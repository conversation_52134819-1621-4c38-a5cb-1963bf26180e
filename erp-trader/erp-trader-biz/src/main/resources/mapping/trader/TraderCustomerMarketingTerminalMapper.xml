<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerMarketingTerminalMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingTerminalEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER_MARKETING_TERMINAL-->
    <id column="TRADER_CUSTOMER_MARKETING_ID" jdbcType="INTEGER" property="traderCustomerMarketingId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="INSTITUTION_TYPE" jdbcType="VARCHAR" property="institutionType" />
    <result column="INSTITUTION_LEVEL" jdbcType="VARCHAR" property="institutionLevel" />
    <result column="INSTITUTION_NATURE" jdbcType="VARCHAR" property="institutionNature" />
    <result column="MANAGEMENT_FORMS" jdbcType="VARCHAR" property="managementForms" />
    <result column="LEGAL_REPRESENTATIVE" jdbcType="VARCHAR" property="legalRepresentative" />
    <result column="BED_NUMBER" jdbcType="VARCHAR" property="bedNumber" />
    <result column="HOSPITAL_DEPARTMENT" jdbcType="VARCHAR" property="hospitalDepartment" />
    <result column="TRADER_CUSTOMER_MARKETING_TYPE" jdbcType="INTEGER" property="traderCustomerMarketingType" />
    <result column="INSTITUTION_TYPE_CHILD" jdbcType="VARCHAR" property="institutionTypeChild" />
    <result column="TERMINAL_IDS" jdbcType="VARCHAR" property="terminalIds" />
    <result column="OTHER_INSTITUTION_TYPE" jdbcType="VARCHAR" property="otherInstitutionType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_MARKETING_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID, INSTITUTION_TYPE, INSTITUTION_LEVEL,
    INSTITUTION_NATURE, MANAGEMENT_FORMS, LEGAL_REPRESENTATIVE, BED_NUMBER, HOSPITAL_DEPARTMENT,
    TRADER_CUSTOMER_MARKETING_TYPE, INSTITUTION_TYPE_CHILD, TERMINAL_IDS, OTHER_INSTITUTION_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_MARKETING_TERMINAL
    where TRADER_CUSTOMER_MARKETING_ID = #{traderCustomerMarketingId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER_MARKETING_TERMINAL
    where TRADER_CUSTOMER_MARKETING_ID = #{traderCustomerMarketingId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_MARKETING_ID" keyProperty="traderCustomerMarketingId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingTerminalEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_MARKETING_TERMINAL (ADD_TIME, MOD_TIME, CREATOR,
    UPDATER, CREATOR_NAME, UPDATER_NAME,
    TRADER_ID, TRADER_CUSTOMER_ID, INSTITUTION_TYPE,
    INSTITUTION_LEVEL, INSTITUTION_NATURE, MANAGEMENT_FORMS,
    LEGAL_REPRESENTATIVE, BED_NUMBER, HOSPITAL_DEPARTMENT,
    TRADER_CUSTOMER_MARKETING_TYPE, INSTITUTION_TYPE_CHILD,
    TERMINAL_IDS, OTHER_INSTITUTION_TYPE)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
    #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
    #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, #{institutionType,jdbcType=VARCHAR},
    #{institutionLevel,jdbcType=VARCHAR}, #{institutionNature,jdbcType=VARCHAR}, #{managementForms,jdbcType=VARCHAR},
    #{legalRepresentative,jdbcType=VARCHAR}, #{bedNumber,jdbcType=VARCHAR}, #{hospitalDepartment,jdbcType=VARCHAR},
    #{traderCustomerMarketingType,jdbcType=INTEGER}, #{institutionTypeChild,jdbcType=VARCHAR},
    #{terminalIds,jdbcType=VARCHAR}, #{otherInstitutionType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_MARKETING_ID" keyProperty="traderCustomerMarketingId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingTerminalEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_MARKETING_TERMINAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="institutionType != null">
        INSTITUTION_TYPE,
      </if>
      <if test="institutionLevel != null">
        INSTITUTION_LEVEL,
      </if>
      <if test="institutionNature != null">
        INSTITUTION_NATURE,
      </if>
      <if test="managementForms != null">
        MANAGEMENT_FORMS,
      </if>
      <if test="legalRepresentative != null">
        LEGAL_REPRESENTATIVE,
      </if>
      <if test="bedNumber != null">
        BED_NUMBER,
      </if>
      <if test="hospitalDepartment != null">
        HOSPITAL_DEPARTMENT,
      </if>
      <if test="traderCustomerMarketingType != null">
        TRADER_CUSTOMER_MARKETING_TYPE,
      </if>
      <if test="institutionTypeChild != null">
        INSTITUTION_TYPE_CHILD,
      </if>
      <if test="terminalIds != null">
        TERMINAL_IDS,
      </if>
      <if test="otherInstitutionType != null">
        OTHER_INSTITUTION_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="institutionType != null">
        #{institutionType,jdbcType=VARCHAR},
      </if>
      <if test="institutionLevel != null">
        #{institutionLevel,jdbcType=VARCHAR},
      </if>
      <if test="institutionNature != null">
        #{institutionNature,jdbcType=VARCHAR},
      </if>
      <if test="managementForms != null">
        #{managementForms,jdbcType=VARCHAR},
      </if>
      <if test="legalRepresentative != null">
        #{legalRepresentative,jdbcType=VARCHAR},
      </if>
      <if test="bedNumber != null">
        #{bedNumber,jdbcType=VARCHAR},
      </if>
      <if test="hospitalDepartment != null">
        #{hospitalDepartment,jdbcType=VARCHAR},
      </if>
      <if test="traderCustomerMarketingType != null">
        #{traderCustomerMarketingType,jdbcType=INTEGER},
      </if>
      <if test="institutionTypeChild != null">
        #{institutionTypeChild,jdbcType=VARCHAR},
      </if>
      <if test="terminalIds != null">
        #{terminalIds,jdbcType=VARCHAR},
      </if>
      <if test="otherInstitutionType != null">
        #{otherInstitutionType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingTerminalEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_MARKETING_TERMINAL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="institutionType != null">
        INSTITUTION_TYPE = #{institutionType,jdbcType=VARCHAR},
      </if>
      <if test="institutionLevel != null">
        INSTITUTION_LEVEL = #{institutionLevel,jdbcType=VARCHAR},
      </if>
      <if test="institutionNature != null">
        INSTITUTION_NATURE = #{institutionNature,jdbcType=VARCHAR},
      </if>
      <if test="managementForms != null">
        MANAGEMENT_FORMS = #{managementForms,jdbcType=VARCHAR},
      </if>
      <if test="legalRepresentative != null">
        LEGAL_REPRESENTATIVE = #{legalRepresentative,jdbcType=VARCHAR},
      </if>
      <if test="bedNumber != null">
        BED_NUMBER = #{bedNumber,jdbcType=VARCHAR},
      </if>
      <if test="hospitalDepartment != null">
        HOSPITAL_DEPARTMENT = #{hospitalDepartment,jdbcType=VARCHAR},
      </if>
      <if test="traderCustomerMarketingType != null">
        TRADER_CUSTOMER_MARKETING_TYPE = #{traderCustomerMarketingType,jdbcType=INTEGER},
      </if>
      <if test="institutionTypeChild != null">
        INSTITUTION_TYPE_CHILD = #{institutionTypeChild,jdbcType=VARCHAR},
      </if>
      <if test="terminalIds != null">
        TERMINAL_IDS = #{terminalIds,jdbcType=VARCHAR},
      </if>
      <if test="otherInstitutionType != null">
        OTHER_INSTITUTION_TYPE = #{otherInstitutionType,jdbcType=VARCHAR},
      </if>
    </set>
    where TRADER_CUSTOMER_MARKETING_ID = #{traderCustomerMarketingId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingTerminalEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_MARKETING_TERMINAL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    TRADER_ID = #{traderId,jdbcType=INTEGER},
    TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
    INSTITUTION_TYPE = #{institutionType,jdbcType=VARCHAR},
    INSTITUTION_LEVEL = #{institutionLevel,jdbcType=VARCHAR},
    INSTITUTION_NATURE = #{institutionNature,jdbcType=VARCHAR},
    MANAGEMENT_FORMS = #{managementForms,jdbcType=VARCHAR},
    LEGAL_REPRESENTATIVE = #{legalRepresentative,jdbcType=VARCHAR},
    BED_NUMBER = #{bedNumber,jdbcType=VARCHAR},
    HOSPITAL_DEPARTMENT = #{hospitalDepartment,jdbcType=VARCHAR},
    TRADER_CUSTOMER_MARKETING_TYPE = #{traderCustomerMarketingType,jdbcType=INTEGER},
    INSTITUTION_TYPE_CHILD = #{institutionTypeChild,jdbcType=VARCHAR},
    TERMINAL_IDS = #{terminalIds,jdbcType=VARCHAR},
    OTHER_INSTITUTION_TYPE = #{otherInstitutionType,jdbcType=VARCHAR}
    where TRADER_CUSTOMER_MARKETING_ID = #{traderCustomerMarketingId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_MARKETING_ID" keyProperty="traderCustomerMarketingId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_MARKETING_TERMINAL
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID,
    INSTITUTION_TYPE, INSTITUTION_LEVEL, INSTITUTION_NATURE, MANAGEMENT_FORMS, LEGAL_REPRESENTATIVE,
    BED_NUMBER, HOSPITAL_DEPARTMENT, TRADER_CUSTOMER_MARKETING_TYPE, INSTITUTION_TYPE_CHILD,
    TERMINAL_IDS, OTHER_INSTITUTION_TYPE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
      #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
      #{item.traderId,jdbcType=INTEGER}, #{item.traderCustomerId,jdbcType=INTEGER}, #{item.institutionType,jdbcType=VARCHAR},
      #{item.institutionLevel,jdbcType=VARCHAR}, #{item.institutionNature,jdbcType=VARCHAR},
      #{item.managementForms,jdbcType=VARCHAR}, #{item.legalRepresentative,jdbcType=VARCHAR},
      #{item.bedNumber,jdbcType=VARCHAR}, #{item.hospitalDepartment,jdbcType=VARCHAR},
      #{item.traderCustomerMarketingType,jdbcType=INTEGER}, #{item.institutionTypeChild,jdbcType=VARCHAR},
      #{item.terminalIds,jdbcType=VARCHAR}, #{item.otherInstitutionType,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <!--auto generated by MybatisCodeHelper on 2023-08-11-->
  <select id="findByTraderCustomerId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_CUSTOMER_MARKETING_TERMINAL
    where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
  </select>
  
  <select id="findByTraderCustomerIdSequence" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_CUSTOMER_MARKETING_TERMINAL
    where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER} 
    order by TRADER_CUSTOMER_MARKETING_TYPE
  </select>

  <!--auto generated by MybatisCodeHelper on 2023-08-14-->
  <delete id="deleteByTraderCustomerId">
    delete from T_TRADER_CUSTOMER_MARKETING_TERMINAL
    where TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER}
  </delete>

<!--auto generated by MybatisCodeHelper on 2023-08-17-->
  <select id="findByTraderIdAndTraderCustomerIdAndTraderCustomerMarketingType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_CUSTOMER_MARKETING_TERMINAL
    where TRADER_ID=#{traderId,jdbcType=INTEGER} and TRADER_CUSTOMER_ID=#{traderCustomerId,jdbcType=INTEGER} and
    TRADER_CUSTOMER_MARKETING_TYPE=#{traderCustomerMarketingType,jdbcType=INTEGER}
  </select>


  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_MARKETING_TERMINAL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderId != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CUSTOMER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderCustomerId != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.traderCustomerId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSTITUTION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.institutionType != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.institutionType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSTITUTION_LEVEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.institutionLevel != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.institutionLevel,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSTITUTION_NATURE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.institutionNature != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.institutionNature,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MANAGEMENT_FORMS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.managementForms != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.managementForms,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="LEGAL_REPRESENTATIVE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.legalRepresentative != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.legalRepresentative,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BED_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bedNumber != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.bedNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="HOSPITAL_DEPARTMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hospitalDepartment != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.hospitalDepartment,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CUSTOMER_MARKETING_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderCustomerMarketingType != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.traderCustomerMarketingType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSTITUTION_TYPE_CHILD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.institutionTypeChild != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.institutionTypeChild,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TERMINAL_IDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.terminalIds != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.terminalIds,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OTHER_INSTITUTION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.otherInstitutionType != null">
            when TRADER_CUSTOMER_MARKETING_ID = #{item.traderCustomerMarketingId,jdbcType=INTEGER} then #{item.otherInstitutionType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where TRADER_CUSTOMER_MARKETING_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.traderCustomerMarketingId,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>