package com.vedeng.infrastructure.kingdee.enums;

import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * 金蝶message code异常枚举
 * <AUTHOR>
 */

public enum KingDeeErrorEnums {

    /**
     * 金蝶异常枚举
     */
    ERROR_ZERO(0,"默认"),
    ERROR_ONE(1,"上下文丢失"),
    ERROR_TWO(2,"没有权限"),
    ERROR_THREE(3,"操作标识为空"),
    ERROR_FOUR(4,"异常"),
    ERROR_FIVE(5,"单据标识为空"),
    ERROR_SIX(6,"数据库操作失败"),
    ERROR_SEVEN(7,"许可错误"),
    ERROR_EIGHT(8,"参数错误"),
    ERROR_NINE(9,"指定字段/值不存在"),
    ERROR_TEN(10,"未找到对应数据"),
    ERROR_ELEVEN(11,"验证失败"),
    ERROR_TWELVE(12,"不可操作"),
    ERROR_THIRTEEN(13,"网控冲突")
    ;

    /**
     * 错误代码
     */
    private final Integer code;

    /**
     * 错误代码信息
     */
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    KingDeeErrorEnums(Integer code,String name){
        this.code = code;
        this.name = name;
    }

    public static String getErrorMsg(Integer code){
        KingDeeErrorEnums kingDeeErrorEnums =  Arrays.stream(KingDeeErrorEnums.values())
                .filter(enums -> enums.getCode().equals(code))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的金蝶业务枚举"));
        return kingDeeErrorEnums.name;
    }
}
