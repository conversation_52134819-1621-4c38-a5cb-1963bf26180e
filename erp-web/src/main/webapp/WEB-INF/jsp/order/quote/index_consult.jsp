<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="报价咨询管理" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/order/quote/index_consult.js?rnd=${resourceVersionKey}'></script>
<div class="main-container">
	<div class="list-pages-search">
		<form method="post" id="search" action="<%=basePath%>order/quote/getQuoteConsultListPage.do?searchFlag=true">
			<ul>
				<li>
					<label class="infor_name">报价单号</label>
					<input type="text" class="input-middle" name="quoteorderNo" id="quoteorderNo" value="${quoteConsult.quoteorderNo}"/>
				</li>
				<li>
					<label class="infor_name">销售人员</label>
					<select class="input-middle" name="saleUserId" id="saleUserId">
						<option value="">全部</option>
						<c:forEach var="list" items="${quoteConsultUserList}" varStatus="status">
							<option value="${list.userId}" <c:if test="${quoteConsult.saleUserId eq list.userId}">selected</c:if>>${list.username}</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">产品归属</label>
					<select class="input-middle" name="goods.goodsUserId" id="goodsUserId">
						<option value="">全部</option>
						<c:forEach var="list" items="${productUserList}" varStatus="status">
							<option value="${list.userId}" <c:if test='${quoteConsult.goods.goodsUserId eq list.userId}'>selected</c:if>>${list.username}</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">产品名称</label>
					<input type="text" class="input-middle" name="goods.goodsName" id="goodsName" value="${quoteConsult.goods.goodsName}"/>
				</li>
				<li>
					<label class="infor_name">品牌</label>
					<input type="text" class="input-middle" name="goods.brandName" id="brandName" value="${quoteConsult.goods.brandName}"/>
				</li>
				<li>
					<label class="infor_name">型号</label>
					<input type="text" class="input-middle" name="goods.model" id="model" value="${quoteConsult.goods.model}"/>
				</li>
				<li>
					<label class="infor_name">订货号</label>
					<input type="text" class="input-middle" name="goods.sku" id="sku" value="${quoteConsult.goods.sku}"/>
				</li>
				<li>
					<label class="infor_name">处理状态</label>
					<select class="input-middle" name="consultStatus" id="consultStatus">
						<option value="">全部</option>
						<option <c:if test="${quoteConsult.consultStatus eq 1}">selected</c:if> value="1">未处理</option>
						<option <c:if test="${quoteConsult.consultStatus eq 2}">selected</c:if> value="2">部分处理</option>
						<option <c:if test="${quoteConsult.consultStatus eq 3}">selected</c:if> value="3">已处理</option>
					</select>
				</li>
				<li>
					<label class="infor_name">报价来源</label>
					<select class="input-middle" name="sourceQuae" id="sourceQuae">
						<option value="">全部</option>

						<option value="" <c:if test="${quoteConsult.sourceQuae ne 'VD'}">selected</c:if>>订单</option>
						<option value="VD" <c:if test="${quoteConsult.sourceQuae eq 'VD'}">selected</c:if>>商机</option>

					</select>
				</li>
				<li>
					<div class="infor_name specialinfor"> 
						<select class="input-smaller" name="type" id="type">
							<option value="1" <c:if test="${quoteConsult.type eq 1}">selected</c:if>>咨询时间</option>
							<option value="2" <c:if test="${quoteConsult.type eq 2}">selected</c:if>>答复时间</option>
						</select>
					</div>
					<input class="Wdate f_left input-smaller mr5" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()" name="searchBeginTime" value="${searchBeginTime}">
					<div class="gang">-</div> 
					<input class="Wdate f_left input-smaller" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()" name="searchEndTime" value="${searchEndTime}">
				</li>
				<li>
					<label class="infor_name">待处理人</label>
					<select class="input-middle" name="consultReplier" id="consultReplier">
						<option value="">全部</option>
						<c:forEach var="list" items="${productUserList}" varStatus="status">
							<option value="${list.userId}" <c:if test='${quoteConsult.consultReplier eq list.userId}'>selected</c:if>>${list.username}</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">跟单状态</label>
					<select class="input-middle" name="trackOrderState">
						<option value="" selected>全部</option>
						<option value="0" <c:if test="${quoteConsult.trackOrderState eq 0}">selected</c:if>>跟单中</option>
						<option value="1" <c:if test="${quoteConsult.trackOrderState eq 1}">selected</c:if>>已成单</option>
						<option value="2" <c:if test="${quoteConsult.trackOrderState eq 2}">selected</c:if>>已失单</option>
					</select>
				</li>
				<li>
					<label class="infor_name">响应预警</label>
					<select class="input-middle" name="purchaserAlarmLevel">
						<option value="" selected>请选择</option>
						<option value="0" <c:if test="${quoteConsult.quoteAlarmMode eq 2 and quoteConsult.purchaserAlarmLevel eq 0}">selected</c:if>>无预警</option>
						<option value="1" <c:if test="${quoteConsult.quoteAlarmMode eq 2 and quoteConsult.purchaserAlarmLevel eq 1}">selected</c:if>>第一级预警</option>
						<option value="2" <c:if test="${quoteConsult.quoteAlarmMode eq 2 and quoteConsult.purchaserAlarmLevel eq 2}">selected</c:if>>第二级预警</option>
						<option value="3" <c:if test="${quoteConsult.quoteAlarmMode eq 2 and quoteConsult.purchaserAlarmLevel eq 3}">selected</c:if>>第三级预警</option>
					</select>
				</li>
			</ul>
			<div class="tcenter">
				<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
				<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
			</div>
		</form>
	</div>
	<div class="normal-list-page">
		<table class="table table-bordered table-striped table-condensed table-centered">
			<thead>
				<tr>
					<th class="wid4">序号</th>
					<th class="wid10">报价单号</th>
					<th class="wid14">报价来源</th>
					<th class="wid12">咨询时间</th>
					<th class="wid8">销售人员</th>
					<th class="wid8">问答次数</th>
					<th class="wid12">回复时间</th>
					<th class="wid12">待处理人</th>
					<th class="wid14">处理状态(报价单)</th>
					<th class="wid20">咨询内容</th>
					<th class="wid14">处理状态(供应链)</th>
					<th class="wid14">跟单状态</th>
					<th class="wid14">响应预警</th>
				</tr>
			</thead>
			<tbody>
				<c:forEach var="list" items="${quoteConsultList}" varStatus="num">
					<tr>
						<td>${num.count}</td>
						<td>
							<a class="addtitle" tabtitle='{"num":"quote_consult${list.quoteorderId}","link":"./order/quote/getQuoteDetail.do?quoteorderId=${list.quoteorderId}&viewType=5","title":"编辑报价"}'>${list.quoteorderNo}</a>
						</td>
						<td>
							<c:choose>
								<c:when test="${fn:substring(list.quoteorderNo,0,2) == 'VD'}">商机</c:when>
								<c:otherwise>订单</c:otherwise>
							</c:choose>
						</td>
						<td><date:date value="${list.sendTime}" /></td>
						<td>${list.saleUserName}</td>
						<td>${list.saleQuizNum} | ${list.purchaseReplyNum}</td>
						<td><date:date value ="${list.replayTime}"/></td>
						<td>${list.consultReplierName}</td>
						<td>
							<c:choose>
								<c:when test="${list.consultStatus eq 1 || list.consultStatus eq 4}"><span style="color:red;">未处理</span></c:when>
								<c:when test="${list.consultStatus eq 2 || list.consultStatus eq 5}"><span style="color:red;">部分处理</span></c:when>
								<c:when test="${list.consultStatus eq 3 || list.consultStatus eq 6}"><span style="color:green;">已处理</span></c:when>
								<c:otherwise>--</c:otherwise>
							</c:choose>
						</td>
						<td>${list.content}</td>
						<td>
							<c:choose>
								<c:when test="${list.consultStatus eq 1}"><span style="color:red;">未处理</span></c:when>
								<c:when test="${list.consultStatus eq 2}"><span style="color:red;">处理中</span></c:when>
								<c:when test="${list.consultStatus eq 3}"><span style="color:green;">已处理</span></c:when>
								<c:otherwise>--</c:otherwise>
							</c:choose>
						</td>
						<td>
							<c:choose>
								<c:when test="${list.trackOrderState eq 0}"><span style="color:red;">跟单中</span></c:when>
								<c:when test="${list.trackOrderState eq 1}"><span style="color:red;">成单</span></c:when>
								<c:when test="${list.trackOrderState eq 2}"><span style="color:green;">失单</span></c:when>
								<c:otherwise>跟单中</c:otherwise>
							</c:choose>
						</td>
						<%--响应预警--%>
						<td>
							<c:choose>
								<c:when test="${list.quoteAlarmMode eq 2 and list.purchaserAlarmLevel eq 1}"><span style="color:red;">一级预警</span></c:when>
								<c:when test="${list.quoteAlarmMode eq 2 and list.purchaserAlarmLevel eq 2}"><span style="color:red;">二级预警</span></c:when>
								<c:when test="${list.quoteAlarmMode eq 2 and list.purchaserAlarmLevel eq 3}"><span style="color:green;">三级预警</span></c:when>
								<c:otherwise>--</c:otherwise>
							</c:choose>
						</td>
					</tr>
				</c:forEach>
				<c:if test="${empty quoteConsultList}">
					<!-- 查询无结果弹出 -->
					<tr>
						<td colspan='15'>查询无结果！请尝试使用其它搜索条件。</td>
					</tr>
				</c:if>
			</tbody>
		</table>
	</div>
	<tags:page page="${page}" />
</div>
<%@ include file="../../common/footer.jsp"%>
