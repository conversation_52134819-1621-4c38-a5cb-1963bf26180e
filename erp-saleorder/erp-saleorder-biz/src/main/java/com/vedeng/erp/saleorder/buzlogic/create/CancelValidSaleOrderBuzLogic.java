package com.vedeng.erp.saleorder.buzlogic.create;

import com.vedeng.common.constant.ErpConst;
 import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Service
public class CancelValidSaleOrderBuzLogic {
    Logger logger= LoggerFactory.getLogger(CancelValidSaleOrderBuzLogic.class);

    private List<String> sequence = Arrays.asList("cancelValidSaleOrder","cancelOrderAutoValid");

    public List<String> getSequence() {
        return sequence;
    }

    public void setSequence(List<String> sequence) {
        this.sequence = sequence;
    }

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;
    @Resource
    private SaleorderSyncService saleorderSyncService;
    @Resource
    private SaleorderMapper saleorderMapper;

    public ResultInfo cancelValidSaleOrder(Saleorder saleorder){
        try {
            baseSaleOrderService.cancelValidOfSaleOrder(saleorder);
            //撤销生效，同步BD订单状态信息
            saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId(),
                    PCOrderStatusEnum.INIT, SaleorderSyncEnum.CACEL_VALID);
            return new ResultInfo(0,"撤销订单成功");
        }catch (Exception exception){
            logger.error("撤销生效订单失败，订单id{},错误信息{},",saleorder.getSaleorderId(), exception.getMessage());
            return new ResultInfo(0,"撤销订单失败");
        }
    }

    public ResultInfo cancelOrderAutoValid(Saleorder saleorder){
        if(ErpConst.ONE.equals(saleorder.getOrderType()) && ErpConst.ONE.equals(saleorder.getAutoAudit())){
            logger.info("线上订单{},撤销生效，取消自动审核",saleorder.getSaleorderNo());
            //BOF自动审核订单---撤销生效后取消自动审核
            Saleorder uptSaleorder = new Saleorder();
            uptSaleorder.setSaleorderId(saleorder.getSaleorderId());
            uptSaleorder.setAutoAudit(ErpConst.ZERO);
            saleorderMapper.updateByPrimaryKeySelective(uptSaleorder);
        }
        return new ResultInfo(0,"操作成功");
    }

    public ResultInfo run(Saleorder saleorder){
        ResultInfo result = new ResultInfo(0,"操作成功");
        for (String methodName : getSequence()){
            switch (methodName) {
                case "cancelValidSaleOrder":
                    if(ErpConst.ZERO.equals(result.getCode())){
                        result = this.cancelValidSaleOrder(saleorder);
                        logger.info("取消生效销售单{},返回信息{},", saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                case "cancelOrderAutoValid":
                    this.cancelOrderAutoValid(saleorder);
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
