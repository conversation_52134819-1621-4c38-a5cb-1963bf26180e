<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.SaleorderAdditionalClauseMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.domain.SaleorderAdditionalClause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="ADDITIONAL_CLAUSE_NOS" jdbcType="VARCHAR" property="additionalClauseNos" />
    <result column="INSTALLATION_ADDRESS" jdbcType="VARCHAR" property="installationAddress" />
    <result column="PROD_NAME_103" jdbcType="VARCHAR" property="prodName103" />
    <result column="PROD_NAME_105" jdbcType="VARCHAR" property="prodName105" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="SALE_CITY_ID" jdbcType="INTEGER" property="saleCityId" />
    <result column="SALE_PROVINCE_ID" jdbcType="INTEGER" property="saleProvinceId" />
    <result column="SALE_PROVINCE_NAME" jdbcType="VARCHAR" property="saleProvinceName" />
    <result column="SALE_CITY_NAME" jdbcType="VARCHAR" property="saleCityName" />
    <result column="TERMINAL_TRADER_NAME" jdbcType="VARCHAR" property="terminalTraderName" />
    <result column="SN_CODE" jdbcType="VARCHAR" property="snCode" />
    <result column="PREPAID_REAGENT_AMOUNT" jdbcType="DECIMAL" property="prepaidReagentAmount" />
    <result column="ADDITIONAL_CLAUSE" jdbcType="VARCHAR" property="additionalClause" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    ID, SALEORDER_ID, SALEORDER_NO, ADDITIONAL_CLAUSE_NOS, INSTALLATION_ADDRESS, PROD_NAME_103, 
    PROD_NAME_105, TRADER_NAME, SALE_CITY_ID, SALE_PROVINCE_ID, SALE_PROVINCE_NAME, SALE_CITY_NAME, 
    TERMINAL_TRADER_NAME, SN_CODE, PREPAID_REAGENT_AMOUNT, ADDITIONAL_CLAUSE, CREATOR, 
    UPDATER, ADD_TIME, MODE_TIME, IS_DELETE
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.erp.saleorder.domain.SaleorderAdditionalClauseExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_SALEORDER_ADDITIONAL_CLAUSE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_SALEORDER_ADDITIONAL_CLAUSE
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    delete from T_SALEORDER_ADDITIONAL_CLAUSE
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.erp.saleorder.domain.SaleorderAdditionalClauseExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    delete from T_SALEORDER_ADDITIONAL_CLAUSE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.saleorder.domain.SaleorderAdditionalClause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SALEORDER_ADDITIONAL_CLAUSE (SALEORDER_ID, SALEORDER_NO, ADDITIONAL_CLAUSE_NOS, 
      INSTALLATION_ADDRESS, PROD_NAME_103, PROD_NAME_105, 
      TRADER_NAME, SALE_CITY_ID, SALE_PROVINCE_ID, 
      SALE_PROVINCE_NAME, SALE_CITY_NAME, TERMINAL_TRADER_NAME, 
      SN_CODE, PREPAID_REAGENT_AMOUNT, ADDITIONAL_CLAUSE, 
      CREATOR, UPDATER, ADD_TIME, 
      MODE_TIME, IS_DELETE)
    values (#{saleorderId,jdbcType=INTEGER}, #{saleorderNo,jdbcType=VARCHAR}, #{additionalClauseNos,jdbcType=VARCHAR}, 
      #{installationAddress,jdbcType=VARCHAR}, #{prodName103,jdbcType=VARCHAR}, #{prodName105,jdbcType=VARCHAR}, 
      #{traderName,jdbcType=VARCHAR}, #{saleCityId,jdbcType=INTEGER}, #{saleProvinceId,jdbcType=INTEGER}, 
      #{saleProvinceName,jdbcType=VARCHAR}, #{saleCityName,jdbcType=VARCHAR}, #{terminalTraderName,jdbcType=VARCHAR}, 
      #{snCode,jdbcType=VARCHAR}, #{prepaidReagentAmount,jdbcType=DECIMAL}, #{additionalClause,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.saleorder.domain.SaleorderAdditionalClause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SALEORDER_ADDITIONAL_CLAUSE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO,
      </if>
      <if test="additionalClauseNos != null">
        ADDITIONAL_CLAUSE_NOS,
      </if>
      <if test="installationAddress != null">
        INSTALLATION_ADDRESS,
      </if>
      <if test="prodName103 != null">
        PROD_NAME_103,
      </if>
      <if test="prodName105 != null">
        PROD_NAME_105,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="saleCityId != null">
        SALE_CITY_ID,
      </if>
      <if test="saleProvinceId != null">
        SALE_PROVINCE_ID,
      </if>
      <if test="saleProvinceName != null">
        SALE_PROVINCE_NAME,
      </if>
      <if test="saleCityName != null">
        SALE_CITY_NAME,
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME,
      </if>
      <if test="snCode != null">
        SN_CODE,
      </if>
      <if test="prepaidReagentAmount != null">
        PREPAID_REAGENT_AMOUNT,
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="additionalClauseNos != null">
        #{additionalClauseNos,jdbcType=VARCHAR},
      </if>
      <if test="installationAddress != null">
        #{installationAddress,jdbcType=VARCHAR},
      </if>
      <if test="prodName103 != null">
        #{prodName103,jdbcType=VARCHAR},
      </if>
      <if test="prodName105 != null">
        #{prodName105,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="saleCityId != null">
        #{saleCityId,jdbcType=INTEGER},
      </if>
      <if test="saleProvinceId != null">
        #{saleProvinceId,jdbcType=INTEGER},
      </if>
      <if test="saleProvinceName != null">
        #{saleProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="saleCityName != null">
        #{saleCityName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderName != null">
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="snCode != null">
        #{snCode,jdbcType=VARCHAR},
      </if>
      <if test="prepaidReagentAmount != null">
        #{prepaidReagentAmount,jdbcType=DECIMAL},
      </if>
      <if test="additionalClause != null">
        #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.erp.saleorder.domain.SaleorderAdditionalClauseExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    select count(*) from T_SALEORDER_ADDITIONAL_CLAUSE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    update T_SALEORDER_ADDITIONAL_CLAUSE
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.saleorderId != null">
        SALEORDER_ID = #{record.saleorderId,jdbcType=INTEGER},
      </if>
      <if test="record.saleorderNo != null">
        SALEORDER_NO = #{record.saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.additionalClauseNos != null">
        ADDITIONAL_CLAUSE_NOS = #{record.additionalClauseNos,jdbcType=VARCHAR},
      </if>
      <if test="record.installationAddress != null">
        INSTALLATION_ADDRESS = #{record.installationAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.prodName103 != null">
        PROD_NAME_103 = #{record.prodName103,jdbcType=VARCHAR},
      </if>
      <if test="record.prodName105 != null">
        PROD_NAME_105 = #{record.prodName105,jdbcType=VARCHAR},
      </if>
      <if test="record.traderName != null">
        TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      </if>
      <if test="record.saleCityId != null">
        SALE_CITY_ID = #{record.saleCityId,jdbcType=INTEGER},
      </if>
      <if test="record.saleProvinceId != null">
        SALE_PROVINCE_ID = #{record.saleProvinceId,jdbcType=INTEGER},
      </if>
      <if test="record.saleProvinceName != null">
        SALE_PROVINCE_NAME = #{record.saleProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.saleCityName != null">
        SALE_CITY_NAME = #{record.saleCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{record.terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="record.snCode != null">
        SN_CODE = #{record.snCode,jdbcType=VARCHAR},
      </if>
      <if test="record.prepaidReagentAmount != null">
        PREPAID_REAGENT_AMOUNT = #{record.prepaidReagentAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.additionalClause != null">
        ADDITIONAL_CLAUSE = #{record.additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modeTime != null">
        MODE_TIME = #{record.modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    update T_SALEORDER_ADDITIONAL_CLAUSE
    set ID = #{record.id,jdbcType=BIGINT},
      SALEORDER_ID = #{record.saleorderId,jdbcType=INTEGER},
      SALEORDER_NO = #{record.saleorderNo,jdbcType=VARCHAR},
      ADDITIONAL_CLAUSE_NOS = #{record.additionalClauseNos,jdbcType=VARCHAR},
      INSTALLATION_ADDRESS = #{record.installationAddress,jdbcType=VARCHAR},
      PROD_NAME_103 = #{record.prodName103,jdbcType=VARCHAR},
      PROD_NAME_105 = #{record.prodName105,jdbcType=VARCHAR},
      TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      SALE_CITY_ID = #{record.saleCityId,jdbcType=INTEGER},
      SALE_PROVINCE_ID = #{record.saleProvinceId,jdbcType=INTEGER},
      SALE_PROVINCE_NAME = #{record.saleProvinceName,jdbcType=VARCHAR},
      SALE_CITY_NAME = #{record.saleCityName,jdbcType=VARCHAR},
      TERMINAL_TRADER_NAME = #{record.terminalTraderName,jdbcType=VARCHAR},
      SN_CODE = #{record.snCode,jdbcType=VARCHAR},
      PREPAID_REAGENT_AMOUNT = #{record.prepaidReagentAmount,jdbcType=DECIMAL},
      ADDITIONAL_CLAUSE = #{record.additionalClause,jdbcType=VARCHAR},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{record.modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.domain.SaleorderAdditionalClause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    update T_SALEORDER_ADDITIONAL_CLAUSE
    <set>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="additionalClauseNos != null">
        ADDITIONAL_CLAUSE_NOS = #{additionalClauseNos,jdbcType=VARCHAR},
      </if>
      <if test="installationAddress != null">
        INSTALLATION_ADDRESS = #{installationAddress,jdbcType=VARCHAR},
      </if>
      <if test="prodName103 != null">
        PROD_NAME_103 = #{prodName103,jdbcType=VARCHAR},
      </if>
      <if test="prodName105 != null">
        PROD_NAME_105 = #{prodName105,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="saleCityId != null">
        SALE_CITY_ID = #{saleCityId,jdbcType=INTEGER},
      </if>
      <if test="saleProvinceId != null">
        SALE_PROVINCE_ID = #{saleProvinceId,jdbcType=INTEGER},
      </if>
      <if test="saleProvinceName != null">
        SALE_PROVINCE_NAME = #{saleProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="saleCityName != null">
        SALE_CITY_NAME = #{saleCityName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="snCode != null">
        SN_CODE = #{snCode,jdbcType=VARCHAR},
      </if>
      <if test="prepaidReagentAmount != null">
        PREPAID_REAGENT_AMOUNT = #{prepaidReagentAmount,jdbcType=DECIMAL},
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.domain.SaleorderAdditionalClause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 07 16:24:30 CST 2025.
    -->
    update T_SALEORDER_ADDITIONAL_CLAUSE
    set SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      ADDITIONAL_CLAUSE_NOS = #{additionalClauseNos,jdbcType=VARCHAR},
      INSTALLATION_ADDRESS = #{installationAddress,jdbcType=VARCHAR},
      PROD_NAME_103 = #{prodName103,jdbcType=VARCHAR},
      PROD_NAME_105 = #{prodName105,jdbcType=VARCHAR},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      SALE_CITY_ID = #{saleCityId,jdbcType=INTEGER},
      SALE_PROVINCE_ID = #{saleProvinceId,jdbcType=INTEGER},
      SALE_PROVINCE_NAME = #{saleProvinceName,jdbcType=VARCHAR},
      SALE_CITY_NAME = #{saleCityName,jdbcType=VARCHAR},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      SN_CODE = #{snCode,jdbcType=VARCHAR},
      PREPAID_REAGENT_AMOUNT = #{prepaidReagentAmount,jdbcType=DECIMAL},
      ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=TINYINT}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>