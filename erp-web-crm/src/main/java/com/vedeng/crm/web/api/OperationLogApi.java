package com.vedeng.crm.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.service.OperationLogApiService;
import com.vedeng.erp.system.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 操作记录
 *
 * <AUTHOR>
 * @version 1.0
 * @menu 操作记录
 * @date 2024/7/22 11:33
 */
@ExceptionController
@RestController
@RequestMapping("/crm/operationLog/public")
@Slf4j
public class OperationLogApi {

    @Autowired
    private OperationLogApiService operationLogApiService;


    /**
     * 操作记录分页接口
     *
     * @param operationLogDto operationLogDto
     * @return
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public R<PageInfo<OperationLogDto>> page(@RequestBody PageParam<OperationLogDto> operationLogDto) {
        return R.success(operationLogApiService.page(operationLogDto));
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/pageForModifyPrice", method = RequestMethod.POST)
    public R<PageInfo<OperationLogDto>> pageForModifyPrice(@RequestBody PageParam<OperationLogDto> operationLogDto) {
        operationLogDto.getParam().setActionTypeEnum("价格修改");
        PageInfo<OperationLogDto> pageInfo = operationLogApiService.page(operationLogDto);
        dealAddTime(pageInfo);
        return R.success(pageInfo);
    }

    private void dealAddTime(PageInfo<OperationLogDto> pageInfo) {

        List<OperationLogDto> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("H:mm");
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("M/d");
            DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("yyyy/M/d");

            for (OperationLogDto operationLogDto : list) {
                LocalDateTime addTime = operationLogDto.getAddTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                Duration duration = Duration.between(addTime, now);
                long minutes = duration.toMinutes();
                long hours = duration.toHours();

                if (addTime.toLocalDate().equals(now.toLocalDate())) {
                    if (minutes < 1) {
                        operationLogDto.setFormatAddTime("刚刚");
                    } else if (minutes < 60) {
                        operationLogDto.setFormatAddTime(minutes + "分钟前");
                    } else {
                        operationLogDto.setFormatAddTime(addTime.format(timeFormatter));
                    }
                } else if (addTime.toLocalDate().equals(now.toLocalDate().minusDays(1))) {
                    operationLogDto.setFormatAddTime("昨天");
                } else if (addTime.getYear() == now.getYear()) {
                    operationLogDto.setFormatAddTime(addTime.format(dateFormatter));
                } else {
                    operationLogDto.setFormatAddTime(addTime.format(yearFormatter));
                }
            }
        }


    }




}
