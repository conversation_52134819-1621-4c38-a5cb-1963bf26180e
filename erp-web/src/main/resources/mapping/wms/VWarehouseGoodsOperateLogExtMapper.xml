<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.dao.VWarehouseGoodsOperateLogExtMapper">
    <resultMap id="BaseResultMap" type="com.wms.model.ddi.VWarehouseGoodsOperateLogExtDto">
        <id column="WAREHOUSE_GOODS_OPERATE_LOG_ID" jdbcType="INTEGER" property="warehouseGoodsOperateLogId"/>
        <result column="BARCODE_ID" jdbcType="INTEGER" property="barcodeId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="WAREHOUSE_PICKING_DETAIL_ID" jdbcType="INTEGER" property="warehousePickingDetailId"/>
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
        <result column="BARCODE_FACTORY" jdbcType="VARCHAR" property="barcodeFactory"/>
        <result column="NUM" jdbcType="INTEGER" property="num"/>
        <result column="WAREHOUSE_ID" jdbcType="INTEGER" property="warehouseId"/>
        <result column="STORAGE_ROOM_ID" jdbcType="INTEGER" property="storageRoomId"/>
        <result column="STORAGE_AREA_ID" jdbcType="INTEGER" property="storageAreaId"/>
        <result column="STORAGE_LOCATION_ID" jdbcType="INTEGER" property="storageLocationId"/>
        <result column="STORAGE_RACK_ID" jdbcType="INTEGER" property="storageRackId"/>
        <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber"/>
        <result column="EXPIRATION_DATE" jdbcType="BIGINT" property="expirationDate"/>
        <result column="CHECK_STATUS" jdbcType="BOOLEAN" property="checkStatus"/>
        <result column="CHECK_STATUS_USER" jdbcType="INTEGER" property="checkStatusUser"/>
        <result column="CHECK_STATUS_TIME" jdbcType="BIGINT" property="checkStatusTime"/>
        <result column="RECHECK_STATUS" jdbcType="BOOLEAN" property="recheckStatus"/>
        <result column="RECHECK_STATUS_USER" jdbcType="INTEGER" property="recheckStatusUser"/>
        <result column="RECHECK_STATUS_TIME" jdbcType="BIGINT" property="recheckStatusTime"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="IS_ENABLE" jdbcType="BOOLEAN" property="isEnable"/>
        <result column="IS_EXPRESS" jdbcType="BOOLEAN" property="isExpress"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="IS_PROBLEM" jdbcType="BOOLEAN" property="isProblem"/>
        <result column="PROBLEM_REMARK" jdbcType="VARCHAR" property="problemRemark"/>
        <result column="PRODUCT_DATE" jdbcType="BIGINT" property="productDate"/>
        <result column="COST_PRICE" jdbcType="DECIMAL" property="costPrice"/>
        <result column="IS_USE" jdbcType="BOOLEAN" property="isUse"/>
        <result column="LOGICAL_WAREHOUSE_ID" jdbcType="INTEGER" property="logicalWarehouseId"/>
        <result column="VEDENG_BATCH_NUMER" jdbcType="VARCHAR" property="vedengBatchNumer"/>
        <result column="LAST_STOCK_NUM" jdbcType="INTEGER" property="lastStockNum"/>
        <result column="STERILZATION_BATCH_NUMBER" jdbcType="VARCHAR" property="sterilzationBatchNumber"/>
        <result column="LOG_TYPE" jdbcType="BOOLEAN" property="logType"/>
    </resultMap>
    <sql id="Base_Column_List">
    WAREHOUSE_GOODS_OPERATE_LOG_ID, BARCODE_ID, COMPANY_ID, OPERATE_TYPE, RELATED_ID, 
    WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, BARCODE_FACTORY, NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, 
    STORAGE_AREA_ID, STORAGE_LOCATION_ID, STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, 
    CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, RECHECK_STATUS, RECHECK_STATUS_USER, 
    RECHECK_STATUS_TIME, COMMENTS, IS_ENABLE, IS_EXPRESS, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, IS_PROBLEM, PROBLEM_REMARK, PRODUCT_DATE, COST_PRICE, IS_USE, LOGICAL_WAREHOUSE_ID, 
    VEDENG_BATCH_NUMER, LAST_STOCK_NUM, STERILZATION_BATCH_NUMBER, LOG_TYPE
  </sql>
    <select id="getAllTodayOrder" resultMap="BaseResultMap">
        SELECT *
        FROM T_WAREHOUSE_GOODS_OPERATE_LOG
        WHERE
        CHECK_STATUS_TIME <![CDATA[>=]]> #{startTime,jdbcType=BIGINT}
        AND
        CHECK_STATUS_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        AND GOODS_ID IN
        <foreach collection="skuIdList" item="sku" index="index" separator="," open="(" close=")">
            #{sku,jdbcType = INTEGER}
        </foreach>
        AND LOG_TYPE = 0
        AND VEDENG_BATCH_NUMER != ''
        AND IS_ENABLE = 1
        AND IS_USE = 0
        AND OPERATE_TYPE IN (1,3,5,8,9,11,12)
    </select>
    <select id="getSingleOrder" resultType="com.wms.model.dto.GoodsStockDto">
        SELECT
        "贝登" AS distributor,
        <if test="operateType == 1">
            bg.GE_CONTRACT_NO AS quoteId,
            bg.GE_SALE_CONTRACT_NO AS saleorderNo,

            bg.UNIT_NAME AS unit,
            b.TRADER_NAME AS supplierName,
            b.TRADER_ID AS supplierId,
        </if>
        <if test="operateType ==3 || operateType ==5">
            coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
            coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
            coalesce(bg.UNIT_NAME,bg2.UNIT_NAME) AS unit,
            coalesce(b.TRADER_NAME,b2.TRADER_NAME) AS supplierName,
            coalesce(b.TRADER_ID,b2.TRADER_ID) AS supplierId,
        </if>
        <if test="operateType == 8">
            coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
            coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
            coalesce(bg.UNIT_NAME,bg2.UNIT_NAME) AS unit,
            coalesce(b.TRADER_NAME,b2.TRADER_NAME) AS supplierName,
            coalesce(b.TRADER_ID,b2.TRADER_ID) AS supplierId,
        </if>
        <if test="operateType == 9">
            coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
            coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
            coalesce(bg.UNIT_NAME,bg2.UNIT_NAME) AS unit,
            coalesce(b.TRADER_NAME,b2.TRADER_NAME) AS supplierName,
            coalesce(b.TRADER_ID,b2.TRADER_ID) AS supplierId,
        </if>
        <if test="operateType == 11 || operateType == 12 ">
            coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
            coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
            coalesce(bg.UNIT_NAME,bg2.UNIT_NAME) AS unit,
            coalesce(b.TRADER_NAME,b2.TRADER_NAME) AS supplierName,
            coalesce(b.TRADER_ID,b2.TRADER_ID) AS supplierId,
        </if>
        a.CHECK_STATUS_TIME AS storageTimeLong,
        sodg.title AS goodsType,
        g.GOODS_NAME AS goodsName,
        g.MODEL AS goodsModel,
        g.REGISTRATION_NUMBER AS registrationNumber,
        wiso.SN_CODE AS serialNumber,
        a.EXPIRATION_DATE AS effectiveDaysLong,
        a.NUM AS num,
        pc.PRODUCT_COMPANY_CHINESE_NAME AS manufacturer,
        sodl.title AS stockStatus,
        coalesce(a.MOD_TIME,a.ADD_TIME) AS stockTimeLong
        FROM
        T_WAREHOUSE_GOODS_OPERATE_LOG a
        LEFT JOIN V_CORE_SKU sku ON a.GOODS_ID = sku.SKU_ID
        LEFT JOIN T_GOODS g ON a.GOODS_ID = g.GOODS_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION sodg ON g.GOODS_TYPE=sodg.SYS_OPTION_DEFINITION_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION sodl ON a.LOGICAL_WAREHOUSE_ID=sodl.SYS_OPTION_DEFINITION_ID

        <if test="operateType == 1">
            LEFT JOIN T_BUYORDER_GOODS bg ON a.RELATED_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON a.RELATED_ID = wiso.ORDER_GOODS_ID AND wiso.OPERATE_TYPE = 0
                      AND a.VEDENG_BATCH_NUMER = wiso.VEDENG_BATCH_NUMER
            AND wiso.SN_CODE != ''
        </if>
        <if test="operateType == 3 || operateType ==5">
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON a.RELATED_ID = wiso.ORDER_GOODS_ID
            AND wiso.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 1
            AND a.VEDENG_BATCH_NUMER = wiso.VEDENG_BATCH_NUMER
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso1 ON wiso.SN_CODE = wiso1.SN_CODE
            AND wiso1.SN_CODE != ''
            AND wiso1.OPERATE_TYPE = 0
            LEFT JOIN T_BUYORDER_GOODS bg ON wiso1.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = wiso.SN_CODE
            AND wiso1.OPERATE_TYPE = 2 AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
            AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
            AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
            AND wiso1.ORDER_GOODS_ID is null
        </if>
        <if test="operateType == 8">
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON a.RELATED_ID = wiso.ORDER_GOODS_ID
            AND wiso.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 2
            AND a.VEDENG_BATCH_NUMER = wiso.VEDENG_BATCH_NUMER
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso1 ON wiso.SN_CODE = wiso1.SN_CODE
            AND wiso1.SN_CODE != ''
            AND wiso1.OPERATE_TYPE = 0
            LEFT JOIN T_BUYORDER_GOODS bg ON wiso1.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = wiso.SN_CODE
            AND wiso2.SN_CODE != ''
            AND wiso1.OPERATE_TYPE = 2 AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
            AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
            AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
            AND wiso1.ORDER_GOODS_ID is null
        </if>
        <if test="operateType == 9">
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON a.RELATED_ID = wiso.ORDER_GOODS_ID
            AND wiso.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 4
            AND a.VEDENG_BATCH_NUMER = wiso.VEDENG_BATCH_NUMER
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso1 ON wiso.SN_CODE = wiso1.SN_CODE
            AND wiso1.SN_CODE != ''
            AND wiso1.OPERATE_TYPE = 0
            LEFT JOIN T_BUYORDER_GOODS bg ON wiso1.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = wiso.SN_CODE
            AND wiso2.SN_CODE != ''
            AND wiso1.OPERATE_TYPE = 2 AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
            AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
            AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
            AND wiso1.ORDER_GOODS_ID is null
        </if>
        <if test="operateType == 11 || operateType == 12">
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON a.RELATED_ID = wiso.ORDER_GOODS_ID
            AND wiso.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 3
            AND a.VEDENG_BATCH_NUMER = wiso.VEDENG_BATCH_NUMER
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso1 ON wiso.SN_CODE = wiso1.SN_CODE
            AND wiso1.SN_CODE != ''
            AND wiso1.OPERATE_TYPE = 0
            LEFT JOIN T_BUYORDER_GOODS bg ON wiso1.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = wiso.SN_CODE
            AND wiso2.SN_CODE != ''
            AND wiso1.OPERATE_TYPE = 2 AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
            AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
            AND wiso1.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
            AND wiso1.ORDER_GOODS_ID is null
        </if>
        LEFT JOIN V_CORE_SPU spu ON sku.SPU_ID = spu.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE fe ON spu.FIRST_ENGAGE_ID = fe.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER rn ON fe.REGISTRATION_NUMBER_ID = rn.REGISTRATION_NUMBER_ID
        LEFT JOIN T_PRODUCT_COMPANY pc ON rn.PRODUCT_COMPANY_ID = pc.PRODUCT_COMPANY_ID
        WHERE
        a.LOG_TYPE = 0
        AND a.VEDENG_BATCH_NUMER != ''
        AND a.IS_ENABLE = 1
        AND a.IS_USE = 0
        AND a.WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
    </select>

</mapper>