<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.kpi.replica.dao.AfterSalesReplicaMapper" >

    <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSales" >
        <id column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
        <result column="AFTER_SALES_NO" property="afterSalesNo" jdbcType="VARCHAR" />
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
        <result column="SUBJECT_TYPE" property="subjectType" jdbcType="INTEGER" />
        <result column="TYPE" property="type" jdbcType="INTEGER" />
        <result column="ORDER_ID" property="orderId" jdbcType="INTEGER" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="SERVICE_USER_ID" property="serviceUserId" jdbcType="INTEGER" />
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
        <result column="STATUS" property="status" jdbcType="BIT" />
        <result column="ATFER_SALES_STATUS" property="atferSalesStatus" jdbcType="BIT" />

        <result column="FIRST_VALID_STATUS" property="firstValidStatus" jdbcType="BIT" />
        <result column="FIRST_VALID_TIME" property="firstValidTime" jdbcType="BIGINT" />
        <result column="FIRST_VALID_USER" property="firstValidUser" jdbcType="INTEGER" />
        <result column="FIRST_VALID_COMMENTS" property="firstValidComments" jdbcType="VARCHAR" />

        <result column="SOURCE" property="source" jdbcType="BIT" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
        <result column="IS_OUT_AFTER" property="isOutAfter" jdbcType="BIT" />

        <association property="afterSalesDetail" javaType="com.vedeng.aftersales.model.AfterSalesDetail">
            <id column="AFTER_SALES_DETAIL_ID" property="afterSalesDetailId" jdbcType="INTEGER" />
            <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
            <result column="REASON" property="reason" jdbcType="INTEGER" />
            <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
            <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
            <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
            <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR" />
            <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR" />
            <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR" />
            <result column="REFUND" property="refund" jdbcType="BIT" />
            <result column="AREA_ID" property="areaId" jdbcType="INTEGER" />
            <result column="ADDRESS_ID" property="addressId" jdbcType="INTEGER" />
            <result column="AREA" property="area" jdbcType="VARCHAR" />
            <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
            <result column="REFUND_AMOUNT" property="refundAmount" jdbcType="DECIMAL" />
            <result column="REFUND_FEE" property="refundFee" jdbcType="DECIMAL" />
            <result column="REAL_REFUND_AMOUNT" property="realRefundAmount" jdbcType="DECIMAL" />
            <result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="DECIMAL" />
            <result column="REFUND_AMOUNT_STATUS" property="refundAmountStatus" jdbcType="BIT" />
            <result column="TRADER_SUBJECT" property="traderSubject" jdbcType="BIT" />
            <result column="PAYEE" property="payee" jdbcType="VARCHAR" />
            <result column="BANK" property="bank" jdbcType="VARCHAR" />
            <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />
            <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR" />
            <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL" />
            <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
            <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT" />
            <result column="TRADER_MODE" property="traderMode" jdbcType="BIT" />
            <result column="PERIOD_AMOUNT" property="payPeriodAmount" jdbcType="DECIMAL" />
            <result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER" />
            <result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR" />
            <result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER" />
            <result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR" />
            <result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR" />
            <result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR" />
            <result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER" />
            <result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR" />
            <result column="INVOICE_TRADER_AREA" property="invoiceTraderArea" jdbcType="VARCHAR" />

            <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />

            <result column="RECEIVE_PAYMENT_STATUS" property="receivePaymentStatus" jdbcType="BIT" />
            <result column="RECEIVE_PAYMENT_TIME" property="receivePaymentTime" jdbcType="BIGINT" />

            <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT" />
            <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT" />
        </association>
    </resultMap>

    <resultMap id="BaseAfterSalesGoods" type="com.vedeng.aftersales.model.AfterSalesGoods">
        <id column="AFTER_SALES_GOODS_ID" property="afterSalesGoodsId" jdbcType="INTEGER" />
        <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
        <result column="ORDER_DETAIL_ID" property="orderDetailId" jdbcType="INTEGER" />
        <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
        <result column="NUM" property="num" jdbcType="INTEGER" />
        <result column="PRICE" property="price" jdbcType="DECIMAL" />
        <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT" />
        <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER" />
        <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
        <result column="ARRIVAL_USER_ID" property="arrivalUserId" jdbcType="INTEGER" />
        <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT" />
        <result column="GOODS_TYPE" property="goodsType" jdbcType="BIT" />
        <result column="SKU_REFUND_AMOUNT" property="skuRefundAmount" jdbcType="DECIMAL" />
        <result column="IS_ACTION_GOODS" property="isActionGoods" jdbcType="INTEGER" />
        <result column="SKU_OLD_REFUND_AMOUNT" property="skuOldRefundAmount" jdbcType="DECIMAL"/>
    </resultMap>

    <select id="getAfterSalesGoodIdBySaleorderBeforeExecuteTime" resultMap="BaseAfterSalesGoods">
        SELECT
            TASG.GOODS_ID, IFNULL(SUM(TASG.NUM),0) AS NUM
        FROM T_AFTER_SALES A
                 LEFT JOIN T_AFTER_SALES_DETAIL TASD on A.AFTER_SALES_ID = TASD.AFTER_SALES_ID
                 LEFT JOIN T_AFTER_SALES_GOODS TASG on A.AFTER_SALES_ID = TASG.AFTER_SALES_ID
        WHERE A.ORDER_NO = #{saleorderNo} AND A.ADD_TIME &lt;= #{executeTime} AND (TASD.REFUND_AMOUNT_STATUS = 3 || (A.ATFER_SALES_STATUS = 2 AND A.TYPE = 539)) AND TASG.GOODS_ID IS NOT NULL GROUP BY TASG.GOODS_ID
    </select>
    <select id="getAfterSalesByAfterSalesDetailId" resultMap="BaseResultMap">
        SELECT * FROM T_AFTER_SALES A LEFT JOIN T_AFTER_SALES_DETAIL AD ON A.AFTER_SALES_ID = AD.AFTER_SALES_ID WHERE AD.AFTER_SALES_DETAIL_ID = #{afterSalesDetailId}
    </select>
    <select id="getAfterSalesByAfterSalesId" resultType="com.vedeng.aftersales.model.AfterSales">
        SELECT * FROM T_AFTER_SALES WHERE AFTER_SALES_ID = #{afterSalesId}
    </select>
    <select id="getRefundStatusOfAfterSales" resultType="java.lang.Integer">
        SELECT REFUND_AMOUNT_STATUS FROM T_AFTER_SALES_DETAIL WHERE AFTER_SALES_ID = #{afterSalesId}
    </select>
    <select id="getAfterSalesBySaleorderNo" resultMap="BaseResultMap">
        SELECT * FROM T_AFTER_SALES A LEFT JOIN T_AFTER_SALES_DETAIL AD ON A.AFTER_SALES_ID = AD.AFTER_SALES_ID WHERE A.ORDER_NO = #{saleorderNo}
        <if test="executeTime != null">
            AND A.ADD_TIME &gt; #{executeTime}
        </if>
    </select>
    <select id="getAfterSalesByStartTime" resultMap="BaseResultMap">
        SELECT * FROM T_AFTER_SALES A LEFT JOIN T_AFTER_SALES_DETAIL AD ON A.AFTER_SALES_ID = AD.AFTER_SALES_ID WHERE A.ADD_TIME &gt; #{startTimestamp} limit #{offset},#{limit}
    </select>

</mapper>