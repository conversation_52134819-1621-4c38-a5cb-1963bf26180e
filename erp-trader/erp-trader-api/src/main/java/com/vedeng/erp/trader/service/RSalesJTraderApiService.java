package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.RSalesJTraderDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.service
 * @Date 2024/1/5 14:51
 */
public interface RSalesJTraderApiService {

    /**
     * 根据traderId查询当前客户的分享记录
     *
     * @param traderId traderId
     * @return List<RSalesJTraderDto>
     */
    List<RSalesJTraderDto> getShareTraderList(Integer traderId);

    /**
     * 判断A客户是否分享给了B这个人
     * @param traderId
     * @param userId
     * @return
     */
    RSalesJTraderDto getShareTraderByUserId(Integer traderId,Integer userId);

    List<Integer> getShareAndBelongTrader(List<Integer> allSubUserId, List<Integer> traderId);
}
