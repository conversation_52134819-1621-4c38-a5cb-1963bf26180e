<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.VisitRecordNewMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.VisitRecord">
        <!--@mbg.generated-->
        <!--@Table T_VISIT_RECORD -->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="PLAN_VISIT_DATE" jdbcType="DATE" property="planVisitDate"/>
        <result column="VISITOR_ID" jdbcType="INTEGER" property="visitorId"/>
        <result column="VISIT_TARGET" jdbcType="VARCHAR" property="visitTarget"/>
        <result column="PROVINCE_CODE" jdbcType="INTEGER" property="provinceCode"/>
        <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="provinceName"/>
        <result column="CITY_CODE" jdbcType="INTEGER" property="cityCode"/>
        <result column="CITY_NAME" jdbcType="VARCHAR" property="cityName"/>
        <result column="AREA_CODE" jdbcType="INTEGER" property="areaCode" />
        <result column="AREA_NAME" jdbcType="VARCHAR" property="areaName" />
        <result column="CUSTOMER_NAME" jdbcType="VARCHAR" property="customerName"/>
        <result column="CUSTOMER_FROM" jdbcType="INTEGER" property="customerFrom"/>
        <result column="CUSTOMER_NATURE" jdbcType="INTEGER" property="customerNature"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId"/>
        <result column="ACTUAL_VISIT_DATE" jdbcType="DATE" property="actualVisitDate"/>
        <result column="CARD_OFF" jdbcType="VARCHAR" property="cardOff"/>
        <result column="PICTURE_LIST" jdbcType="VARCHAR" property="pictureList"/>
        <result column="CONTACT_NAME" jdbcType="VARCHAR" property="contactName"/>
        <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="CONTACT_TELE" jdbcType="VARCHAR" property="contactTele"/>
        <result column="CONTACT_POSITION" jdbcType="VARCHAR" property="contactPosition"/>
        <result column="SHOW_PPT" jdbcType="VARCHAR" property="showPpt"/>
        <result column="INVITE_REG" jdbcType="VARCHAR" property="inviteReg"/>
        <result column="REG_MOBILE" jdbcType="VARCHAR" property="regMobile"/>
        <result column="TRADER_CONTRACT_ID" jdbcType="INTEGER" property="traderContractId"/>
        <result column="COMMUCATE_CONTENT" jdbcType="VARCHAR" property="commucateContent"/>
        <result column="NEXT_VISIT_DATE" jdbcType="DATE" property="nextVisitDate"/>
        <result column="CREATE_BUSINESS_CHANGE" jdbcType="VARCHAR" property="createBusinessChange"/>
        <result column="VISIT_SUCCESS" jdbcType="VARCHAR" property="visitSuccess"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="ADD_USER_ID" jdbcType="INTEGER" property="addUserId"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="MOD_USER_ID" jdbcType="INTEGER" property="modUserId"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId"/>
        <result column="BUSSINESS_CHANCE_NO" jdbcType="VARCHAR" property="bussinessChanceNo"/>
        <result column="VISITOR_NAME" jdbcType="VARCHAR" property="visitorName"/>
        <result column="CARD_TIME" jdbcType="VARCHAR" property="cardTime"/>
    </resultMap>

    <resultMap id="VisitResultMap" type="com.vedeng.erp.trader.dto.VisitInputApiDto">
        <!--@mbg.generated-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="PLAN_VISIT_DATE" jdbcType="DATE" property="planVisitDate"/>
        <result column="VISITOR_ID" jdbcType="INTEGER" property="visitorId"/>
        <result column="VISIT_TARGET" jdbcType="VARCHAR" property="visitTarget"/>
        <result column="PROVINCE_CODE" jdbcType="INTEGER" property="provinceCode"/>
        <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="provinceName"/>
        <result column="CITY_CODE" jdbcType="INTEGER" property="cityCode"/>
        <result column="CITY_NAME" jdbcType="VARCHAR" property="cityName"/>
        <result column="AREA_CODE" jdbcType="INTEGER" property="areaCode" />
        <result column="AREA_NAME" jdbcType="VARCHAR" property="areaName" />
        <result column="CUSTOMER_NAME" jdbcType="VARCHAR" property="customerName"/>
        <result column="CUSTOMER_FROM" jdbcType="INTEGER" property="customerFrom"/>
        <result column="CUSTOMER_NATURE" jdbcType="INTEGER" property="customerNature"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId"/>
        <result column="ACTUAL_VISIT_DATE" jdbcType="DATE" property="actualVisitDate"/>
        <result column="CARD_OFF" jdbcType="VARCHAR" property="cardOff"/>
        <result column="PICTURE_LIST" jdbcType="VARCHAR" property="pictureList"/>
        <result column="CONTACT_NAME" jdbcType="VARCHAR" property="contactName"/>
        <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="CONTACT_TELE" jdbcType="VARCHAR" property="contactTele"/>
        <result column="CONTACT_POSITION" jdbcType="VARCHAR" property="contactPosition"/>
        <result column="SHOW_PPT" jdbcType="VARCHAR" property="showPpt"/>
        <result column="INVITE_REG" jdbcType="VARCHAR" property="inviteReg"/>
        <result column="REG_MOBILE" jdbcType="VARCHAR" property="regMobile"/>
        <result column="TRADER_CONTRACT_ID" jdbcType="INTEGER" property="traderContractId"/>
        <result column="COMMUCATE_CONTENT" jdbcType="VARCHAR" property="commucateContent"/>
        <result column="NEXT_VISIT_DATE" jdbcType="DATE" property="nextVisitDate"/>
        <result column="CREATE_BUSINESS_CHANGE" jdbcType="VARCHAR" property="createBusinessChange"/>
        <result column="VISIT_SUCCESS" jdbcType="VARCHAR" property="visitSuccess"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="ADD_USER_ID" jdbcType="INTEGER" property="addUserId"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="MOD_USER_ID" jdbcType="INTEGER" property="modUserId"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId"/>
        <result column="BUSSINESS_CHANCE_NO" jdbcType="VARCHAR" property="bussinessChanceNo"/>
        <result column="VISITOR_NAME" jdbcType="VARCHAR" property="visitorName"/>
        <result column="TZ_CUSTOMER" jdbcType="INTEGER" property="customerTz"/>
        <result column="TZ_CUSTOMER_LEVEL" jdbcType="INTEGER" property="customerLevel"/>
        <result column="CARD_TIME" jdbcType="VARCHAR" property="cardTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        PLAN_VISIT_DATE,
        VISITOR_ID,
        VISIT_TARGET,
        PROVINCE_CODE,
        PROVINCE_NAME,
        CITY_CODE,
        CITY_NAME,
        CUSTOMER_NAME,
        CUSTOMER_FROM,
        CUSTOMER_NATURE,
        TRADER_ID,
        TRADER_CUSTOMER_ID,
        ACTUAL_VISIT_DATE,
        CARD_OFF,
        PICTURE_LIST,
        CONTACT_NAME,
        CONTACT_MOBILE,
        CONTACT_TELE,
        CONTACT_POSITION,
        SHOW_PPT,
        INVITE_REG,
        REG_MOBILE,
        TRADER_CONTRACT_ID,
        COMMUCATE_CONTENT,
        NEXT_VISIT_DATE,
        CREATE_BUSINESS_CHANGE,
        VISIT_SUCCESS,
        ADD_TIME,
        ADD_USER_ID,
        MOD_TIME,
        MOD_USER_ID,
        IS_DELETE,
        BUSSINESS_CHANCE_ID,
        BUSSINESS_CHANCE_NO,
        VISITOR_NAME,
        CARD_TIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_VISIT_RECORD
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_VISIT_RECORD
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.VisitRecord"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_VISIT_RECORD (PLAN_VISIT_DATE, VISITOR_ID, VISIT_TARGET,
                                    PROVINCE_CODE, PROVINCE_NAME, CITY_CODE,
                                    CITY_NAME, CUSTOMER_NAME, CUSTOMER_FROM,
                                    CUSTOMER_NATURE, TRADER_ID, TRADER_CUSTOMER_ID,
                                    ACTUAL_VISIT_DATE, CARD_OFF, PICTURE_LIST,
                                    CONTACT_NAME, CONTACT_MOBILE, CONTACT_TELE,
                                    CONTACT_POSITION, SHOW_PPT, INVITE_REG,
                                    REG_MOBILE, TRADER_CONTRACT_ID, COMMUCATE_CONTENT,
                                    NEXT_VISIT_DATE, CREATE_BUSINESS_CHANGE, VISIT_SUCCESS,
                                    ADD_TIME, ADD_USER_ID, MOD_TIME,
                                    MOD_USER_ID, IS_DELETE, BUSSINESS_CHANCE_ID,
                                    BUSSINESS_CHANCE_NO, VISITOR_NAME, CARD_TIME)
        values (#{planVisitDate,jdbcType=DATE}, #{visitorId,jdbcType=INTEGER}, #{visitTarget,jdbcType=VARCHAR},
                #{provinceCode,jdbcType=INTEGER}, #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=INTEGER},
                #{cityName,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{customerFrom,jdbcType=INTEGER},
                #{customerNature,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER},
                #{actualVisitDate,jdbcType=DATE}, #{cardOff,jdbcType=VARCHAR}, #{pictureList,jdbcType=VARCHAR},
                #{contactName,jdbcType=VARCHAR}, #{contactMobile,jdbcType=VARCHAR}, #{contactTele,jdbcType=VARCHAR},
                #{contactPosition,jdbcType=VARCHAR}, #{showPpt,jdbcType=VARCHAR}, #{inviteReg,jdbcType=VARCHAR},
                #{regMobile,jdbcType=VARCHAR}, #{traderContractId,jdbcType=INTEGER},
                #{commucateContent,jdbcType=VARCHAR},
                #{nextVisitDate,jdbcType=DATE}, #{createBusinessChange,jdbcType=VARCHAR},
                #{visitSuccess,jdbcType=VARCHAR},
                #{addTime,jdbcType=TIMESTAMP}, #{addUserId,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP},
                #{modUserId,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{bussinessChanceId,jdbcType=INTEGER},
                #{bussinessChanceNo,jdbcType=VARCHAR}, #{visitorName,jdbcType=VARCHAR}, #{cardTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.trader.domain.entity.VisitRecord" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_VISIT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planVisitDate != null">
                PLAN_VISIT_DATE,
            </if>
            <if test="visitorId != null">
                VISITOR_ID,
            </if>
            <if test="visitTarget != null">
                VISIT_TARGET,
            </if>
            <if test="provinceCode != null">
                PROVINCE_CODE,
            </if>
            <if test="provinceName != null">
                PROVINCE_NAME,
            </if>
            <if test="cityCode != null">
                CITY_CODE,
            </if>
            <if test="cityName != null">
                CITY_NAME,
            </if>
            <if test="customerName != null">
                CUSTOMER_NAME,
            </if>
            <if test="customerFrom != null">
                CUSTOMER_FROM,
            </if>
            <if test="customerNature != null">
                CUSTOMER_NATURE,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="traderCustomerId != null">
                TRADER_CUSTOMER_ID,
            </if>
            <if test="actualVisitDate != null">
                ACTUAL_VISIT_DATE,
            </if>
            <if test="cardOff != null">
                CARD_OFF,
            </if>
            <if test="pictureList != null">
                PICTURE_LIST,
            </if>
            <if test="contactName != null">
                CONTACT_NAME,
            </if>
            <if test="contactMobile != null">
                CONTACT_MOBILE,
            </if>
            <if test="contactTele != null">
                CONTACT_TELE,
            </if>
            <if test="contactPosition != null">
                CONTACT_POSITION,
            </if>
            <if test="showPpt != null">
                SHOW_PPT,
            </if>
            <if test="inviteReg != null">
                INVITE_REG,
            </if>
            <if test="regMobile != null">
                REG_MOBILE,
            </if>
            <if test="traderContractId != null">
                TRADER_CONTRACT_ID,
            </if>
            <if test="commucateContent != null">
                COMMUCATE_CONTENT,
            </if>
            <if test="nextVisitDate != null">
                NEXT_VISIT_DATE,
            </if>
            <if test="createBusinessChange != null">
                CREATE_BUSINESS_CHANGE,
            </if>
            <if test="visitSuccess != null">
                VISIT_SUCCESS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="addUserId != null">
                ADD_USER_ID,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="modUserId != null">
                MOD_USER_ID,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="bussinessChanceId != null">
                BUSSINESS_CHANCE_ID,
            </if>
            <if test="bussinessChanceNo != null">
                BUSSINESS_CHANCE_NO,
            </if>
            <if test="visitorName != null">
                VISITOR_NAME,
            </if>
            <if test="cardTime != null">
                CARD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planVisitDate != null">
                #{planVisitDate,jdbcType=DATE},
            </if>
            <if test="visitorId != null">
                #{visitorId,jdbcType=INTEGER},
            </if>
            <if test="visitTarget != null">
                #{visitTarget,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=INTEGER},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=INTEGER},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerFrom != null">
                #{customerFrom,jdbcType=INTEGER},
            </if>
            <if test="customerNature != null">
                #{customerNature,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderCustomerId != null">
                #{traderCustomerId,jdbcType=INTEGER},
            </if>
            <if test="actualVisitDate != null">
                #{actualVisitDate,jdbcType=DATE},
            </if>
            <if test="cardOff != null">
                #{cardOff,jdbcType=VARCHAR},
            </if>
            <if test="pictureList != null">
                #{pictureList,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null">
                #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactMobile != null">
                #{contactMobile,jdbcType=VARCHAR},
            </if>
            <if test="contactTele != null">
                #{contactTele,jdbcType=VARCHAR},
            </if>
            <if test="contactPosition != null">
                #{contactPosition,jdbcType=VARCHAR},
            </if>
            <if test="showPpt != null">
                #{showPpt,jdbcType=VARCHAR},
            </if>
            <if test="inviteReg != null">
                #{inviteReg,jdbcType=VARCHAR},
            </if>
            <if test="regMobile != null">
                #{regMobile,jdbcType=VARCHAR},
            </if>
            <if test="traderContractId != null">
                #{traderContractId,jdbcType=INTEGER},
            </if>
            <if test="commucateContent != null">
                #{commucateContent,jdbcType=VARCHAR},
            </if>
            <if test="nextVisitDate != null">
                #{nextVisitDate,jdbcType=DATE},
            </if>
            <if test="createBusinessChange != null">
                #{createBusinessChange,jdbcType=VARCHAR},
            </if>
            <if test="visitSuccess != null">
                #{visitSuccess,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addUserId != null">
                #{addUserId,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modUserId != null">
                #{modUserId,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="bussinessChanceId != null">
                #{bussinessChanceId,jdbcType=INTEGER},
            </if>
            <if test="bussinessChanceNo != null">
                #{bussinessChanceNo,jdbcType=VARCHAR},
            </if>
            <if test="visitorName != null">
                #{visitorName,jdbcType=VARCHAR},
            </if>
            <if test="cardTime != null">
                #{cardTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.VisitRecord">
        <!--@mbg.generated-->
        update T_VISIT_RECORD
        <set>
            <if test="planVisitDate != null">
                PLAN_VISIT_DATE = #{planVisitDate,jdbcType=DATE},
            </if>
            <if test="visitorId != null">
                VISITOR_ID = #{visitorId,jdbcType=INTEGER},
            </if>
            <if test="visitTarget != null">
                VISIT_TARGET = #{visitTarget,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                PROVINCE_CODE = #{provinceCode,jdbcType=INTEGER},
            </if>
            <if test="provinceName != null">
                PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                CITY_CODE = #{cityCode,jdbcType=INTEGER},
            </if>
            <if test="cityName != null">
                CITY_NAME = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="customerName != null">
                CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerFrom != null">
                CUSTOMER_FROM = #{customerFrom,jdbcType=INTEGER},
            </if>
            <if test="customerNature != null">
                CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderCustomerId != null">
                TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
            </if>
            <if test="actualVisitDate != null">
                ACTUAL_VISIT_DATE = #{actualVisitDate,jdbcType=DATE},
            </if>
            <if test="cardOff != null">
                CARD_OFF = #{cardOff,jdbcType=VARCHAR},
            </if>
            <if test="pictureList != null">
                PICTURE_LIST = #{pictureList,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null">
                CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactMobile != null">
                CONTACT_MOBILE = #{contactMobile,jdbcType=VARCHAR},
            </if>
            <if test="contactTele != null">
                CONTACT_TELE = #{contactTele,jdbcType=VARCHAR},
            </if>
            <if test="contactPosition != null">
                CONTACT_POSITION = #{contactPosition,jdbcType=VARCHAR},
            </if>
            <if test="showPpt != null">
                SHOW_PPT = #{showPpt,jdbcType=VARCHAR},
            </if>
            <if test="inviteReg != null">
                INVITE_REG = #{inviteReg,jdbcType=VARCHAR},
            </if>
            <if test="regMobile != null">
                REG_MOBILE = #{regMobile,jdbcType=VARCHAR},
            </if>
            <if test="traderContractId != null">
                TRADER_CONTRACT_ID = #{traderContractId,jdbcType=INTEGER},
            </if>
            <if test="commucateContent != null">
                COMMUCATE_CONTENT = #{commucateContent,jdbcType=VARCHAR},
            </if>
            <if test="nextVisitDate != null">
                NEXT_VISIT_DATE = #{nextVisitDate,jdbcType=DATE},
            </if>
            <if test="createBusinessChange != null">
                CREATE_BUSINESS_CHANGE = #{createBusinessChange,jdbcType=VARCHAR},
            </if>
            <if test="visitSuccess != null">
                VISIT_SUCCESS = #{visitSuccess,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addUserId != null">
                ADD_USER_ID = #{addUserId,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modUserId != null">
                MOD_USER_ID = #{modUserId,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="bussinessChanceId != null">
                BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER},
            </if>
            <if test="bussinessChanceNo != null">
                BUSSINESS_CHANCE_NO = #{bussinessChanceNo,jdbcType=VARCHAR},
            </if>
            <if test="visitorName != null">
                VISITOR_NAME = #{visitorName,jdbcType=VARCHAR},
            </if>
            <if test="cardTime != null">
                CARD_TIME = #{cardTime,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>



    <update id="updateVisitRecordForOrgGroup" parameterType="com.vedeng.erp.trader.dto.VisitInputDto">
        <!--@mbg.generated-->
        update T_VISIT_RECORD
        set
        ORG_ID=#{orgId,jdbcType=INTEGER},
        ORG_GROUP=#{orgGroup,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.VisitRecord">
        <!--@mbg.generated-->
        update T_VISIT_RECORD
        set PLAN_VISIT_DATE        = #{planVisitDate,jdbcType=DATE},
            VISITOR_ID             = #{visitorId,jdbcType=INTEGER},
            VISIT_TARGET           = #{visitTarget,jdbcType=VARCHAR},
            PROVINCE_CODE          = #{provinceCode,jdbcType=INTEGER},
            PROVINCE_NAME          = #{provinceName,jdbcType=VARCHAR},
            CITY_CODE              = #{cityCode,jdbcType=INTEGER},
            CITY_NAME              = #{cityName,jdbcType=VARCHAR},
            CUSTOMER_NAME          = #{customerName,jdbcType=VARCHAR},
            CUSTOMER_FROM          = #{customerFrom,jdbcType=INTEGER},
            CUSTOMER_NATURE        = #{customerNature,jdbcType=INTEGER},
            TRADER_ID              = #{traderId,jdbcType=INTEGER},
            TRADER_CUSTOMER_ID     = #{traderCustomerId,jdbcType=INTEGER},
            ACTUAL_VISIT_DATE      = #{actualVisitDate,jdbcType=DATE},
            CARD_OFF               = #{cardOff,jdbcType=VARCHAR},
            PICTURE_LIST           = #{pictureList,jdbcType=VARCHAR},
            CONTACT_NAME           = #{contactName,jdbcType=VARCHAR},
            CONTACT_MOBILE         = #{contactMobile,jdbcType=VARCHAR},
            CONTACT_TELE           = #{contactTele,jdbcType=VARCHAR},
            CONTACT_POSITION       = #{contactPosition,jdbcType=VARCHAR},
            SHOW_PPT               = #{showPpt,jdbcType=VARCHAR},
            INVITE_REG             = #{inviteReg,jdbcType=VARCHAR},
            REG_MOBILE             = #{regMobile,jdbcType=VARCHAR},
            TRADER_CONTRACT_ID     = #{traderContractId,jdbcType=INTEGER},
            COMMUCATE_CONTENT      = #{commucateContent,jdbcType=VARCHAR},
            NEXT_VISIT_DATE        = #{nextVisitDate,jdbcType=DATE},
            CREATE_BUSINESS_CHANGE = #{createBusinessChange,jdbcType=VARCHAR},
            VISIT_SUCCESS          = #{visitSuccess,jdbcType=VARCHAR},
            ADD_TIME               = #{addTime,jdbcType=TIMESTAMP},
            ADD_USER_ID            = #{addUserId,jdbcType=INTEGER},
            MOD_TIME               = #{modTime,jdbcType=TIMESTAMP},
            MOD_USER_ID            = #{modUserId,jdbcType=INTEGER},
            IS_DELETE              = #{isDelete,jdbcType=INTEGER},
            BUSSINESS_CHANCE_ID    = #{bussinessChanceId,jdbcType=INTEGER},
            BUSSINESS_CHANCE_NO    = #{bussinessChanceNo,jdbcType=VARCHAR},
            VISITOR_NAME           = #{visitorName,jdbcType=VARCHAR},
            CARD_TIME              = #{cardTime,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=INTEGER}
    </update>




    <select id="selectHistoryVisitRecord"   resultType="com.vedeng.erp.trader.dto.VisitInputDto">
        <!--@mbg.generated-->
        select A.*
        from T_VISIT_RECORD A WHERE ORG_GROUP=''
    </select>

    <select id="selectVisitInputDtoByPrimaryKey" parameterType="java.lang.Integer" resultMap="VisitResultMap">
        <!--@mbg.generated-->
        select A.*,
               C.TZ_CUSTOMER,
               C.TZ_CUSTOMER_LEVEL,
               BC.STATUS,
               CASE BC.STATUS
                   WHEN 0 THEN '未处理'
                   WHEN 1 THEN '报价中'
                   WHEN 2 THEN '已报价'
                   WHEN 3 THEN '已订单'
                   WHEN 4 THEN '已关闭'
                   WHEN 5 THEN '未分配'
                   WHEN 6 THEN '处理中'
                   WHEN 7 THEN '已成单'
                   ELSE '' END AS STATUS_NAME
        from T_VISIT_RECORD A
                 LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
                           ON A.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND A.TRADER_ID = C.TRADER_ID
                 LEFT JOIN T_BUSSINESS_CHANCE BC
                           ON A.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        where A.ID = #{id,jdbcType=INTEGER}
    </select>

    <select id="queryVisitRecordByVisitorId" parameterType="java.lang.Integer" resultType="java.util.Map">
        <!--@mbg.generated-->
        select COUNT(1) as visitrecordNum
        from T_VISIT_RECORD A
        where A.VISITOR_ID = #{visitorId,jdbcType=INTEGER}
          AND A.IS_DELETE = 0
          AND (A.CARD_OFF = 'N' OR A.CARD_OFF IS NULL)
    </select>


    <sql id="Page_Column_List">
        TVR.*,
        C.TZ_CUSTOMER       AS customerTz,
        C.TZ_CUSTOMER_LEVEL AS customerLevel,
        BC.STATUS,
        CUSER.USERNAME         belongUserName,
        U.USERNAME visitorName,
        CASE BC.STATUS
            WHEN 0 THEN '未处理'
            WHEN 1 THEN '报价中'
            WHEN 2 THEN '已报价'
            WHEN 3 THEN '已订单'
            WHEN 4 THEN '已关闭'
            WHEN 5 THEN '未分配'
            WHEN 6 THEN '处理中'
            WHEN 7 THEN '已成单'
            ELSE '' END     AS STATUS_NAME,
        CASE
            WHEN TVR.CARD_OFF = 'N' OR TVR.CARD_OFF IS NULL then '未打卡'
            WHEN TVR.CARD_OFF = 'Y' and TVR.VISIT_SUCCESS = 'N' AND TVR.CONTACT_NAME IS NULL then '仅打卡'
            WHEN TVR.CARD_OFF = 'Y' AND TVR.VISIT_SUCCESS = 'N' AND TVR.CONTACT_NAME IS NOT NULL then '拜访事项缺失'
            WHEN TVR.VISIT_SUCCESS = 'Y' then '拜访成功'
            else ''
            END             AS visitResult
    </sql>


    <select id="searchVisitRecordListPage" resultType="com.vedeng.erp.trader.dto.VisitInputDto">

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
        LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        <if test="visitSearchDto.customerNature != null or visitSearchDto.customrTz == 1">
            LEFT JOIN T_TRADER_CUSTOMER B
            ON TVR.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = B.TRADER_ID
        </if>
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
        ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
        ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>

        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size() > 0">
            and
                (TVR.ADD_USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR TVR.VISITOR_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR TVR.ID IN (
            SELECT A1.ID
            FROM T_VISIT_RECORD A1
                     LEFT JOIN T_R_TRADER_J_USER R1 ON A1.TRADER_ID = R1.TRADER_ID AND R1.TRADER_TYPE = 1
                     LEFT JOIN T_R_SALES_J_TRADER SHARE1 ON A1.TRADER_CUSTOMER_ID = SHARE1.TRADER_CUSTOMER_ID AND
                                                            A1.TRADER_ID = SHARE1.TRADER_ID
            WHERE SHARE1.SALE_USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR R1.USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>)
            )
        </if>
        and TVR.ADD_TIME &lt;= #{visitSearchDto.historyTime}

        union all

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
            LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        <if test="visitSearchDto.customerNature != null or visitSearchDto.customrTz == 1">
            LEFT JOIN T_TRADER_CUSTOMER B
                      ON TVR.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = B.TRADER_ID
        </if>
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
                  ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
                  ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>


        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size() > 0">
            AND (
                TVR.TRADER_ID in (
            select TRADER_ID
            from T_R_TRADER_J_USER where TRADER_TYPE = 1
                                     and USER_ID in
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            )
                OR
                TVR.TRADER_ID in (
            select TRADER_ID
            from T_R_SALES_J_TRADER where
                SALE_USER_ID in
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            )
                OR TVR.TRADER_ID IN (
            select DISTINCT TW.TRADER_ID
            FROM T_CUSTOMER_REGION_SALE TS
            JOIN T_TRADER_WORKAREA TW ON TS.REGION_ID = TW.AREA_ID
            JOIN T_R_TRADER_J_USER TRJ
            ON TRJ.TRADER_ID = TW.TRADER_ID AND TRJ.USER_ID = TS.USER_ID and TRJ.TRADER_TYPE = 1
            WHERE TS.USER_ID_DOWN IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            )
                OR TVR.TRADER_ID IN (
            select DISTINCT TW.TRADER_ID
            FROM T_CUSTOMER_REGION_SALE TS
                     JOIN T_TRADER_WORKAREA TW ON TS.REGION_ID = TW.AREA_ID
                     JOIN T_R_TRADER_J_USER TRJ
                          ON TRJ.TRADER_ID = TW.TRADER_ID AND TRJ.USER_ID = TS.USER_ID_DOWN and TRJ.TRADER_TYPE = 1
            WHERE TS.USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            )
            )
        </if>
             and TVR.ADD_TIME &gt; #{visitSearchDto.historyTime}

        union all

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
        LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        <if test="visitSearchDto.customerNature != null or visitSearchDto.customrTz == 1">
            LEFT JOIN T_TRADER_CUSTOMER B
            ON TVR.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = B.TRADER_ID
        </if>
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
        ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
        ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        LEFT JOIN T_CUSTOMER_REGION_SALE TCRS  on TVR.AREA_CODE= TCRS.REGION_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>
        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size() > 0">
            and TCRS.USER_ID_DOWN IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        and TVR.TRADER_ID  IS NULL
        and TVR.ADD_TIME &gt; #{visitSearchDto.historyTime}

    </select>

    <select id="searchVisitRecordListPageForYingji" resultType="com.vedeng.erp.trader.dto.VisitInputDto">

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
        LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        <if test="visitSearchDto.customerNature != null or visitSearchDto.customrTz == 1">
            LEFT JOIN T_TRADER_CUSTOMER B
            ON TVR.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = B.TRADER_ID
        </if>
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
        ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
        ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>

        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size() > 0">
            and
            (TVR.ADD_USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR TVR.VISITOR_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR TVR.ID IN (
            SELECT A1.ID
            FROM T_VISIT_RECORD A1
            LEFT JOIN T_R_TRADER_J_USER R1 ON A1.TRADER_ID = R1.TRADER_ID AND R1.TRADER_TYPE = 1
            LEFT JOIN T_R_SALES_J_TRADER SHARE1 ON A1.TRADER_CUSTOMER_ID = SHARE1.TRADER_CUSTOMER_ID AND
            A1.TRADER_ID = SHARE1.TRADER_ID
            WHERE SHARE1.SALE_USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR R1.USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>)
            )
        </if>
        and TVR.ADD_TIME &lt;= #{visitSearchDto.historyTime}

        union all

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
            LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
                  ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
                  ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>

        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size != 0">
            AND TVR.TRADER_ID in (
            select TRADER_ID
            from T_R_TRADER_J_USER where TRADER_TYPE = 1
                                     and USER_ID in
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            )
        </if>
        and TVR.ADD_TIME &gt; #{visitSearchDto.historyTime}

        union all

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
        LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        <if test="visitSearchDto.customerNature != null or visitSearchDto.customrTz == 1">
            LEFT JOIN T_TRADER_CUSTOMER B
            ON TVR.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = B.TRADER_ID
        </if>
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
        ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
        ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        LEFT JOIN T_CUSTOMER_REGION_SALE TCRS  on TVR.AREA_CODE= TCRS.REGION_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>
        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size() > 0">
            and TCRS.USER_ID_DOWN IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        and TVR.TRADER_ID  IS NULL
        and TVR.ADD_TIME &gt; #{visitSearchDto.historyTime}
    </select>

    <select id="searchVisitRecordListPageForFeigong" resultType="com.vedeng.erp.trader.dto.VisitInputDto">

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
        LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        <if test="visitSearchDto.customerNature != null or visitSearchDto.customrTz == 1">
            LEFT JOIN T_TRADER_CUSTOMER B
            ON TVR.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = B.TRADER_ID
        </if>
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
        ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
        ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>

        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size() > 0">
            and
            (TVR.ADD_USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR TVR.VISITOR_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR TVR.ID IN (
            SELECT A1.ID
            FROM T_VISIT_RECORD A1
            LEFT JOIN T_R_TRADER_J_USER R1 ON A1.TRADER_ID = R1.TRADER_ID AND R1.TRADER_TYPE = 1
            LEFT JOIN T_R_SALES_J_TRADER SHARE1 ON A1.TRADER_CUSTOMER_ID = SHARE1.TRADER_CUSTOMER_ID AND
            A1.TRADER_ID = SHARE1.TRADER_ID
            WHERE SHARE1.SALE_USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            OR R1.USER_ID IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>)
            )
        </if>
        and TVR.ADD_TIME &lt;= #{visitSearchDto.historyTime}

        union all

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
            LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        <if test="visitSearchDto.customerNature != null or visitSearchDto.customrTz == 1">
            LEFT JOIN T_TRADER_CUSTOMER B
                      ON TVR.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = B.TRADER_ID
        </if>
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
                  ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
                  ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>


        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size() > 0">
            AND (
                TVR.TRADER_ID in (
            select TRADER_ID
            from T_R_TRADER_J_USER where TRADER_TYPE = 1
                                     and USER_ID in
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            )
                OR
                TVR.TRADER_ID in (
            select TRADER_ID
            from T_R_SALES_J_TRADER where
                SALE_USER_ID in
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
            )
                )
        </if>
        and TVR.ADD_TIME &gt; #{visitSearchDto.historyTime}

        union all

        SELECT
        <include refid="Page_Column_List"/>
        FROM T_VISIT_RECORD TVR
        LEFT JOIN T_USER U ON TVR.VISITOR_ID = U.USER_ID
        <if test="visitSearchDto.customerNature != null or visitSearchDto.customrTz == 1">
            LEFT JOIN T_TRADER_CUSTOMER B
            ON TVR.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = B.TRADER_ID
        </if>
        LEFT JOIN T_R_TRADER_J_USER R ON TVR.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
        LEFT JOIN T_USER CUSER ON R.USER_ID = CUSER.USER_ID
        LEFT JOIN T_BUSSINESS_CHANCE BC
        ON TVR.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0, 2)
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C
        ON TVR.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND TVR.TRADER_ID = C.TRADER_ID
        LEFT JOIN T_CUSTOMER_REGION_SALE TCRS  on TVR.AREA_CODE= TCRS.REGION_ID
        WHERE

        <!--公共条件-->
        <include refid="Page_Where_List"/>
        <if test="visitSearchDto.userIdList != null and visitSearchDto.userIdList.size() > 0">
            and TCRS.USER_ID_DOWN IN
            <foreach collection="visitSearchDto.userIdList" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        and TVR.TRADER_ID  IS NULL
        and TVR.ADD_TIME &gt; #{visitSearchDto.historyTime}
    </select>

    <!-- 分页条件 -->
    <sql id="Page_Where_List">
            TVR.IS_DELETE = 0
        <!-- 相同的 where 条件 -->

        <if test="visitSearchDto.visitIdList != null and visitSearchDto.visitIdList.size() > 0">
            AND (
                TVR.VISITOR_ID in
            <foreach collection="visitSearchDto.visitIdList" item="visitId" index="index"
                     open="(" close=")" separator=",">
                #{
            visitId}
            </foreach>
            )
        </if>

        <if test="visitSearchDto.customerName != null and visitSearchDto.customerName != ''">
            AND TVR.CUSTOMER_NAME like CONCAT('%', #{visitSearchDto.customerName}, '%')
        </if>

        <!-- 客户类型 全部 经销商 终端 -->
        <if test="visitSearchDto.customerNature != null">
            AND TVR.CUSTOMER_NATURE = #{visitSearchDto.customerNature,jdbcType=INTEGER}
        </if>

        <if test="visitSearchDto.tzCustomer == 0">
            AND C.TZ_CUSTOMER IS NOT NULL
        </if>
        <if test="visitSearchDto.tzCustomer == 1">
            AND C.TZ_CUSTOMER IS NULL
        </if>

        <if test="visitSearchDto.customerLevel != null and visitSearchDto.customerLevel != ''">
            AND C.TZ_CUSTOMER_LEVEL = #{visitSearchDto.customerLevel,jdbcType=INTEGER}
        </if>

        <if test="visitSearchDto.vdCustomer == 0">
            AND TVR.TRADER_ID IN (select distinct TRADER_ID FROM T_WEB_ACCOUNT where TRADER_ID is not null)
        </if>
        <if test="visitSearchDto.vdCustomer == 1">
            AND TVR.TRADER_ID NOT IN (select distinct TRADER_ID FROM T_WEB_ACCOUNT where TRADER_ID is not null)
        </if>


        <if test="visitSearchDto.actualVisitDay != null">
            AND TVR.ACTUAL_VISIT_DATE between (CURDATE() - INTERVAL ${visitSearchDto.actualVisitDay} DAY) and CURDATE()
        </if>

        <!--拜访结果 1未打卡 2仅打卡 3拜访事项缺失 4 拜访成功 -->
        <if test="visitSearchDto.actualVisitResult != null and visitSearchDto.actualVisitResult == 1">
            AND (TVR.CARD_OFF = 'N' OR TVR.CARD_OFF IS NULL)
        </if>
        <if test="visitSearchDto.actualVisitResult != null and visitSearchDto.actualVisitResult == 2">
            AND TVR.CARD_OFF = 'Y'
                and TVR.VISIT_SUCCESS = 'N'
                AND TVR.CONTACT_NAME IS NULL
        </if>
        <if test="visitSearchDto.actualVisitResult != null and visitSearchDto.actualVisitResult == 3">
            AND TVR.CARD_OFF = 'Y'
                AND TVR.VISIT_SUCCESS = 'N'
                AND TVR.CONTACT_NAME IS NOT NULL
        </if>
        <if test="visitSearchDto.actualVisitResult != null and visitSearchDto.actualVisitResult == 4">
            AND TVR.VISIT_SUCCESS = 'Y'
        </if>


        <!-- 客户会员 全部 1普通会员 2同舟会员 3非客户 -->
        <if test="visitSearchDto.customrTz != null and visitSearchDto.customrTz == 1">
            AND B.TRADER_CUSTOMER_ID is not null
                AND C.TZ_CUSTOMER IS NULL
        </if>
        <if test="visitSearchDto.customrTz != null and visitSearchDto.customrTz == 2">
            AND C.TZ_CUSTOMER = 1
        </if>
        <if test="visitSearchDto.customrTz != null and visitSearchDto.customrTz == 3">
            AND TVR.TRADER_CUSTOMER_ID is null
        </if>

        <if test="visitSearchDto.businessChanceNo != null and visitSearchDto.businessChanceNo != ''">
            AND TVR.BUSSINESS_CHANCE_NO = #{visitSearchDto.businessChanceNo,jdbcType=VARCHAR}
        </if>

        <if test="visitSearchDto.businessChanceStatus != null and visitSearchDto.businessChanceStatus != ''">
            AND BC.STATUS = #{visitSearchDto.businessChanceStatus,jdbcType=INTEGER}
        </if>

        <if test="visitSearchDto.commucateContent != null and visitSearchDto.commucateContent != ''">
            AND TVR.COMMUCATE_CONTENT like CONCAT('%', #{visitSearchDto.commucateContent}, '%')
        </if>

        <if test="visitSearchDto.planVisitDateStart != null and visitSearchDto.planVisitDateStart != ''
        and visitSearchDto.planVisitDateEnd != null and visitSearchDto.planVisitDateEnd != ''">
            AND TVR.PLAN_VISIT_DATE between #{visitSearchDto.planVisitDateStart} and #{visitSearchDto.planVisitDateEnd}
        </if>

        <if test="visitSearchDto.actualVisitDateStart != null and visitSearchDto.actualVisitDateStart != ''
        and visitSearchDto.actualVisitDateEnd != null and visitSearchDto.actualVisitDateEnd != ''">
            AND TVR.CARD_TIME between #{visitSearchDto.actualVisitDateStart} and #{visitSearchDto.actualVisitDateEnd}
        </if>
    </sql>


    <select id="checkIfTraderExists" resultType="java.lang.Boolean">
        SELECT EXISTS (
        select TRTJU.TRADER_ID
        from T_R_TRADER_J_USER TRTJU
        <if test="customerType != null">
        left join T_TRADER_CUSTOMER TTC on TRTJU.TRADER_ID = TTC.TRADER_ID
        </if>
        where TRADER_TYPE = 1
        and USER_ID in
        <foreach collection="userIdList" item="userId" index="index"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
        and TRTJU.TRADER_ID = #{traderId}
        <if test="customerType != null">
        and TTC.CUSTOMER_TYPE = 427
        </if>

        UNION ALL

        select TRTJU.TRADER_ID
        from T_R_SALES_J_TRADER TRTJU
        <if test="customerType != null">
            left join T_TRADER_CUSTOMER TTC on TRTJU.TRADER_ID = TTC.TRADER_ID
        </if>
        where SALE_USER_ID in
        <foreach collection="userIdList" item="userId" index="index"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
        and TRTJU.TRADER_ID =  #{traderId}
        <if test="customerType != null">
            and TTC.CUSTOMER_TYPE = 427
        </if>

        UNION ALL

        select DISTINCT TW.TRADER_ID
        FROM T_CUSTOMER_REGION_SALE TS
        JOIN T_TRADER_WORKAREA TW ON TS.REGION_ID = TW.AREA_ID
        JOIN T_R_TRADER_J_USER TRJ
        ON TRJ.TRADER_ID = TW.TRADER_ID AND TRJ.USER_ID = TS.USER_ID and TRJ.TRADER_TYPE = 1
        <if test="customerType != null">
            left join T_TRADER_CUSTOMER TTC on TRJ.TRADER_ID = TTC.TRADER_ID
        </if>
        WHERE TS.USER_ID_DOWN IN
        <foreach collection="userIdList" item="userId" index="index"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
        and TW.TRADER_ID =  #{traderId}
        <if test="customerType != null">
            and TTC.CUSTOMER_TYPE = 427
        </if>

        UNION ALL

        select DISTINCT TW.TRADER_ID
        FROM T_CUSTOMER_REGION_SALE TS
        JOIN T_TRADER_WORKAREA TW ON TS.REGION_ID = TW.AREA_ID
        JOIN T_R_TRADER_J_USER TRJ
        ON TRJ.TRADER_ID = TW.TRADER_ID AND TRJ.USER_ID = TS.USER_ID_DOWN and TRJ.TRADER_TYPE = 1
        <if test="customerType != null">
            left join T_TRADER_CUSTOMER TTC on TRJ.TRADER_ID = TTC.TRADER_ID
        </if>
        WHERE TS.USER_ID IN
        <foreach collection="userIdList" item="userId" index="index"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
        and TW.TRADER_ID =  #{traderId}
        <if test="customerType != null">
            and TTC.CUSTOMER_TYPE = 427
        </if>

        ) AS traderExists
    </select>

</mapper>