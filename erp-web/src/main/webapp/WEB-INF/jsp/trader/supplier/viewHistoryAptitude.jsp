<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib prefix="date" uri="http://com.vedeng.common.util/tags" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>资质历史数据</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/list.css?rnd=${resourceVersionKey}">
    <style>
        .pager-wrap{
            width: 600px;
            text-align: center;
        }
    </style>
</head>

<body>
<div class="main-container">
    <div class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered" style="width: 600px">
            <tbody>
            <c:forEach var="aptitude" items="${aptitudeHistory}"  varStatus="st">
                <tr>
                    <td style="border: 1px solid transparent !important;text-align: center">
                        <c:if test="${pageNo eq 1}">
                            <c:set var="pageIndex" value="${st.index + 1}"></c:set>
                        </c:if>
                        <c:if test="${pageNo ne 1}">
                            <c:set var="pageIndex" value="${5 * (pageNo-1) + st.index + 1}"></c:set>
                        </c:if>
                        <a href="http://${aptitude.domain}${aptitude.uri}" target="_blank">${aptitudeName} - ${pageIndex}</a>&nbsp;&nbsp;
                    </td>
                    <c:if test="${hasAptitudeTime eq 1}">
                        <td style="border: 1px solid transparent !important;text-align: center">有效期：<date:date value ="${aptitude.begintime}" format="yyyy-MM-dd"/>
                            <c:if test="${aptitude ne null && (aptitude.begintime ne null && aptitude.begintime ne 0) && (aptitude.endtime eq null || aptitude.endtime eq 0 )}">-无限期</c:if>
                            <c:if test="${aptitude.endtime ne null && aptitude.endtime ne 0}">-<date:date value ="${aptitude.endtime}" format="yyyy-MM-dd"/></c:if>
                            <c:if test="${aptitude.endtime ne null && aptitude.endtime ne 0 && aptitude.endtime lt now }"><span style="color: red">（已过期）</span></c:if></td>
                    </c:if>

                </tr>
            </c:forEach>
            <c:if test="${empty aptitudeHistory}">
                <tr>
                    <td colspan='7'>查询无结果！请尝试使用其它搜索条件。</td>
                </tr>
            </c:if>
            </tbody>
        </table>
        <tags:pageNew page="${page}" />
    </div>

    <%--    <tags:page page="${page}" />--%>

</div>
</body>

<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>