package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 注册证生产模式和生产企业信息表
 */
@Getter
@Setter
public class RegistrationProductionModeEntity extends BaseEntity {
    private Integer productionModeId;

    /**
     * 注册证号信息表的ID
     */
    private Integer registrationNumberId;

    /**
     * 生产模式: 0自行生产, 1委托生产
     */
    private Integer productionMode;

    /**
     * 生产企业名称
     */
    private String enterpriseName;

    /**
     * 生产企业ID
     */
    private Integer manufacturerId;
}