package com.vedeng.crm.visitrecord.domain.vo;

import lombok.Data;

/**
 * @Description 拜访记录关联业务校验结果
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/1
 */
@Data
public class VisitBizCheckVo {
    /**
     * 校验结果 true:通过 false:不通过
     */
    private Boolean checkResult = true;

    /**
     * 错误信息（阻断提交的错误）
     */
    private String errorMsg;

    /**
     * 提示信息（不阻断提交的提示）
     */
    private String warnMsg;

    /**
     * 单据类型（1线索 2商机）
     */
    private Integer relateType;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 归属销售
     */
    private String salesName;

    /**
     * 业务单号的ID
     */
    private Integer bizId;
} 