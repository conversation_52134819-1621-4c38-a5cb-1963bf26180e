<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<div class="title-container">
    <div class="table-title nobor">
        用户满意度回访记录
    </div>
    <c:if test="${afterSalesVo.validStatus eq 1}">
        <div class="title-click nobor  pop-new-data" layerParams='{"width":"1000px","height":"800px","title":"新增回访记录",
                   "link":"<%= basePath %>/order/aftersalesUpgrade/addReturnVisitRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增回访记录</div>
    </c:if>
</div>
<div class="parts">
    <table class="table">
        <thead>
        <tr>
            <th>回访客户姓名</th>
            <th>回访客户性质</th>
            <th>回访客户号码</th>
            <th>回访状态</th>
            <th>回访时间</th>
            <th>回访部门</th>
            <th>回访人</th>
            <th>录音ID</th>
            <th>最终分值</th>
            <th>回访结果</th>
            <th>备注</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${returnVisitRecordList}" var="record" varStatus="status">
            <tr>
                <td>${record.customerName}</td>
                <td>
                    <c:if test="${record.customerNature eq 1}">终端</c:if>
                    <c:if test="${record.customerNature eq 2}">经销商</c:if>
                </td>
                <td>${record.customerMobile}</td>
                <td>
                    <c:if test="${record.status eq 1}">已回访</c:if>
                    <c:if test="${record.status eq 2}">不知情</c:if>
                    <c:if test="${record.status eq 3}">拒绝回答</c:if>
                    <c:if test="${record.status eq 4}">拒接</c:if>
                </td>
                <td><date:date value="${record.addTime}" format="yyyy-MM-dd HH:mm:ss" /></td>
                <td>
                    <c:if test="${record.returnVisitDepartment eq 1}">医修帮</c:if>
                    <c:if test="${record.returnVisitDepartment eq 2}">售后</c:if>
                    <c:if test="${record.returnVisitDepartment eq 3}">商城</c:if>
                </td>
                <td>${record.creatorName}</td>
                <td>${record.soundRecordId}</td>
                <td>
                    <div class="customername pos_rel">
                              <span style="float:none;">${record.totalScore}</span>
                        <i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            服务响应分值：${record.serviceResponseScore}<br>
                            服务态度分值：${record.serviceAttitudeScore}<br>
                            服务能力分值：${record.serviceCapabilityScore}<br>
                            是否有投诉：<c:if test="${record.isComplaint eq 1}">有</c:if><c:if test="${record.isComplaint eq 0}">无</c:if><br>
                            是否有推荐：<c:if test="${record.isRecommend eq 1}">有</c:if><c:if test="${record.isRecommend eq 0}">无</c:if>
                        </div>
                    </div>
                </td>
                <td>
                    <c:if test="${record.totalScore >= 80}">满意</c:if>
                    <c:if test="${record.totalScore < 80}">不满意</c:if>
                </td>
                <td>${record.comments}</td>
            </tr>
        </c:forEach>
        <c:if test="${empty returnVisitRecordList}">
            <tr>
                <td colspan='11'>暂无记录！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>




