package com.wms.dto;

import lombok.*;

import java.util.Date;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/12/11 9:05
 **/

/**
    * wms入库
    */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WmsInOrderDto {
    /**
    * id
    */
    private Integer id;

    /**
    * wmsNo
    */
    private String wmsNo;

    /**
    * erpNo
    */
    private String erpNo;

    /**
    * 收货员
    */
    private String consignee;

    /**
    * 验收员
    */
    private String inspector;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 创建者id
    */
    private Integer creator;

    /**
    * 修改者id
    */
    private Integer updater;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者名称
    */
    private String updaterName;
}