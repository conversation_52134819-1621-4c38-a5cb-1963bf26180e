package com.vedeng.todolist.service.impl;

import com.vedeng.common.util.DateUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.RiskCheckLogMapper;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.model.RiskCheckLog;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.RiskCheckLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2020/12/11 10 41
 * @Description:
 */
@Service
public class RiskCheckSkuData implements ITodoInstance {

    private static final Logger logger = LoggerFactory.getLogger(RiskCheckSkuData.class);

    @Resource
    private TodoListMapper todoListMapper;

    @Resource
    private RiskCheckLogMapper riskCheckLogMapper;

    @Resource
    private RiskCheckLogService riskCheckLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void add(Integer buzId, String buzExtra, String comment, String buzProperty) {
        logger.info("待办事项---风控校验订单SKU，buzId:{}，buzExtra：{}生成待办事项",buzId,buzExtra);
        riskCheckLogService.sendMessage2SaleWhenHcOrderRiskCheck(buzExtra);
        if (todoListMapper.selectUnHandledByBuzTypeAndBuzIdAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(), buzId, buzExtra).size() > 0) {
            todoListMapper.deleteByBuzTypeAndBuzIdAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(), buzId,buzExtra);
            riskCheckLogMapper.deleteByBuzTypeAndBuzIdAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(), buzId,buzExtra);
        }
        TodoList instance = new TodoList(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA,buzId,buzExtra,buzProperty,comment, DateUtil.sysTimeMillis(),2);
        todoListMapper.insertSelective(instance);
        //同步添加风控记录
        RiskCheckLog riskCheckLog = new RiskCheckLog(instance.getId(),TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(),buzId,buzExtra,
                buzProperty,
                DateUtil.sysTimeMillis());
        riskCheckLogMapper.insertSelective(riskCheckLog);
    }

    @Override
    public void finish(Integer buzId) {
        todoListMapper.updateStatusByBuzTypeAndBuzId(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(),buzId,1,getCurrentRequestUser().getUserId(),
                System.currentTimeMillis());
        logger.info("待办事项---风控校验订单SKU，buzId;{}更新为已完成",buzId);
    }
}
