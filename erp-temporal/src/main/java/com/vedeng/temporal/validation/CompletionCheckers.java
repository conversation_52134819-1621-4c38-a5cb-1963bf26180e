package com.vedeng.temporal.validation;

import cn.hutool.core.util.StrUtil;
import com.vedeng.temporal.domain.dto.PollingRequest;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 业务完成检查器工具类
 * 
 * 提供常用的预定义检查器，简化业务完成条件的构建
 * 支持字段访问、数据类型转换、复杂条件组合等功能
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-22
 */
@Slf4j
public class CompletionCheckers {
    
    private static final DateTimeFormatter[] DATE_FORMATTERS = {
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ISO_LOCAL_DATE_TIME,
        DateTimeFormatter.ISO_LOCAL_DATE
    };
    
    // ========== 基础字段检查器 ==========
    
    /**
     * 字段非空检查器
     */
    public static BusinessCompletionChecker fieldNotBlank(String fieldPath) {
        return (response, request) -> {
            Object value = getFieldValue(response, fieldPath);
            return value instanceof String && StrUtil.isNotBlank((String) value);
        };
    }
    
    /**
     * 字段非null检查器
     */
    public static BusinessCompletionChecker fieldNotNull(String fieldPath) {
        return (response, request) -> getFieldValue(response, fieldPath) != null;
    }
    
    /**
     * 字段为null检查器
     */
    public static BusinessCompletionChecker fieldIsNull(String fieldPath) {
        return (response, request) -> getFieldValue(response, fieldPath) == null;
    }
    
    /**
     * 字段值相等检查器
     */
    public static BusinessCompletionChecker fieldEquals(String fieldPath, Object expectedValue) {
        return (response, request) -> {
            Object actualValue = getFieldValue(response, fieldPath);
            return Objects.equals(actualValue, expectedValue) ||
                   (actualValue != null && Objects.equals(actualValue.toString(), expectedValue.toString()));
        };
    }
    
    /**
     * 字段值不等检查器
     */
    public static BusinessCompletionChecker fieldNotEquals(String fieldPath, Object expectedValue) {
        return fieldEquals(fieldPath, expectedValue).negate();
    }
    
    /**
     * 字段值包含检查器（字符串包含）
     */
    public static BusinessCompletionChecker fieldContains(String fieldPath, String substring) {
        return (response, request) -> {
            Object value = getFieldValue(response, fieldPath);
            return value instanceof String && ((String) value).contains(substring);
        };
    }
    
    /**
     * 字段值以指定内容开始
     */
    public static BusinessCompletionChecker fieldStartsWith(String fieldPath, String prefix) {
        return (response, request) -> {
            Object value = getFieldValue(response, fieldPath);
            return value instanceof String && ((String) value).startsWith(prefix);
        };
    }
    
    /**
     * 字段值以指定内容结尾
     */
    public static BusinessCompletionChecker fieldEndsWith(String fieldPath, String suffix) {
        return (response, request) -> {
            Object value = getFieldValue(response, fieldPath);
            return value instanceof String && ((String) value).endsWith(suffix);
        };
    }
    
    /**
     * 字段值正则匹配检查器
     */
    public static BusinessCompletionChecker fieldMatches(String fieldPath, String regex) {
        Pattern pattern = Pattern.compile(regex);
        return (response, request) -> {
            Object value = getFieldValue(response, fieldPath);
            return value instanceof String && pattern.matcher((String) value).matches();
        };
    }
    
    /**
     * 字段值在指定集合中
     */
    public static BusinessCompletionChecker fieldIn(String fieldPath, Object... values) {
        return (response, request) -> {
            Object actualValue = getFieldValue(response, fieldPath);
            if (actualValue == null) return false;
            
            for (Object expectedValue : values) {
                if (Objects.equals(actualValue, expectedValue) ||
                    Objects.equals(actualValue.toString(), expectedValue.toString())) {
                    return true;
                }
            }
            return false;
        };
    }
    
    /**
     * 字段值不在指定集合中
     */
    public static BusinessCompletionChecker fieldNotIn(String fieldPath, Object... values) {
        return fieldIn(fieldPath, values).negate();
    }
    
    // ========== 数值比较检查器 ==========
    
    /**
     * 数值大于检查器
     */
    public static BusinessCompletionChecker fieldGreaterThan(String fieldPath, Number expectedValue) {
        return (response, request) -> {
            Object actualValue = getFieldValue(response, fieldPath);
            return compareNumbers(actualValue, expectedValue) > 0;
        };
    }
    
    /**
     * 数值大于等于检查器
     */
    public static BusinessCompletionChecker fieldGreaterThanOrEqual(String fieldPath, Number expectedValue) {
        return (response, request) -> {
            Object actualValue = getFieldValue(response, fieldPath);
            return compareNumbers(actualValue, expectedValue) >= 0;
        };
    }
    
    /**
     * 数值小于检查器
     */
    public static BusinessCompletionChecker fieldLessThan(String fieldPath, Number expectedValue) {
        return (response, request) -> {
            Object actualValue = getFieldValue(response, fieldPath);
            return compareNumbers(actualValue, expectedValue) < 0;
        };
    }
    
    /**
     * 数值小于等于检查器
     */
    public static BusinessCompletionChecker fieldLessThanOrEqual(String fieldPath, Number expectedValue) {
        return (response, request) -> {
            Object actualValue = getFieldValue(response, fieldPath);
            return compareNumbers(actualValue, expectedValue) <= 0;
        };
    }
    
    /**
     * 数值在范围内检查器
     */
    public static BusinessCompletionChecker fieldInRange(String fieldPath, Number minValue, Number maxValue) {
        return fieldGreaterThanOrEqual(fieldPath, minValue).and(fieldLessThanOrEqual(fieldPath, maxValue));
    }
    
    // ========== 集合检查器 ==========
    
    /**
     * 集合大小检查器
     */
    public static BusinessCompletionChecker collectionSize(String fieldPath, int expectedSize) {
        return (response, request) -> {
            Object value = getFieldValue(response, fieldPath);
            if (value instanceof List) {
                return ((List<?>) value).size() == expectedSize;
            } else if (value instanceof Object[]) {
                return ((Object[]) value).length == expectedSize;
            }
            return false;
        };
    }
    
    /**
     * 集合非空检查器
     */
    public static BusinessCompletionChecker collectionNotEmpty(String fieldPath) {
        return (response, request) -> {
            Object value = getFieldValue(response, fieldPath);
            if (value instanceof List) {
                return !((List<?>) value).isEmpty();
            } else if (value instanceof Object[]) {
                return ((Object[]) value).length > 0;
            }
            return false;
        };
    }
    
    /**
     * 集合包含指定值检查器
     */
    public static BusinessCompletionChecker collectionContains(String fieldPath, Object expectedValue) {
        return (response, request) -> {
            Object value = getFieldValue(response, fieldPath);
            if (value instanceof List) {
                return ((List<?>) value).contains(expectedValue);
            } else if (value instanceof Object[]) {
                Object[] array = (Object[]) value;
                for (Object item : array) {
                    if (Objects.equals(item, expectedValue)) {
                        return true;
                    }
                }
            }
            return false;
        };
    }
    
    // ========== 日期时间检查器 ==========
    
    /**
     * 日期在指定日期之后
     */
    public static BusinessCompletionChecker dateAfter(String fieldPath, LocalDate compareDate) {
        return (response, request) -> {
            LocalDateTime actualDate = parseDateTime(getFieldValue(response, fieldPath));
            return actualDate != null && actualDate.toLocalDate().isAfter(compareDate);
        };
    }
    
    /**
     * 日期在指定日期之前
     */
    public static BusinessCompletionChecker dateBefore(String fieldPath, LocalDate compareDate) {
        return (response, request) -> {
            LocalDateTime actualDate = parseDateTime(getFieldValue(response, fieldPath));
            return actualDate != null && actualDate.toLocalDate().isBefore(compareDate);
        };
    }
    
    /**
     * 日期在指定范围内
     */
    public static BusinessCompletionChecker dateInRange(String fieldPath, LocalDate startDate, LocalDate endDate) {
        return dateAfter(fieldPath, startDate.minusDays(1)).and(dateBefore(fieldPath, endDate.plusDays(1)));
    }
    
    /**
     * 日期是今天
     */
    public static BusinessCompletionChecker dateIsToday(String fieldPath) {
        LocalDate today = LocalDate.now();
        return (response, request) -> {
            LocalDateTime actualDate = parseDateTime(getFieldValue(response, fieldPath));
            return actualDate != null && actualDate.toLocalDate().equals(today);
        };
    }
    
    // ========== 组合检查器 ==========
    
    /**
     * 所有条件都必须满足（AND 组合）
     */
    public static BusinessCompletionChecker allOf(BusinessCompletionChecker... checkers) {
        return (response, request) -> {
            for (BusinessCompletionChecker checker : checkers) {
                if (!checker.isCompleted(response, request)) {
                    return false;
                }
            }
            return true;
        };
    }
    
    /**
     * 任一条件满足即可（OR 组合）
     */
    public static BusinessCompletionChecker anyOf(BusinessCompletionChecker... checkers) {
        return (response, request) -> {
            for (BusinessCompletionChecker checker : checkers) {
                if (checker.isCompleted(response, request)) {
                    return true;
                }
            }
            return false;
        };
    }
    
    /**
     * 取反检查器
     */
    public static BusinessCompletionChecker not(BusinessCompletionChecker checker) {
        return checker.negate();
    }
    
    /**
     * 条件检查器（if-then-else）
     */
    public static BusinessCompletionChecker condition(BusinessCompletionChecker condition, 
                                                     BusinessCompletionChecker thenChecker, 
                                                     BusinessCompletionChecker elseChecker) {
        return (response, request) -> {
            if (condition.isCompleted(response, request)) {
                return thenChecker.isCompleted(response, request);
            } else {
                return elseChecker != null ? elseChecker.isCompleted(response, request) : true;
            }
        };
    }
    
    // ========== 上下文相关检查器 ==========
    
    /**
     * 基于请求上下文的检查器
     */
    public static BusinessCompletionChecker contextDependent(ContextAwareChecker checker) {
        return checker::check;
    }
    
    /**
     * 上下文感知检查器接口
     */
    @FunctionalInterface
    public interface ContextAwareChecker {
        boolean check(Map<String, Object> response, PollingRequest request);
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 获取嵌套字段值
     * 支持路径如：user.profile.name
     */
    private static Object getFieldValue(Map<String, Object> data, String fieldPath) {
        if (data == null || fieldPath == null) {
            return null;
        }
        
        String[] parts = fieldPath.split("\\.");
        Object current = data;
        
        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
            } else {
                return null;
            }
        }
        
        return current;
    }
    
    /**
     * 数值比较
     */
    private static int compareNumbers(Object actual, Number expected) {
        if (actual == null) {
            throw new IllegalArgumentException("实际值为null，无法进行数值比较");
        }
        
        try {
            double actualDouble = Double.parseDouble(actual.toString());
            double expectedDouble = expected.doubleValue();
            return Double.compare(actualDouble, expectedDouble);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无法转换为数值: " + actual, e);
        }
    }
    
    /**
     * 解析日期时间
     */
    private static LocalDateTime parseDateTime(Object value) {
        if (value == null) return null;
        
        String dateString = value.toString().trim();
        if (dateString.isEmpty()) return null;
        
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return LocalDateTime.parse(dateString, formatter);
            } catch (DateTimeParseException ignored) {
                // 尝试下一个格式
            }
        }
        
        // 尝试解析为LocalDate然后转换为LocalDateTime
        try {
            LocalDate date = LocalDate.parse(dateString);
            return date.atStartOfDay();
        } catch (DateTimeParseException e) {
            log.warn("无法解析日期时间: {}", dateString);
            return null;
        }
    }
}