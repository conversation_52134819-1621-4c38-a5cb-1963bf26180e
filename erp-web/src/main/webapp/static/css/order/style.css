.t-line-wrap {
  display: flex;
  padding: 10px 0;
}
.t-line-wrap .t-line-item {
  flex: 1;
  display: flex;
  position: relative;
  font-size: 14px;
  align-items: center;
  color: #999;
  padding: 0 5px;
}
.t-line-wrap .t-line-item .t-line-item-icon {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  text-align: center;
  line-height: 22px;
  margin-right: 5px;
  border: 1px solid #aaa;
  color: #aaa;
}
.t-line-wrap .t-line-item .t-line-item-txt {
  font-weight: bold;
  background: #fff;
  position: relative;
  z-index: 11;
  padding-right: 10px;
}
.t-line-wrap .t-line-item .t-line-lock {
  display: flex;
  align-items: center;
  position: absolute;
  left: 85px;
  top: -10px;
  white-space: nowrap;
  z-index: 1;
  color: #e64545;
  font-size: 12px;
}
.t-line-wrap .t-line-item .t-line-item-tip {
  color: #999;
  font-size: 12px;
  position: absolute;
  margin-top: 5px;
}
.t-line-wrap.red-line .t-line-item::before {
  background: #e64545 !important;
}
.t-line-wrap .t-line-item::before {
  content: '';
  position: absolute;
  width: calc(100% - 90px);
  height: 2px;
  background: #ddd;
  right: 5px;
}
.t-line-wrap .t-line-item.t-line-done .t-line-item-icon {
  background: #aaa;
  color: #fff;
}
.t-line-wrap .t-line-item.t-line-doing {
  color: #333;
}
.t-line-wrap .t-line-item.t-line-doing .t-line-item-icon {
  background: #09f;
  border-color: #09f;
  color: #fff;
}
.t-line-wrap .t-line-item.t-line-async .t-line-item-icon {
  border-color: #09f;
  color: #09f;
}
.t-line-wrap .t-line-item:last-child::before {
  display: none;
}
i.lock-icon {
  width: 20px;
  height: 20px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGOSURBVEhL5ZUxS8NAGIad/AXODg4uddBJUYQOdZAiboKIiOhgRapU1CKCUBwEoZMg4qY46Z9wcnFxkVJqG5qapNaigkVN1Ve+6zlor3cpnkXxgeO79+5LHgiXpAkNxrPwzXVxd7SP/HoE2ZEAbrY38Xh+xne940lYtnPIjg0i6WuBOR5EIR5jlbKzOofX0gPvVKMUurksEq3NyMeWULZMvlqBMq3T/nMqwVflKIXGcD9uD/d4EkP7aX8HT3KkQnp01sIkT3LsxWnWr0IqvAqN4v74gCc51Ef9KqTCVHcbXNPgSQ71Ub8KqZAOQz146f/HwuJunN2AvipeB/WrTqpQmPb74ERn2cX1DntlBnZkit+pmiph6fSk7kf5Fdn1f0NIPTK0CjMDXeyAXPa146WQ56uf0Sa83liGFZ5gc6qURWgTOmthGMEeNqdKWYQ24cfeU/KCVcoitAkJY6gXmUAnq7XQKiToBZehXajidwuJ73xLnWgI1nzl1REhFBLFna2qv4GXQVIZNYU/RYOFwDtchWKBKaNvLgAAAABJRU5ErkJggg==);
  background-size: 100% 100%;
  display: inline-block;
}

.no-wrap {
  white-space: nowrap;
  max-width: 150px;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
}
