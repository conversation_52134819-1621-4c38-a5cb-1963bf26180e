.crm-m-list-layout {
    .crm-m-list-header {
        background: #fff;
        display: flex;
        justify-content: space-between;
        border-bottom: solid 1px #EBEFF2;
        position: fixed;
        max-width: 768px;
        width: 100%;
        top: 0;
        z-index: 11;

        .crm-m-list-tab {
            position: relative;
            padding: 10px 0 10px 10px;

            .crm-m-list-tab-txt {
                font-size: 14px;
                display: flex;
                align-items: center;

                .icon-down {
                    font-size: 16px;
                    margin-left: 5px;
                    color: #666;
                    transition: transform .22s ease;
                }

                &.show {
                    color: #09f;

                    .icon-down {
                        color: #666;
                        transform: rotate(180deg);
                    }
                }
            }
        }

        .crm-m-list-header-options {
            display: flex;
            align-items: center;

            .header-option-item {
                font-size: 20px;
                padding: 10px 0;
                line-height: 1;
                color: #666;
                position: relative;

                &.option-sort {
                    padding-left: 10px;
                    padding-right: 6px;

                    &.show {
                        color: #09f;
                    }
                }

                &.option-filter {
                    padding-left: 6px;
                    padding-right: 10px;
                }

                .header-option-filter-num {
                    position: absolute;
                    padding: 0 5px;
                    height: 16px;
                    border-radius: 8px;
                    background: #b3e1ff;
                    color: #09f;
                    text-align: center;
                    line-height: 16px;
                    font-size: 12px;
                    top: 2px;
                    right: 4px;

                    .num-txt {
                        transform: scale(.83);
                    }
                }
            }
        }
    }

    .crm-m-tab-wrap {
        padding-top: 44px;
        padding-bottom: 5px;
    }

    .crm-m-tab-item {
        padding-left: 10px;
        height: 41px;
        font-size: 14px;
        border-bottom: solid 1px #F5F7FA;
        display: flex;
        align-items: center;

        &:last-child {
            border-bottom: 0;
        }

        .crm-m-tab-item-txt {
            flex: 1;
        }

        .vd-ui_icon {
            width: 36px;
            height: 41px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }

        &.active {
            .crm-m-tab-item-txt {
                color: #09f;
            }
        }
    }

    .crm-m-sort-wrap {
        padding-top: 44px;
        padding-bottom: 5px;
    }

    .crm-m-sort-item {
        padding: 10px;
        border-bottom: solid 1px #F5F7FA;
        font-size: 14px;

        &:last-child {
            border-bottom: 0;
        }

        &.active {
            color: #09f;
        }
    }

    .crm-m-list-cnt {
        padding-top: 42px;

        .crm-m-list-inner {
            padding: 10px;
        }

        .crm-m-list-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding-bottom: 10px;
            color: #09f;

            @keyframes loading {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            .icon-loading {
                font-size: 16px;
                animation: loading 2s linear infinite;
                margin-right: 5px;
            }
        }

        .crm-m-list-empty {
            padding-top: 100px;
            text-align: center;

            .crm-m-list-empty-pic {
                width: 223px;
                height: 120px;
                display: inline-block;
                margin-bottom: 15px;

                img {
                    max-width: 100%;
                    max-height: 100%;
                }
            }

            .crm-m-list-empty-txt {
                color: #999;
            }
        }
    }

    .crm-list-add {
        width: 50px;
        height: 50px;
        border-radius: 25px;
        background: #09f;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        position: fixed;
        z-index: 5;
        bottom: 50px;
        right: 10px;
        box-shadow: 0 3px 5px rgba(0, 153, 255, .2);
    }
}

.crm-list-filter-wrap {
    .crm-list-filter-cnt {
        padding: 10px;
        background: #F5F7FA;
        max-height: calc(100vh - 147px);
        max-height: calc(100vh - 147px - constant(safe-area-inset-bottom));
        max-height: calc(100vh - 147px - env(safe-area-inset-bottom));
        overflow: auto;

        .crm-m-search-item {
            background: #fff;
            border-radius: 5px;
            display: flex;
            margin-bottom: 5px;
            overflow: hidden;

            &:last-child {
                margin-bottom: 0;
            }
            
            .crm-m-search-item-title {
                padding: 10px;
                width: 100px;
            }
            
            .crm-m-search-item-cnt {
                flex: 1;
            }

            &.vertical {
                display: block;

                .crm-m-search-item-title {
                    padding-bottom: 5px;
                }

                .vd-ui-select {
                    padding-top: 5px;

                    .vd-ui-select-trigger.multi-select {
                        padding-top: 0;

                        .vd-ui-icon-trigger {
                            margin-right: -10px;
                        }
                    }
                }
            }

            .vd-ui-checkbox-group, .vd-ui-radio-group {
                padding: 5px 10px 10px 10px;
            }

            &.active {
                background: #E0F3FF;
            }
        }
    }
    
    .crm-list-filter-footer {
        display: flex;
        align-items: center;
        border-top: solid 1px #EBEFF2;

        .filter-footer-option {
            flex: 1.5;
            text-align: center;

            .option-icon {
                width: 20px;
                height: 20px;
                background-size: 100% 100%;
                display: inline-block;
                vertical-align: top;

                &.option-add {
                    background-image: url(/mstatic/mui/image/filter-icon-add.svg);
                }

                &.option-reset {
                    background-image: url(/mstatic/mui/image/filter-icon-reset.svg);
                }
            }

            &.disabled {
                color: #ccc;

                .option-icon {
                    &.option-add {
                        background-image: url(/mstatic/mui/image/filter-icon-add-disabled.svg);
                    }
                }
            }
        }

        .filter-footer-btns {
            display: flex;
            align-items: center;
            flex: 7;
            padding: 10px 0;

            .btn-cancel-flex {
                flex: 3;
                padding-right: 10px;
            }

            .btn-confirm-flex {
                flex: 4;
                padding-right: 10px;
            }
        }
    }
}

.crm-list-filter-setting {
    background: #F5F7FA;

    .filter-setting-list {
        padding: 10px;
        max-height: calc(100vh - 147px);
        max-height: calc(100vh - 147px - constant(safe-area-inset-bottom));
        max-height: calc(100vh - 147px - env(safe-area-inset-bottom));
        overflow: auto;

        .filter-setting-item {
            margin-top: 5px;
            border-radius: 5px;
            background: #fff;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &:first-child {
                margin-top: 0;
            }

            &.active {
                background: #E0F3FF;
            }

            &.placehodler {
                opacity: .3;
            }
        }
    }
    
    .filter-setting-footer {
        display: flex;
        align-items: center;
        padding: 10px 0;
        background: #fff;
        border-top: solid 1px #EBEFF2;
        
        .btn-cancel-flex {
            flex: 2;
            padding-left: 10px;
        }

        .btn-confirm-flex {
            flex: 3;
            padding: 0 10px;
        }
    }
}

.crm-filter-add-wrap {
    text-align: left;

    .crm-filter-form-wrap {
        display: flex;
        align-items: center;
        border-bottom: solid 1px #E1E5E8;

        .crm-filter-form-label {
            display: flex;
            align-items: center;
            width: 72px;
            white-space: nowrap;

            .must {
                color: #e64545;
            }
        }
    }

    .crm-filter-form-tip {
        color: #999;
        margin-top: 10px;
    }
}