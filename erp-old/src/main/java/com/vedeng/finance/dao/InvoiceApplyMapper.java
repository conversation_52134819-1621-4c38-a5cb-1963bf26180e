package com.vedeng.finance.dao;

import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.finance.dto.InvoiceApplyReasonSnapshotDto;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.model.InvoiceApplyDetail;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

@Named("invoiceApplyMapper")
public interface InvoiceApplyMapper {

    InvoiceApply selectByPrimaryKey(Integer invoiceApplyId);

    int update(InvoiceApply record);
    /**
    *通过快递id获取开票申请
    * @Author:strange
    * @Date:10:04 2020-01-06
    */
    List<InvoiceApply> getInvoiceApplyByExpressId(Integer expressId);

    /**
     * 快递是否关联都为未通过发票
     * @param expressId
     * @return
     */
    List<InvoiceApply> getInvoiceApplyByExpressIdFaile(Integer expressId);
    //查询所有开票申请
    List<InvoiceApply> getAllSaleInvoiceApplyList();
    //根据订单id查找开票记录
    List<Integer> getInvoiceApplyIdsBySaleOrderIds(@Param("saleOrderNoList")List saleOrderNoList);
    //改变标记状态
    int changeIsSign(@Param("endInvoiceApplyIds")List<Integer> endInvoiceApplyIds);

    /**
     * 添加开票失败原因
     *
     * @param invoiceApply
     * @return
     */
    int updateInvoiceApplyReasons(InvoiceApply invoiceApply);

    /**
     * 检索所有票货同行未完成开票的申请
     *
     * @return
     */
    List<InvoiceApply> getInvoiceAppliesByGoodsPeer();

    /**
     * 根据发票ID检索发票申请相关信息
     *
     * @param invoiceId
     * @return
     */
    InvoiceApply getInvoiceApplyInfoByInvoiceId(Integer invoiceId);

    /**
     * 标记发票申请信息已下传至WMS
     *
     * @param invoiceApplyId
     * @return
     */
    int signIsSendWmsFlag(Integer invoiceApplyId);

    /**
     * 根据订单ID活动商品开票申请记录
     *
     * @param saleorderId
     * @return
     */
    List<InvoiceApplyDetail> getInvoiceAppliesBySaleorderId(Integer saleorderId);

    /**
     * 查询售后订单最近一次开票申请记录
     * @param relatedId
     * @return
     */
    List<InvoiceApply> getAftersaleInvoiceApplyByRelatedId(@Param(value="relatedId")Integer relatedId);

    int insert(InvoiceApply invoiceApply);

    /**
     * 根据订单号 修改发票催单状态
     * @param orderId 订单号
     * @param urage  状态
     */
    void updateUrageByOrderNumber(@Param("orderId") Integer orderId, @Param("urage") Integer urage);

    void updateInvoiceProperty(InvoiceApplyDto invoiceApplyDto);

    void insertApplyReasonSnapshot(InvoiceApplyReasonSnapshotDto dto);
}
