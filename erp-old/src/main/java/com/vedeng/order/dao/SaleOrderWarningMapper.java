package com.vedeng.order.dao;

import com.vedeng.authorization.model.User;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.SaleorderGoodsVo;

import java.util.List;

/**
 * @Description:  待采购预警历史记录
 * @Author:       davis
 * @Date:         2021/4/20 下午5:02
 * @Version:      1.0
 */
public interface SaleOrderWarningMapper {

	/**
	 * 查询该条记录是否已经发生过预警
	 * @param saleorderGoodsVo 消费对象
	 * @return 预警记录
	 */
	List<SaleOrderWarning> getHistorySaleOrderWarn(SaleorderGoodsVo saleorderGoodsVo);

    /**
     * 删除预警记录
	 * @param saleorderGoodsVo 消费对象
	 */
	void deleteSaleOrderWarning(SaleorderGoodsVo saleorderGoodsVo);

	/**
	 * 保存预警记录
	 * @param saleOrderWarning
	 */
	void insertSaleOrderWarning(SaleOrderWarning saleOrderWarning);

	/**
	 * 获取订单助手
	 * @param sgv
	 * @return
	 */
	List<User> getOrderAssistant(SaleorderGoodsVo sgv);

	/**
	 * 更新订单预警记录
     * @param saleOrderWarning
     * @return 更新记录
     */
    int updateSaleOrderWarning(SaleOrderWarning saleOrderWarning);
}

