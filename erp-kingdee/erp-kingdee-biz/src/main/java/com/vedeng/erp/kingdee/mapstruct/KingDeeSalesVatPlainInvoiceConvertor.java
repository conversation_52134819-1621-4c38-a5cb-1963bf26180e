package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDetail;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.mapstruct
 * @Date 2023/1/9 16:00
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface KingDeeSalesVatPlainInvoiceConvertor extends BaseMapStruct<KingDeeSalesVatPlainInvoiceEntity, KingDeeSalesVatPlainInvoiceDto> {

    /**
     * dto转entity
     *
     * @param dto KingDeeSalesVatPlainInvoiceDto
     * @return KingDeeSalesVatPlainInvoiceEntity
     */
    @Override
    @Mapping(target = "fsalesicentry", source = "fsalesicentry", qualifiedByName = "fSalesEntryListToJsonArray")
    @Mapping(target = "FBillTypeId", source = "FBillTypeID")
    KingDeeSalesVatPlainInvoiceEntity toEntity(KingDeeSalesVatPlainInvoiceDto dto);

    /**
     * entity转dto
     *
     * @param entity KingDeeSalesVatPlainInvoiceEntity
     * @return KingDeeSalesVatPlainInvoiceDto
     */
    @Override
    @Mapping(target = "fsalesicentry", source = "fsalesicentry", qualifiedByName = "fSalesEntryJsonArrayToList")
    KingDeeSalesVatPlainInvoiceDto toDto(KingDeeSalesVatPlainInvoiceEntity entity);

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("fSalesEntryListToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeeSalesVatPlainInvoiceDetail> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }

    /**
     * entity 中JSONArray 转 原对象
     *
     * @param jsonArray JSONArray
     * @return List<KingDeeSalesVatPlainInvoiceDetail>
     */
    @Named("fSalesEntryJsonArrayToList")
    default List<KingDeeSalesVatPlainInvoiceDetail> entryJsonArrayToList(JSONArray jsonArray) {
        if (CollUtil.isEmpty(jsonArray)) {
            return Collections.emptyList();
        }
        return jsonArray.toJavaList(KingDeeSalesVatPlainInvoiceDetail.class);
    }

}
