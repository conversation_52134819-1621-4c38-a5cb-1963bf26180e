<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app">
    <div>
        <el-descriptions title="通话信息" column = "2">
            <el-descriptions-item label="客户名称">{{ customerName }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ contactName }} {{ contactPhone }}</el-descriptions-item>
            <el-descriptions-item label="沟通内容">
                <el-input
                        type="textarea"
                        placeholder="请输入内容"
                        v-model="textarea"
                        maxlength="200"
                >
                </el-input>
            </el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="" column = "1">
            <el-descriptions-item label="关联快递" >{{ tips }}</el-descriptions-item>
        </el-descriptions>
    </div>
    <div>
        <el-table
                ref="table"
                :data="tableData"
                :empty-text="tableEmptyText"
                tooltip-effect="dark"
                style="width: 100%"
                @selection-change="handleSelectionChange">
            <el-table-column
                    type="selection"
                    align="center"
                    width="55">
            </el-table-column>
            <el-table-column
                    prop="logisticsName"
                    label="快递公司"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="logisticsNo"
                    label="快递单号"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="deliveryTime"
                    label="发货时间"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="goodsName"
                    label="商品"
                    width="240">
            </el-table-column>
            <el-table-column
                    prop="arriveStatus"
                    label="快递状态"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="communicateRecordIds"
                    label="录音ID"
                    width="120">
            </el-table-column>
        </el-table>

    </div>
    <div style="text-align: center;margin-top: 10px">
        <el-button type="primary" @click="onSubmit" :loading="loading">确定</el-button>
        <el-button @click="onCancel">取消</el-button>
    </div>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const saleOrderId = ${saleOrderId};
    const coid = '${coid}';
    const phone = '${phone}';
    const communicateRecorderId = ${communicateRecorderId};
    const endTime = ${endTime};
new Vue({
    el: '#app',
    data() {
       return {
           saleOrderId: saleOrderId,
           traderId: 0,
           traderContactId: 0,
           customerName: "",
           contactName: "",
           contactPhone: phone,
           textarea: "确认物流信息",
           tips: "勾选快递提交后，快递自动签收",
           tableEmptyText: "暂无数据",
           tableData: [],
           multipleSelection: [],
           communicateRecorderId: communicateRecorderId,
           loading: false
       }
    },
    created() {
        this.initData();
    },
    methods:{
        toggleSelection(rows) {
            if (rows) {
                rows.forEach(row => {
                    this.$refs.multipleTable.toggleRowSelection(row);
                });
            } else {
                this.$refs.multipleTable.clearSelection();
            }
        },
        handleSelectionChange(val) {
            // this.multipleSelection = val;
        },
        onSubmit(){
            console.log(this.textarea)
            var express = this.$refs.table.selection;
            let data = [];
            if (express.length != 0){
                data = this.$refs.table.selection.map((item) => {
                    return {
                        expressId: item.expressId,
                        communicateRecorderIds: this.communicateRecorderId,
                        contents: this.textarea,
                        coid: coid,
                        endTime: endTime,
                        contactName: this.contactName,
                        traderId: this.traderId,
                        traderContactId: this.traderContactId

                    }
                });
            }
            if (express.length == 0){
                let text = {
                    communicateRecorderIds: this.communicateRecorderId,
                    contents: this.textarea,
                    coid: coid,
                    endTime: endTime,
                    contactName: this.contactName,
                    traderId: this.traderId,
                    traderContactId: this.traderContactId
                }
                data.push(text)
            }

            console.log(data);
            bindCommunicationIdAndSignFor(data).then(res => {
                console.log(res);
                if(res.data.code == 0){
                    this.loading = true;
                    this.$message.success("提交成功");
                    window.parent.layer.closeAll();
                }else{
                    this.$message.error(res.data.message);
                }
            });
        },
        onCancel(){
            window.parent.layer.closeAll();
        },
        async initData(){

            await querySaleDirect(this.saleOrderId).then(res =>{

                if(res.data.code == 0){
                    this.tableData = res.data.data;
                }else{
                    this.$message.error(res.data.message);
                }
            });

            await querySaleInfo(this.saleOrderId).then(res =>{
                if(res.data.code == 0){
                    debugger
                    let saleOrder = res.data.data;
                    this.customerName=saleOrder.traderName;
                    this.traderId = saleOrder.traderId;
                    this.contactPhone = phone;
                    if (phone == saleOrder.traderContactMobile || phone == saleOrder.traderContactTelephone){
                        this.contactName =saleOrder.traderContactName;
                        this.traderContactId = saleOrder.traderContactId;
                    }
                    if (phone == saleOrder.takeTraderContactMobile || phone == saleOrder.takeTraderContactTelephone) {
                        this.contactName = saleOrder.takeTraderContactName;
                        this.traderContactId = saleOrder.takeTraderContactId;
                    }

                    if (phone == saleOrder.invoiceTraderContactMobile || phone == saleOrder.invoiceTraderContactTelephone) {
                        this.contactName = saleOrder.invoiceTraderContactName;
                        this.traderContactId = saleOrder.invoiceTraderContactId;
                    }
                }else{
                    this.$message.error(res.data.message);
                }
            });
        }
    },
});

</script>