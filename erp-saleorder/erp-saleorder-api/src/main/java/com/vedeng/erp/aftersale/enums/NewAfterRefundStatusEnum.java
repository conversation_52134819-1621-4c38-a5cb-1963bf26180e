package com.vedeng.erp.aftersale.enums;

/**
 * 售后单-退款状态
 * 退款状态 0无退款状态 1未退款 2部分退款 3全部退款 (用于筛选)
 */
public enum NewAfterRefundStatusEnum {
    
    CONFIRM(0,"无退款"),
    PROCESSING(1,"未退款"),
    COMPLETED(2,"部分退款"),
    CLOSED(3,"全部退款"),
    ;

    private Integer code;

    private String desc;


    NewAfterRefundStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    public static String getDescByCode(Integer code) {
        for (NewAfterRefundStatusEnum e : NewAfterRefundStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();

            }
        }
        return "";
    }
    
}
