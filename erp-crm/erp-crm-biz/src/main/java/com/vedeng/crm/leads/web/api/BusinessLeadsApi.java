package com.vedeng.crm.leads.web.api;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.shard.service.ShareService;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.crm.task.service.impl.TaskServiceImpl;
import com.vedeng.crm.visitrecord.api.CrmVisitApiService;
import com.vedeng.erp.business.common.enums.BusinessLeadsFollowStatusEnums;
import com.vedeng.erp.business.domain.dto.AssignLeadsDto;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadMergeDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.common.dto.*;
import com.vedeng.erp.system.common.annotation.CustomDataOperAnnotation;
import com.vedeng.erp.system.common.enums.CustomDataOperBizTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperTypeEnums;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.CommunicationCascaderApiService;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务数据操作api
 * @date 2022/7/9 14:46
 */
@ExceptionController
@RestController
@RequestMapping("/crm/businessLeads/profile")
@Slf4j
public class BusinessLeadsApi {

    @Autowired
    private CommunicationCascaderApiService communicationCascaderApiService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private BusinessLeadsService businessLeadsService;
    @Autowired
    private CrmVisitApiService crmVisitApiService;
    @Autowired
    private BusinessChanceService businessChanceService;
    @Autowired
    private TaskService taskService;


    /**
     *线索列表分页查询
     */
    @MenuDesc(menuValue = "C01,C02",menuDesc = "线索列表分页查询")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public R<PageInfo<BusinessLeadsDto>> page(@RequestBody PageParam<BusinessLeadsDto> businessLeadsDto) {
        return R.success(businessLeadsService.page(businessLeadsDto));
    }

    @MenuDesc(menuValue = "C01,C02",menuDesc = "查看线索列表对应的创建人")
    @RequestMapping(value = "/findAllCreateUser")
    public R<List<UserDto>> findAllCreateUser(@RequestParam(value = "name", required = false) String name) {
        return R.success(businessLeadsService.findAllCreateUser(name));
    }

    @MenuDesc(menuValue = "C01,C02",menuDesc = "查看线索列表对应的所有归属人")
    @RequestMapping(value = "/findAllBelongUser")
    public R<List<UserDto>> findAllBelongUser(@RequestParam(value = "name", required = false) String name) {
        return R.success(businessLeadsService.findAllBelongUser(name));
    }

    @MenuDesc(menuValue = "C01,C02",menuDesc = "查看线索列表对应的所有协作人")
    @RequestMapping(value = "/findAllShareUser")
    public R<List<UserDto>> findAllShareUser(@RequestParam(value = "name" , required = false) String name) {
        return R.success(businessLeadsService.findAllShareUser(name));
    }


    /**
     * 获取组装好的线索渠道
     * @return list
     */
    @MenuDesc(menuValue = "C01,C02",menuDesc = "线索渠道")
    @RequestMapping(value = "/getCascaderChannelOptions")
    public R<?> getCascaderChannelOptions() {
        return R.success(communicationCascaderApiService.getCascaderChannelOptions());
    }


    /**
     *新增线索
     */
    @MenuDesc(menuValue = "C0101",menuDesc = "新增线索")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<?> add(@RequestBody BusinessLeadsDto businessLeadsDto) {
        ValidatorUtils.validate(businessLeadsDto, AddGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        try{
            businessLeadsDto.setCreator(currentUser.getId());
            businessLeadsDto.setCreatorName(currentUser.getUsername());
            businessLeadsDto.setAddTime(new Date());
            businessLeadsDto.setStatus(1);
            return R.success(businessLeadsService.add(businessLeadsDto));
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return R.error("新增线索失败");
        }

    }

    /**
     *新增线索/商机综合页
     */
    @MenuDesc(menuValue = "C0101",menuDesc = "新增线索/商机")
    @RequestMapping(value = "/addForSale", method = RequestMethod.POST)
    public R<?> addForSale(@RequestBody BusinessLeadsDto businessLeadsDto) {
        ValidatorUtils.validate(businessLeadsDto, AddGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        try{
            businessLeadsDto.setCreator(currentUser.getId());
            businessLeadsDto.setCreatorName(currentUser.getUsername());
            businessLeadsDto.setAddTime(new Date());
            businessLeadsDto.setStatus(1);
            return R.success(businessLeadsService.add(businessLeadsDto));
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return R.error("新增线索失败");
        }

    }

    /**
     *根据参数查询对应的线索0
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getLeadsListByDto", method = RequestMethod.POST)
    public R<?> getLeadsListByDto(@RequestBody BusinessLeadsDto businessLeadsDto) {
        if(StringUtils.isNotBlank(businessLeadsDto.getPhone()) && StringUtils.isNotBlank(businessLeadsDto.getTelephone())){
            return R.success("未查询到相关线索，请输入手机号或电话号码");
        }
        List<BusinessLeadsDto> result = businessLeadsService.getLeadsListByDtoToday(businessLeadsDto);
        if(CollectionUtils.isEmpty(result)){
            return R.success("当前联系方式下无线索，可以新建");
        }
        if(StringUtils.isNotBlank(businessLeadsDto.getPhone())){
            return R.error("该手机号今日已存在线索");
        }else{
            return R.error("该电话今日已存在线索");
        }

     }

    /**
     *编辑线索提交
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @MenuDesc(menuValue = "C0102",menuDesc = "编辑线索")
    public R<?> update(@RequestBody BusinessLeadsDto businessLeadsDto) {
        ValidatorUtils.validate(businessLeadsDto, UpdateGroup.class);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        try{
            BusinessLeadsDto oldBusinessLeadsEntity = businessLeadsService.findById(businessLeadsDto.getId());
            if (oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.CLOSE.getType())
                    || oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.OPPORTUNITY.getType())) {
                return R.error("已关闭，已商机的线索无法再次编辑，请核实线索状态。");
            }

            businessLeadsDto.setUpdater(currentUser.getId());
            businessLeadsDto.setUpdaterName(currentUser.getUsername());
            businessLeadsDto.setModTime(new Date());
            businessLeadsService.update(businessLeadsDto);
            return R.success("更新线索成功");
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return R.error("更新线索失败");
        }

    }

    /**
     *关闭线索
     */
    @MenuDesc(menuValue="C0109",menuDesc = "关闭线索")
    @RequestMapping(value = "/closedLeads", method = RequestMethod.POST)
    public R<?> closedLeads(@RequestBody BusinessLeadsDto businessLeadsDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
       try{
           BusinessLeadsDto oldBusinessLeadsEntity = businessLeadsService.findById(businessLeadsDto.getId());
           if (oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.CLOSE.getType())
                   ) {
               return R.error("线索已关闭线索，无法再次操作，请核实线索状态。");
           }
           businessLeadsDto.setUpdater(currentUser.getId());
           businessLeadsDto.setUpdaterName(currentUser.getUsername());
           businessLeadsDto.setModTime(new Date());
           businessLeadsService.closedLeads(businessLeadsDto);
           taskService.handleTaskCompleteForLeadsClose(businessLeadsDto.getId());//自动关闭该线索下的所有未处理的任务
           String closeReasonTypeName = businessLeadsService.getCloseReasonTypeName(businessLeadsDto.getCloseReasonType());
           crmVisitApiService.SendMessageForCheckLeads(oldBusinessLeadsEntity.getLeadsNo(),currentUser.getUsername(),closeReasonTypeName+"-"+businessLeadsDto.getCloseReason());
           return R.success();
       }catch (Exception e){
           log.error(e.getMessage(), e);
           return R.error("关闭线索失败");
       }

    }

    /**
     *更新线索分级
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/updateLeadsStatus", method = RequestMethod.POST)
    public R<?> updateLeadsStatus(@RequestBody BusinessLeadsDto businessLeadsDto) {
        businessLeadsService.updateLeadsStatus(businessLeadsDto);
        return R.success();
    }

    /**
     *线索列表置顶
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/top", method = RequestMethod.POST)
    @CustomDataOperAnnotation(operBizType = CustomDataOperBizTypeEnums.BUSINESS_LEADS,
            dataOperType = CustomDataOperTypeEnums.TOP)
    public R<?> top(@RequestParam Integer id) {
        return R.success();
    }


    /**
     *线索列表取消置顶
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/unTop")
    @CustomDataOperAnnotation(operBizType = CustomDataOperBizTypeEnums.BUSINESS_LEADS,
            dataOperType = CustomDataOperTypeEnums.UN_TOP)
    public R<?> unTop(@RequestParam Integer id) {
        return R.success();
    }


    /**
     * 查询某个业务ID的协作人列表
     * @param businessId
     * @param businessType
     * @return
     */
    @RequestMapping(value = "/queryShardList", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<ShareUserDto> queryShardList(@RequestParam("businessId") Integer businessId,@RequestParam("businessType") Integer businessType) {
        List<RSalesJBusinessOrderDto> list = shareService.getShareListByBusinessId(businessId,businessType);
        Boolean shareBtn = shareService.checkShareBtn(businessId,businessType);
        ShareUserDto shareUserDto = new ShareUserDto();
        shareUserDto.setList(list);
        shareUserDto.setShareBtn(shareBtn);
        return R.success(shareUserDto);
    }

    @RequestMapping(value = "/queryShardUserForBusiness")
    @NoNeedAccessAuthorization
    public R<?> queryShardUserForBusiness(@RequestParam("businessId") Integer businessId,@RequestParam("businessType") Integer businessType,@RequestParam("name") String name) {
        List<UserDto> userList =  userApiService.getAllUserNotDisabled(name);
        if(businessType !=null){
            Integer currentUserId = CurrentUser.getCurrentUser().getId();
            //遍历userId，将userId为currentUserId的去掉
            userList = userList.stream()
                    .filter(user -> !user.getUserId().equals(currentUserId))
                    .collect(Collectors.toList());
        }
        return R.success(userList);
    }

    /**
     * 分享商机/线索给销售人员
     * @param rSalesJBusinessOrderDto 商机对象
     * @return R
     */
    @RequestMapping(value = "/shareBusiness", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> shareBusiness(@RequestBody RSalesJBusinessOrderDto rSalesJBusinessOrderDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        rSalesJBusinessOrderDto.setAddTime(new Date());
        rSalesJBusinessOrderDto.setModTime(new Date());
        rSalesJBusinessOrderDto.setCreator(currentUser.getId());
        rSalesJBusinessOrderDto.setUpdater(currentUser.getId());

        Boolean shareBtn = shareService.checkShareBtn(rSalesJBusinessOrderDto.getBusinessId(),rSalesJBusinessOrderDto.getBusinessType());
        if (!shareBtn){
            return R.error("仅限归属销售及上级、创建人及上级可以操作!");
        }
        shareService.shareBusiness(rSalesJBusinessOrderDto,currentUser);
        return R.success();
    }

    /**
     * 批量分享商机/线索给销售人员
     * @param batchRSalesJBusinessOrderDto 商机对象
     * @return R
     */
    @RequestMapping(value = "/batchShareBusiness", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> batchShareBusiness(@RequestBody BatchRSalesJBusinessOrderDto batchRSalesJBusinessOrderDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        List<String> errorBusinessNo = new ArrayList<>();
        for (BatchRSalesJBusinessOrderItemDto batchRSalesJBusinessOrderItemDto : batchRSalesJBusinessOrderDto.getBusinessOrderItemDtoList()) {
            Boolean shareBtn = shareService.checkShareBtn(batchRSalesJBusinessOrderItemDto.getBusinessId(),batchRSalesJBusinessOrderItemDto.getBusinessType());
            if (!shareBtn){
                errorBusinessNo.add(batchRSalesJBusinessOrderItemDto.getBusinessNo());
                continue;
            }
            RSalesJBusinessOrderDto rSalesJBusinessOrderDto = new RSalesJBusinessOrderDto();
            rSalesJBusinessOrderDto.setAddTime(new Date());
            rSalesJBusinessOrderDto.setModTime(new Date());
            rSalesJBusinessOrderDto.setCreator(currentUser.getId());
            rSalesJBusinessOrderDto.setUpdater(currentUser.getId());

            rSalesJBusinessOrderDto.setBusinessId(batchRSalesJBusinessOrderItemDto.getBusinessId());
            rSalesJBusinessOrderDto.setBusinessNo(batchRSalesJBusinessOrderItemDto.getBusinessNo());
            rSalesJBusinessOrderDto.setBusinessType(batchRSalesJBusinessOrderItemDto.getBusinessType());

            for (BatchSetUserDto batchSetUserDto : batchRSalesJBusinessOrderDto.getSaleUserList()) {
                rSalesJBusinessOrderDto.setSaleUserId(batchSetUserDto.getSaleUserId());
                rSalesJBusinessOrderDto.setSaleUserName(batchSetUserDto.getSaleUserName());
                shareService.shareBusiness(rSalesJBusinessOrderDto,currentUser);
            }
           
        }
        if (CollUtil.isNotEmpty(errorBusinessNo)){
            return R.error("商机："+ String.join("/",errorBusinessNo) +"无权限添加协作人，剩余添加成功");
        }
        return R.success();
    }


    /**
     * 商机取消分享
     * @return R
     */
    @RequestMapping(value = "cancelShare", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> cancelShareBusinessChance(@RequestParam Integer id) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        shareService.cancelShare(id,currentUser);
        return R.success();
    }

    /**
     *分配线索
     */
    @MenuDesc(menuValue = "C0107",menuDesc = "分配线索")
    @RequestMapping(value = "/assign", method = RequestMethod.POST)
    public R<?> assign(@RequestBody AssignLeadsDto assignLeadsDto) {
        ValidatorUtils.validate(assignLeadsDto, AddGroup.class);
        businessLeadsService.assign(assignLeadsDto);
        return R.success();
    }

    @Autowired
    private UserApiService userApiService;

    /**
     *获取单个线索详情
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getOne", method = RequestMethod.POST)
    public R<BusinessLeadsDto> getOne(@RequestParam Integer id) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        BusinessLeadsDto dto = businessLeadsService.getOne(id);
        if(dto !=null && !businessLeadsService.checkUserIsZongji(currentUser.getId()) ){
            List<Integer> userIdList = new ArrayList<>();
            userIdList.add(dto.getCreator());
            userIdList.add(dto.getBelongerId());
            List<RSalesJBusinessOrderDto> list = shareService.getShareListByBusinessId(id,5);
            if(CollectionUtils.isNotEmpty(list)){
                List<Integer> saleUserIds = list.stream()
                        .map(RSalesJBusinessOrderDto::getSaleUserId)
                        .collect(Collectors.toList());
                userIdList.addAll(saleUserIds);
            }
            if(!userIdList.contains(currentUser.getId())){
                List<Integer> sonList = userApiService.queryUserIdListSubFromUac(currentUser.getId());
                boolean  containSon =  userIdList.stream().anyMatch(sonList::contains);
                if(!containSon){
                    boolean notAssign =  dto.getBelongerId()==null ||  dto.getBelongerId()<1;
                    if(notAssign){
                        return R.error("您无该条单据的查看权限。");
                    }else{
                        return R.error("您无该条单据的查看权限，可联系归属销售" + dto.getBelonger()+ "申请查看");
                    }

                }
            }

        }

        return R.success(dto);
    }


    /**
     *线索转商机
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getLeadsToChance", method = RequestMethod.POST)
    public R<BusinessChanceDto> getLeadsToChance(@RequestParam Integer id) {
        return R.success(businessLeadsService.getLeadsToChance(id));
    }


    @ResponseBody
    @RequestMapping("/getLeadMerge")
    @NoNeedAccessAuthorization
    public R<List<BusinessLeadMergeDto>> getLeadMerge(@RequestParam("id") Integer id) {
        BusinessLeadsDto dto = businessLeadsService.findById(id);
        if(dto == null){
            return R.error("线索不存在");
        }
        List<BusinessLeadMergeDto> leadMerge = businessLeadsService.getLeadMerge(dto.getLeadsNo());
        return R.success(leadMerge);
    }


}
