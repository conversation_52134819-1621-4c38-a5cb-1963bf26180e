<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>审批信息列表</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/list.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/lib/dialogSearch.css?rnd=${resourceVersionKey}">
</head>
<%--Anna.liu  编辑所有--%>
<%--Ethan.lin  编辑临时--%>
<%--Ted.dong  审核--%>

<body>
<div class="erp-wrap">
    <input type="hidden" value="${review.tabStatus}" id ="tabStatus">
    <div class="tab-nav J-list-tab" data-name="">
        <a class=" tab-item <c:if  test="${empty review.tabStatus or review.tabStatus == 1 }"> current </c:if>"
           href="/review/workbench/remindReview.do?tabStatus=1" data-value="">待审批</a>

        <a class="tab-item  <c:if test="${ review.tabStatus == 2}"> current </c:if>"
           href="/review/workbench/remindReview.do?tabStatus=2" data-value="">我的申请
                </a>
    </div>
    <div class="erp-block base-form search-wrap J-search-wrap">
        <div class="search-list ">
            <div class="search-item">
                <div class="item-label">查看类型：</div>
                <div class="item-fields">
                    <select class="J-select" id="checkSpecies" name="checkSpecies">
                        <option id="option" value="">请选择</option>
                        <c:forEach var="species" items="${speciesList}">
                            <option value="${species}"
                                    <c:if test="${species == review.checkSpecies}">selected="selected"</c:if>>${species}</option>
                        </c:forEach>
                    </select>
                </div>
            </div>
            <div class="search-item">
                <div class="item-label">提交时间：</div>
                <div class="item-fields">
                    <div class="item-fields J-date-range">
                        <div class="input-date item-input">
                            <input type="text" name="submitStartTime" id="submitStartTime" class="input-text"
                                   placeholder="请选择日期" readonly value="${review.submitStartTime}">
                        </div>
                        <div class="search-item-gap">-</div>
                        <div class="input-date item-input">
                            <input type="text" name="submitEndTime" id="submitEndTime" class="input-text"
                                   placeholder="请选择日期" readonly value="${review.submitEndTime}">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="search-btns">
            <div class="btn btn-small btn-blue-bd" onclick="search()">查询</div>
            <div class="btn btn-small " onclick=resetTab()>重置</div>
        </div>
    </div>
    <div class="erp-block erp-block-list">
        <table class="table table-base table-hover base-form J-table-wrap">
            <colgroup>
                <col width="">
                <col width="">
                <col width="">
                <col width="">
                <col width="">
                <col width="">
                <col width="">
            </colgroup>
            <tbody>
            <tr>
                <th>流程单号</th>
                <th>待办内容</th>
                <th>查看类型</th>
                <th>提交时间</th>
                <th>
                    <c:if test="${empty review.tabStatus or review.tabStatus == 1}">
                    提交人
                    </c:if>
                    <c:if test="${review.tabStatus == 2}">
                        审批人
                    </c:if>
                </th>
                <th>状态</th>
                <th>操作</th>
            </tr>
            <c:if test="${empty reviewList}">
                <tr>

                    <td class="no-data" colspan="10">
                        <div><i class="vd-icon icon-caution1"></i></div>
                        没有匹配的数据
                    </td>
                </tr>
            </c:if>
            <c:if test="${not empty reviewList}">
            <c:forEach items="${reviewList}" var="list" varStatus="status">
            <!--隐藏域-->
            <tr>
                <td>${list.processNo}</td>
                <td>${list.remindMessage}</td>
                <td>${list.checkSpecies}</td>
                <td>${list.submitTime}</td>
                <td>
                    <c:if test="${empty review.tabStatus or review.tabStatus == 1}">
                        ${list.submitUser}
                    </c:if>
                    <c:if test="${review.tabStatus == 2}">
                        ${list.reviewUser}
                    </c:if>
                </td>
                <td>
                    <c:if test="${empty review.tabStatus or review.tabStatus == 1}">
                        待审批
                    </c:if>
                    <c:if test="${review.tabStatus == 2}">
                        审批中
                    </c:if>
                </td>
                <td>
                    <a class="addtitle" href="javascript:void(0);"
                       tabTitle='{"num":"viewReview<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"${list.checkUrl}","title":"查看信息"}'>查看</a>
                </td>
                </c:forEach>
                </c:if>


            </tbody>
        </table>
        <tags:pageNew page="${page}"/>
    </div>
</div>

<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/list.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/call.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}static/libs/searchableSelect/jquery.searchableSelect.js?rnd=${resourceVersionKey}'></script>
<script>
    function search() {
        var tabStatus = $("#tabStatus").val()
        var checkSpecies = $("#checkSpecies").val()
        var submitStartTime = $("#submitStartTime").val()
        var submitEndTime = $("#submitEndTime").val()
        window.location.href ="/review/workbench/remindReview.do?tabStatus="+tabStatus+"&checkSpecies="+checkSpecies+"&submitStartTime="+submitStartTime+"&submitEndTime="+submitEndTime;
    }

    function resetTab() {
        var tabStatus = $("#tabStatus").val();
        window.location.href ="/review/workbench/remindReview.do?tabStatus="+tabStatus;
    }
</script>
<%@ include file="../../common/footer.jsp" %>