package com.newtask.finance;

import cn.hutool.core.date.DateUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.buyorder.service.BuyorderInfoQueryService;
import com.vedeng.erp.saleorder.api.SaleorderInfoQueryApiService;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.goods.dto.FairValueDto;
import com.vedeng.goods.dto.HistoryDataDto;
import com.vedeng.goods.dto.SaleOrderDataDto;
import com.vedeng.goods.service.FairValueApiService;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.goods.vo.CoreSkuVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/15 11:03
 */
@JobHandler(value = "FairValueTask")
@Component
@Slf4j
public class FairValueTask extends AbstractJobHandler {

    @Autowired
    private FairValueApiService fairValueApiService;

    @Autowired
    private GoodsApiService goodsApiService;

    @Autowired
    private SaleorderInfoQueryApiService saleorderInfoQueryApiService;

    @Autowired
    private BuyorderInfoQueryService buyorderInfoQueryService;

    private static final int CORE_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 5;
    private static final long KEEP_ALIVE_TIME = 60L;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        // 声明异步线程池
        final ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
                MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(MAX_POOL_SIZE * 4, true),
                new ThreadFactoryBuilder().setNameFormat("FairValueTask-pool-%d").build(),
                new ThreadPoolExecutor.AbortPolicy());

        // 所有需要处理的sku
        List<Integer> dataList = Lists.newArrayList();
        if (StringUtils.isBlank(param)){
            dataList = goodsApiService.getAllSkuId();
        }else {
            dataList = Arrays.asList(param.split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
        }


        // 每条子线程处理数据的数量
        int count = 1000;
        // 需要处理的数据总量
        int listSize = dataList.size();
        // 开启的线程数
        int runThreadSize = (listSize / count) + 1;
        // 创建一个线程池，数量和开启线程的数量一样

        // 用于记录返回结果
        List<Future<Map<String, List<FairValueDto>>>> resultList = new ArrayList<>();

        for (int i = 0; i < runThreadSize; i++) {
            // 本次循环子线程需要处理的数据
            List<Integer> newList;
            if ((i + 1) == runThreadSize) {
                // 最后一次分组可能不满count数量，所以特殊处理
                int startIndex;
                startIndex = (i * count);
                int endIndex = dataList.size();
                newList = dataList.subList(startIndex, endIndex);
            } else {
                int startIndex = (i * count);
                int endIndex = (i + 1) * count;
                newList = dataList.subList(startIndex, endIndex);
            }

            // 提交线程池处理
            Future<Map<String, List<FairValueDto>>> future = executor.submit(() -> this.handelSkuInfo(newList));
            resultList.add(future);
        }
        executor.shutdown();

        resultList.forEach(mapFuture -> {
            try {
                Map<String, List<FairValueDto>> stringListMap = mapFuture.get();
                if (stringListMap.get("addList").size() > 0) {
                    fairValueApiService.batchInsert(stringListMap.get("addList"));
                }
                if (stringListMap.get("updateList").size() > 0) {
                    fairValueApiService.batchUpdate(stringListMap.get("updateList"));
                }

            } catch (Exception e) {
                log.error("子线程处理公允价信息失败", e);
            }
        });
        return SUCCESS;
    }

    /**
     * 处理sku的公允价信息
     *
     * @param goodsIdList skuId
     */
    private Map<String, List<FairValueDto>> handelSkuInfo(List<Integer> goodsIdList) {
        List<FairValueDto> addFairValueDtoList = new ArrayList<>();
        List<FairValueDto> updateFairValueDtoList = new ArrayList<>();

        for (Integer goodsId : goodsIdList) {
            CoreSkuVo skuInfo = goodsApiService.getGoodsInfoBySkuId(goodsId);
            // 判断改sku是否已经存在公允价，来决定是新增还是修改
            FairValueDto existFairValue = fairValueApiService.selectByGoodsId(goodsId);
            existFairValue.setSku(skuInfo.getSkuNo());
            existFairValue.setGoodsId(goodsId);
            existFairValue.setSkuName(skuInfo.getShowName());

            List<SaleorderInfoDto> saleOrderInfoList = saleorderInfoQueryApiService.getSkuFairValueInfo(goodsId);
            if (saleOrderInfoList.size() > 0) {
                List<SaleOrderDataDto> saleOrderDataDtoList = new ArrayList<>();
                BigDecimal totalPrice = BigDecimal.ZERO;
                for (SaleorderInfoDto dto : saleOrderInfoList) {
                    SaleOrderDataDto temp = new SaleOrderDataDto();
                    temp.setSaleOrderNo(dto.getSaleorderNo());
                    temp.setPrice(dto.getPrice());
                    temp.setSaleDate(DateUtil.date(dto.getValidTime()));
                    saleOrderDataDtoList.add(temp);
                    totalPrice = totalPrice.add(dto.getPrice());
                }
                existFairValue.setSaleOrderDataDtoList(saleOrderDataDtoList);
                existFairValue.setPrice(totalPrice.divide(new BigDecimal(saleOrderInfoList.size()), 2, RoundingMode.HALF_UP));
            } else {
                BigDecimal fairValue = buyorderInfoQueryService.getBuyOrderGoodsFairValue(goodsId);
                existFairValue.setPrice(fairValue.multiply(new BigDecimal(1.1)).setScale(2, BigDecimal.ROUND_HALF_UP));
                existFairValue.setSaleOrderDataDtoList(new ArrayList<>());
            }

            // 历史公允价需要每次都记录下来
            HistoryDataDto thisMonthFairValue = new HistoryDataDto();
            thisMonthFairValue.setFairValue(existFairValue.getPrice());
            thisMonthFairValue.setGenerateDate(new Date());
            if (existFairValue.getFairValueId() != null) {
                // 编辑时 累加
                existFairValue.getHistoryDataDtoList().add(0, thisMonthFairValue);
                updateFairValueDtoList.add(existFairValue);
            } else {
                // 新增时，直接插入
                existFairValue.setHistoryDataDtoList(Collections.singletonList(thisMonthFairValue));
                addFairValueDtoList.add(existFairValue);
            }
        }

        Map<String, List<FairValueDto>> result = new HashMap<>(2);
        result.put("addList", addFairValueDtoList);
        result.put("updateList", updateFairValueDtoList);
        return result;
    }
}
