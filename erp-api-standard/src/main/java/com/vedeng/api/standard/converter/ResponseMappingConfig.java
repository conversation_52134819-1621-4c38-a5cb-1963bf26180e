package com.vedeng.api.standard.converter;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 响应映射配置类
 * 用于配置通用响应转换的规则
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public class ResponseMappingConfig {
    
    /**
     * 成功视图名称集合
     */
    private Set<String> successViewNames;
    
    /**
     * 失败视图名称集合
     */
    private Set<String> failureViewNames;
    
    /**
     * ID字段名称数组（按优先级排序）
     */
    private String[] idFieldNames;
    
    /**
     * 编号字段名称数组（按优先级排序）
     */
    private String[] numberFieldNames;
    
    /**
     * 名称字段名称数组（按优先级排序）
     */
    private String[] nameFieldNames;
    
    /**
     * 金额字段名称数组（按优先级排序）
     */
    private String[] amountFieldNames;
    
    /**
     * 数量字段名称数组（按优先级排序）
     */
    private String[] quantityFieldNames;
    
    /**
     * 成功消息
     */
    private String successMessage;
    
    /**
     * 失败消息
     */
    private String failureMessage;
    
    /**
     * 跳转URL模板（支持占位符，如：/order/detail.do?id={id}）
     */
    private String redirectUrlTemplate;
    
    /**
     * 私有构造函数
     */
    private ResponseMappingConfig() {
        this.successViewNames = new HashSet<>();
        this.failureViewNames = new HashSet<>();
    }
    
    /**
     * 创建构建器
     * 
     * @return 配置构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 创建用于创建操作的默认配置
     * 
     * @param idFieldNames ID字段名称
     * @param successMessage 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig forCreateOperation(String successMessage, String... idFieldNames) {
        return builder()
                .successViewNames("common/success")
                .failureViewNames("common/fail")
                .idFieldNames(idFieldNames)
                .successMessage(successMessage)
                .failureMessage("操作失败")
                .build();
    }
    
    /**
     * 创建用于查询操作的默认配置
     *
     * @param successMessage 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig forQueryOperation(String successMessage) {
        return builder()
                .successViewNames("common/success")
                .failureViewNames("common/fail")
                .successMessage(successMessage)
                .failureMessage("查询失败")
                .build();
    }

    /**
     * 创建用于更新操作的默认配置
     *
     * @param successMessage 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig forUpdateOperation(String successMessage) {
        return builder()
                .successViewNames("common/success")
                .failureViewNames("common/fail")
                .successMessage(successMessage)
                .failureMessage("更新失败")
                .build();
    }

    /**
     * 创建用于删除操作的默认配置
     *
     * @param successMessage 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig forDeleteOperation(String successMessage) {
        return builder()
                .successViewNames("common/success")
                .failureViewNames("common/fail")
                .successMessage(successMessage)
                .failureMessage("删除失败")
                .build();
    }

    /**
     * 创建用于提交操作的默认配置
     *
     * @param successMessage 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig forSubmitOperation(String successMessage) {
        return builder()
                .successViewNames("common/success")
                .failureViewNames("common/fail")
                .successMessage(successMessage)
                .failureMessage("提交失败")
                .build();
    }

    /**
     * 创建用于审核操作的默认配置
     *
     * @param successMessage 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig forApprovalOperation(String successMessage) {
        return builder()
                .successViewNames("common/success")
                .failureViewNames("common/fail")
                .successMessage(successMessage)
                .failureMessage("审核失败")
                .build();
    }

    /**
     * 创建用于通用操作的默认配置
     *
     * @param successMessage 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig forOperation(String successMessage) {
        return builder()
                .successViewNames("common/success")
                .failureViewNames("common/fail")
                .successMessage(successMessage)
                .failureMessage("操作失败")
                .build();
    }
    
    /**
     * 配置构建器
     */
    public static class Builder {
        private ResponseMappingConfig config;
        
        public Builder() {
            this.config = new ResponseMappingConfig();
        }
        
        public Builder successViewNames(String... viewNames) {
            if (viewNames != null) {
                config.successViewNames.addAll(Arrays.asList(viewNames));
            }
            return this;
        }
        
        public Builder failureViewNames(String... viewNames) {
            if (viewNames != null) {
                config.failureViewNames.addAll(Arrays.asList(viewNames));
            }
            return this;
        }
        
        public Builder idFieldNames(String... fieldNames) {
            config.idFieldNames = fieldNames;
            return this;
        }
        
        public Builder numberFieldNames(String... fieldNames) {
            config.numberFieldNames = fieldNames;
            return this;
        }
        
        public Builder nameFieldNames(String... fieldNames) {
            config.nameFieldNames = fieldNames;
            return this;
        }
        
        public Builder amountFieldNames(String... fieldNames) {
            config.amountFieldNames = fieldNames;
            return this;
        }
        
        public Builder quantityFieldNames(String... fieldNames) {
            config.quantityFieldNames = fieldNames;
            return this;
        }
        
        public Builder successMessage(String message) {
            config.successMessage = message;
            return this;
        }
        
        public Builder failureMessage(String message) {
            config.failureMessage = message;
            return this;
        }
        
        public Builder redirectUrlTemplate(String template) {
            config.redirectUrlTemplate = template;
            return this;
        }
        
        public ResponseMappingConfig build() {
            return config;
        }
    }
    
    // Getter methods
    public Set<String> getSuccessViewNames() {
        return successViewNames;
    }
    
    public Set<String> getFailureViewNames() {
        return failureViewNames;
    }
    
    public String[] getIdFieldNames() {
        return idFieldNames;
    }
    
    public String[] getNumberFieldNames() {
        return numberFieldNames;
    }
    
    public String[] getNameFieldNames() {
        return nameFieldNames;
    }
    
    public String[] getAmountFieldNames() {
        return amountFieldNames;
    }
    
    public String[] getQuantityFieldNames() {
        return quantityFieldNames;
    }
    
    public String getSuccessMessage() {
        return successMessage;
    }
    
    public String getFailureMessage() {
        return failureMessage;
    }
    
    public String getRedirectUrlTemplate() {
        return redirectUrlTemplate;
    }
}
