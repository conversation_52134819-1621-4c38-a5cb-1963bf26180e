package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶  进项费用普通发票
 * @date 2022/11/09 09:13
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID","FPUREXPINVENTRY.FEntryID","FPUREXPINVENTRY.F_QZOK_BDDJHID"})
public class InPutFeePlainInvoiceDto extends KingDeeMqBaseDto {

    private Integer inPutFeePlainInvoiceId;

    /**
     * fid
     */
    @WriteBackField
    @KingDeeID
    private String fid;
    /**
     * 贝登erp对应的单据头ID
     */
    @BusinessID("F_QZOK_BDDJTID")
    private String fQzokBddjtid;
    /**
     * 发票号
     */
    private String finvoiceno;
    /**
     * 发票代码
     */
    private String fQzokFpdm;
    /**
     * 发票日期
     */
    private String finvoicedate;
    /**
     * 业务日期
     */
    private String fdate;
    /**
     * 往来单位类型
     */
    private String fcontactunittype;
    /**
     * 单据类型
     */
    private String fBillTypeID;
    /**
     * 供应商
     */
    private String fsupplierid;
    /**
     * 结算组织
     */
    private String fsettleorgid;
    /**
     * 采购组织
     */
    private String fpurchaseorgid;
    /**
     * 单据状态
     */
    private String fdocumentstatus;
    /**
     * 红蓝字标识
     */
    private String fRedBlue;
    /**
     * 发票明细
     */
    private List<InPutFeePlainInvoiceDetailDto> FPUREXPINVENTRY = new ArrayList<>();


    public InPutFeePlainInvoiceDto() {
        this.fcontactunittype = "BD_Supplier";
        this.fBillTypeID = "FYFP01_SYS";
        this.fsettleorgid = KingDeeConstant.ORG_ID.toString();
        this.fdocumentstatus = "Z";
        this.fpurchaseorgid = KingDeeConstant.ORG_ID.toString();
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.INPUT_FEE_PLAIN_INVOICE;
    }

    public InPutFeePlainInvoiceDto(KingDeeBizEnums kingDeeBizEnums) {
        super(kingDeeBizEnums);
    }
}
