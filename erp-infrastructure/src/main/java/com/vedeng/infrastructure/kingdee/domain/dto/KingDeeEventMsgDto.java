package com.vedeng.infrastructure.kingdee.domain.dto;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务事件消息表
 * @date 2022/8/29 12:47
 */
@Getter
@Setter
public class KingDeeEventMsgDto extends BaseDto {

    /**
     * 主键
     */
    private Integer kingDeeEventMsgId;

    /**
     * 消息体，比如订单对象的序列化结果。
     */
    private JSONArray body;

    /**
     * 对象类型
     */
    private String classPath;

    /**
     * 消息状态(1.消费中 2.已消费 3.重新消费，4无需处理)
     */
    private Integer messageStatus;

    /**
     * 业务类型，根据不同的业务类型进行消费
     * （由KingDeeBizEnums中 business+operation 组成 ： business_operation）
     */
    private String eventType;

    /**
     * 业务主键
     */
    private String businessId;

    /**
     * 消费顺序 定时任务按小到大执行,对未消费的进行排序
     */
    private Integer msgOrder;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 重试次数
     */
    private Integer reTryNum;

    /**
     * 是否发过通知 0 没有 1 有
     */
    private Integer sendMsg;

    /**
     * 异常信息
     */
    private String errorMsg;





}
