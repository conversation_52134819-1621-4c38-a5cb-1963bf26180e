package com.vedeng.infrastructure.kingdee.domain.dto;

import com.vedeng.infrastructure.kingdee.common.sdk.entity.JsonBase;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 查询参数
 * @date 2022/9/9 13:41
 */
@Getter
@Setter
public class KingDeeQueryExtParam extends JsonBase {

    String FormId;
    public String FieldKeys;
    List<KingDeeQueryFilterDto> FilterString;
    String OrderString;
    int StartRow;
    int Limit;
    int TopRowCount;
}
