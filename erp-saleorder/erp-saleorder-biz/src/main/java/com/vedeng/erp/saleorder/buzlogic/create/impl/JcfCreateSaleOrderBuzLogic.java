package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.buzlogic.create.CreateSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.dao.SaleorderCopyRecordMapper;
import com.vedeng.erp.saleorder.domain.entity.SaleorderCopyRecordEntity;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.OrderCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class JcfCreateSaleOrderBuzLogic extends CreateSaleOrderBuzLogic {
    public JcfCreateSaleOrderBuzLogic() {
        List<String> sequence = new ArrayList<>();
        sequence.add("createOrder");
        sequence.add("pushFormal");
        sequence.add("syncSaleorderDate");
        super.setSequence(sequence);
    }

    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    /**
     * @return
     * @desc 推送前台
     */
    public ResultInfo pushFormal(Saleorder saleorder) {
        orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(), null, OrderDataUpdateConstant.SALE_ORDER_GENERATE);
        return new ResultInfo(0, "操作成功");
    }

    @Autowired
    private SaleorderCopyRecordMapper saleorderCopyRecordMapper;

    public ResultInfo<?> saveCopyOrder(Saleorder saleorder) {
        Integer fromSaleorderId = saleorder.getSaleorderId();
        Saleorder newSaleOrder = baseSaleOrderService.saveCopyOrder(saleorder);

        SaleorderCopyRecordEntity insertEntity = new SaleorderCopyRecordEntity();
        insertEntity.setSaleorderId(newSaleOrder.getSaleorderId());
        insertEntity.setFromSaleorderId(fromSaleorderId);
        insertEntity.setChannel(ErpConst.ONE);
        saleorderCopyRecordMapper.insertSelective(insertEntity);

        return new ResultInfo(0, "创建销售单成功");
    }

    @Override
    public ResultInfo run(Saleorder saleorder) {
        new JcfCreateSaleOrderBuzLogic();
        ResultInfo result = new ResultInfo(0, "操作成功");
        for (String methodName : getSequence()) {
            switch (methodName) {
                case "createOrder":
                    saleorder.setConfirmStatus(1);
                    saleorder.setConfirmTime(new Date());
                    if (ErpConst.ZERO.equals(result.getCode()) && ErpConst.ONE.equals(saleorder.getIsCopy())) {
                        result = this.saveCopyOrder(saleorder);
                    } else if (ErpConst.ZERO.equals(result.getCode())) {
                        result = super.createOrder(saleorder);
                    }
                    break;
                case "pushFormal":
                    if (ErpConst.ZERO.equals(result.getCode())) {
                        result = this.pushFormal(saleorder);
                    }
                    break;
                case "syncSaleorderDate":
                    if (ErpConst.ZERO.equals(result.getCode())) {
                        result = super.syncSaleorderDate(saleorder);
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
