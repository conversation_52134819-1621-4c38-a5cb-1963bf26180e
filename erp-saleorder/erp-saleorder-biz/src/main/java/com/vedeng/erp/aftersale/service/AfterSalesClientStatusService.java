package com.vedeng.erp.aftersale.service;


import com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord;

import java.util.List;

/**
 * 用户端售后状态service
 */
public interface AfterSalesClientStatusService {

    /**
     * 获取售后单的客户端状态记录
     * @param afterSalesId 售后单ID
     * @return 客户端状态记录
     */
    List<AfterSalesClientStatusRecord> getClientStatusRecordList(Integer afterSalesId);

    /**
     * 更新售后单的客户端状态
     *
     * @param afterSalesId       售后单ID
     * @param oldClientStatus   旧的客户端状态
     * @param newClientStatus   新的客户端状态
     */
    void updateClientStatus(Integer afterSalesId, Integer oldClientStatus, Integer newClientStatus);
}
