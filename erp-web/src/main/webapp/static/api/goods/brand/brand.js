/**
 * 初始化数据
 * @param data
 * @returns {*}
 */
function queryBrand(data) {
    return axios({
        url: '/goodsBandApi/queryBrand.do',
        method: 'post',
        params:data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

function getBrand(data) {
    return axios({
        url: '/goodsBandApi/getBrand.do',
        method: 'post',
        data:data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}



