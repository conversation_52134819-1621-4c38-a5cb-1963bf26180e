package com.vedeng.goods.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.system.model.Attachment;


import java.util.List;
import java.util.Map;


public interface ManufacturerService {
    Map<String, Object> getProductCompanyInfoListPage(Map<String, Object> paramMap, Page page, Manufacturer manufacturer);

    void checkProductInfo(Manufacturer manufacturer);

    void save(Manufacturer manufacturer);

    void saveTimeChange(Manufacturer manufacturer);

    boolean isValidByRegistrationNumber(Integer firstEngageId);

    /**
     * 添加或编辑生产厂商信息
     *
     * @param manufacturer 生产厂商
     * @param userId 创建人
     * @param resetAudit 是否重置审核状态
     * @return
     */
    Integer addProductInfo(Manufacturer manufacturer, Integer userId, boolean resetAudit);


    Integer addProductInfoTimeChange(Manufacturer manufacturer, Integer userId, boolean resetAudit);

    Manufacturer getManufacturerDetail(Integer manufacturerId);

    Manufacturer getOfficialB(Integer manufacturerId);

    /**
     *  上传vedeng公章
     */
    void uploadOfficialWithB(Manufacturer manufacturer, User sessUser);

    ResultInfo deleteManufacturer(Integer manufacturerId, Integer userId);

    /**
     * 审核生产厂商
     * @param manufacturer model
     * @param sessUser 当前用户
     */
    void auditManufacture(Manufacturer manufacturer, User sessUser);


    /**
     *  获取审核数据
     * @param manufacturerId
     * @return
     */
    List<LogCheckGenerate> listCheckLog(Integer manufacturerId);


    /**
     * 构建生产厂商附件信息
     * @param manufacturer
     */
     void buildAttachmentFile(Manufacturer manufacturer);
}
