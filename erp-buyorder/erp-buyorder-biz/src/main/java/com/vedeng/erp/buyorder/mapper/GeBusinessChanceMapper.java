package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.GeBusinessChance;
import com.vedeng.erp.buyorder.dto.GeExamineBasicDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GeBusinessChanceMapper {
    int deleteByPrimaryKey(Integer geBussinessChanceId);

    int insert(GeBusinessChance record);

    int insertSelective(GeBusinessChance record);

    GeBusinessChance selectByPrimaryKey(Integer geBussinessChanceId);

    int updateByPrimaryKeySelective(GeBusinessChance record);

    int updateByPrimaryKey(GeBusinessChance record);

    GeExamineBasicDto queryBasicInfo(@Param("geBussinessChanceId") Integer geBussinessChanceId);

    /**
     * <AUTHOR>
     * @desc 根据报价单号+终端医院名称+意向型号+报单状态（空、可跟进）查询去重
     * @param geBusinessChance
     * @return
     */
    List<GeBusinessChance> queryForUniqueVerify(GeBusinessChance geBusinessChance);

    /**
     * <AUTHOR>
     * @desc 根据报价单号查询数据
     * @param geBussinessChanceNo
     * @return
     */
    GeBusinessChance queryByNo(String geBussinessChanceNo);

    void updateStatusByGeBussinessChanceId(GeBusinessChance geBusinessChance);

    /**
     * <AUTHOR>
     * @desc 更新商机是否维护信息
     * @param geBusinessChance
     * @return
     */
    int updateIsDefend(GeBusinessChance geBusinessChance);
}