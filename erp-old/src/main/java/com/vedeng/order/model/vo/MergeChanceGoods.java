package com.vedeng.order.model.vo;

import java.io.Serializable;

public class MergeChanceGoods implements Serializable {

    /**
     * @Fields serialVersionUID : TODO
     */
    private static final long serialVersionUID = 1L;

    private Integer bussinessChanceId;

    private Long addTime;

    private String content;

    private Integer goodsCategory;

    private String goodsBrand;

    private String goodsName;

    private String attachmentName;// 附件名称

    private String attachmentUri;//

    private String attachmentDomain;//

    private String goodsCategoryName;// 商品类型

    public Integer getBussinessChanceId() {
        return bussinessChanceId;
    }

    public void setBussinessChanceId(Integer bussinessChanceId) {
        this.bussinessChanceId = bussinessChanceId;
    }

    public String getGoodsCategoryName() {
        return goodsCategoryName;
    }

    public void setGoodsCategoryName(String goodsCategoryName) {
        this.goodsCategoryName = goodsCategoryName;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getGoodsCategory() {
        return goodsCategory;
    }

    public void setGoodsCategory(Integer goodsCategory) {
        this.goodsCategory = goodsCategory;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentUri() {
        return attachmentUri;
    }

    public void setAttachmentUri(String attachmentUri) {
        this.attachmentUri = attachmentUri;
    }

    public String getAttachmentDomain() {
        return attachmentDomain;
    }

    public void setAttachmentDomain(String attachmentDomain) {
        this.attachmentDomain = attachmentDomain;
    }
}
