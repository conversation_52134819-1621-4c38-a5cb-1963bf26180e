package com.vedeng.erp.trader.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 终端数据
 */
@Getter
@Setter
public class TerminalResponseDto {

    /**
     * 终端客户id
     */
    private Integer terminalTraderId;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 覆盖情况
     */
    private String coverStatus;

    /**
     * 合作情况
     */
    private String cooperationStatus;

    /**
     * 所在地区
     */
    private String belongArea;

    private Integer provinceCode;

    private String provinceName;

    private Integer cityCode;

    private String cityName;

    /**
     * 区Id
     */
    private Integer zoneId;

    /**
     * 终端大类
     */
    private String terminalCategories;

    /**
     * 机构评级
     */
    private String institutionLevel;

    /**
     * 机构性质
     */
    private String institutionNature;

    /**
     * 科室
     */
    private String department;

    /**
     * 合作经销商数
     */
    private Integer distributionLinkCount;

    /**
     * 招标次数
     */
    private Integer biddingCount;

    /**
     * 排序的字段
     * 1:中标次数 7:合作经销商数量
     */
    private Integer sortColumn;

    /**
     * 0 升序 1降序 (默认升序)
     */
    private Integer sortType;

    /**
     * 指派拜访人id
     */
    private Integer visitorId;

    /**
     * 指派拜访人名称
     */
    private String visitorName;

    /**
     * 是否归属当前销售 0-否 1-是
     */
    private Integer isBelongCurrentSales;

}