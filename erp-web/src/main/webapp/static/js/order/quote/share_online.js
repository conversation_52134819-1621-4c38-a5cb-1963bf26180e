$(function(){

});

function shareOnlie(quoteorderId) {
    checkLogin();
    $.ajax({
        async : false,
        url : page_url + '/orderstream/saleorder/checkQuoteShare.do',
        data : {"quoteorderId":quoteorderId,},
        type : "POST",
        dataType : "json",
        success : function(data) {
            if (data.code == 0) {
                layer.open({
                    type: 2,
                    shadeClose: false, //点击遮罩关闭
                    area: ["40%", "70%"],
                    title: "分享线上报价单",
                    content: ["/orderstream/saleorder/shareOnline.do?quoteorderId="+quoteorderId,'no']
                });
            } else if(data.code == -1){
                layer.alert(data.message);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}


function copyText() {
    debugger
    var text = document.getElementById("text").innerText;
    var input = document.getElementById("input");
    input.value = text; // 修改文本框的内容
    input.select(); // 选中文本
    document.execCommand("copy"); // 执行浏览器复制命令
}




