package com.vedeng.erp.buyorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.erp.buyorder.domain.entity.*;
import com.vedeng.erp.buyorder.dto.GeBusinessChanceDetailDto;
import com.vedeng.erp.buyorder.dto.GeBusinessChanceDetailRecordDto;
import com.vedeng.erp.buyorder.dto.GeBusinessChanceDto;
import com.vedeng.erp.buyorder.mapper.*;
import com.vedeng.erp.buyorder.service.GeBusinessChanceDetailService;
import com.vedeng.erp.buyorder.service.GeBusinessChanceService;
import com.vedeng.erp.buyorder.dto.GeExamineBasicDto;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 13:01
 * @describe
 */
@Service
public class GeBusinessChanceServiceImpl implements GeBusinessChanceService {
    public static Logger logger = LoggerFactory.getLogger(GeBusinessChanceServiceImpl.class);

    @Resource
    private GeBusinessChanceMapper geBusinessChanceMapper;

    @Autowired
    private OrderNoDict orderNoDict;

    @Resource
    private GeBusinessChanceDetailMapper geBusinessChanceDetailMapper;

    @Resource
    private GeBusinessChanceDetailRecordMapper geBusinessChanceDetailRecordMapper;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Resource
    private GeBusinessChanceFeedBackMapper geBusinessChanceFeedBackMapper;

    @Resource
    private GeActionLogMapper geActionLogMapper;

    @Autowired
    private GeBusinessChanceDetailService geBusinessChanceDetailService;

    @Override
    public GeExamineBasicDto queryBasicInfo(Integer geBussinessChanceId) {
        return geBusinessChanceMapper.queryBasicInfo(geBussinessChanceId);
    }

    @Override
    @Transactional
    public ResultInfo saveGeBusinesschance(GeBusinessChance geBusinessChance, User user) {
        List<GeBusinessChance> existChance = geBusinessChanceMapper.queryForUniqueVerify(geBusinessChance);
        if(CollectionUtils.isNotEmpty(existChance)){
            //存在报价单号+终端医院名称+意向型号+报单状态（空、可跟进）一致的ge商机单
            logger.info("存在信息一致的报价单,查询结果{},", JSON.toJSONString(existChance));
            return new ResultInfo(-1,"与"+existChance.get(0).getGeBussinessChanceNo()+"重复，请核对信息后重新提交!");
        }
        //生成GE商机单号
        geBusinessChance.setGeBussinessChanceNo(orderNoDict.getOrderNum(0, SysOptionConstant.ID_26));
        geBusinessChance.setAddTime(new Date());
        geBusinessChance.setCreator(user.getUserId());
        geBusinessChance.setCreatorName(user.getRealName());
        geBusinessChance.setIsDefend(ErpConst.ZERO);
        geBusinessChanceMapper.insertSelective(geBusinessChance);
        GeBusinessChance result = geBusinessChanceMapper.queryByNo(geBusinessChance.getGeBussinessChanceNo());
        //保存GE商机详细
        GeBusinessChanceDetail geBusinessChanceDetail = new GeBusinessChanceDetail();
        geBusinessChanceDetail.setGeBussinessChanceId(result.getGeBussinessChanceId());
        geBusinessChanceDetail.setBusinessChanceStatus(ErpConst.ONE);
        geBusinessChanceDetail.setAddTime(new Date());
        geBusinessChanceDetail.setCreator(user.getUserId());
        geBusinessChanceDetail.setCreatorName(user.getRealName());
        geBusinessChanceDetailMapper.insertSelective(geBusinessChanceDetail);

        //记录日志
        GeActionLog geActionLog = new GeActionLog();
        geActionLog.setRelatedId(result.getGeBussinessChanceId());
        geActionLog.setRelatedType(1);
        geActionLog.setAddTime(new Date());
        geActionLog.setCreator(user.getUserId());
        geActionLog.setCreatorName(user.getRealName());
        geActionLog.setOperation("提交商机");
        geActionLog.setContent("提交商机待GE审核");
        geActionLogMapper.insertSelective(geActionLog);
        return new ResultInfo(0,"新增成功",result);
    }

    @Override
    public GeBusinessChanceDto searchGeBusinessChanceInfo(ModelAndView mv, Integer geBusinessChanceId) {
        //查询GE商机主表信息
        GeBusinessChance geBusinessChance = geBusinessChanceMapper.selectByPrimaryKey(geBusinessChanceId);
        //商机来源字典
        SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(geBusinessChance.getGeBusinessChanceSource());
        GeBusinessChanceDto geBusinessChanceDto = new GeBusinessChanceDto();
        BeanUtils.copyProperties(geBusinessChance,geBusinessChanceDto);
        geBusinessChanceDto.setGeBusinessChanceSourceName(sysOptionDefinition.getTitle());
        mv.addObject("geBusinessChance",geBusinessChanceDto);
        if(geBusinessChance != null){
            //查询GE商机明细信息
            GeBusinessChanceDetailDto geBusinessChanceDetailDto = geBusinessChanceDetailService.queryByGeBusinessChanceId(geBusinessChanceId);
            mv.addObject("geBusinessChanceDetail",geBusinessChanceDetailDto);
            if(geBusinessChanceDetailDto != null){
                //查询GE商机明细信息记录
                GeBusinessChanceDetailRecord geBusinessChanceDetailRecord = geBusinessChanceDetailRecordMapper.queryByGeBussinessChanceDetailId(geBusinessChanceDetailDto.getGeBussinessChanceDetailId());
                if(geBusinessChanceDetailRecord != null){
                    //医院规模字典
                    GeBusinessChanceDetailRecordDto geBusinessChanceDetailRecordDto = new GeBusinessChanceDetailRecordDto();
                    BeanUtils.copyProperties(geBusinessChanceDetailRecord,geBusinessChanceDetailRecordDto);
                    if(!ErpConst.ZERO.equals(geBusinessChanceDetailRecord.getHospitalSize())){
                        sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(geBusinessChanceDetailRecord.getHospitalSize());
                        geBusinessChanceDetailRecordDto.setHospitalSizeName(sysOptionDefinition.getTitle());
                    }
                    //采购形式字典
                    if(!ErpConst.ZERO.equals(geBusinessChanceDetailRecord.getBuyType())){
                        sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(geBusinessChanceDetailRecord.getBuyType());
                        geBusinessChanceDetailRecordDto.setBuyTypeName(sysOptionDefinition.getTitle());
                    }
                    //项目阶段字典
                    if(!ErpConst.ZERO.equals(geBusinessChanceDetailRecord.getProjectPhase())){
                        sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(geBusinessChanceDetailRecord.getProjectPhase());
                        geBusinessChanceDetailRecordDto.setProjectPhaseName(sysOptionDefinition.getTitle());
                    }
                    mv.addObject("geBusinessChanceDetailRecord",geBusinessChanceDetailRecordDto);
                }
                //查询GE商机维护记录日志表
                GeActionLog geActionLog = new GeActionLog();
                geActionLog.setRelatedId(geBusinessChanceId);
                geActionLog.setRelatedType(ErpConst.TYPE_1);
                List<GeActionLog> geActionLogs = geActionLogMapper.queryByRelatedTypeAndRelatedId(geActionLog);
                mv.addObject("geActionLogs",geActionLogs);
            }
        }
        //GE审核反馈查询
        GeBusinessChanceFeedBack geBusinessChanceFeedBack = geBusinessChanceFeedBackMapper.queryByGeBussinessChanceId(geBusinessChanceId);
        mv.addObject("geBusinessChanceFeedBack",geBusinessChanceFeedBack);
        return geBusinessChanceDto;
    }

    @Override
    public GeBusinessChanceDto queryGeBusinessChanceById(Integer geBusinessChanceId) {
        //查询GE商机主表信息
        GeBusinessChance geBusinessChance = geBusinessChanceMapper.selectByPrimaryKey(geBusinessChanceId);
        //商机来源字典
        SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(geBusinessChance.getGeBusinessChanceSource());
        GeBusinessChanceDto geBusinessChanceDto = new GeBusinessChanceDto();
        BeanUtils.copyProperties(geBusinessChance,geBusinessChanceDto);
        geBusinessChanceDto.setGeBusinessChanceSourceName(sysOptionDefinition.getTitle());
        return geBusinessChanceDto;
    }

    @Override
    public List<GeActionLog> queryOperationLog(Integer geBussinessChanceId) {
        //查询GE商机维护记录日志表
        GeActionLog geActionLog = new GeActionLog();
        geActionLog.setRelatedId(geBussinessChanceId);
        geActionLog.setRelatedType(ErpConst.TYPE_1);
        List<GeActionLog> geActionLogs = geActionLogMapper.queryByRelatedTypeAndRelatedId(geActionLog);
        return geActionLogs;
    }
}
