<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.BankMatchConfigMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.BankMatchConfigEntity">
    <!--@mbg.generated-->
    <!--@Table T_BANK_MATCH_CONFIG-->
    <id column="BANK_MATCH_CONFIG_ID" jdbcType="BIGINT" property="bankMatchConfigId" />
    <result column="GROUP_ID" jdbcType="BIGINT" property="groupId" />
    <result column="KEY_WORDS" jdbcType="VARCHAR" property="keyWords" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BANK_MATCH_CONFIG_ID, GROUP_ID, KEY_WORDS, IS_DELETE, ADD_TIME, MOD_TIME,
    CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BANK_MATCH_CONFIG
    where BANK_MATCH_CONFIG_ID = #{bankMatchConfigId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_BANK_MATCH_CONFIG
    where BANK_MATCH_CONFIG_ID = #{bankMatchConfigId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BANK_MATCH_CONFIG_ID" keyProperty="bankMatchConfigId" parameterType="com.vedeng.erp.trader.domain.entity.BankMatchConfigEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_MATCH_CONFIG (GROUP_ID, KEY_WORDS,
      IS_DELETE, ADD_TIME, MOD_TIME,
      CREATOR, CREATOR_NAME, UPDATER,
      UPDATER_NAME, UPDATE_REMARK)
    values (#{groupId,jdbcType=BIGINT}, #{keyWords,jdbcType=VARCHAR},
      #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER},
      #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="BANK_MATCH_CONFIG_ID" keyProperty="bankMatchConfigId" parameterType="com.vedeng.erp.trader.domain.entity.BankMatchConfigEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_MATCH_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        GROUP_ID,
      </if>
      <if test="keyWords != null">
        KEY_WORDS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="keyWords != null">
        #{keyWords,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.BankMatchConfigEntity">
    <!--@mbg.generated-->
    update T_BANK_MATCH_CONFIG
    <set>
      <if test="groupId != null">
        GROUP_ID = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="keyWords != null">
        KEY_WORDS = #{keyWords,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where BANK_MATCH_CONFIG_ID = #{bankMatchConfigId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.BankMatchConfigEntity">
    <!--@mbg.generated-->
    update T_BANK_MATCH_CONFIG
    set GROUP_ID = #{groupId,jdbcType=BIGINT},
      KEY_WORDS = #{keyWords,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where BANK_MATCH_CONFIG_ID = #{bankMatchConfigId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BANK_MATCH_CONFIG
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="GROUP_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.groupId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="KEY_WORDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.keyWords,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where BANK_MATCH_CONFIG_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.bankMatchConfigId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BANK_MATCH_CONFIG
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="GROUP_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.groupId != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.groupId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="KEY_WORDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.keyWords != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.keyWords,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when BANK_MATCH_CONFIG_ID = #{item.bankMatchConfigId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where BANK_MATCH_CONFIG_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.bankMatchConfigId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="BANK_MATCH_CONFIG_ID" keyProperty="bankMatchConfigId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_MATCH_CONFIG
    (GROUP_ID, KEY_WORDS, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME,
      UPDATER, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groupId,jdbcType=BIGINT}, #{item.keyWords,jdbcType=VARCHAR},
        #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP},
        #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER},
        #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>


  <select id="findAllByGroupId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_BANK_MATCH_CONFIG
    where
    GROUP_ID = #{groupId,jdbcType=BIGINT}
    and IS_DELETE = 0
  </select>

<!--auto generated by MybatisCodeHelper on 2024-08-26-->
  <delete id="deleteByGroupId">
    delete from T_BANK_MATCH_CONFIG
    where GROUP_ID=#{groupId,jdbcType=BIGINT}
  </delete>

<!--auto generated by MybatisCodeHelper on 2024-08-26-->
  <select id="findByKeyWords" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BANK_MATCH_CONFIG
    where KEY_WORDS=#{keyWords,jdbcType=VARCHAR}
    and IS_DELETE = 0
  </select>
</mapper>
