<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="编辑归属" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="form-list  form-tips8">
	<form id="contractPriceForm">
		<div class="tcenter" style="margin-top: 15px">
			<input type="hidden" name="id" value="${contractPriceId} ">
			<span style="color: red">*</span><span>合约价</span><input type="text" value="<fmt:formatNumber value="${contractPrice}" pattern="#.##" minFractionDigits="2" ></fmt:formatNumber>" name="contractPrice" onkeyup="clearNoNum(this)">
		</div>

		<div class="add-tijiao tcenter" style="margin-top: 35px">

			<button type="button" onclick="submited();" style="background-color: blue">提交</button>
			<button class="dele" type="button" id="close-layer">取消</button>
		</div>
	</form>
</div>
<script type="text/javascript">

	function clearNoNum(obj){
		//修复第一个字符是小数点 的情况.
		if(obj.value !=''&& obj.value.substr(0,1) == '.'){
			obj.value="";
		}
		obj.value = obj.value.replace(/^0*(0\.|[1-9])/, '$1');//解决 粘贴不生效
		obj.value = obj.value.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
		obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
		obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
		obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
		if(obj.value.indexOf(".")< 0 && obj.value !=""){//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
			if(obj.value.substr(0,1) == '0' && obj.value.length == 2){
				obj.value= obj.value.substr(1,obj.value.length);
			}
		}
	}


	function submited() {
		$.ajax({
			async : false,
			url : './saveEditContractPrice.do',
			data : $("#contractPriceForm").serialize(),
			type : "POST",
			dataType : "json",
			success : function(data)
			{
				if(data.code == -1){
				layer.confirm(data.message,{
					btn: ['确定'] //按钮
				}, function(){
					layer.close(layer.index);
				}, function(){
				})
				}
				if (data.code == 0){
					parent.location.reload();
				}
			},
			error : function(XmlHttpRequest, textStatus, errorThrown)
			{
				//console.log(XmlHttpRequest.status);
				//console.log(textStatus);
				//console.log(errorThrown);
				if(XmlHttpRequest.status == 1001)
				{
					layer.alert("您没有操作权限哦");
				}
				else
				{
					layer.alert("操作失败");
				}
			}
		});
	}
</script>
<%--<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/goods/category/edit_assign.js?rnd=${resourceVersionKey}"></script>--%>
<%@ include file="../../common/footer.jsp"%>