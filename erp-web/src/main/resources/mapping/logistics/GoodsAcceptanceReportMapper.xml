<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.GoodsAcceptanceReportMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.GoodsAcceptanceReport">
    <!--@mbg.generated-->
    <!--@Table T_GOODS_ACCEPTANCE_REPORT-->
    <id column="GOODS_ACCEPTANCE_REPORT_ID" jdbcType="BIGINT" property="goodsAcceptanceReportId" />
    <result column="TYPE" jdbcType="BOOLEAN" property="type" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="PACKAGING" jdbcType="VARCHAR" property="packaging" />
    <result column="APPEARANCE" jdbcType="VARCHAR" property="appearance" />
    <result column="PERFORMANCE" jdbcType="VARCHAR" property="performance" />
    <result column="BACK_REASON" jdbcType="VARCHAR" property="backReason" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    GOODS_ACCEPTANCE_REPORT_ID, `TYPE`, ORDER_NO, SKU, GOODS_ID, PACKAGING, APPEARANCE, 
    PERFORMANCE, BACK_REASON, DESCRIPTION, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, 
    MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_GOODS_ACCEPTANCE_REPORT
    where GOODS_ACCEPTANCE_REPORT_ID = #{goodsAcceptanceReportId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_GOODS_ACCEPTANCE_REPORT
    where GOODS_ACCEPTANCE_REPORT_ID = #{goodsAcceptanceReportId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="GOODS_ACCEPTANCE_REPORT_ID" keyProperty="goodsAcceptanceReportId" parameterType="com.vedeng.logistics.model.GoodsAcceptanceReport" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_GOODS_ACCEPTANCE_REPORT (`TYPE`, ORDER_NO, SKU, 
      GOODS_ID, PACKAGING, APPEARANCE, 
      PERFORMANCE, BACK_REASON, DESCRIPTION, 
      IS_DELETE, ADD_TIME, CREATOR, 
      CREATOR_NAME, MOD_TIME, UPDATER, 
      UPDATER_NAME, REMARK, UPDATE_REMARK
      )
    values (#{type,jdbcType=BOOLEAN}, #{orderNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{goodsId,jdbcType=INTEGER}, #{packaging,jdbcType=VARCHAR}, #{appearance,jdbcType=VARCHAR}, 
      #{performance,jdbcType=VARCHAR}, #{backReason,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="GOODS_ACCEPTANCE_REPORT_ID" keyProperty="goodsAcceptanceReportId" parameterType="com.vedeng.logistics.model.GoodsAcceptanceReport" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_GOODS_ACCEPTANCE_REPORT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="packaging != null">
        PACKAGING,
      </if>
      <if test="appearance != null">
        APPEARANCE,
      </if>
      <if test="performance != null">
        PERFORMANCE,
      </if>
      <if test="backReason != null">
        BACK_REASON,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=BOOLEAN},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="packaging != null">
        #{packaging,jdbcType=VARCHAR},
      </if>
      <if test="appearance != null">
        #{appearance,jdbcType=VARCHAR},
      </if>
      <if test="performance != null">
        #{performance,jdbcType=VARCHAR},
      </if>
      <if test="backReason != null">
        #{backReason,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.GoodsAcceptanceReport">
    <!--@mbg.generated-->
    update T_GOODS_ACCEPTANCE_REPORT
    <set>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=BOOLEAN},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="packaging != null">
        PACKAGING = #{packaging,jdbcType=VARCHAR},
      </if>
      <if test="appearance != null">
        APPEARANCE = #{appearance,jdbcType=VARCHAR},
      </if>
      <if test="performance != null">
        PERFORMANCE = #{performance,jdbcType=VARCHAR},
      </if>
      <if test="backReason != null">
        BACK_REASON = #{backReason,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where GOODS_ACCEPTANCE_REPORT_ID = #{goodsAcceptanceReportId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.GoodsAcceptanceReport">
    <!--@mbg.generated-->
    update T_GOODS_ACCEPTANCE_REPORT
    set `TYPE` = #{type,jdbcType=BOOLEAN},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      PACKAGING = #{packaging,jdbcType=VARCHAR},
      APPEARANCE = #{appearance,jdbcType=VARCHAR},
      PERFORMANCE = #{performance,jdbcType=VARCHAR},
      BACK_REASON = #{backReason,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where GOODS_ACCEPTANCE_REPORT_ID = #{goodsAcceptanceReportId,jdbcType=BIGINT}
  </update>
</mapper>