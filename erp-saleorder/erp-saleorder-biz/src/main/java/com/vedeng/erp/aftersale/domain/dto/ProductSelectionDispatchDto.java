package com.vedeng.erp.aftersale.domain.dto;

import lombok.*;

/**
 * 销售单工单下派医修帮
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductSelectionDispatchDto {
    /**
     * 产品id
     */
    private Integer productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品品牌
     */
    private String productBrand;
    /**
     * 产品型号
     */
    private String productModel;
    /**
     * 售后数量
     */
    private Integer afterSalesQuantity;
    /**
     * 售后单id
     */
    private Integer afterSalesId;
    /**
     * 取消下派原因
     */
    private String cancelReason;
    /**
     * sku
     */
    private String sku;

}
