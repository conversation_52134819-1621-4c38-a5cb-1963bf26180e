Vue.component('ui-time-table', {
    template: `
        <div class="spinner-list">
            <div class="spinner-item">
                <ul class="">
                    <li
                        v-for="t in arrY" :key="t"
                        :class="{ 'active': hour == t }"
                        @click="chooseHour(t)"
                    >{{ t }}</li>
                </ul>
            </div>
            <div class="spinner-item">
                <ul class="">
                    <li
                        v-for="t in arrMD" :key="t"
                        :class="{ 'active': minute == t }"
                        @click="chooseMinute(t)"
                        >{{ t }}</li>
                    </ul>
                </div>
                <div class="spinner-item">
                    <ul class="">
                        <li
                        v-for="t in arrMD" :key="t"
                        :class="{ 'active': second == t }"
                        @click="chooseSecond(t)"
                    >{{ t }}</li>
                </ul>
            </div>
        </div>
    `,
    props: {
        type: String,
        date: Date
    },
    data () {
        return {
            arrY: [
                '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11',
                '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'
            ],
            arrMD: [
                '00', '01', '02', '03', '04', '05', '06', '07', '08', '09',
                '10', '11', '12', '13', '14', '15', '16', '17', '18', '19',
                '20', '21', '22', '23', '24', '25', '26', '27', '28', '29',
                '30', '31', '32', '33', '34', '35', '36', '37', '38', '39',
                '40', '41', '42', '43', '44', '45', '46', '47', '48', '49',
                '50', '51', '52', '53', '54', '55', '56', '57', '58', '59',
            ]
        }
    },
    computed: {
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },
        day () {
            return this.date.getDate();
        },
        // 时
        hour () {
            let h = this.date.getHours();
            return h < 10 ? '0' + h : h
        },
        // 分
        minute () {
            let m = this.date.getMinutes();
            return m < 10 ? '0' + m : m;
        },
        // 秒
        second () {
            let s = this.date.getSeconds();
            return s < 10 ? '0' + s : s;
        },
    },
    methods: {
        chooseHour (t) {
            let date = new Date(this.year, this.month, this.day, t, this.minute, this.second);
            this.$emit('pick', date);
        },
        chooseMinute (t) { 
            let date = new Date(this.year, this.month, this.day, this.hour, t, this.second);
            this.$emit('pick', date);
        },
        chooseSecond (t) { 
            let date = new Date(this.year, this.month, this.day, this.hour, this.minute, t);
            this.$emit('pick', date);
        }
    }

})
