$(function () {
    $('.J-suggest-input').each(function () {
        var _this = this;
        new Suggest({
            el: this,
            url: page_url + $(this).data('url'),
            params: $(this).attr('name'),
            parseData: function (data) {
                return parseData[$(_this).attr('name')](data);
            }
        });
    });

    var parseData = {
        brandName: function (data) {
            var list = data.listData || [];
            var reslist = [];
            $.each(list, function (i, item) {
                reslist.push({
                    word: item.brandName,
                })
            })
            return reslist;
        }
    }

    /**
     * 关闭弹窗
     */
    $(document).click(function(){
        $('.deal-tab').hide();
    })
});

/**
 * 全选
 * @param obj
 */
function selectAll(obj) {
    if($(obj).is(":checked")){
        $("input[type='checkbox']").not(':disabled').prop("checked",true);
    }else{
        $("input[type='checkbox']").not(':disabled').prop('checked',false);
    }
}

function getIntransitStock(skuId) {
    $("#prepare_stock")
        .attr(
            'layerParams',
            '{"width":"50%","height":"60%","title":"在途列表","link":"'
            + page_url
            + '/flash/prepare/getIntransitStock.do?skuId='
            + skuId
            + '"}');
    $("#prepare_stock").click();
}

/**
 * 安全库存设置页面打开
 * @param obj
 * @param regularId
 * @param threeMonthDaysSaleNum
 */
function initSafeStockPage(obj,regularId,threeMonthDaysSaleNum){
    $("#prepare_stock")
        .attr(
            'layerParams',
            '{"width":"70%","height":"80%","title":"设置安全库存","link":"'
            + page_url
            + '/flash/prepare/initSafeStockPage.do?regularId='
            + regularId
            + '&threeMonthDaysSaleNum='
            + threeMonthDaysSaleNum
            + '"}');
    $("#prepare_stock").click();
}

/**
 * 暂不处理页面打开
 * @param obj
 * @param dealType
 * @param regularId
 * @param stock
 * @param threeMonthDaysSaleNum
 * @param intransitStock
 * @param orderStock
 * @param receiveTimes
 * @param outStockTimes
 * @param cost
 */
function initDealPrepareStockPage(obj,dealType,regularId,stock,threeMonthDaysSaleNum,intransitStock,orderStock,receiveTimes,outStockTimes,cost){
    let supplementStock = Number($(obj).parent().parent().parent().parent().siblings('.supplement-stock').text());
    $("#prepare_stock")
        .attr(
            'layerParams',
            '{"width":"70%","height":"80%","title":"暂不备货","link":"'
            + page_url
            + '/flash/prepare/initDealPrepareStockPage.do?regularId='
            + regularId
            + '&stock='
            + stock
            + '&supplementStock='
            + supplementStock
            + '&intransitStock='
            + intransitStock
            + '&orderStock='
            + orderStock
            + '&receiveTimes='
            + receiveTimes
            + '&outStockTimes='
            + outStockTimes
            + '&threeMonthDaysSaleNum='
            + threeMonthDaysSaleNum
            + '&cost='
            + cost
            + '&dealType='
            + dealType
            + '"}');
    $("#prepare_stock").click();
    hideDealStock();
}

function hideDealStock() {
    $('.deal-tab').each(function (i, v) {
        $(v).css('display', 'none');
    });
}

function dealPrepare(obj, event) {
    // 先隐藏所有
    event.stopPropagation();
    hideDealStock();
    $(obj).siblings('.deal-tab').css('display','block');
}

/**
 * 生成备货单
 * @param obj 元素
 * @param type 类型
 * @returns {boolean}
 */
function addBHOrder(obj, type){
    checkLogin();
    debugger;
    let ids = "";
    let regularIds = "";
    if (type === 'single') {
        ids = $(obj).attr('skuId');
        regularIds = $(obj).attr('regularId');
    } else {
        if($("input[name='checkOne']:checked").length == 0){
            layer.alert("产品不能为空");
            return false;

        }
        $.each($("input[name='checkOne']:checked"),function(i,n){
            ids += $(this).val() + ",";
            regularIds += $(this).attr('regularId') + ",";
        });
    }
    if (!updatePrepare(regularIds)) {
        return false;
    }
    if(ids != ""){
        //var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
        let tabTitle = $(obj).attr('tabTitle');
        if (typeof(tabTitle) == 'undefined') {
            alert('参数错误');
        } else {
            tabTitle = $.parseJSON(tabTitle);
        }
        let id = tabTitle.num;
        let name = tabTitle.title;
        let uri = tabTitle.link+"?goodsId="+ids;
        let closable = 1;
        let item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
        if (typeof(self.parent.closableTab) != 'undefined') {
            self.parent.closableTab.addTab(item);
            self.parent.closableTab.resizeMove();
           // parentWindow.find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
        }else{
            try{
                var uniqueName = uri.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                window.parent.postMessage({
                    from:'ez',
                    name: title,
                    url:uri,
                    id:"tab-"+uniqueName
                }, '*');
            }catch (e){}
        }
    }

    $('.deal-panel').click();
}

/**
 * 更新备货计划为已备货
 * @param regularIds
 * @returns {boolean}
 */
function updatePrepare(regularIds) {
    var result = true;
    $.ajax({
        url: page_url + '/flash/prepare/updatePrepare.do?regularIds='+regularIds,
        type: 'POST',
        async : false,
        success: function (data) {
        },error:function (data) {
            if(data.code==-1){
                layer.alert("更新备货计划失败！！！");
                result = false;
            }else if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                result = false;
            }
        }
    });
    return result;
}

/**
 * 导出备货计划弹出框
 * @param obj
 */
function initExportPrepareStockPage(){
    $("#prepare_stock")
        .attr(
            'layerParams',
            '{"width":"45%","height":"57%","title":"导出备货列表","link":"'
            + page_url
            + '/flash/prepare/initExportPrepareStockPage.do"}'
            );
    $("#prepare_stock").click();
}


/**
 * 配置预测安全库存系数
 * @param obj
 */
function iniForecastSafeRatioPage(){
    $("#prepare_stock")
        .attr(
            'layerParams',
            '{"width":"60%","height":"70%","title":"配置预测安全库存系数","link":"'
            + page_url
            + '/flash/prepare/iniForecastSafeRatioPage.do"}'
        );
    $("#prepare_stock").click();
}

/**
 * 配置预测安全库存系数
 */
function setForecastSafeRatio() {
    checkLogin();
    $.ajax({
        url: page_url + '/flash/prepare/setForecastSafeRatio.do',
        type: 'POST',
        dataType :'json',
        async : false,
        data:$('#forecast_safe_ratio').serialize(),
        success: function (data) {
            if(data.code == 0){
                layerPFF(window.parent.location.href);
                $("#close-layer").click();
            }else{
                layer.alert(data.message)
            }
        },error:function (data) {
            if(data.code==-1){
                layer.alert("配置预测安全库存系数失败！！！")
            }else if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }

        }
    })
}

/**
 * 安全库存日志页面
 * @param obj
 */
function initSafeStockLogPage(obj, regularId){
    $("#prepare_stock")
        .attr(
            'layerParams',
            '{"width":"70%","height":"80%","title":"安全库存日志","link":"'
            + page_url
            + '/flash/prepare/initSafeStockLogPage.do?regularId='+regularId+'"}'
        );
    $("#prepare_stock").click();
}

/**
 * 处理详情页面
 */
function initDealDetailPage(obj, regularId){
    $("#prepare_stock")
        .attr(
            'layerParams',
            '{"width":"70%","height":"80%","title":"处理详情","link":"'
            + page_url
            + '/flash/prepare/initDealDetailPage.do?regularId='+regularId+'"}'
        );
    $("#prepare_stock").click();
}
