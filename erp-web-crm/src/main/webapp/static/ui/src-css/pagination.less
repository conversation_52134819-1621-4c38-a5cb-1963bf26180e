@import (less) '../common.css';

.vd-ui-page {
    position: relative;
    display: flex;
    justify-content: flex-end;

    .vd-ui-page-list {
        display: flex;
        justify-content: center;
    }

    .vd-ui-page-jump {
        display: flex;
        margin-left: 20px;
        
        .vd-ui-input {
            width: 80px !important;
            margin-right: -1px;
            position: relative;
            z-index: 1;
            
            .vd-ui-input__inner {
                border-radius: 3px 0 0 3px;
            }
        }

        .vd-ui-button {
            border-radius: 0 3px 3px 0;
            line-height: 28px;
            padding: 0 14px;
        }
    }

    .vd-ui-page-total-txt {
        line-height: 30px;
        position: absolute;
        left: 0;
        top: 0;

        &.vd-ui-total-small {
            line-height: 29px;
        }
    }

    .vd-ui-page-item {
        width: 33px;
        height: 30px;
        text-align: center;
        line-height: 28px;
        background: @Gray-2;
        border-radius: 3px;
        box-sizing: border-box;
        border: solid 1px @Gray-7;
        margin-right: 5px;
        cursor: pointer;
        text-decoration: none;
        color: @Text-4;

        &:hover {
            background: @Gray-3;
        }

        &.vd-ui-page-active {
            background: @Blue-6;
            border-color: @Blue-6;
            color: @Text-7;
        }

        &.vd-ui-page-item-wider {
            width: auto;
            padding: 0 15px;
        }

        &:last-child {
            margin-right: 0;
        }

        .icon-app-left,
        .icon-app-right {
            vertical-align: -2px;
            font-size: 16px;
        }
    }

    .vd-ui-page-disabled {
        color: @Text-2;
        cursor: not-allowed;

        &:hover {
            background: @Gray-2;
        }
    }

    .vd-ui-page-omit {
        margin: 0 10px 0 5px;
        line-height: 30px;
    }

    .vd-ui-page-small {
        .vd-ui-page-item {
            width: 29px;
            height: 29px;
            line-height: 27px;

            &.vd-ui-page-item-wider {
                width: auto;
                padding: 0 10px;
            }
        }
    }
}