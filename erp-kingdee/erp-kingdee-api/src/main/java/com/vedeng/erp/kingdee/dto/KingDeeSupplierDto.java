package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶供应商dto  由erp实际业务转换
 * @date 2022/8/26 10:18
 */
@Getter
@Setter
@NoArgsConstructor
@WriteBackField(needBackField = {"FSupplierId"})
public class KingDeeSupplierDto extends KingDeeMqBaseDto {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * TRADER_SUPPLIER_FINANCE_ID,
     */
    private Integer traderSupplierFinanceId;

    /**
     * 供应商编号(使用erp供应商id)
     */
    @BusinessID
    private Integer fNumber;

    /**
     * 供应商内码 0：表示新增 非0：云星空系统单据ID值
     */
    @WriteBackField("FSupplierId")
    @KingDeeID
    private String fSupplierId;

    /**
     * 创建组织id
     */
    private Integer fCreateOrgId;

    /**
     * 使用组织id
     */
    private Integer fUseOrgId;

    /**
     * 供应商名称
     */
    private String fName;

    /**
     * 供应类别编码
     */
    private String fBaseInfo;

    // 2022-11-11 对接新增 start
    /**
     * 供应商类别 01 生产厂家  02 分销商
     */
    private String fSupplierClassify;

    /**
     * 银行信息
     */
    private List<KingDeeSupplierBankDto> fBankInfo;

    // 2022-11-11 对接新增 end

    @Override
    public String getFormId() {
        return KingDeeFormConstant.BD_SUPPLIER;
    }

    public KingDeeSupplierDto(KingDeeBizEnums kingDeeBizEnums, Integer fNumber, String fName) {
        super(kingDeeBizEnums);
        this.fNumber = fNumber;
        this.fName = fName;
        this.fSupplierId = "0";
        this.fCreateOrgId = KingDeeConstant.ORG_ID;
        this.fUseOrgId = KingDeeConstant.ORG_ID;
        this.fBaseInfo = "CG";
    }


}
