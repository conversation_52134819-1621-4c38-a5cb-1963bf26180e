package com.vedeng.common.validator;

import com.baidu.unbiz.fluentvalidator.ValidatorContext;
import com.baidu.unbiz.fluentvalidator.ValidatorHandler;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.Consts;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 *
 * <AUTHOR> [<EMAIL>]
 */
public class StringValidatorHandler extends ValidatorHandler<String> {

    private String errorMessage;


    public StringValidatorHandler(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public boolean validate(ValidatorContext context, String input) {
        boolean valid = StringUtils.isNotBlank(input);
        if (!valid) {
            context.addErrorMsg(errorMessage);
        }
        return valid;
    }
}
