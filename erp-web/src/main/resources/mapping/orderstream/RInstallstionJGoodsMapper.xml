<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.orderstream.aftersales.dao.RInstallstionJGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.orderstream.aftersales.model.RInstallstionJGoods">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    <id column="R_INSTALLSTION_J_GOODS_ID" jdbcType="INTEGER" property="rInstallstionJGoodsId" />
    <result column="AFTER_SALES_INSTALLSTION_ID" jdbcType="INTEGER" property="afterSalesInstallstionId" />
    <result column="AFTER_SALES_GOODS_ID" jdbcType="INTEGER" property="afterSalesGoodsId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    R_INSTALLSTION_J_GOODS_ID, AFTER_SALES_INSTALLSTION_ID, AFTER_SALES_GOODS_ID, NUM
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.orderstream.aftersales.model.RInstallstionJGoodsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_R_INSTALLSTION_J_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_R_INSTALLSTION_J_GOODS
    where R_INSTALLSTION_J_GOODS_ID = #{rInstallstionJGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    delete from T_R_INSTALLSTION_J_GOODS
    where R_INSTALLSTION_J_GOODS_ID = #{rInstallstionJGoodsId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.orderstream.aftersales.model.RInstallstionJGoodsExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    delete from T_R_INSTALLSTION_J_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <delete id="delRInstallstionJGoods" parameterType="com.vedeng.orderstream.aftersales.model.RInstallstionJGoods">
    delete from T_R_INSTALLSTION_J_GOODS
    where 1=1
    <if test="afterSalesInstallstionId != null and afterSalesInstallstionId != 0 ">
      and AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER}
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.orderstream.aftersales.model.RInstallstionJGoods">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    <selectKey keyProperty="rInstallstionJGoodsId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_R_INSTALLSTION_J_GOODS (AFTER_SALES_INSTALLSTION_ID, AFTER_SALES_GOODS_ID, 
      NUM)
    values (#{afterSalesInstallstionId,jdbcType=INTEGER}, #{afterSalesGoodsId,jdbcType=INTEGER}, 
      #{num,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.orderstream.aftersales.model.RInstallstionJGoods">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    <selectKey keyProperty="rInstallstionJGoodsId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_R_INSTALLSTION_J_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesInstallstionId != null">
        AFTER_SALES_INSTALLSTION_ID,
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesInstallstionId != null">
        #{afterSalesInstallstionId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.orderstream.aftersales.model.RInstallstionJGoodsExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    select count(*) from T_R_INSTALLSTION_J_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="getRInstallstionJGoodsList" resultType="com.vedeng.aftersales.model.RInstallstionJGoods">
    select
    <include refid="Base_Column_List" />
    from T_R_INSTALLSTION_J_GOODS
    where 1=1
    <if test="afterSalesInstallstionId != null and afterSalesInstallstionId != 0 ">
      and AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER}
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    update T_R_INSTALLSTION_J_GOODS
    <set>
      <if test="record.rInstallstionJGoodsId != null">
        R_INSTALLSTION_J_GOODS_ID = #{record.rInstallstionJGoodsId,jdbcType=INTEGER},
      </if>
      <if test="record.afterSalesInstallstionId != null">
        AFTER_SALES_INSTALLSTION_ID = #{record.afterSalesInstallstionId,jdbcType=INTEGER},
      </if>
      <if test="record.afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID = #{record.afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="record.num != null">
        NUM = #{record.num,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    update T_R_INSTALLSTION_J_GOODS
    set R_INSTALLSTION_J_GOODS_ID = #{record.rInstallstionJGoodsId,jdbcType=INTEGER},
      AFTER_SALES_INSTALLSTION_ID = #{record.afterSalesInstallstionId,jdbcType=INTEGER},
      AFTER_SALES_GOODS_ID = #{record.afterSalesGoodsId,jdbcType=INTEGER},
      NUM = #{record.num,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.orderstream.aftersales.model.RInstallstionJGoods">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    update T_R_INSTALLSTION_J_GOODS
    <set>
      <if test="afterSalesInstallstionId != null">
        AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
    </set>
    where R_INSTALLSTION_J_GOODS_ID = #{rInstallstionJGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.orderstream.aftersales.model.RInstallstionJGoods">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 15 16:55:14 CST 2021.
    -->
    update T_R_INSTALLSTION_J_GOODS
    set AFTER_SALES_INSTALLSTION_ID = #{afterSalesInstallstionId,jdbcType=INTEGER},
      AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER}
    where R_INSTALLSTION_J_GOODS_ID = #{rInstallstionJGoodsId,jdbcType=INTEGER}
  </update>
</mapper>