package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.settlement.domain.entity.CapitalBillEntity;

import java.math.BigDecimal;
import java.util.List;

import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

/**
 * <AUTHOR>
 * @date 2022/8/27 13:18
 **/
@Named("newCapitalBillMapper")
public interface CapitalBillMapper {
    /**
     * delete by primary key
     * @param capitalBillId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer capitalBillId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(CapitalBillEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(CapitalBillEntity record);

    /**
     * select by primary key
     * @param capitalBillId primary key
     * @return object by primary key
     */
    CapitalBillEntity selectByPrimaryKey(Integer capitalBillId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CapitalBillEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CapitalBillEntity record);

    int batchInsert(@Param("list") List<CapitalBillEntity> list);

    /**
     * 根据入参查询
     * @param capitalBillDetailDto 入参 ORDER_TYPE 必传
     * @return List<CapitalBillDto>
     */
    List<CapitalBillDto> getCapitalBillData(CapitalBillDetailDto capitalBillDetailDto);


    /**
     * 根据
     * @param bankBillId
     * @return
     */
    List<CapitalBillDto> findByBankBillId(@Param("bankBillId")Integer bankBillId);


    /**
     * 售后查询
     *
     * @param capitalBillDetailDto
     * @param operationType
     * @return
     */
    List<CapitalBillDto> getAfterReturnCapitalBillData(@Param("capitalBillDetailDto") CapitalBillDetailDto capitalBillDetailDto, @Param("operationType") String operationType);

    /**
     * 查询售后订单收款金额
     */
    BigDecimal getAftersaleServiceAmountBill(@Param("orderNo") String orderNo, @Param("relatedId") Integer relatedId);

    BigDecimal getAfterSalesPublicIncome(@Param("afterSalesId") Integer afterSalesId, @Param("traderName") String traderName);

    BigDecimal getSaleOrderPublicIncome(@Param("saleorderId") Integer saleorderId, @Param("traderName") String traderName);

    BigDecimal getSaleOrderTotalExpenditure(@Param("saleorderId") Integer saleorderId);

    BigDecimal getSaleOrderTotalExpenditureBalance(@Param("saleorderId") Integer saleorderId);

    Integer getFirstCapitalBillIdByBankBillId(Integer bankBillId);

    CapitalBillEntity findByCapitalBillNo(@Param("capitalBillNo") String capitalBillNo);

    List<CapitalBillDto> getReceivedAmountBySaleOrderId(@Param("orderId") Integer orderId);

    List<CapitalBillDto> findAcceptanceBill(@Param("transactionContractNo") String transactionContractNo);

}