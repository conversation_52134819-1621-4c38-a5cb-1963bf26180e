package com.vedeng.erp.finance.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.finance.domain.dto.DiscountNumberDto;
import com.vedeng.erp.finance.dto.AcceptancePayDto;
import com.vedeng.erp.finance.dto.AcceptancePayRespDto;
import com.vedeng.erp.finance.dto.AutoPayConfigDto;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.finance.facade.PayApplyFacade;
import com.vedeng.erp.finance.service.PayApplyApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 付款计划数据接口
 * @date 2022/8/26 16:46
 **/
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/payApply/api")
public class PayApplyApi {

    @Autowired
    private PayApplyApiService payApplyApiService;

    @Autowired
    private PayApplyFacade payApplyFacade;


    /**
     * 查询开票申请
     * @param payApplyDto
     * @return
     */
    @RequestMapping("/payApplyData")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> payApplyData(PayApplyDto payApplyDto) {

        return R.success(payApplyApiService.getPayAppyListByParam(payApplyDto));
    }

    /**
     * 查询付款配置信息
     */
    @RequestMapping("/paymentAllocationInfo")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> paymentAllocationInfo() {
        return R.success(payApplyApiService.paymentAllocationInfo());
    }

    /**
     * 付款配置信息提交
     */
    @RequestMapping("/paymentAllocationSubmit")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> paymentAllocationSubmit(@RequestBody AutoPayConfigDto autoPayConfigDto) {
        payApplyApiService.paymentAllocationSubmit(autoPayConfigDto);
        return R.success();
    }

    /**
     * 承兑信息制单
     */
    @RequestMapping("/acceptancePay")
    @ResponseBody
    public R<List<AcceptancePayRespDto>> acceptancePay(@RequestBody AcceptancePayDto acceptancePayDto) {
        List<AcceptancePayRespDto> list = payApplyFacade.acceptancePay(acceptancePayDto);
        return R.success(list);
    }

    /**
     * 获取银承中收减免审批单号查询
     */
    @RequestMapping("/getDiscountNumber")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<DiscountNumberDto>> getDiscountNumber(BigDecimal amount) {
        List<DiscountNumberDto> discountNumberDtoList = payApplyFacade.getDiscountNumber(amount);
        return R.success(discountNumberDtoList);
    }

}
