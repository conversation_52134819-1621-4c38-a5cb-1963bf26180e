package com.vedeng.erp.buyorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail;
import com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchInfo;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchInfoDto;
import com.vedeng.erp.buyorder.mapper.ExpressDetailMapper;
import com.vedeng.erp.buyorder.mapper.PurchaseDeliveryDirectBatchDetailMapper;
import com.vedeng.erp.buyorder.mapper.PurchaseDeliveryDirectBatchInfoMapper;
import com.vedeng.erp.buyorder.service.PurchaseDeliveryBatchInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PurchaseDeliveryBatchInfoServiceImpl implements PurchaseDeliveryBatchInfoService {

    @Resource
    private ExpressDetailMapper buyorderExpressDetailMapper;

    @Resource
    private com.vedeng.logistics.dao.ExpressDetailMapper expressDetailMapper;

    @Resource
    private PurchaseDeliveryDirectBatchDetailMapper purchaseDeliveryDirectBatchDetailMapper;

    @Resource
    private PurchaseDeliveryDirectBatchInfoMapper purchaseDeliveryDirectBatchInfoMapper;

    @Override
    public Integer verifyIsShowDeliveryDirectBtn(List<Integer> buyorderGoodsVoIds) {
        for(Integer buyorderGoodsId : buyorderGoodsVoIds){
            List<com.vedeng.logistics.model.ExpressDetail> expressDetailList = expressDetailMapper.queryByRelateAndType(buyorderGoodsId, SysOptionConstant.ID_515);
            for(com.vedeng.logistics.model.ExpressDetail expressDetail : expressDetailList){
                //需要录入批次信息数量
                Integer needNum = expressDetail.getNum();
                //已录入同行单批次信息数量
                Integer alreadyNum = 0;
                List<PurchaseDeliveryDirectBatchDetail> purchaseDeliveryDirectBatchDetails = purchaseDeliveryDirectBatchDetailMapper.queryInfoByExpressDetailId(expressDetail.getExpressDetailId());
                for(PurchaseDeliveryDirectBatchDetail purchaseDeliveryDirectBatchDetail : purchaseDeliveryDirectBatchDetails){
                    alreadyNum = alreadyNum + purchaseDeliveryDirectBatchDetail.getArrivalCount();
                }
                if(needNum > alreadyNum){
                    return ErpConst.ONE;
                }
            }
        }
        return ErpConst.ZERO;
    }

    @Override
    public PurchaseDeliveryBatchInfoDto queryByPrimarykey(Integer purchaseDeliveryDirectBatchInfoId) {
        PurchaseDeliveryDirectBatchInfo purchaseDeliveryDirectBatchInfo = purchaseDeliveryDirectBatchInfoMapper.selectByPrimaryKey(purchaseDeliveryDirectBatchInfoId);
        PurchaseDeliveryBatchInfoDto purchaseDeliveryBatchInfoDto = new PurchaseDeliveryBatchInfoDto();
        BeanUtil.copyProperties(purchaseDeliveryDirectBatchInfo,purchaseDeliveryBatchInfoDto);
        return purchaseDeliveryBatchInfoDto;
    }


    @Override
    public List<PurchaseDeliveryBatchInfoDto> queryPurchaseDeliveryInfoByBuyorderId(Integer buyorderId) {
        return purchaseDeliveryDirectBatchInfoMapper.queryPurchaseDeliveryInfoByBuyorderId(buyorderId);
    }
}
