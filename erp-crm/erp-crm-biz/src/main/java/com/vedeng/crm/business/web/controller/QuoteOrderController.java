package com.vedeng.crm.business.web.controller;

import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ExceptionController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

/**
 * 报价单详情
 */
@ExceptionController
@Controller
@RequestMapping("/crm/quoter/profile")
@Slf4j
public class QuoteOrderController extends BaseController {

    @Value("${autoRefreshMin:10}")
    private Integer autoRefreshMin;

    /**
     * 报价单详情
     * @return 页面
     */
    @RequestMapping(value = "/index")
    public String index(HttpServletRequest request) {
        request.getSession().getAttribute("current_user");
        request.setAttribute("autoRefreshMin", autoRefreshMin);
        return "/vue/view/crm/profile/quoteorder/index";
    }
}
