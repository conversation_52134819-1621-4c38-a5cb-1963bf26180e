<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="title" value="标记发票状态" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<div class="formpublic">
    <form method="post" id="editOrderRatioEdit">
        <ul>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>发票状态</lable>
                </div>

                <div class="f_left">
                    <div class="form-blanks">
                        <ul>
                            <li>
                                <input type="radio" name="invoiceStatus" checked value="1">
                                <label>采购发票</label>
                            </li>
                            <li>
                                <input type="radio" name="invoiceStatus" value="5">
                                <label>费用票</label>
                            </li>
                            <li>
                                <input type="radio" name="invoiceStatus" value="8">
                                <label>其他</label>
                            </li>
                            <c:if test="${typeFlag == 2}">
                                <li>
                                    <input type="radio" name="invoiceStatus" value="9">
                                    <label>采购退票</label>
                                </li>
                            </c:if>
                        </ul>
                    </div>
                </div>
            </li>
        </ul>
        <br>
        <div class="add-tijiao tcenter">
            <button type="button" class="bg-light-blue"
                    onclick="saveHxInvoiceStatus('${hxInvoiceId}','${hxInvoiceIds}')">确定
            </button>
            <button class="dele" type="button" onclick="closeMyLayer()">取消</button>
        </div>
    </form>
</div>
<script>
    /**
     * 更新航信发票的状态
     * @param hxInvoiceId 航信发票的ID
     * @param invoiceStatus 航信发票的状态
     */
    function saveHxInvoiceStatus(hxInvoiceId, hxInvoiceIds) {
        var invoiceStatus = $("input[name='invoiceStatus']:checked").val();
        var str = '';
        if (invoiceStatus == 1) {
            str = '采购发票';
        } else if (invoiceStatus == 5) {
            str = '费用票';
        } else if (invoiceStatus == 9) {
            str = '采购退票';
        } else {
            str = '其他';
        }
        layer.confirm('确定将发票标记为' + str + '吗？', {title: '标记发票状态'}, function (index) {
            var url = '';
            if (hxInvoiceId == null || hxInvoiceId == undefined || hxInvoiceId == '') {
                url = '/supplyChain/invoice/batchSaveHxInvoiceStatus.do';
            } else {
                url = '/supplyChain/invoice/saveHxInvoiceStatus.do';
            }
            $.ajax({
                url: url,
                data: {
                    hxInvoiceId: hxInvoiceId,
                    hxInvoiceIds: hxInvoiceIds,
                    invoiceStatus: invoiceStatus
                },
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        layer.alert('标记成功', function () {
                            window.parent.location.reload();
                        });
                    } else {
                        layer.alert('操作失败');
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }, function () {
            layer.close(index);
        })
    }
    function closeMyLayer() {
        layer.closeAll();
        parent.layer.closeAll();
    }
</script>
<%@ include file="../../common/footer.jsp" %>