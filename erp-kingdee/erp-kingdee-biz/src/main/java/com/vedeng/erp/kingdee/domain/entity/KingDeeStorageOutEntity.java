package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;

import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 其他出库
 */
@Getter
@Setter
@Table(name = "KING_DEE_STORAGE_OUT")
public class KingDeeStorageOutEntity extends BaseEntity {

    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 单据内码 0：表示新增 非0：云星空系统单据FID值，表示修改
     */
    private String fId;

    /**
     * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
     */
    private String fBillNo;

    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String fQzokBddjtId;

    /**
     * 库存方向 如果是入库，则默认值 ：GENERAL
     */
    private String fStockDirect;

    /**
     * 单据日期 格式yyyy-MM-dd
     */
    private String fDate;

    /**
     * 单据类型 填单据类型编码，默认QTCKD01_SYS
     */
    private String fBillTypeId;

    /**
     * 库存组织 填写组织编码 默认101,配置化
     */
    private String fStockOrgId;

    /**
     * 客户 填写客户编码
     */
    private String fCustId;

    /**
     * 部门 填写部门编码，默认值 ：BM9999
     */
    private String fDeptId;

    /**
     * 明细
     */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntity;
}