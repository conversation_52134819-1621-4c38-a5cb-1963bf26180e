package com.wms.model.po;

import com.vedeng.common.util.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName TraderLogical.java
 * @Description TODO 客户指定逻辑仓
 * @createTime 2021年07月14日 17:23:00
 */
public class TraderLogical {

    private HashMap<String,Set<Integer>> logcailTraderMap;

    private HashMap<Integer,String> traderIdLogicalMap;

    public HashMap<String, Set<Integer>> getLogcailTraderMap() {
        return logcailTraderMap;
    }

    public void setLogcailTraderMap(HashMap<String, Set<Integer>> logcailTraderMap) {
        this.logcailTraderMap = logcailTraderMap;
    }

    public HashMap<Integer, String> getTraderIdLogicalMap() {
        return traderIdLogicalMap;
    }

    public void setTraderIdLogicalMap(HashMap<Integer, String> traderIdLogicalMap) {
        this.traderIdLogicalMap = traderIdLogicalMap;
    }



}
