package com.vedeng.goods.model;

import java.math.BigDecimal;

public class GoodsExtendGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.GOODS_EXTEND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer goodsExtendId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.GOODS_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer goodsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.CUSTOMER_NAMES
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String customerNames;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.SELLING_WORDS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String sellingWords;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.MARKET_STRATEGY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String marketStrategy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.PROMOTION_POLICY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String promotionPolicy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.WARRANTY_PERIOD
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String warrantyPeriod;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.WARRANTY_PERIOD_RULE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String warrantyPeriodRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.WARRANTY_REPAIR_FEE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private BigDecimal warrantyRepairFee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.RESPONSE_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String responseTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.HAVE_STANDBY_MACHINE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer haveStandbyMachine;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.CONDITIONS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String conditions;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.EXTENDED_WARRANTY_FEE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String extendedWarrantyFee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.IS_REFUND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer isRefund;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.EXCHANGE_CONDITIONS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String exchangeConditions;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.EXCHANGE_MODE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String exchangeMode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.FREIGHT_DESCRIPTION
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String freightDescription;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.DELIVERY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String delivery;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.FUTURES_DELIVERY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String futuresDelivery;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.TRANSPORT_REQUIREMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String transportRequirements;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.TRANSPORT_WEIGHT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String transportWeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.IS_HVAE_FREIGHT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer isHvaeFreight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.TRANSPORTATION_COMPLETION_STANDARD
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer transportationCompletionStandard;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.ACCEPTANCE_NOTICE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String acceptanceNotice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.PACKING_NUMBER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String packingNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.PACKING_QUANTITY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String packingQuantity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.ADVANTAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String advantage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_EXTEND.COMPETITIVE_ANALYSIS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String competitiveAnalysis;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.GOODS_EXTEND_ID
     *
     * @return the value of T_GOODS_EXTEND.GOODS_EXTEND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getGoodsExtendId() {
        return goodsExtendId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.GOODS_EXTEND_ID
     *
     * @param goodsExtendId the value for T_GOODS_EXTEND.GOODS_EXTEND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setGoodsExtendId(Integer goodsExtendId) {
        this.goodsExtendId = goodsExtendId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.GOODS_ID
     *
     * @return the value of T_GOODS_EXTEND.GOODS_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getGoodsId() {
        return goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.GOODS_ID
     *
     * @param goodsId the value for T_GOODS_EXTEND.GOODS_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.CUSTOMER_NAMES
     *
     * @return the value of T_GOODS_EXTEND.CUSTOMER_NAMES
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getCustomerNames() {
        return customerNames;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.CUSTOMER_NAMES
     *
     * @param customerNames the value for T_GOODS_EXTEND.CUSTOMER_NAMES
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCustomerNames(String customerNames) {
        this.customerNames = customerNames;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.SELLING_WORDS
     *
     * @return the value of T_GOODS_EXTEND.SELLING_WORDS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getSellingWords() {
        return sellingWords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.SELLING_WORDS
     *
     * @param sellingWords the value for T_GOODS_EXTEND.SELLING_WORDS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSellingWords(String sellingWords) {
        this.sellingWords = sellingWords;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.MARKET_STRATEGY
     *
     * @return the value of T_GOODS_EXTEND.MARKET_STRATEGY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getMarketStrategy() {
        return marketStrategy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.MARKET_STRATEGY
     *
     * @param marketStrategy the value for T_GOODS_EXTEND.MARKET_STRATEGY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setMarketStrategy(String marketStrategy) {
        this.marketStrategy = marketStrategy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.PROMOTION_POLICY
     *
     * @return the value of T_GOODS_EXTEND.PROMOTION_POLICY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getPromotionPolicy() {
        return promotionPolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.PROMOTION_POLICY
     *
     * @param promotionPolicy the value for T_GOODS_EXTEND.PROMOTION_POLICY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setPromotionPolicy(String promotionPolicy) {
        this.promotionPolicy = promotionPolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.WARRANTY_PERIOD
     *
     * @return the value of T_GOODS_EXTEND.WARRANTY_PERIOD
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getWarrantyPeriod() {
        return warrantyPeriod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.WARRANTY_PERIOD
     *
     * @param warrantyPeriod the value for T_GOODS_EXTEND.WARRANTY_PERIOD
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setWarrantyPeriod(String warrantyPeriod) {
        this.warrantyPeriod = warrantyPeriod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.WARRANTY_PERIOD_RULE
     *
     * @return the value of T_GOODS_EXTEND.WARRANTY_PERIOD_RULE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getWarrantyPeriodRule() {
        return warrantyPeriodRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.WARRANTY_PERIOD_RULE
     *
     * @param warrantyPeriodRule the value for T_GOODS_EXTEND.WARRANTY_PERIOD_RULE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setWarrantyPeriodRule(String warrantyPeriodRule) {
        this.warrantyPeriodRule = warrantyPeriodRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.WARRANTY_REPAIR_FEE
     *
     * @return the value of T_GOODS_EXTEND.WARRANTY_REPAIR_FEE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public BigDecimal getWarrantyRepairFee() {
        return warrantyRepairFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.WARRANTY_REPAIR_FEE
     *
     * @param warrantyRepairFee the value for T_GOODS_EXTEND.WARRANTY_REPAIR_FEE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setWarrantyRepairFee(BigDecimal warrantyRepairFee) {
        this.warrantyRepairFee = warrantyRepairFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.RESPONSE_TIME
     *
     * @return the value of T_GOODS_EXTEND.RESPONSE_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getResponseTime() {
        return responseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.RESPONSE_TIME
     *
     * @param responseTime the value for T_GOODS_EXTEND.RESPONSE_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setResponseTime(String responseTime) {
        this.responseTime = responseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.HAVE_STANDBY_MACHINE
     *
     * @return the value of T_GOODS_EXTEND.HAVE_STANDBY_MACHINE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getHaveStandbyMachine() {
        return haveStandbyMachine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.HAVE_STANDBY_MACHINE
     *
     * @param haveStandbyMachine the value for T_GOODS_EXTEND.HAVE_STANDBY_MACHINE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setHaveStandbyMachine(Integer haveStandbyMachine) {
        this.haveStandbyMachine = haveStandbyMachine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.CONDITIONS
     *
     * @return the value of T_GOODS_EXTEND.CONDITIONS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getConditions() {
        return conditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.CONDITIONS
     *
     * @param conditions the value for T_GOODS_EXTEND.CONDITIONS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setConditions(String conditions) {
        this.conditions = conditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.EXTENDED_WARRANTY_FEE
     *
     * @return the value of T_GOODS_EXTEND.EXTENDED_WARRANTY_FEE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getExtendedWarrantyFee() {
        return extendedWarrantyFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.EXTENDED_WARRANTY_FEE
     *
     * @param extendedWarrantyFee the value for T_GOODS_EXTEND.EXTENDED_WARRANTY_FEE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setExtendedWarrantyFee(String extendedWarrantyFee) {
        this.extendedWarrantyFee = extendedWarrantyFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.IS_REFUND
     *
     * @return the value of T_GOODS_EXTEND.IS_REFUND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getIsRefund() {
        return isRefund;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.IS_REFUND
     *
     * @param isRefund the value for T_GOODS_EXTEND.IS_REFUND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setIsRefund(Integer isRefund) {
        this.isRefund = isRefund;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.EXCHANGE_CONDITIONS
     *
     * @return the value of T_GOODS_EXTEND.EXCHANGE_CONDITIONS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getExchangeConditions() {
        return exchangeConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.EXCHANGE_CONDITIONS
     *
     * @param exchangeConditions the value for T_GOODS_EXTEND.EXCHANGE_CONDITIONS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setExchangeConditions(String exchangeConditions) {
        this.exchangeConditions = exchangeConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.EXCHANGE_MODE
     *
     * @return the value of T_GOODS_EXTEND.EXCHANGE_MODE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getExchangeMode() {
        return exchangeMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.EXCHANGE_MODE
     *
     * @param exchangeMode the value for T_GOODS_EXTEND.EXCHANGE_MODE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setExchangeMode(String exchangeMode) {
        this.exchangeMode = exchangeMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.FREIGHT_DESCRIPTION
     *
     * @return the value of T_GOODS_EXTEND.FREIGHT_DESCRIPTION
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getFreightDescription() {
        return freightDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.FREIGHT_DESCRIPTION
     *
     * @param freightDescription the value for T_GOODS_EXTEND.FREIGHT_DESCRIPTION
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setFreightDescription(String freightDescription) {
        this.freightDescription = freightDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.DELIVERY
     *
     * @return the value of T_GOODS_EXTEND.DELIVERY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getDelivery() {
        return delivery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.DELIVERY
     *
     * @param delivery the value for T_GOODS_EXTEND.DELIVERY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDelivery(String delivery) {
        this.delivery = delivery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.FUTURES_DELIVERY
     *
     * @return the value of T_GOODS_EXTEND.FUTURES_DELIVERY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getFuturesDelivery() {
        return futuresDelivery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.FUTURES_DELIVERY
     *
     * @param futuresDelivery the value for T_GOODS_EXTEND.FUTURES_DELIVERY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setFuturesDelivery(String futuresDelivery) {
        this.futuresDelivery = futuresDelivery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.TRANSPORT_REQUIREMENTS
     *
     * @return the value of T_GOODS_EXTEND.TRANSPORT_REQUIREMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getTransportRequirements() {
        return transportRequirements;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.TRANSPORT_REQUIREMENTS
     *
     * @param transportRequirements the value for T_GOODS_EXTEND.TRANSPORT_REQUIREMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setTransportRequirements(String transportRequirements) {
        this.transportRequirements = transportRequirements;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.TRANSPORT_WEIGHT
     *
     * @return the value of T_GOODS_EXTEND.TRANSPORT_WEIGHT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getTransportWeight() {
        return transportWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.TRANSPORT_WEIGHT
     *
     * @param transportWeight the value for T_GOODS_EXTEND.TRANSPORT_WEIGHT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setTransportWeight(String transportWeight) {
        this.transportWeight = transportWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.IS_HVAE_FREIGHT
     *
     * @return the value of T_GOODS_EXTEND.IS_HVAE_FREIGHT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getIsHvaeFreight() {
        return isHvaeFreight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.IS_HVAE_FREIGHT
     *
     * @param isHvaeFreight the value for T_GOODS_EXTEND.IS_HVAE_FREIGHT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setIsHvaeFreight(Integer isHvaeFreight) {
        this.isHvaeFreight = isHvaeFreight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.TRANSPORTATION_COMPLETION_STANDARD
     *
     * @return the value of T_GOODS_EXTEND.TRANSPORTATION_COMPLETION_STANDARD
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getTransportationCompletionStandard() {
        return transportationCompletionStandard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.TRANSPORTATION_COMPLETION_STANDARD
     *
     * @param transportationCompletionStandard the value for T_GOODS_EXTEND.TRANSPORTATION_COMPLETION_STANDARD
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setTransportationCompletionStandard(Integer transportationCompletionStandard) {
        this.transportationCompletionStandard = transportationCompletionStandard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.ACCEPTANCE_NOTICE
     *
     * @return the value of T_GOODS_EXTEND.ACCEPTANCE_NOTICE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getAcceptanceNotice() {
        return acceptanceNotice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.ACCEPTANCE_NOTICE
     *
     * @param acceptanceNotice the value for T_GOODS_EXTEND.ACCEPTANCE_NOTICE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAcceptanceNotice(String acceptanceNotice) {
        this.acceptanceNotice = acceptanceNotice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.PACKING_NUMBER
     *
     * @return the value of T_GOODS_EXTEND.PACKING_NUMBER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getPackingNumber() {
        return packingNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.PACKING_NUMBER
     *
     * @param packingNumber the value for T_GOODS_EXTEND.PACKING_NUMBER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setPackingNumber(String packingNumber) {
        this.packingNumber = packingNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.PACKING_QUANTITY
     *
     * @return the value of T_GOODS_EXTEND.PACKING_QUANTITY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getPackingQuantity() {
        return packingQuantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.PACKING_QUANTITY
     *
     * @param packingQuantity the value for T_GOODS_EXTEND.PACKING_QUANTITY
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setPackingQuantity(String packingQuantity) {
        this.packingQuantity = packingQuantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.ADVANTAGE
     *
     * @return the value of T_GOODS_EXTEND.ADVANTAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getAdvantage() {
        return advantage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.ADVANTAGE
     *
     * @param advantage the value for T_GOODS_EXTEND.ADVANTAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAdvantage(String advantage) {
        this.advantage = advantage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_EXTEND.COMPETITIVE_ANALYSIS
     *
     * @return the value of T_GOODS_EXTEND.COMPETITIVE_ANALYSIS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getCompetitiveAnalysis() {
        return competitiveAnalysis;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_EXTEND.COMPETITIVE_ANALYSIS
     *
     * @param competitiveAnalysis the value for T_GOODS_EXTEND.COMPETITIVE_ANALYSIS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCompetitiveAnalysis(String competitiveAnalysis) {
        this.competitiveAnalysis = competitiveAnalysis;
    }
}