package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;


/**
 * Table: T_TAX_CLASSIFICATION
 */
@Data
public class TaxClassificationEntity extends BaseEntity {
    /**
     * Column: TAX_CLASSIFICATION_ID
     * Type: INT UNSIGNED
     * Remark: 主键
     */
    private Integer taxClassificationId;

    /**
     * Column: CODE
     * Type: VARCHAR(64)
     * Remark: 税收编码
     */
    private String code;

    /**
     * Column: NAME
     * Type: VARCHAR(255)
     * Remark: 货物和劳务名称
     */
    private String name;

    /**
     * Column: SIMPLE_NAME
     * Type: VARCHAR(128)
     * Remark: 简称
     */
    private String simpleName;

    /**
     * Column: TAX_RATE
     * Type: VARCHAR(64)
     * Remark: 税率，逗号(,)分隔
     */
    private String taxRate;

    /**
     * Column: IS_SUMMARY_ITEM
     * Type: VARCHAR(32)
     * Default value: N
     * Remark: 是否汇总项: Y N
     */
    private String isSummaryItem;

    /**
     * Column: IS_DELETE
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * Column: UPDATE_REMARK
     * Type: VARCHAR(255)
     * Remark: 更新备注
     */
    private String updateRemark;

    /**
     * Column: DESCRIPTION
     * Type: TEXT
     * Remark: 说明
     */
    private String description;

    /**
     * Column: KEYWORDS
     * Type: TEXT
     * Remark: 关键字
     */
    private String keywords;

}