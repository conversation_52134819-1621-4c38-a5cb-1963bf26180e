package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSalesInstallstion;
import com.vedeng.aftersales.model.vo.AfterSalesInstallstionVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface AfterSalesInstallstionMapper {
    int deleteByPrimaryKey(Integer afterSalesInstallstionId);

    int insert(AfterSalesInstallstion afterSalesInstallstion);

    AfterSalesInstallstion selectByPrimaryKey(Integer afterSalesInstallstionId);

    int update(AfterSalesInstallstion afterSalesInstallstion);

    /**
     * <b>Description:</b><br> 获取工程师安调次数
     *
     * @param engineerId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月25日 下午2:33:55
     */
    Integer getEngineerInstallstionCount(@Param("engineerId") Integer engineerId);

    /**
     * <b>Description:</b><br> 获取工程师安调订单（分页）
     *
     * @param map
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月26日 上午9:47:31
     */
    List<AfterSalesInstallstionVo> getInstallstionByEngineerListPage(Map<String, Object> map);

    /**
     * <b>Description:</b><br> 获取工程师服务评价信息
     *
     * @param engineerId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月26日 下午3:09:12
     */
    AfterSalesInstallstionVo getScoreInfoByEngineer(@Param("engineerId") Integer engineerId);

    /**
     * <b>Description:</b><br> 查询工程师的列表
     *
     * @param afterSalesInstallstion
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月24日 上午10:55:19
     */
    List<AfterSalesInstallstionVo> getAfterSalesInstallstionVoByParam(AfterSalesInstallstion afterSalesInstallstion);

    /**
     * <b>Description:</b><br>
     *
     * @param engineerId
     * @return
     * @Note <b>Author:</b> Bill
     * <br><b>Date:</b> 2018年5月10日 下午7:22:51
     */
    List<AfterSalesInstallstionVo> getAfterSalesPayStatus(@Param("afterSalesId") Integer afterSalesId, @Param("engineerId") Integer engineerId);

    /**
     * <b>Description:</b><br> 查询安调信息
     *
     * @param afterSalesInstallstion
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月24日 上午10:55:19
     */
    AfterSalesInstallstionVo getEditAfterSalesInstallstionVo(AfterSalesInstallstionVo afterSalesInstallstion);

    /**
     * <b>Description:</b><br> 查询安调信息
     *
     * @param afterSalesInstallstion
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月24日 上午11:20:01
     */
    List<AfterSalesInstallstion> getSafetyListByParam(AfterSalesInstallstion afterSalesInstallstion);

    /**
     * <b>Description:</b><br> 获取待同步售后安调数据
     *
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年12月25日 下午1:21:27
     */
    List<AfterSalesInstallstion> getWaitSyncAfterSalesInstallstionList();

    /**
     * <b>Description:</b><br> 获取安调信息（派单通知用）
     *
     * @param afterSalesInstallstionVo
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2018年1月29日 下午1:14:59
     */
    AfterSalesInstallstionVo getInstallstionInfo(AfterSalesInstallstionVo afterSalesInstallstionVo);

    /**
     * <b>Description:</b><br> 获取售后
     *
     * @param afterSalesId
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2018年9月10日 下午4:01:30
     */
    BigDecimal getAfterSalesInstallstionAmount(@Param("afterSalesId") Integer afterSalesId);
}