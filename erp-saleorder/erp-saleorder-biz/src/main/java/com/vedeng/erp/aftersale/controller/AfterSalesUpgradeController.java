package com.vedeng.erp.aftersale.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.vedeng.activiti.model.AssigneeVo;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesCost;
import com.vedeng.aftersales.model.AfterSalesRecord;
import com.vedeng.aftersales.model.AfterSalesTrader;
import com.vedeng.aftersales.model.vo.*;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.*;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.aftersale.buzlogic.create.AfterSalesBuzLogic;
import com.vedeng.erp.aftersale.buzlogic.create.Impl.HhAfterSalesBuzLogic;
import com.vedeng.erp.aftersale.buzlogic.create.Impl.ThAfterSalesBuzLogic;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesRefundInfoDto;
import com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity;
import com.vedeng.erp.aftersale.mapper.AfterSaleAuditInfoMapper;
import com.vedeng.erp.aftersale.mapper.AfterSalesGoodsBizMapper;
import com.vedeng.erp.aftersale.service.*;
import com.vedeng.erp.common.constants.AfterSalesConstants;
import com.vedeng.erp.common.Constants;
import com.vedeng.erp.finance.api.CustomerAccountApiService;
import com.vedeng.erp.finance.dto.CustomerAccountReqDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.mobile.dto.AfterSalesGoodsListResultDto;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.erp.saleorder.service.AfterSaleAuditInfoService;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.service.InvoiceAfterService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.LendOut;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.AutoExpenseOrWarnService;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.order.service.ReturnVisitRecordService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.orderstream.aftersales.model.ReturnVisitRecord;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.constant.CancelReasonConstant;
import com.wms.service.CancelTypeService;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import net.sf.json.JSONObject;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 售后订单流升级
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.orderstream.aftersales.controller <br>
 * <b>ClassName:</b> AftersalesUpgradeController <br>
 * <b>Date:</b> 2021/10/8 17:07 <br>
 */
@Controller
@RequestMapping("/order/aftersalesUpgrade")
public class AfterSalesUpgradeController extends BaseController {
    public static Logger logger = LoggerFactory.getLogger(AfterSalesUpgradeController.class);

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    private RegionService regionService;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private AfterSalesBuzLogic afterSalesBuzLogic;

    @Autowired
    private ThAfterSalesBuzLogic thAfterSalesBuzLogic;

    @Autowired
    private HhAfterSalesBuzLogic hhAfterSalesBuzLogic;

    @Autowired
    private AfterSalesService afterSalesOrderService;

    @Autowired
    private UserService userService;

    @Autowired
    private PayApplyService payApplyService;

    @Autowired
    private TraderCustomerService traderCustomerService;
    @Autowired
    private AfterSalesGoodsBizMapper afterSalesGoodsBizMapper;

    @Autowired
    private AfterSalesService afterSalesService;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private AfterSalesUpgradeService afterSalesUpgradeService;

    @Autowired
    private AfterSalesFollowUpRecordService afterSalesFollowUpRecordService;

    @Autowired
    private AfterSalesRevenueRecordService afterSalesRevenueRecordService;

    @Autowired
    private AfterSalesExpenditureRecordService afterSalesExpenditureRecordService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private WarehouseOutService warehouseOutService;

    @Autowired
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;
    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private InvoiceAfterService invoiceAfterService;

    @Autowired
    private AfterSalesCommonService afterSalesCommonService;

    @Autowired
    private OrderInfoSyncService orderInfoSyncService;

    @Autowired
    private VedengSoapService vedengSoapService;

    @Resource
    private ReturnVisitRecordService returnVisitRecordService;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Value("${peer_list_filter_time}")
    private Long peerListFilterTime;

    @Autowired
    private AfterSalesInstallServiceRecordDetailService afterSalesInstallServiceRecordDetailService;

    @Value("${oss_http}")
    private String ossHttp;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Value(("${after_sale_special_user}"))
    private String afterSaleSpecialUser;

    @Value("${afterSaleVirtureSkuFilterTime}")
    private Long afterSaleVirtureSkuFilterTime;

    @Autowired
    private AfterSaleAuditInfoMapper afterSaleAuditInfoMapper;

    @Autowired
    private AfterSaleAuditInfoService afterSaleAuditInfoService;

    @Autowired
    private ExpenseAfterSalesApiService expenseAfterSalesApiService;

    @Autowired
    private AutoExpenseOrWarnService autoExpenseOrWarnService;

    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    FullyDigitalInvoiceApiService fullyDigitalInvoiceApiService;

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;

    @Autowired
    private AfterSalesClientStatusService afterSalesClientStatusService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;
    @Autowired
    CustomerAccountApiService customerAccountApiService;


    /**
     * <b>Description:</b><br>
     * 新增售后
     *
     * @param request, saleorder
     * @return org.springframework.web.servlet.ModelAndView
     * @Note <b>Author:</b> Thor <br>
     * <b>Date:</b> 2021/10/8 17:15
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addAfterSalesPage")
    public ModelAndView addAfterSalesPage(HttpServletRequest request, Saleorder saleorder) {
        saleorder.setCompanyId(ErpConst.NJ_COMPANY_ID);
        ModelAndView mav = new ModelAndView();
        SaleorderVo sv = saleorderService.getSaleorderGoodsVoList(saleorder);

        //售后设置商品数量上限
        try {
            setAfterSaleUpLimitNum(saleorder, sv);
        } catch (Exception e) {
            logger.error("售后设置商品数量上限 error", e);
        }

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end
        newGoodsStreamChange(mav, sv);
        mav.addObject("saleorder", sv);

        // 获取退货原因
        List<SysOptionDefinition> sysList = getSysOptionDefinitionList(3306);
        mav.addObject("sysList", sysList);
        mav.addObject("domain", domain);

        if (AfterSalesProcessEnum.OTHER.getType().equals(saleorder.getFlag())) {
            mav.setViewName("orderstream/aftersales/add_afterSales_qt");
            return mav;
        }

        // VDERP-12546 【费用销采联动】【逆向】销售订单中虚拟商品非销售退货售后改造
        // 销售换货、销售安调、销售维修、技术咨询  类型售后单 屏蔽展示该销售订单中全部虚拟商品
        List<Integer> virtureSkuList = new ArrayList<>();
        // 因为要求 本功能上线后创建的销售订单，其售后类型进行处理；上线前创建的销售订单对应售后类型不做处理 所以这里设定一个上线时间来判断
        // afterSaleVirtureSkuFilterTime 在 apollo 中设置
        if(afterSaleVirtureSkuFilterTime.compareTo(sv.getAddTime()) <= 0){
            virtureSkuList = vGoodsService.getVirtureSkuIdList();
        }

        //根据不同售后类型选择页面
        Integer areaId = sv.getAreaId();
        switch (AfterSalesProcessEnum.getInstance(sv.getFlag())) {
            case AFTERSALES_TH:
                //退货
                mav.setViewName("orderstream/aftersales/add_afterSales_th");
                break;
            case AFTERSALES_HH:
                //换货
                // VDERP-12546 销售换货  屏蔽展示该销售订单中全部虚拟商品
                mav.addObject("virtureSkuList", virtureSkuList);
                mav.setViewName("orderstream/aftersales/add_afterSales_hh");
                break;
            case AFTERSALES_AT:
                //安调
            case AFTERSALES_ATY:
                //销售安调（合同安调）
            case AFTERSALES_ATN:
                //销售安调（附加服务）
                if (ObjectUtils.notEmpty(areaId)) {
                    regionInfo(mav, areaId);
                }
                // VDERP-12546 销售安调  屏蔽展示该销售订单中全部虚拟商品
                mav.addObject("virtureSkuList", virtureSkuList);
                mav.addObject("flag",sv.getFlag());
                mav.setViewName("orderstream/aftersales/add_afterSales_at");
                break;
            case AFTERSALES_WX:
                //维修
                if (ObjectUtils.notEmpty(areaId)) {
                    regionInfo(mav, areaId);
                }
                // VDERP-12546 销售维修  屏蔽展示该销售订单中全部虚拟商品
                mav.addObject("virtureSkuList", virtureSkuList);
                mav.addObject("flag",sv.getFlag());
                mav.setViewName("orderstream/aftersales/add_afterSales_atwx");
                break;
            case AFTERSALES_TP:
                mav.setViewName("orderstream/aftersales/add_afterSales_tp");
                break;
            case LOST_TICKET:
                mav.setViewName("orderstream/aftersales/add_afterSales_qp");
                break;
            case AFTERSALES_TK:
                // 获取交易信息（订单实际金额，客户已付款金额）当订单实际金额=客户已付款金额时候，不允许退款
                Map<String, BigDecimal> saleorderDataInfo = saleorderService.getSaleorderDataInfo(saleorder.getSaleorderId());
                mav.addObject("saleorderDataInfo", saleorderDataInfo);
                mav.setViewName("orderstream/aftersales/add_afterSales_tk");
                break;
            case AFTERSALES_JZ:
                // VDERP-12546 技术咨询  屏蔽展示该销售订单中全部虚拟商品
                mav.addObject("virtureSkuList", virtureSkuList);
                mav.setViewName("orderstream/aftersales/add_afterSales_jz");
                break;
            default:
                logger.info("错误的售后单类型,{}", sv.getFlag());
                break;
        }
        return mav;
    }

    /**
     * 售后设置商品数量上限(维修)
     *
     * @param saleorder
     * @param sv
     */
    private void setAfterSaleUpLimitNum(Saleorder saleorder, SaleorderVo sv) {
        if (!AfterSalesProcessEnum.AFTERSALES_WX.getType().equals(saleorder.getFlag())) {
            return;
        }
        List<SaleorderGoodsVo> saleorderGoodsVo = sv.getSgvList();
        expressService.getSEGoodsNum(saleorderGoodsVo.stream()
                .map(SaleorderGoodsVo::getSaleorderGoodsId).collect(Collectors.toList())).forEach(expressInfo -> {
            for (SaleorderGoodsVo goodsVo : sv.getSgvList()) {
                if (!expressInfo.getSaleOrderGoodsId().equals(goodsVo.getSaleorderGoodsId())) {
                    break;
                }
                goodsVo.setAfterSaleUpLimitNum(expressInfo.getSendNum());
            }
        });
    }

    /**
     * 新商品流切换
     *
     * @param mav
     * @param sv
     */
    private void newGoodsStreamChange(ModelAndView mav, SaleorderVo sv) {
        if (CollectionUtils.isEmpty(sv.getSgvList())) {
            return;
        }
        List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(sv.getSgvList()
                .stream().map(SaleorderGoodsVo::getGoodsId).collect(Collectors.toList()));

        mav.addObject("newSkuInfosMap", skuTipsMap.stream()
                .collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v)));
    }

    /**
     * <b>Description:</b><br>
     * 返回页面渲染省市区信息
     *
     * @param mav 返回页面对象 areadId 地区
     * @return void
     * @Note <b>Author:</b> Thor <br>
     * <b>Date:</b> 2021/10/9 13:17
     */
    public void regionInfo(ModelAndView mav, Integer areaId) {
        Region region = regionService.getRegionByRegionId(areaId);
        if (region.getRegionType() == 1) {// 省级
            mav.addObject("province", areaId);
        } else if (region.getRegionType() == 2) {
            List<Region> cityList = regionService.getRegionByParentId(region.getParentId());
            mav.addObject("cityList", cityList);
            mav.addObject("city", areaId);
            mav.addObject("province", region.getParentId());
        } else if (region.getRegionType() == 3) {
            List<Region> zoneList = regionService.getRegionByParentId(region.getParentId());
            mav.addObject("zoneList", zoneList);
            mav.addObject("zone", areaId);
            Region cyregion = regionService.getRegionByRegionId(zoneList.get(0).getParentId());
            List<Region> cityList = regionService.getRegionByParentId(cyregion.getParentId());
            mav.addObject("cityList", cityList);
            mav.addObject("city", region.getParentId());
            mav.addObject("province", cityList.get(0).getParentId());
        }
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mav.addObject("provinceList", provinceList);
    }

    /**
     * 保存新增的销售售后单
     *
     * @param request
     * @param afterSalesVo
     * @param afterSaleNums
     * @param fileName
     * @param fileUri
     * @param invoiceIds
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveAddAfterSales")
    @SystemControllerLog(operationType = "add", desc = "保存新增售后")
    public ModelAndView saveAddAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                          @RequestParam(required = false, value = "afterSaleNums") String[] afterSaleNums,
                                          @RequestParam(required = false, value = "fileName") String[] fileName,
                                          @RequestParam(required = false, value = "fileUri") String[] fileUri,
                                          @RequestParam(required = false, value = "invoiceIds") String[] invoiceIds) {
        User user = getSessionUser(request);
        afterSalesVo.setCompanyId(ErpConst.NJ_COMPANY_ID);
        afterSalesVo.setAfterSalesNum(afterSaleNums);
        afterSalesVo.setAttachName(fileName);
        afterSalesVo.setAttachUri(fileUri);
        // 销售
        afterSalesVo.setSubjectType(535);
        afterSalesVo.setAtferSalesStatus(ErpConst.ZERO);
        afterSalesVo.setStatus(ErpConst.ZERO);
        afterSalesVo.setValidStatus(ErpConst.ZERO);
        afterSalesVo.setDomain(domain);
        afterSalesVo.setInvoiceIds(invoiceIds);
        //本处适应第三方
        afterSalesVo.setTraderType(ErpConst.ONE);
        //订单流升级--新售后订单增加标识--0否1是
        afterSalesVo.setIsNew(ErpConst.ONE);
        if (ObjectUtils.notEmpty(afterSalesVo.getType()) &&
                (AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSalesVo.getType()) ||
                        AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSalesVo.getType()))){
            afterSalesVo.setTraderType(ErpConst.THREE);
        }
        ModelAndView mav = new ModelAndView();

        List<String> afterSaleSequences = new ArrayList<>();
        ResultInfo resultInfo ;
        switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())) {
            case AFTERSALES_TH:
                afterSaleSequences.add("cancelWms");
                afterSaleSequences.add("saveAfterSale");
                afterSaleSequences.add("putWms");
                resultInfo = thAfterSalesBuzLogic.run(afterSaleSequences, afterSalesVo, user);
                if (resultInfo != null && ErpConst.ZERO.equals(resultInfo.getCode())) {
                    this.createCustomerAccount(Integer.parseInt(resultInfo.getData().toString()));
                }
                break;
            case AFTERSALES_HH:
                //订单未全部付款前不允许创建换货单
                afterSaleSequences.add("hhVerifyAfterSales");
                afterSaleSequences.add("saveAfterSale");
                resultInfo = hhAfterSalesBuzLogic.run(afterSaleSequences, afterSalesVo, user);
                break;
            case AFTERSALES_TK:
                afterSaleSequences.add("saveAfterSale");
                resultInfo = afterSalesBuzLogic.run(afterSaleSequences, afterSalesVo, user);
                if (resultInfo != null && ErpConst.ZERO.equals(resultInfo.getCode())) {
                    this.createCustomerAccount(Integer.parseInt(resultInfo.getData().toString()));
                }
                break;
            default:
                afterSaleSequences.add("saveAfterSale");
                resultInfo = afterSalesBuzLogic.run(afterSaleSequences, afterSalesVo, user);
                break;
        }
        if (resultInfo != null && ErpConst.ZERO.equals(resultInfo.getCode())){
            mav.addObject("url", "./viewAfterSalesDetail.do?afterSalesId=" + resultInfo.getData());
            return success(mav);
        }
        mav.addObject("message", resultInfo != null ? resultInfo.getMessage() : "");
        return fail(mav);
    }

    /**
     * 创建客户账户信息，基于联行号
     */
    private void createCustomerAccount(Integer afterSalesId) {
        if (afterSalesId == null) {
            return;
        }
        logger.info("售后单创建客户账户信息，基于联行号 afterSalesId:{}", afterSalesId);
        try {
            CustomerAccountReqDto customerAccountReqDto = new CustomerAccountReqDto();
            customerAccountReqDto.setAfterSalesId(afterSalesId);
            customerAccountApiService.tryCreate(customerAccountReqDto);
        } catch (Exception e) {
            logger.error("售后单创建客户账户信息，基于联行号 异常 afterSalesId:{}", afterSalesId,e);
        }
    }


    /**
     * 售后单详情
     *
     * @param request
     * @param afterSales
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping("/viewAfterSalesDetail")
    public ModelAndView viewAfterSalesDetail(HttpServletRequest request, AfterSalesVo afterSales) {
        User user = getSessionUser(request);
        //本处适应第三方
        afterSales.setTraderType(ErpConst.ONE);

        AfterSales afterSalesInfo = adapterThirdParty(afterSales);

        afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);

        ModelAndView mav = new ModelAndView();

        //订单流之前的订单跳转
        if (afterSalesInfo != null && ErpConst.ZERO.equals(afterSalesInfo.getIsNew())){
            logger.info("售后单为订单流之前订单跳转至旧页面 afterSalesId:{}", afterSales.getAfterSalesId());
            mav.setViewName("redirect:/aftersales/order/viewAfterSalesDetail.do");
            mav.addObject("afterSalesId", afterSales.getAfterSalesId());
            mav.addObject("traderType", afterSales.getTraderType());
            return mav;
        }

        AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);

        //售后单信息相关
        try {
            setAfterSalesInfo(user, mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("售后单信息相关异常", e);
        }

        //订单SKU相关信息
        try {
            setSkuStr(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单SKU相关信息异常", e);
        }

        //新商品流切换相关信息
        try {
            newGoodsStreamChangeInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("新商品流切换相关信息", e);
        }


        //订单的费用类型以及费用列表相关
        try {
            setOrderCostInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单的费用类型以及费用列表相关异常", e);
        }

        //沟通记录相关
        try {
            setCommunicateInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("沟通记录相关异常", e);
        }

        //发票类型
        mav.addObject("invoiceTypeList", getSysOptionDefinitionList(SysOptionConstant.ID_428));


        //物流信息相关
        try {
            setExpressInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("物流信息相关异常", e);
        }

        //付款申请信息相关
        try {
            setPayApplyInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("付款申请信息相关异常", e);
        }

        //订单审核信息相关
        try {
            setOrderExamineInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("订单审核信息相关异常", e);
        }

        //订单完结信息相关
        try {
            setOrderFinishInfo(afterSales, mav);
        } catch (Exception e) {
            logger.error("订单完结信息相关异常", e);
        }

        //开票申请审核信息相关
        try {
            setInvoiceApplyInfo(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("开票申请审核信息相关异常", e);
        }

        //获取基础模块-跟进记录-支出记录-收入记录-回访记录-安调服务记录-用户端售后状态
        try{
            setBasicsInfo(user,mav,afterSalesVo);
        } catch (Exception e){
            logger.error("获取基础模块异常",e);
        }

        /**
         * 获取顶部状态栏信息
         */
        try {
            setTopStatusList(mav, afterSalesVo);
        } catch (Exception e) {
            logger.error("获取顶部状态栏信息异常", e);
        }

        /**
         * 第一责任部门转化
         */
        try{
            getFirstResponsibleDepartment(afterSalesVo);
        } catch (Exception e) {
            logger.error("获取第一责任部门异常", e);
        }

        //计算是否可关闭状态
        try {
            afterSalesUpgradeService.setIsCloseStatus(afterSalesVo);
        } catch (Exception e) {
            logger.error("setIsCloseStatus warn", e);
        }
        //判断是否显示下派给医修帮按钮和取消下派按钮
        try {
            //判断是否是医修帮公司
            Boolean medicalHelpCompany = afterSalesOrderService.isMedicalHelpCompany(afterSalesVo.getAfterSalesId());
            mav.addObject("medicalHelpCompany", medicalHelpCompany);
            afterSalesUpgradeService.setIsDispatchYXB(afterSalesVo);
        } catch (Exception e) {
            logger.error("判断是否显示下派给医修帮按钮和取消下派按钮 warn", e);
        }
        Integer afterSalesId = afterSalesVo.getAfterSalesId();
        List<AfterSalesGoodsListResultDto> afterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial = afterSalesGoodsBizMapper.getAfterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial(afterSalesId);
        if (CollUtil.isNotEmpty(afterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial)) {
            String skus = afterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial.stream().map(AfterSalesGoodsListResultDto::getSku).collect(Collectors.joining(StrUtil.COMMA));
            mav.addObject("goodIdsStr", skus);
        } else {
            mav.addObject("goodIdsStr", "");
        }
        String skus = afterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial.stream().map(AfterSalesGoodsListResultDto::getSku).collect(Collectors.joining(StrUtil.COMMA));
        mav.addObject("goodIdsStr", skus);
        switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())) {
            //销售订单退货
            case AFTERSALES_TH: {
                //设置退货入库记录展示
                afterSalesUpgradeService.setAfterSalesTHInfo(afterSalesVo);
                mav.addObject("refundAmount", afterSalesService.getHaveRefundedAmount(afterSalesVo));
                mav.setViewName("orderstream/aftersales/view_afterSales_th");

                // 付款申请按钮显隐藏规则
                List<PayApply> payApplyList = null;
                //进行中的付款申请
                if (!CollectionUtils.isEmpty(afterSalesVo.getAfterPayApplyList())) {
                    payApplyList = afterSalesVo.getAfterPayApplyList().stream().filter(item -> ErpConst.ZERO.equals(item.getValidStatus()))
                            .collect(Collectors.toList());
                    mav.addObject("payPlayList", payApplyList);
                }
                boolean shouldPayApplyButton = this.shouldPayApplyButtonTH(afterSalesVo, user, payApplyList);
                mav.addObject("shouldPayApplyButton", shouldPayApplyButton);

                break;
            }
            //销售订单换货
            case AFTERSALES_HH: {
                //设置出入库记录
                afterSalesUpgradeService.setAfterSalesHHInfo(afterSalesVo);
                mav.setViewName("orderstream/aftersales/view_afterSales_hh");
                break;
            }
            //销售订单维修
            case AFTERSALES_WX:

            // 销售订单合同安调
            case AFTERSALES_ATY:

            // 销售订单非合同安调(附加服务)
            case AFTERSALES_ATN:

            //销售订单安调
            case AFTERSALES_AT: {
                //获取产品与工程师信息
                mav.setViewName("orderstream/aftersales/view_afterSales_at");
                break;
            }
            //销售订单退票
            case AFTERSALES_TP: {
                mav.setViewName("orderstream/aftersales/view_afterSales_tp");
                break;
            }
            //销售订单退款
            case AFTERSALES_TK: {
                mav.setViewName("orderstream/aftersales/view_afterSales_tk");
                List<PayApply> payApplyList = null;
                //进行中的付款申请
                if (!CollectionUtils.isEmpty(afterSalesVo.getAfterPayApplyList())){
                    payApplyList = afterSalesVo.getAfterPayApplyList().stream().filter(item -> ErpConst.ZERO.equals(item.getValidStatus()))
                            .collect(Collectors.toList());
                    mav.addObject("payPlayList", payApplyList);
                }
                // 付款申请按钮显隐藏规则
                boolean shouldPayApplyButton = this.shouldPayApplyButtonTK(afterSalesVo, user, payApplyList);
                mav.addObject("shouldPayApplyButton", shouldPayApplyButton);

                //已退款金额计算
                mav.addObject("refundAmount", afterSalesService.getHaveRefundedAmount(afterSalesVo));


                break;
            }
            //销售订单技术咨询
            case AFTERSALES_JZ: {
                mav.setViewName("orderstream/aftersales/view_afterSales_jz");
                break;
            }
            //销售订单其他
            case OTHER: {
                mav.setViewName("orderstream/aftersales/view_afterSales_qt");
                break;
            }

            //销售订单丢票
            case LOST_TICKET: {
                mav.setViewName("orderstream/aftersales/view_afterSales_qp");
                break;
            }

            //第三方安调/维修
            case AFTERASALES_THIRD_WX:
            case AFTERASALES_THIRD_AT:{
                mav.setViewName("orderstream/aftersales/view_afterSales_third_at");
                break;
            }
            default: {
                logger.info("售后类型匹配失败 type:{},跳转至旧的售后详情页", afterSalesVo.getType());
                mav.setViewName("redirect:/aftersales/order/viewAfterSalesDetail.do");
                mav.addObject("afterSalesId", afterSales.getAfterSalesId());
                mav.addObject("traderType", afterSales.getTraderType());
                return mav;
            }
        }
        return mav;
    }

    public void getFirstResponsibleDepartment(AfterSalesVo afterSalesVo){
        if (ObjectUtils.isEmpty(afterSalesVo.getFirstResponsibleDepartmentStr()) && ObjectUtils.notEmpty(afterSalesVo.getFirstResponsibleDepartment())){
            afterSalesVo.setFirstResponsibleDepartmentStr(orgService.getOrgNameById(afterSalesVo.getFirstResponsibleDepartment()));
        }
    }


    /**
     * 判断退货是否应该显示申请退款按钮
     *
     * 命名沿用历史代码TH
     * @param afterSalesVo 售后语音对象，包含退款和金额退款状态等信息
     * @param user 用户对象，包含用户职位类型等信息
     * @param payApplyList 支付申请列表，用于判断是否有未处理的支付申请
     * @return boolean 类型，如果满足所有条件则返回 true，否则返回 false
     */
    private boolean shouldPayApplyButtonTH(AfterSalesVo afterSalesVo, User user, List<PayApply> payApplyList) {
        if (afterSalesVo == null || user == null) {
            return false;
        }
        // 判断退款状态是否为2（2退款完成）
        boolean isRefundCompleted = afterSalesVo.getRefund() != null && afterSalesVo.getRefund() == 2;

        // 判断金额退款状态是否为1或2（1和2代表金额部分退款或全部退款）
        boolean isAmountRefunded = afterSalesVo.getAmountRefundStatus() != null && (afterSalesVo.getAmountRefundStatus() == 1 || afterSalesVo.getAmountRefundStatus() == 2);

        // 判断支付申请列表是否为空或null，表示没有未处理的支付申请
        boolean noPendingPayApply = payApplyList == null || payApplyList.isEmpty();

        // 判断用户职位类型是否为特定类型 312代表售后专员
        boolean isAfterSalesSpecialist = user.getPositType() == 312;

        // 已经编辑过款项退换方式，确认过退换方式及金额
        AfterSalesRefundInfoDto afterSaleApplyInfoDto = afterSalesCommonService.refundDetail(afterSalesVo.getAfterSalesId());
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        if (afterSaleApplyInfoDto != null && afterSaleApplyInfoDto.getRefundDetailDtoList() != null) {
            totalRefundAmount = afterSaleApplyInfoDto.getRefundDetailDtoList().stream()
                    .map(AfterSalesRefundInfoDto.RefundDetailDto::getRefundAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
        }
        boolean hasRefundAmount = totalRefundAmount.compareTo(BigDecimal.ZERO) > 0;

        // 综合所有条件，如果所有条件都满足，则返回true，表示应该显示按钮
        return isRefundCompleted && isAmountRefunded && noPendingPayApply && isAfterSalesSpecialist && hasRefundAmount;
    }


    /**
     * 判断退货是否应该显示申请退款按钮
     *
     * 命名沿用历史代码TK
     * @param afterSalesVo 售后语音对象，包含退款和金额退款状态等信息
     * @param user 用户对象，包含用户职位类型等信息
     * @param payApplyList 支付申请列表，用于判断是否有未处理的支付申请
     * @return boolean 类型，如果满足所有条件则返回 true，否则返回 false
     */
    public boolean shouldPayApplyButtonTK(AfterSalesVo afterSalesVo, User user, List<PayApply> payApplyList) {
        if (afterSalesVo == null || user == null) {
            return false;
        }

        // 判断售后状态是否有效
        boolean isAfterSalesStatusValid = afterSalesVo.getAtferSalesStatus() != null && afterSalesVo.getAtferSalesStatus() == 1;

        // 判断是否有可退款金额
        boolean hasPositiveRefundableAmount = afterSalesVo.getFinalRefundableAmount() != null && afterSalesVo.getFinalRefundableAmount().compareTo(BigDecimal.ZERO) > 0;

        // 判断金额退款状态是否有效
        boolean isAmountRefundStatusValid = afterSalesVo.getAmountRefundStatus() != null && afterSalesVo.getAmountRefundStatus() != 0 && afterSalesVo.getAmountRefundStatus() != 3;

        // 判断支付申请列表是否为空
        boolean isPayPlayListEmpty = payApplyList == null || payApplyList.isEmpty();

        // 判断用户职位类型是否有效 销售 售后 运营
        boolean isUserPositTypeValid = user.getPositType() == 310 || user.getPositType() == 312 || user.getPositType() == 589;

        // 已经编辑过款项退换方式，确认过退换方式及金额
        AfterSalesRefundInfoDto afterSaleApplyInfoDto = afterSalesCommonService.refundDetail(afterSalesVo.getAfterSalesId());
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        if (afterSaleApplyInfoDto != null && afterSaleApplyInfoDto.getRefundDetailDtoList() != null) {
            totalRefundAmount = afterSaleApplyInfoDto.getRefundDetailDtoList().stream()
                    .map(AfterSalesRefundInfoDto.RefundDetailDto::getRefundAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
        }
        boolean hasRefundAmount = totalRefundAmount.compareTo(BigDecimal.ZERO) > 0;

        // 综合所有条件判断是否应该显示申请退款按钮
        return isAfterSalesStatusValid && hasPositiveRefundableAmount && isAmountRefundStatusValid && isPayPlayListEmpty && isUserPositTypeValid && hasRefundAmount;
    }

    /**
     * <b>Description:</b><br> 编辑款项退还信息
     * @param afterSalesVo
     * @return
     * @throws IOException
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月23日 上午11:30:44
     */
    @ResponseBody
    @RequestMapping(value="/editAfterSalesBackFundInfo")
    public ModelAndView editAfterSalesApplyPage(AfterSalesVo afterSalesVo) throws IOException{
        ModelAndView mav = new ModelAndView("/orderstream/aftersales/edit_afterSales_fundInfo");
        mav.addObject("afterSales", afterSalesVo);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(afterSalesVo)));
        return mav;
    }

    /**
     * 获取顶部状态栏信息
     *
     * @param mav
     * @param afterSalesVo
     */
    private void setTopStatusList(ModelAndView mav, AfterSalesVo afterSalesVo) {
        mav.addObject("topStatusList", afterSalesUpgradeService.getAfterSaleNowStatus(afterSalesVo));
        mav.addObject("lastLabelName", ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus()) ? "关闭" : "完结");
    }

    /**
     * 获取基础模块信息
     * @param mv
     * @param afterSalesVo
     */
    private void setBasicsInfo(User user,ModelAndView mv ,AfterSalesVo afterSalesVo){

        //获取部门信息
        mv.addObject("orgList", orgService.getOrgList(ErpConst.ZERO, ErpConst.NJ_COMPANY_ID, false));

        List<User> createList = userService.getAllUser(user);

        //获取创建人信息
        mv.addObject("createList",createList);

        //查询费用类型字典表子集ListID
        mv.addObject("typeList",getSysOptionDefinitionList(SysOptionConstant.AFTER_SALES_COST_TYPE));

        if (afterSalesVo.getAfterSalesId() == null){
            logger.error("afterSalesId:{}", afterSalesVo.getAfterSalesId());
            return;
        }

        // 获取用户端售后状态
        mv.addObject("clientStatusRecordList",afterSalesClientStatusService.getClientStatusRecordList(afterSalesVo.getAfterSalesId()));
        //获取售后跟进记录信息
        mv.addObject("salesFollowUpRecordList",afterSalesFollowUpRecordService.selectByAfterSalesId(afterSalesVo.getAfterSalesId()));
        //获取售后收入记录信息
        mv.addObject("afterSalesRevenueRecords",afterSalesRevenueRecordService.selectByAfterSalesId(afterSalesVo.getAfterSalesId()));
        //获取售后支出记录信息
        mv.addObject("afterSalesExpenditureRecords",afterSalesExpenditureRecordService.selectByAfterSalesId(afterSalesVo.getAfterSalesId()));

        // VDERP-8575 回访记录
        mv.addObject("returnVisitRecordList", returnVisitRecordService.getReturnVisitRecordListByAfterSaleId(afterSalesVo.getAfterSalesId()));

        // 安调服务记录
        if (afterSalesVo.getType() == 4090 || afterSalesVo.getType() == 4091 || afterSalesVo.getType() == 541 || afterSalesVo.getType() == 550) {
            mv.addObject("installServiceRecordList", afterSalesInstallServiceRecordDetailService.getInstallDetailList(afterSalesVo.getAfterSalesId()));
            mv.addObject("ossHttp", ossHttp);
        }

        //一次获取Name
        if (CollectionUtils.isNotEmpty(createList)){

            AfterSalesVo afterSales = new AfterSalesVo();

            if (ObjectUtils.isEmpty(afterSalesVo.getServiceUserName()) && ObjectUtils.notEmpty(afterSalesVo.getServiceUserId())){
                afterSales.setServiceUserId(afterSalesVo.getServiceUserId());
            }

            if (ObjectUtils.isEmpty(afterSalesVo.getCreatorName()) && ObjectUtils.notEmpty(afterSalesVo.getCreator())){
                afterSales.setCreator(afterSalesVo.getCreator());
            }

            if (ObjectUtils.isEmpty(afterSalesVo.getAfterSalesStatusUserName()) && ObjectUtils.notEmpty(afterSalesVo.getAfterSalesStatusUser())){
                afterSales.setAfterSalesStatusUser(afterSalesVo.getAfterSalesStatusUser());
            }

            createList.stream().filter(Objects::nonNull).forEach(s->{
                if(s.getUserId().equals(afterSales.getServiceUserId())){
                    afterSalesVo.setServiceUserName(s.getUsername());
                }
                if(s.getUserId().equals(afterSales.getCreator())){
                    afterSalesVo.setCreatorName(s.getUsername());
                }if(s.getUserId().equals(afterSales.getAfterSalesStatusUser())){
                    afterSalesVo.setAfterSalesStatusUserName(s.getUsername());
                }
            });

        }

    }


    /**
     * 售后单信息相关
     *
     * @param user
     * @param mav
     * @param afterSalesVo
     */
    private void setAfterSalesInfo(User user, ModelAndView mav, AfterSalesVo afterSalesVo) {
        mav.addObject("flag", afterSalesVo.getRealRefundAmount().compareTo(BigDecimal.ZERO) > 0 ? ErpConst.ONE : ErpConst.TWO);
        mav.addObject("isSupply", existOrNot(user, userService.getProductUserByRoleName()));

        //售后单生效后，销售人员不显示"关闭订单"按钮；设置afterSalesVo.setCloseStatus(2)来实现隐藏"关闭订单"按钮
        if (afterSalesVo.getValidStatus() > 0 && null != user.getOrgId() &&
                SysOptionConstant.ID_535.equals(afterSalesVo.getSubjectType()) &&
                user.getOrgId() != 10) {
            //销售
            afterSalesVo.setCloseStatus(ErpConst.TWO);
        }

        //VDERP-12089 售后退换货关闭条件增加有无关联直发采购售后单start
        if(AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesVo.getType())
                || AfterSalesProcessEnum.AFTERSALES_HH.getCode().equals(afterSalesVo.getType())){
            Integer relatedBuyVerify = afterSalesUpgradeService.verifyCloseStatusByBuyorderAfter(afterSalesVo);
            mav.addObject("relatedBuyVerify",relatedBuyVerify);
        }else {
            mav.addObject("relatedBuyVerify",ErpConst.ONE);
        }
        //VDERP-12089 售后退换货关闭条件增加有无关联直发采购售后单end

        afterSalesVo.setAfterSalesStatusResonName((getSysOptionDefinition(afterSalesVo.getAfterSalesStatusReson()).getComments()));

        PayApply payApply = new PayApply();
        payApply.setPayType(ErpConst.AFTERSALES_ORDER_TYPE);
        payApply.setRelatedId(afterSalesVo.getAfterSalesId());
        mav.addObject("payApply", payApplyService.getPayApplyMaxRecord(payApply));

        // 添加航信作废展示处理
        afterSalesOrderService.transAfterSalesVoForBlueInValid(afterSalesVo);

        //判断商品是否为赠品
        if(CollectionUtils.isNotEmpty(afterSalesVo.getAfterSalesGoodsList())){
            afterSalesVo.getAfterSalesGoodsList().forEach(good -> {
                good.setIsGift(saleorderGoodsMapper.getIsGiftBySaleorderGoodsId(good.getOrderDetailId()));
                good.setIsDirectPurchase(saleorderGoodsMapper.getIsDirectPurchaseBySaleorderGoodsId(good.getOrderDetailId()));
            });
        }

        mav.addObject("afterSalesVo", afterSalesVo);
        boolean updateRefundCheck = true;
        try {
            afterSalesCommonService.updateRefundCheck(afterSalesVo.getAfterSalesId());
        } catch (Exception e) {
//            log.info("编辑售后退款校验不通过", e);
            updateRefundCheck = false;
        }
        boolean detailRefundCheck = true;
        try {
            afterSalesCommonService.detailRefundCheck(afterSalesVo.getAfterSalesId());
        } catch (Exception e) {
//            log.info("查看售后退款校验不通过", e);
            detailRefundCheck = false;
        }
        mav.addObject("updateRefundCheck", updateRefundCheck);
        mav.addObject("detailRefundCheck", detailRefundCheck);
        mav.addObject("afterSalesVoJSon", StringEscapeUtils.escapeHtml4(JSON.toJSONString(afterSalesVo)));
        mav.addObject("user", user);
        //售后安调单设置特殊审核人
        //所有特殊用户的id,549,488
        boolean isSpecialUser= ArrayUtils.contains(StringUtils.split(afterSaleSpecialUser,","),user.getUserId().toString());
        mav.addObject("isSpecialUser",isSpecialUser?ErpConst.ONE:ErpConst.ZERO);
    }

    /**
     * 订单SKU相关信息
     *
     * @param mav
     * @param afterSalesVo
     */
    private void setSkuStr(ModelAndView mav, AfterSalesVo afterSalesVo) {
        if (CollectionUtils.isEmpty(afterSalesVo.getAfterSalesGoodsList())) {
            return;
        }
        mav.addObject("sku", Joiner.on(",")
                .join(afterSalesVo.getAfterSalesGoodsList().stream().map(AfterSalesGoodsVo::getSku)
                        .collect(Collectors.toList())));
    }

    /**
     * 新商品流切换相关信息
     *
     * @param mav
     * @param afterSalesVo
     */
    private void newGoodsStreamChangeInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        if (CollectionUtils.isEmpty(afterSalesVo.getAfterSalesGoodsList())) {
            return;
        }
        List<Integer> skuIds = CollectionUtils.isEmpty(afterSalesVo.getAfterReturnOutstockList()) ? new ArrayList<>() :
                afterSalesVo.getAfterReturnOutstockList().stream().map(WarehouseGoodsOperateLog::getGoodsId)
                        .collect(Collectors.toList());

        afterSalesVo.getAfterSalesGoodsList().forEach(saleGood -> {
            skuIds.add(saleGood.getGoodsId());
        });
        List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
        mav.addObject("newSkuInfosMap", skuTipsMap.stream()
                .collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v)));
    }

    /**
     * 订单的费用类型以及费用列表相关
     *
     * @param mav
     * @param afterSalesVo
     */
    private void setOrderCostInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        AfterSalesCost afterSalesCost = new AfterSalesCost();
        afterSalesCost.setAfterSalesId(afterSalesVo.getAfterSalesId());
        List<AfterSalesCostVo> costList = afterSalesOrderService.getAfterSalesCostListById(afterSalesCost);
        mav.addObject("costList", costList);
        if (CollectionUtils.isEmpty(costList)){
            return;
        }
        costList.forEach(item -> {
            item.setCostTypeName(getSysOptionDefinition(item.getType()).getComments());
            item.setLastModTime(DateUtil.convertString(item.getModTime(), ""));
        });
    }

    /**
     * 沟通记录相关
     *
     * @param mav
     * @param afterSalesVo
     */
    private void setCommunicateInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        CommunicateRecord communicateRecord = new CommunicateRecord();
        communicateRecord.setAfterSalesId(afterSalesVo.getAfterSalesId());
        List<CommunicateRecord> communicateRecordList = traderCustomerService.getCommunicateRecordList(communicateRecord);
        mav.addObject("communicateList", communicateRecordList);
        if (CollectionUtils.isEmpty(communicateRecordList)){
            return;
        }
        communicateRecordList.forEach(item -> {
            AfterSalesTrader afterSalesTrader = new AfterSalesTrader();
            afterSalesTrader.setAfterSalesTraderId(item.getAfterSalesTraderId());
            AfterSalesTrader salesTrader = afterSalesOrderService.getAfterSalesTrader(afterSalesTrader);
            if (salesTrader == null){
                return;
            }
            item.setAfterSalesTraderName(item.getTraderName());
        });
    }

    /**
     * 物流信息相关
     *
     * @param mav
     * @param afterSalesVo
     */
    private void setExpressInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        if (afterSalesVo.getSubjectType() == 535 && (afterSalesVo.getType() == 3321 || afterSalesVo.getType() == 3322 ||
                afterSalesVo.getType() == 3323 || afterSalesVo.getType() == 3324)) {
            List<SaleorderGoods> saleorderGoodsList = afterSalesService.getRelatedSaleOrderGoods(afterSalesVo);
            Express express = new Express();
            express.setCompanyId(ErpConst.NJ_COMPANY_ID);
            express.setBusinessType(SysOptionConstant.ID_496);
            List<Integer> relatedIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(saleorderGoodsList)) {
                Integer saleorderId = saleorderGoodsList.get(0).getSaleorderId();
                saleorderGoodsList.forEach(item -> {
                    relatedIds.add(item.getSaleorderGoodsId());
                });
                express.setRelatedIds(relatedIds);
                try {
                    mav.addObject("expressList", expressService.getExpressListNew(express));
                } catch (Exception e) {
                    log.info("获取赔付类型售后单对应的销售单物流信息失败,售后信息" + JSON.toJSONString(afterSalesVo));
                }
                mav.addObject("saleorderId", saleorderId);
            }

        }
    }

    /**
     * 开票申请审核信息相关
     *
     * @param mav
     * @param afterSalesVo
     */
    private void setInvoiceApplyInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        InvoiceApply invoiceApplyInfo = invoiceService.getInvoiceApplyByRelatedId(afterSalesVo.getAfterSalesId(), SysOptionConstant.ID_504, afterSalesVo.getCompanyId());
        List<InvoiceApplyDto> invoiceApplyList = invoiceApplyApiService.getByRelatedIdAndType(afterSalesVo.getAfterSalesId(), SysOptionConstant.ID_504);
        mav.addObject("invoiceApply", invoiceApplyInfo);
        Integer isEnableEditService = ErpConst.ZERO;// 不可编辑
        if (Objects.isNull(invoiceApplyInfo)){
            isEnableEditService = ErpConst.ONE;// 可编辑
        }else{
            if ((invoiceApplyInfo.getIsAdvance() == 1 && invoiceApplyInfo.getAdvanceValidStatus() == 2) ||  // 提前开票审核不通过
                    (invoiceApplyInfo.getValidStatus() == 2)){
                isEnableEditService = ErpConst.ONE;// 可编辑
            }
        }
        mav.addObject("isEnableEditService",isEnableEditService);
        mav.addObject("invoiceApplyList", invoiceApplyList);
        if (invoiceApplyInfo != null) {
            Map<String, Object> historicInfoInvoice = actionProcdefService.getHistoric(processEngine, "invoiceVerify_" + invoiceApplyInfo.getInvoiceApplyId());
            mav.addObject("taskInfoInvoice", historicInfoInvoice.get("taskInfo"));
            mav.addObject("startUserInvoice", historicInfoInvoice.get("startUser"));
            mav.addObject("candidateUserMapInvoice", historicInfoInvoice.get("candidateUserMap"));
            // 最后审核状态
            mav.addObject("endStatusInvoice", historicInfoInvoice.get("endStatus"));
            mav.addObject("historicActivityInstanceInvoice", historicInfoInvoice.get("historicActivityInstance"));
            mav.addObject("commentMapInvoice", historicInfoInvoice.get("commentMap"));

            Task taskInfoInvoice = (Task) historicInfoInvoice.get("taskInfo");
            //当前审核人
            String verifyUsersInvoice = null;
            if (null != taskInfoInvoice) {
                Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfoInvoice);
                verifyUsersInvoice = (String) taskInfoVariables.get("verifyUsers");
            }
            mav.addObject("verifyUsersInvoice", verifyUsersInvoice);
        }
    }

    /**
     * 订单审核信息相关
     *
     * @param mav
     * @param afterSalesVo
     */
    private void setOrderExamineInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {

        //售后单完结审核信息
        //Map<String, Object> historicInfoOver=actionProcdefService.getHistoric(processEngine, "overAfterSalesVerify_"+ afterSalesVo.getAfterSalesId());

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "afterSalesVerify_" + afterSalesVo.getAfterSalesId());
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        mav.addObject("taskInfo", historicInfo.get("taskInfo"));
        mav.addObject("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        mav.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        mav.addObject("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historic = setAssignRealNames(mav, historicInfo);
        mav.addObject("historicActivityInstance", historic);
        mav.addObject("commentMap", historicInfo.get("commentMap"));
        String verifyUsers = null;
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            verifyUsers = (String) taskInfoVariables.get("verifyUsers");
        }
        mav.addObject("verifyUsers", getVerifyUserRealNames(verifyUsers));

        if (!OrderConstant.AFTERSALE_ORDER_SOURCE_FRONT.equals(afterSalesVo.getSource())) {
            return;
        }
        afterSalesVo.setCreatorName(afterSalesVo.getCreateFrontEndUser());
        if (CollectionUtils.isEmpty(historic)) {
            return;
        }
        for (HistoricActivityInstance activityInstance : historic) {
            if (OrderConstant.APPLY_AUDIT_NAME.equals(activityInstance.getActivityName())) {
                if (activityInstance instanceof HistoricActivityInstanceEntity) {
                    HistoricActivityInstanceEntity applyActivityInstance = (HistoricActivityInstanceEntity) activityInstance;
                    ArrayList<AssigneeVo> assigneeVos = (ArrayList<AssigneeVo>) mav.getModel().get("assigneeVos");
                    AssigneeVo assigneeVo = new AssigneeVo();
                    assigneeVo.setAssignee(afterSalesVo.getCreateFrontEndUser());
                    assigneeVo.setRealName(afterSalesVo.getCreateFrontEndUser());
                    assigneeVos.add(assigneeVo);
                    break;
                }
            }
        }
    }


    /**
     * 售后单完结审核信息
     *
     * @param afterSales
     * @param mav
     */
    private void setOrderFinishInfo(AfterSalesVo afterSales, ModelAndView mav) {
        Map<String, Object> historicInfoOver = actionProcdefService.getHistoric(processEngine, "overAfterSalesVerify_" + afterSales.getAfterSalesId());
        Task taskInfoOver = (Task) historicInfoOver.get("taskInfo");
        mav.addObject("taskInfoOver", taskInfoOver);
        mav.addObject("startUserOver", historicInfoOver.get("startUser") != null ?
                getRealNameByUserName(historicInfoOver.get("startUser").toString()) : null);
        // 最后审核状态
        mav.addObject("endStatusOver", historicInfoOver.get("endStatus"));

        List<HistoricActivityInstance> historicActivityInstanceOver = setAssignRealNames(mav, historicInfoOver);
        mav.addObject("historicActivityInstanceOver", historicActivityInstanceOver);
//        // 获取完结时间
//        mav.addObject("endTime", historicActivityInstanceOver.get(historicActivityInstanceOver.size() - 1).getEndTime());

        mav.addObject("commentMapOver", historicInfoOver.get("commentMap"));
        mav.addObject("candidateUserMapOver", historicInfoOver.get("candidateUserMap"));
        String verifyUsersOver = null;
        if (null != taskInfoOver) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfoOver);
            verifyUsersOver = (String) taskInfoVariables.get("verifyUsers");
        }
        mav.addObject("verifyUsersOver", getVerifyUserRealNames(verifyUsersOver));
    }

    /**
     * 付款申请信息相关
     *
     * @param mav
     * @param afterSalesVo
     */
    private void setPayApplyInfo(ModelAndView mav, AfterSalesVo afterSalesVo) {
        Integer isPayApplySh = 0;
        Integer payApplyId = 0;
        if (!CollectionUtils.isEmpty(afterSalesVo.getAfterPayApplyList())) {
            for (PayApply payApply : afterSalesVo.getAfterPayApplyList()) {
                if (payApply.getValidStatus() == 0 || payApply.getValidStatus() == 2) {
                    if (payApply.getValidStatus() == 0) {
                        isPayApplySh = 1;
                    }
                    break;
                }
            }
            if (!afterSalesVo.getAfterPayApplyList().isEmpty()) {
                payApplyId = afterSalesVo.getAfterPayApplyList().get(0).getPayApplyId();
            }
        }


        mav.addObject("isPayApplySh", isPayApplySh);

        Map<String, Object> historicInfoPay = actionProcdefService.getHistoric(processEngine, "paymentVerify_" + payApplyId);
        Task taskInfoPay = (Task) historicInfoPay.get("taskInfo");
        historicInfoPay.get("historicActivityInstance");
        mav.addObject("taskInfoPay", taskInfoPay);
        mav.addObject("startUserPay", historicInfoPay.get("startUser"));
        // 最后审核状态
        mav.addObject("endStatusPay", historicInfoPay.get("endStatus"));
        mav.addObject("historicActivityInstancePay", historicInfoPay.get("historicActivityInstance"));
        mav.addObject("commentMapPay", historicInfoPay.get("commentMap"));
        mav.addObject("candidateUserMapPay", historicInfoPay.get("candidateUserMap"));
        String verifyUsersPay = null;
        if (null != taskInfoPay) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfoPay);
            verifyUsersPay = (String) taskInfoVariables.get("verifyUsers");
        }
        mav.addObject("verifyUsersPay", verifyUsersPay);
    }

    private Integer existOrNot(User sessUser, List<User> users) {
        if (sessUser != null && CollectionUtils.isNotEmpty(users)) {
            Optional<User> first = users.stream().filter(e -> e.getUserId().equals(sessUser.getUserId())).findFirst();
            if (first.isPresent()) {
                return ErpConst.ONE;
            }
        }
        return ErpConst.ZERO;
    }

    /**
     * 销售售后申请审核
     *
     * @param request
     * @param afterSales
     * @param taskId
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/editApplyAudit")
    public ResultInfo<?> editApplyAudit(HttpServletRequest request, AfterSalesVo afterSales, String taskId) {
        logger.info("editApplyAudit start afterSales:{}, taskId:{}", JSON.toJSONString(afterSales), taskId);
        User user = getSessionUser(request);
        afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);
        afterSales.setTraderType(ErpConst.ONE);
        afterSales.setStatus(ErpConst.ONE);
        afterSales.setUpdater(user.getUserId());
        afterSales.setModTime(DateUtil.sysTimeMillis());

        //VDERP-11054【功能】针对前台推送安调单进行校验start
        if(AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(afterSales.getType())
                || AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(afterSales.getType())){
            ResultInfo resultInfo = afterSalesUpgradeService.checkAtGoodsInfo(afterSales);
            if(resultInfo.getCode() == -1){
                return ResultInfo.error(resultInfo.getMessage());
            }
        }
        //VDERP-11054【功能】针对前台推送安调单进行校验end

/*        if (afterSalesOrderService.editApplyAudit(afterSales) == null) {*/
        if (null == afterSalesUpgradeService.editApplyAudit(afterSales)){
            return ResultInfo.error("申请审核失败");
        }
        try {
            Map<String, Object> variableMap = new HashMap<>(16);
            AfterSalesVo afterSalesInfo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
            /**
             *  订单中产品类型（0未维护,1 只有设备,2 只有试剂,3 又有试剂又有设备）
             */
            afterSalesInfo.setGoodsType(ErpConst.ZERO);
            List<Integer> goodsTypeList = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(afterSalesInfo.getAfterSalesGoodsList())) {
                afterSalesInfo.getAfterSalesGoodsList().forEach(item -> {
                    if (item.getGoodsType() != null && (item.getGoodsType() == 316 || item.getGoodsType() == 319)) {
                        goodsTypeList.add(ErpConst.ONE);
                    }
                    if (item.getGoodsType() != null  && (item.getGoodsType() == 317 || item.getGoodsType() == 318)) {
                        goodsTypeList.add(ErpConst.TWO);
                    }
                });

                if (CollectionUtils.isNotEmpty(goodsTypeList)) {
                    List<Integer> newList = new ArrayList(new HashSet(goodsTypeList));
                    if (newList.size() == 1) {
                        if (newList.get(0) == 1) {
                            afterSalesInfo.setGoodsType(1);
                        }
                        if (newList.get(0) == 2) {
                            afterSalesInfo.setGoodsType(2);
                        }
                    }

                    if (newList.size() == 2) {
                        afterSalesInfo.setGoodsType(3);
                    }
                }
            }
            Set<String> vitualAudits=new HashSet<>();
            List<Integer> vitualUserIds=new ArrayList<>();
            // 开始生成流程(如果没有taskId表示新流程需要生成)
            if ("0".equals(taskId)) {
                variableMap.put("afterSalesInfo", afterSalesInfo);
                variableMap.put("processDefinitionKey", "afterSalesVerify");
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("orgId", user.getOrgId());
                variableMap.put("businessKey", "afterSalesVerify_" + afterSalesInfo.getAfterSalesId());
                variableMap.put("relateTableKey", afterSalesInfo.getAfterSalesId());
                variableMap.put("relateTable", "T_AFTER_SALES");
                variableMap.put("skuManages", "");
                variableMap.put("isVirtualVerify", false);
                //VDERP-12089【金蝶对接】【采购售后模块】销售单直发售后增加供应链审核以及不支持采购售后的直普发修改start
                if(CollectionUtils.isNotEmpty(afterSalesInfo.getAfterSalesGoodsList())) {
                    //是否走虚拟商品-供应链审核
                    if(AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesInfo.getType())){
                        List<AfterSalesGoodsVo> afterSalesGoodsVos = afterSalesInfo.getAfterSalesGoodsList().stream().filter(x -> Objects.nonNull(x.getOrderDetailId())).collect(Collectors.toList());
                        List<Integer> saleorderGoodsIds = afterSalesGoodsVos.stream().map(AfterSalesGoodsVo::getOrderDetailId).collect(Collectors.toList());
                        //获取满足条件的sku列表
                        List<String> virtualSkuList = saleorderGoodsMapper.getVirtualGoodsSkuNoList(saleorderGoodsIds);
                        if(CollectionUtils.isNotEmpty(virtualSkuList)){
                            //清空审核记录
                            afterSaleAuditInfoService.clearAuditInfoByAfterSaleId(afterSalesInfo.getAfterSalesId());
                            //获取虚拟商品对应的产品归属人列表
                            List<ProductManageAndAsistDto> manageAndAsistDtoList = coreSkuMapper.batchQueryProductManageAndAsist(virtualSkuList);
                            vitualAudits= afterSaleAuditInfoService.getProductManageAndAsistNameList(manageAndAsistDtoList);
                            vitualUserIds=afterSaleAuditInfoService.getProductManageAndAsistIdList(manageAndAsistDtoList);
                            if (CollectionUtils.isNotEmpty(vitualAudits)){
                                variableMap.put("isVirtualVerify", true);
                                //初始化审核数据
                                afterSaleAuditInfoService.insertAfterSaleAuditInfo(manageAndAsistDtoList,afterSalesInfo);
                            }
                        }
                    }

                    List<AfterSalesGoodsVo> haveDirectGoodsList = afterSalesInfo.getAfterSalesGoodsList().stream().filter(
                            item -> ErpConst.ONE.equals(item.getDeliveryDirect())).collect(Collectors.toList());
                    if ((AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesInfo.getType())
                            || AfterSalesProcessEnum.AFTERSALES_HH.getCode().equals(afterSalesInfo.getType()))
                            && CollectionUtils.isNotEmpty(haveDirectGoodsList)) {
                        //1、销售售后单为销售退货/销售换货
                        //2、包含退/换货方式为直发的商品
                        //3、直发退货的SKU发货数量大于0
                        variableMap.put("isNeedSupplyVerify", false);
                            for (AfterSalesGoodsVo afterSalesVo : haveDirectGoodsList) {
                                SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(afterSalesVo.getOrderDetailId());
                                if (Objects.nonNull(saleorderGoods) && Objects.nonNull(saleorderGoods.getDeliveryNum()) && saleorderGoods.getDeliveryNum() > 0) {
                                    variableMap.put("isNeedSupplyVerify", true);
                                    // 将销售售后单商品的产品归属人加入
                                    List<String> skus = afterSalesInfo.getAfterSalesGoodsList().stream().map(AfterSalesGoodsVo::getSku).collect(Collectors.toList());
                                    if (CollUtil.isNotEmpty(skus)) {
                                        Set<String> productManageAndAsistNameList = getProductManageAndAsistNameList(skus);
                                        if (CollUtil.isNotEmpty(productManageAndAsistNameList)) {
                                            variableMap.put("skuManages", CollUtil.join(productManageAndAsistNameList,","));
                                        }
                                    }
                                    break;
                                }
                            }


                    } else {
                        variableMap.put("isNeedSupplyVerify", false);
                    }
                }else {
                    variableMap.put("isNeedSupplyVerify", false);
                }
                //VDERP-12089【金蝶对接】【采购售后模块】销售单直发售后增加供应链审核以及不支持采购售后的直普发修改end
                actionProcdefService.createProcessInstance(request, "afterSalesVerify",
                        "afterSalesVerify_" + afterSalesInfo.getAfterSalesId(), variableMap);
            }
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "afterSalesVerify_" + afterSalesInfo.getAfterSalesId());
            if (!"审核完成".equals(historicInfo.get("endStatus"))) {
                // 修改锁定备注
                Saleorder saleorderLocked = new Saleorder();
                saleorderLocked.setLockedReason("售后锁定");
                saleorderLocked.setSaleorderId(afterSalesInfo.getOrderId());
                saleorderService.saveEditSaleorderInfo(saleorderLocked, request, request.getSession());
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                // 设置审核完成监听器回写参数
                variables.put("tableName", "T_AFTER_SALES");
                variables.put("db", 2);
                variables.put("id", "AFTER_SALES_ID");
                variables.put("idValue", afterSalesInfo.getAfterSalesId());
                variables.put("key", "STATUS");
                variables.put("value", 2);
                variables.put("key1", "ATFER_SALES_STATUS");
                variables.put("value1", 1);
                variables.put("key2", "VALID_STATUS");
                variables.put("value2", 1);
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        user.getUsername(), variables);
                //设置虚拟商品产品归属人
                TaskService taskService = processEngine.getTaskService();
                if (CollectionUtils.isNotEmpty(vitualAudits)){
                    // 获取当前活动节点
                    taskInfo = taskService
                            .createTaskQuery()
                            .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                            .singleResult();
                    String businessKey = (String) taskService.getVariable(taskInfo.getId(), "businessKey");
                    //设置候选人
                    setTaskCandidateUser(taskService, businessKey, vitualAudits);

                }
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                    //发送消息提醒至虚拟商品-供应链审核的产品归属人
                    if (CollectionUtils.isNotEmpty(vitualUserIds)){
                        //发送站内信
                        sendAftersalesInfo(vitualUserIds, taskService.getVariables(taskInfo.getId()),afterSalesInfo);
                    }
                }


            }
            logger.info("推送售后单到前台商城,申请审核,afterSalesId:{}", afterSales.getAfterSalesId());
            afterSalesApiService.pushAfterSalesToFrontMall(afterSales.getAfterSalesId());
            return ResultInfo.success();
        } catch (Exception e) {
            logger.error("editApplyAudit:", e);
            return ResultInfo.error("任务完成操作失败" + e.getMessage());
        }
    }

    Set<String> getProductManageAndAsistNameList(List<String> skuNos) {

        List<ProductManageAndAsistDto> manageAndAsistDtoList = coreSkuMapper.batchQueryProductManageAndAsist(skuNos);

        Set<String> manageAndAsistNameSet = new HashSet<>();

        manageAndAsistDtoList.stream().forEach(manageAndAsist -> {

            if (StringUtil.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductAssitName());
            }

            if (StringUtil.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductManageName());
            }
        });

        return manageAndAsistNameSet;
    }

    List<Integer> getProductManageAndAsistIdList(List<String> skuNos) {

        List<ProductManageAndAsistDto> manageAndAsistDtoList = coreSkuMapper.batchQueryProductManageAndAsist(skuNos);

        Set<Integer> manageAndAsistNameIdSet = new HashSet<>();

        manageAndAsistDtoList.stream().forEach(manageAndAsist -> {

            if (StringUtil.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductAssitUserId());
            }

            if (StringUtil.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductManageUserId());
            }

        });

        return new ArrayList<>(manageAndAsistNameIdSet);
    }
    /**
     * 初始化申请审核页面
     *
     * @param request
     * @param taskId
     * @param pass
     * @param type
     * @param saleorderId
     * @param afterSalesId
     * @param isLightning
     * @param lightningAfterSalesId
     * @param afterSaleorderId
     * @return
     */
    @FormToken(save=true)
    @ResponseBody
    @RequestMapping(value = "/complement")
    public ModelAndView complement(HttpServletRequest request, String taskId, Boolean pass, Integer type, Integer saleorderId,
                                   Integer afterSalesId, Integer isLightning, Integer lightningAfterSalesId,
                                   @RequestParam(required = false)Integer afterSaleorderId) {
        ModelAndView mv = new ModelAndView("orderstream/aftersales/complement");
        if(StringUtil.isNotBlank(request.getParameter("sku"))){
            mv.addObject("sku", request.getParameter("sku"));
        }
        if(StringUtil.isNotBlank(request.getParameter("orderId"))){
            mv.addObject("orderId", request.getParameter("orderId"));
        }
        if (isLightning!=null){
            mv.addObject("isLightning",isLightning);
            mv.addObject("lightningAfterSalesId",lightningAfterSalesId);
        }
        TaskService taskService = processEngine.getTaskService();
        // 使用任务id,获取任务对象，获取流程实例id
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        String taskName = task.getName();
        if(AfterSalesConstants.VIRTUAL_GOODS_AUDIT.equals(taskName)){
            mv.addObject("virtualAudit", Boolean.TRUE);
        }
        mv.addObject("taskId", taskId);
        mv.addObject("saleorderId", saleorderId);
        mv.addObject("afterSalesId", afterSalesId);
        mv.addObject("afterSaleorderId", afterSaleorderId);
        mv.addObject("pass", pass);
        mv.addObject("type", type);
        return mv;
    }

    /**
     * 订单审核操作
     *
     * @param request
     * @param taskId
     * @param comment
     * @param pass
     * @param saleorderId
     * @param isLightning
     * @param lightningAfterSalesId
     * @param afterSalesId
     * @param afterSaleorderId
     * @return
     */
    @FormToken(remove=true)
    @ResponseBody
    @RequestMapping(value = "/complementAfterSaleTask")
    @SystemControllerLog(operationType = "add",desc = "订单审核操作")
    public ResultInfo<?> complementAfterSaleTask(HttpServletRequest request, String taskId, String comment, Boolean pass,
                                                 Integer saleorderId, Integer isLightning, Integer lightningAfterSalesId,
                                                 Integer afterSalesId,  Integer afterSaleorderId ,Integer isReturn) {
        User user = getSessionUser(request);
        Map<String, Object> variables = new HashMap<>(16);
        variables.put("pass", pass);
        ResultInfo backResultInfo = ResultInfo.success();
        //审批操作
        try {
            //是否闪电换货
            try {
                setLightningInfo(pass, isLightning, lightningAfterSalesId);
            } catch (Exception e) {
                logger.error("是否闪电退货添加失败",e);
            }
            AfterSaleAuditInfoEntity audit=new AfterSaleAuditInfoEntity();
            //售后审核流程相关
            backResultInfo = dealAfterSalesVerifyInfo(pass, lightningAfterSalesId, afterSaleorderId, taskId, variables, backResultInfo,user,isReturn,audit);
            if (pass&&Objects.nonNull(audit)&&StringUtil.isNotBlank(audit.getAuditRemark())){
                comment=audit.getAuditRemark()+comment;
            }
            //处理审核驳回相关信息
            dealOrderRejectInfo(taskId, comment, pass);

            //审核相关信息的保存
            ResultInfo<?> complementStatus = saveCompleteData(request, taskId, comment, pass, user, variables, afterSalesId);
            /**销售单信息处理由监听器触发，此处无需再调用
            if(StringUtil.isNotBlank(request.getParameter("sku")) && StringUtil.isNotBlank(request.getParameter("orderId"))){
                // 销售订单退货,产品解锁
                orderInfoSyncService.unlockSaleOrderGoods(request.getParameter("sku"),Integer.valueOf(request.getParameter("orderId")),user.getUserId());
            }**/

            //信息推送以及消息
            sendMessageAndNotice(request, pass, saleorderId, afterSalesId, complementStatus);

            if ("endEvent".equals(complementStatus.getData())) {
                logger.info("推送售后单到前台商城,审核通过或驳回,afterSalesId:{}", afterSalesId);
                // VDERP-17057  【客户档案】ERP客户档案时间轴 售后完结或关闭(后台)
                afterSalesApiService.trackCompleteClose(afterSalesId);
                afterSalesApiService.pushAfterSalesToFrontMall(afterSalesId);
            }
            return backResultInfo;
        } catch (Exception e) {
            logger.error(" activity error complementAfterSaleTask:{}", afterSalesId, e);
            return ResultInfo.error("任务完成操作失败：" + e.getMessage());
        }
    }

    /**
     * 售后审核流程相关
     *
     * @param pass
     * @param lightningAfterSalesId
     * @param afterSaleorderId
     * @param taskId
     * @param variables
     * @param backResultInfo
     * @param user
     * @param isReturn
     * @param audit
     * @return
     */
    private ResultInfo dealAfterSalesVerifyInfo(Boolean pass, Integer lightningAfterSalesId, Integer afterSaleorderId, String taskId,
                                                Map<String, Object> variables, ResultInfo backResultInfo, User user, Integer isReturn, AfterSaleAuditInfoEntity audit){
        logger.info("dealAfterSalesVerifyInfo {} {} {} {} {}",pass,lightningAfterSalesId,afterSaleorderId,taskId,variables);
        //任务相关service
        HistoryService historyService = processEngine.getHistoryService();
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult()
                        .getProcessInstanceId()).singleResult();
        TaskService taskService = processEngine.getTaskService();
        Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey(historicProcessInstance.getBusinessKey()).singleResult();
        //虚拟商品供应链审核会签
        if(AfterSalesConstants.VIRTUAL_GOODS_AUDIT.equals(taskInfo.getName())){
            //查询未审核的产品归属人
            AfterSaleAuditInfoEntity entity=new AfterSaleAuditInfoEntity();
            entity.setAfterSaleId(afterSaleorderId);
            entity.setAuditStatus(Constants.ZERO);
            List<AfterSaleAuditInfoEntity> unAuditList=afterSaleAuditInfoService.findGoodsAuditList(entity);
            log.info("出入库商品额外信息表:{}", JSON.toJSONString(unAuditList));
            List<String> unAuditSkuList=new ArrayList<>();
            String auditRemark="【供应链审核";
            //修改商品审核状态
            for (AfterSaleAuditInfoEntity afterSaleAuditInfoEntity : unAuditList) {
                if (afterSaleAuditInfoEntity.getUserName().contains(user.getUsername())) {
                    afterSaleAuditInfoEntity.setAuditStatus(Constants.ONE);
                    afterSaleAuditInfoEntity.setIsReturn(isReturn);
                    log.info("修改产品经理负责商品审核状态:afterSaleId {},isReturn {}",afterSaleorderId,isReturn);
                    afterSaleAuditInfoMapper.updateByPrimaryKeySelective(afterSaleAuditInfoEntity);
                    //修改可退不可退备注
                    auditRemark=auditRemark+afterSaleAuditInfoEntity.getSkuNo()+"、";
                    continue;
                }
                // 过滤掉已审核sku行
                unAuditSkuList.add(afterSaleAuditInfoEntity.getSkuNo());
            }
            auditRemark=auditRemark.substring(0,auditRemark.length()-1);
            if (ErpConst.ZERO.equals(isReturn)){
                auditRemark=auditRemark+"不可退】    ";
            }else if(ErpConst.ONE.equals(isReturn)){
                auditRemark=auditRemark+"可退】    ";
            }

            audit.setAuditRemark(auditRemark);
            String businessKey = (String) taskService.getVariable(taskId, "businessKey");
            variables.put("businessKey",businessKey);
            //还有未审核的产品
            if (CollectionUtils.isNotEmpty(unAuditSkuList)) {
                log.info("还有未审核的产品:{}", JSON.toJSONString(unAuditSkuList));
                taskService.setVariable(taskId, "allPass", false);
                variables.put("allPass",false);
                Set<String> uAuditorSet = getProductManageAndAsistNameList(unAuditSkuList);
                variables.put("uAuditorSet",uAuditorSet);
            }else{
                taskService.setVariable(taskId, "allPass", true);
                variables.put("allPass",true);
            }
            log.info("allPass状态:{}",taskService.getVariable(taskId,"allPass"));
        }

        if (!pass || (lightningAfterSalesId == null && afterSaleorderId == null) || System.currentTimeMillis() > peerListFilterTime){
            return backResultInfo;
        }
        ResultInfo resultInfo;
        if(taskInfo==null){
             logger.error(afterSaleorderId+"\t"+historicProcessInstance.getBusinessKey());
        }
        if (taskInfo!=null&&("售后主管审核").equals(taskInfo.getName())) {
            if (lightningAfterSalesId != null) {
                resultInfo = afterSalesService.directSalesAfterSales(lightningAfterSalesId, domain);
            } else {
                resultInfo = afterSalesService.directSalesAfterSales(afterSaleorderId, domain);
            }
            if (resultInfo.getCode() == -1) {
                //审批不通过
                pass = false;
                //重新设定审批流中审批结果为false
                variables.put("pass", pass);
                return resultInfo;
            }
        }
        return backResultInfo;
    }

    /**
     * 信息推送以及消息
     *
     * @param request
     * @param pass
     * @param saleorderId
     * @param afterSalesId
     * @param complementStatus
     */
    private void sendMessageAndNotice(HttpServletRequest request, Boolean pass, Integer saleorderId, Integer afterSalesId, ResultInfo<?> complementStatus) {
        if (saleorderId == null || StringUtil.isBlank(request.getParameter("orderId"))){
            return;
        }
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderId != null ? saleorderId : Integer.valueOf(request.getParameter("orderId")));

        if("endEvent".equals(complementStatus.getData()) && pass){
            //VDERP-2057 BD订单流程优化-ERP部分  售后产生的付款状态变更需要通知mjx
            afterSalesOrderService.notifyStatusToMjx(saleorder.getSaleorderId(),afterSalesId);
            if(afterSalesId != null){
                AfterSales afterSales = new AfterSales();
                afterSales.setBusinessType(1);
                afterSales.setAfterSalesId(afterSalesId);
                AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoListById(afterSales);
                if (!org.springframework.util.StringUtils.isEmpty(afterSalesVo)  && afterSalesVo.getType().equals(539)) {
                    saleorder.setOperateType(StockOperateTypeConst.AFTERORDER_BACK_FINSH);
                }
            }
        }
        //更新售后单updateDataTime
        orderCommonService.updateAfterOrderDataUpdateTime(afterSalesId,null, OrderDataUpdateConstant.AFTER_ORDER_END);
        //调用库存服务
        warehouseStockService.updateOccupyStockService(saleorder, 0);
    }

    /**
     * 保存审核信息
     * @param request
     * @param taskId
     * @param comment
     * @param pass
     * @param user
     * @param variables
     * @param afterSalesId
     * @return
     */
    private ResultInfo<?> saveCompleteData(HttpServletRequest request, String taskId, String comment, Boolean pass,
                                           User user, Map<String, Object> variables, Integer afterSalesId) {
        logger.info("保存审核信息 taskId:{},comment:{},pass:{},afterSalesId:{}",
                taskId, comment, pass, afterSalesId);
        ResultInfo<?> complementStatus = actionProcdefService.complementTask(request,taskId,comment,user.getUsername(),variables);
        if(variables.containsKey("allPass")&&Boolean.FALSE.equals(variables.get("allPass"))){
            //设置下个任务的候选人
            TaskService taskService = processEngine.getTaskService();
            String businessKey=variables.get("businessKey").toString();
            Set<String> uAuditorSet = (Set<String>) variables.get("uAuditorSet");
            setTaskCandidateUser(taskService, businessKey, uAuditorSet);
        }
        //如果审核没结束添加审核对应主表的审核状态
        Task task = new TaskEntity(taskId);
        Map<String, Object> nowMap = actionProcdefService.getVariablesMap(task);
        if ("endEvent".equals(complementStatus.getData()) && nowMap.get("businessKey").toString().contains("afterSalesVerify_")){
            //只有在订单审核的审批流才更新
            //审核通过时相关节点状态更新
            AfterSales afterSales = afterSalesService.getAfterSalesById(afterSalesId);
            logger.info("审核通过时相关节点状态更新 afterSales:{}", JSON.toJSONString(afterSales));
            switch (AfterSalesProcessEnum.getInstance(afterSales.getType())){
                case AFTERSALES_TH:{
                    //退货类型售后单初始化退票状态
                    afterSalesService.refreshInvoiceRefundStatus(afterSalesId);
                    //退货类型售后单初始化开票状态
                    afterSalesCommonService.saveMakeOutInvoiceCompleteData(afterSalesId);
                    //采销联动退货采购费用订单，退货预警 或自动退货或 自动转单
                    if (pass) {
                        autoExpenseOrWarnService.extractedWarnOrAutoDo(afterSales.getAfterSalesId(), afterSales.getOrderNo());
                    }
                    break;
                }
                case AFTERSALES_TP: {
                    //退票类型售后单初始化退票状态
                    if (!pass){
                        logger.info("审核不通过不刷新节点状态 afterSalesId:{}", afterSalesId);
                        break;
                    }
                    logger.info("退票类型售后单初始化退票状态刷新退票状态信息 afterSalesId:{}", afterSalesId);
                    afterSalesService.refreshInvoiceRefundStatus(afterSalesId);
                    break;
                }
                case LOST_TICKET:{
                    //丢票售后审核通过初始化订单处理状态
                    afterSalesService.refreshOrderHandleStatus(afterSalesId);
                    break;
                }
                case AFTERSALES_HH:{
                    //换货售后更新收款状态
                    afterSalesCommonService.saveCollectionAmountAtStatus(afterSalesId);
                    //退货类型售后单初始化开票状态
                    afterSalesCommonService.saveMakeOutInvoiceCompleteData(afterSalesId);
                    break;
                }
                case AFTERASALES_THIRD_AT:
                case AFTERASALES_THIRD_WX:
                case AFTERSALES_WX:
                case AFTERSALES_AT:
                case AFTERSALES_ATY:
                case AFTERSALES_ATN:{
                    if (!pass){
                        logger.info("审核不通过不刷新节点状态 afterSalesId:{}", afterSalesId);
                        break;
                    }
                    //收款状态|付款状态|开票状态|置为0
                    afterSalesCommonService.saveCollectionAmountAtStatus(afterSalesId);
                    afterSalesCommonService.savePayAmountAtStatuts(afterSalesId);
                    //刷新开票状态
                    afterSalesCommonService.saveMakeOutInvoiceCompleteData(afterSalesId);
                    break;
                }
                default:{

                }
            }
            return complementStatus;
        }
        if(!complementStatus.getData().equals("endEvent")){
            verifiesRecordService.saveVerifiesInfo(taskId,pass ? ErpConst.ZERO : ErpConst.TWO);
        }
        return complementStatus;
    }

    /**
     * 处理审核不通过信息保存相关
     *
     * @param taskId
     * @param comment
     * @param pass
     */
    private void dealOrderRejectInfo(String taskId, String comment, Boolean pass) {
        if (pass){
            return;
        }
        //如果审核不通过 获取任务的Service，设置和获取流程变量
        TaskService taskService = processEngine.getTaskService();

        HashMap<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("db", ErpConst.ONE);
        taskService.setVariables(taskId, paramMap);
        String id=(String) taskService.getVariable(taskId, "id");
        Integer idValue=(Integer) taskService.getVariable(taskId, "idValue");

        taskService.setVariable(taskId,"value", 3);
        String key= (String) taskService.getVariable(taskId, "key");
        String tableName= (String) taskService.getVariable(taskId, "tableName");


        if(tableName != null && id != null && idValue != null && key != null){
            actionProcdefService.updateInfo(tableName, id, idValue, key, ErpConst.THREE, ErpConst.ONE);
        }
        verifiesRecordService.saveVerifiesInfo(taskId,2);
        if("T_AFTER_SALES".equals(tableName) && "STATUS".equals(key) && "AFTER_SALES_ID".equals(id)){
            afterSalesService.saveVerifiesNotPassReason(idValue,comment);
        }
    }

    /**
     * 是否闪电换货信息
     *
     * @param pass
     * @param isLightning
     * @param lightningAfterSalesId
     */
    private void setLightningInfo(Boolean pass, Integer isLightning, Integer lightningAfterSalesId) {
        if (lightningAfterSalesId == null || isLightning == null || pass == null || !pass){
           return;
        }
        afterSalesOrderService.addIsLightning(lightningAfterSalesId,isLightning);
    }

    /**
     * 关闭售后订单
     *
     * @param request
     * @param afterSalesVo
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveCloseAfterSales")
    public ResultInfo<?> saveCloseAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo) {
        User user = getSessionUser(request);

        Integer position = user.getPositions() == null ? 0 : user.getPositions().get(0).getType();

        AfterSalesVo afterSales = new AfterSalesVo();
        afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);
        afterSales.setAfterSalesId(afterSalesVo.getAfterSalesId());
        //本处适应第三方
        afterSales.setTraderType(ErpConst.ONE);
        adapterThirdParty(afterSalesVo);

        AfterSalesVo afterSalesInfo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
        afterSalesVo.setAfterSalesNo(afterSalesInfo.getAfterSalesNo());


        //-----------生效销售售后退货换货单需wms确认是否可关闭--------
        try {
            ResultInfo<?> resultInfo = checkOrderCanClose(afterSalesVo, afterSalesInfo);
            if (resultInfo != null && ErpConst.ZERO.equals(resultInfo.getCode())) {
                return resultInfo;
            }
        } catch (Exception e) {
            logger.error("关闭销售售后单下传WMS 判断 error",e);
        }

        //-----------生效销售售后退货换货单需wms确认是否可关闭--------

        // 如果是生效的售后单关闭需要审核
        if (afterSalesInfo.getValidStatus().equals(1) && !afterSalesInfo.getSubjectType().equals(536)
                && position != 310) {
            //// 将关闭原因，关闭备注，关闭人员存在数据库
            afterSalesVo.setAfterSalesStatusUser(user.getUserId());
           afterSalesOrderService.updateAfterSalesById(afterSalesVo);

            // 对售后单的流程判断金额进行计算
            // 销售售后

            switch (afterSalesInfo.getSubjectType()){
                // 销售订单退货
                case 535 :{

                    switch (AfterSalesProcessEnum.getInstance(afterSalesInfo.getType())){
                        case AFTERSALES_TH:{
                            afterSalesVo.setOrderTotalAmount(afterSalesInfo.getRefundAmount() != null ? afterSalesInfo.getRefundAmount() : BigDecimal.ZERO);
                            break;
                        }
                        case AFTERSALES_HH:{
                            //关联外借单
                            List<LendOut> lendoutList = warehouseOutService.getLendOutInfoList(afterSalesVo);
                            if(CollectionUtils.isNotEmpty(lendoutList)) {
                                //存在进行中的外接单不允许关闭
                                return ResultInfo.error("存在未完成的外借单，无法操作");
                        }

                            // 售后单对应的商品信息
                            if (!CollectionUtils.isEmpty(afterSalesInfo.getAfterSalesGoodsList())) {
                                BigDecimal totalAmount = BigDecimal.ZERO;
                                for (AfterSalesGoodsVo asg : afterSalesInfo.getAfterSalesGoodsList()) {
                                    if (asg.getSaleorderNum() != null && asg.getSaleorderPrice() != null) {
                                        totalAmount = totalAmount.add(new BigDecimal(asg.getSaleorderNum())
                                                .multiply(asg.getSaleorderPrice()));
                                    }
                                }
                                afterSalesVo.setOrderTotalAmount(totalAmount);
                            } else {
                                afterSalesVo.setOrderTotalAmount(BigDecimal.ZERO);
                            }

                            break;
                        }

                        case AFTERSALES_TK :{
                            afterSalesVo.setOrderTotalAmount(afterSalesInfo.getRefundAmount() != null ? afterSalesInfo.getRefundAmount() : BigDecimal.ZERO);
                            break;
                        }
                        case AFTERSALES_WX :{
                            afterSalesVo.setOrderTotalAmount(afterSalesInfo.getServiceAmount() != null ? afterSalesInfo.getServiceAmount() : BigDecimal.ZERO);
                            break;
                        }

                        default:{
                            afterSalesVo.setOrderTotalAmount(BigDecimal.ZERO);
                        }
                    }

                    break;
                }
                // 第三方售后
                case 537 :{
                    // 第三方退款

                    if (afterSalesInfo.getType().equals(551)) {
                        afterSalesVo.setOrderTotalAmount(afterSalesInfo.getRefundAmount() == null ?
                                BigDecimal.ZERO : afterSalesInfo.getRefundAmount());
                        // 第三方维修
                    } else if (afterSalesInfo.getType().equals(585)) {
                        afterSalesVo.setOrderTotalAmount(afterSalesInfo.getServiceAmount() == null ?
                                BigDecimal.ZERO : afterSalesInfo.getServiceAmount());
                        // 其他
                    } else {
                        afterSalesVo.setOrderTotalAmount(BigDecimal.ZERO);
                    }
                    break;
                }
                default:{

                }
            }

            try {
                //售后关闭审核流处理
                return dealVerifyInfo(request, afterSalesVo, user, afterSalesInfo);
            } catch (Exception e) {
                logger.error("saveCloseAfterSales:", e);
                return ResultInfo.error("任务完成操作失败：" + e.getMessage());
            }
        } else {
            //调用销售接口同步销售单状态
            logger.info("关闭售后单无需审批流同步数据 afterSalesId:{}", afterSales.getAfterSalesId());
            orderInfoSyncService.unlockSaleOrderWhenAfterSalesClosedOrFinished(afterSales.getAfterSalesId(),ErpConst.ONE);

            ResultInfo<?> resultInfo = afterSalesOrderService.saveCloseAfterSales(afterSalesVo, user);
            if (resultInfo == null || ResultInfo.error().getCode().equals(resultInfo.getCode())){
                return ResultInfo.error("关闭订单失败");
            }

            try {
                dealUnValidOrderSendWms(user, afterSalesInfo);
            } catch (Exception e) {
                logger.error("未生效销售退货单关闭下发WMS error", e);
            }

            // 耗材订单推送关闭信息
            try {
                sendHcOrderCloseMessage(afterSalesVo);
            } catch (Exception e) {
                logger.error("耗材订单推送关闭信息 error", e);
            }

            //销售售后关闭，解除预警
            try {
                Integer afterSalesId = afterSalesVo.getAfterSalesId();
                expenseAfterSalesApiService.releaseReturnEarlyWarn(afterSalesId);
            }catch (Exception e){
                logger.error("销售售后关闭，解除预警 error",e);
            }

            logger.info("推送售后单到前台商城,未生效售后单关闭,afterSalesId:{}", afterSalesVo.getAfterSalesId());
            afterSalesApiService.pushAfterSalesToFrontMall(afterSalesVo.getAfterSalesId());
            return resultInfo;
        }
    }

    /**
     * 未生效销售退货单关闭下发WMS
     *
     * @param user
     * @param afterSalesInfo
     * @throws Exception
     */
    private void dealUnValidOrderSendWms(User user, AfterSalesVo afterSalesInfo) throws Exception {
        if (!StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSalesInfo.getType()) ||
                !afterSalesInfo.getValidStatus().equals(0)){
            return;
        }
        logicalSaleorderChooseServiceImpl.closeAfterPutSaleorder(afterSalesInfo,user);
    }

    /**
     * 耗材订单推送关闭信息
     *
     * @param afterSalesVo
     */
    private void sendHcOrderCloseMessage(AfterSalesVo afterSalesVo) throws Exception {

        afterSalesVo.setTraderType(ErpConst.ONE);

        AfterSalesVo asVo = afterSalesOrderService.getAfterSalesVoDetail(afterSalesVo);

        // 判断是否是耗材订单
        if (!asVo.getSource().equals(1)){
            return;
        }
        Map<String, Object> param = new HashMap<>(16);
        // 查询当前订单的售后列表
        List<AfterSalesVo> afterSalesList = afterSalesOrderService.getAfterSalesVoListByOrderId(asVo);

        param.put("isRefund", CollectionUtils.isNotEmpty(afterSalesList) && CollectionUtils.isNotEmpty(afterSalesList
                .stream().filter(item -> item.getAtferSalesStatus().equals(0) || item.getAtferSalesStatus().equals(1))
                .collect(Collectors.toList())) ? 1 : 0);

        // 请求头
        Map<String, String> header = new HashMap<>(16);
        param.put("orderNo", asVo.getOrderNo());
        header.put("version", "v1");
        JSONObject jsonObject = JSONObject.fromObject(param);
        // 定义反序列化 数据格式
        String url = apiUrl + "/order/after/completeBackMoney";
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
        HttpClientUtils.put(url, jsonObject.toString(), header, TypeRef);
    }

    /**
     * 售后关闭审核流处理
     *
     * @param request
     * @param afterSalesVo
     * @param user
     * @param afterSalesInfo
     * @return
     * @throws Exception
     */
    private ResultInfo<?> dealVerifyInfo(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                         User user, AfterSalesVo afterSalesInfo) throws Exception {
        // 售后单关闭
        afterSalesVo.setVerifiesType(0);
        Map<String, Object> variableMap = new HashMap<String, Object>();
        // 开始生成流程(如果没有taskId表示新流程需要生成)
        variableMap.put("afterSalesVo", afterSalesVo);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("relateTable", "T_AFTER_SALES");
        variableMap.put("processDefinitionKey", "overAfterSalesVerify");
        variableMap.put("relateTableKey", afterSalesInfo.getAfterSalesId());
        variableMap.put("businessKey", "overAfterSalesVerify_" + afterSalesInfo.getAfterSalesId());
        actionProcdefService.createProcessInstance(request, "overAfterSalesVerify",
                "overAfterSalesVerify_" + afterSalesInfo.getAfterSalesId(), variableMap);
        // 默认申请人通过
        // 根据BusinessKey获取生成的审核实例
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                "overAfterSalesVerify_" + afterSalesInfo.getAfterSalesId());
        if ( !"审核完成".equals(historicInfo.get("endStatus"))) {
            Task taskInfo = (Task) historicInfo.get("taskInfo");
            String taskId = taskInfo.getId();
            Authentication.setAuthenticatedUserId(user.getUsername());
            Map<String, Object> variables = new HashMap<>(16);
            // 默认审批通过
            // 如果未结束添加审核对应主表的审核状态
            if (!"endEvent".equals(actionProcdefService
                    .complementTask(request, taskId, "", user.getUsername(), variables).getData())) {
                verifiesRecordService.saveVerifiesInfo(taskId, 0);
            }
        }

        //销售售后关闭，解除预警
        try {
            Integer afterSalesId = afterSalesVo.getAfterSalesId();
            expenseAfterSalesApiService.releaseReturnEarlyWarn(afterSalesId);
        }catch (Exception e){
            logger.error("销售售后关闭，解除预警 error",e);
        }

        return ResultInfo.success();
    }

    /**
     * 检查订单是否可关闭
     *
     * @param afterSalesVo
     * @param afterSalesInfo
     * @return
     */
    private ResultInfo<?> checkOrderCanClose(AfterSalesVo afterSalesVo, AfterSalesVo afterSalesInfo) {
        Integer type = afterSalesVo.getType();
        if ((!StockOperateTypeConst.AFTERORDER_CHANGE_FINSH.equals(type) &&
                !StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(type)) ||
                !afterSalesInfo.getValidStatus().equals(1)){
            return null;
        }
        return saleorderService.cancelWMSOrder(afterSalesVo);
    }

    /**
     * 财务售后详情
     *
     * @param request
     * @param afterSales
     * @param session
     * @return
     */
    @FormToken(save=true)
    @ResponseBody
    @RequestMapping(value="getFinanceAfterSaleDetail")
    public ModelAndView getFinanceAfterSaleDetail(HttpServletRequest request, AfterSalesVo afterSales, HttpSession session){
        ModelAndView modelAndView = new ModelAndView();
        User user = getSessionUser(request);
        modelAndView.addObject("companyId", user.getCompanyId());
        modelAndView.addObject("curr_user", user);
        Integer payApplyId = 0;

        //维护采收售后售后订单详情信息
        AfterSalesVo afterSalesVo = retrievalAfterSalesInfo(afterSales, session, modelAndView);

        //订单流之前的订单跳转
        if (afterSalesVo != null && ErpConst.ZERO.equals(afterSalesVo.getIsNew())){
            logger.info("售后单为订单流之前订单跳转至旧页面 afterSalesId:{}", afterSales.getAfterSalesId());
            modelAndView.setViewName("redirect:/finance/after/getFinanceAfterSaleDetail.do");
            modelAndView.addObject("afterSalesId", afterSales.getAfterSalesId());
            modelAndView.addObject("subjectType", afterSales.getSubjectType());
            modelAndView.addObject("type", afterSales.getType());
            return modelAndView;
        }

        /**
         * 第一责任部门转化
         */
        try{
            getFirstResponsibleDepartment(afterSalesVo);
        } catch (Exception e) {
            logger.error("获取第一责任部门异常", e);
        }

        //修复付款申请记录/交易记录，申请人不展示
        repairPayRecord(afterSalesVo);

        //资金类型相关信息
        retrievalCapitalBillTypeInfo(modelAndView, afterSalesVo);

        //维护财务售后详情沟通记录信息
        retrievalFinanceDetailCommunicateList(modelAndView, afterSalesVo);

        //发票类型
        modelAndView.addObject("invoiceTypeList", getSysOptionDefinitionList(SysOptionConstant.ID_428));

        //获取财务售后详情付款申请信息
        payApplyId = retrievalFinanceDetailPayPlayInfo(modelAndView, afterSalesVo, payApplyId);


        //订单完结信息相关
        try {
            setOrderFinishInfo(afterSales, modelAndView);
        } catch (Exception e) {
            logger.error("订单完结信息相关异常", e);
        }

        List<Integer> skuIds = new ArrayList<>();
        if(!CollectionUtils.isEmpty(afterSalesVo.getAfterSalesGoodsList())){
            afterSalesVo.getAfterSalesGoodsList().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            Map<String,Map<String,Object>> newSkuInfosMap = this.vGoodsService.skuTipList(skuIds)
                    .stream().collect(Collectors.toMap(key->key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            modelAndView.addObject("newSkuInfosMap", newSkuInfosMap);
        }


        //获取财务售后售后单审核信息
        retrievalAfterSaleHistoricInfo(modelAndView, afterSalesVo);

        //获取财务售后售后单付款申请信息
        retrievalAfterSaleHistoricPayInfo(modelAndView, payApplyId);

        //开票申请审核信息
        setInvoiceApplyInfo(modelAndView, afterSalesVo);

        // 获取物流公司列表
        modelAndView.addObject("logisticsList", getLogisticsList(user.getCompanyId()));

        try{
            setBasicsInfo(user,modelAndView,afterSalesVo);
        } catch (Exception e){
            logger.error("获取基础模块异常",e);
        }

        /**
         * 获取顶部状态栏信息
         */
        try {
            setTopStatusList(modelAndView, afterSalesVo);
        } catch (Exception e) {
            logger.error("获取顶部状态栏信息异常", e);
        }
        Integer afterSalesId = afterSalesVo.getAfterSalesId();
        List<AfterSalesGoodsListResultDto> afterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial = afterSalesGoodsBizMapper.getAfterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial(afterSalesId);
        if (CollUtil.isNotEmpty(afterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial)) {
            String skus = afterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial.stream().map(AfterSalesGoodsListResultDto::getSku).collect(Collectors.joining(StrUtil.COMMA));
            modelAndView.addObject("goodIdsStr", skus);
        } else {
            modelAndView.addObject("goodIdsStr", "");
        }
        String skus = afterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial.stream().map(AfterSalesGoodsListResultDto::getSku).collect(Collectors.joining(StrUtil.COMMA));
        modelAndView.addObject("goodIdsStr", skus);

        switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())){
            case AFTERSALES_TH :{
                //设置退款特别提醒
                //设置退货入库记录展示
                afterSalesUpgradeService.setAfterSalesTHInfo(afterSalesVo);
                modelAndView.addObject("refundAmount", afterSalesService.getHaveRefundedAmount(afterSalesVo));
                List<AfterSalesGoodsVo> directGoodsList = afterSalesVo.getDirectGoodsList();//直发
                List<AfterSalesGoodsVo> normalGoodsList = afterSalesVo.getNormalGoodsList();//普发
                //是否显示确认退票和作废电子票按钮,
                boolean isShowAfterGoodes = true;
                if (directGoodsList!=null){
                    for (int i = 0; i < directGoodsList.size(); i++) {
                        AfterSalesGoodsVo afterSalesGoodsVo = directGoodsList.get(i);

                        if (afterSalesGoodsVo.getRknum() != null&&afterSalesGoodsVo.getArrivalNum()!=null) {
                            //该商品实际应退数量
                            int rknum = afterSalesGoodsVo.getRknum().intValue();
                            //该商品已退货入库数量
                            int arrivalNum = afterSalesGoodsVo.getArrivalNum().intValue();
                            if (arrivalNum<rknum){
                                isShowAfterGoodes = false;
                            }
                        }
                    }
                }
                if (normalGoodsList!=null){
                    for (int i = 0; i < normalGoodsList.size(); i++) {
                        AfterSalesGoodsVo afterSalesGoodsVo = normalGoodsList.get(i);
                        if (afterSalesGoodsVo.getRknum() != null&&afterSalesGoodsVo.getArrivalNum()!=null) {
                            //该商品实际应退数量
                            int rknum = afterSalesGoodsVo.getRknum().intValue();
                            //该商品已退货入库数量
                            int arrivalNum = afterSalesGoodsVo.getArrivalNum().intValue();
                            if (arrivalNum<rknum){
                                isShowAfterGoodes = false;
                            }
                        }
                    }
                }
                modelAndView.addObject("isShowAfterGoodes", isShowAfterGoodes);
                modelAndView.setViewName("orderstream/aftersales/finance_after_sale_th");
                break;
            }
            case AFTERSALES_HH :{
                //设置出入库记录
                afterSalesUpgradeService.setAfterSalesHHInfo(afterSalesVo);
                modelAndView.setViewName("orderstream/aftersales/finance_after_sale_hh");
                break;
            }
            case AFTERSALES_WX:
            case AFTERSALES_AT:
            case AFTERSALES_ATY:
            case AFTERSALES_ATN:{
                modelAndView.setViewName("orderstream/aftersales/finance_after_sale_at");
                break;
            }
            case AFTERSALES_TP: {
                modelAndView.setViewName("orderstream/aftersales/finance_after_sale_tp");
                break;
            }
            case AFTERSALES_TK: {
                //已退款金额计算
                modelAndView.addObject("refundAmount", afterSalesService.getHaveRefundedAmount(afterSalesVo));
                modelAndView.setViewName("orderstream/aftersales/finance_after_sale_tk");
                break;
            }
            case LOST_TICKET:{
                modelAndView.setViewName("orderstream/aftersales/finance_after_sale_qp");
                break;
            }
            case AFTERASALES_THIRD_AT:
            case AFTERASALES_THIRD_WX:{
                modelAndView.setViewName("orderstream/aftersales/finance_after_other_at");
                break;
            }
            default:{
                //订单流之前的订单跳转
                logger.info("售后单为订单流之前财务订单详情跳转至旧页面 afterSalesId:{}", afterSales.getAfterSalesId());
                modelAndView.setViewName("redirect:/finance/after/getFinanceAfterSaleDetail.do");
                modelAndView.addObject("afterSalesId", afterSales.getAfterSalesId());
                modelAndView.addObject("subjectType", afterSales.getSubjectType());
                modelAndView.addObject("type", afterSales.getType());
                return modelAndView;
            }
        }
        return modelAndView;
    }

    public void repairPayRecord(AfterSalesVo afterSalesVo){
        if(afterSalesVo == null || CollectionUtils.isEmpty(afterSalesVo.getAfterPayApplyList())){
         return;
        }
        afterSalesVo.getAfterPayApplyList().stream().filter(Objects::nonNull).forEach(s->{
            if (ObjectUtils.notEmpty(s.getCreator()) && ObjectUtils.isEmpty(s.getCreatorName())){
                User user = new User();
                user.setUserId(s.getCreator());
                s.setCreatorName(userService.getUser(user).getUsername());
            }
        });
    }

    /**
     * 维护财务售后售后单审核信息
     *
     * @param mv
     * @param payApplyId
     * @return
     */
    private Task retrievalAfterSaleHistoricPayInfo(ModelAndView mv, Integer payApplyId) {
        Map<String, Object> historicInfoPay=actionProcdefService.getHistoric(processEngine, "paymentVerify_"+ payApplyId);
        Task taskInfoPay = (Task) historicInfoPay.get("taskInfo");
        mv.addObject("taskInfoPay", taskInfoPay);
        mv.addObject("startUserPay", historicInfoPay.get("startUser"));
        mv.addObject("endStatusPay",historicInfoPay.get("endStatus"));

        mv.addObject("commentMapPay", historicInfoPay.get("commentMap"));
        mv.addObject("historicActivityInstancePay", historicInfoPay.get("historicActivityInstance"));
        mv.addObject("candidateUserMapPay", historicInfoPay.get("candidateUserMap"));
        mv.addObject("verifyUsersPay",taskInfoPay != null ? actionProcdefService.getVariablesMap(taskInfoPay).get("verifyUsers") : null);
        return taskInfoPay;
    }

    /**
     * 维护财务售后售后单审核信息
     * @param mv
     * @param afterSalesVo
     */
    private void retrievalAfterSaleHistoricInfo(ModelAndView mv, AfterSalesVo afterSalesVo) {
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "afterSalesVerify_"+ afterSalesVo.getAfterSalesId());
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        mv.addObject("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        mv.addObject("taskInfo", historicInfo.get("taskInfo"));
        mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态

        mv.addObject("endStatus",historicInfo.get("endStatus"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));
        List<HistoricActivityInstance> historic = setAssignRealNames(mv, historicInfo);
        mv.addObject("historicActivityInstance", historic);
        mv.addObject("verifyUsers", taskInfo != null ? actionProcdefService.getVariablesMap(taskInfo).get("verifyUsers") : null);
    }

    /**
     * 维护采收售后售后订单详情信息
     *
     * @param afterSales
     * @param session
     * @param mv
     * @return
     */
    private AfterSalesVo retrievalAfterSalesInfo(AfterSalesVo afterSales, HttpSession session, ModelAndView mv) {
        afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);
        afterSales.setTraderType(afterSales.getSubjectType());
        AfterSalesVo afterSalesVo = invoiceAfterService.getFinanceAfterSaleDetail(afterSales,session);
        //判断商品是否为赠品
        if (afterSalesVo!=null&&!CollectionUtils.isEmpty(afterSalesVo.getAfterSalesGoodsList())){
            afterSalesVo.getAfterSalesGoodsList().forEach(a -> a.setIsGift(saleorderGoodsMapper.getIsGiftBySaleorderGoodsId(a.getOrderDetailId())));
        }
        afterSalesVo.seteFlag(afterSales.geteFlag());
        mv.addObject("afterSalesVo", afterSalesVo);
        afterSalesVo.setAfterSalesStatusResonName((getSysOptionDefinition(afterSalesVo.getAfterSalesStatusReson()).getComments()));
        return afterSalesVo;
    }

    /**
     * 维护财务售后详情付款申请信息
     *
     * @param mv
     * @param afterSalesVo
     * @param payApplyId
     * @return
     */
    private Integer retrievalFinanceDetailPayPlayInfo(ModelAndView mv, AfterSalesVo afterSalesVo, Integer payApplyId) {
        if (CollectionUtils.isEmpty(afterSalesVo.getAfterPayApplyList())){
            return payApplyId;
        }

        Integer isPayApplySh = 0;

        List<PayApply> payApplyList = afterSalesVo.getAfterPayApplyList().stream()
                .filter(item -> ErpConst.ZERO.equals(item.getValidStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(payApplyList)){
            isPayApplySh = 1;

            switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())){
                case AFTERSALES_TK:{
                    //退款类型售后展示最新审核中的付款申请
                    payApplyId = payApplyList.get(0).getPayApplyId();
                    break;
                }
                default: {
                    //默认为旧的财务售后详情页面付款申请展示逻辑，需要时要拓展case
                    payApplyId = !afterSalesVo.getAfterPayApplyList().isEmpty() && ErpConst.ZERO.equals(payApplyId) ?
                            afterSalesVo.getAfterPayApplyList().get(0).getPayApplyId() : payApplyId;
                    break;
                }
            }
        }
        mv.addObject("payApplyId", payApplyId);
        mv.addObject("isPayApplySh", isPayApplySh);
        return payApplyId;
    }

    /**
     * 维护财务售后详情沟通记录信息
     *
     * @param mv
     * @param afterSalesVo
     */
    private void retrievalFinanceDetailCommunicateList(ModelAndView mv, AfterSalesVo afterSalesVo) {
        CommunicateRecord communicateRecord = new CommunicateRecord();
        communicateRecord.setAfterSalesId(afterSalesVo.getAfterSalesId());
        mv.addObject("communicateList", traderCustomerService.getCommunicateRecordList(communicateRecord));
    }

    /**
     * 资金类型相关信息
     *
     * @param mv
     * @param afterSalesVo
     */
    private void retrievalCapitalBillTypeInfo(ModelAndView mv, AfterSalesVo afterSalesVo) {
        if (afterSalesVo == null || CollectionUtils.isEmpty(afterSalesVo.getAfterCapitalBillList())){
            return;
        }
        mv.addObject("traderModes", getSysOptionDefinitionList(519));
        mv.addObject("bussinessTypes", getSysOptionDefinitionList(524));
    }

    /**
     * 确认退票操作初始化
     *
     * @param afterInvoiceVo
     * @return
     */
    @FormToken(save=true)
    @ResponseBody
    @RequestMapping(value="getAfterReturnInvoiceInfo")
    public ModelAndView getAfterReturnInvoiceInfo( AfterSalesInvoiceVo afterInvoiceVo){
        logger.info("init getAfterReturnInvoiceInfo afterInvoiceVo:{}", JSON.toJSONString(afterInvoiceVo));
        ModelAndView mv = new ModelAndView();
        //根据售后单号查询需要退票的发票信息
        try {
            AfterSalesInvoiceVo  afterInvoice= invoiceAfterService.getAfterReturnInvoiceInfo(afterInvoiceVo);
            //发票类型
            List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);
            mv.addObject("afterInvoice", afterInvoice);
            mv.addObject("invoiceTypeList", invoiceTypeList);

            //销售退票
            if(afterInvoice.getAfterType() != null && (AfterSalesProcessEnum.AFTERSALES_TP.getCode().equals(afterInvoice.getAfterType())
                    || AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterInvoice.getAfterType()))
                    //非当月发票 || 全电发票默认红字有效
                    && ((afterInvoice.getCurrentMonthInvoice() != null && ErpConst.ZERO.equals(afterInvoice.getCurrentMonthInvoice())) || ErpConst.THREE.equals(afterInvoice.getInvoiceProperty()))){
                mv.setViewName("orderstream/aftersales/after_return_invoice_tp");
            } else{
                mv.setViewName("orderstream/aftersales/after_return_invoice");
            }
        } catch (Exception e) {
            logger.error("init getAfterReturnInvoiceInfo error", e);
        }
        return mv;
    }


    /**
     * 售后-退货退票保存
     */
    @FormToken(remove=true)
    @ResponseBody
    @RequestMapping(value="saveAfterReturnInvoice")
    @SystemControllerLog(operationType = "add",desc = "售后-退货退票保存")
    public ResultInfo<?> saveAfterReturnInvoice(Invoice invoice
            , @RequestParam(required = false, value="currentMonthInvoice") Integer currentMonthInvoice
            , @RequestParam(required = false, value="detailGoodsIdArr") String detailGoodsIdArr
            , @RequestParam(required = false, value="invoiceAmountArr") String invoiceAmountArr
            , @RequestParam(required = false, value="invoicePriceArr") String invoicePriceArr
            , @RequestParam(required = false, value="invoiceNumArr") String invoiceNumArr) {
        logger.info("售后退货退票保存start invoice:{}, currentMonthInvoice:{}, detailGoodsIdArr:{}, invoiceAmountArr:{},invoicePriceArr:{}, invoiceNumArr:{}",
                JSON.toJSONString(invoice), currentMonthInvoice, detailGoodsIdArr, invoiceAmountArr, invoicePriceArr, invoiceNumArr);
        try {
            // 校验发票是否已退票
            invoiceService.checkInvoiceStatus(invoice);

            //校验发票是否在航信采购发票的流转状态
            ResultInfo<?> hxInvoiceInfo = invoiceService.getHxInvoiceInfo(invoice);
            if (hxInvoiceInfo != null) {
                return hxInvoiceInfo;
            }
            // 数电发票默认红字有效，即跨月。实际上此处应该根据colorType来判断
            if (currentMonthInvoice == 0 || ErpConst.THREE.equals(invoice.getInvoiceProperty())) {
                List<Integer> detailGoodsIdList = JSON.parseArray("[" + detailGoodsIdArr + "]", Integer.class);
                List<BigDecimal> invoiceNumStr = JSON.parseArray("[" + invoiceNumArr + "]", BigDecimal.class);
                List<BigDecimal> invoiceAmountStr = null;
                if (StringUtils.isNotBlank(invoiceAmountArr) && invoiceAmountArr.length() > 0) {
                    invoiceAmountStr = JSON.parseArray("[" + invoiceAmountArr + "]", BigDecimal.class);
                }
                List<BigDecimal> invoicePriceStr = JSON.parseArray("[" + invoicePriceArr + "]", BigDecimal.class);
                //根据税率计算出开票单价
                List<BigDecimal> invoicePriceList = new ArrayList<>();
                List<BigDecimal> invoiceTotleAmountList = new ArrayList<>();
                BigDecimal totalAmount = BigDecimal.ZERO;
                if (invoice != null && invoice.getRatio() != null) {
                    for (int i = 0; i < invoicePriceStr.size(); i++) {
                        if (invoiceAmountStr != null && invoiceAmountStr.size() == invoicePriceStr.size()) {
                            invoiceTotleAmountList.add(invoiceAmountStr.get(i).setScale(2, BigDecimal.ROUND_HALF_UP));
                        } else {
                            invoiceTotleAmountList.add(invoicePriceStr.get(i).multiply(invoiceNumStr.get(i)).setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        invoicePriceList.add(invoicePriceStr.get(i).divide((invoice.getRatio().add(new BigDecimal(1))), 10, BigDecimal.ROUND_HALF_UP).setScale(8, BigDecimal.ROUND_HALF_UP));
                        totalAmount = totalAmount.add(invoiceTotleAmountList.get(i));
                    }
                    invoice.setAmount(totalAmount);
                }
                invoice.setDetailGoodsIdList(detailGoodsIdList);
                invoice.setInvoiceNumList(invoiceNumStr);
                invoice.setInvoicePriceList(invoicePriceList);
                invoice.setInvoiceTotleAmountList(invoiceTotleAmountList);
            }
            //销售退票--退票记录跟着订单走
            if (invoice.getType() == null) {
                invoice.setType(SysOptionConstant.ID_505);
            }

            // 如果是数电票去查 航信
            if (ErpConst.THREE.equals(invoice.getInvoiceProperty())) {
                // 查询
                String OpenDate = fullyDigitalInvoiceApiService.getBlueInvoiceOpenDate(invoice.getInvoiceNo());
                if (StrUtil.isEmpty(OpenDate)) {
                    logger.error("saveSaleorderInvoice:数电发票：{}，未查询到开票日期", invoice.getInvoiceNo());
                    return new ResultInfo(-1, "数电发票未查询到信息，无法保存");
                }
                invoice.setOpenInvoiceTimeStr(OpenDate);
            }

            //保存售后退票发票信息
            CurrentUser user = CurrentUser.getCurrentUser();

            invoice.setAddTime(DateUtil.gainNowDate());
            invoice.setCompanyId(ErpConst.NJ_COMPANY_ID);
            invoice.setCreator(user.getId());
            invoice.setModTime(DateUtil.gainNowDate());
            invoice.setUpdater(user.getId());
            ResultInfo<?> resultInfo = invoiceAfterService.saveAfterReturnInvoice(invoice);
            if (resultInfo == null) {
                return ResultInfo.error();
            }

            //刷新售后订单的退票状态
            logger.info("退货退票保存刷新售后订单的退票状态 afterSalesId:{}", invoice.getAfterSalesId());
            afterSalesService.refreshInvoiceRefundStatus(invoice.getAfterSalesId());
            return resultInfo;
        } catch (Exception e) {
            logger.error("saveAfterReturnInvoice:", e);
            return ResultInfo.error();
        }
    }


    /**
     * <b>Description:</b><br>
     * 电子发票作废
     *
     * @param request
     * @param invoice
     * @return
     * @Note <b>Author:</b> duke <br>
     *       <b>Date:</b> 2018年6月27日 下午3:18:02
     */
    @ResponseBody
    @RequestMapping(value = "/cancelEInvoicePush")
    public ResultInfo<?> cancelEInvoicePush(HttpServletRequest request, Invoice invoice) {
        User user = getSessionUser(request);
        if (null != user) {
            invoice.setCompanyId(ErpConst.NJ_COMPANY_ID);
            invoice.setCreator(user.getUserId());
            invoice.setAddTime(DateUtil.gainNowDate());
        }
        invoice.setType(SysOptionConstant.ID_505);
        ResultInfo<?> resultInfo = invoiceService.cancelEInvoicePush(invoice);
        if (ResultInfo.error().equals(resultInfo)){
            log.info("电子发票作废失败{}",JSON.toJSONString(resultInfo));
            return ResultInfo.error("cancelEInvoicePush error");
        }
        logger.info("电子发票作废刷新售后订单的退票状态 afterSalesId:{}", invoice.getAfterSalesId());
        afterSalesService.refreshInvoiceRefundStatus(invoice.getAfterSalesId());
        return resultInfo;
    }

    /**
     * 跳转到售后编辑页
     *
     * @param request
     * @param afterSales
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/editAfterSalesPage")
    public ModelAndView editAfterSalesPage(HttpServletRequest request, AfterSalesVo afterSales) {
        ModelAndView mav = new ModelAndView();
        if (afterSales == null) {
            return pageNotFound(request);
        }
        Integer modType = afterSales.getType();

        String sku = afterSales.getSku();
        afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);
        //本处适应第三方
        afterSales.setTraderType(ErpConst.ONE);

        adapterThirdParty(afterSales);
        afterSales = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
        afterSales.setSku(sku);

        mav.addObject("afterSales", afterSales);

        // 获取退货原因
        mav.addObject("sysList",  getSysOptionDefinitionList(3306));
        mav.addObject("domain", domain);

        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(afterSales.getOrderId());
        saleorder.setFlag(AfterSalesProcessEnum.getInstance(Objects.nonNull(modType) ? modType:afterSales.getType()).getType());
        saleorder.seteFlag("edit");
        saleorder.setCompanyId(ErpConst.NJ_COMPANY_ID);

        SaleorderVo saleorderVo = saleorderService.getSaleorderGoodsVoList(saleorder);

        mav.addObject("saleorder", saleorderVo);

        if (AfterSalesProcessEnum.AFTERSALES_AT.getType().equals(afterSales.getType())){
            afterSales.setType(AfterSalesProcessEnum.AFTERSALES_ATN.getCode());

            if (CollectionUtils.isNotEmpty(saleorderVo.getSgvList())){
                saleorderVo.setSgvList(saleorderVo.getSgvList().stream().filter(
                        item->ErpConst.ZERO.equals(item.getHaveInstallation())).collect(Collectors.toList()));
            }

        }


        switch (AfterSalesProcessEnum.getInstance(afterSales.getType())){
            case OTHER :{
                mav.setViewName("orderstream/aftersales/edit_afterSales_qt");
                return mav;
            }
            case AFTERSALES_TH: {
                mav.setViewName("orderstream/aftersales/edit_afterSales_th");
                break;
            }
            case AFTERSALES_HH : {
                mav.setViewName("orderstream/aftersales/edit_afterSales_hh");
                break;
            }
            case AFTERASALES_THIRD_WX:{
                if (afterSales.getAreaId() != null){
                    regionInfo(mav, afterSales.getAreaId());
                }
                mav.setViewName("orderstream/aftersales/edit_afterSales_other_wx");
                break;
            }
            case AFTERASALES_THIRD_AT:{
                if (afterSales.getAreaId() != null){
                    regionInfo(mav, afterSales.getAreaId());
                }
                mav.setViewName("orderstream/aftersales/edit_afterSales_other_at");
                break;
            }
            case AFTERSALES_WX :
            case AFTERSALES_AT : {
                if (afterSales.getAreaId() != null){
                    regionInfo(mav, afterSales.getAreaId());
                }
                mav.setViewName("orderstream/aftersales/edit_afterSales_atwx");
                break;
            }

            case AFTERSALES_ATY:
            case AFTERSALES_ATN:{
                if (afterSales.getAreaId() != null){
                    regionInfo(mav, afterSales.getAreaId());
                }
                mav.setViewName("orderstream/aftersales/edit_afterSales_at");
                break;
            }

            case AFTERSALES_TP : {
                mav.setViewName("orderstream/aftersales/edit_afterSales_tp");
                break;
            }
            case AFTERSALES_TK : {
                // 获取交易信息（订单实际金额，客户已付款金额）当订单实际金额=客户已付款金额时候，不允许退款
                Map<String, BigDecimal> saleorderDataInfo = saleorderService.getSaleorderDataInfo(saleorder.getSaleorderId());
                mav.addObject("saleorderDataInfo", saleorderDataInfo);
                mav.setViewName("orderstream/aftersales/edit_afterSales_tk");
                break;
            }
            case AFTERSALES_JZ : {
                mav.setViewName("orderstream/aftersales/edit_afterSales_jz");
                break;
            }
            case LOST_TICKET : {
                mav.setViewName("orderstream/aftersales/edit_afterSales_qp");
                break;
            }
            default:{

            }
        }
        return mav;
    }

    /**
     * 保存编辑售后
     *
     * @param request
     * @param afterSalesVo
     * @param afterSaleNums
     * @param fileName
     * @param fileUri
     * @param invoiceIds
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveEditAfterSales")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑售后")
    @NoRepeatSubmit
    public ModelAndView saveEditAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                           @RequestParam(required = false, value = "afterSaleNums") String[] afterSaleNums,
                                           @RequestParam(required = false, value = "fileName") String[] fileName,
                                           @RequestParam(required = false, value = "fileUri") String[] fileUri,
                                           @RequestParam(required = false, value = "invoiceIds") String[] invoiceIds) {
        logger.info("saveEditAfterSales start afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
        ModelAndView mav = new ModelAndView();
        if (!afterSalesService.checkWhenEditAfterSales(afterSalesVo.getAfterSalesId())){
            mav.addObject("message", "保存操作失败：" + "售后单在待确认状态下才支持编辑操作");
            return fail(mav);
        }
        User user = getSessionUser(request);
        afterSalesVo.setAfterSalesNum(afterSaleNums);
        afterSalesVo.setAttachName(fileName);
        afterSalesVo.setAttachUri(fileUri);
        afterSalesVo.setInvoiceIds(invoiceIds);
        afterSalesVo.setDomain(domain);
        //本处适应第三方
        afterSalesVo.setTraderType(ErpConst.ONE);
        if (ObjectUtils.notEmpty(afterSalesVo.getType()) && (AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSalesVo.getType()) || AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSalesVo.getType()))){
            afterSalesVo.setTraderType(ErpConst.THREE);
        }

        if(StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSalesVo.getType())){

            if(!cancelTypeService.cancelOutSaleOutMethod(afterSalesVo.getOrderNo(), CancelReasonConstant.EDIT_AFTER_ORDER)){
                mav.addObject("message","物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
                return fail(mav);
            }
        }

        ResultInfo<?> resultInfo = afterSalesOrderService.saveEditAfterSales(afterSalesVo, user);
        if (!resultInfo.getCode().equals(0)) {
            return fail(mav);
        }
        if(StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSalesVo.getType())){
            logicalSaleorderChooseServiceImpl.putSaleAfterReturnStartGoods(afterSalesVo.getOrderNo(),user);
        }
        mav.addObject("url", "./viewAfterSalesDetail.do?afterSalesId=" + resultInfo.getData());
        return success(mav);
    }

    /**
     * 售后付款申请审核弹层
     *
     * @param taskId
     * @param pass
     * @param type
     * @return
     */
    @FormToken(save=true)
    @ResponseBody
    @RequestMapping(value = "/initFinanceTaskComplement")
    public ModelAndView initFinanceTaskComplement( String taskId, Boolean pass,Integer type) {
        ModelAndView mv = new ModelAndView("orderstream/aftersales/complementFinanceTask");
        mv.addObject("pass", pass);
        mv.addObject("taskId", taskId);
        mv.addObject("type", type);
        return mv;
    }

    /**
     * 售后付款申请审核操作
     *
     * @param request
     * @param taskId
     * @param comment
     * @param pass
     * @return
     */
    @FormToken(remove=true)
    @ResponseBody
    @RequestMapping(value = "/complementFinanceTask")
    @SystemControllerLog(operationType = "edit", desc = "售后付款申请审核操作")
    public ResultInfo<?> complementFinanceTask(HttpServletRequest request, String taskId, String comment, Boolean pass) {
        logger.info("售后付款申请审核操作 start taskId:{}, comment:{}, pass:{}", taskId, comment, pass);
        User user = getSessionUser(request);
        Map<String, Object> variables = new HashMap<>(16);
        variables.put("pass", pass);
        // 审批操作
        try {
            // 如果审核没结束添加审核对应主表的审核状态
            Integer status = 0;
            TaskService taskService = processEngine.getTaskService();
            Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");

            String id = (String) taskService.getVariable(taskId, "id");
            String key = (String) taskService.getVariable(taskId, "key");
            String tableName = (String) taskService.getVariable(taskId, "tableName");
            String taskName = taskService.createTaskQuery().taskId(taskId).singleResult().getName();;

            PayApply payApply = payApplyService.getPayApplyInfo(idValue);
            if (payApply == null){
                return ResultInfo.error("退款支付申请不存在");
            }
            if (pass) {
                // 如果审核通过
                status = ErpConst.ZERO;
                if(tableName.equals("T_PAY_APPLY") && taskName.equals("财务制单")){
                    //VDERP-2193 如果是售后退货、退款生成的支付申请，那么在制单之前增加限制：退款金额不能大于账户余额
                    TraderCustomer traderCustomer = null;
                    AfterSales afterSales = afterSalesOrderService.getAfterSalesById(payApply.getRelatedId());
                    if (AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSales.getType()) ||
                            AfterSalesProcessEnum.AFTERSALES_TK.getCode().equals(afterSales.getType())){
                        traderCustomer = traderCustomerService.getTraderByPayApply(idValue);
                        if (payApply.getAmount().compareTo(traderCustomer.getAmount()) > 0){
                            return ResultInfo.error("退款金额大于账户余额，无法退款");
                        }
                    }
                    //制单
                    actionProcdefService.updateInfo(tableName, id, idValue, "IS_BILL", 1, 2);

                    ////VDERP-2193 如果是售后退货、退款生成的支付申请，那么制单成功则扣减余额
                    if (traderCustomer != null){
//                        traderCustomerService.updateTraderAmount(traderCustomer.getTraderId(),payApply.getAmount().multiply(new BigDecimal(-1)));
                        logger.info("售后订单在财务制单环节，余额扣减金额  id:{}, amount:{}",payApply.getRelatedId(),payApply.getAmount());
                    }
                }
            } else {
                // 如果审核不通过
                status = ErpConst.TWO;
                // 回写数据的表在db中
                variables.put("db", ErpConst.TWO);
                if(tableName != null && id != null && idValue != null && key != null){
                    actionProcdefService.updateInfo(tableName, id, idValue, key, ErpConst.TWO, ErpConst.TWO);
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);

                // 流程 paymentVerify:3:1792504 的财务审核节点，点击不通过，即售后退款财务审核不通过；
                // VDERP-2193 将售后退款流程中制单环节提前扣减余额的款项补加回来

                if ("T_PAY_APPLY".equals(tableName) && "财务审核".equals(taskName)){
                    if (SysOptionConstant.ID_518.equals(payApply.getPayType())){
                        AfterSales afterSales = afterSalesOrderService.getAfterSalesById(payApply.getRelatedId());
                        if (AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSales.getType()) ||
                                AfterSalesProcessEnum.AFTERSALES_TK.getCode().equals(afterSales.getType())){
//                            Optional.ofNullable(traderCustomerService.getTraderByPayApply(idValue))
//                                    .ifPresent(traderCustomer -> {
//                                        traderCustomerService.updateTraderAmount(traderCustomer.getTraderId(),payApply.getAmount());
//                                        logger.info("售后订单：{}在财务审核环节，审核不通过，余额增加金额：{}",payApply.getRelatedId(),
//                                                payApply.getAmount());
//                                    });
                        }
                    }
                }
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                    user.getUsername(), variables);
            // 如果未结束添加审核对应主表的审核状态
            if (!"endEvent".equals(complementStatus.getData())) {
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }

            return ResultInfo.success();

        } catch (Exception e) {
            logger.error("invoice after complementTask:", e);
            return ResultInfo.error("任务完成操作失败" + e.getMessage());
        }
    }

    /**
     * 执行退款运算前置处理
     * 更新退款状态和退款金额  售后detail表
     * @param afterSales
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/calRefund")
    @NoNeedAccessAuthorization
    public ResultInfo<?> calRefund(AfterSalesVo afterSales){
        ResultInfo<?> resultInfo = afterSalesOrderService.calRefund(afterSales);
        return resultInfo;
    }
    /**
     * 执行退款运算操作
     *
     * @param request
     * @param afterSales
     * @return
     */
    @ResponseBody
    @FormToken(remove=true)
    @RequestMapping(value = "/executeRefundOperation")
    @SystemControllerLog(operationType = "add",desc = "执行退款运算操作")
    public ResultInfo<?> executeRefundOperation(HttpServletRequest request, AfterSalesVo afterSales) {
        logger.info("executeRefundOperation afterSales:{}", JSON.toJSONString(afterSales));
        User user = getSessionUser(request);
//        if (Objects.nonNull(afterSales) && Objects.nonNull(afterSales.getSelectedRefundMethod()) && afterSales.getSelectedRefundMethod() > 0) {
//            afterSalesService.updateRefund(afterSales.getAfterSalesId(), afterSales.getSelectedRefundMethod());
//        }
        AfterSalesDetailVo afterSalesDetailVo = afterSalesService.getAfterSalesDetail(afterSales);
        afterSales.setRefund(afterSalesDetailVo.getRefund());
        if (null != afterSalesDetailVo && Lists.newArrayList(2,3,4).contains(afterSalesDetailVo.getRefundAmountStatus())) {
            logger.info("售后订单 {} 当前退款状态未完成", afterSales.getAfterSalesId());
            return ResultInfo.error("退款状态校验未通过");
        }
        afterSales.setTraderId(afterSalesDetailVo.getTraderId());
        if(AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSales.getType()) ||
                AfterSalesProcessEnum.AFTERSALES_TK.getCode().equals(afterSales.getType())){
            afterSales.setPayer(user.getCompanyName());
        }
        ResultInfo<?> resultInfo = afterSalesOrderService.executeRefundOperation(afterSales, user);

        //执行退款运算之后计算售后单的退款状态
        if (resultInfo == null || ResultInfo.error().getCode().equals(resultInfo.getCode()) || ErpConst.HUNDRED.equals(resultInfo.getCode())){
            //100也是执行退款运算失败的返回值
            return resultInfo;
        }
        afterSalesOrderService.refreshAmountRefundStatus(afterSales.getAfterSalesId());
        return resultInfo;
    }

    /**
     * 初始化订单完结页面
     *
     * @param request
     * @param afterSalesRecord
     * @return
     */
    @ResponseBody
    @RequestMapping(value="/saleorderComplete")
    public ModelAndView saleorderComplete(HttpServletRequest request, AfterSalesRecord afterSalesRecord){
        int afterSalesType = 0;
        if(StringUtil.isNotBlank(request.getParameter("afterSalesType"))){
            afterSalesType = Integer.valueOf(request.getParameter("afterSalesType"));
        }
        ModelAndView mav = new ModelAndView("orderstream/aftersales/saleorderComplete");
        List<SysOptionDefinition> afterSalesTypes ;

        switch (afterSalesType){
            case 756:{
                afterSalesTypes = getSysOptionDefinitionList(SysOptionConstant.ID_756);
                break;
            }
            case 757:{
                afterSalesTypes = getSysOptionDefinitionList(SysOptionConstant.ID_757);
                break;
            }
            case 758:{
                afterSalesTypes = getSysOptionDefinitionList(SysOptionConstant.ID_758);
                break;
            }
            case 759:{
                afterSalesTypes = getSysOptionDefinitionList(SysOptionConstant.ID_759);
                break;
            }
            default:{
                afterSalesTypes = getSysOptionDefinitionList(SysOptionConstant.ID_760);
                break;
            }
        }

        if(StringUtil.isNotBlank(request.getParameter("sku"))){
            mav.addObject("sku", request.getParameter("sku"));
        }
        mav.addObject("afterSalesTypes",afterSalesTypes);
        mav.addObject("type",request.getParameter("type"));
        mav.addObject("traderId",request.getParameter("traderId"));
        mav.addObject("formToken",request.getParameter("formToken"));
        mav.addObject("subjectType",request.getParameter("subjectType"));
        mav.addObject("orderId",request.getParameter("orderId"));
        mav.addObject("afterSalesRecord", afterSalesRecord);
        return mav;
    }

    /**
     * 确认完成
     *
     * @param request
     * @param afterSales
     * @return
     */
    @FormToken(remove=true)
    @ResponseBody
    @RequestMapping(value="/editConfirmComplete")
    @SystemControllerLog(operationType = "edit",desc = "售后订单确认完成")
    public ResultInfo<?> editConfirmComplete(HttpServletRequest request,AfterSalesVo afterSales){
        User user = getSessionUser(request);

        AfterSalesVo afterSalesVo = new AfterSalesVo();
        afterSalesVo.setAfterSalesId(afterSales.getAfterSalesId());
        afterSalesVo.setCompanyId(ErpConst.NJ_COMPANY_ID);
        afterSalesVo.setTraderId(afterSales.getTraderId());
        afterSalesVo.setTraderType(ErpConst.ONE);
        //本处适应第三方
        adapterThirdParty(afterSalesVo);

        AfterSalesVo afterSalesInfo = afterSalesOrderService.getAfterSalesVoDetail(afterSalesVo);

        //将完成原因，完成备注，完成人员存在数据库
        afterSales.setAfterSalesStatusUser(user.getUserId());
        afterSalesOrderService.updateAfterSalesById(afterSales);

        afterSales.setAfterSalesNo(afterSalesInfo.getAfterSalesNo());
        afterSales.setAtferSalesStatus(ErpConst.TWO);
        afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);
        afterSales.setAfterSalesStatusUser(user.getUserId());
        afterSales.setModTime(DateUtil.sysTimeMillis());
        afterSales.setUpdater(user.getUserId());

        //对售后单的流程判断金额进行计算
        // 销售订单退货

        switch (AfterSalesProcessEnum.getInstance(afterSalesInfo.getType())){
            case AFTERSALES_TH: {
                afterSales.setOrderTotalAmount(afterSalesInfo.getRefundAmount() != null ? afterSalesInfo.getRefundAmount() : BigDecimal.ZERO);
            break;
            }
            case AFTERSALES_HH: {
                //关联外借单
                List<LendOut>  lendoutList = warehouseOutService.getLendOutInfoList(afterSalesVo);
                if(CollectionUtils.isNotEmpty(lendoutList)) {
                    //存在进行中的外接单不允许关闭
                    ResultInfo.error("存在未完成的外借单，无法操作");
                }

                // 售后单对应的商品信息
                if (CollectionUtils.isEmpty(afterSalesInfo.getAfterSalesGoodsList())){
                    afterSales.setOrderTotalAmount(BigDecimal.ZERO);
                    break;
                }

                BigDecimal totalAmount = BigDecimal.ZERO;

                for (AfterSalesGoodsVo asg :  afterSalesInfo.getAfterSalesGoodsList()
                        .stream().filter(item -> item.getSaleorderNum() != null && item.getSaleorderPrice() != null)
                        .collect(Collectors.toList())) {
                    totalAmount = totalAmount.add(new BigDecimal(asg.getSaleorderNum()).multiply(asg.getSaleorderPrice()));
                }
                afterSales.setOrderTotalAmount(totalAmount);
                break;
            }
            case AFTERSALES_TK:{
                afterSales.setOrderTotalAmount(afterSalesInfo.getRefundAmount() != null ? afterSalesInfo.getRefundAmount() : BigDecimal.ZERO);
                break;
            }
            case AFTERSALES_WX: {
                afterSales.setOrderTotalAmount(afterSalesInfo.getServiceAmount() != null ? afterSalesInfo.getServiceAmount() : BigDecimal.ZERO);
                break;
            }
            default: {
                afterSales.setOrderTotalAmount(BigDecimal.ZERO);
                break;
            }
        }

        try {
            // 售后单完成
            afterSales.setVerifiesType(ErpConst.ONE);
            Map<String, Object> variableMap = new HashMap<>(16);
            // 开始生成流程(如果没有taskId表示新流程需要生成)
            variableMap.put("afterSales", afterSales);
            variableMap.put("afterSalesVo", afterSales);
            variableMap.put("processDefinitionKey", "overAfterSalesVerify");
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("relateTable", "T_AFTER_SALES");
            variableMap.put("relateTableKey", afterSalesInfo.getAfterSalesId());
            variableMap.put("businessKey", "overAfterSalesVerify_" + afterSalesInfo.getAfterSalesId());

            actionProcdefService.createProcessInstance(request, "overAfterSalesVerify",
                    "overAfterSalesVerify_" + afterSalesInfo.getAfterSalesId(), variableMap);
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "overAfterSalesVerify_" + afterSalesInfo.getAfterSalesId());
            if ( !"审核完成".equals(historicInfo.get("endStatus"))) {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<>(16);
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(taskId, ErpConst.ZERO);
                }

            }
            return ResultInfo.success();
        } catch (Exception e) {
            logger.error("activity error editConfirmComplete:", e);
            return ResultInfo.error("任务完成操作失败" + e.getMessage());
        }

    }

    /**
     * 适应第三方
     *
     * @param afterSalesVo
     * @return
     */
    private AfterSales adapterThirdParty(AfterSalesVo afterSalesVo) {
        if (afterSalesVo == null || afterSalesVo.getAfterSalesId() == null){
            return null;
        }
        AfterSales afterSale = afterSalesOrderService.getAfterSalesById(afterSalesVo.getAfterSalesId());
        if (afterSale != null && (AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSale.getType()) ||
                AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSale.getType()))) {
            afterSalesVo.setTraderType(ErpConst.THREE);
        }
        return afterSale;
    }

    @RequestMapping("/confirmBackInvoiceStatus")
    @ResponseBody
    public ResultInfo confirmBackInvoiceStatus(Integer afterSalesId){
        logger.info("confirmBackInvoiceStatus start afterSalesId:{}", afterSalesId);
        //更新状态为全部退票
        afterSalesUpgradeService.updateBackInvoiceStatus(afterSalesId,ErpConst.THREE);
        return ResultInfo.success();
    }

    /**
     * <b>Description:</b><br>
     * 订单合同回传删除
     *
     * @param request
     * @param attachment
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年7月24日 下午2:58:22
     */
    @ResponseBody
    @RequestMapping(value = "/contractReturnDel")
    @SystemControllerLog(operationType = "delete", desc = "订单合同回传删除")
    public ResultInfo<?> contractReturnDel(HttpServletRequest request, Attachment attachment) {
        ResultInfo<?> delSaleorderAttachment = saleorderService.delSaleorderAttachment(attachment);
        // 附件同步
        if (delSaleorderAttachment != null && ErpConst.ZERO.equals(delSaleorderAttachment.getCode())) {
            vedengSoapService.saleorderAttachmentSyncWeb(attachment.getAttachmentId(), true);
        }
        return delSaleorderAttachment;
    }

    @ResponseBody
    @RequestMapping(value = "/updateClientStatus")
    @NoNeedAccessAuthorization
    public ResultInfo<?> updateClientStatus(Integer afterSalesId, Integer oldClientStatus, Integer newClientStatus) {
        try {
            afterSalesClientStatusService.updateClientStatus(afterSalesId, oldClientStatus, newClientStatus);
        } catch (Exception e) {
            return ResultInfo.error();
        }
        return ResultInfo.success();
    }

    /**
     * 新增回访记录页面
     * @param request
     * @param afterSalesId
     * @return
     */
    @FormToken(save = true)
    @RequestMapping(value = "/addReturnVisitRecord")
    public ModelAndView addCommunicatePage(HttpServletRequest request, Integer afterSalesId) {
        User user = getSessionUser(request);
        ModelAndView mav = new ModelAndView("orderstream/aftersales/add_return_visit_record");
        mav.addObject("user", user);
        mav.addObject("afterSalesId", afterSalesId);
        return mav;
    }

    /**
     * 保存新增回访记录
     * @param returnVisitRecord
     * @return
     */
    @RequestMapping(value = "/saveReturnVisitRecord")
    @ResponseBody
    @FormToken(remove = true)
    public ResultInfo<?> saveReturnVisitRecord(ReturnVisitRecord returnVisitRecord) {
        returnVisitRecord.setAddTime(System.currentTimeMillis());
        Integer integer = returnVisitRecordService.insertReturnVisitRecord(returnVisitRecord);
        if (integer == 1) {
            return ResultInfo.success();
        } else {
            return ResultInfo.error("新增回访记录失败");
        }
    }

    /**
     * 虚拟商品是否可退检查是否选择一致
     * @param afterSalesId
     * @param isReturn
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/checkIsReturnAuditStatus")
    @NoNeedAccessAuthorization
    public ResultInfo<?> checkIsReturnAuditStatus(@RequestParam Integer afterSalesId,@RequestParam Integer isReturn) {
        try {
            AfterSaleAuditInfoEntity afterSaleAuditInfoEntity=new AfterSaleAuditInfoEntity();
            afterSaleAuditInfoEntity.setAfterSaleId(afterSalesId);
            afterSaleAuditInfoEntity.setAuditStatus(Constants.ONE);
            List<AfterSaleAuditInfoEntity> auditList = afterSaleAuditInfoService.findGoodsAuditList(afterSaleAuditInfoEntity);
            for (AfterSaleAuditInfoEntity entity : auditList) {
                if (!isReturn.equals(entity.getIsReturn())){
                    return ResultInfo.error("退货虚拟商品有分歧，请线下协商一致！");
                }
            }
        }catch (Exception e){
            logger.error("虚拟商品是否可退检查是否选择一致",e);
            return ResultInfo.error("退货虚拟商品有分歧，请线下协商一致！");
        }
       return ResultInfo.success();
    }
    /**
     * 设置任务候选人
     *
     * @param taskService
     * @param businessKey
     * @param manageAndAsistNameSet
     */
    private void setTaskCandidateUser(TaskService taskService, String businessKey, Set<String> manageAndAsistNameSet) {

        Task nextTask = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();

        for (String manageAndAsistName : manageAndAsistNameSet) {
            processEngine.getTaskService().addCandidateUser(nextTask.getId() + "", manageAndAsistName);
        }
        ;

    }

    /**
     * 发送站内信
     * @param userIdList
     * @param variables
     */
    private void sendAftersalesInfo(List<Integer> userIdList, Map<String, Object> variables,AfterSalesVo afterSalesVo) {
        String creatorName=afterSalesMapper.getCreatorNameByAfterSalesId(afterSalesVo.getAfterSalesId());
        Map<String, String> variableMap = new HashedMap();
        variableMap.put("afterSalesNo", afterSalesVo.getAfterSalesNo());
        String url = "./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId="
                + afterSalesVo.getAfterSalesId() + "&traderType=1";
        MessageUtil.sendMessage(27, userIdList, variableMap, url,creatorName);
    }

}
