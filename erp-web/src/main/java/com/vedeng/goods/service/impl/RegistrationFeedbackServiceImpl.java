package com.vedeng.goods.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.goods.common.constant.FeedBackProblemTypeEnum;
import com.vedeng.goods.domain.dto.RegistrationFeedbackRecordDto;
import com.vedeng.goods.domain.dto.RegistrationMessageReplyDto;
import com.vedeng.goods.domain.entity.RegistrationFeedbackRecordEntity;
import com.vedeng.goods.mapper.RegistrationFeedbackRecordMapper;
import com.vedeng.goods.service.RegistrationFeedbackService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2022−11-01 下午1:48
 * @description
 */
@Service
public class RegistrationFeedbackServiceImpl implements RegistrationFeedbackService {

    @Resource
    RegistrationFeedbackRecordMapper feedbackRecordMapper;

    /**
     * 发送站内信
     * @param registrationFeedbackDto
     */
    @Override
    public Boolean sendMessage(RegistrationFeedbackRecordDto registrationFeedbackDto) {
        //{$registrationNumber},{$fileType},{$problemType},{$PurposeType},{$goodsLevelNo},{$goodsPositionNo}
        //您有待处理的注册证问题反馈，请查看
        Map mesMap = new HashMap();
        mesMap.put("registrationNumber",registrationFeedbackDto.getRegistrationNumber());
        mesMap.put("fileType",registrationFeedbackDto.getFileType() == 1 ? "附件":"源文件");

        mesMap.put("problemType", FeedBackProblemTypeEnum.get(registrationFeedbackDto.getProblemType()).desc());
        if (null != registrationFeedbackDto.getPurposeType()){
            switch (registrationFeedbackDto.getPurposeType()){
                case 1:
                    mesMap.put("purposeType","投标/陪标");
                    break;
                case 2:
                    mesMap.put("purposeType","客户询价");
                    break;
                case 3:
                    mesMap.put("purposeType","其他");
                    break;
                default:
                    mesMap.put("purposeType","无");
            }
        }else {
            mesMap.put("purposeType","无");
        }
        if (StringUtils.isNotBlank(registrationFeedbackDto.getLevelName())){
            mesMap.put("goodsLevelNo",registrationFeedbackDto.getLevelName());
        }
        if (StringUtils.isNotBlank(registrationFeedbackDto.getPositionName())){
            mesMap.put("goodsPositionNo",StringUtils.isBlank(registrationFeedbackDto.getPositionName()) ? "无档位" :registrationFeedbackDto.getPositionName());
        }
        List<Integer> userIdList = Arrays.asList(registrationFeedbackDto.getAssignmentManagerId(), registrationFeedbackDto.getAssignmentAssistantId());
        Boolean result = MessageUtil.sendMessage(242, userIdList, mesMap, "/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=" + registrationFeedbackDto.getFirstEngageId());
        return result;
    }

    @Override
    public int messageReply(RegistrationMessageReplyDto registrationMessageReplyDto, HttpSession session) {

        //更新处理答复
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Integer userId = user.getUserId();
        Long modTime = System.currentTimeMillis();
        int updateNum = feedbackRecordMapper.messageReplyById(registrationMessageReplyDto.getRegistrationFeedbackRecordId(),registrationMessageReplyDto.getMessageReply(),modTime,userId);
        if (updateNum == 0){
            return 0;
        }
        //发送站内信
        //{$registrationNumber},{$fileType},{$problemType},{$MessageReply}
        //您反馈的注册证问题已处理，请查看
        RegistrationFeedbackRecordEntity feedbackRecordEntity = feedbackRecordMapper.getById(registrationMessageReplyDto.getRegistrationFeedbackRecordId());
        Map  mesMap = new HashMap<>();
        mesMap.put("registrationNumber",feedbackRecordEntity.getRegistrationNumber());
        mesMap.put("fileType",feedbackRecordEntity.getFileType() == 1 ? "附件":"源文件");
        switch (feedbackRecordEntity.getProblemType()){
            case 1:
                mesMap.put("problemType","印章缺失/不清晰");
                break;
            case 2:
                mesMap.put("problemType","打印内容模糊");
                break;
            case 3:
                mesMap.put("problemType","水印内容不当");
                break;
            case 4:
                mesMap.put("problemType","已过期");
                break;
            case 5:
                mesMap.put("problemType","其他");
                break;
            default:
                break;
        }
        mesMap.put("MessageReply",feedbackRecordEntity.getMessageReply());
        MessageUtil.sendMessage(243	, Arrays.asList(feedbackRecordEntity.getCreator()), mesMap, "/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=" + feedbackRecordEntity.getFirstEngageId() );
        return updateNum;
    }
}
