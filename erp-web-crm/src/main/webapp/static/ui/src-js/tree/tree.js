Vue.component('ui-tree', {
    template: `
        <div class="vd-ui-tree" :class="{'vd-ui-tree-vertical': classType === 'vertical'}">
            <div class="vd-ui-tree-wrap">
                <div class="vd-ui-add-wrap" :class="{'noUser': !confirmList.length}">
                    <div class="vd-ui-add" @click="showModal">
                        <i class="vd-ui_icon icon-add"></i>
                        <span class="vd-ui-add-btn">选择</span>
                    </div>
                    <div class="vd-ui-select-num" v-if="confirmList.length">（已添加{{ confirmList.length }}项）</div>
                </div>
                <div class="vd-ui-tree-select" v-if="confirmList.length">
                    <div class="vd-ui-select-item" v-for="(item, index) in confirmList" :key="index">
                        <img 
                            class="vd-ui-select-avatar" 
                            :src="item.aliasHeadPicture || GLOBAL.defaultAvatar" 
                            onerror="this.src='/static/image/crm-user-avatar.svg'"
                            v-if="avatar"
                        />
                        <div class="vd-ui-select-username">{{ item.userName }}</div>
                        <div class="vd-ui-select-del" @click="delUser(item)">
                            <i class="vd-ui_icon icon-delete"></i>
                        </div>
                    </div>
                </div>
                <div class="vd-ui-input-error" v-if="errorable">
                    <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                    <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
                </div>
            </div>


            <ui-dialog
                :visible.sync="isShowModal"
                :title="title"
                :width="width"
                maxHeight="650px"
                :noPadding="true"
                :noScroll="true"
            >
                <div class="vd-ui-tree-model-content">
                    <div class="vd-ui-modal-wrap">
                        <div class="vd-ui-tree-nav">
                            <div class="vd-ui-tree-search">
                                <div 
                                    class="input-wrap" 
                                    @mouseenter="handleEnter" 
                                    @mouseleave="handleLeave"
                                >
                                    <input 
                                        class="" 
                                        placeholder="搜索" 
                                        v-model="searchKey"
                                        @input="handlerInput" 
                                    />
                                    <i class="vd-ui_icon icon-search" v-if="!searchKey"></i>
                                    <i class="vd-ui_icon icon-error2" v-else-if="searchKey && (entered || isFocus)" @click="clearInput"></i>
                                </div>
                            </div>
                            <div class="vd-ui-tree-select-num">已选（{{ selectList.length }}）</div>
                        </div>
                        <div class="vd-ui-tree-panel">
                            <div class="vd-ui-tree-panel-left">
                                <div v-if="isShowFilter" class="vd-ui-tree-select-list">
                                    <div class="vd-ui-filter-wrap">
                                        <div class="ui-filter-loading" v-if="filterLoading">
                                            <i class="vd-ui_icon icon-loading"></i>
                                            <span>加载中...</span>
                                        </div>
                                        <div v-else-if="filterResult.length" class="ui-filter-list">
                                            <div 
                                                class="ui-filter-item" 
                                                v-for="(item, index) in filterResult" :key="index"
                                                @click.stop="clickUserChoose(item, true)"
                                            >
                                                <div class="ui-filter-item-checkbox" :class="{'active': selectIds.includes(item.userId)}">
                                                    <i class="vd-ui_icon icon-selected2"></i>
                                                </div>
                                                <div class="ui-filter-user">
                                                    <img 
                                                        class="ui-filter-avatar"
                                                        :src="item.aliasHeadPicture || GLOBAL.defaultAvatar" 
                                                        onerror="this.src='/static/image/crm-user-avatar.svg'"
                                                        v-if="avatar"
                                                    />
                                                    <span class="ui-filter-name" v-html="filterShow(item.userName)"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="ui-filter-loading">
                                            <i class="vd-ui_icon icon-info1"></i>
                                            <span>无匹配数据</span>
                                        </div>
                                    </div>
                                </div>
                                <ul
                                    v-else
                                    class="vd-ui-tree-select-list"
                                    ref="treeList"
                                    @click.stop
                                    @mousedown.prevent
                                >
                                    <li
                                        v-for="item in list" 
                                        :key="'depart' + item.departmentId"
                                    >
                                        <ui-tree-node :item="item" @pick="pick" :avatar="avatar"></ui-tree-node>
                                    </li>
                                </ul>
                            </div>
                            <div class="vd-ui-tree-panel-right">
                                <div class="vd-ui-tree-choose-wrap">
                                    <div
                                        class="ui-tree-choose-item"
                                        v-for="(item, index) in selectList" :key="index"
                                    >
                                        <img 
                                            class="ui-tree-choose-avatar" 
                                            :src="item.aliasHeadPicture || GLOBAL.defaultAvatar" 
                                            onerror="this.src='/static/image/crm-user-avatar.svg'"
                                            v-if="avatar"
                                        />
                                        <span class="ui-tree-choose-name">{{ item.userName }}</span>
                                        <i class="vd-ui_icon icon-delete" @click="toggleUserChoose(item, false)"></i>
                                    </div>
                                </div>
                                <div class="vd-ui-tree-choose-btns">
                                    <button class="confirm" @click="confirm">确定</button>
                                    <button class="cancel" @click="cancel">取消</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ui-dialog>

            <!-- <div class="vd-ui-tree-modal">
                <div class="vd-ui-tree-model-content">
                    <div class="vd-ui-modal-wrap">
                        <div class="vd-ui-modal-title">选择待办人
                            <div class="vd-ui-modal-close" @click="toggleModal(false)">
                                <i class="vd-ui_icon icon-delete"></i>
                            </div>
                        </div>
                        <div class="vd-ui-tree-nav">
                            <div class="vd-ui-tree-search">
                                <div 
                                    class="input-wrap" 
                                    @mouseenter="handleEnter" 
                                    @mouseleave="handleLeave"
                                >
                                    <input 
                                        class="" 
                                        placeholder="搜索" 
                                        v-model="searchKey"
                                        @input="handlerInput" 
                                    />
                                    <i class="vd-ui_icon icon-search" v-if="!searchKey"></i>
                                    <i class="vd-ui_icon icon-error2" v-else-if="searchKey && (entered || isFocus)" @click="clearInput"></i>
                                </div>
                            </div>
                            <div class="vd-ui-tree-select-num">已选（{{ selectList.length }}）</div>
                        </div>
                        <div class="vd-ui-tree-panel">
                            <div class="vd-ui-tree-panel-left">
                                <div v-if="isShowFilter" class="vd-ui-tree-select-list">
                                    <div class="vd-ui-filter-wrap">
                                        <div class="ui-filter-loading" v-if="filterLoading">
                                            <i class="vd-ui_icon icon-loading"></i>
                                            <span>加载中...</span>
                                        </div>
                                        <div v-else-if="filterResult.length" class="ui-filter-list">
                                            <div 
                                                class="ui-filter-item" 
                                                v-for="(item, index) in filterResult" :key="index"
                                                @click.stop="clickUserChoose(item, true)"
                                            >
                                                <div class="ui-filter-item-checkbox" :class="{'active': selectIds.includes(item.userId)}">
                                                    <i class="vd-ui_icon icon-selected2"></i>
                                                </div>
                                                <div class="ui-filter-user">
                                                    <img 
                                                        class="ui-filter-avatar" 
                                                        :src="item.aliasHeadPicture || GLOBAL.defaultAvatar" 
                                                        onerror="this.src='/static/image/crm-user-avatar.svg'"
                                                        v-if="avatar"
                                                    />
                                                    <span class="ui-filter-name" v-html="filterShow(item.userName)"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="ui-filter-loading">
                                            <i class="vd-ui_icon icon-info1"></i>
                                            <span>无匹配数据</span>
                                        </div>
                                    </div>
                                </div>
                                <ul
                                    v-else
                                    class="vd-ui-tree-select-list"
                                    ref="treeList"
                                    @click.stop
                                    @mousedown.prevent
                                >
                                    <li
                                        v-for="item in list" 
                                        :key="'depart' + item.departmentId"
                                    >
                                        <ui-tree-node :item="item" @pick="pick" :avatar="avatar"></ui-tree-node>
                                    </li>
                                </ul>
                            </div>
                            <div class="vd-ui-tree-panel-right">
                                <div class="vd-ui-tree-choose-wrap">
                                    <div
                                        class="ui-tree-choose-item"
                                        v-for="(item, index) in selectList" :key="index"
                                    >
                                        <img 
                                            class="ui-tree-choose-avatar" 
                                            :src="item.aliasHeadPicture || GLOBAL.defaultAvatar" 
                                            onerror="this.src='/static/image/crm-user-avatar.svg'"
                                            v-if="avatar"
                                        />
                                        <span class="ui-tree-choose-name">{{ item.userName }}</span>
                                        <i class="vd-ui_icon icon-delete" @click="toggleUserChoose(item, false)"></i>
                                    </div>
                                </div>
                                <div class="vd-ui-tree-choose-btns">
                                    <button class="confirm" @click="confirm">确定</button>
                                    <button class="cancel" @click="cancel">取消</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->

        </div>
    `,

    props: {
        data: {
            type: Array,
            required: true,
        },
        title: {
            type: String,
            default: '选择待办人'
        },
        classType: {
            type: String,
            default: ''
        },
        avatar: {
            type: Boolean,
            default: true
        },
        width: {
            type: String,
            default: '720px'
        },
        value: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    provide() {
        return {
            Tree: this
        }
    },
    data() {
        return {
            isShowModal: false,
            errorable: false,
            errorMsg: '',

            // 列表
            list: [], // 全部数据
            confirmList: [], // 确定选中的用户
            confirmIds: [], // 确定选中的id
            selectIds: [], // 已选用户Id列表
            selectList: [], // 已选用户列表
            allUser: [], // 所有user列表

            /* 搜索 */
            timer: null,
            entered: false,
            isFocus: false,
            searchKey: "",
            isShowFilter: false, // 搜索面板
            filterLoading: false,
            filterResult: [],
            confirmInitObj: {}
        }
    },
    watch: {
        data: {
            deep: true,
            immediate: true,
            handler (newV) {
                // console.log('data newV:', newV);
                if (newV && newV.length) {
                    this.initData();
                }
            }
        },
        selectIds: {
            handler(newV) {
                // console.log('selectIds:', this.selectIds);
                this.checkAllStatus(this.list);
            },
            deep: true,
        },
    },
    mounted() {
        this.$form.setValidEl(this);

        if(this.value && this.value.length) {
            this.selectIds = this.value;
            this.getConfirmList(this.list);
        }
    },
    
    methods: {
        // 数据初始化
        initData() {
            this.list = this.handlerData(JSON.parse(JSON.stringify(this.data)));
        },
        handlerData(arr) {
            arr.forEach(item => {
                this.$set(item, 'checked', false);
                this.$set(item, 'indeterminate', false);
                // this.$set(item, 'isOpen', true); // ???
                this.$set(item, 'isOpen', false);
                if (!item.userList) {
                    this.$set(item, 'userList', []);
                }
                if (!item.childDepartment) {
                    this.$set(item, 'childDepartment', []);
                }

                item.userList.forEach(user => {
                    this.$set(user, 'checked', false);
                });

                if (item.childDepartment.length) {
                    this.handlerData(item.childDepartment);
                }
            })
            return arr;
        },

        // 层级面板
        pick(data) {
            // console.log('tree pick:', data);
        },
        // 验证所有层级全选状态
        checkAllStatus(arr) {
            arr.forEach(item => {
                if (item.childDepartment && item.childDepartment.length) {
                    this.checkAllStatus(item.childDepartment);

                    // child最内层结束 => child为checked且本层层userList全选中
                    let userCheckCount = item.userList.filter(f => this.selectIds.includes(f.userId)); // 当前item下user选中数量
                    let childCheckCount = item.childDepartment.filter(f => f.checked);
                    let childIsChecked =  childCheckCount.length && (childCheckCount.length == item.childDepartment.length); // child是否全选中
                    let childIndeterminateCount = item.childDepartment.filter(f => f.indeterminate);
                    let childIsIndeterminate = Boolean(childCheckCount.length || childIndeterminateCount.length); // child是否部分选中

                    if (childIsChecked && (userCheckCount.length == item.userList.length)) { // user和child全选中
                        item.checked = true;
                        item.indeterminate = false;
                    }
                    else if (childIsIndeterminate || userCheckCount.length) { // user或child 未全部选中
                        item.checked = false;
                        item.indeterminate = true;
                    }
                    else { // user和child未选中
                        item.checked = false;
                        item.indeterminate = false;
                    }

                } else {
                    let userCheckCount = item.userList.filter(f => this.selectIds.includes(f.userId));

                    if (userCheckCount.length && (userCheckCount.length == item.userList.length)) { // user全选中
                        item.checked = true;
                        item.indeterminate = false;
                    }
                    else if (userCheckCount.length) { // user未全部选中
                        item.checked = false;
                        item.indeterminate = true;
                    }
                    else { // user未选中
                        item.checked = false;
                        item.indeterminate = false;
                    }
                }
            })

            console.log(this.list)
        },
        getConfirmList(list) {
            list.forEach(item => {
                if(item.userList && item.userList.length) {
                    item.userList.forEach(item => {
                        if(this.selectIds.includes(item.userId) && !this.confirmInitObj[item.userId]){
                            this.confirmList.push(item);
                            this.confirmInitObj[item.userId] = 1;
                        }
                    })
                }

                if(item.childDepartment && item.childDepartment.length) {
                    this.getConfirmList(item.childDepartment);
                }
            })
        },

        /* search */
        handleEnter(){
            this.entered = true;
        },
        handleLeave(){
            this.entered = false;
        },
        handleFocus() {
            this.isFocus = true;
        },
        handleBlur() {
            this.isFocus = false;
        },
        clearInput() {
            this.searchKey = '';
            this.isShowFilter = false;
            this.filterLoading = false;
            this.filterResult = [];
        },
        handlerInput(event) {
            let val = event.target.value && event.target.value.trim() || '';
            if (!val) {
                this.filterResult = [];
                this.isShowFilter = false;
                return;
            };

            // console.log('search 1')
            this.timer && clearTimeout(this.timer);
            this.timer = setTimeout(()=> {
                let val2 = event.target.value && event.target.value.trim() || '';
                // console.log('search 22', val2);
                if (val2) {
                    this.isShowFilter = true;
                    this.filterLoading = true;
                    let filterData = this.getFilterData(this.list, val);
                    this.filterResult = Array.from(new Set(filterData.map(item => item.userId)))
                        .map(userId => filterData.find(item => item.userId == userId));
                    this.filterLoading = false;
                } else {
                    this.filterResult = [];
                    this.isShowFilter = false;
                }
            }, 200);
        },
        getFilterData(arr, words) {
            let list = []; // 匹配结果
            words = words.toLowerCase();
            arr.forEach(item => {
                item.userList.forEach(user => {
                    let Name = (user.userName || '').toLowerCase();
                    if (Name.includes(words)) {
                        list.push(user);
                    }
                })
                if (item.childDepartment && item.childDepartment.length) {
                    let childList = this.getFilterData(item.childDepartment, words);
                    list.push(...childList);
                }
            });
            return list;
        },
        filterShow(userName) {
            let str = userName;
            const regExp = new RegExp(this.searchKey, 'g');
            str = str.replace(regExp, `<font color='#FF6600'">${this.searchKey}</font>`);
            return str;
        },
        // 点击用户
        clickUserChoose (user) {
            if (this.selectIds.includes(user.userId)) {
                let index = this.selectIds.indexOf(user.userId);
                this.selectIds.splice(index, 1);
                let index2 = this.selectList.findIndex(f => f.userId == user.userId);
                this.selectList.splice(index2, 1);
            } else {
                this.selectIds.push(user.userId);
                this.selectList.push(user);
            }
        },
        // 修改用户选项状态
        toggleUserChoose(item, bool) {
            if (bool) {
                if (!this.selectIds.includes(item.userId)) {
                    this.selectIds.push(item.userId);
                    this.selectList.push(item);
                }
            } else {
                let index = this.selectIds.indexOf(item.userId);
                this.selectIds.splice(index, 1);
                let index2 = this.selectList.findIndex(f => f.userId == item.userId);
                this.selectList.splice(index2, 1);
            }
        },


        toggleModal(bool) {
            this.isShowModal = bool;
        },
        // 删除已确认用户
        delUser(item) {
            this.toggleUserChoose(item, false);
            let index3 = this.confirmList.findIndex(f => f.userId == item.userId);
            this.confirmList.splice(index3, 1);
            this.$emit('input', this.selectIds);
            this.$emit('change', this.selectIds, this.confirmList);
            this.checkValid(this.selectIds || '');
        },
        confirm() {
            this.toggleModal(false);
            this.clearInput();

            this.confirmList = JSON.parse(JSON.stringify(this.selectList));
            this.$emit('input', this.selectIds);
            this.$emit('change', this.selectIds, this.confirmList);
            this.checkValid(this.selectIds || '');
        },
        cancel() {
            this.toggleModal(false);
        },

        showModal() {
            this.selectList = JSON.parse(JSON.stringify(this.confirmList));
            let arr = [];
            this.confirmList.forEach(item=> {
                arr.push(item.userId);
            })
            this.selectIds = arr;

            this.toggleModal(true);
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    }
})