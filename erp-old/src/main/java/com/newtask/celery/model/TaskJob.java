package com.newtask.celery.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/9 12:56
 */
@Data
public class TaskJob implements Serializable {

    /**
     * id
     */
    private Long jobId;

    /**
     * 唯一标识符(方法+入参)
     */
    private String uuid;

    /**
     * 任务状态 0 : 待执行 1：失效  2：失败过多 3 成功
     */
    private Integer status;

    /**
     * 执行队列
     */
    private Integer queue;

    /**
     * 执行方法key
     */
    private String handleIndex;

    /**
     * 有效时间
     */
    private Integer validMillis;

    /**
     * inputParams
     */
    private String inputParams;

    /**
     * 入参字节数组
     */
    private byte[] inputArgs;

    /**
     * 失败原因
     */
    private String errorMsg;

    /**
     * 是否需要幂等操作
     */
    private boolean checkIdempotent;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 更新时间
     */
    private Long modTime;


    @Override
    public String toString() {
        return "TaskJob{" +
                "jobId=" + jobId +
                ", uuid='" + uuid + '\'' +
                ", status=" + status +
                ", queue='" + queue + '\'' +
                ", handleIndex='" + handleIndex + '\'' +
                ", validMillis=" + validMillis +
                ", inputParams='" + inputParams + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                ", checkIdempotent=" + checkIdempotent +
                ", addTime=" + addTime +
                ", modTime=" + modTime +
                '}';
    }
}
