package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.entity.CommunicateVoiceAiLogEntity;
import com.vedeng.erp.trader.dto.CommunicateVoiceAiLogApiDto;

public interface CommunicateVoiceAiLogService {

    int insert(CommunicateVoiceAiLogEntity record);

    int insertSelective(CommunicateVoiceAiLogEntity record);

    int deleteByPrimaryKey(Long id);

    CommunicateVoiceAiLogEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CommunicateVoiceAiLogEntity record);

    int updateByPrimaryKey(CommunicateVoiceAiLogEntity record);

    Long saveCommunicateVoiceAiLog(CommunicateVoiceAiLogApiDto communicateVoiceAiLogApiDto);


    CommunicateVoiceAiLogApiDto queryLastComunicateVoiceLog(Integer communicateRecordId,String senceCode,String groupCode);
}

