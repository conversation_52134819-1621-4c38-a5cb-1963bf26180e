<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="销售工作台" scope="application" />
<%@ include file="../../common/common.jsp" %>
<%@ include file="../../component/remarkComponent.jsp" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/orderstream/workbench_staff.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/departmentUserSelect.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/yearMonthSelect.css?rnd=${resourceVersionKey}">
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/echarts/echarts-4.9.min.js?rnd=${resourceVersionKey}"></script>


<style>

    .clear-div {
        clear: both;
    }

    .cannot-click {
        pointer-events: none;
        cursor: default;
    }


</style>
<div class="wrap">
    <div class="main-l">
        <%--商机支持--%>
<%--        <c:if test="${businessSupportFlag}">--%>
<%--            <iframe id="capitalBillIframe" src="${pageContext.request.contextPath}/orderstream/saleorder/workbench/businessSupport.do" frameborder="0" style="width: 100%;border: 0; " onload="setIframeHeight(this)" scrolling="no">--%>
<%--            </iframe>--%>
<%--        </c:if>--%>
        <div class="block block-analysis">
            <div class="title">经营分析<span class="vd-icon icon-problem1 J-help-dlg-show"></span></div>
            <div class="title-option-wrap">
                <div class="option-item J-ym-select"></div>
                <div class="option-item option-user J-user-select"></div>
            </div>
            <div class="analysis-wrap">
                <div class="analysis-l">
                    <div class="circle-wrap">
                        <div class="circle">
                            <div class="circle-left J-circle-left"></div>
                            <div class="circle-right J-circle-right"></div>
                            <div class="circle-bottom-left"></div>
                            <div class="circle-bottom-right"></div>
                        </div>
                        <div class="rate-wrap">
                            <div class="rate-num J-analysis-paymentCompletionRate">-</div>
                            <div class="rate-txt">到款完成率</div>
                        </div>
                    </div>
                    <div class="analysis-num-list">
                        <div class="analysis-num-item">
                            <div class="item-num J-analysis-todayPayment">-</div>
                            <div class="item-txt">当日到款</div>
                        </div>
                        <div class="analysis-num-item">
                            <div class="item-num J-analysis-paymentTarget">-</div>
                            <div class="item-txt">到款目标</div>
                        </div>
                        <div class="analysis-num-item">
                            <div class="item-num J-analysis-actualPayment">-</div>
                            <div class="item-txt">实际到款</div>
                        </div>
                        <div class="analysis-num-item">
                            <div class="item-num J-analysis-grossProfitRate">-</div>
                            <div class="item-txt">毛利率</div>
                        </div>
                        <div class="analysis-num-item">
                            <div class="item-num J-analysis-ownBrandSalesRatio">-</div>
                            <div class="item-txt">自有品牌销售占比</div>
                        </div>
                        <div class="analysis-num-item">
                            <div class="item-num blue J-analysis-expectedOrderOpportunityAmount">-</div>
                            <div class="item-txt">预计成单商机金额</div>
                        </div>
                        <div class="analysis-num-item">
                            <div class="item-num J-analysis-opportunitySatisfactionRate">-</div>
                            <div class="item-txt">商机满足率</div>
                        </div>
                    </div>
                </div>
                <div class="analysis-r">
                    <div class="J-chart-wrap" style="display: none;">
                        <div class="chart-placeholder" id="loudou-chart"></div>
                        <div class="analysis-txt-list">
                            <div class="analysis-txt-item show">
                                <div class="item-l">
                                    <div class="item-label">线索</div>
                                    <div class="item-num J-clue-num">-</div>
                                </div>
                            </div>
                            <div class="analysis-txt-item show">
                                <div class="item-l">
                                    <div class="item-label">商机</div>
                                    <div class="item-num J-opportunity-num">-</div>
                                </div>
                                <div class="item-r">
                                    <div class="current-num J-opportunity-conversionRate">-</div>
                                    <div class="avg-num J-opportunity-conversionRateAvg">-</div>
                                </div>
                            </div>
                            <div class="analysis-txt-item J-preliminaryNegotiation-wrap">
                                <div class="item-l">
                                    <div class="item-label">初步洽淡</div>
                                    <div class="item-num J-preliminaryNegotiation-num">-</div>
                                </div>
                                <div class="item-r">
                                    <div class="current-num J-preliminaryNegotiation-conversionRate">-</div>
                                    <div class="avg-num J-preliminaryNegotiation-conversionRateAvg">-</div>
                                </div>
                            </div>
                            <div class="analysis-txt-item J-opportunityVerification-wrap">
                                <div class="item-l">
                                    <div class="item-label">商机验证</div>
                                    <div class="item-num J-opportunityVerification-num">-</div>
                                </div>
                                <div class="item-r">
                                    <div class="current-num J-opportunityVerification-conversionRate">-</div>
                                    <div class="avg-num J-opportunityVerification-conversionRateAvg">-</div>
                                </div>
                            </div>
                            <div class="analysis-txt-item J-preliminaryPlan-wrap">
                                <div class="item-l">
                                    <div class="item-label">初步方案</div>
                                    <div class="item-num J-preliminaryPlan-num">-</div>
                                </div>
                                <div class="item-r">
                                    <div class="current-num J-preliminaryPlan-conversionRate">-</div>
                                    <div class="avg-num J-preliminaryPlan-conversionRateAvg">-</div>
                                </div>
                            </div>
                            <div class="analysis-txt-item J-finalPlan-wrap">
                                <div class="item-l">
                                    <div class="item-label">最终方案</div>
                                    <div class="item-num J-finalPlan-num">-</div>
                                </div>
                                <div class="item-r">
                                    <div class="current-num J-finalPlan-conversionRate">-</div>
                                    <div class="avg-num J-finalPlan-conversionRateAvg">-</div>
                                </div>
                            </div>
                            <div class="analysis-txt-item J-winOrder-wrap">
                                <div class="item-l">
                                    <div class="item-label">赢单</div>
                                    <div class="item-num J-winOrder-num">-</div>
                                </div>
                                <div class="item-r">
                                    <div class="current-num J-winOrder-conversionRate">-</div>
                                    <div class="avg-num J-winOrder-conversionRateAvg">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="analysis-chart-empty J-chart-empty-wrap" style="display: none;">
                        <div class="analysis-empty-img"></div>
                        <div class="analysis-empty-txt">该范围内无数据无法生成漏斗</div>
                    </div>
                </div>
            </div>
            <div class="analysis-loading J-analysis-loading">
                <span class="loading-img"></span>
            </div>
        </div>
        <div class="block">
            <div class="title">通话汇总(个人)</div>
            <a class="title-link"
               href="javascript:void(0);"
               onclick="javascript:window.top.openLastVoice('${pageContext.request.contextPath}/ai/communicateInfo.do?selectedTab=summary');">查看全部<span class="vd-icon icon-right"></span></a>
            <div class="contact-sum-list">
                <div class="contact-sum-item">
                    <div class="item-num" id="pie1Text"></div>
                    <div class="item-txt">总通话数</div>
                </div>
                <div class="contact-sum-item">
                    <div class="item-num" id="pie2Text"></div>
                    <div class="item-txt">通话总时长（分）</div>
                </div>
                <div class="contact-sum-item">
                    <div class="item-num">${empty workbenchData.businessProfileDataInfo.callNum ? 0:workbenchData.businessProfileDataInfo.callNum}</div>
                    <div class="item-txt">今日外呼数</div>
                </div>
                <div class="contact-sum-item">
                    <div class="item-num">${empty workbenchData.businessProfileDataInfo.callTime ? 0:workbenchData.businessProfileDataInfo.callTime}</div>
                    <div class="item-txt">今日外呼时长（分）</div>
                </div>
                <div class="contact-sum-item">
                    <div class="item-num" id="pie3Text"></div>
                    <div class="item-txt">意向客户预测</div>
                </div>
            </div>
               <!-- <div class="biz-cnt">
                <div class="biz-list" style="padding: 3px !important;">
                    <div class="biz-item" >
                        <div class="pieDiv" id="pie1" style="height:200px;"></div>

                        <div class="item-txt" id="pie1Text">总通话数：</div>

                    </div>
                    <div class="biz-item">
                        <div class="pieDiv"  id="pie2" style="height:200px;"></div>
                        <div class="item-txt" id="pie2Text">通话总时长：</div>
                    </div>
                    <div class="biz-item" >
                        <div class="pieDiv" id="pie3" style="height:200px;"></div>
                        <div class="item-txt" id="pie3Text">意向客户预测：</div>
                    </div>
                </div>
            </div> -->
        </div>
        <!-- 商机管理模块 -->
        <input id="userLevelType" type="hidden" value="${user.levelType}">
        
<%--        <c:if test="${user.isB2B eq 1}">--%>
<%--        <div class="block">--%>
<%--            <div class="title">商机管理</div>--%>
<%--            <a class="addtitle title-link"--%>
<%--               href="javascript:void(0);"--%>
<%--               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",--%>
<%--											              "link":"./workbench/bussinesschance/index.do?",--%>
<%--											              "title":"商机工作台"}' >查看全部</a>--%>
<%--            <div class="biz-cnt">--%>
<%--                <div class="biz-title">--%>
<%--                    <i class="fa fa-bar-chart"></i>--%>
<%--                    商机概况--%>
<%--                </div>--%>
<%--                <div class="biz-list">--%>
<%--                    <c:if test="${user.levelType eq 1}">--%>
<%--                        <div class="biz-item">--%>
<%--                            <div class="item-num red strong">${workbenchData.businessChanceDateInfo.sort}</div>--%>
<%--                            <div class="item-txt">当月五行业绩排行</div>--%>
<%--                        </div>--%>
<%--                    </c:if>--%>
<%--                    <div class="biz-item">--%>
<%--                        <div class="item-num">${workbenchData.businessChanceDateInfo.totalBussinessAmount}元</div>--%>
<%--                        <div class="item-txt">商机总额</div>--%>
<%--                    </div>--%>
<%--                    <div class="biz-item">--%>
<%--                        <div class="item-num">${workbenchData.businessChanceDateInfo.totalBussinessNum}个</div>--%>
<%--                        <div class="item-txt">商机总数</div>--%>
<%--                    </div>--%>
<%--                    <div class="biz-item">--%>
<%--                        <div class="item-num">${workbenchData.businessChanceDateInfo.yesterdayTotalBussinessAmount}元</div>--%>
<%--                        <div class="item-txt">昨日新增商机金额</div>--%>
<%--                    </div>--%>
<%--                    <div class="biz-item">--%>
<%--                        <div class="item-num">${workbenchData.businessChanceDateInfo.yesterdayTotalBussinessNum}个</div>--%>
<%--                        <div class="item-txt">昨日新增商机数量</div>--%>
<%--                    </div>--%>
<%--                    <div class="biz-item">--%>
<%--                        <div class="item-num">${workbenchData.businessChanceDateInfo.thisMouthTotalAmount}元</div>--%>
<%--                        <div class="item-txt">预计本月到款</div>--%>
<%--                    </div>--%>
<%--                    <div class="biz-item">--%>
<%--                        <div class="item-num">${workbenchData.businessChanceDateInfo.thisMouthBussinessNum}个</div>--%>
<%--                        <div class="item-txt">预计本月到款商机数</div>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--            </div>--%>
<%--        </div>--%>
<%--        </c:if>--%>

        <!-- 待办事项 -->
        <div class="block">
            <div class="title">待办事项</div>
            <div class="todo-list">
<%--                <c:if test="${user.isB2B eq 1}">--%>
<%--                &lt;%&ndash;未处理商机&ndash;%&gt;--%>
<%--                <div class="todo-block">--%>
<%--                    <div class="todo-block-title">--%>
<%--                        <div class="title-icon icon-1"></div>--%>
<%--                        未处理商机--%>
<%--                    </div>--%>
<%--                    <div class="todo-block-list">--%>
<%--                        <div class="todo-item">--%>
<%--                            <a id="unHandleNum" class="addtitle item-num"--%>
<%--                               href="javascript:void(0);"--%>
<%--                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",--%>
<%--							              "link":"./businessChance/indexFromWorkbenchUnHandle.do",--%>
<%--							              "title":"未处理商机数量"}'>--%>
<%--                                0--%>
<%--                            </a>--%>
<%--                        </div>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                &lt;%&ndash;待再次沟通商机&ndash;%&gt;--%>
<%--                <div class="todo-block">--%>
<%--                    <div class="todo-block-title">--%>
<%--                        <div class="title-icon icon-1"></div>--%>
<%--                        待再次沟通商机--%>
<%--                    </div>--%>
<%--                    <div class="todo-block-list">--%>
<%--                        <div class="todo-item">--%>
<%--                            <a id="waitCommunicateNum" class="addtitle item-num"--%>
<%--                               href="javascript:void(0);"--%>
<%--                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",--%>
<%--							              "link":"./businessChance/indexFromWorkbenchWaitCommunicate.do",--%>
<%--							              "title":"待再次沟通商机数量"}'>--%>
<%--                                0--%>
<%--                            </a>--%>
<%--                        </div>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--                </c:if>--%>
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-1"></div>
                        待审核订单
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="noVerifyTotal" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoVerifyOrder}?VERIFY_STATUS=3<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"待审核订单"}'>
                                暂无
                            </a>
                            <div class="item-label">总数</div>
                        </div>
                        <div class="todo-item">
                            <a id="noVerifyOnLine" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoVerifyOrder}?VERIFY_STATUS=3&&ORDER_TYPE=1,5,7<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"待审核订单"}'>
                                暂无
                            </a>
                            <div class="item-label">线上</div>
                        </div>
                        <div class="todo-item">
                            <a id="noVerifyOffLine" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoVerifyOrder}?VERIFY_STATUS=3&&ORDER_TYPE=0,8,9<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"待审核订单"}'>
                                暂无
                            </a>
                            <div class="item-label">线下</div>
                        </div>
                    </div>
                </div>
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-2"></div>
                        未收款/部分收款订单
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="noPaymentAll" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoPayment}?PAYMENT_STATUS=0<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"未收款订单"}'>
                                暂无
                            </a>
                            <div class="item-label">未收款</div>
                        </div>
                        <div class="todo-item">
                            <a id="noPaymentSome" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoPayment}?PAYMENT_STATUS=1<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"部分收款订单"}'>
                                暂无
                            </a>
                            <div class="item-label">部分收款</div>
                        </div>
                        <div class="todo-item">
                            <a id="noPaymentMoeny" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoPayment}?PAYMENT_STATUS=0,1<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"未收款/部分收款订单"}'>
                                暂无
                            </a>
                            <div class="item-label">金额</div>
                        </div>
                    </div>
                </div>
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-1"></div>
                        新业绩考核待办
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="noInvoiceOrderNum" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"ezadmin/list/list-performanceAssessment",
											              "title":"业绩考核待办列表"}'>
                                暂无
                            </a>
                            <div class="item-label">未开票</div>
                        </div>
                        <div class="todo-item">
                            <a id="contractNotReviewedOrderNum" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"ezadmin/list/list-performanceAssessment",
											              "title":"业绩考核待办列表"}'>
                                暂无
                            </a>
                            <div class="item-label">合同未审核</div>
                        </div>
                        <div class="todo-item">
                            <a id="confirmationNotApprovedOrderNum" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"ezadmin/list/list-performanceAssessment",
											              "title":"业绩考核待办列表"}'>
                                暂无
                            </a>
                            <div class="item-label">确认单未审核</div>
                        </div>
                    </div>
                </div>
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-1"></div>
                        营销任务
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="marketPlanNum" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
							              "link":"./ezadmin/list/list-marketplan",
							              "title":"销售待办清单"}'>
                                0
                            </a>
                            <div class="item-label">需沟通客户</div>
                        </div>
                    </div>
                </div>
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-1"></div>
                        拜访任务
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="visitrecordNum" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"visitrecordlist<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
							              "link":"./ezadmin/list/list-visitrecordlist?VISITOR_ID=${user.userId}&VISIT_SUCCESS=1",
							              "title":"拜访计划列表"}'>
                                0
                            </a>
                            <div class="item-label">待拜访客户</div>
                        </div>
                    </div>
                </div>
                <div class="todo-block flex-2" <c:if test="${user.levelType ne 1}">style="width: 100%" </c:if> >
                    <div class="todo-block-title">
                        <div class="title-icon icon-2"></div>
                        未回款订单（单位：天）
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="totalNum" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoMoney}",
											              "title":"未回款订单"}'>
                                暂无
                            </a>
                            <div class="item-label">总数</div>
                        </div>
                        <div class="todo-item">
                            <a id="amount" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoMoney}",
											              "title":"未回款订单"}'>
                                暂无
                            </a>
                            <div class="item-label">金额</div>
                        </div>
                        <div class="todo-item">
                            <a id="notOverdue" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoMoney}?DAYS_OF_OVERDUE_FLAG=1",
											              "title":"未回款订单"}'>
                                暂无
                            </a>
                            <div class="item-label">未逾期</div>
                        </div>
                        <div class="todo-item">
                            <a id="betweenZeroAndSixtyAmount" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoMoney}?DAYS_OF_OVERDUE_FLAG=2",
											              "title":"未回款订单"}'>
                                暂无
                            </a>
                            <div class="item-label">0＜逾期≤60</div>
                        </div>
                        <div class="todo-item">
                            <a id="moreThanSixtyAmount" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoMoney}?DAYS_OF_OVERDUE_FLAG=3",
											              "title":"未回款订单"}'>
                                暂无
                            </a>
                            <div class="item-label">逾期＞60</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待跟踪事项 -->
        <div class="block">
            <div class="title">待跟踪事项</div>
            <div class="todo-list">
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-3"></div>
                        审核中订单
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="verifyingTotal" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleVerifying}?VERIFY_STATUS=0<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"审核中订单"}'>
                                暂无
                            </a>
                            <div class="item-label">总数</div>
                        </div>
                        <div class="todo-item">
                            <a id="verifyingOnLine" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleVerifying}?VERIFY_STATUS=0&&ORDER_TYPE=1,5,7<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"审核中订单"}'>
                                暂无
                            </a>
                            <div class="item-label">线上</div>
                        </div>
                        <div class="todo-item">
                            <a id="verifyingOffLine" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleVerifying}?VERIFY_STATUS=0&&ORDER_TYPE=0,8,9<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"审核中订单"}'>
                                暂无
                            </a>
                            <div class="item-label">线下</div>
                        </div>
                    </div>
                </div>
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-1"></div>
                        未采购/部分采购订单
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="noPurchaseAll" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoPurchase}?PURCHASE_STATUS=0<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"未采购订单"}'>
                                暂无
                            </a>
                            <div class="item-label">未采购</div>
                        </div>
                        <div class="todo-item">
                            <a id="noPurchaseSome" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoPurchase}?PURCHASE_STATUS=1<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"部分采购订单"}'>
                                暂无
                            </a>
                            <div class="item-label">部分采购</div>
                        </div>
                    </div>
                </div>
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-2"></div>
                        未发货/部分发货订单
                    </div>
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="noSendGoodsAll" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoSendGoods}?DELIVERY_STATUS=0<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"未发货订单"}'>
                                暂无
                            </a>
                            <div class="item-label">未发货</div>
                        </div>
                        <div class="todo-item">
                            <a id="noSendGoodsSome" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoSendGoods}?DELIVERY_STATUS=1<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"部分发货订单"}'>
                                暂无
                            </a>
                            <div class="item-label">部分发货</div>
                        </div>
                        <div class="todo-item">
                            <a id="noNoSend" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoNoSend}?ARRIVAL_STATUS=0,1<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"逾期交付"}'>
                                暂无
                            </a>
                            <div class="item-label">逾期交付</div>
                        </div>
                    </div>
                </div>
                <div class="todo-block">
                    <div class="todo-block-title">
                        <div class="title-icon icon-2"></div>
                        未完结售后单
                    </div>
                    <!-- 不需要跳转的样式看这边 -->
                    <div class="todo-block-list">
                        <div class="todo-item">
                            <a id="noCompletedAfterSaleReadyVerify" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoCompletedAfterSale}?STATUS=0<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"未完结售后单"}'>
                                暂无
                            </a>
                            <div class="item-label">待审核</div>
                        </div>
                        <div class="todo-item">
                            <a id="noCompletedAfterSaleVerifing" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoCompletedAfterSale}?STATUS=1<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"未完结售后单"}'>
                                暂无
                            </a>
                            <div class="item-label">审核中</div>
                        </div>
                        <div class="todo-item">
                            <a id="noCompletedAfterSaleIng" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoCompletedAfterSale}?ATFER_SALES_STATUS=1<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"未完结售后单"}'>
                                暂无
                            </a>
                            <div class="item-label">进行中</div>
                        </div>
                        <div class="todo-item">
                            <a id="noCompletedAfterSaleRejected" class="addtitle item-num"
                               href="javascript:void(0);"
                               tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleNoCompletedAfterSale}?STATUS=3<c:if test="${user.levelType ne 1}">&itemSearchDateKey=ADD_TIME&itemSearchDateValueStart=EZ_PRE_30_DAY</c:if>",
											              "title":"未完结售后单"}'>
                                暂无
                            </a>
                            <div class="item-label">已驳回</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="main-r">
        <div class="block">
            <input type="hidden" class="J-crm-task-url" value="${crmTodoTaskUrl}">
            <tags:welcome_task/>
        </div>
        <div class="block">
            <div class="title">快速开始/便捷导航</div>
            <div class="quick-list">
                <a class="addtitle"
                   href="javascript:void(0);"
                   tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"./trader/customer/index.do",
											              "title":"客户列表"}'>客户列表</a>
                <a class="addtitle"
                   href="javascript:void(0);"
                   tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"./order/bussinesschance/saleindex.do",
											              "title":"商机列表"}'>商机列表</a>
                <a class="addtitle"
                   href="javascript:void(0);"
                   tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"./order/quote/index.do",
											              "title":"报价列表"}'>报价列表</a>
                <a class="addtitle"
                   href="javascript:void(0);"
                   tabTitle='{"num":"saleorderWorkbench<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"${saleorderList}",
											              "title":"订单列表"}'>订单列表</a>
<c:if test="${fn:length(user.subUserIdList) > 1}">
                <a class="" onclick="$('#tongjiForm').submit();"
                   href="javascript:void(0);"
                   >团队通话报表</a>
</c:if>

            </div>
        </div>
    </div>
</div>
<form id="tongjiForm" target="_blank" method="post" action="http://*************:8080/webroot/decision/view/report?viewlet=%25E6%2595%25B0%25E5%25AD%2597%25E5%258C%2596%25E8%2590%25A5%25E9%2594%2580%25E9%2583%25A8%252F%25E6%25B2%259F%25E9%2580%259A%25E8%25AE%25B0%25E5%25BD%2595%25E6%25AF%258F%25E6%2597%25A5%25E6%258A%25A5%25E8%25A1%25A8-%25E8%25B4%259D%25E5%25A3%25B3%25E5%258A%25A9%25E7%2590%2586.cpt&ref_t=design&ref_c=c74722f3-77ac-4ccb-bb6d-8c55302ea1e9">
    <input type="hidden" name="userId" value="${user.userId}"/>
</form>

<div class="help-dlg-wrap J-help-dlg-wrap">
    <div class="help-dlg-container">
        <div class="help-dlg-main">
            <div class="help-dlg-header">
                <div class="help-dlg-title">经营分析-字段说明</div>
                <span class="vd-icon icon-delete J-help-dlg-close"></span>
            </div>
            <div class="help-dlg-body">
                <div class="help-dlg-cnt">
                    <div class="help-dlg-block">
                        <div class="help-dlg-block-title">业绩目标</div>
                        <div class="help-list-wrap">
                            <div class="help-list">
                                <div class="help-item">
                                    <div class="help-label">今日到款：</div>
                                    <div class="help-txt">仅显示当前自然日内的到款，根据选择人员合计。</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">到款目标：</div>
                                    <div class="help-txt">由营销支持部按月配置</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">实际到款：</div>
                                    <div class="help-txt">筛选范围内，根据客户打款流水并匹配订单结款统计，退货后会扣减。（账期回款后纳入）</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">到款完成率：</div>
                                    <div class="help-txt">筛选范围内，实际到款/到款目标</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">毛利率：</div>
                                    <div class="help-txt">筛选范围内，所有订单的(销售总额-成本总额)/销售总额。 该计算仅为暂估，非最终业绩考核毛利（业绩考核是以开票订单为准）</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">自有品牌销售占比：</div>
                                    <div class="help-txt">筛选范围内，可发货订单中自有品牌产品的金额占比</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">预计成单商机金额：</div>
                                    <div class="help-txt">筛选范围内，非关闭和赢单的商机预计成单金额合计</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">商机满足率：</div>
                                    <div class="help-txt">筛选范围内，（到款金额+商机中的预计成单金额）/到款目标，未关联商机订单的到款不纳入计算</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="help-dlg-block">
                        <div class="help-dlg-block-title">商机漏斗</div>
                        <div class="help-list-wrap">
                            <div class="help-list line-2">
                                <div class="help-item">
                                    <div class="help-label">线索数：</div>
                                    <div class="help-txt">筛选范围内，线索和商机总数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">商机数：</div>
                                    <div class="help-txt">筛选范围内，商机总数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">商机转化率：</div>
                                    <div class="help-txt">筛选范围内，商机数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">商机转化率均值：</div>
                                    <div class="help-txt">全公司的，商机数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">初步洽淡数：</div>
                                    <div class="help-txt">筛选范围内，处于初步洽淡的商机数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">初步洽淡转化率：</div>
                                    <div class="help-txt">筛选范围内，初步洽淡数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">初步洽淡转化均值：</div>
                                    <div class="help-txt">全公司的，初步洽淡数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">商机验证数：</div>
                                    <div class="help-txt">筛选范围内，处于商机验证的商机数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">商机验证转化率：</div>
                                    <div class="help-txt">筛选范围内，商机验证数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">商机验证转化均值：</div>
                                    <div class="help-txt">全公司的，商机验证数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">初步方案数：</div>
                                    <div class="help-txt">筛选范围内，处于初步方案的商机数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">初步方案转化率：</div>
                                    <div class="help-txt">筛选范围内，初步方案数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">初步方案转化均值：</div>
                                    <div class="help-txt">全公司的，初步方案数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">最终方案数：</div>
                                    <div class="help-txt">筛选范围内，处于最终方案的商机数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">最终方案转化率：</div>
                                    <div class="help-txt">筛选范围内，最终方案数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">最终方案转化均值：</div>
                                    <div class="help-txt">全公司的，最终方案数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">赢单数：</div>
                                    <div class="help-txt">筛选范围内，处于赢单的商机数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">赢单转化率：</div>
                                    <div class="help-txt">筛选范围内，赢单数/线索数</div>
                                </div>
                                <div class="help-item">
                                    <div class="help-label">赢单转化均值：</div>
                                    <div class="help-txt">全公司的，赢单数/线索数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
 $(document).ready(function() {

    $.ajax({
        url: "${pageContext.request.contextPath}/communicate/summary.do",
        type: "post",
        timeout: 60000,
        data: {
            "date":getFormattedDate()
        },
        dataType : "json",
        success: function(data) {
            var pieMap1 = data.data.pieMap.sumCommunicatNum;
            showPie1(pieMap1);
            var pieMap2 = data.data.pieMap.sumCommunicateTime;
            //var pieMapValue = pieMap2;
            showPie2(pieMap2);
            var pieMap3 = data.data.pieMap.traderForecastNum;
            //var pieMapValue = pieMap3[0];

            showPie3(pieMap3);

        }
    });

    var initLoudou = function(data) {
        var chartDom = document.getElementById('loudou-chart');
        var myChart = echarts.init(chartDom);
        var option;

        option = {
            title: {
                show: false
            },
            tooltip: {
                show: false
            },
            series: [{
                name: 'Funnel',
                type: 'funnel',
                left: 0,
                top: 0,
                bottom: 0,
                width: '100%',
                min: 0,
                maxSize: '100%',
                sort: function (a, b) {},
                gap: 0,
                label: {
                    show: true,
                    position: 'inside'
                },
                labelLine: {
                    length: 10,
                    lineStyle: {
                        width: 1,
                        type: 'solid'
                    }
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 1
                },
                emphasis: {
                    label: {
                    fontSize: 20
                    }
                },
                data: data
            }]
        };

        option && myChart.setOption(option);
    };

    var nDate = new Date();
    var currentYear = nDate.getFullYear();
    var currentMonth = nDate.getMonth() + 1;
    var selectDate = {
        year: currentYear,
        month: currentMonth
    }

    new YearMonthSelect({
        el: '.J-ym-select',
        value: {
            year: currentYear,
            month: currentMonth
        },
        max: {
            year: currentYear,
            month: currentMonth
        },
        min: {
            year: 2025,
            month: 1
        },
        change: function(value) {
            selectDate = value;

            getAnalysisData()
        }
    })

    $('.J-analysis-loading').css('display', 'flex');

    var getAnalysisData = function(data) {
        $('.J-analysis-loading').css('display', 'flex');
        var $right = $('.J-circle-right')[0];
        var $left = $('.J-circle-left')[0];

        $right.style.transition = '';
        $right.style.transition = '';
        $left.style.transform = '';
        $left.style.transform = '';
        $right.style.opacity = '';

        $.ajax({
            url: '/orderstream/saleorder/getWorkbenchBusinessAnalysis.do',
            data: JSON.stringify({
                year: selectDate.year,
                month: selectDate.month,
                userIds: selectedIds
            }),
            headers: {
                'Content-Type': 'application/json'
            },
            type: 'post',
            dataType: 'json',
            success: function(res) {
                $('.J-analysis-loading').hide();
                if(res.code === 0) {
                    var resData = res.data;

                    
                    $('.J-analysis-todayPayment').html(resData.todayPayment ? '￥' + resData.todayPayment : '-'); //当日到款
                    $('.J-analysis-actualPayment').html(resData.actualPayment ? '￥' + resData.actualPayment : '-'); //实际到款
                    $('.J-analysis-ownBrandSalesRatio').html(resData.ownBrandSalesRatio || '-'); //自有品牌销售占比
                    $('.J-analysis-expectedOrderOpportunityAmount').html(resData.expectedOrderOpportunityAmount ? '￥' + resData.expectedOrderOpportunityAmount : '-'); //预计成单商机金额
                    $('.J-analysis-opportunitySatisfactionRate').html(resData.opportunitySatisfactionRate || '-'); //商机满足率

                    if(resData.paymentTarget && resData.paymentTarget != 0) {
                        $('.J-analysis-paymentTarget').html('￥' + resData.paymentTarget); //到款目标
                        $('.J-analysis-paymentCompletionRate').html(resData.paymentCompletionRate || '-'); //到款完成率
                    } else {
                        $('.J-analysis-paymentTarget').html('未维护目标'); //到款目标
                        $('.J-analysis-paymentCompletionRate').html('未维护目标'); //到款完成率
                    }

                    //到款完成率进度条渲染
                    var paymentCompletionRate = resData.paymentCompletionRateNumber || 0;

                    if(resData.paymentCompletionRateNumber > 1) {
                        paymentCompletionRate = 1;
                    } else if (resData.paymentCompletionRateNumber < 0){
                        paymentCompletionRate = 0;
                    }

                    if (paymentCompletionRate <= 0.5) {  
                        $right.style.transform = 'rotate(' + paymentCompletionRate * 360 + 'deg)';
                    } else {    
                        $right.style.transform = `rotate(180deg)`
                        $right.style.transition = `opacity 0s step-end .2s, transform .2s linear`;
                        $right.style.opacity = 0

                        $left.style.transition = 'transform ' + ((paymentCompletionRate - 0.5) / 0.5 * 0.2) + 's linear .2s';
                        $left.style.transform = 'rotate(' + (paymentCompletionRate * 360 - 180) + 'deg)';
                    }

                    //
                    var clueNum = resData.clueNum;
                    var businessChanceNum = 0;

                    if(resData.validationInfoMap && resData.validationInfoMap.opportunity) {
                        businessChanceNum = resData.validationInfoMap.opportunity.num || 0;
                    }

                    if(!clueNum || !businessChanceNum) {
                        $('.J-chart-empty-wrap').show();
                        $('.J-chart-wrap').hide();
                    } else {
                        $('.J-chart-empty-wrap').hide();
                        $('.J-chart-wrap').show();

                        var colorArray = ['#0087E0', '#09f', '#1fa5ff', '#4cb7ff', '#7fccff', '#b3e1ff', '#e0f3ff'];
                        var dataList = [
                            { value: clueNum, itemStyle: { color: colorArray[0], borderColor: colorArray[0] } },
                            { value: businessChanceNum, itemStyle: { color: colorArray[1], borderColor: colorArray[1] } },
                        ];

                        var resDataList = resData.validationInfoMap || {};

                        $.each(['preliminaryNegotiation', 'opportunityVerification', 'preliminaryPlan', 'finalPlan', 'winOrder'], function(i, item) {
                            if(resDataList[item] && resDataList[item].num && resDataList[item].num !== 0) {
                                dataList.push({ value: resDataList[item].num, itemStyle: { color: colorArray[dataList.length], borderColor: colorArray[dataList.length] } })
                            }
                        })

                        $('#loudou-chart').after('<div class="chart-placeholder" id="loudou-chart"></div>').remove();
                        $('#loudou-chart').height(45 * dataList.length + 'px')
                        initLoudou(dataList);
                    
                        $('.J-clue-num').html(clueNum);

                        var listData = resData.validationInfoMap || {};
                        $.each(['opportunity', 'preliminaryNegotiation', 'opportunityVerification', 'preliminaryPlan', 'finalPlan', 'winOrder'], function(i, item) {
                            $('.J-' + item + '-num').html(listData[item].num);
                            $('.J-' + item + '-conversionRate').html(listData[item].conversionRate);
                            $('.J-' + item + '-conversionRateAvg').html('均值' + listData[item].conversionRateAvg);

                            if(listData[item].conversionRateNumber >= listData[item].conversionRateAvgNumber) {
                                $('.J-' + item + '-conversionRate').addClass('green').removeClass('orange');
                            } else {
                                $('.J-' + item + '-conversionRate').addClass('orange').removeClass('green');
                            }

                            if(!listData[item].num || listData[item].num == 0) {
                                $('.J-' + item + '-wrap').hide().removeClass('show');
                            } else {
                                $('.J-' + item + '-wrap').show().addClass('show');
                            }

                            $('.J-chart-wrap .analysis-txt-item').removeClass('bg')

                            $('.J-chart-wrap .analysis-txt-item.show').each(function(i) {
                                if(i%2 === 0) {
                                    $(this).addClass('bg');
                                }
                            })
                        })
                    }
                }
            }
        })

        $.ajax({
            url: '/orderstream/saleorder/getWorkbenchBusinessAnalysisMaoli.do',
            data: JSON.stringify({
                year: selectDate.year,
                month: selectDate.month,
                userIds: selectedIds
            }),
            headers: {
                'Content-Type': 'application/json'
            },
            type: 'post',
            dataType: 'json',
            success: function(res) {
                if(res.code === 0) {
                    var resData = res.data;

                    $('.J-analysis-grossProfitRate').html(resData || '-'); //毛利率
                }
            }
        })
    }

    var selectedIds = [];
    var selectedItems = [];

    $.ajax({
        url: '/api/userwork/getMyFullDepartmentTree.do',
        dataType: 'json',
        success: function (res) {
            if (res.code === 0) {

                var list = [];

                try {
                    list = res.data.childOrganization[0].childOrganization
                } catch (error) {

                }

                var getAllUsers = function(list) {
                    $.each(list, function(i, item) {
                        if(item.users && item.users.length) {
                            $.each(item.users, function(j, user) {
                                if(selectedIds.indexOf(user.userId) === -1) {
                                    selectedIds.push(user.userId);
                                    selectedItems.push({
                                        userId: user.userId,
                                        userName: user.userName,
                                        avatar: user.aliasHeadPicture || ''
                                    })
                                }
                            })
                        }

                        if(item.childOrganization && item.childOrganization.length) {
                            getAllUsers(item.childOrganization);
                        }
                    })
                };
                
                getAllUsers(list);

                DepartmentUserSelect.initSelect({
                    placeholderEl: '.J-user-select',
                    open: true,
                    listData: list,
                    selected: selectedIds,
                    selectedItems: selectedItems,
                    must: '请选择销售',
                    change: function(value, items) {
                        selectedIds = value;
                        selectedItems = items;

                        getAnalysisData();
                    }
                })

                getAnalysisData();
            }
        }
    })

    //帮助说明
    $('.J-help-dlg-show').click(function() {
        $('.J-help-dlg-wrap').fadeIn(220);
    })

    $('.J-help-dlg-close').click(function() {
        $('.J-help-dlg-wrap').fadeOut(220);
    })
});


</script>

<script type="text/javascript" src='<%= basePath %>static/js/orderstream/saleorder/workbench_staff.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/new/js/common/departmentUserSelect.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/new/js/common/yearMonthSelect.js?rnd=${resourceVersionKey}'></script>
<script>
    $("#maoliTitle").click(function(){
        // 然后使用 layer.msg 显示弹框，并设置 5 秒后自动关闭
        parent.layer.msg($(this).attr("title"), {
            time: 5000, // 5秒后自动关闭
            // 你还可以设置其他的参数，比如样式等
        });
    })


    $("#subMaoli").click(function(){
        var index = parent.layer.load(1);
        // 发起Ajax请求
        $.ajax({
            url: '/orderstream/saleorder/getMaoliList.do', // 你的数据源URL
            type: 'get', // 请求方式，根据实际情况修改
            dataType: 'json', // 返回数据类型，根据实际情况修改
            success: function(data) {
                // 请求成功，关闭加载动画
                parent.layer.close(index);
                // 初始化累加器
                let totalProfit = 0;
                let totalSales = 0;
                let totalTxt = 0;
                $(data.data).each(function(i, item) {
                    if (item.grossProfitAmount && item.totalSaleAmount) {
                        totalProfit += parseFloat(item.grossProfitAmount);
                        totalSales += parseFloat(item.totalSaleAmount);
                    }
                });
                if(totalSales >0){
                    const profitPercentage = (totalProfit / totalSales) * 100;
                    const formattedProfitPercentage = profitPercentage.toFixed(2);
                    totalTxt = formattedProfitPercentage+"";
                }

                //将th的宽度限制为80px;
                var content = "<div name='showSub' style='padding:10px;'><table id='popupTable' style='border:solid 1px #999;'><tr>" +"<td colspan='2' style='overflow: hidden; text-align: center; font-size:14px; border:solid 1px #999; padding-top: 5px; padding-bottom: 5px;'>总计毛利："+totalTxt+"%</td></tr>"+
                "<tr><td style='max-width: 40px; width: 40px; overflow: hidden; text-align: center; font-size:14px; border:solid 1px #999; padding-top: 5px; padding-bottom: 5px;'>下属</td>" +
                "<td style='max-width: 40px; width: 40px; overflow: hidden; text-align: center; font-size:14px; border:solid 1px #999; padding-top: 5px; padding-bottom: 5px;'>当月毛利</td></tr>";

                $(data.data).each(function(i, item) {
                    if(item.saleUserName && item.saleUserName !=undefined){
                        content += "<tr><td style='max-width: 40px; width: 40px; overflow: hidden; text-align: center; font-size: 14px; border:solid 1px #999; padding-top: 5px; padding-bottom: 5px;'>" + item.saleUserName + "</td>" +
                            "<td style='max-width: 40px; width: 40px; overflow: hidden; text-align: center; font-size: 14px; border:solid 1px #999; padding-top: 5px; padding-bottom: 5px;'>" + ((item.grossProfitRate && item.grossProfitRate !='')?item.grossProfitRate:"无") + "</td></tr>";
                    }
                });



                content  = content+"</tbody></table></div>";
                var indexD = parent.layer.myopen({
                    type: 1,
                    title: '下属毛利',
                    content: content, // 在弹出框中包含一个表格元素
                    area: ['220px', '360px'], // 设置宽度和高度，宽度自适应，高度200px
                    success: function(layero, index) {

                    }
                });
            },
            error: function() {
                // 请求失败，关闭加载动画
                parent.layer.close(index);
                // 这里可以添加一些处理错误的代码
            }
        });
    })

</script>
<script>

    function getFormattedDate() {
        var date = new Date();
        var year = date.getFullYear();
        // getMonth() 返回的月份是从 0 开始的，所以我们需要加 1
        var month = ("0" + (date.getMonth() + 1)).slice(-2);
        var day = ("0" + date.getDate()).slice(-2);
        var hours = ("0" + date.getHours()).slice(-2);
        var minutes = ("0" + date.getMinutes()).slice(-2);
        var seconds = ("0" + date.getSeconds()).slice(-2);
        return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
    }

   
    // var pie1Json = '[{"value":1048,"name":"产品推广"},{"value":735,"name":"商机跟进"},{"value":580,"name":"客户开发"},{"value":484,"name":"报价响应"},{"value":300,"name":"商务处理"},{"value":484,"name":"售后"},{"value":300,"name":"其他"}]';
    // var pie1Data = JSON.parse(pie1Json);

    function showPie1(pie1Data){
        var sum = pie1Data.reduce(function(prev, cur) {
            return prev + cur.value;
        }, 0);

        $('#pie1Text').html(sum);
    }


    //第二张图开始
    // var pie2Json = '[{ "value": 1048,"name": "通话总时长" }]';
    // var pie2Data = JSON.parse(pie2Json);
    function showPie2(pie2Data) {
        var sum = pie2Data.reduce(function(prev, cur) {
            return prev + cur.value;
        }, 0);
       
        $('#pie2Text').html(sum);
    }

    //第三张图开始
    //第二张图开始
    // var pie3Json = '[{ "value": 10,"name": "意向客户预测" }]';
    // var pie3Data = JSON.parse(pie3Json);
    function showPie3(pie3Data) {
        var sum = pie3Data.reduce(function(prev, cur) {
            return prev + cur.value;
        }, 0);
        
        $('#pie3Text').html(sum);
    }

</script>
<%@ include file="../../common/footer.jsp" %>