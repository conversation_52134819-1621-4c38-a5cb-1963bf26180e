<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />

<style>
    .radio-item {
        display: block;
        margin-bottom: 15px;
        margin-inline: 25px;
        margin-top: 15px;
    }
</style>
<div id="app">
    <div style="margin-left: 10px">
        <el-descriptions title="基础信息"  direction="vertical" :column="1">
            <el-descriptions-item label="客户类型">【分销】：医疗器械经销商（包括可进行分销合作的厂家）<br>【终端】：医疗终端（包括医院、卫生机构、个人、企业等）</el-descriptions-item>
            <el-descriptions-item label="客户有效性">【有效】：正常营业<br>
                【无效】：营业范围变更（以后不做医疗器械了）；公司注销</el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="经营终端大类"  direction="vertical" :column="1">
            <el-descriptions-item label="等级医院">二级及以上医院，包含公立和非公</el-descriptions-item>
            <el-descriptions-item label="基层医疗">二级以下医院及医疗机构，包含公立和非公</el-descriptions-item>
        </el-descriptions>
    </div>



</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {
            return {
                radio:'',
                traderCustomerTerminalList: []

            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            sendThis(this);
        },

        methods: {
        }
    })
</script>
