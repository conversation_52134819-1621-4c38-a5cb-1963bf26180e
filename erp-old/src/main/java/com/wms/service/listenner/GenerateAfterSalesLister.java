package com.wms.service.listenner;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.model.TraderAddress;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 售后订单生成处理器
 *
 * <AUTHOR>
 */
@Service("generateAfterSalesLister")
public class GenerateAfterSalesLister extends AbstractErpListenner {
    public static Logger logger = LoggerFactory.getLogger(GenerateAfterSalesLister.class);

    @Value("${peer_list_filter_time}")
    private Long peerListFilterTime;

    @Value("${oss_url}")
    protected String domain;

    @Autowired
    private AfterSalesService afterSalesService;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private TraderAddressMapper traderAddressMapper;


    @Override
    protected void doOnActionHappen(Object[] params) throws Exception {
        AfterSalesVo afterSalesInfo = (AfterSalesVo) params[0];

        boolean auditPass = (boolean) params[1];

        logger.info("销售售后订单审核通过 afterSalesInfo:{}", JSON.toJSONString(afterSalesInfo));

        AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesInfo.getAfterSalesId());

        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(afterSales.getOrderId());
        saleorder = saleorderMapper.getSaleOrderBaseInfo(afterSales.getOrderId());

        if (auditPass && System.currentTimeMillis() > peerListFilterTime) {
            //处理生成销售售后直发商品
            dealGenerateDeliveryDirectAfterSales(saleorder, afterSales, afterSales.getAfterSalesId());
        }
    }


    /**
     * 处理生成销售售后直发商品
     *
     * @param afterSales
     * @param afterSalesId
     */
    private void dealGenerateDeliveryDirectAfterSales(Saleorder saleorder, AfterSales afterSales, Integer afterSalesId) {
        logger.info("处理生成销售售后直发商品 afterSalesNo:{}, afterSalesId:{}", afterSales.getAfterSalesNo(), afterSalesId);
        if (!SysOptionConstant.ID_539.equals(afterSales.getType()) && !SysOptionConstant.ID_540.equals(afterSales.getType())) {
            logger.info("非销售退换货跳过直发商品信息的处理 afterSalesNo:{}", afterSales.getAfterSalesNo());
            return;
        }

        List<AfterSalesGoodsVo> afterSalesGoodsVosList = afterSalesGoodsMapper.getAllAftersalesGoodsList(afterSalesId);
        if (CollectionUtils.isEmpty(afterSalesGoodsVosList)) {
            logger.error("检索售后商品信息异常 afterSalesId:{}", afterSalesId);
            throw new RuntimeException("检索售后商品信息异常 afterSalesId:{}" + afterSalesId);
        }

        //直发商品信息
        List<AfterSalesGoodsVo> deliveryDirectGoods = afterSalesGoodsVosList.stream()
                .filter(item -> ErpConst.ONE.equals(item.getDeliveryDirect()) &&
                        !ErpConst.ZERO.equals(item.getOrderDetailId()) &&
                        item.getNum() > 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(deliveryDirectGoods)) {
            logger.info("退换货销售售后单中无直发商品信息 afterSalesId:{}, afterSalesNo:{}", afterSalesId, afterSales.getAfterSalesNo());
            return;
        }

        logger.info("直发商品信息 deliveryDirectGoods:{}", JSON.toJSONString(deliveryDirectGoods));

        ArrayList<Buyorder> buyOrderListForMessage = new ArrayList<>();

        HashMap<String, AfterSalesVo> orderNoAfterSalesMap = new HashMap<>(16);

        deliveryDirectGoods.forEach(goodsInfo -> {
            List<Buyorder> buyOrderList = buyorderMapper.selectSalesBuyorderList(goodsInfo.getAfterSalesGoodsId())
                    .parallelStream().filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(buyOrderList)) {
                logger.info("该售后商品未关联对应的采购商品 afterSalesId:{},afterSalesGoodsId:{}",
                        afterSalesId, goodsInfo.getAfterSalesGoodsId());
                return;
            }

            buyOrderListForMessage.addAll(buyOrderList);

            List<Buyorder> unConditionList = buyOrderList.stream().filter(item -> ErpConst.ONE.equals(item.getLockedStatus()) ||
                            ErpConst.ZERO.equals(item.getStatus()) ||
                            ErpConst.THREE.equals(item.getStatus()))
                    .collect(Collectors.toList());


            if (CollectionUtils.isNotEmpty(unConditionList)) {
                logger.info("关联采购单中存在不符合条件的采购单 afterSalesId:{}, goodsId:{},  unConditionList:{}",
                        afterSalesId, goodsInfo.getAfterSalesGoodsId(), JSON.toJSONString(unConditionList));
                return;
            }

            if (buyOrderList.size() > 1 && !goodsInfo.getNum().equals(goodsInfo.getSaleorderNum())) {
                logger.info("关联多个采购单且未全部退货 afterSalesId:{}, afterSaleGoodsId:{}", afterSalesId, goodsInfo.getAfterSalesGoodsId());
                return;
            }

            buyOrderList.forEach(buyOrder -> {
                User creator = userMapper.selectByPrimaryKey(buyOrder.getCreator());
                BuyorderGoods buyOrderGood = buyorderGoodsMapper.selectBuySkuInfo(buyOrder.getBuyorderId(), goodsInfo.getSku());

                Integer returnNum = buyOrderList.size() > 1 ? buyOrderGood.getNum() : goodsInfo.getNum();

                if (orderNoAfterSalesMap.containsKey(buyOrder.getBuyorderNo())) {
                    AfterSalesVo afterSalesVo = orderNoAfterSalesMap.get(buyOrder.getBuyorderNo());
                    afterSalesVo.setAfterSalesNum((String[]) ArrayUtils.add(afterSalesVo.getAfterSalesNum(),
                            buyOrderGood.getBuyorderGoodsId() + "|" + returnNum + "|1|" + buyOrderGood.getGoodsId()));
                    String[] skus = afterSalesVo.getComments().split("SKU");
                    afterSalesVo.setComments(skus[0] + "SKU" + goodsInfo.getSku() + "," + skus[1]);
                    if (!SysOptionConstant.ID_539.equals(afterSales.getType())) {
                        return;
                    }
                    afterSalesVo.setRefundAmount(afterSalesVo.getRefundAmount()
                            .add(buyOrderGood.getPrice().multiply(new BigDecimal(returnNum))));
                } else {
                    AfterSalesVo afterSalesVo = getAfterSalesVo(afterSales, buyOrder, creator, buyOrderGood, returnNum);
                    orderNoAfterSalesMap.put(buyOrder.getBuyorderNo(), afterSalesVo);
                }
            });
        });

        logger.info("直发销售售后生成采购售后 afterSalesId:{}, orderNoAfterSalesMap:{}", afterSalesId, JSON.toJSONString(orderNoAfterSalesMap));
        orderNoAfterSalesMap.values().forEach(item -> afterSalesService.saveAddAfterSales(item, userMapper.selectByPrimaryKey(item.getCreator())));

        if (CollectionUtils.isEmpty(buyOrderListForMessage)) {
            return;
        }


        buyOrderListForMessage.stream().filter(orderInfo -> !ErpConst.THREE.equals(orderInfo.getStatus()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(Buyorder::getBuyorderId))), ArrayList::new)).forEach(orderInfo -> {
                    HashMap<String, String> paramMap = new HashMap<>(16);
                    paramMap.put("saleorderNo", saleorder.getSaleorderNo());
                    paramMap.put("buyorderNo", orderInfo.getBuyorderNo());
                    MessageUtil.sendMessage(230, Collections.singletonList(orderInfo.getCreator()), paramMap,
                            "./order/buyorder/viewBuyorder.do?buyorderId=" + orderInfo.getBuyorderId(), saleorder.getOptUserName());
                });
    }

    /**
     * 生成售后运输对象
     *
     * @param afterSales
     * @param buyOrder
     * @param creator
     * @param buyOrderGood
     * @param num
     * @return
     */
    private AfterSalesVo getAfterSalesVo(AfterSales afterSales, Buyorder buyOrder, User creator,
                                         BuyorderGoods buyOrderGood, Integer num) {
        AfterSalesVo afterSalesVo = new AfterSalesVo();
        if (SysOptionConstant.ID_539.equals(afterSales.getType())) {
            //销售退货->采购退货
            afterSalesVo.setType(SysOptionConstant.ID_546);
            //默认退还至公司账户，换货无需传值
            afterSalesVo.setRefund(ErpConst.ONE);
            afterSalesVo.setRefundAmount(buyOrderGood.getPrice().multiply(new BigDecimal(num)));
        } else if (SysOptionConstant.ID_540.equals(afterSales.getType())) {
            //销售换货->采购换货
            afterSalesVo.setType(SysOptionConstant.ID_547);
        }
        //售后原因--其他
        afterSalesVo.setReason(559);
        afterSalesVo.setOrderId(buyOrder.getBuyorderId());
        afterSalesVo.setOrderNo(buyOrder.getBuyorderNo());
        afterSalesVo.setCompanyId(ErpConst.ONE);
        afterSalesVo.setTraderId(buyOrder.getTraderId());
        afterSalesVo.setComments("因销售单" + afterSales.getOrderNo() + "中的SKU" + buyOrderGood.getSku() + "发生直发售后退货或换货");
        List<String> afterSalesNums = new ArrayList<>();
        //默认直发
        afterSalesNums.add(buyOrderGood.getBuyorderGoodsId() + "|" + num + "|1|" + buyOrderGood.getGoodsId());
        afterSalesVo.setAfterSalesNum(afterSalesNums.toArray(new String[0]));
        afterSalesVo.setTraderContactId(buyOrder.getTraderContactId());
        TraderAddress traderAddress = traderAddressMapper.getAddressInfoById(buyOrder.getTraderAddressId(), null);
        if (traderAddress != null){
            afterSalesVo.setTakeMsg(buyOrder.getTraderAddressId() + "|" + traderAddress.getAreaId() + "|" + buyOrder.getTraderArea() + "|" + buyOrder.getTraderAddress());
        }
        afterSalesVo.setTraderType(ErpConst.TWO);
        afterSalesVo.setSubjectType(SysOptionConstant.ID_536);
        afterSalesVo.setStatus(ErpConst.ZERO);
        //自动创建的采购售后单
        afterSalesVo.setCreateType(ErpConst.TYPE_1);
        afterSalesVo.setValidStatus(ErpConst.TYPE_0);
        afterSalesVo.setAtferSalesStatus(ErpConst.TYPE_0);
        afterSalesVo.setServiceUserId(buyOrder.getUserId());
        afterSalesVo.setDomain(domain);
        afterSalesVo.setPayee(creator != null ? creator.getCompanyName() : "");
        afterSalesVo.setCreator(buyOrder.getCreator());
        afterSalesVo.setDeliveryDirectAfterSalesId(afterSales.getAfterSalesId());
        return afterSalesVo;
    }
}
