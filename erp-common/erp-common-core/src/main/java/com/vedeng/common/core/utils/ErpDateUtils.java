package com.vedeng.common.core.utils;

import cn.hutool.core.date.DateUtil;
import com.common.constants.Contant;
import com.vedeng.common.core.enums.PatternEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

public class ErpDateUtils {

    public static Logger logger = LoggerFactory.getLogger(DateUtil.class);
    public static String DATE_FORMAT = "yyyy-MM-dd";
    // 长日期格式
    public static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static String getStringDateNow2() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter format = DateTimeFormatter.ofPattern(TIME_FORMAT);
        return now.format(format);
    }

    /**
     * 将长整型数字转换为日期格式的字符串
     *
     * @param time
     * @param format
     * @return
     */
    public static String convertString(long time, String format) {
        if (time > 0L) {
            if (StringUtils.isBlank(format)) {
                format = TIME_FORMAT;
            }
            SimpleDateFormat sf = new SimpleDateFormat(format);
            Date date = new Date(time);
            return sf.format(date);
        }
        return "";
    }
    public static long getNowByDateFormat(){
        try {
            SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT);
            return sf.parse(sf.format(sysTimeMillis())).getTime();
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return 0l;
    }


    /**
     * 获取当前系统的日期
     *
     * @return
     */
    public static long sysTimeMillis() {
        return System.currentTimeMillis();
    }

    /**
     * Date 转 str 格式化
     */
    public static String format(Date date, PatternEnum patternEnum) {
        if (Objects.isNull(date)) {
            return "";
        }
        return DateUtil.format(date, patternEnum.getPattern());
    }
    /**
     * Date 转 00:00:00
     * @param date
     * @return
     */
    public static Date getStartOfDay(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 设置时间为当天的00:00:00
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startOfDay = calendar.getTime();
        return startOfDay;
    }

    /**
     * Date 转 23:59:59
     * @param date
     * @return
     */
    public static Date getEndOfDay(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 设置时间为当天的23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date endOfDay = calendar.getTime();
        return endOfDay;
    }

    /**
     * 当前时间 add hours
     */
    public static Date addHours(Date date, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        return calendar.getTime();
    }
}
