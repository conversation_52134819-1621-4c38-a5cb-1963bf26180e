<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%--
  Created by IntelliJ IDEA.
  User: vedeng
  Date: 2022/3/10
  Time: 17:23
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%--<%@ include file="../common/common.jsp"%>--%>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">

<script type="text/javascript" src='<%=basePath%>static/js/jquery.min.js'></script>
<script type="text/javascript" src="<%=basePath%>static/js/jquery/validation/jquery-form.js"></script>
<script type="text/javascript" src='<%=basePath%>static/libs/jquery/plugins/layer/layer.js'></script>
<script type="text/javascript" src="<%=basePath%>static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<script type="text/javascript" charset="UTF-8" src='<%=basePath%>static/js/form.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" charset="UTF-8" src='<%=basePath%>static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
<html>
<head>
    <title>修改标签</title>
</head>
<body>
<div style="text-align: center">
    <form class="layui-form layui-inline" >
        <input type="hidden" name="traderCustomerTuokeLabelId" value="${traderCustomerTuokeLabelVo.traderCustomerTuokeLabelId}">
        <input type="hidden" name="traderId" value="${traderCustomerTuokeLabelVo.traderId}">
        <table lay-even  class="layui-table" style="text-align: center">
            <tr>
                <td>标签类型</td>
                <td>标签</td>
                <td>标签值</td>
                <td>标签</td>
                <td>标签值</td>
            </tr>
            <tr>
                <td>客群标签</td>
                <td>经营对象</td>
                <td style="text-align: justify">
                    <input type="checkbox" name="businessTarget" lay-skin="primary" title="公立" value="1" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.businessTargets,1+'')}">checked</c:if>/><br>
                    <input type="checkbox" name="businessTarget" lay-skin="primary" title="民营" value="2" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.businessTargets,2+'')}">checked</c:if>>
                </td>
                <td>经营产品</td>
                <td style="text-align: justify">
                    <input type="checkbox" name="hospitalLevel" lay-skin="primary" title="市级以上" value="1" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.hospitalLevels,1+'')}">checked</c:if>/>
                    <input type="checkbox" name="hospitalLevel" lay-skin="primary" title="区县级医疗机构" value="2" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.hospitalLevels,2+'')}">checked</c:if>/><br>
                    <input type="checkbox" name="hospitalLevel" lay-skin="primary" title="基层医疗" value="3" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.hospitalLevels,3+'')}">checked</c:if>/>
                    <input type="checkbox" name="hospitalLevel" lay-skin="primary" title="公共卫生" value="4" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.hospitalLevels,4+'')}">checked</c:if>/>
                </td>
            </tr>
            <tr>
                <td>产品标签</td>
                <td>经营科室</td>
                <td width="460px" style="text-align: justify">
                    <c:forEach items="${traderCustomerTuokeLabelVo.allLabel}" var="label" varStatus="no">
                        <div class="layui-input-inline" style="width: 150px">
                            <input type="checkbox" name="businessDepartment" lay-skin="primary" title="${label.departmentName}" value="${label.departmentId}" <c:if test="${label.checked}">checked</c:if>/>
                        </div>
                    </c:forEach>
                </td>
                <td>经营产品</td>
                <td width="460px" style="text-align: justify">
                    <c:forEach items="${traderCustomerTuokeLabelVo.levelCategory}" var="category" varStatus="no">
                        <div class="layui-input-inline" style="width: 150px">
                            <input type="checkbox" name="businessGoods" lay-skin="primary" title="${category.baseCategoryName}" value="${category.baseCategoryId}"  <c:if test="${category.checked}">checked</c:if>/>
                        </div>
                    </c:forEach>
                </td>
            </tr>
            <tr>
                <td>销售标签</td>
                <td>业务模式</td>
                <td style="text-align: justify">
                    <input type="checkbox" name="businessModel" lay-skin="primary" title="直销" value="1" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.businessModels,1+'')}">checked</c:if>><br>
                    <input type="checkbox" name="businessModel" lay-skin="primary" title="分销" value="2" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.businessModels,2+'')}">checked</c:if>>
                </td>
                <td>销售模式</td>
                <td style="text-align: justify">
                    <input type="checkbox" name="salesModel" lay-skin="primary" title="代理权" value="1" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.salesModels,1+'')}">checked</c:if>><br>
                    <input type="checkbox" name="salesModel" lay-skin="primary" title="关系" value="2" <c:if test="${fn:contains(traderCustomerTuokeLabelVo.salesModels,2+'')}">checked</c:if>>
                </td>
            </tr>
        </table>
        <div class="layui-block" align="center" >
            <button type="button" 	class="layui-btn layui-btn-xs layui-btn-normal" id="btn">确定</button>
            <button type="button" 	class="layui-btn layui-btn-xs layui-btn-warm" id="cancel">取消</button>
        </div>

    </form>
</div>

<script type="text/javascript" src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/tuoke/edit_trader_customer_label.js?rnd=${resourceVersionKey}"></script>
<script>
    layui.use('form', function(){
        var form = layui.form;

        //各种基于事件的操作，下面会有进一步介绍
    });
</script>
</body>
</html>
