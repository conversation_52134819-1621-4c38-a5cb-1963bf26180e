package com.vedeng.infrastructure.taxes.domain;


import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import lombok.Data;

import java.util.List;

/**
 * 公共接口返回
 */
@Data
public class TaxesCommonApiResult implements ITaxesResult {

    private static final long serialVersionUID = -40281933103594796L;

    /**
     * 接口返回：200成功
     */
    private Integer code;
    /**
     * 接口返回消息
     */
    private String message;

    private Boolean isSuccess;

    private ResultData data;

    /**
     * 返回体Data
     */
    @Data
    public class ResultData{
        /**
         * 请求接口：如getInvoice
         */
        private String requestcode;

        /**
         * 返回码
         */
        private String returncode;

        /**
         * 返回消息
         */
        private String returnmessage;

        /**
         * 返回消息体
         */
        private List<Object> data;
    }

    /**
     * 构造方法
     * @param se
     */
    public TaxesCommonApiResult(ServiceException se){
        this.code = se.getCode();
        this.message = se.getMessage();
        this.isSuccess = Boolean.FALSE;
    }
}
