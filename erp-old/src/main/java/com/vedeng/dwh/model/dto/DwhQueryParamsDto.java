package com.vedeng.dwh.model.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * dwh通用查询对象.
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/2 10:52 上午.
 * @author: Tomcat.Hui.
 */
@Data
public class DwhQueryParamsDto {

    private Integer userId;

    private Integer orgId;

    private Integer userType;

    private Integer overviewType;

    private List<Integer> status;

    private List<Integer> orgIds;

    private Timestamp startTime;

    private Timestamp endTime;

    private Long startTimeMillions;

    private Long endTimeMillions;

}
