package com.vedeng.crm.visitrecord.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.visitrecord.domain.dto.VisitRecordInputDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.service.CrmVisitMessageService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/10
 */
@Service("CrmVisitModifyMessageServiceImpl")
public class CrmVisitModifyMessageServiceImpl extends BaseMessageSendServiceImpl implements CrmVisitMessageService {


    @Autowired
    private UserApiService userApiService;

    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;


    @Override
    public void sendMessage(VisitRecordInputDto visitRecordInputDto, Integer recordId, List<Integer>  tongxingIdsList, CurrentUser currentUser) {
        String tongxingNamesStr = "";
        if (CollectionUtils.isNotEmpty(visitRecordInputDto.getTongxingIds())) { //编辑的场景，取入参数里的同行人，tongxingIdsList增加了被删除掉的同行人，以便于消息发送
            List<UserDto> userDtoSendList = userApiService.getUserInfoByUserIds(visitRecordInputDto.getTongxingIds());
            //获取这些人的姓名
            List<String> tongXingUserNames = CollectionUtil.isEmpty(userDtoSendList) ? new ArrayList<>()
                    : (userDtoSendList.stream().map(UserDto::getUsername).collect(Collectors.toList()));
            tongxingNamesStr = StringUtils.join(tongXingUserNames,"，");
        }

        //创建拼接消息，消息模板如下（没有同行人时，消息通知内无同行人字段）：
        //        拜访编号：
        //        拜访客户：
        //        拜访目标：
        //        计划拜访时间：
        //        拜访人：
        //        同行人：
        //        创建人：
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append("拜访编号：").append(visitRecordInputDto.getVisitRecordNo()).append("\n");
        messageBuilder.append("拜访客户：").append(visitRecordInputDto.getCustomerName()).append("\n");
        messageBuilder.append("拜访目标：").append(visitRecordInputDto.getVisitTargetStr()).append("\n");
        messageBuilder.append("计划拜访时间：").append(DateUtil.format(visitRecordInputDto.getPlanVisitDate(),"yyyy-MM-dd") ).append("\n");
        messageBuilder.append("拜访人：").append(visitRecordInputDto.getVisitorName()).append("\n");

        // 如果有同行人则添加同行人信息
        if (StringUtils.isNotEmpty(tongxingNamesStr)) {
            messageBuilder.append("同行人：").append(tongxingNamesStr).append("\n");
        }
        messageBuilder.append("操作人：").append(currentUser.getUsername());
        String message = messageBuilder.toString();

        List<Integer> userIds = new ArrayList<>();
        userIds.addAll(tongxingIdsList);// 上次的创建人+修改前的拜访人+同行人+当前拜访人+修改前的同行人
        if(visitRecordInputDto.getTraderId() != null ){
            //查询归属销售，如果归属销售不在userIds中，则添加到userIds
            TraderUserDto traderUserDto =  traderCustomerBaseService.getTraderUserByTraderId(visitRecordInputDto.getTraderId());
            Integer belongerId = traderUserDto==null?null:traderUserDto.getUserId();
            if(belongerId !=null ){
                userIds.add(belongerId);
            }
        }
        //userIds.remove(currentUser.getId());//消息的发送人，去掉当前的操作人，即创建人
        userIds.removeAll(Collections.singleton(currentUser.getId()));//消息的发送人，去掉当前的操作人，即创建人

        if(CollectionUtil.isNotEmpty(userIds)){
            List<UserDto> userDtoSendList = userApiService.getUserInfoByUserIds(userIds);
            //获取这些人的工号
            List<String> visitJobNumList = CollectionUtil.isEmpty(userDtoSendList)?new ArrayList<>()
                    :(userDtoSendList.stream().map(UserDto::getNumber).collect(Collectors.toList())) ;
            for(String jobNumber:visitJobNumList){
                SendMessageDto sendMessageDto = new SendMessageDto();
                sendMessageDto.setUrl(jumpService.getMjumpUrl(crmApplicationMessageJumpUrl + "/crm/visitRecord/m/detail?id=" + recordId,
                         lxcrmUrl + "/crm/visitRecord/profile/detail?id=" + recordId,
                        JumpErpTitleEnum.VISIT_RECORD_DETAIL));
                sendMessageDto.setUserNumber(jobNumber);
                sendMessageDto.setFormat(message);
                sendCardMsg(sendMessageDto, "拜访计划被修改");
            }
        }
    }
}
