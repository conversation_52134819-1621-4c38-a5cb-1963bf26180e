package com.vedeng.goodsSearch.constant;

/**
 * 商品搜索选项枚举
 *
 * <AUTHOR>
 */
public enum GoodsOptionEnum {

    /**
     * 默认枚举
     */
    DEFAULT_ENUM(0, ""),

    /**
     * 价位段
     */
    PRICE(1, "price"),

    /**
     * 分类筛选
     */
    ERP_CATEGORY(2, "erpCatId3"),

    /**
     * 属性筛选
     */
    SKU_ATTR(3, "skuAttrValueIds"),

    /**
     * 机构筛选
     */
    MECHANISM(4, "mechanismIds"),

    /**
     * 品牌筛选
     */
    BRAND(5, "brandId"),

    /**
     * 类型筛选
     */
    SPU_TYPE(6, "spuType"),

    /**
     * 科室筛选
     */
    DEPARTMENT(7, "departmentIds"),

    /**
     * 进口/国产
     */
    SOURCE(8, "source"),

    /**
     * 等级筛选
     */
    RULE_LEVEL(9, "ruleLevel"),

    /**
     * 挡位筛选
     */
    RULE_GEAR(10, "ruleGear");

    /**
     * 代码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    public static GoodsOptionEnum getInstance(String desc) {
        for (GoodsOptionEnum v : values()) {
            if (v.desc.equalsIgnoreCase(desc)) {
                return v;
            }
        }
        return DEFAULT_ENUM;
    }

    GoodsOptionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
