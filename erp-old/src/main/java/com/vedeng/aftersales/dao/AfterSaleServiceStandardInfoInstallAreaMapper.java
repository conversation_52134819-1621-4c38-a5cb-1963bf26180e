package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSaleServiceStandardInfoInstallArea;

import java.util.List;

public interface AfterSaleServiceStandardInfoInstallAreaMapper {
    int deleteByPrimaryKey(Long installAreaId);

    int insertSelective(AfterSaleServiceStandardInfoInstallArea record);

    AfterSaleServiceStandardInfoInstallArea selectByPrimaryKey(Long installAreaId);

    AfterSaleServiceStandardInfoInstallArea selectByServiceStandardInfoId(Long serviceStandardInfoId);

    int updateByPrimaryKeySelective(AfterSaleServiceStandardInfoInstallArea record);

    void deleteByServiceStandardInfoId(Long serviceStandardApplyId);

    void batchInsertInstallArea(List<AfterSaleServiceStandardInfoInstallArea> list);
}