package com.vedeng.trader.dao;

//import com.smallhospital.dto.ElTraderInfo;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.TraderContactGenerateExample;
import java.util.List;

import com.vedeng.trader.model.vo.TraderContactVo;
import org.apache.ibatis.annotations.Param;

public interface TraderContactGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    long countByExample(TraderContactGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    int deleteByExample(TraderContactGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    int deleteByPrimaryKey(Integer traderContactId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    int insert(TraderContactGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    int insertSelective(TraderContactGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    List<TraderContactGenerate> selectByExample(TraderContactGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    TraderContactGenerate selectByPrimaryKey(Integer traderContactId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    int updateByExampleSelective(@Param("record") TraderContactGenerate record, @Param("example") TraderContactGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    int updateByExample(@Param("record") TraderContactGenerate record, @Param("example") TraderContactGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    int updateByPrimaryKeySelective(TraderContactGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_CONTACT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    int updateByPrimaryKey(TraderContactGenerate record);

    /**
     * 功能描述: 根据条件查询客户联系人是否存在
     * @param: [traderId, traderType1, name, telephone, mobile]
     * @return: com.vedeng.trader.model.TraderContactGenerate
     * @auther: duke.li
     * @date: 2019/8/13 17:31
     */
    TraderContactGenerate getContactInfo(@Param("traderId")Integer traderId,@Param("traderType") Integer traderType,@Param("name") String name,@Param("telephone") String telephone,@Param("mobile") String mobile);

    TraderContactGenerate getTraderDefaultContact(@Param("traderId") Integer traderId);

    /**
     * 通过客户编号和手机号码获取联系人信息
     *
     * @param traderId
     * @param mobileNo
     * @param enable
     * @return
     */
    List<TraderContactGenerate> getByTraderIdAndMobileNo(@Param("traderId") Integer traderId, @Param("mobileNo") String mobileNo, @Param("enable") Integer enable);

  //  List<ElTraderInfo> getTraderContact(@Param("traderId") Integer traderId);

    /**
     * 通过电话号码获取客户ID信息
     *
     * @param mobile
     * @return
     */
    List<Integer> getTraderIdsByPhone(String mobile);

    /**
     * 获取联系人
     * @param traderId
     * @return
     */
    List<TraderContactVo> getTraderContactList(@Param("traderId") Integer traderId);

    /**
     * 更新联系人信息
     * @param traderContact
     * @return
     */
    Integer updateContactStick(TraderContact traderContact);

    /**
     * 根据客户id查询客户联系人信息（联系人手机号在注册用户表中）
     * @param traderId 客户id
     * @return 联系人信息
     */
    List<TraderContactGenerate> getTraderContactListInWebAccountByTraderId(@Param("traderId") Integer traderId);

    /**
     * 号码获取注册用户会员信息
     * @param mobile
     * @return
     */
    Integer getIsVedengMemberByTraderMobile(String mobile);
}