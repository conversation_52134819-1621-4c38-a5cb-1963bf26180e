package com.vedeng.finance.service;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.finance.model.PayOrder;

/**
 * <AUTHOR>
 * @ClassName PayService.java
 * @Description TODO
 * @createTime 2020年09月24日 17:51:00
 */
public interface PayService {
    /**
     * @description: 耗材订单支付同步
     * @return: ResultInfo
     * @author: Strange
     * @date: 2020/9/24
     **/
    ResultInfo orderPay(PayOrder payOrder) throws Exception;
    /**
     * @description: 耗材订单退款同步
     * @return: ResultInfo
     * @author: Strange
     * @date: 2020/9/24
     **/
    ResultInfo orderRefund(PayOrder payOrder) throws Exception;
}
