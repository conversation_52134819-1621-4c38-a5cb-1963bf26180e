package com.vedeng.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class NumberUtil {

    public static boolean isPositive(int number) {
        return number > 0;
    }

    /**
     * Check if the number is positive.
     *
     * @param numberWrapper
     * @return
     */
    public static boolean isPositive(Integer numberWrapper) {
        return numberWrapper != null && isPositive(numberWrapper.intValue());
    }


    public static boolean greaterAndEqualZero(Number numberWrapper) {
        if (numberWrapper == null) {
            return false;
        }

        boolean valid = false;
        if (numberWrapper instanceof Integer) {
            valid = ((Integer) numberWrapper).compareTo(0) >= 0;
        } if (numberWrapper instanceof Long) {
            valid = ((Long) numberWrapper).compareTo(0L) >= 0;
        }else if (numberWrapper instanceof Float) {
            valid = ((Float) numberWrapper).compareTo(0f) >= 0;
        } else if (numberWrapper instanceof Double) {
            valid = ((Double) numberWrapper).compareTo(0D) >= 0;
        } else if (numberWrapper instanceof BigDecimal) {
            valid = ((BigDecimal) numberWrapper).compareTo(BigDecimal.ZERO) >= 0;
        }
        return valid;
    }

    public static boolean isZeroOrNull(Number number) {
        return number == null || number.intValue() == 0;
    }

}
