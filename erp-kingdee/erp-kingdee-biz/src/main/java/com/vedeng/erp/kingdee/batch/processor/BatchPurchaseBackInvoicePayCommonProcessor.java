package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseBackQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseBackConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseBackMapper;
import com.vedeng.erp.kingdee.service.KingDeePayCommonService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseBackApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购退货 的应付单 以及 票的数据推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseBackInvoicePayCommonProcessor implements ItemProcessor<BatchInvoiceDto, KingDeePayCommonDto> {

    @Autowired
    private KingDeePurchaseBackMapper kingDeePurchaseBackMapper;

    @Autowired
    private KingDeePurchaseBackConvertor kingDeePurchaseBackConvertor;

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    protected KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePayCommonService kingDeePayCommonApiService;

    @Autowired
    private KingDeePurchaseBackApiService kingDeePurchaseBackApiService;


    @Autowired
    private BatchRVirtualInvoiceJWarehouseDtoMapper batchRVirtualInvoiceJWarehouseDtoMapper;



    @Override
    public KingDeePayCommonDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("BatchPurchaseBackInvoiceProcessorService 处理采购单红字有效票,{}" , JSON.toJSONString(batchInvoiceDto));


        List<BatchRInvoiceDetailJOperateLogDto> jOperateLogDtos = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        batchInvoiceDto.setBatchRInvoiceDetailJOperateLogDtos(jOperateLogDtos);
        // 根据 商品维度聚合分组
        if (CollUtil.isEmpty(jOperateLogDtos)) {
            log.error("当前采购单红字有效票id:{},未关联到出库单信息",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单红字有效票id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未关联到出库单信息");
        }

        List<Integer> collect = jOperateLogDtos.stream().map(BatchRInvoiceDetailJOperateLogDto::getOperateLogId).collect(Collectors.toList());
        List<BatchWarehouseGoodsOutInDto> batchWarehouseGoodsOutInDtos = batchWarehouseGoodsOutInDtoMapper.selectWarehouseOutInOrder(collect);
        Optional<Date> max = batchWarehouseGoodsOutInDtos.stream().max(Comparator.comparing(BatchWarehouseGoodsOutInDto::getOutInTime)).map(BatchWarehouseGoodsOutInDto::getOutInTime);
        if (max.isPresent() && max.get().getTime() > batchInvoiceDto.getValidTime()) {
            batchInvoiceDto.setValidTime(max.get().getTime());
        }

        processNum(jOperateLogDtos);

        // 票明细id 去聚合
        Map<Integer, List<BatchRInvoiceDetailJOperateLogDto>> groupByInvoiceDetailId = jOperateLogDtos
                .stream().collect(Collectors.groupingBy(BatchRInvoiceDetailJOperateLogDto::getInvoiceDetailId));


        List<String> outInNos = jOperateLogDtos.stream().map(BatchRInvoiceDetailJOperateLogDto::getOutInNo).distinct().collect(Collectors.toList());

        List<KingDeePurchaseBackDto> all2Dto = new ArrayList<>();
        for (String no : outInNos) {
            processOutInNo(all2Dto, no);
        }
        if (CollUtil.isEmpty(all2Dto)) {
            log.error("当前采购单红字有效票:{},有效票未关联到推送金蝶出库单信息",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单红字有效票:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "有效票未关联到推送金蝶出库单信息");
        }

        Map<String, KingDeePurchaseBackDto> kingDeeData = all2Dto
                .stream().collect(Collectors.toMap(KingDeePurchaseBackDto::getFBillNo,c->c,(k1,k2)->k1));
        // 发票类型 1电票 2 纸票
        String QZOK_FPLX = Objects.isNull(batchInvoiceDto.getInvoiceProperty()) ? "2" : batchInvoiceDto.getInvoiceProperty().equals(1) ? "2" : "1";
        KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto = new KingDeePayCommonAndInvoiceDto();
        if (StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName())&&batchInvoiceDto.getInvoiceTypeName().contains("专用发票")) {
            kingDeePayCommonAndInvoiceDto.setSpecial(true);
        } else {
            kingDeePayCommonAndInvoiceDto.setSpecial(false);
        }

        List<Integer> invoiceDetailIds = jOperateLogDtos.stream().map(BatchRInvoiceDetailJOperateLogDto::getInvoiceDetailId).collect(Collectors.toList());
        List<BatchInvoiceDetailDto> batchInvoiceDetailDtos = batchInvoiceDetailDtoMapper.selectByDetailIds(invoiceDetailIds);
        Map<Integer, BatchInvoiceDetailDto> invoiceDetail2Map = batchInvoiceDetailDtos.stream().collect(Collectors.toMap(BatchInvoiceDetailDto::getInvoiceDetailId, c -> c, (k1, k2) -> k1));

        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : kingDeePayCommonAndInvoiceDto.getSpecial() ? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)) : "0.00";
        BigDecimal taxRateNum = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO :kingDeePayCommonAndInvoiceDto.getSpecial()? batchInvoiceDto.getRatio():BigDecimal.ZERO;
        //一级dto
        KingDeePayCommonDto kingDeePayCommonDto = new KingDeePayCommonDto();
        kingDeePayCommonDto.setFQzokBddjtId(batchInvoiceDto.getInvoiceId().toString());
        Boolean exist = kingDeePayCommonApiService.kingDeeIsExist(kingDeePayCommonDto);
        if (exist) {
            return null;
        }
        kingDeePayCommonDto.setFDate(DateUtil.formatDate(new Date(batchInvoiceDto.getValidTime())));
        kingDeePayCommonDto.setFSupplierId(batchInvoiceDto.getTraderSupplierId().toString());

        // 构建应付单

        //二级dto
        List<KingDeePayCommonDetailDto> kingDeePayCommonDetailDtoList = new ArrayList<>();
        kingDeePayCommonDto.setFEntityDetail(kingDeePayCommonDetailDtoList);
        groupByInvoiceDetailId.forEach((k, v) -> {

            // 匹配总数
            BigDecimal totalNum = v.stream().map(BatchRInvoiceDetailJOperateLogDto::getNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (totalNum.compareTo(BigDecimal.ZERO) == 0) {
                log.error("匹配数量为0：{}",JSON.toJSONString(v));
                return;
            }

            List<KingDeePayCommonDetailDto> calcNum = new ArrayList<>();

            // 票明细总额
            BatchInvoiceDetailDto data = invoiceDetail2Map.get(k);
            BigDecimal totalAmount = Objects.isNull(data.getTotalAmount()) ? BigDecimal.ZERO : data.getTotalAmount();
            // 平均价格
            BigDecimal priceAverage = totalAmount.divide(totalNum,6, RoundingMode.HALF_UP);


            for (int i = 0; i < v.size(); i++) {

                BatchRInvoiceDetailJOperateLogDto batchRInvoiceDetailJOperateLogDto = v.get(i);
                BigDecimal thisAllNum = batchRInvoiceDetailJOperateLogDto.getNum();
                BigDecimal thisTotalAmount = thisAllNum.multiply(priceAverage).setScale(2, RoundingMode.HALF_UP);

                KingDeePayCommonDetailDto kingDeePayCommonDetailDto = new KingDeePayCommonDetailDto();
                kingDeePayCommonDetailDtoList.add(kingDeePayCommonDetailDto);
                calcNum.add(kingDeePayCommonDetailDto);
                kingDeePayCommonDetailDto.setFMaterialId(batchRInvoiceDetailJOperateLogDto.getSku());
                // 商品在当前发票行的对应开票总数
                kingDeePayCommonDetailDto.setFPriceQty(thisAllNum.abs().negate().toString());
                // 数组中商品的录票单价平均值
                kingDeePayCommonDetailDto.setFTaxPrice(priceAverage.abs().toString());
                kingDeePayCommonDetailDto.setFEntryTaxRate(taxRate);
                // erp 票的 明细
                KingDeePurchaseBackDto kingDeeInData = kingDeeData.get(batchRInvoiceDetailJOperateLogDto.getOutInNo());
                if (Objects.isNull(kingDeeInData)) {
                    log.error("当前采购单红字有效票：{}未查到出库单信息{}",batchInvoiceDto.getInvoiceId(), k);
                    throw new KingDeeException("当前采购单红字有效票:"+batchInvoiceDto.getInvoiceId()+"未查到出库单信息");
                }
                kingDeePayCommonDetailDto.setF_QZOK_BDDJHID(k.toString()+"-"+batchRInvoiceDetailJOperateLogDto.getOperateLogId());
                kingDeePayCommonDetailDto.setFSourceType(KingDeeFormConstant.PURCHASE_BACK);
                // 价税合计 发票sku商品录入的总额
                kingDeePayCommonDetailDto.setFAllAmountFor_D(thisTotalAmount.toString());
                // 税额  [价税合计/(1+ 税率)]*税率
                BigDecimal taxAmouontFor = thisTotalAmount.multiply(taxRateNum).divide(BigDecimal.ONE.add(taxRateNum),2,RoundingMode.HALF_UP);
                kingDeePayCommonDetailDto.setFTaxAmountFor_D(taxAmouontFor.toString());
                // 发票不含税的金额 价税合计-税额
                kingDeePayCommonDetailDto.setFNoTaxAmountFor_D(thisTotalAmount.subtract(taxAmouontFor).toString());

                //三级dto
                List<KingDeePayCommonDetailLinkDto> kingDeePayCommonDetailLinkDtoList = new ArrayList<>();
                kingDeePayCommonDetailDto.setFEntityDetail_Link(kingDeePayCommonDetailLinkDtoList);

                BatchRInvoiceDetailJOperateLogDto item = batchRInvoiceDetailJOperateLogDto;

                // 入库单
                KingDeePurchaseBackDto kingDeePurchaseBackDto = kingDeeData.get(item.getOutInNo());
                List<KingDeePurchaseBackDetailDto> fInStockEntry = kingDeePurchaseBackDto.getFpurmrbentry();
                // 入库单明细
                Map<String, KingDeePurchaseBackDetailDto> fInStockEntry2Map = fInStockEntry.stream().collect(Collectors.toMap(KingDeePurchaseBackDetailDto::getF_qzok_bddjhid, a -> a, (k1, k2) -> k1));


                // 此条入库单明细
                KingDeePurchaseBackDetailDto kingDeePurchaseBackDetail = fInStockEntry2Map.get(item.getOperateLogId().toString());
                if (Objects.isNull(kingDeePurchaseBackDetail)) {
                    log.error("invoiceId:{}未能查询到此出库单明细对应的金蝶出库单明细{}", batchInvoiceDto.getInvoiceId(),JSON.toJSONString(item));
                    throw new KingDeeException("invoiceId:"+batchInvoiceDto.getInvoiceId()+"未能查询到此出库单明细对应的金蝶出库单明细");
                }
                BatchInvoiceDetailDto batchInvoiceDetailDto = invoiceDetail2Map.get(item.getInvoiceDetailId());
                if (Objects.isNull(batchInvoiceDetailDto)) {
                    log.error("未能查到票的明细信息{}", JSON.toJSONString(item));
                    throw new KingDeeException("未能查到票的明细信息");
                }


                KingDeePayCommonDetailLinkDto kingDeePayCommonDetailLinkDto = new KingDeePayCommonDetailLinkDto();
                kingDeePayCommonDetailLinkDto.setFLinkId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FRuleId("AP_MRBToPayableMap");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FFlowLineId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableName("T_PUR_MRBENTRY");
                // 金蝶出库单id
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSBillId(kingDeePurchaseBackDto.getFId());
                // 金蝶出库行id
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSId(kingDeePurchaseBackDetail.getFEntryId());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FBASICUNITQTYOld(item.getNum().abs().negate().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FBASICUNITQTY(item.getNum().abs().negate().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FStockBaseQty(item.getNum().abs().negate().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FStockBaseQtyOld(item.getNum().abs().negate().toString());
                if (i == v.size() - 1) {

                    // 减去最后一行 获取前面的已录入
                    BigDecimal previousAmount = priceAverage.multiply(totalNum.subtract(item.getNum())).setScale(2,RoundingMode.HALF_UP);
                    // 避免尾差
                    BigDecimal thisLine = totalAmount.subtract(previousAmount);
                    log.info("采购红票invoiceId:{},sku:{},已录入:{},最后一行:{}",batchInvoiceDto.getInvoiceId(),kingDeePayCommonDetailDto.getFMaterialId(),previousAmount,thisLine);
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_DOld(thisLine.toString());
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_D(thisLine.toString());
                } else {
                    // 均价*录入关系的数量
                    BigDecimal thisLine = priceAverage.multiply(item.getNum()).setScale(2,RoundingMode.HALF_UP);
                    log.info("采购红票invoiceId:{},sku:{},前几行金额:{}",batchInvoiceDto.getInvoiceId(),kingDeePayCommonDetailDto.getFMaterialId(),thisLine);
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_DOld(thisLine.toString());
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_D(thisLine.toString());
                }

                kingDeePayCommonDetailLinkDtoList.add(kingDeePayCommonDetailLinkDto);

            }

            if (CollUtil.isEmpty(calcNum)) {
                return;
            }

            processTailDifferent(taxRateNum, calcNum, totalAmount);


        });

        return kingDeePayCommonDto;
    }

    private void processOutInNo(List<KingDeePurchaseBackDto> all2Dto, String no) {
        List<KingDeePurchaseBackEntity> all = kingDeePurchaseBackMapper.findByAll(KingDeePurchaseBackQueryDto.builder().fBillNo(no).build());

        if (CollUtil.isNotEmpty(all)) {
            List<KingDeePurchaseBackDto> data = kingDeePurchaseBackConvertor.toDto(all);
            all2Dto.addAll(data);
        } else {

            // 先查金蝶 查不到查期金蝶
            List<KingDeePurchaseBackQueryResultDto> data = kingDeePurchaseBackApiService.queryByOutInNo(no);

            if (CollUtil.isNotEmpty(data)) {
                KingDeePurchaseBackDto kingDeePurchasePurchaseBackDto = new KingDeePurchaseBackDto();
                kingDeePurchasePurchaseBackDto.setFId(data.get(0).getFID());
                kingDeePurchasePurchaseBackDto.setFId(data.get(0).getFID());
                kingDeePurchasePurchaseBackDto.setFBillNo(data.get(0).getFBillNo());
                List<KingDeePurchaseBackDetailDto> entities = data.stream().map(x -> {
                    KingDeePurchaseBackDetailDto result = new KingDeePurchaseBackDetailDto();
                    result.setFEntryId(x.getFPURMRBENTRY_FEntryId());
                    result.setF_qzok_bddjhid(x.getF_QZOK_BDDJHID());
                    return result;
                }).collect(Collectors.toList());
                kingDeePurchasePurchaseBackDto.setFpurmrbentry(entities);
                all2Dto.add(kingDeePurchasePurchaseBackDto);
            }
        }
    }

    private void processTailDifferent(BigDecimal taxRateNum, List<KingDeePayCommonDetailDto> calcNum, BigDecimal totalAmount) {
        BigDecimal logTotalAmount = calcNum.stream().map(x -> new BigDecimal(x.getFAllAmountFor_D())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 满足查额在0.01以内的
        if (totalAmount.subtract(logTotalAmount.abs()).abs().compareTo(new BigDecimal("0.01")) <= 0) {
            KingDeePayCommonDetailDto kingDeePayCommonDetailDto = calcNum.get(calcNum.size() - 1);

            BigDecimal subtract = totalAmount.subtract(logTotalAmount);
            BigDecimal bigDecimal = new BigDecimal(kingDeePayCommonDetailDto.getFAllAmountFor_D());
            BigDecimal total = bigDecimal.add(subtract);
            kingDeePayCommonDetailDto.setFAllAmountFor_D(total.toString());
            // 税额  [价税合计/(1+ 税率)]*税率
            BigDecimal taxAmouontFor = total.multiply(taxRateNum).divide(BigDecimal.ONE.add(taxRateNum),2,RoundingMode.HALF_UP);
            kingDeePayCommonDetailDto.setFTaxAmountFor_D(taxAmouontFor.toString());
            // 发票不含税的金额 价税合计-税额
            kingDeePayCommonDetailDto.setFNoTaxAmountFor_D(total.subtract(taxAmouontFor).toString());
        }
    }

    private void processNum(List<BatchRInvoiceDetailJOperateLogDto> jOperateLogDtos) {
        // 处理num
        for (BatchRInvoiceDetailJOperateLogDto dto : jOperateLogDtos) {
            // 统一保留2位小数
            // 判断是否为入库单明细的最后一个 sql 倒排
            List<BatchRInvoiceDetailJOperateLogDto> byOperateLogId = batchRInvoiceDetailJOperateLogDtoMapper.findByOperateLogId(dto.getOperateLogId());
            List<BatchRVirtualInvoiceJWarehouseDto> byWarehouseGoodsOutInItemId = batchRVirtualInvoiceJWarehouseDtoMapper.findByWarehouseGoodsOutInItemId(dto.getOperateLogId());
            // 是否为最后一张票
            boolean isLastInvoice = false;
            if (CollUtil.isNotEmpty(byWarehouseGoodsOutInItemId)) {
                BatchRVirtualInvoiceJWarehouseDto batchRVirtualInvoiceJWarehouseDto = byWarehouseGoodsOutInItemId.get(0);
                if (dto.getInvoiceDetailId().equals(byOperateLogId.get(0).getInvoiceDetailId()) ) {
                    if (batchRVirtualInvoiceJWarehouseDto.getAddTime().getTime() <= byOperateLogId.get(0).getAddTime().getTime()) {
                        isLastInvoice = true;
                    } else {
                        isLastInvoice = false;
                    }

                }
            } else {
                isLastInvoice=  dto.getInvoiceDetailId().equals(byOperateLogId.get(0).getInvoiceDetailId());
            }
            // 入库单明细数量是否已经用尽
            BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsOutInItemDto = batchWarehouseGoodsOutInItemDtoMapper.selectByPrimaryKey(Long.valueOf(dto.getOperateLogId()));
            BigDecimal originalNum = batchWarehouseGoodsOutInItemDto.getNum().abs();

            BigDecimal allUseNum = byOperateLogId.stream().map(x -> x.getNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal used = byWarehouseGoodsOutInItemId.stream().filter(Objects::nonNull).map(x -> x.getNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            allUseNum = allUseNum.add(used);
            // 用完 且当前明细为最后一个明细
            if (isLastInvoice && originalNum.compareTo(allUseNum) <= 0) {
                // 前面的四舍五入保留2位
                BigDecimal beforeThis = byOperateLogId.stream()
                        .filter(x -> !x.getRInvoiceDetailJOperateLogId().equals(dto.getRInvoiceDetailJOperateLogId()))
                        .map(x -> {
                            BigDecimal abs = x.getNum().abs();
                            if (abs.compareTo(new BigDecimal("0.01")) <= 0) {
                                return new BigDecimal("0.01");
                            } else {
                                return x.getNum().abs().setScale(2, RoundingMode.DOWN);
                            }
                        })
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal before = byWarehouseGoodsOutInItemId.stream().filter(Objects::nonNull).map(x -> {
                    BigDecimal abs = x.getNum().abs();
                    if (abs.compareTo(new BigDecimal("0.01")) <= 0) {
                        return new BigDecimal("0.01");
                    } else {
                        return x.getNum().abs().setScale(2, RoundingMode.DOWN);
                    }
                }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                beforeThis = beforeThis.add(before);
                dto.setNum(originalNum.subtract(beforeThis));
            } else {

                if (dto.getNum().abs().compareTo(new BigDecimal("0.01")) <= 0) {
                    dto.setNum(new BigDecimal("0.01"));
                } else {
                    dto.setNum(dto.getNum().abs().setScale(2, RoundingMode.DOWN));
                }

            }


        }
    }

}
