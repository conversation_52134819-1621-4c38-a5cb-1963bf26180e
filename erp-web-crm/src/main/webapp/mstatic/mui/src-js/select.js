Vue.component('ui-select', {
    template: `<div class="vd-ui-select">
        <div class="vd-ui-select-trigger" :style="'--line:' + line" v-if="!multi" @click="showOptionDialog">
            <div class="vd-ui-select-trigger-txt" :class="{'placeholder': !selectedItem.label}">{{ selectedItem.label || placeholder }}</div>
            <i class="vd-ui_icon icon-app-right"></i>
        </div>
        <template v-else> 
            <div class="vd-ui-select-trigger multi-select" :style="'--line:' + line" v-if="selectedItemList.length">
                <div class="vd-ui-select-multi-tags">
                    <div class="vd-ui-select-multi-tags-inner">
                        <template v-for="(item, index) in selectedItemList">
                            <div class="vd-ui-select-tag" :class="{'tag-avatar': avatar}" v-if="!showLength || index < showLength" ref="tagItem" :key="index">
                                <template>
                                    <div class="vd-ui-select-tag-avatar" v-if="avatar">
                                        <img :src="item.avatar || '/mstatic/mui/image/crm-user-avatar.svg'" />
                                    </div>
                                    <div class="vd-ui-select-tag-txt">{{ item.label }}</div>
                                    <i class="vd-ui_icon icon-delete" @click="deleteMultiItem(item, index)"></i>
                                </template>
                            </div>
                        </template>
                        <div class="vd-ui-select-tag-more" v-if="showLength">+{{selectedItemList.length - showLength}}</div>
                    </div>
                </div>
                <div class="vd-ui-icon-trigger">
                    <i class="vd-ui_icon icon-app-right" @click="showOptionDialog"></i>
                </div>
            </div>
            <div class="vd-ui-select-trigger" :style="'--line:' + line" v-else @click="showOptionDialog">
                <div class="vd-ui-select-trigger-txt placeholder">{{ placeholder }}</div>
                <i class="vd-ui_icon icon-app-right"></i>
            </div>
        </template>

        <crm-slide-dialog ref="selectOptions" :title="title" :topOptionTxt="multi ? '清空' : ''" :maskHide="false" @topOption="clear">
            <div class="vd-ui-select-options">
                <div class="vd-ui-select-options-search" v-if="filter">
                    <ui-input placeholder="请输入" v-model="searchValue" @input="filterSearch" :border="true" :clear="true"></ui-input>
                </div>
                <template v-if="!isloading">
                    <div class="vd-ui-select-options-list" :class="{filter: filter}" v-if="showListData.length">
                        <div class="vd-ui-select-options-item" :class="{selected: item.checked}" v-for="(item, index) in showListData" @click="handlerOptionChange(item)">
                            <template v-if="!multi">
                                <i class="vd-ui_icon icon-radio1" v-if="!item.checked"></i>
                                <i class="vd-ui_icon icon-radio3" v-else></i>
                            </template>
                            <template v-else>
                                <i class="vd-ui_icon icon-checkbox1" v-if="!item.checked"></i>
                                <i class="vd-ui_icon icon-checkbox2" v-else></i>
                            </template>
                            <div class="vd-ui-select-options-avatar" v-if="avatar">
                                <img :src="item.avatar || '/mstatic/mui/image/crm-user-avatar.svg'"/>
                            </div>
                            <div class="vd-ui-select-options-txt" v-html="item.showLabel || item.label"></div>
                        </div>
                    </div>
                    <div class="vd-ui-select-options-empty" v-else>
                        <div class="empty-img">
                            <img src="/mstatic/image/empty/search-empty.svg" v-if="searchValue"/>
                            <img src="/mstatic/image/empty/list-empty.svg" v-else/>
                        </div>
                        <div class="empty-txt">
                            {{ searchValue ? '抱歉，没有找到您想搜索的结果' : '暂无数据'  }}
                        </div>
                    </div>
                </template>
                <div v-else class="vd-ui-select-loading">
                    <div class="vd-ui-select-loading-icon">
                        <i class="vd-ui_icon icon-loading"></i>
                    </div>
                    <div class="vd-ui-select-loading-txt">加载中...</div>
                </div>
                <div class="vd-ui-select-options-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="hideOptions">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerSelect">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
    </div>`,
    props: {
        value: {
            type: String | Number | Array
        },
        type: {
            type: String,
            default: '' // multi:多选
        },
        title: {
            type: String,
            default: ''
        },
        line: {
            type: Number,
            default: 1 // 回显默认行数
        },
        search: {
            type: Boolean,
            default: false
        },
        remote: {
            type: Boolean,
            default: false
        },
        remoteInfo: {

        },
        filter: {
            type: Boolean,
            default: false
        },
        list: {
            type: Array,
            default() {
                return [];
            }
        },
        multi: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: ''
        },
        defaultItems: {
            type: Array | Object,
        },
        avatar: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            listData: [],
            showListData: [],
            listTempData: [],
            selectedItem: {},
            selectedItemList: [],
            tempValue: '',
            showLength: 0,
            searchValue: '',
            isloading: false,
            timeout: null
        };
    },
    computed: {

    },
    watch: {
        list() {
            this.listData = JSON.parse(JSON.stringify(this.list));
            if (this.value && this.value.length) {
                this.initValue();
                this.$nextTick(() => {
                    this.calcShowTags();
                })
            }
        },
        value(newVal, oldVal) {
            this.selectedItemList = [];
            this.selectedItem = {};

            if (this.value && this.value.length) {
                this.initValue();
                this.$nextTick(() => {
                    this.calcShowTags();
                })
            } 
        }
    },
    mounted() {
        this.listData = JSON.parse(JSON.stringify(this.list));
        if (this.value && this.value.length) {
            this.initValue();
            this.$nextTick(() => {
                this.calcShowTags();
            })
        }
    },
    methods: {
        initValue() {
            if (this.listData.length) {
                this.listData.forEach(item => {
                    if (this.multi) {
                        this.value.forEach(val => {
                            if(val == item.value) {
                                this.selectedItemList.push(item);
                            }
                        })
                    } else if (this.value == item.value) {
                        this.selectedItem = item;
                    }
                });
            } else if (this.multi && this.defaultItems.length) {
                this.selectedItemList = this.defaultItems;
            } else if (!this.multi && this.defaultItems && Object.keys(this.defaultItems)) {
                this.selectedItem = this.defaultItems;
            }
        },
        getRemoteData(data, callback) {
            this.timeout && clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
                this.isloading = true;
                let reqMethod = this.remoteInfo.paramsMethod || 'get';

                let reqData = {
                    url: this.remoteInfo.url,
                    type: reqMethod,
                }

                if (reqMethod == 'get') {
                    reqData.params = data
                } else if (reqMethod == 'post') {
                    reqData.data = data;
                }

                this.$axios(reqData).then(({ data }) => {
                    this.isloading = false;

                    if (data.success) {
                        callback && callback(data.data);
                    }
                })
            }, 300)
        },
        showOptionDialog() {
            if(this.multi) {
                this.tempValue = JSON.parse(JSON.stringify(this.value || []));
            } else {
                this.tempValue = this.value;
            }
            if (this.remote && !this.listData.length) {
                this.getRemoteData(null, (data) => {
                    let list = [];

                    data.forEach(item => {
                        list.push({
                            label: item[this.remoteInfo.label],
                            value: item[this.remoteInfo.value],
                            checked: false
                        })
                    })
                    this.listData = list;
                    this.listTempData = JSON.parse(JSON.stringify(this.listData));
                    this.showListData = this.listTempData;

                    this.checkTempSelected();
                })
            } else {
                this.listTempData = JSON.parse(JSON.stringify(this.listData));
                this.showListData = this.listTempData;
                this.checkTempSelected();
            }
            this.searchValue = "";
            console.log(this.showListData);
            this.$refs.selectOptions.show();
        },
        hideOptions() {
            this.$refs.selectOptions.hide();
        },
        clear() {
            if(this.multi) {
                this.tempValue = [];
            } else {
                this.tempValue = '';
            }

            this.checkTempSelected();
        },
        handlerOptionChange(item) {
            console.log(item)
            if (this.multi) {
                // this.showListData.forEach((listItem, index) => {
                //     if (item.value == listItem.value) {
                //         listItem.checked = !item.checked;
                //     }
                // })
                // this.$set(this.showListData, this.showListData);

                // console.log(this.showListData)

                // this.listTempData.forEach((listItem, index) => {
                //     if (item.value == listItem.value) {
                //         listItem.checked = item.checked;
                //     }
                // })

                if (this.tempValue.indexOf(item.value) !== -1) {
                    this.tempValue.forEach((val, index) => {
                        if (val == item.value) {
                            this.tempValue.splice(index, 1);
                        }
                    })
                } else {
                    this.tempValue.push(item.value);
                }
            } else {
                // this.listTempData.forEach((listItem, index) => {
                //     listItem.checked = item.value == listItem.value
                // })

                // this.showListData.forEach((listItem, index) => {
                //     listItem.checked = item.value == listItem.value
                //     this.$set(this.showListData, index, listItem);
                // })
                this.tempValue = item.value;
            }

            // this.$forceUpdate();

            this.checkTempSelected();
        },
        checkTempSelected() {
            this.showListData.forEach(item => {
                if (this.multi) {
                    if (this.tempValue.indexOf(item.value) !== -1) {
                        item.checked = true;
                    } else {
                        item.checked = false;
                    }
                } else {
                    item.checked = this.tempValue == item.value;
                }
            })

            this.$forceUpdate();
        },
        handlerSelect() {
            if (this.multi) {
                this.selectedItemList = [];
                this.listTempData.forEach(listItem => {
                    if (this.tempValue.indexOf(listItem.value) !== -1) {
                        this.selectedItemList.push(listItem);
                    }
                })

                this.calcShowTags();
            } else {
                this.listTempData.forEach(listItem => {
                    if (this.tempValue == listItem.value) {
                        this.selectedItem = listItem;
                    }
                })
            }

            this.triggerChange(this.tempValue);

            this.listData = JSON.parse(JSON.stringify(this.listTempData))
            this.hideOptions();
        },
        calcShowTags() {
            this.showLength = 0;

            if (this.selectedItemList.length) {
                this.$nextTick(() => {
                    setTimeout(() => {
                        let hasHidden = false;
                        let showNum = 0;
                        this.$refs.tagItem.forEach(item => {
                            console.log(item.offsetTop)
                            if (item.offsetTop - 10 < 35 * (this.line - 1)) {
                                showNum++;
                            } else {
                                hasHidden = true;
                            }
                        })

                        if (hasHidden) {
                            this.showLength = showNum - 1;
                            console.log(this.showLength)
                        }
                    })
                })
            }

        },
        deleteMultiItem(item, index) {
            this.selectedItemList.splice(index, 1);
            this.value.splice(index, 1);

            this.listData.forEach(listItem => {
                if (listItem.value == item.value) {
                    listItem.checked = false;
                }
            })

            this.$nextTick(() => {
                this.calcShowTags();
            })
        },
        triggerChange(value) {
            this.$emit('input', value);
            if (this.multi) {
                this.$emit('change', this.selectedItemList);
            } else {
                this.$emit('change', this.selectedItem);
            }
        },
        filterSearch() {
            let searchValue = this.searchValue.trim();

            if (this.remoteInfo && this.remoteInfo.search) {
                let reqData = {};
                reqData[this.remoteInfo.searchKey] = searchValue;
                this.getRemoteData(reqData, (data) => {
                    let list = [];
                    let reg = new RegExp('(' + searchValue + ')', 'ig');

                    data.forEach(item => {
                        list.push({
                            label: item[this.remoteInfo.label],
                            value: item[this.remoteInfo.value],
                            showLabel: item[this.remoteInfo.label].replace(reg, '<span class="strong">$1</span>'),
                            checked: false
                        })
                    })

                    this.showListData = list;

                    this.checkTempSelected();
                })
            } else {
                if (searchValue) {
                    let list = [];
                    let reg = new RegExp('(' + searchValue + ')', 'ig');

                    this.listTempData.forEach(item => {
                        if (item.label.toUpperCase().indexOf(searchValue.toUpperCase()) !== -1) {
                            let itemData = JSON.parse(JSON.stringify(item))
                            itemData.showLabel = itemData.label.replace(reg, '<span class="strong">$1</span>');
                            list.push(itemData);
                        }
                    })

                    this.showListData = list;
                    this.checkTempSelected()
                } else {
                    this.showListData = this.listTempData;
                    this.checkTempSelected()
                }
            }

        }
    }
})