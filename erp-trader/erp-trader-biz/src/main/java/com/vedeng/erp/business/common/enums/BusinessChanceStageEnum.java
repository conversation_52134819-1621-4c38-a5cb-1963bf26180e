package com.vedeng.erp.business.common.enums;

import lombok.Getter;

/**
 * 商机阶段枚举
 */
@Getter
public enum BusinessChanceStageEnum {

    PRELIMINARY_NEGOTIATION(1,"初步洽谈"),
    
    OPPORTUNITY_VERIFICATION(2,"商机验证"),
    
    PRELIMINARY_SCHEME(3,"初步方案"),
    
    FINAL_SCHEME(4,"最终方案"),
    
    WINNING_ORDER(5,"赢单"),
    
    LOSE_ORDER(6,"关闭")
    
    ;
    
    private final int code;
    
    private final String desc;

    BusinessChanceStageEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static String getDescByCode(int code) {
        for (BusinessChanceStageEnum businessChanceStageEnum : BusinessChanceStageEnum.values()) {
            if (businessChanceStageEnum.getCode() == code) {
                return businessChanceStageEnum.getDesc();
            }
        }
        return "";
    }

}
