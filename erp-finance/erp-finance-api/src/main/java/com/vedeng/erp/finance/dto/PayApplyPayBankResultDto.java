package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PayApplyPayBankResultDto implements Serializable {

    private static final long serialVersionUID = -593122181786702839L;

    /**
     * 付款申请id
     */
    private Integer payApplyId;

    /**
     * 采购单id
     */
    private Integer buyOrderId;
    /**
     * 采购单号
     */
    private String buyOrderNo;

    /**
     * 付款银行id
     */
    private Integer payVedengBankId;

    /**
     * 付款银行开户行
     */
    private String payBankName;

    /**
     * 付款银行账号
     */
    private String payBankNo;

    /**
     * 金蝶系统银行编码
     */
    private String kingDeeBankCode;

}
