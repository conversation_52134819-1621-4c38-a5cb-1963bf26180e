package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceDownDefinedRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceDownRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceDownResponseDto;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.common.constant.TaxesConstant;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Objects;

/**
 * 销项票-下载
 */
@Slf4j
@Service
public class TaxesInvoiceDownOpenApiImpl extends AbstractTaxesOpenApiHandler {

    @Autowired
    private OssUtilsService ossUtilsService;

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        SaleInvoiceDownDefinedRequestDto definedDto = (SaleInvoiceDownDefinedRequestDto) taxesParam;
        SaleInvoiceDownRequestDto param = (SaleInvoiceDownRequestDto) taxesParam;
        SaleInvoiceDownResponseDto responseDto = new SaleInvoiceDownResponseDto();
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();
        responseDto.setReturnCode(return_info.getReturn_code());
        responseDto.setReturnMessage(StrUtil.isNotEmpty(return_info.getReturn_message()) ? return_info.getReturn_message() : TaxesReturnCodeEnum.getMsg(return_info.getReturn_code()));
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code())){
            responseDto.setIsSuccess(Boolean.TRUE);
        }
        String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
        if (StringUtils.isBlank(decodeStr)){
            log.info("销项票-下载失败，发票号{}，解码后为空",param.getFphm());
            return responseDto;
        }
        JSONObject jsonObject = JSONObject.parseObject(decodeStr);
        if (Objects.isNull(jsonObject)){
            log.info("销项票-下载失败，发票号{}，解码后转json为空",param.getFphm());
            return responseDto;
        }
        String data = jsonObject.getString("data");
        if (StringUtils.isBlank(data)){
            log.info("销项票-下载失败，发票号{}，解码后转json后获取data为空",param.getFphm());
            return responseDto;
        }
        String url = downloadFile(data,definedDto.getFileName(), changeSuffix(param.getFiletype()));
        if (StringUtils.isBlank(url)){
            log.info("销项票-下载失败，发票号{}，上传oss未获取到url",param.getFphm());
            responseDto.setReturnMessage("上传oss未获取到url");
            responseDto.setIsSuccess(Boolean.FALSE);
            return responseDto;
        }
        String ossUrl = changeUrl(param.getFiletype(),url);
        responseDto.setOssUrl(ossUrl);
        log.info("销项票-下载成功，发票号{}，出参：{}",param.getFphm(),JSONObject.toJSONString(responseDto));
        return responseDto;
    }

    /**
     * 存储前缀转换
     * @param suffix
     * @return
     */
    private String changeSuffix(String suffix){
        if(TaxesConstant.XML.equals(suffix)){
            return TaxesConstant.ZIP;
        }
        return suffix;
    }

    /**
     * zip下载路径转换
     * @param suffix
     * @param url
     * @return
     */
    private String changeUrl(String suffix,String url){
        if(TaxesConstant.XML.equals(suffix)){
            log.info("下载发票路径转换前:{}",url);
            String newUrl = url.replaceAll("display", "download");
            log.info("下载发票路径转换前:{}",newUrl);
            return newUrl;
        }
        return url;
    }

    /**
     * 上传oss
     * @param base64File 文件流
     * @param suffix 文件后缀
     * @return
     */
    public String downloadFile(String base64File, String fileName, String suffix) {
        if(StringUtils.isBlank(fileName)){
            fileName = "sd_" + IdUtil.simpleUUID();
        }
        InputStream inputStream = null;
        try {
            // 下载文件base64
            byte[] bytes = Base64.decode(base64File);
            inputStream = new ByteArrayInputStream(bytes);
            // 上传SSO
            return ossUtilsService.upload2OssForInputStream(suffix, fileName, inputStream);
        } catch (Exception e) {
            log.error("税金下载发票异常");
            return null;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("税金下载发票文件流关闭失败");
                return null;
            }
        }
    }
}
