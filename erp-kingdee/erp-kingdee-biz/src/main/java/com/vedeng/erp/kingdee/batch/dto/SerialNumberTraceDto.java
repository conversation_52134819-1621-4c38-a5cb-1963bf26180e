package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: sn 来源追踪表
 * @date 2023/6/28 15:43
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SerialNumberTraceDto {

    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer serialNumberTraceId;

    /**
     * 来源 (1.销售出库,2.销售换货出库,3.售后安调补充码)
     */
    private Integer serialNumberTraceSource;

    /**
     * 出入库单号
     */
    private String outInNo;

    /**
     * sn码
     */
    private String serialNumber;

    /**
     * 出入库id
     */
    private Integer warehouseGoodsOutInId;

    /**
     * 出入库明细id
     */
    private Integer warehouseGoodsOutInDetailId;

    /**
     * 换货出库时对应原始销售出库明细id
     */
    private Integer changeWarehouseGoodsOutInDetailId;

    /**
     * 销售单id
     */
    private Integer saleOrderId;

    /**
     * 销售明细id
     */
    private Integer saleOrderGoodsId;

    /**
     * 售后安调服务记录Id
     */
    private Integer afterSalesServiceId;

    /**
     * 售后安调服务记录明细id
     */
    private Integer afterSalesServiceDetailId;

    /**
     * sku
     */
    private String sku;

    /**
     * 来源时间
     */
    private Date sourceTime;

    /**
     * 是否推送金蝶0否，1是
     */
    private Integer isPush;

    private Long beginTime;

    private Long endTime;

    private int _pagesize;

    private int _skiprows;

}
