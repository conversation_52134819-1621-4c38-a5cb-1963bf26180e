package com.vedeng.erp.finance.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.erp.finance.dto.InvoiceApplyDetailDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

public interface InvoiceApplyService {


    /**
     * 分页获取待开票申请
     *
     * @return List<InvoiceApplyDto>
     */
    PageInfo<InvoiceApplyDto> getWaitInvoiceApply(Page page,String saleOrderNo);

    /**
     * 开票申请审核成功
     *
     * @param invoiceApplyId invoiceApplyId
     */
    void auditPassInvoiceApply(Integer invoiceApplyId);

    /**
     * 查询开票信息
     * @param invoiceApplyId
     * @return
     */
    InvoiceApplyDto getInvoiceApply(Integer invoiceApplyId);

    InvoiceApplyDto getInvoiceApplyAt(Integer invoiceApplyId);

    /**
     * 根据开票申请id 查原始单号
     * @param invoiceApplyId
     * @return
     */
    String getSaleOrderNoByInvoiceApplyId(Integer invoiceApplyId);

    /**
     * 根据开票申请id 查售后单号
     * @param invoiceApplyId
     * @return
     */
    String getAfterSalesNoByInvoiceApplyId(Integer invoiceApplyId);

    /**
     * 根据开票申请明细id查询
     *
     * @param invoiceApplyDetailId InvoiceApplyDetailDto
     * @return 开票申请明细主键
     */
    InvoiceApplyDetailDto getInvoiceApplyDetailById(Integer invoiceApplyDetailId);

    /**
     * 根据开票申请id查询
     *
     * @param invoiceApplyIdList
     * @return
     */
    List<InvoiceApplyDetailDto> getByInvoiceApplyId(List<Integer> invoiceApplyIdList);

    /**
     * 查看开票申请(销售、售后)
     * @param invoiceApplyId
     * @return
     */
    InvoiceApplyDto getAfterSaleInvoiceApplyInfo(Integer invoiceApplyId);

    /**
     * 更新开票方式为手动
     * @param invoiceApplyId data
     */
    void updateManualCreateType(Integer invoiceApplyId);
    /**
     * 根据销售单id查询开票申请
     * @param saleOrderId
     * @return
     */
    List<InvoiceApplyDto> getInvoiceApplyBySaleOrderId(Integer saleOrderId);
    List<InvoiceApplyDto> getInvoiceApplyByAfterSaleId(Integer afterSaleId);

}
