<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:apollo="http://www.ctrip.com/schema/apollo" xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd
		http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit.xsd">


    <!--rabbit连接-->
    <rabbit:connection-factory
            id="baseConnectionFactory"
            addresses="${baseMq.rabbitmq.host}"
            username="${baseMq.rabbitmq.username}"
            password="${baseMq.rabbitmq.password}"
            virtual-host="${baseMq.rabbitmq.virtualHost}"
            channel-cache-size="100"
            publisher-returns="true"
            publisher-confirms="true"/>

    <!-- 定义mq管理 -->
    <rabbit:admin connection-factory="baseConnectionFactory"/>

    <!-- 申明队列   -->
    <rabbit:queue name="esignBaseCallbackQueue" auto-declare="true" durable="true"/>


    <rabbit:listener-container connection-factory="baseConnectionFactory" acknowledge="manual"
                               failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}" concurrency="1">
        <rabbit:listener queues="esignBaseCallbackQueue" ref="electronicSignConsumer"/>
    </rabbit:listener-container>

    <!-- 声明队列 订阅base服务 -->
    <rabbit:queue name="erpSignedForQueue" auto-declare="true" durable="true"/>
    <rabbit:fanout-exchange name="signedForExchange">
        <rabbit:bindings>
            <rabbit:binding queue="erpSignedForQueue"/>
        </rabbit:bindings>
    </rabbit:fanout-exchange>
    <rabbit:listener-container connection-factory="baseConnectionFactory" acknowledge="manual"
                               failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}" concurrency="1" prefetch="50">
        <rabbit:listener queues="erpSignedForQueue" ref="updateExpressArrivalStatusConsumer"/>
    </rabbit:listener-container>

</beans>

