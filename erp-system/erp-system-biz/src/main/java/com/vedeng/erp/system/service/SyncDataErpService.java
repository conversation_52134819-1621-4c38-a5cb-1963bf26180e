package com.vedeng.erp.system.service;

import com.vedeng.erp.system.domain.entity.SyncDataErpEntity;

import java.util.List;

public interface SyncDataErpService {
    int deleteByPrimaryKey(Integer id);

    int insert(SyncDataErpEntity record);

    int insertSelective(SyncDataErpEntity record);

    SyncDataErpEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SyncDataErpEntity record);

    int updateByPrimaryKey(SyncDataErpEntity record);

    List<SyncDataErpEntity> selectByProcessStatus(Integer processStatus,String businessType);

    List<SyncDataErpEntity> selectByBusinessNo(String businessNo);
} 