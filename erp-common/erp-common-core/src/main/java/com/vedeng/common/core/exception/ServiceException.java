package com.vedeng.common.core.exception;


import com.vedeng.common.core.base.BaseException;
import com.vedeng.common.core.base.BaseResponseCode;

/**
 * Service层公用的Exception.
 *
 * <AUTHOR>
 */
public class ServiceException extends BaseException {

    private static final long serialVersionUID = 4393164921532381692L;

    public ServiceException(BaseResponseCode code) {
        super(code);
    }

    public ServiceException(BaseResponseCode baseResponseCode, Object data) {
        super(baseResponseCode, data);
    }

    public ServiceException(Integer code, String message) {
        super(code, message);
    }

    public ServiceException(Integer code, String message, Object data) {
        super(code, message, data);
    }

    public ServiceException(String message) {
        super(message);
    }

    public ServiceException(String message,Throwable cause) {
        super(message,cause);
    }
}
