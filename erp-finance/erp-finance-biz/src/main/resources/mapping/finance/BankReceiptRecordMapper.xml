<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.BankReceiptRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.BankReceiptRecordEntity">
    <!--@mbg.generated-->
    <!--@Table T_BANK_RECEIPT_RECORD-->
    <id column="BANK_RECEIPT_ALIAS_ID" jdbcType="BIGINT" property="bankReceiptAliasId" />
    <result column="ACCOUNT_TYPE" jdbcType="INTEGER" property="accountType" />
    <result column="ACCOUNT_NO" jdbcType="VARCHAR" property="accountNo" />
    <result column="BIZ_TYPE" jdbcType="INTEGER" property="bizType" />
    <result column="BIZ_ID" jdbcType="INTEGER" property="bizId" />
    <result column="BANK_NAME_ALIAS" jdbcType="VARCHAR" property="bankNameAlias" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BANK_RECEIPT_ALIAS_ID, ACCOUNT_TYPE, ACCOUNT_NO, BIZ_TYPE, BIZ_ID, BANK_NAME_ALIAS,
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BANK_RECEIPT_RECORD
    where BANK_RECEIPT_ALIAS_ID = #{bankReceiptAliasId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_BANK_RECEIPT_RECORD
    where BANK_RECEIPT_ALIAS_ID = #{bankReceiptAliasId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BANK_RECEIPT_ALIAS_ID" keyProperty="bankReceiptAliasId" parameterType="com.vedeng.erp.finance.domain.entity.BankReceiptRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_RECEIPT_RECORD (ACCOUNT_TYPE, ACCOUNT_NO, BIZ_TYPE,
      BIZ_ID, BANK_NAME_ALIAS, IS_DELETE,
      ADD_TIME, MOD_TIME, CREATOR,
      CREATOR_NAME, UPDATER, UPDATER_NAME,
      UPDATE_REMARK)
    values (#{accountType,jdbcType=INTEGER}, #{accountNo,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER},
      #{bizId,jdbcType=INTEGER}, #{bankNameAlias,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="BANK_RECEIPT_ALIAS_ID" keyProperty="bankReceiptAliasId" parameterType="com.vedeng.erp.finance.domain.entity.BankReceiptRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_RECEIPT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountType != null">
        ACCOUNT_TYPE,
      </if>
      <if test="accountNo != null">
        ACCOUNT_NO,
      </if>
      <if test="bizType != null">
        BIZ_TYPE,
      </if>
      <if test="bizId != null">
        BIZ_ID,
      </if>
      <if test="bankNameAlias != null">
        BANK_NAME_ALIAS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="bankNameAlias != null">
        #{bankNameAlias,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.BankReceiptRecordEntity">
    <!--@mbg.generated-->
    update T_BANK_RECEIPT_RECORD
    <set>
      <if test="accountType != null">
        ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        BIZ_TYPE = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        BIZ_ID = #{bizId,jdbcType=INTEGER},
      </if>
      <if test="bankNameAlias != null">
        BANK_NAME_ALIAS = #{bankNameAlias,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where BANK_RECEIPT_ALIAS_ID = #{bankReceiptAliasId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.BankReceiptRecordEntity">
    <!--@mbg.generated-->
    update T_BANK_RECEIPT_RECORD
    set ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER},
      ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
      BIZ_TYPE = #{bizType,jdbcType=INTEGER},
      BIZ_ID = #{bizId,jdbcType=INTEGER},
      BANK_NAME_ALIAS = #{bankNameAlias,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where BANK_RECEIPT_ALIAS_ID = #{bankReceiptAliasId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BANK_RECEIPT_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ACCOUNT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.accountType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.accountNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BIZ_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.bizType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BIZ_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.bizId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BANK_NAME_ALIAS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.bankNameAlias,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where BANK_RECEIPT_ALIAS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.bankReceiptAliasId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BANK_RECEIPT_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ACCOUNT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountType != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.accountType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountNo != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.accountNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BIZ_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bizType != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.bizType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BIZ_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bizId != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.bizId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BANK_NAME_ALIAS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankNameAlias != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.bankNameAlias,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when BANK_RECEIPT_ALIAS_ID = #{item.bankReceiptAliasId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where BANK_RECEIPT_ALIAS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.bankReceiptAliasId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="BANK_RECEIPT_ALIAS_ID" keyProperty="bankReceiptAliasId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_RECEIPT_RECORD
    (ACCOUNT_TYPE, ACCOUNT_NO, BIZ_TYPE, BIZ_ID, BANK_NAME_ALIAS, IS_DELETE, ADD_TIME,
      MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.accountType,jdbcType=INTEGER}, #{item.accountNo,jdbcType=VARCHAR}, #{item.bizType,jdbcType=INTEGER},
        #{item.bizId,jdbcType=INTEGER}, #{item.bankNameAlias,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER},
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR},
        #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-09-02-->
  <select id="findByAccountNoAndAccountType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BANK_RECEIPT_RECORD
    where ACCOUNT_NO=#{accountNo,jdbcType=VARCHAR} and ACCOUNT_TYPE=#{accountType,jdbcType=INTEGER}
  </select>
</mapper>
