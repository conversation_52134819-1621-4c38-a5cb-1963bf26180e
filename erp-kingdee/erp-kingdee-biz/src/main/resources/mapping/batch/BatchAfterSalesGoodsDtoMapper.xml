<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesGoodsDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALES_GOODS-->
    <id column="AFTER_SALES_GOODS_ID" jdbcType="INTEGER" property="afterSalesGoodsId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="ORDER_DETAIL_ID" jdbcType="INTEGER" property="orderDetailId" />
    <result column="GOODS_TYPE" jdbcType="INTEGER" property="goodsType" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="DELIVERY_DIRECT" jdbcType="INTEGER" property="deliveryDirect" />
    <result column="ARRIVAL_NUM" jdbcType="INTEGER" property="arrivalNum" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="ARRIVAL_USER_ID" jdbcType="INTEGER" property="arrivalUserId" />
    <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
    <result column="DELIVERY_NUM" jdbcType="INTEGER" property="deliveryNum" />
    <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
    <result column="SKU_REFUND_AMOUNT" jdbcType="DECIMAL" property="skuRefundAmount" />
    <result column="SKU_OLD_REFUND_AMOUNT" jdbcType="DECIMAL" property="skuOldRefundAmount" />
    <result column="IS_ACTION_GOODS" jdbcType="INTEGER" property="isActionGoods" />
    <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
    <result column="FACTORY_CODE" jdbcType="VARCHAR" property="factoryCode" />
    <result column="GOOD_CREATE_TIME" jdbcType="BIGINT" property="goodCreateTime" />
    <result column="GOOD_VAILD_TIME" jdbcType="BIGINT" property="goodVaildTime" />
    <result column="RKNUM" jdbcType="INTEGER" property="rknum" />
    <result column="AFTER_INVOICE_NUM" jdbcType="DECIMAL" property="afterInvoiceNum" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALES_GOODS_ID, AFTER_SALES_ID, ORDER_DETAIL_ID, GOODS_TYPE, GOODS_ID, NUM,
    PRICE, DELIVERY_DIRECT, ARRIVAL_NUM, ARRIVAL_TIME, ARRIVAL_USER_ID, ARRIVAL_STATUS,
    DELIVERY_NUM, DELIVERY_STATUS, DELIVERY_TIME, SKU_REFUND_AMOUNT, SKU_OLD_REFUND_AMOUNT,
    IS_ACTION_GOODS, UPDATE_DATA_TIME, FACTORY_CODE, GOOD_CREATE_TIME, GOOD_VAILD_TIME,
    RKNUM, AFTER_INVOICE_NUM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_GOODS
    where AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALES_GOODS
    where AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALES_GOODS_ID" keyProperty="afterSalesGoodsId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_GOODS (AFTER_SALES_ID, ORDER_DETAIL_ID, GOODS_TYPE, 
      GOODS_ID, NUM, PRICE, 
      DELIVERY_DIRECT, ARRIVAL_NUM, ARRIVAL_TIME, 
      ARRIVAL_USER_ID, ARRIVAL_STATUS, DELIVERY_NUM, 
      DELIVERY_STATUS, DELIVERY_TIME, SKU_REFUND_AMOUNT, 
      SKU_OLD_REFUND_AMOUNT, IS_ACTION_GOODS, UPDATE_DATA_TIME, 
      FACTORY_CODE, GOOD_CREATE_TIME, GOOD_VAILD_TIME, 
      RKNUM, AFTER_INVOICE_NUM)
    values (#{afterSalesId,jdbcType=INTEGER}, #{orderDetailId,jdbcType=INTEGER}, #{goodsType,jdbcType=INTEGER}, 
      #{goodsId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, 
      #{deliveryDirect,jdbcType=INTEGER}, #{arrivalNum,jdbcType=INTEGER}, #{arrivalTime,jdbcType=BIGINT}, 
      #{arrivalUserId,jdbcType=INTEGER}, #{arrivalStatus,jdbcType=INTEGER}, #{deliveryNum,jdbcType=INTEGER}, 
      #{deliveryStatus,jdbcType=INTEGER}, #{deliveryTime,jdbcType=BIGINT}, #{skuRefundAmount,jdbcType=DECIMAL}, 
      #{skuOldRefundAmount,jdbcType=DECIMAL}, #{isActionGoods,jdbcType=INTEGER}, #{updateDataTime,jdbcType=TIMESTAMP}, 
      #{factoryCode,jdbcType=VARCHAR}, #{goodCreateTime,jdbcType=BIGINT}, #{goodVaildTime,jdbcType=BIGINT}, 
      #{rknum,jdbcType=INTEGER}, #{afterInvoiceNum,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALES_GOODS_ID" keyProperty="afterSalesGoodsId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="orderDetailId != null">
        ORDER_DETAIL_ID,
      </if>
      <if test="goodsType != null">
        GOODS_TYPE,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="skuRefundAmount != null">
        SKU_REFUND_AMOUNT,
      </if>
      <if test="skuOldRefundAmount != null">
        SKU_OLD_REFUND_AMOUNT,
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="factoryCode != null and factoryCode != ''">
        FACTORY_CODE,
      </if>
      <if test="goodCreateTime != null">
        GOOD_CREATE_TIME,
      </if>
      <if test="goodVaildTime != null">
        GOOD_VAILD_TIME,
      </if>
      <if test="rknum != null">
        RKNUM,
      </if>
      <if test="afterInvoiceNum != null">
        AFTER_INVOICE_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="orderDetailId != null">
        #{orderDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="skuRefundAmount != null">
        #{skuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="skuOldRefundAmount != null">
        #{skuOldRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="isActionGoods != null">
        #{isActionGoods,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="factoryCode != null and factoryCode != ''">
        #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="goodCreateTime != null">
        #{goodCreateTime,jdbcType=BIGINT},
      </if>
      <if test="goodVaildTime != null">
        #{goodVaildTime,jdbcType=BIGINT},
      </if>
      <if test="rknum != null">
        #{rknum,jdbcType=INTEGER},
      </if>
      <if test="afterInvoiceNum != null">
        #{afterInvoiceNum,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto">
    <!--@mbg.generated-->
    update T_AFTER_SALES_GOODS
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="orderDetailId != null">
        ORDER_DETAIL_ID = #{orderDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="skuRefundAmount != null">
        SKU_REFUND_AMOUNT = #{skuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="skuOldRefundAmount != null">
        SKU_OLD_REFUND_AMOUNT = #{skuOldRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS = #{isActionGoods,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="factoryCode != null and factoryCode != ''">
        FACTORY_CODE = #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="goodCreateTime != null">
        GOOD_CREATE_TIME = #{goodCreateTime,jdbcType=BIGINT},
      </if>
      <if test="goodVaildTime != null">
        GOOD_VAILD_TIME = #{goodVaildTime,jdbcType=BIGINT},
      </if>
      <if test="rknum != null">
        RKNUM = #{rknum,jdbcType=INTEGER},
      </if>
      <if test="afterInvoiceNum != null">
        AFTER_INVOICE_NUM = #{afterInvoiceNum,jdbcType=DECIMAL},
      </if>
    </set>
    where AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto">
    <!--@mbg.generated-->
    update T_AFTER_SALES_GOODS
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      ORDER_DETAIL_ID = #{orderDetailId,jdbcType=INTEGER},
      GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      SKU_REFUND_AMOUNT = #{skuRefundAmount,jdbcType=DECIMAL},
      SKU_OLD_REFUND_AMOUNT = #{skuOldRefundAmount,jdbcType=DECIMAL},
      IS_ACTION_GOODS = #{isActionGoods,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      FACTORY_CODE = #{factoryCode,jdbcType=VARCHAR},
      GOOD_CREATE_TIME = #{goodCreateTime,jdbcType=BIGINT},
      GOOD_VAILD_TIME = #{goodVaildTime,jdbcType=BIGINT},
      RKNUM = #{rknum,jdbcType=INTEGER},
      AFTER_INVOICE_NUM = #{afterInvoiceNum,jdbcType=DECIMAL}
    where AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ORDER_DETAIL_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.orderDetailId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="GOODS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.goodsType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.deliveryDirect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.arrivalNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.arrivalUserId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.deliveryNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.deliveryStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="SKU_REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.skuRefundAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="SKU_OLD_REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.skuOldRefundAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="IS_ACTION_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.isActionGoods,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="FACTORY_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.factoryCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="GOOD_CREATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.goodCreateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="GOOD_VAILD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.goodVaildTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="RKNUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.rknum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AFTER_INVOICE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.afterInvoiceNum,jdbcType=DECIMAL}
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_GOODS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesId != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_DETAIL_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderDetailId != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.orderDetailId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsType != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.goodsType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsId != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.price != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryDirect != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.deliveryDirect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalNum != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.arrivalNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalTime != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalUserId != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.arrivalUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalStatus != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryNum != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.deliveryNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryStatus != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.deliveryStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryTime != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuRefundAmount != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.skuRefundAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_OLD_REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuOldRefundAmount != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.skuOldRefundAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ACTION_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isActionGoods != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.isActionGoods,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateDataTime != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="FACTORY_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.factoryCode != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.factoryCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOOD_CREATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodCreateTime != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.goodCreateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOOD_VAILD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodVaildTime != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.goodVaildTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="RKNUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.rknum != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.rknum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_INVOICE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterInvoiceNum != null">
            when AFTER_SALES_GOODS_ID = #{item.afterSalesGoodsId,jdbcType=INTEGER} then #{item.afterInvoiceNum,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_GOODS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="AFTER_SALES_GOODS_ID" keyProperty="afterSalesGoodsId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_GOODS
    (AFTER_SALES_ID, ORDER_DETAIL_ID, GOODS_TYPE, GOODS_ID, NUM, PRICE, DELIVERY_DIRECT, 
      ARRIVAL_NUM, ARRIVAL_TIME, ARRIVAL_USER_ID, ARRIVAL_STATUS, DELIVERY_NUM, DELIVERY_STATUS, 
      DELIVERY_TIME, SKU_REFUND_AMOUNT, SKU_OLD_REFUND_AMOUNT, IS_ACTION_GOODS, UPDATE_DATA_TIME, 
      FACTORY_CODE, GOOD_CREATE_TIME, GOOD_VAILD_TIME, RKNUM, AFTER_INVOICE_NUM)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.afterSalesId,jdbcType=INTEGER}, #{item.orderDetailId,jdbcType=INTEGER}, #{item.goodsType,jdbcType=INTEGER}, 
        #{item.goodsId,jdbcType=INTEGER}, #{item.num,jdbcType=INTEGER}, #{item.price,jdbcType=DECIMAL}, 
        #{item.deliveryDirect,jdbcType=INTEGER}, #{item.arrivalNum,jdbcType=INTEGER}, #{item.arrivalTime,jdbcType=BIGINT}, 
        #{item.arrivalUserId,jdbcType=INTEGER}, #{item.arrivalStatus,jdbcType=INTEGER}, 
        #{item.deliveryNum,jdbcType=INTEGER}, #{item.deliveryStatus,jdbcType=INTEGER}, 
        #{item.deliveryTime,jdbcType=BIGINT}, #{item.skuRefundAmount,jdbcType=DECIMAL}, 
        #{item.skuOldRefundAmount,jdbcType=DECIMAL}, #{item.isActionGoods,jdbcType=INTEGER}, 
        #{item.updateDataTime,jdbcType=TIMESTAMP}, #{item.factoryCode,jdbcType=VARCHAR}, 
        #{item.goodCreateTime,jdbcType=BIGINT}, #{item.goodVaildTime,jdbcType=BIGINT}, 
        #{item.rknum,jdbcType=INTEGER}, #{item.afterInvoiceNum,jdbcType=DECIMAL})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="AFTER_SALES_GOODS_ID" keyProperty="afterSalesGoodsId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID,
      </if>
      AFTER_SALES_ID,
      ORDER_DETAIL_ID,
      GOODS_TYPE,
      GOODS_ID,
      NUM,
      PRICE,
      DELIVERY_DIRECT,
      ARRIVAL_NUM,
      ARRIVAL_TIME,
      ARRIVAL_USER_ID,
      ARRIVAL_STATUS,
      DELIVERY_NUM,
      DELIVERY_STATUS,
      DELIVERY_TIME,
      SKU_REFUND_AMOUNT,
      SKU_OLD_REFUND_AMOUNT,
      IS_ACTION_GOODS,
      UPDATE_DATA_TIME,
      FACTORY_CODE,
      GOOD_CREATE_TIME,
      GOOD_VAILD_TIME,
      RKNUM,
      AFTER_INVOICE_NUM,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesGoodsId != null">
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      #{afterSalesId,jdbcType=INTEGER},
      #{orderDetailId,jdbcType=INTEGER},
      #{goodsType,jdbcType=INTEGER},
      #{goodsId,jdbcType=INTEGER},
      #{num,jdbcType=INTEGER},
      #{price,jdbcType=DECIMAL},
      #{deliveryDirect,jdbcType=INTEGER},
      #{arrivalNum,jdbcType=INTEGER},
      #{arrivalTime,jdbcType=BIGINT},
      #{arrivalUserId,jdbcType=INTEGER},
      #{arrivalStatus,jdbcType=INTEGER},
      #{deliveryNum,jdbcType=INTEGER},
      #{deliveryStatus,jdbcType=INTEGER},
      #{deliveryTime,jdbcType=BIGINT},
      #{skuRefundAmount,jdbcType=DECIMAL},
      #{skuOldRefundAmount,jdbcType=DECIMAL},
      #{isActionGoods,jdbcType=INTEGER},
      #{updateDataTime,jdbcType=TIMESTAMP},
      #{factoryCode,jdbcType=VARCHAR},
      #{goodCreateTime,jdbcType=BIGINT},
      #{goodVaildTime,jdbcType=BIGINT},
      #{rknum,jdbcType=INTEGER},
      #{afterInvoiceNum,jdbcType=DECIMAL},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      ORDER_DETAIL_ID = #{orderDetailId,jdbcType=INTEGER},
      GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      SKU_REFUND_AMOUNT = #{skuRefundAmount,jdbcType=DECIMAL},
      SKU_OLD_REFUND_AMOUNT = #{skuOldRefundAmount,jdbcType=DECIMAL},
      IS_ACTION_GOODS = #{isActionGoods,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      FACTORY_CODE = #{factoryCode,jdbcType=VARCHAR},
      GOOD_CREATE_TIME = #{goodCreateTime,jdbcType=BIGINT},
      GOOD_VAILD_TIME = #{goodVaildTime,jdbcType=BIGINT},
      RKNUM = #{rknum,jdbcType=INTEGER},
      AFTER_INVOICE_NUM = #{afterInvoiceNum,jdbcType=DECIMAL},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="AFTER_SALES_GOODS_ID" keyProperty="afterSalesGoodsId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID,
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="orderDetailId != null">
        ORDER_DETAIL_ID,
      </if>
      <if test="goodsType != null">
        GOODS_TYPE,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="skuRefundAmount != null">
        SKU_REFUND_AMOUNT,
      </if>
      <if test="skuOldRefundAmount != null">
        SKU_OLD_REFUND_AMOUNT,
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="factoryCode != null and factoryCode != ''">
        FACTORY_CODE,
      </if>
      <if test="goodCreateTime != null">
        GOOD_CREATE_TIME,
      </if>
      <if test="goodVaildTime != null">
        GOOD_VAILD_TIME,
      </if>
      <if test="rknum != null">
        RKNUM,
      </if>
      <if test="afterInvoiceNum != null">
        AFTER_INVOICE_NUM,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesGoodsId != null">
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="orderDetailId != null">
        #{orderDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="skuRefundAmount != null">
        #{skuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="skuOldRefundAmount != null">
        #{skuOldRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="isActionGoods != null">
        #{isActionGoods,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="factoryCode != null and factoryCode != ''">
        #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="goodCreateTime != null">
        #{goodCreateTime,jdbcType=BIGINT},
      </if>
      <if test="goodVaildTime != null">
        #{goodVaildTime,jdbcType=BIGINT},
      </if>
      <if test="rknum != null">
        #{rknum,jdbcType=INTEGER},
      </if>
      <if test="afterInvoiceNum != null">
        #{afterInvoiceNum,jdbcType=DECIMAL},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="orderDetailId != null">
        ORDER_DETAIL_ID = #{orderDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="skuRefundAmount != null">
        SKU_REFUND_AMOUNT = #{skuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="skuOldRefundAmount != null">
        SKU_OLD_REFUND_AMOUNT = #{skuOldRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS = #{isActionGoods,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="factoryCode != null and factoryCode != ''">
        FACTORY_CODE = #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="goodCreateTime != null">
        GOOD_CREATE_TIME = #{goodCreateTime,jdbcType=BIGINT},
      </if>
      <if test="goodVaildTime != null">
        GOOD_VAILD_TIME = #{goodVaildTime,jdbcType=BIGINT},
      </if>
      <if test="rknum != null">
        RKNUM = #{rknum,jdbcType=INTEGER},
      </if>
      <if test="afterInvoiceNum != null">
        AFTER_INVOICE_NUM = #{afterInvoiceNum,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <select id="findByAfterSalesId" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List" />
    FROM
        T_AFTER_SALES_GOODS
    WHERE
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    and GOODS_TYPE = 0
    </select>

  <select id="getSpecialGoods" resultMap="BaseResultMap">
    select
    a.AFTER_SALES_GOODS_ID, a.AFTER_SALES_ID, a.ORDER_DETAIL_ID, a.GOODS_ID, a.NUM, a.PRICE, a.DELIVERY_DIRECT,
    a.ARRIVAL_NUM, a.ARRIVAL_TIME, a.ARRIVAL_USER_ID, a.ARRIVAL_STATUS, a.GOODS_TYPE,
    CONCAT('V',a.GOODS_ID) as SKU
    from T_AFTER_SALES_GOODS a
    where a.GOODS_TYPE = 1
      and a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    limit 1
    </select>

  <select id="getKingDeeFCostID" resultType="java.lang.String">
    select tscc.UNIT_KING_DEE_NO
    from V_CORE_SKU vcs
           left join T_SYS_COST_CATEGORY tscc on vcs.COST_CATEGORY_ID = tscc.COST_CATEGORY_ID
    where vcs.SKU_NO = #{sku,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-11-30-->
  <select id="findByAfterSalesGoodsIdList" resultType="java.lang.Integer">
        select
        ORDER_DETAIL_ID
        from T_AFTER_SALES_GOODS
        where AFTER_SALES_GOODS_ID in
    <foreach collection="list" item="item" separator="," close=")" open="(">
      #{item,jdbcType=INTEGER}
    </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-12-06-->
  <select id="selectByOrderDetailIdsAndTotalAfterNum" resultMap="BaseResultMap">
        select
        ORDER_DETAIL_ID,SUM(ABS(IFNULL(TASG.NUM,0))) as NUM
        from T_AFTER_SALES_GOODS TASG left join T_AFTER_SALES TAS on TASG.AFTER_SALES_ID = TAS.AFTER_SALES_ID
        where TASG.ORDER_DETAIL_ID  in
         <foreach collection="list" item="item" separator="," close=")" open="(">
            #{item,jdbcType=INTEGER}
         </foreach>
        and TAS.ATFER_SALES_STATUS =2 and TAS.TYPE=#{type} and TAS.SUBJECT_TYPE = #{subjectType}
    group by TASG.ORDER_DETAIL_ID
    </select>

  <select id="findByAfterSalesGoodsIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_GOODS
    where AFTER_SALES_GOODS_ID in
    <foreach collection="list" item="item" separator="," close=")" open="(">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
</mapper>