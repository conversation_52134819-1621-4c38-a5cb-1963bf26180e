package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.kpi.replica.dao.AfterSalesReplicaMapper;
import com.vedeng.kpi.replica.dao.CapitalBillReplicaMapper;
import com.vedeng.kpi.replica.dao.SaleorderReplicaMapper;
import com.vedeng.kpi.service.KpiCalculateService;
import com.vedeng.order.model.Saleorder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 计算订单和售后单的五行业绩
 * <AUTHOR>
 * @date created in 2020/6/30 8:50
 */
@Component
@JobHandler("kpiCalculateTask")
public class KpiCalculateTask extends AbstractJobHandler {

    @Autowired
    @Qualifier("saleorderReplicaMapper")
    private SaleorderReplicaMapper saleorderReplicaMapper;

    @Autowired
    @Qualifier("afterSalesReplicaMapper")
    private AfterSalesReplicaMapper afterSalesReplicaMapper;

    @Autowired
    @Qualifier("capitalBillReplicaMapper")
    private CapitalBillReplicaMapper capitalBillReplicaMapper;

    @Autowired
    private KpiCalculateService kpiCalculateService;

    /**
     * xxl-job传递参数为json字符串
     * @param s
     * {
     *     "saleorderId":"",
     *     "saleorderNo": "vs1231",
     *     "startTime": "2020-06-01",
     *     "endTime": "2020-06-30",
     *     "startSaleorderId":1
     * }
     * @return 结果
     * @throws Exception 异常
     */
    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        if (StringUtils.isBlank(s)){
            return ReturnT.FAIL;
        }
        //JSON解析s
        JSONObject jsonObject = JSONObject.fromObject(s);

        if (jsonObject.has("saleorderNo")){
            String saleorderNo = jsonObject.getString("saleorderNo");
            if (StringUtils.isNotBlank(saleorderNo)){
                Arrays.stream(saleorderNo.split(",")).forEach(
                        item -> Optional.ofNullable(saleorderReplicaMapper.getSaleorderDetails(item))
                                .ifPresent(this::calculateKpiBySaleorder)
                );
            }
        }else if (jsonObject.has("saleorderId")){
            String saleorderId = jsonObject.getString("saleorderId");
            if (StringUtils.isNotBlank(saleorderId)){
                Arrays.stream(saleorderId.split(",")).forEach(
                        item -> Optional.ofNullable(saleorderReplicaMapper.getSaleorderDetailsBySaleorderId(Integer.valueOf(item)))
                                .ifPresent(this::calculateKpiBySaleorder)
                );
            }
        } else if (jsonObject.has("startTime")){
            String startTime = jsonObject.getString("startTime");
            if (StringUtils.isBlank(startTime)){
                return ReturnT.FAIL;
            }
            long startTimestamp = DateUtil.convertLong(startTime + " 00:00:00","yyyy-MM-dd HH:mm:ss");
            long endTimestamp = 0;
            if (jsonObject.has("endTime")){
                String endTime = jsonObject.getString("endTime");
                if (StringUtils.isNotBlank(endTime)){
                    endTimestamp = DateUtil.convertLong(endTime + " 23:59:59","yyyy-MM-dd HH:mm:ss");
                }
            }
            int startSaleorderId = 0;
            if (jsonObject.has("startSaleorderId")){
                startSaleorderId = jsonObject.getInt("startSaleorderId");
            }

            int offset = 0;
            int saleorderSize;
            int limit = 100;
            do{
                List<Integer> saleorderIdList = saleorderReplicaMapper.getSaleorderIdByValidTimeOrModTime(startTimestamp,endTimestamp,startSaleorderId,offset*limit,limit);
                if (saleorderIdList.isEmpty()){
                    XxlJobLogger.log("当前offset为：{}，五行历史数据计算任务完成",offset);
                    break;
                }
                List<Saleorder> saleorderList = saleorderReplicaMapper.getSaleorderBySaleorderIdList(saleorderIdList);
                saleorderSize = saleorderList.size();
                XxlJobLogger.log("Offset：{}，订单数量：{}",offset,saleorderList.size());
                saleorderList.parallelStream().forEach(this::calculateKpiBySaleorder);
                offset++;
            } while (saleorderSize > 0);
        }
        return ReturnT.SUCCESS;
    }


    private void calculateKpiBySaleorder(Saleorder saleorder){
        Long executeTime = kpiCalculateService.checkSaleorderPerformanceOfSaleorder(saleorder);

        //获取订单的售后单（售后单为销售退货、且退款状态为已退款）
        afterSalesReplicaMapper.getAfterSalesBySaleorderNo(saleorder.getSaleorderNo(),executeTime)
                .forEach(afterSales -> {
                    if (afterSales.getType() == 539){
                        Integer refundStatus = afterSales.getAfterSalesDetail().getRefundAmountStatus();
                        if (refundStatus != null && refundStatus == 3){
                            //获取售后单的最新一条流水
                            Optional.ofNullable(capitalBillReplicaMapper.getFirstCapitalBillByOrder(afterSales.getAfterSalesNo()))
                                    .ifPresent(capitalBill -> kpiCalculateService.checkSalesPerformanceOfAfterSales(afterSales,capitalBill.getAddTime()));
                        }
                    }
                });
    }
}
