package com.wms.service.processor;

import com.alibaba.fastjson.JSON;
import com.netflix.discovery.converters.Auto;
import com.wms.service.WmsIdempotentCheckService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public abstract class AbstractWMSCalllBackProcessor<T> implements WMSCalllBackProcessor<T>{

    public static Logger logger = LoggerFactory.getLogger(AbstractWMSCalllBackProcessor.class);

    @Autowired
    private WmsIdempotentCheckService wmsIdempotentCheckService;

    @Resource
    private RedissonClient redissonClient;

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public void dealWithRequest(T requestBean) throws Exception{
        //是否需要幂等操作，有些接口天然幂等(比如更新商品的宽高体积等) 不需要再做幂等的校验
        boolean needValidator = needIdempotentValidator();

        if(needValidator){

            String businessKey = getBusinessKey(requestBean);

            RLock lock = redissonClient.getLock(businessKey);

            boolean isGetLock = lock.tryLock();
            //没有获取到锁 直接抛错返回
            if(!isGetLock){
//                throw new Exception("已有相同的业务key: "+ businessKey +"在处理中,请稍后重试");
                logger.info("已有相同的业务key: "+ businessKey +"在处理中,请稍后重试");
                return;
            }

            try {
                //幂等校验
                Long idempotentId = wmsIdempotentCheckService.idempotentCheck(businessKey,JSON.toJSONString(requestBean));
                if(idempotentId == null){
                    logger.info("已有业务key: "+ businessKey +"消息已经成功消费，不能重复消费!");
                    return;
                }

                commonValidator(requestBean);

                doDealWithRequest(requestBean);

                //处理成功，更新消息为成功
                wmsIdempotentCheckService.updateIdempotentCheckSuccess(idempotentId);

            } finally {
                lock.unlock();
            }

        }else{

            commonValidator(requestBean);

            doDealWithRequest(requestBean);

        }

    }

    //protected abstract T convertDataToBean(String data);

    protected abstract String getBusinessKey(T requestBean);

    protected abstract boolean needIdempotentValidator();

    protected abstract void commonValidator(T requestBean) throws Exception;

    protected abstract void doDealWithRequest(T requestBean) throws Exception;

}
