package com.vedeng.erp.trader.mapper;
import java.util.Collection;
import java.util.Date;

import com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TraderVoiceFieldResultMapper {
    /**
     * delete by primary key
     *
     * @param fieldResultId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer fieldResultId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(VoiceFieldResultEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(VoiceFieldResultEntity record);

    /**
     * select by primary key
     *
     * @param fieldResultId primary key
     * @return object by primary key
     */
    VoiceFieldResultEntity selectByPrimaryKey(Integer fieldResultId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(VoiceFieldResultEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(VoiceFieldResultEntity record);

    /**
     * select by communicateRecordId and senceCode
     *
     * @param communicateRecordId
     * @param senceCode
     * @return object by communicateRecordId and senceCode
     */
    List<VoiceFieldResultEntity> selectByCommunicateRecordIdAndSence(@Param("communicateRecordId") Integer communicateRecordId
            , @Param("senceCode")String senceCode);

    /**
     * 通话摘要场景详细字段-查询以提供页面展示
     * @param communicateRecordId
     * @param senceCode
     * @param groupCode
     * @return
     */
    List<VoiceFieldResultEntity> selectByCommunicateRecordIdAndSenceGroup(@Param("communicateRecordId") Integer communicateRecordId
            , @Param("senceCode")String senceCode, @Param("groupCode")String groupCode);

    List<VoiceFieldResultEntity> findByCommunicateRecordId(@Param("communicateRecordId")Integer communicateRecordId);

    List<VoiceFieldResultEntity> selectByGroupCodeAndFieldCodeAndCommunicateRecordId(@Param("groupCode")String groupCode,@Param("fieldCode")String fieldCode,@Param("communicateRecordId")Integer communicateRecordId);


    List<VoiceFieldResultEntity> findByAddTimeBetween(@Param("minAddTime")Date minAddTime,@Param("maxAddTime")Date maxAddTime);


    List<VoiceFieldResultEntity> findByCommunicateRecordIdIn(@Param("communicateRecordIdCollection")Collection<Integer> communicateRecordIdCollection);




}