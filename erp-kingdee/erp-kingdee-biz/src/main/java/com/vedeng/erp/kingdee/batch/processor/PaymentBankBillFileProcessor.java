package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.domain.command.KingDeeFileCommand;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayBillMapper;
import com.vedeng.erp.kingdee.service.impl.KingDeePayBillServiceImpl;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PaymentBankBillFileProcessor.java
 * @Description TODO 付款回单
 * @createTime 2023年06月05日 09:39:00
 */
@Slf4j
@Service
public class PaymentBankBillFileProcessor extends BaseProcessor<BatchBankBillDto, KingDeeFileCommand> {

    @Autowired
    private KingDeePayBillServiceImpl kingDeePayBillService;

    @Override
    public KingDeeFileCommand doProcess(BatchBankBillDto input, JobParameters params, ExecutionContext stepContext) throws Exception {
        List<KingDeePayBillDto> deePayBillDtoList = kingDeePayBillService.getPayBillInfoBybankBillId(input.getBankBillId(),0);
        if (CollectionUtils.isEmpty(deePayBillDtoList)){
            return null;
        }
        KingDeePayBillDto kingDeePayBillDto = deePayBillDtoList.get(0);
        KingDeeFileCommand kingDeeFileCommand = new KingDeeFileCommand();
        FileInfoDto fileInfoDto = FileInfoUtils.getBase64FromUrl(input.getReceiptUrl());
        kingDeeFileCommand.setFormId(KingDeeFormConstant.AP_PAYBILL);
        kingDeeFileCommand.setInterId(kingDeePayBillDto.getFId());
        kingDeeFileCommand.setFileName(kingDeePayBillDto.getFQzokCgddh() + fileInfoDto.getSuffix());
        kingDeeFileCommand.setBillNo(kingDeePayBillDto.getFBillNo());
        kingDeeFileCommand.setAliasFileName(kingDeePayBillDto.getFQzokCgddh());
        kingDeeFileCommand.setSendByte(fileInfoDto.getFileBase64());
        stepContext.put("kingDeePayBillId",kingDeePayBillDto.getKingDeePayBillId());
        return kingDeeFileCommand;
    }
}
