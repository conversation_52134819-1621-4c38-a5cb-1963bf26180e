package com.vedeng.infrastructure.file.domain;

import com.vedeng.common.core.utils.validator.group.AddGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 文件
 * @date 2024/9/27 8:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GlobalFileRequest {

    /**
     * 业务id
     */
    @NotNull(message = "业务id不能为空", groups = {AddGroup.class})
    private Integer bizId;

    /**
     * 业务类型
     * 01线索池 02商机库 03 报价单
     */
    @NotNull(message = "业务类型不能为空", groups = {AddGroup.class})
    private String bizType;

    /**
     * 文件
     */
    @NotNull(message = "文件不能为空", groups = {AddGroup.class})
    private MultipartFile file;

    /**
     * 文件id
     */
    private Integer attachmentId;


    public Integer mapAttachmentFunction(String bizType) {
        switch (bizType) {
            case "01":
                return 0;
            case "02":
                return 463;
            case "03":
                return 494;
            default:
                return null;
        }
    }
}
