package com.vedeng.todolist.service;

import java.util.List;

/**
 * @Author: daniel
 * @Date: 2020/12/21 08 46
 * @Description:
 */
public interface RiskCheckLogService {

    /**
     * 根据业务属性：订单类型，获取已完成但未触发的待办事项订单号
     * 订单涉及到的风控待办事项已全部完成
     * @param riskCheckTodoListBuzPropertyEnum 风控待办事项业务属性枚举类
     * @return 销售订单号
     */
    List<String> getUnTriggeredOrderOfRickCheck(String riskCheckTodoListBuzPropertyEnum);

    /**
     * 自动完成风控关联订单触发任务
     * @param riskCheckBuzExtra 风控涉及订单号
     */
    void autoFinishTriggerOfRiskCheck(String riskCheckBuzExtra);

    /**
     * 手动触发风控关联订单任务
     * @param riskCheckBuzExtra 风控涉及的订单号
     * @param riskCheckTriggerUser 人工触发者
     * @param riskCheckTriggerComment 触发备注
     */
    void manualFinishTriggerOfRiskCheck(String riskCheckBuzExtra, Integer riskCheckTriggerUser, String riskCheckTriggerComment);

    /**
     * 当耗材单子被风控时，给对应的归属销售发送站内信
     * 如果同一个耗材单被多个条件风控，只需要发送一条站内信
     * @param hcOrderNo 耗材单号
     */
    void sendMessage2SaleWhenHcOrderRiskCheck(String hcOrderNo);
}
