<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">
    <div class="trader-assign">
        <h2>客户划拨</h2>
        <div class="assign-type">
            <span class="required">*</span>划拨方式:
            <el-radio-group v-model="assignType" @change="tabChange" size="mini">
                <el-radio :label="1">按客户</el-radio>
                <el-radio :label="2">按销售</el-radio>
                <el-radio :label="3">按导入</el-radio>
            </el-radio-group>
        </div>

        <!-- 按销售划拨 -->
        <div v-if="assignType === 2" class="assign-sales">
            <div class="search-area">
                <div class="select-sales">
                    选择销售:
                    <el-select size="mini" v-model="selectedSales" filterable placeholder="输入英文名搜索" >
                        <el-option v-for="item in salesList" 
                            :key="item.userId" 
                            :label="item.username"
                            :value="item.userId">
                        </el-option>
                    </el-select>
                    <el-button size="mini" type="primary" @click="searchSales" :loading="searchLoading" >搜索</el-button>
                </div>
                
                <div class="select-region">
                    选择地区:
                    <el-select size="mini" v-model="province" placeholder="选择省" @change="getCityList" filterable clearable @clear="clearProvince">
                        <el-option v-for="item in provinceList" 
                            :key="item.regionId" 
                            :label="item.regionName"
                            :value="item.regionId">
                        </el-option>
                    </el-select>
                    <el-select size="mini" v-model="city" placeholder="选择市" @change="getRegionIdList" filterable clearable @clear="clearCity">
                        <el-option v-for="item in cityList"
                               :key="item.regionId"
                               :label="item.regionName"
                               :value="item.regionId">
                        </el-option>
                    </el-select>
                    <el-select size="mini" v-model="regionId" placeholder="选择区" filterable clearable>
                        <el-option v-for="item in regionIdList"
                               :key="item.regionId"
                               :label="item.regionName"
                               :value="item.regionId">
                        </el-option>
                    </el-select>
                </div>
            </div>

            <el-table size="mini"
                      style="width: 800px"
                      :data="salesCustomerList"
                      v-loading="searchLoading"
                      border>
                <el-table-column prop="userName" label="销售人员"></el-table-column>
                <el-table-column prop="publicCustomerNum" label="公海客户数"></el-table-column>
                <el-table-column prop="belongCustomerNum" label="名下客户数"></el-table-column>
            </el-table>
        </div>

        <!-- 按客户划拨 -->
        <div v-if="assignType === 1" class="assign-customer">
            <div class="search-customer">
                选择客户:
                <el-autocomplete
                        size="mini"
                        v-model="selectedCustomer"
                        :fetch-suggestions="tNameSearch"
                        select-when-unmatched="true"
                        highlight-first-item="true"
                        placeholder="输入客户名称"
                        :trigger-on-focus="false"
                        @select="tNameHandleSelect"
                        style="width:30%"
                        suffix-icon="el-icon-search"
                ></el-autocomplete>
            </div>

            <el-table :data="customerTableData" border size="mini" style="width: 900px">
                <el-table-column prop="publicCustomer" label="是否公海客户" prop="isPublic"></el-table-column>
                <el-table-column prop="traderName" label="客户名称" prop="name"></el-table-column>
                <el-table-column prop="areaStr" label="地区" prop="region"></el-table-column>
                <el-table-column prop="belongUser" label="归属销售" prop="currentSales"></el-table-column>
                <el-table-column prop="orgBelongUser" label="公海前归属销售" prop="previousSales"></el-table-column>
                <el-table-column prop="buyCount" label="合作次数" prop="cooperationCount"></el-table-column>
                <el-table-column prop="customerLevelStr" label="客户等级" prop="level"></el-table-column>
            </el-table>
        </div>

        <!-- 按导入划拨 -->
        <div v-if="assignType === 3" class="assign-import">
            <el-upload
                ref="upload"
                multiple="false"
                show-file-list
                accept=".xlsx,.xls"
                limit="1"
                action="/trader/customer/importChangeTraderOwner.do"
                :on-remove="onRemoveFile"
                :on-success="handleUploadSuccess"
                :before-upload="beforeUpload"
                :on-preview="onPreview"
                :file-list="fileList"
                :auto-upload="false">
                <el-button type="primary" size="small" @click="handleUpload" size="mini">点击上传</el-button>
            </el-upload>
            <div class="upload-tips">
                <p>- 支持格式: EXCEL格式;</p>
                <p>- 大小不超过10MB;</p>
                <p>- 如果您没有标准模板，请下载<el-link size="mini" type="primary" @click="downloadTemplate">标准模板</el-link></p>
            </div>
            
            <div>
                <el-button style="margin: 100px 300px" size="mini" type="primary" @click="submitUpload" :loading="uploadLoading" :disabled="uploadDisabled">确定</el-button>
            </div>
        </div>

        <!-- 公共部分 -->
        <div class="assign-bottom" v-if="assignType !== 3">
            <div class="assign-to">
                划拨销售:
                <el-select size="mini" v-model="assignToSales" filterable clearable placeholder="输入英文名搜索" @change="selectSale">
                    <el-option v-for="item in salesList"
                               :key="item.userId"
                               :label="item.username"
                               :value="item.userId">
                    </el-option>
                </el-select>
                <el-button type="primary" size="mini" @click="confirmAssign" :loading="confirmLoading">确认转移</el-button>
            </div>
            <div class="sales-dept">
                销售部门:
                <span>{{selectedDept}}</span>
            </div>
        </div>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderAssign.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    let vmjsp = new Vue({
        el: '#app',
        data() {
            return {
                searchLoading: false,
                confirmLoading: false,
                uploadLoading:false,
                uploadDisabled:false,
                assignType: 1, // 划拨方式：1-按客户 2-按销售 3-按导入

                // 销售相关
                selectedSales: '',
                salesList: [],
                province: '',
                city: '',
                regionId: '',
                provinceList: [],
                cityList: [],
                regionIdList: [],
                salesCustomerList: [],

                // 客户相关
                selectedCustomer: '',
                customerList: [],
                customerTableData: [],

                // 公共部分
                assignToSales: '',
                selectedDept: '',
                fileList: [],
                uploadFile:''


            }
        },
        mounted() {
            loadingApp();
        },

        created() {
            this.initData();
        },

        methods: {
            // 初始化数据
            initData() {
                this.getProvinceList();
                this.getSalesList();
            },

            tabChange(){
                this.resetForm();
            },

            // 获取省份列表
            getProvinceList() {
                // API调用
                let param = {
                    regionId: 1,
                }
                getRegion(param).then(res => {
                    if (res.data.code != 0){
                        return
                    }
                    this.provinceList = res.data.listData;
                })
            },
            
            getCityList() {
                let param = {
                    regionId: this.province,
                }
                getRegion(param).then(res => {
                    if (res.data.code != 0){
                        return
                    }
                    this.cityList = res.data.listData;
                    this.regionIdList = [];
                    this.city = '';
                    this.regionId = '';
                })
            },

            getRegionIdList(){
                let param = {
                    regionId: this.city,
                }
                getRegion(param).then(res => {
                    if (res.data.code != 0){
                        return
                    }
                    this.regionIdList = res.data.listData;
                    this.regionId = '';
                })
            },

            clearProvince(){
                this.province = '';
                this.city = '';
                this.regionId = '';
                this.cityList = [];
                this.regionIdList = [];
            },

            clearCity(){
                debugger
                this.city = '';
                this.regionId = '';
                this.regionIdList = [];
            },

            tNameSearch(queryString, callback) {
                if (queryString.trim() === ''){
                    return;
                }
                if(queryString.length > 50){
                    var results = [];
                    callback(results);
                    this.$message({
                        showClose: false,
                        message: '关键字需小于等于50个字',
                        type: 'warning',
                        duration: 1000,
                        offset:70
                    });
                    return
                }
                let param = {
                    traderName: queryString,
                }
                searchTraderName(param).then(res => {
                    if (res.data.code != 0){
                        return
                    }
                    var results = res.data.data.map(item =>({
                        value: item.traderName,
                        id: item.traderId
                    }));
                    callback(results);
                })
            },

            // 获取销售列表
            getSalesList() {
                // API调用
                let param = {
                    username: this.assignToSales,
                }
                console.log(param)
                searchUser(param).then(res => {
                    console.log(res.data.data)
                    this.salesList = res.data.data;
                })
            },
            
            selectSale(){
                let param = {
                    userId: this.assignToSales
                }
                queryOrgName(param).then(res => {
                    this.selectedDept = res.data.data;
                })
            },

            // 按销售搜索
            searchSales() {
                if (this.selectedSales == ''){
                    this.$message({
                        message: '请选择销售',
                        duration: 3000,
                        showClose:true,
                        type: 'error',
                    });
                    return;
                }
                this.searchLoading = true;
                // API调用获取销售的客户数据
                let param = {
                    userId: this.selectedSales,
                    provinceId: this.province,
                    cityId: this.city,
                    regionId: this.regionId,
                }
                queryUserInfo(param).then(res => {
                    this.searchLoading = false;
                    if (res.data.code != 0){
                        return
                    }
                    this.salesCustomerList = res.data.data;
                })
            },

            // 选择了
            tNameHandleSelect(item){
                // 如果选择的是空，则提示选择客户
                if (!item.id > 0){
                    this.$message({
                        message: '请选择客户',
                        duration: 3000,
                        showClose:true,
                        type: 'error',
                    });
                    return;
                }
                let param = {
                    traderId: item.id
                }
                queryAssignInfo(param).then(res => {
                    if (res.data.code != 0){
                        return
                    }
                    this.customerTableData = res.data.data;
                })
                
            },

            // 确认划拨
            confirmAssign() {
                if (this.assignType == 1 && !this.customerTableData.length){
                    this.$message({
                        message: '请选择客户',
                        duration: 3000,
                        showClose:true,
                        type: 'error',
                    });
                    return;
                }
                if ( this.assignType == 2 ){
                    if (!this.salesCustomerList.length){
                        this.$message({
                            message: '请选择销售',
                            duration: 3000,
                            showClose:true,
                            type: 'error',
                        });
                        return;
                    }
                    if (this.salesCustomerList[0].publicCustomerNum + this.salesCustomerList[0].belongCustomerNum == 0){
                        this.$message({
                            message: '归属销售名下无客户',
                            duration: 3000,
                            showClose:true,
                            type: 'error',
                        });
                        return;
                    }
                    
                }
                //
                if (!this.assignToSales) {
                    this.$message({
                        message: '请选择划拨销售',
                        duration: 3000,
                        showClose:true,
                        type: 'error',
                    });
                    return;
                }

                if ( this.assignType == 2 ){
                    if (this.assignToSales == this.selectedSales){
                        this.$message({
                            message: '划拨销售不能与归属销售相同',
                            duration: 3000,
                            showClose:true,
                            type: 'error',
                        });
                        return;
                    }
                }
                
                // API调用执行划拨
                let firstTraderId = this.customerTableData.length > 0 ? this.customerTableData[0].traderId : null;
                let firstTraderName = this.customerTableData.length > 0 ? this.customerTableData[0].traderName : null;
                let belongCustomerNum = this.salesCustomerList.length > 0 ? this.salesCustomerList[0].belongCustomerNum : null;

                let param = {
                    traderId: firstTraderId,
                    traderName: firstTraderName,
                    type:this.assignType,
                    userId: this.assignToSales,
                    province: this.province,
                    city: this.city,
                    regionId: this.regionId,
                    fromUserId: this.selectedSales,
                    belongNum : belongCustomerNum
                }

                this.confirmLoading = true;
                assignCustomer(param).then(res => {
                    this.confirmLoading = false;
                    if (res.data.code === 0) {
                        this.$message.success('操作成功，相关数据处理约需等待30分钟');
                        this.resetForm()
                    } else {
                        if (res.data.message != ''){
                            this.$message({
                                message: res.data.message,
                                duration: 3000,
                                showClose:true,
                                type: 'error',
                            });
                        }else{
                            this.$message.error('划拨失败');
                        }
                    }
                })
            },

            // 文件上传前检查
            beforeUpload(file) {
                const isExcel = file.type === 'application/vnd.ms-excel' ||
                    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                const isLt10M = file.size / 1024 / 1024 < 10;

                if (!isExcel) {
                    this.$message.error('上传文件只能是 Excel 格式!');
                    return false;
                }
                if (!isLt10M) {
                    this.$message.error('上传文件大小不能超过 10MB!');
                    return false;
                }
                return true;
            },

            onRemoveFile(file, fileList){
                this.fileList = fileList;
                this.uploadLoading = false;
            },

            handleUpload(){
                 this.resetForm()
            },

            // 上传成功处理
            handleUploadSuccess(response) {
                this.uploadLoading = false;
                if (response.code === 0) {
                    this.$message.success('操作成功，相关数据处理约需等待30分钟');
                    this.uploadDisabled=true;
                } else {
                    this.uploadDisabled=true;
                    if (response.message != ''){
                        this.$message({
                            message: response.message,
                            duration: 3000,
                            showClose:true,
                            type: 'error',
                        });
                    }else{
                        this.$message.error('导入失败');
                    }
                }
            },

            onPreview(file) {
                debugger
            },
            
            

            // 下载模板
            downloadTemplate() {
                window.location.href = '${pageContext.request.contextPath}/static/template/批量划拨客户归属销售.xlsx';

            },

            submitUpload() {
                this.uploadLoading = false;
                this.$refs.upload.submit();
            },
            
            resetForm() {
                this.searchLoading=false,
                this.uploadLoading=false,
                this.uploadDisabled=false,
                this.confirmLoading=false,

                // 销售相关
                this.selectedSales= '',
                this.province= '',
                this.city= '',
                this.regionId= '',
                this.cityList= [],
                this.regionIdList= [],
                this.salesCustomerList= [],

                // 客户相关
                this.selectedCustomer= '',
                this.customerList= [],
                this.customerTableData= [],

                // 公共部分
                this.assignToSales= '',
                this.selectedDept= '',
                this.fileList= []
            },
        }
    });

</script>
<style>
    .trader-assign {
        padding: 20px;
}

.trader-assign h2 {
    margin-bottom: 20px;
    font-size: 18px;
}

.assign-type {
    margin-bottom: 20px;
    font-size: 14px;
}

.required {
    color: #f56c6c;
    margin-right: 4px;
}

.search-area {
    margin-bottom: 20px;
}

.select-sales,
.select-region,
.search-customer {
    margin-bottom: 15px;
    font-size: 14px;
}

.el-select {
    margin-right: 10px;
    width: 200px;
}

.assign-bottom {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #EBEEF5;
}

.assign-to {
    margin-bottom: 15px;
    font-size: 14px;
}

.upload-tips {
    margin-top: 25px;
    color: #666;
    font-size: 14px;
}

.upload-tips p {
    margin: 5px 0;
}

.sales-dept {
    color: #666;
    font-size: 14px;
}

.el-table {
    margin-top: 15px;
}
.assign-import {
    width: 600px;
}
</style>
