package com.newtask.saleorder;

import com.google.common.collect.Lists;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.saleorder.constant.SalesOrderTypeEnum;
import com.vedeng.erp.saleorder.dao.VJdSaleorderMapper;
import com.vedeng.erp.saleorder.model.po.VJdSaleorder;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.erp.saleorder.service.SaleOrderServiceFactory;
import com.vedeng.goods.mapper.CoreSkuMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.service.LogicalSaleorderChooseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description 京东订单转换为Erp订单
 * <AUTHOR>
 */
@Component
@JobHandler(value = "jdOrdersPayTask")
public class JdOrdersPayTask extends AbstractJobHandler {

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private VJdSaleorderMapper vJdSaleorderMapper;

    @Autowired
    private UserService userService;

    @Value("${jd_order_trader_phone}")
    private String PHONE;

    public static final Logger logger = LoggerFactory.getLogger(JdOrdersPayTask.class);
    @Autowired
    private SaleOrderServiceFactory saleOrderServiceFactory;
    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseServiceImpl;
    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private TraderCustomerService traderCustomerService;
    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("===================京东订单转换为Erp订单任务开始===================");
        List<VJdSaleorder> jdSaleorderList = vJdSaleorderMapper.findByOrderGenerateStatusInAndIsDeleteFalseOrderByAddTime(Lists.newArrayList(5));
        if (CollectionUtils.isEmpty(jdSaleorderList)) {
            XxlJobLogger.log("本次任务查询出的待生成京东订单数量为0");
            return ReturnT.SUCCESS;
        }
        User belongUser = userService.getBDUserInfoByMobile(PHONE);
        if (belongUser == null) {
            XxlJobLogger.log("找不到手机号为：{}对应的注册用户信息", PHONE);
            return ReturnT.FAIL;
        }
        for (int i=0;i<jdSaleorderList.size();i++){
            logger.info("开始处理京东订单的支付：{}",jdSaleorderList.get(i));
            if(StringUtils.isBlank(jdSaleorderList.get(i).getSaleorderNo())){
                XxlJobLogger.log("订单号为空{}",jdSaleorderList.get(i));
                return ReturnT.FAIL;
            }
            Saleorder current=saleorderMapper.getSaleorderByOrderNo(jdSaleorderList.get(i).getSaleorderNo());
            if(current==null){
                logger.error( "找不到订单，订单号{}",jdSaleorderList.get(i));
                return ReturnT.FAIL;
            }
            Integer verifyStatus=vJdSaleorderMapper.getVerifyStatusByOrderId(current.getSaleorderId());
            if(verifyStatus==null||verifyStatus!=1){
                logger.error( " 订单未审核通过，订单号{}",jdSaleorderList.get(i));
                XxlJobLogger.log("订单未审核通过{}",jdSaleorderList.get(i));
                VJdSaleorder successJdSaleorder = new VJdSaleorder();
                successJdSaleorder.setSaleorderId(jdSaleorderList.get(i).getSaleorderId());
                successJdSaleorder.setErrorReason("订单未审核通过");
                successJdSaleorder.setModTime(new Date());
                vJdSaleorderMapper.updateByPrimaryKeySelective(successJdSaleorder);
                 continue;
            }
            Saleorder saleorderDb = saleorderMapper.getSaleOrderById(current.getSaleorderId());
            ResultInfo checkResult = traderCustomerService.checkBillPeriodForOrder(saleorderDb, saleorderMapper.getCustomerIdByOrderId(saleorderDb.getSaleorderId()).intValue());
            if (!checkResult.getCode().equals(0)) {
                logger.error("京东账期不足，停止自动支付，请申请账期。");
                VJdSaleorder successJdSaleorder = new VJdSaleorder();
                successJdSaleorder.setSaleorderId(jdSaleorderList.get(i).getSaleorderId());
                successJdSaleorder.setErrorReason("账期不足");
                successJdSaleorder.setModTime(new Date());
                vJdSaleorderMapper.updateByPrimaryKeySelective(successJdSaleorder);
                return ReturnT.FAIL;
            }
            //对于京东订单模拟 点击信用支付
            if (saleorderDb.getValidStatus() == 1 && saleorderDb.getPaymentStatus() == 0 && saleorderDb.getPaymentType() == 423){
                BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
                try {
                    User  currentUser=userService.getBDUserInfoByMobile(PHONE);
                    if(currentUser==null){
                        currentUser=new User();
                        currentUser.setUserId(2);
                        currentUser.setUsername("njadmin");
                    }
                    currentUser.setCompanyId(1);
                    currentUser.setCompanyName("南京贝登医疗股份有限公司");

                    logger.info("用户：{}对订单：{},发起信用支付",currentUser.getUsername(),saleorderDb.getSaleorderId());
                    saleOrderService.addCapitalBillByCustomerBillPeriod(saleorderDb.getSaleorderId(),currentUser,null);
                    logger.info("用户：{}对订单：{},发起信用支付完成",currentUser.getUsername(),saleorderDb.getSaleorderId());
                    logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorderDb,currentUser);
                    //调用库存服务
                    // warehouseStockService.updateOccupyStockService(saleorder, 0);
                    logger.info("用户：{}对订单：{},下发WMS完成",currentUser.getUsername(),saleorderDb.getSaleorderId());

                    VJdSaleorder successJdSaleorder = new VJdSaleorder();
                    successJdSaleorder.setSaleorderId(jdSaleorderList.get(i).getSaleorderId());
                    successJdSaleorder.setOrderGenerateStatus(Constants.TWO);
                    successJdSaleorder.setErrorReason("订单处理成功");
                    successJdSaleorder.setModTime(new Date());
                     vJdSaleorderMapper.updateByPrimaryKeySelective(successJdSaleorder);
                } catch (Exception e) {
                    logger.error("订单：{},100%账期支付后，下发wms异常：",saleorderDb.getSaleorderId(),e);
                }
            }
        }
        return ReturnT.SUCCESS;
    }

}
