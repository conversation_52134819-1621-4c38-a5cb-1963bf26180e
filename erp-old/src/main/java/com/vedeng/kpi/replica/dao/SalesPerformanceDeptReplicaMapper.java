package com.vedeng.kpi.replica.dao;

import com.vedeng.saleperformance.model.SalesPerformanceDept;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

/**
 * <AUTHOR>
 * @date created in 2020/6/18 10:59
 */
@Named("salesPerformanceDeptReplicaMapper")
public interface SalesPerformanceDeptReplicaMapper {

    SalesPerformanceDept getDeptByUserId(@Param("userId") Integer userId);

}
