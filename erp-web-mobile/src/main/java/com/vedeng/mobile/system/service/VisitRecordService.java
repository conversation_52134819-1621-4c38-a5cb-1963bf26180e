package com.vedeng.mobile.system.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.Page;
import com.vedeng.erp.trader.dto.VisitDeleteDto;
import com.vedeng.erp.system.common.enums.UserOrgEnums;
import com.vedeng.erp.trader.dto.VisitInputDto;
import com.vedeng.erp.trader.dto.VisitSearchDto;
import com.vedeng.mobile.system.dto.*;
import com.vedeng.mobile.system.model.VisitRecord;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
public interface VisitRecordService {

    /**
     * 查询拜访计划列表
     * @param visitSearchDto
     * @param page
     * @return
     */
    PageInfo<VisitInputDto> searchVisitRecordListPage(VisitSearchDto visitSearchDto, Page page);

    /**
     * 查询拜访计划详情
     * @param id 拜访计划的ID
     * @return
     */
    VisitInputDto queryVisitRecordById(Integer id);

    /**
     * 创建拜访计划
     * @param visitInputDto
     * @return
     */
    VisitInputDto insertVisitRecord(VisitInputDto visitInputDto);

    /**
     * 判断当前人员是否可以创建拜访计划
     * 如果返回了上一次的计划，则认为不可创建
     * @param inputDto
     * @return
     */
    VisitRecord checkTraderCanCreate(TraderCustomerInputDto inputDto);

    VisitRecord queryLastVisitRecord(String customerName);


    /**
     * 编辑拜访计划
     * @param visitInputDto
     * @return
     */
    VisitInputDto editVisitRecord(VisitInputDto visitInputDto);

    /**
     * 打卡
     * @param visitCardInputDto
     * @return
     */
    VisitInputDto saveVisitCard(VisitCardInputDto visitCardInputDto);

    /**
     *
     * @param visitContentInputDto
     * @return
     */
    VisitInputDto saveVisitContent(VisitContentInputDto visitContentInputDto);

    /**
     * 编辑拜访内容
     * @param visitContentInputDto
     * @return
     */
    VisitInputDto editVisitContent(VisitContentInputDto visitContentInputDto);

    /**
     * 删除拜访记录
     * @param visitDeleteDto
     * @return
     */
    boolean deleteVisitRecord(VisitDeleteDto visitDeleteDto);


    /**
     * 是否可点击客户详情
     *
     * @param userOrgEnums
     * @param record
     * @param userId
     * @return
     */
    boolean canCheckTraderDetail(UserOrgEnums userOrgEnums, VisitInputDto record, Integer userId, List<Integer> userIds);
}
