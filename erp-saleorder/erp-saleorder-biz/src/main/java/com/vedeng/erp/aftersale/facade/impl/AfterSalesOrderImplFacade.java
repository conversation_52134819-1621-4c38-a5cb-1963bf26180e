package com.vedeng.erp.aftersale.facade.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ServletUtils;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity;
import com.vedeng.erp.aftersale.dto.CreateRefundApplyRequest;
import com.vedeng.erp.aftersale.facade.AfterSalesOrderFacade;
import com.vedeng.erp.aftersale.mapper.AfterSalesBizMapper;
import com.vedeng.erp.aftersale.mapper.AfterSalesDetailMapper;
import com.vedeng.erp.aftersale.service.AfterSalesCommonService;
import com.vedeng.erp.finance.api.CustomerAccountApiService;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.dto.PayApplyDetailDto;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.finance.dto.RPayApplyJBankReceiptDto;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.finance.service.PayApplyAutoPayApi;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.finance.constant.PayApplyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售售后订单相关
 * @date 2024/9/2 9:58
 */
@Slf4j
@Service
public class AfterSalesOrderImplFacade implements AfterSalesOrderFacade {

    @Autowired
    CustomerAccountApiService customerAccountApiService;
    @Autowired
    PayApplyApiService payApplyApiService;
    @Autowired
    AfterSalesService afterSalesService;
    @Autowired
    PayApplyAutoPayApi payApplyAutoPayApi;
    @Autowired
    AfterSalesCommonService afterSalesCommonService;
    @Autowired
    AfterSalesDetailMapper afterSalesDetailMapper;
    @Autowired
    AfterSalesBizMapper afterSalesBizMapper;
    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void createRefundApply(CreateRefundApplyRequest createRefundApplyRequest) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        createRefundApplyRequest.setApplyUserId(currentUser.getId());
        createRefundApplyRequest.setApplyUserName(currentUser.getUsername());
        // 校验是否存在进行中的售后退款申请
        PayApplyCreateBillDto queryDto = new PayApplyCreateBillDto();
        queryDto.setRelatedId(createRefundApplyRequest.getAfterSalesId());
        queryDto.setPayType(PayApplyTypeEnum.AFTER_SALES.getCode());
        queryDto.setValidStatus(ErpConstant.F);
        queryDto.setIsBill(ErpConstant.F);
        queryDto.setPayStatus(ErpConstant.F);
        List<PayApplyCreateBillDto> payApplyByDto = payApplyApiService.getPayApplyByDto(queryDto);
        if (CollUtil.isNotEmpty(payApplyByDto)) {
            throw new ServiceException("该售后订单已存在进行中的售后退款申请");
        }


        log.info("创建售后退款申请:{}", createRefundApplyRequest);
        // 新增付款申请
        PayApplyDto payApplyDto = PayApplyDto.builder()
                .amount(createRefundApplyRequest.getPayAmount())
                .bank(createRefundApplyRequest.getBank())
                .bankCode(createRefundApplyRequest.getBankCode())
                .bankAccount(createRefundApplyRequest.getBankAccount())
                .payType(PayApplyTypeEnum.AFTER_SALES.getCode())
                .validStatus(ErpConstant.F)
                .relatedId(createRefundApplyRequest.getAfterSalesId())
                .traderSubject(createRefundApplyRequest.getTraderSubject())
                .traderMode(createRefundApplyRequest.getTraderMode())
                .traderName(createRefundApplyRequest.getTraderName())
                .companyId(1)
                .currencyUnitId(1)
                .isBill(ErpConstant.F)
                .payStatus(ErpConstant.F)
                .creator(createRefundApplyRequest.getApplyUserId())
                .creatorName(createRefundApplyRequest.getApplyUserName())
                .addTime(System.currentTimeMillis())
                .comments(createRefundApplyRequest.getComment())
                .build();
        log.info("创建售后退款申请:{}", payApplyDto);

        List<PayApplyDetailDto> payApplyDetailDtos = new ArrayList<>();
        PayApplyDetailDto build = PayApplyDetailDto.builder().totalAmount(createRefundApplyRequest.getPayAmount()).build();
        payApplyDetailDtos.add(build);
        payApplyDto.setPayApplyDetailDtos(payApplyDetailDtos);
        payApplyApiService.addPayApply(payApplyDto);

        // 更新往来单位类型
        payApplyApiService.updateAccountType(payApplyDto.getPayApplyId());

        // 调用自动制单公共校验接口，根据结果更新AUTO_BILL字段
        try {
            List<PayApplyCreateBillDto> payApplyCreateBillDtoList = payApplyAutoPayApi.findPayApply(payApplyDto.getPayApplyId());
            if (CollUtil.isNotEmpty(payApplyCreateBillDtoList)) {
                payApplyAutoPayApi.createBillRuleCheck(payApplyCreateBillDtoList.get(0));
                payApplyApiService.updateAutoBill(payApplyDto.getPayApplyId(), ErpConst.ONE);
            }
        } catch (ServiceException e) {
            log.info("售后单调用自动制单公共校验接口不满足自动制单:{}", e.getMessage());
        } catch (Exception e) {
            log.info("售后单调用自动制单公共校验接口异常", e);
        }

        // 发起审核流
        try {
            afterSalesService.saveApplyApprovalProcess(createRefundApplyRequest,
                    payApplyDto.getPayApplyId(),
                    currentUser.getOrgId(),
                    ServletUtils.getRequest());
        } catch (Exception e) {
            throw new ServiceException("创建售后退款申请失败");
        }

        // 建立付款申请和回单关系
        if (createRefundApplyRequest.getBankReceiptAliasId() != null) {
            RPayApplyJBankReceiptDto rPayApplyJBankReceiptDto = new RPayApplyJBankReceiptDto();
            rPayApplyJBankReceiptDto.setBankId(createRefundApplyRequest.getBankId());
            rPayApplyJBankReceiptDto.setPayApplyId(payApplyDto.getPayApplyId());
            rPayApplyJBankReceiptDto.setBankReceiptAliasId(createRefundApplyRequest.getBankReceiptAliasId());
            log.info("建立售后退款申请和回单关系:{}", rPayApplyJBankReceiptDto);
            customerAccountApiService.createPayApplyRelation(rPayApplyJBankReceiptDto);
        }
    }

    @Override
    public boolean isRefundableToOrigin(Integer afterSalesId) {
        // 获取售后订单信息
        AfterSalesEntity afterSales = afterSalesBizMapper.selectByPrimaryKey(afterSalesId);
        if (afterSales == null) {
            return false;
        }

        // 获取售后订单详情
        AfterSalesDetailEntity afterSalesDetail = afterSalesDetailMapper.findByAfterSalesId(afterSalesId);
        if (afterSalesDetail == null) {
            return false;
        }

        // 获取销售订单信息
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getByAfterSaleId(afterSalesId);
        if (saleorderInfoDto == null) {
            return false;
        }

        // 耗材来源
        final int sourceConsumable = 1;
        // 退货类型
        final int typeReturn = 539;
        // 线上支付方式
        final int paymentModeOnline = 0;
        // 支付宝支付类型
        final int paymentTypeAlipay = 1;
        // 微信支付类型
        final int paymentTypeWechat = 2;
        // 退还账户方式
        final int refundMethodAccount = 2;

        // 判断是否满足每个条件
        boolean isSourceConsumableOrMainFood = afterSales.getSource().equals(sourceConsumable);
        boolean isTypeReturn = afterSales.getType().equals(typeReturn);
        boolean isPaymentModeOnline = saleorderInfoDto.getPaymentMode().equals(paymentModeOnline);
        boolean isPaymentTypeAlipayOrWechat = saleorderInfoDto.getPayType().equals(paymentTypeAlipay) ||
                saleorderInfoDto.getPayType().equals(paymentTypeWechat);
        boolean isRefundMethodAccount = afterSalesDetail.getRefund().equals(refundMethodAccount);

        // 判断是否满足所有条件
        return isSourceConsumableOrMainFood &&
                isTypeReturn &&
                isPaymentModeOnline &&
                isPaymentTypeAlipayOrWechat &&
                isRefundMethodAccount;
    }
}
