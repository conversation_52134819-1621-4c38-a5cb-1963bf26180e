package com.vedeng.erp.system.domain.entity;

import lombok.Data;

/**
 * @description 标签库
 * <AUTHOR>
 * @date 2022/7/21 12:11
 **/
@Data
public class TagEntity {
    private Integer tagId;

    /**
    * ERP公司ID(T_COMPANY)
    */
    private Integer companyId;

    /**
    * 类别 SYS_OPTION_DEFINITION_ID
    */
    private Integer tagType;

    /**
    * 是否推荐0否1是
    */
    private Boolean isRecommend;

    /**
    * 标签名称
    */
    private String tagName;

    /**
    * 备注
    */
    private String comments;
}