package com.vedeng.erp.buyorder.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description 采购费用单详细
 * <AUTHOR>
 * @date 2022/10/17 15:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuyorderExpenseDetailDto extends BaseDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    private Integer buyorderExpenseDetailId;

    /**
    * 采购费用单ID
    */
    private Integer buyorderExpenseId;

    /**
    * 付款方式 字典表 
    */
    private Integer paymentType;

    /**
    * 付款备注
    */
    private String paymentComments;

    /**
    * 发票类型 字典表 
    */
    private Integer invoiceType;

    /**
     * @组合对象@ 发票类型
     */
    private String invoiceTypeStr;

    /**
    * 开票备注
    */
    private String invoiceComments;

    /**
    * 采购费用单应付总额
    */
    private BigDecimal totalAmount;

    /**
    * 供应商id
    */
    private Integer traderId;

    /**
    * 供应商名称
    */
    private String traderName;

    /**
     * 交易次数
     */
    private Integer orderCount;

    /**
     * 交易金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 上次交易时间
     */
    private Long lastOrderTime;

    /**
    * 供应商联系人ID
    */
    private Integer traderContactId;

    /**
    * 供应商联系人
    */
    private String traderContactName;

    /**
    * 供应商手机
    */
    private String traderContactMobile;

    /**
    * 供应商电话
    */
    private String traderContactTelephone;

    /**
    * 供应商联系地址ID
    */
    private Integer traderAddressId;

    /**
    * 供应商供应商地区
    */
    private String traderArea;

    /**
    * 供应商联系详细地址(含省市区)
    */
    private String traderAddress;

    /**
    * 供应商备注
    */
    private String traderComments;

    /**
    * 预付金额
    */
    private BigDecimal prepaidAmount;

    /**
    * 账期支付金额
    */
    private BigDecimal accountPeriodAmount;

    /**
    * 账期天数
    */
    private Integer periodDay;

    /**
    * 尾款
    */
    private BigDecimal retainageAmount;

    /**
    * 尾款期限(月)
    */
    private Integer retainageAmountMonth;

    /**
    * 费用来源
    */
    private String expenseSource;

    /**
    * 添加人部门ID
    */
    private Integer orgId;

    /**
     * 部门名称
     */
    private String orgName;

    /**
    * 采购费用订单合同url
    */
    private String contractUrl;

    /**
    * 是否删除0否1是
    */
    private Integer isDelete;

    /**
     * 供应商等级
     */
    private Integer traderGrade;

    private List<OrderRemarkDto> orderDesc;
}