package com.newtask.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.system.domain.dto.BankDto;
import com.vedeng.erp.system.domain.entity.BankEntity;
import com.vedeng.erp.system.mapper.BankMapper;
import com.vedeng.erp.system.mapstruct.BankConvertor;
import com.vedeng.finance.dto.BankNoDto;
import com.vedeng.finance.service.BankBillService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 定时任务拉取中国银行银联号信息
 * @date 2022/8/15 13:29
 */
@JobHandler(value = "GetChinaBanKNoTask")
@Component
@Slf4j
public class getChinaBanKNoTask extends AbstractJobHandler {


    @Autowired
    private BankBillService bankBillService;

    @Autowired
    private BankMapper bankMapper;
    @Autowired
    private BankConvertor bankConvertor;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        ResultInfo<List<BankNoDto>> restfulResult;
        try {
            restfulResult = bankBillService.getChinaBanKNo();
            log.info("调用银联号信息接口，返回信息[{}],code[{}]", restfulResult.getMessage(),restfulResult.getCode());
            if (restfulResult.getCode() == -1) {
                XxlJobLogger.log("同步银联号信息,接口调用失败");
                log.error("调用银联号信息接口，调用异常[{}]", restfulResult.getMessage());
                return FAIL;
            }
        } catch (Exception e) {
            XxlJobLogger.log("同步银联号信息,接口调用失败");
            log.error("调用银联号信息接口，异常信息", e);
            return FAIL;
        }

        List<BankNoDto> bankNoDtoList = restfulResult.getData();

        List<BankEntity> addBank = new ArrayList<>();
        List<BankEntity> updateBank = new ArrayList<>();
        if (CollUtil.isNotEmpty(bankNoDtoList)) {

            for (BankNoDto bankNoDto : bankNoDtoList) {
                if (StrUtil.isBlank(bankNoDto.getBankName()) && StrUtil.isBlank(bankNoDto.getBankNo())) {
                    XxlJobLogger.log("同步银联号信息,银行名称或银联号为空");
                    break;
                }

                BankEntity bankEntityOld = bankMapper.findByBankNoAndIsDel(bankNoDto.getBankNo(), ErpConstant.F);
                if (bankEntityOld == null) {
                    BankDto bankDto = new BankDto(bankNoDto.getBankNo(), bankNoDto.getBankName());
                    bankDto.systemAdd();
                    addBank.add(bankConvertor.toEntity(bankDto));
                    XxlJobLogger.log("新增一条银联号信息,银行名称：{},银联号：{}", bankDto.getBankName(), bankDto.getBankId());
                } else {
                    // 人工添加时，将人工置为不可用状态，同时新增一条银行添加信息
                    if (bankEntityOld.getSource() == 1) {
                        bankEntityOld.setDisableFlag(ErpConstant.T);
                        BankDto bankDto = new BankDto(bankNoDto.getBankNo(), bankNoDto.getBankName());
                        bankDto.systemAdd();
                        addBank.add(bankConvertor.toEntity(bankDto));
                    } else {
                        bankEntityOld.setBankNo(bankNoDto.getBankNo());
                        bankEntityOld.setBankName(bankNoDto.getBankName());
                    }

                    updateBank.add(bankEntityOld);
                    XxlJobLogger.log("修改一条银联号信息,银行名称：{},银联号：{}", bankEntityOld.getBankName(), bankEntityOld.getBankId());
                }
            }

        }
        XxlJobLogger.log("共同步新增[{}]条银联号信息", addBank.size());
        XxlJobLogger.log("共同步修改[{}]条银联号信息", updateBank.size());
        if (CollUtil.isNotEmpty(addBank)) {
            bankMapper.batchInsert(addBank);
        }
        if (CollUtil.isNotEmpty(updateBank)) {
            bankMapper.updateBatchSelective(updateBank);
        }
        return SUCCESS;
    }


}
