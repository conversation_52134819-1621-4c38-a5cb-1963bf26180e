package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleOutStockCommand;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleOutStockCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleOutStockConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleOutStockMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售出库单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeSaleOutStock {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private KingDeeSaleOutStockCommandConvertor commandConvertor;

    @Resource
    private KingDeeSaleOutStockConvertor purchaseReceiptConvertor;

    @Autowired
    private KingDeeSaleOutStockMapper kingDeeSaleOutStockMapper;


    @Test
    public void kingDeePurchaseReceipt() {
        KingDeeSaleOutStockDto dto = new KingDeeSaleOutStockDto();
        dto.setFid("0");
        dto.setFBillTypeID("XSCKD01_SYS");
        dto.setFBillNo("CK2212111000010");
        dto.setF_qzok_bddjtid("12");
        dto.setF_qzok_gsbm("归属部门");
        dto.setFDate("2023-01-12");
        dto.setFSaleOrgId("101");
        dto.setFStockOrgId("101");
        dto.setFCustomerID("18418");


        List<KingDeeSaleOutStockDetailDto>  fEntity = new ArrayList<>();
        KingDeeSaleOutStockDetailDto detailDto = new KingDeeSaleOutStockDetailDto();
        detailDto.setFMaterialID("SKU0001");
        detailDto.setFRealQty(new BigDecimal("100"));
        detailDto.setFIsFree(false);
        detailDto.setFTaxPrice(new BigDecimal("113"));
        detailDto.setFEntryTaxRate(new BigDecimal("13.00"));
        detailDto.setFStockID("ck9999");
        detailDto.setF_QZOK_SFZF("否");
        detailDto.setF_QZOK_YSDDH("BOF019472322121190924");
        detailDto.setF_QZOK_GSYWDH("归属业务单号");
        detailDto.setF_QZOK_YWLX("销售订单");
        detailDto.setF_QZOK_PCH("LET-F");
        detailDto.setF_QZOK_XLH("SN");
        detailDto.setF_QZOK_SFAT2("否");
        detailDto.setF_QZOK_BDDJHID("6");


        fEntity.add(detailDto);

        dto.setFEntity(fEntity);

        KingDeeSaleOutStockCommand command = commandConvertor.toCommand(dto);

        ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("FEntity.FEntryID");
        needReturnFields.add("FEntity.F_QZOK_BDDJHID");
        needReturnFields.add("FEntity.FRealQty");

        System.out.println("入参："+JSON.toJSONString(command));
         kingDeeBaseApi.save(new SaveExtCommand<>(command, "SAL_OUTSTOCK", needReturnFields));

    }
}
