package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeSalesVatPlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeSupplierCommand;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDetail;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDetailLinkDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.mapstruct
 * @Date 2023/1/9 16:01
 */
@Mapper(componentModel = "spring")
public interface KingDeeSalesVatPlainInvoiceCommandConvertor extends BaseCommandMapStruct<KingDeeSalesVatPlainInvoiceCommand, KingDeeSalesVatPlainInvoiceDto> {

    /**
     * dto转化为金蝶进口入参
     *
     * @param dto KingDeeSalesVatPlainInvoiceDto
     * @return KingDeeSalesVatPlainInvoiceCommand4
     */
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "fdate", source = "fdate")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "finvoiceno", source = "finvoiceno")
    @Mapping(target = "finvoicedate", source = "finvoicedate")
    @Mapping(target = "f_QZOK_FPDM", source = "FQzokFpdm")
    @Mapping(target = "fbillingway", source = "fbillingway")
    @Mapping(target = "fcustomerid.FNumber", source = "fcustomerid")
    @Mapping(target = "fdocumentstatus", source = "fdocumentstatus")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeID")
    @Mapping(target = "fsettleorgid.FNumber", source = "fsettleorgid")
    @Mapping(target = "FCancelStatus", source = "FCancelStatus")
    @Mapping(target = "FRedBlue", source = "FRedBlue")
    @Mapping(target = "f_QZOK_PZGSYWDH",source = "FQzokPzgsywdh")
    @Override
    KingDeeSalesVatPlainInvoiceCommand toCommand(KingDeeSalesVatPlainInvoiceDto dto);

    /**
     * 内部子dto转化
     *
     * @param dto KingDeeSalesVatPlainInvoiceDetail
     * @return KingDeeSalesVatPlainInvoiceCommand.FSALESICENTRY
     */
    @Mapping(target = "fmaterialid.FNumber", source = "fmaterialid")
    @Mapping(target = "fpriceqty", source = "fpriceqty")
    @Mapping(target = "fauxtaxprice", source = "fauxtaxprice")
    @Mapping(target = "ftaxrate", source = "ftaxrate")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "fsrcbilltypeid", source = "fsrcbilltypeid")
    @Mapping(target = "FSALESICENTRY_Link", source = "fsalesicentryLink")
    KingDeeSalesVatPlainInvoiceCommand.FSALESICENTRY toCommand(KingDeeSalesVatPlainInvoiceDetail dto);

    /**
     * 内部子dto转化
     *
     * @param dto KingDeeSalesVatPlainInvoiceDetailLinkDto
     * @return KingDeeSalesVatPlainInvoiceCommand.FSALESICENTRY.FSALESICENTRYLink
     */
    @Mapping(target = "FLinkId", source = "FLinkId")
    @Mapping(target = "FSALESICENTRY_Link_FFlowId", source = "fsalesicentryLinkFflowid")
    @Mapping(target = "FSALESICENTRY_Link_FFlowLineId", source = "fsalesicentryLinkFflowlineid")
    @Mapping(target = "FSALESICENTRY_Link_FRuleId", source = "fsalesicentryLinkFruleid")
    @Mapping(target = "FSALESICENTRY_Link_FSTableId", source = "fsalesicentryLinkFstableid")
    @Mapping(target = "FSALESICENTRY_Link_FSTableName", source = "fsalesicentryLinkFstablename")
    @Mapping(target = "FSALESICENTRY_Link_FSBillId", source = "fsalesicentryLinkFsbillid")
    @Mapping(target = "FSALESICENTRY_Link_FSId", source = "fsalesicentryLinkFsid")
    @Mapping(target = "FSALESICENTRY_Link_FBASICUNITQTYOld", source = "fsalesicentryLinkFbasicunitqtyold")
    @Mapping(target = "FSALESICENTRY_Link_FBASICUNITQTY", source = "fsalesicentryLinkFbasicunitqty")
    @Mapping(target = "FSALESICENTRY_Link_FALLAMOUNTFOROld", source = "fsalesicentryLinkFallamountforold")
    @Mapping(target = "FSALESICENTRY_Link_FALLAMOUNTFOR", source = "fsalesicentryLinkFallamountfor")
    KingDeeSalesVatPlainInvoiceCommand.FSALESICENTRY.FSALESICENTRYLink toCommand(KingDeeSalesVatPlainInvoiceDetailLinkDto dto);
}
