package com.wms.service.stockcalculate;

import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dto.StockCalculateDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OutputOrderAuditPassCaculateImpl.java
 * @Description TODO 出库单审核通过或全部付款 库存占用计算
 * @createTime 2020年08月04日 16:07:00
 */
@Service
public class OutputOrderAuditPassCaculateImpl extends AbstractStockinfoCaculateInterace{

    @Override
    protected void calculateStockNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {

    }

    @Override
    protected void calculateOcupyNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {
        /*details.stream().forEach(stockCalculateDto->{

            WarehouseDto warehouseStock = super.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                    stockCalculateDto.getSku(),
                    stockCalculateDto.getLogicalWarehouseId());

            //增加占用库存数量
            warehouseStock.setOccupyNum(stockCalculateDto.getOccupyNum());
        });*/

        super.calculateStockOrOccupyNumBase(details, warehouseStockList, false,false);
    }
}
