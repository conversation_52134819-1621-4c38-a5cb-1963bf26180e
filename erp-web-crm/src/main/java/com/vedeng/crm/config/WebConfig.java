package com.vedeng.crm.config;

import com.vedeng.core.trace.interceptor.MdcInterceptor;
import com.vedeng.security.SecurityConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/20
 */
@Configuration
public class WebConfig implements WebMvcConfigurer, Ordered {

    @Value("${erp_url}")
    private String erpHost;

    @Value("${company_config}")
    private String companyConfig;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        List<String> excludePathPatterns = Arrays.asList("/crm/*/m/**","/static/**", "/favicon.ico", "/api/**", "/error", "/checkpreload.html", "/ezadmin/anon/**", "/sso/anon/**", "/webjars/**", "/ezlist/**");
//        excludePathPatterns.addAll(SecurityConstant.DEFAULT_ALLOWED_PATHS);
        registry.addInterceptor(new ShiroSessionInterceptor()).addPathPatterns("/**")
                .excludePathPatterns(SecurityConstant.DEFAULT_ALLOWED_PATHS);
        registry.addInterceptor(new MdcInterceptor())
                .excludePathPatterns("/**/static/**/*.*");
        registry.addInterceptor(new PageAllInterceptor(erpHost,companyConfig))
                .addPathPatterns("/**");
        registry.addInterceptor(new ProfileInterceptor())
                .addPathPatterns("/crm/*/profile/**");
        registry.addInterceptor(new CrmMsiteSessionInterceptor())
                .addPathPatterns("/crm/*/m/**");


    }

    @Override
    public int getOrder() {
        // 设置最高优先级
        return Ordered.HIGHEST_PRECEDENCE ;
    }
}
