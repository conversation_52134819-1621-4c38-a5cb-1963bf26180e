.visit-list-item {
    background: #fff;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 5px;

    &:last-child {
        margin-bottom: 0;
    }

    .visit-title {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        .title-icon {
            width: 20px;
            height: 20px;
            background-size: 100% 100%;
            margin-right: 5px;

            @level: S, S1, S2, A1, A2, A3, B1, B2, B3, C1, C2, C3, D;

            each(@level, {
                &.lv-@{value} {
                    background-image: url('/mstatic/image/traderLevel/@{value}.svg');
                }
            })
        }

        .title-txt {
            flex: 1;
            font-size: 14px;
            font-weight: 700;
        }

        .title-status {
            line-height: 22px;
            padding: 0 5px;
            border-radius: 3px;

            &.status-1 {
                background: #FFEDE0;
                color: #f60;
            }

            &.status-2 {
                background: #E0F3FF;
                color: #09f;
            }

            &.status-3 {
                background: #E3F7E3;
                color: #13BF13;
            }

            &.status-4 {
                background: #E3EAF0;
                color: #1A4D80;
            }
        }
    }

    .visit-tip {
        display: flex;
        color: #999;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10px;
        border-bottom: solid 1px #F5F7FA;
    }

    .visit-info {
        padding-top: 10px;

        .info-item {
            display: flex;
            margin-bottom: 5px;

            &:last-child {
                margin-bottom: 0;
            }

            .info-label {
                width: 60px;
                color: #999;
            }

            .info-txt {
                flex: 1;
                word-break: break-all;
            }
        }
    }
}

.card-address-btn {
    display: flex;
    align-items: center;
    color: #09f;

    .icon-address {
        font-size: 16px;
        margin-right: 5px;
    }
}

.slide-panel {
    max-height: calc(100vh - 45px - 53px);
    
    .slide-wrap {
        max-height: calc(100vh - 45px - 53px - 53px);
        
        &.form {
            padding: 10px;
            background: #F5F7FA;
        }
    }
}



/* 新建拜访 */
.business-page-wrap {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.businessNo-content {
    padding-right: 10px;

    &.pb {
        padding-bottom: 10px;
    }
}

.other-contact-list {
    .other-contact-item-wrap {
        border-bottom: solid 1px #F5F7FA;

        .other-contact-item {
            display: flex;
            align-items: center;
        
            .contact-input {
                flex: 1;
                min-width: 0;
            }

            .contact-del {
                width: 38px;
                height: 38px;
                display: flex;
                justify-content: center;
                align-items: center;
        
                .icon-delete {
                    font-size: 16px;
                    color: #666;
                }
            }
        }
    }
}

.address-detail {
    position: relative;
    padding: 0 0 10px;

    .ad-tip {
        margin-top: 5px;
        padding: 0 10px;
    }

    .count {
        position: absolute;
        top: -23px;
        right: 10px;
        font-size: 12px;
        color: #999;
    }
}

.textarea-count {
    position: absolute;
    top: -23px;
    right: 10px;
    font-size: 12px;
    color: #999;
}

.businessNo-check {
    padding-left: 10px;

    .info-item {
        font-size: 12px;
        color: #666;
    }

    /deep/ .form-tip {
        margin-top: 10px;
    }
}

.featurn-box {
    height: 53px;
    display: flex;
    align-items: center;
    padding-right: 10px;

    &.single {
        padding: 10px;
    }

    .more {
        width: 53px;
        height: 53px;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        
        .icon1 {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            border: solid 2px #666;
            font-size: 16px;
            color: #666;
        }
    }
}


/* 拜访详情 */
.business-detail-container {
    padding-top: 40px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .detail-page-wrap {
        position: relative;
        z-index: 2;
        padding-bottom: 53px;
    } 
    .operation-pagr-wrap {
        position: relative;
        z-index: 2;
        height: calc(100vh - 40px);
    }
    .page-top-bg {
        height: 121px;
        background: linear-gradient(to bottom, #ddeefa 0%, #f5f7fa 100%);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1;
    
        &.finish {
            background: linear-gradient(to bottom, #e0f2e4 0%, #f5f7fa 100%);
        }
        
        &.close {
            background: linear-gradient(to bottom, #E3EAF0 0%, #f5f7fa 100%);
        }
    
        &.wait {
            background: linear-gradient(to bottom, #ffede1 0%, #f5f7fa 100%);
        }
    }
    .page-nav {
        width: 100vw;
        height: 40px;
        overflow: hidden;
        display: flex;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9;

        .nav-item {
            flex: 1;
            min-width: 0;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
    
            .nav {
                position: relative;
                font-size: 14px;
                font-weight: 400;
                color: #000;
                line-height: 40px;
    
                &.active {
                    color: #09f;
                    font-weight: 700;
    
                    &::after {
                        content: "";
                        border-bottom: solid 3px #09f;
                        width: 100%;
                        position: absolute;
                        bottom: 0;
                        left: 0;
                    }
                }
            }
    
        }

        &::after {
            content: "";
            height: 121px;
            background: linear-gradient(to bottom, #ddeefa 0%, #f5f7fa 100%);
            position: absolute;
            top: 0;
            left: 0;
            right: 0;      
            z-index: -1;
        }

        &.finish::after {
            background: linear-gradient(to bottom, #e0f2e4 0%, #f5f7fa 100%);
        }
        
        &.close::after {
            background: linear-gradient(to bottom, #E3EAF0 0%, #f5f7fa 100%);
        }
    
        &.wait::after {
            background: linear-gradient(to bottom, #ffede1 0%, #f5f7fa 100%);
        }
    }
}

// 页面操作按钮
.page-btns {

    .btn {
        font-size: 14px;
        color: #000;
        padding: 10px 0;
        text-align: center;
        border-bottom: solid 1px #F5F7FA;

        &:last-child {
            border-bottom: none;
        }

        &.red {
            color: #e64545;
        }
    }
}

.fixed-btn {
    position: fixed;
    bottom: 50px;
    right: 10px;
    z-index: 99;

    &.maxTop {
        bottom: 103px;
    }

    .to-list {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        overflow: hidden;
        background: #fff;
        box-shadow: 1px 6px 13px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;

        > .icon-home {
            font-size: 24px;
            color: #666;
        }
    }
}
.close-reason {
    margin: 0 10px;
    border-radius: 5px;
    overflow: hidden;
}
.visitRecord-num {
    padding-right: 44px;
}
.copy {
    font-size: 12px;
    color: #09f;
    line-height: 38px;
    width: 44px;
    height: 38px;
    text-align: center;
    display: block;
    position: absolute;
    right: 0;
    top: 0;
}
.price-light {
    color: #E64545;
}
.dedtail-chance-step {
    margin-bottom: 10px;
}
.visit-status {
    display: inline-block;
    height: 22px;
    line-height: 22px;
    padding: 0 5px;
    border-radius: 3px;
    color: #FF6600;
    background-color: rgba(255, 102, 0, 0.1);
    font-size: 12px;
    margin-top: 1px;

    &.status1 {
        background: #ffede1;
        color: #F60;
    }
    &.status2 {
        background: #e1f3ff;
        color: #09F;
    }
    &.status3 {
        background: #E3F7E3;
        color: #13BF13;
    }
    &.status4 {
        background: #E3EAF0;
        color: #1A4D80;
    }
}

.detail-trader-name {
    .form-fields {
        padding-right: 0;
    }
}

.user-label-list {
    margin-bottom: -10px;
    display: flex;
    flex-wrap: wrap;

    .user-label-wrap {
        margin-right: 20px;
        margin-bottom: 10px;
    }
}

.card-wrap {
    .form-card {
        margin-bottom: 5px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.card-pic-list {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: -5px;

    .pic {
        border-radius: 3px;
        overflow: hidden;
        margin-right: 5px;
        margin-bottom: 5px;

        &.s60 {
            width: 60px;
            height: 60px;
        }

        > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}

// 拜访记录
.communicate-list {
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
    padding: 10px;

    .communicate-item {
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }

        .c-top {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .c-user {
                flex: 1;
                min-width: 0;
            }
            .c-time {
                width: 130px;
                flex-shrink: 0;
                text-align: right;
            }
        }
        .c-content {
            // font-size: 12px;
            color: #000;
            margin-top: 5px;
        }
    }
}

.empty-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 0;
    background: #fff;
    border-radius: 5px;

    > i {
        font-size: 32px;
        color: #09F;
    }

    p {
        margin-top: 10px;
    }
}

.close-radio {
    display: flex;
    flex-wrap: wrap;

    .vd-ui-radio-item {
        flex: 0 0 50%;
        margin: 0 0 20px;

        &:nth-child(2n-1) {
            padding-right: 5px;
        }

        &:nth-child(2n) {
            padding-left: 5px;
        }
    }

}

/* 操作记录 */
.operation-records {
    height: 100%;
    padding: 10px 0 5px;
    box-sizing: border-box;
    overflow-y: auto;

    .list {
        padding: 0 10px 0 30px;

        .item {
            position: relative;
            padding: 10px;
            background: #fff;
            border-radius: 3px;
            margin-bottom: 5px;

            &::before {
                content: "";
                display: block;
                width: 11px;
                height: 11px;
                border-radius: 50%;
                overflow: hidden;
                background: #E1E5E8;
                position: absolute;
                left: -21px;
                top: 15px;
                z-index: 2;
            }
            &::after {
                content: "";
                display: block;
                border-left: dashed 1px #E1E5E8;
                position: absolute;
                left: -16px;
                top: 24px;
                bottom: -24px;
                z-index: 1;
            }

            &:first-child {
                &::before {
                    background: #09f;
                }
            }

            &:last-child {
                border-bottom: none;

                &::after {
                    display: none;
                }
            }

            .row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 5px;

                .creator {
                    flex: 1;
                    min-width: 0;
                    display: flex;
                    align-items: center;

                    .vd-ui-user-wrap {
                        width: 100%;
                    }
                }
                .time {
                    flex-shrink: 0;
                    white-space: nowrap;
                    font-size: 12px;
                    color: #999;
                    text-align: right;
                    margin-left: 10px;
                }
            }
            .content {
                color: #000;
            }
        }
    }
}
.page-null-data {
    padding: 100px;
    text-align: center;

    > i {
        font-size: 32px;
        color: #09f;
    }

    > p {
        font-size: 14px;
        color: #999;
        margin-top: 10px;
    }
}



/* 添加拜访计划 */
.trader-detail {
    margin-top: 5px;
}

.price-dw {
    display: block;
    width: 32px;
    line-height: 36px;
    text-align: center;
    background: #fff;
    position: absolute;
    right: 0;
    top: 0;
}

