package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayExpensesEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseReceiptEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayExpensesConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseReceiptConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayExpensesMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseReceiptMapper;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesApiService;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 采购票的应付单兼容21-22 费用
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseInvoiceExpensePayCommonProcessor implements ItemProcessor<BatchInvoiceDto, BatchPayExpensesDto> {

    public static final String SPECIAL_INVOICE = "专用发票";
    public static final String TYPE_NAME = "运费";;
    private static final String TYPE_008 = "008";
    private static final String TYPE_021 = "021";


    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BatchBuyorderExpenseItemDtoMapper batchBuyorderExpenseItemDtoMapper;

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private KingDeePayExpensesService kingDeePayExpensesApiService;


    @Override
    public BatchPayExpensesDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        // 应付单对象
        KingDeePayExpensesDto dto = new KingDeePayExpensesDto();
        dto.setFQzokBddjtId(batchInvoiceDto.getInvoiceId().toString());
        dto.setFBusinessType("FY");
        // 判断是否数据已存在
        boolean old = kingDeePayExpensesApiService.kingDeeIsExist(dto);
        if(old){
            log.info("采购费用的应付单,数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        List<BuyOrderExpenseExcelDto> excelDtoList = batchInvoiceDto.getExcelDtoList();

        log.info("处理采购费用单 应付单{}", JSON.toJSONString(batchInvoiceDto));
        boolean special;
        if (StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains(SPECIAL_INVOICE)) {
            special = true;
            // 过滤未认证的专票
            if (!batchInvoiceDto.getIsAuth().equals(1)) {
                return null;
            }
        } else {
            special = false;
        }

        if (CollUtil.isEmpty(excelDtoList)) {
            return null;
        }

        List<BatchInvoiceDetailDto> byInvoiceId = batchInvoiceDetailDtoMapper.findByInvoiceIdAndBuyorderGoodsId(batchInvoiceDto.getInvoiceId());
        byInvoiceId.forEach(x->{
            Optional<BuyOrderExpenseExcelDto> first = excelDtoList.stream().filter(c -> c.getSku().equals(x.getSku())).findFirst();
            if (first.isPresent()) {
                x.setSku(first.get().getSku());
                x.setHasExpense(Boolean.TRUE);
            }

        });

        List<BatchInvoiceDetailDto> collect = byInvoiceId.stream().filter(BatchInvoiceDetailDto::getHasExpense).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            log.info("采购票：{}无费用商品",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            return null;
        }

        batchInvoiceDto.setBatchInvoiceDetailDtoList(collect);

        if (CollUtil.isEmpty(excelDtoList)) {
            log.warn("无表格采购费用明细单,{}", excelDtoList);
            return null;
        }
        excelDtoList.forEach(x->{
            String s = batchBuyorderExpenseItemDtoMapper.selectUnitKingDeeNo(x.getSku());
            if (StrUtil.isEmpty(s)) {
                if (x.getType().equals(TYPE_NAME)) {
                    s = TYPE_008;
                } else {
                    s = TYPE_021;
                }
            }
            x.setUnitKingDeeNo(s);
        });


        dto.setFId(0);
        dto.setFDate(DateUtil.formatDate(new Date(batchInvoiceDto.getValidTime())));
        dto.setFSupplierId(batchInvoiceDto.getTraderSupplierId().toString());
        DecimalFormat decimalFormat = new DecimalFormat(("0.00#"));
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" :special? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)):"0.00";
        InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto = null;
        InPutFeePlainInvoiceDto inPutFeePlainInvoiceDto = null;

        excelDtoList.forEach(x -> {
            BigDecimal invoicePrice = x.getPrice();
            Optional<BatchInvoiceDetailDto> first = collect.stream().filter(a -> a.getSku().equals(x.getSku())).findFirst();
            KingDeePayExpensesDetailDto detailDto = new KingDeePayExpensesDetailDto();
            detailDto.setFEntryTaxRate(taxRate);
            detailDto.setFCOSTID(x.getUnitKingDeeNo());
            detailDto.setFTaxPrice(first.isPresent()?first.get().getPrice().toString() : invoicePrice.toString());
            detailDto.setFPriceQty(String.valueOf(first.isPresent()?first.get().getNum():x.getNum()));
            detailDto.setFINCLUDECOST(false);
            detailDto.setFCOSTDEPARTMENTID("BM9999");
            detailDto.setF_QZOK_GSXSDH("");
            // 由于贝登没有应付单，所以没有应付单明细id，将发票id拼接当成明细id传入+销售单号
            String detailIds = byInvoiceId.stream().filter(c -> c.getSku().equals(x.getSku())).map(BatchInvoiceDetailDto::getInvoiceDetailId).sorted().map(String::valueOf).collect(Collectors.joining(StrUtil.DASHED));
            detailDto.setF_QZOK_BDDJHID(detailIds);
            detailDto.setF_QZOK_WLBM(x.getSku());
            dto.getFEntityDetail().add(detailDto);


        });

        return BatchPayExpensesDto.builder()
                .kingDeePayExpensesDto(dto)
                .inPutFeePlainInvoiceDto(inPutFeePlainInvoiceDto)
                .inPutFeeSpecialInvoiceDto(inPutFeeSpecialInvoiceDto)
                .build();
    }




}
