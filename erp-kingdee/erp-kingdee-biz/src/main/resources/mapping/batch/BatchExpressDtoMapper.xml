<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchExpressDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchExpressDto">
    <!--@mbg.generated-->
    <!--@Table T_EXPRESS-->
    <id column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
    <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="DELIVERY_FROM" jdbcType="INTEGER" property="deliveryFrom" />
    <result column="LOGISTICS_COMMENTS" jdbcType="VARCHAR" property="logisticsComments" />
    <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable" />
    <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
    <result column="CARD_NUMBER" jdbcType="VARCHAR" property="cardNumber" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="REAL_WEIGHT" jdbcType="DECIMAL" property="realWeight" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="AMOUNT_WEIGHT" jdbcType="DECIMAL" property="amountWeight" />
    <result column="MAIL_GOODS" jdbcType="VARCHAR" property="mailGoods" />
    <result column="MAIL_GOODS_NUM" jdbcType="INTEGER" property="mailGoodsNum" />
    <result column="IS_PROTECT_PRICE" jdbcType="INTEGER" property="isProtectPrice" />
    <result column="PROTECT_PRICE" jdbcType="DECIMAL" property="protectPrice" />
    <result column="IS_RECEIPT" jdbcType="INTEGER" property="isReceipt" />
    <result column="MAIL_COMMTENTS" jdbcType="VARCHAR" property="mailCommtents" />
    <result column="SENT_SMS" jdbcType="INTEGER" property="sentSms" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="TRAVELING_BY_TICKET" jdbcType="INTEGER" property="travelingByTicket" />
    <result column="IS_INVOICING" jdbcType="INTEGER" property="isInvoicing" />
    <result column="WMS_ORDER_NO" jdbcType="VARCHAR" property="wmsOrderNo" />
    <result column="ONLINE_RECEIPT_ID" jdbcType="INTEGER" property="onlineReceiptId" />
    <result column="BATCH_NO" jdbcType="VARCHAR" property="batchNo" />
    <result column="ENABLE_RECEIVE" jdbcType="INTEGER" property="enableReceive" />
    <result column="SYSTEM_ADD_TIME" jdbcType="TIMESTAMP" property="systemAddTime" />
    <result column="OLD_LOGISTICS_NO" jdbcType="VARCHAR" property="oldLogisticsNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    EXPRESS_ID, LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, DELIVERY_TIME, ARRIVAL_STATUS, 
    ARRIVAL_TIME, DELIVERY_FROM, LOGISTICS_COMMENTS, IS_ENABLE, PAYMENT_TYPE, CARD_NUMBER, 
    BUSINESS_TYPE, REAL_WEIGHT, NUM, AMOUNT_WEIGHT, MAIL_GOODS, MAIL_GOODS_NUM, IS_PROTECT_PRICE, 
    PROTECT_PRICE, IS_RECEIPT, MAIL_COMMTENTS, SENT_SMS, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, TRAVELING_BY_TICKET, IS_INVOICING, WMS_ORDER_NO, ONLINE_RECEIPT_ID, BATCH_NO, 
    ENABLE_RECEIVE, SYSTEM_ADD_TIME, OLD_LOGISTICS_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_EXPRESS
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_EXPRESS
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS (LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, 
      DELIVERY_TIME, ARRIVAL_STATUS, ARRIVAL_TIME, 
      DELIVERY_FROM, LOGISTICS_COMMENTS, IS_ENABLE, 
      PAYMENT_TYPE, CARD_NUMBER, BUSINESS_TYPE, 
      REAL_WEIGHT, NUM, AMOUNT_WEIGHT, 
      MAIL_GOODS, MAIL_GOODS_NUM, IS_PROTECT_PRICE, 
      PROTECT_PRICE, IS_RECEIPT, MAIL_COMMTENTS, 
      SENT_SMS, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, TRAVELING_BY_TICKET, 
      IS_INVOICING, WMS_ORDER_NO, ONLINE_RECEIPT_ID, 
      BATCH_NO, ENABLE_RECEIVE, SYSTEM_ADD_TIME, 
      OLD_LOGISTICS_NO)
    values (#{logisticsId,jdbcType=INTEGER}, #{logisticsNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{deliveryTime,jdbcType=BIGINT}, #{arrivalStatus,jdbcType=INTEGER}, #{arrivalTime,jdbcType=BIGINT},
      #{deliveryFrom,jdbcType=INTEGER}, #{logisticsComments,jdbcType=VARCHAR}, #{isEnable,jdbcType=INTEGER},
      #{paymentType,jdbcType=INTEGER}, #{cardNumber,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER},
      #{realWeight,jdbcType=DECIMAL}, #{num,jdbcType=INTEGER}, #{amountWeight,jdbcType=DECIMAL}, 
      #{mailGoods,jdbcType=VARCHAR}, #{mailGoodsNum,jdbcType=INTEGER}, #{isProtectPrice,jdbcType=INTEGER},
      #{protectPrice,jdbcType=DECIMAL}, #{isReceipt,jdbcType=INTEGER}, #{mailCommtents,jdbcType=VARCHAR},
      #{sentSms,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{travelingByTicket,jdbcType=INTEGER},
      #{isInvoicing,jdbcType=INTEGER}, #{wmsOrderNo,jdbcType=VARCHAR}, #{onlineReceiptId,jdbcType=INTEGER},
      #{batchNo,jdbcType=VARCHAR}, #{enableReceive,jdbcType=INTEGER}, #{systemAddTime,jdbcType=TIMESTAMP},
      #{oldLogisticsNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM,
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        LOGISTICS_COMMENTS,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        CARD_NUMBER,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT,
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        MAIL_GOODS,
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM,
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE,
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE,
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT,
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        MAIL_COMMTENTS,
      </if>
      <if test="sentSms != null">
        SENT_SMS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET,
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING,
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        WMS_ORDER_NO,
      </if>
      <if test="onlineReceiptId != null">
        ONLINE_RECEIPT_ID,
      </if>
      <if test="batchNo != null and batchNo != ''">
        BATCH_NO,
      </if>
      <if test="enableReceive != null">
        ENABLE_RECEIVE,
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME,
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        OLD_LOGISTICS_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="realWeight != null">
        #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        #{isProtectPrice,jdbcType=INTEGER},
      </if>
      <if test="protectPrice != null">
        #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        #{isReceipt,jdbcType=INTEGER},
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        #{sentSms,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        #{travelingByTicket,jdbcType=INTEGER},
      </if>
      <if test="isInvoicing != null">
        #{isInvoicing,jdbcType=INTEGER},
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptId != null">
        #{onlineReceiptId,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null and batchNo != ''">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="enableReceive != null">
        #{enableReceive,jdbcType=INTEGER},
      </if>
      <if test="systemAddTime != null">
        #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        #{oldLogisticsNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDto">
    <!--@mbg.generated-->
    update T_EXPRESS
    <set>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=INTEGER},
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT = #{isReceipt,jdbcType=INTEGER},
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        SENT_SMS = #{sentSms,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=INTEGER},
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING = #{isInvoicing,jdbcType=INTEGER},
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptId != null">
        ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null and batchNo != ''">
        BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="enableReceive != null">
        ENABLE_RECEIVE = #{enableReceive,jdbcType=INTEGER},
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDto">
    <!--@mbg.generated-->
    update T_EXPRESS
    set LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=INTEGER},
      AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=INTEGER},
      PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      IS_RECEIPT = #{isReceipt,jdbcType=INTEGER},
      MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      SENT_SMS = #{sentSms,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=INTEGER},
      IS_INVOICING = #{isInvoicing,jdbcType=INTEGER},
      WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType=INTEGER},
      BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      ENABLE_RECEIVE = #{enableReceive,jdbcType=INTEGER},
      SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR}
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPRESS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="LOGISTICS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_FROM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.deliveryFrom,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isEnable,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.paymentType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CARD_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.cardNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.businessType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="REAL_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.realWeight,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AMOUNT_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.amountWeight,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="MAIL_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailGoods,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MAIL_GOODS_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailGoodsNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_PROTECT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isProtectPrice,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PROTECT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.protectPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="IS_RECEIPT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isReceipt,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MAIL_COMMTENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailCommtents,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SENT_SMS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.sentSms,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRAVELING_BY_TICKET = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.travelingByTicket,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_INVOICING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isInvoicing,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="WMS_ORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.wmsOrderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ONLINE_RECEIPT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.onlineReceiptId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BATCH_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.batchNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ENABLE_RECEIVE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.enableReceive,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SYSTEM_ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.systemAddTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="OLD_LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.oldLogisticsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where EXPRESS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expressId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPRESS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="LOGISTICS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsId != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsNo != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyId != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalStatus != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_FROM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryFrom != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.deliveryFrom,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsComments != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEnable != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isEnable,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentType != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.paymentType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CARD_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.cardNumber != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.cardNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessType != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.businessType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realWeight != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.realWeight,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amountWeight != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.amountWeight,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="MAIL_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mailGoods != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailGoods,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MAIL_GOODS_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mailGoodsNum != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailGoodsNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_PROTECT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isProtectPrice != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isProtectPrice,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROTECT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.protectPrice != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.protectPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_RECEIPT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isReceipt != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isReceipt,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MAIL_COMMTENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mailCommtents != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailCommtents,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SENT_SMS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sentSms != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.sentSms,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRAVELING_BY_TICKET = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.travelingByTicket != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.travelingByTicket,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_INVOICING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isInvoicing != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isInvoicing,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="WMS_ORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.wmsOrderNo != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.wmsOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ONLINE_RECEIPT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.onlineReceiptId != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.onlineReceiptId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BATCH_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.batchNo != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.batchNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ENABLE_RECEIVE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enableReceive != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.enableReceive,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SYSTEM_ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.systemAddTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.systemAddTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="OLD_LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldLogisticsNo != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.oldLogisticsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where EXPRESS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expressId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS
    (LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, DELIVERY_TIME, ARRIVAL_STATUS, ARRIVAL_TIME, 
      DELIVERY_FROM, LOGISTICS_COMMENTS, IS_ENABLE, PAYMENT_TYPE, CARD_NUMBER, BUSINESS_TYPE, 
      REAL_WEIGHT, NUM, AMOUNT_WEIGHT, MAIL_GOODS, MAIL_GOODS_NUM, IS_PROTECT_PRICE, 
      PROTECT_PRICE, IS_RECEIPT, MAIL_COMMTENTS, SENT_SMS, ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, TRAVELING_BY_TICKET, IS_INVOICING, WMS_ORDER_NO, ONLINE_RECEIPT_ID, BATCH_NO, 
      ENABLE_RECEIVE, SYSTEM_ADD_TIME, OLD_LOGISTICS_NO)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.logisticsId,jdbcType=INTEGER}, #{item.logisticsNo,jdbcType=VARCHAR}, #{item.companyId,jdbcType=INTEGER}, 
        #{item.deliveryTime,jdbcType=BIGINT}, #{item.arrivalStatus,jdbcType=INTEGER}, #{item.arrivalTime,jdbcType=BIGINT},
        #{item.deliveryFrom,jdbcType=INTEGER}, #{item.logisticsComments,jdbcType=VARCHAR}, 
        #{item.isEnable,jdbcType=INTEGER}, #{item.paymentType,jdbcType=INTEGER}, #{item.cardNumber,jdbcType=VARCHAR},
        #{item.businessType,jdbcType=INTEGER}, #{item.realWeight,jdbcType=DECIMAL}, #{item.num,jdbcType=INTEGER},
        #{item.amountWeight,jdbcType=DECIMAL}, #{item.mailGoods,jdbcType=VARCHAR}, #{item.mailGoodsNum,jdbcType=INTEGER}, 
        #{item.isProtectPrice,jdbcType=INTEGER}, #{item.protectPrice,jdbcType=DECIMAL},
        #{item.isReceipt,jdbcType=INTEGER}, #{item.mailCommtents,jdbcType=VARCHAR}, #{item.sentSms,jdbcType=INTEGER},
        #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, 
        #{item.updater,jdbcType=INTEGER}, #{item.travelingByTicket,jdbcType=INTEGER}, #{item.isInvoicing,jdbcType=INTEGER},
        #{item.wmsOrderNo,jdbcType=VARCHAR}, #{item.onlineReceiptId,jdbcType=INTEGER}, 
        #{item.batchNo,jdbcType=VARCHAR}, #{item.enableReceive,jdbcType=INTEGER}, #{item.systemAddTime,jdbcType=TIMESTAMP},
        #{item.oldLogisticsNo,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      LOGISTICS_ID,
      LOGISTICS_NO,
      COMPANY_ID,
      DELIVERY_TIME,
      ARRIVAL_STATUS,
      ARRIVAL_TIME,
      DELIVERY_FROM,
      LOGISTICS_COMMENTS,
      IS_ENABLE,
      PAYMENT_TYPE,
      CARD_NUMBER,
      BUSINESS_TYPE,
      REAL_WEIGHT,
      NUM,
      AMOUNT_WEIGHT,
      MAIL_GOODS,
      MAIL_GOODS_NUM,
      IS_PROTECT_PRICE,
      PROTECT_PRICE,
      IS_RECEIPT,
      MAIL_COMMTENTS,
      SENT_SMS,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      TRAVELING_BY_TICKET,
      IS_INVOICING,
      WMS_ORDER_NO,
      ONLINE_RECEIPT_ID,
      BATCH_NO,
      ENABLE_RECEIVE,
      SYSTEM_ADD_TIME,
      OLD_LOGISTICS_NO,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      #{logisticsId,jdbcType=INTEGER},
      #{logisticsNo,jdbcType=VARCHAR},
      #{companyId,jdbcType=INTEGER},
      #{deliveryTime,jdbcType=BIGINT},
      #{arrivalStatus,jdbcType=INTEGER},
      #{arrivalTime,jdbcType=BIGINT},
      #{deliveryFrom,jdbcType=INTEGER},
      #{logisticsComments,jdbcType=VARCHAR},
      #{isEnable,jdbcType=INTEGER},
      #{paymentType,jdbcType=INTEGER},
      #{cardNumber,jdbcType=VARCHAR},
      #{businessType,jdbcType=INTEGER},
      #{realWeight,jdbcType=DECIMAL},
      #{num,jdbcType=INTEGER},
      #{amountWeight,jdbcType=DECIMAL},
      #{mailGoods,jdbcType=VARCHAR},
      #{mailGoodsNum,jdbcType=INTEGER},
      #{isProtectPrice,jdbcType=INTEGER},
      #{protectPrice,jdbcType=DECIMAL},
      #{isReceipt,jdbcType=INTEGER},
      #{mailCommtents,jdbcType=VARCHAR},
      #{sentSms,jdbcType=INTEGER},
      #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER},
      #{travelingByTicket,jdbcType=INTEGER},
      #{isInvoicing,jdbcType=INTEGER},
      #{wmsOrderNo,jdbcType=VARCHAR},
      #{onlineReceiptId,jdbcType=INTEGER},
      #{batchNo,jdbcType=VARCHAR},
      #{enableReceive,jdbcType=INTEGER},
      #{systemAddTime,jdbcType=TIMESTAMP},
      #{oldLogisticsNo,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=INTEGER},
      AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=INTEGER},
      PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      IS_RECEIPT = #{isReceipt,jdbcType=INTEGER},
      MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      SENT_SMS = #{sentSms,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=INTEGER},
      IS_INVOICING = #{isInvoicing,jdbcType=INTEGER},
      WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType=INTEGER},
      BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      ENABLE_RECEIVE = #{enableReceive,jdbcType=INTEGER},
      SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM,
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        LOGISTICS_COMMENTS,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        CARD_NUMBER,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT,
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        MAIL_GOODS,
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM,
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE,
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE,
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT,
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        MAIL_COMMTENTS,
      </if>
      <if test="sentSms != null">
        SENT_SMS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET,
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING,
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        WMS_ORDER_NO,
      </if>
      <if test="onlineReceiptId != null">
        ONLINE_RECEIPT_ID,
      </if>
      <if test="batchNo != null and batchNo != ''">
        BATCH_NO,
      </if>
      <if test="enableReceive != null">
        ENABLE_RECEIVE,
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME,
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        OLD_LOGISTICS_NO,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="realWeight != null">
        #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        #{isProtectPrice,jdbcType=INTEGER},
      </if>
      <if test="protectPrice != null">
        #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        #{isReceipt,jdbcType=INTEGER},
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        #{sentSms,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        #{travelingByTicket,jdbcType=INTEGER},
      </if>
      <if test="isInvoicing != null">
        #{isInvoicing,jdbcType=INTEGER},
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptId != null">
        #{onlineReceiptId,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null and batchNo != ''">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="enableReceive != null">
        #{enableReceive,jdbcType=INTEGER},
      </if>
      <if test="systemAddTime != null">
        #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        #{oldLogisticsNo,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=INTEGER},
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT = #{isReceipt,jdbcType=INTEGER},
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        SENT_SMS = #{sentSms,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=INTEGER},
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING = #{isInvoicing,jdbcType=INTEGER},
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptId != null">
        ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null and batchNo != ''">
        BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="enableReceive != null">
        ENABLE_RECEIVE = #{enableReceive,jdbcType=INTEGER},
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="queryDirectPurchaseArrivedExpress" resultMap="BaseResultMap">
    select te.*
    from T_EXPRESS te
    left join T_EXPRESS_DETAIL ted on te.EXPRESS_ID = ted.EXPRESS_ID
    left join T_BUYORDER_GOODS tbg on ted.RELATED_ID = tbg.BUYORDER_GOODS_ID and tbg.IS_DELETE = 0
    left join T_BUYORDER tb on tbg.BUYORDER_ID = tb.BUYORDER_ID
    where te.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
    and te.ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER}
    and te.ARRIVAL_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
    and te.ARRIVAL_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
    and ted.BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
    and tb.DELIVERY_DIRECT = 1
    group by te.EXPRESS_ID
    limit #{_pagesize} offset #{_skiprows}
  </select>
</mapper>