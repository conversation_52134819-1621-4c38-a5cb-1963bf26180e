<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchFlowOrderDtoMapper">
    <!--auto generated by MybatisCodeHelper on 2025-01-16-->
    <select id="findByAll" resultType="com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderDto">
        select TFO.*
        from T_FLOW_ORDER TFO
                 left join KING_DEE_INTERNAL_PROCUREMENT KDIP on cast(TFO.FLOW_ORDER_ID AS CHAR) = KDIP.F_QZOK_BDDJTID
        <where>
            and IS_DELETE = 0
                  and KDIP.F_QZOK_BDDJTID IS NULL
                  and AUDIT_STATUS = 1
                  AND PUSH_DIRECTION = 1
            <if test="beginTime != null and endTime != null">
                and TFO.AUDIT_TIME <![CDATA[>=]]> #{beginTime}
                and TFO.AUDIT_TIME <![CDATA[<=]]> #{endTime}
            </if>
        </where>

        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="findByFlowOrderIdGetFlowOrderDetailDto"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderDetailDto">
        select *
        from T_FLOW_ORDER_DETAIL
        where FLOW_ORDER_ID = #{flowOrderId}
          and IS_DELETE = 0
    </select>

    <select id="findByFlowOrderIdGetFlowNodeDto" resultType="com.vedeng.erp.kingdee.batch.dto.BatchFlowNodeDto">
        select TFN.*, TSOD.COMMENTS ratio
        from T_FLOW_NODE TFN
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TFN.INVOICE_TYPE
        where FLOW_ORDER_ID = #{flowOrderId}
          and IS_DELETE = 0
    </select>

    <select id="findByFlowOrderIdGetFlowNodeOrderDetailPriceDto"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchFlowNodeOrderDetailPriceDto">
        select *
        from T_FLOW_NODE_ORDER_DETAIL_PRICE
        where FLOW_ORDER_DETAIL_ID = #{flowOrderDetailId}
    </select>

    <select id="findCustomerTraderIdGetCompanyData"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchCompanyDataDto">
        select *
        from KING_DEE_COMPANY_DATA KDCD
        left join T_TRADER_CUSTOMER TTC on KDCD.CUSTOMER_TRADER_ID = TTC.TRADER_CUSTOMER_ID
        where TTC.TRADER_ID = #{customerTraderId}
    </select>

    <select id="findSupplierTraderIdGetCompanyData"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchCompanyDataDto">
        select *
        from KING_DEE_COMPANY_DATA KDCD
        left join T_TRADER_SUPPLIER TTS on KDCD.SUPPLIER_TRADER_ID = TTS.TRADER_SUPPLIER_ID
        where TTS.TRADER_ID = #{supplierTraderId}
    </select>


    <select id="findSupplierTraderId"
            resultType="Integer">
        select TRADER_SUPPLIER_ID
        from T_TRADER_SUPPLIER TTS
        where TTS.TRADER_ID = #{traderId}
    </select>

    <select id="findCustomerTraderId"
            resultType="Integer">
        select TRADER_CUSTOMER_ID
        from T_TRADER_CUSTOMER
        where TRADER_ID = #{traderId}
    </select>


    <select id="findNoPushInternalProcurement"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderDto">
        select *
        from KING_DEE_INTERNAL_PROCUREMENT KDIP
        left join T_FLOW_ORDER TTS on KDIP.F_QZOK_BDDJTID = TTS.FLOW_ORDER_ID
        where KDIP.PUSH_STATUS = 0
        and TTS.AUDIT_STATUS = 1
        and TTS.PUSH_DIRECTION = 1
        and TTS.IS_DELETE = 0;
    </select>

    <insert id="insertSelectiveFlowOrderInfo" keyColumn="FLOW_ORDER_INFO_ID" keyProperty="flowOrderInfoId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderInfoDto" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_FLOW_ORDER_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flowOrderInfoType != null">
                FLOW_ORDER_INFO_TYPE,
            </if>
            <if test="flowOrderInfoNo != null and flowOrderInfoNo != ''">
                FLOW_ORDER_INFO_NO,
            </if>
            <if test="flowNodeId != null">
                FLOW_NODE_ID,
            </if>
            <if test="orderStatus != null">
                ORDER_STATUS,
            </if>
            <if test="paymentStatus != null">
                PAYMENT_STATUS,
            </if>
            <if test="storageStatus != null">
                STORAGE_STATUS,
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS,
            </if>
            <if test="invoiceInfo != null and invoiceInfo != ''">
                INVOICE_INFO,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flowOrderInfoType != null">
                #{flowOrderInfoType},
            </if>
            <if test="flowOrderInfoNo != null and flowOrderInfoNo != ''">
                #{flowOrderInfoNo},
            </if>
            <if test="flowNodeId != null">
                #{flowNodeId},
            </if>
            <if test="orderStatus != null">
                #{orderStatus},
            </if>
            <if test="paymentStatus != null">
                #{paymentStatus},
            </if>
            <if test="storageStatus != null">
                #{storageStatus},
            </if>
            <if test="invoiceStatus != null">
                #{invoiceStatus},
            </if>
            <if test="invoiceInfo != null and invoiceInfo != ''">
                #{invoiceInfo},
            </if>
            <if test="isDelete != null">
                #{isDelete},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelectiveFlowOrderInfo" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderInfoDto">
        <!--@mbg.generated-->
        update T_FLOW_ORDER_INFO
        <set>
            <if test="flowOrderInfoType != null">
                FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType},
            </if>
            <if test="flowOrderInfoNo != null and flowOrderInfoNo != ''">
                FLOW_ORDER_INFO_NO = #{flowOrderInfoNo},
            </if>
            <if test="flowNodeId != null">
                FLOW_NODE_ID = #{flowNodeId},
            </if>
            <if test="orderStatus != null">
                ORDER_STATUS = #{orderStatus},
            </if>
            <if test="paymentStatus != null">
                PAYMENT_STATUS = #{paymentStatus},
            </if>
            <if test="storageStatus != null">
                STORAGE_STATUS = #{storageStatus},
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS = #{invoiceStatus},
            </if>
            <if test="invoiceInfo != null and invoiceInfo != ''">
                INVOICE_INFO = #{invoiceInfo},
            </if>
        </set>
        where FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
    </update>

    <select id="findByflowNodeId" resultType="com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderInfoDto">
        select *
        from T_FLOW_ORDER_INFO
        where FLOW_NODE_ID = #{flowNodeId}
    </select>

</mapper>
