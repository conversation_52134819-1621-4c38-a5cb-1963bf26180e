package com.vedeng.erp.saleorder.constant;

/**
 * 业务确认记录表业务类型
 */
public enum ConfirmRecordBusinessTypeEnum {
    ORDER_CONFIRM(0, "订单确认"),
    PENDING_CONFIRM(1, "货物签收确认");

    private Integer code;

    private String type;

    ConfirmRecordBusinessTypeEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
