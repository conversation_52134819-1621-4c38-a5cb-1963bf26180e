package com.vedeng.infrastructure.esign.mapper;
import java.util.Collection;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity;

public interface ElectronicSignRecordMapper {
    /**
     * delete by primary key
     *
     * @param electronicSignRecordId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer electronicSignRecordId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ElectronicSignRecordEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ElectronicSignRecordEntity record);

    /**
     * select by primary key
     *
     * @param electronicSignRecordId primary key
     * @return object by primary key
     */
    ElectronicSignRecordEntity selectByPrimaryKey(Integer electronicSignRecordId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ElectronicSignRecordEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ElectronicSignRecordEntity record);

    /**
     * 获取对应的数据
     *
     * @param businessId   业务id
     * @param businessType 业务类型
     * @return List<ElectronicSignRecordEntity>
     */
    List<ElectronicSignRecordEntity> findByBusinessIdAndBusinessType(@Param("businessId") String businessId,
                                                                     @Param("businessType") Integer businessType);


    List<ElectronicSignRecordEntity> findBySignStatusIn(@Param("signStatusCollection")Collection<Integer> signStatusCollection,
                                                        @Param("businessType") Integer businessType);

    /**
     * 重试次数加1
     * @param electronicSignRecordId
     * @return
     */
    int addRetryNum(Integer electronicSignRecordId);

}