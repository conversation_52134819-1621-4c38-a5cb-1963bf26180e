
function updateSaleGoodsSave(saleorderId,saleorderGoodsIdArr,modifyFlag){
	var deliveryDirect = 0;
	if($("input:radio[id='deliveryDirect_y']").is(":checked")){
		deliveryDirect = 1;
	}else{
		deliveryDirect = 0;
	}
	 var deliveryDirectComments = $("#deliveryDirectComments").val();
	if(deliveryDirect == '1'){
		if(deliveryDirectComments == null || deliveryDirectComments.length == 0){
			warnTips("deliveryDirectComments","选择直发时，不允许为空");//文本框ID和提示用语
			return false;
		}else if(deliveryDirectComments.length > 64){
			warnTips("deliveryDirectComments","直发备注不允许超出64位字符");//文本框ID和提示用语
			return false;
		}
	}
	let insideComments = $('#insideComments').val();
	let hasRemark = 1;
	if (insideComments) {
		hasRemark = 0;
	}
	checkLogin();


	$.ajax({
		async:false,
		url: page_url+"/order/saleorder/updateSaleGoodsSave.do",
		data : {"saleorderId":saleorderId,"saleorderGoodsIdArr":saleorderGoodsIdArr,"deliveryDirect":deliveryDirect,"deliveryDirectComments":deliveryDirectComments,'insideComments': insideComments,'hasRemark': hasRemark,'modifyFlag':modifyFlag},
		type:"POST",
		dataType : "json",
		success:function(data){
			if (data.code === 0) {
				let labelData = $("#insideComments").attr("label_data");
				// 未设置，不保存
				if(!labelData){
					parent.location.reload();
					$('#close-layer').click();
					return;
				}

				$.ajax({
					type: "POST",
					url:'./updateInsideComments.do',
					data: labelData,
					dataType:'json',
					contentType: "application/json",
					success: function(data){
						//			refreshPageList(data);
						if(data.code==0){
							layer.alert(data.message, {icon: 1},
								function () {
									parent.location.reload();
									$('#close-layer').click();
								}
							);
						}else{
							layer.alert(data.message);
						}
					},
					error:function(data){
						if(data.status == 1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});

			}else {
				layer.alert(data.message,{ icon: 2 });
			}
		},
		error:function(data){
			layer.alert("批量修改内部备注标签失败")
		}
	})

}

// 内部备注重复点击 比较最后一次点击时间和当前时间
var lastClick;
function lockClick(){
	let nowClick = new Date()
	if (lastClick === undefined) {
		lastClick = nowClick
		return true
	} else {
		if (Math.round((nowClick.getTime() - lastClick.getTime())) > 2000) {
			lastClick = nowClick
			return true
		}
		else {
			lastClick = nowClick
			return false
		}
	}
}

/**
 * 内部备注组件触发事件
 * @param dom 当前dom元素
 */
function insideRemark(dom) {

	if(!lockClick()){
		return;
	}

	let labelData = $(dom).attr('label_data');
	let scene = $(dom).attr('scene');
	let remark = $(dom).val();

	let hasRemark = false;
	if (remark) {
		hasRemark = true;
	}
	let skuList = [];
	$(dom).siblings("input[name='skuList']").each(function (i,v) {
		let sku = {
			skuId: $(v).attr('skuId'),
			skuNo:  $(v).attr('skuNo'),
			skuName: $(v).attr('skuName')
		}
		skuList.push(sku)
	});
	console.log(skuList)
	let relationId = $("input[name='saleorderId']").val();
	new LabelMark({
		el: dom,
		value: labelData,
		url: page_url + '/order/remarkComponent/getInitComponent.do',
		query: {
			scene: 0,
			isAll: 0,
			remark: remark,
			hasRemark: hasRemark,
			relationId: relationId,
			skuList: skuList
		},
		multi: true
	});
}

/**
 * 更新产品备注标签
 */
function updateInsideComments() {
	let labelData = $("#insideComments").attr("label_data");
	// 未设置，不保存
	if(!labelData){
		parent.location.reload();
		$('#close-layer').click();
		return;
	}
	$.ajax({
		async:false,
		url:'./updateInsideComments.do',
		data: labelData,
		type:"POST",
		dataType : "json",
		contentType: "application/json",
		success:function(data){
			if (data.code === 0) {
				layer.alert(data.message, {icon: 1},
					function () {
						parent.location.reload();
						$('#close-layer').click();
					}
				);
			}else {
				layer.alert(data.message,{ icon: 2 });
			}
		},
		error:function(data){
			layer.alert("批量修改内部备注标签失败")
		}
	})
}
