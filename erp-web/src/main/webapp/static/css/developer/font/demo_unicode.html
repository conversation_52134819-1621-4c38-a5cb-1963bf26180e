
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">

    <style type="text/css">

        @font-face {font-family: "iconfont";
          src: url('iconfont.eot'); /* IE9*/
          src: url('iconfont.eot#iefix') format('embedded-opentype'), /* IE6-IE8 */
          url('iconfont.woff') format('woff'), /* chrome, firefox */
          url('iconfont.ttf') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
          url('iconfont.svg#iconfont') format('svg'); /* iOS 4.1- */
        }

        .iconfont {
          font-family:"iconfont" !important;
          font-size:16px;
          font-style:normal;
          -webkit-font-smoothing: antialiased;
          -webkit-text-stroke-width: 0.2px;
          -moz-osx-font-smoothing: grayscale;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                <i class="icon iconfont">&#xe650;</i>
                    <div class="name">前端应用模版</div>
                    <div class="code">&amp;#xe650;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe600;</i>
                    <div class="name">sun</div>
                    <div class="code">&amp;#xe600;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe635;</i>
                    <div class="name">樱桃</div>
                    <div class="code">&amp;#xe635;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe68c;</i>
                    <div class="name">sun</div>
                    <div class="code">&amp;#xe68c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61c;</i>
                    <div class="name">可爱</div>
                    <div class="code">&amp;#xe61c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe776;</i>
                    <div class="name">充电电路故障</div>
                    <div class="code">&amp;#xe776;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60e;</i>
                    <div class="name">互联网</div>
                    <div class="code">&amp;#xe60e;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe610;</i>
                    <div class="name">戒指</div>
                    <div class="code">&amp;#xe610;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe651;</i>
                    <div class="name">西瓜</div>
                    <div class="code">&amp;#xe651;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61d;</i>
                    <div class="name">海豚</div>
                    <div class="code">&amp;#xe61d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61e;</i>
                    <div class="name">化学</div>
                    <div class="code">&amp;#xe61e;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6c4;</i>
                    <div class="name">moon_and</div>
                    <div class="code">&amp;#xe6c4;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60f;</i>
                    <div class="name">动物园</div>
                    <div class="code">&amp;#xe60f;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe605;</i>
                    <div class="name">动物图标-上色-长颈鹿</div>
                    <div class="code">&amp;#xe605;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61f;</i>
                    <div class="name">动物图标-上色-猪</div>
                    <div class="code">&amp;#xe61f;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe64b;</i>
                    <div class="name">女生</div>
                    <div class="code">&amp;#xe64b;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe629;</i>
                    <div class="name">戒指1</div>
                    <div class="code">&amp;#xe629;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe620;</i>
                    <div class="name">手链</div>
                    <div class="code">&amp;#xe620;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe621;</i>
                    <div class="name">米妮烘焙坊 </div>
                    <div class="code">&amp;#xe621;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe622;</i>
                    <div class="name">布丁</div>
                    <div class="code">&amp;#xe622;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe623;</i>
                    <div class="name">饼干</div>
                    <div class="code">&amp;#xe623;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe624;</i>
                    <div class="name">杯子蛋糕</div>
                    <div class="code">&amp;#xe624;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe625;</i>
                    <div class="name">爆米花</div>
                    <div class="code">&amp;#xe625;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe626;</i>
                    <div class="name">冰淇淋</div>
                    <div class="code">&amp;#xe626;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe627;</i>
                    <div class="name">餐具</div>
                    <div class="code">&amp;#xe627;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe628;</i>
                    <div class="name">果汁</div>
                    <div class="code">&amp;#xe628;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62a;</i>
                    <div class="name">汉堡</div>
                    <div class="code">&amp;#xe62a;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62b;</i>
                    <div class="name">牛角面包</div>
                    <div class="code">&amp;#xe62b;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62c;</i>
                    <div class="name">咖啡</div>
                    <div class="code">&amp;#xe62c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62d;</i>
                    <div class="name">牛奶</div>
                    <div class="code">&amp;#xe62d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62e;</i>
                    <div class="name">蛋糕</div>
                    <div class="code">&amp;#xe62e;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62f;</i>
                    <div class="name">糖果</div>
                    <div class="code">&amp;#xe62f;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe630;</i>
                    <div class="name">吐司</div>
                    <div class="code">&amp;#xe630;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe631;</i>
                    <div class="name">披萨</div>
                    <div class="code">&amp;#xe631;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe632;</i>
                    <div class="name">甜甜圈</div>
                    <div class="code">&amp;#xe632;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe633;</i>
                    <div class="name">雪糕</div>
                    <div class="code">&amp;#xe633;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe606;</i>
                    <div class="name">香蕉</div>
                    <div class="code">&amp;#xe606;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe607;</i>
                    <div class="name">柠檬</div>
                    <div class="code">&amp;#xe607;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe608;</i>
                    <div class="name">榴莲</div>
                    <div class="code">&amp;#xe608;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe609;</i>
                    <div class="name">荔枝</div>
                    <div class="code">&amp;#xe609;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60a;</i>
                    <div class="name">草莓</div>
                    <div class="code">&amp;#xe60a;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60b;</i>
                    <div class="name">猕猴桃</div>
                    <div class="code">&amp;#xe60b;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60c;</i>
                    <div class="name">葡萄</div>
                    <div class="code">&amp;#xe60c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60d;</i>
                    <div class="name">山竹</div>
                    <div class="code">&amp;#xe60d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe611;</i>
                    <div class="name">柚子</div>
                    <div class="code">&amp;#xe611;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe612;</i>
                    <div class="name">樱桃</div>
                    <div class="code">&amp;#xe612;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe613;</i>
                    <div class="name">椰子</div>
                    <div class="code">&amp;#xe613;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe614;</i>
                    <div class="name">芒果</div>
                    <div class="code">&amp;#xe614;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe615;</i>
                    <div class="name">菠萝</div>
                    <div class="code">&amp;#xe615;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe616;</i>
                    <div class="name">橙子</div>
                    <div class="code">&amp;#xe616;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe617;</i>
                    <div class="name">桃子</div>
                    <div class="code">&amp;#xe617;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe618;</i>
                    <div class="name">蓝莓</div>
                    <div class="code">&amp;#xe618;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe619;</i>
                    <div class="name">香梨</div>
                    <div class="code">&amp;#xe619;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61a;</i>
                    <div class="name">苹果</div>
                    <div class="code">&amp;#xe61a;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61b;</i>
                    <div class="name">火龙果</div>
                    <div class="code">&amp;#xe61b;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6a6;</i>
                    <div class="name">萌版航空航天-星星star</div>
                    <div class="code">&amp;#xe6a6;</div>
                </li>
            
        </ul>
        <h2 id="unicode-">unicode引用</h2>
        <hr>

        <p>unicode是字体在网页端最原始的应用方式，特点是：</p>
        <ul>
        <li>兼容性最好，支持ie6+，及所有现代浏览器。</li>
        <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
        <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
        </ul>
        <blockquote>
        <p>注意：新版iconfont支持多色图标，这些多色图标在unicode模式下将不能使用，如果有需求建议使用symbol的引用方式</p>
        </blockquote>
        <p>unicode使用步骤如下：</p>
        <h3 id="-font-face">第一步：拷贝项目下面生成的font-face</h3>
        <pre><code class="lang-js hljs javascript">@font-face {
  font-family: <span class="hljs-string">'iconfont'</span>;
  src: url(<span class="hljs-string">'iconfont.eot'</span>);
  src: url(<span class="hljs-string">'iconfont.eot?#iefix'</span>) format(<span class="hljs-string">'embedded-opentype'</span>),
  url(<span class="hljs-string">'iconfont.woff'</span>) format(<span class="hljs-string">'woff'</span>),
  url(<span class="hljs-string">'iconfont.ttf'</span>) format(<span class="hljs-string">'truetype'</span>),
  url(<span class="hljs-string">'iconfont.svg#iconfont'</span>) format(<span class="hljs-string">'svg'</span>);
}
</code></pre>
        <h3 id="-iconfont-">第二步：定义使用iconfont的样式</h3>
        <pre><code class="lang-js hljs javascript">.iconfont{
  font-family:<span class="hljs-string">"iconfont"</span> !important;
  font-size:<span class="hljs-number">16</span>px;font-style:normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: <span class="hljs-number">0.2</span>px;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
        <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
        <pre><code class="lang-js hljs javascript">&lt;i <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"iconfont"</span>&gt;&amp;#x33;<span class="xml"><span class="hljs-tag">&lt;/<span class="hljs-name">i</span>&gt;</span></span></code></pre>

        <blockquote>
        <p>"iconfont"是你项目下的font-family。可以通过编辑项目查看，默认是"iconfont"。</p>
        </blockquote>
    </div>


</body>
</html>
