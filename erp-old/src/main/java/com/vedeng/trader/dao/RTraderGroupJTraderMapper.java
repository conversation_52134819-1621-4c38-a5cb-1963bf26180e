package com.vedeng.trader.dao;


import com.vedeng.trader.group.model.TraderGroup;
import com.vedeng.trader.model.RTraderGroupJTrader;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RTraderGroupJTraderMapper {
    int deleteByPrimaryKey(Long rTraderGroupJTrader);

    int insert(RTraderGroupJTrader record);

    int insertSelective(RTraderGroupJTrader record);

    RTraderGroupJTrader selectByPrimaryKey(Long rTraderGroupJTrader);

    int updateByPrimaryKeySelective(RTraderGroupJTrader record);

    int updateByPrimaryKey(RTraderGroupJTrader record);

    List<Integer> getTraderIdsListPage(Map<String,Object> map);

    int deleteByGroupIdAndTraderIds(@Param("groupId")Integer groupId,@Param(value = "traderIds") List<Integer> traderIds);

    List<RTraderGroupJTrader> getTraderGroupById(@Param("traderGroupId") Integer traderGroupId, @Param("traderIds") List<Integer> traderIds);
}