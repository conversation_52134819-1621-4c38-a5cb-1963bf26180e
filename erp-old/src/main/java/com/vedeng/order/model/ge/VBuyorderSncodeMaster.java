package com.vedeng.order.model.ge;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * V_BUYORDER_SNCODE_MASTER
 * <AUTHOR>
@Data
public class VBuyorderSncodeMaster implements Serializable {
    /**
     * 采购单DDI采集主机表id
     */
    private Integer buyorderSncodeMasterId;

    /**
     * 关联采购商品表id
     */
    private Integer relatedId;

    /**
     * SKU
     */
    private String sku;

    /**
     * 商品id
     */
    private Integer skuId;

    /**
     * 主机序列号 SN码
     */
    private String masterSncode;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除  0否  1是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;

    public Integer getBuyorderSncodeMasterId() {
        return buyorderSncodeMasterId;
    }

    public void setBuyorderSncodeMasterId(Integer buyorderSncodeMasterId) {
        this.buyorderSncodeMasterId = buyorderSncodeMasterId;
    }

    public Integer getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getMasterSncode() {
        return masterSncode;
    }

    public void setMasterSncode(String masterSncode) {
        this.masterSncode = masterSncode;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getModeTime() {
        return modeTime;
    }

    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    public Boolean getDelete() {
        return isDelete;
    }

    public void setDelete(Boolean delete) {
        isDelete = delete;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }
}