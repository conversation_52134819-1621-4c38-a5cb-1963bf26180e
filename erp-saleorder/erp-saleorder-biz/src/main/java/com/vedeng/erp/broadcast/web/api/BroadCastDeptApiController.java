package com.vedeng.erp.broadcast.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptFormDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptListDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptQueryDto;
import com.vedeng.erp.broadcast.service.BroadcastDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;

/**
 * 播报部门管理API控制器
 * 提供部门管理相关的RESTful接口
 */
@RestController
@RequestMapping("/broadcast/dept")
@Slf4j
public class BroadCastDeptApiController {

    @Autowired
    private BroadcastDeptService broadcastDeptService;

    /**
     * 人员管理页面
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/list")
    public ModelAndView list() {
        return new ModelAndView("receiptnotice/staff");
    }

    /**
     * 分页查询播报部门列表
     * 支持部门名称模糊查询、父级部门筛选和AED用户筛选
     * 
     * @param pageParam 分页查询参数，包含分页信息和查询条件
     * @return 分页部门列表，包含总数、页码等分页信息
     */
    @ResponseBody
    @RequestMapping("/getDeptListPage")
    @NoNeedAccessAuthorization
    public R<PageInfo<BroadCastDeptListDto>> getDeptListPage(@RequestBody PageParam<BroadCastDeptQueryDto> pageParam) {
        try {
            PageInfo<BroadCastDeptListDto> result = broadcastDeptService.getBroadcastDeptListPage(pageParam);
            return R.success(result);
        } catch (Exception e) {
            log.error("分页查询播报部门列表失败", e);
            return R.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增或编辑播报部门
     * 根据表单中的id字段判断操作类型：
     * - id为null或0: 新增操作
     * - id不为null且大于0: 编辑操作
     * 
     * @param formDto 播报部门表单数据
     * @return 操作结果
     */
    @ResponseBody
    @RequestMapping("/saveOrUpdate")
    public R<String> saveOrUpdate(@Valid @RequestBody BroadCastDeptFormDto formDto) {
        try {
            boolean isUpdate = formDto.getId() != null && formDto.getId() > 0;
            
            if (isUpdate) {
                // 编辑操作
                broadcastDeptService.updateBroadcastDept(formDto);
                return R.success("编辑成功");
            } else {
                // 新增操作
                broadcastDeptService.saveBroadcastDept(formDto);
                return R.success("新增成功");
            }
        } catch (Exception e) {
            String operation = (formDto.getId() != null && formDto.getId() > 0) ? "编辑" : "新增";
            log.error("{}播报部门失败", operation, e);
            return R.error(operation + "失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除播报部门
     * 根据ID逻辑删除播报部门或小组
     * 
     * @param id 播报部门ID
     * @return 操作结果
     */
    @ResponseBody
    @RequestMapping("/delete")
    public R<String> delete(@RequestParam("id") Integer id) {
        try {
            broadcastDeptService.deleteBroadcastDept(id);
            return R.success("删除成功");
        } catch (Exception e) {
            log.error("删除播报部门失败", e);
            return R.error("删除失败：" + e.getMessage());
        }
    }
}
