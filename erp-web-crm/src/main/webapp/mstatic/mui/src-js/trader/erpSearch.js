/* 客户名称 */

Vue.component('ui-erp-search', {
    template: `
        <!-- 客户名称弹层 -->
        <crm-slide-dialog ref="slideDialog" title="客户名称" @hidn="handlerCancel">
            <div class="trader-erp-panel">
                <div class="slide-dialog-input-wrap">
                    <ui-input
                        v-model="dialogInput" 
                        border
                        clear
                        :maxlength="maxlength"
                        @focus="handlerFocus"
                        @input.native="handleInput"
                        @change="handlerChange"
                        @compositionend.native="commentPress"
                    />
                </div>
                <div class="erp-search-list" @click.stop>
                    <template v-if="loading">
                        <div class="erp-loading">
                            <i class="vd-ui_icon icon-loading" ref="loading"></i>
                            <span>加载中...</span>
                        </div>
                    </template>
                    <template v-else-if="loadingFail">
                        <div class="erp-load-fail">
                            <i class="vd-ui_icon icon-error2"></i>
                            <span>加载失败</span>
                        </div>
                    </template>
                    <div class="search-related" v-else-if="axiosSearchKey">
                        <div class="related-list" v-if="relateList.length">
                            <div class="local-data">本地数据匹配</div>
                            <!-- 'disabled': needDisable && !item.belong && !item.share, -->
                            <div 
                                class="related-item" 
                                :class="{
                                    'active': item.traderId == chooseTrader.traderId
                                }"
                                v-for="(item, index) in relateList" :key="index"
                                @click.stop="changeErp(item)"
                            >
                                <div class="related-item-left">
                                    <span class="trader-select">
                                        <i v-if="item.traderId == chooseTrader.traderId" class="vd-ui_icon icon-radio3"></i>
                                        <i v-else class="vd-ui_icon icon-radio1"></i>
                                    </span>
                                    <!-- <p v-if="needDisable && (!item.belong && !item.share)">{{ item.traderName }}</p>
                                    <p v-else v-html="lightName(item)"></p> -->
                                    <p class="name text-line-1" v-html="lightName(item)"></p>
                                    <i class="vd-ui_icon icon-tianyancha icon" v-if="item.tycFlag == 'Y'"></i>
                                </div>
                                <div class="related-item-right text-line-1">{{ item.saleName }}</div>
                            </div>
                        </div>
                        <div class="erp-load-empty" v-else>
                            <img src="/mstatic/image/empty/search-empty.svg"/>
                            <p>抱歉，没有找到符合条件的结果，</p>
                            <p>建议您换个关键词</p>
                        </div>
                    </div>
                </div>
                <!-- slide-dialog-default-footer -->
                <div class="slide-dialog-default-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        traderId: {
            type: [String, Number],
        },
        tycFlag: String,

        // 是否启用天眼查
        needTyc: {
            type: Boolean,
            default: true
        },
        // 选项中 启用禁用
        needDisable: {
            type: Boolean,
            default: false
        },

        placeholder: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        maxlength: {
            type: [Number, String],
            default: 1000,
        },
        saleName: String,
    },
    data() {
        return {
            dialogInput: '',
            // 建档客户-基本信息
            chooseTrader: {
                traderId: '',
                tycFlag: 'N',
                saleName: '',
            },
            traderDetail: {}, // 建档客户-交易信息

            // search
            loading: false,
            loadingFail: false,
            axiosSearchKey: '', // 记录接口搜索词·高亮  有此值，代表已经触发过搜索
            relateList: [], // 搜索联想
            timer: null,
        };
    },
    computed: {
        // 高亮
        lightName () {
            return (item) => {
                let name = item.traderName;
                if (!this.axiosSearchKey) return name;
                const regExp = new RegExp(this.axiosSearchKey, 'g');
                name = name.replace(regExp, `<font color='#FF6600'>${this.axiosSearchKey}</font>`);

                if (item.share) {
                    name = '[分享] ' + name;
                }

                return name;
            }
        },
    },
    mounted() {
        // 有默认值 触发搜索
            // 搜索时 不清空原选中信息, 修改input时清空原选中信息
        // 无默认值 面板不展示内容， 触发搜索后展示内容
    },
    methods: {
        hide() {
            this.$refs.slideDialog.hide();
            this.dialogInput = '';
            this.axiosSearchKey = '';
            this.relateList = [];
            this.loading = false;
            this.loadingFail = false;
        },
        show (name, chooseTrader, traderDetail) {
            this.dialogInput = name;
            this.chooseTrader = chooseTrader;
            this.traderDetail = traderDetail;

            if (name) {
                this.getRelatelist(name);
            }
            this.$refs.slideDialog.show();
        },

        handlerChange () {
            console.log('change', this.dialogInput);
            let val = this.dialogInput.trim();
            this.handlerSearch(val);
        },
        handleInput (e) {
            if (e.inputType != 'insertCompositionText') {
                let val = e.target.value.trim();
                this.handlerSearch(val);
            }
        },
        // 中文结束
        commentPress(e) {
            let val = e.target.value.trim();
            this.handlerSearch(val);
        },
        handlerFocus () {
            this.handlerSearch(this.dialogInput);
        },
        handlerSearch (val) {
            val = val.trim();
            if (!val) {
                this.axiosSearchKey = '';
                this.relateList = [];
                this.loading = false;
                this.loadingFail = false;
                this.timer = null;
                this.clearCompany();
                return;
            };
            if (val == this.axiosSearchKey) return;

            this.timer && clearTimeout(this.timer);
            this.timer = setTimeout(()=> {
                this.clearCompany();
                this.getRelatelist(val);
            }, 300);
        },
        // 搜索联想
        getRelatelist (name) {
            this.loading = true;
            this.loadingFail = false;
            this.axiosSearchKey = name;

            this.$axios.post(`/crm/trader/m/queryTrader?name=${name}`).then(({data}) => {
                this.loading = false;

                if (data.success) {
                    this.relateList = data.data || [];
                } else {
                    this.loadingFail = true;
                }
            }).catch(err=> {
                this.loading = false;
                this.loadingFail = true;
            })
        },
        
        // 清空客户信息
        clearCompany () {
            this.chooseTrader = {
                traderId: '',
                tycFlag: 'N',
                saleName: '',
            }
            this.traderDetail = {};
        },
        // 选择已建档客户
        changeErp (item) {
            this.clearCompany(); // 清空原本选项
            this.chooseTrader = item;
        },

        // 确定
        async handlerConfirm () {
            let name = this.chooseTrader.traderName || this.dialogInput || '';
            if (!name) {
                this.$message({
                    type: 'error',
                    message: '请输入客户名称',
                })
                return;
            }

            // 交易信息
            if (this.chooseTrader.traderId) {
                await this.$axios.post(`/crm/trader/profile/queryTraderDetail?traderId=${this.chooseTrader.traderId}`)
                    .then(({data}) => {
                        if (data.success) {
                            this.traderDetail = data.data || {};
                        }
                    }).catch(err=> {});
            }

            // 确定时 如果修改了值，取输入框
            this.$emit('change', {
                traderName: name,
                chooseTrader: this.chooseTrader,
                traderDetail: this.traderDetail
            });

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.slideDialog.hide();
            this.hasChoose = false;
            this.dialogInput = '';
            this.axiosSearchKey = '';
            this.relateList = [];
            this.loading = false;
            this.loadingFail = false;
        },
    }
})