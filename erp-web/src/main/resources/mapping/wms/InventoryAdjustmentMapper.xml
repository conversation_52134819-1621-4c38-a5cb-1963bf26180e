<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.inventoryadjustment.dao.InventoryAdjustmentMapper">

    <select id="getInventoryAdjustmentVosListPage"
            resultType="com.wms.inventoryadjustment.model.vo.InventoryAdjustmentVo">
        SELECT
        INVENTORY_ADJUSTMENT_ID,
        INVENTORY_ADJUSTMENT_NO,
        TYPE,
        CREAT_TIME
        FROM
        T_WMS_INVENTORY_ADJUSTMENT
        WHERE 1 = 1
        <if test="inventoryAdjustmentSearchDto != null">
            <if test="inventoryAdjustmentSearchDto.inventoryAdjustmentNo != null and inventoryAdjustmentSearchDto.inventoryAdjustmentNo != ''">
                AND INVENTORY_ADJUSTMENT_NO LIKE
                CONCAT('%',#{inventoryAdjustmentSearchDto.inventoryAdjustmentNo,jdbcType=VARCHAR},'%' )
            </if>
            <if test="inventoryAdjustmentSearchDto.type != null and inventoryAdjustmentSearchDto.type != 0">
                AND TYPE = #{inventoryAdjustmentSearchDto.type,jdbcType=INTEGER}
            </if>
            <if test="inventoryAdjustmentSearchDto.startTime != null">
                AND CREAT_TIME <![CDATA[ >= ]]> #{inventoryAdjustmentSearchDto.startTime,jdbcType=BIGINT}
            </if>
            <if test="inventoryAdjustmentSearchDto.endTime != null">
                AND CREAT_TIME <![CDATA[ <= ]]> #{inventoryAdjustmentSearchDto.endTime,jdbcType=BIGINT}
            </if>
        </if>
        ORDER BY
        ADD_TIME DESC
    </select>

    <select id="getInventoryAdjustmentById"
            resultType="com.wms.inventoryadjustment.model.vo.InventoryAdjustmentVo">
        SELECT
            INVENTORY_ADJUSTMENT_ID,
            INVENTORY_ADJUSTMENT_NO,
            TYPE,
            WAREHOUSE_ID,
            CUSTOMER,
            REASON,
            CREAT_TIME
        FROM
            T_WMS_INVENTORY_ADJUSTMENT
        WHERE
            INVENTORY_ADJUSTMENT_ID = #{inventoryAdjustmentId,jdbcType=INTEGER}
    </select>

    <select id="getInventoryAdjustmentDetailById"
            resultType="com.wms.inventoryadjustment.model.vo.InventoryAdjustmentDetailVo">
        SELECT
            INVENTORY_ADJUSTMENT_DETAIL_ID,
            INVENTORY_ADJUSTMENT_ID,
            SKU_NO,
            GOODS_NAME,
            NUM,
            PRICE,
            TOTAL_PRICE,
            ORDER_NO,
            LOGICAL_WAREHOUSE_ID,
            PRODUCTION_TIME,
            VALID_UNTIL_TIME,
            WAREHOUSING_TIME,
            MANUFACTURER_BATCH_NO,
            STERILIZATION_BATCH_NO,
            REGISTRATION_NUMBER
        FROM
            T_WMS_INVENTORY_ADJUSTMENT_DETAIL
        WHERE
             INVENTORY_ADJUSTMENT_ID = #{inventoryAdjustmentId,jdbcType=INTEGER}
    </select>

    <select id="getInventoryAdjustmentBaseInfo"
            resultType="com.wms.inventoryadjustment.model.po.InventoryAdjustmentPo">
         SELECT
            INVENTORY_ADJUSTMENT_ID,
            INVENTORY_ADJUSTMENT_NO,
            TYPE,
            STATUS,
            WAREHOUSE_ID,
            CUSTOMER
        FROM
            T_WMS_INVENTORY_ADJUSTMENT
        WHERE
            INVENTORY_ADJUSTMENT_ID = #{inventoryAdjustmentId,jdbcType=INTEGER}
    </select>

    <select id="getExpressByDetailInfo" resultType="com.vedeng.logistics.model.Express">
           SELECT
                T1.*
            FROM
                T_EXPRESS T1
                LEFT JOIN T_EXPRESS_DETAIL T2 ON T1.EXPRESS_ID = T2.EXPRESS_ID
                AND T1.IS_ENABLE = 1
                INNER JOIN T_SALEORDER_GOODS T3 ON T2.RELATED_ID = T3.SALEORDER_GOODS_ID
                AND T3.IS_DELETE = 0
                LEFT JOIN T_SALEORDER T4 ON T3.SALEORDER_ID = T4.SALEORDER_ID
            WHERE
                T2.BUSINESS_TYPE = 496
                AND T3.SKU =  #{data.skuNo}
                AND T2.NUM =  #{data.num}
                AND T4.SALEORDER_NO = #{orderNo}
    </select>

    <insert id="insertInventoryAdjustment" parameterType="com.wms.inventoryadjustment.model.po.InventoryAdjustmentPo"
            useGeneratedKeys="true" keyProperty="inventoryAdjustmentId">
        INSERT INTO
        T_WMS_INVENTORY_ADJUSTMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryAdjustmentId != null">
                INVENTORY_ADJUSTMENT_ID,
            </if>
            <if test="inventoryAdjustmentNo != null">
                INVENTORY_ADJUSTMENT_NO,
            </if>
            <if test="type != null">
                TYPE,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="warehouseId != null">
                WAREHOUSE_ID,
            </if>
            <if test="customer != null">
                CUSTOMER,
            </if>
            <if test="reason != null">
                REASON,
            </if>
            <if test="creatTime != null">
                CREAT_TIME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryAdjustmentId != null">
                #{inventoryAdjustmentId,jdbcType=INTEGER},
            </if>
            <if test="inventoryAdjustmentNo != null">
                #{inventoryAdjustmentNo,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="warehouseId != null">
                #{warehouseId,jdbcType=INTEGER},
            </if>
            <if test="customer != null">
                #{customer,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="creatTime != null">
                #{creatTime,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER}
            </if>
        </trim>
    </insert>

    <insert id="insertInventoryAdjustmentDetail"
            parameterType="com.wms.inventoryadjustment.model.po.InventoryAdjustmentDetailPo" useGeneratedKeys="true"
            keyProperty="inventoryAdjustmentDetailId">
        INSERT INTO
        T_WMS_INVENTORY_ADJUSTMENT_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryAdjustmentDetailId != null">
                INVENTORY_ADJUSTMENT_DETAIL_ID,
            </if>
            <if test="inventoryAdjustmentId != null">
                INVENTORY_ADJUSTMENT_ID,
            </if>
            <if test="adjustmentNo != null">
                ADJUSTMENT_NO,
            </if>
            <if test="skuNo != null">
                SKU_NO,
            </if>
            <if test="goodsName != null">
                GOODS_NAME,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="price != null">
                PRICE,
            </if>
            <if test="totalPrice != null">
                TOTAL_PRICE,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="logicalWarehouseId != null">
                LOGICAL_WAREHOUSE_ID,
            </if>
            <if test="productionTime != null">
                PRODUCTION_TIME,
            </if>
            <if test="validUntilTime != null">
                VALID_UNTIL_TIME,
            </if>
            <if test="warehousingTime != null">
                WAREHOUSING_TIME,
            </if>
            <if test="manufacturerBatchNo != null">
                MANUFACTURER_BATCH_NO,
            </if>
            <if test="sterilizationBatchNo != null">
                STERILIZATION_BATCH_NO,
            </if>
            <if test="registrationNumberId != null">
                REGISTRATION_NUMBER_ID,
            </if>
            <if test="registrationNumber != null">
                REGISTRATION_NUMBER,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="vedengBatchNumer != null">
                VEDENG_BATCH_NUMER,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryAdjustmentDetailId != null">
                #{inventoryAdjustmentDetailId,jdbcType=INTEGER},
            </if>
            <if test="inventoryAdjustmentId != null">
                #{inventoryAdjustmentId,jdbcType=INTEGER},
            </if>
            <if test="adjustmentNo != null">
                #{adjustmentNo,jdbcType=VARCHAR},
            </if>
            <if test="skuNo != null">
                #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="totalPrice != null">
                #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="logicalWarehouseId != null">
                #{logicalWarehouseId,jdbcType=INTEGER},
            </if>
            <if test="productionTime != null">
                #{productionTime,jdbcType=BIGINT},
            </if>
            <if test="validUntilTime != null">
                #{validUntilTime,jdbcType=BIGINT},
            </if>
            <if test="warehousingTime != null">
                #{warehousingTime,jdbcType=BIGINT},
            </if>
            <if test="manufacturerBatchNo != null">
                #{manufacturerBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="sterilizationBatchNo != null">
                #{sterilizationBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="registrationNumberId != null">
                #{registrationNumberId,jdbcType=INTEGER},
            </if>
            <if test="registrationNumber != null">
                #{registrationNumber,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="vedengBatchNumer != null">
                #{vedengBatchNumer,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>