package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.domain.entity.SyncDataErpEntity;
import com.vedeng.erp.system.mapper.SyncDataErpMapper;
import com.vedeng.erp.system.service.SyncDataErpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SyncDataErpServiceImpl implements SyncDataErpService {

    @Autowired
    private SyncDataErpMapper syncDataErpMapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return syncDataErpMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SyncDataErpEntity record) {
        return syncDataErpMapper.insert(record);
    }

    @Override
    public int insertSelective(SyncDataErpEntity record) {
        return syncDataErpMapper.insertSelective(record);
    }

    @Override
    public SyncDataErpEntity selectByPrimaryKey(Integer id) {
        return syncDataErpMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(SyncDataErpEntity record) {
        return syncDataErpMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(SyncDataErpEntity record) {
        return syncDataErpMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<SyncDataErpEntity> selectByProcessStatus(Integer processStatus,String businessType) {
        return syncDataErpMapper.selectByProcessStatus(processStatus,businessType);
    }

    @Override
    public List<SyncDataErpEntity> selectByBusinessNo(String businessNo) {
        return syncDataErpMapper.selectByBusinessNo(businessNo);
    }
} 