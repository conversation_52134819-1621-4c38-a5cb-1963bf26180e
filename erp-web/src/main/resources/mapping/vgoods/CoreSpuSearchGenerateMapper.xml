<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreSpuSearchGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreSpuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <id column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="CATEGORY_ID" jdbcType="INTEGER" property="categoryId" />
    <result column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
    <result column="SPU_NO" jdbcType="VARCHAR" property="spuNo" />
    <result column="SPU_NAME" jdbcType="VARCHAR" property="spuName" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="SPU_LEVEL" jdbcType="TINYINT" property="spuLevel" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="SPU_TYPE" jdbcType="INTEGER" property="spuType" />
    <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="REGISTRATION_ICON" jdbcType="VARCHAR" property="registrationIcon" />
    <result column="WIKI_HREF" jdbcType="VARCHAR" property="wikiHref" />
    <result column="OPERATE_INFO_FLAG" jdbcType="TINYINT" property="operateInfoFlag" />
    <result column="CHECK_STATUS" jdbcType="TINYINT" property="checkStatus" />
    <result column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
    <result column="HOSPITAL_TAGS" jdbcType="VARCHAR" property="hospitalTags" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="CHECKER" jdbcType="INTEGER" property="checker" />
    <result column="DELETE_REASON" jdbcType="VARCHAR" property="deleteReason" />
    <result column="LAST_CHECK_REASON" jdbcType="VARCHAR" property="lastCheckReason" />
    <result column="ASSIGNMENT_MANAGER_ID" jdbcType="INTEGER" property="assignmentManagerId" />
    <result column="ASSIGNMENT_ASSISTANT_ID" jdbcType="INTEGER" property="assignmentAssistantId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    SPU_ID, CATEGORY_ID, BRAND_ID, SPU_NO, SPU_NAME, SHOW_NAME, SPU_LEVEL, `STATUS`, 
    SPU_TYPE, FIRST_ENGAGE_ID, REGISTRATION_ICON, WIKI_HREF, OPERATE_INFO_FLAG, CHECK_STATUS, 
    OPERATE_INFO_ID, HOSPITAL_TAGS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, CHECK_TIME, 
    CHECKER, DELETE_REASON, LAST_CHECK_REASON, ASSIGNMENT_MANAGER_ID, ASSIGNMENT_ASSISTANT_ID
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.CoreSpuSearchGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from V_CORE_SPU_SEARCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from V_CORE_SPU_SEARCH
    where SPU_ID = #{spuId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from V_CORE_SPU_SEARCH
    where SPU_ID = #{spuId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.CoreSpuSearchGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from V_CORE_SPU_SEARCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.CoreSpuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="spuId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_CORE_SPU_SEARCH (CATEGORY_ID, BRAND_ID, SPU_NO, 
      SPU_NAME, SHOW_NAME, SPU_LEVEL, 
      `STATUS`, SPU_TYPE, FIRST_ENGAGE_ID, 
      REGISTRATION_ICON, WIKI_HREF, OPERATE_INFO_FLAG, 
      CHECK_STATUS, OPERATE_INFO_ID, HOSPITAL_TAGS, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, CHECK_TIME, CHECKER, 
      DELETE_REASON, LAST_CHECK_REASON, ASSIGNMENT_MANAGER_ID, 
      ASSIGNMENT_ASSISTANT_ID)
    values (#{categoryId,jdbcType=INTEGER}, #{brandId,jdbcType=INTEGER}, #{spuNo,jdbcType=VARCHAR}, 
      #{spuName,jdbcType=VARCHAR}, #{showName,jdbcType=VARCHAR}, #{spuLevel,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{spuType,jdbcType=INTEGER}, #{firstEngageId,jdbcType=INTEGER}, 
      #{registrationIcon,jdbcType=VARCHAR}, #{wikiHref,jdbcType=VARCHAR}, #{operateInfoFlag,jdbcType=TINYINT}, 
      #{checkStatus,jdbcType=TINYINT}, #{operateInfoId,jdbcType=INTEGER}, #{hospitalTags,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, #{checker,jdbcType=INTEGER}, 
      #{deleteReason,jdbcType=VARCHAR}, #{lastCheckReason,jdbcType=VARCHAR}, #{assignmentManagerId,jdbcType=INTEGER}, 
      #{assignmentAssistantId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.CoreSpuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="spuId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_CORE_SPU_SEARCH
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        CATEGORY_ID,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
      <if test="spuNo != null">
        SPU_NO,
      </if>
      <if test="spuName != null">
        SPU_NAME,
      </if>
      <if test="showName != null">
        SHOW_NAME,
      </if>
      <if test="spuLevel != null">
        SPU_LEVEL,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="spuType != null">
        SPU_TYPE,
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID,
      </if>
      <if test="registrationIcon != null">
        REGISTRATION_ICON,
      </if>
      <if test="wikiHref != null">
        WIKI_HREF,
      </if>
      <if test="operateInfoFlag != null">
        OPERATE_INFO_FLAG,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID,
      </if>
      <if test="hospitalTags != null">
        HOSPITAL_TAGS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="checkTime != null">
        CHECK_TIME,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="deleteReason != null">
        DELETE_REASON,
      </if>
      <if test="lastCheckReason != null">
        LAST_CHECK_REASON,
      </if>
      <if test="assignmentManagerId != null">
        ASSIGNMENT_MANAGER_ID,
      </if>
      <if test="assignmentAssistantId != null">
        ASSIGNMENT_ASSISTANT_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="spuNo != null">
        #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null">
        #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="spuLevel != null">
        #{spuLevel,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="spuType != null">
        #{spuType,jdbcType=INTEGER},
      </if>
      <if test="firstEngageId != null">
        #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="registrationIcon != null">
        #{registrationIcon,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null">
        #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="operateInfoFlag != null">
        #{operateInfoFlag,jdbcType=TINYINT},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="operateInfoId != null">
        #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="hospitalTags != null">
        #{hospitalTags,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null">
        #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null">
        #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="assignmentManagerId != null">
        #{assignmentManagerId,jdbcType=INTEGER},
      </if>
      <if test="assignmentAssistantId != null">
        #{assignmentAssistantId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.CoreSpuSearchGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select count(*) from V_CORE_SPU_SEARCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_CORE_SPU_SEARCH
    <set>
      <if test="record.spuId != null">
        SPU_ID = #{record.spuId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null">
        CATEGORY_ID = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.brandId != null">
        BRAND_ID = #{record.brandId,jdbcType=INTEGER},
      </if>
      <if test="record.spuNo != null">
        SPU_NO = #{record.spuNo,jdbcType=VARCHAR},
      </if>
      <if test="record.spuName != null">
        SPU_NAME = #{record.spuName,jdbcType=VARCHAR},
      </if>
      <if test="record.showName != null">
        SHOW_NAME = #{record.showName,jdbcType=VARCHAR},
      </if>
      <if test="record.spuLevel != null">
        SPU_LEVEL = #{record.spuLevel,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.spuType != null">
        SPU_TYPE = #{record.spuType,jdbcType=INTEGER},
      </if>
      <if test="record.firstEngageId != null">
        FIRST_ENGAGE_ID = #{record.firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="record.registrationIcon != null">
        REGISTRATION_ICON = #{record.registrationIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.wikiHref != null">
        WIKI_HREF = #{record.wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="record.operateInfoFlag != null">
        OPERATE_INFO_FLAG = #{record.operateInfoFlag,jdbcType=TINYINT},
      </if>
      <if test="record.checkStatus != null">
        CHECK_STATUS = #{record.checkStatus,jdbcType=TINYINT},
      </if>
      <if test="record.operateInfoId != null">
        OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="record.hospitalTags != null">
        HOSPITAL_TAGS = #{record.hospitalTags,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.checkTime != null">
        CHECK_TIME = #{record.checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checker != null">
        CHECKER = #{record.checker,jdbcType=INTEGER},
      </if>
      <if test="record.deleteReason != null">
        DELETE_REASON = #{record.deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="record.lastCheckReason != null">
        LAST_CHECK_REASON = #{record.lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="record.assignmentManagerId != null">
        ASSIGNMENT_MANAGER_ID = #{record.assignmentManagerId,jdbcType=INTEGER},
      </if>
      <if test="record.assignmentAssistantId != null">
        ASSIGNMENT_ASSISTANT_ID = #{record.assignmentAssistantId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_CORE_SPU_SEARCH
    set SPU_ID = #{record.spuId,jdbcType=INTEGER},
      CATEGORY_ID = #{record.categoryId,jdbcType=INTEGER},
      BRAND_ID = #{record.brandId,jdbcType=INTEGER},
      SPU_NO = #{record.spuNo,jdbcType=VARCHAR},
      SPU_NAME = #{record.spuName,jdbcType=VARCHAR},
      SHOW_NAME = #{record.showName,jdbcType=VARCHAR},
      SPU_LEVEL = #{record.spuLevel,jdbcType=TINYINT},
      `STATUS` = #{record.status,jdbcType=TINYINT},
      SPU_TYPE = #{record.spuType,jdbcType=INTEGER},
      FIRST_ENGAGE_ID = #{record.firstEngageId,jdbcType=INTEGER},
      REGISTRATION_ICON = #{record.registrationIcon,jdbcType=VARCHAR},
      WIKI_HREF = #{record.wikiHref,jdbcType=VARCHAR},
      OPERATE_INFO_FLAG = #{record.operateInfoFlag,jdbcType=TINYINT},
      CHECK_STATUS = #{record.checkStatus,jdbcType=TINYINT},
      OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
      HOSPITAL_TAGS = #{record.hospitalTags,jdbcType=VARCHAR},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      CHECK_TIME = #{record.checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{record.checker,jdbcType=INTEGER},
      DELETE_REASON = #{record.deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{record.lastCheckReason,jdbcType=VARCHAR},
      ASSIGNMENT_MANAGER_ID = #{record.assignmentManagerId,jdbcType=INTEGER},
      ASSIGNMENT_ASSISTANT_ID = #{record.assignmentAssistantId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.CoreSpuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_CORE_SPU_SEARCH
    <set>
      <if test="categoryId != null">
        CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        BRAND_ID = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="spuNo != null">
        SPU_NO = #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null">
        SPU_NAME = #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="spuLevel != null">
        SPU_LEVEL = #{spuLevel,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="spuType != null">
        SPU_TYPE = #{spuType,jdbcType=INTEGER},
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="registrationIcon != null">
        REGISTRATION_ICON = #{registrationIcon,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null">
        WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="operateInfoFlag != null">
        OPERATE_INFO_FLAG = #{operateInfoFlag,jdbcType=TINYINT},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="hospitalTags != null">
        HOSPITAL_TAGS = #{hospitalTags,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null">
        DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null">
        LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="assignmentManagerId != null">
        ASSIGNMENT_MANAGER_ID = #{assignmentManagerId,jdbcType=INTEGER},
      </if>
      <if test="assignmentAssistantId != null">
        ASSIGNMENT_ASSISTANT_ID = #{assignmentAssistantId,jdbcType=INTEGER},
      </if>
    </set>
    where SPU_ID = #{spuId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.CoreSpuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_CORE_SPU_SEARCH
    set CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      BRAND_ID = #{brandId,jdbcType=INTEGER},
      SPU_NO = #{spuNo,jdbcType=VARCHAR},
      SPU_NAME = #{spuName,jdbcType=VARCHAR},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      SPU_LEVEL = #{spuLevel,jdbcType=TINYINT},
      `STATUS` = #{status,jdbcType=TINYINT},
      SPU_TYPE = #{spuType,jdbcType=INTEGER},
      FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      REGISTRATION_ICON = #{registrationIcon,jdbcType=VARCHAR},
      WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      OPERATE_INFO_FLAG = #{operateInfoFlag,jdbcType=TINYINT},
      CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      HOSPITAL_TAGS = #{hospitalTags,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{checker,jdbcType=INTEGER},
      DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      ASSIGNMENT_MANAGER_ID = #{assignmentManagerId,jdbcType=INTEGER},
      ASSIGNMENT_ASSISTANT_ID = #{assignmentAssistantId,jdbcType=INTEGER}
    where SPU_ID = #{spuId,jdbcType=INTEGER}
  </update>
</mapper>