<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>采购售后发票冲销审核列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="invoiceReversalList" datasource="erp-datasourcetarget" append_column_url="" append_row_url="" empty_show="-" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">发票号：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入发票号" oper="like" name="INVOICE_NO" empty_show="-" style="" alias="A.INVOICE_NO">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">票种：</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="INVOICE_TYPE" placeholder="请输入" style="" alias="A.INVOICE_TYPE"
                    jdbctype="" data="SELECT SYS_OPTION_DEFINITION_ID K,TITLE V FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 428 AND STATUS = 1"
                    datatype="KVSQLCACHE" oper="in" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">冲销审核状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="STATUS" placeholder="全部" style="" alias="A.REVERSAL_AUDIT_STATUS" jdbctype=""
                        data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;}]"
                        datatype="json" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">冲销申请人：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入申请人" oper="like" name="CREATOR_NAME" empty_show="-" style="" alias="A.CREATOR_NAME">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">所属采购售后单号：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入采购售后单号" oper="like" name="REVERSAL_BILL_NO" empty_show="-" style="" alias="">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">开票方名称：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入开票方名称" oper="like" name="SALE_NAME" empty_show="-" style="" alias="">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">申请时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="请选择日期"
                        style="" alias="A.ADD_TIME"
                        jdbctype="DATETIME" data="" datatype="" oper="BETWEEN">
                </object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">业务类型</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="REVERSAL_BILL_TYPE" placeholder="全部" style="" alias="A.REVERSAL_BILL_TYPE" jdbctype=""
                        data="[{&quot;V&quot;:&quot;采购售后单&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;采购售后费用单&quot;,&quot;K&quot;:&quot;2&quot;}]"
                        datatype="json" oper=""></select>
            </div>
        </div>
    </form>
</div>
<hr class="layui-border-blue">
<table id="table" class="layui-table" style=" width:100%">
    <thead>
    <tr id="column">
        <th item_name="INVOICE_NO" name="INVOICE_NO" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">发票号</th>
        <th item_name="AMOUNT" name="AMOUNT" body="td-text" url="" opentype="PARENT" windowname="" datatype="" data="" style="min-width:200px;word-break: break-all;position: sticky;" head="18">冲销总额</th>
        <th item_name="REVERSAL_BILL_NO" name="REVERSAL_BILL_NO" body="td-link"
            url="${URL}" opentype="PARENT"
            windowname="查看售后单" datatype="" data="" style="min-width:200px;word-break: break-all;position: sticky;" head="18">所属采购售后单号</th>
        <th item_name="INVOICE_TYPE" name="INVOICE_TYPE" body="td-select" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE"
            data="SELECT SYS_OPTION_DEFINITION_ID K,TITLE V FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 428 AND STATUS = 1" style="width:110px" head="th">票种</th>
        <th item_name="SALE_NAME" name="SALE_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">开票方名称</th>
        <th item_name="INVOICE_COLOR" name="INVOICE_COLOR" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">红蓝字</th>
        <th item_name="APPLY_TIME" name="APPLY_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">申请时间</th>
        <th item_name="CREATOR_NAME" name="CREATOR_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">冲销申请人</th>
        <th item_name="STATUS" name="STATUS" type="td-select"td opentype="MODEL" windowname="" datatype="json" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">审核状态</th>
        <th item_name="REVERSAL_AUDIT_USERNAME" name="REVERSAL_AUDIT_USERNAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">审核人员</th>
        <th type="rowbutton" id="rowbutton">
            <button class="layui-btn list-row-button" opentype="MODEL" type="group"
                    url="/after/newBuyorder/verifyCoverApply.do?invoiceReversalId=${INVOICE_REVERSAL_ID}&isPass=true" windowname="操作确认" area="400px,300px"
                    name="update">审核通过
            </button>
            <button class="layui-btn list-row-button" opentype="MODEL" type="group"
                    url="/after/newBuyorder/verifyCoverApply.do?invoiceReversalId=${INVOICE_REVERSAL_ID}&isPass=false" windowname="操作确认" area="400px,300px"
                    name="update">审核不通过
            </button>
        </th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td  colspan=100>
            express:
                <pre id="express" class="layui-code" orderby="ORDER BY A.ADD_TIME DESC" groupby="GROUP BY A.INVOICE_REVERSAL_ID,A.INVOICE_NO,A.ADD_TIME">
                    SELECT
                        A.INVOICE_REVERSAL_ID,A.INVOICE_NO,A.REVERSAL_BILL_TYPE,A.INVOICE_TYPE,A.REVERSAL_BILL_NO,
                        A.SALE_NAME,'蓝字有效' AS INVOICE_COLOR,A.AUDIT_APPLY_TIME,
                        A.CREATOR_NAME,
                        (CASE WHEN A.REVERSAL_AUDIT_STATUS = 0 THEN '待审核' WHEN A.REVERSAL_AUDIT_STATUS = 1 THEN '审核通过' WHEN A.REVERSAL_AUDIT_STATUS = 2 THEN '审核不通过' END) AS STATUS,
                        B.USERNAME AS REVERSAL_AUDIT_USERNAME,
                        A.ADD_TIME,DATE_FORMAT(A.ADD_TIME,'%Y-%m-%d %H:%i:%s') AS APPLY_TIME,(CASE WHEN A.REVERSAL_BILL_TYPE = 1 THEN SUM(C.AMOUNT) ELSE SUM(D.AMOUNT) END) AS AMOUNT,
                        A.INVOICE_ID,(CASE WHEN A.REVERSAL_BILL_TYPE = 1 THEN F.AFTER_SALES_ID ELSE G.EXPENSE_AFTER_SALES_ID END) AS ORDER_ID,
                        (CASE WHEN A.REVERSAL_BILL_TYPE = 1 THEN CONCAT('/order/newBuyorder/viewAfterSalesDetail.do?traderType=2&afterSalesId=',F.AFTER_SALES_ID)
                        ELSE CONCAT('/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=',G.EXPENSE_AFTER_SALES_ID)END) AS URL
                    FROM T_INVOICE_REVERSAL A
                    LEFT JOIN T_USER B ON A.REVERSAL_AUDIT_USER = B.USER_ID
                    LEFT JOIN T_AFTER_SALES F ON F.AFTER_SALES_NO = A.REVERSAL_BILL_NO
                    LEFT JOIN T_INVOICE C ON C.INVOICE_NO = A.INVOICE_NO AND C.RELATED_ID = F.ORDER_ID  AND C.VALID_STATUS = 1
                    LEFT JOIN T_EXPENSE_AFTER_SALES G ON G.EXPENSE_AFTER_SALES_NO = A.REVERSAL_BILL_NO
                    LEFT JOIN T_INVOICE D ON D.INVOICE_NO = A.INVOICE_NO AND D.RELATED_ID = G.BUYORDER_EXPENSE_ID
                    WHERE A.IS_DELETE = 0
                </pre>
            <pre id="count" class="layui-code"></pre>
        </td>
    </tr>
    </tbody>
</table>
<div id="appendFoot"></div>
</body>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</html>
