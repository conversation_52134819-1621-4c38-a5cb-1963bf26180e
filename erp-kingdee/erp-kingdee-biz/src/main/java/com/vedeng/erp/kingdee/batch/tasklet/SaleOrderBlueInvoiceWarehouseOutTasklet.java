package com.vedeng.erp.kingdee.batch.tasklet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.tasklet.BaseTasklet;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.handle.InvoiceWarehouseOutInHandle;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 销售蓝票和出入库关系绑定
 * <AUTHOR>
 */
@Service
@Slf4j
public class SaleOrderBlueInvoiceWarehouseOutTasklet extends BaseTasklet {
    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Autowired
    private InvoiceWarehouseOutInHandle invoiceWarehouseOutInHandle;

    @Override
    public void doExec(Map<String, Object> jobParameters) throws Exception {
        log.info("销售蓝票和出库关系绑定开始");
        // 此处直接取蓝票的reader的条件
        String beginTime = (String) jobParameters.get("beginTime");
        String endTime = (String) jobParameters.get("endTime");
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(505)
                ._pagesize(Integer.MAX_VALUE)
                ._skiprows(0)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        List<BatchInvoiceDto> batchInvoiceDtoList = batchInvoiceDtoMapper.findSaleOrderBlueInvoiceBatch(batchInvoiceDto);
        if (CollUtil.isNotEmpty(batchInvoiceDtoList)) {
            invoiceWarehouseOutInHandle.bindSaleOrderBlueInvoiceAndWarehouseOutRelation(batchInvoiceDtoList);
        }
    }
}
