package com.vedeng.erp.buyorder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.eventbus.EventBus;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SettlementTypeEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.listenerEvent.*;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity;
import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDetailExcelDto;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorder.mapper.BuyorderMapper;
import com.vedeng.erp.buyorder.mapper.BuyorderRebateChargeApplyMapper;
import com.vedeng.erp.buyorder.mapstruct.BuyOrderRebateChargeApplyConvertor;
import com.vedeng.erp.buyorder.service.BuyOrderRebateChargeApplyService;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseItemMapper;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseMapper;
import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.service.SettlementBillApiService;
import com.vedeng.goods.dto.BrandFrontDto;
import com.vedeng.goods.service.BrandApiService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22 17:00
 */
@Service
@Slf4j
public class BuyOrderRebateChargeApplyServiceImpl implements BuyOrderRebateChargeApplyService {

    @Autowired
    private BuyorderRebateChargeApplyMapper buyorderRebateChargeApplyMapper;

    @Autowired
    private BuyOrderRebateChargeApplyConvertor buyOrderRebateChargeApplyConvertor;

    @Autowired
    private AttachmentMapper attachmentMapper;

    private static final String REBATE_CHARGE_VERIFY = "rebateChargeVerify";

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    @Qualifier(value = "newBuyorderMapper")
    private BuyorderMapper buyorderMapper;

    @Autowired
    private BuyorderExpenseMapper buyorderExpenseMapper;

    @Autowired
    private BuyorderExpenseItemMapper buyorderExpenseItemMapper;

    @Autowired
    private SettlementBillApiService settlementBillApiService;

    @Autowired
    private BrandApiService brandApiService;

    @Value("${oss_http}")
    private String ossHttp;

    private static final String BUY_ORDER_ORDER = "采购订单";

    private static final String BUY_ORDER_ORDER_EXPENSE = "采购费用单";

    private static final Integer MAX_LINE = 5000;

    @Override
    public PageInfo<BuyOrderRebateChargeApplyDto> page(PageParam<BuyOrderRebateChargeApplyDto> rebateChargeApplyDtoPageParam) {
        PageInfo<BuyOrderRebateChargeApplyDto> pageInfo = PageHelper.startPage(rebateChargeApplyDtoPageParam).doSelectPageInfo(() -> buyorderRebateChargeApplyMapper.findByAll(rebateChargeApplyDtoPageParam.getParam()));
        // 处理附件及审核人信息
        pageInfo.getList().forEach(this::getAttachmentAndCheckInfo);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Integer add(BuyOrderRebateChargeApplyDto rebateChargeApplyDto) {
        log.info("新增返利结算申请：{}", JSONObject.toJSONString(rebateChargeApplyDto));
        BuyOrderRebateChargeApplyEntity rebateChargeApplyEntity = buyOrderRebateChargeApplyConvertor.toEntity(rebateChargeApplyDto);
        // 生成订单号
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.BUY_ORDER_REBATE_CHARGE_APPLY);
        String buyOrderRebateChargeNo = new BillNumGenerator().distribution(billGeneratorBean);
        rebateChargeApplyEntity.setBuyOrderRebateChargeNo(buyOrderRebateChargeNo);
        buyorderRebateChargeApplyMapper.insertSelective(rebateChargeApplyEntity);
        Integer buyOrderRebateChargeId = rebateChargeApplyEntity.getBuyOrderRebateChargeId();
        // 保存附件信息
        List<Integer> billUrlIds = rebateChargeApplyDto.getBillFileList()
                .stream()
                .peek(item -> item.setRelatedId(buyOrderRebateChargeId))
                .peek(attachmentMapper::insertSelective)
                .map(Attachment::getAttachmentId)
                .collect(Collectors.toList());
        List<Integer> detailUrlIds = rebateChargeApplyDto.getDetailFileList()
                .stream()
                .peek(item -> item.setRelatedId(buyOrderRebateChargeId))
                .peek(attachmentMapper::insertSelective)
                .map(Attachment::getAttachmentId)
                .collect(Collectors.toList());
        rebateChargeApplyEntity.setDetailUrlIds(detailUrlIds.stream().map(Object::toString).collect(Collectors.joining(",")));
        rebateChargeApplyEntity.setBillUrlIds(billUrlIds.stream().map(Object::toString).collect(Collectors.joining(",")));
        buyorderRebateChargeApplyMapper.updateByPrimaryKeySelective(rebateChargeApplyEntity);

        // 开启审核流
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        this.startRebateChargeProcess(buyOrderRebateChargeId, buyOrderRebateChargeNo, currentUser);
        return buyOrderRebateChargeId;
    }

    @Override
    public BuyOrderRebateChargeApplyDto getDetail(Integer buyOrderRebateChargeId) {
        BuyOrderRebateChargeApplyDto detail = buyorderRebateChargeApplyMapper.findById(buyOrderRebateChargeId);
        // 查询附件及审核人信息
        this.getAttachmentAndCheckInfo(detail);
        // 查询审核记录
        AuditRecordEvent auditRecordEvent = new AuditRecordEvent();
        auditRecordEvent.setBusinessKey(REBATE_CHARGE_VERIFY + "_" + buyOrderRebateChargeId);
        eventBusCenter.post(auditRecordEvent);
        detail.setAuditRecordEventResults(auditRecordEvent.getAuditRecordEventResults());
        detail.setTaskId(auditRecordEvent.getTaskId());
        detail.setShowCheckButton(auditRecordEvent.getIsAuditUser());
        return detail;
    }

    @Override
    public void startRebateChargeProcess(Integer rebateChargeId, String buyOrderRebateChargeNo, CurrentUser currentUser) {
        log.info("发起结算申请单审核流====开始,结算单ID:{}", rebateChargeId);
        //初始化工作流变量
        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put("processDefinitionKey", REBATE_CHARGE_VERIFY);
        variableMap.put("businessKey", REBATE_CHARGE_VERIFY + "_" + rebateChargeId);
        variableMap.put("currentAssinee", currentUser.getUsername());
        variableMap.put("buyOrderRebateChargeId", rebateChargeId);
        variableMap.put("buyOrderRebateChargeNo", buyOrderRebateChargeNo);
        variableMap.put("startUser", currentUser.getUsername());
        //EventBus调用old模块 开启流程实例
        CreateActivityEvent createActivityEvent = new CreateActivityEvent();
        createActivityEvent.setProcessDefinitionKey(REBATE_CHARGE_VERIFY);
        createActivityEvent.setBusinessKey(REBATE_CHARGE_VERIFY + "_" + rebateChargeId);
        createActivityEvent.setVariables(variableMap);
        eventBusCenter.post(createActivityEvent);
        // 默认申请人通过
        HistoricActivityEvent historicActivityEvent = new HistoricActivityEvent();
        historicActivityEvent.setProcessEngine(processEngine);
        historicActivityEvent.setBusinessKey(REBATE_CHARGE_VERIFY + "_" + rebateChargeId);
        eventBusCenter.post(historicActivityEvent);
        Map<String, Object> historicInfo = historicActivityEvent.getHistoricInfo();
        if (historicInfo.get("endStatus") != "审核完成") {
            Task taskInfo = (Task) historicInfo.get("taskInfo");
            Authentication.setAuthenticatedUserId(currentUser.getUsername());
            // 默认审批通过
            ComplementTaskEvent complementTaskEvent = new ComplementTaskEvent();
            complementTaskEvent.setTaskId(taskInfo.getId());
            complementTaskEvent.setAssignee(currentUser.getUsername());
            complementTaskEvent.setComment("");
            complementTaskEvent.setVariables(new HashMap<>());
            eventBusCenter.post(complementTaskEvent);
            //修改返利申请单审核状态为审核中
            buyorderRebateChargeApplyMapper.updateStatus(rebateChargeId, FinanceConstant.ZERO);
        }
        log.info("发起结算申请单审核流====结束,结算单ID:{}", rebateChargeId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void complementRebateChargeProcess(Integer rebateChargeId, Boolean pass, String comment, String taskId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("结算申请单审核节点,rebateChargeId:{},pass:{},comment:{},审核人:{}", rebateChargeId, pass, comment, currentUser.getUsername());
        // 2.审核通过或驳回
        if (StrUtil.isBlank(comment)) {
            comment = pass ? "审核通过" : "审核不通过";
        }
        // 审批操作
        String businessKey = "rebateChargeVerify_" + rebateChargeId;
        Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
        if (Objects.isNull(taskInfo)) {
            log.error("流程已结束，请刷新");
        }
        if (StrUtil.isBlank(comment)) {
            comment = "";
        }
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        variables.put("updater", currentUser.getId());
        variables.put("autoCheckAptitude", false);
        ComplementTaskEvent complementTaskEvent = new ComplementTaskEvent();
        complementTaskEvent.setTaskId(taskId);
        complementTaskEvent.setComment(comment);
        complementTaskEvent.setAssignee(currentUser.getUsername());
        complementTaskEvent.setVariables(variables);
        eventBusCenter.post(complementTaskEvent);

        if (Objects.isNull(complementTaskEvent.getData())) {
            throw new ServiceException("当前单据状态发生改变，请刷新后重试");
        }

        //完成审核节点
        if ("endEvent".equals(complementTaskEvent.getData().toString())) {
            log.info("结算申请单审核结束,rebateChargeId:{}", rebateChargeId);
            if (pass) {
                buyorderRebateChargeApplyMapper.updateStatus(rebateChargeId, FinanceConstant.ONE);
                //生成结算单并结算
                SettlementBillCreateCmd cmd = new SettlementBillCreateCmd(BusinessSourceTypeEnum.buyOrderRebateChargeApply,
                        rebateChargeId,
                        Collections.singletonList(SettlementTypeEnum.REBATE_APPLY));
                settlementBillApiService.createAlsoSettlement(cmd);
            } else {
                buyorderRebateChargeApplyMapper.updateStatus(rebateChargeId, FinanceConstant.TWO);
            }
            //审核完成通知提交人
            BuyOrderRebateChargeApplyEntity applyEntity = buyorderRebateChargeApplyMapper.selectByPrimaryKey(rebateChargeId);
            Map<String, String> map = new HashMap<>();
            map.put("buyOrderRebateChargeNo", applyEntity.getBuyOrderRebateChargeNo());
            String url = "./buyOrder/rebateChargeApply/detail.do?buyOrderRebateChargeId=" + rebateChargeId;
            MessageUtil.sendMessage(pass ? 266 : 267, Collections.singletonList(applyEntity.getCreator()), map, url);
        }
    }

    /**
     * 查询 采购返利结算收款申请 的附件信息即审核人信息
     *
     * @param buyOrderRebateChargeApplyDto BuyOrderRebateChargeApplyDto
     */
    private void getAttachmentAndCheckInfo(BuyOrderRebateChargeApplyDto buyOrderRebateChargeApplyDto) {
        if (StringUtils.isNotBlank(buyOrderRebateChargeApplyDto.getBillUrlIds())) {
            List<Long> idList = Arrays.stream(buyOrderRebateChargeApplyDto.getBillUrlIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Attachment> attachmentList = attachmentMapper.selectAllByIds(idList);
            attachmentList.forEach(item -> item.setWholeUrl(ossHttp + item.getDomain() + item.getUri()));
            buyOrderRebateChargeApplyDto.setBillFileList(attachmentList);
        }
        if (StringUtils.isNotBlank(buyOrderRebateChargeApplyDto.getDetailUrlIds())) {
            List<Long> idList = Arrays.stream(buyOrderRebateChargeApplyDto.getDetailUrlIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<Attachment> attachmentList = attachmentMapper.selectAllByIds(idList);
            attachmentList.forEach(item -> item.setWholeUrl(ossHttp + item.getDomain() + item.getUri()));
            buyOrderRebateChargeApplyDto.setDetailFileList(attachmentList);
        }

        // 判断当前用户是否能看到审核按钮
        if (ErpConstant.WAIT_AUDIT.equals(buyOrderRebateChargeApplyDto.getAuditStatus())) {
            CheckAuditUserEvent checkAuditUserEvent = new CheckAuditUserEvent();
            checkAuditUserEvent.setBusinessKey(REBATE_CHARGE_VERIFY + "_" + buyOrderRebateChargeApplyDto.getBuyOrderRebateChargeId());
            eventBusCenter.post(checkAuditUserEvent);
            buyOrderRebateChargeApplyDto.setShowCheckButton(checkAuditUserEvent.getIsAuditUser());
            buyOrderRebateChargeApplyDto.setTaskId(checkAuditUserEvent.getTaskId());
        }

        // 品牌信息
        if (StringUtils.isNotBlank(buyOrderRebateChargeApplyDto.getBrandIds())) {
            List<BrandFrontDto> brandList = brandApiService.getBrand(Arrays.stream(buyOrderRebateChargeApplyDto.getBrandIds().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            buyOrderRebateChargeApplyDto.setBrandName(brandList.stream().map(BrandFrontDto::getBrandName).collect(Collectors.joining(",")));
        }
    }

    @Override
    public FileInfo checkAndUploadDetailFile(HttpServletRequest request, MultipartFile lwfile) {
        try {
            // step1 先校验明细excel是否满足规则
            List<String> checkResultList = this.checkDetailFile(lwfile);
            if (CollectionUtils.isEmpty(checkResultList)) {
                // step2 校验通过, 上传oss
                return ossUtilsService.upload2Oss(request, lwfile);
            } else {
                return new FileInfo(-2, String.join(";", checkResultList));
            }
        } catch (Exception e) {
            log.error("文件上传失败：", e);
            return new FileInfo(-1, "文件上传失败");
        }
    }

    /**
     * 读取excel的行，并校验数据是否合规
     *
     * @param lwfile MultipartFile
     * @return Boolean
     */
    private List<String> checkDetailFile(MultipartFile lwfile) throws IOException {
        List<BuyOrderRebateChargeApplyDetailExcelDto> applyDetail = new ArrayList<>();
        List<String> checkResultList = new ArrayList<>();
        EasyExcel.read(lwfile.getInputStream(), BuyOrderRebateChargeApplyDetailExcelDto.class, new PageReadListener<BuyOrderRebateChargeApplyDetailExcelDto>(applyDetail::addAll)).sheet().doRead();
        if (CollectionUtils.isNotEmpty(applyDetail)) {
            if (applyDetail.size() > MAX_LINE) {
                checkResultList.add("明细附件不可超过5000行");
                return checkResultList;
            }

            for (int i = 0; i < applyDetail.size(); i++) {
                // 第一行是标题，所以0+2
                int index = i + 2;
                // 最多只校验出15行异常，超过直接抛出
                if (checkResultList.size() >= 15) {
                    break;
                }
                BuyOrderRebateChargeApplyDetailExcelDto detailExcelDto = applyDetail.get(i);
                // 校验单据类型
                if (!BUY_ORDER_ORDER.equals(detailExcelDto.getOrderType()) && !BUY_ORDER_ORDER_EXPENSE.equals(detailExcelDto.getOrderType())) {
                    checkResultList.add("第" + index + "行明细<单据类型>有误");
                    continue;
                }
                // 校验订单号是否有效
                if (BUY_ORDER_ORDER.equals(detailExcelDto.getOrderType())) {
                    BuyOrderDto buyOrderDto = buyorderMapper.queryInfoByNo(detailExcelDto.getOrderNo());
                    if (Objects.isNull(buyOrderDto)) {
                        checkResultList.add("第" + index + "行明细<订单号>有误");
                        continue;
                    } else {
                        List<Integer> orderGoods = buyorderMapper.getByBuyOrderIdAndSkuNo(buyOrderDto.getBuyorderId(), detailExcelDto.getSku());
                        if (CollectionUtils.isEmpty(orderGoods)) {
                            checkResultList.add("第" + index + "行明细<SKU>有误");
                            continue;
                        }
                    }
                }
                if (BUY_ORDER_ORDER_EXPENSE.equals(detailExcelDto.getOrderType())) {
                    BuyorderExpenseEntity buyOrderExpense = buyorderExpenseMapper.getBuyorerExpenseByOrderNo(detailExcelDto.getOrderNo());
                    if (Objects.isNull(buyOrderExpense)) {
                        checkResultList.add("第" + index + "行明细<订单号>有误");
                    } else {
                        // 校验SKU
                        List<BuyorderExpenseItemDto> expenseItemDtoList = buyorderExpenseItemMapper.getByExpenseIdAndSkuNo(buyOrderExpense.getBuyorderExpenseId(), detailExcelDto.getSku());
                        if (CollectionUtils.isEmpty(expenseItemDtoList)) {
                            checkResultList.add("第" + index + "行明细<SKU>有误");
                        }
                    }
                }
            }
        } else {
            // 内容行为空，认定为校验失败
            checkResultList.add("当前明细文件内容为空");
        }
        return checkResultList;
    }
}