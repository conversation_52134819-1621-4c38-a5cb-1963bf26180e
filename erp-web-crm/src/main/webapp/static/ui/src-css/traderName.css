.vd-ui-trader-wrap {
  display: flex;
  align-items: flex-start;
}
.vd-ui-trader-wrap .vd-ui-trader-txt.vd-ui-trader-link {
  color: #09f;
  cursor: pointer;
}
.vd-ui-trader-wrap .vd-ui-trader-txt.vd-ui-trader-link:hover {
  color: #f60;
}
.vd-ui-trader-wrap .vd-ui_icon {
  font-size: 16px;
  margin-top: -3px;
  margin-left: 10px;
  cursor: pointer;
}
.vd-ui-trader-wrap .vd-ui_icon:hover {
  color: #f60;
}
.vd-ui-trader-wrap .vd-ui-trader-tyc {
  color: #0084FF;
}
.vd-ui-trader-wrap .vd-ui-trader-baidu {
  color: #2932E1;
}
.vd-ui-trader-wrap.txt-flex .vd-ui-trader-txt {
  flex: 1;
}
.vd-ui-trader-grade {
  width: 20px;
  height: 20px;
  background-size: 100% 100%;
}
.vd-ui-trader-grade.lv-S {
  background-image: url('/static/image/traderLevel/S.svg');
}
.vd-ui-trader-grade.lv-S1 {
  background-image: url('/static/image/traderLevel/S1.svg');
}
.vd-ui-trader-grade.lv-S2 {
  background-image: url('/static/image/traderLevel/S2.svg');
}
.vd-ui-trader-grade.lv-A1 {
  background-image: url('/static/image/traderLevel/A1.svg');
}
.vd-ui-trader-grade.lv-A2 {
  background-image: url('/static/image/traderLevel/A2.svg');
}
.vd-ui-trader-grade.lv-A3 {
  background-image: url('/static/image/traderLevel/A3.svg');
}
.vd-ui-trader-grade.lv-B1 {
  background-image: url('/static/image/traderLevel/B1.svg');
}
.vd-ui-trader-grade.lv-B2 {
  background-image: url('/static/image/traderLevel/B2.svg');
}
.vd-ui-trader-grade.lv-B3 {
  background-image: url('/static/image/traderLevel/B3.svg');
}
.vd-ui-trader-grade.lv-C1 {
  background-image: url('/static/image/traderLevel/C1.svg');
}
.vd-ui-trader-grade.lv-C2 {
  background-image: url('/static/image/traderLevel/C2.svg');
}
.vd-ui-trader-grade.lv-C3 {
  background-image: url('/static/image/traderLevel/C3.svg');
}
.vd-ui-trader-grade.lv-D {
  background-image: url('/static/image/traderLevel/D.svg');
}
.vd-ui-call-wrap {
  display: flex;
  align-items: center;
}
.vd-ui-call-wrap .vd-ui-call-cnt .icon-call2 {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  vertical-align: -2px;
  color: #666;
}
.vd-ui-call-wrap .vd-ui-call-cnt.link {
  color: #09f;
  cursor: pointer;
}
.vd-ui-call-wrap .vd-ui-call-cnt.link .icon-call2 {
  color: #09f;
}
.vd-ui-call-wrap .vd-ui-call-cnt.link:hover {
  color: #f60;
}
.vd-ui-call-wrap .vd-ui-call-cnt.link:hover .icon-call2 {
  color: #f60;
}
.vd-ui-call-wrap .ui-tel-tip {
  display: flex;
  align-items: center;
  margin-left: 10px;
}
.vd-ui-call-wrap .ui-tel-tip .vd-ui_icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
}
.vd-ui-call-wrap .ui-tel-tip.warn {
  color: #f60;
}
.vd-ui-call-wrap .ui-tel-tip.success {
  color: #13BF13;
}
