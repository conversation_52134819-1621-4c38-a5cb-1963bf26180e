Vue.component('ui-user', {
    template: `<div class="vd-ui-user-wrap">
        <template v-if="name">
            <img class="vd-ui-user-avatar" :src="img" alt="" @error="imgError">
            <div class="vd-ui-user-name">{{ name }}</div>
            <ui-tip icon="custom" position="r" v-if="status !== -1 && status != 0">
                <template v-slot:icon>
                    <img class="vd-ui-user-off-icon" src="/static/image/common/user-off-icon.svg"></img>
                </template>
                已离职
            </ui-tip>
        </template>
        <template v-else>-</template>
    </div>`,
    props: {
        avatar: {
            type: String,
            default: ''
        },
        name: {
            type: String,
            default: ''
        },
        status: {
            type: [String, Number],
            default: -1
        }
    },
    watch: {
        avatar() {
            this.img = this.avatar || this.defaultImg;
        }
    },
    data() {
        return {
            img: '',
            defaultImg: '/static/image/crm-user-avatar.svg',
            isError: false
        };
    },
    computed: {

    },
    mounted() {
        this.img = this.avatar || this.defaultImg;
    },
    methods: {
        imgError() {
            this.img = this.defaultImg;
        }
    }
})

Vue.component('ui-department', {
    template: `<div>
            <template v-if="departments && departments.length">
            <template v-if="departments.length === 1">
                <div class="text-line-1" :title="departments[0].departmentName">{{ departments[0].departmentName }}</div>
            </template>
            <ui-block-tip icon="custom" position="rt" v-else>
                <div class="ui-depart-wrap">
                    <div class="ui-depart-txt text-line-1" :title="departments[0].departmentName">{{ departments[0].departmentName }}</div>
                    <div class="ui-depart-more">+{{departments.length - 1}}</div>
                </div>
                <template v-slot:tip>
                    <template v-for="(item, index) in departments">
                        <div v-if="index > 0">{{ item.departmentName }}</div>
                    </template>
                </template>
            </ui-block-tip>
        </template>
        <template v-else>-</template>
    </div>`,
    props: {
        departments: {
            type: Array,
            default() {
                return []
            }
        },
    },
    watch: {
        
    },
    data() {
        return {
            
        };
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
       
    }
})

Vue.component('ui-category', {
    template: `<div>
        <template v-if="list && list.length">
            <template v-if="list.length === 1">
                <div class="text-line-1" :title="list[0]">{{ list[0] }}</div>
            </template>
            <ui-block-tip icon="custom" position="rt" v-else>
                <div class="ui-depart-wrap">
                    <div class="ui-depart-txt text-line-1" :title="list[0]">{{ list[0] }}</div>
                    <div class="ui-depart-more">+{{list.length - 1}}</div>
                </div>
                <template v-slot:tip>
                    <template v-for="(item, index) in list">
                        <div v-if="index > 0">{{ item }}</div>
                    </template>
                </template>
            </ui-block-tip>
        </template>
        <template v-else>-</template>
    </div>`,
    props: {
        list: {
            type: Array,
            default() {
                return []
            }
        },
    },
    data() {
        return {
            
        };
    },
    mounted() {
    }
})