package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.enums.AfterSalesProcessEnum;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCapitalBillDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayVedengBankDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillSrcEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillSrcEntryLinkDto;
import com.vedeng.erp.kingdee.enums.KingDeeBaseEnums;
import com.vedeng.erp.kingdee.enums.KingDeePayBillSettleTypeEnum;
import com.vedeng.erp.kingdee.enums.KingDeePayBillTypeEnums;
import com.vedeng.erp.kingdee.enums.KingDeePayBillUseTypeEnums;
import com.vedeng.erp.kingdee.repository.KingDeePayVedengBankRepository;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PaymentWechatProcessor.java
 * @Description TODO
 * @createTime 2023年06月26日 10:12:00
 */
@Service
@Slf4j
public class PaymentWechatProcessor implements ItemProcessor<BatchBankBillDto, KingDeePayBillDto> {

    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper afterSalesMapper;

    @Resource
    private KingDeePayVedengBankRepository kingDeePayVedengBankRepository;

    @Override
    public KingDeePayBillDto process(BatchBankBillDto batchBankBillDto) throws Exception {
        log.info("金蝶处理微信付款流水推送{},", JSON.toJSONString(batchBankBillDto));
        Integer orderType = batchBankBillDto.getOrderType();
        KingDeePayBillDto kingDeePayBillDto ;
        KingDeePayBillEntryDto kingDeePayBillEntryDto = new KingDeePayBillEntryDto();
         if (KingDeeConstant.THREE.equals(orderType)) {
            //销售售后单申请付款
            BatchAfterSalesDto afterSales = afterSalesMapper.findByAfterSalesNo(batchBankBillDto.getOrderNo());

            kingDeePayBillEntryDto.setFQzokYsddh(afterSales.getOrderNo());

            kingDeePayBillEntryDto.setFQzokGsywdh(afterSales.getAfterSalesNo());
            //VDERP-12445【功能】【erp】安调维修付款申请，金蝶系统付款对象取值修改为ERP付款申请内支付对象start
            if (AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(afterSales.getType())
                    || AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(afterSales.getType())
                    || AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(afterSales.getType())
                    || AfterSalesProcessEnum.AFTERSALES_WX.getCode().equals(afterSales.getType())
                    || AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSales.getType())
                    || AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSales.getType())) {
                //安调维修付款给供应商（工程师）--付款类型为采购
                kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, KingDeeConstant.ONE);
                List<BatchAfterSalesDto> afterSalesDtos = afterSalesMapper.getAfterTraderSuplier(afterSales.getAfterSalesNo());
                if (CollectionUtils.isNotEmpty(afterSalesDtos)) {
                    kingDeePayBillDto.setTraderSupplierId(execute(afterSalesDtos));
                    // 业务类型--售后工程师付款
                    kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.SALE_AFTER_ENGINEER_PAY.getName());
                } else {
                    log.error("安调维修售后单{},查询供应商信息为空,", afterSales.getAfterSalesNo());
                }
                kingDeePayBillDto.setFBillTypeId(KingDeePayBillTypeEnums.BUYORDER_PAY.getCode());

                kingDeePayBillEntryDto.setFpurposeid(KingDeePayBillUseTypeEnums.BUY_PAY.getCode());
            } else {
                kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, KingDeeConstant.TWO);
                BatchAfterSalesDto afterSalesDto = afterSalesMapper.findSaleByAfterSalesNoAndSubjectType(afterSales.getAfterSalesNo(), afterSales.getSubjectType());
                //业务类型--销售售后退货/退款付款
                if (AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSales.getType())) {
                    kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.SALE_AFTER_BACK_GOOD_PAY.getName());
                } else if (AfterSalesProcessEnum.AFTERSALES_TK.getCode().equals(afterSales.getType())) {
                    kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.SALE_AFTER_BACK_REFUND_PAY.getName());
                } else if (AfterSalesProcessEnum.THIRD_AMOUNT_RETURN.getCode().equals(afterSales.getType())) {
                    kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.THIRD_AFTER_BACK_REFUND_PAY.getName());
                } else {
                    log.error("销售售后单{},类型为{},非付款业务类型", afterSales.getAfterSalesId(), afterSales.getType());
                }
                kingDeePayBillDto.setTraderCustomerId(afterSalesDto.getTraderCustomerId());
                kingDeePayBillDto.setFBillTypeId(KingDeePayBillTypeEnums.OTHER_PAY.getCode());

                kingDeePayBillEntryDto.setFpurposeid(KingDeePayBillUseTypeEnums.OTHER_PAY.getCode());
            }
            //VDERP-12445【功能】【erp】安调维修付款申请，金蝶系统付款对象取值修改为ERP付款申请内支付对象end
            kingDeePayBillDto.setFQzokCgddh(afterSales.getAfterSalesNo());
            kingDeePayBillEntryDto.setFcomment(afterSales.getAfterSalesNo());
        }else {
            log.info("历史付款流水推送{},未忽略无已匹配金额，无需推送金蝶",JSON.toJSONString(batchBankBillDto));
            return null;
        }
        kingDeePayBillDto.setErpBankBillId(batchBankBillDto.getBankBillId());

        kingDeePayBillDto.setKingDeeBizEnums(KingDeeBizEnums.savePayBill);

        kingDeePayBillDto.setFBillNo(batchBankBillDto.getTranFlow());

        kingDeePayBillDto.setFDate(DateUtil.formatDate(batchBankBillDto.getTranDate()));

        kingDeePayBillDto.setFQzokJylx("支出");

        kingDeePayBillDto.setFQzokLsh(batchBankBillDto.getTranFlow());

//        kingDeePayBillDto.setFRemark(batchBankBillDto.getIgnoreReason());

        if (KingDeeConstant.ONE.equals(batchBankBillDto.getTraderSubject())) {
            kingDeePayBillDto.setFQzokJyzt("对公");
            kingDeePayBillEntryDto.setFRecType(KingDeeConstant.ZERO.toString());
            kingDeePayBillEntryDto.setFRuZhangType(KingDeeConstant.ONE.toString());
        } else if (KingDeeConstant.TWO.equals(batchBankBillDto.getTraderSubject())) {
            kingDeePayBillDto.setFQzokJyzt("对私");
            kingDeePayBillEntryDto.setFRecType(KingDeeConstant.ONE.toString());
            kingDeePayBillEntryDto.setFRuZhangType(KingDeeConstant.ZERO.toString());
        }

        List<KingDeePayBillEntryDto> kingDeePayBillEntryDtoList = new ArrayList<>();

        //付款方式为银行的付款申请单，推送金蝶
        kingDeePayBillEntryDto.setFsettletypeid(KingDeePayBillSettleTypeEnum.WECHAT_SETTLETYPE.getCode());

        kingDeePayBillEntryDto.setFcostid("");

        kingDeePayBillEntryDto.setFexpensedeptidE("");

        // 我方银行账号
        KingDeePayVedengBankDto kingDeePayVedengBankDto = queryBankInfo(batchBankBillDto.getBankTag());
        if(kingDeePayVedengBankDto == null){
            log.error("流水未查询到我方银行信息{},",batchBankBillDto.getBankTag());
            return null;
        }
        kingDeePayBillEntryDto.setFaccountid(kingDeePayVedengBankDto.getPayBankNo());

        kingDeePayBillEntryDto.setFsettleno("");
        kingDeePayBillEntryDto.setFpostdate(kingDeePayBillDto.getFDate());

        List<BatchCapitalBillDto> capitalBillDtoList = batchCapitalBillDtoMapper.queryCapitalBillByTranFlow(batchBankBillDto.getTranFlow());
        for (BatchCapitalBillDto batchCapitalBillDto : capitalBillDtoList) {
            KingDeePayBillEntryDto kingDeePayBillEntryDto1 = new KingDeePayBillEntryDto();
            BeanUtils.copyProperties(kingDeePayBillEntryDto,kingDeePayBillEntryDto1);
            //收款单位联行号
            kingDeePayBillEntryDto1.setFCnaps(batchBankBillDto.getAccBankNo());
            kingDeePayBillEntryDto1.setFOppositeBankAccount(batchBankBillDto.getAccno2());
            //收款单位开户行
            kingDeePayBillEntryDto1.setFOppositeBankName(batchBankBillDto.getCadBankNm());
            kingDeePayBillEntryDto1.setFOppositeCcountName(batchBankBillDto.getAccName1());
            kingDeePayBillEntryDto1.setFpaytotalamountfor(batchCapitalBillDto.getAmount());

            kingDeePayBillEntryDtoList.add(kingDeePayBillEntryDto1);
        }

        kingDeePayBillDto.setFPayBillEntry(kingDeePayBillEntryDtoList);

        KingDeePayBillSrcEntryDto kingDeePayBillSrcEntryDto = new KingDeePayBillSrcEntryDto();
        KingDeePayBillSrcEntryLinkDto kingDeePayBillSrcEntryLinkDto = new KingDeePayBillSrcEntryLinkDto();
        List<KingDeePayBillSrcEntryLinkDto> kingDeePayBillSrcEntryLinkDtoList = new ArrayList<>();
        kingDeePayBillSrcEntryLinkDtoList.add(kingDeePayBillSrcEntryLinkDto);
        kingDeePayBillSrcEntryDto.setFpaybillsrcentryLink(kingDeePayBillSrcEntryLinkDtoList);

        List<KingDeePayBillSrcEntryDto> kingDeePayBillSrcEntryDtoList = new ArrayList<>();
        kingDeePayBillSrcEntryDtoList.add(kingDeePayBillSrcEntryDto);
        kingDeePayBillDto.setFPayBillSrcEntry(kingDeePayBillSrcEntryDtoList);


        return kingDeePayBillDto;
    }

    private Integer execute(List<BatchAfterSalesDto> afterSalesDtos) {
        Integer trderSuplierId = null;
        if(CollectionUtils.isNotEmpty(afterSalesDtos)){
            for (BatchAfterSalesDto afterSalesDto : afterSalesDtos) {
                if (afterSalesDto.getTraderSupplierId() != null && trderSuplierId == null){
                    trderSuplierId = afterSalesDto.getTraderSupplierId();
                }
            }
            if(trderSuplierId == null){
                trderSuplierId = afterSalesDtos.get(0).getTraderId();
            }
            return trderSuplierId;
        }
        return null;
    }

    /**
     * 根据流水类型查询银行信息
     * @param bankType
     * @return
     */
    private KingDeePayVedengBankDto queryBankInfo(Integer bankType){
        KingDeePayVedengBankDto kingDeePayVedengBankDto ;
        switch (bankType){
            case 1:
                //建设银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SIX);
                break;
            case 2:
                //南京银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.TWO);
                break;
            case 3:
                //中国银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.ONE);
                break;
            case 4:
                //支付宝
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FOUR);
                break;
            case 5:
                //微信
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FIVE);
                break;
            case 6:
                //交通银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.THREE);
                break;
            default:
                log.info("银行流水类型未查对应银行编码");
                return null;
        }
        return kingDeePayVedengBankDto;
    }
}
