package com.vedeng.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.newtask.TodoFinishTask;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.StringUtil;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.model.SimpleMedicalCategory;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.model.Goods;
import com.vedeng.order.chain.RiskEnum;
import com.vedeng.order.chain.RiskHandlerKeyConstant;
import com.vedeng.order.chain.RiskHandlerStep;
import com.vedeng.order.chain.factory.RiskStepBuildFactory;
import com.vedeng.order.chain.model.BuyorderRiskModelVo;
import com.vedeng.order.chain.model.RiskModel;
import com.vedeng.order.chain.model.SaleorderRiskModelVo;
import com.vedeng.order.chain.model.SkuRiskModelVo;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.system.service.UserService;
import com.vedeng.todolist.constant.RiskCheckTodoListBuzPropertyEnum;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.RiskCheckLogMapper;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.RiskCheckLogService;
import com.vedeng.todolist.service.TodoListInstanceFactory;
import com.vedeng.todolist.service.TodoListService;
import com.vedeng.trader.dao.SupplierRegistrationNumberMapper;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.vo.TraderVo;
import com.wms.service.chain.HandlerStepContext;
import org.apache.commons.collections.CollectionUtils;
import org.jaxen.util.SingletonList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName RiskCheckServiceImpl.java
 * @Description TODO 风控业务处理
 * @createTime 2020年12月12日 10:53:00
 */
@Service("riskCheckService")
public class RiskCheckServiceImpl extends BaseServiceimpl implements RiskCheckService {

    private Logger logger = LoggerFactory.getLogger(RiskCheckServiceImpl.class);

    @Autowired
    private RiskStepBuildFactory riskStepBuildFactory;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    private TodoListInstanceFactory todoListInstanceFactory;

    @Resource
    private GoodsMapper goodsMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private TodoListMapper todoListMapper;

    @Transactional
    @Override
    public SaleorderRiskModelVo riskCheckSaleOrder(Saleorder order) {

        Saleorder saleOrder = saleorderMapper.getSaleOrderById(order.getSaleorderId());

        List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(order.getSaleorderId());
        SaleorderRiskModelVo saleorderRiskModelVo = new SaleorderRiskModelVo();
        HandlerStepContext context = new HandlerStepContext();
        //是否存在医疗器械
        boolean checkFlag = false;

        for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
            SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(saleorderGoods.getSku());
            if(simpleMedicalCategory != null && simpleMedicalCategory.getRegistrationNumberId() != null){
                checkFlag = true;
                break;
            }
        }

        if(!checkFlag){
            //不存在医疗器械
            saleorderRiskModelVo.setIsRisk(true);
            logger.info("riskCheckSaleOrder 不存在医疗器械 no:{}",saleOrder.getSaleorderNo());
            return saleorderRiskModelVo;
        }
        try {
            //客户校验
            RiskHandlerStep traderRiskCheck = riskStepBuildFactory.traderRiskCheck();

            Integer traderId = saleOrder.getTraderId();
            String saleorderNo = saleOrder.getSaleorderNo();


            context.put(RiskHandlerKeyConstant.TRADER_ID,traderId);
            RiskModel riskmodel = traderRiskCheck.dealWith(context);

            //商品校验
            context.put(RiskHandlerKeyConstant.RISK_MODEL,riskmodel);
            RiskHandlerStep skuRiskCheck = riskStepBuildFactory.skuRiskCheck();

            //商品质量待办
            ITodoInstance skuTodo = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA);


            List<String> riskSkuList = new ArrayList<>();

            for (SaleorderGoods saleorderGoods : saleorderGoodsList) {

                SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(saleorderGoods.getSku());
                if(simpleMedicalCategory == null || simpleMedicalCategory.getRegistrationNumberId() == null){
                    continue;
                }

                context.put(RiskHandlerKeyConstant.SKU,saleorderGoods.getSku());
                RiskModel skuRiskmodel = skuRiskCheck.dealWith(context);

                Integer goodsId = saleorderGoods.getGoodsId();
                //生成商品待办
                if(!skuRiskmodel.getIsOneChek()){
                    skuTodo.add(goodsId,saleorderNo,RiskEnum.SKU_SPU_FIRST_RISK.getRiskMessage(), RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_SALEORDER.getValue());
                    riskSkuList.add(saleorderGoods.getSku());
                }

            }

            if(!riskmodel.getIsRisk()){
                Set<RiskEnum> riskEnumList = riskmodel.getRiskEnumList();
                StringBuilder riskComments = new StringBuilder();
                //商品风控信息
                if(riskEnumList.contains(RiskEnum.SKU_SPU_FIRST_RISK)){
                    riskComments.append(RiskEnum.SKU_SPU_FIRST_RISK.getRiskMessage()+"</br>");
                }
                //客户资质风控
                if(riskEnumList.contains(RiskEnum.TRADER_QA_RISK)){
                    riskComments.append(RiskEnum.TRADER_QA_RISK.getRiskMessage()+"</br>");

                    //客户资质待办
                    ITodoInstance traderCertificate = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE);
                    traderCertificate.add(traderId,saleorderNo,RiskEnum.TRADER_QA_RISK.getRiskMessage(),RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_SALEORDER.getValue());
                }
                //客户信息风控
                if(riskEnumList.contains(RiskEnum.TRADER_RISK)){
                    riskComments.append(RiskEnum.TRADER_RISK.getRiskMessage()+"</br>");

                    //客户信息待办
                    ITodoInstance traderData = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_DATA);
                    traderData.add(traderId,saleorderNo,RiskEnum.TRADER_RISK.getRiskMessage(),RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_SALEORDER.getValue());
                }

                if(!saleOrder.getIsRisk().equals(RiskHandlerKeyConstant.SALE_RISK) &&
                        !saleOrder.getIsRisk().equals(RiskHandlerKeyConstant.FINISH_RISK)){
                    Saleorder update = new Saleorder();
                    update.setSaleorderId(saleOrder.getSaleorderId());
                    update.setRiskComments(riskComments.toString());
                    update.setIsRisk(RiskHandlerKeyConstant.SALE_RISK);
                    update.setRiskTime(System.currentTimeMillis());
                    saleorderMapper.updateByPrimaryKeySelective(update);
                }

            }else{
                if(saleOrder.getRiskTime() == null || saleOrder.getRiskTime().equals(0L)){
                    Saleorder update = new Saleorder();
                    update.setSaleorderId(saleOrder.getSaleorderId());
                    update.setRiskTime(System.currentTimeMillis());
                    saleorderMapper.updateByPrimaryKeySelective(update);
                }
                if(saleOrder.getIsRisk().equals(RiskHandlerKeyConstant.QUOTE_RISK)){
                    Saleorder update = new Saleorder();
                    update.setSaleorderId(saleOrder.getSaleorderId());
                    update.setIsRisk(RiskHandlerKeyConstant.DEFAULT_RISK);
                    update.setRiskTime(System.currentTimeMillis());
                    saleorderMapper.updateByPrimaryKeySelective(update);
                }
            }

            BeanUtils.copyProperties(riskmodel,saleorderRiskModelVo);
            saleorderRiskModelVo.setRiskSkuList(riskSkuList);
            logger.info("riskCheckSaleOrder 结果 no:{},result:{}",saleorderNo, JSON.toJSONString(saleorderRiskModelVo));
            return saleorderRiskModelVo;
        }catch (Exception e){
            logger.error("riskCheckSaleOrder error",e);
        }
        return saleorderRiskModelVo;
    }

    @Transactional
    @Override
    public BuyorderRiskModelVo riskCheckBuyOrder(Buyorder order) {

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(order.getBuyorderId());

        List<BuyorderGoodsVo> buyorderGoodsList = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(order.getBuyorderId());

        BuyorderRiskModelVo buyorderRiskModelVo = new BuyorderRiskModelVo();
        HandlerStepContext context = new HandlerStepContext();
        try {
            //是否存在医疗器械
            boolean checkFlag = false;

            boolean oneManageCateGoryLevel = true;
            //忽略校验字段
            HashMap<String,Boolean> ignoreFiledMap = new HashMap<>();

            for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsList) {
                SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(buyorderGoodsVo.getSku());
                if(!SysOptionConstant.ID_968.equals(simpleMedicalCategory.getManageCategory())){
                    oneManageCateGoryLevel = false;
                }
                if(simpleMedicalCategory != null && simpleMedicalCategory.getRegistrationNumberId() != null){
                    checkFlag = true;
                    break;
                }

            }

            if(!checkFlag){
                //不存在医疗器械
                buyorderRiskModelVo.setIsRisk(true);
                logger.info("riskCheckBuyOrder 不存在医疗器械 no:{}",buyorder.getBuyorderNo());
                return buyorderRiskModelVo;
            }

            if(oneManageCateGoryLevel){
                ignoreFiledMap.put("qualityAssuranceList",false);
                ignoreFiledMap.put("afterSalesBookList",false);
            }else {
                ignoreFiledMap.put("qualityAssuranceList",true);
                ignoreFiledMap.put("afterSalesBookList",true);
            }

            context.put(RiskHandlerKeyConstant.IGNORE,ignoreFiledMap);
            RiskHandlerStep supplierRiskCheck = riskStepBuildFactory.supplierRiskCheck();

            context.put(RiskHandlerKeyConstant.TRADER_ID,buyorder.getTraderId());

            //供应商校验
            RiskModel supplierRiskModel = supplierRiskCheck.dealWith(context);

            context.put(RiskHandlerKeyConstant.RISK_MODEL,supplierRiskModel);
            RiskHandlerStep skuRiskCheck = riskStepBuildFactory.skuRiskCheck();

            //商品质量待办
            ITodoInstance skuTodo = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA);

            List<String> riskSkuList = new ArrayList<>();
            for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsList) {
                SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(buyorderGoodsVo.getSku());
                if(simpleMedicalCategory == null || simpleMedicalCategory.getRegistrationNumberId() == null){
                    continue;
                }
                context.put(RiskHandlerKeyConstant.SKU,buyorderGoodsVo.getSku());
                RiskModel skuRiskmodel = skuRiskCheck.dealWith(context);

                if(!skuRiskmodel.getIsOneChek()){
                    //风控之SKU数据待办事项
                    skuTodo.add(buyorderGoodsVo.getGoodsId(),buyorder.getBuyorderNo(),RiskEnum.SKU_SPU_FIRST_RISK.getRiskMessage(),RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_BUYORDER.getValue());
                    riskSkuList.add(buyorderGoodsVo.getSku());
                }
            }
            if(!supplierRiskModel.getIsRisk()){
                Set<RiskEnum> riskEnumList = supplierRiskModel.getRiskEnumList();
                StringBuilder riskComments = new StringBuilder();
                if(riskEnumList.contains(RiskEnum.SKU_SPU_FIRST_RISK)){
                    riskComments.append(RiskEnum.SKU_SPU_FIRST_RISK.getRiskMessage()+"</br>");
                }
                if(riskEnumList.contains(RiskEnum.SUPPLIER_RISK)){
                    //风控之供应商数据待办事项
                    ITodoInstance todoInstance = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_TRADER_SUPPLY_DATA);
                    todoInstance.add(buyorder.getTraderId(),buyorder.getBuyorderNo(),RiskEnum.SUPPLIER_RISK.getRiskMessage(),RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_BUYORDER.getValue());
                    riskComments.append(RiskEnum.SUPPLIER_RISK.getRiskMessage()+"</br>");
                }

                if(!buyorder.getIsRisk().equals(RiskHandlerKeyConstant.SALE_RISK) &&
                        !buyorder.getIsRisk().equals(RiskHandlerKeyConstant.FINISH_RISK)){
                    Buyorder update = new Buyorder();
                    update.setIsRisk(RiskHandlerKeyConstant.SALE_RISK);
                    update.setRiskComments(riskComments.toString());
                    update.setBuyorderId(buyorder.getBuyorderId());
                    update.setRiskTime(System.currentTimeMillis());
                    buyorderMapper.updateByPrimaryKeySelective(update);
                }
            }else{
                if(buyorder.getRiskTime() == null || buyorder.getRiskTime().equals(0L)){
                    Buyorder update = new Buyorder();
                    update.setBuyorderId(buyorder.getBuyorderId());
                    update.setRiskTime(System.currentTimeMillis());
                    buyorderMapper.updateByPrimaryKeySelective(update);
                }
            }

            BeanUtils.copyProperties(supplierRiskModel,buyorderRiskModelVo);
            buyorderRiskModelVo.setSkuRiskList(riskSkuList);
            logger.info("riskCheckBuyOrder结果 no:{},result:{}",buyorder.getBuyorderNo(), JSON.toJSONString(buyorderRiskModelVo));
            return buyorderRiskModelVo;
        }catch (Exception e){
            logger.error("riskCheckBuyOrder error",e);
        }
        return buyorderRiskModelVo;
    }

    @Override
    public SkuRiskModelVo riskCheckSku(String sku) {
        SkuRiskModelVo skuRiskModelVo = new SkuRiskModelVo();

        SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(sku);

        if(simpleMedicalCategory == null || simpleMedicalCategory.getRegistrationNumberId() == null){
            skuRiskModelVo.setIsRisk(true);
            return skuRiskModelVo;
        }

        HandlerStepContext context = new HandlerStepContext();
        context.put(RiskHandlerKeyConstant.SKU,sku);
        //商品校验
        RiskHandlerStep skuRiskCheck = riskStepBuildFactory.skuRiskCheck();
        RiskModel skuRiskmodel = skuRiskCheck.dealWith(context);
        try {
            if(!skuRiskmodel.getIsRisk()){
                Set<String> userNameSet = new HashSet<>();
                Goods goods =  goodsMapper.getSkuInfoBySku(sku);
                userNameSet.add(goods.getManagerUserName());
                userNameSet.add(goods.getAssistantUserName());
                StringBuffer sb = new StringBuffer();
                for (String userName : userNameSet) {
                    sb.append(userName).append(" ");
                }

                String skuTitle = "商品质量风控信息不完整，请尽快提醒"+sb.toString()+"补充！否则会影响此订单流程！";
                skuRiskModelVo.setSkuTitle(skuTitle);
            }
            skuRiskModelVo.setIsCheck(skuRiskmodel.getIsCheck());
            skuRiskModelVo.setIsRisk(skuRiskmodel.getIsRisk());
            skuRiskModelVo.setRiskEnumList(skuRiskmodel.getRiskEnumList());
            logger.info("riskCheckSku结果 sku:{},result:{}",sku, JSON.toJSONString(skuRiskModelVo));
        }catch (Exception e){
            logger.error("riskCheckSku error",e);
        }
        return skuRiskModelVo;
    }



    @Override
    public void riskCheckQuoteorder(Saleorder saleorder) {
        List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);

        boolean flag = true;

        //商品质量待办
        ITodoInstance skuTodo = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA);


        for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
            SkuRiskModelVo  skuRiskModelVo = this.riskCheckSku(saleorderGoods.getSku());
            if(!skuRiskModelVo.getIsRisk()){
                flag = false;
                skuTodo.add(saleorderGoods.getGoodsId(),saleorder.getQuoteorderNo(),saleorder.getQuoteorderId()+"中的 "+saleorderGoods.getSku()
                        +" 商品质量风控信息不完整，请尽快补充！否则会影响销售订单流程！",RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_QUOTEORDER.getValue());
            }
        }

        if(!flag){
            Saleorder update = new Saleorder();
            update.setSaleorderId(saleorder.getSaleorderId());
            update.setIsRisk(RiskHandlerKeyConstant.QUOTE_RISK);
            saleorderMapper.updateByPrimaryKeySelective(update);
        }
    }

    @Override
    public void setSaleorderIsRiskInfo(Saleorder saleorder) {
        Saleorder saleOrderById = saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());
        Integer isRisk = saleOrderById.getIsRisk();

        saleorder.setIsRisk(isRisk);
        saleorder.setRiskComments(saleOrderById.getRiskComments());
        saleorder.setRiskTime(saleOrderById.getRiskTime());
    }


    @Override
    public void setSaleorderIsRiskInfo(Saleorder saleorder, List<SaleorderGoods> saleorderGoodsList) {

        Saleorder saleOrderById = saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());

        //是否存在医疗器械
        boolean checkFlag = false;

        for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
            SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(saleorderGoods.getSku());
            if(simpleMedicalCategory != null && simpleMedicalCategory.getRegistrationNumberId() != null){
                checkFlag = true;
                break;
            }
        }
        if(!checkFlag){
            saleorder.setIsRisk(RiskHandlerKeyConstant.NO_RISK);
            return;
        }
        Integer isRisk = saleOrderById.getIsRisk();

        saleorder.setIsRisk(isRisk);
        saleorder.setRiskComments(saleOrderById.getRiskComments());
        saleorder.setRiskTime(saleOrderById.getRiskTime());

        if(isRisk == null || isRisk.equals(0) || isRisk.equals(RiskHandlerKeyConstant.FINISH_RISK)){
            return;
        }

        for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
            SkuRiskModelVo skuRiskModelVo = this.riskCheckSku(saleorderGoods.getSku());
            if(!skuRiskModelVo.getIsRisk()){
                saleorderGoods.setIsRisk(RiskHandlerKeyConstant.SALE_RISK);
                saleorderGoods.setRiskComments(skuRiskModelVo.getSkuTitle());
            }
        }

        RiskModel riskModel = checkTrader(saleOrderById.getTraderId());
        if(!riskModel.getIsRisk()){
            saleorder.setIsTraderRisk(1);
        }
    }

    @Override
    public void setBuyorderGoodsIsRiskInfo(BuyorderVo buyorder) {
        Buyorder buyorderInfo = buyorderMapper.selectByPrimaryKey(buyorder.getBuyorderId());
        Integer isRisk = buyorderInfo.getIsRisk();
        if ( RiskHandlerKeyConstant.SALE_RISK.equals(isRisk) ){
            RiskModel riskModel = riskCheckTrader(buyorder.getTraderId(),isRisk);
            if(riskModel.getIsRisk()){
                isRisk = RiskHandlerKeyConstant.FINISH_RISK;
            }
        }
        buyorder.setIsRisk(isRisk);
        buyorder.setRiskComments(buyorderInfo.getRiskComments());
        buyorder.setRiskTime(buyorderInfo.getRiskTime());
    }

    @Override
    public void setBuyorderGoodsIsRiskInfo(Buyorder buyorder, List<BuyorderGoodsVo> list) {

        Buyorder buyorderInfo = buyorderMapper.selectByPrimaryKey(buyorder.getBuyorderId());
        //是否存在医疗器械
        boolean checkFlag = false;

        boolean oneManageCateGoryLevel = true;
        //忽略校验字段
        HashMap<String,Boolean> ignoreFiledMap = new HashMap<>();

        for (BuyorderGoodsVo buyorderGoodsVo : list) {
            SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(buyorderGoodsVo.getSku());
            if(simpleMedicalCategory != null && !SysOptionConstant.ID_968.equals(simpleMedicalCategory.getManageCategory())){
                oneManageCateGoryLevel = false;
            }
            if(simpleMedicalCategory != null && simpleMedicalCategory.getRegistrationNumberId() != null){
                checkFlag = true;
                break;
            }
        }

        if(!checkFlag){
            buyorder.setIsRisk(RiskHandlerKeyConstant.NO_RISK);
            return;
        }

        if(oneManageCateGoryLevel){
            ignoreFiledMap.put("qualityAssuranceList",false);
            ignoreFiledMap.put("afterSalesBookList",false);
        }else {
            ignoreFiledMap.put("qualityAssuranceList",true);
            ignoreFiledMap.put("afterSalesBookList",true);
        }

        Integer isRisk = buyorderInfo.getIsRisk();
        buyorder.setIsRisk(isRisk);
        buyorder.setRiskComments(buyorderInfo.getRiskComments());
        buyorder.setRiskTime(buyorderInfo.getRiskTime());

        if(isRisk == null || isRisk.equals(0) || isRisk.equals(RiskHandlerKeyConstant.FINISH_RISK)){
            return;
        }
        for (BuyorderGoodsVo buyorderGoodsVo : list) {
            SkuRiskModelVo skuRiskModelVo = this.riskCheckSku(buyorderGoodsVo.getSku());
            if(!skuRiskModelVo.getIsRisk()){
                buyorderGoodsVo.setIsRisk(RiskHandlerKeyConstant.SALE_RISK);
                buyorderGoodsVo.setRiskComments(skuRiskModelVo.getSkuTitle());
            }
        }

//        RiskModel riskModel = checkSupplier(buyorderInfo.getTraderId());
        HandlerStepContext context = new HandlerStepContext();
        RiskModel riskModel = null;
        try {
            //客户校验
            RiskHandlerStep traderRiskCheck = riskStepBuildFactory.supplierRiskCheck();
            context.put(RiskHandlerKeyConstant.TRADER_ID,buyorderInfo.getTraderId());
            context.put(RiskHandlerKeyConstant.IGNORE,ignoreFiledMap);
            riskModel = traderRiskCheck.dealWith(context);
        }catch (Exception e){
            logger.error("checkSupplier error{}",JSON.toJSONString(buyorder),e);
        }
        if(riskModel!=null&&!riskModel.getIsRisk()){
            buyorder.setIsTraderRisk(1);
        }
    }



    @Override
    public SaleorderRiskModelVo getUrlRiskAndCheckSaleorder(Saleorder saleorder) {
        SaleorderRiskModelVo saleorderRiskModelVo = this.riskCheckSaleOrder(saleorder);
        saleorderRiskModelVo.setIsRedirect(0);
        if(!saleorderRiskModelVo.getIsRisk()){
            Set<RiskEnum> riskEnumList = saleorderRiskModelVo.getRiskEnumList();
            StringBuffer sb = new StringBuffer();
            String url = "";
            if(StringUtil.isNotBlank(saleorderRiskModelVo.getTraderRiskMessage())){
                sb.append(saleorderRiskModelVo.getTraderRiskMessage()+"</br>");
            }
            if(riskEnumList.contains(RiskEnum.TRADER_QA_RISK)){

                TraderCustomer traderCustomer = traderCustomerMapper.getCustomerInfo(saleorder.getTraderId());
                url = erpUrl + "trader/customer/getFinanceAndAptitude.do?traderId="+traderCustomer.getTraderId()+
                        "&traderCustomerId="+traderCustomer.getTraderCustomerId()+"&customerNature="+traderCustomer.getCustomerNature();
                saleorderRiskModelVo.setIsRedirect(1);
            }else if(riskEnumList.contains(RiskEnum.TRADER_RISK)){
                if(!riskEnumList.contains(RiskEnum.TRADER_QA_RISK)){
                    url = erpUrl + "trader/customer/baseinfo.do?traderId="+saleorder.getTraderId();
                    saleorderRiskModelVo.setIsRedirect(1);
                }
            }

            if(riskEnumList.contains(RiskEnum.SKU_SPU_FIRST_RISK)){
                List<String> riskSkuList = saleorderRiskModelVo.getRiskSkuList();
                StringBuffer userName = new StringBuffer();
                Set<String> userNameSet = new HashSet<>();
                for (String sku : riskSkuList) {
                    Goods goods =  goodsMapper.getSkuInfoBySku(sku);
                    if(StringUtil.isNotBlank(goods.getManagerUserName())){
                        userNameSet.add(goods.getManagerUserName());
                    }
                    if(StringUtil.isNotBlank(goods.getAssistantUserName())){
                        userNameSet.add(goods.getAssistantUserName());
                    }
                }
                for (String name : userNameSet) {
                    userName.append(name+" ");
                }
                sb.append("订单中部分商品信息不完善，系统已生成待办事项给"+userName.toString()+"，请尽快跟进处理！");
            }
            saleorderRiskModelVo.setUrl(url);
            saleorderRiskModelVo.setTitle(sb.toString());
        }
        return saleorderRiskModelVo;
    }

    @Override
    public Integer getRiskFlag(User user, Integer saleorderIsRisk) {
        //风控质管部
        Boolean permoissionsFlag = this.permoissionsFlag(user, ErpConst.QUALITY_ORG);
        Integer riskFlag;
        if(saleorderIsRisk == null || saleorderIsRisk.equals(RiskHandlerKeyConstant.FINISH_RISK)){
            riskFlag = 1;
        }else if(!saleorderIsRisk.equals(RiskHandlerKeyConstant.SALE_RISK) || getisRiskcheck()){
            riskFlag = 1;
        }else if(saleorderIsRisk.equals(RiskHandlerKeyConstant.SALE_RISK) && permoissionsFlag && !getisRiskcheck()){
            riskFlag = 2;
        }else if(saleorderIsRisk.equals(RiskHandlerKeyConstant.SALE_RISK) && !permoissionsFlag && !getisRiskcheck()){
            riskFlag = 3;
        }else{
            riskFlag = 1;
        }
        return riskFlag;
    }


    @Override
    public Boolean permoissionsFlag(User user, String qualityOrg) {
        if(user.getUserId().equals(1) || user.getUserId().equals(2)){
            return true;
        }
        return this.isOrgFlag(user,qualityOrg);
    }

    @Override
    public Boolean isOrgFlag(User user, String qualityOrg) {
        User userInfo = userMapper.selectByPrimaryKey(user.getUserId());
        if(userInfo.getOrgName() != null && userInfo.getOrgName().contains(qualityOrg)){
            return true;
        }
        return false;
    }


    @Override
    public Boolean getisRiskcheck() {
        return StringUtil.isBlank(isRiskcheck);
    }

    @Override
    public BuyorderRiskModelVo getTitleAndcheckBuyorder(Buyorder buyorder) {
        Buyorder buyorderInfo = buyorderMapper.selectByPrimaryKey(buyorder.getBuyorderId());
        BuyorderRiskModelVo buyorderRiskModelVo = this.riskCheckBuyOrder(buyorder);
        Set<RiskEnum> riskEnumList = buyorderRiskModelVo.getRiskEnumList();

        List<BuyorderGoodsVo> buyorderGoodsList = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(buyorderInfo.getBuyorderId());

        StringBuffer sb = new StringBuffer();
        if(!buyorderRiskModelVo.getIsRisk()) {
            if (riskEnumList.contains(RiskEnum.SUPPLIER_RISK)) {
                BuyorderGoodsVo buyorderGoodsVo = buyorderGoodsList.get(0);
                Goods goods = goodsMapper.getSkuInfoBySku(buyorderGoodsVo.getSku());
                String userName ="";
                if(goods != null){
                    userName = goods.getManagerUserName();
                }
                sb.append("供应商信息不完善,系统已生成待办事项给"+ userName +",请尽快跟进处理！" + "</br>");
            }
            List<String> skuRiskList = buyorderRiskModelVo.getSkuRiskList();
            if (CollectionUtils.isNotEmpty(skuRiskList)) {
                StringBuffer skuStr = new StringBuffer();
                Set<String> userNameSet = new HashSet<>();
                for (String sku : skuRiskList) {
                    Goods goods = goodsMapper.getSkuInfoBySku(sku);
                    if (StringUtil.isNotBlank(goods.getManagerUserName())) {
                        userNameSet.add(goods.getManagerUserName());
                    }
                    if (StringUtil.isNotBlank(goods.getAssistantUserName())) {
                        userNameSet.add(goods.getAssistantUserName());
                    }
                    skuStr.append(sku + " ");
                }
                sb.append("订单中").append(skuStr.toString()).append("商品信息不完善，系统已生成待办事项给,");
                StringBuffer userNameStr = new StringBuffer();
                for (String userName : userNameSet) {
                    userNameStr.append(userName).append(" ");
                }
                sb.append(userNameStr.toString()).append(",请尽快跟进处理！");
            }

            buyorderRiskModelVo.setTitle(sb.toString());
        }
        return buyorderRiskModelVo;
    }

    @Override
    public void checkSpuAndSkuTodo(Integer spuId) {
        List<CoreSku> skuList = coreSkuMapper.getSkuBySpuId(spuId);

        checkSkuListFinshTodo(skuList);
    }

    @Override
    public void checkFirstEnageAndSkuTodo(Integer firstEnageId) {

        List<CoreSku> skuList = coreSkuMapper.getSkuByFirstEnageId(firstEnageId);

        checkSkuListFinshTodo(skuList);
    }

    @Override
    public void checkSkuAndtodo(Integer skuId) {
        if(skuId == null){
            return;
        }
        CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(skuId);

        checkSkuListFinshTodo(Collections.singletonList(coreSku));
    }

    @Override
    public void checkTraderTodo(Integer traderId) {
        if (Objects.isNull(traderId)) {
            return;
        }

        try {
            RiskModel riskmodel = checkTrader(traderId);

            //客户信息待办
            ITodoInstance traderData = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_DATA);
            //客户资质
            ITodoInstance traderCertificate = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE);

            Set<RiskEnum> riskEnumList = riskmodel.getRiskEnumList();
            if (Objects.nonNull(riskEnumList) && !riskEnumList.contains(RiskEnum.TRADER_RISK)) {
                traderData.finish(traderId);
            }
            if (Objects.nonNull(riskEnumList) && !riskEnumList.contains(RiskEnum.TRADER_QA_RISK)) {
                traderCertificate.finish(traderId);
            }
            //1.AROP-3979 前台请求syncYxgAptitudeStatus方法时，request中不带session，此时通过session获取user会报空指针异常
            //2.AROP-4344 getUserInfoByName is null
        } catch (NullPointerException ex){
            logger.error("checkTraderTodo traderId:"+traderId+"error:",ex);
        } catch (Exception e){
            logger.error("checkTraderTodo traderId:"+traderId+"error:",e);
        }
    }

    @Override
    public void checkSupplierTodo(Integer traderId) {
        try {
            List<TodoList> todoLists =todoListMapper.getOrderList(traderId,RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_BUYORDER.getValue(),0);
            for (TodoList todoList : todoLists) {
                Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(todoList.getBuzExtra());
                List<BuyorderGoodsVo> buyorderGoodsList = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(buyorder.getBuyorderId());
                boolean oneManageCateGoryLevel = true;
                //忽略校验字段
                HashMap<String,Boolean> ignoreFiledMap = new HashMap<>();

                for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsList) {
                    SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(buyorderGoodsVo.getSku());
                    if(!SysOptionConstant.ID_968.equals(simpleMedicalCategory.getManageCategory())){
                        oneManageCateGoryLevel = false;
                        break;
                    }
                }
                if(oneManageCateGoryLevel){
                    ignoreFiledMap.put("qualityAssuranceList",false);
                    ignoreFiledMap.put("afterSalesBookList",false);
                }else {
                    ignoreFiledMap.put("qualityAssuranceList",true);
                    ignoreFiledMap.put("afterSalesBookList",true);
                }

                HandlerStepContext context = new HandlerStepContext();
                //客户校验
                RiskHandlerStep traderRiskCheck = riskStepBuildFactory.supplierRiskCheck();
                context.put(RiskHandlerKeyConstant.TRADER_ID,buyorder.getTraderId());
                context.put(RiskHandlerKeyConstant.IGNORE,ignoreFiledMap);
                RiskModel riskmodel = traderRiskCheck.dealWith(context);
                logger.info("checkSupplierTodo traderId:{},flag:{}",traderId,riskmodel.getIsRisk());
                if(riskmodel !=null && riskmodel.getIsRisk()){
                    todoListMapper.updateStatusByBuzTypeAndBuzIdAndOrderNo(TodoListBuzSceneEnum.RISK_CHECK_TRADER_SUPPLY_DATA.getBuzSceneId(),traderId,buyorder.getBuyorderNo(),1,getCurrentRequestUser().getUserId(),System.currentTimeMillis());
                    logger.info("待办事项---风控校验供应商数据，buzId;{}更新为已完成",traderId);
                }
            }

        }catch (Exception e){
            logger.error("checkSupplierTodo traderId:"+traderId+"error:",e);
        }
    }

    private User getCurrentRequestUser(){
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return userMapper.getUserByName("njadmin");
        }
        HttpServletRequest request = attributes.getRequest();
        return (User) request.getSession().getAttribute(ErpConst.CURR_USER);
    }


    private RiskModel checkSupplier(Integer traderId) {
        HandlerStepContext context = new HandlerStepContext();
        RiskModel riskModel = new RiskModel();
        try {
            //客户校验
            RiskHandlerStep traderRiskCheck = riskStepBuildFactory.supplierRiskCheck();

            context.put(RiskHandlerKeyConstant.TRADER_ID,traderId);
            riskModel = traderRiskCheck.dealWith(context);
            return riskModel;
        }catch (Exception e){
            logger.error("checkSupplier error",e);
        }
        return riskModel;
    }

    private RiskModel checkTrader(Integer traderId) {

        HandlerStepContext context = new HandlerStepContext();
        RiskModel riskModel = new RiskModel();
        if(traderId==null){
            return riskModel;
        }
        try {
            //客户校验
            RiskHandlerStep traderRiskCheck = riskStepBuildFactory.traderRiskCheck();

            //忽略校验字段
            HashMap<String,Boolean> ignoreFiledMap = new HashMap<>();
            ignoreFiledMap.put("qualityAssuranceList",true);
            ignoreFiledMap.put("afterSalesBookList",true);
            context.put(RiskHandlerKeyConstant.IGNORE,ignoreFiledMap);

            context.put(RiskHandlerKeyConstant.TRADER_ID,traderId);
            riskModel = traderRiskCheck.dealWith(context);
        }catch (Exception e){
            logger.error("checkTrader error:"+traderId,e);
        }
        return riskModel;
    }

    /**
     * @description: 校验商品风控参数是否通过
     * @return:
     * @author: Strange
     * @date: 2020/12/18
     **/
    private void checkSkuListFinshTodo(List<CoreSku> skuList) {
        if(CollectionUtils.isEmpty(skuList)){
            return;
        }

        ITodoInstance skuTodo = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA);
        for (CoreSku coreSku : skuList) {
            SkuRiskModelVo skuRiskModelVo = this.riskCheckSku(coreSku.getSkuNo());
            if(skuRiskModelVo.getIsRisk()){
                //商品质量待办
                skuTodo.finish(coreSku.getSkuId());
            }
        }
    }
    @Override
    public void resetSaleorderRisk(Integer saleorderId) {
        Saleorder update = new Saleorder();
        update.setSaleorderId(saleorderId);
        update.setIsRisk(RiskHandlerKeyConstant.DEFAULT_RISK);
        saleorderMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void resetBuyorderRiks(Integer buyorderId) {
        Buyorder update = new Buyorder();
        update.setBuyorderId(buyorderId);
        update.setIsRisk(RiskHandlerKeyConstant.DEFAULT_RISK);
        buyorderMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public RiskModel riskCheckTrader(Integer traderId, Integer traderType) {
        RiskModel riskModel;
        if(traderType.equals(1)){
            riskModel = checkSupplier(traderId);
            riskModel.setTraderRiskMessage(riskModel.getSupplierRiskMessage()+"!");
        }else{
            riskModel = checkTrader(traderId);
            Set<RiskEnum> riskEnumList = riskModel.getRiskEnumList();
            String message = "";
            for (RiskEnum riskEnum : riskEnumList) {
                message = message + riskEnum.getRiskMessage()+"!" + "</br>";
            }
            riskModel.setTraderRiskMessage(message);
        }

        return riskModel;
    }

}
