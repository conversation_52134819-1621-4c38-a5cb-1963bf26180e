package com.newtask.data.saleorder;

import com.alibaba.fastjson.JSON;
import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.newtask.data.dto.SaleoderDataDto;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SaleorderCurrentOrgAndUserIdSync extends AbstractDataSync {
    @Resource
    SaleorderDataMapper saleorderDataMapper;

    @Resource
    OrganizationMapper organizationMapper;

    @Resource
    UserMapper userMapper;

    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {

        return saleorderDataMapper.getSaleorderCurrentOrgAndUserIdsList(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> currentOrgIds = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(bizIds)) {
            logger.info("同步订单归属人员信息loadBizData.bizIds={}", JSON.toJSONString(bizIds));
            List<Map<String, Object>> currentOrgIdList = saleorderDataMapper.findCurrentOrgAndUserIdsBySaleorderId(bizIds);
            currentOrgIdList.forEach(item -> {
                Map<String, Object> map = new HashMap<>(16);
                map.put("saleorderId",item.get("saleorderId"));
                if (item.get("traderId") != null && Integer.valueOf(item.get("traderId").toString()) > 0) {
                    // 获取归属销售ID； traderType： 1客户，2供应商
                    User user = userMapper.getUserByTraderId(Integer.valueOf(item.get("traderId")+""), 1);
                    logger.info("遍历销售订单：{}查询到归属销售信息:{}",item.get("saleorderId"),JSON.toJSONString(user));
                    map.put("currentUserId",user == null ? 0 : user.getUserId());

                    // 获取销售部门ID （若一个多个部门，默认取第一个部门）
                    User userOrg = organizationMapper.getTraderUserAndOrgByTraderId(Integer.valueOf(item.get("traderId")+""), 1);
                    map.put("currentOrgId",userOrg == null ? "" : userOrg.getOrgId());
                } else {
                    // 获取归属销售ID TODO jianrong error data
                    User user = userMapper.selectByPrimaryKey(NumberUtils.toInt(item.get("userId")+""));
                    map.put("currentUserId",user == null ? 0 : user.getUserId());
                    Organization org = organizationMapper.getOrgNameByUserId(NumberUtils.toInt(item.get("userId")+""));
                    map.put("currentOrgId",org == null ? "" : org.getOrgId());
                }
                currentOrgIds.add(map);
            });
        }
        logger.info("SaleorderCurrentOrgAndUserIdSync实现类loadBizData()待更新数据汇总map：{}",JSON.toJSONString(currentOrgIds));
        return currentOrgIds;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            long timeMillis = System.currentTimeMillis();
            List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
            dataList.forEach(item -> {
                Integer saleorderId = NumberUtils.toInt(item.get("saleorderId").toString());

                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setCurrentOrgId(item.get("currentOrgId") == null || item.get("currentOrgId") == "" ? null: Integer.valueOf(item.get("currentOrgId")+""));
                saleoderDataDto.setCurrentUserId(item.get("currentUserId") == null || item.get("currentUserId") == "" ? null: Integer.valueOf(item.get("currentUserId")+""));
                saleoderDataDtos.add(saleoderDataDto);
                // 判断saleorderId是否存在
                makeExist(saleorderId, timeMillis);
            });
            // 根据id修改currentOrgId和currentOrgId
            saleorderDataMapper.updateSaleorderCurrentOrgAndUserId(saleoderDataDtos, timeMillis);
        }
    }

    @Override
    public synchronized void makeExist(Integer id, Long nowTime) {
        try {
            Long count = saleorderDataMapper.getSaleorderById(id);
            if (count <= 0) {
                saleorderDataMapper.insertSaleOrder(id, nowTime);
            }
        }catch (Exception e){
           //ignor
        }
    }
}
