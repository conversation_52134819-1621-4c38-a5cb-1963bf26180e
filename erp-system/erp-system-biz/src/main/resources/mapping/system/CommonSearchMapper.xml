<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.CommonSearchMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.CommonSearchEntity">
    <!--@mbg.generated-->
    <!--@Table T_COMMON_SEARCH-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="SEARCH_NAME" jdbcType="VARCHAR" property="searchName" />
    <result column="SEARCH_FROM_ENUM" jdbcType="VARCHAR" property="searchFromEnum" />
    <result column="SEARCH_TYPE" jdbcType="VARCHAR" property="searchType" />
    <result column="SEARCH_CONTENT" jdbcType="VARCHAR" property="searchContent" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, USER_ID, SEARCH_NAME,SEARCH_FROM_ENUM, SEARCH_TYPE, SEARCH_CONTENT, ADD_TIME, CREATOR, MOD_TIME,
    UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_COMMON_SEARCH
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_COMMON_SEARCH
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.CommonSearchEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_COMMON_SEARCH (USER_ID,SEARCH_NAME, SEARCH_FROM_ENUM, SEARCH_TYPE,
      SEARCH_CONTENT, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER)
    values (#{userId,jdbcType=INTEGER},#{searchName,jdbcType=VARCHAR}, #{searchFromEnum,jdbcType=VARCHAR}, #{searchType,jdbcType=VARCHAR},
      #{searchContent,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.CommonSearchEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_COMMON_SEARCH
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="searchName != null and searchName != ''">
        SEARCH_NAME,
      </if>
      <if test="searchFromEnum != null and searchFromEnum != ''">
        SEARCH_FROM_ENUM,
      </if>
      <if test="searchType != null and searchType != ''">
        SEARCH_TYPE,
      </if>
      <if test="searchContent != null and searchContent != ''">
        SEARCH_CONTENT,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="searchName != null and searchName != ''">
        #{searchName,jdbcType=VARCHAR},
      </if>
      <if test="searchFromEnum != null and searchFromEnum != ''">
        #{searchFromEnum,jdbcType=VARCHAR},
      </if>
      <if test="searchType != null and searchType != ''">
        #{searchType,jdbcType=VARCHAR},
      </if>
      <if test="searchContent != null and searchContent != ''">
        #{searchContent,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.CommonSearchEntity">
    <!--@mbg.generated-->
    update T_COMMON_SEARCH
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="searchName != null and searchName != ''">
        SEARCH_NAME = #{searchName,jdbcType=VARCHAR},
      </if>
      <if test="searchFromEnum != null and searchFromEnum != ''">
        SEARCH_FROM_ENUM = #{searchFromEnum,jdbcType=VARCHAR},
      </if>
      <if test="searchType != null and searchType != ''">
        SEARCH_TYPE = #{searchType,jdbcType=VARCHAR},
      </if>
      <if test="searchContent != null and searchContent != ''">
        SEARCH_CONTENT = #{searchContent,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.CommonSearchEntity">
    <!--@mbg.generated-->
    update T_COMMON_SEARCH
    set USER_ID = #{userId,jdbcType=INTEGER},
       SEARCH_NAME = #{searchnAME,jdbcType=VARCHAR},
      SEARCH_FROM_ENUM = #{searchFromEnum,jdbcType=VARCHAR},
      SEARCH_TYPE = #{searchType,jdbcType=VARCHAR},
      SEARCH_CONTENT = #{searchContent,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where ID = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectListByUserIdAndSearchFrom" parameterType="com.vedeng.erp.system.dto.CommonSearchDto" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_COMMON_SEARCH
    where SEARCH_FROM_ENUM = #{searchFromEnum,jdbcType=VARCHAR}
    and USER_ID = #{userId,jdbcType=INTEGER}
    AND SEARCH_TYPE = #{searchType,jdbcType=VARCHAR}
    ORDER BY ID ASC
  </select>
</mapper>