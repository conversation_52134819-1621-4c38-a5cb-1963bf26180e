<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<html>
<head>
    <title>付款配置</title>
</head>
<style>
    .pay_apply_allocation_body {
        margin: 20px;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        background-color: #fff;
    }

    label {
        margin-left: 8px;
        color: #666;
    }

    .demo-input-suffix {
        display: flex;
        align-items: center;
    }

    .el-input,
    .el-time-select {
        margin: 0 5px;
    }

    .el-button {
        margin-right: 10px;
    }

    [v-cloak] {
        display: none;
    }

    /* 新增样式以适应靠左对齐 */
    .section-title, .action-buttons, .demo-input-suffix, .el-checkbox, label {
        text-align: left;
    }

    .el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 200px;
    }

    .el-input, .el-time-select {
        margin: 0 5px;
        width: 100px;
    }

    .row-spacing {
        display: flex;
        align-items: center;
        margin-bottom: 20px; /* 或根据需要调整 */
    }

    .checkbox-wrapper {
        width: 250px; /* 根据需要调整 */
        flex-shrink: 0; /* 防止el-checkbox缩小 */
    }

    label {
        color: gray;
        flex-grow: 1; /* 让label占据剩余空间 */
        margin-left: 8px; /* 根据需要调整，保持与el-checkbox的间隔 */
    }

    .el-input__inner {
        text-align: center;
    }

    .el-checkbox__label {
        font-size: 16px;
    }

</style>

<body>
<div id="app" v-cloak>
    <div class="pay_apply_allocation_body">
        <div class="row-spacing">付款申请规则</div>
        <div class="row-spacing">
            <div class="checkbox-wrapper">
                <el-checkbox v-model="autoPayConfigDto.enableContractAudit">启用采购合同审核状态</el-checkbox>
            </div>
            <label>启用后,采购合同已审核才允许提交付款申请</label>
        </div>
        <div class="row-spacing">
            <div class="checkbox-wrapper">
                <el-checkbox v-model="autoPayConfigDto.enableSupplierWhitelist">启用供应商白名单</el-checkbox>
            </div>
            <label>启用后,白名单中的供应商提交付款申请不限制采购合同状态</label>
        </div>
        <div class="row-spacing">
            周一至周五&nbsp
            <el-time-picker
                    is-range
                    v-model="payApplyTimeSelector"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围">
            </el-time-picker>
            &nbsp外,申请付款限额
            <el-input maxlength="8" v-model="autoPayConfigDto.payLimit" placeholder="请输入"></el-input>
            万
        </div>
        <br>
        <br>
        <div class="row-spacing">自动制单规则</div>
        <div class="row-spacing">
            <div class="checkbox-wrapper">
                <el-checkbox v-model="autoPayConfigDto.enableAutoPay">启用付款申请自动制单</el-checkbox>
            </div>
            <label>定时任务配置:执行频率-每30分钟</label>
        </div>
        <div class="row-spacing">
            <div class="checkbox-wrapper">
                <el-checkbox v-model="autoPayConfigDto.enableKingDeeAutoAudit">启用推送金蝶付款单自动审核</el-checkbox>
            </div>
            <label>启用后,推送金蝶的付款单自动审核,自动提交银行付款</label>
        </div>
        <div class="row-spacing">
            周一至周五&nbsp
            <el-time-picker
                    is-range
                    v-model="payBankTimeSelector"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围">
            </el-time-picker>
            &nbsp外,付款银行为{{autoPayConfigDto.payBank}}
        </div>
    </div>
    <div style="position: fixed;bottom: 10px;right: 30px">
        <el-button type="success" plain @click="submit()" :disabled="submitDisabled">确定</el-button>
        <el-button type="primary" plain @click="cancel()">取消</el-button>
    </div>
</div>
</body>

<script src="${pageContext.request.contextPath}/static/api/payApply/pay_apply_allocation.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    let vm = new Vue({
        el: '#app',
        data() {
            return {
                autoPayConfigDto: {
                    autoPayConfigId: 0,
                    enableAutoPay: false,
                    enableContractAudit: false,
                    enableSupplierWhitelist: false,
                    enableKingDeeAutoAudit: false,
                    payApplyTimeStart: "00:00:00",
                    payApplyTimeEnd: "23:59:59",
                    payLimit: "",
                    payBankTimeStart: "00:00:00",
                    payBankTimeEnd: "23:59:59",
                    payBank: ""
                },
                submitDisabled: false,
                payApplyTimeSelector: [new Date(2016, 9, 10, 8, 40), new Date(2016, 9, 10, 9, 40)],
                payBankTimeSelector: [new Date(2016, 9, 10, 8, 40), new Date(2016, 9, 10, 9, 40)]
            }
        },

        mounted() {
            loadingApp();
            this.initData()
        },


        methods: {
            initData() {
                paymentAllocationInfo()
                    .then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            return;
                        }
                        this.autoPayConfigDto = res.data.data;
                        this.payApplyTimeSelector = [this.convertTimeToTodayDate(this.autoPayConfigDto.payApplyTimeStart),this.convertTimeToTodayDate(this.autoPayConfigDto.payApplyTimeEnd)]
                        this.payBankTimeSelector = [this.convertTimeToTodayDate(this.autoPayConfigDto.payBankTimeStart),this.convertTimeToTodayDate(this.autoPayConfigDto.payBankTimeEnd)]
                    })
                    .catch(err => {
                        this.$message.error("系统异常：" + err.message);
                    });

            },

            cancel() {
                layer.closeAll();
                parent.layer.closeAll();
            },

            submit(){
                if (!this.autoPayConfigDto.payLimit) {
                    this.$message.error('申请付款限额不能为空');
                    return;
                }
                const payLimit = parseInt(this.autoPayConfigDto.payLimit, 10);
                if (isNaN(payLimit) || payLimit < 0 || this.autoPayConfigDto.payLimit !== payLimit.toString()) {
                    this.$message.error('申请付款限额只能填写0和正整数');
                    return;
                }
                if (!this.payApplyTimeSelector || this.payApplyTimeSelector.length !== 2 || !this.payApplyTimeSelector[0] || !this.payApplyTimeSelector[1]) {
                    this.$message.error('请选择完整的付款申请时间范围');
                    return;
                }
                if (!this.payBankTimeSelector || this.payBankTimeSelector.length !== 2 || !this.payBankTimeSelector[0] || !this.payBankTimeSelector[1]) {
                    this.$message.error('请选择完整的自动制单时间范围');
                    return;
                }
                this.autoPayConfigDto.payApplyTimeStart = this.formatDateToTimeString(this.payApplyTimeSelector[0]);
                this.autoPayConfigDto.payApplyTimeEnd = this.formatDateToTimeString(this.payApplyTimeSelector[1]);
                this.autoPayConfigDto.payBankTimeStart = this.formatDateToTimeString(this.payBankTimeSelector[0]);
                this.autoPayConfigDto.payBankTimeEnd = this.formatDateToTimeString(this.payBankTimeSelector[1]);
                //发送请求
                this.submitDisabled = true;
                paymentAllocationSubmit(this.autoPayConfigDto)
                    .then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            this.submitDisabled = false;
                            return;
                        }
                        this.$message.success("提交成功");
                        // 设置延时关闭弹窗和刷新页面
                        setTimeout(() => {
                            this.submitDisabled = false;
                            parent.layer.closeAll();
                        }, 2000); // 3000 毫秒后执行
                    })
                    .catch(err => {
                        this.submitDisabled = false;
                        this.$message.error("系统异常：" + err.message);
                    });
            },

            convertTimeToTodayDate(timeString) {
                const now = new Date();
                const [hours, minutes, seconds] = timeString.split(':').map(Number);
                now.setHours(hours, minutes, seconds, 0);
                return now;
            },

            formatDateToTimeString(date) {
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                const seconds = date.getSeconds().toString().padStart(2, '0');
                return hours+':'+minutes+':'+seconds;
            },
        }
    });
</script>
</html>
