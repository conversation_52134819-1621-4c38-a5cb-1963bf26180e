<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.billsync.dao.TmpWechatBillDataExtMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.billsync.task.model.entity.TmpWechatBillDataExtDo">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="OCCURRENCE_TIME" jdbcType="TIMESTAMP" property="occurrenceTime" />
    <result column="PUBLIC_ID" jdbcType="VARCHAR" property="publicId" />
    <result column="MERCHANT_NO" jdbcType="VARCHAR" property="merchantNo" />
    <result column="SPECIAL_MERCHANT_NO" jdbcType="VARCHAR" property="specialMerchantNo" />
    <result column="DEVICE_NO" jdbcType="VARCHAR" property="deviceNo" />
    <result column="WX_ORDER_NO" jdbcType="VARCHAR" property="wxOrderNo" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="BUSINESS_STATUS" jdbcType="VARCHAR" property="businessStatus" />
    <result column="BANK" jdbcType="VARCHAR" property="bank" />
    <result column="CURRENCY" jdbcType="VARCHAR" property="currency" />
    <result column="ORDER_AMOUNT_DUE" jdbcType="DECIMAL" property="orderAmountDue" />
    <result column="COUPON_AMOUNT" jdbcType="DECIMAL" property="couponAmount" />
    <result column="WX_REFUND_NO" jdbcType="VARCHAR" property="wxRefundNo" />
    <result column="REFUND_NO" jdbcType="VARCHAR" property="refundNo" />
    <result column="REFUND_AMOUNT" jdbcType="DECIMAL" property="refundAmount" />
    <result column="RECHARGE_REFUND_AMOUNT" jdbcType="DECIMAL" property="rechargeRefundAmount" />
    <result column="REFUND_TYPE" jdbcType="VARCHAR" property="refundType" />
    <result column="REFUND_STATUS" jdbcType="VARCHAR" property="refundStatus" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="DATA_PACK" jdbcType="VARCHAR" property="dataPack" />
    <result column="HANDLING_FEE" jdbcType="DECIMAL" property="handlingFee" />
    <result column="RATE" jdbcType="VARCHAR" property="rate" />
    <result column="ORDER_AMOUNT" jdbcType="DECIMAL" property="orderAmount" />
    <result column="REQ_REFUND_AMOUNT" jdbcType="DECIMAL" property="reqRefundAmount" />
    <result column="SYNC_TIME" jdbcType="DATE" property="syncTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, OCCURRENCE_TIME, PUBLIC_ID, MERCHANT_NO, SPECIAL_MERCHANT_NO, DEVICE_NO, WX_ORDER_NO,
    ORDER_NO, USER_ID, BUSINESS_TYPE, BUSINESS_STATUS, BANK, CURRENCY, ORDER_AMOUNT_DUE,
    COUPON_AMOUNT, WX_REFUND_NO, REFUND_NO, REFUND_AMOUNT, RECHARGE_REFUND_AMOUNT, REFUND_TYPE,
    REFUND_STATUS, SKU_NAME, DATA_PACK, HANDLING_FEE, RATE, ORDER_AMOUNT, REQ_REFUND_AMOUNT,
    SYNC_TIME, ADD_TIME, UPDATE_TIME
  </sql>

  <delete id="deleteBySycTime" parameterType="java.lang.String">
    delete from TMP_WECHAT_BILL_DATA
    where SYNC_TIME = #{billDate,jdbcType=DATE}
  </delete>
    <select id="getWechatBillListByTime"
            resultType="com.vedeng.billsync.task.model.entity.TmpWechatBillDataExtDo">
      select
      <include refid="Base_Column_List"/>
      FROM TMP_WECHAT_BILL_DATA
      WHERE
      SYNC_TIME <![CDATA[=]]> #{queryDate,jdbcType = DATE}


    </select>
</mapper>