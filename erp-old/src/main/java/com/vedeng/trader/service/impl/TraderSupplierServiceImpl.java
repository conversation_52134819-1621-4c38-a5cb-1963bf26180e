package com.vedeng.trader.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.constant.goods.LogTypeEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.shiro.constant.SecurityConstants;
import com.vedeng.common.util.*;
import com.vedeng.doc.api.docsupplier.command.SupplierDocCommand;
import com.vedeng.doc.api.dto.DataResult;
import com.vedeng.docSync.service.SyncSupplierService;
import com.vedeng.erp.market.api.MarketPlanApiService;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.vo.RegisterSkuVo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.LogCheckGenerateMapper;
import com.vedeng.goods.enums.ManufacturerStatusEnum;
import com.vedeng.goods.manufacturer.constants.ManufacturerConstants;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.dto.TransCertificateDto;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.manufacturer.model.ManufacturerExample;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.goods.service.ManufacturerService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.Tag;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.dao.*;
import com.vedeng.trader.feign.RemoteDocSupplierApiService;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.vo.RegistrationAttachment;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.service.CommunicateService;
import com.vedeng.trader.service.TraderDataService;
import com.vedeng.trader.service.TraderSupplierService;
import com.vedeng.trader.service.converter.CertificateConverter;
import com.vedeng.ty.api.req.ReqProductDto;
import com.vedeng.ty.api.res.ResOperateCompanyInfoDto;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("traderSupplierService")
public class TraderSupplierServiceImpl extends BaseServiceimpl implements TraderSupplierService {

    public static Logger logger = LoggerFactory.getLogger(TraderSupplierServiceImpl.class);

    @Resource
    private UserMapper userMapper;

    @Autowired
    @Qualifier("rTraderJUserMapper")
    private RTraderJUserMapper rTraderJUserMapper;

    @Autowired
    @Qualifier("communicateRecordMapper")
    private CommunicateRecordMapper communicateRecordMapper;

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;

    @Autowired
    @Qualifier("communicateService")
    private CommunicateService communicateService;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private RegistrationNumberMapper registrationNumberMapper;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private SupplierRegistrationNumberMapper supplierRegistrationNumberMapper;

    @Resource
    private TraderCertificateMapper traderCertificateMapper;

    @Resource
    private TraderSupplierMapper traderSupplierMapper;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private ManufacturerService manufacturerService;

    @Autowired
    private ManufacturerMapper manufacturerMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private TraderDataService traderDataService;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    private RTraderJUserModifyRecordMapper rTraderJUserModifyRecordMapper;

    @Autowired
    private RemoteDocSupplierApiService remoteDocSupplierApiService;

    @Autowired
    private SyncSupplierService syncSupplierService;

    @Autowired
    private LogCheckGenerateMapper logCheckGenerateMapper;

    @Autowired
    private MarketPlanApiService marketPlanApiService;

    @Value("${api_http}")
    protected String api_http;

    @Value("${ty_ali_url}")
    protected String tyAliUrl;

    @Value("${api_http}")
    protected String apiHttp;


    /**
     * 获取供应商列表
     *
     * @param traderSupplierVo
     * @param page
     * @param userList
     * @return
     */
    @Override
    public Map<String, Object> getTraderSupplierList(TraderSupplierVo traderSupplierVo, Page page, List<User> userList) {
        try {
            ResultInfo<?> result = getTraderSupplierListPage(traderSupplierVo, page);

            if (result == null) {
                return null;
            }
            List<TraderSupplierVo> traderSupplierList = (List<TraderSupplierVo>) result.getListData();
            User user = null;
            for (TraderSupplierVo tsv : traderSupplierList) {
                user = userMapper.getUserByTraderId(tsv.getTraderId(), ErpConst.TWO);
                if (null != user) {
                    //归属采购
                    tsv.setPersonal(user.getUsername());
                }
                if (ObjectUtils.notEmpty(tsv.getAreaId())) {
                    tsv.setTraderSupplierAddress(getAddressByAreaId(tsv.getAreaId()));
                }

            }
            page = result.getPage();
            Map<String, Object> map = new HashMap<>();
            map.put("list", traderSupplierList);
            map.put("page", page);
            return map;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public ResultInfo<?> getTraderSupplierListPage(TraderSupplierVo traderSupplierVo, Page page) {
        Map<String, Object> map = new HashMap<>();
        if (traderSupplierVo != null) {
            if (traderSupplierVo.getSearchMsg() != null && !"".equals(traderSupplierVo.getSearchMsg())) {
                List<Integer> searchTraderIds = getTagTraderIdList(traderSupplierVo);
                if (searchTraderIds != null && searchTraderIds.size() > 0) {
                    traderSupplierVo.setSearchTraderIds(searchTraderIds);
                    ;
                } else {
                    List<Integer> traders = new ArrayList<>();
                    traders.add(-1);
                    traderSupplierVo.setSearchTraderIds(traders);
                }
            }

            if (traderSupplierVo.getContactWay() != null && !"".equals(traderSupplierVo.getContactWay())) {
                TraderContact traderContact = new TraderContact();
                traderContact.setTraderType(2);
                traderContact.setMobile(traderSupplierVo.getContactWay());
                List<Integer> traderIds = traderSupplierMapper.getTraderIdListByContactWay(traderContact);
                if (traderIds != null && traderIds.size() > 0) {
                    traderSupplierVo.setContactTraderIds(traderIds);
                } else {
                    List<Integer> traders = new ArrayList<>();
                    traders.add(-1);
                    traderSupplierVo.setContactTraderIds(traders);
                }
            }

            if (traderSupplierVo.getTimeType() != null && traderSupplierVo.getTimeType() == 2) {//交易时间
                //查询采购订单生效未关闭
                List<Integer> tradIds = buyorderMapper.getTraderIdsByTime(traderSupplierVo);
                traderSupplierVo.setTraderTimeList(tradIds);
            }

        }
        //是否有合作
//        if (traderSupplierVo != null && traderSupplierVo.getCooperate() != null && !"".equals(traderSupplierVo.getCooperate())) {
//            List<Integer> traderIds = buyorderMapper.getBuyOrderTraderIdList(traderSupplierVo.getCompanyId());
//            if ("1".equals(traderSupplierVo.getCooperate())) {//有合作，采购订单中已生效的客户
//                if (traderIds != null && traderIds.size() > 0) {
//                    traderSupplierVo.setCooperateTraderIds(traderIds);
//                    ;
//                }
//            } else {
//                traderSupplierVo.setNoCooperateTraderIds(traderIds);
//            }
//        }

        map.put("traderSupplierVo", traderSupplierVo);
        map.put("page", page);
        List<TraderSupplierVo> list = traderSupplierMapper.getTraderSupplierVolistpage(map);
        if (list != null && list.size() > 0) {
//            List<TraderSupplierVo> tsvList = traderSupplierMapper.batchTraderBussinessData(list);

            for (TraderSupplierVo tsv : list) {
//                for (TraderSupplierVo tsv2 : tsvList) {
//                    if (tsv2.getTraderId() != null && tsv2.getTraderId().equals(tsv.getTraderId())) {
//                        tsv.setBuyCount(tsv2.getBuyCount());
//                        tsv.setBuyMoney(tsv2.getBuyMoney());
//                        break;
//                    }
//                }

                //采购搜索供应商时验证帐期余额
                if (traderSupplierVo.getRequestType() != null && "cg".equals(traderSupplierVo.getRequestType())) {
                    //查询供应商账期余额
                    TraderPeriodData traderPeriodData = traderDataService.getTraderPeriodData(tsv.getTraderId(), 2);
                    if (traderPeriodData != null && traderPeriodData.getPeriodAmount() != null && traderPeriodData.getPeriodAmountOccupy() != null
                            && traderPeriodData.getPeriodAmountUsed() != null) {
                        tsv.setPeriodBalance(traderPeriodData.getPeriodAmount().subtract(traderPeriodData.getPeriodAmountOccupy()).subtract(traderPeriodData.getPeriodAmountUsed()));
                    }
                }

            }
        }

        ResultInfo resultInfo = new ResultInfo(0, "查询成功", list);
        resultInfo.setPage(page);
        return resultInfo;
    }


    public Map<String, Object> getTraderSupplierListQualificationExpiration(TraderSupplierVo traderSupplierVo, Page page, List<User> userList) {
        try {
            ResultInfo<?> result = getTraderSupplierListQualificationExpirationPage(traderSupplierVo, page);
            if (result == null) {
                return null;
            }

            List<TraderSupplierVo> traderSupplierList = ((List<TraderSupplierVo>) result.getListData()).stream()
                    .peek(tsv -> {
                        User user = userMapper.getUserByTraderId(tsv.getTraderId(), ErpConst.TWO);
                        tsv.setPersonal(user != null ? user.getUsername() : null);
                        tsv.setTraderSupplierAddress(ObjectUtils.notEmpty(tsv.getAreaId()) ? getAddressByAreaId(tsv.getAreaId()) : null);
                    })
                    .collect(Collectors.toList());

            Map<String, Object> map = new HashMap<>();
            map.put("list", traderSupplierList);
            map.put("page", result.getPage());
            return map;
        } catch (Exception e) {
            logger.error("getTraderSupplierListQualificationExpiration:", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private ResultInfo<?> getTraderSupplierListQualificationExpirationPage(TraderSupplierVo traderSupplierVo, Page page) {
        Map<String, Object> map = new HashMap<>();
        map.put("traderSupplierVo", traderSupplierVo);
        map.put("page", page);
        List<TraderSupplierVo> list = traderSupplierMapper.getTraderSupplierListQualificationExpirationListPage(map);
        ResultInfo resultInfo = new ResultInfo(0, "查询成功", list);
        resultInfo.setPage(page);
        return resultInfo;
    }

    /**
     * 获取供应商列表
     *
     * @param traderSupplierVo
     * @param page
     * @param userList
     * @return
     */
    @Override
    public Map<String, Object> getSupplierByName(TraderSupplierVo traderSupplierVo, Page page, List<User> userList) {
        String url = httpUrl + ErpConst.TRADER_SUPPLIER_PAGE;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderSupplierVo>>> TypeRef2 = new TypeReference<ResultInfo<List<TraderSupplierVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderSupplierVo, clientId, clientKey, TypeRef2, page);
            if (result == null) {
                return null;
            }
            List<TraderSupplierVo> traderSupplierList = (List<TraderSupplierVo>) result.getData();
            for (TraderSupplierVo tsv : traderSupplierList) {
                //产品负责人----预留
                User user = userMapper.getUserByTraderId(tsv.getTraderId(), ErpConst.TWO);
                if (null != user) {
                    tsv.setPersonal(user.getUsername());
                }
                if (ObjectUtils.notEmpty(tsv.getAreaId())) {
                    tsv.setTraderSupplierAddress(getAddressByAreaId(tsv.getAreaId()));
                }

            }
            page = result.getPage();
            Map<String, Object> map = new HashMap<>();
            map.put("list", traderSupplierList);
            map.put("page", page);
            return map;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    /**
     * 是否置顶
     *
     * @param id
     * @param isTop
     * @param user
     * @return
     */
    @Override
    public ResultInfo isStick(Integer id, Integer isTop, User user) {
        TraderSupplier ts = new TraderSupplier();
        ts.setTraderSupplierId(id);
        ts.setIsTop(isTop);
        ts.setUpdater(user.getUserId());
        String url = httpUrl + ErpConst.TRADER_SUPPLIER_TOP;
        return update(ts, url);
    }

    /**
     * <b>Description:</b><br> 是否禁用
     *
     * @param id
     * @param isDisabled
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月17日 上午10:41:36
     */
    @Override
    public ResultInfo isDisabled(Integer userId, Integer id, Integer isDisabled, String disabledReason) {
        TraderSupplier ts = new TraderSupplier();
        ts.setTraderSupplierId(id);
        ts.setIsEnable(isDisabled);
        ts.setUpdater(userId);
        ts.setModTime(System.currentTimeMillis());
        if (isDisabled == 0) {
            ts.setDisableTime(System.currentTimeMillis());
            ts.setDisableReason(disabledReason);
        }
        if (ErpConst.ONE.equals(isDisabled)) {
            ts.setDisableTime(0L);
            ts.setDisableReason("");
        }
        int result = traderSupplierMapper.updateByPrimaryKeySelective(ts);
        if (result > 0) {
            return new ResultInfo(0, "操作成功");
        }
        return new ResultInfo(-1, "操作失败");
    }

    /**
     * <b>Description:</b><br> 更新
     *
     * @param ts
     * @param url
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月17日 上午11:47:29
     */
    public ResultInfo update(TraderSupplier ts, String url) {
        final TypeReference<ResultInfo<TraderSupplier>> TypeRef = new TypeReference<ResultInfo<TraderSupplier>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, ts, clientId, clientKey, TypeRef);
            return result;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    // todo:新增供应商需要重构，有大bug
    @Override
    public TraderSupplier saveSupplier(Trader trader, Integer userId) {
        //接口调用
        String url = httpUrl + "tradersupplier/addsupplierinfo.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderSupplier>> TypeRef2 = new TypeReference<ResultInfo<TraderSupplier>>() {
        };
        TraderSupplier traderSupplier;
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, trader, clientId, clientKey, TypeRef2);
            traderSupplier = (TraderSupplier) result2.getData();
            if (null != traderSupplier) {
                //保存归属
                RTraderJUser rTraderJUser = new RTraderJUser();
                rTraderJUser.setTraderId(traderSupplier.getTraderId());
                rTraderJUser.setUserId(userId);
                rTraderJUser.setTraderType(ErpConst.TWO);
                rTraderJUserMapper.insert(rTraderJUser);
                logger.info("【新增交易者归属变更记录】新增了交易者归属绑定关系 - traderId:{},fromUser:{}, toUser:{}",
                        rTraderJUser.getTraderId(), "无", rTraderJUser.getUserId());
                this.insertRTraderJUserModifyRecordWhenInsertrTraderJUser(rTraderJUser);
            }
        } catch (Exception e) {
            logger.error("【调用DBCenter新增供应商异常】", e);
            throw new ServiceException("【调用DBCenter新增供应商异常】");
        }

        return traderSupplier;
    }

    /**
     * 根据生产厂商名称新增【供应商生产厂商】
     *
     * @param traderName
     * @param userId
     * @return
     */
    @Override
    public TraderSupplier saveSupplierByName(String traderName, Integer userId) {
        Trader trader = new Trader();
        Long time = DateUtil.sysTimeMillis();

        //交易者基本信息
        trader.setCompanyId(ErpConst.NJ_COMPANY_ID);
        trader.setIsEnable(ErpConst.ONE);
        trader.setTraderName(traderName);

        trader.setAddTime(time);
        trader.setCreator(userId);
        trader.setModTime(time);
        trader.setUpdater(userId);

        // 生产厂商 Type:1
        TraderSupplier traderSupplier = new TraderSupplier();
        traderSupplier.setIsEnable(ErpConst.ONE);
        traderSupplier.setAddTime(time);
        traderSupplier.setCreator(userId);
        traderSupplier.setModTime(time);
        traderSupplier.setUpdater(userId);
        traderSupplier.setTraderType(ErpConst.ONE);
        trader.setTraderSupplier(traderSupplier);

        return this.saveSupplier(trader, userId);
    }


    /**
     * @return void
     * @Description 新增交易者归属信息更改记录
     * <AUTHOR>
     * @Date 17:09 2021/8/4
     * @Param [rTrader]
     **/
    private void insertRTraderJUserModifyRecordWhenInsertrTraderJUser(RTraderJUser rTrader) {
        RTraderJUserModifyRecord rTraderJUserModifyRecord = new RTraderJUserModifyRecord();
        rTraderJUserModifyRecord.setTraderType(rTrader.getTraderType());
        rTraderJUserModifyRecord.setUserId(rTrader.getUserId());
        rTraderJUserModifyRecord.setCreator(rTrader.getUserId());
        rTraderJUserModifyRecord.setStartTime(DateUtil.gainNowDate());
        rTraderJUserModifyRecord.setTraderId(rTrader.getTraderId());
        rTraderJUserModifyRecordMapper.insert(rTraderJUserModifyRecord);
    }

    @Override
    public TraderSupplierVo getTraderSupplierBaseInfo(TraderSupplier traderSupplier) {
        // VDERP-10684 错误日志处理
        logger.info("供应商信息{} {}", traderSupplier.getTraderId(), traderSupplier.getTraderSupplierId());
        TraderSupplierVo traderSupplierBaseInfo = traderSupplierMapper.getTraderSupplierBaseInfo(traderSupplier);
        //查询供应商的企业宣传片地址
        if (traderSupplierBaseInfo != null) {
            Attachment att = new Attachment();
            att.setAttachmentType(461);
            att.setAttachmentFunction(647);
            att.setRelatedId(traderSupplierBaseInfo.getTraderSupplierId());
            List<Attachment> cpms = attachmentMapper.getAttachmentList(att);
            traderSupplierBaseInfo.setCompanyUriList(cpms);
        }

        // 客户信息
        TraderCustomerVo traderCustomerVo = traderCustomerMapper.getCustomerByTraderId(traderSupplierBaseInfo.getTraderId());
        if (null != traderCustomerVo) {
            TraderCustomerVo customerSaleorderInfo = traderCustomerMapper.getCustomerSaleorderInfo(traderSupplierBaseInfo.getTraderId());
            if (null != customerSaleorderInfo.getBuyCount()) {
                traderCustomerVo.setBuyCount(customerSaleorderInfo.getBuyCount());
            }
            if (null != customerSaleorderInfo.getBuyMoney()) {
                traderCustomerVo.setBuyMoney(customerSaleorderInfo.getBuyMoney());
            } else {
                traderCustomerVo.setBuyMoney(new BigDecimal(0));
            }

            traderSupplierBaseInfo.setTraderCustomerVo(traderCustomerVo);

            User sale = userMapper.getUserByTraderId(traderCustomerVo.getTraderId(), ErpConst.ONE);

            if (null != sale) {
                traderSupplierBaseInfo.setOwnerSale(sale.getUsername());
            }
        }

        TraderBussinessData traderBussinessData = traderDataService.getTraderBussinessData(traderSupplierBaseInfo.getTraderId(), 2);

        if (traderBussinessData != null) {
            traderSupplierBaseInfo.setOrderCount(traderBussinessData.getOrderTimes());
        } else {
            traderSupplierBaseInfo.setOrderCount(0);
        }


        return traderSupplierBaseInfo;
    }

    @Override
    public TraderSupplierVo getTraderSupplierManageInfo(TraderSupplier traderSupplier) {
        //接口调用
        String url = httpUrl + "tradersupplier/getsuppliermanageinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderSupplierVo>> TypeRef2 = new TypeReference<ResultInfo<TraderSupplierVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderSupplier, clientId, clientKey, TypeRef2);
            TraderSupplierVo res = (TraderSupplierVo) result2.getData();
            if (null != res) {
                User sale = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.TWO);
                if (null != sale) {
                    res.setOwnerSale(sale.getUsername());
                }

                CommunicateRecord communicateRecord = new CommunicateRecord();

                communicateRecord.setTraderId(res.getTraderId());
                communicateRecord.setTraderType(ErpConst.TWO);
                communicateRecord.setCompanyId(traderSupplier.getCompanyId());

                CommunicateRecord customerCommunicateCount = communicateRecordMapper.getTraderCommunicateCount(communicateRecord);
                if (null != customerCommunicateCount) {
                    res.setCommuncateCount(customerCommunicateCount.getCommunicateCount());
                    res.setLastCommuncateTime(customerCommunicateCount.getLastCommunicateTime());
                }
                User user = null;
                user = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.TWO);
                if (null != user) {
                    res.setPersonal(user.getUsername());
                }
            }
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderSupplierVo getOrderCoverInfo(TraderOrderGoods traderOrderGoods) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderSupplierVo>> TypeRef = new TypeReference<ResultInfo<TraderSupplierVo>>() {
        };
        String url = httpUrl + "trader/getordercoverinfo.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, traderOrderGoods, clientId, clientKey, TypeRef);
            TraderSupplierVo res = (TraderSupplierVo) result.getData();

            //去重订单覆盖品类，订单覆盖品牌，订单覆盖产品+
            List<String> categoryNameList = new ArrayList<>();
            List<String> brandNameList = new ArrayList<>();
            List<String> goodNameList = new ArrayList<>();

            List<TraderOrderGoods> traderOrderGoodsList = res.getTraderOrderGoodsList();
            if (traderOrderGoodsList != null && traderOrderGoodsList.size() > 0) {
                for (TraderOrderGoods traderOrderGoods1 : traderOrderGoodsList) {
                    categoryNameList.add(traderOrderGoods1.getCategoryName());
                    brandNameList.add(traderOrderGoods1.getBrandName());
                    goodNameList.add(traderOrderGoods1.getGoodsName());
                }
            }
            res.setCategoryNameList(new ArrayList(new HashSet(categoryNameList)));
            res.setBrandNameList(new ArrayList(new HashSet(brandNameList)));
            res.setGoodNameList(new ArrayList(new HashSet(goodNameList)));

            return res;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return null;
    }

    @Override
    public TraderSupplier saveEditManageInfo(TraderSupplier traderSupplier, HttpServletRequest request,
                                             HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();

        Trader trader = new Trader();
        trader.setCompanyId(user.getCompanyId());

        traderSupplier.setModTime(time);
        traderSupplier.setUpdater(user.getUserId());
        traderSupplier.setTrader(trader);

        //标签
        if (null != request.getParameterValues("tagId")) {//标签库标签
            String[] tagIds = request.getParameterValues("tagId");
            List<Tag> tagList = new ArrayList<>();
            for (String tagId : tagIds) {
                Tag tag = new Tag();
                tag.setTagType(SysOptionConstant.ID_31);
                tag.setTagId(Integer.parseInt(tagId));
                tag.setCompanyId(user.getCompanyId());
                tagList.add(tag);
            }

            traderSupplier.setTag(tagList);
        }
        if (null != request.getParameterValues("tagName")) {//自定义标签
            String[] tagNames = request.getParameterValues("tagName");
            List<String> tagNameList = new ArrayList<>();
            for (String tagName : tagNames) {
                tagNameList.add(tagName);
            }

            traderSupplier.setTagName(tagNameList);
        }

        //接口调用
        String url = httpUrl + "tradersupplier/saveeditmanageinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderSupplier>> TypeRef2 = new TypeReference<ResultInfo<TraderSupplier>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderSupplier, clientId, clientKey, TypeRef2);
            TraderSupplier res = (TraderSupplier) result2.getData();

            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderSupplier saveEditBaseInfo(Trader trader, HttpServletRequest request, HttpSession session) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();

        if (Integer.parseInt(request.getParameter("zone")) > 0) {
            trader.setAreaId(Integer.parseInt(request.getParameter("zone")));
            trader.setAreaIds(request.getParameter("province") + "," + request.getParameter("city") + "," + request.getParameter("zone"));
        } else {
            trader.setAreaId(Integer.parseInt(request.getParameter("city")));
            trader.setAreaIds(request.getParameter("province") + "," + request.getParameter("city") + "," + request.getParameter("zone"));
        }

        trader.setCompanyId(user.getCompanyId());

        trader.setModTime(time);
        trader.setUpdater(user.getUserId());

        TraderSupplier traderSupplier = trader.getTraderSupplier();
        traderSupplier.setModTime(time);
        traderSupplier.setUpdater(user.getUserId());
        traderSupplier.setHotTelephone(request.getParameter("hotTelephone"));
        traderSupplier.setLogisticsName(request.getParameter("logisticsName"));
        traderSupplier.setServiceTelephone(request.getParameter("serviceTelephone"));
        traderSupplier.setAfterSaleManager(request.getParameter("afterSaleManager"));

        traderSupplier.setInstallServiceContactName(request.getParameter("installServiceContactName"));
        traderSupplier.setInstallServiceContactWay(request.getParameter("installServiceContactWay"));
        traderSupplier.setTechnicalDirectContactName(request.getParameter("technicalDirectContactName"));
        traderSupplier.setTechnicalDirectContactWay(request.getParameter("technicalDirectContactWay"));
        traderSupplier.setMaintenanceContactName(request.getParameter("maintenanceContactName"));
        traderSupplier.setMaintenanceContactWay(request.getParameter("maintenanceContactWay"));
        traderSupplier.setExchangeContactName(request.getParameter("exchangeContactName"));
        traderSupplier.setExchangeContactWay(request.getParameter("exchangeContactWay"));
        traderSupplier.setOtherContactName(request.getParameter("otherContactName"));
        traderSupplier.setOtherContactWay(request.getParameter("otherContactWay"));
        traderSupplier.setTaxPayerType(Integer.valueOf(request.getParameter("taxPayerType")));

        traderSupplier.setWebsite(request.getParameter("website"));
        traderSupplier.setWarehouseAreaId(StringUtil.isNotBlank(request.getParameter("warehouseAreaId")) ?
                Integer.parseInt(request.getParameter("warehouseAreaId")) : null);
        traderSupplier.setWarehouseAreaIds(StringUtil.isNotBlank(request.getParameter("warehouseAreaIds")) ?
                request.getParameter("warehouseAreaIds") : null);
        traderSupplier.setWarehouseAddress(StringUtil.isNotBlank(request.getParameter("warehouseAddress")) ?
                request.getParameter("warehouseAddress") : null);
        traderSupplier.setTraderType(StringUtil.isNotBlank(request.getParameter("traderTypeSup")) ?
                Integer.parseInt(request.getParameter("traderTypeSup")) : null);

        if (StringUtil.isNotBlank(request.getParameter("brief"))) {
            traderSupplier.setBrief(request.getParameter("brief"));
        }
        if (StringUtil.isNotBlank(request.getParameter("comments"))) {
            traderSupplier.setComments(request.getParameter("comments"));
        }
        //品牌
        if (null != request.getParameterValues("bussinessBrandId")) {
            String[] bussinessBrandIds = request.getParameterValues("bussinessBrandId");
            List<TraderSupplierSupplyBrand> supplyBrands = new ArrayList<>();
            for (String brandId : bussinessBrandIds) {
                TraderSupplierSupplyBrand traderSupplierSupplyBrand = new TraderSupplierSupplyBrand();
                traderSupplierSupplyBrand.setBrandId(Integer.parseInt(brandId));
                supplyBrands.add(traderSupplierSupplyBrand);
            }

            traderSupplier.setTraderSupplierSupplyBrands(supplyBrands);
        }
        //企业宣传片
        if (null != request.getParameterValues("companyUri")) {
            String[] companyUris = request.getParameterValues("companyUri");
            traderSupplier.setCompanyUris(companyUris);
        }


        //接口调用
        String url = httpUrl + "tradersupplier/saveeditbaseinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderSupplier>> TypeRef2 = new TypeReference<ResultInfo<TraderSupplier>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, trader, clientId, clientKey, TypeRef2);
            TraderSupplier res = (TraderSupplier) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public List<CommunicateRecord> getCommunicateRecordListPage(CommunicateRecord communicateRecord, Page page) {
		/*List<Integer> traderSupplerIdList = new ArrayList<>();
		if(communicateRecord.getTraderSupplierId() != null
				&& communicateRecord.getTraderSupplierId() > 0){
			traderSupplerIdList.add(communicateRecord.getTraderSupplierId());
		}
		communicateRecord.setTraderSupplierIds(traderSupplerIdList);
		
		//后期调用接口查询询价、报价、订单、售后 沟通记录
		 */
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("communicateRecord", communicateRecord);
        map.put("page", page);
        List<CommunicateRecord> communicateRecordList = communicateRecordMapper.getCommunicateRecordList(map);

        //调用接口补充信息（联系人，沟通目的、方式 ，沟通内容（标签））
        String url = httpUrl + "trader/tradercommunicaterecord.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<CommunicateRecord>>> TypeRef2 = new TypeReference<ResultInfo<List<CommunicateRecord>>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecordList, clientId, clientKey, TypeRef2);

            List<CommunicateRecord> list = (List<CommunicateRecord>) result2.getData();
            return list;
        } catch (IOException e) {
            return null;
        }

    }

    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Boolean saveAddCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,
                                      HttpSession session) throws Exception {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();
        communicateRecord.setCompanyId(user.getCompanyId());
        //communicateRecord.setCommunicateType(SysOptionConstant.ID_243);
        //communicateRecord.setRelatedId(communicateRecord.getTraderSupplierId());
        String begin = request.getParameter("begin");
        String end = request.getParameter("end");
        communicateRecord.setBegintime(DateUtil.convertLong(begin, "yyyy-MM-dd HH:mm:ss"));
        communicateRecord.setEndtime(DateUtil.convertLong(end, "yyyy-MM-dd HH:mm:ss"));

        if (request.getParameter("nextDate") != "") {
            communicateRecord.setNextContactDate(request.getParameter("nextDate"));
            communicateRecord.setIsDone(ErpConst.ZERO);
        }
        if (StringUtil.isBlank(request.getParameter("contactContent"))) {
            communicateRecord.setContactContent(request.getParameter("contactContent"));
        }
        communicateRecord.setAddTime(time);
        communicateRecord.setCreator(user.getUserId());
        communicateRecord.setModTime(time);
        communicateRecord.setUpdater(user.getUserId());

        //历史沟通信息处理
        CommunicateRecord old = new CommunicateRecord();

        old.setCommunicateType(communicateRecord.getCommunicateType());
        old.setRelatedId(communicateRecord.getRelatedId());
        communicateService.updateCommunicateDone(communicateRecord, session);

        communicateRecordMapper.insert(communicateRecord);

        //工作台-精准营销活动，需要更新活动-销售任务中的是否已沟通更新为已沟通。-Kerwin VDERP-15559
        marketPlanApiService.updateMarketPlanTraderSendMsg(communicateRecord.getTraderId());

        Integer communicateRecordId = communicateRecord.getCommunicateRecordId();
        if (communicateRecordId > 0) {
            //标签
            if (null != request.getParameterValues("tagId")) {//标签库标签
                String[] tagIds = request.getParameterValues("tagId");
                List<Tag> tagList = new ArrayList<>();
                for (String tagId : tagIds) {
                    Tag tag = new Tag();
                    tag.setTagType(SysOptionConstant.ID_32);
                    tag.setTagId(Integer.parseInt(tagId));
                    tag.setCompanyId(user.getCompanyId());
                    tagList.add(tag);
                }

                communicateRecord.setTag(tagList);
            }
            if (null != request.getParameterValues("tagName")) {//自定义标签
                String[] tagNames = request.getParameterValues("tagName");
                List<String> tagNameList = new ArrayList<>();
                for (String tagName : tagNames) {
                    tagNameList.add(tagName);
                }

                communicateRecord.setTagName(tagNameList);
            }

            //接口调用
            String url = httpUrl + "trader/saveaddcommunicatetag.htm";

            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
            };
            try {
                ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecord, clientId, clientKey, TypeRef2);
                Integer res = (Integer) result2.getCode();

                if (res.equals(0)) {
                    return true;
                }
                return false;
            } catch (IOException e) {
                return false;
            }
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Boolean saveEditCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,
                                       HttpSession session) throws Exception {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Long time = DateUtil.sysTimeMillis();

        String begin = request.getParameter("begin");
        String end = request.getParameter("end");
        communicateRecord.setBegintime(DateUtil.convertLong(begin, "yyyy-MM-dd HH:mm:ss"));
        communicateRecord.setEndtime(DateUtil.convertLong(end, "yyyy-MM-dd HH:mm:ss"));

        if (request.getParameter("nextDate") != "") {
            communicateRecord.setNextContactDate(request.getParameter("nextDate"));
        }

        communicateRecord.setModTime(time);
        communicateRecord.setUpdater(user.getUserId());
        communicateRecord.setCompanyId(user.getCompanyId());
        Integer succ = 0;
        if (null != communicateRecord.getCoid() && communicateRecord.getCoid() != "") {//呼叫中心编辑沟通记录
            communicateRecord.setCreator(user.getUserId());
            succ = communicateRecordMapper.updateByCoidAUserId(communicateRecord);
        } else {

            succ = communicateRecordMapper.update(communicateRecord);
        }
        if (succ > 0) {

            // 标签
            if (null != request.getParameterValues("tagId")) {// 标签库标签
                String[] tagIds = request.getParameterValues("tagId");
                List<Tag> tagList = new ArrayList<>();
                for (String tagId : tagIds) {
                    Tag tag = new Tag();
                    tag.setTagType(SysOptionConstant.ID_32);
                    tag.setTagId(Integer.parseInt(tagId));
                    tag.setCompanyId(user.getCompanyId());
                    tagList.add(tag);
                }

                communicateRecord.setTag(tagList);
            }
            if (null != request.getParameterValues("tagName")) {// 自定义标签
                String[] tagNames = request.getParameterValues("tagName");
                List<String> tagNameList = new ArrayList<>();
                for (String tagName : tagNames) {
                    tagNameList.add(tagName);
                }

                communicateRecord.setTagName(tagNameList);
            }

            // 接口调用
            String url = httpUrl + "trader/saveaddcommunicatetag.htm";

            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
            };
            try {
                ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecord, clientId, clientKey, TypeRef2);
                Integer res = (Integer) result2.getCode();

                if (res.equals(0)) {
                    return true;
                }
                return false;
            } catch (IOException e) {
                return false;
            }
        }
        return false;
    }

    @Override
    public Integer getUserSupplierNum(Integer userId) {
        RTraderJUser rTraderJUser = new RTraderJUser();
        rTraderJUser.setUserId(userId);
        rTraderJUser.setTraderType(ErpConst.TWO);
        List<RTraderJUser> userTrader = rTraderJUserMapper.getUserTrader(rTraderJUser);

        return userTrader.size();
    }

    @Override
    public Map<String, Object> getUserTraderByTraderNameListPage(RTraderJUser rTraderJUser, Page page) {
        List<RTraderJUser> rTraderJUserList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        try {

            List<TraderSupplier> traderList = null;
            Trader trader = new Trader();
            trader.setCompanyId(rTraderJUser.getCompanyId());
            trader.setTraderName(rTraderJUser.getTraderName());

            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<TraderSupplier>>> TypeRef = new TypeReference<ResultInfo<List<TraderSupplier>>>() {
            };
            String url = httpUrl + "tradersupplier/getusersupplierbytradernamelistpage.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, trader, clientId, clientKey, TypeRef, page);
            traderList = (List<TraderSupplier>) result.getData();
            page = result.getPage();

            if (traderList.size() > 0) {
                for (TraderSupplier t : traderList) {
                    RTraderJUser traderJUser = new RTraderJUser();
                    traderJUser.setTraderId(t.getTraderId());
                    traderJUser.setTraderName(t.getTrader().getTraderName());
                    traderJUser.setChangeTimes(t.getOrderTimes());
                    if (null != t.getSysOptionDefinition()
                            && null != t.getSysOptionDefinition().getTitle()) {
                        traderJUser.setLevel(t.getSysOptionDefinition().getTitle());
                    }

                    String region = (String) regionService.getRegion(t.getTrader().getAreaId(), 2);
                    if (null != region) {
                        traderJUser.setAreaStr(region);
                    }

                    User sale = userMapper.getUserByTraderId(t.getTraderId(), ErpConst.TWO);
                    if (null != sale) {
                        traderJUser.setOwnerUser(sale.getUsername());
                    }

                    rTraderJUserList.add(traderJUser);
                }
            }

            map.put("list", rTraderJUserList);
            map.put("page", page);
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return map;
    }

    @Override
    public Boolean assignSingleSupplier(Integer traderId, Integer single_to_user) {
        RTraderJUser rTraderJUser = new RTraderJUser();
        rTraderJUser.setTraderType(ErpConst.TWO);
        rTraderJUser.setTraderId(traderId);
        Integer preUserId = null;
        RTraderJUser ru = rTraderJUserMapper.getUserByTraderId(rTraderJUser.getTraderId());
        if (ru != null) {
            preUserId = ru.getUserId();
        }
        rTraderJUserMapper.deleteRTraderJUser(rTraderJUser);

        logger.info("【过期交易者归属变更记录】删除了交易者归属绑定关系 - traderId:{},oldUser:{}",
                rTraderJUser.getTraderId(), preUserId);
        Long nowTimeMillisecond = DateUtil.gainNowDate();
        deleteOldRTraderJUserModifyRecord(rTraderJUser, single_to_user, nowTimeMillisecond);

        RTraderJUser traderJUser = new RTraderJUser();
        traderJUser.setTraderType(ErpConst.TWO);
        traderJUser.setTraderId(traderId);
        traderJUser.setUserId(single_to_user);
        int insert = rTraderJUserMapper.insert(traderJUser);
        logger.info("【新增交易者归属变更记录】新增了交易者归属绑定关系 - traderId:{},fromUser:{}, toUser:{}",
                traderJUser.getTraderId(), preUserId, traderJUser.getUserId());
        insertRTraderJUserModifyRecordWhenUpdateTraderJUser(traderJUser, preUserId, nowTimeMillisecond);

        if (insert > 0) {
            return true;
        }
        return false;
    }

    private void insertRTraderJUserModifyRecordWhenUpdateTraderJUser(RTraderJUser traderJUser, Integer oldUserId, Long nowTimeMillisecond) {
        //记录交易者归属变更记录(新增最新的记录)
        RTraderJUserModifyRecord rTraderJUserModifyRecordAdd = new RTraderJUserModifyRecord();
        rTraderJUserModifyRecordAdd.setTraderType(traderJUser.getTraderType());
        rTraderJUserModifyRecordAdd.setUserId(traderJUser.getUserId());
        rTraderJUserModifyRecordAdd.setOldUserId(traderJUser.getUserId());
        rTraderJUserModifyRecordAdd.setCreator(oldUserId);
        rTraderJUserModifyRecordAdd.setStartTime(nowTimeMillisecond);
        rTraderJUserModifyRecordAdd.setTraderId(traderJUser.getTraderId());
        rTraderJUserModifyRecordMapper.insert(rTraderJUserModifyRecordAdd);
    }

    private void deleteOldRTraderJUserModifyRecord(RTraderJUser rTraderJUser, Integer updater, Long nowTimeMillisecond) {
        //记录交易者归属变更记录(删除掉以前的记录)
        RTraderJUserModifyRecord rTraderJUserModifyRecord = new RTraderJUserModifyRecord();
        rTraderJUserModifyRecord.setTraderId(rTraderJUser.getTraderId());
        rTraderJUserModifyRecord.setEndTime(nowTimeMillisecond);
        rTraderJUserModifyRecord.setUpdater(updater);
        rTraderJUserModifyRecordMapper.updateEndTimeByTraderId(rTraderJUserModifyRecord);
    }


    @Override
    public Boolean assignBatchSupplier(Integer from_user, Integer batch_to_user) {
        int update = rTraderJUserMapper.update(from_user, batch_to_user, ErpConst.TWO);

        List<RTraderJUserModifyRecord> rTraderJUserModifyRecordOldList =
                rTraderJUserModifyRecordMapper.getRTraderJUserModifyRecordByUserId(from_user, ErpConst.TWO);
        logger.info("【过期交易者归属变更记录】批量修改了交易者userId:{}的归属绑定关系->userId:{} count:{}",
                from_user, batch_to_user, rTraderJUserModifyRecordOldList.size());
        //批量更新交易者归属记录变更信息(删除)
        Long nowTimeMillisecond = DateUtil.gainNowDate();
        if (CollectionUtils.isNotEmpty(rTraderJUserModifyRecordOldList)) {
            rTraderJUserModifyRecordMapper.updateEndTimeByUserId(rTraderJUserModifyRecordOldList, nowTimeMillisecond, batch_to_user);
            //批量更新交易者归属记录变更信息(新增)
            List<RTraderJUserModifyRecord> rTraderJUserModifyRecordAddList = new ArrayList<>();
            RTraderJUserModifyRecord rTraderJUserModifyRecordTemp = null;
            for (RTraderJUserModifyRecord rTraderJUserModifyRecordOld : rTraderJUserModifyRecordOldList) {
                rTraderJUserModifyRecordTemp = new RTraderJUserModifyRecord();
                rTraderJUserModifyRecordTemp.setTraderType(rTraderJUserModifyRecordOld.getTraderType());
                rTraderJUserModifyRecordTemp.setUserId(batch_to_user);
                rTraderJUserModifyRecordTemp.setOldUserId(rTraderJUserModifyRecordOld.getUserId());
                rTraderJUserModifyRecordTemp.setCreator(batch_to_user);
                rTraderJUserModifyRecordTemp.setStartTime(nowTimeMillisecond);
                rTraderJUserModifyRecordTemp.setTraderId(rTraderJUserModifyRecordOld.getTraderId());
                rTraderJUserModifyRecordAddList.add(rTraderJUserModifyRecordTemp);
            }
            if (CollectionUtils.isNotEmpty(rTraderJUserModifyRecordAddList)) {
                logger.info("【新增交易者归属变更记录】批量修改了交易者userId:{}的归属绑定关系->userId:{} count:{}",
                        from_user, batch_to_user, rTraderJUserModifyRecordAddList.size());
                rTraderJUserModifyRecordMapper.saveRTraderJUserModifyRecordBatch(rTraderJUserModifyRecordAddList);
            }
        }

        if (update > 0) {
            return true;
        }
        return false;
    }

    @Override
    public TraderSupplierVo getSupplierInfoByTraderSupplier(TraderSupplier traderSupplier) {
        //接口调用
        String url = httpUrl + "tradersupplier/getsupplierinfobytradersupplier.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderSupplierVo>> TypeRef2 = new TypeReference<ResultInfo<TraderSupplierVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderSupplier, clientId, clientKey, TypeRef2);
            TraderSupplierVo res = (TraderSupplierVo) result2.getData();

            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderSupplierVo getTraderSupplierInfo(TraderSupplier ts) {
        //接口调用
        String url = httpUrl + "tradersupplier/gettradersupplierinfo.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderSupplierVo>> TypeRef2 = new TypeReference<ResultInfo<TraderSupplierVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, ts, clientId, clientKey, TypeRef2);
            TraderSupplierVo res = (TraderSupplierVo) result2.getData();
            if (null != res) {
                //数据处理(地区)
                if (res.getAreaId() > 0) {
                    String region = (String) regionService.getRegion(res.getAreaId(), 2);
                    res.setTraderSupplierAddress(region);
                }
                User sale = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.TWO);
                if (null != sale) {
                    res.setOwnerSale(sale.getUsername());
                }
            }
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderAccountPeriodApply getTraderSupplierLastAccountPeriodApply(Integer traderId) {
        TraderAccountPeriodApply accountPeriodApply = new TraderAccountPeriodApply();
        accountPeriodApply.setTraderId(traderId);
        accountPeriodApply.setTraderType(ErpConst.TWO);
        // 接口调用
        String url = httpUrl + "trader/gettraderlastaccountperiodapply.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderAccountPeriodApply>> TypeRef2 = new TypeReference<ResultInfo<TraderAccountPeriodApply>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, accountPeriodApply, clientId, clientKey,
                    TypeRef2);
            TraderAccountPeriodApply res = (TraderAccountPeriodApply) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public TraderSupplierVo getTraderSupplierForAccountPeriod(TraderSupplierVo traderSupplierVo) {
        // 接口调用
        String url = httpUrl + "tradersupplier/gettradersupplierforaccountperiod.htm";

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<TraderSupplierVo>> TypeRef2 = new TypeReference<ResultInfo<TraderSupplierVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderSupplierVo, clientId, clientKey,
                    TypeRef2);
            TraderSupplierVo res = (TraderSupplierVo) result2.getData();
            return res;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public List<TraderSupplierVo> getMainSupplyListByGoodsId(Integer goodsId) {
        List<TraderSupplierVo> list = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<TraderSupplierVo>>> TypeRef = new TypeReference<ResultInfo<List<TraderSupplierVo>>>() {
        };
        String url = httpUrl + "tradersupplier/getmainsupplylistbygoodsid.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, goodsId, clientId, clientKey, TypeRef);
            list = (List<TraderSupplierVo>) result.getData();
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return list;
    }

    /**
     * <b>Description:</b><br> 查询沟通记录的主键
     *
     * @param traderSupplierVo
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2018年1月16日 下午1:41:50
     */
    @Override
    public List<Integer> getTagTraderIdList(TraderSupplierVo traderSupplierVo) {
        String url = httpUrl + ErpConst.GET_TAG_TRADERLIST;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderSupplierVo, clientId, clientKey, TypeRef2);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            List<Integer> list = (List<Integer>) JSONArray.toCollection(jsonArray, Integer.class);
            return list;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public Map<String, Object> getBusinessListPage(BuyorderGoodsVo buyorderGoodsVo, Page page) {
        // 调用接口
        String url = httpUrl + "tradersupplier/getbusinesslistpage.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<BuyorderGoodsVo>>> TypeRef = new TypeReference<ResultInfo<List<BuyorderGoodsVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, buyorderGoodsVo, clientId, clientKey,
                    TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<BuyorderGoodsVo> list = (List<BuyorderGoodsVo>) result.getData();
            page = result.getPage();
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            map.put("page", page);
            return map;
        } catch (IOException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public List<RegisterSkuVo> getRegisterList(Integer traderId, Page page) {
        List<String> skuNoList = buyorderGoodsMapper.getSkuNoListByTraderId(traderId);
        if (CollectionUtils.isEmpty(skuNoList)) {
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("page", page);
        param.put("traderId", traderId);
        param.put("skuNoList", skuNoList);
        List<RegisterSkuVo> list = registrationNumberMapper.getSupplierRegisterListPage(param);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        Map<String, Object> attachParamMap = new HashMap<>();
        // 附件类型
        attachParamMap.put("attachmentType", CommonConstants.SUPPLIER_REGISTER_ATTACHMENT_TYPE);
        attachParamMap.put("attachmentFunction", Collections.singletonList(CommonConstants.SUPPLIER_REGISTER_ATTACHMENT_FUNCTION));
        SupplierRegistrationNumber queryNumber = new SupplierRegistrationNumber();
        queryNumber.setTraderId(traderId);
        list.stream().forEach(item -> {
            queryNumber.setRegistrationNumberId(item.getRegistrationNumberId());
            SupplierRegistrationNumber supplierRegistration = supplierRegistrationNumberMapper.getInfoByTraderIdAndNumberId(queryNumber);
            if (supplierRegistration != null) {
                item.setCheckStatus((int) supplierRegistration.getCheckStatus());
                item.setCheckTime(supplierRegistration.getCheckTime());
                item.setSupplierRegistrationNumberId(supplierRegistration.getSupplierRegistrationNumberId());
            }
            if (item.getSupplierRegistrationNumberId() != null) {
                attachParamMap.put("registrationNumberId", item.getSupplierRegistrationNumberId());
                item.setAttachments(attachmentMapper.getAttachmentsList(attachParamMap));
                item.setAttachmentMapStr(convertAttachmentList2JsonStr(item.getAttachments()));
            }
            String[] skuNoArray = item.getSkuNoStr().split(",");
            item.setSkuList(coreSkuMapper.getSkuListByNoList(Arrays.asList(skuNoArray)));
        });
        return list;
    }

    private String convertAttachmentList2JsonStr(List<Attachment> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "[]";
        }
        JSONArray jsonArray = new JSONArray();
        for (Attachment a : list) {
            JSONObject attachmentMap = new JSONObject();
            attachmentMap.put("message", "操作成功");
            attachmentMap.put("httpUrl", api_http + a.getDomain());
            // uri
            String uri = a.getUri();
            if (EmptyUtils.isEmpty(uri)) {
                continue;
            }
            String[] uriArray = uri.split("/");
            String fileName = uriArray[uriArray.length - 1];
            String fileNameTemp = "/" + fileName;
            // 文件后缀
            String[] prefixArray = fileNameTemp.split("\\.");
            String prefix = prefixArray[prefixArray.length - 1];
            // 去除路径名
            String filePath = uri.replaceAll(fileNameTemp, "");
            attachmentMap.put("fileName", fileName);
            attachmentMap.put("filePath", uri);
            attachmentMap.put("prefix", prefix);
            attachmentMap.put("domain", a.getDomain());
            attachmentMap.put("id", a.getAttachmentId());
            jsonArray.add(attachmentMap);
        }
        return jsonArray.toString();
    }

    @Override
    public RegistrationAttachment addAttachment(RegistrationAttachment attachment, User user) {
        SupplierRegistrationNumber preNumber = getSupplierRegistration(attachment, user);
        Attachment attach = new Attachment();
        attach.setAttachmentType(CommonConstants.SUPPLIER_REGISTER_ATTACHMENT_TYPE);
        attach.setAttachmentFunction(CommonConstants.SUPPLIER_REGISTER_ATTACHMENT_FUNCTION);
        attach.setRelatedId(preNumber.getSupplierRegistrationNumberId());
        BeanUtils.copyProperties(attachment, attach);
        attachmentMapper.insertSelective(attach);
        attachment.setAttachmentId(attach.getAttachmentId());
        preNumber.setCheckStatus((byte) 0);
        supplierRegistrationNumberMapper.updateByPrimaryKeySelective(preNumber);
        return attachment;
    }

    @Override
    public void deleteAttachment(RegistrationAttachment attachment, User user) {
        attachmentMapper.deleteByPrimaryKey(attachment.getAttachmentId());
        SupplierRegistrationNumber preNumber = getSupplierRegistration(attachment, user);
        preNumber.setCheckStatus((byte) 0);
        supplierRegistrationNumberMapper.updateByPrimaryKeySelective(preNumber);
    }

    @Override
    public SupplierRegistrationNumber checkRegistration(RegistrationAttachment attachment, User user) {
        SupplierRegistrationNumber preNumber = getSupplierRegistration(attachment, user);
        preNumber.setCheckStatus((byte) 1);
        preNumber.setCheckTime(System.currentTimeMillis());
        preNumber.setCheckUserId(user.getUserId());
        supplierRegistrationNumberMapper.updateByPrimaryKeySelective(preNumber);
        preNumber.setCheckTimeStr(DateUtil.convertString(preNumber.getCheckTime(), DateUtil.TIME_FORMAT));
        return preNumber;
    }

    @Override
    public String getTyRegionByName(String operateName) {
        if (StringUtil.isBlank(operateName)) {
            return "";
        }
        String url = tyAliUrl + "/tianyan/getOperateInfo";
        ReqProductDto param = new ReqProductDto();
        param.setOperateName(operateName);
        com.alibaba.fastjson.TypeReference<RestfulResult<ResOperateCompanyInfoDto>> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult<ResOperateCompanyInfoDto>>() {
        };
        logger.info("天眼查地址查询请求参数信息 operateName:{}", operateName);
        RestfulResult<ResOperateCompanyInfoDto> resultInfo = HttpRestClientUtil.restPost(url, typeReference, null, param);
        logger.info("天眼查地址查询返回结果信息 resultInfo:{}", JSON.toJSONString(resultInfo));
        if (resultInfo == null || !resultInfo.isSuccess() || resultInfo.getData() == null) {
            return "";
        }
        return resultInfo.getData().getAddress();
    }

    private SupplierRegistrationNumber getSupplierRegistration(RegistrationAttachment attachment, User user) {
        SupplierRegistrationNumber query = new SupplierRegistrationNumber();
        query.setTraderId(attachment.getTraderId());
        query.setRegistrationNumberId(attachment.getRegistrationNumberId());
        SupplierRegistrationNumber resNumber = supplierRegistrationNumberMapper.getInfoByTraderIdAndNumberId(query);
        if (resNumber == null || resNumber.getSupplierRegistrationNumberId() == null) {
            query.setCreator(user.getUserId());
            query.setAddTime(System.currentTimeMillis());
            supplierRegistrationNumberMapper.insertSelective(query);
            return query;
        }
        return resNumber;
    }


    @Override
    public Integer isExistManufacturer(String manufacturerName, boolean returnTraderId) {
        TraderSupplier traderSupplierQuery = traderSupplierMapper.getSupplierInfoByTraderName(manufacturerName);
        if (Objects.isNull(traderSupplierQuery) || Objects.isNull(traderSupplierQuery.getTraderId())) {
            return null;
        }
        if (returnTraderId) {
            return traderSupplierQuery.getTraderId();
        }

        return traderSupplierQuery.getTraderSupplierId();
    }


    @Transactional(rollbackFor = Exception.class, readOnly = true)
    @Override
    public Manufacturer traderSupplierTransformToManufacturer(String traderName) {
        Objects.requireNonNull(traderName, "生产企业名称不可为空");

        TraderSupplier traderSupplier = traderSupplierMapper.getSupplierInfoByTraderName(traderName);
        if (Objects.isNull(traderSupplier) || Objects.isNull(traderSupplier.getTraderId())) {
            return null;
        }

        Integer traderId = traderSupplier.getTraderId();
        Manufacturer manufacturer = new Manufacturer();
        manufacturer.setRelateId(traderSupplier.getTraderId());
        manufacturer.setManufacturerName(traderName);

        List<TraderCertificateVo> traderCertificateVo = traderCertificateMapper.getTraderCertificateVo(traderId, ErpConst.TWO);
        if (traderCertificateVo.size() == 0) {
            return manufacturer;
        }

        Map<Integer, List<TraderCertificateVo>> traderCertificateMapByType =
                traderCertificateVo.stream().collect(Collectors.groupingBy(TraderCertificateVo::getSysOptionDefinitionId));

        // 营业执照
        List<TraderCertificateVo> businessVo = traderCertificateMapByType.get(SysOptionConstant.ID_25);
        if (CollectionUtils.isNotEmpty(businessVo)) {
            TraderCertificateVo b = businessVo.get(0);

            if (b.getBegintime() != null && b.getBegintime() > 0) {
                manufacturer.setBcStartTime(DateUtil.getDateFormatByTime(b.getBegintime()));
            }
            if (b.getEndtime() != null && b.getEndtime() > 0) {
                manufacturer.setBcEndTime(DateUtil.getDateFormatByTime(b.getEndtime()));
            }

            if (b.getIssueDate() != null && b.getIssueDate() > 0) {
                manufacturer.setBcIssueDate(DateUtil.getDateFormatByTime(b.getIssueDate()));
            }

            Integer function = ManufacturerConstants.SUPPLIER_TO_ATTACHMENT_INDEX.get(SysOptionConstant.ID_25);

            manufacturer.setYzAttachments(buildAttachmentList(function, businessVo));
        }

        // 生产企业生产产品登记表
        List<TraderCertificateVo> registrationFormVo = traderCertificateMapByType.get(SysOptionConstant.ID_1102);
        if (CollectionUtils.isNotEmpty(registrationFormVo)) {
            TraderCertificateVo rf = registrationFormVo.get(0);
            if (rf.getBegintime() != null && rf.getBegintime() > 0) {
                manufacturer.setPepStartTime(DateUtil.getDateFormatByTime(rf.getBegintime()));

            }
            if (rf.getEndtime() != null && rf.getEndtime() > 0) {
                manufacturer.setPepEndTime(DateUtil.getDateFormatByTime(rf.getEndtime()));
            }

            Integer function = ManufacturerConstants.SUPPLIER_TO_ATTACHMENT_INDEX.get(SysOptionConstant.ID_1102);

            manufacturer.setDjbAttachments(buildAttachmentList(function, registrationFormVo));
        }

        // 医疗器械生产许可证
        List<TraderCertificateVo> productionVo = traderCertificateMapByType.get(SysOptionConstant.ID_439);
        if (CollectionUtils.isNotEmpty(productionVo)) {
            TraderCertificateVo tc = productionVo.get(0);
            if (tc.getBegintime() != null && tc.getBegintime() > 0) {
                manufacturer.setPeStartTime(DateUtil.getDateFormatByTime(tc.getBegintime()));

            }
            if (tc.getEndtime() != null && tc.getEndtime() > 0) {
                manufacturer.setPeEndTime(DateUtil.getDateFormatByTime(tc.getEndtime()));
            }

            manufacturer.setProductCompanyLicence(tc.getSn());

            Integer function = ManufacturerConstants.SUPPLIER_TO_ATTACHMENT_INDEX.get(SysOptionConstant.ID_439);

            manufacturer.setScAttachments(buildAttachmentList(function, productionVo));
        }

        // 第一类医疗器械生产备案凭证
        List<TraderCertificateVo> recordCertificateVo = traderCertificateMapByType.get(SysOptionConstant.ID_1101);
        if (CollectionUtils.isNotEmpty(recordCertificateVo)) {
            TraderCertificateVo rc = recordCertificateVo.get(0);
            if (rc.getBegintime() != null && rc.getBegintime() > 0) {
                manufacturer.setRcStartTime(DateUtil.getDateFormatByTime(rc.getBegintime()));
            }
            if (rc.getEndtime() != null && rc.getEndtime() > 0) {
                manufacturer.setRcEndTime(DateUtil.getDateFormatByTime(rc.getEndtime()));
            }

            manufacturer.setRecordCertificateLicence(rc.getRecordNo());
            Integer function = ManufacturerConstants.SUPPLIER_TO_ATTACHMENT_INDEX.get(SysOptionConstant.ID_1101);
            manufacturer.setRcAttachments(buildAttachmentList(function, recordCertificateVo));
        }

        return manufacturer;
    }

    @Override
    public TraderSupplier manufacturerTransformToTraderSupplier(String manufacturerName) {


        return null;
    }

    private List<Attachment> buildAttachmentList(Integer function, List<TraderCertificateVo> source) {
        List<Attachment> attachmentList = Lists.newArrayListWithExpectedSize(source.size());

        for (TraderCertificateVo rfVo : source) {
            Attachment attachment = new Attachment();
            attachment.setAttachmentFunction(function);
            attachment.setUri(rfVo.getUri());
            attachment.setDomain(rfVo.getDomain());
            attachment.setHttpUrl(apiHttp + attachment.getDomain());
            attachmentList.add(attachment);
        }

        return attachmentList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void traderSupplierSyncManufacturer(Integer traderSupplierId) {
        TraderSupplier traderSupplier = new TraderSupplier();
        traderSupplier.setTraderSupplierId(traderSupplierId);
        TraderSupplierVo traderSupplierVo = traderSupplierMapper.getTraderSupplierBaseInfo(traderSupplier);
        if (traderSupplierVo == null) {
            throw new ServiceException("当前供应商不存在");
        }

        if (traderSupplierVo.getTraderType() == 2) {
            return;
        }

        List<Manufacturer> manufacturers;
        if (traderSupplierVo.getTraderId() == null) {
            ManufacturerExample example = new ManufacturerExample();
            ManufacturerExample.Criteria criteria = example.createCriteria();
            criteria.andManufacturerNameEqualTo(traderSupplierVo.getTrader().getTraderName()).andIsDeleteEqualTo(0);
            manufacturers = manufacturerMapper.selectByExample(example);
        } else {
            manufacturers = manufacturerMapper.getByRelateId(traderSupplierVo.getTraderId());
        }

        if (manufacturers.size() > 1) {
            logger.error("首营生产企业名称重复，需处理{}", traderSupplierVo.getTraderSupplierName());
            return;
        }
        Manufacturer syncManufacturer = new Manufacturer();
        if (manufacturers.size() == 1) {
            syncManufacturer = CollUtil.getFirst(manufacturers);
            Date lastVerifyTime = traderSupplierVo.getTrader().getLastValidTime();
            // 【首营生产厂商】不通过审核 对比【首营生产厂商】更新时间和【供应商生产厂商】审核时间
            if (!syncManufacturer.getStatus().equals(ManufacturerStatusEnum.CHECK_SUCCESS.getStatus()) && lastVerifyTime.before(syncManufacturer.getUpdateTime())) {
                return;
            }
            // 【首营生产厂商】通过审核 对比【首营生产厂商】审核时间和【供应商生产厂商】审核时间
            if (syncManufacturer.getStatus().equals(ManufacturerStatusEnum.CHECK_SUCCESS.getStatus())) {
                LogCheckGenerate logCheckGenerate = logCheckGenerateMapper.getByLogBizIdAndLogTypeAndLogStatusOrderByAddTime(syncManufacturer.getManufacturerId(),
                        LogTypeEnum.MANUFACTURER.getLogType(),
                        GoodsCheckStatusEnum.APPROVE.getStatus());
                if (logCheckGenerate == null || logCheckGenerate.getAddTime() == null) {
                    return;
                }
                if (lastVerifyTime.before(logCheckGenerate.getAddTime())) {
                    return;
                }
            }
        }
        Manufacturer manufacturer = this.traderSupplierTransformToManufacturer(traderSupplierVo.getTrader().getTraderName());
        BeanUtil.copyProperties(manufacturer, syncManufacturer, CopyOptions.create().ignoreNullValue());
        manufacturerService.addProductInfo(syncManufacturer, SecurityConstants.SUPER_ADMIN_NO, false);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manufacturerSyncTraderSupplier(Manufacturer manufacturer) {
        // 判断是否可以进行同步，当【首营生产厂商】早于 【供应商生产厂商】 的审核时间，则不会进行同步，则不触发同步到doc资料库逻辑
        TraderSupplier traderSupplier;
        if (manufacturer.getRelateId() != null && manufacturer.getRelateId() != 0) {
            traderSupplier = traderSupplierMapper.getSupplierByTraderId(manufacturer.getRelateId());
            // 更新【供应商生产厂商】名称
            traderMapper.updateByTraderId(manufacturer.getManufacturerName(), manufacturer.getRelateId());
        } else {
            traderSupplier = traderSupplierMapper.getSupplierInfoByTraderName(manufacturer.getManufacturerName());
        }

        if (traderSupplier == null) {
            traderSupplier = this.saveSupplierByName(manufacturer.getManufacturerName(), manufacturer.getAddNo());

        } else {
            // 当【供应商生产厂商】存在 且 不触发【供应商生产厂商】同步到【首营生产厂商】资料更新时，这个时候需要触发一下 【供应商生产厂商】 同步到 【资料库生产厂商】
            this.traderSupplierSyncDocSupplier(traderSupplier);

            if (!manufacturer.getStatus().equals(ManufacturerStatusEnum.CHECK_SUCCESS.getStatus())) {
                logger.error(" 生产厂商未审核通过，  【供应商生产厂商】 同步到 【资料库生产厂商】 {}",manufacturer);
                return;
            }
            LogCheckGenerate logCheckGenerate = logCheckGenerateMapper.getByLogBizIdAndLogTypeAndLogStatusOrderByAddTime(manufacturer.getManufacturerId(),
                    LogTypeEnum.MANUFACTURER.getLogType(),
                    GoodsCheckStatusEnum.APPROVE.getStatus());
            if (logCheckGenerate == null || logCheckGenerate.getAddTime() == null) {
                logger.error("【首营生产厂商】同步到【供应商生产厂商】时,通过审核的首营生产厂商，没有审核记录 {}",manufacturer);
                return;
            }
            // 【供应商生产厂商】未审核 【首营生产厂商】 早于【供应商生产厂商】修改时间
            if (traderSupplier.getLastValidTime() == null && logCheckGenerate.getAddTime().before(cn.hutool.core.date.DateUtil.date(traderSupplier.getModTime()))) {
                logger.warn("【供应商生产厂商】未审核 【首营生产厂商】 早于【供应商生产厂商】修改时间{}",manufacturer);

                return;
            }
            //【首营生产厂商】 早于【供应商生产厂商】审核时间
            if (traderSupplier.getLastValidTime() != null && logCheckGenerate.getAddTime().before(traderSupplier.getLastValidTime())) {
                logger.warn("【首营生产厂商】 早于【供应商生产厂商】审核时间{}",manufacturer);

                return;
            }
        }

        if (traderSupplier == null || traderSupplier.getTraderSupplierId() == null) {
            logger.error("同步供应商失败，请重试{}",manufacturer);
            throw new ServiceException("同步供应商失败，请重试"+manufacturer);
        }

        // 准备附件数据
        manufacturerService.buildAttachmentFile(manufacturer);

        // 更新【供应商生产厂商】附件
        Integer traderId = traderSupplier.getTraderId();

        // 更新【关联关系】
        if (manufacturer.getRelateId() == null || manufacturer.getRelateId() == 0) {
            manufacturerMapper.updateByManufacturerId(traderId, manufacturer.getManufacturerId());
        }

        List<TraderCertificateVo> traderCertificateVo = traderCertificateMapper.getTraderCertificateVo(traderId, ErpConst.TWO);

        Map<Integer, List<TraderCertificateVo>> traderCertificateMapByType =
                traderCertificateVo.stream().collect(Collectors.groupingBy(TraderCertificateVo::getSysOptionDefinitionId));

        // 同步营业执照
        TransCertificateDto businessConverter = CertificateConverter.convertBusiness(manufacturer, traderCertificateMapByType);
        this.realSyncManufacturer(businessConverter, traderId);

        // 生产企业生产产品登记表
        TransCertificateDto rfConverter = CertificateConverter.convertRegistrationForm(manufacturer, traderCertificateMapByType);
        this.realSyncManufacturer(rfConverter, traderId);

        // 第一类医疗器械生产备案凭证
        TransCertificateDto rcConverter = CertificateConverter.convertRecordCertificate(manufacturer, traderCertificateMapByType);
        this.realSyncManufacturer(rcConverter, traderId);

        // 医疗器械生产许可证
        TransCertificateDto productionConverter = CertificateConverter.convertProduction(manufacturer, traderCertificateMapByType);
        this.realSyncManufacturer(productionConverter, traderId);

        // 触发同步【资料库生产厂商】逻辑
        traderSupplier.setTraderSupplierName(manufacturer.getManufacturerName());

        this.traderSupplierSyncDocSupplier(traderSupplier);
    }


    @Override
    public void traderSupplierSyncDocSupplier(TraderSupplier traderSupplier) {
        // 临时文件存放地址
        String path = RequestUtils.getRequest().getSession().getServletContext().getRealPath("/upload/attachment");

        SupplierDocCommand supplierDocCommand = new SupplierDocCommand();
        supplierDocCommand.setSupplierId(traderSupplier.getTraderId());
        supplierDocCommand.setSupplierName(traderSupplier.getTraderSupplierName());
        supplierDocCommand.setUserId(traderSupplier.getCreator());
        try {
            logger.info("traderSupplierSyncDocSupplier:{}",new Object[]{com.alibaba.fastjson.JSONObject.toJSONString(supplierDocCommand)});
            DataResult dataResult = remoteDocSupplierApiService.addDocSupplier(supplierDocCommand);
            if (dataResult.getCode() == 0) {
                syncSupplierService.syncSupplier2Doc(path, traderSupplier.getTraderSupplierId());
            }
        } catch (Exception e) {
            logger.error("【供应商生产企业】同步到资料库异常", e);
        }

    }

    private void realSyncManufacturer(TransCertificateDto dto, Integer traderId) {
        List<TraderCertificateVo> oldInfoList = dto.getOldInfoList();
        TraderCertificateVo baseInfo = dto.getBaseInfo();
        List<Attachment> sourceList = dto.getSourceList();

        switch (dto.getSyncType()) {
            case ManufacturerConstants.NOT_SYNC:
                return;
            case ManufacturerConstants.SYNC_BASE_NOT_FILE:
                if (CollectionUtils.isEmpty(oldInfoList)) {
                    TraderCertificateVo item = new TraderCertificateVo();
                    // 保存信息
                    setTraderCertificateBaseInfo(baseInfo, item, dto.getSysOptionDefinitionId());
                    saveTraderCertificate(Collections.singletonList(item), traderId);
                } else {
                    for (TraderCertificateVo item : oldInfoList) {

                        setTraderCertificateBaseInfo(baseInfo, item, dto.getSysOptionDefinitionId());
                    }
                    saveTraderCertificate(oldInfoList, traderId);
                }
                break;
            case ManufacturerConstants.SYNC_FILE:

                // delete old  确保其他信息完整
                if (CollectionUtils.isNotEmpty(oldInfoList)) {
                    logger.info("删除供应商资质老数据, traderId :{}, sysTypeId :{}", traderId, dto.getSysOptionDefinitionId());
                    traderCertificateMapper.delTraderCertificateAndByTypeId(traderId, dto.getSysOptionDefinitionId());
                }

                List<TraderCertificateVo> saveList = Lists.newArrayListWithExpectedSize(sourceList.size());

                for (Attachment attachment : sourceList) {
                    TraderCertificateVo item = new TraderCertificateVo();

                    setTraderCertificateBaseInfo(baseInfo, item, dto.getSysOptionDefinitionId());

                    String ossResourceId = FileUtil.getOssResourceIdFromStr(attachment.getUri());
                    attachment.setOssResourceId(ossResourceId);
                    item.setUri(attachment.getUri());
                    item.setDomain(attachment.getDomain());
                    item.setName(ossResourceId);
                    saveList.add(item);
                }
                // 保存
                saveTraderCertificate(saveList, traderId);
                break;
            default:
                break;
        }
    }

    private void setTraderCertificateBaseInfo(TraderCertificateVo baseInfo, TraderCertificateVo target, Integer sysOptionDefinitionId) {
        if (baseInfo.getBegintime() != null && baseInfo.getBegintime() > 0) {
            target.setBegintime(baseInfo.getBegintime());
        }
        if (baseInfo.getEndtime() != null && baseInfo.getEndtime() > 0) {
            target.setEndtime(baseInfo.getEndtime());
        }
        if (baseInfo.getIssueDate() != null && baseInfo.getIssueDate() > 0) {
            target.setIssueDate(baseInfo.getIssueDate());
        }
        target.setRecordNo(baseInfo.getRecordNo());
        target.setSn(baseInfo.getSn());
        target.setSysOptionDefinitionId(sysOptionDefinitionId);
        target.setAddTime(System.currentTimeMillis());
        target.setModTime(System.currentTimeMillis());
        target.setTraderType(2);

    }

    private void saveTraderCertificate(List<TraderCertificateVo> list, Integer traderId) {
        for (TraderCertificateVo vo : list) {
            Integer traderCertificateId = vo.getTraderCertificateId();
            vo.setTraderId(traderId);
            if (Objects.nonNull(traderCertificateId) && traderCertificateId > 0) {
                vo.setIsDelete(0);
                // 修改
                traderCertificateMapper.updateByPrimaryKeySelective(vo);
            } else {
                vo.setAddTime(System.currentTimeMillis());
                vo.setIsDelete(0);

                traderCertificateMapper.insertSelective(vo);
            }

        }

    }


    @Override
    public TraderSupplierVo getSupplierInfoByTraderSupplierNew(TraderSupplier traderSupplier) {
        return traderSupplierMapper.getTraderSupplier(traderSupplier);
    }

    @Override
    public Boolean traderCertificateOverdue(Integer traderId) {
        List<Long> endTimeList = traderSupplierMapper.traderCertificateOverdue(traderId);
        if (CollUtil.isEmpty(endTimeList)
                || endTimeList.stream().allMatch(s -> System.currentTimeMillis() < s)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
