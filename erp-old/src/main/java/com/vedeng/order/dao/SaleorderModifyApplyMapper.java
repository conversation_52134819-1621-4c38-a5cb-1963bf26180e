package com.vedeng.order.dao;

import com.vedeng.order.model.SaleorderModifyApply;
import org.apache.ibatis.annotations.Param;

public interface SaleorderModifyApplyMapper {

    int deleteByPrimaryKey(Integer saleorderModifyApplyId);

    int insert(SaleorderModifyApply record);

    int insertSelective(SaleorderModifyApply record);

    SaleorderModifyApply selectByPrimaryKey(Integer saleorderModifyApplyId);

    int updateByPrimaryKeySelective(SaleorderModifyApply record);

    int updateByPrimaryKey(SaleorderModifyApply record);


    /**
     * 获取进行中的修改单(采购发起的)
     */
    SaleorderModifyApply getSaleorderModifyApply(@Param("saleOrderId") Integer saleOrderId,@Param("buyOrderModifyApplyId") Integer buyOrderModifyApplyId);


    SaleorderModifyApply getSaleorderModifyApplyInfo(Integer saleorderModifyApplyId);

    /**
     * 通过订单id和状态获取的修改单
     */
    SaleorderModifyApply getSaleorderModifyApplyBySaleOrderId(@Param("saleOrderId")Integer saleOrderId, @Param("status")Integer status);
}
