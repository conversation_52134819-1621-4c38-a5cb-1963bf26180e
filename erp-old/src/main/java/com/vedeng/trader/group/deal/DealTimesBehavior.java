package com.vedeng.trader.group.deal;

import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.trader.group.BaseBehavior;
import com.vedeng.trader.group.model.SaleorderSum;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.BitSet;
import java.util.List;

public class DealTimesBehavior extends BaseBehavior {
    private Integer lowTimes;
    private Integer highTimes;

    // 运行时注入Mapper
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private SaleorderMapper saleorderMapper=(SaleorderMapper) context.getBean(SaleorderMapper.class);
    @Override
    public BitSet calculate() {
        BitSet res=getBaseTraderSet();
        SaleorderSum query=new SaleorderSum();
        query.setPeriod(DateUtil.getStartAndEndPointForDays(getPeriod()));
        query.setLowTimes(getLowTimes());
        query.setHighTimes(getHighTimes());
        List<Integer> traderIds=saleorderMapper.getTraderIdsByTimes(query);
        BitSet timesSet=new BitSet();
        addBitSet(timesSet,traderIds);
        res.and(timesSet);
        return res;
    }

    public Integer getLowTimes() {
        return lowTimes;
    }

    public void setLowTimes(Integer lowTimes) {
        this.lowTimes = lowTimes;
    }

    public Integer getHighTimes() {
        return highTimes;
    }

    public void setHighTimes(Integer highTimes) {
        this.highTimes = highTimes;
    }
}
