var startTime = "",endTime = "";
$(function(){
	startTime = $("#de_startTime").val();
	endTime = $("#de_endTime").val();
})
function resetPage(){
	reset();
	$("#startTime").val(startTime);
	$("#endTime").val(endTime);
}
function search(){
	var colorTypeEnable = $("#colorTypeEnable").val();
	var arr = colorTypeEnable.split("-");
	$("#colorType").val(arr[0]);
	$("#isEnable").val(arr[1]);
	$("#search").submit();
}

function exportIncomeInvoiceList(){
	var colorTypeEnable = $("#colorTypeEnable").val();
	var arr = colorTypeEnable.split("-");
	$("#colorType").val(arr[0]);
	$("#isEnable").val(arr[1]);
	checkLogin();
	location.href = page_url + '/report/finance/exportIncomeInvoiceList.do?' + $("#search").serialize();
}

function exportIncomeInvoiceDetailList(){
	var colorTypeEnable = $("#colorTypeEnable").val();
	var arr = colorTypeEnable.split("-");
	$("#colorType").val(arr[0]);
	$("#isEnable").val(arr[1]);
	checkLogin();
	location.href = page_url + '/report/finance/exportIncomeInvoiceDetailList.do?' + $("#search").serialize();
}

function sendIncomeInvoiceList(param){

	index = layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			layer.close(index);
			checkLogin();
			var div = '<div class="tip-loadingNewData" style="position:fixed;width:100%;height:100%; z-index:100; background:rgba(0,0,0,0.3)"><i class="iconloadingblue" style="position:absolute;left:50%;top:32%;"></i></div>';
			$("body").prepend(div); //jq获取上一页框架的父级框架；
			var startDate = $(param).parents("div").siblings("ul").find("input[name='startTime']").val();
			var endDate = $(param).parents("div").siblings("ul").find("input[name='endTime']").val();
			$.ajax({
				type: "POST",
				url: page_url + "/finance/invoice/sendincomeinvoicelist.do",
				dataType:'json',
				data : {
					'startDate' : startDate,
					'endDate' : endDate
				},
				success: function(data){
					$(".tip-loadingNewData").remove();
					if(data && data.code == 0){
						if(data.page && data.page.totalRecord){
							layer.alert('推送成功：'+ data.page.totalRecord + '条！');
						}else{
							layer.alert('推送成功：'+ 0 + '条！');
						}
					}
					if(data && data.code == -1){//如果存在traderId为null的
						layer.alert(data.message);
					}
				},
				error:function(data){
					$(".tip-loadingNewData").remove();
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
					}
				}
			});
			
			layer.close(index);
		}, function(){
			
		});
}

/**
 * @describe 查看发票
 * @param invoiceId 发票ID
 * <AUTHOR>
 * @date 2020/6/18 14:35:35
 */
function viewInvoiceImg(invoiceId) {
	var imgSrc = $('#imgSrc' + invoiceId).val();
	if (imgSrc == null || imgSrc == undefined || imgSrc == ''){
		layer.alert('暂未获取到发票图片，请稍后再试!', function () {
			layer.closeAll();
		});
		return;
	}
	if ($('#invoiceFrom' + invoiceId).val() == 1){
		if ($('#imgFlag' + invoiceId).val() == 0) {
			$('#invoiceImg' + invoiceId).html('  <div>' +
				' <img style="max-height: 480px" src="' + $('#imgSrc' + invoiceId).val() + '">' +
				'</div>')
			var imgTr = document.getElementById('imgTr' + invoiceId);
			imgTr.style.display = 'table-row';
			$('#imgFlag' + invoiceId).val(1);
			$('#viewInvocieFont' + invoiceId).html('收起发票');
		} else {
			$('#invoiceImg' + invoiceId).html('')
			var imgTr = document.getElementById('imgTr' + invoiceId);
			imgTr.style.display = 'none';
			$('#imgFlag' + invoiceId).val(0)
			$('#viewInvocieFont' + invoiceId).html('查看发票');
		}
	} else {
		layer.alert('发票信息无法查看');
	}
}

/**
 * 全选
 *
 * @param ckAll
 */
function selectAllInvoice(ckAll) {
	var cks=document.getElementsByName("ck");
	for(var i=0;i<cks.length;i++){
		cks[i].checked=ckAll.checked;
	}
}

/**
 * 单选
 */
function selectOneInvoice() {
	var cks = document.getElementsByName("ck");
	for(var i=0; i<cks.length; i++){
		cks[i].onclick=function(){
			var flag = true;
			var ckArr = document.getElementsByName("ck");
			for (var j=0; j<ckArr.length; j++) {
				if(!ckArr[j].checked){
					flag = false;
					break;
				}
			}
			if(flag){
				document.getElementById("ckAll").checked=true;
			}else{
				document.getElementById("ckAll").checked=false;
			}
		}
	}
}

