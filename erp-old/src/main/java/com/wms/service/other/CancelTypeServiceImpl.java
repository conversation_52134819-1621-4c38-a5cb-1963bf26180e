package com.wms.service.other;

import com.alibaba.fastjson.JSON;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.CancelPoDto;
import com.wms.dto.WmsResponse;
import com.wms.service.CancelTypeService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.WmsInterface;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * wms取消接口集合
 *
 * <AUTHOR>
 * @date 2022/1/6 12:57
 **/
@Service
@Slf4j
public class CancelTypeServiceImpl implements CancelTypeService {

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    // ====================入库单=========================

    @Override
    public boolean cancelInputPurchaseMethod(@NonNull String docNo, @NonNull String erpCancelReason){
        // 传的WmsInterfaceOrderType 以及WMSContant不同
        return this.doWithCancel(docNo, WmsInterfaceOrderType.INPUT_PURCHASE, erpCancelReason, WMSContant.CANCEL_PO);
    }

    @Override
    public boolean cancelInputDeliveryDirectPurchaseMethod(@NonNull String docNo, @NonNull String erpCancelReason) {
        return this.doWithCancel(docNo, WmsInterfaceOrderType.DELIVERY_DIRECT_IN, erpCancelReason, WMSContant.CANCEL_PO);
    }

    @Override
    public boolean cancelInputSaleReturnMethod(@NonNull String docNo, @NonNull String erpCancelReason){
        return this.doWithCancel(docNo, WmsInterfaceOrderType.INPUT_SALE_RETURN, erpCancelReason, WMSContant.CANCEL_PO);
    }

    @Override
    public boolean cancelInputDeliveryDirectSaleReturnMethod(@NonNull String docNo, @NonNull String erpCancelReason) {
        return this.doWithCancel(docNo, WmsInterfaceOrderType.OUT_DIRECT_SALE_OUT, erpCancelReason, WMSContant.CANCEL_ORGGINCAL_SALESORDER);
    }

    @Override
    public boolean cancelExgPurchaseMethod(@NonNull String docNo, @NonNull String erpCancelReason) {
        return this.doWithCancel(docNo, WmsInterfaceOrderType.EXG_PURCHASE, erpCancelReason, WMSContant.CANCEL_PO);
    }

    @Override
    public boolean cancelExchangSaleorderMethod(@NonNull String docNo, @NonNull String erpCancelReason) {
        return this.doWithCancel(docNo, WmsInterfaceOrderType.EXCHANG_SALEORDER, erpCancelReason, WMSContant.CANCEL_PO);
    }

    // ===================出库单======================

    @Override
    public boolean cancelOutPurchaseReturnMethod(@NonNull String docNo, @NonNull String erpCancelReason) {
        return this.doWithCancel(docNo,WmsInterfaceOrderType.OUT_PURCHASE_RETURN,erpCancelReason,WMSContant.CANCEL_ORGGINCAL_SALESORDER);
    }

    @Override
    public boolean cancelOutSaleOutMethod(@NonNull String docNo, @NonNull String erpCancelReason) {
        return this.doWithCancel(docNo,WmsInterfaceOrderType.OUT_SALE_OUT,erpCancelReason,WMSContant.CANCEL_ORGGINCAL_SALESORDER);
    }

    /**
     * 基础方法
     * 不向外暴露
     * @param docNo 单号
     * @param poType 单据类型
     * @param erpCancelReason 取消理由
     * @param interfaceType 取消接口类型
     * @return true 取消成功 false 取消失败
     */
    private boolean doWithCancel(@NonNull String docNo, @NonNull String poType,@NonNull String erpCancelReason,@NonNull String interfaceType){
        CancelPoDto cancelPoDto = new CancelPoDto();
        cancelPoDto.setDocNo(docNo);
        if (WMSContant.CANCEL_PO.equals(interfaceType)) {
            cancelPoDto.setPoType(poType);
        }
        if (WMSContant.CANCEL_ORGGINCAL_SALESORDER.equals(interfaceType)) {
            cancelPoDto.setOrderType(poType);
        }
        if(poType.equals(WmsInterfaceOrderType.DELIVERY_DIRECT_IN) || poType.equals(WmsInterfaceOrderType.OUT_DIRECT_SALE_OUT)){
            cancelPoDto.setWarehouseId("NJ02");
        }
        cancelPoDto.setErpCancelReason(erpCancelReason);
        log.info("ERP取消至WMS的请求: cancelPoDto:{} interfaceType:{}", JSON.toJSONString(cancelPoDto), interfaceType);
        //XxlJobLogger.log("ERP取消至WMS的请求: cancelPoDto:{} interfaceType:{}", JSON.toJSONString(cancelPoDto), interfaceType);
        //取消
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(interfaceType);
        try {
            WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);
            log.info("ERP取消至WMS的请求入参: cancelPoDto:{} interfaceType:{} ERP取消至WMS的响应:{}", JSON.toJSONString(cancelPoDto), interfaceType, JSON.toJSONString(wmsResponse));
           // XxlJobLogger.log("ERP取消至WMS的请求入参: cancelPoDto:{} interfaceType:{} ERP取消至WMS的响应:{}", JSON.toJSONString(cancelPoDto), interfaceType, JSON.toJSONString(wmsResponse));
            switch (interfaceType){
                case WMSContant.CANCEL_PO:
                    return WmsCommonUtil.wmsInputOrderCanCacel(wmsResponse);
                case WMSContant.CANCEL_ORGGINCAL_SALESORDER:
                    return WmsCommonUtil.wmsOutputOrderCanCacel(wmsResponse);
                default: return false;
            }
        } catch (Exception e) {
            log.error("ERP取消至WMS的请求入参:{}调用wms服务异常:{}",JSON.toJSONString(cancelPoDto), e);
           // XxlJobLogger.log("ERP取消至WMS的请求入参:{}调用wms服务异常:{}",JSON.toJSONString(cancelPoDto), e);
        }
        return false;

    }
}
