$(function(){
    var zone = $("#updateTerminalInfo").find("#zone :selected").val();
    if (zone == 1) {
        $("#updateTerminalInfo").find("#province").val(0);
    }
    $("#paymentType").change(function(){
        checkLogin();
        $("form :input").parents('li').find('.warning').remove();
        var paymentTypeSelected = $(this).val();
        if($(this).val()=="419"){
            $("#accountPeriodLi").hide();
            $("#billPeriodSettlementTypeLi").hide();
            $("#retentionMoneyLi").hide();
            //$("#retainageLi").hide();
        }else if($(this).val()=="424"){
            $("#accountPeriodLi").show();
            $("#billPeriodSettlementTypeLi").show();
            $("#retentionMoneyLi").show();
            //$("#retainageLi").show();
        }else{
            $("#accountPeriodLi").show();
            $("#billPeriodSettlementTypeLi").show();
            $("#retentionMoneyLi").show();
            //$("#retainageLi").hide();
        }

        $("#prepaidAmount").val('0.00');$("#accountPeriodAmount").val('0.00');
        $("#retainageAmount").val('0.00');


        //modify by tomcat.Hui ******** VDERP-1294 编辑订单金额错误  增加隐藏域，用于jQuery下拉框联动 start
        var totleMoney= $("#goodsTotleMoney").val();
        if ($("#goodsOrderType").val() == "5" || $("#goodsOrderType").val() == "1"){
            totleMoney = $("#goodsTotleAmount").val();
            console.log("money:" + totleMoney)
        }
        //modify by tomcat.Hui ******** VDERP-1294 编辑订单金额错误  增加隐藏域，用于jQuery下拉框联动 end

        switch (paymentTypeSelected) {
            case "419": //先款后货，预付100%
                $("#prepaidAmount").val(Number(totleMoney).toFixed(2));
                $("#prepaidAmount").attr("readonly",true);
                break;
            case "420": //先货后款，预付80%
                $("#prepaidAmount").val((Number(totleMoney)*0.8).toFixed(2));
                $("#accountPeriodAmount").val(((Number(totleMoney)-(Number($("#prepaidAmount").val()))).toFixed(2)));
                $("#prepaidAmount").attr("readonly",true);
                $("#accountPeriodAmount").attr("readonly",true);
                break;
            case "421"://先货后款，预付50%
                $("#prepaidAmount").val((Number(totleMoney)*0.5).toFixed(2));
                $("#accountPeriodAmount").val(((Number(totleMoney)-(Number($("#prepaidAmount").val()))).toFixed(2)));
                $("#prepaidAmount").attr("readonly",true);
                $("#accountPeriodAmount").attr("readonly",true);
                break;
            case "422"://先货后款，预付30%
                $("#prepaidAmount").val((Number(totleMoney)*0.3).toFixed(2));
                $("#accountPeriodAmount").val(((Number(totleMoney)-(Number($("#prepaidAmount").val()))).toFixed(2)));
                $("#prepaidAmount").attr("readonly",true);
                $("#accountPeriodAmount").attr("readonly",true);
                break;
            case "423"://先货后款，预付0%
                $("#prepaidAmount").removeAttr("readonly");
                $("#accountPeriodAmount").removeAttr("readonly");
                $("#prepaidAmount").val(0);
                $("#accountPeriodAmount").val(Number(totleMoney).toFixed(2));
                $("#prepaidAmount").attr("readonly",true);
                $("#accountPeriodAmount").attr("readonly",true);
                break;
            case "424"://自定义
                $("#prepaidAmount").removeAttr("readonly");
                $("#accountPeriodAmount").removeAttr("readonly");
                //$("#prepaidAmount").val('');$("#accountPeriodAmount").val('');
                break;
            default:
        }
        //付款计划改变时刷新余款和质保金
        $('#retentionMoney').val(0);
        $('#retentionMoneyFont').html(0);
        $('#retentionMoneySpan').hide();
        $('#spareMoneyLab').html(parseFloat($('#accountPeriodAmount').val()).toFixed(2));
    })
    if($('.content2 .payplan .tips-all select').val()==1){
        $('.tips-error').show();
    }
    $('.content2 .payplan .tips-all select').change(function () {
        if($(this).val() === '1'){
            $('.tips-error').show();
        }else{
            $('.tips-error').hide();
        }
    })

    $("#paymentType").trigger('change');

    //合并问题  自定义初始化付款计划
    $('#retentionMoney').val($('#retentionMoneyFlag').val());
    if ($('#paymentType').val() == 424){
        $('#prepaidAmount').val($('#prepaidAmountFlag').val())
        $('#accountPeriodAmount').val($('#accountPeriodAmountFlag').val())
    }
    //先款后货无质保金；2.质保金为0无质保金期限；
    if ($('#paymentType').val() == 419) {
        $('#retentionMoneyLi').hide();
    } else {
        if (parseFloat($('#retentionMoney').val()) == 0){
            $('#retentionMoneySpan').hide();
        } else {
            $('#retentionMoneySpan').show();
            $('#retentionMoneyFont').html($('#retentionMoneyFlag').val());
            $('#spareMoneyLab').html((parseFloat($('#accountPeriodAmount').val()) - parseFloat($('#retentionMoneyFlag').val())).toFixed(2));
        }
    }

    //票货同行初始化
    if ($("input[name='isSendInvoice']:checked").val() == 0){
        $('#isSameAddressLi').hide();
        // $('#invoiceCustomerLi').hide();
        $('#traderContactLi').hide();
        $('#invoiceTraderAddressLi').hide();
        $('#invoiceTraderContactLi').hide();
        $('#invoiceTraderContactMobileLi').hide();
        $('#invoiceEmailLi').hide();
    }

    /**
     * “发票寄送节点”，该按钮显示的前提是：
     （1）“发票是否寄送”选择“寄送”
     （2）“货票地址是否相同”选择“相同”
     */
    if ($("input[name='isSendInvoice']:checked").val() != 1 || $("input[name='isSameAddress']:checked").val() != 1){
        $('#invoiceSendNodeLi').hide();
    }

    triggerContactInfoChanged();
    deliveryClaimChange();
})


function changeIsPrintout() {
    var isPrint = $("#is_print").val();
    var isScientificDept = $("#is_scientificDept").val();
    if (isPrint == 2){
        //不打印随货出库单
        $("#is_printout").val(0);
        if ($("#is_price").length == 1){
            $("#is_price_li").remove();
        }
    } else if (isPrint == 1){
        //判断打印出库单是否带价格
        if (isScientificDept == "true"){
            $("#is_printout").val(3);
        } else {
            if ($("#is_price").length == 0){
                $("#is_print_li").append("<li id='is_price_li'>" +
                    "<div style=\"height: 25px\">" +
                    "<span style=\"color: red;text-indent: 15px\">  *</span>" +
                    "<label style=\"width: 158px\">随货出库单是否自带价格</label>" +
                    "<select  id='is_price' name = \"isPrintout\" style='height: auto' onchange='changeIsPrice()' lay-ignore>" +
                    "<option value=\"0\">请选择</option>\n" +
                    "<option value=\"1\">是</option>\n" +
                    "<option value=\"2\">否</option>\n" +
                    "</select>" +
                    "</div>" +
                    "<div id=\"isPriceMsg\" style=\"clear:both;\"></div>" +
                    "</li>");
                $("#is_printout").val(-2);
            }
        }
    } else if (isPrint == -1){
        $("#is_printout").val(-1);
        if ($("#is_price").length == 1){
            $("#is_price_li").remove();
        }
    }
}

function changeIsPrice() {
    var isPrice = $("#is_price").val();
    if (isPrice == 1){
        $("#is_printout").val(1);
    } else if (isPrice == 2){
        $("#is_printout").val(2);
    } else if (isPrice == 0){
        $("#is_printout").val(-2);
    }
}

/**
 * 是否寄送发票信息
 *
 * @param orderType
 * @param sendFlag
 */
function isSendInvoiceChecked(orderType, sendFlag) {
    if (sendFlag == 0){
        $('#isSameAddressLi').hide();
        // $('#invoiceCustomerLi').hide();
        // $('#traderContactLi').hide();
        // $('#invoiceTraderAddressLi').hide();
        // $('#invoiceTraderContactLi').hide();
        // $('#invoiceTraderContactMobileLi').hide();
        $('#invoiceEmailLi').hide();
    } else {
        $('#isSameAddressLi').show();
        // $('#invoiceCustomerLi').show();
        // $('#traderContactLi').show();
        // $('#invoiceTraderAddressLi').show();
        // $('#invoiceTraderContactLi').show();
        // $('#invoiceTraderContactMobileLi').show();
        $('#invoiceEmailLi').show();
    }
    checkInvoiceGoodsStatus();
}

function isDelayInvoiceChecked() {
    checkLogin();
    if(typeof($("input[name='isDelayInvoiceCheckbox']:checked").val()) == "undefined") {
        $("input[name='isDelayInvoice']").val(0);
    } else {
        $("input[name='isDelayInvoice']").val(1);
    }
}

function updatePayment(obj){
    checkLogin();
    if($(obj).val()=="419"){
        $("#accountPeriodLi").hide();
        $("#billPeriodSettlementTypeLi").hide();
        $("#retentionMoneyLi").hide();
        //$("#retainageLi").hide();
        $('#retentionMoneySpan').hide();
    }else if($(obj).val()=="424"){
        $("#accountPeriodLi").show();
        $("#billPeriodSettlementTypeLi").show();
        $("#retentionMoneyLi").show();
        //$("#retainageLi").show();
        $('#retentionMoneySpan').show();
    }else{
        $("#accountPeriodLi").show();
        $("#billPeriodSettlementTypeLi").show();
        $("#retentionMoneyLi").show();
        //$("#retainageLi").hide();
        $('#retentionMoneySpan').show();
    }
}

function updateInvoiceType(obj){
    checkLogin();
    if($(obj).val()=="681" || $(obj).val()=="971"){
        if($("input:checkbox[name='isSendInvoiceCheckbox']").is(":checked") && $("input[name='isSendInvoice']").val() == "0"){
            $("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option>');
        }else{
            $("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option><option value="3">自动电子发票</option>');
        }
    }else if($(obj).val()=="682" || $(obj).val()=="972"){
        $("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option>');
    }else {
        $("select[name='invoiceMethod']").html('<option value="0">请选择</option>');
    }
}

function logisticsCheck() {
    checkLogin();
    $("#logisticsCollection").val($("#logisticsCheckBox").is(":checked")==true?1:0);
}

function delSaleorderGoods(saleorderGoodsId, saleorderId, skuNo, scene){
    checkLogin();
    layer.confirm("您是否确认该操作？", {
        btn: ['确定','取消'] //按钮
    }, function(){
        $.ajax({
            type: "POST",
            url: "./delSaleorderGoodsById.do",
            data: {'saleorderGoodsId':saleorderGoodsId,'saleorderId':saleorderId,'isDelete':1},
            dataType:'json',
            success: function(data){
               if (data.code == 0){
                   deleteInsideComment(saleorderGoodsId, saleorderId, skuNo, scene);
               }else {
                   layer.alert(data.message);
               }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }, function(){
    });
}

function editSubmit(type){
    checkLogin();

    var $form = $("#editForm");
    $("form :input").parents('li').find('.warning').remove();

    var orderType = Number(type);
    // 不寄送
    var isSendInvoice = $("input[name='isSendInvoice']:checked").val();

    if ($("select[name='traderContactId']").val() == 0) {
        warnTips("traderContactIdMsg","联系人不允许为空");
        return false;
    }
    if ($("select[name='traderAddressId']").val() == 0) {
        warnTips("traderAddressIdMsg","联系地址不允许为空");
        return false;
    }

    if ($("input[name=takeTraderId]").val() == 0 || $("input[name=takeTraderId]").val() == '') {
        warnTips("takeTraderIdMsg"," 收货客户不允许为空");
        return false;
    }

    if ($("select[name='takeTraderContactId']").val() == 0) {
        warnTips("takeTraderContactIdMsg","收货联系人不允许为空");
        return false;
    }

    var traderContactIdText = $("select[name='traderContactId']").find("option:selected").text();
    var traderContactIdTextArr = traderContactIdText.split('/');

    $("input[name='traderContactName']").val(traderContactIdTextArr[0]);
    $("input[name='traderContactMobile']").val(traderContactIdTextArr[1]);

    var traderAddressIdText = $("select[name='traderAddressId']").find("option:selected").text();
    var traderAddressIdTextArr = traderAddressIdText.split('/');
    $("input[name='traderArea']").val(traderAddressIdTextArr[0]);
    $("input[name='traderAddress']").val(traderAddressIdTextArr[1]);


    var takeTraderContactIdText = $("select[name='takeTraderContactId']").find("option:selected").text();
    var takeTraderContactIdTextArr = takeTraderContactIdText.split('/');
    $("input[name='takeTraderContactName']").val(takeTraderContactIdTextArr[0]);
    $("input[name='takeTraderContactTelephone']").val(takeTraderContactIdTextArr[1]);
    $("input[name='takeTraderContactMobile']").val(takeTraderContactIdTextArr[2]);
    var takeTraderAddressIdText = $("select[name='takeTraderAddressId']").find("option:selected").text();
    var takeTraderAddressIdTextArr = takeTraderAddressIdText.split('/');
    $("input[name='takeTraderArea']").val(takeTraderAddressIdTextArr[0]);
    $("input[name='takeTraderAddress']").val(takeTraderAddressIdTextArr[1]);

    var takeAreaLength = takeTraderAddressIdTextArr[0].split(' ').length;
    /*	if(takeAreaLength != 3) {
            warnTips("takeTraderAddressIdMsg","收货地址请补充完省市区");
            return false;
        }*/

    if ($("select[name='takeTraderAddressId']").val() == 0) {
        warnTips("takeTraderAddressIdMsg","收货地址不允许为空");
        return false;
    }

    var logisticsComments = $("#logisticsComments").val();
    if(logisticsComments.length>256){
        warnTips("logisticsComments","物流备注长度应该在0-256个字符之间");
        return false;
    }

    if ($("input[name=invoiceTraderId]").val() == 0 || $("#input[name=invoiceTraderId]").val() == '') {
        warnTips("invoiceTraderIdMsg"," 收票客户不允许为空");
        return false;
    }

    //非HC订单寄送发票时的校验
    // if(isSendInvoice == 1 || isSendInvoice == '1'){

        if ($("select[name='invoiceTraderContactId']").val() == 0) {
            warnTips("invoiceTraderContactIdMsg","收票联系人不允许为空");
            return false;
        }

        var invoiceTraderContactIdText = $("select[name='invoiceTraderContactId']").find("option:selected").text();
        var invoiceTraderContactIdTextArr = invoiceTraderContactIdText.split('/');
        $("input[name='invoiceTraderContactName']").val(invoiceTraderContactIdTextArr[0]);
        $("input[name='invoiceTraderContactTelephone']").val(invoiceTraderContactIdTextArr[1]);
        $("input[name='invoiceTraderContactMobile']").val(invoiceTraderContactIdTextArr[2]);
        var invoiceTraderAddressIdText = $("select[name='invoiceTraderAddressId']").find("option:selected").text();
        var invoiceTraderAddressIdTextArr = invoiceTraderAddressIdText.split('/');
        $("input[name='invoiceTraderArea']").val(invoiceTraderAddressIdTextArr[0]);
        $("input[name='invoiceTraderAddress']").val(invoiceTraderAddressIdTextArr[1]);

        var invoiceAreaLength = invoiceTraderAddressIdTextArr[0].split(' ').length;
        if(invoiceAreaLength != 3) {
            warnTips("invoiceTraderAddressIdMsg","收票地址请补充完省市区");
            return false;
        }

        if ($("select[name='invoiceTraderAddressId']").val() == 0) {
            warnTips("invoiceTraderAddressIdMsg","收票地址不允许为空");
            return false;
        }
    // }

    //寄送发票时才对票货地址是否相同做校验
    if ($("input[name='isSendInvoice']:checked").val() == 1){
        if ($("input[name='isSameAddress']:checked").val() == undefined || $("input[name='isSameAddress']:checked").val() == null) {
            warnTips("isSameAddressMsg","货票地址是否相同不能为空");
            return false;
        }
    }
    /**
     * “发票寄送节点”，该按钮显示的前提是：
     （1）“发票是否寄送”选择“寄送”
     （2）“货票地址是否相同”选择“相同”
     */
    if ($("input[name='isSendInvoice']:checked").val() == 1 && $("input[name='isSameAddress']:checked").val() == 1){
        if ($("input[name='invoiceSendNode']:checked").val() == undefined || $("input[name='invoiceSendNode']:checked").val() == null) {
            warnTips("invoiceSendNodeMsg","发票寄送节点不能为空");
            return false;
        }
    }


    if ($("select[name='invoiceType']").val() == 0) {
        warnTips("invoiceTypeMsg","发票类型不允许为空");
        return false;
    }
    if ($("select[name='invoiceMethod']").val() == 0) {
        warnTips("invoiceMethodMsg","开票方式不允许为空");
        return false;
    }
    //$form.find("#logisticsCollection").val($form.find("#logisticsCheckBox").is(":checked")==true?1:0);
    var invoiceComments = $("#invoiceComments").val();
    if(invoiceComments.length>256){
        warnTips("invoiceComments","开票备注长度应该在0-256个字符之间");
        return false;
    }

    var totleMoney= $("#goodsTotleMoney").val();
    if ($("#goodsOrderType").val() == "5" || $("#goodsOrderType").val() == "1"){
        totleMoney = $("#goodsTotleAmount").val();
    }

    //付款方式
    var paymentType = $form.find("#paymentType").val();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    //付款方式为--自定义时
    if(paymentType=="424"){
        var retainageAmount = $("#retainageAmount").val();
        var prepaidAmount = $("#prepaidAmount").val();
        if(prepaidAmount.length > 14){
            warnTips("prepaidAmountError","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
            return false;
        }

        if(prepaidAmount!=""){
            if(!reg.test(prepaidAmount)){
                warnTips("prepaidAmountError","金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
        }else{
            warnTips("prepaidAmountError","预付金额不允许小于0");//文本框ID和提示用语
            return false;
        }

        var accountPeriodAmount = $("#accountPeriodAmount").val();
        if(accountPeriodAmount.length > 14){
            warnTips("accountPeriodAmount","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
            return false;
        }
        if(accountPeriodAmount!=""){
            if(!reg.test(accountPeriodAmount)){
                warnTips("accountPeriodAmountError","金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
            // TODO 26_V02 集采订单在校验时现在需要校验所有类型的可用额总和，而不是单纯的正式账期
            var accountPeriodLeft = Number($("#accountPeriodLeft").val());//账期最大限额
            var oldAccountPeriodAmount = Number($("#oldAccountPeriodAmount").val());//本订单占用额度
            if(Number(accountPeriodAmount) > 0 && Number(accountPeriodAmount) > accountPeriodLeft + Number(oldAccountPeriodAmount)){
                warnTips("accountPeriodAmountError","帐期支付金额超过帐期,剩余额度:"+accountPeriodLeft);//文本框ID和提示用语
                return false;
            }
        }else{
            warnTips("accountPeriodAmountError","账期支付金额不允许小于0");//文本框ID和提示用语
            return false;
        }


        /*	var retainageAmount = $("#retainageAmount").val();
            if(retainageAmount.length > 14){
                warnTips("retainageAmount","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                return false;
            }
            if(retainageAmount!=""){
                if(!reg.test(retainageAmount)){
                    warnTips("retainageAmountError","金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }else if(Number(retainageAmount)<0){
                    warnTips("retainageAmountError","尾款不允许小于0");//文本框ID和提示用语
                    return false;
                }else if(Number(retainageAmount) > Number(totleMoney)*0.1){
                    warnTips("retainageAmountError","尾款不允许超过合同金额10%");//文本框ID和提示用语
                    return false;
                }
            }else{
                warnTips("retainageAmountError","尾款不允许小于0");//文本框ID和提示用语
                return false;
            }
            var retainageAmountMonth = $("#retainageAmountMonth").val();
            if(retainageAmountMonth.length>0){
                var re = /^[0-9]+$/ ;
                if(retainageAmountMonth=="0" || !re.test(retainageAmountMonth)){
                    warnTips("retainageAmountError","数量必须为大于0的正整数");//文本框ID和提示用语
                    return false;
                }else if(Number(retainageAmountMonth) > 24){
                    warnTips("retainageAmountError","尾款期限不允许超过24个月");//文本框ID和提示用语
                    return false;
                }
            }*/

        if(prepaidAmount!="" && accountPeriodAmount!=""){
            var goodsTotleMoney = $("#goodsTotleMoney").val();
            if(Number(prepaidAmount) + Number(accountPeriodAmount)  != Number(goodsTotleMoney)){
                warnTips("error_div","支付金额总额与总金额不符，请验证！");//文本框ID和提示用语
                return false;
            }
        }
    } else if(paymentType!="419"){//419先货后款，预付100%
        var accountPeriodAmount = $("#accountPeriodAmount").val();
        var retainageAmount = $("#retainageAmount").val();
        var prepaidAmount = $("#prepaidAmount").val();
        if(accountPeriodAmount.length > 14){
            warnTips("accountPeriodAmount","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
            return false;
        }
        if(prepaidAmount!="" && accountPeriodAmount!=""){
            if((Number(prepaidAmount) + Number(accountPeriodAmount)).toFixed(2) != Number(totleMoney).toFixed(2)){
                warnTips("error_div","支付金额总额与总金额不符，请验证！");//文本框ID和提示用语
                return false;
            }
        }
        if(accountPeriodAmount!=""){
            if(!reg.test(accountPeriodAmount)){
                warnTips("accountPeriodAmountError","金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
            // TODO 26_V02 去除校验账期
            var accountPeriodLeft = Number($("#accountPeriodLeft").val());//账期最大限额
            var oldAccountPeriodAmount = Number($("#oldAccountPeriodAmount").val());//本订单占用额度
            if(Number(accountPeriodAmount) > 0 && Number(accountPeriodAmount) > accountPeriodLeft + Number(oldAccountPeriodAmount)){
                warnTips("accountPeriodAmountError","帐期支付金额超过帐期剩余额度:"+accountPeriodLeft);//文本框ID和提示用语
                return false;
            }
        }else{
            warnTips("accountPeriodAmountError","账期支付金额必须大于0");//文本框ID和提示用语
            return false;
        }
    }

    var paymentComments = $("#paymentComments").val();
    if(paymentComments.length > 256){
        warnTips("paymentComments","收款备注长度应该在2-256个字符之间");//文本框ID和提示用语
        return false;
    }

    var additionalClause = $("#additionalClause").val();
    if(additionalClause.length > 256){
        warnTips("additionalClause","附加条款长度应该在0-256个字符之间");//文本框ID和提示用语
        return false;
    }

    var comments = $("#comments").val();
    if(comments.length > 1024){
        warnTips("comments","内部备注长度应该在0-1024个字符之间");//文本框ID和提示用语
        return false;
    }

    if ($("#updateTerminalInfo").find("#province :selected").val() == 0) {
        warnTips("sales_area_msg_div","销售区域不允许为空");//文本框ID和提示用语
        return false;
    }
    if ($("#updateTerminalInfo").find("#city :selected").val() == 0) {
        warnTips("sales_area_msg_div","城市不允许为空");//文本框ID和提示用语
        return false;
    }
    if ($("#updateTerminalInfo").find("#zone :selected").val() == 0) {
        warnTips("sales_area_msg_div","区域不允许为空");//文本框ID和提示用语
        return false;
    }
    var salesArea = ($("#updateTerminalInfo").find("#province :selected").text()=="请选择"?"":$("#updateTerminalInfo").find("#province :selected").text()) + " "
        + ($("#updateTerminalInfo").find("#city :selected").text()=="请选择"?"":$("#updateTerminalInfo").find("#city :selected").text()) + " "
        + ($("#updateTerminalInfo").find("#zone :selected").text()=="请选择"?"":$("#updateTerminalInfo").find("#zone :selected").text());
    $("#quotePayMoneForm").find("#salesArea").val(salesArea);
    var province = $("#updateTerminalInfo").find("#province :selected").val()=="0"?"":$("#updateTerminalInfo").find("#province :selected").val();
    var city = $("#updateTerminalInfo").find("#city :selected").val();
    var zone = $("#updateTerminalInfo").find("#zone :selected").val();
    $form.find("#salesAreaId").val(zone=="0"?(city=="0"?province:city):zone);


    if(orderType != 5) {
        //校验随货出款单不能为空
        var isPrintout = $("#is_printout").val();
        if (isPrintout == -1 || isPrintout == '') {
            warnTips("isPrintoutMsg", "是否打印随货出库单不能为空");
            return false;
        }
        if (isPrintout == -2) {
            warnTips("isPriceMsg", "随货出库单是否自带价格不能为空");
            return false;
        }

        /**
         * 非先款后货校验质保金相关内容
         */
        if ($('#paymentType').val() != 419){
            //质保金
            var retentionMoney = $('#retentionMoney').val();
            if( /^[0-9]+([.]{1}[0-9]{1,2})?$/.test(retentionMoney) ){
                //质保金金额
                var accountPeriodAmount = $('#accountPeriodAmount').val();
                if (parseFloat(retentionMoney) <= accountPeriodAmount){
                    var goodsTotleMoney = $('#goodsTotleMoney').val();
                    if (parseFloat(retentionMoney) <= goodsTotleMoney/2){
                        $('#retentionMoneyFont').html(retentionMoney);
                        delWarnTips('accountPeriodAmountError')
                    } else {
                        warnTips("accountPeriodAmountError","质保金不得超过合同总金额的50%");
                        return false;
                    }
                } else {
                    warnTips("accountPeriodAmountError","质保金不得超过账期支付金额");
                    return false;
                }
            } else {
                warnTips("accountPeriodAmountError","只可填写数字，最多保留两位小数");
                return false;
            }

            //存在质保金时校验质保金期限
            if (parseFloat($('#retentionMoney').val()) != 0){
                var retentionMoneyDay = $('#retentionMoneyDay').val();
                if(!(/(^[1-9]\d*$)/.test(retentionMoneyDay))){
                    warnTips('retentionMoneyDayError','只可填写正整数');
                    return false;
                }
            } else {
                $('#retentionMoneyDay').val(0);
            }
        }
    }

    if ($('#paymentType').val() == 419){
        $('#retentionMoney').val(0);
        $('#retentionMoneyDay').val(0);
        $('#periodDay').val(0);
        $('#retentionMoneyDay').val(0);
    } else {
        /**
         * 余款账期天数校验
         */
        var periodDay = $('#periodDay').val();
        if(!(/(^[1-9]\d*$)/.test(periodDay))){
            warnTips('retentionMoneyDayError','只可填写正整数');
            return false;
        }
    }

    var deliveryClaimSelect = $("#deliveryClaimSelect").val();
    var deliveryDelayTimeStr = $("#deliveryDelayTimeStr").val();
    if (deliveryClaimSelect == 1 && deliveryDelayTimeStr=="" ) {
        warnTips("deliveryDelayTimeStrMsg", "请填写等待截止日期");
        return false;
    }

    // 校验产品是否配置内部备注标签
    let specialSkuList = getSpecialGoodsList();
    let remarkError = 0
    $('#goodsTbody').children('tr').each(function(i, v){
        if (i === $('#goodsTbody').children('tr') - 1) {
            return false;
        }
        $(v).children('td').each(function(_i, _v){
            if ($(_v).hasClass('c-comments') && !$(_v).children('.customername').children('.customernameshow').html()) {
                let skuId = parseInt($(_v).attr('skuId'));
                let skuList = specialSkuList.filter(function (sku) {
                    return skuId === sku;
                })
                if (skuList.length > 0) {
                    return false;
                }
                remarkError++;
                $(_v).children('.no_remark_error').css('display','block');
            }
        })
    })
    if (remarkError > 0) {
        return false;
    }


    // 集团客户下单范围校验
    // if (!checkGoodsRangeForGroupCustomer()) {
    //     return false;
    // }


    // 检验是否满足票货同行条件
    if ($("input[name='isSameAddress']:checked").val() == 1 && $("input[name='isSendInvoice']:checked").val() ==1) {

       var reasonNo = isSameAddressSubmitChecked();

       if (reasonNo != ""){
           layer.open({
               type: 2,
               shadeClose: false, //点击遮罩关闭
               area: ["30%", "40%"],
               title: "提示",
               content: "/order/jc/returnTipsPage.do?reasonNo=" + reasonNo,
           });
           return false;
       }
    }

    //未添加销售商品不做校验
    if ($("#goodsTbody tr").length<=1) {
        //提交订单信息
        $form.submit();
        return
    }

    var text = $('#costomerLink').text();
   layer.confirm('推送的手机号：'+text+'-'+traderContactIdTextArr, {
        icon: 3,
        title: '订单待确认提醒',
        btn: ['确认', '取消'],
    },
    function(index, layero) {
        layer.close(index);
        //提交订单信息
        $form.submit();
    },
    function(index){
        layer.close(index);
    });
}

//  集采订单（JCO/JCF）满足“票货同行”条件：
// 	JCO、JCF订单；
// 1.	发票是否寄送：寄送；
// 2.	开票方式：自动电子发票；
// 3.	订单中全部商品的发货方式为“普发”；
// 4.	订单不存在非“已关闭”状态的“销售订单退货”或“销售订单退票”的售后单；
// 5.	“货票主体”（客户）相同，并且“货票地址”相同；
// 6.	“票货是否同行”：票货同行；
function isSameAddressSubmitChecked(){

    //没有满足的原因
    var reasonNo="";
    //订单Id
    var saleorderId = $("input[name='saleorderId']").val();

    //发票是否寄送
    var isSendInvoice =  $("input[name='isSendInvoice']:checked").val();
    if(isSendInvoice != 1){
        reasonNo = reasonNo + "1";
    }
    //开票方式
    var invoiceMethod =  $("select[name='invoiceMethod']").val();
    if(invoiceMethod != 3 && invoiceMethod != 4){
        reasonNo = reasonNo + "2";
    }
    // 收货客户
    var takeTraderId =  $('#take_trader_1 option:selected').val();
    //收货地址
    var takeTraderAddressId =  $('select[name=takeTraderAddressId] > option:selected').val();
    // 收票客户
    var invoiceTraderId = $('#take_trader_2 option:selected').val();
    //收票地址
    var invoiceTraderAddressId = $("select[name='invoiceTraderAddressId']").val();
    if(takeTraderId != invoiceTraderId || takeTraderAddressId != invoiceTraderAddressId){
        reasonNo = reasonNo + "5";
    }
    //票货是否同行
    var isSameAddress = $("input[name='isSameAddress']:checked").val();
    if(isSameAddress != 1){
        reasonNo = reasonNo + "6";
    }

    $.ajax
    ({
        type : "POST",
        url : page_url+"/order/jc/isSameAddressSubmitCheck.do",
        data :{'saleorderId':saleorderId},
        dataType : 'json',
        async: false,
        success : function(res) {
            reasonNo = reasonNo + res.data;
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });

    return reasonNo;
}

function searchTerminal(){
    checkLogin();
//	$("#updateTerminalInfo").find("#errorMes").removeClass("errorbor");
    delWarnTips("errorTxtMsg");

    var searchTraderName = $("#updateTerminalInfo").find("#searchTraderName").val()==undefined?"":$("#updateTerminalInfo").find("#searchTraderName").val();
    /*	if(searchTraderName==""){
            warnTips("errorMes","请输入终端信息");//文本框ID和提示用语
            return false;
        }*/
    $("#updateTerminalInfo").find("#terminalDiv").attr('layerParams','{"width":"70%","height":"80%","title":"报价终端","link":"'+ page_url+'/trader/customer/getTerminalList.do?searchTraderName=' + encodeURI(searchTraderName) + '&optType=addQuoteTerminal"}');
    $("#updateTerminalInfo").find("#terminalDiv").click();
}

function agingSearchTerminal(){
    checkLogin();
    $("#searchTraderName").val("");
    $("#terminalTraderNameDiv").html("");
    $("#quotePayMoneForm").find(".terminal").each(function(i){
        $(this).val("");
    });
    $("#terminalNameDetail").hide();
    $("#terminalNameCheck").show();
}

function goodsCheckClick(obj){
    if($(obj).is(":checked")){
        var num = 0;
        $("input:checkbox[name='goodsCheckName']").each(function(i){
            if($(this).is(":checked")){
                num ++;
            }
        });
        if(num == $("input:checkbox[name='goodsCheckName']").length){
            $("input:checkbox[name='goodsCheckAllName']").prop("checked",true);
        }
    }else{
        $("input:checkbox[name='goodsCheckAllName']").prop("checked",false);
    }
}

function goodsCheckAllClick(obj){
    if($(obj).is(":checked")){
        $("input:checkbox[name='goodsCheckName']").each(function(i){
            $(this).prop("checked",true);
        });
    }else{
        $("input:checkbox[name='goodsCheckName']").each(function(i){
            $(this).prop("checked",false);
        });
    }
}

function updateSaleGoodsInit(saleorderId,scene){
    checkLogin();
    var saleorderGoodsIdArr = [];
    let skuList = [];
    $("input:checkbox[name='goodsCheckName']:checked").each(function(i){
        saleorderGoodsIdArr.push($(this).val());
        skuList.push({
            skuId: $(this).attr('skuId'),
            skuNo: $(this).attr('skuNo'),
            skuName: $(this).attr('skuName')
        })
    });
    if(saleorderGoodsIdArr.length == 0){
        layer.alert("请选择要修改的商品！");
        return false;
    }
    if (!checkGoods(skuList, 0)) {
        return;
    }
    $("#saleGoodsDeliveryDirect").attr('layerParams','{"width":"750px","height":"650px","title":"修改订单商品","link":"'+ page_url+'/order/saleorder/updateSaleGoodsInit.do?saleorderGoodsIdArr=' + saleorderGoodsIdArr + '&saleorderId='+saleorderId+'&scene='+scene+'"}');
    $("#saleGoodsDeliveryDirect").click();
}

function changeOwnerUserId(obj)
{
    var ownerUserId = $(obj).val();

    $("#ownerUserId").val(ownerUserId);

}

/**
 * 质保金金额校验
 */
function retainageAmountChange() {
    //质保金
    var retentionMoney = $('#retentionMoney').val();
    if( /^[0-9]+([.]{1}[0-9]{1,2})?$/.test(retentionMoney) ){
        //质保金金额
        var accountPeriodAmount = $('#accountPeriodAmount').val();
        if (parseFloat(retentionMoney) <= accountPeriodAmount){
            var goodsTotleMoney = $('#goodsTotleMoney').val();
            if (parseFloat(retentionMoney) <= goodsTotleMoney/2){
                $('#retentionMoneyFont').html(retentionMoney);
                $('#spareMoneyLab').html((parseFloat($('#accountPeriodAmount').val()) - parseFloat(retentionMoney)).toFixed(2));
                if (parseFloat(retentionMoney) != 0 ){
                    $('#retentionMoneySpan').show();
                } else {
                    $('#retentionMoneySpan').hide();
                }
                delWarnTips('accountPeriodAmountError')
            } else {
                warnTips("accountPeriodAmountError","质保金不得超过合同总金额的50%");
                return false;
            }
        } else {
            warnTips("accountPeriodAmountError","质保金不得超过账期支付金额");
            return false;
        }
    } else {
        warnTips("accountPeriodAmountError","只可填写数字，最多保留两位小数");
        return false;
    }
}

/**
 * 质保金期限校验
 */
function retainageAmountDayChange() {
    var retentionMoneyDay = $('#retentionMoneyDay').val();
    if(!(/(^[1-9]\d*$)/.test(retentionMoneyDay))){
        warnTips('retentionMoneyDayError','只可填写正整数');
    } else {
        delWarnTips('retentionMoneyDayError')
    }
}

/**
 * 余款账期天数校验
 */
function periodDayChange() {
    debugger
    var accountPeriodAmount = $('#accountPeriodAmount').val();
    if (accountPeriodAmount != undefined && accountPeriodAmount != '' && accountPeriodAmount != 0){
        var  periodDay = $('#periodDay').val();
        if(!(/(^[1-9]\d*$)/.test( periodDay))){
            warnTips('retentionMoneyDayError','只可填写正整数');
        } else {
            delWarnTips('retentionMoneyDayError')
        }
    } else {
        $('#periodDay').val(0);
    }
}

/**
 * 自定义账期金额
 */
function accountPeriodAmountChange() {
    debugger
    var accountPeriodAmount  = $('#accountPeriodAmount').val();
    if (accountPeriodAmount != undefined && accountPeriodAmount != '' && accountPeriodAmount != 0){
        $('#spareMoneyLab').html(parseFloat(accountPeriodAmount) - parseFloat($('#retentionMoney').val()).toFixed(2));
    } else {
        $('#spareMoneyLab').html(0.00);
        $('#retentionMoney').val(0);
        $('#retentionMoneySpan').hide();
    }
}

/**
 * 票货地址是否相同
 *
 * @param isSameAddressFlag
 */
function isSameAddressChecked(isSameAddressFlag) {
    if (isSameAddressFlag == 1){
        $('#invoiceTraderContactName_5').val($('#takeTraderContactName_5').val());
        $('#invoiceTraderContactMobile_5').val($('#takeTraderContactMobile_5').val());
        $('#invoiceTraderAddressId-province').val($('#takeTraderAddressId-province').val())
        setRegion($('#takeTraderAddressId-province').val(), 'invoiceTraderAddressId-city');
        $("#invoiceTraderAddressId-city ").val($('#takeTraderAddressId-city').val());
        setRegion($('#takeTraderAddressId-city').val(), 'invoiceTraderAddressId-zone');
        $('#invoiceTraderAddressId-zone').val($('#takeTraderAddressId-zone').val());
        $('#invoiceTraderAddress_5').val($('#takeTraderAddress_5').val());
        //收票地址地区标记
        $('#invoiceTraderAddressId').val($('#takeTraderAddressId-zone').val());
    }
    checkInvoiceGoodsStatus();
}

/**
 * 设置地区信息
 *
 * @param regionId 地区ID
 * @param regionType 下级地区标签
 */
function setRegion(regionId,regionType) {
    $.ajax
    ({
        type : "POST",
        url : page_url+"/system/region/getregion.do",
        data :{'regionId':regionId},
        dataType : 'json',
        async: false,
        success : function(data) {
            $option = "<option value='0'>请选择</option>";
            $.each(data.listData,function(i,n){
                $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
            });
            $("#" + regionType).empty();
            $("#" + regionType).html($option);
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });
}

/**
 * 检查票货同行状态
 */
function checkInvoiceGoodsStatus() {
    /**
     * “发票寄送节点”，该按钮显示的前提是：
     （1）“发票是否寄送”选择“寄送”
     （2）“货票地址是否相同”选择“相同”
     */
    if ($("input[name='isSendInvoice']:checked").val() == 1 && $("input[name='isSameAddress']:checked").val() == 1){
        $('#invoiceSendNodeLi').show();
    } else {
        $('#invoiceSendNodeLi').hide();
    }
}

function triggerContactInfoChanged() {
    var contactId = $('#contact_3').find('option:selected').val();
    if (contactId == 0 || contactId=='') {
        $("#contact_position_1").html('');
        $("#allowed_goods_types_1").html('');
        return
    }

    $('.group-label').hide();

    //根据客户ID获取联系人列表&地址列表
    $.ajax({
        url: page_url + '/order/jc/getContactInfo.do',
        data: {"contactId": contactId},
        type: "get",
        dataType: "json",
        success: function (data) {
            if (data.code !== 0 && data.data !== null) {
                console.log("获取客户下联系人列表信息失败，message: " + data.message);
                return
            }

            //处理集采客户
            $("#contact_position_1").html(data.data.positions);
            //处理集采客户 -- 需显示集团标示信息
            if (data.data.groupLevel==1) {
                $('.group-label.parent').show();
            } else {
                $('.group-label.child').show();
            }
            $('input[name=groupContactPositions]').val(data.data.positions);
            $("#allowed_goods_types_1").html(data.data.allowedGoodsTypes);
        }
    });
}

function openAddGoodsView(saleOrderId) {
    layer.open({
        type: 2,
        title: '添加商品',
        area: ['1000px', '800px'],
        content: '/order/jc/addJcOrderGoods.do?saleorderId=' + saleOrderId
    });
}

function openEditOrderView(id) {
    return $("#" + id).click();
}

function openDeleteOrderView(id) {
    return $("#" + id).click();
}



function checkGoodsRangeForGroupCustomer() {
    return true;
}


var form;

function renderSearchSelect() {
    form.render('select');
}

$(function () {
    layui.use('form', function(){
        form = layui.form;

        //切换客户联系人时刷新职位等 信息
        form.on('select(traderContactChangedFilter)', function(data) {
            triggerContactInfoChanged();
        });


        form.on('select(takeTraderFilter)', function(data) {
            var name = $('#take_trader_1 option:selected').text();
            var val = $('#take_trader_1 option:selected').val();
            if(val==0 || val=='') {
                $('input[name=takeTraderId]').val('');
                $('input[name=takeTraderName]').val('');
                return
            }
            $('input[name=takeTraderId]').val(val);
            $('input[name=takeTraderName]').val(name);

            var lastAddressId = $('select[name=takeTraderAddressId] > option:selected').val();
            var lastContactId = $('select[name=takeTraderContactId] > option:selected').val();
            //根据客户ID获取联系人列表&地址列表
            getContactAndAddress(val , '1',lastAddressId, lastContactId);

            renderSearchSelect();
        });


        //切换收票客户时触发
        form.on('select(invoiceTraderFilter)', function(data) {
            var name = $('#take_trader_2 option:selected').text();
            var invoiceTraderId = $('#take_trader_2 option:selected').val();

            //获取客户账户信息
            getCustomerSettlementInfo(invoiceTraderId);

            if(invoiceTraderId == 0 || invoiceTraderId=='') {
                $('input[name=invoiceTraderId]').val('');
                $('input[name=invoiceTraderName]').val('');
                return
            }
            $('input[name=invoiceTraderId]').val(invoiceTraderId);
            $('input[name=invoiceTraderName]').val(name);

            var lastAddressId = $('select[name=invoiceTTraderAddressId] > option:selected').val();
            var lastContactId = $('select[name=invoiceTTraderContactId] > option:selected').val();
            //根据客户ID获取联系人列表&地址列表
            getContactAndAddress(invoiceTraderId , '2',lastAddressId, lastContactId);

            renderSearchSelect();
        });

        /**
         * 获取客户信息及帐期信息
         *
         * @param traderId
         */
        function getCustomerSettlementInfo(traderId) {
            if (traderId == null || traderId == '' || traderId == 0) {
                $('#invoiceCustomerPeriodBalance').text('0.00');
                $('#invoiceCustomerPeriodDay').text('0');
                return
            }

            $.ajax({
                url: '/trader/customer/getCustomerSettlementInfo.do',
                data:{"traderId": traderId },
                type:"GET",
                dataType : "json",
                success:function(data){
                    if (data.code == 0 && data.data != null) {
                        var customerInfo = data.data;

                        //客户当前账期剩余额度
                        var accountPeriodLeft = returnFloat(customerInfo.accountPeriodLeft);
                        var periodAmount = returnFloat(customerInfo.periodAmount);

                        var parsedPeriodDay = parseInt(customerInfo.periodDay);
                        var periodDay = isNaN(parsedPeriodDay) ? 0 : parsedPeriodDay;
                        //收票客户帐期天数
                        $('#periodDay').val(periodDay);
                        //收票客户帐期余额
                        $("#accountPeriodLeft").val(accountPeriodLeft);
                        //收票客户帐期总额
                        $('#accountPeriodAmount').val(periodAmount);

                        $('#invoiceCustomerPeriodBalance').text(accountPeriodLeft);
                        $('#invoiceCustomerPeriodDay').text(periodDay);

                        //触发付款结算计算
                        $("#paymentType").trigger('change');
                    } else {
                        layer.alert('获取收票客户的帐期失败，原因: '+data.message,{ icon: 2 });
                        $('#invoiceCustomerPeriodBalance').text('0.00');
                        $('#invoiceCustomerPeriodDay').text('0');
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }


        // getCustomerSettlementInfo($('input[name=invoiceTraderId]').val());

        function returnFloat(value){
            var value=Math.floor(parseFloat(value)*100)/100;
            var xsd=value.toString().split(".");
            if(xsd.length==1){
                value=value.toString()+".00";
                return value;
            }
            if(xsd.length>1){
                if(xsd[1].length<2){
                    value=value.toString()+"0";
                }
                return value;
            }
        }

        function getContactAndAddress(traderId , indexId, lastAddressId , lastContactId) {
            $.ajax({
                async:false,
                url: '/order/saleorder/getCustomerContactAndAddress.do',
                data:{"traderId": traderId },
                type:"POST",
                dataType : "json",
                success:function(data){
                    if (data.code == 0) {
                        var addressStr = '<option value="0">请选择</option>';
                        for(var i = 0; i< data.param.addressList.length; i++) {
                            var isSelected = data.param.addressList[i].traderAddress.traderAddressId == lastAddressId? 'selected = "selected"' : '';
                            addressStr += '<option value="' + data.param.addressList[i].traderAddress.traderAddressId + '" ' + isSelected + '>' + data.param.addressList[i].area + '/' + data.param.addressList[i].traderAddress.address + '</option>';
                        }
                        var contactStr = '<option value="0">请选择</option>';
                        for(var i = 0; i< data.param.contactList.length; i++) {
                            var isSelected = data.param.contactList[i].traderContactId == lastContactId ? 'selected = "selected"' : '';
                            contactStr += '<option value="' + data.param.contactList[i].traderContactId + '" ' + isSelected + '>' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '</option>';
                        }
                        $("#contact_"+indexId).html(contactStr);
                        $("#address_"+indexId).html(addressStr);

                        $("#add_contact_"+indexId).attr('layerParams', '{"width":"430px","height":"220px","title":"添加联系人","link":"/order/jc/addContact.do?traderId='+ traderId +'&indexId=' + indexId + '"}');
                        $("#add_address_"+indexId).attr('layerParams', '{"width":"600px","height":"200px","title":"添加地址","link":"/order/jc/addAddress.do?traderId='+traderId+'&indexId=' + indexId + '"}');
                    } else {
                        layer.alert(data.message,{ icon: 2 });
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }
    });

});
function deliveryClaimChange(){
    if($("#deliveryClaimSelect").val() ==1){
        $("#waitDeadlineDiv").show();
    }else{
        $("#waitDeadlineDiv").hide();
        $("#deliveryDelayTimeStr").val('');
    }
}




