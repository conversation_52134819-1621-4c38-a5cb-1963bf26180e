package com.vedeng.goods.model;

import java.util.ArrayList;
import java.util.List;

public class CoreSkuDescGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public CoreSkuDescGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGoodsDescIdIsNull() {
            addCriterion("GOODS_DESC_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdIsNotNull() {
            addCriterion("GOODS_DESC_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdEqualTo(Integer value) {
            addCriterion("GOODS_DESC_ID =", value, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdNotEqualTo(Integer value) {
            addCriterion("GOODS_DESC_ID <>", value, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdGreaterThan(Integer value) {
            addCriterion("GOODS_DESC_ID >", value, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_DESC_ID >=", value, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdLessThan(Integer value) {
            addCriterion("GOODS_DESC_ID <", value, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_DESC_ID <=", value, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdIn(List<Integer> values) {
            addCriterion("GOODS_DESC_ID in", values, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdNotIn(List<Integer> values) {
            addCriterion("GOODS_DESC_ID not in", values, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_DESC_ID between", value1, value2, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_DESC_ID not between", value1, value2, "goodsDescId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdIsNull() {
            addCriterion("GOODS_SPU_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdIsNotNull() {
            addCriterion("GOODS_SPU_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdEqualTo(Integer value) {
            addCriterion("GOODS_SPU_ID =", value, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdNotEqualTo(Integer value) {
            addCriterion("GOODS_SPU_ID <>", value, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdGreaterThan(Integer value) {
            addCriterion("GOODS_SPU_ID >", value, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_SPU_ID >=", value, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdLessThan(Integer value) {
            addCriterion("GOODS_SPU_ID <", value, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_SPU_ID <=", value, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdIn(List<Integer> values) {
            addCriterion("GOODS_SPU_ID in", values, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdNotIn(List<Integer> values) {
            addCriterion("GOODS_SPU_ID not in", values, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_SPU_ID between", value1, value2, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSpuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_SPU_ID not between", value1, value2, "goodsSpuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdIsNull() {
            addCriterion("GOODS_SKU_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdIsNotNull() {
            addCriterion("GOODS_SKU_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdEqualTo(Integer value) {
            addCriterion("GOODS_SKU_ID =", value, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdNotEqualTo(Integer value) {
            addCriterion("GOODS_SKU_ID <>", value, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdGreaterThan(Integer value) {
            addCriterion("GOODS_SKU_ID >", value, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_SKU_ID >=", value, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdLessThan(Integer value) {
            addCriterion("GOODS_SKU_ID <", value, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_SKU_ID <=", value, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdIn(List<Integer> values) {
            addCriterion("GOODS_SKU_ID in", values, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdNotIn(List<Integer> values) {
            addCriterion("GOODS_SKU_ID not in", values, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_SKU_ID between", value1, value2, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_SKU_ID not between", value1, value2, "goodsSkuId");
            return (Criteria) this;
        }

        public Criteria andDescTypeIsNull() {
            addCriterion("DESC_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDescTypeIsNotNull() {
            addCriterion("DESC_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDescTypeEqualTo(Byte value) {
            addCriterion("DESC_TYPE =", value, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeNotEqualTo(Byte value) {
            addCriterion("DESC_TYPE <>", value, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeGreaterThan(Byte value) {
            addCriterion("DESC_TYPE >", value, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("DESC_TYPE >=", value, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeLessThan(Byte value) {
            addCriterion("DESC_TYPE <", value, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeLessThanOrEqualTo(Byte value) {
            addCriterion("DESC_TYPE <=", value, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeIn(List<Byte> values) {
            addCriterion("DESC_TYPE in", values, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeNotIn(List<Byte> values) {
            addCriterion("DESC_TYPE not in", values, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeBetween(Byte value1, Byte value2) {
            addCriterion("DESC_TYPE between", value1, value2, "descType");
            return (Criteria) this;
        }

        public Criteria andDescTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("DESC_TYPE not between", value1, value2, "descType");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("GOODS_NAME is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("GOODS_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("GOODS_NAME =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("GOODS_NAME <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("GOODS_NAME >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("GOODS_NAME >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("GOODS_NAME <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("GOODS_NAME <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("GOODS_NAME like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("GOODS_NAME not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("GOODS_NAME in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("GOODS_NAME not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("GOODS_NAME between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("GOODS_NAME not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptIsNull() {
            addCriterion("SEO_DESCRIPT is null");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptIsNotNull() {
            addCriterion("SEO_DESCRIPT is not null");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptEqualTo(String value) {
            addCriterion("SEO_DESCRIPT =", value, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptNotEqualTo(String value) {
            addCriterion("SEO_DESCRIPT <>", value, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptGreaterThan(String value) {
            addCriterion("SEO_DESCRIPT >", value, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptGreaterThanOrEqualTo(String value) {
            addCriterion("SEO_DESCRIPT >=", value, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptLessThan(String value) {
            addCriterion("SEO_DESCRIPT <", value, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptLessThanOrEqualTo(String value) {
            addCriterion("SEO_DESCRIPT <=", value, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptLike(String value) {
            addCriterion("SEO_DESCRIPT like", value, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptNotLike(String value) {
            addCriterion("SEO_DESCRIPT not like", value, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptIn(List<String> values) {
            addCriterion("SEO_DESCRIPT in", values, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptNotIn(List<String> values) {
            addCriterion("SEO_DESCRIPT not in", values, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptBetween(String value1, String value2) {
            addCriterion("SEO_DESCRIPT between", value1, value2, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoDescriptNotBetween(String value1, String value2) {
            addCriterion("SEO_DESCRIPT not between", value1, value2, "seoDescript");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsIsNull() {
            addCriterion("SEO_KEYWORDS is null");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsIsNotNull() {
            addCriterion("SEO_KEYWORDS is not null");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsEqualTo(String value) {
            addCriterion("SEO_KEYWORDS =", value, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsNotEqualTo(String value) {
            addCriterion("SEO_KEYWORDS <>", value, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsGreaterThan(String value) {
            addCriterion("SEO_KEYWORDS >", value, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsGreaterThanOrEqualTo(String value) {
            addCriterion("SEO_KEYWORDS >=", value, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsLessThan(String value) {
            addCriterion("SEO_KEYWORDS <", value, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsLessThanOrEqualTo(String value) {
            addCriterion("SEO_KEYWORDS <=", value, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsLike(String value) {
            addCriterion("SEO_KEYWORDS like", value, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsNotLike(String value) {
            addCriterion("SEO_KEYWORDS not like", value, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsIn(List<String> values) {
            addCriterion("SEO_KEYWORDS in", values, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsNotIn(List<String> values) {
            addCriterion("SEO_KEYWORDS not in", values, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsBetween(String value1, String value2) {
            addCriterion("SEO_KEYWORDS between", value1, value2, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoKeywordsNotBetween(String value1, String value2) {
            addCriterion("SEO_KEYWORDS not between", value1, value2, "seoKeywords");
            return (Criteria) this;
        }

        public Criteria andSeoTitleIsNull() {
            addCriterion("SEO_TITLE is null");
            return (Criteria) this;
        }

        public Criteria andSeoTitleIsNotNull() {
            addCriterion("SEO_TITLE is not null");
            return (Criteria) this;
        }

        public Criteria andSeoTitleEqualTo(String value) {
            addCriterion("SEO_TITLE =", value, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleNotEqualTo(String value) {
            addCriterion("SEO_TITLE <>", value, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleGreaterThan(String value) {
            addCriterion("SEO_TITLE >", value, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleGreaterThanOrEqualTo(String value) {
            addCriterion("SEO_TITLE >=", value, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleLessThan(String value) {
            addCriterion("SEO_TITLE <", value, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleLessThanOrEqualTo(String value) {
            addCriterion("SEO_TITLE <=", value, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleLike(String value) {
            addCriterion("SEO_TITLE like", value, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleNotLike(String value) {
            addCriterion("SEO_TITLE not like", value, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleIn(List<String> values) {
            addCriterion("SEO_TITLE in", values, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleNotIn(List<String> values) {
            addCriterion("SEO_TITLE not in", values, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleBetween(String value1, String value2) {
            addCriterion("SEO_TITLE between", value1, value2, "seoTitle");
            return (Criteria) this;
        }

        public Criteria andSeoTitleNotBetween(String value1, String value2) {
            addCriterion("SEO_TITLE not between", value1, value2, "seoTitle");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated do_not_delete_during_merge Mon May 13 16:57:05 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SKU_DESC
     *
     * @mbg.generated Mon May 13 16:57:05 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}