<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.GeAuthorizationVerifyMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationVerify">
    <id column="AUTHORIZATION_VERIFY_ID" jdbcType="INTEGER" property="authorizationVerifyId" />
    <result column="AUTHORIZATION_ID" jdbcType="INTEGER" property="authorizationId" />
    <result column="STATS" jdbcType="INTEGER" property="stats" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    AUTHORIZATION_VERIFY_ID, AUTHORIZATION_ID, STATS, CONTENT, CREATOR_NAME, CREATOR, 
    ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_GE_AUTHORIZATION_VERIFY
    where AUTHORIZATION_VERIFY_ID = #{authorizationVerifyId,jdbcType=INTEGER}
  </select>
  <select id="selectByAuthorizationId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_GE_AUTHORIZATION_VERIFY
    where AUTHORIZATION_ID = #{authorizationId}
    order by ADD_TIME
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_GE_AUTHORIZATION_VERIFY
    where AUTHORIZATION_VERIFY_ID = #{authorizationVerifyId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AUTHORIZATION_VERIFY_ID" keyProperty="authorizationVerifyId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationVerify" useGeneratedKeys="true">
    insert into T_GE_AUTHORIZATION_VERIFY (AUTHORIZATION_ID, STATS, CONTENT, 
      CREATOR_NAME, CREATOR, ADD_TIME
      )
    values (#{authorizationId,jdbcType=INTEGER}, #{stats,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, 
      #{creatorName,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="AUTHORIZATION_VERIFY_ID" keyProperty="authorizationVerifyId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationVerify" useGeneratedKeys="true">
    insert into T_GE_AUTHORIZATION_VERIFY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authorizationId != null">
        AUTHORIZATION_ID,
      </if>
      <if test="stats != null">
        STATS,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="authorizationId != null">
        #{authorizationId,jdbcType=INTEGER},
      </if>
      <if test="stats != null">
        #{stats,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationVerify">
    update T_GE_AUTHORIZATION_VERIFY
    <set>
      <if test="authorizationId != null">
        AUTHORIZATION_ID = #{authorizationId,jdbcType=INTEGER},
      </if>
      <if test="stats != null">
        STATS = #{stats,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where AUTHORIZATION_VERIFY_ID = #{authorizationVerifyId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorizationVerify">
    update T_GE_AUTHORIZATION_VERIFY
    set AUTHORIZATION_ID = #{authorizationId,jdbcType=INTEGER},
      STATS = #{stats,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
    where AUTHORIZATION_VERIFY_ID = #{authorizationVerifyId,jdbcType=INTEGER}
  </update>
</mapper>