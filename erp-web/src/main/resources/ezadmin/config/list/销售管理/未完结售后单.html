<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>(销售工作台)未完结售后单</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="OXVhmgCl9dM" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">售后单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="AFTER_SALES_NO" placeholder="请输入" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">审核状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline">
      <xm class=" layui-input list-search-item " type="select" name="CURRENT_USER_ID" placeholder="" style="" alias="sd" jdbctype="" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="JSON" oper="IN" validate_rules="" validate_messages=""></xm>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">订单状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="ATFER_SALES_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">合并日期搜索</label>
     <div class="layui-input-inline">
      <search class=" layui-input list-search-item " type="date" name="ADD_TIME,VALID_TIME,PAYMENT_TIME,DELIVERY_TIME,INVOICE_TIME,ARRIVAL_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="" validate_rules="" validate_messages=""></search>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">收货时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="ARRIVAL_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.ARRIVAL_TIME= 0,         NULL,         a.ARRIVAL_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">开票时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="INVOICE_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( s.INVOICE_TIME= 0,         NULL,         s.INVOICE_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">发货时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="DELIVERY_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( s.DELIVERY_TIME= 0,         NULL,         s.DELIVERY_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">付款时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="PAYMENT_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( s.PAYMENT_TIME= 0,         NULL,         s.PAYMENT_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">生效时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="VALID_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( s.VALID_TIME= 0,         NULL,         s.VALID_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="ADD_TIME" placeholder="请选择日期" style="" alias="FROM_UNIXTIME( IF ( s.ADD_TIME= 0,         NULL,         s.ADD_TIME) / 1000,         '%Y-%m-%d %H:%i:%S' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="AFTER_SALES_NO" name="AFTER_SALES_NO" body="td-link" order="0" url="/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=${AFTER_SALES_ID}" opentype="PARENT" windowname="${AFTER_SALES_NO}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">售后单号</th>
      <th item_name="ORDER_NO" name="ORDER_NO" body="td-link" order="0" url="/orderstream/saleorder/detail.do?saleOrderId=${ORDER_ID}&amp;scene=0" opentype="PARENT" windowname="${ORDER_NO}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">对应订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="${TRADER_NAME}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
      <th item_name="CURRENT_USER_ID" name="CURRENT_USER_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="SELECT A.USER_ID K,A.USERNAME V  FROM T_USER A
where  IS_DISABLED=0  AND COMPANY_ID=1" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属销售</th>
      <th item_name="CURRENT_ORG_ID" name="CURRENT_ORG_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select
		ORG_ID K,ORG_NAME V
		from T_ORGANIZATION" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售部门</th>
      <th item_name="TYPE" name="TYPE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="SELECT SYS_OPTION_DEFINITION_ID K, TITLE V FROM T_SYS_OPTION_DEFINITION WHERE STATUS = 1   AND PARENT_ID IN (535, 537)   AND (RELATED_FIELD IS NULL or RELATED_FIELD = '')" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">业务类型</th>
      <th item_name="CREATOR" name="CREATOR" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="SELECT A.USER_ID K,A.USERNAME V  FROM T_USER A
where  IS_DISABLED=0  AND COMPANY_ID=1" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建人</th>
      <th item_name="SERVICE_USER_ID" name="SERVICE_USER_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="SELECT A.USER_ID K,A.USERNAME V  FROM T_USER A
where  IS_DISABLED=0  AND COMPANY_ID=1" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">售后人员</th>
      <th item_name="ATFER_SALES_STATUS" name="ATFER_SALES_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单状态</th>
      <th item_name="STATUS" name="STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">审核状态</th>
      <th item_name="OVER_TIME" name="OVER_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">未完结时效</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">申请时间</th>
      <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">生效时间</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="order by a.AFTER_SALES_ID DESC" groupby="">StringBuilder sql=new StringBuilder("
SELECT
        a.AFTER_SALES_ID,
	a.AFTER_SALES_NO,
	a.ORDER_ID,
	a.ORDER_NO,
	b.TRADER_ID,
	t.TRADER_NAME,
	sd.CURRENT_USER_ID,
	sd.CURRENT_ORG_ID,
	a.TYPE,
	a.CREATOR,
	a.SERVICE_USER_ID,
	a.STATUS,
	a.ATFER_SALES_STATUS,
CASE	
		WHEN a.ATFER_SALES_STATUS = 1 THEN
		FORMAT( ( unix_timestamp( NOW( ) ) - a.VALID_TIME / 1000 ) / 3600 / 24, 2 ) ELSE '' 
	END OVER_TIME,
	FROM_UNIXTIME( IF ( a.ADD_TIME = 0, NULL, a.ADD_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' ) AS ADD_TIME,
	FROM_UNIXTIME( IF ( a.VALID_TIME = 0, NULL, a.VALID_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' ) AS VALID_TIME 
FROM
	T_AFTER_SALES a
        LEFT JOIN T_SALEORDER s ON a.`ORDER_ID` = s.`SALEORDER_ID`
	LEFT JOIN T_AFTER_SALES_DETAIL b ON a.AFTER_SALES_ID = b.AFTER_SALES_ID
	LEFT JOIN T_TRADER t ON b.TRADER_ID = t.TRADER_ID
	LEFT JOIN T_SALEORDER_DATA sd ON  a.`ORDER_ID` = sd.SALEORDER_ID
WHERE
	a.TYPE NOT IN ( 546, 547, 548, 549 ) 
	AND a.COMPANY_ID = 1 
	AND a.ATFER_SALES_STATUS in (0,1)
");


if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " AND sd.CURRENT_USER_ID in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}
list=search(sql);
return list;</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>