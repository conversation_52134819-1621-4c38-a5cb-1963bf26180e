package com.vedeng.common.trace.service.impl;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.service.TrackBaseService;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.util.TrackUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 新增前台客户信息埋点
 * @ClassName:  CustomerAddFromFrontStrategyImpl     
 * @author: <PERSON>.yang
 * @date:   2024年6月6日 下午5:45:27    
 * @Copyright:
 */
@Component
public class CustomerAddFromFrontStrategyImpl  extends TrackBaseService implements TrackStrategy {

	private static final Logger LOGGER = LoggerFactory.getLogger(CustomerAddFromFrontStrategyImpl.class);
	
	@Override
	public EventTrackingEnum getTrackStrategy() {
		return EventTrackingEnum.BASE_INFO_NEW_CUSTOMER_FROM_FRONT;
	}

	@Override
	public boolean checkParamsLegal(TrackParamsData trackParamsData) {
		//获取唯一标识符
		Map<String, Object> trackParams = trackParamsData.getTrackParams();
		String primaryKey = trackParamsData.getEventTrackingEnum().getPrimaryKey();
		String primaryValue = TrackUtils.getJsonValue(new JSONObject(trackParams),primaryKey);
		//唯一标识符获取不到，不能埋点
		if(StringUtils.isEmpty(primaryValue)) {
			LOGGER.info("=======埋点档案：{}，必填字段：{}缺失...",trackParamsData.getEventTrackingEnum().getArchivedName(),primaryKey);
			return false;
		}
		return true;
	}
	
}
