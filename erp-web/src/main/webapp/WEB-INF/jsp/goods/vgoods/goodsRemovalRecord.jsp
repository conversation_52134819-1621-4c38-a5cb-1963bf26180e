<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>商品迁移记录</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}"/>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css"/>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/goodsRemovalRecord.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/lib/dialog.css?rnd=${resourceVersionKey}">
    <%--<link rel="stylesheet"--%>
    <%--href="${pageContext.request.contextPath}/static/css/meinian/layui.css">--%>
    <script type="text/javascript"
            src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
    <script type="text/javascript"
            src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
    <%--<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>--%>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/goodsRemovalRecord.js?rnd=${resourceVersionKey}"></script>

</head>
<body>
<div class="erp-wrap">
    <div class="list-pages-search">
        <form method="get" id="search" action="${pageContext.request.contextPath}/goods/vgoods/goodsRemovalRecord.do">
            <ul>
                <li>
                    <label class="infor_name">SPU名称</label>
                    <input type="text" class="input-middle" name="spuName" id="spuName"
                           value="${queryCondition.spuName}"/>
                </li>
                <li>
                    <label class="infor_name">SPU_ID</label>
                    <input type="text" class="input-middle" name="spuId" id="spuId" value="${queryCondition.spuId}"/>
                </li>
                <li>
                    <label class="infor_name">操作人</label>
                    <input type="text" class="input-middle" name="operatorName" id="operatorName"
                           value="${queryCondition.operatorName}"/>
                </li>
                <li>
                    <label class="infor_name">操作时间</label>
                    <input class="Wdate f_left input-smaller96 m0" type="text"
                           placeholder="请选择日期"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'cdendtime\')}'})" autocomplete="off"
                           name="operateBeginTime" id="cdstarttime"
                           value="${queryCondition.operateBeginTime}">
                    <div class="f_left ml1 mr1 mt4">-</div>
                    <input
                            class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期"
                            onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'cdstarttime\')}'})" autocomplete="off"
                            name="operateEndTime" id="cdendtime" value="${queryCondition.operateEndTime}">
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
            </div>
        </form>
    </div>
    <form autocomplete="off" onsubmit="return false;">
        <div class="erp-block base-form erp-block-list">
            <div class="option-wrap J-fix-wrap">
                <div class="option-fix-wrap cf J-fix-cnt">
                    <button class="btn btn-small J-export-record">导出</button>
                    <div class="option-r"></div>
                </div>
            </div>
            <div class="list-table">
                <div class="table-th">
                    <div class="th">
                        <div class="input-checkbox">
                            <label class="input-wrap">
                                <input type="checkbox" class="J-select-list-all">
                                <span class="input-ctnr"></span>
                            </label>
                        </div>
                    </div>
                    <div class="th">SPU_ID</div>
                    <div class="th">SPU名称</div>
                    <div class="th">原始路径</div>
                    <div class="th">目标路径</div>
                    <div class="th">原始父亲ID</div>
                    <div class="th">目标父亲ID</div>
                    <div class="th">SKU数</div>
                    <div class="th">增补属性数</div>
                    <div class="th">迁移原因</div>
                    <div class="th">操作时间</div>
                    <div class="th">操作人</div>
                </div>
                <c:if test="${not empty goodsRemovalRecordVoList}">
                <c:forEach var="goodsRemovalRecordVo" items="${goodsRemovalRecordVoList}">
                <div class="table-tr">
                    <div class="tr-lv1 J-item-wrap J-list">
                        <div class="tr-list">
                            <div class="tr-item">
                                <div class="input-checkbox">
                                    <label class="input-wrap">
                                        <input type="checkbox" class="J-select-spu"
                                               value="${goodsRemovalRecordVo.spuRemovalRecordId}">
                                        <span class="input-ctnr"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="tr-item">${goodsRemovalRecordVo.spuId}</div>
                            <div class="tr-item">${goodsRemovalRecordVo.spuShowName}</div>
                            <div class="tr-item">${goodsRemovalRecordVo.originalPath}</div>
                            <div class="tr-item">${goodsRemovalRecordVo.targetPath}</div>
                            <div class="tr-item">${goodsRemovalRecordVo.originalParentId}</div>
                            <div class="tr-item">${goodsRemovalRecordVo.targetParentId}</div>
                            <div class="tr-item"><a href="javascript:void(0)"
                                                    onclick="showSKuPropertyValue(${goodsRemovalRecordVo.spuId})">${goodsRemovalRecordVo.skuCount}</a>
                            </div>
                            <div class="tr-item"><a href="javascript:void(0)"
                                                    onclick="showChangedPropertyValue(${goodsRemovalRecordVo.targetCategoryId}, ${goodsRemovalRecordVo.spuRemovalRecordId})">${goodsRemovalRecordVo.addedAttributeCount}</a>
                            </div>
                            <div class="tr-item" style="word-break: break-all;">${goodsRemovalRecordVo.reason}</div>
                            <div class="tr-item">${goodsRemovalRecordVo.lastModTime}</div>
                            <div class="tr-item">${goodsRemovalRecordVo.lastOperatorName}</div>
                        </div>
                    </div>
                    </c:forEach>
                    </c:if>
                    <c:if test="${empty goodsRemovalRecordVoList}">
                        <div class="table-tr no-data">
                            <tr>
                                <td colspan="16">
                                    <!-- 查询无结果弹出 --> 查询无结果！请尝试使用其他搜索条件。
                                </td>
                            </tr>
                        </div>
                    </c:if>
                </div>

                <c:if test="${page.totalPage > 1}">
                    <tags:pageNew page="${page}"/>
                </c:if>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    //spu迁移增补属性明细
    function showChangedPropertyValue(targetCategoryId, spuRemovalLogId) {
        layer.open({
            title: 'SPU迁移属性列表页',
            content: page_url + '/goods/vgoods/getSpuRemovalDetail.do?categoryId='+targetCategoryId+"&spuRemovalLogId="+ spuRemovalLogId,
            type: 2,
            area: ['850px', '500px']
            ,btn: ['确认']
            ,btn1: function(index, layero){
                layer.close(index);
            }
        })
    }


    //spu迁移增补属性明细
    function showSKuPropertyValue(spuId) {
        layer.open({
            title: 'SKU列表',
            content: page_url + '/goods/vgoods/getSkuInfoAfterSpuRemoval.do?spuId='+ spuId,
            type: 2,
            area: ['850px', '500px']
            ,btn: ['确认']
            ,btn1: function(index, layero){
                layer.close(index);
            }
        });
    }
</script>
</body>
</html>