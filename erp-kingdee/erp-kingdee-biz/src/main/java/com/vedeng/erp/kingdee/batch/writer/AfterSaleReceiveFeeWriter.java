package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchReceiveFeeDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceVoucherMapper;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2023−02-02 下午1:36
 * @description (蓝票)售后费用应收单，推送金蝶
 */
@Service
@Slf4j
public class AfterSaleReceiveFeeWriter extends BaseWriter<BatchReceiveFeeDto> {

    @Autowired
    private KingDeeReceiveFeeApiService kingDeeReceiveFeeApiService;

    @Override
    public void doWrite(BatchReceiveFeeDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        KingDeeReceiveFeeDto kingDeeReceiveFeeDto = dto.getKingDeeReceiveFeeDto();
        kingDeeReceiveFeeDto.setKingDeeBizEnums(KingDeeBizEnums.saveReceiveFee);
        kingDeeReceiveFeeApiService.register(kingDeeReceiveFeeDto,true);

    }
}
