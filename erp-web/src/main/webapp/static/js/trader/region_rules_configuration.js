function addRegionRules(){
    // layer.open({
    //     type: 2,
    //     title:'新增区域',
    //     closeBtn: 1,
    //     btn: ['提交', '取消'],
    //     anim: 2,
    //     offset: '20px',
    //     skin: 'demo-class',
    //     area: ['97%', '97%'],
    //     shadeClose: false,
    //     content: './addRegionRules.do',
    //     btnAlign: 'c',
    //     fixed: false,
    //     yes:function (index, layero){
    //         //获得弹出层iframe
    //         let iframe = window["layui-layer-iframe" + index].callbackdata();
    //         // console.log(iframe)
    //         return false;
    //     },
    //     btn2:function (index, layero) {
    //         // console.log(index,layero);
    //         layer.close(index);
    //     }
    //
    // });
    window.location.href='/trader/customer/addRegionRules.do';
}

function updateRegionRules(publicCustomerRegionRulesId){
    layer.open({
        type: 2,
        title:'编辑区域',
        closeBtn: 1,
        btn: ['提交', '取消'],
        anim: 2,
        offset: '20px',
        skin: 'demo-class',
        area: ['97%', '97%'],
        shadeClose: false,
        content: './editRegionRules.do?publicCustomerRegionRulesId='+publicCustomerRegionRulesId,
        btnAlign: 'c',
        fixed: false,
        yes:function (index, layero){
            //获得弹出层iframe
            let iframe = window["layui-layer-iframe" + index].callbackdata();
            // console.log(iframe)
            // return false;
            $("#message").html("归属部门："+"&nbsp;&nbsp;"+"主管：");
            // layer.close(index);
            // $("#search").submit();
        },
        btn2:function (index, layero) {
            // console.log(index,layero);
            $("#message").html("归属部门："+"&nbsp;&nbsp;"+"主管：");
            // layer.close(index);
        }

    });
}
function checkAll(obj){
    if($(obj).is(":checked")){
        $("input[name='reginTrId']").prop("checked",true);
    }else {
        $("input[name='reginTrId']").prop("checked",false);
    }
}

function checkThis(obj){
    let length = $("input[name='reginTrId']").length;
    let checked = $("input[name='reginTrId']:checked").length;
    if(length==checked){
        if (checked!=0) {
            $("#reginTrAll").prop("checked",true);
        }
    }else {
        $("#reginTrAll").prop("checked",false);
    }
    // if($(obj).is(":checked")){
    //     $("input[name='reginTrId']").prop("checked",true);
    // }else {
    //     $("input[name='reginTrId']").prop("checked",false);
    // }
}

function deleteSelected(){
    let length = $("input[name='reginTrId']:checked").length;
    var _list =  new Array()
    $("input[name='reginTrId']:checked").each(
        function (i){
            // _list[i]=$(this).val()
            _list.push($(this).val());
        }
    )
    // alert(_list);
    if (_list.length > 0) {
        layer.confirm('是否删除本页选中的记录?', {icon: 3, title:'提示'}, function(index){
            //do something
            layer.close(index);
            $.ajax({
                type: "POST",
                url: "/trader/customer/batchDeleteRegionRule.do",
                data:  {"idList":_list},
                dataType:'json',
                traditional: true,
                success: function(data){
                    if (data.code == 0) {
                        layer.msg('操作成功', {
                            icon: 1,
                            time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            //do something

                            $("#search").submit();
                        });
                        // debugger

                        // layer.alert("操作成功失败");
                    } else {
                        layer.msg('操作失败', {
                            icon: 2,
                            time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            //do something
                            $("#search").submit();
                        });
                    }

                },
                error:function(data){
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });
        });
    }else {
        layer.alert("请至少选择1个");
        // layer.msg('请选择至少1个', {
        //     icon: 2,
        //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
        // }, function(){
        //     //do something
        // });
    }

}