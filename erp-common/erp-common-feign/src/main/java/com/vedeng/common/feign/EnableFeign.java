package com.vedeng.common.feign;

import com.vedeng.common.feign.domain.FeignFactoryBean;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 开启feign注解
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import(FeignApiRegistrar.class)
public @interface EnableFeign {

    String[] value() default {};


    String[] basePackages() default {};


    Class<? extends Annotation> annotationClass() default Annotation.class;


    Class<? extends FeignFactoryBean> factoryBean() default FeignFactoryBean.class;

}