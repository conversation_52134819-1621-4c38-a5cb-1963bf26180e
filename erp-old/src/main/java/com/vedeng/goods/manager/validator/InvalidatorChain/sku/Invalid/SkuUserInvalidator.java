package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSpu;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SkuUserInvalidatorError;

/**
 * (12)	归属产品经理或归属产品助理点击禁用；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:06.
 * @author: <PERSON><PERSON>.
 */
@Service
public class SkuUserInvalidator extends GoodsInvalidatorChain {


    @Resource
    CoreSpuMapper coreSpuMapper;

    @Override
    public void doInvalid() {
        User user = getUser();
        String SkuNo = getSkuNo();
        List<String> skuNos = Collections.singletonList(SkuNo);
        List<CoreSpu> spuList =  coreSpuMapper.getSpuInfoBySkuNos(skuNos);
        Optional<CoreSpu> first = spuList.stream().filter(e -> !(e.getAssignmentManagerId().equals(user.getUserId()) || e.getAssignmentAssistantId().equals(user.getUserId()))).findFirst();
        if(first.isPresent()){
            throw new InvalidatorChainException(SkuUserInvalidatorError.getCode(),SkuUserInvalidatorError.getMessage());
        }
    }
}
