package com.vedeng.erp.buyorderexpense.mapper;

import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemDetailEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description ${end}
 * @date 2022/10/17 17:43
 **/
@Repository
public interface BuyorderExpenseItemDetailMapper {
    /**
     * delete by primary key
     *
     * @param buyorderExpenseItemDetailId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer buyorderExpenseItemDetailId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BuyorderExpenseItemDetailEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BuyorderExpenseItemDetailEntity record);

    /**
     * select by primary key
     *
     * @param buyorderExpenseItemDetailId primary key
     * @return object by primary key
     */
    BuyorderExpenseItemDetailEntity selectByPrimaryKey(Integer buyorderExpenseItemDetailId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BuyorderExpenseItemDetailEntity record);


    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByBuyorderExpenseItemIdSelective(BuyorderExpenseItemDetailEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BuyorderExpenseItemDetailEntity record);

    /**
     * 批量新增
     *
     * @param list List<BuyorderExpenseItemDetailEntity>
     * @return insert count
     */
    int batchInsert(List<BuyorderExpenseItemDetailEntity> list);

    /**
     * 根据费用单itemIdList查出所有的detail（纯净版 不含连表的信息）
     *
     * @param itemIdList 费用单itemIdList
     * @return List<BuyorderExpenseItemDetailEntity>
     */
    List<BuyorderExpenseItemDetailEntity> getAllByItemIdList(@Param("itemIdList") List<Integer> itemIdList);

    /**
     * 批量修改
     * @param list List<BuyorderExpenseItemDetailEntity>
     * @return update count
     */
    int batchUpdate(List<BuyorderExpenseItemDetailEntity> list);
}