<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper">
    <sql id="Base_Column_List">
        WAREHOUSE_GOODS_OUT_IN_ID,
        OUT_IN_NO,
        WMS_NO,
        RELATE_NO,
        OUT_IN_TYPE,
        OUT_IN_COMPANY,
        OUT_IN_TIME,
        `SOURCE`,
        IS_DELETE,
        ADD_TIME,
        CREATOR,
        CREATOR_NAME,
        MOD_TIME,
        UPDATER,
        UPDATER_NAME,
        REMARK,
        UPDATE_REMARK,
        rate,
        IS_VIRTUAL,
        ORG_NAME,
        TRADER_CUSTOMER_ID
    </sql>
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto">
        <!--@mbg.generated-->
        <!--@Table T_WAREHOUSE_GOODS_OUT_IN-->
        <id column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="BIGINT" property="warehouseGoodsOutInId"/>
        <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo"/>
        <result column="WMS_NO" jdbcType="VARCHAR" property="wmsNo"/>
        <result column="RELATE_NO" jdbcType="VARCHAR" property="relateNo"/>
        <result column="OUT_IN_TYPE" jdbcType="INTEGER" property="outInType"/>
        <result column="OUT_IN_COMPANY" jdbcType="VARCHAR" property="outInCompany"/>
        <result column="OUT_IN_TIME" jdbcType="TIMESTAMP" property="outInTime"/>
        <result column="SOURCE" jdbcType="VARCHAR" property="source"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark"/>
        <result column="rate" jdbcType="VARCHAR" property="rate"/>
        <result column="IS_VIRTUAL" jdbcType="INTEGER" property="isVirtual"/>
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName"/>
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId"/>
        <result column="invoiceTypeName" jdbcType="VARCHAR" property="invoiceTypeName"/>
        <result column="SALE_SETTLEMENT_ADJUSTMENT_ID" jdbcType="INTEGER" property="saleSettlementAdjustmentId"/>
    </resultMap>
    
    <resultMap id="CollectionResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto">
        <id column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="BIGINT" property="warehouseGoodsOutInId"/>
        <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo"/>
        <result column="IS_VIRTUAL" jdbcType="INTEGER" property="isVirtual"/>

        <collection property="batchWarehouseGoodsOutInItemDtos" ofType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto">
            <result column="NUM" jdbcType="DECIMAL" property="num"/>
            <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" jdbcType="BIGINT" property="warehouseGoodsOutInDetailId"/>
            <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
            <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
        </collection>
    </resultMap>

    <select id="findByAll" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
        TWGOI.OUT_IN_NO, TWGOI.WMS_NO, TWGOI.RELATE_NO, TWGOI.OUT_IN_TYPE, TWGOI.OUT_IN_COMPANY, TWGOI.OUT_IN_TIME, TWGOI.SOURCE,
        TWGOI.IS_DELETE, TWGOI.ADD_TIME, TWGOI.CREATOR, TWGOI.CREATOR_NAME, TWGOI.MOD_TIME, TWGOI.UPDATER,
        TWGOI.UPDATER_NAME, TWGOI.REMARK,TWGOI.UPDATE_REMARK, TWGOI.IS_VIRTUAL
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        <where>
            <if test="warehouseGoodsOutInId != null">
                and WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null and outInNo != ''">
                and TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null and wmsNo != ''">
                and TWGOI.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="relateNo != null and relateNo != ''">
                and RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                and OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInCompany != null and outInCompany != ''">
                and OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR}
            </if>
            <if test="outInTime != null">
                and OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP}
            </if>
            <if test="source != null and source != ''">
                and `SOURCE` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and TWGOI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isVirtual != null">
                and IS_VIRTUAL = #{isVirtual,jdbcType=INTEGER}
            </if>
            <if test="outInTypeList != null and outInTypeList.size() > 0">
                and OUT_IN_TYPE in
                <foreach item="item" index="index" collection="outInTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="purchaseInFindByAll" resultMap="BaseResultMap">
        select

        TWGOI.WAREHOUSE_GOODS_OUT_IN_ID, TWGOI.OUT_IN_NO, TWGOI.WMS_NO, TWGOI.RELATE_NO, TWGOI.OUT_IN_TYPE, TWGOI.OUT_IN_COMPANY, TWGOI.OUT_IN_TIME, TWGOI.SOURCE,
        TWGOI.IS_DELETE, TWGOI.ADD_TIME, TWGOI.CREATOR, TWGOI.CREATOR_NAME, TWGOI.MOD_TIME, TWGOI.UPDATER, TWGOI.UPDATER_NAME, TWGOI.REMARK,
        TWGOI.UPDATE_REMARK,
        TSOD.COMMENTS rate,
        TSOD.TITLE     invoiceTypeName
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        left join T_BUYORDER TB on TB.BUYORDER_NO = TWGOI.RELATE_NO
        left join T_SYS_OPTION_DEFINITION TSOD  on TSOD.SYS_OPTION_DEFINITION_ID = TB.INVOICE_TYPE
        <where>
            and TB.IS_GIFT = 0
            <if test="warehouseGoodsOutInId != null">
                and TWGOI.WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null and outInNo != ''">
                and TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null and wmsNo != ''">
                and TWGOI.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="relateNo != null and relateNo != ''">
                and TWGOI.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                and TWGOI.OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInCompany != null and outInCompany != ''">
                and TWGOI.OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR}
            </if>
            <if test="outInTime != null">
                and TWGOI.OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP}
            </if>
            <if test="source != null and source != ''">
                and TWGOI.`SOURCE` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and TWGOI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>

            <if test="outInTypeList != null and outInTypeList.size() > 0">
                and TWGOI.OUT_IN_TYPE in
                <foreach item="item" index="index" collection="outInTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="buyOrderOutFindByAll" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
               TWGOI.OUT_IN_NO,
               TWGOI.WMS_NO,
               TWGOI.RELATE_NO,
               TWGOI.OUT_IN_TYPE,
               TWGOI.OUT_IN_COMPANY,
               TWGOI.OUT_IN_TIME,
               TWGOI.SOURCE,
               TWGOI.IS_DELETE,
               TWGOI.ADD_TIME,
               TWGOI.CREATOR,
               TWGOI.CREATOR_NAME,
               TWGOI.MOD_TIME,
               TWGOI.UPDATER,
               TWGOI.UPDATER_NAME,
               TWGOI.REMARK,
               TWGOI.UPDATE_REMARK,
               TSOD.COMMENTS rate
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                 left join T_AFTER_SALES TAS on TAS.AFTER_SALES_NO = TWGOI.RELATE_NO
                 left join T_BUYORDER TB on TB.BUYORDER_ID = TAS.ORDER_ID
                 left join T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TB.INVOICE_TYPE
        <where>
            and TB.IS_GIFT = 0
            <if test="warehouseGoodsOutInId != null">
                and TWGOI.WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null and outInNo != ''">
                and TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null and wmsNo != ''">
                and TWGOI.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="relateNo != null and relateNo != ''">
                and TWGOI.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                and TWGOI.OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInCompany != null and outInCompany != ''">
                and TWGOI.OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR}
            </if>
            <if test="outInTime != null">
                and TWGOI.OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP}
            </if>
            <if test="source != null and source != ''">
                and TWGOI.`SOURCE` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and TWGOI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>

            <if test="outInTypeList != null and outInTypeList.size() > 0">
                and TWGOI.OUT_IN_TYPE in
                <foreach item="item" index="index" collection="outInTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="giftBuyOrderFindByAll" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
               TWGOI.OUT_IN_NO,
               TWGOI.WMS_NO,
               TWGOI.RELATE_NO,
               TWGOI.OUT_IN_TYPE,
               TWGOI.OUT_IN_COMPANY,
               TWGOI.OUT_IN_TIME,
               TWGOI.SOURCE,
               TWGOI.IS_DELETE,
               TWGOI.ADD_TIME,
               TWGOI.CREATOR,
               TWGOI.CREATOR_NAME,
               TWGOI.MOD_TIME,
               TWGOI.UPDATER,
               TWGOI.UPDATER_NAME,
               TWGOI.REMARK,
               TWGOI.UPDATE_REMARK,
               TSOD.COMMENTS rate
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                 left join T_BUYORDER TB on TB.BUYORDER_NO = TWGOI.RELATE_NO
                 left join T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TB.INVOICE_TYPE
        <where>
            and TB.IS_GIFT = 1
            <if test="warehouseGoodsOutInId != null">
                and TWGOI.WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null and outInNo != ''">
                and TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null and wmsNo != ''">
                and TWGOI.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="relateNo != null and relateNo != ''">
                and TWGOI.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                and TWGOI.OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInCompany != null and outInCompany != ''">
                and TWGOI.OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR}
            </if>
            <if test="outInTime != null">
                and TWGOI.OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP}
            </if>
            <if test="source != null and source != ''">
                and TWGOI.`SOURCE` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and TWGOI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>

            <if test="outInTypeList != null and outInTypeList.size() > 0">
                and TWGOI.OUT_IN_TYPE in
                <foreach item="item" index="index" collection="outInTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="giftBuyOrderOutFindByAll" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
               TWGOI.OUT_IN_NO,
               TWGOI.WMS_NO,
               TWGOI.RELATE_NO,
               TWGOI.OUT_IN_TYPE,
               TWGOI.OUT_IN_COMPANY,
               TWGOI.OUT_IN_TIME,
               TWGOI.SOURCE,
               TWGOI.IS_DELETE,
               TWGOI.ADD_TIME,
               TWGOI.CREATOR,
               TWGOI.CREATOR_NAME,
               TWGOI.MOD_TIME,
               TWGOI.UPDATER,
               TWGOI.UPDATER_NAME,
               TWGOI.REMARK,
               TWGOI.UPDATE_REMARK,
               TSOD.COMMENTS rate
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                 left join T_AFTER_SALES TAS on TAS.AFTER_SALES_NO = TWGOI.RELATE_NO
                 left join T_BUYORDER TB on TB.BUYORDER_ID = TAS.ORDER_ID
                 left join T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TB.INVOICE_TYPE
        <where>
            and TB.IS_GIFT = 1
            <if test="warehouseGoodsOutInId != null">
                and TWGOI.WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null and outInNo != ''">
                and TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null and wmsNo != ''">
                and TWGOI.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="relateNo != null and relateNo != ''">
                and TWGOI.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                and TWGOI.OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInCompany != null and outInCompany != ''">
                and TWGOI.OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR}
            </if>
            <if test="outInTime != null">
                and TWGOI.OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP}
            </if>
            <if test="source != null and source != ''">
                and TWGOI.`SOURCE` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and TWGOI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>

            <if test="outInTypeList != null and outInTypeList.size() > 0">
                and TWGOI.OUT_IN_TYPE in
                <foreach item="item" index="index" collection="outInTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="handleGiftBuyOrderFindAll" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
               TWGOI.OUT_IN_NO,
               TWGOI.WMS_NO,
               TWGOI.RELATE_NO,
               TWGOI.OUT_IN_TYPE,
               TWGOI.OUT_IN_COMPANY,
               TWGOI.OUT_IN_TIME,
               TWGOI.SOURCE,
               TWGOI.IS_DELETE,
               TWGOI.ADD_TIME,
               TWGOI.CREATOR,
               TWGOI.CREATOR_NAME,
               TWGOI.MOD_TIME,
               TWGOI.UPDATER,
               TWGOI.UPDATER_NAME,
               TWGOI.REMARK,
               TWGOI.UPDATE_REMARK,
               TSOD.COMMENTS rate
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                 left join T_AFTER_SALES TAS on TAS.AFTER_SALES_NO = TWGOI.RELATE_NO
                 left join T_BUYORDER TB on TB.BUYORDER_ID = TAS.ORDER_ID
                 left join T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TB.INVOICE_TYPE
        <where>
            and TB.IS_GIFT = 1
            <if test="warehouseGoodsOutInId != null">
                and TWGOI.WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null and outInNo != ''">
                and TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null and wmsNo != ''">
                and TWGOI.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="relateNo != null and relateNo != ''">
                and TWGOI.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                and TWGOI.OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInCompany != null and outInCompany != ''">
                and TWGOI.OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR}
            </if>
            <if test="outInTime != null">
                and TWGOI.OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP}
            </if>
            <if test="source != null and source != ''">
                and TWGOI.`SOURCE` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and TWGOI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>

            <if test="outInTypeList != null and outInTypeList.size() > 0">
                and TWGOI.OUT_IN_TYPE in
                <foreach item="item" index="index" collection="outInTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="saleOrderInFindByAll" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
               TWGOI.OUT_IN_NO,
               TWGOI.WMS_NO,
               TWGOI.RELATE_NO,
               TWGOI.OUT_IN_TYPE,
               TWGOI.OUT_IN_COMPANY,
               TWGOI.OUT_IN_TIME,
               TWGOI.SOURCE,
               TWGOI.IS_DELETE,
               TWGOI.ADD_TIME,
               TWGOI.CREATOR,
               TWGOI.CREATOR_NAME,
               TWGOI.MOD_TIME,
               TWGOI.UPDATER,
               TWGOI.UPDATER_NAME,
               TWGOI.REMARK,
               TWGOI.UPDATE_REMARK,
               TWGOI.IS_VIRTUAL,
               TSOD.COMMENTS rate
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                 left join T_AFTER_SALES TAS on TAS.AFTER_SALES_NO = TWGOI.RELATE_NO
                 left join T_SALEORDER TS on TS.SALEORDER_ID = TAS.ORDER_ID
                 left join T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TS.INVOICE_TYPE
                 left join KING_DEE_SALE_RETURN_STOCK KDSRS on KDSRS.F_BILL_NO = TWGOI.OUT_IN_NO
        <where>
            <if test="warehouseGoodsOutInId != null">
                and TWGOI.WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null and outInNo != ''">
                and TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null and wmsNo != ''">
                and TWGOI.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="relateNo != null and relateNo != ''">
                and TWGOI.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                and TWGOI.OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInCompany != null and outInCompany != ''">
                and TWGOI.OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR}
            </if>
            <if test="source != null and source != ''">
                and TWGOI.`SOURCE` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and TWGOI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>

            <if test="outInTypeList != null and outInTypeList.size() > 0">
                and TWGOI.OUT_IN_TYPE in
                <foreach item="item" index="index" collection="outInTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
    <select id="saleOrderGetRate" resultType="java.lang.String">
        select
            TSOD.COMMENTS
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                 left join T_AFTER_SALES TAS on TAS.AFTER_SALES_NO = TWGOI.RELATE_NO
                 left join T_SALEORDER TS on TS.SALEORDER_ID = TAS.ORDER_ID
                 left join T_SYS_OPTION_DEFINITION TSOD  on TSOD.SYS_OPTION_DEFINITION_ID = TS.INVOICE_TYPE
        where 1 = 1
          and TWGOI.RELATE_NO = #{afterSalesNo,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="findSaleOrderGoodsBatch" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
        TWGOI.OUT_IN_NO, TWGOI.WMS_NO, TWGOI.RELATE_NO, TWGOI.OUT_IN_TYPE, TWGOI.OUT_IN_COMPANY, TWGOI.OUT_IN_TIME, TWGOI.SOURCE,
        TWGOI.IS_DELETE, TWGOI.ADD_TIME, TWGOI.CREATOR, TWGOI.CREATOR_NAME, TWGOI.MOD_TIME, TWGOI.UPDATER,
        TWGOI.UPDATER_NAME, TWGOI.REMARK,TWGOI.UPDATE_REMARK, TOR.ORG_NAME,TRC.TRADER_CUSTOMER_ID
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        left join T_SALEORDER TS ON TWGOI.RELATE_NO = TS.SALEORDER_NO
        LEFT JOIN T_SALEORDER_DATA TSD ON TS.SALEORDER_ID = TSD.SALEORDER_ID
        LEFT JOIN T_ORGANIZATION TOR ON TSD.CURRENT_ORG_ID = TOR.ORG_ID
        LEFT JOIN T_TRADER_CUSTOMER TRC on TRC.TRADER_ID = TS.TRADER_ID
        where
        OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
        and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
        and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        and TWGOI.IS_DELETE = 0
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
    <select id="getWarehouseGoodsOutInItemByWarehouseId" resultType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto">
        SELECT
            vcs.SKU_NO ,
            ABS(twgoii.NUM) AS NUM ,
            tsg.PRICE ,
            tsod.COMMENTS AS ratio,
            tsg.DELIVERY_DIRECT ,
            IFNULL(tsg.IS_GIFT, 0) AS IS_GIFT,
            twgoii.BATCH_NUMBER,
            twgoii.BARCODE_FACTORY,
            tsg.HAVE_INSTALLATION,
            twgoii.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
        FROM
            T_WAREHOUSE_GOODS_OUT_IN_ITEM twgoii
        LEFT JOIN V_CORE_SKU vcs ON
            twgoii.GOODS_ID = vcs.SKU_ID
        LEFT JOIN T_SALEORDER_GOODS tsg ON
            twgoii.RELATED_ID = tsg.SALEORDER_GOODS_ID
        LEFT JOIN T_SALEORDER ts ON
            tsg.SALEORDER_ID = ts.SALEORDER_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION tsod ON
            tsod.SYS_OPTION_DEFINITION_ID = ts.INVOICE_TYPE
        WHERE
            twgoii.IS_DELETE = 0
            AND twgoii.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            AND twgoii.OPERATE_TYPE = 2
            AND twgoii.LOG_TYPE = 1
        GROUP BY
            twgoii.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
    </select>
    <select id="getByRelateNo" resultMap="CollectionResultMap">
    SELECT
        twgoi.WAREHOUSE_GOODS_OUT_IN_ID,
        twgoi.OUT_IN_NO,
        twgoi.IS_VIRTUAL,
        twgoii.NUM,
        twgoii.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
        twgoii.RELATED_ID,
        twgoii.GOODS_ID
    FROM
        T_WAREHOUSE_GOODS_OUT_IN twgoi
        LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM twgoii ON twgoi.OUT_IN_NO = twgoii.OUT_IN_NO AND twgoii.IS_DELETE = 0
    WHERE
        twgoi.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
        AND twgoi.OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
        AND twgoi.IS_DELETE = 0
    ORDER BY
        twgoi.IS_VIRTUAL ASC,
        twgoi.OUT_IN_TIME DESC
    </select>

    <insert id="insertSelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto" keyColumn="WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="warehouseGoodsOutInId" useGeneratedKeys="true">
        <!--          -->
        insert into T_WAREHOUSE_GOODS_OUT_IN
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="warehouseGoodsOutInId != null" >
                WAREHOUSE_GOODS_OUT_IN_ID,
            </if>
            <if test="outInNo != null" >
                OUT_IN_NO,
            </if>
            <if test="wmsNo != null" >
                WMS_NO,
            </if>
            <if test="relateNo != null" >
                RELATE_NO,
            </if>
            <if test="outInType != null" >
                OUT_IN_TYPE,
            </if>
            <if test="outInCompany != null" >
                OUT_IN_COMPANY,
            </if>
            <if test="outInTime != null" >
                OUT_IN_TIME,
            </if>
            <if test="source != null" >
                SOURCE,
            </if>
            <if test="isDelete != null" >
                IS_DELETE,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="creatorName != null" >
                CREATOR_NAME,
            </if>
            <if test="modTime != null" >
                MOD_TIME,
            </if>
            <if test="updater != null" >
                UPDATER,
            </if>
            <if test="updaterName != null" >
                UPDATER_NAME,
            </if>
            <if test="remark != null" >
                REMARK,
            </if>
            <if test="updateRemark != null" >
                UPDATE_REMARK,
            </if>
            <if test="isVirtual != null" >
                IS_VIRTUAL,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="warehouseGoodsOutInId != null" >
                #{warehouseGoodsOutInId,jdbcType=BIGINT},
            </if>
            <if test="outInNo != null" >
                #{outInNo,jdbcType=VARCHAR},
            </if>
            <if test="wmsNo != null" >
                #{wmsNo,jdbcType=VARCHAR},
            </if>
            <if test="relateNo != null" >
                #{relateNo,jdbcType=VARCHAR},
            </if>
            <if test="outInType != null" >
                #{outInType,jdbcType=BIT},
            </if>
            <if test="outInCompany != null" >
                #{outInCompany,jdbcType=VARCHAR},
            </if>
            <if test="outInTime != null" >
                #{outInTime,jdbcType=TIMESTAMP},
            </if>
            <if test="source != null" >
                #{source,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null" >
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null" >
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null" >
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null" >
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null" >
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null" >
                #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="isVirtual != null" >
                #{isVirtual,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="queryInfoByNo" resultMap="BaseResultMap">
        SELECT
            WAREHOUSE_GOODS_OUT_IN_ID,OUT_IN_NO,RELATE_NO,OUT_IN_TYPE
            FROM T_WAREHOUSE_GOODS_OUT_IN
        WHERE OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
        AND OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
        AND IS_DELETE = 0
    </select>

    <select id="findByRelateNo" resultMap="BaseResultMap">
        SELECT
        WAREHOUSE_GOODS_OUT_IN_ID,
        OUT_IN_NO, WMS_NO, RELATE_NO, OUT_IN_TYPE, OUT_IN_COMPANY, OUT_IN_TIME
        FROM T_WAREHOUSE_GOODS_OUT_IN
        <where>
            <if test="relateNo != null">
                AND RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                AND OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInType != null">
                AND IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="outInType != null">
                AND IS_VIRTUAL = #{isVirtual,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="findByItem" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
        TWGOI.OUT_IN_NO, TWGOI.WMS_NO, TWGOI.RELATE_NO, TWGOI.OUT_IN_TYPE, TWGOI.OUT_IN_COMPANY, TWGOI.OUT_IN_TIME, TWGOI.SOURCE,
        TWGOI.IS_DELETE, TWGOI.ADD_TIME, TWGOI.CREATOR, TWGOI.CREATOR_NAME, TWGOI.MOD_TIME, TWGOI.UPDATER,
        TWGOI.UPDATER_NAME, TWGOI.REMARK,TWGOI.UPDATE_REMARK, TWGOI.IS_VIRTUAL
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        <where>
            <if test="warehouseGoodsOutInId != null">
                and WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
            </if>
            <if test="outInNo != null and outInNo != ''">
                and TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
            </if>
            <if test="wmsNo != null and wmsNo != ''">
                and TWGOI.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
            </if>
            <if test="relateNo != null and relateNo != ''">
                and RELATE_NO = #{relateNo,jdbcType=VARCHAR}
            </if>
            <if test="outInType != null">
                and OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
            </if>
            <if test="outInCompany != null and outInCompany != ''">
                and OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR}
            </if>
            <if test="outInTime != null">
                and OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP}
            </if>
            <if test="source != null and source != ''">
                and `SOURCE` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="isDelete != null">
                and TWGOI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isVirtual != null">
                and IS_VIRTUAL = #{isVirtual,jdbcType=INTEGER}
            </if>
            <if test="outInTypeList != null and outInTypeList.size() > 0">
                and OUT_IN_TYPE in
                <foreach item="item" index="index" collection="outInTypeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-04-26-->
    <select id="findByOutInNo" resultMap="BaseResultMap">
        select
        *
        from T_WAREHOUSE_GOODS_OUT_IN
        where OUT_IN_NO=#{outInNo,jdbcType=VARCHAR}
    </select>

    <select id="getSaleTraderNameBySaleOrder" resultType="java.lang.String">
        select TRADER_NAME from T_SALEORDER T WHERE SALEORDER_NO= #{orderNo,jdbcType=VARCHAR}
        AND IFNULL(T.TRADER_NAME,'') !=''
        AND IS_DELETE=0
        LIMIT 1
    </select>

    <select id="getBarcodFactoryListByOrderNo" resultType="java.lang.String">
        select TWGOII.BARCODE_FACTORY barcodeFactory
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
        WHERE TWGOI.RELATE_NO IN
        <foreach collection="orderNos" item="orderNo" index="index"
                 open="(" close=")" separator=",">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
        AND TWGOI.OUT_IN_TYPE=1
        AND TWGOII.LOG_TYPE=0
        AND TWGOI.IS_DELETE=0
        AND TWGOII.IS_DELETE=0
        AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
        AND IFNULL(TWGOII.BARCODE_FACTORY, '') != ''
        order by TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID desc
    </select>

    <select id="getExistBarcodFactoryList" resultType="java.lang.String">
        select TWGOII.BARCODE_FACTORY
        from T_AFTER_SALES TFS
        JOIN T_WAREHOUSE_GOODS_OUT_IN TW ON TFS.AFTER_SALES_NO = TW.RELATE_NO
        JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TW.OUT_IN_NO = TWGOII.OUT_IN_NO
        WHERE TW.IS_DELETE=0
        AND TWGOII.IS_DELETE=0
        AND TFS.ORDER_NO=#{orderNo,jdbcType=VARCHAR}
        AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
        AND TFS.SUBJECT_TYPE=536
        AND TW.OUT_IN_TYPE IN (6,7)
        AND IFNULL(TWGOII.BARCODE_FACTORY, '') != ''
    </select>

    <select id="getBatchNumberListByOrderNo" resultType="java.lang.String">
        select TWGOII.BATCH_NUMBER barcodeFactory
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
        WHERE TWGOI.RELATE_NO = #{orderNo,jdbcType=VARCHAR}
        AND TWGOI.OUT_IN_TYPE=1
        AND TWGOII.LOG_TYPE=0
        AND TWGOI.IS_DELETE=0
        AND TWGOII.IS_DELETE=0
        AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
        AND IFNULL(TWGOII.BATCH_NUMBER, '') != ''
    </select>

<!--auto generated by MybatisCodeHelper on 2023-06-19-->
    <select id="selectWarehouseOutInOrder" resultType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto">
        select TWGOI.*
        from  T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
            left join T_WAREHOUSE_GOODS_OUT_IN TWGOI  on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
        where TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID in
        <foreach collection="itemIds" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
        group by TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,TWGOI.OUT_IN_TIME
        order by  TWGOI.OUT_IN_TIME

    </select>

    <select id="getUsedBarcodFactoryList" resultType="java.lang.String">
        select distinct twgoii.BARCODE_FACTORY
        from T_WAREHOUSE_GOODS_OUT_IN_ITEM twgoii
                 left join T_WAREHOUSE_GOODS_OUT_IN twgoi on twgoii.OUT_IN_NO = twgoi.OUT_IN_NO
        where twgoii.OPERATE_TYPE = 2
          and twgoi.OUT_IN_TYPE = 2
          and twgoii.IS_DELETE = 0
          and twgoi.IS_DELETE = 0
          and twgoi.RELATE_NO = #{orderNo,jdbcType=VARCHAR}
          and twgoii.GOODS_ID = #{goodsId,jdbcType=INTEGER}
          and IFNULL(twgoii.BARCODE_FACTORY, '') != ''
    </select>

    <select id="getGiftBarcodFactoryListByOrderNo" resultType="java.lang.String">
        select TWGOII.BARCODE_FACTORY barcodeFactory
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
        WHERE TWGOI.RELATE_NO IN
        <foreach collection="orderNos" item="orderNo" index="index"
                 open="(" close=")" separator=",">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
        AND TWGOI.OUT_IN_TYPE=17
        AND TWGOII.LOG_TYPE=0
        AND TWGOI.IS_DELETE=0
        AND TWGOII.IS_DELETE=0
        AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
        AND IFNULL(TWGOII.BARCODE_FACTORY, '') != ''
        order by TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID desc
    </select>

    <select id="getGiftBatchNumberListByOrderNo" resultType="java.lang.String">
        select TWGOII.BATCH_NUMBER barcodeFactory
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
        WHERE TWGOI.RELATE_NO = #{orderNo,jdbcType=VARCHAR}
        AND TWGOI.OUT_IN_TYPE=17
        AND TWGOII.LOG_TYPE=0
        AND TWGOI.IS_DELETE=0
        AND TWGOII.IS_DELETE=0
        AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
        AND IFNULL(TWGOII.BATCH_NUMBER, '') != ''
    </select>


    <!--auto generated by MybatisCodeHelper on 2023-06-20-->
    <select id="findByIsVirtualAndRelateNo" resultMap="BaseResultMap">
        select
        *
        from T_WAREHOUSE_GOODS_OUT_IN
        where IS_VIRTUAL=#{isVirtual,jdbcType=INTEGER} and RELATE_NO=#{relateNo,jdbcType=VARCHAR}
    </select>

    <select id="findSaleOrderGiftGoodsAndHaveInvoice" resultMap="BaseResultMap">
        select c.OUT_IN_NO,
        0 wholeGiftSaleOrder
        from T_INVOICE a
        left join T_SALEORDER_GOODS b on a.RELATED_ID = b.SALEORDER_ID
        left join T_WAREHOUSE_GOODS_OUT_IN_ITEM c on  c.RELATED_ID = b.SALEORDER_GOODS_ID
        left join KING_DEE_RECEIVE_COMMON e on e.F_QZOK_BDDJT_ID = c.OUT_IN_NO
        where a.TYPE = 505 and a.COMPANY_ID = 1 and a.IS_ENABLE =1 and a.COLOR_TYPE =2 and a.RELATED_ID >0 and b.IS_GIFT = 1
        and c.OPERATE_TYPE = 2
            <if test="findGiftAndHaveBlueInvoiceTime!=null">
                and a.ADD_TIME> #{findGiftAndHaveBlueInvoiceTime,jdbcType=BIGINT}
            </if>
          and c.OUT_IN_NO is not null
        and  e.ID is null
        group by  c.OUT_IN_NO
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="findWholeSaleOrderGiftGoodsAndHaveInvoice" resultMap="BaseResultMap">
        select TWGOII.OUT_IN_NO,
               1 wholeGiftSaleOrder,
               giftSaleOrder.TRADER_CUSTOMER_ID
        from T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
            join (
            SELECT TS.SALEORDER_NO,
                   TSG.SALEORDER_GOODS_ID,
                   TTC.TRADER_CUSTOMER_ID
            FROM T_SALEORDER TS
                     LEFT JOIN T_SALEORDER_GOODS TSG ON TS.SALEORDER_ID = TSG.SALEORDER_ID  AND TSG.IS_DELETE = 0
                     left join T_TRADER_CUSTOMER TTC on  TS.TRADER_ID  = TTC.TRADER_ID
            GROUP BY TS.SALEORDER_NO
            HAVING COUNT(TSG.SALEORDER_ID) = SUM(CASE WHEN TSG.PRICE <![CDATA[<=]]> 0 THEN 1 ELSE 0 END)
        AND COUNT(TSG.SALEORDER_ID) <![CDATA[>=]]>1
        ORDER BY TS.SALEORDER_NO DESC
        ) giftSaleOrder on TWGOII.RELATED_ID = giftSaleOrder.SALEORDER_GOODS_ID
        LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOII.OUT_IN_NO = TWGOI.OUT_IN_NO
        left join KING_DEE_RECEIVE_COMMON KDRC on KDRC.F_QZOK_BDDJT_ID = TWGOII.OUT_IN_NO
        where TWGOII.OPERATE_TYPE = 2
        and TWGOII.OUT_IN_NO is not null
        and KDRC.ID is null
        <if test="findGiftAndHaveBlueInvoiceTime!=null">
            and TWGOI.OUT_IN_TIME> #{findGiftAndHaveBlueInvoiceTime,jdbcType=BIGINT}
        </if>
        group by TWGOII.OUT_IN_NO
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="findSaleOrderGiftGoodsAfterSaleAndHaveInvoice" resultMap="BaseResultMap">
        select
            e.OUT_IN_NO
        from
            T_INVOICE a
                left join
            T_SALEORDER_GOODS b
            on a.RELATED_ID = b.SALEORDER_ID
                left join T_AFTER_SALES  c on c.ORDER_ID = a.RELATED_ID and c.SUBJECT_TYPE = 535
                left join T_AFTER_SALES_GOODS d on c.AFTER_SALES_ID = d.AFTER_SALES_ID and d.ORDER_DETAIL_ID = b.SALEORDER_GOODS_ID
                left join T_WAREHOUSE_GOODS_OUT_IN_ITEM e on d.AFTER_SALES_GOODS_ID = e.RELATED_ID
        left join KING_DEE_RECEIVE_COMMON f on f.F_QZOK_BDDJT_ID = e.OUT_IN_NO
        where
            a.TYPE = 505
          and a.COMPANY_ID = 1
          and a.IS_ENABLE =1
          and a.COLOR_TYPE =2
            and c.ATFER_SALES_STATUS = 2
          and a.RELATED_ID >0
          and b.IS_GIFT = 1
          and e.OPERATE_TYPE = 5
            <if test="findGiftAndHaveBlueInvoiceTime!=null">
                and a.ADD_TIME> #{findGiftAndHaveBlueInvoiceTime,jdbcType=BIGINT}
            </if>
          and e.OUT_IN_NO is not null
          and f.ID is null
        group by
            e.OUT_IN_NO
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="querySaleAdjustPriceNew" resultMap="BaseResultMap">
        select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID,
               TWGOI.OUT_IN_NO,
               TWGOI.WMS_NO,
               TWGOI.RELATE_NO,
               TWGOI.OUT_IN_TYPE,
               TWGOI.OUT_IN_COMPANY,
               TWGOI.OUT_IN_TIME,
               TWGOI.SOURCE,
               TWGOI.IS_VIRTUAL
        from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                 left join T_SALEORDER TS on TWGOI.RELATE_NO = TS.SALEORDER_NO
                 left join T_SALEORDER_GOODS TSG on TS.SALEORDER_ID = TSG.SALEORDER_ID and TSG.IS_DELETE = 0
                 left join V_CORE_SKU VCS on TSG.GOODS_ID = VCS.SKU_ID
                 left join T_SALE_SETTLEMENT_ADJUSTMENT TSSA on TSSA.SALEORDER_NO = TWGOI.RELATE_NO and TSSA.ADJUSTMENT_TYPE = 1
        where TWGOI.OUT_IN_TYPE = 2
          and TWGOI.IS_DELETE = 0
          and TWGOI.IS_VIRTUAL = 0
          and TS.SALEORDER_ID is not null
          and TS.PAYMENT_STATUS is not null
          and TS.PAYMENT_STATUS != 0
          and TSG.SALEORDER_GOODS_ID is not null
          and TSG.SKU not in ('V127063', 'V251526', 'V256675', 'V253620', 'V251462', 'V140633')
          and TSG.PRICE = 0
          and VCS.IS_VIRTURE_SKU != 1
          and TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID is null
        <if test="beginTime != null">
            and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        group by TWGOI.WAREHOUSE_GOODS_OUT_IN_ID
        order by TWGOI.WAREHOUSE_GOODS_OUT_IN_ID desc
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryAfterSaleAdjustmentPushNew" resultMap="BaseResultMap">
        select B.WAREHOUSE_GOODS_OUT_IN_ID,
               B.OUT_IN_NO,
               B.WMS_NO,
               B.RELATE_NO,
               B.OUT_IN_TYPE,
               B.OUT_IN_COMPANY,
               B.OUT_IN_TIME,
               B.SOURCE,
               B.IS_VIRTUAL,
               B.SALE_SETTLEMENT_ADJUSTMENT_ID
        from (select TWGOI.*,
                     TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID,
                     concat(TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID, '-',
                            TWGOI.WAREHOUSE_GOODS_OUT_IN_ID) as F_QZOK_BDDJTID
              from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                       left join T_SALE_SETTLEMENT_ADJUSTMENT TSSA
                                 on TSSA.SALEORDER_NO = TWGOI.RELATE_NO and TSSA.ADJUSTMENT_TYPE = 2
              where TWGOI.OUT_IN_TYPE = 2
                and TWGOI.IS_DELETE = 0
                and TWGOI.IS_VIRTUAL = 0
                and TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID is not null
                <if test="beginTime != null">
                    and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
                </if>
                <if test="endTime != null">
                    and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
                </if>
              group by TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID,TWGOI.WAREHOUSE_GOODS_OUT_IN_ID) as B
                 left join KING_DEE_SALE_SETTLEMENT_ADJUSTMENT KDSSA on B.F_QZOK_BDDJTID = KDSSA.F_QZOK_BDDJTID
        where KDSSA.ID is null
        order by B.WAREHOUSE_GOODS_OUT_IN_ID desc
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="querySaleAdjustmentPushNew" resultMap="BaseResultMap">
        select B.WAREHOUSE_GOODS_OUT_IN_ID,
               B.OUT_IN_NO,
               B.WMS_NO,
               B.RELATE_NO,
               B.OUT_IN_TYPE,
               B.OUT_IN_COMPANY,
               B.OUT_IN_TIME,
               B.SOURCE,
               B.IS_VIRTUAL,
               B.SALE_SETTLEMENT_ADJUSTMENT_ID
        from (select TWGOI.*,
                     TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID,
                     concat(TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID, '-',
                            TWGOI.WAREHOUSE_GOODS_OUT_IN_ID) as F_QZOK_BDDJTID
              from T_WAREHOUSE_GOODS_OUT_IN TWGOI
                       left join T_SALE_SETTLEMENT_ADJUSTMENT TSSA
                                 on TSSA.SALEORDER_NO = TWGOI.RELATE_NO and TSSA.ADJUSTMENT_TYPE = 1
              where TWGOI.OUT_IN_TYPE = 2
                and TWGOI.IS_DELETE = 0
                and TWGOI.IS_VIRTUAL = 0
                and TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID is not null
                <if test="beginTime != null">
                    and TWGOI.OUT_IN_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
                </if>
                <if test="endTime != null">
                    and TWGOI.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
                </if>
              group by TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID,TWGOI.WAREHOUSE_GOODS_OUT_IN_ID) as B
                 left join KING_DEE_SALE_SETTLEMENT_ADJUSTMENT KDSSA on B.F_QZOK_BDDJTID = KDSSA.F_QZOK_BDDJTID
        where KDSSA.ID is null
        order by B.WAREHOUSE_GOODS_OUT_IN_ID desc
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
</mapper>