package com.vedeng.erp.saleorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022−11-15 下午3:42
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ConfirmationDto implements Serializable {
    private String confirmationName;
    private Integer batchId;
    private String batchNo;
    private Integer auditStatus;
    private String comments;
    private Long modTime;
    private String modTimeLabel;
    private String batchTime;
}
