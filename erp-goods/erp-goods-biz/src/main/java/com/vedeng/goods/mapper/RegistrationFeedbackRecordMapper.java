package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.dto.FeedbackCountDto;
import com.vedeng.goods.domain.entity.RegistrationFeedbackRecordEntity;
import com.vedeng.goods.dto.RegistrationFeedbackDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022−10-31 上午9:21
 * @description
 */
@Repository("registrationFeedbackRecordMapper")
public interface RegistrationFeedbackRecordMapper {

    int insert(RegistrationFeedbackRecordEntity registrationFeedbackRecord);

    List<RegistrationFeedbackDto> listByFirstEngageId(Integer firstEngageId);

    int messageReplyById(@Param("id") Integer registrationFeedbackRecordId, @Param("messageReply") String messageReply,@Param("modTime")Long modTime,@Param("userId")Integer userId);

    RegistrationFeedbackRecordEntity getById(Integer registrationFeedbackRecordId);

    List<FeedbackCountDto> feedbackCount(Integer registrationNumberId);

}
