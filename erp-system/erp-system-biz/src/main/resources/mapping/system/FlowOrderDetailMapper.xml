<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.FlowOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.FlowOrderDetailEntity">
    <!--@mbg.generated-->
    <!--@Table T_FLOW_ORDER_DETAIL-->
    <id column="FLOW_ORDER_DETAIL_ID" property="flowOrderDetailId" />
    <result column="FLOW_ORDER_ID" property="flowOrderId" />
    <result column="SKU_ID" property="skuId" />
    <result column="SKU_NO" property="skuNo" />
    <result column="PRODUCT_NAME" property="productName" />
    <result column="BRAND" property="brand" />
    <result column="MODEL" property="model" />
    <result column="UNIT" property="unit" />
    <result column="QUANTITY" property="quantity" />
    <result column="IS_DELETE" property="isDelete" />
    <result column="CREATOR" property="creator" />
    <result column="UPDATER" property="updater" />
    <result column="CREATOR_NAME" property="creatorName" />
    <result column="UPDATER_NAME" property="updaterName" />
    <result column="ADD_TIME" property="addTime" />
    <result column="MOD_TIME" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FLOW_ORDER_DETAIL_ID, FLOW_ORDER_ID, SKU_ID, SKU_NO, PRODUCT_NAME, BRAND, MODEL, 
    UNIT, QUANTITY, IS_DELETE, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ADD_TIME, 
    MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER_DETAIL
    where FLOW_ORDER_DETAIL_ID = #{flowOrderDetailId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_FLOW_ORDER_DETAIL
    where FLOW_ORDER_DETAIL_ID = #{flowOrderDetailId}
  </delete>
  <insert id="insert" keyColumn="FLOW_ORDER_DETAIL_ID" keyProperty="flowOrderDetailId" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_ORDER_DETAIL (FLOW_ORDER_ID, SKU_ID, SKU_NO, PRODUCT_NAME, BRAND, MODEL, UNIT, 
      QUANTITY, IS_DELETE, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
      ADD_TIME, MOD_TIME)
    values (#{flowOrderId}, #{skuId}, #{skuNo}, #{productName}, #{brand}, #{model}, #{unit}, 
      #{quantity}, #{isDelete}, #{creator}, #{updater}, #{creatorName}, #{updaterName}, 
      #{addTime}, #{modTime})
  </insert>
  <insert id="insertSelective" keyColumn="FLOW_ORDER_DETAIL_ID" keyProperty="flowOrderDetailId" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_ORDER_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowOrderId != null">
        FLOW_ORDER_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO,
      </if>
      <if test="productName != null and productName != ''">
        PRODUCT_NAME,
      </if>
      <if test="brand != null and brand != ''">
        BRAND,
      </if>
      <if test="model != null and model != ''">
        MODEL,
      </if>
      <if test="unit != null and unit != ''">
        UNIT,
      </if>
      <if test="quantity != null">
        QUANTITY,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowOrderId != null">
        #{flowOrderId},
      </if>
      <if test="skuId != null">
        #{skuId},
      </if>
      <if test="skuNo != null and skuNo != ''">
        #{skuNo},
      </if>
      <if test="productName != null and productName != ''">
        #{productName},
      </if>
      <if test="brand != null and brand != ''">
        #{brand},
      </if>
      <if test="model != null and model != ''">
        #{model},
      </if>
      <if test="unit != null and unit != ''">
        #{unit},
      </if>
      <if test="quantity != null">
        #{quantity},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
      <if test="creator != null">
        #{creator},
      </if>
      <if test="updater != null">
        #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName},
      </if>
      <if test="addTime != null">
        #{addTime},
      </if>
      <if test="modTime != null">
        #{modTime},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderDetailEntity">
    <!--@mbg.generated-->
    update T_FLOW_ORDER_DETAIL
    <set>
      <if test="flowOrderId != null">
        FLOW_ORDER_ID = #{flowOrderId},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId},
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO = #{skuNo},
      </if>
      <if test="productName != null and productName != ''">
        PRODUCT_NAME = #{productName},
      </if>
      <if test="brand != null and brand != ''">
        BRAND = #{brand},
      </if>
      <if test="model != null and model != ''">
        MODEL = #{model},
      </if>
      <if test="unit != null and unit != ''">
        UNIT = #{unit},
      </if>
      <if test="quantity != null">
        QUANTITY = #{quantity},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete},
      </if>
      <if test="creator != null">
        CREATOR = #{creator},
      </if>
      <if test="updater != null">
        UPDATER = #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime},
      </if>
    </set>
    where FLOW_ORDER_DETAIL_ID = #{flowOrderDetailId}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderDetailEntity">
    <!--@mbg.generated-->
    update T_FLOW_ORDER_DETAIL
    set FLOW_ORDER_ID = #{flowOrderId},
      SKU_ID = #{skuId},
      SKU_NO = #{skuNo},
      PRODUCT_NAME = #{productName},
      BRAND = #{brand},
      MODEL = #{model},
      UNIT = #{unit},
      QUANTITY = #{quantity},
      IS_DELETE = #{isDelete},
      CREATOR = #{creator},
      UPDATER = #{updater},
      CREATOR_NAME = #{creatorName},
      UPDATER_NAME = #{updaterName},
      ADD_TIME = #{addTime},
      MOD_TIME = #{modTime}
    where FLOW_ORDER_DETAIL_ID = #{flowOrderDetailId}
  </update>

<!--auto generated by MybatisCodeHelper on 2025-01-08-->
  <select id="findByFlowOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_FLOW_ORDER_DETAIL
        where FLOW_ORDER_ID=#{flowOrderId} and IS_DELETE = 0
    </select>
</mapper>