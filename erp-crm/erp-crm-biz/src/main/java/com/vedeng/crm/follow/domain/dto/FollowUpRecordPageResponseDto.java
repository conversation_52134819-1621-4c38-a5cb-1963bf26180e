package com.vedeng.crm.follow.domain.dto;

import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import lombok.Data;

import java.util.List;

/**
 * 跟进记录页面查询参数
 */
@Data
public class FollowUpRecordPageResponseDto {

    /**
     * 商机跟进记录列表
     */
    private List<CommunicateRecordDto> chanceFollowUpRecordList;
    
    /**
     * 线索跟进记录列表
     */
    private List<CommunicateRecordDto> leadsFollowUpRecordList;

    private  List<CommunicateRecordDto> visitFollowUpRecordList;
}
