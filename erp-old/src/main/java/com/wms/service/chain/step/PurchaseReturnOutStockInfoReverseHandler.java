package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 同步库存信息 -> 反向处理
 */
@Service
public class PurchaseReturnOutStockInfoReverseHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseReturnOutWmsRequestHandler.class);

    @Autowired
    @Qualifier("purchaseReturnReverseCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    protected void doDealWith(HandlerStepContext handlerStepContext) throws Exception {

        List<WmsLogicalOrdergoods> wmsLogicalOrdergoods = handlerStepContext.get("logicalOrdergoodList");
        AfterSalesVo afterSalesVo = handlerStepContext.get("afterSalesInfo");

        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();

        wmsLogicalOrdergoods.stream()
                .forEach(wmsLogicalOrdergood -> {
                    StockCalculateDto stockCalculateDto = new StockCalculateDto();
                    stockCalculateDto.setSku(wmsLogicalOrdergood.getSku());
                    stockCalculateDto.setLogicalWarehouseId(wmsLogicalOrdergood.getLogicalWarehouseId());
                    stockCalculateDto.setOccupyNum(wmsLogicalOrdergood.getOccupyNum());
                    stockCalculateList.add(stockCalculateDto);
                });

        //采购退货的库存计算接口
        List<WarehouseDto> warehouseList = stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);

        StockInfoDto stockInfoDto = new StockInfoDto();
        stockInfoDto.setRelatedNo(afterSalesVo.getAfterSalesNo());
        stockInfoDto.setWarehouseStockList(warehouseList);

        LOGGER.info("采购退货出库审核通过，然后关闭，更新库存服务数据========================:" + JSON.toJSONString(stockInfoDto));

        //更新库存服务 较少占用库存
        warehouseStockService.updateStockInfo(stockInfoDto);

    }
}
