package com.vedeng.crm.presales.web.controller;

import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.business.business.service.CrmBusinessChanceService;
import com.vedeng.crm.presales.dto.PreSalesDataEnum;
import com.vedeng.crm.presales.dto.PreSalesInfoDto;
import com.vedeng.crm.presales.dto.PreSalesInfoFromVisitDto;
import com.vedeng.crm.presales.dto.PreSalesInfoType;
import com.vedeng.crm.presales.facade.PreSalesFacade;
import com.vedeng.crm.presales.mapstruct.PreSalesInfoToChanceConverter;
import com.vedeng.crm.presales.mapstruct.PreSalesInfoToLeadsConverter;
import com.vedeng.crm.shard.service.ShareService;
import com.vedeng.crm.visitrecord.domain.dto.VisitRecordInputDto;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.common.Constants;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/9
 */
@ExceptionController
@RestController
@RequestMapping("/crm/presales/profile")
@Slf4j
public class PreSalesController {

    @Autowired
    private PreSalesFacade preSalesFacade;

    @Autowired
    private BusinessLeadsService businessLeadsService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private CrmBusinessChanceService crmBusinessChanceService;

    @Autowired
    private PreSalesInfoToLeadsConverter preSalesInfoToLeadsConverter;

    @Autowired
    private PreSalesInfoToChanceConverter preSalesInfoToChanceConverter;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private CrmVisitRecordService visitRecordService;

    /**
     * 新增商机/新增线索-综合页
     * @param preSalesInfoDto
     * @return
     */
    @MenuDesc(menuValue = "C0201",menuDesc = "新增商机/新增线索-综合页")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<PreSalesInfoType> add(@RequestBody PreSalesInfoFromVisitDto preSalesInfoDto) {
        //PreSalesInfoDto preSalesInfoDto = preSalesInfoFromVisitDto;
        if(preSalesInfoDto.getVisitId() != null){
            preSalesInfoDto.setClueType(Constants.ID_6012);//如果由拜访计划创建，来源改为拜访
        }

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        preSalesInfoDto.setCreator(currentUser.getId());
        preSalesInfoDto.setCreatorName(currentUser.getUsername());
        preSalesInfoDto.setAddTime(new Date());
        if(preSalesInfoDto.getBelongerId() == null || preSalesInfoDto.getBelongerId() == 0){
            preSalesInfoDto.setBelongerId(currentUser.getId());
            preSalesInfoDto.setBelonger(currentUser.getUsername());
        }
        PreSalesInfoType data = preSalesFacade.addPreSalesInfo(preSalesInfoDto);

        if(preSalesInfoDto.getVisitId() != null){
            VisitRecordInputDto.RelateBizData relateBizData = new VisitRecordInputDto.RelateBizData();//step2:将线索或商机与拜访计划进行关联
            relateBizData.setBizId(data.getDataId());
            relateBizData.setBizNo(data.getDataNo());
            relateBizData.setRelateType(data.getPreSaleDataEnum().getDataType());
            visitRecordService.saveBusinessChanceForVisit(relateBizData,preSalesInfoDto.getVisitId(),currentUser);//step3:保存关联
        }


        return R.success(data);
    }

    /**
     * 更新商机/更新线索-综合页
     * @param preSalesInfoDto
     * @return
     */
    @MenuDesc(menuValue = "C0202,C0102",menuDesc = "更新商机/更新线索-综合页")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<PreSalesInfoType> update(@RequestBody PreSalesInfoDto preSalesInfoDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        preSalesInfoDto.setUpdater(currentUser.getId());
        preSalesInfoDto.setUpdaterName(currentUser.getUsername());
        preSalesInfoDto.setModTime(new Date());
        PreSalesInfoType data = preSalesFacade.updatePreSalesInfo(preSalesInfoDto);
        return R.success(data);
    }


    /**
     * 商机/线索-详情页
     * @param dataId 线索ID或商机ID
     * @param type 1或2 1线索2商机
     * @return
     */
    @MenuDesc(menuValue = "C01,C02,C0203,C0102,C0202",menuDesc = "商机/线索-详情页")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public R<PreSalesInfoDto> detail(@RequestParam(required = true) Integer dataId,@RequestParam(required = true) int type) {
//        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (type == 1) {
            CurrentUser currentUser = CurrentUser.getCurrentUser();
            BusinessLeadsDto dto = businessLeadsService.getOne(dataId);
            if(dto !=null && !businessLeadsService.checkUserIsZongji(currentUser.getId()) ){
                List<Integer> userIdList = new ArrayList<>();
                userIdList.add(dto.getCreator());
                userIdList.add(dto.getBelongerId());
                List<RSalesJBusinessOrderDto> list = shareService.getShareListByBusinessId(dataId,5);
                if(CollectionUtils.isNotEmpty(list)){
                    List<Integer> saleUserIds = list.stream()
                            .map(RSalesJBusinessOrderDto::getSaleUserId)
                            .collect(Collectors.toList());
                    userIdList.addAll(saleUserIds);
                }
                if(!userIdList.contains(currentUser.getId())){
                    List<Integer> sonList = userApiService.queryUserIdListSubFromUac(currentUser.getId());
                    boolean  containSon =  userIdList.stream().anyMatch(sonList::contains);
                    if(!containSon){
                        boolean notAssign =  dto.getBelongerId()==null ||  dto.getBelongerId()<1;
                        if(notAssign){
                            return R.error("您无该条单据的查看权限。");
                        }else{
                            return R.error("您无该条单据的查看权限，可联系归属销售" + dto.getBelonger()+ "申请查看");
                        }

                    }
                }
            }

            PreSalesInfoDto preSalesInfoDto = preSalesInfoToLeadsConverter.toResponseBody(dto);
            PreSalesInfoType preSalesInfoType = new PreSalesInfoType();
            preSalesInfoType.setDataId(dto.getId());
            preSalesInfoType.setPreSaleDataEnum(PreSalesDataEnum.BUSSINESS_LEADS);
            preSalesInfoDto.setPreSalesInfoType(preSalesInfoType);
            return R.success(preSalesInfoDto);

        }else if(type == 2) {
            BusinessChanceDto businessChanceDto = crmBusinessChanceService.detail(dataId);
            PreSalesInfoDto preSalesInfoDto = preSalesInfoToChanceConverter.toResponseBody(businessChanceDto);
//            preSalesInfoDto.setClueTypeName(sysOptionDefinitionApiService.getOptionDefinitionById(businessChanceDto.getClueType()).getTitle());

            preSalesInfoDto.setSourceName(sysOptionDefinitionApiService.getOptionDefinitionById(businessChanceDto.getSource()).getTitle());
            preSalesInfoDto.setCommunicationName(sysOptionDefinitionApiService.getOptionDefinitionById(businessChanceDto.getCommunication()).getTitle());
            preSalesInfoDto.setInquiryName(sysOptionDefinitionApiService.getOptionDefinitionById(businessChanceDto.getInquiry()).getTitle());
            preSalesInfoDto.setEntrancesName(sysOptionDefinitionApiService.getOptionDefinitionById(businessChanceDto.getEntrances()).getTitle());
            preSalesInfoDto.setFunctionsName(sysOptionDefinitionApiService.getOptionDefinitionById(businessChanceDto.getFunctions()).getTitle());


            PreSalesInfoType preSalesInfoType = new PreSalesInfoType();
            preSalesInfoType.setDataId(dataId);
            preSalesInfoType.setPreSaleDataEnum(PreSalesDataEnum.BUSSINESS_CHANCE);
            preSalesInfoDto.setPreSalesInfoType(preSalesInfoType);

            preSalesInfoDto.setQuoteorderDto(businessChanceDto.getQuoteorderDto());
            preSalesInfoDto.setSaleorderInfoDto(businessChanceDto.getSaleorderInfoDto());
            return R.success(preSalesInfoDto);
        }
        return R.error("请输入合适的类型");
    }






}
