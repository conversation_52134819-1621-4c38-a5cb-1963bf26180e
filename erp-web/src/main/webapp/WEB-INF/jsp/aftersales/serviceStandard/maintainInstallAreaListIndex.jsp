<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="维护安装区域" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/dialogSelectAndEdit.css?rnd=${resourceVersionKey}">
<div class="main-container">
    <div class="list-pages-search">
        <form method="post" id="search" action="<%=basePath%>aftersale/serviceStandard/maintainInstallArea.do">
            <span style="margin-left: 42px;">订货号:${skuGenerate.skuNo} 商品名称:${skuGenerate.showName}</span>
            </br></br>
            <ul>
                <input type="hidden" name="skuNo" value="${queryDto.skuNo}">
                <li>
                    <label class="infor_name">商品名称</label>
                    <input type="text" style="width:200px" class="input-small" placeholder="请输入订货号/商品名称/制造商型号" name="keyWord" id="keyWord" value="${queryDto.keyWord}"/>

                    <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">查询</span>
                    <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
                </li>

            </ul>
        </form>
    </div>
    <div class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
                <tr>
                    <th class="wid6">订货号</th>
                    <th class="wid6">商品名称</th>
                    <th class="wid6">制造商型号</th>
                    <th class="wid6">审核通过时间</th>
                    <th class="wid6">可安装区域</th>
                    <th class="wid6">操作</th>
                </tr>
            </thead>
            <tbody>
            <c:forEach var="maintainInstallArea" items="${maintainInstallAreaList}"  varStatus="num">
                <tr>
                    <td>${maintainInstallArea.skuNo}</td>
                    <td>
                        ${maintainInstallArea.showName}
                    </td>
                    <td>${maintainInstallArea.spec}</td>
                    <td>
                        ${maintainInstallArea.latestVerifyPassTime}
                    </td>
                    <td>
                        <%--<div class="title-click pop-new-data" style='float:left;margin:-4px 0 0 10px;' layerParams='{"width":"500px","height":"400px","title":"查看安装区域","link":"${pageContext.request.contextPath}/aftersale/serviceStandard/queryInstallAreaList.do?skuNo=${maintainInstallArea.skuNo}"}'>
                            查看
                        </div>--%>
                        <div class="title-click J-area-select" style='float:none;margin:-4px 0 0 10px;'>
                            查看
                        </div>
                        <input type="hidden" class="J-area-value" value='${maintainInstallArea.provinceCityJsonvalue}'>
                    </td>
                    <td><a href="#" onclick='selectImport(${maintainInstallArea.provinceCityJsonvalue})'>选择带入</a></td>
                </tr>
            </c:forEach>
                <c:if test="${empty maintainInstallAreaList}">
                    <tr>
                        <td colspan='6'>查询无结果！请尝试使用其它搜索条件。</td>
                    </tr>
                </c:if>
            </tbody>
        </table>
    </div>
    <tags:page page="${page}" />
</div>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/dialogSelectAndEdit.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    $(function () {
        $('.J-area-select').each(function(){
            new SelectEdit({
                button: $(this),
                url: page_url + '/system/region/getregion.do?isBring=1',
                input: $(this).siblings('.J-area-value'),
                onlyShow: true
            });
        })
    });
    
    function selectImport(provinceCityJsonvalue) {
        window.parent.selected(provinceCityJsonvalue);
        window.parent.layer.closeAll();
    }
</script>
<%@ include file="../../common/footer.jsp"%>
