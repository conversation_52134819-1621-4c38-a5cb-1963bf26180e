package com.vedeng.erp.kingdee.task.batch.mix;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.*;
import com.vedeng.erp.kingdee.task.batch.TaskBatchHandle;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: BuyorderBatchTask
 * @date 2022/5/16 10:57
 */
@JobHandler(value = "BuyorderBatchAllTask")
@Component
public class BuyorderBatchAllTask extends AbstractJobHandler {


    @Autowired
    private BuyOrderBatchJob batchJob;

    @Autowired
    private BuyOrderAfterSaleBatchJob buyOrderAfterSaleBatchJob;

    @Autowired
    private BuyOrderAfterSaleOnlyInvoiceBatchJob buyOrderAfterSaleOnlyInvoiceBatchJob;

    @Autowired
    private GenerateDirectPurchaseOutAndInBatchJob generateDirectPurchaseOutAndInBatchJob;

    @Autowired
    private InvoiceRollbackBatchJob invoiceRollbackBatchJob;


    @Autowired
    private JobLauncher jobLauncher;


    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("=====BlueValidInvoiceRollbackTask start param:{}====" , param);
        JobParameters jobParametersInvoice = new TaskBatchHandle().buildJobParameters(param);
        Job invoiceJob = invoiceRollbackBatchJob.rollbackBlueValidInvoiceJob();
        jobLauncher.run(invoiceJob, jobParametersInvoice);
        XxlJobLogger.log("=====BlueValidInvoiceRollbackTask end====");

        XxlJobLogger.log("====================采购第一次执行冲销开始==================");
        JobParameters jobParametersRed = new TaskBatchHandle().buildJobParameters(param);
        Job buyOrderAfterSaleFlowJobOne = buyOrderAfterSaleBatchJob.buyOrderAfterOneReversalInvoiceJob();
        jobLauncher.run(buyOrderAfterSaleFlowJobOne, jobParametersRed);
        XxlJobLogger.log("====================采购第一次执行冲销结束==================");

        XxlJobLogger.log("====================生成直发采购出库单和入库单开始==================");
        Job generateDirectPurchaseOutAndInFlowJob = generateDirectPurchaseOutAndInBatchJob.generateDirectPurchaseOutAndInFlowJob();
        JobParameters generateDirectPurchaseOutAndInBatchJobParameters = new TaskBatchHandle().buildJobParameters(param);
        jobLauncher.run(generateDirectPurchaseOutAndInFlowJob, generateDirectPurchaseOutAndInBatchJobParameters);
        XxlJobLogger.log("====================生成直发采购出库单和入库单结束==================");

        XxlJobLogger.log("==================采购订单正向流程batch开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.buyOrderFlowJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================采购订单正向流程batch结束====================");

        XxlJobLogger.log("==================采购订单流程退货batch开始====================");
        Job buyOrderAfterSaleFlowJob = buyOrderAfterSaleBatchJob.buyOrderAfterSaleFlowJob();
        jobLauncher.run(buyOrderAfterSaleFlowJob, jobParameters);
        XxlJobLogger.log("==================采购订单流程仅退票batch开始====================");
        Job buyOrderAfterSaleOnlyInvoiceFlowJob = buyOrderAfterSaleOnlyInvoiceBatchJob.buyOrderAfterSaleOnlyInvoiceFlowJob();
        jobLauncher.run(buyOrderAfterSaleOnlyInvoiceFlowJob, jobParameters);

        return SUCCESS;
    }
}
