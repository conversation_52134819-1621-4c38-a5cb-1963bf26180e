function cancel() {
    var index = parent.layer.getFrameIndex(window.name); //获取当前窗体索引
    parent.layer.close(index); //执行关闭;
}

function save() {
    var earlyWarningTaskId = $('#earlyWarningTaskId').val();
    var followUpComment = $('#followUpComment').val();
    $.ajax(
        {
            type: "POST",
            url:  "/flash/earlyWarningGoods/saveFollowInfoDetail.do",
            data:
                {
                    earlyWarningTaskId: earlyWarningTaskId,
                    followUpComment: followUpComment
                },
            dataType: 'json',
            success:function (result) {
                if(result.code == 0){
                    layer.msg(result.message, {
                        icon: 1,
                        time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    }, function () {
                        cancel();
                        parent.location.reload();
                    });
                } else {
                    layer.msg(result.message, {
                        icon: 2,
                        time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    }, function () {
                        cancel();
                        parent.location.reload();
                    });
                }
            },
            error:function () {
                layer.msg('操纵失败', {
                    icon: 1,
                    time: 1000 //2秒关闭（如果不配置，默认是3秒）
                }, function(){
                    cancel();
                    parent.location.reload();
                });
            }
        })

}