package com.test.kingdee;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/8/25 17:22
 */
@Data
public class KingDeeOrg {


    /**
     * 供应商内码
     */
    private String FSupplierId;
    /**
     * 创建组织
     */
    private KingDeeNumber FCreateOrgId = new KingDeeNumber();
    /**
     * 供应商编号
     */
    private String FNumber;
    /**
     * 使用组织
     */
    private KingDeeNumber FUseOrgId = new KingDeeNumber();
    /**
     * 供应商名称
     */
    private String FName;
    /**
     * 供应类别
     */
    private FBaseInfo FBaseInfo = new FBaseInfo();


    @Data
    public static class FBaseInfo {
        /**
         * 供应类别
         */
        private String FSupplyClassify = "CG";
    }
}
