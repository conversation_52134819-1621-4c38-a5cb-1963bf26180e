<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.vedeng.erp</groupId>
        <artifactId>erp-trader</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>erp-trader-api</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>erp-trader-api</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-kingdee-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-system-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>third</id>
            <name>Team Vedeng Release Repository</name>
            <url>http://nexus.ivedeng.com/repository/third/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.ivedeng.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


</project>
