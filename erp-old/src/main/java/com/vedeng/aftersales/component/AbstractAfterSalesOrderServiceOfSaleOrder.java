package com.vedeng.aftersales.component;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.component.dto.*;
import com.vedeng.aftersales.component.exception.*;
import com.vedeng.aftersales.constant.ServiceChangesGoodIdEnum;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesInvoiceMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.AfterSalesInvoice;
import com.vedeng.aftersales.model.vo.AfterSalesDetailVo;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.trader.dao.TraderContactGenerateMapper;
import com.vedeng.trader.model.TraderContactGenerate;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.wms.service.context.ThreadLocalContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.math.BigDecimal;

/**
 * 抽象类
 */
@Service
public abstract class AbstractAfterSalesOrderServiceOfSaleOrder extends AbstractAfterSalesOrderService implements AfterSalesOrderService{

    public static Logger logger = LoggerFactory.getLogger(AbstractAfterSalesOrderServiceOfSaleOrder.class);

    private List<String> pictureSuffix = new ArrayList(Arrays.asList("jpg","png","gif","bmp"));

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesDetailMapper afterSalesDetailMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private TraderContactGenerateMapper traderContactGenerateMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Resource
    protected AfterSalesService afterSalesOrderService;

    @Resource
    private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;
    @Resource
    private OrderCommonService orderCommonService;
    @Resource
    private OrderNoDict orderNoDict;

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;
    
    /**
     * 锁定销售单的商品
     * @param afterSaleOrderAddDto
     */
    protected void lockSaleOrderSkuOnCreate(AfterSaleOrderAddDto afterSaleOrderAddDto) throws LockSaleOrderSkuOnCreateException {
        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(afterSaleOrderAddDto.getSaleOrderNo());
        //锁定销售
        saleorder.setServiceStatus(1);//售后中
        saleorder.setLockedStatus(1);
        saleorder.setLockedReason("售后锁定");//锁定原因
        saleorderMapper.updateByPrimaryKeySelective(saleorder);

        //更新锁定订单预警状态
        updateLockSaleorderWarning(saleorder.getSaleorderId());

        lockSaleorderGoods(afterSaleOrderAddDto.getGoodsList(),saleorder);
    }

    /**
     * @Description 将goodsList中的saleorderGood锁定
     * <AUTHOR>
     * @Date 15:42 2021/7/6
     * @Param [goodsList, saleorder]
     * @return void
     **/
    private void lockSaleorderGoods(List<AfterSaleOrderGoodAddDto> goodsList,Saleorder saleorder) {
        if(CollectionUtils.isNotEmpty(goodsList)){
            List<AfterSalesGoodsVo> list = new ArrayList<>();
            goodsList.stream().filter(goods-> null != goods.getSkuNo() && !("".equals(goods.getSkuNo()))).forEach(goods->{
                    AfterSalesGoodsVo afterSalesGoodsVo = new AfterSalesGoodsVo();
                    afterSalesGoodsVo.setSku(goods.getSkuNo());
                    afterSalesGoodsVo.setSaleorderId(saleorder.getSaleorderId());
                    list.add(afterSalesGoodsVo);
                }
            );
            saleorderGoodsMapper.updateGoodsLockStatusBySku(list);
        }
    }

    /**
     * 新增附件
     * @param addDto
     * @param afterSaleOrder
     */
    @Override
    protected void addAfterSalerOrderFile(Object addDto, AfterSales afterSaleOrder) {

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;
        logger.info("开始处理新增售后单附件，参数addDto：{}，售后单afterSaleOrder：{}", JSON.toJSONString(afterSaleOrderAddDto),JSON.toJSONString(afterSaleOrder));
        List<AttachmentDto> attachments = afterSaleOrderAddDto.getAttachments();
        if(CollectionUtils.isEmpty(attachments)){
            return;
        }
        dealWithAttachmentList(afterSaleOrder, attachments, afterSaleOrderAddDto.getAfterSalesType());
        logger.info("处理新增售后单附件结束");
    }

    private void dealWithAttachmentList(AfterSales afterSaleOrder, List<AttachmentDto> attachments, Integer afterSalesType) {

        for (AttachmentDto attachmentDto : attachments) {
            Attachment attachment = new Attachment();
            if (attachmentDto.getUrl() == null) {
                continue;
            }
            if (attachmentDto.getAttachmentName() != null && attachmentDto.getAttachmentName().length() > 128) {
                attachment.setName(attachmentDto.getAttachmentName().substring(0, 128));
            } else {
                attachment.setName(attachmentDto.getAttachmentName());
            }
            if (attachmentDto.getUrl().toLowerCase().startsWith("http")) {
                String[] split = attachmentDto.getUrl().split("/");
                int endIndex;
                if (split.length > 2) {
                    endIndex = split[0].length() + split[1].length() + split[2].length() + 2;
                } else {
                   endIndex = split[0].length() ;
                }
                attachment.setDomain(attachmentDto.getUrl().substring(0, endIndex));
                attachment.setUri(attachmentDto.getUrl().substring(endIndex));
            } else {
                attachment.setUri(attachmentDto.getUrl());
            }
            Optional<String> isPicture = pictureSuffix.stream().filter(e -> attachmentDto.getUrl().toLowerCase().contains(e)).findAny();
            if (isPicture.isPresent()) {
                attachment.setAttachmentType(460);
            } else {
                attachment.setAttachmentType(461);
            }

            attachment.setAttachmentFunction(afterSalesType);
            attachment.setRelatedId(afterSaleOrder.getAfterSalesId());
            attachment.setCreator(afterSaleOrder.getCreator());
            attachment.setAddTime(afterSaleOrder.getAddTime());
            attachment.setIsDeleted(0);
            attachmentMapper.insertSelective(attachment);
        }
    }

    @Override
    //自定义的特殊处理
    protected void customeAddDeal(Object adddDto) throws Exception{

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)adddDto;

        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");

        // 处理售后单(退货、换货、退款、退票需要处理销售单)
        orderCommonService.updateAfterOrderDataUpdateTime(afterSaleOrder.getAfterSalesId(),null, OrderDataUpdateConstant.AFTER_ORDER_GENERATE);

        customeAddDealOfSaleOrder(adddDto);

    }

    protected abstract void customeAddDealOfSaleOrder(Object adddDto) throws Exception;


    /**
     * 新增售后详情
     * @param addDto 参数
     */
    @Override
    protected void addAfterSaleDetail(Object addDto) throws AfterSaleDetailException {

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;
        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");
        if (null == afterSaleOrder || afterSaleOrder.getAfterSalesId() == null) {
            throw new AfterSaleDetailException("获取的售后单不存在！");
        }
        AfterSalesDetail afterSalesDetail = new AfterSalesDetail();
        afterSalesDetail.setRefundAmountStatus(0); // 退款状态 0无退款 1未退款 2部分退款 3已退款

        commonAfterSaleDetail(afterSalesDetail, afterSaleOrderAddDto);

        afterSalesDetail.setTraderMode(0);//交易方式（字典表查）
        afterSalesDetail.setRefund(0); // 退款方式
        afterSalesDetailMapper.insertSelective(afterSalesDetail);
        try {
            // 开始添加埋点信息
            Map<String, Object> trackParams = new HashMap<>();
            // 客户id
            trackParams.put("traderId", afterSalesDetail.getTraderId());
            // 订单号
            trackParams.put("orderNo", afterSaleOrder.getOrderNo());
            // 售后类型
            SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(afterSaleOrder.getType());
            trackParams.put("afterSaleTypeName", Objects.nonNull(sysOptionDefinition) ? sysOptionDefinition.getTitle() : "");
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.AFTER_SALE_ORDER_CREATE_FRONT);
            TrackParamsData trackParamsData = new TrackParamsData();
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackTime(new Date());
            trackParamsData.setTrackResult(ResultInfo.success());
            trackParamsData.setEventTrackingEnum(EventTrackingEnum.AFTER_SALE_ORDER_CREATE_FRONT);
            trackStrategy.track(trackParamsData);
        } catch (Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.AFTER_SALE_ORDER_CREATE_FRONT.getArchivedName(),e);
        }
    }

    protected abstract <T extends AbstractAfterSaleOrderDto> void customerAddAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderDto) throws AfterSaleDetailException;

    /**
     * 新增售后单商品
     * @param addDto
     */
    @Override
    protected List<AfterSalesGoods> addAfterSaleGood(Object addDto) throws Exception{

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;

        //新增售后单商品
        if(CollectionUtils.isEmpty(afterSaleOrderAddDto.getGoodsList())){
            return Arrays.asList();
        }

        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");

        List<AfterSalesGoods> afterSalesGoodsList = commonAddAfterSaleOrderGoods(afterSaleOrderAddDto.getGoodsList(),afterSaleOrder);

        //自定义的一些处理
        customerAddAfterSaleGood(afterSaleOrderAddDto);

        return afterSalesGoodsList;
    }

    private <T extends AbstractAfterSaleOrderGoodDto> List<AfterSalesGoods> commonAddAfterSaleOrderGoods(List<T> goodList,AfterSales afterSaleOrder) {

        List<AfterSalesGoods> afterSalesGoodsList = new ArrayList<>();

        Map<Integer, Integer> skuNumMap = new HashMap<>();

        Integer afterSalesId = afterSaleOrder.getAfterSalesId();

        List<Integer> saleOrderGoodIdList = new ArrayList<>();

        // 查询售后订单商品单价与小计
        List<AfterSalesGoods> orderGoodsPriceList = afterSalesMapper.getOrderGoodsPriceByAfterIdDB(535, afterSalesId);

        goodList.forEach(afterSaleGoodDto -> {

            Integer isGift = StringUtils.isNotBlank(afterSaleGoodDto.getGiftFlag()) && Constants.IS_GIFT.equals(afterSaleGoodDto.getGiftFlag()) ? 1 : 0;

            SaleorderGoods saleorderGood = saleorderGoodsMapper.getSaleorderGoodsBySaleorderIdAndSkuNoAndIsGift(afterSaleOrder.getOrderId(), afterSaleGoodDto.getSkuNo(), isGift);

            //SaleorderGoods saleorderGood = saleorderGoodsMapper.getSaleorderGoodsBySaleorderIdAndSkuNo(afterSaleOrder.getOrderId(),afterSaleGoodDto.getSkuNo());

            AfterSalesGoods afterSalesGoods = new AfterSalesGoods();
            afterSalesGoods.setAfterSalesId(afterSalesId);
            afterSalesGoods.setNum(afterSaleGoodDto.getNum());
            afterSalesGoods.setOrderDetailId(saleorderGood.getSaleorderGoodsId());
            afterSalesGoods.setGoodsId(Integer.valueOf(afterSaleGoodDto.getSkuNo().substring(1)));

            //订单流升级--设置实际应退/应换数量
            SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(saleorderGood.getSaleorderGoodsId());
            afterSalesOrderService.setRealRknum(afterSaleOrder.getType(),saleorderGoods.getSaleorderGoodsId(),afterSalesGoods);
            BigDecimal price = orderGoodsPriceList.stream().filter(orderGoodsPrice -> saleorderGood.getSaleorderGoodsId().equals(orderGoodsPrice.getGoodsId()))
                    .findFirst()
                    .map(e -> e.getPrice())
                    .orElseGet(null);
            if (price != null) {
                afterSalesGoods.setPrice(price);
                afterSalesGoods.setSkuRefundAmount(price.multiply(new BigDecimal(afterSaleGoodDto.getNum())));
                afterSalesGoods.setSkuOldRefundAmount(price.multiply(new BigDecimal(afterSaleGoodDto.getNum())));
            }

            if(null == skuNumMap.get(saleorderGood.getSaleorderGoodsId())) {
                skuNumMap.put(saleorderGood.getSaleorderGoodsId(), afterSaleGoodDto.getNum());
            } else {
                // 累加
                skuNumMap.put(saleorderGood.getSaleorderGoodsId(), afterSaleGoodDto.getNum() + skuNumMap.get(saleorderGood.getSaleorderGoodsId()));
            }

            afterSalesGoodsMapper.insertSelective(afterSalesGoods);
            afterSalesGoodsList.add(afterSalesGoods);
            saleOrderGoodIdList.add(saleorderGood.getSaleorderGoodsId());
        });

        ThreadLocalContext.put("saleOrderGoodIdList",saleOrderGoodIdList);
        ThreadLocalContext.put("skuNumMap",skuNumMap);

        return afterSalesGoodsList;
    }

    private int getHistoryArrivalNum(Integer saleOrderGoodsId) {
        return afterSalesGoodsMapper.getSaleorderAfterSaleArrivalGoods(saleOrderGoodsId);
    }

    private int getHistoryReturnNum(Integer saleorderGoodsId) {
        return   afterSalesGoodsMapper.getSaleorderAftersaleReturnGoods(saleorderGoodsId);
    }
    /**
     * 自定义新增售后单商品
     * @param afterSaleOrderAddDto
     */
    protected abstract void customerAddAfterSaleGood(AfterSaleOrderAddDto afterSaleOrderAddDto) throws Exception;

    @Override
    protected AfterSales addAfterSalerOrder(Object addDto) {

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;

        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(afterSaleOrderAddDto.getSaleOrderNo());

        AfterSales afterSales = new AfterSales();
        afterSales.setOrderId(saleorder.getSaleorderId());
        afterSales.setOrderNo(afterSaleOrderAddDto.getSaleOrderNo());
        afterSales.setAddTime(afterSaleOrderAddDto.getOperateTime());
        afterSales.setCreator(2);//系统自动生成
        afterSales.setModTime(afterSaleOrderAddDto.getOperateTime());

        //查询归属销售
        User belongToSaleUser = userMapper.getUserByTraderId(saleorder.getTraderId(), ErpConst.ONE);
        afterSales.setServiceUserId(afterSalesOrderService.getAfterSalesServiceUser(belongToSaleUser));

        afterSales.setStatus(0);
        //销售类型
        afterSales.setSubjectType(535);

        //更改默认安调类型
//        if (AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(afterSaleOrderAddDto.getAfterSalesType())){
//            afterSales.setType(AfterSalesProcessEnum.AFTERSALES_ATN.getCode());
//        }else {
//            afterSales.setType(afterSaleOrderAddDto.getAfterSalesType());
//        }
        afterSales.setType(afterSaleOrderAddDto.getAfterSalesType());
        afterSales.setUpdater(2);

        afterSales.setValidStatus(0);
        //售后单公司写死为南京贝登
        afterSales.setCompanyId(1);
        //待确认
        afterSales.setAtferSalesStatus(0);
        //售后来源贝登前台
        afterSales.setSource(2);

        //创建售后单的前台用户名称
        afterSales.setCreateFrontEndUser(afterSaleOrderAddDto.getLoginUsername());

        //订单流新订单标识
        afterSales.setIsNew(ErpConst.ONE);

        customerPropertySet(afterSaleOrderAddDto,afterSales);
        this.afterSalesMapper.insertSelective(afterSales);

//        afterSales.setAfterSalesNo(orderNoDict.getOrderNum(afterSales.getAfterSalesId(), 6));
        afterSales.setAfterSalesNo(afterSaleOrderAddDto.getAfterSalesNo());
        afterSalesMapper.updateByPrimaryKeySelective(afterSales);

        return afterSales;
    }

    //所有售后单通用的校验逻辑都在这里
    @Override
    protected void commonAfterSaleOrderCreateValidate(Object addDto) throws AfterSaleValidateException{

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;

        if(afterSaleOrderAddDto == null){
            throw new AfterSaleValidateException("参数为空");
        }
        if(afterSaleOrderAddDto.getSaleOrderNo() == null){
            throw new AfterSaleValidateException("销售单号为空");
        }
        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(afterSaleOrderAddDto.getSaleOrderNo());
        if(saleorder == null){
            throw new AfterSaleValidateException("销售单号对应的销售单不存在！");
        }
        if(0 == saleorder.getStatus() || 3 == saleorder.getStatus()){
            throw new AfterSaleValidateException("订单状态为待确认或已关闭，不允许创建售后单！");
        }

        if(CollectionUtils.isNotEmpty(afterSaleOrderAddDto.getGoodsList())){
            for(AfterSaleOrderGoodAddDto baseAfterSalesGoodsInput : afterSaleOrderAddDto.getGoodsList()){
                SaleorderGoods saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsBySaleorderIdAndSkuNo(saleorder.getSaleorderId(),baseAfterSalesGoodsInput.getSkuNo());
                if(saleorderGoods == null){
                    throw new AfterSaleValidateException("所选择的售后商品在该销售单中不存在！");
                }else if(ErpConst.ONE.equals(saleorderGoods.getLockedStatus())){
                    throw new AfterSaleValidateException("所选择的售后商品"+saleorderGoods.getSku()+"已经被售后锁定！");
                }
            }
        }

        if(StringUtil.isEmpty(afterSaleOrderAddDto.getAfterSalesComment())||afterSaleOrderAddDto.getAfterSalesComment().length()> 512){
            throw new AfterSaleValidateException("详细说明为必填且不大于512个字符！");
        }
        if(StringUtil.isEmpty(afterSaleOrderAddDto.getLoginUsername())){
            throw new AfterSaleValidateException("联系人必填！");
        }
        if(StringUtil.isEmpty(afterSaleOrderAddDto.getLoginMobile()) || !StringUtil.matchPhone(afterSaleOrderAddDto.getLoginMobile())){
            throw new AfterSaleValidateException("联系人电话未填或不符合电话号码规范！");
        }
        // 判断售后单号是否已创建
        AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(afterSaleOrderAddDto.getAfterSalesNo());
        if(Objects.nonNull(afterSalesByNo)){
            throw new AfterSaleValidateException("该售后单号已存在，请重新填写！");
        }
    }

    //自定义属性设置
    protected abstract void customerPropertySet(AfterSaleOrderAddDto afterSaleOrderAddDto,AfterSales afterSales);

    /**
     * 新增发票
     * @param addDto
     */
    public void addTicket(Object addDto) throws AfterSaleValidateException{

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;

        // 获取售后单号
        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");
        if(afterSaleOrder == null || afterSaleOrder.getAfterSalesId() == null){
            throw new AfterSaleValidateException("未抓取到已生成的售后单信息！");
        }

        // 添加售后退票表信息----开始
        List<InvoiceAddDto> invoiceList = afterSaleOrderAddDto.getInvoiceList();
        if(CollectionUtils.isEmpty(invoiceList)){
            throw new AfterSaleValidateException("未接收到售后发票信息！");
        }
        for(int i = 0;i < invoiceList.size();i++){
            InvoiceAddDto item = invoiceList.get(i);
            if(item != null && !ObjectUtils.isEmpty(item.getInvoiceId())){
                AfterSalesInvoice afterSalesInvoice = new AfterSalesInvoice();
                afterSalesInvoice.setInvoiceId(item.getInvoiceId());
                afterSalesInvoice.setAfterSalesId(afterSaleOrder.getAfterSalesId());
                afterSalesInvoice.setIsRefundInvoice(1);
                int res4 = afterSalesInvoiceMapper.insertSelective(afterSalesInvoice);
                if(res4 == 0){
                    throw new AfterSaleValidateException("保存售后退票表信息失败！");
                }
            }
        }
        // 添加售后退票表信息----结束

    }

    protected void lockSaleOrderSkuOnModify(Object modifyDto) {
        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;
        AfterSales afterSales = ThreadLocalContext.get("afterSaleOrder");
        Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderId(afterSales.getOrderId());
        lockSaleorderGoods(afterSaleOrderModifyDto.getGoodsList(),saleorder);
    }

    @Override
    protected void customeModifyDeal(Object modifyDto) throws AfterSaleValidateException {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;

        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");

        orderCommonService.updateAfterOrderDataUpdateTime(afterSaleOrder.getAfterSalesId(),null,OrderDataUpdateConstant.AFTER_ORDER_CLOSE);

        customeModifyDealOfSaleOrder(modifyDto);
    }

    protected void customeModifyDealOfSaleOrder(Object modifyDto) throws AfterSaleValidateException {};

    @Override
    protected void modifyAfterSalerOrderFile(Object modifyDto) {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;
        logger.info("开始处理修改售后单附件，modifyDto：{}", JSON.toJSONString(afterSaleOrderModifyDto));
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSaleOrderModifyDto.getAfterSalesNo());
        List<AttachmentDto> attachments = afterSaleOrderModifyDto.getAttachments();
        Attachment delAttachement = new Attachment();
        delAttachement.setAttachmentFunction(afterSales.getType());
        delAttachement.setRelatedId(afterSales.getAfterSalesId());
        attachmentMapper.updateByRelation(delAttachement);
        if(CollectionUtils.isEmpty(attachments)){
            return;
        }
        dealWithAttachmentList(afterSales, attachments, afterSales.getType());
        logger.info("修改售后单附件结束");
    };

    @Override
    protected void modifyAfterSaleDetail(Object modifyDto) throws AfterSaleDetailException {
        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;
        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");
        if (null == afterSaleOrder || afterSaleOrder.getAfterSalesId() == null) {
            throw new AfterSaleDetailException("获取的售后单不存在！");
        }
        AfterSalesDetail afterSalesDetail = afterSalesDetailMapper.selectadtbyid(afterSaleOrder);
        commonAfterSaleDetail(afterSalesDetail, afterSaleOrderModifyDto);
        afterSalesDetailMapper.updateByPrimaryKeySelective(afterSalesDetail);
    }

    protected <T extends AbstractAfterSaleOrderDto> AfterSalesDetail commonAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderDto) throws AfterSaleDetailException {
        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");
        BigDecimal refundAmount = BigDecimal.ZERO;
        afterSalesDetail.setRefundAmount(refundAmount);
        afterSalesDetail.setAfterSalesId(afterSaleOrder.getAfterSalesId());
        afterSalesDetail.setComments(afterSaleOrderDto.getAfterSalesComment()); // 详细描述
        afterSalesDetail.setReason(afterSaleOrderDto.getAfterSalesReason());

        Saleorder saleorder = saleorderMapper.getSaleOrderById(afterSaleOrder.getOrderId());
        ThreadLocalContext.put("saleorder", saleorder);

        afterSalesDetail.setTraderId(saleorder.getTraderId());
        TraderContactGenerate traderContact = vailTraderContact(saleorder.getTraderId(), afterSaleOrderDto.getContactUsername(), afterSaleOrderDto.getContactMobile());
        if (traderContact != null) {
            afterSalesDetail.setTraderContactId(traderContact.getTraderContactId());
//            afterSalesDetail.setTraderContactMobile(traderContact.getMobile());
//            afterSalesDetail.setTraderContactName(traderContact.getName());
//            afterSalesDetail.setTraderContactTelephone(traderContact.getTelephone());
            afterSalesDetail.setTraderId(traderContact.getTraderId());

            afterSalesDetail.setAfterConnectUserName(traderContact.getName());
            afterSalesDetail.setAfterConnectPhone(traderContact.getMobile());
        } else {
            afterSalesDetail.setAfterConnectUserName(afterSaleOrderDto.getContactUsername());
            afterSalesDetail.setAfterConnectPhone(afterSaleOrderDto.getContactMobile());
        }
        if (StringUtil.isNotEmpty(afterSaleOrderDto.getContactProvince())) {
            if (StringUtil.isNotEmpty(afterSaleOrderDto.getContactArea())) {
                afterSalesDetail.setAreaId(Integer.valueOf(afterSaleOrderDto.getContactArea()));
            } else if (StringUtil.isNotEmpty(afterSaleOrderDto.getContactCity())) {
                afterSalesDetail.setAreaId(Integer.valueOf(afterSaleOrderDto.getContactCity()));
            } else {
                afterSalesDetail.setAreaId(Integer.valueOf(afterSaleOrderDto.getContactProvince()));
            }
            afterSalesDetail.setArea(afterSalesOrderService.getAddressByAreaId(afterSalesDetail.getAreaId()));
            afterSalesDetail.setAddress(afterSaleOrderDto.getContactAddress());
        }

        afterSalesDetail.setPayee("");
        customerAddAfterSaleDetail(afterSalesDetail, afterSaleOrderDto);
        return afterSalesDetail;
    }

    @Override
    protected void modifyAfterSalerOrder(Object modifyDto) {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;

        AfterSales afterSalesOrder = this.afterSalesMapper.getAfterSalesByNo(afterSaleOrderModifyDto.getAfterSalesNo());

        ThreadLocalContext.put("afterSaleOrder",afterSalesOrder);

        AfterSales afterSales = new AfterSales();
        afterSales.setAfterSalesId(afterSalesOrder.getAfterSalesId());
        afterSales.setModTime(afterSaleOrderModifyDto.getOperateTime());
        afterSales.setStatus(0);
        afterSales.setUpdater(2);
        afterSales.setCompanyId(1);
        afterSalesMapper.updateByPrimaryKeySelective(afterSales);

        List<AfterSalesGoods> afterSalesGoodList = afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesId(afterSalesOrder.getAfterSalesId());
        if(CollectionUtils.isNotEmpty(afterSalesGoodList)){
            //解除商品锁定状态
            List<Integer> saleorderGoodsIds = afterSalesGoodsMapper.getSaleorderGoodsIdByAftersaleId(afterSalesOrder.getAfterSalesId());
            saleorderGoodsMapper.updateAfterGoodsLockStuts(saleorderGoodsIds);
            // 删除售后商品
            afterSalesGoodsMapper.delAfterSalesGoodsByParam(afterSalesOrder.getAfterSalesId());
        }

    };

    @Override
    protected void modifyAfterSaleGood(Object modifyDto) {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;

        //新增售后单商品
        if(CollectionUtils.isEmpty(afterSaleOrderModifyDto.getGoodsList())){
            return;
        }

        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");

        List<AfterSalesGoods> afterSalesGoodsList = commonAddAfterSaleOrderGoods(afterSaleOrderModifyDto.getGoodsList(),afterSaleOrder);

        ThreadLocalContext.put("afterSalesGoodsList", afterSalesGoodsList);

    };

    protected void customerAfterSaleOrderModifyValidate(AfterSaleOrderModifyDto afterSaleOrderModifyDto) throws Exception {};

    @Override
    protected void commonAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException{

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;

        if(afterSaleOrderModifyDto == null){
            throw new AfterSaleValidateException("参数为空");
        }

        // 售后单状态校验
        if(afterSaleOrderModifyDto.getAfterSalesNo() == null){
            throw new AfterSaleValidateException("售后单号为空");
        }

        AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(afterSaleOrderModifyDto.getAfterSalesNo());
        if(afterSalesByNo == null){
            throw new AfterSaleValidateException("售后单【"+afterSaleOrderModifyDto.getAfterSalesNo()+"】信息不存在");
        }

        if(Integer.valueOf(1).equals(afterSalesByNo.getStatus()) || Integer.valueOf(2).equals(afterSalesByNo.getStatus())){
            throw new AfterSaleValidateException("售后订单审核状态为[待确认]或[审核不通过]才能修改");
        }

        if(StringUtil.isEmpty(afterSaleOrderModifyDto.getAfterSalesComment()) || afterSaleOrderModifyDto.getAfterSalesComment().length() > 512){
            throw new AfterSaleValidateException("详细说明为必填且不大于512个字符！");
        }
        if(StringUtil.isEmpty(afterSaleOrderModifyDto.getLoginUsername())){
            throw new AfterSaleValidateException("联系人必填！");
        }
        if(StringUtil.isEmpty(afterSaleOrderModifyDto.getLoginMobile()) || !StringUtil.matchPhone(afterSaleOrderModifyDto.getLoginMobile())){
            throw new AfterSaleValidateException("联系人电话未填或不符合电话号码规范！");
        }
    }


    /**
     * 修改发票
     * @param modifyDto
     */
    public void modifyTicket(Object modifyDto) throws AfterSaleValidateException {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;

        // 获取售后单号
        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");
        if(afterSaleOrder == null || afterSaleOrder.getAfterSalesId() == null){
            throw new AfterSaleValidateException("未抓取到已生成的售后单信息！");
        }

        // 修改售后退票表信息----开始
        List<InvoiceAddDto> invoiceList = afterSaleOrderModifyDto.getInvoiceList();
        if(CollectionUtils.isEmpty(invoiceList)) {
            throw new AfterSaleValidateException("未接收到售后发票信息！");
        }

        // 先删除售后订单之前关联的发票信息
        afterSalesInvoiceMapper.delAfterSalesInvoiceByAfterSalesId(afterSaleOrder.getAfterSalesId());

        // 重新添加售后关联发票信息
        for(int i = 0;i < invoiceList.size();i++){
            InvoiceAddDto item = invoiceList.get(i);
            if(item != null && !ObjectUtils.isEmpty(item.getInvoiceId())){
                AfterSalesInvoice afterSalesInvoice = new AfterSalesInvoice();
                afterSalesInvoice.setInvoiceId(item.getInvoiceId());
                afterSalesInvoice.setAfterSalesId(afterSaleOrder.getAfterSalesId());
                afterSalesInvoice.setIsRefundInvoice(1);
                int res4 = afterSalesInvoiceMapper.insertSelective(afterSalesInvoice);
                if(res4 == 0){
                    throw new AfterSaleValidateException("保存售后退票表信息失败！");
                }
            }
        }
        // 修改售后退票表信息----结束
    }

    /**
     * @Description 更新锁定订单预警状态
     * <AUTHOR>
     * @Date 14:09 2021/7/6
     * @Param [saleorderId]
     * @return void
     **/
    public void updateLockSaleorderWarning(Integer saleorderId) throws LockSaleOrderSkuOnCreateException {
        try{
            List<SaleorderGoods> saleorderGoodsLists = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);
            if(CollectionUtils.isNotEmpty(saleorderGoodsLists)){
                for (SaleorderGoods sg : saleorderGoodsLists) {
                    SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
                    saleorderGoodsVo.setSaleorderGoodsId(sg.getSaleorderGoodsId());
                    saleorderGoodsVo.setWarnLevel(null);
                    saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
                }
            }
        }catch(Exception e){
            logger.error("锁定销售单时，清除预警失败，销售单ID：{},错误原因：{}",saleorderId,e);
            throw new LockSaleOrderSkuOnCreateException("锁定更新销售单预警失败");
        }
    }

    /**
     * @Description 判断交易者联系人是否存在，不存在的话新建一个
     * <AUTHOR>
     * @Date 11:08 2021/7/6
     * @Param [traderId, name, mobile]
     * @return com.vedeng.trader.model.TraderContactGenerate
     **/
    protected TraderContactGenerate vailTraderContact(Integer traderId, String name, String mobile){
        TraderContactGenerate contactInfo = traderContactGenerateMapper.getContactInfo(traderId, CommonConstants.TRADER_TYPE_1, name, null, mobile);
        if (contactInfo == null || contactInfo.getTraderContactId() == null) {
            contactInfo = new TraderContactGenerate();
            contactInfo.setTraderId(traderId);
            contactInfo.setTraderType(CommonConstants.TRADER_TYPE_1);
            contactInfo.setIsEnable(CommonConstants.ENABLE);
            contactInfo.setName(name);
            contactInfo.setMobile(mobile);
            contactInfo.setAddTime(DateUtil.gainNowDate());
            contactInfo.setModTime(DateUtil.gainNowDate());
            traderContactGenerateMapper.insertSelective(contactInfo);
        }
        return traderContactGenerateMapper.getContactInfo(traderId, CommonConstants.TRADER_TYPE_1, name, null, mobile);
    }

    /**
     * @Description AftersaleorderGood表增加手续费goods
     * <AUTHOR>
     * @Date 9:23 2021/7/7
     * @Param []
     * @return void
     **/
    protected void addServiceChargesAftersaleorderGoodCreate() throws AddAfterSaleGoodCreateException {
        AfterSales afterSaleorder = ThreadLocalContext.get("afterSaleOrder");
        AfterSalesGoods afterSalesGoods = new AfterSalesGoods();
        afterSalesGoods.setAfterSalesId(afterSaleorder.getAfterSalesId());
        afterSalesGoods.setGoodsType(1);//特殊商品
        afterSalesGoods.setNum(1);//手续费数量默认为1
        afterSalesGoods.setGoodsId(ServiceChangesGoodIdEnum.getEnumByTypeCode(afterSaleorder.getType()).getGoodCode());//安调手续费商品id
        if(0 == afterSalesGoodsMapper.insertSelective(afterSalesGoods)){
            throw new AddAfterSaleGoodCreateException("写入售后手续费商品信息失败！");
        }
    }

    public void closeAfterSales(Object closeDto) throws AfterSaleCloseException {
        AfterSaleOrderCloseDto afterSaleOrderCloseDto = (AfterSaleOrderCloseDto)closeDto;
        if(null == afterSaleOrderCloseDto || null==afterSaleOrderCloseDto.getAfterSalesNo()){
            throw new AfterSaleCloseException("参数错误！");
        }
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSaleOrderCloseDto.getAfterSalesNo());
        //售后单通用的关闭逻辑
        commonAfterSaleOrderClose(afterSaleOrderCloseDto);

        //每种售后单自己的关闭逻辑
        customerAfterSaleOrderClose(afterSaleOrderCloseDto);

        //更新售后updatatime
        orderCommonService.updateAfterOrderDataUpdateTime(afterSales.getAfterSalesId(),null,OrderDataUpdateConstant.AFTER_ORDER_CLOSE);

        try {
            AfterSalesVo afterSalesVo = new AfterSalesVo();
            afterSalesVo.setAfterSalesId(afterSales.getAfterSalesId());
            afterSalesVo.setCompanyId(1);//公司写死为南京贝登
            afterSalesVo.setTraderType(1);//售后单为销售售后，交易者类型为客户
            AfterSalesVo afterSalesInfo = afterSalesOrderService.getAfterSalesVoDetail(afterSalesVo);
            if(ErpConst.ZERO.equals(afterSalesInfo.getValidStatus()) && StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSalesInfo.getType())){
                User user = userMapper.getUserByUserId(2);//用户默认为njadmin
                logicalSaleorderChooseServiceImpl.closeAfterPutSaleorder(afterSalesInfo,user);
            }
        } catch (Exception e) {
            logger.error("未生效销售退货单关闭下发WMS error",e);
        }
    }
    
    /**
     * @Description 每种销售单自己的关闭后处理逻辑
     * <AUTHOR>
     * @Date 9:48 2021/7/9
     * @Param [afterSaleOrderCloseDto]
     * @return void
     **/
    protected abstract void customerAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) throws AfterSaleCloseException;

    private void commonAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) throws AfterSaleCloseException {
        if(isCanClose(afterSaleOrderCloseDto)){
            close(afterSaleOrderCloseDto);
        }else{
            throw new AfterSaleCloseException("该售后单不符合关闭条件（审核状态为审核不通过，状态不为已完结和已关闭）不可关闭！");
        }
    }

    /**
     * @Description 关闭售后单
     * <AUTHOR>
     * @Date 17:14 2021/7/7
     * @Param [afterSaleOrderCloseDto]
     * @return void
     **/
    private void close(AfterSaleOrderCloseDto afterSaleOrderCloseDto) throws AfterSaleCloseException {
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSaleOrderCloseDto.getAfterSalesNo());
        AfterSalesVo afterSalesVo = new AfterSalesVo();
        afterSalesVo.setAfterSalesId(afterSales.getAfterSalesId());
        afterSalesVo.setSubjectType(afterSales.getSubjectType());
        afterSalesVo.setType(afterSales.getType());
        afterSalesVo.setAfterSalesNo(afterSales.getAfterSalesNo());
        afterSales.setAtferSalesStatus(3);//关闭
        afterSales.setCompanyId(1);//公司写死为南京贝登
        afterSales.setModTime(afterSaleOrderCloseDto.getOperateTime());
        afterSales.setUpdater(2);//更新人置为njadmin
        afterSales.setAfterSalesStatusUserName(afterSaleOrderCloseDto.getLoginUsername());
        afterSales.setAfterSalesStatusReson(afterSaleOrderCloseDto.getCloseAfterSalesReason());
        afterSales.setAfterSalesStatusUser(2);//关闭人置为njadmin
        afterSales.setAfterSalesStatusComments(afterSaleOrderCloseDto.getCloseAfterSalesComment()+"(前台发起的关闭)");
        //设置关闭人Id
        afterSales.setAfterSalesStatusUser(2);//更新人置为njadmin
        afterSales.setCloseFrontEndMobile(afterSaleOrderCloseDto.getLoginUsername());
        if(0 == afterSalesMapper.updateByPrimaryKeySelective(afterSales)){
            throw new AfterSaleCloseException("修改售后单状态出错！");
        }
        try {
            AfterSalesDetailVo detail = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSales.getAfterSalesId());
            // 开始添加埋点信息
            Map<String, Object> trackParams = new HashMap<>();
            // 客户id
            trackParams.put("traderId", detail.getTraderId());
            // 订单号
            trackParams.put("orderNo", afterSales.getOrderNo());
            // 售后类型
            SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(detail.getAfterType());
            trackParams.put("afterSaleTypeName", Objects.nonNull(sysOptionDefinition) ? sysOptionDefinition.getTitle() : "");
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.AFTER_SALE_ORDER_CLOSE_FRONT);
            TrackParamsData trackParamsData = new TrackParamsData();
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackTime(new Date());
            trackParamsData.setTrackResult(ResultInfo.success());
            trackParamsData.setEventTrackingEnum(EventTrackingEnum.AFTER_SALE_ORDER_CLOSE_FRONT);
            trackStrategy.track(trackParamsData);
        } catch (Exception e) {
            logger.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.AFTER_SALE_ORDER_CLOSE_FRONT.getArchivedName(),e);
        }
    }

    /**
     * @Description 更新销售单的预警信息
     * <AUTHOR>
     * @Date 15:48 2021/7/8
     * @Param [saleorderId]
     * @return void
     **/
    protected void updateUnlockSaleOrderWarning(Integer saleorderId){
        try{
            long nowTime = DateUtil.gainNowDate();
            List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);
            if(CollectionUtils.isNotEmpty(saleorderGoodsList)){
                for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
                    // 时效监控开始时间不为空，才去更新，不然不需要重置
                    if (saleorderGoods.getAgingTime() != null && saleorderGoods.getAgingTime() > 0) {
                        SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
                        saleorderGoodsVo.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
                        saleorderGoodsVo.setWarnLevel(null);
                        saleorderGoodsVo.setAging(0);
                        saleorderGoodsVo.setAgingTime(nowTime);
                        saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
                    }
                }
            }
        }catch (Exception e){
            logger.error("解锁时,更新销售单预警状态失败，销售单id:{},错误：{}",saleorderId,e);
        }
    }

    /**
     * @Description 判断售后单是否可以关闭
     * <AUTHOR>
     * @Date 17:14 2021/7/7
     * @Param [afterSaleOrderCloseDto]
     * @return boolean
     **/
    private boolean isCanClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) throws AfterSaleCloseException {
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSaleOrderCloseDto.getAfterSalesNo());
        if(null == afterSales){
            throw new AfterSaleCloseException("未找到对应售后单！");
        }
        //售后单审核状态为审核不通过，状态不为已完结和已关闭则可以关闭
        if(3 == afterSales.getStatus() && 2 !=afterSales.getAtferSalesStatus() && 3 !=afterSales.getAtferSalesStatus()){
            return true;
        }
        return false;
    }
    /**
     * @Description 判断销售单下是否有退款类型售后单
     * <AUTHOR>
     * @Date 16:37 2021/7/12
     * @Param [saleorder]
     * @return boolean
     **/
    protected boolean isHaveReturnMoneyAfterSaleorder(Saleorder saleorder) {
        AfterSales afterSales = new AfterSales();
        afterSales.setOrderId(saleorder.getSaleorderId());
        return !afterSalesMapper.getReturnBackMoneyByOrderId(afterSales).isEmpty();
    }
    /**
     * @Description 判断销售单下是否有未解锁的SaleorderGoods
     * <AUTHOR>
     * @Date 16:38 2021/7/12
     * @Param [saleorder]
     * @return boolean
     **/
    protected boolean isHaveUnlockSaleorderGoods(Saleorder saleorder) {
        List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
        Optional<SaleorderGoodsVo> isHaveUnlockSaleorderGood = sgvList.stream().filter(sgv -> sgv.getLockedStatus() == 1).findAny();
        return isHaveUnlockSaleorderGood.isPresent();
    }
    /**
     * @Description 计算销售单商品的已发货数量
     * <AUTHOR>
     * @Date 17:26 2021/7/12
     * @Param [saleorderGoods]
     * @return java.lang.Integer
     **/
    protected Integer computeAlreadyDeliveryNum(SaleorderGoods saleorderGoods){
        if(saleorderGoods.getDeliveryStatus().equals(2)){
            return saleorderGoods.getNum() - (saleorderGoods.getAfterReturnNum() == null ? 0 : saleorderGoods.getAfterReturnNum());
        }else if(saleorderGoods.getDeliveryStatus().equals(1)){
            return Integer.min(
                    saleorderGoods.getNum() - (saleorderGoods.getAfterReturnNum() == null ? 0 : saleorderGoods.getAfterReturnNum())
                    , saleorderGoods.getDeliveryNum());
        }else{
            return 0;
        }
    }

}
