void function () {
    new Vue({
        el: '#page-container',
        data: {
            id: '',
            copyid: '',
            visitRecordNo: '',
            addTime: '',
            fixedTop: layout_hidden_value ? 0 : 50, //表头悬浮高度，根据有没有头部判断,
            pageLoading: false,
            traderName: '',
            tycFlag: '',
            traderId: '',
            traderInfo: null,
            belonger: '',
            isShare: false,
            traderType: '',
            traderTypeList: [{
                label: '渠道商',
                value: 465
            }, {
                label: '终端',
                value: 466
            }],
            isFromPhone: false,
            mobile: '',
            mobileStatus: 0,
            telephone: '',
            otherContactInfo: '',
            traderContactName: '',
            contactPosition: '',
            positionList: [],
            noContract: false,
            addressData: [],
            area: [],
            areaInfo: [],
            addressDetail: '',
            visitor: '',
            visitorName: '',
            visitorPic: '',
            tongxing: [],
            defaultTongxingItems: [],
            visitTarget: [],
            userRemoteInfo: {
                url: '/crm/user/profile/getAllUserInfo',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            planVisitDate: '',
            remark: '',
            visitType: [],
            visitTypeList: [{
                label: '客情维护',
                value: 'C'
            }, {
                label: '新客开发',
                value: 'A'
            }, {
                label: '会员签约',
                value: 'D'
            }, {
                label: '产品推广',
                value: 'E'
            }, {
                label: '线索商机跟进',
                value: 'B'
            }],
            businessNo: '',
            businessNoMsg: '',
            businessNoTip: '',
            businessInfo: {},
            cansubmit: true,
            bizId: '',
            bizType: '',
        },
        computed: {
            mapAddress () {
                if(this.addressDetail) {
                    let str = '';
                    if(this.areaInfo.length) {
                        this.areaInfo.forEach(item => {
                            str += item.label;
                        })
                    }
                    return str + this.addressDetail;
                } else {
                    return this.traderName || '';
                }
            }
        },
        async created() {
            let id = GLOBAL.getQuery('id');
            let copyid = GLOBAL.getQuery('copy');
            let bizId = GLOBAL.getQuery('bizid');
            let bizType = GLOBAL.getQuery('type');

            if(id || copyid) {
                this.id = id;
                this.copyid = copyid;
                this.getDetailInfo();
            } else if(bizId && bizType) {
                this.bizId = bizId;
                this.bizType = bizType;
                this.getBizInfo();
            } else {
                this.visitor = document.querySelector('#golbal_userId').value;
                this.visitorName = document.querySelector('#golbal_userName').value;
                this.visitorPic = document.querySelector('#golbal_headPic').value;
            }

            // 地址数据
            await this.$axios.get('/crm/common/profile/getRegionAll').then(({ data }) => {
                if (data.success) {
                    this.addressData = data.data || [];
                }
            });

            this.formBindValid();
        },
        mounted() {

        },
        methods: {
            getDetailInfo() {
                GLOBAL.showGlobalLoading();
                this.pageLoading = true;

                this.$axios.get('/crm/visitrecord/profile/detail?id=' + (this.id || this.copyid)).then(({data}) => {
                    GLOBAL.hideGlobalLoading();

                    if(data.success) {
                        
                        let detail = data.data;

                        this.traderId = detail.traderId || '';
                        this.traderName = detail.customerName || '';
                        this.traderType = detail.customerNature || '';

                        if(this.traderType) {
                            this.traderTypeChange();
                        }

                        this.noContract = detail.noContract === 'Y' ? true : false; 

                        if(!this.noContract) {
                            this.traderContactName = detail.contactName || '';
                            this.mobile = detail.contactMobile || '';
                            this.telephone = detail.contactTele || '';
                            this.contactPosition = detail.contactPosition || '';
                            this.otherContactInfo = detail.otherContact || '';
                        }

                        if(this.traderId && detail.visitCustomerVo) {
                            this.belonger = detail.visitCustomerVo.belongerName || '';
                            this.tycFlag = detail.visitCustomerVo.tycFlag || '';
                            this.isShare = detail.visitCustomerVo.share || false;
                            this.traderInfo = {
                                historyTransactionAmount: detail.visitCustomerVo.historyTransactionAmount || '',
                                historyTransactionNum: detail.visitCustomerVo.historyTransactionNum || '',
                                lastOrderTime: detail.visitCustomerVo.lastOrderTime || '',
                                customerGrade: detail.visitCustomerVo.customerGrade || '',
                            }
                        }

                        if(detail.areaName) {
                            this.areaInfo = [{
                                label: detail.provinceName,
                                value: detail.provinceCode
                            }, {
                                label: detail.cityName,
                                value: detail.cityCode
                            }, {
                                label: detail.areaName,
                                value: detail.areaCode
                            }];
                            this.area = [detail.provinceCode, detail.cityCode, detail.areaCode];
                        }

                        this.addressDetail = detail.visitAddress || '';

                        if(this.id) {
                            this.visitRecordNo = detail.visitRecordNo || '';
                            this.visitor = detail.visitorId || '';
                            this.visitorName = detail.visitorName || '';
                            this.visitorPic = detail.visitorPic || '';
                            this.planVisitDate = detail.planVisitDate || '';
                            this.visitType = detail.visitTarget.split(',');
                            this.remark = detail.remark || '';
                            this.addTime = detail.addTime || '';
    
                            if(detail.tongxingUserList && detail.tongxingUserList.length) {
                                detail.tongxingUserList.forEach(item => {
                                    this.defaultTongxingItems.push({
                                        label: item.userName,
                                        value: item.tongxingUserId,
                                        avatar: item.aliasHeadPicture
                                    })
                                    this.tongxing.push(item.tongxingUserId)
                                })
                            }
    
                            if(detail.bussinessChanceNo) {
                                this.businessInfo = {
                                    relateType: detail.relateType,
                                    bizId: detail.bussinessChanceId
                                };
                                this.businessNo = detail.bussinessChanceNo
    
                                this.checkBusinessNo();
                            }
                        } else {
                            this.visitor = document.querySelector('#golbal_userId').value;
                            this.visitorName = document.querySelector('#golbal_userName').value;
                            this.visitorPic = document.querySelector('#golbal_headPic').value;
                        }

                        this.checkMobileStatus();
                    }

                    this.pageLoading = false;
                })
            },
            getBizInfo() {
                GLOBAL.showGlobalLoading();
                this.pageLoading = true;

                this.$axios.get(`/crm/presales/profile/detail?dataId=${this.bizId}&type=${this.bizType}`).then(({data}) => {
                    if(data.success) {
                        let detail = data.data;

                        this.traderId = detail.traderId;
                        this.traderName = detail.traderName;
                        this.belonger = detail.belonger;
                        this.tycFlag = detail.tycFlag;
                        this.traderType = detail.customerNature;

                        if(this.traderType) {
                            this.traderTypeChange();
                        }

                        this.traderContactName = detail.contact;
                        this.mobile = detail.phone;
                        this.telephone = detail.telephone;
                        
                        if(this.traderId) {
                            this.getTraderInfo();
                        }

                        this.businessNo = detail.bussinessChanceNo || detail.leadsNo;
                        this.visitTypeList[4].disabled = true;
                        this.visitType = ['B']

                        if(this.businessNo) {
                            this.checkBusinessNo();
                        }

                    }

                    GLOBAL.hideGlobalLoading();
                    this.pageLoading = false;
                })
            },
            formBindValid() {
                let _this = this;
                let rules = {
                    traderName: {
                        required: '请输入客户名称'
                    },
                    traderType: {
                        required: '请选择客户类型'
                    },
                    mobile: {
                        custom: [
                            {
                                valid() {
                                    if (!_this.noContract && !_this.mobile.trim() && !_this.telephone.trim() && !_this.otherContactInfo.trim()) {
                                        return false;
                                    }

                                    return true;
                                },
                                message: '手机、固话和其他联系方式至少填写一项'
                            },
                            {
                                valid() {
                                    if (!_this.noContract && _this.mobile && _this.mobile.length !== 11) {
                                        return false;
                                    }

                                    return true;
                                },
                                message: '请输入11位手机号码'
                            }
                        ]
                    },
                    traderContactName: {
                        custom: {
                            valid() {
                                if (!_this.noContract && !_this.traderContactName) {
                                    return false;
                                } else {
                                    return true;
                                }
                            },
                            message: '请输入联系人'
                        }
                    },
                    contactPosition: {
                        custom: {
                            valid() {
                                if (!_this.noContract && _this.traderType && !_this.contactPosition) {
                                    return false;
                                } else {
                                    return true;
                                }
                            },
                            message: '请选择联系人职位'
                        }
                    },
                    areaInfo: {
                        required: '请选择拜访地区'
                    },
                    visitor: {
                        required: '请选择拜访人',
                        custom: {
                            valid() {
                                if (_this.visitor && _this.tongxing.length && _this.tongxing.indexOf(parseInt(_this.visitor)) !== -1) {
                                    return false;
                                } else {
                                    return true;
                                }
                            },
                            message: '拜访人和同行拜访人请勿重叠'
                        }
                    },
                    tongxing: {
                        custom: {
                            valid() {
                                if (_this.visitor && _this.tongxing.length && _this.tongxing.indexOf(parseInt(_this.visitor)) !== -1) {
                                    return false;
                                } else {
                                    return true;
                                }
                            },
                            message: '拜访人和同行拜访人请勿重叠'
                        }
                    },
                    visitType: {
                        required: '请选择拜访目标',
                    },
                    planVisitDate: {
                        required: '请选择拜访时间',
                    }
                };

                this.$form && this.$form.rules(rules, 'AddVisitRecord', this);
            },
            getTraderInfo() {
                this.$axios.get('/crm/trader/profile/queryTraderDetail?traderId=' + this.traderId).then(({ data }) => {
                    if (data.success) {
                        this.traderInfo = data.data;
                    }
                })
            },
            /* 客户名称 */
            handlerTrader(data) {
                console.log('handler Trader:', data);
                this.traderId = data.traderId || '';
                this.tycFlag = data.tycFlag || 'N';

                if (this.traderId) { // 已建档且性质为终端：自动带入终端名称和地区 // 465分销 466终端
                    if (data.customerNature == 466 && data.traderName) {
                        this.terminalTraderName = data.traderName || '';
                        this.isFromTyc = true;
                    } else {
                        this.terminalTraderName = '';
                        this.isFromTyc = false;
                    }
                    if (data.areaIds) {
                        let areaIds = data.areaIds.split(',');

                        if(areaIds.length === 3) {
                            this.area = areaIds;
                        }
                    }

                    if (data.saleId) {
                        this.belongerId = data.saleId || '';
                        this.belonger = data.saleName || '';
                    }

                    this.traderType = data.customerNature;
                    this.traderTypeChange();
                    this.$form.validEl('AddVisitRecord_traderType');

                    this.getTraderInfo();
                } else {
                    // 清空
                    this.traderInfo = null;
                    this.traderType = "";

                    if (this.isFromTyc) {
                        this.terminalTraderName = '';
                        this.isFromTyc = false;
                    }
                }

                if(this.businessNo) {
                    this.checkBusinessNo();
                }
            },
            traderTypeChange() {
                this.positionList = [];
                this.contactPosition = "";

                setTimeout(() => {
                    let list = [];
    
                    let labels = {
                        465: ['老板', '销售负责人', '销售经理', '采购负责人', '采购经理', '商务人员', '财务人员', '物流人员', '售后人员', '临床工程师', '其他'],
                        466: ['院长', '设备科人员', '临床医生', '采购负责人', '采购经理', '运营人员', '财务人员', '医院库管', '其他']
                    };
    
                    labels[this.traderType].forEach(item => {
                        list.push({
                            label: item,
                            value: item
                        })
                    })
    
                    this.positionList = list;
                }, 100);
            },
            // 手机
            handlerPhone(val) {
                console.log('handlerPhone', val);
                if (this.traderId) {
                    if (this.isFromPhone) { // 当前是选择的
                        this.traderContactName = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    } else if (val.choosed) {
                        this.isFromPhone = true;
                        this.traderContactName = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    }
                }

                // this.$form.validEl('AddVisitRecord_mobile');
                this.checkMobileStatus();

                if (this.isFromPhone && this.traderContactName) {
                    this.$form.validEl('AddVisitRecord_traderContactName');
                }
            },
            checkMobileStatus() {
                if(this.mobile && this.$form.validEl('AddVisitRecord_mobile')) {
                    this.$axios.post('/crm/visitrecord/profile/checkMobileExists?mobile=' + this.mobile).then(({ data }) => {
                        if (data.success) {
                            this.mobileStatus = data.data ? 1 : 2
                        }
                    })
                } else {
                    this.mobileStatus = 0
                }
            },
            telephone_Blur() {
                this.$form.validEl('AddVisitRecord_mobile');
            },
            //其他联系方式
            handlerMoreContact() {
                this.$form.validEl('AddVisitRecord_mobile');
            },
            handlerContact() {
                this.isFromPhone = false;
                this.traderContactId = '';
            },
            handleArea(data) {
                this.areaInfo = data;
                let area = [];

                data.forEach(item => {
                    if (item.value) {
                        area.push(item.value);
                    }
                })

                this.area = area;
            },
            handlerVisitorChange(data) {
                console.log(data)
                this.visitorName = data.selected.label;
                this.visitorPic = data.selected.avatar;

                if(this.tongxing && this.tongxing.length) {
                    this.$form.validEl('AddVisitRecord_tongxing');
                }
            },
            handlerTongxingChange() {
                if(this.visitor) {
                    this.$form.validEl('AddVisitRecord_visitor');
                }
            },
            async checkBusinessNo() {
                if(this.visitType.indexOf('B') === -1) {
                    return true;
                }

                if (!this.businessNo) {
                    this.businessNoMsg = "请输入跟进的线索或商机编号";
                    return false;
                }
                let flag = true;
                await this.$axios.post('/crm/visitrecord/profile/checkBizNo', {
                    bizNo: this.businessNo,
                    customerName: this.traderName
                }).then(({ data }) => {
                    if (data.success) {
                        flag = data.data.checkResult;
                        this.businessNoMsg = data.data.errorMsg;
                        this.businessNoTip = data.data.warnMsg;

                        if(flag) {
                            this.businessInfo = {
                                relateType: data.data.relateType,
                                bizId: data.data.bizId,
                                bizNo: this.businessNo,
                                customerName: data.data.customerName,
                                salesName: data.data.salesName
                            }
                        } else {
                            this.businessInfo = {
                                relateType: '',
                                bizId: '',
                                bizNo: '',
                                customerName: '',
                                salesName: ''
                            };
                        }
                    }
                });

                if (!flag) {
                    return false;
                }

                this.businessNoMsg = '';
                return true;
            },
            async submit() {
                let bizNoCheck = await this.checkBusinessNo();
                if (!this.cansubmit || !this.$form.validForm('AddVisitRecord') || !bizNoCheck) {
                    return;
                }

                let reqData = {
                    id: this.id,
                    visitRecordNo: this.visitRecordNo,
                    traderId: this.traderId,
                    customerName: this.traderName,
                    customerNature: this.traderType,
                    provinceCode: this.areaInfo[0].value,
                    provinceName: this.areaInfo[0].label,
                    cityCode: this.areaInfo[1].value,
                    cityName: this.areaInfo[1].label,
                    areaCode: this.areaInfo[2].value,
                    areaName: this.areaInfo[2].label,
                    visitAddress: this.addressDetail,
                    visitorId: this.visitor,
                    visitorName: this.visitorName,
                    planVisitDate: this.planVisitDate,
                    visitTarget: this.visitType.join(','),
                    tongxingIds: this.tongxing,
                    remark: this.remark,
                    noContract: this.noContract ? 'Y' : 'N',
                    preId: this.copyid
                }

                if(!this.noContract) {
                    reqData.contactName = this.traderContactName;
                    reqData.contactMobile = this.mobile;
                    reqData.contactTele = this.telephone;
                    reqData.contactPosition = this.contactPosition;
                    reqData.otherContact = this.otherContactInfo;
                }

                if(this.visitType.indexOf('B') !== -1 && this.businessNo) {
                    reqData.relateBizData = {
                        relateType: this.businessInfo.relateType,
                        bizId: this.businessInfo.bizId,
                        bizNo: this.businessNo
                    };
                }

                this.cansubmit = false;

                this.$axios.post('/crm/visitrecord/profile/save', reqData).then(({data}) => {
                    if(data.success) {
                        window.location.href = "/crm/visitRecord/profile/detail?id=" + data.data.id;
                    } else {
                        this.cansubmit = true;
                        this.$message.warn(data.message)
                    }
                })
            }
        }
    })
}.call(this);