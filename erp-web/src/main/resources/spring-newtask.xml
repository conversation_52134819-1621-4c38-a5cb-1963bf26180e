<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:apollo="http://www.ctrip.com/schema/apollo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd">
    
    
	<apollo:config/>

	 
    <!-- ********************************* 基础配置 ********************************* -->
	<!-- 配置01、JobHandler 扫描路径 -->
	<context:component-scan base-package="com.newtask" />
	<context:component-scan base-package="com.vedeng.erp.*.task" />
	<context:component-scan base-package="com.wms.task" />



	<!-- 配置02、执行器 -->
	<bean id="xxlJobSpringExecutor" class="com.newtask.TaskInit"   >
		<!-- 执行器注册中心地址[选填]，为空则关闭自动注册 -->
		<property name="adminAddresses" value="${xxl.job.admin.addresses}" />
		<!-- 执行器AppName[选填]，为空则关闭自动注册 -->
		<property name="appName" value="${xxl.job.executor.appname}" />
		<!-- 执行器端口号[选填]，小于等于0则自动获取 -->
		<property name="port" value="10010" />
		<!-- 访问令牌[选填]，非空则进行匹配校验 -->
		<property name="accessToken" value="${xxl.job.accessToken:erp}" />
		<!-- 执行器日志路径[选填]，为空则使用默认路径 -->
		<property name="logPath" value="${xxl.job.executor.logpath}" />
		<!-- 日志保存天数[选填]，值大于3时生效 -->
		<property name="logRetentionDays" value="${xxl.job.executor.logretentiondays}" />
	</bean>

</beans>
 
