<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.BankBillIgnoreRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.BankBillIgnoreRecordEntity">
    <!--@mbg.generated-->
    <!--@Table T_BANK_BILL_IGNORE_RECORD-->
    <id column="BANK_BILL_IGNORE_INFO_ID" jdbcType="INTEGER" property="bankBillIgnoreInfoId" />
    <result column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId" />
    <result column="ACC_NAME" jdbcType="VARCHAR" property="accName" />
    <result column="CONTACT_UNIT_TYPE" jdbcType="VARCHAR" property="contactUnitType" />
    <result column="CONTACT_UNIT" jdbcType="VARCHAR" property="contactUnit" />
    <result column="CONTACT_UNIT_NO" jdbcType="VARCHAR" property="contactUnitNo" />
    <result column="TRADE_SUBJECT" jdbcType="VARCHAR" property="tradeSubject" />
    <result column="IGNORE_REASON" jdbcType="INTEGER" property="ignoreReason" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BANK_BILL_IGNORE_INFO_ID, BANK_BILL_ID, ACC_NAME, CONTACT_UNIT_TYPE, CONTACT_UNIT, 
    CONTACT_UNIT_NO, TRADE_SUBJECT, IGNORE_REASON, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, 
    CREATOR_NAME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK,`TYPE`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BANK_BILL_IGNORE_RECORD
    where BANK_BILL_IGNORE_INFO_ID = #{bankBillIgnoreInfoId,jdbcType=INTEGER}
  </select>

  <select id="selectByBankBillId" resultType="com.vedeng.erp.settlement.domain.entity.BankBillIgnoreRecordEntity">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BANK_BILL_IGNORE_RECORD
    where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
      and IS_DELETE = 0;
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_BANK_BILL_IGNORE_RECORD
    where BANK_BILL_IGNORE_INFO_ID = #{bankBillIgnoreInfoId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BANK_BILL_IGNORE_INFO_ID" keyProperty="bankBillIgnoreInfoId" parameterType="com.vedeng.erp.settlement.domain.entity.BankBillIgnoreRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_BILL_IGNORE_RECORD (BANK_BILL_ID, ACC_NAME, CONTACT_UNIT_TYPE, 
      CONTACT_UNIT, CONTACT_UNIT_NO, TRADE_SUBJECT, 
      IGNORE_REASON, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, REMARK, 
      UPDATE_REMARK)
    values (#{bankBillId,jdbcType=INTEGER}, #{accName,jdbcType=VARCHAR}, #{contactUnitType,jdbcType=VARCHAR}, 
      #{contactUnit,jdbcType=VARCHAR}, #{contactUnitNo,jdbcType=VARCHAR}, #{tradeSubject,jdbcType=VARCHAR}, 
      #{ignoreReason,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="BANK_BILL_IGNORE_INFO_ID" keyProperty="bankBillIgnoreInfoId" parameterType="com.vedeng.erp.finance.dto.BankBillIgnoreRecordDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_BILL_IGNORE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bankBillId != null">
        BANK_BILL_ID,
      </if>
      <if test="accName != null">
        ACC_NAME,
      </if>
      <if test="contactUnitType != null">
        CONTACT_UNIT_TYPE,
      </if>
      <if test="contactUnit != null">
        CONTACT_UNIT,
      </if>
      <if test="contactUnitNo != null">
        CONTACT_UNIT_NO,
      </if>
      <if test="tradeSubject != null">
        TRADE_SUBJECT,
      </if>
      <if test="ignoreReason != null">
        IGNORE_REASON,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bankBillId != null">
        #{bankBillId,jdbcType=INTEGER},
      </if>
      <if test="accName != null">
        #{accName,jdbcType=VARCHAR},
      </if>
      <if test="contactUnitType != null">
        #{contactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="contactUnit != null">
        #{contactUnit,jdbcType=VARCHAR},
      </if>
      <if test="contactUnitNo != null">
        #{contactUnitNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeSubject != null">
        #{tradeSubject,jdbcType=VARCHAR},
      </if>
      <if test="ignoreReason != null">
        #{ignoreReason,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.BankBillIgnoreRecordEntity">
    <!--@mbg.generated-->
    update T_BANK_BILL_IGNORE_RECORD
    <set>
      <if test="bankBillId != null">
        BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
      </if>
      <if test="accName != null">
        ACC_NAME = #{accName,jdbcType=VARCHAR},
      </if>
      <if test="contactUnitType != null">
        CONTACT_UNIT_TYPE = #{contactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="contactUnit != null">
        CONTACT_UNIT = #{contactUnit,jdbcType=VARCHAR},
      </if>
      <if test="contactUnitNo != null">
        CONTACT_UNIT_NO = #{contactUnitNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeSubject != null">
        TRADE_SUBJECT = #{tradeSubject,jdbcType=VARCHAR},
      </if>
      <if test="ignoreReason != null">
        IGNORE_REASON = #{ignoreReason,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where BANK_BILL_IGNORE_INFO_ID = #{bankBillIgnoreInfoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.BankBillIgnoreRecordEntity">
    <!--@mbg.generated-->
    update T_BANK_BILL_IGNORE_RECORD
    set BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
      ACC_NAME = #{accName,jdbcType=VARCHAR},
      CONTACT_UNIT_TYPE = #{contactUnitType,jdbcType=VARCHAR},
      CONTACT_UNIT = #{contactUnit,jdbcType=VARCHAR},
      CONTACT_UNIT_NO = #{contactUnitNo,jdbcType=VARCHAR},
      TRADE_SUBJECT = #{tradeSubject,jdbcType=VARCHAR},
      IGNORE_REASON = #{ignoreReason,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where BANK_BILL_IGNORE_INFO_ID = #{bankBillIgnoreInfoId,jdbcType=INTEGER}
  </update>
</mapper>