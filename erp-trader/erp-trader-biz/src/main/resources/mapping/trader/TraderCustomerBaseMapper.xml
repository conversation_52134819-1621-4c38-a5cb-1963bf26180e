<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerBaseMapper">
    <update id="updateAssociatedCustomerGroupOfCustomer">
        update T_TRADER_CUSTOMER
        set ASSOCIATED_CUSTOMER_GROUP = #{associatedCustomerGroup}
        where TRADER_CUSTOMER_ID in
        <foreach collection="traderCustomerList" index="index" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>


    <select id="getTraderCutomerInfoByTraderId"
            resultType="com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto">
        SELECT TRADER_CUSTOMER_ID,TRADER_ID,ASSOCIATED_CUSTOMER_GROUP
        FROM T_TRADER_CUSTOMER
        WHERE TRADER_ID = #{traderId,jdbcType = INTEGER}
    </select>
    <select id="getRelatedTraderByGroupId" resultType="com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto">
        SELECT tc.TRADER_CUSTOMER_ID,tc.TRADER_ID,tc.ASSOCIATED_CUSTOMER_GROUP,t.TRADER_NAME
        FROM T_TRADER_CUSTOMER tc join T_TRADER t ON tc.TRADER_ID = t.TRADER_ID
        WHERE tc.ASSOCIATED_CUSTOMER_GROUP = #{associatedCustomerGroup,jdbcType = BIGINT}
    </select>
    <select id="getTraderCutomerInfoById" resultType="com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto">
        SELECT TRADER_CUSTOMER_ID,TRADER_ID,ASSOCIATED_CUSTOMER_GROUP,CUSTOMER_NATURE
        FROM T_TRADER_CUSTOMER
        WHERE TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType = INTEGER}
    </select>
    <select id="getAllTraderCustomerByUserId"
            resultType="com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto">
        SELECT *
        FROM T_TRADER_CUSTOMER TC
        LEFT JOIN T_R_TRADER_J_USER RTJU
            ON TC.TRADER_ID = RTJU.TRADER_ID
        WHERE RTJU.USER_ID = #{userId,jdbcType=INTEGER}
    </select>
    <select id="getTraderCustomerListByAssociatedCustomerGroup" resultType="java.lang.Integer">
        select TRADER_CUSTOMER_ID
        from T_TRADER_CUSTOMER where ASSOCIATED_CUSTOMER_GROUP = #{associatedCustomerGroup}
    </select>

    <select id="getTraderCustomerInfoVo" resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo">
        SELECT A.TRADER_ID,A.TRADER_CUSTOMER_CATEGORY_ID,A.TRADER_CUSTOMER_ID,A.IS_ENABLE,
        B.TRADER_NAME,
        A.CUSTOMER_TYPE,
        D.TITLE AS customerTypeStr,
        A.CUSTOMER_NATURE,
        E.TITLE AS customerNatureStr,
        B.AREA_ID,
        B.AREA_IDS,
        A.CUSTOMER_LEVEL,
        C.TITLE AS customerLevelStr,
        A.REGISTERED_CAPITAL,
        A.REGISTERED_DATE,
        DATE_FORMAT(A.REGISTERED_DATE, '%Y-%m-%d') AS REGISTERED_DATE_STR,
        A.AMOUNT,
        A.OWNERSHIP
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID <!-- AND B.IS_ENABLE = 1 -->
        LEFT JOIN T_SYS_OPTION_DEFINITION C
        ON A.CUSTOMER_LEVEL = C.SYS_OPTION_DEFINITION_ID AND C.PARENT_ID = 11
        LEFT JOIN T_SYS_OPTION_DEFINITION D
        ON A.CUSTOMER_TYPE = D.SYS_OPTION_DEFINITION_ID AND D.PARENT_ID = 425
        LEFT JOIN T_SYS_OPTION_DEFINITION E
        ON     A.CUSTOMER_NATURE = E.SYS_OPTION_DEFINITION_ID
        AND E.PARENT_ID = 464
        WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="getTraderCustomerInfo" resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo">
        select
            B.TRADER_NAME,
            B.TRADER_ID,
            A.TRADER_CUSTOMER_ID,
            A.CUSTOMER_NATURE,
            F.USER_ID  saleId,
            G.USERNAME saleName
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER F on F.TRADER_ID = A.TRADER_ID and F.TRADER_TYPE = 1
        LEFT JOIN T_USER G ON F.USER_ID = G.USER_ID
        where
            B.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="getTraderCustomerInfoVoByName" resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo">
        SELECT A.TRADER_ID,A.TRADER_CUSTOMER_CATEGORY_ID,A.TRADER_CUSTOMER_ID,
        B.TRADER_NAME,
        A.CUSTOMER_TYPE,
        D.TITLE AS customerTypeStr,
        A.CUSTOMER_NATURE,
        E.TITLE AS customerNatureStr,
        B.AREA_ID,
        B.AREA_IDS,
        A.CUSTOMER_LEVEL,
        C.TITLE AS customerLevelStr,
        A.REGISTERED_CAPITAL,
        A.REGISTERED_DATE,
        DATE_FORMAT(A.REGISTERED_DATE, '%Y-%m-%d') AS REGISTERED_DATE_STR,
        A.AMOUNT,
        A.OWNERSHIP,
        F.USER_ID saleId,
        G.USERNAME saleName
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID <!-- AND B.IS_ENABLE = 1 -->
        LEFT JOIN T_SYS_OPTION_DEFINITION C
        ON A.CUSTOMER_LEVEL = C.SYS_OPTION_DEFINITION_ID AND C.PARENT_ID = 11
        LEFT JOIN T_SYS_OPTION_DEFINITION D
        ON A.CUSTOMER_TYPE = D.SYS_OPTION_DEFINITION_ID AND D.PARENT_ID = 425
        LEFT JOIN T_SYS_OPTION_DEFINITION E
        ON     A.CUSTOMER_NATURE = E.SYS_OPTION_DEFINITION_ID
        AND E.PARENT_ID = 464
        LEFT JOIN T_R_TRADER_J_USER F  on F.TRADER_ID = A.TRADER_ID and F.TRADER_TYPE = 1
        LEFT JOIN T_USER G ON F.USER_ID = G.USER_ID
        where
            <if test="name!=null and name!=''">
                 B.TRADER_NAME LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%' )
            </if>
        <if test="belong">
            <if test="userId != null">
               and  G.USER_ID =#{userId,jdbcType=INTEGER}
            </if>
        </if>
        <if test="!belong">
            <if test="userId != null">
                and  G.USER_ID !=#{userId,jdbcType=INTEGER}
            </if>
        </if>
        group by A.TRADER_CUSTOMER_ID
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>

    </select>


    <select id="getTraderCustomerInfoVoByNameAndUserIdForCRM" resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo">
        SELECT A.TRADER_ID,A.TRADER_CUSTOMER_CATEGORY_ID,A.TRADER_CUSTOMER_ID,
        B.TRADER_NAME,
        CASE when TYC.NAME IS NOT NULL THEN 'Y'
        ELSE 'N' END AS "tycFlag",
        A.CUSTOMER_TYPE,
        D.TITLE AS customerTypeStr,
        A.CUSTOMER_NATURE,
        E.TITLE AS customerNatureStr,
        B.AREA_ID,
        B.AREA_IDS,
        A.CUSTOMER_LEVEL,
        C.TITLE AS customerLevelStr,
        A.REGISTERED_CAPITAL,
        A.REGISTERED_DATE,
        DATE_FORMAT(A.REGISTERED_DATE, '%Y-%m-%d') AS REGISTERED_DATE_STR,
        A.AMOUNT,
        A.OWNERSHIP,
        F.USER_ID saleId,
        G.USERNAME saleName
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID <!-- AND B.IS_ENABLE = 1 -->
        LEFT JOIN T_TRADER_INFO_TYC TYC ON B.TRADER_NAME=TYC.NAME
        LEFT JOIN T_SYS_OPTION_DEFINITION C
        ON A.CUSTOMER_LEVEL = C.SYS_OPTION_DEFINITION_ID AND C.PARENT_ID = 11
        LEFT JOIN T_SYS_OPTION_DEFINITION D
        ON A.CUSTOMER_TYPE = D.SYS_OPTION_DEFINITION_ID AND D.PARENT_ID = 425
        LEFT JOIN T_SYS_OPTION_DEFINITION E
        ON     A.CUSTOMER_NATURE = E.SYS_OPTION_DEFINITION_ID
        AND E.PARENT_ID = 464
        LEFT JOIN T_R_TRADER_J_USER F  on F.TRADER_ID = A.TRADER_ID and F.TRADER_TYPE = 1
        LEFT JOIN T_USER G ON F.USER_ID = G.USER_ID
        where 1=1 AND A.IS_ENABLE = 1
        <if test="belong">
            <if test="userId != null">
                and  G.USER_ID =#{userId,jdbcType=INTEGER}
            </if>
        </if>
        <if test="!belong">
            <if test="userId != null">
                and  G.USER_ID !=#{userId,jdbcType=INTEGER}
            </if>
        </if>
        <if test="name!=null and name!=''">
            AND B.TRADER_NAME LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%' )
        </if>

        group by A.TRADER_CUSTOMER_ID
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>

    </select>
    <select id="getTraderCustomerInfoVoByNameAndUserIdForCRMShared" resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo">
        SELECT A.TRADER_ID,A.TRADER_CUSTOMER_CATEGORY_ID,A.TRADER_CUSTOMER_ID,
        B.TRADER_NAME,
        CASE when TYC.NAME IS NOT NULL THEN 'Y'
        ELSE 'N' END AS "tycFlag",
        A.CUSTOMER_TYPE,
        D.TITLE AS customerTypeStr,
        A.CUSTOMER_NATURE,
        E.TITLE AS customerNatureStr,
        B.AREA_ID,
        B.AREA_IDS,
        A.CUSTOMER_LEVEL,
        C.TITLE AS customerLevelStr,
        A.REGISTERED_CAPITAL,
        A.REGISTERED_DATE,
        DATE_FORMAT(A.REGISTERED_DATE, '%Y-%m-%d') AS REGISTERED_DATE_STR,
        A.AMOUNT,
        A.OWNERSHIP,
        F.USER_ID saleId,
        G.USERNAME saleName
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID <!-- AND B.IS_ENABLE = 1 -->
        LEFT JOIN T_TRADER_INFO_TYC TYC ON B.TRADER_NAME=TYC.NAME
        LEFT JOIN T_SYS_OPTION_DEFINITION C
        ON A.CUSTOMER_LEVEL = C.SYS_OPTION_DEFINITION_ID AND C.PARENT_ID = 11
        LEFT JOIN T_SYS_OPTION_DEFINITION D
        ON A.CUSTOMER_TYPE = D.SYS_OPTION_DEFINITION_ID AND D.PARENT_ID = 425
        LEFT JOIN T_SYS_OPTION_DEFINITION E
        ON     A.CUSTOMER_NATURE = E.SYS_OPTION_DEFINITION_ID
        AND E.PARENT_ID = 464
        LEFT JOIN T_R_TRADER_J_USER F  on F.TRADER_ID = A.TRADER_ID and F.TRADER_TYPE = 1
        LEFT JOIN T_USER G ON F.USER_ID = G.USER_ID
        where 1=1 AND A.IS_ENABLE = 1
        <if test="share">
        AND A.TRADER_ID IN(
            SELECT TRADER_ID FROM T_R_SALES_J_TRADER s WHERE s.SALE_USER_ID = #{userId,jdbcType=INTEGER} AND IS_DELETED = 0
        )
        </if>
        <if test="notIncludeTraderIds != null and notIncludeTraderIds.size() != 0">
            AND A.TRADER_ID NOT IN
                <foreach collection="notIncludeTraderIds" close=")" open="(" separator="," item="item">
                    #{item}
                </foreach>
        </if>
        <if test="name!=null and name!=''">
            AND B.TRADER_NAME LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%' )
        </if>
        group by A.TRADER_CUSTOMER_ID
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>

    </select>
    <select id="getTraderCustomerInfoVoByNameForCRM" resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo">
        SELECT A.TRADER_ID,A.TRADER_CUSTOMER_CATEGORY_ID,A.TRADER_CUSTOMER_ID,
        B.TRADER_NAME,
        CASE when TYC.NAME IS NOT NULL THEN 'Y'
        ELSE 'N' END AS "tycFlag",
        A.CUSTOMER_TYPE,
        D.TITLE AS customerTypeStr,
        A.CUSTOMER_NATURE,
        E.TITLE AS customerNatureStr,
        B.AREA_ID,
        B.AREA_IDS,
        A.CUSTOMER_LEVEL,
        C.TITLE AS customerLevelStr,
        A.REGISTERED_CAPITAL,
        A.REGISTERED_DATE,
        DATE_FORMAT(A.REGISTERED_DATE, '%Y-%m-%d') AS REGISTERED_DATE_STR,
        A.AMOUNT,
        A.OWNERSHIP,
        F.USER_ID saleId,
        G.USERNAME saleName
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID <!-- AND B.IS_ENABLE = 1 -->
        LEFT JOIN T_TRADER_INFO_TYC TYC ON B.TRADER_NAME=TYC.NAME
        LEFT JOIN T_SYS_OPTION_DEFINITION C
        ON A.CUSTOMER_LEVEL = C.SYS_OPTION_DEFINITION_ID AND C.PARENT_ID = 11
        LEFT JOIN T_SYS_OPTION_DEFINITION D
        ON A.CUSTOMER_TYPE = D.SYS_OPTION_DEFINITION_ID AND D.PARENT_ID = 425
        LEFT JOIN T_SYS_OPTION_DEFINITION E
        ON     A.CUSTOMER_NATURE = E.SYS_OPTION_DEFINITION_ID
        AND E.PARENT_ID = 464
        LEFT JOIN T_R_TRADER_J_USER F  on F.TRADER_ID = A.TRADER_ID and F.TRADER_TYPE = 1
        LEFT JOIN T_USER G ON F.USER_ID = G.USER_ID
        where 1=1 AND A.IS_ENABLE = 1
        <if test="name!=null and name!=''">
            and B.TRADER_NAME LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%' )
        </if>
        group by A.TRADER_CUSTOMER_ID
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>

    </select>

    <select id="getTraderCustomerUserIdByTraderId" resultType="java.lang.Integer">
        SELECT
        F.USER_ID saleId
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID <!-- AND B.IS_ENABLE = 1 -->
        LEFT JOIN T_R_TRADER_J_USER F  on F.TRADER_ID = A.TRADER_ID and F.TRADER_TYPE = 1
        LEFT JOIN T_USER G ON F.USER_ID = G.USER_ID
        where
            B.TRADER_ID =#{traderId,jdbcType=INTEGER}
        group by A.TRADER_CUSTOMER_CATEGORY_ID
    </select>

    <select id="selectPushKingDeeTraderCustomerData" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT
            A.TRADER_CUSTOMER_ID,
            B.TRADER_ID,
            B.TRADER_NAME
        FROM
            T_TRADER_CUSTOMER A
                LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
                LEFT JOIN T_VERIFIES_INFO x ON x.RELATE_TABLE_KEY = A.TRADER_CUSTOMER_ID
                AND x.RELATE_TABLE = 'T_TRADER_CUSTOMER'
                AND x.VERIFIES_TYPE = 617
            LEFT JOIN KING_DEE_CUSTOMER C on A.TRADER_CUSTOMER_ID = C.F_NUMBER
        WHERE
            1 = 1
          AND B.COMPANY_ID = 1
          AND A.IS_ENABLE = 1
          AND x.`STATUS` = 1
            AND C.F_CUST_ID is null
        <if test="begin != null">
            and A.ADD_TIME <![CDATA[>=]]> #{begin,jdbcType=BIGINT}
        </if>
        <if test="end != null">
            and A.ADD_TIME <![CDATA[<=]]> #{end,jdbcType=BIGINT}
        </if>
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectPushKingDeeTraderCustomerDataByIds" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT
        A.TRADER_CUSTOMER_ID,
        B.TRADER_ID,
        B.TRADER_NAME
        FROM
        T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_VERIFIES_INFO x ON x.RELATE_TABLE_KEY = A.TRADER_CUSTOMER_ID
        AND x.RELATE_TABLE = 'T_TRADER_CUSTOMER'
        AND x.VERIFIES_TYPE = 617
        LEFT JOIN KING_DEE_CUSTOMER C on A.TRADER_CUSTOMER_ID = C.F_NUMBER
        WHERE
        1 = 1
        AND B.COMPANY_ID = 1
        AND A.IS_ENABLE = 1
        AND x.`STATUS` = 1
        AND C.F_NUMBER is null
        AND A.TRADER_CUSTOMER_ID in
        <foreach collection="ids" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectPushKingDeeTraderCustomerlicenceData" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT
        A.TRADER_CUSTOMER_ID,
        B.TRADER_ID,
        B.TRADER_NAME
        FROM
        T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_VERIFIES_INFO x ON x.RELATE_TABLE_KEY = A.TRADER_CUSTOMER_ID
        AND x.RELATE_TABLE = 'T_CUSTOMER_APTITUDE'
        LEFT JOIN KING_DEE_CUSTOMER C on A.TRADER_CUSTOMER_ID = C.F_NUMBER
        WHERE
        1 = 1
        AND B.COMPANY_ID = 1
        AND A.IS_ENABLE = 1
        AND x.`STATUS` = 1
        AND C.F_CUST_ID is null
        <if test="begin != null">
            and A.ADD_TIME <![CDATA[>=]]> #{begin,jdbcType=BIGINT}
        </if>
        <if test="end != null">
            and A.ADD_TIME <![CDATA[<=]]> #{end,jdbcType=BIGINT}
        </if>
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <select id="queryHaveCapitalTraderCustomer" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT
            A.TRADER_CUSTOMER_ID,
            B.TRADER_ID,
            B.TRADER_NAME
        FROM
            T_TRADER_CUSTOMER A
                LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
                LEFT JOIN KING_DEE_CUSTOMER C on A.TRADER_CUSTOMER_ID = C.F_NUMBER
                JOIN T_CAPITAL_BILL_DETAIL D ON B.TRADER_ID = D.TRADER_ID AND D.TRADER_TYPE = 1
                JOIN T_CAPITAL_BILL E ON D.CAPITAL_BILL_ID = E.CAPITAL_BILL_ID
        WHERE
            1 = 1
          AND B.COMPANY_ID = 1
          AND A.IS_ENABLE = 1
          AND C.F_CUST_ID is null
          AND E.ADD_TIME > *************
    </select>
    <select id="getTraderInfoForKingDee" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT
            tt.TRADER_ID ,
            tt.TRADER_NAME ,
            ttc.TRADER_CUSTOMER_ID,
            ttf.BANK AS 'traderFinanceDto.bank',
            ttf.BANK_CODE AS 'traderFinanceDto.bankCode',
            ttf.BANK_ACCOUNT AS 'traderFinanceDto.bankAccount',
            IFNULL(tt2.TRADER_NAME, tt.TRADER_NAME) AS parentTraderName
        FROM
            T_TRADER tt
        LEFT JOIN T_TRADER_FINANCE ttf ON
            tt.TRADER_ID = ttf.TRADER_ID AND ttf.TRADER_TYPE = 1
        LEFT JOIN T_TRADER tt2 ON tt.PARENT_ID = tt2.TRADER_ID
        LEFT JOIN T_TRADER_CUSTOMER ttc ON tt.TRADER_ID = ttc.TRADER_ID
        WHERE
            ttc.TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
        GROUP BY
            tt.TRADER_ID
        LIMIT 1
    </select>
    <select id="getTraderCustomerInfoByTraderId" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT *
        FROM T_TRADER_CUSTOMER
        WHERE TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="getTraderCustomerInfoByTraderIds" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT *
        FROM T_TRADER_CUSTOMER
        WHERE TRADER_ID IN
        <foreach collection="traderIds" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="getTraderCustomerAptitudeInfoByTraderId" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        select v.RELATE_TABLE,v.STATUS as APTITUDE_STATUS -- 当前流程状态0审核中1审核通过2审核不通过
            from T_TRADER_FINANCE a
        left join T_VERIFIES_INFO v
              on v.RELATE_TABLE_KEY = a.TRADER_FINANCE_ID  and v.RELATE_TABLE = 'T_TRADER_FINANCE'
              where a.TRADER_ID = #{traderId,jdbcType=INTEGER}
              limit 1;
    </select>

    <select id="getTraderCustomerPortrait" resultType="com.vedeng.erp.trader.domain.dto.TraderCustomerPortraitDto">
        SELECT
            ttc.TRADER_CUSTOMER_ID ,
            ttc.TRADER_ID,
            tt.TRADER_NAME,
            ttc.TRADER_CUSTOMER_CATEGORY_ID,
            ttc.CUSTOMER_TYPE,
            ttc.CUSTOMER_NATURE,
            tt.AREA_ID,
            ttc.EMPLOYEES,
            ttc.ANNUAL_SALES,
            ttc.REGISTERED_DATE,
            ttc.REGISTERED_CAPITAL,
            ttc.BUSINESS_SCOPE,
            tt.PARENT_ID,
            tt.TRADER_STATUS warehouseAreaId,
            tt.WAREHOUSE_AREA_ID,
            ttc.INVALID_REASON,
            ttc.OTHER_REASON,
            FROM_UNIXTIME(CONVERT(ttc.ADD_TIME/1000,SIGNED),'%Y-%m-%d') addTime
        FROM
            T_TRADER_CUSTOMER ttc
                LEFT JOIN T_TRADER tt ON
                ttc.TRADER_ID = tt.TRADER_ID
        WHERE
            ttc.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>
    <select id="getTraderCustomerAction" resultType="com.vedeng.erp.trader.domain.dto.TraderCustomerActionDto">
        SELECT
            ttc.TRADER_CUSTOMER_ID ,
            tt.TRADER_NAME,
            tt.BELONG_PLATFORM as belongPlatform,
            ttc.CUSTOMER_NATURE,
            ttc.IS_ENABLE
        FROM
            T_TRADER_CUSTOMER ttc
                LEFT JOIN T_TRADER tt ON
                ttc.TRADER_ID = tt.TRADER_ID
        WHERE
            ttc.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>
    <select id="findByAll" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT
            A.*,
            B.AREA_ID,
            B.TRADER_NAME
        FROM
            T_TRADER_CUSTOMER A
            LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
            <if test = "userIdList != null and userIdList.size != 0">
                AND A.TRADER_ID in (
                select TRADER_ID from T_R_TRADER_J_USER where TRADER_TYPE = 1
                and USER_ID in
                <foreach collection = "userIdList" item = "userId" index = "index"
                         open = "(" close = ")" separator = ",">
                    #{userId,jdbcType=INTEGER}
                </foreach>
                )
            </if>
        WHERE
            B.COMPANY_ID = 1
            <if test = "traderName != null and traderName != ''">
                AND B.TRADER_NAME LIKE CONCAT('%', #{traderName},'%' )
            </if>

    </select>

    <select id="getTraderListForSmartQuote" resultType="com.vedeng.erp.trader.dto.TraderForSmartQuoteDto">
        select tt.TRADER_ID, tt.TRADER_NAME,ttc.CUSTOMER_NATURE,ttc.CUSTOMER_TYPE
        from T_TRADER_CUSTOMER ttc
                 left join T_TRADER tt on ttc.TRADER_ID = tt.TRADER_ID
                 left join T_R_TRADER_J_USER tju on tju.TRADER_ID = tt.TRADER_ID and tju.TRADER_TYPE = 1
        where tt.TRADER_NAME like CONCAT('%', #{traderName,jdbcType=VARCHAR}, '%')
          and tju.USER_ID = #{userId,jdbcType=INTEGER}
        group by tt.TRADER_ID
    </select>


    <select id="findTraderCustomerStatus" resultType="java.lang.Integer">
        SELECT x.STATUS
        FROM T_TRADER_CUSTOMER A
                 LEFT JOIN T_VERIFIES_INFO x ON x.RELATE_TABLE_KEY = A.TRADER_CUSTOMER_ID
            AND x.RELATE_TABLE = 'T_TRADER_CUSTOMER' and x.VERIFIES_TYPE = 617
        WHERE A.IS_ENABLE = 1
          AND x.`STATUS` = 1
          and A.TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
    </select>



    <select id="getTraderCustomerListByTraderIds" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        select
            B.TRADER_ID,A.TRADER_CUSTOMER_ID,A.CUSTOMER_NATURE,B.TRADER_NAME
        FROM
            T_TRADER_CUSTOMER A
                LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        where A.IS_ENABLE = 1 and B.TRADER_ID in
        <foreach collection="traderIds" close=")" open="(" separator="," item="traderId">
            #{traderId}
        </foreach>
    </select>

    <select id="getTraderScoreByTraderId" resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN D.TRADER_LEVEL LIKE 'S%' THEN 10
                WHEN D.TRADER_LEVEL LIKE 'A%' THEN 8
                WHEN D.TRADER_LEVEL LIKE 'B%' THEN 5
                WHEN D.TRADER_LEVEL LIKE 'C%' THEN 2
                ELSE 0
                END AS TRADER_SCORE
        FROM T_TRADER_CUSTOMER TTC
                 LEFT JOIN DWH_TRADER_LIST_FILTER_ERP D ON TTC.TRADER_CUSTOMER_ID = D.TRADER_CUSTOMER_ID
        WHERE TTC.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="getTraderByPayApply" resultType="com.vedeng.erp.trader.dto.TraderCustomerDto">
        SELECT
        TC.*
        FROM
        T_PAY_APPLY PA
        LEFT JOIN T_AFTER_SALES A ON PA.RELATED_ID = A.AFTER_SALES_ID
        LEFT JOIN T_SALEORDER S ON A.ORDER_ID = S.SALEORDER_ID
        LEFT JOIN T_TRADER_CUSTOMER TC ON S.TRADER_ID = TC.TRADER_ID
        WHERE
        PA.PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </select>



    <update id="updateTraderAmount">
        UPDATE T_TRADER_CUSTOMER A
        SET A.AMOUNT = A.AMOUNT + #{amount,jdbcType=DECIMAL},
        A.MOD_TIME = now()
        WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </update>

    <select id="getTraderCustomerByTraderName"
            resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo">
        SELECT A.TRADER_ID,
               B.TRADER_NAME
        FROM T_TRADER_CUSTOMER A
                 LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID

        where A.IS_ENABLE=1 AND B.COMPANY_ID=1
        <if test="keyword != null and keyword != ''">
            AND B.TRADER_NAME LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
        </if>
            limit #{start,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
    </select>
    <select id="countTraderCustomerByTraderName" resultType="java.lang.Long">
        SELECT  count(1)
        FROM T_TRADER_CUSTOMER A
                 LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID

        where A.IS_ENABLE=1 AND B.COMPANY_ID=1
           AND
            B.TRADER_NAME LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%' )
    </select>

    <select id="getTrader" resultType="com.vedeng.erp.system.dto.LikeTraderDto">
        SELECT
            t.TRADER_ID,
            t.TRADER_NAME,
            CASE
                WHEN t.WAREHOUSE_DETAIL_ADDRESS IS NOT NULL AND t.WAREHOUSE_DETAIL_ADDRESS != ''
                    AND v.STATUS IS NOT NULL AND v.STATUS = 1
                    THEN 1
                ELSE 0
                END AS riskPass
        FROM
            T_TRADER_CUSTOMER c
                LEFT JOIN T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY = c.TRADER_CUSTOMER_ID AND v.relate_table = 'T_CUSTOMER_APTITUDE'
                LEFT JOIN T_TRADER t ON c.TRADER_ID = t.TRADER_ID
        WHERE
            c.IS_ENABLE = 1
          AND t.IS_ENABLE = 1
          <if test="keyword != null and keyword != ''">
              AND t.TRADER_NAME LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
          </if>
        GROUP BY
            c.TRADER_ID
        ORDER BY
        riskPass desc ,TRADER_ID DESC
        limit 100
    </select>

    <select id="getTraderBelongList" resultType="com.vedeng.erp.trader.dto.TraderBelongDto">
        select a.TRADER_NAME, c.USERNAME as userName,c.NUMBER
        from T_TRADER a
        left join T_TRADER_CUSTOMER tc on tc.TRADER_ID = a.TRADER_ID
        left join T_R_TRADER_J_USER b on a.TRADER_ID = b.TRADER_ID
        left join T_USER c on b.USER_ID = c.USER_ID
        where a.IS_ENABLE = 1 and b.TRADER_TYPE = 1 and tc.IS_ENABLE = 1 and a.TRADER_NAME in
        <foreach collection="traderNameList" item="traderName" separator="," open="(" close=")">
            #{traderName}
        </foreach>
    </select>

        <select id="getTraderBelong" resultType="com.vedeng.erp.trader.dto.TraderBelongDto">
        select a.TRADER_NAME, c.USERNAME as userName,c.NUMBER
        from T_TRADER a
        left join T_TRADER_CUSTOMER tc on tc.TRADER_ID = a.TRADER_ID
        left join T_R_TRADER_J_USER b on a.TRADER_ID = b.TRADER_ID
        left join T_USER c on b.USER_ID = c.USER_ID
        where a.IS_ENABLE = 1 and b.TRADER_TYPE = 1 and tc.IS_ENABLE = 1 and a.TRADER_ID =  #{traderId,jdbcType=INTEGER}
    </select>

    <select id="queryTraderCustomerInfoByNameAndSubUserId"
            resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo">
        SELECT A.TRADER_ID,A.TRADER_CUSTOMER_CATEGORY_ID,A.TRADER_CUSTOMER_ID,
        B.TRADER_NAME,
        CASE when TYC.NAME IS NOT NULL THEN 'Y'
        ELSE 'N' END AS "tycFlag",
        A.CUSTOMER_TYPE,
        D.TITLE AS customerTypeStr,
        A.CUSTOMER_NATURE,
        E.TITLE AS customerNatureStr,
        B.AREA_ID,
        B.AREA_IDS,
        A.CUSTOMER_LEVEL,
        C.TITLE AS customerLevelStr,
        A.REGISTERED_CAPITAL,
        A.REGISTERED_DATE,
        DATE_FORMAT(A.REGISTERED_DATE, '%Y-%m-%d') AS REGISTERED_DATE_STR,
        A.AMOUNT,
        A.OWNERSHIP,
        F.USER_ID saleId,
        G.USERNAME saleName
        FROM T_TRADER_CUSTOMER A
        LEFT JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID <!-- AND B.IS_ENABLE = 1 -->
        LEFT JOIN T_TRADER_INFO_TYC TYC ON B.TRADER_NAME=TYC.NAME
        LEFT JOIN T_SYS_OPTION_DEFINITION C
        ON A.CUSTOMER_LEVEL = C.SYS_OPTION_DEFINITION_ID AND C.PARENT_ID = 11
        LEFT JOIN T_SYS_OPTION_DEFINITION D
        ON A.CUSTOMER_TYPE = D.SYS_OPTION_DEFINITION_ID AND D.PARENT_ID = 425
        LEFT JOIN T_SYS_OPTION_DEFINITION E
        ON     A.CUSTOMER_NATURE = E.SYS_OPTION_DEFINITION_ID
        AND E.PARENT_ID = 464
        LEFT JOIN T_R_TRADER_J_USER F  on F.TRADER_ID = A.TRADER_ID and F.TRADER_TYPE = 1
        LEFT JOIN T_USER G ON F.USER_ID = G.USER_ID
        where 1=1 AND A.IS_ENABLE = 1
        and  G.USER_ID in
        <foreach collection="subUserIds" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
        <if test="notIncludeTraderIds != null and notIncludeTraderIds.size() != 0">
            AND A.TRADER_ID NOT IN
            <foreach collection="notIncludeTraderIds" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="name!=null and name!=''">
            AND B.TRADER_NAME LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%' )
        </if>
        group by A.TRADER_CUSTOMER_ID
        limit #{limit,jdbcType=INTEGER}
    </select>
</mapper>
