<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>

<div class="parts" liname="审核记录" id="审核记录">
    <div class="title-container">
        <div class="table-title nobor">
            审核记录
        </div>
    </div>
    <div class="customer">
        <ul>
            <li>
                <a id="audit_type_1" class="customer-color" onclick="getAuditRecordOfSaleOrder(1,${saleorder.saleorderId})">
                    订单审核<span hidden="hidden" style="color: red">【审】</span>
                </a>
            </li>
            <li>
                <a id="audit_type_2" onclick="getAuditRecordOfSaleOrder(2,${saleorder.saleorderId})">
                    合同回传审核<span hidden="hidden" style="color: red">【审】</span>
                </a>
            </li>
        </ul>
    </div>
    <table class="table">
        <thead>
        <tr>
            <th>操作人</th>
            <th>操作时间</th>
            <th>操作事项</th>
            <th>备注</th>
        </tr>
        </thead>
        <tbody id="audit_tbody">

        </tbody>
    </table>
</div>

<script>
    function getAuditRecordOfSaleOrder(auditType,saleOrderId){
        for (let i = 1; i <= 4; i++) {
            if (auditType === i){
                $('#audit_type_' + i).addClass('customer-color');
            } else {
                $('#audit_type_' + i).removeClass('customer-color')
            }
        }

        $.ajax({
            type: 'GET',
            url: '/orderstream/saleorder/audit_record.do?auditType=' + auditType + '&saleOrderId=' + saleOrderId,
            dataType: 'JSON',
            success: function (res){
                const data = res.data.auditRecordInstanceVoList;
                const checkStatusList = res.data.checkStatusList;
                const tbody = document.getElementById("audit_tbody");
                let tdHtml = "";
                if (res.code === 0 && data.length > 0){
                    for (let i = 0; i < data.length; i++) {
                        tdHtml += '<tr>';
                        tdHtml += '<td>' + parseNull(data[i].operatorName) + '</td>';
                        tdHtml += '<td>' + parseNull(data[i].operateTime) + '</td>';
                        tdHtml += '<td>' + parseNull(data[i].operateInstance) + '</td>';
                        tdHtml += '<td>' + parseNull(data[i].comment) + '</td>';
                        tdHtml += '</tr>';
                    }
                } else {
                    tdHtml += '<tr><td colspan="4">暂无审核记录。</td></tr>';
                }
                tbody.innerHTML = tdHtml;

                for (let i = 0; i < checkStatusList.length; i++) {
                    if (checkStatusList[i] !== 0){
                        $('#audit_type_' + (i + 1)).find('span').attr('hidden','hidden')
                    } else {
                        $('#audit_type_' + (i + 1)).find('span').removeAttr('hidden')
                    }
                }
            },
            error: function (error){
                const tbody = document.getElementById("audit_tbody");
                tbody.innerHTML = '<tr><td colspan="4">暂无审核记录。</td></tr>';
            }
        })
    }

    function parseNull(data){
        if (typeof data === 'undefined' || data == null){
            return "";
        }
        return data;
    }

    window.onload = function () {
        getAuditRecordOfSaleOrder(1,'${saleorder.saleorderId}');
    }
</script>