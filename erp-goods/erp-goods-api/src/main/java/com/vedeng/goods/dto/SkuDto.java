package com.vedeng.goods.dto;

import com.vedeng.erp.system.dto.UserDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SkuDto {

    /**
     * 基础信息
     */
    private BasicInfo basicInfo = new BasicInfo();

    /**
     * 主要参数
     */
    private MainParameters mainParameters = new MainParameters();

    /**
     * 价格信息
     */
    private PriceInfo priceInfo = new PriceInfo();

    /**
     * 售后信息
     */
    private AfterSaleInfo afterSaleInfo = new AfterSaleInfo();

    /**
     * 产品状态
     */
    private ProductStatus productStatus = new ProductStatus();

    /**
     * 产品经理
     */
    private UserDto productManager;

    /**
     * 产品助理
     */
    private UserDto productAssistant;

    /**
     * 基础信息类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BasicInfo {
        /**
         * SKU ID
         */
        private Integer skuId;

        /**
         * 订货号
         */
        private String skuNo;

        /**
         * 产品名称
         */
        private String skuName;

        /**
         * 品牌名称
         */
        private String brandName;

        /**
         * 型号或规格
         */
        private String modelOrSpec;

        /**
         * 产品图片URL，无图片时显示默认图片
         */
        private String imageUrl;
    }

    /**
     * 主要参数类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MainParameters {
        /**
         * 主要参数列表，最多显示前6条
         */
        private List<String> mainParam;
    }

    /**
     * 价格信息类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PriceInfo {
        /**
         * 经销价
         */
        private BigDecimal distributionPrice;

        /**
         * 终端价
         */
        private BigDecimal terminalPrice;

        /**
         * 核价状态 是否通过
         * <p>
         * 如“审核通过”显示终端价、经销价，否则显示“未核价”
         */
        private Integer priceStatus;
    }

    /**
     * 售后信息类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AfterSaleInfo {
        /**
         * 保修政策，如主机保修期
         */
        private String warrantyInfo;

        /**
         * 使用年限或效期
         */
        private String useLife;

        /**
         * 是否提供上门安装服务
         */
        private Integer isInstall;
    }

    /**
     * 产品状态类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProductStatus {
        /**
         * 审核状态
         * 审核状态 0待完善 1审核中 2审核不通过 3审核通过 4删除 5 待提交审核
         */
        private Integer auditStatus;

        /**
         * 商品等级
         */
        private Integer goodsLevelNo;

        private String goodsLevelNoLabel;

        /**
         * 商品挡位
         */
        private Integer goodsPositionNo;

        private String goodsPositionNoLabel;
    }


}