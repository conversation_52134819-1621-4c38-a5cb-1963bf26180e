<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.OrderPaymentDetailsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.OrderPaymentDetails">
    <!--@mbg.generated-->
    <!--@Table T_ORDER_PAYMENT_DETAILS-->
    <id column="T_ORDER_PAYMENT_DETAILS_ID" jdbcType="BIGINT" property="tOrderPaymentDetailsId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
    <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
    <result column="PAYMENT_METHOD_TYPE" jdbcType="INTEGER" property="paymentMethodType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    T_ORDER_PAYMENT_DETAILS_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    ORDER_ID, ORDER_TYPE, PAYMENT_METHOD_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_ORDER_PAYMENT_DETAILS
    where T_ORDER_PAYMENT_DETAILS_ID = #{tOrderPaymentDetailsId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_ORDER_PAYMENT_DETAILS
    where T_ORDER_PAYMENT_DETAILS_ID = #{tOrderPaymentDetailsId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="T_ORDER_PAYMENT_DETAILS_ID" keyProperty="tOrderPaymentDetailsId" parameterType="com.vedeng.erp.buyorder.domain.entity.OrderPaymentDetails" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORDER_PAYMENT_DETAILS (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      ORDER_ID, ORDER_TYPE, PAYMENT_METHOD_TYPE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=INTEGER}, #{orderType,jdbcType=BOOLEAN}, #{paymentMethodType,jdbcType=BOOLEAN}
      )
  </insert>
  <insert id="insertSelective" keyColumn="T_ORDER_PAYMENT_DETAILS_ID" keyProperty="tOrderPaymentDetailsId" parameterType="com.vedeng.erp.buyorder.domain.entity.OrderPaymentDetails" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORDER_PAYMENT_DETAILS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="paymentMethodType != null">
        PAYMENT_METHOD_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=BOOLEAN},
      </if>
      <if test="paymentMethodType != null">
        #{paymentMethodType,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.OrderPaymentDetails">
    <!--@mbg.generated-->
    update T_ORDER_PAYMENT_DETAILS
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
      </if>
      <if test="paymentMethodType != null">
        PAYMENT_METHOD_TYPE = #{paymentMethodType,jdbcType=BOOLEAN},
      </if>
    </set>
    where T_ORDER_PAYMENT_DETAILS_ID = #{tOrderPaymentDetailsId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.OrderPaymentDetails">
    <!--@mbg.generated-->
    update T_ORDER_PAYMENT_DETAILS
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
      PAYMENT_METHOD_TYPE = #{paymentMethodType,jdbcType=BOOLEAN}
    where T_ORDER_PAYMENT_DETAILS_ID = #{tOrderPaymentDetailsId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_ORDER_PAYMENT_DETAILS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.orderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ORDER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.orderType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_METHOD_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.paymentMethodType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
    </trim>
    where T_ORDER_PAYMENT_DETAILS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.tOrderPaymentDetailsId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_ORDER_PAYMENT_DETAILS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderId != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.orderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderType != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.orderType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_METHOD_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentMethodType != null">
            when T_ORDER_PAYMENT_DETAILS_ID = #{item.tOrderPaymentDetailsId,jdbcType=BIGINT} then #{item.paymentMethodType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
    </trim>
    where T_ORDER_PAYMENT_DETAILS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.tOrderPaymentDetailsId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="T_ORDER_PAYMENT_DETAILS_ID" keyProperty="tOrderPaymentDetailsId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORDER_PAYMENT_DETAILS
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ORDER_ID, ORDER_TYPE, 
      PAYMENT_METHOD_TYPE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.orderId,jdbcType=INTEGER}, #{item.orderType,jdbcType=BOOLEAN}, #{item.paymentMethodType,jdbcType=BOOLEAN}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="T_ORDER_PAYMENT_DETAILS_ID" keyProperty="tOrderPaymentDetailsId" parameterType="com.vedeng.erp.buyorder.domain.entity.OrderPaymentDetails" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORDER_PAYMENT_DETAILS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tOrderPaymentDetailsId != null">
        T_ORDER_PAYMENT_DETAILS_ID,
      </if>
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
      ORDER_ID,
      ORDER_TYPE,
      PAYMENT_METHOD_TYPE,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tOrderPaymentDetailsId != null">
        #{tOrderPaymentDetailsId,jdbcType=BIGINT},
      </if>
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
      #{orderId,jdbcType=INTEGER},
      #{orderType,jdbcType=BOOLEAN},
      #{paymentMethodType,jdbcType=BOOLEAN},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="tOrderPaymentDetailsId != null">
        T_ORDER_PAYMENT_DETAILS_ID = #{tOrderPaymentDetailsId,jdbcType=BIGINT},
      </if>
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=INTEGER},
      ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
      PAYMENT_METHOD_TYPE = #{paymentMethodType,jdbcType=BOOLEAN},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="T_ORDER_PAYMENT_DETAILS_ID" keyProperty="tOrderPaymentDetailsId" parameterType="com.vedeng.erp.buyorder.domain.entity.OrderPaymentDetails" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORDER_PAYMENT_DETAILS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tOrderPaymentDetailsId != null">
        T_ORDER_PAYMENT_DETAILS_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="paymentMethodType != null">
        PAYMENT_METHOD_TYPE,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tOrderPaymentDetailsId != null">
        #{tOrderPaymentDetailsId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=BOOLEAN},
      </if>
      <if test="paymentMethodType != null">
        #{paymentMethodType,jdbcType=BOOLEAN},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="tOrderPaymentDetailsId != null">
        T_ORDER_PAYMENT_DETAILS_ID = #{tOrderPaymentDetailsId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
      </if>
      <if test="paymentMethodType != null">
        PAYMENT_METHOD_TYPE = #{paymentMethodType,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  
<!--auto generated by MybatisCodeHelper on 2024-09-09-->
  <select id="findByOrderIdAndOrderTypeAndPaymentMethodType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_ORDER_PAYMENT_DETAILS
    where ORDER_ID=#{orderId,jdbcType=INTEGER} and ORDER_TYPE=#{orderType,jdbcType=INTEGER} and
    PAYMENT_METHOD_TYPE=#{paymentMethodType,jdbcType=INTEGER}
  </select>
</mapper>