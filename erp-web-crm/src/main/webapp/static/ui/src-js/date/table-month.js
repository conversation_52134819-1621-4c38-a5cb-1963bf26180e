// 月份表格
Vue.component('ui-month-table', {
    template: `
        <table @click="handleMonthTableClick" class="vd-ui-month-table">
            <tbody>
                <tr v-for="(row, key) in rows" :key="key">
                    <td :class="getCellStyle(cell)" v-for="(cell, key) in row" :key="key">
                        <div>
                            <a class="cell">{{ monthsShow[cell.text] }}</a>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    `,

    props: {
        disabledDate: {},
        value: {},
        selectionMode: {
            default: 'month'
        },
        defaultValue: {
            validator(val) {
                // null or valid Date Object
                return val === null || util_date.isDate(val) || (Array.isArray(val) && val.every(util_date.isDate));
            }
        },
        date: {},
    },

    data() {
        return {
            months: ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
            monthsShow: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
            tableRows: [ [], [], [] ],
            lastRow: null,
            lastColumn: null
        };
    },

    computed: {
        rows() {
            // TODO: refactory rows / getCellClasses
            const rows = this.tableRows;
            const disabledDate = this.disabledDate;
            const selectedDate = [];
            const now = this.getMonthTimestamp(new Date());

            for (let i = 0; i < 3; i++) {
                const row = rows[i];
                for (let j = 0; j < 4; j++) {
                    let cell = row[j];
                    if (!cell) {
                        cell = { row: i, column: j, type: 'normal', inRange: false, start: false, end: false };
                    }

                    cell.type = 'normal';

                    const index = i * 4 + j;
                    const time = new Date(this.date.getFullYear(), index).getTime();
                    // cell.inRange = time >= this.getMonthTimestamp(this.minDate) && time <= this.getMonthTimestamp(this.maxDate);
                    // cell.start = this.minDate && time === this.getMonthTimestamp(this.minDate);
                    // cell.end = this.maxDate && time === this.getMonthTimestamp(this.maxDate);
                    const isToday = time === now;

                    if (isToday) {
                        cell.type = 'today';
                    }
                    cell.text = index;
                    let cellDate = new Date(time);
                    cell.disabled = typeof disabledDate === 'function' && disabledDate(cellDate);
                    cell.selected = util_date.arrayFind(selectedDate, date => date.getTime() === cellDate.getTime());

                    this.$set(row, j, cell);
                }
            }
            return rows;
        }
    },

    methods: {
        datesInMonth (year, month) {
            const numOfDays = util_date.getDayCountOfMonth(year, month);
            const firstDay = new Date(year, month, 1);
            return util_date.range(numOfDays).map(n => util_date.nextDate(firstDay, n));
        },
        clearDate (date) {
            return new Date(date.getFullYear(), date.getMonth());
        },
        getMonthTimestamp (time) {
            if (typeof time === 'number' || typeof time === 'string') {
                return this.clearDate(new Date(time)).getTime();
            } else if (time instanceof Date) {
                return this.clearDate(time).getTime();
            } else {
                return NaN;
            }
        },

        cellMatchesDate(cell, date) {
            const value = new Date(date);
            return this.date.getFullYear() === value.getFullYear() && Number(cell.text) === value.getMonth();
        },
        getCellStyle(cell) {
            const style = {};
            const year = this.date.getFullYear();
            const today = new Date();
            const month = cell.text;
            const defaultValue = this.defaultValue ? Array.isArray(this.defaultValue) ? this.defaultValue : [this.defaultValue] : [];
            style.disabled = typeof this.disabledDate === 'function'
                ? this.datesInMonth(year, month).every(this.disabledDate)
                : false;
            style.current = util_date.arrayFindIndex(util_date.coerceTruthyValueToArray(this.value), date => date.getFullYear() === year && date.getMonth() === month) >= 0;
            style.today = today.getFullYear() === year && today.getMonth() === month;
            style.default = defaultValue.some(date => this.cellMatchesDate(cell, date));

            if (cell.inRange) {
                style['in-range'] = true;

                if (cell.start) {
                    style['start-date'] = true;
                }

                if (cell.end) {
                    style['end-date'] = true;
                }
            }
            return style;
        },
        getMonthOfCell(month) {
            const year = this.date.getFullYear();
            return new Date(year, month, 1);
        },

        handleMonthTableClick (event) {
            let target = event.target;
            if (target.tagName === 'A') {
                target = target.parentNode.parentNode;
            }
            if (target.tagName === 'DIV') {
                target = target.parentNode;
            }
            if (target.tagName !== 'TD') return;
            if (util_date.hasClass(target, 'disabled')) return;


            const column = target.cellIndex;
            const row = target.parentNode.rowIndex;
            const month = row * 4 + column;
            this.$emit('pick', month);
        }
    },
})