package com.vedeng.todolist.service.impl;

import com.vedeng.common.util.DateUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.ITodoInstance;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 质量部审核客户资质
 * @Author: daniel
 * @Date: 2020/12/11 09 43
 * @Description:
 */
@Service
public class ZlbCheckTraderCustomerCertificate implements ITodoInstance {

    private static final Logger logger = LoggerFactory.getLogger(ZlbCheckTraderCustomerCertificate.class);

    @Resource
    private TodoListMapper todoListMapper;

    /**
     * @param buzId 业务id
     * @param buzExtra 业务冗余字段
     * @param comment 备注
     * @param buzProperty
     */
    @Override
    public synchronized void add(Integer buzId, String buzExtra, String comment, String buzProperty){
        logger.info("待办事项---质量部审核客户资质，buzId:{}",buzId);
        if (todoListMapper.selectUnHandledByBuzTypeAndBuzId(TodoListBuzSceneEnum.ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(), buzId).size() > 0) {
            todoListMapper.deleteByBuzTypeAndBuzId(TodoListBuzSceneEnum.ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(),buzId);
        }
        TodoList instance = new TodoList(TodoListBuzSceneEnum.ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE,buzId,buzExtra,buzProperty,comment,
                DateUtil.sysTimeMillis(),2);
        todoListMapper.insertSelective(instance);
    }

    @Override
    public void finish(Integer buzId){
        todoListMapper.updateStatusByBuzTypeAndBuzId(TodoListBuzSceneEnum.ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(),buzId,1,
                                                    getCurrentRequestUser().getUserId(),
                                                    System.currentTimeMillis());
        logger.info("待办事项---质量部审核客户资质，buzId;{}更新为已完成",buzId);
    }

    public void finish(Integer buzId, Integer checkStatus){
        todoListMapper.finishZlbTodoList(TodoListBuzSceneEnum.ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(), buzId,checkStatus,DateUtil.sysTimeMillis());
        logger.info("待办事项---质量部审核客户资质，buzId;{}更新为已完成",buzId);
    }

    /**
     * 当客户资质审核重置时，删除掉原有客户资质质量部审核待办事项
     * @param buzId 客户id，TRADER_CUSTOMER_ID
     */
    public void delete(Integer buzId){
        todoListMapper.deleteByBuzTypeAndBuzId(TodoListBuzSceneEnum.ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(),buzId);
    }
}
