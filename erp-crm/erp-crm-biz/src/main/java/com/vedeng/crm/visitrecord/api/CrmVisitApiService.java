package com.vedeng.crm.visitrecord.api;

import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/13
 */
public interface CrmVisitApiService {


    void SendMessageForCheckBusinessChance(String bussinessChanceNo,String closeUserName,String closeReason);

    void SendMessageForCheckLeads(String leadsNo,String closeUserName,String closeReason );


    /**
     * 根据关联编号和关联类型查询拜访计划记录
     * @param relateId
     * @param relateType 1线索2商机
     * @return
     */
    List<VisitRecordVo> selectVisitRecordByRelateId(Integer relateId,Integer relateType);

}
