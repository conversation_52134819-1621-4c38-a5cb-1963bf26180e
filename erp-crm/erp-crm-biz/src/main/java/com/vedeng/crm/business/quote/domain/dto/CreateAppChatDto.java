package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CreateAppChatDto {

    /**
     * 报价单id
     */
    @NotNull(message = "报价单id不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    /**
     * 群成员ID
     */
    @NotEmpty(message = "群成员ID不能为空", groups = DefaultGroup.class)
    private List<Integer> userIds;
}
