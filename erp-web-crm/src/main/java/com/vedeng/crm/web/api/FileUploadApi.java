package com.vedeng.crm.web.api;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.service.OperationLogApiService;
import com.vedeng.infrastructure.file.domain.GlobalFileDto;
import com.vedeng.infrastructure.file.domain.GlobalFileRequest;
import com.vedeng.infrastructure.file.manager.GlobalFileService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import com.vedeng.infrastructure.wxapp.utils.WxHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * <AUTHOR>
 * @Version V1.0.0
 * @menu 公共分类
 * @Date 2024/7/18
 */
@ExceptionController
@RestController
@RequestMapping("/crm/common/public")
@Slf4j
public class FileUploadApi {

    public static final int FILE_MAX_SIZE = 1024 * 1024 * 10;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private GlobalFileService globalFileService;

    @Autowired
    private OperationLogApiService operationLogApiService;

    public static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex != -1 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1);
        } else {
            return "";
        }
    }

    /**
     * 上传文件
     */
    @RequestMapping("/uploadFile")
    public FileInfo uploadFile(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        return ossUtilsService.upload2Oss(request, file);
    }

    /**
     * 根据企业微信的mediaId来获取文件
     * @param mediaId
     * @return
     */
    @RequestMapping("/uploadWxFileByMediaId")
    public FileInfo uploadFile(@RequestParam("mediaId") String mediaId) {
        try{
            File destFile = WxHelper.downloadFile(mediaId);
            FileInputStream inputStream = new FileInputStream(destFile);
            String extension = getFileExtension(destFile.getName());
            String fileUrl = ossUtilsService.upload2OssForInputStream(extension, destFile.getName(), inputStream);
            if(StringUtils.isNotBlank(fileUrl)){
                FileInfo fileInfo = new FileInfo();
                fileInfo.setCode(0);
                fileInfo.setHttpUrl(fileUrl);
                return fileInfo;
            }else{
                return new FileInfo(-1, "文件获取失败");
            }
        }catch (Exception e){
            log.error("文件获取失败");
            return new FileInfo(-1, "文件获取失败");
        }
    }

    /**
     * 基于业务上传文件
     */
    @RequestMapping("/uploadBusinessFile")
    public R<GlobalFileDto> uploadBusinessFile(GlobalFileRequest globalFileRequest) {

        ValidatorUtils.validate(globalFileRequest, AddGroup.class);

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser.getId()<=1) {
            return R.error("请先登录");
        }
        List<GlobalFileDto> globalFileDtos = globalFileService.get(globalFileRequest);

        // 判断文件上传上限
        if (globalFileDtos.size() >= 20) {
            return R.error(50001, "上传文件数量不能超过20个");
        }

        // 判断文件不能大于10M
        if (globalFileRequest.getFile().getSize() > FILE_MAX_SIZE) {
            return R.error("上传文件不能超过10M");
        }

        GlobalFileDto globalFileDto = globalFileService.upload(globalFileRequest);
        return R.success(globalFileDto);
    }

    /**
     * 上传文件保存操作记录
     */
    @RequestMapping("/saleUploadFileLog")
    public R<Void> saleUploadFileLog(@RequestBody List<GlobalFileRequest> globalFileRequestList) {
        if (CollUtil.isEmpty(globalFileRequestList)) {
            return R.error("上传文件不能为空");
        }

        GlobalFileRequest globalFileRequest = CollUtil.getFirst(globalFileRequestList);
        if (globalFileRequest.getAttachmentId() == null) {
            return R.error("上传文件ID不能为空");
        }

        GlobalFileDto fileDto = globalFileService.getById(globalFileRequest.getAttachmentId());
        String fileName = generateFileName(globalFileRequestList, fileDto);
        OperationLogDto operationLogDto = createOperationLogDto(globalFileRequest, fileName);
        operationLogApiService.save(operationLogDto, BizLogEnum.UPLOAD_FILE);

        return R.success();
    }

    /**
     * 删除文件
     */
    @RequestMapping("/deleteFile")
    public R<Void> deleteFile(@RequestBody List<GlobalFileRequest> globalFileRequestList) {
        if (CollUtil.isEmpty(globalFileRequestList)) {
            return R.error("上传文件ID不能为空");
        }

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser.getId()<=1) {
            return R.error("请先登录");
        }

        globalFileRequestList.forEach(globalFileRequest -> globalFileService.delete(globalFileRequest.getAttachmentId()));

        GlobalFileRequest globalFileRequest = CollUtil.getFirst(globalFileRequestList);
        GlobalFileDto fileDto = globalFileService.getById(globalFileRequest.getAttachmentId());
        String fileName = generateFileName(globalFileRequestList, fileDto);
        OperationLogDto operationLogDto = createOperationLogDto(globalFileRequest, fileName);
        operationLogApiService.save(operationLogDto, BizLogEnum.DELETE_FILE);

        return R.success();
    }

    /**
     * 下载文件
     */
    @RequestMapping("/downloadFile")
    public R<Void> downloadFile(GlobalFileRequest globalFileRequest) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser.getId()<=1) {
            return R.error("请先登录");
        }
        GlobalFileDto fileDto = globalFileService.getById(globalFileRequest.getAttachmentId());
        OperationLogDto operationLogDto = createOperationLogDto(globalFileRequest, fileDto.getName());
        operationLogApiService.save(operationLogDto, BizLogEnum.DOWNLOAD_FILE);
        return R.success();
    }

    /**
     * 按业务类型获取文件
     */
    @RequestMapping("/getFile")
    public R<List<GlobalFileDto>> getFile(GlobalFileRequest globalFileRequest) {
        return R.success(globalFileService.get(globalFileRequest));
    }

    /**
     * 生成文件名
     *
     * @param globalFileRequestList 文件请求列表
     * @param fileDto               文件DTO
     * @return 文件名
     */
    private String generateFileName(List<GlobalFileRequest> globalFileRequestList, GlobalFileDto fileDto) {
        if (globalFileRequestList.size() == 1) {
            return fileDto.getName();
        } else {
            return fileDto.getName() + "等" + globalFileRequestList.size() + "个文件";
        }
    }

    /**
     * 创建操作日志DTO
     *
     * @param globalFileRequest 文件请求对象
     * @param fileName          文件名
     * @return 操作日志DTO
     */
    private OperationLogDto createOperationLogDto(GlobalFileRequest globalFileRequest, String fileName) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        OperationLogDto logDto = new OperationLogDto();
        Map<String, String> params = new HashMap<>();
        params.put("fileName", fileName);
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        logDto.setBizId(globalFileRequest.getBizId());
        return logDto;
    }
}
