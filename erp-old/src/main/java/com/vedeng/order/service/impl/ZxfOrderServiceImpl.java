package com.vedeng.order.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.*;
import com.vedeng.order.dao.ZxfOrderMapper;
import com.vedeng.order.model.*;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.order.service.ZxfOrderService;
import com.vedeng.system.service.UserService;
import org.aspectj.weaver.ast.Or;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;


@Service("zxfOrderService")
public class ZxfOrderServiceImpl extends BaseServiceimpl implements ZxfOrderService {

	@Autowired
	@Qualifier("userService")
	private UserService userService;

	@Autowired
	@Qualifier("zxfOrderMapper")
	protected ZxfOrderMapper zxfOrderMapper;
	@Resource
	private OrderCommonService orderCommonService;
	@Resource
	private OrderNoDict orderNoDict;

	@Transactional
	@Override
	public Saleorder saveAddSaleorderInfo(Saleorder saleorder, HttpServletRequest request, HttpSession session) {
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();

		saleorder.setAddTime(time);
		saleorder.setCreator(user.getUserId());
		saleorder.setCreatorOrgId(user.getOrgId());
		saleorder.setCreatorOrgName(user.getOrgName());
		saleorder.setCompanyId(user.getCompanyId());
		saleorder.setOrderType(9);
		saleorder.setStatus(0);
		// saleorder.setOrgId(user.getOrgId());
		// saleorder.setUserId(user.getUserId());

		// 归属销售
		User belongUser = new User();
		if (saleorder.getTraderId() != null) {
			belongUser = userService.getUserInfoByTraderId(saleorder.getTraderId(), 1);// 1客户，2供应商
			if (belongUser != null && belongUser.getUserId() != null) {
				saleorder.setUserId(belongUser.getUserId());
			}
			if (belongUser != null && belongUser.getOrgId() != null) {
				saleorder.setOrgId(belongUser.getOrgId());
			}
			if (belongUser != null && belongUser.getOrgName() != null) {
				saleorder.setOrgName(belongUser.getOrgName());
			}
		}

		Saleorder res = saveAddzxfSaleorderInfo(saleorder);
		if(res != null){
			//VDERP-2263   订单售后采购改动通知
			orderCommonService.updateSaleOrderDataUpdateTime(res.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_GENERATE);
		}
		return res;

	}


	public Saleorder saveAddzxfSaleorderInfo(Saleorder saleorder) {
		// 新增订单主信息
		Integer i = zxfOrderMapper.insertSelective(saleorder);
		Integer saleorderId = saleorder.getSaleorderId();
		if (i == 1) {
			Saleorder saleorderExtra = zxfOrderMapper.getBaseSaleorderInfo(saleorderId);
			saleorderExtra.setSaleorderId(saleorderId);
			saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(saleorderId, 25));
			saleorderExtra.setTakeTraderId(saleorderExtra.getTraderId());
			saleorderExtra.setTakeTraderName(saleorderExtra.getTraderName());
			saleorderExtra.setTakeTraderContactId(saleorderExtra.getTraderContactId());
			saleorderExtra.setTakeTraderContactName(saleorderExtra.getTraderContactName());
			saleorderExtra.setTakeTraderContactMobile(saleorderExtra.getTraderContactMobile());
			saleorderExtra.setTakeTraderContactTelephone(saleorderExtra.getTraderContactTelephone());
			saleorderExtra.setTakeTraderAddressId(saleorderExtra.getTraderAddressId());
			saleorderExtra.setInvoiceTraderId(saleorderExtra.getTraderId());
			saleorderExtra.setInvoiceTraderName(saleorderExtra.getTraderName());
			saleorderExtra.setInvoiceTraderContactId(saleorderExtra.getTraderContactId());
			saleorderExtra.setInvoiceTraderContactName(saleorderExtra.getTraderContactName());
			saleorderExtra.setInvoiceTraderContactMobile(saleorderExtra.getTraderContactMobile());
			saleorderExtra.setInvoiceTraderContactTelephone(saleorderExtra.getTraderContactTelephone());
			saleorderExtra.setInvoiceTraderAddressId(saleorderExtra.getTraderAddressId());
			saleorderExtra.setCreateMobile(saleorderExtra.getTraderContactMobile());
			zxfOrderMapper.updateByPrimaryKeySelective(saleorderExtra);
			return saleorderExtra;
		} else {
			return null;
		}
	}

}