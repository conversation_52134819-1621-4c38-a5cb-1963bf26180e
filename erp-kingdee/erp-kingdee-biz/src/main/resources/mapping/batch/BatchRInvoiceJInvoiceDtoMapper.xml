<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceJInvoiceDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceJInvoiceDto">
    <!--@mbg.generated-->
    <!--@Table T_R_INVOICE_J_INVOICE-->
    <id column="R_INVOICE_J_INVOICE_ID" jdbcType="INTEGER" property="rInvoiceJInvoiceId" />
    <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="RELATE_INVOICE_ID" jdbcType="INTEGER" property="relateInvoiceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_INVOICE_J_INVOICE_ID, INVOICE_ID, RELATE_INVOICE_ID
  </sql>

  <resultMap id="BaseMapAndOther" type="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE-->
    <id column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId"/>
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
    <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode"/>
    <result column="INVOICE_FLOW" jdbcType="VARCHAR" property="invoiceFlow"/>
    <result column="INVOICE_PROPERTY" jdbcType="INTEGER" property="invoiceProperty"/>
    <result column="INVOICE_HREF" jdbcType="VARCHAR" property="invoiceHref"/>
    <result column="TYPE" jdbcType="INTEGER" property="type"/>
    <result column="TAG" jdbcType="INTEGER" property="tag"/>
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId"/>
    <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo"/>
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType"/>
    <result column="RATIO" jdbcType="DECIMAL" property="ratio"/>
    <result column="COLOR_TYPE" jdbcType="INTEGER" property="colorType"/>
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
    <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable"/>
    <result column="VALID_USERID" jdbcType="INTEGER" property="validUserid"/>
    <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime"/>
    <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments"/>
    <result column="INVOICE_PRINT_STATUS" jdbcType="INTEGER" property="invoicePrintStatus"/>
    <result column="INVOICE_CANCEL_STATUS" jdbcType="INTEGER" property="invoiceCancelStatus"/>
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId"/>
    <result column="IS_AUTH" jdbcType="INTEGER" property="isAuth"/>
    <result column="IS_MONTH_AUTH" jdbcType="INTEGER" property="isMonthAuth"/>
    <result column="AUTH_TIME" jdbcType="BIGINT" property="authTime"/>
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
    <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
    <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId"/>
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime"/>
    <result column="AFTER_EXPRESS_ID" jdbcType="INTEGER" property="afterExpressId"/>
    <result column="OSS_FILE_URL" jdbcType="VARCHAR" property="ossFileUrl"/>
    <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId"/>
    <result column="INVOICE_FROM" jdbcType="TINYINT" property="invoiceFrom"/>
    <result column="HX_INVOICE_ID" jdbcType="INTEGER" property="hxInvoiceId"/>
    <result column="AUTH_MODE" jdbcType="INTEGER" property="authMode"/>
    <result column="AUTH_FAIL_REASON" jdbcType="VARCHAR" property="authFailReason"/>
    <result column="IS_AUTHING" jdbcType="INTEGER" property="isAuthing"/>
    <result column="COLOR_COMPLEMENT_TYPE" jdbcType="INTEGER" property="colorComplementType"/>
    <result column="IS_AFTER_BUYORDER_ONLY" jdbcType="INTEGER" property="isAfterBuyorderOnly"/>
    <result column="invoiceTypeName" jdbcType="VARCHAR" property="invoiceTypeName"/>
  </resultMap>


  <!--auto generated by MybatisCodeHelper on 2022-12-06-->
  <select id="findOriginalBlueEnable" resultMap="BaseMapAndOther">
    select
    TI.INVOICE_ID,
    TI.COMPANY_ID,
    INVOICE_CODE,
    INVOICE_FLOW,
    INVOICE_PROPERTY,
    INVOICE_HREF,
    TYPE,
    TAG,
    TI.RELATED_ID,
    AFTER_SALES_ID,
    INVOICE_NO,
    TI.INVOICE_TYPE,
    COLOR_TYPE,
    TI.AMOUNT,
    TI.IS_ENABLE,
    VALID_USERID,
    TI.VALID_STATUS,
    TI.VALID_TIME,
    VALID_COMMENTS,
    INVOICE_PRINT_STATUS,
    INVOICE_CANCEL_STATUS,
    EXPRESS_ID,
    IS_AUTH,
    IS_MONTH_AUTH,
    AUTH_TIME,
    TI.ADD_TIME,
    TI.CREATOR,
    TI.MOD_TIME,
    TI.UPDATER,
    INVOICE_APPLY_ID,
    SEND_TIME,
    AFTER_EXPRESS_ID,
    OSS_FILE_URL,
    RESOURCE_ID,
    INVOICE_FROM,
    HX_INVOICE_ID,
    AUTH_MODE,
    AUTH_FAIL_REASON,
    IS_AUTHING,
    COLOR_COMPLEMENT_TYPE,
    TI.IS_AFTER_BUYORDER_ONLY,
    TSOD.COMMENTS ratio,
    TSOD.TITLE    invoiceTypeName
    FROM T_R_INVOICE_J_INVOICE TRIJI
    LEFT JOIN T_INVOICE TI ON TRIJI.INVOICE_ID = TI.INVOICE_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION TSOD ON TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
    LEFT JOIN T_INVOICE_VOUCHER TIV ON TIV.INVOICE_ID = TI.INVOICE_ID and TIV.IS_DELETE = 0
    where TRIJI.RELATE_INVOICE_ID=#{relateInvoiceId,jdbcType=INTEGER}
    AND TI.COLOR_TYPE = 2 AND IS_ENABLE = 1
    AND TI.COMPANY_ID =1
    AND TIV.INVOICE_VOUCHER_ID is not null
    union all
    select
    TI.INVOICE_ID,
    TI.COMPANY_ID,
    INVOICE_CODE,
    INVOICE_FLOW,
    INVOICE_PROPERTY,
    INVOICE_HREF,
    TYPE,
    TAG,
    TI.RELATED_ID,
    AFTER_SALES_ID,
    INVOICE_NO,
    TI.INVOICE_TYPE,
    COLOR_TYPE,
    TI.AMOUNT,
    TI.IS_ENABLE,
    VALID_USERID,
    TI.VALID_STATUS,
    TI.VALID_TIME,
    VALID_COMMENTS,
    INVOICE_PRINT_STATUS,
    INVOICE_CANCEL_STATUS,
    EXPRESS_ID,
    IS_AUTH,
    IS_MONTH_AUTH,
    AUTH_TIME,
    TI.ADD_TIME,
    TI.CREATOR,
    TI.MOD_TIME,
    TI.UPDATER,
    INVOICE_APPLY_ID,
    SEND_TIME,
    AFTER_EXPRESS_ID,
    OSS_FILE_URL,
    RESOURCE_ID,
    INVOICE_FROM,
    HX_INVOICE_ID,
    AUTH_MODE,
    AUTH_FAIL_REASON,
    IS_AUTHING,
    COLOR_COMPLEMENT_TYPE,
    TI.IS_AFTER_BUYORDER_ONLY,
    TSOD.COMMENTS ratio,
    TSOD.TITLE    invoiceTypeName
    FROM T_R_INVOICE_J_INVOICE TRIJI
    LEFT JOIN T_INVOICE TI ON TRIJI.RELATE_INVOICE_ID = TI.INVOICE_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION TSOD ON TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
    LEFT JOIN T_INVOICE_VOUCHER TIV ON TIV.INVOICE_ID = TI.INVOICE_ID and TIV.IS_DELETE = 0
    where TRIJI.INVOICE_ID=#{relateInvoiceId,jdbcType=INTEGER}
    AND TI.COLOR_TYPE = 2 and IS_ENABLE = 1
    AND TI.COMPANY_ID =1
    AND TIV.INVOICE_VOUCHER_ID is not null
  </select>

  <select id="findReversalInvoice" resultMap="BaseMapAndOther">
    select TI.INVOICE_ID,
    TI.COMPANY_ID,
    INVOICE_CODE,
    INVOICE_FLOW,
    INVOICE_PROPERTY,
    INVOICE_HREF,
    TYPE,
    TAG,
    TI.RELATED_ID,
    AFTER_SALES_ID,
    INVOICE_NO,
    TI.INVOICE_TYPE,
    COLOR_TYPE,
    TI.AMOUNT,
    TI.IS_ENABLE,
    VALID_USERID,
    TI.VALID_STATUS,
    TI.VALID_TIME,
    VALID_COMMENTS,
    INVOICE_PRINT_STATUS,
    INVOICE_CANCEL_STATUS,
    EXPRESS_ID,
    IS_AUTH,
    IS_MONTH_AUTH,
    AUTH_TIME,
    TI.ADD_TIME,
    TI.CREATOR,
    TI.MOD_TIME,
    TI.UPDATER,
    INVOICE_APPLY_ID,
    SEND_TIME,
    AFTER_EXPRESS_ID,
    OSS_FILE_URL,
    RESOURCE_ID,
    INVOICE_FROM,
    HX_INVOICE_ID,
    AUTH_MODE,
    AUTH_FAIL_REASON,
    IS_AUTHING,
    COLOR_COMPLEMENT_TYPE,
    TI.IS_AFTER_BUYORDER_ONLY
    from  T_R_INVOICE_J_INVOICE TRIJI
   LEFT JOIN T_INVOICE TI ON TRIJI.RELATE_INVOICE_ID = TI.INVOICE_ID
    where TRIJI.INVOICE_ID=#{relateInvoiceId,jdbcType=INTEGER}
    AND TI.COLOR_TYPE = 1 and IS_ENABLE = 1 and COLOR_COMPLEMENT_TYPE = 1
    AND TI.COMPANY_ID =1
    union all
    select
    TI.INVOICE_ID,
    TI.COMPANY_ID,
    INVOICE_CODE,
    INVOICE_FLOW,
    INVOICE_PROPERTY,
    INVOICE_HREF,
    TYPE,
    TAG,
    TI.RELATED_ID,
    AFTER_SALES_ID,
    INVOICE_NO,
    TI.INVOICE_TYPE,
    COLOR_TYPE,
    TI.AMOUNT,
    TI.IS_ENABLE,
    VALID_USERID,
    TI.VALID_STATUS,
    TI.VALID_TIME,
    VALID_COMMENTS,
    INVOICE_PRINT_STATUS,
    INVOICE_CANCEL_STATUS,
    EXPRESS_ID,
    IS_AUTH,
    IS_MONTH_AUTH,
    AUTH_TIME,
    TI.ADD_TIME,
    TI.CREATOR,
    TI.MOD_TIME,
    TI.UPDATER,
    INVOICE_APPLY_ID,
    SEND_TIME,
    AFTER_EXPRESS_ID,
    OSS_FILE_URL,
    RESOURCE_ID,
    INVOICE_FROM,
    HX_INVOICE_ID,
    AUTH_MODE,
    AUTH_FAIL_REASON,
    IS_AUTHING,
    COLOR_COMPLEMENT_TYPE,
    TI.IS_AFTER_BUYORDER_ONLY
    FROM T_R_INVOICE_J_INVOICE TRIJI
    LEFT JOIN T_INVOICE TI ON TRIJI.INVOICE_ID = TI.INVOICE_ID
    where TRIJI.RELATE_INVOICE_ID=#{relateInvoiceId,jdbcType=INTEGER}
    AND TI.COLOR_TYPE = 1 AND IS_ENABLE = 1 and COLOR_COMPLEMENT_TYPE = 1
    AND TI.COMPANY_ID =1
  </select>
</mapper>