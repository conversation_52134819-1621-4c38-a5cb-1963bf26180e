$(function() {
	$("#orgName").focus();
	$('#orgform').submit(function() {
		checkLogin();
		var url = $("input[name='orgId']").val()*1 > 0 ? './saveeditorg.do' : './saveaddorg.do'
		jQuery.ajax({
			url:url,
			data:$('#orgform').serialize(),
			type:"POST",
			dataType : "json",
			beforeSend:function()
			{  
				var orgName = $("#orgName").val();
				var orgNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,]{1,32}$/;
				if(orgName == ""){
					warnTips("orgName","部门名称不允许为空");
					return false;
				}
				if(orgName.length < 1 || orgName.length > 32){
					warnTips("orgName","部门名称长度不允许超过32个字符");
					return false;
				}
				if(!orgNameReg.test(orgName)){
					warnTips("orgName","部门名称不允许使用特殊字符");
					return false;
				}
				
			},
			success:function(data)
			{
				refreshPageList(data);
				// 2022-11-5 不允许添加一级部门为：南京分公司
				if(data.code == 400){
					var msg = data.message != '' ? data.message : '影响面过大，新的一级部门需要确认采用哪一种业务流程，请找开发部确认。';
					layer.alert(msg);
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}

		});
		return false;
	});
});