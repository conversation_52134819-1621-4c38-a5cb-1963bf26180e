package com.vedeng.erp.saleorder.dao;
import java.util.Collection;

import com.vedeng.erp.saleorder.model.po.VJdSaleorder;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface VJdSaleorderMapper {
    int deleteByPrimaryKey(Long saleorderId);

    int insert(VJdSaleorder record);

    int insertOrUpdate(VJdSaleorder record);

    int insertOrUpdateSelective(VJdSaleorder record);

    int insertSelective(VJdSaleorder record);

    VJdSaleorder selectByPrimaryKey(Long saleorderId);

    VJdSaleorder selectByJdSaleOrderNo(String jdSaleOrderNo);

    int updateByPrimaryKeySelective(VJdSaleorder record);

    int updateByPrimaryKey(VJdSaleorder record);

    int updateBatch(List<VJdSaleorder> list);

    int updateBatchSelective(List<VJdSaleorder> list);

    int batchInsert(@Param("list") List<VJdSaleorder> list);

    List<VJdSaleorder> findByOrderGenerateStatusInAndIsDeleteFalseOrderByAddTime(@Param("orderGenerateStatusCollection")Collection<Integer> orderGenerateStatusCollection);


    int batchInsertJdSaleorder(@Param("list") List<VJdSaleorder> list);

    List<String> getNotAllowedOrder(@Param("list") List<VJdSaleorder> vJdSaleorderList);

    List<String> getErrorOrderList(@Param("list") List<VJdSaleorder> vJdSaleorderList);
    Integer getVerifyStatusByOrderId(@Param("saleorderId") Integer saleorderId);

    int updateBatchByJdSaleorderNo(List<VJdSaleorder> list);
}