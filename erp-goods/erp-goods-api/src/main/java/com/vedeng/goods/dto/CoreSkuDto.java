package com.vedeng.goods.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description com.vedeng.goods.domain.dto
 * @Date 2022/1/10 9:47
 */
@Data
public class CoreSkuDto{

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 型号
     */
    private String model;

    private String modelOrSpec;

    /**
     * 生产厂家
     */
    private String companyName;

    /**
     * 注册证
     */
    private String registrationNumber;
    /**
     * 单位
     */
    private String unitName;

    /**
     * 最小起订量
     */
    private Integer minOrder;


    /**
     * 可用库存
     */
    private Integer availableStockNum;

    /**
     * 库存量
     */
    private Integer stockNum;

    /**
     * 占用库存
     */
    private Integer orderOccupyNum;

    /**
     * 活动锁定库存
     */
    private Integer actionLockNum;

    /**
     * 采购到货时长（工作日）
     */
    private Integer purchaseTime;

    /**
     * 是否需报备 1-是 0-否
     */
    private Integer isNeedReport;

    /**
     * 产品是否可安装 1-是 0-否
     */
    private Integer isInstallable;

    /**
     *商品类型
     */
    private Integer spuType;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * spuId
     */
    private Integer spuId;
    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 费用商品分类
     */
    private Integer costCategoryId;

    /**
     * 费用商品分类名
     */
    private String categoryName;

    /**
     * 是否库存管理
     */
    private Integer haveStockManage;
    /**
     * 是否直发
     */
    private Integer isDirect;
    /**
     * 启用状态 0-禁用 1-启用 2-审核中
     */
    private Integer status;
    /**
     * 返回前端原因
     */
    private String refusedReason;
    /**
     * 审核状态
     */
    private Integer checkStatus;

    /**
     * 预计货期
     */
    private String expectDeliveryTime;

    /**
     * 技术参数
     */
    private String technicalParameter;

    /**
     * 有效期 天
     */
    private String effectiveDays;

    /**
     * 使用年限 年
     */
    private Integer serviceLife;

    /**
     * 主机保修期
     */
    private String hostPeriod;

    /**
     * 商品等级
     */
    private Integer goodsLevelNo;

    /**
     * 商品档位
     */
    private Integer goodsPositionNo;

    private Integer categoryId;

}
