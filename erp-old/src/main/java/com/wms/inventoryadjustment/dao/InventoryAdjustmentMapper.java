package com.wms.inventoryadjustment.dao;

import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.LogisticsOrderGoodsData;
import com.wms.inventoryadjustment.model.po.InventoryAdjustmentDetailPo;
import com.wms.inventoryadjustment.model.po.InventoryAdjustmentPo;
import com.wms.inventoryadjustment.model.vo.InventoryAdjustmentDetailVo;
import com.wms.inventoryadjustment.model.vo.InventoryAdjustmentVo;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;
import java.util.Map;

/**
 * 库存调整单
 *
 * <AUTHOR>
 * @date 2020/7/29 13:30:30
 */
@Named("inventoryAdjustmentMappper")
public interface InventoryAdjustmentMapper {

    /**
     * 获取库存调整单信息
     *
     * @param map
     * @return
     * <AUTHOR>
     * @date 2020/7/29 14:10:15
     */
    List<InventoryAdjustmentVo> getInventoryAdjustmentVosListPage(Map<String, Object> map);

    /**
     * 通过ID获取库存调整单信息
     *
     * @param inventoryAdjustmentId
     * @return
     * <AUTHOR>
     * @date 2020/7/29 13:40:28
     */
    InventoryAdjustmentVo getInventoryAdjustmentById(Integer inventoryAdjustmentId);

    /**
     * @return
     * @describe 获取库存调整单详情信息
     * <AUTHOR>
     * @date 2020/7/29 15:27:15
     */
    List<InventoryAdjustmentDetailVo> getInventoryAdjustmentDetailById(Integer inventoryAdjustmentId);

    /**
     * 获取库存调整单基本信息
     *
     * @param inventoryAdjustmentId
     * @return
     */
    InventoryAdjustmentPo getInventoryAdjustmentBaseInfo(Integer inventoryAdjustmentId);

    /**
     * 保存库存调整单信息
     *
     * @param inventoryAdjustmentPo
     * @return
     */
    int insertInventoryAdjustment(InventoryAdjustmentPo inventoryAdjustmentPo);

    /**
     * 保存库存转移单详情
     *
     * @param inventoryAdjustmentDetailPo
     * @return
     */
    int insertInventoryAdjustmentDetail(InventoryAdjustmentDetailPo inventoryAdjustmentDetailPo);

    /**
     * 检索快递单信息（补偿临时使用）
     *
     * @param data
     * @param orderNo
     * @return
     */
    List<Express> getExpressByDetailInfo(@Param("data") LogisticsOrderGoodsData data, @Param("orderNo") String orderNo);
}
