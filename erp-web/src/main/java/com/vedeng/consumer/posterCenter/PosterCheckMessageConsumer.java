package com.vedeng.consumer.posterCenter;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 海报中心审核站内信消息 队列消费
 * @date 2022/8/10 9:37
 */
@Component
@Slf4j
public class PosterCheckMessageConsumer extends AbstractMessageListener {


    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("开始消费海报审核信息：{}", messageBody);

        List<PosterMessageDto> msgList = JSONObject.parseArray(messageBody, PosterMessageDto.class);
        try {
            msgList.forEach(item -> {
                Map<String, String> param = new HashMap<String, String>(1) {{
                    put("", "");
                }};
                MessageUtil.sendMessage(232, Collections.singletonList(item.getUserId()), param, item.getUrl(), item.getSourceName());
            });
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("消费海报审核信息成功：{}", messageBody);
        } catch (Exception e) {
            log.error("消费海报审核信息失败：{}", messageBody);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }

    }
}