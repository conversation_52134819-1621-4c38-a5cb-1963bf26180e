$(function()
{
    var userId = $("#others_userId_id").val();
    init_page(userId);
    
});

/**
 * @description: 初始化页面.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/17 5:48 下午.
 * @author: Tomcat.Hui.
 */
function init_page(userId) {

    var month = $("#hisMonth").val();

    // 本月概况
    $.ajax(
    {
        type : "POST",
        url : page_url + "/kpi/query/queryKpiDetailXj.do",
        data :
            {
                'userId' : userId,
                'month' : month
            },
        dataType : 'json',
        success : function(result)
        {
            if (result.code == 0)
            {
                //用户
                $("#kpi_chance_success_user").text(result.data.personalKpi.successChance);
                $("#kpi_chance_fail_user").text(result.data.personalKpi.failChance);
                $("#kpi_chance_range_user").text(result.data.personalKpi.rangeCustomerNum);
                $("#kpi_chance_trans_user").text((result.data.personalKpi.transProgress * 100).toFixed(2) + '%');
                $("#kpi_chance_score_user").text(result.data.personalKpi.score.toFixed(2));
                $("#kpi_chance_sort_user").text(result.data.personalKpi.sort);

                //团队
                $("#kpi_chance_success_group").text(result.data.groupKpi.successChance);
                $("#kpi_chance_fail_group").text(result.data.groupKpi.failChance);
                $("#kpi_chance_range_group").text(result.data.groupKpi.rangeCustomerNum);
                $("#kpi_chance_trans_group").text((result.data.groupKpi.transProgress * 100).toFixed(2) + '%');
                $("#kpi_chance_score_group").text(result.data.groupKpi.score.toFixed(2));

                //榜首
                $("#first_one_span_name").text(result.data.firstKpi.userName)
                $("#kpi_chance_success_first").text(result.data.firstKpi.successChance);
                $("#kpi_chance_fail_first").text(result.data.firstKpi.failChance);
                $("#kpi_chance_range_first").text(result.data.firstKpi.rangeCustomerNum);
                $("#kpi_chance_trans_first").text((result.data.firstKpi.transProgress * 100).toFixed(2) + '%');
                $("#kpi_chance_score_first").text(result.data.firstKpi.score.toFixed(2));
                $("#kpi_chance_sort_first").text(result.data.firstKpi.sort);

                //昨天
                $("#kpi_chance_success_yesterday").text(result.data.yesterdayKpi.successChance);
                $("#kpi_chance_fail_yesterday").text(result.data.yesterdayKpi.failChance);
                $("#kpi_chance_range_yesterday").text(result.data.yesterdayKpi.rangeCustomerNum);
                $("#kpi_chance_trans_yesterday").text((result.data.yesterdayKpi.transProgress * 100).toFixed(2) + '%');
                $("#kpi_chance_score_yesterday").text(result.data.yesterdayKpi.score.toFixed(2));
                $("#kpi_chance_sort_yesterday").text(result.data.yesterdayKpi.sort);

                //上月
                $("#kpi_chance_success_lastmonth").text(result.data.lastMonthKpi.successChance);
                $("#kpi_chance_fail_lastmonth").text(result.data.lastMonthKpi.failChance);
                $("#kpi_chance_range_lastmonth").text(result.data.lastMonthKpi.rangeCustomerNum);
                $("#kpi_chance_trans_lastmonth").text((result.data.lastMonthKpi.transProgress * 100).toFixed(2) + '%');
                $("#kpi_chance_score_lastmonth").text(result.data.lastMonthKpi.score.toFixed(2));
                $("#kpi_chance_sort_lastmonth").text(result.data.lastMonthKpi.sort);


                //明细(成交)
                var successChanceDetailList = result.data.successChanceDetailList;
                show_detail(successChanceDetailList,'detail_chance_success_list');

                //明细(失败)
                var failChanceDetailList = result.data.failChanceDetailList;
                show_detail(failChanceDetailList,'detail_chance_fail_list');

                //历史数据
                var hisMonthList = result.data.hisMonthList;
                show_logs(hisMonthList,'chance_log_list');

                loadMoreAddTitle();

            }
            else {
                layer.alert("返回码错误");
            }

        },
        error : function(data)
        {

            if (data.status == 1001)
            {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            } else {
                layer.alert("请求出现异常");
            }

        }
    });

    function show_detail(logList,table_id){
        var emptyFlag = false;
        var ht = '';
        if(null == logList || logList.length == 0)
        {
            emptyFlag = true;
        }

        if(!emptyFlag)
        {
            for(var i = 0; i < logList.length; i++)
            {
                var mod = logList[i];

                if(null == mod)
                    continue;
                ht += '<tr>';
                ht += '<td>' + (i+1) +'</td>';
                ht += '<td><a class="loadMoreAddtitle" href="javascript:void(0);" tabTitle=\'{"num":"view' + mod.chanceId + '","link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=' +  mod.chanceId + '&traderId=' + mod.traderId + '","title":"销售商机详情"}\'>' + mod.chanceNo + '</a></td>';
                ht += '<td><a class="loadMoreAddtitle" href="javascript:void(0);" tabTitle=\'{"num":"viewsaleorder' + mod.orderId + '","link":"./order/saleorder/view.do?saleorderId=' +  mod.orderId + '","title":"订单信息"}\'>' + mod.orderNo + '</a></td>';
                ht += '<td>' + new Date(mod.assignTime).format('yyyy-MM-dd hh:mm:ss') +'</td>';
                ht += '</tr>';
                emptyFlag = false;
            }
        }

        if(emptyFlag)
        {
            ht += '<tr><td colspan="4">暂无数据</td></tr>';
        }

        $("#" + table_id).html(ht);
    }

    function show_logs(logList,table_id){
        var emptyFlag = false;
        var ht = '';
        if(null == logList || logList.length == 0)
        {
            emptyFlag = true;
        }

        if(!emptyFlag)
        {
            for(var i = 0; i < logList.length; i++)
            {
                var mod = logList[i];

                if(null == mod)
                    continue;
                ht += '<tr>';
                ht += '<td>' + mod.monthStr +'</td>';
                ht += '<td>' + mod.successChance +'</td>';
                ht += '<td>' + mod.rangeCustomerNum +'</td>';
                ht += '<td>' + (mod.transProgress * 100).toFixed(2) +'%</td>';
                ht += '<td>' + (mod.groupTransProgress * 100).toFixed(2) +'%</td>';
                ht += '<td>' + (mod.score).toFixed(2) +'</td>';
                ht += '<td>' + mod.sort +'</td>';
                ht += '</tr>';
                emptyFlag = false;
            }
        }

        if(emptyFlag)
        {
            ht += '<tr><td colspan="4">暂无数据</td></tr>';
        }

        $("#" + table_id).html(ht);
    }
}