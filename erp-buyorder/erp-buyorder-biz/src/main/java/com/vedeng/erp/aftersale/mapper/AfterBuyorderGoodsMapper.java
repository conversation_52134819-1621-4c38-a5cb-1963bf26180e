package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesGoodsEntity;
import com.vedeng.erp.aftersale.dto.AfterBuyorderGoodsDto;
import com.vedeng.erp.aftersale.dto.AfterSaleGoodsIdQueryReqDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AfterBuyorderGoodsMapper {
    /**
     * 根据售后单id查询售后商品信息
     * <AUTHOR>
     * @param aftersalesId
     * @return
     */
    List<AfterBuyorderGoodsDto> queryAfterBuyorderGoodsById(@Param("aftersalesId") Integer aftersalesId);

    /**
     * 条件检索售后商品ID
     * @param afterSaleGoodsIdQueryReqDto
     * @return
     */
    Integer getAfterGoodsIdByCondition(AfterSaleGoodsIdQueryReqDto afterSaleGoodsIdQueryReqDto);

    /**
     * 查询所有售后商品（不含虚拟商品，仅普通商品）
     * @param afterSalesId
     * @return
     */
    List<BuyOrderAfterSalesGoodsEntity> findAllByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);
}
