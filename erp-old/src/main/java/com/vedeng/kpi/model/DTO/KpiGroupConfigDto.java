package com.vedeng.kpi.model.DTO;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 团队配置实体类.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 10:39 上午.
 * @author: Tomcat.Hui.
 */
public class KpiGroupConfigDto {

    private Integer groupId;

    private String groupName;

    private List<Integer> groupManagerId;

    private List<KpiConfigItemDto> configItems;

    private List<KpiTeamDto> teams;

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<Integer> getGroupManagerId() {
        return groupManagerId;
    }

    public void setGroupManagerId(List<Integer> groupManagerId) {
        this.groupManagerId = groupManagerId;
    }

    public List<KpiConfigItemDto> getConfigItems() {
        return configItems;
    }

    public void setConfigItems(List<KpiConfigItemDto> configItems) {
        this.configItems = configItems;
    }

    public List<KpiTeamDto> getTeams() {
        return teams;
    }

    public void setTeams(List<KpiTeamDto> teams) {
        this.teams = teams;
    }
}
