package com.wms.model.dto;

public class AddScrappedOutDto {

    private Integer userId;

    private String username;

    private Integer orgId;

    private String orgName;

    private Integer scrapType;

    private Integer scrapLevel;

    private Integer scrapDealType;

    private String applyer;

    private String applyerDepartment;

    private String[] skuNo;

    private Long[] outputNum;

    private String appleOutDate;

    private String remark;


    public Integer getScrapType() {
        return scrapType;
    }

    public void setScrapType(Integer scrapType) {
        this.scrapType = scrapType;
    }

    public Integer getScrapLevel() {
        return scrapLevel;
    }

    public void setScrapLevel(Integer scrapLevel) {
        this.scrapLevel = scrapLevel;
    }

    public Integer getScrapDealType() {
        return scrapDealType;
    }

    public void setScrapDealType(Integer scrapDealType) {
        this.scrapDealType = scrapDealType;
    }

    public String getApplyer() {
        return applyer;
    }

    public void setApplyer(String applyer) {
        this.applyer = applyer;
    }

    public String getApplyerDepartment() {
        return applyerDepartment;
    }

    public void setApplyerDepartment(String applyerDepartment) {
        this.applyerDepartment = applyerDepartment;
    }

    public String[] getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String[] skuNo) {
        this.skuNo = skuNo;
    }

    public Long[] getOutputNum() {
        return outputNum;
    }

    public void setOutputNum(Long[] outputNum) {
        this.outputNum = outputNum;
    }

    public String getAppleOutDate() {
        return appleOutDate;
    }

    public void setAppleOutDate(String appleOutDate) {
        this.appleOutDate = appleOutDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }


}
