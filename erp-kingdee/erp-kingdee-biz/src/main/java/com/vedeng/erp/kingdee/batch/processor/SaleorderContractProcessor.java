package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleorderContractDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class SaleorderContractProcessor extends BaseProcessor<BatchSaleorderContractDto, KingDeeSaleorderContractDto> {

    @Autowired
    KingDeeBaseApi kingDeeBaseApi;
    @Override
    public KingDeeSaleorderContractDto doProcess(BatchSaleorderContractDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始处理销售订单合同推送金蝶，dto:{}", JSONUtil.toJsonStr(dto));
        if(ObjectUtil.isNotNull(dto.getDataId()) || ObjectUtil.isNotNull(dto.getKingdeeDDH())){
            log.info("销售合同推送金蝶，数据已存在:{}",JSON.toJSONString(dto));
            return null;
        }
        KingDeeSaleorderContractDto result = new KingDeeSaleorderContractDto();
        result.setFQzokDdh(dto.getSaleorderNo());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(result);
        if(old){
            log.info("销售合同推送金蝶,数据已存在:{}", JSON.toJSONString(dto));
            return null;
        }
        result.setFId(ErpConstant.ZERO);
        result.setFQzokOrgid(KingDeeConstant.ORG_ID.toString());
        result.setFQzokHtrq(DateUtil.formatDateTime(new Date(dto.getValidTime())));
        result.setFQzokHth(dto.getSaleorderNo());
        result.setFQzokHtje(dto.getTotalAmount().toString());
        result.setFQzokSll(dto.getRate().multiply(new BigDecimal("100")).toString());
        result.setFBillNo(dto.getSaleorderNo());
        return result;
    }
}
