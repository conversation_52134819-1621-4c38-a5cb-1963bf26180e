<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeNeedPayAdjustMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedPayAdjustEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_NEED_PAY_ADJUST-->
    <id column="KING_DEE_NEED_PAY_ENTITY_ID" jdbcType="INTEGER" property="kingDeeNeedPayEntityId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_QZOK_DATE" jdbcType="VARCHAR" property="fQzokDate" />
    <result column="F_QZOK_JG" jdbcType="VARCHAR" property="fQzokJg" />
    <result column="F_QZOK_BASE" jdbcType="VARCHAR" property="fQzokBase" />
    <result column="F_ENTITY" jdbcType="VARCHAR" property="fEntity" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_NEED_PAY_ENTITY_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, FID, F_BILL_NO, F_QZOK_DATE, F_QZOK_JG, F_QZOK_BASE, F_ENTITY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_NEED_PAY_ADJUST
    where KING_DEE_NEED_PAY_ENTITY_ID = #{kingDeeNeedPayEntityId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_NEED_PAY_ADJUST
    where KING_DEE_NEED_PAY_ENTITY_ID = #{kingDeeNeedPayEntityId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_NEED_PAY_ENTITY_ID" keyProperty="kingDeeNeedPayEntityId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedPayAdjustEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_NEED_PAY_ADJUST (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      FID, F_BILL_NO, F_QZOK_DATE, 
      F_QZOK_JG, F_QZOK_BASE, F_ENTITY
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fid,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fQzokDate,jdbcType=VARCHAR}, 
      #{fQzokJg,jdbcType=VARCHAR}, #{fQzokBase,jdbcType=VARCHAR}, #{fEntity,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_NEED_PAY_ENTITY_ID" keyProperty="kingDeeNeedPayEntityId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedPayAdjustEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_NEED_PAY_ADJUST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fid != null">
        FID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fQzokDate != null">
        F_QZOK_DATE,
      </if>
      <if test="fQzokJg != null">
        F_QZOK_JG,
      </if>
      <if test="fQzokBase != null">
        F_QZOK_BASE,
      </if>
      <if test="fEntity != null">
        F_ENTITY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDate != null">
        #{fQzokDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJg != null">
        #{fQzokJg,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBase != null">
        #{fQzokBase,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        #{fEntity,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedPayAdjustEntity">
    <!--@mbg.generated-->
    update KING_DEE_NEED_PAY_ADJUST
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDate != null">
        F_QZOK_DATE = #{fQzokDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJg != null">
        F_QZOK_JG = #{fQzokJg,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBase != null">
        F_QZOK_BASE = #{fQzokBase,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        F_ENTITY = #{fEntity,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_NEED_PAY_ENTITY_ID = #{kingDeeNeedPayEntityId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedPayAdjustEntity">
    <!--@mbg.generated-->
    update KING_DEE_NEED_PAY_ADJUST
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_DATE = #{fQzokDate,jdbcType=VARCHAR},
      F_QZOK_JG = #{fQzokJg,jdbcType=VARCHAR},
      F_QZOK_BASE = #{fQzokBase,jdbcType=VARCHAR},
      F_ENTITY = #{fEntity,jdbcType=VARCHAR}
    where KING_DEE_NEED_PAY_ENTITY_ID = #{kingDeeNeedPayEntityId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="KING_DEE_NEED_PAY_ENTITY_ID" keyProperty="kingDeeNeedPayEntityId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_NEED_PAY_ADJUST
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, F_BILL_NO, 
      F_QZOK_DATE, F_QZOK_JG, F_QZOK_BASE, F_ENTITY)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fid,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, #{item.fQzokDate,jdbcType=VARCHAR}, 
        #{item.fQzokJg,jdbcType=VARCHAR}, #{item.fQzokBase,jdbcType=VARCHAR}, #{item.fEntity,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>