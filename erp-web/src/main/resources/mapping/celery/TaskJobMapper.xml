<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newtask.celery.dao.TaskJobMapper">

    <insert id="save" parameterType="com.newtask.celery.model.TaskJob" useGeneratedKeys="true" keyProperty="jobId">
        INSERT INTO T_ASYNC_TASK_JOB(UUID,QUEUE,CHECK_IDEMPOTENT,HANDLE_INDEX,INPUT_PARAMS,INPUT_ARGS,VALID_MILLIS,ADD_TIME) values (
#{uuid,jdbcType=VARCHAR},#{queue,jdbcType=INTEGER},#{checkIdempotent,jdbcType=INTEGER},#{handleIndex,jdbcType=VARCHAR},#{inputParams,jdbcType=VARCHAR},#{inputArgs,jdbcType=BLOB,typeHandler=org.apache.ibatis.type.ByteArrayTypeHandler},#{validMillis,jdbcType=INTEGER},#{addTime,jdbcType=BIGINT})
    </insert>

    <update id="update"  parameterType="com.newtask.celery.model.TaskJob" >
        update T_ASYNC_TASK_JOB
        <set>
            <if test="status != null">
                STATUS = #{status,jdbcType=INTEGER},
            </if>
            <if test="errorMsg != null">
                ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
        </set>
        where JOB_ID = #{jobId,jdbcType=BIGINT}
    </update>

    <select id="listActiveQueue" resultType="java.lang.Integer">
        SELECT DISTINCT QUEUE FROM T_ASYNC_TASK_JOB WHERE STATUS = 0;
    </select>

    <select id="listTaskByQueue" resultType="com.newtask.celery.model.TaskJob">
        SELECT * FROM T_ASYNC_TASK_JOB WHERE QUEUE = #{queue} AND STATUS = 0 AND JOB_ID > #{lastId} LIMIT 100
    </select>


</mapper>