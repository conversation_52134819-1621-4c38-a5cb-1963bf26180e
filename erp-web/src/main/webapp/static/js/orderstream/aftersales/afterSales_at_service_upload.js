function uploadFile(obj,num){
    checkLogin();
    var imgPath = $(obj).val();
    if(imgPath == '' || imgPath == undefined){
        return false;
    }
    var oldName=imgPath.substr(imgPath.lastIndexOf('\\')+1);
    var domain = $("#domain").val();
    //判断上传文件的后缀名
    var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
    if (strExtension != 'jpg'  && strExtension != 'png'
        && strExtension != 'JPG'  && strExtension != 'PNG' && strExtension !='JPEG'&& strExtension !='jpeg') {
        layer.alert("上传格式错误");
        return false;
    }
    var fileSize = 0;
    var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
    if (isIE && !obj.files) {
        var filePath = obj.value;
        var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
        var file = fileSystem.GetFile (filePath);
        fileSize = file.Size;
    }else {
        fileSize = obj.files[0].size;
    }
    fileSize=Math.round(fileSize/1024*100)/100; //单位为KB
    if(fileSize>20480){
        layer.alert("上传的文件过大");
        return false;
    }
    $(obj).parent().parent().find("i").attr("class", "iconloading mt5").show();
    $(obj).parent().parent().find("a").hide();
    $(obj).parent().parent().find("span").hide();
    var objCopy1 = $(obj).parent();
    var objCopy2 = $(obj).parent().parent();
    $.ajaxFileUpload({
        url : page_url + '/fileUpload/uploadFile2Oss.do', //用于文件上传的服务器端请求地址
        secureuri : false, //一般设置为false
        fileElementId : $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
        dataType : 'json',//返回值类型 一般设置为json
        complete : function() {//只要完成即执行，最后执行
        },
        //服务器成功响应处理函数
        success : function(data) {
            if (data.code == 0) {

                objCopy1.find("input[type='text']").val(data.fileName);
                objCopy1.find("input[type='hidden']").val(data.filePath);
                $("#domain").val(data.httpUrl);
                objCopy2.find("i").attr("class", "iconsuccesss ml7").show();
                objCopy2.find("a").attr("href", 'http://' + data.httpUrl + data.filePath).show();
                objCopy2.find("span").show();
                if (num == 6) {
                    $("#sy-message").css("display","none");
                }

                if(num==1){
                    $("#yy-message").css("display","none");
                }

            } else {
                layer.alert("error"+data.message);
            }
        },
        //服务器响应失败处理函数
        error : function(data1, status, e) {

            if(data1.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }else{
                var data=JSON.parse(data1);
                if (data.code == 0) {

                    objCopy1.find("input[type='text']").val(data.fileName);
                    objCopy1.find("input[type='hidden']").val(data.filePath);
                    $("#domain").val(data.httpUrl);
                    objCopy2.find("i").attr("class", "iconsuccesss ml7").show();
                    objCopy2.find("a").attr("href", 'http://' + data.httpUrl + data.filePath).show();
                    objCopy2.find("span").show();
                } else {
                    layer.alert(data.message);
                }
            }

        }
    });
}

function del(num){
    checkLogin();
    index = layer.confirm("您是否确认该操作？", {
        btn: ['确定','取消'] //按钮
    }, function(){

        $("#img_icon_" + num).hide();
        $("#img_view_" + num).hide();
        $("#img_del_" + num).hide();
        $("#name_"+ num).val("");
        $("#uri_"+ num).val("");

        layer.close(index);
    }, function(){
    });
}


//点击继续添加按钮
function con_add(id,desc){
    var rndNum = RndNum(8);
    var kind=0;

    kind=id;
    var html = '<div class="c_'+kind+'">'+
        '<div class="pos_rel f_left mb8 ">'+
        '<input type="file" class=" uploadErp" name="lwfile" id="lwfile_'+id+'_'+rndNum+'" onchange="uploadFile(this, '+id+')">'+
        '<input type="text" class="input-middle" style="margin-right:10px;" id="name_'+id+'_'+rndNum+'" readonly="readonly" placeholder="'+desc+'" name="fileName" onclick="lwfile_'+id+'_'+rndNum+'.click();" value ="">'+
        '<input type="hidden" class="input-middle mr5" id="uri_'+id+'_'+rndNum+'" name="fileUri" value="" >'+
        '<label class="bt-bg-style bt-middle bg-light-blue ml4" type="file" >浏览</label>'+
        '</div>'+
        '<div class="f_left ">'+
        '<i class="iconsuccesss mt5 none" id="img_icon_'+id+'"></i>'+
        '<a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_'+id+'" style="margin:0px 8px 0 13px;">查看</a>'+
        '<span class="font-red cursor-pointer mt4" onclick="delAttachment(this)" id="img_del_'+id+'">删除</span>'+
        '</div>'+
        '<div class="clear"></div></div>';
    $("#conadd"+id).before(html);
}

function RndNum(n){
    var rnd="";
    for(var i=0;i<n;i++)
        rnd+=Math.floor(Math.random()*10);
    return rnd;
}

function delAttachment(obj) {
    var uri = $(obj).parent().find("a").attr("href");
    if (uri == '') {
        $(obj).parent().parent().remove();
    } else {
        index = layer.confirm("您是否确认该操作？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            var length = $(obj).parent().parent().parent().find("input[type='file']").length;
            if (length == 1) {
                $(obj).parent().find("i").hide();
                $(obj).parent().find("a").hide();
                $(obj).parent().find("span").hide();
                $(obj).parent().parent().parent().find("input[type='text']").val('');
            } else {
                $(obj).parent().parent().remove();
            }
            layer.close(index);
        }, function(){
        });
    }
}

function uploadDirectInfo(){
    let fileNames = [];
    let fileUris = [];
    //给父页面赋值
    $("input[name='fileName']").each(function (){
        fileNames.push($(this).val());
    });
    $("input[name='fileUri']").each(function (){
        fileUris.push($(this).val());
    });
    window.parent.$("#fileNames").val(fileNames);
    window.parent.$("#fileUris").val(fileUris);
    var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
    parent.layer.close(index);//关闭窗口
}
