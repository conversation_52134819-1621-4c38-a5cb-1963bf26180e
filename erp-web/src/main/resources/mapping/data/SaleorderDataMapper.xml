<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newtask.data.dao.SaleorderDataMapper">

    <select id="getSaleorderById" resultType="java.lang.Long">
        SELECT count(1) FROM T_SALEORDER_DATA WHERE SALEORDER_ID = #{id}
    </select>

    <insert id="insertSaleOrder">
        INSERT IGNORE INTO T_SALEORDER_DATA (SALEORDER_ID,ADD_TIME,MOD_TIME) VALUES (#{id}, #{nowTime}, #{nowTime})
    </insert>

    <select id="getSaleorderFirstPayTimeIdsList" resultType="java.lang.Integer">
        SELECT DISTINCT
        SALEORDER_ID
        FROM
        T_SALEORDER a
        LEFT JOIN T_CAPITAL_BILL_DETAIL c ON a.SALEORDER_ID = c.RELATED_ID
        LEFT JOIN T_CAPITAL_BILL b ON b.CAPITAL_BILL_ID = c.CAPITAL_BILL_ID
        WHERE
        a.ORDER_TYPE != 2
        AND c.ORDER_TYPE = 1
        AND c.BUSSINESS_TYPE = 526
             AND b.TRADER_TIME >= #{startTime}
            AND b.TRADER_TIME &lt;= #{endTime}
         GROUP BY a.SALEORDER_ID
    </select>
    <select id="findFirstPayTimeBySaleorderId" resultType="map">
        SELECT DISTINCT
        SALEORDER_ID saleorderId,
        min( IF ( b.TRADER_TIME = 0, NULL, b.TRADER_TIME ) )AS firstPayTime
        FROM
        T_SALEORDER a
        LEFT JOIN T_CAPITAL_BILL_DETAIL c ON a.SALEORDER_ID = c.RELATED_ID
        LEFT JOIN T_CAPITAL_BILL b ON b.CAPITAL_BILL_ID = c.CAPITAL_BILL_ID
        WHERE
        a.ORDER_TYPE != 2
        AND c.ORDER_TYPE = 1
        AND c.BUSSINESS_TYPE = 526

             AND a.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>

        GROUP BY a.SALEORDER_ID
    </select>
    <update id="updateSaleorderFirstPayTime">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        FIRST_PAY_TIME = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.firstPayTime}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>

    <select id="getSaleorderLastInvoiceTimeIdsList" resultType="java.lang.Integer">
        SELECT DISTINCT SALEORDER_ID
        FROM T_SALEORDER a
                 LEFT JOIN T_INVOICE b ON b.RELATED_ID = a.SALEORDER_ID
        WHERE
            a.ORDER_TYPE != 2
          AND b.TYPE = 505
          AND b.COLOR_TYPE = 2
          AND b.IS_ENABLE = 1
          AND b.VALID_STATUS = 1

          AND b.MOD_TIME >= #{startTime}
          AND b.MOD_TIME &lt;= #{endTime}

    </select>
    <select id="findLastInvoiceTimeBySaleorderId" resultType="map">
        SELECT
        a.SALEORDER_ID AS saleorderId,
        max( IF ( b.ADD_TIME = 0, NULL, b.ADD_TIME ) ) AS lastInvoiceTime
        FROM T_SALEORDER a
        LEFT JOIN T_INVOICE b ON b.RELATED_ID = a.SALEORDER_ID
        WHERE
        a.ORDER_TYPE != 2
        AND b.TYPE = 505
        AND b.COLOR_TYPE = 2
        AND b.IS_ENABLE = 1
        AND b.VALID_STATUS = 1

        AND a.SALEORDER_ID in
        <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
            #{bizId}
        </foreach>

        GROUP BY a.SALEORDER_ID
    </select>
    <update id="updateSaleorderLastInvoiceTime">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        LAST_INVOICE_TIME = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.lastInvoiceTime}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>

    <select id="getSaleorderFirstExpressTimeIdsList" resultType="java.lang.Integer">
        SELECT DISTINCT
            s.SALEORDER_ID
        FROM
            T_SALEORDER s
                LEFT JOIN T_SALEORDER_GOODS c ON c.SALEORDER_ID = s.SALEORDER_ID
                LEFT JOIN T_EXPRESS_DETAIL b ON b.RELATED_ID = c.SALEORDER_GOODS_ID
                LEFT JOIN T_EXPRESS a ON a.EXPRESS_ID = b.EXPRESS_ID
        WHERE
                    s.ORDER_TYPE != 2
                AND a.IS_ENABLE = 1
                AND b.BUSINESS_TYPE = 496

                AND (a.MOD_TIME >= #{startTime}
                AND a.MOD_TIME &lt;= #{endTime} )
           OR (a.DELIVERY_TIME >= #{startTime}
            AND a.DELIVERY_TIME &lt;= #{endTime} )

        GROUP BY
            c.SALEORDER_ID
    </select>
    <select id="findFirstExpressTimeBySaleorderId" resultType="java.util.Map">
        SELECT DISTINCT
        s.SALEORDER_ID saleorderId,
        min( IF ( a.ADD_TIME = 0, NULL, a.ADD_TIME ) ) AS firstExpressTime,
        min( IF ( a.ARRIVAL_TIME = 0, NULL, a.ARRIVAL_TIME ) ) AS firstReceiveTime
        FROM
        T_SALEORDER s
        LEFT JOIN T_SALEORDER_GOODS c ON c.SALEORDER_ID = s.SALEORDER_ID
        LEFT JOIN T_EXPRESS_DETAIL b ON b.RELATED_ID = c.SALEORDER_GOODS_ID
        LEFT JOIN T_EXPRESS a ON a.EXPRESS_ID = b.EXPRESS_ID
        WHERE
        s.ORDER_TYPE != 2
        AND a.IS_ENABLE = 1
        AND b.BUSINESS_TYPE = 496


        AND s.SALEORDER_ID in
        <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
            #{bizId}
        </foreach>

        GROUP BY s.SALEORDER_ID
    </select>
    <update id="updateSaleorderFirstExpressTime">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        FIRST_EXPRESS_TIME = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.firstExpressTime}
        </foreach>
        END ,
        FIRST_RECEIVE_TIME = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.firstReceiveTime}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <select id="getSaleorderContractStatusIdsList" resultType="java.lang.Integer">
        SELECT DISTINCT
            a.SALEORDER_ID
        FROM
            T_SALEORDER a
                LEFT JOIN T_ATTACHMENT b ON ATTACHMENT_FUNCTION = 492 AND b.RELATED_ID = a.SALEORDER_ID
        WHERE
            a.ORDER_TYPE != 2
          and a.UPDATE_DATA_TIME >= from_unixtime(#{startTime}/1000)
          AND a.UPDATE_DATA_TIME &lt;=  from_unixtime(#{endTime}/1000)
        GROUP BY
            a.SALEORDER_ID
    </select>
    <select id="findContractStatusBySaleorderId" resultType="java.util.Map">
        SELECT DISTINCT
        a.SALEORDER_ID saleorderId,
        if(count(b.ATTACHMENT_ID) > 0, 1, 0) contractStatus
        FROM
        T_SALEORDER a
        LEFT JOIN T_ATTACHMENT b ON ATTACHMENT_FUNCTION = 492 AND b.RELATED_ID = a.SALEORDER_ID

        WHERE
        a.ORDER_TYPE != 2
        AND b.IS_DELETED = 0
            AND a.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>

        GROUP BY
        a.SALEORDER_ID
    </select>
    <update id="updateSaleorderContractStatus">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        CONTRACT_STATUS = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.contractStatus}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <update id="updateSaleorderLeftAmountPeriod">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        LEFT_AMOUNT_PERIOD = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.leftAmountPeriod}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <update id="updateSaleorderAutoCheck">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        AUTO_CHECK = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.autoCheck}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>



    <update id="updateCommunicateNumCheck">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        COMMUNICATE_NUM = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.communicateNum}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <select id="getSaleorderAfterSalesStatusIdsList" resultType="java.lang.Integer">
        select a.SALEORDER_ID
        from T_SALEORDER a
        left join T_AFTER_SALES b on a.SALEORDER_ID = b.ORDER_ID
        where 1 = 1 AND b.SUBJECT_TYPE = 535

        AND (b.MOD_TIME >= #{startTime}
        AND b.MOD_TIME &lt;= #{endTime} )

        GROUP BY
        a.SALEORDER_ID

    </select>
    <select id="findAfterSalesStatusBySaleorderId" resultType="com.newtask.data.dto.AfterSalesStatusDto">
        select a.SALEORDER_ID saleorderId,b.ATFER_SALES_STATUS afterSalesStatus
        from T_SALEORDER a
        left join T_AFTER_SALES b on a.SALEORDER_ID = b.ORDER_ID
        where b.SUBJECT_TYPE = 535
        and a.SALEORDER_ID in
        <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
            #{bizId}
        </foreach>
    </select>
    <update id="updateAfterSalesStatusCheck">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        AFTER_SALES_STATUS = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.afterSalesStatus}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <select id="getSaleorderInvoiceApplyFlayIdsList" resultType="java.lang.Integer">
        select a.SALEORDER_ID
        from T_SALEORDER a
        left join T_INVOICE_APPLY b on a.SALEORDER_ID = b.RELATED_ID
        where b.TYPE = 505 <!-- 销售开票 -->
             AND (b.MOD_TIME >= #{startTime}
            AND b.MOD_TIME &lt;= #{endTime} )

        GROUP BY
        a.SALEORDER_ID
    </select>
    <select id="findInvoiceApplyFlayBySaleorderId" resultType="java.util.Map">
        select a.SALEORDER_ID saleorderId,if(b.RELATED_ID is null,0,1) invoiceApplyFlay
        from T_SALEORDER a
        left join T_INVOICE_APPLY b on a.SALEORDER_ID = b.RELATED_ID
        and (( ifnull(b.IS_ADVANCE,0)=0 and b.VALID_STATUS!=2)
        or (b.IS_ADVANCE=1 and b.ADVANCE_VALID_STATUS!=2) )
        and b.TYPE = 505
             where  a.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
     </select>
    <update id="updateSaleorderInvoiceApplyFlay">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        INVOICE_APPLY_FLAY = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.invoiceApplyFlay}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <select id="getSaleorderVerifyStatusIdsList" resultType="java.lang.Integer">
        SELECT
        a.SALEORDER_ID
        FROM
        T_SALEORDER a
        LEFT JOIN T_VERIFIES_INFO b ON a.SALEORDER_ID = b.RELATE_TABLE_KEY
        where b.RELATE_TABLE = "T_SALEORDER"
        AND b.VERIFIES_TYPE = 623

            AND (b.MOD_TIME >= #{startTime}
            AND b.MOD_TIME &lt;= #{endTime})

        GROUP BY
        a.SALEORDER_ID
    </select>
    <select id="findVerifyStatusBySaleorderId" resultType="java.util.Map">
        SELECT
        a.SALEORDER_ID saleorderId,
        CASE
        when b.STATUS is null  then '3'
        when b.STATUS = 0 then '0'
        when b.STATUS = 1 then '1'
        when b.STATUS = 2 then '2'
        end verifyStatus
        FROM
        T_SALEORDER a
        LEFT JOIN T_VERIFIES_INFO b ON a.SALEORDER_ID = b.RELATE_TABLE_KEY
        where b.RELATE_TABLE = "T_SALEORDER"
        AND b.VERIFIES_TYPE = 623
             and a.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
         GROUP BY
        a.SALEORDER_ID
    </select>

    <update id="updateSaleorderVerifyStatus">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        VERIFY_STATUS = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.verifyStatus}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <select id="getSaleorderContractVerifyStatusIdsList" resultType="java.lang.Integer">
        SELECT
        a.SALEORDER_ID
        FROM
        T_SALEORDER a

        LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY
        WHERE
        e.RELATE_TABLE = 'T_SALEORDER'
        AND e.VERIFIES_TYPE = 868
             AND (e.MOD_TIME >= #{startTime}
            AND e.MOD_TIME &lt;= #{endTime})
          group by a.SALEORDER_ID
    </select>


    <select id="findContractVerifyStatusBySaleorderId" resultType="java.util.Map">
        SELECT
        a.SALEORDER_ID saleorderId,
        CASE
        when e.`status` is null and b.RELATED_ID is not null  then '4' -- 待提交审核
        when e.`status` is null and b.RELATED_ID is   null  then '5' -- 未上传
        when e.`status` = 0 then '0' -- 审核中
        when e.`status` = 1 then '1' -- 审核通过
        when e.`status` = 2 then '2' -- 审核不通过

        end contractVerifyStatus
        FROM
        T_SALEORDER a
        LEFT JOIN T_ATTACHMENT b ON a.SALEORDER_ID = b.RELATED_ID and b.ATTACHMENT_FUNCTION = 492 and b.is_deleted=0
        LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY
             AND e.RELATE_TABLE = 'T_SALEORDER' AND e.VERIFIES_TYPE = 868
        WHERE

              a.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
        group by a.SALEORDER_ID
    </select>

    <update id="updateSaleorderContractVerifyStatus">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        CONTRACT_VERIFY_STATUS = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.contractVerifyStatus}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>

    <select id="getSaleorderSubStatusIdsList" resultType="java.lang.Integer">
        SELECT
            a.SALEORDER_ID
        FROM
            T_SALEORDER a
                LEFT JOIN T_VERIFIES_INFO b ON
                        a.SALEORDER_ID = b.RELATE_TABLE_KEY
                    and b.RELATE_TABLE = "T_SALEORDER"
                    AND b.VERIFIES_TYPE = 623
                left join T_INVOICE_APPLY c on
                        c.RELATED_ID = a.SALEORDER_ID
                    and c.TYPE = 505
        where
            (a.MOD_TIME >= #{startTime}
                AND a.MOD_TIME &lt;= #{endTime})
           OR (a.UPDATE_DATA_TIME >= FROM_UNIXTIME(#{startTime} / 1000)
            AND a.UPDATE_DATA_TIME &lt;= FROM_UNIXTIME(#{endTime} / 1000))
           OR (a.PAYMENT_TIME >= #{startTime}
            AND a.PAYMENT_TIME &lt;= #{endTime})
        union
        SELECT
            a.SALEORDER_ID
        FROM
            T_SALEORDER a
                LEFT JOIN T_VERIFIES_INFO b ON
                        a.SALEORDER_ID = b.RELATE_TABLE_KEY
                    and b.RELATE_TABLE = "T_SALEORDER"
                    AND b.VERIFIES_TYPE = 623
                left join T_INVOICE_APPLY c on
                        c.RELATED_ID = a.SALEORDER_ID
                    and c.TYPE = 505
        where
            (b.MOD_TIME >= #{startTime}
                AND b.MOD_TIME &lt;= #{endTime})
        union
        SELECT
            a.SALEORDER_ID
        FROM
            T_SALEORDER a
                LEFT JOIN T_VERIFIES_INFO b ON
                        a.SALEORDER_ID = b.RELATE_TABLE_KEY
                    and b.RELATE_TABLE = "T_SALEORDER"
                    AND b.VERIFIES_TYPE = 623
                left join T_INVOICE_APPLY c on
                        c.RELATED_ID = a.SALEORDER_ID
                    and c.TYPE = 505
        where
            (c.MOD_TIME >= #{startTime}
                AND c.MOD_TIME &lt;= #{endTime})
    </select>

    <select id="findSubStatusBySaleorderId" resultType="com.vedeng.order.model.Saleorder">
        SELECT
        a.SALEORDER_ID, a.STATUS, a.PAYMENT_STATUS, a.DELIVERY_STATUS, a.ARRIVAL_STATUS, a.INVOICE_STATUS,a.VALID_STATUS,
        CASE
        when b.STATUS is null  then '3'
        when b.STATUS = 0 then '0'
        when b.STATUS = 1 then '1'
        when b.STATUS = 2 then '2'
        end verifyStatus,c.IS_ADVANCE,c.ADVANCE_VALID_STATUS
        FROM
        T_SALEORDER a
        LEFT JOIN T_VERIFIES_INFO b ON a.SALEORDER_ID = b.RELATE_TABLE_KEY and b.RELATE_TABLE = "T_SALEORDER"
        AND b.VERIFIES_TYPE = 623
        left join T_INVOICE_APPLY c on c.RELATED_ID = a.SALEORDER_ID and c.TYPE = 505
        where 1 = 1
             and a.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
         GROUP BY
        a.SALEORDER_ID
    </select>

    <select id="getSaleorderConcatNameInfoByTime" resultType="java.util.Map">
        SELECT
        SALEORDER_ID saleorderId,
        concat_ws( '_', GROUP_CONCAT( SKU ), GROUP_CONCAT( BRAND_NAME ), GROUP_CONCAT( GOODS_NAME ), GROUP_CONCAT( MODEL )
        , GROUP_CONCAT( SPEC )) concatName
        FROM
        T_SALEORDER_GOODS
        WHERE
        IS_DELETE = 0
             AND SALEORDER_ID in
            <foreach collection="saleorderIds" item="saleorderId" index="index" close=")" open="(" separator=",">
                #{saleorderId}
            </foreach>
         GROUP BY
        SALEORDER_ID
    </select>

    <select id="getSaleorderIdBySaleorderGoodsModTime" resultType="java.lang.Integer">
        SELECT
            DISTINCT SALEORDER_ID saleorderId
        FROM
            T_SALEORDER_GOODS
        WHERE
                    IS_DELETE = 0
                and (MOD_TIME BETWEEN #{startTime} AND #{endTime})
           OR (ADD_TIME >= #{startTime}
            AND ADD_TIME &lt;= #{endTime})
        GROUP BY
            SALEORDER_ID
    </select>

    <update id="updateSaleorderSubStatus">
        UPDATE T_SALEORDER_DATA SET
        MOD_TIME = #{nowTime},
        SUB_STATUS = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.subStatus}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>
    <update id="updateSaleorderNameConcatInfo">
        UPDATE
        T_SALEORDER_DATA
        SET
        MOD_TIME = #{nowTime},
        SKU_BRAND_NAME_MODEL = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.skuBrandNameModel}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <select id="getSaleorderCurrentOrgAndUserIdsList" resultType="java.lang.Integer">
        SELECT SALEORDER_ID
        FROM T_SALEORDER
        WHERE
            (MOD_TIME >= #{startTime}
                AND MOD_TIME &lt;= #{endTime} )
           OR (ADD_TIME >= #{startTime}
            AND ADD_TIME &lt;= #{endTime})
        GROUP BY
            SALEORDER_ID
        union all
        (
            SELECT a.SALEORDER_ID
            FROM T_SALEORDER a left join T_R_TRADER_J_USER B ON a.TRADER_ID=B.TRADER_ID AND B.TRADER_TYPE=1
                               left join T_SALEORDER_DATA C ON C.SALEORDER_ID=a.SALEORDER_ID
                               LEFT JOIN T_R_USER_POSIT UP ON UP.USER_ID=B.USER_ID
                               LEFT JOIN T_POSITION P ON P.POSITION_ID=UP.POSITION_ID
            where (C.CURRENT_USER_ID!=B.USER_ID or P.ORG_ID!=C.CURRENT_ORG_ID)
                AND a.COMPANY_ID =1 AND P.ORG_ID NOT IN (1,2)
                AND a.SALEORDER_ID not in (

                    SELECT distinct a.SALEORDER_ID
                        FROM T_SALEORDER a
                    left join T_R_TRADER_J_USER B ON a.TRADER_ID = B.TRADER_ID AND B.TRADER_TYPE = 1
                    left join T_SALEORDER_DATA C ON C.SALEORDER_ID = a.SALEORDER_ID
                    LEFT JOIN T_R_USER_POSIT UP ON UP.USER_ID = B.USER_ID
                    LEFT JOIN T_POSITION P ON P.POSITION_ID = UP.POSITION_ID
                        where a.COMPANY_ID = 1
                            AND P.ORG_ID NOT IN (1, 2)
                            and P.ORG_ID = C.CURRENT_ORG_ID and C.CURRENT_USER_ID = B.USER_ID
                )

            GROUP BY
                a.SALEORDER_ID
            limit 500)
        limit 1000
    </select>
    <select id="findCurrentOrgAndUserIdsBySaleorderId" resultType="java.util.Map">
        select SALEORDER_ID saleorderId,TRADER_ID traderId, USER_ID userId
        from T_SALEORDER
        where
        SALEORDER_ID in
        <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
            #{bizId}
        </foreach>
    </select>
    <update id="updateSaleorderCurrentOrgAndUserId">
        UPDATE T_SALEORDER_DATA
        SET
        MOD_TIME = #{nowTime},
        CURRENT_ORG_ID = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.currentOrgId}
        </foreach>
        END,
        CURRENT_USER_ID = CASE
        <foreach collection="saleoderDataDtos" item="item" index="index">
            WHEN SALEORDER_ID = #{item.saleorderId} THEN #{item.currentUserId}
        </foreach>
        END
        WHERE SALEORDER_ID IN
        <foreach collection="saleoderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>


    <select id="getSaleorderCouponTypeIdsList" resultType="java.lang.Integer">
        SELECT
            e.SALEORDER_ID
        FROM
            T_SALEORDER e
                LEFT JOIN T_SALEORDER_COUPON g ON e.SALEORDER_ID = g.SALEORDER_ID
        where 1 = 1

            AND (e.MOD_TIME >= #{startTime}
                AND e.MOD_TIME &lt;= #{endTime} )
           OR (e.ADD_TIME >= #{startTime}
            AND e.ADD_TIME &lt;= #{endTime})

        GROUP BY
            e.SALEORDER_ID
    </select>
    <select id="findCouponTypeBySaleorderId" resultType="java.util.Map">
        SELECT
        e.SALEORDER_ID saleorderId,
        CASE

        WHEN COUPON_TYPE = 1
        AND count( g.SALEORDER_ID ) > 0 THEN
        '1'
        WHEN count( g.SALEORDER_ID ) = 0 THEN
        '5'
        ELSE '5'
        END couponType
        FROM
        T_SALEORDER e
        LEFT JOIN T_SALEORDER_COUPON g ON e.SALEORDER_ID = g.SALEORDER_ID
        where 1 = 1
             and e.SALEORDER_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
         GROUP BY
        e.SALEORDER_ID
    </select>
    <update id="updateSaleorderCouponType">
        UPDATE T_SALEORDER_DATA SET COUPON_TYPE = #{couponType}, MOD_TIME = #{nowTime} WHERE SALEORDER_ID = #{saleorderId}
    </update>

    <update id="updateRealTotalAmount">
        UPDATE T_SALEORDER SET REAL_TOTAL_AMOUNT = #{realTotalAmount}  WHERE SALEORDER_ID = #{saleorderId}
    </update>


    <select id="getSaleorderRealTotalAmountIdsList" resultType="java.util.Map">
        SELECT
            a.SALEORDER_ID saleorderId,
            IF
                (
                        COALESCE (
                                    a.TOTAL_AMOUNT - IFNULL( abs( IF ( a.ORDER_TYPE IN ( 1, 5 ), b.SKU_REFUND_AMOUNT, b.tk_amount ) ), 0 ),
                                    0
                            ) &lt; 0,
                        0,
                        COALESCE (
                                    a.TOTAL_AMOUNT - IFNULL( abs( IF ( a.ORDER_TYPE IN ( 1, 5 ), b.SKU_REFUND_AMOUNT, b.tk_amount ) ), 0 ),
                                    0
                            )
                ) realTotalAmount
        FROM
            T_SALEORDER a
                LEFT JOIN (
                SELECT
                    sum( bb.NUM * cc.PRICE ) AS tk_amount,
                    sum( bb.SKU_REFUND_AMOUNT ) AS SKU_REFUND_AMOUNT,
                    aa.ORDER_ID,
                    aa.ATFER_SALES_STATUS,
                    aa.MOD_TIME
                FROM
                    T_AFTER_SALES aa
                        LEFT JOIN T_AFTER_SALES_GOODS bb ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
                        LEFT JOIN T_SALEORDER_GOODS cc ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
                WHERE
                    aa.TYPE = 539
                  AND aa.SUBJECT_TYPE = 535
                  AND aa.VALID_STATUS = 1
                  AND aa.ATFER_SALES_STATUS IN ( 1, 2 )
                  AND bb.GOODS_TYPE = 0
                GROUP BY
                    aa.ORDER_ID
            ) AS b ON a.SALEORDER_ID = b.ORDER_ID
        WHERE
            ( b.ATFER_SALES_STATUS = 2 AND b.MOD_TIME >= #{startTime} AND b.MOD_TIME &lt;= #{endTime} )
           OR ( a.MOD_TIME >= #{startTime} AND a.MOD_TIME &lt;= #{endTime} )
    </select>
    <select id="getSaleorderRealPayAmountIdsList" resultType="java.lang.Integer">
        SELECT b.RELATED_ID
        FROM
            T_CAPITAL_BILL a
                LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        WHERE
            a.TRADER_TYPE IN ( 1, 2, 4, 5 )
          AND (
                ( a.TRADER_TYPE IN ( 1, 4, 5 ) )
                OR ( a.TRADER_TYPE = 2 AND b.BUSSINESS_TYPE = 679 )
            )
          AND b.ORDER_TYPE = 1
          AND b.BUSSINESS_TYPE != 533
          AND ( a.ADD_TIME >= #{startTime} AND a.ADD_TIME &lt;= #{endTime} )
        group by b.RELATED_ID
    </select>
    <select id="findRealPayAmountBySaleorderId" resultType="java.util.Map">
        SELECT b.RELATED_ID saleorderId, COALESCE
        (
        sum(
        IF
        ( a.TRADER_TYPE = 1 OR a.TRADER_TYPE = 4, ABS( a.AMOUNT ),- ABS( a.AMOUNT ) )
        ),
        0
        ) realPayAmount
        FROM
        T_CAPITAL_BILL a
        LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        WHERE
        a.TRADER_TYPE IN ( 1, 2, 4, 5 )
        AND (
        ( a.TRADER_TYPE IN ( 1, 4, 5 ) )
        OR ( a.TRADER_TYPE = 2 AND b.BUSSINESS_TYPE = 679 )
        )
        AND b.ORDER_TYPE = 1
        AND b.BUSSINESS_TYPE != 533
             and b.RELATED_ID in
            <foreach collection="bizIds" item="bizId" index="index" close=")" open="(" separator=",">
                #{bizId}
            </foreach>
         group by  b.RELATED_ID
    </select>

    <select id="getSaleorderGoodsInfoById" resultType="com.vedeng.order.model.vo.SaleorderGoodsVo">
        SELECT
            sg.*,s.IS_NEW
        FROM
            T_SALEORDER_GOODS sg
                LEFT JOIN T_SALEORDER s ON sg.SALEORDER_ID = s.SALEORDER_ID
        WHERE
            SALEORDER_GOODS_ID = #{saleorderGoodsId}
    </select>
    <select id="getBuyStatusChangedGoodsFirstQuery" resultType="java.lang.Integer">
        SELECT
            bs.SALEORDER_GOODS_ID
        FROM
            T_BUYORDER_GOODS bg
                LEFT JOIN T_BUYORDER b ON b.BUYORDER_ID = bg.BUYORDER_ID
                LEFT JOIN T_R_BUYORDER_J_SALEORDER bs ON bs.BUYORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
                LEFT JOIN T_SALEORDER_GOODS sg ON bs.SALEORDER_GOODS_ID = sg.SALEORDER_GOODS_ID
                LEFT JOIN T_SALEORDER s ON s.SALEORDER_ID = sg.SALEORDER_ID
        WHERE
            b.STATUS BETWEEN 0
                AND 2
          AND s.COMPANY_ID = b.COMPANY_ID
          AND bg.MOD_TIME BETWEEN #{startTime} AND #{endTime}
        UNION
        SELECT
            a.ORDER_DETAIL_ID
        FROM
            T_AFTER_SALES_GOODS a
                LEFT JOIN T_AFTER_SALES b ON a.AFTER_SALES_ID = b.AFTER_SALES_ID
        WHERE
            a.GOODS_TYPE = 0
          AND b.ATFER_SALES_STATUS = 2
          AND b.TYPE = 539
          AND b.ADD_TIME BETWEEN #{startTime} AND #{endTime}
        UNION
        SELECT
            c.SALEORDER_GOODS_ID
        FROM
            T_AFTER_SALES_GOODS a
                LEFT JOIN T_BUYORDER_GOODS b ON a.ORDER_DETAIL_ID = b.BUYORDER_GOODS_ID
                LEFT JOIN T_R_BUYORDER_J_SALEORDER c ON b.BUYORDER_GOODS_ID = c.BUYORDER_GOODS_ID
                LEFT JOIN T_AFTER_SALES d ON a.AFTER_SALES_ID = d.AFTER_SALES_ID
                LEFT JOIN T_BUYORDER e ON e.BUYORDER_ID = b.BUYORDER_ID
        WHERE
            d.SUBJECT_TYPE = 536
          AND d.TYPE = 546
          AND d.ATFER_SALES_STATUS = 2
          AND e.STATUS BETWEEN 0
            AND 2
          AND d.MOD_TIME BETWEEN #{startTime} AND #{endTime}
    </select>
    <select id="getFailLeftAccountPeriodSaleorderList" resultType="java.lang.Integer">
        select SALEORDER_ID
        from T_SALEORDER where STATUS in (1,2) and ORDER_TYPE in (0,1,5,6,7,8,9) and REAL_TOTAL_AMOUNT > 0 and HAVE_ACCOUNT_PERIOD = 1
                           and ACCOUNT_PERIOD_AMOUNT > 0 and SALEORDER_ID > #{start} limit 1000
    </select>
    <select id="getSaleOrderDataBySaleOrderId" resultType="com.vedeng.finance.model.SaleorderData">
        SELECT
            sd.*
        FROM
            T_SALEORDER_DATA sd
        WHERE
            sd.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <update id="updateSaleorderRealPayAmount">
        UPDATE T_SALEORDER SET REAL_PAY_AMOUNT = #{realPayAmount}, MOD_TIME = #{nowTime} WHERE SALEORDER_ID = #{saleorderId}
    </update>
    <update id="updateSaleorderGoodsBuyStatus">
        UPDATE
        T_SALEORDER_GOODS
        SET
        MOD_TIME = #{nowTime},
        PURCHASE_STATUS_DATA = CASE
        <foreach collection="saleorderGoodsVos" item="item" index="index">
            WHEN SALEORDER_GOODS_ID = #{item.saleorderId} THEN #{item.purchaseStatusData}
        </foreach>
        END,
        BUY_NUM_DATA = CASE
        <foreach collection="saleorderGoodsVos" item="item" index="index">
            WHEN SALEORDER_GOODS_ID = #{item.saleorderId} THEN #{item.buyNum}
        </foreach>
        END
        WHERE SALEORDER_GOODS_ID IN
        <foreach collection="saleorderGoodsVos" index="index" item="item" open="(" separator="," close=")">
            #{item.saleorderId}
        </foreach>
    </update>
    <update id="updateUrageById">
        UPDATE T_SALEORDER_DATA
        SET SALEORDER_URAGE = 1
        WHERE
            SALEORDER_ID=#{saleorderId}
    </update>

    <select id="contractVerifyStatusBySaleOrderId" resultType="java.lang.Integer">
        SELECT CONTRACT_VERIFY_STATUS
        FROM T_SALEORDER_DATA
        WHERE SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
    </select>
</mapper>