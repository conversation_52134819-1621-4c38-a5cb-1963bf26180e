<%@page import="java.util.Date"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="GE授权书详情" scope="application" />
<%@ include file="../common/common.jsp"%>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/ge/auth/view.css?rnd=${resourceVersionKey}">

<%--	<%@ include file="customer_tag.jsp"%>--%>


<div class="content mt10 ">
	<div class="parts">
		<div class="mb15" style="margin-top: 12px;">
			<div class="add-tijiao tcenter">
				<c:if test="${(geAuthorization.status eq 0||geAuthorization.status eq 3)&&sessionScope.curr_user.userId==geAuthorization.creator}">
					<%--${sessionScope.curr_user.userId}--%><button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="javascrtpt:window.location.href='${pageContext.request.contextPath}/ge/authorization/editview.do?authorizationId=${geAuthorization.authorizationId}&geBussinessChanceId=${geAuthorization.geBussinessChanceId}'">编辑</button>
				</c:if>
				<c:if test="${(geAuthorization.status eq 0||geAuthorization.status eq 3)&&sessionScope.curr_user.userId==geAuthorization.creator}">
					<button type="button" class="bt-bg-style bg-light-blue bt-small mr10" onclick="doForAudit(${geAuthorization.authorizationId})">申请审核</button>
				</c:if>
				<c:if test="${geAuthorization.status eq 1 && geRole}">
					<button type="button" class="bt-bg-style bg-light-blue bt-small mr10" onclick="makeAudit(${geAuthorization.authorizationId})">审核通过</button>
				</c:if>
				<c:if test="${geAuthorization.status eq 1 && geRole}">
					<button type="button" class="bt-bg-style bg-light-orange bt-small mr10" onclick="makeAuditNo(${geAuthorization.authorizationId})">审核不通过</button>
				</c:if>
				<c:if test="${geAuthorization.status eq 0||geAuthorization.status eq 3&&sessionScope.curr_user.userId==geAuthorization.creator}">
					<button type="button" class="bt-bg-style bg-light-red bt-small mr10" onclick="doForClose(${geAuthorization.authorizationId})">关闭</button>
				</c:if>
			</div>
		</div>
	</div>
	<div class="parts">
		<div class="title-container title-container-blue">
			<div class="table-title nobor">基本信息</div>
		</div>
		<table class="table table-bordered table-striped table-condensed table-centered">
			<tbody>
				<tr>
					<td class="table-smaller">GE授权书编号</td>
					<td>${geAuthorization.authorizationNo}</td>
					<td class="table-smaller">授权书状态</td>
					<td>
						<c:choose>
							<c:when test="${geAuthorization.status eq 0}">待审核</c:when>
							<c:when test="${geAuthorization.status eq 1}">审核中</c:when>
							<c:when test="${geAuthorization.status eq 2}">审核通过</c:when>
							<c:when test="${geAuthorization.status eq 3}">审核不通过</c:when>
							<c:when test="${geAuthorization.status eq 4}">已关闭</c:when>
							<c:otherwise></c:otherwise>
						</c:choose>
					</td>
				</tr>
				<tr>
					<td>申请人</td>
					<td>${geAuthorization.creatorName}</td>
					<td>申请时间</td>
					<td><fmt:formatDate value="${geAuthorization.addTime}" pattern="yyyy.MM.dd HH:mm:ss"/></td>
				</tr>
				<tr>
					<td>产品类型</td>
					<td>
						<c:choose>
							<c:when test="${geAuthorization.type eq 1}">CT</c:when>
							<c:when test="${geAuthorization.type eq 2}">超声</c:when>
							<c:otherwise></c:otherwise>
						</c:choose>
					</td>
					<td>商机编号</td>
					<td>${geBusinessChance.geBussinessChanceNo}</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="parts">
		<div class="title-container title-container-blue">
			<div class="table-title nobor">
				资质上传情况
			</div>
		</div>
		<table class="table table-bordered table-striped table-condensed table-centered">
			<tbody>
				<tr>
					<td class="table-smallest">营业执照</td>
					<td style="text-align: left">
						<c:choose>
							<c:when test="${yy ne null }">
								<c:forEach items="${yy }" var="bus" varStatus="st">
									<c:if test="${bus.uri ne null && not empty bus.uri}">
										<a href="http://${bus.domain}${bus.uri}" target="_blank">营业执照- ${st.index + 1}</a>&nbsp;&nbsp;
									</c:if>
								</c:forEach>
							</c:when>
						</c:choose>&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td class="table-smallest">二类医疗资质</td>
					<td style="text-align: left">
						<c:choose>
							<c:when test="${el ne null }">
								<c:forEach items="${el }" var="bus" varStatus="st">
									<c:if test="${bus.uri ne null && not empty bus.uri}">
										<a href="http://${bus.domain}${bus.uri}" target="_blank">二类医疗资质- ${st.index + 1}</a>&nbsp;&nbsp;
									</c:if>
								</c:forEach>
							</c:when>
						</c:choose>&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td class="table-smallest">三类医疗资质</td>
					<td style="text-align: left">
						<c:choose>
							<c:when test="${sl ne null }">
								<c:forEach items="${sl }" var="bus" varStatus="st">
									<c:if test="${bus.uri ne null && not empty bus.uri}">
										<a href="http://${bus.domain}${bus.uri}" target="_blank">三类医疗资质- ${st.index + 1}</a>&nbsp;&nbsp;
									</c:if>
								</c:forEach>
							</c:when>
						</c:choose>&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td class="table-smallest">诚信声明</td>
					<td style="text-align: left">
						<c:choose>
							<c:when test="${cx ne null }">
								<c:forEach items="${cx }" var="bus" varStatus="st">
									<c:if test="${bus.uri ne null && not empty bus.uri}">
										<a href="http://${bus.domain}${bus.uri}" target="_blank">诚信声明- ${st.index + 1}</a>&nbsp;&nbsp;
									</c:if>
								</c:forEach>
							</c:when>
						</c:choose>&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td class="table-smallest">使用二级分销商声明</td>
					<td style="text-align: left">
						<c:choose>
							<c:when test="${sy ne null }">
								<c:forEach items="${sy }" var="bus" varStatus="st">
									<c:if test="${bus.uri ne null && not empty bus.uri}">
										<a href="http://${bus.domain}${bus.uri}" target="_blank">使用二级分销商声明- ${st.index + 1}</a>&nbsp;&nbsp;
									</c:if>
								</c:forEach>
							</c:when>
						</c:choose>&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td class="table-smallest">信用查询结果</td>
					<td style="text-align: left">
						<c:choose>
							<c:when test="${xy ne null }">
								<c:forEach items="${xy }" var="bus" varStatus="st">
									<c:if test="${bus.uri ne null && not empty bus.uri}">
										<a href="http://${bus.domain}${bus.uri}" target="_blank">信用查询结果- ${st.index + 1}</a>&nbsp;&nbsp;
									</c:if>
								</c:forEach>
							</c:when>
						</c:choose>&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td class="table-smallest">招标文件</td>
					<td style="text-align: left">
						<c:choose>
							<c:when test="${zb ne null }">
								<c:forEach items="${zb }" var="bus" varStatus="st">
									<c:if test="${bus.uri ne null && not empty bus.uri}">
										<a href="http://${bus.domain}${bus.uri}" target="_blank">招标文件- ${st.index + 1}</a>&nbsp;&nbsp;
									</c:if>
								</c:forEach>
							</c:when>
						</c:choose>&nbsp;&nbsp;&nbsp;
					</td>
				</tr>
				<tr>
					<td class="table-smallest">其他附件</td>
					<td style="text-align: left">
						<c:choose>
							<c:when test="${qt ne null }">
								<c:forEach items="${qt }" var="bus" varStatus="st">
									<c:if test="${bus.uri ne null && not empty bus.uri}">
										<a href="http://${bus.domain}${bus.uri}" target="_blank">其他附件- ${st.index + 1}</a>&nbsp;&nbsp;
									</c:if>
								</c:forEach>
							</c:when>
						</c:choose>&nbsp;&nbsp;&nbsp;
					</td>
				</tr>

				<tr>
					<td class="table-smallest">备注</td>
					<td style="text-align: left">
						${geAuthorization.content}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="parts">
		<div class="title-container title-container-blue">
			<div class="table-title nobor">
				变更日志
			</div>
		</div>
        <table class="table table-bordered table-striped table-condensed table-centered">
			<tbody>
			<tr>
				<td class="table-smaller">操作人</td>
				<td class="table-smaller">操作时间</td>
				<td class="table-smaller">变更内容</td>
			</tr>
			<c:choose>
				<c:when test="${geAuthorizationLogs ne null }">
					<c:forEach items="${geAuthorizationLogs }" var="data" varStatus="st">
						<c:if test="${st.index<=4}">
							<tr >
								<td class="table-smaller">${data.creatorName}</td>
								<td class="table-smaller"><fmt:formatDate value="${data.addTime}" pattern="yyyy.MM.dd HH:mm:ss"/></td>
								<td class="table-smaller">${data.content}</td>
							</tr>
						</c:if>
						<c:if test="${st.index>4}">
							<tr name="log-hidde" style="display: none">
								<td class="table-smaller">${data.creatorName}</td>
								<td class="table-smaller"><fmt:formatDate value="${data.addTime}" pattern="yyyy.MM.dd HH:mm:ss"/></td>
								<td class="table-smaller">${data.content}</td>
							</tr>
						</c:if>
					</c:forEach>
					<c:if test="${geAuthorizationLogs.size()>5}">
						<tr id="showId">
							<td class="table-smaller" colspan="3" ><span style="color: #3384ef;cursor:pointer" onclick="showMore()">展开更多</span></td>
						</tr>
					</c:if>
				</c:when>
			</c:choose>
			<c:if test="${geAuthorizationLogs eq null || empty geAuthorizationLogs || geAuthorizationLogs.size()==0}">
				<!-- 查询无结果弹出 -->
				<tr>
					<td class="table-smaller" colspan="3">暂无日志记录。</td>
				</tr>
			</c:if>
			</tbody>
		</table>
	</div>
	<div class="parts">
		<div class="title-container title-container-blue">
			<div class="table-title nobor">
				审核记录
			</div>
		</div>
		<table class="table table-bordered table-striped table-condensed table-centered">
			<tbody>
			<tr>
				<td class="table-smaller">操作人</td>
				<td class="table-smaller">操作时间</td>
				<td class="table-smaller">操作事项</td>
				<td class="table-smaller">备注</td>
			</tr>
			<c:choose>
				<c:when test="${geAuthorizationVerifyData ne null || !empty geAuthorizationVerifyData }">
					<c:forEach items="${geAuthorizationVerifyData }" var="data" varStatus="st">
							<tr >
								<td class="table-smaller">${data.creatorName}</td>
								<td class="table-smaller"><fmt:formatDate value="${data.addTime}" pattern="yyyy.MM.dd HH:mm:ss"/></td>
								<c:choose>
									<c:when test="${data.stats eq 2}"><td class="table-smaller">发起审核</td></c:when>
									<c:when test="${data.stats eq 1}"><td class="table-smaller">审核通过</td></c:when>
									<c:when test="${data.stats eq 0}"><td class="table-smaller">审核不通过</td></c:when>
									<c:when test="${data.stats eq 3}"><td class="table-smaller">关闭审核</td></c:when>
									<c:otherwise></c:otherwise>
								</c:choose>
								<td class="table-smaller">${data.content}</td>
							</tr>
<%--						<c:if test="${st.index>4}">--%>
<%--							<tr name="log-hidde" style="display: none">--%>
<%--								<td class="table-smaller">${data.creatorName}</td>--%>
<%--								<td class="table-smaller"><fmt:formatDate value="${data.addTime}" pattern="yyyy.MM.dd HH:mm:ss"/></td>--%>
<%--								<td class="table-smaller">${data.content}</td>--%>
<%--							</tr>--%>
<%--						</c:if>--%>
					</c:forEach>
<%--					<c:if test="${geAuthorizationLogs.size()>5}">--%>
<%--						<tr id="showId">--%>
<%--							<td class="table-smaller" colspan="4" ><span style="color: #3384ef;cursor:pointer" onclick="showMore()">展开更多</span></td>--%>
<%--						</tr>--%>
<%--					</c:if>--%>
				</c:when>

			</c:choose>
			<c:if test="${geAuthorizationVerifyData eq null || empty geAuthorizationVerifyData || geAuthorizationVerifyData.size()==0}">
				<!-- 查询无结果弹出 -->
				<tr>
					<td class="table-smaller" colspan="4">暂无审核记录。</td>
				</tr>
			</c:if>
			</tbody>

		</table>
	</div>



	<input type="hidden" name="formToken" value="${formToken}"/>
	<input type="hidden" id="taskName" name="taskName" value="${taskInfo.name}"/>


</div>

</body>
<script type="text/javascript"
		src='<%= basePath %>static/js/ge/view_authorization.js?rnd=${resourceVersionKey}'></script>
</html>
