package com.vedeng.activiti.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.activiti.service.ActivitiAssigneeService;
import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.system.service.OrganizationApiService;
import com.vedeng.goods.dao.RCategoryJUserMapper;
import com.vedeng.goods.service.GoodsChannelPriceService;
import com.vedeng.homepage.service.MyHomePageService;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.QuoteService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.model.vo.ParamsConfigVo;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("activitiAssigneeService")
public class ActivitiAssigneeServiceImpl implements ActivitiAssigneeService {

	public static Logger logger = LoggerFactory.getLogger(ActivitiAssigneeServiceImpl.class);

	@Resource
    private WebServiceContext webServiceContext;
    
    @Autowired
    @Qualifier("roleMapper")
    private RoleMapper roleMapper;
    @Autowired
    @Qualifier("userService")
    private UserService userService;
    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;
    @Autowired
    @Qualifier("buyorderService")
    private BuyorderService buyorderService;
    @Autowired
    @Qualifier("quoteService")
    private QuoteService quoteService;
    @Autowired
    @Qualifier("myHomePageService")
    private MyHomePageService myHomePageService;
    @Autowired
    @Qualifier("orgService")
    private OrgService orgService;
    
    @Autowired
    @Qualifier("rCategoryJUserMapper")
    private RCategoryJUserMapper rCategoryJUserMapper;
    
    @Autowired
    @Qualifier("goodsChannelPriceService")
    private GoodsChannelPriceService goodsChannelPriceService;
    
    @Autowired
    @Qualifier("traderCustomerService")
    private TraderCustomerService traderCustomerService;// 客户-交易者

	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	@Autowired
	private OrganizationApiService organizationApiService;

    /**
     * 返回Session中的用户Name
     */
    @Override
    public String getSessionUser() {
	 ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
	 HttpServletRequest request =  ra.getRequest();
	 User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
	return curr_user.getUsername();
    }
    /**
     * 根据角色名称获取对应角色的人
     */
    @Override
    public List<String> getUserListByRole(String roleName) {
	 ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
	 HttpServletRequest request=null ;
	 if(ra!=null){
			request =  ra.getRequest();
	 }
	 User curr_user=null;
	 if(request!=null) {
		 curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		 if(curr_user==null||curr_user.getCompanyId()==null){
		 	curr_user=new User();
		 	curr_user.setCompanyId(1);
		 }
	 }else{
	 	curr_user=new User();
	 	curr_user.setCompanyId(1);
	    curr_user.setUsername("njadmin");
	    curr_user.setUserId(1);
	 }
	 //角色名称，当前登陆人的所属公司
	List<String> userNameList= roleMapper.getUserNameByRoleName(roleName,curr_user.getCompanyId());
	if(userNameList.isEmpty()){
	    userNameList.add(curr_user.getUsername());
	}
	return userNameList;
    }

	@Override
	public List<String> afterSaleGetSkuManageAndRole(String roleName,String param) {
		List<String> userListByRole = getUserListByRole(roleName);
		if (StrUtil.isNotEmpty(param)) {
			List<String> split = StrUtil.split(param, ",");
			if (CollUtil.isNotEmpty(split)) {
				userListByRole.addAll(split);
				userListByRole = userListByRole.stream().distinct().collect(Collectors.toList());
			}
		}
		return userListByRole;
	}

	@Override
	public List<String> findByUserIdStr(String userIdStr) {
		if (StringUtils.isBlank(userIdStr)) {
			return Collections.emptyList();
		}
		List<Integer> userIdList = StrUtil.split(userIdStr, ",").stream().map(Integer::parseInt).collect(Collectors.toList());
		return userService.getUserByUserIds(userIdList).stream().map(User::getUsername).collect(Collectors.toList());
	}

	@Override
	public List<String> getExpenseAfterSalesUser(String username) {
		Set<String> result = new HashSet<>();
		User assigneeObj = userService.getUserParentInfo(username, ErpConst.NJ_COMPANY_ID);
		List<String> userNames = getUserListByRole("供应主管");
		if (assigneeObj != null && assigneeObj.getpUsername() != null) {
			result.add(assigneeObj.getpUsername());
		} else {
			result.add(username);
		}
		if (CollectionUtils.isNotEmpty(userNames)) {
			result.addAll(userNames);
		}
		List<String> userNamess = getUserListByRole("产品总监");
		if (CollectionUtils.isNotEmpty(userNamess)) {
			result.addAll(userNamess);
		}
		return new ArrayList<>(result);
	}

	/**
     * 根据对人的名称获取他的直接上级
     */
    @Override
    public String getUserParentsUser(String userName) {
	 User assigneeObj = userService.getUserParentInfo(userName, ErpConst.NJ_COMPANY_ID);
	 return assigneeObj != null && assigneeObj.getpUsername() != null ? assigneeObj.getpUsername() : userName;
    }
    /**
     * 根据部门orgId和对应的职位等级获取对应的人名字的集合
     */
    @Override
    public List<String> getUserByLevel(Integer orgId,Integer level){
	 ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
	 HttpServletRequest request =  ra.getRequest();
	 User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
	List<User> userList = userService.getUserByPositLevel(orgId, level);
	logger.info("ActivitiAssigneeServiceImpl.getUserByLevel，查询userList入参:{},{},结果userList={}",orgId,level, JSONObject.toJSONString(userList));
	List<String> userNameList= new ArrayList<>();
	if (null == userList){
		// 统计用，可以删除
		logger.info("监控到未查到指定组织内的人员，orgId:{},level:{}",orgId,level);
	}
	if(CollectionUtils.isNotEmpty(userList)){
	    for(User u :userList){
			userNameList.add(u.getUsername());
	    }
	}
	if(userNameList.isEmpty()){
	    userNameList.add(curr_user.getUsername());
	}
	logger.info("ActivitiAssigneeServiceImpl.getUserByLevel出参：{}",JSONObject.toJSON(userNameList));
	return userNameList;
    }
    /**
     * 根据分类ID获取对应的分类归属人的名字集合
     */
    @Override
    public List<String> getUserByCategory(Integer categoryId){
	ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
	HttpServletRequest request =  ra.getRequest();
	User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
	String categoryUsers=rCategoryJUserMapper.getUserByCategoryNm(categoryId, curr_user.getCompanyId());
	List<String> result = new ArrayList<>();
	if(categoryUsers!=null){
	     result = Arrays.asList(categoryUsers.split(","));
	     return result;
	}else{
	    ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
	    paramsConfigVo.setCompanyId(curr_user.getCompanyId());
	    paramsConfigVo.setParamsKey(107);
	    ParamsConfigVo quote = myHomePageService.getParamsConfigVoByParamsKey(paramsConfigVo);
	    User defaultUser = userService.getUserById(Integer.parseInt(quote.getParamsValue()));
	    result.add(defaultUser.getUsername());
	    return result;
	}
    }
    /**
     * 根据ID获取对应单的产品分类归属人的名字集合
     * type=1 订单 type=2 报价单 type=3 采购单 type = 4 订单修改
     * userType = 445 产品专员  userType = 609 产品经理  userType = 444 产品主管
     */
    @Override
    public List<String> getUserByOrderId(Integer orderId, Integer type, Integer userType) {
	ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
	HttpServletRequest request =  ra.getRequest();
	User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
	List<String> users = new ArrayList<>();
	List<Integer> categoryList = new ArrayList<>();
	if(type == 1){
	    //订单
	     Saleorder saleorder=new Saleorder();
	     saleorder.setSaleorderId(orderId);
	     saleorder = saleorderService.getBaseSaleorderInfo(saleorder);
	     List<SaleorderGoods> SaleorderGoodsList = saleorderService.getSaleorderGoodsById(saleorder);
	     // 根据客户ID查询客户信息
	     TraderCustomerVo customer = traderCustomerService.getCustomerBussinessInfo(saleorder.getTraderId());
	     SaleorderGoodsList = goodsChannelPriceService.getSaleChannelPriceList(saleorder.getSalesAreaId(), saleorder.getCustomerNature(),customer.getOwnership(), SaleorderGoodsList);
	     if(!SaleorderGoodsList.isEmpty()){
			 for(SaleorderGoods s:SaleorderGoodsList){
			     if(s.getIsDelete() == 0){
				 BigDecimal settlementPrice= saleorderService.getSaleorderGoodsSettlementPrice(s.getGoodsId(),curr_user.getCompanyId());
				 //区域管制，货期大于核价货期，直发，报价小于结算价,参考成本为0
				 if(s.getAreaControl().equals(1) || !s.getDeliveryCycle().equals(s.getChannelDeliveryCycle()) || s.getDeliveryDirect().equals(1) ||  (null != settlementPrice && settlementPrice.compareTo(s.getPrice())==1)  || s.getReferenceCostPrice().compareTo(BigDecimal.ZERO)==0){
				     if(null != s.getGoods().getCategoryId()){
					 categoryList.add(s.getGoods().getCategoryId());
				     }
				 }
			     }
			     
			 }
	     }
	    
	}else if(type == 2){
	    //报价单
	    Map<String, Object> quoteorderGoodsInfo= quoteService.getQuoteGoodsByQuoteId(orderId, curr_user.getCompanyId(),request.getSession(),null,null);
	    List<QuoteorderGoods> quoteList= (List<QuoteorderGoods>) quoteorderGoodsInfo.get("quoteGoodsList");
	    if(!quoteList.isEmpty()){
			for(QuoteorderGoods qg:quoteList){
			    if(qg.getIsDelete() == 0){
			    categoryList.add(qg.getCategoryId());
			    }
			}
	    }
		
	}else if(type == 3){
	    //采购单
	    Buyorder buyorder = new Buyorder();
	    buyorder.setBuyorderId(orderId);
	    BuyorderVo buyorderInfo= buyorderService.getBuyorderVoDetail(buyorder, curr_user);
	    if(!buyorderInfo.getBuyorderGoodsVoList().isEmpty()){
			for(BuyorderGoodsVo bg:buyorderInfo.getBuyorderGoodsVoList()){
			    if(bg.getIsDelete() == 0){
			    categoryList.add(bg.getCategoryId());
			    }
			}
	    }
	    
	}else if(type == 4){
	  //订单
	     Saleorder saleorder=new Saleorder();
	     saleorder.setSaleorderId(orderId);
	     saleorder = saleorderService.getBaseSaleorderInfo(saleorder);
	     List<SaleorderGoods> SaleorderGoodsList = saleorderService.getSaleorderGoodsById(saleorder);
	     if(!SaleorderGoodsList.isEmpty()){
			 for(SaleorderGoods s:SaleorderGoodsList){
			     if(s.getIsDelete() == 0){
				     if(null != s.getGoods().getCategoryId()){
					 categoryList.add(s.getGoods().getCategoryId());
				     }
			     }
			     
			 }
	     }
	   
	}
	//如果分类不为空，去查询分类对应归属
		List<User> userList = new ArrayList<>();
		if(!categoryList.isEmpty()) {
			 userList=rCategoryJUserMapper.getTypeUserByCategoryIds(categoryList,curr_user.getCompanyId(), userType);
			 //分类去重
			 HashSet h = new HashSet(categoryList);   
			 categoryList.clear();   
			 categoryList.addAll(h);
			//如果有分类没有归属
			 if(userList.size() != categoryList.size()){
			     	    ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
				    paramsConfigVo.setCompanyId(curr_user.getCompanyId());
				    paramsConfigVo.setParamsKey(107);
				    ParamsConfigVo quote = myHomePageService.getParamsConfigVoByParamsKey(paramsConfigVo);
				    User defaultUser = userService.getUserById(Integer.parseInt(quote.getParamsValue()));
				    defaultUser.setOwners(defaultUser.getUsername());
				    userList.add(defaultUser);
			 }
		}
		if(!userList.isEmpty()){
		    for(User u:userList){
			if(null != u.getOwners()){
			    List<String> ownerList = Arrays.asList(u.getOwners().split(",")); 
			     users.addAll(ownerList);
			}
		    }
		}
		users = new ArrayList(new HashSet(users));
	if(users.isEmpty()){
	    ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
	    paramsConfigVo.setCompanyId(curr_user.getCompanyId());
	    paramsConfigVo.setParamsKey(107);
	    ParamsConfigVo quote = myHomePageService.getParamsConfigVoByParamsKey(paramsConfigVo);
	    User defaultUser = userService.getUserById(Integer.parseInt(quote.getParamsValue()));
	    users.add(defaultUser.getUsername());
	    return users;
	}else{
	    return users;
	}
	
    }
    
    /**
     * 获取售后负责人
     */
    @Override
    public String getAfterSaleUser(Integer orgId){
//	 ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//	 HttpServletRequest request =  ra.getRequest();
//	 User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
	 ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
	 paramsConfigVo.setCompanyId(1);
	 paramsConfigVo.setParamsKey(109);
	 List<ParamsConfigVo> params = myHomePageService.getParamsConfigVoList(paramsConfigVo);
	 List<Organization> organizationList= orgService.getParentOrgList(orgId);
	 for(ParamsConfigVo pcv:params){
	     for(Organization o:organizationList){
		 if(pcv.getTitle().equals(o.getOrgId().toString())){
		     return pcv.getParamsValue();
		 }
	     }
	 }
	    ParamsConfigVo paramsConfigVoInfo = new ParamsConfigVo();
	    paramsConfigVoInfo.setCompanyId(1);
	    paramsConfigVoInfo.setParamsKey(108);
	    ParamsConfigVo paramsInfo = myHomePageService.getParamsConfigVoByParamsKey(paramsConfigVoInfo);
	    return paramsInfo.getParamsValue();
    }
    /**
     * 根据订单修改信息返回对应的采购人员
     */
    @Override
    public List<String> getBuyorderUserIdBySMA(SaleorderModifyApply saleorderModifyApply){
    	//销售订单产品id集合
    	List<Integer> saleorderGoodsIds = new ArrayList<>();
    	//修改的商品信息
    	List<SaleorderModifyApplyGoods> goodsList= saleorderModifyApply.getGoodsList();
    	for(SaleorderModifyApplyGoods smag: goodsList) {
    		saleorderGoodsIds.add(smag.getSaleorderGoodsId());
    	}
    	//根据销售订单产品id集合获取对应的采购归属人id
    List<Integer> buyorderUserIds= buyorderService.getBuyorderUserBySaleorderGoodsIds(saleorderGoodsIds);
    List<String> buyorderUserNames = new ArrayList<>();
    if(null != buyorderUserIds && !buyorderUserIds.isEmpty()) {
    		List<User> buyorderUsers= userService.getUserByUserIds(buyorderUserIds);
    		for(User u:buyorderUsers) {
    			buyorderUserNames.add(u.getUsername());
    		}
    }
    //如果找不到对应的采购归属人，返回默认的归属人
    if(buyorderUserNames.isEmpty()) {
    		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    		HttpServletRequest request =  ra.getRequest();
    		User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
    		ParamsConfigVo paramsConfigVo = new ParamsConfigVo();
    	    paramsConfigVo.setCompanyId(curr_user.getCompanyId());
    	    paramsConfigVo.setParamsKey(107);
    	    ParamsConfigVo quote = myHomePageService.getParamsConfigVoByParamsKey(paramsConfigVo);
    	    User defaultUser = userService.getUserById(Integer.parseInt(quote.getParamsValue()));
    	    buyorderUserNames.add(defaultUser.getUsername());
    	    return buyorderUserNames;
    }else {
	    	return buyorderUserNames;
    }
    }

	/**
	 * 去重的用户名
	 * @param skuId
	 * @return
	 */
	@Override
	public List<String> getBelongsBySkuID(Integer skuId) {
    	String assistant=userService.getAssistantBySkuId(skuId);
		String manager=userService.getManagerBySkuId(skuId);
		List<String> list=new ArrayList<>();
		if (StringUtils.isNotEmpty(assistant)){
			list.add(assistant);
		}
		if (StringUtils.isNotEmpty(manager)&&!list.contains(manager)){
			list.add(manager);
		}
		return list;
	}



	@Override
	public List<String> getChiefByUserId(String roleName) {
    	User user=new User();
    	user.setUsername(roleName);
    	user.setCompanyId(1);
    	User userResult=userMapper.getUserByName2(user);
		List<String> strings=new ArrayList<>();
		if (userResult!=null) {
			List<Position> positionList = userMapper.userPositionOrganization(userResult.getUserId(), 1);
			if (positionList!=null) {
				for (Position position : positionList) {
					if (position==null){
						continue;
					}
					List<User> userList = userService.getUserByPositLevel(position.getOrgId(), 442);
					if (CollectionUtils.isNotEmpty(userList)) {
						for (User u: userList) {
							if (u==null){
								continue;
							}
							strings.add(u.getUsername());
						}
					}
				}
			}
		}
			return strings;
	}

	@Override
	public List<String> getBelongManagerBySkuId(Integer skuId) {
		String manager=userService.getManagerBySkuId(skuId);
		List<String> list=new ArrayList<>();
		if (StringUtils.isNotEmpty(manager)&&!list.contains(manager)){
			list.add(manager);
		}
		return list;
	}

	/**
	 * 递归找上级，到David为止
	 * @param userName
	 * @return
	 */
	@Override
	public String getTopDepartmentLeader(String userName,int num) {
		if (num>10){
			return null;
		}
		User assigneeObj = userService.getUserParentInfo(userName, ErpConst.NJ_COMPANY_ID);
		if (assigneeObj==null||assigneeObj.getParentId()==null||StringUtils.isBlank(assigneeObj.getpUsername())){
			return null;
		}
		Integer parentId = assigneeObj.getParentId();
		Integer david=50;
		if(david.equals(parentId)){
			return userName;
		}
		return getTopDepartmentLeader(assigneeObj.getpUsername(),num+1);
	}

	/**
	 *根据职位获取userList
	 */
	@Override
	public List<String> getUserListByPosition(String positionName) {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request=null ;
		if(ra!=null){
			request =  ra.getRequest();
		}
		User curr_user=null;
		if(request!=null) {
			curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			if(curr_user==null||curr_user.getCompanyId()==null){
				curr_user=new User();
				curr_user.setCompanyId(1);
			}
		}else{
			curr_user=new User();
			curr_user.setCompanyId(1);
			curr_user.setUsername("njadmin");
			curr_user.setUserId(1);
		}
		//角色名称，当前登陆人的所属公司
		List<String> userNameList= userMapper.getUserListByPositionName(positionName);
		if(userNameList.isEmpty()){
			userNameList.add(curr_user.getUsername());
		}
		return userNameList;
	}

	@Override
	public List<Integer> getUserIdListByPosition(String positionName) {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request=null ;
		if(ra!=null){
			request =  ra.getRequest();
		}
		User curr_user=null;
		if(request!=null) {
			curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			if(curr_user==null||curr_user.getCompanyId()==null){
				curr_user=new User();
				curr_user.setCompanyId(1);
			}
		}else{
			curr_user=new User();
			curr_user.setCompanyId(1);
			curr_user.setUsername("njadmin");
			curr_user.setUserId(1);
		}
		//角色名称，当前登陆人的所属公司
		List<Integer> userNameList= userMapper.getUserIdListByPositionName(positionName);
		return userNameList;
	}

	@Override
	public List<String> getUserListByPositionId(Integer positionId) {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request=null ;
		if(ra!=null){
			request =  ra.getRequest();
		}
		User curr_user=null;
		if(request!=null) {
			curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			if(curr_user==null||curr_user.getCompanyId()==null){
				curr_user=new User();
				curr_user.setCompanyId(1);
			}
		}else{
			curr_user=new User();
			curr_user.setCompanyId(1);
			curr_user.setUsername("njadmin");
			curr_user.setUserId(1);
		}
		return userMapper.getUserListByPositionId(positionId);
	}
}
