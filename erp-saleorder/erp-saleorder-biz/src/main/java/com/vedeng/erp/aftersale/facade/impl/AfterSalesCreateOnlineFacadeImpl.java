package com.vedeng.erp.aftersale.facade.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.component.AfterSalesOrderService;
import com.vedeng.aftersales.component.AfterSalesOrderServiceFactory;
import com.vedeng.aftersales.component.dto.AfterSaleOrderAddDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderCloseDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderModifyDto;
import com.vedeng.aftersales.component.exception.AfterSaleException;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.aftersale.facade.AfterSalesCreateOnlineFacade;
import com.vedeng.erp.saleorder.service.AfterSaleAuditInfoService;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.system.service.VerifiesRecordService;
import com.wms.service.context.ThreadLocalContext;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 售后在线创建
 */
@Service
@Slf4j
public class AfterSalesCreateOnlineFacadeImpl implements AfterSalesCreateOnlineFacade {


    @Autowired // 自动装载
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    /**
     * 客户类型 1客户，2供应商
     */
    private final Integer TRADER_TYPE_CUSTOMER = 1;

    @Resource
    protected AfterSalesService afterSalesOrderService;
    @Autowired
    private AfterSaleAuditInfoService afterSaleAuditInfoService;
    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("saleorderService")
    protected SaleorderService saleorderService;

    @Autowired
    @Qualifier("verifiesRecordService")
    protected VerifiesRecordService verifiesRecordService;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private AfterSalesOrderServiceFactory afterSalesOrderServiceFactory;

    @Resource
    private UserMapper userMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    /**
     * BD创建售后单
     * @param afterSaleOrderAddDto
     * @throws Exception
     */
    @Override
    @Transactional
    public void createAfterSalesOrder(AfterSaleOrderAddDto afterSaleOrderAddDto) throws Exception {
        AfterSalesOrderService afterSalesOrderService = afterSalesOrderServiceFactory.createAfterSalesInstance(afterSaleOrderAddDto.getAfterSalesType());
        afterSalesOrderService.createAfterSalesOrder(afterSaleOrderAddDto);
    }



    /**
     * 启动审核流程
     *
     * @param request
     * @throws Exception
     */
    @Override
    @Transactional
    public void startAuditProcess(HttpServletRequest request, Boolean isAuto) throws Exception {
        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");
        AfterSalesVo afterSales = new AfterSalesVo();
        afterSales.setAfterSalesId(afterSaleOrder.getAfterSalesId());
        afterSales.setTraderType(1);
        // 审核中
        afterSales.setStatus(1);
        afterSales.setCompanyId(1);
        afterSales.setModTime(DateUtil.sysTimeMillis());
        afterSales.setUpdater(2);
        ResultInfo<?> res = afterSalesOrderService.editApplyAudit(afterSales);
        if (res == null) {
            log.info("修改售后单失败");
            throw new AfterSaleValidateException("修改售后单失败");
        }

        Map<String, Object> variableMap = Maps.newHashMap();
        // 查询当前订单的一些状态
        AfterSalesVo afterSalesInfo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);

        setAfterSaleGoodType(afterSalesInfo);

        Saleorder saleorder = ThreadLocalContext.get("saleorder");
        //查询归属销售
        User user = userMapper.getUserByTraderId(saleorder.getTraderId(), ErpConst.ONE);
        // 查询归属销售组织关系
        User userOrg = userMapper.getUserInfoByTraderId(saleorder.getTraderId(), TRADER_TYPE_CUSTOMER);
        // 开始生成流程(如果没有taskId表示新流程需要生成)
        variableMap.put("afterSalesInfo", afterSalesInfo);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("processDefinitionKey", "afterSalesVerify");
        variableMap.put("businessKey", "afterSalesVerify_" + afterSalesInfo.getAfterSalesId());
        variableMap.put("relateTableKey", afterSalesInfo.getAfterSalesId());
        variableMap.put("relateTable", "T_AFTER_SALES");
        variableMap.put("orgId", userOrg.getOrgId());
        variableMap.put("skuManages", "");
        variableMap.put("isVirtualVerify", false);
        Set<String> vitualAudits = new HashSet<>();
        List<Integer> vitualUserIds = Lists.newArrayList();
        //VDERP-12089【金蝶对接】【采购售后模块】销售单直发售后增加供应链审核以及不支持采购售后的直普发修改start
        if(CollectionUtils.isNotEmpty(afterSalesInfo.getAfterSalesGoodsList())) {
            //是否走虚拟商品-供应链审核
            if(AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesInfo.getType())){
                List<AfterSalesGoodsVo> afterSalesGoodsVos = afterSalesInfo.getAfterSalesGoodsList().stream().filter(x -> Objects.nonNull(x.getOrderDetailId())).collect(Collectors.toList());
                List<Integer> saleorderGoodsIds = afterSalesGoodsVos.stream().map(AfterSalesGoodsVo::getOrderDetailId).collect(Collectors.toList());
                //获取满足条件的sku列表
                List<String> virtualSkuList = saleorderGoodsMapper.getVirtualGoodsSkuNoList(saleorderGoodsIds);
                if(CollectionUtils.isNotEmpty(virtualSkuList)){
                    //清空审核记录
                    afterSaleAuditInfoService.clearAuditInfoByAfterSaleId(afterSalesInfo.getAfterSalesId());
                    //获取虚拟商品对应的产品归属人列表
                    List<ProductManageAndAsistDto> manageAndAsistDtoList = coreSkuMapper.batchQueryProductManageAndAsist(virtualSkuList);
                    vitualAudits= afterSaleAuditInfoService.getProductManageAndAsistNameList(manageAndAsistDtoList);
                    vitualUserIds=afterSaleAuditInfoService.getProductManageAndAsistIdList(manageAndAsistDtoList);
                    if (CollectionUtils.isNotEmpty(vitualAudits)){
                        variableMap.put("isVirtualVerify", true);
                        //初始化审核数据
                        afterSaleAuditInfoService.insertAfterSaleAuditInfo(manageAndAsistDtoList,afterSalesInfo);
                    }
                }
            }

            List<AfterSalesGoodsVo> haveDirectGoodsList = afterSalesInfo.getAfterSalesGoodsList().stream().filter(
                    item -> ErpConst.ONE.equals(item.getDeliveryDirect())).collect(Collectors.toList());
            if ((AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesInfo.getType())
                    || AfterSalesProcessEnum.AFTERSALES_HH.getCode().equals(afterSalesInfo.getType()))
                    && CollectionUtils.isNotEmpty(haveDirectGoodsList)) {
                //1、销售售后单为销售退货/销售换货
                //2、包含退/换货方式为直发的商品
                //3、直发退货的SKU发货数量大于0
                variableMap.put("isNeedSupplyVerify", false);
                for (AfterSalesGoodsVo afterSalesVo : haveDirectGoodsList) {
                    SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(afterSalesVo.getOrderDetailId());
                    if (Objects.nonNull(saleorderGoods) && Objects.nonNull(saleorderGoods.getDeliveryNum()) && saleorderGoods.getDeliveryNum() > 0) {
                        variableMap.put("isNeedSupplyVerify", true);
                        // 将销售售后单商品的产品归属人加入
                        List<String> skus = afterSalesInfo.getAfterSalesGoodsList().stream().map(AfterSalesGoodsVo::getSku).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(skus)) {
                            Set<String> productManageAndAsistNameList = getProductManageAndAsistNameList(skus);
                            if (CollUtil.isNotEmpty(productManageAndAsistNameList)) {
                                variableMap.put("skuManages", CollUtil.join(productManageAndAsistNameList,","));
                            }
                        }
                        break;
                    }
                }


            } else {
                variableMap.put("isNeedSupplyVerify", false);
            }
        }else {
            variableMap.put("isNeedSupplyVerify", false);
        }
        //VDERP-12089【金蝶对接】【采购售后模块】销售单直发售后增加供应链审核以及不支持采购售后的直普发修改end
        actionProcdefService.createProcessInstance(request, "afterSalesVerify","afterSalesVerify_" + afterSalesInfo.getAfterSalesId(), variableMap);

        // 默认申请人通过
        // 根据BusinessKey获取生成的审核实例
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,"afterSalesVerify_" + afterSalesInfo.getAfterSalesId());

        if (historicInfo.get("endStatus") != "审核完成") {
            // 修改锁定备注
            Saleorder saleorderLocked = new Saleorder();
            saleorderLocked.setLockedReason("售后锁定");
            saleorderLocked.setSaleorderId(afterSalesInfo.getOrderId());

            saleorderService.saveEditSaleorderInfo(saleorderLocked, request, request == null ? null : request.getSession());
            Task taskInfo = (Task) historicInfo.get("taskInfo");
            String taskId = taskInfo.getId();
            Authentication.setAuthenticatedUserId(user.getUsername());
            Map<String, Object> variables = new HashMap<String, Object>();
            // 设置审核完成监听器回写参数
            variables.put("tableName", "T_AFTER_SALES");
            variables.put("db", 2);
            variables.put("id", "AFTER_SALES_ID");
            variables.put("idValue", afterSalesInfo.getAfterSalesId());
            variables.put("key", "STATUS");
            variables.put("value", 2);
            variables.put("key1", "ATFER_SALES_STATUS");
            variables.put("value1", 1);
            variables.put("key2", "VALID_STATUS");
            variables.put("value2", 1);
            // 默认审批通过
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                    user.getUsername(), variables);
            //设置虚拟商品产品归属人
            TaskService taskService = processEngine.getTaskService();
            if (CollectionUtils.isNotEmpty(vitualAudits)){
                // 获取当前活动节点
                taskInfo = taskService
                        .createTaskQuery()
                        .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                        .singleResult();
                String businessKey = (String) taskService.getVariable(taskInfo.getId(), "businessKey");
                //设置候选人
                setTaskCandidateUser(taskService, businessKey, vitualAudits);

            }
            if (!"endEvent".equals(complementStatus.getData())) {
                verifiesRecordService.saveVerifiesInfo(taskId, 0);
                //发送消息提醒至虚拟商品-供应链审核的产品归属人
                if (CollectionUtils.isNotEmpty(vitualUserIds)){
                    //发送站内信
                    sendAftersalesInfo(vitualUserIds, taskService.getVariables(taskInfo.getId()),afterSalesInfo);
                }
            }
        }
    }

    protected void startAduitProcess(HttpServletRequest request) throws Exception{

        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");

        AfterSalesVo afterSales = new AfterSalesVo();
        afterSales.setAfterSalesId(afterSaleOrder.getAfterSalesId());
        afterSales.setTraderType(1);
        // 审核中
        afterSales.setStatus(1);
        afterSales.setCompanyId(1);
        afterSales.setModTime(DateUtil.sysTimeMillis());
        afterSales.setUpdater(2);
        ResultInfo<?> res = afterSalesOrderService.editApplyAudit(afterSales);

        if (res == null) {
            throw new AfterSaleValidateException("修改售后单失败");
        }

        Map<String, Object> variableMap = new HashMap<String, Object>();
        // 查询当前订单的一些状态
        AfterSalesVo afterSalesInfo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);

        setAfterSaleGoodType(afterSalesInfo);

        Saleorder saleorder = ThreadLocalContext.get("saleorder");
        //查询归属销售
        User user = userMapper.getUserByTraderId(saleorder.getTraderId(), ErpConst.ONE);
        // 查询归属销售组织关系
        User userOrg = userMapper.getUserInfoByTraderId(saleorder.getTraderId(), TRADER_TYPE_CUSTOMER);
        // 开始生成流程(如果没有taskId表示新流程需要生成)
        variableMap.put("afterSalesInfo", afterSalesInfo);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("processDefinitionKey", "afterSalesVerify");
        variableMap.put("businessKey", "afterSalesVerify_" + afterSalesInfo.getAfterSalesId());
        variableMap.put("relateTableKey", afterSalesInfo.getAfterSalesId());
        variableMap.put("relateTable", "T_AFTER_SALES");
        variableMap.put("orgId", userOrg.getOrgId());
        variableMap.put("skuManages", "");
        variableMap.put("isVirtualVerify", false);
        Set<String> vitualAudits=new HashSet<>();
        List<Integer> vitualUserIds=new ArrayList<>();
        //VDERP-12089【金蝶对接】【采购售后模块】销售单直发售后增加供应链审核以及不支持采购售后的直普发修改start
        if(CollectionUtils.isNotEmpty(afterSalesInfo.getAfterSalesGoodsList())) {
            //是否走虚拟商品-供应链审核
            if(AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesInfo.getType())){
                List<AfterSalesGoodsVo> afterSalesGoodsVos = afterSalesInfo.getAfterSalesGoodsList().stream().filter(x -> Objects.nonNull(x.getOrderDetailId())).collect(Collectors.toList());
                List<Integer> saleorderGoodsIds = afterSalesGoodsVos.stream().map(AfterSalesGoodsVo::getOrderDetailId).collect(Collectors.toList());
                //获取满足条件的sku列表
                List<String> virtualSkuList = saleorderGoodsMapper.getVirtualGoodsSkuNoList(saleorderGoodsIds);
                if(CollectionUtils.isNotEmpty(virtualSkuList)){
                    //清空审核记录
                    afterSaleAuditInfoService.clearAuditInfoByAfterSaleId(afterSalesInfo.getAfterSalesId());
                    //获取虚拟商品对应的产品归属人列表
                    List<ProductManageAndAsistDto> manageAndAsistDtoList = coreSkuMapper.batchQueryProductManageAndAsist(virtualSkuList);
                    vitualAudits= afterSaleAuditInfoService.getProductManageAndAsistNameList(manageAndAsistDtoList);
                    vitualUserIds=afterSaleAuditInfoService.getProductManageAndAsistIdList(manageAndAsistDtoList);
                    if (CollectionUtils.isNotEmpty(vitualAudits)){
                        variableMap.put("isVirtualVerify", true);
                        //初始化审核数据
                        afterSaleAuditInfoService.insertAfterSaleAuditInfo(manageAndAsistDtoList,afterSalesInfo);
                    }
                }
            }

            List<AfterSalesGoodsVo> haveDirectGoodsList = afterSalesInfo.getAfterSalesGoodsList().stream().filter(
                    item -> ErpConst.ONE.equals(item.getDeliveryDirect())).collect(Collectors.toList());
            if ((AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesInfo.getType())
                    || AfterSalesProcessEnum.AFTERSALES_HH.getCode().equals(afterSalesInfo.getType()))
                    && CollectionUtils.isNotEmpty(haveDirectGoodsList)) {
                //1、销售售后单为销售退货/销售换货
                //2、包含退/换货方式为直发的商品
                //3、直发退货的SKU发货数量大于0
                variableMap.put("isNeedSupplyVerify", false);
                for (AfterSalesGoodsVo afterSalesVo : haveDirectGoodsList) {
                    SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(afterSalesVo.getOrderDetailId());
                    if (Objects.nonNull(saleorderGoods) && Objects.nonNull(saleorderGoods.getDeliveryNum()) && saleorderGoods.getDeliveryNum() > 0) {
                        variableMap.put("isNeedSupplyVerify", true);
                        // 将销售售后单商品的产品归属人加入
                        List<String> skus = afterSalesInfo.getAfterSalesGoodsList().stream().map(AfterSalesGoodsVo::getSku).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(skus)) {
                            Set<String> productManageAndAsistNameList = getProductManageAndAsistNameList(skus);
                            if (CollUtil.isNotEmpty(productManageAndAsistNameList)) {
                                variableMap.put("skuManages", CollUtil.join(productManageAndAsistNameList,","));
                            }
                        }
                        break;
                    }
                }


            } else {
                variableMap.put("isNeedSupplyVerify", false);
            }
        }else {
            variableMap.put("isNeedSupplyVerify", false);
        }
        //VDERP-12089【金蝶对接】【采购售后模块】销售单直发售后增加供应链审核以及不支持采购售后的直普发修改end
        actionProcdefService.createProcessInstance(request, "afterSalesVerify","afterSalesVerify_" + afterSalesInfo.getAfterSalesId(), variableMap);

        // 默认申请人通过
        // 根据BusinessKey获取生成的审核实例
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,"afterSalesVerify_" + afterSalesInfo.getAfterSalesId());

        if (historicInfo.get("endStatus") != "审核完成") {
            // 修改锁定备注
            Saleorder saleorderLocked = new Saleorder();
            saleorderLocked.setLockedReason("售后锁定");
            saleorderLocked.setSaleorderId(afterSalesInfo.getOrderId());
            saleorderService.saveEditSaleorderInfo(saleorderLocked, request, request.getSession());
            Task taskInfo = (Task) historicInfo.get("taskInfo");
            String taskId = taskInfo.getId();
            Authentication.setAuthenticatedUserId(user.getUsername());
            Map<String, Object> variables = new HashMap<String, Object>();
            // 设置审核完成监听器回写参数
            variables.put("tableName", "T_AFTER_SALES");
            variables.put("db", 2);
            variables.put("id", "AFTER_SALES_ID");
            variables.put("idValue", afterSalesInfo.getAfterSalesId());
            variables.put("key", "STATUS");
            variables.put("value", 2);
            variables.put("key1", "ATFER_SALES_STATUS");
            variables.put("value1", 1);
            variables.put("key2", "VALID_STATUS");
            variables.put("value2", 1);
            // 默认审批通过
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                    user.getUsername(), variables);
            //设置虚拟商品产品归属人
            TaskService taskService = processEngine.getTaskService();
            if (CollectionUtils.isNotEmpty(vitualAudits)){
                // 获取当前活动节点
                taskInfo = taskService
                        .createTaskQuery()
                        .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                        .singleResult();
                String businessKey = (String) taskService.getVariable(taskInfo.getId(), "businessKey");
                //设置候选人
                setTaskCandidateUser(taskService, businessKey, vitualAudits);

            }
            if (!"endEvent".equals(complementStatus.getData())) {
                verifiesRecordService.saveVerifiesInfo(taskId, 0);
                //发送消息提醒至虚拟商品-供应链审核的产品归属人
                if (CollectionUtils.isNotEmpty(vitualUserIds)){
                    //发送站内信
                    sendAftersalesInfo(vitualUserIds, taskService.getVariables(taskInfo.getId()),afterSalesInfo);
                }
            }
        }

    }

    /**
     * 设置任务候选人
     *
     * @param taskService
     * @param businessKey
     * @param manageAndAsistNameSet
     */
    private void setTaskCandidateUser(TaskService taskService, String businessKey, Set<String> manageAndAsistNameSet) {

        Task nextTask = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();

        for (String manageAndAsistName : manageAndAsistNameSet) {
            processEngine.getTaskService().addCandidateUser(nextTask.getId() + "", manageAndAsistName);
        }
        ;

    }

    private void sendAftersalesInfo(List<Integer> userIdList, Map<String, Object> variables,AfterSalesVo afterSalesVo) {
        String creatorName=afterSalesMapper.getCreatorNameByAfterSalesId(afterSalesVo.getAfterSalesId());
        Map<String, String> variableMap = new HashedMap();
        variableMap.put("afterSalesNo", afterSalesVo.getAfterSalesNo());
        String url = "./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId="
                + afterSalesVo.getAfterSalesId() + "&traderType=1";
        MessageUtil.sendMessage(27, userIdList, variableMap, url,creatorName);
    }

    Set<String> getProductManageAndAsistNameList(List<String> skuNos) {

        List<ProductManageAndAsistDto> manageAndAsistDtoList = coreSkuMapper.batchQueryProductManageAndAsist(skuNos);

        Set<String> manageAndAsistNameSet = new HashSet<>();

        manageAndAsistDtoList.stream().forEach(manageAndAsist -> {

            if (StringUtil.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductAssitName());
            }

            if (StringUtil.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductManageName());
            }
        });

        return manageAndAsistNameSet;
    }

    private void setAfterSaleGoodType(AfterSalesVo afterSalesInfo) {

        // 订单中产品类型（0未维护,1 只有设备,2 只有试剂,3 又有试剂又有设备）
        afterSalesInfo.setGoodsType(0);

        if(CollectionUtils.isEmpty(afterSalesInfo.getAfterSalesGoodsList())){
            return;
        }

        //是否包含试剂和设备
        boolean haveDevice = afterSalesInfo.getAfterSalesGoodsList().stream().anyMatch(e -> e.getGoodsType() != null && (e.getGoodsType() == 316 || e.getGoodsType() == 319));

        //是否包含设备和耗材
        boolean haveMaterial = afterSalesInfo.getAfterSalesGoodsList().stream().anyMatch(e -> e.getGoodsType() != null && (e.getGoodsType() == 317 || e.getGoodsType() == 318));

        //都包含
        if(haveDevice && haveMaterial){
            afterSalesInfo.setGoodsType(3);
            // 只有试剂和设备
        }else if(haveDevice && !haveMaterial){
            afterSalesInfo.setGoodsType(1);
            //只有设备和耗材
        }else if(!haveDevice && haveMaterial){
            afterSalesInfo.setGoodsType(2);
        }

    }


    @ResponseBody
    @RequestMapping(method = RequestMethod.POST,value = "/modifyOrder")
    public RestfulResult<?> modifyOrder(HttpServletRequest request, @RequestBody AfterSaleOrderModifyDto modifyDto) {

        try {

            log.info("modifyOrder的入参:" + JSON.toJSONString(modifyDto));

            if (modifyDto == null || StringUtil.isEmpty(modifyDto.getAfterSalesNo())) {
                return  RestfulResult.fail("fail","售后单号不能为空");
            }

            AfterSales afterSales = this.afterSalesMapper.getAfterSalesByNo(modifyDto.getAfterSalesNo());
            if(afterSales == null){
                return RestfulResult.fail("fail","售后单不存在");
            }

            modifyDto.setSaleOrderNo(afterSales.getOrderNo());

            AfterSalesOrderService afterSalesOrderService = afterSalesOrderServiceFactory.createAfterSalesInstance(afterSales.getType());

            afterSalesOrderService.modifyAfterSalesOrder(modifyDto);

            if(!AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(afterSales.getType())
                    && !AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(afterSales.getType())
                    && !AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(afterSales.getType())){
                startAduitProcess(request);
            }
            return RestfulResult.success("");

        } catch (AfterSaleException e) {

            log.error("modifyOrder error:",e);

            return RestfulResult.fail("fail",e.getMessage());

        } catch (Exception e) {
            log.error("modifyOrder error:",e);
        }

        return RestfulResult.fail("fail","修改售后单失败");
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST,value = "/closeOrder")
    public RestfulResult<?> closeOrder(HttpServletRequest request, @RequestBody AfterSaleOrderCloseDto afterSaleOrderCloseDto) {

        try {

            log.info("modifyOrder的入参:" + JSON.toJSONString(afterSaleOrderCloseDto));

            if (afterSaleOrderCloseDto == null) {
                return RestfulResult.fail("fail","参数不能为空");
            }
            AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSaleOrderCloseDto.getAfterSalesNo());
            if(null == afterSales){
                return RestfulResult.fail("fail","未查询到相应售后单");
            }
            AfterSalesOrderService afterSalesOrderService = afterSalesOrderServiceFactory.createAfterSalesInstance(afterSales.getType());

            afterSalesOrderService.closeAfterSalesOrder(afterSaleOrderCloseDto);

            return RestfulResult.success("");

        } catch (AfterSaleException e) {

            log.error("closeOrder error:",e);

            return RestfulResult.fail("fail",e.getMessage());

        } catch (Exception e) {
            log.error("closeOrder error:",e);
        }

        return RestfulResult.fail("fail","关闭售后单失败");
    }
}
