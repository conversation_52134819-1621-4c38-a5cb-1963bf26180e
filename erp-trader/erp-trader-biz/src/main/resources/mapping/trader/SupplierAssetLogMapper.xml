<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.SupplierAssetLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.SupplierAssetLogEntity">
    <!--@mbg.generated-->
    <!--@Table T_SUPPLIER_ASSET_LOG-->
    <id column="SUPPLIER_ASSET_LOG_ID" jdbcType="INTEGER" property="supplierAssetLogId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId" />
    <result column="SUPPLIER_ASSET_ID" jdbcType="INTEGER" property="supplierAssetId" />
    <result column="CHANGE_TYPE" jdbcType="INTEGER" property="changeType" />
    <result column="CHANGE_COUNT" jdbcType="DECIMAL" property="changeCount" />
    <result column="CHANGE_BEFORE_TOTAL" jdbcType="DECIMAL" property="changeBeforeTotal" />
    <result column="CHANGE_AFTER_TOTAL" jdbcType="DECIMAL" property="changeAfterTotal" />
    <result column="CHANGE_COUNT_TOTAL" jdbcType="DECIMAL" property="changeCountTotal" />
    <result column="CHANGE_BEFORE_APPLY" jdbcType="DECIMAL" property="changeBeforeApply" />
    <result column="CHANGE_AFTER_APPLY" jdbcType="DECIMAL" property="changeAfterApply" />
    <result column="CHANGE_COUNT_APPLY" jdbcType="DECIMAL" property="changeCountApply" />
    <result column="CHANGE_BEFORE_OCCUPY" jdbcType="DECIMAL" property="changeBeforeOccupy" />
    <result column="CHANGE_AFTER_OCCUPY" jdbcType="DECIMAL" property="changeAfterOccupy" />
    <result column="CHANGE_COUNT_OCCUPY" jdbcType="DECIMAL" property="changeCountOccupy" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="SOURCE_TYPE" jdbcType="VARCHAR" property="sourceType" />
    <result column="ASSET_TYPE" jdbcType="INTEGER" property="assetType" />
    <result column="OPERATE_USER" jdbcType="VARCHAR" property="operateUser" />
    <result column="OPERATE_NOTE" jdbcType="VARCHAR" property="operateNote" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SUPPLIER_ASSET_LOG_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    TRADER_SUPPLIER_ID, SUPPLIER_ASSET_ID, CHANGE_TYPE, CHANGE_COUNT, CHANGE_BEFORE_TOTAL, 
    CHANGE_AFTER_TOTAL, CHANGE_COUNT_TOTAL, CHANGE_BEFORE_APPLY, CHANGE_AFTER_APPLY, 
    CHANGE_COUNT_APPLY, CHANGE_BEFORE_OCCUPY, CHANGE_AFTER_OCCUPY, CHANGE_COUNT_OCCUPY, 
    BUSINESS_NO, SOURCE_TYPE, ASSET_TYPE, OPERATE_USER, OPERATE_NOTE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SUPPLIER_ASSET_LOG
    where SUPPLIER_ASSET_LOG_ID = #{supplierAssetLogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SUPPLIER_ASSET_LOG
    where SUPPLIER_ASSET_LOG_ID = #{supplierAssetLogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SUPPLIER_ASSET_LOG_ID" keyProperty="supplierAssetLogId" parameterType="com.vedeng.erp.trader.domain.entity.SupplierAssetLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SUPPLIER_ASSET_LOG (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      TRADER_SUPPLIER_ID, SUPPLIER_ASSET_ID, CHANGE_TYPE, 
      CHANGE_COUNT, CHANGE_BEFORE_TOTAL, CHANGE_AFTER_TOTAL, 
      CHANGE_COUNT_TOTAL, CHANGE_BEFORE_APPLY, CHANGE_AFTER_APPLY, 
      CHANGE_COUNT_APPLY, CHANGE_BEFORE_OCCUPY, CHANGE_AFTER_OCCUPY, 
      CHANGE_COUNT_OCCUPY, BUSINESS_NO, SOURCE_TYPE, 
      ASSET_TYPE, OPERATE_USER, OPERATE_NOTE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{traderSupplierId,jdbcType=INTEGER}, #{supplierAssetId,jdbcType=INTEGER}, #{changeType,jdbcType=INTEGER}, 
      #{changeCount,jdbcType=DECIMAL}, #{changeBeforeTotal,jdbcType=DECIMAL}, #{changeAfterTotal,jdbcType=DECIMAL}, 
      #{changeCountTotal,jdbcType=DECIMAL}, #{changeBeforeApply,jdbcType=DECIMAL}, #{changeAfterApply,jdbcType=DECIMAL}, 
      #{changeCountApply,jdbcType=DECIMAL}, #{changeBeforeOccupy,jdbcType=DECIMAL}, #{changeAfterOccupy,jdbcType=DECIMAL}, 
      #{changeCountOccupy,jdbcType=DECIMAL}, #{businessNo,jdbcType=VARCHAR}, #{sourceType,jdbcType=VARCHAR}, 
      #{assetType,jdbcType=INTEGER}, #{operateUser,jdbcType=VARCHAR}, #{operateNote,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SUPPLIER_ASSET_LOG_ID" keyProperty="supplierAssetLogId" parameterType="com.vedeng.erp.trader.domain.entity.SupplierAssetLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SUPPLIER_ASSET_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="traderSupplierId != null">
        TRADER_SUPPLIER_ID,
      </if>
      <if test="supplierAssetId != null">
        SUPPLIER_ASSET_ID,
      </if>
      <if test="changeType != null">
        CHANGE_TYPE,
      </if>
      <if test="changeCount != null">
        CHANGE_COUNT,
      </if>
      <if test="changeBeforeTotal != null">
        CHANGE_BEFORE_TOTAL,
      </if>
      <if test="changeAfterTotal != null">
        CHANGE_AFTER_TOTAL,
      </if>
      <if test="changeCountTotal != null">
        CHANGE_COUNT_TOTAL,
      </if>
      <if test="changeBeforeApply != null">
        CHANGE_BEFORE_APPLY,
      </if>
      <if test="changeAfterApply != null">
        CHANGE_AFTER_APPLY,
      </if>
      <if test="changeCountApply != null">
        CHANGE_COUNT_APPLY,
      </if>
      <if test="changeBeforeOccupy != null">
        CHANGE_BEFORE_OCCUPY,
      </if>
      <if test="changeAfterOccupy != null">
        CHANGE_AFTER_OCCUPY,
      </if>
      <if test="changeCountOccupy != null">
        CHANGE_COUNT_OCCUPY,
      </if>
      <if test="businessNo != null and businessNo != ''">
        BUSINESS_NO,
      </if>
      <if test="sourceType != null and sourceType != ''">
        SOURCE_TYPE,
      </if>
      <if test="assetType != null">
        ASSET_TYPE,
      </if>
      <if test="operateUser != null and operateUser != ''">
        OPERATE_USER,
      </if>
      <if test="operateNote != null and operateNote != ''">
        OPERATE_NOTE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderSupplierId != null">
        #{traderSupplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierAssetId != null">
        #{supplierAssetId,jdbcType=INTEGER},
      </if>
      <if test="changeType != null">
        #{changeType,jdbcType=INTEGER},
      </if>
      <if test="changeCount != null">
        #{changeCount,jdbcType=DECIMAL},
      </if>
      <if test="changeBeforeTotal != null">
        #{changeBeforeTotal,jdbcType=DECIMAL},
      </if>
      <if test="changeAfterTotal != null">
        #{changeAfterTotal,jdbcType=DECIMAL},
      </if>
      <if test="changeCountTotal != null">
        #{changeCountTotal,jdbcType=DECIMAL},
      </if>
      <if test="changeBeforeApply != null">
        #{changeBeforeApply,jdbcType=DECIMAL},
      </if>
      <if test="changeAfterApply != null">
        #{changeAfterApply,jdbcType=DECIMAL},
      </if>
      <if test="changeCountApply != null">
        #{changeCountApply,jdbcType=DECIMAL},
      </if>
      <if test="changeBeforeOccupy != null">
        #{changeBeforeOccupy,jdbcType=DECIMAL},
      </if>
      <if test="changeAfterOccupy != null">
        #{changeAfterOccupy,jdbcType=DECIMAL},
      </if>
      <if test="changeCountOccupy != null">
        #{changeCountOccupy,jdbcType=DECIMAL},
      </if>
      <if test="businessNo != null and businessNo != ''">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null and sourceType != ''">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="assetType != null">
        #{assetType,jdbcType=INTEGER},
      </if>
      <if test="operateUser != null and operateUser != ''">
        #{operateUser,jdbcType=VARCHAR},
      </if>
      <if test="operateNote != null and operateNote != ''">
        #{operateNote,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.SupplierAssetLogEntity">
    <!--@mbg.generated-->
    update T_SUPPLIER_ASSET_LOG
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderSupplierId != null">
        TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierAssetId != null">
        SUPPLIER_ASSET_ID = #{supplierAssetId,jdbcType=INTEGER},
      </if>
      <if test="changeType != null">
        CHANGE_TYPE = #{changeType,jdbcType=INTEGER},
      </if>
      <if test="changeCount != null">
        CHANGE_COUNT = #{changeCount,jdbcType=DECIMAL},
      </if>
      <if test="changeBeforeTotal != null">
        CHANGE_BEFORE_TOTAL = #{changeBeforeTotal,jdbcType=DECIMAL},
      </if>
      <if test="changeAfterTotal != null">
        CHANGE_AFTER_TOTAL = #{changeAfterTotal,jdbcType=DECIMAL},
      </if>
      <if test="changeCountTotal != null">
        CHANGE_COUNT_TOTAL = #{changeCountTotal,jdbcType=DECIMAL},
      </if>
      <if test="changeBeforeApply != null">
        CHANGE_BEFORE_APPLY = #{changeBeforeApply,jdbcType=DECIMAL},
      </if>
      <if test="changeAfterApply != null">
        CHANGE_AFTER_APPLY = #{changeAfterApply,jdbcType=DECIMAL},
      </if>
      <if test="changeCountApply != null">
        CHANGE_COUNT_APPLY = #{changeCountApply,jdbcType=DECIMAL},
      </if>
      <if test="changeBeforeOccupy != null">
        CHANGE_BEFORE_OCCUPY = #{changeBeforeOccupy,jdbcType=DECIMAL},
      </if>
      <if test="changeAfterOccupy != null">
        CHANGE_AFTER_OCCUPY = #{changeAfterOccupy,jdbcType=DECIMAL},
      </if>
      <if test="changeCountOccupy != null">
        CHANGE_COUNT_OCCUPY = #{changeCountOccupy,jdbcType=DECIMAL},
      </if>
      <if test="businessNo != null and businessNo != ''">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null and sourceType != ''">
        SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="assetType != null">
        ASSET_TYPE = #{assetType,jdbcType=INTEGER},
      </if>
      <if test="operateUser != null and operateUser != ''">
        OPERATE_USER = #{operateUser,jdbcType=VARCHAR},
      </if>
      <if test="operateNote != null and operateNote != ''">
        OPERATE_NOTE = #{operateNote,jdbcType=VARCHAR},
      </if>
    </set>
    where SUPPLIER_ASSET_LOG_ID = #{supplierAssetLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.SupplierAssetLogEntity">
    <!--@mbg.generated-->
    update T_SUPPLIER_ASSET_LOG
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER},
      SUPPLIER_ASSET_ID = #{supplierAssetId,jdbcType=INTEGER},
      CHANGE_TYPE = #{changeType,jdbcType=INTEGER},
      CHANGE_COUNT = #{changeCount,jdbcType=DECIMAL},
      CHANGE_BEFORE_TOTAL = #{changeBeforeTotal,jdbcType=DECIMAL},
      CHANGE_AFTER_TOTAL = #{changeAfterTotal,jdbcType=DECIMAL},
      CHANGE_COUNT_TOTAL = #{changeCountTotal,jdbcType=DECIMAL},
      CHANGE_BEFORE_APPLY = #{changeBeforeApply,jdbcType=DECIMAL},
      CHANGE_AFTER_APPLY = #{changeAfterApply,jdbcType=DECIMAL},
      CHANGE_COUNT_APPLY = #{changeCountApply,jdbcType=DECIMAL},
      CHANGE_BEFORE_OCCUPY = #{changeBeforeOccupy,jdbcType=DECIMAL},
      CHANGE_AFTER_OCCUPY = #{changeAfterOccupy,jdbcType=DECIMAL},
      CHANGE_COUNT_OCCUPY = #{changeCountOccupy,jdbcType=DECIMAL},
      BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
      ASSET_TYPE = #{assetType,jdbcType=INTEGER},
      OPERATE_USER = #{operateUser,jdbcType=VARCHAR},
      OPERATE_NOTE = #{operateNote,jdbcType=VARCHAR}
    where SUPPLIER_ASSET_LOG_ID = #{supplierAssetLogId,jdbcType=INTEGER}
  </update>
</mapper>