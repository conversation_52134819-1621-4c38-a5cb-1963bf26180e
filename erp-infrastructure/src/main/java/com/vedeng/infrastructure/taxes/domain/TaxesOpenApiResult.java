package com.vedeng.infrastructure.taxes.domain;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import lombok.Data;

/**
 * 销项票接口原始返回
 */
@Data
public class TaxesOpenApiResult implements ITaxesResult {

    private static final long serialVersionUID = 1822296557809064577L;

    /**
     * 同return_code（以return_info为准，尽管这个字段也是对的）
     */
    private String errorCode;

    /**
     * 同return_message（以return_info为准，尽管这个字段也是对的）
     */
    private String errorMessage;

    /**
     * 返回码：（以这里为准）
     */
    private ReturnInfo return_info;
    /**
     * Base64加密后内容
     */
    private String data;

    /**
     * 返回码
     */
    @Data
    public static class ReturnInfo{
        /**
         * 返回码 0000 成功
         */
        private String return_code;
        /**
         * 返回消息
         */
        private String return_message;

        public ReturnInfo(TaxesReturnCodeEnum taxesReturnCodeEnum) {
            this.return_code = taxesReturnCodeEnum.getCode();
            this.return_message = taxesReturnCodeEnum.getName();
        }

        public ReturnInfo(ServiceException exception) {
            this.return_code = String.valueOf(exception.getCode());
            this.return_message = exception.getMessage();
        }
    }

    public TaxesOpenApiResult(){

    }

    /**
     * 构造方法
     * @param se
     */
    public TaxesOpenApiResult(ServiceException se){
        this.return_info = new ReturnInfo(se);
    }
}
