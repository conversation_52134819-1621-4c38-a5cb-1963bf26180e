package com.vedeng.workbench.model.vo;

import com.vedeng.workbench.model.vo.base.WorkbenchChanceInfo;
import com.vedeng.workbench.model.vo.base.WorkbenchOverview;
import com.vedeng.workbench.model.vo.summary.NumAmounteSummary;
import lombok.Data;

import java.util.List;

/**
 * 商机概况(个人).
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/26 9:04 上午.
 * @author: Tomcat.Hui.
 */
@Data
public class WorkbenchPersonalOverview extends WorkbenchOverview {

    /**
     * 页头展示数据
     */
    private NumAmounteSummary pageHeaderData;

    /**
     * 今日新增商机
     */
    private List<WorkbenchChanceInfo> todayNewChanceList;

    /**
     * 今日待沟通商机
     */
    private List<WorkbenchChanceInfo> todayCommunicateChanceList;

    /**
     * 重点商机
     */
    private List<WorkbenchChanceInfo> importentChanceList;

    /**
     * 预计本周成单
     */
    private List<WorkbenchChanceInfo> thisWeekExpectOrderChanceList;
}
