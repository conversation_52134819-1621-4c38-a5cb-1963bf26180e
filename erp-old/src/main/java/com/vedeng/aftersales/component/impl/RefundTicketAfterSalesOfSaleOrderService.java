package com.vedeng.aftersales.component.impl;

import com.vedeng.aftersales.component.AbstractAfterSalesOrderServiceOfSaleOrder;
import com.vedeng.aftersales.component.dto.*;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.dao.RInvoiceJInvoiceMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.RInvoiceJInvoice;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: daniel
 * @Date: 2021/6/21 15 58
 * @Description:
 */
@Service
public class RefundTicketAfterSalesOfSaleOrderService extends AbstractAfterSalesOrderServiceOfSaleOrder implements ApplicationContextAware {

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Resource
    private RInvoiceJInvoiceMapper rInvoiceJInvoiceMapper;

    @Resource
    private SaleorderMapper saleOrderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    protected void customerAfterSaleOrderCreateValidate(Object addDto) throws AfterSaleValidateException {

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;

        LostTicketAfterSalesOfSaleOrderService lostTicketAfterSalesOfSaleOrderService = applicationContext.getBean(LostTicketAfterSalesOfSaleOrderService.class);
        lostTicketAfterSalesOfSaleOrderService.addTicketValide(afterSaleOrderAddDto);
    }

    @Override
    protected void customerPropertySet(AfterSaleOrderAddDto afterSaleOrderAddDto, AfterSales afterSales) {

    }

    @Override
    protected void customerAddAfterSaleGood(AfterSaleOrderAddDto afterSaleOrderAddDto) {

    }

    @Override
    protected <T extends AbstractAfterSaleOrderDto> void customerAddAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderAddDto) {

    }

    @Override
    protected void customeAddDealOfSaleOrder(Object addDto) throws AfterSaleValidateException{
        // 添加售后退票表信息
        addTicket(addDto);
    }

    @Override
    protected void customerAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;

        LostTicketAfterSalesOfSaleOrderService lostTicketAfterSalesOfSaleOrderService = applicationContext.getBean(LostTicketAfterSalesOfSaleOrderService.class);
        lostTicketAfterSalesOfSaleOrderService.modifyTicketValide(afterSaleOrderModifyDto);
    }
    @Override
    protected void customeModifyDealOfSaleOrder(Object modifyDto) throws AfterSaleValidateException {

        // 修改发票信息
        modifyTicket(modifyDto);
    }

    @Override
    protected void customerAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) {

    }
}
