@import (less) '../common/mixin.css';

.dlg-share-wrap {
    .dlg-share-btn {
        margin-bottom: 20px;
    }
}

.record-wrap {
    display: flex;
    align-items: center;

    .icon-sms {
        font-size: 16px;
        margin-right: 5px;
        color: #09f;
        cursor: pointer;
        line-height: 1;

        &:hover {
            color: #f60;
        }
    }
}

.td-tyc-wrap {
    display: flex;
    align-items: center;

    .text-line-1 {
        flex: 1;
    }

    .tyc-icon {
        width: 16px;
        height: 16px;
        background-image: url(../../image/tyc.png);
        background-size: 100% 100%;
        margin-left: 5px;
        cursor: pointer;
    }
}

.leadsno {
    display: flex;
    align-items: center;
}

.tag-icon-hebing {
    width: 16px;
    height: 16px;
    background-image: url(../../image/common/tag-hebing.svg);
    background-size: 100% 100%;
    margin-top: -1px;
    display: inline-block;
    margin-right: 5px;
}

.ai-wrap {
    // max-width: 590px;
    width: 100%;
    margin-top: 10px;

    .ai-title {
        color: #999;
        border-top: dashed 1px #E1E5E8;
        padding: 10px 0 3px;
    }

    .ai-content {
        margin-top: 7px;
    }
}

.card-title-ai {
    position: absolute;
    top: 15px;
    right: 20px;
}

.highlight {
    color: #09f;
    cursor: pointer;

    .vd-ui_icon {
        font-size: 16px;
        margin-right: 1px;
        cursor: pointer;
        line-height: 1;
    }

    &:hover {
        color: #f60;
    }
}

.detail-tyc {
    word-break: break-all;
    position: relative;
    padding-right: 21px;
    display: inline-block;

    .company {
        font-size: 12px;
        color: #333;

        &.blue {
            color: #09f;
            cursor: pointer;

            &:hover {
                color: #f60;
            }
        }
    }

    .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('../../image/tyc.png') no-repeat;
        background-size: 100%;
        margin-left: 5px;
        cursor: pointer;
        position: absolute;
        top: 1px;
        right: 0;
    }
}

.businessLeads-add-container {
    padding-top: 60px;

    .top-featurn {
        width: 100%;
        height: 60px;
        position: fixed;
        top: 50px;
        left: 0;
        z-index: 40;

        .inner-header {
            padding: 0 15px;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #FFFFFF;
            border-bottom: solid 1px #E1E5E8;

            .inner-header-title {
                font-size: 20px;
                font-weight: 700;
            }
        }
    }
}


.businessLeads-detail-container {
    position: relative;
    padding-top: 60px;
    padding-right: 360px;

    
    .visible-xs {
        display: none;
    }

    .leadsDetail-header-wrap {
        width: 100%;
        min-width: 760px;
        height: 60px;
        background: #fff;
        border-bottom: solid 1px #E1E5E8;
        position: fixed;
        top: 50px;
        z-index: 9;

        .header-main {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;

            .header-content {
                flex: 1;
                min-width: 0;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .header-left {
                    display: flex;
                    align-items: center;
                    padding: 0 15px;

                    .title {
                        font-size: 20px;
                        font-weight: 700;
                        color: #333333;
                    }

                    .status {
                        height: 27px;
                        line-height: 27px;
                        padding: 0 10px;
                        border-radius: 3px;
                        color: #FF6600;
                        background-color: rgba(255, 102, 0, 0.1);
                        margin-left: 15px;

                        &.status0 {
                            background: #ffede1;
                            color: #F60;
                        }
                        &.status1 {
                            background: #ffede1;
                            color: #F60;
                        }
                        &.status2 {
                            background: #e1f3ff;
                            color: #09F;
                        }
                        &.status3 {
                            background: #fce9e9;
                            color: #E64545;
                        }
                        &.status4 {
                            background: #E3F7E3;
                            color: #13BF13;
                        }
                    }
                }
                .header-right {
                    display: flex;
                    align-items: center;
                    padding-right: 10px;

                    .vd-ui-button {
                        margin-right: 10px;
                    }
                }
            }

            .header-aside-wrap {

                // 大屏头
                .header-md-aside {
                    position: relative;
                    width: 360px;
                    display: flex;

                    &::before {
                        content: "";
                        height: 60px;
                        width: 1px;
                        background: #E1E5E8;
                        position: absolute;
                        left: 0;
                    }
                }

                .vd-ui-title-tip-wrap {
                    flex: 1;
                }

                .header-xs-aside {
                    display: none;
                }

                .h-a-item {
                    height: 60px;
                    position: relative;
                    flex: 1;
                    background-size: 24px 24px;
                    background-position: center;
                    background-repeat: no-repeat;
                    cursor: pointer;

                    &::before {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        top: 0;
                        content: "";
                        z-index: 1;
                        background-size: 24px 24px;
                        background-position: center;
                        background-repeat: no-repeat;
                        opacity: 0;
                    }

                    &.msg {
                        background-image: url('../../image/icon/msg.svg');

                        &::before {
                            background-image: url('../../image/icon/msg-active.svg');
                        }
                    }
                    &.time {
                        background-image: url('../../image/icon/time.svg');

                        &::before {
                            background-image: url('../../image/icon/time-active.svg');
                        }
                    }
                    &.record {
                        background-image: url('../../image/icon/record.svg');

                        &::before {
                            background-image: url('../../image/icon/record-active.svg');
                        }
                    }
                    &.user {
                        background-image: url('../../image/icon/user.svg');

                        &::before {
                            background-image: url('../../image/icon/user-active.svg');
                        }
                    }

                    

                    &.active, &:hover {
                        background-image: none;

                        &::before {
                            opacity: 1;
                        }   

                        // &.msg {
                        //     background: url('../../image/icon/msg-active.svg') no-repeat;
                        // }
                        // &.time {
                        //     background: url('../../image/icon/time-active.svg') no-repeat;
                        // }
                        // &.record {
                        //     background: url('../../image/icon/record-active.svg') no-repeat;
                        // }
                        // &.user {
                        //     background: url('../../image/icon/user-active.svg') no-repeat;
                        // }
                    }

                    &.active {
                        &::after {
                            content: "";
                            display: block;
                            width: 30px;
                            height: 2px;
                            background: #09f;
                            position: absolute;
                            bottom: 0;
                            left: 50%;
                            transform: translateX(-50%);
                        }
                    }
                }
            }
        }
    }

    .leadsDetail-page-wrap {
        padding: 10px 20px 20px 20px;

        .main {
            .show-font {
                margin-top: 10px;
                &:first-child {
                    margin-top: 0;
                }
            }
        }

        .right-aside {
            width: 360px;
            background: #fff;
            position: fixed;
            right: 0;
            top: 50px;
            z-index: 3;
            min-height: 500px;
            bottom: 0;
            padding-top: 60px;

            .right-aside-inner {
                height: 100%;
                border-left: solid 1px #E1E5E8;
            }
        }
    }

    .remark-wrap {
        position: relative;
        max-width: 1240px;
        margin: 0 auto;
        background: #fff;
        padding: 20px;
        margin-bottom: 20px;
    }

    .card {
        min-width: 0;
    }
}

.call-span {
    .icon-call2 {
        line-height: 1;
        vertical-align: -2px;
        font-size: 16px;
    }

    &.highlight {
        color: #09f;

        &:hover {
            color: #f60;
        }
    }
}

@media screen and (max-width: 1366px) {
    .businessLeads-detail-container {
        position: relative;
        padding-top: 60px;
        padding-right: 0;

        .hidden-xs {
            display: none;
        }

        .leadsDetail-header-wrap {
            .header-main {
                .header-aside-wrap {
                    .header-md-aside {
                        display: none;
                    }
                    // 小屏头
                    .header-xs-aside {
                        position: relative;
                        width: 192px;
                        display: flex;
                    }
                }
            }
        }

    }
    
    .card {
        min-width: 1160px;
    }
}

.only-form-wrap {
    padding-top: 0 !important;

    .page-container {
        padding-left: 0 !important;
    }

    .businessLeads-add-container {
        padding-top: 0 !important;
    }

    .businessLeads-add-container .top-featurn {
        display: none !important;
    }
}
