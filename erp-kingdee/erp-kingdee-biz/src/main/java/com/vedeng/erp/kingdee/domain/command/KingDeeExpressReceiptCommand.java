package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 快递签收单 dto
 * <AUTHOR>
 * @version 1.0
 * @description: 快递签收单 dto  由erp实际业务转换
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeExpressReceiptCommand {

    /**
     * fid
     */
    private Integer fid;
    /**
     * fBillNo
     */
    private String FBillNo;
    /**
     * fQzokYsddh
     */
    private String f_QZOK_YSDDH;
    /**
     * fQzokGsywdh
     */
    private String f_QZOK_GSYWDH;
    /**
     * fQzokCrkdh
     */
    private String f_QZOK_CRKDH;
    /**
     * fQzokKdh
     */
    private String f_QZOK_KDH;
    /**
     * fQzokYwlx
     */
    private String f_QZOK_YWLX;
    /**
     * fQzokQssj
     */
    private String f_QZOK_QSSJ;
    /**
     * fQzokWlgs
     */
    private String f_QZOK_WLGS;
    /**
     * fQzokOrgid
     */
    private KingDeeNumberCommand FQzokOrgId = new KingDeeNumberCommand();
    /**
     * fQzokBddjbh
     */
    private String f_QZOK_BDDJBH;
    /**
     * fQzokSfsc
     */
    private String f_QZOK_SFSC;
    /**
     * fQzokWlbm
     */
    private KingDeeNumberCommand f_QZOK_WLBM = new KingDeeNumberCommand();
    /**
     * fQzokPch
     */
    private String f_QZOK_PCH;
    /**
     * fQzokXlh
     */
    private String f_QZOK_XLH;
    /**
     * fQzokFhsl
     */
    private BigDecimal f_QZOK_FHSL;
    /**
     * fQzokSjr
     */
    private String f_QZOK_SJR;
    /**
     * fQzokDh
     */
    private String f_QZOK_DH;
    /**
     * fQzokDz
     */
    private String f_QZOK_DZ;
    /**
     * fQzokSfjrcb
     */
    private String f_QZOK_SFJRCB;
    /**
     * fQzokSfzp
     */
    private Integer f_QZOK_SFZP;

}
