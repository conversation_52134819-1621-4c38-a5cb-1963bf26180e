package com.vedeng.erp.aftersale.mapper;
import java.util.List;
import java.util.Collection;

import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface ExpenseAfterSalesStatusMapper {
    /**
     * delete by primary key
     *
     * @param expenseAfterSalesStatusId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long expenseAfterSalesStatusId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ExpenseAfterSalesStatusEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ExpenseAfterSalesStatusEntity record);

    /**
     * select by primary key
     *
     * @param expenseAfterSalesStatusId primary key
     * @return object by primary key
     */
    ExpenseAfterSalesStatusEntity selectByPrimaryKey(Long expenseAfterSalesStatusId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ExpenseAfterSalesStatusEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ExpenseAfterSalesStatusEntity record);

    /**
     * 根据费用售后单id 查询所有的状态信息
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return ExpenseAfterSalesStatusEntity
     */
    ExpenseAfterSalesStatusEntity selectByExpenseAfterSalesId(@Param("expenseAfterSalesId") Long expenseAfterSalesId);

    /**
     * 根据采购费用单售后id去更新
     * @param update
     * @return 影响行数
     */
    int updateByExpenseAfterSalesIdSelective(ExpenseAfterSalesStatusEntity update);

    /**
     * 根据售后单id  查询 计算出的应退金额
     * @param expenseAfterSalesIdCollection
     * @return
     */
    List<ExpenseAfterSalesStatusEntity> findByExpenseAfterSalesIdIn(@Param("expenseAfterSalesIdCollection")List<Long> expenseAfterSalesIdCollection);


}