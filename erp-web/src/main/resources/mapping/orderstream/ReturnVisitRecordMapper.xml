<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.orderstream.aftersales.dao.ReturnVisitRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.orderstream.aftersales.model.ReturnVisitRecord">
    <id column="RETURN_VISIT_RECORD_ID" jdbcType="INTEGER" property="returnVisitRecordId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="CUSTOMER_NAME" jdbcType="VARCHAR" property="customerName" />
    <result column="CUSTOMER_NATURE" jdbcType="INTEGER" property="customerNature" />
    <result column="CUSTOMER_MOBILE" jdbcType="VARCHAR" property="customerMobile" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="RETURN_VISIT_DEPARTMENT" jdbcType="INTEGER" property="returnVisitDepartment" />
    <result column="SOUND_RECORD_ID" jdbcType="INTEGER" property="soundRecordId" />
    <result column="SERVICE_RESPONSE_SCORE" jdbcType="INTEGER" property="serviceResponseScore" />
    <result column="SERVICE_ATTITUDE_SCORE" jdbcType="INTEGER" property="serviceAttitudeScore" />
    <result column="SERVICE_CAPABILITY_SCORE" jdbcType="INTEGER" property="serviceCapabilityScore" />
    <result column="IS_COMPLAINT" jdbcType="INTEGER" property="isComplaint" />
    <result column="IS_RECOMMEND" jdbcType="INTEGER" property="isRecommend" />
    <result column="TOTAL_SCORE" jdbcType="FLOAT" property="totalScore" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
  </resultMap>
  <sql id="Base_Column_List">
    RETURN_VISIT_RECORD_ID, AFTER_SALES_ID, CUSTOMER_NAME, CUSTOMER_NATURE, CUSTOMER_MOBILE, 
    `STATUS`, RETURN_VISIT_DEPARTMENT, SOUND_RECORD_ID, SERVICE_RESPONSE_SCORE, SERVICE_ATTITUDE_SCORE, 
    SERVICE_CAPABILITY_SCORE, IS_COMPLAINT, IS_RECOMMEND, TOTAL_SCORE, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER, COMMENTS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_RETURN_VISIT_RECORD
    where RETURN_VISIT_RECORD_ID = #{returnVisitRecordId,jdbcType=INTEGER}
  </select>

  <select id="getReturnVisitRecordListByAfterSaleId"
          resultType="com.vedeng.orderstream.aftersales.model.dto.ReturnVisitRecordDto">
    SELECT
	R.*,
	U.USERNAME AS CREATOR_NAME
    FROM
	T_RETURN_VISIT_RECORD R
	LEFT JOIN T_USER U ON R.CREATOR = U.USER_ID
    WHERE
    AFTER_SALES_ID = #{afterSaleId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_RETURN_VISIT_RECORD
    where RETURN_VISIT_RECORD_ID = #{returnVisitRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="RETURN_VISIT_RECORD_ID" keyProperty="returnVisitRecordId" parameterType="com.vedeng.orderstream.aftersales.model.ReturnVisitRecord" useGeneratedKeys="true">
    insert into T_RETURN_VISIT_RECORD (AFTER_SALES_ID, CUSTOMER_NAME, CUSTOMER_NATURE, 
      CUSTOMER_MOBILE, `STATUS`, RETURN_VISIT_DEPARTMENT, 
      SOUND_RECORD_ID, SERVICE_RESPONSE_SCORE, SERVICE_ATTITUDE_SCORE, 
      SERVICE_CAPABILITY_SCORE, IS_COMPLAINT, IS_RECOMMEND, 
      TOTAL_SCORE, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, COMMENTS
      )
    values (#{afterSalesId,jdbcType=INTEGER}, #{customerName,jdbcType=VARCHAR}, #{customerNature,jdbcType=INTEGER},
      #{customerMobile,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{returnVisitDepartment,jdbcType=INTEGER},
      #{soundRecordId,jdbcType=INTEGER}, #{serviceResponseScore,jdbcType=INTEGER}, #{serviceAttitudeScore,jdbcType=INTEGER}, 
      #{serviceCapabilityScore,jdbcType=INTEGER}, #{isComplaint,jdbcType=INTEGER}, #{isRecommend,jdbcType=INTEGER},
      #{totalScore,jdbcType=FLOAT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{comments,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="RETURN_VISIT_RECORD_ID" keyProperty="returnVisitRecordId" parameterType="com.vedeng.orderstream.aftersales.model.ReturnVisitRecord" useGeneratedKeys="true">
    insert into T_RETURN_VISIT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="customerName != null">
        CUSTOMER_NAME,
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE,
      </if>
      <if test="customerMobile != null">
        CUSTOMER_MOBILE,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="returnVisitDepartment != null">
        RETURN_VISIT_DEPARTMENT,
      </if>
      <if test="soundRecordId != null">
        SOUND_RECORD_ID,
      </if>
      <if test="serviceResponseScore != null">
        SERVICE_RESPONSE_SCORE,
      </if>
      <if test="serviceAttitudeScore != null">
        SERVICE_ATTITUDE_SCORE,
      </if>
      <if test="serviceCapabilityScore != null">
        SERVICE_CAPABILITY_SCORE,
      </if>
      <if test="isComplaint != null">
        IS_COMPLAINT,
      </if>
      <if test="isRecommend != null">
        IS_RECOMMEND,
      </if>
      <if test="totalScore != null">
        TOTAL_SCORE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerNature != null">
        #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="customerMobile != null">
        #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="returnVisitDepartment != null">
        #{returnVisitDepartment,jdbcType=INTEGER},
      </if>
      <if test="soundRecordId != null">
        #{soundRecordId,jdbcType=INTEGER},
      </if>
      <if test="serviceResponseScore != null">
        #{serviceResponseScore,jdbcType=INTEGER},
      </if>
      <if test="serviceAttitudeScore != null">
        #{serviceAttitudeScore,jdbcType=INTEGER},
      </if>
      <if test="serviceCapabilityScore != null">
        #{serviceCapabilityScore,jdbcType=INTEGER},
      </if>
      <if test="isComplaint != null">
        #{isComplaint,jdbcType=INTEGER},
      </if>
      <if test="isRecommend != null">
        #{isRecommend,jdbcType=INTEGER},
      </if>
      <if test="totalScore != null">
        #{totalScore,jdbcType=FLOAT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.orderstream.aftersales.model.ReturnVisitRecord">
    update T_RETURN_VISIT_RECORD
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="customerMobile != null">
        CUSTOMER_MOBILE = #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=INTEGER},
      </if>
      <if test="returnVisitDepartment != null">
        RETURN_VISIT_DEPARTMENT = #{returnVisitDepartment,jdbcType=INTEGER},
      </if>
      <if test="soundRecordId != null">
        SOUND_RECORD_ID = #{soundRecordId,jdbcType=INTEGER},
      </if>
      <if test="serviceResponseScore != null">
        SERVICE_RESPONSE_SCORE = #{serviceResponseScore,jdbcType=INTEGER},
      </if>
      <if test="serviceAttitudeScore != null">
        SERVICE_ATTITUDE_SCORE = #{serviceAttitudeScore,jdbcType=INTEGER},
      </if>
      <if test="serviceCapabilityScore != null">
        SERVICE_CAPABILITY_SCORE = #{serviceCapabilityScore,jdbcType=INTEGER},
      </if>
      <if test="isComplaint != null">
        IS_COMPLAINT = #{isComplaint,jdbcType=INTEGER},
      </if>
      <if test="isRecommend != null">
        IS_RECOMMEND = #{isRecommend,jdbcType=INTEGER},
      </if>
      <if test="totalScore != null">
        TOTAL_SCORE = #{totalScore,jdbcType=FLOAT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
    </set>
    where RETURN_VISIT_RECORD_ID = #{returnVisitRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.orderstream.aftersales.model.ReturnVisitRecord">
    update T_RETURN_VISIT_RECORD
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
      CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      CUSTOMER_MOBILE = #{customerMobile,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=INTEGER},
      RETURN_VISIT_DEPARTMENT = #{returnVisitDepartment,jdbcType=INTEGER},
      SOUND_RECORD_ID = #{soundRecordId,jdbcType=INTEGER},
      SERVICE_RESPONSE_SCORE = #{serviceResponseScore,jdbcType=INTEGER},
      SERVICE_ATTITUDE_SCORE = #{serviceAttitudeScore,jdbcType=INTEGER},
      SERVICE_CAPABILITY_SCORE = #{serviceCapabilityScore,jdbcType=INTEGER},
      IS_COMPLAINT = #{isComplaint,jdbcType=INTEGER},
      IS_RECOMMEND = #{isRecommend,jdbcType=INTEGER},
      TOTAL_SCORE = #{totalScore,jdbcType=FLOAT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      COMMENTS = #{comments,jdbcType=VARCHAR}
    where RETURN_VISIT_RECORD_ID = #{returnVisitRecordId,jdbcType=INTEGER}
  </update>
</mapper>