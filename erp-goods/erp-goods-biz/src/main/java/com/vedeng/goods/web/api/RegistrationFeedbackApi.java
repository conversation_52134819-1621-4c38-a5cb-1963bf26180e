//package com.vedeng.goods.web.api;
//
//import com.vedeng.common.core.base.R;
//import com.vedeng.goods.domain.entity.RegistrationFeedbackRecordEntity;
//import com.vedeng.goods.service.RegistrationFeedbackRecordService;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @create 2022−10-31 上午10:23
// * @description
// */
//@Controller
//@RequestMapping("/registrationFeedbackController")
//public class RegistrationFeedbackApi{
//
//    @Resource
//    RegistrationFeedbackRecordService feedbackRecordService;
//
//    /**
//     * 新增注册证反馈记录
//     * @param registrationFeedbackRecord
//     * @return
//     */
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ResponseBody
//    public R<?> add(RegistrationFeedbackRecordEntity registrationFeedbackRecord){
//        //保存到数据库
//        feedbackRecordService.addFeedbackRecord(registrationFeedbackRecord);
//        //发送站内信
//        feedbackRecordService.sendMessage(registrationFeedbackRecord);
//        return R.success("");
//    }
//}
