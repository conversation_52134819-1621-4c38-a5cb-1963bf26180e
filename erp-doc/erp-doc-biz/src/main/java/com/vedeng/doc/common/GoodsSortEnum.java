package com.vedeng.doc.common;

/**
 * 彩页、招标参数(技术参数)、配置清单、检验报告、注册证
 *
 * <AUTHOR>
 * @create 2022/1/10 20:36
 */
public enum GoodsSortEnum {

    COLORING_PAGE(1, "彩页"),
    TECHNICAL_PARAMETER(2, "招标参数(技术参数)"),
    CONFIGURATION_LIST(3, "配置清单"),
    INSPECTION_REPORT(4, "检验报告"),
    REGISTRATION_CERTIFICATE(5, "注册证");

    private Integer sort;
    private String tagName;

    GoodsSortEnum(Integer sort, String tagName) {
        this.sort = sort;
        this.tagName = tagName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }
}
