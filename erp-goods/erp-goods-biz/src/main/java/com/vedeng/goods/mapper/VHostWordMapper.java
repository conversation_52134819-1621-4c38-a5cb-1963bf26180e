package com.vedeng.goods.mapper;

import com.vedeng.goods.dto.VHostWordDTO;
import com.vedeng.goods.dto.VHostWordDTOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository("hostWordMapper")
public interface VHostWordMapper {
    long countByExample(VHostWordDTOExample example);

    int deleteByExample(VHostWordDTOExample example);

    int deleteByPrimaryKey(Integer hostWordId);

    int insert(VHostWordDTO record);

    int insertSelective(VHostWordDTO record);

    List<VHostWordDTO> selectByExample(VHostWordDTOExample example);

    VHostWordDTO selectByPrimaryKey(Integer hostWordId);

    int updateByExampleSelective(@Param("record") VHostWordDTO record, @Param("example") VHostWordDTOExample example);

    int updateByExample(@Param("record") VHostWordDTO record, @Param("example") VHostWordDTOExample example);

    int updateByPrimaryKeySelective(VHostWordDTO record);

    int updateByPrimaryKey(VHostWordDTO record);

    VHostWordDTO getHostWordByOpHostId(Integer opHostWordId);
}
