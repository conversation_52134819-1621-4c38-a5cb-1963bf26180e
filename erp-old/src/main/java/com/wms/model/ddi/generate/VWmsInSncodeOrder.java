package com.wms.model.ddi.generate;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * V_WMS_IN_SNCODE_ORDER
 * <AUTHOR>
@Data
public class VWmsInSncodeOrder implements Serializable {
    /**
     * 入库SN码追溯表id
     */
    private Integer inSncodeOrderId;

    /**
     * 关联订单id
     */
    private Integer orderId;

    /**
     * 关联订单商品id
     */
    private Integer orderGoodsId;

    /**
     * 业务类型 0采购入库 1销售售后入库 2采购售后入库 3盘盈入库 4借货归还 5库存初始化入库单 6赠品入库
     */
    private Integer operateType;

    /**
     * WMS上架交易号
     */
    private String wmsOrderNo;

    /**
     * SKU
     */
    private String sku;

    /**
     * 商品id
     */
    private Integer skuId;

    /**
     * 厂家SN码
     */
    private String snCode;

    /**
     * 贝登追溯码
     */
    private String serialNo;

    /**
     * 入库时间
     */
    private Date inTime;

    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 有效期至
     */
    private Date expirationDate;

    /**
     * 厂家批号
     */
    private String batchNumber;

    /**
     * 贝登批次码
     */
    private String vedengBatchNumer;

    /**
     * 备注
     */
    private String comments;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除  0否  1是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;
}