package com.vedeng.goods.dao;

import com.vedeng.goods.model.BaseAttributeValueGenerate;
import com.vedeng.goods.model.BaseAttributeValueGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseAttributeValueGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(BaseAttributeValueGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(BaseAttributeValueGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer baseAttributeValueId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(BaseAttributeValueGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(BaseAttributeValueGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<BaseAttributeValueGenerate> selectByExample(BaseAttributeValueGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    BaseAttributeValueGenerate selectByPrimaryKey(Integer baseAttributeValueId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") BaseAttributeValueGenerate record, @Param("example") BaseAttributeValueGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") BaseAttributeValueGenerate record, @Param("example") BaseAttributeValueGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(BaseAttributeValueGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_BASE_ATTRIBUTE_VALUE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(BaseAttributeValueGenerate record);
}