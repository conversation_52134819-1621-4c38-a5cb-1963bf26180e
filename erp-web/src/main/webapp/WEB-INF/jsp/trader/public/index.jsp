<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="公海列表" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript" src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/layui.js"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/css/layui.css"/>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/cascader.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/cascader.css"/>
<script type="text/javascript">
    var gongHaiLevelLayCascader = null;
    var invalidReasonLayCascader = null;
    $(document).ready(function(){
        initGongHaiLevelSelect();
       
    });
    $(document).ready(function(){
        initInvalidReasonSelect();
    });

    function initGongHaiLevelSelect() {
        layui.use('layCascader', function () {
            var layCascader = layui.layCascader;
            let gongHaiLevelList = $("#gongHaiLevel").val();
            if (gongHaiLevelList != null && gongHaiLevelList.length > 0) {
                gongHaiLevelList = JSON.parse(gongHaiLevelList);
            } else {
                gongHaiLevelList = [];//queryGongHaiLevelList
            }
            gongHaiLevelLayCascader = layCascader({
                elem: '#queryGongHaiLevelListSelect',
                clearable: true,
                collapseTags: true,
                filterable: true,
                value: gongHaiLevelList,
                minCollapseTagsNumber: 1,
                extendClass: true,
                extendStyle: true,
                props: {
                    multiple: true
                },
                handleChange(value){
                    console.log(value);
                },
                options: [
                    {value: 2, label: "优质客户"},
                    {value: 3, label: "重点关注客户"},
                    {value: 4, label: "潜力客户"},
                    {value: 5, label: "次要客户"},
                    {value: 1, label: "无效客户"}
                ]
            });
            
        });
    }

    function initInvalidReasonSelect() {
        layui.use('layCascader', function () {
            var layCascader = layui.layCascader;
            let invalidReasonList = $("#invalidReason").val();
            if (invalidReasonList != null && invalidReasonList.length > 0) {
                invalidReasonList = JSON.parse(invalidReasonList);
            } else {
                invalidReasonList = [];//
            }
            invalidReasonLayCascader = layCascader({
                elem: '#queryInvalidReasonListSelect',
                clearable: true,
                collapseTags: true,
                filterable: true,
                value: invalidReasonList,
                minCollapseTagsNumber: 1,
                extendClass: true,
                extendStyle: true,
                props: {
                    multiple: true
                },
                handleChange(value){
                    console.log(value);
                },
                options: [
                    {value: 1, label: "系统判定-经营状态异常"},
                    {value: 2, label: "系统判定-客户无联系人"},
                    {value: 3, label: "系统判定-客户禁用"},
                    {value: 4, label: "销售标记-公司转行"},
                    {value: 5, label: "销售标记-公司注销"},
                    {value: 6, label: "销售标记-服务型公司"},
                    {value: 7, label: "销售标记-其他"},   
                ]
            });
        });
    }
</script>
<div class="main-container">
    <div class="list-pages-search">
        <form action="${pageContext.request.contextPath}/trader/public/index.do" method="post" id="search">
            <input type="hidden" id="cityid" value="${city}"/>
            <input type="hidden" id="zoneid" value="${zone}"/>
            <input type="hidden" name="gongHaiLevel" id="gongHaiLevel"
                   value="${traderCustomerVo.queryGongHaiLevelList}">
            <input type="hidden" name="invalidReason" id="invalidReason"
                   value="${traderCustomerVo.queryInvalidReasonList}">
            <input type="hidden" name="search" value="click">
            <ul>
                <li>
                    <label class="infor_name">客户名称</label>
                    <input type="text" class="input-middle" name="name" id="name" value="${traderCustomerVo.name}">
                </li>
                <li>
                    <label class="infor_name">状态</label>
                    <select class="input-middle f_left" name="lockStatus">
                        <option value="">全部</option>
                        <option value="1"
                                <c:if test="${traderCustomerVo.lockStatus eq 1 }">selected="selected"</c:if>>锁定中</option>
                        <option value="0"
                                <c:if test="${traderCustomerVo.lockStatus eq 0 }">selected="selected"</c:if>>正常</option>
                    </select>
                </li>c
                <li>
                    <label class="infor_name">原归属销售</label>
                    <select class="input-middle f_left" name="originUserId">
                        <option value="">全部</option>
                        <c:if test="${not empty originUsers }">
                            <c:forEach items="${originUsers }" var="user">
                                <option value="${user.userId }"
                                        <c:if test="${traderCustomerVo.originUserId !=null and traderCustomerVo.originUserId  eq user.userId }">selected="selected"</c:if>>${user.username }</option>
                            </c:forEach>
                        </c:if>
                    </select>
                </li>
                <li>
                    <label class="infor_name">客户类型</label>
                    <select class="input-middle f_left" name="customerType">
                        <option value="0">全部</option>
                        <option value="427"
                                <c:if test="${traderCustomerVo.customerType eq 427 }">selected="selected"</c:if>>临床医疗
                        </option>
                        <option value="426"
                                <c:if test="${traderCustomerVo.customerType eq 426 }">selected="selected"</c:if>>科研医疗
                        </option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">客户性质</label>
                    <select class="input-middle f_left" name="customerNature">
                        <option value="0">全部</option>
                        <option value="465"
                                <c:if test="${traderCustomerVo.customerNature eq 465 }">selected="selected"</c:if>>分销
                        </option>
                        <option value="466"
                                <c:if test="${traderCustomerVo.customerNature eq 466 }">selected="selected"</c:if>>终端
                        </option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">有合作</label>
                    <select
                            class="input-middle f_left" name="cooperate">
                        <option value="">全部</option>
                        <option value="1"
                                <c:if test="${traderCustomerVo.cooperate eq 1 }">selected="selected"</c:if>>是
                        </option>
                        <option value="2"
                                <c:if test="${traderCustomerVo.cooperate eq 2 }">selected="selected"</c:if>>否
                        </option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">有报价</label>
                    <select
                            class="input-middle f_left" name="quote">
                        <option value="0">全部</option>
                        <option value="1"
                                <c:if test="${traderCustomerVo.quote eq 1 }">selected="selected"</c:if>>是
                        </option>
                        <option value="2"
                                <c:if test="${traderCustomerVo.quote eq 2 }">selected="selected"</c:if>>否
                        </option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">信息搜索</label>
                    <input type="text"
                           class="input-middle" placeholder="沟通记录/商机信息"
                           name="searchMsg" id=""
                           value="${traderCustomerVo.searchMsg}">
                </li>
                <li><label class="infor_name">客户等级</label> <select
                        class="input-middle f_left" name="traderCustomerLevelGrade">
                    <option value="">全部</option>
                    <c:forEach items="${traderCustomerLevelGradeList}" var="level">
                        <option value="${level}" <c:if test="${traderCustomerVo.traderCustomerLevelGrade == level}">selected="selected"</c:if>>${level}</option>
                    </c:forEach>
                </select></li>
<%--                <li>--%>
<%--                    <label class="infor_name">是否可改价</label>--%>
<%--                    <select class="input-middle f_left" name="isLimitprice">--%>
<%--                        <option value="">全部</option>--%>
<%--                        <option value="0"--%>
<%--                                <c:if test="${traderCustomerVo.isLimitprice eq 0 }">selected="selected"</c:if>>可改价--%>
<%--                        </option>--%>
<%--                        <option value="1"--%>
<%--                                <c:if test="${traderCustomerVo.isLimitprice eq 1 }">selected="selected"</c:if>>不可改价--%>
<%--                        </option>--%>
<%--                    </select>--%>
<%--                </li>--%>
                <li>
                    <label class="infor_name">资质审核状态</label>
                    <select class="input-middle f_left" name="aptitudeStatus">
                        <option value="">全部</option>
                        <option value="3"
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 3 }">selected="selected"</c:if>>待审核
                        </option>
                        <option value="4"
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 4 }">selected="selected"</c:if>>初审中
                        </option>
                        <option value="5"
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 5 }">selected="selected"</c:if>>公章审核
                        </option>
                        <option value="0"
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 0 }">selected="selected"</c:if>>复审中
                        </option>
                        <option value="1"
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 1 }">selected="selected"</c:if>>审核通过
                        </option>
                        <option value="2"
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 2 }">selected="selected"</c:if>>审核不通过
                        </option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">联系方式</label>
                    <input type="text"
                           class="input-middle" placeholder="电话/手机/QQ/微信/邮箱"
                           name="contactWay"
                           id="contactWay"
                           value="${traderCustomerVo.contactWay}">
                </li>
<%--                <li>--%>
<%--                    <label class="infor_name">基层医疗经销商</label>--%>
<%--                    <select--%>
<%--                            class="input-middle f_left" name="basicMedicalDealer">--%>
<%--                        <option value="-1">全部</option>--%>
<%--                        <option value="1"--%>
<%--                                <c:if test="${traderCustomerVo.basicMedicalDealer eq 1 }">selected="selected"</c:if>>是--%>
<%--                        </option>--%>
<%--                        <option value="0"--%>
<%--                                <c:if test="${traderCustomerVo.basicMedicalDealer eq 0 }">selected="selected"</c:if>>否--%>
<%--                        </option>--%>
<%--                    </select>--%>
<%--                </li>--%>
                <li>
                    <label class="infor_name">归属平台</label>
                    <select class="input-middle f_left" name="belongPlatform">
                        <option value="">全部</option>
                        <option value="1"
                                <c:if test="${traderCustomerVo.belongPlatform eq 1 }">selected="selected"</c:if>>贝登医疗
                        </option>
                        <option value="2"
                                <c:if test="${traderCustomerVo.belongPlatform eq 2 }">selected="selected"</c:if>>医械购
                        </option>
                        <option value="3"
                                <c:if test="${traderCustomerVo.belongPlatform eq 3 }">selected="selected"</c:if>>科研购
                        </option>
                        <option value="5"
                                <c:if test="${traderCustomerVo.belongPlatform eq 5 }">selected="selected"</c:if>>其他
                        </option>
                        <option value="6"
                                <c:if test="${traderCustomerVo.belongPlatform eq 6 }">selected="selected"</c:if>>集采
                        </option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">挖掘价值</label>
                    <select class="input-middle f_left" name="conditionType">
                        <option value="">全部</option>
                        <option value="1"
                                <c:if test="${traderCustomerVo.conditionType eq 1 }">selected="selected"</c:if>>联系健全</option>
                        <option value="2"
                                <c:if test="${traderCustomerVo.conditionType eq 2 }">selected="selected"</c:if>>线上客户</option>
                        <option value="3"
                                <c:if test="${traderCustomerVo.conditionType eq 3 }">selected="selected"</c:if>>线索客户</option>
                        <option value="4"
                                <c:if test="${traderCustomerVo.conditionType eq 4 }">selected="selected"</c:if>>商机客户</option>
                        <option value="5"
                                <c:if test="${traderCustomerVo.conditionType eq 5 }">selected="selected"</c:if>>报价客户</option>
                        <option value="6"
                                <c:if test="${traderCustomerVo.conditionType eq 6 }">selected="selected"</c:if>>交易客户</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name" title="公海客户等级划分规则：
无效客户： ①客户禁用，或②天眼查经营状态不正常，或③无客户联系人信息；
优质客户： 近两年订单数>0且近一年中标次数>0；
重点关注客户： 近两年订单数>0且近一年中标次数=0；
潜力客户： 近两年订单数=0且近一年中标次数>0；
次要客户： 近两年订单数=0且近一年中标次数=0；
">公海客户等级<i class="vd-tip-icon vd-icon icon-info2 goods-required-item" style="color: rgb(239, 200, 89); background: none;"></i></label>
                    <div class="input-middle f_left">
                        <input type="hidden" id="queryGongHaiLevelList" name="queryGongHaiLevelList" value=""  >
                        <!-- 这个元素为form-submit提交时，取queryGongHaiLevelListSelect的值，将[1,2,3,5]外的中括号去掉，以实现spring-form-submit 的自动转换 -->
                        <input id="queryGongHaiLevelListSelect" name="queryGongHaiLevelListSelect" style="width: 100%">
                    </div>
                </li>
                <li>
                    <label class="infor_name" title="【有效】：正常营业
【无效】：营业范围变更（以后不做医疗器械了）；公司注销等
PS：此为销售在客户基本信息中填写">客户有效性<i class="vd-tip-icon vd-icon icon-info2 goods-required-item" style="color: rgb(239, 200, 89); background: none;"></i></label>
                    <select class="input-middle f_left" name="effectiveness">
                        <option value="">全部</option>
                        <option value="1"
                                <c:if test="${traderCustomerVo.effectiveness eq 1 }">selected="selected"</c:if>>有效</option>
                        <option value="0"
                                <c:if test="${traderCustomerVo.effectiveness eq 0 }">selected="selected"</c:if>>无效</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">无效客户原因</label>
                    <div class="input-middle f_left">
                        <input type="hidden" id="queryInvalidReasonList" name="queryInvalidReasonList" value=""  >
                        <input id="queryInvalidReasonListSelect" name="queryInvalidReasonListSelect" style="width: 100%;width:250px">
                    </div>
                </li>
                <li>
                    <label class="infor_name">地区</label>
                    <ul class="inputfloat f_left">
                        <li>
                            <select class="wid9" name="province" id="province">
                                <option value="0">全部</option>
                                <c:if test="${not empty provinceList }">
                                    <c:forEach items="${provinceList }" var="prov">
                                        <option value="${prov.regionId }"
                                                <c:if test="${traderCustomerVo.province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                        <li>
                            <select class="wid9" name="city" id="city">
                                <option value="0">全部</option>
                                <c:if test="${not empty cityList }">
                                    <c:forEach items="${cityList }" var="cy">
                                        <option value="${cy.regionId }"
                                                <c:if test="${traderCustomerVo.city eq cy.regionId }">selected="selected"</c:if>>${cy.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                        <li>
                            <select class="wid9" name="zone" id="zone">
                                <option value="0">全部</option>
                                <c:if test="${not empty zoneList }">
                                    <c:forEach items="${zoneList }" var="zo">
                                        <option value="${zo.regionId }"
                                                <c:if test="${traderCustomerVo.zone eq zo.regionId }">selected="selected"</c:if>>${zo.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                    </ul>
                </li>
<%--                <li>--%>
<%--                    <div class="infor_name">客户得分</div>--%>
<%--                    <input type="text" class="f_left" name="smallScore" value="${traderCustomerVo.smallScore}"--%>
<%--                           style='width:86px;'>--%>
<%--                    <div class="gang">-</div>--%>
<%--                    <input type="text" class="f_left" name="bigScore" value="${traderCustomerVo.bigScore}"--%>
<%--                           style='width:86px;'>--%>
<%--                </li>--%>
                <li>
                    <div class="infor_name" style='margin-top:1px; width: 120px;'>
                        <select name="timeType">
                            <option value="1"
                                    <c:if test="${traderCustomerVo.timeType eq 1 }">selected="selected"</c:if>>创建时间
                            </option>
                            <option value="2"
                                    <c:if test="${traderCustomerVo.timeType eq 2 }">selected="selected"</c:if>>交易时间
                            </option>
                            <option value="3"
                                    <c:if test="${traderCustomerVo.timeType eq 3 }">selected="selected"</c:if>>更新时间
                            </option>
                            <option value="4"
                                    <c:if test="${traderCustomerVo.timeType eq 4 }">selected="selected"</c:if>>最近沟通时间
                            </option>
                        </select>
                    </div>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endTime\')}'})"
                           name="queryStartTime" id="startTime" value=<date:date
                            value="${traderCustomerVo.startTime} "/>>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startTime\')}'})"
                           name="queryEndTime" id="endTime" value=<date:date value="${traderCustomerVo.endTime} "/>>
                </li>
            </ul>
            <div style="position: relative">
                <div style="  bottom: 8px;">
                    提醒：24小时内对该客户有2分钟通话或新建商机或新增有生效订单行为，可解锁该客户。
                </div>
            </div>
            <div class="tcenter">
                <span class="bg-light-blue bt-bg-style bt-middle" id="searchSpan" onclick="searchTraderListForGongHai();">搜索</span>
                <span class="bt-middle bg-light-blue bt-bg-style" onclick="resettForGongHai();">重置</span>
                
                <script type="text/javascript">
                    function searchTraderListForGongHai() {
                        var selectVal = document.getElementById("queryGongHaiLevelListSelect").value;
                        var selectListJson = JSON.parse(selectVal==""?"[]":selectVal);
                        var queryGongHaiLevelList = selectListJson.join(',');
                        $("#queryGongHaiLevelList").val(queryGongHaiLevelList);

                        var selectVal2 = document.getElementById("queryInvalidReasonListSelect").value;
                        var selectListJson2 = JSON.parse(selectVal2==""?"[]":selectVal2);
                        var queryInvalidReasonList = selectListJson2.join(',');
                        $("#queryInvalidReasonList").val(queryInvalidReasonList);
                        search();
                    }

                    function resettForGongHai(){
                        debugger
                        var gongHaiLevel = document.getElementById("gongHaiLevel").value;
                        if(gongHaiLevelLayCascader !=null){
                            gongHaiLevelLayCascader.setValue(gongHaiLevel);
                        }

                        $("#invalidReason").val("");
                        if(invalidReasonLayCascader !=null){
                            invalidReasonLayCascader.setValue("");
                        }
                       
                        reset();
                    }
                    
                </script>
                <span class="bg-light-blue bt-bg-style bt-middle addtitle"
                      tabTitle='{"num":"publicTrader","link":"./trader/customer/publicCustomerRules.do","title":"公海规则"}'>公海规则</span>
            </div>
        </form>
    </div>

    <div class="fixdiv">
        <div class="superdiv" style="margin:auto;">
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <c:if test="${isShow}">
                        <th class="wid4">选择</th>
                    </c:if>
                    <th class="sorts">序号</th>
                    <th class="wid22">客户名称</th>
                    <th class="wid10">状态</th>
                    <th class="wid20">地区</th>
                    <th class="wid10">公海客户等级</th>
                    <th class="wid8">客户类型</th>
                    <th class="wid7">客户性质</th>
                    <th class="wid10">贝登商城会员</th>
                    <th class="wid7">交易次数</th>
                    <th class="wid8">交易金额</th>
                    <th class="wid7">报价次数</th>
                    <th class="wid7">沟通次数</th>
                    <th class="wid7">客户等级</th>
                <%--                    <th class="wid7">客户得分</th>--%>
                    <th class="wid10">客户资质审核</th>
                <%--                    <th class="wid10">基层医疗经销商</th>--%>
                    <th class="wid8">客户有效性</th>
                    <th class="wid15">锁定人</th>
                    <th class="wid15">原归属销售</th>
                    <th class="wid15">最近沟通时间</th>
                    <th class="wid15">更新时间</th>
                    <th class="wid16">操作</th>
                </tr>
                </thead>
                <tbody class="employeestate">
                <c:if test="${not empty list}">
                    <c:forEach items="${list}" var="traderCustomerVo" varStatus="status">
                        <tr>
                            <input type="hidden" name="publicTraderCustomer${traderCustomerVo.publicCustomerRecordId}">
                            <input type="hidden"
                                   name="publicTraderCustomerGroup${traderCustomerVo.associatedCustomerGroup}">
                            <c:if test="${isShow}">
                                <td>
                                    <input type="checkbox" name="checkPublicTraderCustomer" value="${traderCustomerVo.publicCustomerRecordId}" associatedCustomerGroup="${traderCustomerVo.associatedCustomerGroup}">
                                </td>
                            </c:if>
                            <td>${status.count}</td>
                            <td class="text-center">
                                <div style="display: none"
                                     class="aPagePublicTraderCustomer${traderCustomerVo.associatedCustomerGroup}"
                                     id="aPagePublicTraderCustomer${traderCustomerVo.publicCustomerRecordId}">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                "link":"./trader/customer/baseinfo.do?traderId=${traderCustomerVo.traderId}",
                                                "title":"客户信息"}'>
                                        <c:if test="${not empty traderGroupMap && not empty traderGroupMap[traderCustomerVo.traderId]}">
                                            <span style="color: red">【${traderGroupMap[traderCustomerVo.traderId].traderGroupName}】</span>
                                        </c:if>
                                            ${traderCustomerVo.name}
                                    </a>
<%--                                    <c:if test="${traderCustomerVo.isView eq 1 && curr_user.positType eq 310}">--%>
<%--                                        <a class="addtitle" href="javascript:void(0);"--%>
<%--                                           tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",--%>
<%--                                                "link":"./trader/customer/baseinfo.do?traderId=${traderCustomerVo.traderId}",--%>
<%--                                                "title":"客户信息"}'>--%>
<%--                                            <c:if test="${not empty traderGroupMap && not empty traderGroupMap[traderCustomerVo.traderId]}">--%>
<%--                                                <span style="color: red">【${traderGroupMap[traderCustomerVo.traderId].traderGroupName}】</span>--%>
<%--                                            </c:if>--%>
<%--                                                ${traderCustomerVo.name}--%>
<%--                                        </a>--%>
<%--                                    </c:if>--%>
<%--                                    <c:if test="${traderCustomerVo.isView ne 1 && curr_user.positType eq 310}">--%>
<%--                                        <c:if test="${not empty traderGroupMap && not empty traderGroupMap[traderCustomerVo.traderId]}">--%>
<%--                                            <span style="color: red">【${traderGroupMap[traderCustomerVo.traderId].traderGroupName}】</span>--%>
<%--                                        </c:if>--%>
<%--                                        ${traderCustomerVo.name}--%>
<%--                                    </c:if>--%>
<%--                                    <c:if test="${curr_user.positType ne 310}">--%>
<%--                                        <a class="addtitle" href="javascript:void(0);"--%>
<%--                                           tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",--%>
<%--                                                "link":"./trader/customer/baseinfo.do?traderId=${traderCustomerVo.traderId}",--%>
<%--                                                "title":"客户信息"}'>--%>
<%--                                            <c:if test="${not empty traderGroupMap && not empty traderGroupMap[traderCustomerVo.traderId]}">--%>
<%--                                                <span style="color: red">【${traderGroupMap[traderCustomerVo.traderId].traderGroupName}】</span>--%>
<%--                                            </c:if>--%>
<%--                                                ${traderCustomerVo.name}--%>
<%--                                        </a>--%>
<%--                                        ${traderCustomerVo.verifyStatus == 0 && fn:contains(traderCustomerVo.verifyUsername, curr_user.username) ?"<font color='red'>[审]</font>":""}--%>
<%--                                    </c:if>--%>
                                </div>
                                <div class="textPagePublicTraderCustomer${traderCustomerVo.associatedCustomerGroup}"
                                     id="textPagePublicTraderCustomer${traderCustomerVo.publicCustomerRecordId}">
                                        ${traderCustomerVo.name}
                                </div>
                            </td>
                            <td>
                                <c:if test="${traderCustomerVo.isPrivatized eq 1}">锁定中</c:if>
                                <c:if test="${traderCustomerVo.isPrivatized eq 0}">正常</c:if>
                            </td>
                            <td>${traderCustomerVo.address }</td>
                            <td>
                                <c:if test="${not empty traderCustomerVo.gonghaiLevel }">
                                    <c:if test="${traderCustomerVo.gonghaiLevel eq 2}">优质客户</c:if>
                                    <c:if test="${traderCustomerVo.gonghaiLevel eq 3}">重点关注客户
                                       </c:if>
                                    <c:if test="${traderCustomerVo.gonghaiLevel eq 4}">潜力客户</c:if>
                                    <c:if test="${traderCustomerVo.gonghaiLevel eq 5}">次要客户</c:if>
                                    <c:if test="${traderCustomerVo.gonghaiLevel eq 1}">无效客户
                                        <c:if test="${(traderCustomerVo.systemJudge ne null and traderCustomerVo.systemJudge ne '')
                                         or (traderCustomerVo.userJudge ne '' and traderCustomerVo.userJudge ne null)}">
                                        <div class="tooltip">
                                            <i class="vd-tip-icon vd-icon icon-info2 goods-required-item" 
                                               style="color: skyblue; background: none;"></i>
                                                <div class="tooltiptext">
                                                    <div class="tooltip-line">无效原因：</div>
                                                    <div class="tooltip-line">${traderCustomerVo.systemJudge}</div>
                                                    <div class="tooltip-line">${traderCustomerVo.userJudge}</div>
                                                </div>
                                        </div>
                                        </c:if>
                                    </c:if>
                                </c:if>
                            </td>
                            <td>${traderCustomerVo.customerTypeStr }</td>
                            <td>${traderCustomerVo.customerNatureStr }</td>
                            <td>${traderCustomerVo.isVedengMember==1?"是":"否"}</td>
                            <td>${traderCustomerVo.buyCount}</td>
                            <td><fmt:formatNumber type="number" value="${traderCustomerVo.buyMoney}" pattern="0.00"
                                                  maxFractionDigits="2"/></td>
                            <td>${traderCustomerVo.quoteCount}</td>
                            <td>${traderCustomerVo.communcateCount}</td>
                            <td>${traderCustomerVo.traderCustomerLevelGrade}</td>
<%--                            <td>${traderCustomerVo.customerScore}</td>--%>
                            <td>
                                <c:if test="${empty traderCustomerVo.aptitudeStatus || traderCustomerVo.aptitudeStatus eq 3}">待审核</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 4}">初审中</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 5}">公章审核</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 0}">复审中</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 1}">审核通过</c:if>
                                <c:if test="${traderCustomerVo.aptitudeStatus eq 2}">审核不通过</c:if>
                            </td>
                            <td>
                                <c:if test="${not empty traderCustomerVo.effectiveness }">
                                    <c:if test="${traderCustomerVo.effectiveness eq 1}">有效</c:if>
                                    <c:if test="${traderCustomerVo.effectiveness eq 0}">无效</c:if>
                                </c:if>
                            </td>
<%--                            <td><c:if--%>
<%--                                    test="${traderCustomerVo.basicMedicalDealer eq 0}">否</c:if> <c:if--%>
<%--                                    test="${traderCustomerVo.basicMedicalDealer eq 1}">是</c:if></td>--%>
                            <td>${traderCustomerVo.lockUserName}</td>
                            <td>${traderCustomerVo.originUserName}</td>
                            <td><date:date value="${traderCustomerVo.lastCommuncateTime} "/></td>
                            <td><date:date value="${traderCustomerVo.modTime} "/></td>
                            <td>
                                <c:if test="${traderCustomerVo.isPrivatized eq 1}">
<%--                                    锁定--%>
                                    <span class="bg-light-blue bt-bg-style bt-smallest cancel-lock-public-trader-btn-${traderCustomerVo.associatedCustomerGroup}"
                                          id="cancel-lock-public-trader-btn-${traderCustomerVo.publicCustomerRecordId}"
                                          onclick="revokeLockPublicTraderCustomer(${traderCustomerVo.publicCustomerRecordId},${traderCustomerVo.associatedCustomerGroup});">撤销锁定</span>
                                </c:if>
                                <c:if test="${traderCustomerVo.isPrivatized eq 0}">
                                    <span class="bg-light-blue bt-bg-style bt-smallest lock-public-trader-btn-${traderCustomerVo.associatedCustomerGroup}"
                                          id="lock-public-trader-btn-${traderCustomerVo.publicCustomerRecordId}"
                                          onclick="lockPublicTraderCustomer(${traderCustomerVo.publicCustomerRecordId},${traderCustomerVo.associatedCustomerGroup});">锁定</span>
                                    <span class="bg-light-blue bt-bg-style bt-smallest cancel-public-trader-btn-${traderCustomerVo.associatedCustomerGroup}"
                                          id="cancel-public-trader-btn-${traderCustomerVo.publicCustomerRecordId}"
                                          onclick="revokePublicTraderCustomer(${traderCustomerVo.publicCustomerRecordId},${traderCustomerVo.associatedCustomerGroup});">撤销公海</span>
                                </c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty list}">
                    <c:choose>
                        <c:when test="${isShow}"><td colspan="20">查询无结果！请尝试使用其他搜索条件。</td></c:when>
                        <c:otherwise><td colspan="19">查询无结果！请尝试使用其他搜索条件。</td></c:otherwise>
                    </c:choose>

                </c:if>
                </tbody>
            </table>
        </div>
    </div>
    <div>
        <c:if test="${isShow}">
            <div class="inputfloat f_left">
                <input type="checkbox" class="mt6 mr4" id="selectAll"
                       onclick="selectAll(this);"> <label class="mr10 mt4">选中本页</label>
                <span class="bt-bg-style bg-light-blue bt-small mr10"
                      onclick="batchRevokePublicTraderCustomer(1,1);">批量撤销</span>
            </div>
        </c:if>

        <tags:page page="${page}" />
    </div>
<%--    <tags:page page="${page}"/>--%>
</div>
<style>
    .el-cascader__tags{
       /* width:50px;*/
        padding-top: 5px;
    }
    .list-pages-search .el-cascader {
        line-height: 26px;
        height: 26px;
        width: 70%
    }
    .el-cascader__search-input {
        margin-top: 2px;
    }
    .el-cascader .el-input .el-input__inner {
        height: 26px;
        line-height: 26px;
    }

    .el-cascader i, .el-cascader-panel i {
        height: auto;
        background: none;
    }

    .el-cascader .el-cascader__tags {
        flex-wrap: inherit;
    }
    .vd-icon {
        cursor: help;
        vertical-align: -3px;
        font-family: 'HC' !important;
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
    }
    .el-cascader__search-input{
        min-width: 10px;
    }
    .el-cascader-menu{
        width : 280px !important;
        min-width: 250px;
    }
    /*修改菜单高度*/
    .el-cascader-menu__wrap {
        height: 300px;
    }

    /* 工具提示容器 */
    .tooltip {
        position: relative;
        display: inline-block;
    }

    /* 工具提示文本 */
    .tooltip .tooltiptext {
        visibility: hidden;
        background-color: #555;
        color: #fff;
        text-align: left; /* 左对齐 */
        position: absolute;
        padding: 5px;
        z-index: 1;
        top: 50%; /* 垂直居中 */
        left: 110%; /* 将提示框放在图标右侧 */
        transform: translateY(-50%); /* 确保垂直居中 */
        opacity: 0;
        transition: opacity 0.3s;
        white-space: nowrap; /* 防止文本换行 */
        min-height: 70px;
        width: 280px;
    }

    /* 每行文字的样式 */
    .tooltip .tooltiptext .tooltip-line {
        display: block; /* 每行显示为块级元素 */
        line-height: 1.5; /* 调整行高 */
    }

    /* 工具提示箭头 */
    .tooltip .tooltiptext::after {
        content: "";
        position: absolute;
        top: 50%;
        right: 100%; /* 将箭头放在提示框左侧 */
        margin-top: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: transparent #555 transparent transparent;
    }

    /* 鼠标悬停时显示提示框 */
    .tooltip:hover .tooltiptext {
    /*.tooltip .tooltiptext {*/
        visibility: visible;
        opacity: 1;
    }
</style>
<%@ include file="../../common/footer.jsp" %>
<script type="text/javascript" src="<%=basePath%>/static/js/customer/index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="<%=basePath%>/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/trader/public/index.css">
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/trader/public/index.js?rnd=${resourceVersionKey}"></script>
