<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsInputOrderGoodsMapper" >
  <resultMap id="BaseResultMap" type="com.wms.model.po.WmsInputOrderGoods" >
    <!--          -->
    <id column="WMS_INPUT_ORDER_GOODS_ID" property="wmsInputOrderGoodsId" jdbcType="INTEGER" />
    <result column="WMS_INPUT_ORDER_ID" property="wmsInputOrderId" jdbcType="INTEGER" />
    <result column="SKU_NO" property="skuNo" jdbcType="VARCHAR" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="INPUT_NUM" property="inputNum" jdbcType="INTEGER" />
    <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER" />
    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MODE_TIME" property="modeTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_DELTET" property="isDeltet" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    WMS_INPUT_ORDER_GOODS_ID, WMS_INPUT_ORDER_ID, SKU_NO, GOODS_ID, INPUT_NUM, ARRIVAL_NUM, 
    ARRIVAL_STATUS, ADD_TIME, MODE_TIME, CREATOR, UPDATER, IS_DELTET
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_INPUT_ORDER_GOODS
    where WMS_INPUT_ORDER_GOODS_ID = #{wmsInputOrderGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--          -->
    delete from T_WMS_INPUT_ORDER_GOODS
    where WMS_INPUT_ORDER_GOODS_ID = #{wmsInputOrderGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.wms.model.po.WmsInputOrderGoods" >
    <!--          -->
    insert into T_WMS_INPUT_ORDER_GOODS (WMS_INPUT_ORDER_GOODS_ID, WMS_INPUT_ORDER_ID, 
      SKU_NO, GOODS_ID, INPUT_NUM, 
      ARRIVAL_NUM, ARRIVAL_STATUS, ADD_TIME, 
      MODE_TIME, CREATOR, UPDATER, 
      IS_DELTET)
    values (#{wmsInputOrderGoodsId,jdbcType=INTEGER}, #{wmsInputOrderId,jdbcType=INTEGER},
      #{skuNo,jdbcType=VARCHAR}, #{goodsId,jdbcType=INTEGER}, #{inputNum,jdbcType=INTEGER}, 
      #{arrivalNum,jdbcType=INTEGER}, #{arrivalStatus,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modeTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{isDeltet,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.wms.model.po.WmsInputOrderGoods" >
    <!--          -->
    insert into T_WMS_INPUT_ORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="wmsInputOrderGoodsId != null" >
        WMS_INPUT_ORDER_GOODS_ID,
      </if>
      <if test="wmsInputOrderId != null" >
        WMS_INPUT_ORDER_ID,
      </if>
      <if test="skuNo != null" >
        SKU_NO,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="inputNum != null" >
        INPUT_NUM,
      </if>
      <if test="arrivalNum != null" >
        ARRIVAL_NUM,
      </if>
      <if test="arrivalStatus != null" >
        ARRIVAL_STATUS,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modeTime != null" >
        MODE_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="isDeltet != null" >
        IS_DELTET,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="wmsInputOrderGoodsId != null" >
        #{wmsInputOrderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="wmsInputOrderId != null" >
        #{wmsInputOrderId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null" >
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="inputNum != null" >
        #{inputNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null" >
        #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null" >
        #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDeltet != null" >
        #{isDeltet,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsInputOrderGoods" >
    <!--          -->
    update T_WMS_INPUT_ORDER_GOODS
    <set >
      <if test="wmsInputOrderId != null" >
        WMS_INPUT_ORDER_ID = #{wmsInputOrderId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null" >
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null" >
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="inputNum != null" >
        INPUT_NUM = #{inputNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null" >
        ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null" >
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDeltet != null" >
        IS_DELTET = #{isDeltet,jdbcType=INTEGER},
      </if>
    </set>
    where WMS_INPUT_ORDER_GOODS_ID = #{wmsInputOrderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.model.po.WmsInputOrderGoods" >
    <!--          -->
    update T_WMS_INPUT_ORDER_GOODS
    set WMS_INPUT_ORDER_ID = #{wmsInputOrderId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      INPUT_NUM = #{inputNum,jdbcType=INTEGER},
      ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELTET = #{isDeltet,jdbcType=INTEGER}
    where WMS_INPUT_ORDER_GOODS_ID = #{wmsInputOrderGoodsId,jdbcType=INTEGER}
  </update>

  <select id="getWmsInfoOrderGoodsByWmsInputOrderId" resultType="com.wms.model.po.WmsInputOrderGoods">
    SELECT
    <include refid="Base_Column_List" />
    FROM  T_WMS_INPUT_ORDER_GOODS
    WHERE WMS_INPUT_ORDER_ID = #{wmsInputOrderId,jdbcType=INTEGER}
    AND IS_DELTET=0
  </select>
  <select id="getWmsInfoOrderGoodsDtoByWmsInputOrderId" resultType="com.wms.model.dto.WmsInputOrderGoodsDto">
    SELECT
        A.*,
	    B.SHOW_NAME,B.MODEL,BR.BRAND_NAME
    FROM  T_WMS_INPUT_ORDER_GOODS A
    LEFT  JOIN V_CORE_SKU B ON A.GOODS_ID=B.SKU_ID
	LEFT JOIN V_CORE_SPU C ON B.SPU_ID=C.SPU_ID
	LEFT JOIN T_BRAND BR ON C.BRAND_ID=BR.BRAND_ID
    WHERE WMS_INPUT_ORDER_ID = #{wmsInputOrderId,jdbcType=INTEGER}
    AND IS_DELTET=0
  </select>

  <select id="getOrderGoods" resultMap="BaseResultMap">
    SELECT
    A.WMS_INPUT_ORDER_GOODS_ID, A.WMS_INPUT_ORDER_ID, A.SKU_NO, A.GOODS_ID, A.INPUT_NUM, A.ARRIVAL_NUM,
    A.ARRIVAL_STATUS, A.ADD_TIME, A.MODE_TIME, A.CREATOR, A.UPDATER, IS_DELTET
    FROM T_WMS_INPUT_ORDER_GOODS A
    LEFT JOIN T_WMS_INPUT_ORDER B ON A.WMS_INPUT_ORDER_ID = B.WMS_INPUT_ORDER_ID
    WHERE B.ORDER_NO = #{orderNo,jdbcType=VARCHAR} AND A.GOODS_ID = #{skuId,jdbcType=INTEGER}
  </select>

</mapper>