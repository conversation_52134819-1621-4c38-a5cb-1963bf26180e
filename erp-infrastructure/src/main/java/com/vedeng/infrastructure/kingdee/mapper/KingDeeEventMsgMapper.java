package com.vedeng.infrastructure.kingdee.mapper;

import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.domain.entity.KingDeeEventMsgEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KingDeeEventMsgMapper {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeEventMsgEntity record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeEventMsgEntity record);

    /**
     * 重试次数加1
     * @param kingDeeEventMsgId
     * @return
     */
    int addRetryNum(Integer kingDeeEventMsgId);


    int updateNotHandleByIds(List<KingDeeEventMsgDto> list);

    /**
     * 根据状态获取消息
     * @param messageStatus 消息消费状态
     * @param retryNum 边界值
     * @return
     */
    List<KingDeeEventMsgEntity> findByMessageStatus(@Param("messageStatus")Integer messageStatus,
                                                    @Param("retryNum")Integer retryNum,
                                                    @Param("msgId") Integer msgId,
                                                    @Param("eventTypeList") List<String> eventTypeList);

    /**
     * 获取未发送消息的失败消息
     * @param messageStatus 消息消费状态
     * @param retryNum 重试边界值
     * @return
     */
    List<KingDeeEventMsgEntity> findNeedSendMsg(@Param("messageStatus")Integer messageStatus, @Param("retryNum")Integer retryNum,@Param("openSend")boolean openSend);


    List<KingDeeEventMsgEntity> findAllByBusinessIdAndEventType(@Param("businessId")String businessId, @Param("eventType")String eventType);

    List<KingDeeEventMsgEntity> findLtByBusinessIdAndEventType(@Param("businessId")String businessId, @Param("eventType")String eventType,@Param("msgId")Integer msgId);


    void deleteEventMsg(@Param("kingDeeEventMsgId")Integer kingDeeEventMsgId, @Param("userId")Integer userId);

    KingDeeEventMsgEntity selectByPrimaryKey(@Param("kingDeeEventMsgId")Integer kingDeeEventMsgId);

    /**
     * 将执行成功的msg移入备份表
     *
     * @param record 原数据
     * @return insert count
     */
    int insertIntoBackUpTable(KingDeeEventMsgEntity record);

    /**
     * 删除金蝶消息表
     *
     * @param kingDeeEventMsgId 主键id
     * @return delete count
     */
    int deleteByPrimaryKey(Integer kingDeeEventMsgId);

}