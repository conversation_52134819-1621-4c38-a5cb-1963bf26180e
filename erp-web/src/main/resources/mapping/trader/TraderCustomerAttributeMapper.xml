<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderCustomerAttributeMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderCustomerAttribute" >
    <id column="TRADER_CUSTOMER_ATTRIBUTE_ID" property="traderCustomerAttributeId" jdbcType="INTEGER" />
    <result column="TRADER_CUSTOMER_ID" property="traderCustomerId" jdbcType="INTEGER" />
    <result column="ATTRIBUTE_CATEGORY_ID" property="attributeCategoryId" jdbcType="INTEGER" />
    <result column="ATTRIBUTE_ID" property="attributeId" jdbcType="INTEGER" />
    <result column="ATTRIBUTE_OTHER" property="attributeOther" jdbcType="VARCHAR" />
    <result column="SUB_CATEGORY_IDS" property="subCategoryIds" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    TRADER_CUSTOMER_ATTRIBUTE_ID, TRADER_CUSTOMER_ID, ATTRIBUTE_CATEGORY_ID, ATTRIBUTE_ID, 
    ATTRIBUTE_OTHER, SUB_CATEGORY_IDS
  </sql>
    <select id="getTraderIdListByParentId" resultType="java.lang.Integer" parameterType="com.vedeng.trader.model.TraderCustomerAttribute">
  	select
  		distinct b.TRADER_ID
  	from
  		T_TRADER_CUSTOMER_ATTRIBUTE a
  	left join
  		T_TRADER_CUSTOMER b on a.TRADER_CUSTOMER_ID = b.TRADER_CUSTOMER_ID
  	Where
  		a.ATTRIBUTE_CATEGORY_ID = #{attributeCategoryId,jdbcType=INTEGER}
  		and a.ATTRIBUTE_ID = #{attributeId,jdbcType=INTEGER}
  </select>


</mapper>