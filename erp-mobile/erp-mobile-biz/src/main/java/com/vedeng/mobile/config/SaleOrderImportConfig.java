package com.vedeng.mobile.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 掌上小贝按需映入erp销售模块中的service
 * @date 2024/2/5 9:18
 */
@Configuration
@Import({
        //com.vedeng.erp.saleorder.service.impl.api.SaleOrderApiService.class,
        //com.vedeng.erp.market.service.impl.api.MarketServiceApiImpl.class,
        //com.vedeng.erp.aftersale.service.impl.AftersaleInfoServiceImpl.class
})
public class SaleOrderImportConfig {
}
