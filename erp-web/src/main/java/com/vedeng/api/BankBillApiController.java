package com.vedeng.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.VoucherDto;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.sql.Struct;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/12 11:16
 **/
@Controller
@Slf4j
@RequestMapping("/api/bankBill")
public class BankBillApiController {

    @Autowired
    private BankBillApiService bankBillApiService;

    @Autowired
    private OssUtilsService ossUtilsService;

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/updateBankBillReceipt")
    @ResponseBody
    public R<?> updateBankBillReceipt(@Param("bankTag") Integer bankTag, @RequestParam("file") MultipartFile file ) {


        String originalFilename = file.getOriginalFilename();
        String suffix = FileUtil.extName(originalFilename);
        String tranFlow = getTranFlow(originalFilename);
        List<BankBillDto> bankBillList = bankBillApiService.getByTranFlowAndBankTag(tranFlow, bankTag);
        if (CollUtil.isEmpty(bankBillList)){
            log.error("未获取到对应的记录,tranFlow:{},bankTag:{}",suffix,bankTag);
            return R.error("未获取到对应的记录");
        }
        InputStream inputStream = null;
        try{
            log.info("bankBillList:{}", JSON.toJSONString(bankBillList));
            inputStream = file.getInputStream();
            String fileUrl = ossUtilsService.upload2OssForInputStream(suffix,originalFilename,inputStream);
            log.info("fileUrl:{}",fileUrl);
            if (StrUtil.isEmpty(fileUrl)) {
                throw new ServiceException("上传文件地址为空");
            }
            BankBillDto update = new BankBillDto();
            update.setBankBillId(bankBillList.get(0).getBankBillId());
            update.setReceiptUrl(fileUrl);
            bankBillApiService.update(update);
            return R.success();
        }catch (Exception e){
            log.error("上传凭证附件失败",e);
            return R.error("上传凭证附件失败"+e.getMessage());
        }finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("上传凭证附件失败，流关闭失败");
            }
        }
    }

    private String getTranFlow(String originalFilename) {
        List<String> split = StrUtil.split(originalFilename, "_");
        String s = split.get(0);
        return s.replaceAll("-", "");
    }


}
