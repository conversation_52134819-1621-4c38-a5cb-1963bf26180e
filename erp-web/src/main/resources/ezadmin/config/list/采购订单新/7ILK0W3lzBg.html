<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>(订单流)采购订单列表</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="7ILK0W3lzBg" datasource="erp-datasourcetarget" fixednumber="3" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">采购单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="BUYORDER_NO" placeholder="请输入" style="" alias="bo" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">销售单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_NOS" placeholder="请输入" style=""  jdbctype="BODY" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">是否全部添加物流信息</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="IS_ALL_LOGISTICS_STATUS" placeholder="默认全部" style="text-overflow: clip ;" alias="td" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">录票申请</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="RECORD_INVOICE_APPLY_STATUS" placeholder="" style="" alias="td" jdbctype="" data="[{&quot;V&quot;:&quot;默认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未录票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分录票&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已录票审核&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">账期未还</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="LACK_PERIOD_STATUS" placeholder="默认全部" style="" alias="td" jdbctype="" data="[{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">产品归属</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="BELONG" placeholder="请输入" style="" alias="CONCAT_WS('_',S.ASSIGNMENT_MANAGER_ID,S.ASSIGNMENT_ASSISTANT_ID)" jdbctype="" data="SELECT DISTINCT tu.USER_ID K, lower(USERNAME) V
FROM T_USER tu
         INNER JOIN T_R_USER_ROLE tru ON tu.USER_ID = tru.USER_ID
         INNER JOIN T_ROLE tr ON tr.ROLE_ID = tru.ROLE_ID
WHERE (tr.ROLE_NAME = '产品专员'
   or tr.ROLE_NAME = '产品主管'
   or tr.ROLE_NAME = '产品总监')       order by USERNAME" datatype="KVSQLCACHE" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">创建人</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="CREATOR" placeholder="" style="" alias="bo" jdbctype="" data="SELECT DISTINCT u.USER_ID K, lower(u.USERNAME) V
FROM T_USER u
         LEFT JOIN T_R_USER_POSIT up ON u.USER_ID = up.USER_ID
         LEFT JOIN T_POSITION p on up.POSITION_ID = p.POSITION_ID
         LEFT JOIN T_ORGANIZATION o ON p.ORG_ID = o.ORG_ID
WHERE (o.TYPE = 311 or o.TYPE=310) and u.IS_DISABLED=0 and u.COMPANY_ID=1  order by u.USERNAME;" datatype="KVSQLCACHE" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">创建部门</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="search-orggyl" name="ORG_ID" placeholder="" style="" alias="" jdbctype=""   oper="IN"></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">规格/型号</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="19" name="MODEL_SPEC" placeholder="请输入" style="" alias="CONCAT_WS('_',BG.MODEL,BG.SPEC)" jdbctype="" data="" datatype="" oper="LIKE"></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">品牌</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="BRAND_NAME" placeholder="请输入" style="" alias="BG" jdbctype="" data="" datatype="" oper="LIKE"></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">产品名称</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="19" name="GOODS_NAME" placeholder="请输入" style="" alias="BG" jdbctype="" data="" datatype="" oper="LIKE"></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">订货号</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SKU" placeholder="请输入" style="" alias="BG" jdbctype="" data="" datatype="" oper="LIKE"></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="daterange" name="ADDTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME( IF ( bo.ADD_TIME = 0, NULL, bo.ADD_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">合同回传</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="IS_CONTRACT_RETURN_STATUS" placeholder="默认全部" style="" alias="td" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">付款申请到财务</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="IS_FINANCE_ALREADY_STATUS" placeholder="默认全部" style="" alias="td" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">供应商名称</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="请输入" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE"></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">是否直发</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="DELIVERY_DIRECT" placeholder="默认全部" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;普发&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;直发&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">锁定状态</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="LOCKED_STATUS" placeholder="默认全部" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;未锁定&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已锁定&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">生效状态</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="VALID_STATUS" placeholder="默认全部" style="" alias="bo" jdbctype="" data="[{&quot;V&quot;:&quot;未生效&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已生效&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">售后状态</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="SERVICE_STATUS" placeholder="默认全部" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;无售后&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;售后中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;售后完成&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;售后关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">收票状态</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="INVOICE_STATUS" placeholder="默认全部" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;未收票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收票&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">收货状态</label>
   <div class="layui-input-inline"><xm class=" layui-input list-search-item " type="select" name="ARRIVAL_STATUS" placeholder="默认全部" style="" alias="bo" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></xm>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">发货状态</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="DELIVERY_STATUS" placeholder="默认全部" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">付款状态</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="PAYMENT_STATUS" placeholder="默认全部" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;未付款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分付款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部付款&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">催货跟进状态</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="EXPEDITING_FOLLOW_STATUS" placeholder="默认全部" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;未跟进&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分跟进&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已跟进&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">催货预警</label>
   <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="EXPEDITING_STATUS" placeholder="默认正常" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;临期&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;逾期&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">审核状态</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="VERIFY_STATUS" placeholder="默认全部" style="" alias="bo" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">订单状态</label>
   <div class="layui-input-inline"><xm class=" layui-input list-search-item " type="select" name="STATUS" placeholder="默认全部" style="" alias="bo" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="IN"></xm>
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">订单进度</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="SUB_STATUS" placeholder="默认全部" style="" alias="bo" jdbctype="" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;待付款&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;待发货&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;待收货&quot;,&quot;K&quot;:&quot;5&quot;},{&quot;V&quot;:&quot;待收票&quot;,&quot;K&quot;:&quot;6&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;7&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;8&quot;}]" datatype="JSON" oper="LIKE"></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">是否可收货</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="21" name="EXPRESS_ENABLE_RECEIVE" placeholder="默认全部"
            style="" alias="bo" jdbctype="NUMBER"
            data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]"
            datatype="JSON" oper=""></object>
   </div>
  </div>

    <div class=" layui-inline ">
     <label class="layui-form-label"></label>
     <div class="layui-input-inline">
      <object class="layui-input list-search-item" jdbctype="VARCHAR" type="union" placeholder="请输入收货联系人/手机号/地址"  oper="like" name="TAKE_TRADER_CONTACT_NAME,TAKE_TRADER_CONTACT_MOBILE,TAKE_TRADER_ADDRESS" empty_show="-" validate_rules="{&quot;maxlength&quot;:250}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过{0}&quot;}" style="" alias="bo">
      </object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">收货联系人</label>
     <div class="layui-input-inline">
      <input class="layui-input list-search-item" jdbctype="VARCHAR" type="hidden" placeholder="请输入收货联系人/手机号/地址" oper="like" name="TAKE_TRADER_CONTACT_NAME" empty_show="" style="" alias="bo">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">手机号</label>
     <div class="layui-input-inline">
      <input class="layui-input list-search-item" jdbctype="VARCHAR" type="hidden" placeholder="请输入收货联系人/手机号/地址" oper="like" name="TAKE_TRADER_CONTACT_MOBILE" empty_show="" style="" alias="bo">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">地址</label>
     <div class="layui-input-inline">
      <input class="layui-input list-search-item" jdbctype="VARCHAR" type="hidden" placeholder="请输入收货联系人/手机号/地址" oper="like" name="TAKE_TRADER_ADDRESS" empty_show="" style="" alias="bo">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">是否特麦帮</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " name="IS_SPECIAL" placeholder="" style="" alias="if(TSS.SPECIAL_SALES_ID is null, '否', '是')" jdbctype="" data="[{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;是&quot;},{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;否&quot;}]" datatype="JSON" oper=""></select>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">合同回传审核状态</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " name="contractStatus" placeholder="默认全部" style="" alias="ifnull(v3.STATUS,3)" jdbctype="" data="[{&quot;V&quot;:&quot;待提交&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></select>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
       采购单号
      <th item_name="EXPEDITING_STATUS" name="EXPEDITING_STATUS" body="td-html"  url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;<span style='color: orange'>临期</span>&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;<span style='color: red'>逾期</span>&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" >催货预警</th>
      <th item_name="BUYORDER_NO" name="BUYORDER_NO" body="td-spanlink"  url="/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="${BUYORDER_NO}" datatype="" data="" style="word-break: break-all;" >订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text"  url="" opentype="MODEL" windowname="" datatype="" data="" style="" >供应商</th>
      <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text"  url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align: right" jdbctype="NUMBER" >总额</th>
      <th item_name="REAL_PAY_AMOUNT" name="REAL_PAY_AMOUNT" body="td-text"  url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align: right" jdbctype="NUMBER">已付金额</th>
      <th item_name="STATUS_STR" name="STATUS_STR" body="td-select"  url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" >订单状态</th>
      <th item_name="PAYMENT_STATUS" name="PAYMENT_STATUS" body="td-select"  url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未付款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分付款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部付款&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" >付款状态</th>
      <th item_name="DELIVERY_STATUS" name="DELIVERY_STATUS" body="td-select"  url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" >发货状态</th>
      <th item_name="ARRIVAL_STATUS" name="ARRIVAL_STATUS" body="td-select"  url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" >收货状态</th>
      <th item_name="INVOICE_STATUS" name="INVOICE_STATUS" body="td-select"  url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收票&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" >收票状态</th>
      <th item_name="DELIVERY_DIRECT" name="DELIVERY_DIRECT" body="td-select"  url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" >是否直发</th>

      <th item_name="ORG_ID" name="ORG_ID" body="td-select"  url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select
		ORG_ID K,ORG_NAME V
		from T_ORGANIZATION" style="" >创建人部门</th>

      <th item_name="CREATOR" name="CREATOR" body="td-select"  url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select USER_ID K,USERNAME V FROM T_USER" style="" >创建人</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text"  url="" opentype="MODEL" windowname="" datatype="" data="" style="" >创建时间</th>
      <th item_name="VALID_TIME" name="VALID_TIME" body="td-text"  url="" opentype="MODEL" windowname="" datatype="" data="" style="" >生效时间</th>
      <th item_name="LACK_ACCOUNT_PERIOD_AMOUNT" name="LACK_ACCOUNT_PERIOD_AMOUNT" body="td-text"  url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align: right"  jdbctype="NUMBER">未还账期款</th>
      <th item_name="IS_SPECIAL" name="IS_SPECIAL" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">是否特麦帮</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by bo.ADD_TIME DESC" groupby="group by bo.BUYORDER_ID">StringBuilder sql=new StringBuilder("

SELECT
CASE
	WHEN
		bo.STATUS = 0 THEN
			'orangecircle'
			WHEN bo.STATUS = 1 THEN
			'greencircle'
			WHEN bo.STATUS = 2 THEN
			'bluecircle'
			WHEN bo.STATUS = 3 THEN
			'greycircle'
		END COLOR_CIRCLE,
        bo.BUYORDER_ID,
	bo.BUYORDER_NO,
	bo.STATUS * 1 AS STATUS_STR,
	PAYMENT_STATUS * 1 PAYMENT_STATUS,
	DELIVERY_STATUS * 1 DELIVERY_STATUS,
	bo.ARRIVAL_STATUS * 1 ARRIVAL_STATUS,
	INVOICE_STATUS * 1 INVOICE_STATUS,
	EXPEDITING_STATUS * 1 EXPEDITING_STATUS,
	EXPEDITING_FOLLOW_STATUS * 1 EXPEDITING_FOLLOW_STATUS,
	SERVICE_STATUS * 1 SERVICE_STATUS,
	LOCKED_STATUS * 1 LOCKED_STATUS,
	bo.TRADER_NAME,
	DELIVERY_DIRECT * 1 DELIVERY_DIRECT,
	TOTAL_AMOUNT,
REAL_PAY_AMOUNT,
COALESCE(td.LACK_ACCOUNT_PERIOD_AMOUNT,'0.00') LACK_ACCOUNT_PERIOD_AMOUNT,
	bo.CREATOR,
	ORG_ID,
	FROM_UNIXTIME( IF ( bo.ADD_TIME = 0, NULL, bo.ADD_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' ) AS ADD_TIME,
	FROM_UNIXTIME( IF ( bo.VALID_TIME = 0, NULL, bo.VALID_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' ) AS VALID_TIME,

	 CONCAT(COALESCE ( v.STATUS, 3 ),',',v.VERIFY_USERNAME ) VERIFY_USERNAME,
	bo.SUB_STATUS*1 SUB_STATUS,
	td.IS_CONTRACT_RETURN_STATUS*1 IS_CONTRACT_RETURN_STATUS,
	td.LACK_PERIOD_STATUS*1 LACK_PERIOD_STATUS,
	td.IS_FINANCE_ALREADY_STATUS*1 IS_FINANCE_ALREADY_STATUS,
	td.IS_ALL_LOGISTICS_STATUS*1 IS_ALL_LOGISTICS_STATUS,
    if(TSS.SPECIAL_SALES_ID is null, '否', '是') as IS_SPECIAL,
	bo.VERIFY_STATUS*1 VERIFY_STATUS
FROM
	T_BUYORDER bo
        LEFT JOIN  T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY = bo.BUYORDER_ID AND v.RELATE_TABLE = 'T_BUYORDER'
	LEFT JOIN T_BUYORDER_DATA td ON td.BUYORDER_ID = bo.BUYORDER_ID
       LEFT JOIN T_SPECIAL_SALES TSS ON TSS.RELATE_ID = bo.BUYORDER_ID AND TSS.RELATE_TYPE = 4 AND TSS.IS_DELETE = 0
       LEFT JOIN (select RELATE_TABLE_KEY,
                max(VERIFIES_INFO_ID) as v2Id
      from T_VERIFIES_INFO
      where RELATE_TABLE = 'T_BUYORDER_CONTRACT'
      group by RELATE_TABLE_KEY
      )v2 on bo.BUYORDER_ID = v2.RELATE_TABLE_KEY
      left join T_VERIFIES_INFO v3 on v3.VERIFIES_INFO_ID = v2.v2Id
");

if ( isNotBlank("SKU") || isNotBlank("GOODS_NAME") || isNotBlank("BRAND_NAME") ||  isNotBlank("MODEL_SPEC") ||  isNotBlank("BELONG")) {
	sql.append ( " LEFT JOIN T_BUYORDER_GOODS BG ON bo.BUYORDER_ID = BG.BUYORDER_ID" );
}

if ( isNotBlank("BELONG")) {
	sql.append ( " LEFT JOIN V_CORE_SKU CS ON BG.SKU = CS.SKU_NO
	LEFT JOIN V_CORE_SPU S ON CS.SPU_ID = S.SPU_ID  " );
}

sql.append("
WHERE
	1 = 1 and COMPANY_ID =1
");

if (isNotBlank("LACK_PERIOD_STATUS")) {
sql.append ( " AND  bo.HAVE_ACCOUNT_PERIOD = 1 AND   bo.PAYMENT_STATUS != 0 ");
}
saleorderNos = $("SALEORDER_NOS");
if ( isNotBlank("SALEORDER_NOS","request")) {
     sql.append (" AND bo.BUYORDER_ID IN (SELECT DISTINCT bg.BUYORDER_ID from T_BUYORDER_GOODS bg
          LEFT JOIN T_R_BUYORDER_J_SALEORDER bs on bs.BUYORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
          LEFT JOIN T_SALEORDER_GOODS saleordergoods ON bs.SALEORDER_GOODS_ID = saleordergoods.SALEORDER_GOODS_ID
          LEFT JOIN T_SALEORDER saleorder on saleorder.SALEORDER_ID=saleordergoods.SALEORDER_ID
          where saleorder.SALEORDER_NO LIKE concat('%', '").append(saleorderNos).append("', '%')  ) ");
}


list=search(sql);
return list;</pre> count: <pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot">
   <script>

$(function(){

 $("td[item_name=PAYMENT_STATUS],td[item_name=INVOICE_STATUS],td[item_name=DELIVERY_STATUS],td[item_name=ARRIVAL_STATUS]").each(function(e){
   if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
})
//  $("td").each(function(e){
//    if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
// })
//
//  $("td").each(function(e){
//    if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
// })
//  $("td").each(function(e){
//    if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
// })
 $("[name=itemSearchKey]").find("option").eq(0).remove();
 layui.form.render();
})

 $("input[name='row_data_hidden_VERIFY_USERNAME']").each(function(e){
        var curUser = $("#EZ_SESSION_USER_NAME_KEY").val();
        var verifyUser = $(this).val();
      //  debugger
        var varifyUserChar = verifyUser.split(",");
        var flag = false;
        if (varifyUserChar[0] == '0'){
            for (var i = 1; i < varifyUserChar.length; i++) {
                if (varifyUserChar[i] == curUser) {
                    flag = true;
                    break;
                }
            }
        }
        if (flag){
            var shen = "<font color='red'>[审]</font>";
            $(this).parent().next().next().children("a").after(shen);
        }
    })


</script>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>