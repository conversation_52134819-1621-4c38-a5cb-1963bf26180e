package com.newtask.data.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description com.newtask.data.dto
 * @Date 2021/10/28 14:31
 */
@Getter
@Setter
public class SaleoderDataDto {

    /**
     * 订单ID
     */
    private Integer saleorderId;

    /**
     * SKU_BRAND_NAME_MODEL
     */
    private String skuBrandNameModel;

    /**
     * 售后状态
     */
    private Long afterSalesStatus;

    /**
     * 自动审核
     */
    private Long autoCheck;

    /**
     * 沟通次数
     */
    private Long communicateNum;

    /**
     * 合同状态
     */
    private Long contractStatus;

    /**
     * 合同审核状态
     */
    private Long contractVerifyStatus;

    private Long firstExpressTime;

    private Long firstReceiveTime;

    /**
     * 首次付款时间
     */
    private Long firstPayTime;

    private Long invoiceApplyFlay;

    private Long lastInvoiceTime;

    private BigDecimal leftAmountPeriod;

    private Long subStatus;

    private Long verifyStatus;

    private Integer currentOrgId;

    private Integer currentUserId;


}
