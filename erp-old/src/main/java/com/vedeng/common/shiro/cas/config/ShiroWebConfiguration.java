package com.vedeng.common.shiro.cas.config;

import com.vedeng.common.shiro.RedisSessionDAO;
import com.vedeng.common.shiro.cas.CasClientConstants;
import com.vedeng.common.shiro.cas.CustomPac4jRealm;
import com.vedeng.common.shiro.cas.CustomPermissionResolver;
import com.vedeng.common.shiro.cas.CustomUserFilter;
import com.vedeng.common.shiro.cas.properties.CasClientProperties;
import com.vedeng.common.shiro.cas.session.MessagingEventSessionManager;
import com.vedeng.common.shiro.constant.SecurityConstants;
import io.buji.pac4j.filter.CallbackFilter;
import io.buji.pac4j.filter.LogoutFilter;
import io.buji.pac4j.filter.SecurityFilter;
import io.buji.pac4j.subject.Pac4jSubjectFactory;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.web.servlet.Cookie;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.WebSessionManager;
import org.pac4j.core.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

import javax.servlet.Filter;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR> [<EMAIL>]
 */
public class ShiroWebConfiguration {

    @Autowired
    private CasClientProperties casClientProperties;

    @Autowired
    private Config config;

    @Bean
    public Realm pac4jRealm() {
        CustomPac4jRealm customPac4jRealm = new CustomPac4jRealm();
        customPac4jRealm.setAuthorizationCachingEnabled(false);
        customPac4jRealm.setPermissionResolver(new CustomPermissionResolver());
        return customPac4jRealm;
    }

    @Bean
    public Pac4jSubjectFactory pac4jSubjectFactory() {
        return new Pac4jSubjectFactory();
    }

    protected Cookie createSessionCookie() {
        Cookie sessionCookie = new SimpleCookie(casClientProperties.getSessionIdName());
        sessionCookie.setHttpOnly(true);
        sessionCookie.setMaxAge(casClientProperties.getSessionIdMaxAge());
        sessionCookie.setPath(casClientProperties.getSessionIdPath());
        sessionCookie.setDomain("");
        return sessionCookie;
    }


    @Bean(name = "messagingEventSessionManager")
    public WebSessionManager messagingEventSessionManager(RedisSessionDAO redisSessionDAO) {
        MessagingEventSessionManager webSessionManager = new MessagingEventSessionManager();
        webSessionManager.setSessionIdCookieEnabled(true);
        webSessionManager.setSessionIdCookie(createSessionCookie());
        webSessionManager.setSessionIdUrlRewritingEnabled(true);
        webSessionManager.setSessionValidationSchedulerEnabled(true);
        webSessionManager.setSessionValidationInterval(TimeUnit.MINUTES.toMillis(5));
        webSessionManager.setGlobalSessionTimeout(casClientProperties.getSessionIdMaxAge() * 1000);
        webSessionManager.setSessionDAO(redisSessionDAO);
        return webSessionManager;
    }


    @Bean
    public Filter casSecurityFilter() {
        SecurityFilter securityFilter = new SecurityFilter();
        securityFilter.setClients(CasClientConstants.DEFAULT_CAS_CLIENT_NAME);
        securityFilter.setConfig(config);
        return securityFilter;
    }


    @Bean
    public Filter casLogoutFilter() {
        LogoutFilter logoutFilter = new LogoutFilter();
        logoutFilter.setConfig(config);
        logoutFilter.setCentralLogout(true);
        logoutFilter.setDefaultUrl(casClientProperties.getServiceUrl());
        return logoutFilter;
    }

    @Bean
    public Filter casCallbackFilter() {
        CallbackFilter callbackFilter = new CallbackFilter();
        callbackFilter.setConfig(config);
        callbackFilter.setDefaultUrl(SecurityConstants.INDEX_URL);
        return callbackFilter;
    }

    @Bean
    public Filter customUserFilter() {
        return new CustomUserFilter();
    }

}
