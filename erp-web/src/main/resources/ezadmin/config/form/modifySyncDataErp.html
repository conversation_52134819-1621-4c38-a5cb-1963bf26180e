<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>同步数据ERP编辑</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="modifySyncDataErp" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append">
      <style>
          .layui-quote-nm{display: none;}
          small {
              display: none;
          }
      </style>
  </div>
  <div class="layui-container">
   <form id="inputForm" method="post" class="layui-form">
       <input type="hidden" name="ID" value="${ID}">

       <div class="layui-form-item ">
           <label class="layui-form-label">业务单号</label>
           <div class="layui-input-inline form-group">
               <input lay_verify="required" datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="BUSINESS_NO" item_desc="业务单号" maxlength="50">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">业务类型</label>
           <div class="layui-input-inline form-group">
               <select class="layui-input list-search-item" name="BUSINESS_TYPE" alias="BUSINESS_TYPE"
                       data='[
                                {"K":"BUYORDER_TO_SALEORDER","V":"采购单转销售单"},
                                {"K":"BUYORDERCONTACT_TO_SALECONTACT","V":"采购订单合同同步销售订单合同"},
                                {"K":"SALEORDER_LOGISTICS_TO_BUYORDER","V":"销售反向同步物流信息"},
                                {"K":"SALEORDER_AUTO_COMFIRM_ORDER","V":"自动单生成确认单"}
                            ]'
                       datatype="json" oper="IN" placeholder="全部">
               </select>
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">目标ERP简称</label>
           <div class="layui-input-inline form-group">
               <input lay_verify="required" datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="TARGET_ERP" item_desc="目标ERP简称" maxlength="20">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">数据JSON请求内容</label>
           <div class="layui-input-inline form-group" style="width: 400px;">
               <textarea lay_verify="required" datatype="text" jdbctype="TEXT"
                         class="layui-input form-item"
                         name="REQUEST_CONTENT" item_desc="数据JSON请求内容" maxlength="2000" style="height:120px;"></textarea>
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">处理状态</label>
           <div class="layui-input-inline form-group">
               <select class="layui-input list-search-item" name="PROCESS_STATUS" alias="PROCESS_STATUS"
                       data='[
                                {"K":"0","V":"待处理"},
                                {"K":"1","V":"处理中"},
                                {"K":"2","V":"处理成功"},
                                {"K":"3","V":"处理失败"}
                            ]'
                       datatype="json" oper="IN" placeholder="全部">
               </select>
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">备注</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="REMARK" item_desc="备注" maxlength="200">
           </div>
       </div>
   </form>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
  <div id="APPEND_FOOT" class="append">
   <script>
    </script>
  </div>
  <form id="configForm" class="layui-form">
 <div class="layui-card" >
  <div class="layui-card-header">
    表单数据配置
  </div>
  <div class="layui-card-body">
  </div>
 </div>
 <div class="layui-form-item "><label class="layui-form-label">初始表达式</label>
  <div class="layui-input-block form-group">
   <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("
   SELECT
             ID,
             BUSINESS_NO,
             BUSINESS_TYPE,
             TARGET_ERP,
             REQUEST_CONTENT,
             PROCESS_STATUS,
             REMARK
         FROM
             T_SYNC_DATA_ERP
WHERE
 ID = ${ID}");
return list.get(0);</pre>
  </div>
 </div>
 <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label>
  <div class="layui-input-block form-group">
   <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">if(null==ID||"0".equals(ID)){
     insert("INSERT INTO T_SYNC_DATA_ERP
       (BUSINESS_NO, BUSINESS_TYPE, TARGET_ERP, REQUEST_CONTENT, PROCESS_STATUS, REMARK, CREATE_TIME, UPDATE_TIME, IS_DELETED)
     VALUES
       (#{BUSINESS_NO}, #{BUSINESS_TYPE}, #{TARGET_ERP}, #{REQUEST_CONTENT}, #{PROCESS_STATUS}, #{REMARK}, now(), now(), 0)");
    return 0;
}else{
 update("UPDATE T_SYNC_DATA_ERP
       SET
       BUSINESS_NO=#{BUSINESS_NO},
       BUSINESS_TYPE=#{BUSINESS_TYPE},
       TARGET_ERP=#{TARGET_ERP},
       REQUEST_CONTENT=#{REQUEST_CONTENT},
       PROCESS_STATUS=#{PROCESS_STATUS},
       REMARK=#{REMARK},
       UPDATE_TIME=now()
       WHERE ID = #{ID}");
return ID;
}</pre>
  </div>
 </div>
 <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label>
  <div class="layui-input-block form-group">
   <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type="">
       update("UPDATE T_SYNC_DATA_ERP SET IS_DELETED = 1, UPDATE_TIME = NOW() WHERE ID = #{ID}");
       return ID;
   </pre>
  </div>
 </div>
 <div class="layui-form-item">
    <div class="layui-input-block">
        <button type="button" class="layui-btn layui-btn-danger" 
                url="/ezadmin/form/doDelete-modifySyncDataErp?ID=${ID}" 
                opentype="CONFIRM_AJAX" windowname="删除">删除
        </button>
    </div>
</div>
</form>
 </body>
</html> 