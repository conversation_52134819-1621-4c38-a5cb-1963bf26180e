package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.enums.CostActEnum;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchExpressCostDto;
import com.vedeng.erp.kingdee.batch.processor.ExpressCostPushAddProcessor;
import com.vedeng.erp.kingdee.batch.processor.ExpressCostPushUpdateProcessor;
import com.vedeng.erp.kingdee.batch.writer.ExpressCostPushAddWrite;
import com.vedeng.erp.kingdee.batch.writer.ExpressCostPushUpdateWrite;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Slf4j
@Configuration
@SuppressWarnings("all")
public class ExpressCostBatchJob extends BaseJob {

    @Autowired
    private ExpressCostPushAddProcessor expressCostPushAddProcessor;

    @Autowired
    private ExpressCostPushAddWrite expressCostPushAddWrite;

    @Autowired
    private ExpressCostPushUpdateProcessor expressCostPushUpdateProcessor;

    @Autowired
    private ExpressCostPushUpdateWrite expressCostPushUpdateWrite;

    public Job expressCostPushJob() {
        return jobBuilderFactory.get("expressCostPushJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(expressCostPushAddStep())
                .next(expressCostPushUpdateStep())
                .build();
    }

    public Job expressCostPushOnlyAddJob() {
        return jobBuilderFactory.get("expressCostPushOnlyAddJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(expressCostPushAddStep())
                .build();
    }

    /**
     * 批处理新增
     * @return
     */
    private Step expressCostPushAddStep() {
        return stepBuilderFactory.get("快递成本-ADD类型")
                .<BatchExpressCostDto, KingDeeExpressCostDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(expressCostRecordAddReader(null,null))
                .processor(expressCostPushAddProcessor)
                .writer(expressCostPushAddWrite)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 顺序处理更新和删除
     * @return
     */
    private Step expressCostPushUpdateStep() {
        return stepBuilderFactory.get("快递成本-UPDATE类型")
                .<BatchExpressCostDto, KingDeeExpressCostDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(expressCostRecordUpdateReader(null,null))
                .processor(expressCostPushUpdateProcessor)
                .writer(expressCostPushUpdateWrite)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchExpressCostDto> expressCostRecordAddReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getExpressCostDtoAddRecordCommonMybatisItemReader(beginTime, endTime, Collections.singletonList(CostActEnum.ACT_ADD.getCode()));
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchExpressCostDto> expressCostRecordUpdateReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getExpressCostDtoAddRecordCommonMybatisItemReader(beginTime, endTime, Arrays.asList(CostActEnum.ACT_UPDATE.getCode(),CostActEnum.ACT_DELETE.getCode()));
    }

    private CommonMybatisItemReader<BatchExpressCostDto> getExpressCostDtoAddRecordCommonMybatisItemReader(
            String beginTime, String endTime, List<Integer> costActFlags) {
        BatchExpressCostDto batchExpressCostDto = BatchExpressCostDto
                .builder()
                .costActFlags(costActFlags)
                .beginTime(beginTime == null ? DateUtil.beginOfMonth(new DateTime()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfMonth(new DateTime()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchExpressCostDto.class.getSimpleName(),"findExpressCostByAll", batchExpressCostDto);
    }
}
