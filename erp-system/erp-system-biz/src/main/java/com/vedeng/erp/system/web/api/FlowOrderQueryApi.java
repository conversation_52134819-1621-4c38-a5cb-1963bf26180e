package com.vedeng.erp.system.web.api;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.system.service.FlowOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@ExceptionController
@RestController
@RequestMapping("/api/flowOrder")
@Slf4j
public class FlowOrderQueryApi {


    @Autowired
    private FlowOrderService flowOrderService;

    /**
     * 删除
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/queryByBusinessNo")
    public R<String> queryByBusinessNo(@RequestParam("businessNo") String businessNo) {
        String result = flowOrderService.getFlowOrderByBusinessForStandard(businessNo);
        return R.success("查询成功",result);
    }
}
