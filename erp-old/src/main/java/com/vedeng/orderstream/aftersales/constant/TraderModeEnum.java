package com.vedeng.orderstream.aftersales.constant;

/**
 * 交易方式
 *
 * <AUTHOR>
 */
public enum TraderModeEnum {
    ALI_PAY(520, "支付宝"),
    BANK_PAY(521, "银行"),
    WE_CHAT_PAY(522, "微信");

    private Integer code;

    private String desc;

    TraderModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }}
