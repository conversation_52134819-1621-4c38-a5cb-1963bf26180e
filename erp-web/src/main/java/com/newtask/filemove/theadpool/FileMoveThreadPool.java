package com.newtask.filemove.theadpool;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class FileMoveThreadPool {

    public static ExecutorService getTraderCertificateThreadPool(){
        return new ThreadPoolExecutor(10, 20,
                60L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(1024 * 100),
                new ThreadFactoryBuilder()
                        .setNameFormat("fileMove-traderCertificate-%d")
                        .build(),
                new ThreadPoolExecutor.AbortPolicy());
    }
}
