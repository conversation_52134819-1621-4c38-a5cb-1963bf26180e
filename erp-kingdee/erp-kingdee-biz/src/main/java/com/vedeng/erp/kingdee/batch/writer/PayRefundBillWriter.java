package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundBillDto;
import com.vedeng.erp.kingdee.service.KingDeePayRefundApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 金蝶付款退款单推送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/24 17:04
 */
@Slf4j
@Service
public class PayRefundBillWriter extends BaseWriter<KingDeePayRefundBillDto> {

    @Autowired
    private KingDeePayRefundApiService kingDeePayRefundApiService;

    @Override
    public void doWrite(KingDeePayRefundBillDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        item.setKingDeeBizEnums(KingDeeBizEnums.savePayRefundBill);
        kingDeePayRefundApiService.register(item, true);
    }
}