package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 操作插入和更新接口入参
 */
@Data
public class QuoteGoodsRequestDto extends QuoteGoodsBaseDto{

    /**
     * 客户需求id
     */
    private Long quoteorderNeedsId;

    /**
     * 报价ID
     */
    private Integer quoteorderId;

    /**
     * 报价商品ID
     */
    private Integer quoteorderGoodsId;

    /**
     * 货期
     */
    private String deliveryCycle;

    /**
     * 原货期
     */
    private String oldDeliveryCycle;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 原价格
     */
    private BigDecimal oldPrice;

    /**
     * 报备状态
     */
    private Integer reportStatus;

    /**
     * 原报备状态
     */
    private Integer oldReportStatus;

    /**
     * 需求负责人（无sku时，编辑负责人传值）
     */
    private Integer needsUserId;
}
