package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;

import java.util.Date;

/**
 * Table: T_TAXCODE_CLASSIFICATION
 */
public class TaxcodeClassificationEntity extends BaseEntity {
    /**
     * Column: TAXCODE_CLASSIFICATION_ID
     * Type: INT UNSIGNED
     * Remark: 主键
     */
    private Integer taxcodeClassificationId;

    /**
     * Column: SUMMARY_CODE
     * Type: VARCHAR(64)
     * Remark: 汇总编码
     */
    private String summaryCode;

    /**
     * Column: GOODS_SERVICES_CLASSIFICATION_ABBREVIATION
     * Type: VARCHAR(64)
     * Remark: 汇总项（商品和服务分类简称)
     */
    private String goodsServicesClassificationAbbreviation;

    /**
     * Column: GOODS_SERVICES_NAME_ABBREVIATION
     * Type: VARCHAR(255)
     * Remark: 汇总项（货物和劳务名称)
     */
    private String goodsServicesNameAbbreviation;

    /**
     * Column: FINAL_CODE
     * Type: VARCHAR(255)
     * Remark: 末级编码
     */
    private String finalCode;

    /**
     * Column: GOODS_SERVICES_NAME
     * Type: VARCHAR(255)
     * Remark: 货物和劳务名称
     */
    private String goodsServicesName;

    /**
     * Column: CLASSIFICATION_ABBREVIATION
     * Type: VARCHAR(64)
     * Remark: 商品和服务分类简称
     */
    private String classificationAbbreviation;

    /**
     * Column: DESCRIPTION
     * Type: VARCHAR(255)
     * Remark: 说明
     */
    private String description;

    /**
     * Column: KEYWORD
     * Type: VARCHAR(255)
     * Remark: 关键字
     */
    private String keyword;

    /**
     * Column: VATRATE
     * Type: VARCHAR(64)
     * Remark: 增值税税率
     */
    private String vatrate;

    /**
     * Column: VAT_SPECIAL_MANAGEMENT
     * Type: VARCHAR(255)
     * Remark: 增值税特殊管理
     */
    private String vatSpecialManagement;

    /**
     * Column: VAT_POLICY_BASIS
     * Type: VARCHAR(255)
     * Remark: 增值税政策依据
     */
    private String vatPolicyBasis;

    /**
     * Column: VAT_SPECIAL_CONTENT_CODE
     * Type: VARCHAR(255)
     * Remark: 增值税特殊内容代码
     */
    private String vatSpecialContentCode;

    /**
     * Column: CONSUMPTION_TAX_MANAGEMENT
     * Type: VARCHAR(255)
     * Remark: 消费税管理
     */
    private String consumptionTaxManagement;

    /**
     * Column: CONSUMPTION_TAX_POLICY_BASIS
     * Type: VARCHAR(255)
     * Remark: 消费税政策依据
     */
    private String consumptionTaxPolicyBasis;

    /**
     * Column: CONSUMPTION_TAX_SPECIAL_CONTENT_CODE
     * Type: VARCHAR(64)
     * Remark: 消费税特殊内容代码
     */
    private String consumptionTaxSpecialContentCode;

    /**
     * Column: IS_SUMMARY_ITEM
     * Type: VARCHAR(16)
     * Default value: N
     * Remark: 是否汇总项: Y N
     */
    private String isSummaryItem;

    /**
     * Column: NATIONAL_INDUSTRY_CODE
     * Type: VARCHAR(255)
     * Remark: 对应统计局编码或国民行业代码
     */
    private String nationalIndustryCode;

    /**
     * Column: EXPORT_COMMODITY_ITEM
     * Type: VARCHAR(255)
     * Remark: 海关进出口商品品目
     */
    private String exportCommodityItem;

    /**
     * Column: START_DATE
     * Type: DATE
     * Remark: 启用时间
     */
    private Date startDate;

    /**
     * Column: END_DATE
     * Type: DATE
     * Remark: 过渡期截止时间
     */
    private Date endDate;

    /**
     * Column: IS_COMMON
     * Type: VARCHAR(32)
     * Default value: N
     * Remark: 是否常用: Y N
     */
    private String isCommon;

    /**
     * Column: IS_DELETE
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0否 1是
     */
    private Boolean isDelete;

    /**
     * Column: ADD_TIME
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private Date addTime;

    /**
     * Column: MOD_TIME
     * Type: DATETIME
     * Default value: CURRENT_TIMESTAMP
     * Remark: 修改时间
     */
    private Date modTime;

    /**
     * Column: CREATOR
     * Type: INT
     * Default value: 0
     * Remark: 添加人ID
     */
    private Integer creator;

    /**
     * Column: CREATOR_NAME
     * Type: VARCHAR(64)
     * Remark: 添加人名称
     */
    private String creatorName;

    /**
     * Column: UPDATER
     * Type: INT
     * Default value: 0
     * Remark: 更新人ID
     */
    private Integer updater;

    /**
     * Column: UPDATER_NAME
     * Type: VARCHAR(64)
     * Remark: 更新人名称
     */
    private String updaterName;

    /**
     * Column: UPDATE_REMARK
     * Type: VARCHAR(255)
     * Remark: 更新备注
     */
    private String updateRemark;

    public Integer getTaxcodeClassificationId() {
        return taxcodeClassificationId;
    }

    public void setTaxcodeClassificationId(Integer taxcodeClassificationId) {
        this.taxcodeClassificationId = taxcodeClassificationId;
    }

    public String getSummaryCode() {
        return summaryCode;
    }

    public void setSummaryCode(String summaryCode) {
        this.summaryCode = summaryCode == null ? null : summaryCode.trim();
    }

    public String getGoodsServicesClassificationAbbreviation() {
        return goodsServicesClassificationAbbreviation;
    }

    public void setGoodsServicesClassificationAbbreviation(String goodsServicesClassificationAbbreviation) {
        this.goodsServicesClassificationAbbreviation = goodsServicesClassificationAbbreviation == null ? null : goodsServicesClassificationAbbreviation.trim();
    }

    public String getGoodsServicesNameAbbreviation() {
        return goodsServicesNameAbbreviation;
    }

    public void setGoodsServicesNameAbbreviation(String goodsServicesNameAbbreviation) {
        this.goodsServicesNameAbbreviation = goodsServicesNameAbbreviation == null ? null : goodsServicesNameAbbreviation.trim();
    }

    public String getFinalCode() {
        return finalCode;
    }

    public void setFinalCode(String finalCode) {
        this.finalCode = finalCode == null ? null : finalCode.trim();
    }

    public String getGoodsServicesName() {
        return goodsServicesName;
    }

    public void setGoodsServicesName(String goodsServicesName) {
        this.goodsServicesName = goodsServicesName == null ? null : goodsServicesName.trim();
    }

    public String getClassificationAbbreviation() {
        return classificationAbbreviation;
    }

    public void setClassificationAbbreviation(String classificationAbbreviation) {
        this.classificationAbbreviation = classificationAbbreviation == null ? null : classificationAbbreviation.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword == null ? null : keyword.trim();
    }

    public String getVatrate() {
        return vatrate;
    }

    public void setVatrate(String vatrate) {
        this.vatrate = vatrate == null ? null : vatrate.trim();
    }

    public String getVatSpecialManagement() {
        return vatSpecialManagement;
    }

    public void setVatSpecialManagement(String vatSpecialManagement) {
        this.vatSpecialManagement = vatSpecialManagement == null ? null : vatSpecialManagement.trim();
    }

    public String getVatPolicyBasis() {
        return vatPolicyBasis;
    }

    public void setVatPolicyBasis(String vatPolicyBasis) {
        this.vatPolicyBasis = vatPolicyBasis == null ? null : vatPolicyBasis.trim();
    }

    public String getVatSpecialContentCode() {
        return vatSpecialContentCode;
    }

    public void setVatSpecialContentCode(String vatSpecialContentCode) {
        this.vatSpecialContentCode = vatSpecialContentCode == null ? null : vatSpecialContentCode.trim();
    }

    public String getConsumptionTaxManagement() {
        return consumptionTaxManagement;
    }

    public void setConsumptionTaxManagement(String consumptionTaxManagement) {
        this.consumptionTaxManagement = consumptionTaxManagement == null ? null : consumptionTaxManagement.trim();
    }

    public String getConsumptionTaxPolicyBasis() {
        return consumptionTaxPolicyBasis;
    }

    public void setConsumptionTaxPolicyBasis(String consumptionTaxPolicyBasis) {
        this.consumptionTaxPolicyBasis = consumptionTaxPolicyBasis == null ? null : consumptionTaxPolicyBasis.trim();
    }

    public String getConsumptionTaxSpecialContentCode() {
        return consumptionTaxSpecialContentCode;
    }

    public void setConsumptionTaxSpecialContentCode(String consumptionTaxSpecialContentCode) {
        this.consumptionTaxSpecialContentCode = consumptionTaxSpecialContentCode == null ? null : consumptionTaxSpecialContentCode.trim();
    }

    public String getIsSummaryItem() {
        return isSummaryItem;
    }

    public void setIsSummaryItem(String isSummaryItem) {
        this.isSummaryItem = isSummaryItem == null ? null : isSummaryItem.trim();
    }

    public String getNationalIndustryCode() {
        return nationalIndustryCode;
    }

    public void setNationalIndustryCode(String nationalIndustryCode) {
        this.nationalIndustryCode = nationalIndustryCode == null ? null : nationalIndustryCode.trim();
    }

    public String getExportCommodityItem() {
        return exportCommodityItem;
    }

    public void setExportCommodityItem(String exportCommodityItem) {
        this.exportCommodityItem = exportCommodityItem == null ? null : exportCommodityItem.trim();
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getIsCommon() {
        return isCommon;
    }

    public void setIsCommon(String isCommon) {
        this.isCommon = isCommon == null ? null : isCommon.trim();
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName == null ? null : updaterName.trim();
    }

    public String getUpdateRemark() {
        return updateRemark;
    }

    public void setUpdateRemark(String updateRemark) {
        this.updateRemark = updateRemark == null ? null : updateRemark.trim();
    }
}