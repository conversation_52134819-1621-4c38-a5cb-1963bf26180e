<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto">
    <!--@mbg.generated-->
    <!--@Table T_WAREHOUSE_GOODS_OUT_IN_ITEM-->
    <id column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" jdbcType="BIGINT" property="warehouseGoodsOutInDetailId" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
    <result column="WMS_NO" jdbcType="VARCHAR" property="wmsNo" />
    <result column="BARCODE_ID" jdbcType="INTEGER" property="barcodeId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="LOG_TYPE" jdbcType="INTEGER" property="logType" />
    <result column="OPERATE_TYPE" jdbcType="INTEGER" property="operateType" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="WAREHOUSE_PICKING_DETAIL_ID" jdbcType="INTEGER" property="warehousePickingDetailId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="BARCODE_FACTORY" jdbcType="VARCHAR" property="barcodeFactory" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="WAREHOUSE_ID" jdbcType="INTEGER" property="warehouseId" />
    <result column="STORAGE_ROOM_ID" jdbcType="INTEGER" property="storageRoomId" />
    <result column="STORAGE_AREA_ID" jdbcType="INTEGER" property="storageAreaId" />
    <result column="STORAGE_LOCATION_ID" jdbcType="INTEGER" property="storageLocationId" />
    <result column="STORAGE_RACK_ID" jdbcType="INTEGER" property="storageRackId" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="EXPIRATION_DATE" jdbcType="VARCHAR" property="expirationDate" />
    <result column="CHECK_STATUS" jdbcType="INTEGER" property="checkStatus" />
    <result column="CHECK_STATUS_USER" jdbcType="INTEGER" property="checkStatusUser" />
    <result column="CHECK_STATUS_TIME" jdbcType="VARCHAR" property="checkStatusTime" />
    <result column="RECHECK_STATUS" jdbcType="INTEGER" property="recheckStatus" />
    <result column="RECHECK_STATUS_USER" jdbcType="INTEGER" property="recheckStatusUser" />
    <result column="RECHECK_STATUS_TIME" jdbcType="VARCHAR" property="recheckStatusTime" />
    <result column="IS_EXPRESS" jdbcType="INTEGER" property="isExpress" />
    <result column="IS_PROBLEM" jdbcType="INTEGER" property="isProblem" />
    <result column="PROBLEM_REMARK" jdbcType="VARCHAR" property="problemRemark" />
    <result column="PRODUCT_DATE" jdbcType="VARCHAR" property="productDate" />
    <result column="COST_PRICE" jdbcType="DECIMAL" property="costPrice" />
    <result column="IS_USE" jdbcType="INTEGER" property="isUse" />
    <result column="LOGICAL_WAREHOUSE_ID" jdbcType="INTEGER" property="logicalWarehouseId" />
    <result column="VEDENG_BATCH_NUMBER" jdbcType="VARCHAR" property="vedengBatchNumber" />
    <result column="LAST_STOCK_NUM" jdbcType="INTEGER" property="lastStockNum" />
    <result column="STERILIZATION_BATCH_NUMBER" jdbcType="VARCHAR" property="sterilizationBatchNumber" />
    <result column="NEW_COST_PRICE" jdbcType="DECIMAL" property="newCostPrice" />
    <result column="DEDICATED_BUYORDER_NO" jdbcType="VARCHAR" property="dedicatedBuyorderNo" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <resultMap id="outInNoAndIsGiftResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto">
    <!--@mbg.generated-->
    <!--@Table T_WAREHOUSE_GOODS_OUT_IN_ITEM-->
    <id column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" jdbcType="BIGINT" property="warehouseGoodsOutInDetailId" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    WAREHOUSE_GOODS_OUT_IN_DETAIL_ID, OUT_IN_NO, WMS_NO, BARCODE_ID, COMPANY_ID, LOG_TYPE, 
    OPERATE_TYPE, RELATED_ID, WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, BARCODE_FACTORY, 
    NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, STORAGE_AREA_ID, STORAGE_LOCATION_ID, STORAGE_RACK_ID, 
    BATCH_NUMBER, EXPIRATION_DATE, CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, 
    RECHECK_STATUS, RECHECK_STATUS_USER, RECHECK_STATUS_TIME, IS_EXPRESS, IS_PROBLEM, 
    PROBLEM_REMARK, PRODUCT_DATE, COST_PRICE, IS_USE, LOGICAL_WAREHOUSE_ID, VEDENG_BATCH_NUMBER, 
    LAST_STOCK_NUM, STERILIZATION_BATCH_NUMBER, NEW_COST_PRICE, DEDICATED_BUYORDER_NO, 
    IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, 
    UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{warehouseGoodsOutInDetailId,jdbcType=BIGINT}
  </select>

<!--auto generated by MybatisCodeHelper on 2022-12-09-->
  <select id="findByOutInNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where OUT_IN_NO=#{outInNo,jdbcType=VARCHAR}
    and IS_DELETE =0
  </select>

  <select id="findAllOutInGoodsByOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where
      OUT_IN_NO in
      <foreach collection="list" item="item" open="(" separator="," close=")" index="index">
        #{item.outInNo}
      </foreach>
      and IS_DELETE = 0
    order by ADD_TIME
  </select>

  <select id="getSkuByIn" resultType="java.lang.String">
    select VCS.SKU_NO
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
    left join T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOII.OUT_IN_NO = TWGOI.OUT_IN_NO
    left join T_AFTER_SALES TAS on TWGOI.RELATE_NO = TAS.AFTER_SALES_NO
    left join T_AFTER_SALES_GOODS TASG on TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID
    left join V_CORE_SKU VCS on TASG.GOODS_ID = VCS.SKU_ID
    where TWGOII.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
        and TASG.AFTER_SALES_GOODS_ID = #{relatedId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="getSkuByOut" resultType="java.lang.String">
    select TSG.SKU
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
           left join T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOII.OUT_IN_NO = TWGOI.OUT_IN_NO
           left join T_SALEORDER TS on TWGOI.RELATE_NO = TS.SALEORDER_NO
           left join T_SALEORDER_GOODS TSG on TS.SALEORDER_ID = TSG.SALEORDER_ID
    where TWGOII.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
      and TSG.SALEORDER_GOODS_ID = #{relatedId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="getSpuTypeByIn" resultType="java.lang.Integer">
    select VCS2.SPU_TYPE
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
    left join T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOII.OUT_IN_NO = TWGOI.OUT_IN_NO
    left join T_AFTER_SALES TAS on TWGOI.RELATE_NO = TAS.AFTER_SALES_NO
    left join T_AFTER_SALES_GOODS TASG on TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID
    left join V_CORE_SKU VCS on TASG.GOODS_ID = VCS.SKU_ID
    left join V_CORE_SPU VCS2 on VCS.SPU_ID = VCS2.SPU_ID
    where TWGOII.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
        and TASG.AFTER_SALES_GOODS_ID = #{relatedId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="getOrderDetailId" resultType="java.lang.Integer" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto">
    select TASG.ORDER_DETAIL_ID
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
        left join T_AFTER_SALES TAS on TWGOI.RELATE_NO = TAS.AFTER_SALES_NO
        left join T_AFTER_SALES_GOODS TASG on TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID
    where TWGOI.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
        and TASG.AFTER_SALES_GOODS_ID = #{relatedId,jdbcType=INTEGER}
  </select>

  <select id="findAllOutByIn" resultMap="BaseResultMap">
    select distinct
    TWGOII2.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID, TWGOII2.OUT_IN_NO, TWGOII2.WMS_NO,
    TWGOII2.BARCODE_ID, TWGOII2.COMPANY_ID, TWGOII2.LOG_TYPE, TWGOII2.OPERATE_TYPE,
    TWGOII2.RELATED_ID, TWGOII2.WAREHOUSE_PICKING_DETAIL_ID, TWGOII2.GOODS_ID, TWGOII2.BARCODE_FACTORY,
    TWGOII2.NUM, TWGOII2.WAREHOUSE_ID, TWGOII2.STORAGE_ROOM_ID, TWGOII2.STORAGE_AREA_ID, TWGOII2.STORAGE_LOCATION_ID,
    TWGOII2.STORAGE_RACK_ID, TWGOII2.BATCH_NUMBER, TWGOII2.EXPIRATION_DATE, TWGOII2.CHECK_STATUS, TWGOII2.CHECK_STATUS_USER,
    TWGOII2.CHECK_STATUS_TIME, TWGOII2.RECHECK_STATUS, TWGOII2.RECHECK_STATUS_USER, TWGOII2.RECHECK_STATUS_TIME, TWGOII2.IS_EXPRESS,
    TWGOII2.IS_PROBLEM, TWGOII2.PROBLEM_REMARK, TWGOII2.PRODUCT_DATE, TWGOII2.COST_PRICE, TWGOII2.IS_USE, TWGOII2.LOGICAL_WAREHOUSE_ID,
    TWGOII2.VEDENG_BATCH_NUMBER, TWGOII2.LAST_STOCK_NUM, TWGOII2.STERILIZATION_BATCH_NUMBER, TWGOII2.NEW_COST_PRICE, TWGOII2.DEDICATED_BUYORDER_NO,
    TWGOII2.IS_DELETE, TWGOII2.ADD_TIME, TWGOII2.CREATOR, TWGOII2.CREATOR_NAME, TWGOII2.MOD_TIME, TWGOII2.UPDATER, TWGOII2.UPDATER_NAME,
    TWGOII2.REMARK, TWGOII2.UPDATE_REMARK

    from T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
    left join T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    left join T_AFTER_SALES TAS on TAS.AFTER_SALES_NO = TWGOI.RELATE_NO
    left join T_SALEORDER TS on TAS.ORDER_ID = TS.SALEORDER_ID
    left join T_WAREHOUSE_GOODS_OUT_IN TWGOI2 on TS.SALEORDER_NO = TWGOI2.RELATE_NO and TWGOI2.IS_VIRTUAL = 0 and TWGOI2.IS_DELETE = 0
    left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII2 on TWGOI2.OUT_IN_NO = TWGOII2.OUT_IN_NO
    <where>
        TWGOI.OUT_IN_NO = #{outInItem.outInNo,jdbcType=VARCHAR}
      and TWGOII.IS_DELETE = 0
      <if test="isSn != null and isSn == true">
        and TWGOII2.BARCODE_FACTORY != ''
      </if>
      <if test="isSn != null and isSn == false">
        and TWGOII2.BARCODE_FACTORY = ''
      </if>
      <if test="isBatch != null and isBatch == true">
        and TWGOII2.BATCH_NUMBER != ''
      </if>
      <if test="isBatch != null and isBatch == false">
        and TWGOII2.BATCH_NUMBER = ''
      </if>
      and TS.ADD_TIME <![CDATA[>=]]> #{validTime,jdbcType=BIGINT}
    </where>
    order by TWGOII2.ADD_TIME
  </select>


  <!--auto generated by MybatisCodeHelper on 2023-06-14-->
  <select id="findByOperateTypeAndRelatedIdIn" resultMap="BaseResultMap">
    select
    A.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID, A.OUT_IN_NO, A.WMS_NO, A.BARCODE_ID, A.COMPANY_ID, A.LOG_TYPE,
    A.OPERATE_TYPE, A.RELATED_ID, A.WAREHOUSE_PICKING_DETAIL_ID, A.GOODS_ID, A.BARCODE_FACTORY,
    A.NUM, A.WAREHOUSE_ID, A.STORAGE_ROOM_ID, A.STORAGE_AREA_ID, A.STORAGE_LOCATION_ID, A.STORAGE_RACK_ID,
    A.BATCH_NUMBER, A.EXPIRATION_DATE, A.CHECK_STATUS, A.CHECK_STATUS_USER, A.CHECK_STATUS_TIME,
    A.RECHECK_STATUS, A.RECHECK_STATUS_USER, A.RECHECK_STATUS_TIME, A.IS_EXPRESS, A.IS_PROBLEM,
    A.PROBLEM_REMARK, A.PRODUCT_DATE, A.COST_PRICE, A.IS_USE, A.LOGICAL_WAREHOUSE_ID, A.VEDENG_BATCH_NUMBER,
    A.LAST_STOCK_NUM, A.STERILIZATION_BATCH_NUMBER, A.NEW_COST_PRICE, A.DEDICATED_BUYORDER_NO,
    A.IS_DELETE, A.ADD_TIME, A.CREATOR, A.CREATOR_NAME, A.MOD_TIME, A.UPDATER, A.UPDATER_NAME, A.REMARK,
    A.UPDATE_REMARK
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM A
    where OPERATE_TYPE=#{operateType,jdbcType=INTEGER} and RELATED_ID in
    <foreach item="item" index="index" collection="relatedIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="getGiftGoodsWarehouseIn" resultType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto">
    SELECT TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
           TWGOII.OUT_IN_NO,
           TWGOII.WMS_NO,
           TWGOII.BARCODE_ID,
           TWGOII.COMPANY_ID,
           TWGOII.LOG_TYPE,
           TWGOII.OPERATE_TYPE,
           TWGOII.RELATED_ID,
           TWGOII.WAREHOUSE_PICKING_DETAIL_ID,
           TWGOII.GOODS_ID,
           TWGOII.BARCODE_FACTORY,
           TWGOII.NUM,
           TWGOII.WAREHOUSE_ID,
           TWGOII.STORAGE_ROOM_ID,
           TWGOII.STORAGE_AREA_ID,
           TWGOII.STORAGE_LOCATION_ID,
           TWGOII.STORAGE_RACK_ID,
           TWGOII.BATCH_NUMBER,
           TWGOII.EXPIRATION_DATE,
           TWGOII.CHECK_STATUS,
           TWGOII.CHECK_STATUS_USER,
           TWGOII.CHECK_STATUS_TIME,
           TWGOII.RECHECK_STATUS,
           TWGOII.RECHECK_STATUS_USER,
           TWGOII.RECHECK_STATUS_TIME,
           TWGOII.IS_EXPRESS,
           TWGOII.IS_PROBLEM,
           TWGOII.PROBLEM_REMARK,
           TWGOII.PRODUCT_DATE,
           TWGOII.COST_PRICE,
           TWGOII.IS_USE,
           TWGOII.LOGICAL_WAREHOUSE_ID,
           TWGOII.VEDENG_BATCH_NUMBER,
           TWGOII.LAST_STOCK_NUM,
           TWGOII.STERILIZATION_BATCH_NUMBER,
           TWGOII.NEW_COST_PRICE,
           TWGOII.DEDICATED_BUYORDER_NO,
           TWGOII.IS_DELETE,
           TWGOII.ADD_TIME,
           TWGOII.CREATOR,
           TWGOII.CREATOR_NAME,
           TWGOII.MOD_TIME,
           TWGOII.UPDATER,
           TWGOII.UPDATER_NAME,
           TWGOII.REMARK,
           TWGOII.UPDATE_REMARK,
           TSG.IS_GIFT,
           TSG.SALEORDER_ID,
           TSG.SKU skuNo
    FROM T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
           LEFT JOIN
         T_AFTER_SALES_GOODS TASG on TASG.AFTER_SALES_GOODS_ID = TWGOII.RELATED_ID
           left join T_SALEORDER_GOODS TSG on TSG.SALEORDER_GOODS_ID = TASG.ORDER_DETAIL_ID
    WHERE TWGOII.OPERATE_TYPE = 5
      AND TWGOII.IS_DELETE = 0
      and TSG.IS_GIFT = 1
      and TASG.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>

  <insert id="insertSelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto" keyColumn="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" keyProperty="warehouseGoodsOutInDetailId" useGeneratedKeys="true">
    <!--          -->
    insert into T_WAREHOUSE_GOODS_OUT_IN_ITEM
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="warehouseGoodsOutInDetailId != null" >
        WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
      </if>
      <if test="outInNo != null" >
        OUT_IN_NO,
      </if>
      <if test="wmsNo != null" >
        WMS_NO,
      </if>
      <if test="barcodeId != null" >
        BARCODE_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="logType != null" >
        LOG_TYPE,
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="warehousePickingDetailId != null" >
        WAREHOUSE_PICKING_DETAIL_ID,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="barcodeFactory != null" >
        BARCODE_FACTORY,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="warehouseId != null" >
        WAREHOUSE_ID,
      </if>
      <if test="storageRoomId != null" >
        STORAGE_ROOM_ID,
      </if>
      <if test="storageAreaId != null" >
        STORAGE_AREA_ID,
      </if>
      <if test="storageLocationId != null" >
        STORAGE_LOCATION_ID,
      </if>
      <if test="storageRackId != null" >
        STORAGE_RACK_ID,
      </if>
      <if test="batchNumber != null" >
        BATCH_NUMBER,
      </if>
      <if test="expirationDate != null" >
        EXPIRATION_DATE,
      </if>
      <if test="checkStatus != null" >
        CHECK_STATUS,
      </if>
      <if test="checkStatusUser != null" >
        CHECK_STATUS_USER,
      </if>
      <if test="checkStatusTime != null" >
        CHECK_STATUS_TIME,
      </if>
      <if test="recheckStatus != null" >
        RECHECK_STATUS,
      </if>
      <if test="recheckStatusUser != null" >
        RECHECK_STATUS_USER,
      </if>
      <if test="recheckStatusTime != null" >
        RECHECK_STATUS_TIME,
      </if>
      <if test="isExpress != null" >
        IS_EXPRESS,
      </if>
      <if test="isProblem != null" >
        IS_PROBLEM,
      </if>
      <if test="problemRemark != null" >
        PROBLEM_REMARK,
      </if>
      <if test="productDate != null" >
        PRODUCT_DATE,
      </if>
      <if test="costPrice != null" >
        COST_PRICE,
      </if>
      <if test="isUse != null" >
        IS_USE,
      </if>
      <if test="logicalWarehouseId != null" >
        LOGICAL_WAREHOUSE_ID,
      </if>
      <if test="vedengBatchNumber != null" >
        VEDENG_BATCH_NUMBER,
      </if>
      <if test="lastStockNum != null" >
        LAST_STOCK_NUM,
      </if>
      <if test="sterilizationBatchNumber != null" >
        STERILIZATION_BATCH_NUMBER,
      </if>
      <if test="newCostPrice != null" >
        NEW_COST_PRICE,
      </if>
      <if test="dedicatedBuyorderNo != null" >
        DEDICATED_BUYORDER_NO,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="updateRemark != null" >
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="warehouseGoodsOutInDetailId != null" >
        #{warehouseGoodsOutInDetailId,jdbcType=BIGINT},
      </if>
      <if test="outInNo != null" >
        #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="wmsNo != null" >
        #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="barcodeId != null" >
        #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=BIT},
      </if>
      <if test="operateType != null" >
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null" >
        #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null" >
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null" >
        #{num,jdbcType=DECIMAL},
      </if>
      <if test="warehouseId != null" >
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null" >
        #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null" >
        #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null" >
        #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null" >
        #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null" >
        #{expirationDate,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null" >
        #{checkStatus,jdbcType=BIT},
      </if>
      <if test="checkStatusUser != null" >
        #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null" >
        #{checkStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="recheckStatus != null" >
        #{recheckStatus,jdbcType=BIT},
      </if>
      <if test="recheckStatusUser != null" >
        #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null" >
        #{recheckStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="isExpress != null" >
        #{isExpress,jdbcType=BIT},
      </if>
      <if test="isProblem != null" >
        #{isProblem,jdbcType=BIT},
      </if>
      <if test="problemRemark != null" >
        #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null" >
        #{productDate,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null" >
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null" >
        #{isUse,jdbcType=BIT},
      </if>
      <if test="logicalWarehouseId != null" >
        #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumber != null" >
        #{vedengBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null" >
        #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilizationBatchNumber != null" >
        #{sterilizationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="newCostPrice != null" >
        #{newCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="dedicatedBuyorderNo != null" >
        #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null" >
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null" >
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto" >
    <!--          -->
    update T_WAREHOUSE_GOODS_OUT_IN_ITEM
    <set >
      <if test="outInNo != null" >
        OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="wmsNo != null" >
        WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="barcodeId != null" >
        BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="logType != null" >
        LOG_TYPE = #{logType,jdbcType=BIT},
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null" >
        WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null" >
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=DECIMAL},
      </if>
      <if test="warehouseId != null" >
        WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null" >
        STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null" >
        STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null" >
        STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null" >
        STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null" >
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null" >
        EXPIRATION_DATE = #{expirationDate,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null" >
        CHECK_STATUS = #{checkStatus,jdbcType=BIT},
      </if>
      <if test="checkStatusUser != null" >
        CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null" >
        CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="recheckStatus != null" >
        RECHECK_STATUS = #{recheckStatus,jdbcType=BIT},
      </if>
      <if test="recheckStatusUser != null" >
        RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null" >
        RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="isExpress != null" >
        IS_EXPRESS = #{isExpress,jdbcType=BIT},
      </if>
      <if test="isProblem != null" >
        IS_PROBLEM = #{isProblem,jdbcType=BIT},
      </if>
      <if test="problemRemark != null" >
        PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null" >
        PRODUCT_DATE = #{productDate,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null" >
        COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null" >
        IS_USE = #{isUse,jdbcType=BIT},
      </if>
      <if test="logicalWarehouseId != null" >
        LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumber != null" >
        VEDENG_BATCH_NUMBER = #{vedengBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null" >
        LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilizationBatchNumber != null" >
        STERILIZATION_BATCH_NUMBER = #{sterilizationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="newCostPrice != null" >
        NEW_COST_PRICE = #{newCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="dedicatedBuyorderNo != null" >
        DEDICATED_BUYORDER_NO = #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null" >
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{warehouseGoodsOutInDetailId,jdbcType=BIGINT}
  </update>

  <select id="queryInfoByGoodsIdAndNo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
        FROM T_WAREHOUSE_GOODS_OUT_IN_ITEM
    WHERE OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
    AND OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
    AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
  </select>
  <select id="getGiftWarehouseItemByOrderNo" resultType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto">
    SELECT TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
           TWGOII.OUT_IN_NO,
           TWGOII.WMS_NO,
           TWGOII.BARCODE_ID,
           TWGOII.COMPANY_ID,
           TWGOII.LOG_TYPE,
           TWGOII.OPERATE_TYPE,
           TWGOII.RELATED_ID,
           TWGOII.WAREHOUSE_PICKING_DETAIL_ID,
           TWGOII.GOODS_ID,
           TWGOII.BARCODE_FACTORY,
           TWGOII.NUM,
           TWGOII.WAREHOUSE_ID,
           TWGOII.STORAGE_ROOM_ID,
           TWGOII.STORAGE_AREA_ID,
           TWGOII.STORAGE_LOCATION_ID,
           TWGOII.STORAGE_RACK_ID,
           TWGOII.BATCH_NUMBER,
           TWGOII.EXPIRATION_DATE,
           TWGOII.CHECK_STATUS,
           TWGOII.CHECK_STATUS_USER,
           TWGOII.CHECK_STATUS_TIME,
           TWGOII.RECHECK_STATUS,
           TWGOII.RECHECK_STATUS_USER,
           TWGOII.RECHECK_STATUS_TIME,
           TWGOII.IS_EXPRESS,
           TWGOII.IS_PROBLEM,
           TWGOII.PROBLEM_REMARK,
           TWGOII.PRODUCT_DATE,
           TWGOII.COST_PRICE,
           TWGOII.IS_USE,
           TWGOII.LOGICAL_WAREHOUSE_ID,
           TWGOII.VEDENG_BATCH_NUMBER,
           TWGOII.LAST_STOCK_NUM,
           TWGOII.STERILIZATION_BATCH_NUMBER,
           TWGOII.NEW_COST_PRICE,
           TWGOII.DEDICATED_BUYORDER_NO,
           TWGOII.IS_DELETE,
           TWGOII.ADD_TIME,
           TWGOII.CREATOR,
           TWGOII.CREATOR_NAME,
           TWGOII.MOD_TIME,
           TWGOII.UPDATER,
           TWGOII.UPDATER_NAME,
           TWGOII.REMARK,
           TWGOII.UPDATE_REMARK,
           TSG.IS_GIFT,
           TSG.SALEORDER_ID,
           TSG.SKU skuNo
    FROM T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
           LEFT JOIN
         T_SALEORDER_GOODS TSG on TSG.SALEORDER_GOODS_ID = TWGOII.RELATED_ID
           left join T_SALEORDER TS on TS.SALEORDER_ID = TSG.SALEORDER_ID
    WHERE TWGOII.OPERATE_TYPE = 2
      AND TWGOII.IS_DELETE = 0
      and TSG.IS_GIFT = 1
      and TS.SALEORDER_NO = #{orderNo,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-02-18-->
  <select id="findByBarcodeFactory" resultMap="BaseResultMap">
    select
    TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,TWGOII.BARCODE_FACTORY,TWGOII.OUT_IN_NO
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
    left join T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOII.OUT_IN_NO = TWGOI.OUT_IN_NO
    where
    TWGOII.OPERATE_TYPE in (2,4)
    and TWGOI.IS_VIRTUAL = 0
    and TWGOII.IS_DELETE = 0
    and TWGOI.IS_DELETE = 0
    and TWGOII.BARCODE_FACTORY=#{barcodeFactory,jdbcType=VARCHAR}
    order by TWGOII.ADD_TIME desc
  </select>

  <select id="findByRelatedId" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List"/>
    FROM
        T_WAREHOUSE_GOODS_OUT_IN_ITEM
    WHERE
        RELATED_ID = #{relatedId,jdbcType=INTEGER}
    AND
        IS_DELETE = 0
        AND OPERATE_TYPE = 2
  </select>


<!--auto generated by MybatisCodeHelper on 2023-04-26-->
  <select id="findByOutInNoAndBarcodeFactory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where OUT_IN_NO=#{outInNo,jdbcType=VARCHAR} and
    BARCODE_FACTORY=#{barcodeFactory,jdbcType=VARCHAR}
  </select>


  <select id="getWarehouseGoodsOutInBySaleOrderGoodsId" resultMap="BaseResultMap">
    select
    TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,TWGOII.BARCODE_FACTORY,TWGOII.OUT_IN_NO
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
    left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    where TWGOI.OUT_IN_TYPE in (2,4)
    and TWGOII.LOG_TYPE=1
    and TWGOI.IS_DELETE=0
    and TWGOII.IS_DELETE=0
    and TWGOI.IS_VIRTUAL=0
    and TWGOII.RELATED_ID = #{relatedId,jdbcType=INTEGER}
  </select>

  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OUT_IN_ITEM
    (COMPANY_ID,
    OPERATE_TYPE,
    RELATED_ID,
    GOODS_ID,
    CHECK_STATUS_TIME,
    ADD_TIME,
    CREATOR,
    LOG_TYPE,
    BARCODE_FACTORY,
    OUT_IN_NO,
    NUM,
    BATCH_NUMBER,
    VEDENG_BATCH_NUMBER)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.companyId,jdbcType=INTEGER},
      #{item.operateType,jdbcType=INTEGER},
      #{item.relatedId,jdbcType=INTEGER},
      #{item.goodsId,jdbcType=INTEGER},
      #{item.checkStatusTime,jdbcType=VARCHAR},
      #{item.addTime,jdbcType=TIMESTAMP},
      #{item.creator,jdbcType=INTEGER},
      #{item.logType,jdbcType=INTEGER},
      #{item.barcodeFactory,jdbcType=VARCHAR},
      #{item.outInNo,jdbcType=VARCHAR},
      #{item.num,jdbcType=DECIMAL},
      #{item.batchNumber,jdbcType=VARCHAR},
      #{item.vedengBatchNumber,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-05-11-->
  <select id="findByAll" resultMap="BaseResultMap" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto">
    select
    <include refid="Base_Column_List"/>
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    <where>
      <if test="outInNo != null and outInNo != ''">
        and OUT_IN_NO=#{outInNo,jdbcType=VARCHAR}
      </if>
      <if test="companyId != null">
        and COMPANY_ID=#{companyId,jdbcType=INTEGER}
      </if>
      <if test="logType != null">
        and LOG_TYPE=#{logType,jdbcType=INTEGER}
      </if>
      <if test="operateType != null">
        and OPERATE_TYPE=#{operateType,jdbcType=INTEGER}
      </if>
      <if test="relatedId != null">
        and RELATED_ID=#{relatedId,jdbcType=INTEGER}
      </if>
      <if test="goodsId != null">
        and GOODS_ID=#{goodsId,jdbcType=INTEGER}
      </if>
      <if test="barcodeFactory != null and barcodeFactory != ''">
        and BARCODE_FACTORY=#{barcodeFactory,jdbcType=VARCHAR}
      </if>
      <if test="num != null">
        and NUM=#{num,jdbcType=INTEGER}
      </if>
      <if test="batchNumber != null and batchNumber != ''">
        and BATCH_NUMBER=#{batchNumber,jdbcType=VARCHAR}
      </if>
      <if test="checkStatusTime != null and checkStatusTime != ''">
        and CHECK_STATUS_TIME=#{checkStatusTime,jdbcType=VARCHAR}
      </if>
      <if test="vedengBatchNumber != null and vedengBatchNumber != ''">
        and VEDENG_BATCH_NUMBER=#{vedengBatchNumber,jdbcType=VARCHAR}
      </if>
      <if test="creator != null">
        and CREATOR=#{creator,jdbcType=INTEGER}
      </if>
    </where>
  </select>

  <update id="updateBatchBarcodeFactory" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_WAREHOUSE_GOODS_OUT_IN_ITEM
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BARCODE_FACTORY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{item.warehouseGoodsOutInDetailId,jdbcType=BIGINT} then #{item.barcodeFactory,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where WAREHOUSE_GOODS_OUT_IN_DETAIL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.warehouseGoodsOutInDetailId,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="findByOperateTypeAndRelatedId" resultMap="BaseResultMap">
    SELECT A.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
    A.OUT_IN_NO,
    A.WMS_NO,
    A.BARCODE_ID,
    A.COMPANY_ID,
    A.LOG_TYPE,
    A.OPERATE_TYPE,
    A.RELATED_ID,
    A.WAREHOUSE_PICKING_DETAIL_ID,
    A.GOODS_ID,
    A.BARCODE_FACTORY,
    A.NUM,
    A.WAREHOUSE_ID,
    A.STORAGE_ROOM_ID,
    A.STORAGE_AREA_ID,
    A.STORAGE_LOCATION_ID,
    A.STORAGE_RACK_ID,
    A.BATCH_NUMBER,
    A.EXPIRATION_DATE,
    A.CHECK_STATUS,
    A.CHECK_STATUS_USER,
    A.CHECK_STATUS_TIME,
    A.RECHECK_STATUS,
    A.RECHECK_STATUS_USER,
    A.RECHECK_STATUS_TIME,
    A.IS_EXPRESS,
    A.IS_PROBLEM,
    A.PROBLEM_REMARK,
    A.PRODUCT_DATE,
    A.COST_PRICE,
    A.IS_USE,
    A.LOGICAL_WAREHOUSE_ID,
    A.VEDENG_BATCH_NUMBER,
    A.LAST_STOCK_NUM,
    A.STERILIZATION_BATCH_NUMBER,
    A.NEW_COST_PRICE,
    A.DEDICATED_BUYORDER_NO,
    A.IS_DELETE,
    A.ADD_TIME,
    A.CREATOR,
    A.CREATOR_NAME,
    A.MOD_TIME,
    A.UPDATER,
    A.UPDATER_NAME,
    A.REMARK,
    A.UPDATE_REMARK
    FROM T_WAREHOUSE_GOODS_OUT_IN_ITEM A
    LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN B ON A.OUT_IN_NO = B.OUT_IN_NO
    WHERE A.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
    AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    AND A.IS_DELETE = 0
    AND B.IS_DELETE = 0
    AND B.IS_VIRTUAL = #{isVirtual,jdbcType=INTEGER}
    AND B.RELATE_NO = #{relateNo,jdbcType=VARCHAR}
  </select>
    <select id="findWarehouseGoodsOutInIdByItemId" resultType="java.lang.Integer">
      select TWGOI.WAREHOUSE_GOODS_OUT_IN_ID
      from T_WAREHOUSE_GOODS_OUT_IN TWGOI
             left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
      where TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = #{changeWarehouseGoodsOutInDetailId}
        limit 1
    </select>

  <insert id="batchInsertOld" keyColumn="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" keyProperty="warehouseGoodsOutInDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OUT_IN_ITEM
    (COMPANY_ID,
    OPERATE_TYPE,
    RELATED_ID,
    GOODS_ID,
    CHECK_STATUS_TIME,
    ADD_TIME,
    LOG_TYPE,
    OUT_IN_NO,
    NUM,
    REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.companyId,jdbcType=INTEGER},
      #{item.operateType,jdbcType=INTEGER},
      #{item.relatedId,jdbcType=INTEGER},
      #{item.goodsId,jdbcType=INTEGER},
      #{item.checkStatusTime,jdbcType=VARCHAR},
      #{item.addTime,jdbcType=TIMESTAMP},
      #{item.logType,jdbcType=INTEGER},
      #{item.outInNo,jdbcType=VARCHAR},
      #{item.num,jdbcType=DECIMAL},
      #{item.remark,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-08-31-->
  <select id="selectByOutInNoAndIsGift" resultMap="outInNoAndIsGiftResultMap">
    select
    a.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,a.RELATED_ID,a.OUT_IN_NO,a.NUM,b.SKU
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM a
    left join T_SALEORDER_GOODS b on a.RELATED_ID = b.SALEORDER_GOODS_ID
    where OUT_IN_NO=#{outInNo,jdbcType=VARCHAR}
    and b.IS_GIFT =1
    and a.IS_DELETE =0
    </select>

  <select id="selectByOutInNoAndIsGiftAfterSale" resultMap="outInNoAndIsGiftResultMap">
    select
    a.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,a.RELATED_ID,a.OUT_IN_NO,a.NUM,c.SKU
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM a
    left join T_AFTER_SALES_GOODS b on a.RELATED_ID = b.AFTER_SALES_GOODS_ID
    left join T_SALEORDER_GOODS c on c.SALEORDER_GOODS_ID = b.ORDER_DETAIL_ID
    where OUT_IN_NO=#{outInNo,jdbcType=VARCHAR}
    and c.IS_GIFT =1
    and a.IS_DELETE =0
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-06-->
  <select id="selectByRelatedIdsAndOperateType" resultMap="BaseResultMap">
    select
    RELATED_ID,SUM(ABS(IFNULL(NUM,0))) as NUM
    from T_WAREHOUSE_GOODS_OUT_IN_ITEM
    where RELATED_ID in
    <foreach collection="list" item="item" open="(" close=")" separator="," >
      #{item,jdbcType=INTEGER}
    </foreach>
    and OPERATE_TYPE=#{operateType,jdbcType=INTEGER}
    and IS_DELETE = 0
    group by RELATED_ID
  </select>
</mapper>