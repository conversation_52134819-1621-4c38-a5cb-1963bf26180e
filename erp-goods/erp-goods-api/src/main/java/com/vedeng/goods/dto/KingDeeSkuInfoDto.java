package com.vedeng.goods.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/29 10:23
 */
@Setter
@Getter
public class KingDeeSkuInfoDto {

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * spuId
     */
    private Integer spuId;

    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 型号
     */
    private String model;

    /**
     * SPU 产品类型SYS_OPTION_DEFINITION_ID
     */
    private Integer spuType;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 注册证适用范围
     */
    private String productUseRange;

    /**
     * SPU一级分类
     */
    private String firstCategoryName;

    /**
     * SPU二级分类
     */
    private String secondCategoryName;

    /**
     * SPU三级分类
     */
    private String thirdCategoryName;

    /**
     * 非医疗器械一级分类名称
     */
    private String noMedicalFirstTypeName;

    /**
     * 非医疗器械二级分类名称
     */
    private String noMedicalSecondTypeName;

    /**
     * 金蝶单位编号
     */
    private String unitKingDeeNo;

    /**
     * 税收分类编码
     */
    private String taxCategoryNo;

}