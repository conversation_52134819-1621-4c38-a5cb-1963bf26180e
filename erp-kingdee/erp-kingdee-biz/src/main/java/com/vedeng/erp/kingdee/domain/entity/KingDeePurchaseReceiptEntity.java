package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: 采购入库单
 * @author: yana.jiang
 * @date: 2022/11/10
 */

@Getter
@Setter
@ToString
@Table(name = "KING_DEE_PURCHASE_RECEIPT")
public class KingDeePurchaseReceiptEntity extends BaseEntity {
    /**
    * id
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;


    /**
    * 单据内码
    */
    private String fId;

    /**
    * 单据类型
    */
    private String fBillTypeId;

    /**
    * 单据编号
    */
    private String fBillNo;

    /**
    * 贝登单据头ID
    */
    private String fQzokBddjtId;

    /**
    * 单据日期
    */
    private String fDate;

    /**
    * 库存组织
    */
    private String fStockOrgId;

    /**
    * 供应商
    */
    private String fSupplierId;

    /**
    * fInStockEntry
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fInStockEntry;
}