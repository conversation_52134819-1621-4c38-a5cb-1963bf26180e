package com.newtask.quoteorder;

import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;

import java.util.BitSet;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class TimeUtils {

    private static final int SATURDAY = 6;
    private static final int SUNDAY = 7;

    private static final BitSet HOLIDAY_BIT_SET;


    /**
     * 根据指定的天数，计算出下一次时间点
     *
     * @param timeInMillis
     * @param numOfDays
     * @return
     */
    public static DateTime calculate(long timeInMillis, int numOfDays) {
        if (numOfDays < 0) {
            throw new IllegalArgumentException("numOfDays is negative");
        }
        if (numOfDays == 0) {
            return new DateTime(timeInMillis);
        }

        DateTime dateTimeToUse = new DateTime(timeInMillis);
        //从明天开始
        int currentDayOfYear = dateTimeToUse.getDayOfYear();

        int daysToAdd = numOfDays;

        int i = 0;
        while (i < numOfDays) {
            if (HOLIDAY_BIT_SET.get(++currentDayOfYear)) {
                daysToAdd++;
                continue;
            }
            i++;
        }

        return dateTimeToUse.plusDays(daysToAdd);
    }


    private static List<LocalDate> listDateOnHoliday() {
        List<LocalDate> list = new LinkedList<>();
        list.add(new LocalDate(2021, 1, 1));
        list.add(new LocalDate(2021, 1, 2));
        list.add(new LocalDate(2021, 1, 3));

        list.add(new LocalDate(2021, 2, 11));
        list.add(new LocalDate(2021, 2, 12));
        list.add(new LocalDate(2021, 2, 13));
        list.add(new LocalDate(2021, 2, 14));
        list.add(new LocalDate(2021, 2, 15));
        list.add(new LocalDate(2021, 2, 16));
        list.add(new LocalDate(2021, 2, 17));

        list.add(new LocalDate(2021, 4, 3));
        list.add(new LocalDate(2021, 4, 4));
        list.add(new LocalDate(2021, 4, 5));

        list.add(new LocalDate(2021, 5, 1));
        list.add(new LocalDate(2021, 5, 2));
        list.add(new LocalDate(2021, 5, 3));
        list.add(new LocalDate(2021, 5, 4));
        list.add(new LocalDate(2021, 5, 5));

        list.add(new LocalDate(2021, 6, 12));
        list.add(new LocalDate(2021, 6, 13));
        list.add(new LocalDate(2021, 6, 14));

        list.add(new LocalDate(2021, 9, 19));
        list.add(new LocalDate(2021, 9, 20));
        list.add(new LocalDate(2021, 9, 21));

        list.add(new LocalDate(2021, 10, 1));
        list.add(new LocalDate(2021, 10, 2));
        list.add(new LocalDate(2021, 10, 3));
        list.add(new LocalDate(2021, 10, 4));
        list.add(new LocalDate(2021, 10, 5));
        list.add(new LocalDate(2021, 10, 6));
        list.add(new LocalDate(2021, 10, 7));

        return list;
    }

    private static List<LocalDate> listDateOnDuty() {
        List<LocalDate> list = new LinkedList<>();
        list.add(new LocalDate(2021, 2, 7));
        list.add(new LocalDate(2021, 2, 20));
        list.add(new LocalDate(2021, 4, 25));
        list.add(new LocalDate(2021, 5, 8));
        list.add(new LocalDate(2021, 9, 18));
        list.add(new LocalDate(2021, 9, 26));
        list.add(new LocalDate(2021, 10, 9));

        return list;
    }

    static {
        LocalDate startDate = new LocalDate(2021, 1, 1);
        LocalDate endDate = new LocalDate(2022, 1, 1);

        int totalDays = Days.daysBetween(startDate, endDate).getDays();

        HOLIDAY_BIT_SET = new BitSet(totalDays);

        LocalDate dateToUse = new LocalDate(startDate);

        for (int i = 1; i <= totalDays; i++) {
            int currentWeek = dateToUse.getDayOfWeek();
            if (currentWeek == SATURDAY || currentWeek == SUNDAY) {
                HOLIDAY_BIT_SET.set(i);
            }
            dateToUse = dateToUse.plusDays(1);
        }

        //放假日期
        for (LocalDate dateOnHoliday : listDateOnHoliday()) {
            HOLIDAY_BIT_SET.set(dateOnHoliday.getDayOfYear());
        }

        //值班日期
        for (LocalDate dateOnDuty : listDateOnDuty()) {
            HOLIDAY_BIT_SET.set(dateOnDuty.getDayOfYear(), false);
        }
    }

    private static boolean isHoliday(Date date) {
        DateTime dateTime = new DateTime(date);
        return HOLIDAY_BIT_SET.get(dateTime.getDayOfYear());
    }
}
