package com.vedeng.erp.saleorder.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.trader.dto.CommunicateRecordApiDto;
import com.vedeng.erp.trader.service.CommunicateRecordApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import java.util.Optional;

@Controller
@ExceptionController
@RequestMapping("/saleCall")
@Slf4j
public class CallExpressController {

    @Autowired
    private CommunicateRecordApiService communicateRecordApiService;

    /**
     * 跳转呼出通话记录弹框
     * @param orderId
     * @param coid
     * @param phone
     * @return
     */
    @RequestMapping("/callWithExpress")
    @NoNeedAccessAuthorization
    public ModelAndView SelectExpressView(@RequestParam("orderId") Integer orderId,
                                          @RequestParam("coid") String coid,
                                          @RequestParam("phone") String phone) {
        log.info("呼出通话记录弹框，orderId:{},phone:{} coid:{}",orderId,phone,coid);
        ModelAndView mv = new ModelAndView("vue/view/call/select_express_with_call");
        CommunicateRecordApiDto communicateRecordByCoid = communicateRecordApiService.getCommunicateRecordByCoid(coid);
        log.info("根据coid查询结果：{}", JSONObject.toJSONString(communicateRecordByCoid));
        mv.addObject("saleOrderId",orderId);
        mv.addObject("coid",coid);
        mv.addObject("phone",phone);
        mv.addObject("communicateRecorderId", Optional.ofNullable(communicateRecordByCoid).map(CommunicateRecordApiDto::getCommunicateRecordId).orElseThrow(()->new ServiceException("未查到通话记录")));
        mv.addObject("endTime", DateUtil.current());
        return mv;
    }

    /**
     * 跳转添加通话记录弹框
     * @param expressId
     * @return
     */
    @RequestMapping("/addCommunicateRecorderIds")
    @NoNeedAccessAuthorization
    public ModelAndView addCommunicateRecorderIds(@RequestParam Integer expressId) {
        ModelAndView mv = new ModelAndView("vue/view/call/add_comm");
        mv.addObject("expressId",expressId);
        return mv;
    }
}
