package com.vedeng.erp.trader.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.entity.TraderCertificateErpEntity;
import com.vedeng.erp.trader.domain.entity.TraderContactEntity;
import com.vedeng.erp.trader.dto.BusinessCardDto;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.mapper.TraderCertificateErpMapper;
import com.vedeng.erp.trader.mapper.TraderContactMapper;
import com.vedeng.erp.trader.mapstruct.TraderContactConvertor;
import com.vedeng.erp.trader.service.TraderContactService;
import groovy.util.logging.Slf4j;
import org.apache.batik.gvt.CompositeGraphicsNode;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 交易者联系人(TraderContact)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-12 16:53:42
 */
@Service("newTraderContactService")
@Slf4j
public class TraderContactServiceImpl implements TraderContactService {

    @Autowired
    @Qualifier("newTraderContactMapper")
    private  TraderContactMapper traderContactDaoMapper;
    @Autowired
    private TraderCertificateErpMapper traderCertificateErpMapper;
    @Autowired
    private  TraderContactConvertor traderContactConvertor;

    @Value("${oss_http}")
    private String ossHttp;

    /**
     * 分页查询
     *
     * @param recordDtoPageParam 查询参数
     * @return 查询结果
     */
    @Override
    public PageInfo<TraderContactDto> page(PageParam<TraderContactDto> recordDtoPageParam) {
        PageInfo<TraderContactDto> pageInfo = PageHelper.startPage(recordDtoPageParam).doSelectPageInfo(() -> traderContactDaoMapper.findByAll(recordDtoPageParam.getParam()));
        pageInfo.getList().forEach(dto -> {
            List<TraderCertificateErpEntity> list = traderCertificateErpMapper.selectByRelatedId(dto.getTraderContactId());
            List<BusinessCardDto> businessCardList = new ArrayList<>();
            list.forEach(l -> {
                BusinessCardDto businessCardDto = new BusinessCardDto();
                businessCardDto.setUri(ossHttp + l.getDomain() + l.getUri());
                businessCardList.add(businessCardDto);
            });
            dto.setBusinessCard(businessCardList);
        });
        return pageInfo;
    }

    /**
     * 分页查询
     *
     * @param recordDtoPageParam 查询参数
     * @return 查询结果
     */
    @Override
    public PageInfo<TraderContactDto> erpPage(PageParam<TraderContactDto> recordDtoPageParam) {
        return PageHelper.startPage(recordDtoPageParam).doSelectPageInfo(() -> traderContactDaoMapper.erpFindByAll(recordDtoPageParam.getParam()));
    }

    @Override
    public PageInfo<TraderContactDto> searchByTraderIdAndMobile(PageParam<TraderContactDto> recordDtoPageParam) {
        return PageHelper.startPage(recordDtoPageParam).doSelectPageInfo(() -> traderContactDaoMapper.searchByTraderIdAndMobile(recordDtoPageParam.getParam()));
    }

    @Override
    public PageInfo<TraderContactDto> searchByTraderIdAndMobileAccurateMatch(PageParam<TraderContactDto> recordDtoPageParam) {
        return PageHelper.startPage(recordDtoPageParam).doSelectPageInfo(() -> traderContactDaoMapper.searchByTraderIdAndMobileAccurateMatch(recordDtoPageParam.getParam()));
    }


    /**
     * 新增数据
     *
     * @param traderContactDto 实例对象
     */
    @Override
    public void add(TraderContactDto traderContactDto) {
        TraderContactEntity traderContactEntity = traderContactConvertor.toEntity(traderContactDto);
        traderContactDaoMapper.insertSelective(traderContactEntity);
        traderContactDto.setTraderContactId(traderContactEntity.getTraderContactId());
    }

    @Override
    public List<TraderContactDto> matchTraderContractSearch(TraderContactDto traderContactDto) {
        return  traderContactDaoMapper.matchTraderContractSearch(traderContactDto);
    }

    @Override
    public List<TraderContactDto> getLatestCommunicateContact(Integer traderId) {
        List<Integer> latestCommunicateContact = traderContactDaoMapper.getLatestCommunicateContact(traderId);
        return CollectionUtils.isNotEmpty(latestCommunicateContact) ? traderContactDaoMapper.getTraderContactInfoByIdList(latestCommunicateContact) : new ArrayList<>();
    }

    @Override
    public List<TraderContactDto> getTraderContactListBySort(Integer traderId) {
        List<Integer> contactListBySort = traderContactDaoMapper.getTraderContactListBySort(traderId);
        // 这边要按照contactListBySort中id的顺序来展示结果集
        if (CollectionUtils.isNotEmpty(contactListBySort)) {
            List<TraderContactDto> contactDtoList = traderContactDaoMapper.getTraderContactInfoByIdList(contactListBySort);
            Map<Integer, TraderContactDto> contactDtoMap = contactDtoList.stream()
                    .collect(Collectors.toMap(TraderContactDto::getTraderContactId, Function.identity()));
            return contactListBySort.stream()
                    .map(contactDtoMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public TraderContactDto queryTraderContractDefault(Integer traderId) {
        return traderContactDaoMapper.queryTraderContractDefault(traderId);
    }


}
