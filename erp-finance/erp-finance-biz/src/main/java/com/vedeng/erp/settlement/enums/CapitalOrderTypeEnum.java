package com.vedeng.erp.settlement.enums;

/**
 * 订单类型
 */
public enum CapitalOrderTypeEnum {


    /**
     * 订单类型
     */
    SALE_ORDER(1, "销售订单"),
    BUY_ORDER(2, "采购订单"),
    AFTER_SALE_ORDER(3, "售后订单"),
    BUY_ORDER_EXPENSE(4, "采购费用订单"),
    AFTER_SALE_ORDER_EXPENSE(5, "采购费用售后订单"),
    BUY_ORDER_REBATE_CHARGE_APPLY(6, "采购返利结算收款申请");
    ;


    private Integer code;

    private String desc;

    CapitalOrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
