package com.wms.dto;

import java.util.List;

/**
 * 换货单dto
 */
public class ExgOrderDto {


    //售后换货单号或借货单号
    private String DOCNO;

    /**
     * PS 采购售后换货单
     * SS 销售售后换货单
     * JS 借货单"
     */
    private String DOC_TYPE;

    //是否闪电换货
    private String is_exchange;


    //审核时间
    private String ApproveTime;

    //部门
    private String Department;

    //制单员
    private String StaffName;

    //客户ID或供应商ID
    private String ConsigneeID;

    //客户名称或供应商名称
    private String ConsigneeName;

    //省
    private String C_Province;

    //城市
    private String C_City;

    //区
    private String C_district;

    //街道
    private String C_street;

    //收货详细地址
    private String C_Address1;

    //收货人联系人
    private String C_Contact;

    //收货人电话
    private String C_Tel1;

    //收货人手机
    private String C_Tel2;

    //售后原因
    private String asreasons;

    //原始单据号
    private String referenceNo;
    //备注
    private String notes;

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    private List<ExgOrderGoodDto> details;

    public String getDOCNO() {
        return DOCNO;
    }

    public void setDOCNO(String DOCNO) {
        this.DOCNO = DOCNO;
    }

    public String getDOC_TYPE() {
        return DOC_TYPE;
    }

    public void setDOC_TYPE(String DOC_TYPE) {
        this.DOC_TYPE = DOC_TYPE;
    }

    public String getIs_exchange() {
        return is_exchange;
    }

    public void setIs_exchange(String is_exchange) {
        this.is_exchange = is_exchange;
    }

    public String getApproveTime() {
        return ApproveTime;
    }

    public void setApproveTime(String approveTime) {
        ApproveTime = approveTime;
    }

    public String getDepartment() {
        return Department;
    }

    public void setDepartment(String department) {
        Department = department;
    }

    public String getStaffName() {
        return StaffName;
    }

    public void setStaffName(String staffName) {
        StaffName = staffName;
    }

    public String getConsigneeID() {
        return ConsigneeID;
    }

    public void setConsigneeID(String consigneeID) {
        ConsigneeID = consigneeID;
    }

    public String getConsigneeName() {
        return ConsigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        ConsigneeName = consigneeName;
    }

    public String getC_Province() {
        return C_Province;
    }

    public void setC_Province(String c_Province) {
        C_Province = c_Province;
    }

    public String getC_City() {
        return C_City;
    }

    public void setC_City(String c_City) {
        C_City = c_City;
    }

    public String getC_district() {
        return C_district;
    }

    public void setC_district(String c_district) {
        C_district = c_district;
    }

    public String getC_street() {
        return C_street;
    }

    public void setC_street(String c_street) {
        C_street = c_street;
    }

    public String getC_Address1() {
        return C_Address1;
    }

    public void setC_Address1(String c_Address1) {
        C_Address1 = c_Address1;
    }

    public String getC_Contact() {
        return C_Contact;
    }

    public void setC_Contact(String c_Contact) {
        C_Contact = c_Contact;
    }

    public String getC_Tel1() {
        return C_Tel1;
    }

    public void setC_Tel1(String c_Tel1) {
        C_Tel1 = c_Tel1;
    }

    public String getC_Tel2() {
        return C_Tel2;
    }

    public void setC_Tel2(String c_Tel2) {
        C_Tel2 = c_Tel2;
    }

    public String getAsreasons() {
        return asreasons;
    }

    public void setAsreasons(String asreasons) {
        this.asreasons = asreasons;
    }

    public List<ExgOrderGoodDto> getDetails() {
        return details;
    }

    public void setDetails(List<ExgOrderGoodDto> details) {
        this.details = details;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }
}
