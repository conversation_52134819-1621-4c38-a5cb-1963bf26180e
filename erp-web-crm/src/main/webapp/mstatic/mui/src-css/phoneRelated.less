
.vd-ui-phone-related {

    .phone-related-show-wrap {
        position: relative;
        width: 100%;
        padding-left: 10px;

        .phone-related-show {
            // padding-bottom: 10px;

            &.pb {
                padding-bottom: 10px;
            }

            .phone-value {
                font-size: 12px;
                color: #000;
                height: 38px;
                line-height: 38px;
                line-height: 38px;
            }
        }

        .phone-placeholder {
            display: flex;
            justify-content: space-between;
            align-items: center;

            > span {
                font-size: 12px;
                color: #ccc;
            }
            > i {
                font-size: 16px;
                color: #666;
            }
        }
    }
}

.phone-related-panel {
    height: calc(100vh - 98px);
    height: calc(100vh - 98px - constant(safe-area-inset-bottom));
    height: calc(100vh - 98px - env(safe-area-inset-bottom));

    .phone-related-ul {
        // max-height: calc(100vh - 44px - 53px - 53px - 50px); // 447px
        height: calc(100% - 53px - 53px);
        overflow-y: auto;

        .related-list {

            .pr-item {
                padding: 10px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
                color: #000;
                border-bottom: solid 1px #F5F7FA;

                &.active {
                    background: #E0F3FF;
                }

                .name {
                    flex: 1;
                    min-width: 0;
                    margin-right: 10px;
                }

                .mobile {
                    width: 90px;
                    flex-shrink: 0;
                    white-space: nowrap;
                    text-align: right;
                }
            }
            
        }
    }
}
