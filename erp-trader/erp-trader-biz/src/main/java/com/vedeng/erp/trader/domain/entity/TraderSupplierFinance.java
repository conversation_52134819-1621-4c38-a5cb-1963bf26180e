package com.vedeng.erp.trader.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
    * 供应商列表（财务专用）
    */
@Data
public class TraderSupplierFinance extends BaseEntity implements Serializable {
    /**
    * 主键
    */
    private Integer traderSupplierFinanceId;

    /**
    * 交易者ID
    */
    private Integer traderId;

    /**
    * 供应商类别 1生产厂家  2经销商
    */
    private Integer traderType;

    /**
    * 是否推送金蝶 0否 1是
    */
    private Integer isPush;

    /**
    * 推送时间
    */
    private Date pushTime;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;

    private static final long serialVersionUID = 1L;
}