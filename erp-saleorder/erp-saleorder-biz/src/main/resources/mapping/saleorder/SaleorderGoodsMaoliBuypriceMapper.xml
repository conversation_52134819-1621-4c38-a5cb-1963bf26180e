<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.SaleorderGoodsMaoliBuypriceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice">
    <!--@mbg.generated-->
    <!--@Table T_SALEORDER_GOODS_MAOLI_BUYPRICE-->
    <id column="SALEORDER_GOODS_MAOLI_BUYPRICE_ID" jdbcType="INTEGER" property="saleorderGoodsMaoliBuypriceId" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="BUY_PRICE" jdbcType="DECIMAL" property="buyPrice" />
    <result column="BUY_PRICE_DESC" jdbcType="VARCHAR" property="buyPriceDesc" />
    <result column="PRICE_STATUS" jdbcType="INTEGER" property="priceStatus" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SALEORDER_GOODS_MAOLI_BUYPRICE_ID, SALEORDER_ID, SKU_NO, BUY_PRICE, BUY_PRICE_DESC, 
    PRICE_STATUS, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SALEORDER_GOODS_MAOLI_BUYPRICE
    where SALEORDER_GOODS_MAOLI_BUYPRICE_ID = #{saleorderGoodsMaoliBuypriceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SALEORDER_GOODS_MAOLI_BUYPRICE
    where SALEORDER_GOODS_MAOLI_BUYPRICE_ID = #{saleorderGoodsMaoliBuypriceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SALEORDER_GOODS_MAOLI_BUYPRICE_ID" keyProperty="saleorderGoodsMaoliBuypriceId" parameterType="com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER_GOODS_MAOLI_BUYPRICE (SALEORDER_ID, SKU_NO, BUY_PRICE, 
      BUY_PRICE_DESC, PRICE_STATUS, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME)
    values (#{saleorderId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR}, #{buyPrice,jdbcType=DECIMAL}, 
      #{buyPriceDesc,jdbcType=VARCHAR}, #{priceStatus,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="SALEORDER_GOODS_MAOLI_BUYPRICE_ID" keyProperty="saleorderGoodsMaoliBuypriceId" parameterType="com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER_GOODS_MAOLI_BUYPRICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="buyPrice != null">
        BUY_PRICE,
      </if>
      <if test="buyPriceDesc != null">
        BUY_PRICE_DESC,
      </if>
      <if test="priceStatus != null">
        PRICE_STATUS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="buyPrice != null">
        #{buyPrice,jdbcType=DECIMAL},
      </if>
      <if test="buyPriceDesc != null">
        #{buyPriceDesc,jdbcType=VARCHAR},
      </if>
      <if test="priceStatus != null">
        #{priceStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice">
    <!--@mbg.generated-->
    update T_SALEORDER_GOODS_MAOLI_BUYPRICE
    <set>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="buyPrice != null">
        BUY_PRICE = #{buyPrice,jdbcType=DECIMAL},
      </if>
      <if test="buyPriceDesc != null">
        BUY_PRICE_DESC = #{buyPriceDesc,jdbcType=VARCHAR},
      </if>
      <if test="priceStatus != null">
        PRICE_STATUS = #{priceStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where SALEORDER_GOODS_MAOLI_BUYPRICE_ID = #{saleorderGoodsMaoliBuypriceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice">
    <!--@mbg.generated-->
    update T_SALEORDER_GOODS_MAOLI_BUYPRICE
    set SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      BUY_PRICE = #{buyPrice,jdbcType=DECIMAL},
      BUY_PRICE_DESC = #{buyPriceDesc,jdbcType=VARCHAR},
      PRICE_STATUS = #{priceStatus,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where SALEORDER_GOODS_MAOLI_BUYPRICE_ID = #{saleorderGoodsMaoliBuypriceId,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into T_SALEORDER_GOODS_MAOLI_BUYPRICE (
    SALEORDER_ID, SKU_NO, BUY_PRICE,
    PRICE_STATUS, ADD_TIME, MOD_TIME,
    CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME
    )values
    <foreach collection="list" index="index" item="data" separator=",">
      (
      #{data.saleorderId,jdbcType=INTEGER}, #{data.skuNo,jdbcType=VARCHAR}, #{data.buyPrice,jdbcType=DECIMAL},
      #{data.priceStatus,jdbcType=INTEGER}, #{data.addTime,jdbcType=TIMESTAMP}, #{data.modTime,jdbcType=TIMESTAMP},
      #{data.creator,jdbcType=INTEGER}, #{data.updater,jdbcType=INTEGER}, #{data.creatorName,jdbcType=VARCHAR},
      #{data.updaterName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>


  <delete id="deleteBySaleorderId" parameterType="java.lang.Integer">
      delete from T_SALEORDER_GOODS_MAOLI_BUYPRICE
      where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </delete>

  <select id="selectUnReadMaoliSaleOrderId" resultType="java.lang.Integer">
    select SALEORDER_ID FROM T_SALEORDER_GOODS_MAOLI_BUYPRICE
    WHERE PRICE_STATUS IN (0,2) and ADD_TIME <![CDATA[<=]]> DATE_SUB(NOW(), INTERVAL 10 SECOND)
      GROUP BY SALEORDER_ID
    LIMIT 100
  </select>

  <select id="queryListBySaleorderId" resultType="com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice">
    select
    <include refid="Base_Column_List" />
    FROM T_SALEORDER_GOODS_MAOLI_BUYPRICE
    WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    LIMIT 1000
  </select>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach close="" collection="list" index="index" item="item" open="" separator=";">
      UPDATE T_SALEORDER_GOODS_MAOLI_BUYPRICE
      <set>
        BUY_PRICE = #{item.buyPrice,jdbcType=DECIMAL},
        BUY_PRICE_DESC = #{item.buyPriceDesc,jdbcType=VARCHAR},
        PRICE_STATUS = #{item.priceStatus,jdbcType=INTEGER},
        MOD_TIME = #{item.modTime,jdbcType=TIMESTAMP}
      </set>
      WHERE SALEORDER_GOODS_MAOLI_BUYPRICE_ID = #{item.saleorderGoodsMaoliBuypriceId,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>