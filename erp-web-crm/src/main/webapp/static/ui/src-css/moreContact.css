.more-contact-component {
  position: relative;
  margin-bottom: 15px;
}
.more-contact-component .contact-list .contact-item {
  position: relative;
}
.more-contact-component .contact-list .contact-item .delete {
  position: absolute;
  left: 573px;
  top: 0;
  z-index: 5;
  width: 34px;
  height: 30px;
}
.more-contact-component .contact-list .contact-item .delete > i {
  width: 34px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.more-contact-component .contact-list .contact-item .delete > i:hover {
  color: #f60;
}
.more-contact-component .add-contact {
  padding-left: 250px;
  margin-top: 15px;
}
.more-contact-component .add-contact > a {
  font-size: 12px;
  color: #09f;
  cursor: pointer;
}
.more-contact-component .add-contact > a:hover {
  color: #f60;
}
.more-contact-component .add-contact > a .icon-add {
  font-size: 16px;
  vertical-align: -2px;
}
.modal-form-wrap {
  min-height: 32px;
}
.other {
  margin-top: 10px;
}
.other .tips {
  margin-top: 5px;
  color: #999;
}
