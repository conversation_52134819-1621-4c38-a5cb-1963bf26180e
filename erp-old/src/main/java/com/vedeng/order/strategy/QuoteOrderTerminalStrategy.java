package com.vedeng.order.strategy;

import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.strategy.OrderTerminalStrategy;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.model.Quoteorder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 报价单终端信息处理类
 * @Date 2023/9/5 13:24
 */
@Service
public class QuoteOrderTerminalStrategy implements OrderTerminalStrategy {

    @Autowired
    private OrderTerminalApiService orderTerminalApiService;

    @Autowired
    private QuoteorderMapper quoteorderMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(OrderTerminalDto orderTerminalDto) {
        if (Objects.nonNull(orderTerminalDto.getAreaId()) && orderTerminalDto.getAreaId() > 0) {
            orderTerminalDto.setBusinessType(2);
            Quoteorder update = new Quoteorder();
            update.setQuoteorderId(orderTerminalDto.getBusinessId());
            orderTerminalApiService.save(orderTerminalDto);
            update.setSalesAreaId(orderTerminalDto.getAreaId());
            update.setSalesArea(orderTerminalDto.getProvinceName() + orderTerminalDto.getCityName() + orderTerminalDto.getAreaName());
            update.setTerminalTraderName(orderTerminalDto.getTerminalName());
            // 取不到的字段 附上默认值
            update.setTerminalTraderId(0);
            update.setTerminalTraderType(0);
            quoteorderMapper.updateByPrimaryKeySelective(update);
        }
    }
}