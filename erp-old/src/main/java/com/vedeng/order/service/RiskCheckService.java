package com.vedeng.order.service;

import com.vedeng.authorization.model.User;
import com.vedeng.order.chain.model.BuyorderRiskModelVo;
import com.vedeng.order.chain.model.RiskModel;
import com.vedeng.order.chain.model.SaleorderRiskModelVo;
import com.vedeng.order.chain.model.SkuRiskModelVo;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName RiskCheckService.java
 * @Description TODO 风控校验
 * @createTime 2020年12月12日 10:52:00
 */
public interface RiskCheckService {
    /**
     * @description: 销售单风控
     * @return: RiskModel
     * @author: Strange
     * @date: 2020/12/12
     **/
    SaleorderRiskModelVo riskCheckSaleOrder(Saleorder order);

    /**
     * @description: 采购单风控
     * @return: BuyorderRiskModelVo
     * @author: Strange
     * @date: 2020/12/12
     **/
    BuyorderRiskModelVo riskCheckBuyOrder(Buyorder order);

    /**
     * @description: 商品风控
     * @return:  SkuRiskModelVo
     * @author: Strange
     * @date: 2020/12/14
     **/
    SkuRiskModelVo riskCheckSku(String sku);

    /**
     * @description: 报价单风控
     * @return: SaleorderRiskModelVo
     * @author: Strange
     * @date: 2020/12/14
     **/
    void riskCheckQuoteorder(Saleorder saleorder);

    /**
     * @description: 设置销售单商品风控信息
     * @return:
     * @author: Strange
     * @date: 2020/12/15
     **/
    void setSaleorderIsRiskInfo(Saleorder sale, List<SaleorderGoods> saleorderGoodsList);

    /**
     * @description: 获取未通过时报错提示 和url
     * @return:
     * @author: Strange
     * @date: 2020/12/15
     **/
    SaleorderRiskModelVo getUrlRiskAndCheckSaleorder(Saleorder saleorder);
    /**
     * @description: 设置采购单商品风控信息
     * @return:
     * @author: Strange
     * @date: 2020/12/15
     **/
    void setBuyorderGoodsIsRiskInfo(Buyorder buyorder, List<BuyorderGoodsVo> list);

    /**
     * @description: 获取风控申请审核按钮flag
     * @return: Integer
     * @author: Strange
     * @date: 2020/12/17
     **/
    Integer getRiskFlag(User user, Integer saleorderIsRisk);

    /**
     * @description: 是否为此部门
     * @return: Boolean
     * @author: Strange
     * @date: 2020/12/16
     **/
    Boolean permoissionsFlag(User user, String qualityOrg);

    Boolean isOrgFlag(User user, String qualityOrg);

    /**
     * @description: 风控总开关
     * @return: Boolean
     * @author: Strange
     * @date: 2020/12/17
     **/
    Boolean getisRiskcheck();

    /**
     * @description: 获取校验文本并且check
     * @return: BuyorderRiskModelVo
     * @author: Strange
     * @date: 2020/12/17
     **/
    BuyorderRiskModelVo getTitleAndcheckBuyorder(Buyorder buyorder);

    /**
     * @description: check此spu关联所有的sku
     * @return:
     * @author: Strange
     * @date: 2020/12/18
     **/
    void checkSpuAndSkuTodo(Integer spuId);

    void checkFirstEnageAndSkuTodo(Integer firstEnageId);

    void checkSkuAndtodo(Integer skuId);

    void checkTraderTodo(Integer traderId);

    void checkSupplierTodo(Integer traderId);

    void resetSaleorderRisk(Integer saleorderId);

    void resetBuyorderRiks(Integer buyorderId);

    RiskModel riskCheckTrader(Integer traderId, Integer traderType);

    void setSaleorderIsRiskInfo(Saleorder order);

    void setBuyorderGoodsIsRiskInfo(BuyorderVo bv);
}
