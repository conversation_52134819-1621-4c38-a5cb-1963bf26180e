package com.vedeng.erp.trader.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 报价产品
 * <AUTHOR>
 */
@Data
public class QuoteorderGoodsDto {
    private Integer quoteorderGoodsId;

    /**
     * 报价ID
     */
    private Integer quoteorderId;

    /**
     * 是否临时产品 0否 1是
     */
    private Boolean isTemp;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 唯一编码
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 中文名
     */
    private String unitName;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 货币单位ID
     */
    private Integer currencyUnitId;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 货期
     */
    private String deliveryCycle;

    /**
     * 是否直发 0否 1是
     */
    private Boolean deliveryDirect;

    /**
     * 直发备注
     */
    private String deliveryDirectComments;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 参考成本
     */
    private BigDecimal referenceCostPrice;

    /**
     * 参考价格（产品部门回复）
     */
    private String referencePrice;

    /**
     * 参考货期（产品部门回复）
     */
    private String referenceDeliveryCycle;

    /**
     * 报备结果0不需要报备 1等待报备 2成功 3失败
     */
    private Boolean reportStatus;

    /**
     * 报备原因
     */
    private String reportComments;

    /**
     * 是否包含安调 0否 1是
     */
    private Boolean haveInstallation;

    /**
     * 产品备注
     */
    private String goodsComments;

    /**
     * 内部备注
     */
    private String insideComments;

    /**
     * 是否删除0否1是
     */
    private Boolean isDelete;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 上次回复价格的用户ID
     */
    private Integer lastReferenceUser;

    /**
     * 是否需要人工回复 0否 1是
     */
    private Boolean isNeedReply;
}