@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-select {
  position: relative;
  width: 300px;
  height: 100%;
}
.vd-ui-select .vd-ui-select-wrapper {
  position: relative;
  width: 100%;
}
.vd-ui-select .vd-ui-select-wrapper .icon {
  position: absolute;
  right: 5px;
  font-size: 16px;
  color: #666666;
  cursor: pointer;
  pointer-events: none;
  transition: 0.19s;
  top: 8px;
  line-height: 1;
}
.vd-ui-select .vd-ui-select-wrapper .icon.large {
  line-height: 42px;
}
.vd-ui-select .vd-ui-select-wrapper .icon.small {
  line-height: 26px;
}
.vd-ui-select .vd-ui-select-wrapper .icon.rotate {
  transform: rotate(180deg);
  transition: 0.22s;
}
.vd-ui-select .vd-ui-select-wrapper input::-webkit-input-placeholder {
  color: #999999;
}
.vd-ui-select .vd-ui-select-wrapper .vd-ui-input input {
  cursor: pointer;
}
.vd-ui-select-wrapper__disabled .icon {
  color: #999999;
}
.vd-ui-select .vd-ui-select-wrapper__error .vd-ui-input input {
  border-color: #E64545 !important;
}
.vd-ui-select-multiple-wrapper {
  position: relative;
  background-color: #ffffff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  min-height: 30px;
  box-sizing: border-box;
  transition: border-color 0.1s linear;
  cursor: pointer;
  padding: 0 10px;
  padding-right: 36px;
  display: flex;
  align-items: center;
}
.vd-ui-select-multiple-wrapper:hover {
  border-color: #969B9E;
}
.vd-ui-select-multiple-wrapper.is-focus {
  border-color: #0099FF;
}
.vd-ui-select-multiple-wrapper.is-focus:hover {
  border-color: #0099FF;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag {
  max-width: 80%;
  font-size: 0;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .placeholder {
  margin-top: 4px;
  color: #999999;
  font-size: 12px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag.has-more .vd-ui-select-tag {
  max-width: calc(100% - 38px);
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag {
  padding-left: 5px;
  padding-right: 24px;
  background-color: #F5F7FA;
  font-size: 12px;
  border-radius: 2px;
  border: 1px solid #E1E5E8;
  position: relative;
  margin-right: 5px;
  display: inline-block;
  height: 22px;
  box-sizing: border-box;
  max-width: calc(100% - 5px);
  transform-origin: center center;
  animation: tagAppear 0.19s ease-in;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag.tag-more {
  padding-right: 5px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag > span {
  line-height: 20px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag .tag-text {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  margin-bottom: -5px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag .tag-icon {
  padding-right: 5px;
  cursor: pointer;
  position: absolute;
  right: 0;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag .tag-icon:hover i {
  color: #0099FF;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag .tag-icon i {
  font-size: 14px;
  position: relative;
  top: 1px;
  color: #999999;
}
.vd-ui-select-multiple-wrapper .vd-ui-input-multiple {
  flex: 1;
  border: none;
  min-width: 20%;
  height: 26px;
  padding-left: 10px;
  margin-left: -10px;
  border-radius: 3px 0 0 3px;
}
.vd-ui-select-multiple-wrapper .vd-ui-input-multiple::placeholder {
  font-size: 12px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag-auto {
  max-width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag-auto .vd-ui-select-tag {
  margin-top: 2px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag-auto .vd-ui-input-multiple {
  line-height: 26px;
  font-size: 12px;
}
.vd-ui-select-multiple-wrapper .vd-ui-readonly {
  max-width: 100%;
  flex: 1;
}
.vd-ui-select-multiple-wrapper .mul-icon {
  font-size: 16px;
  position: absolute;
  right: 10px;
  transition: 0.19s;
}
.vd-ui-select-multiple-wrapper .mul-icon.icon-error2 {
  color: #666666;
}
.vd-ui-select-multiple-wrapper .mul-icon.rotate {
  transform: rotate(180deg);
  transition: 0.22s;
}
.vd-ui-select-multiple-wrapper__error {
  border-color: #E64545 !important;
}
.vd-ui-select-multiple-wrapper__disabled {
  background-color: #F5F7FA;
  border-color: #D7DADE;
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-select-multiple-wrapper__disabled .vd-ui-tag .vd-ui-select-tag {
  padding-right: 5px;
}
.vd-ui-select-multiple-wrapper__disabled:hover {
  border-color: #D7DADE;
}
.vd-ui-select-multiple-wrapper__disabled.is-focus {
  border-color: #D7DADE;
}
.vd-ui-select-multiple-wrapper__disabled.is-focus:hover {
  border-color: #D7DADE;
}
.vd-ui-select-multiple-wrapper-large {
  min-height: 42px;
}
.vd-ui-select-list {
  width: 100%;
  box-sizing: border-box;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  z-index: 15;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
  overscroll-behavior: contain;
}
.vd-ui-select-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-select-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-select-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-select-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-select-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-select-list.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-select-list.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-select-list .loading-li {
  height: 29px;
  line-height: 29px;
  padding: 0px 10px;
}
.vd-ui-select-list .loading-li i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 2px;
  font-size: 16px;
  margin-right: 5px;
  color: #0099FF;
}
.vd-ui-select-list .failed-li {
  height: 29px;
  line-height: 29px;
  padding: 0px 10px;
}
.vd-ui-select-list .failed-li i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-select-list .failed-li .reload {
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-select-list .empty-li {
  height: 29px;
  line-height: 29px;
  padding: 0px 10px;
  text-align: center;
  color: #999999;
}
.vd-ui-select .vd-ui-input-error {
  color: #E64545;
  margin-top: 5px;
  display: flex;
  align-items: center;
}
.vd-ui-select .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
}
.vd-ui-select .vd-ui-input-error .vd-ui-input-error--errmsg {
  margin: 0px;
}
@keyframes appear {
  0% {
    opacity: 0;
    -webkit-transform: scale(1, 0);
  }
  100% {
    -webkit-transform: scale(1, 1);
    opacity: 1;
  }
}
@keyframes tagAppear {
  0% {
    opacity: 0;
    -webkit-transform: scale(1, 0);
  }
  100% {
    -webkit-transform: scale(1, 1);
    opacity: 1;
  }
}
.slide-enter {
  opacity: 1;
  transform: scale(1, 1);
}
.slide-leave-to {
  opacity: 0;
  -webkit-transform: scale(1, 0);
}
.vd-ui-group {
  margin-bottom: 5px;
}
.vd-ui-group:last-child {
  margin-bottom: 0;
}
.vd-ui-group .vd-ui-group-title {
  padding: 0px 10px;
  height: 33px;
  color: #999999;
}
.vd-ui-group .vd-ui-group-title p {
  line-height: 33px;
}
.ui-select-option-li {
  height: 33px;
  width: 100%;
  cursor: pointer;
  position: relative;
}
.ui-select-option-li .icon-checkbox1,
.ui-select-option-li .icon-checkbox2 {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #969B9E;
}
.ui-select-option-li .icon-checkbox2 {
  color: #0099FF;
}
.ui-select-option-li .li-p {
  padding: 0px 10px;
  display: flex;
}
.ui-select-option-li .li-p .before {
  width: 50%;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}
.ui-select-option-li .li-p .after {
  padding-left: 10px;
  width: 50%;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}
.ui-select-option-li .li-p-made {
  display: flex;
  align-items: center;
}
.ui-select-option-li .li-p-made .li-avatar {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
  position: relative;
}
.ui-select-option-li .li-p-made .li-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.ui-select-option-li .li-p-made .li-avatar img.error {
  width: 0;
  height: 0;
}
.ui-select-option-li .li-p-made .li-avatar img.error:before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  left: 0;
  top: 0;
  background-image: url(../image/crm-user-avatar.svg);
  background-size: 100% 100%;
}
.ui-select-option-li .li-p-made .li-label {
  flex: 1;
}
.ui-select-option-li .li-p-made .li-label .strong {
  color: #f60;
}
.ui-select-option-li .li-p-made .li-name {
  text-align: right;
  width: 50%;
  margin-left: 10px;
  color: #999;
}
.ui-select-option-li.selected .li-p .li-label {
  color: #0099FF;
}
.ui-select-option-li.multiple .li-p {
  padding-left: 36px;
}
.ui-select-option-li p {
  line-height: 33px;
  margin: 0;
  font-size: 12px;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}
.ui-select-option-li:hover {
  background-color: #F5F7FA;
}
.ui-select-option-li:hover .icon-checkbox1 {
  color: #0099FF;
}
.ui-select-option-li.disabled:hover {
  background-color: #ffffff;
}
.ui-select-option-li.disabled:hover .icon-checkbox1 {
  color: #D7DADE;
}
.ui-select-option-li.disabled:hover .icon-checkbox2 {
  color: #D7DADE;
}
.ui-select-option-li.disabled .icon-checkbox1 {
  color: #D7DADE;
}
.ui-select-option-li.disabled .icon-checkbox2 {
  color: #D7DADE;
}
.ui-select-option-li.disabled .li-p {
  cursor: not-allowed;
}
.ui-select-option-li.disabled .li-p p {
  color: #999999;
}
.ui-select-option-li:active {
  background-color: #EBEFF2;
}
.vd-ui-custom-select-drop {
  background: #fff;
  padding: 10px 0;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  z-index: 11;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-search {
  padding: 0 10px;
  margin-bottom: 10px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-search .vd-ui-input .vd-ui-input__inner {
  border-radius: 0;
  border-top: 0;
  border-left: 0;
  border-right: 0;
}
.vd-ui-custom-select-drop .vd-ui-custom-loading-wrap {
  display: flex;
  color: #666;
  padding: 6px 10px;
  align-items: center;
}
.vd-ui-custom-select-drop .vd-ui-custom-loading-wrap .icon-loading {
  font-size: 16px;
  line-height: 1;
  color: #09f;
  margin-right: 5px;
  animation: loading 2s linear infinite;
}
.vd-ui-custom-select-drop .vd-ui-custom-empty {
  color: #999;
  text-align: center;
  padding: 6px 10px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list {
  max-height: 330px;
  overflow: auto;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 33px;
  padding: 0 10px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox {
  font-size: 16px;
  margin-right: 5px;
  display: flex;
  align-items: center;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox .vd-ui_icon {
  line-height: 1;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox .vd-ui_icon.icon-checkbox1 {
  color: #969B9E;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox .vd-ui_icon.icon-checkbox2 {
  color: #09f;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox + .item-avatar {
  margin-left: 5px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-avatar {
  width: 18px;
  height: 18px;
  border-radius: 2px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-label {
  flex: 1;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-label .strong {
  color: #f60;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item:hover {
  background: #f5f7fa;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item:hover .item-checkbox .vd-ui_icon.icon-checkbox1 {
  color: #09f;
}
.vd-ui-custom-select-drop .vd-ui-custom-footer {
  margin-top: 5px;
  padding: 10px 10px 0 10px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #E1E5E8;
}
.vd-ui-custom-select-drop .vd-ui-custom-footer .vd-ui-button {
  margin-left: 10px;
}
