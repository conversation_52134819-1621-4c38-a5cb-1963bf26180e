package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.model.po.VJdGoods;
import java.util.List;

import com.vedeng.erp.saleorder.model.po.VJdSaleorder;
import org.apache.ibatis.annotations.Param;

public interface VJdGoodsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(VJdGoods record);

    int insertOrUpdate(VJdGoods record);

    int insertOrUpdateSelective(VJdGoods record);

    int insertSelective(VJdGoods record);

    VJdGoods selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(VJdGoods record);

    int updateByPrimaryKey(VJdGoods record);

    int updateBatch(List<VJdGoods> list);

    int updateBatchSelective(List<VJdGoods> list);

    int batchInsert(@Param("list") List<VJdGoods> list);

    VJdGoods findOneByJdGoodsIdAndDeleteFlag(@Param("jdGoodsId")Long jdGoodsId,@Param("deleteFlag")Integer deleteFlag);



    List<VJdGoods> getGoodsMappingByList(@Param("list") List<VJdSaleorder> insertList);
}