<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<div class="customer">
    <form id="kpiForm"action="${pageContext.request.contextPath}/sales/fiveSales/detailsPage.do" method="get">

       <input type="hidden" name="userId" value="${five_userId}">
        <input type="hidden" name="userFlag" value="${userFlag}">
        <input type="hidden" name="hisMonth" id="hisMonth" value="${hisMonth}">
        <input type="hidden" name="sortType" value="${sortType}">
    </form>
    <ul>
        <li>
            <a sorttype="1" name="label" class="click<c:if test="${empty sortType || sortType == 1 }"> customer-color</c:if>"
            	 >五行剑法-业绩
            </a>
        </li>
<%--        <li>--%>
<%--            <a href="${pageContext.request.contextPath}/sales/fiveSales/detailsPage.do?sortType=7&userId=${five_userId}&companyId=${companyId}&userFlag=${userFlag}" --%>
<%--            	<c:if test="${sortType == 7 }">class="customer-color"</c:if> >五行剑法-核心商品--%>
<%--            </a>--%>
<%--        </li>--%>
        <li>
            <a sorttype="3" name="label" class="click<c:if test="${sortType == 3 }"> customer-color</c:if>"
            	 >五行剑法-客户
            </a>
        </li>
<%--        <li>--%>
<%--            <a href="${pageContext.request.contextPath}/sales/fiveSales/detailsPage.do?sortType=4&userId=${five_userId}&companyId=${companyId}&userFlag=${userFlag}" --%>
<%--            	<c:if test="${sortType == 4 }">class="customer-color"</c:if> >五行剑法-通话时长--%>
<%--            </a>--%>
<%--        </li>--%>
        <li>
            <a  sorttype="8" name="label" class="click<c:if test="${sortType == 8 }"> customer-color</c:if>"
            	<c:if test="${sortType == 8 }">class="customer-color"</c:if> >五行剑法-BD客户数
            </a>
        </li>
        <li>
            <a  sorttype="5" name="label" class="click<c:if test="${sortType == 5 }"> customer-color</c:if>"
               <c:if test="${sortType == 5 }">class="customer-color"</c:if> >五行剑法-询价转化率
            </a>
        </li>
        <li>
            <label class="infor_name">历史月份</label>
            <select class="input-middle" name="monthList" id="monthList">
                <c:forEach items="${monthList}" var="month">
                    <option value="${month}" <c:if test="${month eq hisMonth}">selected="selected"</c:if>>${month}</option>
                </c:forEach>
            </select>
        </li>
    </ul>
</div>
<script>
    $(function(){
        $("#monthList").change(function(){
            $("#hisMonth").val($(this).val());
            $("#kpiForm").submit();
        });

        $(".click").click(function(){
            $("input[name=sortType]").val($(this).attr("sorttype"));
            $("#kpiForm").submit();
        });
    })
</script>