package com.vedeng.erp.trader.common.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 沟通类型
 * @date 2022/7/21 20:52
 **/
public enum CommunicateRecordTypeEnum {


    // 客户
    CUSTOMER(242, "客户"),
    // 供应商
    SUPPLIER(243, "供应商"),
    // 商机
    BUSINESS_CHANCE(244, "商机"),
    // 报价
    QUOTE(245, "报价"),
    // 销售订单
    SALES_ORDER(246, "销售订单"),
    // 采购订单
    PURCHASE_ORDER(247, "采购订单"),
    // 售后订单
    AFTER_SALES_ORDER(248, "售后订单"),
    // 线索
    LEAD(4083, "线索"),
    // 商机线索
    BUSINESS_LEAD(4109, "商机线索"),
    // 采购费用售后
    PURCHASE_AFTER_SALES(4133, "采购费用售后"),

    VISIT_RECORD(5503, "拜访计划");




    private final Integer code;

    private final String type;

    CommunicateRecordTypeEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    /**
     * 根据code获取对应的description
     *
     * @param code 枚举的code值
     * @return 对应的description，如果没有找到匹配的code，返回异常
     */
    public static String getTypeByCode(int code) {
        return Stream.of(CommunicateRecordTypeEnum.values())
                .filter(bt -> bt.code == code)
                .findFirst()
                .map(CommunicateRecordTypeEnum::getType)
                .orElseThrow(() -> new SecurityException("No enum constant with code " + code));
    }
}
