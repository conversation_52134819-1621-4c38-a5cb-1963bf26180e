package com.vedeng.logistics.eums;

/**
 * 出入库日志主表：来源、明细表对应关系
 */
public enum WarehouseOutInSourceEnum {
    NORMAL_DELIVERY("WMS","T_WAREHOUSE_GOODS_OPERATE_LOG",2),
    DIRECT_DELIVERY("ERP","T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT",1);

    private final String source;
    private final String table;
    private final Integer companyId;

    WarehouseOutInSourceEnum(String source, String table, Integer companyId) {
        this.source = source;
        this.table = table;
        this.companyId = companyId;
    }

    public String getSource() {
        return source;
    }

    public String getTable() {
        return table;
    }

    public Integer getCompanyId() {
        return companyId;
    }
}
