package com.vedeng.goods.manager.validator.builder;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.exception.InvalidatorNoSkuNoException;
import com.vedeng.goods.exception.InvalidatorNoSpuNoException;
import com.vedeng.goods.exception.InvalidatorNoUserException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.manager.validator.InvalidatorChain.ReasonNumInvalidator;
import com.wms.service.context.ThreadLocalContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedList;
import java.util.List;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.ReasonNumNullInvalidatorError;


@Slf4j
public class GoodsInvalidatorChainBuilder {


    private LinkedList<GoodsInvalidatorChain> goodsInvalidatorChainList = new LinkedList<>();

    public static GoodsInvalidatorChainBuilder build(){
        return new GoodsInvalidatorChainBuilder();
    }

    public GoodsInvalidatorChainBuilder add(GoodsInvalidatorChain goodsInvalidatorChain){
        goodsInvalidatorChainList.add(goodsInvalidatorChain);
        return this;
    }

    public GoodsInvalidatorChainBuilder setSkuNo(String skuNo){
        if(StringUtils.isBlank(skuNo)){
            throw new InvalidatorNoSkuNoException();
        }
        ThreadLocalContext.put("invalidSkuNo",skuNo);
        return this;
    }

    public GoodsInvalidatorChainBuilder setSpuNo(String spuNo){
        if(StringUtils.isBlank(spuNo)){
            throw new InvalidatorNoSpuNoException();
        }
        ThreadLocalContext.put("invalidSpuNo",spuNo);
        return this;
    }

    public GoodsInvalidatorChainBuilder setSkuNoList(List<String> skuNoList){
        ThreadLocalContext.put("invalidSkuNoList",skuNoList);
        return this;
    }


    public GoodsInvalidatorChainBuilder setUser(User user){
        if(user == null){
            throw new InvalidatorNoUserException();
        }
        ThreadLocalContext.put("invalidUser",user);
        return this;
    }
    public GoodsInvalidatorChainBuilder setReason(String reason){
        if(StringUtils.isBlank(reason)){
            throw new InvalidatorChainException(ReasonNumNullInvalidatorError.getCode(),ReasonNumNullInvalidatorError.getMessage());
        }
        ThreadLocalContext.put("invalidReason",reason);
        return this;
    }



    public void removeAll(){
        ThreadLocalContext.remove("invalidSkuNo");
        ThreadLocalContext.remove("invalidSpuNo");
        ThreadLocalContext.remove("invalidUser");
        ThreadLocalContext.remove("invalidReason");
    }

    public ResultInfo execute(){
        try {
            if(goodsInvalidatorChainList.size() > 0){
                for (GoodsInvalidatorChain goodsInvalidatorChain : goodsInvalidatorChainList) {
                    goodsInvalidatorChain.doInvalid();
                }
            }
            removeAll();
            log.info("检验成功");
        } catch (InvalidatorChainException e){
            log.info("商品禁用审核校验出错"+e);
            return new ResultInfo(e.getErrorCode(),e.getErrorMessage());
        }
        catch (Exception e) {
            log.info("商品禁用审核校验出错"+e.getMessage());
            return ResultInfo.error() ;
        }
        return ResultInfo.success();
    }
}
