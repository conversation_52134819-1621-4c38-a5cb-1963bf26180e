package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderCustomerMarketingNodeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 客户类型
 * @date 2023/8/9 9:26
 **/
public interface TraderCustomerMarketingNodeApiService {

    /**
     * 获取三级分类
     *
     * @param type 类型
     * @param isSort 排序
     * @return List<TraderCustomerMarketingNodeDto> 集合
     */
    List<TraderCustomerMarketingNodeDto> getTraderCustomerMarketList(Integer type,boolean isSort);


    /**
     * 获取机构类型的子集
     * @param threeCustomerType 三级分类
     * @param institutionType 机构类型
     * @return List<TraderCustomerMarketingNodeDto.Node>
     */
    List<TraderCustomerMarketingNodeDto.Node> getInstitutionTypeChild(Integer threeCustomerType,Integer institutionType);

}
