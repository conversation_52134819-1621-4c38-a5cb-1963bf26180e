package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 通用附件推送金蝶（针对单个附件）
 * @date 2023/1/10 13:55
 */
@Service
@Slf4j
public class CommonFileDataWriter extends BaseWriter<KingDeeFileDataDto> {

    @Autowired
    private KingDeeFileDataApiService kingDeeFileDataApiService;

    @Override
    public void doWrite(KingDeeFileDataDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("CommonFileDataWriter#doWrite:{}", JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.uploadFileData);
        kingDeeFileDataApiService.register(dto, true);
    }

}
