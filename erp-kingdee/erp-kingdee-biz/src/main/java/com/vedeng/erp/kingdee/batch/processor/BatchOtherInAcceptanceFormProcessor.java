package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseTypeAndFormIdEnum;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAttachmentDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.service.*;
import com.vedeng.erp.kingdee.service.impl.KingDeeAllocationServiceImpl;
import com.vedeng.erp.kingdee.service.impl.KingDeeInventoryProfitServiceImpl;
import com.vedeng.erp.kingdee.service.impl.KingDeeProfitLossServiceImpl;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购入库验收单推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchOtherInAcceptanceFormProcessor extends BaseProcessor<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto> {

    public static final String ZERO = "0";

    @Value("${oss_http}")
    private String ossHttp;

    @Autowired
    private BatchAttachmentDtoMapper batchAttachmentDtoMapper;
    @Autowired
    private KingDeeStorageOutService kingDeeStorageOutService;
    @Autowired
    private KingDeeStorageInService kingDeeStorageInService;
    @Autowired
    private KingDeeAllocationServiceImpl kingDeeAllocationApiService;
    @Autowired
    private KingDeeProfitLossServiceImpl kingDeeProfitLossApiService;
    @Autowired
    private KingDeeInventoryProfitServiceImpl kingDeeInventoryProfitApiService;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    //盘盈单
    //盘亏单
    //样品出库
    //报废出库
    //领用出库
    //外借出库
    //外借入库
    //库存转换
    @Override
    public KingDeeFileDataDto doProcess(BatchWarehouseGoodsOutInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("其他出入库单数据：{}", JSON.toJSONString(dto));
        // 出 18,10,13,14,16,19
        // 入 9,12,20
        Integer outInType = dto.getOutInType();
        Integer attachmentFunction = getAttachmentFunction(outInType);

        BatchAttachmentDto query = BatchAttachmentDto.builder()
                .attachmentType(462)
                .attachmentFunction(attachmentFunction)
                .relatedId(dto.getWarehouseGoodsOutInId().intValue())
                .build();
        BatchAttachmentDto batchAttachmentDto = batchAttachmentDtoMapper.purchaseInfindByQuery(query);
        log.info("上传其他出入库单附件入参对象：{}", JSON.toJSONString(batchAttachmentDto));
        if (Objects.isNull(batchAttachmentDto) || StrUtil.isEmpty(batchAttachmentDto.getUri()) || StrUtil.isEmpty(batchAttachmentDto.getDomain())) {
            log.info("暂无其他出入库单附件: {}", JSON.toJSONString(dto));
            return null;
        }

        String fileUrl = ossHttp + batchAttachmentDto.getDomain() + batchAttachmentDto.getUri();

        KingDeeFileDataDto fileDataDto = KingDeeFileDataDto.builder()
                .formId(WarehouseTypeAndFormIdEnum.getFormIdByType(dto.getOutInType()))
                .erpId(dto.getWarehouseGoodsOutInId().toString())
                .url(fileUrl)
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(fileDataDto);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(fileDataDto));
            return null;
        }

        String kingDeeFid = getKingDeeFid(dto);
        if (StrUtil.isEmpty(kingDeeFid)) {
            return null;
        }

        String formIdByType = WarehouseTypeAndFormIdEnum.getFormIdByType(outInType);
        FileInfoDto base64FromUrl = FileInfoUtils.getBase64FromUrl(fileUrl);
        String name = StrUtil.isEmpty(batchAttachmentDto.getName()) ? "file" + base64FromUrl.getSuffix() : batchAttachmentDto.getName() + base64FromUrl.getSuffix();
        return KingDeeFileDataDto.builder()
                .fileName(name)
                .aliasFileName(batchAttachmentDto.getName())
                .billNo(dto.getOutInNo())
                .formId(formIdByType)
                .isLast(true)
                .fId(kingDeeFid)
                .url(fileUrl)
                .erpId(dto.getWarehouseGoodsOutInId().toString())
                .businessId(formIdByType + dto.getWarehouseGoodsOutInId().toString())
                .build();
    }

    private Integer getAttachmentFunction( Integer outInType) {
        if (outInType.equals(9)|| outInType.equals(12)||outInType.equals(20)) {
            return 4211;
        }
        if (outInType.equals(18)|| outInType.equals(10)|| outInType.equals(13)|| outInType.equals(14)|| outInType.equals(16)||outInType.equals(19)) {
            return 4213;
        }
        return null;
    }

    private String getKingDeeFid(BatchWarehouseGoodsOutInDto dto) {

        if (WarehouseTypeAndFormIdEnum.LEND_WAREHOUSE_OUT.getOutInType().equals(dto.getOutInType())||
                WarehouseTypeAndFormIdEnum.LENDOUT_WAREHOUSE_IN.getOutInType().equals(dto.getOutInType())) {
            KingDeeAllocationDto query = new KingDeeAllocationDto();
            query.setFBillNo(dto.getOutInNo());
            kingDeeAllocationApiService.query(query);
            if (query.getFId() == null || ZERO.equals(query.getFId())) {
                log.info("上传出入库单附件,原始出入库单未推送金蝶：{}", dto.getOutInNo());
                return null;
            }
            return query.getFId();
        }
        if (WarehouseTypeAndFormIdEnum.SCRAP_WAREHOUSE_OUT.getOutInType().equals(dto.getOutInType())||
                WarehouseTypeAndFormIdEnum.RECEIVE_WAREHOUSE_OUT.getOutInType().equals(dto.getOutInType())||
                WarehouseTypeAndFormIdEnum.SAMPLE_WAREHOUSE_OUT.getOutInType().equals(dto.getOutInType())) {
            KingDeeStorageOutDto query = new KingDeeStorageOutDto();
            query.setFBillNo(dto.getOutInNo());
            kingDeeStorageOutService.query(query);
            if (query.getFId() == null || ZERO.equals(query.getFId())) {
                log.info("上传出入库单附件,原始出入库单未推送金蝶：{}", dto.getOutInNo());
            }
            return query.getFId();
        }

        if (WarehouseTypeAndFormIdEnum.UNIT_CONVERSION_IN.getOutInType().equals(dto.getOutInType())||
                WarehouseTypeAndFormIdEnum.UNIT_CONVERSION_OUT.getOutInType().equals(dto.getOutInType())) {
            KingDeeStorageInDto query = new KingDeeStorageInDto();
            query.setFBillNo(dto.getOutInNo());
            kingDeeStorageInService.query(query);
            if (query.getFId() == null || ZERO.equals(query.getFId())) {
                log.info("上传出入库单附件,原始出入库单未推送金蝶：{}", dto.getOutInNo());
            }
            return query.getFId();
        }

        if (WarehouseTypeAndFormIdEnum.INVENTORY_LOSS_WAREHOUSE_OUT.getOutInType().equals(dto.getOutInType())) {
            KingDeeProfitLossDto query = new KingDeeProfitLossDto();
            query.setFBillNo(dto.getOutInNo());
            kingDeeProfitLossApiService.query(query);
            if (query.getFId() == null || ZERO.equals(query.getFId())) {
                log.info("上传出入库单附件,原始出入库单未推送金蝶：{}", dto.getOutInNo());
                return null;
            }
            return query.getFId();
        }

        if (WarehouseTypeAndFormIdEnum.DISK_SURPLUS_IN.getOutInType().equals(dto.getOutInType())) {
            KingDeeInventoryProfitDto query = new KingDeeInventoryProfitDto();
            query.setFBillNo(dto.getOutInNo());
            kingDeeInventoryProfitApiService.query(query);
            if (query.getFId() == null || ZERO.equals(query.getFId())) {
                log.info("上传出入库单附件,原始出入库单未推送金蝶：{}", dto.getOutInNo());
                return null;
            }
            return query.getFId();
        }

        return null;
    }
}
