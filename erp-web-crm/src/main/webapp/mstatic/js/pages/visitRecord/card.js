void function () {
    new Vue({
        el: '#page-container',
        data: {
            traderName: '',
            longitude: '',
            latitude: '',
            preLongitude: '',
            preLatitude: '',
            cardTime: '',
            picList: [],
            id: '',
            userId: '',
            needPic: false,
            isloading: true,
            isLocationloading: false,
            isLocationError: false
        },
        created() {
            GLOBAL.showGlobalLoading();
            let _this = this;
            GLOBAL.wxregister(['chooseImage', 'uploadImage', 'previewImage', 'getLocation', 'openLocation', 'onHistoryBack'], () => {
                ww.onHistoryBack(function() {
                    _this.$dialog({
                        type: 'warn',
                        message: '未提交的内容将会丢失，确定离开此页吗？',
                        buttons: [{
                            txt: '离开',
                            callback() {
                                window.history.go(-1)
                            }
                        }, {
                            txt: '留在此页',
                        }]
                    })

                    return false;
                })
            });
            this.getCardInfo();
        },
        mounted() {
            
        },
        methods: {
            getCardInfo() {
                this.id = GLOBAL.getQuery('id');
                this.userId = document.querySelector('#golbal_userId').value;

                this.$axios.get('/crm/visitrecord/m/detail?id=' + this.id).then(({data}) => {
                    if(data.success) {
                        let cardList = data.data.cardList;
                        let isCardFlag = false;

                        if(data.data.visitorId == this.userId) {
                            this.needPic = true;
                        }

                        this.traderName = data.data.customerName;

                        if(cardList && cardList.length) {
                            cardList.forEach(item => {
                                if(item.visitUserId == this.userId) {
                                    this.picList = item.cardPhotoUrls ? item.cardPhotoUrls.split(',') : [];
                                   
                                    if(item.cardLocation) {
                                        this.preLongitude = item.cardLocation.split(',')[0];
                                        this.preLatitude = item.cardLocation.split(',')[1];
                                        this.cardTime = item.cardTime;
                                        isCardFlag = true;
                                    }
                                }
                            });
                        }

                        if(!isCardFlag) {
                            this.getLocation();
                        }
                    }

                    GLOBAL.hideGlobalLoading();
                    this.isloading = false;
                })
            },
            async getLocation() {
                if(this.isLocationloading) {
                    return;
                }

                let _this = this;

                this.isLocationloading = true;
                let locationInfo = await ww.getLocation({
                    type: 'gcj02',
                    success: function (data) {
                        console.log('success')
                        console.log(data)
                        _this.isLocationloading = false;
                    },
                    fail: function (data) {
                        console.log('fail')
                        console.log(data)
                        if(_this.preLatitude || _this.latitude) {
                            _this.$message.error('获取当前位置失败')
                        } else {
                            _this.isLocationError = true;
                        }
                        _this.isLocationloading = false;
                    },
                    cancel: function(data) {
                        console.log('cancel')
                        if(_this.preLatitude || _this.latitude) {
                            _this.$message.error('获取当前位置失败')
                        } else {
                            _this.isLocationError = true;
                        }
                        _this.isLocationloading = false;
                    },
                    complete: function(data) {
                        console.log('complete')
                        _this.isLocationloading = false;
                    }
                })

                this.isLocationloading = false;

                if (locationInfo.longitude && locationInfo.latitude) {
                    if(!this.longitude) {
                        this.$message.success('获取当前位置成功')
                    }
                    this.longitude = locationInfo.longitude
                    this.latitude = locationInfo.latitude
                }
                
            },
            cancelUpdate() {
                this.longitude = '';
                this.latitude = '';
            },
            showLocation() {
                ww.openLocation({
                    longitude: this.longitude,
                    latitude: this.latitude,
                    scale: 16
                })
            },
            handlerUploadChange(list) {
                this.picList = list;
            },
            submitCard() {
                if(!this.longitude && !this.preLongitude) {
                    this.$message.error('请获取定位');
                    return;
                }

                if(this.needPic && !this.picList.length) {
                    this.$message.error('请上传照片');
                    return;
                }

                this.$axios.post('/crm/visitrecord/m/card', {
                    recordId: this.id,
                    visitUserId: this.userId,
                    cardPhotoUrls: this.picList.join(','),
                    cardLocation: (this.longitude || this.preLongitude) + ',' + (this.latitude || this.preLatitude)
                }).then(({data}) => {
                    if(data.success) {
                        this.$message.success('打卡成功');
                        setTimeout(() => {
                            window.localStorage.setItem('crm_visit_detail_refresh', '1')
                            window.history.go(-1);
                        }, 1000)
                    } else {
                        this.$message.error(data.message);
                    }
                })
            }
        }
    })
}.call(this);