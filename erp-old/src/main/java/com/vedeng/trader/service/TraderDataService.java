package com.vedeng.trader.service;

import com.vedeng.trader.model.TraderBussinessData;
import com.vedeng.trader.model.TraderPeriodData;

import java.math.BigDecimal;

/**
 * @Author: Cherny.chen
 * @Create: 2021/6/10 18:58
 */
public interface TraderDataService {

    /**
     * <b>Description:</b><br> 获取账期信息
     *
     * @param traderId
     * @param traderType
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月12日 上午10:26:21
     */
    TraderPeriodData getTraderPeriodData(Integer traderId, Integer traderType);


    /**
     * <b>Description:</b><br> 账期占用额度
     *
     * @param traderId
     * @param traderType
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月12日 上午10:03:55
     */
    BigDecimal getPeriodAmountOccupy(Integer traderId, Integer traderType);

    /**
     * <b>Description:</b><br> 账期使用额度
     *
     * @param traderId
     * @param traderType
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月12日 上午10:04:08
     */
    BigDecimal getPeriodAmountUsed(Integer traderId, Integer traderType);

    /**
     * <b>Description:</b><br> 账期使用次数
     * @param traderId
     * @param traderType
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月12日 上午10:04:49
     */
    Integer getPeriodAmountUsedTimes(Integer traderId,Integer traderType);

    /**
     * <b>Description:</b><br> 获取交易信息
     * @param traderId
     * @param traderType
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月12日 上午10:25:55
     */
    TraderBussinessData getTraderBussinessData(Integer traderId,Integer traderType);

}
