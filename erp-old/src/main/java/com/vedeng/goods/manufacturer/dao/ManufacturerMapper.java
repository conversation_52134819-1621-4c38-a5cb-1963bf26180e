package com.vedeng.goods.manufacturer.dao;
import java.util.Collection;

import com.vedeng.authorization.model.User;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.manufacturer.model.ManufacturerExample;
import com.vedeng.system.model.Attachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ManufacturerMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 13:56:59 CST 2021
     */
    int insertSelective(Manufacturer record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 13:56:59 CST 2021
     */
    List<Manufacturer> selectByExample(ManufacturerExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 13:56:59 CST 2021
     */
    Manufacturer selectByPrimaryKey(Integer manufacturerId);
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 13:56:59 CST 2021
     */
    int updateByPrimaryKeySelective(Manufacturer record);

    int updateByPrimaryKeySelectiveTimeChange(Manufacturer record);

    List<User> findUserName();

    List<Manufacturer> getProductCompanyInfoListPage(Map<String, Object> paramMap);

    List<String> getManufacturerVoInfoByStr(Map<String, Object> paramMap);

    void insertAttachement(Attachment attachment);

    List<Manufacturer> IsUpLoad(Map<String, Object> paramMap);

    void updateIsUpLoad(Manufacturer manufacturer);

    List<Manufacturer> findIsNeedDel(Map<String, Object> paramMap);

    Integer deleteManufacturer(Map<String, Object> paramMap);

    int isValidByRegistrationNumberId(@Param("registrationNumberId") Integer registrationNumberId, @Param("status") Integer status);

    Manufacturer getOneByName(@Param("productNameToUse") String productNameToUse);

    List<Manufacturer> getByRelateId(@Param("relateId")Integer relateId);

    int updateByManufacturerId(@Param("relateId")Integer relateId,@Param("manufacturerId")Integer manufacturerId);

    List<Manufacturer> getByManufacturerNameIn(@Param("manufacturerNameCollection")Collection<String> manufacturerNameCollection);


    /**
     * 批量更新
     *
     * @param list list
     * @return update count
     */
    int updateBatchNameSelective(List<Manufacturer> list);

}