package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 推送金蝶银行流水dto
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchBankBillDto extends BatchBaseDto {
    /**
     * 流水主键id
     */
    private Integer bankBillId;
    /**
     * 银行标示1建设银行2南京银行3中国银行4支付宝5微信6交通银行
     */
    private Integer bankTag;
    /**
     * 流水号
     */
    private String tranFlow;
    /**
     * 单据日期
     */
    private Date tranDate;
    /**
     * 交易时间
     */
    private Date tranTime;
    /**
     * 真实交易日期时间
     */
    private Date realTrandatetime;
    /**
     * 发生额
     */
    private BigDecimal amt;
    /**
     * 是否忽略0否1是
     */
    private Integer status;
    /**
     * 已匹配项目字典id
     */
    private Integer matchedObject;
    /**
     * 忽略原因-字典title
     */
    private String ignoreReason;
    /**
     * 借贷标志(0-借 转出 ,1-贷 转入)
     */
    private Integer flag1;
    /**
     * 回单url地址
     */
    private String receiptUrl;
    /**
     * 0未推送 1已推送
     */
    private Integer havePushedKingdee;
    /**
     * 推送金蝶流水开始计算时间
     */
    private Date beginTime;
    /**
     * 推送金蝶流水结束计算时间
     */
    private Date endTime;
    /**
     * 需要推送的TRADER_NAME集合
     */
    private List<String> needPushTraderNameList;
    /**
     * 资金流水开始时间
     */
    private Long capitalBillBeginTime;
    /**
     * 资金流水结束时间
     */
    private Long capitalBillEndTime;
    /**
     * 已匹配金额
     */
    private BigDecimal matchedAmount;
    /**
     * 对方账户名称
     */
    private String accName1;
    /**
     * 流水回单下载名称
     */
    private String receiptName;
    /**
     * 业务类型
     */
    private Integer bussinessType;
    /**
     * 单号
     */
    private String orderNo;
    /**
     * 交易主体
     */
    private Integer traderSubject;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 对方账户开户行名称
     */
    private String cadBankNm;
    /**
     * 对方账号
     */
    private String accno2;
    /**
     * 对方行号
     */
    private String accBankNo;
    /**
     * 客户Id
     */
    private Integer traderId;

    /**
     * 财务添加的用于匹配的流水号
     */
    private String capitalSearchFlow;
    /**
     * 资金流水主键id
     */
    private Integer capitalBillId;
    /**
     * 资金流水总额
     */
    private BigDecimal amount;

    /**
     * 往来单位
     */
    private String contactUnit;

    /**
     * T_CAPITAL_BILL_DETAIL 关联的erp业务订单id
     */
    private Integer relatedId;

    /**
     * 结算方式 (1.现金结算 2.银行转账 3.银行承兑汇票 4.商业承兑汇票 5.微信 6.支付宝)
     */
    private Integer settlementMethod;
}
