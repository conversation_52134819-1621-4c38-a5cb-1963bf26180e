package com.vedeng.erp.settlement.enums;

/**
 * 流水业务类型
 */
public enum CapitalBusinessTypeEnum {
    ORDER_PAYMENT(525, "订单付款"),
    ORDER_RECEIPT(526, "订单收款"),
    ORDER_REFUND(531, "退款"),
    CREDIT_REMOVE(532, "资金转移"),
    CREDIT_PAYMENT(533, "信用还款"),
    WITHDRAW_FOR(679, "对私提现");

    private Integer code;

    private String desc;


    CapitalBusinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
