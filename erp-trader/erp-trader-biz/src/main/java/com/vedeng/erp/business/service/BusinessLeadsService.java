package com.vedeng.erp.business.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.erp.business.domain.dto.AssignLeadsDto;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadMergeDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.domain.entity.BusinessLeadsEntity;

import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.system.dto.UserDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import static com.vedeng.common.core.domain.CurrentUser.getCurrentUser;

/**
 * <AUTHOR>
 */
public interface BusinessLeadsService {



    List<UserDto> findAllCreateUser(String name);

    List<UserDto> findAllBelongUser(String name);

    List<UserDto> findAllShareUser(String name);

    boolean checkUserIsZongji(Integer userId);

    /**
     * 分页查询
     *
     * @param businessLeadsDto businessLeadsDto
     * @return
     */
    PageInfo<BusinessLeadsDto> page(PageParam<BusinessLeadsDto> businessLeadsDto);


    /**
     * 新增
     *
     * @param businessLeadsDto businessLeadsDto
     * @return leadsId
     */
    Integer add(BusinessLeadsDto businessLeadsDto);


    /**
     * 合并线索
     * @param leadsNo
     */
    boolean mergeLeads(String leadsNo);


    String getCloseReasonTypeName(Integer closeReasonType);
    /**
     * 查询线索
     *
     * @param id id
     * @return BusinessLeadsDto
     */
    BusinessLeadsDto getOne(Integer id);

    /**
     * 关闭线索
     *
     * @param businessLeadsDto 参数对象
     */
    void closedLeads(BusinessLeadsDto businessLeadsDto);

    /**
     * 导入线索
     *
     * @param file file
     */
    List<Integer> importExcel(MultipartFile file) throws IOException;

    /**
     * 更新线索
     *
     * @param businessLeadsDto
     */
    void update(BusinessLeadsDto businessLeadsDto);

    /**
     * 分配线索
     *
     * @param assignLeadsDto assignLeadsDto
     */
    void assign(AssignLeadsDto assignLeadsDto);

    /**
     * 根据线索Id返回商机Dto
     * @param id
     * @return
     */
    BusinessChanceDto getLeadsToChance(Integer id);

    List<BusinessLeadMergeDto> getLeadMerge(String leadNo);

    /**
     * 查询线索集合
     * @param businessLeadsDto 参数
     * @return list
     */
    List<BusinessLeadsDto> getLeadsListByDto(BusinessLeadsDto businessLeadsDto);


    public List<BusinessLeadsDto> getLeadsListByDtoToday(BusinessLeadsDto businessLeadsDto);


    void updateLeadsFollowStatusAndFirstFollowTime(Integer id);

    /**
     * 更新跟进状态已商机
     *
     * @param leadsId id
     * @param businessChanceId id
     */
    void updateLeadsFollowStatusOpportunity(Integer leadsId, Integer businessChanceId);

    /**
     * 更新线索分级
     * @param businessLeadsDto
     */
    void updateLeadsStatus(BusinessLeadsDto businessLeadsDto);

    /**
     * 新建线索埋点
     * @param businessLeadsEntity
     * @param eventTrackingEnum
     * @param addTime 追溯使用
     */
    void track(BusinessLeadsEntity businessLeadsEntity,EventTrackingEnum eventTrackingEnum,Date addTime);

    /**
     * 根据条件，查询线索
     * @param bussinessChanceId 商机ID
     * @return
     */
    List<BusinessLeadsDto> getLeadsListByParams(Integer bussinessChanceId);

    /**
     * 根据商机ID查询线索
     * @param relatedId
     */
    BusinessLeadsDto findByBusinessChanceId(Integer relatedId);

    /**
     * 根据主键id查询线索
     * @param relatedId
     * @return
     */
    BusinessLeadsDto findById(Integer relatedId);

    BusinessLeadsDto findByLeadsNo(String leadsNo);

}
