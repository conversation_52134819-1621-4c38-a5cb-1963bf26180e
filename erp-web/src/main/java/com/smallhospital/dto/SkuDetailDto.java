package com.smallhospital.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/12/25 17:19
 */
public class SkuDetailDto extends ELSkuDetaiDto{
    private String price;

    private Integer categoryId;

    private String model;

    private String genericName;

    private String remark;

    private List<String> productPic;

    private String approvalNumber;

    private String productionAddress;

    private String registrationCertificateProductName;

    private Integer validityDate;

    private String validityDateStart;

    private String validityDateEnd;

    private List<String> crtificatePicAddress;

    private String documentType;

    private String companyName;

    private String companyAbbr;

    private String packageUnit;

    private String uniformCreditCode;

    private String enterpriseLicenseRecordNumber;

    private String registeredAddress;

    private String manufacturerId;

    private List<String> manufacturerPicUrls;

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getGenericName() {
        return genericName;
    }

    public void setGenericName(String genericName) {
        this.genericName = genericName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getProductPic() {
        return productPic;
    }

    public void setProductPic(List<String> productPic) {
        this.productPic = productPic;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getProductionAddress() {
        return productionAddress;
    }

    public void setProductionAddress(String productionAddress) {
        this.productionAddress = productionAddress;
    }

    public String getRegistrationCertificateProductName() {
        return registrationCertificateProductName;
    }

    public void setRegistrationCertificateProductName(String registrationCertificateProductName) {
        this.registrationCertificateProductName = registrationCertificateProductName;
    }

    public Integer getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Integer validityDate) {
        this.validityDate = validityDate;
    }

    public String getValidityDateStart() {
        return validityDateStart;
    }

    public void setValidityDateStart(String validityDateStart) {
        this.validityDateStart = validityDateStart;
    }

    public String getValidityDateEnd() {
        return validityDateEnd;
    }

    public void setValidityDateEnd(String validityDateEnd) {
        this.validityDateEnd = validityDateEnd;
    }

    public List<String> getCrtificatePicAddress() {
        return crtificatePicAddress;
    }

    public void setCrtificatePicAddress(List<String> crtificatePicAddress) {
        this.crtificatePicAddress = crtificatePicAddress;
    }

    public String getDocumentType() {
        return documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAbbr() {
        return companyAbbr;
    }

    public void setCompanyAbbr(String companyAbbr) {
        this.companyAbbr = companyAbbr;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    public String getUniformCreditCode() {
        return uniformCreditCode;
    }

    public void setUniformCreditCode(String uniformCreditCode) {
        this.uniformCreditCode = uniformCreditCode;
    }

    public String getEnterpriseLicenseRecordNumber() {
        return enterpriseLicenseRecordNumber;
    }

    public void setEnterpriseLicenseRecordNumber(String enterpriseLicenseRecordNumber) {
        this.enterpriseLicenseRecordNumber = enterpriseLicenseRecordNumber;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public List<String> getManufacturerPicUrls() {
        return manufacturerPicUrls;
    }

    public void setManufacturerPicUrls(List<String> manufacturerPicUrls) {
        this.manufacturerPicUrls = manufacturerPicUrls;
    }
}
