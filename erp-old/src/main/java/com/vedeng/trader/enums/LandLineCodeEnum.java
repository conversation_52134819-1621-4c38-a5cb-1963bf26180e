package com.vedeng.trader.enums;

/**
 * 主叫线路枚举
 *
 * <AUTHOR>
 */
public enum LandLineCodeEnum {

    /**
     * 老客户专线
     */
    CUSTOMER_LINE(0, "老客户专线"),
    /**
     * 联通线路
     */
    UNICOM_LINE(1, "联通线路"),

    /**
     * 移动线路
     */
    MOBILE_LINE(2, "移动线路"),

    /**
     * 电信线路
     */
    TELECOM_LINE(3, "电信线路"),

    /**
     * 号码池
     */
    NUMBER_POOL(4, "号码池");

    /**
     * 线路代码
     */
    private Integer lineCode;

    /**
     * 线路描述
     */
    private String lineDesc;

    LandLineCodeEnum(Integer lineCode, String lineDesc) {
        this.lineCode = lineCode;
        this.lineDesc = lineDesc;
    }

    public Integer getLineCode() {
        return lineCode;
    }

    public String getLineDesc() {
        return lineDesc;
    }
}
