<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.ordergoods.dao.SaleorderGoodsGenerateMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.ordergoods.model.SaleorderGoodsGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    <id column="SALEORDER_GOODS_ID" property="saleorderGoodsId" jdbcType="INTEGER" />
    <result column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
    <result column="MODEL" property="model" jdbcType="VARCHAR" />
    <result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="REAL_PRICE" property="realPrice" jdbcType="DECIMAL" />
    <result column="PURCHASING_PRICE" property="purchasingPrice" jdbcType="DECIMAL" />
    <result column="CURRENCY_UNIT_ID" property="currencyUnitId" jdbcType="INTEGER" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="BUY_NUM" property="buyNum" jdbcType="INTEGER" />
    <result column="DELIVERY_NUM" property="deliveryNum" jdbcType="INTEGER" />
    <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="TINYINT" />
    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
    <result column="DELIVERY_CYCLE" property="deliveryCycle" jdbcType="VARCHAR" />
    <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="TINYINT" />
    <result column="DELIVERY_DIRECT_COMMENTS" property="deliveryDirectComments" jdbcType="VARCHAR" />
    <result column="REGISTRATION_NUMBER" property="registrationNumber" jdbcType="VARCHAR" />
    <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR" />
    <result column="REFERENCE_COST_PRICE" property="referenceCostPrice" jdbcType="DECIMAL" />
    <result column="REFERENCE_PRICE" property="referencePrice" jdbcType="VARCHAR" />
    <result column="REFERENCE_DELIVERY_CYCLE" property="referenceDeliveryCycle" jdbcType="VARCHAR" />
    <result column="REPORT_STATUS" property="reportStatus" jdbcType="TINYINT" />
    <result column="REPORT_COMMENTS" property="reportComments" jdbcType="VARCHAR" />
    <result column="HAVE_INSTALLATION" property="haveInstallation" jdbcType="TINYINT" />
    <result column="GOODS_COMMENTS" property="goodsComments" jdbcType="VARCHAR" />
    <result column="INSIDE_COMMENTS" property="insideComments" jdbcType="VARCHAR" />
    <result column="ARRIVAL_USER_ID" property="arrivalUserId" jdbcType="INTEGER" />
    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="TINYINT" />
    <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
    <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT" />
    <result column="IS_IGNORE" property="isIgnore" jdbcType="TINYINT" />
    <result column="IGNORE_TIME" property="ignoreTime" jdbcType="BIGINT" />
    <result column="IGNORE_USER_ID" property="ignoreUserId" jdbcType="INTEGER" />
    <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="TINYINT" />
    <result column="MAX_SKU_REFUND_AMOUNT" property="maxSkuRefundAmount" jdbcType="DECIMAL" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="OCCUPY_NUM" property="occupyNum" jdbcType="INTEGER" />
    <result column="IS_ACTION_GOODS" property="isActionGoods" jdbcType="TINYINT" />
    <result column="ACTION_OCCUPY_NUM" property="actionOccupyNum" jdbcType="INTEGER" />
    <result column="IS_COUPONS" property="isCoupons" jdbcType="TINYINT" />
    <result column="EL_ORDERLIST_ID" property="elOrderlistId" jdbcType="INTEGER" />
    <result column="UPDATE_DATA_TIME" property="updateDataTime" jdbcType="TIMESTAMP" />
    <result column="REAL_PAY_AMOUNT" property="realPayAmount" jdbcType="DECIMAL" />
    <result column="AFTER_RETURN_AMOUNT" property="afterReturnAmount" jdbcType="DECIMAL" />
    <result column="AFTER_RETURN_NUM" property="afterReturnNum" jdbcType="INTEGER" />
    <result column="IS_GIFT" property="isGift" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    SALEORDER_GOODS_ID, SALEORDER_ID, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME, MODEL, UNIT_NAME, 
    PRICE, REAL_PRICE, PURCHASING_PRICE, CURRENCY_UNIT_ID, NUM, BUY_NUM, DELIVERY_NUM, 
    DELIVERY_STATUS, DELIVERY_TIME, DELIVERY_CYCLE, DELIVERY_DIRECT, DELIVERY_DIRECT_COMMENTS, 
    REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE, REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE, 
    REPORT_STATUS, REPORT_COMMENTS, HAVE_INSTALLATION, GOODS_COMMENTS, INSIDE_COMMENTS, 
    ARRIVAL_USER_ID, ARRIVAL_STATUS, ARRIVAL_TIME, IS_DELETE, IS_IGNORE, IGNORE_TIME, 
    IGNORE_USER_ID, LOCKED_STATUS, MAX_SKU_REFUND_AMOUNT, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, OCCUPY_NUM, IS_ACTION_GOODS, ACTION_OCCUPY_NUM, IS_COUPONS, EL_ORDERLIST_ID, 
    UPDATE_DATA_TIME, REAL_PAY_AMOUNT, AFTER_RETURN_AMOUNT, AFTER_RETURN_NUM, IS_GIFT
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.vedeng.ordergoods.model.SaleorderGoodsGenerateExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_SALEORDER_GOODS
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_SALEORDER_GOODS
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    delete from T_SALEORDER_GOODS
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.ordergoods.model.SaleorderGoodsGenerateExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    delete from T_SALEORDER_GOODS
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.ordergoods.model.SaleorderGoodsGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="saleorderGoodsId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SALEORDER_GOODS (SALEORDER_ID, GOODS_ID, SKU, 
      GOODS_NAME, BRAND_NAME, MODEL, 
      UNIT_NAME, PRICE, REAL_PRICE, 
      PURCHASING_PRICE, CURRENCY_UNIT_ID, NUM, 
      BUY_NUM, DELIVERY_NUM, DELIVERY_STATUS, 
      DELIVERY_TIME, DELIVERY_CYCLE, DELIVERY_DIRECT, 
      DELIVERY_DIRECT_COMMENTS, REGISTRATION_NUMBER, 
      SUPPLIER_NAME, REFERENCE_COST_PRICE, REFERENCE_PRICE, 
      REFERENCE_DELIVERY_CYCLE, REPORT_STATUS, REPORT_COMMENTS, 
      HAVE_INSTALLATION, GOODS_COMMENTS, INSIDE_COMMENTS, 
      ARRIVAL_USER_ID, ARRIVAL_STATUS, ARRIVAL_TIME, 
      IS_DELETE, IS_IGNORE, IGNORE_TIME, 
      IGNORE_USER_ID, LOCKED_STATUS, MAX_SKU_REFUND_AMOUNT, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, OCCUPY_NUM, IS_ACTION_GOODS, 
      ACTION_OCCUPY_NUM, IS_COUPONS, EL_ORDERLIST_ID, 
      UPDATE_DATA_TIME, REAL_PAY_AMOUNT, AFTER_RETURN_AMOUNT, 
      AFTER_RETURN_NUM)
    values (#{saleorderId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{unitName,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{realPrice,jdbcType=DECIMAL}, 
      #{purchasingPrice,jdbcType=DECIMAL}, #{currencyUnitId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER}, 
      #{buyNum,jdbcType=INTEGER}, #{deliveryNum,jdbcType=INTEGER}, #{deliveryStatus,jdbcType=TINYINT}, 
      #{deliveryTime,jdbcType=BIGINT}, #{deliveryCycle,jdbcType=VARCHAR}, #{deliveryDirect,jdbcType=TINYINT}, 
      #{deliveryDirectComments,jdbcType=VARCHAR}, #{registrationNumber,jdbcType=VARCHAR}, 
      #{supplierName,jdbcType=VARCHAR}, #{referenceCostPrice,jdbcType=DECIMAL}, #{referencePrice,jdbcType=DECIMAL}, 
      #{referenceDeliveryCycle,jdbcType=VARCHAR}, #{reportStatus,jdbcType=TINYINT}, #{reportComments,jdbcType=VARCHAR}, 
      #{haveInstallation,jdbcType=TINYINT}, #{goodsComments,jdbcType=VARCHAR}, #{insideComments,jdbcType=VARCHAR}, 
      #{arrivalUserId,jdbcType=INTEGER}, #{arrivalStatus,jdbcType=TINYINT}, #{arrivalTime,jdbcType=BIGINT}, 
      #{isDelete,jdbcType=TINYINT}, #{isIgnore,jdbcType=TINYINT}, #{ignoreTime,jdbcType=BIGINT}, 
      #{ignoreUserId,jdbcType=INTEGER}, #{lockedStatus,jdbcType=TINYINT}, #{maxSkuRefundAmount,jdbcType=DECIMAL}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{occupyNum,jdbcType=INTEGER}, #{isActionGoods,jdbcType=TINYINT}, 
      #{actionOccupyNum,jdbcType=INTEGER}, #{isCoupons,jdbcType=TINYINT}, #{elOrderlistId,jdbcType=INTEGER}, 
      #{updateDataTime,jdbcType=TIMESTAMP}, #{realPayAmount,jdbcType=DECIMAL}, #{afterReturnAmount,jdbcType=DECIMAL}, 
      #{afterReturnNum,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.ordergoods.model.SaleorderGoodsGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="saleorderGoodsId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SALEORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="saleorderId != null" >
        SALEORDER_ID,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="sku != null" >
        SKU,
      </if>
      <if test="goodsName != null" >
        GOODS_NAME,
      </if>
      <if test="brandName != null" >
        BRAND_NAME,
      </if>
      <if test="model != null" >
        MODEL,
      </if>
      <if test="unitName != null" >
        UNIT_NAME,
      </if>
      <if test="price != null" >
        PRICE,
      </if>
      <if test="realPrice != null" >
        REAL_PRICE,
      </if>
      <if test="purchasingPrice != null" >
        PURCHASING_PRICE,
      </if>
      <if test="currencyUnitId != null" >
        CURRENCY_UNIT_ID,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="buyNum != null" >
        BUY_NUM,
      </if>
      <if test="deliveryNum != null" >
        DELIVERY_NUM,
      </if>
      <if test="deliveryStatus != null" >
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null" >
        DELIVERY_TIME,
      </if>
      <if test="deliveryCycle != null" >
        DELIVERY_CYCLE,
      </if>
      <if test="deliveryDirect != null" >
        DELIVERY_DIRECT,
      </if>
      <if test="deliveryDirectComments != null" >
        DELIVERY_DIRECT_COMMENTS,
      </if>
      <if test="registrationNumber != null" >
        REGISTRATION_NUMBER,
      </if>
      <if test="supplierName != null" >
        SUPPLIER_NAME,
      </if>
      <if test="referenceCostPrice != null" >
        REFERENCE_COST_PRICE,
      </if>
      <if test="referencePrice != null" >
        REFERENCE_PRICE,
      </if>
      <if test="referenceDeliveryCycle != null" >
        REFERENCE_DELIVERY_CYCLE,
      </if>
      <if test="reportStatus != null" >
        REPORT_STATUS,
      </if>
      <if test="reportComments != null" >
        REPORT_COMMENTS,
      </if>
      <if test="haveInstallation != null" >
        HAVE_INSTALLATION,
      </if>
      <if test="goodsComments != null" >
        GOODS_COMMENTS,
      </if>
      <if test="insideComments != null" >
        INSIDE_COMMENTS,
      </if>
      <if test="arrivalUserId != null" >
        ARRIVAL_USER_ID,
      </if>
      <if test="arrivalStatus != null" >
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null" >
        ARRIVAL_TIME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="isIgnore != null" >
        IS_IGNORE,
      </if>
      <if test="ignoreTime != null" >
        IGNORE_TIME,
      </if>
      <if test="ignoreUserId != null" >
        IGNORE_USER_ID,
      </if>
      <if test="lockedStatus != null" >
        LOCKED_STATUS,
      </if>
      <if test="maxSkuRefundAmount != null" >
        MAX_SKU_REFUND_AMOUNT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="occupyNum != null" >
        OCCUPY_NUM,
      </if>
      <if test="isActionGoods != null" >
        IS_ACTION_GOODS,
      </if>
      <if test="actionOccupyNum != null" >
        ACTION_OCCUPY_NUM,
      </if>
      <if test="isCoupons != null" >
        IS_COUPONS,
      </if>
      <if test="elOrderlistId != null" >
        EL_ORDERLIST_ID,
      </if>
      <if test="updateDataTime != null" >
        UPDATE_DATA_TIME,
      </if>
      <if test="realPayAmount != null" >
        REAL_PAY_AMOUNT,
      </if>
      <if test="afterReturnAmount != null" >
        AFTER_RETURN_AMOUNT,
      </if>
      <if test="afterReturnNum != null" >
        AFTER_RETURN_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="saleorderId != null" >
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null" >
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null" >
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null" >
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="realPrice != null" >
        #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasingPrice != null" >
        #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null" >
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
      <if test="buyNum != null" >
        #{buyNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null" >
        #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null" >
        #{deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveryTime != null" >
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryCycle != null" >
        #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null" >
        #{deliveryDirect,jdbcType=TINYINT},
      </if>
      <if test="deliveryDirectComments != null" >
        #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null" >
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null" >
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null" >
        #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null" >
        #{referencePrice,jdbcType=DECIMAL},
      </if>
      <if test="referenceDeliveryCycle != null" >
        #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null" >
        #{reportStatus,jdbcType=TINYINT},
      </if>
      <if test="reportComments != null" >
        #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null" >
        #{haveInstallation,jdbcType=TINYINT},
      </if>
      <if test="goodsComments != null" >
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null" >
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserId != null" >
        #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null" >
        #{arrivalStatus,jdbcType=TINYINT},
      </if>
      <if test="arrivalTime != null" >
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isIgnore != null" >
        #{isIgnore,jdbcType=TINYINT},
      </if>
      <if test="ignoreTime != null" >
        #{ignoreTime,jdbcType=BIGINT},
      </if>
      <if test="ignoreUserId != null" >
        #{ignoreUserId,jdbcType=INTEGER},
      </if>
      <if test="lockedStatus != null" >
        #{lockedStatus,jdbcType=TINYINT},
      </if>
      <if test="maxSkuRefundAmount != null" >
        #{maxSkuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null" >
        #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="isActionGoods != null" >
        #{isActionGoods,jdbcType=TINYINT},
      </if>
      <if test="actionOccupyNum != null" >
        #{actionOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null" >
        #{isCoupons,jdbcType=TINYINT},
      </if>
      <if test="elOrderlistId != null" >
        #{elOrderlistId,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null" >
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realPayAmount != null" >
        #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnAmount != null" >
        #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null" >
        #{afterReturnNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.ordergoods.model.SaleorderGoodsGenerateExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    select count(*) from T_SALEORDER_GOODS
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    update T_SALEORDER_GOODS
    <set >
      <if test="record.saleorderGoodsId != null" >
        SALEORDER_GOODS_ID = #{record.saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="record.saleorderId != null" >
        SALEORDER_ID = #{record.saleorderId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsId != null" >
        GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.sku != null" >
        SKU = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null" >
        GOODS_NAME = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null" >
        BRAND_NAME = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null" >
        MODEL = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.unitName != null" >
        UNIT_NAME = #{record.unitName,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null" >
        PRICE = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.realPrice != null" >
        REAL_PRICE = #{record.realPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.purchasingPrice != null" >
        PURCHASING_PRICE = #{record.purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.currencyUnitId != null" >
        CURRENCY_UNIT_ID = #{record.currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="record.num != null" >
        NUM = #{record.num,jdbcType=INTEGER},
      </if>
      <if test="record.buyNum != null" >
        BUY_NUM = #{record.buyNum,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryNum != null" >
        DELIVERY_NUM = #{record.deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryStatus != null" >
        DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryTime != null" >
        DELIVERY_TIME = #{record.deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryCycle != null" >
        DELIVERY_CYCLE = #{record.deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryDirect != null" >
        DELIVERY_DIRECT = #{record.deliveryDirect,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryDirectComments != null" >
        DELIVERY_DIRECT_COMMENTS = #{record.deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="record.registrationNumber != null" >
        REGISTRATION_NUMBER = #{record.registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null" >
        SUPPLIER_NAME = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.referenceCostPrice != null" >
        REFERENCE_COST_PRICE = #{record.referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.referencePrice != null" >
        REFERENCE_PRICE = #{record.referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="record.referenceDeliveryCycle != null" >
        REFERENCE_DELIVERY_CYCLE = #{record.referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="record.reportStatus != null" >
        REPORT_STATUS = #{record.reportStatus,jdbcType=TINYINT},
      </if>
      <if test="record.reportComments != null" >
        REPORT_COMMENTS = #{record.reportComments,jdbcType=VARCHAR},
      </if>
      <if test="record.haveInstallation != null" >
        HAVE_INSTALLATION = #{record.haveInstallation,jdbcType=TINYINT},
      </if>
      <if test="record.goodsComments != null" >
        GOODS_COMMENTS = #{record.goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="record.insideComments != null" >
        INSIDE_COMMENTS = #{record.insideComments,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalUserId != null" >
        ARRIVAL_USER_ID = #{record.arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="record.arrivalStatus != null" >
        ARRIVAL_STATUS = #{record.arrivalStatus,jdbcType=TINYINT},
      </if>
      <if test="record.arrivalTime != null" >
        ARRIVAL_TIME = #{record.arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="record.isDelete != null" >
        IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      </if>
      <if test="record.isIgnore != null" >
        IS_IGNORE = #{record.isIgnore,jdbcType=TINYINT},
      </if>
      <if test="record.ignoreTime != null" >
        IGNORE_TIME = #{record.ignoreTime,jdbcType=BIGINT},
      </if>
      <if test="record.ignoreUserId != null" >
        IGNORE_USER_ID = #{record.ignoreUserId,jdbcType=INTEGER},
      </if>
      <if test="record.lockedStatus != null" >
        LOCKED_STATUS = #{record.lockedStatus,jdbcType=TINYINT},
      </if>
      <if test="record.maxSkuRefundAmount != null" >
        MAX_SKU_REFUND_AMOUNT = #{record.maxSkuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.addTime != null" >
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null" >
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null" >
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.updater != null" >
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.occupyNum != null" >
        OCCUPY_NUM = #{record.occupyNum,jdbcType=INTEGER},
      </if>
      <if test="record.isActionGoods != null" >
        IS_ACTION_GOODS = #{record.isActionGoods,jdbcType=TINYINT},
      </if>
      <if test="record.actionOccupyNum != null" >
        ACTION_OCCUPY_NUM = #{record.actionOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="record.isCoupons != null" >
        IS_COUPONS = #{record.isCoupons,jdbcType=TINYINT},
      </if>
      <if test="record.elOrderlistId != null" >
        EL_ORDERLIST_ID = #{record.elOrderlistId,jdbcType=INTEGER},
      </if>
      <if test="record.updateDataTime != null" >
        UPDATE_DATA_TIME = #{record.updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.realPayAmount != null" >
        REAL_PAY_AMOUNT = #{record.realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.afterReturnAmount != null" >
        AFTER_RETURN_AMOUNT = #{record.afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.afterReturnNum != null" >
        AFTER_RETURN_NUM = #{record.afterReturnNum,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    update T_SALEORDER_GOODS
    set SALEORDER_GOODS_ID = #{record.saleorderGoodsId,jdbcType=INTEGER},
      SALEORDER_ID = #{record.saleorderId,jdbcType=INTEGER},
      GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      SKU = #{record.sku,jdbcType=VARCHAR},
      GOODS_NAME = #{record.goodsName,jdbcType=VARCHAR},
      BRAND_NAME = #{record.brandName,jdbcType=VARCHAR},
      MODEL = #{record.model,jdbcType=VARCHAR},
      UNIT_NAME = #{record.unitName,jdbcType=VARCHAR},
      PRICE = #{record.price,jdbcType=DECIMAL},
      REAL_PRICE = #{record.realPrice,jdbcType=DECIMAL},
      PURCHASING_PRICE = #{record.purchasingPrice,jdbcType=DECIMAL},
      CURRENCY_UNIT_ID = #{record.currencyUnitId,jdbcType=INTEGER},
      NUM = #{record.num,jdbcType=INTEGER},
      BUY_NUM = #{record.buyNum,jdbcType=INTEGER},
      DELIVERY_NUM = #{record.deliveryNum,jdbcType=INTEGER},
      DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
      DELIVERY_TIME = #{record.deliveryTime,jdbcType=BIGINT},
      DELIVERY_CYCLE = #{record.deliveryCycle,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{record.deliveryDirect,jdbcType=TINYINT},
      DELIVERY_DIRECT_COMMENTS = #{record.deliveryDirectComments,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{record.registrationNumber,jdbcType=VARCHAR},
      SUPPLIER_NAME = #{record.supplierName,jdbcType=VARCHAR},
      REFERENCE_COST_PRICE = #{record.referenceCostPrice,jdbcType=DECIMAL},
      REFERENCE_PRICE = #{record.referencePrice,jdbcType=VARCHAR},
      REFERENCE_DELIVERY_CYCLE = #{record.referenceDeliveryCycle,jdbcType=VARCHAR},
      REPORT_STATUS = #{record.reportStatus,jdbcType=TINYINT},
      REPORT_COMMENTS = #{record.reportComments,jdbcType=VARCHAR},
      HAVE_INSTALLATION = #{record.haveInstallation,jdbcType=TINYINT},
      GOODS_COMMENTS = #{record.goodsComments,jdbcType=VARCHAR},
      INSIDE_COMMENTS = #{record.insideComments,jdbcType=VARCHAR},
      ARRIVAL_USER_ID = #{record.arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{record.arrivalStatus,jdbcType=TINYINT},
      ARRIVAL_TIME = #{record.arrivalTime,jdbcType=BIGINT},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      IS_IGNORE = #{record.isIgnore,jdbcType=TINYINT},
      IGNORE_TIME = #{record.ignoreTime,jdbcType=BIGINT},
      IGNORE_USER_ID = #{record.ignoreUserId,jdbcType=INTEGER},
      LOCKED_STATUS = #{record.lockedStatus,jdbcType=TINYINT},
      MAX_SKU_REFUND_AMOUNT = #{record.maxSkuRefundAmount,jdbcType=DECIMAL},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      OCCUPY_NUM = #{record.occupyNum,jdbcType=INTEGER},
      IS_ACTION_GOODS = #{record.isActionGoods,jdbcType=TINYINT},
      ACTION_OCCUPY_NUM = #{record.actionOccupyNum,jdbcType=INTEGER},
      IS_COUPONS = #{record.isCoupons,jdbcType=TINYINT},
      EL_ORDERLIST_ID = #{record.elOrderlistId,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{record.updateDataTime,jdbcType=TIMESTAMP},
      REAL_PAY_AMOUNT = #{record.realPayAmount,jdbcType=DECIMAL},
      AFTER_RETURN_AMOUNT = #{record.afterReturnAmount,jdbcType=DECIMAL},
      AFTER_RETURN_NUM = #{record.afterReturnNum,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.ordergoods.model.SaleorderGoodsGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    update T_SALEORDER_GOODS
    <set >
      <if test="saleorderId != null" >
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null" >
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null" >
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null" >
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="realPrice != null" >
        REAL_PRICE = #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasingPrice != null" >
        PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null" >
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="buyNum != null" >
        BUY_NUM = #{buyNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null" >
        DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null" >
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveryTime != null" >
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryCycle != null" >
        DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null" >
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=TINYINT},
      </if>
      <if test="deliveryDirectComments != null" >
        DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null" >
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null" >
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null" >
        REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null" >
        REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null" >
        REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null" >
        REPORT_STATUS = #{reportStatus,jdbcType=TINYINT},
      </if>
      <if test="reportComments != null" >
        REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null" >
        HAVE_INSTALLATION = #{haveInstallation,jdbcType=TINYINT},
      </if>
      <if test="goodsComments != null" >
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null" >
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserId != null" >
        ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null" >
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=TINYINT},
      </if>
      <if test="arrivalTime != null" >
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isIgnore != null" >
        IS_IGNORE = #{isIgnore,jdbcType=TINYINT},
      </if>
      <if test="ignoreTime != null" >
        IGNORE_TIME = #{ignoreTime,jdbcType=BIGINT},
      </if>
      <if test="ignoreUserId != null" >
        IGNORE_USER_ID = #{ignoreUserId,jdbcType=INTEGER},
      </if>
      <if test="lockedStatus != null" >
        LOCKED_STATUS = #{lockedStatus,jdbcType=TINYINT},
      </if>
      <if test="maxSkuRefundAmount != null" >
        MAX_SKU_REFUND_AMOUNT = #{maxSkuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null" >
        OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="isActionGoods != null" >
        IS_ACTION_GOODS = #{isActionGoods,jdbcType=TINYINT},
      </if>
      <if test="actionOccupyNum != null" >
        ACTION_OCCUPY_NUM = #{actionOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null" >
        IS_COUPONS = #{isCoupons,jdbcType=TINYINT},
      </if>
      <if test="elOrderlistId != null" >
        EL_ORDERLIST_ID = #{elOrderlistId,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null" >
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realPayAmount != null" >
        REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnAmount != null" >
        AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null" >
        AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      </if>
    </set>
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.ordergoods.model.SaleorderGoodsGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 05 17:05:41 CST 2020.
    -->
    update T_SALEORDER_GOODS
    set SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      REAL_PRICE = #{realPrice,jdbcType=DECIMAL},
      PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      BUY_NUM = #{buyNum,jdbcType=INTEGER},
      DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=TINYINT},
      DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      REPORT_STATUS = #{reportStatus,jdbcType=TINYINT},
      REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      HAVE_INSTALLATION = #{haveInstallation,jdbcType=TINYINT},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=TINYINT},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      IS_IGNORE = #{isIgnore,jdbcType=TINYINT},
      IGNORE_TIME = #{ignoreTime,jdbcType=BIGINT},
      IGNORE_USER_ID = #{ignoreUserId,jdbcType=INTEGER},
      LOCKED_STATUS = #{lockedStatus,jdbcType=TINYINT},
      MAX_SKU_REFUND_AMOUNT = #{maxSkuRefundAmount,jdbcType=DECIMAL},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      IS_ACTION_GOODS = #{isActionGoods,jdbcType=TINYINT},
      ACTION_OCCUPY_NUM = #{actionOccupyNum,jdbcType=INTEGER},
      IS_COUPONS = #{isCoupons,jdbcType=TINYINT},
      EL_ORDERLIST_ID = #{elOrderlistId,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER}
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </update>
</mapper>