<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/tailwind/tailwind.min.css">


<div id="app" style="display: none;">

    <div class="fixed top-0 right-2 h-full flex items-center justify-start writing-mode-vertical mt-2">
        <button :class="{ 'bg-blue-500 text-white px-3 py-10 mb-2 rounded focus:outline-none': selectedTab === 'summary', 'bg-gray-200 text-black px-3 py-10 mb-2 rounded focus:outline-none': selectedTab !== 'summary' }"
                @click="selectedTabSummary">
            个人通话汇总
        </button>
        <button :class="{ 'bg-blue-500 text-white px-3 py-10 mb-2 rounded focus:outline-none': selectedTab === 'assistant', 'bg-gray-200 text-black px-3 py-10 mb-2 rounded focus:outline-none': selectedTab !== 'assistant' }"
                @click="selectedTabAssistant">
            通话小助手
        </button>
    </div>

    <div v-if="selectedTab === 'assistant'">
        <div class="bg-white p-2" style="width: 97%;">
            <div class="bg-blue-500 text-white p-2">
                <h1 class="text-xl font-bold">通话小助手</h1>
            </div>
            <div class="flex justify-between items-center mt-1">
                <div class="flex items-center">
                    <img alt="Profile icon" class="rounded-full p-1" height="50"
                         src="http://ai.vedeng.com/images/chat_Beta.png" width="50"/>
                    <div class="bg-gray-200 p-2 ml-2">
                        <p>通话小助手为你解析信息如下</p>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="ml-6">
                        <span class="p-1">当日通话记录</span>
                        <el-select v-model="selectedCommunicateRecordTime" @change="onOptionSelected" class="w-80 ml-2">
                            <el-option v-for="option in communicateRecordToday" :key="option.id" :value="option.text"
                                       :label="option.text">
                            </el-option>
                        </el-select>
                    </div>
                    <div @click="showAiHelpme(aiCommunicateRecord.communicateRecordId)" title="ERP贝壳助手"
                         class="flex items-center cursor-pointer border border-gray-200 rounded w-30 p-2 ml-1"
                         style="margin-right: 30px;">
                        <img src="/static/images/ai-zhushou.gif" width="20px" height="20px">
                        <span>播放</span>
                    </div>
                </div>
            </div>

            <div class="border p-1 mb-4" v-if="communicateRecordToday.length > 0">
                <div class="flex items-center border-b-2 border-gray-300 p-2">
                    <h2 class="text-xl font-bold mr-4 w-40">通话内容AI分析</h2>
                    <el-alert v-if="aiCommunicateRecord.exist == false "
                              class="w-auto mt-1"
                              :title="aiCommunicateRecord.tip"
                              type="warning"
                              show-icon
                              :closable="false"
                              >
                    </el-alert>
                </div>

                <div style="width: 100%">
                    <div class="p-2">
                        <div><strong>录音ID:</strong>
                            <button class="bg-transparent text-blue-500 rounded"
                                    @click="getRecordCall(aiCommunicateRecord.communicateRecordId)">
                                {{aiCommunicateRecord.communicateRecordId}}
                            </button>
                            <span v-if="aiCommunicateRecord.coidType === 3">（企微-呼出）</span>
                            <span v-if="aiCommunicateRecord.coidType === 4">（企微-呼入）</span>
                        </div>
                        <div v-if="aiCommunicateRecord.traderId"><strong>客户名称:</strong>
                            <button class="bg-transparent text-blue-500 rounded"
                                    @click="viewTrader(aiCommunicateRecord.traderId,aiCommunicateRecord.traderCustomerId)">
                                {{aiCommunicateRecord.traderCustomerName}}
                            </button>
                        </div>
                        <div><strong>联系人:</strong> {{aiCommunicateRecord.traderContactName}} |
                            {{aiCommunicateRecord.traderContactPhone}}
                        </div>
                        <div><strong>通话类型:</strong> {{aiCommunicateRecord.contactType}}</div>
                    </div>
                </div>

                <div class="mb-4 border p-1">
                    <div class="border-b-2 border-gray-300 p-2">
                        <h3 class="text-lg font-bold">AI摘要分析</h3>
                    </div>
                    <div class="p-4" v-if="aiCommunicateRecord.digestList.length > 0">
                        <el-checkbox-group v-model="checkedDigest" @change="handleCheckedDigest">
                            <el-checkbox
                                    v-for="(item, index) in aiCommunicateRecord.digestList"
                                    :key="index"
                                    :label="item.id"
                                    :disabled="item.disabled"
                                    style="display: block;"
                            >
                                <span class="checkbox_content">{{item.value}}</span>
                            </el-checkbox>
                        </el-checkbox-group>
                        <el-checkbox
                                :indeterminate="indeterminate"
                                v-model="allChecked"
                                @change="handleAllCheckChange"
                        >全选
                        </el-checkbox>
                        <button class="bg-blue-500 text-white p-1 ml-4 rounded text-xs"
                                @click="handleAddCommunicateRecord">
                            添加沟通记录
                        </button>
                    </div>

                    <div class="flex items-center bg-gray-200 p-4"
                         v-if="aiCommunicateRecord.digestList.length == 0">
                        <p>暂未分析到摘要</p>
                    </div>
                </div>


                <div class="border p-1 mb-4" v-if="communicateRecordToday.length > 0">
                    <div class="border-b-2 border-gray-300 p-2">
                        <h3 class="text-lg font-bold">待办事项</h3>
                    </div>

                    <div class="p-4" v-if="aiCommunicateRecord.todoList.length > 0">
                        <div class="mb-4"
                             v-if=" needTodo.needBusinessOpportunity != null && needTodo.needBusinessOpportunity == '是' ">
                            <h2 class="text-lg font-semibold">可能需要生成新的商机</h2>
                            <div class="flex items-center bg-gray-200 p-4"
                                 v-if="rCommunicateTodoJAi.createBusinessChange == null">
                                <p>是否生成商机？</p>
                                <div class="ml-auto">
                                    <button class="bg-blue-500 text-white py-1 px-2 rounded"
                                            @click="createBusinessChange">
                                        生成商机
                                    </button>
                                    <button class="bg-gray-200 text-blue-500 py-1 px-2 rounded ml-2"
                                            @click="noCreateBusinessChange">无需生成
                                    </button>
                                </div>
                            </div>

                            <div class="flex items-center bg-gray-200 p-4"
                                 v-if="rCommunicateTodoJAi.createBusinessChange != null && rCommunicateTodoJAi.createBusinessChange">
                                <p>已生成商机:
                                    <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                            @click="viewBusinessChange(rCommunicateTodoJAi.businessId)">
                                        {{rCommunicateTodoJAi.businessNo}}
                                    </button>
                                </p>
                            </div>
                            <div class="flex items-center bg-gray-200 p-4"
                                 v-if="rCommunicateTodoJAi.createBusinessChange != null && !rCommunicateTodoJAi.createBusinessChange">
                                <p>销售确认:无需生成</p>
                            </div>
                        </div>

                        <div class="mb-4"
                             v-if="needTodo.needToSendProductInfo !=null && needTodo.needToSendProductInfo == '是' ">
                            <h2 class="text-lg font-semibold">客户想要获取商品资料: 聚合页, 彩页, 图片等</h2>
                            <div class="flex items-center bg-gray-200 p-4" v-if="needTodo.productInfo">
                                <p>{{needTodo.productInfo}}</p>
                                <div class="ml-auto">
                                    <button class="bg-blue-500 text-white py-1 px-2 rounded"
                                            @click="viewAi(needTodo.productInfo)">
                                        查看AI资料库小助手
                                    </button>
                                </div>
                            </div>
                            <div class="flex items-center bg-gray-200 p-4" v-if="!needTodo.productInfo">
                                <p>暂未找到匹配资料，请自主查找资料</p>
                            </div>
                        </div>

                        <div class="mb-4" v-if="needTodo.needToSendQuote != null && needTodo.needToSendQuote == '是' ">
                            <h2 class="text-lg font-semibold">提供商品报价</h2>
                            <div class="bg-gray-200 p-4">
                                <p class="mt-2">为你解析出要推荐的商品如下:</p>
                                <div class="mt-4">
                                    <button
                                            v-for="(product, index) in needTodo.quoteProduct"
                                            :key="index"
                                            class="bg-blue-500 text-white py-1 px-2 rounded mr-2"
                                            @click="handleProductClick(product)"
                                    >
                                        {{ product }}
                                    </button>
                                    <p class="mt-2 text-xs">该信息由ai解析。不能作为最终解释，具体请咨询相关部门或者
                                        <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                                @click="viewAi('我要查价格')">
                                            AI询价小助手
                                        </button>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4" v-if="needTodo.recommendProduct != null && needTodo.recommendProduct== '是' ">
                            <h2 class="text-lg font-semibold">推荐商品</h2>
                            <div class="bg-gray-200 p-4">
                                <p class="mt-2">为你解析出要推荐的商品如下:</p>
                                <div class="mt-4">
                                    <button
                                            v-for="(product, index) in needTodo.recommendedProduct"
                                            :key="index"
                                            class="bg-blue-500 text-white py-1 px-2 rounded mr-2"
                                            @click="handleProductClick(product)"
                                    >
                                        {{ product }}
                                    </button>
                                </div>
                                <p class="mt-2 text-sm">该信息由ai解析。不能作为最终解释，具体请咨询相关部门或者
                                    <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                            @click="viewAi('我要查价格')">
                                        AI询价小助手
                                    </button>
                                </p>
                            </div>
                        </div>

                        <div class="mb-4"
                             v-if="needTodo.needToSendProductPlan != null  && needTodo.needToSendProductPlan == '是' ">
                            <h2 class="text-lg font-semibold">客户想获取产品方案</h2>
                            <div class="bg-gray-200 p-4">
                                <p class="mt-1">请咨询产品方案部门</p>
                            </div>
                        </div>

                        <div class="mb-4" v-if=" ( needTodo.sendAfterSalesPolicy !=null && needTodo.sendAfterSalesPolicy == '是' )
                    && needTodo.afterSaleGoodsTodos!=null && needTodo.afterSaleGoodsTodos.length>0 ">
                            <h2 class="text-lg font-semibold">发送售后政策</h2>
                            <div class="bg-gray-200 p-4">
                                <div v-for="(td, index) in needTodo.afterSaleGoodsTodos" :key="index">
                                    <p class="mt-1">
                                        {{ td.skuName }}
                                        <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                                @click="viewAfter(td.skuNo)">
                                            {{ td.skuNo }}
                                        </button>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h2 class="text-lg font-semibold">是否加入/修改客户标签</h2>
                            <div class="flex items-center bg-gray-200 p-4"
                                 v-if="rCommunicateTodoJAi.updateTraderSign == null">
                                <p>是否加入/修改客户标签？</p>
                                <div class="ml-auto">
                                    <button class="bg-blue-500 text-white py-1 px-2 rounded" @click="updateTraderSign">
                                        添加/修改客户标签
                                    </button>
                                    <button class="bg-gray-200 text-blue-500 py-1 px-2 rounded ml-2"
                                            @click="noUpdateTraderSign">无需修改
                                    </button>
                                </div>
                            </div>
                            <div class="flex items-center bg-gray-200 p-4"
                                 v-if="rCommunicateTodoJAi.updateTraderSign != null && rCommunicateTodoJAi.updateTraderSign">
                                <p>已添加/修改客户标签:
                                    <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                            @click="viewTrader(rCommunicateTodoJAi.traderId,rCommunicateTodoJAi.traderCustomerId)">
                                        {{rCommunicateTodoJAi.traderCustomerName}}
                                    </button>
                                </p>
                            </div>
                            <div class="flex items-center bg-gray-200 p-4"
                                 v-if="rCommunicateTodoJAi.updateTraderSign != null && !rCommunicateTodoJAi.updateTraderSign">
                                <p>销售确认:无需添加/修改</p>
                            </div>
                        </div>

                        <div class="mb-4"
                             v-if="needTodo.completeContactPositionInfo!=null && needTodo.completeContactPositionInfo == '是' ">
                            <h2 class="text-lg font-semibold">是否同步联系人职位信息</h2>
                            <div class="flex items-center bg-gray-200 p-4"
                                 v-if="rCommunicateTodoJAi.syncContactPosition === null">
                                <div>
                                    <button class="bg-blue-500 text-white py-1 px-2 rounded"
                                            @click="syncContactPosition">
                                        同步到联系人标签
                                    </button>
                                </div>
                            </div>
                            <div class="flex items-center bg-gray-200 p-4"
                                 v-if="rCommunicateTodoJAi.syncContactPosition != null && rCommunicateTodoJAi.syncContactPosition">
                                <p>已同步到联系人职位信息</p>
                            </div>
                        </div>

                        <div class="mb-4" v-if="needTodo.isWeChatAdded!=null && needTodo.isWeChatAdded  == '是' ">
                            <h2 class="text-lg font-semibold">添加微信</h2>
                            <div class="flex items-center bg-gray-200 p-4">
                                <p class="mt-1">添加微信</p>
                            </div>
                        </div>

                        <div class="mb-4"
                             v-if="needTodo.needToSendContractInformation!=null && needTodo.needToSendContractInformation == '是' ">
                            <h2 class="text-lg font-semibold">发送合同</h2>
                            <div class="flex items-center bg-gray-200 p-4">
                                <p class="mt-1">发送合同</p>
                            </div>
                        </div>

                        <div class="mb-4" v-if="needTodo.needOtherMatters!=null && needTodo.needOtherMatters == '是' ">
                            <h2 class="text-lg font-semibold">其他待办</h2>
                            <div class="flex items-center bg-gray-200 p-4">
                                <p class="mt-1">{{needTodo.otherMatters}}</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 flex items-center bg-gray-200" v-if="aiCommunicateRecord.todoList.length == 0">
                        <p>暂未分析到待办事项</p>
                    </div>
                </div>
            </div>

            <div class="centered-div" v-if="communicateRecordToday.length == 0">
                <p>暂无通话记录</p>
            </div>
        </div>

    </div>

    <div v-if="selectedTab === 'summary'">
        <div class="bg-white p-2" style="width: 97%;">
            <div class="bg-blue-500 text-white p-2">
                <h1 class="text-xl font-bold">通话汇总</h1>
            </div>

            <div class="p-2 flex justify-between items-center">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold mr-2">通话事项统计: </h2>
                    <span> {{ parseTime(this.aiCommunicateRecordSummary.summaryDate, '{y}-{m}-{d}') }} </span>
                </div>
                <div class="flex justify-between items-center mr-5">
                    <h2 class="text-lg font-semibold mr-2">切换日期:</h2>
                    <el-date-picker
                            v-model="aiCommunicateRecordSummary.summaryDate"
                            type="date"
                            placeholder="选择日期"
                            @change="viewSummary"
                            size="medium"
                            :picker-options="{ disabledDate(date) {return date && date.getTime() > new Date().getTime();}}">
                    </el-date-picker>
                </div>
            </div>

            <div class="border mb-4">
                <div class="border-b-2 border-gray-300 p-2">
                    <h2 class="text-xl font-bold">类型汇总&结果预测</h2>
                </div>

                <div class="flex flex-wrap mb-2">
                    <div class="w-1/5 px-2  bg-gray-100 rounded-md">
                        <div ref="pie1" style="height:140px;"></div>
                        <div class="text-center mb-2">总通话数：{{ this.aiCommunicateRecordSummary.sumCommunicatNum }}</div>
                    </div>
                    <div class="w-1/5 px-2  bg-gray-100 rounded-md">
                        <div ref="pie2" style="height:140px;"></div>
                        <div class="text-center mb-2">通话总时长：{{ this.aiCommunicateRecordSummary.sumCommunicateTime }} 分钟
                        </div>
                    </div>
                    <div class="w-1/5 px-2  bg-gray-100 rounded-md">
                        <div ref="pie3" style="height:140px;"></div>
                        <div class="text-center mb-2">意向客户预测：{{ this.aiCommunicateRecordSummary.traderForecastNum }}
                        </div>
                    </div>
                    <div class="w-1/5 px-2 bg-gray-100 rounded-md">
                        <div ref="pie4" style="height:140px;"></div>
                        <div class="text-center mb-2">生成商机数: {{ this.aiCommunicateRecordSummary.businessNum }}</div>
                    </div>
                    <div class="w-1/5 px-2  bg-gray-100 rounded-md">
                        <div ref="pie5" style="height:140px;"></div>
                        <div class="text-center mb-2">添加微信数预测: {{ this.aiCommunicateRecordSummary.wechatForecastNum }}
                        </div>
                    </div>
                </div>


                <table class="w-full text-sm text-left text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-blue-200">
                    <tr>
                        <th class="py-3 px-6">通话类型</th>
                        <th class="py-3 px-6">总通话数</th>
                        <th class="py-3 px-6">接通通话数</th>
                        <th class="py-3 px-6">通话时长 (分钟)</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr class="bg-white border-b" v-for="item in aiCommunicateRecordSummary.summaryList">
                        <td class="py-4 px-6">{{ item.type }}</td>
                        <td class="py-4 px-6">{{ item.total }}</td>
                        <td class="py-4 px-6">{{ item.completed }}</td>
                        <td class="py-4 px-6">{{ item.duration }}</td>
                    </tr>
                    </tbody>
                </table>

            </div>


            <div class="border mb-4" style="height: 300px;">
                <div class="border-b-2 border-gray-300 p-2">
                    <h2 class="text-xl font-bold">通话待办事项汇总</h2>
                </div>
                <table class="w-full text-sm text-left text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-blue-200">
                    <tr>
                        <th class="py-3 px-6">添加客户微信</th>
                        <th class="py-3 px-6">完善客户标签</th>
                        <th class="py-3 px-6">生成商机</th>
                        <th class="py-3 px-6">发送报价</th>
                        <th class="py-3 px-6">发送商品资料</th>
                        <th class="py-3 px-6">发送产品方案</th>
                        <th class="py-3 px-6">发送合同信息</th>
                        <th class="py-3 px-6">发送售后政策</th>
                        <th class="py-3 px-6">其他</th>
                        <th class="py-3 px-6">总计</th>
                    </tr>
                    </thead>
                    <tbody>
                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.isWeChatAddedNum }}</span>
                            <button class="arrow-button" @click="togglePanel(1)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 1">
                            <div class="phone-number font-bold">拨打号码</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.isWeChatAddedRecordIdList"
                                 :key="number">{{ number }}
                            </div>
                        </div>
                    </td>

                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.completeCustomerTagNum }}</span>
                            <button class="arrow-button" @click="togglePanel(2)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 2">
                            <div class="phone-number font-bold">录音ID</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.completeCustomerTagList"
                                 :key="number">
                                <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                        @click="getRecordCall(number)">
                                    {{ number }}
                                </button>
                            </div>
                        </div>
                    </td>

                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.needBusinessOpportunityNum }}</span>
                            <button class="arrow-button" @click="togglePanel(3)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 3">
                            <div class="phone-number font-bold">录音ID</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.needBusinessOpportunityPhoneList"
                                 :key="number">
                                <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                        @click="getRecordCall(number)">
                                    {{ number }}
                                </button>
                            </div>
                        </div>
                    </td>

                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.needToSendQuoteNum }}</span>
                            <button class="arrow-button" @click="togglePanel(4)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 4">
                            <div class="phone-number font-bold">录音ID</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.needToSendQuoteRecordIdList"
                                 :key="number">
                                <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                        @click="getRecordCall(number)">
                                    {{ number }}
                                </button>
                            </div>
                        </div>
                    </td>

                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.needToSendProductInfoNum }}</span>
                            <button class="arrow-button" @click="togglePanel(5)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 5">
                            <div class="phone-number font-bold">录音ID</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.needToSendProductInfoRecordIdList"
                                 :key="number">
                                <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                        @click="getRecordCall(number)">
                                    {{ number }}
                                </button>
                            </div>
                        </div>
                    </td>

                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.recommendProductNum }}</span>
                            <button class="arrow-button" @click="togglePanel(6)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 6">
                            <div class="phone-number font-bold">录音ID</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.recommendProductList"
                                 :key="number">
                                <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                        @click="getRecordCall(number)">
                                    {{ number }}
                                </button>
                            </div>
                        </div>
                    </td>

                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.needToSendContractInformationNum }}</span>
                            <button class="arrow-button" @click="togglePanel(7)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 7">
                            <div class="phone-number font-bold">录音ID</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.needToSendContractInformationAddedRecordIdList"
                                 :key="number">
                                <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                        @click="getRecordCall(number)">
                                    {{ number }}
                                </button>
                            </div>
                        </div>
                    </td>
                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.sendAfterSalesPolicyNum }}</span>
                            <button class="arrow-button" @click="togglePanel(8)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 8">
                            <div class="phone-number font-bold">录音ID</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.sendAfterSalesPolicyRecordIdList"
                                 :key="number">
                                <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                        @click="getRecordCall(number)">
                                    {{ number }}
                                </button>
                            </div>
                        </div>
                    </td>
                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.otherMattersNum }}</span>
                            <button class="arrow-button" @click="togglePanel(9)"><i class="el-icon-arrow-down"></i>
                            </button>
                        </div>
                        <!-- 面板 -->
                        <div class="panel absolute" v-if="showPanel === 9">
                            <div class="phone-number font-bold">录音ID</div>
                            <div class="phone-number pl-4"
                                 v-for="number in aiCommunicateRecordSummary.todoDetail.otherMattersRecordIdList"
                                 :key="number">
                                <button class="bg-transparent text-blue-500 py-1 px-2 rounded"
                                        @click="getRecordCall(number)">
                                    {{ number }}
                                </button>
                            </div>
                        </div>
                    </td>

                    <td class="py-4 px-6 relative">
                        <div class="flex items-center">
                            <span>{{ aiCommunicateRecordSummary.todoDetail.total }}</span>
                        </div>
                    </td>
                    </tbody>
                </table>
            </div>


        </div>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/communicateRecord.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/echarts/echarts-4.9.min.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const viewInfo = {
        communicateRecordId: '${communicateRecordId}',
        selectedTab: '${selectedTab}',
    }

    new Vue({
        el: '#app',
        data() {
            return {
                showPanel: null, // 控制面板的显示状态
                selectedTab: viewInfo.selectedTab ? viewInfo.selectedTab : 'assistant',
                communicateRecordId: viewInfo.communicateRecordId,

                aiCommunicateRecord: {
                    communicateRecordId: '',
                    coidType: 0,
                    traderContactName: '',
                    contactDate: '',
                    contactType: '',
                    todoList: [],
                    digestList: []
                },
                communicateRecordToday: [],
                allChecked: false,
                indeterminate: false,
                selectedCommunicateRecordTime: '',
                checkedDigest: [],
                rCommunicateTodoJAi: {},
                needTodo: {},
                aiCommunicateRecordSummary: {
                    summaryList: [],
                    todoDetail: [],
                    pieMap: []
                },

            }
        },

        created() {

        },

        mounted() {
            loadingApp()
            this.updateAllCheckedStatus();
            this.initData();
        },

        methods: {
            togglePanel(index) {
                this.showPanel = this.showPanel === index ? null : index;
            },
            initData() {
                if (this.selectedTab === 'summary') {
                    this.selectedTabSummary()
                } else {
                    this.selectedTabAssistant()
                }
            },

            // 获取当日录音
            getCommunicateRecordToday() {
                this.communicateRecordToday = []
                getCommunicateRecordToday().then(res => {
                    if (res.data.code === 0) {
                        res.data.data.forEach(item => {
                            this.communicateRecordToday.push(
                                {
                                    id: item.communicateRecordId,
                                    text: '录音ID: ' + item.communicateRecordId + ' | ' + parseTime(item.addTime)
                                })
                        })
                        if (this.communicateRecordToday.length > 0) {
                            this.selectedCommunicateRecordTime = this.communicateRecordToday[0].text;
                            this.communicateRecordId = this.communicateRecordToday[0].id;
                            this.getAiInfo(this.communicateRecordId);
                        }
                    }
                }).catch(error => {
                    this.$message.error('请稍后重试');
                });
            },

            getAiInfo(communicateRecordId) {
                getAiInfo({communicateRecordId: communicateRecordId}).then(res => {
                    if (res.data.code === 0) {
                        this.aiCommunicateRecord = res.data.data;
                        console.log("xxxx"+this.aiCommunicateRecord);
                        this.rCommunicateTodoJAi = this.aiCommunicateRecord.rcommunicateTodoJAiDto

                        this.needTodo = this.aiCommunicateRecord.todoList.reduce((obj, item) => {
                            if (item['fieldResult'] != null) {
                                obj[item['fieldCode']] = item['fieldResult'];
                                // 正则表达式，匹配英文逗号或中文逗号
                                const splitRegex = /,|，/;
                                if (item['fieldCode'] === 'quoteProduct' && typeof obj[item['fieldCode']] === 'string') {
                                    obj[item['fieldCode']] = obj[item['fieldCode']].split(splitRegex).map(item => item.trim());
                                }
                                if (item['fieldCode'] === 'recommendedProduct' && typeof obj[item['fieldCode']] === 'string') {
                                    obj[item['fieldCode']] = obj[item['fieldCode']].split(splitRegex).map(item => item.trim());
                                }
                            }
                            return obj;
                        }, {});
                        console.log(this.needTodo)
                    }
                })
            },

            onOptionSelected(value) {
                const selectedOption = this.communicateRecordToday.find(option => option.text === value);
                this.communicateRecordId = selectedOption.id
                this.getAiInfo(this.communicateRecordId)
            },

            handleCheckedDigest(item) {
                this.updateAllCheckedStatus(item);
            },

            handleAllCheckChange(val) {
                if (val) {
                    // 全选
                    this.checkedDigest = this.aiCommunicateRecord.digestList.map(item => item.id);
                    this.indeterminate = false;
                } else {
                    // 取消全选
                    this.checkedDigest = [];
                    this.indeterminate = false;
                }
            },
            updateAllCheckedStatus(value) {
                if (value != undefined) {
                    let checkedCount = value.length;
                    this.checkAll = checkedCount === this.aiCommunicateRecord.digestList.length;
                    this.indeterminate = checkedCount > 0 && checkedCount < this.aiCommunicateRecord.digestList.length;
                }
            },

            handleAddCommunicateRecord() {
                const selectedValues = this.checkedDigest;
                const selectedObjects = this.aiCommunicateRecord.digestList.filter(item => {
                    return selectedValues.includes(item.id) && !item.disabled;
                });
                if (selectedObjects.length === 0) {
                    this.$message.error('请选择摘要');
                    return;
                }
                const selectedValuesArray = selectedObjects.map(item => item.value);
                const rCommunicateTodoJAiDto = {
                    communicateRecordId: this.aiCommunicateRecord.communicateRecordId,
                    checkDigestId: this.checkedDigest,
                    checkDigest: selectedValuesArray
                }
                addAiCommunicateRecord(rCommunicateTodoJAiDto).then(res => {
                    if (res.data.code === 0) {
                        this.getAiInfo(this.communicateRecordId);
                        this.$message.success('成功');
                    }
                }).catch(error => {
                    this.$message.error('请稍后重试');
                });

            },

            createBusinessChange() {
                const url = "businessChance/aiAssistantToEdit.do?isAiAssistant=1&aiCommunicateRecordId=" + this.aiCommunicateRecord.communicateRecordId
                window.top.postMessage({
                    name: "新增商机",
                    url: url,
                    id: "1"
                }, '*');
            },

            noCreateBusinessChange() {
                const rCommunicateTodoJAiDto = {
                    traderId: this.aiCommunicateRecord.traderId,
                    communicateRecordId: this.aiCommunicateRecord.communicateRecordId,
                    isBusinessChange: 0,
                }
                noCreateBusinessChange(rCommunicateTodoJAiDto).then(res => {
                    if (res.data.code === 0) {
                        this.getAiInfo(this.communicateRecordId);
                        this.$message.success('成功');
                    }
                }).catch(error => {
                    this.$message.error('请稍后重试');
                });
            },

            viewAi(productInfo) {
                console.log('查看ai小助手', productInfo)
                window.top.openAiWindow(productInfo);
            },

            handleProductClick(product) {
                const productUrl = "/goodsInfo/search/goods.do?keywords=" + product + "&pageNo=1"
                window.top.postMessage({
                    name: "商品选型",
                    url: productUrl,
                    id: "2"
                }, '*');
            },

            updateTraderSign() {
                let url = ""
                let name = ""
                console.log(this.aiCommunicateRecord.traderId)
                if (this.aiCommunicateRecord.traderId != null) {
                    url = "/trader/customer/editbaseinfo.do?traderId=" + this.aiCommunicateRecord.traderId + "&isAiAssistant=1&aiCommunicateRecordId=" + this.aiCommunicateRecord.communicateRecordId
                    name = "编辑客户"
                } else {
                    url = "/trader/customer/add.do?isAiAssistant=1&aiCommunicateRecordId=" + this.aiCommunicateRecord.communicateRecordId
                    name = "新增客户"
                }
                window.top.postMessage({
                    name: name,
                    url: url,
                    id: "3"
                }, '*');

            },

            viewTrader(traderId, traderCustomerId) {
                const url = "/trader/customer/baseinfo.do?traderId=" + traderId + "&traderCustomerId=" + traderCustomerId
                window.top.postMessage({
                    name: "客户详情",
                    url: url,
                    id: "4"
                }, '*');
            },

            viewAfter(skuNo) {
                const url = " /aftersale/serviceStandard/detail.do?skuNo=" + skuNo
                window.top.postMessage({
                    name: "售后政策详情",
                    url: url,
                    id: "5"
                }, '*');
            },

            viewBusinessChange(businessId) {
                const url = "/businessChance/details.do?id=" + businessId
                window.top.postMessage({
                    name: "商机详情",
                    url: url,
                    id: "6"
                }, '*');
            },

            getRecordCall(communicateRecordId) {
                const url = "/system/call/getrecord.do?communicateRecordId=" + communicateRecordId
                window.top.postMessage({
                    name: "通话记录",
                    url: url,
                    id: "7"
                }, '*');
            },


            noUpdateTraderSign() {
                const rCommunicateTodoJAiDto = {
                    communicateRecordId: this.aiCommunicateRecord.communicateRecordId,
                }
                noUpdateTraderSign(rCommunicateTodoJAiDto).then(res => {
                    if (res.data.code === 0) {
                        this.getAiInfo(this.communicateRecordId);
                        this.$message.success('成功');
                    }
                }).catch(error => {
                    this.$message.error('请稍后重试');
                });

            },


            syncContactPosition() {
                if (this.aiCommunicateRecord.traderContactId == null) {
                    const url = "/ezadmin/list/list-traderContractList?IS_ENABLE=1&perPageInt=50&" + "TRADER_ID=" + this.aiCommunicateRecord.traderId + "&TRADER_CUSTOMERID=" + this.aiCommunicateRecord.traderId + "&customerNature=465"
                    window.top.postMessage({
                        name: "客户联系人",
                        url: url,
                        id: "7"
                    }, '*');
                } else {
                    const rCommunicateTodoJAiDto = {
                        communicateRecordId: this.aiCommunicateRecord.communicateRecordId,
                        traderId: this.aiCommunicateRecord.traderContactId
                    }
                    syncContactPosition(rCommunicateTodoJAiDto).then(res => {
                        if (res.data.code === 0) {
                            this.getAiInfo(this.communicateRecordId);
                            this.$message.success('成功');
                        }
                    }).catch(error => {
                        this.$message.error('请稍后重试');
                    });
                }

            },


            selectedTabSummary() {
                this.selectedTab = 'summary'
                const date = new Date();
                this.summary(parseTime(date));
            },
            selectedTabAssistant() {
                this.selectedTab = 'assistant'
                this.getCommunicateRecordToday();
            },

            viewSummary(date) {
                this.summary(parseTime(date));
            },


            summary(date) {
                getSummary({date: date}).then(res => {
                    if (res.data.code === 0) {
                        this.aiCommunicateRecordSummary = res.data.data
                        this.renderChart();
                    }
                })
            },

            showAiHelpme(communicateRecordId) {

                if (communicateRecordId == null || communicateRecordId == '') {
                    this.$message.error('请选择通话记录');
                    return;
                }

                window.top.showAiHelpme(communicateRecordId)
            },

            renderChart() {
                const color = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC'];
                const pie1 = this.$refs.pie1;
                const myChart1 = echarts.init(pie1);

                let filteredData = this.aiCommunicateRecordSummary.pieMap.sumCommunicatNum.filter(item => item.value !== 0);
                if (filteredData.length === 0) {
                    filteredData = [{value: 0, name: '暂无通话数据'}];
                }

                const option1 = {
                    color: filteredData.length === 0 ? ['#999999'] : color,
                    title: {
                        left: 'center',
                    },
                    tooltip: {
                        trigger: 'item',
                        position: 'inside',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    series: [
                        {
                            name: '',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: filteredData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },

                        }
                    ]
                };
                myChart1.setOption(option1);

                const pie2 = this.$refs.pie2;
                const myChart2 = echarts.init(pie2);
                const option2 = {
                    color: this.aiCommunicateRecordSummary.pieMap.sumCommunicateTime[0].value === 0 ? ['#999999'] : color,
                    title: {
                        left: 'center',
                    },
                    tooltip: {
                        trigger: 'item',
                        position: 'inside',
                        formatter: '{a} <br/>{b} : {c}'
                    },
                    series: [
                        {
                            name: '',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: this.aiCommunicateRecordSummary.pieMap.sumCommunicateTime,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },

                        },

                    ]
                };
                myChart2.setOption(option2);

                const pie3 = this.$refs.pie3;
                const myChart3 = echarts.init(pie3);
                const option3 = {
                    color: this.aiCommunicateRecordSummary.pieMap.traderForecastNum[0].value === 0 ? ['#999999'] : color,
                    title: {
                        left: 'center',
                    },
                    tooltip: {
                        trigger: 'item',
                        position: 'inside',
                        formatter: '{a} <br/>{b} : {c})'
                    },
                    series: [
                        {
                            name: '',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: this.aiCommunicateRecordSummary.pieMap.traderForecastNum,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },

                        }
                    ]
                };
                myChart3.setOption(option3);

                const pie4 = this.$refs.pie4;
                const myChart4 = echarts.init(pie4);
                const option4 = {
                    color: this.aiCommunicateRecordSummary.pieMap.businessNum[0].value === 0 ? ['#999999'] : color,
                    title: {
                        left: 'center',
                    },
                    tooltip: {
                        trigger: 'item',
                        position: 'inside',
                        formatter: '{a} <br/>{b} : {c}'
                    },
                    series: [
                        {
                            name: '',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: this.aiCommunicateRecordSummary.pieMap.businessNum,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                        }
                    ]
                };
                myChart4.setOption(option4);

                const pie5 = this.$refs.pie5;
                const myChart5 = echarts.init(pie5);
                const option5 = {
                    color: this.aiCommunicateRecordSummary.pieMap.wechatForecastNum[0].value === 0 ? ['#999999'] : color,
                    title: {
                        left: 'center',
                    },
                    tooltip: {
                        trigger: 'item',
                        position: 'inside',
                        formatter: '{a} <br/>{b} : {c}'
                    },
                    series: [
                        {
                            name: '',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: this.aiCommunicateRecordSummary.pieMap.wechatForecastNum,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                        }
                    ]
                };
                myChart5.setOption(option5);
            }
        },
    })

</script>


<style>
    .writing-mode-vertical {
        writing-mode: vertical-rl; /* 或者 vertical-lr 根据你的需求 */
    }

    .checkbox_content {
        width: 800px;
        display: inline-grid;
        white-space: pre-line;
        word-wrap: break-word;
        overflow: hidden;
    }

    .relative {
        position: relative;
    }

    .arrow-button {
        border: none;
        background: none;
        cursor: pointer;
        margin-left: 10px;
    }

    .panel {
        position: absolute;
        top: 100%; /* 定位在 td 下方 */
        left: 0;
        width: 100%;
        max-height: 150px; /* 设置面板的最大高度 */
        overflow-y: auto; /* 当内容超出高度时显示滚动条 */
        background-color: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        padding: 10px;
        z-index: 1; /* 确保面板在其他元素之上 */
    }

    .phone-number {
        margin-bottom: 5px;
    }

    .centered-div {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
    }

</style>
