package com.vedeng.workbench.model;

import com.vedeng.workbench.model.vo.base.WorkbenchSummaryObject;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;


@Data
public class HeaddataSummaryDto {
    /**
     * 统计对象信息
     */
    private WorkbenchSummaryObject objectInfo;

    /**
     * 商机总额
     */
    private BigDecimal totalBussinessAmount;

    /**
     * 商机数量
     */
    private Integer totalBussinessNum;

    /**
     * 平均金额
     */
    private BigDecimal averageAmount;

    /**
     * 昨日新增商机数量
     */
    private Integer yesterdayTotalBussinessNum;

    /**
     * 本周新增商机数量
     */
    private Integer thisWeekTotalBussinessNum;

    /**
     * 预计当月到款商机数
     */
    private Integer thisMouthBussinessNum;
    /**
     * 昨日新增商机金额
     */
    private BigDecimal yesterdayTotalBussinessAmount;

    /**
     * 本周新增商机金额
     */
    private BigDecimal thisWeekTotalAmount;

    /**
     * 预计当月到款
     */
    private BigDecimal thisMouthTotalAmount;
}
