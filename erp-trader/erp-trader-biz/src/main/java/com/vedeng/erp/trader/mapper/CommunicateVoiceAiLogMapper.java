package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.CommunicateVoiceAiLogEntity;
import com.vedeng.erp.trader.dto.CommunicateVoiceAiLogApiDto;
import org.apache.ibatis.annotations.Param;

public interface CommunicateVoiceAiLogMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(CommunicateVoiceAiLogEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(CommunicateVoiceAiLogEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    CommunicateVoiceAiLogEntity selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CommunicateVoiceAiLogEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CommunicateVoiceAiLogEntity record);

    CommunicateVoiceAiLogApiDto selectByCommunicateAndSenceCodeGroupCode(@Param(value ="communicateRecordId") Integer communicateRecordId,
                                                                         @Param(value ="senceCode") String senceCode,
                                                                         @Param(value ="groupCode") String groupCode);

}