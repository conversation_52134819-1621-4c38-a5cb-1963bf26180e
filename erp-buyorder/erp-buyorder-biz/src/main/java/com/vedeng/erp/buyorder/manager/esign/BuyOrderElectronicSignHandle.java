package com.vedeng.erp.buyorder.manager.esign;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.base.api.dto.reqParam.FileInfo;
import com.vedeng.base.api.dto.reqParam.SignCallbackDto;
import com.vedeng.base.api.dto.resParam.SignCompanyInfo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.system.SyncDataTypeEnum;
import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.SyncDataErpDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.dto.PurchaseContractDto;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购单e签宝实现类
 * @date 2021/12/7 9:26
 */
@Component
@Slf4j
public class BuyOrderElectronicSignHandle extends AbstractElectronicSignHandle {
    @Autowired
    private SyncDataErpApiService syncDataErpApiService;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Value("${erp.domain}")
    private String erpDomain;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Value("${ht_remove_blank_page}")
    private Boolean isRemoveBlankPage;


    private static final String PRINT_CONTRACT_URL = "/order/newBuyorder/printBuyOrder.do?buyorderId=";

    private static final String RENDER_URL = "/api/render";

    private static final String FILENAME_PREFIX = "采购合同";

    @Override
    protected void electronicSignFailCompensate(String error, ElectronicSignParam electronicSignParam) {
        log.error("采购单异步签章任务失败日志[{}],参数[{}]" , error, JSON.toJSONString(electronicSignParam));
        if (electronicSignParam.getFlowType() == 2 || electronicSignParam.getFlowType() == 3) {
            // 置空合同半程盖章字段
            buyorderMapper.updateContractUrlOfBuyorder(electronicSignParam.getBuyOrderId(), null);
        }
    }

    @Override
    protected FileInfo toPdfGetFile(ElectronicSignParam electronicSignParam) {
        if (electronicSignParam.getBuyOrderId() == null) {
            throw new ServiceException("电子签章:采购单id不能为空");
        }

        Buyorder buyorder = buyorderMapper.getBuyorderVoById(electronicSignParam.getBuyOrderId());
        electronicSignParam.getBusinessInfo().setOrderNo(buyorder.getBuyorderNo());

        String contractTemplateUrl = erpDomain + PRINT_CONTRACT_URL + electronicSignParam.getBuyOrderId();
        String html2PdfUrl = html2PdfDomain + RENDER_URL;

        UrlToPdfParam urlToPdfParam = UrlToPdfParam.defaultUrlToPdfParam(contractTemplateUrl);
        // 上传未盖章空白合同链接
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf" , "采购单合同" + buyorder.getBuyorderNo(), urlToPdfParam);
        log.info("电子签章:自动生成订单模板合同,采购单ID[{}]，采购单编号[{}]，合同url[{}]" , buyorder.getBuyorderId(), buyorder.getBuyorderNo(), ossUrl);

        if (isRemoveBlankPage) {
            AtomicInteger retryCount = new AtomicInteger(0);
            ossUrl = removeBlankPdfPagesAndSaveFile2Oss(ossUrl, retryCount,null);
        }

        if (StringUtils.isBlank(ossUrl)) {
            log.error("电子签章:自动生成采购单模板合同失败，采购单ID[{}]" , electronicSignParam.getBuyOrderId());
            throw new ServiceException("电子签章:自动生成采购单合同失败");
        }
        log.info("电子签章:自动生成订单模板合同,采购单ID[{}]，采购单编号[{}]，合同url[{}]" , buyorder.getBuyorderId(), buyorder.getBuyorderNo(), ossUrl);
        buyorderMapper.updateContractUrlOfBuyorder(electronicSignParam.getBuyOrderId(), ossUrl);
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileName(CharSequenceUtil.subAfter(ossUrl, "=" , true) + ".pdf");
        fileInfo.setFilePath(ossUrl);
        return fileInfo;
    }

    @Override
    protected void electronicSignSuccessCompensate(ElectronicSignParam electronicSignParam) {

    }


    @Override
    protected void preMqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入采购订单实际消费业务类前置处理器,编号{}" , signCallbackDto.getOrderNo());
        AtomicInteger retryCount = new AtomicInteger(0);
        String fileName;
        Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(signCallbackDto.getOrderNo());
        if (buyorder != null) {
            fileName = buyorder.getBuyorderNo() + buyorder.getTraderName();
        } else {
            fileName = FILENAME_PREFIX + signCallbackDto.getOrderNo();
        }
        log.info("消费-》oss保存文件名：{}" , fileName);
        this.saveFile2Oss(signCallbackDto, retryCount, fileName);
    }

    @Autowired
    private BuyorderService buyorderService;

    /**
     * 采购订单：半程签贝登章 更新合同url
     *
     * @param signCallbackDto 电子签章MQ回传消息
     */
    @Override
    protected void mqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入采购订单实际消费业务类,编号{}" , signCallbackDto.getOrderNo());
        Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(signCallbackDto.getOrderNo());
        if (buyorder == null) {
            log.error("采购单不存在,编号{}" , signCallbackDto.getOrderNo());
            return;
        }
        buyorderMapper.updateContractUrlOfBuyorder(buyorder.getBuyorderId(), signCallbackDto.getFileUrl());
        log.info("进行双章合同附件检查是否上传的逻辑判断");
        if(checkSignCompanyInfo(buyorder.getBuyorderId())) {
            PurchaseContractDto purchaseContractDto = buildPurchaseContractDto(buyorder, signCallbackDto.getFileUrl());
            if (purchaseContractDto != null) {
                buyorderService.saveBuyorderAttachmentForAadmin(purchaseContractDto);
            }
        }
        this.checkNeedSendToOtherErp(buyorder,signCallbackDto.getFileUrl());
    }



    /**
     * 判断是否需要同步采购订单的合同给另一个ERP的销售订单
     * @param buyorder
     * @param fileUrl
     */
    public void checkNeedSendToOtherErp(Buyorder buyorder,String fileUrl){
        String traderName = buyorder.getTraderName();
        BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(traderName);
        if(baseCompanyInfoDto == null){
            log.info("采购订单合同{}不符合同步到子公司的条件",traderName);
            return;
        }
        log.info("采购订单合同{}符合同步到子公司的条件",traderName);

        String jsonContent = buildSaleContractDto(buyorder,fileUrl);
        String dataType = SyncDataTypeEnum.BUYORDERCONTACT_TO_SALECONTACT.getDataType();
        //需要同步给子公司
        SyncDataErpDto syncDataErpDto = new SyncDataErpDto();
        syncDataErpDto.setBusinessNo(buyorder.getBuyorderNo());
        syncDataErpDto.setTargetErp(baseCompanyInfoDto.getCompanyShortName());
        syncDataErpDto.setBusinessType(dataType);
        syncDataErpDto.setRequestContent(jsonContent);
        syncDataErpDto.setProcessStatus(ErpConst.ZERO);
        syncDataErpApiService.insert(syncDataErpDto);
    }

    /**
     * 组装为SaleContractDto对象，以便job发起时处理
     * @param buyorder
     * @param fileUrl
     * @return
     */
    private String buildSaleContractDto(Buyorder buyorder,String fileUrl){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saleorderNo", buyorder.getBuyorderNo());//源单号
        List<Attachment> attachmentList = buildAttachmentList(buyorder ,fileUrl);
        jsonObject.put("attachmentList", attachmentList);
        return jsonObject.toString();
    }

    private List<Attachment> buildAttachmentList(Buyorder buyorder ,String fileUrl) {
        List<Attachment> attachmentList = new ArrayList<>();
        Attachment attachment = new Attachment();
        attachment.setPrefix("pdf");
        attachment.setFileName("采购订单"+buyorder.getBuyorderNo()+"-合同-双边章.pdf");
        try {
            URI uri = new URI(fileUrl);
            // 获取主机名 (file.ivedeng.com)
            String host = uri.getHost();
            // 获取路径和查询参数 (/file/display?resourceId=ee78eba43f634ed28578aa80cea308fa)
            String path = uri.getPath();
            String query = uri.getQuery();
            String pathAndQuery = path + (query != null ? "?" + query : "");

            String resourceId = null;
            if(query != null) {
                String[] params = query.split("&"); // 按 "&" 分割多个参数
                for (String param : params) {
                    String[] keyValue = param.split("="); // 按 "=" 分割键值对
                    if (keyValue.length == 2 && "resourceId".equals(keyValue[0])) {
                        resourceId = keyValue[1];
                        break;
                    }
                }
            }
            attachment.setOssResourceId(resourceId);
            attachment.setDomain(host);
            attachment.setFilePath(pathAndQuery);
        } catch (Exception e) {
            log.error("双边盖章合同上传erp失败",e);
            return null;
        }
        attachmentList.add(attachment);
        return attachmentList;
    }



    private PurchaseContractDto buildPurchaseContractDto(Buyorder buyorder ,String fileUrl) {
        PurchaseContractDto purchaseContractDto = new PurchaseContractDto();
        purchaseContractDto.setBuyorderId(buyorder.getBuyorderId());
        purchaseContractDto.setBuyorderNo(buyorder.getBuyorderNo());
        purchaseContractDto.setAltType(1);
        List<Attachment> attachmentList = buildAttachmentList(buyorder ,fileUrl);
        purchaseContractDto.setAttachmentList(attachmentList);
        return purchaseContractDto;
    }



    @Override
    protected void postMqProcessors(SignCallbackDto signCallbackDto) {

    }


    @Override
    protected void dealWithByMqException(String errorMsg, SignCallbackDto signCallbackDto) {
        log.error("Mq消费失败，补偿任务触发[{}]" , JSON.toJSONString(signCallbackDto));
        if (signCallbackDto.getBusinessType() == 2) {
            Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(signCallbackDto.getOrderNo());
            // 置空合同半程盖章字段
            buyorderMapper.updateContractUrlOfBuyorder(buyorder.getBuyorderId(), null);
        }
    }

    private static final String SEAL_NAME = "合同专用章";

    private static final String HIDE_NAME_JIA = "$yi$";

    private static final String HIDE_NAME_YI = "$yifang$";

    @Value("${erpCompanyName:南京贝登医疗股份有限公司}")
    private String erpCompanyName;

    @Override
    protected void getSignCompanyInfoList(ElectronicSignParam electronicSignParam, List<SignCompanyInfo> signCompanyInfoList) {
        Integer buyorderId = electronicSignParam.getBuyOrderId();
        Buyorder buyorder = buyorderMapper.getBuyorderVoById(buyorderId);
        if (buyorder == null) {
            log.error("发起电子签单时，多主体，该订单未找到");
            return;
        }

        //甲方
        SignCompanyInfo signCompanyInfo = new SignCompanyInfo();
        signCompanyInfo.setSignCompanyName(erpCompanyName);
        signCompanyInfo.setHideCompanyName(HIDE_NAME_JIA);
        signCompanyInfo.setSignAllSeal(true);
        signCompanyInfo.setSignSealName(SEAL_NAME);
        signCompanyInfoList.add(signCompanyInfo);

        //已方
        SignCompanyInfo signCompanyInfoYi = new SignCompanyInfo();
        signCompanyInfoYi.setSignCompanyName(buyorder.getTraderName());
        signCompanyInfoYi.setHideCompanyName(HIDE_NAME_YI);
        signCompanyInfoYi.setSignAllSeal(true);
        signCompanyInfoYi.setSignSealName(SEAL_NAME);
        signCompanyInfoList.add(signCompanyInfoYi);


    }

    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;


    private boolean checkSignCompanyInfo(Integer buyorderId) {
        Buyorder buyorder = buyorderMapper.getBuyorderVoById(buyorderId);
        if (buyorder == null) {
            log.error("发起电子签单时，多主体，该订单未找到");
            return false;
        }
        List<String> companyNames=  new ArrayList<>();
        companyNames.add(erpCompanyName);
        companyNames.add(buyorder.getTraderName());

        List<BaseCompanyInfoDto> companyInfoDtos = baseCompanyInfoApiService.selectBaseCompanyByCompanyNames(companyNames);
        if(companyInfoDtos != null && companyInfoDtos.size()>=2){
            log.info("采购订单：{}符合盖双章的条件，进行双章盖章",buyorderId);
            return true;//直接直接需要改双章
        }
        log.info("采购订单：{}不符合盖双章的条件。",buyorderId);
        return false;
    }

    public ElectronicSignParam buildElectronicSignParam(Integer buyorderId, BusinessInfo businessInfo) {
        if(checkSignCompanyInfo(buyorderId)){
            ElectronicSignParam electronicSignParam = ElectronicSignParam
                    .builder()
                    .buyOrderId(buyorderId)
                    .flowType(3) // 3 表示多主体印章盖章
                    .businessInfo(businessInfo)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.MULT_BUYORDER_SEAL)
                    .build();
            return electronicSignParam;
        }else{
            ElectronicSignParam electronicSignParam = ElectronicSignParam
                    .builder()
                    .buyOrderId(buyorderId)
                    .flowType(2)
                    .businessInfo(businessInfo)
                    .electronicSignBusinessEnums(ElectronicSignBusinessEnums.BUY_ORDER)
                    .build();
            return electronicSignParam;
        }
    }

}
