package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BuyOrderKingDeeDto {

    /**
     * 采购单Id
     */
    private Integer buyorderId;

    private String buyorderNo;

    private Long validTime;

    private String takeTraderContactName;

    private String takeTraderContactMobile;

    private String takeTraderContactTelephone;

    private String takeTraderArea;

    private String takeTraderAddress;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 现采购单
     */
    private Integer traderId;

    /**
     * 主键
     */
    private Long buyorderActualSupplierId;

    /**
     * 交易者ID
     */
    private Integer actualTraderId;
    /**
     * 供应商ID
     */
    private Integer actualTraderSupplierId;

    /**
     * 交易者名称
     */
    private String actualTraderName;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 手机
     */
    private String traderContactMobile;

    /**
     * 电话
     */
    private String traderContactTelephone;

    /**
     * 付款方式 字典库
     */
    private Integer paymentType;

    /**
     * 预付金额
     */
    private BigDecimal prepaidAmount;

    /**
     * 账期支付金额
     */
    private BigDecimal accountPeriodAmount;

    /**
     * 尾款
     */
    private BigDecimal retainageAmount;

    /**
     * 尾款期限(月)
     */
    private Integer retainageAmountMonth;

    /**
     * 发票类型
     */
    private Integer actualInvoiceType;

    /**
     * 是否需要发票
     */
    private Integer needInvoice;

}
