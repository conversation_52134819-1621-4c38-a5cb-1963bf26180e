package com.vedeng.trader.group.visit;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.track.api.dto.business.req.BusinessReqDto;
import com.vedeng.track.api.dto.business.res.TrackResDto;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.group.BaseBehavior;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;

/**
 * <b>Description:</b>最近无访问<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class NoVisitBehavior extends TrackBaseBehvior {
    private Logger logger= LoggerFactory.getLogger(NoVisitBehavior.class);

    @Override
    public BitSet calculate() {
        BusinessReqDto reqDto=new BusinessReqDto();
        reqDto.setDayType(getPeriod());
        BitSet middleResult=getVisitData(reqDto);
        BitSet result=getBaseTraderSet();
        result.andNot(middleResult);
        return result;
    }
}
