<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSalesTraderMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSalesTrader" >
    <id column="AFTER_SALES_TRADER_ID" property="afterSalesTraderId" jdbcType="INTEGER" />
    <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="REAL_TRADER_TYPE" property="realTraderType" jdbcType="BIT" />
    <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    AFTER_SALES_TRADER_ID, AFTER_SALES_ID, TRADER_ID, REAL_TRADER_TYPE, TRADER_TYPE, TRADER_NAME, COMMENTS, 
    IS_ENABLE, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_TRADER
    where AFTER_SALES_TRADER_ID = #{afterSalesTraderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_AFTER_SALES_TRADER
    where AFTER_SALES_TRADER_ID = #{afterSalesTraderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.aftersales.model.AfterSalesTrader" >
    insert into T_AFTER_SALES_TRADER (AFTER_SALES_TRADER_ID, AFTER_SALES_ID, 
      TRADER_ID, REAL_TRADER_TYPE, TRADER_TYPE, TRADER_NAME, 
      COMMENTS, IS_ENABLE, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{afterSalesTraderId,jdbcType=INTEGER}, #{afterSalesId,jdbcType=INTEGER}, 
      #{traderId,jdbcType=INTEGER}, #{realTraderType,jdbcType=BIT}, #{traderType,jdbcType=BIT}, #{traderName,jdbcType=VARCHAR}, 
      #{comments,jdbcType=VARCHAR}, #{isEnable,jdbcType=BIT}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.AfterSalesTrader" >
    insert into T_AFTER_SALES_TRADER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="afterSalesTraderId != null" >
        AFTER_SALES_TRADER_ID,
      </if>
      <if test="afterSalesId != null" >
        AFTER_SALES_ID,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="realTraderType != null" >
        REAL_TRADER_TYPE,
      </if>
      <if test="traderType != null" >
        TRADER_TYPE,
      </if>
      <if test="traderName != null" >
        TRADER_NAME,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="afterSalesTraderId != null" >
        #{afterSalesTraderId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null" >
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="realTraderType != null" >
        #{realTraderType,jdbcType=BIT},
      </if>
      <if test="traderType != null" >
        #{traderType,jdbcType=BIT},
      </if>
      <if test="traderName != null" >
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.AfterSalesTrader" >
    update T_AFTER_SALES_TRADER
    <set >
      <if test="afterSalesId != null" >
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="realTraderType != null" >
        REAL_TRADER_TYPE = #{realTraderType,jdbcType=BIT},
      </if>
      <if test="traderType != null" >
        TRADER_TYPE = #{traderType,jdbcType=BIT},
      </if>
      <if test="traderName != null" >
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where AFTER_SALES_TRADER_ID = #{afterSalesTraderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.AfterSalesTrader" >
    update T_AFTER_SALES_TRADER
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      REAL_TRADER_TYPE = #{realTraderType,jdbcType=BIT},
      TRADER_TYPE = #{traderType,jdbcType=BIT},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where AFTER_SALES_TRADER_ID = #{afterSalesTraderId,jdbcType=INTEGER}
  </update>
  
   <select id="getAfterSalesTraderList" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_TRADER
    where IS_ENABLE = 1 and AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>
  
  <select id="getAfterSalesTrader" resultType="int" parameterType="com.vedeng.aftersales.model.AfterSalesTrader" >
    select 
    COUNT(1)
    from T_AFTER_SALES_TRADER
    where IS_ENABLE = 1 AND TRADER_TYPE = #{traderType,jdbcType=BIT} 
    		AND TRADER_NAME = #{traderName,jdbcType=VARCHAR}
    		AND AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>
</mapper>