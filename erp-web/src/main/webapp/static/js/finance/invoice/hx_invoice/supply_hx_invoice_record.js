/**
 * 删除一条录票暂存信息
 * @param invoiceEntryStashId
 * <AUTHOR>
 * @date 2020/5/27 10:14:16
 */
function deleteInvoiceEntryStash(invoiceEntryStashId) {
    layer.confirm('确定删除该录票数据吗？', {title: '删除录票数据'}, function (index) {
        $.ajax({
            url: '/supplyChain/invoice/deleteInvoiceEntryStash.do',
            data: {
                invoiceEntryStashId: invoiceEntryStashId
            },
            type: 'post',
            dataType: 'json',
            success: function (res) {
                if (res.code == 0) {
                    layer.alert('删除成功', function () {
                        window.location.reload();
                    });
                } else {
                    layer.alert('删除失败');
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }, function () {
        layer.close(index);
    })
}

/**
 * @desc 修改商品的本次录票数量
 * @param invoiceEntryStashId
 * @param hxInvoiceDetailId
 * <AUTHOR>
 * @date 2020/5/28 10:17:16
 */
function changeRecordNum(invoiceEntryStashId, hxInvoiceDetailId) {
    //1.校验录票数量范围 0 - 已入库数量
    var thisEntryCount = parseInt($('#hasEntryCount' + invoiceEntryStashId).val());
    var canRecordNum = parseInt($('#arrivalNum_' + invoiceEntryStashId).html());
    if (thisEntryCount > 0 && thisEntryCount <= canRecordNum){
        //2.更新相关金额
        var hasEntryCount = parseFloat($('#hasEntryCount' + invoiceEntryStashId).val());
        var price = parseFloat($('#price' + invoiceEntryStashId).html());
        $('#invoiceDetailAmountStash' + invoiceEntryStashId).html(hasEntryCount * price);
        flushAmountData();
    } else {
        layer.alert('该商品本次录票数量范围为 0 - ' + canRecordNum , function () {
            $('#hasEntryCount' + invoiceEntryStashId).val(canRecordNum);
            layer.closeAll();
        });
    }
}

/**
 * @describe 初始化页面以及组件
 * <AUTHOR>
 * @date 2020/6/1 15:30:00
 */
$(function () {
    $('.J-list').sortable({
        axis: 'y',
        container: $('.J-list'),
        cursorAt: {left: 22, top: 32},
        revert: 100,
        distance: 10,
        handle: ".J-item-sort",
        tolerance: "pointer",
        scroll: false,
        items: ".J-item, .J-place",
        opacity: 0.75,
        placeholder: "placeholder",
        start: function () {
            // $('.J-place').show();
        },
        stop: function () {
            flushAmountData();
        }
    });
});

/**
 * 刷新相关金额
 */
function flushAmountData() {
    var map = new Map();
    $('.J-item').each(function () {
        var $parents = $(this).prevAll('.J-skuno:first');
        var hxInvoiceDetailId = $parents.data('id');
        console.log('hxInvoiceDetailId' + hxInvoiceDetailId);
        var invoiceEntryStashId = $(this).data('id');
        var recordedAmount = parseFloat($('#invoiceDetailAmountStash' + invoiceEntryStashId).html());
        if (map.has(hxInvoiceDetailId)) {
            map.set(hxInvoiceDetailId, map.get(hxInvoiceDetailId) + recordedAmount);
        } else {
            map.set(hxInvoiceDetailId, recordedAmount);
        }
    });

    var hxInvoiceDetailIds = [];
    $('.hxInvoiceDetailIdFlag').each(function () {
        hxInvoiceDetailIds.push($(this).val());
    });
    console.log("hxInvoiceDetailIds" + hxInvoiceDetailIds);

    var totalRecordAmount = 0;

    map.forEach(function (value, key) {
        console.log(value, key);
        $('#recordAmount' + key).html(value);
        $('#canRecordAmount' + key).html(parseFloat($('#amount' + key).html()) - value);
        totalRecordAmount += value;
         if (parseFloat($('#amount' + key).html()) - value < 0){
           $('#canRecordAmount' + key).css('color', 'red');
       } else {
           $('#canRecordAmount' + key).css('color', 'black');
       }
    });

    for (var index = 0; index < hxInvoiceDetailIds.length; index++) {
        var detailId = parseInt(hxInvoiceDetailIds[index]);
        if (!map.has(detailId)) {
            console.log('不存在的ID' + detailId);
            $('#recordAmount' + detailId).html(0.00);
            $('#canRecordAmount' + detailId).html(parseFloat($('#amount' + detailId).html()));
            $('#canRecordAmount' + detailId).css('color', 'black');
        }
    }
    $('#hxInvoiceRecordedAmount').html(totalRecordAmount.toFixed(2));
    $('#canRecordAmount').html((parseFloat($('#hxInvoiceAmount').html()) - totalRecordAmount.toFixed(2)).toFixed(2));
}

/**
 * @describe 清空当前发票的已录票信息
 * @param hxInvoiceId 发票ID
 * <AUTHOR>
 * @date 2020/6/1 15:41:22
 */
function cleanInvoiceStash(hxInvoiceId) {
    layer.confirm('确定清空该发票的已录票数据吗？？', {title: '清空录票数据'}, function (index) {
        $.ajax({
            url: '/supplyChain/invoice/deleteInvoiceDetails.do',
            data: {
                hxInvoiceId: hxInvoiceId
            },
            type: 'post',
            dataType: 'json',
            success: function (res) {
                if (res.code == 0) {
                    layer.alert('清空成功', function () {
                        window.location.reload();
                    });
                } else {
                    layer.alert('清空失败');
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }, function () {
        layer.close(index);
    })
}

/**
 * @describe 保存发票的暂存信息
 * <AUTHOR>
 * @date 2020/6/1 15:42:30
 */
function saveInvoiceEntryStash(hxInvoiceId) {
    var result = getResult(hxInvoiceId);
    $.ajax({
        url: '/supplyChain/invoice/saveInvoiceEntryStash.do',
        data: JSON.stringify(result),
        type: 'post',
        dataType: 'json',
        contentType: 'application/json;charset=utf-8',
        success: function (res) {
            if (res.code == 0) {
                layer.alert('暂存发票信息成功', function () {
                    window.location.reload();
                });
            } else {
                layer.alert(res.message);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}

/**
 * @describe 同步发票详情信息
 * <AUTHOR>
 * @date 2020/6/2 13:51:00
 */
function saveHxInvoiceDetail(hxInvoiceId) {
    if (parseFloat($('#hxInvoiceAmount').html()) != parseFloat($('#hxInvoiceRecordedAmount').html())){
        layer.confirm('已录票金额≠发票总额，确认提交审核吗？', {title: '提交录票数据'}, function (index) {
            var result = getResult(hxInvoiceId);
            doSaveHxInvoiceDetail(result);
        })
    } else {
        layer.confirm('确认提交审核吗？', {title: '提交录票数据'}, function (index) {
            var result = getResult(hxInvoiceId);
            doSaveHxInvoiceDetail(result);
        })
    }
}

/**
 * @describe 获取页面拖动结果
 * <AUTHOR>
 * @date 2020/6/1 15:46:03
 */
function getResult(hxInvoiceId) {
    var details = [];
    var setData = function () {
        $('.J-item').each(function () {
            var $parents = $(this).prevAll('.J-skuno:first');
            var detailId = $parents.data('id');
            var invoiceEntryStashId = $(this).data('id');
            var hasEntryCount = $('#hasEntryCount' + invoiceEntryStashId).val();
            details.push({
                hxInvoiceDetailId: detailId,
                hasEntryCount: hasEntryCount,
                invoiceEntryStashId: invoiceEntryStashId,
                hxInvoiceId: hxInvoiceId
            })
        })
    };
    setData();
    console.log(details)
    return details;
}

/**
 *@describe 选择录票的采购订单商品
 * @param specification
 * @param salerName
 * @param taxRate
 * @param price
 * @param createTime
 * @param hxInvoiceDetailId
 * @param hxInvoiceId
 * <AUTHOR>
 * @date 2020/6/8 16:08:28
 */
function choiceHxInvoice(specification, salerName, taxRate, price, createTime, hxInvoiceDetailId, hxInvoiceId) {
    $.ajax({
        url: '/supplyChain/invoice/supply_hx_invoice_goods_choice.do',
        data: {
            specification: specification,
            salerName: salerName,
            taxRate: taxRate,
            price: price,
            createTime: createTime,
            invoiceDetailId: hxInvoiceDetailId,
            hxInvoiceId: hxInvoiceId
        },
        type: 'post',
        dataType: "html",
        success: function (res) {
            layer.open({
                type: 1,
                shade: 0.1,
                area: ['1600px', '900px'],
                title: '选择录票商品',
                content: res,
                success: function (layero, index) {

                }
            });
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}

/**
 * @describe 请求保存录票相关信息
 * @param result 请求参数
 * <AUTHOR>
 * @date 2020/07/06 16:58:28
 */
function doSaveHxInvoiceDetail(result) {
    $.ajax({
        url: '/supplyChain/invoice/saveHxInvoiceDetail.do',
        data: JSON.stringify(result),
        type: 'post',
        dataType: 'json',
        contentType: 'application/json;charset=utf-8',
        success: function (res) {
            if (res.code == 0) {
                layer.alert('保存发票信息成功', function () {
                    window.location.reload();
                });
            } else {
                layer.alert(res.message);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}