<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.ProcinstMapper" >

  <update id="updateActHiActinstInfoByTaskId" parameterType="java.util.Map" >
    update ACT_HI_ACTINST
    <set>
      <if test="ACT_NAME_ != null">
        ACT_NAME_ = #{ACT_NAME_,jdbcType=VARCHAR},
      </if>
      <if test="END_TIME_ != null">
        END_TIME_ = #{END_TIME_,jdbcType=TIMESTAMP},
      </if>
      <if test="ASSIGNEE_ != null">
        ASSIGNEE_ = #{ASSIGNEE_,jdbcType=VARCHAR},
      </if>
    </set>
    where
    TASK_ID_ = #{TASK_ID_,jdbcType=VARCHAR}
  </update>

  <update id="updateActHiTaskinstInfoById" parameterType="java.util.Map" >
    update ACT_HI_TASKINST
    <set>
      <if test="NAME_ != null">
        NAME_ = #{NAME_,jdbcType=VARCHAR},
      </if>
      <if test="END_TIME_ != null">
        END_TIME_ = #{END_TIME_,jdbcType=TIMESTAMP},
      </if>
      <if test="ASSIGNEE_ != null">
        ASSIGNEE_ = #{ASSIGNEE_,jdbcType=VARCHAR},
      </if>
    </set>
    where
    ID_ = #{TASK_ID_,jdbcType=VARCHAR}
  </update>


  <select id="getBuyOrderIdsByCurrentOperateUser" resultType="java.lang.String" parameterType="java.util.Map" >
  	select PROC_INST_ID_ from ACT_HI_ACTINST where END_TIME_ is null and ASSIGNEE_ like concat('%',#{currentOperateUser,jdbcType=VARCHAR},'%')  and ( PROC_DEF_ID_ like 'buyorderVerify%' or PROC_DEF_ID_ like 'paymentVerify%' )
  </select>


    <select id="getBuyOrderIdsByProcessIds" resultType="java.lang.String" parameterType="java.util.Map" >
      select BUSINESS_KEY_ from ACT_HI_PROCINST where PROC_INST_ID_ in
        <foreach item="processId" index="index" collection="processIds" separator="," open="(" close=")">
            #{processId,jdbcType=INTEGER}
        </foreach>
  </select>

  <select id="getActHiInfoByTaskId" resultType="java.util.Map" parameterType="java.util.Map" >
      select ID_,TASK_ID_,END_TIME_,ASSIGNEE_,ACT_NAME_ from ACT_HI_ACTINST where TASK_ID_ = #{taskId,jdbcType=VARCHAR}
  </select>


</mapper>