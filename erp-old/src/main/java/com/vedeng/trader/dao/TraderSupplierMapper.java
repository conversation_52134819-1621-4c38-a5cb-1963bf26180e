package com.vedeng.trader.dao;


import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.model.vo.TraderVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TraderSupplierMapper {
    int deleteByPrimaryKey(Integer traderSupplierId);

    int insert(TraderSupplier record);

    int insertSelective(TraderSupplier record);

    TraderSupplier selectByPrimaryKey(Integer traderSupplierId);

    int updateByPrimaryKeySelective(TraderSupplier record);

    int updateByPrimaryKey(TraderSupplier record);

    TraderSupplier getSuplierInfoByTraderId(Integer traderId);

    TraderSupplier getSupplierInfoByTraderName(String traderName);

    TraderVo getTraderSupplierByTraderId(Integer traderId);

    List<Integer> getTraderIdListByContactWay(TraderContact traderContact);

    List<TraderSupplierVo> getTraderSupplierVolistpage(Map<String, Object> map);

    List<TraderSupplierVo> getTraderSupplierListQualificationExpirationListPage(Map<String, Object> map);

    /**
     * <b>Description:</b><br> 批量查询交易信息
     *
     * @param traderSupplierVos
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2018年2月9日 上午11:27:16
     */
    List<TraderSupplierVo> batchTraderBussinessData(@Param("traderSupplierVos") List<TraderSupplierVo> traderSupplierVos);

    /**
     * <b>Description:</b><br> 获取供应商账期信息（使用次数，账期信息）
     *
     * @param traderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月6日 下午3:23:27
     */
    List<TraderSupplierVo> getTraderSupplierAccountPeriodInfo(@Param("traderId") Integer traderId);

    /**
     * <b>Description:</b><br> 供应商采购信息
     *
     * @param traderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年7月5日 下午4:12:09
     */
    TraderSupplierVo getSupplierBuyorderInfo(@Param("traderId") Integer traderId);

    /**
     * <b>Description:</b><br> 获取账期未还金额
     *
     * @param traderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月1日 上午8:58:18
     */
    BigDecimal getTraderSupplierUsedAccountPeriodAmount(@Param("traderId") Integer traderId);

    /**
     * <b>Description:</b><br> 获取订单账期占用金额（未支付）
     *
     * @param traderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月1日 上午8:58:34
     */
    BigDecimal getTraderSupplierZYAccountPeriodAmount(@Param("traderId") Integer traderId);

    /**
     * 采购费用账期
     * @param traderId
     * @return
     */
    BigDecimal getTraderSupplierCGFYZYAccountPeriodAmount(Integer traderId);


    /**
     * 采购费用偿还账期
     * @param traderId
     * @return
     */
    BigDecimal getCGFYReturnPeriodAmount(Integer traderId);

    /**
     * <b>Description:</b><br> 获取账期使用次数
     *
     * @param traderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年11月1日 上午8:58:05
     */
    Integer getTraderSupplierAccountPeriodTimes(@Param("traderId") Integer traderId);

    /**
     * <b>Description:</b><br> 基本信息
     *
     * @param traderSupplier
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年5月18日 上午11:13:56
     */
    TraderSupplierVo getTraderSupplierBaseInfo(TraderSupplier traderSupplier);

    /**
     * <b>Description:</b><br> 获取供应商基本表信息
     *
     * @param traderId
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年7月5日 下午5:30:55
     */
    TraderSupplierVo getSupplierByTraderId(@Param("traderId") Integer traderId);


    /**
     * 根据供应商交易者ID or供应商ID获取供应商基本表信息
     *
     * @param traderSupplier
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年7月28日 上午9:34:36
     */
    TraderSupplierVo getTraderSupplier(TraderSupplier traderSupplier);

    /**
     * <b>Description:</b><br> 编辑供应商
     *
     * @param traderSupplier
     * @return
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年5月18日 下午1:30:46
     */
    int updateByPrimaryKeySelectiveDB(TraderSupplier traderSupplier);

    TraderSupplier getTraderSupplierByTraderName(String traderName);

    TraderSupplier getOneByName(@Param("productNameToUse") String productNameToUse);

    Integer getSupplierIdByProductCompanyId(@Param("productCompanyId") Integer productCompanyId);

    /**
     * 根据名称和类型获取供应商
     *
     * @param traderName
     * @param traderType
     * @return
     */
    List<TraderSupplier> getTraderSupplierByTraderNameAndTraderType(@Param("traderName") String traderName, @Param("traderType") Integer traderType);

    /**
     * 根据类型获取供应商
     *
     * @param traderType
     * @return
     */
	List<TraderSupplier> getAllByTraderType(@Param("traderType")Integer traderType);


    List<Long> traderCertificateOverdue(Integer traderId);
}