package com.vedeng.erp.aftersale.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesToYxbDto;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesToYxbEntity;

import java.util.List;

/**
 * 售后单单推送医修帮
 */
public interface AfterSalesToYxbApiService {

    /**
     * 用于给售后单判断是否显示下派按钮
     */
    List<AfterSalesToYxbDto> selectByConditionDispatch(AfterSalesToYxbEntity afterSalesToYxbEntity);
    /**
     * 用于插入医修帮接口推送结果数据
     */
    int insertCancelAfterSalesToYxb(AfterSalesToYxbEntity afterSalesToYxbEntity);
    /**
     * 用于取消下派后把所有的下派成功数据软删除
     */
    int logicDeleteAfterCancel(Integer afterSalesId);

    /**
     * 用于给定时任务查询推送成功的但没有关闭和完结的单子
     */
    PageInfo<AfterSalesToYxbDto> selectByForDownTask(Page page);

    /**
     * 用于给定时任务查询所有推送成功的单子
     */
    PageInfo<AfterSalesToYxbDto> selectForPayPushTask(Page page);


}
