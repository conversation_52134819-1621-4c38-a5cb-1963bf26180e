package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeStorageOutMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageOutCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageOutConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 领用出库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchReceiveWriter extends BaseWriter<KingDeeStorageOutDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private KingDeeStorageOutMapper kingDeeStorageOutMapper;
    @Autowired
    private KingDeeStorageOutConvertor kingDeeStorageOutConvertor;
    @Autowired
    private KingDeeStorageOutCommandConvertor commandConvertor;

    @Override
    public void doWrite(KingDeeStorageOutDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("BatchReceiveWriterService#doWrite,领用出库{}", JSON.toJSONString(dto));
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(commandConvertor.toCommand(dto), dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            dto.setFId(successEntity.getId());
            kingDeeStorageOutMapper.insertSelective(kingDeeStorageOutConvertor.toEntity(dto));
        }
    }

}
