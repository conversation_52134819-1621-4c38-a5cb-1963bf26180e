<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>(订单流)销售售后列表</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="Jb8cQU_twaQ" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab">
<!--     <li><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
<!--     <li class="layui-this"><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->
    </ul>
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">售后单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="AFTER_SALES_NO" placeholder="请输入售后单号" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">所属销售单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="ORDER_NO" placeholder="请输入销售单号" style="" alias="a" jdbctype="" data="" datatype="" oper="EQ"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="请输入客户名称" style="" alias="t" jdbctype="" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">订单状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="ATFER_SALES_STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="EQ"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">完结/关闭时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="CLOSE_TIME" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.`ATFER_SALES_STATUS` = 2 or a.`ATFER_SALES_STATUS` = 3, a.`MOD_TIME` / 1000, NULL),                      '%Y-%m-%d %H:%i:%s')" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">生效时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="VALID_TIME" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.VALID_TIME = 0, NULL, a.VALID_TIME) / 1000, '%Y-%m-%d')" jdbctype="DATE" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="ADD_TIME" placeholder="2015-10-02" style="" alias="FROM_UNIXTIME(IF(a.ADD_TIME= 0, NULL, a.ADD_TIME) / 1000, '%Y-%m-%d')" jdbctype="DATE" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">售后处理人</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="SERVICE_USER_ID" placeholder="全部" style="" alias="a" jdbctype="" data="select DISTINCT a.USER_ID K, lower(a.USERNAME) V from T_USER a          left join T_R_USER_POSIT b on a.USER_ID = b.USER_ID          left join T_POSITION d                    ON d.POSITION_ID = b.POSITION_ID where d.TYPE = 312   and a.COMPANY_ID = 1   and a.IS_DISABLED = 0 ORDER BY a.USERNAME" datatype="KVSQL" oper="EQ"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">申请人</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="CREATOR" placeholder="全部" style="" alias="a" jdbctype="" data="select DISTINCT a.USER_ID K, lower(a.USERNAME) V from T_USER a          left join T_R_USER_POSIT b on a.USER_ID = b.USER_ID          left join T_POSITION d                    ON d.POSITION_ID = b.POSITION_ID where d.TYPE = 310   and a.COMPANY_ID = 1   and a.IS_DISABLED = 0 ORDER BY a.USERNAME" datatype="KVSQL" oper="EQ"></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">开票状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="INVOICE_MAKEOUT_STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;无开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未开票 &quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">退票状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="INVOICE_REFUND_STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;无退票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未退票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分退票&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部退票&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">合并日期搜索</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="search-date" name="CLOSE_TIME,VALID_TIME,ADD_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">处理状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="HANDLE_STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;无处理&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未处理&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分处理&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部处理&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">付款状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="AMOUNT_PAY_STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;无付款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未付款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分付款&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部付款&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">退款状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="AMOUNT_REFUND_STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;无退款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未退款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分退款&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部退款&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">收款状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="AMOUNT_COLLECTION_STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;无收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    
    <div class=" layui-inline "><label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="USER_ID" placeholder="全部" style="" alias="ts" jdbctype="" data="select DISTINCT a.USER_ID K, lower(a.USERNAME) V from T_USER a          left join T_R_USER_POSIT b on a.USER_ID = b.USER_ID          left join T_POSITION d                    ON d.POSITION_ID = b.POSITION_ID where d.TYPE = 310   and a.COMPANY_ID = 1   and a.IS_DISABLED = 0 ORDER BY a.USERNAME" datatype="KVSQL" oper="IN"></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">生效状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="VALID_STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">审核状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="STATUS" placeholder="全部" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">产品名称</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="ALL_NAME" placeholder="请输入产品名称" style="" alias="SKU_NAME" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">订货号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="ALL_SKU" placeholder="请输入订货号" style="" alias="SKU_NAME" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>


    <div class=" layui-inline "><label class="layui-form-label">售后类型</label>
     <div class="layui-input-inline">
      <xm class=" layui-input list-search-item " type="select" name="TYPE" placeholder="全部" style="" alias="a" jdbctype="" data="SELECT SYS_OPTION_DEFINITION_ID K, TITLE V FROM T_SYS_OPTION_DEFINITION WHERE STATUS = 1   AND PARENT_ID IN (535, 537)   AND (RELATED_FIELD IS NULL or RELATED_FIELD = '')" datatype="KVSQLCACHE" oper="IN"></xm> 
     </div>
    </div>
    
    <div class=" layui-inline "><label class="layui-form-label">下次跟进时间</label>
      <div class="layui-input-inline">
       <object class=" layui-input list-search-item " type="daterange" name="NEXT_TIME" placeholder="2015-10-02" style="" alias="FUR.NEXT_TIME" jdbctype="" data="DATE" datatype="" oper="BETWEEN"></object>
      </div>
     </div>

   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> <object item_name="新增售后" name="新增售后" url="order/afterSalesCommon/addSelfAfterSalesAtWx.do?flag=at" opentype="PARENT" windowname="新增售后" style type="22">新增售后</object>
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="AFTER_SALES_NO" name="AFTER_SALES_NO" body="td-link-color" order="0" url="/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=${AFTER_SALES_ID}" opentype="PARENT" windowname="${AFTER_SALES_NO}" datatype="" data="" style="" head="18">售后单号</th>
      <th item_name="SOURCE" name="SOURCE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;ERP&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;耗材&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;贝登前台&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">来源</th>
      <th item_name="TYPE" name="TYPE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="SELECT SYS_OPTION_DEFINITION_ID K, TITLE V
FROM T_SYS_OPTION_DEFINITION
WHERE STATUS = 1
  AND PARENT_ID IN (535, 537)
  AND (RELATED_FIELD IS NULL or RELATED_FIELD = '')" style="" head="18">售后类型</th>
      <th item_name="ORDER_NO" name="ORDER_NO" body="td-link" order="0" url="/orderstream/saleorder/detail.do?saleOrderId=${ORDER_ID}&amp;scene=0" opentype="PARENT" windowname="${ORDER_NO}" datatype="" data="" style="" head="18">所属销售单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="${TRADER_NAME}" datatype="" data="" style="" head="18">客户名称</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">归属销售</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">归属部门</th>
      <th item_name="CREATOR" name="CREATOR" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQL" data="select USER_ID K ,USERNAME V from T_USER
union all
select 0 K,'-' V" style="" head="18">申请人</th>
      <th item_name="SERVICE_USER_ID" name="SERVICE_USER_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQL" data="select USER_ID K ,USERNAME V from T_USER" style="" head="18">售后处理人</th>
      <th item_name="ATFER_SALES_STATUS" name="ATFER_SALES_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" head="18">订单状态</th>
      <th item_name="VALID_STATUS" name="VALID_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" head="18">生效状态</th>
      <th item_name="STATUS" name="STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">审核状态</th>
      <th item_name="INVOICE_REFUND_STATUS" name="INVOICE_REFUND_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无退票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未退票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分退票&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部退票&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;-&quot;,&quot;K&quot;:&quot;&quot;}]" style="" head="18">退票状态</th>
      <th item_name="INVOICE_MAKEOUT_STATUS" name="INVOICE_MAKEOUT_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未开票 &quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;-&quot;,&quot;K&quot;:&quot;&quot;}]" style="" head="18">开票状态</th>
      <th item_name="AMOUNT_REFUND_STATUS" name="AMOUNT_REFUND_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无退款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未退款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分退款&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部退款&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;-&quot;,&quot;K&quot;:&quot;&quot;}]" style="" head="18">退款状态</th>
      <th item_name="AMOUNT_COLLECTION_STATUS" name="AMOUNT_COLLECTION_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;-&quot;,&quot;K&quot;:&quot;&quot;}]" style="" head="18">收款状态</th>
      <th item_name="AMOUNT_PAY_STATUS" name="AMOUNT_PAY_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无付款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未付款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分付款&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部付款&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;-&quot;,&quot;K&quot;:&quot;&quot;}]" style="" head="18">付款状态</th>
      <th item_name="HANDLE_STATUS" name="HANDLE_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无处理&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未处理&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;部分处理&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;全部处理&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;-&quot;,&quot;K&quot;:&quot;&quot;}]" style="" head="18">处理状态</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">申请时间</th>
      <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">生效时间</th>
      <th item_name="FINISH_TIME" name="FINISH_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">完结时间</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by a.ADD_TIME desc" groupby="">StringBuilder sql=new StringBuilder(" SELECT CASE
           WHEN a.ATFER_SALES_STATUS = 0 THEN 'orangecircle'
           WHEN a.ATFER_SALES_STATUS = 1 THEN 'greencircle'
           WHEN a.ATFER_SALES_STATUS = 2 THEN 'bluecircle'
           WHEN a.ATFER_SALES_STATUS = 3 THEN 'greycircle' END AS COLOR_CIRCLE,
       a.AFTER_SALES_ID,
       a.AFTER_SALES_NO,
       a.SOURCE * 1                                            as SOURCE,
       a.TYPE,
       a.ORDER_ID,
       ifnull(a.ORDER_NO, '-')                                 as ORDER_NO,
       t.TRADER_ID,
       t.TRADER_NAME,
       ifnull(ts.USERNAME, '-')                                as USERNAME,
       ifnull(ton.ORG_NAME, '-')                               as ORG_NAME,
       a.CREATOR                                               as CREATOR,
       a.SERVICE_USER_ID,
       a.ATFER_SALES_STATUS * 1                                as ATFER_SALES_STATUS,
       a.VALID_STATUS * 1                                      as VALID_STATUS,
       a.STATUS * 1                                            as STATUS,
       v.STATUS * 1                                            AS VERIFY_STATUS,
       a.INVOICE_REFUND_STATUS * 1                             as INVOICE_REFUND_STATUS,
       a.INVOICE_MAKEOUT_STATUS * 1                            as INVOICE_MAKEOUT_STATUS,
       a.AMOUNT_REFUND_STATUS * 1                              as AMOUNT_REFUND_STATUS,
       a.AMOUNT_COLLECTION_STATUS * 1                          as AMOUNT_COLLECTION_STATUS,
       a.AMOUNT_PAY_STATUS * 1                                 as AMOUNT_PAY_STATUS,
       a.HANDLE_STATUS * 1                                     as HANDLE_STATUS,
       FROM_UNIXTIME(IF(a.ADD_TIME = 0, NULL, a.ADD_TIME) / 1000,
                     '%Y-%m-%d %H:%i:%s')                      AS ADD_TIME,
       FROM_UNIXTIME(IF(a.VALID_TIME = 0, NULL, a.VALID_TIME) / 1000,
                     '%Y-%m-%d %H:%i:%s')                      AS VALID_TIME,
       FROM_UNIXTIME(IF(a.`ATFER_SALES_STATUS` = 2 or a.`ATFER_SALES_STATUS` = 3, a.`MOD_TIME` / 1000, NULL),
                     '%Y-%m-%d %H:%i:%s')                      AS FINISH_TIME
       
");
if ( isNotBlank("ALL_SKU") || isNotBlank("ALL_NAME")) {
sql.append ( ",SKU_NAME.ALL_SKU,
       SKU_NAME.ALL_NAME" );
}
sql.append("
FROM T_AFTER_SALES a
         LEFT JOIN T_AFTER_SALES_DETAIL b ON a.AFTER_SALES_ID = b.AFTER_SALES_ID
         LEFT JOIN T_TRADER t ON b.TRADER_ID = t.TRADER_ID
         LEFT JOIN T_SALEORDER tsa ON a.`ORDER_ID` = tsa.`SALEORDER_ID`
         LEFT JOIN T_R_TRADER_J_USER trj ON tsa.TRADER_ID = trj.TRADER_ID and trj.TRADER_TYPE = 1
         LEFT JOIN T_USER ts ON trj.`USER_ID` = ts.`USER_ID`
          LEFT JOIN T_SALEORDER_DATA sd ON sd.`SALEORDER_ID` = tsa.`SALEORDER_ID`
         LEFT JOIN T_ORGANIZATION ton ON sd.`CURRENT_ORG_ID` = ton.`ORG_ID`
         LEFT JOIN T_VERIFIES_INFO v ON a.AFTER_SALES_ID = v.RELATE_TABLE_KEY AND v.RELATE_TABLE = 'T_AFTER_SALES' AND
                                        v.VERIFIES_TYPE = 614
       LEFT JOIN (select AFTER_SALES_ID,
                           max(RECORD_ID) as RECORD_ID
                    from T_AFTER_SALES_FOLLOW_UP_RECORD
                    where IS_DELETE = 0 or IS_DELETE is null
                    group by AFTER_SALES_ID) R on R.AFTER_SALES_ID = a.AFTER_SALES_ID
         LEFT JOIN T_AFTER_SALES_FOLLOW_UP_RECORD FUR on FUR.RECORD_ID = R.RECORD_ID
");


if ( isNotBlank("ALL_SKU") || isNotBlank("ALL_NAME")) {
	sql.append ( " LEFT JOIN (select a.AFTER_SALES_ID,
                           GROUP_CONCAT(distinct SKU)                        as ALL_SKU,
                           GROUP_CONCAT(distinct vk.SKU_NAME SEPARATOR '||') as ALL_NAME
                    from T_AFTER_SALES_GOODS a
                             join T_GOODS b
                                  on a.GOODS_ID = b.GOODS_ID and b.SKU is not null
                             LEFT JOIN V_CORE_SKU vk ON vk.SKU_ID = b.GOODS_ID
                    group by a.AFTER_SALES_ID) SKU_NAME on a.AFTER_SALES_ID = SKU_NAME.AFTER_SALES_ID" );
}
sql.append("
where  (a.TYPE !=546 and a.TYPE != 547 and a.TYPE !=  548 and  a.TYPE != 549)
  and a.COMPANY_ID = 1
");



sql.append ( " and (1=2  " );


if ( isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
sql.append ( " or  sd.CURRENT_ORG_ID in (" );
sql.append ( $$("EZ_SESSION_ORG_IDS_LIST_KEY") );
sql.append (  ") " );
}

if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " or trj.USER_ID in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}

if ( !isNotBlank("EZ_SESSION_MY_USER_KEY","session") &amp;&amp; !isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
sql.append ( " or 1=1" );
}
sql.append (  ") " );
 

list=search(sql);
return list;</pre> count: <pre id="count" class="layui-code">count(1)</pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot">
   <script>

$.each($('tr td.ezadmin-td-307 a.ezopenbutton'),
function(index, item) {
	if (this.innerText == '-') {
		$(this).attr('item_url', '#');
	}
})

</script>
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>
