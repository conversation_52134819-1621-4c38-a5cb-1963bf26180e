<?xml version="1.0" encoding="UTF-8"?>
<!-- ~ Licensed to the Apache Software Foundation (ASF) under one ~ or more 
    contributor license agreements. See the NOTICE file ~ distributed with this 
    work for additional information ~ regarding copyright ownership. The ASF 
    licenses this file ~ to you under the Apache License, Version 2.0 (the ~ 
    "License"); you may not use this file except in compliance ~ with the License. 
    You may obtain a copy of the License at ~ ~ http://www.apache.org/licenses/LICENSE-2.0 
    ~ ~ Unless required by applicable law or agreed to in writing, ~ software 
    distributed under the License is distributed on an ~ "AS IS" BASIS, WITHOUT 
    WARRANTIES OR CONDITIONS OF ANY ~ KIND, either express or implied. See the 
    License for the ~ specific language governing permissions and limitations 
    ~ under the License. -->
<!-- 通过ServiceObjectSupplier参数指定SpringServletContextObjectSupplier类来获得Spring的ApplicationContext对象 -->
<serviceGroup>
	<service name="callapi">
	    <description>axis2与spring集成</description>
	    <!-- 通过ServiceObjectSupplier参数指定SpringServletContextObjectSupplier类来获得Spring的ApplicationContext对象 -->
	    <parameter name="ServiceObjectSupplier">
	        org.apache.axis2.extensions.spring.receivers.SpringServletContextObjectSupplier
	    </parameter>
	    <!--
	       SpringBeanName固定的不能改
	       CpWebService是spring中注册的实现类得id
	     -->
	    <parameter name="SpringBeanName">callSoap</parameter>
	    <!--
	    在这里最值得注意的是<messageReceivers>元素，该元素用于设置处理WebService方法的处理器。
	    例如，getGreeting方法有一个返回值，因此，需要使用可处理输入输出的RPCMessageReceiver类，
	    而update方法没有返回值，因此，需要使用只能处理输入的RPCInOnlyMessageReceiver类。
	     -->
	    <messageReceivers>
	        <messageReceiver mep="http://www.w3.org/2004/08/wsdl/in-only"
	                         class="org.apache.axis2.rpc.receivers.RPCInOnlyMessageReceiver"/>
	        <messageReceiver mep="http://www.w3.org/2004/08/wsdl/in-out"
	                         class="org.apache.axis2.rpc.receivers.RPCMessageReceiver"/>
	    </messageReceivers>
	</service>
	<service name="api">
	    <description>axis2与spring集成</description>
	    <!-- 通过ServiceObjectSupplier参数指定SpringServletContextObjectSupplier类来获得Spring的ApplicationContext对象 -->
	    <parameter name="ServiceObjectSupplier">
	        org.apache.axis2.extensions.spring.receivers.SpringServletContextObjectSupplier
	    </parameter>
	    <!--
	       SpringBeanName固定的不能改
	       CpWebService是spring中注册的实现类得id
	     -->
	    <parameter name="SpringBeanName">apiSoap</parameter>
	    <!--
	    在这里最值得注意的是<messageReceivers>元素，该元素用于设置处理WebService方法的处理器。
	    例如，getGreeting方法有一个返回值，因此，需要使用可处理输入输出的RPCMessageReceiver类，
	    而update方法没有返回值，因此，需要使用只能处理输入的RPCInOnlyMessageReceiver类。
	     -->
	    <messageReceivers>
	        <messageReceiver mep="http://www.w3.org/2004/08/wsdl/in-only"
	                         class="org.apache.axis2.rpc.receivers.RPCInOnlyMessageReceiver"/>
	        <messageReceiver mep="http://www.w3.org/2004/08/wsdl/in-out"
	                         class="org.apache.axis2.rpc.receivers.RPCMessageReceiver"/>
	    </messageReceivers>
	</service>
</serviceGroup>