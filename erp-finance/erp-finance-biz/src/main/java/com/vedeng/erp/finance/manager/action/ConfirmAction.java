package com.vedeng.erp.finance.manager.action;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.domain.context.InvoiceRedConfirmationContext;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import com.vedeng.erp.finance.manager.InvoiceRedConfirmationAction;
import com.vedeng.erp.finance.service.InvoiceRedConfirmationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 确认
 * @date 2023/10/17 11:15
 */
@Component
@Slf4j
public class ConfirmAction implements InvoiceRedConfirmationAction {

    @Autowired
    InvoiceRedConfirmationService invoiceRedConfirmationService;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void execute(InvoiceRedConfirmationStateEnum from, InvoiceRedConfirmationStateEnum to,
                        InvoiceRedConfirmationEvent event, InvoiceRedConfirmationContext context) {
        // 1. 更新确认单信息
        log.info("ConfirmAction入参{}", JSON.toJSONString(context));
        invoiceRedConfirmationService.modify(context.getInvoiceRedConfirmationDto());
    }

}
