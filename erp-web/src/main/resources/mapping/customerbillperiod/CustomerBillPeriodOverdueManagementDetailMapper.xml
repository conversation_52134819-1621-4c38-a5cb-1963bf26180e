<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.customerbillperiod.dao.CustomerBillPeriodOverdueManagementDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail">
    <id column="BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID" jdbcType="BIGINT" property="billPeriodOverdueManagementDetailId" />
    <result column="BILL_PERIOD_OVERDUE_MANAGEMENT_CODE" jdbcType="VARCHAR" property="billPeriodOverdueManagementCode" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="CUSTOMER_ID" jdbcType="BIGINT" property="customerId" />
    <result column="BILL_PERIOD_USE_DETAIL_ID" jdbcType="BIGINT" property="billPeriodUseDetailId" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="UNRETURNED_AMOUNT" jdbcType="DECIMAL" property="unreturnedAmount" />
    <result column="TYPE" jdbcType="TINYINT" property="type" />
    <result column="RELATED_ID" jdbcType="BIGINT" property="relatedId" />
    <result column="PARENT_MANAGEMENT_DETAIL_ID" jdbcType="BIGINT" property="parentManagementDetailId" />
    <result column="SETTLEMENT_PERIOD" jdbcType="INTEGER" property="settlementPeriod" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="OVERDUE_DAYS" jdbcType="INTEGER" property="overdueDays" />
    <result column="OVERDUE_AMOUNT" jdbcType="DECIMAL" property="overdueAmount" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID, BILL_PERIOD_OVERDUE_MANAGEMENT_CODE, COMPANY_ID, 
    CUSTOMER_ID, BILL_PERIOD_USE_DETAIL_ID, AMOUNT, UNRETURNED_AMOUNT, `TYPE`, RELATED_ID, 
    PARENT_MANAGEMENT_DETAIL_ID, SETTLEMENT_PERIOD, ADD_TIME, OVERDUE_DAYS, OVERDUE_AMOUNT, 
    MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL
    where BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID = #{billPeriodOverdueManagementDetailId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL
    where BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID = #{billPeriodOverdueManagementDetailId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID" keyProperty="billPeriodOverdueManagementDetailId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL (BILL_PERIOD_OVERDUE_MANAGEMENT_CODE, COMPANY_ID, 
      CUSTOMER_ID, BILL_PERIOD_USE_DETAIL_ID, AMOUNT, 
      UNRETURNED_AMOUNT, `TYPE`, RELATED_ID, 
      PARENT_MANAGEMENT_DETAIL_ID, SETTLEMENT_PERIOD, 
      ADD_TIME, OVERDUE_DAYS, OVERDUE_AMOUNT, 
      MOD_TIME)
    values (#{billPeriodOverdueManagementCode,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{customerId,jdbcType=BIGINT}, #{billPeriodUseDetailId,jdbcType=BIGINT}, #{amount,jdbcType=DECIMAL}, 
      #{unreturnedAmount,jdbcType=DECIMAL}, #{type,jdbcType=TINYINT}, #{relatedId,jdbcType=BIGINT}, 
      #{parentManagementDetailId,jdbcType=BIGINT}, #{settlementPeriod,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{overdueDays,jdbcType=INTEGER}, #{overdueAmount,jdbcType=DECIMAL}, 
      #{modTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID" keyProperty="billPeriodOverdueManagementDetailId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billPeriodOverdueManagementCode != null">
        BILL_PERIOD_OVERDUE_MANAGEMENT_CODE,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="billPeriodUseDetailId != null">
        BILL_PERIOD_USE_DETAIL_ID,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="unreturnedAmount != null">
        UNRETURNED_AMOUNT,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="parentManagementDetailId != null">
        PARENT_MANAGEMENT_DETAIL_ID,
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="overdueDays != null">
        OVERDUE_DAYS,
      </if>
      <if test="overdueAmount != null">
        OVERDUE_AMOUNT,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billPeriodOverdueManagementCode != null">
        #{billPeriodOverdueManagementCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="billPeriodUseDetailId != null">
        #{billPeriodUseDetailId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unreturnedAmount != null">
        #{unreturnedAmount,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="parentManagementDetailId != null">
        #{parentManagementDetailId,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="overdueDays != null">
        #{overdueDays,jdbcType=INTEGER},
      </if>
      <if test="overdueAmount != null">
        #{overdueAmount,jdbcType=DECIMAL},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail">
    update T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL
    <set>
      <if test="billPeriodOverdueManagementCode != null">
        BILL_PERIOD_OVERDUE_MANAGEMENT_CODE = #{billPeriodOverdueManagementCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="billPeriodUseDetailId != null">
        BILL_PERIOD_USE_DETAIL_ID = #{billPeriodUseDetailId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unreturnedAmount != null">
        UNRETURNED_AMOUNT = #{unreturnedAmount,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="parentManagementDetailId != null">
        PARENT_MANAGEMENT_DETAIL_ID = #{parentManagementDetailId,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD = #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="overdueDays != null">
        OVERDUE_DAYS = #{overdueDays,jdbcType=INTEGER},
      </if>
      <if test="overdueAmount != null">
        OVERDUE_AMOUNT = #{overdueAmount,jdbcType=DECIMAL},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
    </set>
    where BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID = #{billPeriodOverdueManagementDetailId,jdbcType=BIGINT}
  </update>

  <select id="getOverdueManageDetailByUseDetailIdList"
          resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail">
    select *
    from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL where BILL_PERIOD_USE_DETAIL_ID in
    <foreach collection="useDetailIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
    and TYPE in
    <foreach collection="typeList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="getOrderIdListByOverdueManageDetailIdList" resultType="java.lang.Long">
    select distinct ud.RELATED_ID
    from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL md join T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud on md.BILL_PERIOD_USE_DETAIL_ID = ud.BILL_PERIOD_USE_DETAIL_ID
    where md.BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID in
    <foreach collection="overdueManageDetailIdList" index="index" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <select id="getCustomerBillPeriodUseDetailIdHasOverdue" resultType="java.lang.Long">
    select distinct BILL_PERIOD_USE_DETAIL_ID
    from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL where TYPE in (1,2) and BILL_PERIOD_USE_DETAIL_ID in
    <foreach collection="useDetailIdList" index="index" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
     and OVERDUE_DAYS > 0
  </select>
  <select id="geNewestBillPeriodManagementCodeByOrderId" resultType="java.lang.String">
    select md.BILL_PERIOD_OVERDUE_MANAGEMENT_CODE
    from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL md
           join T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud on md.BILL_PERIOD_USE_DETAIL_ID = ud.BILL_PERIOD_USE_DETAIL_ID
    where ud.COMPANY_ID = #{companyId}
      and ud.CUSTOMER_ID = #{customerId}
      and ud.RELATED_ID = #{orderId} and ud.USE_TYPE = 1
    order by md.BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL_ID desc limit 1
  </select>
    <select id="getCustomerBillPeriodOverDueInfoByOverdueDay"
            resultType="com.vedeng.customerbillperiod.model.vo.CustomerBillPeriodOverdueManagementDetailVo">
      SELECT
        A.*,
        B.RELATED_ID saleorderId,
        DATEDIFF(CURDATE() ,DATE_ADD(DATE_ADD(FROM_UNIXTIME(A.ADD_TIME/1000,'%Y-%m-%d') ,INTERVAL 1 DAY),INTERVAL A.SETTLEMENT_PERIOD DAY)) dayNum
      FROM
        T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL A
          LEFT JOIN T_CUSTOMER_BILL_PERIOD_USE_DETAIL B ON A.BILL_PERIOD_USE_DETAIL_ID = B.BILL_PERIOD_USE_DETAIL_ID
      WHERE
        A.UNRETURNED_AMOUNT > 0
        AND A.TYPE IN ( 1, 2 )
        AND B.USE_TYPE = 1
        AND DATEDIFF(CURDATE() ,DATE_ADD(DATE_ADD(FROM_UNIXTIME(A.ADD_TIME/1000,'%Y-%m-%d') ,INTERVAL 1 DAY),INTERVAL A.SETTLEMENT_PERIOD DAY))
        <choose>
            <when test="dayNum != null and dayNum &lt; 0">
              BETWEEN #{dayNum} and 0
            </when>
            <otherwise>
              &gt; #{dayNum}
            </otherwise>
        </choose>


    </select>
  <select id="getOverdueManagementDetailListUnreturnedByOrderId"
          resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail">
    select md.*
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud
           join T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL md
                on ud.BILL_PERIOD_USE_DETAIL_ID = md.BILL_PERIOD_USE_DETAIL_ID
    where ud.USE_TYPE = 1
      and ud.COMPANY_ID = #{companyId}
      and ud.CUSTOMER_ID = #{customerId}
      and ud.RELATED_ID = #{orderId}
      and md.TYPE in (1, 2)
      and md.UNRETURNED_AMOUNT > 0
  </select>
  <select id="getOverdueAmountByBillPeriodIdList" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail">
    /*select sum(md.OVERDUE_AMOUNT) OVERDUE_AMOUNT, ud.BILL_PERIOD_ID BILL_PERIOD_USE_DETAIL_ID*/
    select sum(IF ( md.OVERDUE_DAYS > 0, md.UNRETURNED_AMOUNT, 0 )) OVERDUE_AMOUNT, ud.BILL_PERIOD_ID BILL_PERIOD_USE_DETAIL_ID
    from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL md
           join T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud on md.BILL_PERIOD_USE_DETAIL_ID = ud.BILL_PERIOD_USE_DETAIL_ID
    where ud.USE_TYPE in (1, 2)
      and ud.BILL_PERIOD_ID in
      <foreach collection="billPeriodIdList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=BIGINT}
      </foreach>
      and md.TYPE in (1, 2)
      and md.OVERDUE_DAYS > 0
    group by ud.BILL_PERIOD_ID
  </select>
  <select id="getCountOfOverdueGroupByBillPeriod" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail">
    select ud.BILL_PERIOD_ID as BILL_PERIOD_USE_DETAIL_ID, count(1) as OVERDUE_DAYS
    from T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL md
           join T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud on ud.BILL_PERIOD_USE_DETAIL_ID = md.BILL_PERIOD_USE_DETAIL_ID
    where ud.BILL_PERIOD_ID in
      <foreach collection="billPeriodIdList" index="index" item="item" open="(" close=")" separator=",">
        #{item,jdbcType=BIGINT}
      </foreach>
      and ud.USE_TYPE = 1
      and md.TYPE in (1, 2)
      and md.OVERDUE_DAYS > 0
    group by ud.BILL_PERIOD_ID
  </select>
</mapper>