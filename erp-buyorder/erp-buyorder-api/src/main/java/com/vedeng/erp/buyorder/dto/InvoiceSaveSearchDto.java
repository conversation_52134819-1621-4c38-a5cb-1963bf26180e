package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 发票保存搜索运输对象
 *
 * <AUTHOR>
 */
@Data
public class InvoiceSaveSearchDto implements Serializable {

    private String orderNo;

    private String model;

    private String goodsName;

    private String brandName;

    private String traderName;

    private Integer invoiceType;

    /**
     * 采购备注
     */
    private String insideComments;

    /**
     * 0全部  1属于采购 2不属于采购
     */
    private Integer isBelongBuyOrder = 0;
}
