package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 应收单：标准应收单   https://www.yuque.com/manhuo/gf1570/aiguep
 * 标准应收单关联的源单是销售出库库单和销售退货按，同一个接口，两种场景入参，请注意入参参数。
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@Getter
@Setter
@WriteBackField(needBackField = {"FID","FEntityDetail.FEntryID", "FEntityDetail.F_QZOK_BDDJHID"})
public class KingDeeReceiveCommonDto extends KingDeeMqBaseDto {


    /**
     * 0：表示新增，非0：云星空系统单据FID值，表示修改
     */
    @WriteBackField
    @KingDeeID
    private String fId;

    /**
     * 格式yyyy-MM-dd
     */
    private String fDate;

    /**
     * 贝登单据头ID号（预留）
     */
    @BusinessID("F_QZOK_BDDJTID")
    private String fQzokBddjtId;

    /**
     * 业务类型 默认BZ
     */
    private String fBusinessType;


    /**
     * 立账类型 默认填写1
     */
    private String fSetAccountType;
    /**
     * 明细
     */
    private List<KingDeeReceiveCommonDetailDto> fEntityDetail = new ArrayList<>();

    /**
     * 填单据类型编码，默认"YSD01_SYS"
     */
    private String fBillTypeId;

    /**
     * 填写客户编码
     */
    private String fCustomerId;

    /**
     * 结算组织 填写组织编码
     */
    private String fSettleOrgId;

    /**
     * 付款组织 填写组织编码
     */
    private String fPayOrgId;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.RECEIVE_EXPENSES;
    }

    public KingDeeReceiveCommonDto() {
        this.fBillTypeId = "YSD01_SYS";
        this.fBusinessType = "BZ";
        this.fSettleOrgId = KingDeeConstant.ORG_ID.toString();
        this.fPayOrgId = KingDeeConstant.ORG_ID.toString();
        this.fSetAccountType = "1";
    }

}
