package com.wms.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.activiti.dto.ActivitiNoticeInfoEntity;
import com.vedeng.common.activiti.service.ActivitiNoticeService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderGoodsAptitudeConstants;
import com.vedeng.common.constant.OrderGoodsAptitudeEnum;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.model.SimpleMedicalCategory;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.dao.WmsOutputOrderGoodsExtraMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.model.WmsOutputOrderGoodsExtra;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.OrderAssistantRelationDo;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.dao.TraderCertificateMapper;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderMedicalCategoryMapper;
import com.vedeng.trader.model.TraderCertificate;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.TraderMedicalCategory;
import com.vedeng.uac.api.dto.BatchMessageSendDto;
import com.wms.constant.VerifyStatusEnum;
import com.wms.constant.WmsOutputOrderTypeConstant;
import com.wms.dao.WmsOutputOrderExtraMapper;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsSampleOrderGoodsExtraMapper;
import com.wms.model.dto.AddSampleOutDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.vedeng.erp.wms.dto.WmsOutputOrderExtra;
import com.wms.model.po.WmsOutputOrderGoods;
import com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra;
import com.wms.service.WmsSampleOutService;
import com.wms.service.chain.Build.StepBuildFactory;
import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.HandlerStepContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class WmsSampleOutServiceImpl implements WmsSampleOutService {

    @Resource
    private WmsOutputOrderMapper outputOrderMapper;

    @Resource
    private WmsOutputOrderGoodsMapper wmsOutputOrderGoodsMapper;

    @Resource
    private WmsOutputOrderExtraMapper wmsOutputOrderExtraMapper;

    @Resource
    private WmsSampleOrderGoodsExtraMapper wmsSampleOrderGoodsExtraMapper;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Autowired
    private StepBuildFactory stepBuildFactory;

    @Resource
    private OrganizationMapper organizationMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    private TraderCertificateMapper traderCertificateMapper;

    @Autowired
    private TraderMedicalCategoryMapper traderMedicalCategoryMapper;

    @Autowired
    private FirstEngageMapper firstEngageMapper;

    @Autowired
    private SaleorderService saleorderService;

    private static final Integer KA_ORGID = 378;

    private static final Integer NEW_GOODS_TYPE = 4221;

    public static Logger logger = LoggerFactory.getLogger(WmsSampleOutServiceImpl.class);

    @Autowired
    private WmsOutputOrderGoodsExtraMapper wmsOutputOrderGoodsExtraMapper;

    @Override
    public Long saveSampleOutOrder(AddSampleOutDto addSampleOutDto, User user) {
        WmsOutputOrder wmsOutputOrder = convert2LendOutBean(addSampleOutDto, user);
        outputOrderMapper.insertSelective(wmsOutputOrder);

        updateOrderNo(wmsOutputOrder);

        BigDecimal totalAmount = saveSampleOutOrderGoods(wmsOutputOrder, addSampleOutDto);

        saveSampleOutExtra(addSampleOutDto, wmsOutputOrder.getId(), totalAmount);

        String name = addSampleOutDto.getFileName();
        String uri = addSampleOutDto.getFileUri();

        if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(uri)) {
            Attachment attachment = new Attachment();
            attachment.setName(name);
            attachment.setUri(uri);
            attachment.setAttachmentType(ErpConst.SAMPLE_ORDER_TYPE);
            attachment.setAttachmentFunction(ErpConst.SAMPLE_ORDER_FUNCTION);
            attachment.setRelatedId(wmsOutputOrder.getId().intValue());
            attachment.setCreator(user.getUserId());
            attachment.setAddTime(System.currentTimeMillis());
            attachment.setDomain(attachment.getDomain());
            attachmentMapper.insert(attachment);
        }

        return wmsOutputOrder.getId();
    }

    @Override
    public WmsOutputOrder getSampleOutOrderDetail(Long sampleOrderId) {
        WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(sampleOrderId);
        if (Objects.isNull(wmsOutputOrder)) {
            return null;
        }
        wmsOutputOrder.setWmsOutputOrderExtra(wmsOutputOrderExtraMapper.selectByWmsOutputOrderId(sampleOrderId));
        return wmsOutputOrder;
    }

    @Override
    public List<WmsOutputOrderGoodsDto> queryOutputGoodsBySampleOrderId(Long sampleOrderId) {
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoods = wmsOutputOrderGoodsMapper.queryOutputGoodsBySampleOutId(sampleOrderId);
        if (CollectionUtils.isEmpty(wmsOutputOrderGoods)) {
            return null;
        }

        List<String> skuList = wmsOutputOrderGoods.stream().map(WmsOutputOrderGoods::getSkuNo).collect(Collectors.toList());
        Map<String, WarehouseStock> stockInfoMap = warehouseStockService.getStockInfo(skuList);
        wmsOutputOrderGoods.forEach(goods -> {
            goods.setSampleOrderGoodsExtra(wmsSampleOrderGoodsExtraMapper.selectByWmsOutputOrderGoodsId(goods.getId()));
            if (MapUtils.isNotEmpty(stockInfoMap)) {
                if (stockInfoMap.containsKey(goods.getSkuNo())) {
                    WarehouseStock warehouseStock = stockInfoMap.get(goods.getSkuNo());
                    goods.setAvailableStockNum(warehouseStock.getAvailableStockNum());
                    goods.setStockNum(warehouseStock.getStockNum());
                } else {
                    goods.setAvailableStockNum(ErpConst.ZERO);
                    goods.setStockNum(ErpConst.ZERO);
                }
            }
        });
        return wmsOutputOrderGoods;
    }

    @Override
    public Attachment getAttachmentBySampleOrderId(Long sampleOrderId) {
        HashMap<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("attachmentType", ErpConst.SAMPLE_ORDER_TYPE);
        paramMap.put("attachmentFunction", Collections.singletonList(ErpConst.SAMPLE_ORDER_FUNCTION));
        paramMap.put("registrationNumberId", sampleOrderId);
        List<Attachment> attachmentsList = attachmentMapper.getAttachmentsList(paramMap);
        return CollectionUtils.isNotEmpty(attachmentsList) ? attachmentsList.get(0) : null;
    }

    @Override
    public Long saveEditSampleOutOrder(AddSampleOutDto addSampleOutDto, User user) {
        logger.info("saveEditSampleOutOrder addSampleOutDto:{}", JSON.toJSONString(addSampleOutDto));
        if (Objects.isNull(addSampleOutDto)) {
            return null;
        }

        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        wmsOutputOrder.setId(addSampleOutDto.getSampleOrderId());
        wmsOutputOrder.setBorrowReason(addSampleOutDto.getBorrowReason());
        wmsOutputOrder.setBorrowTraderId(addSampleOutDto.getBorrowTraderId());
        wmsOutputOrder.setBorrowTraderName(addSampleOutDto.getBorrowTraderName());
        wmsOutputOrder.setLogisticCommnet(addSampleOutDto.getLogisticCommnet());


        String[] contactInfo = addSampleOutDto.getTraderContact().split("/");

        wmsOutputOrder.setReceiver(contactInfo[0]);
        wmsOutputOrder.setReceiverPhone(contactInfo[1]);
        wmsOutputOrder.setReceiverTelphone(contactInfo[2]);

        String[] addressInfo = addSampleOutDto.getTraderAddress().split("/");
        wmsOutputOrder.setReceiverAddress(addressInfo[0]);
        wmsOutputOrder.setDetailAddress(addressInfo[1]);

        wmsOutputOrder.setBelongDepartment(user.getOrgName());

        wmsOutputOrder.setUpdator(user.getUsername());
        wmsOutputOrder.setApplyerId(user.getUserId());

        String addTime = DateUtil.convertString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss");
        wmsOutputOrder.setUpdateTime(addTime);
        outputOrderMapper.updateByPrimaryKeySelective(wmsOutputOrder);

        wmsOutputOrderExtraMapper.deleteWmsOutputOrderExtraByOrderId(addSampleOutDto.getSampleOrderId());
        wmsOutputOrderGoodsMapper.deleteWmsOutputOrderGoodsByOrderId(addSampleOutDto.getSampleOrderId());


        BigDecimal totalAmount = saveSampleOutOrderGoods(wmsOutputOrder, addSampleOutDto);

        saveSampleOutExtra(addSampleOutDto, wmsOutputOrder.getId(), totalAmount);

        saveEditAttachment(addSampleOutDto, user, wmsOutputOrder);

        return addSampleOutDto.getSampleOrderId();
    }

    private void saveEditAttachment(AddSampleOutDto addSampleOutDto, User user, WmsOutputOrder wmsOutputOrder) {
        Attachment attachmentBySampleOrderId = getAttachmentBySampleOrderId(addSampleOutDto.getSampleOrderId());
        if (Objects.nonNull(attachmentBySampleOrderId)) {
            attachmentMapper.batchDeleteByPrimaryKey(Collections.singletonList(attachmentBySampleOrderId.getAttachmentId()));
        }

        String name = addSampleOutDto.getFileName();
        String uri = addSampleOutDto.getFileUri();

        if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(uri)) {
            Attachment attachment = new Attachment();
            attachment.setName(name);
            attachment.setUri(uri);
            attachment.setAttachmentType(ErpConst.SAMPLE_ORDER_TYPE);
            attachment.setAttachmentFunction(ErpConst.SAMPLE_ORDER_FUNCTION);
            attachment.setRelatedId(wmsOutputOrder.getId().intValue());
            attachment.setCreator(user.getUserId());
            attachment.setAddTime(System.currentTimeMillis());
            attachment.setDomain(attachment.getDomain());
            attachmentMapper.insert(attachment);
        }
    }

    @Override
    public void updateSampleOutAuditStatus(Long sampleOutOrderId, int value) {
        logger.info("updateSampleOutAuditStatus sampleOutOrderId:{},value:{}", sampleOutOrderId, value);
        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        wmsOutputOrder.setId(sampleOutOrderId);
        wmsOutputOrder.setVerifyStatus(value);
        wmsOutputOrder.setUpdateTime(DateUtil.convertString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));

        outputOrderMapper.updateByPrimaryKeySelective(wmsOutputOrder);
    }


    @Override
    public  String getAuditForSameOut(Long sampleOutOrderId) {
        WmsOutputOrderGoodsExtra extra = new WmsOutputOrderGoodsExtra();
        extra.setWmsOutputOrderId(sampleOutOrderId);
        extra.setProductAudit(0);
        List<WmsOutputOrderGoodsExtra> goodsExtraList = wmsOutputOrderGoodsExtraMapper.selectWmsExtraByLendOutId(extra);
        Set<String> userList = new HashSet();
        for(WmsOutputOrderGoodsExtra good:goodsExtraList){
            String[] users = good.getProductBelongNameInfo().split(",");
            if(users.length > 0){
                userList.add(users[0]);
            }
            if(users.length > 1){
                userList.add(users[1]);
            }
        }
        return String.join(",", userList);
    }

    @Override
    public Integer getAuditLine(WmsOutputOrder wmsOutputOrder) {
        logger.info("样品出库单获取审核线 wmsOutputOrder:{}", JSON.toJSONString(wmsOutputOrder));
//        if (NEW_GOODS_TYPE.equals(wmsOutputOrder.getWmsOutputOrderExtra().getApplyType())) {
            List<WmsOutputOrderGoodsDto> wmsOutputOrderGoodsList = queryOutputGoodsBySampleOrderId(wmsOutputOrder.getId());
            if (CollectionUtils.isEmpty(wmsOutputOrderGoodsList)) {
                logger.error("样品出库单商品信息异常 id:{}", wmsOutputOrder.getId());
                throw new RuntimeException("样品出库单商品信息异常");
            }
//            Integer totalOutPutNum = 0;
//            for (WmsOutputOrderGoodsDto wmsOutputOrderGoodsDto : wmsOutputOrderGoodsList) {
//                totalOutPutNum += wmsOutputOrderGoodsDto.getOutputNum();
//            }
            if ( wmsOutputOrder.getWmsOutputOrderExtra().getApplyAmount().compareTo(BigDecimal.valueOf(1000)) < -1) {
                return ErpConst.ONE;
            } else if (wmsOutputOrder.getWmsOutputOrderExtra().getApplyAmount().compareTo(BigDecimal.valueOf(3000)) > 0) {
                return ErpConst.THREE;
            } else {
                return ErpConst.TWO;
            }
//        } else {
//            // VDERP-15907 流程合并优化
//            if (wmsOutputOrder.getWmsOutputOrderExtra().getApplyAmount().compareTo(BigDecimal.valueOf(1000)) < 0) {
//                return ErpConst.FOUR;
//            } else if (wmsOutputOrder.getWmsOutputOrderExtra().getApplyAmount().compareTo(BigDecimal.valueOf(5000)) > -1) {
//                return ErpConst.SIX;
//            } else {
//                return ErpConst.FIVE;
//            }
//        }
    }

    @Override
    public Integer isKaOrg(User user) {
        return judgeIsKaOrg(user.getOrgId());
    }

    private Integer judgeIsKaOrg(Integer orgId) {
        if (orgId == null || orgId == 0) {
            return 0;
        }
        if (KA_ORGID.equals(orgId)) {
            return 1;
        }
        Integer parentId = organizationMapper.getParentIdById(orgId);
        return judgeIsKaOrg(parentId);
    }

    private void saveSampleOutExtra(AddSampleOutDto addSampleOutDto, long id, BigDecimal totalAmount) {
        WmsOutputOrderExtra WmsOutputOrderExtra = com.vedeng.erp.wms.dto.WmsOutputOrderExtra.builder()
                .wmsOutputOrderId(id)
                .applyType(addSampleOutDto.getApplyType())
                .activityCode(addSampleOutDto.getActivityCode())
                .activityName(addSampleOutDto.getActivityName())
                .receiverId(addSampleOutDto.getTraderContactId())
                .receiverAddressId(addSampleOutDto.getTraderAddressId())
                .applyAmount(totalAmount)
                .build();

        wmsOutputOrderExtraMapper.insertSelective(WmsOutputOrderExtra);
    }

    private WmsOutputOrder convert2LendOutBean(AddSampleOutDto addSampleOutDto, User user) {

        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        wmsOutputOrder.setOutStatus(ErpConst.ZERO);
        wmsOutputOrder.setType(WmsOutputOrderTypeConstant.SAMPLE_OUT);
        wmsOutputOrder.setVerifyStatus(VerifyStatusEnum.Reviewing.getValue());

        wmsOutputOrder.setBorrowReason(addSampleOutDto.getBorrowReason());
        wmsOutputOrder.setBorrowTraderId(addSampleOutDto.getBorrowTraderId());
        wmsOutputOrder.setBorrowTraderName(addSampleOutDto.getBorrowTraderName());
        wmsOutputOrder.setLogisticCommnet(addSampleOutDto.getLogisticCommnet());


        String[] contactInfo = addSampleOutDto.getTraderContact().split("/");

        wmsOutputOrder.setReceiver(contactInfo[0]);
        wmsOutputOrder.setReceiverPhone(contactInfo[1]);
        wmsOutputOrder.setReceiverTelphone(contactInfo[2]);

        String[] addressInfo = addSampleOutDto.getTraderAddress().split("/");
        wmsOutputOrder.setReceiverAddress(addressInfo[0]);
        wmsOutputOrder.setDetailAddress(addressInfo[1]);

        wmsOutputOrder.setBelongDepartment(user.getOrgName());

        wmsOutputOrder.setCreator(user.getUsername());
        wmsOutputOrder.setUpdator(user.getUsername());
        wmsOutputOrder.setApplyerId(user.getUserId());

        String addTime = DateUtil.convertString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss");
        wmsOutputOrder.setAddTime(addTime);
        wmsOutputOrder.setUpdateTime(addTime);
        return wmsOutputOrder;
    }

    private void updateOrderNo(WmsOutputOrder lendOutOrder) {
        WmsOutputOrder updateOrder = new WmsOutputOrder();
        updateOrder.setId(lendOutOrder.getId());
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WMS_SAMPLE_OUTPUT_ORDER);
        updateOrder.setOrderNo(new BillNumGenerator().distribution(billGeneratorBean));
        outputOrderMapper.updateByPrimaryKeySelective(updateOrder);
    }

    private BigDecimal saveSampleOutOrderGoods(WmsOutputOrder wmsOutputOrder, AddSampleOutDto addSampleOutDto) {
        BigDecimal totalAmount = BigDecimal.ZERO;

        if (Objects.isNull(addSampleOutDto.getSkuNo()) || addSampleOutDto.getSkuNo().length == 0) {
            return totalAmount;
        }


        for (int i = 0; i < addSampleOutDto.getSkuNo().length; i++) {
            WmsOutputOrderGoods wmsOutputOrderGoods = new WmsOutputOrderGoods();

            wmsOutputOrderGoods.setWmsOutputOrderId(wmsOutputOrder.getId());
            wmsOutputOrderGoods.setSkuNo(addSampleOutDto.getSkuNo()[i]);
            wmsOutputOrderGoods.setOutputNum(addSampleOutDto.getOutputNum()[i].intValue());
            wmsOutputOrderGoods.setAddTime(wmsOutputOrder.getAddTime());
            wmsOutputOrderGoods.setUpdateTime(wmsOutputOrder.getAddTime());
            wmsOutputOrderGoodsMapper.insertSelective(wmsOutputOrderGoods);

            BigDecimal price = addSampleOutDto.getPrice()[i];
            BigDecimal totalPrice = price.multiply(new BigDecimal(addSampleOutDto.getOutputNum()[i]));
            BigDecimal purchasePrice = addSampleOutDto.getPurchasePrice()[i];

            BigDecimal purchasePriceSnapshot = Objects.isNull(purchasePrice) || BigDecimal.ZERO.compareTo(purchasePrice) == 0 ? new BigDecimal("5000") : purchasePrice;
            BigDecimal totalPurchasePrice = purchasePriceSnapshot.multiply(new BigDecimal(addSampleOutDto.getOutputNum()[i]));

            totalAmount = totalAmount.add(totalPurchasePrice);


            WmsSampleOrderGoodsExtra wmsSampleOrderGoodsExtra = WmsSampleOrderGoodsExtra.builder()
                    .wmsOutputOrderGoodsId(wmsOutputOrderGoods.getId())
                    .price(price)
                    .purchasePrice(purchasePrice)
                    .totalPrice(totalPrice)
                    .totalPurchasePrice(totalPurchasePrice)
//                    .productBelongIdInfo(productManageAndAsistDto.getProductAssitUserId() + "," + productManageAndAsistDto.getProductManageUserId())
//                    .productBelongNameInfo(productManageAndAsistDto.getProductAssitName() + "," + productManageAndAsistDto.getProductManageName())
//                    .productAudit(0)
                    .build();
            wmsSampleOrderGoodsExtraMapper.insertSelective(wmsSampleOrderGoodsExtra);

            //SKU的产品经理或产品助理
            ProductManageAndAsistDto productManageAndAsistDto = this.coreSkuMapper.queryProductManageAndAsist(addSampleOutDto.getSkuNo()[i]);
            WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra=new WmsOutputOrderGoodsExtra();
            wmsOutputOrderGoodsExtra.setWmsOutputOrderId(wmsOutputOrder.getId());
            wmsOutputOrderGoodsExtra.setSkuNo(addSampleOutDto.getSkuNo()[i]);
            wmsOutputOrderGoodsExtra.setProductBelongIdInfo(productManageAndAsistDto.getProductAssitUserId() + "," + productManageAndAsistDto.getProductManageUserId());
            wmsOutputOrderGoodsExtra.setProductBelongNameInfo(productManageAndAsistDto.getProductAssitName() + "," + productManageAndAsistDto.getProductManageName());
            this.wmsOutputOrderGoodsExtraMapper.insertSelective(wmsOutputOrderGoodsExtra);

        }

        return totalAmount;
    }


//    private void setProductBelongInfo(List<WmsOutputOrderGoods> lendOutOrderGoods) {
//
//        if (CollectionUtils.isEmpty(lendOutOrderGoods)) {
//            return;
//        }
//
//        lendOutOrderGoods.stream().forEach(good -> {
//
//            ProductManageAndAsistDto productManageAndAsistDto = this.coreSkuMapper.queryProductManageAndAsist(good.getSkuNo());
//            WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra=new WmsOutputOrderGoodsExtra();
//            wmsOutputOrderGoodsExtra.setWmsOutputOrderId(good.getWmsOutputOrderId());
//            wmsOutputOrderGoodsExtra.setSkuNo(good.getSkuNo());
//            wmsOutputOrderGoodsExtra.setProductBelongIdInfo(productManageAndAsistDto.getProductAssitUserId() + "," + productManageAndAsistDto.getProductManageUserId());
//            wmsOutputOrderGoodsExtra.setProductBelongNameInfo(productManageAndAsistDto.getProductAssitName() + "," + productManageAndAsistDto.getProductManageName());
//
//            this.wmsOutputOrderGoodsExtraMapper.insertSelective(wmsOutputOrderGoodsExtra);
//
//        });
//    }

    /**
     * 更新wms状态
     *
     * @param sampleOutOrderId
     * @param auditStatusValue
     */
    @Override
    public void updateLendOutAuditStatus(Long sampleOutOrderId, int auditStatusValue) {

        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        wmsOutputOrder.setId(sampleOutOrderId);
        wmsOutputOrder.setVerifyStatus(auditStatusValue);
        wmsOutputOrder.setUpdateTime(DateUtil.convertString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));

        outputOrderMapper.updateByPrimaryKeySelective(wmsOutputOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sampleOutOrderAuditPass(Long sampleOutOrderId) throws Exception {

        HandlerStepContext handlerStepContext = new HandlerStepContext();
        handlerStepContext.put("sampleOutOrderId", sampleOutOrderId);

        HandlerStep handlerStep = stepBuildFactory.buildSampleOutStep();
        handlerStep.dealWith(handlerStepContext);
    }

    @Override
    public List<WarehouseGoodsOperateLog> getWLById(WarehouseGoodsOperateLog warehouseGoodsOperateLog, Integer isDirect) {
        return outputOrderMapper.getWarehouseOutList(warehouseGoodsOperateLog);
    }

    @Override
    public List<Integer> getAllMessageUserIdList(Long sampleOrderId) {
        if (Objects.isNull(sampleOrderId)){
            return null;
        }
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoodsDtos = queryOutputGoodsBySampleOrderId(sampleOrderId);
        if (CollectionUtils.isEmpty(wmsOutputOrderGoodsDtos)){
            return null;
        }
        ArrayList<Integer> skuIdList = new ArrayList<>();
        for (WmsOutputOrderGoodsDto wmsOutputOrderGoodsDto : wmsOutputOrderGoodsDtos) {
            skuIdList.add(Integer.parseInt(wmsOutputOrderGoodsDto.getSkuNo().substring(1)));
        }
        List<OrderAssistantRelationDo> orderAssistantBySkuIdList = outputOrderMapper.getOrderAssistantBySkuIdList(skuIdList);
        if (CollectionUtils.isEmpty(orderAssistantBySkuIdList)){
            return null;
        }
        ArrayList<Integer> messageUserIdList = new ArrayList<>();
        orderAssistantBySkuIdList.forEach(orderAssistantRelationDo -> {
            if (Objects.nonNull(orderAssistantRelationDo) && Objects.nonNull(orderAssistantRelationDo.getProductManagerUserId()) && orderAssistantRelationDo.getProductManagerUserId() > 0){
                messageUserIdList.add(orderAssistantRelationDo.getProductManagerUserId());
            }
            if (Objects.nonNull(orderAssistantRelationDo) && Objects.nonNull(orderAssistantRelationDo.getOrderAssistantUserId()) && orderAssistantRelationDo.getOrderAssistantUserId() > 0){
                messageUserIdList.add(orderAssistantRelationDo.getOrderAssistantUserId());
            }
        });
        return messageUserIdList;
    }

    @Override
    public List<Integer> getPrintOutIdListByType(Saleorder saleorder, Integer isDirect, Integer expressType) {
        return warehouseGoodsOperateLogMapper.getExpressWMSWlogIdsByExpressIds(Collections.singletonList(saleorder.getExpressId()));
    }

    @Override
    public ResultInfo checkWmsSampleOutAutoValid(Integer sampleOutId) {
        try {
            Optional<WmsOutputOrder> optionalWmsOutputOrder = Optional.ofNullable(outputOrderMapper.selectByPrimaryKey(Long.valueOf(sampleOutId)));
            if (!optionalWmsOutputOrder.isPresent() || optionalWmsOutputOrder.get().getBorrowTraderId() == null) {
                return new ResultInfo(-1, OrderGoodsAptitudeEnum.ORDER_IS_NULL.getMessage());
            }
            Integer traderId = optionalWmsOutputOrder.get().getBorrowTraderId().intValue();
            Optional<TraderCustomer> traderCustomer = Optional.ofNullable(traderCustomerMapper.getBaseCustomerByTraderId(traderId));
            if (!traderCustomer.isPresent() || traderCustomer.get().getCustomerNature() == null || traderCustomer.get().getCustomerNature() == 0) {
                return new ResultInfo(-1, OrderGoodsAptitudeEnum.CUSTOMER_IS_NULL.getMessage());
            }
            List<TraderCertificate> certificates = traderCertificateMapper.getCertificateListByTraderId(traderId, ErpConst.ONE);
            //新自动审核规则，终端和分销都需要审核营业执照
            int businessCertificateValid = this.checkCertificateValid(certificates, SysOptionConstant.ID_25);
            if (businessCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
                return new ResultInfo(-1, OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_IS_NULL.getMessage());
            }
            if (businessCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
                return new ResultInfo(-1, OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_DATE_OUT.getMessage());
            }
            Integer customerNature = traderCustomer.get().getCustomerNature();
            //处理分销
            if (SysOptionConstant.ID_465.equals(customerNature)) {
                return this.checkDistributionInfo(certificates, traderId, sampleOutId);
            } else if (SysOptionConstant.ID_466.equals(customerNature)) {
                // 处理终端
                return this.checkTerminalInfo(certificates, sampleOutId);
            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception ex) {
            logger.error("订单自动审核出现异常", ex);
            return new ResultInfo(-1, "自动审核失败");
        }
    }

    @Override
    public String getCommentsOfAutoCheck(Integer sampleOutId) {
        Optional<WmsOutputOrder> wmsOutputOrder = Optional.ofNullable(outputOrderMapper.selectByPrimaryKey(Long.valueOf(sampleOutId)));
        if (!wmsOutputOrder.isPresent()) {
            return null;
        }
        Optional<TraderCustomer> traderCustomer = Optional.ofNullable(traderCustomerMapper.getTraderCustomerById(wmsOutputOrder.get().getBorrowTraderId().intValue()));
        if (!traderCustomer.isPresent()) {
            return null;
        }
        //获取订单里sku为医疗器械的首营管理类别
        //968：一类，969：二类，970：三类
        List<Integer> managerCategoryList = wmsOutputOrderGoodsMapper.getManageCategoryOfSku(Long.valueOf(sampleOutId));
        if (managerCategoryList == null || managerCategoryList.size() == 0) {
            return null;
        }
        return saleorderService.getCommentsOfAutoCheckFromManageCategoryList(traderCustomer.get().getCustomerNature(), managerCategoryList);
    }

    /**
     * 校验终端资质信息
     *
     * @param certificates 当前客户资质集合
     * @param sampleOutId  外借单id
     * @return ResultInfo
     */
    private ResultInfo checkTerminalInfo(List<TraderCertificate> certificates, Integer sampleOutId) {
        // 医疗机构执业许可证
        int operateCertificateValid = this.checkCertificateValid(certificates, SysOptionConstant.ID_438);
        // 中医诊所备案证
        int chnMedicalCertificateValid = this.checkCertificateValid(certificates, SysOptionConstant.CHINESE_MEDICAL_CLINIC);
        // 动物诊疗许可证
        int animalCertificateValid = this.checkCertificateValid(certificates, SysOptionConstant.ANIMAL_CLINIC);
        int firstCertificateValid = this.checkMedicalValid(certificates);
        logger.info("operateCertificateValid{},firstCertificateValid{}", operateCertificateValid, firstCertificateValid);
        List<WmsOutputOrderGoods> goods = wmsOutputOrderGoodsMapper.getWmsOutPutOrderGoods(Long.valueOf(sampleOutId));
        boolean checkStatus = false;
        String checkStr = "";
        for (WmsOutputOrderGoods g : goods) {
            if (g == null || StringUtil.isEmpty(g.getSkuNo())) {
                continue;
            }
            SimpleMedicalCategory category = firstEngageMapper.getSimpleMedicalCategory(g.getSkuNo());
            if (category == null) {
                return new ResultInfo(-1, OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessage());
            }
            //新的业务逻辑：根据sku是否有注册证信息来判断sku是否为医疗器械
            if (category.getRegistrationNumberId() != null && category.getRegistrationNumberId() > 0) {
                //是医疗器械
                if (SysOptionConstant.ID_968.equals(category.getManageCategory())) {
                    //一类判断营业执照是否包括医疗器械
                    if (firstCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED) {
                        return new ResultInfo(-1, OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessage());
                    }
                } else if (SysOptionConstant.ID_969.equals(category.getManageCategory()) || SysOptionConstant.ID_970.equals(category.getManageCategory())) {
                    //二类三类
                    //不存在存在旧国标6827且不存在新国标20的情况(?)
                    boolean b = this.judgeSku(category);
                    if (b) {
                        StringBuilder validMsg = new StringBuilder();
                        //医疗机构执业许可证和动物诊疗许可证都不存在的情况->不通过
                        if (operateCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD && animalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD) {
                            if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL && validMsg.length() == 0) {
                                validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessage());
                            } else if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT && validMsg.length() == 0) {
                                validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessage());
                            }
                            if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL && validMsg.length() == 0) {
                                validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessage());
                            } else if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT && validMsg.length() == 0) {
                                validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessage());
                            }
                            return new ResultInfo(-1, validMsg.toString());
                        } else {
                            checkStatus = true;
                            if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD) {
                                if ("".equals(checkStr)) {
                                    checkStr = "客户有医疗机构执业许可证";
                                }
                            } else {
                                if ("".equals(checkStr)) {
                                    checkStr = "客户有动物诊疗许可证";
                                }
                            }
                        }
                    } else {
                        //医疗机构执业许可证和动物诊疗许可证，中医诊所备案证都不存在的情况->不通过
                        if (operateCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD && animalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD && chnMedicalCertificateValid != OrderGoodsAptitudeConstants.CERTIFICATE_VAILD) {
                            StringBuilder validMsg = new StringBuilder();
                            if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
                                if (validMsg.length() == 0) {
                                    validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_IS_NULL.getMessage());
                                }
                            } else if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
                                if (validMsg.length() == 0) {
                                    validMsg.append(OrderGoodsAptitudeEnum.OPERATE_CERTIFICATE_DATE_OUT.getMessage());
                                }
                            }
                            if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
                                if (validMsg.length() == 0) {
                                    validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_IS_NULL.getMessage());
                                }
                            } else if (animalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
                                if (validMsg.length() == 0) {
                                    validMsg.append(OrderGoodsAptitudeEnum.ANIMAL_CERTIFICATE_DATE_OUT.getMessage());
                                }
                            }
                            if (chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
                                if (validMsg.length() == 0) {
                                    validMsg.append(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_IS_NULL.getMessage());
                                }
                            } else if (chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
                                if (validMsg.length() == 0) {
                                    validMsg.append(OrderGoodsAptitudeEnum.CHNMEDICAL_CERTIFICATE_DATE_OUT.getMessage());
                                }
                            }
                            return new ResultInfo(-1, validMsg.toString());
                        } else {
                            // 三者至少有一个存在，即算校验通过
                            checkStatus = true;
                            if (operateCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD) {
                                if ("".equals(checkStr)) {
                                    checkStr = "客户有医疗机构执业许可证";
                                }
                            } else if (chnMedicalCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_VAILD) {
                                if ("".equals(checkStr)) {
                                    checkStr = "客户有中医诊所备案证";
                                }
                            } else {
                                if ("".equals(checkStr)) {
                                    checkStr = "客户有动物诊疗许可证";
                                }
                            }
                        }
                    }
                }
            }
        }
        if (checkStatus) {
            return new ResultInfo(1, checkStr);
        }
        return new ResultInfo(0, "操作成功");
    }

    /**
     * 校验经销商 相关资质信息
     *
     * @param certificates 当前客户资质集合
     * @param traderId     客户id
     * @param sampleOutId  外借单id
     * @return ResultInfo
     */
    private ResultInfo checkDistributionInfo(List<TraderCertificate> certificates, Integer traderId, Integer sampleOutId) {
        // 判断客户的营业执照是否包含医疗器械
        int firstCertificateValid = this.checkMedicalValid(certificates);
        // 判断客户是否有二类医疗资质
        int secondCertificateValid = this.checkCertificateValid(certificates, SysOptionConstant.ID_28);
        // 判断客户是否有三类医疗资质
        int thirdCertificateValid = this.checkCertificateValid(certificates, SysOptionConstant.ID_29);

        TraderMedicalCategory queryCategory = new TraderMedicalCategory();
        queryCategory.setTraderId(traderId);
        queryCategory.setTraderType(ErpConst.TYPE_1);
        List<TraderMedicalCategory> medicalCategories = traderMedicalCategoryMapper.getMedicalCategoryList(queryCategory);
        HashMap<Integer, HashSet<Integer>> categoryMap = this.handleMedicalLevelAndCategory(medicalCategories);

        List<WmsOutputOrderGoods> wmsOutPutOrderGoods = wmsOutputOrderGoodsMapper.getWmsOutPutOrderGoods(Long.valueOf(sampleOutId));
        for (WmsOutputOrderGoods g : wmsOutPutOrderGoods) {
            if (StringUtil.isEmpty(g.getSkuNo())) {
                continue;
            }
            SimpleMedicalCategory category = firstEngageMapper.getSimpleMedicalCategory(g.getSkuNo());
            if (category == null) {
                return new ResultInfo(-1, OrderGoodsAptitudeEnum.CATEGORY_IS_NULL.getMessage());
            }
            if (ErpConst.ZERO.equals(category.getOldMedicalCategory())) {
                category.setOldMedicalCategory(null);
            }
            //新的业务逻辑：根据sku是否有注册证信息来判断sku是否为医疗器械
            if (category.getRegistrationNumberId() != null && category.getRegistrationNumberId() > 0) {
                //是医疗器械
                if (SysOptionConstant.ID_968.equals(category.getManageCategory())) {
                    //一类判断营业执照是否包括医疗器械
                    if (firstCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED) {
                        return new ResultInfo(-1, OrderGoodsAptitudeEnum.BUSINESS_CERTIFICATE_WITHOUT_MED.getMessage());
                    }
                } else if (SysOptionConstant.ID_969.equals(category.getManageCategory())) {
                    //二类
                    if (secondCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
                        return new ResultInfo(-1, OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_IS_NULL.getMessage());
                    }
                    if (secondCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
                        return new ResultInfo(-1, OrderGoodsAptitudeEnum.SECOND_CERTIFICATE_DATE_OUT.getMessage());
                    }
                    ResultInfo categoryResult = this.checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_SECOND_OLD_MEDICAL_CATEGORY), category.getOldMedicalCategory(), SysOptionConstant.ID_969);
                    if (categoryResult.getCode() == -1) {
                        categoryResult = this.checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_SECOND_NEW_MEDICAL_CATEGORY), category.getNewMedicalCategory(), SysOptionConstant.ID_969);
                        if (categoryResult.getCode() == -1) {
                            return categoryResult;
                        }
                    }
                } else if (SysOptionConstant.ID_970.equals(category.getManageCategory())) {
                    //三类
                    if (thirdCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL) {
                        return new ResultInfo(-1, OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_IS_NULL.getMessage());
                    }
                    if (thirdCertificateValid == OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT) {
                        return new ResultInfo(-1, OrderGoodsAptitudeEnum.THIRD_CERTIFICATE_DATE_OUT.getMessage());
                    }
                    ResultInfo categoryResult = this.checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_THIRD_OLD_MEDICAL_CATEGORY), category.getOldMedicalCategory(), SysOptionConstant.ID_970);
                    if (categoryResult.getCode() == -1) {
                        categoryResult = this.checkMedicalCategory(categoryMap.get(OrderGoodsAptitudeConstants.LEVEL_THIRD_NEW_MEDICAL_CATEGORY), category.getNewMedicalCategory(), SysOptionConstant.ID_970);
                        if (categoryResult.getCode() == -1) {
                            return categoryResult;
                        }
                    }
                }
            }
        }
        return new ResultInfo(0, "操作成功");
    }


    /**
     * 校验客户资质（重构自 checkCertificateVaild方法）
     *
     * @param certificates 客户所有资质信息
     * @param type         资质类型（字典表，parentId = 1）
     * @return 校验结果
     */
    private int checkCertificateValid(List<TraderCertificate> certificates, Integer type) {
        // 先判断是否有当前资质类型的信息
        Optional<TraderCertificate> existCertificate = certificates.stream()
                .filter(certificate -> certificate != null && !StringUtil.isEmpty(certificate.getUri()) && certificate.getSysOptionDefinitionId() != null)
                .filter(certificate -> type.equals(certificate.getSysOptionDefinitionId()))
                .findFirst();
        if (existCertificate.isPresent()) {
            // 有当前资质类型的信息，则判断是否在有效期内
            Optional<TraderCertificate> validCertificate = certificates.stream()
                    .filter(certificate -> certificate != null && !StringUtil.isEmpty(certificate.getUri()) && certificate.getSysOptionDefinitionId() != null)
                    .filter(certificate -> type.equals(certificate.getSysOptionDefinitionId()))
                    .filter(certificate -> certificate.getEndtime() == null || certificate.getEndtime() >= System.currentTimeMillis() || certificate.getEndtime().equals(0L))
                    .findFirst();
            if (validCertificate.isPresent()) {
                // 在有效期内
                return OrderGoodsAptitudeConstants.CERTIFICATE_VAILD;
            } else {
                // 过期了
                return OrderGoodsAptitudeConstants.CERTIFICATE_DATE_OUT;
            }
        } else {
            // 没有当前资质类型的信息
            return OrderGoodsAptitudeConstants.CERTIFICATE_IS_NULL;
        }
    }

    /**
     * 校验一类医疗器械的营业执照资质是否包含医疗器械（重构自checkMedicalVaild）
     *
     * @param certificates 客户所有资质信息
     * @return 校验结果
     */
    private int checkMedicalValid(List<TraderCertificate> certificates) {
        // 先判断是否包含营业执照
        Optional<TraderCertificate> validCertificate = certificates.stream()
                .filter(certificate -> certificate != null && !StringUtil.isEmpty(certificate.getUri()) && certificate.getSysOptionDefinitionId() != null)
                .filter(certificate -> SysOptionConstant.ID_25.equals(certificate.getSysOptionDefinitionId()))
                .findFirst();
        if (validCertificate.isPresent()) {
            // 有营业执照，则判断其中是否包含了医疗器械资质
            Optional<TraderCertificate> medicalCertificate = certificates.stream()
                    .filter(certificate -> certificate != null && !StringUtil.isEmpty(certificate.getUri()) && certificate.getSysOptionDefinitionId() != null)
                    .filter(certificate -> SysOptionConstant.ID_25.equals(certificate.getSysOptionDefinitionId()))
                    .filter(certificate -> certificate.getIsMedical() != null && SysOptionConstant.CONTAIN_MEDICAL.equals(certificate.getIsMedical()))
                    .findFirst();
            if (medicalCertificate.isPresent()) {
                return OrderGoodsAptitudeConstants.CERTIFICATE_VAILD;
            } else {
                return OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED;
            }
        } else {
            // 没有营业执照，那自然也不会包含医疗器械资质
            return OrderGoodsAptitudeConstants.CERTIFICATE_WITHOUT_MED;
        }
    }

    /**
     * 处理客户/供应商经营类别（重构自 getSetFromMedicalCategory）
     *
     * @param categoryVoList 客户经营类别集合
     * @return HashMap<级别                                                                                                                               ,                                                                                                                               HashSet                                                                                                                               <                                                                                                                               Integer> 级别下的分类集合>
     */
    private HashMap<Integer, HashSet<Integer>> handleMedicalLevelAndCategory(List<TraderMedicalCategory> categoryVoList) {
        HashMap<Integer, HashSet<Integer>> resultMap = new HashMap<>();
        categoryVoList.stream()
                .filter(Objects::nonNull)
                .filter(c -> c.getMedicalCategoryId() != null && c.getMedicalCategoryLevel() != null)
                .forEach(c -> resultMap.computeIfAbsent(c.getMedicalCategoryLevel(), k -> new HashSet<>()).add(c.getMedicalCategoryId()));
        return resultMap;
    }

    /**
     * 校验医疗器械（二类三类）相关信息
     *
     * @param categorySet 客户经营类别集合
     * @param categoryId  分类id
     * @param type        二类医疗器械/三类医疗器械
     * @return ResultInfo
     */
    private ResultInfo checkMedicalCategory(HashSet categorySet, Integer categoryId, Integer type) {
        logger.info("checkMedicalCategory方法入参：categorySet：{}，categoryId：{}， type：{}", JSONObject.toJSONString(categorySet), categoryId, type);
        if (categorySet == null || categoryId == null || !categorySet.contains(categoryId)) {
            if (SysOptionConstant.ID_969.equals(type)) {
                return new ResultInfo(-1, OrderGoodsAptitudeEnum.SECOND_MEDICAL_IS_INVALIED.getMessage());
            }
            if (SysOptionConstant.ID_970.equals(type)) {
                return new ResultInfo(-1, OrderGoodsAptitudeEnum.THIRD_MEDICAL_IS_INVALIED.getMessage());
            }
        }
        return new ResultInfo(0, "验证有效");
    }

    /**
     * 判断新旧国标是否存在
     *
     * @param category SimpleMedicalCategory
     * @return true：表示新旧国标至少存在一个； false：表示新旧国标都不存在
     */
    private boolean judgeSku(SimpleMedicalCategory category) {
        return !OrderGoodsAptitudeConstants.LEVEL_CHN_MEDICAL_NEW.equals(category.getNewMedicalCategory()) && !OrderGoodsAptitudeConstants.LEVEL_CHN_MEDICAL_OLD.equals(category.getOldMedicalCategory());
    }


    @Autowired
    private UserService userService;
    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService ;

    @Value("${erp_url}")
    private String erp_url; //erp_url	http://erp.ivedeng.com/ 此为apollo里的值

    public static final String NOTICE_MESSAGE_TEMPLATE_FOR_ACTIVITI = "**样品申请单审批提醒**\n>    **{lendOutNum}**审批到您已经30分钟了，请及时处理。  \n>    [查看详情]({jumpUrl})";

    @Autowired
    private ActivitiNoticeService activitiNoticeService;

    public static final String NOTICE_MESSAGE_TEMPLATE_FOR_WEIXIN = "**样品申请单抄送通知**\n>    样品申请单号：**{sampleOutNum}**  \n>    发起时间：{createTime}  \n>    申请类型：{applyTypeName}  \n>    申请企业：{factoryName}  \n>    产品信息：{skuInfo1}   \n>    [查看详情]({jumpUrl})";

    @Resource
    private CoreSkuMapper coreSkuMapper;


    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Override
    public void sendWeixinNotice(Long sampleOutId,  Collection<String> userNameList) {
        log.info("样品申请单开始消息推送:{},{}",sampleOutId,userNameList);
        try{
            WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(sampleOutId);

            WmsOutputOrderExtra outputOrderExtra = wmsOutputOrderExtraMapper.selectByWmsOutputOrderId(sampleOutId);
            List<WmsOutputOrderGoods> goodsExtraList = wmsOutputOrderGoodsMapper.getWmsOutPutOrderGoods(sampleOutId);
            String message = new String(NOTICE_MESSAGE_TEMPLATE_FOR_WEIXIN);
            String sampleOutNum = wmsOutputOrder.getOrderNo();//外借单号
            message = message.replace("{sampleOutNum}",sampleOutNum);

            String createTime =wmsOutputOrder.getAddTime();// 创建时间
            message = message.replace("{createTime}",createTime);

            Integer applyType = outputOrderExtra.getApplyType();//申请类型在订单扩展表里
            SysOptionDefinitionDto sysOptionDefinitionDto= sysOptionDefinitionApiService.getOptionDefinitionById(applyType);
            String applyTypeName = sysOptionDefinitionDto.getTitle();
            message = message.replace("{applyTypeName}",applyTypeName);

            String factoryName = wmsOutputOrder.getBorrowTraderName();//借用的企业
            message = message.replace("{factoryName}",factoryName);

            String skuNo = goodsExtraList.get(0).getSkuNo();
            Integer fisrtNum = goodsExtraList.get(0).getOutputNum();
            CoreSku coreSku = coreSkuMapper.getSkuInfoByNo(skuNo);
            String skuInfo1 =  coreSku.getSkuName() +"*" + fisrtNum;

            Integer skuNum = CollectionUtils.size(goodsExtraList);

            Integer skuSum = goodsExtraList.stream().mapToInt(WmsOutputOrderGoods::getOutputNum).sum();
            if(CollectionUtils.size(goodsExtraList)>1){
                skuInfo1 = skuInfo1+"等 （总数量"+skuSum+"个，总计"+skuNum+"种）";
            }
            message = message.replace("{skuInfo1}",skuInfo1);


            String targetUrl = URLEncoder.encode("/wms/sampleOut/detail.do?sampleOrderId="+sampleOutId.intValue()+"&title=样品申请单详情页","UTF-8");
            String jumpUrl = erp_url+"index.do?target="+targetUrl;
            message = message.replace("{jumpUrl}",jumpUrl);


            List<User> userList =  userService.getUserByUserIds(userNameList);

            BatchMessageSendDto batchMessageSendDto = new BatchMessageSendDto();
            batchMessageSendDto.setUserIdList(userList.stream().map(User::getUserId).collect(Collectors.toList()));
            batchMessageSendDto.setMsgContent(message);
            RestfulResult result = uacWxUserInfoApiService.sendBatchMsg(batchMessageSendDto);
            if("success".equals(result.getCode())){
                return;
            }else{
                log.warn("外借单推送消息可能含有失败的内容:{}", JSONObject.toJSONString(result));
            }
        }catch (Exception e){
            log.error("外借单推送消息失败",e);
        }
    }

    @Override
    public void sendWeixinNoticeForActiviti(Long sampleOutId, String businessKey,String taskId, String userName) {
        try{
            WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(sampleOutId);
            String message = new String(NOTICE_MESSAGE_TEMPLATE_FOR_ACTIVITI);
            String lendOutNum = wmsOutputOrder.getOrderNo();//外借单号
            message = message.replace("{lendOutNum}",lendOutNum);

            String targetUrl = URLEncoder.encode("/wms/sampleOut/detail.do?sampleOrderId="+sampleOutId.intValue()+"&title=样品申请单详情页","UTF-8");
            String jumpUrl = erp_url+"index.do?target="+targetUrl;
            message = message.replace("{jumpUrl}",jumpUrl);
            List<User> userList =  userService.getUserByUserIds(Arrays.asList(userName));


            ActivitiNoticeInfoEntity activitiNoticeInfoEntity = new ActivitiNoticeInfoEntity();
            activitiNoticeInfoEntity.setTaskId(taskId);
            activitiNoticeInfoEntity.setBusinessKey(businessKey);
            activitiNoticeInfoEntity.setIsClick(0);
            activitiNoticeInfoEntity.setClickTime(null);
            activitiNoticeInfoEntity.setAcceptUserName(userName);
            activitiNoticeInfoEntity.setAcceptUserId(userList.stream().map(User::getUserId).collect(Collectors.toList()).get(0));
            activitiNoticeInfoEntity.setNoticeTime(new Date());
            activitiNoticeService.saveActivitiTaskNoticeInfo(activitiNoticeInfoEntity);

            BatchMessageSendDto batchMessageSendDto = new BatchMessageSendDto();
            batchMessageSendDto.setUserIdList(userList.stream().map(User::getUserId).collect(Collectors.toList()));
            batchMessageSendDto.setMsgContent(message);
            RestfulResult result = uacWxUserInfoApiService.sendBatchMsg(batchMessageSendDto);
            if("success".equals(result.getCode())){
                return;
            }else{
                log.warn("样品申请单推送消息可能含有失败的内容:{}", JSONObject.toJSONString(result));
            }
        }catch (Exception e){
            log.error("样品申请单审批提醒失败",e);
        }



    }
}
