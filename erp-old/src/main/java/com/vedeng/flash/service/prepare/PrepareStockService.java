package com.vedeng.flash.service.prepare;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.flash.dto.DealDetailDto;
import com.vedeng.flash.dto.PrepareStockCommand;
import com.vedeng.flash.dto.PrepareStockDto;
import com.vedeng.flash.model.RegularSnapshot;
import com.vedeng.goods.model.Goods;
import com.vedeng.order.model.Buyorder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * @Description:  备货计划服务接口
 * @Author:       davis
 * @Date:         2021/5/18 下午7:51
 * @Version:      1.0
 */
public interface PrepareStockService {

    /**
     * 分页查询备货计划列表
     * @param command 查询参数
     * @param page 分页条件
     * @return 备货计划集合
     */
    List<PrepareStockDto> getPrepareStockListPage(PrepareStockCommand command, Page page);

    /**
     * 初始化设置安全库存页面
     * @param command 查询条件
     * @return 定品相关信息
     */
    PrepareStockDto initSafeStockPage(PrepareStockCommand command);

    /**
     * 处理销量
     * @param stockDto
     */
    void dealWithSaleNum(PrepareStockDto stockDto);

    /**
     * 设置安全库存
     * @param stockDto 安全库存
     * @param request 请求头
     * @return
     */
    ResultInfo setSafeStock(HttpServletRequest request, PrepareStockDto stockDto);

    /**
     * 查询SKU
     * @param command
     * @return
     */
    ResultInfo<Goods> searchSku(HttpServletRequest request, PrepareStockCommand command);

    /**
     * 处理暂不备货
     * @param stockDto 备货计划
     * @param request 请求头
     * @return
     */
    ResultInfo dealPrepareStock(HttpServletRequest request, PrepareStockDto stockDto);

    /**
     * 更新备货计划
     * @param request
     * @param regularIds
     * @return
     */
    ResultInfo updatePrepare(HttpServletRequest request, String regularIds);

    /**
     * 导出备货计划
     * @param request
     * @param command
     * @return
     */
    Map<String, Object> exportPrepareStock(HttpServletRequest request, PrepareStockCommand command);

    /**
     * 同步定品快照
     * @return
     */
    List<RegularSnapshot> syncRegularSkuSnapshot();

    /**
     * 备货计划预警处理
     * @return
     */
    Map<String, Object> prepareStockWarn();

    /**
     * 配置预测安全库存系数
     * @param prepareStockDto
     * @return
     */
    ResultInfo setForecastSafeRatio(HttpServletRequest request, PrepareStockDto prepareStockDto);

    /**
     * 安全库存日志页面
     * @param command 查询条件
     * @return
     */
    Map<String, Object> initSafeStockLogPage(PrepareStockCommand command);
    /**
     * @Description 处理详情界面
     * <AUTHOR>
     * @Date 9:22 2021/5/28
     * @Param [command]
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    DealDetailDto initDealDetailPage(PrepareStockCommand command,Page page);

    /**
     * 保存备货操作日志
     * @param stockDtoList
     */
    void saveBhOperateLog(HttpSession session, List<PrepareStockDto> stockDtoList);

    /**
     * 获取在途采购订单
     * @param command
     * @return
     */
    List<Buyorder> getIntranstiStockList(PrepareStockCommand command);
}
