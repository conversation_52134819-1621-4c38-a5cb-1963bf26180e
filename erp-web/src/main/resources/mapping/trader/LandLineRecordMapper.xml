<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.LandLineRecordMapper">

    <insert id="insert">
        insert into T_LANDLINE_CALL_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="landlineCallRecordId != null">
                LANDLINE_CALL_RECORD_ID,
            </if>
            <if test="phone != null">
                PHONE,
            </if>
            <if test="callingNumber != null">
                CALLING_NUMBER,
            </if>
            <if test="lineCode != null">
                LINE_CODE,
            </if>
            <if test="coid != null">
                COID,
            </if>
            <if test="coidType != null">
                COID_TYPE,
            </if>
            <if test="coidLength != null">
                COID_LENGTH,
            </if>
            <if test="isConnect != null">
                IS_CONNECT,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="landlineCallRecordId != null">
                #{landlineCallRecordIdjdbcType=BIGINT},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="callingNumber != null">
                #{callingNumber,jdbcType=VARCHAR},
            </if>
            <if test="lineCode != null">
                #{lineCode,jdbcType=INTEGER},
            </if>
            <if test="coid != null">
                #{coid,jdbcType=VARCHAR},
            </if>
            <if test="coidType != null">
                #{coidType,jdbcType=INTEGER},
            </if>
            <if test="coidLength != null">
                #{coidLength,jdbcType=INTEGER},
            </if>
            <if test="isConnect != null">
                #{isConnect,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>

        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="landlineCallRecordId">
            SELECT LAST_INSERT_ID() AS landlineCallRecordId
        </selectKey>
    </insert>

    <select id="getRecentRecordWithCondition" resultType="com.vedeng.trader.model.dto.RecentLandLineRecordDto">
        SELECT MAX(RESULT.LANDLINE_CALL_RECORD_ID) RECENT_RECORD_ID,
               SUM(RESULT.COID_LENGTH)             TOTAL_LENGTH
        FROM (
                 SELECT PHONE,
                        LANDLINE_CALL_RECORD_ID,
                        IFNULL(COID_LENGTH, 0) COID_LENGTH
                 FROM T_LANDLINE_CALL_RECORD
                 WHERE PHONE = #{phone,jdbcType=VARCHAR}
                   AND ADD_TIME >= UNIX_TIMESTAMP(CAST(SYSDATE() AS DATE)) * 1000
                   AND IS_CONNECT = 2
                   AND IS_ENABLE = 1
                 ORDER BY LANDLINE_CALL_RECORD_ID DESC LIMIT 5
             ) RESULT
        GROUP BY RESULT.PHONE
        HAVING TOTAL_LENGTH > 120
    </select>

    <select id="getRecordInfoById" resultType="com.vedeng.trader.model.LandLineCallRecord">
        SELECT *
        FROM T_LANDLINE_CALL_RECORD
        WHERE LANDLINE_CALL_RECORD_ID = #{recordId,jdbcType=BIGINT}
    </select>

    <select id="getConnectLineRecent2Years" resultType="com.vedeng.trader.model.LandLineCallRecord">
        SELECT *
        FROM T_LANDLINE_CALL_RECORD
        WHERE PHONE = #{phone,jdbcType=VARCHAR}
          AND IS_CONNECT = 2
          AND IS_ENABLE = 1
          AND COID_TYPE = 2
          AND ADD_TIME >= UNIX_TIMESTAMP(date_sub(curdate(), INTERVAL + 2 YEAR )) * 1000
        ORDER BY LANDLINE_CALL_RECORD_ID DESC LIMIT 1
    </select>

    <select id="getToadyUnConnectTimesByLineCode" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM T_LANDLINE_CALL_RECORD
        WHERE PHONE = #{phone,jdbcType=VARCHAR}
        <if test="lineCode != null">
            AND LINE_CODE = #{lineCode,jdbcType=INTEGER}
        </if>
        AND IS_CONNECT <![CDATA[<]]> 2
        AND COID_TYPE = 2
        AND IS_ENABLE = 1
        AND ADD_TIME >= UNIX_TIMESTAMP(CAST(DATE_SUB(SYSDATE(), INTERVAL 1 DAY) AS DATE)) * 1000;
    </select>

    <select id="getLandLineCallRecordByIds" resultType="com.vedeng.trader.model.LandLineCallRecord">
        SELECT *
        FROM T_LANDLINE_CALL_RECORD
        WHERE LANDLINE_CALL_RECORD_ID IN
        <foreach item="recordId" index="index" collection="recordIds" open="(" separator="," close=")">
            #{recordId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="getUnHandleRecordListToday" resultType="com.vedeng.trader.model.LandLineCallRecord">
        SELECT *
        FROM T_LANDLINE_CALL_RECORD
        WHERE IS_CONNECT = 0
          AND IS_ENABLE = 1
          AND ADD_TIME >= UNIX_TIMESTAMP(CAST(SYSDATE() AS DATE)) * 1000
    </select>

    <update id="syncLandLineRecordInfo">
        UPDATE T_LANDLINE_CALL_RECORD
        SET COID_LENGTH = #{landLineCallRecord.coidLength,jdbcType=INTEGER},
        <if test="landLineCallRecord.callingNumber != null">
            CALLING_NUMBER = #{landLineCallRecord.callingNumber,jdbcType=VARCHAR},
        </if>
        <if test="landLineCallRecord.lineCode != null">
            LINE_CODE = #{landLineCallRecord.lineCode,jdbcType=INTEGER},
        </if>
        IS_CONNECT = #{landLineCallRecord.isConnect,jdbcType=INTEGER},
        UPDATER = #{landLineCallRecord.updater,jdbcType=INTEGER},
        MOD_TIME = #{landLineCallRecord.modTime,jdbcType=BIGINT}
        WHERE LANDLINE_CALL_RECORD_ID = #{landLineCallRecord.landlineCallRecordId,jdbcType=BIGINT}
    </update>

    <select id="getPhoneToadyLineRecords" resultType="com.vedeng.trader.model.LandLineCallRecord">
        SELECT *
        FROM T_LANDLINE_CALL_RECORD
        WHERE PHONE = #{phone,jdbcType=VARCHAR}
          AND LINE_CODE = #{lineCode,jdbcType=INTEGER}
          AND IS_ENABLE = 1
          AND ADD_TIME >= UNIX_TIMESTAMP(CAST(DATE_SUB(SYSDATE(), INTERVAL 1 DAY) AS DATE)) * 1000
        ORDER BY ADD_TIME DESC
    </select>
</mapper>