package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeExpressCostMapper {
    /**
     * delete by primary key
     * @param kingDeeExpressCostId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer kingDeeExpressCostId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeExpressCostEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeExpressCostEntity record);

    /**
     * select by primary key
     * @param kingDeeExpressCostId primary key
     * @return object by primary key
     */
    KingDeeExpressCostEntity selectByPrimaryKey(Integer kingDeeExpressCostId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeExpressCostEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeExpressCostEntity record);

    int batchInsert(@Param("list") List<KingDeeExpressCostEntity> list);

    List<KingDeeExpressCostEntity> selectByBddjbh(String Bddjbh);
}