package com.vedeng.goods.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode
public class SceneSkuPoiDto {

    /**
     * 场景名称
     */
    private String skuSceneName;

    /**
     * 场景分类名称
     */
    private String skuSceneCategoryName;

    /**
     * sku信息
     */
    private List<SkuInfo> skuInfoList = new ArrayList<>();


    @Getter
    @Setter
    @EqualsAndHashCode
    public static class SkuInfo {

        @ExcelProperty("订货号")
        private String skuNo;

        @ExcelProperty("产品名称")
        private String skuName;

        @ExcelProperty("品牌名称")
        private String brandName;

        @ExcelProperty("型号或规格")
        private String modelOrSpec;

        @ExcelProperty("主要参数列表")
        private String mainParam;

        @ExcelProperty("经销价")
        private BigDecimal distributionPrice;

        @ExcelProperty("终端价")
        private BigDecimal terminalPrice;

        @ExcelProperty("保修政策")
        private String warrantyInfo;

        @ExcelProperty("使用年限或效期")
        private String useLife;

        @ExcelProperty("是否提供上门安装服务")
        private String isInstall;

        @ExcelProperty("审核状态")
        private String auditStatus;

        @ExcelProperty("商品等级")
        private String goodsLevelNo;

        @ExcelProperty("商品挡位")
        private String goodsPositionNo;

        @ExcelProperty("产品经理")
        private String productManager;

        @ExcelProperty("产品助理")
        private String productAssistant;
    }


}