package com.newtask.kingdee;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.task.service.KingDeeBankBillTaskService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.service.KingDeePayBillApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 金蝶付款流水，回单对接
 * @date 2022/9/7 13:23
 **/
@Component
@JobHandler(value = "KingDeeBankBillTask")
@Slf4j
public class KingDeeBankBillTask extends AbstractJobHandler {

    /**
     * 模拟数据
     */
    public static final String MOCK = "mock";
    /**
     * 测试 p版 环境
     */
    public static final String TEST = "test";
    /**
     * 多条数据
     */
    public static final String LIST = "list";
    @Autowired
    private KingDeeBankBillTaskService kingDeeBankBillTaskService;

    @Autowired
    private KingDeePayBillApiService kingDeePayBillApiService;


    /**
     * 参数格式要求： param: {"type":"","data":""}
     * <p>
     * 1.type: test
     * 2.type: mock  data为 模拟数据
     * {
     * "type": "mock",
     * "data": [
     * {
     * "fQzokDzhzd": "************",
     * "fQzokLsh": "",
     * "fQzokYhzhhm":"",
     * "fBillNo":"",
     * "fQzokCgddh":"",
     * "kingDeePayBillId":""
     * }
     * ]
     * }
     * 3.type:list data为ids
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if (StringUtils.isEmpty(param)) {
            List<KingDeePayBillDto> kingDeePayBillDtos = kingDeePayBillApiService.queryKingDeePayBillNoReturnUrl();
            List<KingDeePayBillDto> kingDeeReceiveRefundBillDtos = kingDeePayBillApiService.queryKingDeeReceiveRefundBillNoReturnUrl();
            doKingDeeBankBill(kingDeePayBillDtos,kingDeeReceiveRefundBillDtos);
            return ReturnT.SUCCESS;
        }

        JSONObject jsonObject = JSON.parseObject(param);
        String type = jsonObject.getString("type");
        if (MOCK.equals(type)) {
            JSONArray kingDeePayBillDtos = jsonObject.getJSONArray("data");
            kingDeePayBillDtos.forEach(k -> {
                KingDeePayBillDto data = JSON.parseObject(JSON.toJSONString(k),KingDeePayBillDto.class);
                kingDeeBankBillTaskService.doBankBillAndVerify(data, Boolean.FALSE);
            });
        }

        if (TEST.equals(type)) {
            //为测试环境及p版模拟数据，正常情况请勿执行
            List<KingDeePayBillDto> kingDeePayBillDtos = kingDeePayBillApiService.queryKingDeePayBillNoReturnUrl();
            doKingDeeBankBill(kingDeePayBillDtos,new ArrayList<>());
        }

        if (LIST.equals(type)) {
            List<Integer> ids = Arrays.stream(type.split(StrUtil.COMMA)).map(Integer::valueOf).collect(Collectors.toList());
            List<KingDeePayBillDto> kingDeePayBillDtos = kingDeePayBillApiService.queryKingDeePayBillNoReturnUrlByKingDeePayBillIds(ids);
            doKingDeeBankBill(kingDeePayBillDtos,new ArrayList<>());
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 执行金蝶流水任务
     *
     * @param kingDeePayBillDtos kingDeePayBillDtos
     */
    private void doKingDeeBankBill(List<KingDeePayBillDto> kingDeePayBillDtos,List<KingDeePayBillDto> receiveRefundDtos) {
        for (KingDeePayBillDto data : kingDeePayBillDtos) {
            boolean success = kingDeePayBillApiService.queryKingDeeReturnUrlAndTranFlowAndBank(data);
            if(success){
                kingDeeBankBillTaskService.doBankBillAndVerify(data, Boolean.FALSE);
            }
        }
        for (KingDeePayBillDto data : receiveRefundDtos) {
            boolean success = kingDeePayBillApiService.queryKingDeeReturnUrlAndTranFlowAndBank(data);
            log.info("获取金蝶付款的收款退款单回传erp,kingDeePayBillDto:{},查询结果:{}", JSONUtil.toJsonStr(data),success);
            if(success){
                kingDeeBankBillTaskService.doBankBillAndVerify(data, Boolean.TRUE);
            }
        }

    }

}
