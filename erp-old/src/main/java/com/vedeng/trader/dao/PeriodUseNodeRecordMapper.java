package com.vedeng.trader.dao;

import com.vedeng.trader.model.po.PeriodUseNodeRecordPo;

import javax.inject.Named;
import java.util.List;

/**
 * 账期使用节点记录Mapper
 * <AUTHOR>
 */
@Named("periodUseNodeRecordMapper")
public interface PeriodUseNodeRecordMapper {
    /**
     * 添加节点使用记录
     * @param periodUseNodeRecordPo
     * @return
     */
    Long  insertRecord(PeriodUseNodeRecordPo periodUseNodeRecordPo);

    /**
     * 检索未处理的节点记录
     * @return
     */
    List<PeriodUseNodeRecordPo> getUnHandledRecord();

    /**
     * 更新账期记录使用记录处理状态；
     *
     * @param recordId
     * @return
     */
    int updateHandleStatusByRecordId(Long recordId);
}
