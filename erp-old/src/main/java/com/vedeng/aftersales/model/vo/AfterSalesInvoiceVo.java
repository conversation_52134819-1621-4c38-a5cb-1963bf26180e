package com.vedeng.aftersales.model.vo;

import java.math.BigDecimal;
import java.util.List;

import com.vedeng.aftersales.model.AfterSalesInvoice;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AfterSalesInvoiceVo extends AfterSalesInvoice {


	private static final long serialVersionUID = 1L;
	
	private String invoiceNo;//发票号码
	
	private String invoiceCode;//发票代码
	
	private BigDecimal amount;//发票金额
	
	private Integer invoiceType;//发票类型 字典库

	private String invoiceHref;//下载地址

	private Integer invoiceTag;//录票/开票 1开票 2录票
	
	private String invoiceTypeName;//发票类型名称 字典库
	
	private Integer expressId;//是否寄送（非0-已寄送）
	
	private Integer colorType;//发票颜色
	
	private Integer isEnable;//是否有效
	
	private Integer creator;//创建人
	
	private Long addTime;//创建时间
	
	private String logisticsName;//物流名称
	
	private String logisticsNo;//物流单号
	
	private Long deliveryTime;//寄送时间
	
	private Integer arrivalStatus;//收货状态
	
	private String logisticsComments;//物流备注
	
	private String creatorName;//开票人
	
	private Integer subjectType;//售后主体类型:535销售，536采购，537第三方
	
	private Integer currentMonthInvoice;//当前月发票：1是：0否

	private List<AfterSalesGoodsVo> afterGoodsList;//售后产品列表
	
    private BigDecimal ratio;//发票税率
    
    private Long validTime;//发票审核时间
    
    private Integer validStatus;//审核 0待审核 1通过 不通过

    private Integer validUserId;//审核人
    
    private String validUsername;//审核人名称

    private Integer isSendInvoice;//售后发票是否寄送

    private Integer afterType;//售后类型 
    
    private Integer orderId;
    
    private Integer invoiceId;//发票Id

    private Integer invoiceProperty;//1纸质发票2电子发票    
    
    private BigDecimal amountCount;//发票总金额

	private Long arrivalTime;//到达时间

	private String name;//物流公司名称

	private Integer type;//发票类型

	private  Integer afterExpressId;//售后快递

	private Integer companyId;

	private Integer relatedId;

	private List<Integer> typeList;//发票类型（销售和售后，采购和售后）

	private List<Integer> orderGoodsIdList;//关联的产品id

	/**
	 * 更新者
	 */
	private String updaterName;

	private List<String> invoiceNoList; // 发票号集合

	/**
	 * 采购发票金额
	 */
	private BigDecimal buyorderInvoiceAmount;

	/**
	 * 是否有正在进行中的冲销申请0否1是
	 */
	private Integer isHaveCoverApply;
	/**
	 * 仅退票原发票号
	 */
	private String oldInvoiceNo;
	/**
	 * oss文件地址
	 */
	private String ossFileUrl;

}
