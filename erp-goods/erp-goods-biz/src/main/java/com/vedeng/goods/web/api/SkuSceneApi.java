package com.vedeng.goods.web.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.UserComponent;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.goods.domain.dto.SceneSkuPoiDto;
import com.vedeng.goods.dto.SkuDto;
import com.vedeng.goods.dto.SkuSceneDto;
import com.vedeng.goods.service.SkuSceneService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 场景方案
 * @menu: 场景方案
 * @date 2024/12/2 10:42
 */
@RequestMapping("/sku/scene")
@Slf4j
@ExceptionController
@RestController
public class SkuSceneApi {

    @Autowired
    private SkuSceneService skuSceneService;

    /**
     * 列表
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<PageInfo<SkuSceneDto>> page(@RequestBody PageParam<SkuSceneDto> pageParam) {
        return R.success(skuSceneService.page(pageParam));
    }

    /**
     * 列表：获取创建人集合
     */
    @RequestMapping("/getCreateUser")
    @NoNeedAccessAuthorization
    public R<List<UserComponent>> getCreateUser(@RequestParam(required = false) String username) {
        List<UserComponent> createUser = skuSceneService.getCreateUser();
        if (StrUtil.isNotEmpty(username)) {
            String lowerCaseUsername = username.toLowerCase();
            createUser = createUser.stream().filter(t -> t.getUserName().toLowerCase().contains(lowerCaseUsername)).collect(Collectors.toList());
        }
        return R.success(createUser);
    }

    /**
     * 列表：获取编辑人集合
     */
    @RequestMapping("/getEditUser")
    @NoNeedAccessAuthorization
    public R<List<UserComponent>> getEditUser(@RequestParam(required = false) String username) {
        List<UserComponent> editUser = skuSceneService.getEditUser();
        if (StrUtil.isNotEmpty(username)) {
            String lowerCaseUsername = username.toLowerCase();
            editUser = editUser.stream().filter(t -> t.getUserName().toLowerCase().contains(lowerCaseUsername)).collect(Collectors.toList());
        }
        return R.success(editUser);
    }

    /**
     * 获取场景方案
     */
    @RequestMapping("/get")
    @NoNeedAccessAuthorization
    public R<SkuSceneDto> get(@RequestParam Long id) {
        SkuSceneDto byId = skuSceneService.getById(id);
        return R.success(byId);
    }

    /**
     * 检查场景方案名称是否可用
     *
     * @param name 名称
     * @param id   id
     * @return true=可用，false=不可用
     */
    @RequestMapping("/check")
    @NoNeedAccessAuthorization
    public R<Boolean> check(@RequestParam String name, @RequestParam Long id) {
        boolean b = skuSceneService.check(name, id);
        return R.success(b);
    }


    /**
     * 新增
     */
    @RequestMapping("/create")
    @NoNeedAccessAuthorization
    public R<SkuSceneDto> create(@RequestBody SkuSceneDto skuSceneDto) {
        ValidatorUtils.validate(skuSceneDto, AddGroup.class);
        skuSceneService.create(skuSceneDto);
        return R.success(skuSceneDto);
    }

    /**
     * 获取产品信息
     */
    @RequestMapping("/getProductInfo")
    @NoNeedAccessAuthorization
    public R<List<SkuDto>> getProductInfo(@RequestParam List<String> skuNos) {
        List<SkuDto> productInfo = skuSceneService.getProductInfo(skuNos);
        return R.success(productInfo);
    }


    /**
     * 修改
     */
    @RequestMapping("/update")
    @NoNeedAccessAuthorization
    public R<Void> update(@RequestBody SkuSceneDto skuSceneDto) {
        ValidatorUtils.validate(skuSceneDto, UpdateGroup.class);
        skuSceneService.update(skuSceneDto);
        return R.success();
    }

    /**
     * 上架下架
     *
     * @param id     方案id
     * @param status 状态 0=上架 1=下架
     */
    @RequestMapping("/updateStatus")
    @NoNeedAccessAuthorization
    public R<Void> updateStatus(@RequestParam Long id, @RequestParam Integer status) {
        skuSceneService.updateStatus(id, status);
        return R.success();
    }


    /**
     * 排序
     */
    @RequestMapping("/sort")
    @NoNeedAccessAuthorization
    public R<Void> sort(@RequestParam Long id, @RequestParam Integer sort) {
        skuSceneService.sort(id, sort);
        return R.success();
    }

    /**
     * 获取场景和场景分类级联
     */
    @RequestMapping("/getSceneAndCategory")
    @NoNeedAccessAuthorization
    public R<List<SkuSceneDto>> getSceneAndCategory() {
        List<SkuSceneDto> sceneAndCategory = skuSceneService.getSceneAndCategory();
        return R.success(sceneAndCategory);
    }

    /**
     * 导出excel
     */
    @RequestMapping("/exportExcel")
    @NoNeedAccessAuthorization
    public void exportExcel(HttpServletResponse response, @RequestParam Long id) throws IOException {
        List<SceneSkuPoiDto> dataList = skuSceneService.exportExcel(id);

        if (CollUtil.isEmpty(dataList)) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "No data found for the given ID");
            return;
        }

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        String fileName = URLEncoder.encode(CollUtil.getFirst(dataList).getSkuSceneName()+ DateUtils.format(new Date(),"yyyyMMdd"), StandardCharsets.UTF_8.name());
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), SceneSkuPoiDto.SkuInfo.class).build()) {
            int sheetNo = 0;
            for (SceneSkuPoiDto data : dataList) {
                if (data.getSkuInfoList() != null && !data.getSkuInfoList().isEmpty()) {
                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetNo, data.getSkuSceneCategoryName()).build();
                    excelWriter.write(data.getSkuInfoList(), writeSheet);
                }
                sheetNo++;
            }
        }
    }

}
