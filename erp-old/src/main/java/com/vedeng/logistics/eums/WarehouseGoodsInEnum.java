package com.vedeng.logistics.eums;


/**
 * 入库类型
 * @ClassName:  WarehouseGoodsInEnum   
 * @author: <PERSON>.yang
 * @date:   2022年11月16日 上午9:50:52    
 * @Copyright:
 */
public enum WarehouseGoodsInEnum {

    /**
     * WMS:入库
     * 
     * PO 采购入库
     * PA 赠品入库单
     * RC 盘盈入库单
     * TT 调拔入库单
     * RA 销售售后退货单
     * PS 采购售后换货入库
     * SS 销售售后换货入库
     * JS 借货归还入库单
     * CS 库存初始化入库单
     * 
     * ERP:入库
     * 
     * 1  入库 
     * 3  销售换货入库  
     * 5  销售退货入库 
     * 8  采购换货入库 
     * 9  外借入库
     * 11 调整盘盈入库 
     * 12 盘盈入库
     * 15 调整盘亏
     * 17 采购赠品入库
     * 20 库存转换入库
     */
    PURCHASE_IN(1,"PO", "采购入库单", ""),
    ORDER_WAREHOUSE_CHANGE_IN(3, "SS","销售换货入库单", ""),
    ORDER_WAREHOUSE_BACK_IN(5,"RA", "销售退货入库单", ""),
    BUYORDER_WAREHOUSE_CHANGE_IN(8,"PS", "采购售后换货入库单", ""),
    LENDOUT_WAREHOUSE_IN(9,"JS", "外借归还入库", ""),
    RECEIVE_WAREHOUSE_OUT(12,"RC", "盘盈入库单", ""),
    PURCHASE_GIFT_IN(17,"PGI", "采购赠品入库单", ""),
    UNIT_CONVERSION_IN(20,"UCI","库存转换入库","")
    ;

    private final Integer erpCode;
    private final String wmsCode;
    private final String type;

    private final String service;

    WarehouseGoodsInEnum(Integer erpCode, String wmsCode, String type, String service) {
        this.erpCode = erpCode;
        this.wmsCode = wmsCode;
        this.type = type;
        this.service = service;
    }

    public Integer getErpCode() {
        return erpCode;
    }

    public String getWmsCode() {
        return wmsCode;
    }

    public String getType() {
        return type;
    }

    /**
     * 根据 erpCode 获取 type
     * @param erpCode 出库单类型erpCode
     * @return 出库单类型名称
     */
    public static String getTypeByCode(Integer erpCode){
        for (WarehouseGoodsInEnum warehouseGoodsOutEnum : WarehouseGoodsInEnum.values()) {
            if (warehouseGoodsOutEnum.erpCode.equals(erpCode)) {
                return warehouseGoodsOutEnum.type;
            }
        }
        return "";
    }

    /**
     * 根据 wmsCode 获取 erpCode
     * @param wmsCode
     * @return erpCode
     */
    public static Integer getErpCodeByCode(String wmsCode){
        for (WarehouseGoodsInEnum warehouseGoodsOutEnum : WarehouseGoodsInEnum.values()) {
            if (warehouseGoodsOutEnum.wmsCode.equals(wmsCode)) {
                return warehouseGoodsOutEnum.erpCode;
            }
        }
        return -1;
    }
}
