<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="基础价格维护" scope="application" />
<%@ include file="../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<style>
.tooltip-inner {
    white-space: pre-line !important;
    text-align: left !important;
    max-width: 300px !important;
}

/* 修改 infor_name 类的样式，允许文字换行 */
.infor_name {
    white-space: normal !important;  /* 允许文字换行 */
    word-wrap: break-word;          /* 允许长单词断行 */
    line-height: 1.2;              /* 适当调整行高 */
    height: auto;                  /* 高度自适应 */
}

</style>
<div class="main-container">
	<div class="list-pages-search">
		<form method="post" id="search" action="<%=basePath%>price/basePriceMaintain/index.do">
			<input class="hidden-reset" name="includeSkuNosStr" value="${queryDto.includeSkuNosStr}" type="hidden" />
			<ul>
				<li>
					<label class="infor_name">关键词</label>
					<input type="text" class="input-middle" placeholder="请输入订货号/商品名称/禁用原因" name="priceKeyWord" id="priceKeyWord" value="${queryDto.priceKeyWord}"/>
				</li>
				<li>
					<label class="infor_name">核价审核状态</label>
					<select class="input-middle f_left" name="priceAuditStatus">
						<option value="">全部</option>
						<option value="0" <c:if test="${queryDto.priceAuditStatus eq 0 }">selected="selected"</c:if>>待完善</option>
						<option value="1" <c:if test="${queryDto.priceAuditStatus eq 1 }">selected="selected"</c:if>>审核中</option>
						<option value="2" <c:if test="${queryDto.priceAuditStatus eq 2 }">selected="selected"</c:if>>审核通过</option>
						<option value="3" <c:if test="${queryDto.priceAuditStatus eq 3 }">selected="selected"</c:if>>审核不通过</option>
					</select>
				</li>
				<li>
					<label class="infor_name">归属产品经理</label>
					<select class="input-middle f_left" name="productManagerName">
						<option value="" >全部</option>
						<c:forEach var="manage" items="${managerUserList}">
							<option value="${manage.username}" <c:if test="${queryDto.productManagerName == manage.username}">selected="selected"</c:if> >${manage.username}</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">归属产品助理</label>
					<select class="input-middle f_left" name="productAssistantName">
						<option value="">全部</option>
						<c:forEach var="ass" items="${assUserList}">
							<option value="${ass.username}" <c:if test="${queryDto.productAssistantName == ass.username}">selected="selected"</c:if> >${ass.username}</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">审核人</label>
					<select class="input-middle f_left" name="checkPersonId">
						<option value="">全部</option>
						<c:forEach var="checkPerson" items="${checkPersonList}">
							<option value="${checkPerson.userId}" <c:if test="${queryDto.checkPersonId == checkPerson.userId}">selected="selected"</c:if> >${checkPerson.username}</option>
						</c:forEach>
					</select>
				</li>

				<li>
					<label class="infor_name">是否已核价</label>
					<select class="input-middle f_left" name="alreadyPriced">
						<option value="">全部</option>
						<option value="0" <c:if test="${queryDto.alreadyPriced == '0'}">selected="selected"</c:if>>未核价</option>
						<option value="1" <c:if test="${queryDto.alreadyPriced == '1'}">selected="selected"</c:if>>已核价</option>
					</select>
				</li>

				<li>
					<label class="infor_name">是否禁用</label>
					<select class="input-middle f_left" name="disabled">
						<option value="">全部</option>
						<option value="0" <c:if test="${queryDto.disabled == '0'}">selected="selected"</c:if>>启用</option>
						<option value="1" <c:if test="${queryDto.disabled == '1'}">selected="selected"</c:if>>禁用</option>
					</select>
				</li>

				<li>
					<label class="infor_name">是否需要报备</label>
					<select class="input-middle f_left" name="isNeedReport">
						<option value="">全部</option>
						<option value="0" <c:if test="${queryDto.isNeedReport == '0'}">selected="selected"</c:if>>否</option>
						<option value="1" <c:if test="${queryDto.isNeedReport == '1'}">selected="selected"</c:if>>是</option>
					</select>
				</li>

				<li>
					<label class="infor_name">经销价是否含运费</label>
					<select class="input-middle f_left" name="isSaleContainsFee">
						<option value="">全部</option>
						<option value="0" <c:if test="${queryDto.isSaleContainsFee == '0'}">selected="selected"</c:if>>待维护</option>
						<option value="1" <c:if test="${queryDto.isSaleContainsFee == '1'}">selected="selected"</c:if>>含运费</option>
						<option value="2" <c:if test="${queryDto.isSaleContainsFee == '2'}">selected="selected"</c:if>>不含运费</option>
					</select>
				</li>
			</ul>
			<div class="tcenter">

				<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>

				<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>

				<button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
						layerparams='{"width":"500px","height":"200px","title":"批量核价","link":"./batchUploadPage.do"}'>批量核价</button>
			</div>
		</form>
	</div>
	<div class="normal-list-page">
		<table class="table table-bordered table-striped table-condensed table-centered">
			<thead>
			<tr>
				<th class="wid6">序号</th>
				<th class="wid6">订货号</th>
				<th class="wid6">商品名称</th>
				<th class="wid6">单位</th>
				<th class="wid6">产品归属</th>
				<th class="wid6">是否需报备</th>
				<th class="wid6">采购成本</th>
				<th class="wid6">市场价</th>
				<th class="wid6">终端价</th>
				<th class="wid6">
					经销价
					<a href="javascript:void(0);" class="custom-tooltip" data-toggle="tooltip" data-html="true" data-placement="bottom" 
					  title="销售价填写规范：&#10;0 < 单价 < 10，可保留2位小数。【例:6.99】，&#10;10 <= 单价 < 100，只可保留1位小数，且小数只能为8或9 【例:69.9】，&#10;100 <= 单价 < 1000，只能填写整数，且个数仅限于0、5、8、9 【例:135】，&#10;1000 <= 单价 < 100000，只能填写整数，且个位数仅限于0、8、9 【例:19999】，&#10;单价超过100000时，只能填写整数，且个位、十位必须为0 【例:158000】">
						<i class="iconbluesigh ml4"></i>
					</a>
				</th>	
				<th class="wid6">集团价</th>
				<th class="wid6">电商价</th>
				<th class="wid8">
					商城是否可见
					<a href="javascript:void(0);" class="custom-tooltip" data-toggle="tooltip" data-html="true" data-placement="bottom" 
					  title="商城价格是否可见除了此设置外，&#10;还取决于商品是否推送到商城，&#10;在商城是否上架，客户是否登录，&#10;客户是否资质通过等因素。">
						<i class="iconbluesigh ml4"></i>
					</a>
				</th>
				<th class="wid6">核价审核状态</th>
				<th class="wid6">是否已核价</th>
				<th class="wid6">是否禁用</th>
				<th class="wid6">禁用原因</th>
				<th class="wid6">更新时间</th>
				<th class="wid10">操作</th>
			</tr>
			</thead>
			<tbody>
			<c:forEach var="priceChangeApply" items="${priceChangeApplyList}"  varStatus="num">
				<tr>
					<td>${num.count}</td>
					<td>${priceChangeApply.skuNo}</td>
					<td>
						<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${priceChangeApply.skuNo}","link":"./goods/goods/viewbaseinfo.do?goodsId=${fn:substringAfter(priceChangeApply.skuNo,'V')}","title":"产品信息"}'>${priceChangeApply.skuName}
						</a>
					</td>
					<td>${priceChangeApply.unitName}</td>
					<td>
						<c:choose>
							<c:when test="${priceChangeApply.belongProductManager == '' && priceChangeApply.belongProductAssit == ''}">
								-
							</c:when>
							<c:otherwise>
								${priceChangeApply.belongProductManager}&${priceChangeApply.belongProductAssit}
							</c:otherwise>
						</c:choose>
					</td>
					<td>
						<c:choose>
							<c:when test="${priceChangeApply.isNeedReport == 1}">
								是



								<div class="customername pos_rel" style="display: inline-block;">
									<i class="iconbluesigh ml4"></i>
									<div class="pos_abs customernameshow">

										<div class="table-item item-col">
											<div class="table-td">
												是否获得授权：
												<c:if test="${priceChangeApply.isAuthorized eq 1}">
													有授权
												</c:if>
												<c:if test="${priceChangeApply.isAuthorized eq 0}">
													无授权
												</c:if>
											</div>
										</div>

										<c:if test="${priceChangeApply.isAuthorized eq 1}">
										<div class="table-item item-col">
											<div class="table-td">
												授权范围：
												<c:forEach items="${priceChangeApply.skuAuthorizationVo.skuAuthorizationItemVoList}" var="skuAuthorizationItem">
													<c:choose>
														<c:when test="${fn:length(skuAuthorizationItem.regionIds) == fn:length(regions)}">
															"全国"
														</c:when>
														<c:otherwise>
															<c:forEach items="${skuAuthorizationItem.regionIds}" var="regionId"  varStatus="regionStatus">
																<c:if test="${regionStatus.first}">
																	"
																</c:if>
																<c:forEach items="${regions}" var="region">
																	<c:if test="${region.regionId eq regionId}">
																		${region.regionName}
																	</c:if>
																</c:forEach>
																<c:if test="${!regionStatus.last}">
																	、
																</c:if>
																<c:if test="${regionStatus.last}">
																	"
																</c:if>
															</c:forEach>
														</c:otherwise>
													</c:choose>
													的
													<c:choose>
														<c:when test="${fn:length(skuAuthorizationItem.terminalTypeIds) == fn:length(terminalTypes)}">
															"全部终端"
														</c:when>
														<c:otherwise>
															<c:forEach items="${skuAuthorizationItem.terminalTypeIds}" var="terminalTypeId" varStatus="terminalTypeStatus">
																<c:if test="${terminalTypeStatus.first}">
																	"
																</c:if>
																<c:forEach items="${terminalTypes}" var="terminalType">
																	<c:if test="${terminalType.sysOptionDefinitionId eq terminalTypeId}">
																		${terminalType.title}
																	</c:if>
																</c:forEach>
																<c:if test="${!terminalTypeStatus.last}">
																	、
																</c:if>
																<c:if test="${terminalTypeStatus.last}">
																	"
																</c:if>
															</c:forEach>
														</c:otherwise>
													</c:choose>
													已获得授权
													</br>
												</c:forEach>
												<label style="color: red;font-weight: bold">注：除以上范围，需要报备。</label>
											</div>
											</c:if>

										</div>
									</div>
								</div>
							</c:when>
							<c:when test="${priceChangeApply.isNeedReport eq 0}">
								否
							</c:when>
							<c:otherwise>
								-
							</c:otherwise>
						</c:choose>

					</td>
					<td>
						<c:choose>
							<c:when test="${priceChangeApply.middlePrice == ''}">
								-
							</c:when>
							<c:otherwise>
								${priceChangeApply.middlePrice}
							</c:otherwise>
						</c:choose>

					</td>
					<td>
						<c:if test="${priceChangeApply.verifyStatusName!='审核中'}">
							<c:choose>

								<c:when test="${priceChangeApply.marketPrice == null}">
									-
								</c:when>
								<c:otherwise>
									${myfn:toString(priceChangeApply.marketPrice)}
								</c:otherwise>
							</c:choose>
						</c:if>
						<c:if test="${priceChangeApply.verifyStatusName=='审核中'}">
							<c:choose>

								<c:when test="${priceChangeApply.lastMarketPrice == null}">
									-
								</c:when>
								<c:otherwise>
									${myfn:toString(priceChangeApply.lastMarketPrice)}
								</c:otherwise>
							</c:choose>
						</c:if>

					</td>
					<td>
						<c:if test="${priceChangeApply.verifyStatusName!='审核中'}">
							<c:choose>
								<c:when test="${priceChangeApply.terminalPrice == null}">
									-
								</c:when>
								<c:otherwise>
									${myfn:toString(priceChangeApply.terminalPrice)}
								</c:otherwise>
							</c:choose>
						</c:if>
						<c:if test="${priceChangeApply.verifyStatusName=='审核中'}">
							<c:choose>

								<c:when test="${priceChangeApply.lastTerminalPrice == null}">
									-
								</c:when>
								<c:otherwise>
									${myfn:toString(priceChangeApply.lastTerminalPrice)}
								</c:otherwise>
							</c:choose>
						</c:if>
					</td>
					<td>
						<c:if test="${priceChangeApply.verifyStatusName!='审核中'}">
							<c:choose>
								<c:when test="${priceChangeApply.distributionPrice == null}">
									-
								</c:when>
								<c:otherwise>
									<div>${myfn:toString(priceChangeApply.distributionPrice)}</div>
									<c:if test="${priceChangeApply.saleContainsFee == 2}">
										<div style="color:red;font-size:12px;">(不含运费)</div>
									</c:if>
								</c:otherwise>
							</c:choose>
						</c:if>
						<c:if test="${priceChangeApply.verifyStatusName=='审核中'}">
							<c:choose>
								<c:when test="${priceChangeApply.lastDistributionPrice == null}">
									-
								</c:when>
								<c:otherwise>
									<div>${myfn:toString(priceChangeApply.lastDistributionPrice)}</div>
									<c:if test="${priceChangeApply.saleContainsFee == 2}">
										<div style="color:red;font-size:12px;">(不含运费)</div>
									</c:if>
								</c:otherwise>
							</c:choose>
						</c:if>
					</td>
					<td>
						<c:if test="${priceChangeApply.verifyStatusName!='审核中'}">
							<c:choose>
								<c:when test="${priceChangeApply.groupPrice == null}">
									-
								</c:when>
								<c:otherwise>
									${myfn:toString(priceChangeApply.groupPrice)}
								</c:otherwise>
							</c:choose>
						</c:if>
						<c:if test="${priceChangeApply.verifyStatusName=='审核中'}">
							<c:choose>

								<c:when test="${priceChangeApply.lastGroupPrice == null}">
									-
								</c:when>
								<c:otherwise>
									${myfn:toString(priceChangeApply.lastGroupPrice)}
								</c:otherwise>
							</c:choose>
						</c:if>
					</td>
					<%--电商价--%>
					<td>
						<c:if test="${priceChangeApply.verifyStatusName!='审核中'}">
							<c:choose>
								<c:when test="${priceChangeApply.electronicCommercePrice == null}">
									-
								</c:when>
								<c:otherwise>
									${myfn:toString(priceChangeApply.electronicCommercePrice)}
								</c:otherwise>
							</c:choose>
						</c:if>
						<c:if test="${priceChangeApply.verifyStatusName=='审核中'}">
							<c:choose>

								<c:when test="${priceChangeApply.lastElectronicCommercePrice == null}">
									-
								</c:when>
								<c:otherwise>
									${myfn:toString(priceChangeApply.lastElectronicCommercePrice)}
								</c:otherwise>
							</c:choose>
						</c:if>

					</td>
					<%--商城是否可见--%>
					<td>
						${(priceChangeApply.vdIsVisible == 1)? "是":"否"}
					</td>
					<td>${priceChangeApply.verifyStatusName}</td>
					<td>${(priceChangeApply.auditPass == 1 && priceChangeApply.disabled == 0)? "是":"否"}</td>
					<td>${priceChangeApply.disabled == 1? "禁用":"启用"}</td>
					<td>${priceChangeApply.disableReason}</td>
					<td>${priceChangeApply.modTime}</td>


					<td>

						<%--<span class="edit-user " onclick="toEdit('${priceChangeApply.id}')">编辑</span>

						<span class="edit-user " onclick="toDetail('${priceChangeApply.id}')">详情</span>--%>

						<c:if test="${priceChangeApply.verifyStatusName!='审核中'}">
							<span class="edit-user" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./price/basePriceMaintain/edit.do?skuPriceChangeApplyId=${priceChangeApply.id}","title":"编辑"}' onclick="toEdit('${priceChangeApply.id}',this)">编辑</span>
						</c:if>


						<span class="edit-user" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./price/basePriceMaintain/detail.do?skuPriceChangeApplyId=${priceChangeApply.id}","title":"详情"}' onclick="toDetail('${priceChangeApply.id}',this)">详情</span>
						<c:if test="${priceChangeApply.disabled == 1}">
							<c:choose>
								<c:when test="${priceChangeApply.verifyStatusName=='审核中'}">
									<button type="button" class="edit-user bt-bg-style bt-small" style="background-color:#ccc;cursor:not-allowed;" disabled title="审核中的价格无法启用">启用</button>
								</c:when>
								<c:otherwise>
									<button type="button" class="edit-user bt-bg-style bt-small" onclick="enablePrice('${priceChangeApply.id}');">启用</button>
								</c:otherwise>
							</c:choose>
						</c:if>

						<c:if test="${priceChangeApply.disabled == 0}">
							<button type="button" class="edit-user bt-bg-style bt-small" onclick="disablePrice('${priceChangeApply.skuNo}',${priceChangeApply.id},'${priceChangeApply.verifyStatusName}');">禁用</button>
							<span class="edit-user  pop-new-data bt-small bt-bg-style" id="disablePriceSpan"
								  layerParams='{"width":"700px","height":"250px","title":"禁用价格","link":"${pageContext.request.contextPath}/price/basePriceMaintain/toDisablePricePage.do?skuPriceChangeApplyId=${priceChangeApply.id}&verifyStatusName=${priceChangeApply.verifyStatusName}"}' style="display: none">
								禁用
							</span>
						</c:if>
						
						<c:if test="${priceChangeApply.vdIsVisible == 1}">
							<button type="button" class="edit-user bt-bg-style bt-small"  onclick="toggleVdVisibility(${priceChangeApply.id}, 0);">商城不可见</button>
						</c:if>
						<c:if test="${priceChangeApply.vdIsVisible == 0 || priceChangeApply.vdIsVisible == null}">
							<c:choose>
								<c:when test="${priceChangeApply.verifyStatusName=='审核中' ? 
									(priceChangeApply.lastMarketPrice != null && priceChangeApply.lastTerminalPrice != null && 
									priceChangeApply.lastDistributionPrice != null && priceChangeApply.lastGroupPrice != null) : 
									(priceChangeApply.marketPrice != null && priceChangeApply.terminalPrice != null && 
									priceChangeApply.distributionPrice != null && priceChangeApply.groupPrice != null)}">
									<button type="button" class="edit-user bt-bg-style bt-small" onclick="toggleVdVisibility(${priceChangeApply.id}, 1);">商城可见</button>
								</c:when>
								<c:otherwise>
									<button type="button" class="edit-user bt-bg-style bt-small" style="background-color:#ccc;cursor:not-allowed;" disabled title="价格信息不完整，无法操作">商城可见</button>
								</c:otherwise>
							</c:choose>
						</c:if>
					</td>
				</tr>
			</c:forEach>
			<c:if test="${empty priceChangeApplyList}">
				<tr>
					<td colspan='16'>查询无结果！请尝试使用其它搜索条件。</td>
				</tr>
			</c:if>
			</tbody>
		</table>
	</div>
	<tags:page page="${page}" />
</div>

<script type="text/javascript">

	function toDetail(skuPriceChangeApplyId,span) {
		$.ajax({
			async:true,
			url:page_url+'/price/basePriceMaintain/toDetailValidator.do',
			data:{"skuPriceChangeApplyId":skuPriceChangeApplyId},
			type:"POST",
			dataType : "json",
			success:function(data){
				if(data.code==0){
					openTab(span);
				}else{
					layer.alert(data.message)
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	}

	function toEdit(skuPriceChangeApplyId,span){
		$.ajax({
			async:true,
			url:page_url+'/price/basePriceMaintain/toEditValidator.do',
			data:{"skuPriceChangeApplyId":skuPriceChangeApplyId},
			type:"POST",
			dataType : "json",
			success:function(data){
				if(data.code==0){

					debugger;
					var verifyStatus = data.data;
					if(verifyStatus == '审核中'){
						layer.alert("该商品核价正在审核中，不可进行编辑");
					}else{
						openTab(span);
					}
				}else{
					layer.alert(data.message)
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	}

	function openTab(span){
		var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
		var newPageId;
		var tabTitle = $(span).attr('tabTitle');
		if (typeof(tabTitle) == 'undefined') {
			alert('参数错误');
		} else {
			tabTitle = $.parseJSON(tabTitle);
		}
		var id = tabTitle.num;
		// var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
		var name = tabTitle.title;
		var uri = tabTitle.link;
		var closable = 1;
		var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
		self.parent.closableTab.addTab(item);
		self.parent.closableTab.resizeMove();
		$(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
	}

	/**
	 * 禁用价格
	 */
	function disablePrice(skuNo,priceChangeApplyId,verifyStatusName){
		//请求医械购 查看sku是否已经在医械购上架 如果已经上架 则不允许禁用价格
		$.ajax({
			async:true,
			url:page_url+'/price/basePriceMaintain/skuIsShelfOnYXG.do',
			data:{"skuNo":skuNo},
			type:"POST",
			dataType : "json",
			success:function(data){
				if(data.code==0){
					layer.alert("该商品已在医械购上架，无法禁用。")
					return;
				}

				$("#disablePriceSpan").attr('layerparams','{"width":"700px","height":"300px","title":"禁用价格","link":"${pageContext.request.contextPath}/price/basePriceMaintain/toDisablePricePage.do?skuNo='+skuNo+'&skuPriceChangeApplyId='+priceChangeApplyId+'&verifyStatusName='+encodeURI(verifyStatusName)+'"}');
				$("#disablePriceSpan").click();

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	}

	function enablePrice(priceChangeApplyId) {


		layer.confirm("确定要启用该价格么？", {
			btn : [ '确定', '取消' ]
			//按钮
		}, function() {
			$.ajax({
				async:true,
				url:page_url+'/price/basePriceMaintain/enablePrice.do',
				data:{"skuPriceChangeApplyId":priceChangeApplyId},
				type:"POST",
				dataType : "json",
				success:function(data){
					if(data.code == '0'){
						refreshPageList(data);
					}else{
						layer.alert(data.message);
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
		}, function() {
		});
	}
	
	/**
	 * 切换商城可见性
	 */
	function toggleVdVisibility(priceChangeApplyId, isVdVisible) {
		var confirmMsg = isVdVisible == 1 ? "确定要将该价格设为商城可见吗？" : "确定要将该价格设为商城不可见吗？";
		// 先进行权限验证
		$.ajax({
			async: true,
			url: page_url+'/price/basePriceMaintain/toEditValidator.do',
			data: {"skuPriceChangeApplyId": priceChangeApplyId},
			type: "POST",
			dataType: "json",
			success: function(data) {
				if(data.code == 0) {
					// 通过权限验证后，执行商城可见性切换操作
					layer.confirm(confirmMsg, {
						btn: ['确定', '取消']
					}, function() {
						$.ajax({
							async: true,
							url: page_url+'/price/basePriceMaintain/toggleVdVisibility.do',
							data: {
								"skuPriceChangeApplyId": priceChangeApplyId,
								"isVdVisible": isVdVisible
							},
							type: "POST",
							dataType: "json",
							success: function(data){
								if(data.code == '0'){
									refreshPageList(data);
								} else {
									layer.alert(data.message);
								}
							},
							error: function(data){
								layer.alert("设置失败.");
							}
						});
					}, function() {
						// 取消操作，不做任何处理
					});
				}
				if(data.code == -1){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}

	$(document).ready(function() {
		$('.custom-tooltip').tooltip({
			html: true,
			container: 'body'
		});
	});
</script>

<script type="text/javascript"
		src="${pageContext.request.contextPath}/static/js/price/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../common/footer.jsp"%>
