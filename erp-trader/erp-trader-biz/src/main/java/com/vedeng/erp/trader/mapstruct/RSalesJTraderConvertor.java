package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.trader.dto.RSalesJTraderDto;
import com.vedeng.erp.trader.domain.entity.RSalesJTraderEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.mapstruct
 * @Date 2024/1/4 16:07
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface RSalesJTraderConvertor extends BaseMapStruct<RSalesJTraderEntity, RSalesJTraderDto> {
}
