package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecord;
import com.vedeng.erp.aftersale.vo.AfterSalesInstallServiceRecordVo;

import java.util.List;

public interface AfterSalesInstallServiceRecordMapper {
    int deleteByPrimaryKey(Integer afterSalesServiceId);

    int insert(AfterSalesInstallServiceRecord record);

    int insertSelective(AfterSalesInstallServiceRecord record);

    AfterSalesInstallServiceRecord selectByPrimaryKey(Integer afterSalesServiceId);

    int updateByPrimaryKeySelective(AfterSalesInstallServiceRecord record);

    int updateByPrimaryKey(AfterSalesInstallServiceRecord record);

    /**
     * 根据售后单id查询安调服务记录
     */
    List<AfterSalesInstallServiceRecordVo> getInstallServiceRecordList(Integer afterSalesId);

    /**
     * 逻辑删除
     * @param afterSalesServiceId
     * @return
     */
    int logicDelete(Integer afterSalesServiceId);

}