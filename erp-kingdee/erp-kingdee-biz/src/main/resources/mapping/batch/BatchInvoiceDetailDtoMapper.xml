<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE_DETAIL-->
        <id column="INVOICE_DETAIL_ID" jdbcType="INTEGER" property="invoiceDetailId"/>
        <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId"/>
        <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailgoodsId"/>
        <result column="PRICE" jdbcType="DECIMAL" property="price"/>
        <result column="NUM" jdbcType="DECIMAL" property="num"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="CHANGED_GOODS_NAME" jdbcType="VARCHAR" property="changedGoodsName"/>
    </resultMap>
    <resultMap id="BaseResultMapAndBuyOrder" type="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE_DETAIL-->
        <id column="INVOICE_DETAIL_ID" jdbcType="INTEGER" property="invoiceDetailId"/>
        <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId"/>
        <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailgoodsId"/>
        <result column="PRICE" jdbcType="DECIMAL" property="price"/>
        <result column="NUM" jdbcType="DECIMAL" property="num"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="CHANGED_GOODS_NAME" jdbcType="VARCHAR" property="changedGoodsName"/>
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
        <result column="SKU" jdbcType="VARCHAR" property="sku"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        INVOICE_DETAIL_ID,
        INVOICE_ID,
        DETAILGOODS_ID,
        PRICE,
        NUM,
        TOTAL_AMOUNT,
        CHANGED_GOODS_NAME
    </sql>

    <select id="selectByDetailIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_DETAIL
        where INVOICE_DETAIL_ID in
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-12-06-->
    <select id="findByInvoiceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_DETAIL
        where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </select>


    <select id="findByInvoiceIdAndBuyorderGoodsId" resultMap="BaseResultMapAndBuyOrder">
        select TID.INVOICE_DETAIL_ID,
               TID.INVOICE_ID,
               TID.DETAILGOODS_ID,
               TID.PRICE,
               TID.NUM,
               TID.TOTAL_AMOUNT,
               TID.CHANGED_GOODS_NAME,
               TBG.SKU
        from T_INVOICE_DETAIL TID
                 left join T_BUYORDER_GOODS TBG on TID.DETAILGOODS_ID = TBG.BUYORDER_GOODS_ID
        where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
          and IS_DELETE = 0
    </select>

    <select id="findCostCategoryKingDeeIdBySaleOrderGoodsId" resultType="java.lang.String">
        select TSCC.UNIT_KING_DEE_NO
        from T_INVOICE TI
                 left join T_AFTER_SALES TAS on TI.RELATED_ID = TAS.AFTER_SALES_ID
                 left join T_AFTER_SALES_GOODS TASG on TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID and TASG.GOODS_TYPE = 0
                 left join T_SALEORDER_GOODS TSG on TASG.ORDER_DETAIL_ID = TSG.SALEORDER_GOODS_ID
                 left join V_CORE_SKU VCS on TSG.SKU = VCS.SKU_NO
                 left join T_SYS_COST_CATEGORY TSCC on VCS.COST_CATEGORY_ID = TSCC.COST_CATEGORY_ID
        where TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER};
    </select>

    <select id="querySaleInvoiceByInvoiceIds" resultMap="BaseResultMap">
        SELECT A.INVOICE_DETAIL_ID,
               A.INVOICE_ID,
               A.DETAILGOODS_ID,
               A.PRICE,
               A.NUM,
               A.TOTAL_AMOUNT,
               A.CHANGED_GOODS_NAME,
               B.GOODS_ID,
               B.SKU
        FROM T_INVOICE_DETAIL A
                 LEFT JOIN T_SALEORDER_GOODS B ON A.DETAILGOODS_ID = B.SALEORDER_GOODS_ID
                 LEFT JOIN V_CORE_SKU C ON B.GOODS_ID = C.SKU_ID
        WHERE
           B.IS_GIFT = 0
          AND C.IS_VIRTURE_SKU = 0
          AND  A.INVOICE_ID IN
        <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="findByAfterSalesId" resultType="java.lang.Integer">
        select TI.INVOICE_ID
        from T_INVOICE TI
                 left join T_AFTER_SALES TAS on TI.AFTER_SALES_ID = TAS.AFTER_SALES_ID
        WHERE TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>


    <select id="getAllValidInvoiceDetailByInvoiceIds" resultMap="BaseResultMap">
        SELECT
        a.INVOICE_DETAIL_ID, a.INVOICE_ID, a.DETAILGOODS_ID, PRICE, abs(ifnull(a.NUM,0))-sum(abs(ifnull(TRIDJOL.NUM,0))) as NUM, TOTAL_AMOUNT, CHANGED_GOODS_NAME,
        abs(ifnull(a.NUM,0))-sum(abs(ifnull(TRIDJOL.NUM,0))) calcNum
        FROM
        T_INVOICE_DETAIL a
        left join T_R_INVOICE_DETAIL_J_OPERATE_LOG TRIDJOL on a.INVOICE_DETAIL_ID = TRIDJOL.INVOICE_DETAIL_ID and TRIDJOL.IS_DELETE = 0
        WHERE
        a.INVOICE_ID IN
        <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
        group by a.INVOICE_DETAIL_ID HAVING calcNum>0
    </select>

    <select id="getSaleOrderInvoiceDetailList" resultType="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto">
        SELECT tid.INVOICE_DETAIL_ID,
               tid.NUM,
               tid.PRICE,
               tscc.UNIT_KING_DEE_NO,
               vcs.SKU_NO AS sku,
               tid.DETAILGOODS_ID,
               vcs.IS_VIRTURE_SKU,
               tsg.GOODS_ID,
               tid.TOTAL_AMOUNT,
               tid.INVOICE_ID
        FROM T_INVOICE_DETAIL tid
                 LEFT JOIN T_SALEORDER_GOODS tsg ON tsg.SALEORDER_GOODS_ID = tid.DETAILGOODS_ID
                 LEFT JOIN V_CORE_SKU vcs ON vcs.SKU_ID = tsg.GOODS_ID
                 LEFT JOIN T_SYS_COST_CATEGORY tscc ON tscc.COST_CATEGORY_ID = vcs.COST_CATEGORY_ID
        WHERE tid.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </select>

    <select id="getRedInvoiceTotalNumByCondition" resultType="java.math.BigDecimal">
        SELECT SUM(T2.NUM)
        FROM T_INVOICE T1
                 LEFT JOIN T_INVOICE_DETAIL T2 ON T1.INVOICE_ID = T2.INVOICE_ID
        WHERE VALID_STATUS = 1
          AND T1.COLOR_TYPE = 1
          AND T1.IS_ENABLE = 1
          AND T1.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
          AND T2.DETAILGOODS_ID = #{detailGoodsId,jdbcType=INTEGER}
        GROUP BY T2.DETAILGOODS_ID
    </select>


<!--    <select id="querySaleInvoiceByInvoiceIds" resultMap="BaseResultMap">-->
<!--        SELECT A.INVOICE_DETAIL_ID,-->
<!--        A.INVOICE_ID,-->
<!--        A.DETAILGOODS_ID,-->
<!--        A.PRICE,-->
<!--        A.NUM,-->
<!--        A.TOTAL_AMOUNT,-->
<!--        A.CHANGED_GOODS_NAME,-->
<!--        B.GOODS_ID,-->
<!--        B.SKU-->
<!--        FROM T_INVOICE_DETAIL A-->
<!--        LEFT JOIN T_SALEORDER_GOODS B ON A.DETAILGOODS_ID = B.SALEORDER_GOODS_ID-->
<!--        LEFT JOIN V_CORE_SKU C ON B.GOODS_ID = C.SKU_ID-->
<!--        WHERE-->
<!--        B.IS_GIFT = 0-->
<!--        AND C.IS_VIRTURE_SKU = 0-->
<!--        AND  A.INVOICE_ID IN-->
<!--        <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">-->
<!--            #{invoiceId,jdbcType=INTEGER}-->
<!--        </foreach>-->
<!--    </select>-->

    <select id="findByAfterSalesIdGetInvoiceId" resultType="java.lang.Integer">
        select TI.INVOICE_ID
        from T_INVOICE TI
        left join T_AFTER_SALES TAS on TI.AFTER_SALES_ID = TAS.AFTER_SALES_ID
        WHERE TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>
    <select id="getInvoiceDetailByInvoiceIdList" resultType="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto">
        SELECT tid.INVOICE_DETAIL_ID,
               tid.NUM,
               tid.PRICE,
               tscc.UNIT_KING_DEE_NO,
               vcs.SKU_NO AS sku,
               tid.DETAILGOODS_ID,
               vcs.IS_VIRTURE_SKU,
               tsg.GOODS_ID,
               tid.TOTAL_AMOUNT,
               tid.INVOICE_ID
        FROM T_INVOICE_DETAIL tid
                 LEFT JOIN T_SALEORDER_GOODS tsg ON tsg.SALEORDER_GOODS_ID = tid.DETAILGOODS_ID
                 LEFT JOIN V_CORE_SKU vcs ON vcs.SKU_ID = tsg.GOODS_ID
                 LEFT JOIN T_SYS_COST_CATEGORY tscc ON tscc.COST_CATEGORY_ID = vcs.COST_CATEGORY_ID
        WHERE tid.INVOICE_ID in
        <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
            #{invoiceId,jdbcType=INTEGER}
        </foreach>
    </select>


</mapper>