<html>
<head>
    <meta charset="UTF-8">
    <title>虚拟商品列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="virture_sku_list" datasource="erp-datasourcetarget" append_column_url="" append_row_url="" empty_show="-" firstcol="numbers">
    <div id="appendHead"></div>
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <object class="layui-input list-search-item" jdbctype="VARCHAR" type="union" placeholder="空格或%代替任意多个字符" oper="like" name="SKU_NAME,SKU_NO" empty_show="-" style="" alias="">
                </object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">商品名称</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="hidden" placeholder="空格或%代替任意多个字符" oper="like" name="SKU_NAME" empty_show="-" style="" alias="">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">订货号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="hidden" placeholder="空格或%代替任意多个字符" oper="like" name="SKU_NO" empty_show="-" style="" alias="">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">操作人</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="select-search" name="VIRTURE_CREATOR" placeholder="请输入" style="" alias="" jdbctype=""
                                                    data="SELECT DISTINCT u.USER_ID K, lower(u.USERNAME) V FROM T_USER u
                                                    LEFT JOIN T_R_USER_POSIT up ON u.USER_ID = up.USER_ID
                                                    LEFT JOIN T_POSITION p on up.POSITION_ID = p.POSITION_ID
                                                    LEFT JOIN T_ORGANIZATION o ON p.ORG_ID = o.ORG_ID
                                                    WHERE o.TYPE = 311   order by u.USERNAME" datatype="KVSQLCACHE" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">操作时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="VIRTURE_TIME" placeholder="请选择日期"
                        style="" alias=""
                        jdbctype="DATE" data="" datatype="" oper="BETWEEN">
                </object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">费用大类</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="COST_CATEGORY_ID" placeholder="请输入" style=""
                                                    alias=""
                                                    jdbctype="" data="SELECT DISTINCT COST_CATEGORY_ID K,CATEGORY_NAME V FROM T_SYS_COST_CATEGORY WHERE IS_DEL = 0" datatype="KVSQLCACHE" oper="in"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">启用状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="STATUS" placeholder="全部" style="" alias="A.STATUS" jdbctype=""
                        data="[{&quot;V&quot;:&quot;禁用&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;启用&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;修改审核中&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="json" oper="">
                <select/>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">是否库存管理</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="HAVE_STOCK_MANAGE" placeholder="全部" style="" alias="" jdbctype=""
                       data="yesno" datatype="" oper="">
                </select>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button item_name="新增" name="新增" url="/goods/vgoods/initChooseSku.do" opentype="MODEL" windowname="" area="70%,80%" style type="table">新增</button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="SKU_NO" name="SKU_NO" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">订货号</th>
            <th item_name="SKU_NAME" name="SKU_NAME" body="td-link"
                url="/goods/vgoods/viewSku.do?skuId=${SKU_ID}&spuId=${SPU_ID}&pageType=0" opentype="PARENT"
                windowname="查看SKU" datatype="" data="" style="min-width:200px;word-break: break-all;position: sticky;" head="18">商品名称</th>
            <th item_name="STATUS" name="STATUS" type="td-select"td opentype="MODEL" windowname="" datatype="json" data="[{&quot;V&quot;:&quot;禁用&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;启用&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;修改审核中&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">启用状态</th>
            <th item_name="ASSIGNMENTS" name="ASSIGNMENTS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">商品归属人</th>
            <th item_name="COST_CATEGORY_ID" name="COST_CATEGORY_ID" body="td-select" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE"
                data="SELECT COST_CATEGORY_ID K,CATEGORY_NAME V FROM T_SYS_COST_CATEGORY WHERE IS_DEL = 0" style="width:110px" head="th">费用大类</th>
            <th item_name="HAVE_STOCK_MANAGE" name="HAVE_STOCK_MANAGE" body="td-select" url="" opentype="MODEL" windowname="" datatype="" data="yesno" style="" head="18">是否可库存管理</th>
            <th item_name="VIRTURE_CREATOR_NAME" name="VIRTURE_CREATOR_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype=""
                style="" head="18">操作人</th>
            <th item_name="VIRTURE_TIME" name="VIRTURE_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">操作时间</th>
            <th type="rowbutton" id="rowbutton">
                <button class="layui-btn list-row-button" type="single" opentype="MODEL"
                        url="/goods/vgoods/editChooseSku.do?relatedId=${SKU_ID}"
                        windowname="编辑" name="MODEL" area="70%,80%">编辑
                </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr colspan=100>
            <td>
                <pre id="express" class="layui-code" orderby="" groupby="">
                    StringBuilder sql=new StringBuilder("
                        SELECT A.SKU_ID,A.SPU_ID,A.SKU_NAME,A.SKU_NO,
                        (CASE WHEN A.STATUS = 0 THEN '禁用' WHEN A.STATUS = 1 THEN '启用' WHEN A.STATUS = 2 THEN '修改审核中' END) STATUS,
                        CONCAT_WS('&',D.USERNAME,E.USERNAME) AS ASSIGNMENTS,
                        A.COST_CATEGORY_ID,A.HAVE_STOCK_MANAGE,A.VIRTURE_CREATOR,C.USERNAME AS VIRTURE_CREATOR_NAME,
                        DATE_FORMAT(A.VIRTURE_TIME, '%Y-%m-%d') VIRTURE_TIME
                        FROM V_CORE_SKU A
                        LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
                        LEFT JOIN T_USER C ON A.VIRTURE_CREATOR = C.USER_ID
                        LEFT JOIN T_USER D ON B.ASSIGNMENT_MANAGER_ID = D.USER_ID
                        LEFT JOIN T_USER E ON B.ASSIGNMENT_ASSISTANT_ID = E.USER_ID
                        WHERE A.IS_VIRTURE_SKU = 1
                    ");
                    list=search(sql);
                    return list;
                </pre>
                <pre id="count" class="layui-code">count(1)</pre>
            </td>
        </tr>
        </tbody>
    </table>
    <div id="appendFoot"></div>
</body>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</html>
