<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>提交修改线下销售所负责的区域</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head> 
 <body id="modifyCustomerRegionSale" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append">

  </div>
  <div class="layui-container"> 
   <form id="inputForm" method="post" class="layui-form"> 
    <div class="layui-card" group_name="EZ_DEFAULT_GROUP"> 
     <div class="layui-card-header">
       EZ_DEFAULT_GROUP 
     </div> 
     <div class="layui-card-body"> 
      <div class="layui-form-item "><label class="layui-form-label">归属销售</label>
       <div class="layui-input-block form-group">
           <select class="form-item" name="BELONG_SALES_ID"  type="search" value="" item_name="BELONG_SALES_ID" placeholder="" style="" alias="" data="" jdbctype=""  datatype="JSON" lay_verify="required" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></select>
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">_CHECKD_IDS</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="_CHECKD_IDS" type="hidden" value="" item_name="_CHECKD_IDS" placeholder="" style="display:none" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
         <div class="layui-form-item ">
             <label class="layui-form-label">_CHECKD_IDS2</label>
             <div class="layui-input-block form-group">
                 <input class="form-item" name="USERLIST" type="hidden" value="" item_name="USERLIST" placeholder="" style="display:none" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
             </div>
         </div>
     </div> 
    </div> 
   </form> 
   <form id="configForm" class="layui-form"> 
    <div class="layui-card" group_name="表单数据配置"> 
     <div class="layui-card-header">
       表单数据配置 
     </div> 
     <div class="layui-card-body"> 
     </div> 
    </div> 
    <div class="layui-form-item ">
     <label class="layui-form-label">初始表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">

        select 1
      </pre>
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">StringBuilder sql=new StringBuilder();
sql.append(" update  T_CUSTOMER_REGION_SALE SET USER_ID = #{userIdOn},USER_ID_DOWN = #{userIdDown}   where REGION_ID in (#{areaId})");
update(sql);
return 1;</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type="">StringBuilder sql=new StringBuilder();
sql.append(" delete from  T_CUSTOMER_REGION_SALE  where PUBLIC_CUSTOMER_REGION_RULES_ID IN (${ID}) ");
update(sql);
return ID;</pre>
     </div> 
    </div>
    <div class="layui-form-item ">
     <label class="layui-form-label">分组表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre> 
     </div> 
    </div>
   </form> 
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {
        var visitorList = eval(parent.$("[name='VISITOR_ID']").attr("itemsjson"));
        console.log(visitorList);
    })
</script> 
  <div id="APPEND_FOOT" class="append">
      <script type="text/javascript">
          var visitorList = eval(parent.$("[name='VISITOR_ID']").attr("itemsjson"));
          $("blockquote").html("共选择了"+($("input[name='_CHECKD_IDS']").val().split(",").length-1)+"个拜访计划");


          console.log(visitorList);
          // var dataArray = JSON.parse(jsonData);

          // 遍历JSON数组并填充select元素
          $.each(visitorList, function(index, item) {
              $('#ITEM_ID_BELONG_SALES_ID').append($('<option>', {
                  value: item.K,
                  text: item.V
              }));
          });


      </script>
  </div> 
 </body>
</html>