package com.newtask.finance;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.service.InvoiceRedConfirmationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 数电发票-红字确认单初始化定时任务
 * 拉取本地没有的确认单 我司线下发起 或购方发起
 * @date 2023/9/20 11:50
 */
@JobHandler("InitInvoiceRedConfirmationTask")
@Component
@Slf4j
public class InitInvoiceRedConfirmationTask extends AbstractJobHandler {

    @Autowired
    InvoiceRedConfirmationService invoiceRedConfirmationService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        invoiceRedConfirmationService.sysInit();
        return ReturnT.SUCCESS;
    }
}
