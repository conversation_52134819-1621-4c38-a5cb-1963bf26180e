/* 线索/商机卡片 */
.business-card {

    .business-leads {}
    .business-change {}

    .business-top {
        border-bottom: solid 1px #F5F7FA;
        padding: 10px;

        .row {
            display: flex;
            justify-content: space-between;
            // align-items: center;

            &.mt {
                margin-top: 5px;
            }

            .business-trader {
                flex: 1;
                min-width: 0;
                margin-right: 10px;
                display: flex;
                align-items: center;

                .trader-name {
                    font-size: 14px;
                    color: #000;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .icon-tianyancha {
                    width: 38px;
                    flex-shrink: 0;
                    font-size: 16px;
                    color: #0084FF;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
            .business-status {
                flex-shrink: 0;

                .leads-status {
                    height: 22px;
                    font-size: 12px;
                    line-height: 22px;
                    padding: 0 5px;
                    border-radius: 3px;

                    &.s0, &.s1 {
                        background: #FFEDE0;
                        color: #FF6600;
                    }
                    &.s2 {
                        background: #E0F3FF;
                        color: #09F;
                    }
                    &.s4 {
                        background: #E3F7E3;
                        color: #13BF13;
                    }
                    &.s3 {
                        background: #E3EAF0;
                        color: #1A4D80;
                    }
                }

                .change-status {
                    height: 22px;
                    font-size: 12px;
                    line-height: 22px;
                    padding: 0 5px;
                    border-radius: 3px;

                    &.s1, &.s2 {
                        background: #FFEDE0;
                        color: #FF6600;
                    }
                    &.s3, &.s4 {
                        background: #E0F3FF;
                        color: #09F;
                    }
                    &.s5 {
                        background: #E3F7E3;
                        color: #13BF13;
                    }
                    &.s6 {
                        background: #E3EAF0;
                        color: #1A4D80;
                    }
                }
            }

            .business-no {
                font-size: 12px;
                color: #999;
            }
            .business-time {
                font-size: 12px;
                color: #999;
                margin-left: 10px;
            }
        }
    }

    .business-bottom {
        padding: 10px;

        .business-attrs {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: -5px;

            .item-attr {
                flex: 0 0 50%;
                min-width: 0;
                font-size: 12px;
                display: flex;
                margin-bottom: 5px;

                &:nth-child(2n-1) {
                    padding-right: 5px;
                }

                .label {
                    color: #999;
                    flex-shrink: 0;
                }

                .value {
                    flex: 1;
                    min-width: 0;
                }

            }
        }

        .pro-name {
            font-size: 12px;
            margin-top: 5px;
        }

        .trader-check {
            margin-top: 10px;
        }
    }
}