package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatPlainInvoiceEntity;

/**
 * <AUTHOR>
 */
public interface KingDeeSalesVatPlainInvoiceMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeSalesVatPlainInvoiceEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeSalesVatPlainInvoiceEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    KingDeeSalesVatPlainInvoiceEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeSalesVatPlainInvoiceEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeSalesVatPlainInvoiceEntity record);
}