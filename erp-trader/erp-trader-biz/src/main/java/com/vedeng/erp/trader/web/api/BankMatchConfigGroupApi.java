package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.dto.BankMatchConfigGroupDto;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalQuery;
import com.vedeng.erp.trader.service.BankMatchConfigGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: bankMatchConfig
 * @date 2024/8/26 9:34
 */
@ExceptionController
@RestController
@RequestMapping("/bankMatchConfig")
public class BankMatchConfigGroupApi {


    @Autowired
    BankMatchConfigGroupService bankMatchConfigGroupService;


    /**
     * 分页查询接口
     *
     * @param query 查询对象
     * @return R
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> page(@RequestBody PageParam<BankMatchConfigGroupDto> query) {
        return R.success(bankMatchConfigGroupService.page(query));
    }


    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> detail(Long groupId) {
        return R.success(bankMatchConfigGroupService.selectOne(groupId));
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> save(@RequestBody BankMatchConfigGroupDto bankMatchConfigGroup) {
        bankMatchConfigGroupService.save(bankMatchConfigGroup);
        return R.success();
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> update(@RequestBody BankMatchConfigGroupDto bankMatchConfigGroup) {
        bankMatchConfigGroupService.update(bankMatchConfigGroup);
        return R.success();
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> delete(Long groupId) {
        bankMatchConfigGroupService.delete(groupId);
        return R.success();
    }

}
