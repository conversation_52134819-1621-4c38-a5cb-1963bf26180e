package com.vedeng.logistics.service.impl;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.wms.model.po.WmsOutputOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class SampleWarehouseGoodsOutDetailServiceImpl extends BaseWarehouseGoodsOutDetailService {

    private static final Integer WMS_OUTPUT_ORDER_TYPE_SAMPLE = 5;

    private static final String DETAIL_URL_SAMPLE_OUT = "/wms/sampleOut/detail.do?sampleOrderId=";


    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(relatedNo, WMS_OUTPUT_ORDER_TYPE_SAMPLE);
        String url = "";
        if (wmsOutputOrder != null) {
            url = REDIRECT_URL_PREFIX + DETAIL_URL_SAMPLE_OUT + wmsOutputOrder.getId();
        }
        return url;
    }

    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.SAMPLE_WAREHOUSE_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo warehouseGoodsOutVo = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.SAMPLE_WAREHOUSE_OUT.getErpCode());
        if (warehouseGoodsOutVo != null){
            String wmsSampleOutOrderNo = warehouseGoodsOutVo.getRelateNo();
            if (StringUtils.isNotBlank(wmsSampleOutOrderNo)){
                WmsOutputOrder wmsReceiveOutOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(wmsSampleOutOrderNo, WMS_OUTPUT_ORDER_TYPE_SAMPLE);
                if(Objects.nonNull(wmsReceiveOutOrder)){
                warehouseGoodsOutVo.setTakeTraderName(wmsReceiveOutOrder.getBorrowTraderName());
                warehouseGoodsOutVo.setTakeTraderContactName(wmsReceiveOutOrder.getReceiver());
                warehouseGoodsOutVo.setTakeTraderContactTelephone(wmsReceiveOutOrder.getReceiverPhone());
                warehouseGoodsOutVo.setTakeTraderContactMobile(wmsReceiveOutOrder.getReceiverTelphone());
                warehouseGoodsOutVo.setTakeArea(wmsReceiveOutOrder.getReceiverAddress());
                warehouseGoodsOutVo.setTakeAddress(wmsReceiveOutOrder.getDetailAddress());
                warehouseGoodsOutVo.setLogisticsComments(wmsReceiveOutOrder.getLogisticCommnet());
                warehouseGoodsOutVo.setBelongUserName(wmsReceiveOutOrder.getCreator());
                if(StringUtils.isNotBlank(wmsReceiveOutOrder.getCreator())){
                    User receiveOutCreator = userMapper.getUserByUserName(wmsReceiveOutOrder.getCreator());
                    if(receiveOutCreator != null && StringUtils.isNotBlank(receiveOutCreator.getOrgName())){
                        warehouseGoodsOutVo.setBelongUserOrgName(receiveOutCreator.getOrgName());
                    }
                }
                }
            }
        }
        return warehouseGoodsOutVo;
    }
}
