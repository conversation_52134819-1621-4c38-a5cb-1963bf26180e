<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.ZxfOrderMapper" >
      <resultMap id="BaseResultMap" type="com.vedeng.order.model.Saleorder">
        <id column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER"/>
        <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER"/>
        <result column="PARENT_ID" property="parentId" jdbcType="INTEGER"/>
        <result column="SALEORDER_NO" property="saleorderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="BIT"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER"/>
        <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="INTEGER"/>
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT"/>
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT"/>
        <result column="STATUS" property="status" jdbcType="BIT"/>
        <result column="PURCHASE_STATUS" property="purchaseStatus" jdbcType="BIT"/>
        <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="BIT"/>
        <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="BIT"/>
        <result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT"/>
        <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT"/>
        <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT"/>
        <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT"/>
        <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT"/>
        <result column="IS_CUSTOMER_ARRIVAL" property="isCustomerArrival" jdbcType="BIT"/>
        <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT"/>
        <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT"/>
        <result column="SERVICE_STATUS" property="serviceStatus" jdbcType="BIT"/>
        <result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="BIT"/>
        <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="CUSTOMER_TYPE" property="customerType" jdbcType="INTEGER"/>
        <result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER"/>
        <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER"/>
        <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER"/>
        <result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR"/>
        <result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR"/>
        <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT"/>
        <result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR"/>
        <result column="SALES_AREA_ID" property="salesAreaId" jdbcType="INTEGER"/>
        <result column="SALES_AREA" property="salesArea" jdbcType="VARCHAR"/>
        <result column="TERMINAL_TRADER_ID" property="terminalTraderId" jdbcType="INTEGER"/>
        <result column="TERMINAL_TRADER_NAME" property="terminalTraderName" jdbcType="VARCHAR"/>
        <result column="TERMINAL_TRADER_TYPE" property="terminalTraderType" jdbcType="INTEGER"/>
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER"/>
        <result column="INVOICE_METHOD" property="invoiceMethod" jdbcType="INTEGER"/>
        <result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER"/>
        <result column="DELIVERY_TYPE" property="deliveryType" jdbcType="INTEGER"/>
        <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER"/>
        <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER"/>
        <result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL"/>
        <result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL"/>
        <result column="LOGISTICS_COLLECTION" property="logisticsCollection" jdbcType="BIT"/>
        <result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL"/>
        <result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER"/>
        <result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR"/>
        <result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR"/>
        <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR"/>
        <result column="FINANCE_COMMENTS" property="financeComments" jdbcType="VARCHAR"/>
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
        <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR"/>
        <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT"/>
        <result column="SUPPLIER_CLAUSE" property="supplierClause" jdbcType="VARCHAR"/>
        <result column="HAVE_ADVANCE_PURCHASE" property="haveAdvancePurchase" jdbcType="BIT"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="BIT"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="HAVE_COMMUNICATE" property="haveCommunicate" jdbcType="BIT"/>
        <result column="PREPARE_COMMENTS" property="prepareComments" jdbcType="VARCHAR"/>
        <result column="MARKETING_PLAN" property="marketingPlan" jdbcType="VARCHAR"/>
        <result column="STATUS_COMMENTS" property="statusComments" jdbcType="INTEGER"/>
        <result column="SYNC_STATUS" property="syncStatus" jdbcType="BIT"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
        <result column="UPDATER" property="updater" jdbcType="INTEGER"/>
        <result column="TRADER_AREA" property="traderArea" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_AREA" property="invoiceTraderArea" jdbcType="VARCHAR"/>
        <result column="IS_PAYMENT" property="isPayment" jdbcType="BIT"/>
        <result column="VERIFIES_TYPE" property="verifiesType" jdbcType="INTEGER"/>
        <result column="VERIFY_USERNAME" property="verifyUsername" jdbcType="VARCHAR"/>
        <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="INTEGER"/>
        <result column="CONTRACT_STATUS" property="contractStatus" jdbcType="INTEGER"/>
        <result column="ADVANCE_PURCHASE_STATUS" property="advancePurchaseStatus" jdbcType="INTEGER"/>
        <result column="ADVANCE_PURCHASE_COMMENTS" property="advancePurchaseComments" jdbcType="VARCHAR"/>
        <result column="ADVANCE_PURCHASE_TIME" property="advancePurchaseTime" jdbcType="BIGINT"/>

		<result column="IS_SALES_PERFORMANCE" property="isSalesPerformance" jdbcType="INTEGER" />
	    <result column="SALES_PERFORMANCE_TIME" property="salesPerformanceTime" jdbcType="BIGINT" />

        <result column="SATISFY_INVOICE_TIME" property="satisfyInvoiceTime" jdbcType="BIGINT"/>
        <result column="SATISFY_DELIVERY_TIME" property="satisfyDeliveryTime" jdbcType="BIGINT"/>

        <result column="IS_DELAY_INVOICE" property="isDelayInvoice" jdbcType="BIT"/>
        <result column="LOCKED_REASON" property="lockedReason" jdbcType="VARCHAR"/>
        <result column="COST_USER_IDS" property="costUserIds" jdbcType="VARCHAR"/>
		<!-- 收票邮箱 -->
		<result column="INVOICE_EMAIL" property="invoiceEmail" jdbcType="VARCHAR" />
		<!-- 订单归属  userId -->
		<result column="OWNER_USER_ID" property="ownerUserId" jdbcType="INTEGER" />

		
		<result column="PAY_TYPE" property="payType" jdbcType="BIT"/>
		<result column="PAYMENT_MODE" property="paymentMode" jdbcType="BIT"/>
		
		<result column="TRADER_AREA_ID" property="traderAreaId" jdbcType="INTEGER"/>
		<result column="TAKE_TRADER_AREA_ID" property="takeTraderAreaId" jdbcType="INTEGER"/>
		<result column="INVOICE_TRADER_AREA_ID" property="invoiceTraderAreaId" jdbcType="INTEGER"/>
		<result column="CREATE_MOBILE" property="createMobile" jdbcType="VARCHAR"/>
		<result column="BD_MOBILE_TIME" property="bdMobileTime" jdbcType="BIT"/>
		  <result column="ACTION_ID" property="actionId" jdbcType="INTEGER"/>
		  <result column="IS_PRINTOUT" property="isPrintout" jdbcType="INTEGER"/>

		  <result column="DELIVERY_METHOD" property="deliveryMethod" jdbcType="INTEGER"/>
		  <result column="SEND_TO_PC" property="sendToPc" jdbcType="INTEGER"/>
		  <result column="SSO_ACCOUNT_ID" property="ssoAccountId" jdbcType="INTEGER"/>
		  <result column="WEB_ACCOUNT_ID" property="webAccountId" jdbcType="INTEGER"/>
		  <result column="IS_SAME_ADDRESS" property="isSameAddress" jdbcType="TINYINT"/>
		  <result column="INVOICE_SEND_NODE" property="invoiceSendNode" jdbcType="TINYINT"/>

		  <result column="IS_COUPONS" property="isCoupons" jdbcType="INTEGER"/>
		  <result column="CONTRACT_URL" property="contractUrl" jdbcType="VARCHAR" />

	   <result column="AUTO_AUDIT" property="autoAudit" jdbcType="INTEGER"/>

    </resultMap>

     <resultMap type="com.vedeng.order.model.SaleorderContract" id="ContractResultMap" extends="BaseResultMap">
     	<result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR"/>
      	<result column="FIRST_PAYMENT_TIME" property="firstPaymentTime" jdbcType="VARCHAR"/>
       	<result column="NOW_DAY_BETWEEN" property="nowDayBetween" jdbcType="INTEGER"/>
       	<result column="CONTRACT_TYPE" property="contractType" jdbcType="VARCHAR"/>
       	<result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="DECIMAL"/>
       	<result column="OPT_USER_NAME" property="optUserName" jdbcType="VARCHAR"/>
    </resultMap>

	<resultMap type="com.vedeng.order.model.GoodsData" id="goodsDataMap">
		<result column="GOODS_ID" property="goodsId" jdbcType="INTEGER"/>
		<result column="occupyNum" property="occupyNum" jdbcType="INTEGER"/>
		<result column="saleNum30" property="saleNum30" jdbcType="INTEGER"/>
		<result column="needNum" property="needNum" jdbcType="INTEGER"/>
	</resultMap>
	<sql id="Base_Column_List">
	   SALEORDER_ID, QUOTEORDER_ID, PARENT_ID, SALEORDER_NO, M_SALEORDER_NO, ORDER_TYPE,
    COMPANY_ID, SOURCE, CREATOR_ORG_ID, CREATOR_ORG_NAME, ORG_ID, ORG_NAME, USER_ID,
    VALID_ORG_ID, VALID_ORG_NAME, VALID_USER_ID, VALID_STATUS, VALID_TIME, END_TIME,
    STATUS, PURCHASE_STATUS, LOCKED_STATUS, INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS,
    PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME, IS_CUSTOMER_ARRIVAL, ARRIVAL_STATUS,
    ARRIVAL_TIME, SERVICE_STATUS, HAVE_ACCOUNT_PERIOD, IS_PAYMENT, TOTAL_AMOUNT, TRADER_ID,
    CUSTOMER_TYPE, CUSTOMER_NATURE, TRADER_NAME, TRADER_CONTACT_ID, TRADER_CONTACT_NAME,
    TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, TRADER_AREA_ID,
    TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID,
    TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE,
    TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS,
    IS_SEND_INVOICE, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID,
    INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, INVOICE_TRADER_CONTACT_TELEPHONE,
    INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA_ID, INVOICE_TRADER_AREA, INVOICE_TRADER_ADDRESS,
    SALES_AREA_ID, SALES_AREA, TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE,
    INVOICE_TYPE, FREIGHT_DESCRIPTION, DELIVERY_TYPE, LOGISTICS_ID, PAYMENT_TYPE, PREPAID_AMOUNT,
    ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, LOGISTICS_COLLECTION, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH,
    PAYMENT_COMMENTS, ADDITIONAL_CLAUSE, LOGISTICS_COMMENTS, FINANCE_COMMENTS, COMMENTS,
    INVOICE_COMMENTS, DELIVERY_DIRECT, SUPPLIER_CLAUSE, HAVE_ADVANCE_PURCHASE, ADVANCE_PURCHASE_STATUS,
    ADVANCE_PURCHASE_COMMENTS, ADVANCE_PURCHASE_TIME, IS_URGENT, URGENT_AMOUNT, HAVE_COMMUNICATE,
    PREPARE_COMMENTS, MARKETING_PLAN, STATUS_COMMENTS, SYNC_STATUS, LOGISTICS_API_SYNC,
    LOGISTICS_WXSEND_SYNC, SATISFY_INVOICE_TIME, SATISFY_DELIVERY_TIME, IS_SALES_PERFORMANCE,
    SALES_PERFORMANCE_TIME, SALES_PERFORMANCE_MOD_TIME, IS_DELAY_INVOICE, INVOICE_METHOD,
    LOCKED_REASON, COST_USER_IDS, OWNER_USER_ID, INVOICE_EMAIL, PAYMENT_MODE, PAY_TYPE,
    IS_APPLY_INVOICE, APPLY_INVOICE_TIME, ADD_TIME, CREATOR, MOD_TIME, UPDATER, ADK_SALEORDER_NO,
    CREATE_MOBILE, BDTRADER_COMMENTS, CLOSE_COMMENTS, BD_MOBILE_TIME, WEB_TAKE_DELIVERY_TIME,ACTION_ID,
    IS_PRINTOUT,SEND_TO_PC,IS_SAME_ADDRESS,INVOICE_SEND_NODE,IS_COUPONS,AUTO_AUDIT,IS_RISK,RISK_COMMENTS,RISK_TIME
	  </sql>
	<!---->
  <select id="getSaleOrderIdListByParam" resultType="java.lang.Integer">
	SELECT
		ts.SALEORDER_ID
	FROM
		T_SALEORDER ts
	LEFT JOIN T_ATTACHMENT ta ON ts.SALEORDER_ID = ta.RELATED_ID
	WHERE
		1 = 1
	AND ta.ATTACHMENT_FUNCTION = #{attachmentFunction, javaType=INTEGER}
	AND ts.COMPANY_ID = #{companyId, javaType=INTEGER}
	AND(ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-01-01')
	AND(ts.VALID_TIME / 1000) <![CDATA[ <= ]]> UNIX_TIMESTAMP('2018-12-31')
	GROUP BY
		ts.SALEORDER_ID
  </select>
  
  <select id="getOrderListInfoById" resultType="com.vedeng.order.model.Saleorder">
	SELECT
		a.SALEORDER_ID,
		u.USERNAME AS verifyUsername
	FROM
		T_SALEORDER a
	LEFT JOIN T_QUOTEORDER c ON a.QUOTEORDER_ID = c.QUOTEORDER_ID
	LEFT JOIN T_BUSSINESS_CHANCE d ON c.BUSSINESS_CHANCE_ID = d.BUSSINESS_CHANCE_ID
	LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER'
	AND e.VERIFIES_TYPE = 623
	LEFT JOIN T_VERIFIES_INFO con ON a.SALEORDER_ID = con.RELATE_TABLE_KEY
	AND con.RELATE_TABLE = 'T_SALEORDER'
	AND con.VERIFIES_TYPE = 868
	LEFT JOIN T_USER u ON a.USER_ID = u.USER_ID
	WHERE
		1 = 1
		AND a.SALEORDER_ID IN
	<foreach collection="list" item="saleorderId" open="(" close=")" separator=",">
		#{saleorderId, jdbcType=INTEGER}
	</foreach>
	AND a.COMPANY_ID = 1
	AND con.`STATUS` = 0
	AND con.VERIFY_USERNAME = ''
/*	AND con.`STATUS` IS NULL*/
	AND(
		a.HAVE_ACCOUNT_PERIOD = 1
		OR a.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000
	) 
	AND(a.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-01-01')
	AND(a.VALID_TIME / 1000) <![CDATA[ <= ]]> UNIX_TIMESTAMP('2018-12-31')
	AND a.ORDER_TYPE <![CDATA[ != ]]> 2
	AND a.VALID_STATUS = 1
	AND a.ADD_TIME <![CDATA[ > ]]> 0
	ORDER BY
		a.ADD_TIME DESC
  </select>
  <select id="getContractReturnOrderListPage" resultMap="ContractResultMap" parameterType="com.vedeng.order.model.SaleorderContract">
       SELECT
			ts.TRADER_NAME,
			ts.TRADER_ID,
			tu.USERNAME as OPT_USER_NAME,
			ts.SALEORDER_ID,
			ts.SALEORDER_NO,
			cbt.AMOUNT AS PAYMENT_AMOUNT,
			FROM_UNIXTIME(
				cbt.TRADE_TIME / 1000,
				'%Y-%m-%d %H:%i:%s'
			) AS FIRST_PAYMENT_TIME,
			CASE
				WHEN ts.HAVE_ACCOUNT_PERIOD = 1 THEN
					'电子+纸质'
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 10000 THEN
					NULL
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000 THEN
					'电子'
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 THEN
					'电子+纸质'
				END AS CONTRACT_TYPE,
			DATEDIFF(SYSDATE(),FROM_UNIXTIME(
				cbt.TRADE_TIME / 1000,
				'%Y-%m-%d %H:%i:%s'
			)) AS NOW_DAY_BETWEEN,
				CASE
					WHEN DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					) > 30 THEN
						DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					) 
					ELSE 30 - 	DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					) 
				 END AS ORDER_COL
		FROM
			T_SALEORDER ts
		LEFT JOIN T_ATTACHMENT ta ON ts.SALEORDER_ID = ta.RELATED_ID
		AND ta.ATTACHMENT_FUNCTION = 492
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		LEFT JOIN T_USER tu ON tu.USER_ID = trj.USER_ID
		INNER JOIN (
			SELECT
				COALESCE (
					sum(
						IF (
							a.TRADER_TYPE = 1
							OR a.TRADER_TYPE = 4,
							ABS(a.AMOUNT),
		
						IF (
							a.TRADER_TYPE = 2
							OR a.TRADER_TYPE = 5 ,- ABS(a.AMOUNT),
							0
						)
						)
					),
					0
				) AS AMOUNT,
				b.RELATED_ID AS SALEORDER_ID,
				MIN(a.TRADER_TIME) AS TRADE_TIME
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN (
				SELECT
					COALESCE (sum(ABS(a2.AMOUNT)), 0) AS periodAmount,
					COALESCE (
						sum(ABS(a2.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.hk_amount, 0),
						0
					) AS zq_amount,
					b2.RELATED_ID AS zq_RELATED_ID
				FROM
					T_CAPITAL_BILL a2
				LEFT JOIN T_CAPITAL_BILL_DETAIL b2 ON a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						b1.RELATED_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					WHERE
						a1.TRADER_TYPE IN (1, 4)
					AND b1.ORDER_TYPE = 1
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						b1.RELATED_ID
				) AS c ON b2.RELATED_ID = c.RELATED_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						c1.ORDER_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
					AND c1.SUBJECT_TYPE = 535
					WHERE
						a1.TRADER_TYPE = 3
					AND b1.ORDER_TYPE = 3
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						c1.ORDER_ID
				) AS d ON d.ORDER_ID = b2.RELATED_ID
				WHERE
					a2.TRADER_TYPE = 3
				AND b2.ORDER_TYPE = 1
				AND a2.TRADER_MODE = 527
				GROUP BY
					b2.RELATED_ID
			) AS zq ON zq.zq_RELATED_ID = b.RELATED_ID
			WHERE
				b.ORDER_TYPE = 1
			AND b.BUSSINESS_TYPE != 533
			GROUP BY
				b.RELATED_ID
		) cbt ON cbt.SALEORDER_ID = ts.SALEORDER_ID
		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
			trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0,1, 4)
	  	AND ts.STATUS <![CDATA[ <> ]]> 3
		AND ts.COMPANY_ID = #{saleOrderContract.companyId, jdbcType=INTEGER}
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` is null
		 <if test="saleOrderContract.customerName != null">
            and ts.TRADER_NAME LIKE  CONCAT('%',#{saleOrderContract.customerName,jdbcType=VARCHAR},'%' ) 
        </if>
         <if test="saleOrderContract.saleorderNo != null">
         	and ts.SALEORDER_NO LIKE  CONCAT('%',#{saleOrderContract.saleorderNo,jdbcType=VARCHAR},'%' ) 
        </if> 
        <if test="saleOrderContract.searchBegintime != null">
            and cbt.TRADE_TIME <![CDATA[ >= ]]> #{saleOrderContract.searchBegintime}
        </if> 
        <if test="saleOrderContract.searchEndtime != null">
            and cbt.TRADE_TIME <![CDATA[ <= ]]> #{saleOrderContract.searchEndtime}
        </if> 
         <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子'">
            and ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000 
        </if> 
          <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子+纸质'">
            and (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
        </if> 
        GROUP BY ts.SALEORDER_ID
		ORDER BY ORDER_COL DESC
  </select>
  
  <select id="getContractReturnOrderListCount" resultType="java.lang.Integer" parameterType="Map">
      SELECT COUNT(tt.NUMS) FROM (
		SELECT
			COUNT(1) AS NUMS
		FROM
			T_SALEORDER ts
		LEFT JOIN T_ATTACHMENT ta ON ts.SALEORDER_ID = ta.RELATED_ID
		AND ta.ATTACHMENT_FUNCTION = 492
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		INNER JOIN (
			SELECT
				COALESCE (
					sum(
						IF (
							a.TRADER_TYPE = 1
							OR a.TRADER_TYPE = 4,
							ABS(a.AMOUNT),
		
						IF (
							a.TRADER_TYPE = 2
							OR a.TRADER_TYPE = 5 ,- ABS(a.AMOUNT),
							0
						)
						)
					),
					0
				) AS AMOUNT,
				b.RELATED_ID AS SALEORDER_ID,
				MIN(a.TRADER_TIME) AS TRADE_TIME
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN (
				SELECT
					COALESCE (sum(ABS(a2.AMOUNT)), 0) AS periodAmount,
					COALESCE (
						sum(ABS(a2.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.hk_amount, 0),
						0
					) AS zq_amount,
					b2.RELATED_ID AS zq_RELATED_ID
				FROM
					T_CAPITAL_BILL a2
				LEFT JOIN T_CAPITAL_BILL_DETAIL b2 ON a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						b1.RELATED_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					WHERE
						a1.TRADER_TYPE IN (1, 4)
					AND b1.ORDER_TYPE = 1
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						b1.RELATED_ID
				) AS c ON b2.RELATED_ID = c.RELATED_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						c1.ORDER_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
					AND c1.SUBJECT_TYPE = 535
					WHERE
						a1.TRADER_TYPE = 3
					AND b1.ORDER_TYPE = 3
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						c1.ORDER_ID
				) AS d ON d.ORDER_ID = b2.RELATED_ID
				WHERE
					a2.TRADER_TYPE = 3
				AND b2.ORDER_TYPE = 1
				AND a2.TRADER_MODE = 527
				GROUP BY
					b2.RELATED_ID
			) AS zq ON zq.zq_RELATED_ID = b.RELATED_ID
			WHERE
				b.ORDER_TYPE = 1
			AND b.BUSSINESS_TYPE != 533
			GROUP BY
				b.RELATED_ID
		) cbt ON cbt.SALEORDER_ID = ts.SALEORDER_ID
		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
			trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0, 1,4)
	  	AND ts.STATUS <![CDATA[ <> ]]> 3
		AND ts.COMPANY_ID = #{saleOrderContract.companyId, jdbcType=INTEGER}
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` is null
		 <if test="saleOrderContract.customerName != null">
            and ts.TRADER_NAME LIKE  CONCAT('%',#{saleOrderContract.customerName,jdbcType=VARCHAR},'%' ) 
        </if>
        <if test="saleOrderContract.saleorderNo != null">
         	and ts.SALEORDER_NO LIKE  CONCAT('%',#{saleOrderContract.saleorderNo,jdbcType=VARCHAR},'%' ) 
        </if> 
        <if test="saleOrderContract.searchBegintime != null">
            and cbt.TRADE_TIME <![CDATA[ >= ]]> #{saleOrderContract.searchBegintime}
        </if> 
        <if test="saleOrderContract.searchEndtime != null">
            and cbt.TRADE_TIME <![CDATA[ <= ]]> #{saleOrderContract.searchEndtime}
        </if> 
         <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子'">
            and ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000 
        </if> 
          <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子+纸质'">
            and (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
        </if> 
        GROUP BY ts.SALEORDER_ID)  tt
  </select>
    <select id="getContractReturnOrderNoqualityListPage" resultMap="ContractResultMap" parameterType="com.vedeng.order.model.SaleorderContract">
       SELECT
			ts.TRADER_NAME,
			ts.TRADER_ID,
			tu.USERNAME as OPT_USER_NAME,
			ts.SALEORDER_ID,
			ts.SALEORDER_NO,
			cbt.AMOUNT AS PAYMENT_AMOUNT,
			FROM_UNIXTIME(
				cbt.TRADE_TIME / 1000,
				'%Y-%m-%d %H:%i:%s'
			) AS FIRST_PAYMENT_TIME,
			CASE
				WHEN ts.HAVE_ACCOUNT_PERIOD = 1 THEN
					'电子+纸质'
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 10000 THEN
					NULL
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000 THEN
					'电子'
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 THEN
					'电子+纸质'
				END AS CONTRACT_TYPE,
			DATEDIFF(SYSDATE(),FROM_UNIXTIME(
				cbt.TRADE_TIME / 1000,
				'%Y-%m-%d %H:%i:%s'
			)) AS NOW_DAY_BETWEEN,
				CASE
					WHEN DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					) > 30 THEN
						DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					) 
					ELSE 30 - 	DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					) 
				 END AS ORDER_COL
		FROM
			T_SALEORDER ts
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		LEFT JOIN T_USER tu ON tu.USER_ID = trj.USER_ID
		INNER JOIN (
			SELECT
				COALESCE (
					sum(
						IF (
							a.TRADER_TYPE = 1
							OR a.TRADER_TYPE = 4,
							ABS(a.AMOUNT),
		
						IF (
							a.TRADER_TYPE = 2
							OR a.TRADER_TYPE = 5 ,- ABS(a.AMOUNT),
							0
						)
						)
					),
					0
				) AS AMOUNT,
				b.RELATED_ID AS SALEORDER_ID,
				MIN(a.TRADER_TIME) AS TRADE_TIME
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN (
				SELECT
					COALESCE (sum(ABS(a2.AMOUNT)), 0) AS periodAmount,
					COALESCE (
						sum(ABS(a2.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.hk_amount, 0),
						0
					) AS zq_amount,
					b2.RELATED_ID AS zq_RELATED_ID
				FROM
					T_CAPITAL_BILL a2
				LEFT JOIN T_CAPITAL_BILL_DETAIL b2 ON a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						b1.RELATED_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					WHERE
						a1.TRADER_TYPE IN (1, 4)
					AND b1.ORDER_TYPE = 1
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						b1.RELATED_ID
				) AS c ON b2.RELATED_ID = c.RELATED_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						c1.ORDER_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
					AND c1.SUBJECT_TYPE = 535
					WHERE
						a1.TRADER_TYPE = 3
					AND b1.ORDER_TYPE = 3
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						c1.ORDER_ID
				) AS d ON d.ORDER_ID = b2.RELATED_ID
				WHERE
					a2.TRADER_TYPE = 3
				AND b2.ORDER_TYPE = 1
				AND a2.TRADER_MODE = 527
				GROUP BY
					b2.RELATED_ID
			) AS zq ON zq.zq_RELATED_ID = b.RELATED_ID
			WHERE
				b.ORDER_TYPE = 1
			AND b.BUSSINESS_TYPE != 533
			GROUP BY
				b.RELATED_ID
		) cbt ON cbt.SALEORDER_ID = ts.SALEORDER_ID
		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
			trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0, 1,4)
		AND ts.COMPANY_ID = #{saleOrderContract.companyId, jdbcType=INTEGER}
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` = 2
		 <if test="saleOrderContract.customerName != null">
            and ts.TRADER_NAME LIKE  CONCAT('%',#{saleOrderContract.customerName,jdbcType=VARCHAR},'%' ) 
        </if>
         <if test="saleOrderContract.saleorderNo != null">
         	and ts.SALEORDER_NO LIKE  CONCAT('%',#{saleOrderContract.saleorderNo,jdbcType=VARCHAR},'%' ) 
        </if> 
        <if test="saleOrderContract.searchBegintime != null">
            and cbt.TRADE_TIME <![CDATA[ >= ]]> #{saleOrderContract.searchBegintime}
        </if> 
        <if test="saleOrderContract.searchEndtime != null">
            and cbt.TRADE_TIME <![CDATA[ <= ]]> #{saleOrderContract.searchEndtime}
        </if> 
         <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子'">
            and ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000 
        </if> 
          <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子+纸质'">
            and (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
        </if> 
        GROUP BY ts.SALEORDER_ID
		ORDER BY ORDER_COL DESC
  </select>
  
  <select id="getContractReturnOrderNoqualityListCount" resultType="java.lang.Integer" parameterType="Map">
      SELECT COUNT(tt.NUMS) FROM (
		SELECT
			COUNT(1) AS NUMS
		FROM
			T_SALEORDER ts
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		INNER JOIN (
			SELECT
				COALESCE (
					sum(
						IF (
							a.TRADER_TYPE = 1
							OR a.TRADER_TYPE = 4,
							ABS(a.AMOUNT),
		
						IF (
							a.TRADER_TYPE = 2
							OR a.TRADER_TYPE = 5 ,- ABS(a.AMOUNT),
							0
						)
						)
					),
					0
				) AS AMOUNT,
				b.RELATED_ID AS SALEORDER_ID,
				MIN(a.TRADER_TIME) AS TRADE_TIME
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN (
				SELECT
					COALESCE (sum(ABS(a2.AMOUNT)), 0) AS periodAmount,
					COALESCE (
						sum(ABS(a2.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.hk_amount, 0),
						0
					) AS zq_amount,
					b2.RELATED_ID AS zq_RELATED_ID
				FROM
					T_CAPITAL_BILL a2
				LEFT JOIN T_CAPITAL_BILL_DETAIL b2 ON a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						b1.RELATED_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					WHERE
						a1.TRADER_TYPE IN (1, 4)
					AND b1.ORDER_TYPE = 1
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						b1.RELATED_ID
				) AS c ON b2.RELATED_ID = c.RELATED_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						c1.ORDER_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
					AND c1.SUBJECT_TYPE = 535
					WHERE
						a1.TRADER_TYPE = 3
					AND b1.ORDER_TYPE = 3
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						c1.ORDER_ID
				) AS d ON d.ORDER_ID = b2.RELATED_ID
				WHERE
					a2.TRADER_TYPE = 3
				AND b2.ORDER_TYPE = 1
				AND a2.TRADER_MODE = 527
				GROUP BY
					b2.RELATED_ID
			) AS zq ON zq.zq_RELATED_ID = b.RELATED_ID
			WHERE
				b.ORDER_TYPE = 1
			AND b.BUSSINESS_TYPE != 533
			GROUP BY
				b.RELATED_ID
		) cbt ON cbt.SALEORDER_ID = ts.SALEORDER_ID
		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
			trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0,1, 4)
		AND ts.COMPANY_ID = #{saleOrderContract.companyId, jdbcType=INTEGER}
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` = 2
		 <if test="saleOrderContract.customerName != null">
            and ts.TRADER_NAME LIKE  CONCAT('%',#{saleOrderContract.customerName,jdbcType=VARCHAR},'%' ) 
        </if>
        <if test="saleOrderContract.saleorderNo != null">
         	and ts.SALEORDER_NO LIKE  CONCAT('%',#{saleOrderContract.saleorderNo,jdbcType=VARCHAR},'%' ) 
        </if> 
        <if test="saleOrderContract.searchBegintime != null">
            and cbt.TRADE_TIME <![CDATA[ >= ]]> #{saleOrderContract.searchBegintime}
        </if> 
        <if test="saleOrderContract.searchEndtime != null">
            and cbt.TRADE_TIME <![CDATA[ <= ]]> #{saleOrderContract.searchEndtime}
        </if> 
         <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子'">
            and ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000 
        </if> 
          <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子+纸质'">
            and (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
        </if> 
        GROUP BY ts.SALEORDER_ID)  tt
  </select>

	<!-- 修改订单税率保存 -->
	<update id="saveOrderRatioEdit" parameterType="java.lang.Integer">
        UPDATE T_SALEORDER A SET A.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER} WHERE A.SALEORDER_ID = #{orderId,jdbcType=INTEGER}
    </update>
	<select id="getSaleOrderlistByStatusTraderId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
	SELECT
	A.*
	FROM
	`T_SALEORDER` A
	LEFT JOIN T_VERIFIES_INFO B
	ON B.`RELATE_TABLE` = 'T_SALEORDER'
	AND B.`RELATE_TABLE_KEY` = A.`SALEORDER_ID`
	AND B.`STATUS`= 2
	WHERE A.`TRADER_ID` = #{traderId}
	AND A.STATUS = 0
	AND A.`ORDER_TYPE`=1
	</select>
	<select id="getSaleOrderlistByStatusMobile"  resultMap="BaseResultMap">
	SELECT
	A.*
	FROM
	`T_SALEORDER` A
	LEFT JOIN T_VERIFIES_INFO B
	ON B.`RELATE_TABLE` = 'T_SALEORDER'
	AND B.`RELATE_TABLE_KEY` = A.`SALEORDER_ID`
	AND B.`STATUS`= 2
	WHERE A.`CREATE_MOBILE` = #{createMobile}
	AND A.STATUS = 0
	AND A.`ORDER_TYPE`=1
	</select>
	<!--耗材商城推送消息-->
	<select id="getHcOrderList" resultMap="ContractResultMap" parameterType="com.vedeng.order.model.SaleorderContract">
		SELECT
			SALEORDER_ID,
			SALEORDER_NO
		FROM
			T_SALEORDER
		WHERE
			1=1
		and VALID_STATUS = 0
		and ORDER_TYPE = 5
		<if test="traderId != null">
			and TRADER_ID = #{traderId, jdbcType=INTEGER}
		</if>
		<if test="saleorderNo != null and saleorderNo !=''">
			and SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
		</if>
	</select>

	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.Saleorder" >
		update T_SALEORDER
		<set >
			<if test="invoiceSendNode != null" >
				INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=INTEGER},
			</if>
			<if test="quoteorderId != null" >
				QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null" >
				PARENT_ID = #{parentId,jdbcType=INTEGER},
			</if>
			<if test="saleorderNo != null" >
				SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
			</if>
			<if test="orderType != null" >
				ORDER_TYPE = #{orderType,jdbcType=BIT},
			</if>
			<if test="companyId != null" >
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="orgId != null" >
				ORG_ID = #{orgId,jdbcType=INTEGER},
			</if>
			<if test="orgName !=null">
				ORG_NAME = #{orgName,jdbcType=VARCHAR},
			</if>
			<if test="userId != null" >
				USER_ID = #{userId,jdbcType=INTEGER},
			</if>
			<if test="validStatus != null" >
				VALID_STATUS = #{validStatus,jdbcType=BIT},
			</if>
			<if test="validTime != null" >
				VALID_TIME = #{validTime,jdbcType=BIGINT},
			</if>
			<if test="status != null" >
				STATUS = #{status,jdbcType=BIT},
			</if>
			<if test="purchaseStatus != null" >
				PURCHASE_STATUS = #{purchaseStatus,jdbcType=BIT},
			</if>
			<if test="lockedStatus != null" >
				LOCKED_STATUS = #{lockedStatus,jdbcType=BIT},
			</if>
			<if test="invoiceStatus != null" >
				INVOICE_STATUS = #{invoiceStatus,jdbcType=BIT},
			</if>
			<if test="invoiceTime != null" >
				INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
			</if>
			<if test="paymentStatus != null" >
				PAYMENT_STATUS = #{paymentStatus,jdbcType=BIT},
			</if>
			<if test="isPayment != null" >
				IS_PAYMENT = #{isPayment,jdbcType=BIT},
			</if>
			<if test="paymentTime != null" >
				PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryStatus != null" >
				DELIVERY_STATUS = #{deliveryStatus,jdbcType=BIT},
			</if>
			<if test="deliveryTime != null" >
				DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="isCustomerArrival != null" >
				IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=BIT},
			</if>
			<if test="arrivalStatus != null" >
				ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BIT},
			</if>
			<if test="arrivalTime != null" >
				ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="serviceStatus != null" >
				SERVICE_STATUS = #{serviceStatus,jdbcType=BIT},
			</if>
			<if test="haveAccountPeriod != null" >
				HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=BIT},
			</if>
			<if test="totalAmount != null" >
				TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
			</if>
			<if test="traderId != null" >
				TRADER_ID = #{traderId,jdbcType=INTEGER},
			</if>
			<if test="customerType != null" >
				CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
			</if>
			<if test="customerNature != null" >
				CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
			</if>
			<if test="traderName != null" >
				TRADER_NAME = #{traderName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactId != null" >
				TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
			</if>
			<if test="traderContactName != null" >
				TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactMobile != null" >
				TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="traderContactTelephone != null" >
				TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="traderAddressId != null" >
				TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
			</if>
			<if test="traderAddress != null" >
				TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
			</if>
			<if test="traderComments != null" >
				TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderId != null" >
				TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderName != null" >
				TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactId != null" >
				TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderContactName != null" >
				TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactMobile != null" >
				TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactTelephone != null" >
				TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddressId != null" >
				TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderAddress != null" >
				TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="isSendInvoice != null" >
				IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BIT},
			</if>
			<if test="invoiceTraderId != null" >
				INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderName != null" >
				INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactId != null" >
				INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderContactName != null" >
				INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactMobile != null" >
				INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactTelephone != null" >
				INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderAddressId != null" >
				INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderAddress != null" >
				INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="salesAreaId != null" >
				SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
			</if>
			<if test="salesArea != null" >
				SALES_AREA = #{salesArea,jdbcType=VARCHAR},
			</if>
			<if test="terminalTraderId != null" >
				TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
			</if>
			<if test="terminalTraderName != null" >
				TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
			</if>
			<if test="terminalTraderType != null" >
				TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
			</if>
			<if test="invoiceType != null" >
				INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
			</if>
			<if test="invoiceMethod != null" >
				INVOICE_METHOD = #{invoiceMethod,jdbcType=INTEGER},
			</if>
			<if test="freightDescription != null" >
				FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
			</if>
			<if test="deliveryType != null" >
				DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
			</if>
			<if test="logisticsId != null" >
				LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="paymentType != null" >
				PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
			</if>
			<if test="prepaidAmount != null" >
				PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
			</if>
			<if test="accountPeriodAmount != null" >
				ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
			</if>
			<if test="logisticsCollection != null" >
				LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BIT},
			</if>
			<if test="retainageAmount != null" >
				RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
			</if>
			<if test="retainageAmountMonth != null" >
				RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
			</if>
			<if test="paymentComments != null" >
				PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
			</if>
			<if test="additionalClause != null" >
				ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
			</if>
			<if test="logisticsComments != null" >
				LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="financeComments != null" >
				FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
			</if>
			<if test="comments != null" >
				COMMENTS = #{comments,jdbcType=VARCHAR},
			</if>
			<if test="invoiceComments != null" >
				INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
			</if>
			<if test="deliveryDirect != null" >
				DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
			</if>
			<if test="supplierClause != null" >
				SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
			</if>
			<if test="haveAdvancePurchase != null" >
				HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=BIT},
			</if>
			<if test="isUrgent != null" >
				IS_URGENT = #{isUrgent,jdbcType=BIT},
			</if>
			<if test="urgentAmount != null" >
				URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
			</if>
			<if test="haveCommunicate != null" >
				HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BIT},
			</if>
			<if test="prepareComments != null" >
				PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
			</if>
			<if test="marketingPlan != null" >
				MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
			</if>
			<if test="syncStatus != null" >
				SYNC_STATUS = #{syncStatus,jdbcType=BIT},
			</if>
			<if test="satisfyInvoiceTime != null" >
				SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
			</if>
			<if test="satisfyDeliveryTime != null" >
				SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
			</if>
			<if test="addTime != null" >
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null" >
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null" >
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="traderArea != null" >
				TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderArea != null" >
				TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderArea != null" >
				INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="advancePurchaseStatus != null" >
				ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=INTEGER},
			</if>
			<if test="advancePurchaseComments != null" >
				ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
			</if>
			<if test="advancePurchaseTime != null" >
				ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
			</if>
			<if test="validUserId != null" >
				VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
			</if>
			<if test="validOrgId != null" >
				VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
			</if>
			<if test="validOrgName != null" >
				VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
			</if>
			<if test="isSalesPerformance != null" >
				IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=INTEGER},
			</if>
			<if test="salesPerformanceTime != null" >
				SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
			</if>
			<if test="salesPerformanceModTime != null" >
				SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
			</if>
			<if test="isDelayInvoice != null" >
				IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=BIT},
			</if>
			<if test="lockedReason != null" >
				LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
			</if>
			<if test="costUserIds != null" >
				COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
			</if>
			<if test="invoiceEmail != null">
				INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
			</if>
			<if test="ownerUserId != null">
				OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
			</if>
			<if test="traderAreaId != null">
				TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderAreaId != null">
				TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderAreaId != null">
				INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId, jdbcType=INTEGER},
			</if>
			<if test="logisticsApiSync != null" >
				LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=BIT},
			</if>
			<if test="logisticsWxsendSync != null" >
				LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=BIT},
			</if>
			<if test="closeComments != null">
				CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
			</if>
			<if test="bdMobileTime != null">
				BD_MOBILE_TIME = #{bdMobileTime,jdbcType=VARCHAR},
			</if>
			<if test="contractUrl != null">
				CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
			</if>
			<if test="autoAudit != null">
				AUTO_AUDIT = #{autoAudit, jdbcType=INTEGER},
			</if>
			<if test="isRisk != null">
				IS_RISK = #{isRisk,jdbcType=INTEGER},
			</if>
			<if test="riskComments != null">
				RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
			</if>
			<if test="riskTime != null">
				RISK_TIME = #{riskTime,jdbcType=BIGINT},
			</if>
		</set>
		where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>
	 <select id="getSaleOrderById" resultMap="BaseResultMap">
	SELECT <include refid="Base_Column_List"/> FROM `T_SALEORDER`  WHERE `SALEORDER_ID`=#{saleorderId}
	</select>
	 <select id="getSaleorderBySaleorderNo" resultMap="BaseResultMap" parameterType="com.vedeng.order.model.Saleorder">
  	select a.*,b.COMMENTS AS taxRate
    from T_SALEORDER a
    left join T_SYS_OPTION_DEFINITION b on a.INVOICE_TYPE = b.SYS_OPTION_DEFINITION_ID
    where a.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
  </select>
  <select id="getSaleorderListByStatus" resultMap="BaseResultMap">
  	SELECT <include refid="Base_Column_List"/> FROM T_SALEORDER  WHERE `STATUS`=#{status,jdbcType=INTEGER} AND `ORDER_TYPE`=#{orderType,jdbcType=INTEGER}
  </select>
	<select id="getSaleorderidByStatusLimit" resultType="java.lang.Integer">
		SELECT
		a.SALEORDER_ID
		FROM
		T_SALEORDER a
		WHERE
		a.`STATUS` != 3
		AND a.ORDER_TYPE != 2
		AND a.UPDATE_DATA_TIME > DATE_SUB(
		CURDATE(),
		INTERVAL #{day} DAY)
		ORDER BY
		a.SALEORDER_ID DESC
		LIMIT #{limit},1000
	</select>
    <select id="getActionOrderLimit" resultType="java.lang.Integer">
		SELECT
		a.SALEORDER_ID
		FROM
		T_SALEORDER a
		WHERE
		a.ACTION_ID > 0
		AND a.`STATUS` != 3
		AND a.ORDER_TYPE != 2
		AND a.UPDATE_DATA_TIME > DATE_SUB(
		CURDATE(),
		INTERVAL  #{day} DAY)
		ORDER BY
		a.SALEORDER_ID DESC
		LIMIT #{limit},1000
    </select>
	<select id="getSaleorderidByStatus" resultType="java.lang.Integer" >
	SELECT
	count(*)
	FROM
	T_SALEORDER a
	WHERE
	a.`STATUS` != 3
	AND a.ORDER_TYPE !=2
	AND a.UPDATE_DATA_TIME > DATE_SUB(
		CURDATE(),
	INTERVAL #{day} DAY)
	</select>
	<select id="getSaleorderidAll" resultType="java.lang.Integer" >
	select count(*) from T_SALEORDER a where a.ORDER_TYPE !=2
	</select>
	<select id="getSaleorderidAllLimit" resultType="java.lang.Integer">
	select a.SALEORDER_ID from T_SALEORDER a where a.ORDER_TYPE !=2   ORDER BY a.SALEORDER_ID desc limit  #{orgId},1000
	</select>
	<select id="selectLatestSaleOrderByMobile" resultMap="BaseResultMap">
	select a.TRADER_ID,a.saleorder_id from T_SALEORDER a where a.ORDER_TYPE =0 and TRADER_CONTACT_MOBILE= #{mobile}
	 and valid_time &gt; 1592236800000
	 ORDER BY a.VALID_TIME desc
	limit 30
	</select>

	<select id="selectSaleorderNo" resultMap="BaseResultMap">
		SELECT SALEORDER_NO,SALEORDER_ID FROM T_SALEORDER
		WHERE 1=1
		and `STATUS` IN (0,1,4)
		and ORDER_TYPE=1
		<if test="saleorderId!=null">
			AND SALEORDER_ID=#{saleorderId}
		</if>
		<if test="createMobile!=null and createMobile!=''">
			AND  CREATE_MOBILE=#{createMobile}
		</if>
		<if test="createMobileList!=null">
			AND CREATE_MOBILE IN <foreach collection="createMobileList" item="createMo" index="index" open="(" separator="," close=")">
			#{createMo}
		</foreach>
		</if>
	</select>

	<!-- add by Tomcat.hui transplanted from project:dbcenter 20190816 -->
	<select id="getGoodsOccupyNum" resultType="java.lang.Integer">
		select
		COALESCE(SUM(if(b.NUM-b.DELIVERY_NUM-IFNULL(c.thNum,0)>0,b.NUM-b.DELIVERY_NUM-IFNULL(c.thNum,0),0) ),0)
		from
		T_SALEORDER a
		left join
		T_SALEORDER_GOODS b
		on
		a.SALEORDER_ID = b.SALEORDER_ID
		left join
		(
		select
		COALESCE(SUM(aa.NUM),0) as thNum,
		aa.ORDER_DETAIL_ID
		from
		T_AFTER_SALES_GOODS aa
		left join
		T_AFTER_SALES bb
		on
		aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		where
		bb.ATFER_SALES_STATUS in (1,2)
		AND
		bb.SUBJECT_TYPE = 535
		AND
		bb.TYPE = 539
		AND aa.GOODS_ID = #{goodsId,jdbcType=INTEGER}
		GROUP BY
		aa.ORDER_DETAIL_ID
		) as c on b.SALEORDER_GOODS_ID = c.ORDER_DETAIL_ID
		where
		a.STATUS = 1
		and
		a.DELIVERY_STATUS != 2
		and
		b.DELIVERY_DIRECT = 0
		and
		b.IS_DELETE = 0
		and
		a.ORDER_TYPE in (0,3)
		and
		b.GOODS_ID = #{goodsId,jdbcType=INTEGER}
		and
		a.SATISFY_DELIVERY_TIME>0
	</select>

	<!-- add by Tomcat.hui transplanted from project:dbcenter 20190816 -->
	<select id="getGoodsOccupyNumList" resultMap="goodsDataMap">
		select
		COALESCE(SUM(if(b.NUM-b.DELIVERY_NUM-IFNULL(c.thNum,0)>0,b.NUM-b.DELIVERY_NUM-IFNULL(c.thNum,0),0) ),0) as
		occupyNum,b.GOODS_ID
		from
		T_SALEORDER a
		left join
		T_SALEORDER_GOODS b
		on
		a.SALEORDER_ID = b.SALEORDER_ID
		left join
		(
		select
		COALESCE(SUM(aa.NUM),0) as thNum,
		aa.ORDER_DETAIL_ID
		from
		T_AFTER_SALES_GOODS aa
		left join
		T_AFTER_SALES bb
		on
		aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		where
		bb.ATFER_SALES_STATUS in (1,2)
		AND
		bb.SUBJECT_TYPE = 535
		AND
		bb.TYPE = 539
		<if test="goodsIds != null">
			and aa.GOODS_ID in
			<foreach collection="goodsIds" item="goodsId" index="index"
					 open="(" close=")" separator=",">
				#{goodsId}
			</foreach>
		</if>
		GROUP BY
		aa.ORDER_DETAIL_ID
		) as c on b.SALEORDER_GOODS_ID = c.ORDER_DETAIL_ID
		where
		a.STATUS = 1
		and
		a.DELIVERY_STATUS != 2
		and
		b.DELIVERY_DIRECT = 0
		and
		b.IS_DELETE = 0
		and
		a.ORDER_TYPE in (0,3)
		<if test="goodsIds != null">
			and b.GOODS_ID in
			<foreach collection="goodsIds" item="goodsId" index="index"
					 open="(" close=")" separator=",">
				#{goodsId}
			</foreach>
		</if>
		and
		a.SATISFY_DELIVERY_TIME>0
		group by b.GOODS_ID
	</select>

	<select id="isExistQuoteorderId" resultType="int">
     SELECT COUNT(1) FROM T_SALEORDER
      WHERE QUOTEORDER_ID=#{quoteorderId}

	</select>


	<!-- add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 取LackAccountPeriodAmount. start -->
	<select id="getSaleorderLackAccountPeriodAmount" parameterType="java.lang.Integer"
			resultType="java.math.BigDecimal">
  	select
  		COALESCE(sum(ABS(a.AMOUNT))-IFNULL(c.hk_amount,0)-ifnull(d.hk_amount,0),0)
  	from
  		T_CAPITAL_BILL a
  	left join
		T_CAPITAL_BILL_DETAIL b
	on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
  	left join
  		(
  			select
  				COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,b1.RELATED_ID
  			from
		  		T_CAPITAL_BILL a1
		  	left join
				T_CAPITAL_BILL_DETAIL b1
			on
				a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
			where
				a1.TRADER_TYPE in (1,4)
			and
  				b1.ORDER_TYPE = 1
			and
				b1.BUSSINESS_TYPE = 533
			group by b1.RELATED_ID
  		) as c
  	on
  		b.RELATED_ID = c.RELATED_ID
  	left join
  		(
  			select
				COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,c1.ORDER_ID
			from
				T_CAPITAL_BILL a1
			left join
				T_CAPITAL_BILL_DETAIL b1
			on
				a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
			left JOIN
				T_AFTER_SALES c1
			ON
				c1.AFTER_SALES_ID = b1.RELATED_ID and c1.SUBJECT_TYPE = 535
			where
				a1.TRADER_TYPE = 3
			and
				b1.ORDER_TYPE = 3
			and
				b1.BUSSINESS_TYPE = 533
			group by c1.ORDER_ID
  		) as d
  	on
  		d.ORDER_ID = b.RELATED_ID
  	where
  		a.TRADER_TYPE = 3
  	and
  		a.TRADER_MODE = 527
  	and
  		b.ORDER_TYPE = 1
  	and
  		b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
  </select>
	<!-- add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 取LackAccountPeriodAmount. end -->

	<!-- add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 取Amount. start -->
	<select id="getPeriodAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	select
  		COALESCE(sum(ABS(a.AMOUNT))-IFNULL(abs(c.tk_amount),0),0)
  	from
  		T_CAPITAL_BILL a
  	left join
		T_CAPITAL_BILL_DETAIL b
	on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
	left join
		(
			SELECT
				sum(aa.AMOUNT) as tk_amount,cc.ORDER_ID
			FROM
				T_CAPITAL_BILL aa
			left JOIN
				T_CAPITAL_BILL_DETAIL bb
			ON
				aa.CAPITAL_BILL_ID = bb.CAPITAL_BILL_ID
			left JOIN
				T_AFTER_SALES cc
			ON
				cc.AFTER_SALES_ID = bb.RELATED_ID
			AND
				bb.ORDER_TYPE = 3
			WHERE
				aa.TRADER_MODE = 529
			AND
				aa.TRADER_TYPE = 3
			AND
				cc.SUBJECT_TYPE = 535
			GROUP BY
				cc.ORDER_ID
		) as c
		ON
		c.ORDER_ID = b.RELATED_ID
  	where
  		a.TRADER_TYPE = 3
  	and
  		a.TRADER_MODE = 527
  	and
  		b.ORDER_TYPE = 1
  	and
  		b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
  </select>
	<!--根据订单id查询订单号和用户id-->
	<select id="getWebAccountId" resultMap="BaseResultMap">
           SELECT
        ORDER_TYPE,
        SALEORDER_NO,
        SALEORDER_ID,
				CASE
				WHEN ORDER_TYPE=0
				THEN K.WEB_ACCOUNT_ID
				ELSE
        w.WEB_ACCOUNT_ID
				END AS WEB_ACCOUNT_ID,
				CASE
				WHEN ORDER_TYPE=0
				THEN K.SSO_ACCOUNT_ID
				ELSE

        w.SSO_ACCOUNT_ID
				END AS SSO_ACCOUNT_ID,t.SEND_TO_PC
    FROM
        T_SALEORDER t
    LEFT  JOIN
        T_WEB_ACCOUNT w
            ON t.CREATE_MOBILE=w.MOBILE
            <!--如果是VS订单，则需要根据联系人手机号来匹配-->
	 left join T_WEB_ACCOUNT K ON t.TRADER_CONTACT_MOBILE=K.MOBILE
    WHERE
         t.SALEORDER_ID=#{SaleOrderId}
	</select>

	<!-- add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 取PeriodAmount. end -->
	<select id="getSaleorderBySaleorderNoList" resultType="com.vedeng.order.model.Saleorder" >
		SELECT  a.* ,e.`STATUS` AS VERIFY_STATUS ,u.USERNAME userName,DATE_FORMAT(FROM_UNIXTIME(a.`ADD_TIME`/1000, '%Y-%m-%d %H:%i:%S'),"%Y-%m-%d %H:%i:%S")  addTimeStr
		FROM T_SALEORDER a
		LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY
		AND e.RELATE_TABLE = 'T_SALEORDER' AND e.VERIFIES_TYPE = 623
		LEFT JOIN T_R_TRADER_J_USER f ON a.TRADER_ID = f.TRADER_ID
		LEFT JOIN T_USER u
		ON f.USER_ID=u.USER_ID
		WHERE a.SALEORDER_NO IN
		<foreach collection="list" item="saleorderNo" open="(" close=")" separator=",">
			#{saleorderNo, jdbcType=VARCHAR}
		</foreach>
	</select>

	<update id="updateDeliveryStatusBySaleorderNo" parameterType="com.vedeng.order.model.Saleorder">
		update T_SALEORDER
		<set >
			<if test="arrivalStatus  != null" >
				ARRIVAL_STATUS = #{arrivalStatus ,jdbcType=INTEGER},
			</if>
			<if test="deliveryStatus  != null" >
				DELIVERY_STATUS = #{deliveryStatus ,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="webTakeDeliveryTime != null" >
				WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryDirect != null" >
				DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT}
			</if>
		</set>
		where SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
	</update>
	<update id="updateSaleorderStatusById">
		UPDATE T_SALEORDER SET STATUS = 3, UPDATER = #{userId,jdbcType=INTEGER}, MOD_TIME = #{modTime,jdbcType=BIGINT} WHERE SALEORDER_ID IN
		<foreach collection="idList" index="index" item="item" separator="," open="(" close=")">
			#{item,jdbcType=INTEGER}
		</foreach>
	</update>


	<select id="getSaleorderCountByTime" parameterType="com.vedeng.order.model.SaleorderCountParam" resultType="java.lang.Integer">
		SELECT COALESCE(COUNT(1),0) FROM T_SALEORDER
		WHERE
		 TRADER_ID=#{traderId}
		 AND STATUS != 3
		<if test="type==0">
			AND VALID_STATUS=1
		</if>
		<if test="beginTime !=null">
			AND ADD_TIME <![CDATA[ >= ]]> #{beginTime}
		</if>
		<if test="endTime != null">
			AND ADD_TIME <![CDATA[ <= ]]> #{endTime}
		</if>
	</select>


	<select id="getOrderGoodsSkuByTraderId" parameterType="com.vedeng.trader.model.TraderOrderGoods" resultType="java.lang.String">
		SELECT distinct g.sku FROM T_SALEORDER_GOODS g LEFT JOIN T_SALEORDER o on o.SALEORDER_ID=g.SALEORDER_ID
		WHERE o.TRADER_ID=#{traderId}
		AND g.IS_DELETE = 0
	    AND g.IS_IGNORE = 0
	</select>


	<select id="getDaysCountSum" parameterType="com.vedeng.order.model.SaleorderCountParam" resultType="com.vedeng.order.model.SaleorderCountResult">
		SELECT COUNT(1) as orderDaysCount,COALESCE (SUM(b.totalAmount),0) as orderDaysMoneySum from (
		SELECT a.addTime,SUM(TOTAL_AMOUNT) as totalAmount FROM (
		SELECT FROM_UNIXTIME(ADD_TIME/1000,'%Y-%m-%d') as addTime,TOTAL_AMOUNT FROM T_SALEORDER
		WHERE
		TRADER_ID=#{traderId}
		AND ORDER_TYPE = 5
		<if test="beginTime !=null">
			AND ADD_TIME <![CDATA[ >= ]]> #{beginTime}
		</if>
		<if test="endTime != null">
			AND ADD_TIME <![CDATA[ <= ]]> #{endTime}
		</if>) a
		GROUP BY a.addTime
		) b
	</select>
	<select id="getSaleorderBySaleorderGoodsId" resultMap="BaseResultMap">
		SELECT
			B.*
			FROM
				T_SALEORDER_GOODS A
				LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
			WHERE
				A.SALEORDER_GOODS_ID=#{saleorderGoodsId}
	</select>
	<update id="updateOrderStatusByOrderNo" parameterType="com.vedeng.order.model.Saleorder">
		update T_SALEORDER
		<set >
			<if test="status != null" >
				STATUS = #{status,jdbcType=INTEGER},
			</if>
			<if test="updater != null" >
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>

			<if test="arrivalStatus != null" >
				ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
			</if>

			<if test="deliveryStatus != null" >
				DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
			</if>
		</set>
		where SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
	</update>

	<!-- 获取订单基本信息 -->
	<select id="getBaseSaleorderInfo" resultType="com.vedeng.order.model.Saleorder" parameterType="java.lang.Integer">
	SELECT
	a.*,
	b.QUOTEORDER_NO,
	c.BUSSINESS_CHANCE_ID,
	c.BUSSINESS_CHANCE_NO,
	d.VERIFY_USERNAME,
	d.STATUS AS VERIFY_STATUS,
	e.STATUS AS CONTRACT_STATUS,
	bh.STATUS AS bhVerifyStatus
	FROM
	T_SALEORDER a
	LEFT JOIN T_QUOTEORDER b
	ON a.QUOTEORDER_ID = b.QUOTEORDER_ID
	LEFT JOIN T_BUSSINESS_CHANCE c
	ON b.BUSSINESS_CHANCE_ID = c.BUSSINESS_CHANCE_ID
	LEFT JOIN T_VERIFIES_INFO d
	ON a.SALEORDER_ID = d.RELATE_TABLE_KEY
	AND d.RELATE_TABLE = "T_SALEORDER"
	AND d.VERIFIES_TYPE = 623
	LEFT JOIN T_VERIFIES_INFO e
	ON a.SALEORDER_ID = e.RELATE_TABLE_KEY
	AND e.RELATE_TABLE = "T_SALEORDER"
	AND e.VERIFIES_TYPE = 868
	LEFT JOIN T_VERIFIES_INFO bh
	ON a.SALEORDER_ID = bh.RELATE_TABLE_KEY
	AND bh.RELATE_TABLE = "T_SALEORDER"
	AND bh.VERIFIES_TYPE = 620
	WHERE a.SALEORDER_ID =
	#{saleorderId,jdbcType=INTEGER}
  </select>

	<insert id="insertSelective" parameterType="com.vedeng.order.model.Saleorder" useGeneratedKeys="true" keyProperty="saleorderId">
		insert into T_SALEORDER
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="saleorderId != null">
				SALEORDER_ID,
			</if>
			<if test="quoteorderId != null">
				QUOTEORDER_ID,
			</if>
			<if test="parentId != null">
				PARENT_ID,
			</if>
			<if test="saleorderNo != null">
				SALEORDER_NO,
			</if>
			<if test="orderType != null">
				ORDER_TYPE,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="source != null">
				SOURCE,
			</if>
			<if test="creatorOrgId != null">
				CREATOR_ORG_ID,
			</if>
			<if test="creatorOrgName != null">
				CREATOR_ORG_NAME,
			</if>
			<if test="orgId != null">
				ORG_ID,
			</if>
			<if test="orgName != null">
				ORG_NAME,
			</if>
			<if test="userId != null">
				USER_ID,
			</if>
			<if test="validOrgId != null">
				VALID_ORG_ID,
			</if>
			<if test="validOrgName != null">
				VALID_ORG_NAME,
			</if>
			<if test="validUserId != null">
				VALID_USER_ID,
			</if>
			<if test="validStatus != null">
				VALID_STATUS,
			</if>
			<if test="validTime != null">
				VALID_TIME,
			</if>
			<if test="endTime != null">
				END_TIME,
			</if>
			<if test="status != null">
				STATUS,
			</if>
			<if test="purchaseStatus != null">
				PURCHASE_STATUS,
			</if>
			<if test="lockedStatus != null">
				LOCKED_STATUS,
			</if>
			<if test="invoiceStatus != null">
				INVOICE_STATUS,
			</if>
			<if test="invoiceTime != null">
				INVOICE_TIME,
			</if>
			<if test="paymentStatus != null">
				PAYMENT_STATUS,
			</if>
			<if test="paymentTime != null">
				PAYMENT_TIME,
			</if>
			<if test="deliveryStatus != null">
				DELIVERY_STATUS,
			</if>
			<if test="deliveryTime != null">
				DELIVERY_TIME,
			</if>
			<if test="isCustomerArrival != null">
				IS_CUSTOMER_ARRIVAL,
			</if>
			<if test="arrivalStatus != null">
				ARRIVAL_STATUS,
			</if>
			<if test="arrivalTime != null">
				ARRIVAL_TIME,
			</if>
			<if test="serviceStatus != null">
				SERVICE_STATUS,
			</if>
			<if test="haveAccountPeriod != null">
				HAVE_ACCOUNT_PERIOD,
			</if>
			<if test="isPayment != null">
				IS_PAYMENT,
			</if>
			<if test="totalAmount != null">
				TOTAL_AMOUNT,
			</if>
			<if test="traderId != null">
				TRADER_ID,
			</if>
			<if test="customerType != null">
				CUSTOMER_TYPE,
			</if>
			<if test="customerNature != null">
				CUSTOMER_NATURE,
			</if>
			<if test="traderName != null">
				TRADER_NAME,
			</if>
			<if test="traderContactId != null">
				TRADER_CONTACT_ID,
			</if>
			<if test="traderContactName != null">
				TRADER_CONTACT_NAME,
			</if>
			<if test="traderContactMobile != null">
				TRADER_CONTACT_MOBILE,
			</if>
			<if test="traderContactTelephone != null">
				TRADER_CONTACT_TELEPHONE,
			</if>
			<if test="traderAddressId != null">
				TRADER_ADDRESS_ID,
			</if>
			<if test="traderArea != null">
				TRADER_AREA,
			</if>
			<if test="traderAddress != null">
				TRADER_ADDRESS,
			</if>
			<if test="traderComments != null">
				TRADER_COMMENTS,
			</if>
			<if test="takeTraderId != null">
				TAKE_TRADER_ID,
			</if>
			<if test="takeTraderName != null">
				TAKE_TRADER_NAME,
			</if>
			<if test="takeTraderContactId != null">
				TAKE_TRADER_CONTACT_ID,
			</if>
			<if test="takeTraderContactName != null">
				TAKE_TRADER_CONTACT_NAME,
			</if>
			<if test="takeTraderContactMobile != null">
				TAKE_TRADER_CONTACT_MOBILE,
			</if>
			<if test="takeTraderContactTelephone != null">
				TAKE_TRADER_CONTACT_TELEPHONE,
			</if>
			<if test="takeTraderAddressId != null">
				TAKE_TRADER_ADDRESS_ID,
			</if>
			<if test="takeTraderArea != null">
				TAKE_TRADER_AREA,
			</if>
			<if test="takeTraderAddress != null">
				TAKE_TRADER_ADDRESS,
			</if>
			<if test="isSendInvoice != null">
				IS_SEND_INVOICE,
			</if>
			<if test="invoiceTraderId != null">
				INVOICE_TRADER_ID,
			</if>
			<if test="invoiceTraderName != null">
				INVOICE_TRADER_NAME,
			</if>
			<if test="invoiceTraderContactId != null">
				INVOICE_TRADER_CONTACT_ID,
			</if>
			<if test="invoiceTraderContactName != null">
				INVOICE_TRADER_CONTACT_NAME,
			</if>
			<if test="invoiceTraderContactMobile != null">
				INVOICE_TRADER_CONTACT_MOBILE,
			</if>
			<if test="invoiceTraderContactTelephone != null">
				INVOICE_TRADER_CONTACT_TELEPHONE,
			</if>
			<if test="invoiceTraderAddressId != null">
				INVOICE_TRADER_ADDRESS_ID,
			</if>
			<if test="invoiceTraderArea != null">
				INVOICE_TRADER_AREA,
			</if>
			<if test="invoiceTraderAddress != null">
				INVOICE_TRADER_ADDRESS,
			</if>
			<if test="salesAreaId != null">
				SALES_AREA_ID,
			</if>
			<if test="salesArea != null">
				SALES_AREA,
			</if>
			<if test="terminalTraderId != null">
				TERMINAL_TRADER_ID,
			</if>
			<if test="terminalTraderName != null">
				TERMINAL_TRADER_NAME,
			</if>
			<if test="terminalTraderType != null">
				TERMINAL_TRADER_TYPE,
			</if>
			<if test="invoiceType != null">
				INVOICE_TYPE,
			</if>
			<if test="freightDescription != null">
				FREIGHT_DESCRIPTION,
			</if>
			<if test="deliveryType != null">
				DELIVERY_TYPE,
			</if>
			<if test="logisticsId != null">
				LOGISTICS_ID,
			</if>
			<if test="paymentType != null">
				PAYMENT_TYPE,
			</if>
			<if test="prepaidAmount != null">
				PREPAID_AMOUNT,
			</if>
			<if test="accountPeriodAmount != null">
				ACCOUNT_PERIOD_AMOUNT,
			</if>
			<if test="periodDay != null">
				PERIOD_DAY,
			</if>
			<if test="logisticsCollection != null">
				LOGISTICS_COLLECTION,
			</if>
			<if test="retainageAmount != null">
				RETAINAGE_AMOUNT,
			</if>
			<if test="retainageAmountMonth != null">
				RETAINAGE_AMOUNT_MONTH,
			</if>
			<if test="paymentComments != null">
				PAYMENT_COMMENTS,
			</if>
			<if test="additionalClause != null">
				ADDITIONAL_CLAUSE,
			</if>
			<if test="logisticsComments != null">
				LOGISTICS_COMMENTS,
			</if>
			<if test="financeComments != null">
				FINANCE_COMMENTS,
			</if>
			<if test="comments != null">
				COMMENTS,
			</if>
			<if test="invoiceComments != null">
				INVOICE_COMMENTS,
			</if>
			<if test="deliveryDirect != null">
				DELIVERY_DIRECT,
			</if>
			<if test="supplierClause != null">
				SUPPLIER_CLAUSE,
			</if>
			<if test="haveAdvancePurchase != null">
				HAVE_ADVANCE_PURCHASE,
			</if>
			<if test="advancePurchaseStatus != null">
				ADVANCE_PURCHASE_STATUS,
			</if>
			<if test="advancePurchaseComments != null">
				ADVANCE_PURCHASE_COMMENTS,
			</if>
			<if test="advancePurchaseTime != null">
				ADVANCE_PURCHASE_TIME,
			</if>
			<if test="isUrgent != null">
				IS_URGENT,
			</if>
			<if test="urgentAmount != null">
				URGENT_AMOUNT,
			</if>
			<if test="haveCommunicate != null">
				HAVE_COMMUNICATE,
			</if>
			<if test="prepareComments != null">
				PREPARE_COMMENTS,
			</if>
			<if test="marketingPlan != null">
				MARKETING_PLAN,
			</if>
			<if test="statusComments != null">
				STATUS_COMMENTS,
			</if>
			<if test="syncStatus != null">
				SYNC_STATUS,
			</if>
			<if test="satisfyInvoiceTime != null">
				SATISFY_INVOICE_TIME,
			</if>
			<if test="satisfyDeliveryTime != null">
				SATISFY_DELIVERY_TIME,
			</if>
			<if test="isSalesPerformance != null">
				IS_SALES_PERFORMANCE,
			</if>
			<if test="salesPerformanceTime != null">
				SALES_PERFORMANCE_TIME,
			</if>
			<if test="salesPerformanceModTime != null">
				SALES_PERFORMANCE_MOD_TIME,
			</if>
			<if test="isDelayInvoice != null">
				IS_DELAY_INVOICE,
			</if>
			<if test="invoiceMethod != null">
				INVOICE_METHOD,
			</if>
			<if test="lockedReason != null">
				LOCKED_REASON,
			</if>
			<if test="costUserIds != null">
				COST_USER_IDS,
			</if>
			<if test="ownerUserId != null">
				OWNER_USER_ID,
			</if>
			<if test="invoiceEmail != null">
				INVOICE_EMAIL,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
			<if test="traderAreaId != null">
				TRADER_AREA_ID,
			</if>
			<if test="takeTraderAreaId != null">
				TAKE_TRADER_AREA_ID,
			</if>
			<if test="invoiceTraderAreaId != null">
				INVOICE_TRADER_AREA_ID,
			</if>

			<if test="couponMoney!=null">
				COUPONMONEY,
			</if>
			<if test="isCoupons!=null">
				IS_COUPONS,
			</if>
			<if test="actionId!=null">
				ACTION_ID,
			</if>
			<if test="originalAmount!=null">
				ORIGINAL_AMOUNT,
			</if>
			<if test="autoAudit!=null">
				AUTO_AUDIT,
			</if>
			<if test="isSameAddress!=null">
				IS_SAME_ADDRESS,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="saleorderId != null">
				#{saleorderId,jdbcType=INTEGER},
			</if>
			<if test="quoteorderId != null">
				#{quoteorderId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null">
				#{parentId,jdbcType=INTEGER},
			</if>
			<if test="saleorderNo != null">
				#{saleorderNo,jdbcType=VARCHAR},
			</if>
			<if test="orderType != null">
				#{orderType,jdbcType=BIT},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="source != null">
				#{source,jdbcType=BIT},
			</if>
			<if test="creatorOrgId != null">
				#{creatorOrgId,jdbcType=INTEGER},
			</if>
			<if test="creatorOrgName != null">
				#{creatorOrgName,jdbcType=VARCHAR},
			</if>
			<if test="orgId != null">
				#{orgId,jdbcType=INTEGER},
			</if>
			<if test="orgName != null">
				#{orgName,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
				#{userId,jdbcType=INTEGER},
			</if>
			<if test="validOrgId != null">
				#{validOrgId,jdbcType=INTEGER},
			</if>
			<if test="validOrgName != null">
				#{validOrgName,jdbcType=VARCHAR},
			</if>
			<if test="validUserId != null">
				#{validUserId,jdbcType=INTEGER},
			</if>
			<if test="validStatus != null">
				#{validStatus,jdbcType=BIT},
			</if>
			<if test="validTime != null">
				#{validTime,jdbcType=BIGINT},
			</if>
			<if test="endTime != null">
				#{endTime,jdbcType=BIGINT},
			</if>
			<if test="status != null">
				#{status,jdbcType=BIT},
			</if>
			<if test="purchaseStatus != null">
				#{purchaseStatus,jdbcType=BIT},
			</if>
			<if test="lockedStatus != null">
				#{lockedStatus,jdbcType=BIT},
			</if>
			<if test="invoiceStatus != null">
				#{invoiceStatus,jdbcType=BIT},
			</if>
			<if test="invoiceTime != null">
				#{invoiceTime,jdbcType=BIGINT},
			</if>
			<if test="paymentStatus != null">
				#{paymentStatus,jdbcType=BIT},
			</if>
			<if test="paymentTime != null">
				#{paymentTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryStatus != null">
				#{deliveryStatus,jdbcType=BIT},
			</if>
			<if test="deliveryTime != null">
				#{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="isCustomerArrival != null">
				#{isCustomerArrival,jdbcType=BIT},
			</if>
			<if test="arrivalStatus != null">
				#{arrivalStatus,jdbcType=BIT},
			</if>
			<if test="arrivalTime != null">
				#{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="serviceStatus != null">
				#{serviceStatus,jdbcType=BIT},
			</if>
			<if test="haveAccountPeriod != null">
				#{haveAccountPeriod,jdbcType=BIT},
			</if>
			<if test="isPayment != null">
				#{isPayment,jdbcType=BIT},
			</if>
			<if test="totalAmount != null">
				#{totalAmount,jdbcType=DECIMAL},
			</if>
			<if test="traderId != null">
				#{traderId,jdbcType=INTEGER},
			</if>
			<if test="customerType != null">
				#{customerType,jdbcType=INTEGER},
			</if>
			<if test="customerNature != null">
				#{customerNature,jdbcType=INTEGER},
			</if>
			<if test="traderName != null">
				#{traderName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactId != null">
				#{traderContactId,jdbcType=INTEGER},
			</if>
			<if test="traderContactName != null">
				#{traderContactName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactMobile != null">
				#{traderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="traderContactTelephone != null">
				#{traderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="traderAddressId != null">
				#{traderAddressId,jdbcType=INTEGER},
			</if>
			<if test="traderArea != null">
				#{traderArea,jdbcType=VARCHAR},
			</if>
			<if test="traderAddress != null">
				#{traderAddress,jdbcType=VARCHAR},
			</if>
			<if test="traderComments != null">
				#{traderComments,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderId != null">
				#{takeTraderId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderName != null">
				#{takeTraderName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactId != null">
				#{takeTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderContactName != null">
				#{takeTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactMobile != null">
				#{takeTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactTelephone != null">
				#{takeTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddressId != null">
				#{takeTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderArea != null">
				#{takeTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddress != null">
				#{takeTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="isSendInvoice != null">
				#{isSendInvoice,jdbcType=BIT},
			</if>
			<if test="invoiceTraderId != null">
				#{invoiceTraderId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderName != null">
				#{invoiceTraderName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactId != null">
				#{invoiceTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderContactName != null">
				#{invoiceTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactMobile != null">
				#{invoiceTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactTelephone != null">
				#{invoiceTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderAddressId != null">
				#{invoiceTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderArea != null">
				#{invoiceTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderAddress != null">
				#{invoiceTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="salesAreaId != null">
				#{salesAreaId,jdbcType=INTEGER},
			</if>
			<if test="salesArea != null">
				#{salesArea,jdbcType=VARCHAR},
			</if>
			<if test="terminalTraderId != null">
				#{terminalTraderId,jdbcType=INTEGER},
			</if>
			<if test="terminalTraderName != null">
				#{terminalTraderName,jdbcType=VARCHAR},
			</if>
			<if test="terminalTraderType != null">
				#{terminalTraderType,jdbcType=INTEGER},
			</if>
			<if test="invoiceType != null">
				#{invoiceType,jdbcType=INTEGER},
			</if>
			<if test="freightDescription != null">
				#{freightDescription,jdbcType=INTEGER},
			</if>
			<if test="deliveryType != null">
				#{deliveryType,jdbcType=INTEGER},
			</if>
			<if test="logisticsId != null">
				#{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="paymentType != null">
				#{paymentType,jdbcType=INTEGER},
			</if>
			<if test="prepaidAmount != null">
				#{prepaidAmount},
			</if>
			<if test="accountPeriodAmount != null">
				#{accountPeriodAmount,jdbcType=DECIMAL},
			</if>
			<if test="periodDay != null">
				#{periodDay,jdbcType=INTEGER},
			</if>
			<if test="logisticsCollection != null">
				#{logisticsCollection,jdbcType=BIT},
			</if>
			<if test="retainageAmount != null">
				#{retainageAmount,jdbcType=DECIMAL},
			</if>
			<if test="retainageAmountMonth != null">
				#{retainageAmountMonth,jdbcType=INTEGER},
			</if>
			<if test="paymentComments != null">
				#{paymentComments,jdbcType=VARCHAR},
			</if>
			<if test="additionalClause != null">
				#{additionalClause,jdbcType=VARCHAR},
			</if>
			<if test="logisticsComments != null">
				#{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="financeComments != null">
				#{financeComments,jdbcType=VARCHAR},
			</if>
			<if test="comments != null">
				#{comments,jdbcType=VARCHAR},
			</if>
			<if test="invoiceComments != null">
				#{invoiceComments,jdbcType=VARCHAR},
			</if>
			<if test="deliveryDirect != null">
				#{deliveryDirect,jdbcType=BIT},
			</if>
			<if test="supplierClause != null">
				#{supplierClause,jdbcType=VARCHAR},
			</if>
			<if test="haveAdvancePurchase != null">
				#{haveAdvancePurchase,jdbcType=BIT},
			</if>
			<if test="advancePurchaseStatus != null">
				#{advancePurchaseStatus,jdbcType=BIT},
			</if>
			<if test="advancePurchaseComments != null">
				#{advancePurchaseComments,jdbcType=VARCHAR},
			</if>
			<if test="advancePurchaseTime != null">
				#{advancePurchaseTime,jdbcType=BIGINT},
			</if>
			<if test="isUrgent != null">
				#{isUrgent,jdbcType=BIT},
			</if>
			<if test="urgentAmount != null">
				#{urgentAmount,jdbcType=DECIMAL},
			</if>
			<if test="haveCommunicate != null">
				#{haveCommunicate,jdbcType=BIT},
			</if>
			<if test="prepareComments != null">
				#{prepareComments,jdbcType=VARCHAR},
			</if>
			<if test="marketingPlan != null">
				#{marketingPlan,jdbcType=VARCHAR},
			</if>
			<if test="statusComments != null">
				#{statusComments,jdbcType=INTEGER},
			</if>
			<if test="syncStatus != null">
				#{syncStatus,jdbcType=BIT},
			</if>
			<if test="satisfyInvoiceTime != null">
				#{satisfyInvoiceTime,jdbcType=BIGINT},
			</if>
			<if test="satisfyDeliveryTime != null">
				#{satisfyDeliveryTime,jdbcType=BIGINT},
			</if>
			<if test="isSalesPerformance != null">
				#{isSalesPerformance,jdbcType=BIT},
			</if>
			<if test="salesPerformanceTime != null">
				#{salesPerformanceTime,jdbcType=BIGINT},
			</if>
			<if test="salesPerformanceModTime != null">
				#{salesPerformanceModTime,jdbcType=BIGINT},
			</if>
			<if test="isDelayInvoice != null">
				#{isDelayInvoice,jdbcType=BIT},
			</if>
			<if test="invoiceMethod != null">
				#{invoiceMethod,jdbcType=BIT},
			</if>
			<if test="lockedReason != null">
				#{lockedReason,jdbcType=VARCHAR},
			</if>
			<if test="costUserIds != null">
				#{costUserIds,jdbcType=VARCHAR},
			</if>
			<if test="ownerUserId != null">
				#{ownerUserId,jdbcType=INTEGER},
			</if>
			<if test="invoiceEmail != null">
				#{invoiceEmail,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
			<if test="traderAreaId != null">
				#{traderAreaId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderAreaId != null">
				#{takeTraderAreaId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderAreaId != null">
				#{invoiceTraderAreaId, jdbcType=INTEGER},
			</if>
			<if test="couponMoney!=null">
				#{couponMoney},
			</if>
			<if test="isCoupons!=null">
				#{isCoupons},
			</if>
			<if test="actionId!=null">
				#{actionId},
			</if>
			<if test="originalAmount!=null">
				#{originalAmount},
			</if>
			<if test="autoAudit!=null">
				#{autoAudit},
			</if>
			<if test="isSameAddress!=null">
				#{isSameAddress},
			</if>
		</trim>
	</insert>
	<select id="getSaleOrersByIdList" resultType="com.vedeng.order.model.Saleorder">
		SELECT *
		FROM
		T_SALEORDER
		WHERE SALEORDER_ID IN
		<foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<!--根据订单号查询订单-->
	<select id="getSaleOrderId" resultMap="BaseResultMap">
		select SALEORDER_ID,LOGISTICS_COMMENTS from T_SALEORDER where SALEORDER_NO=#{saleorderNo}
	</select>
    <select id="getSaleorderByExpressDetailId" resultType="com.vedeng.order.model.Saleorder" parameterType="list">
		SELECT S.SALEORDER_ID, S.SALEORDER_NO, S.TRADER_NAME, GROUP_CONCAT(ED.EXPRESS_DETAIL_ID) AS COMMENTS FROM T_SALEORDER S
		JOIN T_SALEORDER_GOODS SG ON S.SALEORDER_ID = SG.SALEORDER_ID
		JOIN T_EXPRESS_DETAIL ED ON SG.SALEORDER_GOODS_ID = ED.RELATED_ID
		WHERE
		ED.EXPRESS_DETAIL_ID
		IN
		<foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
			#{item,jdbcType=INTEGER}
		</foreach>
		GROUP BY SG.SALEORDER_ID
	</select>


    <select id="getAllActionId" resultType="int">
		SELECT
			A.ACTION_ID
		FROM
			T_SALEORDER A
		WHERE
			A.ACTION_ID > 0
		  AND FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d' ) > DATE_SUB( CURDATE(), INTERVAL #{day} DAY )
		GROUP BY
			A.ACTION_ID
    </select>
	<select id="getSaleorderGoodsByOrderListId" resultType="com.vedeng.order.model.SaleorderGoods">
		SELECT * FROM T_SALEORDER_GOODS WHERE EL_ORDERLIST_ID IN
		<foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
			#{item,jdbcType=INTEGER}
		</foreach>
	</select>
	<select id="getSaleorderByOrderListId" resultType="com.vedeng.order.model.Saleorder">
		SELECT S.* FROM T_SALEORDER S JOIN T_SALEORDER_GOODS SG ON S.SALEORDER_ID = SG.SALEORDER_ID WHERE SG.EL_ORDERLIST_ID = #{orderListId,jdbcType=INTEGER}
	</select>

	<!--改变第一次物流的评论信息-->
	<update id="updateLogisticsComments">
			UPDATE T_SALEORDER SET LOGISTICS_COMMENTS=#{logisticsComments} WHERE SALEORDER_ID=#{saleorderId}
	</update>

	<select id="getUserDetailInfoByUserId" resultType="com.vedeng.authorization.model.User" parameterType="java.lang.Integer">
        select
          USER_ID,EMAIL
        from T_USER_DETAIL
        where USER_ID = #{userId,jdbcType=INTEGER}
  	</select>

	<!--所属类型TRADER_TYPE 1::经销商（包含终端）2:供应商-->
	<select id="getUserInfoByTraderId" parameterType="java.lang.Integer" resultType="com.vedeng.authorization.model.User">
		SELECT
			b.USER_ID,b.USERNAME,d.ORG_ID,e.ORG_NAME
		FROM
			T_R_TRADER_J_USER a
		LEFT JOIN
			T_USER b
		ON
			a.USER_ID = b.USER_ID
		LEFT JOIN
			T_R_USER_POSIT c
		ON
			b.USER_ID = c.USER_ID
		LEFT JOIN
			T_POSITION d
		ON
			d.POSITION_ID = c.POSITION_ID
		LEFT JOIN
			T_ORGANIZATION e
		ON
			d.ORG_ID = e.ORG_ID
		WHERE
			a.TRADER_ID = #{traderId}
			AND
			a.TRADER_TYPE = 1
			limit 1
	</select>

	<!--宝石花出库单列表-->
	<select id="getFlowerPrintOutListPage" resultMap="BaseResultMap" parameterType="Map">
	SELECT
	DISTINCT
		A.SALEORDER_ID,A.SALEORDER_NO,A.TRADER_NAME,A.PAYMENT_STATUS,A.DELIVERY_STATUS,A.ARRIVAL_STATUS,A.VALID_TIME,A.TRADER_ID
	FROM
		T_SALEORDER A
	LEFT JOIN T_SALEORDER_GOODS  B ON A.SALEORDER_ID=B.SALEORDER_ID
	JOIN T_WAREHOUSE_GOODS_OPERATE_LOG C ON C.RELATED_ID = B.SALEORDER_GOODS_ID AND C.OPERATE_TYPE=2 AND C.IS_ENABLE =1
	WHERE
		1=1
		<if test="saleorder.takeTraderName != null and saleorder.takeTraderName != '' ">
		  AND A.TAKE_TRADER_NAME LIKE CONCAT('%',#{saleorder.takeTraderName },'%' )
		</if>
		AND A.TRADER_ID IN
		<foreach collection="saleorder.traderIdList" item="item" open="(" close=")"  separator=",">
		 #{item}
		</foreach>
		<if test="saleorder.searchBegintime != null and saleorder.searchBegintime != ''">
			AND C.ADD_TIME >= #{saleorder.searchBegintime,jdbcType=INTEGER}
		</if>
		<if test="saleorder.searchEndtime != null and saleorder.searchEndtime != ''">
			AND C.ADD_TIME <![CDATA[ <= ]]>
			#{saleorder.searchEndtime,jdbcType=INTEGER}
		</if>
		ORDER BY A.SALEORDER_ID DESC
	</select>

	<update id="cleanSaleOrder"  >

UPDATE T_SALEORDER
SET COMPANY_ID = 6 ,STATUS = 3,MOD_TIME=NOW()
WHERE
	SALEORDER_ID IN (
	SELECT
		t.SALEORDER_ID
	FROM
		(
		SELECT
			SALEORDER_ID
		FROM
			T_SALEORDER S
		WHERE
		S.COMPANY_ID=1 AND
			S.USER_ID IN ( SELECT user_id FROM T_USER WHERE username IN ( 'alina.Huang', 'jayden.duan', 'test', 'haocai.vedeng' ) )
			AND S.TRADER_ID IN ( SELECT TRADER_ID FROM T_R_TRADER_J_USER WHERE USER_ID IN ( SELECT USER_ID FROM T_USER WHERE username IN ( 'alina.Huang', 'jayden.duan', 'test' ) ) )
		) t
	)

	</update>
	<update id="cleanAfterSale"  >
UPDATE T_AFTER_SALES
SET COMPANY_ID = 6,STATUS = 3,
mod_time = now( )
WHERE
	ORDER_ID IN (
	SELECT
		t.SALEORDER_ID
	FROM
		(
		SELECT
			SALEORDER_ID
		FROM
			T_SALEORDER S
		WHERE
		   S.COMPANY_ID=1 AND
			S.USER_ID IN ( SELECT user_id FROM T_USER WHERE username IN ( 'alina.Huang', 'jayden.duan', 'test', 'haocai.vedeng' ) )
			AND S.TRADER_ID IN ( SELECT TRADER_ID FROM T_R_TRADER_J_USER WHERE USER_ID IN ( SELECT USER_ID FROM T_USER WHERE username IN ( 'alina.Huang', 'jayden.duan', 'test' ) ) )
		) t
	)
	</update>


	<!--校验锁的状态-->
	<update id="updateLockedStatus">
		update T_SALEORDER set LOCKED_STATUS=1 where SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
	</update>
	<update id="clearBussiness"  >
	update T_BUSSINESS_CHANCE set COMPANY_ID = 6 where USER_ID in (select  USER_ID from T_USER  where USERNAME  in('test','alina.Huang','jerry.li','jayden.duan'))
	</update>
	<update id="updateDataTimeByOrderId">
	UPDATE T_SALEORDER
	SET UPDATE_DATA_TIME = NOW()
	WHERE
	SALEORDER_ID = #{orderId,jdbcType=INTEGER}
	</update>
	<update id="updateDataTimeByDetailId">
	UPDATE T_SALEORDER
	SET UPDATE_DATA_TIME = NOW()
	WHERE
	SALEORDER_ID = (
	  SELECT A.SALEORDER_ID FROM T_SALEORDER_GOODS A WHERE A.SALEORDER_GOODS_ID = #{orderDetailId,jdbcType=INTEGER}
	)
	</update>
	<select id="getPaymentAmount" resultType="java.math.BigDecimal">
	SELECT
	IFNULL(SUM( a.AMOUNT ),0) receive_amount
FROM
	T_CAPITAL_BILL a
	LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
WHERE
	b.ORDER_TYPE = 1
	AND (
		a.TRADER_TYPE IN ( 1, 4 )
		OR (
			a.TRADER_TYPE = 2
			AND (a.TRADER_MODE = 520 OR a.TRADER_MODE= 522)
		))
	 AND  b.RELATED_ID = #{orderId,jdbcType=INTEGER}
	</select>
	<select id="getReturnAmount" resultType="java.math.BigDecimal">
	SELECT
	IFNULL(ABS(SUM(C.AMOUNT)),0) as AMOUNT
	FROM
	T_AFTER_SALES A
	LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID=B.RELATED_ID
	LEFT JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID=C.CAPITAL_BILL_ID
	WHERE
	A.ORDER_ID = #{orderId,jdbcType=INTEGER}
	AND A.SUBJECT_TYPE = 535
	AND A.ATFER_SALES_STATUS IN ( 1, 2 )
	AND B.BUSSINESS_TYPE=531
	AND B.ORDER_TYPE=3
	AND C.TRADER_MODE=530
	</select>
	<update id="updateAmountInfo">
	UPDATE T_SALEORDER
	SET REAL_PAY_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
	REAL_RETURN_AMOUNT = #{endAmount,jdbcType=DECIMAL},
	REAL_TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
	NOPAYBACK_AMOUNT = #{accountPayable,jdbcType=DECIMAL}
	WHERE
	SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
	</update>
    <update id="updateNotSalesPerformanceOfSaleorder">
		UPDATE T_SALEORDER SET IS_SALES_PERFORMANCE = 0, SALES_PERFORMANCE_TIME = 0 WHERE SALEORDER_ID = #{saleorderId}
	</update>
	<select id="getrecentDayOrder" resultType="java.lang.Integer">
		SELECT
		a.SALEORDER_ID
		FROM
		T_SALEORDER a
		WHERE
		a.`STATUS` != 3
		AND a.ORDER_TYPE != 2
		AND a.UPDATE_DATA_TIME > DATE_SUB(
		CURDATE(),
		INTERVAL ${num} DAY)
		ORDER BY
		a.SALEORDER_ID DESC
	</select>
	<select id="getSaleorderGoodsById" resultType="com.vedeng.order.model.SaleorderGoods">
		SELECT * FROM T_SALEORDER_GOODS WHERE SALEORDER_ID = #{saleorderId} AND IS_DELETE = 0;
	</select>


	<select id="findByElorderNo" parameterType="java.lang.String" resultType="java.lang.Integer">
    select
     count(SALEORDER_ID)
    from T_SALEORDER
    where EL_SALEORDRE_NO = #{orderNumber,jdbcType=VARCHAR}
  </select>
<!--	<select id="getSaleorderRealAmountByTraderId" resultMap="BaseResultMap">-->
<!--	SELECT-->
<!--	A.SALEORDER_ID-->
<!--	FROM-->
<!--	T_SALEORDER A-->
<!--	WHERE-->
<!--	A.`STATUS` IN ( 1, 2 )-->
<!--	AND A.REAL_PAY_AMOUNT > 0-->
<!--	AND A.TRADER_ID =#{traderId,jdbcType=INTEGER}-->
<!--	</select>-->
    <select id="getCouponOrderDataByCouponId" resultType="com.vedeng.order.model.coupon.CouponOrderData">
    SELECT
    COUNT(DISTINCT B.TRADER_ID) AS traderNum,
	COUNT(B.SALEORDER_ID) AS orderNum,
	SUM(B.REAL_PAY_AMOUNT) AS allTotalAmount,
	SUM(A.DENOMINATION) AS totalCouponAmount,
	(SUM(B.TOTAL_AMOUNT)+SUM(A.DENOMINATION)) AS saleAmount
    FROM
	T_SALEORDER_COUPON A
	LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
    WHERE
	B.ORDER_TYPE IN ( 1, 5 )
	AND B.PAYMENT_STATUS = 2
	AND A.COUPON_ID = #{couponId,jdbcType=INTEGER}
    </select>
	<select id="getCouponOrderDataDetailListByCouponId" resultType="com.vedeng.order.model.coupon.CouponOrderDetailData">
	SELECT
	B.SALEORDER_NO as orderNo,
	B.TRADER_ID as traderId,
	C.TRADER_NAME as traderName,
	A.COUPON_ID as couponId,
	A.COUPON_CODE as couponCode,
	B.TOTAL_AMOUNT as originalAmount,
	B.REAL_PAY_AMOUNT  as realPayAmount
    FROM
	T_SALEORDER_COUPON A
	LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
	LEFT JOIN T_TRADER C ON B.TRADER_ID = C.TRADER_ID
    WHERE
    B.ORDER_TYPE IN (1,5)
    AND B.PAYMENT_STATUS=2
	AND A.COUPON_ID = #{couponId,jdbcType=INTEGER}
    </select>
    <select id="getCouponOrderDataDetailListBycouponCodeList"
            resultType="com.vedeng.order.model.coupon.CouponOrderDetailData">
		SELECT
	B.SALEORDER_NO as orderNo,
	B.TRADER_ID as traderId,
	C.TRADER_NAME as traderName,
	A.COUPON_ID as couponId,
	A.COUPON_CODE as couponCode,
	B.TOTAL_AMOUNT as originalAmount,
	B.REAL_PAY_AMOUNT  as realPayAmount
    FROM
	T_SALEORDER_COUPON A
	LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
	LEFT JOIN T_TRADER C ON B.TRADER_ID = C.TRADER_ID
    WHERE
    B.ORDER_TYPE IN (1,5)
	AND A.COUPON_CODE IN
		<foreach collection="list" item="item" open="(" close=")"  separator=",">
			#{item}
		</foreach>
	GROUP BY B.SALEORDER_ID
	</select>

	<select id="getBDSaleOrderAndHasCoupon" resultType="com.vedeng.order.model.Saleorder">
		SELECT
			A.*
		FROM
			T_SALEORDER A
		INNER JOIN T_SALEORDER_COUPON B ON A.SALEORDER_ID = B.SALEORDER_ID
		WHERE A.STATUS IN (0,1) AND A.ORDER_TYPE = 1
    </select>

    <select id="getSaleorderByQuoteorderId" resultType="com.vedeng.order.model.Saleorder">
		SELECT * FROM T_SALEORDER WHERE QUOTEORDER_ID = #{quoteorderId}
	</select>

	<select id="getTraderIdOrderedInPeriod" resultType="int" parameterType="com.vedeng.trader.model.Period">
		SELECT TRADER_ID FROM T_SALEORDER
		 WHERE PAYMENT_STATUS=0
		 AND ORDER_TYPE IN (0,1)
		AND ADD_TIME <![CDATA[>=]]> #{beginTime}
      and ADD_TIME <![CDATA[<=]]> #{endTime}
      AND TRADER_ID IS NOT NULL
	</select>

	<select id="getTraderIdBuyInPeriod" resultType="int" parameterType="com.vedeng.trader.model.Period">
		SELECT TRADER_ID FROM T_SALEORDER
		WHERE
		PAYMENT_STATUS!=0
		AND ORDER_TYPE IN (0,1)
		AND ADD_TIME <![CDATA[>=]]> #{beginTime}
      and ADD_TIME <![CDATA[<=]]> #{endTime}
      AND TRADER_ID IS NOT NULL
	</select>

	<select id="getTraderIdsOrderGoods" resultType="int" parameterType="com.vedeng.trader.group.model.GoodsQueryParam">
		SELECT DISTINCT O.TRADER_ID FROM T_SALEORDER O
		LEFT JOIN T_SALEORDER_GOODS G ON O.SALEORDER_ID=G.SALEORDER_ID
		<if test="categoryIds !=null or brandId !=null">
			LEFT JOIN V_CORE_SKU K ON G.SKU=K.SKU_NO
			LEFT JOIN V_CORE_SPU P ON K.SPU_ID=K.SPU_ID
		</if>
		WHERE 1=1
		AND O.ORDER_TYPE IN (0,1)
		AND O.PAYMENT_STATUS=0
		AND G.IS_DELETE=0
		and O.ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		and O.ADD_TIME <![CDATA[<=]]> #{period.endTime}
		<if test="categoryIds !=null ">
			AND P.CATEGORY_ID IN
			<foreach collection="categoryIds" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
		<if test="brandId !=null ">
			AND P.BRAND_ID =#{brandId}
		</if>
		<if test="skuList !=null ">
			AND G.SKU IN
			<foreach collection="skuList" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
	</select>

	<select id="getTraderIdsBuyGoods" resultType="int" parameterType="com.vedeng.trader.group.model.GoodsQueryParam">
		SELECT DISTINCT O.TRADER_ID FROM T_SALEORDER O
		LEFT JOIN T_SALEORDER_GOODS G ON O.SALEORDER_ID=G.SALEORDER_ID
		<if test="categoryIds !=null or brandId !=null">
			LEFT JOIN V_CORE_SKU K ON G.SKU=K.SKU_NO
			LEFT JOIN V_CORE_SPU P ON K.SPU_ID=K.SPU_ID
		</if>
		WHERE 1=1
		AND O.ORDER_TYPE IN (0,1)
		AND O.PAYMENT_STATUS!=0
		AND G.IS_DELETE=0
		AND O.ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		AND O.ADD_TIME <![CDATA[<=]]> #{period.endTime}
		<if test="categoryIds !=null ">
			AND P.CATEGORY_ID IN
			<foreach collection="categoryIds" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
		<if test="brandId !=null ">
			AND P.BRAND_ID =#{brandId}
		</if>
		<if test="skuList !=null ">
			AND G.SKU IN
			<foreach collection="skuList" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
	</select>

	<select id="getTraderIdsByAmount" parameterType="com.vedeng.trader.group.model.SaleorderSum" resultType="java.lang.Integer">
		select A.TRADER_ID from (
		   SELECT e.TRADER_ID,SUM(e.realAmount) AS AMOUNT from (
		SELECT a.TRADER_ID, if(COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE = 5,b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0) <![CDATA[ < ]]> 0,
		0,
		COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE = 5,b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0)) as realAmount
		FROM T_SALEORDER a
		LEFT JOIN
		(SELECT sum(bb.NUM * cc.PRICE) AS tk_amount,
		sum(bb.SKU_REFUND_AMOUNT) AS SKU_REFUND_AMOUNT,
		aa.ORDER_ID
		FROM T_AFTER_SALES aa
		LEFT JOIN T_AFTER_SALES_GOODS bb
		ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		LEFT JOIN T_SALEORDER_GOODS cc
		ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		WHERE     aa.TYPE = 539
		AND aa.SUBJECT_TYPE = 535
		AND aa.VALID_STATUS = 1
		AND aa.ATFER_SALES_STATUS IN (1,2)
		GROUP BY aa.ORDER_ID) AS b
		ON a.SALEORDER_ID = b.ORDER_ID
		WHERE a.ORDER_TYPE=1
		and a.ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		and a.ADD_TIME <![CDATA[<=]]> #{period.endTime}
		) e
		   GROUP BY e.TRADER_ID
		) A
		WHERE 1=1
		<if test="lowAmount!=null">
			and A.AMOUNT <![CDATA[>=]]> #{lowAmount}
		</if>
		<if test="highAmount!=null">
			and A.AMOUNT <![CDATA[<=]]> #{highAmount}
		</if>
	</select>

	<select id="getTraderIdsByTimes" parameterType="com.vedeng.trader.group.model.SaleorderSum" resultType="java.lang.Integer">
		select A.TRADER_ID FROM (
		select e.TRADER_ID,COUNT(e.TRADER_ID) AS `TIMES` from (
		SELECT a.TRADER_ID, if(COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE = 5,b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0) <![CDATA[<]]> 0,
		0,
		COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE = 5,b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0)) as realAmount
		FROM T_SALEORDER a
		LEFT JOIN
		(SELECT sum(bb.NUM * cc.PRICE) AS tk_amount,
		sum(bb.SKU_REFUND_AMOUNT) AS SKU_REFUND_AMOUNT,
		aa.ORDER_ID
		FROM T_AFTER_SALES aa
		LEFT JOIN T_AFTER_SALES_GOODS bb
		ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		LEFT JOIN T_SALEORDER_GOODS cc
		ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		WHERE     aa.TYPE = 539
		AND aa.SUBJECT_TYPE = 535
		AND aa.VALID_STATUS = 1
		AND aa.ATFER_SALES_STATUS IN (1,2)
		GROUP BY aa.ORDER_ID) AS b
		ON a.SALEORDER_ID = b.ORDER_ID
		WHERE a.PAYMENT_STATUS!=0 AND a.ORDER_TYPE=1 AND  a.ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		and a.ADD_TIME <![CDATA[<=]]> #{period.endTime}
		) e where e.realAmount <![CDATA[>]]>0
		GROUP BY e.TRADER_ID
		) A
		WHERE 1=1
		<if test="lowTimes!=null">
			and A.TIMES <![CDATA[>=]]> #{lowTimes}
		</if>
		<if test="highTimes!=null">
			and A.TIMES <![CDATA[<=]]> #{highTimes}
		</if>
	</select>

	<select id="getTraderIdsByDealRecently" parameterType="com.vedeng.trader.group.model.SaleorderSum" resultType="java.lang.Integer">
		   SELECT DISTINCT TRADER_ID from T_SALEORDER
		   WHERE ORDER_TYPE=1 AND ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		     and ADD_TIME <![CDATA[<=]]> #{period.endTime}

	</select>
    <select id="getVaildOrderAndNoChooseOrderId" resultType="java.lang.Integer">
	SELECT
	A.SALEORDER_ID
	FROM
	T_SALEORDER_GOODS A
	LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
	WHERE
	B.`STATUS` = 1
	AND A.IS_DELETE = 0
	AND A.DELIVERY_STATUS !=2
	AND B.DELIVERY_STATUS !=2
	AND A.DELIVERY_DIRECT = 0
	AND B.ORDER_TYPE != 2
	AND A.GOODS_ID != 127063
	AND B.ADD_TIME > 1577808000000
	GROUP BY
	A.SALEORDER_ID
	</select>
	<select id="getSaleorderRealAmountByTraderId" resultType="com.vedeng.order.model.Saleorder">
    SELECT
	A.SALEORDER_ID
	FROM
	T_SALEORDER A
	WHERE
	A.`STATUS` IN ( 1, 2 )
	AND A.REAL_PAY_AMOUNT > 0
	AND A.TRADER_ID =#{traderId,jdbcType=INTEGER}
    </select>

	<select id="getHcSaleorderInfoByInvoiceIds" resultType="com.vedeng.order.model.Saleorder">
		SELECT
			T1.*
		FROM
			T_SALEORDER T1
			INNER JOIN T_INVOICE T2 ON T1.SALEORDER_ID = T2.RELATED_ID
		WHERE
			T1.ORDER_TYPE = 5
		AND T2.INVOICE_ID IN
		<foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
			#{invoiceId,jdbcType=INTEGER}
		</foreach>
		GROUP BY
			T2.INVOICE_ID
	</select>

	<select id="findRelateSaleOrderNo" resultType="java.lang.String">
		SELECT
			s.SALEORDER_NO
		FROM T_SALEORDER_GOODS sg
		LEFT JOIN T_SALEORDER s ON s.SALEORDER_ID = sg.SALEORDER_ID
		LEFT JOIN T_R_BUYORDER_J_SALEORDER rjs ON rjs.SALEORDER_GOODS_ID=sg.SALEORDER_GOODS_ID
		WHERE rjs.BUYORDER_GOODS_ID = #{buyOrderGoodId,jdbcType=INTEGER}
	</select>
	<select id="getSaleorderRealAmountByTraderIdAndSaleorderId" resultType="com.vedeng.order.model.Saleorder">
	SELECT
	A.SALEORDER_ID
	FROM
	T_SALEORDER A
	WHERE
	A.`STATUS` IN ( 1, 2 )
	AND A.REAL_PAY_AMOUNT > 0
	AND A.SALEORDER_ID != #{saleorderId,jdbcType=INTEGER}
	AND A.TRADER_ID = #{traderId,jdbcType=INTEGER}
	</select>
    <select id="getRealAmount" resultType="java.math.BigDecimal">
		SELECT if(COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE IN(1, 5),b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0) <![CDATA[ < ]]> 0,
		          0,
		          COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE IN(1, 5),b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0))
		FROM T_SALEORDER a
		     LEFT JOIN
		     (SELECT sum(bb.NUM * cc.PRICE) AS tk_amount,
		             sum(bb.SKU_REFUND_AMOUNT) AS SKU_REFUND_AMOUNT,
		             aa.ORDER_ID
		      FROM T_AFTER_SALES aa
		           LEFT JOIN T_AFTER_SALES_GOODS bb
		              ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		           LEFT JOIN T_SALEORDER_GOODS cc
		              ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		      WHERE     aa.TYPE = 539
		            AND aa.SUBJECT_TYPE = 535
		            AND aa.VALID_STATUS = 1
		            AND aa.ATFER_SALES_STATUS IN (1,2)
					AND bb.GOODS_TYPE = 0 <!-- VDERP-7492 计算实际金额，需要剔除手续费 -->
		            AND aa.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
		      GROUP BY aa.ORDER_ID) AS b
		        ON a.SALEORDER_ID = b.ORDER_ID
		WHERE a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
	<update id="updateSaleorderPayStatus" parameterType="com.vedeng.order.model.Saleorder">
		UPDATE T_SALEORDER
	  	SET
	  		PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
	  		PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
	  		IS_PAYMENT = #{isPayment,jdbcType=BIT},
	  		SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
	  		PAY_TYPE = #{payType,jdbcType=BIT},
	  		PAYMENT_MODE = #{paymentMode,jdbcType=BIT}
		WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>


	<update id="updateIsDeleteByOrderNo" parameterType="com.vedeng.order.model.Saleorder">
		UPDATE T_SALEORDER SET IS_DELETE=1 WHERE SALEORDER_NO =#{saleorderNo}
	</update>
	<update id="updateContractUrlOfSaleorder">
		UPDATE T_SALEORDER SET CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR} WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>

	<select id="getHCSaleOrderAndHasCoupon"  resultType="com.vedeng.order.model.Saleorder">
		SELECT SALEORDER_ID,SALEORDER_NO,TRADER_ID,STATUS,PAYMENT_STATUS,VALID_TIME,ADD_TIME FROM T_SALEORDER WHERE (ACTION_ID = 0 OR ACTION_ID IS NULL) and PAYMENT_STATUS=0 AND ORDER_TYPE=5 and STATUS <![CDATA[ <> ]]> 3
	</select>


	<select id="getHcOrgValidStatus" resultType="com.vedeng.order.model.Saleorder">

SELECT
	A.SALEORDER_NO,
	A.SALEORDER_ID,
	A.`STATUS`,
	ORG.ORG_NAME,
	A.TRADER_NAME,
	B.SKU,
	B.NUM,
	IFNULL( B.AFTER_RETURN_NUM, 0 ) AFTER_RETURN_NUM
FROM
	T_SALEORDER A
	LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID = B.SALEORDER_ID
	AND B.NUM - IFNULL( B.AFTER_RETURN_NUM, 0 ) > 0 AND B.IS_DELETE=0
	LEFT JOIN T_R_TRADER_J_USER C ON C.TRADER_ID = A.TRADER_ID
	AND C.TRADER_TYPE = 1
	LEFT JOIN T_USER D ON D.USER_ID = C.USER_ID
	LEFT JOIN T_R_USER_POSIT UP ON UP.USER_ID = D.USER_ID
	LEFT JOIN T_POSITION PO ON UP.POSITION_ID = PO.POSITION_ID
	LEFT JOIN T_ORGANIZATION ORG ON PO.ORG_ID = ORG.ORG_ID
WHERE
	A.`STATUS` IN (1,2)
	AND A.SALEORDER_ID != #{saleorderId,jdbcType=INTEGER}
	AND A.TRADER_ID = #{traderId,jdbcType=INTEGER}
	AND ORG.ORG_NAME LIKE '%医械购%'
	AND B.SALEORDER_GOODS_ID IS NOT NULL
	AND B.GOODS_ID NOT IN ( SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693 )
	AND A.VALID_TIME <![CDATA[ < ]]> (SELECT S.VALID_TIME FROM T_SALEORDER S WHERE S.SALEORDER_ID =  #{saleorderId,jdbcType=INTEGER})
GROUP BY
	A.SALEORDER_ID
	</select>

    <select id="getBdOrderListPage" parameterType="java.util.Map" resultType="com.vedeng.order.model.SimpleBdOrderChooseRes">
        SELECT S.SALEORDER_ID,S.SALEORDER_NO,S.TRADER_ID,S.TRADER_NAME,S.TRADER_CONTACT_NAME,S.TRADER_CONTACT_MOBILE,
        S.REAL_TOTAL_AMOUNT AS REAL_AMOUNT,S.TOTAL_AMOUNT,
        S.STATUS,S.ADD_TIME,U.USERNAME AS OPT_USERNAME
        FROM T_SALEORDER S
        LEFT JOIN T_SALEORDER_GOODS G ON S.SALEORDER_ID=G.SALEORDER_ID
        LEFT JOIN T_USER U ON S.USER_ID=U.USER_ID
        LEFT JOIN T_QUOTEORDER Q ON S.QUOTEORDER_ID=Q.QUOTEORDER_ID
        WHERE 1=1 AND S.ORDER_TYPE =1 AND S.STATUS IN (0,1) AND (S.QUOTEORDER_ID IS NULL OR S.QUOTEORDER_ID=0 OR Q.BUSSINESS_CHANCE_ID =0)
        <if test="order.saleorderNo!=null and order.saleorderNo!=''">
            AND S.SALEORDER_NO=#{order.saleorderNo}
        </if>
        <if test="order.traderId!=null">
            AND S.TRADER_ID=#{order.traderId}
        </if>
        <if test="order.traderName!=null and order.traderName!=''">
            AND S.TRADER_NAME like CONCAT('%',#{order.traderName},'%')
        </if>
        <if test="order.traderContactName!=null and order.traderContactName!=''">
            AND S.TRADER_CONTACT_NAME =#{order.traderContactName}
        </if>
        <if test="order.traderContactMobile!=null and order.traderContactMobile!=''">
            AND S.TRADER_CONTACT_MOBILE=#{order.traderContactMobile}
        </if>
        <if test="order.productName!=null and order.productName!=''">
            AND G.GOODS_NAME LIKE CONCAT('%',#{order.productName},'%')
        </if>
        <if test="order.skuNo!=null and order.skuNo!=''">
            AND G.SKU LIKE CONCAT('%',#{order.skuNo},'%')
        </if>
        <if test="order.status!=null and order.status != -1">
            AND S.STATUS =#{order.status}
        </if>
        <if test="order.beginTime!=null">
            AND S.ADD_TIME <![CDATA[ >= ]]> #{order.beginTime}
        </if>
        <if test="order.beginTime!=null">
            AND S.ADD_TIME <![CDATA[ <= ]]> #{order.beginTime}
        </if>
        <if test="order.saleUserList != null and order.saleUserList.size() > 0">
            and S.USER_ID in
            <foreach collection="order.saleUserList" item="list" separator="," open="(" close=")">
                #{list.userId,jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY S.SALEORDER_ID
    </select>
    <select id="getNoPayBackAmount" resultType="java.math.BigDecimal">
		SELECT COALESCE(
		            SUM(ABS(A.AMOUNT))
		          - IFNULL(C.HK_AMOUNT, 0)
		          - IFNULL(D.HK_AMOUNT, 0),
		          0)
		          AS RELATEDDECIMAL,
		       B.RELATED_ID AS RELATEDID
		FROM T_CAPITAL_BILL A
		     LEFT JOIN T_CAPITAL_BILL_DETAIL B
		        ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
		     LEFT JOIN
		     (SELECT COALESCE(SUM(ABS(B1.AMOUNT)), 0) AS HK_AMOUNT, B1.RELATED_ID
		      FROM T_CAPITAL_BILL A1
		           LEFT JOIN T_CAPITAL_BILL_DETAIL B1
		              ON A1.CAPITAL_BILL_ID = B1.CAPITAL_BILL_ID
		      WHERE     A1.TRADER_TYPE in (1,4)
		            AND B1.ORDER_TYPE = 1
		            AND B1.BUSSINESS_TYPE = 533
		      GROUP BY B1.RELATED_ID) AS C
		        ON B.RELATED_ID = C.RELATED_ID
		     LEFT JOIN
		     (SELECT COALESCE(SUM(ABS(B1.AMOUNT)), 0) AS HK_AMOUNT, C1.ORDER_ID
		      FROM T_CAPITAL_BILL A1
		           LEFT JOIN T_CAPITAL_BILL_DETAIL B1
		              ON A1.CAPITAL_BILL_ID = B1.CAPITAL_BILL_ID
		           LEFT JOIN T_AFTER_SALES C1
		              ON C1.AFTER_SALES_ID = B1.RELATED_ID AND C1.SUBJECT_TYPE = 535
		      WHERE     A1.TRADER_TYPE = 3
		            AND B1.ORDER_TYPE = 3
		            AND B1.BUSSINESS_TYPE = 533
		      GROUP BY C1.ORDER_ID) AS D
		        ON D.ORDER_ID = B.RELATED_ID
		WHERE     A.TRADER_TYPE = 3
		      AND A.TRADER_MODE = 527
		      AND B.ORDER_TYPE = 1
		      AND B.RELATED_ID = #{orderId,jdbcType=INTEGER}
		GROUP BY B.RELATED_ID
	</select>
	<select id="getManageCategoryOfSkuBySaleorderId" resultType="java.lang.Integer">
		SELECT DISTINCT
			R.MANAGE_CATEGORY_LEVEL
		FROM
			T_SALEORDER_GOODS SG
		JOIN V_CORE_SKU SKU ON SG.GOODS_ID = SKU.SKU_ID AND SG.IS_DELETE = 0
		JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
		JOIN T_FIRST_ENGAGE F ON SPU.FIRST_ENGAGE_ID = F.FIRST_ENGAGE_ID
		JOIN T_REGISTRATION_NUMBER R ON F.REGISTRATION_NUMBER_ID = R.REGISTRATION_NUMBER_ID
		WHERE
			SG.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
	<select id="getSaleorderByOrderNo" resultType="com.vedeng.order.model.Saleorder">
		SELECT * FROM T_SALEORDER WHERE SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
	</select>
    <select id="getSaleorderBySaleorderId" resultType="com.vedeng.order.model.Saleorder">
		SELECT * FROM T_SALEORDER WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
    <select id="getSaleorderOfNeedUpdateContract" resultType="java.lang.Integer">
		SELECT DISTINCT
			S.SALEORDER_ID
		FROM
		( SELECT * FROM T_SALEORDER WHERE ORDER_TYPE IN ( 0, 1 ) AND `STATUS` = 1 AND CONTRACT_URL IS NOT NULL AND TRADER_ID = #{traderId,jdbcType=INTEGER} ) S
			LEFT JOIN ( SELECT * FROM T_ATTACHMENT WHERE ATTACHMENT_FUNCTION = 492 ) A ON S.SALEORDER_ID = A.RELATED_ID
		WHERE
			A.ATTACHMENT_ID IS NULL
	</select>
	<select id="getSaleorderIdByModifyApplyId" resultType="java.lang.Integer">
		SELECT SALEORDER_ID FROM T_SALEORDER_MODIFY_APPLY WHERE SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
	</select>

    <select id="getSaleorderModifyApplyList" resultType="com.vedeng.order.model.SaleorderModifyApply">
		SELECT
		b.*, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
		from T_SALEORDER_MODIFY_APPLY b
		LEFT JOIN T_VERIFIES_INFO e ON b.SALEORDER_MODIFY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER_MODIFY_APPLY'
		WHERE b.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
		order by b.ADD_TIME DESC
	</select>

	<select id="getUncheckedSkuCountOfSaleorder" resultType="java.lang.String">
		SELECT
			SG.SKU
		FROM
			T_SALEORDER_GOODS SG
		JOIN V_CORE_SKU SKU ON SG.SKU = SKU.SKU_NO
		WHERE
			SG.IS_DELETE = 0
			AND SKU.CHECK_STATUS != 3 AND SG.SALEORDER_ID = #{saleorderId，jdbcType=INTEGER}
	</select>

	<select id="getOneYearSkuSaleNumListPage" parameterType="java.util.Map" resultType="com.newtask.model.SkuSaleNum">
		select G.SKU AS SKU_NO,SUM(G.NUM-IFNULL(G.AFTER_RETURN_NUM,0)) AS SALE_NUM
		from T_SALEORDER_GOODS G left join T_SALEORDER S
		ON G.SALEORDER_ID=S.SALEORDER_ID
		where G.IS_DELETE=0
		AND S.`STATUS` IN (1,2)
		and S.PAYMENT_STATUS IN (1,2)
		and S.VALID_TIME>UNIX_TIMESTAMP(date_sub(curdate(),interval 1 YEAR))*1000
		GROUP BY G.SKU
	</select>
    <select id="getSalerOfSaleorderByOrderNo" resultType="com.vedeng.order.model.Saleorder">
		SELECT S.SALEORDER_ID, S.ORDER_TYPE, T.USER_ID AS CREATOR FROM T_SALEORDER S JOIN T_R_TRADER_J_USER T on S.TRADER_ID = T.TRADER_ID WHERE
				S.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR} LIMIT 1
	</select>

    <update id="updateHcOrderDefaultValue" parameterType="com.vedeng.order.model.Saleorder">
		update T_SALEORDER
		<set >
			<if test="invoiceSendNode != null" >
				INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=INTEGER},
			</if>
			<if test="deliveryType != null" >
				DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
			</if>
		</set>
		where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>

	<select id="getSaleordergetVerifyStatus" resultType="java.lang.Integer" >
		SELECT b.STATUS
		FROM T_SALEORDER a
		left join T_VERIFIES_INFO b on a.SALEORDER_ID=b.RELATE_TABLE_KEY
		WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER} AND b.VERIFIES_TYPE=623
	</select>
	<select id="getFinishOrderStatus" resultType="com.vedeng.order.model.Saleorder">
		SELECT
			A.SALEORDER_ID,A.SALEORDER_NO
		FROM
			T_SALEORDER A
		WHERE
			A.`STATUS` = #{status,jdbcType=INTEGER}
		  AND A.IS_RISK = #{isRisk,jdbcType=INTEGER}
	</select>

	<select id="getLastWeekIdsByUpdateDateTime" resultType= "java.lang.Integer" >
		SELECT SALEORDER_ID FROM T_SALEORDER WHERE UPDATE_DATA_TIME > #{lastWeek} AND STATUS = 1;
	</select>

	<select id="getAllSaleOrderIds" resultType= "java.lang.Integer" >
		SELECT SALEORDER_ID FROM T_SALEORDER WHERE STATUS = 1;
	</select>

</mapper>

