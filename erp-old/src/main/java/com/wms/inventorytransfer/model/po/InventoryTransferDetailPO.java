package com.wms.inventorytransfer.model.po;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @describe 库存转移单详情
 * @date 2020/7/10 13:35:28
 */
public class InventoryTransferDetailPO implements Serializable {
    /**
     * 库存转移单详情单ID
     */
    private Integer inventoryTransferDetailId;

    /**
     * 库存转移单ID
     */
    private Integer inventoryTransferId;

    /**
     * 商品唯一编码
     */
    private String skuNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 转移数量
     */
    private Integer num;

    /**
     * WMS转移成功数量
     */
    private Integer transferNum;

    /**
     * 来源逻辑仓
     */
    private Integer fromWarehouseId;

    /**
     * 目标逻辑仓
     */
    private Integer toWarehouseId;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 最后一次编辑人
     */
    private Integer updater;

    public Integer getInventoryTransferDetailId() {
        return inventoryTransferDetailId;
    }

    public void setInventoryTransferDetailId(Integer inventoryTransferDetailId) {
        this.inventoryTransferDetailId = inventoryTransferDetailId;
    }

    public Integer getInventoryTransferId() {
        return inventoryTransferId;
    }

    public void setInventoryTransferId(Integer inventoryTransferId) {
        this.inventoryTransferId = inventoryTransferId;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }


    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getFromWarehouseId() {
        return fromWarehouseId;
    }

    public void setFromWarehouseId(Integer fromWarehouseId) {
        this.fromWarehouseId = fromWarehouseId;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Integer getTransferNum() {
        return transferNum;
    }

    public void setTransferNum(Integer transferNum) {
        this.transferNum = transferNum;
    }

    @Override
    public String toString() {
        return "InventoryTransferDetailPO{" +
                "inventoryTransferDetailId=" + inventoryTransferDetailId +
                ", inventoryTransferId=" + inventoryTransferId +
                ", skuNo='" + skuNo + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", num=" + num +
                ", transferNum=" + transferNum +
                ", fromWarehouseId=" + fromWarehouseId +
                ", toWarehouseId=" + toWarehouseId +
                ", addTime=" + addTime +
                ", updateTime=" + updateTime +
                ", creator=" + creator +
                ", updater=" + updater +
                '}';
    }
}
