package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleBackCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSaleReturnStockDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleBackCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleBackConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeSaleReturnStockRepository;
import com.vedeng.erp.kingdee.service.KingDeeSaleReturnStockApiService;
import com.vedeng.erp.kingdee.service.KingDeeSaleReturnStockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KingDeeSaleReturnStockServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeSaleReturnStockEntity,
        KingDeeSaleReturnStockDto,
        KingDeeSaleBackCommand,
        KingDeeSaleReturnStockRepository,
        KingDeeSaleBackConvertor,
        KingDeeSaleBackCommandConvertor
        > implements KingDeeSaleReturnStockService, KingDeeSaleReturnStockApiService {
}
