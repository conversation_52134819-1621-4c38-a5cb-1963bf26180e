<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderFinanceMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderFinanceEntity">
        <!--@mbg.generated-->
        <!--@Table T_TRADER_FINANCE-->
        <id column="TRADER_FINANCE_ID" jdbcType="INTEGER" property="traderFinanceId"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType"/>
        <result column="CREDIT_RATING" jdbcType="INTEGER" property="creditRating"/>
        <result column="REG_ADDRESS" jdbcType="VARCHAR" property="regAddress"/>
        <result column="REG_TEL" jdbcType="VARCHAR" property="regTel"/>
        <result column="TAX_NUM" jdbcType="VARCHAR" property="taxNum"/>
        <result column="AVERAGE_TAXPAYER_DOMAIN" jdbcType="VARCHAR" property="averageTaxpayerDomain"/>
        <result column="AVERAGE_TAXPAYER_URI" jdbcType="VARCHAR" property="averageTaxpayerUri"/>
        <result column="BANK" jdbcType="VARCHAR" property="bank"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="BANK_ACCOUNT" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="OSS_RESOURCE_ID" jdbcType="VARCHAR" property="ossResourceId"/>
        <result column="ORIGINAL_FILEPATH" jdbcType="VARCHAR" property="originalFilepath"/>
        <result column="SYN_SUCCESS" jdbcType="INTEGER" property="synSuccess"/>
        <result column="COST_TIME" jdbcType="BIGINT" property="costTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        TRADER_FINANCE_ID,
        TRADER_ID,
        TRADER_TYPE,
        CREDIT_RATING,
        REG_ADDRESS,
        REG_TEL,
        TAX_NUM,
        AVERAGE_TAXPAYER_DOMAIN,
        AVERAGE_TAXPAYER_URI,
        BANK,
        BANK_CODE,
        BANK_ACCOUNT,
        COMMENTS,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        OSS_RESOURCE_ID,
        ORIGINAL_FILEPATH,
        SYN_SUCCESS,
        COST_TIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_FINANCE
        where TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_TRADER_FINANCE
        where TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="TRADER_FINANCE_ID" keyProperty="traderFinanceId"
            parameterType="com.vedeng.erp.trader.domain.entity.TraderFinanceEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TRADER_FINANCE (TRADER_ID, TRADER_TYPE, CREDIT_RATING,
                                      REG_ADDRESS, REG_TEL, TAX_NUM,
                                      AVERAGE_TAXPAYER_DOMAIN, AVERAGE_TAXPAYER_URI,
                                      BANK, BANK_CODE, BANK_ACCOUNT,
                                      COMMENTS, ADD_TIME, CREATOR,
                                      MOD_TIME, UPDATER, OSS_RESOURCE_ID,
                                      ORIGINAL_FILEPATH, SYN_SUCCESS, COST_TIME)
        values (#{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=INTEGER}, #{creditRating,jdbcType=INTEGER},
                #{regAddress,jdbcType=VARCHAR}, #{regTel,jdbcType=VARCHAR}, #{taxNum,jdbcType=VARCHAR},
                #{averageTaxpayerDomain,jdbcType=VARCHAR}, #{averageTaxpayerUri,jdbcType=VARCHAR},
                #{bank,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, #{bankAccount,jdbcType=VARCHAR},
                #{comments,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
                #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{ossResourceId,jdbcType=VARCHAR},
                #{originalFilepath,jdbcType=VARCHAR}, #{synSuccess,jdbcType=INTEGER}, #{costTime,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="TRADER_FINANCE_ID" keyProperty="traderFinanceId"
            parameterType="com.vedeng.erp.trader.domain.entity.TraderFinanceEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_TRADER_FINANCE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="traderType != null">
                TRADER_TYPE,
            </if>
            <if test="creditRating != null">
                CREDIT_RATING,
            </if>
            <if test="regAddress != null">
                REG_ADDRESS,
            </if>
            <if test="regTel != null">
                REG_TEL,
            </if>
            <if test="taxNum != null">
                TAX_NUM,
            </if>
            <if test="averageTaxpayerDomain != null">
                AVERAGE_TAXPAYER_DOMAIN,
            </if>
            <if test="averageTaxpayerUri != null">
                AVERAGE_TAXPAYER_URI,
            </if>
            <if test="bank != null">
                BANK,
            </if>
            <if test="bankCode != null">
                BANK_CODE,
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="ossResourceId != null">
                OSS_RESOURCE_ID,
            </if>
            <if test="originalFilepath != null">
                ORIGINAL_FILEPATH,
            </if>
            <if test="synSuccess != null">
                SYN_SUCCESS,
            </if>
            <if test="costTime != null">
                COST_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null">
                #{traderType,jdbcType=INTEGER},
            </if>
            <if test="creditRating != null">
                #{creditRating,jdbcType=INTEGER},
            </if>
            <if test="regAddress != null">
                #{regAddress,jdbcType=VARCHAR},
            </if>
            <if test="regTel != null">
                #{regTel,jdbcType=VARCHAR},
            </if>
            <if test="taxNum != null">
                #{taxNum,jdbcType=VARCHAR},
            </if>
            <if test="averageTaxpayerDomain != null">
                #{averageTaxpayerDomain,jdbcType=VARCHAR},
            </if>
            <if test="averageTaxpayerUri != null">
                #{averageTaxpayerUri,jdbcType=VARCHAR},
            </if>
            <if test="bank != null">
                #{bank,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="ossResourceId != null">
                #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null">
                #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="synSuccess != null">
                #{synSuccess,jdbcType=INTEGER},
            </if>
            <if test="costTime != null">
                #{costTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderFinanceEntity">
        <!--@mbg.generated-->
        update T_TRADER_FINANCE
        <set>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null">
                TRADER_TYPE = #{traderType,jdbcType=INTEGER},
            </if>
            <if test="creditRating != null">
                CREDIT_RATING = #{creditRating,jdbcType=INTEGER},
            </if>
            <if test="regAddress != null">
                REG_ADDRESS = #{regAddress,jdbcType=VARCHAR},
            </if>
            <if test="regTel != null">
                REG_TEL = #{regTel,jdbcType=VARCHAR},
            </if>
            <if test="taxNum != null">
                TAX_NUM = #{taxNum,jdbcType=VARCHAR},
            </if>
            <if test="averageTaxpayerDomain != null">
                AVERAGE_TAXPAYER_DOMAIN = #{averageTaxpayerDomain,jdbcType=VARCHAR},
            </if>
            <if test="averageTaxpayerUri != null">
                AVERAGE_TAXPAYER_URI = #{averageTaxpayerUri,jdbcType=VARCHAR},
            </if>
            <if test="bank != null">
                BANK = #{bank,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="ossResourceId != null">
                OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null">
                ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="synSuccess != null">
                SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
            </if>
            <if test="costTime != null">
                COST_TIME = #{costTime,jdbcType=BIGINT},
            </if>
        </set>
        where TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderFinanceEntity">
        <!--@mbg.generated-->
        update T_TRADER_FINANCE
        set TRADER_ID               = #{traderId,jdbcType=INTEGER},
            TRADER_TYPE             = #{traderType,jdbcType=INTEGER},
            CREDIT_RATING           = #{creditRating,jdbcType=INTEGER},
            REG_ADDRESS             = #{regAddress,jdbcType=VARCHAR},
            REG_TEL                 = #{regTel,jdbcType=VARCHAR},
            TAX_NUM                 = #{taxNum,jdbcType=VARCHAR},
            AVERAGE_TAXPAYER_DOMAIN = #{averageTaxpayerDomain,jdbcType=VARCHAR},
            AVERAGE_TAXPAYER_URI    = #{averageTaxpayerUri,jdbcType=VARCHAR},
            BANK                    = #{bank,jdbcType=VARCHAR},
            BANK_CODE               = #{bankCode,jdbcType=VARCHAR},
            BANK_ACCOUNT            = #{bankAccount,jdbcType=VARCHAR},
            COMMENTS                = #{comments,jdbcType=VARCHAR},
            ADD_TIME                = #{addTime,jdbcType=BIGINT},
            CREATOR                 = #{creator,jdbcType=INTEGER},
            MOD_TIME                = #{modTime,jdbcType=BIGINT},
            UPDATER                 = #{updater,jdbcType=INTEGER},
            OSS_RESOURCE_ID         = #{ossResourceId,jdbcType=VARCHAR},
            ORIGINAL_FILEPATH       = #{originalFilepath,jdbcType=VARCHAR},
            SYN_SUCCESS             = #{synSuccess,jdbcType=INTEGER},
            COST_TIME               = #{costTime,jdbcType=BIGINT}
        where TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
    </update>

    <select id="selectCustomerFiance" resultType="com.vedeng.erp.trader.dto.TraderFinanceDto">
        select tf.TRADER_FINANCE_ID,
               tf.TRADER_ID,
               tf.TRADER_TYPE,
               tf.CREDIT_RATING,
               tf.REG_ADDRESS,
               tf.REG_TEL,
               tf.TAX_NUM,
               tf.AVERAGE_TAXPAYER_DOMAIN,
               tf.AVERAGE_TAXPAYER_URI,
               tf.BANK,
               tf.BANK_CODE,
               tf.BANK_ACCOUNT,
               tf.COMMENTS,
               tf.ADD_TIME,
               tf.CREATOR,
               tf.MOD_TIME,
               tf.UPDATER,
               t.AMOUNT
        <!--               ,v.STATUS as CHECK_STATUS-->
        from T_TRADER_FINANCE tf
                 left join
             T_TRADER_CUSTOMER t
             on tf.TRADER_ID = t.TRADER_ID
        <!--                 left join-->
        <!--             T_VERIFIES_INFO v-->
        <!--             ON v.RELATE_TABLE_KEY = tf.TRADER_FINANCE_ID-->
        <!--                 AND v.relate_table = 'T_TRADER_FINANCE'-->
        where 1 = 1
          and tf.TRADER_ID = #{traderId,jdbcType=INTEGER}
          and t.TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
          and tf.TRADER_TYPE = 1
        <!--          and v.STATUS = 1-->
        order by tf.ADD_TIME desc
        limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-14-->
    <select id="selectByTraderIdAndTraderType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_FINANCE
        where TRADER_ID=#{traderId,jdbcType=INTEGER}
        and TRADER_TYPE=#{traderType,jdbcType=INTEGER}
    </select>

    <select id="findByTraderIdAndTraderType" resultType="com.vedeng.erp.trader.dto.TraderFinanceDto">
        select TTF.TRADER_FINANCE_ID,
        TTF.TRADER_ID,
        TTF.TRADER_TYPE,
        TTF.CREDIT_RATING,
        TTF.REG_ADDRESS,
        TTF.REG_TEL,
        TTF.TAX_NUM,
        TTF.AVERAGE_TAXPAYER_DOMAIN,
        TTF.AVERAGE_TAXPAYER_URI,
        TTF.BANK,
        TTF.BANK_CODE,
        TTF.BANK_ACCOUNT,
        TTF.COMMENTS,
        TTF.ADD_TIME,
        TTF.CREATOR,
        TTF.MOD_TIME,
        TTF.UPDATER,
        TTF.OSS_RESOURCE_ID,
        TTF.ORIGINAL_FILEPATH,
        TTF.SYN_SUCCESS,
        TTF.COST_TIME,
        TT.TRADER_NAME traderName

        from T_TRADER_FINANCE TTF
        left join T_TRADER TT on TTF.TRADER_ID = TT.TRADER_ID
        where TTF.TRADER_ID = #{traderId,jdbcType=INTEGER}
        and TTF.TRADER_TYPE = #{traderType,jdbcType=BOOLEAN}
    </select>
</mapper>