package com.vedeng.erp.saleorder.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活动预处理订单。后续可转真实订单
 * @date 2022/12/22 13:48
 */
@Getter
@Setter
public class ActivityPreOrderEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer activityPreOrderId;

    /**
     * 转换后的订单id
     */
    private Integer saleorderId;

    /**
     * 编号
     */
    private String orderNo;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 活动id
     */
    private Integer activeId;


    /**
     * 联系人姓名
     */
    private String contactPerson;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 归属销售id
     */
    private String belongSalesId;

    /**
     * 归属销售
     */
    private String belongSales;

    /**
     * 订单状态  1进行中（未创建订单&未关闭）、2已建单（已关联订单）、3已成单【已创建订单&（关联的订单已经部分收款|全部收款）】、4已关闭（商机关闭）
     */
    private Integer orderStatus;

    /**
     * 活动限额数
     */
    private Integer quota;

    /**
     * 活动标签 1：秒杀
     */
    private Integer tag;

    /**
     * 备注
     */
    private String remark;

}