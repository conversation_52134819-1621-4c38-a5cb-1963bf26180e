package com.vedeng.erp.kingdee.enums;

/**
 * 流水忽略原因与金蝶编码匹配
 * <AUTHOR>
 */
public enum KingdeeIgnoreBillTypeEnums {

    SALE_RECEIVE("销售收款","HL033",0,"客户收款","BD_Customer"),
    CURRENT_ACCOUNTS_VEDENG("往来款（贝登—贝登）","HL033",4287,"银行代收款","BD_BANK"),
    CURRENT_ACCOUNTS_VEDENG_SON("往来款（贝登—子公司）","HL002",4288,"银行代收款","BD_BANK"),
    AFTER_SALE_REPAIRE("售后-客户维修款","HL003",4289,"客户收款","BD_Customer"),
    AFTER_BUYORDER_BACK("售后-供应商退款","HL004",4290,"供应商款","BD_Supplier"),
    BUYORDER_PAY("支付供应商货款","HL005",4291,"供应商款","BD_Supplier"),
    BUYORDER_EXPENSE_PAY("供应商费用款","HL006",4292,"供应商款","BD_Supplier"),
    BUYORDER_INDEMNIFY("收到供应商赔款","HL007",4293,"供应商款","BD_Supplier"),
    SALEORDER_PAY("客户货款","HL008",4294,"客户收款","BD_Customer"),
    SALEORDER_INDEMNIFY("收到客户赔款","HL009",4295,"客户收款","BD_Customer"),
    ALI_TENCENT_RECHARGE("阿里云、腾讯云等充值","HL010",4296,"银行代收款","BD_BANK"),
    SERVICE_MESSAGE_PAY("手续费、短信服务费","HL011",4297,"银行代收款","BD_BANK"),
    ALI_RECHARGE("支付宝提现、充值","HL012",4298,"银行代收款","BD_BANK"),
    WECHAT_RECHARGE("微信提现、充值","HL013",4299,"银行代收款","BD_BANK"),
    TRADER_GRADUATE_TRADER("客户保证金、押金","HL015",4300,"客户收款","BD_Customer"),
    BUY_DEPOSIT("购买结构性存款","HL016",4302,"银行代收款","BD_BANK"),
    RESEARCH_EXPENSE("研发费用付款","HL017",4303,"银行代收款","BD_BANK"),
    JD_EXPENSE("京东费用款","HL018",4304,"银行代收款","BD_BANK"),
    STOCK_RIGHT_EXPENSE("股权转让款","HL019",4305,"银行代收款","BD_BANK"),
    PETTY_CASH("备用金（取现）","HL020",4306,"银行代收款","BD_BANK"),
    PAY_ATTESTATION_EXPENSE("支付认证款","HL021",4307,"银行代收款","BD_BANK"),
    RECEIVE_ATTESTATION_EXPENSE("收到认证款","HL022",4308,"银行代收款","BD_BANK"),
    INTEREST_RECEIVE("利息收入","HL023",4309,"银行代收款","BD_BANK"),
    INVEST_RECEIVE("投资收益","HL024",4310,"银行代收款","BD_BANK"),
    EXPRESS_REPAIRE("物流赔款","HL025",4311,"银行代收款","BD_BANK"),
    COMMUNICATION_EXPENSE("通讯费","HL026",4312,"银行代收款","BD_BANK"),
    ACCUMULATION_FUND("公积金","HL027",4313,"银行代收款","BD_BANK"),
    SOCIAL_SECURITY("社保","HL028",4314,"银行代收款","BD_BANK"),
    SUBSIDY("补贴","HL029",4315,"银行代收款","BD_BANK"),
    SUBMIT_TAX("交税","HL030",4316,"银行代收款","BD_BANK"),
    BACK_TAX("退税","HL031",4317,"银行代收款","BD_BANK"),
    OTHER("其他","HL032",4318,"银行代收款","BD_BANK"),
    SUPPLIER_GRADUATE_SUPPLIER("供应商保证金、押金","HL014",4326,"供应商款","BD_Supplier"),
    PAY_SUPPLIER_INDEMNITY("支付供应商赔款","HL038",4440,"供应商款","BD_Supplier"),
    PAY_CUSTOMER_INDEMNITY("支付客户赔款","HL039",4441,"客户收款","BD_Customer"),
    REDEEM_STRUCTURE_DEPOSIT("赎回结构性存款","HL040",4442,"银行代收款","BD_BANK"),
    PROVIDE_BORROWING("提供借款","HL041",4443,"银行代收款","BD_BANK"),
    WITHDRAW_BORROWING("收回借款","HL042",4444,"银行代收款","BD_BANK"),
    BUY_REGULAR_MONEY("购买定期存款","HL043",4446,"银行代收款","BD_BANK"),
    REDEEM_REGULAR_MONEY("赎回定期存款","HL044",4447,"银行代收款","BD_BANK"),

    ;
    /**
     * 忽略原因
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 字典表id
     */
    private Integer sysId;
    /**
     * 单位类型
     */
    private String unitType;
    /**
     * 单位类型编码
     */
    private String unitCode;

    public String getCode() {
        return code;
    }

    public Integer getSysId() {
        return sysId;
    }

    KingdeeIgnoreBillTypeEnums(String name, String code, Integer sysId,String unitType,String unitCode){
        this.name = name;
        this.code = code;
        this.sysId = sysId;
        this.unitType = unitType;
        this.unitCode = unitCode;
    }

    public static String matchCodeById(Integer sysId){
        for(KingdeeIgnoreBillTypeEnums kingdeeIgnoreBillTypeEnums : KingdeeIgnoreBillTypeEnums.values()){
            if(kingdeeIgnoreBillTypeEnums.getSysId().equals(sysId)){
                return kingdeeIgnoreBillTypeEnums.code;
            }
        }
        throw new IllegalArgumentException("忽略项目无code匹配");
    }

    public static String matchUnitCodeById(Integer sysId){
        for(KingdeeIgnoreBillTypeEnums kingdeeIgnoreBillTypeEnums : KingdeeIgnoreBillTypeEnums.values()){
            if(kingdeeIgnoreBillTypeEnums.getSysId().equals(sysId)){
                return kingdeeIgnoreBillTypeEnums.unitCode;
            }
        }
        throw new IllegalArgumentException("忽略项目无unit类型匹配");
    }

    public static Integer matchUnitTypeById(Integer sysId){
        String unitType = matchUnitCodeById(sysId);
        switch (unitType){
            case "BD_Customer":
                return 1;
            case "BD_Supplier":
                return 2;
            case "BD_BANK":
                return 4;
            default:
                return 3;
        }
    }
}
