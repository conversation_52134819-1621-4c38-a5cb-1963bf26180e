package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveRefundBillCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveRefundBillCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveRefundBillConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeReceiveBillRepository;
import com.vedeng.erp.kingdee.repository.KingDeeReceiveRefundBillRepository;
import com.vedeng.erp.kingdee.service.KingDeeReceiveRefundBillApiService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveRefundBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Service
@Slf4j
public class KingDeeReceiveRefundBillServiceImpl extends KingDeeBaseServiceImpl<KingDeeReceiveRefundBill
        , KingDeeReceiveRefundBillDto
        , KingDeeReceiveRefundBillCommand
        , KingDeeReceiveRefundBillRepository
        , KingDeeReceiveRefundBillConvertor
        , KingDeeReceiveRefundBillCommandConvertor> implements KingDeeReceiveRefundBillService, KingDeeReceiveRefundBillApiService {

    @Value("${payIsAutoSubmitAndAudit}")
    public Integer payIsAutoSubmitAndAudit;

    @Override
    public boolean getIsAutoSubmitAndAudit(KingDeeReceiveRefundBillDto kingDeeReceiveRefundBillDto){
        return Objects.equals(payIsAutoSubmitAndAudit, 1);
    }
}
