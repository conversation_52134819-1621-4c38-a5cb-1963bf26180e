package com.vedeng.crm.visitrecord.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordLogVo;
import com.vedeng.crm.visitrecord.enums.VisitOperationTypeEnum;
import com.vedeng.erp.system.dto.OperationLogDto;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/3
 */
public interface CrmVisitRecordLogService {


    /**
     * 记录操作日志
     */
    void addOperationLog(Integer recordId, Integer operationType, String content, CurrentUser currentUser);


    void addOperationLog(Integer recordId, VisitOperationTypeEnum operationType, String content, CurrentUser currentUser);

    void addOperationLog(Integer recordId, VisitOperationTypeEnum operationType, CurrentUser currentUser);
    /**
     * 查询操作日志
     */
    PageInfo<VisitRecordLogVo> getOperationLogs(PageParam<Integer> pageParam);

    PageInfo<OperationLogDto> selectPageByVisitRecordId(PageParam<Integer> pageParam);
}
