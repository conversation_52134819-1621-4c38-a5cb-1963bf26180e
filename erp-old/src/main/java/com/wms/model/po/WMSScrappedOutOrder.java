package com.wms.model.po;

public class WMSScrappedOutOrder extends WmsOutputOrder{

    private Integer outputNum;

    private String skuName;

    private String skuNo;

    private String model;

    private String unitName;

    private String brandName;

    private Integer stockNum;

    private Integer countGoods;

    private String scrapDealTypeStr;

    private String scrapLevelStr;

    private String scrapTypeStr;

    private String outStatusStr;

    public String getOutStatusStr() {
        return outStatusStr;
    }

    public void setOutStatusStr(String outStatusStr) {
        this.outStatusStr = outStatusStr;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getScrapDealTypeStr() {
        return scrapDealTypeStr;
    }

    public void setScrapDealTypeStr(String scrapDealTypeStr) {
        this.scrapDealTypeStr = scrapDealTypeStr;
    }

    public String getScrapLevelStr() {
        return scrapLevelStr;
    }

    public void setScrapLevelStr(String scrapLevelStr) {
        this.scrapLevelStr = scrapLevelStr;
    }

    public String getScrapTypeStr() {
        return scrapTypeStr;
    }

    public void setScrapTypeStr(String scrapTypeStr) {
        this.scrapTypeStr = scrapTypeStr;
    }

    public Integer getCountGoods() {
        return countGoods;
    }

    public void setCountGoods(Integer countGoods) {
        this.countGoods = countGoods;
    }

    public Integer getOutputNum() {
        return outputNum;
    }

    public void setOutputNum(Integer outputNum) {
        this.outputNum = outputNum;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getStockNum() {
        return stockNum;
    }

    public void setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
    }

}
