<%--
  Created by IntelliJ IDEA.
  User: Simple
  Date: 2021/10/12
  Time: 17:44
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="订单编辑" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<%@ include file="../../component/remarkComponent.jsp" %>
<link rel="stylesheet" href="<%=basePath%>static/css/select2.css?rnd=${resourceVersionKey}"/>
<script type="text/javascript" src='<%= basePath %>static/js/select2.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='<%= basePath %>static/js/select2_locale_zh-CN.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='<%= basePath %>static/js/order/saleorder/saleorder_common.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/customer/search_customer_list.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">
    $(function () {
        var saleorderId = $("input[name='saleorderId']").val();

        //补订单产品详情相关数据
        $.ajax({
            async: true,
            url: page_url + '/order/saleorder/getsaleordergoodsextrainfo.do',
            data: {"saleorderId": saleorderId, "extraType": "order_saleorder"},//销售订单详情（占用，库存，采购状态，到库状态，发货状态，收货状态）
            type: "POST",
            dataType: "json",
            success: function (data) {
                if (data.code == 0) {
                    for (var i = 0; i < data.data.length; i++) {
                        //alert(data.data[i].saleorderGoodsId);
                        $("#orderOccupy_stockNum_" + data.data[i].saleorderGoodsId).html(data.data[i].goods.orderOccupy + "/" + data.data[i].goods.stockNum);
                        $("#kc_" + data.data[i].saleorderGoodsId).html(data.data[i].goods.stockNum);
                        $("#kykc_" + data.data[i].saleorderGoodsId).html(data.data[i].goods.stockNum - data.data[i].goods.orderOccupy);
                        $("#dzzy_" + data.data[i].saleorderGoodsId).html(data.data[i].goods.orderOccupy);
                        $("#ktj_" + data.data[i].saleorderGoodsId).html(data.data[i].goods.adjustableNum);
                    }
                } else {
                    layer.alert(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    })


    function validateOrderCouponInfo(id) {

        if (${orderType != '1'}) {
            return $("#" + id).click();
        }

        if ("${saleorder.isCoupons}" == "1") {

            if (id.startsWith("editSaleOrderGood")) {
                return $("#" + id).click();
            } else {
                layer.alert("客户已选择优惠券，无法更改产品信息！");
                return;
            }
        }

        var hasCoupon = false;
        var hasAuthority = true;

        $.ajax({
            url: page_url + '/orderstream/saleorder/orderHasCouponInfo.do',
            data: {"saleOrderId": "${saleorder.saleorderId}"},
            type: "POST",
            dataType: "json",
            async: false,
            success: function (data) {
                if (data.data == true) {
                    hasCoupon = true;
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    hasAuthority = false;
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

        if (!hasAuthority) {
            return;
        }

        if (!hasCoupon) {
            return $("#" + id).click();
        }

        //有优惠券
        layer.confirm("已使用优惠券，继续操作将清空已选择的优惠券？", {
            btn: ['确定', '取消']
            //按钮
        }, function () {
            //清空优惠券
            $.ajax({
                url: page_url + '/order/saleorder/clearCoupon.do',
                data: {"saleOrderId": "${saleorder.saleorderId}"},
                type: "POST",
                dataType: "json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    window.location.reload();
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });

        }, function () {

        });
    }

    //选择优惠券
    function chooseCoupon() {

        //如果是BD订单且已经使用了优惠券的 就不让选择优惠券
        if (${orderType == '1' && saleorder.isCoupons == '1'}) {
            layer.alert("客户已选择优惠券，无法更改优惠券信息！");
            return;
        }


        var trs = $("#goodsTbody tr");
        if (trs.length < 2) {
            layer.alert("订单没有对应的产品信息，请添加产品后重试");
            return;
        }

        var skuNos = "";
        for (var i = 0; i <= trs.length - 2; i++) {
            skuNos += trs.eq(i).find("td input:first").val();
            if (i != trs.length - 2) {
                skuNos += ",";
            }
        }


        $("#chooseCouponSpan").attr('layerparams', '{"width":"700px","height":"550px","title":"选择优惠券","link":"/order/saleorder/chooseCoupon.do?saleorderId=${saleorder.saleorderId}&skuNos=' + skuNos + '"}');
        $("#chooseCouponSpan").click();
    }

    /**
     * 查看客户所有额优惠券
     */
    function viewCustomAllCoupons() {
        $("#myCouponSpan").click();
    }

    /**
     * 选择优惠券的回调函数
     * @param couponId
     * @param denomination
     * @param useThreshold
     * @param limitTypeStr
     * @param effevtiveEndTime
     */
    function selectCoupon(couponInfo) {

        $.ajax({
            url: page_url + '/order/saleorder/selectedCoupon.do',
            data: {"saleOrderId": "${saleorder.saleorderId}", "couponInfoStr": JSON.stringify(couponInfo)},
            type: "POST",
            dataType: "json",
            async: false,
            success: function (data) {
                if (data.code == 0) {
                    window.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

    }

    $(function () {
        //联系人下拉搜索初始化
        $(".contact").select2();
        // 联系地址下拉搜索初始化
        $(".address").select2();
    });

</script>
<style>
    .select2-container .select2-choice .select2-arrow {
        background: white;
    }
</style>
<div class="content mt10 ">
    <!-- 这一块内容是页头进度条 -->
    <div style="height: 60px;text-align: center">
        <div class="t-line-wrap J-line-wrap"
             data-json='[{"label":"待确认","status":1},{"label":"待审核","status":2},{"label":"待收款","status":3},{"label":"待发货"},{"label":"待收货"},{"label":"待开票"},{"label":"已完结"}]'></div>
    </div>
    <!-- end -->
    <!-- 这一块内容是测试用的模块，只需要绑定id -->
    <%--    <div class="block">模块1</div>--%>
    <%--    <div class="block">模块2</div>--%>
    <%--    <div class="block" id="J-test">模块3</div>--%>
    <%--    <div class="block">模块4</div>--%>
    <%--    <div class="block">模块5</div>--%>
    <!-- end -->
    <c:set var="orderType" value="${null == orderType ? -1 : orderType}"></c:set>
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">基本信息</div>
            <input type="hidden" id="is_scientificDept" value="${isScientificDept}">
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">订单号</td>
                <td>${saleorder.saleorderNo}</td>
                <td class="table-smaller">订单状态</td>
                <td>
                    <c:choose>
                        <c:when test="${saleorder.status eq 0}">待确认</c:when>
                        <c:when test="${saleorder.status eq 1}">进行中</c:when>
                        <c:when test="${saleorder.status eq 2}">已完结</c:when>
                        <c:when test="${saleorder.status eq 3}">已关闭</c:when>
                        <c:otherwise></c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>创建者</td>
                <td>${saleorder.creatorName}</td>
                <td>创建时间</td>
                <td><date:date value="${saleorder.addTime}"/></td>
            </tr>
            <tr>
                <td>销售部门</td>
                <td>${saleorder.salesDeptName}</td>
                <td>归属销售</td>
                <td>${saleorder.optUserName}</td>
            </tr>
            <%--            <tr>--%>
            <%--                <td>生效状态</td>--%>
            <%--                <td>--%>
            <%--                    <c:choose>--%>
            <%--                        <c:when test="${saleorder.validStatus eq 0}">未生效</c:when>--%>
            <%--                        <c:when test="${saleorder.validStatus eq 1}">已生效</c:when>--%>
            <%--                        <c:otherwise></c:otherwise>--%>
            <%--                    </c:choose>--%>
            <%--                </td>--%>
            <%--                <td>生效时间</td>--%>
            <%--                <td><date:date value ="${list.validTime}"/></td>--%>
            <%--            </tr>--%>
            <%--            <tr>--%>
            <%--                <td>审核状态</td>--%>
            <%--                <td>--%>
            <%--                    <c:choose>--%>
            <%--                        <c:when test="${saleorder.verifyStatus == null}">待审核</c:when>--%>
            <%--                        <c:when test="${saleorder.verifyStatus eq 0}">审核中</c:when>--%>
            <%--                        <c:when test="${saleorder.verifyStatus eq 1}">审核通过</c:when>--%>
            <%--                        <c:when test="${saleorder.verifyStatus eq 2}">审核不通过</c:when>--%>
            <%--                        <c:otherwise></c:otherwise>--%>
            <%--                    </c:choose>--%>
            <%--                </td>--%>
            <%--                <td>锁定状态</td>--%>
            <%--                <td>--%>
            <%--                    <c:choose>--%>
            <%--                        <c:when test="${saleorder.lockedStatus eq 0}">未锁定</c:when>--%>
            <%--                        <c:when test="${saleorder.lockedStatus eq 1}">已锁定</c:when>--%>
            <%--                        <c:otherwise></c:otherwise>--%>
            <%--                    </c:choose>--%>
            <%--                </td>--%>
            <%--            </tr>--%>
            <%--            <tr>--%>
            <%--                <td>商机编号</td>--%>
            <%--                <td>--%>
            <%--                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${saleorder.bussinessChanceId}","link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${saleorder.bussinessChanceId}","title":"商机信息"}'>${saleorder.bussinessChanceNo}</a>--%>
            <%--                </td>--%>
            <%--                <td>报价单号</td>--%>
            <%--                <td>--%>
            <%--                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewquoteorder${saleorder.quoteorderId}","link":"./order/quote/getQuoteDetail.do?quoteorderId=${saleorder.quoteorderId}&viewType=2","title":"报价信息"}'>${saleorder.quoteorderNo}</a>--%>
            <%--                </td>--%>
            <%--            </tr>--%>
            </tbody>
        </table>
    </div>
    <form action="${pageContext.request.contextPath}/order/saleorder/saveeditsaleorderinfo.do" method="post"
          id="editForm">
        <input type="hidden" value="${orderType}" id="orderType" name="orderType">
        <input type="hidden" value="${saleorder.ownerUserId}" name="ownerUserId" id="ownerUserId"/>

        <div class="parts content1 ">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">客户信息</div>
            </div>
            <%--                <div class="formtitle mt10">客户信息</div>--%>
            <div style="border: 1px solid #ddd;">

                <ul class="payplan visible" style="padding-top:10px">
                    <li class="visible">
                        <div class="infor_name infor_name72 mt0">
                            <span>*</span>
                            <label>客户名称</label>
                        </div>
                        <div class="f_left  customername pos_rel">
                                <span class="font-blue">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
                                                "link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}&traderId=${saleorder.traderId}",
                                                "title":"客户信息"}'>
                                        ${customer.traderName}
                                    </a>
                                </span>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name72 mt0">
                            <span></span>
                            <label>客户类型</label>
                        </div>
                        <div class="f_left">
                            <label>${saleorder.customerTypeStr} ${saleorder.customerNatureStr}</label>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name infor_name72">
                            <span>*</span>
                            <label>联系人</label>
                        </div>
                        <div class="f_left ">
                            <select class="input-xx mr10 contact" name="traderContactId" id="contact_3">
                                <option value="0">请选择</option>
                                <c:if test="${not empty traderContactList}">
                                    <c:forEach items="${traderContactList}" var="list" varStatus="status">
                                        <option value="${list.traderContactId}"
                                                <c:if test="${list.traderContactId eq saleorder.traderContactId}">selected="selected"</c:if>>${list.name}/${list.telephone}/${list.mobile}</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <input type="hidden" name="traderContactName">
                            <input type="hidden" name="traderContactTelephone">
                            <input type="hidden" name="traderContactMobile">
                            <span class="mt4 pop-new-data font-blue"
                                  layerParams='{"width":"430px","height":"220px","title":"添加联系人","link":"./addContact.do?traderId=${saleorder.traderId}&traderCustomerId=${customer.traderCustomerId}&indexId=3"}'>添加联系人</span>
                            <div id="traderContactIdMsg" style="clear:both"></div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name72">
                            <span>*</span>
                            <label>联系地址</label>
                        </div>
                        <div class="f_left">
                            <div>
                                <select class="input-xx mr10 address" name="traderAddressId" id="address_3">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty traderAddressList}">
                                        <c:forEach items="${traderAddressList}" var="list" varStatus="status">
                                            <option value="${list.traderAddress.traderAddressId}"
                                                    <c:if test="${list.traderAddress.traderAddressId eq saleorder.traderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <input type="hidden" name="traderArea">
                                <input type="hidden" name="traderAddress">
                                <span class="mt4 pop-new-data font-blue"
                                      layerParams='{"width":"600px","height":"200px","title":"添加地址","link":"./addAddress.do?traderId=${saleorder.traderId}&indexId=3"}'>添加地址</span>
                                <div id="traderAddressIdMsg" style="clear:both"></div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>

        </div>

        <c:if test="${saleorder.customerNature eq 465}"><!-- 分销 -->
        <div id="quotePayMoneForm">
            <input type="hidden" value="${saleorder.customerNature}" id="saleCustomerNature">
            <input type="hidden" name="terminalTraderName" id="terminalTraderName" class="terminal"
                   value="${saleorder.terminalTraderName}"/>
            <input type="hidden" name="terminalTraderId" id="terminalTraderId" class="terminal"
                   value="${saleorder.terminalTraderId}"/>
            <input type="hidden" name="terminalTraderType" id="terminalTraderType" class="terminal"
                   value="${saleorder.terminalTraderType}"/>
            <input type="hidden" name="salesArea" id="salesArea" class="terminal" value="${saleorder.salesArea}"/>
            <input type="hidden" name="salesAreaId" id="salesAreaId" class="terminal" value="${saleorder.salesAreaId}"/>
        </div>

        <div class="parts" id="updateTerminalInfo" style="padding-top: 15px">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">终端信息</div>
            </div>
            <ul class="payplan" style="border: 1px solid #ddd;padding-top:10px">
                <c:choose>
                <c:when test="${not empty saleorder.terminalTraderName}"><!-- 客户名称存在，则默认不显示选择框 -->
                <li id="terminalNameCheck" style="display: none;">
                    </c:when>
                    <c:otherwise><!-- 客户名称不存在，默认显示选择框 -->
                <li id="terminalNameCheck">
                    </c:otherwise>
                    </c:choose>
                    <div class="infor_name">
                        <label>终端名称</label>
                    </div>
                    <div class="f_left">
                        <div class="inputfloat" id="errorTxtMsg">
                            <!-- 客户为终端 -->
                            <input type="text" placeholder="请输入终端名称" class="input-largest" name="searchTraderName"
                                   id="searchTraderName">
                            <label class="bt-bg-style bg-light-blue bt-small" onclick="searchTerminal();" id="errorMes">搜索</label>
                            <span style="display: none;"> <!-- 弹框 -->
                                            <div class="title-click nobor  pop-new-data" id="terminalDiv"></div>
                                        </span>
                        </div>
                    </div>
                </li>
                <c:choose>
                <c:when test="${empty saleorder.terminalTraderName}"><!-- 客户名称不存在，默认显示选择框 -->
                <li id="terminalNameDetail" style="display: none;">
                    </c:when>
                    <c:otherwise><!-- 客户名称不存在，默认显示选择框 -->
                <li id="terminalNameDetail">
                    </c:otherwise>
                    </c:choose>
                    <div class="infor_name">
                        <label>终端名称</label>
                    </div>
                    <div class="f_left">
                        <div class=" inputfloat" id="errorTxtMsg">
                            <span class="mr8 mt3" id="terminalTraderNameDiv">${saleorder.terminalTraderName}</span>
                            <label class="bt-bg-style bg-light-blue bt-small"
                                   onclick="agingSearchTerminal();">重新搜索</label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="infor_name ">
                        <span>*</span>
                        <label>销售区域</label>
                    </div>
                    <div class="f_left">
                        <c:choose>
                            <c:when test="${empty saleorder.salesAreaId}">
                                <select class="input-small f_left mr10" name="province" id="province">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty provinceList }">
                                        <c:forEach items="${provinceList }" var="prov">
                                            <option value="${prov.regionId }"
                                                    <c:if test="${province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <select class="input-small f_left mr10" name="city" id="city">
                                    <option value="0">请选择</option>
                                </select>
                                <select class="input-small f_left" name="zone" id="zone">
                                    <option value="0">请选择</option>
                                </select>
                            </c:when>
                            <c:otherwise>
                                <select class="input-small f_left mr10" name="province" id="province">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty provinceList }">
                                        <c:forEach items="${provinceList }" var="province">
                                            <option value="${province.regionId }"
                                                    <c:if test="${ not empty provinceRegion &&  province.regionId == provinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <select class="input-small f_left mr10" name="city" id="city">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty cityList }">
                                        <c:forEach items="${cityList }" var="city">
                                            <option value="${city.regionId }"
                                                    <c:if test="${ not empty cityRegion &&  city.regionId == cityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <select class="input-small f_left" name="zone" id="zone">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty zoneList }">
                                        <c:forEach items="${zoneList }" var="zone">
                                            <option value="${zone.regionId }"
                                                    <c:if test="${ not empty zoneRegion &&  zone.regionId == zoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                            </c:otherwise>
                        </c:choose>
                        <div id="sales_area_msg_div" style="clear:both"></div>
                    </div>
                </li>
            </ul>
        </div>
        </c:if>


        <div class="parts" style="padding-top: 15px">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">产品信息</div>
                <c:if test="${orderType ne 5 }">
                    <a class="title-click nobor" href="javascript:void(0);"
                       onclick="validateOrderCouponInfo('addGood');">添加</a>
                    <div id="addGood" class="title-click nobor  pop-new-data"
                         layerParams='{"width":"800px","height":"650px","title":"添加产品","link":"./addSaleorderGoods.do?scene=${scene}&saleorderId=${saleorder.saleorderId}"}'
                         style="display: none;"></div>
                    <c:if test="${saleorder.quoteorderId eq 0}">
                        <a class="title-click nobor" href="javascript:void(0);"
                           onclick="validateOrderCouponInfo('batchAddGood');">批量新增</a>
                        <div id="batchAddGood" class="title-click nobor  pop-new-data"
                             layerparams='{"width":"650px","height":"650px","title":"批量新增","link":"/order/saleorder/batchAddSaleGoodsInit.do?saleorderId=${saleorder.saleorderId}"}'
                             style="display: none;">批量新增
                        </div>
                    </c:if>
                    <%--                        <a onclick="updateSaleGoodsInit(${saleorder.saleorderId},${scene});" >批量修改</a>--%>
                    <a class="title-click nobor" href="javascript:void(0);"
                       onclick="updateSaleGoodsInit(${saleorder.saleorderId},${scene});">批量修改</a>
                </c:if>
            </div>
            <table class="table  table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th style="width:50px">序号</th>
                    <th>产品信息</th>
                    <th style="width:80px">单价</th>
                    <th style="width:35px">数量</th>
                    <th style="width:80px">总额</th>
                    <th style="width:70px">货期</th>
                    <th style="width:50px">直发</th>
                    <th style="width:50px">报价含安调</th>
                    <th style="width:150px">核价参考</th>
                    <th style="width:80px">占用/库存</th>
                    <th>产品备注</th>
                    <th>内部备注</th>
                    <th style="width:110px">操作</th>
                </tr>
                </thead>
                <tbody id="goodsTbody">
                <c:set var="num" value="0"></c:set>
                <c:set var="totleMoney" value="0.00"></c:set>
                <!-- 原订单总金额 -->
                <c:set var="frTotleMoney" value="0.00"></c:set>
                <c:forEach var="list" items="${saleorderGoodsList}" varStatus="staut">
                    <c:if test="${list.isDelete eq 0}">
                        <c:set var="num" value="${num + list.num}"></c:set>
                        <c:set var="totleMoney" value="${totleMoney + (list.price * list.num)}"></c:set>
                        <c:set var="frTotleMoney" value="${frTotleMoney + (list.realPrice * list.num)}"></c:set>
                    </c:if>
                    <tr <c:if test="${list.isDelete eq 1}">class="caozuo-grey"</c:if>>
                        <td style="overflow:hidden;padding-left:12px;">
                            <input type="hidden" value="${list.sku}">
                            <input type="checkbox" name="goodsCheckName" onclick="goodsCheckClick(this);"
                                   value="${list.saleorderGoodsId}" skuNo="${list.sku}" skuId="${list.goodsId}"
                                   skuName="${list.goodsName}" class="f_left" autocomplete="off"/>
                            <span class="f_left">${staut.count}</span>
                        </td>
                        <td class="text-left">
                            <div class="customername pos_rel">
                                <c:choose>
                                    <c:when test="${list.isDelete eq 1}">
                                        <span>${newSkuInfosMap[list.sku].SHOW_NAME}<br/></span>
                                        <span>${newSkuInfosMap[list.sku].SKU_NO}<br>${newSkuInfosMap[list.sku].MATERIAL_CODE}</span>
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${list.isRisk > 0}">
                                            <img src="${pageContext.request.contextPath}/static/images/risk.png"
                                                 width="28px" id="riskFlag_${list.saleorderGoodsId}"
                                                 onclick="checkSaleorderGoodsRisk('${newSkuInfosMap[list.sku].SKU_NO}','${list.saleorderGoodsId}')">
                                        </c:if>
                                        <span class="font-blue"><a class="addtitle" href="javascript:void(0);"
                                                                   tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":"产品信息"}'>${newSkuInfosMap[list.sku].SHOW_NAME}</a>&nbsp;<i
                                                class="iconbluemouth contorlIcon"></i><br/></span>
                                        <span>${newSkuInfosMap[list.sku].SKU_NO}<br>${newSkuInfosMap[list.sku].MATERIAL_CODE}</span><br>
                                        <span>品牌：${newSkuInfosMap[list.sku].BRAND_NAME}</span><br>
                                        <span>规格/型号：${newSkuInfosMap[list.sku].MODEL}</span><br>
                                        <span>单位：${newSkuInfosMap[list.sku].UNIT_NAME}</span>
                                        <c:set var="skuNo" value="${list.sku}"></c:set>
                                        <%@ include file="../../common/new_sku_common_tip.jsp" %>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </td>
                        <td>${list.price}</td>
                        <c:if test="${orderType eq 5 or orderType eq 1}">
                            <td>
                                <fmt:formatNumber type="number" value="${null == list.realPrice ? 0 : list.realPrice}"
                                                  pattern="0.00" maxFractionDigits="2"/>
                            </td>
                        </c:if>
                        <td>${list.num}</td>
                        <td>

                            <c:choose>
                                <c:when test="${orderType eq 5 or orderType eq 1}">
                                    <fmt:formatNumber type="number"
                                                      value="${list.maxSkuRefundAmount - list.afterReturnAmount}"
                                                      pattern="0.00" maxFractionDigits="2"/>
                                </c:when>
                                <c:otherwise>
                                    <fmt:formatNumber type="number" value="${list.price * list.num}" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                </c:otherwise>
                            </c:choose>

                        </td>
                        <td>${list.deliveryCycle}</td>
                        <td>
                            <div class="customername pos_rel">
		                                <span>
		                                <c:choose>
											<c:when test="${list.deliveryDirect eq 0}">否</c:when>
											<c:when test="${list.deliveryDirect eq 3}"></c:when>
											<c:otherwise>
												是<i class="iconbluesigh ml4 contorlIcon"></i></span>
                                <div class="pos_abs customernameshow">直发原因：${list.deliveryDirectComments}</div>
                                </c:otherwise>
                                </c:choose>
                            </div>
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${list.haveInstallation eq 0}">否</c:when>
                                <c:otherwise>是</c:otherwise>
                            </c:choose>
                        </td>
                        <td>
                            <div class="customername pos_rel">
                                核价参考价格：${skuNoAndPriceMap[list.sku]}<br/>
                                参考价格：${list.referencePrice} <br/>
                                参考货期：${list.referenceDeliveryCycle} <br/>
                                <shiro:hasPermission name="/order/quote/settlementPrice.do">
                                    结算价：${list.settlePrice} <br/>
                                </shiro:hasPermission>
                                <c:choose>
                                    <c:when test="${list.reportStatus eq 2}">
                                        报备成功<i class="iconbluesigh ml4"></i>
                                    </c:when>
                                    <c:when test="${list.reportStatus eq 3}">
                                        报备失败<i class="iconredsigh ml4"></i>
                                    </c:when>
                                    <c:otherwise>
                                        <i class="iconbluesigh ml4"></i>
                                    </c:otherwise>
                                </c:choose>
                                <div class="pos_abs customernameshow">
                                    <c:set var="goodsUserNm" value=""/>
                                    <c:forEach var="user" items="${userList}">
                                        <c:if test="${user.userId eq list.lastReferenceUser}"><c:set var="goodsUserNm"
                                                                                                     value="${user.username}"/></c:if>
                                    </c:forEach>
                                    核价参考价格：${skuNoAndPriceMap[list.sku]}<br/>
                                    客户上次购买价格：<fmt:formatNumber type="number" value="${list.lastOrderPrice}"
                                                               pattern="0.00" maxFractionDigits="2"/><br/>
                                    参考价格（${goodsUserNm}）：${list.referencePrice} <br/>
                                    期货交货期：${list.channelDeliveryCycle} <br>
                                    现货交货期：${list.delivery} <br>
                                    参考货期（${goodsUserNm}）：${list.referenceDeliveryCycle} <br/>
                                    报备结果：
                                    <c:if test="${list.reportStatus eq 2}">
                                        成功 <br/>
                                        报备失败原因：<%-- ${list.reportComments} --%>
                                    </c:if>
                                    <c:if test="${list.reportStatus eq 3}">
                                        失败 <br/>
                                        报备失败原因：${list.reportComments}
                                    </c:if>
                                </div>
                            </div>
                            </br>
                            是否需报备：
                            <c:choose>
                            <c:when test="${list.isNeedReport == 1}">
                            是

                            <div class="customername pos_rel" style="display: inline-block;">
                                <i class="iconbluesigh ml4"></i>
                                <div class="pos_abs customernameshow">

                                    <div class="table-item item-col">
                                        <div class="table-td">
                                            是否获得授权：
                                            <c:if test="${list.isAuthorized eq 1}">
                                                有授权
                                            </c:if>
                                            <c:if test="${list.isAuthorized eq 0}">
                                                无授权
                                            </c:if>
                                        </div>
                                    </div>
                                    <c:if test="${list.isAuthorized eq 1}">
                                        <div class="table-item item-col">
                                            <div class="table-td">
                                                授权范围：
                                                <c:forEach items="${list.skuAuthorizationVo.skuAuthorizationItemVoList}"
                                                           var="skuAuthorizationItem">
                                                    <c:choose>
                                                        <c:when test="${fn:length(skuAuthorizationItem.regionIds) == fn:length(regions)}">
                                                            “全国”
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:forEach items="${skuAuthorizationItem.regionIds}"
                                                                       var="regionId" varStatus="regionStatus">
                                                                <c:if test="${regionStatus.first}">
                                                                    "
                                                                </c:if>
                                                                <c:forEach items="${regions}" var="region">
                                                                    <c:if test="${region.regionId eq regionId}">
                                                                        ${region.regionName}
                                                                    </c:if>
                                                                </c:forEach>
                                                                <c:if test="${!regionStatus.last}">
                                                                    、
                                                                </c:if>
                                                                <c:if test="${regionStatus.last}">
                                                                    "
                                                                </c:if>
                                                            </c:forEach>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    的
                                                    <c:choose>
                                                        <c:when test="${fn:length(skuAuthorizationItem.terminalTypeIds) == fn:length(terminalTypes)}">
                                                            “全部终端”
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:forEach items="${skuAuthorizationItem.terminalTypeIds}"
                                                                       var="terminalTypeId"
                                                                       varStatus="terminalTypeStatus">
                                                                <c:if test="${terminalTypeStatus.first}">
                                                                    "
                                                                </c:if>
                                                                <c:forEach items="${terminalTypes}" var="terminalType">
                                                                    <c:if test="${terminalType.sysOptionDefinitionId eq terminalTypeId}">
                                                                        ${terminalType.title}
                                                                    </c:if>
                                                                </c:forEach>
                                                                <c:if test="${!terminalTypeStatus.last}">
                                                                    、
                                                                </c:if>
                                                                <c:if test="${terminalTypeStatus.last}">
                                                                    "
                                                                </c:if>
                                                            </c:forEach>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    已获得授权
                                                    </br>
                                                </c:forEach>
                                                <label style="color: red;font-weight: bold">注：除以上范围，需要报备。</label>
                                            </div>
                                        </div>
                                    </c:if>
                                </div>

                                </c:when>
                                <c:otherwise>
                                否
                                </c:otherwise>
                                </c:choose>
                        </td>
                        <td><span
                                id="orderOccupy_stockNum_${list.saleorderGoodsId}">${list.goods.orderOccupy}/${list.goods.stockNum}</span>
                        </td>

                        <td>${list.goodsComments}</td>
                        <td class="c-comments" skuId="${list.goodsId}">
                            <div class="customername pos_rel lm-main-item">
                                    ${list.insideComments}
                                <c:if test="${list.componentHtml ne ''}">
                                    <i class="iconbluemouth contorlIcon"></i>
                                    <div class="pos_abs customernameshow" label_left="-300"
                                         style="width: 500px; top: 25px;background-color: #00CD66;">
                                            ${list.componentHtml}
                                    </div>
                                </c:if>
                            </div>
                            <div class="no_remark_error" style="display: none;">
                                <span style="color:red;">请设置订单要求，点击编辑进行内部备注修改</span>
                            </div>
                        </td>
                        <td>
                            <div class="caozuo">
                                <c:choose>
                                    <c:when test="${list.isDelete eq 0}">

                                        <span class="caozuo-blue" href="javascript:void(0);"
                                              onclick="validateOrderCouponInfo('editSaleOrderGood${list.sku}');">编辑</span>
                                        <span id="editSaleOrderGood${list.sku}" style="display: none"
                                              class="caozuo-blue pop-new-data"
                                              layerParams='{"width":"700px","height":"650px","title":"编辑产品信息","link":"./editSaleorderGoods.do?saleorderGoodsId=${list.saleorderGoodsId}&orderType=${orderType}&isCoupons=${saleorder.isCoupons}&scene=${scene}"}'>编辑</span>

                                        <c:if test="${orderType ne 5 }">
                                            <span class="caozuo-red" href="javascript:void(0);"
                                                  onclick="validateOrderCouponInfo('deleteSaleOrderGood${list.sku}');">删除</span>
                                            <span id="deleteSaleOrderGood${list.sku}" style="display: none"
                                                  class="caozuo-red"
                                                  onclick="delSaleorderGoods(${list.saleorderGoodsId},${list.saleorderId},'${list.sku}',0);">删除</span>
                                        </c:if>

                                    </c:when>
                                    <c:otherwise>
                                        <span class="caozuo-grey cursor-normal">已删除</span>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </td>
                    </tr>
                </c:forEach>
                <tr style="background: #eaf2fd;">

                    <td colspan="13" class="text-left">

                        <input type="checkbox" name="goodsCheckAllName" id="goodsCheckAllName"
                               onclick="goodsCheckAllClick(this);" autocomplete="off"/><span>全选</span>
                        <span style="display: none;"> <!-- 弹框 -->
									<div class="title-click nobor  pop-new-data" id="saleGoodsDeliveryDirect"></div>
								</span>

                        &nbsp;&nbsp;总件数<span class="font-red">${num}</span>

                        <c:if test="${orderType eq 5 }">
                            ，原总金额
                            <span class="font-red">
                                   	<fmt:formatNumber type="number" value="${frTotleMoney}" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                   </span>
                            ，优惠金额 <span class="font-red"><fmt:formatNumber type="number"
                                                                           value="${null == saleorder.couponAmount ? 0 : saleorder.couponAmount}"
                                                                           pattern="0.00" maxFractionDigits="2"/></span>
                        </c:if>

                        <c:if test="${orderType ne 1 && orderType ne 5}">
                            ， 总金额 <span class="font-red"><fmt:formatNumber type="number" value="${totleMoney}"
                                                                           pattern="0.00" maxFractionDigits="2"/></span>
                        </c:if>

                        <c:if test="${orderType eq 5}">
                            ， 总金额<span class="font-red">
                            <fmt:formatNumber type="number" value="${saleorder.totalAmount}" pattern="0.00"
                                              maxFractionDigits="2"/></span>
                            </span>
                        </c:if>

                        <c:if test="${orderType eq 1}">
                            ， 优惠前总额 <span class="font-red"><fmt:formatNumber type="number" value="${frTotleMoney}"
                                                                             pattern="0.00"
                                                                             maxFractionDigits="2"/></span>
                            ， 优惠金额 <span class="font-red"><fmt:formatNumber type="number"
                                                                            value="${saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination}"
                                                                            pattern="0.00"
                                                                            maxFractionDigits="2"/></span>
                            ， 优惠后金额 <span class="font-red"><fmt:formatNumber type="number"
                                                                             value="${frTotleMoney - (saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination)}"
                                                                             pattern="0.00"
                                                                             maxFractionDigits="2"/></span>
                        </c:if>

                        <input type="hidden" id="goodsTotleMoney" value="${totleMoney}">
                        <!-- modify by tomcat.Hui 20190918 VDERP-1294 编辑订单金额错误  增加隐藏域，用于jQuery下拉框联动 start -->
                        <input type="hidden" id="goodsTotleAmount" value="${saleorder.totalAmount}">
                        <input type="hidden" id="goodsOrderType" value="${orderType}">
                        <!-- modify by tomcat.Hui 20190918 VDERP-1294 编辑订单金额错误  增加隐藏域，用于jQuery下拉框联动 end -->
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <c:if test="${orderType ne 5}">
        <div class="parts" id="couponsInfo">
            <div class="title-container title-container-blue">
                <div class="table-title nobor">优惠券信息</div>
            </div>
            <div style="border: 1px solid #ddd; padding-top:10px">

                <ul class="payplan" style="padding:10px">
                    <li class="visible">
                        <div class="f_left  customername pos_rel">
                            &nbsp;&nbsp;&nbsp;
                            <%--<a href="javascript:void(0);" onclick="chooseCoupon()">选择优惠券</a>--%>
                            <span class="mt4 pop-new-data font-blue" id="chooseCouponSpan"
                                  style="display: none;"></span>
                            <span class="edit-user addtitle" id="myCouponSpan"
                                  tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./order/saleorder/getTraderCoupons.do?traderId=${saleorder.traderId}","title":"我的优惠券"}'
                                  style="display: none">我的优惠券</span>
                        </div>
                    </li>
                    <li>
                        <div class="f_left" id="couponInfo">
                            <c:if test="${saleorderCoupon != null}">
                                ${saleorderCoupon.denomination}元
                                满${saleorderCoupon.useThreshold}减${saleorderCoupon.denomination}元
                                ${saleorderCoupon.limitTypeStr}
                                有效期至:${saleorderCoupon.effevtiveEndTime}
                            </c:if>
                        </div>
                    </li>
                </ul>

                </table>
                    <%--                    <div class="formtitle">优惠券信息</div>--%>

            </div>
            </c:if>


            <div class="parts content1 " style="padding-top: 15px">
                <div class="title-container title-container-blue">
                    <div class="table-title nobor">收货信息 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <c:if test="${type eq 1}">
                            <span style="color: red">GE订单，【收货信息】需正确填写终端用户信息</span>
                        </c:if>
                    </div>
                </div>
                <div style="border: 1px solid #ddd;padding-top:10px">
                    <ul class="payplan" style="padding:10px">
                        <li>
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>收货客户</label>
                            </div>
                            <div class="f_left  customername pos_rel">
                                <input type="hidden" name="takeTraderId" id="trader_id_1"
                                       value="${saleorder.takeTraderId}">
                                <input type="hidden" name="takeTraderName" id="trader_name_1"
                                       value="${saleorder.takeTraderName}">
                                <span class="font-blue">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
                                                "link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}&traderId=${saleorder.takeTraderId}",
                                                "title":"客户信息"}'>
                                        ${saleorder.takeTraderName}
                                    </a>
                                </span>
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>收货联系人</label>
                            </div>
                            <div class="f_left">
                                <select class="input-xx contact" id="contact_1" name="takeTraderContactId">
                                    <option value="0">请输入联系人姓名或手机号</option>
                                    <c:if test="${not empty takeTraderContactList}">
                                        <c:forEach items="${takeTraderContactList}" var="list" varStatus="status">
                                            <option value="${list.traderContactId}"
                                                    <c:if test="${list.traderContactId eq saleorder.takeTraderContactId}">selected="selected"</c:if>>
                                                    ${list.name}/${list.telephone}/${list.mobile}
                                            </option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <input type="hidden" name="takeTraderContactName">
                                <input type="hidden" name="takeTraderContactTelephone">
                                <input type="hidden" name="takeTraderContactMobile">
                                <span class="mt4 pop-new-data font-blue" id="add_contact_1"
                                      layerParams='{"width":"430px","height":"220px","title":"添加联系人","link":"./addContact.do?traderId=${saleorder.takeTraderId}&traderCustomerId=${takeTraderCustomerInfo.traderCustomerId}&indexId=1"}'>新增</span>
                                <div id="takeTraderContactIdMsg" style="clear:both"></div>
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>收货地址</label>
                            </div>
                            <div class="f_left">
                                <select class="input-xx address" id="address_1" name="takeTraderAddressId">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty takeTraderAddressList}">
                                        <c:forEach items="${takeTraderAddressList}" var="list" varStatus="status">
                                            <option value="${list.traderAddress.traderAddressId}"
                                                    <c:if test="${list.traderAddress.traderAddressId eq saleorder.takeTraderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <input type="hidden" name="takeTraderArea">
                                <input type="hidden" name="takeTraderAddress">
                                <span class="mt4 pop-new-data font-blue" id="add_address_1"
                                      layerParams='{"width":"600px","height":"200px","title":"添加地址","link":"./addAddress.do?traderId=${saleorder.takeTraderId}&indexId=1"}'>添加地址</span>
                                <div id="takeTraderAddressIdMsg" style="clear:both"></div>
                            </div>
                        </li>


                        <li>
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>发货方式</label>
                            </div>
                            <div class="f_left inputfloat">
                                <select class="input-middle" name="deliveryType" id="deliveryTypeSelect"
                                        <c:if test="${saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0)}"> disabled="disabled" </c:if>>
                                    <c:forEach var="list" items="${deliveryTypes}">
                                        <option value="${list.sysOptionDefinitionId}"
                                                <c:if test="${saleorder.deliveryType == list.sysOptionDefinitionId}">selected="selected"</c:if>
                                                <c:if test="${(saleorder.deliveryType == 0 or saleorder.deliveryType == null) and list.title == '分批发货'}">selected="selected"</c:if>
                                        >${list.title}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>发货要求</label>
                            </div>
                            <div class="f_left inputfloat">
                                <select class="input-middle" name="deliveryClaim" id="deliveryClaimSelect"
                                        onchange="deliveryClaimChange();"
                                        <c:if test="${saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0)}"> disabled="disabled" </c:if>>
                                    <option value="0"
                                            <c:if test="${saleorder.deliveryClaim == 0}">selected="selected"</c:if>>立即发货
                                    </option>
                                    <option value="1"
                                            <c:if test="${saleorder.deliveryClaim == 1}">selected="selected"</c:if>>
                                        等通知发货
                                    </option>
                                </select>
                            </div>
                            <div id="waitDeadlineDiv">
                                <div class="infor_name infor_name72">
                                    <span>*</span>
                                    <label>等待截止日</label>
                                </div>
                                <div class="f_left inputfloat">
                                    <input class="Wdate f_left input-smaller96 mr5" autocomplete="off" type="text"
                                           placeholder="请选择日期"
                                    <c:if test="${saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0)}">
                                           disabled="disabled" </c:if>
                                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-{%d+1}',maxDate:'%y-%M-{%d+15}'})"
                                           autocomplete="off"
                                           name="deliveryDelayTimeStr" id="deliveryDelayTimeStr"
                                           value='<date:date value ="${saleorder.deliveryDelayTime}" format="yyyy-MM-dd"/>'
                                           style="width:240px;">
                                </div>
                                <div id="deliveryDelayTimeStrMsg" style="clear:both;"></div>
                            </div>

                        </li>

                        <c:if test="${saleorder.orderType != 5}">
                            <c:choose>
                                <c:when test="${saleorder.isPrintout == -1}">
                                    <li id="is_print_li">
                                        <div>
                                            <div style="height: 25px">
                                                <span style="color: red;text-indent: 15px">  *</span>
                                                <label style="width: 150px">是否打印随货出库单</label>
                                                <select id="is_print" style="height: auto"
                                                        onchange="changeIsPrintout()">
                                                    <option value="-1" selected=selected>请选择</option>
                                                    <option value="1">是</option>
                                                    <option value="2">否</option>
                                                </select>
                                            </div>
                                            <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                            <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                            <div id="isPrintoutMsg" style="clear:both;"></div>
                                        </div>
                                        <input id="is_printout" type="hidden" name="isPrintout"
                                               value="${saleorder.isPrintout}">
                                    </li>
                                </c:when>

                                <c:when test="${saleorder.isPrintout == 0}">
                                    <li id="is_print_li">
                                        <div>
                                            <div style="height: 25px">
                                                <span style="color: red;text-indent: 15px">  *</span>
                                                <label style="width: 150px">是否打印随货出库单</label>
                                                <select id="is_print" style="height: auto"
                                                        onchange="changeIsPrintout()">
                                                    <option value="-1">请选择</option>
                                                    <option value="1">是</option>
                                                    <option value="2" selected=selected>否</option>
                                                </select>
                                            </div>
                                            <input type="hidden" id="is_scientificDept" value="${isScientificDep}">

                                            <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                            <div id="isPrintoutMsg" style="clear:both;"></div>
                                        </div>
                                        <input id="is_printout" type="hidden" name="isPrintout"
                                               value="${saleorder.isPrintout}">
                                    </li>
                                </c:when>
                                <c:when test="${saleorder.isPrintout == 1}">

                                    <li id="is_print_li">
                                        <div>
                                            <div style="height: 25px">
                                                <span style="color: red;text-indent: 15px">  *</span>
                                                <label style="width: 150px">是否打印随货出库单</label>
                                                <select id="is_print" style="height: auto"
                                                        onchange="changeIsPrintout()">
                                                    <option value="-1">请选择</option>
                                                    <option value="1" selected=selected>是</option>
                                                    <option value="2">否</option>
                                                </select>
                                            </div>
                                            <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                            <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                            <div id="isPrintoutMsg" style="clear:both;"></div>
                                        </div>
                                        <input id="is_printout" type="hidden" name="isPrintout"
                                               value="${saleorder.isPrintout}">
                                    </li>

                                    <li id='is_price_li'>
                                        <div style="height: 25px">
                                            <span style="color: red;text-indent: 15px">*</span>
                                            <label style="width: 150px">随货出库单是否自带价格</label>
                                            <select id='is_price' name="isPrintout" style='height: auto'
                                                    onchange='changeIsPrice()'>
                                                <option value="0">请选择</option>
                                                <option value="1" selected=selected>是</option>
                                                <option value="2">否</option>
                                            </select>
                                        </div>
                                        <div id="isPriceMsg" style="clear:both;"></div>
                                    </li>
                                </c:when>

                                <c:when test="${saleorder.isPrintout == 2}">

                                    <li id="is_print_li">
                                        <div>
                                            <div style="height: 25px">
                                                <span style="color: red;text-indent: 15px">  *</span>
                                                <label style="width: 150px">是否打印随货出库单</label>
                                                <select id="is_print" style="height: auto"
                                                        onchange="changeIsPrintout()">
                                                    <option value="-1">请选择</option>
                                                    <option value="1" selected=selected>是</option>
                                                    <option value="2">否</option>
                                                </select>
                                            </div>
                                            <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                            <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                            <div id="isPrintoutMsg" style="clear:both;"></div>
                                        </div>
                                        <input id="is_printout" type="hidden" name="isPrintout"
                                               value="${saleorder.isPrintout}">
                                    </li>

                                    <li id='is_price_li'>
                                        <div style="height: 25px">
                                            <span style="color: red;text-indent: 15px">*</span>
                                            <label style="width: 150px">随货出库单是否自带价格</label>
                                            <select id='is_price' name="isPrintout" style='height: auto'
                                                    onchange='changeIsPrice()'>
                                                <option value="0">请选择</option>
                                                <option value="1">是</option>
                                                <option value="2" selected=selected>否</option>
                                            </select>
                                        </div>
                                        <div id="isPriceMsg" style="clear:both;"></div>
                                    </li>
                                </c:when>

                                <c:when test="${saleorder.isPrintout == 3}">

                                    <li id="is_print_li">
                                        <div>
                                            <div style="height: 25px">

                                                <span style="color: red;text-indent: 15px">  *</span>
                                                <label style="width: 150px">是否打印随货出库单</label>
                                                <select id="is_print" style="height: auto"
                                                        onchange="changeIsPrintout()">
                                                    <option value="-1">请选择</option>
                                                    <option value="1" selected=selected>是</option>
                                                    <option value="2">否</option>
                                                </select>

                                            </div>

                                            <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                            <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                            <div id="isPrintoutMsg" style="clear:both;"></div>
                                        </div>
                                        <input id="is_printout" type="hidden" name="isPrintout"
                                               value="${saleorder.isPrintout}">
                                    </li>
                                </c:when>
                                <c:otherwise>
                                    <li id="is_print_li">
                                        <div>
                                            <div style="height: 25px">
                                                <span style="color: red;text-indent: 15px">  *</span>
                                                <label style="width: 150px">是否打印随货出库单</label>
                                                <select id="is_print" style="height: auto"
                                                        onchange="changeIsPrintout()">
                                                    <option value="-1" selected=selected>请选择</option>
                                                    <option value="1">是</option>
                                                    <option value="2">否</option>
                                                </select>
                                            </div>
                                            <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                            <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
                                            <div id="isPrintoutMsg" style="clear:both;"></div>
                                        </div>
                                        <input id="is_printout" type="hidden" name="isPrintout"
                                               value="${saleorder.isPrintout}">
                                    </li>
                                </c:otherwise>
                            </c:choose>
                        </c:if>

                        <li>
                            <div class="infor_name infor_name72">
                                <label>指定物流公司</label>
                            </div>
                            <div class="f_left inputfloat">
                                <select class="input-middle" name="logisticsId">
                                    <option value="0">请选择</option>
                                    <c:forEach var="list" items="${logisticsList}">
                                        <c:if test="${list.isEnable == 1}">
                                            <option value="${list.logisticsId}"
                                                    <c:if test="${saleorder.logisticsId == list.logisticsId}">selected="selected"</c:if> >${list.name}</option>
                                        </c:if>
                                    </c:forEach>
                                </select>
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <label>运费说明</label>
                            </div>
                            <div class="f_left inputfloat">
                                <select class="input-middle" name="freightDescription">
                                    <option value="0">请选择</option>
                                    <c:forEach var="list" items="${freightDescriptions}">
                                        <option value="${list.sysOptionDefinitionId}"
                                                <c:if test="${saleorder.freightDescription == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </li>
                        <li>

                            <div class="infor_name infor_name72">
                                <label>物流备注</label>
                            </div>
                            <div class="f_left inputfloat">
                                <input type="text" name="logisticsComments" id="logisticsComments"
                                       value="${saleorder.logisticsComments}"
                                       placeholder="对内使用，适用于向物流部同事告知发货要求，默认同步客户信息中物流备注" class="input-xx"/>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="parts content1 content2" style="padding-top: 15px">
                <div class="title-container title-container-blue">
                    <div class="table-title nobor">
                        收票信息
                    </div>
                </div>
                <div style="border: 1px solid #ddd;padding-top:10px">
                    <ul class="payplan" style="padding:10px">
                        <li>
                            <div class="infor_name infor_name96">
                                <span>*</span>
                                <label>发票是否寄送</label>
                            </div>
                            <div class="f_left inputfloat">
                                <input type="radio" name="isSendInvoice"
                                       onclick="isSendInvoiceChecked('${orderType}', 1)"
                                       <c:if test="${saleorder.isSendInvoice eq 1}">checked</c:if> value="1">
                                <label>寄送</label> &nbsp;&nbsp;
                                <input type="radio" name="isSendInvoice"
                                       onclick="isSendInvoiceChecked('${orderType}',0)"
                                       <c:if test="${saleorder.isSendInvoice eq 0}">checked</c:if> value="0">
                                <label>不寄送</label>
                            </div>
                        </li>

                        <c:if test="${orderType eq 5}">
                            <li id="isSameAddressLi">
                                <div class="infor_name infor_name120">
                                    <span>*</span>
                                    <label>货票地址是否相同</label>
                                </div>
                                <div class="f_left inputfloat">
                                    <input type="radio" name="isSameAddress" onclick="isSameAddressChecked(1)"
                                           <c:if test="${saleorder.isSameAddress eq 1}">checked</c:if> value="1">
                                    <label>相同</label> &nbsp;&nbsp;
                                    <input type="radio" name="isSameAddress" onclick="isSameAddressChecked(0)"
                                           <c:if test="${saleorder.isSameAddress eq 0}">checked</c:if> value="0">
                                    <label>不相同</label>
                                    <div id="isSameAddressMsg" style="clear:both"></div>
                                </div>
                            </li>
                            <li id="invoiceSendNodeLi">
                                <div class="infor_name infor_name96">
                                    <span>*</span>
                                    <label>发票寄送节点</label>
                                </div>
                                <div class="f_left inputfloat">
                                    <input type="radio" name="invoiceSendNode" onclick=""
                                           <c:if test="${saleorder.invoiceSendNode eq 0}">checked</c:if> value="0">
                                    <label>全部发货时一次寄送</label> &nbsp;
                                    <input type="radio" name="invoiceSendNode" onclick=""
                                           <c:if test="${saleorder.invoiceSendNode eq 1}">checked</c:if> value="1">
                                    <label>每次发货时分别寄送</label> &nbsp;
                                    <label style="color: red; font-weight:bold">注:该选项只影响"票货同行"订单</label>
                                    <div id="invoiceSendNodeMsg" style="clear:both"></div>
                                </div>
                            </li>
                        </c:if>

                        <li id="invoiceCustomerLi">
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>收票客户</label>
                            </div>
                            <c:choose>
                                <c:when test="${orderType eq 5 }">
                                    <div class="f_left  customername pos_rel">
                                        <span class="font-blue">
                                        <a class="addtitle" href="javascript:void(0);"
                                           tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
                                                    "link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}&traderId=${saleorder.traderId}",
                                                    "title":"客户信息"}'>
                                                ${customer.name}</a></span>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="f_left inputfloat">
                                        <span class="mr10 mt3"
                                              id="trader_name_span_2">${saleorder.invoiceTraderName}</span>
                                        <input type="hidden" name="invoiceTraderId" id="trader_id_2"
                                               value="${saleorder.invoiceTraderId}">
                                        <input type="hidden" name="invoiceTraderName" id="trader_name_2"
                                               value="${saleorder.invoiceTraderName}">
                                        <span class="bt-bg-style bt-small bg-light-blue pop-new-data"
                                              layerParams='{"width":"800px","height":"300px","title":"编辑收票信息","link":"${pageContext.request.contextPath}/trader/customer/searchCustomerList.do?indexId=2&searchTraderName="}'>重新搜索</span>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </li>


                        <li id="traderContactLi">
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>收票联系人</label>
                            </div>
                            <div class="f_left">
                                <select class="input-xx contact" id="contact_2" name="invoiceTraderContactId">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty invoiceTraderContactList}">
                                        <c:forEach items="${invoiceTraderContactList}" var="list" varStatus="status">
                                            <option value="${list.traderContactId}"
                                                    <c:if test="${list.traderContactId eq saleorder.invoiceTraderContactId}">selected="selected"</c:if>>${list.name}/${list.telephone}/${list.mobile}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <input type="hidden" name="invoiceTraderContactName">
                                <input type="hidden" name="invoiceTraderContactTelephone">
                                <input type="hidden" name="invoiceTraderContactMobile">
                                <span class="mt4 pop-new-data font-blue" id="add_contact_2"
                                      layerParams='{"width":"430px","height":"220px","title":"添加联系人","link":"./addContact.do?traderId=${saleorder.invoiceTraderId}&traderCustomerId=${invoiceTraderCustomerInfo.traderCustomerId}&indexId=2"}'>添加联系人</span>
                                <div id="invoiceTraderContactIdMsg" style="clear:both"></div>
                            </div>
                        </li>

                        <li id="invoiceTraderAddressLi">
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>收票地址</label>
                            </div>
                            <div class="f_left ">
                                <select class="input-xx address" id="address_2" name="invoiceTraderAddressId">
                                    <option value="0">请选择</option>
                                    <c:if test="${not empty invoiceTraderAddressList}">
                                        <c:forEach items="${invoiceTraderAddressList}" var="list" varStatus="status">
                                            <option value="${list.traderAddress.traderAddressId}"
                                                    <c:if test="${list.traderAddress.traderAddressId eq saleorder.invoiceTraderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                                <input type="hidden" name="invoiceTraderArea">
                                <input type="hidden" name="invoiceTraderAddress">
                                <span class="mt4 pop-new-data font-blue" id="add_address_2"
                                      layerParams='{"width":"600px","height":"200px","title":"添加地址","link":"./addAddress.do?traderId=${saleorder.invoiceTraderId}&indexId=2"}'>添加地址</span>
                                <div id="invoiceTraderAddressIdMsg" style="clear:both"></div>
                            </div>
                        </li>

                        <li>
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>发票类型</label>
                            </div>
                            <!-- 获取当前日期 -->
                            <jsp:useBean id="now" class="java.util.Date"/>
                            <fmt:formatDate value="${now}" type="both" dateStyle="long" var="today"
                                            pattern="yyyy-MM-dd"/>
                            <div class="f_left inputfloat">
                                <select class="input-middle" name="invoiceType" onchange="updateInvoiceType(this);">
                                    <option value="0">请选择</option>
                                    <!-- 4月1号后税率只有13% -->
                                    <c:choose>
                                        <c:when test="${today >= '2019-04-01'}">
                                            <c:forEach var="list" items="${invoiceTypes}">
                                                <c:if test="${list.sysOptionDefinitionId eq 971 or list.sysOptionDefinitionId eq 972}">
                                                    <option value="${list.sysOptionDefinitionId}"
                                                            <c:if test="${saleorder.invoiceType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                                                </c:if>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <c:forEach var="list" items="${invoiceTypes}">
                                                <c:if test="${list.sysOptionDefinitionId eq 681 or list.sysOptionDefinitionId eq 682 or list.sysOptionDefinitionId eq 971 or list.sysOptionDefinitionId eq 972}">
                                                    <option value="${list.sysOptionDefinitionId}"
                                                            <c:if test="${saleorder.invoiceType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                                                </c:if>
                                            </c:forEach>
                                        </c:otherwise>
                                    </c:choose>
                                </select>
                                <%-- <input type="checkbox" name="isSendInvoiceCheckbox" class="mt5" onclick="isSendInvoiceChecked(${orderType});"  <c:if test="${saleorder.isSendInvoice == '0'}">checked</c:if>>--%>
                                <%-- <input type="hidden" id="isSendInvoice" name="isSendInvoice" value="${saleorder.isSendInvoice}">--%>
                                <%--   <span class="mt3">不寄送</span><c:if test="${orderType eq 5}"><span class="mt3" style="color:#666666;" >注：耗材商城订单，选择不寄送时，收票信息中收票联系人，收票手机号，收票地址，收票邮箱非必填</span></c:if>--%>
                                <div id="invoiceTypeMsg" style="clear:both"></div>
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <span>*</span>
                                <label>开票方式</label>
                            </div>

                            <div class="f_left inputfloat tips-all">
                                <select class="input-middle" name="invoiceMethod">
                                    <option value="0">请选择</option>
                                    <c:if test="${saleorder.invoiceType == 681 or saleorder.invoiceType == 971}">
                                        <option value="1" <c:if test="${saleorder.invoiceMethod == 1}">selected</c:if>>
                                            手动纸质开票
                                        </option>
                                        <option value="2" <c:if test="${saleorder.invoiceMethod == 2}">selected</c:if>>
                                            自动纸质开票
                                        </option>
                                        <option value="3" <c:if test="${saleorder.invoiceMethod == 3}">selected</c:if>>
                                            自动电子发票
                                        </option>
                                    </c:if>
                                    <c:if test="${saleorder.invoiceType == 682 or saleorder.invoiceType == 972}">
                                        <option value="1" <c:if test="${saleorder.invoiceMethod == 1}">selected</c:if>>
                                            手动纸质开票
                                        </option>
                                        <option value="2" <c:if test="${saleorder.invoiceMethod == 2}">selected</c:if>>
                                            自动纸质开票
                                        </option>
                                    </c:if>
                                </select>
                                <div id="invoiceMethodMsg" style="clear:both"></div>
                            </div>
                            <div class="tips-error" style="display: none;color:red;line-height: 26px;">
                                “手动纸质开票”的订单，不在自动开票推送的范围内，后期需要手动申请开票。
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <label>开票备注</label>
                            </div>
                            <div class="f_left inputfloat">
                                <input type="text" name="invoiceComments" id="invoiceComments"
                                       value="${saleorder.invoiceComments}" placeholder="对内使用，适用于向财务部同事告知开票要求"
                                       class="input-xx"/>
                            </div>
                        </li>

                        <li>
                            <div class="infor_name infor_name72">
                                <label>暂缓开票</label>
                            </div>
                            <div class="f_left inputfloat">
                                <input type="checkbox" name="isDelayInvoiceCheckbox" class="mt5"
                                       onclick="isDelayInvoiceChecked();"
                                       <c:if test="${saleorder.isDelayInvoice == 1}">checked="checked"</c:if> >
                                <input type="hidden" name="isDelayInvoice" value="${saleorder.isDelayInvoice}">
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="parts content1" style="padding-top: 15px">
                <div class="title-container title-container-blue">
                    <div class="table-title nobor">
                        付款计划
                    </div>
                </div>
                <div style="border: 1px solid #ddd;padding-top:10px">
                    <ul class="payplan" id="payplan" style="padding:10px">
                        <li>
                            <div class="infor_name infor_name72">
                                <label>付款方式</label>
                            </div>
                            <div class="f_left inputfloat">
                                <select
                                        <c:if test="${ 0 eq saleorder.paymentMode }">disabled="disabled"</c:if> <c:if
                                        test="${5 eq orderType }"></c:if> id="paymentType" autocomplete="off"
                                        name="paymentType" onchange="updatePayment(this,${totleMoney});"
                                        class="input-middle">
                                    <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                        <option value="${list.sysOptionDefinitionId}"
                                                <c:if test="${list.sysOptionDefinitionId eq saleorder.paymentType}">selected</c:if>>${list.title}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72 ">
                                <label>预付金额</label>
                            </div>

                            <div class="f_left">
                                <!-- modify by tomcat.Hui 20190918 VDERP-1294 编辑订单金额错误  改为直接取订单totalamount -->
                                <div><input type="text" autocomplete="off" class="input-middle" id="prepaidAmount"
                                            name="prepaidAmount"
                                <c:if test="${5 eq orderType || 1 eq orderType || saleorder.paymentType ne 424}">
                                            readonly
                                </c:if>
                                <c:choose>
                                <c:when test="${saleorder.prepaidAmount == '0.00' && saleorder.paymentType == 419}">
                                <c:choose>
                                <c:when test="${5 eq orderType }">
                                            value="${saleorder.totalAmount}"
                                </c:when>
                                <c:when test="${1 eq orderType }">
                                            value="${saleorder.totalAmount}"
                                </c:when>
                                <c:otherwise>
                                            value="${totleMoney}"
                                </c:otherwise>
                                </c:choose>
                                </c:when>
                                <c:otherwise>
                                            value="${saleorder.prepaidAmount}"
                                </c:otherwise>
                                </c:choose>
                                ></div>
                                <div id="prepaidAmountError"></div>
                            </div>

                        </li>
                        <li id="accountPeriodLi"
                            <c:if test="${(saleorder.paymentType eq 419) or (saleorder.paymentType eq 0)}">style="display:none"</c:if>>
                            <div class="infor_name infor_name72 ">
                                <label>账期支付</label>
                            </div>
                            <div class="f_left inputfloat">
                                <!-- 账期支付最大限额（剩余账期额度） -->
                                <input type="hidden" id="accountPeriodLeft"
                                       value="<fmt:formatNumber type="number" value="${customer.accountPeriodLeft}" pattern="0.00" maxFractionDigits="2" />">
                                <input type="text" class="input-middle" name="accountPeriodAmount"
                                       id="accountPeriodAmount"
                                       onchange="accountPeriodAmountChange()"
                                       value="<fmt:formatNumber type="number" value="${saleorder.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2" />">
                                <input type="hidden" name="logisticsCollection" id="logisticsCollection"/>
                                <c:if test="${saleorder.orderType != 5}">
                                    <label class="mt4">（其中包含质保金:</label>
                                    <input type="text" class="input-smallest" name="retentionMoney" id="retentionMoney"
                                           onchange="retainageAmountChange()" value="${saleorder.retentionMoney}">
                                    <label class="mt4">元）</label>
                                </c:if>
                                <input type="checkbox" style="margin-top: 7px" name="logisticsCheckBox"
                                       id="logisticsCheckBox"
                                       <c:if test="${saleorder.logisticsCollection eq 1}">checked</c:if>>
                                <label class="mt4">物流代收账期款</label>
                                <div id="accountPeriodAmountError"></div>
                            </div>
                        </li>
                        <li id="retentionMoneyLi">
                            <div class="f_left inputfloat">
                                <div class="infor_name infor_name72 ">
                                </div>
                                <label class="mt4">合同余款</label>
                                <label class="mt4" id="spareMoneyLab"><fmt:formatNumber type="number"
                                                                                        value="${saleorder.accountPeriodAmount - saleorder.retentionMoney}"
                                                                                        pattern="0.00"
                                                                                        maxFractionDigits="2"/></label>
                                <label class="mt4">元，需在发货/开票后</label>
                                <input type="text" class="input-smallest" name="periodDay" id="periodDay"
                                       value="${saleorder.periodDay eq 0 ? customer.periodDay : saleorder.periodDay}"
                                       onchange="periodDayChange()">
                                <label class="mt4">天内支付</label>
                                <c:if test="${saleorder.orderType != 5}">
                                   <span id="retentionMoneySpan">
                                        <label class="mt4">；质保金<font
                                                id="retentionMoneyFont">${saleorder.retentionMoney}</font>元，需要在发货/开票后</label>
                                        <input type="text" class="input-smallest" name="retentionMoneyDay"
                                               value="${saleorder.retentionMoneyDay eq 0 ? customer.periodDay : saleorder.retentionMoneyDay}"
                                               id="retentionMoneyDay" onchange=" retainageAmountDayChange()">
                                        <label class="mt4">天内支付</label>
                                   </span>
                                </c:if>
                                <div id="retentionMoneyDayError"></div>
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <label>收款备注</label>
                            </div>
                            <div class="f_left">
                                <input type="text" placeholder="对内使用，适用于向财务部同事告知收款相关特殊要求，默认同步客户信息中财务备注" class="input-xx"
                                       name="paymentComments" id="paymentComments"
                                       value="${saleorder.paymentComments}"/>
                                <div class="font-grey9 mt10" id="error_div">客户当前账期剩余额度<fmt:formatNumber type="number"
                                                                                                        value="${customer.accountPeriodLeft}"
                                                                                                        pattern="0.00"
                                                                                                        maxFractionDigits="2"/>元，账期天数${customer.periodDay}天；如需更改账期，您需要在客户详情财务信息中申请账期；
                                </div
                            </div>
                        </li>
                        <li id="billPeriodSettlementTypeLi"
                            <c:if test="${(saleorder.paymentType eq 419) or (saleorder.paymentType eq 0)}">style="display:none"</c:if>>
                            <div class="infor_name infor_name72">
                                <label>结算标准</label>
                            </div>
                            <div class="f_left">
                                <select class="input-small f_left mr10" name="billPeriodSettlementType"
                                        id="billPeriodSettlementType">
                                    <option value="1"
                                            <c:if test="${1 eq saleorder.billPeriodSettlementType}">selected="selected"</c:if>>
                                        产品发货
                                    </option>
                                    <option value="2"
                                            <c:if test="${2 eq saleorder.billPeriodSettlementType || null eq saleorder.billPeriodSettlementType}">selected="selected"</c:if>>
                                        产品开票
                                    </option>
                                </select>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="parts content1" style="padding-top:15px">
                <div class="title-container title-container-blue">
                    <div class="table-title nobor">
                        其他信息
                    </div>
                </div>
                <div style="border: 1px solid #ddd;padding-top:10px">
                    <ul class="payplan" style="padding:10px">
                        <li>
                            <div class="infor_name infor_name72">
                                <label>附加条款</label>
                            </div>
                            <div class="f_left">
                                <input type="text" class="input-xx" name="additionalClause" id="additionalClause"
                                       value="${saleorder.additionalClause}" placeholder="面向客户条款，客户可见">
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <label>内部备注</label>
                            </div>
                            <div class="customername pos_rel f_left inputfloat">
                                <input type="text" class="input-xx" name="comments" id="comments"
                                       value="${saleorder.comments}" placeholder="对内使用，客户不可见,可用作自己的备注">
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72">
                                <label>订单测试：</label>
                            </div>
                            <div class="f_left inputfloat">
                                <shiro:lacksPermission  name="/order/edit/orderTestBtnNoUrl.do">
                                    <input type="text" class="input-xx" name="orderTestContent-marker" id="orderTestContent-marker" value="无权限填写"
                                           readonly="readonly" title="无权限填写" style="background-color: #e7e7e7;"
                                           placeholder="无权限填写" lay-ignore>
                                    <input type="hidden"  name="orderTestContent" id="orderTestContent" value="${saleorder.orderTestContent}"  >
                                </shiro:lacksPermission >
                                <shiro:hasPermission  name="/order/edit/orderTestBtnNoUrl.do">
                                    <input type="text" class="input-xx" name="orderTestContent" id="orderTestContent" value="${saleorder.orderTestContent}" maxlength="512"
                                           placeholder="请按照格式填写" lay-ignore>
                                </shiro:hasPermission >
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <input type="hidden" id="retentionMoneyFlag" value="${saleorder.retentionMoney}">
            <input type="hidden" id="prepaidAmountFlag" value="${saleorder.prepaidAmount}">
            <input type="hidden" id="accountPeriodAmountFlag" value="${saleorder.accountPeriodAmount}">
            <div class="add-tijiao">
                <input type="hidden" name="formToken" value="${formToken}"/>
                <input type="hidden" name="saleorderId" value="${saleorder.saleorderId}">
                <input type="hidden" name="beforeParams" value='${beforeParams}'>
                <input type="hidden" name="oldAccountPeriodAmount" id="oldAccountPeriodAmount"
                       value='${saleorder.accountPeriodAmount}'>
                <button type="button" class="bt-bg-style bg-deep-green" onclick="editSubmit(${orderType});">确定</button>
            </div>
    </form>
</div>
<script type="text/json" class="J-contact-json">
             ${traderContactList}

</script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src='<%= basePath %>static/js/order/saleorder/edit.js?rnd=${resourceVersionKey}'></script>

<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp" %>

