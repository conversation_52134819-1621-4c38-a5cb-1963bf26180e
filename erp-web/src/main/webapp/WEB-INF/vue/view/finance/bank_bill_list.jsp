<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<meta charset="UTF-8">
<div id="app" style="padding:5px">
    <div class="bankBillForm">
        <el-form :inline="true" :model="formInline" size="mini" label-position="right" label-width="100px">
            <el-form-item label="对方名称">
                <el-input v-model="formInline.accName1" placeholder="" style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="对方开户机构">
                <el-input v-model="formInline.cadbankNm" placeholder=""  style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="对方账号">
                <el-input v-model="formInline.accno2" placeholder=""  style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="摘要">
                <el-input v-model="formInline.message" placeholder=""  style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="流水号">
                <el-input v-model="formInline.tranFlow" placeholder=""  style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="订单号">
                <el-input v-model="formInline.buyorderNo" placeholder=""  style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="合同名称">
                <el-input v-model="formInline.traderName" placeholder=""  style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="交易时间">
                <el-date-picker
                        v-model="payTime"
                        type="daterange"
                        style="width: 200px"
                        :clearable="false"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="收支">
                <el-select v-model="formInline.flag1" placeholder="" style="width: 200px">
                    <el-option label="全部" value="-1"></el-option>
                    <el-option label="收入" value="1"></el-option>
                    <el-option label="支出" value="0"></el-option>
                </el-select>
            </el-form-item>

            <div style="display: flex;justify-content: center;">
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" :loading="tableLoading">查询</el-button>
                    <el-button @click="resetForm">重置</el-button>
                </el-form-item>
            </div>
        </el-form>
    </div>

    <div class="bankBillTab">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane name="first">
                <template slot="label">建设银行</template>
            </el-tab-pane>
            <el-tab-pane name="fourth">
                <template slot="label">支付宝</template>
            </el-tab-pane>
            <el-tab-pane name="fifth">
                <template slot="label">微信</template>
            </el-tab-pane>
            <el-tab-pane name="third">
                <template slot="label">中国银行</template>
            </el-tab-pane>
            <el-tab-pane name="seventh">
                <template slot="label">民生银行</template>
            </el-tab-pane>
            <el-tab-pane name="sixth">
                <template slot="label">交通银行</template>
            </el-tab-pane>
            <el-tab-pane name="second">
                <template slot="label">南京银行</template>
            </el-tab-pane>
        </el-tabs>
    </div>

    <div class="bankBillTable">
        <el-table
                v-loading="tableLoading"
                tooltip-effect="dark"
                :data="tableData"
                border
                size="small"
                :header-cell-style="{'text-align':'center', 'background-color': '#F5F5F5'}"
                :cell-style="{'text-align':'center', 'background-color': '#F5F5F5'}"
                :default-expand-all="true">

            <el-table-column type="expand">
                <template slot-scope="props">
                    <el-table
                            v-if="props.row.capitalBillDetailList && props.row.capitalBillDetailList.length"
                            :data="props.row.capitalBillDetailList"
                            border
                            size="small"
                            :header-cell-style="{'text-align':'center'}"
                            :cell-style="{'text-align':'center'}"
                            >
                        <el-table-column
                                prop="saleorder.saleorderNo"
                                label="订单号"
                                show-overflow-tooltip
                                width="200">
                            <template #default="{ row }">
                                <el-link type="primary" size="small" v-if="row.orderType == 1" @click="viewDetail(row)">
                                    {{row.saleorder.saleorderNo}}
                                </el-link>
                                <el-link type="primary" size="small" v-if="row.orderType == 2" @click="viewDetail(row)">
                                    {{row.buyorder.buyorderNo}}
                                </el-link>
                                <el-link type="primary" size="small" v-if="row.orderType == 3" @click="viewDetail(row)">
                                    {{row.afterSales.afterSalesNo}}
                                </el-link>
                                <el-link type="primary" size="small" v-if="row.orderType == 4" @click="viewDetail(row)">
                                    {{row.afterSales.afterSalesNo}}
                                </el-link>
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="saleorder.traderName"
                                show-overflow-tooltip
                                label="合同名称">
                        </el-table-column>
                        <el-table-column
                                prop="saleorder.addTime"
                                label="创建时间"
                                show-overflow-tooltip
                                width="200">
                            <template #default="{ row }">
                                <span v-if="row.saleorder.addTime">{{ formatDateStamp(row.saleorder.addTime) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="saleorder.validTime"
                                label="生效时间"
                                show-overflow-tooltip
                                width="200">
                            <template #default="{ row }">
                                <span v-if="row.saleorder.validTime">{{ formatDateStamp(row.saleorder.validTime) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="saleorder.totalAmount"
                                label="合同金额"
                                show-overflow-tooltip
                                width="200">
                        </el-table-column>
                        <el-table-column
                                prop="saleorder.receivedAmount"
                                label="已结款金额"
                                show-overflow-tooltip
                                width="200">
                        </el-table-column>
                        <el-table-column
                                prop="amount"
                                label="本次到款"
                                show-overflow-tooltip
                                width="200">
                        </el-table-column>
                        <el-table-column
                                prop="payee"
                                label="收款名称"
                                show-overflow-tooltip
                                width="400">
                        </el-table-column>
                        <el-table-column
                                prop="comments"
                                label="备注"
                                show-overflow-tooltip
                                width="400">
                        </el-table-column>
                    </el-table>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    label="序号"
                    type="index"
                    width="50">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="tranFlow"
                    label="流水号"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="accName1"
                    label="对方名称"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="cadbankNm"
                    label="对方开户机构"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="accno2"
                    label="对方账号"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="realTrandatetime"
                    label="交易时间"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="message"
                    label="摘要"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="amt"
                    label="收款金额"
                    width="100">
                <template #default="{ row }">
                    <span v-if="row.flag1 != 0">{{ row.amt }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="amt"
                    label="付款金额"
                    width="100">
                <template #default="{ row }">
                    <span v-if="row.flag1 == 0">{{ row.amt }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="amt1"
                    label="银行余额"
                    width="100">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="det"
                    label="备注"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="orderNo"
                    label="商户订单号"
                    show-overflow-tooltip
                    width="150">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="matchedAmount"
                    label="剩余结款金额"
                    width="100">
                <template #default="{ row }">
                    <span>{{ row.amt - row.matchedAmount }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="matchedObjectName"
                    label="匹配项目"
                    show-overflow-tooltip
                    width="100">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="financeVoucherNo"
                    label="金蝶凭证号"
                    show-overflow-tooltip
                    width="150">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="voucherNo"
                    label="云星空凭证号"
                    show-overflow-tooltip
                    width="100">
                <template #default="{ row }">
                    <span v-if="row.voucherDate">{{row.voucherDate}}</span>
                    <span v-if="row.voucherNo">{{row.voucherNo}}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop=""
                    label="结算方式"
                    show-overflow-tooltip
                    width="100">
                <template #default="{ row }">
                    <span v-if="row.settlementMethod == 3">银行承兑汇票</span>
                    <span v-else-if="row.bankTag == 4">支付宝</span>
                    <span v-else-if="row.bankTag == 5">微信</span>
                    <span v-else>银行转账</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    label="操作"
                    width="100">
                <template #default="{ row }">
                    <el-link type="primary" size="small" @click="viewReceipt(row)">
                        回单
                    </el-link>
                </template>
            </el-table-column>
        </el-table>

        <div style="display: flex;flex-direction:column;text-align:center;margin-top:10px;margin-left:10px">
            <div style="display: flex; justify-content: flex-end;">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total=total>
                </el-pagination>
            </div>
        </div>
    </div>

    <div class="payApplyFooter">

    </div>
</div>
<script src="${pageContext.request.contextPath}/static/api/bankBill/bank_bill_list.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    new Vue({
        el: '#app',
        data() {
            return {
                tableLoading: false,
                currentPage: 1,
                total: 0,
                activeName: 'first',
                formInline: {
                    accName1: '',
                    cadbankNm: '',
                    accno2: '',
                    message: '',
                    tranFlow: '',
                    buyorderNo: '',
                    traderName: '',
                    searchBegintime: '',
                    searchEndtime: '',
                    flag1: '-1',
                    syncBankFlag: 0,
                    bankType: 1,

                    pageNo: 1,
                    pageSize: 10
                },
                payTime: [],
                tableData: [],
                tabMap:{
                    first:1,
                    second:2,
                    third:3,
                    fourth:4,
                    fifth:5,
                    sixth:6,
                    seventh:7
                },
            }
        },
        created() {
            this.init();
            this.queryList();
        },
        methods: {
            init(){
                const date1 = new Date();
                const date2 = new Date();
                date1.setDate(date1.getDate() - 1);
                console.log("date1,date2", date1, date2);
                this.payTime = [date1,date2];
            },
            queryList(){
                this.toSearchTime()
                this.tableLoading = true;
                console.log("formInline", this.formInline);
                getBankBillList(this.formInline).then(res => {
                    this.tableData = res.data.data.bankBillList;
                    this.tableLoading = false;
                    this.total = res.data.data.page.totalRecord;
                    this.currentPage = res.data.data.page.pageNo;
                }).catch(error => {
                    this.tableLoading = false;
                });
            },
            toSearchTime(){
                console.log("payTime", this.payTime)
                if (this.payTime != null && this.payTime.length > 0) {
                    console.log("formatDate(this.payTime[0])", this.formatDateNew(this.payTime[0]));
                    console.log("formatDate(this.payTime[1])", this.formatDateNew(this.payTime[1]));

                    this.formInline.searchBegintime = this.formatDateNew(this.payTime[0]);
                    this.formInline.searchEndtime = this.formatDateNew(this.payTime[1]);
                }
            },
            handleClick(row) {
                const tabMap = {
                    first: 1,
                    second: 2,
                    third: 3,
                    fourth: 4,
                    fifth: 5,
                    sixth: 6,
                    seventh: 7
                };
                this.formInline.bankType = tabMap[row.name];
                this.formInline.pageSize = 10;
                this.formInline.pageNo = 1;
                this.formInline.currentPage = 1;
                this.queryList();
            },
            onSubmit() {
                this.queryList();
            },
            handleSizeChange(val) {
                this.formInline.pageSize = val;
                this.queryList();
            },
            handleCurrentChange(val) {
                this.formInline.pageNo = val;
                this.currentPage = val;
                this.queryList();
            },
            formatDateNew(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            },
            formatDateStamp(timestamp) {
                const date = new Date(timestamp);
                return this.formatDateNew(date); // 根据需要格式化日期
            },
            viewDetail(row) {
                console.log("row", row);
                if (row.orderType == 1) {
                    openTab("订单信息", '/order/saleorder/view.do?saleorderId=' + row.saleorder.saleorderId);
                } else if (row.orderType == 2) {
                    openTab("订单信息", '/finance/buyorder/viewBuyorder.do?buyorderId=' + row.buyorder.buyorderId);
                } else if (row.orderType == 3) {
                    debugger
                    if (row.afterSales.subjectType == 536){
                        // 采购售后
                        openTab("订单信息", '/order/newBuyorder/viewAfterSalesDetail.do?traderType=2&afterSalesId=' + row.afterSales.afterSalesId);
                    }else{
                        // 销售和三方
                        openTab("订单信息", '/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=' + row.afterSales.afterSalesId);
                    }
                } else if (row.orderType == 4) {
                    // 费用
                    openTab("订单信息", '/buyorderExpense/details.do?buyorderExpenseId=' + row.buyorderexpense.buyorderExpenseId);
                }
            },
            viewReceipt(row) {
                console.log("row", row.receiptUrl);
                if(row.receiptUrl == null || row.receiptUrl == ""){
                    layer.alert("暂无回单");
                }else{
                    window.open(row.receiptUrl);
                }
            },
            resetForm() {
                this.init();
                const bankType = this.formInline.bankType
                this.formInline = {
                    accName1: '',
                    cadbankNm: '',
                    accno2: '',
                    message: '',
                    tranFlow: '',
                    buyorderNo: '',
                    traderName: '',
                    searchBegintime: '',
                    searchEndtime: '',
                    flag1: '-1',
                    syncBankFlag: 0,
                    bankType: bankType,
                    pageNo: 1,
                    pageSize: 10
                };
                this.queryList();
            },

        }
    })
</script>
<style>
    .badge-item {
        margin-top:10px;
        height: 53px;
    }
    .form-inline {
        margin: 30px;

    }

    .separator {
        margin: 0 5px;
    }
    .form-line {
        display:flex;
        flex-direction:row;

    }

    .el-table--small .el-table__cell {
        padding: 8px 0;
    }

    .el-divider__text, .el-link {
        font-weight: 500;
        font-size: 12px;
    }

    .el-table thead tr:first-child th {
        color: #333; /* 可选：设置文字颜色 */
    }
    .el-form-item__label{
        font-size: 12px !important;
    }
    .el-tabs__item{
        font-size: 12px !important;
    }
    .el-table thead tr:first-child th {
        background-color: #f0f0f0; /* 设置背景色为灰色 */
        color: #333; /* 可选：设置文字颜色 */
    }
</style>
