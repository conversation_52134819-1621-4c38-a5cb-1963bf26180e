<%--
  Created by IntelliJ IDEA.
  User: lecon
  Date: 2020-08-13
  Time: 04:50
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<html>
<head>
    <title>SKU迁移列表</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/css/meinian/layui.css">
</head>
<body>
<style type="text/css">
    .div-spu {
        margin-top: 20px;
        margin-bottom: 10px;
        margin-left: 5px;
        margin-right: 5px;
    }
</style>
<div class="div-spu">
    <div>
        <div class="div-spu" style="overflow:auto;">
            <table id="spuConfirmTable" class="layui-table">
                <colgroup>
                    <col width="100">
                    <col width="150">
                    <col>
                </colgroup>
                <thead>
                <tr>
                    <th>订货号</th>
                    <th>SKU名称</th>
                    <th>推送状态</th>
                    <th>上下架状态</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty skuList}">
                    <c:forEach var="sku" items="${skuList}" varStatus="status">
                    <tr>
                        <td>${sku.skuNo}</td>
                        <td>${sku.showName}</td>
                        <td>${sku.pushedPlatformNames}</td>
                        <td>${sku.sellingStatusStr}</td>
                    </tr>
                </c:forEach>
                </c:if>
                <c:if test="${ empty skuList}">
                    <tr>
                        <td colspan="4">
                            <div><i class="vd-icon icon-caution1"></i></div>
                            没有匹配的数据
                        </td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </div>
</div>
</body>
</html>
