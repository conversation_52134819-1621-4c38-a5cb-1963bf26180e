package com.vedeng.flash.dto;

import lombok.Data;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;

import java.util.List;

@Data
public class ProcessResultDto {
    //历史流程
    List<HistoricProcessInstance> historicProcessInstanceList;
    //任务节点
    List<HistoricTaskInstance> historicTaskInstanceList;

    List<ProcessInstance> processInstanceList;

    //任务节点
    List<Task> taskInstanceList;
    Integer total;
}
