package com.vedeng.goods.dao;


import com.vedeng.goods.command.SpuAddCommand;
import com.vedeng.goods.model.BaseCategory;
import com.vedeng.goods.model.FirstAndSecondCategoryInfo;
import com.vedeng.goods.model.dto.CategorySaleInfoDto;
import com.vedeng.goods.model.dto.MoveCategoryDto;
import com.vedeng.goods.model.vo.BaseCategoryVo;
import com.vedeng.goods.model.vo.CategoryToOpVo;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;
import java.util.Map;

@Named("baseCategoryMapper")
public interface BaseCategoryMapper {
    int deleteByPrimaryKey(Integer baseCategoryId);
    List<BaseCategoryVo> getCategoryListByParentId(Integer parentId);
    int insert(BaseCategory record);

    int insertSelective(BaseCategory record);

    BaseCategoryVo selectByPrimaryKey(Integer baseCategoryId);

    int updateByPrimaryKeySelective(BaseCategory record);

    int updateByPrimaryKey(BaseCategory record);

    /**
     * @description 据属性Id获取已应用的商品分类列表
     * <AUTHOR>
     * @param
     * @date 2019/5/22
     */
    List<BaseCategoryVo> getBaseCategoryListPage(Map<String,Object> map);

    /**
     * @description 据属性Id获取已应用的商品分类列表
     * <AUTHOR>
     * @param
     * @date 2019/5/22
     */
    Map<String,Long> getBaseCategoryList(Integer baseCategoryId);

    /**
     * @description 获取商品分类列表
     * <AUTHOR>
     * @param
     * @date 2019/5/23
     */
    List<BaseCategoryVo> getFirstCategoryListPage(Map<String,Object> map);

    /**
     * @description 获取商品分类列表
     * <AUTHOR>
     * @param
     * @date 2019/5/23
     */
    List<BaseCategoryVo> getSecondCategoryList(Map<String,Object> map);

    /**
     * @description 获取商品分类列表
     * <AUTHOR>
     * @param
     * @date 2019/5/23
     */
    List<BaseCategoryVo> getThirdCategoryList(Map<String,Object> map);

    /**
     * @description 根据条件获取下级分类
     * <AUTHOR>
     * @param
     * @date 2019/5/22
     */
    List<BaseCategoryVo> getCategoryListByIds(Map<String,Object> map);

    /**
     * @description 根据三级分类ID获取该三级分类的信息
     * <AUTHOR>
     * @param
     * @date 2019/5/22
     */
    List<BaseCategoryVo> getthirdCategoryListById(BaseCategoryVo baseCategoryVo);

    /**
     * @description 删除一级商品分类
     * <AUTHOR>
     * @param map
     * @return
     * @date 2019/5/24
     */
    Integer deleteCategory(Map<String,Object> map);

    /**
     * @description 验证分类是否重复
     * <AUTHOR>
     * @param baseCategoryVo
     * @return
     * @date 2019/5/24
     */
    Integer checkRepeatCategory(BaseCategoryVo baseCategoryVo);

    /**
     * 根据三级分类的ID和指定的连接符号获取分类路径。(eg:一级分类>二级分类>三级分类)
     *
     * @param thirdCategoryId
     * @param symbol 指定连接符号 {@link com.vedeng.common.constant.ErpConst.Symbol}
     * <AUTHOR>
     * @date 2019/5/24
     * @return
     */
    String getOrganizedCategoryNameById(@Param("thirdCategoryId") Integer thirdCategoryId, @Param("symbol") String symbol);

    /**
     * @description 根据关键词获取商品关联的分类ID列表
     * @param keyWords
     * <AUTHOR>
     * @date 2019/5/24
     * @return
     */
    List<Integer> getCategoryIdByKeyWords(String keyWords);

    /**
     * @description 根据关键词和分类ID获取分类列表
     * @param keyWords
     * <AUTHOR>
     * @date 2019/5/24
     * @return
     */
    List<BaseCategoryVo> getCategoryListByKeyWords(@Param("keyWords") String keyWords , @Param("list") List<Integer> list);

    List<BaseCategoryVo> getSecondCategoryListForNameQuery(Map<String, Object> map);

    List<BaseCategoryVo> getThirdCategoryListForNameQuery(Map<String, Object> map);

    List<BaseCategory> getFirstCategory();

    List<BaseCategory> getSecondCategory(Integer baseCategoryId);

    BaseCategory getBaseCategoryInfoById(Integer categoryId);

    void moveCatrgory(@Param("baseCatrgoryId")Integer baseCatrgoryId, @Param("parentId")Integer parentId);

    List<MoveCategoryDto> getMoveCategoryDtoByCategoryId(Integer categoryId);

    List<FirstAndSecondCategoryInfo> getFirstAndSecondCategoryListByKeyWords(@Param("keyWords") String keyWords,@Param("list") List<Integer> idList);

    /**
     * 获取三级分类的完整层级关系。eg:一级分类Id,二级分类Id,三级分类Id
     *
     * @param thirdCategoryId
     * @return
     */
    String getAllHierarchyFor(Integer thirdCategoryId);

    /**
     * <b>Description:</b>获取一、二、三级分类<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/8/10
     */
    List<CategoryToOpVo> getCategoryLevelAllListPage(Map<String,Object> map);

    /**
     * <b>Description:</b>根据三级分类Id查询一二三级分类<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/8/10
     */
    CategoryToOpVo getCategoryLevelAllById(@Param("categoryId")Integer categoryId);

    /**
     * <b>Description:</b>根据三级分类Id集合查询一二三级分类<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/8/10
     */
    List<CategoryToOpVo> getCategoryLevelAllByIdList(List<Integer> categoryIds);

    /**
     * 根据三级分类id获关联的spu编号
     *
     * @param thirdCategoryId
     * @return
     */
    List<Integer> listSpuIdiUnderThirdCategory(Integer thirdCategoryId);
    String getAllLevelCategoryNameById(@Param("categoryId")Integer categoryId);

    /**
     * 获取三级分类下审核通过的spu信息
     *
     * @param baseCategoryId
     * @return
     */
    List<SpuAddCommand> getValidSpuInfoByCategoryId(Integer baseCategoryId);

    String getDepartmentByBaseCategoryId(@Param("categoryId")Integer baseCategoryId);

    /**
     * 根据名称查找分类
     * @param baseCategoryName
     * @param isDeleted
     * @return
     */
    List<BaseCategory> findByBaseCategoryNameAndIsDeleted(@Param("baseCategoryName")String baseCategoryName,@Param("isDeleted")Integer isDeleted);

    /**
     * 主键ID批量获取分类信息
     *
     * @param baseCategoryIds
     * @return
     */
    List<BaseCategoryVo> getBaseCategoryListByIds(@Param("baseCategoryIds")List<Integer> baseCategoryIds);

    /**
     * 获取近6个月分类销量信息
     *
     * @return
     */
    List<CategorySaleInfoDto> getRecent6MonthCategorySaleInfo();

    /**
     * 更新近6个月销量信息
     *
     * @param categorySaleInfoList
     * @return
     */
    int batchUpdateRecent6MonthCategorySaleNum(@Param("categorySaleInfoList") List<CategorySaleInfoDto> categorySaleInfoList);

}