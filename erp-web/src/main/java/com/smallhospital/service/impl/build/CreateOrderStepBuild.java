package com.smallhospital.service.impl.build;

import com.smallhospital.service.impl.chain.CreateContactStep;
import com.smallhospital.service.impl.chain.CreateOrderStep;
import com.smallhospital.service.impl.chain.CreateSaleOrderGoodsStep;
import com.smallhospital.service.impl.chain.CreateSaleOrderStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CreateOrderStepBuild {

   @Autowired
   private CreateSaleOrderStep createSaleOrderStep;

   @Autowired
   private CreateSaleOrderGoodsStep createSaleOrderGoodsStep;

   @Autowired
   private CreateContactStep createContactStep;

    /**
     * 组装创建订单的步骤
     * @return
     */
   public CreateOrderStep getCreateOrderStep(){
       createSaleOrderStep.setNext(createSaleOrderGoodsStep);
       createSaleOrderGoodsStep.setNext(createContactStep);
       return createSaleOrderStep;
   }

}
