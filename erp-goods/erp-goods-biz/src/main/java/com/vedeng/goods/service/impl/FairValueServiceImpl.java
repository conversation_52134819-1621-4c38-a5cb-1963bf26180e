package com.vedeng.goods.service.impl;

import cn.hutool.core.date.DateUtil;
import com.vedeng.goods.domain.entity.FairValueEntity;
import com.alibaba.fastjson.JSON;
import com.vedeng.goods.domain.entity.FairValueEntity;
import com.vedeng.goods.dto.FairValueDto;
import com.vedeng.goods.dto.HistoryDataDto;
import com.vedeng.goods.dto.SaleOrderDataDto;
import com.vedeng.goods.mapper.FairValueMapper;
import com.vedeng.goods.mapstruct.FairValueConvertor;
import com.vedeng.goods.service.FairValueApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Optional;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/15 13:18
 */
@Service
@Slf4j
public class FairValueServiceImpl implements FairValueApiService {

    @Resource
    private FairValueMapper fairValueMapper;

    @Autowired
    private FairValueConvertor fairValueConvertor;

    @Override
    public void batchInsert(List<FairValueDto> fairValueDtoList) {
        fairValueMapper.batchInsert(fairValueConvertor.toEntity(fairValueDtoList));
    }

    @Override
    public void batchUpdate(List<FairValueDto> fairValueDtoList) {
        fairValueMapper.batchUpdate(fairValueConvertor.toEntity(fairValueDtoList));
    }

    @Override
    public FairValueDto selectByGoodsId(Integer goodsId) {
        return Optional.ofNullable(fairValueConvertor.toDto(fairValueMapper.selectByGoodsId(goodsId))).orElse(new FairValueDto());
    }

    @Override
    public FairValueDto getFairPriceDto(Integer fairValueId) {
        FairValueEntity fairValueEntity = fairValueMapper.selectByPrimaryKey(fairValueId);
        FairValueDto fairValueDto = new FairValueDto();
        fairValueDto.setSkuName(fairValueEntity.getSkuName());
        //解析sku历史json
        if(fairValueEntity.getHistoryData() != null && !fairValueEntity.getHistoryData().equals("")) {
            List<HistoryDataDto> historyDataDtoList = JSON.parseArray(fairValueEntity.getHistoryData().toJSONString(),HistoryDataDto.class);
            historyDataDtoList = historyDataDtoList.stream().sorted(Comparator.comparing(HistoryDataDto :: getGenerateDate).reversed()).collect(Collectors.toList());
            fairValueDto.setHistoryDataDtoList(historyDataDtoList);
        }
        //解析sku当月计算销售数据
        if(fairValueEntity.getSaleOrderData() != null && !fairValueEntity.getSaleOrderData().equals("")) {
            List<SaleOrderDataDto> saleOrderDataDtoList = JSON.parseArray(fairValueEntity.getSaleOrderData().toJSONString(),SaleOrderDataDto.class);
            saleOrderDataDtoList = saleOrderDataDtoList.stream().sorted(Comparator.comparing(SaleOrderDataDto :: getSaleDate).reversed()).collect(Collectors.toList());
            fairValueDto.setSaleOrderDataDtoList(saleOrderDataDtoList);
        }
        return fairValueDto;
    }


}