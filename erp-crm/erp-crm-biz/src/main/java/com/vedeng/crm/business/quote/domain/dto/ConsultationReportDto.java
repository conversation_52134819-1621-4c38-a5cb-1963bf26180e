package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 发起咨询
 */
@Data
public class ConsultationReportDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 报价单id
     */
    @NotNull(message = "报价单id不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    /**
     * 报价单商品id
     */
//    @NotEmpty(message = "报价单商品id不能为空", groups = DefaultGroup.class) 空行也发起咨询
    private List<Integer> quoteorderGoodsIds;

    /**
     * 货期
     *  1是 0否
     */
    @NotNull(message = "货期不能为空", groups = DefaultGroup.class)
    private Integer isConsulDeliveryCycle;

    /**
     * 价格
     *  1是 0否
     */
    @NotNull(message = "价格不能为空", groups = DefaultGroup.class)
    private Integer isConsulPrice;

    /**
     * 报备
     *  1是 0否
     */
    @NotNull(message = "报备不能为空", groups = DefaultGroup.class)
    private Integer isConsulReport;

    /**
     * 咨询人userId
     */
    @NotEmpty(message = "咨询人不能为空", groups = DefaultGroup.class)
    private List<Integer> toUserList;

}
