package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchPayExpensesDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesGoodsDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeePayExpensesQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeePayExpensesApiService;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: ckt
 * @Date: 2023/6/5
 * @Description: 销售售后录票（蓝字有效）进项票，组装数据
 *
 */

@Slf4j
@Service
public class SaleOrderAfterSaleInputFeeProcessor extends BaseProcessor<BatchInvoiceDto, BatchPayExpensesDto> {
    @Autowired
    private KingDeePayExpensesApiService kingDeePayExpensesApiService;
    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;
    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;
    @Autowired
    private PayApplyApiService payApplyApiService;
    @Autowired
    private BatchAfterSalesGoodsDtoMapper batchAfterSalesGoodsDtoMapper;

    @Override
    public BatchPayExpensesDto doProcess(BatchInvoiceDto batchInvoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售售后录票(蓝字有效)进项票,组装数据,BatchInvoiceDto:{}", JSONUtil.toJsonStr(batchInvoiceDto));
        //组合对象
        BatchPayExpensesDto batchPayExpensesDto = new BatchPayExpensesDto();
        //普票对象
        InPutFeePlainInvoiceDto plainInvoiceDto;
        //专票对象
        InPutFeeSpecialInvoiceDto specialInvoiceDto;
        //判断是否专票
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchInvoiceDto.getInvoiceType());
        // 获取费用应付单
        List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses = kingDeePayExpensesApiService.getKingDeePayExpenses(batchInvoiceDto.getInvoiceId());
        if (CollUtil.isEmpty(kingDeePayExpenses)) {
            log.info("销售售后录票(蓝字有效)费用应付单,数据不存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        log.info("销售售后录票(蓝字有效)费用应付单,{}", JSON.toJSONString(kingDeePayExpenses));
        if (batchInvoiceDto.getRelatedId() == null) {
            log.info("销售售后录票(蓝字有效)进项票,发票id为{},无法找到对应订单,请检查数据", batchInvoiceDto.getInvoiceId());
            return null;
        }
        if (batchInvoiceDto.getAfterSalesId() == null) {
            log.info("销售售后录票(蓝字有效)进项票,发票id为{},无法找到对应售后单,请检查数据", batchInvoiceDto.getInvoiceId());
            return null;
        }
        //付款申请记录-支付对象的供应商ID
        Integer supplierId = payApplyApiService.getPayApplyMaxRecord(batchInvoiceDto.getAfterSalesId());
        if (ObjectUtil.isNull(supplierId)){
            log.error("销售售后录票(蓝字有效)费用应付单,未查到供应商编码,BatchInvoiceDto:{}",JSONUtil.toJsonStr(batchInvoiceDto));
            return null;
        }
        //获取售后单中特殊商品
        BatchAfterSalesGoodsDto specialGoods = batchAfterSalesGoodsDtoMapper.getSpecialGoods(batchInvoiceDto.getAfterSalesId());
        log.info("销售售后录票(蓝字有效)费用应付单,售后单的特殊商品,specialGoods:{}",JSONUtil.toJsonStr(specialGoods));
        String kingDeeFCostID = batchAfterSalesGoodsDtoMapper.getKingDeeFCostID(specialGoods.getSku());
        if (StringUtils.isBlank(kingDeeFCostID)){
            log.info("销售售后录票(蓝字有效)进项票，未查到金蝶费用编码,skuNo:{}",specialGoods.getSku());
        }
        //售后单号
        String afterSaleOrderNo = batchInvoiceDtoMapper.findAfterSaleOrderNoByAfterSaleId(batchInvoiceDto.getRelatedId());
        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        //税率
        String taxRate = (Objects.isNull(batchInvoiceDto.getRatio()) || !isSpecialInvoice) ? "0.00" : decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        //发票详情
        List<BatchInvoiceDetailDto> invoiceDetailList = batchInvoiceDetailDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        Assert.notEmpty(invoiceDetailList,"销售售后录票(蓝字有效)进项票,发票明细列表为空,invoiceID:{}",batchInvoiceDto.getInvoiceId());
        if (isSpecialInvoice){
            specialInvoiceDto = new InPutFeeSpecialInvoiceDto();
            this.inPutFeeSpecialInvoice(specialInvoiceDto,batchInvoiceDto,invoiceDetailList
                    ,taxRate,afterSaleOrderNo,kingDeePayExpenses,supplierId,kingDeeFCostID,specialGoods.getSku());
            batchPayExpensesDto.setInPutFeeSpecialInvoiceDto(specialInvoiceDto);
        }else {
            plainInvoiceDto = new InPutFeePlainInvoiceDto();
            this.inputFeePlainInvoice(plainInvoiceDto,batchInvoiceDto,invoiceDetailList
                    ,taxRate,afterSaleOrderNo,kingDeePayExpenses,supplierId,kingDeeFCostID);
            batchPayExpensesDto.setInPutFeePlainInvoiceDto(plainInvoiceDto);
        }
        return batchPayExpensesDto;
    }

    /**
     * 销售售后录票(蓝字有效)进项票-普票
     */
    private void inputFeePlainInvoice(InPutFeePlainInvoiceDto plainInvoiceDto
            , BatchInvoiceDto batchInvoiceDto
            , List<BatchInvoiceDetailDto> invoiceDetailList
            , String taxRate
            , String afterSaleOrderNo
            , List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses
            , Integer supplierId
            , String kingDeeFCostID) {
        log.info("销售售后录票(蓝字有效)进项票-普票,BatchInvoiceDto:{}",batchInvoiceDto);
        plainInvoiceDto.setFid("0");
        plainInvoiceDto.setFQzokBddjtid(Convert.toStr(batchInvoiceDto.getInvoiceId()));
        plainInvoiceDto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
        plainInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        plainInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        plainInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
        plainInvoiceDto.setFsupplierid(Convert.toStr(supplierId));
        plainInvoiceDto.setFRedBlue("0");
        for (BatchInvoiceDetailDto d : invoiceDetailList) {
            //发票明细
            InPutFeePlainInvoiceDetailDto detailDto = new InPutFeePlainInvoiceDetailDto();
            detailDto.setFexpenseid(kingDeeFCostID);
            detailDto.setFQty(Convert.toStr(d.getNum()));
            detailDto.setFunitprice(d.getTotalAmount().divide(d.getNum(),6,RoundingMode.HALF_UP));
            detailDto.setFtaxrate(taxRate);
            detailDto.setFQzokBddjhid(Convert.toStr(d.getInvoiceDetailId()));
            detailDto.setFQzokYsddh(afterSaleOrderNo);
            detailDto.setFQzokGsywdh(afterSaleOrderNo);
            detailDto.setFsourcetype("AP_Payable");
            detailDto.setFQzokYwlx("售后费用");
            plainInvoiceDto.getFPUREXPINVENTRY().add(detailDto);

            //关联关系
            InPutFeePlainInvoiceDetailLinkDto linkDto = new InPutFeePlainInvoiceDetailLinkDto();
            linkDto.setFLinkId("0");
            linkDto.setFpurexpinventryLinkFruleid("IV_PayableToPUREXVATIN");
            linkDto.setFpurexpinventryLinkFstablename("T_AP_PAYABLEENTRY");
            linkDto.setFpurexpinventryLinkFstableid("0");
            KingDeePayExpensesQueryResultDto resultDto = kingDeePayExpenses.stream()
                    .filter(s -> s.getF_QZOK_BDDJHID().equals(Convert.toStr(d.getInvoiceDetailId())))
                    .findFirst().orElseThrow(() -> new KingDeeException("销售售后录票(蓝字有效)进项票-普票，未能关联上应付单对象，发票id：" + batchInvoiceDto.getInvoiceId()));
            linkDto.setFpurexpinventryLinkFsbillid(resultDto.getFID());
            linkDto.setFpurexpinventryLinkFsid(resultDto.getFEntityDetail_FEntryId());
            linkDto.setFpurexpinventryLinkFamountforDold(Convert.toStr(d.getTotalAmount()));
            linkDto.setFpurexpinventryLinkFamountforD(Convert.toStr(d.getTotalAmount()));

            detailDto.getFPUREXPINVENTRY_Link().add(linkDto);
        }
    }

    /**
     * 销售售后录票(蓝字有效)进项票-专票
     */
    private void inPutFeeSpecialInvoice(InPutFeeSpecialInvoiceDto specialInvoiceDto
            , BatchInvoiceDto batchInvoiceDto
            , List<BatchInvoiceDetailDto> invoiceDetailList
            , String taxRate
            , String afterSaleOrderNo
            , List<KingDeePayExpensesQueryResultDto> kingDeePayExpenses
            , Integer supplierId
            , String kingDeeFCostID
            , String bdSku) {
        log.info("销售售后录票(蓝字有效)进项票-专票,BatchInvoiceDto:{}",batchInvoiceDto);
        specialInvoiceDto.setFid("0");
        specialInvoiceDto.setFQzokBddjtid(Convert.toStr(batchInvoiceDto.getInvoiceId()));
        specialInvoiceDto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
        specialInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        specialInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        specialInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
        specialInvoiceDto.setFsupplierid(Convert.toStr(supplierId));
        specialInvoiceDto.setFRedBlue("0");
        for (BatchInvoiceDetailDto d : invoiceDetailList) {
            //发票明细
            InPutFeeSpecialInvoiceDetailDto detailDto = new InPutFeeSpecialInvoiceDetailDto();
            detailDto.setFexpenseid(kingDeeFCostID);
            detailDto.setFQty(Convert.toStr(d.getNum()));
            detailDto.setFunitprice(d.getTotalAmount().divide(d.getNum(),6,RoundingMode.HALF_UP));
            detailDto.setFtaxrate(taxRate);
            detailDto.setFQzokBddjhid(Convert.toStr(d.getInvoiceDetailId()));
            detailDto.setFQzokYsddh(afterSaleOrderNo);
            detailDto.setFQzokGsywdh(afterSaleOrderNo);
            detailDto.setFsourcetype("AP_Payable");
            detailDto.setFQzokBdsku(bdSku);
            detailDto.setFQzokYwlx("售后费用");
            specialInvoiceDto.getFPUREXPINVENTRY().add(detailDto);

            //关联关系
            InPutFeeSpecialInvoiceDetailLinkDto linkDto = new InPutFeeSpecialInvoiceDetailLinkDto();
            linkDto.setFLinkId("0");
            linkDto.setFpurexpinventryLinkFruleid("IV_PayableToPUREXVATIN");
            linkDto.setFpurexpinventryLinkFstablename("T_AP_PAYABLEENTRY");
            linkDto.setFpurexpinventryLinkFstableid("0");
            KingDeePayExpensesQueryResultDto resultDto = kingDeePayExpenses.stream()
                    .filter(s -> s.getF_QZOK_BDDJHID().equals(Convert.toStr(d.getInvoiceDetailId())))
                    .findFirst().orElseThrow(() -> new KingDeeException("销售售后录票(蓝字有效)进项票-专票，未能关联上应付单对象，发票id：" + batchInvoiceDto.getInvoiceId()));
            linkDto.setFpurexpinventryLinkFsbillid(resultDto.getFID());
            linkDto.setFpurexpinventryLinkFsid(resultDto.getFEntityDetail_FEntryId());
            linkDto.setFpurexpinventryLinkFamountforDold(Convert.toStr(d.getTotalAmount()));
            linkDto.setFpurexpinventryLinkFamountforD(Convert.toStr(d.getTotalAmount()));

            detailDto.getFPUREXPINVENTRY_Link().add(linkDto);
        }

    }

}
