function isEmpty(obj) {
    if(typeof obj == "undefined" || obj == null || obj == ""){
        return true;
    }else{
        return false;
    }
}

function validatorRequiredConditionDisable(validator, element) {
    var name = element.name;
    var rules = validator.settings.rules;
    if (!isEmpty(rules) && rules.hasOwnProperty(name)) {
        var targetRule=rules[name];
        if(!targetRule.hasOwnProperty('required')){
            return true
        }
    }
    return false
}

//存储条件温度校验规则
jQuery.validator.addMethod("checkTem", function(value, element) {
    var lowerValue = $('#lessTemperature').val();
    var upperValue = $('#greatTemperature').val();

    var returnVal = true;
    var lessValue = parseInt(lowerValue);
    var greatValue = parseInt(upperValue);
    if (lessValue > greatValue) {
        returnVal = false;
    }
    if (isNaN(lessValue) || isNaN(greatValue)) {
        return false;
    }
    return returnVal;
}, '温度范围有误，请重新填写');

//存储条件湿度校验规则
jQuery.validator.addMethod("checkHum", function(value, element) {
    var lowerValue = $("input[name=storageConditionHumidityLowerValue]").val();
    var upperValue = $("input[name=storageConditionHumidityUpperValue]").val();

    if (isEmpty(lowerValue) && isEmpty(upperValue) && validatorRequiredConditionDisable(this, element)) {
        return true;
    }
    var lessValue = parseInt(lowerValue);
    var greatValue = parseInt(upperValue);
    if (isNaN(lessValue) || isNaN(greatValue)) {
        return false;
    }
    return lessValue < greatValue;
}, '湿度度范围有误，请重新填写');


$('input[name=storageConditionTemperature]').change(function () {
    if(this.value==='4'&& this.checked){
        $('#lessTemperature').attr('disabled', false)
        $('#greatTemperature').attr('disabled', false)
    }else {
        $('#lessTemperature').attr('disabled', true)
        $('#greatTemperature').attr('disabled', true)
    }
});