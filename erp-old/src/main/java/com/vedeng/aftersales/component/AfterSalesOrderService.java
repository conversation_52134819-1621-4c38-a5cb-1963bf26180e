package com.vedeng.aftersales.component;

import com.vedeng.aftersales.component.dto.AfterSaleOrderAddDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderCloseDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderModifyDto;

/**
 * 售后单服务类
 */
public interface AfterSalesOrderService {

    /**
     * 新增售后单
     * @param afterSaleOrderAddDto
     */
    void createAfterSalesOrder(Object afterSaleOrderAddDto) throws Exception;

    /**
     * 修改售后单
     * @param afterSaleOrderModifyDto 待修改的售后单信息
     * @return 修改结果
     */
     void modifyAfterSalesOrder(Object afterSaleOrderModifyDto) throws Exception;

    /**
     * 关闭销售单
     * @param afterSaleOrderCloseDto
     */
     void closeAfterSalesOrder(Object afterSaleOrderCloseDto) throws Exception;
}
