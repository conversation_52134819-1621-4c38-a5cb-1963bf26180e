package com.vedeng.erp.saleorder.enums;

import com.vedeng.common.core.exception.ServiceException;

/**
 * 销售单付款状态(收款状态) 0未付款 1部分付款 2全部付款
 */
public enum SaleOrderPaymentStatusEnum {

    NULL(0, "未付款"),
    PART(1, "部分付款"),
    ALL(2, "全部付款"),
    ;
    private Integer code;

    private String desc;

    SaleOrderPaymentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(Integer code){
        for(SaleOrderPaymentStatusEnum v : values()){
            if(v.getCode().equals(code)){
                return v.getDesc();
            }
        }
        return "";
    }
}
