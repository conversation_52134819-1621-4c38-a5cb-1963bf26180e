package com.vedeng.dwh.service;

import com.vedeng.dwh.model.dto.DwhWorkbenchDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto;

import java.util.List;

/**
 * Dwh商机查询service.
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/4 9:05 上午.
 * @author: Tomcat.Hui.
 */
public interface DwhBussinessChanceService {

    /**
     * 获取历史所有商机概况.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 2:44 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    DwhWorkbenchOverviewDto getHisBussinessChanceOverviewByUser(Integer userId, Long startTime, Long endTime);

    /**
     * 获取昨日新增商机概况.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 2:44 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    DwhWorkbenchOverviewDto getYesterdayBussinessChanceOverviewByUser(Integer userId, Long startTime, Long endTime);

    /**
     * 预计到款.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 2:48 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    DwhWorkbenchOverviewDto getExpectBussinessChanceOverviewByUser(Integer userId, Long startTime, Long endTime);

    /**
     * 商机状态分布.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 3:03 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getBussinessChanceStatusOverviewByUser(Integer userId, Long startTime, Long endTime);

    /**
     * .
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 3:07 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    List<DwhWorkbenchDto> getImportantBussinessChancesByUser(Integer userId, Long startTime, Long endTime);

    // changed by Randy.Xu   .Desc: begin

    /**
     * 按照部门查询商机概况.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 3:33 下午.
     * @author: Tomcat.Hui.
     * @param orgIds: .
     * @param type: 1:按照一级部门分组;2:按照2级部门分组;3:按照3级部门分组.
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getHisBussinessChanceOverviewByDept(List<Integer> orgIds,Integer type,Long startTime, Long endTime);


    /**
     * 按照部门查询商机概况.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 3:33 下午.
     * @author: Tomcat.Hui.
     * @param orgIds: .
     * @param type: 1:按照一级部门分组;2:按照2级部门分组;3:按照3级部门分组.
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getYesterdayBussinessChanceOverviewByDept(List<Integer> orgIds,Integer type,Long startTime, Long endTime);

    // changed by Randy.Xu   .Desc: end

    /**
     * 获取按照部门分组的商机状态分布.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 3:49 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getBussinessChanceStatusOverviewByDept(List<Integer> orgIds,Integer type, Long startTime, Long endTime);

    /**
     * 按照部门分组取重点商机聚合.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 4:23 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    List<DwhWorkbenchOverviewDto> getImportantBussinessChancesByDept(List<Integer> orgIds,Integer type, Long startTime, Long endTime);


    // add by Randy.Xu 2020/11/5 16:28 .Desc: . begin
    /**
     * 按部门分组取商机聚合
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/5 16:29.
     * @author: Randy.Xu.
     * @param userId
     * @param startTime
     * @param endTime
     * @return: com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto.
     * @throws:  .
     */
    List<DwhWorkbenchOverviewDto> getExpectBussinessChanceOverviewByDept(List<Integer> orgIds,Integer type, Long startTime, Long endTime);
    // add by Randy.Xu 2020/11/5 16:28 .Desc: . end
}
