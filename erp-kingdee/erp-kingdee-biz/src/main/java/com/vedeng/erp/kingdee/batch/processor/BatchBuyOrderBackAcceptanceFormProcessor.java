package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAttachmentDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseBackService;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 采购售后出库单附件
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchBuyOrderBackAcceptanceFormProcessor extends BaseProcessor<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto> {

    public static final String ZERO = "0";

    @Value("${oss_http}")
    private String ossHttp;

    @Autowired
    private BatchAttachmentDtoMapper batchAttachmentDtoMapper;

    @Autowired
    private KingDeePurchaseBackService kingDeePurchaseBackService;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;


    @Override
    public KingDeeFileDataDto doProcess(BatchWarehouseGoodsOutInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购退货出库单数据：{}", JSON.toJSONString(dto));
        BatchAttachmentDto attachmentDto = BatchAttachmentDto.builder()
                .attachmentType(462)
                .attachmentFunction(4213)
                .relatedId(dto.getWarehouseGoodsOutInId().intValue())
                .build();
        BatchAttachmentDto batchAttachmentDto = batchAttachmentDtoMapper.purchaseInfindByQuery(attachmentDto);
        if (Objects.isNull(batchAttachmentDto) || StrUtil.isEmpty(batchAttachmentDto.getUri()) || StrUtil.isEmpty(batchAttachmentDto.getDomain())) {
            log.info("暂无采购退货出库单附件: {}", JSON.toJSONString(dto));
            return null;
        }
        String fileUrl = ossHttp + batchAttachmentDto.getDomain() + batchAttachmentDto.getUri();
        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.PURCHASE_BACK)
                .erpId(dto.getWarehouseGoodsOutInId().toString())
                .url(fileUrl)
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }

        log.info("上传采购退货出库单附件入参对象：{}", JSON.toJSONString(batchAttachmentDto));
        KingDeePurchaseBackDto kingDeePurchaseBackDto = new KingDeePurchaseBackDto();
        kingDeePurchaseBackDto.setFBillNo(dto.getOutInNo());
        kingDeePurchaseBackService.query(kingDeePurchaseBackDto);
        if (kingDeePurchaseBackDto.getFId() == null || ZERO.equals(kingDeePurchaseBackDto.getFId())) {
            log.info("上传采购退货出库单附件,出库单未推送金蝶：{}", dto.getOutInNo());
            return null;
        }

        String suffix = FileInfoUtils.getSuffix(fileUrl);
        String name = StrUtil.isEmpty(batchAttachmentDto.getName()) ? "file" + suffix : batchAttachmentDto.getName() + suffix;
        return KingDeeFileDataDto.builder()
                .fileName(name)
                .aliasFileName(batchAttachmentDto.getName())
                .billNo(dto.getOutInNo())
                .formId(kingDeePurchaseBackDto.getFormId())
                .isLast(true)
                .fId(kingDeePurchaseBackDto.getFId())
                .url(fileUrl)
                .erpId(dto.getWarehouseGoodsOutInId().toString())
                .businessId(kingDeePurchaseBackDto.getFormId() + dto.getWarehouseGoodsOutInId().toString())
                .build();
    }
}
