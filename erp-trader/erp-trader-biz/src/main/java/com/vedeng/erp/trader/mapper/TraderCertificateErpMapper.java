package com.vedeng.erp.trader.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.trader.domain.entity.TraderCertificateErpEntity;
import com.vedeng.erp.trader.dto.TraderCertificateErpDto;


/**
 * <AUTHOR>
 * @description ${end}
 * @date 2024/2/4 14:31
 **/
public interface TraderCertificateErpMapper {
    int deleteByPrimaryKey(Integer traderCertificateId);

    int insert(TraderCertificateErpEntity record);

    int insertSelective(TraderCertificateErpEntity record);

    TraderCertificateErpEntity selectByPrimaryKey(Integer traderCertificateId);

    int updateByPrimaryKeySelective(TraderCertificateErpEntity record);

    int updateByPrimaryKey(TraderCertificateErpEntity record);

    void updatTraderBusinessCard(TraderCertificateErpDto erpDto);

    List<TraderCertificateErpEntity> selectByRelatedId(@Param("relatedId")Integer relatedId);

	
}