package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.SaleOrderExpenseBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/1/30 14:11
 * @version 1.0
 */
@JobHandler(value = "SaleOrderExpenseBatchTask")
@Component
public class SaleOrderExpenseBatchTask extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private SaleOrderExpenseBatchJob saleOrderExpenseBatchJob;

    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================销售订单费用商品流程batch开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = saleOrderExpenseBatchJob.saleOrderVirtualGoodsFlowJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================销售订单费用商品流程batch结束====================");
        return SUCCESS;
    }
}
