package com.vedeng.customerbillperiod.dao;

import com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail;
import com.vedeng.customerbillperiod.model.vo.CustomerBillPeriodOverdueManagementDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerBillPeriodOverdueManagementDetailMapper {
    int deleteByPrimaryKey(Long billPeriodOverdueManagementDetailId);

    int insert(CustomerBillPeriodOverdueManagementDetail record);

    int insertSelective(CustomerBillPeriodOverdueManagementDetail record);

    CustomerBillPeriodOverdueManagementDetail selectByPrimaryKey(Long billPeriodOverdueManagementDetailId);

    int updateByPrimaryKeySelective(CustomerBillPeriodOverdueManagementDetail record);

    /**
     * 根据账期使用集合查询账期逾期管理明细
     * @param useDetailIdList 账期使用记录集合
     * @param typeList 使用类型集合
     * @return 账期逾期管理明细集合
     */
    List<CustomerBillPeriodOverdueManagementDetail> getOverdueManageDetailByUseDetailIdList(@Param("useDetailIdList") List<Long> useDetailIdList,
                                                                                            @Param("typeList") List<Integer> typeList);



    /**
     * 根据账期逾期管理明细集合查询相关订单
     * @param overdueManageDetailIdList 账期逾期管理明细集合
     * @return 订单集合
     */
    List<Long> getOrderIdListByOverdueManageDetailIdList(@Param("overdueManageDetailIdList") List<Long> overdueManageDetailIdList);


    /**
     * 根据使用明细id集合查询已经逾期的使用明细id
     * @param useDetailIdList 使用明细id集合
     * @return 逾期的使用明细id
     */
    List<Long> getCustomerBillPeriodUseDetailIdHasOverdue(@Param("useDetailIdList") List<Long> useDetailIdList);


    /**
     * 获取订单最新的账期管理编码
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @return 最新账期管理编码
     */
    String geNewestBillPeriodManagementCodeByOrderId(@Param("companyId") Integer companyId, @Param("customerId") Long customerId, @Param("orderId") Long orderId);

    /**
     * @description: 获取逾期时间 大于等于 当前天数+入参天数  逾期管理明细
     * @return: 逾期管理明细已逾期结果
     * @author: Strange
     * @date: 2021/7/28
     **/
    List<CustomerBillPeriodOverdueManagementDetailVo> getCustomerBillPeriodOverDueInfoByOverdueDay(@Param("dayNum") Integer dayNum);

    /**
     * 根据订单获取未归还完毕的账期逾期管理明细
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @return 逾期管理编码
     */
    List<CustomerBillPeriodOverdueManagementDetail> getOverdueManagementDetailListUnreturnedByOrderId(@Param("companyId") Integer companyId,
                                                                                                      @Param("customerId") Long customerId,
                                                                                                      @Param("orderId") Long orderId);


    /**
     * 查询指定账期的逾期金额（未归还、已逾期）
     * @param billPeriodIdList 账期集合
     * @return 逾期金额
     */
    List<CustomerBillPeriodOverdueManagementDetail> getOverdueAmountByBillPeriodIdList(@Param("billPeriodIdList") List<Long> billPeriodIdList);


    /**
     * 查询每个账期的逾期次数
     * @param billPeriodIdList 账期集合
     * @return 账期的逾期次数
     */
    List<CustomerBillPeriodOverdueManagementDetail> getCountOfOverdueGroupByBillPeriod(@Param("billPeriodIdList") List<Long> billPeriodIdList);
}