<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.ThirdRequestLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.ThirdRequestLog">
    <id column="THIRD_REQUEST_LOG_ID" jdbcType="BIGINT" property="thirdRequestLogId" />
    <result column="SERVER_NAME" jdbcType="VARCHAR" property="serverName" />
    <result column="ERP_METHOD_NAME" jdbcType="VARCHAR" property="erpMethodName" />
    <result column="URL" jdbcType="VARCHAR" property="url" />
    <result column="REQUEST_TYPE" jdbcType="INTEGER" property="requestType" />
    <result column="REQUEST_PARAM" jdbcType="VARCHAR" property="requestParam" />
    <result column="RESPONSE" jdbcType="VARCHAR" property="response" />
    <result column="RETRY_STATUS" jdbcType="TINYINT" property="retryStatus" />
    <result column="RETRY_TIMES" jdbcType="INTEGER" property="retryTimes" />
    <result column="LATEST_RESPONSE" jdbcType="VARCHAR" property="latestResponse" />
    <result column="LATEST_RETRY_TIME" jdbcType="TIMESTAMP" property="latestRetryTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
  </resultMap>
  <sql id="Base_Column_List">
    THIRD_REQUEST_LOG_ID, `SERVER_NAME`, ERP_METHOD_NAME, URL, REQUEST_TYPE, REQUEST_PARAM, 
    RESPONSE, RETRY_STATUS, RETRY_TIMES, LATEST_RESPONSE, LATEST_RETRY_TIME, ADD_TIME, BUSINESS_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_THIRD_REQUEST_LOG
    where THIRD_REQUEST_LOG_ID = #{thirdRequestLogId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_THIRD_REQUEST_LOG
    where THIRD_REQUEST_LOG_ID = #{thirdRequestLogId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="THIRD_REQUEST_LOG_ID" keyProperty="thirdRequestLogId" parameterType="com.vedeng.erp.system.domain.entity.ThirdRequestLog" useGeneratedKeys="true">
    insert into T_THIRD_REQUEST_LOG (`SERVER_NAME`, ERP_METHOD_NAME, URL, 
      REQUEST_TYPE, REQUEST_PARAM, RESPONSE, 
      RETRY_STATUS, RETRY_TIMES, LATEST_RESPONSE, 
      LATEST_RETRY_TIME, ADD_TIME, BUSINESS_NO)
    values (#{serverName,jdbcType=VARCHAR}, #{erpMethodName,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{requestType,jdbcType=INTEGER}, #{requestParam,jdbcType=VARCHAR}, #{response,jdbcType=VARCHAR}, 
      #{retryStatus,jdbcType=TINYINT}, #{retryTimes,jdbcType=INTEGER}, #{latestResponse,jdbcType=VARCHAR},
      #{latestRetryTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP}, #{businessNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="THIRD_REQUEST_LOG_ID" keyProperty="thirdRequestLogId" parameterType="com.vedeng.erp.system.domain.entity.ThirdRequestLog" useGeneratedKeys="true">
    insert into T_THIRD_REQUEST_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serverName != null">
        `SERVER_NAME`,
      </if>
      <if test="erpMethodName != null">
        ERP_METHOD_NAME,
      </if>
      <if test="url != null">
        URL,
      </if>
      <if test="requestType != null">
        REQUEST_TYPE,
      </if>
      <if test="requestParam != null">
        REQUEST_PARAM,
      </if>
      <if test="response != null">
        RESPONSE,
      </if>
      <if test="retryStatus != null">
        RETRY_STATUS,
      </if>
      <if test="retryTimes != null">
        RETRY_TIMES,
      </if>
      <if test="latestResponse != null">
        LATEST_RESPONSE,
      </if>
      <if test="latestRetryTime != null">
        LATEST_RETRY_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="businessNo != null">
        BUSINESS_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serverName != null">
        #{serverName,jdbcType=VARCHAR},
      </if>
      <if test="erpMethodName != null">
        #{erpMethodName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="requestType != null">
        #{requestType,jdbcType=INTEGER},
      </if>
      <if test="requestParam != null">
        #{requestParam,jdbcType=VARCHAR},
      </if>
      <if test="response != null">
        #{response,jdbcType=VARCHAR},
      </if>
      <if test="retryStatus != null">
        #{retryStatus,jdbcType=TINYINT},
      </if>
      <if test="retryTimes != null">
        #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="latestResponse != null">
        #{latestResponse,jdbcType=VARCHAR},
      </if>
      <if test="latestRetryTime != null">
        #{latestRetryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.ThirdRequestLog">
    update T_THIRD_REQUEST_LOG
    <set>
      <if test="serverName != null">
        `SERVER_NAME` = #{serverName,jdbcType=VARCHAR},
      </if>
      <if test="erpMethodName != null">
        ERP_METHOD_NAME = #{erpMethodName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        URL = #{url,jdbcType=VARCHAR},
      </if>
      <if test="requestType != null">
        REQUEST_TYPE = #{requestType,jdbcType=INTEGER},
      </if>
      <if test="requestParam != null">
        REQUEST_PARAM = #{requestParam,jdbcType=VARCHAR},
      </if>
      <if test="response != null">
        RESPONSE = #{response,jdbcType=VARCHAR},
      </if>
      <if test="retryStatus != null">
        RETRY_STATUS = #{retryStatus,jdbcType=TINYINT},
      </if>
      <if test="retryTimes != null">
        RETRY_TIMES = #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="latestResponse != null">
        LATEST_RESPONSE = #{latestResponse,jdbcType=VARCHAR},
      </if>
      <if test="latestRetryTime != null">
        LATEST_RETRY_TIME = #{latestRetryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessNo != null">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
    </set>
    where THIRD_REQUEST_LOG_ID = #{thirdRequestLogId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.ThirdRequestLog">
    update T_THIRD_REQUEST_LOG
    set `SERVER_NAME` = #{serverName,jdbcType=VARCHAR},
      ERP_METHOD_NAME = #{erpMethodName,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR},
      REQUEST_TYPE = #{requestType,jdbcType=INTEGER},
      REQUEST_PARAM = #{requestParam,jdbcType=VARCHAR},
      RESPONSE = #{response,jdbcType=VARCHAR},
      RETRY_STATUS = #{retryStatus,jdbcType=TINYINT},
      RETRY_TIMES = #{retryTimes,jdbcType=INTEGER},
      LATEST_RESPONSE = #{latestResponse,jdbcType=VARCHAR},
      LATEST_RETRY_TIME = #{latestRetryTime,jdbcType=TIMESTAMP},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      BUSINESS_NO = #{businessNo,jdbcType=VARCHAR}
    where THIRD_REQUEST_LOG_ID = #{thirdRequestLogId,jdbcType=BIGINT}
  </update>

  <select id="getRetryRequest" resultType="com.vedeng.erp.system.domain.entity.ThirdRequestLog">
    select
    <include refid="Base_Column_List" />
    from T_THIRD_REQUEST_LOG
    where RETRY_STATUS = #{retryStatus,jdbcType=INTEGER} and RETRY_TIMES &lt; #{retryTimes,jdbcType=INTEGER}
  </select>
</mapper>