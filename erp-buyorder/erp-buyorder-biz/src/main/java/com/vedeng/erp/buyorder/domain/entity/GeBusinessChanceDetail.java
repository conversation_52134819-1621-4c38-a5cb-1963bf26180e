package com.vedeng.erp.buyorder.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

public class GeBusinessChanceDetail {
    /**   GE_BUSSINESS_CHANCE_DETAIL_ID **/
    private Integer geBussinessChanceDetailId;

    /** GE商机ID  GE_BUSSINESS_CHANCE_ID **/
    private Integer geBussinessChanceId;

    /** 商机状态 1跟进中 2赢单 3失单  BUSINESS_CHANCE_STATUS **/
    private Integer businessChanceStatus;

    /** 销售额(万元)  SALES_AMOUNT **/
    private BigDecimal salesAmount;

    /** 医院等级规模 字典表  HOSTPITAL_SIZE **/
    private Integer hospitalSize;

    /** 床位数  BED_NUM **/
    private Integer bedNum;

    /** 年营收(万元)  YEAR_IN_SUM **/
    private BigDecimal yearInSum;

    /** CT患者量  CT_DAILY_NUM **/
    private Integer ctDailyNum;

    /** 是否新建医院 0否 1是  IS_NEW_HOSTPITAL **/
    private Integer isNewHospital;

    /** 新建医院场地进度 1-20%,2-50%,3-70%,4-100%  NEW_HOSTPITAL_PERCENT **/
    private Integer newHospitalPercent;

    /** 预计采购时间  EXPECT_BUY_TIME **/
    private Date expectBuyTime;

    /** 现有竞争对手  COMPETE_NAME **/
    private String competeName;

    /** 竞争对手产品  COMPETE_SKU_NAME **/
    private String competeSkuName;

    /** 竞品价格  COMPETE_SKU_PRICE **/
    private BigDecimal competeSkuPrice;

    /** 是否新增装机信息 0否 1是  IS_NEW_INSTALL **/
    private Integer isNewInstall;

    /** 原有品牌  ORIGIN_SKU_BAND **/
    private String originSkuBand;

    /** 原有型号  ORIGIN_SKU_MODEL **/
    private String originSkuModel;

    /** 装机时间  INSTALL_TIME **/
    private Date installTime;

    /** 销售公司  SALE_COMPANY_NAME **/
    private String saleCompanyName;

    /** 赢单率 1高2中3低  WIN_ORDER_PERCENT **/
    private Integer winOrderPercent;

    /** 资金来源  MONEY_SOURCE **/
    private String moneySource;

    /** 资金情况  MONEY_SITUATION **/
    private String moneySituation;

    /** 预备资金金额  PREPARE_AMOUNT **/
    private BigDecimal prepareAmount;

    /** 采购形式 字典表  BUY_TYPE **/
    private Integer buyType;

    /** 是否安排考察 0否 1是  IS_APPLY_INSPECT **/
    private Integer isApplyInspect;

    /** 机房是否布置完成 0否 1是  IS_ENGINE_COMPLETE **/
    private Integer isEngineComplete;

    /** 报价方案  QUOTE_METHOD **/
    private String quoteMethod;

    /** 方案价格  QUOTE_METHOD_PRICE **/
    private BigDecimal quoteMethodPrice;

    /** 项目阶段 字典表  PROJECT_PHASE **/
    private Integer projectPhase;

    /** 待解决事项  NEED_COMPLETE **/
    private String needComplete;

    /** 需求时间  NEED_TIME **/
    private Date needTime;

    /** 协助部门  ASSIST_DEPARTMENT **/
    private String assistDepartment;

    /** 解决进展  PROJECT_EVOLVE **/
    private String projectEvolve;

    /** 解决时间  EVOLVE_TIME **/
    private Date evolveTime;

    /** 下一步计划  NEXT_PLAN **/
    private String nextPlan;

    /** 需要支持的事宜  NEED_SUPPORT **/
    private String needSupport;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MOD_TIME **/
    private Date modTime;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /** 创建人真实姓名  CREATOR_NAME **/
    private String creatorName;

    /** 修改人真实姓名  UPDATER_NAME **/
    private String updaterName;

    /** 是否删除 0否 1是  IS_DELETE **/
    private Integer isDelete;

    /** 终端信息模块是否已维护0否1是  TERMINAL_PART_IS_CHANGE **/
    private Integer terminalPartIsChange;

    /** 竞品信息模块是否已维护0否1是  COMPETE_PART_IS_CHANGE **/
    private Integer competePartIsChange;

    /** 原有装机信息模块是否已维护0否1是  ORIGIN_INSTALL_PART_IS_CHANGE **/
    private Integer originInstallPartIsChange;

    /** 商机详细信息模块是否已维护0否1是  CHANCE_DETAIL_PART_IS_CHANGE **/
    private Integer chanceDetailPartIsChange;

    /** 关键问题解决模块是否已维护0否1是  KEY_DEAL_PART_IS_CHANGE **/
    private Integer keyDealPartIsChange;

    /**     GE_BUSSINESS_CHANCE_DETAIL_ID   **/
    public Integer getGeBussinessChanceDetailId() {
        return geBussinessChanceDetailId;
    }

    /**     GE_BUSSINESS_CHANCE_DETAIL_ID   **/
    public void setGeBussinessChanceDetailId(Integer geBussinessChanceDetailId) {
        this.geBussinessChanceDetailId = geBussinessChanceDetailId;
    }

    /**   GE商机ID  GE_BUSSINESS_CHANCE_ID   **/
    public Integer getGeBussinessChanceId() {
        return geBussinessChanceId;
    }

    /**   GE商机ID  GE_BUSSINESS_CHANCE_ID   **/
    public void setGeBussinessChanceId(Integer geBussinessChanceId) {
        this.geBussinessChanceId = geBussinessChanceId;
    }

    /**   商机状态 1跟进中 2赢单 3失单  BUSINESS_CHANCE_STATUS   **/
    public Integer getBusinessChanceStatus() {
        return businessChanceStatus;
    }

    /**   商机状态 1跟进中 2赢单 3失单  BUSINESS_CHANCE_STATUS   **/
    public void setBusinessChanceStatus(Integer businessChanceStatus) {
        this.businessChanceStatus = businessChanceStatus;
    }

    /**   销售额(万元)  SALES_AMOUNT   **/
    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    /**   销售额(万元)  SALES_AMOUNT   **/
    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    /**   医院等级规模 字典表  HOSTPITAL_SIZE   **/
    public Integer getHospitalSize() {
        return hospitalSize;
    }

    /**   医院等级规模 字典表  HOSTPITAL_SIZE   **/
    public void setHospitalSize(Integer hospitalSize) {
        this.hospitalSize = hospitalSize;
    }

    /**   床位数  BED_NUM   **/
    public Integer getBedNum() {
        return bedNum;
    }

    /**   床位数  BED_NUM   **/
    public void setBedNum(Integer bedNum) {
        this.bedNum = bedNum;
    }

    /**   年营收(万元)  YEAR_IN_SUM   **/
    public BigDecimal getYearInSum() {
        return yearInSum;
    }

    /**   年营收(万元)  YEAR_IN_SUM   **/
    public void setYearInSum(BigDecimal yearInSum) {
        this.yearInSum = yearInSum;
    }

    /**   床位数  CT_DAILY_NUM   **/
    public Integer getCtDailyNum() {
        return ctDailyNum;
    }

    /**   床位数  CT_DAILY_NUM   **/
    public void setCtDailyNum(Integer ctDailyNum) {
        this.ctDailyNum = ctDailyNum;
    }

    /**   是否新建医院 0否 1是  IS_NEW_HOSTPITAL   **/
    public Integer getIsNewHospital() {
        return isNewHospital;
    }

    /**   是否新建医院 0否 1是  IS_NEW_HOSTPITAL   **/
    public void setIsNewHospital(Integer isNewHospital) {
        this.isNewHospital = isNewHospital;
    }

    /**   新建医院场地进度 1-20%,2-50%,3-70%,4-100%  NEW_HOSTPITAL_PERCENT   **/
    public Integer getNewHospitalPercent() {
        return newHospitalPercent;
    }

    /**   新建医院场地进度 1-20%,2-50%,3-70%,4-100%  NEW_HOSTPITAL_PERCENT   **/
    public void setNewHospitalPercent(Integer newHospitalPercent) {
        this.newHospitalPercent = newHospitalPercent;
    }

    /**   预计采购时间  EXPECT_BUY_TIME   **/
    public Date getExpectBuyTime() {
        return expectBuyTime;
    }

    /**   预计采购时间  EXPECT_BUY_TIME   **/
    public void setExpectBuyTime(Date expectBuyTime) {
        this.expectBuyTime = expectBuyTime;
    }

    /**   现有竞争对手  COMPETE_NAME   **/
    public String getCompeteName() {
        return competeName;
    }

    /**   现有竞争对手  COMPETE_NAME   **/
    public void setCompeteName(String competeName) {
        this.competeName = competeName == null ? null : competeName.trim();
    }

    /**   竞争对手产品  COMPETE_SKU_NAME   **/
    public String getCompeteSkuName() {
        return competeSkuName;
    }

    /**   竞争对手产品  COMPETE_SKU_NAME   **/
    public void setCompeteSkuName(String competeSkuName) {
        this.competeSkuName = competeSkuName == null ? null : competeSkuName.trim();
    }

    /**   竞品价格  COMPETE_SKU_PRICE   **/
    public BigDecimal getCompeteSkuPrice() {
        return competeSkuPrice;
    }

    /**   竞品价格  COMPETE_SKU_PRICE   **/
    public void setCompeteSkuPrice(BigDecimal competeSkuPrice) {
        this.competeSkuPrice = competeSkuPrice;
    }

    /**   是否新增装机信息 0否 1是  IS_NEW_INSTALL   **/
    public Integer getIsNewInstall() {
        return isNewInstall;
    }

    /**   是否新增装机信息 0否 1是  IS_NEW_INSTALL   **/
    public void setIsNewInstall(Integer isNewInstall) {
        this.isNewInstall = isNewInstall;
    }

    /**   原有品牌  ORIGIN_SKU_BAND   **/
    public String getOriginSkuBand() {
        return originSkuBand;
    }

    /**   原有品牌  ORIGIN_SKU_BAND   **/
    public void setOriginSkuBand(String originSkuBand) {
        this.originSkuBand = originSkuBand == null ? null : originSkuBand.trim();
    }

    /**   原有型号  ORIGIN_SKU_MODEL   **/
    public String getOriginSkuModel() {
        return originSkuModel;
    }

    /**   原有型号  ORIGIN_SKU_MODEL   **/
    public void setOriginSkuModel(String originSkuModel) {
        this.originSkuModel = originSkuModel == null ? null : originSkuModel.trim();
    }

    /**   装机时间  INSTALL_TIME   **/
    public Date getInstallTime() {
        return installTime;
    }

    /**   装机时间  INSTALL_TIME   **/
    public void setInstallTime(Date installTime) {
        this.installTime = installTime;
    }

    /**   销售公司  SALE_COMPANY_NAME   **/
    public String getSaleCompanyName() {
        return saleCompanyName;
    }

    /**   销售公司  SALE_COMPANY_NAME   **/
    public void setSaleCompanyName(String saleCompanyName) {
        this.saleCompanyName = saleCompanyName == null ? null : saleCompanyName.trim();
    }

    /**   赢单率 1高2中3低  WIN_ORDER_PERCENT   **/
    public Integer getWinOrderPercent() {
        return winOrderPercent;
    }

    /**   赢单率 1高2中3低  WIN_ORDER_PERCENT   **/
    public void setWinOrderPercent(Integer winOrderPercent) {
        this.winOrderPercent = winOrderPercent;
    }

    /**   资金来源  MONEY_SOURCE   **/
    public String getMoneySource() {
        return moneySource;
    }

    /**   资金来源  MONEY_SOURCE   **/
    public void setMoneySource(String moneySource) {
        this.moneySource = moneySource == null ? null : moneySource.trim();
    }

    /**   资金情况  MONEY_SITUATION   **/
    public String getMoneySituation() {
        return moneySituation;
    }

    /**   资金情况  MONEY_SITUATION   **/
    public void setMoneySituation(String moneySituation) {
        this.moneySituation = moneySituation == null ? null : moneySituation.trim();
    }

    /**   预备资金金额  PREPARE_AMOUNT   **/
    public BigDecimal getPrepareAmount() {
        return prepareAmount;
    }

    /**   预备资金金额  PREPARE_AMOUNT   **/
    public void setPrepareAmount(BigDecimal prepareAmount) {
        this.prepareAmount = prepareAmount;
    }

    /**   采购形式 字典表  BUY_TYPE   **/
    public Integer getBuyType() {
        return buyType;
    }

    /**   采购形式 字典表  BUY_TYPE   **/
    public void setBuyType(Integer buyType) {
        this.buyType = buyType;
    }

    /**   是否安排考察 0否 1是  IS_APPLY_INSPECT   **/
    public Integer getIsApplyInspect() {
        return isApplyInspect;
    }

    /**   是否安排考察 0否 1是  IS_APPLY_INSPECT   **/
    public void setIsApplyInspect(Integer isApplyInspect) {
        this.isApplyInspect = isApplyInspect;
    }

    /**   机房是否布置完成 0否 1是  IS_ENGINE_COMPLETE   **/
    public Integer getIsEngineComplete() {
        return isEngineComplete;
    }

    /**   机房是否布置完成 0否 1是  IS_ENGINE_COMPLETE   **/
    public void setIsEngineComplete(Integer isEngineComplete) {
        this.isEngineComplete = isEngineComplete;
    }

    /**   报价方案  QUOTE_METHOD   **/
    public String getQuoteMethod() {
        return quoteMethod;
    }

    /**   报价方案  QUOTE_METHOD   **/
    public void setQuoteMethod(String quoteMethod) {
        this.quoteMethod = quoteMethod == null ? null : quoteMethod.trim();
    }

    /**   方案价格  QUOTE_METHOD_PRICE   **/
    public BigDecimal getQuoteMethodPrice() {
        return quoteMethodPrice;
    }

    /**   方案价格  QUOTE_METHOD_PRICE   **/
    public void setQuoteMethodPrice(BigDecimal quoteMethodPrice) {
        this.quoteMethodPrice = quoteMethodPrice;
    }

    /**   项目阶段 字典表  PROJECT_PHASE   **/
    public Integer getProjectPhase() {
        return projectPhase;
    }

    /**   项目阶段 字典表  PROJECT_PHASE   **/
    public void setProjectPhase(Integer projectPhase) {
        this.projectPhase = projectPhase;
    }

    /**   待解决事项  NEED_COMPLETE   **/
    public String getNeedComplete() {
        return needComplete;
    }

    /**   待解决事项  NEED_COMPLETE   **/
    public void setNeedComplete(String needComplete) {
        this.needComplete = needComplete == null ? null : needComplete.trim();
    }

    /**   需求时间  NEED_TIME   **/
    public Date getNeedTime() {
        return needTime;
    }

    /**   需求时间  NEED_TIME   **/
    public void setNeedTime(Date needTime) {
        this.needTime = needTime;
    }

    /**   协助部门  ASSIST_DEPARTMENT   **/
    public String getAssistDepartment() {
        return assistDepartment;
    }

    /**   协助部门  ASSIST_DEPARTMENT   **/
    public void setAssistDepartment(String assistDepartment) {
        this.assistDepartment = assistDepartment == null ? null : assistDepartment.trim();
    }

    /**   解决进展  PROJECT_EVOLVE   **/
    public String getProjectEvolve() {
        return projectEvolve;
    }

    /**   解决进展  PROJECT_EVOLVE   **/
    public void setProjectEvolve(String projectEvolve) {
        this.projectEvolve = projectEvolve == null ? null : projectEvolve.trim();
    }

    /**   解决时间  EVOLVE_TIME   **/
    public Date getEvolveTime() {
        return evolveTime;
    }

    /**   解决时间  EVOLVE_TIME   **/
    public void setEvolveTime(Date evolveTime) {
        this.evolveTime = evolveTime;
    }

    /**   下一步计划  NEXT_PLAN   **/
    public String getNextPlan() {
        return nextPlan;
    }

    /**   下一步计划  NEXT_PLAN   **/
    public void setNextPlan(String nextPlan) {
        this.nextPlan = nextPlan == null ? null : nextPlan.trim();
    }

    /**   需要支持的事宜  NEED_SUPPORT   **/
    public String getNeedSupport() {
        return needSupport;
    }

    /**   需要支持的事宜  NEED_SUPPORT   **/
    public void setNeedSupport(String needSupport) {
        this.needSupport = needSupport == null ? null : needSupport.trim();
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MOD_TIME   **/
    public Date getModTime() {
        return modTime;
    }

    /**   更新时间  MOD_TIME   **/
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**   创建人真实姓名  CREATOR_NAME   **/
    public String getCreatorName() {
        return creatorName;
    }

    /**   创建人真实姓名  CREATOR_NAME   **/
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    /**   修改人真实姓名  UPDATER_NAME   **/
    public String getUpdaterName() {
        return updaterName;
    }

    /**   修改人真实姓名  UPDATER_NAME   **/
    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName == null ? null : updaterName.trim();
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public Integer getIsDelete() {
        return isDelete;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**   终端信息模块是否已维护0否1是  TERMINAL_PART_IS_CHANGE   **/
    public Integer getTerminalPartIsChange() {
        return terminalPartIsChange;
    }

    /**   终端信息模块是否已维护0否1是  TERMINAL_PART_IS_CHANGE   **/
    public void setTerminalPartIsChange(Integer terminalPartIsChange) {
        this.terminalPartIsChange = terminalPartIsChange;
    }

    /**   竞品信息模块是否已维护0否1是  COMPETE_PART_IS_CHANGE   **/
    public Integer getCompetePartIsChange() {
        return competePartIsChange;
    }

    /**   竞品信息模块是否已维护0否1是  COMPETE_PART_IS_CHANGE   **/
    public void setCompetePartIsChange(Integer competePartIsChange) {
        this.competePartIsChange = competePartIsChange;
    }

    /**   原有装机信息模块是否已维护0否1是  ORIGIN_INSTALL_PART_IS_CHANGE   **/
    public Integer getOriginInstallPartIsChange() {
        return originInstallPartIsChange;
    }

    /**   原有装机信息模块是否已维护0否1是  ORIGIN_INSTALL_PART_IS_CHANGE   **/
    public void setOriginInstallPartIsChange(Integer originInstallPartIsChange) {
        this.originInstallPartIsChange = originInstallPartIsChange;
    }

    /**   关键问题解决模块是否已维护0否1是  KEY_DEAL_PART_IS_CHANGE   **/
    public Integer getKeyDealPartIsChange() {
        return keyDealPartIsChange;
    }

    /**   关键问题解决模块是否已维护0否1是  KEY_DEAL_PART_IS_CHANGE   **/
    public void setKeyDealPartIsChange(Integer keyDealPartIsChange) {
        this.keyDealPartIsChange = keyDealPartIsChange;
    }

    public Integer getChanceDetailPartIsChange() {
        return chanceDetailPartIsChange;
    }

    public void setChanceDetailPartIsChange(Integer chanceDetailPartIsChange) {
        this.chanceDetailPartIsChange = chanceDetailPartIsChange;
    }
}