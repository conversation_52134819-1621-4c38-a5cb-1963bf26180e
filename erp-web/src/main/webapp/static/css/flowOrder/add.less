html,
body {
    font-size: 13px;
    margin: 0;
    background: #fff;

    * {
        box-sizing: border-box;
    }
}

.wrap {
    display: flex;
    border: 1px solid #DCDFE6;
    height: 100vh;
    min-width: 1200px;

    .container-l {
        width: 320px;
        border-right: 1px solid #DCDFE6;
        position: relative;
        transition: all .22s ease;

        .l-trigger-show {
            position: absolute;
            top: 40px;
            right: -1px;
            width: 32px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #DCDFE6;
            cursor: pointer;
            z-index: 111;
            background-color: #fff;
            font-size: 20px;
            color: #666;

            &:hover {
                background: #F5F7FA;
            }
        }

        &.hide{
            width: 0;
            border: 0;

            .l-trigger-show {
                right: -32px;

                .el-icon-arrow-left {
                    transform: rotate(180deg);
                }
            }

            .l-title, .l-steps {
                display: none;
            }
        }

        .l-title {
            padding: 10px 0;
            text-align: center;
            border-bottom: 1px solid #DCDFE6;
            font-weight: 700;
        }

        .l-steps {
            height: calc(100% - 41px);
            display: flex;
            align-items: center;
            font-size: 12px;

            .step-list {
                width: 100%;
                padding: 20px;
                max-height: 100%;
                overflow-y: auto;

                &.reverse {
                    .step-item {
                        .step-gap {
                            transform: rotate(180deg);
                            top: -10px;
                        }

                        .step-r {
                            .step-gap {
                                top: -20px;
                                bottom: auto;
                            }
                        }

                        &:nth-child(2) {
                            .step-gap {
                                &::before {
                                    height: calc(100% + 20px);
                                }
                            }

                            .step-r {
                                .step-gap {
                                    &::before {
                                        height: 100%;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .step-item {
                display: flex;
                margin-bottom: 40px;

                &:last-child {
                    margin-bottom: 0;
                }

                .step-l {
                    margin-right: 20px;
                    position: relative;
                }

                .step-r {
                    flex: 1;
                    position: relative;

                    .step-gap {
                        top: auto;
                        left: calc(50% - 7px);
                        bottom: 2px;

                        &::before {
                            height: calc(100% + 20px);
                        }
                    }
                }

                .step-l-txt {
                    font-weight: 700;
                    line-height: 39px;
                    width: 30px;
                    text-align: center;
                }

                .step-company {
                    padding: 10px;
                    border: 1px solid #67C23A;
                    text-align: center;
                }

                .step-rate {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-top: 5px;
                }

                .step-gap {
                    position: absolute;
                    top: 30px;
                    left: 8px;

                    i {
                        color: #ccc;
                        font-weight: 700;
                    }

                    &::before {
                        content: "";
                        width: 1px;
                        height: calc(100% + 40px);
                        border-right: 2px dashed #ccc;
                        position: absolute;
                        top: 17px;
                        left: 5px;
                    }
                }
            }
        }
    }

    .container-r {
        flex: 1;
        padding: 20px;
        height: calc(100vh - 2px);
        overflow: auto;
        padding-bottom: 63px;

        &.left-hide {
            .block-table {
                width: calc(100vw - 60px);
            }

            .container-r-footer {
                width: 100%;
            }
        }

        .content-block {
            margin-bottom: 30px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .block-title {
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;

            .block-title-options {
                font-weight: normal;
                margin-left: 20px;

                .option-item {
                    display: flex;
                    align-items: center;
                    color: #409EFF;
                    cursor: pointer;
                    user-select: none;

                    &:hover {
                        color: #f60;
                    }

                    i {
                        margin-right: 5px;
                    }

                    &.disabled {
                        color: #999;
                        cursor: not-allowed;
                    }
                }
            }
        }

        .block-table {
            width: calc(100vw - 460px);
            overflow-x: auto;

            .el-input-number--mini {
                width: 100%;
            }

            th .must.cell {
                padding-left: 15px;
                position: relative;
                display: inline-block;
                width: auto;

                &::before {
                    content: "*";
                    position: absolute;
                    color: #F56C6C;
                    left: 8px;
                }
            }

            .el-select {
                width: 100%;
            }

            .rate-wrap {
                display: flex;
                align-items: center;

                .el-input-number {
                    margin-right: 3px;
                }
            }

            .rate-header-wrap {
                display: flex;
                align-items: center;

                .el-icon-edit {
                    color: #409EFF;
                    font-size: 16px;
                    margin-left: 3px;
                    cursor: pointer;

                    &:hover {
                        color: #f60;
                    }
                }
            }

            .vd-ui-select {
                width: 100%;
            }
        }

        .option-btn {
            cursor: pointer;

            &.option-red {
                color: #F56C6C;

                &:hover {
                    color: #f60;
                }
            }
        }
    }

    .container-r-footer {
        position: fixed;
        bottom: 0;
        background: #fff;
        padding: 10px 20px;
        border-top: 1px solid #DCDFE6;
        width: calc(100% - 401px);
        right: 0;
        display: flex;
        justify-content: center;
        z-index: 9;
    }
}

.text-line-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.global-loading {
    font-size: 32px;

    .el-icon-loading {
        color: #fff;
    }
}

.supplier-option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 33px;
    width: 100%;

    .item-label {
        flex: 1;
    }

    .item-icon {
        width: 16px;
        height: 16px;
        background-size: 100% 100%;
        background-image: url(/static/images/floworder/fk-icon.jpg);
        margin-left: 10px;

        &.disabled {
            background-image: url(/static/images/floworder/fk-icon-grey.jpg);
        }
    }

    .item-tel {
        margin-left: 10px;
    }
}

.ui-select-option-li.selected {
    .supplier-option-item {
        color: #09f;
    }
}

.ui-select-option-li.disabled {
    .supplier-option-item {
        color: #999;
        cursor: not-allowed;
    }
}

.dialog-form-wrap {
    .el-dialog__title {
        font-size: 16px;
    }

    .el-form-item {
        &:last-child {
            margin-bottom: 0;
        }
    }

    .el-dialog__body {
        padding: 20px;
    }

    .rate-wrap {
        display: flex;
        align-items: center;

        .el-input-number {
            margin-right: 5px;
            text-align: left;
        }
    }

    .el-dialog__footer {
        padding: 0 20px 20px;
    }
}

.ui-select-option-li p {
    font-size: 12px;
}

.form-error-wrap {
    display: flex;
    align-items: center;
    color: #e64545;
    margin-top: 5px;

    i {
        font-size: 16px;
        margin-right: 5px;
        margin-top: 2px;
    }
}

.error .el-input__inner {
    border-color: #e64545;
}