package com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.afterProcessor.ext;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.orderstrategy.BuyorderStrategyContext;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.afterProcessor.OverBuyorderAfterSaleChangeOrderAfterProcessor;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.vedeng.common.constant.OrderDataUpdateConstant.BUY_ORDER_AFTERORDER_OPY;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DealBuyorderForBuyChangeAfterProcessor extends OverBuyorderAfterSaleChangeOrderAfterProcessor {

    @Autowired
    BuyorderMapper buyorderMapper;

    @Autowired
    AfterSalesMapper afterSalesMapper;

    @Autowired
    BuyorderStrategyContext buyorderStrategyContext;

    @Override
    public void doProcess(BizDto bizDto) {
        int count = 0;
        AfterSalesVo afterSalesVo = (AfterSalesVo) bizDto.getBizData();
        List<AfterSalesVo> byOrderLists = afterSalesMapper.getAfterSalesVoListByOrderId(afterSalesVo);
        if (byOrderLists != null && byOrderLists.size() > 0) {
            for (AfterSalesVo afterSalesVo1 : byOrderLists) {
                if (afterSalesVo1.getAtferSalesStatus().equals(0) || afterSalesVo1.getAtferSalesStatus().equals(1)) {
                    count++;
                }
            }
        }

        if (count == 0) {
            //采购
            Buyorder buyorder = new Buyorder();
            buyorder.setBuyorderId(afterSalesVo.getOrderId());
            buyorder.setServiceStatus(2);
            buyorder.setLockedStatus(0);
            buyorderMapper.updateByPrimaryKeySelective(buyorder);
        }
        buyorderStrategyContext.executeAll(buyorderStrategyContext.add(BuyorderStrategyContext.BUYORDER_AMOUNT_STRATEGY, BUY_ORDER_AFTERORDER_OPY), afterSalesVo.getOrderId());
    }
}
