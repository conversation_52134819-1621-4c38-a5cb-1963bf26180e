<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>/static/js/aftersales/order/add_afterSales_th.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%=basePath%>/static/js/aftersales/order/controlRadio.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<div class="form-list  form-tips5">
                <div class="parts">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>产品名称</th>
                                <th >开票数量</th>
                                <th>单位</th>
                                <th>本次开票金额</th>

                            </tr>
                        </thead>
                        <tbody>
                        	<c:forEach items="${invoiceDetails}" var="invoiceGood">
                       			<tr>
                                    <td class=""> ${invoiceGood.goodsName}</td>
	                                <td><fmt:formatNumber type="number" value=" ${invoiceGood.num}" pattern="0.00"/></td>
                                    <td>${invoiceGood.unitName}</td>
	                                <td>${invoiceGood.totalAmount}</td>
	                            </tr>
                        	</c:forEach>
                        </tbody>
                    </table>
                </div>
    </div>
<%@ include file="../../common/footer.jsp"%>