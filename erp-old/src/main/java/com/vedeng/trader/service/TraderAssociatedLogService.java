package com.vedeng.trader.service;

import com.vedeng.aftersales.model.dto.TraderAssociatedLogDto;
import com.vedeng.aftersales.model.vo.TraderAssociatedLogVo;

import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface TraderAssociatedLogService {

    int REMARK_LIMITED_SIZE = 30;

    /**
     * 根据注册用户唯一编号获取关联公司日志
     *
     * @param webAccountId
     * @return
     * @since ERP_SV_2020_78
     */
    List<TraderAssociatedLogVo> listByWebAccountId(Integer webAccountId);

    /**
     * 记录注册用户关联/解除客户公司日志
     *
     * @param traderAssociatedLogDto
     * @since ERP_SV_2020_78
     */
    void recordTraderAssociatedLog(TraderAssociatedLogDto traderAssociatedLogDto);
}
