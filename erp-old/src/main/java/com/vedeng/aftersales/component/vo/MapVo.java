
package com.vedeng.aftersales.component.vo;

import java.io.Serializable;

/**
 * <b>Description: 用于key-value对应的数据模型类</b><br> 
 * <b>Author: Franlin.wu</b> 
 * 
 * <br><b>Date: 2018年12月7日 下午4:22:58 </b> 
 *
 */
public class MapVo<E> implements Serializable
{
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = -1065513013632153933L;

	/**
	 * key
	 */
	private String key;
	
	/**
	 * value
	 */
	private E value;

	public String getKey()
	{
		return key;
	}

	public void setKey(String key)
	{
		this.key = key;
	}
	
	public E getValue()
	{
		return value;
	}

	public void setValue(E value)
	{
		this.value = value;
	}

	@Override
	public String toString()
	{
		StringBuffer buf = new StringBuffer("");
		buf.append("key = ").append(key).append(", value = ").append(value);
		return buf.toString();
	}
	
	
}

