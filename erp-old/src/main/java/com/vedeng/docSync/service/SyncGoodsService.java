package com.vedeng.docSync.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.newtask.celery.CeleryAsync;
import com.newtask.celery.model.CeleryQueueEnum;
import com.pricecenter.service.BasePriceMaintainService;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.util.FileUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.docSync.enums.DocSyncEventEnum;
import com.vedeng.docSync.model.pojo.DocBuzTagExtDo;
import com.vedeng.docSync.model.pojo.DocFileExtDo;
import com.vedeng.docSync.model.pojo.DocOfGoodsExtDo;
import com.vedeng.docSync.model.pojo.DocRBuzTagJFileExtDo;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.dao.SyncGoodsInfoMapper;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.model.dto.*;
import com.vedeng.order.model.vo.SkuDto;
import com.vedeng.price.api.price.dto.price.SkuPriceContainsFeeApi;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.FtpUtilService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Author: Cherny.chen
 * @Create: 2021/6/4 10:19
 */
@Service
public class SyncGoodsService extends BaseService {

    private final static Logger logger = LoggerFactory.getLogger(SyncGoodsService.class);

    /**
     * 一次推送数量
     */
    private static final Integer SKU_SYNC_SIZE = 100;

    /**
     * 同步商品超时时间
     */
    private static final Integer SYNC_SKU_TIME_OUT_SECOND = 60;

    @Autowired
    ErpMsgProducer erpMsgProducer;

    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private CoreSpuMapper coreSpuMapper;

    @Resource
    private RegistrationNumberMapper registrationNumberMapper;

    @Autowired
    private FtpUtilService ftpUtilService;

    @Value("${registration_certificate}")
    private String registrationCertificateStr;

    @Value("${registration_certificate_resource}")
    private String registrationCertificateResourceStr;

    @Value("${sku_jump_url}")
    private String skuJumpUrl;

    @Value("${erp_url}")
    private String erpUrl;

    @Value("${spu_jump_url}")
    private String spuJumpUrl;


    @Value("${ftp_file_url}")
    private String ftpFileUrl;

    /**
     * 目前 buzTag唯一，只同步注册证
     */
    private static Integer buzTag = null;

    private static Integer buzTagzczy = null;

    @Resource
    private SyncGoodsInfoMapper syncGoodsInfoMapper;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;
    
    @Resource
    private BasePriceMaintainService basePriceMaintainService;


    /**
     * 初始化 注册证标签id
     */
    @PostConstruct
    public void initBuzTag() {
        //所有标签
        List<DocBuzTagExtDo> docBuzTagExtDoList = docSyncMapper.listBuzTagByBuzType(1);

        if (docBuzTagExtDoList.size() == 0) {
            throw new RuntimeException("查询doc标签为空！ 请check ！");
        }

        // 只处理注册证同步
        DocBuzTagExtDo syncTag = null;
        DocBuzTagExtDo syncTagzczy = null;
        for (DocBuzTagExtDo docBuzTagExtDo : docBuzTagExtDoList) {
            if (docBuzTagExtDo.getBuzTagName().equals(registrationCertificateStr)) {
                syncTag = docBuzTagExtDo;
            }
            if (docBuzTagExtDo.getBuzTagName().equals(registrationCertificateResourceStr)) {
                syncTagzczy = docBuzTagExtDo;
            }
        }

        if (null == syncTag || null == syncTagzczy) {
            throw new RuntimeException("未找到doc注册证标签!! 请check");
        } else {
            buzTag = syncTag.getId();
            buzTagzczy = syncTagzczy.getId();

        }


    }

    /**
     * @param asyncType            {@link DocSyncEventEnum}
     * @param key                  skuId 或 spuId
     * @param registrationNumberId 对应注册证id
     */
    @Transactional(rollbackFor = Exception.class)
    @CeleryAsync(queue = CeleryQueueEnum.SYNC_DOC)
    public void syncGoods2Doc(int asyncType, Integer key, Integer registrationNumberId) {
        logger.info("同步商品资料, asyncType:{},key:{},registrationNumberId{}", asyncType,key,registrationNumberId);

        List<DocOfGoodsExtDo> docOfGoodsExtDoList = new ArrayList<>();
        if (DocSyncEventEnum.SKU_SYNC.getType().equals(asyncType)) {
            docOfGoodsExtDoList = docSyncMapper.getDocOfGoodsBySkuId(key);

        }
        if (DocSyncEventEnum.SPU_SYNC.getType().equals(asyncType)) {
            docOfGoodsExtDoList = docSyncMapper.getDocOfGoodsBySpuId(key);

        }
        if (DocSyncEventEnum.FIRST_ENGAGE_SYNC.getType().equals(asyncType)) {
            List<Integer> spuIdList = firstEngageMapper.getSpuByFirstEngageId(key);
            docOfGoodsExtDoList = docSyncMapper.getDocOfGoodsBySpuIdList(spuIdList);
        }

        // 同步商品资料
        syncDocOfGoodsBCIdFromErp(docOfGoodsExtDoList);

        // 同步附件资料
        for (DocOfGoodsExtDo docOfGoodsExtDo : docOfGoodsExtDoList) {
            if (Objects.nonNull(buzTag)) {
                HashMap<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
                paramMap.put("registrationNumberId", registrationNumberId);
                paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
                paramMap.put("attachmentFunction", CommonConstants.ATTACHMENT_FUNCTION_975);

                // 查询附件信息
                List<Attachment> attachments = firstEngageMapper.getRegistrationAttachment(paramMap);
                paramMap.put("attachmentFunction", CommonConstants.ATTACHMENT_FUNCTION_1010);
                List<Attachment> zczyattachments = firstEngageMapper.getRegistrationAttachment(paramMap);
                if (zczyattachments.size()>0) {
                    attachments.addAll(zczyattachments);
                }
                // 注册证信息
                RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(registrationNumberId);
                if (attachments.size() > 0) {
                    dealWithValidGoodsFile(asyncType, docOfGoodsExtDo, attachments, registrationNumber);
                }
            }
        }
        logger.info("处理同步SKU或SPU结束, key :{}", key);
    }

    /**
     * 同步商品资料
     *
     * @param docOfGoodsExtDoList
     */
    private void syncDocOfGoodsBCIdFromErp(List<DocOfGoodsExtDo> docOfGoodsExtDoList) {
        logger.info("同步商品资料, docOfGoodsExtDoList :{}", JSON.toJSONString(docOfGoodsExtDoList));

        if (CollectionUtils.isEmpty(docOfGoodsExtDoList)) {
            return;
        }
        docOfGoodsExtDoList.forEach(item -> {
            // SPU资料
            if (item.getSkuId() == null && item.getSpuId() != null) {
                CoreSpu spuInfoById = coreSpuMapper.getSpuinfoById(item.getSpuId());
                docSyncMapper.updateBCIdBySpuInfo(spuInfoById);
            }

            // SKU资料
            // 同步审核通过时，在sku迁移spu场景中会出现，当spu和sku的关系变化时，需要同步资料库中T_DOC_OF_GOODS spu和sku关系
            if (item.getSkuId() != null && item.getSpuId() != null) {
                SkuDto skuDto = coreSkuMapper.getSkuInfoById(item.getSkuId());
                docSyncMapper.updateBCIdBySkuInfo(skuDto);
            }
        });

    }

    private void dealWithValidGoodsFile(int asyncType, DocOfGoodsExtDo docOfGoodsExtDo, List<Attachment> attachments, RegistrationNumber registrationNumber) {
        Integer docOfGoodsExtDoId = docOfGoodsExtDo.getId();
        List<DocRBuzTagJFileExtDo> docBuzTagFileExtDoList = docSyncMapper.listBuzTagFileForGoods(docOfGoodsExtDoId, buzTag);

        List<DocRBuzTagJFileExtDo> docBuzTagFileExtDoListzzy = docSyncMapper.listBuzTagFileForGoods(docOfGoodsExtDoId, buzTagzczy);
        docBuzTagFileExtDoList.addAll(docBuzTagFileExtDoListzzy);
        // 清理旧数据
        super.clearOldData(docBuzTagFileExtDoList);

        //增加文件
        for (Attachment attachment : attachments) {
            syncAttachment2Doc(attachment, registrationNumber, docOfGoodsExtDo, asyncType);

        }

    }

    private void syncAttachment2Doc(Attachment attachment, RegistrationNumber registrationNumber, DocOfGoodsExtDo docOfGoodsExtDo, int asyncType) {
        try {
            String fileUrl = attachment.getDomain() + attachment.getUri();
            DocFileExtDo docFileExtDo = new DocFileExtDo();
            docFileExtDo.setAddTime(attachment.getAddTime());
            docFileExtDo.setCreator(attachment.getCreator());
            docFileExtDo.setModTime(attachment.getAddTime());
            docFileExtDo.setUpdater(attachment.getCreator());
            String http = FileUtil.getHttp(fileUrl);
            docFileExtDo.setOriginLink(http + fileUrl);
            docFileExtDo.setSyncTime(System.currentTimeMillis());
            docFileExtDo.setFileName(attachment.getFileName());
            docFileExtDo.setOssLinkMobile(docFileExtDo.getOriginLink());
            docFileExtDo.setSyncOssMobile(true);
            // 如果是oss,需要上传到ftp.
            DocFileExtDo exitDocFileExtDoByOriginUrl = docSyncMapper.getDocFileByOriginUrl(http + fileUrl);
            Integer docFileId = null;
            //资料库已经存在此文件 实际不需要处理
            if (exitDocFileExtDoByOriginUrl != null) {
                logger.info("同步资料库厂商   资料库已经存在此文件 " + fileUrl);
                docFileId = exitDocFileExtDoByOriginUrl.getFileId();
            } else {
                logger.info("同步资料库厂商   资料库不存在此文件 开始下载 " + http + fileUrl);
                FileUtil.FileInfo fileInfoInner = FileUtil.getFile(getTempPath(), http + fileUrl);
                logger.info("同步资料库厂商   资料库不存在此文件  下载成功 " + http + fileUrl);

                File uploadFile = fileInfoInner.getFile();
                FileInfo fileInfo = ftpUtilService.exeUploadFileToFtp(uploadFile.getPath(), "/upload/ajax", uploadFile.getName());
                logger.info("同步资料库厂商   资料库不存在此文件  上传至FTP成功 " + uploadFile.getName());

                if (StringUtil.isNotEmpty(fileUrl) && fileUrl.indexOf("resourceId") != -1) {
                    docFileExtDo.setDomain(ftpFileUrl);
                    docFileExtDo.setUri(fileInfo.getFilePath() + "/" + fileInfo.getFileName());
                    docFileExtDo.setSuffix(fileInfo.getPrefix());
                } else {
                    docFileExtDo.setDomain(http + attachment.getDomain());
                    docFileExtDo.setUri(attachment.getUri());
                    docFileExtDo.setSuffix(attachment.getSuffix());
                }
                docFileExtDo.setMd5(fileInfoInner.getMd5());

                if (fileInfoInner.getMd5() == null) {
                    logger.error("get file md5 fail ... attachment :{}", attachment);
                    return;
                }

                DocFileExtDo exitDocFileExtDo = docSyncMapper.getDocFileByMd5(fileInfoInner.getMd5());

                if (exitDocFileExtDo == null) {
                    docSyncMapper.saveDocFile(docFileExtDo);
                    exitDocFileExtDo = docFileExtDo;
                }
                docFileId = exitDocFileExtDo.getFileId();
            }
            try {
                if (docFileId != null) {
                    docFileExtDo.setFileId(docFileId);
                    docSyncMapper.updateDocFileOriginLink(docFileExtDo);
                }

            } catch (Exception e) {
                logger.error("", e);
            }
            //增加关联表记录
            DocRBuzTagJFileExtDo docBuzTagFileExtDo = new DocRBuzTagJFileExtDo();
            docBuzTagFileExtDo.setFileId(docFileId);
            docBuzTagFileExtDo.setFileName(registrationNumber.getRegistrationNumber());
            docBuzTagFileExtDo.setBuzType(1);
            docBuzTagFileExtDo.setBuzId(docOfGoodsExtDo.getId());
            docBuzTagFileExtDo.setBuzTagId(buzTag);
            if (attachment.getAttachmentFunction()!=null&&attachment.getAttachmentFunction().equals(CommonConstants.ATTACHMENT_FUNCTION_1010)){
                docBuzTagFileExtDo.setBuzTagId(buzTagzczy);
            }

            //效期开始
            if (registrationNumber.getIssuingDate() != null && registrationNumber.getIssuingDate() == 0) {
                docBuzTagFileExtDo.setValidStartTime(null);
            } else {
                docBuzTagFileExtDo.setValidStartTime(registrationNumber.getIssuingDate());
            }
            //效期结束
            if (registrationNumber.getEffectiveDate() != null && registrationNumber.getEffectiveDate() == 0) {
                docBuzTagFileExtDo.setValidEndTime(null);
            } else {
                docBuzTagFileExtDo.setValidEndTime(registrationNumber.getEffectiveDate());
            }

            if (DocSyncEventEnum.SKU_SYNC.getType().equals(asyncType)) {
                docBuzTagFileExtDo.setExternalUrl(erpUrl + skuJumpUrl + docOfGoodsExtDo.getSkuId() + "&spuId=" + docOfGoodsExtDo.getSpuId() + "&&pageType=0");

            } else {
                docBuzTagFileExtDo.setHasStamp(1);
                docBuzTagFileExtDo.setExternalUrl(erpUrl + spuJumpUrl + docOfGoodsExtDo.getSpuId());

            }

            docBuzTagFileExtDo.setAddTime(attachment.getAddTime());
            docBuzTagFileExtDo.setCreator(attachment.getCreator());
            docBuzTagFileExtDo.setModTime(attachment.getAddTime());
            docBuzTagFileExtDo.setUpdater(attachment.getCreator());

            docSyncMapper.saveBuzTagJFile(docBuzTagFileExtDo);

            logger.info("erp保存商品资料文件关系表成功, Do: {}", docBuzTagFileExtDo);
            //增加操作表记录
            if (attachment.getAttachmentFunction()!=null&&attachment.getAttachmentFunction().equals(CommonConstants.ATTACHMENT_FUNCTION_1010)){
                super.saveActionLog(docOfGoodsExtDo.getId(), 1, buzTagzczy, docBuzTagFileExtDo.getId(), attachment.getCreator());
            }else {
                super.saveActionLog(docOfGoodsExtDo.getId(), 1, buzTag, docBuzTagFileExtDo.getId(), attachment.getCreator());
            }
        } catch (SocketTimeoutException e2) {
            logger.error("同步sku到资料库，新增数据失败，model:{},失败原因：{}", JSON.toJSONString(docOfGoodsExtDo), e2);
        } catch (Exception e) {
            logger.error("同步sku到资料库，新增数据失败，model:{},失败原因：{}", JSON.toJSONString(docOfGoodsExtDo), e);
        }
    }

    public void syncNotValidSkuInfo2EsBySkuIds(){
        List<Integer> notValidSkuIdList = syncGoodsInfoMapper.getNotValidSkuIds();
        //将该list分割成每100个一组
        int limit = (notValidSkuIdList.size() + SKU_SYNC_SIZE - 1) / SKU_SYNC_SIZE;
        Stream.iterate(0, n -> n + 1).limit(limit).forEach(item -> {
            List<Integer> groupSkuIds = notValidSkuIdList.stream().skip((long) item * SKU_SYNC_SIZE).limit(SKU_SYNC_SIZE).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(groupSkuIds)) {
                return;
            }

            String syncSkuInfoBySkuInfoJson = JSON.toJSONString(getNotValidSyncSkuInfo2EsList(groupSkuIds));//获取禁用商品的报文

            Callable<String> task = new Callable<String>() {
                @Override
                public String call() throws Exception {
                    logger.info("ERP商品信息同步 start syncSkuInfoBySkuInfoJson:{}", syncSkuInfoBySkuInfoJson);
                    erpMsgProducer.sendMsg(RabbitConfig.SKU_INFO_CHANGED_EXCHANGE, RabbitConfig.SKU_INFO_CHANGED_KEY, syncSkuInfoBySkuInfoJson);
                    return null;
                }
            };

            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                Future<String> future = executorService.submit(task);
                String obj = future.get(SYNC_SKU_TIME_OUT_SECOND, TimeUnit.SECONDS);
                logger.info("sku信息推送es成功，groupSkuIds:{},返回:{}", JSON.toJSONString(groupSkuIds), obj);
            } catch (TimeoutException ex) {
                logger.error("要处理! sku信息推送es超时,groupSkuIds:{},ex:{}", JSON.toJSONString(groupSkuIds), ex);
            } catch (Exception e) {
                logger.error("ERP商品信息同步失败 syncSkuInfoBySkuInfoJson:{}", syncSkuInfoBySkuInfoJson, e);
            } finally {
                executorService.shutdown();
            }
        });

    }

    /**
     * ERP商品信息同步
     * @param validSkuIds
     * @return
     */
    public void syncSkuInfo2EsBySkuIds(List<Integer> validSkuIds){
        if (CollectionUtils.isEmpty(validSkuIds)){
            return ;
        }

        int limit = (validSkuIds.size() + SKU_SYNC_SIZE - 1) / SKU_SYNC_SIZE;

        Stream.iterate(0, n -> n + 1).limit(limit).forEach(item -> {
            List<Integer> groupSkuIds = validSkuIds.stream().skip((long) item * SKU_SYNC_SIZE).limit(SKU_SYNC_SIZE).collect(Collectors.toList());

            List<SyncSkuInfo2EsDto> syncSkuInfoBySkuIds = getSyncSkuInfo2EsList(groupSkuIds);
            if (CollectionUtils.isEmpty(syncSkuInfoBySkuIds)){
                return;
            }

            String syncSkuInfoBySkuInfoJson = JSON.toJSONString(syncSkuInfoBySkuIds);

            Callable<String> task = new Callable<String>() {
                @Override
                public String call() throws Exception {
                    logger.info("ERP商品信息同步 start syncSkuInfoBySkuInfoJson:{}", syncSkuInfoBySkuIds);
                    erpMsgProducer.sendMsg(RabbitConfig.SKU_INFO_CHANGED_EXCHANGE, RabbitConfig.SKU_INFO_CHANGED_KEY, syncSkuInfoBySkuInfoJson);
                    return null;
                }
            };

            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                Future<String> future = executorService.submit(task);
                String obj = future.get(SYNC_SKU_TIME_OUT_SECOND, TimeUnit.SECONDS);
                logger.info("sku信息推送es成功，groupSkuIds:{},返回:{}", JSON.toJSONString(groupSkuIds), obj);
            } catch (TimeoutException ex) {
                logger.error("要处理! sku信息推送es超时,groupSkuIds:{},ex:{}", JSON.toJSONString(groupSkuIds), ex);
            } catch (Exception e) {
                logger.error("ERP商品信息同步失败 syncSkuInfoBySkuInfoJson:{}", syncSkuInfoBySkuInfoJson, e);
            } finally {
                executorService.shutdown();
            }
        });
    }

    /**
     * 封装禁用的SKU信息
     * @param groupSkuIds
     * @return
     */
    private List<SyncSkuInfo2EsDto> getNotValidSyncSkuInfo2EsList(List<Integer> groupSkuIds) {
        List<SyncSkuInfo2EsDto> syncSkuInfoBySkuIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(groupSkuIds)){
            return syncSkuInfoBySkuIds;
        }
        groupSkuIds.forEach(groupSkuId -> {
            SyncSkuInfo2EsDto syncSkuInfo2EsDto = new SyncSkuInfo2EsDto();
            syncSkuInfo2EsDto.setVersion(System.currentTimeMillis());
            syncSkuInfo2EsDto.setSkuId(groupSkuId);
            syncSkuInfo2EsDto.setPlatformSkuId(groupSkuId);
            syncSkuInfo2EsDto.setSkuNo("V"+groupSkuId);
            syncSkuInfo2EsDto.setDeleteFlag("Y");
            syncSkuInfoBySkuIds.add(syncSkuInfo2EsDto);
        });
        return syncSkuInfoBySkuIds;

    }

    /**
     * 封装推送的SKU相关信息
     *
     * @param groupSkuIds
     * @return
     */
    private List<SyncSkuInfo2EsDto> getSyncSkuInfo2EsList(List<Integer> groupSkuIds) {
        Integer skuIdLog = CollectionUtils.isEmpty(groupSkuIds) ? 0 : groupSkuIds.get(0);

        logger.info("getSyncSkuInfoBySkuIdsStart skuId:{},currentTimeMillis:{}", skuIdLog, System.currentTimeMillis());
        List<SyncSkuInfo2EsDto> syncSkuInfoBySkuIds = syncGoodsInfoMapper.getSyncSkuInfoBySkuIds(groupSkuIds);
        if (CollectionUtils.isEmpty(syncSkuInfoBySkuIds)){
            return null;
        }

        logger.info("getSkuAttrHashMapStart skuId:{},currentTimeMillis:{}", skuIdLog, System.currentTimeMillis());
        HashMap<Integer, List<SyncSkuAttrDto>> skuAttrHashMap = getSkuAttrHashMap(groupSkuIds);

        logger.info("getSkuDeptMapStart skuId:{},currentTimeMillis:{}", skuIdLog, System.currentTimeMillis());
        HashMap<Integer, List<SyncSkuDeptDto>> skuDeptMap = getSkuDeptMap(groupSkuIds);

        logger.info("getSyncSkuInspectionMapStart skuId:{},currentTimeMillis:{}", skuIdLog, System.currentTimeMillis());
        HashMap<Integer, List<SyncSkuInspectionDto>> skuInspectionMap = getSyncSkuInspectionMap(groupSkuIds);

        logger.info("getSyncSkuSaleInfoMapStart skuId:{},currentTimeMillis:{}", skuIdLog, System.currentTimeMillis());
        Map<Integer, SyncSkuSaleInfoDto> syncSkuSaleInfoMap = getSyncSkuSaleInfoMap(groupSkuIds);

        logger.info("getDictionaryByParentId skuId:{},currentTimeMillis:{}", skuIdLog, System.currentTimeMillis());
        Map<Integer, String> mechanismMap = sysOptionDefinitionMapper.getDictionaryByParentId(ErpConst.MECHANISM_LEVEL)
                .stream().collect(Collectors.toMap(SysOptionDefinition::getSysOptionDefinitionId, SysOptionDefinition::getTitle));

        logger.info("getSyncSkuSceneCategoryDtoMap skuId:{},currentTimeMillis:{}", skuIdLog, System.currentTimeMillis());
        // 将groupSkuIds遍历，在首位加上v
        List<String> skuNos = groupSkuIds.stream().map(skuId -> "v" + skuId).collect(Collectors.toList());
        Map<Integer, List<SyncSkuSceneCategoryDto>> syncSkuSceneCategoryDtoMap = getSkuSceneCategoryHashMap(skuNos);
        
        //获取经销价是否含运费、成本价是否含运费的信息
        logger.info("获取经销价是否含运费、成本价是否含运费的信息 skuId:{},currentTimeMillis:{}", skuIdLog, System.currentTimeMillis());
        Map<Integer, SkuPriceContainsFeeApi> syncSkuPriceContainsFeeMap = getSkuContineFeeHashMap(skuNos);

        syncSkuInfoBySkuIds.forEach(syncSkuInfoItem -> {
            Integer skuId = syncSkuInfoItem.getSkuId();
            if (skuAttrHashMap.containsKey(skuId)){
                List<SyncSkuAttrDto> syncSkuAttrDtoList = skuAttrHashMap.get(skuId);
                syncSkuInfoItem.setAttrValue(StringUtils.join(syncSkuAttrDtoList.stream().map(SyncSkuAttrDto::getAttrValue).collect(Collectors.toList()), " "));
                syncSkuInfoItem.setAddSearchScopeName(syncSkuInfoItem.getAttrValue());
                syncSkuInfoItem.setSkuAttrValueIds(StringUtils.join(syncSkuAttrDtoList.stream().map(SyncSkuAttrDto::getBaseAttributeValueId).collect(Collectors.toList()), " "));
            }

            if (skuDeptMap.containsKey(skuId)) {
                List<SyncSkuDeptDto> syncSkuDeptDtoList = skuDeptMap.get(skuId);
                syncSkuInfoItem.setDepartmentIds(StringUtils.join(syncSkuDeptDtoList.stream().map(SyncSkuDeptDto :: getDepartmentId).collect(Collectors.toList()), " "));
                syncSkuInfoItem.setDepartmentValue(StringUtils.join(syncSkuDeptDtoList.stream().map(SyncSkuDeptDto :: getDepartmentName).collect(Collectors.toList()), " "));
            }

            if (skuInspectionMap.containsKey(skuId)){
                List<SyncSkuInspectionDto> syncSkuInspectionList = skuInspectionMap.get(skuId);
                syncSkuInfoItem.setProjectIds(StringUtils.join(syncSkuInspectionList.stream().map(SyncSkuInspectionDto :: getId).collect(Collectors.toList()), " "));
                syncSkuInfoItem.setProjectValue(StringUtils.join(syncSkuInspectionList.stream().map(SyncSkuInspectionDto :: getName).collect(Collectors.toList()), " "));
            }

            if (syncSkuSaleInfoMap.containsKey(skuId)){
                SyncSkuSaleInfoDto syncSkuSaleInfo = syncSkuSaleInfoMap.get(skuId);
                syncSkuInfoItem.setRuleSkuSaleCount(syncSkuSaleInfo.getSaleCount());
                syncSkuInfoItem.setSaleCount(syncSkuSaleInfo.getSaleCount());
                syncSkuInfoItem.setSaleTotalMoney(syncSkuSaleInfo.getSaleTotalMoney());
            }

            if(syncSkuSceneCategoryDtoMap.containsKey(skuId)){
                List<SyncSkuSceneCategoryDto> syncSkuSceneCategoryDtos = syncSkuSceneCategoryDtoMap.get(skuId);
                syncSkuInfoItem.setSkuSceneCategoryIds(StringUtils.join(syncSkuSceneCategoryDtos.stream().map(SyncSkuSceneCategoryDto::getSkuSceneCategoryId).collect(Collectors.toList()), " "));
            }

            if (syncSkuInfoItem.getRuleCategorySaleCount() > 0){
                syncSkuInfoItem.setRuleSaleRatio(new BigDecimal(syncSkuInfoItem.getRuleSkuSaleCount())
                        .divide(new BigDecimal(syncSkuInfoItem.getRuleCategorySaleCount()), BigDecimal.ROUND_HALF_UP, RoundingMode.HALF_UP));
            }
            //设置采购、销售是否包含运费
            if (Objects.nonNull(syncSkuPriceContainsFeeMap.get(skuId))){
            	SkuPriceContainsFeeApi skuPriceContainsFeeApi = syncSkuPriceContainsFeeMap.get(skuId);
                syncSkuInfoItem.setPurchaseContainsFee(skuPriceContainsFeeApi.getPurchaseContainsFee());
                syncSkuInfoItem.setSaleContainsFee(skuPriceContainsFeeApi.getSaleContainsFee());
            }

            if (StringUtils.isNotBlank(syncSkuInfoItem.getMechanismIds())){

                List<Integer> mechanismIdList = Arrays.stream(Arrays.stream(syncSkuInfoItem.getMechanismIds().split(","))
                        .mapToInt(Integer::parseInt).toArray()).boxed().collect(Collectors.toList());

                syncSkuInfoItem.setMechanismIds(StringUtils.join(mechanismIdList, " "));

                StringBuffer mechanismValueStr = new StringBuffer();
                mechanismIdList.forEach(mechanismId -> {
                    mechanismValueStr.append(mechanismMap.getOrDefault(mechanismId, ""));
                    mechanismValueStr.append(" ");
                });

                syncSkuInfoItem.setMechanismValue(mechanismValueStr.toString());
            }
            syncSkuInfoItem.setVersion(System.currentTimeMillis());
        });

        return syncSkuInfoBySkuIds;
    }


    /**
     * 调用price服务获取是否包含运费信息
     * @param skuNos
     * @return
     */
    private Map<Integer, SkuPriceContainsFeeApi> getSkuContineFeeHashMap(List<String> skuNos) {
    	Map<Integer, SkuPriceContainsFeeApi> skuPriceContainsFeeDtoMap = new HashMap<>();
    	List<SkuPriceContainsFeeApi> skuPriceContainsFeeApiList =  basePriceMaintainService.selectPriceInfoBySkuNos(skuNos);
    	if(CollectionUtils.isEmpty(skuPriceContainsFeeApiList)) {
    		return skuPriceContainsFeeDtoMap;
    	}
    	
    	skuPriceContainsFeeDtoMap = skuPriceContainsFeeApiList.stream().collect(Collectors.toMap(SkuPriceContainsFeeApi::getSkuId, item -> item));
    	return skuPriceContainsFeeDtoMap;
	}

	/**
     * sku获取三级分类销量信息
     *
     * @param groupSkuIds
     * @return
     */
    private Map<Integer, Integer> getSyncSkuCategorySaleInfoMap(List<Integer> groupSkuIds) {
        List<SyncSkuCategorySaleInfoDto> syncSkuCategorySaleInfoBySkuIds = syncGoodsInfoMapper.getSyncSkuCategorySaleInfoBySkuIds(groupSkuIds);
        if (CollectionUtils.isEmpty(syncSkuCategorySaleInfoBySkuIds)){
            return new HashMap<>(1);
        }
        return syncSkuCategorySaleInfoBySkuIds.stream().collect(Collectors.toMap(SyncSkuCategorySaleInfoDto :: getSkuId, SyncSkuCategorySaleInfoDto :: getMyCount));
    }

    /**
     * skuId获取近6个月销量信息
     * @param groupSkuIds
     * @return
     */
    private Map<Integer, SyncSkuSaleInfoDto> getSyncSkuSaleInfoMap(List<Integer> groupSkuIds) {
        List<SyncSkuSaleInfoDto> syncSkuSaleInfoRecent6Month = syncGoodsInfoMapper.getSyncSkuSaleInfoRecent6MonthBySkuIds(groupSkuIds);
        if (CollectionUtils.isEmpty(syncSkuSaleInfoRecent6Month)){
            return new HashMap<>(1);
        }
        return syncSkuSaleInfoRecent6Month.stream().collect(Collectors.toMap(SyncSkuSaleInfoDto::getSkuId, item -> item));
    }

    /**
     * skuId获取检查项目信息
     * @param groupSkuIds
     * @return
     */
    private HashMap<Integer, List<SyncSkuInspectionDto>> getSyncSkuInspectionMap(List<Integer> groupSkuIds) {
        HashMap<Integer, List<SyncSkuInspectionDto>> syncSkuInspectionMap = new HashMap<>(16);
        List<SyncSkuInspectionDto> syncSkuInspectionBySkuIds = syncGoodsInfoMapper.getSyncSkuInspectionBySkuIds(groupSkuIds);
        if (CollectionUtils.isEmpty(syncSkuInspectionBySkuIds)){
            return syncSkuInspectionMap;
        }
        syncSkuInspectionBySkuIds.forEach(item -> {
            if (syncSkuInspectionMap.containsKey(item.getSkuId())){
                syncSkuInspectionMap.get(item.getSkuId()).add(item);
            }else {
                ArrayList<SyncSkuInspectionDto> syncSkuAttrDtoArrayList = new ArrayList<>();
                syncSkuAttrDtoArrayList.add(item);
                syncSkuInspectionMap.put(item.getSkuId(), syncSkuAttrDtoArrayList);
            }
        });
        return syncSkuInspectionMap;
    }

    /**
     * 返回SKU部门信息
     * @param groupSkuIds
     * @return
     */
    private HashMap<Integer, List<SyncSkuDeptDto>> getSkuDeptMap(List<Integer> groupSkuIds) {
        HashMap<Integer, List<SyncSkuDeptDto>> skuDeptMap = new HashMap<>(16);
        List<SyncSkuDeptDto> syncSkuDeptBySkuId = syncGoodsInfoMapper.getSyncSkuDeptBySkuIds(groupSkuIds);
        if (CollectionUtils.isEmpty(syncSkuDeptBySkuId)){
            return skuDeptMap;
        }
        syncSkuDeptBySkuId.forEach(item -> {
            if (skuDeptMap.containsKey(item.getSkuId())){
                skuDeptMap.get(item.getSkuId()).add(item);
            }else {
                ArrayList<SyncSkuDeptDto> syncSkuAttrDtoArrayList = new ArrayList<>();
                syncSkuAttrDtoArrayList.add(item);
                skuDeptMap.put(item.getSkuId(), syncSkuAttrDtoArrayList);
            }
        });
        return skuDeptMap;
    }

    /**
     * 返回SKU属性信息
     * @param groupSkuIds
     * @return
     */
    private HashMap<Integer, List<SyncSkuAttrDto>> getSkuAttrHashMap(List<Integer> groupSkuIds) {
        HashMap<Integer, List<SyncSkuAttrDto>> skuAttrHashMap = new HashMap<>(16);
        List<SyncSkuAttrDto> syncSkuAttrBySkuIds = syncGoodsInfoMapper.getSyncSkuAttrBySkuIds(groupSkuIds);
        if (CollectionUtils.isEmpty(syncSkuAttrBySkuIds)){
            return skuAttrHashMap;
        }

        syncSkuAttrBySkuIds.forEach(item -> {
            if (skuAttrHashMap.containsKey(item.getSkuId())){
                skuAttrHashMap.get(item.getSkuId()).add(item);
            }else {
                ArrayList<SyncSkuAttrDto> syncSkuAttrDtoArrayList = new ArrayList<>();
                syncSkuAttrDtoArrayList.add(item);
                skuAttrHashMap.put(item.getSkuId(), syncSkuAttrDtoArrayList);
            }
        });
        return skuAttrHashMap;
    }

    /**
     * 返回SKU方案场景
     * @param groupSkuNos
     * @return
     */
    private HashMap<Integer, List<SyncSkuSceneCategoryDto>> getSkuSceneCategoryHashMap(List<String> groupSkuNos) {
        HashMap<Integer, List<SyncSkuSceneCategoryDto>> skuAttrHashMap = new HashMap<>(16);
        List<SyncSkuSceneCategoryDto> syncSkuSceneCategoryDtos = syncGoodsInfoMapper.selectSkuSceneCategoryBySkuNos(groupSkuNos);
        if (CollectionUtils.isEmpty(syncSkuSceneCategoryDtos)){
            return skuAttrHashMap;
        }

        syncSkuSceneCategoryDtos.forEach(item -> {
            if (skuAttrHashMap.containsKey(item.getSkuId())){
                skuAttrHashMap.get(item.getSkuId()).add(item);
            }else {
                ArrayList<SyncSkuSceneCategoryDto> syncSkuSceneCategoryDtoList = new ArrayList<>();
                syncSkuSceneCategoryDtoList.add(item);
                skuAttrHashMap.put(item.getSkuId(), syncSkuSceneCategoryDtoList);
            }
        });
        return skuAttrHashMap;
    }
}