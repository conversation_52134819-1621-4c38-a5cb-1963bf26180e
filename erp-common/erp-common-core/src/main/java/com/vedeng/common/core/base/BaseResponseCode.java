package com.vedeng.common.core.base;

/**
 * BaseResponseCode
 *
 * <AUTHOR>
 */
public enum BaseResponseCode {
    TAXES_FAIL(-1,"税金调用异常"),
    /**
     * code=401001 引导用户重新登录
     * code=401008 无权限访问
     */
    SUCCESS(0, "操作成功"),
    CONFIRM(1, "待用户确认"),
    FAILURE(2, "操作失败"),
    LOW_VERSION(-2, "系统优化了拜访计划等功能，请更新版本再使用"),

    BAD_REQUEST(400, "请求异常！"),
    REQUEST_NOT_FOUND(404, "请求路径不存在！"),
    HTTP_BAD_METHOD(405, "请求方式不支持！"),
    // 开票时处理方案同TaxesReturnCodeEnum.REPEAT
    TAXES_TIMEOUT(502,"税金调用超时"),

    USER_NOT_LOGIN(401001, "用户未登录"),
    UNAUTHORIZED_ACCESS(401002, "无权限访问"),
    TOKEN_INVALID(401003, "令牌无效"),
    TOKEN_EXPIRED(401004, "令牌过期"),
    USER_ACCOUNT_EXCEPTION(401005, "用户账户异常，请联系管理员"),

    SYSTEM_BUSY(500000, "系统繁忙，请稍候再试"),
    NOT_DATA_ERROR(500001, "数据不存在"),
    OPERATION_ERROR(500002, "操作失败"),
    OPERATION_PROCESS_ERROR(500003, "请求处理中，请稍后再试"),
    OPERATION_REPEAT_ERROR(500004, "请求数据重复，请确认后重试"),
    URL_NOT_FOUNT(501005,"controller映射路径未声明，请显式声明对应路径"),

    ELECTRONIC_SIGN_ERROR(700000, "电子签章调用异常"),
    ELECTRONIC_PDF_2_FILE_ERROR(700001, "电子签章:获取PDF文件异常"),
    ELECTRONIC_SIGN_MQ_ERROR (700002, "电子签章:mq消费异常"),

    YXB_SN_ORDER_ERROR(600000, "yxb数据异常不用重推"),

    TERMINAL_INACCURACY(800000, "终端数据不够精准，将导致经销商信息不够准确，请输入精确的终端信息后再进行搜索"),

    // CRM
    CRM_NO_DATA_NOT_EXIST(900000, "数据不存在"),

            ;
    /**
     * 错误码
     */
    private final int code;
    /**
     * 错误消息
     */
    private final String msg;

    BaseResponseCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
