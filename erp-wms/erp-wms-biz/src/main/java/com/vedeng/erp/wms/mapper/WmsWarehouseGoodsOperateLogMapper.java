package com.vedeng.erp.wms.mapper;

import com.vedeng.erp.wms.domain.entity.WmsWarehouseGoodsOperateLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmsWarehouseGoodsOperateLogMapper {
    /**
     * delete by primary key
     * @param warehouseGoodsOperateLogId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer warehouseGoodsOperateLogId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(WmsWarehouseGoodsOperateLogEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(WmsWarehouseGoodsOperateLogEntity record);

    /**
     * select by primary key
     * @param warehouseGoodsOperateLogId primary key
     * @return object by primary key
     */
    WmsWarehouseGoodsOperateLogEntity selectByPrimaryKey(Integer warehouseGoodsOperateLogId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(WmsWarehouseGoodsOperateLogEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(WmsWarehouseGoodsOperateLogEntity record);

    int updateBatch(List<WmsWarehouseGoodsOperateLogEntity> list);

    int batchInsert(@Param("list") List<WmsWarehouseGoodsOperateLogEntity> list);

    /**
     * 根据条件查询
     * @param WmsWarehouseGoodsOperateLogEntity
     * @return
     */
    List<WmsWarehouseGoodsOperateLogEntity> findByAll(WmsWarehouseGoodsOperateLogEntity WmsWarehouseGoodsOperateLogEntity);
}