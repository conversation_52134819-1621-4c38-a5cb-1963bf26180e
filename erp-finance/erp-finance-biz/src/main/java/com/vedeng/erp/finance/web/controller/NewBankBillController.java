package com.vedeng.erp.finance.web.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 银行流水
 */
@RequestMapping("/bank/bankBill")
@Controller
@Slf4j
public class NewBankBillController extends BaseController {

    @RequestMapping(value = "/list")
    @NoNeedAccessAuthorization
    public ModelAndView list() {
        ModelAndView mv = new ModelAndView("vue/view/finance/bank_bill_list");
        return mv;
    }
}
