<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.NewMessageMapper">

    <select id="getPosterCenterUnReadMessage" resultType="java.lang.Integer">
       SELECT
            COUNT( MU.MESSAGE_ID )
        FROM
            T_MESSAGE_USER MU
            LEFT JOIN T_MESSAGE M ON M.MESSAGE_ID = MU.MESSAGE_ID
        WHERE
            MU.IS_VIEW = 0
            AND M.CATEGORY = 4116
            AND MU.USER_ID = #{userId,jdbcType=INTEGER}
    </select>

</mapper>