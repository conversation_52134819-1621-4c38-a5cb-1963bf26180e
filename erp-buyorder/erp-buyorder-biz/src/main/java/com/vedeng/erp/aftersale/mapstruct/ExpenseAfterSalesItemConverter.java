package com.vedeng.erp.aftersale.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity;
import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesItemEntity;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesItemDto;
import com.vedeng.erp.buyorder.dto.OrderRemarkDto;
import org.mapstruct.*;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.mapstruct
 * @Date 2022/10/20 19:59
 */
@Repository
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface ExpenseAfterSalesItemConverter extends BaseMapStruct<ExpenseAfterSalesItemEntity, ExpenseAfterSalesItemDto> {


}
