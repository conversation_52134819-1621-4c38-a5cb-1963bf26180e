<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.VBuyorderSncodeMasterMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.ge.VBuyorderSncodeMaster">
    <id column="BUYORDER_SNCODE_MASTER_ID" jdbcType="INTEGER" property="buyorderSncodeMasterId" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="MASTER_SNCODE" jdbcType="VARCHAR" property="masterSncode" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    BUYORDER_SNCODE_MASTER_ID, RELATED_ID, SKU, SKU_ID, MASTER_SNCODE, ADD_TIME, MODE_TIME, 
    IS_DELETE, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.vedeng.order.model.ge.VBuyorderSncodeMaster" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_BUYORDER_SNCODE_MASTER
    where MASTER_SNCODE = #{masterSncode,jdbcType=VARCHAR}
  </select>

  <select id="getGeMasterInfo" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
			A.BUYORDER_SNCODE_MASTER_ID,A.MASTER_SNCODE
		FROM V_BUYORDER_SNCODE_MASTER A
		WHERE
			  A.RELATED_ID = #{buyorderGoodsId,jdbcType=VARCHAR}
    </select>

  <select id="checkMasterSno" parameterType="com.vedeng.order.model.ge.VBuyorderSncodeMaster" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM V_BUYORDER_SNCODE_MASTER
	WHERE
    MASTER_SNCODE = #{masterSncode,jdbcType=VARCHAR} AND RELATED_ID!=#{relatedId,jdbcType=INTEGER}
    </select>

  <delete id="delGeMasterInfoCollection" parameterType="java.lang.Integer">
    delete from V_BUYORDER_SNCODE_MASTER
    where RELATED_ID = #{buyorderGoodsId,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" keyColumn="BUYORDER_SNCODE_MASTER_ID" keyProperty="buyorderSncodeMasterId" parameterType="com.vedeng.order.model.ge.VBuyorderSncodeMaster" useGeneratedKeys="true">
    insert into V_BUYORDER_SNCODE_MASTER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="masterSncode != null">
        MASTER_SNCODE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="masterSncode != null">
        #{masterSncode,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

</mapper>