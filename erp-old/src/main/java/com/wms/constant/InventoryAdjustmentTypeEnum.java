package com.wms.constant;

/**
 * 库存调整单类型枚举
 *
 * <AUTHOR>
 * @date 2020/7/29 16:04:15
 */
public enum InventoryAdjustmentTypeEnum {
    INVENTORY_ADJUSTMENT(1, "库存调整单", "CM");

    InventoryAdjustmentTypeEnum(Integer type, String typeStr, String code) {
        this.type = type;
        this.typeStr = typeStr;
        this.code = code;
    }

    private Integer type;

    private String typeStr;

    private String code;

    public static String getTypeStr(Integer type) {
        String typeStr = "";

        for (InventoryAdjustmentTypeEnum enumItem : InventoryAdjustmentTypeEnum.values()) {
            if (enumItem.getType().equals(type)) {
                typeStr = enumItem.getTypeStr();
                break;
            }
        }

        return typeStr;
    }

    /**
     * 通过库存调整单代码获取类型
     *
     * @param code
     * @return
     */
    public static Integer getTypeByCode(String code) {
        Integer type = 0;

        for (InventoryAdjustmentTypeEnum enumItem : InventoryAdjustmentTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                type = enumItem.getType();
                break;
            }
        }

        return type;
    }


    public Integer getType() {
        return type;
    }

    public String getTypeStr() {
        return typeStr;
    }

    public String getCode() {
        return code;
    }}
