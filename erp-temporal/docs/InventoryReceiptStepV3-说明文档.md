# InventoryReceiptStepV3 - 快递增量同步版本（基于下游完成状态判断）

## 版本概述

`InventoryReceiptStepV3` 是入库单步骤的第三个版本，专门为解决快递增量同步问题而设计。经过深度重构，基于下游数据完成状态进行判断。

### 核心改进

**V2 版本问题：**
- 等待上游公司采购单中的**所有快递**都完成才开始同步下游
- 即使某个快递已经可以创建，也要等待同一采购单下的其他快递
- 造成下游同步延迟，影响业务效率

**V3 版本解决方案（重构版）：**
- 当上游采购单中的**单个快递**达到可创建状态时，立即为该快递创建对应的下游快递
- **正确的完成判断逻辑**：**下游快递商品数 = 下游采购单商品数**
- 接口直接返回完成状态，避免客户端复杂计算和数据完整性问题
- 完全符合 Temporal 框架要求，支持可靠的重放和故障恢复

## 技术架构

### 1. 核心流程（基于数据对比的重构版）

```
1. 检查下游完成状态 (下游快递商品数 = 下游采购单商品数)
   ↓
2. 如果已完成：退出循环
   ↓  
3. 如果未完成：获取上游全部快递列表
   ↓
4. 获取下游全部快递列表
   ↓
5. 通过快递单号对比，找出需要新增的快递
   ↓
6. 为新增快递创建对应的下游快递
   ↓
7. 等待30秒后回到步骤1继续检查 (30秒轮询)
```

### 2. 关键方法（重构版）

#### 主要方法：
- `monitorAndCreateExpressIncrement()` - 增量监控和创建主流程（重构为基于数据对比）
- `findNewExpress()` - **核心算法**：对比上下游快递，找出需要新增的快递
- `createDownstreamExpressAndReturnId()` - 单个下游快递创建
- 保留 V2 的签收逻辑：`executeAsyncExpressReceiptV2()`

#### 新增 Activity 方法：
- `checkDownstreamCompletionStatus()` - **核心方法**：检查下游完成状态
- `getUpstreamAllExpress()` - **新增**：获取上游全部快递列表
- `getDownstreamAllExpress()` - **新增**：获取下游全部快递列表
- `waitForExpressReady()` - 等待快递状态就绪
- `createSingleExpress()` - 创建单个快递

#### 废弃方法：
- `checkUpstreamNewExpress()` - 已废弃，使用数据对比方式替代
- `checkUpstreamExpressList()` - 已废弃，使用 `getUpstreamAllExpress()` 替代

### 3. 数据结构（重构版）

```java
// 快递创建结果（简化版）
ExpressCreationResult
├── createdExpressIds      // 已创建的下游快递ID列表
├── processedUpstreamIds   // 已处理的上游快递ID列表
├── createdExpressCount    // 已创建数量
├── success               // 是否成功
└── errorMessage          // 错误信息

// 下游状态信息（新增）
DownstreamStatusInfo
├── downstreamExpressGoodsCount    // 下游快递商品数
├── downstreamPurchaseGoodsCount   // 下游采购单商品数
└── isCompleted                    // 是否完成（接口直接返回）

// 快递对比信息（新增）
ExpressComparisonInfo
├── upstreamExpressList    // 上游全部快递
├── downstreamExpressList  // 下游全部快递
├── newExpressList         // 需要新增的快递
├── upstreamCount          // 上游快递数量
├── downstreamCount        // 下游快递数量
└── newExpressCount        // 新增快递数量

// 快递列表信息（简化版，已废弃）
ExpressListInfo
└── newExpressList       // 新增快递列表（其他字段已废弃）
```

## Temporal 兼容性

### 1. 确定性保证
- ✅ 所有外部 API 调用通过 Activity 执行
- ✅ Workflow 状态正确持久化，支持重放
- ✅ 使用确定性的状态变更逻辑

### 2. 状态管理
- `createdDownstreamExpressIds` - 已创建的下游快递ID (Workflow状态)
- `processedUpstreamExpressIds` - 已处理的上游快递ID (Workflow状态)
- `expectedExpressCount` - 预期快递数量 (Workflow状态)

### 3. 重放安全
- 系统宕机重启后，重放时会恢复到正确的状态
- 不会重复处理已处理的快递
- 不会重复创建已创建的下游快递

## API 依赖（重构版）

### 新增 API 端点：
1. `/api/v1/express/checkDownstreamCompletion.do` - **核心接口**：检查下游完成状态
2. `/api/v1/express/getUpstreamAll.do` - **新增**：获取上游全部快递列表
3. `/api/v1/express/getDownstreamAll.do` - **新增**：获取下游全部快递列表
4. `/api/v1/express/checkStatus.do` - 检查单个快递状态
5. `/api/v1/express/createSingle.do` - 创建单个快递

### 废弃 API：
- `/api/v1/express/checkNewExpress.do` - 已废弃，使用数据对比方式替代
- `/api/v1/express/listWithStatus.do` - 已废弃，使用 `getUpstreamAll.do` 替代

### 复用现有 API：
- `/api/v1/express/signCheck.do` - 签收状态检查
- `/api/v1/express/sign.do` - 执行签收
- `/api/v1/peerlist/queryStockRecords.do` - 查询库存记录
- `/api/v1/peerlist/create.do` - 创建同行单

## 业务价值（重构版）

### 1. 响应时间提升
- **之前**：等待采购单中最慢的快递，所有快递完成后才开始下游同步
- **现在**：第一个快递完成后立即开始下游同步

### 2. 数据可靠性改善
- **之前**：依赖上游数据完整性，存在数据不完整风险
- **现在**：基于下游实际数据状态，完全可靠的判断逻辑

### 3. 数据对比优化
- **之前**：依赖内存状态和复杂的数据解析逻辑
- **现在**：基于实际数据对比，通过快递单号精确匹配

### 4. 接口简化优化
- **之前**：客户端需要复杂计算和数据解析
- **现在**：接口职责清晰，客户端逻辑简化

### 5. 并行处理效率
- **场景**：一个采购单包含5个快递
- **之前**：串行等待，总时间 = max(快递1, 快递2, 快递3, 快递4, 快递5)
- **现在**：并行处理，总时间 = 各快递处理时间的并集

### 6. 用户体验改善
- 下游用户更快收到可处理的快递信息
- 减少业务流程的等待时间
- 提高整体业务效率

## 部署和使用

### 1. 向后兼容
- V2 版本继续存在，不影响现有功能
- 可以通过配置选择使用 V2 或 V3 版本

### 2. 渐进迁移
- 建议先在测试环境验证 V3 版本功能
- 确认无误后再切换到生产环境
- 可以按业务模块逐步迁移

### 3. 监控指标
- **同步延迟**：从快递创建到下游同步完成的时间
- **并发处理数**：同时处理的快递数量
- **成功率**：快递同步成功的比例
- **处理进度**：已创建/预期总数的比例

## 风险控制

### 1. 并发控制
- 限制同时处理的快递数量，避免系统过载
- 完善的错误处理，单个快递失败不影响其他快递

### 2. 幂等性保证
- 支持重复执行不会造成问题
- 确保快递不会被重复创建或处理

### 3. 异常恢复
- 系统宕机后能正确恢复处理状态
- 支持从中断点继续执行，不丢失进度

## 总结

`InventoryReceiptStepV3` 通过重构核心判断逻辑，从依赖上游数据转为基于下游实际状态，彻底解决了数据完整性问题和快递同步延迟问题。新版本具有以下特点：

### 核心优势
1. **数据可靠性**：基于下游实际数据状态，避免上游数据完整性问题
2. **逻辑简化**：接口直接返回完成状态，无需复杂客户端计算
3. **性能优化**：实现真正的实时同步，显著提升响应速度
4. **框架兼容**：完全符合 Temporal 确定性要求，支持可靠重放

### 重构成果
- **主判断逻辑**：下游快递商品数 = 下游采购单商品数
- **对比算法优化**：通过上下游快递单号精确对比，找出真正需要新增的快递
- **数据获取方式**：分别获取上下游全量数据，避免依赖不完整信息
- **接口职责清晰**：每个接口功能单一，数据完整，易于维护
- **容错能力强**：基于实际数据状态，具备自愈能力

### 核心算法
```java
// 快递对比核心逻辑
Set<String> downstreamLogisticsNos = downstreamList.stream()
    .map(ExpressSignDto::getLogisticsNo)
    .collect(Collectors.toSet());

List<ExpressSignDto> newExpressList = upstreamList.stream()
    .filter(express -> !downstreamLogisticsNos.contains(express.getLogisticsNo()))
    .collect(Collectors.toList());
```

在保持 Temporal 框架兼容性的同时，V3版本实现了更可靠、更准确的快递增量同步机制。