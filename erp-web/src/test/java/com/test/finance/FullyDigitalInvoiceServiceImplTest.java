package com.test.finance;

import com.google.common.eventbus.EventBus;
import com.test.BaseTestWithSpring;
import com.vedeng.common.core.listenerEvent.TraderBillPeriodEvent;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.math.BigDecimal;

public class FullyDigitalInvoiceServiceImplTest extends BaseTestWithSpring {

    @Autowired
    FullyDigitalInvoiceService fullyDigitalInvoiceService;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Test
    public void testOpenRedInvoice() {
        fullyDigitalInvoiceService.openRedInvoice(new InvoiceRedConfirmationDto());
    }

    @Test
    public void testOpenBlueInvoice() {
        // 销售订单开票处理订单生成账期逾期编码
        TraderBillPeriodEvent traderBillPeriodEvent = TraderBillPeriodEvent
                .builder()
                .invoiceId(1)
                .saleOrderId(1)
                .amount(BigDecimal.ZERO)
                .build();
        eventBusCenter.post(traderBillPeriodEvent);
    }
    
}