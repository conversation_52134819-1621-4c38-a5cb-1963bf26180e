

function getTraderRemoteList(data) {
    return axios({
        url: '/traderCustomerBase/query.do',
        method: 'post',
        params: data
    })
}
function getCustomerBaseInfo(data) {
    return axios({
        url: '/traderCustomerBase/getCustomerBaseInfo.do',
        method: 'post',
        params: data
    })
}


function getBidInfoByTraderId(param) {
    return axios({
        url: '/businessChance/getBidInfo.do',
        method: 'post',
        params: param
    })
}

function getLeadMerge(leadNo) {
    return axios({
        url: '/businessLeads/getLeadMerge.do',
        method: 'get',
        params: {"leadNo":leadNo}
    })
}

function getTraderInfoByTraderId(param) {
    return axios({
        url: '/businessChance/getTraderInfoByTraderId.do',
        method: 'post',
        params: param
    })
}

function getTraderCustomerPortrait(param) {
    return axios({
        url: '/traderCustomerBase/get/portrait.do',
        method: 'post',
        params: param
    })
}

function getCustomerBehaviorTrace(param) {
    return axios({
        url: '/traderCustomerBase/behavior/trace.do',
        method: 'post',
        params: param
    })
}

function getCustomerArchiveTrace(param) {
    return axios({
        url: '/traderCustomerBase/archive/trace.do',
        method: 'post',
        params: param
    })
}


function getDistributionLink(data) {
    return axios({
        url: '/traderCustomerBase/distributionLink/page.do',
        method: 'post',
        data: data
    })
}

function getDistributionLinkD3(traderId, linkSourceType, cooperationTimeFrame) {
    return axios({
        url: '/traderCustomerBase/distributionLink/d3.do?traderId=' + traderId + '&linkSourceType=' + linkSourceType + '&cooperationTimeFrame=' + cooperationTimeFrame,
        method: 'post'
    })
}


function getCpm(traderId) {
    return axios({
        url: '/traderCustomerBase/cpm.do',
        method: 'post',
        params: traderId
    })
}

function searchUser(username) {
    return axios({
        url: '/system/user/search.do',
        method: 'post',
        params: username
    })
}

function saveShareTrader(data) {
    return axios({
        url: '/traderCustomerBase/saveShareTrader.do',
        method: 'post',
        data: data
    })
}

function getShareTraderList(traderId) {
    return axios({
        url: '/traderCustomerBase/getShareTraderList.do',
        method: 'post',
        params: traderId
    })
}

function saveCancelShare(id) {
    return axios({
        url: '/traderCustomerBase/saveCancelShare.do',
        method: 'post',
        params: id
    })
}

function getLatestSummary(traderId) {
    return axios({
        url: '/communicateSummary/getByTraderId.do',
        method: 'get',
        params: traderId
    })
}

function getByCommunicateSummaryId(communicateSummaryId) {
    return axios({
        url: '/communicateSummary/getByCommunicateSummaryId.do',
        method: 'get',
        params: communicateSummaryId
    })
}

function saveModifySummary(data) {
    return axios({
        url: '/communicateSummary/update.do',
        method: 'post',
        data: data
    })
}

function getTraderCustomerTerminalPortrait(param) {
    return axios({
        url: '/traderCustomerBase/get/terminalPortrait.do',
        method: 'post',
        params: param
    })
}

function getTerminalDistributionLinkD3(searchName, linkSourceType, cooperationTimeFrame) {
    return axios({
        url: '/traderCustomerBase/terminalDistributionLink/d3.do?searchName=' + searchName + '&linkSourceType=' + linkSourceType + '&cooperationTimeFrame=' + cooperationTimeFrame,
        method: 'post'
    })
}

function getTerminalDistributionLink(data) {
    return axios({
        url: '/traderCustomerBase/terminalDistributionLink/page.do',
        method: 'post',
        data: data
    })
}

/**
 * 终端列表
 * @param data
 * @returns {*}
 */
function getTerminalPage(data) {
    return axios({
        url: '/traderCustomerBase/terminal/page.do',
        method: 'post',
        data: data
    })
}

/**
 * 终端经销商列表
 * @param data
 * @returns {*}
 */
function getCooperationDistributionLinkPage(data) {
    return axios({
        url: '/traderCustomerBase/terminal/cooperationDistributionLink/page.do',
        method: 'post',
        data: data
    })
}

// 批量拜访计划查询
function getDistributionVisitPlan(data) {
    return axios({
        url: '/traderCustomerBase/distribute/visitPlan.do',
        method: 'post',
        data: data
    })
}

function submitVisitPlan(data) {
    return axios({
        url: '/traderCustomerBase/submit/visitPlan.do',
        method: 'post',
        data: data
    })
}

function querySaleDirect(saleOrderId) {
    return axios({
        url: '/saleOrderExpress/querySaleDirect.do?saleOrderId='+saleOrderId,
        method: 'get'
    })
}

function querySaleInfo(saleOrderId) {
    return axios({
        url: '/saleOrderExpress/querySaleInfo.do?saleOrderId='+saleOrderId,
        method: 'get'
    })
}

function bindCommunicationIdAndSignFor(data) {
    return axios({
        url: '/saleOrderExpress/bindCommunicationIdAndSignFor.do',
        method: 'post',
        data: data
    })
}
function bindCommunicationId(data) {
    return axios({
        url: '/saleOrderExpress/bindCommunicationId.do',
        method: 'post',
        data: data
    })
}

function getExpressCommunicationId(expressId) {
    return axios({
        url: '/saleOrderExpress/getExpressCommunicationId.do?expressId='+expressId,
        method: 'get'
    })
}