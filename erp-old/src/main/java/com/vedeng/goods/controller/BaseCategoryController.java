package com.vedeng.goods.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.common.dto.TreeDto;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;
import com.vedeng.erp.finance.service.TaxcodeClassificationApiService;
import com.vedeng.goods.command.SpuAddCommand;
import com.vedeng.goods.vo.InspectionItemVo;
import com.vedeng.goods.model.BaseCategory;
import com.vedeng.goods.model.CategoryAttrValueMapping;
import com.vedeng.goods.model.CategoryMigration;
import com.vedeng.goods.model.FirstAndSecondCategoryInfo;
import com.vedeng.goods.model.dto.CategoryQueryDTO;
import com.vedeng.goods.model.dto.MoveCategoryDto;
import com.vedeng.goods.model.vo.*;
import com.vedeng.goods.service.*;
import com.vedeng.system.model.SysOptionDefinition;
import com.wms.service.util.GlobalThreadPool;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @param
 * <AUTHOR>
 * @description 分类基础信息
 * @date $
 */

@Controller
@RequestMapping("/category/base")
public class BaseCategoryController extends BaseController {

    @Autowired
    private BaseCategoryService baseCategoryService;

    @Autowired
    private BaseAttributeService baseAttributeService;

    @Autowired
    private BaseAttributeValueService baseAttributeValueService;

    @Autowired
    private VgoodsService goodsService;

    @Autowired
    private InspectionItemApiService inspectionItemApiService;

    @Autowired
    private BaseCategoryApiService baseCategoryApiService;

    @Autowired
    private TaxcodeClassificationApiService taxcodeClassificationApiService;


    @Value("${appoloMigrationReason}")
    private String appoloMigrationReason;


    @Value("${op.url}")
    private String op_url;

    /**
     * 有权限可以编辑科室和检查项目的用户ids Levi  kelly  Serena
     */
    private static final List<Integer> EDITABLE_USER_IDS = CollUtil.toList(1187, 977, 1399);
    /**
     * 税收编码是否同步可操作用户 kelly
     */
    @Value("${taxcode_synchronization_userId}")
    private String taxcodeSynchronizationUserId;

    /**
     * @description 打开分类新增或编辑界面
     * <AUTHOR>
     * @param
     * @date 2019/5/23
     */
    @FormToken(save = true)
    @RequestMapping(value = "/openCategoryPage")
    public ModelAndView openCategoryPage(HttpServletRequest request, BaseCategoryVo baseCategoryVo){
        try{
            ModelAndView categoryPageMv = new ModelAndView();
            //如果不为空，则为编辑
            if (baseCategoryVo.getBaseCategoryId() != null){
                ResultInfo result = this.checkDelete(baseCategoryVo);
                if (CommonConstants.FAIL_CODE.equals(result.getCode())){
                    return this.pageNotFound(request);
                }
                String firstLevelCategoryName = baseCategoryVo.getFirstLevelCategoryName();
                String secondLevelCategoryName = baseCategoryVo.getSecondLevelCategoryName();
                        //查询分类详细信息
                baseCategoryVo = baseCategoryService.getBaseCategoryByParam(baseCategoryVo.getBaseCategoryId());
                if (StrUtil.isNotBlank(baseCategoryVo.getTaxClassificationCode())) {
                    TaxcodeClassificationDto taxcodeClassificationDto = taxcodeClassificationApiService.findByCode(baseCategoryVo.getTaxClassificationCode());
                    if (Objects.nonNull(taxcodeClassificationDto)) {
                        baseCategoryVo.setTaxCodeSimpleName(taxcodeClassificationDto.getClassificationAbbreviation());
                    }
                }
                baseCategoryVo.setFirstLevelCategoryName(firstLevelCategoryName);
                baseCategoryVo.setSecondLevelCategoryName(secondLevelCategoryName);
                if (CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())){
                    //如果是三级分类，需要查询引用的属性及属性值列表信息
                    List<BaseCategoryVo> baseCategoryVoList = new ArrayList<>();
                    baseCategoryVoList.add(baseCategoryVo);
                    //查询分类关联的属性列表信息
                    List<BaseAttributeVo> attributeVoList = baseAttributeService.getAttributeListByCategoryId(baseCategoryVoList);
                    //查询分类关联的属性值列表信息
                    List<BaseAttributeValueVo> attributeValueVoList = baseAttributeValueService.getAttrValueListByCategoryId(baseCategoryVoList);
                    attributeVoList = baseCategoryService.doAttrAndValueToString(attributeVoList,attributeValueVoList);
                    baseCategoryVo.setAttributeVoList(attributeVoList);
                }
            }

            // 获取科室
            List<CategoryDepartmentVo> categoryDepartmentVoList = baseCategoryService.listCategoryDepartmentVo(baseCategoryVo.getBaseCategoryId());
            categoryPageMv.addObject("departmentsHospitalList", categoryDepartmentVoList);

            // 获取检查项目
            List<InspectionItemVo> inspectionItemDtoList = inspectionItemApiService.getInspectionItemList(baseCategoryVo.getBaseCategoryId());
            categoryPageMv.addObject("inspectionItemDtoList", inspectionItemDtoList);

            // 是否有权限编辑科室和检查项目
            CurrentUser currentUser = CurrentUser.getCurrentUser();
            categoryPageMv.addObject("editableDepartmentOrInspection",!EDITABLE_USER_IDS.contains(currentUser.getId()));
            List<Integer> taxcodeSyncUserIds = JSON.parseArray(taxcodeSynchronizationUserId, Integer.class);
            categoryPageMv.addObject("synchronousOrNot", taxcodeSyncUserIds.contains(currentUser.getId()));

            //查询所有属性以及属性下面的属性值列表
            BaseAttributeVo baseAttributeVo = new BaseAttributeVo();
            baseAttributeVo.setIsDeleted(CommonConstants.IS_DELETE_0);//未删除状态
            // 获取属性列表
            List<BaseAttributeVo> list = baseAttributeService.getBaseAttributeInfoListPage(baseAttributeVo, null);
            // 获取属性值列表
            BaseAttributeValueVo baseAttributeValueVo = new BaseAttributeValueVo();
            baseAttributeValueVo.setIsDeleted(CommonConstants.IS_DELETE_0);//未删除状态
            List<BaseAttributeValueVo> baseAttributeValueVoList = baseAttributeValueService.getBaseAttributeValueVoListASC(baseAttributeValueVo,list);
            String attrAndValueJson = baseCategoryService.doAttrAndValueToJson(list,baseAttributeValueVoList);
            //categoryPageMv.addObject("formToken",request.getAttribute("formtoken"));
            categoryPageMv.addObject("attrAndValueJson",attrAndValueJson);
            categoryPageMv.addObject("baseCategoryVo",baseCategoryVo);
            categoryPageMv.setViewName("goods/basecategory/add");
            return categoryPageMv;
        }catch (Exception e){
            logger.error("打开分类新增或编辑界面异常：",e);
            return this.page500(request);
        }
    }

    /**
     * @param
     * @description 保存分类信息
     * <AUTHOR>
     * @date 2019/5/8
     */
    @NoNeedAccessAuthorization
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveCategory")
    public ModelAndView saveBaseCategory(HttpServletRequest request, BaseCategoryVo baseCategoryVo) {
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            if (user == null) {
                request.setAttribute("error", "登陆信息已失效，请重新登陆");
            }

            if (baseCategoryVo.getBaseCategoryId() != null) {
                BaseCategory baseCategory = baseCategoryService.getBaseCategoryByParam(baseCategoryVo.getBaseCategoryId());
                if (CommonConstants.IS_DELETE_1.equals(baseCategory.getIsDeleted())) {
                    return this.pageNotFound(request);
                }
            }

            //校验
            ResultInfo checkResult = baseCategoryService.checkCategoryField(baseCategoryVo);
            if (!CommonConstants.SUCCESS_CODE.equals(checkResult.getCode())) {
                request.setAttribute("error", checkResult.getMessage());
            }
            if (baseCategoryVo.getBaseCategoryId() != null) {
                ResultInfo result = this.checkDelete(baseCategoryVo);
                if (CommonConstants.FAIL_CODE.equals(result.getCode())) {
                    return this.pageNotFound(request);
                }
                baseCategoryVo.setUpdater(user.getUserId());
            } else {
                baseCategoryVo.setUpdater(user.getUserId());
                baseCategoryVo.setCreator(user.getUserId());
            }

            // 保存分类信息
            ResultInfo resultInfo = baseCategoryService.saveBaseCategory(baseCategoryVo, user);
            if (resultInfo == null) {
                request.setAttribute("error", "商品分类保存失败");
            }

            // 成功，重定向到详情页
            if (CommonConstants.SUCCESS_CODE.equals(resultInfo.getCode())) {
                if (baseCategoryVo.getIsEditGoods() != null && baseCategoryVo.getIsEditGoods().equals(CommonConstants.STATUS_1)) {
                    //如果该分类下有商品，且修改了下面的属性属性值，则更新商品状态
                    goodsService.changeSpuStatusByCategoryChange(request, baseCategoryVo.getBaseCategoryId(), user);
                }
                String url = "redirect:./getCategoryInfo.do?baseCategoryId=" + resultInfo.getData();
                if (CommonConstants.CATEGORY_LEVEL_2.equals(baseCategoryVo.getBaseCategoryLevel())) {
                    url = url + "&firstLevelCategoryName=" + URLEncoder.encode(baseCategoryVo.getFirstLevelCategoryName(), "UTF-8");
                }
                if (CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())) {
                    if (Constants.ONE.equals(baseCategoryVo.getTaxCodeSync())) {
                        log.info("同步税收编码,baseCategoryId:{},taxClassificationCode:{}", baseCategoryVo.getBaseCategoryId(), baseCategoryVo.getTaxClassificationCode());
                        baseCategoryApiService.synchronousTaxCode(baseCategoryVo.getBaseCategoryId(), baseCategoryVo.getTaxClassificationCode());
                    }

                    url = url + "&firstLevelCategoryName=" + URLEncoder.encode(baseCategoryVo.getFirstLevelCategoryName(), "UTF-8")
                            + "&secondLevelCategoryName=" + URLEncoder.encode(baseCategoryVo.getSecondLevelCategoryName(), "UTF-8");
                }
                return new ModelAndView(url);
            } else {
                request.setAttribute("error", resultInfo.getMessage());
            }
            return new ModelAndView("forward:./openNewCategoryPage.do");
        } catch (Exception e) {
            logger.error("商品分类保存异常：", e);
            return this.page500(request);
        }
    }

    @RequestMapping("/taxCodePopup")
    @NoNeedAccessAuthorization
    public ModelAndView taxCodePopup() {
        ModelAndView modelAndView = new ModelAndView("goods/basecategory/tax_code_popup");
        return modelAndView;
    }


    /**
     * @description 查看分类详情
     * <AUTHOR>
     * @param
     * @date 2019/5/16
     */
    @RequestMapping(value = "/getCategoryInfo")
    public ModelAndView getCategoryInfo(HttpServletRequest request, BaseCategoryVo baseCategoryVo){
        try{
            ResultInfo result = this.checkDelete(baseCategoryVo);
            if (CommonConstants.FAIL_CODE.equals(result.getCode())){
                return this.pageNotFound(request);
            }
            ModelAndView categoryInfoMv = new ModelAndView();
            String firstLevelCategoryName = baseCategoryVo.getFirstLevelCategoryName();
            String secondLevelCategoryName = baseCategoryVo.getSecondLevelCategoryName();
            // 基本信息 -- 根据id查询属性属性值信息
            baseCategoryVo = baseCategoryService.getBaseCategoryByParam(baseCategoryVo.getBaseCategoryId());
            if (StrUtil.isNotBlank(baseCategoryVo.getTaxClassificationCode())) {
                TaxcodeClassificationDto taxcodeClassificationDto = taxcodeClassificationApiService.findByCode(baseCategoryVo.getTaxClassificationCode());
                if (Objects.nonNull(taxcodeClassificationDto)) {
                    baseCategoryVo.setTaxCodeSimpleName(taxcodeClassificationDto.getClassificationAbbreviation());
                }
            }
            baseCategoryVo.setFirstLevelCategoryName(firstLevelCategoryName);
            baseCategoryVo.setSecondLevelCategoryName(secondLevelCategoryName);
            if (CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())){
                //如果是三级分类，需要查询引用的属性及属性值列表信息
                List<BaseCategoryVo> baseCategoryVoList = new ArrayList<>();
                baseCategoryVoList.add(baseCategoryVo);
                //查询分类关联的属性列表信息
                List<BaseAttributeVo> attributeVoList = baseAttributeService.getAttributeListByCategoryId(baseCategoryVoList);
                //查询分类关联的属性值列表信息
                List<BaseAttributeValueVo> attributeValueVoList = baseAttributeValueService.getAttrValueListByCategoryId(baseCategoryVoList);
                attributeVoList = baseAttributeService.doAttrAndValue(attributeVoList,attributeValueVoList);
                baseCategoryVo.setAttributeVoList(attributeVoList);
                //获取科室信息
                List<CategoryDepartmentVo> categoryDepartmentVoList = baseCategoryService.listCategoryDepartmentVo(baseCategoryVo.getBaseCategoryId());
                categoryInfoMv.addObject("categoryDepartmentList", categoryDepartmentVoList);
                // 获取检查项目
                List<InspectionItemVo> inspectionItemDtoList = inspectionItemApiService.getInspectionItemList(baseCategoryVo.getBaseCategoryId());
                categoryInfoMv.addObject("inspectionItemDtoList", inspectionItemDtoList);

            }
            categoryInfoMv.addObject("baseCategoryVo",baseCategoryVo);
            categoryInfoMv.setViewName("goods/basecategory/view");
            return categoryInfoMv;
        }catch (Exception e){
            logger.error("查看分类详情异常：",e);
            return this.page500(request);
        }
    }


    /**
     * @description 获取商品分类列表
     * <AUTHOR>
     * @param
     * @date 2019/5/23
     */
    @RequestMapping(value = "/index")
    public ModelAndView getCategoryListPage(HttpServletRequest request, BaseCategoryVo baseCategoryVo,
           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
           @RequestParam(required = false) Integer pageSize){
        try{
            ModelAndView categoryListmv = new ModelAndView();
            Page page = getPageTag(request, pageNo, pageSize);
            // 未删除状态
            baseCategoryVo.setIsDeleted(CommonConstants.IS_DELETE_0);
            //paramMap.put("isDeleted", CommonConstants.IS_DELETE_0);
            // 获取一级分类列表
            baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_1);
            List<BaseCategoryVo> firstCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,page);
            List<BaseCategoryVo> secondCategoryList = null;
            List<BaseCategoryVo> thirdCategoryList = null;
            List<CategoryAttrValueMappingVo> categoryAttrValueMappingVoList = null;
            if (CollectionUtils.isNotEmpty(firstCategoryList)){
                // 获取二级分类列表
                baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_2);
                secondCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,null);
                if (CollectionUtils.isNotEmpty(secondCategoryList)){
                    // 获取三级分类列表
                    baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_3);
                    thirdCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,null);
                    if (CollectionUtils.isEmpty(thirdCategoryList) || thirdCategoryList.get(0) == null ||
                            (thirdCategoryList != null && thirdCategoryList.size() == 1 && thirdCategoryList.get(0) != null
                            && thirdCategoryList.get(0).getBaseCategoryId() == null)) {
                        thirdCategoryList = null;
                    }else{
                        // 获取三级分类下引用的属性关联列表
                        categoryAttrValueMappingVoList =  baseCategoryService.getCategoryAttrValueMappingVoList(thirdCategoryList);
                    }
                }
            }
            //计算一级分类下的二级分类与三级分类数量
            List<BaseCategoryVo> baseCategoryVoList=this.doBaseCategoryLevel(firstCategoryList,
                    secondCategoryList,thirdCategoryList,categoryAttrValueMappingVoList);


            for (BaseCategoryVo baseCategory: baseCategoryVoList) {
                List<BaseCategoryVo> secondBaseCategory=baseCategory.getSecondCategoryList();
                baseCategory.setSecondCategoryNum(secondBaseCategory.size());
                Integer thirdCount=0;
                for (BaseCategoryVo secondLevel: secondBaseCategory) {
                    List<BaseCategoryVo> thirdBaseCategory=secondLevel.getThirdCategoryList();
                    secondLevel.setThirdCategoryNum(thirdBaseCategory.size());
                    thirdCount+=thirdBaseCategory.size();
                }
                baseCategory.setThirdCategoryNum(thirdCount);
            }





            // 处理分类级别关联
            categoryListmv.addObject("list",baseCategoryVoList);
            categoryListmv.addObject("page",page);
            categoryListmv.addObject("baseCategoryVo",baseCategoryVo);
            categoryListmv.setViewName("goods/basecategory/index");
            return categoryListmv;
        }catch (Exception e){
            logger.error("获取商品分类列表异常：",e);
            return this.page500(request);
        }
    }

    /**
     * @description 转发打开新增或编辑分类页
     * <AUTHOR>
     * @param
     * @date 2019/5/8
     */
    @FormToken(save = true)
    @RequestMapping(value = "/openNewCategoryPage")
    public ModelAndView openNewCategoryPage(HttpServletRequest request, BaseCategoryVo baseCategoryVo) {
        try {
            if (baseCategoryVo.getBaseCategoryId()!= null && !"".equals(baseCategoryVo.getBaseCategoryId())){
                ResultInfo result = this.checkDelete(baseCategoryVo);
                if (CommonConstants.FAIL_CODE.equals(result.getCode())){
                    return this.pageNotFound(request);
                }
            }
            ModelAndView newCategoryMv = new ModelAndView();
            if (CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())){
                //处理传过来的属性与属性值
                baseCategoryVo.setAttributeVoList(baseCategoryService.doAttrAndValueToList(baseCategoryVo));
            }
            //查询所有属性以及属性下面的属性值列表
            BaseAttributeVo baseAttributeVo = new BaseAttributeVo();
            baseAttributeVo.setIsDeleted(CommonConstants.IS_DELETE_0);//未删除状态
            // 获取属性列表
            List<BaseAttributeVo> list = baseAttributeService.getBaseAttributeInfoListPage(baseAttributeVo, null);
            // 获取属性值列表
            BaseAttributeValueVo baseAttributeValueVo = new BaseAttributeValueVo();
            baseAttributeValueVo.setIsDeleted(CommonConstants.IS_DELETE_0);//未删除状态
            List<BaseAttributeValueVo> baseAttributeValueVoList = baseAttributeValueService.getBaseAttributeValueVoListASC(baseAttributeValueVo,list);
            String attrAndValueJson = baseCategoryService.doAttrAndValueToJson(list,baseAttributeValueVoList);
            //newCategoryMv.addObject("formToken",request.getAttribute("formtoken"));
            newCategoryMv.addObject("attrAndValueJson", attrAndValueJson);
            newCategoryMv.addObject("baseCategoryVo", baseCategoryVo);
            newCategoryMv.addObject("error", request.getAttribute("error"));
            newCategoryMv.setViewName("goods/basecategory/add");
            return newCategoryMv;
        }catch (Exception e){
            logger.error("转发打开新增或编辑分类页异常：",e);
            return this.page500(request);
        }
    }

    /**
     * @description 删除分类
     * <AUTHOR>
     * @param
     * @date 2019/5/17
     */
    @RequestMapping(value = "/deleteCategory")
    @ResponseBody
    public ResultInfo deleteCategory(HttpServletRequest request, BaseCategoryVo baseCategoryVo){
        try{
            // 当前用户
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            if (user != null) {//登陆已失效
                BaseCategory baseCategory = baseCategoryService.getBaseCategoryByParam(baseCategoryVo.getBaseCategoryId());
                if (CommonConstants.IS_DELETE_1.equals(baseCategory.getIsDeleted())){
                    return new ResultInfo(CommonConstants.FAIL_CODE,"该分类已经被删除，无法操作");
                }
                baseCategoryVo.setUpdater(user.getUserId());
                baseCategoryVo.setModTime(new Date());
                baseCategoryVo.setIsDeleted(CommonConstants.IS_DELETE_1);
                List<BaseCategoryVo> firstCategoryList = null;
                List<BaseCategoryVo> secondCategoryList = null;
                List<BaseCategoryVo> thirdCategoryList = null;
                List<CategoryAttrValueMappingVo> categoryAttrValueMappingVoList = null;
                //如果删除的是一级分类，则删除该一级分类以及该一级分类下的所有二级、三级分类，以及三级分类下的所有属性引用关系
                if (CommonConstants.CATEGORY_LEVEL_1.equals(baseCategoryVo.getBaseCategoryLevel())){
                    firstCategoryList = new ArrayList<>();
                    firstCategoryList.add(baseCategoryVo);
                    //查询出该一级分类下的所有未删除的二级分类
                    secondCategoryList = baseCategoryService.getCategoryListByIds(firstCategoryList,CommonConstants.CATEGORY_LEVEL_2);
                    //查询出所有二级分类下的三级分类
                    if (CollectionUtils.isNotEmpty(secondCategoryList)){
                        thirdCategoryList = baseCategoryService.getCategoryListByIds(secondCategoryList,CommonConstants.CATEGORY_LEVEL_3);
                    }
                }
                //如果删除的是二级分类，则删除该二级分类以及该二级分类下的所有三级分类，以及三级分类下的所有属性引用关系
                if (CommonConstants.CATEGORY_LEVEL_2.equals(baseCategoryVo.getBaseCategoryLevel())){
                    //查询出该二级分类下的所有三级分类
                    secondCategoryList = new ArrayList<>();
                    secondCategoryList.add(baseCategoryVo);
                    thirdCategoryList = baseCategoryService.getCategoryListByIds(secondCategoryList,CommonConstants.CATEGORY_LEVEL_3);
                }
                //如果是三级分类，则删除该分类本身，以及三级分类下的所有属性引用关系
                if (CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())){
                    //查询出三级分类下的商品数目信息
                    thirdCategoryList = baseCategoryService.getthirdCategoryListById(baseCategoryVo);
                }
                //验证是否可删除
                if (CollectionUtils.isEmpty(thirdCategoryList) || thirdCategoryList.get(0) == null ||
                        (thirdCategoryList != null && thirdCategoryList.size() == 1 && thirdCategoryList.get(0) != null
                                && thirdCategoryList.get(0).getBaseCategoryId() == null)){
                    thirdCategoryList = null;
                }else{
                    for (BaseCategoryVo baseCategoryVoTemp : thirdCategoryList){
                        Integer coreProductNum = baseCategoryVoTemp.getCoreProductNum() == null ? 0 : baseCategoryVoTemp.getCoreProductNum();
                        Integer temporaryProductNum = baseCategoryVoTemp.getTemporaryProductNum() == null ? 0 : baseCategoryVoTemp.getTemporaryProductNum();
                        Integer otherProductNum = baseCategoryVoTemp.getOtherProductNum() == null ? 0 : baseCategoryVoTemp.getOtherProductNum();
                        if (coreProductNum > 0 || temporaryProductNum > 0
                                || otherProductNum > 0){
                            return new ResultInfo(CommonConstants.FAIL_CODE,"分类下存在商品，暂不可删除！");
                        }
                    }
                }
                //删除操作
                Integer result = baseCategoryService.deleteCategory(firstCategoryList,secondCategoryList,thirdCategoryList,user);
                if (result > 0){
                    return new ResultInfo(CommonConstants.SUCCESS_CODE,"删除成功！");
                }
                return new ResultInfo(CommonConstants.FAIL_CODE,"删除失败！");
            }else{
                return new ResultInfo(CommonConstants.FAIL_CODE,"登陆信息已失效，请重新登陆！");
            }
        }catch (Exception e){
            logger.error("删除分类信息异常：",e);
            return new ResultInfo(CommonConstants.FAIL_CODE,"删除分类异常！");
        }
    }

    /**
     * @description 根据属性id查询所有引用的分类
     * <AUTHOR>
     * @param
     * @date 2019/5/17
     */
    @RequestMapping(value = "/getCateforyListByAttrId")
    @ResponseBody
    public ResultInfo getCateforyListByAttrId(HttpServletRequest request, Integer baseAttributeId,
        @RequestParam(required = false, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize){
        ResultInfo resultInfo = new ResultInfo();
        Page page = getPageTag(request, pageNo, pageSize);
        // 已引用商品分类
        List<BaseCategoryVo> categoryList = baseCategoryService.getBaseCategoryListPageByAttr(baseAttributeId, page);
        resultInfo.setCode(CommonConstants.SUCCESS_CODE);
        resultInfo.setListData(categoryList);
        resultInfo.setPage(page);
        resultInfo.setMessage("查询成功！");
        return resultInfo;
    }

    /**
     * @description 处理分类级别关联
     * <AUTHOR>
     * @param
     * @date 2019/5/23
     */
    public List<BaseCategoryVo> doBaseCategoryLevel(List<BaseCategoryVo> firstCategoryList,
                    List<BaseCategoryVo> secondCategoryList,List<BaseCategoryVo> thirdCategoryList,
                    List<CategoryAttrValueMappingVo> categoryAttrValueMappingVoList){
        try{
            //先将三级分类关联的属性列表关联到三级分类中
            if (CollectionUtils.isNotEmpty(thirdCategoryList)){
                for (BaseCategoryVo thirdCategory : thirdCategoryList) {
                    if (thirdCategory != null){
                        List<CategoryAttrValueMappingVo> attributeVoList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(categoryAttrValueMappingVoList)){
                           for (CategoryAttrValueMappingVo categoryAttrValueMappingVo : categoryAttrValueMappingVoList) {
                                if (categoryAttrValueMappingVoList != null){
                                    if (thirdCategory.getBaseCategoryId().equals(categoryAttrValueMappingVo.getBaseCategoryId())){
                                        attributeVoList.add(categoryAttrValueMappingVo);
                                    }
                                }
                            }
                        }
                        thirdCategory.setCategoryAttrValueMappingVoList(attributeVoList);
                    }
                }
            }

            //先将三级分类关联到二级分类中
            if (CollectionUtils.isNotEmpty(secondCategoryList)) {
                for (BaseCategoryVo secondCategory : secondCategoryList) {
                    if (secondCategory != null){
                        List<BaseCategoryVo> categoryVoList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(thirdCategoryList)) {
                            for (BaseCategoryVo thirdCategory : thirdCategoryList) {
                                if (thirdCategory !=  null){
                                    if (secondCategory.getBaseCategoryId().equals(thirdCategory.getParentId())) {
                                        categoryVoList.add(thirdCategory);
                                        Integer coreProductNum = thirdCategory.getCoreProductNum() == null ? 0 : thirdCategory.getCoreProductNum();
                                        Integer temporaryProductNum = thirdCategory.getTemporaryProductNum() == null ? 0 : thirdCategory.getTemporaryProductNum();
                                        Integer otherProductNum = thirdCategory.getOtherProductNum() == null ? 0 : thirdCategory.getOtherProductNum();
                                        secondCategory.setCoreProductNum(secondCategory.getCoreProductNum() == null ? coreProductNum : secondCategory.getCoreProductNum() + coreProductNum);
                                        secondCategory.setTemporaryProductNum(secondCategory.getTemporaryProductNum() == null ? temporaryProductNum : secondCategory.getTemporaryProductNum() + temporaryProductNum);
                                        secondCategory.setOtherProductNum(secondCategory.getOtherProductNum() == null ? otherProductNum : secondCategory.getOtherProductNum() + otherProductNum);
                                    }
                                }
                            }
                        }
                        secondCategory.setThirdCategoryList(categoryVoList);
                    }
                }
            }
            //再将二级分类关联到一级分类中
            if (CollectionUtils.isNotEmpty(firstCategoryList)) {
                for (BaseCategoryVo firstCategory : firstCategoryList) {
                    if (firstCategory !=  null){
                        List<BaseCategoryVo> categoryVoList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(secondCategoryList)) {
                            for (BaseCategoryVo secondCategory : secondCategoryList) {
                                if (secondCategory != null){
                                    if (firstCategory.getBaseCategoryId().equals(secondCategory.getParentId())){
                                        categoryVoList.add(secondCategory);
                                        Integer coreProductNum = firstCategory.getCoreProductNum() == null ? 0 : firstCategory.getCoreProductNum();
                                        Integer temporaryProductNum = firstCategory.getTemporaryProductNum() == null ? 0 : firstCategory.getTemporaryProductNum();
                                        Integer otherProductNum = firstCategory.getOtherProductNum() == null ? 0 : firstCategory.getOtherProductNum();
                                        firstCategory.setCoreProductNum(secondCategory.getCoreProductNum() == null ? coreProductNum : secondCategory.getCoreProductNum() + coreProductNum);
                                        firstCategory.setTemporaryProductNum(secondCategory.getTemporaryProductNum() == null ? temporaryProductNum : secondCategory.getTemporaryProductNum() + temporaryProductNum);
                                        firstCategory.setOtherProductNum(secondCategory.getOtherProductNum() == null ? otherProductNum : secondCategory.getOtherProductNum() + otherProductNum);
                                    }
                                }
                            }
                        }
                        firstCategory.setSecondCategoryList(categoryVoList);
                    }
                }
            }
            //剔除不满足搜索条件的所有分类

            return firstCategoryList;
        }catch (Exception e){
            logger.error("处理分类级别关联",e);
            return null;
        }
    }

    /**
     * 列表页检查是否已经删除
     * @param baseCategoryVo
     * @return
     */
    public ResultInfo checkDelete(BaseCategoryVo baseCategoryVo){
        baseCategoryVo = baseCategoryService.getBaseCategoryByParam(baseCategoryVo.getBaseCategoryId());
        if (CommonConstants.IS_DELETE_1.equals(baseCategoryVo.getIsDeleted())){
            return new ResultInfo(CommonConstants.FAIL_CODE,CommonConstants.FAIL_MSG);
        }
        return new ResultInfo(CommonConstants.SUCCESS_CODE,CommonConstants.SUCCESS_MSG);
    }

    /**
     * @description 获取分类列表
     * <AUTHOR>
     * @param
     * @date 2019/5/23
     */
    @RequestMapping(value = {"/getCategoryList","/static/getCategoryList"})
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo getCategoryList(HttpServletRequest request, HttpServletResponse response){
        try{
            BaseCategoryVo baseCategoryVo = new BaseCategoryVo();
            // 未删除状态
            baseCategoryVo.setIsDeleted(CommonConstants.IS_DELETE_0);
            //paramMap.put("isDeleted", CommonConstants.IS_DELETE_0);
            // 获取一级分类列表
            baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_1);
            List<BaseCategoryVo> firstCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,null);
            List<BaseCategoryVo> secondCategoryList = null;
            List<BaseCategoryVo> thirdCategoryList = null;
            if (CollectionUtils.isNotEmpty(firstCategoryList)){
                // 获取二级分类列表
                baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_2);
                secondCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,null);
                if (CollectionUtils.isNotEmpty(secondCategoryList)){
                    // 获取三级分类列表
                    baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_3);
                    thirdCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,null);
                    if (CollectionUtils.isEmpty(thirdCategoryList) || thirdCategoryList.get(0) == null ||
                            (thirdCategoryList != null && thirdCategoryList.size() == 1 && thirdCategoryList.get(0) != null
                                    && thirdCategoryList.get(0).getBaseCategoryId() == null)) {
                        thirdCategoryList = null;
                    }
                }
            }
            // 处理分类级别关联
            firstCategoryList = this.doBaseCategoryLevel(firstCategoryList,secondCategoryList,thirdCategoryList,null);
                return new ResultInfo(CommonConstants.SUCCESS_CODE,CommonConstants.SUCCESS_MSG,firstCategoryList);
        }catch (Exception e){
            logger.error("处理分类级别关联",e);
            return null;
        }
    }

    /**
     * @description 检查三级分类下是否有商品，有商品的话保存三级分类是否修改了属性以及属性值
     * <AUTHOR>
     * @param
     * @date 2019/5/23
     */
    @RequestMapping(value = "/checkCategoryAttr")
    @ResponseBody
    public ResultInfo checkCategoryAttr(Integer baseCategoryId,String attrName,String attrValue,
           HttpServletRequest request, HttpServletResponse response){
        try{
            //如果是三级分类，且是编辑后保存，查询分类下是否有商品
            BaseCategoryVo baseCategoryVo = new BaseCategoryVo();
            baseCategoryVo.setIsDeleted(CommonConstants.IS_DELETE_0);
            baseCategoryVo.setBaseCategoryId(baseCategoryId);
            baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_3);
            List<BaseCategoryVo> thirdCategoryList = baseCategoryService.getthirdCategoryListById(baseCategoryVo);
            if (CollectionUtils.isEmpty(thirdCategoryList) || thirdCategoryList.get(0) == null ||
                    (thirdCategoryList != null && thirdCategoryList.size() == 1 && thirdCategoryList.get(0) != null
                            && thirdCategoryList.get(0).getBaseCategoryId() == null)){
                thirdCategoryList = null;
            }else{
                BaseCategoryVo baseCategoryVoTemp = thirdCategoryList.get(0);
                Integer coreProductNum = baseCategoryVoTemp.getCoreProductNum() == null ? 0 : baseCategoryVoTemp.getCoreProductNum();
                Integer temporaryProductNum = baseCategoryVoTemp.getTemporaryProductNum() == null ? 0 : baseCategoryVoTemp.getTemporaryProductNum();
                Integer otherProductNum = baseCategoryVoTemp.getOtherProductNum() == null ? 0 : baseCategoryVoTemp.getOtherProductNum();
                if (coreProductNum > 0 || temporaryProductNum > 0
                        || otherProductNum > 0){
                    //处理属性与属性值
                    String[] attrIdArray = attrName.split(",");
                    String[] attrValueIdsArray = attrValue.split(",");
                    baseCategoryVo.setBaseAttributeId(attrIdArray);
                    baseCategoryVo.setBaseAttributeValueIds(attrValueIdsArray);
                    List<BaseAttributeVo> baseAttributeVoList = baseCategoryService.doAttrAndValueToList(baseCategoryVo);
                    if (CollectionUtils.isNotEmpty(baseAttributeVoList)){
                        List<CategoryAttrValueMappingVo> categoryAttrValueMappingVos = new ArrayList<>();
                        for (BaseAttributeVo attributeVo : baseAttributeVoList){
                            String[] attrValueIdArray = attributeVo.getBaseAttributeValueIds().split("@");
                            for (String attrValueId : attrValueIdArray){
                                CategoryAttrValueMappingVo categoryAttrValueMappingVo = new CategoryAttrValueMappingVo();
                                categoryAttrValueMappingVo.setBaseAttributeId(attributeVo.getBaseAttributeId());
                                if (attrValueId!=null&& !"".equals(attrValueId)){
                                    categoryAttrValueMappingVo.setBaseAttributeValueId(Integer.valueOf(attrValueId));
                                }
                                categoryAttrValueMappingVos.add(categoryAttrValueMappingVo);
                            }
                        }
                        //查询该分类下的属性与属性值关联列表
                        List<CategoryAttrValueMapping> categoryAttrValueMappingVoList = baseCategoryService.getCategoryAttrValueMappingVoList(baseCategoryId);
                        if (CollectionUtils.isNotEmpty(categoryAttrValueMappingVos) && CollectionUtils.isNotEmpty(categoryAttrValueMappingVoList)){
                            Integer categoryAttrValueMappingNum = 0;
                            for (CategoryAttrValueMapping categoryAttrValueMapping : categoryAttrValueMappingVoList){
                                for (CategoryAttrValueMappingVo categoryAttrValueMappingVo : categoryAttrValueMappingVos){
                                    if (categoryAttrValueMapping.getBaseAttributeId().equals(categoryAttrValueMappingVo.getBaseAttributeId())
                                         && categoryAttrValueMapping.getBaseAttributeValueId().equals(categoryAttrValueMappingVo.getBaseAttributeValueId())){
                                        categoryAttrValueMappingNum = categoryAttrValueMappingNum + 1;
                                    }
                                }
                            }
                            if (categoryAttrValueMappingNum != categoryAttrValueMappingVoList.size()){
                                return new ResultInfo(CommonConstants.FAIL_CODE,"修改类目，会导致所有子商品参数信息丢失且不可恢复，并且会下架所有子商品，请确认是否继续修改？");
                            }
                        }
                    }
                }
            }
            return new ResultInfo(CommonConstants.SUCCESS_CODE,CommonConstants.SUCCESS_MSG);
        }catch (Exception e){
            logger.error("检查三级分类下上商品异常：",e);
            return null;
        }
    }

    /**
     *  根据关键词（商品名/分类名/国标码）获取分类列表
     * @param keyWords
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/getCategoryListByKeyWords")
    @ResponseBody
    public ResultInfo getCategoryListByKeyWords(String keyWords, HttpServletRequest request, HttpServletResponse response){
        try{
            if (keyWords == null || "".equals(keyWords)){
                return new ResultInfo(CommonConstants.FAIL_CODE,"关键词不可为空");
            }else {
                List<BaseCategoryVo> baseCategoryVoList = baseCategoryService.getCategoryListByKeyWords(keyWords);
                return new ResultInfo(CommonConstants.SUCCESS_CODE, CommonConstants.SUCCESS_MSG, baseCategoryVoList);
            }
        }catch (Exception e){
            logger.error("根据关键词（商品名/分类名/国标码）获取分类列表异常：",e);
            return null;
        }
    }

    @ResponseBody
    @RequestMapping(value="/getCategoryListByParentId")
    public ResultInfo<?> getCategoryListByParentId(HttpServletRequest request, BaseCategoryVo baseCategoryVo){
        ResultInfo<BaseCategoryVo> result = new ResultInfo<>();
        List<BaseCategoryVo> standardCategoryList = baseCategoryService.getCategoryListByParentId(baseCategoryVo.getBaseCategoryId());
        if(StringUtils.equals("1",request.getParameter("loadChildren"))){
            standardCategoryList.forEach(this::loadChildren);
        }
        result.setCode(0);
        result.setMessage("操作成功");
        result.setListData(standardCategoryList);
        return result;
    }
    @ResponseBody
    @RequestMapping(value="/treeCategory")
    @NoNeedAccessAuthorization
    public R<List<TreeDto>> treeDept(HttpServletRequest request, BaseCategoryVo baseCategoryVo){
        if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_CATEGORY_LIST + baseCategoryVo.getParentId())){
            List<TreeDto> treeDtos = JSON.parseArray(JedisUtils.get(dbType + ErpConst.KEY_PREFIX_CATEGORY_LIST + baseCategoryVo.getParentId()), TreeDto.class);
            return  R.success(treeDtos);
        }else{
            List<BaseCategoryVo> standardCategoryList = baseCategoryService.getCategoryListByParentId(baseCategoryVo.getParentId());
            standardCategoryList.forEach(this::loadChildren);

            List<TreeDto> treeDtos = standardCategoryList.stream().map(item -> {
                TreeDto treeDto = new TreeDto();
                treeDto.setValue(item.getBaseCategoryId()+"");
                treeDto.setLabel(item.getBaseCategoryName());
                if (CollectionUtils.isNotEmpty(item.getSecondCategoryList())) {
                    treeDto.setChildren(item.getSecondCategoryList().stream().map(secondItem -> {
                        TreeDto secondTreeDto = new TreeDto();
                        secondTreeDto.setValue(secondItem.getBaseCategoryId()+"");
                        secondTreeDto.setLabel(secondItem.getBaseCategoryName());
                        if (CollectionUtils.isNotEmpty(secondItem.getSecondCategoryList())) {
                            secondTreeDto.setChildren(secondItem.getSecondCategoryList().stream().map(thirdItem -> {
                                TreeDto thirdTreeDto = new TreeDto();
                                thirdTreeDto.setValue(thirdItem.getBaseCategoryId()+"");
                                thirdTreeDto.setLabel(thirdItem.getBaseCategoryName());
                                return thirdTreeDto;
                            }).collect(Collectors.toList()));
                        }
                        return secondTreeDto;
                    }).collect(Collectors.toList()));
                }
                return treeDto;
            }).collect(Collectors.toList());
            JedisUtils.set(dbType + ErpConst.KEY_PREFIX_CATEGORY_LIST + baseCategoryVo.getParentId(), JSON.toJSONString(treeDtos), 60 * 60 * 24*7);
            return  R.success(treeDtos);
        }
    }
    private void loadChildren(BaseCategoryVo vo){
        List<BaseCategoryVo> children = baseCategoryService.getCategoryListByParentId(vo.getBaseCategoryId());
        if(CollectionUtils.isNotEmpty(children)){
            vo.setSecondCategoryList(children);
            children.forEach(item->{
                loadChildren(item);
            });
        }
    }

    @RequestMapping(value = "getFirstCategoryList")
    public ModelAndView getFirstCategoryList(Integer baseCategoryId){
        List<BaseCategory> baseCategoryList=baseCategoryService.getFirstCategory();
        ModelAndView mv=new ModelAndView("goods/basecategory/categoryList");
        mv.addObject("firstCatrgory",baseCategoryList);
        mv.addObject("thirdCatrgoryId",baseCategoryId);
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "getSecondCategoryList")
    public ResultInfo<?> getSecondCategoryList(Integer baseCategoryId){
        ResultInfo<List<BaseCategory>> resultInfo=new ResultInfo<>();
        List<BaseCategory> baseCategoryList=baseCategoryService.getSecondCategory(baseCategoryId);
        resultInfo.setData(baseCategoryList);
        resultInfo.setCode(0);
        return resultInfo;
    }

    /**
     * 查看分类提交页面
     * @param firstCategoryId
     * @param secondCatrgoryId
     * @param thirdCatrgoryId
     * @return
     */
    @FormToken(save = true)
    @RequestMapping(value = "commitCategory")
    public ModelAndView commitCategory(Integer firstCategoryId,Integer secondCatrgoryId,Integer thirdCatrgoryId){
        ModelAndView mv=new ModelAndView("goods/basecategory/categoryConfirm");
        try {
            BaseCategory thirdBaseCategory=baseCategoryService.getBaseCategoryInfoById(thirdCatrgoryId);
            BaseCategory oldSecondBaseCategory=baseCategoryService.getBaseCategoryInfoById(thirdBaseCategory.getParentId());
            BaseCategory oldFirstBaseCategory=baseCategoryService.getBaseCategoryInfoById(oldSecondBaseCategory.getParentId());

            BaseCategory newSecondBaseCategory=baseCategoryService.getBaseCategoryInfoById(secondCatrgoryId);
            BaseCategory newFirstBaseCategory=baseCategoryService.getBaseCategoryInfoById(firstCategoryId);

            /*baseCategoryService.moveCatrgory(thirdCatrgoryId,secondCatrgoryId);*/
            List<MoveCategoryDto> moveCategoryDto=baseCategoryService.getMoveCategoryDtoByCategoryId(thirdCatrgoryId);

            for (MoveCategoryDto m: moveCategoryDto) {
                m.setOldFirstCategoryId(oldFirstBaseCategory.getBaseCategoryId());
                m.setOldFirstCategoryName(oldFirstBaseCategory.getBaseCategoryName());
                m.setOldSecondCategoryId(oldSecondBaseCategory.getBaseCategoryId());
                m.setOldSecondCategoryName(oldSecondBaseCategory.getBaseCategoryName());
                m.setOldThirdCategoryId(thirdBaseCategory.getBaseCategoryId());
                m.setOldThirdCategoryName(thirdBaseCategory.getBaseCategoryName());

                m.setNewFirstCategoryId(newFirstBaseCategory.getBaseCategoryId());
                m.setNewFirstCategoryName(newFirstBaseCategory.getBaseCategoryName());
                m.setNewSecondCategoryId(newSecondBaseCategory.getBaseCategoryId());
                m.setNewSecondCategoryName(newSecondBaseCategory.getBaseCategoryName());
                m.setNewThirdCategoryId(thirdBaseCategory.getBaseCategoryId());
                m.setNewThirdCategoryName(thirdBaseCategory.getBaseCategoryName());
            }

            MoveCategoryDto moveCategory=new MoveCategoryDto();
            moveCategory.setOldFirstCategoryId(oldFirstBaseCategory.getBaseCategoryId());
            moveCategory.setOldFirstCategoryName(oldFirstBaseCategory.getBaseCategoryName());
            moveCategory.setOldSecondCategoryId(oldSecondBaseCategory.getBaseCategoryId());
            moveCategory.setOldSecondCategoryName(oldSecondBaseCategory.getBaseCategoryName());
            moveCategory.setOldThirdCategoryId(thirdBaseCategory.getBaseCategoryId());
            moveCategory.setOldThirdCategoryName(thirdBaseCategory.getBaseCategoryName());

            moveCategory.setNewFirstCategoryId(newFirstBaseCategory.getBaseCategoryId());
            moveCategory.setNewFirstCategoryName(newFirstBaseCategory.getBaseCategoryName());
            moveCategory.setNewSecondCategoryId(newSecondBaseCategory.getBaseCategoryId());
            moveCategory.setNewSecondCategoryName(newSecondBaseCategory.getBaseCategoryName());
            moveCategory.setNewThirdCategoryId(thirdBaseCategory.getBaseCategoryId());
            moveCategory.setNewThirdCategoryName(thirdBaseCategory.getBaseCategoryName());
            mv.addObject("moveCategory",moveCategory);


            mv.addObject("moveCategoryDtoList",moveCategoryDto);
            mv.addObject("secondCategoryId",secondCatrgoryId);
            mv.addObject("thirdCategoryId",thirdCatrgoryId);
        } catch (Exception e) {
            logger.error("分类查询失败"+"一级Id"+firstCategoryId+"二级Id"+secondCatrgoryId+"三级Id"+thirdCatrgoryId,e);
        }
        return mv;
    }

    /**
     * 分类提交确认
     * @param moveCategoryDto
     * @param migrationReason
     * @param request
     * @return
     * @throws IOException
     */
    @FormToken(remove = true)
    @Transactional(rollbackFor = Exception.class)
    @ResponseBody
    @RequestMapping(value = "submitCategory")
    public ResultInfo<?> submitCategory(MoveCategoryDto moveCategoryDto,String migrationReason,HttpServletRequest request){
        ResultInfo<Boolean> resultInfo=new ResultInfo<>();

        if ("1".equals(appoloMigrationReason)){
            if (StringUtils.isEmpty(migrationReason)){
                resultInfo.setData(false);
                resultInfo.setMessage("迁移原因不能为空");
                return resultInfo;
            }
        }
        int resultFlag=0;
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            Long time= DateUtil.sysTimeMillis();
            if (moveCategoryDto.getNewThirdCategoryId()==null || moveCategoryDto.getNewSecondCategoryId()==null){
                resultInfo.setData(false);
                resultInfo.setMessage("迁移出错，请刷新页面重新选择");
                return resultInfo;
            }


            baseCategoryService.moveCatrgory(moveCategoryDto.getNewThirdCategoryId(),moveCategoryDto.getNewSecondCategoryId());

            CategoryMigration categoryMigration=new CategoryMigration();
            categoryMigration.setCategoryId(moveCategoryDto.getNewThirdCategoryId());
            categoryMigration.setCategoryName(moveCategoryDto.getNewThirdCategoryName());
            categoryMigration.setOriginPath(moveCategoryDto.getOldFirstCategoryName()+"/"+moveCategoryDto.getOldSecondCategoryName()+"/"+moveCategoryDto.getNewThirdCategoryName());
            categoryMigration.setTargetPath(moveCategoryDto.getNewFirstCategoryName()+"/"+moveCategoryDto.getNewSecondCategoryName()+"/"+moveCategoryDto.getNewThirdCategoryName());
            categoryMigration.setOriginParentId(""+moveCategoryDto.getOldSecondCategoryId());
            categoryMigration.setTargetParentId(""+moveCategoryDto.getNewSecondCategoryId());
            categoryMigration.setMigrationReason(migrationReason);
            categoryMigration.setAddTime(time);
            categoryMigration.setUpdateTime(time);
            categoryMigration.setUpdater(user.getUserId());
            categoryMigration.setCreator(user.getUserId());
            categoryMigration.setIsDeleted(0);
            baseCategoryService.insertCategoryMigration(categoryMigration);

            CategoryToOpVo categoryToOpVo=new CategoryToOpVo();
            categoryToOpVo.setBaseCategoryId(moveCategoryDto.getNewThirdCategoryId());
            categoryToOpVo.setBaseCategoryName(moveCategoryDto.getNewThirdCategoryName());
            categoryToOpVo.setFirstCategoryId(moveCategoryDto.getNewFirstCategoryId());
            categoryToOpVo.setFirstCategoryName(moveCategoryDto.getNewFirstCategoryName());
            categoryToOpVo.setSecondCategoryId(moveCategoryDto.getNewSecondCategoryId());
            categoryToOpVo.setSecondCategoryName(moveCategoryDto.getNewSecondCategoryName());
            resultFlag=baseCategoryService.migrateCategory2Op(categoryToOpVo);
        } catch (Exception e) {
            logger.error("分类迁移出现错误"+moveCategoryDto.toString(),e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            resultInfo.setData(false);
            resultInfo.setMessage("分类迁移操作失败");
            return resultInfo;
        }

        if (resultFlag==1){
                    resultInfo.setData(true);
                    return resultInfo;
            }else {
                    logger.error("推送op出现异常"+moveCategoryDto.toString());
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    resultInfo.setData(false);
                    resultInfo.setMessage("推送操作失败");
                    return resultInfo;
            }
    }

    /**
     * 处理分类迁移时sku信息推送
     *
     * @param moveCategoryDto
     */
    private void dealPushCategoryTask(MoveCategoryDto moveCategoryDto) {
        logger.info("处理分类迁移时sku信息推送 moveCategoryDto:{}", JSON.toJSONString(moveCategoryDto));
        if (moveCategoryDto == null || moveCategoryDto.getNewThirdCategoryId() == null){
            return;
        }
        List<SpuAddCommand> spuAddCommandList = baseCategoryService.getValidSpuInfoByCategoryId(moveCategoryDto.getNewThirdCategoryId());
        logger.info("分类迁移时该分类下审核通过的SPU信息 spuAddCommandList:{}", JSON.toJSONString(spuAddCommandList));
        if (CollectionUtils.isEmpty(spuAddCommandList)){
            return;
        }
        GlobalThreadPool.submitMessage(new Runnable() {
            @Override
            public void run() {
                String uuid = UUID.randomUUID().toString().replace("-", "");
                log.info("开始执行dealPushTaskWithSpuExamine，uuid：{}",uuid);
                spuAddCommandList.forEach(spuAddCommand -> {
                    dealPushTaskWithSpuExamine(spuAddCommand);
                });
                log.info("结束执行dealPushTaskWithSpuExamine，uuid：{}",uuid);
            }
        });
    }

    @RequestMapping(value = "choiceCategory")
    public ModelAndView choiceCategory(String keyWords,Integer thirdCategoryId){
        ModelAndView mv=new ModelAndView("goods/basecategory/choiceCategory");
        try {
            keyWords = URLDecoder.decode(keyWords, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("字符串转换：",e);
        }

        List<FirstAndSecondCategoryInfo> firstAndSecondCategoryInfoList = baseCategoryService.getFirstAndSecondCategoryListByKeyWords(keyWords);
        mv.addObject("keyWords",keyWords);
        mv.addObject("thirdCategoryId",thirdCategoryId);
        mv.addObject("firstAndSecondCategoryInfoList",firstAndSecondCategoryInfoList);
        return mv;
    }


    @RequestMapping(value = "categoryMigretionIndex")
    public ModelAndView categoryMigretionIndex(HttpServletRequest request,CategoryMigrationVo categoryMigrationVo,
                                               @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                               @RequestParam(required = false) Integer pageSize){
        ModelAndView mv=new ModelAndView("goods/basecategory/category_migration_list");
        Page page = getPageTag(request,pageNo,pageSize);
        List<CategoryMigration> list=baseCategoryService.getCtegoryMigretionlistPage(categoryMigrationVo,page);
        mv.addObject("categoryMigrationVo",categoryMigrationVo);
        mv.addObject("categoryMigrationList",list);
        mv.addObject("page",page);
        return mv;
    }

    @RequestMapping(value = "categoryMigrationExamine")
    public ResultInfo<?> categoryMigrationExamine(String categoryMigrationIdArr){
       // System.out.println(categoryMigrationIdArr);
        return new ResultInfo<>();
    }


    @RequestMapping (value = "getAllLevelCategoryByIdList")
    @ResponseBody
    public RestfulResult<List<CategoryToOpVo>> getAllLevelCategoryById(@RequestBody CategoryQueryDTO dto){
        if(dto==null||CollectionUtils.isEmpty(dto.getCategoryIds())){
            logger.info("根据三级分类获取一二三级分类信息，参数为空");
            return RestfulResult.fail("fail","根据三级分类获取一二三级分类信息，参数为空");
        }
        logger.info("根据三级分类获取一二三级分类信息 {}",dto.getCategoryIds());
        return RestfulResult.success(baseCategoryService.getAllLevelCategoryByIdList(dto.getCategoryIds()));
    }
}
