package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 合同状态校验
 */
@Service
@Slf4j
public class ContractStatusCheckHandler extends AbstractCheckHandler {

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    /**
     * 限制时间-2023-01-01 00:00:00
     */
    private static final Long LIMIT_TIME = 1672502400000L;
    /**
     * 线上订单
     */
    public static final List<Integer> ONLINE_ORDER = Collections.unmodifiableList(Lists.newArrayList(1, 5, 7));
    private static final BigDecimal LIMIT_AMOUNT_ONE = BigDecimal.valueOf(5000);
    private static final BigDecimal LIMIT_AMOUNT_TWO = BigDecimal.valueOf(50000);

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("合同状态校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderId(invoiceCheckRequestDto.getRelatedId());
        if (Objects.isNull(saleOrder)) {
            throw new ServiceException("申请数量校验-销售,订单不存在");
        }
        BigDecimal totalAmount = saleOrder.getTotalAmount();
        Long addTime = saleOrder.getAddTime();
        Integer haveAccountPeriod = saleOrder.getHaveAccountPeriod();
        Integer orderType = saleOrder.getOrderType();
        // 订单创建日期小于2023-01-01 00:00:00
        // 或者账期类型为是
        // 或者订单原金额小于5000
        // 或者订单金额大于5000小于50000且订单类型为线上订单
        // 则合同状态校验不限制
        if (addTime < LIMIT_TIME || ErpConstant.ONE.equals(haveAccountPeriod) || LIMIT_AMOUNT_ONE.compareTo(totalAmount) > 0 ||
                (LIMIT_AMOUNT_ONE.compareTo(totalAmount) <= 0 && LIMIT_AMOUNT_TWO.compareTo(totalAmount) > 0 && ONLINE_ORDER.contains(orderType))) {
            log.info("合同状态校验-销售,不限制,totalAmount:{},addTime:{},haveAccountPeriod:{},orderType:{}", totalAmount, addTime, haveAccountPeriod, orderType);
            return;
        }
        // 其他情况则限制合同审核状态为审核通过
        Integer contractVerifyStatus = saleOrderApiService.getContractVerifyStatusBySaleOrderId(invoiceCheckRequestDto.getRelatedId());
        log.info("合同状态校验-销售,限制,contractVerifyStatus:{},addTime:{},haveAccountPeriod:{},totalAmount:{},orderType:{}", contractVerifyStatus, addTime, haveAccountPeriod, totalAmount, orderType);
        if (!ErpConstant.ONE.equals(contractVerifyStatus)) {
            String contractVerifyStatusStr = getContractVerifyStatusStr(contractVerifyStatus);
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), contractVerifyStatusStr);
            buildResult(invoiceCheckResultDto, promptText);
        }
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
    }

    private static String getContractVerifyStatusStr(Integer contractVerifyStatus) {
        if (Objects.isNull(contractVerifyStatus)) {
            return "未上传";
        }
        String contractVerifyStatusStr;
        switch (contractVerifyStatus) {
            case 0:
                contractVerifyStatusStr = "审核中";
                break;
            case 1:
                contractVerifyStatusStr = "审核通过";
                break;
            case 2:
                contractVerifyStatusStr = "审核不通过";
                break;
            case 4:
                contractVerifyStatusStr = "待审核";
                break;
            default:
                contractVerifyStatusStr = "未上传";
                break;
        }
        return contractVerifyStatusStr;
    }
}
