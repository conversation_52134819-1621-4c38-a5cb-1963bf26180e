package com.vedeng.finance.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.component.AfterSalesTypeEnum;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.kingdee.service.KingDeePayBillApiService;
import com.vedeng.finance.constant.PayApplyTypeEnum;
import com.vedeng.finance.dto.PayBillCancelDto;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.service.TraderCustomerService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


/**
 * 金蝶回调接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/kingdee")
public class KingdeeController {

    public static Logger logger = LoggerFactory.getLogger(KingdeeController.class);

    private static final String END_EVENT = "endEvent";

    private static final String FINANCIAL_REVIEW = "财务审核";

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    @Qualifier("payApplyService")
    private PayApplyService payApplyService;

    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    private AfterSalesService afterSalesOrderService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private KingDeePayBillApiService kingDeePayBillApiService;

    /**
     * 金蝶取消付款回调
     *
     * @param payBillCancelDto
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/paybill/cancel", method = RequestMethod.POST)
    public R<?> payApplyCancel(@RequestBody PayBillCancelDto payBillCancelDto) {
        logger.info("payApplyCancel payBillCancelDto:{}", JSON.toJSONString(payBillCancelDto));
        if (payBillCancelDto == null || payBillCancelDto.getErpPayBillId() == null) {
            return R.error("参数异常");
        }
        /**
         * 作废金蝶推送表付款单信息
         */
        kingDeePayBillApiService.deleteKingDeeInfoByFBillNo(payBillCancelDto.getErpPayBillId().toString());
        PayApply payApplyInfo = payApplyService.getPayApplyInfo(payBillCancelDto.getErpPayBillId());
        if (payApplyInfo == null) {
            logger.warn("金蝶回调付款单取消接口付款单信息异常 id:{}", payBillCancelDto.getErpPayBillId());
            return R.error("金蝶回调付款单取消接口付款单信息异常");
        }

        if (payApplyInfo.getValidStatus() > 0) {
            logger.warn("该付款单已经审核，请确认 payApplyInfo:{}", JSON.toJSONString(payApplyInfo));
            return R.error("该付款单已经审核，请确认");
        }

        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", false);
        variables.put("db", ErpConst.TWO);

        // 如果审核没结束添加审核对应主表的审核状态
        Integer status = ErpConst.TWO;

        // 获取任务的Service，设置和获取流程变量
        TaskService taskService = processEngine.getTaskService();
        Task taskInfoPay = taskService.createTaskQuery()
                .processInstanceBusinessKey("paymentVerify_" + payApplyInfo.getPayApplyId())
                .singleResult();

        if (taskInfoPay == null) {
            logger.warn("付款单审核流异常 id:{}", payApplyInfo.getPayApplyId());
            return R.error("付款单审核流异常");
        }

        String taskId = taskInfoPay.getId();
        String id = (String) taskService.getVariable(taskId, "id");
        Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
        String key = (String) taskService.getVariable(taskId, "key");
        String tableName = (String) taskService.getVariable(taskId, "tableName");


        switch (PayApplyTypeEnum.getInstance(payApplyInfo.getPayType())) {
            case BUY_ORDER: {
                //采购单ID判断问题
                Integer buyOrderId = payApplyInfo.getRelatedId();
                if (taskService.getVariable(taskId, "buyorderId") != null) {
                    buyOrderId = (Integer) taskService.getVariable(taskId, "buyorderId");
                    // 采购单付款申请不通过解锁
                    actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyOrderId, "LOCKED_STATUS", 0, 2);
                }
                if (tableName != null && id != null && idValue != null && key != null) {
                    actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);
                if (buyOrderId != null && buyOrderId > 0) {
                    riskCheckService.resetBuyorderRiks(buyOrderId);
                }
                break;
            }
            case BUY_EXPENSE_ORDER: {
                //采购费用单付款申请不通过
                Integer relatedId = (Integer) taskService.getVariable(taskId, "buyorderExpenseId");
                actionProcdefService.updateInfo("T_BUYORDER_EXPENSE", "BUYORDER_EXPENSE_ID", relatedId, "LOCKED_STATUS", 0, 2);
                if (tableName != null && id != null && idValue != null && key != null) {
                    actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);
                break;
            }
            case AFTER_SALES: {
                // 使用任务id,获取任务对象，获取流程实例id
                Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
                String taskName = task.getName();

                if (tableName != null && id != null && idValue != null && key != null) {
                    actionProcdefService.updateInfo(tableName, id, idValue, key, 2, 2);
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);
                if (!FINANCIAL_REVIEW.equals(taskName)) {
                    break;
                }

                AfterSales afterSales = afterSalesOrderService.getAfterSalesById(payApplyInfo.getRelatedId());
                if (afterSales == null) {
                    logger.warn("金蝶付款申请驳回检索售后单信息异常 payApplyInfo:{}", JSON.toJSONString(payApplyInfo));
                    return R.error("金蝶付款申请驳回检索售后单信息异常");
                }

                switch (AfterSalesTypeEnum.getEnumByCode(afterSales.getType())) {
                    case RETURNS_SALEORDER:
                    case REFUND_MONEY_SALEORDER: {
                        Optional.ofNullable(traderCustomerService.getTraderByPayApply(idValue))
                                .ifPresent(traderCustomer -> {
                                    traderCustomerService.updateTraderAmount(traderCustomer.getTraderId(), payApplyInfo.getAmount());
                                    logger.info("售后订单：{}在金蝶财务审核环节，审核不通过，余额增加金额：{}", payApplyInfo.getRelatedId(), payApplyInfo.getAmount());
                                });
                    }
                    default: {
                        break;
                    }
                }
                break;
            }
            default: {
                logger.warn("金蝶回传付款申请类型错误 payApplyInfo:{}", JSON.toJSONString(payApplyInfo));
                return R.error("金蝶回传付款申请类型错误");
            }
        }

        ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, taskId, "", ErpConst.NJ_ADMIN_NAME, variables);
        // 如果未结束添加审核对应主表的审核状态
        if (!END_EVENT.equals(complementStatus.getData())) {
            verifiesRecordService.saveVerifiesInfo(taskId, status);
        }

        return R.success();
    }
}
