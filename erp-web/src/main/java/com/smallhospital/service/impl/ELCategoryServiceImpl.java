package com.smallhospital.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.smallhospital.constant.UrlConstant;
import com.smallhospital.dao.ElSkuMapper;
import com.smallhospital.dto.ELCategoryDto;
import com.smallhospital.dto.ELSkuBasicInfo;
import com.smallhospital.model.ELCategory;
import com.smallhospital.model.vo.ELCategoryVO;
import com.smallhospital.model.vo.ELContractVO;
import com.smallhospital.service.ELCategoryService;
import com.smallhospital.service.impl.remote.SynCategoryInfoService;
import com.smallhospital.service.impl.remote.SynTradeInfoService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 同步分类信息
 */
@Service
public class ELCategoryServiceImpl implements ELCategoryService {

    Logger logger= LoggerFactory.getLogger(ELCategoryServiceImpl.class);

    @Value("${api_url}")
    protected String apiUrl;

    @Autowired
    private SynCategoryInfoService categoryInfoService;

    @Autowired
    private ElSkuMapper skuMapper;

    @Override
    public void synCategoryInfo() {
        categoryInfoService.syncData(null);
    }

    @Override
    public List<ELCategoryVO> intentionCategoryIds(ELCategoryDto categoryDto){
        logger.info("小医院获取OP分类入参:" + JSON.toJSONString(categoryDto));
        TypeReference<RestfulResult<List<ELCategory>>> reference = new TypeReference<RestfulResult<List<ELCategory>>>(){};
        RestfulResult<List<ELCategory>> restfulResult = HttpRestClientUtil.restPost(apiUrl + UrlConstant.GET_CATEGORY, reference,null, categoryDto);
        logger.info("小医院获取OP分类出参:" + JSON.toJSONString(restfulResult));
        if (null != restfulResult && restfulResult.isSuccess() && CollectionUtils.isNotEmpty(restfulResult.getData())){
            List<ELCategoryVO> categoryInfos = new ArrayList<>();
            restfulResult.getData().stream().forEach(data ->{
                ELCategoryVO elCategoryVO = new ELCategoryVO();
                elCategoryVO.setCategoryCode(data.getvCategoryId());
                elCategoryVO.setCategoryName(data.getCategoryName());
                categoryInfos.add(elCategoryVO);
            });
            return categoryInfos;
        }
        return new ArrayList<>();
    }
}
