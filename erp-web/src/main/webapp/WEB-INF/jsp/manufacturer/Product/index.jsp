<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>


<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <title>生产企业管理</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/index.css?rnd=${resourceVersionKey}">
</head>

<body>
    <div class="erp-wrap">
        <div class="erp-title">
            <div class="erp-title-txt">生产企业管理</div>
        </div>

        <div class="erp-top-option">
            <div class="option-btn-wrap">
                <a class="btn btn-blue btn-small" tabTitle='{"num":"addProduct","link":"./goods/manufacturer/addProduct.do","title":"新增企业", "random": "1"}'>新增生产企业</a>
            </div>
        </div>
        <div class="erp-block base-form search-wrap J-search-wrap">
            <div class="search-list">
                <div class="search-item item-search-select">
                    <div class="item-label" data-place="请输入生产企业">
                        关键词:
                    </div>
                    <div class="item-fields">
                        <div class="search-input-wrap item-input">
                            <input type="text" name="manufacturerName" autocomplete="off" maxlength="128" class="input-text J-search-word" value="${manufacturer.manufacturerName }">
                            <ul class="search-history-wrap J-search-history" style="display:none;"></ul>
                        </div>
                    </div>
                </div>


                <div class="search-item">
                    <div class="item-label">是否上传贝登公章：</div>
                    <div class="item-fields">
                        <select name="isUpload" class="J-select">
                            <option value="">全部</option>
                            <!--TODO 36 需求调整 2.2.6 -->
                            <option value="2"
                                    <c:if test="${manufacturer.isUpload eq 2}">selected</c:if> >全部上传
                            </option>
                            <option value="1"
                                    <c:if test="${manufacturer.isUpload eq 1}">selected</c:if> >部分上传
                            </option>
                            <option value="0" <c:if test="${manufacturer.isUpload eq 0}">selected</c:if>>未上传</option>
                        </select>
                    </div>
                </div>

                <div class="search-item">
                    <div class="item-label">归属产品经理/助理:</div>
                    <div class="item-fields">
                        <select name="assignmentManagerId" class="J-select">
                            <option value="-1">全部</option>
                            <c:forEach var="user" items="${userList}" >
                                <option value="${user.userId}"<c:if test="${(user.userId == userId || user.userId==manufacturer.assignmentManagerId)&& isWhole ne -1}">selected</c:if>
                                >${user.username}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>

                <div class="search-item">
                    <div class="item-label">审核状态:</div>
                    <div class="item-fields">
                        <select name="status" class="J-select">
                            <option value="-1">全部</option>
                            <option value="5" <c:if test="${manufacturer.status eq 5}">selected</c:if>>待提交审核</option>
                            <option value="1" <c:if test="${manufacturer.status eq 1}">selected</c:if>>审核中</option>
                            <option value="2" <c:if test="${manufacturer.status eq 2}">selected</c:if>>审核不通过</option>
                            <option value="3" <c:if test="${manufacturer.status eq 3}">selected</c:if>>审核通过</option>
                        </select>
                    </div>
                </div>

            </div>
            <div class="search-btns">
                <div class="btn btn-small btn-blue-bd J-search">搜索</div>
                <div class="btn btn-small J-reset">重置</div>
            </div>
        </div>
        <div class="erp-block erp-block-list">
            <div class="option-wrap J-fix-wrap">
                <div class="option-fix-wrap cf J-fix-cnt">

                </div>
            </div>
            <table class="table table-base table-hover base-form J-table-wrap">
                <colgroup>
                    <col width="14.5%">
                    <col width="14.5%">
                    <col width="14.5%">
                    <col width="14.5%">
                    <c:if test="${tag ne 2}">
                        <col width="10%">
                    </c:if>
                </colgroup>
                <tbody>
                    <tr>

                        <th>企业名称</th>
                        <th>归属产品经理/助理</th>
                        <th>资质是否上传贝登公章</th>
                        <th>审核状态</th>
                        <c:if test="${tag ne 2}">
                            <th>操作</th>
                        </c:if>
                    </tr>
                    <c:if test="${empty manufacturerList}">
                        <tr>
                            <td class="no-data" colspan='
                            <c:if test="${tag eq 1 }">
                                9
                            </c:if>
                            <c:if test="${tag eq 2 }">
                                6
                            </c:if>
                            <c:if test="${tag eq 3 || tag eq 4 }">
                                8
                            </c:if>
                            '>
                                <div><i class="vd-icon icon-caution1"></i></div>
                                没有匹配的数据
                            </td>
                        </tr>
                    </c:if>

                    <c:if test="${not empty manufacturerList}">
                        <c:forEach var="manufacturer" items="${manufacturerList }">
                            <tr>

                                <td>
                                    <a href="javascript:void(0);" tabTitle='{"num":"viewProduct${manufacturer.manufacturerId}","link":"./goods/manufacturer/getManufacturerDetail.do?manufacturerId=${manufacturer.manufacturerId }","title":"查看生产企业"}' class="">${manufacturer.manufacturerName }</a>
                                </td>
                                <td>${manufacturer.userName }</td>
                                <td>
                                    <div class="stand-wrap">
                                        <c:if test="${manufacturer.isUpload eq 2}">
                                            全部上传
                                        </c:if>
                                        <c:if test="${manufacturer.isUpload eq 1}">
                                            部分上传
                                        </c:if>
                                        <c:if test="${manufacturer.isUpload eq 0}">
                                            未上传
                                        </c:if>

                                    </div>
                                </td>

                                <td>
                                    <div class="stand-wrap">
                                        <c:if test="${manufacturer.status eq 5}">
                                            待提交审核
                                        </c:if>
                                        <c:if test="${manufacturer.status eq 1}">
                                            审核中
                                        </c:if>
                                        <c:if test="${manufacturer.status eq 2}">
                                            审核不通过
                                        </c:if>
                                        <c:if test="${manufacturer.status eq 3}">
                                            审核通过
                                        </c:if>
                                    </div>
                                </td>



                                <c:if test="${tag ne 2}">
                                    <td>
                                            <div class="option-select-wrap J-option-select-wrap" data-id="${manufacturer.manufacturerId }">

                                                <div class="option-select-btn" tabTitle='{"link":"./goods/manufacturer/addProduct.do?manufacturerId=${manufacturer.manufacturerId }","title":"编辑生产企业信息"}'>编辑</div>
                                                <%--<div class="option-select-btn option-select-item J-one-upload" data-id="${manufacturer.manufacturerId}">上传</div>--%>
                                                <div class="option-select-btn option-select-item J-one-del">删除</div>
                                            </div>
                                    </td>
                                </c:if>
                            </tr>
                        </c:forEach>
                    </c:if>
                </tbody>
            </table>
            <c:if test="${not empty manufacturerList}">
                    <tags:pageNew page="${page}" />
            </c:if>
        </div>

    </div>
    <script type="text/tmpl" class="J-del-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i>确认删除该生产企业？
            </div>
        </div>
    </script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/manufacturer/product/index.js?rnd=${resourceVersionKey}"></script>
</body>

</html>