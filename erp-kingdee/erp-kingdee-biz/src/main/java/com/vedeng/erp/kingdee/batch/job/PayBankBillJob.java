package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.writer.BankBillWriter;
import com.vedeng.erp.kingdee.batch.writer.CommonFileDataWriter;
import com.vedeng.erp.kingdee.batch.writer.PayRefundBillWriter;
import com.vedeng.erp.kingdee.batch.writer.ReceiptRefundBillWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 银行流水推送金蝶job
 * <AUTHOR>
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class PayBankBillJob extends BaseJob {


    @Autowired
    private BankBillWriter bankBillWriter;

    @Autowired
    private BankBillProcessor bankBillProcessor;

    @Autowired
    private IgnoreBankReceiveBillProcessor ignoreBankBillProcessor;

    @Autowired
    private BankBillFileProcessor bankBillFileProcessor;

    @Autowired
    private ReceiveBillFillProcessor receiveBillFillProcessor;

    @Autowired
    private ReceiveRefundBillFillProcessor receiveRefundBillFillProcessor;

    @Autowired
    private PayBillFillProcessor payBillFillProcessor;

    @Autowired
    private PayRefundBillFillProcessor payRefundBillFillProcessor;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;

    @Autowired
    private IgnoredPayRefundBillProcessor ignoredPayRefundBillProcessor;

    @Autowired
    private PayRefundBillWriter payRefundBillWriter;

    @Autowired
    private SettledPayRefundBillProcessor settledPayRefundBillProcessor;

    @Autowired
    private ReceiptBankBillNewProcessor receiptBankBillNewProcessor;

    @Autowired
    private ReceiptRefundBillWriter receiptRefundBillWriter;

    @Autowired
    private SettledReceiptRefundBillProcessor settledReceiptRefundBillProcessor;
    @Autowired
    private IgnoredReceiptRefundBillProcessor ignoredReceiptRefundBillProcessor;

    /**
     * 银行流水推送金蝶
     * @return
     */
    public Job pullBankBIllJob(){
        return jobBuilderFactory.get("pullBankBIllJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(receiptBankBillNew())
                .next(ignoreBankBill())
                .next(settledReceivmentRefundForm())
                .next(ignoredReceivementRefundForm())
                .next(settledPaymentRefundForm())
                .next(ignoredPaymentRefundForm())
                .next(receiveBillFill())
                .next(receiveRefundBillFill())
                .next(payBillFill())
                .next(payRefundBillFill())
                .build();
    }

    private Step payRefundBillFill() {
        return stepBuilderFactory.get("付款退款单回单推送")
                .<BatchBankBillDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(payRefundBillFillReader(null,null))
                .processor(payRefundBillFillProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step payBillFill() {
        return stepBuilderFactory.get("付款单回单推送")
                .<BatchBankBillDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(payBillFillReader(null,null))
                .processor(payBillFillProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step receiveRefundBillFill() {
        return stepBuilderFactory.get("收款退款单单回单推送")
                .<BatchBankBillDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(receiveRefundBillFillReader(null,null))
                .processor(receiveRefundBillFillProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step receiveBillFill() {
        return stepBuilderFactory.get("收款单回单推送")
                .<BatchBankBillDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(receiveBillFillReader(null,null))
                .processor(receiveBillFillProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    public Step receiptBankBillNew(){
        return stepBuilderFactory.get("已结算收款流水(银行支付宝微信)")
                .<BatchBankBillDto, KingDeeReceiveBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(receiptBankBillNewReader(null,null))
                .processor(receiptBankBillNewProcessor)
                .writer(bankBillWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> receiptBankBillNewReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                              @Value("#{jobParameters['endTime']}") String endTime) {
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .capitalBillBeginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .capitalBillEndTime(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryNeedPushReceiptBankBillNew", batchBankBillDto);
    }

    /**
     * 银行流水推送金蝶step
     * @return
     */
    public Step bankBill(){
        return stepBuilderFactory.get("银行流水推送")
                .<BatchBankBillDto, KingDeeReceiveBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(bankBillReader(null,null))
                .processor(bankBillProcessor)
                .writer(bankBillWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 忽略项流水推送Step
     * 已忽略-收款单
     * @return
     */
    public Step ignoreBankBill(){
        return stepBuilderFactory.get("银行忽略项流水推送")
                .<BatchBankBillDto, KingDeeReceiveBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(ignoreBankBillReader(null,null))
                .processor(ignoreBankBillProcessor)
                .writer(bankBillWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 查询银行流水推送金蝶
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> bankBillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                    @Value("#{jobParameters['endTime']}") String endTime){
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .flag1(ErpConstant.ONE)
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? null: DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryNeedPullBankBill", batchBankBillDto);
    }

    /**
     * 查询收款单附件推送金蝶
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> receiveBillFillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                    @Value("#{jobParameters['endTime']}") String endTime){
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .flag1(ErpConstant.ONE)
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? null: DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryReceiveBillFill", batchBankBillDto);
    }
    /**
     * 查询收款退款单附件推送金蝶
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> receiveRefundBillFillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                           @Value("#{jobParameters['endTime']}") String endTime){
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .flag1(ErpConstant.ZERO)
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? null: DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryReceiveRefundBillFill", batchBankBillDto);
    }
    /**
     * 查询付款单附件推送金蝶
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> payBillFillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                 @Value("#{jobParameters['endTime']}") String endTime){
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .flag1(ErpConstant.ZERO)
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? null: DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryPayBillFill", batchBankBillDto);
    }
    /**
     * 查询付款退款单附件推送金蝶
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> payRefundBillFillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                       @Value("#{jobParameters['endTime']}") String endTime){
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .flag1(ErpConstant.ONE)
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? null: DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryPayRefundBillFill", batchBankBillDto);
    }

    /**
     * (收款单)查询银行流水推送金蝶
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> ignoreBankBillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                    @Value("#{jobParameters['endTime']}") String endTime){
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                // --0：支出，1：收入
                .flag1(ErpConstant.ONE)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()): DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryNeedPullIgnoreBankBill", batchBankBillDto);
    }

    /**
     * 银行回单推送金蝶step （含忽略）
     * @return
     */
    public Step bankFile() {
        return stepBuilderFactory.get("银行流水回单推送")
                .<BatchBankBillDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(bankFillReader(null,null))
                .processor(bankBillFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> bankFillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                           @Value("#{jobParameters['endTime']}") String endTime){
        BatchBankBillDto batchBankBillDto = BatchBankBillDto
                .builder()
                .flag1(ErpConstant.ONE)
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? null: DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryNeedPullBankBill", batchBankBillDto);
    }

    /**
     * 已结算 付款退款单
     *
     * @return
     */
    public Step settledPaymentRefundForm() {
        return stepBuilderFactory.get("已结算付款退款单推送")
                .<BatchBankBillDto, KingDeePayRefundBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(settledPayRefundBillReader(null, null))
                .processor(settledPayRefundBillProcessor)
                .writer(payRefundBillWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> settledPayRefundBillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                @Value("#{jobParameters['endTime']}") String endTime) {
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "querySettledPayRefundBill", BatchBankBillDto.builder().build());
    }

    /**
     * 已忽略 付款退款单
     *
     * @return
     */
    public Step ignoredPaymentRefundForm() {
        return stepBuilderFactory.get("已忽略付款退款单推送")
                .<BatchBankBillDto, KingDeePayRefundBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(ignoredPayRefundBillReader(null, null))
                .processor(ignoredPayRefundBillProcessor)
                .writer(payRefundBillWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> ignoredPayRefundBillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                @Value("#{jobParameters['endTime']}") String endTime) {
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryIgnoredPayRefundBill", BatchBankBillDto.builder().build());
    }






    /**
     * 已结算 收款退款单
     *
     * @return
     */
    public Step settledReceivmentRefundForm() {
        return stepBuilderFactory.get("已结算收款退款单推送")
                .<BatchBankBillDto, KingDeeReceiveRefundBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(settledReceiveRefundBillReader(null, null))
                .processor(settledReceiptRefundBillProcessor)
                .writer(receiptRefundBillWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> settledReceiveRefundBillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                @Value("#{jobParameters['endTime']}") String endTime) {
        CommonMybatisItemReader<BatchBankBillDto> querySettledReceiveRefundBill = new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "querySettledReceiveRefundBill", BatchBankBillDto.builder().build());
        return querySettledReceiveRefundBill;
    }

    /**
     * 已忽略 收款退款单
     *
     * @return
     */
    public Step ignoredReceivementRefundForm() {
        return stepBuilderFactory.get("已忽略收款退款单推送")
                .<BatchBankBillDto, KingDeeReceiveRefundBillDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(ignoredReceiveRefundBillReader(null, null))
                .processor(ignoredReceiptRefundBillProcessor)
                .writer(receiptRefundBillWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBankBillDto> ignoredReceiveRefundBillReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                @Value("#{jobParameters['endTime']}") String endTime) {
        CommonMybatisItemReader<BatchBankBillDto> queryIgnoredReceiveRefundBill = new CommonMybatisItemReader<>(sqlSessionFactory, BatchBankBillDto.class.getSimpleName(), "queryIgnoredReceiveRefundBill", BatchBankBillDto.builder().build());
        return queryIgnoredReceiveRefundBill;
    }

}
