<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="新增外借单" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript">

    function deleteRow(row) {
        $(row).parent().parent().remove();
    }

    function addSubmit(t) {

        checkLogin();

        if ($("#borrowReason").val() == '') {
            layer.alert("借用原因不能为空");
            return;
        }

        if ($("#borrowTraderName").val() == '') {
            layer.alert("借用企业不能为空");
            return;
        }

        if ($("#contact").val() == '') {
            layer.alert("收货联系人不能为空");
            return;
        }

        if ($("#address").val() == '') {
            layer.alert("收货地址不能为空");
            return;
        }

        var addressLength = $("#address").val().split("/")[0].split(" ").length;
        if (addressLength != 3) {
            layer.alert("收货地址请补充完整省市区");
            return;
        }

        if ($("#logisticCommnet").val().length > 200) {
            layer.alert("物流备注不能超过200个字符");
            return false;
        }

        if ($("#logisticCommnet").val() == '') {
            layer.alert("请填写物流备注");
            return false;
        }

        var trs = $('#thisTimeUpdateTbody tr');

        if (trs.length == 0) {
            layer.alert("产品信息不能为空");
            return;
        }

        var traderId = $("#borrowTraderId").val()
        var traderType = $("#traderType").val()
        disableSubmit(t);
        if (!checkTraderVerify(traderId, traderType)) {
            enableSubmit(t);
            return false
        }
        // if(!checkTraderVerify(traderId, traderType)){
        //     if (traderType == 1){
        //         layer.alert("客户资质尚未审核通过，请完成客户资质审核！");
        //     }else {
        //         layer.alert("供应商尚未审核通过，请完成供应商审核！");
        //     }
        //     return false
        // }

        var skuNos = [];
        var re = /^[0-9]+$/;
        for (var i = 0; i < trs.length; i++) {

            var skuNo = $(trs[i]).find('td').eq(0).find('input').eq(0).val();
            var outputNum = $(trs[i]).find('td').eq(4).find('input').eq(0).val();
            var returnDate = $(trs[i]).find('td').eq(5).find('input').eq(0).val();

            if (skuNos.includes(skuNo)) {
                enableSubmit(t);
                layer.alert("sku:'" + skuNo + "'已添加，不可以添加重复数据!");
                return;
            }

            if (outputNum == '') {
                enableSubmit(t);
                layer.alert("sku:'" + skuNo + "'出库数量不能为空");
                return;
            }

            if (!re.test(outputNum)) {
                enableSubmit(t);
                layer.alert("sku:" + skuNo + "出库数量:" + outputNum + "必须为正整数");
                return false;
            }

            if (returnDate == '') {
                enableSubmit(t);
                layer.alert("sku:'" + skuNo + "'归还日期不能为空");
                return;
            }

            skuNos.push(skuNo);
        }

        $.ajax({
            async: true,
            url: page_url + '/wms/commodityLendOut/canSubmitLendOut.do',
            type: "POST",
            dataType: "json",
            success: function (data) {
                if (data.code == 0) {
                    $("#addForm").submit();
                } else {
                    enableSubmit(t);
                    layer.alert(data.message)
                }

            },
            error: function (data) {
                enableSubmit(t);
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });


    }

    function disableSubmit(t){
        $(t).prop('disabled', true).text("提交中").css('background-color', 'gray');
    }
    function enableSubmit(t){
        $(t).prop('disabled', false).text("提交并审核").css('background-color', '');
    }

    function checkTraderVerify(traderId, traderType) {
        var flag = false;
        $.ajax({
            // 同步
            async: false,
            url: page_url + '/trader/check/traderVerifies.do',
            data: {"traderId": traderId, "traderType": traderType},
            type: "POST",
            dataType: "json",
            success: function (data) {
                if (data.data == false) {
                    if (traderType == 1) {
                        layer.alert("客户资质尚未审核通过，请完成客户资质审核！");
                    } else {
                        layer.alert("供应商尚未审核通过，请完成供应商审核！");
                    }
                }
                flag = data.data
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
        return flag
    }

    function searchTerminal() {
        checkLogin();
        delWarnTips("errorTxtMsg");

        var searchTraderName = $("#borrowTraderName").val();

        if ($('#traderType').val() == 1) {
            searchURL = '/trader/customer/searchCustomerList.do';
        } else {
            searchURL = '/order/buyorder/getSupplierByName.do';
        }

        $("#traderInfo").find("#terminalDiv").attr('layerParams', '{"width":"800px","height":"300px","title":"客户信息","link":"' + page_url + searchURL + '?traderType=' + $("#traderType").val() + '&callbackFuntion=setTraderInfo&lendOut=1&searchTraderName=' + encodeURI(searchTraderName) + '&indexId=1"}');
        $("#traderInfo").find("#terminalDiv").click();
    }

    function setGoodData(sku, goodsName, brandName, model) {
        var tbody = $("#thisTimeUpdateTbody");
        var tr = $("<tr>\n" +
            "     <td>\n" +
            "     " + sku + "\n" +
            "       <input type=\"hidden\" name=\"skuNo\" value=\"" + sku + "\">\n" +
            "     </td>\n" +
            "     <td>" + goodsName + "</td>\n" +
            "     <td>" + brandName + "</td>\n" +
            "     <td>" + model + "</td>\n" +
            "     <td><input style=\"width:100%;\" type=\"text\" class=\"input-middle\" name=\"outputNum\"></td>\n" +
            "     <td>\n" +
            "       <input style=\"width:100%;\" class=\"Wdate f_left input-smaller96\" type=\"text\" placeholder=\"请选择日期\"\n" +
            "               onClick=\"WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'${today}'})\"\n" +
            "               name=\"expectReturnedTime\">\n" +
            "     </td>\n" +
            "     <td><a href=\"#\" onclick=\"deleteRow(this)\">删除</a></td>\n" +
            "     </tr>");
        tbody.append(tr);
    }


    function setTraderInfo(traderId, traderName, traderType) {

        debugger;

        //根据客户ID获取联系人列表&地址列表
        $.ajax({
            async: true,
            url: page_url + '/order/saleorder/getCustomerContactAndAddress.do',
            data: {"traderId": traderId, "traderType": traderType},
            type: "POST",
            dataType: "json",
            success: function (data) {

                $("#borrowTraderId").val(traderId);
                $("#borrowTraderName").val(traderName);
                $("#traderType").val(traderType);

                var contactStr = '<option value="">请选择</option>';
                for (var i = 0; i < data.param.contactList.length; i++) {
                    var isSelected = data.param.contactList[i].isDefault == 1 ? 'selected = "selected"' : '';
                    contactStr += '<option value="' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '" ' + isSelected + '>' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '</option>';
                }
                $("#contact").html(contactStr);

                var addressStr = '<option value="">请选择</option>';
                for (var i = 0; i < data.param.addressList.length; i++) {
                    var isSelected = data.param.addressList[i].traderAddress.isDefault == 1 ? 'selected = "selected"' : '';
                    addressStr += '<option value="' + data.param.addressList[i].area + '/' + data.param.addressList[i].traderAddress.address + '" ' + isSelected + '>' + data.param.addressList[i].area + '/' + data.param.addressList[i].traderAddress.address + '</option>';
                }
                $("#address").html(addressStr);
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

</script>

<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/wms/commodityLendOut/addLendOut.do">

            <ul class="payplan">

                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>借用原因:</label>
                    </div>
                    <div class="f_left">
                        <input type="text" placeholder="请填写借用原因" class="input-middle" name="borrowReason" id="borrowReason" value=""/>
                    </div>
                </li>

                <div>
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">借用企业信息</div>
                    </div>
                    &nbsp;
                    <li id="traderInfo">
                        <div class="infor_name">
                            <span class="font-red">*</span><label>借用企业:</label>
                        </div>
                        <div class="f_left">

                            <input type="text" placeholder="请填写借用企业" class="input-middle" id="borrowTraderName" name="borrowTraderName" value=""/>
                            <input type="hidden" id="borrowTraderId" name="borrowTraderId"/>
                            <input type="hidden" id="traderType" name="traderType" value="1"/>

                            <label class="bt-bg-style bg-light-blue bt-small" onclick="searchTerminal();" id="errorMes">搜索</label>

                            <!-- 弹框 -->
                            <span style="display: none;">
                                <div class="title-click nobor  pop-new-data" id="terminalDiv"></div>
                            </span>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>收货联系人:</label>
                        </div>
                        <div class="f_left">
                            <select class="input-xx" id="contact" name="traderContact">
                                <option value="">请选择</option>
                            </select>
                            <%--<input type="hidden" name="traderContactName">
                            <input type="hidden" name="traderContactTelephone">
                            <input type="hidden" name="traderContactMobile">--%>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>收货地址:</label>
                        </div>
                        <div class="f_left">
                            <select class="input-xx" id="address" name="traderAddress">
                                <option value="">请选择</option>
                            </select>
                            <%--<input type="hidden" name="traderArea">
                            <input type="hidden" name="traderAddress">--%>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>物流备注:</label>
                        </div>
                        <div class="f_left">
                            <input type="text" placeholder="请填写外借场景及外借商品等级需求" class="input-middle" id="logisticCommnet" name="logisticCommnet" value=""/>
                        </div>
                    </li>
                </div>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品信息</div>
                            <div class="title-click nobor  pop-new-data" layerParams='{"width":"700px","height":"480px","title":"添加产品","link":"<%=path%>/order/saleorder/addSaleorderGoods.do?callbackFuntion=setGoodData"}'>新增</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:auto">订货号</th>
                                <th style="width:auto">产品名称</th>
                                <th style="width:auto">品牌</th>
                                <th style="width:auto">型号</th>
                                <th style="width:auto">数量</th>
                                <th style="width:auto">预计归还日期</th>
                                <th style="width:auto">操作</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            <%--<tr>
                                <td>
                                    1111111111
                                    <input type="hidden" name="skuNo" value="">
                                </td>
                                <td>1111</td>
                                <td>2222</td>
                                <td>1233</td>
                                <td><input type="text" class="input-middle" name="outputNum"></td>
                                <td>
                                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期"
                                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                                           name="expectReturnedTime">
                                </td>
                                <td><a href="#" onclick="deleteRow(this)">删除</a></td>
                            </tr>--%>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter mt10">
                <button type="button" class="bt-bg-style bg-deep-green" onclick="addSubmit(this);">提交并审核</button>
            </div>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>