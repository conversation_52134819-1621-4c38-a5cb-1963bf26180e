package com.vedeng.erp.finance.cmd;

import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.BusinessTypeEnum;
import com.vedeng.common.core.enums.SettlementTypeEnum;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务结算单查询命令
 * @date 2023/11/16 10:41
 */
@Builder
@AllArgsConstructor
@Getter
public class SettlementBillQueryCmd {

    /**
     * 业务来源id
     */
    private Integer businessSourceId;

    /**
     * 业务来源类型
     */
    private BusinessSourceTypeEnum businessSourceTypeEnum;
}
