package com.vedeng.order.dao;

import com.vedeng.order.model.ComponentRelation;
import com.vedeng.order.model.RemarkComponent;
import com.vedeng.order.model.query.LabelQuery;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author:       davis
 * @Date:         2021/4/14 下午5:33
 * @Version:      1.0
 */
public interface RemarkComponentMapper {

    /**
     * 获取所有备注组件
     * @param labelQuery 查询参数
     * @return 备注组件集合
     */
    List<RemarkComponent> getInitComponent(LabelQuery labelQuery);

    /**
     * 获取备注组件场景关联关系
     * @param labelQuery 查询参数
     * @return 备注组件场景关联关系
     */
    ComponentRelation getComponentRelation(LabelQuery labelQuery);

    /**
     * 保存组件关联关系
     * @param componentRelation
     */
    void saveComponentRelation(ComponentRelation componentRelation);

    /**
     * 删除已配置的备注标签
     * @param param
     */
    void deleteComponentRelation(LabelQuery param);

    /**
     * 更更新已配置的备注标签
     * @param componentRelationIsExist
     */
    void updateComponentRelation(ComponentRelation componentRelationIsExist);

    /**
     * 获取组件备注标签
     * @param labelQuery
     * @return
     */
    List<ComponentRelation> getComponentRelationList(LabelQuery labelQuery);

    List<Map<String, Object>> findComponentList(Map map);

    /**
     * 获取报价单的内部备注标签
     * @param labelQuery
     * @return
     */
    List<ComponentRelation> getComponentRelationListByRelationId(@Param("labelQuery") LabelQuery labelQuery);

    /**
     * 组件关联关系
     * @param labelQuery
     * @return
     */
    List<RemarkComponent> selectComponentRelationList(@Param("labelQuery") LabelQuery labelQuery);

    ArrayList<Integer> selectType();
}
