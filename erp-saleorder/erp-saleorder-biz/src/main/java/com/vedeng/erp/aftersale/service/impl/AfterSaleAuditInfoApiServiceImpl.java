package com.vedeng.erp.aftersale.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity;
import com.vedeng.erp.aftersale.dto.AfterSaleAuditInfoDto;
import com.vedeng.erp.aftersale.mapper.AfterSaleAuditInfoMapper;
import com.vedeng.erp.aftersale.mapstruct.AfterSaleAuditInfoConvertor;
import com.vedeng.erp.aftersale.service.AfterSaleAuditInfoApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 审核表相关实现类
 * @date 2023/1/11 18:35
 **/
@Service
@Slf4j
public class AfterSaleAuditInfoApiServiceImpl implements AfterSaleAuditInfoApiService {

    @Autowired
    private AfterSaleAuditInfoMapper afterSaleAuditInfoMapper;

    @Autowired
    private AfterSaleAuditInfoConvertor afterSaleAuditInfoConvertor;

    @Override
    public List<AfterSaleAuditInfoDto> getAfterSaleAuditInfoDto(Integer afterSaleId) {
        log.info("getAfterSaleAuditInfoDto 入参：{}", JSON.toJSONString(afterSaleId));
        Assert.notNull(afterSaleId, "售后单id不可为空");
        List<AfterSaleAuditInfoEntity> afterSaleAuditInfoEntities = afterSaleAuditInfoMapper.selectByAfterSaleId(afterSaleId);

        return afterSaleAuditInfoConvertor.toDto(afterSaleAuditInfoEntities);

    }
}
