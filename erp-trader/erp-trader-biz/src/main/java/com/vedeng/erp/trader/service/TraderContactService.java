package com.vedeng.erp.trader.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.TraderContactDto;

import java.util.List;

/**
 * 交易者联系人(TraderContact)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-12 16:53:42
 */
public interface TraderContactService {

    /**
     * 分页查询
     *
     * @param recordDtoPageParam 分页参数
     * @return 分页对象
     */
    PageInfo<TraderContactDto> page(PageParam<TraderContactDto> recordDtoPageParam);
    /**
     * 分页查询
     *
     * @param recordDtoPageParam 分页参数
     * @return 分页对象
     */
    PageInfo<TraderContactDto> erpPage(PageParam<TraderContactDto> recordDtoPageParam);

    PageInfo<TraderContactDto> searchByTraderIdAndMobile(PageParam<TraderContactDto> recordDtoPageParam);


    PageInfo<TraderContactDto> searchByTraderIdAndMobileAccurateMatch(PageParam<TraderContactDto> recordDtoPageParam);

    /**
     * 新增数据
     *
     * @param traderContactDto 实例对象
     */
    void add(TraderContactDto traderContactDto);

    /**
     * 匹配联系人模糊查询
     *
     * @param traderContactDto 查询参数
     * @return list
     */
    List<TraderContactDto> matchTraderContractSearch(TraderContactDto traderContactDto);

    /**
     * 查询客户联系人信息中，在职的且最新沟通的一个联系人
     *
     * @param traderId traderId
     * @return TraderContactDto
     */
    List<TraderContactDto> getLatestCommunicateContact(Integer traderId);

    /**
     * 根据职位优先级 查询客户联系人
     *
     * @param traderId traderId
     * @return TraderContactDto
     */
    List<TraderContactDto> getTraderContactListBySort(Integer traderId);


    /**
     * 查询默认的联系人-北京京东弘健健康有限公司
     * @param traderId
     * @return
     */
    TraderContactDto queryTraderContractDefault(Integer traderId);

}
