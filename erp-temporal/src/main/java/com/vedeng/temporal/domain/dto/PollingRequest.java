package com.vedeng.temporal.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vedeng.temporal.config.TemporalProperties;
import com.vedeng.temporal.validation.BusinessCompletionChecker;
import com.vedeng.temporal.validation.CompletionCheckConfigParser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 业务状态查询请求对象
 * 用于封装状态轮询所需的参数信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-02
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class PollingRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 业务类型
     * 如：SALES_ORDER, PURCHASE_ORDER, INVENTORY, INVOICE, PAYMENT 等
     */
    private String businessType;

    /**
     * 公司代码
     * 业务单据所属的公司标识
     */
    private String companyCode;

    /**
     * API路径
     * 状态查询的API路径，不包含BaseURL（由SystemApiClient自动处理）
     */
    private String apiPath;

    /**
     * API参数
     * 调用API时需要传递的具体参数，由调用方根据API要求自定义设置
     * 如：{"saleOrderId": 123} 或 {"buyOrderId": 456, "status": "PENDING"}
     */
    private Map<String, Object> apiParameters;

    /**
     * 轮询配置
     * 如果为空，将使用默认配置
     */
    private TemporalProperties.PollingConfig pollingConfig;

    /**
     * 业务ID
     * 关联的业务流程ID，用于日志追踪
     */
    private String businessId;

    /**
     * 业务完成检查配置（必填）
     * 用于配置业务完成判断逻辑的字符串配置
     *
     * 支持的配置格式：
     * - "field:value" - 简单字段值检查，如 "auditStatus:APPROVED"
     * - "field:value1,value2" - 多值检查，如 "status:COMPLETED,APPROVED"
     * - "field:>value" - 数值比较，如 "paidAmount:>0"
     * - "field1:value1&field2:value2" - 多条件AND，如 "auditStatus:APPROVED&paidAmount:>0"
     * - "field1:value1|field2:value2" - 多条件OR，如 "status:COMPLETED|status:APPROVED"
     *
     * 使用示例：
     * <pre>
     * // 简单状态检查
     * .completionCheckConfig("auditStatus:APPROVED")
     *
     * // 复杂条件组合
     * .completionCheckConfig("auditStatus:APPROVED&paidAmount:>0")
     * </pre>
     */
    private String completionCheckConfig;
    

    /**
     * 获取有效的轮询配置
     * 如果当前配置为空，返回默认配置
     *
     * @return 轮询配置对象
     */
    @JsonIgnore
    public TemporalProperties.PollingConfig getEffectivePollingConfig() {
        if (pollingConfig != null) {
            return pollingConfig;
        }

        // 返回默认配置
        return new TemporalProperties.PollingConfig(
            true,    // enabled
            30,      // initialIntervalSeconds
            300,     // maxIntervalSeconds
            1.5,     // backoffCoefficient
            7,       // maxTimeoutDays
            5,       // heartbeatIntervalMinutes
            10,      // maxRetryCount
            false    // enableVerboseLogging
        );
    }

    /**
     * 获取业务完成检查器
     * 从 completionCheckConfig 解析创建检查器实例
     *
     * @return 业务完成检查器
     */
    @JsonIgnore
    public BusinessCompletionChecker getCompletionChecker() {
        return CompletionCheckConfigParser.parseConfig(completionCheckConfig);
    }

    /**
     * 验证请求参数的有效性
     *
     * @throws IllegalArgumentException 如果必要参数缺失或无效
     */
    @JsonIgnore
    public void validate() {
        if (businessType == null || businessType.trim().isEmpty()) {
            throw new IllegalArgumentException("业务类型不能为空");
        }

        if (companyCode == null || companyCode.trim().isEmpty()) {
            throw new IllegalArgumentException("公司代码不能为空");
        }

        if (apiPath == null || apiPath.trim().isEmpty()) {
            throw new IllegalArgumentException("API路径不能为空");
        }

        if (completionCheckConfig == null || completionCheckConfig.trim().isEmpty()) {
            throw new IllegalArgumentException("业务完成检查配置不能为空");
        }

        // 验证轮询配置
        TemporalProperties.PollingConfig config = getEffectivePollingConfig();
        if (config != null) {
            validatePollingConfig(config);
        }
    }

    /**
     * 验证轮询配置参数的有效性
     *
     * @param config 轮询配置
     * @throws IllegalArgumentException 如果配置参数无效
     */
    private void validatePollingConfig(TemporalProperties.PollingConfig config) {
        if (config.getInitialIntervalSeconds() <= 0) {
            throw new IllegalArgumentException("初始轮询间隔必须大于0秒");
        }

        if (config.getMaxIntervalSeconds() <= 0) {
            throw new IllegalArgumentException("最大轮询间隔必须大于0秒");
        }

        if (config.getMaxIntervalSeconds() < config.getInitialIntervalSeconds()) {
            throw new IllegalArgumentException("最大轮询间隔不能小于初始轮询间隔");
        }

        if (config.getBackoffCoefficient() <= 1.0) {
            throw new IllegalArgumentException("退避系数必须大于1.0");
        }

        if (config.getMaxTimeoutDays() <= 0) {
            throw new IllegalArgumentException("最大超时时间必须大于0天");
        }

        if (config.getHeartbeatIntervalMinutes() <= 0) {
            throw new IllegalArgumentException("心跳间隔必须大于0分钟");
        }

        if (config.getMaxRetryCount() == 0) {
            throw new IllegalArgumentException("最大重试次数不能为0");
        }
    }

    /**
     * 获取请求的唯一标识
     * 用于日志记录和问题追踪
     *
     * @return 请求唯一标识字符串
     */
    @JsonIgnore
    public String getRequestKey() {
        return String.format("%s:%s:%s", companyCode, businessType, businessId);
    }
    


    @Override
    public String toString() {
        return String.format("BusinessStatusRequest{businessType='%s', companyCode='%s', businessId='%s'}",
                businessType, companyCode, businessId);
    }
}
