<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑售后-退款" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%=basePath%>/static/js/orderstream/aftersales/add_afterSales_th.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='<%=basePath%>/static/js/orderstream/aftersales/controlRadio.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<div class="form-list  form-tips8">
    <form method="post" action="<%= basePath %>/order/aftersalesUpgrade/saveEditAfterSales.do">
        <ul>
            <li>
                <div class="form-tips">
                    <lable>售后类型</lable>
                </div>
                <div class="f_left">
                    <div class="form-blanks">
                        <ul class="aftersales">
                            <li>
                                <a href="javascript:void(0);">
                                    <input type="radio" name="type" value="539" disabled="disabled">
                                    <label>销售退货</label>
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);">
                                    <input type="radio" name="type" value="540" disabled="disabled">
                                    <label>销售换货</label>
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);">
                                    <input type="radio" name="type" value="543" disabled="disabled" checked="true">
                                    <label>销售退款</label>
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);">
                                    <input type="radio" name="type" value="542" disabled="disabled ">
                                    <label>销售退票</label>
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);">
                                    <input type="radio" value="1135" disabled="disabled">
                                    <label>销售丢票</label>
                                </a>
                            </li>
                            <!-- 耗材订单不展示 安调与维修 -->
                            <c:if test="${saleorder.orderType ne 5}">
                                <li>
                                    <a href="javascript:void(0);">
                                        <input type="radio" name="type" value="541" disabled="disabled">
                                        <label>销售安调</label>
                                    </a>
                                </li>
                                <li>
                                    <a href="javascript:void(0);">
                                        <input type="radio" name="type" value="584" disabled="disabled">
                                        <label>销售维修</label>
                                    </a>
                                </li>
                            </c:if>
                            <li>
                                <a href="javascript:void(0);">
                                    <input type="radio" name="type" value="544" disabled="disabled">
                                    <label>技术咨询</label>
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0);">
                                    <input type="radio" name="type" value="545" disabled="disabled">
                                    <label>其他</label>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </li>
            <input type="hidden" id="shtype" value="tk"/>
            <input type="hidden" name="afterSalesId" value="${afterSales.afterSalesId}"/>
            <input type="hidden" name="afterSalesDetailId" value="${afterSales.afterSalesDetailId}"/>
            <input type="hidden" name="subjectType" value="${afterSales.subjectType}"/>
            <input type="hidden" name="type" value="${afterSales.type}"/>
            <input type="hidden" name="beforeParams" value='${beforeParams}'/>


            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>详情说明</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks ">
                        <textarea name="comments" id="comments" placeholder="请详述客户退款要求以便售后同事联系" rows="5"
                                  class="wid90">${afterSales.comments}</textarea>
                    </div>
                    <div id="commentsError"></div>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>售后单报单人</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <select class="input-largest" name="traderContactId" id="traderContactId">
                            <c:if test="${empty afterSales.traderContactId}">
                                <c:forEach items="${saleorder.tcList}" var="tc">
                                    <option value="${tc.traderContactId}"
                                            <c:if test="${saleorder.traderContactId eq tc.traderContactId}">selected="selected"</c:if>>${tc.name}|${tc.mobile}|${tc.telephone}</option>
                                </c:forEach>
                            </c:if>
                            <c:if test="${not empty afterSales.traderContactId}">
                                <c:forEach items="${saleorder.tcList}" var="tc">
                                    <option value="${tc.traderContactId}"
                                            <c:if test="${afterSales.traderContactId eq tc.traderContactId}">selected="selected"</c:if>>${tc.name}|${tc.mobile}|${tc.telephone}</option>
                                </c:forEach>
                            </c:if>
                        </select>
                    </div>
                    <div id="traderContactIdError"></div>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>售后联系人</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" id="afterConnectUserName" name="afterConnectUserName"
                               value="${afterSales.afterConnectUserName}" placeholder="请输入售后联系人名">
                    </div>
                    <div id="afterConnectUserNameError" class="font-red " style="display: none">售后联系人名不允许为空</div>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>售后联系电话</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" id="afterConnectPhone" name="afterConnectPhone"
                               value="${afterSales.afterConnectPhone}" placeholder="请输入售后联系人电话/手机号">
                    </div>
                    <div id="afterConnectPhoneError" class="font-red " style="display: none">售后联系人电话/手机号不允许为空</div>
                </div>
            </li>

            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>退款金额</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" name="refundAmount" id="refundAmount" class="input-middle"
                               value="${afterSales.refundAmount}"/>
                    </div>
                    <div id="refundAmountError"></div>
                </div>
            </li>

                </div>
            </li>

            <li>
                <div class="form-tips">
                    <lable>添加附件</lable>
                </div>
                <input type="hidden" id="domain" name="domain" value="${domain}">
                <input type="hidden" id="realAmount" value="${saleorderDataInfo['realAmount']}">
                <input type="hidden" id="payedAmount"
                       value="${saleorderDataInfo['paymentAmount'] + saleorderDataInfo['periodAmount'] - saleorderDataInfo['lackAccountPeriodAmount'] - saleorderDataInfo['refundBalanceAmount']}">
                <div class="f_left ">
                    <c:if test="${empty afterSales.attachmentList }">
                        <div class="form-blanks">
                            <div class="pos_rel f_left">
                                <input type="file" class="uploadErp" id='file_1' name="lwfile"
                                       onchange="uploadFile(this,1);">
                                <input type="text" class="input-largest" id="name_1" readonly="readonly"
                                       placeholder="请上传附件" name="fileName" onclick="file_1.click();">
                                <input type="hidden" id="uri_1" name="fileUri">
                            </div>
                            <label class="bt-bg-style bt-small bg-light-blue" type="file" id="busUpload"
                                   onclick="file_1.click();">浏览</label>
                            <!-- 上传成功出现 -->
                            <div class="f_left">
                                <i class="iconsuccesss mt3 none" id="img_icon_1"></i>
                                <a href="" target="_blank" class="font-blue cursor-pointer  mt3 none"
                                   id="img_view_1">查看</a>
                                <span class="font-red cursor-pointer  mt3 none" onclick="del(1)"
                                      id="img_del_1">删除</span>
                            </div>
                            <div class='clear'></div>
                        </div>
                    </c:if>
                    <c:if test="${not empty afterSales.attachmentList}">
                        <c:forEach items="${afterSales.attachmentList}" var="att" varStatus="status">
                            <div class="form-blanks <c:if test='${status.count ne 1}'>mt10</c:if>">
                                <div class="pos_rel f_left">
                                    <input type="file" class="uploadErp" id='file_${status.count}' name="lwfile"
                                           onchange="uploadFile(this,${status.count});">
                                    <input type="text" class="input-largest" id="name_${status.count}"
                                           readonly="readonly"
                                           placeholder="请上传附件" name="fileName" onclick="file_${status.count}.click();"
                                           value="${att.name}">
                                    <input type="hidden" id="uri_${status.count}" name="fileUri" value="${att.uri}">
                                </div>
                                <label class="bt-bg-style bt-small bg-light-blue" type="file" id="busUpload"
                                       onclick="file_${status.count}.click();">浏览</label>
                                <!-- 上传成功出现 -->
                                <div class="f_left">
                                    <i class="iconsuccesss mt3" id="img_icon_${status.count}"></i>
                                    <a href="http://${att.domain}${att.uri}" target="_blank"
                                       class="font-blue cursor-pointer mt3" id="img_view_${status.count}">查看</a>
                                    <span class="font-red cursor-pointer mt3" onclick="del(${status.count})"
                                          id="img_del_${status.count}">删除</span>
                                </div>
                                <div class='clear'></div>
                            </div>
                        </c:forEach>
                    </c:if>
                    <div class="mt8" id="conadd">
                        <span class="bt-border-style bt-small border-blue" onclick="conadd();">继续添加</span>
                    </div>
                    <div class="font-grey9  mb4">
                        支持拓展名：png.jpg.jpeg等图片格式
                        <br/>
                        文件大小不得超过2M
                    </div>
                    <div class="pop-friend-tips mt6">
                        <div class="add-tijiao text-left mt8">
                            <button type="submit" id="submit">提交</button>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </form>
</div>
<%@ include file="../../common/footer.jsp" %>