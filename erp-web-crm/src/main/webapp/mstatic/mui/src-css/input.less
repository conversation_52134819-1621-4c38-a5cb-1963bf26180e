.vd-ui-input-wrap {
    position: relative;

    .vd-ui-input {
        width: 100%;
        padding: 10px;
        border-radius: 3px;
        background: transparent;

        &::placeholder {
            color: #ccc;
            font-size: 12px;
        }

        &.border {
            border: 1px solid #BABFC2;
            padding: 5px 9px;
            font-size: 14px;
            background: #fff;

            &::placeholder {
                font-size: 14px;
            }

            &:focus {
                border-color: #09f;
            }
        }
    }

    .icon {
        position: absolute;
        width: 36px;
        height: calc(100% - 2px);
        background: #fff;
        right: 1px;
        top: 1px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: #666;
        border-radius: 3px;
    }
}



.vd-ui-textarea-wrap {
    position: relative;
    padding: 10px;

    .vd-ui-textarea {
        width: 100%;
        border-radius: 3px;
        background: transparent;

        &::placeholder {
            color: #ccc;
            font-size: 12px;
        }

        &.border {
            border: 1px solid #BABFC2;
            padding: 5px 9px;
            font-size: 14px;
            background: #fff;

            &::placeholder {
                font-size: 14px;
            }

            &:focus {
                border-color: #09f;
            }
        }
    }

    .vd-ui-textarea-count {
        color: #999;
        position: absolute;
        top: -23px;
        right: 10px;
        text-align: right;
        white-space: nowrap;

        &.upper-limit {
            color: #f60;
        }
    }
}