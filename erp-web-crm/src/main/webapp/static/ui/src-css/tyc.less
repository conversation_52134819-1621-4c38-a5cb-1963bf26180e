@import (less) '../common.css';

.vd-ui-tianyancha-wrap { 
    .tyc-trader-tip {
        display: flex;
        align-items: center;
        margin-top: 5px;

        .vd-ui_icon {
            line-height: 1;
            margin-right: 5px;
            font-size: 16px;
        }

        &.tip-success {
            color: #13BF13;
        }

        &.tip-warn {
            color: #FF6600;
        }
    }
}

.vd-ui-tianyancha {
    position: relative;
    display: flex;

    .vd-ui-tyc-input {
        width: 300px;
        position: relative;
    }

    .vd-ui-tyc-salename {
        position: absolute;
        right: 1px;
        color: #999;
        line-height: 28px;
        top: 1px;
        background: #fff;
        padding: 0 10px;
        border-radius: 3px;
    }

    .vd-ui-tyc-right {
        height: 30px;
        display: flex;
        align-items: center;

        .vd-ui-tyc-search-icon {
            display: flex;
            align-items: center;
            margin-left: 10px;
            cursor: pointer;
            color: #09F;

            .icon-search {
                font-size: 16px;
            }
            > span {
                margin-left: 5px;
            }

            &:hover {
                color: #f60;
            }
        }

        .vd-ui-tyc-warn-icon {
            position: relative;

            .icon-caution1 {
                font-size: 16px;
                color: #F60;
                margin-left: 10px;
    
                &:hover + .vd-ui-tyc-icon-tip {
                    display: block;
                }
            }

            .vd-ui-tyc-icon-tip {
                display: none;
                position: absolute;
                transition: display 0.3s ease;
                top: -50px;
                left: -3px;
                max-width: 600px;
                width: 100vw;

                .tip-txt {
                    position: relative;
                    max-width: 600px;
                    max-width: max-content;
                    max-height: 110px;
                    padding: 10px 15px;
                    background-color: #fff;
                    font-size: 12px;
                    border-radius: 3px;
                    box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
                    box-sizing: border-box;

                    &::after {
                        content: "";
                        display: block;
                        width: 0;
                        height: 0;
                        border-right: 5px solid transparent;
                        border-left: 5px solid transparent;
                        border-top: 5px solid #fff;
                        border-bottom: 5px solid transparent;
                        position: absolute;
                        top: 38px;
                        left: 16px;
                    }
                }
            }
        }
        .icon-tyc {
            font-size: 16px;
            width: 16px;
            height: 16px;
            display: inline-block;
            background: url(../image/tyc.png) no-repeat;
            background-size: 16px;
            margin-left: 10px;
            cursor: pointer;
        }
    }

    .vd-ui-tyc-search-related {
        max-height: 370px;
        overflow-y: auto;
        border-radius: 3px;
        background: #fff;
        border: solid 1px #BABFC2;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
        position: absolute;
        top: 30px;
        z-index: 9;
        .scrollbar();

        .loading {
            color: #666;
            height: 39px;
            line-height: 37px;
            padding: 0px 10px;
            overflow: hidden;
            i {
                animation: loading 1.8s linear  infinite;
                display: inline-block;
                position: relative;
                top: 1px;
                font-size: 16px;
                margin-right: 5px;
                color: #09F;
            }
        }
        .failed-li {
            height: 39px;
            line-height: 39px;
            padding: 0px 10px;
            i {
                position: relative;
                top: 2px;
                font-size: 16px;
                color: #E64545;
                margin-right: 5px;
            }
            .reload {
                color: #09f;
                cursor: pointer;
            }
        }
        .empty-li {
            height: 39px;
            line-height: 39px;
            padding: 0px 10px;
            text-align: center;
            color: #999;
        }

        .search-related-list {
            padding: 5px 10px;

            .local-data {
                font-size: 12px;
                color: #999;
                line-height: 30px;
            }
            .sr-item {
                padding: 6px 0;
                display: flex;
                justify-content: space-between;
                cursor: pointer;

                .sr-item-left {
                    flex: 1;
                    min-width: 0;
                    margin-right: 10px;
                    display: flex;

                    .icon {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        flex-shrink: 0;
                        margin-right: 5px;
                        margin-top: 1px;
                        background: url('../image/tyc.png') no-repeat;
                        background-size: 16px;
                    }
                    > p {
                        flex: 1;
                    }
                }
                .sr-item-right {
                    width: 150px;
                    text-align: right;
                    color: #999;
                }

                &:hover .sr-item-left {
                    color: #f60;
                }

                &.disabled {
                    color: #999;
                    pointer-events: none;
                }
            }
            
        }
    }

}

// 天眼查列表
.tyc-table-wrap.vd-ui-table-wrap {
    width: 100%;

    &.with-border {
        border-left: 1px solid #E1E5E8;
        border-right: 1px solid #E1E5E8;
    }

    .vd-ui-wrap-widthscroll {

        .vd-ui-th,
        .vd-ui-td {
            transition: box-shadow 0.3s;

            &.sticky-left {
                position: sticky;
                left: 0;
                z-index: 3;
            }

            &.sticky-right {
                position: sticky;
                right: 0;
                z-index: 3;
            }
        }

        .vd-ui-th {
            &.sticky-left {
                &:before {
                    content: "";
                    width: 1px;
                    height: 100%;
                    background: #E1E5E8;
                    right: -1px;
                    position: absolute;
                    top: 0;
                }
            }
        }

        &.is-left-fixed {

            .vd-ui-th,
            .vd-ui-td {
                &.sticky-left-last {
                    &:after {
                        position: absolute;
                        top: -1px;
                        left: -1px;
                        width: 100%;
                        height: 100%;
                        content: "";
                        pointer-events: none;
                    }

                    &:before {
                        background: transparent;
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: -1px;
                        width: 100%;
                        transform: translate(100%);
                        transition: box-shadow 0.3s;
                        content: "";
                        pointer-events: none;
                        box-shadow: inset 10px 0 8px -8px #00000026;
                    }
                }
            }

            .vd-ui-th {
                &.sticky-left-last {
                    border-right: 0;
                }

                &.sticky-left-last+td {
                    border-left: 0;
                }
            }
        }

        &.is-right-fixed {

            .vd-ui-th,
            .vd-ui-td {
                &.sticky-right-first {
                    &:after {
                        position: absolute;
                        top: -1px;
                        right: -1px;
                        width: calc(100% - 1px);
                        height: 100%;
                        content: "";
                        pointer-events: none;
                    }

                    &:before {
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: -1px;
                        width: 100%;
                        transform: translate(-100%);
                        transition: box-shadow 0.3s;
                        content: "";
                        pointer-events: none;
                        box-shadow: inset -10px 0 8px -8px #00000026;
                    }
                }
            }

            .vd-ui-th {
                &.sticky-right-first {
                    border-left: 0;
                }
            }
        }
    }

    .vd-ui-table-body {
        overflow: auto;
        position: relative;


        &.vd-ui-wrap-scroll {
            overflow-y: auto;
        }

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: #D7DADE;
            width: 6px;
            height: 6px;
            border-radius: 3px;

            &:hover {
                background: #BABFC2;
            }

            &:active {
                background: #969B9E;
            }
        }
    }

    .vd-ui-table-container {
        width: 100%;
        overflow: auto;
    }

    .vd-ui-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        text-align: left;

        .td-text {
            word-break: break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .td-text2 {
            word-break: break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .td-text .td-link {
            color: #0066CC;
            cursor: pointer;

            &:hover {
                color: #f60;
            }
        }

        .vd-ui-th {
            padding: 8px 10px;
            background: #FAFBFC;
            font-weight: normal;
            border: solid 1px #E1E5E8;
            color: #999;
            position: relative;
            z-index: 2;

            &:first-child {
                border-left: 0;
            }

            &:last-child {
                border-right: 0;
            }

            &.vd-ui-th-bar {
                border-left-color: #F5F7FA;
                z-index: 1;
            }

            &.vd-ui-th-bar-prev {
                border-right-color: #F5F7FA;
            }
        }

        .vd-ui-td {
            padding: 8px 10px;
            border-bottom: solid 1px #EBEFF2;
            background: #fff;
            vertical-align: top;
            word-break: break-all;
        }

        .vd-ui-tr {
            &:hover {
                .vd-ui-td {
                    background: #EBEFF2;
                }
            }

            &.tr-checked {
                .vd-ui-td {
                    background: #E6F7FF;
                }

                &:hover {
                    .vd-ui-td {
                        background: #D1EEFF;
                    }
                }
            }
        }
    }

    .option-item {
        color: #0066CC;
        cursor: pointer;
        margin-right: 20px;
        position: relative;

        &::before {
            content: "";
            width: 1px;
            position: absolute;
            right: -10px;
            height: 15px;
            background: #e1e5e8;
            top: 3px;
        }

        &:last-child {
            margin-right: 0;

            &::before {
                display: none;
            }
        }

        &.item-red {
            color: #E64545;
        }

        &:hover {
            color: #f60;
        }
    }

    .tyc-choose {
        color: #09f;
        cursor: pointer;

        &:hover {
            color: #f60;
        }
    }
}
.tyc-empty {
    padding: 130px 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .empty-img {
        width: 110px;
        height: 95px;
        background: url('../image/common/no-data.svg') no-repeat;
        background-size: 110px 95px;
        background-position: center;
        margin-bottom: 40px;
    }
}

// 天眼查详情
.tyc-info-wrap {
    @keyframes zhuan {
        0% {
            transform: rotate(0);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .tyc-info-loading {
        padding: 100px 0;
        text-align: center;

        .icon-loading {
            display: inline-block;
            font-size: 32px;
            color: #06c;
            animation: zhuan 2s linear infinite;
        }
    }

    .tyc-info-title {
        margin-bottom: 20px;

        .tyc-info-title-txt {
            font-size: 20px;
            font-weight: 700;
        }

        .tyc-info-tags {
            display: flex;
            align-items: center;
            margin-top: 10px;
            margin-bottom: -5px;
            flex-wrap: wrap;

            .tag-item {
                background: #E6F7FF;
                font-size: 12px;
                height: 22px;
                line-height: 22px;
                padding: 0 5px;
                margin-right: 5px;
                margin-bottom: 5px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }

    .tyc-info-list {
        // max-height: 500px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: #D7DADE;
            width: 6px;
            height: 6px;
            border-radius: 3px;

            &:hover {
                background: #BABFC2;
            }

            &:active {
                background: #969B9E;
            }
        }

        .tyc-info-item {
            display: flex;
            border-bottom: solid 1px #E1E5E8;

            .tyc-info-label {
                width: 160px;
                padding: 7px 10px;
                border-left: solid 1px #E1E5E8;
                background: #F5F7FA;
            }

            .tyc-info-txt {
                flex: 1;
                padding: 7px 10px;
                border-left: solid 1px #E1E5E8;
                border-right: solid 1px #E1E5E8;
            }

            &:first-child {
                border-top: solid 1px #E1E5E8;
            }
        }
    }
}

