package com.vedeng.flash.dao;

import com.vedeng.authorization.model.User;
import com.vedeng.flash.dto.po.NewAddOrderingQueryPO;
import com.vedeng.flash.dto.po.OrderingPoolQueryPO;
import com.vedeng.flash.dto.po.RecommendOrderingQueryPO;
import com.vedeng.flash.dto.PrepareStockCommand;
import com.vedeng.flash.dto.temp.JoinOrderingPoolOperateTemp;
import com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp;
import com.vedeng.flash.dto.PrepareStockDto;
import com.vedeng.flash.model.RegularOperateLog;
import com.vedeng.flash.model.RegularPrepareSku;
import com.vedeng.flash.model.RegularSnapshot;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.order.model.Buyorder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RegularPrepareSkuMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_PREPARE_SKU
     *
     * @mbggenerated Tue May 18 14:39:24 CST 2021
     */
    int deleteByPrimaryKey(Integer regularId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_PREPARE_SKU
     *
     * @mbggenerated Tue May 18 14:39:24 CST 2021
     */
    int insert(RegularPrepareSku record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_PREPARE_SKU
     *
     * @mbggenerated Tue May 18 14:39:24 CST 2021
     */
    int insertSelective(RegularPrepareSku record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_PREPARE_SKU
     *
     * @mbggenerated Tue May 18 14:39:24 CST 2021
     */
    PrepareStockDto selectByPrimaryKey(Integer regularId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_PREPARE_SKU
     *
     * @mbggenerated Tue May 18 14:39:24 CST 2021
     */
    int updateByPrimaryKeySelective(RegularPrepareSku record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_PREPARE_SKU
     *
     * @mbggenerated Tue May 18 14:39:24 CST 2021
     */
    int updateByPrimaryKey(RegularPrepareSku record);

    /**
     * DAO查询定品池
     * @param mapParamValue
     * @return
     */
    List<OrderingPoolQueryPO> selectOrderingPoollistpage(Map<String, Object> mapParamValue);

    /**
     * DAO查询推荐定品
     * @param mapParamValue
     * @return
     */
    List<RecommendOrderingQueryPO> selectRecommendOrderinglistpage(Map<String, Object> mapParamValue);

    /**
     * 分页查询备货计划列表
     * @param param 查询参数
     * @return 备货计划集合
     */
    List<PrepareStockDto> getPrepareStockListPage(Map<String, Object> param);

    /**
     * 根据SKU_NO批量查询SKU的在途数量
     * @param skuNoList skuNo集合
     * @return SKU在途数量集合
     */
    List<PrepareStockDto> getInTransitSkuNumList(List<String> skuNoList);

    /**
     * 获取平均到货时间
     * @param skuNoList sku集合
     * @return 平均到货时间集合
     */
    List<PrepareStockDto> getReceiveTimesList(List<String> skuNoList);

    /**
     * 获取SKU前3个月日均销量
     * @param command 查询条件
     * @return SKU销量集合
     */
    List<PrepareStockDto> getRecentSkuSaleNumList(PrepareStockCommand command);

    /**
     * 获取SKU前3个月日均销量
     * @param skuIdList 查询条件
     * @return SKU销量集合
     */
    List<PrepareStockDto> getRecentSkuSaleNumListModify(@Param("skuIdList") List<Integer> skuIdList);

    /**
     * 查询含SKU的采购单订单
     * @param command 查询条件
     * @return 采购单集合
     */
    List<Buyorder> getBuyorderListByPrepareSku(PrepareStockCommand command);

    /**
     * 获取对应销售量
     * @param skuId
     * @return
     */
    OneThreeMonthLastYearSaleNumTemp selectOneThreeMonthLastYearSaleNum(Integer skuId);

    /**
     * 获取对应销售量
     * @param skuId
     * @return
     */
    List<OneThreeMonthLastYearSaleNumTemp> selectOneThreeMonthLastYearSaleNumModify(@Param("skuIdList") List<Integer> skuIdList);

    /**
     * skuId查询定品池定品
     * @param skuId
     */
    RegularPrepareSku selectBySkuId(Integer skuId);

    /**
     * 根据条件查询定品
     * @param command 查询条件
     * @return 定品
     */
    PrepareStockDto getPrepareStockByCommand(PrepareStockCommand command);

    /**
     * 更新安全库存
     * @param stockDto
     */
    void updatePrepareSkuById(PrepareStockDto stockDto);

    /**
     * 获取全局系数
     * @return
     */
    Integer getSafeRatio();

    /**
     * 删除定品
     * @param stockDto
     */
    void deletePrepareRegularById(PrepareStockDto stockDto);

    /**
     * 更新定品是否备货
     * @param list
     */
    void updatePrepare(List<Integer> list);

    /**
     * 新增定品查询
     * @param mapParamValue
     * @return
     */
    List<NewAddOrderingQueryPO> selectNewAddOrderinglistpage(Map<String, Object> mapParamValue);

    /**
     * 更新预警等级
     * @param prepareStockDto
     */
    void updatePrepareWarnLevelById(PrepareStockDto prepareStockDto);

    /**
     * 更新定品状态
     * @param skuIdList
     * @param user
     */
    void updatePrepareRegularBySkuId(@Param("skuIdList") List<Integer> skuIdList, @Param("user") User user);

    /**
     * 根据订货号查询所有简单日志信息（复杂信息后置）
     * @param skuNoList
     * @return
     */
    List<RegularOperateLog> selectRegularOperateLogBySkuNoList(@Param("skuNoList")List<String> skuNoList);

    /**
     * 获取预测安全库存系数
     * @return
     */
    Integer getForecastSafeRatio();

    /**
     * 删除预测安全库存系数
     * @param prepareStockDto
     */
    void deleteForecastSafeRatio(PrepareStockDto prepareStockDto);

    /**
     * 新增预测安全库存系数
     * @param prepareStockDto
     */
    void insertForecastSafeRatio(PrepareStockDto prepareStockDto);

    /**
     * 更新首次断货时间
     * @param prepareStockDto
     */
    void updatePrepareFirstOutStockTimeById(PrepareStockDto prepareStockDto);

    /**
     * 查询当月定品池中启动定品
     * @return
     */
    List<RegularSnapshot> selectSnapshotListByExportMonth();

    /**
     * 获取在途采购订单
     * @param command
     * @return
     */
    List<Buyorder> getIntranstiStockList(PrepareStockCommand command);

    List<OneThreeMonthLastYearSaleNumTemp> selectLastYearSumSaleNum(@Param("skuIdList") List<Integer> skuIdList);

    List<OneThreeMonthLastYearSaleNumTemp> selectLastYearPartSaleNum(@Param("skuIdList") List<Integer> skuIdList);

    List<OneThreeMonthLastYearSaleNumTemp> selectThreeMonthSumSaleNum(@Param("skuIdList") List<Integer> skuIdList);

    List<OneThreeMonthLastYearSaleNumTemp> selectThreeMonthPartSaleNum(@Param("skuIdList") List<Integer> skuIdList);

    List<OneThreeMonthLastYearSaleNumTemp> selectOneMonthSumSaleNum(@Param("skuIdList") List<Integer> skuIdList);

    List<OneThreeMonthLastYearSaleNumTemp> selectOneMonthPartSaleNum(@Param("skuIdList") List<Integer> skuIdList);


}
