package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.kpi.replica.dao.AfterSalesReplicaMapper;
import com.vedeng.kpi.replica.dao.CapitalBillReplicaMapper;
import com.vedeng.kpi.replica.dao.SaleorderReplicaMapper;
import com.vedeng.kpi.service.KpiCalculateService;
import com.vedeng.order.model.Saleorder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date created in 2020/8/27 17:46
 */
@Component
@JobHandler("kpiCompensateCalculateTask")
public class KpiCompensateCalculateTask extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(KpiCompensateCalculateTask.class);

    @Autowired
    @Qualifier("saleorderReplicaMapper")
    private SaleorderReplicaMapper saleorderReplicaMapper;

    @Autowired
    @Qualifier("afterSalesReplicaMapper")
    private AfterSalesReplicaMapper afterSalesReplicaMapper;

    @Autowired
    @Qualifier("capitalBillReplicaMapper")
    private CapitalBillReplicaMapper capitalBillReplicaMapper;

    @Autowired
    private KpiCalculateService kpiCalculateService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        long startTimestamp;
        if (StringUtils.isBlank(param)){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            param = LocalDate.now().plusMonths(-1).format(formatter);
        }
        startTimestamp = DateUtil.convertLong(param + " 00:00:00","yyyy-MM-dd HH:mm:ss");
        int offset = 0;
        int limit = 100;
        int saleorderSize;
        do {
            List<Integer> saleorderIdList = saleorderReplicaMapper.getSaleorderIdByValidTimeOrModTime(startTimestamp,null,0,offset*limit,limit);
            saleorderSize = saleorderIdList.size();

            if (saleorderSize > 0){
                saleorderReplicaMapper.getSaleorderBySaleorderIdList(saleorderIdList)
                        .parallelStream()
                        .forEach(this::calculateKpiBySaleorder);
            }
            offset ++;
        }while (saleorderSize > 0);
        return ReturnT.SUCCESS;
    }


    private void calculateKpiBySaleorder(Saleorder saleorder){
        Long executeTime = kpiCalculateService.checkSaleorderPerformanceOfSaleorder(saleorder);
        if (executeTime > 0){
            logger.info("五行业绩计算，订单{}补偿计算",saleorder.getBussinessNo());
        }
        //获取订单的售后单（售后单为销售退货、且退款状态为已退款）
        afterSalesReplicaMapper.getAfterSalesBySaleorderNo(saleorder.getSaleorderNo(),executeTime)
                .forEach(afterSales -> {
                    if (afterSales.getType() == 539){
                        Integer refundStatus = afterSales.getAfterSalesDetail().getRefundAmountStatus();
                        if (refundStatus != null && refundStatus == 3){
                            //获取售后单的最新一条流水
                            Optional.ofNullable(capitalBillReplicaMapper.getFirstCapitalBillByOrder(afterSales.getAfterSalesNo()))
                                    .ifPresent(capitalBill -> kpiCalculateService.checkSalesPerformanceOfAfterSales(afterSales,capitalBill.getAddTime()));
                        }
                    }
                });
    }
}
