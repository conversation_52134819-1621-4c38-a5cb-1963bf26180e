<%@ tag language="java" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>


<style>
    @font-face {
        font-family: layui-icon;
        src: url(/static/layui/layui2.5.6/font/iconfont.eot?v=256);
        src: url(/static/layui/layui2.5.6/font/iconfont.eot?v=256#iefix) format('embedded-opentype'),url(/static/layui/layui2.5.6/font/iconfont.woff2?v=256) format('woff2'),url(/static/layui/layui2.5.6/font/iconfont.woff?v=256) format('woff'),url(/static/layui/layui2.5.6/font/iconfont.ttf?v=256) format('truetype'),url(/static/layui/layui2.5.6/font/iconfont.svg?v=256#layui-icon) format('svg')
    }
    
    .layui-icon {
        font-family: layui-icon!important;
        font-size: 16px;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: none;
    }
    
    .task-date-wrap {
        width: 100%;
        text-align: center;
        position: relative;
    }
    
    .task-date-wrap  #layui-laydate1 {
        border: 0;
        box-shadow: none;
        display: block;
        font-size: 12px;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-main {
        width: 100%;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-content {
        padding: 10px 20px;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-header {
        padding-top: 5px;
        border-bottom-color: #EFEFEF;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-header .laydate-next-m {
        right: 80px;
        top: 5px;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-header .laydate-prev-m {
        left: 80px;
        top: 5px;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-footer {
        padding: 0;
        height: 0;
        border: 0;
        position: static;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-footer .layui-laydate-preview {
        display: none;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-footer .laydate-btns-now {
        position: absolute;
        top: -2px;
        right: 2px;
        border: 0;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-footer .laydate-btns-now:hover {
        color: #09f;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-main table {
        width: 100%;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-main td {
        border-radius: 3px;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-main td:hover {
        background: #F5F7FA;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-main table .layui-this {
        background: #09f !important;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-header .laydate-prev-y, 
    .task-date-wrap  #layui-laydate1 .layui-laydate-header .laydate-next-y {
        display: none;
    }
    
    .task-date-wrap  #layui-laydate1 .layui-laydate-header .laydate-set-ym {
        pointer-events: none;
    }
    
    .task-date-wrap .task-tip {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: absolute;
        top: 3px;
        right: 3px;
    }
    
    .task-date-wrap .task-tip.tip-orange {
        background: #f60;
    }
    
    .task-date-wrap .task-tip.tip-grey {
        background: #babfc2;
    }

    .task-list-wrap {
        border-top: solid 1px #EFEFEF;
        padding: 20px;
        font-size: 12px;
        text-align: left;
    }

    .task-list-wrap .task-list .task-item {
        padding: 6px 10px;
        border-left: 3px solid;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 5px;
        background: #F5F7FA;
        cursor: pointer;
    }

    .task-list-wrap .task-list .task-item:hover {
        background: #E0F3FF;
    }

    .task-list-wrap .task-list .task-item.item-status-0 {
        border-left: solid 3px #FF6600;
    }

    .task-list-wrap .task-list .task-item.item-status-1 {
        border-left: solid 3px #13BF13;
        color: #999;
    }

    .task-list-wrap .task-list .task-item.item-status-2 {
        border-left: solid 3px #1A4D80;
        color: #999;
    }

    .task-list-wrap .task-list .task-item:last-child {
        margin-bottom: 0;
    }

    .task-list-wrap .task-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
    }

    .task-list-wrap .task-total {
        color: #999;
    }

    .task-list-wrap .task-more {
        color: #09f;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .task-list-wrap .task-more:hover {
        color: #f60;
    }

    .task-list-wrap .task-more i {
        background: none;
        height: auto;
        margin-left: 5px;
        margin-top: -1px;
        font-size: 16px;
    }

    .task-list-wrap .task-empty {
        padding-top: 94px;
        text-align: center;
        color: #999;
        height: 270px;
    }

    .task-list-wrap .task-empty .empty-img {
        width: 70px;
        height: 60px;
        background-image: url(/static/images/workbrench_staff/empty.png);
        background-size: 100% 100%;
        margin: 0 auto 10px auto;
    }

    .task-block-wrap {
        background: #fff;
        margin-bottom: 10px;
    }

    .task-block-wrap .task-block-title {
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: solid 1px #EFEFEF;
    }

    .task-block-wrap .task-block-title .title-txt {
        font-size: 16px;
        font-weight: 700;
    }

    .task-block-wrap .task-block-title .title-link {
        display: flex;
        align-items: center;
        color: #09f;
        position: static;
        cursor: pointer;
    }

    .task-block-wrap .task-block-title .title-link .icon-right {
        margin-left: 5px;
    }

    .task-block-wrap .task-block-title .title-link:hover {
        color: #f60;
    }
</style>

<div class="task-block-wrap">
    <div class="task-block-title">
        <div class="title-txt">CRM 我的待办</div>
        <div class="title-link J-task-view-all">查看全部<span class="vd-icon icon-right"></span></div>
    </div>
    <div class="task-date-wrap">
        <div id="task_date_placeholder"></div>
        <div class="task-list-wrap">
            <div class="J-task-list" style="display: none;">
                <div class="task-list"></div>
                <div class="task-footer">
                    <div class="task-total">共<span class="J-task-day-total"></span>条</div>
                    <div class="task-more J-task-view-day">查看当天全部<i class="vd-icon icon-right"></i></div>
                </div>
            </div>
            <div class="task-empty J-task-empty" style="display: none;">
                <div class="empty-img"></div>
                <div class="empty-txt">无待办信息</div>
            </div>
        </div>
    </div>
</div>

<script src="/static/layui/laydate/laydate.js"></script>

<script>
    $(function() {
        //时间转换
        var timeFormat = {
            incode: function(str) {
                //2025-2-8 => 2025-02-08
                var arr = str.split('-');

                $.each(arr, function(i, item) {
                    if(parseInt(item) < 10) {
                        arr[i] = '0' + item;
                    }
                })

                return arr.join('-');
            },
            decode: function() {
                //2025-02-08 => 2025-2-8
                var arr = str.split('-');

                $.each(arr, function(i, item) {
                    arr[i] = parseInt(item);
                })

                return arr.join('-');
            }
        };

        var firstDate = ''; //监听当前页第一天，判断月份是否有改变，从而触发月份数据请求
        var lastDate = ''; //监听当前页第一天，判断月份是否有改变，从而触发月份数据请求
        var ajaxMonthTimeout = null;
        var ajaxDayTimeout = null;
        var nowDate = '';

        var getMonthTask = function() {
            ajaxMonthTimeout && clearTimeout(ajaxMonthTimeout);

            ajaxMonthTimeout = setTimeout(function() {
                $.ajax({
                    url: '/orderstream/saleorder/getCrmTaskGroupCount.do',
                    data: {
                        startDate: timeFormat.incode(firstDate),
                        endDate: timeFormat.incode(lastDate)
                    },
                    dataType: 'json',
                    success: function(res) {
                        if(res.code === 0) {
                            $('#layui-laydate1').find('.layui-laydate-content tbody td').each(function(i, td) {
                                var itemDate = timeFormat.incode($(this).attr('lay-ymd') || '');
                                var $td = $(td);
                                
                                $.each(res.data, function(j, item) {
                                    if(item.dateStr === itemDate) {
                                        if(item.undoneCount) {
                                            $td.append('<div class="task-tip tip-orange"></div>')
                                        } else if (item.doneCount || item.closeCount) {
                                            $td.append('<div class="task-tip tip-grey"></div>')
                                        }
                                    }
                                })
                            })
                        }
                    }
                })
            }, 300)
        };

        var getDayTask = function(day) {
            ajaxDayTimeout && clearTimeout(ajaxDayTimeout);

            ajaxDayTimeout = setTimeout(function() {
                $.ajax({
                    url: '/orderstream/saleorder/getCrmTaskForOneDay.do',
                    data: {
                        dateStr: day,
                    },
                    dataType: 'json',
                    success: function(res) {
                        if(res.code === 0) {
                            if(res.data && res.data.length) {
                                $('.J-task-list').show();
                                $('.J-task-empty').hide();
    
                                $('.J-task-list .task-list').empty();
    
                                $.each(res.data, function(i, item) {
                                    if(i < 6) {
                                        $('.J-task-list .task-list').append('<div class="task-item J-task-item item-status-' + item.doneStatus + '" data-id="' + item.taskItemId + '" data-bizno="' + item.bizNo + '">' + item.taskContent.replace(/\n/g, ' ') + '</div>')
                                    }
                                })
    
                                $('.J-task-day-total').html(res.data.length);
                            } else {
                                $('.J-task-list').hide();
                                $('.J-task-empty').show();
                            }
                        }
                    }
                })
            }, 300)
        };

        laydate.render({
            elem: '#task_date_placeholder',
            position: 'static',
            // showBottom: false,
            weekStart: 1,
            format: 'yyyy-MM-dd',
            isPreview: false,
            btns: ['now'],
            ready: function(date, a){
                firstDate = $('#layui-laydate1').find('.layui-laydate-content tbody td').first().attr('lay-ymd');
                lastDate = $('#layui-laydate1').find('.layui-laydate-content tbody td').last().attr('lay-ymd');
                nowDate = timeFormat.incode(date.year + '-' + date.month + '-' + date.date);
                getDayTask(nowDate);
                getMonthTask();

                $('#task_date_placeholder .laydate-btns-now').html('今天')
                $('#task_date_placeholder .laydate-btns-now').click(function() {
                    setTimeout(function(){
                        nowDate = timeFormat.incode(date.year + '-' + date.month + '-' + date.date);
                        getDayTask(nowDate);
                        getMonthTask();
                    }, 200)
                })
            },
            change: function(value, date){ //日期被切换的回调
                console.log('aaaaaaa')
                var nowfirstDate = $('#layui-laydate1').find('.layui-laydate-content tbody td').first().attr('lay-ymd');
                var nowlastDate = $('#layui-laydate1').find('.layui-laydate-content tbody td').last().attr('lay-ymd');

                firstDate = nowfirstDate;
                lastDate = nowlastDate;
                nowDate = value;
                getDayTask(value);
                getMonthTask();
            }
        });

        var openLink = function(link, params) {
            if (window.parent != window) {
                if (window.parent.closableTab) {
                    var item = {
                        'id': params.id ? params.id : new Date().getTime(),
                        'name': params.name,
                        'url': link,
                        'closable': params.noclose ? false : true
                    };

                    window.parent.closableTab.addTab(item);
                    window.parent.closableTab.resizeMove();
                }
            } else {
                window.open(link);
            }
        };

        var crmTaskUrl = $('.J-crm-task-url').val();

        //任务详情
        $('.J-task-list').on('click', '.J-task-item', function() {
            var url =  crmTaskUrl + '&taskItemId=' + $(this).data('id') + '&businessNo=' + $(this).data('bizno') + '&endDate=' + nowDate + '&noStatus=1';
            openLink(url, {
                name: '我的任务'
            })
        });

        //当天任务
        $('.J-task-view-day').on('click', function() {
            var url =  crmTaskUrl + '&endDate=' + nowDate + '&noStatus=1';
            openLink(url, {
                name: '我的任务'
            })
        });

        //所有任务
        $('.J-task-view-all').on('click', function() {
            openLink(crmTaskUrl, {
                name: '我的任务',
                id: 'crm_task_mine'
            })
        });
    })
</script>