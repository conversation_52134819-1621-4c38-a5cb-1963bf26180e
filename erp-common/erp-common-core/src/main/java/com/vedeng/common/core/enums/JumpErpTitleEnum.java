package com.vedeng.common.core.enums;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/8/17
 */
public enum JumpErpTitleEnum {
    // 任务列表
    TASK_LIST(1, "任务列表"),
    // 商机详情
    BUSSINESS_CHANCE_DETAIL(2, "商机详情"),
    /**
     * 拜访计划详情
     */
    VISIT_RECORD_DETAIL(3,"拜访计划详情"),

    BUSSINESS_LEAD_DETAIL(4, "线索详情");


    private Integer code;

    private String title;

    JumpErpTitleEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
