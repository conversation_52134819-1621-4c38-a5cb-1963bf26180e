package com.vedeng.aftersales.component.impl;

import com.vedeng.aftersales.component.AbstractAfterSalesOrderServiceOfSaleOrder;
import com.vedeng.aftersales.component.dto.*;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.dao.RInvoiceJInvoiceMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.RInvoiceJInvoice;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.Invoice;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2021/6/21 16 00
 * @Description: 销售订单丢票类型售后单实现类
 */
@Service
public class LostTicketAfterSalesOfSaleOrderService extends AbstractAfterSalesOrderServiceOfSaleOrder {

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Resource
    private RInvoiceJInvoiceMapper rInvoiceJInvoiceMapper;

    @Resource
    private SaleorderMapper saleOrderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;


    @Override
    protected void customerAfterSaleOrderCreateValidate(Object addDto) throws AfterSaleValidateException {

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;
        addTicketValide(afterSaleOrderAddDto);

    }

    public void addTicketValide(AfterSaleOrderAddDto afterSaleOrderAddDto) throws AfterSaleValidateException {

        // 售后原因不能为空
        Integer afterSalesReason = afterSaleOrderAddDto.getAfterSalesReason();
        if(afterSalesReason == null){
            throw new AfterSaleValidateException("售后原因不能为空");
        }

        // 发票不能为空
        List<InvoiceAddDto> useInvoiceList = afterSaleOrderAddDto.getInvoiceList();
        if(CollectionUtils.isEmpty(useInvoiceList)){
            throw new AfterSaleValidateException("所选的售后发票为空");
        }
        List<InvoiceAddDto> validInvoiceList = useInvoiceList.stream().filter(item -> item != null && !ObjectUtils.isEmpty(item.getInvoiceId())).collect(Collectors.toList());
        if(validInvoiceList.size() == 0){
            throw new AfterSaleValidateException("所选的售后发票信息(发票ID)缺失");
        }

        // 发票数据校验
        // 能否创建售后单的销售单校验
        String relatedOrderNo = afterSaleOrderAddDto.getSaleOrderNo();
        if(StringUtils.isEmpty(relatedOrderNo)){
            throw new AfterSaleValidateException("销售单号不能为空");
        }
        // 销售单号查询完整销售单信息
        Saleorder saleOrderLack = new Saleorder();
        saleOrderLack.setSaleorderNo(relatedOrderNo);
        Saleorder saleOrderAll = saleOrderMapper.getSaleorder(saleOrderLack);
        if(saleOrderAll == null || saleOrderAll.getSaleorderId() == null){
            throw new AfterSaleValidateException("查询不到该销售单");
        }
        List<SaleorderGoods> saleOrderGoodsList = saleorderGoodsMapper.selectSaleorderGoodsListBySaleorderId(saleOrderAll);
        if(CollectionUtils.isEmpty(saleOrderGoodsList)){
            throw new AfterSaleValidateException("该销售单下没有商品，无法发生售后");
        }
        // 查询该销售单下SKU所有发票
        Invoice invoice = new Invoice();
        invoice.setCompanyId(saleOrderAll.getCompanyId());
        // 销售开票 & 售后开票
        List<Integer> typeList = new ArrayList<>();
        typeList.add(504);
        typeList.add(505);
        // 查询销售单SKU关联的 销售发票 & 售后发票
        List<Invoice> invoiceList = invoiceMapper.selectInvoiceListByParam(invoice,saleOrderGoodsList,typeList);
        if (CollectionUtils.isEmpty(invoiceList)) {
            throw new AfterSaleValidateException("该销售单下暂无发票");
        }
        // 删选掉已经发生售后的发票-剩余未售后完或者未售后的销售发票
        Iterator<Invoice> invoiceIterator = invoiceList.iterator();
        while (invoiceIterator.hasNext()) {
            Invoice asi = invoiceIterator.next();
            // 查看发票 关联表（销售&售后）发生售后退票(红票)
            List<RInvoiceJInvoice> rInvoiceJInvoiceList = rInvoiceJInvoiceMapper
                    .getRInvoiceJInvoiceList(asi.getInvoiceId());
            if (CollectionUtils.isEmpty(rInvoiceJInvoiceList)) {
                continue;
            }
            // 查询销售发票 和 售后发票差值 （可退票金额） 大于0才可以退票
            BigDecimal amount = rInvoiceJInvoiceMapper.getAfterInvoiceTotalAmount(asi.getInvoiceId());
            if (amount.compareTo(new BigDecimal(0)) <= 0) {
                invoiceIterator.remove();
            } else {
                asi.setAmount(amount);
            }
        }
        // 删选后发票集合
        if (CollectionUtils.isEmpty(invoiceList)) {
            throw new AfterSaleValidateException("该销售单下暂无有效发票");
        }

        // 创建退票售后单里发票是否有效
        List<Integer> invalidInvoiceIdList = invoiceList.stream().map(item -> item.getInvoiceId()).collect(Collectors.toList());
        List<Integer> toValidInvoiceIdList = validInvoiceList.stream().map(item -> item.getInvoiceId()).collect(Collectors.toList());
        boolean allValid = invalidInvoiceIdList.containsAll(toValidInvoiceIdList);
        if(!allValid){
            throw new AfterSaleValidateException("创建售后单中存在无效发票");
        }

        // 进行中退票售后单
        List<Integer> ingAfterSaleOrderList = invoiceMapper.selectIngAfterSaleOrderByInvoiceIdList(invalidInvoiceIdList,afterSaleOrderAddDto);
        for (InvoiceAddDto item : validInvoiceList) {
            boolean contains = ingAfterSaleOrderList.contains(item.getInvoiceId());
            if (contains) {
                throw new AfterSaleValidateException("创建售后单的发票集合里存在正在售后发票");
            }
        }
    }

    @Override
    protected <T extends AbstractAfterSaleOrderDto> void customerAddAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderAddDto) {

    }

    @Override
    protected void customerAddAfterSaleGood(AfterSaleOrderAddDto afterSaleOrderAddDto) {

    }

    @Override
    protected void customerPropertySet(AfterSaleOrderAddDto afterSaleOrderAddDto, AfterSales afterSales) {

    }


    @Override
    protected void customeAddDealOfSaleOrder(Object adddDto) throws Exception {

        // 添加售后退票表信息
        addTicket(adddDto);
    }

    @Override
    public void customeModifyDealOfSaleOrder(Object modifyDto) throws AfterSaleValidateException {

        // 修改发票信息
        modifyTicket(modifyDto);
    }

    @Override
    protected void customerAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) {

    }

    @Override
    protected void customerAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;
        modifyTicketValide(afterSaleOrderModifyDto);

    }

    public void modifyTicketValide(AfterSaleOrderModifyDto afterSaleOrderModifyDto) throws AfterSaleValidateException {

        // 发票不能为空
        List<InvoiceAddDto> useInvoiceList = afterSaleOrderModifyDto.getInvoiceList();
        if(CollectionUtils.isEmpty(useInvoiceList)){
            throw new AfterSaleValidateException("所选的售后发票为空");
        }
        List<InvoiceAddDto> validInvoiceList = useInvoiceList.stream().filter(item -> item != null && !ObjectUtils.isEmpty(item.getInvoiceId())).collect(Collectors.toList());
        if(validInvoiceList.size() == 0){
            throw new AfterSaleValidateException("所选的售后发票信息(发票ID)缺失");
        }

    }
}
