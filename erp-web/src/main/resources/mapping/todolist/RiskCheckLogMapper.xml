<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.todolist.dao.RiskCheckLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.todolist.model.RiskCheckLog">
    <id column="RISK_CHECK_LOG_ID" jdbcType="INTEGER" property="riskCheckLogId" />
    <result column="TODO_LIST_ID" jdbcType="INTEGER" property="todoListId" />
    <result column="RISK_CHECK_BUZ_TYPE" jdbcType="INTEGER" property="riskCheckBuzType" />
    <result column="RISK_CHECK_BUZ_ID" jdbcType="INTEGER" property="riskCheckBuzId" />
    <result column="RISK_CHECK_BUZ_EXTRA" jdbcType="VARCHAR" property="riskCheckBuzExtra" />
    <result column="RISK_CHECK_BUZ_PROPERTY" jdbcType="VARCHAR" property="riskCheckBuzProperty" />
    <result column="RISK_CHECK_TRIGGER_STATUS" jdbcType="INTEGER" property="riskCheckTriggerStatus" />
    <result column="RISK_CHECK_TRIGGER_USER" jdbcType="INTEGER" property="riskCheckTriggerUser" />
    <result column="RISK_CHECK_TRIGGER_TIME" jdbcType="BIGINT" property="riskCheckTriggerTime" />
    <result column="RISK_CHECK_TRIGGER_COMMENT" jdbcType="VARCHAR" property="riskCheckTriggerComment" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="DELETE" jdbcType="INTEGER" property="delete" />
  </resultMap>
  <sql id="Base_Column_List">
    RISK_CHECK_LOG_ID, TODO_LIST_ID, RISK_CHECK_BUZ_TYPE, RISK_CHECK_BUZ_ID, RISK_CHECK_BUZ_EXTRA,
    RISK_CHECK_BUZ_PROPERTY, RISK_CHECK_TRIGGER_STATUS, RISK_CHECK_TRIGGER_USER, RISK_CHECK_TRIGGER_TIME, 
    RISK_CHECK_TRIGGER_COMMENT, ADD_TIME, `DELETE`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_RISK_CHECK_LOG
    where RISK_CHECK_LOG_ID = #{riskCheckLogId,jdbcType=INTEGER}
  </select>
  <select id="getUnTriggerOrderOfHasFinished" resultType="java.lang.String">
    SELECT
        L.BUZ_EXTRA
    FROM
        T_TODO_LIST L
    JOIN T_RISK_CHECK_LOG R ON L.ID = R.TODO_LIST_ID
    WHERE
        R.RISK_CHECK_TRIGGER_STATUS = 0 AND R.RISK_CHECK_BUZ_PROPERTY = #{riskCheckTodoListBuzPropertyEnum,jdbcType=VARCHAR}
    GROUP BY
        L.BUZ_EXTRA HAVING SUM(L.`STATUS`) = COUNT(*)
  </select>
    <select id="getRiskCheckLogByBuzExtra" resultType="com.vedeng.todolist.model.RiskCheckLog">
      SELECT * FROM T_RISK_CHECK_LOG WHERE RISK_CHECK_BUZ_EXTRA = #{buzExtra,jdbcType=VARCHAR} AND RISK_CHECK_BUZ_PROPERTY IS NOT NULL LIMIT 1
    </select>
  <select id="getUnTriggerOrderOfHasFinishedByOrderNo" resultType="com.vedeng.todolist.model.RiskCheckLog">
    SELECT
      R.*
    FROM
      T_TODO_LIST L
        JOIN T_RISK_CHECK_LOG R ON L.ID = R.TODO_LIST_ID
    WHERE
      R.RISK_CHECK_TRIGGER_STATUS = 0 AND R.RISK_CHECK_BUZ_EXTRA = #{orderNo,jdbcType=VARCHAR}
    GROUP BY
      L.BUZ_EXTRA HAVING SUM(L.`STATUS`) = COUNT(*)
  </select>
  <select id="getUnTriggerOrderOfUnFinishedByOrderNo" resultType="com.vedeng.todolist.model.RiskCheckLog">
    SELECT
      R.*
    FROM
      T_RISK_CHECK_LOG R
    WHERE
      R.RISK_CHECK_TRIGGER_STATUS = 0 AND R.RISK_CHECK_BUZ_EXTRA = #{orderNo,jdbcType=VARCHAR}
  </select>
  <select id="getUnTriggeredCountByPropertyGroupByBuzExtra" resultType="java.lang.Integer">
    SELECT COUNT(DISTINCT RISK_CHECK_BUZ_EXTRA) FROM T_RISK_CHECK_LOG WHERE RISK_CHECK_BUZ_PROPERTY = #{riskCheckBuzProperty,jdbcType=VARCHAR} AND RISK_CHECK_TRIGGER_STATUS = 0
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_RISK_CHECK_LOG
    where RISK_CHECK_LOG_ID = #{riskCheckLogId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByBuzTypeAndBuzIdAndBuzExtra">
    DELETE FROM T_RISK_CHECK_LOG WHERE RISK_CHECK_BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND RISK_CHECK_BUZ_ID = #{buzId,jdbcType=INTEGER} AND RISK_CHECK_BUZ_EXTRA =
    #{buzExtra,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="RISK_CHECK_LOG_ID" keyProperty="riskCheckLogId" parameterType="com.vedeng.todolist.model.RiskCheckLog" useGeneratedKeys="true">
    insert into T_RISK_CHECK_LOG (TODO_LIST_ID,RISK_CHECK_BUZ_TYPE, RISK_CHECK_BUZ_ID,
      RISK_CHECK_BUZ_EXTRA, RISK_CHECK_BUZ_PROPERTY, 
      RISK_CHECK_TRIGGER_STATUS, RISK_CHECK_TRIGGER_USER, 
      RISK_CHECK_TRIGGER_TIME, RISK_CHECK_TRIGGER_COMMENT, 
      ADD_TIME, `DELETE`)
    values (#{todoListId,jdbcType=INTEGER}, #{riskCheckBuzType,jdbcType=INTEGER}, #{riskCheckBuzId,jdbcType=INTEGER},
      #{riskCheckBuzExtra,jdbcType=VARCHAR}, #{riskCheckBuzProperty,jdbcType=VARCHAR}, 
      #{riskCheckTriggerStatus,jdbcType=INTEGER}, #{riskCheckTriggerUser,jdbcType=INTEGER}, 
      #{riskCheckTriggerTime,jdbcType=BIGINT}, #{riskCheckTriggerComment,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{delete,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="RISK_CHECK_LOG_ID" keyProperty="riskCheckLogId" parameterType="com.vedeng.todolist.model.RiskCheckLog" useGeneratedKeys="true">
    insert into T_RISK_CHECK_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="todoListId != null">
        TODO_LIST_ID,
      </if>
      <if test="riskCheckBuzType != null">
        RISK_CHECK_BUZ_TYPE,
      </if>
      <if test="riskCheckBuzId != null">
        RISK_CHECK_BUZ_ID,
      </if>
      <if test="riskCheckBuzExtra != null">
        RISK_CHECK_BUZ_EXTRA,
      </if>
      <if test="riskCheckBuzProperty != null">
        RISK_CHECK_BUZ_PROPERTY,
      </if>
      <if test="riskCheckTriggerStatus != null">
        RISK_CHECK_TRIGGER_STATUS,
      </if>
      <if test="riskCheckTriggerUser != null">
        RISK_CHECK_TRIGGER_USER,
      </if>
      <if test="riskCheckTriggerTime != null">
        RISK_CHECK_TRIGGER_TIME,
      </if>
      <if test="riskCheckTriggerComment != null">
        RISK_CHECK_TRIGGER_COMMENT,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="delete != null">
        `DELETE`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="todoListId != null">
        #{todoListId,jdbcType=INTEGER},
      </if>
      <if test="riskCheckBuzType != null">
        #{riskCheckBuzType,jdbcType=INTEGER},
      </if>
      <if test="riskCheckBuzId != null">
        #{riskCheckBuzId,jdbcType=INTEGER},
      </if>
      <if test="riskCheckBuzExtra != null">
        #{riskCheckBuzExtra,jdbcType=VARCHAR},
      </if>
      <if test="riskCheckBuzProperty != null">
        #{riskCheckBuzProperty,jdbcType=VARCHAR},
      </if>
      <if test="riskCheckTriggerStatus != null">
        #{riskCheckTriggerStatus,jdbcType=INTEGER},
      </if>
      <if test="riskCheckTriggerUser != null">
        #{riskCheckTriggerUser,jdbcType=INTEGER},
      </if>
      <if test="riskCheckTriggerTime != null">
        #{riskCheckTriggerTime,jdbcType=BIGINT},
      </if>
      <if test="riskCheckTriggerComment != null">
        #{riskCheckTriggerComment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="delete != null">
        #{delete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.todolist.model.RiskCheckLog">
    update T_RISK_CHECK_LOG
    <set>
      <if test="todoListId != null">
        TODO_LIST_ID = #{riskCheckBtodoListIduzType,jdbcType=INTEGER},
      </if>
      <if test="riskCheckBuzType != null">
        RISK_CHECK_BUZ_TYPE = #{riskCheckBuzType,jdbcType=INTEGER},
      </if>
      <if test="riskCheckBuzId != null">
        RISK_CHECK_BUZ_ID = #{riskCheckBuzId,jdbcType=INTEGER},
      </if>
      <if test="riskCheckBuzExtra != null">
        RISK_CHECK_BUZ_EXTRA = #{riskCheckBuzExtra,jdbcType=VARCHAR},
      </if>
      <if test="riskCheckBuzProperty != null">
        RISK_CHECK_BUZ_PROPERTY = #{riskCheckBuzProperty,jdbcType=VARCHAR},
      </if>
      <if test="riskCheckTriggerStatus != null">
        RISK_CHECK_TRIGGER_STATUS = #{riskCheckTriggerStatus,jdbcType=INTEGER},
      </if>
      <if test="riskCheckTriggerUser != null">
        RISK_CHECK_TRIGGER_USER = #{riskCheckTriggerUser,jdbcType=INTEGER},
      </if>
      <if test="riskCheckTriggerTime != null">
        RISK_CHECK_TRIGGER_TIME = #{riskCheckTriggerTime,jdbcType=BIGINT},
      </if>
      <if test="riskCheckTriggerComment != null">
        RISK_CHECK_TRIGGER_COMMENT = #{riskCheckTriggerComment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="delete != null">
        `DELETE` = #{delete,jdbcType=INTEGER},
      </if>
    </set>
    where RISK_CHECK_LOG_ID = #{riskCheckLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.todolist.model.RiskCheckLog">
    update T_RISK_CHECK_LOG
    set  TODO_LIST_ID = #{todoListId,jdbcType=INTEGER},
      RISK_CHECK_BUZ_TYPE = #{riskCheckBuzType,jdbcType=INTEGER},
      RISK_CHECK_BUZ_ID = #{riskCheckBuzId,jdbcType=INTEGER},
      RISK_CHECK_BUZ_EXTRA = #{riskCheckBuzExtra,jdbcType=VARCHAR},
      RISK_CHECK_BUZ_PROPERTY = #{riskCheckBuzProperty,jdbcType=VARCHAR},
      RISK_CHECK_TRIGGER_STATUS = #{riskCheckTriggerStatus,jdbcType=INTEGER},
      RISK_CHECK_TRIGGER_USER = #{riskCheckTriggerUser,jdbcType=INTEGER},
      RISK_CHECK_TRIGGER_TIME = #{riskCheckTriggerTime,jdbcType=BIGINT},
      RISK_CHECK_TRIGGER_COMMENT = #{riskCheckTriggerComment,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      `DELETE` = #{delete,jdbcType=INTEGER}
    where RISK_CHECK_LOG_ID = #{riskCheckLogId,jdbcType=INTEGER}
  </update>
  <update id="finishTriggerOfRiskCheckLog">
    update T_RISK_CHECK_LOG
    <set>
      <if test="riskCheckTriggerStatus != null">
        RISK_CHECK_TRIGGER_STATUS = #{riskCheckTriggerStatus,jdbcType=INTEGER},
      </if>
      <if test="riskCheckTriggerUser != null">
        RISK_CHECK_TRIGGER_USER = #{riskCheckTriggerUser,jdbcType=INTEGER},
      </if>
      <if test="riskCheckTriggerTime != null">
        RISK_CHECK_TRIGGER_TIME = #{riskCheckTriggerTime,jdbcType=BIGINT},
      </if>
      <if test="riskCheckTriggerComment != null">
        RISK_CHECK_TRIGGER_COMMENT = #{riskCheckTriggerComment,jdbcType=VARCHAR},
      </if>
    </set>
    where RISK_CHECK_BUZ_EXTRA = #{riskCheckBuzExtra,jdbcType=VARCHAR} AND RISK_CHECK_TRIGGER_STATUS = 0
  </update>

</mapper>