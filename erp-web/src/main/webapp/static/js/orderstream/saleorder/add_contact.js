$(function() {
    $("#dept span").click(function(){
        checkLogin();
        $("#department").val($(this).text());
    })

    $("#posi span").click(function(){
        checkLogin();
        $("#position").val($(this).text());
    })

    $("#myform").submit(function(){
        checkLogin();
        var traderId = $("#traderId").val();
        var indexId = $("#indexId").val();
        //姓名
        var realName = $("#name").val();
        var realNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.]{2,20}$/;
        var realMessage = "员工姓名不允许使用特殊字符";
        if(realName == ''){
            warnTips("name","员工姓名不允许为空")
            return  false;
        }else{
            delWarnTips("name");
        }
        if(realName.length < 2 || realName.length > 20){
            warnTips("name","姓名最多20个字")
            return false;
        }else{
            delWarnTips("name");
        }
        if(!realNameReg.test(realName)){
            warnTips("name",realMessage)
            return  false;
        }else{
            delWarnTips("name");
        }
        //手机
        var mobile = $("#mobile").val();
        var mobileReg = /^1[3-9]\d{9}$|^$/;
        var mobileMessage = "手机号格式错误";
        if(mobile == ''){
            warnTips("mobile","手机号不可为空")
            return  false;
        }else{
            delWarnTips("mobile");
        }
        if(!mobileReg.test(mobile)){
            warnTips("mobile",mobileMessage)
            return  false;
        }else{
            delWarnTips("mobile");
        }
        //手机2
        var mobile2 = $("#mobile2").val();
        if(!mobileReg.test(mobile2)){
            warnTips("mobile2",mobileMessage)
            return  false;
        }else{
            delWarnTips("mobile2");
        }

        //电话
        var telephone = $("#telephone").val();
        var telephoneReg = /^(\d{3,4}-?)?\d{7,9}(-?\d{2,6})?$|^$/;
        // var regx = /((d{3})|d{3}-)?d{8}/g
        // var regx = /((d{3}|d{3}-)?d{8}) | ((d{4}|d{4}-)?d{7})/
        var regx = /^(0[1,2]{1}\d{1}-?\d{8})|(0[3-9]{1}\d{2}-?\d{7})/
        var telephoneMessage = "座机号码格式错误";

        if(telephone != '' && !regx.test(telephone)){
            warnTips("telephone",telephoneMessage);
            return  false;
        }else{
            delWarnTips("telephone");
        }

        var pattern = new RegExp("[/]");
        // var org = $("input[name='org']:checked").val();
        // if(org == '其他' && $("#department").val() == '' ){
        //     warnTips("department","选择其他时，所在部门不能为空");
        //     return  false;
        // }else{
        //     delWarnTips("department");
        // }
        // if($("#department").val().length > 20 && pattern.test($("#department").val())){
        //     warnTips("department","部门名称不允许使用特殊字符");
        //     return  false;
        // }else{
        //     delWarnTips("department");
        // }
        // if($("#department").val().length > 20){
        //     warnTips("department","部门名称长度不能超过20个汉字");
        //     return  false;
        // }else{
        //     delWarnTips("department");
        // }
        var pos = $("input[name='pos']:checked").val();
        if(pos == '其他' && $("#position").val() == '' ){
            warnTips("position","选择其他时，职位不能为空");
            return  false;
        }else{
            delWarnTips("position");
        }
        if($("#position").val().length > 0 && pattern.test($("#position").val())){
            warnTips("position","职位名称不允许使用特殊字符");
            return  false;
        }else{
            delWarnTips("position");
        }
        if($("#position").val().length > 20){
            warnTips("position","职位名称长度不允许超过20个汉字");
            return  false;
        }else{
            delWarnTips("position");
        }


        // if($("#fax").val() != undefined && $("#fax").val().length > 0 && pattern.test($("#fax").val())){
        //     layer.alert("传真不允许使用特殊字符");
        //     return  false;
        // }
        // if($("#fax").val() != undefined && $("#fax").val().length > 32){
        //     layer.alert("传真不允许超过32个字符");
        //     return  false;
        // }

        //邮箱
        var email = $("#email").val();
        var emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$|^$/;
        var emailMessage = "邮箱格式错误";
        if(email != '' && !emailReg.test(email)){
            warnTips("email",emailMessage);
            return  false;
        }else{
            delWarnTips("email");
        }
        if(email != undefined && email.length > 50){
            warnTips("email","邮箱不允许超过50个字符");
            return  false;
        }else{
            delWarnTips("email");
        }
        var qqReg=/^\d{5,16}$/;
        if($("#qq").val()!='' && !qqReg.test($("#qq").val())){
            warnTips("qq","qq格式不正确");
            return  false;
        }else{
            delWarnTips("qq");
        }
        if($("#qq").val() != undefined && $("#qq").val().length > 20){
            warnTips("qq","qq不允许超过20个字符");
            return  false;
        }else{
            delWarnTips("qq");
        }

        if($("#comments").val()!='' && $("#comments").val().length > 200){
            warnTips("comments","备注长度不能超过200字符");
            return  false;
        }else{
            delWarnTips("comments");
        }

        if($("#mod").val()!="mod"){
            debugger
            var a= $('#myform').serialize();
            $.ajax({
                url:page_url+'/trader/customer/addSaveContact.do',
                data:$('#myform').serialize(),
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data2)
                {
                    if (data2.code == 0) {
                        var dataArr = data2.data.split(',');
                        //根据客户ID获取联系人列表&地址列表
                        $.ajax({
                            async:false,
                            url: page_url + '/orderstream/saleorder/getCustomerContactAndAddress.do',
                            data:{"traderId":traderId},
                            type:"POST",
                            dataType : "json",
                            success:function(data){

                                //jsonStr = JSON.stringify(data.param);
                                if (data.code == 0) {
                                    var contactStr = '<option value="0">请选择联系人</option>';
                                    /*for(var i = 0; i< data.param.contactList.length; i++) {
                                        var isSelected = data.param.contactList[i].traderContactId == dataArr[2] ? 'selected = "selected"' : '';
                                        contactStr += '<option value="' + data.param.contactList[i].traderContactId + '" ' + isSelected + '>' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '</option>';
                                    }*/
                                    var content;
                                    // var val1;
                                    if(indexId == 'webId'){
                                        for(var i = 0; i< data.param.contactList.length; i++) {
                                            var isSelected = data.param.contactList[i].traderContactId == dataArr[2] ? 'selected = "selected"' : '';
                                            contactStr += '<option value="' + data.param.contactList[i].traderContactId + '"' + isSelected + '>' + data.param.contactList[i].name + '|' + data.param.contactList[i].mobile + '</option>';
                                        }
                                        $("#traderContactId", window.parent.document).html(contactStr);
                                    } else {
                                        for(var i = 0; i< data.param.contactList.length; i++) {
                                            // var isSelected = data.param.contactList[i].traderContactId == dataArr[2] ? 'selected = "selected"' : '';
                                            contactStr += '<option value="' + data.param.contactList[i].traderContactId + '">' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '</option>';
                                            if (data.param.contactList[i].traderContactId == dataArr[2]){
                                                content = data.param.contactList[i].traderContactId;
                                            }
                                        }

                                        var contact="#trader_contact_" + indexId;
                                        var val1 = $("#trader_contact_1 > option:selected" , window.parent.document).val();
                                        var val2 = $("#trader_contact_2 > option:selected" , window.parent.document).val();
                                        var val3 = $("#trader_contact_3 > option:selected" , window.parent.document).val();

                                        $("#trader_contact_1", window.parent.document).html(contactStr);

                                        $("#trader_contact_2", window.parent.document).html(contactStr);

                                        $("#trader_contact_3", window.parent.document).html(contactStr);

                                        if ("#trader_contact_1" == contact ) {
                                            $('#trader_contact_1 > option', window.parent.document).each(function () {
                                                if($(this).val()==content){
                                                    $(this).attr('selected', true);
                                                    window.parent.setContentSelect(content,contact)
                                                }
                                            });
                                        }else {
                                            $('#trader_contact_1 > option', window.parent.document).each(function () {
                                                if($(this).val()==val1){
                                                    $(this).attr('selected', true);
                                                }
                                            });
                                        }

                                        if ("#trader_contact_2" == contact ) {
                                            $('#trader_contact_2 > option', window.parent.document).each(function () {
                                                if ($(this).val() == content) {
                                                    $(this).attr('selected', true);
                                                    window.parent.setContentSelect(content,contact)
                                                }
                                            });
                                        }else {
                                            $('#trader_contact_2 > option', window.parent.document).each(function () {
                                                if($(this).val()==val2){
                                                    $(this).attr('selected', true);
                                                }
                                            });
                                        }

                                        if ("#trader_contact_3" == contact ) {
                                            $('#trader_contact_3 > option', window.parent.document).each(function () {
                                                var a = $(this).val();
                                                if ($(this).val() == content) {
                                                    $(this).attr('selected', true);
                                                    window.parent.setContentSelect(content,contact)

                                                }
                                            });
                                        }else {
                                            $('#trader_contact_3 > option', window.parent.document).each(function () {
                                                if($(this).val()==val3){
                                                    $(this).attr('selected', true);
                                                }
                                            });
                                        }
                                    }

                                    if(parent.layer!=undefined){
                                        if (window.parent.updateTrader) {
                                            window.parent.updateTrader()
                                        }
                                        parent.layer.close(index);
                                    }
                                } else {
                                    layer.alert(data.message,{ icon: 2 });
                                }
                            },
                            error:function(data){
                                if(data.status ==1001){
                                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                                }
                            }
                        });
                    } else {
                        layer.alert(data2.message,{ icon: 2 });
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
            return false;
        }

    })

});
