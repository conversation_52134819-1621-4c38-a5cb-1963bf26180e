package com.vedeng.workbench.model.vo;

import com.vedeng.workbench.model.vo.base.WorkbenchOverview;
import com.vedeng.workbench.model.vo.base.WorkbenchUserInfo;
import lombok.Data;

/**
 * 商机工作台基本返回对象.
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 7:45 下午.
 * @author: Tomcat.Hui.
 */
@Data
public class WorkbenchResponse {


    private WorkbenchUserInfo currentWorkbenchUserInfo;

    private WorkbenchOverview resultData;


}
