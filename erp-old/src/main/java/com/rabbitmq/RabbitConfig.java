package com.rabbitmq;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfig {

    private final Logger log = LoggerFactory.getLogger(this.getClass());
 
//    @Value("${mjx.rabbitmq.host}")
//    private String host;
//
////    @Value("${mjx.rabbitmq.port}")
////    private int port;
//
//    @Value("${mjx.rabbitmq.username}")
//    private String username;
//
//    @Value("${mjx.rabbitmq.password}")
//    private String password;
//
//    @Value("${mjx.rabbitmq.virtualHost}")
//    private String virtualHost;

    // afterSalesOrderExchange
    public static final String AFTER_SALES_ORDER_EXCHANGE = "afterSalesOrderExchange";
    public static final String ERP_CONSUMER_AFTER_SALES_ORDER_IS_SUCCESS_ROUTING_KEY = "erpConsumeAfterSalesOrderIsSuccessRoutingKey";


    //exchange
    public static final String MJX_ADDLOGISTICS_EXCHANGE = "MjxLogisticsExchange";
    public static final String STOCK_SERVICE_EXCHANGE = "StockServiceExchange";
    public static final String MARKET_RETURNCOUPON_EXCHANGE = "returnCouponExchange";
    public static final String ERP_USER_SERVICE_EXCHANGE = "ERPUserServiceExchange";
    public static final String ERP_TRADER_SERVICE_EXCHANGE = "ERPTraderServiceExchange";
    public static final String TRADER_NATURE_EXCHANGE = "trader_nature_exchange";
    public static final String TRADER_APTITUDE_STATUS_EXCHANGE = "trader_aptitude_status_exchange";
    public static final String TRADER_BELONG_PLATFORM_EXCHANGE = "trader_belongplatform_exchange";
    public static final String TRADER_LINK_ACCOUNT_EXCHANGE = "trader_link_account_exchange";
    public static final String CUSTOMER_LINK_ACCOUNT_EXCHANGE = "customer_link_account_exchange";
    public static final String PUBLIC_CUSTOMER_LOCK_EXCHANGE = "publicCustomerLockExchange";
    public static final String SKU_INFO_CHANGED_EXCHANGE = "skuInfoChangedExchange";
    public static final String ONLINE_INVOICING_EXCHANGE = "onLineInvoicingExchange";
    public static final String SIGNED_FOR_EXCHANGE = "signedForExchange";

    /**
     * 自动注册交换机
     */
    public static final String AUTOMATICALLY_REGISTER_EXCHANGE = "AutomaticallyRegisterExchange";

    //queue
    public static final String MJX_ADDLOGISTICS_QUEUE = "MjxLogisticsQueue";
    public static final String ERP_TEXT_QUEUE = "erp.text.Queue";
    public static final String STOCK_SERVICE_OCCUPY_QUEUE = "StockServiceOccupyQueue";
    public static final String STOCK_SERVICE_STOCKNUM_QUEUE = "StockServiceStockNumQueue";
    public static final String ACTION_CHANGE_MSG_QUEUE = "ActionChangeMsgQueue";
    public static final String INVENTORY_TRANSFER_QUEUE = "InventoryTransferQueue";
    public static final String SKU_INFO_CHANGED_QUENE = "skuInfoChangedQueue";

    public static final String MARKET_RETURNCOUPON_QUEUE = "returnCouponQueue";
    /**
     * 自动注册队列
     */
    public static final String AUTOMATIC_REGISTRATION_MSG_QUEUE = "AutomaticRegistrationMsgQueue";

    public static final String MARKET_RETURNCOUPON_ROUTINGKEY = "returnCouponRoutingKey";

    //routingkey
    public static final String MJX_ADDLOGISTICS_ROUTINGKEY = "MjxLogisticsRoutingkey";
    public static final String TEXT_ERP_ROUTINGKEY ="erp.text.routingkey";
    public static final String STOCK_SERVICE_OCCUPY_ROUTINGKEY = "StockServiceOccupyRoutingkey";
    public static final String STOCK_SERVICE_STOCKNUM_ROUTINGKEY = "StockServiceStockNumRoutingkey";
    public static final String ERP_USER_ROUTINGKEY = "ERPUserRoutingkey";
    public static final String USER_ORG_CHANGE_ROUTINGKEY = "UserOrgRoutingkey";
    public static final String ERP_TRADER_ROUTINGKEY = "ERPTraderRoutingkey";
    public static final String PUBLIC_CUSTOMER_LOCK_ROUTINGKEY = "publicCustomerLockRoutingKey";
    public static final String SKU_INFO_CHANGED_KEY = "skuInfoChangedKey";
    public static final String ONLINE_INVOICING_KEY = "onLineInvoiceOpenResultKey";
    /**
     * 自动注册路由key
     */
    public static final String AUTOMATICALLY_REGISTER_ROUTINGKEY = "AutomaticallyRegisterRoutingkey";

    /**
     * 集采订单状态变化MQ
     */
    public static final String JC_ORDER_STATUS_CHANGE_EXCHANGE = "jcOrderChangeExchange";
    public static final String JC_ORDER_STATUS_CHANGE_ROUTING_KEY = "jcOrderChangeKey";

    public static final String JC_ERP_ORG_EXCHANGE="jcErpOrgExchange";
    public static final String JC_ERP_ORG_ROUTINGKEY ="jcErpOrgRoutingKey";

    //解绑
    public static final String JCERPUNBOUNDROUTINGKEY ="jcErpUnboundRoutingkey";

    public static final String HC_ORDER_ARRIVE_EXCHANGE = "hcOrderArriveExchange";

    public static final String BUYORDER_ENABLE_RECEIVE_LOCK_EXCHANGE = "buyOrderEnableReceiveLockExchange";
    public static final String BUYORDER_ENABLER_ECEIVE_LOCK_ROUTINGKEY = "buyOrderEnableReceiveLockRoutingKey";

    public static final String AFTER_SALES_CLIENT_STATUS_EXCHANGE = "afterSalesOrderExchange";
    public static final String AFTER_SALES_CLIENT_STATUS_ROUTINGKEY = "erpCreateAfterSalesOrderFlowToBdRoutingKey";

    public static final String PUSH_AFTERSALES_TO_FRONTMALL_EXCHANGE = "afterSalesOrderExchange";
    public static final String PUSH_AFTERSALES_TO_FRONTMALL_ROUTINGKEY = "erpChangAfterSalesOrderToBdRoutingKey";

}