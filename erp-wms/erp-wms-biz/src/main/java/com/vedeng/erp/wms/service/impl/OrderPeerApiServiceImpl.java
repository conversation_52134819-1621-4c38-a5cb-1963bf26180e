package com.vedeng.erp.wms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.wms.service.OrderPeerApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/22 13:47
 **/
@Service
@Slf4j
public class OrderPeerApiServiceImpl implements OrderPeerApiService {

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Autowired
    private CapitalBillApiService capitalBillApiService;

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;


    @Value("${invoice_goods_orderType}")
    private String invoiceGoodsOrderType;

    @Value("${orderIsGoodsPeer_Time}")
    protected Long orderIsGoodsPeerTime;


    @Override
    public boolean getOrderIsGoodsPeer(Integer saleorderId) {

        log.info("订单是否票货同行判断开始，订单id：{}",saleorderId);
        if(saleorderId == null){
            return false;
        }
        SaleorderInfoDto bySaleOrderId = saleOrderApiService.getBySaleOrderId(saleorderId);
        //“票货同行”条件：
        //（1）HC或ZXF订单
        //（2）开票方式为电子发票 或者 数电
        //（3）订单中全部商品的的发货方式为“普发”
        // (4) 无退票,退货售后单
        // (5)“发票是否寄送”字段为：寄送
        // (6)“货票地址是否相同”字段为“相同”
        // 专票对公收款
        // 没有申请方式非票货同行的已审核的开票申请

        List<Integer> invoiceGoodsOrderTypeList = JSON.parseArray(invoiceGoodsOrderType, Integer.class);
        if(bySaleOrderId != null) {
            if (!invoiceGoodsOrderTypeList.contains(bySaleOrderId.getOrderType()) || bySaleOrderId.getIsSendInvoice() == 0
                    || bySaleOrderId.getIsSameAddress() == null || bySaleOrderId.getIsSameAddress() == 0 || bySaleOrderId.getAddTime() < orderIsGoodsPeerTime) {
                log.info("订单不满足票货同行条件，订单信息：{}", JSON.toJSONString(bySaleOrderId));
                return false;
            }
        }else{
            return false;
        }
        List<SaleOrderGoodsDetailDto> goodsList = saleOrderGoodsApiService.getSalesOrderGoodsByOrderId(saleorderId);
        if(CollUtil.isNotEmpty(goodsList)) {
            for (SaleOrderGoodsDetailDto saleorderGoods : goodsList) {
                if (saleorderGoods.getDeliveryDirect() != null && saleorderGoods.getDeliveryDirect().equals(1)) {
                    log.info("订单不满足票货同行条件，订单信息：{}", JSON.toJSONString(saleorderGoods));
                    return false;
                }
            }
        }

        List<AfterSalesDto> afterSaleListBySaleId = afterSalesApiService.getAfterSaleListBySaleId(saleorderId);

        if(CollUtil.isNotEmpty(afterSaleListBySaleId)){
            log.info("订单不满足票货同行条件，订单信息：{}", JSON.toJSONString(afterSaleListBySaleId));
            return false;
        }

        //  专票对公收款
        Integer invoiceType = bySaleOrderId.getInvoiceType();
        String traderName = bySaleOrderId.getTraderName();
        BigDecimal realTotalAmount = bySaleOrderId.getRealTotalAmount();
        if (InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType)) {
            BigDecimal publicIncome = capitalBillApiService.getSaleOrderPublicIncome(saleorderId, traderName);
            BigDecimal totalExpenditure = capitalBillApiService.getSaleOrderTotalExpenditure(saleorderId);
            if (publicIncome.subtract(totalExpenditure).compareTo(realTotalAmount) < 0) {
                log.info("订单不满足票货同行条件，订单信息：{},publicIncome:{},totalExpenditure:{}", JSON.toJSONString(bySaleOrderId), JSON.toJSONString(publicIncome), JSON.toJSONString(totalExpenditure));
                return false;
            }
        }

        // 没有申请方式非票货同行的已审核的开票申请
        List<InvoiceApplyDto> applyNoPeerBySalesId = invoiceApplyApiService.getApplyNoPeerBySalesId(saleorderId);
        if (CollUtil.isNotEmpty(applyNoPeerBySalesId)) {
            log.info("订单不满足票货同行条件，订单信息：{}", JSON.toJSONString(applyNoPeerBySalesId));
            return false;
        }

        log.info("订单满足票货同行条件，订单信息：{}", JSON.toJSONString(bySaleOrderId));
        return true;
    }

    @Override
    public boolean checkNoAfterSale(Integer saleOrderId) {
        Integer integer = afterSalesApiService.queryAfterSaleInProgress(saleOrderId);
        return integer==0;
    }
}
