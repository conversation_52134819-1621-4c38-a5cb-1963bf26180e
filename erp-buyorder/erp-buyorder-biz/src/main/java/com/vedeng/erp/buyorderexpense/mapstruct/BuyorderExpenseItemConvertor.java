package com.vedeng.erp.buyorderexpense.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description dto转entity
 * @date 2022/7/12 10:45
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface BuyorderExpenseItemConvertor extends BaseMapStruct<BuyorderExpenseItemEntity, BuyorderExpenseItemDto> {
}
