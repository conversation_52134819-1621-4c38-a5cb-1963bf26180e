<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="销售业绩排行" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/home/<USER>/home_page.js?rnd=${resourceVersionKey}'></script>
<%--<script type="text/javascript" src='<%= basePath %>static/js/saleperformance/sale/sales_five_dept.js?rnd=${resourceVersionKey}'></script>--%>
<div class="main-container">
	<!--  %@ include file="../../homepage/sale/sale_engineer_tag.jsp"% -->
	<input type="hidden" id="accessType_id" value="${accessType}" />
	<input type="hidden" id="sortType" value="${sortType}" />
	<input type="hidden" id="companyId" value="${companyId}" />
	<!-- 上级通过连接查询该userId的五行剑法页面-->
	<input type="hidden" id="others_userId_id" value="${five_userId}" />
	<div style="display:none"> <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('15366165967',349402,1,0,0,263348);"></i> </div>
	<div class="five-elements">
      	<%--<span id="five_sale_span_button" tabtitle='{"num":"sales_five_${five_userId}", "link":"./sales/fiveSales/detailsPage.do?sortType=1&userId=${five_userId}&companyId=${companyId}&userFlag=${userFlag}", "title":"五行剑法"}' href="javascript:void(0);" class="bg-light-blue bt-bg-style addtitle">五行剑法<i class="icon iconfiveelements"></i></span>--%>
      	<%--<span tabtitle='{"num":"sales_index_${five_userId}", "link":"./home/<USER>/index.do?accessType=1", "title":"今日任务"}' href="javascript:void(0);" class="bg-light-blue bt-bg-style addtitle" >今日任务<i class="icon icontodaytask"></i></span>--%>
      	<%--<span tabtitle='{"num":"sales_index_${five_userId}", "link":"./home/<USER>/contact.do", "title":"跨部门联系人"}' href="javascript:void(0);" class="bg-light-blue bt-bg-style addtitle" >跨部门联系人<i class="icon iconlinker"></i></span>--%>
    </div>
    <div class="parts">
    	<br/>
    </div>
	<!-- 部门销售 大致数据展示 -->
	<div class="parts">

		<c:if test="${empty kpiDailyCounts}">
			<div class="title-container">
				<div class="table-title nobor">
					部门本月概况
				</div>
			</div>
			<table class="table">
				<thead>
				<tr>
					<th>综合排名</th>
					<th style="width:60px;">人员</th>
					<th>业绩得分</th>
					<th>业绩排名</th>
					<th>客户得分</th>
					<th>客户排名</th>
					<th>BD客户数得分</th>
					<th>BD客户数排名</th>
					<th>询价转化率得分</th>
					<th>询价转化率排名</th>
					<th>总得分</th>
					<th>昨日综合排名</th>
					<th>上月综合排名</th>
				</tr>
				</thead>
				<tr><td colspan="13">查询无结果</td></tr>
			</table>
		</c:if>



		<c:if test="${!empty kpiDailyCounts}">
				<div class="title-container">
					<div class="table-title nobor">
						<div class="table-title nobor">部门本月概况</div>
					</div>
				</div>
				<table class="table">
					<thead>
					<tr>
						<th>综合排名</th>
						<th style="width:60px;">人员</th>
						<th>业绩得分</th>
						<th>业绩排名</th>
						<th>客户得分</th>
						<th>客户排名</th>
						<th>BD客户数得分</th>
						<th>BD客户数排名</th>
						<th>询价转化率得分</th>
						<th>询价转化率排名</th>
						<th>总得分</th>
						<th>昨日综合排名</th>
						<th>上月综合排名</th>
					</tr>
					</thead>
					<c:if test="${empty kpiDailyCounts }">
						<tr><td colspan="13">查询无结果</td></tr>
					</c:if>
					<c:if test="${!empty kpiDailyCounts }">
						<tbody>
						<c:forEach var="dept" items="${kpiDailyCounts}">
							<tr>
								<td>${dept.totalSort}</td>
								<c:choose>

									<c:when test="${dept.userId == five_userId}">
										<td class="font-blue"><a class="addtitle" href="javascript:void(0);"
																 tabtitle='{"num":"sales_five_pingtai_zhuguan_${dept.userId}","link":"./sales/fiveSales/detailsPage.do?sortType=1&userId=${dept.userId}&companyId=1&userFlag=1", "title":"五行剑法"}'>${dept.userName }</a></td>
									</c:when>
									<c:otherwise>
										<td>${dept.userName }</td>
									</c:otherwise>
								</c:choose>
								<td><fmt:formatNumber type="number" value="${dept.kpiAmountScore}" pattern="0.00" maxFractionDigits="2" /></td>
								<td>${dept.amountSort}</td>
								<td><fmt:formatNumber type="number" value="${dept.customerScore}" pattern="0.00" maxFractionDigits="2" /></td>
								<td>${dept.customerSort}</td>
								<td><fmt:formatNumber type="number" value="${dept.bdCustomerScore}" pattern="0.00" maxFractionDigits="2" /></td>
								<td>${dept.bdCustomerSort}</td>
								<td><fmt:formatNumber type="number" value="${dept.chanceScore}" pattern="0.00" maxFractionDigits="2" /></td>
								<td>${dept.chanceSort}</td>
								<td><fmt:formatNumber type="number" value="${dept.totalScore}" pattern="0.00" maxFractionDigits="2" /></td>
								<td>${dept.yesterdaySort}</td>
								<td>${dept.lastmonthSort}</td>
							</tr>
						</c:forEach>
						</tbody>
					</c:if>
				</table>
		</c:if>
	</div>
	
	
</div>
<%@ include file="../../common/footer.jsp"%>