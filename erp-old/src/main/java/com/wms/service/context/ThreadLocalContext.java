package com.wms.service.context;

import java.util.HashMap;
import java.util.Map;

/**
 * 上下文信息 用来保存线程调用过程中一些信息
 */
public class ThreadLocalContext {

    private static ThreadLocal<Map> threadLocalMap = new ThreadLocal(){
        @Override
        protected Map initialValue() {
            return new HashMap<>();
        }
    };


    public static <T,V> void put(T key,V value){
        threadLocalMap.get().put(key,value);
    }

    public static <T,V> V get(T key){
        return (V) threadLocalMap.get().get(key);
    }

    public static <T,V> void remove(T key){
        threadLocalMap.get().remove(key);
    }
    public static <T,V> void removeAll(){
        threadLocalMap.remove();
    }
}
