<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesToYxbMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSalesToYxbEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="AFTER_SALES_TO_YXB_ID" jdbcType="INTEGER" property="afterSalesToYxbId" />
    <result column="INTERFACE_TYPE" jdbcType="INTEGER" property="interfaceType" />
    <result column="SUCCESS" jdbcType="INTEGER" property="success" />
    <result column="AFTER_SALES_ORDER_ID" jdbcType="INTEGER" property="afterSalesOrderId" />
    <result column="AFTER_SALES_ORDER_NO" jdbcType="VARCHAR" property="afterSalesOrderNo" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="PARAM" jdbcType="CHAR" property="param" />
    <result column="RESULT" jdbcType="CHAR" property="result" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from T_AFTER_SALES_TO_YXB
    where AFTER_SALES_TO_YXB_ID = #{afterSalesToYxbId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesToYxbEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into T_AFTER_SALES_TO_YXB (AFTER_SALES_TO_YXB_ID, INTERFACE_TYPE, 
      SUCCESS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO, 
      IS_DELETE, PARAM, `RESULT`, 
      ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME
      )
    values (#{afterSalesToYxbId,jdbcType=INTEGER}, #{interfaceType,jdbcType=INTEGER}, 
      #{success,jdbcType=INTEGER}, #{afterSalesOrderId,jdbcType=INTEGER}, #{afterSalesOrderNo,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=INTEGER}, #{param,jdbcType=CHAR}, #{result,jdbcType=CHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesToYxbEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update T_AFTER_SALES_TO_YXB
    set INTERFACE_TYPE = #{interfaceType,jdbcType=INTEGER},
      SUCCESS = #{success,jdbcType=INTEGER},
      AFTER_SALES_ORDER_ID = #{afterSalesOrderId,jdbcType=INTEGER},
      AFTER_SALES_ORDER_NO = #{afterSalesOrderNo,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      PARAM = #{param,jdbcType=CHAR},
      `RESULT` = #{result,jdbcType=CHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where AFTER_SALES_TO_YXB_ID = #{afterSalesToYxbId,jdbcType=INTEGER}
  </update>
  <update id="updateIsDeleteByAfterSalesOrderIdAndInterfaceTypeInt">
    update T_AFTER_SALES_TO_YXB
    set IS_DELETE = 1
    where AFTER_SALES_ORDER_ID = #{afterSalesOrderId,jdbcType=INTEGER}
      and INTERFACE_TYPE = #{interfaceType,jdbcType=INTEGER}
  </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select AFTER_SALES_TO_YXB_ID, INTERFACE_TYPE, SUCCESS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO, 
    IS_DELETE, PARAM, `RESULT`, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
    from T_AFTER_SALES_TO_YXB
    where AFTER_SALES_TO_YXB_ID = #{afterSalesToYxbId,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select AFTER_SALES_TO_YXB_ID, INTERFACE_TYPE, SUCCESS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO, 
    IS_DELETE, PARAM, `RESULT`, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
    from T_AFTER_SALES_TO_YXB
  </select>
  <select id="selectByCondition" resultType="com.vedeng.erp.aftersale.domain.entity.AfterSalesToYxbEntity">
    select AFTER_SALES_TO_YXB_ID, INTERFACE_TYPE, SUCCESS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO,
           IS_DELETE, PARAM, `RESULT`, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
    from T_AFTER_SALES_TO_YXB
    where 1 = 1
    <if test="interfaceType != null">
      and INTERFACE_TYPE = #{interfaceType,jdbcType=INTEGER}
    </if>
    <if test="success != null">
      and SUCCESS = #{success,jdbcType=INTEGER}
    </if>
    <if test="afterSalesOrderId != null">
      and AFTER_SALES_ORDER_ID = #{afterSalesOrderId,jdbcType=INTEGER}
    </if>
    <if test="afterSalesOrderNo != null">
      and AFTER_SALES_ORDER_NO = #{afterSalesOrderNo,jdbcType=VARCHAR}
    </if>
    <if test="isDelete != null">
      and IS_DELETE = #{isDelete,jdbcType=INTEGER}
    </if>
    <if test="param != null">
      and PARAM = #{param,jdbcType=CHAR}
    </if>
    <if test="result != null">
      and `RESULT` = #{result,jdbcType=CHAR}
    </if>
    <if test="modTime != null">
      and MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="selectByForDownTask"
          resultType="com.vedeng.erp.aftersale.domain.dto.AfterSalesToYxbDto">
    SELECT AFTER_SALES_TO_YXB_ID, INTERFACE_TYPE, SUCCESS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO,
           IS_DELETE, PARAM, `RESULT`, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
    FROM T_AFTER_SALES_TO_YXB t1
    WHERE t1.INTERFACE_TYPE = 0
      AND t1.IS_DELETE = 0
      AND t1.SUCCESS = 1
      AND NOT EXISTS (
            SELECT 1
            FROM T_AFTER_SALES_TO_YXB t2
            WHERE t2.AFTER_SALES_ORDER_ID = t1.AFTER_SALES_ORDER_ID
              AND t2.INTERFACE_TYPE = 2
              AND t2.SUCCESS = 1
      )
     AND NOT EXISTS (
                   SELECT 1
            FROM T_AFTER_SALES_TO_YXB t3
            WHERE t3.AFTER_SALES_ORDER_ID = t1.AFTER_SALES_ORDER_ID
              AND t3.INTERFACE_TYPE = 3
              AND t3.SUCCESS = 1
   )
  </select>
  <select id="selectForPayPushTask" resultType="com.vedeng.erp.aftersale.domain.dto.AfterSalesToYxbDto">
    SELECT AFTER_SALES_TO_YXB_ID, INTERFACE_TYPE, SUCCESS, AFTER_SALES_ORDER_ID, AFTER_SALES_ORDER_NO,
           IS_DELETE, PARAM, `RESULT`, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
    FROM T_AFTER_SALES_TO_YXB t1
    WHERE t1.INTERFACE_TYPE = 0
      AND t1.IS_DELETE = 0
      AND t1.SUCCESS = 1
  </select>
</mapper>