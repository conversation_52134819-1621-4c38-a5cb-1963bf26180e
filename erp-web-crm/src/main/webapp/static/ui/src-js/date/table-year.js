// 年份表格
Vue.component('ui-year-table', {
    template: `
        <table @click="handleYearTableClick" class="vd-ui-year-table">
            <tbody>
                <tr>
                    <td class="available" :class="getCellStyle(startYear + 0)">
                        <a class="cell">{{ startYear }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 1)">
                        <a class="cell">{{ startYear + 1 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 2)">
                        <a class="cell">{{ startYear + 2 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 3)">
                        <a class="cell">{{ startYear + 3 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 4)">
                        <a class="cell">{{ startYear + 4 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 5)">
                        <a class="cell">{{ startYear + 5 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 6)">
                        <a class="cell">{{ startYear + 6 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 7)">
                        <a class="cell">{{ startYear + 7 }}</a>
                    </td>

                    <td class="available" :class="getCellStyle(startYear + 8)">
                        <a class="cell">{{ startYear + 8 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 9)">
                        <a class="cell">{{ startYear + 9 }}</a>
                    </td>
                    <td  class="available" :class="getCellStyle(startYear + 10)">
                        <a class="cell">{{ startYear + 10 }}</a>
                    </td>
                    <td  class="available" :class="getCellStyle(startYear + 11)">
                        <a class="cell">{{ startYear + 11 }}</a>
                    </td>
                </tr>
            </tbody>
        </table>
    `,

    props: {
        disabledDate: {},
        value: {},
        defaultValue: {
            validator(val) {
                return val === null || (val instanceof Date && util_date.isDate(val));
            }
        },
        date: {}
    },

    computed: {
        startYear () {
            return Math.floor(this.date.getFullYear() / 12) * 12;
        }
    },

    methods: {
        datesInYear (year) {
            const numOfDays = util_date.getDayCountOfYear(year);
            const firstDay = new Date(year, 0, 1);
            return util_date.range(numOfDays).map(n => util_date.nextDate(firstDay, n));
        },

        getCellStyle (year) {
            const style = {};
            const today = new Date();

            style.disabled = typeof this.disabledDate === 'function'
                ? this.datesInYear(year).every(this.disabledDate)
                : false;

            style.current = util_date.arrayFindIndex(util_date.coerceTruthyValueToArray(this.value), date => date.getFullYear() === year ) >= 0;
            style.today = today.getFullYear() === year;
            style.default = this.defaultValue && this.defaultValue.getFullYear() === year;

            return style;
        },

        handleYearTableClick (event) {
            const target = event.target;

            if (target.tagName === 'A') {

                if (util_date.hasClass(target.parentNode, 'disabled')) return;

                const year = target.textContent || target.innerText;
                this.$emit('pick', Number(year));
            }
        }
    }
})