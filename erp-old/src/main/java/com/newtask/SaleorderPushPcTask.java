package com.newtask;

import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.orderstrategy.StrategyContext;
import com.vedeng.order.dao.SaleorderGenerateMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *订单实际金额
 * <AUTHOR>
 * @date $
 */
@Component
@JobHandler(value="SaleorderPushPcTask")
public class SaleorderPushPcTask extends AbstractJobHandler {
    private Logger logger = LoggerFactory.getLogger(SaleorderPushPcTask.class);
    @Autowired
    private SaleorderMapper saleorderMapper;
    @Autowired
    SaleorderGenerateMapper saleorderGenerateMapper;



    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("SaleorderPushPcTask start ."+s);
        if(StringUtils.isNotBlank( s)){
            executeHis(s);
        }else{
            pushNewUser();
        }

        return SUCCESS;
    }

    private void executeHis(String s) {
        String saleorderIds[]=StringUtils.split(s,",");
        for(String id:saleorderIds){
            try {
                Integer saleorderId = Integer.parseInt(id);
                StrategyContext strategyContext = new StrategyContext();
                strategyContext.add(StrategyContext.PUSH_PC_STRATEGY, OrderDataUpdateConstant.SALE_ORDER_VAILD);
                strategyContext.executeAll(saleorderId);
                //更新状态

            }catch (Exception e){
                XxlJobLogger.log("executeHis ..............."+id);
            }
        }
    }

    private void pushNewUser( ) {

            XxlJobLogger.log("pushNewUser.updateSaleOrderAmountInfo | end ...............");

    }

}
