package com.wms.service.other;

import com.wms.constant.InventoryAdjustmentStatusEnum;
import com.wms.constant.WMSContant;
import com.wms.dto.InventoryAdjustmentDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 库存调整单下传
 */
@Service
@Slf4j
public class PutInventoryAdjustmentInterface extends AbstractWmsInterface {

    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.PUT_INVENTORY_ADJUSRMENT);
        wmsRequest.setMessageId(WMSContant.PUT_INVENTORY_ADJUSRMENT);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {
        if (param == null || param.length == 0) {
            return null;
        }
        JSONArray headArray = new JSONArray();
        Arrays.stream(param).forEach(paramItem -> {
            InventoryAdjustmentDto inventoryAdjustmentDto = (InventoryAdjustmentDto) paramItem;
            JSONObject headItem = parseTraderToJsonObject(inventoryAdjustmentDto);
            headArray.add(headItem);
        });

        JSONObject headObject = new JSONObject();
        headObject.put("header", headArray);
        return headObject;
    }

    private JSONObject parseTraderToJsonObject(InventoryAdjustmentDto inventoryAdjustmentDto) {
        JSONObject headItem = new JSONObject();
        try {
            headItem.put("WarehouseID", "NJ01");
            headItem.put("CustomerID", "VEDENG");
            headItem.put("ADJNo", inventoryAdjustmentDto.getADJNo());
            headItem.put("status", InventoryAdjustmentStatusEnum.APPROVED.getCode());
        } catch (Exception e) {
            log.error("【parseTraderToJsonObject】处理异常",e);
        }
        return headItem;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
