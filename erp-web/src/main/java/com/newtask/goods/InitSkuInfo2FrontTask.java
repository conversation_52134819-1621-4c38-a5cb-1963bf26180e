package com.newtask.goods;

import cn.hutool.system.SystemUtil;
import com.alibaba.excel.EasyExcel;
import com.newtask.goods.excel.InitSkuInfo2FrontListener;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.goods.common.constant.ErpGoodsConstant;
import com.vedeng.goods.domain.dto.InitSkuInfo2FrontDto;
import com.vedeng.goods.domain.entity.CoreSku;
import com.vedeng.goods.domain.entity.CoreSpu;
import com.vedeng.goods.feign.price.RemotePriceApiService;
import com.vedeng.goods.mapper.CoreSkuMapper;
import com.vedeng.goods.mapper.CoreSpuMapper;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.price.api.price.dto.ResultInfo;
import com.vedeng.price.api.price.dto.price.SkuPriceChangeApplyDto;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoPurchaseDetailDto;
import com.vedeng.price.api.price.dto.price.UpdatePriceChangeAuditorDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 读取excel表中数据（未上架商品数据初始化推送到前台）
 * <AUTHOR>
 */
@Component
@JobHandler(value = "initSkuInfo2FrontTask")
public class InitSkuInfo2FrontTask extends AbstractJobHandler {


    @Autowired
    private RemotePriceApiService remotePriceApiService;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private CoreSpuMapper coreSpuMapper;

    @Autowired
    private VgoodsService vgoodsService;

    private static final String FILE_PATH = SystemUtil.getOsInfo().isLinux() ? "/tmp/initGoodsExcel.xlsx" : "C:/Users/<USER>/Desktop/initGoodsExcel.xlsx";

    private static final String PLATFORM_IDS = "1,2";

    private static final String IS_SUCCESS = "success";

    public static final Logger logger = LoggerFactory.getLogger(InitSkuInfo2FrontTask.class);

    private static final Map<String, Integer> GOODS_LEVEL_NO_MAP = new HashMap<>(4);

    private static final Map<String, Integer> GOODS_POSITION_NO_MAP = new HashMap<>(5);

    static {
        // 加载商品等级
        GOODS_LEVEL_NO_MAP.put("直购商品", 1);
        GOODS_LEVEL_NO_MAP.put("询价商品", 2);
        GOODS_LEVEL_NO_MAP.put("寻货商品", 3);
        GOODS_LEVEL_NO_MAP.put("内部商品", 4);
        // 加载商品档位
        GOODS_POSITION_NO_MAP.put("未签约", 1);
        GOODS_POSITION_NO_MAP.put("代销产品", 2);
        GOODS_POSITION_NO_MAP.put("经销产品", 3);
        GOODS_POSITION_NO_MAP.put("代理产品", 4);
        GOODS_POSITION_NO_MAP.put("独家产品", 5);
    }

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("===================未上架商品数据初始化推送到前台任务开始===================");
        Function<List<InitSkuInfo2FrontDto>, Boolean> updateFunc = initSkuInfo2FrontDtoList -> {
            // 获取skuNo集合
            List<String> skuNoList = initSkuInfo2FrontDtoList.stream().map(InitSkuInfo2FrontDto::getSkuNo).collect(Collectors.toList());
            // 批量更新V_CORE_SKU表信息
            List<CoreSku> updateCoreSkuList = initSkuInfo2FrontDtoList.stream().map(initSkuInfo2FrontDto -> {
                CoreSku coreSku = new CoreSku();
                coreSku.setSkuNo(initSkuInfo2FrontDto.getSkuNo());
                coreSku.setPurchaseTime(Integer.valueOf(StringUtils.substringBefore(initSkuInfo2FrontDto.getPurchaseTime(), "天")));
                coreSku.setIsNeedReport(ErpGoodsConstant.IS_NEED_REPORT_TRUE.equals(initSkuInfo2FrontDto.getIsNeedReport()));
                coreSku.setGoodsLevelNo(GOODS_LEVEL_NO_MAP.get(initSkuInfo2FrontDto.getGoodsLevelNo()));
                coreSku.setCheckStatus(ErpGoodsConstant.CommonNumbers.THREE);
                coreSku.setStatus(true);
                coreSku.setGoodsPositionNo(GOODS_POSITION_NO_MAP.get(initSkuInfo2FrontDto.getGoodsPositionNo()));
                return coreSku;
            }).collect(Collectors.toList());
            int updateCount = coreSkuMapper.updateBatchSelectiveInitSkuInfo2Front(updateCoreSkuList);
            XxlJobLogger.log("更新SKU表:" + updateCount + "条数据");
            // 批量查询coreSku数据
            List<CoreSku> coreSkuList = coreSkuMapper.listBySkuNoList(skuNoList);
            if (CollectionUtils.isNotEmpty(coreSkuList)) {
                // 批量更新V_CORE_SPU的审核状态和启用状态
                List<CoreSpu> coreSpuList = coreSkuList.stream().map(coreSku -> {
                    CoreSpu coreSpu = new CoreSpu();
                    coreSpu.setSpuId(coreSku.getSpuId());
                    coreSpu.setStatus(true);
                    coreSpu.setCheckStatus(ErpGoodsConstant.CommonNumbers.THREE);
                    return coreSpu;
                }).collect(Collectors.toList());
                int spuUpdateCount =  coreSpuMapper.updateStatusAndCheckStatusBySpuIds(coreSpuList);
                XxlJobLogger.log("更新SPU表:" + spuUpdateCount + "条数据");
                // 根据skuId集合 远程调用 批量查询sku_price_change_apply的主键ids
                List<Long> skuIdList = coreSkuList.stream().map(coreSku -> coreSku.getSkuId().longValue()).collect(Collectors.toList());
                UpdatePriceChangeAuditorDto updatePriceChangeAuditorDto = new UpdatePriceChangeAuditorDto();
                updatePriceChangeAuditorDto.setSkuPriceChangeIds(skuIdList);
                ResultInfo<List<SkuPriceInfoPurchaseDetailDto>> listSkuPriceChangeApplyResultInfo = remotePriceApiService.listSkuPriceChangeApplyBySkuIds(updatePriceChangeAuditorDto);
                if (IS_SUCCESS.equals(listSkuPriceChangeApplyResultInfo.getCode()) && CollectionUtils.isNotEmpty(listSkuPriceChangeApplyResultInfo.getData())) {
                    List<Long> skuPriceChangeApplyIds = listSkuPriceChangeApplyResultInfo.getData().stream().map(SkuPriceInfoPurchaseDetailDto::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(skuPriceChangeApplyIds)) {
                        skuPriceChangeApplyIds.forEach(id -> {
                            // 调用价格中心接口商品价格禁用接口
                            SkuPriceChangeApplyDto skuPriceChangeApplyDto = new SkuPriceChangeApplyDto();
                            skuPriceChangeApplyDto.setId(id);
                            skuPriceChangeApplyDto.setDisableReason("其他");
                            ResultInfo resultInfo = remotePriceApiService.disablePrice(skuPriceChangeApplyDto);
                            if (!IS_SUCCESS.equals(resultInfo.getCode())) {
                                XxlJobLogger.log("调用价格中心接口商品价格禁用接口异常:主键id ={}", id);
                            }
                        });
                    }
                }
                // 推送到前台
                coreSkuList.forEach(coreSku -> {
                    if (!vgoodsService.pushSkuInfo(PLATFORM_IDS, coreSku.getSkuId(), coreSku.getSpuId(), "")) {
                        XxlJobLogger.log("推送到前台异常:skuNo={},skuId={},spuId={},", coreSku.getSkuNo(), coreSku.getSkuId(), coreSku.getSpuId());
                    }
                });
            }
            return true;
        };
        InitSkuInfo2FrontListener initSkuInfo2FrontListener = new InitSkuInfo2FrontListener(updateFunc);
        try {
            EasyExcel.read(FILE_PATH, InitSkuInfo2FrontDto.class, initSkuInfo2FrontListener).sheet().doRead();
        } catch (Exception e) {
            logger.error("未上架商品数据初始化推送到前台异常", e);
        }
        XxlJobLogger.log("===================未上架商品数据初始化推送到前台任务结束===================");
        return SUCCESS;
    }

}
