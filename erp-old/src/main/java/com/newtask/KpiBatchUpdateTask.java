package com.newtask;

import com.google.common.base.Splitter;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiGroupConfigDto;
import com.vedeng.kpi.model.DTO.KpiUserConfigDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.service.KpiBaseService;
import com.vedeng.kpi.service.KpiUpdateService;
import com.vedeng.kpi.share.KpiCommonConstant;
import com.vedeng.kpi.share.KpiUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@JobHandler(value="KpiBatchUpdateTask")
/**
 * @description: 指定用户指定日期更新kpi.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/29 2:50 下午.
 * @author: Tomcat.Hui.
 */ 
public class KpiBatchUpdateTask extends AbstractJobHandler {

    @Autowired
    private KpiUpdateService kpiUpdateService;

    @Autowired
    private KpiLoadingCache kpiLoadingCache;

    /**
     * 更新单个用户.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 2:10 下午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @return: boolean.
     * @throw: .
     */
    private boolean updateKpiSingle(KpiUserInfoDto userInfo) throws Exception {
        try {
            XxlJobLogger.log("开始更新 " + userInfo.getUserIdName() + " " +
                    userInfo.getKpiDateEnd().toString() + " 日五行数据");

            if(null == kpiLoadingCache.getUserConfig(userInfo.getUserId())) {
                XxlJobLogger.log("用户不存在");
                return false;
            }
            kpiUpdateService.updateKpiSingle(userInfo);
            XxlJobLogger.log("更新 " + userInfo.getUserId() + userInfo.getKpiDateEnd().toString() +
                    " 日五行数据完成");
            return true;
        } catch (Exception e){
            XxlJobLogger.log("更新 " + userInfo.getUserId() + userInfo.getKpiDateEnd().toString() +
                    " 日五行数据出现异常: ",e);
            throw e;
        }
    }

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("XXL-JOB, KpiSingleUpdateTask. 参数: " + s);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String[] str = Iterables.toArray(Splitter.on(",").split(s),String.class);
        String mode = str[0];

        if (mode.equals("0")) {
            //清空缓存
            kpiLoadingCache.clearAll();
            return SUCCESS;
        }

        Integer id = str.length >= 2 && !Lists.newArrayList("3","4","5").contains(mode) ?
                Integer.parseInt(str[1]) : null;
        String kpiDateStr = str.length == 3 ? str[2] : null;

        if (mode.equals("4")) {
            //4 更新实时数据
            kpiDateStr = sdf.format(KpiUtils.getDateStart(new Date()));
        } else if (mode.equals("5")) {
            //4 更新昨日数据
            kpiDateStr = sdf.format(KpiUtils.getYesterday(new Date()));
        }

        Date endDate = KpiUtils.getDateStart(sdf.parse(kpiDateStr));
        Date startDate = KpiUtils.getMonthStart(sdf.parse(kpiDateStr));


        if (mode.equals("1")) {


            XxlJobLogger.log("开始更新团队 " +
                    kpiLoadingCache.getGroupName(id) + " " + kpiDateStr + " 日" + " 数据");

            /**
             * important
             * 重置团队共享数据
             * important
             * */
            clearGroupThreadData(id,
                    startDate,
                    endDate,
                    KpiCommonConstant.KPI_CUSTOMERS_DAYS_90);

            List<KpiUserInfoDto> list = kpiLoadingCache.getAllUserConfig().stream()
                    .filter(u -> u.getGroupId().equals(id))
                    .map(KpiUserConfigDto::getUserId)
                    .map(userId  -> getKpiUserInfo(userId,startDate,endDate))
                    .collect(Collectors.toList());


            for (KpiUserInfoDto user : list) {
                updateKpiSingle(user);
            }

        } else if (mode.equals("2")){

            XxlJobLogger.log("开始更新单个用户 " + id + " " + kpiDateStr + " 日数据");

            /**
             * important
             * 重置团队共享数据
             * important
             * */
            clearGroupThreadData(kpiLoadingCache.getUserConfig(id).getGroupId(),
                    startDate,
                    endDate,
                    KpiCommonConstant.KPI_CUSTOMERS_DAYS_90);
            kpiLoadingCache.clearAll();

            updateKpiSingle(getKpiUserInfo(id,startDate,endDate));

        } else if (mode.equals("3") || mode.equals("4") || mode.equals("5")) {

            if (mode.equals("5")) {
                //跑昨日数据,需要清空缓存
                kpiLoadingCache.clearAll();
            }

            XxlJobLogger.log("开始更新全量用户 " + kpiDateStr + " 日数据");

            for (KpiGroupConfigDto groupConfig : kpiLoadingCache.getAllGroupConfig()){

                /**
                 * important
                 * 重置团队共享数据
                 * important
                 * */
                clearGroupThreadData(groupConfig.getGroupId(),
                        startDate,endDate,
                        KpiCommonConstant.KPI_CUSTOMERS_DAYS_90);

                List<KpiUserInfoDto> list = kpiLoadingCache.getAllUserConfig().stream()
                        .filter(u -> u.getGroupId().equals(groupConfig.getGroupId()))
                        .map(KpiUserConfigDto::getUserId)
                        .map(userId  -> getKpiUserInfo(userId,startDate,endDate))
                        .collect(Collectors.toList());

                for (KpiUserInfoDto user : list) {
                    updateKpiSingle(user);
                }
            }

        }
        return SUCCESS;
    }

    /**
     * 清空团队共享线程数据.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 切换团队计算时需要重置.
     * @version: 1.0.
     * @date: 2020/7/11 11:54 上午.
     * @author: Tomcat.Hui.
     * @param groupId: .
     * @return: void.
     * @throw: .
     */
    private void clearGroupThreadData(Integer groupId,Date start,Date end,Integer days) {
        KpiDataQueryDto query = new KpiDataQueryDto();
        query.setUserIds(kpiLoadingCache.getGroupUsersByGroupId(groupId)
                .stream().map(KpiUserConfigDto::getUserId).collect(Collectors.toList()));
        query.setGroupId(groupId);
        query.setKpiDateStart(start);
        query.setKpiDateEnd(end);
        query.setKpiDate(end);
        query.setStartMillisecond(start.getTime());
        query.setEndMillisecond(KpiUtils.getDateEnd(end).getTime());
        query.setDays(days);
        KpiBaseService.initThreadParams(query);
    }

    /**
     * 组装对象.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/11 12:05 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param start: .
     * @param end: .
     * @return: com.vedeng.kpi.model.DTO.KpiUserInfoDto.
     * @throw: .
     */
    private KpiUserInfoDto getKpiUserInfo(Integer userId,Date start,Date end) {
        KpiUserInfoDto userInfo = new KpiUserInfoDto();
        userInfo.setUserId(userId);
        userInfo.setUserName(kpiLoadingCache.getUserConfig(userInfo.getUserId()).getUserName());
        userInfo.setKpiDate(end);
        userInfo.setKpiDateEnd(end);
        userInfo.setKpiDateStart(start);
        return userInfo;
    }

}
