<!DOCTYPE html>

<html>

<head>
    <title>CRM-线索</title>
    <link rel="stylesheet" href="./css/common/common.css">
    <link rel="stylesheet" href="./ui/ui.css">
</head>

<body>
    <div class="form-wrap" id="page-container">
        <div style="background:#fff;margin-bottom:20px;border-radius:10px;padding:20px 40px;">

            <div class="flex-box">
                <ui-date-picker
                    v-model="date1"
                    placeholder="请选择日期"
                    @change="handlerDateChange3"
                ></ui-date-picker>
                <ui-date-picker
                    v-model="date3"
                    type="daterange"
                    start-placeholder="起始时间"
                    end-placeholder="截止时间"
                    @change="handlerChange1"
                ></ui-date-picker>
                <ui-date-picker
                    v-model="date4"
                    type="time"
                    placeholder="选择日期时间"
                    @change="handlerDateChange"
                ></ui-date-picker>
                <ui-date-picker
                    v-model="date5"
                    type="datetime"
                    placeholder="选择日期时间"
                    @change="handlerDateChange2"
                ></ui-date-picker>
            </div>
            <div class="flex-box">
                <ui-radio
                    style="margin-right: 200px;"
                    :checked.sync="radio1"
                    @change="handlerChange"
                    label="单选"
                ></ui-radio>
                <ui-radio-group
                    :list="radioList"
                    :value.sync="checkValue1"
                    @change="handlerChange"
                ></ui-radio-group>
            </div>

            <div class="flex-box">
                <ui-checkbox
                    style="margin-right: 200px;"
                    :checked.sync="checked1"
                    @change="handlerChange"
                    label="多选"
                ></ui-checkbox>
                <ui-checkbox-group
                    :list="checkboxList"
                    :onProgress.sync="progress"
                    :values.sync="checkValues2"
                    @change="handlerChange"
                ></ui-checkbox-group>
            </div>

            <div class="flex-box">
                <ui-select
                    :data="selectList1"
                    placeholder="请选择"
                    width="200px"
                    v-model="selectValue1"
                    class="margin"
                    clearable
                ></ui-select>
                <ui-select
                    placeholder="加载中"
                    width="200px"
                    loading
                    class="margin"
                ></ui-select>
            </div>
            <div class="flex-box">
                <ui-select
                    placeholder="请输入"
                    width="250px"
                    class="margin"
                    multiple-type="fixed"
                    @change="selectChange2"
                    :data="selectList2"
                    v-model="selectValue2"
                ></ui-select>
                <ui-select 
                    placeholder="请输入" 
                    width="300px" 
                    class="margin"
                    size="small"
                    remote
                    multiple-type="fixed"
                    :loading="loading2"
                    @search="search"
                    @change="selectChange2"
                    :data="selectList2"
                    v-model="selectValue2"
                    clearable
                ></ui-select>
            </div>

            <div class="flex-box">
                <ui-cascader 
                    class="margin" 
                    :data="cascaderList" 
                    v-model="cascaderValue" 
                    clearable 
                    filterable
                    @change="handleChange"
                ></ui-cascader>

                <ui-cascader 
                    class="margin" 
                    :data="cascaderList" 
                    v-model="cascaderValue2" 
                    clearable 
                    multiple
                    filterable
                    @change="handleChange"
                ></ui-cascader>
            </div>

            <div class="flex-box">
                <ui-tyc
                    placeholder="请准确填写客户名称"
                    @change="changeTyc"
                ></ui-tyc>
            </div>
        </div>


        <div class="form-block">
            <!-- <ui-form-item label="字段1" :must="true">
                <div class="ui-col-5">
                    <ui-input v-model="inputValue" valid="busiForm_inputValue" placeholder="暗注释"></ui-input>
                </div>
            </ui-form-item> -->
            <ui-form-item label="字段2" :must="true">
                <div class="ui-col-5">
                    <ui-select
                        :data="selectList1"
                        placeholder="请选择"
                        valid="busiForm_inputValue1"
                        v-model="inputValue1"
                        class="margin"
                        clearable
                        multiple-type="fixed"
                    ></ui-select>
                </div>
            </ui-form-item>
            <!-- <ui-form-item label="字段3" :must="true">
                <ui-input v-model="inputValue2" valid="busiForm_inputValue2" placeholder="暗注释2"></ui-input>
            </ui-form-item> -->
            <ui-form-item label="字段4" :text="true">
                <div>这是一段静态文本</div>
            </ui-form-item>
        </div>
        <div class="form-btn-wrap">
            <ui-button @click="submit" type="primary">提交</ui-button>
        </div>

        <div class="page">
            <ui-pagination
                :total="listTotal"
                :pageSize="30"
                :pageNo="10"
                type="combine"
                totalTxt="个商品"
            ></ui-pagination>
        </div>

        <div class="page">
            <ui-table :headers="tableHeaders" :list="tableList" :left-fixed="true" :scroll="true" :setting="true"  :right-fixed="true" container-height="200px">
                <template v-slot:option="{ rowData }">
                    <a @click="edit(rowData.accountId)">编辑</a>
                    <a>删除</a>
                </template>
            </ui-table>
        </div>

        
        <ui-button @click="openDialog" type="primary">打开弹窗</ui-button>
        <ui-dialog
            :visible.sync="isShowDialog"
            title="弹框"
        >
            <ui-form-item label="字段1" :must="true">
                <ui-input v-model="inputValue" valid="inputValue" placeholder="暗注释"></ui-input>
            </ui-form-item>
            <div>这是内容1</div>
            <div>这是内容1</div>
            <div slot="footer">
                <ui-button @click="isShowDialog=false" type="primary">确定</ui-button>
                <ui-button @click="isShowDialog=false" class="close">取消</ui-button>
            </div>
        </ui-dialog>

        <!-- <div class="search-wrap">
            <ui-search-container ref="listContainer" list-name="businessleadsList" :default-tab="defaultTab" :headers="tableHeaders" :list="tableList">
                <ui-search-item v-for="item in 8" :label="'标签'+item" :value="inputValue">
                    <ui-input v-model="inputValue" placeholder="暗注释"></ui-input>
                </ui-search-item>
                <template v-slot:list-button>
                    <ui-button @click="assignLeads">分配线索</ui-button>
                    <ui-button>分配线索</ui-button>
                </template>
                <template v-slot:option="{ row }">
                    <div class="option-wrap">
                        <a @click="edit(row.accountId)" class="table-edit">编辑</a>
                        <a class="table-edit">删除</a>
                    </div>
                </template>
            </ui-search-container>
        </div> -->
    </div>
    <script src="./js/common/vue.js"></script>
    <script src="./js/common/axios.js"></script>
    <script src="./js/common/lodash.min.js"></script>
    <script src="./ui/ui.js"></script>
    <script src="./js/common/layout.js"></script>
    <script src="./js/pages/test.js"></script>
</body>

</html>