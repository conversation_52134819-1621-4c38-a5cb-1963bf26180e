package com.vedeng.finance.vo;

import com.vedeng.finance.model.HxInvoice;
import com.vedeng.order.model.Buyorder;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/5/20 9:46
 */
public class HxInvoiceVo extends HxInvoice {

    /**
     * 录票时间
     */
    private Long entryTime;

    private Integer entryUserId;

    private String entryUser;

    private Long validTime;

    private Integer validUserId;

    private String validUser;

    private Integer validStatus;

    private String validComments;

    private Integer authStatus;

    private Long authTime;

    private Integer authMonth;

    /**
     * 金蝶凭证号
     */
    private String financeVoucherNo;

    private Integer sendResult;

    private List<Buyorder> buyorders;

    public List<Buyorder> getBuyorders() {
        return buyorders;
    }

    /**
     * 订单的归属人ID
     */
    private Integer orderUserId;

    /**
     * 已录票金额
     */
    private BigDecimal recordedAmount;

    /**
     * 可录票金额
     */
    private BigDecimal canRecordAmount;

    /**
     * 订单号
     */
    private String saleorderNo;

    /**
     * '发票来自哪儿，1：航信',
     */
    private Integer invoiceFrom;

    /**
     * 发票税率
     */
    private BigDecimal ratio;

    /**
     * 是否可认证
     */
    private Boolean canIdentification;

    /**
     * 录票类型
     */
    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getCanIdentification() {
        return canIdentification;
    }

    public void setCanIdentification(Boolean canIdentification) {
        this.canIdentification = canIdentification;
    }

    public void setBuyorders(List<Buyorder> buyorders) {
        this.buyorders = buyorders;
    }

    public Integer getEntryUserId() {
        return entryUserId;
    }

    public void setEntryUserId(Integer entryUserId) {
        this.entryUserId = entryUserId;
    }

    public Integer getValidUserId() {
        return validUserId;
    }

    public void setValidUserId(Integer validUserId) {
        this.validUserId = validUserId;
    }

    public Long getEntryTime() {
        return entryTime;
    }

    public void setEntryTime(Long entryTime) {
        this.entryTime = entryTime;
    }

    public String getEntryUser() {
        return entryUser;
    }

    public void setEntryUser(String entryUser) {
        this.entryUser = entryUser;
    }

    public Long getValidTime() {
        return validTime;
    }

    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }

    public String getValidUser() {
        return validUser;
    }

    public void setValidUser(String validUser) {
        this.validUser = validUser;
    }

    public Integer getValidStatus() {
        return validStatus;
    }

    public void setValidStatus(Integer validStatus) {
        this.validStatus = validStatus;
    }

    public String getValidComments() {
        return validComments;
    }

    public void setValidComments(String validComments) {
        this.validComments = validComments;
    }

    public Integer getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(Integer authStatus) {
        this.authStatus = authStatus;
    }

    public Long getAuthTime() {
        return authTime;
    }

    public void setAuthTime(Long authTime) {
        this.authTime = authTime;
    }

    public Integer getAuthMonth() {
        return authMonth;
    }

    public void setAuthMonth(Integer authMonth) {
        this.authMonth = authMonth;
    }

    public String getFinanceVoucherNo() {
        return financeVoucherNo;
    }

    public void setFinanceVoucherNo(String financeVoucherNo) {
        this.financeVoucherNo = financeVoucherNo;
    }

    public Integer getSendResult() {
        return sendResult;
    }

    public void setSendResult(Integer sendResult) {
        this.sendResult = sendResult;
    }

    public BigDecimal getRecordedAmount() {
        return recordedAmount;
    }

    public void setRecordedAmount(BigDecimal recordedAmount) {
        this.recordedAmount = recordedAmount;
    }

    public Integer getOrderUserId() {
        return orderUserId;
    }

    public void setOrderUserId(Integer orderUserId) {
        this.orderUserId = orderUserId;
    }

    public String getSaleorderNo() {
        return saleorderNo;
    }

    public void setSaleorderNo(String saleorderNo) {
        this.saleorderNo = saleorderNo;
    }

    public BigDecimal getCanRecordAmount() {
        return canRecordAmount;
    }

    public void setCanRecordAmount(BigDecimal canRecordAmount) {
        this.canRecordAmount = canRecordAmount;
    }

    public Integer getInvoiceFrom() {
        return invoiceFrom;
    }

    public void setInvoiceFrom(Integer invoiceFrom) {
        this.invoiceFrom = invoiceFrom;
    }

    public BigDecimal getRatio() {
        return ratio;
    }

    public void setRatio(BigDecimal ratio) {
        this.ratio = ratio;
    }
}
