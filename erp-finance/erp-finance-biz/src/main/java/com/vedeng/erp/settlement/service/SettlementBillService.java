package com.vedeng.erp.settlement.service;

import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.cmd.SettlementBillDeleteCmd;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.cmd.SettlementBillSettleCmd;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 结算单
 * @date 2023/11/23 14:32
 */
public interface SettlementBillService {

    /**
     * 创建结算单
     *
     * @param settlementBillCreateCmd settlementBillCreateCmd
     * @return 结算单id
     */
    Integer create(SettlementBillCreateCmd settlementBillCreateCmd);

    /**
     * 创建结算单并且结算
     *
     * @param settlementBillCreateCmd settlementBillCreateCmd
     */
    void createAlsoSettlement(SettlementBillCreateCmd settlementBillCreateCmd);

    /**
     * 结算
     * 产生支付动作
     *
     * @param settlementBillSettleCmd settlementBillSettleCmd
     */
    void settlement(SettlementBillSettleCmd settlementBillSettleCmd);

    /**
     * 修改结算单
     *
     * @param settlementBillDto settlementBillDto
     */
    void modify(SettlementBillDto settlementBillDto);

    /**
     * 删除结算单（软删）
     *
     * @param settlementBillDeleteCmd cmd
     */
    void delete(SettlementBillDeleteCmd settlementBillDeleteCmd);

    /**
     * 查询结算单
     *
     * @param settlementBillQueryCmd      settlementBillQueryCmd
     * @return Integer
     */
    Integer getSettlementIdByBusiness(SettlementBillQueryCmd settlementBillQueryCmd);

    /**
     * 查询结算单
     *
     * @param settlementBillQueryCmd      settlementBillQueryCmd
     * @return SettlementBillDto
     */
    SettlementBillDto getSettlementByBusiness(SettlementBillQueryCmd settlementBillQueryCmd);

}
