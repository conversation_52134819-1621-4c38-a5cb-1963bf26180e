package com.vedeng.goods.model.vo;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.model.dto.CoreSkuBaseDTO;
import com.vedeng.system.service.UserService;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

public class CoreSkuBaseVO extends CoreSkuBaseDTO {

	private String onSaleStr;

	public String getOnSaleStr() {
		return onSaleStr;
	}

	public void setOnSaleStr(String onSaleStr) {
		this.onSaleStr = onSaleStr;
	}

	/**
	 * 上下架子状态
	 *
	 * @since ERP_LV_2020_56
	 */
	private String sellingStatusStr;

	/**
	 * 推送平台
	 *
	 * Note:新商品流列表中展示推送平台【未推送、XX/XX/XX（贝登/医械购/科研购）】（未推送则显示未推送，已推送的根据推送的平台名称显示，多个用/分割）
	 * @since ERP_LV_2020_56
	 */
	private String pushedPlatformNames;

	public String getPushedPlatformNames() {
		return pushedPlatformNames;
	}

	public void setPushedPlatformNames(String pushedPlatformNames) {
		this.pushedPlatformNames = pushedPlatformNames;
	}

	public String getSellingStatusStr() {
		return sellingStatusStr;
	}

	public void setSellingStatusStr(String sellingStatusStr) {
		this.sellingStatusStr = sellingStatusStr;
	}

	/**
	 * 是否拥有编辑权限 0 否  1 是
	 */
	private Integer hasEditAuth;

	/**
	 * 商品级别
	 *
	 * @since ERP_LV_2020_105
	 */
	private String goodsLevelName;

	/**
	 * 商品档位
	 *
	 * @since  ERP_LV_2020_105
	 */
	private String goodsPositionName;


	private Integer hasTodoItemsCount;

	public Integer getHasEditAuth() {
		return hasEditAuth;
	}

	public void setHasEditAuth(Integer hasEditAuth) {
		this.hasEditAuth = hasEditAuth;
	}

	public String getIsStockupShow() {
		isStockupShow = NumberUtils.toInt(getIsStockup() + "") > 0 ? "是" : "否";
		return isStockupShow;
	}

	public void setIsStockupShow(String isStockupShow) {
		this.isStockupShow = isStockupShow;
	}

	private String isStockupShow;
	private String checkStatusShow;
	private String modTimeShow;
	private String operateInfoIdShow;

	public String getAssignmentManager() {
		WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
		UserService service=	context.getBean(UserService.class);
		User user=service.getUserById(getAssignmentManagerId());
		assignmentManager=user==null?"":user.getUsername();
		return assignmentManager;
	}

	public void setAssignmentManager(String assignmentManager) {
		this.assignmentManager = assignmentManager;
	}


	public String getAssignmentAssistant() {
		WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
		UserService service=	context.getBean(UserService.class);
		User user=service.getUserById(getAssignmentAssistantId());

		assignmentAssistant=user==null?"":user.getUsername();

		return assignmentAssistant;
	}

	public void setAssignmentAssistant(String assignmentAssistant) {
		this.assignmentAssistant = assignmentAssistant;
	}

	private String assignmentManager;
	private String assignmentAssistant;

	public String getAssignment() {
		String assistant=getAssignmentAssistant();
		String manager=getAssignmentManager();
		StringBuilder result=new StringBuilder();

		if(StringUtils.isNotBlank(manager)){
			result.append("&"+manager);
		}

		if(StringUtils.isNotBlank(assistant)){
		 	result.append("&"+assistant);
		}

		if(StringUtils.isNotBlank(result.toString())){
			return result.substring(1);
		}else{
			return "待完善";
		}
	}

	public void setAssignment(String assignment) {
		this.assignment = assignment;
	}

	private String assignment;

	public String getSkuInfoShow() {
		 if(StringUtils.isNotBlank(getModel())){
		 	return getModel();
		 }
		 return getSpec();
	}

	public void setSkuInfoShow(String skuInfoShow) {
		this.skuInfoShow = skuInfoShow;
	}

	private String skuInfoShow;


	public String getCheckStatusShow() {
		checkStatusShow = GoodsCheckStatusEnum.statusName(getCheckStatus());
		return checkStatusShow;
	}

	public void setCheckStatusShow(String checkStatusShow) {
		this.checkStatusShow = checkStatusShow;
	}

	public String getModTimeShow() {
		if (getModTime() != null) {
			modTimeShow = DateFormatUtils.format(getModTime(), DateUtil.TIME_FORMAT);
		}
		return modTimeShow;
	}

	public void setModTimeShow(String modTimeShow) {
		this.modTimeShow = modTimeShow;
	}

	public String getOperateInfoIdShow() {
		operateInfoIdShow = NumberUtils.toInt(getOperateInfoId() + "") > 0 ? "已添加" : "未添加";
		return operateInfoIdShow;
	}

	public void setOperateInfoIdShow(String operateInfoIdShow) {
		this.operateInfoIdShow = operateInfoIdShow;
	}

	public String getGoodsLevelName() {
		return goodsLevelName;
	}

	public void setGoodsLevelName(String goodsLevelName) {
		this.goodsLevelName = goodsLevelName;
	}

	public String getGoodsPositionName() {
		return goodsPositionName;
	}

	public void setGoodsPositionName(String goodsPositionName) {
		this.goodsPositionName = goodsPositionName;
	}

	public Integer getHasTodoItemsCount() {
		return hasTodoItemsCount;
	}

	public void setHasTodoItemsCount(Integer hasTodoItemsCount) {
		this.hasTodoItemsCount = hasTodoItemsCount;
	}
}
