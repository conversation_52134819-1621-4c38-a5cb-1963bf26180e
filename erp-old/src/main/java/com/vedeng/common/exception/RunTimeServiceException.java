package com.vedeng.common.exception;

/**
 * <b>Description: 运行时业务异常类</b><br>
 * <b>Author: Franlin.wu</b>
 *
 * <br><b>Date: 2018年11月23日 下午12:42:02 </b>
 *
 */
public class RunTimeServiceException extends Exception
{
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = -11254476093277254L;

    /**
     * 业务错误码
     */
    private Integer errorCode;

    /**
     * 业务错误信息
     */
    private String errorMessage;

    public RunTimeServiceException(Integer errorCode, String errorMessage)
    {
        super();
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public RunTimeServiceException(Integer errorCode)
    {
        super();
        this.errorCode = errorCode;
    }

    public RunTimeServiceException()
    {
        super();
    }

    public Integer getErrorCode()
    {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode)
    {
        this.errorCode = errorCode;
    }

    public String getErrorMessage()
    {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage)
    {
        this.errorMessage = errorMessage;
    }




}
