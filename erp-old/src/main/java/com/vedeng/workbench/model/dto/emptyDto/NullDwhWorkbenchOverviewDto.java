package com.vedeng.workbench.model.dto.emptyDto;

import com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto;

import java.math.BigDecimal;


/**
 * .初始化空防错
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/7 20:57.
 * @author: <PERSON><PERSON>.
 */

public class NullDwhWorkbenchOverviewDto extends DwhWorkbenchOverviewDto {


    public NullDwhWorkbenchOverviewDto() {
        this.setAaTotalAmount(BigDecimal.ZERO);
        this.setAaTotalNum(0);
        this.setOrgName("默认");
        this.setSsTotalAmount(BigDecimal.ZERO);
        this.setSsTotalNum(0);
        this.setOrgId(-1);
        this.setStatus(-1);
        this.setTotalBussinessChanceAmount(BigDecimal.ZERO);
        this.setTotalBussinessChanceNum(0);
        this.setTotalExpectAmount(BigDecimal.ZERO);
    }
}
