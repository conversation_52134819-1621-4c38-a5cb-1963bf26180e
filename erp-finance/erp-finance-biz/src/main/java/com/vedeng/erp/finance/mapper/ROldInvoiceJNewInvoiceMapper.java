package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.ROldInvoiceJNewInvoiceEntity;

public interface ROldInvoiceJNewInvoiceMapper {
    int deleteByPrimaryKey(Integer rOldInvoiceJNewInvoiceId);

    int insert(ROldInvoiceJNewInvoiceEntity record);

    int insertSelective(ROldInvoiceJNewInvoiceEntity record);

    ROldInvoiceJNewInvoiceEntity selectByPrimaryKey(Integer rOldInvoiceJNewInvoiceId);

    int updateByPrimaryKeySelective(ROldInvoiceJNewInvoiceEntity record);

    int updateByPrimaryKey(ROldInvoiceJNewInvoiceEntity record);
}