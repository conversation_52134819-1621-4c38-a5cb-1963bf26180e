package com.vedeng.erp.business.web.controller;

import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.business.service.InitializationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @description 线索 商机 表格初始化
 * @date 2022/7/23 16:55
 **/
@Controller
@RequestMapping("/initialization")
@Slf4j
public class InitializationByFileController extends BaseController {

    /**
     * 初始话页面
     * @param type 1 线索 2 商机
     * @return 页面
     */
    @RequestMapping(value = "/upLoadFile")
    public ModelAndView upLoadFile(Integer type) {
        ModelAndView mv = new ModelAndView("vue/view/initialization/upLoadFile");
        mv.addObject("type", type);
        mv.addObject("currentUser", CurrentUser.getCurrentUser().getId());
        mv.addObject("currentUserName", CurrentUser.getCurrentUser().getUsername());
        return mv;
    }


}
