package com.vedeng.erp.saleorder.model.po;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2022-12-06
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class VJdSaleorderFailed extends VJdSaleorder {
    private static final long serialVersionUID = 6906719125697903013L;
    @ExcelProperty(value = "错误消息" , index = 0)
    private String errorMsg;


    public static VJdSaleorderFailed excelAddGoods2Failed(VJdSaleorder vJdSaleorder,String msg) {
        VJdSaleorderFailed failed = new VJdSaleorderFailed();
        failed.setErrorMsg(msg);
        failed.setJdSaleorderNo(vJdSaleorder.getJdSaleorderNo());
        failed.setJdSkuNo(vJdSaleorder.getJdSkuNo());
        failed.setPurchasingPrice(vJdSaleorder.getPurchasingPrice());
        failed.setNum(vJdSaleorder.getNum());
        failed.setArrivalUserName(vJdSaleorder.getArrivalUserName());
        failed.setArrivalUserPhone(vJdSaleorder.getArrivalUserPhone());
        failed.setConsigneeAddress(vJdSaleorder.getConsigneeAddress());
        failed.setProvince(vJdSaleorder.getProvince());
        failed.setCity(vJdSaleorder.getCity());
        failed.setCounty(vJdSaleorder.getCounty());
        failed.setOrderStatus(vJdSaleorder.getOrderStatus());
        return failed;
    }
}
