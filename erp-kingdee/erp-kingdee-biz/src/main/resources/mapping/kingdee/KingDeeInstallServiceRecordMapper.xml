<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeInstallServiceRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_INSTALL_SERVICE_RECORD-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_QZOK_DATE" jdbcType="VARCHAR" property="fQzokDate" />
    <result column="F_QZOK_ORG_ID" jdbcType="VARCHAR" property="fQzokOrgId" />
    <result column="F_QZOK_YSSJ" jdbcType="VARCHAR" property="fQzokYssj" />
    <result column="F_QZOK_QSSJ" jdbcType="VARCHAR" property="fQzokQssj" />
    <result column="F_QZOK_BCFWSL" jdbcType="DECIMAL" property="fQzokBcfwsl" />
    <result column="F_QZOK_YSFS" jdbcType="VARCHAR" property="fQzokYsfs" />
    <result column="F_QZOK_YSJL" jdbcType="VARCHAR" property="fQzokYsjl" />
    <result column="F_QZOK_YSDDH" jdbcType="VARCHAR" property="fQzokYsddh" />
    <result column="F_QZOK_GSYWDH" jdbcType="VARCHAR" property="fQzokGsywdh" />
    <result column="F_QZOK_YWLX" jdbcType="VARCHAR" property="fQzokYwlx" />
    <result column="F_QZOK_WLBM" jdbcType="VARCHAR" property="fQzokWlbm" />
    <result column="F_QZOK_XLH" jdbcType="VARCHAR" property="fQzokXlh" />
    <result column="F_QZOK_FID" jdbcType="VARCHAR" property="fQzokFid" />
    <result column="F_QZOK_FEntryID" jdbcType="VARCHAR" property="fQzokFentryid" />
    <result column="F_QZOK_CKDH" jdbcType="VARCHAR" property="fQzokCkdh" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, F_BILL_NO, 
    F_QZOK_DATE, F_QZOK_ORG_ID, F_QZOK_YSSJ, F_QZOK_QSSJ, F_QZOK_BCFWSL, F_QZOK_YSFS, 
    F_QZOK_YSJL, F_QZOK_YSDDH, F_QZOK_GSYWDH, F_QZOK_YWLX, F_QZOK_WLBM, F_QZOK_XLH, F_QZOK_FID, 
    F_QZOK_FEntryID, F_QZOK_CKDH
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_INSTALL_SERVICE_RECORD
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_INSTALL_SERVICE_RECORD
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_INSTALL_SERVICE_RECORD (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      FID, F_BILL_NO, F_QZOK_DATE, 
      F_QZOK_ORG_ID, F_QZOK_YSSJ, F_QZOK_QSSJ, 
      F_QZOK_BCFWSL, F_QZOK_YSFS, F_QZOK_YSJL, 
      F_QZOK_YSDDH, F_QZOK_GSYWDH, F_QZOK_YWLX, 
      F_QZOK_WLBM, F_QZOK_XLH, F_QZOK_FID, 
      F_QZOK_FEntryID, F_QZOK_CKDH)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fid,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fQzokDate,jdbcType=VARCHAR}, 
      #{fQzokOrgId,jdbcType=VARCHAR}, #{fQzokYssj,jdbcType=VARCHAR}, #{fQzokQssj,jdbcType=VARCHAR}, 
      #{fQzokBcfwsl,jdbcType=DECIMAL}, #{fQzokYsfs,jdbcType=VARCHAR}, #{fQzokYsjl,jdbcType=VARCHAR}, 
      #{fQzokYsddh,jdbcType=VARCHAR}, #{fQzokGsywdh,jdbcType=VARCHAR}, #{fQzokYwlx,jdbcType=VARCHAR}, 
      #{fQzokWlbm,jdbcType=VARCHAR}, #{fQzokXlh,jdbcType=VARCHAR}, #{fQzokFid,jdbcType=VARCHAR}, 
      #{fQzokFentryid,jdbcType=VARCHAR}, #{fQzokCkdh,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_INSTALL_SERVICE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="fid != null and fid != ''">
        FID,
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO,
      </if>
      <if test="fQzokDate != null and fQzokDate != ''">
        F_QZOK_DATE,
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        F_QZOK_ORG_ID,
      </if>
      <if test="fQzokYssj != null and fQzokYssj != ''">
        F_QZOK_YSSJ,
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        F_QZOK_QSSJ,
      </if>
      <if test="fQzokBcfwsl != null">
        F_QZOK_BCFWSL,
      </if>
      <if test="fQzokYsfs != null and fQzokYsfs != ''">
        F_QZOK_YSFS,
      </if>
      <if test="fQzokYsjl != null and fQzokYsjl != ''">
        F_QZOK_YSJL,
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        F_QZOK_YSDDH,
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        F_QZOK_GSYWDH,
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        F_QZOK_YWLX,
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        F_QZOK_WLBM,
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        F_QZOK_XLH,
      </if>
      <if test="fQzokFid != null and fQzokFid != ''">
        F_QZOK_FID,
      </if>
      <if test="fQzokFentryid != null and fQzokFentryid != ''">
        F_QZOK_FEntryID,
      </if>
      <if test="fQzokCkdh != null and fQzokCkdh != ''">
        F_QZOK_CKDH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null and fid != ''">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDate != null and fQzokDate != ''">
        #{fQzokDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYssj != null and fQzokYssj != ''">
        #{fQzokYssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        #{fQzokQssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBcfwsl != null">
        #{fQzokBcfwsl,jdbcType=DECIMAL},
      </if>
      <if test="fQzokYsfs != null and fQzokYsfs != ''">
        #{fQzokYsfs,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsjl != null and fQzokYsjl != ''">
        #{fQzokYsjl,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFid != null and fQzokFid != ''">
        #{fQzokFid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFentryid != null and fQzokFentryid != ''">
        #{fQzokFentryid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCkdh != null and fQzokCkdh != ''">
        #{fQzokCkdh,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity">
    <!--@mbg.generated-->
    update KING_DEE_INSTALL_SERVICE_RECORD
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null and fid != ''">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDate != null and fQzokDate != ''">
        F_QZOK_DATE = #{fQzokDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYssj != null and fQzokYssj != ''">
        F_QZOK_YSSJ = #{fQzokYssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        F_QZOK_QSSJ = #{fQzokQssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBcfwsl != null">
        F_QZOK_BCFWSL = #{fQzokBcfwsl,jdbcType=DECIMAL},
      </if>
      <if test="fQzokYsfs != null and fQzokYsfs != ''">
        F_QZOK_YSFS = #{fQzokYsfs,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsjl != null and fQzokYsjl != ''">
        F_QZOK_YSJL = #{fQzokYsjl,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFid != null and fQzokFid != ''">
        F_QZOK_FID = #{fQzokFid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFentryid != null and fQzokFentryid != ''">
        F_QZOK_FEntryID = #{fQzokFentryid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCkdh != null and fQzokCkdh != ''">
        F_QZOK_CKDH = #{fQzokCkdh,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity">
    <!--@mbg.generated-->
    update KING_DEE_INSTALL_SERVICE_RECORD
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_DATE = #{fQzokDate,jdbcType=VARCHAR},
      F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      F_QZOK_YSSJ = #{fQzokYssj,jdbcType=VARCHAR},
      F_QZOK_QSSJ = #{fQzokQssj,jdbcType=VARCHAR},
      F_QZOK_BCFWSL = #{fQzokBcfwsl,jdbcType=DECIMAL},
      F_QZOK_YSFS = #{fQzokYsfs,jdbcType=VARCHAR},
      F_QZOK_YSJL = #{fQzokYsjl,jdbcType=VARCHAR},
      F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      F_QZOK_FID = #{fQzokFid,jdbcType=VARCHAR},
      F_QZOK_FEntryID = #{fQzokFentryid,jdbcType=VARCHAR},
      F_QZOK_CKDH = #{fQzokCkdh,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_INSTALL_SERVICE_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokDate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokOrgId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSSJ = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYssj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_QSSJ = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokQssj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BCFWSL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokBcfwsl,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSFS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYsfs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSJL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYsjl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSDDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYsddh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_GSYWDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokGsywdh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YWLX = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYwlx,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_WLBM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokWlbm,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_XLH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokXlh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokFid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_FEntryID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokFentryid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_CKDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokCkdh,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_INSTALL_SERVICE_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillNo != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokDate != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSSJ = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokYssj != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYssj,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_QSSJ = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokQssj != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokQssj,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BCFWSL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokBcfwsl != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokBcfwsl,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSFS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokYsfs != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYsfs,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSJL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokYsjl != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYsjl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSDDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokYsddh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYsddh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_GSYWDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokGsywdh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokGsywdh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YWLX = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokYwlx != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYwlx,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_WLBM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokWlbm != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokWlbm,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_XLH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokXlh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokXlh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokFid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokFid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_FEntryID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokFentryid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokFentryid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_CKDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokCkdh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokCkdh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_INSTALL_SERVICE_RECORD
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, F_BILL_NO, 
      F_QZOK_DATE, F_QZOK_ORG_ID, F_QZOK_YSSJ, F_QZOK_QSSJ, F_QZOK_BCFWSL, F_QZOK_YSFS, 
      F_QZOK_YSJL, F_QZOK_YSDDH, F_QZOK_GSYWDH, F_QZOK_YWLX, F_QZOK_WLBM, F_QZOK_XLH, 
      F_QZOK_FID, F_QZOK_FEntryID, F_QZOK_CKDH)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fid,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, #{item.fQzokDate,jdbcType=VARCHAR}, 
        #{item.fQzokOrgId,jdbcType=VARCHAR}, #{item.fQzokYssj,jdbcType=VARCHAR}, #{item.fQzokQssj,jdbcType=VARCHAR}, 
        #{item.fQzokBcfwsl,jdbcType=DECIMAL}, #{item.fQzokYsfs,jdbcType=VARCHAR}, #{item.fQzokYsjl,jdbcType=VARCHAR}, 
        #{item.fQzokYsddh,jdbcType=VARCHAR}, #{item.fQzokGsywdh,jdbcType=VARCHAR}, #{item.fQzokYwlx,jdbcType=VARCHAR}, 
        #{item.fQzokWlbm,jdbcType=VARCHAR}, #{item.fQzokXlh,jdbcType=VARCHAR}, #{item.fQzokFid,jdbcType=VARCHAR}, 
        #{item.fQzokFentryid,jdbcType=VARCHAR}, #{item.fQzokCkdh,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_INSTALL_SERVICE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
      FID,
      F_BILL_NO,
      F_QZOK_DATE,
      F_QZOK_ORG_ID,
      F_QZOK_YSSJ,
      F_QZOK_QSSJ,
      F_QZOK_BCFWSL,
      F_QZOK_YSFS,
      F_QZOK_YSJL,
      F_QZOK_YSDDH,
      F_QZOK_GSYWDH,
      F_QZOK_YWLX,
      F_QZOK_WLBM,
      F_QZOK_XLH,
      F_QZOK_FID,
      F_QZOK_FEntryID,
      F_QZOK_CKDH,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
      #{fid,jdbcType=VARCHAR},
      #{fBillNo,jdbcType=VARCHAR},
      #{fQzokDate,jdbcType=VARCHAR},
      #{fQzokOrgId,jdbcType=VARCHAR},
      #{fQzokYssj,jdbcType=VARCHAR},
      #{fQzokQssj,jdbcType=VARCHAR},
      #{fQzokBcfwsl,jdbcType=DECIMAL},
      #{fQzokYsfs,jdbcType=VARCHAR},
      #{fQzokYsjl,jdbcType=VARCHAR},
      #{fQzokYsddh,jdbcType=VARCHAR},
      #{fQzokGsywdh,jdbcType=VARCHAR},
      #{fQzokYwlx,jdbcType=VARCHAR},
      #{fQzokWlbm,jdbcType=VARCHAR},
      #{fQzokXlh,jdbcType=VARCHAR},
      #{fQzokFid,jdbcType=VARCHAR},
      #{fQzokFentryid,jdbcType=VARCHAR},
      #{fQzokCkdh,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=INTEGER},
      </if>
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_DATE = #{fQzokDate,jdbcType=VARCHAR},
      F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      F_QZOK_YSSJ = #{fQzokYssj,jdbcType=VARCHAR},
      F_QZOK_QSSJ = #{fQzokQssj,jdbcType=VARCHAR},
      F_QZOK_BCFWSL = #{fQzokBcfwsl,jdbcType=DECIMAL},
      F_QZOK_YSFS = #{fQzokYsfs,jdbcType=VARCHAR},
      F_QZOK_YSJL = #{fQzokYsjl,jdbcType=VARCHAR},
      F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      F_QZOK_FID = #{fQzokFid,jdbcType=VARCHAR},
      F_QZOK_FEntryID = #{fQzokFentryid,jdbcType=VARCHAR},
      F_QZOK_CKDH = #{fQzokCkdh,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_INSTALL_SERVICE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="fid != null and fid != ''">
        FID,
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO,
      </if>
      <if test="fQzokDate != null and fQzokDate != ''">
        F_QZOK_DATE,
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        F_QZOK_ORG_ID,
      </if>
      <if test="fQzokYssj != null and fQzokYssj != ''">
        F_QZOK_YSSJ,
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        F_QZOK_QSSJ,
      </if>
      <if test="fQzokBcfwsl != null">
        F_QZOK_BCFWSL,
      </if>
      <if test="fQzokYsfs != null and fQzokYsfs != ''">
        F_QZOK_YSFS,
      </if>
      <if test="fQzokYsjl != null and fQzokYsjl != ''">
        F_QZOK_YSJL,
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        F_QZOK_YSDDH,
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        F_QZOK_GSYWDH,
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        F_QZOK_YWLX,
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        F_QZOK_WLBM,
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        F_QZOK_XLH,
      </if>
      <if test="fQzokFid != null and fQzokFid != ''">
        F_QZOK_FID,
      </if>
      <if test="fQzokFentryid != null and fQzokFentryid != ''">
        F_QZOK_FEntryID,
      </if>
      <if test="fQzokCkdh != null and fQzokCkdh != ''">
        F_QZOK_CKDH,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null and fid != ''">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDate != null and fQzokDate != ''">
        #{fQzokDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYssj != null and fQzokYssj != ''">
        #{fQzokYssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        #{fQzokQssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBcfwsl != null">
        #{fQzokBcfwsl,jdbcType=DECIMAL},
      </if>
      <if test="fQzokYsfs != null and fQzokYsfs != ''">
        #{fQzokYsfs,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsjl != null and fQzokYsjl != ''">
        #{fQzokYsjl,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFid != null and fQzokFid != ''">
        #{fQzokFid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFentryid != null and fQzokFentryid != ''">
        #{fQzokFentryid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCkdh != null and fQzokCkdh != ''">
        #{fQzokCkdh,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null and fid != ''">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDate != null and fQzokDate != ''">
        F_QZOK_DATE = #{fQzokDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYssj != null and fQzokYssj != ''">
        F_QZOK_YSSJ = #{fQzokYssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        F_QZOK_QSSJ = #{fQzokQssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBcfwsl != null">
        F_QZOK_BCFWSL = #{fQzokBcfwsl,jdbcType=DECIMAL},
      </if>
      <if test="fQzokYsfs != null and fQzokYsfs != ''">
        F_QZOK_YSFS = #{fQzokYsfs,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsjl != null and fQzokYsjl != ''">
        F_QZOK_YSJL = #{fQzokYsjl,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFid != null and fQzokFid != ''">
        F_QZOK_FID = #{fQzokFid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFentryid != null and fQzokFentryid != ''">
        F_QZOK_FEntryID = #{fQzokFentryid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCkdh != null and fQzokCkdh != ''">
        F_QZOK_CKDH = #{fQzokCkdh,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-02-21-->
  <select id="findByFBillNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from KING_DEE_INSTALL_SERVICE_RECORD
        where F_BILL_NO=#{fBillNo,jdbcType=VARCHAR}
    </select>
</mapper>