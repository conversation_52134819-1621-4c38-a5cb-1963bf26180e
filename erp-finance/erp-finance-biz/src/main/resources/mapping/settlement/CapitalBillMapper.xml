<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.CapitalBillMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.CapitalBillEntity">
        <!--@mbg.generated-->
        <!--@Table T_CAPITAL_BILL-->
        <id column="CAPITAL_BILL_ID" jdbcType="INTEGER" property="capitalBillId"/>
        <result column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId"/>
        <result column="CAPITAL_BILL_NO" jdbcType="VARCHAR" property="capitalBillNo"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="TRAN_FLOW" jdbcType="VARCHAR" property="tranFlow"/>
        <result column="TRADER_TIME" jdbcType="BIGINT" property="traderTime"/>
        <result column="TRADER_SUBJECT" jdbcType="TINYINT" property="traderSubject"/>
        <result column="TRADER_TYPE" jdbcType="TINYINT" property="traderType"/>
        <result column="TRADER_MODE" jdbcType="INTEGER" property="traderMode"/>
        <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId"/>
        <result column="PAYER" jdbcType="VARCHAR" property="payer"/>
        <result column="PAYER_BANK_ACCOUNT" jdbcType="VARCHAR" property="payerBankAccount"/>
        <result column="PAYER_BANK_NAME" jdbcType="VARCHAR" property="payerBankName"/>
        <result column="PAYEE" jdbcType="VARCHAR" property="payee"/>
        <result column="PAYEE_BANK_ACCOUNT" jdbcType="VARCHAR" property="payeeBankAccount"/>
        <result column="PAYEE_BANK_NAME" jdbcType="VARCHAR" property="payeeBankName"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CAPITAL_SEARCH_FLOW" jdbcType="VARCHAR" property="capitalSearchFlow"/>
    </resultMap>

    <resultMap id="getCapitalBillDataResult" type="com.vedeng.erp.finance.dto.CapitalBillDto">
        <id column="CAPITAL_BILL_ID" jdbcType="INTEGER" property="capitalBillId"/>
        <result column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId"/>
        <result column="CAPITAL_BILL_NO" jdbcType="VARCHAR" property="capitalBillNo"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="TRAN_FLOW" jdbcType="VARCHAR" property="tranFlow"/>
        <result column="TRADER_TIME" jdbcType="BIGINT" property="traderTime"/>
        <result column="TRADER_SUBJECT" jdbcType="TINYINT" property="traderSubject"/>
        <result column="TRADER_TYPE" jdbcType="TINYINT" property="traderType"/>
        <result column="TRADER_MODE" jdbcType="INTEGER" property="traderMode"/>
        <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId"/>
        <result column="PAYER" jdbcType="VARCHAR" property="payer"/>
        <result column="PAYER_BANK_ACCOUNT" jdbcType="VARCHAR" property="payerBankAccount"/>
        <result column="PAYER_BANK_NAME" jdbcType="VARCHAR" property="payerBankName"/>
        <result column="PAYEE" jdbcType="VARCHAR" property="payee"/>
        <result column="PAYEE_BANK_ACCOUNT" jdbcType="VARCHAR" property="payeeBankAccount"/>
        <result column="PAYEE_BANK_NAME" jdbcType="VARCHAR" property="payeeBankName"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CAPITAL_SEARCH_FLOW" jdbcType="VARCHAR" property="capitalSearchFlow"/>

        <association property="capitalBillDetailDto"
                     javaType="com.vedeng.erp.finance.dto.CapitalBillDetailDto">
            <id column="CAPITAL_BILL_DETAIL_ID" property="capitalBillDetailId"
                jdbcType="INTEGER" />
            <result column="BUSSINESS_TYPE" property="bussinessType"
                    jdbcType="INTEGER" />
            <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        </association>

    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        CAPITAL_BILL_ID,
        BANK_BILL_ID,
        CAPITAL_BILL_NO,
        COMPANY_ID,
        TRAN_FLOW,
        TRADER_TIME,
        TRADER_SUBJECT,
        TRADER_TYPE,
        TRADER_MODE,
        PAYMENT_TYPE,
        AMOUNT,
        CURRENCY_UNIT_ID,
        PAYER,
        PAYER_BANK_ACCOUNT,
        PAYER_BANK_NAME,
        PAYEE,
        PAYEE_BANK_ACCOUNT,
        PAYEE_BANK_NAME,
        COMMENTS,
        ADD_TIME,
        CREATOR,
        CAPITAL_SEARCH_FLOW
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_CAPITAL_BILL
        where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_CAPITAL_BILL
        where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="CAPITAL_BILL_ID" keyProperty="capitalBillId"
            parameterType="com.vedeng.erp.settlement.domain.entity.CapitalBillEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CAPITAL_BILL (BANK_BILL_ID, CAPITAL_BILL_NO, COMPANY_ID,
                                    TRAN_FLOW, TRADER_TIME, TRADER_SUBJECT,
                                    TRADER_TYPE, TRADER_MODE, PAYMENT_TYPE,
                                    AMOUNT, CURRENCY_UNIT_ID, PAYER,
                                    PAYER_BANK_ACCOUNT, PAYER_BANK_NAME, PAYEE,
                                    PAYEE_BANK_ACCOUNT, PAYEE_BANK_NAME, COMMENTS,
                                    ADD_TIME, CREATOR, CAPITAL_SEARCH_FLOW)
        values (#{bankBillId,jdbcType=INTEGER}, #{capitalBillNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER},
                #{tranFlow,jdbcType=VARCHAR}, #{traderTime,jdbcType=BIGINT}, #{traderSubject,jdbcType=TINYINT},
                #{traderType,jdbcType=TINYINT}, #{traderMode,jdbcType=INTEGER}, #{paymentType,jdbcType=INTEGER},
                #{amount,jdbcType=DECIMAL}, #{currencyUnitId,jdbcType=INTEGER}, #{payer,jdbcType=VARCHAR},
                #{payerBankAccount,jdbcType=VARCHAR}, #{payerBankName,jdbcType=VARCHAR}, #{payee,jdbcType=VARCHAR},
                #{payeeBankAccount,jdbcType=VARCHAR}, #{payeeBankName,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR},
                #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{capitalSearchFlow,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="CAPITAL_BILL_ID" keyProperty="capitalBillId"
            parameterType="com.vedeng.erp.settlement.domain.entity.CapitalBillEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CAPITAL_BILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bankBillId != null">
                BANK_BILL_ID,
            </if>
            <if test="capitalBillNo != null">
                CAPITAL_BILL_NO,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="tranFlow != null">
                TRAN_FLOW,
            </if>
            <if test="traderTime != null">
                TRADER_TIME,
            </if>
            <if test="traderSubject != null">
                TRADER_SUBJECT,
            </if>
            <if test="traderType != null">
                TRADER_TYPE,
            </if>
            <if test="traderMode != null">
                TRADER_MODE,
            </if>
            <if test="paymentType != null">
                PAYMENT_TYPE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="currencyUnitId != null">
                CURRENCY_UNIT_ID,
            </if>
            <if test="payer != null">
                PAYER,
            </if>
            <if test="payerBankAccount != null">
                PAYER_BANK_ACCOUNT,
            </if>
            <if test="payerBankName != null">
                PAYER_BANK_NAME,
            </if>
            <if test="payee != null">
                PAYEE,
            </if>
            <if test="payeeBankAccount != null">
                PAYEE_BANK_ACCOUNT,
            </if>
            <if test="payeeBankName != null">
                PAYEE_BANK_NAME,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="capitalSearchFlow != null">
                CAPITAL_SEARCH_FLOW,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bankBillId != null">
                #{bankBillId,jdbcType=INTEGER},
            </if>
            <if test="capitalBillNo != null">
                #{capitalBillNo,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="tranFlow != null">
                #{tranFlow,jdbcType=VARCHAR},
            </if>
            <if test="traderTime != null">
                #{traderTime,jdbcType=BIGINT},
            </if>
            <if test="traderSubject != null">
                #{traderSubject,jdbcType=TINYINT},
            </if>
            <if test="traderType != null">
                #{traderType,jdbcType=TINYINT},
            </if>
            <if test="traderMode != null">
                #{traderMode,jdbcType=INTEGER},
            </if>
            <if test="paymentType != null">
                #{paymentType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null">
                #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="payer != null">
                #{payer,jdbcType=VARCHAR},
            </if>
            <if test="payerBankAccount != null">
                #{payerBankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payerBankName != null">
                #{payerBankName,jdbcType=VARCHAR},
            </if>
            <if test="payee != null">
                #{payee,jdbcType=VARCHAR},
            </if>
            <if test="payeeBankAccount != null">
                #{payeeBankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payeeBankName != null">
                #{payeeBankName,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="capitalSearchFlow != null">
                #{capitalSearchFlow,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.CapitalBillEntity">
        <!--@mbg.generated-->
        update T_CAPITAL_BILL
        <set>
            <if test="bankBillId != null">
                BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
            </if>
            <if test="capitalBillNo != null">
                CAPITAL_BILL_NO = #{capitalBillNo,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="tranFlow != null">
                TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
            </if>
            <if test="traderTime != null">
                TRADER_TIME = #{traderTime,jdbcType=BIGINT},
            </if>
            <if test="traderSubject != null">
                TRADER_SUBJECT = #{traderSubject,jdbcType=TINYINT},
            </if>
            <if test="traderType != null">
                TRADER_TYPE = #{traderType,jdbcType=TINYINT},
            </if>
            <if test="traderMode != null">
                TRADER_MODE = #{traderMode,jdbcType=INTEGER},
            </if>
            <if test="paymentType != null">
                PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null">
                CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="payer != null">
                PAYER = #{payer,jdbcType=VARCHAR},
            </if>
            <if test="payerBankAccount != null">
                PAYER_BANK_ACCOUNT = #{payerBankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payerBankName != null">
                PAYER_BANK_NAME = #{payerBankName,jdbcType=VARCHAR},
            </if>
            <if test="payee != null">
                PAYEE = #{payee,jdbcType=VARCHAR},
            </if>
            <if test="payeeBankAccount != null">
                PAYEE_BANK_ACCOUNT = #{payeeBankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payeeBankName != null">
                PAYEE_BANK_NAME = #{payeeBankName,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="capitalSearchFlow != null">
                CAPITAL_SEARCH_FLOW = #{capitalSearchFlow,jdbcType=VARCHAR},
            </if>
        </set>
        where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.CapitalBillEntity">
        <!--@mbg.generated-->
        update T_CAPITAL_BILL
        set BANK_BILL_ID        = #{bankBillId,jdbcType=INTEGER},
            CAPITAL_BILL_NO     = #{capitalBillNo,jdbcType=VARCHAR},
            COMPANY_ID          = #{companyId,jdbcType=INTEGER},
            TRAN_FLOW           = #{tranFlow,jdbcType=VARCHAR},
            TRADER_TIME         = #{traderTime,jdbcType=BIGINT},
            TRADER_SUBJECT      = #{traderSubject,jdbcType=TINYINT},
            TRADER_TYPE         = #{traderType,jdbcType=TINYINT},
            TRADER_MODE         = #{traderMode,jdbcType=INTEGER},
            PAYMENT_TYPE        = #{paymentType,jdbcType=INTEGER},
            AMOUNT              = #{amount,jdbcType=DECIMAL},
            CURRENCY_UNIT_ID    = #{currencyUnitId,jdbcType=INTEGER},
            PAYER               = #{payer,jdbcType=VARCHAR},
            PAYER_BANK_ACCOUNT  = #{payerBankAccount,jdbcType=VARCHAR},
            PAYER_BANK_NAME     = #{payerBankName,jdbcType=VARCHAR},
            PAYEE               = #{payee,jdbcType=VARCHAR},
            PAYEE_BANK_ACCOUNT  = #{payeeBankAccount,jdbcType=VARCHAR},
            PAYEE_BANK_NAME     = #{payeeBankName,jdbcType=VARCHAR},
            COMMENTS            = #{comments,jdbcType=VARCHAR},
            ADD_TIME            = #{addTime,jdbcType=BIGINT},
            CREATOR             = #{creator,jdbcType=INTEGER},
            CAPITAL_SEARCH_FLOW = #{capitalSearchFlow,jdbcType=VARCHAR}
        where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="CAPITAL_BILL_ID" keyProperty="capitalBillId" parameterType="map"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CAPITAL_BILL
        (BANK_BILL_ID, CAPITAL_BILL_NO, COMPANY_ID, TRAN_FLOW, TRADER_TIME, TRADER_SUBJECT,
         TRADER_TYPE, TRADER_MODE, PAYMENT_TYPE, AMOUNT, CURRENCY_UNIT_ID, PAYER, PAYER_BANK_ACCOUNT,
         PAYER_BANK_NAME, PAYEE, PAYEE_BANK_ACCOUNT, PAYEE_BANK_NAME, COMMENTS, ADD_TIME,
         CREATOR, CAPITAL_SEARCH_FLOW)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.bankBillId,jdbcType=INTEGER}, #{item.capitalBillNo,jdbcType=VARCHAR},
             #{item.companyId,jdbcType=INTEGER},
             #{item.tranFlow,jdbcType=VARCHAR}, #{item.traderTime,jdbcType=BIGINT},
             #{item.traderSubject,jdbcType=TINYINT},
             #{item.traderType,jdbcType=TINYINT}, #{item.traderMode,jdbcType=INTEGER},
             #{item.paymentType,jdbcType=INTEGER},
             #{item.amount,jdbcType=DECIMAL}, #{item.currencyUnitId,jdbcType=INTEGER}, #{item.payer,jdbcType=VARCHAR},
             #{item.payerBankAccount,jdbcType=VARCHAR}, #{item.payerBankName,jdbcType=VARCHAR},
             #{item.payee,jdbcType=VARCHAR}, #{item.payeeBankAccount,jdbcType=VARCHAR},
             #{item.payeeBankName,jdbcType=VARCHAR},
             #{item.comments,jdbcType=VARCHAR}, #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER},
             #{item.capitalSearchFlow,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="getCapitalBillData" resultMap="getCapitalBillDataResult">
        select a.*,
               b.CAPITAL_BILL_DETAIL_ID,
               b.BUSSINESS_TYPE,
               b.ORDER_NO
        from T_CAPITAL_BILL a
                 join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        <where>
            <if test="bussinessType != null and bussinessType != ''">
                <!-- 业务类型 -->
                AND b.BUSSINESS_TYPE =
                    #{bussinessType,jdbcType=INTEGER}
            </if>
            <!-- 订单类型 -->
            AND b.ORDER_TYPE = #{orderType}
            <if test="relatedId != null and relatedId != ''">
                <!-- 关联表ID -->
                AND b.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="traderId != null and traderId != ''">
                <!-- 关联表ID -->
                AND b.TRADER_ID = #{traderId,jdbcType=INTEGER}
            </if>
            <if test="traderType != null and traderType != ''">
                <!-- 关联表ID -->
                AND b.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
            </if>
            <if test="traderMode != null and traderMode != ''">
                AND a.TRADER_MODE = #{traderMode,jdbcType=INTEGER}
            </if>
        </where>
        order by a.CAPITAL_BILL_ID DESC
    </select>

    <select id="getAfterReturnCapitalBillData" resultMap="getCapitalBillDataResult">
        <!-- 销售 -->
        <if test="operationType != null and operationType == 'finance_sale_detail'">
            SELECT C.*,
            B.CAPITAL_BILL_DETAIL_ID,
            B.BUSSINESS_TYPE,
            B.ORDER_NO
            FROM T_AFTER_SALES A
            INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID = B.RELATED_ID
            INNER JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
            WHERE A.ORDER_ID = #{capitalBillDetailDto.relatedId,jdbcType=INTEGER}
            AND A.SUBJECT_TYPE = 535
            AND C.TRADER_TYPE IN (3,5)	<!-- 转移、转出 -->
            AND C.TRADER_MODE IN (521, 529, 530)	<!-- 退还银行、信用、余额 -->
            AND B.ORDER_TYPE = 3	<!-- 订单类型3售后 -->
            <!-- 交易者ID -->
            <if
                    test="capitalBillDetailDto.traderId != null and capitalBillDetailDto.traderId != ''">
                AND B.TRADER_ID = #{capitalBillDetailDto.traderId,jdbcType=INTEGER}
            </if>
            <!-- 所属类型 1::经销商（包含终端）2:供应商 -->
            <if
                    test="capitalBillDetailDto.traderType != null and capitalBillDetailDto.traderType != ''">
                AND B.TRADER_TYPE = #{capitalBillDetailDto.traderType,jdbcType=BIT}
            </if>
            AND B.BUSSINESS_TYPE IN (531,533)	<!-- 退款,信用还款 -->
            AND A.VALID_STATUS = 1	<!-- 生效 -->
            AND A.ATFER_SALES_STATUS IN (1, 2)	<!-- 进行中，已完结 -->
            ORDER BY B.CAPITAL_BILL_ID DESC
        </if>
        <!-- 采购 -->
        <if test="operationType != null and operationType == 'finance_buy_detail'">
            SELECT C.*,
            B.CAPITAL_BILL_DETAIL_ID,
            B.BUSSINESS_TYPE,
            B.ORDER_NO
            FROM T_AFTER_SALES A
            INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID = B.RELATED_ID
            INNER JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
            WHERE A.ORDER_ID = #{capitalBillDetailDto.relatedId,jdbcType=INTEGER}
            AND A.SUBJECT_TYPE = 536
            AND C.TRADER_TYPE IN (1, 3, 4)	<!-- 收入、转移、转入 -->
            AND C.TRADER_MODE IN (521, 529, 530)	<!-- 退还银行、信用、余额 -->
            AND B.ORDER_TYPE = 3	<!-- 订单类型3售后 -->
            <!-- 交易者ID -->
            <if
                    test="capitalBillDetailDto.traderId != null and capitalBillDetailDto.traderId != ''">
                AND B.TRADER_ID = #{capitalBillDetailDto.traderId,jdbcType=INTEGER}
            </if>
            <!-- 所属类型 1::经销商（包含终端）2:供应商 -->
            <if
                    test="capitalBillDetailDto.traderType != null and capitalBillDetailDto.traderType != ''">
                AND B.TRADER_TYPE = #{capitalBillDetailDto.traderType}
            </if>
            AND B.BUSSINESS_TYPE IN (531,533)	<!-- 退款,信用还款 -->
            AND A.VALID_STATUS = 1	<!-- 生效 -->
            AND A.ATFER_SALES_STATUS IN (1, 2)	<!-- 进行中，已完结 -->
            ORDER BY B.CAPITAL_BILL_ID DESC
        </if>
        <!-- 采购费用       -->
        <if test="operationType != null and operationType == 'finance_buy_expense_detail'">
            SELECT C.*,
            B.CAPITAL_BILL_DETAIL_ID,
            B.BUSSINESS_TYPE,
            B.ORDER_NO
            FROM T_EXPENSE_AFTER_SALES A
            LEFT join T_EXPENSE_AFTER_SALES_STATUS D on A.EXPENSE_AFTER_SALES_ID = D.EXPENSE_AFTER_SALES_ID
            INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.EXPENSE_AFTER_SALES_ID = B.RELATED_ID
            INNER JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
            WHERE A.BUYORDER_EXPENSE_ID = #{capitalBillDetailDto.relatedId,jdbcType=INTEGER}
            AND B.ORDER_TYPE = 5	<!-- 订单类型5费用售后 -->
            <!-- 交易者ID -->
            <if
                    test="capitalBillDetailDto.traderId != null and capitalBillDetailDto.traderId != ''">
                AND B.TRADER_ID = #{capitalBillDetailDto.traderId,jdbcType=INTEGER}
            </if>
            <!-- 所属类型 1::经销商（包含终端）2:供应商 -->
            <if
                    test="capitalBillDetailDto.traderType != null and capitalBillDetailDto.traderType != ''">
                AND B.TRADER_TYPE = #{capitalBillDetailDto.traderType}
            </if>
            AND B.BUSSINESS_TYPE IN (531,533)	<!-- 退款,信用还款 -->
            AND D.VALID_STATUS = 1	<!-- 生效 -->
            AND D.AFTER_SALES_STATUS IN (1, 2)	<!-- 进行中，已完结 -->
            ORDER BY B.CAPITAL_BILL_ID DESC
        </if>
    </select>

    <select id="findByBankBillId" resultType="com.vedeng.erp.finance.dto.CapitalBillDto">
        select
        <include refid="Base_Column_List"/>
        from T_CAPITAL_BILL
        where BANK_BILL_ID=#{bankBillId,jdbcType=INTEGER}
    </select>

    <select id="getAftersaleServiceAmountBill" resultType="java.math.BigDecimal">
        select
        COALESCE(SUM(b.AMOUNT),0)
        from
        T_CAPITAL_BILL a
        left join
        T_CAPITAL_BILL_DETAIL b
        on
        a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        where
        a.TRADER_TYPE = 1 and b.ORDER_TYPE = 3 and b.BUSSINESS_TYPE = 526
        and b.ORDER_NO = #{orderNo,jdbcType=VARCHAR}
        and b.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>

    <select id="getAfterSalesPublicIncome" resultType="java.math.BigDecimal">
        select IFNULL(SUM(ABS(tcb.AMOUNT)), 0)
        from T_CAPITAL_BILL tcb
                 left join T_CAPITAL_BILL_DETAIL tcbd on tcb.CAPITAL_BILL_ID = tcbd.CAPITAL_BILL_ID
        where tcb.TRADER_SUBJECT = 1
          and tcbd.BUSSINESS_TYPE = 526
          and tcb.PAYER = #{traderName,jdbcType=VARCHAR}
          and tcbd.ORDER_TYPE = 3
          and tcb.PAYER != '支付宝（中国）网络技术有限公司'
          and tcb.PAYER != '财付通支付科技有限公司'
          and tcbd.RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>

    <select id="getSaleOrderPublicIncome" resultType="java.math.BigDecimal">
        select IFNULL(SUM(ABS(tcb.AMOUNT)), 0)
        from T_CAPITAL_BILL tcb
                 left join T_CAPITAL_BILL_DETAIL tcbd on tcb.CAPITAL_BILL_ID = tcbd.CAPITAL_BILL_ID
        where tcb.TRADER_SUBJECT = 1
          and tcbd.BUSSINESS_TYPE = 526
          and tcb.PAYER = #{traderName,jdbcType=VARCHAR}
          and tcbd.ORDER_TYPE = 1
          and tcb.PAYER != '支付宝（中国）网络技术有限公司'
          and tcb.PAYER != '财付通支付科技有限公司'
          and tcbd.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <select id="getSaleOrderTotalExpenditure" resultType="java.math.BigDecimal">
        select IFNULL(SUM(ABS(tcb.AMOUNT)), 0)
        from T_CAPITAL_BILL tcb
                 left join T_CAPITAL_BILL_DETAIL tcbd on tcb.CAPITAL_BILL_ID = tcbd.CAPITAL_BILL_ID
                 left join T_AFTER_SALES tas on tcbd.RELATED_ID = tas.AFTER_SALES_ID
                 left join T_AFTER_SALES_DETAIL tasd on tas.AFTER_SALES_ID = tasd.AFTER_SALES_ID
        where tcbd.BUSSINESS_TYPE = 531
          and tcb.TRADER_SUBJECT = 1
          and tcbd.ORDER_TYPE = 3
          and tas.SUBJECT_TYPE = 535
          and tasd.REFUND = 2
          and tcb.TRADER_MODE in (520,521,522)
          and tas.TYPE in (539, 543)
          and tas.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <select id="getSaleOrderTotalExpenditureBalance" resultType="java.math.BigDecimal">
        select IFNULL(SUM(ABS(tcb.AMOUNT)), 0)
        from T_CAPITAL_BILL tcb
        left join T_CAPITAL_BILL_DETAIL tcbd on tcb.CAPITAL_BILL_ID = tcbd.CAPITAL_BILL_ID
        left join T_AFTER_SALES tas on tcbd.RELATED_ID = tas.AFTER_SALES_ID
        left join T_AFTER_SALES_DETAIL tasd on tas.AFTER_SALES_ID = tasd.AFTER_SALES_ID
        where tcbd.BUSSINESS_TYPE = 531
        and tcbd.ORDER_TYPE = 3
        and tas.SUBJECT_TYPE = 535
        and tasd.REFUND = 1
        and tas.TYPE in (539, 543)
        and tas.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <select id="getFirstCapitalBillIdByBankBillId" resultType="java.lang.Integer">
        select CAPITAL_BILL_ID from T_CAPITAL_BILL where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
        order by CAPITAL_BILL_ID limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2024-01-11-->
    <select id="findByCapitalBillNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CAPITAL_BILL
        where CAPITAL_BILL_NO=#{capitalBillNo,jdbcType=VARCHAR}
    </select>

    <select id="getReceivedAmountBySaleOrderId" resultMap="getCapitalBillDataResult">
        select tcb.*
        from T_CAPITAL_BILL tcb
        left join T_CAPITAL_BILL_DETAIL tcbd on tcb.CAPITAL_BILL_ID = tcbd.CAPITAL_BILL_ID
        where tcb.TRADER_TYPE != 3
        and tcbd.BUSSINESS_TYPE != 679
        and (tcb.PAYER IS NULL OR tcb.PAYER NOT IN ('支付宝（中国）网络技术有限公司', '财付通支付科技有限公司'))
        and tcbd.BUSSINESS_TYPE in (526,533)
        and tcbd.ORDER_TYPE = 1
        and tcbd.RELATED_ID = #{orderId,jdbcType=INTEGER}
    </select>


    <select id="findAcceptanceBill" resultType="com.vedeng.erp.finance.dto.CapitalBillDto">
        select
        TCB.*
        from T_CAPITAL_BILL TCB
                 left join T_CAPITAL_BILL_DETAIL TCD on TCB.CAPITAL_BILL_ID = TCD.CAPITAL_BILL_ID
        where TCD.ORDER_TYPE = 2
          and TCD.ORDER_NO = #{transactionContractNo,jdbcType=VARCHAR}
          and TCD.BUSSINESS_TYPE = 525
          and TCB.TRADER_SUBJECT = 1
          and TCB.TRADER_TYPE = 2
          and TCB.TRADER_MODE = 10001
    </select>
</mapper>