package com.vedeng.crm.constant;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/8/13
 */
public class CrmConstants {

    private static String staticResourceVersion  = "1.0.0"; //默认的版本号

    public static String getStaticResourceVersion() {
        return staticResourceVersion;
    }

    public static void setStaticResourceVersion(String staticResourceVersion) {
        CrmConstants.staticResourceVersion = staticResourceVersion;
    }

    private static String checkCrmIsInner = "";//判断是否是内网 crm加载此变量来获取是否在内网环境
    public static String getCheckCrmIsInner() {
        return checkCrmIsInner;
    }

    public static void setCheckCrmIsInner(String checkCrmIsInner) {
        CrmConstants.checkCrmIsInner = checkCrmIsInner;
    }

}
