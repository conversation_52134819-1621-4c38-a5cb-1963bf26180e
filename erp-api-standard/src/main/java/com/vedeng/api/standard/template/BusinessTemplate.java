package com.vedeng.api.standard.template;

import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.DataConverter;
import com.vedeng.api.standard.duplicate.generator.IdempotencyKeyGenerator;
import com.vedeng.api.standard.duplicate.helper.IdempotencyHelper;
import com.vedeng.api.standard.duplicate.service.IdempotencyRecordService;
import com.vedeng.api.standard.duplicate.enums.IdempotencyStatus;
import com.alibaba.fastjson.JSON;
import com.vedeng.api.standard.duplicate.entity.IdempotencyRecord;
import com.vedeng.api.standard.internal.InternalCallRequest;
import com.vedeng.api.standard.internal.InternalCallResult;
import com.vedeng.api.standard.internal.InternalHttpCallService;
import com.vedeng.api.standard.validation.StandardValidator;
import com.vedeng.api.standard.validation.exception.ValidationException;
import com.vedeng.authorization.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务模板类
 * 提供统一的业务执行流程，支持创建、更新、查询、删除等操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
@Component
public class BusinessTemplate {

    private static final Logger logger = LoggerFactory.getLogger(BusinessTemplate.class);

    @Autowired
    private DataConverter dataConverter;

    @Autowired
    private StandardValidator standardValidator;

    @Autowired
    private InternalHttpCallService internalHttpCallService;

    @Autowired
    private IdempotencyHelper idempotencyHelper;
    
    @Autowired
    private IdempotencyRecordService idempotencyRecordService;

    @Autowired
    private IdempotencyKeyGenerator keyGenerator;

    /**
     * 执行创建操作
     *
     * @param request 原始请求
     * @param <T>     请求类型
     * @param <R>     响应类型
     * @return 业务执行构建器
     */
    public <T, R> BusinessExecutionBuilder<T, R> executeCreate(ApiRequest request) {
        BusinessExecutionBuilder<T, R> builder = new BusinessExecutionBuilder<>(this, request);
        // 应用创建操作的默认配置
        BusinessStepConfig defaultConfig = BusinessStepConfig.forCreateOperation();
        applyDefaultConfig(builder, defaultConfig);
        return builder;
    }

    /**
     * 执行更新操作
     *
     * @param request 原始请求
     * @param <T>     请求类型
     * @param <R>     响应类型
     * @return 业务执行构建器
     */
    public <T, R> BusinessExecutionBuilder<T, R> executeUpdate(ApiRequest request) {
        BusinessExecutionBuilder<T, R> builder = new BusinessExecutionBuilder<>(this, request);
        // 应用更新操作的默认配置
        BusinessStepConfig defaultConfig = BusinessStepConfig.forUpdateOperation();
        applyDefaultConfig(builder, defaultConfig);
        return builder;
    }

    /**
     * 执行查询操作
     *
     * @param request 原始请求
     * @param <T>     请求类型
     * @param <R>     响应类型
     * @return 业务执行构建器
     */
    public <T, R> BusinessExecutionBuilder<T, R> executeQuery(ApiRequest request) {
        BusinessExecutionBuilder<T, R> builder = new BusinessExecutionBuilder<>(this, request);
        // 应用查询操作的默认配置
        BusinessStepConfig defaultConfig = BusinessStepConfig.forQueryOperation();
        applyDefaultConfig(builder, defaultConfig);
        return builder;
    }

    /**
     * 执行删除操作
     *
     * @param request 原始请求
     * @param <T>     请求类型
     * @param <R>     响应类型
     * @return 业务执行构建器
     */
    public <T, R> BusinessExecutionBuilder<T, R> executeDelete(ApiRequest request) {
        BusinessExecutionBuilder<T, R> builder = new BusinessExecutionBuilder<>(this, request);
        // 应用删除操作的默认配置
        BusinessStepConfig defaultConfig = BusinessStepConfig.forDeleteOperation();
        applyDefaultConfig(builder, defaultConfig);
        return builder;
    }

    /**
     * 执行自定义操作
     *
     * @param request 原始请求
     * @param <T>     请求类型
     * @param <R>     响应类型
     * @return 业务执行构建器
     */
    public <T, R> BusinessExecutionBuilder<T, R> executeCustom(ApiRequest request) {
        return new BusinessExecutionBuilder<>(this, request);
    }

    /**
     * 根据配置执行业务逻辑
     *
     * @param request 原始请求
     * @param config  步骤配置
     * @return 执行结果
     * @throws Exception 执行异常
     */
    @SuppressWarnings("unchecked")
    public Object executeWithConfig(ApiRequest request, BusinessStepConfig config) throws Exception {
        logger.info("开始执行业务逻辑: requestType={}, controller={}.{}",
                config.getRequestType().getSimpleName(),
                config.getControllerBeanName(), config.getControllerMethodName());

        // 验证幂等性配置一致性
        validateIdempotencyConfig(config);

        // 幂等性预检查 - 在所有业务逻辑之前
        if (config.isIdempotencyHandlingEnabled()) {
            Object existingResult = checkIdempotencyPreCondition(request, config);
            if (existingResult != null) {
                return existingResult;
            }
        }

        try {
            // 1. 数据转换
            Object convertedRequest = convertRequestData(request, config);

            // 2. 幂等性记录管理（如果启用了幂等性处理）
            if (config.isIdempotencyHandlingEnabled()) {
                setupIdempotencyManagement(request, config, convertedRequest);
            }

            // 3. 参数验证
            if (!config.isSkipValidation()) {
                performValidation(convertedRequest, config);
            }

            // 4. 执行业务逻辑
            InternalCallResult callResult = executeBusinessLogic(config, request);

            // 5. 更新幂等性记录为成功状态（如果启用了幂等性处理）
            if (config.isIdempotencyHandlingEnabled()) {
                updateIdempotencySuccess(request, config, callResult);
            }

            // 6. 构建幂等性响应（如果启用了幂等性处理）
            if (config.isIdempotencyHandlingEnabled()) {
                // 从 InternalCallResult 中提取业务数据构建幂等性响应
                Object businessData = callResult != null ? callResult.getData() : null;
                return buildIdempotentResponse(businessData, config);
            }
            // 返回业务数据而不是框架封装对象
            return callResult != null ? callResult.getData() : null;

        } catch (Exception e) {
            // 统一更新幂等性记录为失败状态
            if (config.isIdempotencyHandlingEnabled()) {
                // 确保失败状态更新时有键可用
                ensureIdempotencyKeyGenerated(request, config);
                updateIdempotencyFailure(request, config, e);
            }

            logger.error("业务执行失败: requestType={}, controller={}.{}",
                    config.getRequestType().getSimpleName(),
                    config.getControllerBeanName(), config.getControllerMethodName(), e);
            throw e;
        }
    }

    /**
     * 转换请求数据
     */
    private Object convertRequestData(ApiRequest request, BusinessStepConfig config) throws Exception {
        logger.debug("转换请求数据: {} -> {}",
                request.getData().getClass().getSimpleName(),
                config.getRequestType().getSimpleName());

        return dataConverter.convert(request.getData(), config.getRequestType());
    }

    /**
     * 执行参数验证
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private void performValidation(Object convertedRequest, BusinessStepConfig config) throws Exception {
        if (config.getValidationRules() != null && config.getValidationRules().length > 0) {
            logger.debug("执行参数验证: rules={}", (Object[]) config.getValidationRules());

            // 使用反射调用 validateAndThrow 方法来避免泛型类型问题
            try {
                java.lang.reflect.Method method = StandardValidator.class.getMethod("validateAndThrow",
                        Object.class, Map.class, Class[].class);
                method.invoke(standardValidator, convertedRequest, config.getValidationContext(),
                        config.getValidationRules());
            } catch (java.lang.reflect.InvocationTargetException e) {
                if (e.getCause() instanceof ValidationException) {
                    throw (ValidationException) e.getCause();
                } else {
                    throw new Exception("验证失败", e.getCause());
                }
            }
        }
    }

    /**
     * 设置幂等性管理
     * 独立管理幂等性记录的创建和键生成，专门用于幂等性处理
     * 
     * @param request API请求
     * @param config 业务配置  
     * @param convertedRequest 转换后的请求对象
     */
    private void setupIdempotencyManagement(ApiRequest request, BusinessStepConfig config, Object convertedRequest) {
        try {
            // 1. 确保idempotencyContext不为null
            Map<String, Object> idempotencyContext = config.getIdempotencyContext();
            if (idempotencyContext == null) {
                idempotencyContext = new HashMap<>();
                config.setIdempotencyContext(idempotencyContext);
                logger.debug("初始化幂等性上下文: requestId={}", request.getRequestId());
            }
            if (!idempotencyContext.containsKey("apiRequest")) {
                idempotencyContext.put("apiRequest", request);
                logger.debug("添加apiRequest到幂等性上下文: requestId={}", request.getRequestId());
            }

            // 2. 生成或获取幂等性键
            String idempotencyKey = generateOrGetIdempotencyKey(request, config);
            if (idempotencyKey == null) {
                logger.warn("无法生成幂等性键，跳过幂等性记录创建: requestId={}", request.getRequestId());
                return;
            }
            
            // 3. 直接创建处理中记录（预检查已在executeWithConfig中完成）
            // 如果记录已存在，数据库会抛出DuplicateKeyException，由service层处理
            idempotencyRecordService.createProcessingRecord(idempotencyKey, convertedRequest, idempotencyContext);
            logger.info("创建幂等性处理记录: key={}, businessType={}", idempotencyKey, config.getBusinessTypeName());
            
        } catch (Exception e) {
            logger.error("设置幂等性管理失败: requestId={}", request.getRequestId(), e);
            // 不抛出异常，让业务继续执行，但后续更新可能会失败
        }
    }

    /**
     * 执行业务逻辑
     */
    private InternalCallResult executeBusinessLogic(BusinessStepConfig config, ApiRequest request) throws Exception {
        // 获取 Controller 信息（必须显式配置）
        ControllerInfo controllerInfo = getControllerInfo(config);

        logger.debug("执行业务逻辑: controller={}.{}",
                controllerInfo.getControllerBeanName(), controllerInfo.getMethodName());

        // 获取当前用户
        User currentUser = request.getCurrentUser();
        logger.debug("传递用户上下文: user={}", currentUser != null ? currentUser.getUsername() : "null");

        // 动态解析参数配置（如果有原始配置）
        if (config.getOriginalParameterConfigs() != null) {
            resolveParametersFromValidationContext(config);
        }

        // 检查是否有自定义参数配置
        if (config.getCustomParameterTypes() != null && config.getCustomParameterValues() != null) {
            // 使用自定义参数配置调用
            logger.info("使用自定义参数配置调用: parameterCount={}", config.getCustomParameterTypes().length);

            // 创建InternalCallRequest并直接设置自定义参数
            InternalCallRequest internalRequest = InternalCallRequest.post(
                            controllerInfo.getControllerBeanName(),
                            controllerInfo.getMethodName())
                    .withUser(currentUser)
                    .withSession(currentUser != null) // 根据用户是否存在决定是否需要会话
                    .withParameterTypes(config.getCustomParameterTypes())
                    .withCustomParameters(config.getCustomParameterValues());

            // 添加请求参数（用于request.getParameter()）
            if (config.getCallParams() != null && !config.getCallParams().isEmpty()) {
                internalRequest.addParameters(config.getCallParams());
                logger.debug("添加请求参数: {}", config.getCallParams());
            }

            return internalHttpCallService.execute(internalRequest);
        }

        // 如果没有自定义参数配置，抛出异常要求明确配置
        throw new IllegalArgumentException(
                "必须通过 .parameters() 方法明确配置Controller方法参数。" +
                        "例如：.parameters(ParameterConfig.of(BuyorderVo.class, buyorderVo))");
    }


    /**
     * 从验证上下文中解析参数配置
     *
     * @param config 业务步骤配置
     */
    private void resolveParametersFromValidationContext(BusinessStepConfig config) {
        ParameterConfig[] originalConfigs = config.getOriginalParameterConfigs();
        if (originalConfigs == null || originalConfigs.length == 0) {
            return;
        }

        logger.debug("开始解析验证上下文参数: originalConfigCount={}", originalConfigs.length);

        // 分离不同类型的参数
        java.util.List<ParameterConfig> businessParams = new java.util.ArrayList<>();
        java.util.Map<String, Object> requestParams = new java.util.HashMap<>();
        java.util.List<Object> validationContextParams = new java.util.ArrayList<>();
        java.util.List<Class<?>> validationContextTypes = new java.util.ArrayList<>();

        for (ParameterConfig paramConfig : originalConfigs) {
            if (paramConfig.isRequestParameter()) {
                // 请求参数
                requestParams.put(paramConfig.getRequestParameterName(), paramConfig.getValue());
            } else if (paramConfig.isFromValidationContext()) {
                // 从验证上下文获取参数
                String contextKey = paramConfig.getValidationContextKey();
                Object contextValue = config.getValidationContext().get(contextKey);

                if (contextValue == null) {
                    logger.warn("验证上下文中未找到键: {}, 类型: {}", contextKey, paramConfig.getType().getSimpleName());
                    // 使用null值，让业务逻辑自己处理
                }

                validationContextParams.add(contextValue);
                validationContextTypes.add(paramConfig.getType());
                logger.debug("从验证上下文获取参数: key={}, type={}, value={}",
                        contextKey, paramConfig.getType().getSimpleName(), contextValue);
            } else {
                // 普通业务参数
                businessParams.add(paramConfig);
            }
        }

        // 重新构建参数类型和值数组
        int totalParamCount = businessParams.size() + validationContextParams.size();
        if (config.isIncludeHttpRequest()) {
            totalParamCount += 1; // +1 for HttpServletRequest
        }

        Class<?>[] types = new Class<?>[totalParamCount];
        Object[] values = new Object[totalParamCount - (config.isIncludeHttpRequest() ? 1 : 0)];

        int typeIndex = 0;
        int valueIndex = 0;

        // 添加HttpServletRequest（如果需要）
        if (config.isIncludeHttpRequest()) {
            types[typeIndex++] = javax.servlet.http.HttpServletRequest.class;
            // HttpServletRequest不需要在values数组中，会自动注入
        }

        // 添加普通业务参数
        for (ParameterConfig businessParam : businessParams) {
            types[typeIndex++] = businessParam.getType();
            values[valueIndex++] = businessParam.getValue();
        }

        // 添加验证上下文参数
        for (int i = 0; i < validationContextTypes.size(); i++) {
            types[typeIndex++] = validationContextTypes.get(i);
            values[valueIndex++] = validationContextParams.get(i);
        }

        // 更新配置
        config.setCustomParameterTypes(types);
        config.setCustomParameterValues(values);

        // 设置请求参数
        if (!requestParams.isEmpty()) {
            config.setCallParams(requestParams);
        }

        logger.debug("参数解析完成: totalTypes={}, totalValues={}, requestParams={}",
                types.length, values.length, requestParams.size());
    }

    /**
     * 获取 Controller 信息（必须显式配置）
     *
     * @param config 业务步骤配置
     * @return Controller 信息
     */
    private ControllerInfo getControllerInfo(BusinessStepConfig config) {
        // 必须显式配置 Controller 信息
        if (config.getControllerBeanName() == null || config.getControllerMethodName() == null) {
            throw new IllegalArgumentException("必须通过 .controller(beanName, methodName) 显式配置 Controller 信息");
        }

        logger.debug("使用显式配置的 Controller 信息: controllerBeanName={}, methodName={}",
                config.getControllerBeanName(), config.getControllerMethodName());
        return new ControllerInfo(config.getControllerBeanName(), config.getControllerMethodName());
    }


    /**
     * Controller 信息封装类
     */
    private static class ControllerInfo {
        private final String controllerBeanName;
        private final String methodName;

        public ControllerInfo(String controllerBeanName, String methodName) {
            this.controllerBeanName = controllerBeanName;
            this.methodName = methodName;
        }

        public String getControllerBeanName() {
            return controllerBeanName;
        }

        public String getMethodName() {
            return methodName;
        }
    }


    /**
     * 应用默认配置
     */
    private <T, R> void applyDefaultConfig(BusinessExecutionBuilder<T, R> builder, BusinessStepConfig defaultConfig) {
        BusinessStepConfig builderConfig = builder.getStepConfig();

        // 只应用未设置的配置项
        if (builderConfig.isSkipValidation() != defaultConfig.isSkipValidation()) {
            builderConfig.setSkipValidation(defaultConfig.isSkipValidation());
        }
    }

    /**
     * 更新幂等性记录为成功状态
     * 注意：此方法异常不会影响主业务流程，只记录日志用于监控告警
     */
    private void updateIdempotencySuccess(ApiRequest request, BusinessStepConfig config, InternalCallResult callResult) {
        String idempotencyKey = null;
        try {
            idempotencyKey = extractIdempotencyKey(config);
            if (idempotencyKey != null) {
                String idFieldName = config.getIdempotencyIdField();
                String noFieldName = config.getIdempotencyNoField();
                
                // 从 InternalCallResult 中提取真正的业务响应数据
                Object businessData = callResult != null ? callResult.getData() : null;
                idempotencyHelper.updateIdempotencySuccess(idempotencyKey, businessData, idFieldName, noFieldName);
                logger.debug("更新幂等性记录为成功: key={}, idField={}, noField={}, hasBusinessData={}", 
                    idempotencyKey, idFieldName, noFieldName, businessData != null);
            }
        } catch (Exception e) {
            // 幂等性状态更新失败不应影响主业务成功执行
            // 记录ERROR级别日志用于监控告警，但不抛出异常
            logger.error("IDEMPOTENCY_UPDATE_FAILED: 幂等性成功状态更新失败 - requestId={}, key={}, businessSuccess=true", 
                    request.getRequestId(), idempotencyKey, e);
        }
    }

    /**
     * 更新幂等性记录为失败状态
     * 注意：此方法异常不会影响主业务异常传播，只记录日志用于监控告警
     */
    private void updateIdempotencyFailure(ApiRequest request, BusinessStepConfig config, Exception exception) {
        String idempotencyKey = null;
        try {
            idempotencyKey = extractIdempotencyKey(config);

            if (idempotencyKey != null) {
                idempotencyHelper.updateIdempotencyFailure(idempotencyKey, exception);
                logger.debug("更新幂等性记录为失败: key={}", idempotencyKey);
            }
        } catch (Exception e) {
            // 幂等性失败状态更新异常不应影响主业务异常传播
            // 记录ERROR级别日志用于监控告警，但不抛出异常
            logger.error("IDEMPOTENCY_UPDATE_FAILED: 幂等性失败状态更新失败 - requestId={}, key={}, businessSuccess=false", 
                    request.getRequestId(), idempotencyKey, e);

        }
    }

    /**
     * 构建幂等性响应
     */
    private Object buildIdempotentResponse(Object response, BusinessStepConfig config) {
        try {
            String businessType = config.getBusinessTypeName();
            return idempotencyHelper.buildIdempotentResponse(response, true, businessType);
        } catch (Exception e) {
            logger.warn("构建幂等性响应异常: businessType={}", config.getBusinessTypeName(), e);
            return response; // 返回原始响应
        }
    }

    /**
     * 从配置中提取幂等性键
     */
    private String extractIdempotencyKey(BusinessStepConfig config) {
        if (config.getIdempotencyContext() != null) {
            return (String) config.getIdempotencyContext().get("idempotencyKey");
        }
        return null;
    }

    /**
     * 生成或获取幂等性键
     * 统一的幂等性键生成逻辑，避免重复代码
     * 
     * @param request API请求
     * @param config 业务配置
     * @return 幂等性键，如果生成失败返回null
     */
    private String generateOrGetIdempotencyKey(ApiRequest request, BusinessStepConfig config) {
        try {
            // 1. 检查是否已存在键
            String existingKey = extractIdempotencyKey(config);
            if (existingKey != null) {
                logger.debug("使用已存在的幂等性键: key={}", existingKey);
                return existingKey;
            }
            
            // 2. 检查业务类型配置
            String businessType = config.getBusinessTypeName();
            if (businessType == null) {
                logger.warn("业务类型未配置，无法生成幂等性键: requestId={}", request.getRequestId());
                return null;
            }
            
            // 3. 生成新的幂等性键
            String idempotencyKey = keyGenerator.generateIdempotencyKey(request, businessType);
            
            // 4. 确保幂等性上下文存在
            Map<String, Object> idempotencyContext = config.getIdempotencyContext();
            if (idempotencyContext == null) {
                idempotencyContext = new HashMap<>();
                config.setIdempotencyContext(idempotencyContext);
            }
            
            // 5. 存储键到上下文中
            idempotencyContext.put("idempotencyKey", idempotencyKey);
            
            logger.debug("成功生成幂等性键: key={}, businessType={}", idempotencyKey, businessType);
            return idempotencyKey;
            
        } catch (Exception e) {
            logger.error("生成幂等性键失败: requestId={}, businessType={}", 
                request.getRequestId(), config.getBusinessTypeName(), e);
            return null;
        }
    }

    /**
     * 确保幂等性键已生成
     * 在异常处理场景中确保有键可用于状态更新
     *
     * @param request API请求
     * @param config  业务配置
     */
    private void ensureIdempotencyKeyGenerated(ApiRequest request, BusinessStepConfig config) {
        String idempotencyKey = generateOrGetIdempotencyKey(request, config);
        if (idempotencyKey == null) {
            logger.warn("无法确保幂等性键存在: requestId={}, businessType={}", 
                request.getRequestId(), config.getBusinessTypeName());
        }
    }

    /**
     * 验证幂等性配置一致性
     *
     * @param config 业务配置
     */
    private void validateIdempotencyConfig(BusinessStepConfig config) {
        // 简化配置验证，只检查幂等性相关配置
        boolean idempotencyEnabled = config.isIdempotencyHandlingEnabled();
        
        if (idempotencyEnabled && config.getBusinessTypeName() == null) {
            logger.warn("幂等性处理已启用但未配置业务类型，可能导致功能异常");
        }
    }

    /**
     * 检查幂等性预条件
     * 如果发现成功的幂等性记录，直接返回历史结果
     *
     * @param request API请求
     * @param config 业务配置
     * @return 已存在的结果，如果没有返回null
     */
    private Object checkIdempotencyPreCondition(ApiRequest request, BusinessStepConfig config) {
        try {
            String idempotencyKey = generateOrGetIdempotencyKey(request, config);
            if (idempotencyKey == null) {
                logger.warn("无法生成幂等性键，跳过预检查: requestId={}", request.getRequestId());
                return null;
            }

            IdempotencyRecord existingRecord = idempotencyRecordService.findByKey(idempotencyKey);
            if (existingRecord == null) {
                logger.debug("幂等性记录不存在，继续执行业务逻辑: key={}", idempotencyKey);
                return null;
            }

            // 处理不同状态的记录
            IdempotencyStatus status = IdempotencyStatus.fromCode(existingRecord.getStatus());
            
            switch (status) {
                case SUCCESS:
                    logger.info("发现成功的幂等性记录，直接返回历史结果: key={}", idempotencyKey);
                    return buildResponseFromExistingRecord(existingRecord, config);
                    
                case PROCESSING:
                    logger.warn("发现处理中的幂等性记录，可能存在并发或长时间处理: key={}", idempotencyKey);
                    // 可选择等待、报错或继续执行，这里选择继续执行（让数据库处理并发）
                    return null;
                    
                case FAILED:
                    logger.info("发现失败的幂等性记录，逻辑删除失败记录并允许重新执行: key={}", idempotencyKey);
                    // 逻辑删除失败记录，保留历史数据用于问题排查，让后续流程重新创建处理记录
                    try {
                        int deleted = idempotencyRecordService.logicalDeleteByIdempotencyKey(idempotencyKey);
                        logger.debug("逻辑删除失败的幂等性记录: key={}, deletedCount={}", idempotencyKey, deleted);
                    } catch (Exception e) {
                        logger.warn("逻辑删除失败的幂等性记录时发生异常，继续执行: key={}", idempotencyKey, e);
                        // 逻辑删除失败不影响业务继续执行，setupIdempotencyManagement会处理重复记录的情况
                    }
                    return null;
                    
                default:
                    logger.warn("未知的幂等性记录状态: key={}, status={}", idempotencyKey, existingRecord.getStatus());
                    return null;
            }
            
        } catch (Exception e) {
            logger.error("幂等性预检查失败: requestId={}", request.getRequestId(), e);
            // 预检查失败不应影响业务执行，继续正常流程
            return null;
        }
    }

    /**
     * 从已存在的幂等性记录构建响应
     *
     * @param record 幂等性记录
     * @param config 业务配置
     * @return 构建的响应对象
     */
    private Object buildResponseFromExistingRecord(IdempotencyRecord record, BusinessStepConfig config) {
        try {
            // 构造响应数据
            Map<String, Object> responseData = new HashMap<>();
            
            // 1. 添加业务文档信息
            if (!org.springframework.util.StringUtils.isEmpty(record.getBusinessDocumentId())) {
                responseData.put("id", record.getBusinessDocumentId());
                responseData.put("documentId", record.getBusinessDocumentId());
            }
            if (!org.springframework.util.StringUtils.isEmpty(record.getBusinessDocumentNo())) {
                responseData.put("documentNo", record.getBusinessDocumentNo());
            }
            
            // 2. 尝试解析原始响应数据
            if (!org.springframework.util.StringUtils.isEmpty(record.getResponseData())) {
                try {
                    Object originalData = JSON.parseObject(record.getResponseData(), Map.class);
                    if (originalData instanceof Map) {
                        responseData.putAll((Map<String, Object>) originalData);
                    }
                } catch (Exception e) {
                    logger.warn("解析原始响应数据失败，使用基础信息: key={}", record.getIdempotencyKey(), e);
                }
            }
            
            // 3. 使用幂等性助手构建最终响应
            String businessType = config.getBusinessTypeName();
            return idempotencyHelper.buildIdempotentResponse(responseData, false, businessType);
            
        } catch (Exception e) {
            logger.error("从已存在记录构建响应失败: key={}", record.getIdempotencyKey(), e);
            
            // 构建最小响应，避免返回null
            Map<String, Object> minimalResponse = new HashMap<>();
            minimalResponse.put("id", record.getBusinessDocumentId());
            minimalResponse.put("documentNo", record.getBusinessDocumentNo());
            minimalResponse.put("message", "幂等性记录已存在，但响应构建异常");
            minimalResponse.put("isNewCreated", false);
            return minimalResponse;
        }
    }

}
