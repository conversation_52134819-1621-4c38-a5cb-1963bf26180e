package com.vedeng.ordergoods.model;

public class SpuMigratedLogDomain {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.SPU_MIGTATION_LOG_ID
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Integer spuMigtationLogId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.SPU_ID
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.SPU_NO
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private String spuNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.CATEGORY_ID_PRE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Integer categoryIdPre;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.CATEGORY_NAME_PRE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private String categoryNamePre;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.CATEGORY_ID_AFTER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Integer categoryIdAfter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.CATEGORY_NAME_AFTER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private String categoryNameAfter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.ORIGIN_PATH
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private String originPath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.TARGET_PATH
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private String targetPath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.MIGRATION_REASON
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private String migrationReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.ADD_ATTRS
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private String addAttrs;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.ADD_TIME
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.CREATOR
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.UPDATE_TIME
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Long updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.UPDATER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SPU_MIGRATION_LOG.IS_DELETE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    private Byte isDelete;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.SPU_MIGTATION_LOG_ID
     *
     * @return the value of T_SPU_MIGRATION_LOG.SPU_MIGTATION_LOG_ID
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Integer getSpuMigtationLogId() {
        return spuMigtationLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.SPU_MIGTATION_LOG_ID
     *
     * @param spuMigtationLogId the value for T_SPU_MIGRATION_LOG.SPU_MIGTATION_LOG_ID
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setSpuMigtationLogId(Integer spuMigtationLogId) {
        this.spuMigtationLogId = spuMigtationLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.SPU_ID
     *
     * @return the value of T_SPU_MIGRATION_LOG.SPU_ID
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Integer getSpuId() {
        return spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.SPU_ID
     *
     * @param spuId the value for T_SPU_MIGRATION_LOG.SPU_ID
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.SPU_NO
     *
     * @return the value of T_SPU_MIGRATION_LOG.SPU_NO
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public String getSpuNo() {
        return spuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.SPU_NO
     *
     * @param spuNo the value for T_SPU_MIGRATION_LOG.SPU_NO
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setSpuNo(String spuNo) {
        this.spuNo = spuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.CATEGORY_ID_PRE
     *
     * @return the value of T_SPU_MIGRATION_LOG.CATEGORY_ID_PRE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Integer getCategoryIdPre() {
        return categoryIdPre;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.CATEGORY_ID_PRE
     *
     * @param categoryIdPre the value for T_SPU_MIGRATION_LOG.CATEGORY_ID_PRE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setCategoryIdPre(Integer categoryIdPre) {
        this.categoryIdPre = categoryIdPre;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.CATEGORY_NAME_PRE
     *
     * @return the value of T_SPU_MIGRATION_LOG.CATEGORY_NAME_PRE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public String getCategoryNamePre() {
        return categoryNamePre;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.CATEGORY_NAME_PRE
     *
     * @param categoryNamePre the value for T_SPU_MIGRATION_LOG.CATEGORY_NAME_PRE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setCategoryNamePre(String categoryNamePre) {
        this.categoryNamePre = categoryNamePre;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.CATEGORY_ID_AFTER
     *
     * @return the value of T_SPU_MIGRATION_LOG.CATEGORY_ID_AFTER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Integer getCategoryIdAfter() {
        return categoryIdAfter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.CATEGORY_ID_AFTER
     *
     * @param categoryIdAfter the value for T_SPU_MIGRATION_LOG.CATEGORY_ID_AFTER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setCategoryIdAfter(Integer categoryIdAfter) {
        this.categoryIdAfter = categoryIdAfter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.CATEGORY_NAME_AFTER
     *
     * @return the value of T_SPU_MIGRATION_LOG.CATEGORY_NAME_AFTER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public String getCategoryNameAfter() {
        return categoryNameAfter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.CATEGORY_NAME_AFTER
     *
     * @param categoryNameAfter the value for T_SPU_MIGRATION_LOG.CATEGORY_NAME_AFTER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setCategoryNameAfter(String categoryNameAfter) {
        this.categoryNameAfter = categoryNameAfter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.ORIGIN_PATH
     *
     * @return the value of T_SPU_MIGRATION_LOG.ORIGIN_PATH
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public String getOriginPath() {
        return originPath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.ORIGIN_PATH
     *
     * @param originPath the value for T_SPU_MIGRATION_LOG.ORIGIN_PATH
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setOriginPath(String originPath) {
        this.originPath = originPath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.TARGET_PATH
     *
     * @return the value of T_SPU_MIGRATION_LOG.TARGET_PATH
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public String getTargetPath() {
        return targetPath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.TARGET_PATH
     *
     * @param targetPath the value for T_SPU_MIGRATION_LOG.TARGET_PATH
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setTargetPath(String targetPath) {
        this.targetPath = targetPath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.MIGRATION_REASON
     *
     * @return the value of T_SPU_MIGRATION_LOG.MIGRATION_REASON
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public String getMigrationReason() {
        return migrationReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.MIGRATION_REASON
     *
     * @param migrationReason the value for T_SPU_MIGRATION_LOG.MIGRATION_REASON
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setMigrationReason(String migrationReason) {
        this.migrationReason = migrationReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.ADD_ATTRS
     *
     * @return the value of T_SPU_MIGRATION_LOG.ADD_ATTRS
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public String getAddAttrs() {
        return addAttrs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.ADD_ATTRS
     *
     * @param addAttrs the value for T_SPU_MIGRATION_LOG.ADD_ATTRS
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setAddAttrs(String addAttrs) {
        this.addAttrs = addAttrs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.ADD_TIME
     *
     * @return the value of T_SPU_MIGRATION_LOG.ADD_TIME
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.ADD_TIME
     *
     * @param addTime the value for T_SPU_MIGRATION_LOG.ADD_TIME
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.CREATOR
     *
     * @return the value of T_SPU_MIGRATION_LOG.CREATOR
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.CREATOR
     *
     * @param creator the value for T_SPU_MIGRATION_LOG.CREATOR
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.UPDATE_TIME
     *
     * @return the value of T_SPU_MIGRATION_LOG.UPDATE_TIME
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.UPDATE_TIME
     *
     * @param updateTime the value for T_SPU_MIGRATION_LOG.UPDATE_TIME
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.UPDATER
     *
     * @return the value of T_SPU_MIGRATION_LOG.UPDATER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.UPDATER
     *
     * @param updater the value for T_SPU_MIGRATION_LOG.UPDATER
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SPU_MIGRATION_LOG.IS_DELETE
     *
     * @return the value of T_SPU_MIGRATION_LOG.IS_DELETE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SPU_MIGRATION_LOG.IS_DELETE
     *
     * @param isDelete the value for T_SPU_MIGRATION_LOG.IS_DELETE
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }
}