package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 销售换货入库
 */

@Service
@Slf4j
public class BatchSaleExchangeInProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;


    @Override
    public KingDeeStorageInDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        KingDeeStorageInDto dto = new KingDeeStorageInDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("销售换货入库,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        // 补充详细单数据
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));
        log.info("销售换货入库 SaleExchangeInProcessorService.process：" + JSON.toJSONString(batchWarehouseGoodsOutInDto));
        // 根据入库单关系 获取售后单
        BatchAfterSalesDto batchAfterSalesDto = batchAfterSalesDtoMapper.findSaleByAfterSalesNoAndSubjectType(
                batchWarehouseGoodsOutInDto.getRelateNo(), 535);
        if (Objects.isNull(batchAfterSalesDto)) {
            return null;
        }
        if (batchAfterSalesDto.getBatchAfterSalesGoodsDtoList()==null){
            log.error("销售换货入库,数据没有商品:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        Map<Integer, BatchAfterSalesGoodsDto> map = batchAfterSalesDto.getBatchAfterSalesGoodsDtoList()
                .stream().collect(Collectors.toMap(BatchAfterSalesGoodsDto::getAfterSalesGoodsId, c -> c, (k1, k2) -> k1));

        dto.setFId("0");
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFStockDirect("GENERAL");
        dto.setFQzokKh(batchAfterSalesDto.getTraderCustomerId().toString());
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }

        List<KingDeeStorageInDetailDto> detailDtoList = new ArrayList<>();
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l -> {
            KingDeeStorageInDetailDto detailDto = new KingDeeStorageInDetailDto();
            BatchAfterSalesGoodsDto afterSalesGoodsDto = map.get(l.getRelatedId());
            detailDto.setFMaterialId(afterSalesGoodsDto.getSku());
            detailDto.setFStockId("CK9997");
            detailDto.setFQty(l.getNum().toString());
            detailDto.setFPrice("0.01");
            detailDto.setFAmount(new BigDecimal(detailDto.getFQty()).multiply(new BigDecimal(detailDto.getFPrice())).toString());
            detailDto.setFQzokYsddh(batchAfterSalesDto.getOrderNo());
            detailDto.setFQzokGsywdh(batchAfterSalesDto.getAfterSalesNo());
            detailDto.setFQzokYwlx("销售换货");
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            detailDto.setFQzokSfzf(afterSalesGoodsDto.getDeliveryDirect().equals(0) ? "否" : "是");
            detailDto.setFQzokBddjhId(l.getWarehouseGoodsOutInDetailId().toString());

            detailDtoList.add(detailDto);
        });
        dto.setFEntity(detailDtoList);
        return dto;
    }

}
