package com.wms.task;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsCoreSku;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @ClassName PutSkuToWmsTask
 * @Description 商品档案下传WMS job（全量大概5w条数据）
 * @Date 2020/7/17 14:12
 */
@Component
@JobHandler(value="PutSkuToWmsTask")
public class PutSkuToWmsTask extends AbstractJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PutSkuToWmsTask.class);

    private volatile AtomicInteger totalNum =new AtomicInteger(0);

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        if(StringUtils.isNotEmpty(param)){
            try {

                List<WmsCoreSku> coreSkuList = coreSkuGenerateMapper.getSkuListBySkuNoStr(param);

                if(CollectionUtils.isEmpty(coreSkuList)){
                    XxlJobLogger.log("未找到对应的sku 直接返回");
                    LOGGER.info("未找到对应的sku 直接返回");
                    return SUCCESS;
                }

                putSku(coreSkuList);
                return SUCCESS;

            }catch (Exception e){
                LOGGER.error("下传SKU失败:",e);
                LOGGER.info("下传SKU失败:{}",e.getMessage());
                return FAIL;
            }
        }

        LOGGER.info("商品档案下传WMS-->job开始");
        XxlJobLogger.log("PutSkuToWmsTask start ."+ param);
        List<WmsCoreSku> skuList=coreSkuGenerateMapper.getWmsCoreSkuList();
        XxlJobLogger.log("skuList size:{}",skuList.size());
        Long start=System.currentTimeMillis();
        if (CollectionUtils.isEmpty(skuList)){
            LOGGER.info("获取商品档案列表为空");
            XxlJobLogger.log("获取商品档案列表为空");
            return SUCCESS;
        }
        ExecutorService exec = getExecutorService(skuList);
        exec.shutdown();
        while (!exec.isTerminated()) {
            //等待线程池中的所有任务都结束，才退出主线程
            //当前线程休眠一段时间 让出cpu资源，防止循环过度消耗
            Thread.currentThread().sleep(100);
        }
        XxlJobLogger.log("totalNum :{}",totalNum.intValue());
        XxlJobLogger.log("PutSkuToWmsTask end ."+param);
        totalNum.set(0);
        LOGGER.info("商品档案下传WMS-->job结束,总耗时：{} ms",(System.currentTimeMillis()-start));
        return SUCCESS;
    }
    /**
     * 分割数据,构造任务,创建线程池
     * @Param: [skuList]
     * @Return: java.util.concurrent.ExecutorService
     * @Author: Rivan
     * @Date: 2020/7/27 10:02
     */
    private ExecutorService getExecutorService(List<WmsCoreSku> skuList) {
        //每个定时执行多少记录
        int skuListSize = skuList.size();
        //线程池大小
        int poolnum = 5;
        //每个线程执行多少记录
        int perThreadNum = 1000/poolnum;
        //实际线程池+1
        ExecutorService exec = Executors.newFixedThreadPool(poolnum+1);
        int count = 1;
        //实际开启线程个数
        if(perThreadNum > 0){
            count = skuListSize/perThreadNum +1;
        }
        if( count == ( poolnum + 1 )){
            count = poolnum;
        }
        //最多1000条记录，分最多10个线程，每个线程最多100个记录
        for (int i = 1; i <= count; i++) {
            if(i == count){
                exec.execute(putSkuRunnable(skuList.subList((i-1)*perThreadNum, skuListSize)));
            }else{
                exec.execute(putSkuRunnable(skuList.subList((i-1)*perThreadNum, i*perThreadNum)));
            }
        }
        return exec;
    }

    /**
     * 构造线程任务
     * @Param: [skuList]
     * @Return: java.lang.Runnable
     * @Author: Rivan
     * @Date: 2020/7/27 10:02
     */
    private Runnable putSkuRunnable(List<WmsCoreSku> skuList) {
        return () -> putSku(skuList);
    }

    /**
     * 下传sku
     * @Param: [skuList]
     * @Return: void
     * @Author: Rivan
     * @Date: 2020/8/7 15:01
     */
    private void putSku(List<WmsCoreSku> skuList) {
        totalNum.addAndGet(skuList.size());
        //商品档案
        try {
            LOGGER.info("ERP下传商品档案本批次数量:" + skuList.size());
            XxlJobLogger.log("ERP下传商品档案本批次数量：{}" , skuList.size());
            WmsInterface putSkuInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_SKU);
            WmsResponse response = putSkuInterface.request(skuList.toArray());
            LOGGER.info("ERP下传商品档案至WMS的响应:" + JSON.toJSONString(response));
            XxlJobLogger.log("ERP下传商品档案至WMS的响应:"+JSON.toJSONString(response));
        }catch (Exception e){
            LOGGER.error("ERP下传商品档案至WMS请求接口报错",e);
            XxlJobLogger.log("ERP下传商品档案至WMS请求接口报错:{}",e);
        }
    }
}
