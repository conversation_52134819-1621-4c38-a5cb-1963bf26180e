<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:apollo="http://www.ctrip.com/schema/apollo" xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd
		http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit.xsd">

    <!--rabbit连接-->
    <rabbit:connection-factory
            id="opConnectionFactory"
            addresses="${mjx.rabbitmq.host}"
            username="${mjx.rabbitmq.username}"
            password="${mjx.rabbitmq.password}"
            virtual-host="${op.rabbitmq.virtualHost}"
            channel-cache-size="100"
            publisher-returns="true"
            publisher-confirms="true" />

    <!-- 定义mq管理 -->
    <rabbit:admin connection-factory="opConnectionFactory" />
    <rabbit:template id="opRabbitTemplate"
                     connection-factory="opConnectionFactory" message-converter="messageConverter" />


    <!--定义queue  说明：durable:是否持久化 exclusive: 仅创建者可以使用的私有队列，断开后自动删除 auto_delete: 当所有消费客户端连接断开后，是否自动删除队列-->
    <rabbit:queue name="goods_isonsale_change_queue" durable="true" auto-delete="false" exclusive="false" />
    <!--定义direct-exchange（一对一） -->
    <rabbit:direct-exchange name="goods_onsale_exchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="goods_isonsale_change_queue" key="goods_isonsale_change" />
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <rabbit:fanout-exchange name="trader_nature_exchange"  durable="true"  auto-delete="false"></rabbit:fanout-exchange>
    <rabbit:fanout-exchange name="trader_aptitude_status_exchange"  durable="true"  auto-delete="false"></rabbit:fanout-exchange>
    <rabbit:fanout-exchange name="trader_belongplatform_exchange"  durable="true"  auto-delete="false"></rabbit:fanout-exchange>
    <rabbit:fanout-exchange name="trader_link_account_exchange"  durable="true"  auto-delete="false"></rabbit:fanout-exchange>


    <rabbit:listener-container connection-factory="opConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="skuOnSaleConsumer" queues="goods_isonsale_change_queue"></rabbit:listener>
    </rabbit:listener-container>


    <rabbit:fanout-exchange name="hotWordToErpExchange"  durable="true"  auto-delete="false"></rabbit:fanout-exchange>
    <rabbit:queue name="hotWordToErpQueue" durable="true" auto-delete="false" exclusive="false" />
    <rabbit:listener-container connection-factory="opConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="hostSkuConsumer" queues="hotWordToErpQueue"></rabbit:listener>
    </rabbit:listener-container>

</beans>
