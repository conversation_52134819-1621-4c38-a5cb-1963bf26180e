package com.vedeng.erp.aftersale.service;

import com.vedeng.orderstream.aftersales.model.AfterSalesRevenueRecord;

import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/13 20:21
 * @desc :
 */
public interface AfterSalesRevenueRecordService {

    /**
     * 新增收入记录
     * @param afterSalesRevenueRecord
     * @return
     */
    int insert(AfterSalesRevenueRecord afterSalesRevenueRecord);

    /**
     * 查询收入记录
     * @param afterSalesId
     * @return
     */
    List<AfterSalesRevenueRecord> selectByAfterSalesId(Integer afterSalesId);
}
