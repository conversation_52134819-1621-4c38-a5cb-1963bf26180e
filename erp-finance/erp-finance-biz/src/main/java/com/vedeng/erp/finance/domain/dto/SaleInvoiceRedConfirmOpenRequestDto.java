package com.vedeng.erp.finance.domain.dto;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.utils.TaxesUtil;
import lombok.Data;

/**
 * 红字确认单开票入参
 */
@Data
public class SaleInvoiceRedConfirmOpenRequestDto implements ITaxesParam {

    /**
     * 纳税人识别号(销方)
     */
    private String nsrsbh;

    /** 红字信息表id*/
    private String uuid;

    public SaleInvoiceRedConfirmOpenRequestDto(String uuid){
        this.nsrsbh = TaxesUtil.taxesConfig.taxNo;
        this.uuid = uuid;
    }
}
