package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeAllocationCommand;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @description:调拨单
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Mapper(componentModel = "spring")
public interface KingDeeAllocationCommandConvertor extends BaseCommandMapStruct<KingDeeAllocationCommand, KingDeeAllocationDto> {

    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FTransferDirect", source = "FTransferDirect")
    @Mapping(target = "FTransferBizType", source = "FTransferBizType")
    @Mapping(target = "FStockOutOrgId.FNumber", source = "FStockOutOrgId")
    @Mapping(target = "FOwnerTypeOutIdHead", source = "FOwnerTypeOutIdHead")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "f_QZOK_JDR", source = "FQzokJdr")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    KingDeeAllocationCommand toCommand(KingDeeAllocationDto dto);

    @Mapping(target = "FMaterialId.FNumber", source = "FMaterialId")
    @Mapping(target = "FQty", source = "FQty")
    @Mapping(target = "FSrcStockId.FNumber", source = "FSrcStockId")
    @Mapping(target = "FDestStockId.FNumber", source = "FDestStockId")
    @Mapping(target = "f_QZOK_JDKHID", source = "FQzokJdkhid")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    KingDeeAllocationCommand.KingDeeAllocationDetailCommand toCommand(KingDeeAllocationDetailDto dto);
}
