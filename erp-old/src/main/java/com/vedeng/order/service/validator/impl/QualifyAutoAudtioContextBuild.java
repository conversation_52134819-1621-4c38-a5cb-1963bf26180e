package com.vedeng.order.service.validator.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.dao.ProductCompanyMapper;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.model.ProductCompany;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.goods.dao.CoreSpuGenerateMapper;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.trader.dao.TraderCertificateMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.TraderMedicalCategoryMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderMedicalCategory;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import com.vedeng.trader.model.vo.TraderMedicalCategoryVo;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 构建上下文信息
 */
@Service
public class QualifyAutoAudtioContextBuild {

    public static Logger logger = LoggerFactory.getLogger(QualifyAutoAudtioContextBuild.class);

    @Resource
    private CoreSpuGenerateMapper coreSpuGenerateMapper;

    @Resource
    private TraderCertificateMapper traderCertificateMapper;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private FirstEngageService firstEngageService;

    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Resource
    private ProductCompanyMapper productCompanyMapper;

    @Resource
    private RegistrationNumberMapper registrationNumberMapper;

    @Resource
    private TraderMedicalCategoryMapper traderMedicalCategoryMapper;


    /**
     * 构建可能需要的应用上下文
     * @param buyOrderGoods
     */
    public void buildContext(BuyorderGoodsVo buyOrderGoods) {

        //构建供应商信息
        Trader trader = ThreadLocalContext.get(QualifyAutoAudtioUtil.getTraderInfoKey());

        if(trader == null){
            logger.info("采购单自动审核 threadlocal trader== null {} {}" ,buyOrderGoods.getBuyorderId(),buyOrderGoods.getBuyorderGoodsId() );
            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyOrderGoods.getBuyorderId());
            buildTraderRelateInfo(buyorder.getTraderId());
        }
        logger.info("采购单自动审核 threadlocal buildSpuRelateInfo  {} {}" ,buyOrderGoods.getBuyorderId(),buyOrderGoods.getBuyorderGoodsId() );

        buildSpuRelateInfo(buyOrderGoods.getSku());
    }
    
    /**
     * 基于traderId和skuNo构建上下文
     * @param traderId 交易员ID
     * @param skuNo 商品编号
     */
    public void buildContext(Integer traderId, String skuNo) {
        // 构建供应商信息
        Trader trader = ThreadLocalContext.get(QualifyAutoAudtioUtil.getTraderInfoKey());

        if(trader == null){
            logger.info("采购单自动审核 threadlocal trader== null，traderId:{}, skuNo:{}" , traderId, skuNo);
            buildTraderRelateInfo(traderId);
        }
        logger.info("采购单自动审核 threadlocal buildSpuRelateInfo traderId:{}, skuNo:{}" , traderId, skuNo);

        buildSpuRelateInfo(skuNo);
    }

    /**
     * 构建SPU的相关信息
     */
    private void buildSpuRelateInfo(String skuNo){

        //查询SPU相关信息
        CoreSpuGenerate spuInfo = coreSpuGenerateMapper.selectSpuInfoBySkuNo(skuNo);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getSpuInfoKey(skuNo),spuInfo);

        // 首营信息
        if (spuInfo.getFirstEngageId() !=null && spuInfo.getFirstEngageId() > 0) {

            FirstEngage firstEngage = this.firstEngageMapper.selectByPrimaryKey(spuInfo.getFirstEngageId());
             if(firstEngage!=null&&ErpConst.CHECK_STATUS_STATE.APPROVED.equals(firstEngage.getStatus())){
                ThreadLocalContext.put(QualifyAutoAudtioUtil.getFirstEngageInfoKey(skuNo),firstEngage);
                // 注册证信息
                RegistrationNumber registrationNumber = registrationNumberMapper.getRegistrationNumberByFirstEngageId(spuInfo.getFirstEngageId());

                if(registrationNumber != null){
                    // 企业信息
                    ProductCompany productCompany = productCompanyMapper.selectByPrimaryKey(registrationNumber.getProductCompanyId());
                    registrationNumber.setProductCompany(productCompany);
                }
                ThreadLocalContext.put(QualifyAutoAudtioUtil.getRegisterCertificateInfoKey(skuNo),registrationNumber);
             }
        }

        //是否有注册证
        boolean hasRegistrationCert = false;

        if (spuInfo.getFirstEngageId() !=null && spuInfo.getFirstEngageId() > 0 ) {
            // 根据首营编号获取其关联的注册证
            hasRegistrationCert = firstEngageService.associateWithRegistrationCert(spuInfo.getFirstEngageId());
        }
        ThreadLocalContext.put(QualifyAutoAudtioUtil.hasRegistrationCertKey(skuNo), hasRegistrationCert);
    }

    /**
     * 构建供应商信息
     * @param traderId
     */
    private void buildTraderRelateInfo(Integer traderId){

        ThreadLocalContext.put(QualifyAutoAudtioUtil.getTraderInfoKey(),this.traderMapper.selectByPrimaryKey(traderId));

        // 营业执照
        TraderCertificateVo traderCertificate = new TraderCertificateVo();
        traderCertificate.setTraderId(traderId);
        traderCertificate.setTraderType(ErpConst.TWO);
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_25);

        //营业执照是否上传
        List<TraderCertificateVo> businessList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getBusinessLicenseUploadKey(traderId), CollectionUtils.isNotEmpty(businessList));
        try {
            logger.info("采购单自动审核 营业执照是否上传 {} info-->: {}"   ,traderId, JsonUtils.translateToJson(businessList));
        } catch (Exception e) {
            //
        }
        //营业执照是否有效
        boolean isBusinessValid = businessList.stream().anyMatch(business -> business.getEndtime() == null
                || business.getEndtime() == 0
                || business.getEndtime() >= System.currentTimeMillis());
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getBusinessValidKey(traderId), isBusinessValid);
        logger.info("采购单自动审核 营业执照是否有效 {} info-->: {}"   ,traderId,isBusinessValid);
        //营业执照是否含有医疗器械
        boolean isBusinessMedical = businessList.stream().anyMatch(business -> business.getIsMedical() == 1);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getBusinessMedicalKey(traderId), isBusinessMedical);
        logger.info("采购单自动审核 营业执照是否含有医疗器械 {} info-->: {}"   ,traderId,isBusinessMedical);
        //医疗器械二类备案凭证
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_28);
        List<TraderCertificateVo> twoMedicalList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
        try {
            logger.info("采购单自动审核 医疗器械二类备案凭证 {} info-->{}:"   ,traderId, JsonUtils.translateToJson(twoMedicalList));
        } catch ( Exception e) {
           //
        }

        ThreadLocalContext.put(QualifyAutoAudtioUtil.getSecondCategoryListKey(traderId), twoMedicalList);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getSecondCategoryUploadKey(traderId), CollectionUtils.isNotEmpty(twoMedicalList));

        //医疗器械经营许可证（三类）
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_29);
        List<TraderCertificateVo> threeMedicalList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
        try {
            logger.info("采购单自动审核 医疗器械经营许可证（三类） {} info-->: {}"   ,traderId, JsonUtils.translateToJson(threeMedicalList));
        } catch ( Exception e) {
           //
        }

        ThreadLocalContext.put(QualifyAutoAudtioUtil.getThreeMedicalLicenseListKey(traderId), threeMedicalList);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getThreeMedicalLicenseUploadKey(traderId), CollectionUtils.isNotEmpty(threeMedicalList));

        // 医疗资质信息
        TraderMedicalCategory traderMedicalCategory = new TraderMedicalCategory();
        traderMedicalCategory.setTraderId(traderId);
        traderMedicalCategory.setTraderType(ErpConst.TWO);

        //医疗器械二类备案凭证详情（旧国标）
        traderMedicalCategory.setMedicalCategoryLevel(239);
        List<TraderMedicalCategoryVo> oldSecondCategoryList = traderMedicalCategoryMapper.getTraderMedicalCategoryList(traderMedicalCategory);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getOldSecondCategoryListKey(traderId), oldSecondCategoryList);

        //医疗器械二类备案凭证详情（新国标）
        traderMedicalCategory.setMedicalCategoryLevel(250);
        List<TraderMedicalCategoryVo> newSecondCategoryList = traderMedicalCategoryMapper.getTraderMedicalCategoryList(traderMedicalCategory);

        logger.info("采购单自动审核 医疗器械二类备案凭证详情（新国标） {} info-->: {}"   ,traderId, CollectionUtils.isNotEmpty(newSecondCategoryList));


        ThreadLocalContext.put(QualifyAutoAudtioUtil.getNewSecondCategoryListKey(traderId), newSecondCategoryList);

        //医疗器械经营许可证详情 （三类）（旧国标）
        traderMedicalCategory.setMedicalCategoryLevel(240);
        List<TraderMedicalCategoryVo> oldThirdCategoryList = traderMedicalCategoryMapper.getTraderMedicalCategoryList(traderMedicalCategory);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getOldThirdCategoryListKey(traderId), oldThirdCategoryList);

        //医疗器械经营许可证详情（三类）（新国标）
        traderMedicalCategory.setMedicalCategoryLevel(251);
        List<TraderMedicalCategoryVo> newThirdCategoryList = traderMedicalCategoryMapper.getTraderMedicalCategoryList(traderMedicalCategory);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getNewThirdCategoryListKey(traderId), newThirdCategoryList);

        //第一类医疗器械生产备案凭证
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_1101);
        List<TraderCertificateVo> firstCategoryCertificateList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getFirstCategoryListKey(traderId), firstCategoryCertificateList);

        //医疗器械生产许可证
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_439);
        List<TraderCertificateVo> productPermissionList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getProductPermissionListKey(traderId), productPermissionList);

        //生产企业生产产品登记表
        traderCertificate.setSysOptionDefinitionId(SysOptionConstant.ID_1102);
        List<TraderCertificateVo> productRegistrationList = traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
        ThreadLocalContext.put(QualifyAutoAudtioUtil.getProductRegistrationListKey(traderId), productRegistrationList);
    }
}
