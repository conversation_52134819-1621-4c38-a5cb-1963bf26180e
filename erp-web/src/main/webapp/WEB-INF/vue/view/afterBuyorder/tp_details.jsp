<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/tailwind/tailwind.min.css">

<div id="app" style="display: none;">
    <br>
    <br>
        <%--进度条--%>
        <div class="container mx-auto">
            <el-steps  finish-status="success" align-center>
                <template v-for="(item, index) in items">
                    <el-step :title="item.title" :status="item.type">
                        <template slot="icon">
                            <div class="el-step__icon-inner">&nbsp;</div>
                        </template>
                    </el-step>
                </template>
            </el-steps>
        </div>
    <br>
    <br>
    <el-row style="text-align: center; margin-bottom: 20px">
        <el-button type="primary" size="small" v-if="afterSalesDto.atferSalesStatus != 2 && afterSalesDto.atferSalesStatus != 3
                                                    && afterSalesDto.status != 1 && afterSalesDto.invoiceRefundStatus == 1
                                                    && isOperator" @click="closeAfterSale()">关闭订单</el-button>

        <el-button type="primary" size="small"
                   v-if="afterSalesDto.atferSalesStatus != 2 && afterSalesDto.atferSalesStatus != 3
                                                    && afterSalesDto.status != 1 && afterSalesDto.invoiceRefundStatus == 1
                                                    && isOperator"
                   @click="editAfterSale()">编辑订单</el-button>

        <el-button type="primary" size="small" v-if="afterSalesDto.atferSalesStatus == 0 && isOperator &&
                    afterSalesDto.status != 1"
                   @click="applyValid()">申请审核</el-button>

        <el-button type="primary" size="small" v-if="afterSalesDto.status == 1 && isCheckUser"
                   @click="auditRecord(true)">审核通过</el-button>

        <el-button  type="primary" size="small" v-if="afterSalesDto.status == 1 && isCheckUser"
                    @click="auditRecord(false)">审核不通过</el-button>

        <el-button type="info" disabled size="small" v-if="afterSalesDto.status == 1 && isOperator && !isCheckUser">已申请审核</el-button>
    </el-row>
<%--带边框列表--基本信息--%>
    <el-descriptions title="基本信息" label-class-name="my-title" column="2" border>
        <div slot="title" style="width: 100%;padding-left: 10px">
            <div>基本信息</div>
        </div>
        <el-descriptions-item label="售后单号" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{afterSalesDto.afterSalesNo}}
        </el-descriptions-item>
        <el-descriptions-item label="售后类型" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span style="color: red">{{afterSalesDto.typeName}}</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建者" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{afterSalesDto.creatorName}}
        </el-descriptions-item>
        <el-descriptions-item label="售后状态" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="afterSalesDto.atferSalesStatus==0">待确认</span>
            <span v-if="afterSalesDto.atferSalesStatus==1">进行中</span>
            <span v-if="afterSalesDto.atferSalesStatus==2">已完结</span>
            <span v-if="afterSalesDto.atferSalesStatus==3">已关闭</span>
        </el-descriptions-item>
        <el-descriptions-item label="审核状态" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="afterSalesDto.status==0">待审核</span>
            <span v-if="afterSalesDto.status==1">审核中</span>
            <span v-if="afterSalesDto.status==2">审核通过</span>
            <span v-if="afterSalesDto.status==3">审核不通过</span>
        </el-descriptions-item>
        <el-descriptions-item label="生效状态" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="afterSalesDto.validStatus==0">未生效</span>
            <span v-if="afterSalesDto.validStatus==1">已生效</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{parseTime(afterSalesDto.addTime)}}
        </el-descriptions-item>
        <el-descriptions-item label="生效时间" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{parseTime(afterSalesDto.validTime)}}
        </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="售后申请" label-class-name="my-title" column="2" border>
        <div slot="title" style="width: 100%;padding-left: 10px">
            <div>订单审核</div>
        </div>
    </el-descriptions>
    <div class="container mx-auto">
        <el-steps finish-status="success" align-center>
            <template v-for="(item, index) in auditItems">
                <el-step :title="item.title" :status="item.type" :description="item.description">
                    <template slot="icon">
                        <div class="el-step__icon-inner">&nbsp;</div>
                    </template>
                    <template slot="description">
                        <p class="break-all" v-if="item.description.length >= 50">
                            {{item.simpleDesc}}
                            <template>
                                <el-popover trigger="hover" placement="right" >
                                <p class="break-all w-28">{{item.description}}</p>
                                <template slot="reference" >
                                    <i class="el-icon-copy-document"></i>
                                </template>
                                </el-popover>
                            </template>
                            <template slot="reference" >
                                <i class="el-icon-copy-document"></i>
                            </template>
                        </p>
                        <p v-if="item.description.length < 50">
                            {{item.description}}
                        </p>
                        <p>
                            {{item.verifyTime}}
                        </p>
                        <p class="break-all" v-if="item.remark.length >= 40">
                            {{item.simpleRemark}}
                            <template>
                                <el-popover trigger="hover" placement="right" >
                                    <p class="break-all w-28">{{item.remark}}</p>
                                    <template slot="reference" >
                                        <i class="el-icon-copy-document"></i>
                                    </template>
                                    </el-popover>
                            </template>
                            <template slot="reference" >
                                <i class="el-icon-copy-document"></i>
                            </template>
                        </p>
                        <p v-if="item.remark.length < 40">
                            {{item.remark}}
                        </p>
                    </el-step>
                </template>
            </template>
        </el-steps>
    </div>
    <el-descriptions title="售后申请" label-class-name="my-title" column="2" border>
        <div slot="title" style="width: 100%;padding-left: 10px">
            <div>售后申请</div>
        </div>
        <el-descriptions-item label="售后原因" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="afterSalesDto.reason==577">供应商开错且已跨月</span>
            <span v-if="afterSalesDto.reason==4258">发票录错</span>
            <span v-if="afterSalesDto.reason==580">其他</span>
        </el-descriptions-item>
        <el-descriptions-item label="发票号" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{afterSalesDto.newInvoiceNo}}
        </el-descriptions-item>
        <el-descriptions-item label="详情说明" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{afterSalesDto.comments}}
        </el-descriptions-item>
        <el-descriptions-item label="发票代码" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{afterSalesDto.newInvoiceCode}}
        </el-descriptions-item>
        <el-descriptions-item label="发票种类" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="afterSalesDto.invoiceType == 688">0%增值税专用发票</span>
            <span v-if="afterSalesDto.invoiceType == 429">17%增值税专用发票</span>
            <span v-if="afterSalesDto.invoiceType == 430">17%增值税普通发票</span>
            <span v-if="afterSalesDto.invoiceType == 681">16%增值税普通发票</span>
            <span v-if="afterSalesDto.invoiceType == 682">16%增值税专用发票</span>
            <span v-if="afterSalesDto.invoiceType == 683">6%增值税普通发票</span>
            <span v-if="afterSalesDto.invoiceType == 684">6%增值税专用发票</span>
            <span v-if="afterSalesDto.invoiceType == 685">3%增值税普通发票</span>
            <span v-if="afterSalesDto.invoiceType == 686">3%增值税专用发票</span>
            <span v-if="afterSalesDto.invoiceType == 687">0%增值税普通发票</span>
            <span v-if="afterSalesDto.invoiceType == 971">13%增值税普通发票</span>
            <span v-if="afterSalesDto.invoiceType == 972">13%增值税专用发票</span>
            <span v-if="afterSalesDto.invoiceType == 1758">1%增值税专用发票</span>
            <span v-if="afterSalesDto.invoiceType == 4071">1%增值税普通发票</span>
        </el-descriptions-item>
        <el-descriptions-item label="附件" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <el-link type="primary" :underline="false" v-for="item in afterSalesDto.attachmentList" @click="viewAttachment(item.domain, item.uri)">
                {{item.name}}&nbsp;&nbsp;&nbsp;&nbsp;
            </el-link>
        </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="所属订单" label-class-name="my-title" column="2" border>
        <div slot="title" style="width: 100%;padding-left: 10px">
            <div>所属订单</div>
        </div>
        <el-descriptions-item label="采购单号" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <el-link type="primary" :underline="false"
                     @click="viewBuyorder(buyorderInfo.buyorderId)">
                {{buyorderInfo.buyorderNo}}
            </el-link>
        </el-descriptions-item>
        <el-descriptions-item label="订单金额" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{formatNum(buyorderInfo.totalAmount)}}
        </el-descriptions-item>
        <el-descriptions-item label="部门" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderInfo.orgName}}
        </el-descriptions-item>
        <el-descriptions-item label="创建人" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderInfo.creatorName}}
        </el-descriptions-item>
        <el-descriptions-item label="订单状态" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="buyorderInfo.status==0">待确认</span>
            <span v-if="buyorderInfo.status==1">进行中</span>
            <span v-if="buyorderInfo.status==2">已完结</span>
            <span v-if="buyorderInfo.status==3">已关闭</span>
        </el-descriptions-item>
        <el-descriptions-item label="生效时间" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{parseTime(buyorderInfo.validTime)}}
        </el-descriptions-item>
        <el-descriptions-item label="供应商名称" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <el-link type="primary" :underline="false"
                     @click="viewTrader(buyorderInfo.traderId)">
                {{buyorderInfo.traderName}}
            </el-link>
        </el-descriptions-item>
        <el-descriptions-item label="供应商等级" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="buyorderInfo.grade==null"></span>
            <span v-if="buyorderInfo.grade==59">核心供应商</span>
            <span v-if="buyorderInfo.grade==60">普通供应商</span>
            <span v-if="buyorderInfo.grade==62">临时供应商</span>
        </el-descriptions-item>
    </el-descriptions>

<%--退票商品--%>
    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span>退票商品</span>
        </div>

        <template>
            <el-table :data="afterSalesDto.afterBuyorderGoodsDtoList" border :header-cell-style="{textAlign: 'center'}" style="width: 100%">
                <el-table-column align="center" label="序列号" type="index" width="80">
                </el-table-column>
                <el-table-column align="center" label="产品名称" width="180">
                    <template slot-scope="scope">
                        <p>{{ scope.row.sku }}</p>
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.goodsId)">{{ scope.row.skuName }}</el-link>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="品牌">
                    <template slot-scope="scope">
                        <span>{{scope.row.brandName}}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="型号">
                    <template slot-scope="scope">
                        <span>{{scope.row.model}}</span>
                        <span>{{scope.row.spec}}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="物料编码">
                    <template slot-scope="scope">
                        <span>{{ scope.row.materialCode }}</span>
                    </template>
                </el-table-column>

                <el-table-column align="center" label="采购价">
                    <template slot-scope="scope">
                        <span>{{formatNum(scope.row.buyPrice)}}</span>
                    </template>
                </el-table-column>

                <el-table-column label="采购数量" align="center">
                    <template slot-scope="scope">
                        <span>{{scope.row.buyNum}} </span>
                    </template>
                </el-table-column>

                <el-table-column align="center" label="单位">
                    <template slot-scope="scope">
                        <span>{{scope.row.unitName}}</span>
                    </template>
                </el-table-column>

                <el-table-column align="center" label="本次退票数">
                    <template slot-scope="scope">
                        <span>{{formatNum((scope.row.afterInvoiceNum))}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </el-card>
<%--退票信息--%>
    <template>
        <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
                <span>退票信息</span>
            </div>
            <template>
                <el-table :data="afterSalesDto.returnBuyorderInvoiceDto" border :header-cell-style="{textAlign: 'center'}" :cell-style="{textAlign: 'center'}"
                        :row-style="{height:'30px'}" style="width: 100%">
                    <el-table-column label="发票号" width="180">
                        <template slot-scope="scope">
                            <span>{{ scope.row.invoiceNo }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="发票金额">
                        <template slot-scope="scope">
                            <span>{{formatNum(scope.row.afterInvoiceAmount)}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="票种" width="180">
                        <template slot-scope="scope">
                            <span v-if="scope.row.invoiceType == 688">0%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 429">17%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 430">17%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 681">16%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 682">16%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 683">6%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 684">6%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 685">3%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 686">3%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 687">0%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 971">13%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 972">13%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 1758">1%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 4071">1%增值税普通发票</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="寄送状态">
                        <template slot-scope="scope">
                            <span>未寄送</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="退票状态">
                        <template slot-scope="scope">
                            <span v-if="scope.row.status == 0">未退票</span>
                            <span v-if="scope.row.status == 1" style="color: red">已退票</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-link type="primary" :underline="false"
                                     :disabled="afterSalesDto.invoiceRefundStatus == 3 || afterSalesDto.atferSalesStatus != 1"
                                     v-if="topOrgId == 8 && scope.row.status == 0"
                                     @click="confirmRefundInvoice(scope.row.invoiceNo)">确认退票</el-link>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </el-card>
        <br>
        <br>
    </template>
<%--发票记录--%>
    <template>
        <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
                <span>发票记录</span>
            </div>
            <template>
                <el-table :data="afterSalesDto.invoiceDtoList" border :header-cell-style="{textAlign: 'center'}" :cell-style="{textAlign: 'center'}"
                          :row-style="{height:'30px'}" style="width: 100%">
                    <el-table-column label="发票号" width="180">
                        <template slot-scope="scope">
                            <span>{{ scope.row.invoiceNo }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="票种" width="180">
                        <template slot-scope="scope">
                            <span v-if="scope.row.invoiceType == 688">0%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 429">17%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 430">17%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 681">16%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 682">16%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 683">6%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 684">6%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 685">3%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 686">3%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 687">0%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 971">13%增值税普通发票</span>
                            <span v-if="scope.row.invoiceType == 972">13%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 1758">1%增值税专用发票</span>
                            <span v-if="scope.row.invoiceType == 4071">1%增值税普通发票</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="红蓝字">
                        <template slot-scope="scope">
                            <span v-if="scope.row.colorType==1 && scope.row.isEnable==1">红字有效</span>
                            <span v-if="scope.row.colorType==2 && scope.row.isEnable==1">蓝字有效</span>
                            <span v-if="scope.row.colorType==2 && scope.row.isEnable==0">蓝字作废</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="发票金额">
                        <template slot-scope="scope">
                            <span>{{formatNum(scope.row.amount)}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作人">
                        <template slot-scope="scope">
                            <span>{{scope.row.creatorName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作时间">
                        <template slot-scope="scope">
                            <span>{{parseTime(scope.row.addTime)}}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </el-card>
        <br>
        <br>
    </template>
</div>
<script src="${pageContext.request.contextPath}/static/api/buyorder/tp_detail.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const viewInfo = {
        afterSalesId: '${afterSalesId}',
    };
    const topOrgId = '${topOrgId}';
    const sendThis = (_this) => {
        vm = _this;
    };
    new Vue({
        el: '#app',
        data() {
            return {
                afterSalesDto: {
                    afterBuyorderGoodsDtoList:[],
                    returnBuyorderInvoiceDto: [],
                    invoiceDtoList: []
                },
                taskInfo: null,
                buyorderInfo: {
                },
                // 判断当前用户是否是审核节点候选人
                isCheckUser: false,
                isOperator: false,
                items: [
                    {
                        id: 1,
                        type: "wait",
                        title: "待确认"
                    },
                    {
                        id: 2,
                        type: "wait",
                        title: "审核中"
                    },
                    {
                        id: 3,
                        type: "wait",
                        title: "进行中"
                    },
                    {
                        id: 5,
                        type: "wait",
                        title: "已完结"
                    }
                ],
                auditRecordDtos: [],
                auditItems: [
                    {
                        id: 1,
                        type: "wait",
                        title: "开始审核",
                        description: "",
                        verifyTime: "",
                        simpleDesc: "",
                        remark: "",
                        simpleRemark: ""
                    },
                    {
                        id: 1,
                        type: "wait",
                        title: "申请人",
                        description: "",
                        verifyTime: "",
                        simpleDesc: "",
                        remark: "",
                        simpleRemark: ""
                    },
                    {
                        id: 1,
                        type: "wait",
                        title: "供应链主管或产品总监审核",
                        description: "",
                        verifyTime: "",
                        simpleDesc: "",
                        remark: "",
                        simpleRemark: ""
                    },
                    {
                        id: 1,
                        type: "wait",
                        title: "财务专员审核",
                        description: "",
                        verifyTime: "",
                        simpleDesc: "",
                        remark: "",
                        simpleRemark: ""
                    },
                    {
                        id: 1,
                        type: "wait",
                        title: "审核完成",
                        description: "",
                        verifyTime: "",
                        simpleDesc: "",
                        remark: "",
                        simpleRemark: ""
                    }
                ]
            };
        },

        created() {
            this.initData();

        },

        mounted() {
            loadingApp()

            sendThis(this);

        },

        methods: {
            async initData() {
                if (viewInfo.afterSalesId != null && viewInfo.afterSalesId != "") {
                    await viewData(viewInfo.afterSalesId).then(res => {
                        this.afterSalesDto = res.data.data;
                        this.updateItems();
                    });
                    await viewOrderData(this.afterSalesDto.orderNo).then(res => {
                        this.buyorderInfo = res.data.data;
                    });
                    // 审核状态进度条
                    await auditRecordData(viewInfo.afterSalesId).then(res => {
                        this.isCheckUser = res.data.data.isCheckUser;
                        this.isOperator = res.data.data.isOperator;
                        this.auditRecordDtos = res.data.data.auditRecordDtos;
                        this.taskInfo = res.data.data.taskInfo;
                        this.updateAuditItems();
                    });
                }
            },
            //计算顶部状态栏
            updateItems(){
                if(this.afterSalesDto.atferSalesStatus == 0 && this.afterSalesDto.status == 1){
                    //待确认，审核中
                    this.items[0].type = "success";
                    this.items[1].type = "process";
                }else if(this.afterSalesDto.atferSalesStatus == 1){
                    //进行中
                    this.items[0].type = "success";
                    this.items[1].type = "success";
                    this.items[2].type = "process";
                }else if(this.afterSalesDto.atferSalesStatus == 2){
                    //已完结
                    this.items[0].type = "success";
                    this.items[1].type = "success";
                    this.items[2].type = "success";
                    this.items[3].type = "success";
                }else if(this.afterSalesDto.atferSalesStatus ==3 && this.afterSalesDto.status == 0){
                    //已关闭未发起审核
                    this.items[0].title = '已关闭';
                    this.items[0].type = 'error';
                    this.items.splice(1);
                    this.items.splice(2);
                    this.items.splice(3);
                } else if(this.afterSalesDto.atferSalesStatus ==3 && this.afterSalesDto.status == 2){
                    //审核通过已关闭
                    this.items[0].title = '待确认';
                    this.items[0].type = 'success'
                    this.items[1].title = '审核通过';
                    this.items[1].type = 'success';
                    this.items[2].title = '已关闭';
                    this.items[2].type = 'error';
                    this.items.splice(3);
                }else if(this.afterSalesDto.atferSalesStatus ==3 && this.afterSalesDto.status == 3){
                    this.items[0].title = '待确认';
                    this.items[0].type = 'success'
                    this.items[1].title = '审核不通过';
                    this.items[1].type = 'success';
                    this.items[2].title = '已关闭';
                    this.items[2].type = 'error';
                    this.items.splice(3);
                } else {
                    //待确认
                    this.items[0].type = "success";
                }
            },
            updateAuditItems(){
                let length = this.auditRecordDtos.length;
                let auditLength = this.auditItems.length;
                for(var i = 0;i < length;i++){
                    let isShowRemark = false;
                    if(this.auditRecordDtos[i].remark != ''){
                        isShowRemark = true;
                    }
                    if(i == length - 1 && this.auditRecordDtos[i].operation != '审核完成' && this.afterSalesDto.status == 1){
                        //审核流进行中-最新节点展示进行中
                        this.auditItems[i].type = "process";
                    }else if(this.afterSalesDto.status == 3 && this.auditRecordDtos[length - 1].operation == '审核不通过' && i >= length - 2){
                        //审核不通过-最后一个节点为审核不通过-最后两个节点展示x
                        this.auditItems[i].type = "error";
                        this.auditItems[i].title = this.auditRecordDtos[i].operation;
                        if(i == length - 1) {
                            for (var j = i + 1; j < auditLength; j++) {
                                this.auditItems.splice(j);
                            }
                        }
                    } else {
                        this.auditItems[i].type = "success";
                    }
                    this.auditItems[i].description = this.auditRecordDtos[i].operator;
                    if(isShowRemark){
                        this.auditItems[i].remark = '备注：' + this.auditRecordDtos[i].remark;
                        if(this.auditRecordDtos[i].remark.length >= 40) {
                            this.auditItems[i].simpleRemark = '备注：' + this.auditRecordDtos[i].remark.toString().substring(0,40) + '...';
                        }
                    }
                    if(this.auditRecordDtos[i].operator.length >= 50){
                        let verifyUsers = this.auditRecordDtos[i].operator.split(',');
                        this.auditItems[i].simpleDesc = verifyUsers[0] + ',' + verifyUsers[1] + ',' + verifyUsers[2] + '...';
                     }
                    this.auditItems[i].verifyTime = parseTime(this.auditRecordDtos[i].operationTime);
                }
            },
            applyValid() {
                let afterSalesId = this.afterSalesDto.afterSalesId;
                layer.confirm("是否确认提交审核？", {
                    btn: ['确定', '取消'] //按钮
                }, function () {
                    $.ajax({
                        type: "POST",
                        url: "/old/afterBuyorder/editApplyAudit.do",
                        data: {"afterSalesId": afterSalesId, "taskId": 0},
                        dataType: 'json',
                        success: function (data) {
                            if (data.code == 0) {
                                window.location.reload();
                            } else {
                                layer.alert(data.message);
                            }
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                            }
                        }
                    });
                    layer.alert(data.message);
                    layer.confirm(data.message, {btn: ['确认']}, function () {
                        window.location.reload();
                    });
                    return false;
                })
            },
            formatNum(data) {
                if (data == null) {
                    data = 0;
                }
                return data.toFixed(2);
            },
            auditRecord(pass) {
                layer.open({
                    type: 2,
                    shadeClose: false,
                    area: ["500px", "180px"],
                    title: "操作确认",
                    content: "/old/afterBuyorder/complement.do?type=3&taskId=" + this.taskInfo + "&pass=" + pass + "&afterSalesId=" + this.afterSalesDto.afterSalesId,
                    success: function (layero, index) {
                    }
                });
            },
            // 关闭售后单
            closeAfterSale() {
                this.$confirm('您是否确认该操作？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    closeAfterSalesTp({"afterSalesId": this.afterSalesDto.afterSalesId, "buyorderId":this.buyorderInfo.buyorderId}).then(res => {
                        openTab("采购售后详情", '/after/newBuyorder/afterBuyorderTpDetail.do?afterSalesId=' + this.afterSalesDto.afterSalesId);
                        this.closeThis()
                    })
                }).catch(() => {
                });
            },

            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            // 确认退票
            confirmRefundInvoice(invoiceNo) {
                openTab("确认退票", '/finance/after/getAfterReturnInvoiceInfo.do?afterSalesId=' + this.afterSalesDto.afterSalesId + '&invoiceNo=' + invoiceNo + '&type=548');
            },

            // 编辑售后单
            editAfterSale() {
                openTab("编辑售后", '/order/newBuyorder/editAfterSalesPage.do?afterSalesId='
                    + this.afterSalesDto.afterSalesId + '&orderId=' + this.buyorderInfo.buyorderId
                    + '&subjectType=536&type=548&afterSalesNo=' + this.afterSalesDto.afterSalesNo);
                this.closeThis()
            },

            viewAttachment(domain, uri) {
                window.open("http://" + domain + uri);
            },

            //采购单详情页跳转
            viewBuyorder(id) {
                openTab("采购订单详情", '/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=' + id);
            },
            //供应商详情页跳转
            viewTrader(id){
                openTab("供应商详情", '/trader/supplier/baseinfo.do?traderId=' + id);
            },
            // 查看商品信息
            viewSkuInfo(id) {
                openTab("商品信息", '/goods/goods/viewbaseinfo.do?goodsId=' + id);
            },
        },
    });
</script>
<style>
    .el-step__head.is-success {
        color: #409EFF;
        border-color: #409EFF;
    }
    .el-step__title.is-success {
        color: #409EFF;
    }
    .el-step__description.is-success {
        color: #409EFF;
    }
</style>