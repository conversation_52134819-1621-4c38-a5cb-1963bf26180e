<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchBuyorderGoodsDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchBuyorderGoodsDto">
    <!--@mbg.generated-->
    <!--@Table T_BUYORDER_GOODS-->
    <id column="BUYORDER_GOODS_ID" jdbcType="INTEGER" property="buyorderGoodsId" />
    <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="ORIGINAL_PURCHASE_PRICE" jdbcType="DECIMAL" property="originalPurchasePrice" />
    <result column="COUPON_REASON" jdbcType="VARCHAR" property="couponReason" />
    <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="ARRIVAL_NUM" jdbcType="INTEGER" property="arrivalNum" />
    <result column="ESTIMATE_DELIVERY_TIME" jdbcType="BIGINT" property="estimateDeliveryTime" />
    <result column="ESTIMATE_ARRIVAL_TIME" jdbcType="BIGINT" property="estimateArrivalTime" />
    <result column="ARRIVAL_USER_ID" jdbcType="INTEGER" property="arrivalUserId" />
    <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments" />
    <result column="DELIVERY_CYCLE" jdbcType="VARCHAR" property="deliveryCycle" />
    <result column="INSTALLATION" jdbcType="VARCHAR" property="installation" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
    <result column="AFTER_RETURN_AMOUNT" jdbcType="DECIMAL" property="afterReturnAmount" />
    <result column="AFTER_RETURN_NUM" jdbcType="INTEGER" property="afterReturnNum" />
    <result column="REAL_INVOICE_AMOUNT" jdbcType="DECIMAL" property="realInvoiceAmount" />
    <result column="REAL_INVOICE_NUM" jdbcType="DECIMAL" property="realInvoiceNum" />
    <result column="GE_CONTRACT_NO" jdbcType="VARCHAR" property="geContractNo" />
    <result column="GE_SALE_CONTRACT_NO" jdbcType="VARCHAR" property="geSaleContractNo" />
    <result column="PRODUCT_AUDIT" jdbcType="INTEGER" property="productAudit" />
    <result column="PROCESS_DESCRIPTION" jdbcType="VARCHAR" property="processDescription" />
    <result column="FIRST_SEND_GOODS_TIME" jdbcType="BIGINT" property="firstSendGoodsTime" />
    <result column="FIRST_RECEIVE_GOODS_TIME" jdbcType="BIGINT" property="firstReceiveGoodsTime" />
    <result column="SEND_GOODS_TIME" jdbcType="BIGINT" property="sendGoodsTime" />
    <result column="RECEIVE_GOODS_TIME" jdbcType="BIGINT" property="receiveGoodsTime" />
    <result column="IS_SEND_CREATE_FLAG" jdbcType="INTEGER" property="isSendCreateFlag" />
    <result column="ALREADY_EXPEDITING_ALARM" jdbcType="INTEGER" property="alreadyExpeditingAlarm" />
    <result column="PRODUCT_BELONG_ID_INFO" jdbcType="VARCHAR" property="productBelongIdInfo" />
    <result column="PRODUCT_BELONG_NAME_INFO" jdbcType="VARCHAR" property="productBelongNameInfo" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="MANUFACTURER_NAME" jdbcType="VARCHAR" property="manufacturerName" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="IS_HAVE_AUTH" jdbcType="INTEGER" property="isHaveAuth" />
    <result column="IS_GIFT" jdbcType="INTEGER" property="isGift" />
    <result column="REFER_PRICE" jdbcType="DECIMAL" property="referPrice" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUYORDER_GOODS_ID, BUYORDER_ID, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME, MODEL, UNIT_NAME, 
    PRICE, ORIGINAL_PURCHASE_PRICE, COUPON_REASON, CURRENCY_UNIT_ID, NUM, ARRIVAL_NUM, 
    ESTIMATE_DELIVERY_TIME, ESTIMATE_ARRIVAL_TIME, ARRIVAL_USER_ID, ARRIVAL_STATUS, ARRIVAL_TIME, 
    IS_DELETE, INSIDE_COMMENTS, DELIVERY_CYCLE, INSTALLATION, COMMENTS, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER, UPDATE_DATA_TIME, AFTER_RETURN_AMOUNT, AFTER_RETURN_NUM, REAL_INVOICE_AMOUNT, 
    REAL_INVOICE_NUM, GE_CONTRACT_NO, GE_SALE_CONTRACT_NO, PRODUCT_AUDIT, PROCESS_DESCRIPTION, 
    FIRST_SEND_GOODS_TIME, FIRST_RECEIVE_GOODS_TIME, SEND_GOODS_TIME, RECEIVE_GOODS_TIME, 
    IS_SEND_CREATE_FLAG, ALREADY_EXPEDITING_ALARM, PRODUCT_BELONG_ID_INFO, PRODUCT_BELONG_NAME_INFO, 
    SPEC, MANUFACTURER_NAME, REGISTRATION_NUMBER, IS_HAVE_AUTH, IS_GIFT, REFER_PRICE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUYORDER_GOODS
    where BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2022-11-24-->
  <select id="selectByBuyorderIdNotDelete" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BUYORDER_GOODS
    where BUYORDER_ID=#{buyorderId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>

  <select id="selectBySkuValidLately" resultMap="BaseResultMap">
    SELECT
      TBG.BUYORDER_GOODS_ID, TBG.BUYORDER_ID, TBG.GOODS_ID, TBG.SKU, TBG.GOODS_NAME, TBG.BRAND_NAME, TBG.MODEL, TBG.UNIT_NAME,
      TBG.PRICE, TBG.ORIGINAL_PURCHASE_PRICE, TBG.COUPON_REASON, TBG.CURRENCY_UNIT_ID, TBG.NUM, TBG.ARRIVAL_NUM,
      TBG.ESTIMATE_DELIVERY_TIME, TBG.ESTIMATE_ARRIVAL_TIME, TBG.ARRIVAL_USER_ID, TBG.ARRIVAL_STATUS, TBG.ARRIVAL_TIME,
      TBG.IS_DELETE, TBG.INSIDE_COMMENTS, TBG.DELIVERY_CYCLE, TBG.INSTALLATION, TBG.COMMENTS, TBG.ADD_TIME, TBG.CREATOR,
      TBG.MOD_TIME, TBG.UPDATER, TBG.UPDATE_DATA_TIME, TBG.AFTER_RETURN_AMOUNT, TBG.AFTER_RETURN_NUM, TBG.REAL_INVOICE_AMOUNT,
      TBG.REAL_INVOICE_NUM, TBG.GE_CONTRACT_NO, TBG.GE_SALE_CONTRACT_NO, TBG.PRODUCT_AUDIT, TBG.PROCESS_DESCRIPTION,
      TBG.FIRST_SEND_GOODS_TIME, TBG.FIRST_RECEIVE_GOODS_TIME, TBG.SEND_GOODS_TIME, TBG.RECEIVE_GOODS_TIME,
      TBG.IS_SEND_CREATE_FLAG, TBG.ALREADY_EXPEDITING_ALARM, TBG.PRODUCT_BELONG_ID_INFO, TBG.PRODUCT_BELONG_NAME_INFO,
      TBG.SPEC, TBG.MANUFACTURER_NAME, TBG.REGISTRATION_NUMBER, TBG.IS_HAVE_AUTH, TBG.IS_GIFT, TBG.REFER_PRICE
    FROM
        T_BUYORDER_GOODS TBG
    LEFT JOIN
        T_BUYORDER TB on TBG.BUYORDER_ID = TB.BUYORDER_ID
    WHERE
        TBG.SKU = #{sku,jdbcType=VARCHAR} AND TB.VALID_STATUS = 1 AND TBG.PRICE != 0
    ORDER BY
        TB.ADD_TIME desc
    LIMIT 1
  </select>

<!--auto generated by MybatisCodeHelper on 2023-05-06-->
  <select id="findByBuyorderGoodsIdInAndIsDelete" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BUYORDER_GOODS
    where BUYORDER_GOODS_ID in
    <foreach item="item" index="index" collection="buyorderGoodsIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    and IS_DELETE=#{isDelete,jdbcType=INTEGER}
  </select>

  <select id="getAfterGoodsIdByCondition" resultType="java.lang.Integer">
    SELECT ASG.AFTER_SALES_GOODS_ID
    FROM T_BUYORDER_GOODS BG
    LEFT JOIN T_BUYORDER TB ON BG.BUYORDER_ID = TB.BUYORDER_ID
    LEFT JOIN T_AFTER_SALES TAS ON TB.BUYORDER_ID = TAS.ORDER_ID
    LEFT JOIN T_AFTER_SALES_GOODS ASG ON TAS.AFTER_SALES_ID = ASG.AFTER_SALES_ID
    AND ASG.ORDER_DETAIL_ID = BG.BUYORDER_GOODS_ID
    WHERE BG.BUYORDER_GOODS_ID = #{buyOrderGoodsId,jdbcType=INTEGER}
    AND TAS.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    LIMIT 1
  </select>

<!--auto generated by MybatisCodeHelper on 2023-06-28-->
  <select id="findHaveInstallationByBuyorderGoodsId" resultMap="BaseResultMap">
    select
    TBG.*
    from
    T_BUYORDER_GOODS TBG
    inner join T_R_BUYORDER_J_SALEORDER TRBJS on TBG.BUYORDER_GOODS_ID = TRBJS.BUYORDER_GOODS_ID
    inner join T_SALEORDER_GOODS TSG on TRBJS.SALEORDER_GOODS_ID = TSG.SALEORDER_GOODS_ID
    where TBG.BUYORDER_GOODS_ID=#{buyorderGoodsId,jdbcType=INTEGER}
    and TSG.HAVE_INSTALLATION = 1
    group by TBG.BUYORDER_GOODS_ID
  </select>

  <select id="findBySkuInAndBuyorderId" resultMap="BaseResultMap">
    select
    TBG.BUYORDER_GOODS_ID,
    TBG.SKU
    from T_BUYORDER_GOODS TBG
    inner join T_BUYORDER TB on TBG.BUYORDER_ID = TB.BUYORDER_ID
    where TBG.SKU in
    <foreach item="item" index="index" collection="skuCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    and TB.BUYORDER_NO=#{buyorderNo,jdbcType=VARCHAR};
  </select>

</mapper>