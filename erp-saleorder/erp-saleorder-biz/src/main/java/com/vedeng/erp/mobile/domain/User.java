package com.vedeng.erp.mobile.domain;

import lombok.Data;

import java.util.List;

/**
    * 用户基本信息
    */
@Data
public class User {
    private Integer userId;

    /**
    * 公司ID
    */
    private Integer companyId;

    /**
    * 用户名(登陆名)
    */
    private String username;

    /**
    * 工号
    */
    private String number;

    /**
    * 密码
    */
    private String password;

    /**
    * 加密扰码
    */
    private String salt;

    /**
    * 直接上级用户ID
    */
    private Integer parentId;

    /**
    * 是否管理员0普通用户1管理员2超级管理员
    */
    private Boolean isAdmin;

    /**
    * 是否禁用0未禁用 1已禁用
    */
    private Integer isDisabled;

    /**
    * 禁用原因
    */
    private String disabledReason;

    /**
    * 上次登录时间
    */
    private Long lastLoginTime;

    /**
    * 上次登录IP
    */
    private String lastLoginIp;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 最近一次编辑时间
    */
    private Long modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;

    /**
    * 是否贝登员工:1 是 0 否
    */
    private Boolean staff;

    /**
    * 可登录系统,逗号拼接 1,2,3
    */
    private String system;

    /**
    * 所属公司Id,贝登员工默认是0
    */
    private Integer userBelongCompanyId;

    /**
    * 主叫号码
    */
    private String ttNumber;

    /**
    * 用户可以查看的部门ID
    */
    private String orgIdsList;

    /**
    * 电信线路号码
    */
    private String telecomLine;

    /**
    * 移动线路号码
    */
    private String mobileLine;

    /**
    * 联通线路号码
    */
    private String unicomLine;

    /**
    * 客户专线
    */
    private String customerLine;

    /**
     * 职位集合
     */
    private List<Position> positions;

    /**
     * 职位类型
     */
    private Integer positType;

    /**
     * 职位级别
     */
    private Integer positLevel;

    /**
     * 部门id
     */
    private Integer orgId;

    /**
     * 部门名称
     */
    private String orgName;

    /**
     * 职位类型集合
     */
    private List<Integer> positionTypes;

    private String ccNumber;
}