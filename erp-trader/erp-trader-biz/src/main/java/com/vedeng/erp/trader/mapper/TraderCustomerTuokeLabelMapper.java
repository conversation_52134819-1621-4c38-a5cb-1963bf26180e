package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.dto.TraderCustomerTuokeLabel;
import com.vedeng.erp.trader.domain.vo.TraderCustomerTuokeLabelVo;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【T_TRADER_CUSTOMER_TUOKE_LABEL(客户群标签表)】的数据库操作Mapper
* @createDate 2022-03-10 17:03:36
* @Entity com.vedeng.erp.business.domain.entity.TraderCustomerTuokeLabel
*/
public interface TraderCustomerTuokeLabelMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(TraderCustomerTuokeLabel record);

    int insertSelective(TraderCustomerTuokeLabel record);

    TraderCustomerTuokeLabel selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TraderCustomerTuokeLabel record);

    int updateByPrimaryKey(TraderCustomerTuokeLabel record);

    TraderCustomerTuokeLabel selectByTraderId(Integer traderId);

    /**
     * 查询客户群标签信息
     */
    TraderCustomerTuokeLabelVo getTuokeLabelInfo(@Param("traderId") Integer traderId, @Param("businessCluesId") Integer businessCluesId);

}
