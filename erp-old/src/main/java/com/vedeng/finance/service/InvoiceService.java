package com.vedeng.finance.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.BaseService;
import com.vedeng.erp.buyorder.dto.InvoiceSaveSearchDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRuleDto;
import com.vedeng.erp.finance.dto.SaveCheckRuleDto;
import com.vedeng.finance.dto.HxInvoiceAuthResponse;
import com.vedeng.finance.dto.HxInvoiceSearchDTO;
import com.vedeng.finance.dto.InvoiceApplyReasonSnapshotDto;
import com.vedeng.finance.dto.InvoiceHrefDownDto;
import com.vedeng.finance.model.*;
import com.vedeng.finance.model.vo.HxInvoiceConfigVo;
import com.vedeng.finance.model.vo.InvoiceApplyError;
import com.vedeng.finance.vo.HxInvoiceVo;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.BuyorderGoodsRecordDTO;
import com.vedeng.order.model.dto.BuyorderSearchDTO;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.wms.dto.WmsInvoiceInfoDto;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface InvoiceService extends BaseService{

	/**
	 * <b>Description:</b><br> 根据外键查询发票记录（对应产品开票记录）
	 * @param bo
	 * @return
	 * @throws Exception
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月18日 上午11:31:04
	 */
	Map<String,Object> getInvoiceListByBuyorder(BuyorderVo bo,Invoice invoice,Integer inputInvoiceType) throws Exception;

	/**
	 * <b>Description:</b><br> 录票信息添加保存
	 * @param invoice
	 * @return
	 * @throws Exception
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月21日 下午3:47:30
	 */
	ResultInfo<?> saveInvoice(Invoice invoice) throws Exception;

	/**
	 * <b>Description:</b><br> 根据发票单号查询收票审核
	 * @param invoice
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月23日 上午11:27:15
	 */
	Map<String, Object> getInvoiceAuditListByInvoiceNo(Invoice invoice);

	/**
	 * <b>Description:</b><br> 保存开票记录审核结果
	 * @param invoice
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月24日 下午3:04:27
	 */
	ResultInfo<?> saveInvoiceAudit(Invoice invoice);

	/**
	 * <b>Description:</b><br> 查询收票记录列表
	 * @param invoice
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月24日 下午4:20:57
	 */
	Map<String,Object> getInvoiceListPage(Invoice invoice,Page page);

	/**
	 * <b>Description:</b><br> 保存批量认证
	 * @param invoices
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年5月28日 下午4:56:51
	 */
	ResultInfo<?> saveBatchAuthentication(List<Invoice> invoices);

	/**
	 * <b>Description:</b><br> 开票记录列表导出
	 * @param invoice
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月28日 上午9:10:22
	 */
	Map<String, Object> exportIncomeInvoiceList(Invoice invoice);

	/**
	 * <b>Description:</b><br> 保存开票申请（包括提前开票）
	 * @param invoiceApply
	 * @return
	 * @throws Exception
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月28日 下午2:08:31
	 */
	ResultInfo<?> saveOpenInvoceApply(InvoiceApply invoiceApply);

	/**
	 * <b>Description:</b><br> 开票申请列表页
	 * @param invoiceApply
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月28日 下午3:08:00
	 */
	Map<String, Object> getSaleInvoiceApplyListPage(InvoiceApply invoiceApply,Page page);

	/**
	 * <b>Description:</b><br> 分公司-开票申请待审核列表（审核流程）
	 * @param invoiceApply
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年5月29日 下午3:53:50
	 */
	Map<String, Object> getInvoiceApplyVerifyListPage(InvoiceApply invoiceApply, Page page);

	/**
	 * @param invoiceApply
	 * @return
	 * @Note
	 */
	List<InvoiceApplyError> advanceInvoiceAudit(InvoiceApply invoiceApply);

	/**
	 * 开票申请审核
	 * @param invoiceApply
	 * @return List<InvoiceApplyError>
	 */
	List<InvoiceApplyError> invoiceAudit(InvoiceApply invoiceApply);

	/**
	 * <b>Description:</b><br> 提前开票申请列表页
	 * @param invoiceApply
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月30日 上午9:17:11
	 */
	Map<String, Object> getAdvanceInvoiceApplyListPage(InvoiceApply invoiceApply, Page page);

	/**
	 * <b>Description:</b><br> 获取销售产品及已开票数量
	 * @param invoice
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月30日 上午11:47:10
	 */
	Map<String, Object> getSaleInvoiceList(Invoice invoice);

	/**
	 * <b>Description:</b><br> 开票记录（销售）查询
	 * @param invoice
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年9月1日 下午2:00:21
	 */
	Map<String, Object> getSaleInvoiceListPage(Invoice invoice, Page page);

	/**
	 * <b>Description:</b><br> 根据单号(销售或其他)获取发票信息
	 * @param saleorderId
	 * @param id505
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年9月1日 下午3:33:10
	 */
	List<Invoice> getInvoiceInfoByRelatedId(Integer relatedId, Integer id);

	/**
	 * <b>Description:</b><br> 根据销售订单获取开票申请记录
	 * @param saleorderId
	 * @param id505
	 * @return
	 * @Note
	 * <b>Author:</b> Administrator
	 * <br><b>Date:</b> 2017年9月11日 下午3:40:52
	 */
	List<InvoiceApply> getSaleInvoiceApplyList(Integer saleorderId, Integer id505,Integer isAdvance);

	/**
	 * <b>Description:</b><br> 查询此订单最近一次开票申请记录
	 * @param saleorderId
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年9月29日 上午9:11:38
	 */
	InvoiceApply getInvoiceApplyByRelatedId(Integer saleorderId,Integer type,Integer companyId);
	/**
	 * <b>Description:</b><br> 保存发票寄送快递信息
	 * @param express
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年9月30日 下午1:35:45
	 */
	ResultInfo<?> saveExpressInvoice(Express express);

	/**
	 * <b>Description:</b><br> 获取采购发票待审核列表
	 * @param invoice
	 * @param page
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年11月9日 下午1:35:05
	 */
	Map<String, Object> getBuyInvoiceAuditList(Invoice invoice, Page page ,List<SysOptionDefinition> invoiceTypeList);
    /**
     *
     * <b>Description:</b><br> 根据单号获取发票信息
     * @param ic
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2017年12月11日 上午8:57:20
     */
	Invoice getInvoiceByNo(Invoice ic);

	/**
	 * <b>Description:</b><br> 售后开票申请列表
	 * @param invoiceApply
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年4月16日 下午4:29:47
	 */
	Map<String, Object> getAfterOpenInvoiceListPage(InvoiceApply invoiceApply, Page page);

	/**
	 * <b>Description:</b><br> 售后开票申请待审核列表（分公司流程）
	 * @param invoiceApply
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年5月30日 下午2:55:20
	 */
	Map<String, Object> getAfterInvoiceApplyVerifyListPage(InvoiceApply invoiceApply, Page page);

	/**
	 * <b>Description:</b><br> 采购录票确认列表
	 * @param invoice
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年5月4日 下午2:07:29
	 */
	Map<String, Object> getBuyInvoiceConfirmListPage(Invoice invoice, Page page);

	/**
	 * 发送至金蝶开票记录
	 * <b>Description:</b><br>
	 * @param invoice
	 * @param page
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> Bill
	 * <br><b>Date:</b> 2018年5月30日 下午1:34:26
	 */
	ResultInfo<?> sendOpenInvoicelist(Invoice invoice, Page page, HttpSession session) throws Exception;

	/**
	 * 发送至金蝶收票记录
	 * <b>Description:</b><br>
	 * @param invoice
	 * @param page
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> Bill
	 * <br><b>Date:</b> 2018年5月30日 下午1:35:36
	 */
	ResultInfo<?> sendIncomeInvoiceList(Invoice invoice, Page page, HttpSession session) throws Exception;

	/**
	 * <b>Description:</b><br> 开具电子发票
	 * @param invoiceApply
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年6月15日 上午9:25:06
	 */
	ResultInfo<?> openEInvoicePush(InvoiceApply invoiceApply);

	/**
	 * <b>Description:</b><br> 电子发票下载
	 * @param invoiceApply
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年6月25日 上午10:25:41
	 */
	ResultInfo<?> batchDownEInvoice(Invoice invoice);

	/**
	 * <b>Description:</b><br> 电子发票作废
	 * @param invoice
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年6月27日 下午3:20:11
	 */
	ResultInfo<?> cancelEInvoicePush(Invoice invoice);
    /**
     *
     * <b>Description:</b><br> 发票状态回退
     * @param express
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2018年7月3日 上午8:44:11
     */
	ResultInfo<?> updateExpressInvoice(Express express);

	/**
	 * <b>Description:</b><br> 电子票推送短信和邮箱
	 * @param invoice
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年7月20日 下午6:44:01
	 */
	ResultInfo<?> sendInvoiceSmsAndMail(Invoice invoice);

	/* *
	 * 功能描述: 查询所有集中开票客户
	 * @param: [traderName]
	 * @return: java.util.List<TraderCustomer>
	 * @auther: duke.li
	 * @date: 2019/4/12 9:09
	 */
	List<TraderCustomerVo> getCollectInvoiceTraderName(String traderName);

	/* *
	 * 功能描述: 集中开票客户导入
	 * @param: [list]
	 * @return: com.vedeng.common.model.ResultInfo<?>
	 * @auther: duke.li
	 * @date: 2019/4/12 13:07
	 */
	ResultInfo<?> updateCollectInvoiceTrader(List<Trader> list);

	/* *
	 * 功能描述: 批量删除集中开票客户
	 * @param: [traderCustomerVo]
	 * @return: com.vedeng.common.model.ResultInfo<?>
	 * @auther: duke.li
	 * @date: 2019/4/12 17:07
	 */
	ResultInfo<?> delCollectInvoiceTrader(TraderCustomerVo traderCustomerVo);

	/* *
	 * 功能描述: 修改发票申请标记状态
	 * @param: [invoiceApply]
	 * @return: com.vedeng.common.model.ResultInfo<?>
	 * @auther: duke.li
	 * @date: 2019/4/13 14:48
	 */
    ResultInfo<?> updateInvoiceApplySign(InvoiceApply invoiceApply);

    /**
     * <b>Description:</b><br>发票寄送后发微信模版消息给对应客户
     *
     *
     * @param :[invoiceIdList]
     * @return :com.vedeng.common.model.ResultInfo<?>
     * @Note <b>Author:</b> Michael <br>
     *       <b>Date:</b> 2019/5/22 11:23 AM
     */
	ResultInfo<?> sendWxMessageForInvoice(List<Invoice> invoiceList);

	/**
	 * 根据发票ID查询list
	 * @param invoiceIdLis
	 * @return
	 */
	List<Invoice> getInvoiceListByInvoiceIdList(List<Integer> invoiceIdLis);


	/** @description: getInvoiceNums.
	 * @notes: VDERP-1325 分批开票 获取已开票数量和未开票数量.
	 * @author: Tomcat.Hui.
	 * @date: 2019/11/8 15:15.
	 * @param saleorderNo
	 * @return: java.util.Map<java.lang.Integer , java.util.Map < java.lang.String , java.math.BigDecimal>>.
	 * @throws: .
	 */
	Map<Integer ,Map<String,Object>> getInvoiceNums(Saleorder saleorder);

	/** @description: getApplyDetailById.
	 * @notes: VDERP-1325 分批开票 获取开票申请详情.
	 * @author: Tomcat.Hui.
	 * @date: 2019/11/21 9:58.
	 * @param invoiceApply
	 * @return: com.vedeng.finance.model.InvoiceApply.
	 * @throws: .
	 */
	InvoiceApply getApplyDetailById(InvoiceApply invoiceApply);

	/** @description: getInvoiceDetailById.
	 * @notes:  VDERP-1325 分批开票 获取发票明细详情.
	 * @author: Tomcat.Hui.
	 * @date: 2019/11/21 10:03.
	 * @param invoice
	 * @return: com.vedeng.finance.model.Invoice.
	 * @throws: .
	 */
	Invoice getInvoiceDetailById(Invoice invoice);
	/**
	*获取当前订单开票审核状态 1 存在审核中 0 不存在
	* @Author:strange
	* @Date:10:25 2019-11-23
	*/
    Integer getInvoiceApplyNowAuditStatus(Integer saleorderId);

	/**
	 * 根据ID查询关联表ID
	 * @param InvoiceApplyId
	 * @return
	 */
	InvoiceApply getInvoiceApply(Integer InvoiceApplyId);

	/**
	 * 批量开发票
	 * @param invoiceApply
	 * @param invoiceApplyIdList
	 * @return
	 * <AUTHOR>
	 */
	ResultInfo<?> openEInvoiceBatchPush(InvoiceApply invoiceApply, List<Integer> invoiceApplyIdList);

	/**
	* 更新开票申请信息
	* @Author:strange
	* @Date:15:40 2019-12-30
	*/
    InvoiceApply updateInvoiceApplyInfo(InvoiceApply invoiceApply);
	/**
	* 票货同行快递单是否可编辑
	* @Author:strange
	* @Date:09:59 2020-01-06
	*/
    Boolean getInvoiceApplyByExpressId(Integer expressId);
	/**
	*驳回票货同行未开票申请
	* @Author:strange
	* @Date:10:01 2020-01-06
	*/
	void updateInvoiceApplyByExpressId(Integer expressId);

    /** @description: getInvoiceApplyByExpressId.
     * @notes: VDERP-1039 票货同行 根据快递单ID查询开票申请..
     * @author: Tomcat.Hui.
     * @date: 2020/1/3 17:13.
     * @param invoiceApply
     * @return: com.vedeng.model.finance.InvoiceApply.
     * @throws: .
     */
	List<InvoiceApply> getInvoiceApplyInfoByExpressId(InvoiceApply invoiceApply);

    /** @description: getInvoiceByApplyId.
     * @notes: VDERP-1039 票货同行 根据开票申请ID获取发票信息.
     * @author: Tomcat.Hui.
     * @date: 2020/1/6 10:08.
     * @param InvoiceApplyId
     * @return: java.util.List<com.vedeng.finance.model.Invoice>.
     * @throws: .
     */
    List<Invoice> getInvoiceByApplyId(Integer InvoiceApplyId);
	/**
	*票货同行订单是否可删除快递
	* @Author:strange
	* @Date:21:20 2020-01-06
	*/
    boolean isDelExpressByExpressId(Express express);


    /** @description: getInvoicedData.
	 * @notes: VDERP-1874 开票中和已开票数量、金额的计算规则变更 获取已被占用数量(已申请+已开票).
	 * @author: Tomcat.Hui.
	 * @date: 2020/1/16 19:23.
	 * @param saleorderNo
	 * @return: java.util.Map<java.lang.Integer , java.math.BigDecimal>.
	 * @throws: .
	 */
	Map<Integer, BigDecimal> getInvoiceOccupiedAmount(Saleorder saleorder);

	/** @description: getExpressInvoiceInfo.
     * @notes: 查询物流list每个对象的开票申请和开票信息.
     * @author: Tomcat.Hui.
     * @date: 2020/1/17 10:30.
     * @param expressList
     * @return: java.util.List<com.vedeng.logistics.model.Express>.
     * @throws: .
     */
    public List<Express> getExpressInvoiceInfo(List<Express> expressList);

	/**
	 * 快递是否关联发票
	 * @param expressId
	 * @return
	 */
	Boolean getInvoiceApplyByExpressIdNo(Integer expressId);
	/**
	 * 快递是否关联都为不通过发票
	 * @param expressId
	 * @return
	 */
    Boolean getInvoiceApplyByExpressIdFaile(Integer expressId);

	/**
	 * 判断是否为待审核
	 * @param expressId
	 * @return
	 */
	Boolean getInvoiceApplyByExpressIdByValidIsZero(Integer expressId);
	//查询所有开票申请
	List<InvoiceApply> getAllSaleInvoiceApplyList();
	//根据订单id查找开票记录
	List<Integer> getInvoiceApplyIdsBySaleOrderIds(List saleOrderNoList);
	//改变标记状态
	int changeIsSign(List<Integer> endInvoiceApplyIds);

	/**
	 * @Description: 查询发票下商品信息
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2020/4/28
	 */
	List<InvoiceDetail> getInvoiceGoodsList(Integer invoiceId);

	/**
	 * 校验发票是否已快递
	 * <AUTHOR>
	 * @Date 3:21 下午 2020/5/8
	 * @Param
	 * @return
	 **/
    boolean checkInvoiceExpress(List<Integer> invoiceIdList);


    Map<String, Object> getHxInvoiceByFinanceListPage(HxInvoiceSearchDTO searchDTO, Page page, Integer flag);

	/**
	 * @describe 供应链页面获取航信的待录票列表
	 * @param searchDTO
	 * @param page
	 * @param user
	 * @return
	 * <AUTHOR>
	 * @date 2020/5/22
	 */
	Map<String, Object> getSupplyHxInvoiceWaitListPage(HxInvoiceSearchDTO searchDTO, Page page,Integer idFlag, User user);

	/**
	 *@describe 保存更新航信的发票状态信息
	 * @param hxInvoiceId 航信发票的ID
	 * @param invoiceStatus 发票的状态
	 * @return
	 * <AUTHOR>
	 * @date 2020/5/26
	 */
	Integer saveHxInvoiceStatus(Integer hxInvoiceId, Integer invoiceStatus);

	/**
	 * @describe 获取进行票以及商品得相关信息
	 * @param hxInvoiceId 航信进项票ID
	 * @return
	 * <AUTHOR>
	 * @date 2020 5/27
	 */
	Map<String, Object> getHxInvoiceById(Integer hxInvoiceId);

	/**
	 * @describe 暂存航信发票录票信息
	 * @param invoiceEntryStash 录票信息
	 * @return
	 * <AUTHOR>
	 * @date 2020/5/27 16:01:13
	 */
    Integer saveInvoiceEntryStash(InvoiceEntryStash invoiceEntryStash);

	/**
	 * @describe 删除一条航信录票暂存状态
	 * @param invoiceEntryStashId 录票暂存ID
	 * @return
	 * <AUTHOR>
	 * @date 2020/5/28 10:05:10
	 */
	ResultInfo deleteInvoiceEntryStash(Integer invoiceEntryStashId);

	/**
	 * @describe 条件获取待录票订单相关信息
	 * @param buyorderSearchDTO
	 * @return
	 * <AUTHOR>
	 * @date 2020/5/28 15:53:18
	 */
	List<BuyorderVo> getInvoiceBuyorderList(BuyorderSearchDTO buyorderSearchDTO);

	/**
	 * @describe 保存航信发票录票详情信息
	 * @param currentUser 当前登陆用户
	 * @param invoiceEntryStashs 录票详情信息
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/1 11:22:13
	 */
	ResultInfo saveHxInvoiceDetails(User currentUser, List<InvoiceEntryStash> invoiceEntryStashs);

	/**
	 * @describe 清空当前发票的暂存录票信息
	 * @param hxInvoiceId
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/1 15:25:05
	 */
	ResultInfo deleteInvoiceDetails(Integer hxInvoiceId);

	/**
	 * @describe 更新暂存航信发票录票信息
	 * @param invoiceEntryStash 录票信息
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/2 9:13:13
	 */
	Integer updtateInvoiceEntryStash( InvoiceEntryStash invoiceEntryStash);

	/**
	 * @describe 通过ID获取航信发票详情
	 * @param hxInvoiceDetailId
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/2 13:05:10
	 */
	HxInvoiceDetail getHxInvoiceDetailById(Integer hxInvoiceDetailId);

	/**
	 * @describe 获取航信发票相关信息
	 * @param hxInvoiceId 发票ID
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/3 13:17:17
	 */
	HxInvoiceVo getHxInvoiceInfoByHxInvocieId(Integer hxInvoiceId);

	/**
	 * @describe 根据采购商品ID获取其暂存录票信息
	 * @param buyorderGoodsId
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/3 16:38:48
	 */
	BuyorderGoodsRecordDTO getBuyorderGoodsRecordDTOByGoodsId(Integer buyorderGoodsId);

	/**
	 * @describe 标记航信发票的退票处理状态
	 * @param hxInvoice
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/9 15:40:01
	 */
	Integer updateInvoiceRefundStatus(HxInvoice hxInvoice);

	/**
	 * @describe 获取航信发票基础信息
	 * @param hxInvoiceId 航信发票ID
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/9 17:16:15
	 */
	HxInvoice getHxInvocieBaseInfoById(Integer hxInvoiceId);

	/**
	 *@describe 批量保存更新航信的发票状态信息
	 * @param hxInvoiceIds 航信发票的ID集合
	 * @param invoiceStatus 发票的状态
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/17
	 */
	Integer batchSaveHxInvoiceStatus(List<Integer>  hxInvoiceIds, Integer invoiceStatus);

	/**
	 * @desc 根据条件查询航信发票（手动录票时使用）
	 * @param hxInvoice 航信发票
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/30 16:53:10
	 */
	List<HxInvoice> getHxInvoiceInfoByCondition(HxInvoice hxInvoice);

	/**
	 * 根据发票ID获取发票基本信息
	 *
	 * @param invoiceIds
	 * @return
	 */
	List<Invoice> getInvoiceBaseInfoByInvoiceIds(List<Integer> invoiceIds, Integer isHxInvoiceFlag);

	/**
	 * 条件获取发票基本信息（发票认证使用）
	 * @param invoice
	 *
	 * @return
	 */
	Invoice getInvoiceBaseInfoByCondition(Invoice invoice);

	/**
	 * 保存发票接口认证结果信息
	 *
	 * @param invoice
	 * @param hxInvoiceAuthResponse
	 * @return
	 */
	int saveInvoiceAuthInfo(Invoice invoice, HxInvoiceAuthResponse hxInvoiceAuthResponse,Integer isHxInvoiceFlag);

	/**
	 * 根据发票ID检索发票申请相关信息
	 *
	 * @param invoiceId
	 * @return
	 */
	InvoiceApply getInvoiceApplyInfoByInvoiceId(Integer invoiceId);

	/**
	 * 标记发票申请以下传WMS
	 *
	 * @param invoiceApplyId
	 * @return
	 */
	int signIsSendWmsFlag(Integer invoiceApplyId);

	/**
	 * 获取满足票货同行并且没有推送的发票信息
	 *
	 * @param invoiceGoodsOrderTypes
	 * @return
	 */
	List<WmsInvoiceInfoDto> getWmsInvoiceInfoDtos(List<Integer> invoiceGoodsOrderTypes);

	/**
	 * 标记发票是否正在认证的状态
	 *
	 * @param invoiceIds 发票IDS
	 * @param isAuthStatus 认证状态
	 * @return
	 */
	int signInvoiceAuthStatus(List<Integer> invoiceIds, Integer isAuthStatus, Integer isHxInvoiceFlag);

	/**
	 * 保存发票地址信息
	 *
	 * @param invoiceHrefDownDto
	 * @param session
	 * @param invoiceHref
	 * @return
	 */
	int saveInvoiceHref(InvoiceHrefDownDto invoiceHrefDownDto, HttpSession session, String invoiceHref);

    /**
     * 采购
     * 根据外键查询发票记录（对应产品开票记录）
     *
     * @param relatedIdList
     * @return
     */
    List<InvoiceDetail> getInvoiceListByRelatedId(List<Integer> relatedIdList);

	/**
	 * 录票时处理航信流转处理
	 *
	 * @param invoice
	 * @return
	 */
	void dealHxInvoiceInfoByInvoice(Invoice invoice);


	List<HxInvoiceConfigVo> getHxInvoiceConfig();
	/**
	 * <b>Description:</b><br>
	 * 手动录票检查发票信息(由controller移至service)
	 *
	 * @param invoice
	 * @return com.vedeng.common.model.ResultInfo<?>
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/7/8 13:22
	 */
	ResultInfo<?> getHxInvoiceInfo(Invoice invoice);

	/**
	 *  航信发票拉取处理蓝字作废票
	 * @param blueValid
	 */
	void rollBackForBlueValidTicket(HxInvoice blueValid);

	/**
	 * 处理采购订单状态信息
	 * @param invoice
	 */
	void dealOrderInvoiceStatus(Invoice invoice);

	/**
	 * 处理催票信息
	 * @param invoice
	 */
	void dealEarlyWarningTicketTask(Invoice invoice);

	/**
	 * 校验该发票是否已经退票
	 * @param invoice
	 */
    void checkInvoiceStatus(Invoice invoice);

	/**
	 * 获取发票快递信息
	 * @param invoiceIdList
	 * @return
	 */
	List<Invoice> getInvoiceExpressInfo(List<Integer> invoiceIdList);

	/**
	 * 刷新航信发票状态
	 * @param hxInvoiceId
	 */
	void refreshHxInvoiceStatus(Integer hxInvoiceId);

	/**
	 * 正向录票数据
	 * @param buyorderVo
	 * @param invoice
	 * @return
	 */
	List<BuyorderVo> getInvoiceBuyOrderList(BuyorderVo buyorderVo,Invoice invoice);

	/**
	 * 发票基础信息
	 * @param invoiceId
	 * @return
	 */
	Invoice getInvoiceBaseInfoById(Integer invoiceId);

	/**
	 * 判断是否满足申请开票条件
	 * @return
	 */
    ModelAndView invoiceConfirm(ModelAndView mv, SaleorderUserInfoDto userInfo, Integer saleOrderId);

	/**
	 * @description: 财务修改发票时间
	 * @return:
	 * @author: Suqin
	 * @date: 2022/12/3 16:26
	 *
	 * @param invoice*/
	Boolean updateInvoiceInfo(InvoiceTimeDto invoice);

	/**
	 * @description: 根据发票id获取发票信息
	 * @return:
	 * @author: Suqin
	 * @date: 2022/12/3 17:18
	 **/
	InvoiceTimeDto getInvoiceInfoByInvoiceId(Integer invoiceId);

	/**
	 * @description: 获取售后付款申请中客户名称
	 * @return:
	 * @author: Suqin
	 * @date: 2022/12/5 11:04
	 **/
	String getTraderName(Integer traderId);

    void saveCheckRule(SaveCheckRuleDto saveCheckRuleDto);

    void insertApplyReasonSnapshot(InvoiceApplyReasonSnapshotDto dto);

	List<BuyorderGoodsVo> getBuyOrderInvoiceInfo(InvoiceSaveSearchDto invoiceSaveSearchDto);

}
