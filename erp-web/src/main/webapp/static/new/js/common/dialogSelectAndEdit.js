var template = function () {
    var c = {}, x = Object.prototype.hasOwnProperty, r = Array.prototype.indexOf, s = Array.prototype.some, t = Array.prototype.filter, u = Array.prototype.map, n = {}; c.type = function (a) { var b, d = /\{\s*\[native\s*code\]\s*\}/i; null === a ? b = "null" : "undefined" === typeof a ? b = "undefined" : (b = Object.prototype.toString.call(a).match(/\w+/g)[1].toLowerCase(), "object" === b && d.test(a + "") && (b = "function")); return b }; c.trim = function (a) { return (a + "").replace(/^[\s\u00A0]+|[\s\u00A0]+$/g, "") }; c.extend = function () {
        var a =
            arguments.callee, b, d; "object" !== c.type(arguments[0]) ? (b = 1, d = !!arguments[0]) : (b = 0, d = !1); var e = arguments[b] || {}; b = [].slice.call(arguments, b + 1); for (var k, h; b.length;)if (k = b.shift(), "object" === c.type(k)) {
                var f, g; for (g in k) if (f = k[g], "object" === c.type(f)) if (f == window || f == document || "childNodes" in f && "nextSibling" in f && "nodeType" in f) { if (d || !(g in e)) e[g] = f } else if (f.jquery && /^[\d\.]+$/.test(f.jquery)) e[g] = f; else {
                    h = c.type(e[g]); if (!(g in e) || "undefined" === h || "null" === h || d && ("string" === h || "number" === h || "bool" ===
                        h)) e[g] = {}; "object" === c.type(e[g]) && a(d, e[g], f)
                } else if (d || !(g in e)) e[g] = f
            } return e
    }; var p = c.each = function (a, b, d) { if (null != a) if ([].forEach && a.forEach === [].forEach) a.forEach(b, d); else if (a.length === +a.length) for (var e = 0, k = a.length; e < k && b.call(d, a[e], e, a) !== n; e++); else for (e in a) if (c.has(a, e) && b.call(d, a[e], e, a) === n) break }; c.has = function (a, b) { return x.call(a, b) }; c.identity = function (a) { return a }; var v = c.some = c.any = function (a, b, d) {
        b || (b = c.identity); var e = !1; if (null == a) return e; if (s && a.some === s) return a.some(b,
            d); p(a, function (a, c, f) { if (e || (e = b.call(d, a, c, f))) return n }); return !!e
    }; c.find = c.detect = function (a, b, d) { var e; v(a, function (a, c, f) { if (b.call(d, a, c, f)) return e = a, !0 }); return e }; c.contains = c.include = function (a, b) { return null == a ? !1 : r && a.indexOf === r ? -1 != a.indexOf(b) : v(a, function (a) { return a === b }) }; c.filter = c.select = function (a, b, d) { var c = []; if (null == a) return c; if (t && a.filter === t) return a.filter(b, d); p(a, function (a, h, f) { b.call(d, a, h, f) && (c[c.length] = a) }); return c }; c.map = c.collect = function (a, b, c) {
        var e = [];
        if (null == a) return e; if (u && a.map === u) return a.map(b, c); p(a, function (a, h, f) { e[e.length] = b.call(c, a, h, f) }); return e
    }; c.invert = function (a) { var b = {}, d; for (d in a) c.has(a, d) && (b[a[d]] = d); return b }; c.keys = Object.keys || function (a) { if (a !== Object(a)) throw new TypeError("Invalid object"); var b = [], d; for (d in a) c.has(a, d) && (b[b.length] = d); return b }; c.values = function (a) { var b = [], d; for (d in a) c.has(a, d) && b.push(a[d]); return b }; c.random = function (a, b) { null == b && (b = a, a = 0); return a + Math.floor(Math.random() * (b - a + 1)) };
    var l = { escape: { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#x27;", "/": "&#x2F;" } }; l.unescape = c.invert(l.escape); var y = { escape: RegExp("[" + c.keys(l.escape).join("") + "]", "g"), unescape: RegExp("(" + c.keys(l.unescape).join("|") + ")", "g") }; c.each(["escape", "unescape"], function (a) { c[a] = function (b) { return null == b ? "" : ("" + b).replace(y[a], function (b) { return l[a][b] }) } }); var w = { evaluate: /{{([\s\S]+?)}}/g, interpolate: /{{=([\s\S]+?)}}/g, escape: /{{-([\s\S]+?)}}/g }, q = /(.)^/, z = {
        "'": "'", "\\": "\\", "\r": "r", "\n": "n",
        "\t": "t", "\u2028": "u2028", "\u2029": "u2029"
    }, A = /\\|'|\r|\n|\t|\u2028|\u2029/g, m = function (a, b, d) {
        var e; d = c.extend(!0, {}, w, d); var k = RegExp([(d.escape || q).source, (d.interpolate || q).source, (d.evaluate || q).source].join("|") + "|$", "g"), h = 0, f = "__p+='"; a.replace(k, function (b, c, d, e, g) { f += a.slice(h, g).replace(A, function (a) { return "\\" + z[a] }); c && (f += "'+\n((__t=(" + c + "))==null?'':util.escape(__t))+\n'"); d && (f += "'+\n((__t=(" + d + "))==null?'':__t)+\n'"); e && (f += "';\n" + e + "\n__p+='"); h = g + b.length; return b }); f += "';\n"; d.variable ||
            (f = "with(obj||{}){\n" + f + "}\n"); f = "var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n" + f + "return __p;\n"; try { e = new Function(d.variable || "obj", "util", f) } catch (g) { throw g.source = f, g; } if (b) return e(b, c); b = function (a) { return e.call(this, a, c) }; b.source = "function(" + (d.variable || "obj") + "){\n" + f + "}"; return b
    }; m.util = c; m.entities = l; m.settings = w; return m
}.call(this);

; void function () {
    var wrapDom = `
     <div class="dlg-lvselect--wrap J-dlg-lvselect--price" data-id="{{=ID}}">
        <div class="dlg-inner">
            <div class="dlg-title">选择</div>
            <div class="dlg-cnt">
                <div class="sp-wrap J-dlg-sp--container">
                    <div class="sp-loading J-dlg-sp-loading">加载中。。。</div>
                </div>
            </div>
            <div class="dlg-footer" >
                {{ if(!onlyShow){ }}
                    <div class="sp-btn btn-blue J-sp--confirm">确认</div>
                {{ } }}
                <div class="sp-btn J-sp--cancel">
                    {{ if(onlyShow){ }}
                    关闭
                    {{ }else{ }}
                    取消
                    {{ } }}
                </div>
            </div>
        </div>
    </div>`;

    var listDom = `
        <div class="sp-list J-sp--list" data-lv={{=lv}}>
            <div class="sp-item">
                <div class="item-txt">
                    <label>
                        <input type="checkbox" class="J-sp--item--all" name=""{{ if(onlyShow){ }}disabled{{ } }} value="">
                        <span class="input-cb-place"></span>
                    </label>
                    <span class="item-txt-span">
                        全选
                    </span>
                </div>
                {{ if(needPrice){ }}
                <div class="sp-input-price">
                    <input type="text" disabled>
                </div>
                {{ } }}
            </div>
            {{ $.each(list, function(i, item){ }}
                <div class="sp-item J-sp--item" data-id="{{=item.regionId}}">
                    <div class="item-txt">
                        <label>
                            <input type="checkbox" name="" value="" {{ if(onlyShow){ }}disabled{{ } }} >
                            <span class="input-cb-place"></span>
                        </label>
                        <span class="item-txt-span J-dlg-lv--next">
                            {{=item.regionName}}
                        </span>
                    </div>
                    {{ if(needPrice){ }}
                    <div class="sp-input-price">
                        <input type="text" disabled>
                    </div>
                    {{ } }}
                    {{ if(lv == 1){ }}
                        <div class="sp--info J-sp-prev--toggle" data-id="{{=item.regionId}}">
                            <i class="vd-icon icon-info2"></i>
                            <div class="sp-prev-wrap J-sp-prev--list" data-id="{{=item.regionId}}">
                                <div class="sp-prev-item">江苏省</div>
                            </div>
                        </div>
                    {{ } }}
                </div>
            {{ }) }}
        </div>
    `;

    var defaults = {
        button: '',
        url: '',
        needPrice: false,
        selectedData: {},
        input: '',
        onlyShow: false
    };

    var SelectEdit = function (config) {
        this.config = $.extend({}, defaults, config);
        this.__init();
        return this;
    }

    SelectEdit.prototype = {
        constructor: 'SelectEdit',
        __init: function () {
            this.selectID = new Date().valueOf() + '_' + parseInt(1000 * Math.random());
            var wrapTmpl = template(wrapDom);
            this.listTmpl = template(listDom);
            this.wrapHtml = wrapTmpl({ ID: this.selectID, onlyShow: this.config.onlyShow });
            this.$wrap = null;
            this.lv1Id = null;
            this.lv2Id = null;
            this.lv3Id = null;
            this.selectedData = {};

            this.__bindEvent();
        },
        __bindEvent: function () {
            var _this = this;
            $(this.config.button).click(function () {
                var editData = JSON.parse($(_this.config.input).val() || '{}');
                _this.selectedData = editData;

                if (_this.$wrap && _this.$wrap.length) {
                    _this.$wrap.find('.J-sp--list').remove();
                    _this.$wrap.show();
                    _this.lv1Id = null;
                    _this.lv2Id = null;
                    _this.lv3Id = null;
                    _this.initLv(1);
                } else {
                    $('body').append(_this.wrapHtml);
                    _this.$wrap = $('.J-dlg-lvselect--price[data-id="' + _this.selectID + '"]');
                    _this.$list = $('.J-dlg-sp--container', _this.$wrap);
                    _this.$loading = $('.J-dlg-sp-loading', _this.$wrap);
                    _this.initLv(1);

                    _this.$wrap.on('click', '.J-dlg-lv--next', function () {
                        var $parent = $(this).parents('.J-sp--list:first');
                        var $item = $(this).parents('.J-sp--item:first');
                        var lv = $parent.data('lv');
                        var regionId = $item.data('id');

                        if (_this['lv' + lv + 'Id'] !== regionId) {
                            _this['lv' + lv + 'Id'] = regionId;

                            if (lv != 3) {
                                $parent.find('.active').removeClass('active');
                                $item.addClass('active');
                                _this.initLv($(this).parents('.J-sp--list:first').data('lv') + 1, $item);
                            }
                        }
                    })

                    _this.$wrap.on('change', 'input:checkbox:not(.J-sp--item--all)', function () {
                        _this.itemClick($(this));
                    });

                    _this.$wrap.on('change', '.J-sp--item--all', function () {
                        _this.allClick($(this));
                    });

                    _this.$wrap.on('click', '.J-sp--confirm', function () {
                        // console.log(_this.selectedData)
                        _this.config.input && $(_this.config.input).val(JSON.stringify(_this.selectedData));
                        _this.config.confirm && _this.config.confirm(_this.config.selectedData);
                        _this.$wrap.hide();
                    })

                    _this.$wrap.on('click', '.J-sp--cancel', function () {
                        _this.$wrap.hide();
                    })

                    _this.$wrap.on('mouseenter', '.J-sp-prev--toggle', function () {
                        _this.getPrev($(this).data('id'));
                    })

                    if (_this.config.needPrice) {
                        _this.$wrap.on('change', 'input:text', function () {
                            _this.resetChildInput($(this).parents('.J-sp--item:first'));
                        })
                    }
                }
            })
        },
        itemClick: function ($this) {
            var $item = $this.parents('.J-sp--item:first');
            var $list = $this.parents('.J-sp--list:first');
            var lv = $list.data('lv');
            var regionId = $item.data('id');
            var checked = $this[0].checked;
            var _this = this;

            var isAll = true;
            var hasValue = false;
            $list.find('input:checkbox:not(.J-sp--item--all)').each(function () {
                if (!$(this)[0].checked) {
                    isAll = false;
                } else {
                    hasValue = true;
                }
            });

            $list.find('.J-sp--item--all')[0].checked = isAll;

            switch (lv) {
                case 1: {
                    if (regionId == _this.lv1Id) {
                        _this.$wrap.find('.J-sp--list[data-lv=2], .J-sp--list[data-lv=3]').find('input:checkbox').prop('checked', checked);
                    }
                    _this.selectedData[regionId]['checked'] = checked;
                    _this.selectedData[regionId].isAll = checked;
                    _this.selectedData[regionId] = _this.resetItem(_this.selectedData[regionId], checked);

                    break;
                }
                case 2: {
                    var $list1 = _this.$wrap.find('.J-sp--list[data-lv=1]');
                    $list1.find('.J-sp--item.active input:checkbox')[0].checked = hasValue;

                    if (regionId == _this.lv2Id) {
                        _this.$wrap.find('.J-sp--list[data-lv=3]').find('input:checkbox').prop('checked', checked);
                    }

                    _this.selectedData[_this.lv1Id][regionId] = _this.resetItem(_this.selectedData[_this.lv1Id][regionId], checked);

                    _this.selectedData[_this.lv1Id][regionId]['checked'] = checked;
                    _this.selectedData[_this.lv1Id][regionId].isAll = checked;

                    _this.selectedData[_this.lv1Id]['checked'] = hasValue;
                    _this.selectedData[_this.lv1Id].isAll = _this.checkIsAll(_this.selectedData[_this.lv1Id], 1);

                    break;
                }
                case 3: {
                    var $list2 = _this.$wrap.find('.J-sp--list[data-lv=2]');
                    $list2.find('.J-sp--item.active input:checkbox')[0].checked = hasValue;
                    var hasValue2 = false;
                    var isAll2 = true;
                    $list2.find('input:checkbox:not(.J-sp--item--all)').each(function () {
                        if ($(this)[0].checked) {
                            hasValue2 = true;
                        } else {
                            isAll2 = false;
                        }
                    })

                    $list2.find('.J-sp--item--all')[0].checked = isAll2;

                    var $list1 = _this.$wrap.find('.J-sp--list[data-lv=1]');
                    $list1.find('.J-sp--item.active input:checkbox')[0].checked = hasValue2;

                    _this.selectedData[_this.lv1Id][_this.lv2Id]['checked'] = hasValue;
                    _this.selectedData[_this.lv1Id][_this.lv2Id][regionId]['checked'] = checked;
                    _this.selectedData[_this.lv1Id][_this.lv2Id].isAll = _this.checkIsAll(_this.selectedData[_this.lv1Id][_this.lv2Id], 2);

                    _this.selectedData[_this.lv1Id]['checked'] = hasValue2;
                    _this.selectedData[_this.lv1Id].isAll = _this.checkIsAll(_this.selectedData[_this.lv1Id], 1);

                    break;
                }
            }
        },
        allClick: function ($this) {
            var $list = $this.parents('.J-sp--list:first');
            var lv = $list.data('lv');
            var checked = $this[0].checked;
            var _this = this;

            switch (lv) {
                case 1: {
                    $list.find('.J-sp--item input:checkbox').each(function () {
                        var regionId = $(this).parents('.J-sp--item:first').data('id');
                        $(this)[0].checked = checked;
                        _this.selectedData[regionId].checked = checked;
                        _this.$wrap.find('.J-sp--list[data-lv=2],.J-sp--list[data-lv=3]').find('input:checkbox').prop('checked', checked);
                        _this.resetItem(_this.selectedData[regionId], checked);
                        _this.selectedData[regionId].isAll = checked;
                    });

                    break;
                }
                case 2: {
                    _this.$wrap.find('.J-sp--list[data-lv=1] .J-sp--item.active input:checkbox').prop('checked', checked);
                    _this.selectedData[_this.lv1Id].checked = checked;
                    $list.find('.J-sp--item input:checkbox').each(function () {
                        var regionId = $(this).parents('.J-sp--item:first').data('id');
                        $(this)[0].checked = checked;
                        _this.selectedData[_this.lv1Id][regionId].checked = checked;
                        _this.$wrap.find('.J-sp--list[data-lv=3] input:checkbox').prop('checked', checked);
                        _this.resetItem(_this.selectedData[_this.lv1Id][regionId], checked);
                        _this.selectedData[_this.lv1Id][regionId].isAll = checked;
                    });

                    var $list1 = _this.$wrap.find('.J-sp--list[data-lv=1]');
                    var isAll1 = true;
                    $list1.find('input:checkbox:not(.J-sp--item--all)').each(function () {
                        if (!$(this)[0].checked) {
                            isAll1 = false;
                        }
                    })

                    _this.$wrap.find('.J-sp--list[data-lv=1] .J-sp--item--all')[0].checked = isAll1;
                    _this.selectedData[_this.lv1Id].isAll = _this.checkIsAll(_this.selectedData[_this.lv1Id], 1);

                    break;
                }
                case 3: {
                    _this.$wrap.find('.J-sp--list[data-lv=2] .J-sp--item.active input:checkbox').prop('checked', checked);

                    $list.find('.J-sp--item input:checkbox').each(function () {
                        var regionId = $(this).parents('.J-sp--item:first').data('id');
                        $(this)[0].checked = checked;

                        _this.selectedData[_this.lv1Id][_this.lv2Id][regionId].checked = checked;
                    });

                    _this.selectedData[_this.lv1Id][_this.lv2Id].checked = checked;

                    var $list2 = _this.$wrap.find('.J-sp--list[data-lv=2]');
                    var isAll2 = true;
                    var hasValue2 = false;
                    $list2.find('input:checkbox:not(.J-sp--item--all)').each(function () {
                        if (!$(this)[0].checked) {
                            isAll2 = false;
                        } else {
                            hasValue2 = true;
                        }
                    })

                    _this.$wrap.find('.J-sp--list[data-lv=2] .J-sp--item--all')[0].checked = isAll2;
                    _this.$wrap.find('.J-sp--list[data-lv=1] .J-sp--item.active input:checkbox').prop('checked', hasValue2);
                    _this.selectedData[_this.lv1Id].checked = hasValue2;
                    var $list1 = _this.$wrap.find('.J-sp--list[data-lv=1]');
                    var isAll1 = true;
                    $list1.find('input:checkbox:not(.J-sp--item--all)').each(function () {
                        if (!$(this)[0].checked) {
                            isAll1 = false;
                        }
                    })

                    _this.$wrap.find('.J-sp--list[data-lv=1] .J-sp--item--all')[0].checked = isAll1;

                    _this.selectedData[_this.lv1Id][_this.lv2Id].isAll = _this.checkIsAll(_this.selectedData[_this.lv1Id][_this.lv2Id], 2);
                    _this.selectedData[_this.lv1Id].isAll = _this.checkIsAll(_this.selectedData[_this.lv1Id], 1);

                    break;
                }
            }
        },
        checkIsAll: function (data, lv) {
            var isAll = true;

            for (var item in data) {
                if (/^\d*$/.test(item)) {
                    if (!data[item].checked) {
                        isAll = false;
                    } else if (lv < 3) {
                        if (!this.checkIsAll(data[item], lv + 1)) {
                            isAll = false;
                        };
                    }
                }
            }

            return isAll;
        },
        resetItem: function (data, checked) {
            for (var item in data) {
                if (/^\d*$/.test(item)) {
                    data[item]['checked'] = checked;

                    this.resetItem(data[item], checked)
                }
            }

            return data;
        },
        initLv: function (lv, $item) {
            var list = [];
            var regionId = 1;
            var _this = this;

            if ($item) {
                regionId = $item.data('id');
            }

            $.ajax({
                url: this.config.url,
                data: {
                    regionId: regionId
                },
                async: false,
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        list = res.listData;
                    }
                }
            });

            var listHtml = this.listTmpl({
                list: list,
                lv: lv,
                needPrice: this.config.needPrice,
                onlyShow: this.config.onlyShow
            });

            this.$loading.hide();

            switch (lv) {
                case 1: {
                    $.each(list, function (i, item) {
                        if (!_this.selectedData[item.regionId]) {
                            _this.selectedData[item.regionId] = {};
                        }

                        _this.selectedData[item.regionId] = $.extend({}, item, _this.selectedData[item.regionId]);
                    });

                    this.$list.append(listHtml);

                    for (var item in _this.selectedData) {
                        if (/^\d*$/.test(item) && _this.selectedData[item].checked) {
                            $('.J-sp--list[data-lv=1]').find('.J-sp--item[data-id=' + item + '] input:checkbox')[0].checked = true;
                        }
                    }

                    break;
                }
                case 2: {
                    $('.J-sp--list[data-lv=2], .J-sp--list[data-lv=3]', _this.$wrap).remove();
                    this.$list.append(listHtml);

                    $.each(list, function (i, item) {
                        if (!_this.selectedData[_this.lv1Id][item.regionId]) {
                            _this.selectedData[_this.lv1Id][item.regionId] = {
                                isAll: _this.selectedData[_this.lv1Id].isAll
                            };
                        }

                        _this.selectedData[_this.lv1Id][item.regionId] = $.extend(
                            {},
                            item,
                            _this.selectedData[_this.lv1Id][item.regionId]);
                    })

                    var checkedLen = 0;
                    var allLen = 0;
                    for (var item in _this.selectedData[_this.lv1Id]) {
                        if (/^\d*$/.test(item)) {
                            allLen++;
                            if (_this.selectedData[_this.lv1Id][item].checked) {
                                checkedLen++;
                            }
                        }
                    }

                    for (var item in _this.selectedData[_this.lv1Id]) {
                        if (/^\d*$/.test(item) && checkedLen > 0 && _this.selectedData[_this.lv1Id][item].checked) {
                            $('.J-sp--list[data-lv=2]').find('.J-sp--item[data-id=' + item + '] input:checkbox')[0].checked = true;
                        }
                    }

                    if (allLen == checkedLen) {
                        $('.J-sp--list[data-lv=2]').find('.J-sp--item--all')[0].checked = true;
                    }


                    if (checkedLen == 0 && _this.selectedData[_this.lv1Id].checked) {
                        $('.J-sp--list[data-lv=2]').find('.J-sp--item input:checkbox').prop('checked', true);
                        $('.J-sp--list[data-lv=2]').find('.J-sp--item--all')[0].checked = true;

                        for (var item in _this.selectedData[_this.lv1Id]) {
                            if (/^\d*$/.test(item)) {
                                _this.selectedData[_this.lv1Id][item].checked = true;
                            }
                        }
                    }

                    break;
                }
                case 3: {
                    $('.J-sp--list[data-lv=3]', _this.$wrap).remove();
                    this.$list.append(listHtml);

                    $.each(list, function (i, item) {
                        if (!_this.selectedData[_this.lv1Id][_this.lv2Id][item.regionId]) {
                            _this.selectedData[_this.lv1Id][_this.lv2Id][item.regionId] = {};
                        }

                        _this.selectedData[_this.lv1Id][_this.lv2Id][item.regionId] = $.extend(
                            {},
                            item,
                            _this.selectedData[_this.lv1Id][_this.lv2Id][item.regionId]);
                    })

                    var checkedLen = 0;
                    var allLen = 0;
                    for (var item in _this.selectedData[_this.lv1Id][_this.lv2Id]) {
                        if (/^\d*$/.test(item)) {
                            allLen++;
                            if (_this.selectedData[_this.lv1Id][_this.lv2Id][item].checked) {
                                checkedLen++;
                            }
                        }
                    }

                    for (var item in _this.selectedData[_this.lv1Id][_this.lv2Id]) {
                        if (/^\d*$/.test(item) && checkedLen > 0 && _this.selectedData[_this.lv1Id][_this.lv2Id][item].checked) {
                            $('.J-sp--list[data-lv=3]').find('.J-sp--item[data-id=' + item + '] input:checkbox')[0].checked = true;
                        }
                    }

                    if (allLen == checkedLen) {
                        $('.J-sp--list[data-lv=3]').find('.J-sp--item--all')[0].checked = true;
                    }


                    if (checkedLen == 0 && _this.selectedData[_this.lv1Id][_this.lv2Id].checked) {
                        $('.J-sp--list[data-lv=3]').find('.J-sp--item input:checkbox').prop('checked', true);
                        $('.J-sp--list[data-lv=3]').find('.J-sp--item--all')[0].checked = true;

                        for (var item in _this.selectedData[_this.lv1Id][_this.lv2Id]) {
                            if (/^\d*$/.test(item)) {
                                _this.selectedData[_this.lv1Id][_this.lv2Id][item].checked = true;
                            }
                        }

                    }

                    break;
                }
            }
        },
        checkInputEnabled: function ($item) {
            var lv = $item.parents('.J-sp--list:first').data('lv');
            var disabled = !$item.find('input:checkbox')[0].checked;
            $item.find('input:text')[0].disabled = disabled;

            if (this['lv' + lv + 'Id'] == $item.data('id')) {
                if (lv == 1) {
                    $('.J-sp--list[data-lv=2], .J-sp--list[data-lv=3]').find('.J-sp--item').each(function () {
                        if ($(this).find('input:checkbox')[0].checked) {
                            $(this).find('input:text').attr('disabled', disabled);
                        }
                    });
                } else if (lv == 2) {
                    $('.J-sp--list[data-lv=3]').find('.J-sp--item').each(function () {
                        if ($(this).find('input:checkbox')[0].checked) {
                            $(this).find('input:text').attr('disabled', disabled);
                        }
                    });
                }
            }
        },
        resetChildInput: function ($item) {
            var lv = $item.parents('.J-sp--list:first').data('lv');
            var value = $.trim($item.find('input:text').val());
            if (this['lv' + lv + 'Id'] == $item.data('id')) {
                $('.J-sp--list[data-lv=' + (lv + 1) + ']').find('.J-sp--item').each(function () {
                    if ($(this).find('input:checkbox')[0].checked && !$.trim($(this).find('input:text').val())) {
                        $(this).find('input:text').val(value)
                    }
                });
            }
        },
        getPrev: function (regionId) {
            var data = this.selectedData[regionId];
            var dom = this.getDom(data, 1);

            $('.J-sp-prev--list[data-id=' + regionId + ']').empty().html(dom);
        },
        getDom(data, lv, prev) {
            var dom = '';
            var prevStr = prev || '';

            if (!data.checked) {
                dom += lv == 1 ? '<div>暂无选择项</div>' : '';
            } else if (data.isAll) {
                dom += '<div>' + prevStr + ' ' + data.regionName + '</div>';
            } else {
                var hasChild = false;
                for (var item in data) {
                    if (/^\d*$/.test(item)) {
                        dom += this.getDom(data[item], lv + 1, prevStr + ' ' + data.regionName);
                        hasChild = true;
                    }
                }

                if (!hasChild) {
                    dom += '<div>' + prevStr + ' ' + data.regionName + '</div>';
                }
            }

            return dom;
        }
    }

    window.SelectEdit = SelectEdit;


}.call(this);