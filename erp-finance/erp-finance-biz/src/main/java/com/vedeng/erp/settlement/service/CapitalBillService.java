package com.vedeng.erp.settlement.service;

import com.vedeng.erp.finance.dto.CapitalBillDto;

import java.util.List;

/**
 * 交易流水
 *
 * <AUTHOR>
 */
public interface CapitalBillService {

    /**
     * 新增交易流水
     *
     * @param capitalBillDto
     */
    void create(CapitalBillDto capitalBillDto);

    /**
     * 根据承兑汇票单号查询资金流水
     */
    List<CapitalBillDto> findAcceptanceBill(String acceptanceBillNo);

    void update(CapitalBillDto capitalBillDto);
}
