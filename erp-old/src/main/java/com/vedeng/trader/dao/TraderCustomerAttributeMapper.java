package com.vedeng.trader.dao;

import com.vedeng.trader.model.TraderCustomerAttribute;

import java.util.List;

public interface TraderCustomerAttributeMapper {

    /**
     * <b>Description:</b><br> 根据ParentId查询交易者的id集合
     * @param traderCustomerAttribute
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年11月10日 上午9:39:02
     */
    List<Integer> getTraderIdListByParentId(TraderCustomerAttribute traderCustomerAttribute);
}