<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="临时账期申请" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/steps/steps.min.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_edit.css?rnd=${resourceVersionKey}">
<div class="form-list  form-tips8 trader-customer-accountperiodapply">
    <div class="detail-block-tip vd-tip tip-orange" style="display:inline-block;width: 85%;">
        <i class="vd-tip-icon vd-icon icon-info2"></i>
        <div class="vd-tip-cnt">订单账期可以有多个,但只能用于指定的订单(创建商机/订单时，付款<br>
            计划可先选择先款后货,以获取订单号)</div>
    </div>
    <form method="post" action="" id="myform">
        <ul>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>订单号：</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" readonly name="saleorderNo" value="${saleorderNo != null ? saleorderNo : ''}" id="saleorderNo" style="background-color: #ddd">
                        <input type="hidden" name="relatedOrderId" value="${customerBillPeriod.relatedOrderId == null?0:customerBillPeriod.relatedOrderId}" id="relatedOrderId">
                    </div>
                </div>

            </li>
            <li>
                <div class="pop-friend-tips">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;--不调整则无需填写，若首次申请则必填，有效期原则上不可超过一年
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>额度调整：</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks" >
                        <select id="changeType">
                            <option value="上调">上调</option>
                            <option value="下调">下调</option>
                        </select>
                        <input style="margin-top: -3px" type="/order/saleorder/editSaleorderGoods" id="changeMount" value="0.00" placeholder="请输入需调整的额度"> <span>元&nbsp;&nbsp;&nbsp;&nbsp;调后额度：</span>
                        <span id="applyAmount" style="margin-top: 1px">
                            ${customerBillPeriodSummaryInfoDto == null ? 0 + customerBillPeriod.applyAmount:customerBillPeriodSummaryInfoDto.applyAmount + customerBillPeriod.applyAmount}
                        </span>
                        <input type="hidden" name="applyAmount" id="finalApplyAmount" value="${customerBillPeriod.applyAmount == null?0:customerBillPeriod.applyAmount}">
                    </div>
                </div>
            </li>
            <li>
                <div class="pop-friend-tips">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;--不调整可设置为0，调低范围不可大于当前可用额
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>结算周期:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" name="settlementPeriod" id="settlementPeriod" placeholder="请设置结算周期"
                               value="${customerBillPeriod.settlementPeriod == null ? '':customerBillPeriod.settlementPeriod}"> 天
                    </div>
                </div>
            </li>
            <li>
                <div class="pop-friend-tips">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;--基于结算标准+结算周期，确定所使用的账期理应归还的截止时间
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>预期毛利率：</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" name="expectedMargin" id="expectedMargin" readonly="readonly" style="background-color: #ddd"
                               value="${customerBillPeriod.expectedMargin==null?'':customerBillPeriod.expectedMargin}"> %
                    </div>
                </div>
            </li>
            <li>
                <div class="pop-friend-tips">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;--请如实填写数字,作为审批人判断依据
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>申请原因</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <textarea name="applyReason" readonly="readonly" placeholder="请填写申请原因"  style="width: 500px;height: 50px;background-color: #ddd">${customerBillPeriod.applyReason==null?'':customerBillPeriod.applyReason}</textarea>
                    </div>
                </div>
            </li>
            <li>
                <div class="pop-friend-tips">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;--请完整填写申请原因,便于上级审核,提高通过率
                </div>
            </li>
        </ul>
        <div class="pop-friend-tips">
            友情提醒<br/> 1、已归还的帐期可以重新使用，如果不需要修改帐期额度和时间，您不用重复申请;<br/>
            2、逾期不还,将对客户信用造成影响;
        </div>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="customerId" value="${traderCustomer.traderCustomerId}" />
            <input type="hidden" name="traderId" value="${traderCustomer.traderId}" />
            <input type="hidden" name="companyId" value="1" />
            <input type="hidden" name="billPeriodApplyId" value="${customerBillPeriod == null ? null:customerBillPeriod.billPeriodApplyId}" />
            <input type="hidden" name="billPeriodType" value="3" />
            <input type="hidden" name="operateType" value="${customerBillPeriod == null ? 1 : 2}"/>
            <input type="hidden" name="isVerifyChange" value="1"/>
            <input type="hidden" id="formerApplyAmount" value="${customerBillPeriod.applyAmount == null ? '':customerBillPeriod.applyAmount}">
            <input type="hidden" id="formerTotalAmount" value="${customerBillPeriodSummaryInfoDto == null ? 0:customerBillPeriodSummaryInfoDto.applyAmount}">
            <input type="hidden" name="billPeriodId" value="${customerBillPeriod == null ? 0: customerBillPeriod.billPeriodId}" />
            <button type="submit" id="submit" >提交</button>
            <button class="dele" id="close-layer" type="button">取消</button>
        </div>
    </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/customer/apply_order_verify_change.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>