package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.processor.AfterSaleToBalanceProcessor;
import com.vedeng.erp.kingdee.batch.writer.AfterSaleToBalanceWriter;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 销售售后退款至客户余额 流水推送job
 * (应收余额调整单)
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class PaySaleOrderAfterSaleBalanceBatchJob extends BaseJob {

    @Autowired
    private AfterSaleToBalanceProcessor afterSaleToBalanceProcessor;
    @Autowired
    private AfterSaleToBalanceWriter afterSaleToBalanceWriter;


    public Job afterSaleToBalanceBatchJob() {
        return jobBuilderFactory.get("afterSaleToBalanceBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(afterSaleToBalanceBatchStep())
                .build();
    }

    private Step afterSaleToBalanceBatchStep() {
        return stepBuilderFactory.get("余额-销售售后退款流水推送金蝶")
                .<BatchCapitalBillDto, KingDeeNeedReceiveDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(afterSaleToBalanceReader(null,null))
                .listener(baseReadListener)
                .processor(afterSaleToBalanceProcessor)
                .writer(afterSaleToBalanceWriter)
                .listener(baseProcessListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCapitalBillDto> afterSaleToBalanceReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                 @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCapitalBillDto batchCapitalBillDto = new BatchCapitalBillDto();
        batchCapitalBillDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime());
        batchCapitalBillDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCapitalBillDto.class.getSimpleName(),"afterSaleToBalanceReader", batchCapitalBillDto);
    }
}
