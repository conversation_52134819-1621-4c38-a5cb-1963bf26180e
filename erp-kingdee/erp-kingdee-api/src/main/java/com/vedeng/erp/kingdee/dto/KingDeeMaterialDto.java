package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶基础数据 物料
 * @date 2022/11/07 17:13
 */
@Getter
@Setter
@NoArgsConstructor
@WriteBackField(needBackField = {"fmaterialid"})
public class KingDeeMaterialDto extends KingDeeMqBaseDto {

    /**
     * 主键
     */
    private Integer kingDeeMaterialEntityId;

    /**
     * GOODS_FINANCE_ID
     */
    private Long goodsFinanceId;

    /**
     * 物料内码
     */
    @WriteBackField("FMATERIALID")
    @KingDeeID
    private String fmaterialid;
    /**
     * 创建组织
     */
    private String fCreateOrgId;
    /**
     * 使用组织
     */
    private String fUseOrgId;
    /**
     * 物料编码
     */
    @BusinessID
    private String fNumber;
    /**
     * 名称
     */
    private String fName;
    /**
     * 规格型号
     */
    private String fSpecification;
    /**
     * fOldNumber
     */
    private String fOldNumber;
    /**
     * 订货号
     */
    private String fQzokDhh;
    /**
     * 供应商物料编码
     */
    private String fQzokGyswlbm;
    /**
     * 品牌
     */
    private String fQzokPpptext;
    /**
     * 注册证号
     */
    private String fQzokZczhtext;
    /**
     * 是否为医疗器械产品
     */
    private String fQzokYlqxtext;
    /**
     * 医疗器械产线
     */
    private String fQzokYlqxcxtext;
    /**
     * 医疗器械用途
     */
    private String fQzokYlqxyttext;
    /**
     * fQzokYlqxxgjfl
     */
    private String fQzokYlqxxgjfl;
    /**
     * 医疗器械细分类
     */
    private String fQzokYlqxxfltext;
    /**
     * 医疗器械分类
     */
    private String fQzokYlqxfltext;
    /**
     * 商品运营一级分类
     */
    private String fQzokSpyyyjfltext;
    /**
     * 商品运营二级分类
     */
    private String fQzokSpyyejfltext;
    /**
     * 商品运营三级分类
     */
    private String fQzokSpyysjfltext;
    /**
     * 商品运营四级分类
     */
    private String fQzokSpyysijfltext;
    /**
     * 商品运营五级分类
     */
    private String fQzokSpyywjfltext;
    /**
     * 非医疗器械一级分类
     */
    private String fQzokFylqxyjfltext;
    /**
     * 非医疗器械二级分类
     */
    private String fQzokFylqxejfltext;
    /**
     * 主要产品线
     */
    private String fQzokZycpx;
    /**
     * 产线划分类型
     */
    private String fQzokCxfl;

    /**
     * 安调分类
     */
    private String fQzokAtfl;


    /**
     * 税收分类编码
     */
    private String fTaxcategoryCodeId;

    /**
     * 基本信息
     */
    private KingDeeMaterialSubHeadEntityDto subHeadEntity;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.BD_MATERIAL;
    }

    public KingDeeMaterialDto(KingDeeBizEnums kingDeeBizEnums) {
        super(kingDeeBizEnums);
        this.fCreateOrgId = KingDeeConstant.ORG_ID.toString();
        this.fUseOrgId = KingDeeConstant.ORG_ID.toString();
    }
}
