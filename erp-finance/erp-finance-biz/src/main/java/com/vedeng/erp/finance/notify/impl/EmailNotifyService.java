package com.vedeng.erp.finance.notify.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceQrRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceQrResponseDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.notify.NotifyService;
import com.vedeng.erp.finance.service.TaxesOpenApiService;
import com.vedeng.infrastructure.email.service.impl.JavaMailSenderEmailService;
import com.vedeng.infrastructure.shorturl.service.ShortUrlApiService;
import com.vedeng.infrastructure.sms.service.SmsService;
import com.vedeng.infrastructure.taxes.config.TaxesConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/22 15:46
 **/
@Service
@Slf4j
public class EmailNotifyService implements NotifyService {

    @Autowired
    private TaxesConfig taxesConfig;


    @Autowired
    private TaxesOpenApiService taxesOpenApiService;


    @Autowired
    private JavaMailSenderEmailService javaMailSenderEmailService;

    @Override
    public void notifyMessage(InvoiceDto invoiceDto) {
        if (!taxesConfig.openInvoiceSendEmail) {
            log.info("开票发送邮件关闭");
            return;
        }
        log.info("开票发送邮件：{}", JSON.toJSONString(invoiceDto));
        if (StrUtil.isEmpty(invoiceDto.getEmail()) && StrUtil.isEmpty(invoiceDto.getInvoiceTraderContactEmail())) {
            log.info("邮箱地址为空不发邮件：{}", JSON.toJSONString(invoiceDto));
            return;
        }

        SaleInvoiceQrRequestDto saleInvoiceQrRequestDto = new SaleInvoiceQrRequestDto();
        saleInvoiceQrRequestDto.setKprq(DateUtil.formatDateTime(invoiceDto.getOpenInvoiceTime()));
        saleInvoiceQrRequestDto.setFphm(invoiceDto.getInvoiceNo());
        saleInvoiceQrRequestDto.setNsrsbh(taxesConfig.getTaxNo());
        SaleInvoiceQrResponseDto saleInvoiceQrResponseDto = (SaleInvoiceQrResponseDto) taxesOpenApiService.openapi(saleInvoiceQrRequestDto,
                TaxesInterfaceCodeEnum.QR_CODE);

        if (saleInvoiceQrResponseDto.getIsSuccess()) {
            invoiceDto.setTaxUrl(saleInvoiceQrResponseDto.getData());
            try {
                javaMailSenderEmailService.sendMailHtml(StrUtil.isNotEmpty(invoiceDto.getEmail()) ? invoiceDto.getEmail() : invoiceDto.getInvoiceTraderContactEmail(), "您申请的发票已开出", bindEmail(invoiceDto));
                return;
            } catch (Exception e) {
                log.error("数电发票邮件发送失败：{}", JSON.toJSONString(invoiceDto), e);
            }
        }
        log.info("开票发送邮件获取链接失败：{},result：{}", JSON.toJSONString(invoiceDto), JSON.toJSONString(saleInvoiceQrResponseDto));
    }

    /**
     * 邮件模板
     * @param invoiceDto
     * @return
     */
    private String bindEmail(InvoiceDto invoiceDto) {
        return StrUtil.format(taxesConfig.invoiceEmailTemple, invoiceDto.getOrderNo(), invoiceDto.getTaxUrl(), invoiceDto.getTaxUrl(), invoiceDto.getInvoiceNo(), DateUtil.formatDate(invoiceDto.getOpenInvoiceTime()));
    }
}
