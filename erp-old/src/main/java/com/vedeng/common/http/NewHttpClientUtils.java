package com.vedeng.common.http;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.model.ResultInfo;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.*;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.Page;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Auther: Duke.li
 * @Date: 2019/7/12 09:33
 */
public class NewHttpClientUtils {
	private static Logger logger = LoggerFactory.getLogger(NewHttpClientUtils.class);
    // 编码格式。发送编码格式统一用UTF-8
    private static final String ENCODING = "UTF-8";

    // 设置连接超时时间，单位毫秒。
    private static final int CONNECT_TIMEOUT = 10000;

    // 请求获取数据的超时时间(即响应时间)，单位毫秒。
    private static final int SOCKET_TIMEOUT = 10000;


    private final static RequestConfig DEFAULT_REQUEST_CONFIG = RequestConfig.custom().
            setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();

    private final static CloseableHttpClient HTTP_CLIENT =  HttpClientBuilder.create()
            .disableCookieManagement()
            .disableRedirectHandling()
            .disableAuthCaching()
            .setConnectionTimeToLive(10, TimeUnit.MINUTES)
            .setMaxConnPerRoute(100)
            .setMaxConnTotal(100)
            .build();

    /**
     * 发送get请求；不带请求头和请求参数
     *
     * @param url 请求地址
     * @return
     * @throws Exception
     */
    public static ResultInfo doGet(String url) throws Exception {
        return doGet(url, null, null);
    }

    /**
     * 发送get请求；带请求参数
     *
     * @param url    请求地址
     * @param params 请求参数集合
     * @return
     * @throws Exception
     */
    public static ResultInfo doGet(String url, Map<String, String> params) throws Exception {
        return doGet(url, null, params);
    }

    /**
     * 发送get请求；带请求头和请求参数
     *
     * @param url     请求地址
     * @param headers 请求头集合
     * @param params  请求参数集合
     * @return
     * @throws Exception
     */
    public static ResultInfo doGet(String url, Map<String, String> headers, Map<String, String> params) throws Exception {
        // 创建httpClient对象
       // CloseableHttpClient httpClient = HttpClients.createDefault();

        // 创建访问的地址
        URIBuilder uriBuilder = new URIBuilder(url);
        if (params != null) {
            Set<Map.Entry<String, String>> entrySet = params.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                uriBuilder.setParameter(entry.getKey(), entry.getValue());
            }
        }

        // 创建http对象
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        /**
         * setConnectTimeout：设置连接超时时间，单位毫秒。
         * setConnectionRequestTimeout：设置从connect Manager(连接池)获取Connection
         * 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
         * setSocketTimeout：请求获取数据的超时时间(即响应时间)，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
         */
     //   RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();
        httpGet.setConfig(DEFAULT_REQUEST_CONFIG);

        // 设置请求头
        packageHeader(headers, httpGet);

        // 创建httpResponse对象
        CloseableHttpResponse httpResponse = null;

        try {
            // 执行请求并获得响应结果
            return getHttpClientResult(httpResponse, HTTP_CLIENT, httpGet);
        } finally {
            // 释放资源
            release(httpResponse, HTTP_CLIENT);
        }
    }

    /**
     * 发送post请求；不带请求头和请求参数
     *
     * @param url 请求地址
     * @return
     * @throws Exception
     */
    public static ResultInfo doPost(String url) throws Exception {
        return doPost(url, null, null);
    }

    /**
     * 发送post请求；带请求参数
     *
     * @param url    请求地址
     * @param params 参数集合
     * @return
     * @throws Exception
     */
    public static ResultInfo doPost(String url, Map<String, String> params) throws Exception {
        return doPost(url, null, params);
    }

    /**
     * 发送post请求；带请求头和请求参数
     *
     * @param url     请求地址
     * @param headers 请求头集合
     * @param params  请求参数集合
     * @return
     * @throws Exception
     */
    public static ResultInfo doPost(String url, Map<String, String> headers, Map<String, String> params) throws Exception {
        // 创建httpClient对象
       // CloseableHttpClient httpClient = HttpClients.createDefault();

        // 创建http对象
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(DEFAULT_REQUEST_CONFIG);
        packageHeader(headers, httpPost);

        // 封装请求参数
        packageParam(params, httpPost);

        // 创建httpResponse对象
        CloseableHttpResponse httpResponse = null;

        try {
            // 执行请求并获得响应结果
            return getHttpClientResult(httpResponse, HTTP_CLIENT, httpPost);
        } finally {
            // 释放资源
            release(httpResponse,  null);
        }
    }
    public static ResultInfo doPostForPool(String url, Map<String, String> params) throws IOException {

        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(5000)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectTimeout(5000)
                .build();

        httpPost.setConfig(requestConfig);
        // 封装请求参数
        packageParam(params, httpPost);
        // 创建httpResponse对象
        CloseableHttpResponse httpResponse = null;

        try {
            // 执行请求
            httpResponse = HTTP_CLIENT.execute(httpPost);
            // 获取返回结果
            if (httpResponse != null && httpResponse.getStatusLine() != null) {
                String content = "";
                if (httpResponse.getEntity() != null) {
                    content = EntityUtils.toString(httpResponse.getEntity(), ENCODING);
                }
                return new ResultInfo(httpResponse.getStatusLine().getStatusCode(),"请求成功", content);
            }
            return new ResultInfo(HttpStatus.SC_INTERNAL_SERVER_ERROR, httpResponse.getStatusLine()+"");
        } finally {
            // 释放资源
            release(httpResponse, null);
        }

    }
    public static net.sf.json.JSONObject httpPost(String url, net.sf.json.JSONObject json){
        //post请求返回结果
       // DefaultHttpClient httpClient = new DefaultHttpClient();
//        net.sf.json.JSONObject jsonResult = null;
//        HttpPost method = new HttpPost(url);
//
//        RequestConfig requestConfig = RequestConfig.custom()
//                .setConnectionRequestTimeout(2000)
//                .setSocketTimeout(SOCKET_TIMEOUT)
//                .setConnectTimeout(3000)
//                .build();
//
//        method.setConfig(requestConfig);
//        CloseableHttpResponse result=null;
//        //logger.info("start http post url:"+url+"\t param:"+json);
//        try {
//            if (null != json ) {
//                //解决中文乱码问题
//                StringEntity entity = new StringEntity(json.toString(), "utf-8");
//                entity.setContentEncoding("UTF-8");
//                entity.setContentType("application/json");
//                method.setEntity(entity);
//            }
//              result = HTTP_CLIENT.execute(method);
//            url = URLDecoder.decode(url, "UTF-8");
//            /**请求发送成功，并得到响应**/
//            if (result.getStatusLine().getStatusCode() == 200) {
//                String str = "";
//                try {
//                    /**读取服务器返回过来的json字符串数据**/
//                    str = EntityUtils.toString(result.getEntity());
//                    /**把json字符串转换成json对象**/
//                    jsonResult = net.sf.json.JSONObject.fromObject(str);
//                } catch (Exception e) {
//                    logger.error("httpPost：返回数据错误"+str,e);
//                }
//            }else{
//                logger.error("httpPost：返回状态码错误"+result.getStatusLine().getStatusCode());
//            }
//        } catch (IOException e) {
//            logger.error("httpPost：请求发生异常",e);
//        }finally {
//           release(result,null);
//        }
        if(json==null){
            return null;
        }
        return httpPost(url,json.toString());
    }
    public static net.sf.json.JSONObject httpPost(String url, net.sf.json.JSONArray json){
        if(json==null){
            return null;
        }
        return httpPost(url,json.toString());
//        //post请求返回结果
//       // DefaultHttpClient httpClient = new DefaultHttpClient();
//        net.sf.json.JSONObject jsonResult = null;
//        HttpPost method = new HttpPost(url);
//        CloseableHttpResponse result=null;
//        try {
//            if (null != json ) {
//                //解决中文乱码问题
//                StringEntity entity = new StringEntity(json.toString(), "utf-8");
//                entity.setContentEncoding("UTF-8");
//                entity.setContentType("application/json");
//                method.setEntity(entity);
//            }
//              result = HTTP_CLIENT.execute(method);
//            url = URLDecoder.decode(url, "UTF-8");
//            /**请求发送成功，并得到响应**/
//            if (result.getStatusLine().getStatusCode() == 200) {
//                String str = "";
//                try {
//                    /**读取服务器返回过来的json字符串数据**/
//                    str = EntityUtils.toString(result.getEntity());
//                    /**把json字符串转换成json对象**/
//                    jsonResult = net.sf.json.JSONObject.fromObject(str);
//                } catch (Exception e) {
//                }
//            }
//        } catch (IOException e) {
//            logger.error("httpPost：请求发生异常",e);
//        }finally {
//            release(result,null);
//        }
//        return jsonResult;
    }

    /**
     * 最大一分钟
     * @param url
     * @param json
     * @return
     */
    public static net.sf.json.JSONObject httpPostNotTimeOut(String url, String json ){
        //post请求返回结果
        HttpPost method = new HttpPost(url);

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(600000)
                .setSocketTimeout(600000)
                .setConnectTimeout(600000)
                .build();

        method.setConfig(requestConfig);
        net.sf.json.JSONObject jsonResult = null;
        //HttpPost method = new HttpPost(url);
        CloseableHttpResponse httpResponse = null;
        try {
            if (null != json ) {
                //解决中文乱码问题
                StringEntity entity = new StringEntity(json.toString(), "utf-8");
                entity.setContentType("application/json");
                method.setEntity(entity);
            }
            httpResponse= HTTP_CLIENT.execute(method);

            /**请求发送成功，并得到响应**/
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                String str  = EntityUtils.toString(httpResponse.getEntity());
                jsonResult = net.sf.json.JSONObject.fromObject(str);
            }else{
                logger.error("请求状态不为200"+url+json+httpResponse.getStatusLine()+EntityUtils.toString(httpResponse.getEntity()));
            }
        }catch (TruncatedChunkException te){
            logger.error("httpPost：请求发生异常",te);
        }catch (IOException e) {
            logger.error("httpPost：请求发生异常",e);
        }finally {
            release(httpResponse,null);
        }
        return jsonResult;
    }
    public static net.sf.json.JSONObject httpPost(String url, String json){
        //post请求返回结果
        HttpPost method = new HttpPost(url);
        method.setConfig(DEFAULT_REQUEST_CONFIG);
        net.sf.json.JSONObject jsonResult = null;
        CloseableHttpResponse httpResponse = null;
        try {
            if (null != json ) {
                //解决中文乱码问题
                StringEntity entity = new StringEntity(json.toString(), "utf-8");
                entity.setContentType("application/json");
                method.setEntity(entity);
            }
            httpResponse= HTTP_CLIENT.execute(method);
            /**请求发送成功，并得到响应**/
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                String str  = EntityUtils.toString(httpResponse.getEntity());
                jsonResult = net.sf.json.JSONObject.fromObject(str);
            }else{
                logger.error("请求状态不为200"+url+json+httpResponse.getStatusLine()+EntityUtils.toString(httpResponse.getEntity()));
            }
        } catch (Exception e) {
            logger.error("httpPost：请求发生异常{} {} ",url,json,e);
        }finally {
            release(httpResponse,null);
        }
        return jsonResult;
    }
    public static net.sf.json.JSONObject doPut(String url,String json){
        HttpPut httpPut = new HttpPut(url);
        net.sf.json.JSONObject jsonResult = null;
        CloseableHttpResponse result = null;
        httpPut.setConfig(DEFAULT_REQUEST_CONFIG);
        try {
            if (null != json ) {
                //解决中文乱码问题
                StringEntity entity = new StringEntity(json.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPut.setEntity(entity);
            }
            result = HTTP_CLIENT.execute(httpPut);
            url = URLDecoder.decode(url, "UTF-8");
            /**请求发送成功，并得到响应**/
            if (result.getStatusLine().getStatusCode() == 200) {
                String str = "";
                try {
                    /**读取服务器返回过来的json字符串数据**/
                    str = EntityUtils.toString(result.getEntity());
                    /**把json字符串转换成json对象**/
                    jsonResult = net.sf.json.JSONObject.fromObject(str);
                } catch (Exception e) {
                    logger.error("【doPut】处理异常",e);
                }
            }
        } catch (IOException e) {
            logger.error("httpPost：请求发生异常",e);
        }finally {
            release(result,null);
        }
        return jsonResult;
    }
    /**
     * 发送put请求；不带请求参数
     *
     * @param url    请求地址
     * @return
     * @throws Exception
     */
//    public static ResultInfo doPut(String url) throws Exception {
//        return doPut(url);
//    }

    /**
     * 发送put请求；带请求参数
     *
     * @param url    请求地址
     * @param params 参数集合
     * @return
     * @throws Exception
     */
//    public static ResultInfo doPut(String url, Map<String, String> params) throws Exception {
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//        HttpPut httpPut = new HttpPut(url);
//        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();
//        httpPut.setConfig(requestConfig);
//
//        packageParam(params, httpPut);
//
//        CloseableHttpResponse httpResponse = null;
//
//        try {
//            return getHttpClientResult(httpResponse, httpClient, httpPut);
//        } finally {
//            release(httpResponse, httpClient);
//        }
//    }

    /**
     * 发送delete请求；不带请求参数
     *
     * @param url    请求地址
     * @return
     * @throws Exception
     */
//    public static ResultInfo doDelete(String url) throws Exception {
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//        HttpDelete httpDelete = new HttpDelete(url);
//        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();
//        httpDelete.setConfig(requestConfig);
//
//        CloseableHttpResponse httpResponse = null;
//        try {
//            return getHttpClientResult(httpResponse, httpClient, httpDelete);
//        } finally {
//            release(httpResponse, httpClient);
//        }
//    }

    /**
     * 发送delete请求；带请求参数
     *
     * @param url    请求地址
     * @param params 参数集合
     * @return
     * @throws Exception
     */
//    public static ResultInfo doDelete(String url, Map<String, String> params) throws Exception {
//        if (params == null) {
//            params = new HashMap<String, String>();
//        }
//
//        params.put("_method", "delete");
//        return doPost(url, params);
//    }

    /**
     * Description: 封装请求头
     *
     * @param params
     * @param httpMethod
     */
    public static void packageHeader(Map<String, String> params, HttpRequestBase httpMethod) {
        // 封装请求头
        if (params != null) {
            Set<Map.Entry<String, String>> entrySet = params.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                // 设置到请求头到HttpRequestBase对象中
                httpMethod.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * Description: 封装请求参数
     *
     * @param params
     * @param httpMethod
     * @throws UnsupportedEncodingException
     */
    public static void packageParam(Map<String, String> params, HttpEntityEnclosingRequestBase httpMethod)
            throws UnsupportedEncodingException {
        // 封装请求参数
        if (params != null) {
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            Set<Map.Entry<String, String>> entrySet = params.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            // 设置到请求的http对象中
            httpMethod.setEntity(new UrlEncodedFormEntity(nvps, ENCODING));
        }
    }

    /**
     * Description: 获得响应结果
     *
     * @param httpResponse
     * @param httpClient
     * @param httpMethod
     * @return
     * @throws Exception
     */
    public static ResultInfo getHttpClientResult(CloseableHttpResponse httpResponse,
                                                 CloseableHttpClient httpClient, HttpRequestBase httpMethod) throws Exception {
        // 执行请求
        httpResponse = httpClient.execute(httpMethod);
        // 获取返回结果
        if (httpResponse != null && httpResponse.getStatusLine() != null) {
            String content = "";
            if (httpResponse.getEntity() != null) {
                content = EntityUtils.toString(httpResponse.getEntity(), ENCODING);
            }
            return new ResultInfo(httpResponse.getStatusLine().getStatusCode(),"请求成功", content);
        }
        return new ResultInfo(HttpStatus.SC_INTERNAL_SERVER_ERROR, null);
    }

    /**
     * Description: 释放资源
     *
     * @param httpResponse
     * @param httpClient
     * @throws IOException
     */
    public static void release(CloseableHttpResponse httpResponse, CloseableHttpClient httpClient)  {
        // 释放资源
        try {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }catch (Exception e){
            //ignor
        }
    }


    public static String httpPostWithRawContent(String url, String json){
        //post请求返回结果
        HttpPost method = new HttpPost(url);
        method.setConfig(DEFAULT_REQUEST_CONFIG);
        CloseableHttpResponse httpResponse = null;
        try {
            if (null != json ) {
                //解决中文乱码问题
                StringEntity entity = new StringEntity(json.toString(), "utf-8");
                entity.setContentType("application/json");
                method.setEntity(entity);
            }
            httpResponse= HTTP_CLIENT.execute(method);
            /**请求发送成功，并得到响应**/
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                return EntityUtils.toString(httpResponse.getEntity());
            }else{
                logger.error("请求状态不为200"+url+json+httpResponse.getStatusLine()+EntityUtils.toString(httpResponse.getEntity()));
            }
        } catch (Exception e) {
            logger.error("httpPost：请求发生异常{} {} ",url,json,e);
        }finally {
            release(httpResponse,null);
        }
        return null;
    }
}
