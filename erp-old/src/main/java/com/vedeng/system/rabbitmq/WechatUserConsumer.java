package com.vedeng.system.rabbitmq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.authorization.dao.UserDetailMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.model.UserDetail;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.Salt;
import com.vedeng.system.model.WechatUser;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.function.Consumer;

/**
 * 处理uac过来的用户信息变更事件
 *
 * <AUTHOR>
 */
@Component
public class WechatUserConsumer extends AbstractMessageListener {
    public static final Logger logger = LoggerFactory.getLogger(WechatUserConsumer.class);

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserDetailMapper userDetailMapper;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        List<WechatUser> list = JSONObject.parseArray(messageBody, WechatUser.class);

        try {
            for (WechatUser wechatUser : list) {
                logger.info("当前同步员工信息{}", wechatUser);
                try {
                    processWechatUser(wechatUser);
                } catch (Exception e) {
                    logger.error("同步员工信息至ERP异常{}", wechatUser, e);
                }
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            logger.error("同步员工信息至ERP异常", e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ex) {
                logger.error("注册用户同步消费失败，将消息返回给RabbitMQ错误：", ex);
            }
        }
    }

    private void processWechatUser(WechatUser wechatUser) {
        User user = userMapper.selectByPrimaryKey(wechatUser.getAccountId());

        if (user != null) {
            updateUser(user, wechatUser);
        } else {
            addUser(wechatUser);
        }
    }

    private void updateUser(User user, WechatUser wechatUser) {
        //boolean updateUserFlag = updateUserFields(user, wechatUser);
        //if (updateUserFlag) {
        //    userMapper.update(user);
        //    logger.info("用户id{}, 更新用户信息{}", user.getUserId(), user);
        //}

        UserDetail userDetail = userDetailMapper.getUserDetail(user.getUserId());
        if (userDetail != null) {
            boolean updateUserDetailFlag = updateUserDetailFields(userDetail, wechatUser);
            if (updateUserDetailFlag) {
                userDetailMapper.update(userDetail);
                logger.info("用户id{}, 更新用户明细信息{}", userDetail.getUserId(), userDetail);
            }
        } else {
            addUserDetail(user, wechatUser);
        }
    }

    private boolean updateUserFields(User user, WechatUser wechatUser) {
        boolean flag = false;

        flag |= updateField(user::setUsername, user.getUsername(), wechatUser.getDisplayName());
        flag |= updateField(user::setNumber, user.getNumber(), wechatUser.getJobNumber());
        //flag |= updateField(user::setIsDisabled, user.getIsDisabled(), wechatUser.getEnable());

        if (flag) {
            user.setUpdater(ErpConstant.NJADMIN_ID);
            user.setModTime(System.currentTimeMillis());
        }

        return flag;
    }


    /**
     * 更新用户详细信息字段。
     * 通过比较微信用户信息和现有用户详细信息的字段值，如果字段值有变化，则更新字段，并返回true表示至少有一个字段被更新；否则返回false。
     *
     * @param userDetail 当前的用户详细信息对象
     * @param wechatUser 微信用户信息对象，用于对比和更新用户详细信息
     * @return 如果至少有一个字段被更新，则返回true；否则返回false。
     */
    private boolean updateUserDetailFields(UserDetail userDetail, WechatUser wechatUser) {
        boolean flag = false;

        //flag |= updateField(userDetail::setRealName, userDetail.getRealName(), wechatUser.getRealName());
        //flag |= updateField(userDetail::setSex, userDetail.getSex(), wechatUser.getSex());
        //flag |= updateField(userDetail::setEmail, userDetail.getEmail(), wechatUser.getEmail());
        flag |= updateField(userDetail::setAliasHeadPicture, userDetail.getAliasHeadPicture(), wechatUser.getAliasHeadPicture());

        return flag;
    }

    /**
     * 比较旧值和新值，如果值有变化，则使用新值更新对象的指定字段，并返回true；否则返回false。
     *
     * @param setter 函数式接口，用于更新对象的指定字段
     * @param oldValue 当前字段的值
     * @param newValue 新的字段值
     * @param <T> 字段值的类型
     * @return 如果字段值有变化且成功更新，则返回true；否则返回false。
     */
    private <T> boolean updateField(Consumer<T> setter, T oldValue, T newValue) {
        if (newValue == null) {
            return false;
        }
        // 如果旧值为null且新值不为null，或者旧值和新值不相等，则更新字段值并返回true
        if ((oldValue == null && newValue != null) || (oldValue != null && !oldValue.equals(newValue))) {
            setter.accept(newValue);
            return true;
        }
        return false;
    }



    private void addUser(WechatUser wechatUser) {
        User user = new User();
        UserDetail userDetail = new UserDetail();

        user.setUserId(wechatUser.getAccountId());
        user.setUsername(wechatUser.getDisplayName());
        user.setNumber(wechatUser.getJobNumber());
        user.setIsDisabled(wechatUser.getEnable() == 0 ? 1 : 0);
        user.setCompanyId(1);
        String salt = new Salt().createSalt(false);
        user.setSalt(salt);
        user.setPassword(DigestUtils.md5Hex("123456" + salt));
        user.setAddTime(System.currentTimeMillis());
        user.setCreator(ErpConstant.NJADMIN_ID);
        user.setStaff(1);

        userDetail.setUserId(user.getUserId());
        userDetail.setEmail(wechatUser.getEmail());
        userDetail.setRealName(wechatUser.getRealName());
        userDetail.setSex(wechatUser.getSex());

        userMapper.insert(user);
        logger.info("新增用户信息{}", user);
        userDetailMapper.insert(userDetail);
        logger.info("用户id{}, 新增用户明细信息{}", userDetail.getUserId(), userDetail);
    }

    private void addUserDetail(User user, WechatUser wechatUser) {
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(user.getUserId());
        userDetail.setEmail(wechatUser.getEmail());
        userDetail.setRealName(wechatUser.getRealName());
        userDetail.setSex(wechatUser.getSex());
        userDetailMapper.insert(userDetail);
        logger.info("用户id{}, 新增用户明细信息{}", userDetail.getUserId(), userDetail);
    }
}

