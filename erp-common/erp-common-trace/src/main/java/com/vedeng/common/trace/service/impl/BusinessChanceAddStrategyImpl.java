package com.vedeng.common.trace.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.service.TrackBaseService;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.util.TrackUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 新建商机
 * @ClassName:  BusinessChanceAddStrategyImpl     
 * @author: <PERSON>.yang
 * @date:   2024年6月12日 上午10:06:48    
 * @Copyright:
 */
@Component
public class BusinessChanceAddStrategyImpl extends TrackBaseService implements TrackStrategy {

	private static final Logger LOGGER = LoggerFactory.getLogger(BusinessChanceAddStrategyImpl.class);
	
	@Override
	public EventTrackingEnum getTrackStrategy() {
		return EventTrackingEnum.PRE_SALE_NEW_BUSINESS_CHANCE;
	}

	@Override
	public boolean checkParamsLegal(TrackParamsData trackParamsData) {
		//获取唯一标识符
		Map<String, Object> trackParams = trackParamsData.getTrackParams();
		String primaryKey = trackParamsData.getEventTrackingEnum().getPrimaryKey();
		String primaryValue = TrackUtils.getJsonValue(new JSONObject(trackParams),primaryKey);
		//唯一标识符获取不到，不能埋点
		if(StringUtils.isEmpty(primaryValue) || "0".equals(primaryValue)) {
			LOGGER.info("=======埋点档案：{}，必填字段：{}缺失...",trackParamsData.getEventTrackingEnum().getArchivedName(),primaryKey);
			return false;
		}
		if(Objects.isNull(trackParams.get("track_user"))) {
			LOGGER.info("=======埋点档案：{}，必填字段：{}缺失...",trackParamsData.getEventTrackingEnum().getArchivedName(),"track_user");
			return false;
		}
		return true;
	}

}
