package com.wms.unitconversionorder.service;

import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @description 库存转换单接口
 * <AUTHOR>
 * @date 2023/2/17 11:02
 **/
public interface WmsUnitConversionOrderService {

    /**
     * 保存库存转换单
     * @param wmsUnitConversionOrderDto 页面保存数据
     * @return 主键
     */
    Integer add(WmsUnitConversionOrderDto wmsUnitConversionOrderDto);

    /**
     * 编辑库存转换单 明细id要传 需要根据明细id是否存在判断 新增删除还是修改
     * @param wmsUnitConversionOrderDto 页面数据
     * @return
     */
    void edit(WmsUnitConversionOrderDto wmsUnitConversionOrderDto);


    /**
     * 根据主键id 查询订单信息 订单 + 订单明细数据
     *
     * @param wmsUnitConversionOrderId
     * @return WmsUnitConversionOrderDto 订单 + 订单明细数据
     */
    WmsUnitConversionOrderDto getViewData(Integer wmsUnitConversionOrderId);

    /**
     * 原始wms出入库日志表
     * 获取出库单信息
     * @param wmsUnitConversionOrderId 订单id
     * @return List<WarehouseGoodsOperateLog> 订单
     */
    List<WarehouseGoodsOperateLog> getOutLog(Integer wmsUnitConversionOrderId);

    /**
     * 原始wms出入库日志表
     * 获取入库单信息
     * @param wmsUnitConversionOrderId 订单id
     * @return List<WarehouseGoodsOperateLog> 订单
     */
    List<WarehouseGoodsOperateLog> getInLog(Integer wmsUnitConversionOrderId);

    /**
     * 审核通过
     *
     * @param wmsUnitConversionOrderId 订单id
     * @param checkStatus 审核状态
     */
    void audit(Integer wmsUnitConversionOrderId, Integer checkStatus);

    /**
     * 下发出入库任务
     * @param wmsUnitConversionOrderId 订单id
     */
    void putWmsTask(Integer wmsUnitConversionOrderId) throws Exception;

    /**
     * 单位转换单申请审核
     *
     * @param request                  HttpServletRequest
     * @param wmsUnitConversionOrderId 单位转换单id
     */
    void applyAudit(HttpServletRequest request, Integer wmsUnitConversionOrderId);

    /**
     * 查询sku的库存数量
     * @param skuNo
     * @return
     */
    Integer getSkuStockNum(String skuNo);
}
