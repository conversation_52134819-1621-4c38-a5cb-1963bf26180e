package com.vedeng.erp.finance.service;

import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.dto.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务发票
 * @date 2023/9/20 13:03
 */
public interface FullyDigitalInvoiceService {


    /**
     * 数电发票下载是否开启
     *
     * @return 是否开启
     */
    boolean isEnableGlobal();

    /**
     * 数电发票开具销项票（销售蓝票）
     *
     * @param invoiceApplyDto 开票申请
     * @return 发票结果返回dto
     */
    OpenInvoiceResultDto openBlueInvoice(InvoiceApplyDto invoiceApplyDto);

    /**
     * 数电发票开具销项票（销售红票）
     * @param invoiceRedConfirmationDto 红字确认单
     */
    void openRedInvoice(InvoiceRedConfirmationDto invoiceRedConfirmationDto);

    /**
     * 数电发票下载
     *
     * @param downloadInvoice
     */
    void downloadInvoice(DownloadInvoice downloadInvoice);

    /**
     * 重试下载
     *
     * @param invoiceTaxSystemRecordDto
     */
    void errorRetryDownInvoice(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto, String type);

    /**
     * 数电发票蓝票详情（销售蓝票）
     *
     * @param invoiceNo 发票号
     * @return 发票结果返回dto
     */
    String getBlueInvoiceOpenDate(String invoiceNo);


    /**
     * 获取数电发票二维码
     * @param invoiceId
     * @return
     */
    String getInvoiceQrCode(Integer invoiceId);

    /**
     * 超出重试预警
     * @param invoiceTaxSystemRecordDto
     * @param msg
     */
    void retryOut(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto, String msg);


}
