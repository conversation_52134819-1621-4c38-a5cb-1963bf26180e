package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.*;

import java.math.BigDecimal;

/**
 * 金蝶安调记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "KING_DEE_INSTALL_SERVICE_RECORD")
public class KingDeeInstallServiceRecordEntity extends BaseEntity {
    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 单据内码
     */
    private String fid;

    /**
     * 单据编号
     */
    private String fBillNo;

    /**
     * 单据日期
     */
    private String fQzokDate;

    /**
     * 安调组织
     */
    private String fQzokOrgId;

    /**
     * 验收时间
     */
    private String fQzokYssj;

    /**
     * 签收时间
     */
    private String fQzokQssj;

    /**
     * 本次服务数量
     */
    private BigDecimal fQzokBcfwsl;

    /**
     * 验收方式
     */
    private String fQzokYsfs;

    /**
     * 验收结论
     */
    private String fQzokYsjl;

    /**
     * 原始订单号
     */
    private String fQzokYsddh;

    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;

    /**
     * 业务类型
     */
    private String fQzokYwlx;

    /**
     * 对应物料的编码
     */
    private String fQzokWlbm;

    /**
     * 对应物料序列号
     */
    private String fQzokXlh;

    /**
     * 关联销售出库单ID
     */
    private String fQzokFid;

    /**
     * 关联销售出库单行ID
     */
    private String fQzokFentryid;

    /**
     * 对应的出库单号
     */
    private String fQzokCkdh;
}