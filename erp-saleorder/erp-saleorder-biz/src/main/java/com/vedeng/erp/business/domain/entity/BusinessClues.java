package com.vedeng.erp.business.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 线索表
 * @TableName T_BUSINESS_CLUES
 */
@Data
public class BusinessClues implements Serializable {
    /**
     * 
     */
    private Integer businessCluesId;

    /**
     * 线索编号
     */
    private String businessCluesNo;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 线索标签
     */
    private String cluesLabel;

    /**
     * 中标次数
     */
    private Integer zhongbiaoCount;

    /**
     * 跟踪建议
     */
    private String tracSuggestion;

    /**
     * 话术
     */
    private String remark;

    /**
     * 线索转成商机的商机ID
     */
    private Integer businessChanceId;

    /**
     * 置顶
     */
    private Integer top;

    /**
     * 线索创建时间
     */
    private Long addTime;

    /**
     * 群组ID
     */
    private Integer groupId;

    /**
     * 群组名称
     */
    private String groupName;

    /**
     * 置顶时间
     */
    private Long topTime;

    /**
     * 线索是否有价值，1有价值，0没价值，null未评估
     */
    private Integer worth;

    /**
     * 备注
     */
    private String comment;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新者
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;


}