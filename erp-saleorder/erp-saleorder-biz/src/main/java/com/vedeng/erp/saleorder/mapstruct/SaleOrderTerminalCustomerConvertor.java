package com.vedeng.erp.saleorder.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalCustomerEntity;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalCustomerDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.mapstruct
 * @Date 2023/9/4 15:10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SaleOrderTerminalCustomerConvertor extends BaseMapStruct<SaleOrderTerminalCustomerEntity, SaleOrderTerminalCustomerDto> {
}
