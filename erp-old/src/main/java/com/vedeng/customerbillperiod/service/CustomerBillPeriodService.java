package com.vedeng.customerbillperiod.service;

import com.vedeng.customerbillperiod.dto.*;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriod;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail;

import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/7/22 15 03
 * @Description: 账期服务
 */
public interface CustomerBillPeriodService {

    /**
     * 查询订单占用的账期金额
     * @param companyId 公司id
     * @param orderId 订单id
     * @param customerId 客户id
     * @return 订单账期占用情况
     */
    CustomerBillPeriodUseDetailOfOrder getUsedCustomerBillPeriodAmountByOrderId(Integer companyId, Long customerId, Long orderId);

    /**
     * 获取客户的各个类型账期的简要信息
     * @param companyId 公司id
     * @param customerId 客户id
     * @return 各类型账期的简要信息
     */
    List<CustomerBillPeriodSummaryInfoDto> getCustomerBillPeriodSummaryInfo(Integer companyId, Long customerId);


    /**
     * 获取指定类型的有效客户账期集合
     * @param companyId 公司id
     * @param customerId 客户id
     * @param billPeriodType 客户账期类型
     * @return 账期详情
     */
    List<CustomerBillPeriodDetailsDto> getCustomerBillPeriodDetailsByType(Integer companyId, Long customerId, Integer billPeriodType);

    /**
     * 根据客户账期申请人查询各个账期类型的客户信用记录
     * @param queryDto 查询类
     * @return 各个账期类型的客户信用记录
     */
    List<CustomerBillPeriodCreditHistoryDto> getCustomerBillPeriodCreditHistoryByCreators(CustomerBillPeriodCreditHistoryQueryDto queryDto);

    /**
     * <b>Description:</b><br>
     * 查询账期归还明细
     *
     * @param billPeriodId 账期id, relatedId 关联订单,parentUseDetailId 售后退货、还款产生的明细对应的支付订单使用ID
     * @return java.util.List<com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/8/4 17:38
     */
    List<CustomerBillPeriodUseDetail> revertCustomerBillPeriodInfo(Long billPeriodId,Long relatedId,Long parentUseDetailId);

    /**
     * <b>Description:</b><br>
     * 查询账期使用记录
     *
     * @param parentUseDetailId 售后退货、还款产生的明细对应的支付订单使用ID
     * @return java.util.List<com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/8/5 15:32
     */
    List<CustomerBillPeriodDetailManageDto> getBillPeriodDetailManageInfo(Long parentUseDetailId);
    /**
     * 查询客户账期可用余额
     * 1、账期扣减顺序：订单、正式、临时；@see http://jira.ivedeng.com/browse/VDERP-8008
     * 2、如果查询参数带有订单id，则计算可用余额会加上订单账期；否则只计算正式、临时账期的当前可用余额
     * @param availableAmountQueryDto 查询类
     * @return 可用余额
     */
    CustomerBillPeriodAvailableAmountDto getCustomerBillPeriodAvailableAmount(CustomerBillPeriodAvailableAmountQueryDto availableAmountQueryDto);

    /**
     * 冻结客户账期金额，账期扣减顺序：订单、正式、临时；@see http://jira.ivedeng.com/browse/VDERP-8008
     * 如果查询参数带有订单id，则账期扣减会加上订单账期；否则只扣减正式、临时账期的当前可用余额
     * @param freezingDto 冻结客户账期信息
     * @throws CustomerBillPeriodException 冻结操作异常类
     */
    void freezingCustomerBillPeriodAmount(CustomerBillPeriodFreezingDto freezingDto) throws CustomerBillPeriodException;

    /**
     * 占用已冻结的客户账期金额
     * @param occupancyDto 占用
     * @throws CustomerBillPeriodException 占用已冻结金额的操作异常
     */
    void occupancyCustomerBillPeriodFrozenAmount(CustomerBillPeriodOccupancyDto occupancyDto) throws CustomerBillPeriodException;


    /**
     * 查询是否被占用
     * @param occupancyDto 占用
     * @return
     * @throws CustomerBillPeriodException
     */
    List<CustomerBillPeriodUseDetail> getOccupancyCustomerBillPeriodUseDetail(CustomerBillPeriodOccupancyDto occupancyDto) throws CustomerBillPeriodException;

    /**
     * 解冻客户账期已冻结的金额
     * @param unfreezingDto 解冻账期金额
     * @throws CustomerBillPeriodException 解冻账期金额操作异常类
     */
    void unfreezingCustomerBillPeriodAmount(CustomerBillPeriodUnfreezingDto unfreezingDto) throws CustomerBillPeriodException;
    /**
     * <b>Description:</b><br>
     * 根据主键id查询账期记录
     *
     * @param billPeriodId
     * @return com.vedeng.customerbillperiod.model.CustomerBillPeriod
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/8/20 10:09
     */
    CustomerBillPeriod selectByprimaryKey(Long billPeriodId);
}