package com.vedeng.goods.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Title: ${file_name}
 * @Package ${package_name}
 * @Description: ${todo}
 * @date 2021/9/1316:56
 */
@Data
public class CoreSkuDeliverTime {

    /**
     * sku发货时长id
     */
    private Integer skuDeliverTimeId;

    /**
     * SKU
     */
    private String sku;

    /**
     * 区域id
     */
    private Integer areaId;

    /**
     * 区域等级(0：国家 ，1：省级，2：市级)
     */
    private Integer areaLevel;

    /**
     * 发货到货时长(工作日)
     */
    private Integer deliverTime;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;
}
