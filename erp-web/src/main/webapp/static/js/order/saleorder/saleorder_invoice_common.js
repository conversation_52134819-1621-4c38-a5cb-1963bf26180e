
/**
 * 查看下载销项发票信息
 *
 * @param invoiceHref 发票链接
 * @param invoiceCode 发票代码
 * @param invoiceNum 发票号
 * @param invoiceMethod 开票方式
 * @param isSendInvoice 寄送发票
 */
function viewAndDownloadInvoice(ossFileUrl,invoiceHref, invoiceCode,invoiceNum,invoiceId,invoiceMethod,isSendInvoice) {
    /**
     * 当订单的开票方式为“手动纸质开票”或“自动纸质开票”时，
     * 若寄送方式为“不寄送”，则点击时报错：该订单已选择不寄送发票，无法查看
     */
    if ((invoiceMethod == 1 || invoiceMethod == 2) && isSendInvoice == 0){
        layer.alert('该订单已选择不寄送发票，无法查看!');
        return;
    }
    downloadAndSaveInvoiceHref(ossFileUrl,invoiceHref, invoiceCode,invoiceNum,invoiceId);
}


/**
 * 下载并保存航信发票地址信息
 *
 * @param ossFileUrl 发票链接
 * @param invoiceCode 发票代码
 * @param invoiceNum 发票号
 */
function downloadAndSaveInvoiceHref(ossFileUrl, invoiceCode,invoiceNum,invoiceId) {
    /**
     * 发票存在则展示并可下载  否则请求航信信息
     */
    /*if (invoiceHref != null && invoiceHref != '' && invoiceHref != undefined){
        window.open(invoiceHref);
        return;
    }*/
    // 更新，使用贝登OSS地址
    if (ossFileUrl != null && ossFileUrl != '' && ossFileUrl != undefined){
        window.open(ossFileUrl);
        return;
    }
    $.ajax({
        url: '/finance/invoice/downLoadAndSaveInvoiceHref.do',
        data :{
            invoiceId: invoiceId,
            invoiceType: '0',
            invoiceCode: invoiceCode,
            invoiceNum: invoiceNum
        },
        type: 'post',
        dataType: "json",
        success: function (res) {
            if (res.code == -1){
                layer.alert(res.message,function () {
                    layer.closeAll();
                });
            } else {
                window.open(res.data);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}