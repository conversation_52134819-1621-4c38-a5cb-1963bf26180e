<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>同行单录入待办列表页</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="Gp2rOHgq8t4" datasource="erp-reportdatasource" fixednumber="0" fixednumberright="0" append_column_url="" append_row_url="" empty_show="/" firstcol="">
<div id="appendHead">
    <style>
        .COLOR_RISK {
            background-image: url(http://qa.goerp.ivedeng.com/static/images/peerAlarmBell.png);
            width: 35px;
            height: 22px;
            left: 38px;
            top: 9px;
            position: absolute;
        }

    </style>
</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">采购单号</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="BUYORDER_NO" placeholder="请输入" style="" alias="b" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">销售单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="SALEORDER_NOS" placeholder="请输入" style="" alias="b" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">创建人</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="CREATOR" placeholder="" style="" alias="b" jdbctype="" data="SELECT  	DISTINCT u.USER_ID K,lower(u.USERNAME) V 	FROM  	T_USER u  	LEFT JOIN  T_R_USER_POSIT up ON u.USER_ID = up.USER_ID 	LEFT JOIN T_POSITION p on up.POSITION_ID = p.POSITION_ID      LEFT JOIN T_ORGANIZATION o ON p.ORG_ID =  o.ORG_ID 	WHERE o.TYPE = 311 order by u.USERNAME" datatype="KVSQLCACHE" oper="" ></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">创建人部门</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="ORG_ID" placeholder="" style="" alias="b" jdbctype="" data="select  ORG_ID K,ORG_NAME V  from T_ORGANIZATION WHERE TYPE = 311" datatype="KVSQLCACHE" oper="" ></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">是否紧急</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " type="" name="URGED_MAINTAIN_BATCH_INFO" placeholder="请选择" style="" alias="b" jdbctype="VARCHAR" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper="LIKE" ></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">供应商</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="请输入" style="" alias="b" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">产品名称</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="GOODS_NAME" placeholder="请输入" style="" alias="bg" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">品牌</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="BRAND_NAME" placeholder="请输入" style="" alias="bg" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADDTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME( IF ( b.ADD_TIME = 0, NULL, b.ADD_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">IS_ADMIN</label>
            <div class="layui-input-inline">
                <hidden class=" layui-input list-search-item " type="nowhere" name="IS_ADMIN" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="IN" ></hidden>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"><button item_name="BUYORDER_ID" name="BUYORDER_ID" url="/order/newBuyorderPeerList/editPeerListView.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="维护数据" style area type="single">维护数据</button></th>
            <th item_name="URGED_MAINTAIN_BATCH_INFO" name="URGED_MAINTAIN_BATCH_INFO" body="td-link-color" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="word-break: break-all;position: relative;" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">紧急待办</th>
            <th item_name="BUYORDER_NO" name="BUYORDER_NO" body="td-link" order="0" url="/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="${BUYORDER_NO}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购单号</th>
            <th item_name="SALEORDER_NOS" name="SALEORDER_NOS" body="td-link" order="0" url="/orderstream/saleorder/detailByNo.do?saleorderNo=${SALEORDER_NOS}" opentype="PARENT" windowname="${SALEORDER_NOS}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">关联销售单</th>
            <th item_name="STATUS" name="STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购单状态</th>
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">供应商</th>
            <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">生效时间</th>
            <th item_name="CREATOR" name="CREATOR" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select USER_ID K,USERNAME V FROM T_USER" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建人</th>
            <th item_name="ORG_ID" name="ORG_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select
		ORG_ID K,ORG_NAME V
		from T_ORGANIZATION" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建人部门</th>
            <th item_name="ARRIVAL_STATUS" name="ARRIVAL_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">收货状态</th>
            <th item_name="DELIVERY_STATUS" name="DELIVERY_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">发货状态</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="" groupby="">StringBuilder sql=new StringBuilder("

SELECT DISTINCT
	b.BUYORDER_ID,
	b.URGED_MAINTAIN_BATCH_INFO,
case when b.URGED_MAINTAIN_BATCH_INFO=1  then 'COLOR_RISK' else '' end COLOR_RISK,
	b.BUYORDER_NO,
	b.SALEORDER_NOS,
	b.`STATUS`,
	b.TRADER_NAME,
        b.ADD_TIME,
       FROM_UNIXTIME( IF ( b.VALID_TIME = 0, NULL, b.VALID_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' ) AS VALID_TIME,
	b.CREATOR,
	b.ORG_ID,
	b.ARRIVAL_STATUS,
	b.DELIVERY_STATUS
FROM
	T_BUYORDER b
	JOIN T_BUYORDER_GOODS bg ON b.BUYORDER_ID = bg.BUYORDER_ID
	LEFT JOIN (
	SELECT
		ed.RELATED_ID,
		ed.NUM - sum( ifnull( pd.ARRIVAL_COUNT, 0 ) ) to_maintain_count
	FROM
		T_EXPRESS_DETAIL ed
		JOIN T_EXPRESS e ON ed.BUSINESS_TYPE = 515
		AND ed.EXPRESS_ID = e.EXPRESS_ID
		AND e.IS_ENABLE = 1
		LEFT JOIN T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL pd ON ed.EXPRESS_DETAIL_ID = pd.EXPRESS_DETAIL_ID
	GROUP BY
		ed.EXPRESS_DETAIL_ID
	) a ON bg.BUYORDER_GOODS_ID = a.RELATED_ID
	AND to_maintain_count &gt; 0  and b.ADD_TIME &gt; 1652697000000

WHERE
       b.DELIVERY_DIRECT = 1 AND
b.LOCKED_STATUS=0
	AND DELIVERY_STATUS IN ( 1, 2 )
  and to_maintain_count is not null
");

sql.append ( " and ( 1=2  " );



if ( !isNotBlank("IS_ADMIN","request") &amp;&amp; isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " or b.CREATOR in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}


if ( !isNotBlank("EZ_SESSION_MY_USER_KEY","session") &amp;&amp; !isNotBlank("IS_ADMIN","request")) {
sql.append ( " or b.CREATOR in (" );
sql.append ( $$("EZ_SESSION_USER_ID_KEY") );
sql.append (  ") " );
}

if ( isNotBlank("IS_ADMIN","request")) {
sql.append (  " or  1=1 " );
}

sql.append (  ") " );



list=search(sql);
return list;</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>
        $(
            function delHtmlxx(){
                $('a').each(function () {
                    var id = $(this).attr('tab-id');
                    if (id=='URGED_MAINTAIN_BATCH_INFO-2808'){
                        $(this).remove();
                    }
                    if (id=='SALEORDER_NOS-2810'){
                        if($(this).html().trim() == '/'){
                            $(this).removeClass();
                        }
                    }
                });
            }
        )
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>