$(function () {
    $('#table-smallest8-all').on('change',function () {
        if (this.checked) {
            $("#smallest8List :checkbox").prop("checked", true);
        } else {
            $("#smallest8List :checkbox").prop("checked", false);
        }
    })

    $('#smallest8List input').on('change', function () {
        var inputLength = $('#smallest8List input').length;
        var checkedLength = $('#smallest8List [name=check_value]:checked').length;
        if (checkedLength == inputLength) {
            $("#table-smallest8-all").prop("checked", true);
        } else {
            $("#table-smallest8-all").prop("checked", false);
        }
    })

    var callbackFuntion = $('#callbackFuntion').val();
    $("#small8-submit").click(function(){
        var valArr = new Array;
        $("#smallest8List input[name='check_value']").each(function(i){
            if($(this).prop("checked")){
                valArr[i]= [$(this).val()];
            }
        });
        if (valArr.length == 0) {
            layer.alert("请选择出库产品")
            return;
        }
        var checkDatas = [];



        var ischeck = true;
        $.each(valArr, function (i,value) {
            if(value !== null && value !== undefined && value !== ''){
                var split = value[0].split("%@;");
                var data = createCheckData(split[0],split[6]);
                checkDatas.push(data);
            }

        })

        $.ajax({
            url:page_url+'/wms/inventoryOut/checkData.do',
            data:JSON.stringify(checkDatas),
            type:"POST",
            dataType : "json",
            contentType : " application/json;charset=utf-8",
            async:false,
            success:function(data)
            {
                if(data.code == -1){
                    ischeck = false;
                    layer.alert("该产品无库存，请刷新搜索页重新选择")
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }

            }
        });

        if (!ischeck) {
            return;
        }
        $.each(valArr, function (i,value) {
            if(value !== null && value !== undefined && value !== ''){
                var split = value[0].split("%@;");
                debugger
                selectGoods(callbackFuntion, split[0], split[1], split[2], split[3], split[4], split[5],split[6]);
            }

        });
    });

    $("#close_window").click(function () {
        window.parent.layer.closeAll()
    });
})

function createCheckData(sku,logicalName){

    var obj = new Object();

    obj.skuNo = sku;

    obj.logicalName = logicalName;
    return obj;
}

function selectGoods(callbackFuntion,sku,goodsName,brandName,model,unitName,stockNum,logicalName){
    //如果有回调函数就调用
    if(callbackFuntion != null && callbackFuntion != ''){
        if (typeof(stockNum) == "undefined") {
            stockNum = 0
        }
        debugger
        eval("window.parent."+callbackFuntion+"('"+sku+"','"+goodsName+"','"+brandName+"','"+model+"','"+unitName+"','"+logicalName+"','"+stockNum+"')");
    }
    return;
}
function search() {
    checkLogin();
    clearErroeMes();//清除錯誤提示信息
    if($("#searchContent").val()==undefined || $("#searchContent").val()==""){
        warnTips("errorMes","查询条件不能为空");//文本框ID和提示用语
        $("#searchContent").addClass("errorbor");
        return false;
    }
    $("#search").submit();
}

function searchAll() {
    checkLogin();
    clearErroeMes();//清除錯誤提示信息
    if($("#searchContent").val()==undefined || $("#searchContent").val()==""){
        warnTips("errorMes2","查询条件不能为空");//文本框ID和提示用语
        $("#searchContent").addClass("errorbor");
        return false;
    }
    $("#searchType").val(1);
    $("#search").submit();
}