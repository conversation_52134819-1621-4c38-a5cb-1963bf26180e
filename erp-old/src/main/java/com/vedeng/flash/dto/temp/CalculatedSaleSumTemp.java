package com.vedeng.flash.dto.temp;

import lombok.*;

import java.math.BigDecimal;

/**
 * @program: erp.vedeng.com
 * @description: 存储销售额临时数据
 * @author: Pusan
 * @create: 2021-05-19 10:15
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CalculatedSaleSumTemp {

    /**
     * SkuId
     */
    private Integer skuId;
    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 不同时间段销售额
     */
    private BigDecimal saleSum;
}
