package com.vedeng.activiti;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class ProcessInstanceContext {

    public static final String ATTR_NAME_PROC_DEF_KEY_NAME = "processDefinitionKey";
    public static final String ATTR_NAME_BUSINESS_KEY = "businessKey";

    public static final String ATTR_NAME_RELATED_TABLE_KEY = "relateTableKey";
    public static final String ATTR_NAME_RELATED_TABLE_NAME = "relateTable";
    public static final String ATTR_NAME_ASSIGNEE_NAME = "currentAssinee";
    public static final String ATTR_NAME_ASSIGNEE_ORG_ID = "orgId";
    public static final String IS_YJ_PERIOD = "isYjPeriod";
    public static final String ATTR_NAME_RELATED_COMMENT = "comment";

    public final static String ATTR_NAME_TRADER_ID = "traderId";
    public final static String ATTR_NAME_CUSTOMER_ID = "customerId";
    public final static String ATTR_NAME_CUSTOMER_NAME = "customerName";

    public final static String ATTR_NAME_ACCOUNT_PERIOD_APPLY_AMOUNT = "accountPeriodApplyAmount";
    public final static String ATTR_NAME_ACCOUNT_PERIOD_APPLY_DAYS = "accountPeriodDaysApply";
    public final static String ATTR_NAME_ACCOUNT_PERIOD_APPLY_TYPE = "accountPeriodType";
    public final static String TRADER_BELONG = "traderBelong";

    private String processDefinitionKey;
    private String businessKey;
    private String assignee;
    private Integer relatedId;
    private String relateTable;
    private String tenantId;
    private String comment;
    private final Map<String, Object> attributes = new HashMap<>();

    public String getRelateTable() {
        return relateTable;
    }

    public void setRelateTable(String relateTable) {
        this.relateTable = relateTable;
        setSafely(ATTR_NAME_RELATED_TABLE_NAME, relateTable);
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
        setSafely(ATTR_NAME_RELATED_COMMENT, comment);
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
        setSafely(ATTR_NAME_PROC_DEF_KEY_NAME, processDefinitionKey);
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
        setSafely(ATTR_NAME_BUSINESS_KEY, businessKey);
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
        setSafely(ATTR_NAME_ASSIGNEE_NAME, assignee);
    }

    public Integer getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
        setSafely(ATTR_NAME_RELATED_TABLE_KEY, relatedId);
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public <V> V getByType(String key, Class<V> clazz) {
        return getSafely(key, clazz);
    }

    public Object get(String key) {
        return attributes.get(key);
    }


    public void set(String key, Object value) {
        setSafely(key, value);
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    private void setSafely(String key, Object value) {
        if (value != null) {
            attributes.put(key, value);
        }
    }

    private <V> V getSafely(String key, Class<V> clazz) {
        Object val = attributes.get(key);
        if (clazz.isInstance(val)) {
            return clazz.cast(val);
        }
        return null;
    }

    @Override
    public String toString() {
        return "ProcessInstanceContext{" +
                "processDefinitionKey='" + processDefinitionKey + '\'' +
                ", businessKey='" + businessKey + '\'' +
                ", assignee='" + assignee + '\'' +
                ", relatedId=" + relatedId +
                '}';
    }
}
