package com.newtask;

import com.alibaba.fastjson.TypeReference;
import com.newtask.service.TraderGroupService;
import com.newtask.service.TraderLabelService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.crm.api.dto.customergroup.TraderGroupEXTDto;
import com.vedeng.crm.api.dto.customergroup.TraderLabelEXTDto;
import com.vedeng.system.service.OrganizationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@JobHandler(value = "traderGroupAndLabelHandler")
@Component
public class TraderGroupAndLabelHandler extends AbstractJobHandler {

    private Logger logger= LoggerFactory.getLogger(TraderGroupAndLabelHandler.class);

    @Value("${crm_url}")
    protected String crmUrl;

    @Value("${track_url}")
    protected String trackUrl;

    @Value("${b2b_department_name}")
    protected String departmentName;
    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private TraderGroupService traderGroupService;

    @Autowired
    private TraderLabelService traderLabelService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        int count=0;
        while (!isTrackRunSuccess()){
            XxlJobLogger.log("埋点系统数据未跑完，正在等待");
            logger.info("埋点系统数据未跑完，正在等待");
            count++;
            if(count>36){
                XxlJobLogger.log("等待埋点数据时间超时，结束此次任务");
                logger.error("等待埋点数据时间超时，结束此次任务");
                return ReturnT.FAIL;
            }
            Thread.sleep(5*60*1000);
        }
        XxlJobLogger.log("开始处理客户分群、标签的计算");
        logger.info("开始处理客户分群、标签的计算");
        List<TraderLabelEXTDto> labels = getLabels();
        List<TraderGroupEXTDto> groups = getGroups();
        List<Integer> organizations = organizationService.getChildOrgByName(departmentName);
        if (CollectionUtils.isNotEmpty(labels)) {
            try {
                traderLabelService.handleTraderLabel(labels, organizations);
            } catch (Exception ex) {
                //logger.error("处理标签失败",ex);
                XxlJobLogger.log("处理标签失败", ex);
            }
        }
        if (CollectionUtils.isNotEmpty(groups)) {
            try {
                traderGroupService.handleTraderGroup(groups, organizations);
            } catch (Exception ex) {
                //logger.error("处理群组失败",ex);
                XxlJobLogger.log("处理群组失败", ex);
            }
        }
        XxlJobLogger.log("结束处理客户分群、标签的计算");
        logger.info("结束处理客户分群、标签的计算");
        return ReturnT.SUCCESS;
    }

    private List<TraderLabelEXTDto> getLabels() {
        TypeReference<RestfulResult<List<TraderLabelEXTDto>>> typeReference = new TypeReference<RestfulResult<List<TraderLabelEXTDto>>>() {
        };
        String url = crmUrl + "api/customergroup/queryCustomerLabelList";
        RestfulResult<List<TraderLabelEXTDto>> result = HttpRestClientUtil.restPost(url, typeReference, null, null);
        if (result != null && result.isSuccess()) {
            return result.getData();
        }
        return null;
    }

    private List<TraderGroupEXTDto> getGroups() {
        TypeReference<RestfulResult<List<TraderGroupEXTDto>>> typeReference = new TypeReference<RestfulResult<List<TraderGroupEXTDto>>>() {
        };
        String url = crmUrl + "api/customergroup/index";
        RestfulResult<List<TraderGroupEXTDto>> result = HttpRestClientUtil.restPost(url, typeReference, null, null);
        if (result != null && result.isSuccess()) {
            return result.getData();
        }
        return null;
    }

    private boolean isTrackRunSuccess(){
        TypeReference<RestfulResult> typeReference = new TypeReference<RestfulResult>() {
        };
        String url = trackUrl + "track/getRunJobSuccess";
        RestfulResult<List<TraderGroupEXTDto>> result = HttpRestClientUtil.restPost(url, typeReference, null, null);
        if (result != null) {
            return result.isSuccess();
        }
        return false;
    }
}
