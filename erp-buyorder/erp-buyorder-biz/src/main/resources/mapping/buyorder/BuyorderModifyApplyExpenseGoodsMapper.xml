<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.BuyorderModifyApplyExpenseGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity">
    <!--@mbg.generated-->
    <!--@Table T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS-->
    <id column="BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID" jdbcType="INTEGER" property="buyorderModifyApplyExpenseGoodsId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="BUYORDER_MODIFY_APPLY_ID" jdbcType="INTEGER" property="buyorderModifyApplyId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments" />
    <result column="OLD_INSIDE_COMMENTS" jdbcType="VARCHAR" property="oldInsideComments" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, BUYORDER_MODIFY_APPLY_ID, BUYORDER_EXPENSE_ITEM_ID, INSIDE_COMMENTS, 
    OLD_INSIDE_COMMENTS, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS
    where BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS
    where BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID" keyProperty="buyorderModifyApplyExpenseGoodsId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      BUYORDER_MODIFY_APPLY_ID, BUYORDER_EXPENSE_ITEM_ID, 
      INSIDE_COMMENTS, OLD_INSIDE_COMMENTS, IS_DELETE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{buyorderModifyApplyId,jdbcType=INTEGER}, #{buyorderExpenseItemId,jdbcType=INTEGER}, 
      #{insideComments,jdbcType=VARCHAR}, #{oldInsideComments,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID" keyProperty="buyorderModifyApplyExpenseGoodsId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="buyorderModifyApplyId != null">
        BUYORDER_MODIFY_APPLY_ID,
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID,
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS,
      </if>
      <if test="oldInsideComments != null">
        OLD_INSIDE_COMMENTS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="buyorderModifyApplyId != null">
        #{buyorderModifyApplyId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseItemId != null">
        #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="insideComments != null">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="oldInsideComments != null">
        #{oldInsideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity">
    <!--@mbg.generated-->
    update T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="buyorderModifyApplyId != null">
        BUYORDER_MODIFY_APPLY_ID = #{buyorderModifyApplyId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="oldInsideComments != null">
        OLD_INSIDE_COMMENTS = #{oldInsideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity">
    <!--@mbg.generated-->
    update T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      BUYORDER_MODIFY_APPLY_ID = #{buyorderModifyApplyId,jdbcType=INTEGER},
      BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      OLD_INSIDE_COMMENTS = #{oldInsideComments,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUYORDER_MODIFY_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyorderModifyApplyId != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.buyorderModifyApplyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUYORDER_EXPENSE_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyorderExpenseItemId != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.buyorderExpenseItemId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSIDE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.insideComments != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.insideComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OLD_INSIDE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldInsideComments != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.oldInsideComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID = #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where BUYORDER_MODIFY_APPLY_EXPENSE_GOODS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.buyorderModifyApplyExpenseGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>


  <select id="getByBuyorderModifyApplyId" resultType="com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity">
    SELECT
    <include refid="Base_Column_List" />
    FROM
        T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS tbmaeg
    WHERE
        tbmaeg.BUYORDER_MODIFY_APPLY_ID = #{buyorderModifyApplyId,jdbcType=INTEGER}
        AND tbmaeg.IS_DELETE = 0
  </select>
</mapper>