<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:apollo="http://www.ctrip.com/schema/apollo" xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd
		http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit.xsd">

    <!--rabbit连接-->
    <rabbit:connection-factory
            id="mlConnectionFactory"
            addresses="${ml.rabbitmq.host}"
            username="${ml.rabbitmq.username}"
            password="${ml.rabbitmq.password}"
            virtual-host="${ml.rabbitmq.virtualHost}"
            channel-cache-size="100"
            publisher-returns="true"
            publisher-confirms="true" />

    <!-- 定义mq管理 -->
    <rabbit:admin connection-factory="mlConnectionFactory" />
    <rabbit:template id="mlRabbitTemplate"
                     connection-factory="mlConnectionFactory" message-converter="messageConverter" />


    <!--定义queue  说明：durable:是否持久化 exclusive: 仅创建者可以使用的私有队列，断开后自动删除 auto_delete: 当所有消费客户端连接断开后，是否自动删除队列-->
    <rabbit:queue name="mlPosterCenterQueue" durable="true" auto-delete="false" exclusive="false" />
    <!--定义direct-exchange（一对一） -->
    <rabbit:direct-exchange name="mlPosterCenterExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="mlPosterCenterQueue" key="approvalNotice" />
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <rabbit:listener-container connection-factory="mlConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="posterCheckMessageConsumer" queues="mlPosterCenterQueue"></rabbit:listener>
    </rabbit:listener-container>



</beans>
