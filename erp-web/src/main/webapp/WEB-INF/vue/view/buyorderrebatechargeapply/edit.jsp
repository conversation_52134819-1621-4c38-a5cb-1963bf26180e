<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <el-form :model="buyOrderRebateChargeApply" :rules="rules" ref="buyOrderRebateChargeApply" label-width="120px" label-suffix=":">
        <el-row style="margin-bottom: 10px;color: #606266;">基本信息</el-row>
        <el-form-item label="供应商名称" prop="traderSupplierName" :rules="rules.traderSupplierName">
            <el-input style="width: 350px" v-model="buyOrderRebateChargeApply.traderSupplierName"  @click.native="searchSupplier()" readonly="true"></el-input>
        </el-form-item>

        <el-form-item label="品牌" prop="chooseBrandIds" :rules="rules.chooseBrandIds">
            <el-select
                    v-model="buyOrderRebateChargeApply.chooseBrandIds"
                    multiple
                    filterable
                    remote
                    collapse-tags
                    reserve-keyword
                    placeholder="请输入关键词"
                    :remote-method="remoteMethod"
                    @change="handleSelectChange"
                    :loading="loading">
                <el-option
                        v-for="item in brandOptions"
                        :key="item.brandId"
                        :label="item.brandName"
                        :value="item.brandId">
                </el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="产线负责人" prop="headUserId" :rules="rules.headUserId">
            <el-select v-model="buyOrderRebateChargeApply.headUserId" placeholder="请选择" ref="headUserIdRef" filterable >
                <el-option
                        v-for="item in headUserList"
                        :key="item.userId"
                        :label="item.username"
                        :value="item.userId">
                </el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="返利周期" prop="cycleTime">
            <el-date-picker
                    v-model="buyOrderRebateChargeApply.cycleTime"
                    type="daterange"
                    ref="timeBox"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
        </el-form-item>

        <el-form-item label="返利使用期限" prop="usePeriodTime">
            <el-date-picker
                    v-model="buyOrderRebateChargeApply.usePeriodTime"
                    type="daterange"
                    ref="timeBox"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
        </el-form-item>

        <el-form-item label="返利政策条款" prop="policyTerms" :rules="rules.policyTerms">
            <el-input style="width: 650px" type="textarea" v-model="buyOrderRebateChargeApply.policyTerms" maxlength="1000" show-word-limit></el-input>
        </el-form-item>


        <el-form-item label="备注" prop="remark">
            <el-input style="width: 650px" type="textarea" v-model="buyOrderRebateChargeApply.remark" maxlength="1000" show-word-limit></el-input>
        </el-form-item>

        <el-form-item label="合计返利" prop="totalAmount" :rules="rules.totalAmount">
            <el-input style="width: 150px" v-model="buyOrderRebateChargeApply.totalAmount" @input="handleTotalAmount(buyOrderRebateChargeApply.totalAmount)"></el-input>
        </el-form-item>

        <el-row style="margin-bottom: 10px; color: #606266;">其它信息</el-row>
        <el-form-item label="确认函" prop="billUrl">
            <el-upload
                    action="/fileUpload/ajaxFileUpload.do"
                    :on-success="handleBillFileSuccess"
                    :on-remove="handleBillFileRemove"
                    :before-upload="beforeBillFileUpload"
                    accept=".doc, .docx, .pdf, .jpg, .png"
                    :on-preview="handlePreview"
                    multiple
                    name="lwfile"
                    :limit="2"
                    :file-list="billFileList">
                <el-button size="small">
                    <i class="el-icon-upload2" style="margin-right: 5px"></i>点击上传
                </el-button>
                <div slot="tip" class="el-upload__tip">支持扩展名：.doc .docx .pdf .jpg .png</div>
            </el-upload>
        </el-form-item>

        <el-form-item label="明细" prop="detailUrl">
            <el-row>
                <el-col :span="8">
                    <el-upload
                            action="/buyOrder/rebateChargeApply/api/checkAndUpload.do"
                            :on-success="handleDetailFileSuccess"
                            :on-remove="handleDetailFileRemove"
                            :before-upload="beforeDetailFileUpload"
                            accept=".xlsx, .xls"
                            :on-preview="handlePreview"
                            name="lwfile"
                            multiple
                            :limit="2"
                            :file-list="detailFileList">
                        <el-button size="small">
                            <i class="el-icon-upload2" style="margin-right: 5px"></i>点击上传
                        </el-button>
                        <div slot="tip" class="el-upload__tip">支持扩展名：.xls .xlsx</div>
                    </el-upload>
                </el-col>
                <el-col :span="16">
                    <el-button size="small" plain type="primary" @click="downloadTemplate()">
                        下载模板
                    </el-button>
                </el-col>
            </el-row>
        </el-form-item>

        <el-form-item style="margin-left: 35%">
            <el-button type="primary" @click="submitForm('buyOrderRebateChargeApply')">提交</el-button>
        </el-form-item>
    </el-form>
</div>
<script src="${pageContext.request.contextPath}/static/api/buyorder/buyOrderRebateChargeApply.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">

    const ossHttp = '${ossHttp}';
    var vm = new Vue({
        el: '#app',
        data() {
            let checkTotalAmount = (rule, value, callback) => {
                if (value === undefined || value === '' || value === null) {
                    callback(new Error("合计返利不可为空"));
                }
                if (Number(value) === 0) {
                    callback(new Error("合计返利不可为0"));
                }
                callback();
            };

            return {
                buyOrderRebateChargeApply: {
                    traderSupplierName: '',
                    traderSupplierId: 0,
                    traderId: 0,
                    cycleTime: [],
                    usePeriodTime: [],
                    remark: '',
                    totalAmount: 0.00,
                    cycleBeginTime: null,
                    cycleEndTime: null,
                    usePeriodBeginTime: null,
                    usePeriodEndTime: null,
                    billFileList: [],
                    detailFileList: [],
                    headUserId: null,
                    headUserName: '',
                    chooseBrandIds: [],
                    brandIds: ''
                },
                rules: {
                    totalAmount: [
                        {required: true, message: '未填写合计返利', trigger: ['blur', 'change']},
                        {validator: checkTotalAmount, trigger: 'blur'}
                    ],
                    traderSupplierName : [
                        {required: true, message: '未关联供应商', trigger: 'blur'}
                    ],
                    cycleTime: [
                        {required: true, message: '请选择返利周期', trigger: 'blur'}
                    ],
                    usePeriodTime: [
                        {required: true, message: '请选择返利使用期限', trigger: 'blur'}
                    ],
                    chooseBrandIds: [
                        {required: true, message: '请选择品牌', trigger: 'change'}
                    ],
                    headUserId: [
                        {required: true, message: '请选择产线负责人', trigger: 'change'}
                    ],
                    policyTerms: [
                        {required: true, message: '请填写返利政策条款', trigger: 'blur'}
                    ]
                },
                // 结算单附件
                billFileList: [],
                // 明细附件
                detailFileList: [],
                // 因为文件上传后，会被赋一个自动生成的文件名，所以我们需要将文件的原始文件名记住
                tempBillFileName: '',
                tempDetailFileName: '',
                globalFileNameList: [],
                headUserList: [],
                brandOptions: [],
                loading: false
            };
        },

        mounted() {
            loadingApp();
        },

        created() {
            getAllHeadUser({"positionType" : 311}).then(res => {
                this.headUserList = res.data.data;
            })
        },

        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if(valid) {
                        const loading = this.$loading({
                            lock: true,
                            text: '保存中',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });

                        this.buyOrderRebateChargeApply.cycleBeginTime =  this.buyOrderRebateChargeApply.cycleTime == null ? null : this.buyOrderRebateChargeApply.cycleTime[0];
                        this.buyOrderRebateChargeApply.cycleEndTime =  this.buyOrderRebateChargeApply.cycleTime == null ? null : this.buyOrderRebateChargeApply.cycleTime[1];
                        this.buyOrderRebateChargeApply.usePeriodBeginTime =  this.buyOrderRebateChargeApply.usePeriodTime == null ? null : this.buyOrderRebateChargeApply.usePeriodTime[0];
                        this.buyOrderRebateChargeApply.usePeriodEndTime =  this.buyOrderRebateChargeApply.usePeriodTime == null ? null : this.buyOrderRebateChargeApply.usePeriodTime[1];
                        this.buyOrderRebateChargeApply.headUserName = this.$refs.headUserIdRef.selected.label;
                        this.buyOrderRebateChargeApply.brandIds = this.buyOrderRebateChargeApply.chooseBrandIds.join(",");
                        add(this.buyOrderRebateChargeApply).then(res => {
                            loading.close();
                            openTab("返利申请详情", '/buyOrder/rebateChargeApply/detail.do?buyOrderRebateChargeId=' + res.data.data);
                            this.closeThis();
                        })
                    } else {
                        return false;
                    }
                });
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },

            // 品牌的远程搜索方法
            remoteMethod(query) {
                if (query !== '') {
                    this.loading = true;
                    getBrand({"brandName" : query}).then(res => {
                        this.brandOptions = res.data.data;
                        this.loading = false;
                    })

                } else {
                    this.brandOptions = [];
                }
            },

            handleSelectChange() {
                // 如果已选选项数量超过 10，只保留前 10 个选项
                if (this.buyOrderRebateChargeApply.chooseBrandIds.length > 10) {
                    this.buyOrderRebateChargeApply.chooseBrandIds = this.buyOrderRebateChargeApply.chooseBrandIds.slice(0, 10);
                }
            },

            searchSupplier() {
                layer.open({
                    type: 2,
                    shadeClose: false,
                    area: ['80%', '80%'],
                    title: '选择供应商',
                    content: '/order/buyorder/getSupplierByName.do?supplierName=&callbackFuntion=buyOrderRebateChargeApplyPage&pop=pop'
                });
            },

            // 下载模板
            downloadTemplate() {
                let url = 'https://file.vedeng.com/file/download?resourceId=2c1930dcfecd49eea941ae0ab5933d4a';
                let name = parseTime(new Date()) + '-' + '结算单明细模板' + '.' + 'xlsx';
                fetch(url).then(res => res.blob().then(blob => {
                    let a = document.createElement('a');
                    let url = window.URL.createObjectURL(blob);
                    let filename = name || '';
                    a.href = url;
                    a.download = filename;
                    a.click();
                    window.URL.revokeObjectURL(url);
                }));
            },

            // 结算单附件处理方法
            handleBillFileSuccess(file) {
                if (file.code == -1) {
                    this.$message({
                        message: file.message,
                        type: 'error'
                    });
                    this.buyOrderRebateChargeApply.billFileList.splice(-1 ,1);
                    this.billFileList.splice(-1 ,1);
                    return;
                }
                let tempFile = {
                    attachmentType: 4465,
                    attachmentFunction: 4465,
                    // 新增时取不到售后单id，因此这边统一设置为0，到接口内取id
                    relatedId: 0,
                    name: this.tempBillFileName,
                    domain: file.httpUrl,
                    uri: file.filePath,
                    suffix: file.prefix,
                    ossResourceId: file.ossResourceId
                };
                this.buyOrderRebateChargeApply.billFileList.push(tempFile);
                this.globalFileNameList.push(tempFile.name);
                this.tempBillFileName = '';
            },

            handleBillFileRemove(file) {
                if (file && file.status==="success") {
                    let nameList = [];
                    this.buyOrderRebateChargeApply.billFileList.forEach(item => {
                        nameList.push(item.name)
                    });
                    let index = nameList.indexOf(file.name);
                    this.buyOrderRebateChargeApply.billFileList.splice(index, 1);
                    for (let i = this.globalFileNameList.length - 1; i >= 0; i--) {
                        if (this.globalFileNameList[i] === file.name) {
                            this.globalFileNameList.splice(i, 1);
                        }
                    }
                }
            },

            beforeBillFileUpload(file) {
                if (this.globalFileNameList.includes(file.name)) {
                    this.$message.error("不能上传重复附件！");
                    return false
                }

                this.tempBillFileName = file.name;
                let img = file.name.substring(file.name.lastIndexOf('.') + 1);
                img = img.toLocaleLowerCase();
                const suffix = img === 'jpg';
                const suffix2 = img === 'png';
                const suffix3 = img === 'doc';
                const suffix4 = img === 'docx';
                const suffix5 = img === 'pdf';
                if (!suffix && !suffix3 && !suffix4 && !suffix5 && !suffix2) {
                    this.$message.error("只能上传jpg、doc、docx、pdf、png等格式！");
                    return false
                }
                const isLt2M = file.size / 1024 / 1024 <= 3;
                if (!isLt2M) {
                    this.$message.error('上传附件大小不能超过 3MB!');
                    return false
                }
                return true;
            },

            // 明细附件处理方法
            handleDetailFileSuccess(file) {
                if (file.code == -1) {
                    this.$message({
                        message: file.message,
                        type: 'error'
                    });
                    this.buyOrderRebateChargeApply.detailFileList.splice(-1 ,1);
                    this.detailFileList.splice(-1 ,1);
                    return;
                }

                // excel内容校验没通过
                if (file.code == -2) {
                    this.$alert(file.message, '上传失败提醒',{
                        confirmButtonText: '确定'
                        // dangerouslyUseHTMLString: true
                    });
                    this.buyOrderRebateChargeApply.detailFileList.splice(-1 ,1);
                    this.detailFileList.splice(-1 ,1);
                    return;
                }

                let tempFile = {
                    attachmentType: 4466,
                    attachmentFunction: 4466,
                    // 新增时取不到售后单id，因此这边统一设置为0，到接口内取id
                    relatedId: 0,
                    name: this.tempDetailFileName,
                    domain: file.httpUrl,
                    uri: file.filePath,
                    suffix: file.prefix,
                    ossResourceId: file.ossResourceId
                };
                this.buyOrderRebateChargeApply.detailFileList.push(tempFile);
                this.globalFileNameList.push(tempFile.name);
                this.tempDetailFileName = '';
            },

            handleDetailFileRemove(file) {
                if (file && file.status==="success") {
                    let nameList = [];
                    this.buyOrderRebateChargeApply.detailFileList.forEach(item => {
                        nameList.push(item.name)
                    });
                    let index = nameList.indexOf(file.name);
                    this.buyOrderRebateChargeApply.detailFileList.splice(index, 1);
                    for (let i = this.globalFileNameList.length - 1; i >= 0; i--) {
                        if (this.globalFileNameList[i] === file.name) {
                            this.globalFileNameList.splice(i, 1);
                        }
                    }
                }
            },

            beforeDetailFileUpload(file) {
                if (this.globalFileNameList.includes(file.name)) {
                    this.$message.error("不能上传重复附件！");
                    return false
                }

                this.tempDetailFileName = file.name;
                let img = file.name.substring(file.name.lastIndexOf('.') + 1);
                img = img.toLocaleLowerCase();
                const suffix = img === 'xlsx';
                const suffix2 = img === 'xls';
                if (!suffix && !suffix2) {
                    this.$message.error("只能上传xlsx、xls格式！");
                    return false
                }
                return true;
            },

            handlePreview(file) {
                var prefix = file.response.prefix.toLowerCase();
                if (prefix == 'jpg' || prefix == 'pdf' || prefix == 'png') {
                    // 预览
                    window.open(ossHttp + file.response.ossUrl);
                } else {
                    // 下载
                    window.open(ossHttp + file.response.ossUrl.replace("display", "download"));
                }
            },

            handleTotalAmount(totalAmount) {
                this.buyOrderRebateChargeApply.totalAmount = this.limitTotalAmount(totalAmount);
            },

            limitTotalAmount(totalAmount) {
                let str = totalAmount;
                let len1 = str.substr(0, 1);
                let len2 = str.substr(1, 1);
                //如果第一位是0，第二位不是点，就用数字把点替换掉
                if (str.length > 1 && len1 == 0 && len2 != '.') {
                    str = str.substr(1, 1);
                }
                //第一位不能是.
                if (len1 == '.') {
                    str = '';
                }
                //限制只能输入一个小数点
                if (str.indexOf('.') != -1) {
                    let str_ = str.substr(str.indexOf('.') + 1);
                    if (str_.indexOf('.') != -1) {
                        str = str.substr(0, str.indexOf('.') + str_.indexOf('.') + 1);
                    }
                }
                //正则替换
                str = str.replace(/[^\d\.]+/g, ''); // 保留数字和小数点
                str = str.replace(/(\.\d{2})\d*/, '$1'); // 小数点后只能输两位
                return str;
            },
        }
    });

    // 选择供应商后的回调函数
    function buyOrderRebateChargeApplyPage(data) {
        vm.buyOrderRebateChargeApply.traderSupplierName = data.traderSupplierName;
        vm.buyOrderRebateChargeApply.traderId = data.traderId;
        vm.buyOrderRebateChargeApply.traderSupplierId = data.traderSupplierId;
        layer.closeAll();
        vm.rules.traderSupplierName[0] =true;
    }
</script>

<style>
    .el-upload-list__item-status-label {
       margin-right: 35%;
    }

    .el-upload-list__item .el-icon-close {
        margin-right: 35%;
    }
    .el-upload-list__item .el-icon-close-tip {
        margin-right: 35%;
    }

</style>

</body>
</html>
