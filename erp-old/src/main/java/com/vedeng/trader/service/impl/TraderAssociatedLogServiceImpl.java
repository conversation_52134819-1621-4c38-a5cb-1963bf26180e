package com.vedeng.trader.service.impl;

import com.google.common.base.Preconditions;
import com.vedeng.aftersales.dao.TraderAssociatedLogMapper;
import com.vedeng.aftersales.model.TraderAssociatedLogDo;
import com.vedeng.aftersales.model.dto.TraderAssociatedLogDto;
import com.vedeng.aftersales.model.vo.TraderAssociatedLogVo;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.common.util.DateUtil;
import com.vedeng.trader.enums.TraderAssociatedLogEnum;
import com.vedeng.trader.service.TraderAssociatedLogService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class TraderAssociatedLogServiceImpl implements TraderAssociatedLogService {

    private final static Logger LOGGER = LoggerFactory.getLogger(TraderAssociatedLogServiceImpl.class);

    @Resource
    private TraderAssociatedLogMapper traderAssociatedLogMapper;
    @Resource
    private OrganizationMapper organizationMapper;


    @Transactional(readOnly = true, rollbackFor = Exception.class)
    @Override
    public List<TraderAssociatedLogVo> listByWebAccountId(Integer webAccountId) {
        if (webAccountId == null) {
            return Collections.emptyList();
        }

        List<TraderAssociatedLogVo> resultList = new ArrayList<>();
        List<TraderAssociatedLogDo> traderAssociatedLogDoList = traderAssociatedLogMapper.listByWebAccountId(webAccountId);
        traderAssociatedLogDoList.forEach(record -> {
            TraderAssociatedLogVo traderAssociatedLogVo = new TraderAssociatedLogVo();
            traderAssociatedLogVo.setOperationDescription(record.getOperationDescription());
            traderAssociatedLogVo.setOperatorName(record.getOperatorName());
            traderAssociatedLogVo.setOperatorName(record.getOperatorName());
            traderAssociatedLogVo.setReason(record.getReason());
            traderAssociatedLogVo.setRemark(record.getRemark());
            traderAssociatedLogVo.setOrganizationNameOfOperator(record.getOperatorOrganization());
            traderAssociatedLogVo.setOperatorName(record.getOperatorName());
            traderAssociatedLogVo.setOperationTime(DateUtil.DateToString(record.getOperateTime(), DateUtil.TIME_FORMAT));

            resultList.add(traderAssociatedLogVo);
        });

        return resultList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void recordTraderAssociatedLog(TraderAssociatedLogDto traderAssociatedLogDto) {
        Preconditions.checkArgument(traderAssociatedLogDto != null, "请求参数为空");

        TraderAssociatedLogEnum operationTypeToUse = TraderAssociatedLogEnum.getByType(traderAssociatedLogDto.getOperationType());
        if (operationTypeToUse == null) {
            throw new UnsupportedOperationException(String.format("目前不支持此类型[type=%s]操作", traderAssociatedLogDto.getOperationType()));
        }

        TraderAssociatedLogDo traderAssociatedLogDo = new TraderAssociatedLogDo();
        traderAssociatedLogDo.setRegisteredAccountPlatform(traderAssociatedLogDto.getWebAccountToBelongPlatformNo());
        traderAssociatedLogDo.setErpAccountId(traderAssociatedLogDto.getErpAccountId());
        traderAssociatedLogDo.setOperationType(operationTypeToUse.getType());
        traderAssociatedLogDo.setOperationDescription(operationTypeToUse.getDescription());
        traderAssociatedLogDo.setTraderId(traderAssociatedLogDto.getTraderId());
        if (StringUtils.isNotEmpty(traderAssociatedLogDto.getReason())) {
            traderAssociatedLogDo.setReason(traderAssociatedLogDto.getReason());
        }
        if (StringUtils.isNotEmpty(traderAssociatedLogDto.getRemark())) {
            if (traderAssociatedLogDto.getRemark().length() > REMARK_LIMITED_SIZE) {
                traderAssociatedLogDo.setRemark(traderAssociatedLogDto.getRemark().substring(0, REMARK_LIMITED_SIZE - 1));
            } else {
                traderAssociatedLogDo.setRemark(traderAssociatedLogDto.getRemark());
            }
        }

        if(StringUtils.isNotEmpty(traderAssociatedLogDto.getOperatorOrganization())){
            traderAssociatedLogDo.setOperatorOrganization(traderAssociatedLogDto.getOperatorOrganization());
        }else if (traderAssociatedLogDto.getOperatorId() != null) {
            Organization organizationQuery = organizationMapper.getOrgNameByUserId(traderAssociatedLogDto.getOperatorId());
            if (organizationQuery != null) {
                traderAssociatedLogDo.setOperatorOrganization(organizationQuery.getOrgName());
            }
        }

        traderAssociatedLogDo.setOperatorId(traderAssociatedLogDto.getOperatorId());
        traderAssociatedLogDo.setOperatorName(traderAssociatedLogDto.getOperatorName());
        traderAssociatedLogDo.setOperateTime(new Date());

        traderAssociatedLogMapper.insertSelective(traderAssociatedLogDo);

        LOGGER.info("【关联客户】记录客户/注册用户关联日志成功 - log：{}", traderAssociatedLogDo);
    }

}