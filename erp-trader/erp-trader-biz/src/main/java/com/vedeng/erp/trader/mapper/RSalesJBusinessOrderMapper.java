package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.RSalesJBusinessOrderEntity;
import  com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface RSalesJBusinessOrderMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(RSalesJBusinessOrderEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RSalesJBusinessOrderEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    RSalesJBusinessOrderEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RSalesJBusinessOrderEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RSalesJBusinessOrderEntity record);

    int updateBatchSelective(List<RSalesJBusinessOrderEntity> list);

    int batchInsert(List<RSalesJBusinessOrderEntity> list);

    List<RSalesJBusinessOrderDto> findByBusinessId(@Param("businessId")Integer businessId);

    int deleteById(@Param("id")Integer id);


    List<RSalesJBusinessOrderDto> findByBusinessIdAndTypeAndUserId(@Param("businessDto") RSalesJBusinessOrderDto businessDto);
    
    List<RSalesJBusinessOrderEntity> findByBusinessTypeAndSaleUserId(@Param("businessType")Integer businessType,@Param("saleUserId")Integer saleUserId);


    /**
     * 根据线索ID计算分享标签，完全不依赖于T_R_SALES_J_BUSINESS_ORDER表作为判断条件
     * 对于标签1和2，直接查询相关配置表，只有标签3(手动添加)需要查询分享表
     * @param businessId 线索ID
     * @return 分享列表与标签
     */
    List<RSalesJBusinessOrderDto> calculateShareTagsWithoutBusinessJOrder(@Param("businessId")Integer businessId);
    
    /**
     * 根据商机ID计算分享标签，完全不依赖于T_R_SALES_J_BUSINESS_ORDER表作为判断条件
     * 对于标签1和2，直接查询相关配置表，只有标签3(手动添加)需要查询分享表
     * @param businessId 商机ID
     * @return 分享列表与标签
     */
    List<RSalesJBusinessOrderDto> calculateShareTagsWithoutBusinessJOrderForChance(@Param("businessId")Integer businessId);
}