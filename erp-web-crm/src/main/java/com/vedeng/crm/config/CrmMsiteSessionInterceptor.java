package com.vedeng.crm.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ClientMustBeWeixinException;
import com.vedeng.common.core.exception.UserLimitException;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.crm.constant.CrmConstants;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.security.SecurityConstant;
import com.vedeng.security.service.model.AccountInfo;
import com.vedeng.security.service.model.Permission;
import com.vedeng.security.service.model.SSOUser;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: ShiroSessionInterceptor
 * @date 2024/7/20 15:33
 */
public class CrmMsiteSessionInterceptor implements HandlerInterceptor {

//    private UserApiService userApiService = ApplicationContextProvider.getContext().getBean(UserApiService.class);

//    @Value("${shiro_redis_session:36000000L}")
    private Long shrioSessionTimeOut = 2592000000L;//30天。


    @Value("${redis_dbtype}")
    protected String dbType;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        request.setAttribute("staticResourceVersion", CrmConstants.getStaticResourceVersion());
        request.setAttribute("checkCrmIsInner", CrmConstants.getCheckCrmIsInner());
        Session sessionShiro = SecurityUtils.getSubject().getSession();
        if(sessionShiro !=null &&  sessionShiro.getTimeout() < shrioSessionTimeOut){
            sessionShiro.setTimeout(shrioSessionTimeOut);
        }
        if (sessionShiro == null) {
            // 异常处理：处理Session无效的情况
            // 可以记录日志或执行其他必要的操作
            return true;
        }
        StringBuffer url = request.getRequestURL();
        String queryString = request.getQueryString();
        if (queryString != null) {
            url.append('?').append(queryString);
        }
        String fullUrl = url.toString();
        HttpSession session = request.getSession();
        if (session.getAttribute(ErpConstant.CURRENT_USER) != null ) {
            return  true;
        }else{
            String lxcrmLoginToken = getLoginTokenFromSession(request);
            if (lxcrmLoginToken != null) {
                String loginUserStr = RedisUtil.StringOps.get(dbType+"lxcrm-login:"+lxcrmLoginToken);
                if(StringUtils.isNotBlank(loginUserStr)) {
                    RedisUtil.KeyOps.expire(dbType + "lxcrm-login:" + lxcrmLoginToken, 30 * 24 * 3600 , TimeUnit.SECONDS);//30天*24小时*3600秒
                    CurrentUser currentUser = JSONObject.parseObject(loginUserStr, CurrentUser.class);
                    session.setAttribute(ErpConstant.CURRENT_USER, currentUser);
                    return true;
                }else {
                    throw new ClientMustBeWeixinException(fullUrl);//将访问地址进行重定向，clientMustBeWeixinException方法中，捕获到全局异常，会将此url写入到浏览器cookie
                }
            }else {
                throw new ClientMustBeWeixinException(fullUrl);//将访问地址进行重定向，clientMustBeWeixinException方法中，捕获到全局异常，会将此url写入到浏览器cookie
            }
        }
    }


    private String getLoginTokenFromSession(HttpServletRequest request) {
        String lxcrmLoginToken = "";
        //读取cookie，取loginToken这个参数
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if("lxcrmLoginToken".equals(cookie.getName())){
                    lxcrmLoginToken = cookie.getValue();
                    break;
                }
            }
        }
        return lxcrmLoginToken;

    }
}
