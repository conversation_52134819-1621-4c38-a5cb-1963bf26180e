package com.vedeng.erp.saleorder.task;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.dao.ExpressFeedbackMapper;
import com.vedeng.erp.saleorder.model.po.ExpressFeedback;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 快递反馈任务
 * @Date 2023/4/18 11:03
 */
@JobHandler(value = "ExpressFeedbackTask")
@Component
@Slf4j
public class ExpressFeedbackTask extends AbstractJobHandler {
    @Resource
    private ExpressFeedbackMapper expressFeedbackMapper;

    @Resource
    private ExpressMapper expressMapper;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取上一个月的1号凌晨0点时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date time = calendar.getTime();
        //time转String
        String beginTime = sdf.format(time);
        //获取上一个月的最后一天的23点59分59秒
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date time1 = calendar.getTime();
        //time转String
        String endTime = sdf.format(time1);


        if (StrUtil.isNotBlank(param)) {
            JSONObject jsonObject = JSON.parseObject(param);
            beginTime = jsonObject.getString("beginTime");
            endTime = jsonObject.getString("endTime");
        }
        List<ExpressFeedback> expressFeedbacks = expressFeedbackMapper.selectByIsSuccess(beginTime, endTime);
        //expressFeedbacks判空


        if (CollectionUtils.isEmpty(expressFeedbacks)) {
            log.info("没有需要处理的数据");
            return ReturnT.SUCCESS;
        }
        //取同expressId的最新一条数据
        List<ExpressFeedback> expressFeedbacksBetter = expressFeedbacks.stream()
                .collect(Collectors.groupingBy(ExpressFeedback::getExpressId,
                        Collectors.maxBy(Comparator.comparing(ExpressFeedback::getExpressFeedbackId))))
                        .values().stream().map(Optional::get).collect(Collectors.toList());

        //标记过滤后的数据
        List<ExpressFeedback> expressFeedbacksNoUse = new ArrayList<>(expressFeedbacks);
        expressFeedbacksNoUse.removeAll(expressFeedbacksBetter);
        //更新标记过滤后的数据
        if (!CollectionUtils.isEmpty(expressFeedbacksNoUse)) {
            expressFeedbacksNoUse.forEach(expressFeedback -> {
                expressFeedback.setIsSuccess(1);
                expressFeedback.setRemark("已被过滤");
                expressFeedbackMapper.updateByPrimaryKeySelective(expressFeedback);
            });
        }
        for (ExpressFeedback expressFeedback : expressFeedbacksBetter) {
            //校验数据是否正确
            Integer expressId = expressFeedback.getExpressId();
            //主键查询
            Express expressById = expressMapper.getExpressInfoById(expressId);
            //校验数据
            if (checkData(expressFeedback, expressById)) continue;
            //更新数据
            ErpSpringBeanUtil.getBean(this.getClass()).updateData(expressFeedback, expressById);


        }
        return ReturnT.SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateData(ExpressFeedback expressFeedback, Express expressById) {
        Integer expressId = expressFeedback.getExpressId();
        String genLogisticsNo = expressFeedback.getGenLogisticsNo();
        try {
            Express express = new Express();
            express.setExpressId(expressById.getExpressId());
            express.setOldLogisticsNo(StringUtil.isNotBlank(expressById.getOldLogisticsNo()) ? expressById.getOldLogisticsNo() : expressById.getLogisticsNo());
            express.setLogisticsNo(genLogisticsNo);
            String logisticsComments = expressById.getLogisticsComments();
            String newLogisticsComments = StringUtil.isNotBlank(logisticsComments)
                    ? new StringBuilder(logisticsComments).append(System.lineSeparator()).append("系统生成快递单号").toString()
                    : "系统生成快递单号";
            express.setLogisticsComments(newLogisticsComments);
            expressMapper.updateByPrimaryKeySelective(express);
            log.info("更新数据成功,主键为{}", expressId);
            expressFeedback.setRemark("更新数据成功");
            expressFeedback.setIsSuccess(1);
            expressFeedbackMapper.updateByPrimaryKeySelective(expressFeedback);
        } catch (Exception e) {
            log.error("更新数据失败,主键为{},异常信息为{}", expressId, e.getMessage());
            expressFeedback.setRemark("更新数据失败");
            handleUpdateFailure(expressFeedback);
        }
    }

    public void handleUpdateFailure(ExpressFeedback expressFeedback) {
        expressFeedback.setIsSuccess(2);
        //重试次数-1
        expressFeedback.setRetryCount(expressFeedback.getRetryCount() - 1);
        expressFeedbackMapper.updateByPrimaryKeySelective(expressFeedback);
    }


    public boolean checkData(ExpressFeedback expressFeedback, Express expressById) {
        Integer expressId = expressFeedback.getExpressId();
        String expressLogisticsNo = expressFeedback.getExpressLogisticsNo();
        if (expressById == null) {
            expressFeedback.setRemark("主键查询不到数据");
            log.info("主键查询不到数据,主键为{}", expressId);
            handleUpdateFailure(expressFeedback);
            return true;
        }
        String logisticsNo = expressById.getLogisticsNo();

        if (StringUtil.isEmpty(logisticsNo)) {
            log.info("主键查询的数据没有单号，expressId:{}", expressId);
            expressFeedback.setRemark("主键查询的数据没有单号");
            handleUpdateFailure(expressFeedback);
            return true;
        }
        if (!logisticsNo.equals(expressLogisticsNo)) {
            log.info("主键查询到的数据与传入的数据不一致,主键为{},传入的数据为{},查询到的数据为{}", expressId, expressLogisticsNo, logisticsNo);
            expressFeedback.setRemark("主键查询到的数据与传入的数据不一致");
            handleUpdateFailure(expressFeedback);
            return true;
        }
        return false;
    }



    //    private void updateData(ExpressFeedback expressFeedback, Integer expressId, String genLogisticsNo, Express expressById) {
//        try {
//            Express express = new Express();
//            express.setExpressId(expressById.getExpressId());
//            if (!StringUtil.isNotEmpty(expressById.getOldLogisticsNo())) {
//                express.setOldLogisticsNo(expressById.getLogisticsNo());
//            }
//            express.setLogisticsNo(genLogisticsNo);
//            //备注，在原备注内容基础上，换行增加一条文案提醒，文案：系统生成快递单号
//            String logisticsComments = expressById.getLogisticsComments();
//            if (StringUtil.isNotEmpty(logisticsComments)) {
//                StringBuilder str = new StringBuilder(logisticsComments).append(System.lineSeparator()).append("系统生成快递单号");
//                express.setLogisticsComments(str.toString());
//            }else {
//                StringBuilder str = new StringBuilder("系统生成快递单号");
//                express.setLogisticsComments(str.toString());
//            }
//            expressMapper.updateByPrimaryKeySelective(express);
//        } catch (Exception e) {
//            log.info("更新数据失败,主键为{},异常信息为{}", expressId, e.getMessage());
//            expressFeedback.setRemark("更新数据失败");
//            expressFeedback.setIsSuccess(2);
//            //重试次数-1
//            expressFeedback.setRetryCount(expressFeedback.getRetryCount() - 1);
//            expressFeedbackMapper.updateByPrimaryKeySelective(expressFeedback);
//            return;
//        }
//        //更新成功
//        log.info("更新数据成功,主键为{}", expressId);
//        expressFeedback.setRemark("更新数据成功");
//        expressFeedback.setIsSuccess(1);
//        expressFeedbackMapper.updateByPrimaryKeySelective(expressFeedback);
//    }
}
