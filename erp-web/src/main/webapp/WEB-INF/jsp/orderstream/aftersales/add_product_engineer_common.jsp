<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <c:if test="${not empty afterSalesVo.afterSalesInstallstionVoList}">
        <%--适应原页面--%>
        <c:choose>
            <c:when test="${afterSalesVo.status eq 0 || afterSalesVo.status eq 3}">
                <div class="table-container">
                    <table class="table  table-style10">
                        <thead>
                        <tr>
                            <th class="wid5">序号</th>
                            <th class="wid8">SKU</th>
                            <th>产品名称</th>
                            <th class="wid10">品牌</th>
                            <th class="wid8">型号</th>
                            <th class="wid8">物料编号</th>
                            <th class="wid8">采购订单</th>
                            <th class="wid5">销售价</th>
                            <th class="wid8">数量</th>
                            <th class="wid5">单位</th>
                            <th class="wid6">售后数量</th>
                            <th class="wid10">是否含安调</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:if test="${not empty afterSalesVo.atGoodsList}">
                            <c:forEach items="${afterSalesVo.atGoodsList}" var="asg" varStatus="sttaus">
                                <tr class="J-skuInfo-tr" skuId="${asg.goodsId}">
                                    <td>${sttaus.count }</td>
                                    <td class="JskuCode"></td>

                                    <td class="text-left">
                                        <div class="customername pos_rel" style="text-align: center;">
                                            <c:if test="${asg.isActionGoods==1}"><span
                                                    style="color:red;">【活动】</span></c:if>
                                            <c:if test="${asg.saleorderPrice eq '0.00'}">
                                                <img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" />
                                            </c:if>
                                            <span class="brand-color1 addtitle JskuName" style="float:none;"
                                                  tabTitle='{"num":"viewgoods${list.goodsId}","link":"./order/afterSalesCommon/viewbaseinfo.do?goodsId=${asg.goodsId}",
	                                       					"title":"产品信息"}'>sdf+${asg.goodsName}</span><i
                                                class="iconbluemouth"></i>

                                            <div class="pos_abs customernameshow JskuInfo" style="display: none;"></div>
                                        </div>
                                    </td>
                                    <td class="JbrandName"></td>
                                    <td class="JskuModel">
                                    </td>
                                    <td class="JmaterialCode"></td>
                                    <td>

                                        <c:if test="${not empty asg.buyorderNos}">
                                            <c:forEach items="${asg.buyorderNos}" var="buyorder">
                                                ${buyorder.buyorderNo}<br>
                                            </c:forEach>
                                        </c:if>
                                    </td>
                                    <td><fmt:formatNumber type="number" value="${asg.saleorderPrice}" pattern="0.00"
                                                          maxFractionDigits="2"/></td>
                                    <td>${asg.saleorderNum}</td>
                                    <td class="JskuUnit"></td>
                                    <td>${asg.num}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${asg.haveInstallation eq 0}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                是
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        <c:if test="${empty afterSalesVo.atGoodsList}">
                            <tr>
                                <td colspan="11">暂无记录！</td>
                            </tr>
                        </c:if>
                        </tbody>
                    </table>
                </div>
            </c:when>

            <c:when test="${afterSalesVo.status eq 1 }">
                <c:forEach items="${afterSalesVo.afterSalesInstallstionVoList}" var="asg" varStatus="sttaus">
                    <table class="table  table-style10">
                        <thead>
                        <tr>
                            <th class="wid5">序号</th>
                            <th class="wid15">售后安调公司</th>
                            <th class="wid15">手机号</th>
                            <th class="wid15">服务时间</th>
                            <th class="wid10">酬金</th>
                            <th class="wid10">上次通知时间</th>
                            <c:choose>
                                <%--售后订单状态 0待确认（默认）、1进行中、2已完结、3已关闭 --%>
                                <c:when test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 && asg.validStatus ne 0 && asg.validStatus ne 1}">
                                    <th class="wid8">服务评分</th>
                                    <th class="wid8">技术评分</th>
                                    <th class="wid15">操作</th>
                                </c:when>
                                <c:otherwise>
                                    <th class="wid10">服务评分</th>
                                    <th class="wid10">技术评分</th>
                                    <th class="wid11">操作</th>
                                </c:otherwise>
                            </c:choose>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>${sttaus.count }</td>
                            <td>
                                <div class="customername pos_rel">
                                        ${asg.name }<i class="iconbluemouth"></i>
                                    <div class="pos_abs customernameshow" style="display: none;">
                                        开户行：${asg.bank}<br>
                                        账号：${asg.bankAccount}<br>
                                        所属公司：${asg.company}<br>
                                        服务次数：${asg.serviceTimes}<br>
                                        服务评分：${asg.serviceScoreAverage}<br>
                                        技能评分：${asg.skillScoreAverage}<br>
                                    </div>
                                </div>
                            </td>
                            <td>${asg.mobile }</td>
                            <td><date:date value ="${asg.serviceTime}" format="yyyy-MM-dd"/></td>
                            <td>${asg.engineerAmount }</td>
                            <td><date:date value ="${asg.lastNoticeTime}" format="yyyy-MM-dd"/></td>
                            <td>${asg.serviceScore }</td>
                            <td>${asg.skillScore }</td>
                            <td class="caozuo">
                                    <%--isSupply-当前用户是否有效--%>
                                        <c:if test="${isSupply ne 1}">
                                            <c:if test="${afterSalesVo.verifyStatus ne 0}"></c:if>
                                            <span class="border-blue addtitle" tabTitle='{"num":"viewafterSalesId<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
					                		"link":"./order/afterSalesCommon/editEngineerPage.do?afterSalesInstallstionId=${asg.afterSalesInstallstionId}&&afterSalesId=${afterSalesVo.afterSalesId}&&areaId=${afterSalesVo.areaId}&&subjectType=${afterSalesVo.subjectType}&&isAtwx=${isAtwx}","title":"编辑产品与售后安调公司"}'>编辑 </span>

                                        </c:if>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="9" class="table-container">

                                <c:set var="afterSalesGoodsVoListPage" value="${asg.afterSalesGoodsVoList}"></c:set>
                                    <%--产品信息--%>
                                <%@ include file="add_product_engineer_goods_common.jsp"%>

                            </td>
                        </tr>
                        </tbody>
                    </table>
                </c:forEach>
            </c:when>
            <c:when test="${ afterSalesVo.status eq 2 }">
                <c:forEach items="${afterSalesVo.afterSalesInstallstionVoList}" var="asg" varStatus="sttaus">
                    <table class="table  table-style10">
                        <thead>
                        <tr>
                            <th class="wid5">序号</th>
                            <th class="wid15">售后安调公司</th>
                            <th class="wid15">手机号</th>
                            <th class="wid15">服务时间</th>
                            <th class="wid10">酬金</th>
                            <th class="wid10">上次通知时间</th>
                            <c:choose>
                                <%--售后订单状态 0待确认（默认）、1进行中、2已完结、3已关闭 --%>
                                <c:when test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 && asg.validStatus ne 0 && asg.validStatus ne 1}">
                                    <th class="wid8">服务评分</th>
                                    <th class="wid8">技术评分</th>
                                    <th class="wid15">操作</th>
                                </c:when>
                                <c:otherwise>
                                    <th class="wid10">服务评分</th>
                                    <th class="wid10">技术评分</th>
                                    <th class="wid11">操作</th>
                                </c:otherwise>
                            </c:choose>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>${sttaus.count }</td>
                            <td>
                                <div class="customername pos_rel">
                                        ${asg.name }<i class="iconbluemouth"></i>
                                    <div class="pos_abs customernameshow" style="display: none;">
                                        开户行：${asg.bank}<br>
                                        账号：${asg.bankAccount}<br>
                                        所属公司：${asg.company}<br>
                                        服务次数：${asg.serviceTimes}<br>
                                        服务评分：${asg.serviceScoreAverage}<br>
                                        技能评分：${asg.skillScoreAverage}<br>
                                    </div>
                                </div>
                            </td>
                            <td>${asg.mobile }</td>
                            <td><date:date value ="${asg.serviceTime}" format="yyyy-MM-dd"/></td>
                            <td>${asg.engineerAmount }</td>
                            <td><date:date value ="${asg.lastNoticeTime}" format="yyyy-MM-dd"/></td>
                            <td>${asg.serviceScore }</td>
                            <td>${asg.skillScore }</td>
                            <td class="caozuo">
                                    <%--isSupply-当前用户是否有效--%>
                                <c:if test="${isSupply ne 1}">
<%--			                     	<span class="border-blue edit-user pop-new-data" layerParams='{"width":"600px","height":"350px","title":"发送派单通知",--%>
<%--										"link":"/order/afterSalesCommon/toSendInstallstionSmsPage.do?afterSalesInstallstionId=${asg.afterSalesInstallstionId}&name=${asg.name}&mobile=${asg.mobile}&afterSalesNo=${afterSalesVo.afterSalesNo}&typeName=${afterSalesVo.typeName}&noticeTimes=${asg.noticeTimes}"}'>发送派单通知</span>--%>
                                    <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 && asg.validStatus ne 0 && asg.validStatus ne 1}">
	 			                     	<span class="border-blue addtitle" tabTitle='{"num":"viewafterSalesId<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
					                		"link":"./order/afterSalesCommon/editEngineerPage.do?afterSalesInstallstionId=${asg.afterSalesInstallstionId}&&afterSalesId=${afterSalesVo.afterSalesId}&&areaId=${afterSalesVo.areaId}&&subjectType=${afterSalesVo.subjectType}&&isAtwx=${isAtwx}","title":"编辑产品与售后安调公司"}'>编辑 </span>
                                    </c:if>
                                    <span class="edit-user pop-new-data" layerParams='{"width":"500px","height":"270px","title":"编辑评价",
										"link":"/order/afterSalesCommon/editscore.do?afterSalesInstallstionId=${asg.afterSalesInstallstionId}"}'>评价</span>
                                </c:if>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="9" class="table-container">

                                <c:set var="afterSalesGoodsVoListPage" value="${asg.afterSalesGoodsVoList}"></c:set>
                                    <%--产品信息--%>
                                <%@ include file="add_product_engineer_goods_common.jsp"%>

                            </td>
                        </tr>
                        </tbody>
                    </table>
                </c:forEach>
            </c:when>
        </c:choose>

    </c:if>
    <c:if test="${empty afterSalesVo.afterSalesInstallstionVoList}">
        <div class="table-container">
            <table class="table  table-style10">
                <thead>
                <tr>
                    <th class="wid5">序号</th>
                    <th class="wid8">SKU</th>
                    <th>产品名称</th>
                    <th class="wid10">品牌</th>
                    <th class="wid8">型号</th>
                    <th class="wid8">物料编号</th>
                    <th class="wid8">采购订单</th>
                    <th class="wid5">销售价</th>
                    <th class="wid8">数量</th>
                    <th class="wid5">单位</th>
                    <th class="wid6">售后数量</th>
                    <th class="wid10">是否含安调</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty afterSalesVo.atGoodsList}">
                    <c:forEach items="${afterSalesVo.atGoodsList}" var="asg" varStatus="sttaus">
                        <tr class="J-skuInfo-tr" skuId="${asg.goodsId}">
                            <td>${sttaus.count }</td>
                            <td class="JskuCode"></td>

                            <td class="text-left">
                                <div class="customername pos_rel" style="text-align: center;">
                                    <c:if test="${asg.isActionGoods==1}"><span
                                            style="color:red;">【活动】</span></c:if>
                                    <c:if test="${asg.isGift eq '1'}">
                                        <img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" />
                                    </c:if>
                                    <c:if test="${asg.isDirectPurchase == 1}">
                                        <img id="isDirectPurchase" src="<%= basePath %>static/images/promote_icon.svg" style="width: 15px" title="商城&quot;现货现价&quot;促销"/>
                                    </c:if>
                                    <span class="brand-color1 addtitle JskuName" style="float:none;"
                                          tabTitle='{"num":"viewgoods${list.goodsId}","link":"./order/afterSalesCommon/viewbaseinfo.do?goodsId=${asg.goodsId}",
	                                       					"title":"产品信息"}'>${asg.goodsName}</span><i
                                        class="iconbluemouth"></i>

                                    <div class="pos_abs customernameshow JskuInfo" style="display: none;"></div>
                                </div>
                            </td>
                            <td class="JbrandName"></td>
                            <td class="JskuModel">
                            </td>
                            <td class="JmaterialCode"></td>
                            <td>

                                <c:if test="${not empty asg.buyorderNos}">
                                    <c:forEach items="${asg.buyorderNos}" var="buyorder">
                                        ${buyorder.buyorderNo}<br>
                                    </c:forEach>
                                </c:if>
                            </td>
                            <td><fmt:formatNumber type="number" value="${asg.saleorderPrice}" pattern="0.00"
                                                  maxFractionDigits="2"/></td>
                            <td>${asg.saleorderNum}</td>
                            <td class="JskuUnit"></td>
                            <td>${asg.num}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${asg.haveInstallation eq 0}">
                                        否
                                    </c:when>
                                    <c:otherwise>
                                        是
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty afterSalesVo.atGoodsList}">
                    <tr>
                        <td colspan="11">暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>

    </c:if>
</div>

<script type="application/javascript">
    //物料编码、注册证号（对应spu的“注册证号”）、管理类别、产品负责人（字段内容改为：该商品对应spu的归属产品经理&归属产品助理）、采购提醒、包装清单、服务条款、审核状态
    $(function(){
        $(".J-skuInfo-tr").each(function(){
            var tr=$(this)
            $.getJSON("/goods/vgoods/static/skuTip.do?skuId="+$(this).attr("skuId"),function(result){
                console.log(result.data)
                var data=result.data
                var goodsInfo  ='物料编号：'+data.MATERIAL_CODE;
                goodsInfo +='<br>注册证号：'+data.REGISTRATION_NUMBER;
                goodsInfo +='<br>管理类别：'+data.MANAGE_CATEGORY_LEVEL;
                goodsInfo +='<br>产品负责人：'+data.PRODUCTMANAGER;
                goodsInfo +='<br>包装清单：'+data.PACKING_LIST;
                goodsInfo +='<br>质保年限：'+(data.QA_YEARS==undefined || data.QA_YEARS==''?'':data.QA_YEARS+'年') ;

                goodsInfo +='<br>库存：'+data.STOCKNUM;
                goodsInfo +='<br>可用库存：'+data.AVAILABLESTOCKNUM;
                goodsInfo +='<br>订单占用：'+data.OCCUPYNUM;
                goodsInfo +='<br>审核状态：'+data.CHECK_STATUS;
                tr.find(".Jproductmanager").html(data.PRODUCTMANAGER_NO_SPACE);
                tr.find(".JmanageLevel").html(data.MANAGE_CATEGORY_LEVEL);
                tr.find(".JskuCode").html(data.SKU_NO);
                tr.find(".JmaterialCode").html(data.MATERIAL_CODE);
                tr.find(".JbrandName").html(data.BRAND_NAME);
                tr.find(".JskuName").html(data.SHOW_NAME);
                tr.find(".JskuModel").html(data.MODEL);
                tr.find(".JskuInfo").html(goodsInfo);
                tr.find(".JskuUnit").html(data.UNIT_NAME);
                tr.find(".JskuStock").html(data.STOCKNUM);
                tr.find(".JskuAvailableStockNum").html(data.AVAILABLESTOCKNUM);
                tr.find(".JskubaseCategoryName").html(data.BASE_CATEGORY_NAME);
            })
        })
    })
</script>