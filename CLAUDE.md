# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Build Commands
```bash
# Build entire project
mvn clean install

# Build specific module
mvn clean install -pl erp-web

# Build module with dependencies
mvn clean install -pl erp-web -am

# Package as WAR files
mvn clean package

# Skip tests during build (tests are skipped by default)
mvn clean install -DskipTests=true
```

### Test Commands
```bash
# Run tests (note: tests are skipped by default)
mvn test -DskipTests=false

# Run tests for specific module
mvn test -pl erp-web -DskipTests=false

# Run integration tests
mvn verify -DskipTests=false
```

### Development Commands
```bash
# Run Spring Boot application (for erp-web-crm)
mvn spring-boot:run -pl erp-web-crm

# Run mobile web application
mvn spring-boot:run -pl erp-web-mobile

# Check dependencies
mvn dependency:tree

# Generate MyBatis code
mvn mybatis-generator:generate
```

### Application Startup
```bash
# Main ERP web application
java -jar erp-web/target/erp-web.war

# CRM web application 
java -jar erp-web-crm/target/erp-web-crm.jar

# Mobile web application
java -jar erp-web-mobile/target/erp-web-mobile.jar
```

## Project Architecture

This is a multi-module Maven Java ERP system with the following key characteristics:

### Technology Stack
- **Backend Framework**: Spring 4.1.9.RELEASE (main), Spring Boot 2.1.5.RELEASE (mobile/CRM)
- **ORM Framework**: MyBatis 3.3.1
- **Cache**: Redis (Spring Data Redis 1.6.6)
- **Database**: MySQL 5.1.47
- **Message Queue**: RabbitMQ
- **Workflow Engine**: Temporal 1.22.0 (erp-temporal module)
- **Job Scheduling**: XXL-Job 2.1.1
- **Utilities**: Hutool 5.7.17, MapStruct 1.4.2, PageHelper 5.3.0
- **Excel Processing**: EasyExcel 3.1.1
- **Monitoring**: CAT 3.0.0

### Module Structure

#### Core Business Modules
- **erp-system**: User management, regions, departments, dictionaries
- **erp-trader**: Customer/supplier management, business opportunities
- **erp-saleorder**: Sales orders, quotes, after-sales service
- **erp-buyorder**: Purchase orders, procurement after-sales
- **erp-goods**: Product information, inventory, categories
- **erp-wms**: Warehouse management, stock transfers
- **erp-finance**: Invoicing, payment terms, financial flows
- **erp-crm**: Customer relationship management
- **erp-oa**: Office automation workflows
- **erp-doc**: Document management
- **erp-kingdee**: Integration with Kingdee ERP system

#### Web Applications
- **erp-web**: Main web application with message queues and scheduled tasks
- **erp-web-mobile**: Mobile web app ("掌上小贝") - Spring Boot 2.1.5
- **erp-web-crm**: CRM-specific web interface - Spring Boot 2.1.5
- **erp-mobile**: Mobile API services

#### Infrastructure & Common Modules
- **erp-common**: Shared utilities with submodules:
  - **erp-common-core**: Core utilities and tools
  - **erp-common-redis**: Redis cache integration
  - **erp-common-mybatis**: MyBatis extensions
  - **erp-common-feign**: Feign remote call wrapper
  - **erp-common-statemachine**: State machine components
  - **erp-common-trace**: Distributed tracing
  - **erp-common-cat**: CAT monitoring integration
  - **erp-common-activiti**: Activiti workflow integration
- **erp-infrastructure**: Third-party integrations (OSS, e-signature, SMS, express)
- **erp-api-standard**: API standardization and validation
- **erp-temporal**: Temporal workflow engine integration
- **erp-old**: Legacy ERP code (maintenance only, no new features)

### Code Layer Structure

Each business module follows this pattern:

```
erp-xxx/
├── erp-xxx-api/           # External interfaces
│   ├── dto/               # Data transfer objects
│   ├── service/           # Interface definitions
│   └── remoteapi/         # Remote service interfaces
└── erp-xxx-biz/           # Business implementation
    ├── common/            # Utils, constants
    ├── config/            # Configuration
    ├── domain/            # Domain objects
    │   ├── entity/        # Database entities
    │   └── dto/           # Business objects
    ├── dao/               # Data access layer
    ├── manager/           # Third-party calls, transaction management
    ├── service/           # Business logic
    └── web/               # Web layer
        ├── api/           # Frontend APIs
        └── controller/    # Route controllers
```

## Development Guidelines

### Module Interaction
- **Never directly depend on other modules' implementation classes**
- **Use API interfaces** for cross-module communication through the `-api` modules
- **Leverage dependency inversion** principle through API modules
- Spring container manages service implementations, enabling interface-based calls

### Transaction Management
- Use `@Transactional` in **manager layer** for database operations
- Keep **service layer** focused on business logic without transaction concerns
- Avoid long-running transactions in service methods
- Use `rollbackFor = Throwable.class` for all transactional methods

### Legacy Code Policy
- **erp-old module**: Maintenance only, no new features
- New functionality should be implemented in corresponding new modules
- Use API interfaces to allow erp-old to call new module functionality
- Follow dependency inversion to enable gradual migration

### Code Conventions (from README.md)
- Use Lombok for boilerplate code reduction
- Entities end with `Entity`, DTOs with `DTO`, VOs with `VO`
- Maximum 3 parameters per method (encapsulate more in JavaBeans)
- Use meaningful method names with proper prefixes (get/list/count/save/update/remove)
- Boolean fields should end with `Flag` (e.g., `deletedFlag` not `isDeleted`)
- Use wrapper types for primitives (Integer, Boolean, etc.)
- Follow URL naming: `/business-module/sub-module/action`

### Testing
- Tests are configured to skip by default (`<skipTests>true</skipTests>`)
- Use `-DskipTests=false` to run tests explicitly
- Test files located in `src/test/java/com/test/`

### Database Standards
- Required fields: `ID`, `ADD_TIME`, `MOD_TIME`
- ID field: Long type, single table auto-increment
- ADD_TIME: datetime, default CURRENT_TIMESTAMP
- MOD_TIME: datetime, default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
- Enum fields must include all possible values in comments
- Use proper indexing and naming conventions

### Workflow Engine (erp-temporal)
- Based on Temporal 1.22.0 for reliable distributed workflows
- Integrates with Spring 4.1.9 using Java Bean configuration
- Supports multi-company business process automation
- Database-driven dynamic workflow configuration via T_FLOW_ORDER and T_FLOW_NODE tables
- Provides SystemApiClient for multi-company API calls

## Important Notes

- Project uses Git with branch-based development (current: ERP_LV_2025_27, main: master)
- Commit messages must include Jira ticket numbers (format: VDERP-XXXXX)
- Code formatting and compilation checks required before commits
- Maven repositories configured for internal Vedeng nexus
- System integrates with external services through erp-infrastructure
- Mixed Spring versions: 4.1.9 (main) and Spring Boot 2.1.5 (mobile/CRM)
- Apollo configuration management integrated
