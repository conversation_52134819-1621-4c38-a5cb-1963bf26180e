package com.vedeng.kpi.service;

import com.vedeng.kpi.model.DTO.KpiUserInfoDto;

/**
 * @description: 整合更新kpi操作.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/11 2:02 下午.
 * @author: Tomcat.Hui.
 */
public interface KpiUpdateService {

    /**
     * @description: 更新单个用户kpi.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/11 2:31 下午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @return: void.
     * @throws: .
     */
    void updateKpiSingle(KpiUserInfoDto userInfo) throws Exception;
}
