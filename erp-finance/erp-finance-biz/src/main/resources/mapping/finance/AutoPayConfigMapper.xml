<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.AutoPayConfigMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.AutoPayConfig">
    <!--@mbg.generated-->
    <!--@Table T_AUTO_PAY_CONFIG-->
    <id column="AUTO_PAY_CONFIG_ID" jdbcType="INTEGER" property="autoPayConfigId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="ENABLE_AUTO_PAY" jdbcType="INTEGER" property="enableAutoPay" />
    <result column="ENABLE_CONTRACT_AUDIT" jdbcType="INTEGER" property="enableContractAudit" />
    <result column="ENABLE_SUPPLIER_WHITELIST" jdbcType="INTEGER" property="enableSupplierWhitelist" />
    <result column="SUPPLIER_WHITELIST" jdbcType="VARCHAR" property="supplierWhitelist" />
    <result column="ENABLE_KING_DEE_AUTO_AUDIT" jdbcType="INTEGER" property="enableKingDeeAutoAudit" />
    <result column="PAY_APPLY_TIME" jdbcType="VARCHAR" property="payApplyTime" />
    <result column="PAY_LIMIT" jdbcType="VARCHAR" property="payLimit" />
    <result column="PAY_BANK_TIME" jdbcType="VARCHAR" property="payBankTime" />
    <result column="PAY_BANK" jdbcType="VARCHAR" property="payBank" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AUTO_PAY_CONFIG_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    ENABLE_AUTO_PAY, ENABLE_CONTRACT_AUDIT, ENABLE_SUPPLIER_WHITELIST, SUPPLIER_WHITELIST, 
    ENABLE_KING_DEE_AUTO_AUDIT, PAY_APPLY_TIME, PAY_LIMIT, PAY_BANK_TIME, PAY_BANK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AUTO_PAY_CONFIG
    where AUTO_PAY_CONFIG_ID = #{autoPayConfigId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AUTO_PAY_CONFIG
    where AUTO_PAY_CONFIG_ID = #{autoPayConfigId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AUTO_PAY_CONFIG_ID" keyProperty="autoPayConfigId" parameterType="com.vedeng.erp.finance.domain.entity.AutoPayConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AUTO_PAY_CONFIG (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      ENABLE_AUTO_PAY, ENABLE_CONTRACT_AUDIT, ENABLE_SUPPLIER_WHITELIST, 
      SUPPLIER_WHITELIST, ENABLE_KING_DEE_AUTO_AUDIT, 
      PAY_APPLY_TIME, PAY_LIMIT, PAY_BANK_TIME, 
      PAY_BANK)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{enableAutoPay,jdbcType=INTEGER}, #{enableContractAudit,jdbcType=INTEGER}, #{enableSupplierWhitelist,jdbcType=INTEGER}, 
      #{supplierWhitelist,jdbcType=VARCHAR}, #{enableKingDeeAutoAudit,jdbcType=INTEGER}, 
      #{payApplyTime,jdbcType=VARCHAR}, #{payLimit,jdbcType=VARCHAR}, #{payBankTime,jdbcType=VARCHAR}, 
      #{payBank,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="AUTO_PAY_CONFIG_ID" keyProperty="autoPayConfigId" parameterType="com.vedeng.erp.finance.domain.entity.AutoPayConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AUTO_PAY_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="enableAutoPay != null">
        ENABLE_AUTO_PAY,
      </if>
      <if test="enableContractAudit != null">
        ENABLE_CONTRACT_AUDIT,
      </if>
      <if test="enableSupplierWhitelist != null">
        ENABLE_SUPPLIER_WHITELIST,
      </if>
      <if test="supplierWhitelist != null">
        SUPPLIER_WHITELIST,
      </if>
      <if test="enableKingDeeAutoAudit != null">
        ENABLE_KING_DEE_AUTO_AUDIT,
      </if>
      <if test="payApplyTime != null">
        PAY_APPLY_TIME,
      </if>
      <if test="payLimit != null">
        PAY_LIMIT,
      </if>
      <if test="payBankTime != null">
        PAY_BANK_TIME,
      </if>
      <if test="payBank != null">
        PAY_BANK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="enableAutoPay != null">
        #{enableAutoPay,jdbcType=INTEGER},
      </if>
      <if test="enableContractAudit != null">
        #{enableContractAudit,jdbcType=INTEGER},
      </if>
      <if test="enableSupplierWhitelist != null">
        #{enableSupplierWhitelist,jdbcType=INTEGER},
      </if>
      <if test="supplierWhitelist != null">
        #{supplierWhitelist,jdbcType=VARCHAR},
      </if>
      <if test="enableKingDeeAutoAudit != null">
        #{enableKingDeeAutoAudit,jdbcType=INTEGER},
      </if>
      <if test="payApplyTime != null">
        #{payApplyTime,jdbcType=VARCHAR},
      </if>
      <if test="payLimit != null">
        #{payLimit,jdbcType=VARCHAR},
      </if>
      <if test="payBankTime != null">
        #{payBankTime,jdbcType=VARCHAR},
      </if>
      <if test="payBank != null">
        #{payBank,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.AutoPayConfig">
    <!--@mbg.generated-->
    update T_AUTO_PAY_CONFIG
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="enableAutoPay != null">
        ENABLE_AUTO_PAY = #{enableAutoPay,jdbcType=INTEGER},
      </if>
      <if test="enableContractAudit != null">
        ENABLE_CONTRACT_AUDIT = #{enableContractAudit,jdbcType=INTEGER},
      </if>
      <if test="enableSupplierWhitelist != null">
        ENABLE_SUPPLIER_WHITELIST = #{enableSupplierWhitelist,jdbcType=INTEGER},
      </if>
      <if test="supplierWhitelist != null">
        SUPPLIER_WHITELIST = #{supplierWhitelist,jdbcType=VARCHAR},
      </if>
      <if test="enableKingDeeAutoAudit != null">
        ENABLE_KING_DEE_AUTO_AUDIT = #{enableKingDeeAutoAudit,jdbcType=INTEGER},
      </if>
      <if test="payApplyTime != null">
        PAY_APPLY_TIME = #{payApplyTime,jdbcType=VARCHAR},
      </if>
      <if test="payLimit != null">
        PAY_LIMIT = #{payLimit,jdbcType=VARCHAR},
      </if>
      <if test="payBankTime != null">
        PAY_BANK_TIME = #{payBankTime,jdbcType=VARCHAR},
      </if>
      <if test="payBank != null">
        PAY_BANK = #{payBank,jdbcType=VARCHAR},
      </if>
    </set>
    where AUTO_PAY_CONFIG_ID = #{autoPayConfigId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.AutoPayConfig">
    <!--@mbg.generated-->
    update T_AUTO_PAY_CONFIG
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      ENABLE_AUTO_PAY = #{enableAutoPay,jdbcType=INTEGER},
      ENABLE_CONTRACT_AUDIT = #{enableContractAudit,jdbcType=INTEGER},
      ENABLE_SUPPLIER_WHITELIST = #{enableSupplierWhitelist,jdbcType=INTEGER},
      SUPPLIER_WHITELIST = #{supplierWhitelist,jdbcType=VARCHAR},
      ENABLE_KING_DEE_AUTO_AUDIT = #{enableKingDeeAutoAudit,jdbcType=INTEGER},
      PAY_APPLY_TIME = #{payApplyTime,jdbcType=VARCHAR},
      PAY_LIMIT = #{payLimit,jdbcType=VARCHAR},
      PAY_BANK_TIME = #{payBankTime,jdbcType=VARCHAR},
      PAY_BANK = #{payBank,jdbcType=VARCHAR}
    where AUTO_PAY_CONFIG_ID = #{autoPayConfigId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AUTO_PAY_CONFIG
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ENABLE_AUTO_PAY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.enableAutoPay,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ENABLE_CONTRACT_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.enableContractAudit,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ENABLE_SUPPLIER_WHITELIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.enableSupplierWhitelist,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SUPPLIER_WHITELIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.supplierWhitelist,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ENABLE_KING_DEE_AUTO_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.enableKingDeeAutoAudit,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PAY_APPLY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.payApplyTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PAY_LIMIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.payLimit,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PAY_BANK_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.payBankTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PAY_BANK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.payBank,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where AUTO_PAY_CONFIG_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.autoPayConfigId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AUTO_PAY_CONFIG
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ENABLE_AUTO_PAY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enableAutoPay != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.enableAutoPay,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ENABLE_CONTRACT_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enableContractAudit != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.enableContractAudit,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ENABLE_SUPPLIER_WHITELIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enableSupplierWhitelist != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.enableSupplierWhitelist,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SUPPLIER_WHITELIST = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.supplierWhitelist != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.supplierWhitelist,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ENABLE_KING_DEE_AUTO_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enableKingDeeAutoAudit != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.enableKingDeeAutoAudit,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAY_APPLY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payApplyTime != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.payApplyTime,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAY_LIMIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payLimit != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.payLimit,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAY_BANK_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payBankTime != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.payBankTime,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAY_BANK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payBank != null">
            when AUTO_PAY_CONFIG_ID = #{item.autoPayConfigId,jdbcType=INTEGER} then #{item.payBank,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where AUTO_PAY_CONFIG_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.autoPayConfigId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="AUTO_PAY_CONFIG_ID" keyProperty="autoPayConfigId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AUTO_PAY_CONFIG
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ENABLE_AUTO_PAY, 
      ENABLE_CONTRACT_AUDIT, ENABLE_SUPPLIER_WHITELIST, SUPPLIER_WHITELIST, ENABLE_KING_DEE_AUTO_AUDIT, 
      PAY_APPLY_TIME, PAY_LIMIT, PAY_BANK_TIME, PAY_BANK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.enableAutoPay,jdbcType=INTEGER}, #{item.enableContractAudit,jdbcType=INTEGER}, 
        #{item.enableSupplierWhitelist,jdbcType=INTEGER}, #{item.supplierWhitelist,jdbcType=VARCHAR}, 
        #{item.enableKingDeeAutoAudit,jdbcType=INTEGER}, #{item.payApplyTime,jdbcType=VARCHAR}, 
        #{item.payLimit,jdbcType=VARCHAR}, #{item.payBankTime,jdbcType=VARCHAR}, #{item.payBank,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="AUTO_PAY_CONFIG_ID" keyProperty="autoPayConfigId" parameterType="com.vedeng.erp.finance.domain.entity.AutoPayConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AUTO_PAY_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="autoPayConfigId != null">
        AUTO_PAY_CONFIG_ID,
      </if>
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
      ENABLE_AUTO_PAY,
      ENABLE_CONTRACT_AUDIT,
      ENABLE_SUPPLIER_WHITELIST,
      SUPPLIER_WHITELIST,
      ENABLE_KING_DEE_AUTO_AUDIT,
      PAY_APPLY_TIME,
      PAY_LIMIT,
      PAY_BANK_TIME,
      PAY_BANK,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="autoPayConfigId != null">
        #{autoPayConfigId,jdbcType=INTEGER},
      </if>
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
      #{enableAutoPay,jdbcType=INTEGER},
      #{enableContractAudit,jdbcType=INTEGER},
      #{enableSupplierWhitelist,jdbcType=INTEGER},
      #{supplierWhitelist,jdbcType=VARCHAR},
      #{enableKingDeeAutoAudit,jdbcType=INTEGER},
      #{payApplyTime,jdbcType=VARCHAR},
      #{payLimit,jdbcType=VARCHAR},
      #{payBankTime,jdbcType=VARCHAR},
      #{payBank,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="autoPayConfigId != null">
        AUTO_PAY_CONFIG_ID = #{autoPayConfigId,jdbcType=INTEGER},
      </if>
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      ENABLE_AUTO_PAY = #{enableAutoPay,jdbcType=INTEGER},
      ENABLE_CONTRACT_AUDIT = #{enableContractAudit,jdbcType=INTEGER},
      ENABLE_SUPPLIER_WHITELIST = #{enableSupplierWhitelist,jdbcType=INTEGER},
      SUPPLIER_WHITELIST = #{supplierWhitelist,jdbcType=VARCHAR},
      ENABLE_KING_DEE_AUTO_AUDIT = #{enableKingDeeAutoAudit,jdbcType=INTEGER},
      PAY_APPLY_TIME = #{payApplyTime,jdbcType=VARCHAR},
      PAY_LIMIT = #{payLimit,jdbcType=VARCHAR},
      PAY_BANK_TIME = #{payBankTime,jdbcType=VARCHAR},
      PAY_BANK = #{payBank,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="AUTO_PAY_CONFIG_ID" keyProperty="autoPayConfigId" parameterType="com.vedeng.erp.finance.domain.entity.AutoPayConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AUTO_PAY_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="autoPayConfigId != null">
        AUTO_PAY_CONFIG_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="enableAutoPay != null">
        ENABLE_AUTO_PAY,
      </if>
      <if test="enableContractAudit != null">
        ENABLE_CONTRACT_AUDIT,
      </if>
      <if test="enableSupplierWhitelist != null">
        ENABLE_SUPPLIER_WHITELIST,
      </if>
      <if test="supplierWhitelist != null">
        SUPPLIER_WHITELIST,
      </if>
      <if test="enableKingDeeAutoAudit != null">
        ENABLE_KING_DEE_AUTO_AUDIT,
      </if>
      <if test="payApplyTime != null">
        PAY_APPLY_TIME,
      </if>
      <if test="payLimit != null">
        PAY_LIMIT,
      </if>
      <if test="payBankTime != null">
        PAY_BANK_TIME,
      </if>
      <if test="payBank != null">
        PAY_BANK,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="autoPayConfigId != null">
        #{autoPayConfigId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="enableAutoPay != null">
        #{enableAutoPay,jdbcType=INTEGER},
      </if>
      <if test="enableContractAudit != null">
        #{enableContractAudit,jdbcType=INTEGER},
      </if>
      <if test="enableSupplierWhitelist != null">
        #{enableSupplierWhitelist,jdbcType=INTEGER},
      </if>
      <if test="supplierWhitelist != null">
        #{supplierWhitelist,jdbcType=VARCHAR},
      </if>
      <if test="enableKingDeeAutoAudit != null">
        #{enableKingDeeAutoAudit,jdbcType=INTEGER},
      </if>
      <if test="payApplyTime != null">
        #{payApplyTime,jdbcType=VARCHAR},
      </if>
      <if test="payLimit != null">
        #{payLimit,jdbcType=VARCHAR},
      </if>
      <if test="payBankTime != null">
        #{payBankTime,jdbcType=VARCHAR},
      </if>
      <if test="payBank != null">
        #{payBank,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="autoPayConfigId != null">
        AUTO_PAY_CONFIG_ID = #{autoPayConfigId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="enableAutoPay != null">
        ENABLE_AUTO_PAY = #{enableAutoPay,jdbcType=INTEGER},
      </if>
      <if test="enableContractAudit != null">
        ENABLE_CONTRACT_AUDIT = #{enableContractAudit,jdbcType=INTEGER},
      </if>
      <if test="enableSupplierWhitelist != null">
        ENABLE_SUPPLIER_WHITELIST = #{enableSupplierWhitelist,jdbcType=INTEGER},
      </if>
      <if test="supplierWhitelist != null">
        SUPPLIER_WHITELIST = #{supplierWhitelist,jdbcType=VARCHAR},
      </if>
      <if test="enableKingDeeAutoAudit != null">
        ENABLE_KING_DEE_AUTO_AUDIT = #{enableKingDeeAutoAudit,jdbcType=INTEGER},
      </if>
      <if test="payApplyTime != null">
        PAY_APPLY_TIME = #{payApplyTime,jdbcType=VARCHAR},
      </if>
      <if test="payLimit != null">
        PAY_LIMIT = #{payLimit,jdbcType=VARCHAR},
      </if>
      <if test="payBankTime != null">
        PAY_BANK_TIME = #{payBankTime,jdbcType=VARCHAR},
      </if>
      <if test="payBank != null">
        PAY_BANK = #{payBank,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-04-25-->
  <select id="findLastAutoPayConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AUTO_PAY_CONFIG order by AUTO_PAY_CONFIG_ID desc limit 1
    </select>
</mapper>