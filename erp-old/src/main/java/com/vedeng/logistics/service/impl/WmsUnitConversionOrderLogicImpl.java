package com.vedeng.logistics.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.wms.model.po.WmsOutputOrder;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/28 17:32
 **/
@Service
public class WmsUnitConversionOrderLogicImpl extends BaseWarehouseGoodsOutDetailService {

    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;

    private final static String DETAIL_URL = "/wmsUnitConversion/detail.do?wmsUnitConversionOrderId=";

    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(relatedNo);
        String url = "";
        if (wmsUnitConversionOrder != null){
            url = REDIRECT_URL_PREFIX + DETAIL_URL + wmsUnitConversionOrder.getWmsUnitConversionOrderId();
        }
        return url;
    }


    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.UNIT_CONVERSION_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo warehouseGoodsOutVo = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.UNIT_CONVERSION_OUT.getErpCode());
        if(warehouseGoodsOutVo != null){
            String relateNo = warehouseGoodsOutVo.getRelateNo();
            if (StringUtils.isNotBlank(relateNo)) {
                WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(relateNo);
                if(Objects.nonNull(wmsUnitConversionOrder)){
                    warehouseGoodsOutVo.setBelongUserName(wmsUnitConversionOrder.getCreatorName());
                    warehouseGoodsOutVo.setBelongUserOrgName(wmsUnitConversionOrder.getOrgName());
                }
            }
        }
        return warehouseGoodsOutVo;
    }

}
