package com.vedeng.trader.model;

import java.io.Serializable;

/**
 * T_TRADER_CERTIFICATE_HISTORY
 * <AUTHOR>
public class TraderCertificateHistory implements Serializable {
    /**
     * 资质ID
     */
    private Integer traderCertificateHistoryId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * scope :1001
     */
    private Integer sysOptionDefinitionId;

    /**
     * 证书开始时间
     */
    private Long begintime;

    /**
     * 证书结束时间
     */
    private Long endtime;

    /**
     * 证书编号
     */
    private String sn;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 证书域名
     */
    private String domain;

    /**
     * 文件地址
     */
    private String uri;

    /**
     * 扩展信息
     */
    private String extra;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 备案号
     */
    private String recordNo;

    private static final long serialVersionUID = 1L;

    public Integer getTraderCertificateHistoryId() {
        return traderCertificateHistoryId;
    }

    public void setTraderCertificateHistoryId(Integer traderCertificateHistoryId) {
        this.traderCertificateHistoryId = traderCertificateHistoryId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getSysOptionDefinitionId() {
        return sysOptionDefinitionId;
    }

    public void setSysOptionDefinitionId(Integer sysOptionDefinitionId) {
        this.sysOptionDefinitionId = sysOptionDefinitionId;
    }

    public Long getBegintime() {
        return begintime;
    }

    public void setBegintime(Long begintime) {
        this.begintime = begintime;
    }

    public Long getEndtime() {
        return endtime;
    }

    public void setEndtime(Long endtime) {
        this.endtime = endtime;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo;
    }
}