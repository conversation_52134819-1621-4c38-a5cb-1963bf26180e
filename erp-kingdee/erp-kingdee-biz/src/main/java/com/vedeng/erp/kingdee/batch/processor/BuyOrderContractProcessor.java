package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBuyOrderContractDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;
import com.vedeng.erp.kingdee.batch.dto.BatchVerifiesInfoDto;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeBuyOrderContractDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleorderContractDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BuyOrderContractProcessor extends BaseProcessor<BatchBuyOrderContractDto, KingDeeBuyOrderContractDto> {

    @Autowired
    KingDeeBaseApi kingDeeBaseApi;
    public static final String RELATE_TABLE_BUYORDER_CONTRACT = "T_BUYORDER_CONTRACT";

    @Override
    public KingDeeBuyOrderContractDto doProcess(BatchBuyOrderContractDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始处理采购订单合同推送金蝶，dto:{}", JSONUtil.toJsonStr(dto));
        if(ObjectUtil.isNotNull(dto.getDataId())){
            log.info("采购合同推送金蝶，数据已存在:{}",JSON.toJSONString(dto));
            return null;
        }

        KingDeeBuyOrderContractDto result = new KingDeeBuyOrderContractDto();
        result.setFQzokDdh(dto.getBuyOrderNo());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(result);
        if(old){
            log.info("采购合同推送金蝶,数据已存在:{}", JSON.toJSONString(dto));
            return null;
        }
        result.setFId(ErpConstant.ZERO);
        result.setFQzokOrgid(KingDeeConstant.ORG_ID.toString());
        result.setFQzokHtrq(DateUtil.formatDateTime(new Date(dto.getValidTime())));
        result.setFQzokHth(dto.getBuyOrderNo());
        result.setFQzokHtje(dto.getTotalAmount().toString());
        result.setFQzokSll(dto.getRate().multiply(new BigDecimal("100")).toString());
        result.setFBillNo(dto.getBuyOrderNo());
        return result;
    }
}
