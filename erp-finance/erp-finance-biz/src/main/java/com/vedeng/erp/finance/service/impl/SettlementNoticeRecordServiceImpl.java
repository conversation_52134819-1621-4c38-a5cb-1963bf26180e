package com.vedeng.erp.finance.service.impl;

import com.vedeng.erp.finance.domain.entity.SettlementNoticeRecordEntity;
import com.vedeng.erp.finance.dto.SettlementNoticeRecordDto;
import com.vedeng.erp.finance.mapper.SettlementNoticeRecordMapper;
import com.vedeng.erp.finance.mapstruct.SettlementNoticeRecordConvertor;
import com.vedeng.erp.finance.service.SettlementNoticeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务结款通知记录服务实现类
 */
@Service
@Slf4j
public class SettlementNoticeRecordServiceImpl implements SettlementNoticeRecordService {

    @Autowired
    private SettlementNoticeRecordMapper settlementNoticeRecordMapper;
    
    @Autowired
    private SettlementNoticeRecordConvertor settlementNoticeRecordConvertor;

    @Override
    public List<SettlementNoticeRecordDto> querySettlementNoticeRecord(Integer bankBillId, Integer noticeType) {
        List<SettlementNoticeRecordEntity> entities = settlementNoticeRecordMapper.querySettlementNoticeRecord(bankBillId, noticeType);
        return settlementNoticeRecordConvertor.toDto(entities);
    }

    @Override
    public Long createSettlementNoticeRecord(SettlementNoticeRecordDto settlementNoticeRecordDto) {
        SettlementNoticeRecordEntity entity = settlementNoticeRecordConvertor.toEntity(settlementNoticeRecordDto);
        settlementNoticeRecordMapper.insertSelective(entity);
        return entity.getSettlementNoticeRecordId();
    }

    @Override
    public void updateSettlementNoticeRecord(SettlementNoticeRecordDto settlementNoticeRecordDto) {
        SettlementNoticeRecordEntity entity = settlementNoticeRecordConvertor.toEntity(settlementNoticeRecordDto);
        settlementNoticeRecordMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public Map<String, String> getUserNumber() {
        Map<String, String> codeMap = new HashMap<>();

        // 添加映射关系
        codeMap.put("0973", "qy015681230e9d0db29cd25b6fbb");
        codeMap.put("0949", "qy018381450e800db09c9988b01a");
        codeMap.put("1012", "qy01aa81fd0e2e0db29c871f3dd4");
        codeMap.put("1027", "qy012881bb0e570db09c96438a1c");
        codeMap.put("1023", "qy018781280e7b0db39cb20c5706");
        codeMap.put("1043", "qy017b81fe0eb10db19cb789fd1c");
        codeMap.put("1045", "qy012381660e1c0db19c9cb09de5");
        codeMap.put("1046", "qy01a281ed0e240db39c5d5fa158");
        codeMap.put("1047", "qy012581de0e560db29cecfed2ec");
        return codeMap;
    }
}
