$(function()
{
    var userId = $("#others_userId_id").val();
    init_page(userId);
    
});

/**
 * @description: 初始化页面.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/17 5:48 下午.
 * @author: Tomcat.Hui.
 */
function init_page(userId) {

    var month = $("#hisMonth").val();

    // 本月概况
    $.ajax(
    {
        type : "POST",
        url : page_url + "/kpi/query/queryKpiDetailKh.do",
        data :
            {
                'userId' : userId,
                'month' : month
            },
        dataType : 'json',
        success : function(result)
        {
            if (result.code == 0)
            {
                //用户
                $("#kpi_customer_co_user").text(result.data.personalKpi.coCustomerNum);
                $("#kpi_customer_lost_user").text(result.data.personalKpi.lostCustomerNum);
                $("#kpi_customer_score_user").text((result.data.personalKpi.score).toFixed(2));
                $("#kpi_customer_sort_user").text(result.data.personalKpi.sort);

                //团队
                $("#kpi_customer_co_group").text(result.data.groupKpi.coCustomerNum);
                $("#kpi_customer_lost_group").text(result.data.groupKpi.lostCustomerNum);
                $("#kpi_customer_score_group").text((result.data.groupKpi.score).toFixed(2));
                // $("#kpi_customer_sort_group").text(result.data.groupKpi.sort);

                //榜首
                $("#first_one_span_name").text(result.data.firstKpi.userName)
                $("#kpi_customer_co_first").text(result.data.firstKpi.coCustomerNum);
                $("#kpi_customer_lost_first").text(result.data.firstKpi.lostCustomerNum);
                $("#kpi_customer_score_first").text((result.data.firstKpi.score).toFixed(2));
                $("#kpi_customer_sort_first").text(result.data.firstKpi.sort);

                //昨天
                $("#kpi_customer_co_yesterday").text(result.data.yesterdayKpi.coCustomerNum);
                $("#kpi_customer_lost_yesterday").text(result.data.yesterdayKpi.lostCustomerNum);
                $("#kpi_customer_score_yesterday").text((result.data.yesterdayKpi.score).toFixed(2));
                $("#kpi_customer_sort_yesterday").text(result.data.yesterdayKpi.sort);

                //上月
                $("#kpi_customer_co_lastmonth").text(result.data.lastMonthKpi.coCustomerNum);
                $("#kpi_customer_lost_lastmonth").text(result.data.lastMonthKpi.lostCustomerNum);
                $("#kpi_customer_score_lastmonth").text((result.data.lastMonthKpi.score).toFixed(2));
                $("#kpi_customer_sort_lastmonth").text(result.data.lastMonthKpi.sort);

                show_detail(result.data.coCustomerList_90,'detail_customer_co_90');
                show_detail(result.data.lostCustomerList_90,'detail_customer_lost_90');
                show_detail(result.data.lostCustomerList_180,'detail_customer_tb_180');

                loadMoreAddTitle();
            }
            else {
                layer.alert("返回码错误");
            }

        },
        error : function(data)
        {

            if (data.status == 1001)
            {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            } else {
                layer.alert("请求出现异常");
            }

        }
    });

    function show_detail(logList,table_id){
        var emptyFlag = false;
        var ht = '';
        if(null == logList || logList.length == 0)
        {
            emptyFlag = true;
        }

        if(!emptyFlag)
        {
            for(var i = 0; i < logList.length; i++)
            {
                var mod = logList[i];

                if(null != mod)
                ht += '<tr>';
                ht += '<td><a class="loadMoreAddtitle" href="javascript:void(0);" tabTitle=\'{"num":"viewcustomer' + mod.traderId + '","link":"./trader/customer/baseinfo.do?traderId=' +  mod.traderId + '","title":"客户信息"}\'>' + mod.traderName + '</a></td>';
                ht += '<td>'+ mod.customersNums +'</td>';
                ht += '<td>'+ ((null == mod.lastOrderTime) ? '-' : new Date(mod.lastOrderTime).format('yyyy-MM-dd hh:mm:ss')) +'</td>';
                ht += '<td>'+ ((null == mod.lastChatTime) ? '-' : new Date(mod.lastChatTime).format('yyyy-MM-dd hh:mm:ss')) +'</td>';
                ht += '<td>'+ ((null == mod.chatApartDays) ? '-' : mod.chatApartDays)+'</td>';
                ht += '<td>'+ mod.kpiAmount / 10000 +'</td>';
                // ht += '<td>'+ - +'</td>';
                ht += '</tr>';
                emptyFlag = false;
            }
        }

        if(emptyFlag)
        {
            ht += '<tr><td colspan="6">暂无数据</td></tr>';
        }

        $("#" + table_id).html(ht);
    }
}
