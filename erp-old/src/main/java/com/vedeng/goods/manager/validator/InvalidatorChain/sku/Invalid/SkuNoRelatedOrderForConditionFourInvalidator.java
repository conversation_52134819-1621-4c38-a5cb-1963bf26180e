package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;

/**
 * (4)	该SKU没有（待确认、进行中的VB，VP采购单，销售售后退货单，且单据中该SKU处于未收货，或部分收货状态）
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/8/31 16:50.
 * @author: Randy.Xu.
 */
@Service
public class SkuNoRelatedOrderForConditionFourInvalidator extends GoodsInvalidatorChain {


    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Override
    public void doInvalid() {
        String skuNo = getSkuNo();
        List<Integer> goodsIds = coreSkuMapper.getGoodsIdBySkuNo(skuNo);
        List<Buyorder> relatedBuyorder =  buyorderMapper.getRelateBuyorderInProgress(skuNo);
        List<AfterSales> relatedAftersales = afterSalesMapper.getRelateAftersalesConditionFour(goodsIds);
        StringBuffer sb = new StringBuffer();
        if(CollectionUtils.isNotEmpty(relatedBuyorder)){
            String collect = relatedBuyorder.stream().map(e -> e.getBuyorderNo()).collect(Collectors.joining(","));
            sb.append(collect);
            sb.append(",");
        }
        if(CollectionUtils.isNotEmpty(relatedAftersales)){
            String collect = relatedAftersales.stream().map(e -> e.getAfterSalesNo()).collect(Collectors.joining(","));
            sb.append(collect);
            sb.append(",");
        }
        if(sb.length()>0){
            String s = sb.substring(0,sb.length()-1);
            String orderNoStr = SkuNoRelatedOrderForConditionFourInvalidatorError.getMessage().replace("orderNo", s);
            throw new InvalidatorChainException(SkuNoRelatedOrderForConditionFourInvalidatorError.getCode(),orderNoStr);
        }

    }
}
