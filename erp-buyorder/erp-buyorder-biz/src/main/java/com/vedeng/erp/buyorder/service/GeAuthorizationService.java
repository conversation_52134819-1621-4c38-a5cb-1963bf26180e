package com.vedeng.erp.buyorder.service;

import com.vedeng.erp.buyorder.domain.entity.GeAuthorization;
import com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate;
import com.vedeng.erp.buyorder.domain.entity.GeActionLog;
import com.vedeng.erp.buyorder.domain.entity.GeAuthorizationVerify;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 授权书接口
 * <AUTHOR>
 * @date 2022/2/15 11:01
 **/
public interface GeAuthorizationService {

    /**
     * 保存授权书营业执照
     * @param request
     * @param geAuthorization
     * @param userId
     * @return
     */
    List<GeAuthorizationCertificate> saveYyFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId);

    /**
     * 二类
     * @param request
     * @param geAuthorization
     * @param userId
     * @return
     */
    List<GeAuthorizationCertificate> saveElFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId);

    /**
     *
     * @param request
     * @param geAuthorization
     * @param userId
     * @return
     */
    List<GeAuthorizationCertificate> saveSlFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId);

    /**
     *
     * @param request
     * @param geAuthorization
     * @param userId
     * @return
     */
    List<GeAuthorizationCertificate> saveCxFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId);

    /**
     *
     * @param request
     * @param geAuthorization
     * @param userId
     * @return
     */
    List<GeAuthorizationCertificate> saveSyFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId);

    /**
     *
     * @param request
     * @param geAuthorization
     * @param userId
     * @return
     */
    List<GeAuthorizationCertificate> saveXyFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId);

    /**
     *
     * @param request
     * @param geAuthorization
     * @param userId
     * @return
     */
    List<GeAuthorizationCertificate> saveZbFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId);

    /**
     *
     * @param request
     * @param geAuthorization
     * @param userId
     * @return
     */
    List<GeAuthorizationCertificate> saveQtFile(HttpServletRequest request, GeAuthorization geAuthorization, Integer userId);

    /**
     *
     * @param key
     * @param prefix
     * @param length
     * @return
     */
    String getGeAuthorizationNoByRedis(String key, String prefix, Integer length);

    /**
     * 获取当前授权书的信息
     * @param authorizationId
     * @return
     */
    Map<String,Object> getGeAuthorizationData(Integer authorizationId);

    /**
     * 保存授权书主信息返回主键
     * @param geAuthorization
     * @return
     */
    GeAuthorization saveGeAuthorization(GeAuthorization geAuthorization);

    /**
     * 更具授权书主键更新授权书信息
     * @param geAuthorization
     */
    void updateAuthorizationById(GeAuthorization geAuthorization);

    /**
     * 去除老图附件
     * @param geAuthorization
     */
    void deleteOldTypeFile(GeAuthorization geAuthorization);

    /**
     * 保存日志
     * @param geActionLog
     */
    void saveAuthorizationLog(GeActionLog geActionLog);

    /**
     * 计算日志信息
     * @param changes
     * @return
     */
    String getGeAuthorizationLogContent(String[] changes);

    /**
     * 获取授权书修改等日志信息
     * @param authorizationId 授权书id
     */
    List<GeActionLog> getGeAuthorizationLogData(Integer authorizationId);

    /**
     * 发起审核或关闭审核 未审核0以及审核未通过3的可以发起
     * @param authorizationId 授权书id
     * @return boolean
     */
    boolean doForAudit(Integer authorizationId,Integer status);

    /**
     * 保存审核记录
     * @param geAuthorizationVerify 审核日志信息
     */
    void saveGeAuthorizationAuitLog(GeAuthorizationVerify geAuthorizationVerify);

    /**
     * 更改审核状态
     * @param geAuthorization 授权书
     * @return
     */
    boolean makeAudit(GeAuthorization geAuthorization);

    /**
     * 获取审核记录
     * @param authorizationId
     */
    List<GeAuthorizationVerify> getGeAuthorizationVerifyData(Integer authorizationId);

    /**
     * 查询未关闭的授权书
     *
     * @param geAuthorization
     * @return
     */
    List<GeAuthorization> countGeBussinessChanceHaveNoCloseGeAuthorization(GeAuthorization geAuthorization);

    /**
     * 查询ge商机状态
     * @param geBussinessChanceId
     * @return
     */
    boolean checkBusinessChance(Integer geBussinessChanceId);
}
