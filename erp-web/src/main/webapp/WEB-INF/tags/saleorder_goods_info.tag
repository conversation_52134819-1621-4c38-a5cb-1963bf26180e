<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="saleorderGoodsList" type="java.util.List" required="true" %>
<%@attribute name="newSkuInfosMap" type="java.util.Map" required="true" %>
<%@attribute name="terminalTypes" type="java.util.List" required="true" %>
<%@attribute name="skuNoAndPriceMap" type="java.util.Map" required="true" %>
<%@attribute name="regions" type="java.util.List" required="true" %>
<%@attribute name="componentList" type="java.util.List" required="true" %>
<%@attribute name="consultUserList" type="java.util.List" required="true" %>
<%@attribute name="quoteInfo" type="com.vedeng.order.model.Quoteorder" required="true" %>
<%@attribute name="expressSeList" type="java.util.List" required="true" %>
<%@attribute name="specialDeliveryList" type="java.util.List" required="true" %>
<%@attribute name="saleorderCoupon" type="com.vedeng.order.model.SaleorderCoupon" required="true" %>
<%@attribute name="realAmount" type="java.math.BigDecimal" required="true" %>
<%@attribute name="totalReferenceCostPrice" type="java.math.BigDecimal" required="true" %>
<%@attribute name="awardAmount" type="java.math.BigDecimal" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/goods-info-table.css">
<style>
    .detail-wrap .info-pic {
        display: flex;
    }

    .detail-wrap .info-pic .info-pic-item {
        width: 50px;
        margin-right: 10px;
        height: 50px;
        border: 1px solid rgb(237, 240, 242);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }
    .goods-sku{
        display: inline-block;
        vertical-align: top;
    }

</style>
<%-- 判断是否有查看成本的权限 --%>
<c:set var="hasBuyPriceLimit" value="0" />
<%-- 判断是否含有无成本SKU --%>
<c:set var="hasOneNoBuyPrice" value="0"/>
<%-- 判断是否有退货数量 --%>
<c:set var="hasReturnNum" value="0"/>


<shiro:hasPermission name="/saleorder/order/showBuyPriceNoUrl.do">
    <%-- 判断是否有查看成本的权限 --%>
    <c:set var="hasBuyPriceLimit" value="1" />
    <input type="hidden" id="hasBuyPriceLimit" name="hasBuyPriceLimit" value="1" />
</shiro:hasPermission>
<div class="parts">
    <div class="title-container title-container-blue">
        <div class="table-title nobor">产品信息</div>
    </div>
    <table class="table  table-bordered table-striped table-condensed table-centered goods-info-table" style="min-width:1405px;">
        <colgroup>
            <col width="45px">
            <col width="280px">
            <col width="160px">
            <col width="160px">
            <col width="200px">
            <col width="160px">
            <col width="120px">
            <col width="120px">
            <col width="160px">
        </colgroup>
        <thead>
            <tr>
                <th>序号</th>
                <th>产品信息</th>
                <th>报价信息</th>
                <th>总价</th>
                <th>参考信息</th>
                <th>供应链回复</th>
                <th>产品备注</th>
                <th>内部备注</th>
                <th>交付信息</th>
            </tr>
        </thead>
        <tbody id="goodsTbody">
            <c:set var="num" value="0" />
            <c:set var="totleMoney" value="0.00" />
            <c:set var="totleMoneyForBuyPrice" value="0.00" />
            <c:set var="buyTotalPrice" value="0" />
            <c:set var="isNotDelPriceZero" value="0" />
            <c:forEach var="list" items="${saleorderGoodsList}" varStatus="staut">
                <c:if test="${list.isDelete eq 0}">
                    <c:choose>
                        <c:when test="${list.sku eq 'V127063'}">
                            <c:set var="num" value="${num + 0}" />
                        </c:when>
                        <c:otherwise>
                            <c:set var="num" value="${num + list.num - list.afterReturnNum}" />
                        </c:otherwise>
                    </c:choose>
                <!-- 总额计算方式变更 2018-12-27 产品Ada 总额 = 单价*(订单商品数量-订单商品售后数量) -->
                <c:set var="totleMoney" value="${totleMoney + (list.price * (list.num - list.afterReturnNum))}" />
                <c:set var="buyTotalPrice" value="${buyTotalPrice + ( (list.maoLiBuyPrice==null?0:list.maoLiBuyPrice) * (list.num - list.afterReturnNum))}" />
                <c:if test="${list.price == '0.00'}">
                    <c:set var="isNotDelPriceZero" value="1" />
                </c:if>
                </c:if>
                <!-- -->
                    <tr>
                        <td class="middle goods-list-num-wrap">
                            ${staut.count}
                            <c:choose>
                                <c:when test="${list.lockedStatus eq 0}"></c:when>
                                <c:otherwise>
                                    <div class="goods-tip-wrap">
                                        <span class="vd-icon icon-password"></span>
                                        <div class="goods-tip-cnt nowrap">已锁定</div>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <!-- 第2列，产品信息 -->
                        <td>
                            <div class="goods-info-wrap">
                                <div class="info-pic">
                                    <div class="info-pic-inner addtitle" tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":""}'>
                                        <c:if test="${not empty list.imgUrl}">
                                            <img src="${list.imgUrl}" onerror="this.src='${pageContext.request.contextPath}/static/images/prod-img-error.png'">
                                        </c:if>
                                        <c:if test="${empty list.imgUrl}">
                                            <img src="${pageContext.request.contextPath}/static/images/prod-img-placeholder.png" alt="">
                                        </c:if>
                                    </div>
                                    <div class="prod-tag-wrap">
                                        <c:if test="${list.isRisk > 0}">
                                            <div class="goods-tip-wrap J-fk-icon" data-sku="${list.sku}">
                                                <img src="${pageContext.request.contextPath}/static/images/goods-info-tag/tag-fk.svg" class="tag-item tag-fk" />
                                                <div class="goods-tip-cnt">
                                                    <div class="J-tips-cnt-txt" style="display: none;">
                                                        
                                                    </div>
                                                    <div class="tips-cnt-loading J-tips-cnt-loading">
                                                        <span class="vd-icon icon-loading"></span>
                                                        <span class="tips-loading-txt">加载中...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${list.isGift eq '1'}">
                                            <img id="isGift" src="<%= basePath %>static/images/goods-info-tag/tag-gift.svg" class="tag-item" title="赠品"/>
                                        </c:if>
                                        <c:if test="${list.isDirectPurchase == 1}">
                                            <img id="isDirectPurchase" src="<%= basePath %>static/images/goods-info-tag/tag-cu.svg" class="tag-item" title="商城&quot;现货现价&quot;促销"/>
                                        </c:if>
                                    </div>
                                </div>
                                <div class="info-detail" >
                                    <div class="goods-name" <c:if test="${list.isDirectPurchase == 1}">title="商城&quot;现货现价&quot;促销"</c:if>><a class="addtitle" href="javascript:void(0);"
                                                                tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":""}'>${list.goodsName}</a>
                                    </div>
                                    <div class="goods-sku">
                                        <div class="sku-txt">${list.sku}</div>
                                        <div class="goods-tip-wrap">
                                            <span class="vd-icon icon-info1"></span>
                                            <tags:saleorder_goods_purchase_tips saleorder="${saleorder}" saleorderGoods="${list}" skuDetailMap="${newSkuInfosMap[list.sku]}" />
                                        </div>
                                    </div>
                                    <div class="detail-info-field">
                                        <div class="detail-info-label">品牌：</div>
                                        <div class="detail-info-txt">${list.brandName}</div>
                                    </div>
                                    <div class="detail-info-field">
                                        <div class="detail-info-label">
                                            <c:choose>
                                                <c:when test="${list.spuType == 316 || list.spuType == 1008}">
                                                    型号
                                                </c:when>
                                                <c:when test="${list.spuType == 317 || list.spuType == 318}">
                                                    规格
                                                </c:when>
                                                <c:otherwise>
                                                    <c:choose>
                                                        <c:when test="${list.model != null && list.model != ''}">
                                                            型号
                                                        </c:when>
                                                        <c:otherwise>规格</c:otherwise>
                                                    </c:choose>
                                                </c:otherwise>
                                            </c:choose>
                                            ：</div>
                                        <div class="detail-info-txt">
                                            <c:choose>
                                                <c:when test="${list.spuType == 316 || list.spuType == 1008}">
                                                    ${list.model}
                                                </c:when>
                                                <c:when test="${list.spuType == 317 || list.spuType == 318}">
                                                    ${list.spec}
                                                </c:when>
                                                <c:otherwise>
                                                    <c:choose>
                                                        <c:when test="${list.model != null && list.model != ''}">
                                                            ${list.model}
                                                        </c:when>
                                                        <c:otherwise>${list.spec}</c:otherwise>
                                                    </c:choose>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <!-- 第3列，报价信息 -->
                        <td>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">单价：</div>
                                <div class="detail-info-txt">
                                    <div class="info-txt red">￥${list.price}</div>
                                    <c:if test= "${1 == list.isLowerGoods}" >
                                        <div class="goods-tip-wrap">
                                            <span class="vd-icon icon-caution1"></span>
                                            <div class="goods-tip-cnt nowrap">核价销售价:￥${list.checkPrice}</div>
                                        </div>
                                    </c:if>
                                </div>
                            </div>
                            <c:if test="${saleorder.orderType eq 5 || saleorder.orderType eq 1}">
                                    <%--<c:if test="${list.realPrice != list.price}">--%>
                                    <div class="detail-info-field width1">
                                        <div class="detail-info-label">原单价：</div>
                                        <div class="detail-info-txt">
                                            ￥<fmt:formatNumber type="number" value="${null == list.realPrice ? 0 : list.realPrice}" pattern="0.00" maxFractionDigits="2" />
                                        </div>
                                    </div>
                                <%--</c:if>--%>
                            </c:if>


                            <div class="detail-info-field width1">
                                <div class="detail-info-label">数量：</div>
                                <div class="detail-info-txt">
                                    <c:choose>
                                        <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                            <div class="info-txt">${list.num}${list.unitName}</div>
                                        </c:when>
                                        <c:otherwise>
                                            <%-- 将是否有退货标识的标记打上1--%>
                                            <c:set var="hasReturnNum" value="1"/>
                                            <div class="info-txt">${list.num - list.afterReturnNum}${list.unitName}</div>
                                            <div class="goods-tip-wrap">
                                                <span class="vd-icon icon-info1"></span>
                                                <div class="goods-tip-cnt nowrap">原值：${list.num}</div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">货期：</div>
                                <div class="detail-info-txt">${list.deliveryCycle}天</div>
                            </div>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">物流：</div>
                                <div class="detail-info-txt">
                                    <c:choose>
										<c:when test="${list.deliveryDirect eq 0}">普发
                                        </c:when>
                                        <c:otherwise>
                                            <div class="info-txt">直发</div>
                                            <div class="goods-tip-wrap width10">
                                                <span class="vd-icon icon-info1"></span>
                                                <div class="goods-tip-cnt">直发原因：${list.deliveryDirectComments}</div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">含安调：</div>
                                <div class="detail-info-txt">
                                    <c:choose>
                                        <c:when test="${list.haveInstallation eq 0}">否</c:when>
                                        <c:otherwise>是</c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                        </td>

                        <!-- 第4列，总价 -->
                        <td>
                            <div class="detail-info-field">
                                <c:choose>
                                    <c:when test="${saleorder.orderType eq 1}">
                                        <div class="detail-info-label">总价：</div>
                                        <%--<!- 总额计算方式变更 2018-12-27 产品Ada 总额 = 单价*(订单商品数量-订单商品售后数量)->
                                                <fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" />--%>
                                        <div class="detail-info-txt red">
                                            ￥<fmt:formatNumber type="number" value="${list.maxSkuRefundAmount - list.afterReturnAmount}" pattern="0.00" maxFractionDigits="2" />
                                        </div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="detail-info-label">总价：</div>
                                        <%--<!- 总额计算方式变更 2018-12-27 产品Ada 总额 = 单价*(订单商品数量-订单商品售后数量)->
                                                <fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" />--%>
                                        <div class="detail-info-txt red">
                                            ￥<fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" />
                                        </div>
                                    </c:otherwise>
                                </c:choose>


                            </div>

                            <c:if test="${saleorder.orderType eq 1 || saleorder.orderType eq 5}">
                                <div class="detail-info-field">
                                    <div class="detail-info-label">原总价：</div>
                                    <div class="detail-info-txt">￥<fmt:formatNumber type="number" value="${list.realPrice * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /></div>
                                </div>
                                <div class="detail-info-field">
                                    <div class="detail-info-label">优惠额：</div>
                                    <div class="detail-info-txt red">￥<fmt:formatNumber type="number" value="${(list.realPrice-list.price) * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /></div>
                                </div>
                            </c:if>

                            <%--<c:if test="${list.realPrice != list.price}">
                                <div class="detail-info-field">
                                    <div class="detail-info-label">原总价：</div>
                                    <div class="detail-info-txt">￥<fmt:formatNumber type="number" value="${list.realPrice * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /></div>
                                </div>
                                <div class="detail-info-field">
                                    <div class="detail-info-label">优惠额：</div>
                                    <div class="detail-info-txt red">￥<fmt:formatNumber type="number" value="${(list.realPrice-list.price) * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /></div>
                                </div>
                            </c:if>--%>
                            <c:if test="${hasBuyPriceLimit == 1}">

                                    <div class="detail-info-field">
                                        <div class="detail-info-label">预估毛利：</div>
                                        <!-- 这边需要判断毛利的大小，大于配置的值，加“red”,小于配置的值，加“green” -->
                                        <%--<div class="detail-info-txt red">
                                            <fmt:formatNumber type="number" value="${((list.price-list.maoLiBuyPrice)/list.price)*100}" pattern="0.0" maxFractionDigits="1" />%
                                        </div>--%>
                                        <c:choose>
                                            <c:when test="${list.maoLiBuyPrice != null && list.maoLiBuyPrice > 0 }">
                                                <c:set var="totleMoneyForBuyPrice" value="${totleMoneyForBuyPrice + (list.price * (list.num - list.afterReturnNum))}" />
                                                <div class="detail-info-txt
                                                <c:choose>
                                                    <c:when test="${list.price !=null && list.price>0 && ((list.price - list.maoLiBuyPrice) / list.price) * 100 > 13.5}">red</c:when>
                                                    <c:otherwise>green</c:otherwise>
                                                </c:choose>
                                            ">
                                                    <c:choose>
                                                        <c:when test="${list.price !=null && list.price>0}">
                                                            <fmt:formatNumber type="number" value="${(100*(list.price - list.maoLiBuyPrice) / list.price)}" pattern="0.0" maxFractionDigits="1" />%
                                                        </c:when>
                                                        <c:otherwise>0%</c:otherwise>
                                                    </c:choose>

                                                </div>
                                            </c:when>
                                            <c:otherwise>
                                                <c:set var="hasOneNoBuyPrice" value="1"/>
                                                <div class="detail-info-txt">
                                                    -
                                                </div>
                                            </c:otherwise>

                                        </c:choose>

                                    </div>
                                <c:choose>
                                <c:when test="${list.maoLiBuyPrice != null && list.maoLiBuyPrice > 0}">
                                    <div class="detail-info-field wrap">
                                        <div class="detail-info-label">${list.maoLiBuyPriceDesc}：</div>
                                        <div class="detail-info-txt">￥<fmt:formatNumber type="number" value="${ list.maoLiBuyPrice}" pattern="0.00" maxFractionDigits="2" /></div>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="detail-info-field wrap">
                                        <div class="detail-info-label">近两年无采购价参考</div>
                                        <div class="detail-info-txt"></div>
                                    </div>
                                </c:otherwise>
                                </c:choose>

                            </c:if>
                           <%-- --%>
                            <%-- <div class="customername pos_rel " style="text-align: left">
                               总额计算方式变更 2018-12-27 产品Ada 总额 = 单价*(订单商品数量-订单商品售后数量)->
                                <fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" />
                                <span>总价：</span>
                                        <fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /><br/>
                                <c:if test="${list.realPrice != list.price}">
                                    <span>原总价：</span><fmt:formatNumber type="number" value="${list.realPrice * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /><br/>
                                    <span>优惠额：</span><fmt:formatNumber type="number" value="${(list.realPrice-list.price) * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /><br/>
                                </c:if>
                                <c:if test="${hasBuyPriceLimit == 1}">
                                    <c:if test="${list.maoLiBuyPrice != null && list.maoLiBuyPrice > 0}">
                                        <span>
                                            预估毛利：<fmt:formatNumber type="number" value="${((list.price-list.maoLiBuyPrice)/list.price)*100}" pattern="0.0" maxFractionDigits="1" />%<br/>
                                        </span>
                                        <span>${list.maoLiBuyPriceDesc}: <fmt:formatNumber type="number" value="${list.maoLiBuyPrice}" pattern="0.00" maxFractionDigits="2" /></span>
                                    </c:if>
                                </c:if>
                            </div> --%>

                        </td>

                        <!-- 第5列，参考信息 -->
                        <td>
                            <tags:order_goods_consult_hejia_info_new item="${list}" skuNoAndPriceMap="${skuNoAndPriceMap}" />
                            <tags:sku_authorization_new list="${list}" terminalTypes="${terminalTypes}" regions="${regions}"/>
                        </td>

                        <!-- 第6列，供应商回复 -->
                        <td>
                            <tags:order_goods_consult_info_new item="${list}" quote="${quoteInfo}" userList="${consultUserList}"/>
                        </td>
                        <!-- 第7列，产品备注 -->
                        <td>${list.goodsComments}</td>

                        <!-- 第8列，内部备注 -->
                        <td skuId="${list.goodsId}">

                                    ${list.insideComments}


                           <%-- <div class="no_remark_error" style="display: none;">
                                <span style="color:red;">请设置订单要求，点击编辑进行内部备注修改</span>
                            </div>--%>
                        </td>
                        <!-- 第9列，交付信息 -->
                        <td class="text-left">
                            <div class="detail-info-field width3">
                                <div class="detail-info-label">采购状态：</div>
                                <div class="detail-info-txt">
                                    <div class="info-txt">
                                        <c:choose>
                                            <c:when test="${list.bindedBuyOrder eq 2}">
                                                <c:if test="${list.buyorderStatus eq 0}">
                                                    <span>未采购</span>
                                                </c:if>
                                                <c:if test="${list.buyorderStatus ne 0}">
                                                    <span>已采购</span>
                                                </c:if>
                                                </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span id="cgztStr_${list.saleorderGoodsId}"></span>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                    <div class="goods-tip-wrap width10">
                                        <span class="vd-icon icon-info1"></span>
                                        <div class="goods-tip-cnt right">
                                            销售要求<br>
                                            采购要求：
                                            <c:forEach var="component" items="${componentList}" >
                                                <c:choose>
                                                    <c:when test="${component.skuNo == list.sku}">
                                                        <c:if test="${component.parentId eq 1}">
                                                            ${component.name}<br>
                                                            <c:if test="${component.time!=null && component.name!='立即采购'}">
                                                                预计日期：${component.timeDate}<br> 原因：${component.reason}<br>
                                                            </c:if>
                                                        </c:if>
                                                    </c:when>
                                                </c:choose>
                                            </c:forEach>
                                            供应商要求：
                                            <c:forEach var="component" items="${componentList}" >
                                                <c:choose>
                                                    <c:when test="${component.skuNo == list.sku}">
                                                        <c:if test="${component.parentId eq 4}">
                                                            ${component.name}
                                                        </c:if>
                                                    </c:when>
                                                </c:choose>
                                            </c:forEach>
                                            <br>
                                            首次满足可采购的时间：<date:date value="${saleorder.satisfyDeliveryTime}" format="yyyy-MM-dd HH:mm:ss"/> <br>
                                            <c:if test="${list.bindedBuyOrder eq 0 && (list.buyProcessModTimeString != '' && list.buyProcessModTimeString !=null)}">
                                                <br>
                                                <b>采购回复</b><br>
                                                采购对接人：${list.buyDockUserName}<br>
                                                采购进程：${list.buyOrderDemand}
                                                <c:if test="${list.buyProcessModReson !='' && list.buyProcessModReson !=null}">
                                                    (${list.buyProcessModReson})
                                                </c:if><br>
                                                状态更新时间：${list.buyProcessModTimeString}<br>
                                            </c:if>
                                            <c:if test="${list.bindedBuyOrder eq 1}">
                                                <br>
                                                <b>采购回复</b><br>
                                                采购对接人：${list.buyDockUserName}<br>
                                                采购进程：${list.buyOrderDemand}<br>
                                                状态更新时间：${list.buyProcessModTimeString}<br>
                                                采购单号：${list.bindBuyOrderId}<br>
                                                采购预计发货日：${list.sendGoodTimeString}<br>
                                                采购预计到货日：${list.receiveGoodTimeString}<br>
                                            </c:if>
                                            <c:if test="${list.bindedBuyOrder eq 2 && list.buyorderStatus ne 0}">
                                                <br>
                                                <b>采购回复</b><br>
                                                采购对接人：${list.buyDockUserName}<br>
                                                采购进程：${list.buyOrderDemand}<br>
                                                状态更新时间：${list.buyProcessModTimeString}<br>
                                                采购单号：${list.bindBuyOrderId}<br>
                                            </c:if>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-info-field width3">
                                <div class="detail-info-label">发货数量：</div>
                                <div class="detail-info-txt">
                                    <div class="info-txt">
                                        <c:forEach var="express" items="${expressSeList}" varStatus="staut">
                                            <c:if test="${list.saleorderGoodsId eq express.saleOrderGoodsId}">${express.sendNum}/
                                                <c:choose>
                                                    <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                                        ${list.num}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${list.num - list.afterReturnNum}
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:if>
                                        </c:forEach>
                                    </div>
                                    <div class="goods-tip-wrap width10">
                                        <span class="vd-icon icon-info1"></span>
                                        <div class="goods-tip-cnt right width12">
                                            <c:forEach var="special" items="${specialDeliveryList}">
                                                <c:if test="${special.saleorderGoodsId == list.saleorderGoodsId}">
                                                    <div class="tip-cnt-title">发货要求</div>
                                                    <div class="tip-cnt-item">
                                                        <div class="tip-item-label">发货要求：</div>
                                                        <div class="tip-item-txt">${special.deliveryClaimStr}<c:if test="${special.deliveryClaim eq 1}">/${special.deliveryDelayTimeStr}</c:if></div>
                                                    </div>
                                                    <div class="tip-cnt-item">
                                                        <div class="tip-item-label">专向发货：</div>
                                                        <div class="tip-item-txt">${special.isSpecialDeliveryStr}</div>
                                                    </div>
                                                    <div class="tip-cnt-item">
                                                        <div class="tip-item-label">专向采购单号：</div>
                                                        <div class="tip-item-txt">${special.buyorderNoStr}</div>
                                                    </div>
                                                    <c:if test="${special.isExpediting eq 1}">
                                                        <div class="tip-cnt-title">发货要求</div>
                                                        <div class="tip-cnt-item">
                                                            <div class="tip-item-label">催货预警：</div>
                                                            <div class="tip-item-txt">${special.taskStatusStr}</div>
                                                        </div>
                                                        <div class="tip-cnt-item">
                                                            <div class="tip-item-label">催货跟进次数：</div>
                                                            <div class="tip-item-txt">${special.followUpNum}</div>
                                                        </div>
                                                        <div class="tip-cnt-item">
                                                            <div class="tip-item-label">催货跟进内容：</div>
                                                            <div class="tip-item-txt">${special.followUpComment}</div>
                                                        </div>
                                                    </c:if>
                                                    <!-- &nbsp&nbsp发货要求： ${special.deliveryClaimStr}<c:if test="${special.deliveryClaim eq 1}">/${special.deliveryDelayTimeStr}</c:if><br>
                                                    &nbsp&nbsp专向发货：${special.isSpecialDeliveryStr}<br>
                                                    &nbsp&nbsp专向采购单号：${special.buyorderNoStr}<br><br>
                                                    <c:if test="${special.isExpediting eq 1}">
                                                        <p style="font-weight:bold;font-size:larger" >发货信息:</p>
                                                        &nbsp&nbsp催货预警：${special.taskStatusStr}<br>
                                                        &nbsp&nbsp催货跟进次数：${special.followUpNum}<br>
                                                        &nbsp&nbsp催货跟进内容：${special.followUpComment}<br><br>
                                                    </c:if> -->
                                                </c:if>
                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-info-field width3">
                                <div class="detail-info-label">收货数量：</div>
                                <div class="detail-info-txt">
                                    <c:forEach var="express" items="${expressSeList}" varStatus="staut">
                                        <c:if test="${list.saleorderGoodsId eq express.saleOrderGoodsId}">${express.arriveNum}/
                                            <c:choose>
                                                <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                                    ${list.num}
                                                </c:when>
                                                <c:otherwise>
                                                    ${list.num - list.afterReturnNum}
                                                </c:otherwise>
                                            </c:choose>
                                        </c:if>
                                    </c:forEach>
                                </div>
                            </div>

                            <div class="detail-info-field width3">
                                <div class="detail-info-label">已开票数量：</div>
                                <div class="detail-info-txt">
                                    ${list.invoicedNum}/${list.appliedNum }
                                </div>
                            </div>


                            <!-- 采购状态：
                            <c:choose>
                                <c:when test="${list.bindedBuyOrder eq 2}">
                                    <c:if test="${list.buyorderStatus eq 0}">
                                        <span>未采购</span>
                                    </c:if>
                                    <c:if test="${list.buyorderStatus ne 0}">
                                        <span>已采购</span>
                                    </c:if>
                                    </span>
                                </c:when>
                                <c:otherwise>
                            <span id="cgztStr_${list.saleorderGoodsId}"></span>
                                </c:otherwise>
                            </c:choose>
                            <span class="customername pos_rel">
                                <i class="iconbluemouth contorlIcon"></i>
                                <div class="pos_abs customernameshow mouthControlPos">
                                    销售要求<br>
                                    采购要求：
                                    <c:forEach var="component" items="${componentList}" >
                                        <c:choose>
                                            <c:when test="${component.skuNo == list.sku}">
                                                <c:if test="${component.parentId eq 1}">
                                                    ${component.name}<br>
                                                    <c:if test="${component.time!=null && component.name!='立即采购'}">
                                                        预计日期：${component.timeDate}<br> 原因：${component.reason}<br>
                                                    </c:if>
                                                </c:if>
                                            </c:when>
                                        </c:choose>
                                    </c:forEach>
                                    供应商要求：
                                    <c:forEach var="component" items="${componentList}" >
                                        <c:choose>
                                            <c:when test="${component.skuNo == list.sku}">
                                                <c:if test="${component.parentId eq 4}">
                                                    ${component.name}
                                                </c:if>
                                            </c:when>
                                        </c:choose>
                                    </c:forEach>
                                    <br>
                                    首次满足可采购的时间：<date:date value="${saleorder.satisfyDeliveryTime}" format="yyyy-MM-dd HH:mm:ss"/> <br>
                                    <c:if test="${list.bindedBuyOrder eq 0 && (list.buyProcessModTimeString != '' && list.buyProcessModTimeString !=null)}">
                                        <br>
                                        <b>采购回复</b><br>
                                        采购对接人：${list.buyDockUserName}<br>
                                        采购进程：${list.buyOrderDemand}
                                        <c:if test="${list.buyProcessModReson !='' && list.buyProcessModReson !=null}">
                                            (${list.buyProcessModReson})
                                        </c:if><br>
                                        状态更新时间：${list.buyProcessModTimeString}<br>
                                    </c:if>
                                    <c:if test="${list.bindedBuyOrder eq 1}">
                                        <br>
                                        <b>采购回复</b><br>
                                        采购对接人：${list.buyDockUserName}<br>
                                        采购进程：${list.buyOrderDemand}<br>
                                        状态更新时间：${list.buyProcessModTimeString}<br>
                                        采购单号：${list.bindBuyOrderId}<br>
                                        采购预计发货日：${list.sendGoodTimeString}<br>
                                        采购预计到货日：${list.receiveGoodTimeString}<br>
                                    </c:if>
                                    <c:if test="${list.bindedBuyOrder eq 2 && list.buyorderStatus ne 0}">
                                        <br>
                                        <b>采购回复</b><br>
                                        采购对接人：${list.buyDockUserName}<br>
                                        采购进程：${list.buyOrderDemand}<br>
                                        状态更新时间：${list.buyProcessModTimeString}<br>
                                        采购单号：${list.bindBuyOrderId}<br>
                                    </c:if>
                                </div>
                            </span>
                            <br> -->

                            <!-- 发货数量：
                            <c:forEach var="express" items="${expressSeList}" varStatus="staut">
                                <c:if test="${list.saleorderGoodsId eq express.saleOrderGoodsId}">${express.sendNum}/
                                    <c:choose>
                                        <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                            ${list.num}
                                        </c:when>
                                        <c:otherwise>
                                            ${list.num - list.afterReturnNum}
                                        </c:otherwise>
                                    </c:choose>
                                </c:if>
                            </c:forEach>
                            <span class="customername pos_rel">
                                <i class="iconbluemouth contorlIcon"></i>
                                <div class="pos_abs customernameshow">

                                <c:forEach var="special" items="${specialDeliveryList}">
                                    <c:if test="${special.saleorderGoodsId == list.saleorderGoodsId}">
                                        <p style="font-weight:bold;font-size: larger">发货要求</p>
                                        &nbsp&nbsp发货要求： ${special.deliveryClaimStr}<c:if test="${special.deliveryClaim eq 1}">/${special.deliveryDelayTimeStr}</c:if><br>
                                        &nbsp&nbsp专向发货：${special.isSpecialDeliveryStr}<br>
                                        &nbsp&nbsp专向采购单号：${special.buyorderNoStr}<br><br>
                                        <c:if test="${special.isExpediting eq 1}">
                                            <p style="font-weight:bold;font-size:larger" >发货信息:</p>
                                            &nbsp&nbsp催货预警：${special.taskStatusStr}<br>
                                            &nbsp&nbsp催货跟进次数：${special.followUpNum}<br>
                                            &nbsp&nbsp催货跟进内容：${special.followUpComment}<br><br>
                                        </c:if>
                                    </c:if>
                                </c:forEach>
                                </div>
                            </span>
                            <br> -->

                            <!-- 收货数量：
                            <c:forEach var="express" items="${expressSeList}" varStatus="staut">
                                <c:if test="${list.saleorderGoodsId eq express.saleOrderGoodsId}">${express.arriveNum}/
                                    <c:choose>
                                        <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                            ${list.num}
                                        </c:when>
                                        <c:otherwise>
                                            ${list.num - list.afterReturnNum}
                                        </c:otherwise>
                                    </c:choose>
                                </c:if>
                            </c:forEach>
                            <br>
                            已开票数量：${list.invoicedNum}/${list.appliedNum } -->
                        </td>
                    </tr>
            </c:forEach>
        <tr class="total">
            <td colspan="9">
                <div class="goods-total-wrap">
                    <input type="hidden" value="${isNotDelPriceZero}" id="isNotDelPriceZero">
                    <div class="total-txt-item">
                        <div class="total-item-label">实际金额：</div>
                        <div class="total-item-txt red">￥<fmt:formatNumber type="number" value="${realAmount}" pattern="0.00" maxFractionDigits="2" /></div>
                        <c:if test="${hasReturnNum >0}" >
                        <div class="goods-tip-wrap">
                            <span class="vd-icon icon-info1"></span>
                            <div class="goods-tip-cnt nowrap">已扣除退货产品金额</div>
                        </div>
                        </c:if>
                    </div>
                    <div class="total-txt-item">
                        <div class="total-item-label">实际件数：</div>
                        <div class="total-item-txt">${num}</div>
                        <c:if test="${hasReturnNum >0}" >
                        <div class="goods-tip-wrap">
                            <span class="vd-icon icon-info1"></span>
                            <div class="goods-tip-cnt nowrap">已扣除退货产品数量</div>
                        </div>
                        </c:if>
                    </div>

                    <div class="total-txt-item">
                        <div class="total-item-label">优惠金额：</div>
                        <div class="total-item-txt">￥<fmt:formatNumber type="number" value="${(saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination) + (awardAmount == null? 0 : awardAmount)}" pattern="0.00" maxFractionDigits="2" /></div>
                    </div>
                    <div class="total-txt-item">
                        <div class="total-item-label">优惠前金额：</div>
                        <div class="total-item-txt">￥<fmt:formatNumber type="number" value="${saleorder.totalAmount +awardAmount + (saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination)}" pattern="0.00" maxFractionDigits="2" /></div>
                    </div>
                    <%--<div class="total-txt-item">
                        <div class="total-item-label">优惠券金额：</div>
                        <div class="total-item-txt">￥<fmt:formatNumber type="number" value="${saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination}" pattern="0.00" maxFractionDigits="2" /></div>
                    </div>
                    <c:if test="${awardAmount !=null && awardAmount > 0}">
                        <div class="total-txt-item">
                            <div class="total-item-label">随机立减金额：</div>
                            <div class="total-item-txt">￥<fmt:formatNumber type="number" value="${awardAmount}" pattern="0.00" maxFractionDigits="2" /></div>
                        </div>
                    </c:if>--%>
                    <c:if test="${hasBuyPriceLimit == 1}">
                        <c:choose >
                            <c:when test="${buyTotalPrice>0}">
                            <div class="total-txt-item">
                                <div class="total-item-label">预估采购总额：</div>
                                <div class="total-item-txt">￥${buyTotalPrice}</div>
                            </div>
                            <div class="total-txt-item">
                                <div class="total-item-label">预估毛利：</div>
                                <div class="total-item-txt">
                                    <div class="item-txt
                                            <c:choose>
                                                <c:when test="${totleMoneyForBuyPrice >0 &&  100*(totleMoneyForBuyPrice-buyTotalPrice)/totleMoneyForBuyPrice > 13.5}">red</c:when>
                                                <c:otherwise>green</c:otherwise>
                                            </c:choose>
                                        ">
                                        <c:choose>
                                            <c:when test="${totleMoneyForBuyPrice > 0}">
                                                <fmt:formatNumber type="number" value="${100*(totleMoneyForBuyPrice-buyTotalPrice)/totleMoneyForBuyPrice}" pattern="0.0" maxFractionDigits="1"/>%
                                            </c:when>
                                            <c:otherwise>0%</c:otherwise>
                                        </c:choose>

                                    </div>

                                    <c:if test="${hasOneNoBuyPrice ==1}">
                                    <div class="total-item-tip">
                                        （无成本产品不纳入计算，赠品纳入计算）
                                    </div>
                                    </c:if>
                                </div>
                            </div>
                            </c:when>
                            <c:otherwise>
                                <div class="total-txt-item">
                                    <div class="total-item-label">预估采购总额：</div>
                                    <div class="total-item-txt">-</div>
                                </div>
                                <div class="total-txt-item">
                                    <div class="total-item-label">预估毛利：</div>
                                    <div class="total-item-txt">
                                        <div class="item-txt">
                                            -
                                        </div>
                                        <div class="total-item-tip">
                                            （所有产品均无采购成本，无法计算）
                                        </div>
                                    </div>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </c:if>
                </div>


                <%-- 订单实际金额&nbsp;&nbsp;<span class="font-red"><fmt:formatNumber type="number" value="${realAmount}" pattern="0.00" maxFractionDigits="2" /></span>
                <span>总件数 &nbsp;</span><span class="font-red"> ${num}</span>
                订单总金额 &nbsp;<span class="font-red"><fmt:formatNumber type="number" value="${saleorder.totalAmount}" pattern="0.00" maxFractionDigits="2" /></span>&nbsp;&nbsp;
                优惠券金额&nbsp;<span class="font-red"><fmt:formatNumber type="number" value="${saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination}" pattern="0.00" maxFractionDigits="2" /></span>
                <c:if test="${awardAmount !=null && awardAmount > 0}">
                    ， 随机立减金额&nbsp;<span class="font-red"><fmt:formatNumber type="number" value="${awardAmount}" pattern="0.00" maxFractionDigits="2" /></span>
                </c:if>
                预估采购总额&nbsp; ${buyTotalPrice}
                预估毛利&nbsp;
                 <div class="item-txt
                                    <c:choose>
                                        <c:when test="${(totleMoney-buyTotalPrice)/totleMoney*100 > 13.5}">red</c:when>
                                        <c:otherwise>green</c:otherwise>
                                    </c:choose>
                                ">
                    <fmt:formatNumber type="number" value="${(totleMoney-buyTotalPrice)/totleMoney*100}" pattern="0.0" maxFractionDigits="1" />%
                    </div>
                    <div class="goods-tip-wrap width12">
                        <c:if test="${buyTotalPrice >0}">
                        <span class="vd-icon icon-info1"></span>
                        <div class="goods-tip-cnt">无成本SKU不纳入计算，赠品纳入计算</div>
                        </c:if>
                    </div>
             <div class="f_left inputfloat customername pos_rel">

                    <i class="iconbluesigh ml4 contorlIcon"></i>
                    <div class="pos_abs customernameshow">
                        总件数：指购买的产品总件数，不包含运费！
                    </div>
                </div>

                <c:if test="${saleorder.orderType ne 1 && saleorder.orderType ne 5}">
                    ，
                </c:if>

                <c:if test="${saleorder.orderType eq 1 || saleorder.orderType eq 5}">
                    ， 优惠前金额 <span class="font-red"><fmt:formatNumber type="number" value="${saleorder.totalAmount +awardAmount + (saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination)}" pattern="0.00" maxFractionDigits="2" /></span>
                    ，

                    ， 优惠后金额 <span class="font-red"><fmt:formatNumber type="number" value="${saleorder.totalAmount}" pattern="0.00" maxFractionDigits="2" /></span>
                </c:if>   --%>
            </td>
        </tr>
        </tbody>
    </table>
</div>

<script>
    $(
        function (){
            var saleorderId = ${saleorder.saleorderId};

            //补订单产品详情相关数据
            $.ajax({
                async:true,
                url:page_url+'/order/saleorder/getsaleordergoodsextrainfo.do',
                data:{"saleorderId":saleorderId, "extraType":"order_saleorder"},//销售订单详情（占用，库存，采购状态，到库状态，发货状态，收货状态）
                type:"POST",
                dataType : "json",
                success:function(data){
                    if(data.code==0){
                        for (var i = 0; i < data.data.length; i++) {
                            $("#orderOccupy_stockNum_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.availableStockNum+"/"+data.data[i].goods.stockNum);
                            $("#kc_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.stockNum);
                            $("#kykc_"+data.data[i].saleorderGoodsId).html((data.data[i].goods.stockNum-data.data[i].goods.orderOccupy) < 0 ? 0 : (data.data[i].goods.stockNum-data.data[i].goods.orderOccupy));
                            $("#dzzy_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.orderOccupy);
                            $("#ktj_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.adjustableNum);

                            //到库状态
                            var dkztStr = '';
                            if (data.data[i].warehouseNum == undefined || data.data[i].warehouseNum == 0) {
                                dkztStr = "未到库";
                            } else if (data.data[i].warehouseNum < data.data[i].num) {
                                dkztStr = "部分到库";
                            } else if (data.data[i].warehouseNum == data.data[i].num) {
                                dkztStr = "已到库";
                            }
                            $("#dkztStr_"+data.data[i].saleorderGoodsId).html(dkztStr);
                        }
                    }else{
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }
    )
</script>

<script>
    $(function (){
        $.ajax({
            async:true,
            url:page_url+'/orderstream/saleorder/purchaseCount.do?saleOrderId=' + ${saleorder.saleorderId},
            dataType : "json",
            success: function (data){
                if (data.code === 0){
                    for (let i = 0; i < data.data.length; i++) {
                        //采购状态
                        let cgztStr = '';
                        if (typeof (data.data[i].buyNum) == "undefined" || data.data[i].buyNum === 0) {
                            cgztStr = "未采购";
                        } else if (data.data[i].buyNum < data.data[i].num) {
                            cgztStr = "部分采购";
                        } else {
                            cgztStr = "已采购";
                        }
                        $("#cgztStr_"+data.data[i].saleorderGoodsId).html(cgztStr);
                    }
                }
            }
        })
    })
</script>