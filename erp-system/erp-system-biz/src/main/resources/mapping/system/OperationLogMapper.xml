<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.OperationLogMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.OperationLogEntity">
        <!--@mbg.generated-->
        <!--@Table T_OPERATION_LOG-->
        <id column="OPERATION_LOG_ID" jdbcType="BIGINT" property="operationLogId"/>
        <result column="BIZ_ID" jdbcType="INTEGER" property="bizId"/>
        <result column="BIZ_NAME" jdbcType="VARCHAR" property="bizName"/>
        <result column="BIZ_TYPE_ENUM" jdbcType="VARCHAR" property="bizTypeEnum"/>
        <result column="ACTION_TYPE_ENUM" jdbcType="VARCHAR" property="actionTypeEnum"/>
        <result column="LOG_PARAM" jdbcType="VARCHAR" property="logParam"/>
        <result column="LOG_CONTENT" jdbcType="VARCHAR" property="logContent"/>
        <result column="OPERATION_TIME" jdbcType="TIMESTAMP" property="operationTime"/>
        <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        OPERATION_LOG_ID,
        BIZ_ID,
        BIZ_NAME,
        BIZ_TYPE_ENUM,
        ACTION_TYPE_ENUM,
        LOG_PARAM,
        LOG_CONTENT,
        OPERATION_TIME,
        IS_DELETE,
        ADD_TIME,
        MOD_TIME,
        CREATOR,
        CREATOR_NAME,
        UPDATER,
        UPDATER_NAME,
        UPDATE_REMARK
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_OPERATION_LOG
        where OPERATION_LOG_ID = #{operationLogId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from T_OPERATION_LOG
        where OPERATION_LOG_ID = #{operationLogId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="OPERATION_LOG_ID" keyProperty="operationLogId"
            parameterType="com.vedeng.erp.system.domain.entity.OperationLogEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_OPERATION_LOG (BIZ_ID, BIZ_NAME, BIZ_TYPE_ENUM,
                                     ACTION_TYPE_ENUM, LOG_PARAM, LOG_CONTENT,
                                     OPERATION_TIME, IS_DELETE, ADD_TIME,
                                     MOD_TIME, CREATOR, CREATOR_NAME,
                                     UPDATER, UPDATER_NAME, UPDATE_REMARK)
        values (#{bizId,jdbcType=INTEGER}, #{bizName,jdbcType=VARCHAR}, #{bizTypeEnum,jdbcType=VARCHAR},
                #{actionTypeEnum,jdbcType=VARCHAR}, #{logParam,jdbcType=VARCHAR}, #{logContent,jdbcType=VARCHAR},
                #{operationTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP},
                #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
                #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="OPERATION_LOG_ID" keyProperty="operationLogId"
            parameterType="com.vedeng.erp.system.domain.entity.OperationLogEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_OPERATION_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizId != null">
                BIZ_ID,
            </if>
            <if test="bizName != null and bizName != ''">
                BIZ_NAME,
            </if>
            <if test="bizTypeEnum != null and bizTypeEnum != ''">
                BIZ_TYPE_ENUM,
            </if>
            <if test="actionTypeEnum != null and actionTypeEnum != ''">
                ACTION_TYPE_ENUM,
            </if>
            <if test="logParam != null and logParam != ''">
                LOG_PARAM,
            </if>
            <if test="logContent != null and logContent != ''">
                LOG_CONTENT,
            </if>
            <if test="operationTime != null">
                OPERATION_TIME,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                UPDATE_REMARK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizId != null">
                #{bizId,jdbcType=INTEGER},
            </if>
            <if test="bizName != null and bizName != ''">
                #{bizName,jdbcType=VARCHAR},
            </if>
            <if test="bizTypeEnum != null and bizTypeEnum != ''">
                #{bizTypeEnum,jdbcType=VARCHAR},
            </if>
            <if test="actionTypeEnum != null and actionTypeEnum != ''">
                #{actionTypeEnum,jdbcType=VARCHAR},
            </if>
            <if test="logParam != null and logParam != ''">
                #{logParam,jdbcType=VARCHAR},
            </if>
            <if test="logContent != null and logContent != ''">
                #{logContent,jdbcType=VARCHAR},
            </if>
            <if test="operationTime != null">
                #{operationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                #{updateRemark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.OperationLogEntity">
        <!--@mbg.generated-->
        update T_OPERATION_LOG
        <set>
            <if test="bizId != null">
                BIZ_ID = #{bizId,jdbcType=INTEGER},
            </if>
            <if test="bizName != null and bizName != ''">
                BIZ_NAME = #{bizName,jdbcType=VARCHAR},
            </if>
            <if test="bizTypeEnum != null and bizTypeEnum != ''">
                BIZ_TYPE_ENUM = #{bizTypeEnum,jdbcType=VARCHAR},
            </if>
            <if test="actionTypeEnum != null and actionTypeEnum != ''">
                ACTION_TYPE_ENUM = #{actionTypeEnum,jdbcType=VARCHAR},
            </if>
            <if test="logParam != null and logParam != ''">
                LOG_PARAM = #{logParam,jdbcType=VARCHAR},
            </if>
            <if test="logContent != null and logContent != ''">
                LOG_CONTENT = #{logContent,jdbcType=VARCHAR},
            </if>
            <if test="operationTime != null">
                OPERATION_TIME = #{operationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
            </if>
        </set>
        where OPERATION_LOG_ID = #{operationLogId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.OperationLogEntity">
        <!--@mbg.generated-->
        update T_OPERATION_LOG
        set BIZ_ID           = #{bizId,jdbcType=INTEGER},
            BIZ_NAME         = #{bizName,jdbcType=VARCHAR},
            BIZ_TYPE_ENUM    = #{bizTypeEnum,jdbcType=VARCHAR},
            ACTION_TYPE_ENUM = #{actionTypeEnum,jdbcType=VARCHAR},
            LOG_PARAM        = #{logParam,jdbcType=VARCHAR},
            LOG_CONTENT      = #{logContent,jdbcType=VARCHAR},
            OPERATION_TIME   = #{operationTime,jdbcType=TIMESTAMP},
            IS_DELETE        = #{isDelete,jdbcType=BOOLEAN},
            ADD_TIME         = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME         = #{modTime,jdbcType=TIMESTAMP},
            CREATOR          = #{creator,jdbcType=INTEGER},
            CREATOR_NAME     = #{creatorName,jdbcType=VARCHAR},
            UPDATER          = #{updater,jdbcType=INTEGER},
            UPDATER_NAME     = #{updaterName,jdbcType=VARCHAR},
            UPDATE_REMARK    = #{updateRemark,jdbcType=VARCHAR}
        where OPERATION_LOG_ID = #{operationLogId,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-07-22-->
    <select id="findAllByBizIdAndBizTypeEnum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_OPERATION_LOG
        where BIZ_ID = #{bizId,jdbcType=INTEGER}
          and BIZ_TYPE_ENUM = #{bizTypeEnum,jdbcType=VARCHAR}
          and IS_DELETE = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-07-22-->
    <select id="findByAll" resultType="com.vedeng.erp.system.dto.OperationLogDto">
        select
        <include refid="Base_Column_List"/>
        from T_OPERATION_LOG
        <where>
            <if test="operationLogId != null">
                and OPERATION_LOG_ID = #{operationLogId,jdbcType=BIGINT}
            </if>
            <if test="bizId != null">
                and BIZ_ID = #{bizId,jdbcType=INTEGER}
            </if>
            <if test="bizName != null and bizName != ''">
                and BIZ_NAME = #{bizName,jdbcType=VARCHAR}
            </if>
            <if test="bizTypeEnum != null and bizTypeEnum != ''">
                and BIZ_TYPE_ENUM = #{bizTypeEnum,jdbcType=VARCHAR}
            </if>

            <if test="bizList != null and bizList.size() != 0">
                and (BIZ_TYPE_ENUM, BIZ_ID) IN
                <foreach collection="bizList" item="biz" open="(" separator="," close=")">
                    (#{biz.bizTypeEnum,jdbcType=VARCHAR}, #{biz.bizId,jdbcType=INTEGER})
                </foreach>
            </if>

            <if test="actionTypeEnum != null and actionTypeEnum != ''">
                and ACTION_TYPE_ENUM = #{actionTypeEnum,jdbcType=VARCHAR}
            </if>
            <if test="logParam != null and logParam != ''">
                and LOG_PARAM = #{logParam,jdbcType=VARCHAR}
            </if>

            <if test="quoteorderGoodsId != null and quoteorderGoodsId != ''">
                and JSON_EXTRACT(LOG_PARAM, '$.quoteorderGoodsId') ='${quoteorderGoodsId}'
            </if>
            <if test="logContent != null and logContent != ''">
                and LOG_CONTENT = #{logContent,jdbcType=VARCHAR}
            </if>
            <if test="operationTime != null">
                and OPERATION_TIME = #{operationTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isDelete != null">
                and IS_DELETE = #{isDelete,jdbcType=BOOLEAN}
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                and UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
            </if>
            <if test="addTime != null">
                and ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="creatorName != null and creatorName != ''">
                and CREATOR_NAME = #{creatorName,jdbcType=VARCHAR}
            </if>
            <if test="updaterName != null and updaterName != ''">
                and UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>