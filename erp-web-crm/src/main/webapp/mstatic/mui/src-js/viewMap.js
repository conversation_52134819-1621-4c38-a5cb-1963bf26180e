Vue.component('ui-map-view', {
    template: `<div class="vd-ui-map-wrap">
        <div class="vd-ui-map-trigger" @click="showMap">
            <slot></slot>
        <div>
        <crm-slide-dialog ref="mapDialog" title="查看地图" zindex="9999">
            <div class="ui-map-iframe-wrap">
                <iframe :src="url"></iframe>
            </div>
        </crm-slide-dialog>
    </div>`,
    props: {
        address: {
            type: String,
            default: ''
        },
        // 省市区名称
        area: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isShow: false,
            url: ''
        };
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        showMap() {
            let addressStr = this.area + this.address;
            this.url = 'https://m.amap.com/search/mapview/keywords=' + encodeURI(addressStr) + '&cluster_state=5&pagenum=1';
            this.$refs.mapDialog.show();
        }
    }
})

Vue.component('ui-location-view', {
    template: `<div class="vd-ui-map-wrap">
        <div class="vd-ui-map-trigger" @click="showMap">
            <slot></slot>
        <div>
    </div>`,
    props: {
        location: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
        };
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        showMap() {
            if(!this.location) {
                this.$message.warn('历史打卡数据，无法地图查看');
                return;
            }
            ww.openLocation({
                longitude: this.location.split(',')[0],
                latitude: this.location.split(',')[1],
                scale: 16
            })
        }
    }
})