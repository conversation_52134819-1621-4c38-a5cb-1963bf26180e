package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchAfterSalesInstallServiceRecordDetailDtoMapper {
    int deleteByPrimaryKey(Integer afterSalesServiceDetailId);

    int insert(BatchAfterSalesInstallServiceRecordDetailDto record);

    int insertOrUpdate(BatchAfterSalesInstallServiceRecordDetailDto record);

    int insertOrUpdateSelective(BatchAfterSalesInstallServiceRecordDetailDto record);

    int insertSelective(BatchAfterSalesInstallServiceRecordDetailDto record);

    BatchAfterSalesInstallServiceRecordDetailDto selectByPrimaryKey(Integer afterSalesServiceDetailId);

    int updateByPrimaryKeySelective(BatchAfterSalesInstallServiceRecordDetailDto record);

    int updateByPrimaryKey(BatchAfterSalesInstallServiceRecordDetailDto record);

    int updateBatch(List<BatchAfterSalesInstallServiceRecordDetailDto> list);

    int updateBatchSelective(List<BatchAfterSalesInstallServiceRecordDetailDto> list);

    int batchInsert(@Param("list") List<BatchAfterSalesInstallServiceRecordDetailDto> list);

    List<BatchAfterSalesInstallServiceRecordDetailDto> findByAddTime(BatchAfterSalesInstallServiceRecordDetailDto record);


}