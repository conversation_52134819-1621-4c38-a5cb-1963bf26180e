package com.vedeng.crm.business.quote.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.vedeng.crm.business.quote.service.impl.ImageConverter;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ContentRowHeight(40)
public class QuoteOrderGoodsCommonExportVo {
//
//    /**
//     * 产品需求
//     */
//    @ExcelProperty(value = "需求产品")
//    @ColumnWidth(20)
//    @ExcelIgnore
//    private String productNeeds;
//
//    /**
//     * 数量需求
//     */
//    @ExcelProperty(value = "需求数量")
//    @ColumnWidth(20)
//    @ExcelIgnore
//    private String numNeeds;
//
//    /**
//     * 需求预算
//     */
//    @ExcelProperty(value = "需求预算")
//    @ColumnWidth(20)
//    @ExcelIgnore
//    private String budgetaryNeeds;
//
//    /**
//     * 需求备注
//     */
//    @ExcelProperty(value = "需求备注")
//    @ColumnWidth(20)
//    @ExcelIgnore
//    private String extraNeeds;

    /**
     * 订货号
     */
    @ExcelProperty(value = "订货号")
    @ColumnWidth(15)
    private String skuNo;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    @ColumnWidth(40)
    private String skuName;

    /**
     * 图片 设置宽高
     */
    @ExcelProperty(value = "图片", converter = ImageConverter.class)
    @ColumnWidth(10)
    private String picture;

    /**
     * 品牌
     */
    @ExcelProperty(value = "品牌")
    @ColumnWidth(20)
    private String brandName;

    /**
     * 型号
     */
    @ExcelProperty(value = "型号")
    @ColumnWidth(20)
    private String model;

    /**
     * 主要参数
     */
    @ExcelProperty(value = "主要参数")
    @ColumnWidth(40)
    private String mainParameter;

    /**
     * 使用年限/效期
     */
    @ExcelProperty(value = "使用年限/效期")
    @ColumnWidth(20)
    private String useLife;

    /**
     * 预计货期
     */
    @ExcelProperty(value = "预计货期")
    @ColumnWidth(20)
    private String expectDeliveryTime;

    /**
     * 销售单价
     */
    @ExcelProperty(value = "销售单价")
    @ColumnWidth(20)
    private BigDecimal salePrice;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    @ColumnWidth(20)
    private Integer num;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    @ColumnWidth(20)
    private String unitName;

    /**
     * 归属产品经理&助理
     */
    @ExcelProperty(value = "产品负责")
    @ColumnWidth(20)
    private String productManager;

    /**
     * 产品是否可安装 1-是 0-否 null展示-
     */
    @ExcelProperty(value = "是否可安装")
    @ColumnWidth(20)
    private String isInstall;

    @ExcelIgnore
    private Integer skuId;
}
