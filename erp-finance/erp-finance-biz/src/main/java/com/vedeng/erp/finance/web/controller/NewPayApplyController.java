package com.vedeng.erp.finance.web.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 付款申请
 */
@RequestMapping("/pay/payApply")
@Controller
@Slf4j
public class NewPayApplyController extends BaseController {


    /**
     * 付款配置
     */
    @RequestMapping(value = "/allocation")
    @ExcludeAuthorization
    public ModelAndView allocation() {
        return new ModelAndView("vue/view/finance/pay_apply_allocation");
    }

    @RequestMapping(value = "/list")
    public ModelAndView list(InvoiceRedConfirmationDto param) {
        ModelAndView mv = new ModelAndView("vue/view/finance/pay_apply_list");
        mv.addObject("viewData", param);
        return mv;
    }
}
