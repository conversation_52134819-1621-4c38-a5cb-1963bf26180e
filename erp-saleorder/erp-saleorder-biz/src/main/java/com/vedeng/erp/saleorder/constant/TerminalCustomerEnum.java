package com.vedeng.erp.saleorder.constant;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.constant
 * @Date 2023/9/7 10:08
 */
public enum TerminalCustomerEnum {

    /**
     * 默认值
     */
    DEFAULT("", "其他"),

    ZERO("0", "一级医院"),

    ONE("1", "二级医院"),

    TWO("2", "三级医院"),

    THREE("3", "未定级医院"),

    FOUR("4", "社区卫生服务中心（站）"),

    FIVE("5", "乡镇卫生院"),

    SIX("6", "诊所（医务室）"),

    SEVEN("7", "村卫生室"),

    EIGHT("8", "应急三级医院"),

    NINE("9", "应急二级医院"),

    TEN("10", "应急基层医疗");

    /**
     * 枚举值
     */
    private final String value;

    /**
     * label
     */
    private final String label;

    TerminalCustomerEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static String getLabel(String value) {
        return Arrays.stream(TerminalCustomerEnum.values())
                .filter(enums -> enums.getValue().equals(value))
                .findFirst().orElse(TerminalCustomerEnum.DEFAULT).getLabel();
    }
}
