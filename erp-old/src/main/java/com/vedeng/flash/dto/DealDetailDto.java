package com.vedeng.flash.dto;

import com.vedeng.common.page.Page;
import com.vedeng.order.model.BuyorderGoods;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 处理详情界面Dto
 * @date 2021/5/27 19:46
 */
@Data
public class DealDetailDto {
    private PrepareStockDto prepareStockDto;
    private Page page;
    private List<RegularOperateLogDto> prepareLog;
    private List<RegularOperateLogDto> doNotPrepareLog;
    private List<RegularOperateLogDto> repalceLog;
    private List<RegularOperateLogDto> conversionLog;
    private List<RegularOperateLogDto> deleteLog;
}
