package com.wms.service.validator;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.wms.model.dto.ValidatorResult;
import com.wms.service.CancelTypeService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.listenner.PutSaleAfterReturnFinshLister;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采购换货关闭校验器
 */
@Service
public class PurchaseExgCloseValidator extends AbstractValidator {

    public static Logger LOGGER = LoggerFactory.getLogger(PutSaleAfterReturnFinshLister.class);

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private CancelTypeService cancelTypeService;

    public static final Integer EFFECT = 1;

    @Override
    protected boolean needToValidate(Object[] requestData) {

        AfterSalesVo afterSalesVo = (AfterSalesVo) requestData[0];

        return afterSalesVo.getSubjectType() == 536 && afterSalesVo.getType() == 547 && afterSalesVo.getValidStatus() == EFFECT;
    }

    @Override
    protected ValidatorResult doValidator(Object[] requestData) throws Exception {


        AfterSalesVo afterSaleDB = (AfterSalesVo) requestData[0];
        LOGGER.info("采购换货单关闭，请求WMS查看是否能关,start,afterSaleDBNO:{}",afterSaleDB.getAfterSalesNo());

        /*//取消订单参数对象
        CancelPoDto cancelPoDto = new CancelPoDto();
        cancelPoDto.setDocNo(afterSaleDB.getAfterSalesNo());
        cancelPoDto.setPoType(WmsInterfaceOrderType.EXG_PURCHASE);
        cancelPoDto.setErpCancelReason(afterSaleDB.getReasonName());

        //撤销入库单 TODO Holiis ok
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_PO);
        WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);*/

        //撤销入库单 如果不能取消,返回提示消息
        if(!cancelTypeService.cancelExgPurchaseMethod(afterSaleDB.getAfterSalesNo(),"采购换货单关闭，请求WMS查看是否能关")){
            return ValidatorResult.newBuild().setMessage("物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
        }

        return ValidatorResult.newBuild().setResult(true);
    }
}
