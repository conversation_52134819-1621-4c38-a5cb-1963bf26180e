package com.vedeng.erp.aftersale.service;

import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.aftersale.dto.ReturnInvoiceWriteBackDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 采购费用售后
 * @date 2022/10/28 9:24
 **/
public interface ExpenseAfterSalesApiService {

    /**
     * 因为作废票引起的 售后退票 完结
     *
     * @param invoiceIds 票id
     */
    void doExpenseAfterSalesInvoiceStatusByInvalid(List<Integer> invoiceIds);

    /**
     * 根据费用单售后id查询费用单的主表和状态信息
     *
     * @param expenseAfterSalesId
     * @return
     */
    ExpenseAfterSalesDto getAfterSalesAndStatusInfo(Long expenseAfterSalesId);

    /**
     * 发起采购费用售后单审核
     *
     * @param expenseAfterSalesId 采购费用售后单id
     */
    int doAudit(Long expenseAfterSalesId);

    /**
     * 采购费用单售后审核 通过
     *
     * @param expenseAfterSalesId 采购费用售后单id
     */
    int audit(Long expenseAfterSalesId);

    /**
     * 采购费用单售后审核 不通过
     *
     * @param expenseAfterSalesId 采购费用售后单id
     */
    int unAudit(Long expenseAfterSalesId);

    /**
     * 冲销后状态回写 票回写
     *
     * @param returnInvoiceWriteBackDto
     * @return 影响行数
     */
    Integer updateReversalInvoiceGoodsData(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto);

    /**
     * 根据采购费用售后单号查询采购费用售后单信息
     *
     * @return
     * <AUTHOR>
     */
    ExpenseAfterSalesDto queryExpenseAfterSalesInfoByNo(String expenseAfterSalesNo);

    /**
     * 计算并更新 采购费用售后单的 退票状态
     *
     * @param expenseAfterSalesId 费用售后单id
     */
    void calculateAndUpdateInvoiceReturnStatus(Long expenseAfterSalesId);

    /**
     * 校验费用售后单 是否满足完结 （自动完结和点击按钮手动完结都调用这个接口）
     *
     * @param expenseAfterSalesId 费用售后单id
     */
    void completeExpenseAfterSales(Long expenseAfterSalesId);

    /**
     * 更新付款状态
     * @param expenseAfterSalesId 费用售后id
     */
    void updatePayStatus(Long expenseAfterSalesId);


    void updateReturnInvoiceStatus(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto);

    /**
     * 获取所有已完结售后的退款金额
     * @param buyOrderExpenseId
     * @return
     */
    BigDecimal getRefundMoney(Integer buyOrderExpenseId);

    /**
     * 采销联动退货采购费用订单，退货预警
     * @param afterSalesId
     */
    List<BuyorderExpenseDto> expenseReturnAndWarning(Integer afterSalesId);

    /**
     * 预警解除
     * @param afterSalesId
     */
    void releaseReturnEarlyWarn(Integer afterSalesId);
}
