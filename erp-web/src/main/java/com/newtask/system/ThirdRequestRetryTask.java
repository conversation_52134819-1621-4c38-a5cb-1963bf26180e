package com.newtask.system;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.utils.HttpClientUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.erp.system.common.constants.ThirdRequestLogConstant;
import com.vedeng.erp.system.dto.ThirdRequestLogDto;
import com.vedeng.erp.system.service.ThirdRequestLogApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/16 15:29
 */
@JobHandler(value = "ThirdRequestRetryTask")
@Component
@Slf4j
public class ThirdRequestRetryTask extends AbstractJobHandler {

    @Autowired
    private ThirdRequestLogApiService thirdRequestLogApiService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        List<ThirdRequestLogDto> thirdRequestLogDtoList;
        if (!StringUtils.isBlank(s)) {
            HashMap jsonMap = JSONObject.parseObject(s, HashMap.class);
            thirdRequestLogDtoList = thirdRequestLogApiService.getRetryRequest(Integer.valueOf(jsonMap.get("retryStatus").toString()), Integer.valueOf(jsonMap.get("retryTimes").toString()));
        } else {
            thirdRequestLogDtoList = thirdRequestLogApiService.getRetryRequest(0, 6);
        }
        if (CollectionUtils.isNotEmpty(thirdRequestLogDtoList)) {
            for (ThirdRequestLogDto requestLogDto : thirdRequestLogDtoList) {
                log.info("正在处理:{}的接口重处理", requestLogDto.getUrl());
                // 处理POST
                if (ThirdRequestLogConstant.REQUEST_TYPE_POST.equals(requestLogDto.getRequestType())) {
                    net.sf.json.JSONObject result = NewHttpClientUtils.httpPost(requestLogDto.getUrl(), requestLogDto.getRequestParam());
                    if (Objects.isNull(result) || !result.getBoolean(ThirdRequestLogConstant.RESPONSE_SUCCESS)) {
                        // 重试失败
                        doRetryResultForFailure(requestLogDto);
                    } else {
                        // 成功
                        doRetryResultForSuccess(requestLogDto, result.toString());
                    }
                } else if (ThirdRequestLogConstant.REQUEST_TYPE_POST_VOICE.equals(requestLogDto.getRequestType())) {
                    com.alibaba.fastjson.JSONObject jsonResult = HttpClientUtil.httpPost(requestLogDto.getUrl(),requestLogDto.getRequestParam());
                    if(jsonResult == null || !jsonResult.getBoolean("success")){
                        doRetryResultForFailure(requestLogDto);
                    }else{

                        doRetryResultForSuccess(requestLogDto,  jsonResult.toJSONString());
                    }
                }
            }
        }
        return SUCCESS;
    }

    private void doRetryResultForSuccess(ThirdRequestLogDto thirdRequestLogDto, String result) {
        log.info("Post请求重试成功, 日志id：{}", thirdRequestLogDto.getThirdRequestLogId());
        thirdRequestLogDto.setRetryStatus(1);
        thirdRequestLogDto.setLatestResponse(result);
        thirdRequestLogDto.setLatestRetryTime(new Date());
        thirdRequestLogDto.setRetryTimes(thirdRequestLogDto.getRetryTimes() + 1);
        thirdRequestLogApiService.updateLog(thirdRequestLogDto);
    }

    private void doRetryResultForFailure(ThirdRequestLogDto thirdRequestLogDto) {
        log.info("Post请求重试失败, 日志id：{}", thirdRequestLogDto.getThirdRequestLogId());
        thirdRequestLogDto.setLatestRetryTime(new Date());
        thirdRequestLogDto.setRetryTimes(thirdRequestLogDto.getRetryTimes() + 1);
        thirdRequestLogApiService.updateLog(thirdRequestLogDto);
    }

}
