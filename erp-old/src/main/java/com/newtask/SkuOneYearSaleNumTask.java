package com.newtask;

import com.newtask.model.SkuSaleNum;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.page.Page;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JobHandler(value = "skuOneYearSaleNumHandler")
@Component
public class SkuOneYearSaleNumTask extends AbstractJobHandler {

    @Autowired
    private CoreSkuMapper coreSkuMapper;
    @Autowired
    private SaleorderMapper saleorderMapper;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        coreSkuMapper.initOneYearSaleNum();
        int currentPage=1;
        Page page=new Page(currentPage,500);
        Map<String,Object> pageMap=new HashMap<>();
        pageMap.put("page",page);
        do{
            XxlJobLogger.log("正在同步第"+currentPage+"页");
            //    VDERP-5595 近一年成交数据定时任务修改
            List<SkuSaleNum> list=saleorderMapper.getOneYearSkuSaleNumListPage(pageMap);
            if(CollectionUtils.isNotEmpty(list)){
                list.forEach(item->{
                    coreSkuMapper.updateOneYearSaleNum(item);
                });
            }
            currentPage++;
            page.setPageNo(currentPage);
        }while (currentPage<=page.getTotalPage());
        return ReturnT.SUCCESS;
    }
}
