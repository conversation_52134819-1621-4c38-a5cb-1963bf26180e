package com.vedeng.infrastructure.kingdee.common;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.*;
import com.vedeng.infrastructure.kingdee.domain.command.BatchSaveExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeFileDataDto;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Recover;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶调用api异常重试最终处理类
 * @date 2022/9/20 19:51
 */
@Slf4j
@Component
public class KingDeeRecoverHandle {

    private static final String ERROR_MSG = "调用超过3次异常,调用金蝶接口异常，参数[{},{}]";


    @Recover
    public RepoStatus save(KingDeeException e, SaveExtCommand<?> command) {
        log.error(ERROR_MSG, JSON.toJSONString(command), e);
        RepoStatus repoStatus = new RepoStatus();
        repoStatus.setIsSuccess(false);
        return repoStatus;
    }

    @Recover
    public ArrayList<SuccessEntity> batchSave(KingDeeException e, BatchSaveExtCommand<?> command) {
        log.error(ERROR_MSG, JSON.toJSONString(command), e);
        return new ArrayList<>();
    }

    @Recover
    public RepoStatus update(KingDeeException e, UpdateExtCommand<?> command) {
        log.error(ERROR_MSG, JSON.toJSONString(command), e);
        RepoStatus repoStatus = new RepoStatus();
        repoStatus.setIsSuccess(false);
        return repoStatus;
    }

    @Recover
    public ArrayList<SuccessEntity> audit(KingDeeException e, OperateExtCommand command) {
        log.error(ERROR_MSG, JSON.toJSONString(command), e);
        return new ArrayList<>();
    }

    @Recover
    public ArrayList<SuccessEntity> delete(KingDeeException e, OperateExtCommand command) {
        log.error(ERROR_MSG, JSON.toJSONString(command), e);
        return new ArrayList<>();
    }

    @Recover
    public ArrayList<SuccessEntity> unAudit(KingDeeException e, OperateExtCommand command) {
        log.error(ERROR_MSG, JSON.toJSONString(command), e);
        return new ArrayList<>();
    }

    @Recover
    public List<Map<String, Object>> query(KingDeeException e, KingDeeQueryExtParam param) {
        log.error(ERROR_MSG, JSON.toJSONString(param), e);
        return new ArrayList<>();
    }

    @Recover
    public ByteArrayInputStream downloadFile(KingDeeException e, KingDeeFileDataDto param) {
        log.error(ERROR_MSG, JSON.toJSONString(param), e);
        return null;
    }

    @Recover
    public RepoRet attachmentUpload(KingDeeException e, Object param) {
        log.error(ERROR_MSG, JSON.toJSONString(param), e);
        return new RepoRet();
    }

    @Recover
    public ArrayList<SuccessEntity> unAudit(KingDeeException e, String formId, OperateParam command) {
        log.error(ERROR_MSG, JSON.toJSONString(command),formId, e);
        return new ArrayList<>();
    }


    @Recover
    public ArrayList<SuccessEntity> delete(KingDeeException e,String formId,  OperateParam command) {
        log.error(ERROR_MSG, JSON.toJSONString(command),formId, e);
        return new ArrayList<>();
    }

}
