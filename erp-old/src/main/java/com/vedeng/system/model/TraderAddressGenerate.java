package com.vedeng.system.model;

public class TraderAddressGenerate {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.TRADER_ADDRESS_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer traderAddressId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.TRADER_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer traderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.TRADER_TYPE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer traderType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.IS_ENABLE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer isEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.AREA_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer areaId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.AREA_IDS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private String areaIds;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private String address;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.IS_DEFAULT
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer isDefault;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.ZIP_CODE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private String zipCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.COMMENTS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private String comments;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.ADD_TIME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Long addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.CREATOR
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.MOD_TIME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Long modTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_ADDRESS.UPDATER
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer updater;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.TRADER_ADDRESS_ID
     *
     * @return the value of T_TRADER_ADDRESS.TRADER_ADDRESS_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getTraderAddressId() {
        return traderAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.TRADER_ADDRESS_ID
     *
     * @param traderAddressId the value for T_TRADER_ADDRESS.TRADER_ADDRESS_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setTraderAddressId(Integer traderAddressId) {
        this.traderAddressId = traderAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.TRADER_ID
     *
     * @return the value of T_TRADER_ADDRESS.TRADER_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getTraderId() {
        return traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.TRADER_ID
     *
     * @param traderId the value for T_TRADER_ADDRESS.TRADER_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.TRADER_TYPE
     *
     * @return the value of T_TRADER_ADDRESS.TRADER_TYPE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getTraderType() {
        return traderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.TRADER_TYPE
     *
     * @param traderType the value for T_TRADER_ADDRESS.TRADER_TYPE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setTraderType(Integer traderType) {
        this.traderType = traderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.IS_ENABLE
     *
     * @return the value of T_TRADER_ADDRESS.IS_ENABLE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getIsEnable() {
        return isEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.IS_ENABLE
     *
     * @param isEnable the value for T_TRADER_ADDRESS.IS_ENABLE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.AREA_ID
     *
     * @return the value of T_TRADER_ADDRESS.AREA_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getAreaId() {
        return areaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.AREA_ID
     *
     * @param areaId the value for T_TRADER_ADDRESS.AREA_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.AREA_IDS
     *
     * @return the value of T_TRADER_ADDRESS.AREA_IDS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public String getAreaIds() {
        return areaIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.AREA_IDS
     *
     * @param areaIds the value for T_TRADER_ADDRESS.AREA_IDS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setAreaIds(String areaIds) {
        this.areaIds = areaIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.ADDRESS
     *
     * @return the value of T_TRADER_ADDRESS.ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public String getAddress() {
        return address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.ADDRESS
     *
     * @param address the value for T_TRADER_ADDRESS.ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.IS_DEFAULT
     *
     * @return the value of T_TRADER_ADDRESS.IS_DEFAULT
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getIsDefault() {
        return isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.IS_DEFAULT
     *
     * @param isDefault the value for T_TRADER_ADDRESS.IS_DEFAULT
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.ZIP_CODE
     *
     * @return the value of T_TRADER_ADDRESS.ZIP_CODE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.ZIP_CODE
     *
     * @param zipCode the value for T_TRADER_ADDRESS.ZIP_CODE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.COMMENTS
     *
     * @return the value of T_TRADER_ADDRESS.COMMENTS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public String getComments() {
        return comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.COMMENTS
     *
     * @param comments the value for T_TRADER_ADDRESS.COMMENTS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setComments(String comments) {
        this.comments = comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.ADD_TIME
     *
     * @return the value of T_TRADER_ADDRESS.ADD_TIME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.ADD_TIME
     *
     * @param addTime the value for T_TRADER_ADDRESS.ADD_TIME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.CREATOR
     *
     * @return the value of T_TRADER_ADDRESS.CREATOR
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.CREATOR
     *
     * @param creator the value for T_TRADER_ADDRESS.CREATOR
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.MOD_TIME
     *
     * @return the value of T_TRADER_ADDRESS.MOD_TIME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.MOD_TIME
     *
     * @param modTime the value for T_TRADER_ADDRESS.MOD_TIME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_ADDRESS.UPDATER
     *
     * @return the value of T_TRADER_ADDRESS.UPDATER
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_ADDRESS.UPDATER
     *
     * @param updater the value for T_TRADER_ADDRESS.UPDATER
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}