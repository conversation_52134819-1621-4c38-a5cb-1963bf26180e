package com.vedeng.trader.service;

import javax.servlet.http.HttpSession;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.BaseService;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.dto.CallingLineDto;

import java.util.List;
import java.util.Map;

/**
 * <b>Description:</b><br> 沟通记录
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.trader.service
 * <br><b>ClassName:</b> CommunicateService
 * <br><b>Date:</b> 2017年7月7日 上午9:57:54
 */
public interface CommunicateService extends BaseService {
	/**
	 * <b>Description:</b><br> 更新历史沟通（处理状态）需传入沟通类型+关联主表ID
	 * @param communicateRecord 
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年7月7日 上午9:52:41
	 */
	Integer updateCommunicateDone(CommunicateRecord communicateRecord,HttpSession session);
	
	/**
	 * <b>Description:</b><br> 新增沟通
	 * @param communicateRecord
	 * @param session
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年7月13日 下午1:26:49
	 */
	Integer addCommunicate(CommunicateRecord communicateRecord,HttpSession session);

    Integer countTtNumberCountToday(String ttNumber);

    void addCallCenterCommunicate(CommunicateRecord communicateRecord, Integer loginUserId);

	/**
	 * 获取配置时间内客户沟通次数map（超过120s）
	 * 没有的需要返回0
	 * @param customerIds
	 * @param communicationDays
	 * @return
	 */
	Map<Integer, Integer> getTraderCommunicateCountByRecentDaysMap(List<Integer> customerIds, Integer communicationDays);

	/**
	 * 条件判断沟通记录是否保存
	 * @param ccId
	 * @param userId
	 * @return
	 */
	boolean isCommunicateSavedByCondition(String ccId, Integer userId);

	/**
	 * 呼出主叫号码选择
	 * 返回呼出主叫号码
	 * @param phone 呼出手机号
	 * @param userId 用户ID
	 * @return
	 */
	CallingLineDto getCalloutNumber(String phone, Integer userId);

	/**
	 * 座机呼出线路二次校验
	 * 呼出线路加二次校验：所有非号码池线路 在当天呼出 同一个号码超过3次未接通并且最后一通电话未接通时统一跳转号码池线路；
	 *
	 * @param callingLine
	 * @param phone
	 * @param userId
	 * @return
	 */
	CallingLineDto callingLineSecondCheck(CallingLineDto callingLine, String phone, Integer userId);
}
