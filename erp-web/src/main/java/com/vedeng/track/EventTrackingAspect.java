package com.vedeng.track;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.annotation.EventTrackingAnnotation;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.EventTrackingEvent;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import org.apache.commons.collections.MapUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 监听事件埋点
 * @ClassName:  EventTrackingAspect
 * @author: Neil.yang
 * @date:   2024年6月3日 下午3:07:14
 * @Copyright:
 */
@Component
@Aspect
@Order(1)
public class EventTrackingAspect {
	
	/**日志*/
	private static final Logger LOG = LoggerFactory.getLogger(EventTrackingAspect.class);

	/**Map capacity*/
	private static final int MAP_CAPACITY = 32;

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    @Value("${trackLogOn:true}")
    private boolean trackLogOn;


    @Pointcut("@annotation(com.vedeng.common.trace.annotation.EventTrackingAnnotation)")
    public void eventTracking() {
        //切入点方法，空实现
    }

    /**
     * 环绕通知
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("eventTracking()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        //避免埋点引起的异常，先执行方法，埋点进行异常捕获
        Object result = joinPoint.proceed();
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            //获取注解的入参参数
            EventTrackingAnnotation annotation = method.getAnnotation(EventTrackingAnnotation.class);
            //事件埋点策略枚举
            EventTrackingEnum eventTrackingEnum = annotation.eventTrackingEnum();
            //获取业务参数
            Object[] args = joinPoint.getArgs();
            Map<String, Object> fieldsName = getFieldsName(method,args);
            //空参直接返回
            if(MapUtils.isEmpty(fieldsName)) {
                return result;
            }
            if (trackLogOn) {
                LOG.info("请求参数为:{}", JSON.toJSONString(fieldsName));
            }
            if (trackLogOn) {
                LOG.info("请求结束,controller的返回值是:{}", JSON.toJSONString(result));
            }
            //程序异常（异常不会执行此方法，示例写法，如果是接口，验证接口的返回类型，并判断接口的返回码是否异常），不记录埋点
            if(result instanceof Exception) {
                return result;
            }
            //异步处理事件埋点记录
            TrackParamsData trackParamsData = new TrackParamsData();
            trackParamsData.setTrackParams(fieldsName);
            trackParamsData.setTrackResult(result);
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            //有些数据必须在执行结束之后才能获取信息
            //定义事件入参
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            trackStrategy.track(trackParamsData);

        } catch (Exception e){
            LOG.error("埋点失败",e);
        }
        return result;
    }

    /**
     * 根据切面获取请求参数列表
     * 文件上传、Request、Response、Session 不计入参数列表，有特殊的需要自定义
     */
    private static Map<String, Object> getFieldsName(Method method,Object[] args) {
        ParameterNameDiscoverer pnd = new DefaultParameterNameDiscoverer();
        String[] parameterNames = pnd.getParameterNames(method);
        Map<String, Object> paramMap = new HashMap<>(MAP_CAPACITY);
        if (parameterNames != null) {
            for (int i = 0; i < parameterNames.length; i++) {
                if(!(args[i] instanceof MultipartFile || args[i] instanceof HttpServletRequest || args[i] instanceof HttpServletResponse || args[i] instanceof HttpSession)) {
                    paramMap.put(parameterNames[i], args[i]);
                }
                //例外：用户信息需要到request中获取
                if(args[i] instanceof HttpServletRequest) {
                	//获取用户信息
                	HttpServletRequest request = (HttpServletRequest)args[i];
                	HttpSession session = request.getSession();
        			if(!Objects.isNull(session)) {
        				paramMap.put("track_user", session.getAttribute(ErpConst.CURR_USER));
        			}
                }
            }
        }
        return paramMap;
    }
}
