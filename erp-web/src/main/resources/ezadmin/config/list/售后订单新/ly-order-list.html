<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>商品领用列表</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="ly-order-list" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">领用订单号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="ORDER_NO" placeholder style alias="TT" jdbctype data datatype oper="LIKE">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder style="" alias="" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">审核状态</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="VERIFY_STATUS_CODE" placeholder="全部" style="" alias="" jdbctype="" data="[{
	&quot;V&quot;: &quot;审核中&quot;,
	&quot;K&quot;: &quot;1&quot;
}, {
	&quot;V&quot;: &quot;审核通过&quot;,
	&quot;K&quot;: &quot;2&quot;
}, {
	&quot;V&quot;: &quot;审核不通过&quot;,
	&quot;K&quot;: &quot;3&quot;
}]" datatype="JSON" oper="EQ"></select>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">订货号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="SKU_NO" placeholder style alias="TT" jdbctype data datatype oper="LIKE">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">产品名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="SHOW_NAME" placeholder style alias="TT" jdbctype data datatype oper="LIKE">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">品牌</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="BRAND_NAME" placeholder style alias="TT" jdbctype data datatype oper="LIKE">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">型号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="MODEL" placeholder style alias="TT" jdbctype data datatype oper="LIKE">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">领用状态</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="RECEIVE_STATUS" placeholder="全部" style="" alias="" jdbctype="" data="[{
	&quot;V&quot;: &quot;未领用&quot;,
	&quot;K&quot;: &quot;未领用&quot;
}, {
	&quot;V&quot;: &quot;部分领用&quot;,
	&quot;K&quot;: &quot;部分领用&quot;
}, {
	&quot;V&quot;: &quot;全部领用&quot;,
	&quot;K&quot;: &quot;全部领用&quot;
}]" datatype="JSON" oper="EQ"></select>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
    <button type="table" url="/wms/receiveOut/toAddReceiveOut.do" opentype="PARENT" windowname="新增领用" style class="layui-btn">新增领用</button>
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="ORDER_NO" name="ORDER_NO" body="td-link" order="" url="/wms/receiveOut/receiveDetail.do?receiveOutId=${ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="th">领用订单号</th>
      <th item_name="APPLYER" name="APPLYER" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">创建人</th>
      <th item_name="APPLYER_DEPARTMENT" name="APPLYER_DEPARTMENT" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">归属部门</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">创建时间</th>
      <th item_name="VERIFY_STATUS" name="VERIFY_STATUS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">审核状态</th>
      <th item_name="SKU_NO" name="SKU_NO" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">订货号</th>
      <th item_name="SHOW_NAME" name="SHOW_NAME" body="td-link" order="" url="/goods/goods/viewbaseinfo.do?goodsId=${SKU_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="th">产品名称</th>
      <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">品牌</th>
      <th item_name="MODEL" name="MODEL" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">型号</th>
      <th item_name="BATCH_NUMBER" name="BATCH_NUMBER" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">产品生产批次</th>
      <th item_name="BARCODE_FACTORY" name="BARCODE_FACTORY" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">SN码</th>
      <th item_name="OUTPUT_NUM" name="OUTPUT_NUM" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">领用数量</th>
      <th item_name="ALREADY_OUTPUT_NUM" name="ALREADY_OUTPUT_NUM" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">出库数量</th>
      <th item_name="RECEIVE_STATUS" name="RECEIVE_STATUS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">领用状态</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY ADD_TIME DESC" groupby="">StringBuilder sql=new StringBuilder();
sql.append("SELECT * FROM
       (SELECT
       T.ID,T.ORDER_NO, T.APPLYER, T.APPLYER_DEPARTMENT, T.ADD_TIME, T.VERIFY_STATUS AS VERIFY_STATUS_CODE, CASE WHEN T.VERIFY_STATUS = 0 THEN '待审核' WHEN T.VERIFY_STATUS = 1 THEN '审核中' WHEN T.VERIFY_STATUS = 2 THEN '审核通过' WHEN T.VERIFY_STATUS = 3 THEN '审核不通过' WHEN T.VERIFY_STATUS = 4 THEN '已关闭' END AS VERIFY_STATUS, SKU.SKU_ID, SKU.SKU_NO, SKU.SHOW_NAME, BR.BRAND_NAME, SKU.MODEL, IFNULL(WL.BATCH_NUMBER, '') AS BATCH_NUMBER, IFNULL(WL.BARCODE_FACTORY, '') AS BARCODE_FACTORY, IFNULL(GOOD.OUTPUT_NUM, 0) AS OUTPUT_NUM, IFNULL(GOOD.ALREADY_OUTPUT_NUM, 0) AS ALREADY_OUTPUT_NUM, CASE WHEN IFNULL(GOOD.ALREADY_OUTPUT_NUM, 0) = 0 THEN '未领用' WHEN IFNULL(GOOD.ALREADY_OUTPUT_NUM, 0) &gt; 0 AND IFNULL(GOOD.ALREADY_OUTPUT_NUM, 0) &lt; IFNULL(GOOD.OUTPUT_NUM, 0) THEN '部分领用' WHEN IFNULL(GOOD.ALREADY_OUTPUT_NUM, 0) = IFNULL(GOOD.OUTPUT_NUM, 0) THEN '全部领用'  END AS RECEIVE_STATUS
       FROM T_WMS_OUTPUT_ORDER T
       LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS GOOD ON GOOD.WMS_OUTPUT_ORDER_ID = T.ID
       LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM WL ON GOOD.ID = WL.RELATED_ID AND WL.OPERATE_TYPE = 14 AND WL.IS_DELETE = 0
       LEFT JOIN V_CORE_SKU SKU ON SKU.SKU_NO = GOOD.SKU_NO LEFT JOIN V_CORE_SPU SPU ON SPU.SPU_ID = SKU.SPU_ID
       LEFT JOIN T_UNIT U ON SKU.UNIT_ID = U.UNIT_ID
       LEFT JOIN T_BRAND BR ON BR.BRAND_ID = SPU.BRAND_ID WHERE T.TYPE = 3 AND T.IS_DELETE = 0 AND T.ORDER_NO LIKE 'LY%' AND T.VERIFY_STATUS IN (1,2,3)) TT WHERE 1=1 ");
return search(sql);</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>