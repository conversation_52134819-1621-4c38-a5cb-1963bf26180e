<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />

<style>


</style>
<div id="app">
    <br>
    <el-row>
        <el-col :span="1" offset="1" :sm="2" :lg="1" :md="2"><span style="font-size: 12px">终端名称</span></el-col>
        <el-col :span="3" :sm="6" :lg="3" :md="5"><el-input v-model="query" placeholder="请输入内容" size="mini" style="display: inline-block;width: 150px;" ></el-input></el-col>
        <el-col :span="1" :sm="2" :lg="1" :md="2"><el-button type="primary" size="mini" @click="queryName" :loading="queryLoading">搜索</el-button></el-col>
        <el-col :span="17" :sm="13" :lg="18" :md="14">
            <%--<el-tooltip content="请尽量输入精准终端名称,关键字需大于等于4个字" placement="right" effect="light">--%>
                <i class="el-icon-warning" style="color: #dc851f"></i> <span style="color: grey;font-size: 12px">请尽量输入精准终端名称,关键字需大于等于4个字</span>
            <%--</el-tooltip>--%>
        </el-col>
    </el-row>
    <br>
    <el-row>
        <el-col :span="23" :offset="2"  >
            <template v-if="tableShow">
                <el-table
                        :data="traderCustomerTerminalList"
                        border
                        size="small"
                        style="width: 80%">
                    <el-table-column
                            prop="hosName"
                            label="终端名称"

                    >
                        <template slot-scope="scope">
                            <div>
                                <el-tag v-if="!scope.row.uniqueId">天眼查</el-tag><span style="flex-wrap: wrap;">{{scope.row.hosName}}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="hosArea"
                            label="终端区域"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="institutionNature"
                            label="终端类型"
                            width="100">
                        <template slot-scope="scope">
                            <span>{{ scope.row.institutionNature == '0'?'公立':scope.row.institutionNature == '1'?'非公':'' }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                            prop="institutionLevel"
                            label="终端等级"
                            width="180">
                        <template slot-scope="scope">
                            <span>{{ scope.row.institutionLevel == '0'?'一级医院':
                                    scope.row.institutionLevel == '1'?'二级医院':
                                            scope.row.institutionLevel == '2'?'三级医院':
                                                    scope.row.institutionLevel == '3'?'未定级医院':
                                                            scope.row.institutionLevel == '4'?'社区卫生服务中心（站）':
                                                                    scope.row.institutionLevel == '5'?'乡镇卫生院':
                                                                            scope.row.institutionLevel == '6'?'诊所（医务室）':
                                                                                    scope.row.institutionLevel == '7'?'村卫生室':
                                                                                            scope.row.institutionLevel == '8'?'应急三级医院':
                                                                                                    scope.row.institutionLevel == '9'?'应急二级医院':
                                                                                                            scope.row.institutionLevel == '10'?'应急基层医疗':''
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="unifiedSocialCreditCode"
                            label="统一社会信用代码">
                    </el-table-column>
                    <el-table-column
                    <%--fixed="right"--%>
                            label="操作"
                            width="100">
                        <template slot-scope="scope">
                            <el-button @click="handleClick(scope.row)" :disabled="scope.row.disabled" type="text" size="small">选择</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-row style="margin-top: 20px; margin-bottom: 20px" v-if="showTyc">
                    <span style="color: grey;font-size: 12px">没有结果？不够准确？请点击</span>
                    <el-button type="primary" size="mini" plain @click="searchTyc()">天眼查搜索</el-button>
                </el-row>
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPageNo"
                        :page-sizes="[20, 50, 100]"
                        :page-size="currentSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="totalLines">
                </el-pagination>
            </template>

        </el-col>

    </el-row>
    <br>
    <el-row>
        <el-col :span="1" offset="1" :sm="2" :lg="1" :md="2"><span style="font-size: 12px">上传建链依据</span></el-col>
        <el-col :span="22" :sm="21" :lg="22" :md="21">
                <i class="el-icon-warning" style="color: #dc851f"></i> <span style="color: grey;font-size: 12px">依据可以是沟通记录编号、聊天记录截屏</span>
        </el-col>
    </el-row>
    <br>
    <el-form ref="form" :model="form" size="small" label-width="120px">
        <el-row v-for="terminal in form.traderCustomerTerminalChecked">

            <el-col :offset="2" :span="15">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>{{terminal.name}}</span>
                        <el-button style="float: right; padding: 3px 0" @click="deleteForm(terminal.uniqueId)" type="text">删除</el-button>
                    </div>
                    <el-form-item label="沟通录音ID" style="width: 300px">
                        <el-input v-model="terminal.cuid" maxlength="9" placeholder="请输入沟通记录录音ID"></el-input>
                    </el-form-item>
                    <el-form-item label="聊天记录截图" style="width: 1200px;">
                        <el-upload
                                action="/orderstream/saleorder/uploadImg.do"
                                list-type="picture-card"
                                :on-preview="handlePictureCardPreview"
                                :on-success="(res,file, fileList) => handleAvatarSuccess(res,file, fileList, terminal)"
                                :on-remove="(file, fileList) => handleRemove(file, fileList,terminal)"
                                :before-upload="beforeAvatarUpload"
                                accept="image/jpg,image/jpeg,image/png"
                                limit="3"
                                :file-list="terminal.rowFileList"
                        >
                            <i class="el-icon-plus"></i>
                        </el-upload>
                        <el-dialog :visible.sync="pictureDialogVisible">
                            <img width="100%" :src="followPic" alt="">
                        </el-dialog>
                    </el-form-item>
                    <el-form-item  style="width: 300px">
                        <span style="color:grey">最多可添加三张图片</span>
                    </el-form-item>
                </el-card>
            </el-col>

        </el-row>

        <br>
        <template v-if="form.traderCustomerTerminalChecked !=null&& form.traderCustomerTerminalChecked.length>0">
            <el-row>
                <el-col :span="22" :offset="2">
                    <i class="el-icon-warning" style="color: #dc851f"></i> <span style="color: grey;font-size: 12px">添动客户经营的终端单位，系统可智能匹配标签，标签和建链将在审核通过后生效</span>
                </el-col>
            </el-row>
            <br>
            <el-row>
                <el-col :span="22" :offset="2">
                        <el-button type="primary" size="small" style="margin-left: 0px" :loading="subLoaging" @click="submitForm('form')">提交</el-button>
                </el-col>
            </el-row>
        </template>
    </el-form>


</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const viewData={
        traderCustomerId : '${traderCustomerId}',
        traderId : '${traderId}'
    }
    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {


            return {
                // 显示table
                tableShow:false,
                queryLoading:false,
                currentSize:20,
                currentPageNo:1,
                totalLines:0,
                radio:'',
                query:'',
                showTyc:false,
                traderCustomerTerminalList: [],
                form:{
                    traderCustomerTerminalChecked:[]
                },
                //图片展示
                pictureDialogVisible: false,
                followPic:'',
                fileList: [],
                subLoaging:false

            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            sendThis(this);


        },

        methods: {
            scrollTo(ref){
                let top = this.$refs[ref].$el.offsetTop;
                window.scrollTo(0, top);
            },
            handleSizeChange(val) {
                this.currentSize = val;
                this.queryName();
            },
            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.queryName();
            },
            queryName() {
                if (this.query.length < 4) {
                    this.$message({
                        message: '至少输入四个关键字',
                        type: 'warning'
                    });
                } else {
                    this.queryLoading = true;
                    let that = this;
                    queryTerminalData({"name": this.query,"traderId":viewData.traderId,"pageSize": this.currentSize,"pageNum": this.currentPageNo}).then(res => {
                        var arr = [];
                        res.data.data.list.forEach(function (item) {
                            let flag = false;
                            let find = that.form.traderCustomerTerminalChecked.find(a => a.uniqueId == item.uniqueId && a.uniqueId != null && a.uniqueId != undefined && a.uniqueId != '');
                            if (find !== undefined) {
                                flag=true;
                            }
                            arr.push({
                                ...item,
                                disabled:item.checked||flag
                            })

                        });
                        this.totalLines = res.data.data.total;
                        that.traderCustomerTerminalList = arr;
                        this.tableShow = true;
                        this.queryLoading = false;
                        if (this.totalLines < 6) {
                            this.showTyc = true;
                        } else {
                            this.showTyc = false;
                        }
                    })
                }

            },
            searchTyc() {
                let that = this;
                searchTycTerminalAndCheck({
                    "name": this.query,
                    "traderId":viewData.traderId,
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo
                }).then(res => {
                    var arrs = [];
                    res.data.data.list.forEach(function (item) {
                        let flag = false;
                        let find = that.form.traderCustomerTerminalChecked.find(A => A.name == item.hosName &&!(A.uniqueId != null && A.uniqueId != undefined && A.uniqueId != ''));
                        if (find !== undefined) {
                            flag=true;
                        }
                        arrs.push({
                            ...item,
                            disabled:item.checked||flag
                        })

                    });
                    this.totalLines = res.data.data.total;
                    that.traderCustomerTerminalList = arrs;
                    this.showTyc=false;
                })
            },
            handleClick(data) {

                if (data.uniqueId != null && data.uniqueId != undefined && data.uniqueId != '') {
                    let find = this.form.traderCustomerTerminalChecked.find(item => item.uniqueId == data.uniqueId && item.uniqueId != null && item.uniqueId != undefined && item.uniqueId != '');
                    if (find !== undefined) {
                        return;
                    }
                } else {
                    let find = this.form.traderCustomerTerminalChecked.find(item => item.hosName == data.hosName &&!(item.uniqueId != null && item.uniqueId != undefined && item.uniqueId != ''));
                    if (find !== undefined) {
                        return;
                    }
                }

                data.disabled = true;
                this.form.traderCustomerTerminalChecked.push({name:data.hosName,uniqueId:data.uniqueId,cuid:'',rowFileList:[],fileList:[]})
            },
            deleteForm(uniqueId) {
                debugger
                let findIndex = this.form.traderCustomerTerminalChecked.findIndex(item => item.uniqueId == uniqueId);
                if (findIndex != -1) {
                    this.form.traderCustomerTerminalChecked.splice(findIndex, 1);
                }
                let find = this.traderCustomerTerminalList.find(item => item.uniqueId == uniqueId);
                if (find != undefined) {
                    find.disabled = false;
                }
            },
            handlePictureCardPreview(file) {
                this.followPic = file.url;
                this.pictureDialogVisible = true;
            },
            // 上传成功
            handleAvatarSuccess(res,file, fileList,terminal) {
                terminal.fileList.push({filePath:res.ossUrl})
                this.subLoaging = false;
            },
            // 删除
            handleRemove(file, fileList, terminal) {
                let index = terminal.fileList.findIndex(i=>i.filePath == file.response.ossUrl);
                terminal.fileList.splice(index, 1);
            },
            // 校验
            beforeAvatarUpload(file) {

                var img = file.name.substring(file.name.lastIndexOf('.') + 1)
                img = img.toLocaleLowerCase();
                const suffix = img === 'jpg'
                const suffix2 = img === 'png'
                const suffix3 = img === 'jpeg'
                if (!suffix && !suffix2 && !suffix3) {
                    this.$message.error("只能上传图片！");
                    return false
                }

                const isLt2M = file.size / 1024 / 1024 <= 5;
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 5MB!');
                    return false
                }

                this.subLoaging = true;

                return true;
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            checkNumber(data) {
                var re = /^[1-9]\d*$/;
                return re.test(data);
            },
            submitForm(formName) {

                let flag = true;
                let that = this;
                for (let i = 0; i < this.form.traderCustomerTerminalChecked.length; i++) {
                    let item = this.form.traderCustomerTerminalChecked[i];
                    if (item.cuid == '' && item.fileList.length == 0) {
                        this.$message.error(item.name+" 沟通记录ID和聊天记录截图至少填写一项");
                        flag = false;
                        break;
                    }
                    if (item.cuid != '') {
                        if (!this.checkNumber(item.cuid)) {
                            this.$message.error(item.name+" 沟通记录ID只能为正整数");
                            flag = false;
                            break;
                        }
                    }
                }
                if (flag) {
                    var arr = [];
                    this.form.traderCustomerTerminalChecked.forEach(item=>{
                        arr.push({
                            traderId:viewData.traderId,
                            traderCustomerId:viewData.traderCustomerId,
                            terminalName:item.name,
                            dwhTerminalId:item.uniqueId,
                            communicateRecordId:item.cuid,
                            picUrl: item.fileList.length > 0 ? item.fileList.map(item => item.filePath).join(','): ''
                        })

                    })

                    this.subLoaging = true;
                    traderCustomerTerminalAdd(arr).then(res => {

                        if (res.data.code != 0) {
                            this.$message.error(res.data.message);
                            this.subLoaging = false;
                        } else {
                            this.$message({
                                message: '保存成功，跳转客户基础信息页',
                                type: 'success',
                                duration:500,
                                onClose: () => {
                                    window.location.href = '/trader/customer/baseinfo.do?traderId='+viewData.traderId+'&traderCustomerId=' + viewData.traderCustomerId;
                                }
                            });
                        }

                    });

                } else {
                    return false;
                }

            }


        }
    })
</script>
