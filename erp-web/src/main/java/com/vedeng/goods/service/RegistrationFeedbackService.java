package com.vedeng.goods.service;

import com.vedeng.goods.domain.dto.RegistrationFeedbackRecordDto;
import com.vedeng.goods.domain.dto.RegistrationMessageReplyDto;

import javax.servlet.http.HttpSession;

/**
 * <AUTHOR>
 * @create 2022−11-01 下午1:47
 * @description
 */
public interface RegistrationFeedbackService {

    Boolean sendMessage(RegistrationFeedbackRecordDto registrationFeedbackDto);

    int messageReply(RegistrationMessageReplyDto registrationMessageReplyDto, HttpSession session);
}
