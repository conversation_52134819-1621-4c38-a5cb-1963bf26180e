package com.vedeng.crm.business.quote.service;

import com.vedeng.crm.business.quote.domain.dto.NeedsImportInfoDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import com.vedeng.crm.business.quote.domain.entity.QuoteorderNeedsEntity;

import java.util.List;

public interface QuoteOrderNeedsService {

    /**
     * 批量插入
     */
    int batchInsert(List<NeedsImportInfoDto> list, QuoteorderNeedsEntity quoteorderNeedsEntity);

    /**
     * 根据QuoteorderId查询
     */
    List<QuoteorderNeedsEntity> selectByQuoteorderId(Integer quoteorderId);

    List<QuoteorderNeedsEntity> selectByQuoteNeedsIdList(List<Long> quoteorderNeedsIdList);

    int updateByPrimaryKeySelective(QuoteorderNeedsEntity record);

    void deleteByQuoteorderNeedsId(List<QuoteorderNeedsEntity> extraOldList);

    List<CrmQuoteorderGoods> queryQuoteGoodsByNeedsIdAndSkuNos(List<String> skuNos, Long quoteorderNeedsId);
}
