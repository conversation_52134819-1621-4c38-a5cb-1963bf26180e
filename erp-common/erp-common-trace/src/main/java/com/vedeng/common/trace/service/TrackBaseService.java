package com.vedeng.common.trace.service;

import cn.hutool.core.util.ReflectUtil;
//import com.vedeng.track.log.bigdata.BigdataTraceInErp;
import com.vedeng.track.server.bigdata.interceptor.BigdataTrace;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.util.TrackUtils;
import com.vedeng.track.server.bigdata.param.EventTraceReqDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.ModelAndView;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 基础公共数据服务
 * @ClassName:  TrackBaseService     
 * @author: Neil.yang
 * @date:   2024年6月4日 下午2:35:43    
 * @Copyright:
 */
public abstract class TrackBaseService {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(TrackBaseService.class);
	
	@Value("${trackLogOn:true}")
	private boolean trackLogOn;
	
	/**
	 * 针对埋点的必填字段，客户相关的必须要traderId
	 * 有用户操作痕迹的必须要有trader_user对象，例如定时任务和前台传递的数据则不需要验证此对象
	 * @param trackParamsData
	 * @return
	 */
	public abstract boolean checkParamsLegal(TrackParamsData trackParamsData);
	
	/**
	 * 埋点操作
	 * @param trackParamsData
	 */
	public void track(TrackParamsData trackParamsData) {
		//执行失败的不需要埋点
		if(!methodExecuteSuccess(trackParamsData)) {
			if(trackLogOn) {
				LOGGER.info("{}:执行失败的不需要埋点",trackParamsData.getEventTrackingEnum().getArchivedName());
			}
			return;
		}
		
		//字段验证
		if(!checkParamsLegal(trackParamsData)) {
			return;
		}
		
		//执行成功，对参数进行转换
		BigdataTrace.eventLogSubmit(convertMatchParam(trackParamsData));
//		BigdataTraceInErp.eventLogSubmit(convertMatchParam(trackParamsData));
	}
	

	/**验证方法是否执行成功，执行失败，不需要埋点*/
	@SuppressWarnings("unchecked")
	private static boolean methodExecuteSuccess(TrackParamsData trackParamsData) {
		try {
			Object trackResult = trackParamsData.getTrackResult();

			if (trackResult instanceof ModelAndView) {
				ModelAndView view = (ModelAndView) trackResult;
				return "common/success".equals(view.getViewName());
			} else if (isInstanceOfResultInfo(trackResult)) {
				return getResultInfoCode(trackResult) == 0;
			} else {
				LOGGER.error("{},埋点检测到返回值类型变更，无法进行埋点...", trackParamsData.getEventTrackingEnum().getArchivedName());
				return false;
			}
		} catch (Exception e) {
			LOGGER.error("验证方法执行失败", e);
			return false;
		}
	}

	private static boolean isInstanceOfResultInfo(Object obj) {
		return obj != null && ("ResultInfo".equals(obj.getClass().getSimpleName()) ||
				"R".equals(obj.getClass().getSimpleName()));
	}

	private static int getResultInfoCode(Object resultInfo) {
		try {
			Field codeField = resultInfo.getClass().getDeclaredField("code");
			codeField.setAccessible(true);
			return (int) codeField.get(resultInfo);
		} catch (NoSuchFieldException | IllegalAccessException e) {
			LOGGER.error("获取ResultInfo字段失败", e);
			return -1;
		}
	}

	
	/**转换成大数据需要的数据*/
	private EventTraceReqDto convertMatchParam(TrackParamsData trackParamsData) {
		EventTraceReqDto eventTraceReqDto = new EventTraceReqDto();
		CurrentUser userInfo = getUserInfo(trackParamsData);
		if(trackLogOn) {
			LOGGER.info("埋点获取的用户信息：{}",JSON.toJSONString(userInfo));
		}
		//参数解析
		Map<String,Object> trackParams = trackParamsData.getTrackParams();
		if(Objects.nonNull(userInfo)) {
			//用户信息
			eventTraceReqDto.setUserId(userInfo.getId());
			eventTraceReqDto.setOrgName(userInfo.getOrgName());
			eventTraceReqDto.setUsername(userInfo.getUsername());
			trackParams.put("username", userInfo.getUsername());
			trackParams.put("number", userInfo.getNumber());
		}
		//档案信息
		EventTrackingEnum eventTrackingEnum = trackParamsData.getEventTrackingEnum();
		eventTraceReqDto.setArchivedId(eventTrackingEnum.getArchivedId());
		eventTraceReqDto.setEventTime(new Date());
		if(Objects.nonNull(trackParamsData.getTrackTime())) {
			eventTraceReqDto.setEventTime(trackParamsData.getTrackTime());
		}
		//预先找好的对应参数
		List<String> paramsList = eventTrackingEnum.getParams();
		//获取档案内容
		JSONObject jsonObject = new JSONObject();
		if(Objects.nonNull(paramsList)) {
			for (String key : paramsList) {
				String value = TrackUtils.getJsonValue(new JSONObject(trackParams),key);
				jsonObject.put(key, value);
			}
		}
		//档案内容
		eventTraceReqDto.setArchivedContext(jsonObject);
		//获取唯一标识符（客户ID）
		String primaryKey = eventTrackingEnum.getPrimaryKey();
		String primaryValue = TrackUtils.getJsonValue(new JSONObject(trackParams),primaryKey);
		//客户ID
		eventTraceReqDto.setTraderId(Integer.parseInt(primaryValue));
		if(trackLogOn) {
			LOGGER.info("埋点传给大数据参数：{}",JSON.toJSONString(eventTraceReqDto));
		}
		return eventTraceReqDto;
	}
	
	/**
	 * 获取登录用户信息
	 * @param trackParamsData
	 * @return
	 */
	public CurrentUser getUserInfo(TrackParamsData trackParamsData){
		try {
			Map<String, Object> trackParams = trackParamsData.getTrackParams();
			Object trackUser = trackParams.get("track_user");

			if (Objects.isNull(trackUser)) {
				return null;
			}

			CurrentUser currentUser = new CurrentUser();
			Integer userId = (Integer) ReflectUtil.getFieldValue(trackUser, "userId");
			currentUser.setId(userId);
			String orgName = (String) ReflectUtil.getFieldValue(trackUser, "orgName");
			currentUser.setOrgName(orgName);
			String username = (String) ReflectUtil.getFieldValue(trackUser, "username");
			currentUser.setUsername(username);
			String number =  (String) ReflectUtil.getFieldValue(trackUser, "number");
			currentUser.setNumber(number);

			return currentUser;
		} catch (Exception e) {
			LOGGER.error("埋点获取用户信息失败", e);
		}
		return null;
	}


}
