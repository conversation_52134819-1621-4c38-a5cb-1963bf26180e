package com.vedeng.logistics.exception;

import com.vedeng.logistics.eums.UrgerDeliveryOrderResponeCode;
import org.tuckey.web.filters.urlrewrite.Run;

/**
 * <AUTHOR>
 * @Description com.vedeng.logistics.eums
 * @Date 2022/4/14 19:41
 */

public class UrgeDeliveryOrderException extends RuntimeException {

    private Integer code;
    private String message;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public UrgeDeliveryOrderException(UrgerDeliveryOrderResponeCode urgerDeliveryOrderResponeCode) {
        this.code = urgerDeliveryOrderResponeCode.getCode();
        this.message = urgerDeliveryOrderResponeCode.getMsg();
    }
}
