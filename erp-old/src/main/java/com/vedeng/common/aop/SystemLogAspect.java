package com.vedeng.common.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.common.constants.Contant;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.model.UserOperationLog;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.system.service.ActionService;
import com.vedeng.system.service.UserOperationLogService;
import net.sf.json.JSONObject;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Aspect
@Component
public class SystemLogAspect {
	public static Logger logger = LoggerFactory.getLogger(SystemLogAspect.class);

	@Resource
	private UserOperationLogService userOperationLogService;
	@Resource
	private ActionService actionService;

	//本地异常日志记录对象    
    //private  static  final Logger logger = LoggerFactory.getLogger(SystemLogAspect. class);  
    
    //Controller层切点    
    //@Pointcut("execution (* com.vedeng.*.controller..*.*(..)) && !execution (* com.vedeng.common.controller..*.*(..))")
    @Pointcut("@annotation(com.vedeng.common.annotation.SystemControllerLog)")
    public  void controllerAspect() {} 
    
    //service层切点   
//    @Pointcut("@annotation(com.vedeng.common.annotation.SystemServiceLog)")
//    public  void serviceAspect() {} 
    
    @Around("controllerAspect()")
    public Object process(ProceedingJoinPoint point) throws Throwable {
    	//访问目标方法的参数：
        Object[] args = point.getArgs();
        if (args != null && args.length > 0 && args[0].getClass() == String.class) {
            
        }
        //用改变后的参数执行目标方法
        Object returnValue = point.proceed(args);
    	return returnValue ;
    }
 
    /**
     * <b>Description:</b><br> 后置通知
     * @param point
     * @param returnValue
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年9月6日 下午1:06:34
     */
    @AfterReturning(value="controllerAspect()", returning="returnValue")
    public void doAfterReturn(JoinPoint joinPoint, Object returnValue){
    	RequestAttributes ra = RequestContextHolder.getRequestAttributes();  
    	ServletRequestAttributes sra = (ServletRequestAttributes) ra;  
    	HttpServletRequest request = sra.getRequest();   
        HttpSession session = request.getSession();    
        //读取session中的用户    
        User user = (User) session.getAttribute(ErpConst.CURR_USER);    
        //请求功能点url
        String url = request.getServletPath();   
        try {
	        Integer optType = 0;
	        String desc = "";
	        Integer type  = 0;
	        Integer isEnable  = 0;
	        Map<String, Object> map = getServiceMethodMsg(joinPoint,url);
	        if(map != null){
	        	if(map.containsKey("type")){
	        		type = Integer.valueOf(map.get("type").toString());
	        	}
	        	if(map.containsKey("desc")){
	        		desc = map.get("desc").toString();
	        	}
	        	if(map.containsKey("opt")){
	        		optType = Integer.valueOf(map.get("opt").toString());
	        	}
				if(map.containsKey("isEnable")){
					isEnable = Integer.valueOf(map.get("isEnable").toString());
				}
	        }
	        Integer actionId = getActionId(url);
	        if(optType !=5){
    			//*========数据库日志=========*//  
                UserOperationLog userOperationLog = new UserOperationLog();
                userOperationLog.setAccessTime(DateUtil.sysTimeMillis());
                userOperationLog.setUserId(user.getUserId());
                userOperationLog.setUsername(user.getUsername());
                userOperationLog.setAccessIp(getIpAddress(request));
                userOperationLog.setActionId(actionId);
                userOperationLog.setType(type);
                userOperationLog.setDesc(desc);
                userOperationLog.setOperationType(optType);
                if(optType !=3 && optType !=4){//导入导出不做前后记录
                	Object [] methods = joinPoint.getArgs();
                    StringBuffer inParams = new StringBuffer();
                    for (Object obj : methods) {
    					if(obj != null && !obj.toString().contains("servlet")&&!obj.toString().contains("beforeParams") && !obj.toString().contains("BeanPropertyBindingResult")){
    						try {
    							// 尝试JSON序列化
    							inParams.append(JsonUtils.translateToJson(obj)).append(";");
    						} catch (Exception e) {
    							// 序列化失败时，记录对象的简单信息而不是抛出异常
    							logger.debug("对象序列化失败，使用简单信息记录: {}", obj.getClass().getSimpleName(), e);
    							inParams.append("[").append(obj.getClass().getSimpleName()).append(":").append(obj.toString()).append("];");
    						}
    					}
    					if(optType != 0 && obj != null && obj.toString().contains("beforeParams")){
    						String key = obj.toString();
    						String beforeParams = JedisUtils.get(key);
    						userOperationLog.setBeforeEntity(beforeParams);
    						JedisUtils.del(key);
    	                }
    				}
                    userOperationLog.setAfterEntity(inParams.toString());
                }
                 if(returnValue !=null && returnValue.toString().contains("ResultInfo")){
                	 JSONObject json = JSONObject.fromObject(returnValue);
//                	 ResultInfo<?> res = (ResultInfo<?>) JSONObject.toBean(json, ResultInfo.class);
		  		     Gson gson = new Gson();
					 Type typeOfT = new TypeToken<ResultInfo>(){}.getType();
					 ResultInfo res = gson.fromJson(json.toString(), typeOfT);
                	 if(res==null || res.getCode()!=0){
                		 userOperationLog.setResultStatus(-1);
                	 }else{
                		 userOperationLog.setResultStatus(0);
                	 }
                	 //业务主体id
                	 if(res != null && res.getData() != null && res.getData() instanceof Integer){
                		 userOperationLog.setRelatedId((Integer)res.getData()); 
                	 }
                 }else if(returnValue !=null && !returnValue.toString().contains("404") && !returnValue.toString().contains("fail")
                		 &&(returnValue.toString().contains("redirect")||returnValue.toString().contains(".do") || returnValue.toString().contains("success"))){
                	 userOperationLog.setResultStatus(0);
                 }else{
                	 userOperationLog.setResultStatus(-1);
                 }
                // logger.info("ASPECTLOG1"+JSON.toJSONString(userOperationLog));
				if (isEnable == 1){
					userOperationLogService.saveUserOperationLog(userOperationLog);
				}
                //userOperationLogService.saveUserOperationLog(userOperationLog);
        		}
		}  catch (JSONException ex){
        	logger.error("Unknown property:",ex);
		}  catch (Exception e) {
            //记录本地异常日志    
//            logger.error("==后置通知异常==");    
//            logger.error("异常信息:{}", e.getMessage());  
        	logger.error(Contant.ERROR_MSG, e);
        } 
        
            
    }

//	public static void main(String[] args) {
//		String a = "{\"code\":0,\"data\":[{\"accountEndTime\":0,\"accountPayable\":0,\"accountPeriod\":0,\"accountPeriodAmount\":0,\"actionId\":0,\"addTime\":0,\"addTimeStr\":\"\",\"addTimejh\":0,\"additionalClause\":\"\",\"advancePurchaseComments\":\"\",\"advancePurchaseStatus\":0,\"advancePurchaseTime\":0,\"advanceValidStatus\":0,\"afterReturnNum\":0,\"afterSalesGoodsList\":[],\"allDeliveryNum\":0,\"allNum\":0,\"allTotalAmount\":0,\"amount\":0,\"applicantName\":\"\",\"applyInvoiceTime\":0,\"areaId\":0,\"arrivalStatus\":2,\"arrivalTime\":*************,\"autoAudit\":0,\"averageTaxpayerUri\":\"\",\"bank\":\"\",\"bankAccount\":\"\",\"bankCode\":\"\",\"batchNoCommentArray\":\"\",\"batchNoComments\":\"\",\"bdMobileTime\":0,\"bdtraderComments\":\"\",\"beginTime\":0,\"bhVerifyStatus\":0,\"billPeriodSettlementType\":0,\"brandName\":\"\",\"bussinessChanceId\":0,\"bussinessChanceNo\":\"\",\"bussinessId\":0,\"bussinessNo\":\"\",\"bussinessType\":0,\"buyNum\":0,\"buyTime\":0,\"capitalBillAmount\":0,\"capitalBillId\":0,\"closeComments\":\"\",\"comments\":\"\",\"communicateNum\":0,\"companyId\":1,\"companyName\":\"\",\"componentHtml\":\"\",\"componentId\":0,\"confirmNumber\":0,\"confirmStatus\":0,\"confirmTime\":null,\"confirmationFormAudit\":0,\"confirmationFormUpload\":0,\"confirmationSubmitTime\":0,\"consultStatus\":0,\"contractNoStampUrl\":\"\",\"contractStatus\":0,\"contractUrl\":\"\",\"costAmount\":0,\"costUserIds\":\"\",\"costUserIdsList\":[],\"couponAmount\":0,\"couponInfo\":null,\"couponMoney\":0,\"couponType\":0,\"createMobile\":\"\",\"createMobileList\":[],\"creator\":0,\"creatorName\":\"\",\"creatorOrgId\":0,\"creatorOrgName\":\"\",\"creatorjh\":0,\"currentOperator\":\"\",\"customerLevelStr\":\"\",\"customerNature\":0,\"customerNatureStr\":\"\",\"customerType\":0,\"customerTypeStr\":\"\",\"deliveryClaim\":0,\"deliveryCycle\":\"\",\"deliveryDelayTime\":0,\"deliveryDelayTimeStr\":\"\",\"deliveryDirect\":0,\"deliveryDirectComments\":\"\",\"deliveryMethod\":0,\"deliveryNum\":0,\"deliveryStatus\":0,\"deliveryTime\":0,\"deliveryType\":0,\"deliveryTypeStr\":\"\",\"discountTypeId\":0,\"eFlag\":\"0\",\"elSaleordreNo\":\"\",\"email\":\"\",\"endAmount\":0,\"endTime\":0,\"expressId\":0,\"expressIds\":[],\"extraType\":\"\",\"fax\":\"\",\"financeCheckStatus\":0,\"financeComments\":\"\",\"firstBussinessTimeStr\":\"\",\"fiveTotalAmount\":0,\"flag\":\"0\",\"freightDescription\":0,\"goodsComments\":\"\",\"goodsId\":0,\"goodsIds\":[],\"goodsList\":[],\"goodsName\":\"\",\"goodsType\":0,\"goodslist\":[],\"groupContactPositions\":\"\",\"groupCustomerId\":0,\"hasReturnMoney\":0,\"haveAccountPeriod\":0,\"haveAdvancePurchase\":0,\"haveCommunicate\":0,\"hxTime\":0,\"idCnt\":\"\",\"insideComments\":\"\",\"invoiceAreaId\":0,\"invoiceComments\":\"\",\"invoiceEmail\":\"\",\"invoiceId\":[],\"invoiceList\":[],\"invoiceMethod\":0,\"invoiceSendNode\":0,\"invoiceStatus\":0,\"invoiceTime\":0,\"invoiceTraderAddress\":\"\",\"invoiceTraderAddressId\":0,\"invoiceTraderArea\":\"\",\"invoiceTraderAreaId\":0,\"invoiceTraderContactEmail\":\"\",\"invoiceTraderContactId\":0,\"invoiceTraderContactMobile\":\"\",\"invoiceTraderContactName\":\"\",\"invoiceTraderContactTelephone\":\"\",\"invoiceTraderId\":0,\"invoiceTraderName\":\"\",\"invoiceType\":0,\"invoiceTypeStr\":\"\",\"isActionGoods\":0,\"isAdvance\":0,\"isAftersale\":0,\"isApplyInvoice\":0,\"isCalInvoiceGoods\":0,\"isCloseSale\":0,\"isConfirmTime\":\"\",\"isContractReturn\":0,\"isCopy\":0,\"isCoupons\":0,\"isCustomerArrival\":0,\"isDelayInvoice\":0,\"isDeliveryOrderReturn\":0,\"isExpreeInfo\":\"\",\"isHandMatch\":0,\"isHaveInvoice\":0,\"isHaveInvoiceApply\":0,\"isIncludePrice\":0,\"isLocked\":0,\"isNew\":0,\"isOpenInvoice\":0,\"isOut\":\"0\",\"isOverSettlementPrice\":0,\"isPayment\":0,\"isPrint\":0,\"isPrintout\":0,\"isReferenceCostPrice\":0,\"isRisk\":0,\"isSM\":\"0\",\"isSaleOut\":0,\"isSalesPerformance\":0,\"isSameAddress\":0,\"isSearchCount\":0,\"isSendInvoice\":0,\"isSendSms\":\"\",\"isSpecialSales\":\"\",\"isTraderRisk\":0,\"isUrgent\":0,\"isVirture\":0,\"isWeiXin\":0,\"keyIds\":[],\"lastBussinessTimeStr\":\"\",\"lendOutId\":0,\"lendout\":null,\"lockedReason\":\"\",\"lockedStatus\":0,\"loginUserBelongToProductManager\":0,\"logisticsApiSync\":0,\"logisticsCollection\":0,\"logisticsComments\":\"\",\"logisticsId\":0,\"logisticsName\":\"\",\"logisticsNo\":\"\",\"logisticsWxsendSync\":0,\"marketingPlan\":\"\",\"materialCode\":\"\",\"meetInvoiceConditions\":0,\"mobile\":\"\",\"modTime\":0,\"modTimejh\":0,\"model\":\"\",\"monthAmountList\":[],\"msaleorderNo\":\"\",\"name\":\"\",\"needShowConfirm\":false,\"num\":0,\"onlineFlag\":false,\"openInvoiceApply\":0,\"operateType\":0,\"optType\":\"\",\"optUserId\":0,\"optUserName\":\"\",\"optor\":\"\",\"orderId\":0,\"orderNos\":[],\"orderStreamStatus\":0,\"orderType\":9,\"orgId\":0,\"orgIdList\":[],\"orgName\":\"\",\"originalAmount\":0,\"outGoodsTime\":\"\",\"outIsFlag\":0,\"overLimit\":0,\"overdueStatus\":0,\"ownerUserId\":0,\"ownerUserName\":\"\",\"paid\":0,\"parentId\":0,\"payType\":0,\"payee\":\"\",\"paymentAmount\":0,\"paymentComments\":\"\",\"paymentMode\":0,\"paymentStatus\":0,\"paymentTime\":0,\"paymentType\":0,\"paymentTypeStr\":\"\",\"periodAmount\":0,\"periodDay\":0,\"periodTime\":0,\"phoneNo\":\"\",\"phtxFlag\":false,\"pickNums\":\"\",\"prepaidAmount\":0,\"prepareComments\":\"\",\"prepareReaseonType\":0,\"price\":0,\"proBuySum\":0,\"productBelongUserId\":0,\"purchase\":0,\"purchaseStatus\":0,\"quoteorderId\":0,\"quoteorderNo\":\"\",\"realAmount\":0,\"realPayAmount\":0,\"realTotalAmount\":0,\"reasonNo\":\"\",\"receivedAmount\":0,\"regAddress\":\"\",\"regTel\":\"\",\"regionType\":0,\"reqType\":0,\"residueAmount\":0,\"retainageAmount\":0,\"retainageAmountMonth\":0,\"retentionMoney\":0,\"retentionMoneyDay\":0,\"riskComments\":\"\",\"riskKey\":\"\",\"riskTime\":0,\"saleUserList\":[],\"saleorderGoodId\":0,\"saleorderGoodsId\":0,\"saleorderGoodsIds\":\"\",\"saleorderId\":195160,\"saleorderModifyApplyId\":0,\"saleorderModifyApplyNo\":\"\",\"saleorderMoney\":0,\"saleorderNo\":\"YFZ009505824012991714\",\"saleorderNum\":0,\"salesArea\":\"\",\"salesAreaId\":0,\"salesDeptId\":\"\",\"salesDeptName\":\"\",\"salesDeptUser\":[],\"salesName\":\"\",\"salesPerformanceModTime\":0,\"salesPerformanceTime\":0,\"satisfyDeliveryTime\":0,\"satisfyInvoiceTime\":0,\"search\":\"\",\"searchBeginDate\":\"\",\"searchBegintime\":0,\"searchBegintimeList\":[],\"searchDateType\":0,\"searchEndDate\":\"\",\"searchEndtime\":0,\"searchEndtimeList\":[],\"searchPaymentBeginTime\":0,\"searchPaymentEndTime\":0,\"searchType\":0,\"sendTime\":null,\"sendToPc\":0,\"serviceStatus\":0,\"sex\":0,\"sgvList\":[],\"shName\":\"\",\"shType\":0,\"show\":\"\",\"sku\":\"\",\"source\":0,\"ssoAccountId\":0,\"startAmount\":0,\"status\":0,\"statusComments\":0,\"supplierClause\":\"\",\"syncStatus\":0,\"takeTraderAddress\":\"\",\"takeTraderAddressId\":0,\"takeTraderArea\":\"\",\"takeTraderAreaId\":0,\"takeTraderContactId\":0,\"takeTraderContactMobile\":\"\",\"takeTraderContactName\":\"\",\"takeTraderContactTelephone\":\"\",\"takeTraderId\":0,\"takeTraderName\":\"\",\"tavList\":[],\"taxNum\":\"\",\"tcList\":[],\"terminalTraderId\":0,\"terminalTraderName\":\"\",\"terminalTraderType\":0,\"terminalTraderTypeStr\":\"\",\"thk\":0,\"tkTime\":0,\"totalAmount\":1,\"totalNum\":1,\"traderAddress\":\"纪翟路888号19幢西楼上海新起点康复医院\",\"traderAddressId\":0,\"traderAddressVoList\":[],\"traderArea\":\"\",\"traderAreaId\":0,\"traderComments\":\"\",\"traderContact\":\"\",\"traderContactId\":0,\"traderContactMobile\":\"***********\",\"traderContactName\":\"戴卫群\",\"traderContactTelephone\":\"\",\"traderCustomerId\":0,\"traderFinanceVo\":null,\"traderId\":95058,\"traderIdList\":[],\"traderName\":\"\",\"traderSource\":0,\"traderStatus\":0,\"traderSubject\":0,\"traderTime\":0,\"type\":0,\"typef\":\"\",\"updateDataTime\":null,\"updater\":0,\"updaterjh\":0,\"urgentAmount\":0,\"userId\":0,\"userIdList\":[],\"userName\":\"\",\"validOrgId\":0,\"validOrgName\":\"\",\"validStatus\":0,\"validTime\":0,\"validTimeStr\":\"\",\"validUserId\":0,\"verifiesType\":0,\"verifyStatus\":0,\"verifyUsername\":\"\",\"verifyUsernameList\":[],\"webAccountId\":0,\"webTakeDeliveryTime\":0,\"wheatherJdOrder\":0}],\"listData\":[],\"message\":\"修改成功\",\"page\":null,\"param\":{\"skey\":\"c07836e7-47cb-4ec4-8f6d-634aeca65286\",\"systime\":\"*************\"},\"status\":0} ";
//
//		JSONObject json2 = JSONObject.fromObject(a);
////		ResultInfo res = (ResultInfo<SaleorderVo>) JSONObject.toBean(json2, ResultInfo.class);
//
//		Gson gson = new Gson();
//		Type typeOfT = new TypeToken<ResultInfo>(){}.getType();
//		ResultInfo ResultInfo = gson.fromJson(a, typeOfT);
//		logger.info("11111111111111111ResultInfo:{}", ResultInfo);
//		logger.info("11111111111111111ResultInfo22222:{}", ResultInfo.getData());
//
//	}


	/**
     * 异常通知 用于拦截Controller层记录异常日志  
     *  
     * @param joinPoint  
     * @param e  
     */    
    @AfterThrowing(pointcut = "controllerAspect()", throwing = "e")    
     public void doAfterThrowing(JoinPoint joinPoint, Throwable e) {    
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();    
        HttpSession session = request.getSession();    
        //读取session中的用户    
        User user = (User) session.getAttribute(ErpConst.CURR_USER); 
        //请求功能点url
        String url = request.getServletPath();
    	try {
    		Integer optType = 0;
	        String desc = "";
	        Integer type = 0;
	        Integer isEnable = 0;
	        Map<String, Object> map = getServiceMethodMsg(joinPoint,url);
	        if(map != null){
	        	if(map.containsKey("type")){
	        		type = Integer.valueOf(map.get("type").toString());
	        	}
	        	if(map.containsKey("desc")){
	        		desc = map.get("desc").toString();
	        	}
	        	if(map.containsKey("opt")){
	        		optType = Integer.valueOf(map.get("opt").toString());
	        	}
				if(map.containsKey("isEnable")){
					isEnable = Integer.valueOf(map.get("isEnable").toString());
				}
	        }
	        Integer actionId = getActionId(url);
	        if(optType !=5){
        	
        		//Integer optType = getControllerMethodOperationType(joinPoint);
        		//if(optType !=5){//查询暂不做记录
    			//*========数据库日志=========*//  
                UserOperationLog userOperationLog = new UserOperationLog();
                userOperationLog.setAccessTime(DateUtil.sysTimeMillis());
                userOperationLog.setUserId(user.getUserId());
                userOperationLog.setUsername(user.getUsername());
                userOperationLog.setAccessIp(getIpAddress(request));
                userOperationLog.setType(type);
                userOperationLog.setAfterEntity(e.getMessage());
                userOperationLog.setActionId(actionId);
                userOperationLog.setResultStatus(-1);
                userOperationLog.setDesc(desc);
                userOperationLog.setOperationType(optType);
                if(optType != 0){
                	//操作前数据原型
                	userOperationLog.setBeforeEntity(null); 
                }
				logger.info("ASPECTLOG2"+JSON.toJSONString(userOperationLog));
				if (isEnable == 1){
					userOperationLogService.saveUserOperationLog(userOperationLog);
				}
              //  userOperationLogService.saveUserOperationLog(userOperationLog);
	        }   
        }  catch (Exception ex) {    
            //记录本地异常日志    
//            logger.error("==异常通知异常==");    
//            logger.error("异常信息:{}", ex.getMessage());  
        	logger.error(Contant.ERROR_MSG, ex);
        } finally {
			logger.error(Contant.ERROR_MSG, e);
		}
            
             

    }       
    
    /**  
     * 获取注解中对方法的描述信息 用于Controller层注解  
     *  
     * @param joinPoint 切点  
     * @return 方法描述  
     * @throws Exception  
     */    
     @SuppressWarnings("rawtypes")
	public  static Map<String,Object> getServiceMethodMsg(JoinPoint joinPoint,String url)  throws Exception {    
        String targetName = joinPoint.getTarget().getClass().getName();    
        String methodName = joinPoint.getSignature().getName();    
        Object[] arguments = joinPoint.getArgs();    
        Class targetClass = Class.forName(targetName);    
        Method[] methods = targetClass.getMethods();    
        Map<String, Object> map =new HashMap<>();
         for (Method method : methods) { 
             if (method.getName().equals(methodName)) {    
                Class[] clazzs = method.getParameterTypes();    
                 if (clazzs.length == arguments.length) { 
                	 int type = method.getAnnotation(SystemControllerLog. class).type(); 
                	 map.put("type", type);
                	 String description = method.getAnnotation(SystemControllerLog. class).desc(); 
                	 map.put("desc", url+"  "+description);
                	 String operation = method.getAnnotation(SystemControllerLog. class).operationType();
					 int isEnable = method.getAnnotation(SystemControllerLog. class).isEnable();
					 map.put("isEnable", isEnable);
                	 if(operation != null && "select".equals(operation)){
                		 map.put("opt", 5);
                	 }else if(operation != null && "add".equals(operation)){
                		 map.put("opt", 0);
                	 }else if(operation != null && "edit".equals(operation)){
                		 map.put("opt", 1);
                	 }else if(operation != null && "delete".equals(operation)){
                		 map.put("opt", 2);
                	 }else if(operation != null && "export".equals(operation)){
                		 map.put("opt", 3);
                	 }else if(operation != null && "import".equals(operation)){
                		 map.put("opt", 4);
                	 }
                     break;    
                }    
            }    
        }    
         return map;    
    }    
     
     /**  
      * 获取注解中对方法的操作类型 用于Controller层注解  
      *  
      * @param joinPoint 切点  
      * @return 方法描述  
     * @throws ClassNotFoundException 
      * @throws Exception  
      */    
      @SuppressWarnings("rawtypes")
      public static Integer getControllerMethodOperationType(JoinPoint joinPoint) throws ClassNotFoundException {    
         String targetName = joinPoint.getTarget().getClass().getName();    
         String methodName = joinPoint.getSignature().getName();    
         Object[] arguments = joinPoint.getArgs();    
         Class targetClass = Class.forName(targetName);    
         Method[] methods = targetClass.getMethods();    
         String operation = "";    
          for (Method method : methods) {    
              if (method.getName().equals(methodName)) {    
                 Class[] clazzs = method.getParameterTypes();    
                  if (clazzs.length == arguments.length) {    
                	  operation = method.getAnnotation(SystemControllerLog. class).operationType();    
                      break;    
                 }    
             }    
         }
         if(operation!=null && !"".equals(operation)){
        	 if("select".equals(operation)){
        		 return 5;
        	 }else if("add".equals(operation)){
        		 return 0;
        	 }else if("edit".equals(operation)){
        		 return 1;
        	 }else if("delete".equals(operation)){
        		 return 2;
        	 }else if("export".equals(operation)){
        		 return 3;
        	 }else if("import".equals(operation)){
        		 return 4;
        	 }
         }
		return -1;    
     }
      
     /**
     * <b>Description:</b><br> 根据请求的url查询actionid
     * @param url
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年9月6日 下午1:40:47
     */
    private Integer getActionId(String url){
    	String [] urls = url.split("/");
    	 if(urls.length>3){
    		 String modelName=urls[1];
    		 String controllerName=urls[2];
			 String action = url.replace("/" + modelName +"/" + controllerName + "/","");
			 String actionName = action.substring(0, action.indexOf("."));
//    		 String actionName=urls[3].substring(0, urls[3].indexOf("."));
    		 Integer actionId  = actionService.getActionId(modelName, controllerName, actionName);
//    		 if(actionId == null || actionId == 0){
//    			 Action action =new Action();
//    			 action.setActionName(actionName);
//    			 action.setModuleName(modelName);
//    			 action.setControllerName(controllerName);
//    			 action.setIsMenu(0);
//    			 action.setSort(1);
//    			 actionService.insert(action);
//    			 actionId  = action.getActionId();
//    		 }
    		 return actionId;
    	 }
    	 return 0;
     }
   
	
	/** 
	 * 获取用户真实IP地址，不使用request.getRemoteAddr();的原因是有可能用户使用了代理软件方式避免真实IP地址, 
	 * 参考文章： http://developer.51cto.com/art/201111/305181.htm 
	 *  
	 * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值，究竟哪个才是真正的用户端的真实IP呢？ 
	 * 答案是取X-Forwarded-For中第一个非unknown的有效IP字符串。 
	 *  
	 * 如：X-Forwarded-For：*************, *************, *************, 
	 * ************* 
	 *  
	 * 用户真实IP为： ************* 
	 *  
	 * @param request 
	 * @return 
	 */  
	private String getIpAddress(HttpServletRequest request) {  
	    String ip = request.getHeader("x-forwarded-for");  
	    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	        ip = request.getHeader("Proxy-Client-IP");  
	    }  
	    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	        ip = request.getHeader("WL-Proxy-Client-IP");  
	    }  
	    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	        ip = request.getHeader("HTTP_CLIENT_IP");  
	    }  
	    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	        ip = request.getHeader("HTTP_X_FORWARDED_FOR");  
	    }  
	    if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
	        ip = request.getRemoteAddr();  
	    }  
	    return ip;  
	}	
} 

