package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * 客户账户信息
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/customerBankAccount")
public class CustomerBankAccountController {

    /**
     * 客户账户信息列表
     *
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/list")
    public ModelAndView index() {
        return new ModelAndView("vue/view/tradercustomer/customer_bank_account_list");
    }

}