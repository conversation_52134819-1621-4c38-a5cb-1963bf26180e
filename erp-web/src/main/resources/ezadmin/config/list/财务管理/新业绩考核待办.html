<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>新业绩考核待办</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="performanceAssessment" datasource="erp-datasourcetarget" fixednumber="1" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">订单号</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="请输入" style="" alias="ts" jdbctype="VARCHAR" data="" datatype="" oper="EQ" ></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">客户名称</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="请输入" style="" alias="ts" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">归属销售</label>
            <div class="layui-input-inline">
                <xm class=" layui-input list-search-item " type="select" name="CURRENT_USER_ID" placeholder="" style="" alias="tsd" jdbctype="NUMBER" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="JSON" oper="IN" ></xm>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">订单类型</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="select" name="ORDER_TYPE" placeholder="全部" style="" alias="ts" jdbctype="" data="[{&quot;V&quot;:&quot;线上&quot;,&quot;K&quot;:&quot;1,5,7&quot;},{&quot;V&quot;:&quot;线下&quot;,&quot;K&quot;:&quot;0,2,3,4,6,8,9&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">归属销售所在部门</label>
            <div class="layui-input-inline">
                <xm class=" layui-input list-search-item " type="select" name="ORG_ID" placeholder="" style="" alias="to2" jdbctype="NUMBER" data='
           SELECT
	DISTINCT to2.ORG_ID K,
	to2.ORG_NAME V
from
	T_SALEORDER ts
LEFT JOIN T_SALEORDER_DATA tsd ON
	ts.SALEORDER_ID = tsd.SALEORDER_ID
LEFT JOIN T_ORGANIZATION to2 ON
	to2.ORG_ID = ts.ORG_ID
WHERE
	ts.ADD_TIME > *************
	AND ts.STATUS != 3
	AND ts.PAYMENT_STATUS = 2
	AND (ts.INVOICE_STATUS != 2
           OR (tsd.CONTRACT_VERIFY_STATUS != 1 and
               (ts.ADD_TIME < ************* and ts.TOTAL_AMOUNT >= 5000)
               or (ts.ADD_TIME >= ************* and ts.ORDER_TYPE in (1,5,7) and ts.TOTAL_AMOUNT >= 50000)
               or (ts.ADD_TIME >= ************* and ts.ORDER_TYPE not in (1,5,7) and ts.TOTAL_AMOUNT >= 5000))
           OR (ts.TOTAL_AMOUNT >= 5000 AND ts.CONFIRMATION_FORM_AUDIT != 2))
	AND tsd.CURRENT_USER_ID IN (${EZ_SESSION_MY_USER_KEY})' datatype="KVSQL" oper="IN" ></xm>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">订单发货状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="DELIVERY_STATUS" placeholder="" style="" alias="ts" jdbctype="" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">订单收货状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="ARRIVAL_STATUS" placeholder="" style="" alias="ts" jdbctype="" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">单据是否考核</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="INCLUDE_PERFORMANCE" placeholder="" style="" alias=" IF(ts.TOTAL_AMOUNT<5000 and ts.HAVE_ACCOUNT_PERIOD = 0, 0, 1)" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper="="></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">合同审核状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="CONTRACT_VERIFY_STATUS" placeholder="" style="" alias="IFNULL(tsd.CONTRACT_VERIFY_STATUS, 5)" jdbctype="" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;未回传&quot;,&quot;K&quot;:&quot;5&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">确认单审核状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="CONFIRMATION_FORM_AUDIT" placeholder="" style="" alias="ts" jdbctype="" data="[{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">开票状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="INVOICE_STATUS" placeholder="" style="" alias="ts" jdbctype="" data="[{&quot;V&quot;:&quot;未开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>



        <div class=" layui-inline "><label class="layui-form-label">订单创建时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder style=""
                        alias="from_unixtime(ts.ADD_TIME/1000 ,'%Y-%m-%d %h:%i:%s')" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">付款时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="PAYMENT_TIME" placeholder style=""
                        alias="from_unixtime(ts.PAYMENT_TIME/1000 ,'%Y-%m-%d %h:%i:%s')" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-spanlink" url="/orderstream/saleorder/detail.do?saleOrderId=${SALEORDER_ID}&amp;scene=0" opentype="PARENT" windowname="${SALEORDER_NO}" datatype="" data="" style="min-width:200px;
word-break: break-all;position: sticky;" head="18">订单号</th>
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="0" opentype="PARENT" windowname="${TRADER_ID}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
            <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属销售</th>
            <th item_name="ORDER_TYPE" name="ORDER_TYPE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单类型</th>
            <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属销售所在部门	</th>
            <th item_name="STATUS" name="STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;待用户确认&quot;,&quot;K&quot;:&quot;4&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单状态</th>
            <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单原金额</th>
            <th item_name="HAVE_ACCOUNT_PERIOD" name="HAVE_ACCOUNT_PERIOD" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否含账期</th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单创建时间</th>
            <th item_name="PAYMENT_TIME" name="PAYMENT_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单付款时间</th>
            <th item_name="DELIVERY_STATUS" name="DELIVERY_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单发货状态</th>
            <th item_name="ARRIVAL_STATUS" name="ARRIVAL_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单收货状态</th>
            <th item_name="INCLUDE_PERFORMANCE" name="INCLUDE_PERFORMANCE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">单据是否考核</th>
            <th item_name="CONTRACT_VERIFY_STATUS" name="CONTRACT_VERIFY_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;未回传&quot;,&quot;K&quot;:&quot;5&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">合同审核状态</th>
            <th item_name="CONFIRMATION_FORM_AUDIT" name="CONFIRMATION_FORM_AUDIT" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">确认单审核状态</th>
            <th item_name="INVOICE_STATUS" name="INVOICE_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">开票状态</th>

        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY ts.ADD_TIME DESC" groupby="">StringBuilder sql=new StringBuilder("
            SELECT
                   ts.SALEORDER_ID,
                   ts.SALEORDER_NO,
                   ts.TRADER_NAME,
                   tsd.CURRENT_USER_ID,
                   tu2.USERNAME,
                   IF(ts.ORDER_TYPE in (1,5,7),'线上','线下') ORDER_TYPE,
                   to2.ORG_NAME,
                   to2.ORG_ID,
                   ts.STATUS,
                   ts.TOTAL_AMOUNT,
                   ts.HAVE_ACCOUNT_PERIOD,
                   FROM_UNIXTIME(IF(ts.ADD_TIME = 0, NULL, ts.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s')         AS ADD_TIME,
                   FROM_UNIXTIME(IF(ts.PAYMENT_TIME = 0, NULL, ts.PAYMENT_TIME) / 1000, '%Y-%m-%d %H:%i:%s') AS PAYMENT_TIME,
                   ts.DELIVERY_STATUS,
                   ts.ARRIVAL_STATUS,
                   IFNULL(tsd.CONTRACT_VERIFY_STATUS, 5)                                                     AS CONTRACT_VERIFY_STATUS,
                   IF(ts.TOTAL_AMOUNT < 5000 and ts.HAVE_ACCOUNT_PERIOD = 0, 0, 1)                           AS INCLUDE_PERFORMANCE,
                   ts.CONFIRMATION_FORM_AUDIT,
                   ts.INVOICE_STATUS
            from T_SALEORDER ts
                     LEFT JOIN T_SALEORDER_DATA tsd ON
                ts.SALEORDER_ID = tsd.SALEORDER_ID
                     LEFT JOIN T_ORGANIZATION to2 ON to2.ORG_ID = ts.ORG_ID
                     LEFT JOIN T_USER tu2 ON tu2.USER_ID = tsd.CURRENT_USER_ID
            WHERE ts.ADD_TIME > *************
              AND ts.STATUS != 3
              AND ts.PAYMENT_STATUS = 2
              AND (ts.INVOICE_STATUS != 2
                       OR (tsd.CONTRACT_VERIFY_STATUS != 1 and
                           ((ts.ADD_TIME < ************* and ts.TOTAL_AMOUNT >= 5000)
                           or (ts.ADD_TIME >= ************* and ts.ORDER_TYPE in (1,5,7) and ts.TOTAL_AMOUNT >= 50000)
                           or (ts.ADD_TIME >= ************* and ts.ORDER_TYPE not in (1,5,7) and ts.TOTAL_AMOUNT >= 5000)))
                       OR (ts.TOTAL_AMOUNT >= 5000 AND ts.CONFIRMATION_FORM_AUDIT != 2))
");

if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " AND tsd.CURRENT_USER_ID in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}

list=search(sql);
return list;</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <div class=" layui-elem-quote" style="float:right;width:100%;padding: 0">
        <div>1、【单据是否考核】-【是】，【合同审核状态】选择非【审核通过】可筛选考核要求内合同需要回传或审核的订单。</div>
        <div>2、【单据是否考核】-【是】，【确认审核状态】选择非【审核通过】可筛选考核要求内确认单需要回传或审核的订单。</div>
        <div>3、【开票状态】选择非【全部开票】可筛选需开票订单。</div>
    </div>

    <script>
        $(function () {
            $("td[item_name=CONTRACT_VERIFY_STATUS]").each(function (e) {
                if ($(this).text().indexOf("审核通过") == -1) {
                    $(this).css("color", "red");
                    $(this).css("font-weight", "900")
                }
            });
            $("td[item_name=CONFIRMATION_FORM_AUDIT]").each(function (e) {
                if ($(this).text().indexOf("审核通过") == -1) {
                    $(this).css("color", "red");
                    $(this).css("font-weight", "900")
                }
            });

            $("td[item_name=INVOICE_STATUS]").each(function (e) {
                if ($(this).text().indexOf("全部开票") == -1) {
                    $(this).css("color", "red");
                    $(this).css("font-weight", "900")
                }
            })
        })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    });
</script>
</body>
</html>