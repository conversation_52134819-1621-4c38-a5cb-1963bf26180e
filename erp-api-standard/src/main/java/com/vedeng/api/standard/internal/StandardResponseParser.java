package com.vedeng.api.standard.internal;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 标准响应解析器实现
 * 支持解析系统中常见的JSON响应格式，识别错误码和错误信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-21
 */
public class StandardResponseParser implements ResponseParser {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardResponseParser.class);
    
    /**
     * 成功标识的值
     */
    private static final String SUCCESS_TRUE = "true";
    private static final String SUCCESS_1 = "1";
    private static final Integer SUCCESS_CODE_200 = 200;
    private static final Integer SUCCESS_CODE_0 = 0;
    private static final String SUCCESS_CODE_0000 = "0000";
    
    @Override
    public boolean canParse(Object responseData) {
        return responseData instanceof Map;
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public ParseResult parse(Object responseData) {
        if (!canParse(responseData)) {
            logger.info("响应数据不是Map类型，无法解析: {}", responseData != null ? responseData.getClass().getSimpleName() : "null");
            return ParseResult.unparsable(responseData);
        }
        
        Map<String, Object> dataMap = (Map<String, Object>) responseData;
        logger.info("开始解析响应数据: {}", dataMap);
        
        // 尝试不同的响应格式解析策略
        ParseResult result = parseApiResponseFormat(dataMap);
        if (result != null) {
            return result;
        }
        
        result = parseRClassFormat(dataMap);
        if (result != null) {
            return result;
        }
        
        result = parseResultInfoFormat(dataMap);
        if (result != null) {
            return result;
        }
        
        result = parseGenericFormat(dataMap);
        if (result != null) {
            return result;
        }
        
        // 如果都无法解析，返回原数据
        logger.info("无法识别的响应格式，按原数据处理");
        return ParseResult.unparsable(responseData);
    }
    
    /**
     * 解析ApiResponse格式
     * 格式: {success: true/false, code: "0000"/"ERR001", message: "消息", data: {...}}
     */
    private ParseResult parseApiResponseFormat(Map<String, Object> dataMap) {
        Object successObj = dataMap.get("success");
        Object codeObj = dataMap.get("code");
        Object messageObj = dataMap.get("message");
        Object dataObj = dataMap.get("data");
        
        if (successObj != null || codeObj != null) {
            boolean isSuccess = isSuccessValue(successObj) && isSuccessCode(codeObj);
            String businessCode = extractStringValue(codeObj);
            String errorMessage = extractStringValue(messageObj);
            
            logger.debug("ApiResponse格式解析结果: success={}, code={}, message={}", isSuccess, businessCode, errorMessage);
            
            if (isSuccess) {
                return ParseResult.success(dataObj != null ? dataObj : dataMap);
            } else {
                return ParseResult.failure(businessCode, errorMessage, dataMap);
            }
        }
        
        return null;
    }
    
    /**
     * 解析R类格式
     * 格式: {code: 200/-1, msg: "成功"/"失败", data: {...}}
     */
    private ParseResult parseRClassFormat(Map<String, Object> dataMap) {
        Object codeObj = dataMap.get("code");
        Object msgObj = dataMap.get("msg");
        Object dataObj = dataMap.get("data");
        
        if (codeObj != null && isNumericValue(codeObj)) {
            boolean isSuccess = isSuccessCode(codeObj);
            String businessCode = extractStringValue(codeObj);
            String errorMessage = extractStringValue(msgObj);
            
            logger.debug("R类格式解析结果: success={}, code={}, msg={}", isSuccess, businessCode, errorMessage);
            
            if (isSuccess) {
                return ParseResult.success(dataObj != null ? dataObj : dataMap);
            } else {
                return ParseResult.failure(businessCode, errorMessage, dataMap);
            }
        }
        
        return null;
    }
    
    /**
     * 解析ResultInfo格式
     * 格式: {success: true/false, errorCode: "0000"/"ERR001", errorMessage: "消息", data: {...}}
     */
    private ParseResult parseResultInfoFormat(Map<String, Object> dataMap) {
        Object successObj = dataMap.get("success");
        Object errorCodeObj = dataMap.get("errorCode");
        Object errorMessageObj = dataMap.get("errorMessage");
        Object dataObj = dataMap.get("data");
        
        if (successObj != null || errorCodeObj != null) {
            boolean isSuccess = isSuccessValue(successObj) && isSuccessCode(errorCodeObj);
            String businessCode = extractStringValue(errorCodeObj);
            String errorMessage = extractStringValue(errorMessageObj);
            
            logger.debug("ResultInfo格式解析结果: success={}, errorCode={}, errorMessage={}", isSuccess, businessCode, errorMessage);
            
            if (isSuccess) {
                return ParseResult.success(dataObj != null ? dataObj : dataMap);
            } else {
                return ParseResult.failure(businessCode, errorMessage, dataMap);
            }
        }
        
        return null;
    }
    
    /**
     * 解析通用格式（尝试常见字段组合）
     */
    private ParseResult parseGenericFormat(Map<String, Object> dataMap) {
        // 检查是否有明显的错误标识
        for (String errorField : new String[]{"error", "err", "failed", "failure"}) {
            Object errorObj = dataMap.get(errorField);
            if (errorObj != null && isTrueValue(errorObj)) {
                String errorMessage = extractStringValue(dataMap.get("message"));
                if (errorMessage == null) {
                    errorMessage = extractStringValue(dataMap.get("msg"));
                }
                if (errorMessage == null) {
                    errorMessage = extractStringValue(dataMap.get("errorMessage"));
                }
                
                logger.debug("通用格式解析发现错误标识: {}={}", errorField, errorObj);
                return ParseResult.failure(null, errorMessage, dataMap);
            }
        }
        
        return null;
    }
    
    /**
     * 判断是否为成功值
     */
    private boolean isSuccessValue(Object value) {
        if (value == null) {
            return true; // 没有success字段时默认为成功
        }
        
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        
        String strValue = value.toString().toLowerCase().trim();
        return SUCCESS_TRUE.equals(strValue) || SUCCESS_1.equals(strValue);
    }
    
    /**
     * 判断是否为成功的错误码
     */
    private boolean isSuccessCode(Object code) {
        if (code == null) {
            return true; // 没有code字段时默认为成功
        }
        
        if (code instanceof Integer) {
            Integer intCode = (Integer) code;
            return SUCCESS_CODE_200.equals(intCode) || SUCCESS_CODE_0.equals(intCode);
        }
        
        String strCode = code.toString().trim();
        return SUCCESS_CODE_0000.equals(strCode) || "200".equals(strCode) || "0".equals(strCode);
    }
    
    /**
     * 判断是否为数值类型
     */
    private boolean isNumericValue(Object value) {
        if (value instanceof Number) {
            return true;
        }
        
        if (value instanceof String) {
            try {
                Integer.parseInt((String) value);
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否为真值
     */
    private boolean isTrueValue(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        
        if (value instanceof String) {
            String strValue = ((String) value).toLowerCase().trim();
            return SUCCESS_TRUE.equals(strValue) || SUCCESS_1.equals(strValue);
        }
        
        if (value instanceof Integer) {
            return !SUCCESS_CODE_0.equals(value);
        }
        
        return false;
    }
    
    /**
     * 提取字符串值
     */
    private String extractStringValue(Object value) {
        if (value == null) {
            return null;
        }
        
        String strValue = value.toString().trim();
        return strValue.isEmpty() ? null : strValue;
    }
}