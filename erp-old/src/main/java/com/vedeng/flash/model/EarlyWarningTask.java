package com.vedeng.flash.model;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class EarlyWarningTask {
    private Long earlyWarningTaskId;

    private Integer earlyWarningType;

    private Integer relateBusinessId;

    private String businessExtra1;

    private Long businessExtra2;

    private Integer urgingTicketNum;

    private BigDecimal urgingTicketAmount;

    private BigDecimal alreadyInputNum;

    private BigDecimal alreadyInputAmout;

    private Integer waningLevel;

    private Integer taskStatus;

    private String taskDealer;

    private String followUpPerson;

    private Integer followUpNum;

    private String followUpTime;

    private String followUpComment;

    private Boolean sendInitMessage;

    private Integer sendImpendingMessage;

    private Integer sendOverdueMessage;

    private Integer sendOver12hoursMessage;

    private Boolean sendOver48hoursMessage;

    private Integer isDeleted;

    private String addTime;

    private Integer creator;

    private String updateTime;

    private Integer updator;
    private Integer followUpResult;
    private String canTicketTime;
    private BigDecimal canTicketAmount;

    private Long deliveryTime;

    private Integer goodsDay;

    private BigDecimal urgingTicketNumFollowUp;

    private BigDecimal urgingTicketAmountFollowUp;

}

