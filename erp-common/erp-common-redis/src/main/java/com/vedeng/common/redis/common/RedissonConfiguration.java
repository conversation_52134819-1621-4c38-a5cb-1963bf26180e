package com.vedeng.common.redis.common;

import com.vedeng.common.redis.redission.RedissonDistributeLocker;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分布式锁config
 * @date 2022/12/15 16:11
 */
@Configuration
@Slf4j
public class RedissonConfiguration {

    /**
     * 分布式锁实例化并交给工具类
     *
     * @param redissonClient redissonClient
     */
    @Bean
    public RedissonDistributeLocker redissonLocker(RedissonClient redissonClient) {
        RedissonDistributeLocker locker = new RedissonDistributeLocker(redissonClient);
        RedissonLockUtils.setLocker(locker);
        log.info("分布式锁实例化并交给工具类");
        return locker;
    }

}
