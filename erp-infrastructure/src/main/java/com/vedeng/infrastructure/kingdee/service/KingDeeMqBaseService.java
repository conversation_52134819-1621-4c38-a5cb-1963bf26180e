package com.vedeng.infrastructure.kingdee.service;

import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶mq基础能力接口 1.存储发送请求日志 2. 推送消息到mq中
 * @date 2022/8/27 12:40
 */
public interface KingDeeMqBaseService<T extends KingDeeMqBaseDto> {


    /**
     * 注册任务，异步执行
     *
     * @param t 金蝶对象
     */
    void register(T t);

    /**
     * 注册任务，同步执行
     *
     * @param t      金蝶对象
     * @param isSync 是否同步
     */
    void register(T t, boolean isSync);


    /**
     * 取消正在执行中的任务
     *
     * @param t 金蝶对象
     */
    void cancel(T t);

    /**
     * 异步任务执行器
     *
     * @param msgDto KingDeeEventMsgDto
     */
    void execute(KingDeeEventMsgDto msgDto);


}
