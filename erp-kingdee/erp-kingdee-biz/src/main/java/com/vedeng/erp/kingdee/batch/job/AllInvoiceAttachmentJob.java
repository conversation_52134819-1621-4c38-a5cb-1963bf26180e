package com.vedeng.erp.kingdee.batch.job;

import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.processor.BatchBuyOrderExpenseInvoiceFileProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchBuyOrderPhysicalInvoiceFileProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleExpenseInvoiceFileProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSalePhysicalInvoiceFileProcessor;
import com.vedeng.erp.kingdee.batch.writer.CommonFileDataWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 所有类型发票附件推送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/15 13:22
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class AllInvoiceAttachmentJob extends BaseJob {

    @Autowired
    private BatchSalePhysicalInvoiceFileProcessor batchSalePhysicalInvoiceFileProcessor;

    @Autowired
    private BatchSaleExpenseInvoiceFileProcessor batchSaleExpenseInvoiceFileProcessor;

    @Autowired
    private BatchBuyOrderPhysicalInvoiceFileProcessor batchBuyOrderPhysicalInvoiceFileProcessor;

    @Autowired
    private BatchBuyOrderExpenseInvoiceFileProcessor batchBuyOrderExpenseInvoiceFileProcessor;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;

    public Job invoiceAttachmentJob() {
        return jobBuilderFactory.get("invoiceAttachmentJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(pushSalePhysicalBlueInvoicePdfFile())
                .next(pushSalePhysicalBlueInvoiceXmlFile())
                .next(pushSalePhysicalRedInvoicePdfFile())
                .next(pushSalePhysicalRedInvoiceXmlFile())
                .next(pushSaleExpenseBlueInvoicePdfFile())
                .next(pushSaleExpenseBlueInvoiceXmlFile())
                .next(pushSaleExpenseRedInvoicePdfFile())
                .next(pushSaleExpenseRedInvoiceXmlFile())
                .next(pushBuyPhysicalBlueInvoicePdfFile())
                .next(pushBuyPhysicalBlueInvoiceXmlFile())
                .next(pushBuyPhysicalRedInvoicePdfFile())
                .next(pushBuyPhysicalRedInvoiceXmlFile())
                .next(pushBuyExpenseBlueInvoicePdfFile())
                .next(pushBuyExpenseBlueInvoiceXmlFile())
                .next(pushBuyExpenseRedInvoicePdfFile())
                .next(pushBuyExpenseRedInvoiceXmlFile())

                .next(pushSaleAfterExpenseInvoicePdfFile())
                .next(pushSaleAfterExpenseInvoiceXmlFile())


                .next(pushSaleAfterInvoicePdfFile())
                .next(pushSaleAfterInvoiceXmlFile())

                .build();
    }

    /**
     * 推送销售实物蓝票Pdf附件
     *
     * @return
     */
    private Step pushSalePhysicalBlueInvoicePdfFile() {
        return stepBuilderFactory.get("销售实物蓝票Pdf附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderPhysicalBlueInvoicePdfFileReader(null, null))
                .processor(batchSalePhysicalInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送销售实物蓝票Xml附件
     *
     * @return
     */
    private Step pushSalePhysicalBlueInvoiceXmlFile() {
        return stepBuilderFactory.get("销售实物蓝票Xml附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderPhysicalBlueInvoiceXmlFileReader(null, null))
                .processor(batchSalePhysicalInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送销售实物红票Pdf附件
     *
     * @return
     */
    private Step pushSalePhysicalRedInvoicePdfFile() {
        return stepBuilderFactory.get("销售实物红票Pdf附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderPhysicalRedInvoicePdfFileReader(null, null))
                .processor(batchSalePhysicalInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送销售实物红票Xml附件
     *
     * @return
     */
    private Step pushSalePhysicalRedInvoiceXmlFile() {
        return stepBuilderFactory.get("销售实物红票Xml附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderPhysicalRedInvoiceXmlFileReader(null, null))
                .processor(batchSalePhysicalInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送销售费用蓝票Pdf附件
     *
     * @return
     */
    private Step pushSaleExpenseBlueInvoicePdfFile() {
        return stepBuilderFactory.get("销售费用蓝票Pdf附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderExpenseBlueInvoicePdfFileReader(null, null))
                .processor(batchSaleExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送销售费用蓝票Xml附件
     *
     * @return
     */
    private Step pushSaleExpenseBlueInvoiceXmlFile() {
        return stepBuilderFactory.get("销售费用蓝票Xml附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderExpenseBlueInvoiceXmlFileReader(null, null))
                .processor(batchSaleExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 推送销售费用红票Pdf附件
     *
     * @return
     */
    private Step pushSaleExpenseRedInvoicePdfFile() {
        return stepBuilderFactory.get("销售费用红票Pdf附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderExpenseRedInvoicePdfFileReader(null, null))
                .processor(batchSaleExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送销售费用红票Xml附件
     *
     * @return
     */
    private Step pushSaleExpenseRedInvoiceXmlFile() {
        return stepBuilderFactory.get("销售费用红票Xml附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderExpenseRedInvoiceXmlFileReader(null, null))
                .processor(batchSaleExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送采购实物红票Pdf附件
     *
     * @return
     */
    private Step pushBuyPhysicalRedInvoicePdfFile() {
        return stepBuilderFactory.get("采购实物红票Pdf附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderPhysicalRedInvoicePdfFileReader(null, null))
                .processor(batchBuyOrderPhysicalInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送采购实物红票Xml附件
     *
     * @return
     */
    private Step pushBuyPhysicalRedInvoiceXmlFile() {
        return stepBuilderFactory.get("采购实物红票Xml附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderPhysicalRedInvoiceXmlFileReader(null, null))
                .processor(batchBuyOrderPhysicalInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送采购实物蓝票Pdf附件
     *
     * @return
     */
    private Step pushBuyPhysicalBlueInvoicePdfFile() {
        return stepBuilderFactory.get("采购实物蓝票Pdf附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderPhysicalBlueInvoicePdfFileReader(null, null))
                .processor(batchBuyOrderPhysicalInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送采购实物蓝票Xml附件
     *
     * @return
     */
    private Step pushBuyPhysicalBlueInvoiceXmlFile() {
        return stepBuilderFactory.get("采购实物蓝票Xml附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderPhysicalBlueInvoiceXmlFileReader(null, null))
                .processor(batchBuyOrderPhysicalInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送采购费用蓝票Pdf附件
     *
     * @return
     */
    private Step pushBuyExpenseBlueInvoicePdfFile() {
        return stepBuilderFactory.get("采购费用蓝票Pdf附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderExpenseBlueInvoicePdfFileReader(null, null))
                .processor(batchBuyOrderExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送采购费用蓝票Xml附件
     *
     * @return
     */
    private Step pushBuyExpenseBlueInvoiceXmlFile() {
        return stepBuilderFactory.get("采购费用蓝票Xml附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderExpenseBlueInvoiceXmlFileReader(null, null))
                .processor(batchBuyOrderExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送采购费用红票Pdf附件
     *
     * @return
     */
    private Step pushBuyExpenseRedInvoicePdfFile() {
        return stepBuilderFactory.get("采购费用红票Pdf附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderExpenseRedInvoicePdfFileReader(null, null))
                .processor(batchBuyOrderExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 推送采购费用红票Xml附件
     *
     * @return
     */
    private Step pushBuyExpenseRedInvoiceXmlFile() {
        return stepBuilderFactory.get("采购费用红票Xml附件推送")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderExpenseRedInvoiceXmlFileReader(null, null))
                .processor(batchBuyOrderExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 推送销售售后手续费蓝票Pdf附件
     *
     * @return
     */
    private Step pushSaleAfterExpenseInvoicePdfFile() {
        return stepBuilderFactory.get("推送销售售后手续费蓝票Pdf附件")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleAfterInvoiceExpensePdfFileReader(null, null))
                .processor(batchSaleExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 推送销售售后手续费蓝票xml附件
     *
     * @return
     */
    private Step pushSaleAfterExpenseInvoiceXmlFile() {
        return stepBuilderFactory.get("推送销售售后手续费蓝票xml附件")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleAfterInvoiceExpenseXmlFileReader(null, null))
                .processor(batchSaleExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 推送销售售后录蓝票Pdf附件
     *
     * @return
     */
    private Step pushSaleAfterInvoicePdfFile() {
        return stepBuilderFactory.get("推送销售售后录蓝票Pdf附件")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleAfterInvoicePdfFileReader(null, null))
                .processor(batchBuyOrderExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 推送销售售后录蓝票xml附件
     *
     * @return
     */
    private Step pushSaleAfterInvoiceXmlFile() {
        return stepBuilderFactory.get("推送销售售后录蓝票xml附件")
                .<BatchInvoiceDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleAfterInvoiceXmlFileReader(null, null))
                .processor(batchBuyOrderExpenseInvoiceFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }




    /**
     * 销售实物蓝票Pdf附件reader
     *
     * @param beginTime 开始时间（xxl传入）
     * @param endTime   结束时间 （xxl传入）
     * @return BatchInvoiceDto
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderPhysicalBlueInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(505)
                .colorType(2)
                .isEnable(1)
                .formIdList(Arrays.asList(KingDeeFormConstant.SALE_VAT_SPECIAL_INVOICE, KingDeeFormConstant.SALE_VAT_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 销售实物蓝票Xml附件reader
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderPhysicalBlueInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(505)
                .colorType(2)
                .isEnable(1)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }

    /**
     * 销售实物红票Pdf附件reader
     *
     * @param beginTime 开始时间（xxl传入）
     * @param endTime   结束时间 （xxl传入）
     * @return BatchInvoiceDto
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderPhysicalRedInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 销售开票
                .type(505)
                .formIdList(Arrays.asList(KingDeeFormConstant.SALE_VAT_SPECIAL_INVOICE, KingDeeFormConstant.SALE_VAT_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 销售实物红票Xml附件reader
     *
     * @param beginTime 开始时间（xxl传入）
     * @param endTime   结束时间 （xxl传入）
     * @return BatchInvoiceDto
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderPhysicalRedInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 销售开票
                .type(505)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }

    /**
     * 销售费用蓝票Pdf附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderExpenseBlueInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(505)
                .colorType(2)
                .isEnable(1)
                .formIdList(Arrays.asList(KingDeeFormConstant.OUTPUT_FEE_SPECIAL_INVOICE, KingDeeFormConstant.OUTPUT_FEE_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 销售费用蓝票Xml附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderExpenseBlueInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(505)
                .colorType(2)
                .isEnable(1)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }

    /**
     * 销售费用红票Pdf附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderExpenseRedInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 销售开票
                .type(505)
                .formIdList(Arrays.asList(KingDeeFormConstant.OUTPUT_FEE_SPECIAL_INVOICE, KingDeeFormConstant.OUTPUT_FEE_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 销售费用红票Xml附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderExpenseRedInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 销售开票
                .type(505)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }

    /**
     * 采购实物红票Pdf附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderPhysicalRedInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                .formIdList(Arrays.asList(KingDeeFormConstant.PURCHASE_VAT_SPECIAL_INVOICE, KingDeeFormConstant.PURCHASE_VAT_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 采购实物红票Xml附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderPhysicalRedInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }

    /**
     * 采购实物蓝票Pdf附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderPhysicalBlueInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                .formIdList(Arrays.asList(KingDeeFormConstant.PURCHASE_VAT_SPECIAL_INVOICE, KingDeeFormConstant.PURCHASE_VAT_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 采购实物蓝票Xml附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderPhysicalBlueInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }

    /**
     * 采购费用蓝票Pdf附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderExpenseBlueInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购费用开票
                .type(4126)
                .formIdList(Arrays.asList(KingDeeFormConstant.INPUT_FEE_SPECIAL_INVOICE, KingDeeFormConstant.INPUT_FEE_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 采购费用蓝票Xml附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderExpenseBlueInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购费用开票
                .type(4126)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }


    /**
     * 采购费用蓝票Pdf附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderExpenseRedInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(4126)
                .formIdList(Arrays.asList(KingDeeFormConstant.INPUT_FEE_SPECIAL_INVOICE, KingDeeFormConstant.INPUT_FEE_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 采购费用蓝票Xml附件读取
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderExpenseRedInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(4126)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }




    /**
     * 推送售后手续费蓝票Pdf附件reader
     *
     * @param beginTime 开始时间（xxl传入）
     * @param endTime   结束时间 （xxl传入）
     * @return BatchInvoiceDto
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleAfterInvoiceExpensePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(504)
                .colorType(2)
                .isEnable(1)
                .tag(1)
                .formIdList(Arrays.asList(KingDeeFormConstant.OUTPUT_FEE_SPECIAL_INVOICE, KingDeeFormConstant.OUTPUT_FEE_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 推送售后手续费蓝票xml附件reader
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleAfterInvoiceExpenseXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(504)
                .colorType(2)
                .tag(1)
                .isEnable(1)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }



    /**
     * 推送售后手续费蓝票Pdf附件reader
     *
     * @param beginTime 开始时间（xxl传入）
     * @param endTime   结束时间 （xxl传入）
     * @return BatchInvoiceDto
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleAfterInvoicePdfFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(504)
                .colorType(2)
                .isEnable(1)
                .tag(2)
                .formIdList(Arrays.asList(KingDeeFormConstant.INPUT_FEE_SPECIAL_INVOICE, KingDeeFormConstant.INPUT_FEE_PLAIN_INVOICE))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoicePdfFileReader", batchInvoiceDto);
    }

    /**
     * 推送售后手续费蓝票xml附件reader
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleAfterInvoiceXmlFileReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(504)
                .colorType(2)
                .tag(2)
                .isEnable(1)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "commonInvoiceXmlFileReader", batchInvoiceDto);
    }

}