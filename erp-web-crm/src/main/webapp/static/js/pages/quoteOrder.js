
void function () {

    let quoteorderId = GLOBAL.getQuery('id');

    if (!quoteorderId) {
        window.location.href = "/crm/404";
    }

    new Vue({
        el: '#page-container',
        data: {
            quoteTableHeaders: [
                {
                    label: "",
                    width: "29px",
                    vertical: 'center'
                },
                {
                    label: "报价产品信息",
                    width: "300px"
                },
                {
                    label: "主要参数",
                    width: "280px"
                },
                {
                    label: "销售属性信息",
                    width: "216px"
                },
                {
                    label: "售后政策",
                    width: "137px"
                },
                {
                    label: "参考信息",
                    width: "185px"
                },
                {
                    label: "报备信息",
                    width: "205px"
                },
                {
                    label: "备注信息",
                    width: "200px"
                },
                {
                    key: 'option',
                    label: "操作",
                    width: "112px"
                },
            ],
            list: [],
            showList: [],
            firstLoading: true,
            hasNeeds: false,
            isShowImportNeeds: false, //导入需求
            isShowMultiAdd: false, //批量添加
            isShowSingleAdd: false, //单个新增产品
            tableSelectedList: [],
            quoteorderId: quoteorderId,
            quoteorderNo: '',
            importErrorMsg: '',
            importFile: null,
            multiValue: '',
            multiAddErrorMsg: '',
            //单个新增产品所用字段
            singleAddValue: '',
            singleAddErrorMsg: '',
            singleAddNeedsId: '',
            singleAddPrevGoods: null,
            //end
            //所有产品负责人
            productMangerList: [],
            //产品负责人过滤列表
            productMangerFilterList: [],
            //报备状态过滤列表
            reportStatusFilterList: [{
                label: '未报备',
                value: '-1'
            }, {
                label: '无需报备',
                value: '0'
            }, {
                label: '报备中',
                value: '1'
            }, {
                label: '报备成功',
                value: '2'
            }, {
                label: '报备失败',
                value: '3'
            }],
            //已筛选产品负责人
            pmFilterObj: {},
            //已筛选报备状态
            reportStatusFilterObj: {},
            //报备状态弹层radio列表
            reportStatusList: [{
                label: '成功',
                value: '2'
            }, {
                label: '失败',
                value: '3'
            }, {
                label: '无需',
                value: '0'
            },],
            //报价咨询弹层
            isShowConsultationDialog: false,
            consultationContent: [{
                label: '货期',
                value: '1'
            }, {
                label: '价格',
                value: '2'
            }, {
                label: '报备',
                value: '3'
            }],
            consultationValue: [],
            defaultConsultationUser: [],
            consultationUserList: [],
            consultationQuoteorderGoodsIds: [],
            //end
            totalPrice: 0, //总价
            //申请授权书信息
            quoteApplyInfo: {},
            //建群字段
            groupChatHeaders: [{
                label: "产品负责人",
                width: "220px",
                key: 'userName'
            },
            {
                label: "职位",
                width: "200px",
                key: 'position'
            },
            {
                label: "所属部门",
                width: "390px",
                key: 'department'
            },],
            chatUserList: [],
            isShowChatDialog: false,
            chatSelectedUserList: [],
            isBuildChat: false,
            chatName: '',
            //数据更新轮询周期
            syncLocal: 0, //获焦位置上报周期
            syncLocalInter: null,
            localListObj: {},
            syncLocalPosition: '',
            syncLocalPositionValue: '',
            syncCycle: 0, //编辑字段轮询周期
            syncCycleInter: null,
            syncCycleUpdateTime: 0,
            priceChangeObj: {}, //价格改变的对象
            numChangeObj: {},//数量改变的对象
            timeChangeObj: {},//货期改变的对象,
            oldGoodsLength: 0,
            oldNeedsLength: 0,
            //消息提醒数
            taskNum: 0,
            productNum: 0,
            //需求更新时间
            needUpdateTime: '',
            //商机信息
            businessStatus: '',
            businessStage: '',
            businessInfo: {},
            //报价单状态
            quoteValidStatus: 0,
            //表头悬浮高度
            tableFixedTop: layout_hidden_value ? 114 : 164,
            isContentHeaderFixed: false,
            isOnUpdating: false,
            helpUrl: '', //操作帮助
            isShowShareDialog: false, //分享报价
            shareTxt: '您好，根据您的采购需求，我为您制作一份报价单，请您核实，若对商品信息无异议可点击页面下方“立即下单”按钮一键生成订单。详情点击：',
            shareQuoteInfo: {}, //分享的数据
            selectProdDialogType: '', //商品选型弹层类型 'needs'单个需求添加 'prod'无需求添加
            isShowCustomAddDialog: false, //手动添加产品弹层
            customDialogType: '', //edit编辑 add添加
            customAddInfo: {
                goodsName: '',
                brandName: '',
                brandId: '',
                model: '',
                unitName: '',
                paramContent: '',
                imgUrl: ''
            },
            //品牌下拉数据
            customBrandRemoteInfo: {
                url: '/crm/quote/profile/searchBrand',
                paramsType: 'url',
                paramsKey: 'brandName',
                parseLabel: 'brandName',
                parseValue: 'brandId'
            },
            isShowBusinessInfo: false, //商机信息弹层
            isShowReqDescription: false, //需求描述弹层
            isAuthEditReqDescription: false, //是否有权限编写需求
            resDescription: '', //需求描述
            isShowAddRemark: false, //添加备注弹层
            isShowRemarkDetail: false, //查看备注弹层
            remarkInfoList: [],
            remarkEditValue: '', //备注弹层值
            editingRow: null,
            priceHistoryTimeout: null,
            priceHistoryList: [],
            priceHistoryCurrentId: '',
            priceHistoryLoading: true,
            priceHistoryHoverId: '',
            priceHistoryFocusId: '',
            needResetPriceHistory: false,
            resetPriceHistoryTimeout: null,
            attachmentsNum: 0, //报价单附件数量
            stayTime: '',
            isShowOptionTip: false,
            numberChangeTimeout: null, //防抖
            goodsTipInfo: {}, //气泡提示产品信息缓存
            isGoodsTipLoading: false,
            //转订单--start
            isShowToOrder: false,
            toorderOrderType: '',
            toorderSaleOrderNo: '',
            toorderSaleOrderNoError: '',
            ToOrderTypes: [
                { label: '新建订单', tip: '（将当前商机转成新的订单）', value: '1' },
                { label: '关联订单', tip: '（将当前商机与已有订单进行关联）', value: '2' }
            ],
            canToOrderSubmit: true,
            //--end
            //商品审核状态枚举
            parseProdStatus: {
                0: '待完善',
                1: '审核中',
                2: '审核不通过',
                3: '审核通过',
                4: '删除',
                5: '待提交审核'
            },
            headerTaskNum: 0, //头部任务数量
            isFilterPrice: false,
            chargeInfo: {}, //群聊审核人员信息
        },
        computed: {
            isAuthAddProduct() {
                return this.businessStage != 6 && this.businessStage != 5 && this.quoteValidStatus == 0;
            },
            isAuthImport() {
                return this.businessStage != 6 && this.businessStage != 5 && this.quoteValidStatus == 0 && this.businessInfo.businessType == 5703;
            },
            isAuthValid() {
                return this.businessStage != 6 && this.businessStage != 5;
            },
            isAuthInvalid() {
                return this.businessStage != 6 && this.businessStage != 5;
            },
            isAuthExport() {
                return this.businessStage != 6 && this.quoteValidStatus == 1;
            },
            isAuthApply() {
                return this.businessStage != 6 && this.quoteValidStatus == 1;
            },
            isAuthChat() {
                return this.businessStage != 6 && this.businessStage != 5 && this.quoteValidStatus == 0;
            },
            isAuthDelete() {
                return GLOBAL.auth('C0223') && this.businessStage != 6 && this.businessStage != 5 && this.quoteValidStatus == 0;
            },
            isAuthConsultation() {
                return GLOBAL.auth('C0217') && this.businessStage != 6 && this.businessStage != 5 && this.quoteValidStatus == 0;
            },
            isAuthShare() {
                // return GLOBAL.auth('C0217') && this.businessStage != 6 && this.businessStage != 5 && this.quoteValidStatus == 0;
                return this.businessStage != 6 && this.businessStage != 5 && this.quoteValidStatus == 1;
            },
            isAuthToOrder() {
                return this.businessStage != 6 && this.businessStage != 5 && this.quoteValidStatus == 1;
            },
            tagsShow() {
                let arr = [];
                if (this.businessInfo.tags && this.businessInfo.tags.length) {
                    this.businessInfo.tags.forEach(item => {
                        if (item.name) {
                            arr.push(item.name);
                        }
                    })
                }
                return arr.join(' / ');
            },
        },
        mounted() {
            this.initQuoteList();
            this.getProductMangerList();
            this.getFilterProductManagerList();
            this.getTaskInfo();

            this.isAuthEditReqDescription = GLOBAL.auth('C0230');

            window.addEventListener('scroll', this.checkContentFixed);
        },
        methods: {
            getTaskInfo() {
                this.$axios.post('/crm/quote/profile/taskTips?quoteorderId=' + this.quoteorderId).then(({ data }) => {
                    if (data.success) {
                        this.taskNum = data.data.taskNum;
                        this.productNum = data.data.productNum;

                        if (this.taskNum || this.productNum) {
                            this.tableFixedTop = layout_hidden_value ? 152 : 202;
                        }
                    }
                })
            },
            getHeaderTaskNum() {
                this.$axios.post('/crm/task/profile/getCurrentUserTodoCount', {
                    bizList: [{
                        bizType: 1,
                        bizId: this.businessInfo.businessChanceId
                    }, {
                        bizType: 3,
                        bizId: this.businessInfo.businessChanceId
                    }],
                    listType: 1
                }).then(({ data }) => {
                    if (data.success) {
                        let headerTaskNum = data.data || 0;
                        this.headerTaskNum = headerTaskNum > 99 ? '99+' : headerTaskNum;
                    }
                })
            },
            checkContentFixed() {
                if (window.scrollY > 20) {
                    this.isContentHeaderFixed = true;
                } else {
                    this.isContentHeaderFixed = false;
                }
            },
            initQuoteList(noloading) {
                if (!noloading) {
                    GLOBAL.showGlobalLoading();
                }
                this.$axios.post('/crm/quote/profile/queryQuoteDetail?quoteorderId=' + quoteorderId).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        let quoteorderNeedsList = data.data.quoteorderNeedsList || [];
                        let crmQuoteorderGoodsList = data.data.crmQuoteorderGoodsList || [];
                        let list = [];

                        //报价单基本信息
                        let quoteInfo = data.data.crmQuoteOrderDto;
                        this.quoteorderNo = quoteInfo.quoteorderNo;
                        this.quoteValidStatus = quoteInfo.validStatus;
                        this.resDescription = quoteInfo.needsDesc || '';
                        this.attachmentsNum = quoteInfo.fileCount || 0;
                        if (!this.quoteApplyInfo.applyName) {
                            this.quoteApplyInfo = quoteInfo.quoteApplyDto;
                        }
                        this.isBuildChat = quoteInfo.isBuildChat;
                        this.chatName = quoteInfo.chatName || '';
                        if (quoteInfo.traderName) {
                            document.title = quoteInfo.traderName + '报价单'
                        }

                        //已生效去掉操作
                        if (quoteInfo.validStatus == 1 && this.firstLoading) {
                            this.quoteTableHeaders.splice(this.quoteTableHeaders.length - 1, 1);
                        }

                        //配置信息更新
                        let config = data.data.quoteConfigDto;
                        this.defaultConsultationUser = config.defaultUser || [];
                        this.helpUrl = config.helpUrl || '';

                        if (quoteInfo.validStatus == 0 && !this.syncLocal) {
                            this.syncCycle = config.syncCycle || 5;
                            this.syncLocal = config.syncLocal || 5;
                            this.startWatchingData();
                            this.startWatchingLocation();

                            this.stayTime = new Date().getTime();
                            document.querySelector('body').onmousemove = () => {
                                this.stayTime = new Date().getTime();
                            };

                            this.listenMousemove();
                        }

                        //商机信息
                        let businessInfo = data.data.crmBusinessChanceDto;
                        if (businessInfo.businessChanceId && !this.businessInfo.businessChanceId) {
                            //获取头部任务数量
                            this.getBusinessInfo(businessInfo.businessChanceId);
                            this.businessStatus = businessInfo.status;
                            this.businessStage = businessInfo.stage;
                        }

                        //报价单列表
                        if (quoteorderNeedsList && quoteorderNeedsList.length) {
                            this.needUpdateTime = quoteorderNeedsList[0].addTime;

                            if (!this.hasNeeds) {
                                this.quoteTableHeaders.splice(1, 0, {
                                    label: "需求产品信息",
                                    width: "180px"
                                })
                                this.quoteTableHeaders.splice(2, 0, {
                                    label: "需求备注",
                                    width: "120px"
                                })
                            }

                            this.hasNeeds = true;

                            quoteorderNeedsList.forEach(needItem => {
                                let hasInsert = false;
                                crmQuoteorderGoodsList.forEach(goodsItem => {
                                    if (needItem.quoteorderGoodsIds && needItem.quoteorderGoodsIds.length) {
                                        if (needItem.quoteorderGoodsIds.indexOf(goodsItem.quoteorderGoodsId) !== -1) {
                                            goodsItem.hasInsert = true;

                                            if (hasInsert) {
                                                list.push({
                                                    goodsInfo: goodsItem,
                                                    parent: needItem
                                                })
                                            } else {
                                                hasInsert = true;
                                                list.push({
                                                    needsInfo: needItem,
                                                    goodsInfo: goodsItem,
                                                    rowspan: needItem.quoteorderGoodsIds.length
                                                })
                                            }
                                        }
                                    }
                                })

                                if (!hasInsert) {
                                    list.push({
                                        needsInfo: needItem,
                                        rowspan: 1,
                                    })
                                }
                            });

                            crmQuoteorderGoodsList.forEach(item => {
                                if (!item.hasInsert) {
                                    list.push({
                                        goodsInfo: item,
                                        needEmpty: true
                                    })
                                }
                            })
                        } else {
                            if (this.hasNeeds) {
                                this.quoteTableHeaders.splice(1, 2)
                            }

                            this.hasNeeds = false;

                            crmQuoteorderGoodsList.forEach(item => {
                                list.push({
                                    goodsInfo: item
                                })
                            })
                        }
                        this.oldGoodsLength = crmQuoteorderGoodsList.length;
                        this.oldNeedsLength = quoteorderNeedsList.length;
                        this.list = list;

                        this.firstLoading = false;

                        this.calcTotalPrice();
                        this.checkFilterList();
                    } else {
                        this.$message.warn(data.message);
                    }
                })
            },
            getBusinessInfo(id) {
                this.$axios.get(`/crm/presales/profile/detail?dataId=${id}&type=2`).then(({ data }) => {
                    if (data.success) {
                        this.businessInfo = {
                            businessChanceId: id,
                            ...data.data
                        }
                        this.getHeaderTaskNum();
                    }
                })
            },
            getProductMangerList() {
                this.$axios.post('/crm/quote/profile/querySupplierUser').then(({ data }) => {
                    if (data.success) {
                        let list = [];
                        data.data.forEach(item => {
                            list.push({
                                label: item.userName,
                                value: item.userId,
                                avatar: item.headPic || GLOBAL.defaultAvatar
                            })
                        })

                        this.productMangerList = list;
                    }
                })
            },
            getFilterProductManagerList() {
                let filterList = [];
                let filterObj = {};

                this.list.forEach(item => {
                    if (item.goodsInfo) {
                        if (item.goodsInfo.productManager && item.goodsInfo.productManager.length) {
                            item.goodsInfo.productManager.forEach(pm => {
                                if (!filterObj[pm.userId]) {
                                    filterList.push({
                                        label: pm.username,
                                        value: pm.userId,
                                        avatar: pm.aliasHeadPicture || GLOBAL.defaultAvatar
                                    })
                                    filterObj[pm.userId] = 1;
                                }
                            })
                        }
                    } else if (item.needsInfo && item.needsInfo.headUserList && item.needsInfo.headUserList.length) {
                        item.needsInfo.headUserList.forEach(pm => {
                            if (pm.userId && !filterObj[pm.userId]) {
                                filterList.push({
                                    label: pm.username,
                                    value: pm.userId,
                                    avatar: pm.aliasHeadPicture || GLOBAL.defaultAvatar
                                })
                                filterObj[pm.userId] = 1;
                            }
                        })
                    }
                })
                this.productMangerFilterList = filterList;

                for (let item in this.pmFilterObj) {
                    if (!filterObj[item]) {
                        delete this.pmFilterObj[item];
                    }
                }
            },
            hanlderMultiAddInputBlur() {
                let multiValue = this.multiValue.trim().replace(/[^vV\d]+/g, '');
                multiValue = multiValue.toUpperCase();
                if (multiValue) {
                    let skuIds = multiValue.split('V');
                    let values = [];
                    skuIds.forEach(item => {
                        if (item) {
                            values.push('V' + item);
                        }
                    })
                    this.multiValue = values.join('\n');
                } else {
                    this.multiValue = "";
                }

                this.multiAddErrorMsg = "";
                this.validMultiValue();
            },
            validMultiValue() {
                if (!this.multiValue) {
                    this.multiAddErrorMsg = "请输入需要添加商品的订货号";
                    return false;
                }

                let multiSkuNo = this.multiValue.split('\n');

                if (multiSkuNo.length > 200) {
                    this.multiAddErrorMsg = "单次操作最多支持选择200个订货号";
                    return false;
                }

                let styleRight = true;
                let sameList = [];
                let filterObj = {};
                let seletedArr = [];

                multiSkuNo.forEach(item => {
                    if (!/^[Vv]\d{6}$/.test(item)) {
                        styleRight = false;
                    }

                    let toUpperCaseItem = item.toUpperCase();

                    if (!filterObj[toUpperCaseItem]) {
                        filterObj[toUpperCaseItem] = 1;
                    } else if (sameList.indexOf(toUpperCaseItem) === -1) {
                        sameList.push(toUpperCaseItem)
                    }

                    this.list.forEach(listItem => {
                        if (listItem.goodsInfo && listItem.goodsInfo.skuNo === toUpperCaseItem && seletedArr.indexOf(toUpperCaseItem) === -1) {
                            seletedArr.push(toUpperCaseItem);
                        }
                    })
                })

                if (!styleRight) {
                    this.multiAddErrorMsg = "输入格式错误，请检查并处理内容格式后重新提交";
                    return false;
                }

                if (sameList.length) {
                    this.multiAddErrorMsg = `订货号“${sameList.join(',')}”存在重复，请检查并处理后重新提交`;
                    return false;
                }

                if (seletedArr.length) {
                    this.multiAddErrorMsg = `订货号“${seletedArr.join(',')}”商品已选择，请检查并处理后重新提交`;
                    return false;
                }

                return true;
            },
            multiAddProds() {
                if (this.validMultiValue()) {
                    this.multiAddProdsReq(this.multiValue)
                }
            },
            multiAddProdsReq(skuNos) {
                GLOBAL.showGlobalLoading();
                this.$axios.post('/crm/quote/profile/batchAddGoods', {
                    quoteorderId,
                    skuNos: skuNos
                }).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        this.$message.success(data.message || '添加成功');
                        this.initQuoteList();
                        this.hideMultiAdd();
                    } else {
                        this.$popup.warn({
                            message: data.message || '添加失败',
                            buttons: [{
                                txt: '我知道了',
                                btnClass: 'confirm',
                            }]
                        });
                    }
                })
            },
            showMultiAdd() {
                if (!GLOBAL.auth('C0210')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                this.isShowMultiAdd = true;
                this.multiValue = "";
                this.multiAddErrorMsg = "";
            },
            hideMultiAdd() {
                this.isShowMultiAdd = false;
            },
            //选择产品弹层后回调
            handlerProdSelect(skuNos) {
                if (this.selectProdDialogType === 'prod') {
                    this.multiAddProdsReq(skuNos.join(','))
                } else {
                    GLOBAL.showGlobalLoading();

                    let needsInfo = this.editingRow.needsInfo || this.editingRow.parent;

                    this.$axios.post('/crm/quote/profile/batchAddGoodsNeeds', {
                        quoteorderId: this.quoteorderId,
                        quoteorderNeedsId: needsInfo.quoteorderNeedsId,
                        skuNos
                    }).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.$message.success(data.message || '添加成功');
                            this.initQuoteList();
                        } else {
                            this.$message.warn(data.message);
                        }
                    })
                }
            },
            checkSingleProd() {
                let sku = this.singleAddValue.trim();

                if (!sku) {
                    this.singleAddErrorMsg = '请输入需要添加商品的订货号';
                    return false;
                }

                if (!/^[vV]\d{6}/.test(sku)) {
                    this.singleAddErrorMsg = '输入格式错误，请检查并处理内容格式后重新提交';
                    return false;
                }

                let hasInsert = false;

                this.list.forEach(listItem => {
                    if (listItem.goodsInfo && listItem.goodsInfo.skuNo === sku.toUpperCase()) {
                        hasInsert = true;
                    }
                })

                if (hasInsert) {
                    this.singleAddErrorMsg = `订货号“${sku.toUpperCase()}”商品已选择，请检查并处理后重新提交`;
                    return false;
                }

                this.singleAddErrorMsg = '';
                return true;
            },
            // singleAddProd() {
            //     if (this.checkSingleProd()) {
            //         GLOBAL.showGlobalLoading();

            //         let reqData = {
            //             quoteorderId: this.quoteorderId,
            //             quoteorderNeedsId: this.singleAddNeedsId,
            //             skuNo: this.singleAddValue.trim()
            //         };

            //         if (this.singleAddPrevGoods) {
            //             reqData.oldSkuNo = this.singleAddPrevGoods.skuNo;
            //             reqData.quoteorderGoodsId = this.singleAddPrevGoods.quoteorderGoodsId;
            //         }

            //         this.$axios.post('/crm/quote/profile/singleAddGoods', reqData).then(({ data }) => {
            //             GLOBAL.hideGlobalLoading();
            //             if (data.success) {
            //                 this.hideSingleAdd();
            //                 this.initQuoteList();
            //             } else {
            //                 this.$popup.warn({
            //                     message: data.message || '添加失败',
            //                     buttons: [{
            //                         txt: '我知道了',
            //                         btnClass: 'confirm',
            //                     }]
            //                 });
            //             }
            //         })
            //     }
            // },
            showSingleAdd(data, prev) {
                if (!GLOBAL.auth('C0210')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                this.singleAddValue = "";
                this.singleAddErrorMsg = "";
                this.singleAddPrevGoods = null;

                this.isShowSingleAdd = true;
                this.singleAddNeedsId = data.needsInfo ? data.needsInfo.quoteorderNeedsId : '';

                console.log(prev)
                if (prev && prev.skuNo) {
                    this.singleAddPrevGoods = prev;
                }
            },
            hideSingleAdd() {
                this.isShowSingleAdd = false;
            },
            handlerTableListSelect(list) {
                this.tableSelectedList = list;
            },
            deleteConfirm(type, data) {
                if (type === 'multi') {
                    if (!this.tableSelectedList.length) {
                        this.$message.warn('请勾选具体产品');
                        return;
                    }
                }

                let _this = this;

                this.$popup.warn({
                    message: '是否需要删除产品？',
                    buttons: [{
                        txt: '确定删除',
                        btnClass: 'delete',
                        callback() {
                            if (type === 'multi') {
                                _this.multiDelete();
                            } else if (type == 'single') {
                                _this.deleteRequest([{
                                    quoteorderGoodsId: data.goodsInfo.quoteorderGoodsId,
                                    sku: data.goodsInfo.skuNo
                                }])
                            }
                        }
                    }, {
                        txt: '取消',
                    }]
                })
            },
            multiDelete() {
                let deleteDetailList = [];
                this.tableSelectedList.forEach(item => {
                    if (item.goodsInfo) {
                        deleteDetailList.push({
                            quoteorderGoodsId: item.goodsInfo.quoteorderGoodsId,
                            sku: item.goodsInfo.skuNo
                        })
                    }
                })

                this.deleteRequest(deleteDetailList)
            },
            deleteRequest(data) {
                GLOBAL.showGlobalLoading();
                this.$axios.post('/crm/quote/profile/deleteQuoteGoods', {
                    quoteorderId: this.quoteorderId,
                    deleteDetailList: data
                }).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        this.$message.success('删除成功');
                        this.initQuoteList();
                    } else {
                        this.$message.warn(data.message || '删除失败')
                    }
                })
            },
            handlerImportChange(file) {
                if (file) {
                    this.importErrorMsg = '';
                }

                this.importFile = file;
            },
            multiImportProds() {
                if (this.importFile) {
                    let form = new FormData();
                    form.append('file', this.importFile);

                    GLOBAL.showGlobalLoading();

                    this.$axios.post('/crm/quote/profile/import?quoteorderId=' + this.quoteorderId, form, {
                        headers: { 'Content-Type': 'multipart/form-data' }
                    }).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();
                        this.tempFile = null;
                        if (data.success) {
                            this.isShowImportNeeds = false;
                            let _this = this;
                            this.$popup.success({
                                message: data.message || '导入成功',
                                buttons: [{
                                    txt: '我知道了',
                                    btnClass: 'confirm',
                                    callback() {
                                        _this.initQuoteList();
                                    }
                                }]
                            });
                        } else {
                            this.$popup.warn({
                                message: data.message || '导入失败',
                                buttons: [{
                                    txt: '我知道了',
                                    btnClass: 'confirm',
                                }]
                            });
                        }
                    })
                } else {
                    this.importErrorMsg = "请上传需要导入的数据文件";
                }
            },
            showMultiImportAdd() {
                if (!GLOBAL.auth('C0211')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                this.importFile = null;
                this.importErrorMsg = "";
                this.isShowImportNeeds = true;
            },
            hideMultiImportAdd() {
                this.isShowImportNeeds = false;
            },
            updateQuoteData(data, needToast, callback) {
                this.isOnUpdating = true;
                this.$axios.post('/crm/quote/profile/insertOrUpdate', {
                    quoteorderId: this.quoteorderId,
                    ...data
                }).then(({ data }) => {
                    this.isOnUpdating = false;
                    if (needToast) {
                        if (data.success) {
                            this.$message.success('提交成功');
                            callback && callback();
                        } else {
                            this.$message.warn(data.message || '提交失败')
                        }
                    }
                })
            },
            handlerFilterChange(type, data) {
                if (type == 'pm') {
                    this.pmFilterObj = data;
                } else {
                    this.reportStatusFilterObj = data;
                }

                this.checkFilterList();
                console.log(this.pmFilterObj)
            },
            handlerPriceFilterChange() {
                this.checkFilterList();
            },
            checkFilterPm(item) {
                if (!Object.keys(this.pmFilterObj).length) {
                    return true;
                }

                let isIn = false;

                if (item.goodsInfo) {
                    if (item.goodsInfo.productManager && item.goodsInfo.productManager.length) {
                        item.goodsInfo.productManager.forEach(user => {
                            if (this.pmFilterObj[user.userId]) {
                                isIn = true;
                            }
                        })
                    }
                } else if (item.needsInfo && item.needsInfo.headUserList && item.needsInfo.headUserList.length) {
                    if (item.needsInfo.headUserList[0].userId && this.pmFilterObj[item.needsInfo.headUserList[0].userId]) {
                        isIn = true;
                    }
                }

                return isIn;
            },
            checkFilterReportStatus(item) {
                if (!Object.keys(this.reportStatusFilterObj).length) {
                    return true;
                }

                let isIn = false;

                if (item.goodsInfo) {
                    let isNoStatus = this.reportStatusFilterObj['-1'] && !item.goodsInfo.reportStatus && item.goodsInfo.reportStatus !== 0;
                    if (isNoStatus || this.reportStatusFilterObj[item.goodsInfo.reportStatus]) {
                        isIn = true;
                    }
                }

                return isIn;
            },
            checkFilterPrice(item) {
                if (!this.isFilterPrice) {
                    return true;
                }

                let isIn = false;

                if (item.goodsInfo) {
                    if (!item.goodsInfo.salePrice && item.goodsInfo.salePrice !== 0) {
                        isIn = true;
                    }
                } else {
                    isIn = true;
                }

                return isIn;
            },
            getItemId(item) {
                let selectedId = "";
                if (item.needsInfo) {
                    selectedId = "needs" + item.needsInfo.quoteorderNeedsId;
                } else if (item.goodsInfo) {
                    selectedId = "goods" + item.goodsInfo.quoteorderGoodsId;
                }
                return selectedId;
            },
            checkRowSpan(list) {
                list.forEach(item => {
                    if (item.needsInfo) {
                        let num = 1
                        list.forEach(item1 => {
                            if (!item1.needsInfo && item1.parent && item1.parent.quoteorderNeedsId == item.needsInfo.quoteorderNeedsId) {
                                num++;
                            }
                        })

                        item.rowspan = num;
                        console.log(num)
                    }
                })
            },
            checkFilterList(setSelected) {
                let selectedIndex = this.$refs.tableList.tableSelectedIndex;
                let selectedId = "";

                if ((selectedIndex === 0 || selectedIndex) && selectedIndex != -1) {
                    selectedId = this.getItemId(this.showList[selectedIndex]);
                }

                let showList = [];
                let checkedList = [];

                if (!Object.keys(this.pmFilterObj).length && !Object.keys(this.reportStatusFilterObj).length && !this.isFilterPrice) {
                    showList = JSON.parse(JSON.stringify(this.list));
                } else {
                    let list = [];
                    let needsIdsObj = {};

                    this.list.forEach(item => {
                        let isInPmFilter = this.checkFilterPm(item);
                        let isInReportFilter = this.checkFilterReportStatus(item);
                        let isNoPrice = this.checkFilterPrice(item);

                        if (isInPmFilter && isInReportFilter && isNoPrice) {
                            let itemData = JSON.parse(JSON.stringify(item));
                            if (itemData.needsInfo) {
                                if (!needsIdsObj[itemData.needsInfo.quoteorderNeedsId]) {
                                    needsIdsObj[itemData.needsInfo.quoteorderNeedsId] = 1
                                }
                            } else if (itemData.parent) {
                                if (!needsIdsObj[itemData.parent.quoteorderNeedsId]) {
                                    itemData.needsInfo = itemData.parent;
                                    needsIdsObj[itemData.needsInfo.quoteorderNeedsId] = 1
                                }
                            }

                            list.push(JSON.parse(JSON.stringify(itemData)))
                        }
                    })
                    this.checkRowSpan(list);
                    showList = list;
                }

                let newIndex = -1;
                showList.forEach((item, index) => {
                    let id = this.getItemId(item);

                    if (id === selectedId) {
                        newIndex = index;
                    }

                    if (item.goodsInfo) {
                        //如果有正在编辑字段，更新时候值不变
                        if (this.syncLocalPosition && this.syncLocalPosition.split('_')[0] == item.goodsInfo.quoteorderGoodsId) {
                            console.log('isIn')
                            let key = this.syncLocalPosition.split('_')[1];
                            if (key == 'time') {
                                item.goodsInfo.expectDeliveryTime = this.syncLocalPositionValue;
                            } else {
                                item.goodsInfo[key] = this.syncLocalPositionValue;
                            }
                        }
                    }

                    if (setSelected) {
                        this.tableSelectedList.forEach(selectedItem => {
                            if (selectedItem.needsInfo && item.needsInfo && selectedItem.needsInfo.quoteorderNeedsId == item.needsInfo.quoteorderNeedsId) {
                                checkedList.push(item);
                                item.checked = true;
                            } else if (selectedItem.goodsInfo && item.goodsInfo && selectedItem.goodsInfo.quoteorderGoodsId == item.goodsInfo.quoteorderGoodsId) {
                                checkedList.push(item);
                                item.checked = true;
                            }
                        })
                    }
                })

                if (setSelected) {
                    this.tableSelectedList = checkedList;
                }

                this.showList = showList;

                this.$nextTick(() => {
                    this.$refs.tableList.tableSelectedIndex = newIndex;
                })
            },
            handlerReportStatusChange(row) {
                console.log(row.goodsInfo.reportStatus)
                console.log(row.goodsInfo.reportComments)

                let oldRow = {};

                this.list.forEach(item => {
                    if (item.goodsInfo && item.goodsInfo.quoteorderGoodsId == row.goodsInfo.quoteorderGoodsId) {
                        oldRow = item;
                    }
                })

                if (row.goodsInfo.reportStatus != oldRow.goodsInfo.reportStatus || row.goodsInfo.reportComments != oldRow.goodsInfo.reportComments) {
                    this.reportStatusSubmit(row, oldRow);
                }
            },
            //报备状态修改
            reportStatusSubmit(row, oldRow) {
                // if (this.$form.validForm('quoteReportStatusEdit')) {
                // GLOBAL.showGlobalLoading();
                this.isOnUpdating = true;
                let reportStatusReason = row.goodsInfo.reportStatus == 3 ? row.goodsInfo.reportComments : '';
                this.$axios.post('/crm/quote/profile/updateReportStatus', {
                    quoteorderId: this.quoteorderId,
                    quoteorderGoodsId: row.goodsInfo.quoteorderGoodsId,
                    reportStatus: row.goodsInfo.reportStatus,
                    oldReportStatus: oldRow.goodsInfo.reportStatus,
                    reportComments: reportStatusReason,
                    sku: row.goodsInfo.skuNo
                }).then(({ data }) => {
                    // GLOBAL.hideGlobalLoading();
                    this.isOnUpdating = false;
                    if (data.success) {
                        // this.list.forEach(item => {
                        //     if (item.goodsInfo && item.goodsInfo.quoteorderGoodsId == row.goodsInfo.quoteorderGoodsId) {
                        //         this.reportEditRow.goodsInfo.reportStatus = item.goodsInfo.reportStatus = this.reportStatusValue;
                        //         this.reportEditRow.goodsInfo.reportStatusDesc = item.goodsInfo.reportStatusDesc = { 0: '无需报备', 2: '报备成功', 3: '报备失败' }[this.reportStatusValue];
                        //         this.reportEditRow.goodsInfo.reportComments = item.goodsInfo.reportComments = reportStatusReason;
                        //     }
                        // })
                        // this.$forceUpdate();
                    } else {
                        // this.$message.warn(data.message || '提交失败')
                    }
                })
                // }
            },
            //价格修改
            handlerSalePriceChange(row) {
                this.priceHistoryFocusId = "";
                this.priceHistoryCurrentId = "";
                let flag = true;

                let newPrice = row.goodsInfo.salePrice.toString().trim();

                if (newPrice && !/^\d{1,8}(\.\d{1,2})?$/.test(newPrice)) {
                    this.$message.error('最多支持8位数+2位小数，仅限正数');
                    flag = false;
                }

                if (newPrice && flag) {
                    newPrice = row.goodsInfo.salePrice = parseFloat(newPrice);
                }

                this.list.forEach(item => {
                    if (item.goodsInfo && item.goodsInfo.quoteorderGoodsId == row.goodsInfo.quoteorderGoodsId) {
                        let oldPrice = item.goodsInfo.salePrice;

                        if ((!newPrice && newPrice !== 0) || !flag) {
                            newPrice = oldPrice;
                            row.goodsInfo.salePrice = oldPrice;
                        }

                        if (oldPrice != newPrice) {
                            this.updateQuoteData({
                                quoteorderId: this.quoteorderId,
                                quoteorderGoodsId: row.goodsInfo.quoteorderGoodsId,
                                price: newPrice,
                                oldPrice: oldPrice,
                                sku: row.goodsInfo.skuNo
                            })

                            item.goodsInfo.salePrice = newPrice;

                            this.calcTotalPrice();
                        }
                    }
                })

                this.syncLocalPosition = "";
            },
            //数量修改
            handlerNumberChange(row) {
                this.numberChangeTimeout && clearTimeout(this.numberChangeTimeout);
                this.isOnUpdating = true;
                this.numberChangeTimeout = setTimeout(() => {
                    this.list.forEach(item => {
                        if (item.goodsInfo && item.goodsInfo.quoteorderGoodsId == row.goodsInfo.quoteorderGoodsId) {
                            let oldNum = item.goodsInfo.num;
                            let newNum = row.goodsInfo.num;

                            if (oldNum != newNum) {
                                this.updateQuoteData({
                                    quoteorderId: this.quoteorderId,
                                    quoteorderGoodsId: row.goodsInfo.quoteorderGoodsId,
                                    num: newNum,
                                    oldNum: oldNum,
                                    sku: row.goodsInfo.skuNo
                                })

                                item.goodsInfo.num = newNum;
                            }
                        }
                    })

                    this.calcTotalPrice();
                }, 500)
            },
            handlerNumberBlur() {
                this.syncLocalPosition = "";
            },
            //货期修改
            handlerExpectDeliveryTimeChange(row) {
                this.list.forEach(item => {
                    if (item.goodsInfo && item.goodsInfo.quoteorderGoodsId == row.goodsInfo.quoteorderGoodsId) {
                        let oldDeliveryCycle = item.goodsInfo.expectDeliveryTime;
                        if (row.goodsInfo.expectDeliveryTime) {
                            row.goodsInfo.expectDeliveryTime = parseInt(row.goodsInfo.expectDeliveryTime);
                        } else {
                            row.goodsInfo.expectDeliveryTime = oldDeliveryCycle;
                        }
                        let newDeliveryCycle = row.goodsInfo.expectDeliveryTime;

                        if (oldDeliveryCycle != newDeliveryCycle) {
                            this.updateQuoteData({
                                quoteorderId: this.quoteorderId,
                                quoteorderGoodsId: row.goodsInfo.quoteorderGoodsId,
                                deliveryCycle: newDeliveryCycle,
                                oldDeliveryCycle: oldDeliveryCycle,
                                sku: row.goodsInfo.skuNo
                            })

                            item.goodsInfo.expectDeliveryTime = newDeliveryCycle;
                        }
                    }
                })

                this.syncLocalPosition = "";
            },
            showConsultationDialog() {
                this.consultationValue = [];
                this.isShowConsultationDialog = true;

                this.$form.rules({
                    consultationValue: {
                        required: '请选择咨询内容'
                    }
                }, 'quoteConsultationDialog', this)
            },
            hideConsultationDialog() {
                this.isShowConsultationDialog = false;
            },
            //单个咨询
            singleConsultation(row) {
                this.consultationType = 'single';
                this.consultationRow = row;
                this.initConsultationDialog([row])
            },
            //批量咨询
            multiConsultation() {
                if (!this.tableSelectedList.length) {
                    this.$message.warn('请勾选具体产品');
                    return;
                }
                this.consultationType = 'multi';
                this.initConsultationDialog(this.tableSelectedList);
            },
            initConsultationDialog(consultationList) {
                this.consultationQuoteorderGoodsIds = [];
                this.consultationUserList = [];

                let toUserList = [];
                let quoteorderGoodsIds = [];
                let skuNos = [];
                let userObj = {};

                consultationList.forEach(item => {
                    if (item.goodsInfo && item.goodsInfo.skuNo) {
                        quoteorderGoodsIds.push(item.goodsInfo.quoteorderGoodsId)
                        skuNos.push(item.goodsInfo.skuNo)
                    } else {
                        if (item.needsInfo.headUserList && item.needsInfo.headUserList.length && item.needsInfo.headUserList[0].userId) {
                            let user = item.needsInfo.headUserList[0];
                            if (!userObj[user.userId]) {
                                toUserList.push({
                                    userName: user.username,
                                    userId: user.userId,
                                    headPic: user.aliasHeadPicture
                                });
                                userObj[user.userId] = 1;
                            }
                        } else {
                            this.defaultConsultationUser.forEach(user => {
                                if (!userObj[user.userId]) {
                                    toUserList.push(user);
                                    userObj[user.userId] = 1;
                                }
                            })
                        }
                    }
                })

                if (skuNos && skuNos.length) {
                    GLOBAL.showGlobalLoading();
                    this.$axios.post('/crm/quote/profile/queryAskUser', skuNos).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            let list = data.data || [];

                            list.forEach(user => {
                                if (!userObj[user.userId]) {
                                    toUserList.push(user);
                                    userObj[user.userId] = 1;
                                }
                            })

                            this.consultationUserList = toUserList;
                            this.consultationQuoteorderGoodsIds = quoteorderGoodsIds;
                            this.showConsultationDialog();
                        } else {
                            this.$message.warn(data.message)
                        }
                    })
                } else {
                    this.consultationUserList = toUserList;
                    this.showConsultationDialog();
                }
            },
            //咨询提交
            consultationSubmit() {
                if (this.$form.validForm('quoteConsultationDialog')) {
                    let toUserList = [];

                    this.consultationUserList.forEach(item => {
                        toUserList.push(item.userId)
                    })

                    GLOBAL.showGlobalLoading();

                    this.$axios.post('/crm/quote/profile/consultationReport', {
                        quoteorderId: this.quoteorderId,
                        isConsulDeliveryCycle: this.consultationValue.indexOf('1') !== -1 ? 1 : 0,
                        isConsulPrice: this.consultationValue.indexOf('2') !== -1 ? 1 : 0,
                        isConsulReport: this.consultationValue.indexOf('3') !== -1 ? 1 : 0,
                        quoteorderGoodsIds: this.consultationQuoteorderGoodsIds,
                        toUserList
                    }).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.isShowConsultationDialog = false;
                            this.$message.success("提交成功")
                            this.initQuoteList();
                        } else {
                            this.$message.warn(data.message || '提交失败');
                        }
                    })
                }
            },
            calcTotalPrice() {
                let totalPrice = 0;

                this.list.forEach(item => {
                    if (item.goodsInfo && item.goodsInfo.skuName) {
                        let price = parseFloat(item.goodsInfo.salePrice || '0');
                        let num = parseFloat(item.goodsInfo.num || '0');

                        totalPrice += price * num;
                    }
                })

                this.totalPrice = totalPrice.toFixed(2);
            },
            exportQuote() {
                if (!GLOBAL.auth('C0222')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                window.open('/crm/quote/profile/export?quoteorderId=' + this.quoteorderId);
            },
            //分享报价单前校验
            checkProdShare() {
                let isGoodsNosku = false;
                let isGoodsSame = false;

                let skuObj = {};

                this.list.forEach(item => {
                    if (item.goodsInfo && item.goodsInfo.skuName) {
                        if (!item.goodsInfo.skuNo) {
                            isGoodsNosku = true;
                        } else {
                            if (skuObj[item.goodsInfo.skuNo]) {
                                isGoodsSame = true;
                            }

                            skuObj[item.goodsInfo.skuNo] = 1;
                        }
                    }
                })

                return {
                    isGoodsNosku,
                    isGoodsSame
                }
            },
            //分享报价
            shareQuote(confirm) {
                if (!GLOBAL.auth('C0226')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                // if(this.hasNeeds) {
                let checkData = this.checkProdShare();

                if (checkData.isGoodsNosku) {
                    this.$popup.warn({
                        message: '“分享线上报价”时需把手动添加产品换成ERP已建档产品',
                        buttons: [{
                            txt: '我知道了',
                            btnClass: 'confirm',
                        }]
                    });

                    return;
                }

                if (checkData.isGoodsSame && confirm !== 'Y') {
                    let _this = this;

                    this.$popup.warn({
                        message: '报价单中存在重复产品，线上报价单将进行数量合并，并取最低价格和货期，是否确认？',
                        buttons: [{
                            txt: '确定',
                            btnClass: 'confirm',
                            callback() {
                                _this.shareQuote('Y');
                            }
                        }, {
                            txt: '取消'
                        }]
                    });

                    return;
                }
                // }

                GLOBAL.showGlobalLoading();
                this.$axios.get('/crm/quote/profile/shareQuoteDetail?quoteorderId=' + this.quoteorderId).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        this.shareQuoteInfo = data.data;
                        this.isShowShareDialog = true;
                    } else {
                        this.$message.warn(data.message);
                    }
                })
            },
            copyShareInfo() {
                GLOBAL.copyTextToClipboard(this.shareTxt + this.shareQuoteInfo.shardUrl, this);
            },
            //判断是否需要左侧标签提示
            needLeftTip(row) {
                if (row.goodsInfo) {
                    let goodsInfo = row.goodsInfo;
                    let hasTip = goodsInfo.isConsulPrice || goodsInfo.isConsulReport || goodsInfo.isConsulDeliveryCycle;
                    let isMyTip = false;
                    if (goodsInfo.productManager && goodsInfo.productManager.length) {
                        goodsInfo.productManager.forEach(item => {
                            if (item.userId == USERINFO.userId) {
                                isMyTip = true;
                            }
                        })
                    }

                    return hasTip && isMyTip;

                } else {
                    return false;
                }
            },
            leftTipText(info) {
                let tips = [];
                if (info.isConsulPrice) {
                    tips.push('报价')
                }
                if (info.isConsulDeliveryCycle) {
                    tips.push('货期')
                }
                if (info.isConsulReport) {
                    tips.push('报备')
                }

                return tips.join('、')
            },
            //轮询获取最新数据
            startWatchingData() {
                this.syncCycleInter && clearInterval(this.syncCycleInter)

                this.syncCycleInter = setInterval(() => {
                    let time = new Date().getTime();
                    this.syncCycleUpdateTime = time;
                    this.$axios.get('/crm/quote/profile/syncEdit?quoteorderId=' + this.quoteorderId, {
                        headers: {
                            tt: time
                        }
                    }).then(res => {
                        if (this.isOnUpdating) {
                            return;
                        }
                        let reqTime = res.config.headers.tt;
                        if (reqTime < this.syncCycleUpdateTime) {
                            return;
                        }
                        let data = res.data;
                        if (data.success) {
                            let needsList = data.data.quoteorderNeedsList || [];
                            let goodsList = data.data.crmQuoteorderGoodsList || [];

                            let isEnd = false;

                            if (goodsList.length !== this.oldGoodsLength) {
                                isEnd = true;
                                console.log('goods length change')
                            }

                            if (needsList.length !== this.oldNeedsLength || (needsList.length && needsList[0].addTime !== this.needUpdateTime)) {
                                isEnd = true;
                                console.log('needs length change')
                            }

                            goodsList.forEach(newGoodsItem => {
                                if (isEnd) {
                                    return;
                                }

                                let isNew = true;
                                this.list.forEach((listItem, listIndex) => {
                                    if (listItem.goodsInfo) {
                                        if (isEnd) {
                                            return;
                                        }

                                        let oldGoodsItem = listItem.goodsInfo;

                                        //相同的quoteorderGoodsId，重新赋值
                                        if (newGoodsItem.quoteorderGoodsId == oldGoodsItem.quoteorderGoodsId) {
                                            isNew = false;
                                            //如果是整个sku替换了，整个sku重新渲染
                                            if (newGoodsItem.skuNo != oldGoodsItem.skuNo) {
                                                isEnd = true;
                                            }
                                            if (newGoodsItem.salePrice != oldGoodsItem.salePrice && this.syncLocalPosition != (newGoodsItem.quoteorderGoodsId + '_salePrice')) {
                                                console.log('price change')
                                                this.list[listIndex].goodsInfo.salePrice = newGoodsItem.salePrice;
                                                this.$set(this.priceChangeObj, newGoodsItem.quoteorderGoodsId, 1);
                                                this.calcTotalPrice();
                                                setTimeout(() => {
                                                    this.$set(this.priceChangeObj, newGoodsItem.quoteorderGoodsId, null);
                                                    delete this.priceChangeObj[newGoodsItem.quoteorderGoodsId]
                                                }, 2000)
                                            }
                                            if (newGoodsItem.num != oldGoodsItem.num && this.syncLocalPosition != (newGoodsItem.quoteorderGoodsId + '_num')) {
                                                this.list[listIndex].goodsInfo.num = newGoodsItem.num;
                                                this.$set(this.numChangeObj, newGoodsItem.quoteorderGoodsId, 1);
                                                this.calcTotalPrice();
                                                setTimeout(() => {
                                                    this.$set(this.numChangeObj, newGoodsItem.quoteorderGoodsId, null);
                                                    delete this.numChangeObj[newGoodsItem.quoteorderGoodsId]
                                                }, 2000)
                                            }
                                            if (newGoodsItem.expectDeliveryTime != oldGoodsItem.expectDeliveryTime && this.syncLocalPosition != (newGoodsItem.quoteorderGoodsId + '_time')) {
                                                this.list[listIndex].goodsInfo.expectDeliveryTime = newGoodsItem.expectDeliveryTime;
                                                this.$set(this.timeChangeObj, newGoodsItem.quoteorderGoodsId, 1);
                                                setTimeout(() => {
                                                    this.$set(this.timeChangeObj, newGoodsItem.quoteorderGoodsId, null);
                                                    delete this.timeChangeObj[newGoodsItem.quoteorderGoodsId]
                                                }, 2000)
                                            }

                                            if (this.syncLocalPosition != (newGoodsItem.quoteorderGoodsId + '_reportComments')) {
                                                this.list[listIndex].goodsInfo.reportComments = newGoodsItem.reportComments;
                                            }

                                            this.list[listIndex].goodsInfo.reportStatus = newGoodsItem.reportStatus;
                                            this.list[listIndex].goodsInfo.isConsulDeliveryCycle = newGoodsItem.isConsulDeliveryCycle;
                                            this.list[listIndex].goodsInfo.isConsulPrice = newGoodsItem.isConsulPrice;
                                            this.list[listIndex].goodsInfo.isConsulReport = newGoodsItem.isConsulReport;
                                        }
                                    }
                                })

                                needsList.forEach(needsItem => {
                                    this.list.forEach((listItem, index) => {
                                        if (listItem.needsInfo && !listItem.goodsInfo && needsItem.quoteorderNeedsId == listItem.needsInfo.quoteorderNeedsId) {
                                            let prevUser = ((listItem.needsInfo.headUserList || [])[0] || {}).userId;
                                            let newUser = ((needsItem.headUserList || [])[0] || {}).userId;

                                            if (prevUser != newUser) {
                                                this.list[index].needsInfo.headUserList = needsItem.headUserList;
                                                console.log(prevUser, newUser)
                                            }
                                        }
                                    })
                                })

                                if (isNew) {
                                    isEnd = true;
                                }
                            })

                            if (isEnd) {
                                this.initQuoteList(true)
                            }

                            this.checkFilterList(true);


                        }
                    })
                }, this.syncCycle * 1000)
                // }, this.syncCycle * 2000)  //测试时候稍微调高时间以便观察效果
            },
            //轮询上报获焦状态
            startWatchingLocation() {
                this.syncLocalInter && clearInterval(this.syncLocalInter);

                this.syncLocalInter = setInterval(() => {
                    this.$axios.post('/crm/quote/profile/sync', {
                        quoteorderId: this.quoteorderId,
                        timestamp: new Date().getTime(),
                        local: this.syncLocalPosition
                    }).then(({ data }) => {
                        if (data.success) {
                            if (data.data.editUserLocal && data.data.editUserLocal.length) {
                                data.data.editUserLocal.forEach(item => {
                                    this.localListObj[item.local] = item;
                                })
                            }
                        }
                    })
                }, this.syncLocal * 1000)
            },
            handlerPositionChange(info, type, value) {
                this.syncLocalPosition = info.quoteorderGoodsId + '_' + type;
                this.handlerListInputChange(value);

                //历史编辑价格
                if (type === 'salePrice') {
                    if (this.priceHistoryCurrentId != info.quoteorderGoodsId) {
                        this.priceHistoryFocusId = "";
                        this.getPriceHistory(info.quoteorderGoodsId);
                        setTimeout(() => {
                            console.log(111)
                            this.priceHistoryFocusId = info.quoteorderGoodsId;
                        }, 200)
                    } else {
                        this.priceHistoryFocusId = info.quoteorderGoodsId;
                    }
                }
            },
            handlerListInputChange(value) {
                this.syncLocalPositionValue = value;
            },
            isOnfocus(info, type) {
                let infoLocation = info.quoteorderGoodsId + '_' + type;
                if (infoLocation == this.syncLocalPosition && this.localListObj[infoLocation]) {
                    return true;
                } else {
                    return false;
                }
            },
            handlerTableScroll() {
                for (item in this.$refs) {
                    if (/^pm\d*$/.test(item)) {
                        if (this.$refs[item]) {
                            this.$refs[item].hide();
                        }
                    }
                }
            },
            handlerTableScrollLeft() {
                if (!this.needResetPriceHistory) {
                    this.needResetPriceHistory = true;
                    this.resetPriceHistoryTimeout && clearTimeout(this.resetPriceHistoryTimeout);
                    this.resetPriceHistoryTimeout = setTimeout(() => {
                        this.needResetPriceHistory = false;
                    }, 100)
                }
            },
            showChatDialog() {
                if (!GLOBAL.auth('C0215')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                GLOBAL.showGlobalLoading();
                this.$axios.post('/crm/quote/profile/queryUserByQuoteorderId?quoteorderId=' + this.quoteorderId).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        this.chatUserList = data.data.userList || [];
                        this.isBuildChat = data.data.isBuildChat;

                        this.chatUserList.forEach(item => {
                            if (item.workStatus === 'N') {
                                item.disabled = true;
                                item.userName = item.userName + "(已离职)";
                            } else {
                                if(this.isBuildChat == 2) {
                                    item.disabled = true;
                                    if(data.data.userIdList.indexOf(item.userId) !== -1) {
                                        item.checked = true;
                                    }
                                } else {
                                    item.checked = true;
                                }
                            }
                        })

                        this.chargeInfo = {
                            chargeId: data.data.chargeId,
                            chargeName: data.data.chargeName,
                            chargeNameHeadPicture: data.data.chargeNameHeadPicture
                        }
                        this.isShowChatDialog = true;
                    } else {
                        this.$message.warn(data.message)
                    }
                })
            },
            handlerChatTableListSelect(list) {
                this.chatSelectedUserList = list;
            },
            // createGroupChat() {
            //     if (!this.chatSelectedUserList.length) {
            //         this.$message.warn('请勾选产品负责人');
            //         return;
            //     }

            //     let userIds = [];

            //     this.chatSelectedUserList.forEach(item => {
            //         userIds.push(item.userId);
            //     })
            //     GLOBAL.showGlobalLoading();
            //     this.$axios.post('/crm/quote/profile/createAppChat', {
            //         quoteorderId: this.quoteorderId,
            //         userIds
            //     }).then(({ data }) => {
            //         GLOBAL.hideGlobalLoading();
            //         if (data.success) {
            //             this.isBuildChat = 2;
            //             this.$message({
            //                 message: `建群申请已提交${this.chargeInfo.chargeName || ''}审批`,
            //                 type: 'success'
            //             })
            //             // this.chatName = data.data.name;
            //             // this.isShowChatDialog = false;
            //             // this.showChatDialogTip();
            //         } else {
            //             this.$message.warn(data.message)
            //         }
            //     })
            // },
            approveGroupChat(status) {

                let reqData = {
                    quoteorderId: this.quoteorderId,
                    approverStatus: status,
                }


                if (status == 2) {
                    if (!this.chatSelectedUserList.length) {
                        this.$message.warn('请勾选产品负责人');
                        return;
                    }

                    let userIdList = [];
                    this.chatSelectedUserList.forEach(item => {
                        userIdList.push(item.userId);
                    })

                    reqData.userIdList = userIdList;
                }

                
                GLOBAL.showGlobalLoading();

                this.$axios.post('/crm/quote/profile/approverAppChat', reqData).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        if (status == 1) {
                            this.isBuildChat = 1;
                            this.chatName = data.data.name;
                            this.isShowChatDialog = false;
                            this.showChatDialogTip();
                        } else if (status == 0) {
                            this.isBuildChat = 0;
                            this.$message({
                                message: '驳回成功',
                                type: 'success'
                            })
                            
                            this.isShowChatDialog = false;
                        } else if (status == 2) {
                            this.isBuildChat = 2;
                            this.$message({
                                message: `建群申请已提交${this.chargeInfo.chargeName || ''}审批`,
                                type: 'success'
                            })
                            
                            this.isShowChatDialog = false;
                        }
                    } else {
                        this.$message.warn(data.message)
                    }
                })
            },
            showChatDialogTip() {
                if (!GLOBAL.auth('C0215')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let _this = this;
                this.$popup.info({
                    message: this.chatName + '商机协同群已创建，可打开企业微信查看跟进。',
                    buttons: [{
                        txt: '复制群名称',
                        btnClass: 'vd-button-link',
                        callback() {
                            GLOBAL.copyTextToClipboard(_this.chatName, _this);
                        }
                    }, {
                        txt: '我知道了',
                        btnClass: 'confirm'
                    }]
                });
            },
            finishTaskConfirm() {
                GLOBAL.showGlobalLoading();
                this.$axios.post('/crm/quote/profile/taskTips?quoteorderId=' + this.quoteorderId).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        let taskNum = data.data.taskNum;
                        let productNum = data.data.productNum;
                        let tips = [];
                        let tip = "";

                        if (taskNum > this.taskNum || productNum > this.productNum) {
                            if (taskNum > this.taskNum) {
                                tips.push(`${taskNum - this.taskNum}条代办`)
                            }
                            if (productNum > this.productNum) {
                                tips.push(`${productNum - this.productNum}条咨询`)
                            }
                            tip = `在您处理报价单期间，新增了${tips.join('和')}，是否一并处理？`;
                        } else {
                            if (this.taskNum) {
                                tips.push(`${this.taskNum}条代办`);
                            }
                            if (this.productNum) {
                                tips.push(`${this.productNum}条咨询`);
                            }
                            tip = `将会为您批量处理${tips.join('和')}。`
                        }

                        let _this = this;

                        this.$popup.warn({
                            message: tip,
                            buttons: [{
                                txt: '确定',
                                btnClass: 'confirm',
                                callback() {
                                    _this.finishTask();
                                }
                            }, {
                                txt: '取消'
                            }]
                        })
                    }
                })
            },
            finishTask() {
                GLOBAL.showGlobalLoading();
                this.$axios.post('/crm/quote/profile/consultationReportFinish?quoteorderId=' + this.quoteorderId, {
                    quoteorderId: this.quoteorderId
                }).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        this.$message.success("处理完成");
                        setTimeout(() => {
                            window.location.reload();
                        }, 500)
                    } else {
                        this.$message.warn(data.message)
                    }
                })
            },
            //生效报价
            validQuoteStatus() {
                if (!GLOBAL.auth('C0212')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                let errorSkuNos = [];
                let customProdErr = false;
                let skuNos = [];
                this.list.forEach(item => {
                    if (item.goodsInfo && item.goodsInfo.skuName) {
                        skuNos.push(item.goodsInfo.skuName)
                        if ((!item.goodsInfo.salePrice && item.goodsInfo.salePrice !== 0) || !item.goodsInfo.num) {
                            if (item.goodsInfo.skuNo) {
                                errorSkuNos.push(item.goodsInfo.skuNo)
                            } else {
                                customProdErr = true;
                            }
                        }
                    }
                })

                if (!skuNos.length) {
                    this.$popup.warn({
                        message: '请确认报价单中已添加产品',
                        buttons: [{
                            txt: '我知道了',
                            btnClass: 'confirm',
                        }]
                    });

                    return false;
                }

                if (errorSkuNos.length || customProdErr) {
                    let customProdErrInfo = '';
                    if (customProdErr) {
                        customProdErrInfo = '手工添加产品';

                        if (errorSkuNos.length) {
                            customProdErrInfo = '及' + customProdErrInfo;
                        }
                    }

                    this.$popup.warn({
                        message: errorSkuNos.join('/') + customProdErrInfo + '的单价或数量没维护，请维护后生效',
                        buttons: [{
                            txt: '我知道了',
                            btnClass: 'confirm',
                        }]
                    });

                    return false;
                }

                let _this = this;
                this.$popup.warn({
                    message: '是否确认生效报价？',
                    buttons: [{
                        txt: '确认生效',
                        btnClass: 'confirm',
                        callback() {
                            _this.checkTaskStatus();
                        }
                    }, {
                        txt: '取消',
                    }]
                });
            },
            //撤销生效报价
            invalidQuoteStatus() {
                if (!GLOBAL.auth('C0213')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                let _this = this;
                this.$popup.warn({
                    message: '是否确认撤销生效？',
                    buttons: [{
                        txt: '确认撤销生效',
                        btnClass: 'confirm',
                        callback() {
                            _this.updateQuoteStatus(false);
                        }
                    }, {
                        txt: '取消',
                    }]
                });
            },
            // 检测是否有未结束待办任务
            checkTaskStatus(confirm) {
                const _this = this;

                this.$axios.post('/crm/quote/profile/updateValid', {
                    quoteorderId: this.quoteorderId,
                    confirmed: !!confirm,
                    valid: true
                }).then(({ data }) => {
                    if (data.code === 1) {
                        this.$popup.warn({
                            message: '该报价单中供应链还有待办任务未完成，报价生效后将无法操作，是否继续？',
                            buttons: [{
                                txt: '继续',
                                btnClass: 'confirm',
                                callback() {
                                    _this.checkTaskStatus(true);
                                }
                            }, {
                                txt: '取消',
                            }]
                        });
                    } else if (data.code === 0) {
                        window.location.reload();
                    } else {
                        this.$popup.warn({
                            message: data.message,
                            buttons: [{
                                txt: '我知道了',
                                btnClass: 'confirm',
                            }]
                        })
                    }
                })
            },
            updateQuoteStatus(bol) {
                this.$axios.post('/crm/quote/profile/updateValid', {
                    quoteorderId: this.quoteorderId,
                    valid: bol
                }).then(({ data }) => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        this.$popup.warn({
                            message: data.message,
                            buttons: [{
                                txt: '我知道了',
                                btnClass: 'confirm',
                            }]
                        })
                    }
                })
            },
            //价格提示
            checkSalePrice(row) {
                let goodsInfo = row.goodsInfo;

                if (goodsInfo.salePrice && goodsInfo.dealerPrice) {
                    if (parseFloat(goodsInfo.salePrice) < parseFloat(goodsInfo.dealerPrice)) {
                        return true;
                    }
                }

                return false;
            },
            //跟进记录
            openRecord() {
                this.$refs.followRecordListDialog.open({
                    relatedId: this.businessInfo.businessChanceId,
                    traderId: this.businessInfo.traderId,
                    traderName: this.businessInfo.traderName,
                    belongerId: this.businessInfo.belongerId,
                    belonger: this.businessInfo.belonger,
                    belongerPic: this.businessInfo.belongPic,
                    traderNameLink: this.businessInfo.traderNameLink,
                    traderNameInnerLink: this.businessInfo.traderNameInnerLink,
                    tycFlag: this.businessInfo.tycFlag,
                    contact: this.businessInfo.traderContactName,
                    traderContactId: this.businessInfo.traderContactId,
                    phone: this.businessInfo.phone,
                    telePhone: this.businessInfo.telephone,
                    communicateType: 244
                });
            },
            //操作记录
            openLog() {
                this.$refs.operationLogDialog.open({
                    bizTypeEnum: '02', // 业务类型 01线索池 02商机库 03 报价单
                    relatedId: this.businessInfo.businessChanceId, // 业务id
                    quoteorderId: this.quoteorderId, // 报价单id
                });
            },
            //任务
            openTask() {
                this.$refs.renwuDialog.open({
                    bizType: '1', //  1:商机 2:线索 3:报价
                    relatedId: this.businessInfo.businessChanceId, // 业务id
                    quoteorderId: this.quoteorderId, // 报价单id
                });
            },
            //协作人
            openPartner() {
                this.$refs.partnerListDialog.open({
                    relatedId: this.businessInfo.businessChanceId, // 业务id
                    businessType: 1, // 1.商机 2.报价 3.订单 4.售后 5.线索
                    belongerId: this.businessInfo.userId,
                    businessNo: this.businessInfo.bussinessChanceNo,
                });
            },
            gotoApply() {
                if (!GLOBAL.auth('C0214')) {
                    GLOBAL.showNoAuth();
                    return;
                }
                GLOBAL.showGlobalLoading();
                this.$axios.post('/crm/quote/profile/checkQuoteApply?quoteorderId=' + this.quoteorderId).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        let totalNum = data.data.num;
                        let applyNum = data.data.appliedNum;;

                        if (applyNum == 0) {
                            GLOBAL.link({ name: '申请授权书', url: data.data.submitInnerLink, link: data.data.submitLink, nohost: true, crmType: 1 })
                        } else {
                            this.$popup.warn({
                                message: `您已提交了${applyNum}个授权申请，是否继续申请？`,
                                buttons: [{
                                    txt: '查看授权',
                                    btnClass: 'confirm',
                                    callback() {
                                        GLOBAL.link({ name: '查看授权书', url: data.data.checkInnerLink, link: data.data.checkLink, nohost: true, crmType: 1 })
                                    },
                                }, {
                                    txt: '继续申请',
                                    callback() {
                                        GLOBAL.link({ name: '申请授权书', url: data.data.submitInnerLink, link: data.data.submitLink, nohost: true, crmType: 1 })
                                    }
                                }]
                            });
                        }
                        // } else if (totalNum == applyNum) {
                        //     this.$popup.warn({
                        //         message: `您已提交了${applyNum}个授权申请`,
                        //         buttons: [{
                        //             txt: '查看授权',
                        //             btnClass: 'confirm',
                        //             callback() {
                        //                 GLOBAL.link({ name: '查看授权书', url: data.data.checkInnerLink, link: data.data.checkLink, nohost: true, crmType: 1  })
                        //             },
                        //         }]
                        //     });
                        // }

                    } else {
                        GLOBAL.link({ name: '申请授权书', url: this.quoteApplyInfo.quoteApplyInnerLink, link: this.quoteApplyInfo.quoteApplyLink, nohost: true, crmType: 1 })
                    }
                })
            },
            showSelectProdDialog(data) {
                let skuNos = [];
                let needsInfo = null;

                if (data && (data.needsInfo || data.parent)) {
                    needsInfo = data.needsInfo || data.parent;
                    this.selectProdDialogType = 'needs';
                    this.editingRow = data;
                    console.log(data)
                    let quoteorderGoodsIds = needsInfo.quoteorderGoodsIds;
                    this.list.forEach(item => {
                        let goodsInfo = item.goodsInfo;

                        if (quoteorderGoodsIds && goodsInfo && goodsInfo.quoteorderGoodsId && quoteorderGoodsIds.indexOf(goodsInfo.quoteorderGoodsId) !== -1 && goodsInfo.skuNo) {
                            skuNos.push(goodsInfo.skuNo);
                        }
                    })

                    console.log(skuNos)
                } else {
                    this.selectProdDialogType = 'prod';

                    this.list.forEach(listItem => {
                        if (listItem.goodsInfo && listItem.goodsInfo.skuNo) {
                            skuNos.push(listItem.goodsInfo.skuNo);
                        }
                    })
                }

                this.$refs.selectProd.show({ skuNos, needsInfo });
            },
            showCustomSelectProdDialog(data, type) {
                this.editingRow = data;

                if (type && type === 'edit') {
                    console.log(data.goodsInfo)
                    this.customDialogType = 'edit';
                    this.customAddInfo = {
                        goodsName: data.goodsInfo.skuName,
                        brandName: data.goodsInfo.brandName,
                        brandId: data.goodsInfo.brandId,
                        model: data.goodsInfo.modelOrSpec,
                        unitName: data.goodsInfo.unitName,
                        paramContent: data.goodsInfo.mainParam && data.goodsInfo.mainParam.length ? data.goodsInfo.mainParam[0] : '',
                        imgUrl: data.goodsInfo.imageUrl || ''
                    }
                } else {
                    this.customDialogType = 'add';
                    this.customAddInfo = {
                        goodsName: '',
                        brandName: '',
                        brandId: '',
                        model: '',
                        unitName: '',
                        paramContent: '',
                        imgUrl: ''
                    }
                }

                this.isShowCustomAddDialog = true;

                this.$form.rules({
                    goodsName: {
                        required: '请填写“产品名称”'
                    },
                    model: {
                        required: '请填写“产品型号”'
                    },
                    brandId: {
                        required: '请选择“产品品牌”'
                    },
                    unitName: {
                        required: '请填写“产品单位”'
                    },
                }, 'customAddProdForm', this.customAddInfo)
            },
            handlerCustomProdUpload(data) {
                console.log(data)
                if (data && data.length) {
                    console.log(data[0].url)
                    this.customAddInfo.imgUrl = data[0].url;
                } else {
                    this.customAddInfo.imgUrl = '';
                }
            },
            customBrandSelectChange(data) {
                this.customAddInfo.brandName = data.selected.brandName || '';
            },
            submitCustomProdInfo() {
                if (this.$form.validForm('customAddProdForm')) {
                    let needsInfo = this.editingRow.needsInfo || this.editingRow.parent;
                    GLOBAL.showGlobalLoading();
                    let reqData = {
                        quoteorderId: this.quoteorderId,
                        quoteorderNeedsId: needsInfo.quoteorderNeedsId,
                        quoteorderGoodsId: this.customDialogType === 'edit' ? this.editingRow.goodsInfo.quoteorderGoodsId : '',
                        ...this.customAddInfo
                    };
                    this.$axios.post('/crm/quote/profile/addQuoteGoodsNoSku', reqData).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();

                        if (data.success) {
                            this.$message.success('提交成功');
                            this.isShowCustomAddDialog = false;
                            this.initQuoteList();
                        } else {
                            this.$message.warn(data.message);
                        }
                    })
                }
            },
            listShowAddRemarkDialog() {
                this.showAddRemarkDialog(this.editingRow);
            },
            showAddRemarkDialog(row) {
                this.editingRow = row;
                this.isShowAddRemark = true;
                this.remarkEditValue = "";

                this.$form.rules({
                    remarkEditValue: {
                        required: '请填写备注'
                    },
                }, 'remarkAddForm', this)
            },
            submitRemark() {
                if (this.$form.validForm('remarkAddForm')) {
                    GLOBAL.showGlobalLoading();
                    this.$axios.post('/crm/quote/profile/addQuoteGoodsRemark', {
                        quoteorderGoodsId: this.editingRow.goodsInfo.quoteorderGoodsId,
                        remark: this.remarkEditValue.trim()
                    }).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();

                        if (data.success) {
                            this.$message.success('添加成功');
                            this.initQuoteList();
                            this.isShowAddRemark = false;

                            if (this.isShowRemarkDetail) {
                                this.initRemarkList();
                            }
                        } else {
                            this.$message.warn(data.message);
                        }
                    })
                }
            },
            showRemarkDetail(row) {
                this.editingRow = row;
                this.initRemarkList();
                this.isShowRemarkDetail = true;
            },
            initRemarkList() {
                GLOBAL.showGlobalLoading();

                this.$axios.post('/crm/quote/profile/queryAllRemark?quoteorderGoodsId=' + this.editingRow.goodsInfo.quoteorderGoodsId).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();

                    if (data.success) {
                        this.remarkInfoList = data.data;
                    } else {
                        this.$message.warn(data.message);
                    }
                })
            },
            handlerRemarkDialogClose() {
                this.remarkInfoList = [];
            },
            //编辑客户需求描述
            editResDescription() {
                GLOBAL.showGlobalLoading();

                this.$axios.post('/crm/quote/profile/addQuoteNeedsDesc', {
                    quoteorderId: this.quoteorderId,
                    needsDesc: this.resDescription.trim()
                }).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    if (data.success) {
                        this.isShowReqDescription = false;
                        this.$message.success('编辑成功');
                    } else {
                        this.$message.warn(data.message);
                    }
                })
            },
            // 打开需求附件
            openFile() {
                this.$refs.fileDialog.open({
                    bizId: this.quoteorderId, // 业务id
                    bizType: '03', // 业务类型,01线索池02商机库03报价单
                });
            },
            //获取价格历史
            getPriceHistory(quoteorderGoodsId) {
                if (this.quoteValidStatus == 1 || this.businessStage == 6 || this.priceHistoryFocusId) {
                    return;
                }

                this.priceHistoryCurrentId = quoteorderGoodsId;
                this.priceHistoryLoading = true;
                this.priceHistoryTimeout && clearTimeout(this.priceHistoryTimeout);
                this.priceHistoryTimeout = setTimeout(() => {
                    this.$axios.post('/crm/operationLog/public/pageForModifyPrice', {
                        param: {
                            bizList: [
                                {
                                    bizTypeEnum: "03",
                                    bizId: this.quoteorderId
                                }
                            ],
                            quoteorderGoodsId: quoteorderGoodsId
                        },
                        "pageNum": 1,
                        "pageSize": 100
                    }).then(({ data }) => {
                        this.priceHistoryLoading = false;

                        if (data.success) {
                            let list = data.data.list || [];

                            list.forEach(item => {
                                let price = '';
                                try {
                                    let params = JSON.parse(item.logParam);
                                    price = parseFloat(params.price).toFixed(2);
                                } catch (error) {
                                    console.log('价格解析错误');
                                }

                                item.priceTxt = price;
                            })

                            this.priceHistoryList = list;
                        }
                    })
                }, 500)
            },
            hidePriceHistory() {
                if (this.priceHistoryFocusId) {
                    return;
                }

                this.priceHistoryCurrentId = "";
            },
            //删除需求
            deleteNeedsConfirm(needsId) {
                let _this = this;
                this.$popup.warn({
                    message: '报价需求删除后，相关的操作信息将一并删除，确定删除吗？',
                    buttons: [{
                        txt: '删除',
                        btnClass: 'delete',
                        callback() {
                            _this.$axios.post('/crm/quote/profile/deleteQuoteNeeds', {
                                quoteorderId: _this.quoteorderId,
                                deleteDetailList: [{
                                    quoteorderNeedsId: needsId
                                }]
                            }).then(({ data }) => {
                                if (data.success) {
                                    _this.$message.success('删除成功');
                                    _this.initQuoteList();
                                } else {
                                    _this.$message.warn(data.message);
                                }
                            })
                        }
                    }, {
                        txt: '取消',
                    }]
                })
            },
            fileUploadChange(num) {
                this.attachmentsNum = num;
            },
            listenMousemove() {
                let nowTime = new Date().getTime();
                let maxTime = parseFloat(document.querySelector('#autoRefreshMin').value || '3');
                if (this.stayTime && nowTime - this.stayTime > maxTime * 60 * 1000) {
                    this.syncCycleInter && clearInterval(this.syncCycleInter);
                    this.syncLocalInter && clearInterval(this.syncLocalInter);
                    this.isShowOptionTip = true;
                } else {
                    setTimeout(() => {
                        this.listenMousemove();
                    }, 10000)
                }
            },
            refreshPage() {
                window.location.reload();
            },
            handlerGoodsTipHover(skuNo) {
                if (!this.goodsTipInfo[skuNo]) {
                    if (!this.isGoodsTipLoading) {
                        this.isGoodsTipLoading = true;
                        this.$axios.get(`/crm/sku/public/querySkuSkip?skuNo=${skuNo}`).then(({ data }) => {
                            if (data.success) {
                                let prodInfo = data.data;

                                let managers = [];

                                if (prodInfo.managerName) {
                                    managers.push(prodInfo.managerName);
                                }

                                if (prodInfo.assistName) {
                                    managers.push(prodInfo.assistName);
                                }

                                prodInfo.managers = managers.join('、');

                                this.goodsTipInfo[skuNo] = prodInfo;
                                this.isGoodsTipLoading = false;
                                // this.$refs.tableList.initListData();
                                this.checkFilterList();
                            }
                        })
                    }
                }
            },
            //转订单
            handlerToOrder() {
                if (!GLOBAL.auth('C0208')) {
                    GLOBAL.showNoAuth();
                    return;
                }

                let _this = this;
                let confirmParam = confirm === 'Y' ? '&confirm=Y' : '';

                GLOBAL.checkNetwork(() => {
                    _this.$axios.get(`/crm/businessChance/profile/toOrderVerify?businessChanceId=${_this.businessInfo.businessChanceId}${confirmParam}&systemCode=${GLOBAL.checkCrmType().type}`)
                        .then(({ data }) => {
                            if (data.code === 0) {
                                _this.toorderOrderType = '';
                                _this.toorderSaleOrderNo = '';
                                _this.isShowToOrder = true;

                                _this.ToOrderTypes = [
                                    { label: '新建订单', tip: `（将商机转成<span class="red">${GLOBAL.checkCrmType().label}</span>销售订单）`, value: '1' },
                                    { label: '关联订单', tip: `（将商机与<span class="red">${GLOBAL.checkCrmType().label}</span>销售订单关联）`, value: '2' }
                                ];

                                _this.$form.rules({
                                    toorderOrderType: {
                                        required: '请选择转订单的方式'
                                    },
                                }, 'ToOrderValid', _this);
                            } else if (data.code === 1) {
                                _this.$popup.warn({
                                    message: '报价单中存在重复产品，转订单后将进行数量合并，并取最低价格和货期，是否确认？',
                                    buttons: [{
                                        txt: '确认',
                                        btnClass: 'confirm',
                                        callback() {
                                            _this.handlerToOrder('Y');
                                        }
                                    }]
                                })
                            } else {
                                _this.$popup.warn({
                                    message: data.message,
                                    buttons: [{
                                        txt: '我知道了',
                                        btnClass: 'confirm',
                                    }]
                                })
                            }
                        })
                })
            },
            toorderChackOrderNo() {
                if (this.toorderOrderType == '2' && !this.toorderSaleOrderNo.trim()) {
                    this.toorderSaleOrderNoError = "请输入关联的ERP销售单号";
                } else {
                    this.toorderSaleOrderNoError = "";
                }
            },
            // 验证转商机表单
            validToOrderForm() {
                let error = 0;

                if (!this.$form.validForm('ToOrderValid')) {
                    error++;
                }

                if (this.toorderOrderType == '2' && !this.toorderSaleOrderNo.trim()) {
                    this.toorderSaleOrderNoError = "请输入关联的ERP销售单号";
                    error++;
                }

                if (error) {
                    return false;
                }
                return true;
            },
            toorderValidSubmit() {
                if (!this.validToOrderForm()) return;
                if (!this.canToOrderSubmit) return;

                const _this = this;
                this.$popup.warn({
                    message: '关联/新建订单之后将无法修改，您是否确定继续？',
                    buttons: [{
                        txt: '确认',
                        btnClass: 'confirm',
                        callback() {
                            _this.submitToOrder();
                        }
                    }]
                })
            },
            submitToOrder() {
                this.canToOrderSubmit = false;
                GLOBAL.showGlobalLoading();
                // 新建订单
                if (this.toorderOrderType == 1) {
                    this.$axios.get(`/crm/businessChance/profile/toOrder?businessChanceId=${this.businessInfo.businessChanceId}&systemCode=${GLOBAL.checkCrmType().type}`)
                        .then(({ data }) => {
                            GLOBAL.hideGlobalLoading();
                            if (data.success) {
                                this.isShowToOrder = false;
                                this.$message.success('转订单成功');
                                setTimeout(() => {
                                    GLOBAL.link({ name: '商机转订单', url: data.data.jumpErpInnerUrl, link: data.data.jumpErpUrl, nohost: true });
                                }, 2000);
                            } else {
                                this.canToOrderSubmit = true;
                                this.$message.error(data.message);
                            }
                        })
                }
                // 关联订单
                else {
                    this.$axios.post(`/crm/businessChance/profile/linkOrder`, {
                        businessChanceId: this.businessInfo.businessChanceId,
                        saleOrderNo: this.toorderSaleOrderNo.trim(),
                        systemCode: GLOBAL.checkCrmType().type
                    }).then(({ data }) => {
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.isShowToOrder = false;
                            this.$message.success('转订单成功');
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        } else {
                            this.canToOrderSubmit = true;
                            this.$message.error(data.message);
                        }
                    })
                }
            }
        }
    })


}.call(this);
