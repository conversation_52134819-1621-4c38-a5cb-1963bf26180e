<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <el-form :model="buyOrderRebateChargeApply" ref="buyOrderRebateChargeApply" label-width="120px" label-suffix=":">
        <el-row style="font-weight: 700;padding-left: 10px">
            <el-col :span="18" style="color: #606266;">基本信息</el-col>
            <el-col :span="6" v-if="buyOrderRebateChargeApply.showCheckButton">
                <el-button type="primary" @click="auditOperation(true)">通过</el-button>
                <el-button @click="auditOperation(false)">不通过</el-button>
            </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-form-item label="结算单号" prop="buyOrderRebateChargeNo">
            <span>{{buyOrderRebateChargeApply.buyOrderRebateChargeNo}}</span>
        </el-form-item>

        <el-form-item label="供应商名称" prop="traderSupplierName">
            <span>{{buyOrderRebateChargeApply.traderSupplierName}}</span>
        </el-form-item>

        <el-form-item label="品牌">
            <span>{{buyOrderRebateChargeApply.brandName}}</span>
        </el-form-item>

        <el-form-item label="产线负责人">
            <span>{{buyOrderRebateChargeApply.headUserName}}</span>
        </el-form-item>

        <el-form-item label="返利周期">
            <span>{{parseTime(buyOrderRebateChargeApply.cycleBeginTime, '{y}-{m}-{d}')}} ~ {{parseTime(buyOrderRebateChargeApply.cycleEndTime, '{y}-{m}-{d}')}}</span>
        </el-form-item>

        <el-form-item label="返利使用期限">
            <span>{{parseTime(buyOrderRebateChargeApply.usePeriodBeginTime, '{y}-{m}-{d}')}} ~ {{parseTime(buyOrderRebateChargeApply.usePeriodEndTime, '{y}-{m}-{d}')}}</span>
        </el-form-item>

        <el-form-item label="返利政策条款">
            <span>{{buyOrderRebateChargeApply.policyTerms}}</span>
        </el-form-item>

        <el-form-item label="备注">
            <span>{{buyOrderRebateChargeApply.remark}}</span>
        </el-form-item>

        <el-form-item label="合计返利" prop="totalAmount">
            <span>{{Number(buyOrderRebateChargeApply.totalAmount).toFixed(2)}}</span>
        </el-form-item>

        <el-row style="color: #606266; font-weight: 700;padding-left: 10px">其它信息</el-row>
        <el-divider></el-divider>
        <el-form-item label="确认函" prop="billUrl" class="link-container">
            <el-link type="primary" :underline="false" v-for="item in buyOrderRebateChargeApply.billFileList" @click="downloadAttachment(item.wholeUrl, item.suffix)">{{item.name}}</el-link>
        </el-form-item>
        <el-form-item label="明细" prop="detailUrl" class="link-container">
            <el-link type="primary" :underline="false" v-for="item in buyOrderRebateChargeApply.detailFileList" @click="downloadAttachment(item.wholeUrl, item.suffix)">{{item.name}}</el-link><br>
        </el-form-item>

        <el-row style="color: #606266; font-weight: 700;padding-left: 10px">审核记录</el-row>
        <el-divider></el-divider>
        <template>
            <el-table
                    :data="this.buyOrderRebateChargeApply.auditRecordEventResults"
                    empty-text="暂无审核记录"
                    border
                    :cell-style="{'text-align':'center'}"
                    :header-cell-style="{'text-align':'center', 'background': '#E3ECD9FF'}"
                    style="width: 70%">
                <el-table-column
                        prop="operationTime"
                        label="操作时间"
                        width="180">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.operationTime)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="operator"
                        label="操作人">
                </el-table-column>
                <el-table-column
                        prop="operation"
                        label="操作事项"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="remark"
                        label="备注">
                </el-table-column>
            </el-table>
        </template>

        <el-dialog title="操作确认" :visible.sync="dialogFormVisible" width="500px">
            <el-form :model="auditForm" :rules="rules" ref="form">
                <el-form-item label="备注" prop="comments">
                    <el-input v-model="auditForm.comments" autocomplete="off" style="width: 85%; height: 26px"></el-input>
                </el-form-item>

                <el-form-item style="text-align: center; margin-top: 10px">
                    <el-button style="background-color: #72BB72; color: #FFFFFF" size="mini" @click="submitAudit('form')">提交</el-button>
                    <el-button style="background-color: #FFAA02; color: #FFFFFF" size="mini" @click="dialogFormVisible = false">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

    </el-form>
</div>
<script src="${pageContext.request.contextPath}/static/api/buyorder/buyOrderRebateChargeApply.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const buyOrderRebateChargeId = '${buyOrderRebateChargeId}';
    new Vue({
        el: '#app',
        data() {
            return {
                buyOrderRebateChargeApply: {},
                dialogFormVisible: false,
                auditForm: {
                    comments: ''
                },
                rules: {
                    comments: [
                        {required: true, message: '请填写备注！', trigger: 'blur'},
                        {max: 256, message: '备注内容不允许超过256个字符', trigger: 'blur' }
                    ]
                },
                globalPassFlag: false
            };
        },

        mounted() {
            loadingApp();
        },

        created() {
            this.getDetail();
        },

        methods: {
            getDetail() {
                getDetailById( {"buyOrderRebateChargeId": buyOrderRebateChargeId}).then(res => {
                    this.buyOrderRebateChargeApply = res.data.data;
                })
            },

            auditOperation(pass) {
                if (pass) {
                    this.rules.comments = [
                        {required: false, trigger: 'blur'}
                    ]
                } else {
                    this.rules.comments = [
                        {required: true, message: '请填写备注！', trigger: 'blur'},
                        {max: 256, message: '备注内容不允许超过256个字符', trigger: 'blur' }
                    ]
                }

                this.globalPassFlag = pass;
                this.auditForm.comments = '';
                this.dialogFormVisible = true;
            },

            // 审核操作
            submitAudit(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        let auditVariables = {
                            taskId: this.buyOrderRebateChargeApply.taskId,
                            comments: this.auditForm.comments,
                            pass: this.globalPassFlag,
                            businessId: buyOrderRebateChargeId
                        };
                        doAudit(auditVariables).then(res => {
                            if (res.data.code == 0) {
                                this.dialogFormVisible = false;
                                openTab("返利申请详情", '/buyOrder/rebateChargeApply/detail.do?buyOrderRebateChargeId=' + buyOrderRebateChargeId);
                                this.closeThis()
                            } else {
                                this.$message.error(res.data.message);
                            }
                        });
                    } else {
                        return false;
                    }
                });
            },

            downloadAttachment(url, suffix) {
                if (suffix == 'jpg' || suffix == 'pdf' || suffix == 'png') {
                    // 预览
                    window.open(url);
                } else {
                    // 下载
                    window.open(url.replace("display", "download"));
                }
            },

            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            }

        }
    })


</script>

<style>
    .el-upload-list__item-status-label {
        margin-right: 50%;
    }

    .el-upload-list__item .el-icon-close {
        margin-right: 50%;
    }
    .el-upload-list__item .el-icon-close-tip {
        margin-right: 50%;
    }

    .link-container .el-link {
        display: block;
    }

</style>

</body>
</html>
