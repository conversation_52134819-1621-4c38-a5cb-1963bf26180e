<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.unitconversionorder.dao.WmsUnitConversionOrderItemMapper">
  <resultMap id="BaseResultMap" type="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem">
    <!--@mbg.generated-->
    <!--@Table T_WMS_UNIT_CONVERSION_ORDER_ITEM-->
    <id column="WMS_UNIT_CONVERSION_ORDER_ITEM_ID" jdbcType="INTEGER" property="wmsUnitConversionOrderItemId" />
    <result column="WMS_UNIT_CONVERSION_ORDER_ID" jdbcType="INTEGER" property="wmsUnitConversionOrderId" />
    <result column="SOURCE_SKU_ID" jdbcType="INTEGER" property="sourceSkuId" />
    <result column="SOURCE_SKU_NO" jdbcType="VARCHAR" property="sourceSkuNo" />
    <result column="SOURCE_GOODS_NAME" jdbcType="VARCHAR" property="sourceGoodsName" />
    <result column="SOURCE_UNIT_ID" jdbcType="INTEGER" property="sourceUnitId" />
    <result column="SOURCE_UNIT_NAME" jdbcType="VARCHAR" property="sourceUnitName" />
    <result column="SOURCE_NUM" jdbcType="DECIMAL" property="sourceNum" />
    <result column="SOURCE_PRICE" jdbcType="DECIMAL" property="sourcePrice" />
    <result column="WMS_REAL_OUT_NUM" jdbcType="DECIMAL" property="wmsRealOutNum" />
    <result column="OUT_STATUS" jdbcType="INTEGER" property="outStatus" />
    <result column="TARGET_SKU_ID" jdbcType="INTEGER" property="targetSkuId" />
    <result column="TARGET_SKU_NO" jdbcType="VARCHAR" property="targetSkuNo" />
    <result column="TARGET_GOODS_NAME" jdbcType="VARCHAR" property="targetGoodsName" />
    <result column="TARGET_UNIT_ID" jdbcType="INTEGER" property="targetUnitId" />
    <result column="TARGET_UNIT_NAME" jdbcType="VARCHAR" property="targetUnitName" />
    <result column="TARGET_NUM" jdbcType="DECIMAL" property="targetNum" />
    <result column="TARGET_PRICE" jdbcType="DECIMAL" property="targetPrice" />
    <result column="WMS_REAL_IN_NUM" jdbcType="DECIMAL" property="wmsRealInNum" />
    <result column="IN_STATUS" jdbcType="INTEGER" property="inStatus" />
    <result column="TAX_RATE" jdbcType="INTEGER" property="taxRate" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    WMS_UNIT_CONVERSION_ORDER_ITEM_ID, WMS_UNIT_CONVERSION_ORDER_ID, SOURCE_SKU_ID, SOURCE_SKU_NO, 
    SOURCE_GOODS_NAME, SOURCE_UNIT_ID, SOURCE_UNIT_NAME, SOURCE_NUM, SOURCE_PRICE, WMS_REAL_OUT_NUM, 
    OUT_STATUS, TARGET_SKU_ID, TARGET_SKU_NO, TARGET_GOODS_NAME, TARGET_UNIT_ID, TARGET_UNIT_NAME, 
    TARGET_NUM, TARGET_PRICE, WMS_REAL_IN_NUM, IN_STATUS, TAX_RATE, IS_DELETE, ADD_TIME, 
    CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_UNIT_CONVERSION_ORDER_ITEM
    where WMS_UNIT_CONVERSION_ORDER_ITEM_ID = #{wmsUnitConversionOrderItemId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_WMS_UNIT_CONVERSION_ORDER_ITEM
    where WMS_UNIT_CONVERSION_ORDER_ITEM_ID = #{wmsUnitConversionOrderItemId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="WMS_UNIT_CONVERSION_ORDER_ITEM_ID" keyProperty="wmsUnitConversionOrderItemId" parameterType="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER_ITEM (WMS_UNIT_CONVERSION_ORDER_ID, SOURCE_SKU_ID, 
      SOURCE_SKU_NO, SOURCE_GOODS_NAME, SOURCE_UNIT_ID, 
      SOURCE_UNIT_NAME, SOURCE_NUM, SOURCE_PRICE, 
      WMS_REAL_OUT_NUM, OUT_STATUS, TARGET_SKU_ID, 
      TARGET_SKU_NO, TARGET_GOODS_NAME, TARGET_UNIT_ID, 
      TARGET_UNIT_NAME, TARGET_NUM, TARGET_PRICE, 
      WMS_REAL_IN_NUM, IN_STATUS, TAX_RATE, 
      IS_DELETE, ADD_TIME, CREATOR, 
      CREATOR_NAME, UPDATER, UPDATER_NAME, 
      MOD_TIME)
    values (#{wmsUnitConversionOrderId,jdbcType=INTEGER}, #{sourceSkuId,jdbcType=INTEGER}, 
      #{sourceSkuNo,jdbcType=VARCHAR}, #{sourceGoodsName,jdbcType=VARCHAR}, #{sourceUnitId,jdbcType=INTEGER}, 
      #{sourceUnitName,jdbcType=VARCHAR}, #{sourceNum,jdbcType=DECIMAL}, #{sourcePrice,jdbcType=DECIMAL}, 
      #{wmsRealOutNum,jdbcType=DECIMAL}, #{outStatus,jdbcType=INTEGER}, #{targetSkuId,jdbcType=INTEGER}, 
      #{targetSkuNo,jdbcType=VARCHAR}, #{targetGoodsName,jdbcType=VARCHAR}, #{targetUnitId,jdbcType=INTEGER}, 
      #{targetUnitName,jdbcType=VARCHAR}, #{targetNum,jdbcType=DECIMAL}, #{targetPrice,jdbcType=DECIMAL}, 
      #{wmsRealInNum,jdbcType=DECIMAL}, #{inStatus,jdbcType=INTEGER}, #{taxRate,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="WMS_UNIT_CONVERSION_ORDER_ITEM_ID" keyProperty="wmsUnitConversionOrderItemId" parameterType="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wmsUnitConversionOrderId != null">
        WMS_UNIT_CONVERSION_ORDER_ID,
      </if>
      <if test="sourceSkuId != null">
        SOURCE_SKU_ID,
      </if>
      <if test="sourceSkuNo != null">
        SOURCE_SKU_NO,
      </if>
      <if test="sourceGoodsName != null">
        SOURCE_GOODS_NAME,
      </if>
      <if test="sourceUnitId != null">
        SOURCE_UNIT_ID,
      </if>
      <if test="sourceUnitName != null">
        SOURCE_UNIT_NAME,
      </if>
      <if test="sourceNum != null">
        SOURCE_NUM,
      </if>
      <if test="sourcePrice != null">
        SOURCE_PRICE,
      </if>
      <if test="wmsRealOutNum != null">
        WMS_REAL_OUT_NUM,
      </if>
      <if test="outStatus != null">
        OUT_STATUS,
      </if>
      <if test="targetSkuId != null">
        TARGET_SKU_ID,
      </if>
      <if test="targetSkuNo != null">
        TARGET_SKU_NO,
      </if>
      <if test="targetGoodsName != null">
        TARGET_GOODS_NAME,
      </if>
      <if test="targetUnitId != null">
        TARGET_UNIT_ID,
      </if>
      <if test="targetUnitName != null">
        TARGET_UNIT_NAME,
      </if>
      <if test="targetNum != null">
        TARGET_NUM,
      </if>
      <if test="targetPrice != null">
        TARGET_PRICE,
      </if>
      <if test="wmsRealInNum != null">
        WMS_REAL_IN_NUM,
      </if>
      <if test="inStatus != null">
        IN_STATUS,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wmsUnitConversionOrderId != null">
        #{wmsUnitConversionOrderId,jdbcType=INTEGER},
      </if>
      <if test="sourceSkuId != null">
        #{sourceSkuId,jdbcType=INTEGER},
      </if>
      <if test="sourceSkuNo != null">
        #{sourceSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="sourceGoodsName != null">
        #{sourceGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="sourceUnitId != null">
        #{sourceUnitId,jdbcType=INTEGER},
      </if>
      <if test="sourceUnitName != null">
        #{sourceUnitName,jdbcType=VARCHAR},
      </if>
      <if test="sourceNum != null">
        #{sourceNum,jdbcType=DECIMAL},
      </if>
      <if test="sourcePrice != null">
        #{sourcePrice,jdbcType=DECIMAL},
      </if>
      <if test="wmsRealOutNum != null">
        #{wmsRealOutNum,jdbcType=DECIMAL},
      </if>
      <if test="outStatus != null">
        #{outStatus,jdbcType=INTEGER},
      </if>
      <if test="targetSkuId != null">
        #{targetSkuId,jdbcType=INTEGER},
      </if>
      <if test="targetSkuNo != null">
        #{targetSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="targetGoodsName != null">
        #{targetGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="targetUnitId != null">
        #{targetUnitId,jdbcType=INTEGER},
      </if>
      <if test="targetUnitName != null">
        #{targetUnitName,jdbcType=VARCHAR},
      </if>
      <if test="targetNum != null">
        #{targetNum,jdbcType=DECIMAL},
      </if>
      <if test="targetPrice != null">
        #{targetPrice,jdbcType=DECIMAL},
      </if>
      <if test="wmsRealInNum != null">
        #{wmsRealInNum,jdbcType=DECIMAL},
      </if>
      <if test="inStatus != null">
        #{inStatus,jdbcType=INTEGER},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem">
    <!--@mbg.generated-->
    update T_WMS_UNIT_CONVERSION_ORDER_ITEM
    <set>
      <if test="wmsUnitConversionOrderId != null">
        WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER},
      </if>
      <if test="sourceSkuId != null">
        SOURCE_SKU_ID = #{sourceSkuId,jdbcType=INTEGER},
      </if>
      <if test="sourceSkuNo != null">
        SOURCE_SKU_NO = #{sourceSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="sourceGoodsName != null">
        SOURCE_GOODS_NAME = #{sourceGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="sourceUnitId != null">
        SOURCE_UNIT_ID = #{sourceUnitId,jdbcType=INTEGER},
      </if>
      <if test="sourceUnitName != null">
        SOURCE_UNIT_NAME = #{sourceUnitName,jdbcType=VARCHAR},
      </if>
      <if test="sourceNum != null">
        SOURCE_NUM = #{sourceNum,jdbcType=DECIMAL},
      </if>
      <if test="sourcePrice != null">
        SOURCE_PRICE = #{sourcePrice,jdbcType=DECIMAL},
      </if>
      <if test="wmsRealOutNum != null">
        WMS_REAL_OUT_NUM = #{wmsRealOutNum,jdbcType=DECIMAL},
      </if>
      <if test="outStatus != null">
        OUT_STATUS = #{outStatus,jdbcType=INTEGER},
      </if>
      <if test="targetSkuId != null">
        TARGET_SKU_ID = #{targetSkuId,jdbcType=INTEGER},
      </if>
      <if test="targetSkuNo != null">
        TARGET_SKU_NO = #{targetSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="targetGoodsName != null">
        TARGET_GOODS_NAME = #{targetGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="targetUnitId != null">
        TARGET_UNIT_ID = #{targetUnitId,jdbcType=INTEGER},
      </if>
      <if test="targetUnitName != null">
        TARGET_UNIT_NAME = #{targetUnitName,jdbcType=VARCHAR},
      </if>
      <if test="targetNum != null">
        TARGET_NUM = #{targetNum,jdbcType=DECIMAL},
      </if>
      <if test="targetPrice != null">
        TARGET_PRICE = #{targetPrice,jdbcType=DECIMAL},
      </if>
      <if test="wmsRealInNum != null">
        WMS_REAL_IN_NUM = #{wmsRealInNum,jdbcType=DECIMAL},
      </if>
      <if test="inStatus != null">
        IN_STATUS = #{inStatus,jdbcType=INTEGER},
      </if>
      <if test="taxRate != null">
        TAX_RATE = #{taxRate,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where WMS_UNIT_CONVERSION_ORDER_ITEM_ID = #{wmsUnitConversionOrderItemId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem">
    <!--@mbg.generated-->
    update T_WMS_UNIT_CONVERSION_ORDER_ITEM
    set WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER},
      SOURCE_SKU_ID = #{sourceSkuId,jdbcType=INTEGER},
      SOURCE_SKU_NO = #{sourceSkuNo,jdbcType=VARCHAR},
      SOURCE_GOODS_NAME = #{sourceGoodsName,jdbcType=VARCHAR},
      SOURCE_UNIT_ID = #{sourceUnitId,jdbcType=INTEGER},
      SOURCE_UNIT_NAME = #{sourceUnitName,jdbcType=VARCHAR},
      SOURCE_NUM = #{sourceNum,jdbcType=DECIMAL},
      SOURCE_PRICE = #{sourcePrice,jdbcType=DECIMAL},
      WMS_REAL_OUT_NUM = #{wmsRealOutNum,jdbcType=DECIMAL},
      OUT_STATUS = #{outStatus,jdbcType=INTEGER},
      TARGET_SKU_ID = #{targetSkuId,jdbcType=INTEGER},
      TARGET_SKU_NO = #{targetSkuNo,jdbcType=VARCHAR},
      TARGET_GOODS_NAME = #{targetGoodsName,jdbcType=VARCHAR},
      TARGET_UNIT_ID = #{targetUnitId,jdbcType=INTEGER},
      TARGET_UNIT_NAME = #{targetUnitName,jdbcType=VARCHAR},
      TARGET_NUM = #{targetNum,jdbcType=DECIMAL},
      TARGET_PRICE = #{targetPrice,jdbcType=DECIMAL},
      WMS_REAL_IN_NUM = #{wmsRealInNum,jdbcType=DECIMAL},
      IN_STATUS = #{inStatus,jdbcType=INTEGER},
      TAX_RATE = #{taxRate,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where WMS_UNIT_CONVERSION_ORDER_ITEM_ID = #{wmsUnitConversionOrderItemId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="WMS_UNIT_CONVERSION_ORDER_ITEM_ID" keyProperty="wmsUnitConversionOrderItemId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER_ITEM
    (WMS_UNIT_CONVERSION_ORDER_ID, SOURCE_SKU_ID, SOURCE_SKU_NO, SOURCE_GOODS_NAME, SOURCE_UNIT_ID, 
      SOURCE_UNIT_NAME, SOURCE_NUM, SOURCE_PRICE, WMS_REAL_OUT_NUM, OUT_STATUS, TARGET_SKU_ID, 
      TARGET_SKU_NO, TARGET_GOODS_NAME, TARGET_UNIT_ID, TARGET_UNIT_NAME, TARGET_NUM, 
      TARGET_PRICE, WMS_REAL_IN_NUM, IN_STATUS, TAX_RATE, IS_DELETE, ADD_TIME, CREATOR, 
      CREATOR_NAME, UPDATER, UPDATER_NAME, MOD_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.wmsUnitConversionOrderId,jdbcType=INTEGER}, #{item.sourceSkuId,jdbcType=INTEGER}, 
        #{item.sourceSkuNo,jdbcType=VARCHAR}, #{item.sourceGoodsName,jdbcType=VARCHAR}, 
        #{item.sourceUnitId,jdbcType=INTEGER}, #{item.sourceUnitName,jdbcType=VARCHAR}, 
        #{item.sourceNum,jdbcType=DECIMAL}, #{item.sourcePrice,jdbcType=DECIMAL}, #{item.wmsRealOutNum,jdbcType=DECIMAL}, 
        #{item.outStatus,jdbcType=INTEGER}, #{item.targetSkuId,jdbcType=INTEGER}, #{item.targetSkuNo,jdbcType=VARCHAR}, 
        #{item.targetGoodsName,jdbcType=VARCHAR}, #{item.targetUnitId,jdbcType=INTEGER}, 
        #{item.targetUnitName,jdbcType=VARCHAR}, #{item.targetNum,jdbcType=DECIMAL}, #{item.targetPrice,jdbcType=DECIMAL}, 
        #{item.wmsRealInNum,jdbcType=DECIMAL}, #{item.inStatus,jdbcType=INTEGER}, #{item.taxRate,jdbcType=INTEGER}, 
        #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.modTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-02-20-->
  <select id="selectByWmsUnitConversionOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WMS_UNIT_CONVERSION_ORDER_ITEM
    where WMS_UNIT_CONVERSION_ORDER_ID=#{wmsUnitConversionOrderId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>
</mapper>