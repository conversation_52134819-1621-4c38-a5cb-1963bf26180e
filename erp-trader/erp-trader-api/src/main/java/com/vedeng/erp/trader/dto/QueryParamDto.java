package com.vedeng.erp.trader.dto;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/6 9:20
 **/
@Setter
@Getter
public class QueryParamDto {


    /**
     * 客户类型
     */
    private List<KeyValue> customerTypeQuery ;

    /**
     * 经销商有效性
     */
    private List<KeyValue> effectiveness ;

    /**
     * 归属销售
     */
    private List<KeyValue> userList;

    /**
     * 经营终端类型
     */
    private List<KeyValue> traderCustomerMarketingTypeList;

    /**
     * 终端机构评级
     */
    private List<KeyValue> institutionLevel;

    /**
     * 主营商品类型
     */
    private List<KeyValue> skuType ;

    /**
     * 主营商品范畴
     */
    private List<KeyValue> traderCustomerMainCategoryType ;

    /**
     * 销售类别
     */
    private List<KeyValue> traderCustomerOwnership ;

    /**
     * 终端机构性质
     */
    private List<KeyValue> institutionNature ;

    /**
     * 核心资源
     */
    private List<KeyValue> traderCustomerDevelopLevel;




    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class KeyValue {
        private Object key;
        private String value;
    }
    
}

