<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="去电|销售" scope="application" />
<%@ include file="./common/header.jsp"%>
<style>
	#leftTip{
		border: 1px solid #E6E6E6;
		margin:10px;
		padding:10px;
		height: 450px;
	}
	#leftTip #typeTip{
		font-size: 14px;
		font-weight: bolder;
	}
	#leftTip span{
		font-size:	12px;
	}
	#tipDetail{
		font-size:12px;
		margin-left: 5px;
	}
	#tipDetail ul{
		list-style-position: inside; /* 将项目符号放在内部 */
		list-style-type: disc;
		margin-left:20px;
	}
	#tipDetail ul li{
		list-style: disc !important;
	}

</style>

<div class="layer-content call-layer-content">
	<!-- 标题 -->
	<div class="callcenter-title">
		<%@ include file="./common/call_out.jsp"%>

		<div class="right-title">
			<%--2020.12http://jira.ivedeng.com/browse/VDERP-5008 需求中要求删除的字段--%>
			<%--<c:if test="${positType == 310 }">
				<span
					onclick="addComm('${phone}',${traderCustomer.traderId},${callOut.traderType },${callType },${orderId},${traderContactId },1,'${callOut.coid }');">新增联系</span>
			</c:if>--%>
				<c:if test="${isClick}">

					<%--<button style="height: 22px;line-height: 22px;" type="button"
												  <c:if test="${callType == 7}">disabled="disabled" title="可直接线索转商机" </c:if>
												  onclick="javascrtpt:window.open(page_url+'/order/bussinesschance/newAddBussinesChance.do?traderId=${traderCustomer.traderId}&traderContactId=${traderCustomer.traderContactId}')">新增商机</button>--%>
					<c:choose>
						<c:when test="${callType == 7}">
							<a   style="color: #ffffff; border: solid 1px #ccc;background: #ccc;" title="可直接线索转商机" href="javascript:void(0);" disabled="disabled"
							   tabTitle='{"num":"addbusinessChance${traderCustomer.traderId}",
										"link":"${lxcrmUrl}/crm/businessChance/profile/add?traderId=${traderCustomer.traderId}&traderContactId=${traderCustomer.traderContactId}&phone=${phone}&traderName=${traderName}",
										"title":"新增商机"}'>
								新增商机</a>
						</c:when>
						<c:otherwise>
							<a class="addtitle" style="color: #ffffff; border: solid 1px #ccc;" href="javascript:void(0);"
							   tabTitle='{"num":"addbusinessChance${traderCustomer.traderId}",
										"link":"${lxcrmUrl}/crm/businessChance/profile/add?traderId=${traderCustomer.traderId}&traderContactId=${traderCustomer.traderContactId}&phone=${phone}&traderName=${traderName}",
										"title":"新增商机"}'>
								新增商机</a>
						</c:otherwise>
					</c:choose>

				</c:if>	<i class="iconclosetitle" onclick="window.parent.closeScreenAll();"></i>
		</div>

	</div>
	<!-- 表格信息 -->
	<div class="content-box" style="display: flex;">
		<div class="content-colum content-colum2" style="width: 35%;display: inline-block;">
			<div id="leftTip" style="">
					<span id="typeTip">通话类型：
					<c:choose>
						<c:when test="${callType == 9}">
							产品推广
						</c:when>
						<c:when test="${callType == 1}">
							商机跟进
						</c:when>
						<c:when test="${callType == 10}">
							客户开发
						</c:when>
						<c:when test="${callType == 3}">
							报价响应
						</c:when>
						<c:when test="${callType == 2}">
							商务处理
						</c:when>
						<c:when test="${callType == 4}">
							售后
						</c:when>
						<c:otherwise>
							其他
						</c:otherwise>
					</c:choose>
						</span>
				<div>
					<li style="margin-top:5px;margin-bottom:5px;font-weight: bolder;">话术关键词提示：</li>
				</div>

					<div id="tipDetail">
						<ul >
						<c:choose>
						<c:when test="${callType == 9}">
						<li>商品：品牌，型号，参数、价格、授权范围</li>
						<li>活动</li>
						<li>地方性政策	</li>
						<li>方案	</li>
						<li>利润	</li>
						<li>预算	</li>
						<li>代理政策	</li>
						<li>经销政策	</li>
						<li>售后政策：如7天无理由等	</li>
						</c:when>
						<c:when test="${callType == 1}">
						<li>意向提示：品牌、型号、货期	</li>
						<li>采购方式提示：直采、招标挂网、内部竞价、其他	</li>
						<li>预计成单时间	</li>
						<li>预计金额	</li>
						<li>关键决策人/职位提示：	</li>
						<li>（渠道商：老板、采购负责人、采购专员、销售负责人、销售专员、财务负责人、财务专员、物流负责人、物流专员、商务负责人、商务专员、售后负责人、售后专员、其他；	</li>
						<li>终端：总经理、院长、副院长、采购负责人、采购专员、科室主任、科室医生、护士长、护士、运营负责人、运营专员、财务负责人、财务专员、设备科长、医工、库房负责人、库房专员、咨询师；）	</li>
						<li>终端名称	</li>
						<li>资金是否到位	</li>
						<li>客户关心问题和疑惑	</li>

						</c:when>
						<c:when test="${callType == 10}">
						<li>客户标签提示：客户类型、经营客户类型、经营品类、经营模式、经营地区、客户意向商品	</li>
						<li>贝登介绍：规模、代理区域、合作机会、贝登商城关注	</li>
						<li>客户近期动态：是否有项目	</li>
						<li>客户转介绍	</li>
						<li>推荐贝登	</li>
						</c:when>
						<c:when test="${callType == 3}">
						<li>报价单/表	</li>
						<li>申请价格/优惠	</li>
						<li>终端名称	</li>
						<li>客户单位名称	</li>
						<li>商品（品牌、型号、参数）	</li>
						<li>采购日期、采购流程等	</li>

						</c:when>
						<c:when test="${callType == 2}">
						<li>货期	</li>
						<li>合同信息（收货联系人、地址，收票联系人、专票普票、直发普发）	</li>
						<li>合同回传	</li>
						<li>开票	</li>
						<li>签收	</li>
						<li>下单方式：线上下单、线下制单	</li>

						</c:when>
						<c:when test="${callType == 4}">
						<li>售后类型提示：退货、换货、退款、退票、丢票、安调、维修、技术咨询、其他	</li>
						<li>售后订单	</li>
						<li>售后商品	</li>

						</c:when>
						<c:otherwise>
						<li>暂无提示	</li>
						</c:otherwise>
					</c:choose>
						</ul>
					</div>


			</div>

		</div>
		<div class="content-colum content-colum2" style="width: 65%;display: inline-block;">
			<c:choose>
				<c:when test="${isClick}">
					<%@ include file="./common/customer_info.jsp"%>
					<%@ include file="./common/communicate.jsp"%>
					<%@ include file="./common/bussiness_chance.jsp"%>
					<%@ include file="./common/quoteorder.jsp"%>
					<%@ include file="./common/saleorder.jsp"%>
				</c:when>
				<c:otherwise>
					<%@ include file="./common/customer_info_nolink.jsp"%>
					<%@ include file="./common/communicate.jsp"%>
					<%@ include file="./common/bussiness_chance_nolink.jsp"%>
					<%@ include file="./common/quoteorder_nolink.jsp"%>
					<%@ include file="./common/saleorder_nolink.jsp"%>
				</c:otherwise>
			</c:choose>




		</div>
		<div class="clear"></div>
	</div>
</div>

<%@ include file="../common/footer.jsp"%>