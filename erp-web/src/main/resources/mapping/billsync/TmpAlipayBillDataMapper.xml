<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.billsync.dao.TmpAlipayBillDataMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.billsync.task.model.entity.generate.TmpAlipayBillDataDo">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="FINANCIAL_TURNOVER" jdbcType="VARCHAR" property="financialTurnover" />
    <result column="BUSINESS_TURNOVER" jdbcType="VARCHAR" property="businessTurnover" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="OCCURRENCE_TIME" jdbcType="TIMESTAMP" property="occurrenceTime" />
    <result column="OTHER_ACCOUNT" jdbcType="VARCHAR" property="otherAccount" />
    <result column="INCOME" jdbcType="DECIMAL" property="income" />
    <result column="OUTLAY" jdbcType="DECIMAL" property="outlay" />
    <result column="BALANCE" jdbcType="DECIMAL" property="balance" />
    <result column="TRADING_CHANNEL" jdbcType="VARCHAR" property="tradingChannel" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="SYNC_TIME" jdbcType="DATE" property="syncTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, FINANCIAL_TURNOVER, BUSINESS_TURNOVER, ORDER_NO, SKU_NAME, OCCURRENCE_TIME, OTHER_ACCOUNT, 
    INCOME, OUTLAY, BALANCE, TRADING_CHANNEL, BUSINESS_TYPE, REMARK, SYNC_TIME, UPDATE_TIME, 
    ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TMP_ALIPAY_BILL_DATA
    where ID = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByFinancialTurnover" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from TMP_ALIPAY_BILL_DATA
    where FINANCIAL_TURNOVER = #{financialTurnover,jdbcType=VARCHAR}
    LIMIT 1
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TMP_ALIPAY_BILL_DATA
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.billsync.task.model.entity.generate.TmpAlipayBillDataDo" useGeneratedKeys="true">
    insert into TMP_ALIPAY_BILL_DATA (FINANCIAL_TURNOVER, BUSINESS_TURNOVER, 
      ORDER_NO, SKU_NAME, OCCURRENCE_TIME, 
      OTHER_ACCOUNT, INCOME, OUTLAY, 
      BALANCE, TRADING_CHANNEL, BUSINESS_TYPE, 
      REMARK, SYNC_TIME,
      ADD_TIME)
    values (#{financialTurnover,jdbcType=VARCHAR}, #{businessTurnover,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, #{occurrenceTime,jdbcType=TIMESTAMP}, 
      #{otherAccount,jdbcType=VARCHAR}, #{income,jdbcType=DECIMAL}, #{outlay,jdbcType=DECIMAL}, 
      #{balance,jdbcType=DECIMAL}, #{tradingChannel,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{syncTime,jdbcType=DATE},
      #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.billsync.task.model.entity.generate.TmpAlipayBillDataDo" useGeneratedKeys="true">
    insert into TMP_ALIPAY_BILL_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="financialTurnover != null">
        FINANCIAL_TURNOVER,
      </if>
      <if test="businessTurnover != null">
        BUSINESS_TURNOVER,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="skuName != null">
        SKU_NAME,
      </if>
      <if test="occurrenceTime != null">
        OCCURRENCE_TIME,
      </if>
      <if test="otherAccount != null">
        OTHER_ACCOUNT,
      </if>
      <if test="income != null">
        INCOME,
      </if>
      <if test="outlay != null">
        OUTLAY,
      </if>
      <if test="balance != null">
        BALANCE,
      </if>
      <if test="tradingChannel != null">
        TRADING_CHANNEL,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="syncTime != null">
        SYNC_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="financialTurnover != null">
        #{financialTurnover,jdbcType=VARCHAR},
      </if>
      <if test="businessTurnover != null">
        #{businessTurnover,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="occurrenceTime != null">
        #{occurrenceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="otherAccount != null">
        #{otherAccount,jdbcType=VARCHAR},
      </if>
      <if test="income != null">
        #{income,jdbcType=DECIMAL},
      </if>
      <if test="outlay != null">
        #{outlay,jdbcType=DECIMAL},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=DECIMAL},
      </if>
      <if test="tradingChannel != null">
        #{tradingChannel,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="syncTime != null">
        #{syncTime,jdbcType=DATE},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.billsync.task.model.entity.generate.TmpAlipayBillDataDo">
    update TMP_ALIPAY_BILL_DATA
    <set>
      <if test="financialTurnover != null">
        FINANCIAL_TURNOVER = #{financialTurnover,jdbcType=VARCHAR},
      </if>
      <if test="businessTurnover != null">
        BUSINESS_TURNOVER = #{businessTurnover,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="occurrenceTime != null">
        OCCURRENCE_TIME = #{occurrenceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="otherAccount != null">
        OTHER_ACCOUNT = #{otherAccount,jdbcType=VARCHAR},
      </if>
      <if test="income != null">
        INCOME = #{income,jdbcType=DECIMAL},
      </if>
      <if test="outlay != null">
        OUTLAY = #{outlay,jdbcType=DECIMAL},
      </if>
      <if test="balance != null">
        BALANCE = #{balance,jdbcType=DECIMAL},
      </if>
      <if test="tradingChannel != null">
        TRADING_CHANNEL = #{tradingChannel,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="syncTime != null">
        SYNC_TIME = #{syncTime,jdbcType=DATE},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.billsync.task.model.entity.generate.TmpAlipayBillDataDo">
    update TMP_ALIPAY_BILL_DATA
    set FINANCIAL_TURNOVER = #{financialTurnover,jdbcType=VARCHAR},
      BUSINESS_TURNOVER = #{businessTurnover,jdbcType=VARCHAR},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      OCCURRENCE_TIME = #{occurrenceTime,jdbcType=TIMESTAMP},
      OTHER_ACCOUNT = #{otherAccount,jdbcType=VARCHAR},
      INCOME = #{income,jdbcType=DECIMAL},
      OUTLAY = #{outlay,jdbcType=DECIMAL},
      BALANCE = #{balance,jdbcType=DECIMAL},
      TRADING_CHANNEL = #{tradingChannel,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      SYNC_TIME = #{syncTime,jdbcType=DATE},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>