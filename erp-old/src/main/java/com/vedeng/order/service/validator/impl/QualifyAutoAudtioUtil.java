package com.vedeng.order.service.validator.impl;

public class QualifyAutoAudtioUtil {

    public static String getTraderInfoKey(){
        return "traderInfo";
    }

    //华丽的分割线----------------------------SPU相关信息------------------------
    public static String getSpuInfoKey(String skuNo){
        return "spuInfo_" + skuNo;
    }

    public static String getFirstEngageInfoKey(String skuNo) {
        return "firstEngageInfo_" + skuNo;
    }

    public static String hasRegistrationCertKey(String skuNo){
        return "hasRegistrationCert_" + skuNo;
    }

    public static String getRegisterCertificateInfoKey(String skuNo) {
        return "registerCertificateInfo_" + skuNo;
    }

    //华丽的分割线----------------------------供应商相关信息------------------------
    public static String getBusinessLicenseUploadKey(Integer traderId){
        return "trader_" + traderId + "_isBusinessLicenseUpload";
    }

    public static String getBusinessValidKey(Integer traderId){
        return "trader_" + traderId + "_isBusinessValid";
    }

    public static String getBusinessMedicalKey(Integer traderId){
        return "trader_" + traderId + "_isBusinessMedical";
    }

    public static String getSecondCategoryUploadKey(Integer traderId) {
        return "trader_" + traderId + "_twoMedicalLicenseUpload";
    }

    public static String getThreeMedicalLicenseUploadKey(Integer traderId) {
        return "trader_" + traderId + "_threeMedicalLicenseUpload";
    }

    public static String getOldSecondCategoryListKey(Integer traderId) {
        return "trader_" + traderId + "_oldSecondCategoryList";
    }

    public static String getNewSecondCategoryListKey(Integer traderId) {
        return "trader_" + traderId + "_newSecondCategoryList";
    }

    public static String getOldThirdCategoryListKey(Integer traderId) {
        return "trader_" + traderId + "_oldThirdCategoryList";
    }

    public static String getNewThirdCategoryListKey(Integer traderId) {
        return "trader_" + traderId + "_newThirdCategoryList";
    }

    public static String getFirstCategoryListKey(Integer traderId) {
        return "trader_" + traderId + "_firstCategoryList";
    }

    public static String getProductPermissionListKey(Integer traderId) {
        return "trader_" + traderId + "_productPermissionList";
    }

    public static String getProductRegistrationListKey(Integer traderId) {
        return "trader_" + traderId + "_productRegistrationList";
    }

    public static String getSecondCategoryListKey(Integer traderId) {
        return "trader_" + traderId + "_secondCategoryList";
    }

    public static String getThreeMedicalLicenseListKey(Integer traderId) {
        return "trader_" + traderId + "_threeMedicalLicenseList";
    }
}
