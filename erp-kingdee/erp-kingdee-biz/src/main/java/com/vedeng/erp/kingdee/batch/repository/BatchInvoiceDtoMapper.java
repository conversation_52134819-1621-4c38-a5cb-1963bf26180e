package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BatchInvoiceDtoMapper {
    /**
     * 采购单的蓝字有效票推送 排除已推送排除新蓝字
     * @param batchInvoiceDto 票相关信息
     * @return 票记录
     */
    List<BatchInvoiceDto> buyorderBlueEnableInvoicefindByAll(BatchInvoiceDto batchInvoiceDto);


    /**
     * 采购单的蓝字有效票推送
     * @param batchInvoiceDto 票相关信息
     * @return 票记录
     */
    List<BatchInvoiceDto> buyorderNewBlueEnableInvoicefindByAll(BatchInvoiceDto batchInvoiceDto);


    /**
     * 采购单售后的红字有效票推送
     * @param batchInvoiceDto 票相关信息
     * @return 票记录
     */
    List<BatchInvoiceDto> buyorderRedEnableInvoicefindByAll(BatchInvoiceDto batchInvoiceDto);

    /**
     * 查找是否有蓝字作废
     * @param invoiceCode
     * @param invoiceNo
     * @return
     */
    List<BatchInvoiceDto> findBlueDisEnableByInvoiceCodeAndInvoiceNo(@Param("invoiceCode")String invoiceCode, @Param("invoiceNo")String invoiceNo,@Param("type")Integer type);

    /**
     * 采购费用单的蓝字有效票推送 排除已推送排除新蓝字
     * @param batchInvoiceDto 票相关信息
     * @return 票记录
     */
    List<BatchInvoiceDto> findByAll(BatchInvoiceDto batchInvoiceDto);

    /**
     * 销售售后手续费-应收单
     */
    List<BatchInvoiceDto> saleOrderAfterSaleReceiveFeeInvoice(BatchInvoiceDto batchInvoiceDto);

    /**
     * 销售售后录票-费用应付单
     */
    List<BatchInvoiceDto> saleOrderAfterSalePayExpenseReader(BatchInvoiceDto batchInvoiceDto);

    /**
     * 销售售后录票-进项票
     */
    List<BatchInvoiceDto> saleOrderAfterSaleInputFeeReader(BatchInvoiceDto batchInvoiceDto);

    /**
     * 销售售后手续费-销项费用票
     */
    List<BatchInvoiceDto> saleOrderAfterSaleOutPutFeeInvoice(BatchInvoiceDto batchInvoiceDto);

    String findSaleorderNoByRelatedId(Integer relatedId);

    String findAfterSaleOrderNoByAfterSaleId(Integer afterSalesId);

    /**
     * 查询销售商品 蓝票信息（包括实物商品和费用商品）
     *
     * @param batchInvoiceDto BatchInvoiceDto
     * @return List<BatchInvoiceDto>
     */
    List<BatchInvoiceDto> findSaleOrderBlueInvoiceBatch(BatchInvoiceDto batchInvoiceDto);

    /**
     * 获取销售单售后红票
     * @param batchInvoiceDto 票相关信息
     * @return 票记录
     */
    List<BatchInvoiceDto> saleorderRedInvoiceFindByAll(BatchInvoiceDto batchInvoiceDto);

    /**
     * 根据蓝字有效票id查询蓝字作废票
     * @param invoiceId
     * @return
     */
    List<InvoiceDto> findDeprecatedBlueInvoice(Integer invoiceId);

    /**
     * 查询符合条件的销售红票，生成虚拟出入库记录
     * @param batchInvoiceDto
     * @return
     */
    List<BatchInvoiceDto> queryAllNeedVirturalInvoice(BatchInvoiceDto batchInvoiceDto);


    String getSaleOrderNoBySaleOrderId(Integer saleOrderId);


    String getAfterSalesNoByAfterSalesId(Integer afterSalesId);

    /**
     * 查询售后单发票费用信息
     * @param invoiceDetailId
     * @return
     */
    BatchAfterSalesGoodsDto getAfterSalesGoodsByInvoiceDetialId(Integer invoiceDetailId);

    List<Integer> findByRelatedIdAndTypeAndColorType(BatchInvoiceDto batchInvoiceDto);

    /**
     * 查询提前开票的 销售蓝票信息 应收（包括实物商品和费用商品）
     *
     * @param batchInvoiceDto BatchInvoiceDto
     * @return List<BatchInvoiceDto>
     */
    List<BatchInvoiceDto> findAdvanceSaleOrderReceivableBatch(BatchInvoiceDto batchInvoiceDto);


    /**
     * 查询提前开票的 销售蓝票信息（包括实物商品和费用商品）
     *
     * @param batchInvoiceDto BatchInvoiceDto
     * @return List<BatchInvoiceDto>
     */
    List<BatchInvoiceDto> findAdvanceSaleOrderBlueInvoiceBatch(BatchInvoiceDto batchInvoiceDto);


    /**
     *
     * @param saleOrderNo
     * @return b.ADD_TIME,b.RELATED_ID,c.TRADER_CUSTOMER_ID
     */
    BatchInvoiceDto findBySaleOrderNoAndMinAddTime(String saleOrderNo);

    /**
     *
     * @param afterSaleNo
     * @return b.ADD_TIME,b.RELATED_ID,c.TRADER_CUSTOMER_ID
     */
    BatchInvoiceDto findBySaleOrderNoAndMinAddTimeByAfterSaleNo(String afterSaleNo);

    /**
     * 查当前采购单下蓝票（包含作废）
     * @param relatedId
     * @return
     */
    List<BatchInvoiceDto> selectBuyOrderBlueInvoiceByRelatedId(@Param("relatedId")Integer relatedId);

    /**
     * 根据售后单查采购红票
     * @param afterSaleId
     * @return
     */
    List<BatchInvoiceDto> selectBuyOrderRedInvoiceByAfterSaleId(@Param("afterSaleId")Integer afterSaleId);

    BatchInvoiceDto findByInvoiceId(@Param("invoiceId")Integer invoiceId);





    /**
     * 发票Pdf附件查询通用方法
     *
     * @param batchInvoiceDto 查询参数
     * @return List<BatchInvoiceDto>
     */
    List<BatchInvoiceDto> commonInvoicePdfFileReader(BatchInvoiceDto batchInvoiceDto);

    /**
     * 发票Xml附件查询通用方法
     *
     * @param batchInvoiceDto 查询参数
     * @return List<BatchInvoiceDto>
     */
    List<BatchInvoiceDto> commonInvoiceXmlFileReader(BatchInvoiceDto batchInvoiceDto);

}