package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.TraderCustomerErpEntity;
import java.util.List;

import com.vedeng.erp.trader.dto.CustomerQueryDto;
import com.vedeng.erp.trader.dto.TraderCustomerErpDto;
import org.apache.ibatis.annotations.Param;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/8/11 14:43
 **/
public interface TraderCustomerErpMapper {
    int deleteByPrimaryKey(Integer traderCustomerId);

    int insert(TraderCustomerErpEntity record);

    int insertSelective(TraderCustomerErpEntity record);

    TraderCustomerErpEntity selectByPrimaryKey(Integer traderCustomerId);

    int updateByPrimaryKeySelective(TraderCustomerErpEntity record);

    int updateByPrimaryKey(TraderCustomerErpEntity record);

    int batchInsert(@Param("list") List<TraderCustomerErpEntity> list);

    TraderCustomerErpEntity selectByTraderName(String traderName);

    List<TraderCustomerErpDto> findByAll(@Param("listCustomerQuery") CustomerQueryDto traderCustomerErpDto);


}