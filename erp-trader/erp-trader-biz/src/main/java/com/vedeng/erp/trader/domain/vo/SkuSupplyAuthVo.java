package com.vedeng.erp.trader.domain.vo;

import com.vedeng.erp.trader.domain.dto.SkuSupplyAuth;
import com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail;
import com.vedeng.infrastructure.file.domain.Attachment;
import lombok.Data;

import java.util.List;


@Data
public class SkuSupplyAuthVo extends SkuSupplyAuth {
    /**
     * 授权明细绑定sku信息
     */
    private List<SkuSupplyAuthDetail> skuSupplyAuthDetails;
    /**
     * 授权书品牌名
     */
    private String brandNames;
    /**
     * 图片格式文件
     */
    private List<Attachment> pictureAttachment;
    /**
     * 文件格式文件
     */
    private List<Attachment> fileAttachment;
    /**
     * 授权书列表页面展示的图片格式串
     */
    private List<String> pictureAttachmentJsonStrings;
    /**
     * 品牌名称，用逗号隔开
     */
    private String brandNameString;

    private String authStatusShow;
}
