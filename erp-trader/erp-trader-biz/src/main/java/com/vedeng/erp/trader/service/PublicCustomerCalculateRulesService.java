package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.PublicCustomerCalculateRules;
import com.vedeng.erp.trader.domain.vo.PublicCustomerCalculateRulesDetailVo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/2/15 16:26
 */
public interface PublicCustomerCalculateRulesService {

    /**
     * 公海条件配置详情页
     */
    PublicCustomerCalculateRulesDetailVo getRulesDetail();

    /**
     * 保存公海条件配置
     */
    int saveCalculateRule(PublicCustomerCalculateRules publicCustomerCalculateRules);

}
