<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<meta charset="UTF-8">
<div id="app" style="padding:5px">
    <div class="payApplyForm">
        <el-form :inline="true" :model="formInline" size="mini" label-position="right" label-width="100px">
            <el-form-item label="订单号" size="mini">
                <el-input v-model="formInline.buyorderNo" placeholder="" style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="供应商名称">
                <el-input v-model="formInline.buyorderTraderName" placeholder="" style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="收款名称">
                <el-input v-model="formInline.traderName" placeholder="" style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="交易主体">
                <el-select v-model="formInline.traderSubject" placeholder="" style="width: 200px">
                    <el-option label="全部" value="-1"></el-option>
                    <el-option label="对公" value="1"></el-option>
                    <el-option label="对私" value="2"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="审核状态">
                <el-select v-model="formInline.validStatus" placeholder="" style="width: 200px">
                    <el-option label="全部" value="-1"></el-option>
                    <el-option label="审核中" value="0"></el-option>
                    <el-option label="通过" value="1"></el-option>
                    <el-option label="不通过" value="2"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="制单状态">
                <el-select v-model="formInline.isBill" placeholder="" style="width: 200px">
                    <el-option label="全部" value="-1"></el-option>
                    <el-option label="未制单" value="0"></el-option>
                    <el-option label="已制单" value="1"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="订单金额">
                <div class="form-line">
                    <el-input v-model="formInline.searchBeginAmount" placeholder="" style="width: 90px"></el-input>
                    <span class="separator">-</span>
                    <el-input v-model="formInline.searchEndAmount" placeholder="" style="width: 90px"></el-input>
                </div>
            </el-form-item>

            <el-form-item label="申请时间">
                <el-date-picker
                        v-model="applyTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="daterange"
                        style="width: 200px"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="流水交易时间">
                <el-date-picker
                        v-model="payTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="daterange"
                        style="width: 200px"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="内部付款备注">
                <el-select v-model="formInline.comments" placeholder="" style="width: 200px">
                    <el-option label="全部" value="-1"></el-option>
                    <el-option label="有" value="1"></el-option>
                    <el-option label="无" value="0"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="往来单位类型">
                <el-select v-model="formInline.accountType" placeholder="" style="width: 200px">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="客户" value="0"></el-option>
                    <el-option label="供应商" value="1"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="满足自动制单">
                <el-select v-model="formInline.autoBill" placeholder="" style="width: 200px">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="否" value="0"></el-option>
                    <el-option label="是" value="1"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="制单方式">
                <el-select v-model="formInline.billMethod" placeholder="" style="width: 200px">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="手动" value="0"></el-option>
                    <el-option label="自动" value="1"></el-option>
                </el-select>
            </el-form-item>
            <div style="display: flex;justify-content: center;">
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" :loading="tableLoading">查询</el-button>
                    <el-button @click="resetForm">重置</el-button>
                    <el-button>
                        <templat>
                            <span id="paymentAllocation" class="pop-new-data-noclose bt-small bg-light-blue bt-bg-style"
                                  layerParams='{"width":"55%","height":"600px","title":"付款配置","link":"/pay/payApply/allocation.do"}'>付款配置</span>
                        </templat>
                    </el-button>
                </el-form-item>
            </div>
        </el-form>
    </div>

    <div class="payApplyTab">
        <el-tabs v-model="activeName" @tab-click="handleClick" v-loading="tabLoading" style="width: 390px">
            <el-tab-pane name="first">
                <template slot="label">
                    <el-badge style="font-size: 12px" :value="firstNum" :hidden="firstNum == 0" :max="maxNum"
                              class="badge-item">银行转账
                    </el-badge>
                </template>
            </el-tab-pane>
            <el-tab-pane name="second">
                <template slot="label">
                    <el-badge style="font-size: 12px" :value="secondNum" :hidden="secondNum == 0" :max="maxNum"
                              class="badge-item">微信
                    </el-badge>
                </template>
            </el-tab-pane>
            <el-tab-pane name="third">
                <template slot="label">
                    <el-badge style="font-size: 12px" :value="thirdNum" :hidden="thirdNum == 0" :max="maxNum"
                              class="badge-item">支付宝
                    </el-badge>
                </template>
            </el-tab-pane>
            <el-tab-pane name="fourth">
                <template slot="label">
                    <el-badge style="font-size: 12px" :value="fourthNum" :hidden="fourthNum == 0" :max="maxNum"
                              class="badge-item">余额
                    </el-badge>
                </template>
            </el-tab-pane>
            <el-tab-pane name="fifth">
                <template slot="label">
                    <el-badge style="font-size: 12px" :value="fifthNum" :hidden="fifthNum == 0" :max="maxNum"
                              class="badge-item">银行承兑汇票
                    </el-badge>
                </template>
            </el-tab-pane>
        </el-tabs>
    </div>

    <div class="payApplyTable">
        <el-table
                v-loading="tableLoading"
                ref="multipleTable"
                tooltip-effect="dark"
                highlight-current-row
                @selection-change="handleSelectionChange"
                :data="tableData"
                border
                size="small"
        >
            <el-table-column
                    align="center"
                    header-align="center"
                    type="selection"
                    :selectable="selectable"
                    width="40">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="payApplyId"
                    label="ID"
                    show-overflow-tooltip
                    width="60">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="buyorderNo"
                    label="订单号"
                    show-overflow-tooltip
                    width="230">
                <template slot-scope="scope">
                    <div style="display:flex;justify-content:center">
                        <el-link type="primary" v-if="scope.row.payType == 517" @click="viewDetail(scope.row)"
                                 style="font-size: 12px">
                            {{scope.row.buyorderNo}}
                        </el-link>
                        <el-link type="primary" v-if="scope.row.payType == 4125" @click="viewDetail(scope.row)"
                                 style="font-size: 12px">
                            {{scope.row.buyorderNo}}
                        </el-link>
                        <el-link type="primary" v-if="scope.row.payType == 518" @click="viewDetail(scope.row)"
                                 style="font-size: 12px">
                            {{scope.row.afterSalesNo}}
                        </el-link>
                        <span v-if="scope.row.currentUserCanProcess" style="color: red;margin-left:5px">[审]</span>
                    </div>

                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="buyorderTraderName"
                    label="供应商名称"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="amount"
                    label="申请金额"
                    width="80">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="addTime"
                    label="申请时间"
                    show-overflow-tooltip
                    width="140">
                <template #default="{ row }">
                    <span v-if="row.addTime">{{ formatDate(row.addTime) }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="creatorName"
                    label="申请人"
                    show-overflow-tooltip
                    width="90">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="traderName"
                    label="收款名称"
                    show-overflow-tooltip
                    width="250">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="traderSubject"
                    label="交易主体"
                    show-overflow-tooltip
                    width="80">
                <template #default="{ row }">
                    <span>{{ row.traderSubject === 1 ? '对公' : '对私' }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="verifyStatus"
                    label="审核状态"
                    show-overflow-tooltip
                    width="70">
                <template #default="{ row }">
                    <span>
                      {{ row.verifyStatus === 0 ? '审核中' :
                            row.verifyStatus === 1 ? '审核通过' :
                                    row.verifyStatus === 2 ? '审核未通过' : '待审核' }}
                    </span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="isBill"
                    label="制单状态"
                    show-overflow-tooltip
                    width="100">
                <template #default="{ row }">
                    <span>{{ row.isBill === 1 ? '已制单' : '未制单' }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="comments"
                    label="内部付款备注"
                    show-overflow-tooltip
                    width="300">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="bankRemark"
                    label="银行回单备注"
                    show-overflow-tooltip
                    width="300">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="payBankTypeName"
                    label="付款银行"
                    show-overflow-tooltip
                    width="200">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="accountType"
                    label="往来单位类型"
                    show-overflow-tooltip
                    width="100">
                <template #default="{ row }">
                    <span>{{ row.accountType === 0 ? '客户' : row.accountType === 1 ? '供应商' : '' }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="autoBill"
                    label="满足自动制单"
                    show-overflow-tooltip
                    width="100">
                <template #default="{ row }">
                    <span>{{ row.autoBill === 1 ? '是' : '否' }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="billMethod"
                    label="制单方式"
                    show-overflow-tooltip
                    width="100">
                <template #default="{ row }">
                    <span> {{ row.billMethod === 0 ? '手动' : row.billMethod === 1 ? '自动' : '' }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="billTime"
                    label="制单时间"
                    show-overflow-tooltip
                    width="140">
                <template #default="{ row }">
                    <span v-if="row.billTime">{{ formatDate(row.billTime) }}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="creditType"
                    label="授信类型"
                    show-overflow-tooltip
                    width="100">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="offline"
                    label="线下开票"
                    show-overflow-tooltip
                    width="100">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="submitStatus"
                    label="提交银行状态"
                    show-overflow-tooltip
                    width="100">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    prop="submitMsg"
                    label="提交银行说明"
                    show-overflow-tooltip
                    width="100">
            </el-table-column>

            <el-table-column
                    align="center"
                    header-align="center"
                    fixed="right"
                    label="操作"
                    width="200">
                <template #default="{ row }">
                    <span v-if="row.isBill == 1 && row.verifyStatus == 0">
                        <span v-if="row.payType == 517 || row.payType == 4125">
                            <span v-if="row.traderMode == 10001">
                                <el-button size="mini" type="primary"
                                           @click="passBuyOrderBankAcceptance(row)">通过</el-button>
                                <el-button size="mini" type="danger" @click="rejectBuyOrder(row)">不通过</el-button>
                            </span>
                            <span v-else>
                                <el-button size="mini" type="primary" @click="passBuyOrder(row)">通过</el-button>
                                <el-button size="mini" type="danger" @click="rejectBuyOrder(row)">不通过</el-button>
                            </span>
                        </span>
                        <span v-if="row.payType == 518">
                            <el-button size="mini" type="primary" @click="passAfterSale(row)">通过</el-button>
                            <el-button size="mini" type="danger" @click="rejectAfterSale(row)">不通过</el-button>
                        </span>
                    </span>
                    <span v-if="row.isBill == 0 && (row.payType == 517 || row.payType == 4125)">
                        <span v-if="row.traderMode == 10001">
                            <el-button size="mini" type="primary" @click="openBankAcceptanceDialog(row)">同意</el-button>
                            <el-button size="mini" type="danger" @click="disagreeBuyOrder(row)">不同意</el-button>
                        </span>
                        <span v-else>
                            <el-button size="mini" type="primary" @click="agreeBuyOrder(row)">同意</el-button>
                            <el-button size="mini" type="danger" @click="disagreeBuyOrder(row)">不同意</el-button>
                        </span>
                    </span>
                    <span v-if="row.isBill == 0 && row.payType == 518">
                        <el-button size="mini" type="primary" @click="agreeAfterSale(row)">同意</el-button>
                        <el-button size="mini" type="danger" @click="disagreeAfterSale(row)">不同意</el-button>
                    </span>
                </template>
            </el-table-column>

        </el-table>
        <div style="display: flex;flex-direction:column;text-align:center;margin-top:10px;margin-left:10px">
            <div style="display: flex;justify-content: space-between;">
                <div>
                    <span style="font-size:14px;">已选择{{ multipleSelection.length }}条</span>
                    &nbsp;
                    <el-button size="small" @click="getSelectedRows">同意</el-button>
                    <input id="payVedengBankIdParent" type="hidden" value="">
                    <input id="commentParent" type="hidden" value="">
                </div>

                <div>
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="10"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total=total>
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>

    <div class="payApplyFooter">

    </div>
    <!-- 承兑信息弹框 -->
    <el-dialog title="承兑信息" :visible.sync="bankAcceptanceDialogVisible" width="500px">
        <div>
            <el-form v-loading="formLoading" :rules="rules" :model="bankAcceptanceForm" ref="bankAcceptanceForm">
                <el-form-item label="承兑银行">
                    <el-radio-group v-model="bankAcceptanceForm.bank">
                        <el-radio :label="1">民生银行</el-radio>
                        <el-radio disabled="true" :label="2">交通银行</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item prop="creditType" label="授信类型" required>
                    <el-radio-group v-model="bankAcceptanceForm.creditType">
                        <el-radio :label="1">综合授信</el-radio>
                        <el-radio :label="2">单笔授信</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="线下开票">
                    <el-radio-group v-model="bankAcceptanceForm.offlineInvoice">
                        <el-radio :label="0">否</el-radio>
                        <el-radio :label="1">是</el-radio>
                    </el-radio-group>
                    <el-tooltip class="item" effect="dark" content="线下开具承兑汇票时选择&quot;是&quot;，选择&quot;是&quot;时不会调用银行接口开票"
                                placement="top">
                        <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                </el-form-item>
                <!-- 新增减免单号下拉框 -->
                <el-form-item prop="discountNumber" label="减免单号" v-if="bankAcceptanceForm.offlineInvoice === 0">
                    <el-select v-model="bankAcceptanceForm.discountNumber" placeholder="请选择减免单号" clearable
                               style="width: 250px;">
                        <el-option
                                v-for="item in discountNumbers"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="bankAcceptanceDialogCancel">取消</el-button>
                <el-button size="small" type="primary" @click="bankAcceptanceDialogSubmit('bankAcceptanceForm')"
                           :disabled="isSubmitting">确定</el-button>
        </span>
    </el-dialog>

    <!-- 操作失败信息 -->
    <el-dialog title="操作失败信息" :visible.sync="operationFailureDialogVisible" width="500px">
        <div>
            <el-table
                    :data="respErrorList"
                    border
                    width="500px">
                <el-table-column
                        prop="payApplyId"
                        label="ID"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="businessNo"
                        label="编码"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="msg"
                        label="失败原因">
                </el-table-column>
            </el-table>
        </div>
    </el-dialog>
</div>
<script src="${pageContext.request.contextPath}/static/api/payApply/pay_apply_list.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                tableLoading: false,
                tabLoading: true,
                currentPage: 1,
                total: 0,
                activeName: 'first',
                multipleSelection: [],
                formInline: {
                    buyorderNo: '',
                    buyorderTraderName: '',
                    traderName: '',
                    traderMode: '',
                    traderSubject: '-1',
                    validStatus: '0',
                    isBill: '-1',
                    searchBeginAmount: '',
                    searchEndAmount: '',
                    searchBegintime: '',
                    searchEndtime: '',
                    searchPayBegintime: '',
                    searchPayEndtime: '',
                    comments: '-1',
                    accountType: '',
                    autoBill: '',
                    billMethod: '',
                    pageNo: 1,
                    pageSize: 10
                },
                payTime: [],
                applyTime: [],
                tableData: [],
                maxNum: 99,
                firstNum: 0,
                secondNum: 0,
                thirdNum: 0,
                fourthNum: 0,
                fifthNum: 0,
                tabMap: {
                    first: 521,
                    second: 522,
                    third: 520,
                    fourth: 528,
                    fifth: 10001
                },
                layerIndex: 0,
                currentRow: null,
                payApplyIds: [],
                bankAcceptanceDialogVisible: false,
                operationFailureDialogVisible: false,
                bankAcceptanceForm: {
                    bank: 1,
                    creditType: '',
                    offlineInvoice: 0,
                    discountNumber: '' // 减免单号字段
                },
                isSubmitting: false,
                formLoading: false,
                respErrorList: [],
                rules: {
                    creditType: [
                        {required: true, message: '授信类型不能为空', trigger: 'blur'}
                    ],
                    discountNumber: [
                        {
                            required: true,
                            message: '减免单号不能为空',
                            trigger: 'blur',
                            validator: (rule, value, callback) => {
                                if (this.bankAcceptanceForm.offlineInvoice === 0) {
                                    // 只有当“线下开票”选择“否”时，才需要校验减免单号
                                    if (!value) {
                                        callback(new Error('减免单号不能为空'));
                                    } else {
                                        callback();
                                    }
                                } else {
                                    // 如果“线下开票”选择“是”，则不需要校验
                                    callback();
                                }
                            }
                        }
                    ]
                },
                discountNumbers: [] // 减免单号数据
            }
        },

        created() {
            this.init();
            this.calAllTabCount();
            this.formInline.traderMode = 521;
        },
        methods: {
            init() {
                const date1 = new Date();
                date1.setMonth(date1.getMonth() - 1);
                date1.setHours(0, 0, 0, 0);
                const date2 = new Date();
                date2.setHours(23, 59, 59, 999);


                this.payTime = [date1, date2];
                this.applyTime = [date1, date2];
                this.formInline.traderMode = this.tabMap.first;
                this.queryList();
            },
            async calAllTabCount() {
                this.toSearchTime();

                const promises = [];
                for (const key in this.tabMap) {
                    const traderMode = this.tabMap[key];
                    this.formInline.traderMode = traderMode;
                    promises.push(
                        getPayApplyList(this.formInline)
                            .then(res => {
                                this.updateTabCount(res.data.data.payApply.payApplyTodoCount, traderMode);
                            })
                            .catch(e => {
                                console.error(e); // 处理错误
                            })
                    );

                }
                await Promise.all(promises);
                this.tabLoading = false;
            },
            updateTabCount(todoCount, traderMode) {
                console.log(traderMode, todoCount)
                switch (traderMode) {
                    case this.tabMap.first:
                        this.firstNum = todoCount;
                        break;
                    case this.tabMap.second:
                        this.secondNum = todoCount;
                        break;
                    case this.tabMap.third:
                        this.thirdNum = todoCount;
                        break;
                    case this.tabMap.fourth:
                        this.fourthNum = todoCount;
                        break;
                    case this.tabMap.fifth:
                        this.fifthNum = todoCount;
                        break;
                    default:
                        console.log('未知的 traderMode');
                }
            },
            queryList() {
                this.toSearchTime()
                this.tableLoading = true;
                const traderMode = this.formInline.traderMode;
                getPayApplyList(this.formInline).then(res => {
                    this.tableData = res.data.data.payApplyList;
                    this.tableLoading = false;
                    this.total = res.data.data.page.totalRecord;
                    this.currentPage = res.data.data.page.pageNo;

                    this.updateTabCount(res.data.data.payApply.payApplyTodoCount, traderMode);
                }).catch(error => {
                    this.tableLoading = false;
                    this.tabLoading = false;
                });
            },
            toSearchTime() {

                if (this.payTime != null && this.payTime.length > 0) {
                    this.formInline.searchPayBegintime = new Date(this.payTime[0]).getTime();
                    this.formInline.searchPayEndtime = new Date(this.payTime[1]).getTime();
                }
                if (this.applyTime != null && this.applyTime.length > 0) {
                    this.formInline.searchBegintime = new Date(this.applyTime[0]).getTime();
                    this.formInline.searchEndtime = new Date(this.applyTime[1]).getTime();
                }
            },
            getSelectedRows() {
                console.log("multipleSelection", this.multipleSelection);
                console.log("this.formInline.traderMode", this.formInline.traderMode);
                if (this.multipleSelection.length === 0) {
                    this.$message.error("请选择具体的数据");
                    return;
                }
                if (!this.formInline.traderMode) {
                    this.$message.error("请选择交易方式");
                    return;
                }
                var selectedIds = this.multipleSelection.map(row => row.payApplyId).join(",");
                console.log("selectedIds", selectedIds);
                if (this.formInline.traderMode === 10001) {
                    this.payApplyIds = this.multipleSelection.map(row => row.payApplyId);
                    this.bankAcceptanceDialogVisible = true;
                    console.log("this.payApplyIds", this.payApplyIds);
                } else {
                    var layerIndex = layer.open({
                        type: 2,
                        shadeClose: false,
                        area: ['30%', '30%'],
                        title: '选择付款银行',
                        content: '/order/buyorder/batchConfirmView.do?ids=' + selectedIds
                    });
                    this.layerIndex = layerIndex;
                }
            },
            batchConfirm() {
                var selectedIds = this.multipleSelection.map(row => row.payApplyId).join(",");
                console.log("selectedIds", selectedIds);
                let validComments = $("#commentParent").val();
                let payVedengBankId = $("#payVedengBankIdParent").val();
                var requestData = {
                    payVedengBankId: payVedengBankId,
                    idListStr: selectedIds,
                    validComments: validComments,
                }
                batchAgreeBuyOrderBankAcceptance(requestData)
                    .then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            return;
                        }
                        layer.close(this.layerIndex);
                        this.$message.success("提交成功");
                        this.queryList();
                        // // 设置延时关闭弹窗和刷新页面
                        // setTimeout(() => {
                        //     this.queryList()
                        // }, 2000);
                    })
                    .catch(err => {
                        layer.close(this.layerIndex);
                        this.$message.error("系统异常：" + err.message);
                    });

            },
            passBuyOrderBankAcceptance(row) {
                console.log("row", row);
                this.$confirm('确认通过吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'success',
                    beforeClose: (action, ctx, close) => {
                        if (action === 'cancel') {
                            close();
                            return
                        }
                        ctx.confirmButtonLoading = true;
                        var requestData = {
                            paymentType: 642,
                            taskId: row.taskInfoPayId,
                            payApplyId: row.payApplyId,
                        }
                        passBuyOrderBankAcceptance(requestData)
                            .then(res => {
                                if (res.data.code !== 0) {
                                    this.$message.error("数据异常：" + res.data.message);
                                    return;
                                }
                                this.$message.success("提交成功");
                                this.queryList();
                                
                                // 设置延时关闭弹窗和刷新页面
                                // setTimeout(() => {
                                //     this.queryList()
                                // }, 2000);
                            })
                            .catch(err => {
                                this.$message.error("系统异常：" + err.message);
                            }).finally(() => {
                            ctx.confirmButtonLoading = false;
                            close();
                        });
                    }
                }).then(() => {

                }).catch(() => {
                });
            },
            openBankAcceptanceDialog(row) {
                this.bankAcceptanceForm = {
                    bank: 1,
                    creditType: '',
                    offlineInvoice: 0,
                    discountNumber: '' // 重置减免单号字段
                };
                this.payApplyIds.push(row.payApplyId);
                this.bankAcceptanceDialogVisible = true;
                this.formLoading = true;

                // 调用获取减免单号的接口
                getDiscountNumber({amount: row.amount}).then(res => {
                    if (res.data.code === 0) {
                        this.discountNumbers = res.data.data.map(item => ({
                            label: item.taskNo,
                            value: item.taskNo
                        }));

                    } else {
                        this.$message.error("获取减免单号失败：" + res.data.message);
                    }
                }).catch(err => {
                    this.$message.error("系统异常：" + err.message);
                }).finally(() => {
                    // 关闭加载状态
                    this.formLoading = false;
                });
            },
            bankAcceptanceDialogCancel() {
                this.payApplyIds = [];
                this.bankAcceptanceDialogVisible = false;
                this.isSubmitting = false;
                this.formLoading = false;
                this.discountNumbers = [];
                this.$refs['bankAcceptanceForm'].resetFields()
            },

            bankAcceptanceDialogSubmit(bankAcceptanceForm) {
                console.log("this.payApplyIds", this.payApplyIds);


                this.$refs[bankAcceptanceForm].validate((valid) => {
                    if (valid) {
                        this.isSubmitting = true;
                        this.formLoading = true;
                        var requestData = {
                            payApplyIds: this.payApplyIds,
                            bank: this.bankAcceptanceForm.bank,
                            creditType: this.bankAcceptanceForm.creditType,
                            offline: this.bankAcceptanceForm.offlineInvoice,
                            taskNo: this.bankAcceptanceForm.discountNumber // 传入选择的减免单号
                        }
                        agreeBankAcceptanceSubmit(requestData)
                            .then(res => {
                                if (res.data.code !== 0) {
                                    this.$message.error("数据异常：" + res.data.message);
                                    this.bankAcceptanceDialogCancel();
                                    return;
                                }
                                if (res.data.data && res.data.data.length > 0) {
                                    this.bankAcceptanceDialogCancel();
                                    this.respErrorList = res.data.data;
                                    this.operationFailureDialogVisible = true;
                                } else {
                                    this.$message.success("提交成功");
                                    this.bankAcceptanceDialogCancel();
                                    this.queryList();
                                }
                            })
                            .catch(err => {
                                this.$message.error("系统异常：" + err.message);
                                this.bankAcceptanceDialogCancel();
                            });
                    }
                });
            },

            agreeBuyOrderBankAcceptance(row) {
                console.log("row", row);
                this.$confirm('确认同意吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'success'
                }).then(() => {
                    var requestData = {
                        payVedengBankId: 7,
                        taskId: row.taskInfoPayId,
                        pass: true,
                        type: 1,
                    }
                    agreeBuyOrderBankAcceptance(requestData)
                        .then(res => {
                            if (res.data.code !== 0) {
                                this.$message.error("数据异常：" + res.data.message);
                                return;
                            }
                            this.$message.success("提交成功");
                            this.queryList();
                        })
                        .catch(err => {
                            this.$message.error("系统异常：" + err.message);
                        });
                }).catch(() => {
                });
            },
            passBuyOrder(row) {
                var url = '/finance/buyorder/initPayApplyPass.do?id=' + row.payApplyId + '&taskId=' + row.taskInfoPayId + '&relatedId=' + row.relatedId + '&payType=' + row.payType + '&refreshParent=1';
                this.openLayer('新增交易记录', url, ['600px', '350px']);
            },
            rejectBuyOrder(row) {
                var url = '/order/buyorder/complement.do?taskId=' + row.taskInfoPayId + '&pass=false&type=1&refreshParent=1';
                this.openLayer('操作确认', url, ['500px', '180px']);
            },
            passAfterSale(row) {
                var url = '/finance/after/addFinanceAfterCapital.do?afterSalesId=' + row.relatedId + '&billType=2&payApplyId=' + row.payApplyId + '&taskId=' + row.taskInfoPayId + '&pageType=1&refreshParent=1';
                this.openLayer('新增交易记录', url, ['600px', '350px']);
            },
            rejectAfterSale(row) {
                var url = '/finance/after/complement.do?taskId=' + row.taskInfoPayId + '&pass=false&type=1&refreshParent=1';
                this.openLayer('操作确认', url, ['500px', '180px']);
            },
            agreeBuyOrder(row) {
                var url = '/order/buyorder/complement.do?taskId=' + row.taskInfoPayId + '&pass=true&type=1&pageType=T_PAY_APPLY&relatedId=' + row.relatedId + '&payType=' + row.payType + '&refreshParent=1';
                this.openLayer('操作确认', url, ['500px', '250px']);
            },
            disagreeBuyOrder(row) {
                var url = '/order/buyorder/complement.do?taskId=' + row.taskInfoPayId + '&pass=false&type=1&refreshParent=1';
                this.openLayer('操作确认', url, ['500px', '180px']);
            },
            agreeAfterSale(row) {
                var url = '/finance/after/complement.do?taskId=' + row.taskInfoPayId + '&pass=true&type=1&pageType=T_PAY_APPLY&refreshParent=1';
                this.openLayer('操作确认', url, ['500px', '180px']);
            },
            disagreeAfterSale(row) {
                var url = '/finance/after/complement.do?taskId=' + row.taskInfoPayId + '&pass=false&type=1&refreshParent=1';
                this.openLayer('操作确认', url, ['500px', '180px']);
            },
            openLayer(title, url, area) {
                layer.open({
                    type: 2,
                    shadeClose: false,
                    area: area,
                    title: title,
                    content: url,
                });
            },
            selectable(row) {
                return row.isBill === 0;
            },
            handleClick(row) {
                const tabMap = {
                    first: 521,
                    second: 522,
                    third: 520,
                    fourth: 528,
                    fifth: 10001
                };
                this.formInline.traderMode = tabMap[row.name];
                this.formInline.pageSize = 10;
                this.formInline.pageNo = 1;
                this.formInline.currentPage = 1;
                this.queryList();
            },
            toggleSelection(rows) {
                if (rows) {
                    rows.forEach(row => {
                        this.$refs.multipleTable.toggleRowSelection(row);
                    });
                } else {
                    this.$refs.multipleTable.clearSelection();
                }
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            onSubmit() {
                this.queryList();
            },
            handleSizeChange(val) {
                this.formInline.pageSize = val;
                this.queryList();
            },
            handleCurrentChange(val) {
                this.formInline.pageNo = val;
                this.currentPage = val;
                this.queryList();
            },
            formatDate(timestamp) {
                const date = new Date(timestamp);
                return date.toLocaleString(); // 根据需要格式化日期
            },
            viewDetail(row) {
                if (row.payType == 517) {
                    openTab("订单信息", '/finance/buyorder/viewBuyorder.do?buyorderId=' + row.relatedId);
                } else if (row.payType == 518) {
                    openTab("售后详情", '/finance/after/getFinanceAfterSaleDetail.do?afterSalesId=' + row.relatedId);
                } else if (row.payType == 4125) {
                    openTab("订单详情", '/buyorderExpense/details.do?buyorderExpenseId=' + row.relatedId);
                }
            },
            resetForm() {
                this.init();
                this.activeName = 'first';
                this.formInline = {
                    buyorderNo: '',
                    buyorderTraderName: '',
                    traderName: '',
                    traderMode: 521,
                    traderSubject: '-1',
                    validStatus: '-1',
                    isBill: '-1',
                    searchBeginAmount: '',
                    searchEndAmount: '',
                    searchBegintime: 0,
                    searchEndtime: 0,
                    searchPayBegintime: 0,
                    searchPayEndtime: 0,
                    comments: '-1',
                    accountType: '',
                    autoBill: '',
                    billMethod: ''
                };
            },

        }
    })

    window.vm = vm;
</script>
</script>

<style>
    .badge-item {
        margin-top: 10px;
        height: 60px;
    }

    .form-inline {
        margin: 30px;

    }

    .separator {
        margin: 0 5px;
    }

    .form-line {
        display: flex;
        flex-direction: row;

    }

    .el-form-item__label {
        font-size: 12px !important;
    }

    .el-table thead tr:first-child th {
        background-color: #f0f0f0; /* 设置背景色为灰色 */
        color: #333; /* 可选：设置文字颜色 */
    }

    .el-tabs__nav-wrap::after {
        width: 93%;
    }
</style>
