package com.vedeng.erp.trader.service;

import com.vedeng.authorization.model.User;
import com.vedeng.erp.trader.domain.dto.TraderSupplierFinanceDetail;
import com.vedeng.erp.trader.domain.entity.TraderSupplierFinance;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface TraderSupplierFinanceService {
    /**
     * 获取编辑页供应商信息
     * @param traderCustomerFinanceId
     * @return
     */
    TraderSupplierFinanceDetail getTraderSupplierFinancialById(Integer traderCustomerFinanceId);

    /**
     * 修改供应商
     * @param supplierFinance
     * @param user
     */
    void updateTraderSupplierFinance(TraderSupplierFinance supplierFinance, User user);

    /**
     * 批量导入供应商
     * @param file
     * @param user
     */
    void importTraderSupplierFinancial(MultipartFile file, User user) throws Exception;
	/**
	 * 同步供应商
	 * @param startDate
	 * @param endDate
	 */
	void syncTrader(Long startDate, Long endDate);

}
