package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.VisitDeleteDto;
import com.vedeng.erp.trader.dto.VisitSearchDto;
import com.vedeng.erp.trader.service.VisitRecordApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 拜访计划
 **/
@ExceptionController
@RestController
@RequestMapping("/visitrecord")
@Slf4j
public class VisitApi {

    @Autowired
    VisitRecordApiService visitRecordApiService;

    /**
     * 分页查询
     *
     * @param pageParam 筛选条件
     * @return 查询结果
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> page(@RequestBody PageParam<VisitSearchDto> pageParam) {
        return R.success(visitRecordApiService.searchVisitRecordListPage(pageParam));
    }


    @NoNeedAccessAuthorization
    @RequestMapping(value = "/queryVisitUserListByBelongUser")
    public R<?> queryShardUserListByBelongUser() {
        return R.success(visitRecordApiService.queryVisitUserListByBelongUser());
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/delete",method = RequestMethod.POST)
    public R<?> queryShardUserListByBelongUser(@RequestBody VisitDeleteDto dto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        dto.setUserId(currentUser.getId());
        return R.success(visitRecordApiService.deleteVisitRecord(dto));
    }

}
