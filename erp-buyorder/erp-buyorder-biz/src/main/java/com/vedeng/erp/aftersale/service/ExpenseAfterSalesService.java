package com.vedeng.erp.aftersale.service;

import com.common.dto.StepsNodeDto;
import com.vedeng.erp.aftersale.dto.*;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.dto.InvoiceReversalDto;
import io.swagger.models.auth.In;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.service
 * @Date 2022/10/20 10:45
 */
public interface ExpenseAfterSalesService {

    /**
     * 新增/编辑费用单页面信息查询
     *
     * @param expenseAfterSalesId 费用售后单id
     * @param buyorderExpenseId   费用单id
     * @return ExpenseAfterSalesDto
     */
    ExpenseAfterSalesDto getAddEditExpenseAfterSalesInfo(Long expenseAfterSalesId, Integer buyorderExpenseId);

    /**
     * 新增编辑页面 查询退货商品集合
     *
     * @param expenseAfterSalesId 费用售后单id
     * @param buyOrderExpenseId   费用单id
     * @return List<BuyorderExpenseItemDto>
     */
    List<BuyorderExpenseItemDto> getExpenseItemInfoForAfterSale(Long expenseAfterSalesId, Integer buyOrderExpenseId);

    /**
     * 新增编辑页面 查询仅退票商品集合
     *
     * @param expenseAfterSalesId 费用售后单id
     * @param buyorderExpenseId   费用单id
     * @return List<ExpenseItemAndInvoiceDto>
     */
    List<ExpenseItemAndInvoiceDto> getExpenseItemAndInvoiceForAfterSale(Long expenseAfterSalesId, Integer buyorderExpenseId);

    /**
     * 保存 新增费用售后订单
     *
     * @param expenseAfterSalesDto expenseAfterSalesDto
     * @return 售后单id
     */
    Long saveAddExpenseAfterSale(ExpenseAfterSalesDto expenseAfterSalesDto);

    /**
     * 保存 编辑费用售后订单
     *
     * @param expenseAfterSalesDto expenseAfterSalesDto
     * @return 售后单id
     */
    Long saveEditExpenseAfterSale(ExpenseAfterSalesDto expenseAfterSalesDto);

    /**
     * 详情页 查询采购费用售后单明细
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return ExpenseAfterSalesDto
     */
    ExpenseAfterSalesDto getExpenseAfterSalesDetail(Long expenseAfterSalesId);

    /**
     * 查询费用单所有的售后单
     *
     * @param buyorderExpenseId 费用单id
     * @return 只有主体的费用售后单状态不包含费用售后里的具体的商品
     */
    List<ExpenseAfterSalesDto> getExpenseAfterSalesBybuyorderExpenseId(Integer buyorderExpenseId);

    /**
     * 执行退款运算
     *
     * @param expenseAfterSalesDto
     * @return
     */
    Boolean executeRefundOperation(ExecuteRefundDto expenseAfterSalesDto);


    /**
     * 查询此费用单的
     *
     * @param buyorderExpenseId
     * @return
     */
    List<ExpenseAfterSalesItemDto> getExpenseAfterSalesReturnGoodsBybuyorderExpenseId(Integer buyorderExpenseId);

    /**
     * 获取费用完结的售后 中关联到指定销售单的数据
     * @param buyorderExpenseId
     * @param saleOrderId
     * @return BUYORDER_EXPENSE_ITEM_ID 费用单明细id num 数量
     */
    Map<Integer, Integer> getExpenseAfterSalesReturnGoodsBybuyorderExpenseIdAndSaleOrderId(Integer buyorderExpenseId, Integer saleOrderId);

    /**
     * 查询费用售后单的状态/审核信息 进度条
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return List<StepsNodeDto>
     */
    List<StepsNodeDto> getStepNodeList(Long expenseAfterSalesId);

    /**
     * 查询售后单的可退票记录
     *
     * @param expenseAfterSalesId expenseAfterSalesId
     * @return List<RefundInvoiceRecordDto>
     */
    List<RefundInvoiceRecordDto> getCanRefundInvoicesByRelatedId(Long expenseAfterSalesId);

    /**
     * 无需退票
     *
     * @param returnInvoiceWriteBackDto expenseAfterSalesId、invoiceNo、invoiceCode
     */
    void noNeedRefundInvoice(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto);

    /**
     * 申请冲销
     *
     * @param expenseAfterSalesId
     */
    InvoiceReversalDto reversalInvoices(Long expenseAfterSalesId, String invoiceCode, String invoiceNo);

    /**
     * 查询费用售后单关联的 发票信息
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return List<InvoiceDto>
     */
    List<InvoiceDto> getInvoiceListByAfterSalesId(Long expenseAfterSalesId);

    /**
     * 退票封装的对象
     *
     * @param query 查询条件
     * @return ReturnInvoiceDto 退票信息
     */
    ReturnInvoiceDto getReturnInvoiceGoodsData(ExpenseAfterSalesViewDto query);

    /**
     * 关闭费用售后单
     *
     * @param expenseAfterSalesId 费用售后单id
     * @param buyorderExpenseId   费用单id
     */
    void closeExpenseAfterSales(Long expenseAfterSalesId, Integer buyorderExpenseId);

    /**
     * 财务退票保存
     *
     * @param data 退票数据
     * @return 发票id
     */
    Integer recordRefundInvoice(ReturnInvoiceDto data);

    /**
     * 退票后更新记录
     *
     * @param returnInvoiceWriteBackDto 蓝票code 蓝票no 售后id 要回写的红票
     * @return 影响行数
     */
    Integer updateReturnInvoiceGoodsData(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto);

    /**
     * 计算获取 采购费用单模块数据
     *
     * @param expenseAfterSalesId 采购费用售后id
     * @return 退款信息
     */
    ExpenseAfterSalesPaymentDto getReturnPaymentData(Long expenseAfterSalesId);

    /**
     * @param expenseAfterSalesId 费用售后单id
     * 费用售后单交易记录
     *
     * @param expenseAfterSalesId 费用售后单id
     * @return 交易记录
     */
    List<CapitalBillDto> getExpenseAfterSalesCapitalBill(Long expenseAfterSalesId);

    /**
     * 新增费用售后单交易记录
     */
    void saveExpenseAfterSalesCapitalBill(CapitalBillDto capitalBill);

    /**
     * 获取流水号
     *
     * @param flowNo 流水号
     */
    List<BankBillDto> getBankBill(String flowNo);

}
