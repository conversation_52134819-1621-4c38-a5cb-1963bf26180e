<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="在途列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<div class="content">
    <div>
        <div class="layui-card-body">
            <div>
                <table class="table table-bordered table-striped table-condensed table-centered"
                       id="cus">
                    <thead>
                    <tr>
                        <th colspan="2">序号</th>
                        <th colspan="8">订单号</th>
                        <th colspan="8">生效时间</th>
                        <th colspan="2">数量</th>
                    </tr>
                    </thead>
                    <tbody class="employeestate">
                    <c:choose>
                        <c:when test="${not empty buyOrderList}">
                            <c:forEach var="buyOrder" items="${buyOrderList}" varStatus="num">
                                <tr>
                                    <td colspan="2">${num.count}</td>
                                    <td colspan="8">
                                        <a class="addtitle1 can-click" style="color:blue;"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"viewBhSaleorder","link":"/order/buyorder/viewBuyorder.do?buyorderId=${buyOrder.buyorderId}","title":"采购单信息"}'>
                                                ${buyOrder.buyorderNo}
                                        </a>
                                    </td>
                                    <td colspan="8"><date:date value ="${buyOrder.validTime} " format="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td colspan="2">${buyOrder.num}</td>
                                </tr>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <tr>
                                <td colspan="20">暂无在途数据！</td>
                            </tr>
                        </c:otherwise>
                    </c:choose>
                    </tbody>
                </table>
            </div>
            <hr>
        </div>
    </div>
</div>
<div class="submit-c tcenter">
    <button id="close-layer" type="button" class="bt-largest bt-bg-style bg-light-blue">确定</button>
</div>
<style>
    .submit-c {
        position: fixed;
        bottom: 10px;
        width: 100%;
    }

</style>
<%@ include file="../../common/footer.jsp" %>
