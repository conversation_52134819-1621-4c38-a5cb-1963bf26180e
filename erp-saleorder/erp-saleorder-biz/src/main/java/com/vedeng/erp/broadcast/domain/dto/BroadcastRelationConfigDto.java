package com.vedeng.erp.broadcast.domain.dto;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BroadcastRelationConfigDto {
    //初始化
    private List<BroadcastRelationDept> deptRelationList;
    private List<BroadcastRelationUser> userRelationList;

    private List<BroadcastRelationConfigReqDto> deptSaveList;
    private List<BroadcastRelationConfigReqDto> userSaveList;

    public static void main(String[] args) {
            // 测试构造函数
        BroadcastRelationConfigDto configDto = new BroadcastRelationConfigDto();

        BroadcastRelationConfigReqDto a= new BroadcastRelationConfigReqDto();
        a.setErpDeptId(1391);
        a.setBroadcastGroupId(82);
        BroadcastRelationConfigReqDto B= new BroadcastRelationConfigReqDto();
        B.setErpDeptId(1422);
        B.setBroadcastGroupId(82);

        configDto.setDeptSaveList(Lists.newArrayList(a,B));


        BroadcastRelationConfigReqDto U= new BroadcastRelationConfigReqDto();
        U.setErpDeptId(1158);
        U.setBroadcastGroupId(82);
        configDto.setUserSaveList(Lists.newArrayList(U));
        System.out.println(JSON.toJSONString(configDto));
    }
}
