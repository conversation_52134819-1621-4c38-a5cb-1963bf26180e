package com.vedeng.goods.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.core.domain.UserComponent;
import com.vedeng.goods.domain.dto.SceneSkuPoiDto;
import com.vedeng.goods.dto.SkuDto;
import com.vedeng.goods.dto.SkuSceneDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SkuSceneService {

    /**
     * 分页查询
     */
    PageInfo<SkuSceneDto> page(PageParam<SkuSceneDto> pageParam);

    /**
     * 根据id获取
     */
    SkuSceneDto getById(Long id);

    /**
     * 新增
     */
    void create(SkuSceneDto createDTO);

    /**
     * 更新
     */
    void update(SkuSceneDto updateDTO);

    /**
     * 更新状态
     */
    void updateStatus(Long id, Integer status);

    /**
     * 删除
     */
    void delete(Long id);

    /**
     * 排序
     */
    void sort(Long id, Integer sort);

    /**
     * 获取创建人
     */
    List<UserComponent> getCreateUser();

    /**
     * 获取修改人
     */
    List<UserComponent> getEditUser();

    /**
     * 获取产品信息
     */
    List<SkuDto> getProductInfo(List<String> skuNos);

    /**
     * 导出
     */
    List<SceneSkuPoiDto> exportExcel(Long id);

    /**
     * 校验名称是否重复
     */
    boolean check(String name,Long id);

    /**
     * 获取方案和分类
     */
    List<SkuSceneDto> getSceneAndCategory();
}
