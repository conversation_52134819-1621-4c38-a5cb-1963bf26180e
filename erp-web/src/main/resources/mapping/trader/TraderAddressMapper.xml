<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderAddressMapper" >
	<resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderAddress" >  
    <id column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="AREA_ID" property="areaId" jdbcType="INTEGER" />
    <result column="AREA_IDS" property="areaIds" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="BIT" />
    <result column="ZIP_CODE" property="zipCode" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_TOP" property="isTop" jdbcType="BIT" />
    <!-- 扩展字段 -->
    <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR" />
    <result column="MOBILE" property="mobile" jdbcType="VARCHAR" />
    <result column="USERNAME" property="userName" jdbcType="VARCHAR" />
    <result column="areaString" property="areaString" jdbcType="VARCHAR" />
  </resultMap>
	<sql id="Base_Column_List" >
    TRADER_ADDRESS_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, AREA_ID, AREA_IDS, ADDRESS,
    IS_DEFAULT, ZIP_CODE, COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER,IS_TOP
  </sql>
	<select id="getAddressInfoByParam" resultType="com.vedeng.trader.model.TraderAddress">
		SELECT
			a.TRADER_ADDRESS_ID,
			a.AREA_IDS
		FROM
			T_TRADER_ADDRESS a
		WHERE
			1 = 1
			<if test="null != isEnable">
				AND	a.IS_ENABLE = #{isEnable, jdbcType=INTEGER}
			</if>
			<if test="null != addressId">
				AND a.TRADER_ADDRESS_ID = #{addressId, jdbcType=INTEGER}
			</if>
		
	</select>
	<select id="getAddressInfoById" resultMap="BaseResultMap">
		SELECT
			*
		FROM
			T_TRADER_ADDRESS a
		WHERE
			1 = 1
			<if test="null != isEnable">
				AND	a.IS_ENABLE = #{isEnable, jdbcType=INTEGER}
			</if>
			<if test="null != traderAddressId">
				AND a.TRADER_ADDRESS_ID = #{traderAddressId, jdbcType=INTEGER}
			</if>
	</select>
	<!-- 查询客户/供应商的默认联系人联系信息 -->
	<select id="getTraderContactInfo" resultMap="BaseResultMap">
		SELECT TRADER_CONTACT_ID,TELEPHONE,MOBILE,NAME USERNAME FROM T_TRADER_CONTACT
		WHERE TRADER_ID =  #{traderId, jdbcType=INTEGER}
		AND TRADER_TYPE =  #{traderType, jdbcType=INTEGER}
		AND IS_DEFAULT = 1
		AND IS_ENABLE = 1
		LIMIT 1
	</select>
	<!-- 查询客户/供应商的默认联系人地址信息 -->
	<select id="getTraderContactLxInfo" resultMap="BaseResultMap">
		SELECT AREA_ID,ADDRESS FROM T_TRADER_ADDRESS
		WHERE TRADER_ID = #{traderId, jdbcType=INTEGER}
		AND TRADER_TYPE = #{traderType, jdbcType=INTEGER}
		AND IS_ENABLE = 1
		AND IS_DEFAULT = 1
		LIMIT 1 
	</select>

	<!--获取客户默认的地址信息-->
	<select id="getTraderDefaultAdressInfo"  parameterType="java.lang.Integer" resultType="com.vedeng.trader.model.TraderAddress">
		SELECT AREA_ID,ADDRESS FROM T_TRADER_ADDRESS
		WHERE TRADER_ID = #{traderId, jdbcType=INTEGER}
		AND TRADER_TYPE = 1 AND IS_ENABLE = 1 AND IS_DEFAULT = 1
		LIMIT 1
	</select>

<!--	<select id="getTraderAdressInfo"  parameterType="java.lang.Integer" resultType="com.smallhospital.dto.ElTraderAddressInfo">-->
<!--		SELECT-->
<!--		CONCAT(IFNULL(D.REGION_FULL_NAME, ""),"," ,IFNULL(C.REGION_FULL_NAME, ""),",",IFNULL(B.REGION_FULL_NAME, "")) as aera,-->
<!--		A.ADDRESS AS address,-->
<!--		A.IS_DEFAULT AS isDefault,-->
<!--		A.COMMENTS AS remark-->
<!--		FROM T_TRADER_ADDRESS A-->
<!--		LEFT JOIN T_REGION B ON A.AREA_ID = B.REGION_ID-->
<!--		LEFT JOIN T_REGION C ON B.PARENT_ID = C.REGION_ID-->
<!--		LEFT JOIN T_REGION D ON C.PARENT_ID = D.REGION_ID-->
<!--		WHERE TRADER_ID = #{traderId, jdbcType=INTEGER}-->
<!--		AND TRADER_TYPE = 1 AND IS_ENABLE = 1-->
<!--	</select>-->

	<select id="getAddressInfoByAddress" resultMap="BaseResultMap">
		SELECT *
		FROM T_TRADER_ADDRESS A
		WHERE     A.TRADER_ID = #{traderId,jdbcType=INTEGER}
			  AND A.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
			  AND A.AREA_IDS = #{areaIds,jdbcType=VARCHAR}
			  AND A.IS_ENABLE = 1
			  AND A.AREA_ID = #{areaId,jdbcType=INTEGER}
			  AND A.ADDRESS = #{address,jdbcType=VARCHAR}
		LIMIT 1
	</select>

	<select id="getAddressInfoByTraderIdAndAreaId" resultMap="BaseResultMap">
		SELECT *
		FROM T_TRADER_ADDRESS A
		WHERE
		      A.TRADER_ID = #{traderId,jdbcType=INTEGER}
		  AND A.TRADER_TYPE = 1
		  AND A.IS_ENABLE = 1
		  AND A.AREA_ID = #{areaId,jdbcType=INTEGER}
		  AND A.ADDRESS = #{address,jdbcType=VARCHAR}
		limit 1
	</select>
	<insert id="insertSelective" parameterType="com.vedeng.trader.model.TraderAddress" keyProperty="traderAddressId" useGeneratedKeys="true">
		insert into T_TRADER_ADDRESS
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="traderAddressId != null" >
				TRADER_ADDRESS_ID,
			</if>
			<if test="traderId != null" >
				TRADER_ID,
			</if>
			<if test="traderType != null" >
				TRADER_TYPE,
			</if>
			<if test="isEnable != null" >
				IS_ENABLE,
			</if>
			<if test="areaId != null" >
				AREA_ID,
			</if>
			<if test="areaIds != null" >
				AREA_IDS,
			</if>
			<if test="address != null" >
				ADDRESS,
			</if>
			<if test="isDefault != null" >
				IS_DEFAULT,
			</if>
			<if test="zipCode != null" >
				ZIP_CODE,
			</if>
			<if test="comments != null" >
				COMMENTS,
			</if>
			<if test="addTime != null" >
				ADD_TIME,
			</if>
			<if test="creator != null" >
				CREATOR,
			</if>
			<if test="modTime != null" >
				MOD_TIME,
			</if>
			<if test="updater != null" >
				UPDATER,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="traderAddressId != null" >
				#{traderAddressId,jdbcType=INTEGER},
			</if>
			<if test="traderId != null" >
				#{traderId,jdbcType=INTEGER},
			</if>
			<if test="traderType != null" >
				#{traderType,jdbcType=BIT},
			</if>
			<if test="isEnable != null" >
				#{isEnable,jdbcType=BIT},
			</if>
			<if test="areaId != null" >
				#{areaId,jdbcType=INTEGER},
			</if>
			<if test="areaIds != null" >
				#{areaIds,jdbcType=VARCHAR},
			</if>
			<if test="address != null" >
				#{address,jdbcType=VARCHAR},
			</if>
			<if test="isDefault != null" >
				#{isDefault,jdbcType=BIT},
			</if>
			<if test="zipCode != null" >
				#{zipCode,jdbcType=VARCHAR},
			</if>
			<if test="comments != null" >
				#{comments,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null" >
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null" >
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null" >
				#{updater,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>

	<select id="getTraderAddressList" resultType="com.vedeng.trader.model.TraderAddress" parameterType="java.lang.Integer">
		select
			<include refid="Base_Column_List" />
		from
			T_TRADER_ADDRESS
		where TRADER_TYPE = 1
		<if test="traderId != null" >
			and TRADER_ID = #{traderId,jdbcType=INTEGER}
		</if>
		ORDER BY IS_TOP DESC,IS_ENABLE DESC,MOD_TIME DESC
	</select>

	<update id="updateAddressStick">
		update T_TRADER_ADDRESS
		set IS_TOP = #{isTop,jdbcType=BIT}
		where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
	</update>

	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
		select
		<include refid="Base_Column_List" />
		from T_TRADER_ADDRESS
		where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
	</select>

	<select id="getTraderAddressListByModel" resultMap="BaseResultMap" parameterType="com.vedeng.trader.model.TraderAddress" >
		select
		<include refid="Base_Column_List" />
		from T_TRADER_ADDRESS
		where 1=1
		<if test="traderType != null" >
			and TRADER_TYPE = #{traderType,jdbcType=BIT}
		</if>
		<if test="traderId != null" >
			and TRADER_ID = #{traderId,jdbcType=INTEGER}
		</if>
		<if test="isEnable != null">
			and IS_ENABLE = #{isEnable,jdbcType=BIT}
		</if>
		<if test="areaId != null" >
			and AREA_ID = #{areaId,jdbcType=INTEGER}
		</if>
		<if test="areaIds != null" >
			and AREA_IDS = #{areaIds,jdbcType=VARCHAR}
		</if>
		<if test="address != null" >
			and ADDRESS = #{address,jdbcType=VARCHAR}
		</if>
	</select>
	<select id="getAllTraderAddressList" resultType="com.vedeng.trader.model.TraderAddress">
		select
		<include refid="Base_Column_List" />
		from T_TRADER_ADDRESS
		where 1=1
		<if test="traderType != null" >
			and TRADER_TYPE = #{traderType,jdbcType=BIT}
		</if>
		<if test="traderId != null" >
			and TRADER_ID = #{traderId,jdbcType=INTEGER}
		</if>
		<if test="isEnable != null">
			and IS_ENABLE = #{isEnable,jdbcType=BIT}
		</if>
		<if test="areaId != null" >
			and AREA_ID = #{areaId,jdbcType=INTEGER}
		</if>
		<if test="areaIds != null" >
			and AREA_IDS = #{areaIds,jdbcType=VARCHAR}
		</if>
		<if test="address != null" >
			and ADDRESS = #{address,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getTraderAddressInfoList" resultType="com.vedeng.trader.model.TraderAddress">
		select
		<include refid="Base_Column_List" />,
		 CONCAT(D.REGION_NAME,' ',C.REGION_NAME,' ',B.REGION_NAME) areaString
		from T_TRADER_ADDRESS  A LEFT JOIN T_REGION B ON A.AREA_ID=B.REGION_ID
		LEFT JOIN T_REGION C ON C.REGION_ID=B.PARENT_ID
		LEFT JOIN T_REGION D ON D.REGION_ID=C.PARENT_ID
		where 1=1
		and TRADER_ID = #{traderId,jdbcType=INTEGER}
		<if test="traderType != null" >
			and TRADER_TYPE = #{traderType,jdbcType=BIT}
		</if>
		<if test="isEnable != null">
			and IS_ENABLE = #{isEnable,jdbcType=BIT}
		</if>
		<if test="areaId != null" >
			and AREA_ID = #{areaId,jdbcType=INTEGER}
		</if>
		<if test="areaIds != null" >
			and AREA_IDS = #{areaIds,jdbcType=VARCHAR}
		</if>
		<if test="address != null" >
			and ADDRESS = #{address,jdbcType=VARCHAR}
		</if>
		<if test="areaQuery != null and areaQuery != '' " >
			and (ADDRESS like CONCAT('%',#{areaQuery},'%' )
				or D.REGION_NAME like CONCAT('%',#{areaQuery},'%' )
			or C.REGION_NAME like CONCAT('%',#{areaQuery},'%' )
			or B.REGION_NAME like CONCAT('%',#{areaQuery},'%' )
			)

		</if>
	</select>

	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.TraderAddress" >
	update T_TRADER_ADDRESS
	<set>
		<if test="traderId != null" >
			TRADER_ID = #{traderId,jdbcType=INTEGER},
		</if>
		<if test="traderType != null" >
			TRADER_TYPE = #{traderType,jdbcType=BIT},
		</if>
		<if test="isEnable != null" >
			IS_ENABLE = #{isEnable,jdbcType=BIT},
		</if>
		<if test="areaId != null" >
			AREA_ID = #{areaId,jdbcType=INTEGER},
		</if>
		<if test="areaIds != null" >
			AREA_IDS = #{areaIds,jdbcType=VARCHAR},
		</if>
		<if test="address != null" >
			ADDRESS = #{address,jdbcType=VARCHAR},
		</if>
		<if test="isDefault != null" >
			IS_DEFAULT = #{isDefault,jdbcType=BIT},
		</if>
		<if test="zipCode != null" >
			ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
		</if>
		<if test="comments != null" >
			COMMENTS = #{comments,jdbcType=VARCHAR},
		</if>
		<if test="addTime != null" >
			ADD_TIME = #{addTime,jdbcType=BIGINT},
		</if>
		<if test="creator != null" >
			CREATOR = #{creator,jdbcType=INTEGER},
		</if>
		<if test="modTime != null" >
			MOD_TIME = #{modTime,jdbcType=BIGINT},
		</if>
		<if test="updater != null" >
			UPDATER = #{updater,jdbcType=INTEGER},
		</if>
	</set>
	where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
	</update>
</mapper>