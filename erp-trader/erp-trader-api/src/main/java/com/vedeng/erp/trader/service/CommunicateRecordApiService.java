package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.CommunicateRecordApiDto;
import com.vedeng.erp.trader.dto.TagDto;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/4
 */
public interface CommunicateRecordApiService {
    /**
     * 获取沟通记录列表的Tag标签
     * @param communicateRecordId
     * @return
     */
    List<TagDto> getTag(Integer communicateRecordId);

    CommunicateRecordApiDto getCommunicateRecord(Integer communicateRecordId);

    CommunicateRecordApiDto getCommunicateRecordByCoid(String coid);

    void updateCommunicateRecord(CommunicateRecordApiDto communicateRecordApiDto);

}
