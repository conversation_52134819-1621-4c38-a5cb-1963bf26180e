package com.vedeng.goods.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.goods.command.SpuAddCommand;
import com.vedeng.goods.dao.BaseCategoryMapper;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.dto.MoveCategoryDto;
import com.vedeng.goods.model.vo.*;
import com.vedeng.goods.model.BaseAttribute;
import com.vedeng.goods.model.CategoryAttrValueMapping;
import com.vedeng.goods.model.vo.BaseAttributeValueVo;
import com.vedeng.goods.model.vo.BaseAttributeVo;
import com.vedeng.goods.model.vo.BaseCategoryVo;
import com.vedeng.goods.model.vo.CategoryAttrValueMappingVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description 分类
 * <AUTHOR>
 * @param
 * @date 2019/5/9
 */
public interface BaseCategoryService {


	/**
	 * @description 查询分类信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/24
	 */
	BaseCategoryVo getBaseCategoryByParam(Integer baseCategoryId);

	/**
	 * @description 保存分类信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/24
	 */
    ResultInfo saveBaseCategory(BaseCategoryVo baseCategoryVo, User operator);

	/**
	 * @description 获取商品分类列表
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/23
	 */
	List<BaseCategoryVo> getCategoryListPage(BaseCategoryVo baseCategoryVo, Page page);

	/**
	 * @description 获取属性信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/17
	 */
	List<BaseAttribute> getAttributeInfo(Integer baseCategoryId);

	/**
	 * @description 根据属性Id获取已应用的商品分类列表
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/22
	 */
	List<BaseCategoryVo> getBaseCategoryListPageByAttr(Integer attrId, Page page);

	/**
	 * @description 根据条件获取下级分类
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/22
	 */
	List<BaseCategoryVo> getCategoryListByIds(List<BaseCategoryVo> list,Integer level);

	/**
	 * @description 根据三级分类ID获取该三级分类的信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/22
	 */
	List<BaseCategoryVo> getthirdCategoryListById(BaseCategoryVo baseCategoryVo);

	/**
	 * @description 根据分类Id删除分类以及所有关联的分类及属性
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/22
	 */
	Integer deleteCategory(List<BaseCategoryVo> firstCategoryVoList, List<BaseCategoryVo> secondCategoryVoList,
		 List<BaseCategoryVo> thirdBasegoryVoList, User user);

	/**
	 * @description 查询三级分类下关联的属性列表
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/22
	 */
	List<CategoryAttrValueMappingVo> getCategoryAttrValueMappingVoList(List<BaseCategoryVo> list);

	/**
	 * @description 属性及属性值处理,将属性值的Id以@拼接后放入到对应的属性中
	 * @param list
	 * @param baseAttributeValueVoList
	 * <AUTHOR>
	 * @date 2019/5/24
	 * @return
	 */
	List<BaseAttributeVo> doAttrAndValueToString(List<BaseAttributeVo> list , List<BaseAttributeValueVo> baseAttributeValueVoList);

	/**
	 * @description 属性及属性值处理,处理成JSON格式
	 * @param list
	 * @param baseAttributeValueVoList
	 * <AUTHOR>
	 * @date 2019/5/24
	 * @return
	 */
	String doAttrAndValueToJson(List<BaseAttributeVo> list , List<BaseAttributeValueVo> baseAttributeValueVoList);

	/**
	 * @description 属性及属性值处理,处理成List
	 * @param baseCategoryVo
	 * <AUTHOR>
	 * @date 2019/5/24
	 * @return
	 */
	List<BaseAttributeVo> doAttrAndValueToList(BaseCategoryVo baseCategoryVo);
	/**
	 * @description 验证分类字段是否合法
	 * @param baseCategoryVo
	 * <AUTHOR>
	 * @date 2019/5/24
	 * @return
	 */
	ResultInfo checkCategoryField(BaseCategoryVo baseCategoryVo);

	/**
	 * @description 根据三级分类的ID获取一级分类>二级分类>三级分类
	 * @param thirdCategoryId
	 * <AUTHOR>
	 * @date 2019/5/24
	 * @return
	 */
	String getOrganizedCategoryNameById(Integer thirdCategoryId);

    /**
     * @description 查询三级分类下关联的属性以及属性值列表
     * <AUTHOR>
     * @param
     * @date 2019/5/22
     */
    List<CategoryAttrValueMapping> getCategoryAttrValueMappingVoList(Integer thirdCategoryId);

	/**
	 * @description 根据三级分类的ID获取一级分类>二级分类>三级分类
	 * @param keyWords
	 * <AUTHOR>
	 * @date 2019/5/24
	 * @return
	 */
	List<BaseCategoryVo> getCategoryListByKeyWords(String keyWords);
	List<BaseCategoryVo> getCategoryListByParentId(Integer parentId);

    List<BaseCategory> getFirstCategory();

    List<BaseCategory> getSecondCategory(Integer baseCategoryId);

	BaseCategory getBaseCategoryInfoById(Integer categoryId);

	void moveCatrgory(Integer baseCatrgoryId, Integer parentId);

	List<MoveCategoryDto> getMoveCategoryDtoByCategoryId(Integer thirdBaseCategory);

    void insertCategoryMigration(CategoryMigration categoryMigration);

	List<FirstAndSecondCategoryInfo> getFirstAndSecondCategoryListByKeyWords(String keyWords);

    List<CategoryMigration> getCtegoryMigretionlistPage(CategoryMigrationVo categoryMigrationVo, Page page);

    /**
     * <b>Description:</b>修改分类时通知op<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/8/6
     */
    int modifyCategory2Op(CategoryToOpVo categoryToOpVo);

    /**
     * <b>Description:</b>迁移分类时通知op<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/8/6
     */
    int migrateCategory2Op(CategoryToOpVo categoryToOpVo);

    /**
     * <b>Description:</b>根据三级分类id集合,获取一二三级分类信息<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/8/10
     */
    List<CategoryToOpVo> getAllLevelCategoryByIdList(List<Integer> categoryIds);
	/**
	 * <b>Description:</b>根据三级分类id获取一二三级分类名称<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/8/11
	 */
	String getAllLevelCategoryNameById(Integer categoryId);

	/**
	 * 根据三级分类编号和删除状态查询分类与医院的科室关联关系
	 *
	 * @param thirdLevelCategoryId
	 * @param deleted
	 * @return
	 * @since ERP_LV_2020_86
	 */
	List<CategoryDepartmentDo> listCategoryDepartmentDo(Integer thirdLevelCategoryId, Integer deleted);

	/**
	 * 获取医院科室列表信息
	 * <p>
	 * Note:兼容前台科室信息查询（ERP_LV_2020_86科室信息从spu移至三级商品分类下）
	 *
	 * @param thirdLevelCategoryId 三级商品分类id
	 * @return
	 * @since ERP_LV_2020_86
	 */
	List<CategoryDepartmentVo> listCategoryDepartmentVo(Integer thirdLevelCategoryId);

	/**
	 * 获取三级分类全路径
	 *
	 * @param thirdCategoryId
	 * @param delimiter if empty use {@code ,}
	 * @return
	 */
	String getThirdCategoryFullPath(Integer thirdCategoryId, String delimiter);

	/**
	 * 获取三级分类下审核通过的spu信息
	 *
	 * @param baseCategoryId
	 * @return
	 */
	List<SpuAddCommand> getValidSpuInfoByCategoryId(Integer baseCategoryId);

	String getDepartmentByBaseCategoryId(@Param("categoryId") Integer categoryId);
}