package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveEntityDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeNeedReceiveAdjustMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class AfterSaleToBalanceProcessor implements ItemProcessor<BatchCapitalBillDto, KingDeeNeedReceiveDto> {
    @Autowired
    KingDeeCustomerMapper kingDeeCustomerMapper;
    @Autowired
    KingDeeNeedReceiveAdjustMapper kingDeeNeedReceiveAdjustMapper;
    @Autowired
    KingDeeBaseApi kingDeeBaseApi;
    @Override
    public KingDeeNeedReceiveDto process(BatchCapitalBillDto dto) throws Exception {
        log.info("销售售后退款至客户余额，金蝶应收单调整单，参数:{}",JSON.toJSONString(dto));
        //判断售后类型是否为'销售退款',销售退款时忽略'退至客户'的流水
        Integer countNum =kingDeeNeedReceiveAdjustMapper.countMatchNum(dto.getAfterSaleNo());
        if (countNum>0){
            log.info("销售售后退款至客户余额，金蝶应收单调整单，售后类型为销售退款，忽略退至客户的流水，参数:{}",JSON.toJSONString(dto));
            return null;
        }
        // 根据erp客户id查询金蝶客户id
        KingDeeCustomerDto kingDeeCustomerDto = kingDeeCustomerMapper.queryInfoByCustomerId(dto.getTraderCustomerId());
        if (ObjectUtil.isEmpty(kingDeeCustomerDto)){
            log.error("销售售后退款至客户余额，金蝶应收单调整单，未查询到金蝶客户id，参数:{}",JSON.toJSONString(dto));
            return null;
        }
        // 金蝶应收单调整单 needReceiveDto
        KingDeeNeedReceiveDto needReceiveDto = new KingDeeNeedReceiveDto();
        needReceiveDto.setFid("0");
        needReceiveDto.setFBillNo(dto.getCapitalBillNo());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(needReceiveDto);
        if(old){
            log.info("销售售后退款至客户余额,数据已存在:{}", JSON.toJSONString(dto));
            return null;
        }
        needReceiveDto.setFVpfnDate(DateUtil.formatDate(DateUtil.date(dto.getTraderTime())));
        needReceiveDto.setFVpfnJg(KingDeeConstant.ORG_ID.toString());
        needReceiveDto.setFVpfnKh(Convert.toStr(kingDeeCustomerDto.getFNumber()));
        List<KingDeeNeedReceiveEntityDto> FEntity = new ArrayList<>();
        KingDeeNeedReceiveEntityDto needReceiveEntityDto = new KingDeeNeedReceiveEntityDto();
        needReceiveEntityDto.setFVpfnYsddh(dto.getOrderNo());
        needReceiveEntityDto.setFVpfnGsywdh(dto.getAfterSaleNo());
        //销售售后-应收单调整单 业务类型
        needReceiveEntityDto.setFVpfnYwlx("退款");
        // erp中销售售后退款至余额的金额为负数，对应金蝶中的应收单调整单金额也用负数
        needReceiveEntityDto.setFVpfnTzje(dto.getAmount().abs().negate());
        FEntity.add(needReceiveEntityDto);
        needReceiveDto.setFEntityList(FEntity);
        return needReceiveDto;
    }
}
