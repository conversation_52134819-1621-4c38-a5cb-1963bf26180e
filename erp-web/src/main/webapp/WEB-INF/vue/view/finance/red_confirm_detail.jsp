<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <el-form ref="invoiceRedConfirmationDto" :model="invoiceRedConfirmationDto" label-width="120px">
        <el-row>
            <el-col :span="12">
                <el-form-item label="申请原因：">
                    <el-select v-model="invoiceRedConfirmationDto.applyReason" placeholder="请选择" disabled="true">
                        <el-option label="开票有误" value="01"></el-option>
                        <el-option label="销货退回" value="02"></el-option>
                        <el-option label="服务中止" value="03"></el-option>
                        <el-option label="销售折让" value="04"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>

            <el-col :span="12">
                <el-form-item label="红冲范围：">
                    <el-radio-group v-model="invoiceRedConfirmationDto.redConfirmationScope">
                        <el-radio :label="0" disabled="true">全部红冲</el-radio>
                        <el-radio :label="1" disabled="true">部分红冲</el-radio>
                    </el-radio-group>

                    <template>
                        <el-popover
                                placement="top-start"
                                title=""
                                width="300"
                                trigger="hover">
                                <div class="custom-content">
                                    1. 蓝票未确认用途，不支持部分红冲<br>
                                    2. 申请原因为"开票有误"， 不支持部分红冲<br/>
                                </div>
                                <i class="el-icon-question" style="margin-left: 35px" slot="reference"></i>
                        </el-popover>
                    </template>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="12">
                <el-form-item label="销售单号：">
                    <span>{{invoiceRedConfirmationDto.businessOrderNo}}</span>
                </el-form-item>
            </el-col>

            <el-col :span="12">
                <el-form-item label="售后单号：">
                    <span>{{invoiceRedConfirmationDto.afterSaleBusinessOrderNo}}</span>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row style="margin-top: 2%">
            <el-col :span="12" style="padding-left: 35px">
                <span>红字明细列表</span>
            </el-col>

            <el-col :span="12" style="padding-left: 35px">
                <span>蓝票号码：{{this.invoiceRedConfirmationDto.blueInvoiceNo}}</span>
            </el-col>
        </el-row>

        <el-row style="padding-left: 35px; margin-top: 10px;height: 500px;overflow: auto;" v-if="this.invoiceRedConfirmationDto.redConfirmationScope == 0">
            <el-table
                    :data="invoiceRedConfirmationDto.invoiceRedConfirmationItemDtoList"
                    border
                    stripe
                    key="allRed"
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    style="width: 100%">
                <el-table-column
                        label="项目名称"
                        prop="projectName">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        min-width="20%"
                        prop="specifications">
                </el-table-column>
                <el-table-column
                        label="单位"
                        min-width="15%"
                        prop="unit">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        min-width="25%"
                        prop="taxCategoryNo">
                </el-table-column>
                <el-table-column
                        label="红字数量"
                        min-width="15%"
                        prop="quantity">
                    <template slot-scope="scope">
                        <span>{{Math.abs(scope.row.quantity)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="红字金额"
                        min-width="15%">
                    <template slot-scope="scope">
                        <span>{{Math.abs(scope.row.amount).toFixed(2)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="红字价税合计"
                        min-width="15%">
                    <template slot-scope="scope">
                        <span>{{Math.abs(scope.row.pricePlusTaxes).toFixed(2)}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-row>

        <el-row style="padding-left: 35px; margin-top: 10px; height: 500px;overflow: auto;" v-if="this.invoiceRedConfirmationDto.redConfirmationScope == 1">
            <el-table
                    :data="invoiceRedConfirmationDto.invoiceRedConfirmationItemDtoList"
                    border
                    stripe
                    key="partRed"
                    :header-cell-style="{'text-align':'center'}"
                    :cell-style="{'text-align':'center'}"
                    :row-key="row => row.invoiceRedConfirmationItemId"
                    :expand-row-keys="this.expandRowKeys"
                    style="width: 100%">
                <el-table-column type="expand">
                    <template slot-scope="props">
                        <el-table
                                :data="props.row.afterSalesGoodsDtoList"
                                border
                                stripe
                                :header-cell-style="{'text-align':'center', 'background-color': '#f0f9eb'}"
                                :cell-style="{'text-align':'center', 'background-color': '#f0f9eb'}"
                                style="width: 100%">
                            <el-table-column
                                    prop="skuName"
                                    label="产品名称">
                            </el-table-column>
                            <el-table-column
                                    label="规格型号"
                                    min-width="15%"
                                    prop="spec">
                            </el-table-column>
                            <el-table-column
                                    label="单位"
                                    min-width="15%"
                                    prop="unitName">
                            </el-table-column>
                            <el-table-column
                                    label="税收分类编码"
                                    min-width="25%"
                                    prop="taxCategoryNo">
                            </el-table-column>
                            <el-table-column
                                    label="售后数量"
                                    min-width="15%"
                                    prop="afterSaleNum">
                            </el-table-column>
                            <el-table-column
                                    label="售后金额"
                                    min-width="15%"
                                    prop="afterSalesAmount">
                            </el-table-column>
                            <el-table-column
                                    label="售后价税合计"
                                    min-width="15%"
                                    prop="afterSalesAmountAndTax">
                            </el-table-column>
                        </el-table>
                    </template>
                </el-table-column>
                <el-table-column
                        label="项目名称"
                        prop="projectName">
                </el-table-column>
                <el-table-column
                        label="规格型号"
                        min-width="15%"
                        prop="specifications">
                </el-table-column>
                <el-table-column
                        label="单位"
                        min-width="15%"
                        prop="unit">
                </el-table-column>
                <el-table-column
                        label="税收分类编码"
                        prop="taxCategoryNo"
                        min-width="25%">
                </el-table-column>
                <el-table-column
                        label="红字数量"
                        min-width="15%"
                        prop="quantity">
                    <template slot-scope="scope">
                        <span>{{Math.abs(scope.row.quantity)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="红字金额"
                        min-width="15%">
                    <template slot-scope="scope">
                        <span>{{Math.abs(scope.row.amount).toFixed(2)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="红字价税合计"
                        min-width="15%">
                    <template slot-scope="scope">
                        <span>{{Math.abs(scope.row.pricePlusTaxes).toFixed(2)}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-row>
    </el-form>

    <el-divider style="margin-top: 10%"></el-divider>
    <el-row>
        <el-col :span="6">
            <span style="font-weight: 600">红字合计：</span>
            <span>数量：{{Math.abs(this.totalNum)}}</span>
        </el-col>
        <el-col :span="6">
            <span>金额：{{Math.abs(this.totalAfterSalesAmount).toFixed(2)}}</span>
        </el-col>
        <el-col :span="6">
            <span>税额：{{Math.abs(this.totalTax).toFixed(2)}}</span>
        </el-col>
        <el-col :span="6">
            <span>价税合计：{{Math.abs(this.totalAfterSalesAmountAndTax).toFixed(2)}}</span>
        </el-col>
    </el-row>
    <el-divider style="margin-top: 10%"></el-divider>
    <el-row>
        <el-col :span="12" style="padding-left: 2%; ">
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 0">初始化</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 1">已申请</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 2">已确认</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 3">已开票</el-button>
            <el-button type="warning" plain round v-if="this.invoiceRedConfirmationDto.redConfirmationStatus == 4">已作废</el-button>
        </el-col>
        <el-col :span="12" style="text-align: right; padding-right: 5%">
            <template v-for="item in this.invoiceRedConfirmationDto.buttonList">
                <template v-if="item.show">
                    <template v-if="!item.needConfirm">
                        <vxe-button  @click="doButton(item)" size="small" status="primary" >{{item.buttonName}}</vxe-button>
                    </template>
                    <template v-if="item.needConfirm">
                        <vxe-button  @click="confirmDoButton(item)" size="small" status="primary">{{item.buttonName}}</vxe-button>
                    </template>
                </template>
            </template>
            <el-button size="small" @click="close()">取消</el-button>
        </el-col>
    </el-row>
</div>
<script src="${pageContext.request.contextPath}/static/api/invoiceredconfirmation/red_confirm_apply.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const invoiceRedConfirmationId = ${invoiceRedConfirmationId};
    new Vue({
        el: '#app',
        data() {
            return {
                invoiceRedConfirmationDto: {},
                expandRowKeys: [],
                // 合计数据
                totalNum: 0,
                totalAfterSalesAmount: 0.00,
                totalTax: 0.00,
                totalAfterSalesAmountAndTax: 0.00
            }
        },

        mounted() {
            loadingApp();
            this.initData()
        },

        methods: {
            // 页面初始化数据加载
            initData() {
                getInvoiceRedConfirmationById({"invoiceRedConfirmationId" : invoiceRedConfirmationId}).then(res => {
                    this.invoiceRedConfirmationDto = res.data.data;
                    this.invoiceRedConfirmationDto.invoiceRedConfirmationItemDtoList.forEach(item => {
                        // 默认全部展开
                        this.expandRowKeys.push(item.invoiceRedConfirmationItemId);
                        this.totalNum = this.totalNum + item.quantity;
                        this.totalAfterSalesAmount = this.totalAfterSalesAmount + item.amount;
                        this.totalTax = this.totalTax + item.uaxAmount;
                        this.totalAfterSalesAmountAndTax = this.totalAfterSalesAmountAndTax + item.pricePlusTaxes;
                    });
                })
            },

            doButton(button) {
                let pageParams=[];
                pageParams.push(invoiceRedConfirmationId);
                if (button.viewType == 1) {
                    openModelParam( button.url, button.buttonName, "70%", "100%");
                }
                if (button.viewType == 0) {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    axios({
                        url: button.url,
                        method: 'post',
                        data: {invoiceRedConfirmationIds:pageParams}
                    }).then(res => {
                        loading.close();
                    if (res.data.data != undefined && res.data.data.length > 0) {
                        VXETable.modal.alert({ content:res.data.data[0].failReason, title:'操作失败信息', status: 'error' });
                    } else {
                        VXETable.modal.message({content: '操作成功', status: 'success'});
                        parent.layer.closeAll();
                        parent.window.location.reload();
                    }

                });
                }
            },

            async confirmDoButton(button) {
            const type = await VXETable.modal.confirm('确定'+button.buttonName+'吗？')
            if (type == 'confirm') {

                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                let pageParams=[];
                pageParams.push(invoiceRedConfirmationId);
                let params = {invoiceRedConfirmationIds:pageParams};
                if (button.button == 1) {
                    params = {invoiceRedConfirmationIds:pageParams,status:'Y'};
                }
                if (button.button == 2) {
                    params = {invoiceRedConfirmationIds:pageParams,status:'N'};
                }

                axios({
                    url: button.url,
                    method: 'post',
                    data: params
                }).then(res => {
                    loading.close();
                if (res.data.data != undefined && res.data.data.length > 0) {
                    VXETable.modal.alert({content: res.data.data[0].failReason, title: '操作失败信息', status: 'error'});
                } else {
                    VXETable.modal.message({content: '操作成功', status: 'success'});
                    parent.layer.closeAll();
                    parent.window.location.reload();
                }

            });
            }
        },

            // 取消按钮
            close() {
                parent.layer.closeAll();
            }
        }
    })
</script>

<style>
</style>