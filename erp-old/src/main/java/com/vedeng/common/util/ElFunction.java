package com.vedeng.common.util;

import java.math.BigDecimal;

public class ElFunction {

    public static String rounding(Object str){
        return String.format("%.2f",str);
    }

    public static String toString(BigDecimal price){
        return price.toPlainString();
    }


    public static String object2Json(Object o){
        if(o==null){
            return null;
        }
       return JsonUtils.convertObjectToJsonStr(o);
    }
}
