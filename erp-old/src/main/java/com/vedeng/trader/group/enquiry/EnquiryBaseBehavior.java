package com.vedeng.trader.group.enquiry;

import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.group.BaseBehavior;
import com.vedeng.trader.model.Period;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.BitSet;
import java.util.List;

public abstract class EnquiryBaseBehavior extends BaseBehavior {

    // 运行时注入Mapper
    private BussinessChanceMapper bussinessChanceMapper=(BussinessChanceMapper) context.getBean(BussinessChanceMapper.class);
    public BitSet getRencentEnquiryData(){
        BitSet result=new BitSet();
       Period period = DateUtil.getStartAndEndPointForDays(getPeriod());
        List<Integer> traderIds=bussinessChanceMapper.getTraderIdsByPeriod(period);
        if(CollectionUtils.isNotEmpty(traderIds)){
            for(Integer id:traderIds){
                if(id==null){
                    continue;
                }
                result.set(id);
            }
        }
        return result;
    }
}
