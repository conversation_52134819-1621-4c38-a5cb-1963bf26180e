package com.wms.service.other;

import com.wms.constant.WMSContant;
import com.wms.dto.WmsInvoiceDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * ERP下传是否开发票接口
 */
@Service
@Slf4j
public class PutInvoiceDataInterface extends AbstractWmsInterface {

    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.PUT_INVOICE_DATA);
        wmsRequest.setMessageId(WMSContant.PUT_INVOICE_DATA);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {
        if (param == null || param.length == 0) {
            return null;
        }
        JSONArray headArray = new JSONArray();
        Arrays.stream(param).forEach(paramItem -> {
            WmsInvoiceDto wmsInvoiceDto = (WmsInvoiceDto) paramItem;
            JSONObject headItem = parseTraderToJsonObject(wmsInvoiceDto);
            headArray.add(headItem);
        });

        JSONObject headObject = new JSONObject();
        headObject.put("header", headArray);
        return headObject;
    }

    private JSONObject parseTraderToJsonObject(WmsInvoiceDto wmsInvoiceDto) {
        JSONObject headItem = new JSONObject();
        try {
            headItem.put("CustomerID", "VEDENG");
            headItem.put("WarehouseID", "NJ01");
            //ERP单据编号
            headItem.put("SOReference1", wmsInvoiceDto.getSOReference1());
            //WMS单据编号
            headItem.put("OrderNo", wmsInvoiceDto.getOrderNo());
            //开票结果
            headItem.put("Invoice_Flag", wmsInvoiceDto.getInvoice_Flag());
            //开票申请ID
            headItem.put("UserDefine1", wmsInvoiceDto.getUserDefine1());
            //开票失败原因
            headItem.put("UserDefine2", wmsInvoiceDto.getUserDefine2());
        } catch (Exception e) {
            log.error("【parseTraderToJsonObject】处理异常",e);
        }
        return headItem;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
