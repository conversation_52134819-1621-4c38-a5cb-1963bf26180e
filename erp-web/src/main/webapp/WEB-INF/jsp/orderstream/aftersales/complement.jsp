<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认审核" scope="application" />
<%@ include file="../../common/common.jsp"%>
 <div class="formpublic">
            <form method="post" action="" id="complement">
                <ul>
					<c:if test="${virtualAudit==true && pass==true}">
					<li>
						<div class="infor_name">
							<lable for='name'>虚拟商品是否可退</lable>
						</div>
						<div class="f_left">
							<input type="radio" name="isReturn" value="1" checked="checked"/><span>可退 </span>
							<input type="radio" name="isReturn" value="0" /><span>不可退</span>
						</div>
					</li>
					<li>
						<div class="text-center" style="color: #00a0e9">
							<span id="audit_tips">系统将自动创建采购费用退货订单</span>
						</div>
					</li>
					<li id="audit_warn"></li>
					</c:if>
                   <li>
					<div class="infor_name">
						<c:if test="${pass==false}">
						<span>*</span>
						</c:if>
						<lable for='name'>备注</lable>
					</div>
					<div class="f_left">
						<input type="text" name="comment" id="comment" class="input-larger" value="" />
					</div>
				</li>
                </ul>
                <div class="add-tijiao tcenter">
                	<input type="hidden" value="${taskId}" name="taskId">
                	<input type="hidden" value="${saleorderId}" name="saleorderId">
                	<input type="hidden" value="${pass}" name="pass">
					<input type="hidden" value="${virtualAudit}" name="virtualAudit">
                	<input type="hidden" value="${type}" name="type">
                	<input type="hidden" value="${sku}" name="sku">
                	<input type="hidden" value="${orderId}" name="orderId">
					<input type="hidden" value="${afterSalesId}" name="afterSalesId">
					<input type="hidden" value="${isLightning}" name="isLightning">
					<input type="hidden" value="${afterSaleorderId}" name="afterSaleorderId">
					<input type="hidden" value="${lightningAfterSalesId}" name="lightningAfterSalesId">
                	<input type="hidden" name="formToken" value="${formToken}"/>
                    <button type="button" class="bg-light-green" onclick="complementTask()">提交</button>
                    <button class="dele" type="button" id="close-layer">取消</button>
                </div>
           </form>
</div>
<script type="text/javascript">
	$(function(){
		$('input[name="isReturn"]').click(function(){
			if ($(this).val()=='1'){
				$("#audit_tips").html('系统将自动创建采购费用退货订单');
			}else{
				$("#audit_tips").html('系统将一键转单，无需手动操作');
			}
		});
	});
</script>
<script type="text/javascript" src='<%= basePath %>static/js/orderstream/aftersales/complete.js?rnd=${resourceVersionKey}'></script>
<%@ include file="../../common/footer.jsp"%>