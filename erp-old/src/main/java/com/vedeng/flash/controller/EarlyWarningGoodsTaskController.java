package com.vedeng.flash.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.dto.EarlyTaskGoodsQueryDto;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.flash.service.warningtask.ExpeditingTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpSession;

@Controller
@RequestMapping("flash/earlyWarningGoods")
public class EarlyWarningGoodsTaskController {

    @Autowired
    @Qualifier("expeditingTaskService")
    ExpeditingTaskService expeditingTaskService;


    /**
     * 跟进记录详情
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/22 12:55.
     * @author: Randy.Xu.
     * @param earlyTaskGoodsQueryDto
     * @return: org.springframework.web.servlet.ModelAndView.
     * @throws:  .
     */
        @RequestMapping("followInfoDetail")
    public ModelAndView getFollowInfoDetail(EarlyTaskGoodsQueryDto earlyTaskGoodsQueryDto){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("flash/expeditinggoods/followUpDetail");
        EarlyWarningTaskDto earlyWarningTaskDto = expeditingTaskService.getFollowInfoDetailById(earlyTaskGoodsQueryDto);
        mav.addObject("task",earlyWarningTaskDto);
        return mav;
    }

    /**
     * 更改跟进记录详情
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/22 12:56.
     * @author: Randy.Xu.
     * @param earlyWarningTask
     * @param session
     * @return: com.vedeng.common.model.ResultInfo.
     * @throws:  .
     */
    @RequestMapping("saveFollowInfoDetail")
    @ResponseBody
    public ResultInfo saveFollowInfoDetail(EarlyWarningTask earlyWarningTask, HttpSession session){
        User user = (User)session.getAttribute(ErpConst.CURR_USER);
        earlyWarningTask.setFollowUpPerson(user.getUsername());
        String timeStr = DateUtil.convertString(DateUtil.gainNowDate(), DateUtil.TIME_FORMAT);
        earlyWarningTask.setFollowUpTime(timeStr);
        EarlyWarningTask historyEarlyWarningTask = expeditingTaskService.getFollowInfoDetail(earlyWarningTask);
        earlyWarningTask.setFollowUpNum(historyEarlyWarningTask.getFollowUpNum()+1);
        earlyWarningTask.setRelateBusinessId(historyEarlyWarningTask.getRelateBusinessId());
        ResultInfo resultInfo = expeditingTaskService.updateFollowInfoById(earlyWarningTask);
        return resultInfo;
    }
}
