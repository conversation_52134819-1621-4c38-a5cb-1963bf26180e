package com.newtask.category;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.goods.dao.BaseCategoryMapper;
import com.vedeng.goods.model.dto.CategorySaleInfoDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分类数量信息计算
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "categorySalesCalculationTask")
public class CategorySalesCalculationTask extends AbstractJobHandler {

    @Resource
    private BaseCategoryMapper baseCategoryMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("CategorySalesCalculationTask -----------" + s);

        List<CategorySaleInfoDto> recent6MonthCategorySaleInfo = baseCategoryMapper.getRecent6MonthCategorySaleInfo();
        if (CollectionUtils.isEmpty(recent6MonthCategorySaleInfo)) {
            return SUCCESS;
        }

        baseCategoryMapper.batchUpdateRecent6MonthCategorySaleNum(recent6MonthCategorySaleInfo.stream().filter(item -> item.getMyCount() != null).collect(Collectors.toList()));

        return SUCCESS;
    }
}
