<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.logistics.dao.LogisticsMapper" >

	<select id="getLogisticsListByParam" resultType="com.vedeng.logistics.model.Logistics">
		SELECT
		t.*
		FROM
		T_LOGISTICS t
		WHERE
		1 = 1

		<if test="null != companyId">
			AND t.COMPANY_ID = #{companyId, jdbcType=INTEGER}
		</if>

		<if test="null != IS_ENABLE">
			AND t.IS_ENABLE = #{IS_ENABLE, jdbcType=INTEGER}
		</if>
		<!-- 中通和顺丰 -->
		<if test="null != ZT_LOGISTICS or null != SF_LOGISTICS">
			AND (t.LOGISTICS_ID = #{ZT_LOGISTICS, jdbcType=INTEGER} OR t.LOGISTICS_ID = #{SF_LOGISTICS, jdbcType=INTEGER})
		</if>

		ORDER BY
		t.IS_DEFAULT DESC,
		t.SORT DESC
	</select>

	<select id="getLogisticsListByParamForDeliery" resultType="com.vedeng.logistics.model.Logistics">
		SELECT
		t.*
		FROM
		T_LOGISTICS t
		WHERE
		1 = 1

		<if test="null != companyId">
			AND t.COMPANY_ID = #{companyId, jdbcType=INTEGER}
		</if>

		<if test="null != IS_ENABLE">
			AND t.IS_ENABLE = #{IS_ENABLE, jdbcType=INTEGER}
		</if>
		<!-- 中通和顺丰 -->
		AND  t.LOGISTICS_ID IN
		<foreach collection="logisticsIdList" item="relatedId" index="index"
				 open="(" close=")" separator=",">
			#{relatedId}
		</foreach>

		ORDER BY
		t.IS_DEFAULT DESC,
		t.SORT DESC
	</select>



	<select id="getFreeByParam" resultType="java.math.BigDecimal">
  	SELECT
		COALESCE(a.FREIGHT, 0)
	FROM
		T_LOGISTICS_FREIGHT a
	WHERE
		a.REGION_ID = #{regionId, jdbcType=INTEGER}
	AND a.LOGISTICS_ID = #{logisticsId, jdbcType=INTEGER}
  </select>


	<select id="getLogisticsById" resultType="com.vedeng.logistics.model.Logistics">
		SELECT
		t.*
		FROM
		T_LOGISTICS t
		WHERE
		1 = 1
		<if test="null != logisticsId">
			AND t.LOGISTICS_ID = #{logisticsId, jdbcType=INTEGER}
		</if>
		LIMIT 1
	</select>

	<!--根据快递名称查询物流编码-->
	<select id="getLogisticsCode" resultType="java.lang.String">
	SELECT
	c.CODE
	FROM
	T_LOGISTICS t
	LEFT JOIN T_LOGISTICS_CODE c
	ON t.Name = c.Name
	WHERE t.Name=#{name,jdbcType=VARCHAR}
	and t.IS_ENABLE=1
	</select>

	<select id="getLogisticsIdByName" resultType="java.lang.Integer">
		SELECT
			LOGISTICS_ID
		FROM
			`T_LOGISTICS`
		WHERE
			NAME = #{name,jdbcType=VARCHAR}
			AND IS_ENABLE = 1
			AND COMPANY_ID = 1
			LIMIT 1
	</select>

	<select id="getLogisticsByCarrId" resultType="com.vedeng.logistics.model.Logistics">
		SELECT
			*
		FROM
			T_LOGISTICS
		WHERE
			CARRIERID = #{carrierId,jdbcType=VARCHAR}
			AND IS_ENABLE = 1
			AND COMPANY_ID = 1
			LIMIT 1
	</select>

    <select id="getLogisticsCodeByLogisticsId" resultType="java.lang.String">
		SELECT
			T2.`CODE`
		FROM
			T_LOGISTICS T1
				LEFT JOIN T_LOGISTICS_CODE T2 ON T1.NAME = T2.NAME
		WHERE
			T1.IS_ENABLE = 1
		  AND T1.LOGISTICS_ID = #{logisticsId, jdbcType=INTEGER}
		GROUP BY
			T1.LOGISTICS_ID
	</select>

	<select id="getWmsLogisticsList" resultType="com.vedeng.logistics.model.Logistics">
		SELECT
			*
		FROM
			T_LOGISTICS
		WHERE
			COMPANY_ID = #{companyId,jdbcType=INTEGER}
		  AND IS_ENABLE = 1
			AND IS_WMS_LOGISTICS = #{type,jdbcType=INTEGER}

	</select>
	<select id="getExpressIdByCodeAndNo" resultType="java.lang.Integer">
		SELECT distinct T.EXPRESS_ID
		FROM T_EXPRESS T
				 LEFT JOIN T_LOGISTICS L ON T.LOGISTICS_ID = L.LOGISTICS_ID
				 LEFT JOIN T_LOGISTICS_CODE C ON C.NAME = L.NAME
		WHERE C.CODE = #{code,jdbcType=VARCHAR}
		AND T.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
	</select>

	<select id="getExistLogisticsById" resultType="com.vedeng.logistics.model.Logistics">
		SELECT t.*
		FROM T_LOGISTICS t
				 LEFT join T_LOGISTICS_CODE tlc on t.Name = tlc.Name
		WHERE 1 = 1
		  AND tlc.NAME IS NOT NULL
		  AND t.IS_ENABLE = 1
		<if test="logisticsId != null">
			AND t.LOGISTICS_ID = #{logisticsId, jdbcType=INTEGER}
		</if>
		LIMIT 1
	</select>
</mapper>

