package com.vedeng.trader.model;

import java.math.BigDecimal;

/**
 * <b>Description:</b><br> 交易者数据信息（账期）
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> dbcenter
 * <br><b>PackageName:</b> com.vedeng.model.common
 * <br><b>ClassName:</b> TraderData
 * <br><b>Date:</b> 2017年9月5日 下午1:28:17
 */
public class TraderPeriodData {

    private BigDecimal periodAmount;//账期额度

	private Integer periodDay;//账期天数
    
	private BigDecimal periodAmountOccupy;//账期占用额度
	
	private BigDecimal periodAmountUsed;//账期使用额度
	
	private BigDecimal periodAmountOverdue;//账期逾期额度 （帐期开始时间+帐期天数）、帐期开始时间按生效订单开始发货的时间计算
	
    private Integer periodAmountUsedTimes;//账期使用次数
    
    private Integer periodAmountOverdueTimes;//账期逾期次数
  

	public BigDecimal getPeriodAmount() {
		return periodAmount;
	}

	public void setPeriodAmount(BigDecimal periodAmount) {
		this.periodAmount = periodAmount;
	}

	public Integer getPeriodDay() {
		return periodDay;
	}

	public void setPeriodDay(Integer periodDay) {
		this.periodDay = periodDay;
	}

	public BigDecimal getPeriodAmountUsed() {
		return periodAmountUsed;
	}

	public void setPeriodAmountUsed(BigDecimal periodAmountUsed) {
		this.periodAmountUsed = periodAmountUsed;
	}

	public BigDecimal getPeriodAmountOverdue() {
		return periodAmountOverdue;
	}

	public void setPeriodAmountOverdue(BigDecimal periodAmountOverdue) {
		this.periodAmountOverdue = periodAmountOverdue;
	}

	public Integer getPeriodAmountUsedTimes() {
		return periodAmountUsedTimes;
	}

	public void setPeriodAmountUsedTimes(Integer periodAmountUsedTimes) {
		this.periodAmountUsedTimes = periodAmountUsedTimes;
	}

	public Integer getPeriodAmountOverdueTimes() {
		return periodAmountOverdueTimes;
	}

	public void setPeriodAmountOverdueTimes(Integer periodAmountOverdueTimes) {
		this.periodAmountOverdueTimes = periodAmountOverdueTimes;
	}

	public BigDecimal getPeriodAmountOccupy() {
		return periodAmountOccupy;
	}

	public void setPeriodAmountOccupy(BigDecimal periodAmountOccupy) {
		this.periodAmountOccupy = periodAmountOccupy;
	}

}
