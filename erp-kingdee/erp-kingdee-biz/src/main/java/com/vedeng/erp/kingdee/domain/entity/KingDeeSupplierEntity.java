package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * 金蝶供应商
 * <AUTHOR>
 */
@Getter
@Setter
@Table(name = "KING_DEE_SUPPLIER")
public class KingDeeSupplierEntity extends BaseEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 供应商编号(使用erp供应商id)
     */
    private Integer fNumber;

    /**
     * 供应商内码 0：表示新增 非0：云星空系统单据ID值
     */
    private String fSupplierId;

    /**
     * 创建组织id
     */
    private Integer fCreateOrgId;

    /**
     * 使用组织id
     */
    private Integer fUseOrgId;

    /**
     * 供应商名称
     */
    private String fName;

    /**
     * 供应类别编码
     */
    private String fBaseInfo;

    /**
     * 供应商类别 01 生产厂家  02 分销商
     */
    private String fsupplierclassify;

    /**
     * 银行信息
     */
    private String fbankinfo;
}