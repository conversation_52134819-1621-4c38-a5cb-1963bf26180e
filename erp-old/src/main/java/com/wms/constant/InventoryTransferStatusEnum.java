package com.wms.constant;

/**
 * <AUTHOR>
 * @Description 库存转移单状态
 * @createTime 2020年06月28日 11:37:00
 */
public enum InventoryTransferStatusEnum {
    ON_GOING(1, "进行中"),
    FINISHED(2, "已完结");

    InventoryTransferStatusEnum(Integer status, String statusStr) {
        this.status = status;
        this.statusStr = statusStr;
    }

    private Integer status;

    private String statusStr;

    public static String getStatusStr(Integer status) {
        String statusStr = "";

        for (InventoryTransferStatusEnum enumItem : InventoryTransferStatusEnum.values()) {
            if (enumItem.getStatus().equals(status)) {
                statusStr = enumItem.getStatusStr();
                break;
            }
        }

        return statusStr;
    }

    public Integer getStatus() {
        return status;
    }

    public String getStatusStr() {
        return statusStr;
    }}
