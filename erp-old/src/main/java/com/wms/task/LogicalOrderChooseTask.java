package com.wms.task;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderVo;
import com.wms.service.LogicalAfterorderChooseService;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.listenner.PutAfterReturnAuditFinishLister;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName LogicalOrderChooseTask.java
 * @Description TODO 订单选择逻辑仓任务
 * @createTime 2020年07月29日 15:31:00
 */
@Slf4j
@Component
@JobHandler(value="LogicalOrderChooseTask")
public class LogicalOrderChooseTask extends AbstractJobHandler {
    
    @Autowired
    private LogicalAfterorderChooseService logicalAfterorderChooseService;
    
    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Autowired
    private PutAfterReturnAuditFinishLister putAfterReturnAuditFinishLister;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        logicalorderChoose(param);
        return SUCCESS;
    }

    private void logicalorderChoose(String param) throws Exception {
        if(param != null){
            String[] split = param.split(":");
            String type = split[0];
            if("sale".equals(type)){
                String[] orderNos = split[1].split(",");
                Set<String> noSet = new HashSet<>(Arrays.asList(orderNos));
                for (String orderNo : noSet) {
                    orderNo = orderNo.trim();
                    Saleorder saleorder = saleorderMapper.getSaleOrderId(orderNo);
                    chooseSaleorder(saleorder);
                }
            }else if("after".equals(type)){
                Integer orderId = Integer.valueOf(split[1]);
                AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(orderId);
                chooseAfterorder(afterSales);
            }else if("saleAll".equals(type)){
              List<Integer> orderIdList = saleorderMapper.getVaildOrderAndNoChooseOrderId();
                for (Integer orderId : orderIdList) {
                    Saleorder saleorder = new Saleorder();
                    saleorder.setSaleorderId(orderId);
                    chooseSaleorder(saleorder);
                }
            }else if("putsaleReturn".equals(type)){
                try {
                    List<AfterSalesVo> list = afterSalesMapper.getNeedPutWmsSalereturnAfterList();
                    User user  = new User();
                    user.setUsername("定时任务");
                    for (AfterSalesVo afterSalesVo : list) {
                        putAfterReturnAuditFinishLister.onActionHappen(afterSalesVo,true,user);
                    }
                }catch (Exception e){
                    log.error("【logicalorderChoose】处理异常",e);
                }
            }else if("putsaleExchange".equals(type)){
                List<AfterSales> list = afterSalesMapper.getNeedPutWmsSaleExchangeAfterList();
                User user  = new User();
                user.setUsername("定时任务");
                for (AfterSales afterSales : list) {
                    logicalAfterorderChooseService.chooseLogicalAfterorder(afterSales,user);
                }
            }else if("closesale".equals(type)){
                String[] orderNos = split[1].split(",");
                Set<String> noSet = new HashSet<>(Arrays.asList(orderNos));
                for (String orderNo : noSet) {
                    orderNo = orderNo.trim();
                    Saleorder saleorder = saleorderMapper.getSaleOrderId(orderNo);
                    if(saleorder != null){
                        saleorder.setStatus(3);
                        saleorder.setLogisticsComments(saleorder.getLogisticsComments()+" WMS上线，清理历史数据");
                        saleorderMapper.updateByPrimaryKeySelective(saleorder);
                        warehouseStockService.updateOccupyStockService(saleorder,0);
                        XxlJobLogger.log("关闭销售订单号:{}",orderNo);
                    }
                }
            }else if("closebuy".equals(type)){
                String[] orderNos = split[1].split(",");
                Set<String> noSet = new HashSet<>(Arrays.asList(orderNos));
                for (String orderNo : noSet) {
                    orderNo = orderNo.trim();
                    BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoByBuyorderNo(orderNo);
                    buyorderVo.setStatus(3);
                    buyorderVo.setLogisticsComments(buyorderVo.getLogisticsComments()+" WMS上线，清理历史数据");
                    buyorderMapper.updateByPrimaryKeySelective(buyorderVo);
                    XxlJobLogger.log("关闭采购订单号:{}",orderNo);
                }

            }
        }
    }

    private void chooseAfterorder(AfterSales afterSales) throws Exception {
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(2);
        user.setPositionName("南京贝登");
        logicalAfterorderChooseService.chooseLogicalAfterorder(afterSales,user);
    }

    private void chooseSaleorder(Saleorder saleorder) throws Exception {
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(2);
        user.setPositionName("南京贝登");
        logicalSaleorderChooseService.chooseLogicalSaleorder(saleorder,user);
    }

}
