package com.vedeng.order.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class BuyorderModifyApplyGoods implements Serializable{
    /**
	 * @Fields serialVersionUID : TODO
	 */
	private static final long serialVersionUID = 1L;

	private Integer buyorderModifyApplyGoodsId;

    private Integer buyorderModifyApplyId;

    private Integer buyorderGoodsId;

    private String insideComments;

    private String oldInsideComments;

    // 修改后的 采购商品单价
    private BigDecimal price;

    // 原 采购商品单价
    private BigDecimal oldPrice;

    // 发货时间
    private Long sendGoodsTime;

    private String sendGoodsTimeShow;

    // 原来的 发货时间
    private Long oldSendGoodsTime;

    private String oldSendGoodsTimeShow;

    // 收货时间
    private Long receiveGoodsTime;

    private String receiveGoodsTimeShow;

    // 原来的 收货时间
    private Long oldReceiveGoodsTime;

    private String oldReceiveGoodsTimeShow;

    // 货期
    private String deliveryCycle;

    private String oldDeliveryCycle;

    /**
     * 是否有授权
     */
    private Integer isHaveAuth;

    private Integer oldIsHaveAuth;

    private BigDecimal referPrice = BigDecimal.ZERO;

    // 原 采购商品单价
    private BigDecimal oldReferPrice = BigDecimal.ZERO;

    public BigDecimal getReferPrice() {
        return referPrice;
    }

    public void setReferPrice(BigDecimal referPrice) {
        this.referPrice = referPrice;
    }

    public BigDecimal getOldReferPrice() {
        return oldReferPrice;
    }

    public void setOldReferPrice(BigDecimal oldReferPrice) {
        this.oldReferPrice = oldReferPrice;
    }

    public Integer getIsHaveAuth() {
        return isHaveAuth;
    }

    public void setIsHaveAuth(Integer isHaveAuth) {
        this.isHaveAuth = isHaveAuth;
    }

    public Integer getOldIsHaveAuth() {
        return oldIsHaveAuth;
    }

    public void setOldIsHaveAuth(Integer oldIsHaveAuth) {
        this.oldIsHaveAuth = oldIsHaveAuth;
    }

    public Integer getBuyorderModifyApplyGoodsId() {
        return buyorderModifyApplyGoodsId;
    }

    public void setBuyorderModifyApplyGoodsId(Integer buyorderModifyApplyGoodsId) {
        this.buyorderModifyApplyGoodsId = buyorderModifyApplyGoodsId;
    }

    public Integer getBuyorderModifyApplyId() {
        return buyorderModifyApplyId;
    }

    public void setBuyorderModifyApplyId(Integer buyorderModifyApplyId) {
        this.buyorderModifyApplyId = buyorderModifyApplyId;
    }

    public Integer getBuyorderGoodsId() {
        return buyorderGoodsId;
    }

    public void setBuyorderGoodsId(Integer buyorderGoodsId) {
        this.buyorderGoodsId = buyorderGoodsId;
    }

    public String getInsideComments() {
        return insideComments;
    }

    public void setInsideComments(String insideComments) {
        this.insideComments = insideComments == null ? null : insideComments.trim();
    }

    public String getOldInsideComments() {
        return oldInsideComments;
    }

    public void setOldInsideComments(String oldInsideComments) {
        this.oldInsideComments = oldInsideComments == null ? null : oldInsideComments.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getOldPrice() {
        return oldPrice;
    }

    public void setOldPrice(BigDecimal oldPrice) {
        this.oldPrice = oldPrice;
    }

    public Long getSendGoodsTime() {
        return sendGoodsTime;
    }

    public void setSendGoodsTime(Long sendGoodsTime) {
        this.sendGoodsTime = sendGoodsTime;
    }

    public Long getOldSendGoodsTime() {
        return oldSendGoodsTime;
    }

    public void setOldSendGoodsTime(Long oldSendGoodsTime) {
        this.oldSendGoodsTime = oldSendGoodsTime;
    }

    public Long getReceiveGoodsTime() {
        return receiveGoodsTime;
    }

    public void setReceiveGoodsTime(Long receiveGoodsTime) {
        this.receiveGoodsTime = receiveGoodsTime;
    }

    public Long getOldReceiveGoodsTime() {
        return oldReceiveGoodsTime;
    }

    public void setOldReceiveGoodsTime(Long oldReceiveGoodsTime) {
        this.oldReceiveGoodsTime = oldReceiveGoodsTime;
    }

    public String getSendGoodsTimeShow() {
        return sendGoodsTimeShow;
    }

    public void setSendGoodsTimeShow(String sendGoodsTimeShow) {
        this.sendGoodsTimeShow = sendGoodsTimeShow;
    }

    public String getOldSendGoodsTimeShow() {
        return oldSendGoodsTimeShow;
    }

    public void setOldSendGoodsTimeShow(String oldSendGoodsTimeShow) {
        this.oldSendGoodsTimeShow = oldSendGoodsTimeShow;
    }

    public String getReceiveGoodsTimeShow() {
        return receiveGoodsTimeShow;
    }

    public void setReceiveGoodsTimeShow(String receiveGoodsTimeShow) {
        this.receiveGoodsTimeShow = receiveGoodsTimeShow;
    }

    public String getOldReceiveGoodsTimeShow() {
        return oldReceiveGoodsTimeShow;
    }

    public void setOldReceiveGoodsTimeShow(String oldReceiveGoodsTimeShow) {
        this.oldReceiveGoodsTimeShow = oldReceiveGoodsTimeShow;
    }

    public String getDeliveryCycle() {
        return deliveryCycle;
    }

    public void setDeliveryCycle(String deliveryCycle) {
        this.deliveryCycle = deliveryCycle;
    }

    public String getOldDeliveryCycle() {
        return oldDeliveryCycle;
    }

    public void setOldDeliveryCycle(String oldDeliveryCycle) {
        this.oldDeliveryCycle = oldDeliveryCycle;
    }

    // 商品采购数量（页面不可修改，用于计算总额）
    private Integer num;

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    // 是否订单流新标识
    private Integer isNew;

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    /**
     * 直属费用单商品id
     */
    private Integer buyorderExpenseItemId;

    public Integer getBuyorderExpenseItemId() {
        return buyorderExpenseItemId;
    }

    public void setBuyorderExpenseItemId(Integer buyorderExpenseItemId) {
        this.buyorderExpenseItemId = buyorderExpenseItemId;
    }
}
