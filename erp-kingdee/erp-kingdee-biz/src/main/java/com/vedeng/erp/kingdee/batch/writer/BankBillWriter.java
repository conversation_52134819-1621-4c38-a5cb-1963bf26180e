package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveBillApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BankBillWriter extends BaseWriter<KingDeeReceiveBillDto> {


    @Autowired
    private KingDeeReceiveBillApiService kingDeeReceiveBillApiService;

    @Override
    public void doWrite(KingDeeReceiveBillDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("银行流水推送金蝶信息{},",JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.saveReceiveBill);
        kingDeeReceiveBillApiService.register(item,true);
    }
}
