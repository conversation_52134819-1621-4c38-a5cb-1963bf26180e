<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date" %>

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_view.css?rnd=${resourceVersionKey}">
</head>

<body>
<div class="detail-wrap">
    <!-- end -->
    <c:if test="${not empty goodsTodoItemVoList}">
        <div style="width: 400px;">
            <div class="detail-table">
                <c:forEach items="${goodsTodoItemVoList}" var="tdoItem">
                    <c:if test="${!tdoItem.done}">
                        <div class="table-item" style="width: 100%;height: 10%">
                            <div class="table-td" style="border: 0px;padding: 0px 8px;">
                                <div class="detail-block-tip vd-tip tip-blue" style="background: #fffce5;">
                                    <i class="vd-tip-icon vd-icon icon-info2 goods-required-item"
                                       style="color: #eFcC47"></i>
                                    <div class="vd-tip-cnt">${tdoItem.serviceName}
                                            <%--<a class="goto-ulr"  href="javascript:void(0);" tabTitle='{"num":"vgoodsview${coreSpuDto.spuId}","link":"${tdoItem.url}","title":"${tdoItem.viewName}"}'>去完善>></a>--%>
                                        <a class="goto-url"  href="javascript:void(0);" link="${tdoItem.url}">去完善>></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                </c:forEach>
                <c:forEach items="${goodsTodoItemVoList}" var="tdoItem">
                    <c:if test="${tdoItem.done}">
                        <div class="table-item" style="width: 100%;height: 10%">
                            <div class="table-td" style="border: 0px;padding: 0px 8px;">
                                <div class="detail-block-tip vd-tip tip-blue" style="background: #30e22f;">
                                    <i class="vd-tip-icon vd-icon icon-yes2"
                                       style="color: #1c9620"></i>
                                    <div class="vd-tip-cnt">${tdoItem.serviceName}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                </c:forEach>
            </div>
        </div>
    </c:if>
</div>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
<script type="text/javascript">
    $('.goto-url').click(function () {
        var tabValue = $(this).attr('link');
        if(tabValue != null){
            layer.closeAll();
            window.parent.location.href=tabValue;
        }
    });
</script>
