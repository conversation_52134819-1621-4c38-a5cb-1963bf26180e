package com.vedeng.todolist.model;

import java.io.Serializable;
import lombok.Data;

/**
 * T_RISK_CHECK_LOG
 * <AUTHOR>
@Data
public class RiskCheckLog implements Serializable {
    private Integer riskCheckLogId;

    /**
     * 待办事项主键
     */
    private Integer todoListId;

    /**
     * 风控类型
     */
    private Integer riskCheckBuzType;

    /**
     * 风控类型的主键
     */
    private Integer riskCheckBuzId;

    /**
     * 风控关联的信息
     */
    private String riskCheckBuzExtra;

    /**
     * 风控参数
     */
    private String riskCheckBuzProperty;

    /**
     * 风控触发状态，0未触发，1已触发
     */
    private Integer riskCheckTriggerStatus;

    /**
     * 风控触发者
     */
    private Integer riskCheckTriggerUser;

    /**
     * 风控触发时间
     */
    private Long riskCheckTriggerTime;

    /**
     * 风控触发备注
     */
    private String riskCheckTriggerComment;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 是否删除，0未删除，1已删除
     */
    private Integer delete;

    private static final long serialVersionUID = 1L;

    public RiskCheckLog() {
    }

    public RiskCheckLog(Integer todoListId, Integer riskCheckBuzType, Integer riskCheckBuzId, String riskCheckBuzExtra, String riskCheckBuzProperty,
                        Long addTime) {
        this.todoListId = todoListId;
        this.riskCheckBuzType = riskCheckBuzType;
        this.riskCheckBuzId = riskCheckBuzId;
        this.riskCheckBuzExtra = riskCheckBuzExtra;
        this.riskCheckBuzProperty = riskCheckBuzProperty;
        this.addTime = addTime;
    }
}