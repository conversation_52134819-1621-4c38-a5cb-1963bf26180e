package com.vedeng.erp.trader.domain.dto;

import com.vedeng.onedataapi.api.terminal.req.TerminalAreaSearchDto;
import com.vedeng.onedataapi.api.trader.req.TraderAreaSearchDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 终端数据
 */
@Getter
@Setter
public class TerminalRequestDto {

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 覆盖情况
     */
    private Integer coverStatus;

    /**
     * 合作情况
     */
    private Integer cooperationStatus;

    /**
     * 所在地区
     */
    private String belongArea;

    /**
     * 所在地区
     */
    private List<TerminalAreaSearchDto> terminalAreaSearchList;

    /**
     * 终端大类
     */
    private List<String> terminalCategories;

    /**
     * 机构评级
     */
    private List<Integer> institutionLevel;

    /**
     * 机构性质
     */
    private Integer institutionNature;

    /**
     * 科室
     */
    private String department;

    /**
     * 排序的字段
     * 1:合作经销商数
     * 2:招标次数
     */
    private Integer sortColumn;

    /**
     * 0 升序 1降序 (默认升序)
     */
    private Integer sortType;

}