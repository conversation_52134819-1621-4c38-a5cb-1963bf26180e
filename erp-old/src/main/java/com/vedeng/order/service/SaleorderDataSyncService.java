package com.vedeng.order.service;

import com.common.dto.CanalMqBodyDTO;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * @Author: daniel
 * @Date: 2022/3/7 11 03
 * @Description: 订单冗余表数据同步接口
 */
public interface SaleorderDataSyncService {

    /**
     * 根据T_SALEORDER表变化同步信息
     * @param mqBodyDTO 订单id集合
     */
    void syncDataBySaleorderTable(CanalMqBodyDTO mqBodyDTO);

    /**
     * 根据T_VERIFIES_INFO表变化同步信息
     * @param mqBodyDTO 订单id
     */
    void syncDataByVerifiesInfoTable(CanalMqBodyDTO mqBodyDTO);

    /**
     * 根据T_INVOICE_APPLY表变化同步信息
     * @param mqBodyDTO
     */
    void syncDataByInvoiceApplyTable(CanalMqBodyDTO mqBodyDTO);


    void syncDataByCapitalBillDetailTable(CanalMqBodyDTO mqBodyDTO);

    void syncDataByCommunicationRecordTable(CanalMqBodyDTO mqBodyDTO);

    void syncDataByAfterSalesTable(CanalMqBodyDTO mqBodyDTO);

    void syncContractStatusBySaleorderId(Integer saleOrderNo,String from);
}
