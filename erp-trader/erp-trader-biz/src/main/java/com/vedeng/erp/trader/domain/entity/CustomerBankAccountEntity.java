package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
    * 客户账户表
    */
@Data
public class CustomerBankAccountEntity extends BaseEntity {
    /**
    * 主键
    */
    private Long customerBankAccountId;

    /**
    * 交易者ID
    */
    private Integer traderId;

    /**
    * 账户类型 1银行 2微信 3支付宝
    */
    private Integer accountType;

    /**
    * 账号
    */
    private String accountNo;

    /**
    * 账户名称
    */
    private String accountName;

    /**
    * 银行ID
    */
    private Integer bankId;

    /**
    * 是否验证 0否 1是
    */
    private Integer isVerify;

    /**
    * 是否可用 0否 1是
    */
    private Integer isEnable;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    private Date lastUseTime;

    /**
    * 更新备注
    */
    private String updateRemark;
}
