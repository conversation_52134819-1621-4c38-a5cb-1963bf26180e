package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶供应商dto  由erp实际业务转换
 * @date 2022/8/26 10:18
 */
@Getter
@Setter
@NoArgsConstructor
@WriteBackField(needBackField = {"FCUSTID"})
public class KingDeeCustomerDto extends KingDeeMqBaseDto {

    private Integer id;

    /**
     * TRADER_CUSTOMER_FINANCE_ID,
     */
    private Integer traderCustomerFinanceId;

    /**
     * 客户编号(使用erp客户id trader customer id)
     */
    @BusinessID
    private Integer fNumber;

    /**
     * 客户内码 0：表示新增 非0：云星空系统单据ID值
     */
    @WriteBackField("FCUSTID")
    @KingDeeID
    private String fCustId;

    /**
     * 创建组织id
     */
    private Integer fCreateOrgId;

    /**
     * 使用组织id
     */
    private Integer fUseOrgId;

    /**
     * 客户名称
     */
    private String fName;

    /**
     * 客户简称
     */
    private String fShortName;

    /**
     * 客户类别编码
     */
    private String fCustTypeId;

    /**
     * 通讯地址
     */
    private String fAddress;

    /**
     * 发票抬头
     */
    private String fInvoiceTitle;

    /**
     * 纳税登记号
     */
    private String fTaxRegisterCode;

    /**
     * 开户银行
     */
    private String fInvoiceBankName;

    /**
     * 开票联系电话
     */
    private String fInvoiceTel;

    /**
     * 银行账号
     */
    private String fInvoiceBankAccount;

    /**
     * 开票通讯地址
     */
    private String fInvoiceAddress;

    /**
     * 客户分组编码
     */
    private String fGroup;

    /**
     * 结算币别 默认PRE001
     */
    private String fTradingCurrid;

    /**
     * 结算方式编码
     */
    private String fSettleTypeId;

    /**
     * 销售部门 填写部门编码
     */
    private String fSaldeptId;

    /**
     * 税率编码
     */
    private String fTaxRate;

    /**
     * 贸易条款 这是一个自定义的举例，如果有自定义，需要先确认金蝶单据字段后填写
     */
    private String fQZOKTradeTerms;

    /**  2022-11-10 接口调试新增字段 start
    /**
     * 所属医院共体
     */
    private String fQzokSyyygt;
    /**
     * 所属集团
     */
    private String fQzokSsjt;
    /**
     * 现用名
     */
    private String fQzokXym;
    /**
     * 客户性质
     */
    private String fQzokKhxztext;
    /**
     * 终端客户分类
     */
    private String fQzokZdkhfltext;
    /**
     * 医疗机构分类
     */
    private String fQzokYljgfltext;
    /**
     * 客户细分类
     */
    private String fQzokKhxfltext;
    /**
     * 医院等级
     */
    private String fQzokYydjtext;
    /**
     * 客户等级文本
     */
    private String fQzokKhdj;
    /**
     * 客户所属省份
     */
    private String fprovince;

    /**
     * 客户银行账户信息
     */
    private List<KingDeeCustomerDetailDto> FT_BD_CUSTBANK;
/**  2022-11-10 接口调试新增字段  end **/

    @Override
    public String getFormId() {
        return KingDeeFormConstant.BD_CUSTOMER;
    }

    public KingDeeCustomerDto(KingDeeBizEnums kingDeeBizEnums, Integer fNumber, String fName) {
        super(kingDeeBizEnums);
        this.fNumber = fNumber;
        this.fName = fName;
        this.fCreateOrgId = KingDeeConstant.ORG_ID;
        this.fUseOrgId = KingDeeConstant.ORG_ID;
        this.fTradingCurrid = "PRE001";
    }


}
