package com.vedeng.infrastructure.kingdee.common.sdk.utils;


import com.vedeng.infrastructure.kingdee.common.sdk.entity.AppCfg;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.*;

/**
 * <AUTHOR>
 */ /* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/utils/HttpUtils.class */
@Slf4j
public class HttpUtils {
    static int proxyRunning = -1;

    public static Proxy getProxy() {
        AppCfg cfg = CfgUtil.getAppDefaultCfg();
        if (cfg == null || cfg.getProxy() == null) {
            return null;
        }
        try {
            URL url = new URL(cfg.getProxy());
            if (isHostConnectable(url.getHost(), url.getPort())) {
                System.out.println(String.format("Used proxy,Host:%s,port:%s", url.getHost(), Integer.valueOf(url.getPort())));
                return new Proxy(Proxy.Type.HTTP, new InetSocketAddress(url.getHost(), url.getPort()));
            }
            System.out.println(String.format("is not host %s", url.toString()));
            return null;
        } catch (MalformedURLException e) {
            log.error("【getProxy】处理异常",e);
            return null;
        }
    }

    static boolean proxyIsRunning() {
        if (proxyRunning > -1) {
            return proxyRunning != 0;
        }
        if (isHostConnectable("127.0.0.1", 8888)) {
            proxyRunning = 1;
            return true;
        }
        proxyRunning = 0;
        return false;
    }

    static boolean isHostConnectable(String host, int port) {
        Socket socket = new Socket();
        try {
            socket.connect(new InetSocketAddress(host, port));
            try {
                socket.close();
                return true;
            } catch (IOException e) {
                log.error("【isHostConnectable】处理异常",e);
                return true;
            }
        } catch (IOException e2) {
            try {
                socket.close();
            } catch (IOException e3) {
            }
            return false;
        } catch (Throwable th) {
            try {
                socket.close();
            } catch (IOException e4) {
                log.error("【socket.close】 error",e4);
            }
            throw th;
        }
    }
}
