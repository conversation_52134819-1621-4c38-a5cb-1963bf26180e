<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderCertificateMapper" >
    <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderCertificate" >
        <id column="TRADER_CERTIFICATE_ID" property="traderCertificateId" jdbcType="INTEGER" />
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
        <result column="SYS_OPTION_DEFINITION_ID" property="sysOptionDefinitionId" jdbcType="INTEGER" />
        <result column="BEGINTIME" property="begintime" jdbcType="BIGINT" />
        <result column="ENDTIME" property="endtime" jdbcType="BIGINT" />
        <result column="NAME" property="name" jdbcType="VARCHAR" />
        <result column="SN" property="sn" jdbcType="VARCHAR" />
        <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
        <result column="URI" property="uri" jdbcType="VARCHAR" />
        <result column="IS_MEDICAL" property="isMedical" jdbcType="BIT" />
        <result column="EXTRA" property="extra" jdbcType="VARCHAR" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="ISSUE_DATE" property="issueDate" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
        <result column="AUTH_POST" property="authPost" jdbcType="VARCHAR" />
        <result column="AUTH_USERNAME" property="authUserName" jdbcType="VARCHAR" />
        <result column="AUTH_CONTACTINFO" property="authContactInfo" jdbcType="VARCHAR" />
        <result column="RECORD_NO" property="recordNo" jdbcType="VARCHAR" />
        <result column="SUFFIX" property="suffix" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap type="com.vedeng.trader.model.vo.TraderCertificateVo" id="VoResultMap" extends="BaseResultMap">
        <result column="THREE_IN_ONE" property="threeInOne" jdbcType="BIT" />
        <result column="MEDICAL_QUALIFICATION" property="medicalQualification" jdbcType="BIT" />
    </resultMap>

    <insert id="insertSelective" parameterType="com.vedeng.trader.model.TraderCertificate" >
        insert into T_TRADER_CERTIFICATE
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="traderCertificateId != null" >
                TRADER_CERTIFICATE_ID,
            </if>
            <if test="traderId != null" >
                TRADER_ID,
            </if>
            <if test="sysOptionDefinitionId != null" >
                SYS_OPTION_DEFINITION_ID,
            </if>
            <if test="begintime != null" >
                BEGINTIME,
            </if>
            <if test="endtime != null" >
                ENDTIME,
            </if>
            <if test="name != null" >
                NAME,
            </if>
            <if test="sn != null" >
                SN,
            </if>
            <if test="domain != null" >
                DOMAIN,
            </if>
            <if test="uri != null" >
                URI,
            </if>
            <if test="isMedical != null" >
                IS_MEDICAL,
            </if>
            <if test="extra != null" >
                EXTRA,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="modTime != null" >
                MOD_TIME,
            </if>
            <if test="updater != null" >
                UPDATER,
            </if>
            <if test="ossResourceId != null">
                OSS_RESOURCE_ID,
            </if>
            <if test="issueDate != null">
                ISSUE_DATE,
            </if>
            <if test="recordNo != null" >
                RECORD_NO,
            </if>
            <if test="suffix != null" >
                SUFFIX,
            </if>
            <if test="traderType != null" >
                TRADER_TYPE,
            </if>
            <if test="isDelete != null" >
                IS_DELETE,
            </if>
            <if test="relatedId != null" >
                RELATED_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="traderCertificateId != null" >
                #{traderCertificateId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null" >
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="sysOptionDefinitionId != null" >
                #{sysOptionDefinitionId,jdbcType=INTEGER},
            </if>
            <if test="begintime != null" >
                #{begintime,jdbcType=BIGINT},
            </if>
            <if test="endtime != null" >
                #{endtime,jdbcType=BIGINT},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sn != null" >
                #{sn,jdbcType=VARCHAR},
            </if>
            <if test="domain != null" >
                #{domain,jdbcType=VARCHAR},
            </if>
            <if test="uri != null" >
                #{uri,jdbcType=VARCHAR},
            </if>
            <if test="isMedical != null" >
                #{isMedical,jdbcType=BIT},
            </if>
            <if test="extra != null" >
                #{extra,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="ossResourceId != null">
                #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="issueDate != null">
                #{issueDate,jdbcType=BIGINT},
            </if>
            <if test="recordNo != null" >
                #{recordNo,jdbcType=VARCHAR},
            </if>
            <if test="suffix != null" >
                #{suffix,jdbcType=VARCHAR},
            </if>
            <if test="traderType != null" >
                #{traderType, jdbcType=INTEGER},
            </if>
            <if test="isDelete != null" >
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null" >
                #{relatedId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.TraderCertificate" >
        update T_TRADER_CERTIFICATE
        <set >
            <if test="traderId != null" >
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="sysOptionDefinitionId != null" >
                SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER},
            </if>
            <if test="begintime != null" >
                BEGINTIME = #{begintime,jdbcType=BIGINT},
            </if>
            <if test="endtime != null" >
                ENDTIME = #{endtime,jdbcType=BIGINT},
            </if>
            <if test="name != null" >
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sn != null" >
                SN = #{sn,jdbcType=VARCHAR},
            </if>
            <if test="domain != null" >
                DOMAIN = #{domain,jdbcType=VARCHAR},
            </if>
            <if test="uri != null" >
                URI = #{uri,jdbcType=VARCHAR},
            </if>
            <if test="isMedical != null" >
                IS_MEDICAL = #{isMedical,jdbcType=BIT},
            </if>
            <if test="extra != null" >
                EXTRA = #{extra,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null" >
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null" >
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="ossResourceId != null">
                OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="issueDate != null">
                ISSUE_DATE = #{issueDate,jdbcType=BIGINT},
            </if>
            <if test="recordNo != null">
                RECORD_NO = #{recordNo,jdbcType=VARCHAR},
            </if>
            <if test="suffix != null">
                SUFFIX = #{suffix,jdbcType=VARCHAR},
            </if>
        </set>
        where TRADER_CERTIFICATE_ID = #{traderCertificateId,jdbcType=INTEGER}
    </update>
    <sql id="Base_Column_List" >
    TRADER_CERTIFICATE_ID, TRADER_ID, SYS_OPTION_DEFINITION_ID, BEGINTIME, ENDTIME,NAME, SN,
    DOMAIN, URI, IS_MEDICAL, EXTRA,RECORD_NO,ADD_TIME,MOD_TIME,ISSUE_DATE,SUFFIX,IS_DELETE,TRADER_TYPE,RELATED_ID
    </sql>

    <update id="updateTraderCertificate">
        UPDATE T_TRADER_CERTIFICATE
        SET URI = #{certificate.uri},
        DOMAIN = #{certificate.domain},
        OSS_RESOURCE_ID = #{certificate.ossResourceId},
        EXTRA = '1'
        WHERE TRADER_CERTIFICATE_ID = #{certificate.traderCertificateId}
    </update>

    <select id="getTraderCertificatesByTraderId" resultMap="BaseResultMap" parameterType="com.vedeng.trader.model.TraderCustomer">
        select
        <include refid="Base_Column_List" />
        from T_TRADER_CERTIFICATE
        where TRADER_ID = #{traderId,jdbcType=INTEGER} AND IS_DELETE = 0
    </select>

    <select id="getCertificateListByTraderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_TRADER_CERTIFICATE
        where 1=1
        and TRADER_ID = #{traderId,jdbcType=INTEGER} AND IS_DELETE = 0 and TRADER_TYPE = #{traderType,jdbcType=INTEGER}
    </select>
    <select id="getTraderCertificates" resultType="com.vedeng.trader.model.TraderCertificate">
        SELECT * FROM T_TRADER_CERTIFICATE where TRADER_CERTIFICATE_ID &gt; #{start} AND IS_DELETE = 0 AND DOMAIN = 'file1.vedeng.com' limit #{limit}
    </select>
    <select id="getTraderCertificateById" resultType="com.vedeng.trader.model.TraderCertificate">
        SELECT * FROM T_TRADER_CERTIFICATE WHERE TRADER_CERTIFICATE_ID = #{id}  AND IS_DELETE = 0
    </select>

    <delete id="delTraderCertificateAndByTypeId" >
        DELETE
        FROM T_TRADER_CERTIFICATE
        WHERE
        TRADER_ID = #{traderId,jdbcType=INTEGER}
        AND SYS_OPTION_DEFINITION_ID =#{typeId, jdbcType=INTEGER}
    </delete>
    <delete id="delTraderCertificate" parameterType="java.lang.Integer">
  	delete from T_TRADER_CERTIFICATE where TRADER_ID = #{traderId,jdbcType=INTEGER}
  </delete>


    <select id="getListTraderCertificateVo" resultType="com.vedeng.trader.model.vo.TraderCertificateVo">
        select
        tc.TRADER_CERTIFICATE_ID, tc.TRADER_ID, tc.SYS_OPTION_DEFINITION_ID, tc.BEGINTIME, tc.ENDTIME,tc.NAME, tc.SN,tc.ISSUE_DATE,tc.RECORD_NO,tc.SUFFIX,
        tc.DOMAIN, tc.URI, tc.IS_MEDICAL, tc.EXTRA, tc.ADD_TIME, tc.CREATOR, tc.MOD_TIME, tc.UPDATER, tc.AUTH_POST, tc.AUTH_USERNAME, tc.AUTH_CONTACTINFO, t.THREE_IN_ONE, t.MEDICAL_QUALIFICATION
        from T_TRADER_CERTIFICATE tc
        left join T_TRADER t on t.TRADER_ID=tc.TRADER_ID
        where
        tc.TRADER_ID = #{traderId,jdbcType=INTEGER}
        and tc.SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER}
          <if test="traderType != null">
              and tc.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
          </if>
        AND tc.IS_DELETE = 0
        order by tc.ADD_TIME desc
    </select>
    <select id="getTraderCertificateVoListByIdAndType"
            resultType="com.vedeng.trader.model.vo.TraderCertificateVo">
        select
        tc.TRADER_CERTIFICATE_ID, tc.TRADER_ID, tc.SYS_OPTION_DEFINITION_ID, tc.BEGINTIME, tc.ENDTIME,tc.NAME, tc.SN,tc.ISSUE_DATE,tc.SUFFIX,
        tc.DOMAIN, tc.URI, tc.IS_MEDICAL, tc.EXTRA, tc.ADD_TIME, tc.CREATOR, tc.MOD_TIME, tc.UPDATER, tc.AUTH_POST, tc.AUTH_USERNAME, tc.AUTH_CONTACTINFO, t.THREE_IN_ONE, t.MEDICAL_QUALIFICATION
        from T_TRADER_CERTIFICATE tc
        left join T_TRADER t on t.TRADER_ID=tc.TRADER_ID
        where 1=1 AND tc.IS_DELETE = 0

            and tc.TRADER_ID = #{traderId,jdbcType=INTEGER}

        <if test="traderType != null">
            and tc.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
        </if>
        <if test="sysOptionDefinitionId != null">
            and tc.SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER}
        </if>
        order by tc.ADD_TIME desc
    </select>


    <!--getTraderCertificate  -->
    <select id="getTraderCertificateVo" resultMap="VoResultMap" parameterType="com.vedeng.trader.model.vo.TraderCertificateVo" >
        select
        tc.TRADER_CERTIFICATE_ID, tc.TRADER_ID, tc.SYS_OPTION_DEFINITION_ID, tc.BEGINTIME, tc.ENDTIME,tc.NAME, tc.SN,
        tc.DOMAIN, tc.URI, tc.IS_MEDICAL, tc.EXTRA, tc.ADD_TIME, tc.CREATOR, tc.MOD_TIME, tc.UPDATER, tc.AUTH_POST,
        tc.AUTH_USERNAME, tc.AUTH_CONTACTINFO, t.THREE_IN_ONE, t.MEDICAL_QUALIFICATION,tc.RECORD_NO,tc.ISSUE_DATE,tc.SUFFIX
        from T_TRADER_CERTIFICATE tc
        left join T_TRADER t on t.TRADER_ID=tc.TRADER_ID
        where tc.IS_DELETE = 0
        <if test="traderId != null">
            and tc.TRADER_ID = #{traderId,jdbcType=INTEGER}
        </if>
        order by tc.ADD_TIME desc
    </select>

    <!--getTraderCertificate  -->
    <select id="getTraderCertificatePageVo" resultMap="VoResultMap" parameterType="com.vedeng.trader.model.vo.TraderCertificateVo" >
        select
        tc.TRADER_CERTIFICATE_ID, tc.TRADER_ID, tc.SYS_OPTION_DEFINITION_ID, tc.BEGINTIME, tc.ENDTIME,tc.NAME, tc.SN,
        tc.DOMAIN, tc.URI, tc.IS_MEDICAL, tc.EXTRA, tc.ADD_TIME, tc.CREATOR, tc.MOD_TIME, tc.UPDATER, tc.AUTH_POST,
        tc.AUTH_USERNAME, tc.AUTH_CONTACTINFO, t.THREE_IN_ONE, t.MEDICAL_QUALIFICATION,tc.RECORD_NO,tc.ISSUE_DATE,tc.SUFFIX
        from T_TRADER_CERTIFICATE tc
        left join T_TRADER t on t.TRADER_ID=tc.TRADER_ID
        where 1=1 and tc.IS_DELETE = 0
        <if test="traderId != null">
            and tc.TRADER_ID = #{traderId,jdbcType=INTEGER}
        </if>
        <if test="traderType != null">
            and tc.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
        </if>
        <if test="sysOptionDefinitionId != null">
            and tc.SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER}
        </if>
        order by tc.ADD_TIME desc limit 1
    </select>

    <select id="getTraderCertificateList" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            T_TRADER_CERTIFICATE
        WHERE
            1 = 1
          AND IS_DELETE = 0
          AND SUFFIX IS NULL
          AND ( URI IS NOT NULL AND URI != '' )
          AND ( DOMAIN IS NOT NULL AND DOMAIN != '' )
        <if test="page != null and count != null">
            LIMIT  #{page, jdbcType=INTEGER}, #{count, jdbcType=INTEGER}
        </if>
    </select>

    <update id="batchUpdateTraderCertificate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE T_TRADER_CERTIFICATE SET
            SUFFIX = #{item.suffix,jdbcType=INTEGER}
            where TRADER_CERTIFICATE_ID = #{item.traderCertificateId,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="delTraderCertificateByTraderIdAndTraderType">
        update T_TRADER_CERTIFICATE set IS_DELETE = 1 where TRADER_ID = #{traderId} AND TRADER_TYPE = #{traderType}
    </update>

    <update id="updateTraderCertificateSuffix">
        UPDATE T_TRADER_CERTIFICATE SET
        SUFFIX = #{traderCertificate.suffix,jdbcType=INTEGER}
        where TRADER_CERTIFICATE_ID = #{traderCertificate.traderCertificateId,jdbcType=INTEGER}
    </update>

    <select id="getTraderCertificatesById" parameterType="com.vedeng.trader.model.TraderCertificate" resultType="com.vedeng.trader.model.TraderCertificate">
        select
        <include refid="Base_Column_List" />
        from T_TRADER_CERTIFICATE
        where 1=1 AND IS_DELETE = 0
        <if test="relatedId != null">
            and RELATED_ID = #{relatedId,jdbcType=INTEGER}
        </if>
        <if test="traderType != null">
            and TRADER_TYPE = #{traderType,jdbcType=INTEGER}
        </if>
        <if test="sysOptionDefinitionId != null">
            and SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER}
        </if>
    </select>

    <update id="updatTraderBusinessCard" parameterType="com.vedeng.trader.model.TraderCertificate">
        update T_TRADER_CERTIFICATE set IS_DELETE = 1
        where RELATED_ID = #{relatedId,jdbcType=INTEGER}
          AND TRADER_TYPE = #{traderType}
          AND SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER}
    </update>

</mapper>