package com.vedeng.goods.dao;

import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSkuSearchGenerate;
import com.vedeng.goods.model.CoreSkuSearchGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CoreSkuSearchGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int countByExample(CoreSkuSearchGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int deleteByExample(CoreSkuSearchGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int deleteByPrimaryKey(Integer skuId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int insert(CoreSkuSearchGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int insertSelective(CoreSkuSearchGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    List<CoreSkuSearchGenerate> selectByExample(CoreSkuSearchGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    CoreSkuSearchGenerate selectByPrimaryKey(Integer skuId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int updateByExampleSelective(@Param("record") CoreSkuSearchGenerate record, @Param("example") CoreSkuSearchGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int updateByExample(@Param("record") CoreSkuSearchGenerate record, @Param("example") CoreSkuSearchGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int updateByPrimaryKeySelective(CoreSkuSearchGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU_SEARCH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    int updateByPrimaryKey(CoreSkuSearchGenerate record);

    /** 
     * @description: 批量更新skusearch表更新时间.
     * @jira: VDERP-2492 修改手动填报预计可发货时间触发通知XXX.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/20 3:19 下午.
     * @author: Tomcat.Hui.
     * @param skuNoList: . 
     * @return: java.lang.Integer.
     * @throws: .
     */ 
    Integer updateModTime(@Param("list") List<CoreSkuGenerate> skuNoList);

    /**
     * 更新采购到货时间
     * @param skuNoList
     * @return
     */
    Integer updateSkuSearchPurchaseTime(@Param("list") List<CoreSkuGenerate> skuNoList);
}