package com.vedeng.erp.kingdee.batch.dto;

import lombok.*;

import java.util.Date;


/**
 * @description wms出库
 * <AUTHOR>
 * @date 2023/12/11 8:54
 **/

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BatchWmsOutOrderDto {
    /**
    * id
    */
    private Integer id;

    /**
    * wmsNo
    */
    private String wmsNo;

    /**
    * erpNo
    */
    private String erpNo;

    /**
    * 拣货人
    */
    private String picker;

    /**
    * 复核人
    */
    private String reviewer;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 创建者id
    */
    private Integer creator;

    /**
    * 修改者id
    */
    private Integer updater;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者名称
    */
    private String updaterName;
}