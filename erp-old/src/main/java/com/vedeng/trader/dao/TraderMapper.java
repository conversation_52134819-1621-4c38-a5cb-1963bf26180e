
package com.vedeng.trader.dao;

import com.newtask.dto.TraderDto;
import com.pricecenter.dto.ContractTraderDto;
import com.vedeng.system.model.vo.AccountSalerToGo;
import com.vedeng.trader.model.RTraderJUser;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import org.apache.ibatis.annotations.Param;

import com.vedeng.trader.model.Trader;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <b>Description: 客户基本信息mapper</b><br> 
 * <b>Author: Franlin.wu</b> 
 * @fileName TraderMapper.java
 * <br><b>Date: 2019年5月13日 下午1:33:45 </b> 
 *
 */
public interface TraderMapper
{

	/**
	 * <b>Description:</b><br> 新增客户
	 * @param record
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年5月15日 下午5:00:41
	 */
	int insert(Trader trader);

	Trader selectByPrimaryKey(Integer traderId);

	/**
	 * 
	 * <b>Description: 根据traderId</b><br> 
	 * @param traderId
	 * @param string 
	 * @return
	 * <b>Author: Franlin.wu</b>  
	 * <br><b>Date: 2019年5月13日 下午1:35:23 </b>
	 */
	Trader getTraderByTraderId(@Param("traderId") Integer traderId);
	/**
	 * 
	 * <b>Description: 根据traderId,和手机号获取客户基本信息</b><br> 
	 * @param traderId
	 * @param string 
	 * @return
	 * <b>Author: strange</b>  
	 * <br><b>Date: 2019年5月13日 下午1:35:23 </b>
	 */
	Trader getTraderInfoByTraderId(@Param("traderId") Integer traderId);
	
	/**
	* @Title: getTraderBussinessAreaByTraderId
	* @Description: TODO(根据traderid获取交易者的经营范围areaids)
	* @param @param traderId
	* @return String    返回类型
	* <AUTHOR>
	* @throws
	* @date 2019年7月22日
	*/
	String getTraderBussinessAreaByTraderId(@Param("traderId") Integer traderId);
	
	
	/**
	* @Title: getTraderCompanyByTraderId
	* @Description: TODO(获取交易者公司信息)
	* @param @param traderId
	* @return Trader    返回类型
	* <AUTHOR>
	 * @param integer 
	* @throws
	* @date 2019年7月25日
	*/
	Trader getTraderCompanyByTraderId(@Param("traderId")Integer traderId,@Param("traderAddressId") Integer traderAddressId);
	/**
	* @Title: getTraderNameByTraderContactId
	* @Description: TODO(获取交易者姓名电话和id)
	* @param @param traderContactId
	* @param @return    参数
	* @return Trader    返回类型
	* <AUTHOR>
	* @throws
	* @date 2019年7月25日
	*/
	Trader getTraderNameByTraderContactId(Integer traderContactId);

	/**
	 * 客户归属推送（不要调用）
	 * <b>Description:</b>
	 *
	 * @param map
	 * @return ResultInfo<?>
	 * @Note <b>Author：</b> cooper.xu <b>Date:</b> 2019年9月24日 下午6:44:07
	 */
	List<AccountSalerToGo> getAccountSaler(List<Integer> list);
	/**
	* @Title: getTraderTypeById
	* @Description: TODO(获取客户类型)
	* @param @param traderId
	* @return Integer    返回类型
	* <AUTHOR>
	* @throws
	* @date 2019年8月26日
	*/
	Integer getTraderTypeById(@Param("traderId")Integer traderId);


	/**
	 * <b>Description:</b>分页获取客户信息，traderType=1<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/2/29
	 */
	List<Trader> getTraderListPage(Map<String,Object> map);


	/**
	* @Description: 修改客户列表
	* @Param:
	* @return:
	* @Author: addis
	* @Date: 2020/3/3
	*/
	Integer updateBelongPlatformOfTrader(Trader trader);

	/**
	 * <b>Description:</b>修改客户列表部分字段<br>
	 * @param trader 客户信息
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/3
	 */
	void updatePartBySelective(Trader trader);

	Trader getTraderWrapByTraderId(Integer traderId);

	@Deprecated
	List<Integer> getAllUserId();

	void updateTraderPlatformByUserList(@Param("belongPlatform") Integer belongPlatform, @Param("userList") List<Integer> userList);


	Trader getTraderAptitudeCheckedByTraderId(@Param("traderId")Integer traderId);

	/**
	*获取宝石花客户id
	* @Author:strange
	* @Date:16:23 2020-02-20
	*/
    List<Integer> getFlowerTraderId(@Param("traderNameList") List<String> traderNameList);

	Trader findTraderByName(String traderName);

    Integer findTraderAuditStatus(Integer traderId);

    List<TraderCustomerVo> getLimitPriceTraderName(@Param("traderName") String traderName);

	/**
	 * 仅限定时任务
	 * @return
	 */
	@Deprecated
    List<Trader> getTraderList();

	/**
	 * 仅限定时任务
	 * @return
	 */
	@Deprecated
	List<Trader> getCustomerList();

	/**
	 * 仅限定时任务
	 * @return
	 */
	@Deprecated
	List<Trader> getSupplierList();

	/**
	 * 仅限定时任务
	 * @return
	 */
	@Deprecated
	List<Trader> getCustomerAndsupplierList();


    List<Trader> getTraderByWebAccountMobile(String mobile);

    List<Trader> getCustomerAndsupplierIn(@Param("traderIdStr") String traderIdStr);

    void updateLastCommunicateTimeOfTrader(@Param("traderId") Integer traderId, @Param("communicateTime") Long communicateTime);

    List<Integer> getTraderListOfLastCommunicateTimeIsNull(@Param("startTraderId") Integer startTraderId,
														   @Param("traderId") Integer traderId,
														   @Param("restart") Integer restart,
														   @Param("limit") Integer limit);

	List<Trader> getTraderSupplyByTraderName(String traderName);

	/**
	 * 获取审核通过的客户的traderId和审核通过时间
	 * @jira: .
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2021/1/6 14:20.
	 * @author: Randy.Xu.
	 * @param traderCustomerVo
	 * @return: com.vedeng.trader.model.Trader.
	 * @throws:  .
	 */
    Trader getTraderCustomerValidTime(TraderCustomerVo traderCustomerVo);





	/**
	 * 获取审核通过的供应商的traderId和审核通过时间
	 * @jira: .
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2021/1/7 9:06.
	 * @author: Randy.Xu.
	 * @param traderSupplierVo
	 * @return: com.vedeng.trader.model.Trader.
	 * @throws:  .
	 */
	Trader getTraderSupplyValidTime(TraderSupplierVo traderSupplierVo);

	/**
	 * 更新traderId对应的审核通过时间
	 * @jira: .
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2021/1/7 15:04.
	 * @author: Randy.Xu.
	 * @param traderId
	 * @param validTime
	 * @return: void.
	 * @throws:  .
	 */
	void UpdateLastValidTimeByIdAndTime(@Param("traderId") Integer traderId, @Param("validTime") Date validTime);

	List<Trader> getTraderByTraderName(@Param("traderName") String traderName);

	void updateTraderWarehouseAdressById(TraderDto traderDto);

    Trader getTraderInfoByTraderIdAndMobile(@Param("traderId") Integer traderId, @Param("mobile") String mobile);



	Trader getJDTraderInfoByTraderIdAndMobile(@Param("traderId") Integer traderId, @Param("mobile") String mobile);

    List<Trader> getChildrenTrader(Integer traderId);

    List<ContractTraderDto> findTradePlatFormByIds(@Param("tradreIdList") List<Integer> tradreIdList);

    List<Integer> getTradeIdList(@Param("companyId") Integer companyId);

    RTraderJUser getRTraderUser(RTraderJUser rTraderJUser);

    List<Trader>  queryNoPublicGroupList();

	void setPublicCustomerPrivatizedByAssign(@Param("traderIdList") List<Integer> traderIdList);

	void setPublicCustomerPrivatizedByAssignWithTraderId(@Param("traderCustomerId") Integer traderCustomerId);

	int updateByTraderId(@Param("traderName") String traderName, @Param("traderId")Integer traderId);

	/**
	 * @desc 查询实付金额订单，判断首单逻辑
	 * @param traderId
	 * @return
	 */
	List<Integer> getSaleorderRealAmountByTraderId(Integer traderId);

	Map<String,Object> findByTraderIdGetSaleInfo(Integer traderId);

	/**
	 * 获取应急系统指定的客户信息（主要获取地址相关）
	 * @param traderId
	 * @param mobile
	 * @param address 
	 * @param  
	 * @return
	 */
	Trader getTraderInfoByDeal(@Param("traderId") Integer traderId, @Param("mobile") String mobile, @Param("address") String address, @Param("contactName") String contactName);

}

