package com.wms.service.processor.input;

import com.vedeng.common.util.DateUtil;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.service.processor.AbstractInputOrOutputProcessor;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * CS类型的入库单处理器
 */
@Service
public class CsInputOrderProcessor extends AbstractInputOrOutputProcessor<InputOrderDto> {


    private static final Logger logger = LoggerFactory.getLogger(CsInputOrderProcessor.class);

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    private StringBuffer statisticsInfo  = new StringBuffer();

    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Override
    protected String getBusinessKey(InputOrderDto inputOrderDto) {
        return "CS" + inputOrderDto.getASNNO();
    }

    @Override
    protected void commonValidator(InputOrderDto inputOrderDto) throws Exception {

        if(!WmsInterfaceOrderType.INPUT_CS_INIT.equals(inputOrderDto.getASNType())){
            throw new Exception("入库单:"+inputOrderDto.getASNReference1()+"的类型错误!");
        }

        if(CollectionUtils.isEmpty(inputOrderDto.getDetails())){
            throw new Exception("平滑上线初始化单据中的商品不能为空");
        }

    }

    @Override
    protected void insertOrUpdateWarehouseLog(InputOrderDto orderDto) throws Exception {

        logger.info("CS入单库处理器开始,本次处理的单号:"+orderDto.getASNNO()+"=================");

        statisticsInfo.setLength(0);

        StringBuffer currentStatistic = new StringBuffer();

        for(InputOrderGoodsDto inputOrderGood : orderDto.getDetails()){

            currentStatistic.setLength(0);

            //查询设置为特殊库位 入库日志
            WarehouseGoodsOperateLog queryCon = new WarehouseGoodsOperateLog();
            queryCon.setStorageLocationId(1325);
            queryCon.setStorageRackId(284);
            queryCon.setStorageAreaId(15);
            queryCon.setStorageRoomId(19);
            queryCon.setWarehouseId(9);
            queryCon.setGoodsId(Integer.valueOf(inputOrderGood.getSKU().substring(1)));

            logger.info("查询特殊库位的请求 sku：" + queryCon.getGoodsId());
            //特殊库位的入库记录
            List<WarehouseGoodsOperateLog> warehouseLogListByType = warehouseGoodsOperateLogMapper.getSpecialLocationLog(queryCon);

            logger.info("查询特殊库位的响应 数量：" + warehouseLogListByType.size());

            if(CollectionUtils.isEmpty(warehouseLogListByType)) {
                continue;
            }

            int index = 0;

            //待处理数量
            int waitDealNum = inputOrderGood.getReceivedQty().intValue();

            logger.info("本次SKU:"+inputOrderGood.getSKU()  +",待处理的数量：" + waitDealNum +"\n");

            int allDealNum = 0;

            while(waitDealNum > 0 && index <= (warehouseLogListByType.size()-1)){

                WarehouseGoodsOperateLog warehouseGoodsOperateLog = warehouseLogListByType.get(index);

                int dealNum = dealWithWarehouseLog(waitDealNum,warehouseGoodsOperateLog,inputOrderGood);

                allDealNum += dealNum;

                if(waitDealNum <= warehouseGoodsOperateLog.getNum()){
                    break;
                }

                waitDealNum -= warehouseGoodsOperateLog.getNum();

                index++;
            }


            currentStatistic.append("本次sku:" + inputOrderGood.getSKU() + ",待处理数量:" + inputOrderGood.getReceivedQty() + ".");
            currentStatistic.append("本次sku:" + inputOrderGood.getSKU() + ",已处理数量:" + allDealNum  + ".");
            statisticsInfo.append(currentStatistic);
        }

        logger.info("本次错误处理结果:" + statisticsInfo.toString());
    }

    /**
     * 处理对应的单据
     * @param waitDealNum
     * @param warehouseGoodsOperateLog
     * @param inputOrderGood
     */
    private int dealWithWarehouseLog(int waitDealNum, WarehouseGoodsOperateLog warehouseGoodsOperateLog,InputOrderGoodsDto inputOrderGood) {

        int minNum = Math.min(waitDealNum,warehouseGoodsOperateLog.getNum());

        Integer goodsId =  Integer.valueOf(inputOrderGood.getSKU().substring(1));

        logger.info("查询具体类型的请求 sku=" + goodsId + ",operateType= "+warehouseGoodsOperateLog.getOperateType()+",relatedId=" + warehouseGoodsOperateLog.getRelatedId());

        List<WarehouseGoodsOperateLog> warehouseLogList = warehouseGoodsOperateLogMapper.getWarehouseoutLogByTypeAndRelateId(goodsId,warehouseGoodsOperateLog.getOperateType(),warehouseGoodsOperateLog.getRelatedId());

        logger.info("查询具体类型的请求 响应数量=" + warehouseLogList.size());

        for(int i = 0;i < minNum; i++){

            WarehouseGoodsOperateLog updateLog = warehouseLogList.get(i);

            //生产日期
            updateLog.setProductDate(DateUtil.convertLong(inputOrderGood.getLotAtt01() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));

            //效期
            updateLog.setExpirationDate(DateUtil.convertLong(inputOrderGood.getLotAtt02() +" 00:00:00","yyyy-MM-dd HH:mm:ss"));

            //入库时间
            updateLog.setCheckStatusTime(DateUtil.convertLong(inputOrderGood.getLotAtt03() +" 00:00:00","yyyy-MM-dd HH:mm:ss"));

            //批次号
            updateLog.setBatchNumber("NO-LOT".equals(inputOrderGood.getLotAtt04()) ? "": inputOrderGood.getLotAtt04());

            //灭菌批号
            updateLog.setSterilizationBatchNo(inputOrderGood.getLotAtt05());

            //贝登批次吗
            updateLog.setVedengBatchNumer(inputOrderGood.getLotAtt11());

            updateLog.setModTime(System.currentTimeMillis());

            warehouseGoodsOperateLogMapper.updateByPrimaryKeySelective(updateLog);
        }

        logger.info("本次SKU:V"+goodsId +"已处理的数量:" + minNum);

        return minNum;
    }


    @Override
    protected void updateOrderData(InputOrderDto inputOrderDto) {}

    @Override
    protected void synchronizeStockData(InputOrderDto inputOrderDto) throws Exception {}

    @Override
    protected void insertOrUpdateLogicalOrderGoods(InputOrderDto inputOrderDto) {}

}
