Vue.component('ui-step', {
    template: `<div class="ui-step-wrap">
        <div class="ui-step-list" :class="status">
            <div class="ui-step-item" :class="{active: item.active}" v-for="(item, index) in list" :key="index">
                <div class="ui-step-item-icon"></div>
                <div class="ui-step-item-txt">{{ item.label }}</div>
            </div>
        </div>
    </div>`,
    props: {
        list: {
            type: Array,
            default() {
                return [];
            }
        },
        active: {
            type: String | Number,
            default: ''
        },
        status: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
           
        }
    },
    created() {
        this.checkStepStatus();
    },
    methods: {
        checkStepStatus() {
            let stop = false;
            this.list.forEach(item => {
                if ((item.id != this.active && !stop) || item.id == this.active) {
                    item.active = true;

                    if (item.id == this.active) {
                        stop = true;
                    }
                }  
            });

            console.log(this.list, this.active)
        }
    }
})