package com.vedeng.erp.aftersale.buzlogic.create.Impl;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.aftersale.buzlogic.create.AfterSalesBuzLogic;
import com.wms.constant.CancelReasonConstant;
import com.wms.service.CancelTypeService;
import com.wms.service.LogicalSaleorderChooseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <b>Description:</b><br>
 * 类解释
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.erp.aftersale.buzlogic.create.Impl <br>
 * <b>ClassName:</b> ThAfterSalesBuzLogic <br>
 * <b>Date:</b> 2021/10/9 17:06 <br>
 */
@Service
public class ThAfterSalesBuzLogic extends AfterSalesBuzLogic {
    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Autowired
    private CancelTypeService cancelTypeService;
    /**
     * <b>Description:</b><br>
     * 取消wms出库任务
     *
     * @param
     * @return com.vedeng.common.model.ResultInfo
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/11 13:22
     */
    public ResultInfo cancelWms(AfterSalesVo afterSalesVo){
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");

        if (!cancelTypeService.cancelOutSaleOutMethod(afterSalesVo.getOrderNo(), CancelReasonConstant.AFTER_ORDER)) {
            resultInfo = new ResultInfo(-1, "物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 下发销售退货到wms
     *
     * @param afterSalesVo, user
     * @return void
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/11 13:49
     */
    public void putWms(AfterSalesVo afterSalesVo, User user){
        logicalSaleorderChooseService.putSaleAfterReturnStartGoods(afterSalesVo.getOrderNo(), user);
    }

    @Override
    public ResultInfo run(List<String> sequence,AfterSalesVo afterSalesVo,User user){
        super.init(sequence,afterSalesVo,user);
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        for (String methodName : getSequence()){
            switch (methodName) {
                case "cancelWms":
                    if(resultInfo.getCode().equals(0)) {
                        resultInfo = cancelWms(super.getAfterSalesVo());
                    }
                    break;
                case "saveAfterSale":
                    if(resultInfo.getCode().equals(0)) {
                        resultInfo = super.saveAfterSale(super.getAfterSalesVo(),super.getUser());
                    }
                    break;
                case "putWms":
                    if(resultInfo.getCode().equals(0)) {
                        this.putWms(super.getAfterSalesVo(),super.getUser());
                    }
                    break;
                default:
                    break;
            }
        }
        return resultInfo;
    }
}
