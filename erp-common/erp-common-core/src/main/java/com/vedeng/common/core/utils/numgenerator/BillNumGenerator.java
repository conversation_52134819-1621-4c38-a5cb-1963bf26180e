package com.vedeng.common.core.utils.numgenerator;


import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基于单据类型编号生成接口类
 * @date 2022/7/13 11:05
 */
public class BillNumGenerator  {

    private static final String PERSISTENCE_METHOD = "updateByPrimaryKey";

    private BillGeneratorBean generatorBean;

    /**
     * 分发，并获取
     * @param billGeneratorBean
     * @return
     */
    public String distribution(BillGeneratorBean billGeneratorBean) {

        BillType billType = billGeneratorBean.getBillType();
        NoGeneratorBean noGeneratorBean = billGeneratorBean.getNoGeneratorBean();
        if (noGeneratorBean == null) {
            noGeneratorBean = billType.getNoGeneratorBean();
        }
        noGeneratorBean.setPrefix(billType.getPrefix());
        NumGenerator numGenerator = billType.getNumGenerator();
        return numGenerator.generate(billGeneratorBean.getNoGeneratorBean());
    }


}
