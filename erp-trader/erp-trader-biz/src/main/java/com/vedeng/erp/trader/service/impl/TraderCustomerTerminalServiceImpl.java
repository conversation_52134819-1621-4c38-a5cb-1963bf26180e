package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.OrganizationApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.common.enums.TraderCustomerTerminalAuditEnum;
import com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalEntity;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingTerminalDto;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalAuditRecordDto;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalDto;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalQuery;
import com.vedeng.erp.trader.feign.OneDataTerminalInfoServiceApi;
import com.vedeng.erp.trader.mapper.TraderCustomerTerminalMapper;
import com.vedeng.erp.trader.mapstruct.TraderCustomerTerminalConvertor;
import com.vedeng.erp.trader.service.TraderCustomerMarketingApiService;
import com.vedeng.erp.trader.service.TraderCustomerTerminalApiService;
import com.vedeng.erp.trader.service.TraderCustomerTerminalAuditRecordService;
import com.vedeng.onedataapi.api.terminal.req.TerminalReqDto;
import com.vedeng.onedataapi.api.terminal.res.TerminalDataRes;
import com.vedeng.onedataapi.api.terminal.res.TerminalRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/4 19:14
 **/
@Service
@Slf4j
public class TraderCustomerTerminalServiceImpl implements TraderCustomerTerminalApiService {

    @Autowired
    private TraderCustomerTerminalConvertor traderCustomerTerminalConvertor;

    @Autowired
    private TraderCustomerTerminalMapper traderCustomerTerminalMapper;

    @Autowired
    private TraderCustomerTerminalAuditRecordService traderCustomerTerminalAuditRecordService;

    @Autowired
    private OrganizationApiService organizationApiService;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private TraderCustomerMarketingApiService traderCustomerMarketingApiService;

    @Autowired
    private OneDataTerminalInfoServiceApi oneDataTerminalInfoServiceApi;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addByList(List<TraderCustomerTerminalDto> traderCustomerTerminalDtoList) {

        log.info("addByList 入参：{}", JSON.toJSONString(traderCustomerTerminalDtoList));

        if (CollUtil.isEmpty(traderCustomerTerminalDtoList)) {
            return;
        }

        List<TraderCustomerTerminalEntity> traderCustomerTerminalEntities = traderCustomerTerminalConvertor.toEntity(traderCustomerTerminalDtoList);

        traderCustomerTerminalEntities.forEach(x->{

            List<TraderCustomerTerminalEntity> data = traderCustomerTerminalMapper.selectByTraderIdAndDwhTerminalId(x.getTraderId(), x.getDwhTerminalId());
            if (CollUtil.isNotEmpty(data)) {
                log.warn("当前提交终端:{},有已存在数据:{}，请刷新基本信息页后重新填写。",JSON.toJSONString(x),JSON.toJSONString(data));
                throw new ServiceException("当前提交终端有已存在数据，请刷新基本信息页后重新填写。");
            }
            x.setAuditStatus(TraderCustomerTerminalAuditEnum.DO_AUDIT.getStatus());
            traderCustomerTerminalMapper.insertSelective(x);
            TraderCustomerTerminalServiceImpl bean = SpringUtil.getBean(TraderCustomerTerminalServiceImpl.class);
            bean.doAudit(x);
        });
    }

    @Override
    public List<TraderCustomerTerminalDto> queryByTraderId(Integer traderId) {

        if (Objects.isNull(traderId)) {
            return Collections.emptyList();
        }

        List<TraderCustomerTerminalEntity> traderCustomerTerminalEntities = traderCustomerTerminalMapper.selectByTraderId(traderId);
        List<TraderCustomerTerminalDto> traderCustomerTerminalDtoList = traderCustomerTerminalConvertor.toDto(traderCustomerTerminalEntities);
        if (CollUtil.isNotEmpty(traderCustomerTerminalDtoList)) {
            traderCustomerTerminalDtoList.forEach(x->{
                if (TraderCustomerTerminalAuditEnum.REJECTED.getStatus().equals(x.getAuditStatus())) {
                    String desc = traderCustomerTerminalAuditRecordService.queryLastRejectDesc(x.getTraderCustomerTerminalId());
                    x.setRejectDesc(desc);
                }
            });
        }
        return traderCustomerTerminalDtoList;
    }

    @Override
    public PageInfo<TraderCustomerTerminalDto> page(PageParam<TraderCustomerTerminalQuery> query) {
        // 获取所有下级用户id
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        TraderCustomerTerminalQuery param = query.getParam();
        if (Objects.isNull(param.getUserId())) {
            // 查询顶级部门
            // 查询部门下所有的明细
            // 查询部门下所有的销售
            Integer topOrgIdByOrgId = organizationApiService.getTopOrgIdByOrgId(currentUser.getOrgId());
            List<Integer> orgAllSubId = organizationApiService.getOrgAllSubId(topOrgIdByOrgId);
            List<Integer> positionType = Collections.singletonList(310);
            List<UserDto> allUsers = userApiService.getUserByPositionTypeAndOrg(orgAllSubId, positionType, false);
            List<Integer> userIdList = allUsers.stream().filter(Objects::nonNull).map(UserDto::getUserId).collect(Collectors.toList());
            param.setUserIdList(userIdList);
        } else {
            param.setUserIdList(Collections.singletonList(param.getUserId()));
        }

        return PageHelper.startPage(query).doSelectPageInfo(() -> traderCustomerTerminalMapper.findByAll(param));
    }

    @Override
    public List<TraderCustomerTerminalDto> getAuditData(Integer traderId) {

        if (Objects.isNull(traderId)) {
            return Collections.emptyList();
        }
        List<TraderCustomerTerminalDto> traderCustomerTerminalDtos = traderCustomerTerminalMapper.selectByTraderIdAndTraderCustomerIdAndAuditStatus(traderId, TraderCustomerTerminalAuditEnum.DO_AUDIT.getStatus());
        // 分割 方便前端展示
        if (CollUtil.isNotEmpty(traderCustomerTerminalDtos)) {
            traderCustomerTerminalDtos.forEach(x->{
                if (StrUtil.isEmpty(x.getPicUrl())) {
                    return;
                }
                x.setPic2List(StrUtil.split(x.getPicUrl(),StrUtil.COMMA));
            });
        }
        return traderCustomerTerminalDtos;
    }

    @Override
    public TraderCustomerTerminalDto getData(Integer traderCustomerTerminalId) {

        Assert.notNull(traderCustomerTerminalId, "入参不可为空");

        TraderCustomerTerminalEntity traderCustomerTerminalEntity = traderCustomerTerminalMapper.selectByPrimaryKey(traderCustomerTerminalId);
        TraderCustomerTerminalDto traderCustomerTerminalDto = traderCustomerTerminalConvertor.toDto(traderCustomerTerminalEntity);

        if (Objects.nonNull(traderCustomerTerminalDto) && StrUtil.isNotEmpty(traderCustomerTerminalDto.getPicUrl())) {

            traderCustomerTerminalDto.setPic2List(StrUtil.split(traderCustomerTerminalDto.getPicUrl(), StrUtil.COMMA));
        }
        return traderCustomerTerminalDto;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(TraderCustomerTerminalDto traderCustomerTerminalDto) {

        // check 历史状态
        TraderCustomerTerminalEntity traderCustomerTerminalEntity = traderCustomerTerminalMapper.selectByPrimaryKey(traderCustomerTerminalDto.getTraderCustomerTerminalId());
        if (Objects.isNull(traderCustomerTerminalEntity) || !TraderCustomerTerminalAuditEnum.REJECTED.getStatus().equals(traderCustomerTerminalEntity.getAuditStatus())) {
            log.warn("此终端:{},审核状态已经改变请刷新页面重试",JSON.toJSONString(traderCustomerTerminalDto));
            throw new ServiceException("此终端审核状态已经改变请刷新页面重试");
        }
        TraderCustomerTerminalEntity update = traderCustomerTerminalConvertor.toEntity(traderCustomerTerminalDto);
        update.setModTime(new Date());
        update.setAuditStatus(TraderCustomerTerminalAuditEnum.DO_AUDIT.getStatus());
        traderCustomerTerminalMapper.updateByPrimaryKeySelective(update);
        TraderCustomerTerminalServiceImpl bean = SpringUtil.getBean(TraderCustomerTerminalServiceImpl.class);
        bean.doAudit(update);

    }

    /**
     * 记录发起审核
     * @param data 数据
     */
    @Transactional(rollbackFor = Throwable.class)
    public void doAudit(TraderCustomerTerminalEntity data) {
        TraderCustomerTerminalAuditRecordDto add = new TraderCustomerTerminalAuditRecordDto();
        add.setAuditStatus(TraderCustomerTerminalAuditEnum.DO_AUDIT.getStatus());
        add.setTraderCustomerId(data.getTraderCustomerId());
        add.setTraderId(data.getTraderId());
        add.setAuditUserId(data.getUpdater());
        add.setAuditUsername(data.getUpdaterName());
        add.setAuditTime(new Date());
        add.setTraderCustomerTerminalId(data.getTraderCustomerTerminalId());
        traderCustomerTerminalAuditRecordService.add(add);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void doPass(Integer traderCustomerTerminalId) {
        TraderCustomerTerminalEntity traderCustomerTerminalEntity = traderCustomerTerminalMapper.selectByPrimaryKey(traderCustomerTerminalId);

        if (Objects.isNull(traderCustomerTerminalEntity) || !TraderCustomerTerminalAuditEnum.DO_AUDIT.getStatus().equals(traderCustomerTerminalEntity.getAuditStatus())) {
            log.error("此终端:{},审核状态已经改变请刷新页面重试",traderCustomerTerminalId);
            throw new ServiceException("此终端审核状态已经改变请刷新页面重试");
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        TraderCustomerTerminalEntity update = new TraderCustomerTerminalEntity();
        update.setTraderCustomerTerminalId(traderCustomerTerminalId);
        update.setAuditStatus(TraderCustomerTerminalAuditEnum.PASS.getStatus());
        update.setModTime(new Date());
        traderCustomerTerminalMapper.updateByPrimaryKeySelective(update);

        TraderCustomerTerminalAuditRecordDto add = new TraderCustomerTerminalAuditRecordDto();
        add.setTraderCustomerId(traderCustomerTerminalEntity.getTraderCustomerId());
        add.setTraderId(traderCustomerTerminalEntity.getTraderId());
        add.setAuditTime(new Date());
        add.setAuditStatus(TraderCustomerTerminalAuditEnum.PASS.getStatus());
        add.setAuditUsername(currentUser.getUsername());
        add.setAuditUserId(currentUser.getId());
        add.setTraderCustomerTerminalId(traderCustomerTerminalEntity.getTraderCustomerTerminalId());

        traderCustomerTerminalAuditRecordService.add(add);

        //  合并

        String dwhTerminalId = traderCustomerTerminalEntity.getDwhTerminalId();
        if (StrUtil.isEmpty(dwhTerminalId)) {
            return;
        }

        TerminalReqDto query = new TerminalReqDto();
        query.setUniqueId(dwhTerminalId);
        //2：经营终端大类
        query.setQueryScene(2);
        TerminalDataRes data = null;
        try {
            RestfulResult<TerminalDataRes> terminalSearchDetail = oneDataTerminalInfoServiceApi.getTerminalSearchDetail(query);
            log.info("大数据终端接口,getTerminalSearchDetail,查询结果：{}",JSON.toJSONString(terminalSearchDetail));
            if (terminalSearchDetail.isSuccess()) {
                data = terminalSearchDetail.getData();
            }
        } catch (Exception e) {
            log.error("大数据终端接口,getTerminalSearchDetail,查询异常，请联系研发部");
            throw new ServiceException("大数据终端接口查询异常，请联系研发部");
        }

        if (Objects.isNull(data) || CollUtil.isEmpty(data.getList())) {
            log.info("大数据终端接口未查询到数据,uniqueId:{}",dwhTerminalId);
            return;
        }
        TerminalRes terminalRes = data.getList().get(0);
        TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto = new TraderCustomerMarketingTerminalDto();

        if (StrUtil.isEmpty(terminalRes.getHosTerminalType())) {
            log.info("大数据终端接口查询到数据异常,uniqueId:{}",dwhTerminalId);
            return;
        }
        int marketType = getTraderCustomerMarketingType(terminalRes.getHosTerminalType());
        if (marketType == 6) {
            log.info("大数据终端接口查询到数据异常,uniqueId:{}",dwhTerminalId);
            return;
        }

        traderCustomerMarketingTerminalDto.setTraderCustomerMarketingType(marketType);
        traderCustomerMarketingTerminalDto.setInstitutionType(terminalRes.getHosType());
        traderCustomerMarketingTerminalDto.setInstitutionNature(terminalRes.getHosModel());
        traderCustomerMarketingTerminalDto.setInstitutionLevel(terminalRes.getHosLevel());
        traderCustomerMarketingTerminalDto.setInstitutionTypeChild(terminalRes.getHosSmallType());
        traderCustomerMarketingTerminalDto.setTraderCustomerId(traderCustomerTerminalEntity.getTraderCustomerId());
        traderCustomerMarketingTerminalDto.setTraderId(traderCustomerTerminalEntity.getTraderId());
        traderCustomerMarketingApiService.mergeNewData(traderCustomerMarketingTerminalDto);


    }


    public Integer getTraderCustomerMarketingType(String typeName){
        //0 等级医院 1 基层医疗 2 应急医疗机构 3 专业公共卫生机构 4 其他医疗机构 5 非医疗机构
        switch (typeName){
            case "等级医院": return 0;
            case "基层医疗": return 1;
            case "应急医疗机构": return 2;
            case "专业公共卫生机构": return 3;
            case "其他医疗机构": return 4;
            case "非医疗机构": return 5;
            default: return 6;
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void doReject(Integer traderCustomerTerminalId, String desc) {
        TraderCustomerTerminalEntity traderCustomerTerminalEntity = traderCustomerTerminalMapper.selectByPrimaryKey(traderCustomerTerminalId);

        if (Objects.isNull(traderCustomerTerminalEntity) || !TraderCustomerTerminalAuditEnum.DO_AUDIT.getStatus().equals(traderCustomerTerminalEntity.getAuditStatus())) {
            throw new ServiceException("此终端审核状态已经改变请刷新页面重试");
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        TraderCustomerTerminalEntity update = new TraderCustomerTerminalEntity();
        update.setTraderCustomerTerminalId(traderCustomerTerminalId);
        update.setAuditStatus(TraderCustomerTerminalAuditEnum.REJECTED.getStatus());
        update.setModTime(new Date());
        traderCustomerTerminalMapper.updateByPrimaryKeySelective(update);

        TraderCustomerTerminalAuditRecordDto add = new TraderCustomerTerminalAuditRecordDto();
        add.setTraderCustomerId(traderCustomerTerminalEntity.getTraderCustomerId());
        add.setTraderId(traderCustomerTerminalEntity.getTraderId());
        add.setAuditTime(new Date());
        add.setAuditStatus(TraderCustomerTerminalAuditEnum.REJECTED.getStatus());
        add.setComments(desc);
        add.setAuditUsername(currentUser.getUsername());
        add.setAuditUserId(currentUser.getId());
        add.setTraderCustomerTerminalId(traderCustomerTerminalEntity.getTraderCustomerTerminalId());

        traderCustomerTerminalAuditRecordService.add(add);

    }
}
