package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleReturnStockDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleReturnStockDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售退货单 entity 、dto 互转
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeSaleBackConvertor extends BaseMapStruct<KingDeeSaleReturnStockEntity, KingDeeSaleReturnStockDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillTypeId", source = "FBillTypeID")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FQzokBddjtid", source = "FQzokBddjtid")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FSaleOrgId", source = "FSaleOrgId")
    @Mapping(target = "FStockOrgId", source = "FStockOrgId")
    @Mapping(target = "FRetcustId", source = "FRetcustId")
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "expensesToJsonArray")
    @Override
    KingDeeSaleReturnStockEntity toEntity(KingDeeSaleReturnStockDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillTypeID", source = "FBillTypeId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FQzokBddjtid", source = "FQzokBddjtid")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FSaleOrgId", source = "FSaleOrgId")
    @Mapping(target = "FStockOrgId", source = "FStockOrgId")
    @Mapping(target = "FRetcustId", source = "FRetcustId")
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "expensesJsonArrayToList")
    @Override
    KingDeeSaleReturnStockDto toDto(KingDeeSaleReturnStockEntity entity);
	
    
    /**
     * entity 中JSONArray 转 原对象
     *
     * @param kingDeeSaleReturnStockDetailDto JSONArray
     * @return List<KingDeeSaleReturnStockDetailDto> dto中的对象
     */
    @Named("expensesJsonArrayToList")
    default List<KingDeeSaleReturnStockDetailDto> entryJsonArrayToList(JSONArray kingDeeSaleReturnStockDetailDto) {
        if (CollUtil.isEmpty(kingDeeSaleReturnStockDetailDto)) {
            return Collections.emptyList();
        }
        return kingDeeSaleReturnStockDetailDto.toJavaList(KingDeeSaleReturnStockDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("expensesToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeeSaleReturnStockDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
    
}
