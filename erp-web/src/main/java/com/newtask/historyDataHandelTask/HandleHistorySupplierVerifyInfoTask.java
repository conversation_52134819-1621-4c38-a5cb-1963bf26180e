package com.newtask.historyDataHandelTask;

import com.task.service.VerifyInfoTaskService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@JobHandler(value = "HandleHistoryVerifyInfoTask")
@Slf4j
@Deprecated
public class HandleHistorySupplierVerifyInfoTask extends AbstractJobHandler {

    @Autowired
    VerifyInfoTaskService verifyInfoTaskService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("开始处理供应商历史审批数据");
        log.info("开始处理供应商历史审批数据");
        Integer result =  verifyInfoTaskService.handleHistorySupplierVerifyInfo();
        XxlJobLogger.log("结束处理供应商历史审批数据");
        log.info("结束处理供应商历史审批数据");
        return SUCCESS;
    }
}
