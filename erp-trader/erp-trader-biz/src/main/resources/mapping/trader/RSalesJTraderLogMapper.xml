<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.RSalesJTraderLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.RSalesJTraderLogEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_SALES_J_TRADER_LOG-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="SALE_USER_ID" jdbcType="INTEGER" property="saleUserId" />
    <result column="SALE_USER_NAME" jdbcType="VARCHAR" property="saleUserName" />
    <result column="OPERATE_TYPE" jdbcType="VARCHAR" property="operateType" />
    <result column="IS_DELETED" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, TRADER_ID, 
    TRADER_CUSTOMER_ID, SALE_USER_ID, SALE_USER_NAME, OPERATE_TYPE, IS_DELETED
  </sql>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJTraderLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_SALES_J_TRADER_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="saleUserId != null">
        SALE_USER_ID,
      </if>
      <if test="saleUserName != null and saleUserName != ''">
        SALE_USER_NAME,
      </if>
      <if test="operateType != null and operateType != ''">
        OPERATE_TYPE,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="saleUserId != null">
        #{saleUserId,jdbcType=INTEGER},
      </if>
      <if test="saleUserName != null and saleUserName != ''">
        #{saleUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null and operateType != ''">
        #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>