package com.vedeng.erp.buyorder.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * T_BUYORDER
 * <AUTHOR>
@Data
public class Buyorder implements Serializable {
    private Integer buyorderId;

    /**
     * 采购单号
     */
    private String buyorderNo;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 订单类型0销售订单采购1备货订单采购
     */
    private Integer orderType;

    /**
     * 部门ID
     */
    private Integer orgId;

    /**
     * 归属ERP用户ID
     */
    private Integer userId;

    /**
     * 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Long validTime;

    /**
     * 订单状态：0待确认（默认）、1进行中、2已完结、3已关闭
     */
    private Integer status;

    /**
     * 锁定状态0未锁定 1已锁定
     */
    private Integer lockedStatus;

    /**
     * 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer invoiceStatus;

    /**
     * 收票时间
     */
    private Long invoiceTime;

    /**
     * 付款状态 0未付款 1部分付款 2全部付款
     */
    private Integer paymentStatus;

    /**
     * 付款时间
     */
    private Long paymentTime;

    /**
     * 供应商发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    private Long deliveryTime;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 收货时间
     */
    private Long arrivalTime;

    /**
     * 售后状态 0无 1售后中 2售后完成 3售后关闭
     */
    private Integer serviceStatus;

    /**
     * 含有账期支付 0无 1有
     */
    private Integer haveAccountPeriod;

    /**
     * 是否直发 0否 1是
     */
    private Integer deliveryDirect;

    /**
     * 总额
     */
    private BigDecimal totalAmount;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 手机
     */
    private String traderContactMobile;

    /**
     * 电话
     */
    private String traderContactTelephone;

    /**
     * 联系地址ID
     */
    private Integer traderAddressId;

    /**
     * 供应商地区
     */
    private String traderArea;

    /**
     * 联系详细地址(含省市区)
     */
    private String traderAddress;

    /**
     * 供应商备注
            
     */
    private String traderComments;

    /**
     * 收货公司ID
     */
    private Integer takeTraderId;

    /**
     * 收货公司名称
     */
    private String takeTraderName;

    /**
     * 收货联系人ID
     */
    private Integer takeTraderContactId;

    /**
     * 收货联系人名称
     */
    private String takeTraderContactName;

    /**
     * 收货联系人手机
     */
    private String takeTraderContactMobile;

    /**
     * 收货联系人电话
     */
    private String takeTraderContactTelephone;

    /**
     * 收货地址ID
     */
    private Integer takeTraderAddressId;

    /**
     * 收货地区
     */
    private String takeTraderArea;

    /**
     * 收货地址
     */
    private String takeTraderAddress;

    /**
     * 付款方式 字典库
     */
    private Integer paymentType;

    /**
     * 预付金额
     */
    private BigDecimal prepaidAmount;

    /**
     * 账期支付金额
     */
    private BigDecimal accountPeriodAmount;

    /**
     * 账期天数
     */
    private Integer periodDay;

    /**
     * 尾款
     */
    private BigDecimal retainageAmount;

    /**
     * 尾款期限(月)
     */
    private Integer retainageAmountMonth;

    /**
     * 物流公司ID
     */
    private Integer logisticsId;

    /**
     * 发票类型 字典表
     */
    private Integer invoiceType;

    /**
     * 运费说明 字典表
     */
    private Integer freightDescription;

    /**
     * 付款备注
     */
    private String paymentComments;

    /**
     * 物流备注
     */
    private String logisticsComments;

    /**
     * 开票备注
     */
    private String invoiceComments;

    /**
     * 内部备注
     */
    private String comments;

    /**
     * 附加条款
     */
    private String additionalClause;

    /**
     * 审核备注(关闭原因) 字典库
     */
    private Integer statusComments;

    /**
     * 满足发货时间
     */
    private Long satisfyDeliveryTime;

    /**
     * 预计到货时间
     */
    private String estimateArrivalTime;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 采购单关联业务更新时间
     */
    private Date updateDataTime;

    /**
     * 订单实付金额(包括账期,减去了订单实退金额)
     */
    private BigDecimal realPayAmount;

    /**
     * 订单实退金额(银行流水)
     */
    private BigDecimal realReturnAmount;

    /**
     * 订单实际金额(减去了售后退货金额)
     */
    private BigDecimal realTotalAmount;

    /**
     * 订单未还账期金额
     */
    private BigDecimal nopaybackAmount;

    /**
     * 订单实际收票金额
     */
    private Long realInvoiceAmount;

    /**
     * 是否风控 0否 1是 3完成风控
     */
    private Integer isRisk;

    /**
     * 风控内容
     */
    private String riskComments;

    /**
     * 风控时间
     */
    private Long riskTime;

    /**
     * 1-临期 2-逾期 （主要是为了采购单列表搜索）
     */
    private Integer expeditingStatus;

    /**
     * 催货跟进状态，1未跟进，2部分跟进，3全部跟进
     */
    private Integer expeditingFollowStatus;

    /**
     * 新的采购单流程-资质自动审核
     */
    private Integer newFlow;

    /**
     * 订单子状态：1待确认、2审核中、3待收款、4待发货、5待收货、6待开票、7已完结、8已关闭
     */
    private String subStatus;

    /**
     * 订单审核状态：3待审核、0审核中、1审核通过、2审核不通过
     */
    private Integer verifyStatus;

    /**
     * 是否订单流订单 0否 1是
     */
    private Byte isNew;

    /**
     * 关联的销售单号集合，逗号隔开
     */
    private String saleorderNos;

    /**
     * 采购订单合同url
     */
    private String contractUrl;

    /**
     * 直发采购单是否被催办维护同行单批次信息，0否，1是
     */
    private Byte urgedMaintainBatchInfo;

    private List<PeerListBuyorderGoods> needEditBuyGoods;

    private static final long serialVersionUID = 1L;



}