package com.vedeng.common.core.listenerEvent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 客户账期event
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TraderBillPeriodEvent {
    private Integer invoiceId;
    private Integer saleOrderId;
    private BigDecimal amount;
}
