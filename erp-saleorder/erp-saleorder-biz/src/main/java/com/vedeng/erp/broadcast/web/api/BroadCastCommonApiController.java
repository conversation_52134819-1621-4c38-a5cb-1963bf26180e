package com.vedeng.erp.broadcast.web.api;

import com.common.dto.SelectDto;
import com.common.dto.TreeDto;
import com.github.pagehelper.PageHelper;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.broadcast.domain.dto.BroadcastDeptDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptMapper;
import com.vedeng.erp.broadcast.service.BroadcastDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 到款通知配置  前端路由
 */
@RestController
@RequestMapping("/broadcast/common")
public class BroadCastCommonApiController {

    @Autowired
    BroadcastDeptMapper broadcastDeptMapper;


    @Resource
    BroadcastDeptService broadcastDeptService;


    /**
     * 获取一级通知部门&&根据一级通知部门获取小组
     * @return JSON
     */
    @ResponseBody
    @RequestMapping("/listDept")
    @NoNeedAccessAuthorization
    public R<List<SelectDto>> listRootDept(BroadcastDeptDto param) {
        PageHelper.startPage(1,100);
        List<BroadcastDeptEntity> list=broadcastDeptService.getBroadcastDeptListByParentId(param.getParentId(),param.getDeptName());
        if (list == null || list.isEmpty()) {
            return R.success("没有数据", new ArrayList<>());
        }
       // list转成selectdto
        List<SelectDto> result = list.stream().map(sku -> {
            SelectDto map = new SelectDto(sku.getId()+"",sku.getDeptName());
            return map;
        }).collect(Collectors.toList());
        return R.success(result);
    }
//
    /**
     * 获取一级通知部门+小组树形结构
     * @return JSON
     */
    @ResponseBody
    @RequestMapping("/treeDept")
    @NoNeedAccessAuthorization
    public R<List<TreeDto>> treeDept( BroadcastDeptDto param) {
        List<BroadcastDeptEntity>  allDepts=broadcastDeptService.getBroadcastDeptListAll();
        // 组装树形结构
        List<TreeDto> rootNodes = buildDepartmentTree(allDepts);

        return R.success(rootNodes);
    }
    /**
     * 构建部门树形结构
     * @param allDepts 所有部门数据
     * @return 根节点列表
     */
    private List<TreeDto> buildDepartmentTree(List<BroadcastDeptEntity> allDepts) {
        if (allDepts == null || allDepts.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 1. 先将所有部门转换为TreeDto，并建立id到TreeDto的映射
        Map<Integer, TreeDto> deptMap = new HashMap<>();
        Map<Integer, Integer> parentIdMap = new HashMap<>(); // 临时存储parentId关系
        
        for (BroadcastDeptEntity dept : allDepts) {
            TreeDto treeDto = new TreeDto();
            treeDto.setValue(dept.getId().toString());
            treeDto.setLabel(dept.getDeptName());
            treeDto.setChildren(new ArrayList<>());
            
            deptMap.put(dept.getId(), treeDto);
            parentIdMap.put(dept.getId(), dept.getParentId());
        }
        
        // 2. 通过parentId建立父子关系
        List<TreeDto> rootNodes = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : parentIdMap.entrySet()) {
            Integer deptId = entry.getKey();
            Integer parentId = entry.getValue();
            TreeDto currentNode = deptMap.get(deptId);
            if (parentId == null || parentId == 0) {
                // 根节点
                rootNodes.add(currentNode);
            } else {
                // 子节点，添加到父节点的children中
                TreeDto parent = deptMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(currentNode);
                }
            }
        }
        return rootNodes;
    }









}
