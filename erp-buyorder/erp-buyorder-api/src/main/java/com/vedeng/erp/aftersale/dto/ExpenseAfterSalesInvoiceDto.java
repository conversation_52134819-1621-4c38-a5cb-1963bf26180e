package com.vedeng.erp.aftersale.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * T_EXPENSE_AFTER_SALES_INVOICE
 *
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
public class ExpenseAfterSalesInvoiceDto extends BaseDto {
    /**
     * 主键
     */
    private Long expenseAfterSalesInvoiceId;

    /**
     * 费用售后表主键ID
     */
    private Long expenseAfterSalesId;

    /**
     * 费用单明细表主键ID
     */
    private Integer buyorderExpenseItemId;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 本次退票数
     */
    private BigDecimal returnNum;

    /**
     * 是否需退票 0否 1是
     */
    private Integer isRefundInvoice;

    /**
     * 退票状态 0未退票 1已退票
     */
    private Integer returnInvoiceStatus;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新备注
     */
    private String updateRemark;

}