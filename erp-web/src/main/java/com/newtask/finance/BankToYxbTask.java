package com.newtask.finance;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.system.domain.dto.BankDto;
import com.vedeng.erp.system.domain.dto.BankQueryDto;
import com.vedeng.erp.system.service.BankService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/15 13:00
 **/
@JobHandler("BankToYxbTask")
@Component
@Slf4j
public class BankToYxbTask extends AbstractJobHandler {


    @Autowired
    private BankService bankService;
    /**
     * {
     *   "startTime": "2023-05-18 10:30:00",
     *   "endTime": "2023-05-18 12:00:00"
     * }
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        //任务开始日志
        log.info("BankToYxbTask erp 联行号同步yxb,param:{}" , param);
        //如果传入的开始时间和结束时间不为空，则获取传入的开始时间和结束时间
        if (!StrUtil.isEmpty(param)) {
            JSONObject jsonObject = JSON.parseObject(param);
            String startTimeStr = jsonObject.getString("startTime");
            String endTimeStr = jsonObject.getString("endTime");
            Date startTime = DateUtil.parse(startTimeStr);
            Date endTime = DateUtil.parse(endTimeStr);
            pushBank(startTime, endTime);
            //任务结束日志
            log.info("BankToYxbTask erp 联行号同步yxb任务结束");
            return ReturnT.SUCCESS;
        }
        //获取开始时间和结束时间，为前一天的开始时间和结束时间
        DateTime start = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -7));
        DateTime end = DateUtil.endOfDay(new Date());
        pushBank(start, end);
        log.info("BankToYxbTask erp 联行号同步yxb任务结束");

        return ReturnT.SUCCESS;
    }

    private void pushBank(Date startTime, Date endTime) {
        int pageSize = 100;
        int pageNum = 1;
        PageParam<BankQueryDto> param = new PageParam<>();
        BankQueryDto bankQueryDto = new BankQueryDto();
        bankQueryDto.setStart(startTime);
        bankQueryDto.setEnd(endTime);

        while (true) {
            param.setParam(bankQueryDto);
            param.setPageNum(pageNum);
            param.setPageSize(pageSize);
            PageInfo<BankDto> bankDtoPageInfo = bankService.selectByTime(param);

            List<BankDto> list = bankDtoPageInfo.getList();
            if (list.isEmpty()) {
                break;
            }
            try {
                bankService.doPostYxb(list);
            } catch (Exception e) {
                log.error("联行号同步医修帮失败,",e);
            }

            if (!bankDtoPageInfo.isHasNextPage()) {
                break;

            }

            pageNum = bankDtoPageInfo.getNextPage();

        }




    }
}
