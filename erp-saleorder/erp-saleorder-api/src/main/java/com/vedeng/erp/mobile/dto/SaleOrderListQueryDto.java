package com.vedeng.erp.mobile.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class SaleOrderListQueryDto {

    /**
     * 搜索关键字(订单号或客户名称)
     */
    private String searchKey;

    /**
     * 订单状态筛选(0待确认、1待审核、2待收款、3待发货、4待收货、5待开票、6已完结、7已关闭)
     */
    private List<Integer> orderStatusList;

    /**
     * 页数
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;

    /**
     * 当前登录用户的userId
     */
    private List<Integer> currentOrgIdList;

    /**
     * 当前登录用户的userId
     */
    private List<Integer> currentUserIdList;

}
