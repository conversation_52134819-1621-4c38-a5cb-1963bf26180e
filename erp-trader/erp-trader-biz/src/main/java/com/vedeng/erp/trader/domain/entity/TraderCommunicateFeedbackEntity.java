package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 沟通记录摘要意见反馈
 */
@Getter
@Setter
public class TraderCommunicateFeedbackEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long traderCommunicateFeedbackId;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 意见反馈
     */
    private String feedback;
}