<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceDetailEntity">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE_DETAIL-->
    <id column="INVOICE_DETAIL_ID" jdbcType="INTEGER" property="invoiceDetailId" />
    <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailgoodsId" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
    <result column="CHANGED_GOODS_NAME" jdbcType="VARCHAR" property="changedGoodsName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INVOICE_DETAIL_ID, INVOICE_ID, DETAILGOODS_ID, PRICE, NUM, TOTAL_AMOUNT, CHANGED_GOODS_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_INVOICE_DETAIL
    where INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_INVOICE_DETAIL
    where INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="INVOICE_DETAIL_ID" keyProperty="invoiceDetailId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_DETAIL (INVOICE_ID, DETAILGOODS_ID, PRICE, 
      NUM, TOTAL_AMOUNT, CHANGED_GOODS_NAME
      )
    values (#{invoiceId,jdbcType=INTEGER}, #{detailgoodsId,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, 
      #{num,jdbcType=DECIMAL}, #{totalAmount,jdbcType=DECIMAL}, #{changedGoodsName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="INVOICE_DETAIL_ID" keyProperty="invoiceDetailId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      <if test="detailgoodsId != null">
        DETAILGOODS_ID,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
      <if test="changedGoodsName != null">
        CHANGED_GOODS_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="detailgoodsId != null">
        #{detailgoodsId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="changedGoodsName != null">
        #{changedGoodsName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceDetailEntity">
    <!--@mbg.generated-->
    update T_INVOICE_DETAIL
    <set>
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="detailgoodsId != null">
        DETAILGOODS_ID = #{detailgoodsId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="changedGoodsName != null">
        CHANGED_GOODS_NAME = #{changedGoodsName,jdbcType=VARCHAR},
      </if>
    </set>
    where INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceDetailEntity">
    <!--@mbg.generated-->
    update T_INVOICE_DETAIL
    set INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      DETAILGOODS_ID = #{detailgoodsId,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=DECIMAL},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      CHANGED_GOODS_NAME = #{changedGoodsName,jdbcType=VARCHAR}
    where INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="INVOICE_DETAIL_ID" keyProperty="invoiceDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_DETAIL
    (INVOICE_ID, DETAILGOODS_ID, PRICE, NUM, TOTAL_AMOUNT, CHANGED_GOODS_NAME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.invoiceId,jdbcType=INTEGER}, #{item.detailgoodsId,jdbcType=INTEGER}, #{item.price,jdbcType=DECIMAL}, 
        #{item.num,jdbcType=DECIMAL}, #{item.totalAmount,jdbcType=DECIMAL}, #{item.changedGoodsName,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="queryAllByInvoiceId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_INVOICE_DETAIL
    WHERE INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
  </select>

  <select id="querySumDetailByInvoiceIds" resultType="com.vedeng.erp.finance.dto.InvoiceDetailDto">
    SELECT
        SUM(NUM) AS NUM,
        SUM(TOTAL_AMOUNT) AS TOTAL_AMOUNT,
        DETAILGOODS_ID
    FROM T_INVOICE_DETAIL
    WHERE INVOICE_ID IN
    <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
      #{invoiceId,jdbcType=INTEGER}
    </foreach>
    GROUP BY DETAILGOODS_ID
  </select>

  <select id="queryDetailByInvoiceInfo" resultType="com.vedeng.erp.finance.dto.InvoiceDetailDto">
    SELECT
    A.*
    FROM T_INVOICE_DETAIL A
    JOIN T_INVOICE B ON A.INVOICE_ID = B.INVOICE_ID
    WHERE
        A.DETAILGOODS_ID = #{detailGoodsId,jdbcType=INTEGER}
        AND B.INVOICE_NO = #{invoiceNo,jdbcType=INTEGER}
        AND B.COLOR_TYPE = 2
        AND B.IS_ENABLE = 1
        LIMIT 1
  </select>

  <select id="getAllValidInvoiceDetailByInvoiceIds" resultMap="BaseResultMap">
    SELECT
    *
    FROM
    T_INVOICE_DETAIL
    WHERE
    INVOICE_ID IN
    <foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
      #{invoiceId,jdbcType=INTEGER}
    </foreach>
  </select>
    <select id="findByInvoiceIdAndDetailGoodsId" resultType="com.vedeng.erp.finance.dto.InvoiceDetailDto">
      SELECT
        *
      FROM
        T_INVOICE_DETAIL
      WHERE
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
        AND DETAILGOODS_ID = #{detailGoodsId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-10-31-->
  <select id="findByInvoiceIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_INVOICE_DETAIL
    where INVOICE_ID in
    <foreach item="item" index="index" collection="invoiceIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="findRealInvoiceNum" resultType="com.vedeng.erp.finance.dto.InvoiceDetailDto">

    select
    SUM(
     (IFNULL( CASE WHEN a.IS_ENABLE = 1 AND a.COLOR_TYPE = 2 THEN b.NUM ELSE - b.NUM END, 0 ))
    ) AS num
    ,b.DETAILGOODS_ID
    from T_INVOICE a left join T_INVOICE_DETAIL b on a.INVOICE_ID = b.INVOICE_ID
    where b.DETAILGOODS_ID in
    <foreach collection="salesOrderDetailIds" item="item" open="(" close=")" separator="," >
      #{item,jdbcType=INTEGER}
    </foreach>
    and a.TYPE = 505
    and a.VALID_STATUS =1
    group by b.DETAILGOODS_ID
  </select>

  <select id="getInvoicedTaxNum" resultType="java.util.Map">
    SELECT
    ROUND(SUM(
    CASE WHEN A.VALID_STATUS = 1 THEN (IFNULL( CASE WHEN I.IS_ENABLE = 1 AND I.COLOR_TYPE = 2 THEN D.NUM ELSE - D.NUM END, 0 )) ELSE 0 END
    ),2) AS INVOICE_NUM,
    ROUND(SUM(
    CASE WHEN A.VALID_STATUS = 1 THEN (IFNULL( D.TOTAL_AMOUNT, 0 )) ELSE 0 END
    ),2) AS INVOICE_AMOUNT,
    G.GOODS_NAME,
    G.SALEORDER_GOODS_ID,
    G.SKU
    FROM
    T_SALEORDER_GOODS G
    LEFT JOIN T_INVOICE_DETAIL D ON D.DETAILGOODS_ID = G.SALEORDER_GOODS_ID
    LEFT JOIN T_SALEORDER S ON G.SALEORDER_ID = S.SALEORDER_ID
    LEFT JOIN T_INVOICE I ON I.INVOICE_ID = D.INVOICE_ID
    LEFT JOIN T_INVOICE_APPLY A ON I.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
    WHERE
    1 = 1
    <choose>
      <when test="saleorderId != null">
        AND S.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
      </when>
      <otherwise>
        AND S.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
      </otherwise>
    </choose>
    GROUP BY
    SALEORDER_GOODS_ID
  </select>

  <select id="getInvoicedDataOld" resultType="java.util.Map">
    SELECT
    ROUND(SUM(IFNULL( (CASE
    WHEN I.IS_ENABLE = 1
    AND I.COLOR_TYPE = 2
    AND  I.INVOICE_ID IS NOT NULL  THEN D.NUM
    WHEN (I.IS_ENABLE <![CDATA[ <> ]]> 1
    OR I.COLOR_TYPE <![CDATA[ <> ]]> 2 )
    AND  I.INVOICE_ID IS NOT NULL  THEN -D.NUM
    ELSE 0
    END), 0 )),2) AS INVOICE_NUM,
    ROUND( SUM( IFNULL( CASE WHEN I.INVOICE_ID IS NOT NULL THEN D.TOTAL_AMOUNT ELSE 0 END, 0 ) ), 2 ) AS INVOICE_AMOUNT,
    G.GOODS_NAME,
    G.SALEORDER_GOODS_ID,
    G.SKU
    FROM
    T_SALEORDER_GOODS G
    LEFT JOIN T_INVOICE_DETAIL D ON D.DETAILGOODS_ID = G.SALEORDER_GOODS_ID
    LEFT JOIN T_SALEORDER S ON G.SALEORDER_ID = S.SALEORDER_ID
    LEFT JOIN T_INVOICE I ON I.INVOICE_ID = D.INVOICE_ID  AND I.INVOICE_APPLY_ID IS NULL AND I.TYPE=505
    WHERE
    1 = 1
    <choose>
      <when test="saleorderId != null">
        AND S.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
      </when>
      <otherwise>
        AND S.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
      </otherwise>
    </choose>
    GROUP BY
    SALEORDER_GOODS_ID
  </select>

  <select id="getAppliedTaxNum" resultType="java.util.Map">
    SELECT
    ROUND( IFNULL( SUM( D.NUM ), 0 ), 2 ) AS APPLY_NUM,
    ROUND( IFNULL( SUM( D.TOTAL_AMOUNT ), 0 ), 2 ) AS TOTAL_AMOUNT,
    G.SALEORDER_GOODS_ID AS SALEORDER_GOODS_ID,
    G.SKU,
    G.GOODS_NAME
    FROM
    T_SALEORDER_GOODS G
    LEFT JOIN T_SALEORDER S ON S.SALEORDER_ID = G.SALEORDER_ID
    LEFT JOIN T_INVOICE_APPLY A ON S.SALEORDER_ID = A.RELATED_ID
    AND ((A.ADVANCE_VALID_STATUS = 0 AND A.VALID_STATUS = 0) OR (A.ADVANCE_VALID_STATUS = 1 AND A.VALID_STATUS = 0)) <!-- 提前开票审核中和开票审核中 -->
    LEFT JOIN T_INVOICE_APPLY_DETAIL D ON G.SALEORDER_GOODS_ID = D.DETAILGOODS_ID
    AND D.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
    WHERE
    1 = 1
    <choose>
      <when test="saleorderId != null">
        AND S.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
      </when>
      <otherwise>
        AND S.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
      </otherwise>
    </choose>
    GROUP BY
    G.SALEORDER_GOODS_ID
  </select>
  
  <select id="queryInvoiceGoods" resultType="com.vedeng.erp.finance.dto.InvoiceGoodsDto">
    select c.SKU, b.NUM, b.TOTAL_AMOUNT, a.AMOUNT, a.INVOICE_TYPE, a.RATIO
    from T_INVOICE a
           left join T_INVOICE_DETAIL b on a.INVOICE_ID = b.INVOICE_ID
           left join T_SALEORDER_GOODS c on b.DETAILGOODS_ID = c.SALEORDER_GOODS_ID
    where a.TYPE = 505
      and a.IS_ENABLE = 1
      and c.IS_DELETE = 0
      and a.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
  </select>
  
  <select id="queryInvoiceNo" resultType="java.lang.String">
    select a.INVOICE_NO
    from T_INVOICE a
           left join T_SALEORDER b on a.RELATED_ID = b.SALEORDER_ID
    where a.TYPE = 505
      and a.TAG = 1
      and COLOR_TYPE = 2
      and INVOICE_PROPERTY = 3
      and b.SALEORDER_NO =  #{saleorderNo,jdbcType=VARCHAR}
  </select>
</mapper>
