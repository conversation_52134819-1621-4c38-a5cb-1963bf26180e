package com.vedeng.flash.model;

import lombok.Data;


@Data
public class RegularSnapshot extends RegularOperateLog{
    /**
     * 快照主键ID
     */
    private Integer snapshotId;

    /**
     * 定品状态
     */
    private Integer skuStatus;

    /**
     * 定品状态
     */
    private String skuStatusStr;

    /**
     * 最后一次加入定品池时间
     */
    private Long lastAddTime;

    /**
     * 最后一次加入定品池时间
     */
    private String lastAddTimeStr;

    /**
     * 快照日期
     */
    private String snapshotTime;

    /**
     * 快照创建日期
     */
    private Long addTime;
}
