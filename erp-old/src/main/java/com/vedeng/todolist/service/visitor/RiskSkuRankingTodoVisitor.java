package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.util.DateUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.PersonalTodoDealingInfo;
import com.vedeng.todolist.dto.RankingDto;
import com.vedeng.todolist.dto.SupplyChainAdminTodoDtoBuild;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理者页面->风控SKU排名
 */
@Service
public class RiskSkuRankingTodoVisitor implements TodoVisitor<SupplyChainAdminTodoDtoBuild>{

    @Resource
    private TodoListMapper todoListMapper;

    @Resource
    private UserMapper userMapper;

    @Override
    public void visitor(SupplyChainAdminTodoDtoBuild todoDtoBuild) {

        long firstDayTimestamp = DateUtil.getFirstDayofMonth();

        //查询历史代办+当月已完成代办 按照处理人分组 按照平均处理时间排序
        List<PersonalTodoDealingInfo> personalTodoDealingInfoList = todoListMapper.getAllRiskCheckSkuTodoListGroupByAssitId(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(),firstDayTimestamp);

        if(CollectionUtils.isEmpty(personalTodoDealingInfoList)){
            return;
        }

        List<RankingDto> rankingDtoList = new ArrayList<>();

        List<Integer> userIdList = new ArrayList<>(personalTodoDealingInfoList.stream().map(info->info.getUserId()).collect(Collectors.toSet()));

        Map<Integer, User> userMap = this.userMapper.getPurchaseUserListByUserIdList(userIdList).stream().collect(Collectors.toMap(k->k.getUserId(),v->v, (k,v) -> v));

        for(int i = 0;i < personalTodoDealingInfoList.size(); i++){

            RankingDto rankingDto = new RankingDto();
            rankingDtoList.add(rankingDto);

            rankingDto.setHandler(getUserName(userMap,personalTodoDealingInfoList.get(i).getUserId()));
            rankingDto.setGroupName(getUserDepartment(userMap,personalTodoDealingInfoList.get(i).getUserId()));
            rankingDto.setAvgDealTime(new BigDecimal(personalTodoDealingInfoList.get(i).getTotalDealTime() / (personalTodoDealingInfoList.get(i).getNum() * 1000 * 60 * 60.0))
                                                    .setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        }

        rankingDtoList = rankingDtoList.stream().sorted((r1, r2) ->  Double.valueOf(r1.getAvgDealTime()).compareTo(Double.valueOf(r2.getAvgDealTime()))).collect(Collectors.toList());

        todoDtoBuild.get().setRickSkuRankingDto(rankingDtoList);
    }

    private String getUserName(Map<Integer, User> userMap,Integer userId){
        if(userMap.get(userId) == null || StringUtils.isEmpty(userMap.get(userId).getUsername())){
            return "无此用户";
        }
        return userMap.get(userId).getUsername();
    }

    private String getUserDepartment(Map<Integer, User> userMap,Integer userId){
        if(userMap.get(userId) == null || StringUtils.isEmpty(userMap.get(userId).getOrgName())){
            return "无采购部门";
        }
        return userMap.get(userId).getOrgName();
    }
}
