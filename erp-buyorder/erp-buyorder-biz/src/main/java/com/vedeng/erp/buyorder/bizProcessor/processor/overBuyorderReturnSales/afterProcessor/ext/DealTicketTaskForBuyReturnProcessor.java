package com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext;

import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.OverBuyorderAfterSaleReturnOrderAfterProcessor;
import com.vedeng.erp.buyorder.common.constant.AfterSalesConstant;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DealTicketTaskForBuyReturnProcessor extends OverBuyorderAfterSaleReturnOrderAfterProcessor {

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    /**
     * 采购单的售后单售后完结时对应sku催票任务处理如下
     * <p>
     * --原采购10，收了8，催票8，退了1，此时催票数＜现值9，催票任务不变
     * <p>
     * --原采购10，收了8，催票8，退了4，此时催票数＞现值6，催票任务要扣减到6，同一个sku多个催票任务，按创建时间远到近的顺序依次扣减
     * --原采购10，收了8，催票8，退了10，此时催票任务都关闭
     *
     * @param bizDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doProcess(BizDto bizDto) {
        AfterSalesVo afterSalesVo = (AfterSalesVo) bizDto.getBizData();
        AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(afterSalesVo.getAfterSalesId());
        List<BuyorderGoodsVo> buyorderGoodsListByBuyorderId = buyorderGoodsMapper.getBuyorderGoodsListByBuyorderId(afterSalesById.getOrderId());
        if (CollectionUtils.isEmpty(buyorderGoodsListByBuyorderId)) {
            return;
        }
        for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsListByBuyorderId) {
            BuyorderGoods buyorderGoods = buyorderGoodsMapper.getbuyorderGoodsInfoByBuyorderGoodsId(buyorderGoodsVo.getBuyorderGoodsId());
            List<EarlyWarningTask> earlyWarningTicketTaskList = earlyWarningTaskMapper.getEarlyWarningTicketTaskByBuyorderGoodId(buyorderGoods.getBuyorderGoodsId());
            if (CollectionUtils.isEmpty(earlyWarningTicketTaskList)) {
                continue;
            }
            AfterSalesGoods afterSalesGoodsQuery = new AfterSalesGoods();
            afterSalesGoodsQuery.setOrderDetailId(buyorderGoods.getBuyorderGoodsId());
            afterSalesGoodsQuery.setOperateType(AfterSalesConstant.TH_BUYORDER_AFTER_SALE_CODE);
            Integer afterbuyorderNumByBuyorderGoodsId = afterSalesGoodsMapper.getAfterbuyorderNumByBuyorderGoodsId(afterSalesGoodsQuery);
            Integer realNum = buyorderGoods.getNum() - afterbuyorderNumByBuyorderGoodsId;
            if (realNum <= 0) {
                int num = earlyWarningTaskMapper.closeEarlyTicketTaskList(earlyWarningTicketTaskList);
                continue;
            }
            Integer ticketNum = 0;
            for (EarlyWarningTask earlyWarningTask : earlyWarningTicketTaskList) {
                ticketNum += earlyWarningTask.getUrgingTicketNum();
            }
            if (ticketNum > realNum) {
                Integer cancleNum = ticketNum - realNum;
                for (EarlyWarningTask earlyWarningTask : earlyWarningTicketTaskList) {
                    if (cancleNum <= 0) {
                        break;
                    }
                    EarlyWarningTask newEarlyWaringTask = new EarlyWarningTask();
                    newEarlyWaringTask.setEarlyWarningTaskId(earlyWarningTask.getEarlyWarningTaskId());
                    if (earlyWarningTask.getUrgingTicketNum() > cancleNum) {
                        newEarlyWaringTask.setUrgingTicketNum(earlyWarningTask.getUrgingTicketNum() - cancleNum);
                        newEarlyWaringTask.setUrgingTicketAmount(earlyWarningTask.getUrgingTicketAmount().subtract(buyorderGoods.getPrice().multiply(new BigDecimal(cancleNum))));
                        newEarlyWaringTask.setUpdateTime(DateUtil.getNowDate(null));
                        BigDecimal haveInvoiceNums = buyorderMapper.getHaveInvoiceNums(buyorderGoodsVo.getBuyorderGoodsId());
                        if (haveInvoiceNums == null) {
                            haveInvoiceNums = new BigDecimal(0);
                        }
                        newEarlyWaringTask.setAlreadyInputNum(haveInvoiceNums);
                        newEarlyWaringTask.setAlreadyInputAmout(buyorderGoods.getPrice().multiply(haveInvoiceNums));
                    } else {
                        newEarlyWaringTask.setUrgingTicketNum(0);
                        newEarlyWaringTask.setUrgingTicketAmount(new BigDecimal(0));
                        newEarlyWaringTask.setUpdateTime(DateUtil.getNowDate(null));
                        newEarlyWaringTask.setIsDeleted(1);
                    }
                    earlyWarningTaskMapper.updateByPrimaryKeySelective(newEarlyWaringTask);
                    cancleNum = cancleNum - earlyWarningTask.getUrgingTicketNum();
                }
            } else {
                EarlyWarningTask earlyWarningTask = earlyWarningTicketTaskList.get(0);
                EarlyWarningTask newEarlyWaringTask = new EarlyWarningTask();
                newEarlyWaringTask.setEarlyWarningTaskId(earlyWarningTask.getEarlyWarningTaskId());
                newEarlyWaringTask.setUpdateTime(DateUtil.getNowDate(null));
                BigDecimal haveInvoiceNums = buyorderMapper.getHaveInvoiceNums(buyorderGoodsVo.getBuyorderGoodsId());
                if (haveInvoiceNums != null) {
                    newEarlyWaringTask.setAlreadyInputNum(haveInvoiceNums);
                    newEarlyWaringTask.setAlreadyInputAmout(buyorderGoods.getPrice().multiply(haveInvoiceNums));
                    earlyWarningTaskMapper.updateByPrimaryKeySelective(newEarlyWaringTask);
                }
            }
        }
    }
}
