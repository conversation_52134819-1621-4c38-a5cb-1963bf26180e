<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="授权书申请" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<link rel="stylesheet" href="<%=basePath%>static/css/authorization_print.css?rnd=${resourceVersionKey}"/>
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/font/font.css"/>
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/lib/pikaday.css?rnd=${resourceVersionKey}"/>
<script type="text/javascript"
        src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<style>

    /* 公章类型下拉框样式 */
    .seal-type-select {
        width: 300px !important; /* 增加宽度以完整显示选项文本 */
        max-width: 100%;
    }

    .centered-text {
        text-align: center; /* 使文本居中 */
        position: relative; /* 为虚线定位做准备 */
        padding: 20px; /* 给div添加一些内边距 */
    }

    .centered-text::before {
        content: ''; /* 伪元素需要content属性 */
        position: absolute; /* 绝对定位 */
        top: 50%; /* 将虚线置于div的中间 */
        left: 0; /* 从div的左边开始 */
        right: 0; /* 到div的右边结束 */
        border-top: 1px dashed #000; /* 虚线样式 */
    }

    .centered-text span {
        background-color: white;
        padding: 0 10px;
    }


    #productSelectDialog{
        padding: 0px 10px 10px 10px;
    }
    #productSelectDialog table{
        border: solid 1px #c3c3c3;
        border-collapse: collapse;
    }
    #productSelectDialog table th{
        font-size: 12px;
        border: solid 1px #c3c3c3;
        border-collapse: collapse;
    }
    #productSelectDialog table td{
        font-size: 12px;
        border: solid 1px #c3c3c3;
        border-collapse: collapse;
    }
    #productSelectDialog .input-print{
        border-width: 0 0 1px 0; /* 上、右、左边框宽度为 0，底部边框宽度为 1px */
        border-style: solid; /* 边框样式为实线 */
        border-color: #ccc; /* 边框颜色为黑色 */
        padding: 5px; /* 可选：添加内边距 */
        outline: none; /* 去掉聚焦时的外边框 */
    }
    #productSelectDialog #productSelectMessage{
        font-size: 12px;
        color:red;
        margin-top:10px;
    }
    #productSelectDialog .errorTips{
        color:red;
        display: none;
    }
    #applyList{
        padding:20px;
    }
    #applyList  ul {
        display: flex ;
        list-style: none;
        /*border-top: solid 1px #ccc;*/
        flex-wrap: wrap;
        gap: 10px;

    }

    #applyList  ul li {
        font-family: '微软雅黑', sans-serif;
        font-weight: 400;
        width: 160px;
        text-align: left;
        border: solid 1px #ccc;
        cursor: pointer;
        font-size: 12px;
        background-color: #ccc;
        color:#333333;
        height: 56px;
        box-sizing: border-box;
        display: grid; /* 使用 grid */
        place-items: center; /* 上下居中 */
        padding: 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        border-radius: 4px;
    }
    #applyList  ul li span{
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    #applyList  ul li:hover{
        font-family: '微软雅黑', sans-serif;
        font-weight: 400;
        width: 160px;
        text-align: left;
        border: solid 1px #0099FF;
        cursor: pointer;
        font-size: 12px;
        background-color: white;
        height: 56px;
        box-sizing: border-box;
        display: grid; /* 使用 grid */
        place-items: center; /* 上下居中 */
        color:#0099FF;
    }


    #applyList   .active{
        background: inherit;
        background-color: rgba(0, 153, 255, 1);
        color:#FFFFFF;
    }






    .form-submit {
        /*border: 1px solid #000;*/ /* 添加边框样式 */
        padding: 20px; /* 添加内边距以便内容不紧贴边框 */
        margin-top: 40px;
        margin-bottom: 20px; /* 添加底部外边距以便与下方内容区分 */
        background-color: white;
    }
    .wrapper {
        padding: 20px;
        width: 960px;
        margin: 0 auto;
    }

    .wrapper .form-item {
        margin-top: 10px;
        padding: 7px 0;
        display: flex;
    }

    .wrapper .form-item .item-label {
        width: 120px;
        text-align: right;
        margin-right: 10px;
    }
</style>


<form method="post" action="<%= basePath %>/order/quote/authorizationApply.do" id="tjform">
    <input type="hidden" id="companyInfoListWithSeq" name="companyInfoListWithSeq" value='${companyInfoListWithSeq}'/>
    <input type="hidden" value="${quoteorderId}" name="quoteorderId">
    <input type="hidden" value="${skuId}" name="skuId" id="skuId">
    <%--<input type="hidden" name="temporaryStorageId" value="${authorizationStorage.temporaryStorageId}">--%>
    <input type="hidden" name="temporaryStorageIds" value="${temporaryStorageIds}">
    <input type="hidden" name="maxId" value="${maxId}" id="maxId">
    <input type="hidden" name="formToken" value="${formToken}" id="formToken">
    <input type="hidden" name="draftSkuJson"  id="draftSkuJson" />
    <div class="form-public" style="background-color: #F5F7FA;">
        <div class="wrapper">
            <div class="form-submit">
                <div class="form-item">
                    <!--新增授权类型字段 authType-->
                    <div class="item-label"><span style="color:red;">*</span>授权类型：</div>
                    <div class="item-fields">
                        <input type="radio" name="authType" value="0" onclick="initDateForApply(90,false)"
                               <c:if test="${(empty authorizationStorage.authType) or (authorizationStorage.authType eq 0)}">checked</c:if>>项目授权
                        <input type="radio" name="authType" value="1" onclick="initDateForApply(365,false)"
                               <c:if test="${(not empty authorizationStorage.authType) and (authorizationStorage.authType eq 1)}">checked</c:if>>经销授权
                    </div>
                </div>

                <div class="form-item">
                    <div class="item-label"><span style="color:red;">*</span>授权书模板：</div>
                    <div class="item-fields">
                        <input type="radio" name="standardTemplate" value="0" onclick="javascript:clickBiaoZhun();"
                               <c:if test="${(empty authorizationStorage.standardTemplate) or (authorizationStorage.standardTemplate eq 0)}">checked</c:if>>标准模板
                        <input type="radio" name="standardTemplate" value="1" onclick="javascript:clickNotBiaoZhun();"
                               <c:if test="${authorizationStorage.standardTemplate eq 1}">checked</c:if>>非标准模板
                    </div>
                </div>

                <div class="form-item" id="notBiaoZhunDiv">
                    <div class="item-label"><span style="color:red;">*</span>非标授权书附件：</div>
                    <div class="item-fields">
                        <input type="hidden" id="domain" name="domain" value="${domain}">
                        <div>
                            <div class="form-blanks">
                                <c:choose>
                                    <c:when test="${nonStandardAuthorizationUrl ne null && nonStandardAuthorizationUrl ne ''}">
                                        <div class="c_1">
                                            <div class="pos_rel f_left ">
                                                <input type="file" class="upload_file" name="lwfile"
                                                       id="file_non" style="display: none;"
                                                       onchange="uploadFileNonStandardAuthorization(this,'non');"/>

                                                <input type="text"
                                                       class="input-middle"
                                                       id="name_non"
                                                       readonly="readonly"
                                                       placeholder="请上传附件"
                                                       name="nonStandardAuthorizationName"
                                                       onclick="file_non.click();"
                                                       value="${nonStandardAuthorizationName}">

                                                <input type="hidden"
                                                       id="uri_non"
                                                       name="nonStandardAuthorizationUrl"
                                                       value="${nonStandardAuthorizationUrl}">

                                                <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                       id="busUpload"
                                                       onclick="return $('#file_non').click();">浏览</label>

                                                <div class="font-red" style="display: none;">请上传附件</div>
                                            </div>


                                            <c:choose>
                                                <c:when test="${nonStandardAuthorizationUrl ne null && nonStandardAuthorizationUrl ne ''}">
                                                    <div class="f_left ">
                                                        <i class="iconsuccesss ml7" id="img_icon_non"></i>

                                                        <a href="http://${domain}${nonStandardAuthorizationUrl}"
                                                           target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_non">查看</a>

                                                        <span class="font-red cursor-pointer mt4"
                                                              onclick="del('non')"
                                                              id="img_del_non">删除</span>
                                                    </div>
                                                </c:when>
                                            </c:choose>

                                            <div class="clear"></div>
                                        </div>
                                    </c:when>

                                    <c:otherwise>
                                        <div class="c_1">
                                            <div class="pos_rel f_left">
                                                <input type="file"
                                                       class="upload_file"
                                                       name="lwfile"
                                                       id="file_non"
                                                       style="display: none;"
                                                       onchange="uploadFileNonStandardAuthorization(this,'non');"/>

                                                <input type="text"
                                                       class="input-middle"
                                                       id="name_non"
                                                       readonly="readonly"
                                                       placeholder="请上传附件"
                                                       name="nonStandardAuthorizationName"
                                                       onclick="file_non.click();"
                                                >

                                                <input type="hidden" id="uri_non" name="nonStandardAuthorizationUrl">

                                                <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                       id="busUpload"
                                                       onclick="return $('#file_non').click();">浏览</label>

                                                <div class="font-red" style="display: none;">请上传附件</div>
                                            </div>

                                            <!-- 上传成功出现 -->
                                            <i class="iconsuccesss ml7 none" id="img_icon_non"></i>
                                            <a href="" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                               id="img_view_non">查看</a>
                                            <span class="font-red cursor-pointer mt4 none" onclick="del('non')"
                                                  id="img_del_non">删除</span>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>

                                </c:choose>

                                <!-- 上传成功出现 -->
                                <div class="pos_rel f_left">
                                    <i class="iconsuccesss mt3 none" id="img_icon_non"></i>
                                    <a href="" target="_blank" class="font-blue cursor-pointer  mt3 none"
                                       id="img_view_non">查看</a>
                                    <span class="font-red cursor-pointer  mt3 none" onclick="del('non')"
                                          id="img_del_non">删除</span>
                                </div>
                                <div class='clear'></div>
                            </div>
                        </div>
                        <div class="tip">
                            <input type="hidden"
                                   id="whether_sign"
                                   name="whetherSign"
                                   value="${whetherSign}">
                            <div id="whether_sign_tip" style="display: none;color: #ff0000;font-size: 12px">文件无法识别自动盖电子章，该申请需在审批后联系财务盖鲜章</div>
                        </div>

                        <div class="tip">
                            <div style="font-size: 12px">仅限上传20M以内文本型PDF。</div>
                        </div>
                    </div>
                </div>

                <div class="form-item">
                    <div class="item-label">其他附件：</div>
                    <div class="item-fields">
                        <input type="hidden" id="domain" name="domain" value="${domain}">
                        <div>
                            <div class="form-blanks">
                                <c:choose>
                                    <c:when test="${!empty attachmentList }">
                                        <c:forEach items="${attachmentList }" var="bus" varStatus="st">
                                            <div class="c_1">
                                                <div class="pos_rel f_left mb8">
                                                    <input type="file" class="upload_file" name="lwfile"
                                                           id="file_${st.index}" style="display: none;"
                                                           onchange="uploadFile(this,${st.index});"/>
                                                    <c:choose>
                                                        <c:when test="${st.index == 0 }">
                                                            <input type="text" class="input-middle" id="name_${st.index}"
                                                                   readonly="readonly"
                                                                   placeholder="请上传附件" name="fileName"
                                                                   onclick="file_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_${st.index}" name="fileUri"
                                                                   value="${bus.uri}">

                                                        </c:when>
                                                        <c:otherwise>
                                                            <input type="text" class="input-middle"
                                                                   id="name_${st.index}" readonly="readonly"
                                                                   placeholder="请上传附件" name="fileName"
                                                                   onclick="file_${st.index}.click();"
                                                                   value="${bus.name}">
                                                            <input type="hidden" id="uri_${st.index}" name="fileUri"
                                                                   value="${bus.uri}">
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           id="busUpload"
                                                           onclick="return $('#file_${st.index}').click();">浏览</label>
                                                    <div class="font-red " style="display: none;">请上传附件</div>
                                                </div>

                                                <c:choose>
                                                    <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7" id="img_icon_1"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4"
                                                               id="img_view_${st.index}">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="del(${st.index})"
                                                                          id="img_del_${st.index}">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="delAttachment(this)"
                                                                          id="img_del_${st.index}">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <div class="f_left ">
                                                            <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                            <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                               class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                               id="img_view_4">查看</a>
                                                            <c:choose>
                                                                <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="del(1)" id="img_del_4">删除</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="delAttachment(this)" id="img_del_4">删除</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="c_1">
                                            <div class="pos_rel f_left mb8">
                                                <input type="file" class="upload_file" name="lwfile" id="file_1"
                                                       style="display: none;" onchange="uploadFile(this,1);"/>
                                                <input type="text" class="input-middle" id="name_1" readonly="readonly"
                                                       placeholder="请上传附件" name="fileName" onclick="file_1.click();"
                                                       value="${bus.name}">
                                                <input type="hidden" id="uri_1" name="fileUri" value="${bus.uri}">
                                                <label class="bt-bg-style bt-middle bg-light-blue ml4" id="busUpload"
                                                       onclick="return $('#file_1').click();">浏览</label>
                                                <div class="font-red" style="display: none;">请上传附件</div>
                                            </div>
                                            <!-- 上传成功出现 -->
                                            <c:choose>
                                                <c:when test="${!empty bus.uri}">
                                                    <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                    <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_1">查看</a>
                                                    <span class="font-red cursor-pointer mt4" onclick="del(1)"
                                                          id="img_del_1">删除</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="iconsuccesss ml7 none" id="img_icon_1"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_1">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(1)"
                                                          id="img_del_1">删除</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <!-- 上传成功出现 -->
                                <div class="pos_rel f_left">
                                    <i class="iconsuccesss mt3 none" id="img_icon_1"></i>
                                    <a href="" target="_blank" class="font-blue cursor-pointer  mt3 none"
                                       id="img_view_1">查看</a>
                                    <span class="font-red cursor-pointer  mt3 none" onclick="del(1)"
                                          id="img_del_1">删除</span>
                                </div>
                                <div class='clear'></div>
                            </div>
                            <div  id="conadd">
                                <span class="bt-border-style bt-small border-blue" onclick="conadd();">继续添加</span>
                            </div>
                        </div>
                        <div class="tip">
                            <div style="font-size: 12px">仅限上传20M以内，WORD/PDF/JPG/PNG。</div>
                        </div>
                    </div>
                </div>

                <div class="form-item">
                    <div class="item-label">事项描述：</div>
                    <div class="item-fields">
                <textarea class="input-textarea" name="described" id="described" placeholder="授权书若使用非标准模板，请说明原因，100字以内。"
                          cols="30"
                          rows="5" maxlength="100">${authorizationStorage.described}</textarea>
                    </div>
                </div>

                <div class="form-item">
                    <div class="item-label"><span style="color:red;">*</span>公章类型：</div>
                    <div class="item-fields">
                        <select name="sealType" id="sealType" class="input-middle seal-type-select" onchange="changeSealType()">
                            <c:forEach items="${companyInfoList}" var="company">
                                <option value="${company.frontEndSeq}" <c:if test="${not empty authorizationStorage.sealType and authorizationStorage.sealType eq company.frontEndSeq}">selected</c:if>>${company.companyName}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>

                <div class="form-item">
                    <div class="item-label"><span style="color:red;">*</span>份数：</div>
                    <input type="text" class="select-print" name="num" id="auNum" value="${authorizationStorage.num}"
                           onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                           onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}">份
                </div>
            </div>
            <%--<div class="centered-text">
                分页线
            </div>--%>
            <div style="height: 20px;">

            </div>
            <div id="applyList" style="display: none;background-color: white;">
                <ul >

                </ul>
            </div>
            <div  style="margin: 0 auto;background-color: white;">
            <div id="print-contract" style="background-color: white;">

                <div class="header">
                    <div class="logo">
                        <img src="<%=basePath%>static/images/print_logo.png" alt="" id="company-logo">
                    </div>
                    <div class="header-txt" style="padding-right:80px;">
                        <br>
                        <br>
                        <br>
                        授权书编号：${authorizationStorage.authorizationApplyNum}&nbsp; <br>
                    </div>
                </div>


                <div class="title">授权书</div>
                <div class="content">
                    <p style="text-indent: 0;">致：<input type="text" class="input-print left xl"
                                                        value="${authorizationStorage.purchaseOrBidding}"
                                                        name="purchaseOrBidding" id="purchaseOrBidding"
                                                        placeholder="请填写采购单位/招标公司" maxlength="200"></p>
                    <p>南京贝登医疗股份有限公司作为
                        <input type="text" class="input-print xl" value="${authorizationStorage.productCompany}" onclick="showSku()" readonly
                               name="productCompany" id="productCompany" maxlength="200" placeholder="请填写生产厂家"
                               maxlength="200">
                        <select class="select-print" name="natureOfOperation" id="natureOfOperation" onclick="showSku()" readonly>
                            <option value="1"
                                    <c:if test="${authorizationStorage.natureOfOperation eq 1}">selected="selected"</c:if>>
                                生产
                            </option>
                            <option value="2"
                                    <c:if test="${authorizationStorage.natureOfOperation eq 2}">selected="selected"</c:if>>
                                代理销售
                            </option>
                        </select>
                        <input type="text" class="select-print" name="brandName" id="brandName" value="${brandName}" onclick="showSku()" readonly />
                               <%--onclick="findSku(${quoteorderId});"--%>

                        品牌的
                        <input type="text" class="select-print" style="width: 320px" name="skuName" id="skuName" onclick="showSku()"
                               value="${skuName}"   readonly>

                        <input type="hidden" class="select-print" name="skuModel" id="model" value="${model}" />
                        的
                        <select class="select-print" name="distributionsType" id="distributionsType" onclick="showSku()" readonly>
                            <option value="1"
                                    <c:if test="${authorizationStorage.distributionsType eq 1}">selected="selected"</c:if>>
                                独家经销商
                            </option>
                            <option value="2"
                                    <c:if test="${authorizationStorage.distributionsType eq 2}">selected="selected"</c:if>>
                                经销商
                            </option>
                            <option value="3"
                                    <c:if test="${authorizationStorage.distributionsType eq 3}">selected="selected"</c:if>>
                                代理商
                            </option>
                        </select>
                        ，在此授权
                        <input type="text" class="input-print xl J-company-1" id="traderName" value="${authorizedCompany}"
                               placeholder="（授权公司）" maxlength="200">
                        （被授权人）使用上述货物就项目：
                        <input type="text" style="width: 200px;" value="${authorizationStorage.purchaseProjectName}"
                               name="purchaseProjectName" id="purchaseProjectName" class="input-print xl"
                               placeholder="请填写采购项目全称" maxlength="200" required="required">
                        项目编号：
                        <input type="text" value="${authorizationStorage.purchaseProjectNum}" name="purchaseProjectNum"
                               id="purchaseProjectNum" class="input-print l" placeholder="请填写采购项目编号" maxlength="50">
                        递交
                        <select class="select-print" name="fileType" id="fileType">
                            <option value="1"
                                    <c:if test="${authorizationStorage.fileType eq 1}">selected="selected"</c:if>>
                                投标
                            </option>
                            <option value="2"
                                    <c:if test="${authorizationStorage.fileType eq 2}">selected="selected"</c:if>>
                                响应
                            </option>
                        </select>
                        文件，且
                        <input style="width: 200px;" type="text" class="input-print xl J-company-2"
                               value="${authorizedCompany}" name="authorizedCompany" id="authorizedCompany"
                               placeholder="（授权公司）" maxlength="200">
                        以其自己的名义处理后续的商业谈判和签署合同并独立承担责任。
                    </p>
                    <p>
                        以上所述授权产品的相关售后服务支持工作将由
                        <input type="text" class="input-print xl" value="${authorizationStorage.aftersalesCompany}"
                               name="aftersalesCompany" id="aftersalesCompany" placeholder="（售后支持公司全称）" maxlength="200">
                        提供。
                    </p>
                    <p>
                        该授权未经南京贝登医疗股份有限公司书面同意，被授权人不可转授任何第三方。被授权人禁止将该授权书作为任何网络线上渠道的资格审核材料，且禁止被授权人在任何网络线上渠道展示和销售该授权书涉及的商品。
                    </p>
                    <p class="J-date-range">
                        本授权的有效期：
                        <input type="text"  autocomplete="off" value="${authorizationStorage.beginTime}" id="beginTime" name="beginTime"
                               class="input-print input-date l" readonly>
                        至
                        <input type="text"  autocomplete="off" value="${authorizationStorage.endTime}" id="endTime" name="endTime"
                               class="input-print input-date l" readonly>
                        (控制：<span id="pikadayNum" style="display: contents;">项目授权，时间不得超过90天</span>)
                    </p>
                    <p class="top right">
                        南京贝登医疗股份有限公司
                    </p>
                    <p class="right">
                        <select class="input-print x2" name="applyYear" id="applyYear">
                            <option value="${canladerYear-5}" ${dateYear == canladerYear-5 ? 'selected="selected"' : ''}>${canladerYear-5}</option>
                            <option value="${canladerYear-4}" ${dateYear == canladerYear-4 ? 'selected="selected"' : ''}>${canladerYear-4}</option>
                            <option value="${canladerYear-3}" ${dateYear == canladerYear-3 ? 'selected="selected"' : ''}>${canladerYear-3}</option>
                            <option value="${canladerYear-2}" ${dateYear == canladerYear-2 ? 'selected="selected"' : ''}>${canladerYear-2}</option>
                            <option value="${canladerYear-1}" ${dateYear == canladerYear-1 ? 'selected="selected"' : ''}>${canladerYear-1}</option>
                            <option value="${canladerYear}" ${dateYear == canladerYear ? 'selected="selected"' : ''}>${canladerYear}</option>
                            <option value="${canladerYear+1}" ${dateYear == canladerYear+1 ? 'selected="selected"' : ''}>${canladerYear+1}</option>
                            <option value="${canladerYear+2}" ${dateYear == canladerYear+2 ? 'selected="selected"' : ''}>${canladerYear+2}</option>
                            <option value="${canladerYear+3}" ${dateYear == canladerYear+3 ? 'selected="selected"' : ''}>${canladerYear+3}</option>
                            <option value="${canladerYear+4}" ${dateYear == canladerYear+4 ? 'selected="selected"' : ''}>${canladerYear+4}</option>
                            <option value="${canladerYear+5}" ${dateYear == canladerYear+5 ? 'selected="selected"' : ''}>${canladerYear+5}</option>
                            <option value="${canladerYear+6}" ${dateYear == canladerYear+6 ? 'selected="selected"' : ''}>${canladerYear+6}</option>
                            <option value="${canladerYear+7}" ${dateYear == canladerYear+7 ? 'selected="selected"' : ''}>${canladerYear+7}</option>
                            <option value="${canladerYear+8}" ${dateYear == canladerYear+8 ? 'selected="selected"' : ''}>${canladerYear+8}</option>
                            <option value="${canladerYear+9}" ${dateYear == canladerYear+9 ? 'selected="selected"' : ''}>${canladerYear+9}</option>
                            <option value="${canladerYear+10}" ${dateYear == canladerYear+10 ? 'selected="selected"' : ''}>${canladerYear+10}</option>
                        </select>
                        年
                        <select class="input-print x2" name="applyMonth" id="applyMonth">
                            <option value="01" ${dateMonth == '01' ? 'selected="selected"' : ''}>01</option>
                            <option value="02" ${dateMonth == '02' ? 'selected="selected"' : ''}>02</option>
                            <option value="03" ${dateMonth == '03' ? 'selected="selected"' : ''}>03</option>
                            <option value="04" ${dateMonth == '04' ? 'selected="selected"' : ''}>04</option>
                            <option value="05" ${dateMonth == '05' ? 'selected="selected"' : ''}>05</option>
                            <option value="06" ${dateMonth == '06' ? 'selected="selected"' : ''}>06</option>
                            <option value="07" ${dateMonth == '07' ? 'selected="selected"' : ''}>07</option>
                            <option value="08" ${dateMonth == '08' ? 'selected="selected"' : ''}>08</option>
                            <option value="09" ${dateMonth == '09' ? 'selected="selected"' : ''}>09</option>
                            <option value="10" ${dateMonth == '10' ? 'selected="selected"' : ''}>10</option>
                            <option value="11" ${dateMonth == '11' ? 'selected="selected"' : ''}>11</option>
                            <option value="12" ${dateMonth == '12' ? 'selected="selected"' : ''}>12</option>
                        </select>
                        月
                        <select class="input-print x2" name="applyDay" id="applyDay">
                            <option value="01" ${dateDay == '01' ? 'selected="selected"' : ''}>01</option>
                            <option value="02" ${dateDay == '02' ? 'selected="selected"' : ''}>02</option>
                            <option value="03" ${dateDay == '03' ? 'selected="selected"' : ''}>03</option>
                            <option value="04" ${dateDay == '04' ? 'selected="selected"' : ''}>04</option>
                            <option value="05" ${dateDay == '05' ? 'selected="selected"' : ''}>05</option>
                            <option value="06" ${dateDay == '06' ? 'selected="selected"' : ''}>06</option>
                            <option value="07" ${dateDay == '07' ? 'selected="selected"' : ''}>07</option>
                            <option value="08" ${dateDay == '08' ? 'selected="selected"' : ''}>08</option>
                            <option value="09" ${dateDay == '09' ? 'selected="selected"' : ''}>09</option>
                            <option value="10" ${dateDay == '10' ? 'selected="selected"' : ''}>10</option>
                            <option value="11" ${dateDay == '11' ? 'selected="selected"' : ''}>11</option>
                            <option value="12" ${dateDay == '12' ? 'selected="selected"' : ''}>12</option>
                            <option value="13" ${dateDay == '13' ? 'selected="selected"' : ''}>13</option>
                            <option value="14" ${dateDay == '14' ? 'selected="selected"' : ''}>14</option>
                            <option value="15" ${dateDay == '15' ? 'selected="selected"' : ''}>15</option>
                            <option value="16" ${dateDay == '16' ? 'selected="selected"' : ''}>16</option>
                            <option value="17" ${dateDay == '17' ? 'selected="selected"' : ''}>17</option>
                            <option value="18" ${dateDay == '18' ? 'selected="selected"' : ''}>18</option>
                            <option value="19" ${dateDay == '19' ? 'selected="selected"' : ''}>19</option>
                            <option value="20" ${dateDay == '20' ? 'selected="selected"' : ''}>20</option>
                            <option value="21" ${dateDay == '21' ? 'selected="selected"' : ''}>21</option>
                            <option value="22" ${dateDay == '22' ? 'selected="selected"' : ''}>22</option>
                            <option value="23" ${dateDay == '23' ? 'selected="selected"' : ''}>23</option>
                            <option value="24" ${dateDay == '24' ? 'selected="selected"' : ''}>24</option>
                            <option value="25" ${dateDay == '25' ? 'selected="selected"' : ''}>25</option>
                            <option value="26" ${dateDay == '26' ? 'selected="selected"' : ''}>26</option>
                            <option value="27" ${dateDay == '27' ? 'selected="selected"' : ''}>27</option>
                            <option value="28" ${dateDay == '28' ? 'selected="selected"' : ''}>28</option>
                            <option value="29" ${dateDay == '29' ? 'selected="selected"' : ''}>29</option>
                            <option value="30" ${dateDay == '30' ? 'selected="selected"' : ''}>30</option>
                            <option value="31" ${dateDay == '31' ? 'selected="selected"' : ''}>31</option>
                        </select>
                        日
                    </p>
                </div>
                <div class="footer">
                    <div class="footer-l">www.vedeng.com</div>
                    <div class="footer-r">医疗器械互联网供应链服务平台</div>
                </div>
            </div>


            </div>
        </div>

        <div class="add-tijiao pb15">
            <button type="button" class="btn btn-blue btn-large" style="background-color: #1E9FFF" onclick="tj()">提交
            </button>
            <button type="button" class="btn btn-blue btn-large" style="background-color: #1E9FFF;" id="baocun"
                    onclick="bc();">保存
            </button>
            <button type="button" class="btn btn-blue btn-large" style="background-color: #1E9FFF;"
                    onclick="authorizationPre()">预览
            </button>
            <span style="display:none;"><div class="title-click nobor addtitle2" id="authorizationPreview"></div></span>
        </div>

        <!-- 弹框的HTML结构 -->

            <div id="productSelectDialog" style="display: none;">
                <table class="layui-table">
                    <thead>
                    <tr>
                        <th style="width: 80px;"><input type="checkbox" id="checkAll" onclick="checkAllProduct(this)">全选/反选</th>
                        <th style="width: 100px;">品牌</th>
                        <th style="width: 180px;">产品名称</th>
                        <th style="width: 200px;">厂家/代理</th>
                        <th style="width: 100px;">类型</th>
                        <th style="width: 100px;">贝登渠道</th>
                    </tr>
                    </thead>
                    <tbody id="productList">
                    <!-- 产品列表将通过JS动态生成 -->
                    </tbody>
                </table>
                <span id="productSelectMessage">
                    说明：当选择多个产品时填写完相关信息后，系统会批量申请授权。
                </span>
            </div>


    </div>



</form>
<script type="text/javascript">
    $("#btnPrint").click(function () {
        $("#btnPrint").hide();
        if (window.ActiveXObject || "ActiveXObject" in window) {
            $("#print-contract").printArea({
                mode: 'popup'
            });
        } else {
            $("#print-contract").printArea();
        }
        $("#btnPrint").show();
    });

</script>

<script>

    // 假设这是你的产品信息JSON数组
    var productListJson = '${productList}';
    var products = JSON.parse(productListJson);
    $(function() {
        $(".select-print").css("border-color","#0099FF");
        $(".input-print").css("border-color","#0099FF");
        <c:if test="${(empty authorizationStorage.standardTemplate) or (authorizationStorage.standardTemplate eq 0)}">
        clickBiaoZhun();
        </c:if>
        // 遍历 products 数组，筛选出 check=1 的对象
        var initproducts = products.filter(function (product) {
            return product.check == 1;
        });
        showUl(initproducts);
        console.log(products);
    })


</script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/quote/authorization.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp" %>
