package com.vedeng.erp.trader.service.impl;

import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.page.Page;
import com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto;
import com.vedeng.erp.trader.mapper.PublicCustomerExemptUsersMapper;
import com.vedeng.erp.trader.service.PublicCustomerExemptUsersService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/29 15:21
 **/
@Service
@Slf4j
public class PublicCustomerExemptUsersServiceImpl implements PublicCustomerExemptUsersService {

    @Resource
    private PublicCustomerExemptUsersMapper publicCustomerExemptUsersMapper;

    @Override
    public List<PublicCustomerExemptUsersDto> queryExemptData(Page page) {

        Map<String, Object> map = new HashMap<>(2);
        // 未删除信息
        map.put("deleted", false);
        map.put("page", page);
        List<PublicCustomerExemptUsersDto> listpage = publicCustomerExemptUsersMapper.selectByDeletedlistPage(map);
        return listpage;
    }

    @Override
    public Integer deleteExemptionUser(PublicCustomerExemptUsersDto publicCustomerExemptUsersDto) {

        if (publicCustomerExemptUsersDto == null || publicCustomerExemptUsersDto.getPublicCustomerExemptUsersId() == null) {
            log.error("入参错误");
            throw new ServiceException("入参错误");
        }

        // 查询数据库数据
        PublicCustomerExemptUsersDto oldData = publicCustomerExemptUsersMapper.selectByPrimaryKey(publicCustomerExemptUsersDto.getPublicCustomerExemptUsersId());
        if (oldData != null && oldData.getDeleted()) {
            return 2;
        }
        publicCustomerExemptUsersDto.setDeleted(true);
        publicCustomerExemptUsersDto.setModTime(new Date());
        publicCustomerExemptUsersMapper.updateByPrimaryKeySelective(publicCustomerExemptUsersDto);
        return 1;
    }

    @Override
    @MethodLock(field = "userId",className = PublicCustomerExemptUsersDto.class)
    public Integer saveExemptionUser(PublicCustomerExemptUsersDto publicCustomerExemptUsersDto) {

        List<PublicCustomerExemptUsersDto> publicCustomerExemptUsersDtos = publicCustomerExemptUsersMapper.selectByUserId(publicCustomerExemptUsersDto.getUserId());
        // 存在否
        if (CollectionUtils.isNotEmpty(publicCustomerExemptUsersDtos)) {
            PublicCustomerExemptUsersDto oldData = publicCustomerExemptUsersDtos.get(0);
            // 已删除 恢复
            if (oldData.getDeleted()) {
                oldData.setDeleted(false);
                oldData.setUpdater(publicCustomerExemptUsersDto.getCreator());
                oldData.setCreator(publicCustomerExemptUsersDto.getCreator());
                oldData.setModTime(new Date());
                oldData.setAddTime(new Date());
                publicCustomerExemptUsersMapper.updateByPrimaryKey(oldData);
            } else {
                return 2;
            }

        } else {
            publicCustomerExemptUsersMapper.insertSelective(publicCustomerExemptUsersDto);
        }
        return 1;
    }
}
