package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.enums.UnitConversionTaxRateEnum;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsUnitConversionOrderItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWmsUnitConversionOrderItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInResultDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 单位转换入库单
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchUnitConversionInProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto> {



    @Autowired
    private BatchWmsUnitConversionOrderItemDtoMapper batchWmsUnitConversionOrderItemDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public KingDeeStorageInDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        log.info("BatchUnitConversionInProcessor.process：{}" , JSON.toJSONString(batchWarehouseGoodsOutInDto));

        log.info("查询是否推送金蝶入库单");
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_MISCELLANEOUS);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").compare("=").left("(").right(")").value(batchWarehouseGoodsOutInDto.getOutInNo()).logic("AND").build());
        queryParam.setFilterString(queryFilterDtos);
        List<KingDeeStorageInResultDto> query = kingDeeBaseApi.query(queryParam, KingDeeStorageInResultDto.class);
        log.info("单位转换入库单查询金蝶结果：{}",JSON.toJSONString(query));
        if (CollUtil.isNotEmpty(query)) {
            log.info("已推送金蝶数据，无需推送");
            return null;
        }
        List<BatchWmsUnitConversionOrderItemDto> batchWmsUnitConversionOrderItemDtoList = batchWmsUnitConversionOrderItemDtoMapper.selectByWmsUnitConversionOrderNo(batchWarehouseGoodsOutInDto.getRelateNo());
        if (CollUtil.isEmpty(batchWmsUnitConversionOrderItemDtoList)) {
            throw new KingDeeException("单位转换入库单未查询到商品信息");
        }

        Map<Integer, BatchWmsUnitConversionOrderItemDto> map = batchWmsUnitConversionOrderItemDtoList
                .stream().collect(Collectors.toMap(BatchWmsUnitConversionOrderItemDto::getWmsUnitConversionOrderItemId, c -> c, (k1, k2) -> k1));

        KingDeeStorageInDto dto = new KingDeeStorageInDto();
        dto.setFId("0");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFStockDirect("GENERAL");

        List<KingDeeStorageInDetailDto> detailDtoList = new ArrayList<>();
        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.warn("未能查到入库单子单信息");
            return null;
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l -> {
            KingDeeStorageInDetailDto detailDto = new KingDeeStorageInDetailDto();
            BatchWmsUnitConversionOrderItemDto batchWmsUnitConversionOrderItemDto = map.get(l.getRelatedId());
            if (Objects.isNull(batchWmsUnitConversionOrderItemDto)||Objects.isNull(batchWmsUnitConversionOrderItemDto.getTargetPrice())) {
                log.error("单位转换单入库未能查到具体的商品明细：{}",JSON.toJSONString(l));
                throw new KingDeeException("单位转换单入库未能查到具体的商品明细");
            }
            detailDto.setFMaterialId(batchWmsUnitConversionOrderItemDto.getTargetSkuNo());
            detailDto.setFStockId("CK9999");
            BigDecimal num = l.getNum().abs();
            detailDto.setFQty(num.toString());
            BigDecimal rate = UnitConversionTaxRateEnum.getRate(batchWmsUnitConversionOrderItemDto.getTaxRate());
            BigDecimal priceNoTax = batchWmsUnitConversionOrderItemDto.getTargetPrice().divide(BigDecimal.ONE.add(rate),2, RoundingMode.HALF_UP);
            detailDto.setFPrice(priceNoTax.toString());
            detailDto.setFAmount(num.multiply(priceNoTax).setScale(2,RoundingMode.HALF_UP).toString());

            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokYwlx("单位转换");
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            detailDto.setFQzokSfzf("false");
            detailDto.setFQzokBddjhId(l.getWarehouseGoodsOutInDetailId().toString());

            detailDtoList.add(detailDto);
        });
        dto.setFEntity(detailDtoList);

        return dto;
    }

}
