package com.vedeng.order.dao;

import com.newtask.model.BqoSysAutoCloseDto;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.MergeBussinessChanceQuery;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.trader.model.Period;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;


public interface BussinessChanceMapper {
    int deleteByPrimaryKey(Integer bussinessChanceId);

    int insert(BussinessChance record);

    int insertSelective(BussinessChance record);

    BussinessChance selectByPrimaryKey(Integer bussinessChanceId);

    int updateByPrimaryKeySelective(BussinessChance record);

    int updateByPrimaryKey(BussinessChance record);


    /**
     * 系统自动关闭报价查询出符合一部分条件的报价
     * @return
     */

    LinkedList<BqoSysAutoCloseDto> getBussCommList(@Param("start") Integer start, @Param("limit") Integer limit);


    /**
     * 批量关闭商机
     * @param bqoSysAutoCloseDtoList
     * @return
     */
    Integer batchCloseBussChance(@Param("BqoList")List<BqoSysAutoCloseDto> bqoSysAutoCloseDtoList,@Param("closeReason")Integer closeReason,@Param("closeComment")String closeComment);

    /**
     * 联动关闭商机byId
     * @param bussinessChanceId
     * @param closeReason
     * @param closeComment
     * @return
     */
    Integer  relateCloseBussChanceById(@Param("bussinessChanceId")Integer bussinessChanceId,@Param("closeReason")Integer closeReason,@Param("closeComment")String closeComment);
    /**
     * 获取指定客户未完结商机列表
     * @param map
     * @return
     */
    List<BussinessChanceVo> getTraderHistoryListPage(Map<String, Object> map);
    /**
     * 获取当前客户未处理和报价中商机
     * @param traderId
     * @return
     */
    List<BussinessChanceVo> getTraderHasHistoryBussiness(Integer traderId);

    List<BussinessChance> getBusinessChanceLimit(@Param("start") Integer start, @Param("limit") Integer limit);

    Integer getBusinessChanceCountByMobileInNinetyDays(@Param("mobile") String mobile, @Param("now") Long now);

    List<Integer> getTraderIdsByPeriod(Period period);


    /**
     * <b>Description:</b>查询这条手机号下当天的可以合并商机<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/6/16
     */
    BussinessChance getChanceCanMerge(MergeBussinessChanceQuery param);

    /**
     * <b>Description:</b>获取商机的编号及合并状态<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/6/18
     */
    BussinessChance getMergeStatus(@Param("chanceId")Integer chanceId);

    /**
     * <b>Description:</b>获取新合并之后的商机id<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/6/18
     */
    Integer getNewMergeChanceId(@Param("chanceNo")String chanceNo);

    BussinessChanceVo getBusinessChanceInfo(@Param("bussinessChanceId") Integer bussinessChanceId);

    /**
     * <b>Description:</b>更新商机的状态<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/8/27
     */
    int updateBCStatus(BussinessChance bc);

    /**
     * <b>Description:</b>分页获取简单的商机列表<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/8/28
     */
    List<BussinessChance> getSimpleBcListPage(Map<String,Object> map);
    /**
     * <b>Description:</b>根据商机编号获取商机信息<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/9/3
     */
    BussinessChance getSimpleBcByNo(@Param("chanceNo")String chanceNo);
    void updateSaleUserOfBussinessChanceByTraderList(@Param("targetUserId") Integer targetUserId, @Param("traderIdList") List<Integer> traderIdList);

    int updateEditComments(BussinessChance bussinessChance);

    List<BussinessChanceVo> getLastMonthBussinessChance(@Param("traderId") Integer traderId, @Param("addTime") Long addTime, @Param("content") String content);

    /**
     * <b>Description:</b>根据订单id获取商机信息<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/8/28
     */
    BussinessChance getBCBySaleorderId(@Param("orderId")Integer orderId);

    /**
     * 检索最近创建商机的客户ID集合
     * @return
     */
    List<Integer> getCreateBusinessChanceRecentlyCustomerIds();

    BussinessChance selectBusinessChanceTimesByTraderId(@Param("traderId") Integer traderId, @Param("addTime") Long addTime);

    /**
     * 根据客户手机号 和 source 查询商机历史
     * @param loginMobile 客户手机号
     * @param source source
     * @return 商机历史列表
     */
    BussinessChance listBussinessByMobileAndSource(@Param("loginMobile") String loginMobile, @Param("source") Integer source);

    BussinessChance getBusinessChanceByChanceNo(@Param("bussinessChanceNo")String bussinessChanceNo);

    List<BussinessChance> queryForassignBussinessChance(@Param("bussinessChanceIds")List<Integer> bussinessChanceIds);


    /**
     * 获取crm任务数量
     */
    Integer getCrmTaskCount(@Param("userId") Integer userId);

    /**
     * 获取crm待办任务数量
     */
    Integer getCrmTodoTaskCount(@Param("userId") Integer userId);


    List<com.vedeng.order.model.vo.TaskGroupVo> getCrmTaskGroupCount(@Param("userId") Integer userId, @Param("startDate") String startDate, @Param("endDate") String endDate);


    List<com.vedeng.order.model.vo.TaskDetailVo> getCrmTaskForOneDay(@Param("userId") Integer userId, @Param("dateStr") String dateStr);


}