package com.vedeng.flash.dto.vo;

import com.vedeng.flash.dto.temp.JoinOrderingPoolOperateTemp;
import lombok.*;

import java.util.List;

/**
 * @program: erp.vedeng.com
 * @description: 加入定品池操作
 * @author: <PERSON>usa<PERSON>
 * @create: 2021-05-21 17:02
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class JoinOrderingPoolOperateVO {

    /** id or No id:1 no:2*/
    private Integer idFlag;

    /**
     * 入池集合
     */
    private List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTempList;

}
