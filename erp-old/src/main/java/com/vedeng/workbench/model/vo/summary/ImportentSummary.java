package com.vedeng.workbench.model.vo.summary;

import com.vedeng.workbench.model.vo.base.WorkbenchSummaryObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 重点商机汇总信息.
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 9:02 下午.
 * @author: Tomcat.Hui.
 */
@Data
public class ImportentSummary {

    /**
     * 统计对象信息
     */
    private WorkbenchSummaryObject objectInfo;

    /**
     * s级数量
     */
    private Integer sNum;

    /**
     * s级金额
     */
    private BigDecimal sAMount;

    /**
     * a级数量
     */
    private Integer aNum;

    /**
     * a级金额
     */
    private Integer aAmount;
}
