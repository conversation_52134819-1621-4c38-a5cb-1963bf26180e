<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.BuyorderGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.BuyorderGoods">
    <!--@mbg.generated-->
    <!--@Table T_BUYORDER_GOODS-->
    <id column="BUYORDER_GOODS_ID" jdbcType="INTEGER" property="buyorderGoodsId" />
    <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyorderId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="ARRIVAL_NUM" jdbcType="INTEGER" property="arrivalNum" />
    <result column="ESTIMATE_DELIVERY_TIME" jdbcType="BIGINT" property="estimateDeliveryTime" />
    <result column="ESTIMATE_ARRIVAL_TIME" jdbcType="BIGINT" property="estimateArrivalTime" />
    <result column="ARRIVAL_USER_ID" jdbcType="INTEGER" property="arrivalUserId" />
    <result column="ARRIVAL_STATUS" jdbcType="BOOLEAN" property="arrivalStatus" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments" />
    <result column="DELIVERY_CYCLE" jdbcType="VARCHAR" property="deliveryCycle" />
    <result column="INSTALLATION" jdbcType="VARCHAR" property="installation" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
    <result column="ORIGINAL_PURCHASE_PRICE" jdbcType="DECIMAL" property="originalPurchasePrice" />
    <result column="COUPON_REASON" jdbcType="VARCHAR" property="couponReason" />
    <result column="AFTER_RETURN_AMOUNT" jdbcType="DECIMAL" property="afterReturnAmount" />
    <result column="AFTER_RETURN_NUM" jdbcType="INTEGER" property="afterReturnNum" />
    <result column="REAL_INVOICE_AMOUNT" jdbcType="DECIMAL" property="realInvoiceAmount" />
    <result column="REAL_INVOICE_NUM" jdbcType="DECIMAL" property="realInvoiceNum" />
    <result column="GE_CONTRACT_NO" jdbcType="VARCHAR" property="geContractNo" />
    <result column="GE_SALE_CONTRACT_NO" jdbcType="VARCHAR" property="geSaleContractNo" />
    <result column="PRODUCT_AUDIT" jdbcType="INTEGER" property="productAudit" />
    <result column="FIRST_SEND_GOODS_TIME" jdbcType="BIGINT" property="firstSendGoodsTime" />
    <result column="FIRST_RECEIVE_GOODS_TIME" jdbcType="BIGINT" property="firstReceiveGoodsTime" />
    <result column="SEND_GOODS_TIME" jdbcType="BIGINT" property="sendGoodsTime" />
    <result column="RECEIVE_GOODS_TIME" jdbcType="BIGINT" property="receiveGoodsTime" />
    <result column="PROCESS_DESCRIPTION" jdbcType="VARCHAR" property="processDescription" />
    <result column="IS_SEND_CREATE_FLAG" jdbcType="BOOLEAN" property="isSendCreateFlag" />
    <result column="ALREADY_EXPEDITING_ALARM" jdbcType="BOOLEAN" property="alreadyExpeditingAlarm" />
    <result column="PRODUCT_BELONG_ID_INFO" jdbcType="VARCHAR" property="productBelongIdInfo" />
    <result column="PRODUCT_BELONG_NAME_INFO" jdbcType="VARCHAR" property="productBelongNameInfo" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="MANUFACTURER_NAME" jdbcType="VARCHAR" property="manufacturerName" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="IS_HAVE_AUTH" jdbcType="INTEGER" property="isHaveAuth" />
    <result column="IS_GIFT" jdbcType="BOOLEAN" property="isGift" />
    <result column="REFER_PRICE" jdbcType="DECIMAL" property="referPrice" />
    <result column="REBATE_PRICE" jdbcType="DECIMAL" property="rebatePrice" />
    <result column="REBATE_AMOUNT" jdbcType="DECIMAL" property="rebateAmount" />
    <result column="ACTUAL_PURCHASE_PRICE" jdbcType="DECIMAL" property="actualPurchasePrice" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUYORDER_GOODS_ID, BUYORDER_ID, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME, MODEL, UNIT_NAME, 
    PRICE, CURRENCY_UNIT_ID, NUM, ARRIVAL_NUM, ESTIMATE_DELIVERY_TIME, ESTIMATE_ARRIVAL_TIME, 
    ARRIVAL_USER_ID, ARRIVAL_STATUS, ARRIVAL_TIME, IS_DELETE, INSIDE_COMMENTS, DELIVERY_CYCLE, 
    INSTALLATION, COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, UPDATE_DATA_TIME, ORIGINAL_PURCHASE_PRICE, 
    COUPON_REASON, AFTER_RETURN_AMOUNT, AFTER_RETURN_NUM, REAL_INVOICE_AMOUNT, REAL_INVOICE_NUM, 
    GE_CONTRACT_NO, GE_SALE_CONTRACT_NO, PRODUCT_AUDIT, FIRST_SEND_GOODS_TIME, FIRST_RECEIVE_GOODS_TIME, 
    SEND_GOODS_TIME, RECEIVE_GOODS_TIME, PROCESS_DESCRIPTION, IS_SEND_CREATE_FLAG, ALREADY_EXPEDITING_ALARM, 
    PRODUCT_BELONG_ID_INFO, PRODUCT_BELONG_NAME_INFO, SPEC, MANUFACTURER_NAME, REGISTRATION_NUMBER, 
    IS_HAVE_AUTH, IS_GIFT, REFER_PRICE, REBATE_PRICE, REBATE_AMOUNT,ACTUAL_PURCHASE_PRICE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUYORDER_GOODS
    where BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_BUYORDER_GOODS
    where BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BUYORDER_GOODS_ID" keyProperty="buyorderGoodsId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_GOODS (BUYORDER_ID, GOODS_ID, SKU, 
      GOODS_NAME, BRAND_NAME, MODEL, 
      UNIT_NAME, PRICE, CURRENCY_UNIT_ID, 
      NUM, ARRIVAL_NUM, ESTIMATE_DELIVERY_TIME, 
      ESTIMATE_ARRIVAL_TIME, ARRIVAL_USER_ID, ARRIVAL_STATUS, 
      ARRIVAL_TIME, IS_DELETE, INSIDE_COMMENTS, 
      DELIVERY_CYCLE, INSTALLATION, COMMENTS, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, UPDATE_DATA_TIME, ORIGINAL_PURCHASE_PRICE, 
      COUPON_REASON, AFTER_RETURN_AMOUNT, AFTER_RETURN_NUM, 
      REAL_INVOICE_AMOUNT, REAL_INVOICE_NUM, GE_CONTRACT_NO, 
      GE_SALE_CONTRACT_NO, PRODUCT_AUDIT, FIRST_SEND_GOODS_TIME, 
      FIRST_RECEIVE_GOODS_TIME, SEND_GOODS_TIME, RECEIVE_GOODS_TIME, 
      PROCESS_DESCRIPTION, IS_SEND_CREATE_FLAG, ALREADY_EXPEDITING_ALARM, 
      PRODUCT_BELONG_ID_INFO, PRODUCT_BELONG_NAME_INFO, 
      SPEC, MANUFACTURER_NAME, REGISTRATION_NUMBER, 
      IS_HAVE_AUTH, IS_GIFT, REFER_PRICE
      )
    values (#{buyorderId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{unitName,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{currencyUnitId,jdbcType=INTEGER}, 
      #{num,jdbcType=INTEGER}, #{arrivalNum,jdbcType=INTEGER}, #{estimateDeliveryTime,jdbcType=BIGINT}, 
      #{estimateArrivalTime,jdbcType=BIGINT}, #{arrivalUserId,jdbcType=INTEGER}, #{arrivalStatus,jdbcType=BOOLEAN}, 
      #{arrivalTime,jdbcType=BIGINT}, #{isDelete,jdbcType=BOOLEAN}, #{insideComments,jdbcType=VARCHAR}, 
      #{deliveryCycle,jdbcType=VARCHAR}, #{installation,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{updateDataTime,jdbcType=TIMESTAMP}, #{originalPurchasePrice,jdbcType=DECIMAL}, 
      #{couponReason,jdbcType=VARCHAR}, #{afterReturnAmount,jdbcType=DECIMAL}, #{afterReturnNum,jdbcType=INTEGER}, 
      #{realInvoiceAmount,jdbcType=DECIMAL}, #{realInvoiceNum,jdbcType=DECIMAL}, #{geContractNo,jdbcType=VARCHAR}, 
      #{geSaleContractNo,jdbcType=VARCHAR}, #{productAudit,jdbcType=INTEGER}, #{firstSendGoodsTime,jdbcType=BIGINT}, 
      #{firstReceiveGoodsTime,jdbcType=BIGINT}, #{sendGoodsTime,jdbcType=BIGINT}, #{receiveGoodsTime,jdbcType=BIGINT}, 
      #{processDescription,jdbcType=VARCHAR}, #{isSendCreateFlag,jdbcType=BOOLEAN}, #{alreadyExpeditingAlarm,jdbcType=BOOLEAN}, 
      #{productBelongIdInfo,jdbcType=VARCHAR}, #{productBelongNameInfo,jdbcType=VARCHAR}, 
      #{spec,jdbcType=VARCHAR}, #{manufacturerName,jdbcType=VARCHAR}, #{registrationNumber,jdbcType=VARCHAR}, 
      #{isHaveAuth,jdbcType=INTEGER}, #{isGift,jdbcType=BOOLEAN}, #{referPrice,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" keyColumn="BUYORDER_GOODS_ID" keyProperty="buyorderGoodsId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderId != null">
        BUYORDER_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="sku != null and sku != ''">
        SKU,
      </if>
      <if test="goodsName != null and goodsName != ''">
        GOODS_NAME,
      </if>
      <if test="brandName != null and brandName != ''">
        BRAND_NAME,
      </if>
      <if test="model != null and model != ''">
        MODEL,
      </if>
      <if test="unitName != null and unitName != ''">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM,
      </if>
      <if test="estimateDeliveryTime != null">
        ESTIMATE_DELIVERY_TIME,
      </if>
      <if test="estimateArrivalTime != null">
        ESTIMATE_ARRIVAL_TIME,
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="insideComments != null and insideComments != ''">
        INSIDE_COMMENTS,
      </if>
      <if test="deliveryCycle != null and deliveryCycle != ''">
        DELIVERY_CYCLE,
      </if>
      <if test="installation != null and installation != ''">
        INSTALLATION,
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="originalPurchasePrice != null">
        ORIGINAL_PURCHASE_PRICE,
      </if>
      <if test="couponReason != null and couponReason != ''">
        COUPON_REASON,
      </if>
      <if test="afterReturnAmount != null">
        AFTER_RETURN_AMOUNT,
      </if>
      <if test="afterReturnNum != null">
        AFTER_RETURN_NUM,
      </if>
      <if test="realInvoiceAmount != null">
        REAL_INVOICE_AMOUNT,
      </if>
      <if test="realInvoiceNum != null">
        REAL_INVOICE_NUM,
      </if>
      <if test="geContractNo != null and geContractNo != ''">
        GE_CONTRACT_NO,
      </if>
      <if test="geSaleContractNo != null and geSaleContractNo != ''">
        GE_SALE_CONTRACT_NO,
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT,
      </if>
      <if test="firstSendGoodsTime != null">
        FIRST_SEND_GOODS_TIME,
      </if>
      <if test="firstReceiveGoodsTime != null">
        FIRST_RECEIVE_GOODS_TIME,
      </if>
      <if test="sendGoodsTime != null">
        SEND_GOODS_TIME,
      </if>
      <if test="receiveGoodsTime != null">
        RECEIVE_GOODS_TIME,
      </if>
      <if test="processDescription != null and processDescription != ''">
        PROCESS_DESCRIPTION,
      </if>
      <if test="isSendCreateFlag != null">
        IS_SEND_CREATE_FLAG,
      </if>
      <if test="alreadyExpeditingAlarm != null">
        ALREADY_EXPEDITING_ALARM,
      </if>
      <if test="productBelongIdInfo != null and productBelongIdInfo != ''">
        PRODUCT_BELONG_ID_INFO,
      </if>
      <if test="productBelongNameInfo != null and productBelongNameInfo != ''">
        PRODUCT_BELONG_NAME_INFO,
      </if>
      <if test="spec != null and spec != ''">
        SPEC,
      </if>
      <if test="manufacturerName != null and manufacturerName != ''">
        MANUFACTURER_NAME,
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        REGISTRATION_NUMBER,
      </if>
      <if test="isHaveAuth != null">
        IS_HAVE_AUTH,
      </if>
      <if test="isGift != null">
        IS_GIFT,
      </if>
      <if test="referPrice != null">
        REFER_PRICE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyorderId != null">
        #{buyorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null and unitName != ''">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="estimateDeliveryTime != null">
        #{estimateDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="estimateArrivalTime != null">
        #{estimateArrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="insideComments != null and insideComments != ''">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCycle != null and deliveryCycle != ''">
        #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="installation != null and installation != ''">
        #{installation,jdbcType=VARCHAR},
      </if>
      <if test="comments != null and comments != ''">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="originalPurchasePrice != null">
        #{originalPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="couponReason != null and couponReason != ''">
        #{couponReason,jdbcType=VARCHAR},
      </if>
      <if test="afterReturnAmount != null">
        #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null">
        #{afterReturnNum,jdbcType=INTEGER},
      </if>
      <if test="realInvoiceAmount != null">
        #{realInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="realInvoiceNum != null">
        #{realInvoiceNum,jdbcType=DECIMAL},
      </if>
      <if test="geContractNo != null and geContractNo != ''">
        #{geContractNo,jdbcType=VARCHAR},
      </if>
      <if test="geSaleContractNo != null and geSaleContractNo != ''">
        #{geSaleContractNo,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        #{productAudit,jdbcType=INTEGER},
      </if>
      <if test="firstSendGoodsTime != null">
        #{firstSendGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="firstReceiveGoodsTime != null">
        #{firstReceiveGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="sendGoodsTime != null">
        #{sendGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="receiveGoodsTime != null">
        #{receiveGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="processDescription != null and processDescription != ''">
        #{processDescription,jdbcType=VARCHAR},
      </if>
      <if test="isSendCreateFlag != null">
        #{isSendCreateFlag,jdbcType=BOOLEAN},
      </if>
      <if test="alreadyExpeditingAlarm != null">
        #{alreadyExpeditingAlarm,jdbcType=BOOLEAN},
      </if>
      <if test="productBelongIdInfo != null and productBelongIdInfo != ''">
        #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null and productBelongNameInfo != ''">
        #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null and manufacturerName != ''">
        #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="isHaveAuth != null">
        #{isHaveAuth,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        #{isGift,jdbcType=BOOLEAN},
      </if>
      <if test="referPrice != null">
        #{referPrice,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderGoods">
    <!--@mbg.generated-->
    update T_BUYORDER_GOODS
    <set>
      <if test="buyorderId != null">
        BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null and unitName != ''">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="estimateDeliveryTime != null">
        ESTIMATE_DELIVERY_TIME = #{estimateDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="estimateArrivalTime != null">
        ESTIMATE_ARRIVAL_TIME = #{estimateArrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="insideComments != null and insideComments != ''">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCycle != null and deliveryCycle != ''">
        DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="installation != null and installation != ''">
        INSTALLATION = #{installation,jdbcType=VARCHAR},
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="originalPurchasePrice != null">
        ORIGINAL_PURCHASE_PRICE = #{originalPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="couponReason != null and couponReason != ''">
        COUPON_REASON = #{couponReason,jdbcType=VARCHAR},
      </if>
      <if test="afterReturnAmount != null">
        AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null">
        AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      </if>
      <if test="realInvoiceAmount != null">
        REAL_INVOICE_AMOUNT = #{realInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="realInvoiceNum != null">
        REAL_INVOICE_NUM = #{realInvoiceNum,jdbcType=DECIMAL},
      </if>
      <if test="geContractNo != null and geContractNo != ''">
        GE_CONTRACT_NO = #{geContractNo,jdbcType=VARCHAR},
      </if>
      <if test="geSaleContractNo != null and geSaleContractNo != ''">
        GE_SALE_CONTRACT_NO = #{geSaleContractNo,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT = #{productAudit,jdbcType=INTEGER},
      </if>
      <if test="firstSendGoodsTime != null">
        FIRST_SEND_GOODS_TIME = #{firstSendGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="firstReceiveGoodsTime != null">
        FIRST_RECEIVE_GOODS_TIME = #{firstReceiveGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="sendGoodsTime != null">
        SEND_GOODS_TIME = #{sendGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="receiveGoodsTime != null">
        RECEIVE_GOODS_TIME = #{receiveGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="processDescription != null and processDescription != ''">
        PROCESS_DESCRIPTION = #{processDescription,jdbcType=VARCHAR},
      </if>
      <if test="isSendCreateFlag != null">
        IS_SEND_CREATE_FLAG = #{isSendCreateFlag,jdbcType=BOOLEAN},
      </if>
      <if test="alreadyExpeditingAlarm != null">
        ALREADY_EXPEDITING_ALARM = #{alreadyExpeditingAlarm,jdbcType=BOOLEAN},
      </if>
      <if test="productBelongIdInfo != null and productBelongIdInfo != ''">
        PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null and productBelongNameInfo != ''">
        PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null and manufacturerName != ''">
        MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="isHaveAuth != null">
        IS_HAVE_AUTH = #{isHaveAuth,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        IS_GIFT = #{isGift,jdbcType=BOOLEAN},
      </if>
      <if test="referPrice != null">
        REFER_PRICE = #{referPrice,jdbcType=DECIMAL},
      </if>
    </set>
    where BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderGoods">
    <!--@mbg.generated-->
    update T_BUYORDER_GOODS
    set BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      ESTIMATE_DELIVERY_TIME = #{estimateDeliveryTime,jdbcType=BIGINT},
      ESTIMATE_ARRIVAL_TIME = #{estimateArrivalTime,jdbcType=BIGINT},
      ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      INSTALLATION = #{installation,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      ORIGINAL_PURCHASE_PRICE = #{originalPurchasePrice,jdbcType=DECIMAL},
      COUPON_REASON = #{couponReason,jdbcType=VARCHAR},
      AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      REAL_INVOICE_AMOUNT = #{realInvoiceAmount,jdbcType=DECIMAL},
      REAL_INVOICE_NUM = #{realInvoiceNum,jdbcType=DECIMAL},
      GE_CONTRACT_NO = #{geContractNo,jdbcType=VARCHAR},
      GE_SALE_CONTRACT_NO = #{geSaleContractNo,jdbcType=VARCHAR},
      PRODUCT_AUDIT = #{productAudit,jdbcType=INTEGER},
      FIRST_SEND_GOODS_TIME = #{firstSendGoodsTime,jdbcType=BIGINT},
      FIRST_RECEIVE_GOODS_TIME = #{firstReceiveGoodsTime,jdbcType=BIGINT},
      SEND_GOODS_TIME = #{sendGoodsTime,jdbcType=BIGINT},
      RECEIVE_GOODS_TIME = #{receiveGoodsTime,jdbcType=BIGINT},
      PROCESS_DESCRIPTION = #{processDescription,jdbcType=VARCHAR},
      IS_SEND_CREATE_FLAG = #{isSendCreateFlag,jdbcType=BOOLEAN},
      ALREADY_EXPEDITING_ALARM = #{alreadyExpeditingAlarm,jdbcType=BOOLEAN},
      PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      IS_HAVE_AUTH = #{isHaveAuth,jdbcType=INTEGER},
      IS_GIFT = #{isGift,jdbcType=BOOLEAN},
      REFER_PRICE = #{referPrice,jdbcType=DECIMAL}
    where BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BUYORDER_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BUYORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.buyorderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="GOODS_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.goodsName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BRAND_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.brandName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UNIT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.unitName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="CURRENCY_UNIT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.currencyUnitId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.arrivalNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ESTIMATE_DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.estimateDeliveryTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ESTIMATE_ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.estimateArrivalTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.arrivalUserId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INSIDE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.insideComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_CYCLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.deliveryCycle,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INSTALLATION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.installation,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.comments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="ORIGINAL_PURCHASE_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.originalPurchasePrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="COUPON_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.couponReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="AFTER_RETURN_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.afterReturnAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="AFTER_RETURN_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.afterReturnNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="REAL_INVOICE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.realInvoiceAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="REAL_INVOICE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.realInvoiceNum,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="GE_CONTRACT_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.geContractNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="GE_SALE_CONTRACT_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.geSaleContractNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.productAudit,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="FIRST_SEND_GOODS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.firstSendGoodsTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="FIRST_RECEIVE_GOODS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.firstReceiveGoodsTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="SEND_GOODS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.sendGoodsTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="RECEIVE_GOODS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.receiveGoodsTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="PROCESS_DESCRIPTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.processDescription,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_SEND_CREATE_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.isSendCreateFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ALREADY_EXPEDITING_ALARM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.alreadyExpeditingAlarm,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_BELONG_ID_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.productBelongIdInfo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_BELONG_NAME_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.productBelongNameInfo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SPEC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.spec,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MANUFACTURER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.manufacturerName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REGISTRATION_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.registrationNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_HAVE_AUTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.isHaveAuth,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_GIFT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.isGift,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="REFER_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.referPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
    </trim>
    where BUYORDER_GOODS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.buyorderGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BUYORDER_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BUYORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyorderId != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.buyorderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsId != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sku != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsName != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.goodsName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BRAND_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.brandName != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.brandName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.model != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UNIT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unitName != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.unitName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.price != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="CURRENCY_UNIT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.currencyUnitId != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.currencyUnitId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalNum != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.arrivalNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ESTIMATE_DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.estimateDeliveryTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.estimateDeliveryTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ESTIMATE_ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.estimateArrivalTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.estimateArrivalTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalUserId != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.arrivalUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalStatus != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSIDE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.insideComments != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.insideComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_CYCLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryCycle != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.deliveryCycle,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSTALLATION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.installation != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.installation,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.comments != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.comments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateDataTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORIGINAL_PURCHASE_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.originalPurchasePrice != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.originalPurchasePrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="COUPON_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.couponReason != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.couponReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_RETURN_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterReturnAmount != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.afterReturnAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_RETURN_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterReturnNum != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.afterReturnNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_INVOICE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realInvoiceAmount != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.realInvoiceAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_INVOICE_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realInvoiceNum != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.realInvoiceNum,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="GE_CONTRACT_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.geContractNo != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.geContractNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="GE_SALE_CONTRACT_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.geSaleContractNo != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.geSaleContractNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productAudit != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.productAudit,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_SEND_GOODS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstSendGoodsTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.firstSendGoodsTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_RECEIVE_GOODS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstReceiveGoodsTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.firstReceiveGoodsTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="SEND_GOODS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sendGoodsTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.sendGoodsTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECEIVE_GOODS_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveGoodsTime != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.receiveGoodsTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROCESS_DESCRIPTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.processDescription != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.processDescription,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_SEND_CREATE_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isSendCreateFlag != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.isSendCreateFlag,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ALREADY_EXPEDITING_ALARM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.alreadyExpeditingAlarm != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.alreadyExpeditingAlarm,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_BELONG_ID_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productBelongIdInfo != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.productBelongIdInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_BELONG_NAME_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productBelongNameInfo != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.productBelongNameInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPEC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spec != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.spec,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MANUFACTURER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.manufacturerName != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.manufacturerName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="REGISTRATION_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.registrationNumber != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.registrationNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_HAVE_AUTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isHaveAuth != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.isHaveAuth,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_GIFT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isGift != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.isGift,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFER_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.referPrice != null">
            when BUYORDER_GOODS_ID = #{item.buyorderGoodsId,jdbcType=INTEGER} then #{item.referPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
    </trim>
    where BUYORDER_GOODS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.buyorderGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="BUYORDER_GOODS_ID" keyProperty="buyorderGoodsId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_GOODS
    (BUYORDER_ID, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME, MODEL, UNIT_NAME, PRICE, CURRENCY_UNIT_ID, 
      NUM, ARRIVAL_NUM, ESTIMATE_DELIVERY_TIME, ESTIMATE_ARRIVAL_TIME, ARRIVAL_USER_ID, 
      ARRIVAL_STATUS, ARRIVAL_TIME, IS_DELETE, INSIDE_COMMENTS, DELIVERY_CYCLE, INSTALLATION, 
      COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, UPDATE_DATA_TIME, ORIGINAL_PURCHASE_PRICE, 
      COUPON_REASON, AFTER_RETURN_AMOUNT, AFTER_RETURN_NUM, REAL_INVOICE_AMOUNT, REAL_INVOICE_NUM, 
      GE_CONTRACT_NO, GE_SALE_CONTRACT_NO, PRODUCT_AUDIT, FIRST_SEND_GOODS_TIME, FIRST_RECEIVE_GOODS_TIME, 
      SEND_GOODS_TIME, RECEIVE_GOODS_TIME, PROCESS_DESCRIPTION, IS_SEND_CREATE_FLAG, 
      ALREADY_EXPEDITING_ALARM, PRODUCT_BELONG_ID_INFO, PRODUCT_BELONG_NAME_INFO, SPEC, 
      MANUFACTURER_NAME, REGISTRATION_NUMBER, IS_HAVE_AUTH, IS_GIFT, REFER_PRICE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.buyorderId,jdbcType=INTEGER}, #{item.goodsId,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR}, 
        #{item.goodsName,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR}, 
        #{item.unitName,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL}, #{item.currencyUnitId,jdbcType=INTEGER}, 
        #{item.num,jdbcType=INTEGER}, #{item.arrivalNum,jdbcType=INTEGER}, #{item.estimateDeliveryTime,jdbcType=BIGINT}, 
        #{item.estimateArrivalTime,jdbcType=BIGINT}, #{item.arrivalUserId,jdbcType=INTEGER}, 
        #{item.arrivalStatus,jdbcType=BOOLEAN}, #{item.arrivalTime,jdbcType=BIGINT}, #{item.isDelete,jdbcType=BOOLEAN}, 
        #{item.insideComments,jdbcType=VARCHAR}, #{item.deliveryCycle,jdbcType=VARCHAR}, 
        #{item.installation,jdbcType=VARCHAR}, #{item.comments,jdbcType=VARCHAR}, #{item.addTime,jdbcType=BIGINT}, 
        #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER}, 
        #{item.updateDataTime,jdbcType=TIMESTAMP}, #{item.originalPurchasePrice,jdbcType=DECIMAL}, 
        #{item.couponReason,jdbcType=VARCHAR}, #{item.afterReturnAmount,jdbcType=DECIMAL}, 
        #{item.afterReturnNum,jdbcType=INTEGER}, #{item.realInvoiceAmount,jdbcType=DECIMAL}, 
        #{item.realInvoiceNum,jdbcType=DECIMAL}, #{item.geContractNo,jdbcType=VARCHAR}, 
        #{item.geSaleContractNo,jdbcType=VARCHAR}, #{item.productAudit,jdbcType=INTEGER}, 
        #{item.firstSendGoodsTime,jdbcType=BIGINT}, #{item.firstReceiveGoodsTime,jdbcType=BIGINT}, 
        #{item.sendGoodsTime,jdbcType=BIGINT}, #{item.receiveGoodsTime,jdbcType=BIGINT}, 
        #{item.processDescription,jdbcType=VARCHAR}, #{item.isSendCreateFlag,jdbcType=BOOLEAN}, 
        #{item.alreadyExpeditingAlarm,jdbcType=BOOLEAN}, #{item.productBelongIdInfo,jdbcType=VARCHAR}, 
        #{item.productBelongNameInfo,jdbcType=VARCHAR}, #{item.spec,jdbcType=VARCHAR}, 
        #{item.manufacturerName,jdbcType=VARCHAR}, #{item.registrationNumber,jdbcType=VARCHAR}, 
        #{item.isHaveAuth,jdbcType=INTEGER}, #{item.isGift,jdbcType=BOOLEAN}, #{item.referPrice,jdbcType=DECIMAL}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="BUYORDER_GOODS_ID" keyProperty="buyorderGoodsId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderGoodsId != null">
        BUYORDER_GOODS_ID,
      </if>
      BUYORDER_ID,
      GOODS_ID,
      SKU,
      GOODS_NAME,
      BRAND_NAME,
      MODEL,
      UNIT_NAME,
      PRICE,
      CURRENCY_UNIT_ID,
      NUM,
      ARRIVAL_NUM,
      ESTIMATE_DELIVERY_TIME,
      ESTIMATE_ARRIVAL_TIME,
      ARRIVAL_USER_ID,
      ARRIVAL_STATUS,
      ARRIVAL_TIME,
      IS_DELETE,
      INSIDE_COMMENTS,
      DELIVERY_CYCLE,
      INSTALLATION,
      COMMENTS,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      UPDATE_DATA_TIME,
      ORIGINAL_PURCHASE_PRICE,
      COUPON_REASON,
      AFTER_RETURN_AMOUNT,
      AFTER_RETURN_NUM,
      REAL_INVOICE_AMOUNT,
      REAL_INVOICE_NUM,
      GE_CONTRACT_NO,
      GE_SALE_CONTRACT_NO,
      PRODUCT_AUDIT,
      FIRST_SEND_GOODS_TIME,
      FIRST_RECEIVE_GOODS_TIME,
      SEND_GOODS_TIME,
      RECEIVE_GOODS_TIME,
      PROCESS_DESCRIPTION,
      IS_SEND_CREATE_FLAG,
      ALREADY_EXPEDITING_ALARM,
      PRODUCT_BELONG_ID_INFO,
      PRODUCT_BELONG_NAME_INFO,
      SPEC,
      MANUFACTURER_NAME,
      REGISTRATION_NUMBER,
      IS_HAVE_AUTH,
      IS_GIFT,
      REFER_PRICE,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderGoodsId != null">
        #{buyorderGoodsId,jdbcType=INTEGER},
      </if>
      #{buyorderId,jdbcType=INTEGER},
      #{goodsId,jdbcType=INTEGER},
      #{sku,jdbcType=VARCHAR},
      #{goodsName,jdbcType=VARCHAR},
      #{brandName,jdbcType=VARCHAR},
      #{model,jdbcType=VARCHAR},
      #{unitName,jdbcType=VARCHAR},
      #{price,jdbcType=DECIMAL},
      #{currencyUnitId,jdbcType=INTEGER},
      #{num,jdbcType=INTEGER},
      #{arrivalNum,jdbcType=INTEGER},
      #{estimateDeliveryTime,jdbcType=BIGINT},
      #{estimateArrivalTime,jdbcType=BIGINT},
      #{arrivalUserId,jdbcType=INTEGER},
      #{arrivalStatus,jdbcType=BOOLEAN},
      #{arrivalTime,jdbcType=BIGINT},
      #{isDelete,jdbcType=BOOLEAN},
      #{insideComments,jdbcType=VARCHAR},
      #{deliveryCycle,jdbcType=VARCHAR},
      #{installation,jdbcType=VARCHAR},
      #{comments,jdbcType=VARCHAR},
      #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER},
      #{updateDataTime,jdbcType=TIMESTAMP},
      #{originalPurchasePrice,jdbcType=DECIMAL},
      #{couponReason,jdbcType=VARCHAR},
      #{afterReturnAmount,jdbcType=DECIMAL},
      #{afterReturnNum,jdbcType=INTEGER},
      #{realInvoiceAmount,jdbcType=DECIMAL},
      #{realInvoiceNum,jdbcType=DECIMAL},
      #{geContractNo,jdbcType=VARCHAR},
      #{geSaleContractNo,jdbcType=VARCHAR},
      #{productAudit,jdbcType=INTEGER},
      #{firstSendGoodsTime,jdbcType=BIGINT},
      #{firstReceiveGoodsTime,jdbcType=BIGINT},
      #{sendGoodsTime,jdbcType=BIGINT},
      #{receiveGoodsTime,jdbcType=BIGINT},
      #{processDescription,jdbcType=VARCHAR},
      #{isSendCreateFlag,jdbcType=BOOLEAN},
      #{alreadyExpeditingAlarm,jdbcType=BOOLEAN},
      #{productBelongIdInfo,jdbcType=VARCHAR},
      #{productBelongNameInfo,jdbcType=VARCHAR},
      #{spec,jdbcType=VARCHAR},
      #{manufacturerName,jdbcType=VARCHAR},
      #{registrationNumber,jdbcType=VARCHAR},
      #{isHaveAuth,jdbcType=INTEGER},
      #{isGift,jdbcType=BOOLEAN},
      #{referPrice,jdbcType=DECIMAL},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="buyorderGoodsId != null">
        BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER},
      </if>
      BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      ESTIMATE_DELIVERY_TIME = #{estimateDeliveryTime,jdbcType=BIGINT},
      ESTIMATE_ARRIVAL_TIME = #{estimateArrivalTime,jdbcType=BIGINT},
      ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      INSTALLATION = #{installation,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      ORIGINAL_PURCHASE_PRICE = #{originalPurchasePrice,jdbcType=DECIMAL},
      COUPON_REASON = #{couponReason,jdbcType=VARCHAR},
      AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      REAL_INVOICE_AMOUNT = #{realInvoiceAmount,jdbcType=DECIMAL},
      REAL_INVOICE_NUM = #{realInvoiceNum,jdbcType=DECIMAL},
      GE_CONTRACT_NO = #{geContractNo,jdbcType=VARCHAR},
      GE_SALE_CONTRACT_NO = #{geSaleContractNo,jdbcType=VARCHAR},
      PRODUCT_AUDIT = #{productAudit,jdbcType=INTEGER},
      FIRST_SEND_GOODS_TIME = #{firstSendGoodsTime,jdbcType=BIGINT},
      FIRST_RECEIVE_GOODS_TIME = #{firstReceiveGoodsTime,jdbcType=BIGINT},
      SEND_GOODS_TIME = #{sendGoodsTime,jdbcType=BIGINT},
      RECEIVE_GOODS_TIME = #{receiveGoodsTime,jdbcType=BIGINT},
      PROCESS_DESCRIPTION = #{processDescription,jdbcType=VARCHAR},
      IS_SEND_CREATE_FLAG = #{isSendCreateFlag,jdbcType=BOOLEAN},
      ALREADY_EXPEDITING_ALARM = #{alreadyExpeditingAlarm,jdbcType=BOOLEAN},
      PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      IS_HAVE_AUTH = #{isHaveAuth,jdbcType=INTEGER},
      IS_GIFT = #{isGift,jdbcType=BOOLEAN},
      REFER_PRICE = #{referPrice,jdbcType=DECIMAL},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="BUYORDER_GOODS_ID" keyProperty="buyorderGoodsId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyorderGoods" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderGoodsId != null">
        BUYORDER_GOODS_ID,
      </if>
      <if test="buyorderId != null">
        BUYORDER_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="sku != null and sku != ''">
        SKU,
      </if>
      <if test="goodsName != null and goodsName != ''">
        GOODS_NAME,
      </if>
      <if test="brandName != null and brandName != ''">
        BRAND_NAME,
      </if>
      <if test="model != null and model != ''">
        MODEL,
      </if>
      <if test="unitName != null and unitName != ''">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM,
      </if>
      <if test="estimateDeliveryTime != null">
        ESTIMATE_DELIVERY_TIME,
      </if>
      <if test="estimateArrivalTime != null">
        ESTIMATE_ARRIVAL_TIME,
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="insideComments != null and insideComments != ''">
        INSIDE_COMMENTS,
      </if>
      <if test="deliveryCycle != null and deliveryCycle != ''">
        DELIVERY_CYCLE,
      </if>
      <if test="installation != null and installation != ''">
        INSTALLATION,
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="originalPurchasePrice != null">
        ORIGINAL_PURCHASE_PRICE,
      </if>
      <if test="couponReason != null and couponReason != ''">
        COUPON_REASON,
      </if>
      <if test="afterReturnAmount != null">
        AFTER_RETURN_AMOUNT,
      </if>
      <if test="afterReturnNum != null">
        AFTER_RETURN_NUM,
      </if>
      <if test="realInvoiceAmount != null">
        REAL_INVOICE_AMOUNT,
      </if>
      <if test="realInvoiceNum != null">
        REAL_INVOICE_NUM,
      </if>
      <if test="geContractNo != null and geContractNo != ''">
        GE_CONTRACT_NO,
      </if>
      <if test="geSaleContractNo != null and geSaleContractNo != ''">
        GE_SALE_CONTRACT_NO,
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT,
      </if>
      <if test="firstSendGoodsTime != null">
        FIRST_SEND_GOODS_TIME,
      </if>
      <if test="firstReceiveGoodsTime != null">
        FIRST_RECEIVE_GOODS_TIME,
      </if>
      <if test="sendGoodsTime != null">
        SEND_GOODS_TIME,
      </if>
      <if test="receiveGoodsTime != null">
        RECEIVE_GOODS_TIME,
      </if>
      <if test="processDescription != null and processDescription != ''">
        PROCESS_DESCRIPTION,
      </if>
      <if test="isSendCreateFlag != null">
        IS_SEND_CREATE_FLAG,
      </if>
      <if test="alreadyExpeditingAlarm != null">
        ALREADY_EXPEDITING_ALARM,
      </if>
      <if test="productBelongIdInfo != null and productBelongIdInfo != ''">
        PRODUCT_BELONG_ID_INFO,
      </if>
      <if test="productBelongNameInfo != null and productBelongNameInfo != ''">
        PRODUCT_BELONG_NAME_INFO,
      </if>
      <if test="spec != null and spec != ''">
        SPEC,
      </if>
      <if test="manufacturerName != null and manufacturerName != ''">
        MANUFACTURER_NAME,
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        REGISTRATION_NUMBER,
      </if>
      <if test="isHaveAuth != null">
        IS_HAVE_AUTH,
      </if>
      <if test="isGift != null">
        IS_GIFT,
      </if>
      <if test="referPrice != null">
        REFER_PRICE,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderGoodsId != null">
        #{buyorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="buyorderId != null">
        #{buyorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null and unitName != ''">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="estimateDeliveryTime != null">
        #{estimateDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="estimateArrivalTime != null">
        #{estimateArrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="insideComments != null and insideComments != ''">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCycle != null and deliveryCycle != ''">
        #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="installation != null and installation != ''">
        #{installation,jdbcType=VARCHAR},
      </if>
      <if test="comments != null and comments != ''">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="originalPurchasePrice != null">
        #{originalPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="couponReason != null and couponReason != ''">
        #{couponReason,jdbcType=VARCHAR},
      </if>
      <if test="afterReturnAmount != null">
        #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null">
        #{afterReturnNum,jdbcType=INTEGER},
      </if>
      <if test="realInvoiceAmount != null">
        #{realInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="realInvoiceNum != null">
        #{realInvoiceNum,jdbcType=DECIMAL},
      </if>
      <if test="geContractNo != null and geContractNo != ''">
        #{geContractNo,jdbcType=VARCHAR},
      </if>
      <if test="geSaleContractNo != null and geSaleContractNo != ''">
        #{geSaleContractNo,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        #{productAudit,jdbcType=INTEGER},
      </if>
      <if test="firstSendGoodsTime != null">
        #{firstSendGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="firstReceiveGoodsTime != null">
        #{firstReceiveGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="sendGoodsTime != null">
        #{sendGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="receiveGoodsTime != null">
        #{receiveGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="processDescription != null and processDescription != ''">
        #{processDescription,jdbcType=VARCHAR},
      </if>
      <if test="isSendCreateFlag != null">
        #{isSendCreateFlag,jdbcType=BOOLEAN},
      </if>
      <if test="alreadyExpeditingAlarm != null">
        #{alreadyExpeditingAlarm,jdbcType=BOOLEAN},
      </if>
      <if test="productBelongIdInfo != null and productBelongIdInfo != ''">
        #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null and productBelongNameInfo != ''">
        #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null and manufacturerName != ''">
        #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="isHaveAuth != null">
        #{isHaveAuth,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        #{isGift,jdbcType=BOOLEAN},
      </if>
      <if test="referPrice != null">
        #{referPrice,jdbcType=DECIMAL},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="buyorderGoodsId != null">
        BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="buyorderId != null">
        BUYORDER_ID = #{buyorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null and unitName != ''">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null">
        ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="estimateDeliveryTime != null">
        ESTIMATE_DELIVERY_TIME = #{estimateDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="estimateArrivalTime != null">
        ESTIMATE_ARRIVAL_TIME = #{estimateArrivalTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="insideComments != null and insideComments != ''">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCycle != null and deliveryCycle != ''">
        DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="installation != null and installation != ''">
        INSTALLATION = #{installation,jdbcType=VARCHAR},
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="originalPurchasePrice != null">
        ORIGINAL_PURCHASE_PRICE = #{originalPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="couponReason != null and couponReason != ''">
        COUPON_REASON = #{couponReason,jdbcType=VARCHAR},
      </if>
      <if test="afterReturnAmount != null">
        AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null">
        AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      </if>
      <if test="realInvoiceAmount != null">
        REAL_INVOICE_AMOUNT = #{realInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="realInvoiceNum != null">
        REAL_INVOICE_NUM = #{realInvoiceNum,jdbcType=DECIMAL},
      </if>
      <if test="geContractNo != null and geContractNo != ''">
        GE_CONTRACT_NO = #{geContractNo,jdbcType=VARCHAR},
      </if>
      <if test="geSaleContractNo != null and geSaleContractNo != ''">
        GE_SALE_CONTRACT_NO = #{geSaleContractNo,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT = #{productAudit,jdbcType=INTEGER},
      </if>
      <if test="firstSendGoodsTime != null">
        FIRST_SEND_GOODS_TIME = #{firstSendGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="firstReceiveGoodsTime != null">
        FIRST_RECEIVE_GOODS_TIME = #{firstReceiveGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="sendGoodsTime != null">
        SEND_GOODS_TIME = #{sendGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="receiveGoodsTime != null">
        RECEIVE_GOODS_TIME = #{receiveGoodsTime,jdbcType=BIGINT},
      </if>
      <if test="processDescription != null and processDescription != ''">
        PROCESS_DESCRIPTION = #{processDescription,jdbcType=VARCHAR},
      </if>
      <if test="isSendCreateFlag != null">
        IS_SEND_CREATE_FLAG = #{isSendCreateFlag,jdbcType=BOOLEAN},
      </if>
      <if test="alreadyExpeditingAlarm != null">
        ALREADY_EXPEDITING_ALARM = #{alreadyExpeditingAlarm,jdbcType=BOOLEAN},
      </if>
      <if test="productBelongIdInfo != null and productBelongIdInfo != ''">
        PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null and productBelongNameInfo != ''">
        PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null and manufacturerName != ''">
        MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="isHaveAuth != null">
        IS_HAVE_AUTH = #{isHaveAuth,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        IS_GIFT = #{isGift,jdbcType=BOOLEAN},
      </if>
      <if test="referPrice != null">
        REFER_PRICE = #{referPrice,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-12-01-->
  <select id="findByBuyorderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BUYORDER_GOODS
        where BUYORDER_ID=#{buyorderId,jdbcType=INTEGER}
        and IS_DELETE = 0
    </select>
</mapper>