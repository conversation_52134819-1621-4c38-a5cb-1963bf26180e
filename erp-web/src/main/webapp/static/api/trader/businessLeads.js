let vm = null
const sendThis = (_this) => {
    vm = _this;
}

function add(data) {
    return axios({
        url: '/businessLeads/add.do',
        method: 'post',
        data: data
    })
}


function getLeadsListByDto(data) {
    return axios({
        url: '/businessLeads/getLeadsListByDto.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}


function getLeadsDetails(data) {
    return axios({
        url: '/businessLeads/getOne.do',
        method: 'post',
        params: data
    })
}

function updateLeadsFirstFollowTime(data) {
    return axios({
        url: '/businessLeads/updateLeadsFirstFollowTime.do',
        method: 'post',
        params: data
    })
}

function updateLeadsFirstFollowStatus(data) {
    return axios({
        url: '/businessLeads/updateLeadsFollowStatus.do',
        method: 'post',
        params: data
    })
}


function updateLeads(data) {
    return axios({
        url: '/businessLeads/update.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

function closedLeads(data) {
    return axios({
        url: '/businessLeads/closedLeads.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

function updateLeadsStatus(data) {
    return axios({
        url: '/businessLeads/updateLeadsStatus.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}




function todo(value) {
    vm.emptyDialogTableVisible = value;
}

function addTraderContract() {

    layer.config({
        extend: 'vedeng.com/style.css', //加载您的扩展样式
        skin: 'vedeng.com'
    });
    var layerParams = $("#concat").attr('layerParams');
    if (typeof (layerParams) == 'undefined') {
        alert('参数错误');
    } else {
        layerParams = $.parseJSON(layerParams);
    }
    var link = layerParams.link;
    if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 == link.length)) {
        link += "pop=pop";
    } else if (link.indexOf("?") < 0) {
        link += "?pop=pop";
    } else if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 != link.length)) {
        link += "&pop=pop";
    }
    var index = layer.open({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        //area: 'auto',
        area: [layerParams.width, layerParams.height],
        title: layerParams.title,
        content: layerParams.noEncodeURI ? encodeURI(link) : encodeURI(encodeURI(link)),
        success: function (layero, index) {
            //layer.iframeAuto(index);
        }
    });
}


function setBusinessLeadsTraderNameByEye(value) {
    layer.closeAll();
    vm.businessLeads.traderName = value;
    vm.businessLeads.traderId = '';
    if (!vm.displaySelectOp) {
        vm.businessLeads.belongerId = leadsInfo.creater;
        vm.businessLeads.belonger = leadsInfo.creatername;
    }
}

//天眼查参数
function upperCase(traderName) {

    layer.config({
        extend: 'vedeng.com/style.css',
        skin: 'vedeng.com'
    });

    var link = "/trader/customer/queryEyeCheck.do?type=1";
    if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 == link.length)) {
        link += "pop=pop";
    } else if (link.indexOf("?") < 0) {
        link += "?pop=pop";
    } else if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 != link.length)) {
        link += "&pop=pop";
    }
    var index = layer.open({
        type: 2,
        shadeClose: false,
        area: ['40%', '320px'],
        title: '客户列表',
        content: encodeURI(link + "&traderName=" + traderName),
        success: function (layero, index) {

        }
    });
    return false;
}

function toTop(data) {
    return axios({
        url: '/businessLeads/top.do',
        method: 'post',
        params: data
    })
}

function toUnTop(data) {
    return axios({
        url: '/businessLeads/unTop.do',
        method: 'post',
        params: data
    })
}

function assign(data) {
    return axios({
        url: '/businessLeads/assign.do',
        method: 'post',
        data: data
    })
}

function page(data) {
    return axios({
        url: '/businessLeads/page.do',
        method: 'post',
        data: data
    }).then(response => response.data.data);
}
