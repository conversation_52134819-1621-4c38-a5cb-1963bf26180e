package com.vedeng.goods.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.R;
import com.vedeng.goods.dto.FairValueDto;
import com.vedeng.goods.service.FairValueApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/29 13:17
 **/
@Controller
@RequestMapping("/goods/finance")
@Slf4j
public class GoodsFinanceController extends BaseController {

    @Autowired
    private FairValueApiService fairValueApiService;

    @RequestMapping("/edit")
    public ModelAndView edit(@RequestParam(required = false,defaultValue = "0") Integer goodsFinanceId){
        ModelAndView mv = new ModelAndView();
        mv.addObject("goodsFinanceId", goodsFinanceId);
        mv.setViewName("vue/view/goodsFinance/edit");
        return mv;
    }

    @RequestMapping("/audit")
    public ModelAndView audit(@RequestParam(required = false,defaultValue = "0") Integer goodsFinanceId){
        ModelAndView mv = new ModelAndView();
        mv.addObject("goodsFinanceId", goodsFinanceId);
        mv.setViewName("vue/view/goodsFinance/audit");
        return mv;
    }

    @RequestMapping("/upLoadFile")
    public ModelAndView upLoadFile(){
        ModelAndView mv = new ModelAndView();
        mv.setViewName("vue/view/goodsFinance/upLoadFile");
        return mv;
    }

    /**
     * 查看历史公允价信息
     * <AUTHOR>
     * @param fairValueId
     * @return
     */
    @RequestMapping("/viewHistoryFairPrice")
    @NoNeedAccessAuthorization
    public ModelAndView viewHistoryFairPrice(Integer fairValueId){
        ModelAndView mv = new ModelAndView("vue/view/goodsFinance/fair_history_price");
        mv.addObject("fairValueId", fairValueId);
        return mv;
    }

    /**
     * 公允价-历史公允价信息
     * <AUTHOR>
     * @param fairValueId
     * @return
     */
    @RequestMapping("/getFairPriceInfoDto")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<FairValueDto> getFairPriceInfoDto(@RequestParam Integer fairValueId){
        return R.success(fairValueApiService.getFairPriceDto(fairValueId));
    }

    /**
     * 查看当月公允价销售单计算信息
     * <AUTHOR>
     * @param fairValueId
     * @return
     */
    @RequestMapping("/viewFairPriceSales")
    @NoNeedAccessAuthorization
    public ModelAndView viewFairPriceSales(Integer fairValueId){
        ModelAndView mv = new ModelAndView("vue/view/goodsFinance/fair_price_sales");
        mv.addObject("fairValueId", fairValueId);
        return mv;
    }
}
