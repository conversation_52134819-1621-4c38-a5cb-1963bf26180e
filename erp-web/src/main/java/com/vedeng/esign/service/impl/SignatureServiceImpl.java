package com.vedeng.esign.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.esign.service.SignatureService;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.goods.enums.ManufacturerStatusEnum;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.service.ManufacturerService;
import com.vedeng.system.service.ThreeCertificatesStamp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/10 13:58
 **/
@Service
@Slf4j
public class SignatureServiceImpl implements SignatureService {


    @Autowired
    private ThreeCertificatesStamp threeCertificatesStamp;
    @Resource
    private ManufacturerMapper manufacturerMapper;
    @Resource
    private FirstEngageMapper firstEngageMapper;
    @Resource
    private ManufacturerService manufacturerService;



    @Override
    public void checkManufacture(Manufacturer manufacturer, User sessUser) {
        if (null == manufacturer || null == manufacturer.getManufacturerId()) {
            throw new ShowErrorMsgException("找不到对应的生产企业信息！");
        }
        if (ManufacturerStatusEnum.CHECK_FAIL.getStatus().equals(manufacturer.getStatus()) && EmptyUtils.isBlank(manufacturer.getReason())) {
            throw new ShowErrorMsgException("审核不通过原因不能为空！");
        }
        Integer manufacturerId = manufacturer.getManufacturerId();
        Integer status = manufacturer.getStatus();
        // 记录已发起电子签章请求
        Manufacturer updateDo = new Manufacturer();
        updateDo.setManufacturerId(manufacturerId);
        updateDo.setSignature(1);
        updateDo.setUpdateTime(new Date());
        updateDo.setUpdateNo(sessUser.getUserId());
        manufacturerMapper.updateByPrimaryKeySelective(updateDo);

        // 审核通过
        if (status.equals(ManufacturerStatusEnum.CHECK_SUCCESS.getStatus())) {
            // 调用电子签章服务
            try {
                threeCertificatesStamp.certificatesStamp(manufacturer.getManufacturerId(), sessUser);
            } catch (Exception e) {
                log.error(e.toString());
            }
        }
    }

    @Override
    public void checkFirstengage(FirstEngage firstEngage, User sessUser) {
        // 首营信息id
        if(null == firstEngage || null == firstEngage.getFirstEngageId()){
            throw new ShowErrorMsgException("找不到对应的首营信息！");
        }
        if (CommonConstants.FIRST_ENGAGE_STATUS_2.equals(firstEngage.getStatus()) && EmptyUtils.isBlank(firstEngage.getReason())) {
            throw new ShowErrorMsgException("审核不通过原因不能为空！");
        }
        FirstEngage originFirstEngage = firstEngageMapper.selectByPrimaryKey(firstEngage.getFirstEngageId());

        if (GoodsCheckStatusEnum.PRE.getStatus() == firstEngage.getStatus() && !manufacturerService.isValidByRegistrationNumber(originFirstEngage.getRegistrationNumberId())) {
            throw new ShowErrorMsgException("此注册证的【生产厂商】未审核通过，无法提交注册证审核！");
        }

        // 修改电子签章状态
        FirstEngage update = new FirstEngage();
        update.setFirstEngageId(firstEngage.getFirstEngageId());
        update.setUpdater(sessUser.getUserId());
        update.setSignature(1);
        firstEngageMapper.updateByPrimaryKeySelective(update);

        // 添加审核记录

        // 审核通过，同时发起电子签章 处理begin
        if (firstEngage.getStatus() == 3) {
            // hollis  调用电子签章服务 begin
            log.info("调用电子签章服务 begin");
            try {
                threeCertificatesStamp.certificatesStampRegistration(firstEngage.getFirstEngageId(), sessUser);
            } catch (Exception e) {
                log.error(e.toString());
            }
            log.info("调用电子签章服务 end");
            // hollis  调用电子签章服务 end
        }
        // 审核通过，同时发起电子签章 处理 end
    }

}
