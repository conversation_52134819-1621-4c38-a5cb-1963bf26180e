package com.vedeng.erp.kingdee.repository.mappers;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity;

/**
 * <AUTHOR>
 */
public interface KingDeeSaleOutStockMapper {
    /**
     * delete by primary key
     *
     * @param kingDeeSaleOutStockId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer kingDeeSaleOutStockId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeSaleOutStockEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeSaleOutStockEntity record);

    /**
     * select by primary key
     *
     * @param kingDeeSaleOutStockId primary key
     * @return object by primary key
     */
    KingDeeSaleOutStockEntity selectByPrimaryKey(Integer kingDeeSaleOutStockId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeSaleOutStockEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeSaleOutStockEntity record);

    KingDeeSaleOutStockEntity findByFBillNo(@Param("fBillNo")String fBillNo);


}