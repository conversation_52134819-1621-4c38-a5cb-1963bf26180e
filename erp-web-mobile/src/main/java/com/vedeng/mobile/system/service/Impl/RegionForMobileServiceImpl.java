package com.vedeng.mobile.system.service.Impl;

import com.vedeng.mobile.constant.ErpMobileConst;
import com.vedeng.mobile.system.dto.RegionDto;
import com.vedeng.mobile.system.dto.RegionForMobile;
import com.vedeng.mobile.system.dto.RegionInnerDto;
import com.vedeng.mobile.system.mapper.RegionMapper;
import com.vedeng.mobile.system.service.RegionService;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022/1/10 17:18
 */
@Service
@RequiredArgsConstructor
public class RegionForMobileServiceImpl implements RegionService {

    private final RegionMapper regionMapper;


    @Override
    public RegionDto queryAllRegions() {

        Integer topRegionId = ErpMobileConst.Number.ONE_HUNDRED_THOUSAND;

        // 查询所有该parentId下级地区(递归)
        List<RegionInnerDto> regionInnerDtoList = regionMapper.queryRegionByTopId(topRegionId);

        // 循环两次设置currentRegionCount
        regionInnerDtoList.forEach(regionInnerDto -> {
            if (Objects.isNull(regionInnerDto.getRegionList())) {
                regionInnerDto.setCurrentRegionCount(ErpMobileConst.Number.ZERO);
                return;
            }
            regionInnerDto.setCurrentRegionCount(regionInnerDto.getRegionList().size());
            regionInnerDto.getRegionList().forEach(anotherRegionInnerDto -> {
                if (Objects.isNull(anotherRegionInnerDto.getRegionList())) {
                    anotherRegionInnerDto.setCurrentRegionCount(ErpMobileConst.Number.ZERO);
                    return;
                }
                anotherRegionInnerDto.setCurrentRegionCount(anotherRegionInnerDto.getRegionList().size());
            });
        });

        return new RegionDto(regionInnerDtoList);
    }

    public RegionInnerDto queryRegionByThreeId(Integer regionId) {
        List<RegionInnerDto> regionInnerDto = regionMapper.queryRegionByThreeId(regionId);
        if(regionInnerDto.size() == 0){
            return null;
        }
        return regionInnerDto.get(0);

    }


    @Override
    public RegionForMobile queryRegionLevelTwo(@Param("parentId") Integer topRegionId){
        List<RegionInnerDto> list = regionMapper.queryRegionLevelTwo(topRegionId);
        return new RegionForMobile(list);
    }

    @Override
    public RegionForMobile queryRegionByParentId(Integer topRegionId) {
        List<RegionInnerDto> list = regionMapper.queryRegionByParentId(topRegionId);
        return new RegionForMobile(list);
    }


}
