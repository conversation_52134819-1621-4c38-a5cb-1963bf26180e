package com.vedeng.goods.manager.validator;

import com.baidu.unbiz.fluentvalidator.FluentValidator;
import com.baidu.unbiz.fluentvalidator.Result;
import com.baidu.unbiz.fluentvalidator.ValidatorContext;
import com.baidu.unbiz.fluentvalidator.ValidatorHandler;
import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum;
import com.vedeng.goods.manager.rule.GoodsValidTypeEnum;
import com.vedeng.goods.manager.rule.GoodsValidatedRule;
import com.vedeng.goods.manager.GoodsValidationRuleLoader;
import com.vedeng.goods.manager.validator.model.GoodsValidObject;
import com.vedeng.goods.model.vo.GoodsLevelVo;
import com.vedeng.goods.service.GoodsCommonService;
import com.vedeng.goods.utils.GoodsStorageConditionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
public abstract class GoodsValidator<T extends GoodsValidObject> {

    private static final Map<Class<?>, PropertyDescriptor[]> PROPERTY_DESC_CACHE = new LinkedHashMap<>();

    private final GoodsValidTypeEnum goodsType;

    protected GoodsValidContext goodsValidContext;


    GoodsValidator(GoodsValidTypeEnum typeEnum) {
        if (typeEnum == null) {
            throw new IllegalArgumentException();
        }
        this.goodsType = typeEnum;

       List< GoodsValidatedRule > goodsValidatedRules=SpringContextHolder.getBean(GoodsCommonService.class)
               .getAllValidatedRule(getType().getType());
//                GoodsValidationRuleLoader.getInstance()
//                .loadFromApolloConfig(getType().getApolloConfigKey());
        this.goodsValidContext = new GoodsValidContext(goodsValidatedRules);
    }


    protected static PropertyDescriptor[] getPropertyDesc(Class<?> clazz) {
        PropertyDescriptor[] propertyDescriptors = PROPERTY_DESC_CACHE.get(clazz);
        if (propertyDescriptors != null) {
            return propertyDescriptors;
        }

        synchronized (SkuBasicValidator.class) {
            if (!PROPERTY_DESC_CACHE.containsKey(clazz)) {
                BeanInfo beanInfo;
                try {
                    beanInfo = Introspector.getBeanInfo(clazz);
                } catch (IntrospectionException e) {
                    throw new IllegalArgumentException(e);
                }

                PROPERTY_DESC_CACHE.put(clazz, beanInfo.getPropertyDescriptors());
            }
        }

        return PROPERTY_DESC_CACHE.get(clazz);
    }


    protected void onValidCommonProperties(GoodsValidObject requestData) {
        FluentValidator validator = goodsValidContext.getValidator();
        //存储条件(温度)
        if (GoodsStorageConditionTemperatureEnum.validate(requestData.getStorageConditionTemperature())) {
            validator.on(requestData.getStorageConditionTemperature(), new ValidatorHandler<Integer>() {
                @Override
                public boolean validate(ValidatorContext context, Integer storageConditionTemperature) {
                    boolean success;

                    success = GoodsStorageConditionTemperatureEnum.validate(storageConditionTemperature);
                    if (!success) {
                        context.addErrorMsg("存储条件（温度）范围有误");
                        return false;
                    }
                    //如果为其他温度需要检验输入范围
                    if (storageConditionTemperature.equals(GoodsStorageConditionTemperatureEnum.OTHERS.getCode())) {
                        success = GoodsStorageConditionUtils.validateTemperatureRange(requestData.getStorageConditionTemperatureLowerValue(),
                                requestData.getStorageConditionTemperatureUpperValue());
                        if (!success) {
                            context.addErrorMsg("存储条件（温度）范围输入有误");
                        }
                    }
                    return success;
                }
            });
        }

        //存储条件湿度
        if (NumberUtil.greaterAndEqualZero(requestData.getStorageConditionHumidityLowerValue()) && NumberUtil.greaterAndEqualZero(requestData.getStorageConditionHumidityUpperValue())) {
            validator.on(null, new ValidatorHandler<Object>() {
                @Override
                public boolean validate(ValidatorContext context, Object obj) {
                    boolean valid = GoodsStorageConditionUtils
                            .validateHumidityRange(requestData.getStorageConditionHumidityLowerValue(), requestData.getStorageConditionHumidityUpperValue());
                    if (!valid) {
                        context.addErrorMsg("存储条件(湿度)输入有误");
                    }
                    return valid;
                }
            });
        }


        //存储条件其他
        if (ArrayUtils.isNotEmpty(requestData.getStorageConditionOthersArray())) {
            validator.onEach(requestData.getStorageConditionOthersArray(), new ValidatorHandler<Integer>() {
                @Override
                public boolean validate(ValidatorContext context, Integer input) {
                    if (!GoodsStorageConditionUtils.validateOthersConditionIfPresent(input)) {
                        context.addErrorMsg("存储条件(其他)输入有误");
                        return false;
                    }
                    return true;
                }
            });
        }

        if (StringUtils.isNotEmpty(requestData.getWikiHref())) {
            validator.on(requestData.getWikiHref(), new ValidatorHandler<String>() {
                @Override
                public boolean validate(ValidatorContext context, String url) {
                    try {
                        new URL(url);
                    } catch (MalformedURLException e) {
                        context.addErrorMsg("wiki地址无法访问");
                        return false;
                    }
                    if (StringUtils.length(url) > 250) {
                        context.addErrorMsg("wiki地址超过长度");
                        return false;
                    }
                    return true;
                }
            });
        }
    }

    /**
     * 商品校验
     *
     * @param requestData
     * @return
     */
    public abstract Result validate(T requestData);


    public GoodsValidTypeEnum getType() {
        return goodsType;
    }


    protected FluentValidator getValidator() {
        return goodsValidContext.getValidator();
    }

}
