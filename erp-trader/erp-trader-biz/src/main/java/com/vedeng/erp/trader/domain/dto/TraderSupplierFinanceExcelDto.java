package com.vedeng.erp.trader.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
    * 供应商列表（财务专用）
    */
@Data
public class TraderSupplierFinanceExcelDto {

    /**
     * 交易者ID
     */
    @ExcelProperty(value = "*供应商ID", index = 0)
    private Integer traderId;

    /**
     * 交易者ID
     */
    @ExcelProperty(value = "*供应商类别（填数值:1生产厂家/2分销商）", index = 1)
    private Integer traderType;

    /**
     * 主键
     */
    private Integer traderSupplierFinanceId;


    /**
     * 是否推送金蝶 0否 1是
     */
    private Integer isPush = 0;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete = 0;

    /**
     * 备注
     */
    private String remark = "";

    /**
     * 更新备注
     */
    private String updateRemark = "";

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

}