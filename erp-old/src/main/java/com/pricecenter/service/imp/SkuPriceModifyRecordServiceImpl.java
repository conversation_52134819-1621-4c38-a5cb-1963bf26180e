package com.pricecenter.service.imp;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pricecenter.dto.PriceChangeAffectOrderIndexDto;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.pricecenter.dto.SkuPriceChangeApplyPageQueryDto;
import com.pricecenter.dto.SkuPriceModifyRecordIndexDto;
import com.pricecenter.service.SkuPriceModifyRecordService;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.price.dao.PriceChangeAffectOrderMapper;
import com.vedeng.price.dao.PriceChangeReadRecordMapper;
import com.vedeng.price.dao.SkuPriceModifyRecordMapper;
import com.vedeng.price.dto.SkuPriceModifyRecordSearchDto;
import com.vedeng.price.model.PriceChangeReadRecord;
import com.vedeng.price.model.SkuPriceModifyRecord;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/8/18 16:57
 */
@Service
public class SkuPriceModifyRecordServiceImpl implements SkuPriceModifyRecordService {
    Logger logger = LoggerFactory.getLogger(SkuPriceModifyRecordServiceImpl.class);
    @Autowired
    private SkuPriceModifyRecordMapper skuPriceModifyRecordMapper;

    @Autowired
    private PriceChangeAffectOrderMapper priceChangeAffectOrderMapper;

    @Autowired
    private PriceChangeReadRecordMapper priceChangeReadRecordMapper;

    @Value("${price.url}")
    protected String priceUrl;
    private static final String SUCCESS_CODE = "success";
    private static final String BASE_PRICEMAINTAIN_PAGE = "sku_price_change_apply/list/page";

    public SkuPriceChangeApplyDto findPriceChangeApply(Integer skuId) {
        SkuPriceChangeApplyPageQueryDto skuPriceChangeApplyPageQueryDto = new SkuPriceChangeApplyPageQueryDto();
        skuPriceChangeApplyPageQueryDto.setIncludeSkuNosStr("V"+skuId);

        try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(skuPriceChangeApplyPageQueryDto);


            logger.info("findPriceChangeApply->findByPage 请求参数:" + requestJson);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_PAGE, requestJson);

            logger.info("findPriceChangeApply->findByPage 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return null;
            }

            Gson gson = new Gson();

            List<SkuPriceChangeApplyDto> priceChangeApplyList = gson.fromJson(resultJsonObj.getJSONObject("data").get("priceChangeApplyList").toString(),
                    new TypeToken<List<SkuPriceChangeApplyDto>>() {
                    }.getType());
            if(CollectionUtils.isNotEmpty(priceChangeApplyList)){
                return priceChangeApplyList.get(0);
            }
            return null;
        } catch (Exception e) {
            logger.error("基础信息核价查询失败", e);
        }
        return null;
    }


    @Override
    public List<SkuPriceModifyRecordIndexDto> findByPage(SkuPriceModifyRecordSearchDto skuPriceModifyRecordSearchDto, Page page){

        dealPriceChangeSearchInfo(skuPriceModifyRecordSearchDto);
        
        Map<String, Object> map = new HashMap<>();
        map.put("skuPriceModifyRecordSearchDto", skuPriceModifyRecordSearchDto);
        map.put("page", page);
        List<SkuPriceModifyRecordIndexDto> resultList = skuPriceModifyRecordMapper.findByListPage(map);

        //计算价格改动幅度与价格浮动方向
        resultList.forEach(skuPriceModifyRecordIndexDto ->
                computePriceFluctuationPercentageAndDirection(skuPriceModifyRecordIndexDto));

        return resultList;
    }

    @Override
    public SkuPriceModifyRecordIndexDto getLastestPriceChangeRecord(Integer skuId) {
        SkuPriceModifyRecordIndexDto result = skuPriceModifyRecordMapper.getLastestPriceChangeRecord(skuId);
        if(null == result){
            return null;
        }
        //计算价格改动幅度与价格浮动方向
        computePriceFluctuationPercentageAndDirection(result);
        return result;
    }

    /**
     * @Description 根据传过来的多选框组织搜索类
     * <AUTHOR>
     * @Date 9:56 2021/8/23
     * @Param [skuPriceModifyRecordSearchDto]
     * @return void
     **/
    private void dealPriceChangeSearchInfo(SkuPriceModifyRecordSearchDto skuPriceModifyRecordSearchDto) {
        if (CollectionUtils.isEmpty(skuPriceModifyRecordSearchDto.getIsPriceChange())){
            return;
        }
        skuPriceModifyRecordSearchDto.getIsPriceChange().removeAll(Collections.singleton(null));
        skuPriceModifyRecordSearchDto.getIsPriceChange().stream().forEach(priceChangeFlag -> {
            switch (priceChangeFlag){
                case 1:
                    skuPriceModifyRecordSearchDto.setIsPurchaseCostsChange(1);
                    break;
                case 2:
                    skuPriceModifyRecordSearchDto.setIsMarketPriceChange(1);
                    break;
                case 3:
                    skuPriceModifyRecordSearchDto.setIsDistributionPriceChange(1);
                    break;
                case 4:
                    skuPriceModifyRecordSearchDto.setIsTerminalPriceChange(1);
                    break;
                case 5:
                    skuPriceModifyRecordSearchDto.setIsGroupPriceChange(1);
                    break;
            }
        });
    }

    /**
     * @Description 计算价格幅度回写到参数中
     * <AUTHOR>
     * @Date 9:57 2021/8/23
     * @Param [skuPriceModifyRecordIndexDto]
     * @return void
     **/
    private void computePriceFluctuationPercentageAndDirection(SkuPriceModifyRecordIndexDto skuPriceModifyRecordIndexDto){
        //计算价格改变幅度
        skuPriceModifyRecordIndexDto.setDistributionPriceFluctuation(computePriceFluctuationPercentage(
                skuPriceModifyRecordIndexDto.getBeforeModDistributionPrice(),skuPriceModifyRecordIndexDto.getAfterModDistributionPrice()));
        skuPriceModifyRecordIndexDto.setDistributionPriceFluctuationDreaction(
                skuPriceModifyRecordIndexDto.getAfterModDistributionPrice().compareTo(skuPriceModifyRecordIndexDto.getBeforeModDistributionPrice()));

        skuPriceModifyRecordIndexDto.setGroupPriceFluctuation(computePriceFluctuationPercentage(
                skuPriceModifyRecordIndexDto.getBeforeModGroupPrice(),skuPriceModifyRecordIndexDto.getAfterModGroupPrice()));
        skuPriceModifyRecordIndexDto.setGroupPriceFluctuationDreaction(
                skuPriceModifyRecordIndexDto.getAfterModGroupPrice().compareTo(skuPriceModifyRecordIndexDto.getBeforeModGroupPrice()));

        skuPriceModifyRecordIndexDto.setMarketPriceFluctuation(computePriceFluctuationPercentage(
                skuPriceModifyRecordIndexDto.getBeforeModMarketPrice(),skuPriceModifyRecordIndexDto.getAfterModMarketPrice()));
        skuPriceModifyRecordIndexDto.setMarketPriceFluctuationDreaction(
                skuPriceModifyRecordIndexDto.getAfterModMarketPrice().compareTo(skuPriceModifyRecordIndexDto.getBeforeModMarketPrice()));

        skuPriceModifyRecordIndexDto.setTerminalPriceFluctuation(computePriceFluctuationPercentage(
                skuPriceModifyRecordIndexDto.getBeforeModTerminalPrice(),skuPriceModifyRecordIndexDto.getAfterModTerminalPrice()));
        skuPriceModifyRecordIndexDto.setTerminalPriceFluctuationDreaction(
                skuPriceModifyRecordIndexDto.getAfterModTerminalPrice().compareTo(skuPriceModifyRecordIndexDto.getBeforeModTerminalPrice()));

        skuPriceModifyRecordIndexDto.setPurchaseCostsFluctuation(computePriceFluctuationPercentage(
                skuPriceModifyRecordIndexDto.getBeforeModPurchaseCosts(),skuPriceModifyRecordIndexDto.getAfterModPurchaseCosts()));
        skuPriceModifyRecordIndexDto.setPurchaseCostsFluctuationDreaction(
                skuPriceModifyRecordIndexDto.getAfterModPurchaseCosts().compareTo(skuPriceModifyRecordIndexDto.getBeforeModPurchaseCosts()));

        // 计算电商价、科研终端价的趋势
        skuPriceModifyRecordIndexDto.setElectronicCommercePriceFluctuation(computePriceFluctuationPercentage(
                skuPriceModifyRecordIndexDto.getBeforeModElectronicCommercePrice(), skuPriceModifyRecordIndexDto.getAfterModElectronicCommercePrice()));
        skuPriceModifyRecordIndexDto.setElectronicCommercePriceFluctuationDreaction(
                skuPriceModifyRecordIndexDto.getAfterModElectronicCommercePrice().compareTo(skuPriceModifyRecordIndexDto.getBeforeModElectronicCommercePrice()));

        skuPriceModifyRecordIndexDto.setResearchTerminalPriceFluctuation(computePriceFluctuationPercentage(
                skuPriceModifyRecordIndexDto.getBeforeModResearchTerminalPrice(),skuPriceModifyRecordIndexDto.getAfterModResearchTerminalPrice()));
        skuPriceModifyRecordIndexDto.setResearchTerminalPriceFluctuationDreaction(
                skuPriceModifyRecordIndexDto.getAfterModResearchTerminalPrice().compareTo(skuPriceModifyRecordIndexDto.getBeforeModResearchTerminalPrice()));

    }

    /**
     * @Description 计算两个价格之间的价格改变幅度保留两位小数多余小数直接舍去
     * <AUTHOR>
     * @Date 9:57 2021/8/23
     * @Param [beforePrice, afterPrice]
     * @return java.math.BigDecimal
     **/
    private BigDecimal computePriceFluctuationPercentage(BigDecimal beforePrice,BigDecimal afterPrice){
        if(beforePrice.compareTo(BigDecimal.ZERO) <= 0){
            return BigDecimal.ZERO;
        }
        return afterPrice.subtract(beforePrice).divide(beforePrice,2,BigDecimal.ROUND_DOWN).abs().multiply(new BigDecimal(100));
    }

    @Override
    public List<SkuPriceModifyRecord> getYestedayPriceChangeRecordList(){
        return skuPriceModifyRecordMapper.getYestedayPriceChangeInfo();
    }

    @Override
    public List<PriceChangeAffectOrderIndexDto> getAffectOrderList(Integer skuPriceModifyRecordId, Integer currentUserId, Page page){
        Map<String, Object> map = new HashMap<>();
        map.put("skuPriceModifyRecordId", skuPriceModifyRecordId);
        map.put("currentUserId",currentUserId);
        map.put("page", page);
        return priceChangeAffectOrderMapper.getAffectOrderBySkuPriceModifyRecordIdListPage(map);
    }

    @Override
    public ResultInfo<?> dealAffrctOrder(Integer priceChangeAffectOrderId){
        priceChangeAffectOrderMapper.dealAffrctOrder(priceChangeAffectOrderId);
        return new ResultInfo<>(0,"确认成功");
    }

    @Override
    public Integer isNeedNotify(Integer userId,Integer positType){
        PriceChangeReadRecord userLatestReadRecord = priceChangeReadRecordMapper.getLatestReadRecordByUserId(userId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userLatestReadRecord", userLatestReadRecord);
        if((userLatestReadRecord==null||userLatestReadRecord.getReadTime()==null)&&!positType.equals(SysOptionConstant.ID_310)){
            logger.info("没有阅读价格记录 userId={}",userId);
            return 0;
        }
        //拿到所有订单对应的改过价的 且当天没有查看的sku
       List<Integer> skuIds = priceChangeAffectOrderMapper.queryChangePriceSku(map);
       if(org.springframework.util.CollectionUtils.isEmpty(skuIds)){
           return 0;
       }
       //找订单 订单状态为进行中 且 收款状态为 ‘未收款的’
        Integer orderNum = priceChangeAffectOrderMapper.isNeedNotifyOrder(skuIds,userId);

       if(orderNum>0){
           return orderNum;
       }
       //找报价单 跟单状态为跟单中的报价单
        return priceChangeAffectOrderMapper.isNeedNotifyQuoteorder(skuIds,userId);
    }

    @Override
    public void read(Integer userId){
        PriceChangeReadRecord nowRead = new PriceChangeReadRecord();
        nowRead.setReaderId(Long.valueOf(userId));
        nowRead.setReadTime(DateUtil.gainNowDate());
        nowRead.setPriceChangeDate(DateUtil.getNowDate(DateUtil.DATE_FORMAT));
        priceChangeReadRecordMapper.insert(nowRead);
    }

}
