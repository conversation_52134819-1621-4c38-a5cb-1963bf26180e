<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.finance.mapper.HxInvoiceMapper">

    <select id="getHxInvoiceRecordInfoByHxInvoiceId" resultType="com.vedeng.erp.finance.dto.HxInvoiceDto">
        SELECT
            A.HX_INVOICE_ID,
            A.INVOICE_STATUS,
            ROUND(A.AMOUNT, 2)                 AMOUNT,
            SUM(ROUND(IFNULL(B.AMOUNT, 0), 2)) RECORDED_AMOUNT
        FROM T_HX_INVOICE A
                 LEFT JOIN T_INVOICE B ON A.HX_INVOICE_ID = B.HX_INVOICE_ID
            AND B.TYPE IN (503, 504, 4126)
            AND B.VALID_STATUS != 2
        AND ((B.COLOR_TYPE = 2 AND B.IS_ENABLE = 1) OR B.COLOR_COMPLEMENT_TYPE = 1)
            WHERE A.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </select>

    <update id="saveHxInvoiceStatus">
        UPDATE T_HX_INVOICE
        SET INVOICE_STATUS = #{invoiceStatus,jdbcType=TINYINT}
        WHERE
            HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
    </update>

    <select id="queryLeftAmountByInvoiceNo" resultType="java.math.BigDecimal">
        SELECT
            (CASE WHEN T1.HX_INVOICE_ID IS NULL THEN NULL ELSE T1.AMOUNT - ROUND(SUM( IFNULL( T2.AMOUNT, 0 ) ),2) END) LEFT_AMOUNT
        FROM
            T_HX_INVOICE T1
        LEFT JOIN T_INVOICE T2 ON T1.HX_INVOICE_ID = T2.HX_INVOICE_ID
        AND T2.VALID_STATUS != 2
        AND ((T2.COLOR_TYPE = 2
        AND T2.IS_ENABLE = 1)
        OR T2.COLOR_COMPLEMENT_TYPE = 1)
        WHERE
            T1.INVOICE_NUM = #{invoiceNo,jdbcType=VARCHAR}
            AND T1.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
        GROUP BY
            T1.HX_INVOICE_ID
    </select>

    <select id="getRecentHxInvoiceDtoByNo" resultType="com.vedeng.erp.finance.dto.HxInvoiceDto">
        SELECT
            HX_INVOICE_ID
        FROM
            `T_HX_INVOICE`
        WHERE
            INVOICE_TYPE = 1
          AND INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
          AND INVOICE_NUM = #{invoiceNo,jdbcType=VARCHAR}
        ORDER BY INVOICE_STATUS DESC
            LIMIT 1
    </select>
</mapper>
