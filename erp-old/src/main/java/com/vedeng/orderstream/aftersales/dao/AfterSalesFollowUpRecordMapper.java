package com.vedeng.orderstream.aftersales.dao;

import com.vedeng.orderstream.aftersales.model.AfterSalesFollowUpRecord;
import com.vedeng.orderstream.aftersales.model.AfterSalesFollowUpRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named("afterSalesFollowUpRecordMapper")
public interface AfterSalesFollowUpRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int countByExample(AfterSalesFollowUpRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int deleteByExample(AfterSalesFollowUpRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int deleteByPrimaryKey(Integer recordId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int insert(AfterSalesFollowUpRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int insertSelective(AfterSalesFollowUpRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    List<AfterSalesFollowUpRecord> selectByExample(AfterSalesFollowUpRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    AfterSalesFollowUpRecord selectByPrimaryKey(Integer recordId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int updateByExampleSelective(@Param("record") AfterSalesFollowUpRecord record, @Param("example") AfterSalesFollowUpRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int updateByExample(@Param("record") AfterSalesFollowUpRecord record, @Param("example") AfterSalesFollowUpRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int updateByPrimaryKeySelective(AfterSalesFollowUpRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_FOLLOW_UP_RECORD
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    int updateByPrimaryKey(AfterSalesFollowUpRecord record);

    /**
     * Obtain after sales follow-up records
     * Using parameter afterSalesId
     * @simgo Tue Oct 12 11:17:55 CST 2021
     * @param afterSalesId
     * @return
     */
    List<AfterSalesFollowUpRecord> selectByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);
}