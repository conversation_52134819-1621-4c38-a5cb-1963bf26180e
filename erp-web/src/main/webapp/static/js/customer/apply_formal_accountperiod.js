$(function() {
    $("#changeMount").change(function () {
        checkLogin();
        clearErroeMes();
        $("#changeMount").removeClass("errorbor");
        var changeMount = $("#changeMount").val();
        var totalAmount = $("#totalAmount").text();
        if(totalAmount == "-"){
            totalAmount = 0;
        }
        var changeType = $("#changeType").val();

        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if(changeMount.length>0 && !reg.test(changeMount)){
            warn2Tips("changeMount","调整额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
            return false;
        }
        var operateType = $("#operateType").val();
        if(changeType == "上调"){
            $("#applyAmount").text(Number(totalAmount) + Number(changeMount));
            if(operateType == 1){
                $("#finalApplyAmount").val(Number(totalAmount) + Number(changeMount));
            }else {
                $("#finalApplyAmount").val(changeMount);
            }
        }else {
            $("#applyAmount").text((Number(totalAmount) - Number(changeMount)).toFixed(2));
            if(operateType == 1){
                $("#finalApplyAmount").val((Number(totalAmount) - Number(changeMount)).toFixed(2));
            }else {
                $("#finalApplyAmount").val(Number(-changeMount));
            }
        }
    });

    $("#changeType").change(function () {
        checkLogin();
        clearErroeMes();
        $("#changeMount").removeClass("errorbor");
        var changeMount = $("#changeMount").val();
        var totalAmount = $("#totalAmount").text();
        if(totalAmount == "-"){
            totalAmount = 0;
        }
        var changeType = $("#changeType").val();

        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if(changeMount.length>0 && !reg.test(changeMount)){
            warn2Tips("changeMount","调整额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
            return false;
        }
        var operateType = $("#operateType").val();
        if(changeType == "上调"){
            $("#applyAmount").text(Number(totalAmount) + Number(changeMount));
            if(operateType == 1){
                $("#finalApplyAmount").val((Number(totalAmount) - Number(changeMount)).toFixed(2));
            }else {
                $("#finalApplyAmount").val(changeMount);
            }
        }else {
            $("#applyAmount").text((Number(totalAmount) - Number(changeMount)).toFixed(2));
            if(operateType == 1){
                $("#finalApplyAmount").val(Number(totalAmount) - Number(changeMount));
            }else {
                $("#finalApplyAmount").val(Number(-changeMount));
            }
        }
    })
//提交表单校验
    $('#myform').submit(function() {
        checkLogin();
        clearErroeMes();
        jQuery.ajax({
            url:'./saveAccountPeriodApplyNew.do',
            data:$('#myform').serialize(),
            type:"POST",
            dataType : "json",
            beforeSend:function(){
                var billPeriodStart = $("#billPeriodStart").val();
                var billPeriodEnd = $("#billPeriodEnd").val();
                if(billPeriodStart.length == 0){
                    warn2Tips("billPeriodStart","有效期开始时间不能为空");
                    return false;
                }
                if(billPeriodEnd.length == 0){
                    warn2Tips("billPeriodEnd","有效期结束时间不能为空");
                    return false;
                }
                if(billPeriodStart > billPeriodEnd){
                    warn2Tips("billPeriodEnd","有效期开始时间不能大于结束时间");
                    return false;
                }
                var applyAmount = $("#applyAmount").text();
                if(applyAmount != ''){
                    if(Number(applyAmount) < 0){
                        warn2Tips("changeMount","调后额度不可小于0！");
                        $("#changeMount").addClass("errorbor");
                        return false;
                    }
                }
                var usefulAmount = $("#usefulAmount").text();
                if(usefulAmount != "-"){
                    if(Number(applyAmount) < Number(usefulAmount)){
                        warn2Tips("changeMount","额度降低的范围不可高于目前可用额度！");
                        $("#changeMount").addClass("errorbor");
                        return false;
                    }
                }
                var re = /^[0-9]+$/;
                var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
                var settlementPeriod = $("#settlementPeriod").val();
                if(settlementPeriod.length == 0){
                    warn2Tips("settlementPeriod","结算周期不能为空");//文本框ID和提示用语
                    return false;
                }
                if(settlementPeriod.length>0 && !re.test(settlementPeriod)){
                    warn2Tips("settlementPeriod","结算周期输入错误！仅允许使用数字");//文本框ID和提示用语
                    return false;
                }
                var expectedMargin = $("#expectedMargin").val();
                if(expectedMargin.length>0 && !reg.test(expectedMargin)){
                    warn2Tips("expectedMargin","预期毛利率输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }
                var applyReason = $("#applyReason").val();
                if(applyReason.length == 0){
                    warn2Tips("applyReason","申请原因不能为空");//文本框ID和提示用语
                    return false;
                }
            },
            success:function(data){
                if(data.code == 0){
                    layer.alert(data.message,{
                        closeBtn: 0,
                        btn: ['确定'] //按钮
                    }, function(){
                        var st=data.data.split(",");
                        var str=page_url+"/trader/customer/getFinanceAndAptitude.do?traderId="+st[0]+"&traderCustomerId="+st[1];
                        $("#finace").attr('href',str);
                        window.parent.location.reload();
                    });
                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;
    });
});