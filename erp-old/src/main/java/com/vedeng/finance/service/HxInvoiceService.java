package com.vedeng.finance.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.finance.dto.HxIncomeInvoiceDTO;
import com.vedeng.finance.dto.HxInvoiceAuthResponse;
import com.vedeng.finance.dto.HxInvoiceResponse;
import com.vedeng.finance.model.HxInvoiceConfig;
import com.vedeng.finance.model.vo.InvoiceConfig;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航信发票服务接口
 * <AUTHOR>
 * @date created in 2020/5/18 13:54
 */
public interface HxInvoiceService {

    /**
     * 下载发票图片
     * @param invoiceType 发票类型，01销项发票，02进项发票
     * @param invoiceCode 发票代码
     * @param invoiceNum 发票号码
     * @return 下载后保存到FTP服务器上的URL地址
     */
    String downloadInvoiceImgFromHx(String invoiceType, String invoiceCode, String invoiceNum);

    /**
     * 获取进项发票列表
     * @param start 起始时间，yyy-MM-dd
     * @param end 截止时间，yyyy-MM-dd
     * @return 请求结果
     */
    Boolean getHxIncomeInvoiceList(String start, String end);

    /**
     * 发票认证操作
     * @param taxPayerNum 纳税人识别号
     * @param invoiceCode 发票代码
     * @param invoiceNum 发票号码
     * @param amount 税额
     * @param operationType 操作类型，0撤销勾选，1勾选
     * @param invoiceCreateTime 发票开票日期，格式yyyy-MM-dd
     * @return 请求结果
     */
    HxInvoiceAuthResponse authHxInvoice(String taxPayerNum, String invoiceCode, String invoiceNum, BigDecimal amount, Integer operationType, String invoiceCreateTime);


    void batchDownloadSaleInvoiceImg(Long start, Long end);

    /**
     * 保存航信发票信息
     *
     * @param hxIncomeInvoiceDTOS
     */
    void convertAndSaveHxIncomeInvoice(List<HxIncomeInvoiceDTO> hxIncomeInvoiceDTOS);

    ResultInfo saveInvoiceConfig(InvoiceConfig invoiceConfig, User user);

    Integer deleteInvoiceConfig();
//
//    void refreshHxInvoiceStatus(List<HxInvoiceConfig> hxInvoiceConfigVoList, User user);
}
