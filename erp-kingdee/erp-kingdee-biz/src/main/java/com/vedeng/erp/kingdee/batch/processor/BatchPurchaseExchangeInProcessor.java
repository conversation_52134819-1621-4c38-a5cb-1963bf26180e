package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 采购换货入库
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchPurchaseExchangeInProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;
    @Autowired
    private BatchBuyorderDtoMapper buyorderDtoMapper;


    @Override
    public KingDeeStorageInDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        //ERP_LV_2023_48排除借用采购换货入库类型的数据,updateRemark = '21-22其他'
        if ("21-22其他".equals(batchWarehouseGoodsOutInDto.getUpdateRemark())){
            log.info("排除借用采购换货入库类型的数据,{}",JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        KingDeeStorageInDto dto = new KingDeeStorageInDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("采购换货入库,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        // 补充详细单数据
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));
        log.info("采购换货入库 PurchaseExchangeInProcessorService.process：" + JSON.toJSONString(batchWarehouseGoodsOutInDto));
        // 根据入库单关系 获取售后单
        BatchAfterSalesDto batchAfterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesNoAndSubjectType(
                batchWarehouseGoodsOutInDto.getRelateNo(), 536);
        if (Objects.isNull(batchAfterSalesDto)) {
            return null;
        }

        // VDERP-14781 排除赠品采购单，单独处理
        BatchBuyorderDto batchBuyorderDto = buyorderDtoMapper.selectByBuyorderNo(batchAfterSalesDto.getOrderNo());
        if (Objects.isNull(batchBuyorderDto)){
            log.info("采购换货入库,未查询到原始采购单,采购单号:{}",batchAfterSalesDto.getOrderNo());
            return null;
        }
        if (ErpConstant.ONE.equals(batchBuyorderDto.getIsGift())){
            log.info("采购换货入库,关联的采购单为赠品订单,需单独处理.warehouseGoodsOutInNo:{},buyorderNo:{}"
                    ,batchWarehouseGoodsOutInDto.getOutInNo(),batchBuyorderDto.getBuyorderNo());
            return null;
        }

        Map<Integer, BatchAfterSalesGoodsDto> map = batchAfterSalesDto.getBatchAfterSalesGoodsDtoList()
                .stream().collect(Collectors.toMap(BatchAfterSalesGoodsDto::getAfterSalesGoodsId, c -> c, (k1, k2) -> k1));

        dto.setFId("0");
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFSupplierId(batchAfterSalesDto.getTraderSupplierId().toString());
        dto.setFStockDirect("GENERAL");

        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchBuyorderDto.getInvoiceType());
        BigDecimal tax = InvoiceTaxTypeEnum.getTax(batchBuyorderDto.getInvoiceType());
        List<KingDeeStorageInDetailDto> detailDtoList = new ArrayList<>();
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l -> {
            KingDeeStorageInDetailDto detailDto = new KingDeeStorageInDetailDto();
            BatchAfterSalesGoodsDto afterSalesGoodsDto = map.get(l.getRelatedId());
            detailDto.setFMaterialId(afterSalesGoodsDto.getSku());
            detailDto.setFStockId("CK9997");
            detailDto.setFQty(l.getNum().abs().toString());
            BigDecimal price = isSpecialInvoice?afterSalesGoodsDto.getPrice().divide(BigDecimal.ONE.add(tax),6, RoundingMode.HALF_UP):afterSalesGoodsDto.getPrice();
            detailDto.setFPrice(price.toString());
            detailDto.setFAmount(price.multiply(new BigDecimal(l.getNum().toString())).toString());

            detailDto.setFQzokYsddh(batchAfterSalesDto.getOrderNo());
            detailDto.setFQzokGsywdh(batchAfterSalesDto.getAfterSalesNo());
            detailDto.setFQzokYwlx("采购换货");
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            detailDto.setFQzokSfzf(afterSalesGoodsDto.getDeliveryDirect().equals(0) ? "否" : "是");
            detailDto.setFQzokBddjhId(l.getWarehouseGoodsOutInDetailId().toString());

            detailDtoList.add(detailDto);
        });
        dto.setFEntity(detailDtoList);

        return dto;
    }

}
