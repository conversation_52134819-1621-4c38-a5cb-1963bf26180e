<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.temporal.mapper.WorkflowCheckpointMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.vedeng.temporal.domain.entity.WorkflowCheckpointEntity">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CHECKPOINT_ID" property="checkpointId" jdbcType="VARCHAR"/>
        <result column="WORKFLOW_ID" property="workflowId" jdbcType="VARCHAR"/>
        <result column="BUSINESS_ID" property="businessId" jdbcType="VARCHAR"/>
        <result column="STEP_NAME" property="stepName" jdbcType="VARCHAR"/>
        <result column="COMPANY_CODE" property="companyCode" jdbcType="VARCHAR"/>
        <result column="CHECKPOINT_TYPE" property="checkpointType" jdbcType="VARCHAR"/>
        <result column="CHECKPOINT_DATA" property="checkpointData" jdbcType="LONGVARCHAR"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="CHECKPOINT_SIZE" property="checkpointSize" jdbcType="BIGINT"/>
        <result column="CREATED_TIME" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_TIME" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="IS_VALID" property="isValid" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, CHECKPOINT_ID, WORKFLOW_ID, BUSINESS_ID, STEP_NAME, COMPANY_CODE,
        CHECKPOINT_TYPE, CHECKPOINT_DATA, DESCRIPTION, VERSION, CHECKPOINT_SIZE,
        CREATED_TIME, UPDATED_TIME, IS_VALID
    </sql>

    <!-- 插入检查点记录 -->
    <insert id="insert" parameterType="com.vedeng.temporal.domain.entity.WorkflowCheckpointEntity" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO T_WORKFLOW_CHECKPOINT (
            CHECKPOINT_ID, WORKFLOW_ID, BUSINESS_ID, STEP_NAME, COMPANY_CODE,
            CHECKPOINT_TYPE, CHECKPOINT_DATA, DESCRIPTION, VERSION, CHECKPOINT_SIZE,
            CREATED_TIME, UPDATED_TIME, IS_VALID
        ) VALUES (
            #{checkpointId}, #{workflowId}, #{businessId}, #{stepName}, #{companyCode},
            #{checkpointType}, #{checkpointData}, #{description}, #{version}, #{checkpointSize},
            #{createdTime}, #{updatedTime}, #{isValid}
        )
    </insert>

    <!-- 根据检查点ID查询 -->
    <select id="selectByCheckpointId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_WORKFLOW_CHECKPOINT
        WHERE CHECKPOINT_ID = #{checkpointId} AND IS_VALID = 1
    </select>

    <!-- 根据工作流ID查询所有检查点 -->
    <select id="selectByWorkflowId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_WORKFLOW_CHECKPOINT
        WHERE WORKFLOW_ID = #{workflowId} AND IS_VALID = 1
        ORDER BY CREATED_TIME DESC
    </select>

    <!-- 根据工作流ID查询最新检查点 -->
    <select id="selectLatestByWorkflowId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_WORKFLOW_CHECKPOINT
        WHERE WORKFLOW_ID = #{workflowId} AND IS_VALID = 1
        ORDER BY CREATED_TIME DESC
        LIMIT 1
    </select>

    <!-- 根据工作流ID和步骤名称查询检查点 -->
    <select id="selectByWorkflowIdAndStep" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_WORKFLOW_CHECKPOINT
        WHERE WORKFLOW_ID = #{workflowId} AND STEP_NAME = #{stepName} AND IS_VALID = 1
        ORDER BY CREATED_TIME DESC
        LIMIT 1
    </select>

    <!-- 根据业务ID查询检查点 -->
    <select id="selectByBusinessId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_WORKFLOW_CHECKPOINT
        WHERE BUSINESS_ID = #{businessId} AND IS_VALID = 1
        ORDER BY CREATED_TIME DESC
    </select>

    <!-- 根据检查点类型查询 -->
    <select id="selectByCheckpointType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_WORKFLOW_CHECKPOINT
        WHERE CHECKPOINT_TYPE = #{checkpointType} AND IS_VALID = 1
        ORDER BY CREATED_TIME DESC
    </select>

    <!-- 查询指定时间范围内的检查点 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_WORKFLOW_CHECKPOINT
        WHERE CREATED_TIME BETWEEN #{startTime} AND #{endTime} AND IS_VALID = 1
        ORDER BY CREATED_TIME DESC
    </select>

    <!-- 更新检查点 -->
    <update id="update" parameterType="com.vedeng.temporal.domain.entity.WorkflowCheckpointEntity">
        UPDATE T_WORKFLOW_CHECKPOINT SET
            CHECKPOINT_DATA = #{checkpointData},
            DESCRIPTION = #{description},
            VERSION = #{version},
            CHECKPOINT_SIZE = #{checkpointSize},
            UPDATED_TIME = #{updatedTime}
        WHERE CHECKPOINT_ID = #{checkpointId}
    </update>

    <!-- 逻辑删除检查点 -->
    <update id="deleteByCheckpointId" parameterType="java.lang.String">
        UPDATE T_WORKFLOW_CHECKPOINT SET IS_VALID = 0, UPDATED_TIME = NOW()
        WHERE CHECKPOINT_ID = #{checkpointId}
    </update>

    <!-- 逻辑删除工作流的所有检查点 -->
    <update id="deleteByWorkflowId" parameterType="java.lang.String">
        UPDATE T_WORKFLOW_CHECKPOINT SET IS_VALID = 0, UPDATED_TIME = NOW()
        WHERE WORKFLOW_ID = #{workflowId}
    </update>

    <!-- 物理删除过期的检查点 -->
    <delete id="deleteExpiredCheckpoints" parameterType="java.sql.Timestamp">
        DELETE FROM T_WORKFLOW_CHECKPOINT
        WHERE CREATED_TIME &lt; #{expireTime}
    </delete>

    <!-- 统计检查点数量 -->
    <select id="countByWorkflowId" parameterType="java.lang.String" resultType="int">
        SELECT COUNT(*)
        FROM T_WORKFLOW_CHECKPOINT
        WHERE WORKFLOW_ID = #{workflowId} AND IS_VALID = 1
    </select>

    <!-- 统计指定类型的检查点数量 -->
    <select id="countByWorkflowIdAndType" resultType="int">
        SELECT COUNT(*)
        FROM T_WORKFLOW_CHECKPOINT
        WHERE WORKFLOW_ID = #{workflowId} AND CHECKPOINT_TYPE = #{checkpointType} AND IS_VALID = 1
    </select>

    <!-- 查询检查点总大小 -->
    <select id="sumCheckpointSizeByWorkflowId" parameterType="java.lang.String" resultType="long">
        SELECT COALESCE(SUM(CHECKPOINT_SIZE), 0)
        FROM T_WORKFLOW_CHECKPOINT
        WHERE WORKFLOW_ID = #{workflowId} AND IS_VALID = 1
    </select>

    <!-- 查询最近的失败检查点 -->
    <select id="selectLatestFailureCheckpoint" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM T_WORKFLOW_CHECKPOINT
        WHERE WORKFLOW_ID = #{workflowId} AND CHECKPOINT_TYPE = 'FAILURE' AND IS_VALID = 1
        ORDER BY CREATED_TIME DESC
        LIMIT 1
    </select>

    <!-- 批量插入检查点 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO T_WORKFLOW_CHECKPOINT (
            CHECKPOINT_ID, WORKFLOW_ID, BUSINESS_ID, STEP_NAME, COMPANY_CODE,
            CHECKPOINT_TYPE, CHECKPOINT_DATA, DESCRIPTION, VERSION, CHECKPOINT_SIZE,
            CREATED_TIME, UPDATED_TIME, IS_VALID
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.checkpointId}, #{entity.workflowId}, #{entity.businessId},
             #{entity.stepName}, #{entity.companyCode}, #{entity.checkpointType},
             #{entity.checkpointData}, #{entity.description}, #{entity.version},
             #{entity.checkpointSize}, #{entity.createdTime}, #{entity.updatedTime}, #{entity.isValid})
        </foreach>
    </insert>

</mapper>
