<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>销售订单异常风控已处理</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="rA662FVSu_Y" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
     <li item_name="待处理"><a href="/ezadmin/list/list-YCb1lxlGWwU" item_name="待处理">待处理</a></li>
     <li item_name="已处理"><a href="/ezadmin/list/list-rA662FVSu_Y" item_name="已处理">已处理</a></li>
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">订单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="RISK_CHECK_BUZ_EXTRA" placeholder="" style="" alias="L" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="T" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="SALER" placeholder="" style="" alias="U1.USERNAME" jdbctype="" data="SELECT 	U.USERNAME AS 'K', 	USERNAME AS 'V'  FROM 	T_USER U 	JOIN T_R_USER_POSIT UP ON U.USER_ID = UP.USER_ID  	AND U.IS_DISABLED = 0 AND U.COMPANY_ID = 1 	JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID  WHERE 	P.TYPE = 310" datatype="KVSQLCACHE" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">销售部门</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="ORG_NAME" placeholder="" style="" alias="O" jdbctype="" data="SELECT 	A.ORG_NAME AS 'K', 	A.ORG_NAME AS 'V'  FROM 	T_ORGANIZATION A 	LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID  WHERE 	B.TYPE = 310  	AND A.COMPANY_ID = 1  GROUP BY 	A.ORG_ID" datatype="KVSQLCACHE" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">被风控时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="17" name="RISK_BEGIN_TIME" placeholder="" style="" alias="FROM_UNIXTIME( L.ADD_TIME / 1000 )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">订单创建时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="17" name="SALEORDER_TIME" placeholder="" style="" alias="FROM_UNIXTIME( S.ADD_TIME / 1000 )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">解除时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="17" name="TRIGGER_TIME" placeholder="" style="" alias="FROM_UNIXTIME( L.RISK_CHECK_TRIGGER_TIME / 1000 )" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">操作者</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="USERNAME" placeholder="" style="" alias="U.USERNAME" jdbctype="" data="SELECT USERNAME  AS 'K', USERNAME AS 'V' FROM T_USER WHERE IS_DISABLED = 0 AND COMPANY_ID  = 1" datatype="KVSQLCACHE" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="RISK_CHECK_BUZ_EXTRA" name="RISK_CHECK_BUZ_EXTRA" body="td-link" order="0" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
      <th item_name="SALER" name="SALER" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属销售</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售部门</th>
      <th item_name="RISK_BEGIN_TIME" name="RISK_BEGIN_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">被风控时间</th>
      <th item_name="SALEORDER_TIME" name="SALEORDER_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单创建时间</th>
      <th item_name="TRIGGER_TYPE" name="TRIGGER_TYPE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">解除风控类型</th>
      <th item_name="TRIGGER_TIME" name="TRIGGER_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">解除时间</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">操作者</th>
      <th item_name="RISK_TIME" name="RISK_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">处理时长</th>
      <th item_name="RISK_CHECK_COMMENT_CUSTOM" name="RISK_CHECK_COMMENT_CUSTOM" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户资质被风控备注</th>
      <th item_name="RISK_CHECK_COMMENT_CUSTOM_INFO" name="RISK_CHECK_COMMENT_CUSTOM_INFO" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户信息被风控备注</th>
      <th item_name="RISK_CHECK_COMMENT_GOOD" name="RISK_CHECK_COMMENT_GOOD" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品被风控备注</th>
      <th item_name="RISK_CHECK_COMMENT_TRADER" name="RISK_CHECK_COMMENT_TRADER" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">供应商被风控备注</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="" groupby="GROUP BY 	L.RISK_CHECK_BUZ_EXTRA HAVING SUM(L.RISK_CHECK_TRIGGER_STATUS) = COUNT(*)">SELECT
	L.RISK_CHECK_BUZ_EXTRA,
	T.TRADER_NAME,
	T.TRADER_ID,
	S.SALEORDER_ID,
	U1.USERNAME SALER,
	U1.USER_ID SALER_ID,
	O.ORG_ID,
	O.ORG_NAME,
	CASE
		WHEN FIND_IN_SET (4,GROUP_CONCAT(DISTINCT L.RISK_CHECK_BUZ_TYPE ))!=0 THEN '客户资质未审核通过'
	END RISK_CHECK_COMMENT_CUSTOM,
	CASE
		WHEN FIND_IN_SET (5,GROUP_CONCAT(DISTINCT L.RISK_CHECK_BUZ_TYPE ))!=0 THEN '客户信息不完整'
	END RISK_CHECK_COMMENT_CUSTOM_INFO,
	CASE
		WHEN FIND_IN_SET (6,GROUP_CONCAT(DISTINCT L.RISK_CHECK_BUZ_TYPE ))!=0 THEN '供应商信息不完整'
	END RISK_CHECK_COMMENT_TRADER,
	CASE
		WHEN FIND_IN_SET (7,GROUP_CONCAT(DISTINCT L.RISK_CHECK_BUZ_TYPE ))!=0 THEN '商品信息不完整'
	END RISK_CHECK_COMMENT_GOOD,
	L.RISK_CHECK_BUZ_TYPE,
	FROM_UNIXTIME( L.RISK_CHECK_TRIGGER_TIME / 1000 ) TRIGGER_TIME,
	U.USERNAME USERNAME,
	L.RISK_CHECK_TRIGGER_USER,
  L.RISK_CHECK_TRIGGER_TYPE,
	CONCAT( FORMAT(L.RISK_TIME / 60000,2),' Min') AS RISK_TIME,
	FROM_UNIXTIME( L.ADD_TIME / 1000 ) RISK_BEGIN_TIME,
	FROM_UNIXTIME( S.ADD_TIME / 1000 ) SALEORDER_TIME,
CASE

		RISK_CHECK_TRIGGER_USER
		WHEN 2 THEN
		'系统自动解除' ELSE '人工解除'
	END TRIGGER_TYPE ,
       CASE S.ORDER_TYPE WHEN 5 THEN '/order/hc/hcOrderDetailsPage.do?saleorderId=' ELSE '/order/saleorder/view.do?saleorderId=' END URI
FROM
	(
	SELECT
		ADD_TIME,
		RISK_CHECK_BUZ_TYPE,
		RISK_CHECK_BUZ_ID,
		RISK_CHECK_BUZ_EXTRA,
		RISK_CHECK_TRIGGER_TIME,
		RISK_CHECK_TRIGGER_USER,
	    RISK_CHECK_TRIGGER_STATUS,
(RISK_CHECK_TRIGGER_TIME-ADD_TIME) AS RISK_TIME,
CASE
		RISK_CHECK_TRIGGER_USER
		WHEN 2 THEN
		2 ELSE 1
	END RISK_CHECK_TRIGGER_TYPE
FROM
	T_RISK_CHECK_LOG
WHERE
	 RISK_CHECK_BUZ_PROPERTY = 'saleorder'
	) L
	LEFT JOIN T_SALEORDER S ON L.RISK_CHECK_BUZ_EXTRA = S.SALEORDER_NO
	LEFT JOIN T_TRADER T ON S.TRADER_ID = T.TRADER_ID
	LEFT JOIN T_USER U ON L.RISK_CHECK_TRIGGER_USER = U.USER_ID
	LEFT JOIN T_R_TRADER_J_USER TU ON T.TRADER_ID = TU.TRADER_ID
	LEFT JOIN T_USER U1 ON TU.USER_ID = U1.USER_ID
	LEFT JOIN T_R_USER_POSIT UP ON U1.USER_ID = UP.USER_ID
	LEFT JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID
	LEFT JOIN T_ORGANIZATION O ON P.ORG_ID = O.ORG_ID
where 1=1</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>