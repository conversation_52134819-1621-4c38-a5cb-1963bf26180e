<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>平铺行按钮</title>
</head>
<body id="button-single-none" type="listbutton">
<table>
    <tr id="content">
        <td class=' rowButtons  fixedCol '  >
            <div class="layui-btn-container    ">
                <button th:each="item:${rowButtonItemList}" th:text="${item.itemLabel}" th:if="${not #lists.isEmpty(rowButtonItemList) }"
                        th:ITEM_ID="${item.itemName}" th:ITEM_URL="${item.itemUrl}" th:item_name="${item.itemName}" th:name="${item.itemName}" th:ITEM_OPEN_TYPE="${item.openType}"
                        th:ITEM_OPEN_TITLE="${item.windowName}" th:area="${item.area}"
                        type="button" editor="button_row"
                        th:style="${'margin-bottom:1px;'+item.itemStyle}"
                        th:ITEM_STYLE="${'margin-bottom:0px;'+item.itemStyle}"
                        class="rowbutton ezopenbutton  layui-btn layui-btn-sm layui-btn-primary "  >
                </button>
            </div>
        </td> </tr>
</table>
</body>
</html>
