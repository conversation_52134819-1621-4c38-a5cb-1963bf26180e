package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseBackQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseBackConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseBackMapper;
import com.vedeng.erp.kingdee.service.KingDeePayCommonService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseBackApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购退货 的应付单 以及 票的数据推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchBuyOrderBackVirtualInvoicePayCommonProcessor implements ItemProcessor<BatchVirtualInvoiceDto, KingDeePayCommonDto> {

    @Autowired
    private KingDeePurchaseBackMapper kingDeePurchaseBackMapper;

    @Autowired
    private KingDeePurchaseBackConvertor kingDeePurchaseBackConvertor;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    protected KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePayCommonService kingDeePayCommonApiService;

    @Autowired
    private KingDeePurchaseBackApiService kingDeePurchaseBackApiService;

    @Autowired
    private BatchRVirtualInvoiceJWarehouseDtoMapper batchRVirtualInvoiceJWarehouseDtoMapper;

    @Autowired
    private BatchVirtualInvoiceItemDtoMapper batchVirtualInvoiceItemDtoMapper;



    @Override
    public KingDeePayCommonDto process(BatchVirtualInvoiceDto batchInvoiceDto) throws Exception {
        log.info("BatchBuyOrderBackVirtualInvoicePayCommonProcessor 处理采购单红字有效票{}" + JSON.toJSONString(batchInvoiceDto));


        List<BatchRVirtualInvoiceJWarehouseDto> rVirtualInvoiceJWarehouseDtoList = batchRVirtualInvoiceJWarehouseDtoMapper.selectByVirtualInvoiceId(batchInvoiceDto.getVirtualInvoiceId());
        // 根据 商品维度聚合分组
        if (CollUtil.isEmpty(rVirtualInvoiceJWarehouseDtoList)) {
            log.error("当前采购单红字有效虚拟票id:{},未关联到出库单信息",JSON.toJSONString(batchInvoiceDto.getVirtualInvoiceId()));
            throw new KingDeeException("当前采购单红字有效虚拟票id:" + JSON.toJSONString(batchInvoiceDto.getVirtualInvoiceId()) + "未关联到出库单信息");
        }

        KingDeePayCommonDto kingDeePayCommonDto = new KingDeePayCommonDto();
        kingDeePayCommonDto.setFQzokBddjtId(batchInvoiceDto.getUuid());
        Boolean exist = kingDeePayCommonApiService.kingDeeIsExist(kingDeePayCommonDto);
        if (exist) {
            return null;
        }

        List<Integer> collect = rVirtualInvoiceJWarehouseDtoList.stream().map(BatchRVirtualInvoiceJWarehouseDto::getWarehouseGoodsOutInItemId).collect(Collectors.toList());
        List<BatchWarehouseGoodsOutInDto> batchWarehouseGoodsOutInDtos = batchWarehouseGoodsOutInDtoMapper.selectWarehouseOutInOrder(collect);
        updateInvoiceOpenTime(batchInvoiceDto, batchWarehouseGoodsOutInDtos);

        processNum(rVirtualInvoiceJWarehouseDtoList);

        // 票明细id 去聚合
        Map<Integer, List<BatchRVirtualInvoiceJWarehouseDto>> groupByInvoiceDetailId = rVirtualInvoiceJWarehouseDtoList
                .stream().collect(Collectors.groupingBy(BatchRVirtualInvoiceJWarehouseDto::getVirtualInvoiceItemId));



        List<String> outInNos = batchWarehouseGoodsOutInDtos.stream().map(BatchWarehouseGoodsOutInDto::getOutInNo).distinct().collect(Collectors.toList());

        List<KingDeePurchaseBackDto> all2Dto = new ArrayList<>();
        for (String no : outInNos) {
            processOutInNo(all2Dto, no);
        }
        if (CollUtil.isEmpty(all2Dto)) {
            log.error("当前采购单红字有效虚拟票:{},有效票未关联到推送金蝶出库单信息",JSON.toJSONString(batchInvoiceDto.getVirtualInvoiceId()));
            throw new KingDeeException("当前采购单红字有效虚拟票:" + JSON.toJSONString(batchInvoiceDto.getVirtualInvoiceId()) + "有效票未关联到推送金蝶出库单信息");
        }

        Map<String, KingDeePurchaseBackDto> kingDeeData = all2Dto
                .stream().collect(Collectors.toMap(KingDeePurchaseBackDto::getFBillNo,c->c,(k1,k2)->k1));
        // 发票类型 1电票 2 纸票
        String QZOK_FPLX = Objects.isNull(batchInvoiceDto.getInvoiceProperty()) ? "2" : batchInvoiceDto.getInvoiceProperty().equals(1) ? "2" : "1";
        KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto = new KingDeePayCommonAndInvoiceDto();
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchInvoiceDto.getInvoiceType());
        kingDeePayCommonAndInvoiceDto.setSpecial(isSpecialInvoice);

        List<Integer> invoiceDetailIds = rVirtualInvoiceJWarehouseDtoList.stream().map(BatchRVirtualInvoiceJWarehouseDto::getVirtualInvoiceItemId).collect(Collectors.toList());
        List<BatchVirtualInvoiceItemDto> batchInvoiceDetailDtos = batchVirtualInvoiceItemDtoMapper.selectByVirtualInvoiceItemIds(invoiceDetailIds);
        Map<Integer, BatchVirtualInvoiceItemDto> invoiceDetail2Map = batchInvoiceDetailDtos.stream().collect(Collectors.toMap(BatchVirtualInvoiceItemDto::getVirtualInvoiceItemId, c -> c, (k1, k2) -> k1));

        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : kingDeePayCommonAndInvoiceDto.getSpecial() ? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)) : "0.00";
        BigDecimal taxRateNum = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO :kingDeePayCommonAndInvoiceDto.getSpecial()? batchInvoiceDto.getRatio():BigDecimal.ZERO;
        //一级dto

        kingDeePayCommonDto.setFDate(DateUtil.formatDate(batchInvoiceDto.getOpenInvoiceTime()));
        kingDeePayCommonDto.setFSupplierId(batchInvoiceDto.getTraderSupplierId().toString());

        // 构建应付单

        //二级dto
        List<KingDeePayCommonDetailDto> kingDeePayCommonDetailDtoList = new ArrayList<>();
        kingDeePayCommonDto.setFEntityDetail(kingDeePayCommonDetailDtoList);
        groupByInvoiceDetailId.forEach((k, v) -> {

            // 匹配总数
            BigDecimal totalNum = v.stream().map(BatchRVirtualInvoiceJWarehouseDto::getNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (totalNum.compareTo(BigDecimal.ZERO) == 0) {
                log.error("匹配数量为0：{}",JSON.toJSONString(v));
                return;
            }

            List<KingDeePayCommonDetailDto> calcNum = new ArrayList<>();

            // 票明细总额
            BatchVirtualInvoiceItemDto data = invoiceDetail2Map.get(k);
            BigDecimal totalAmount = Objects.isNull(data.getTotalAmount()) ? BigDecimal.ZERO : data.getTotalAmount();
            // 平均价格
            BigDecimal priceAverage = totalAmount.divide(totalNum,6, RoundingMode.HALF_UP);


            for (int i = 0; i < v.size(); i++) {

                BatchRVirtualInvoiceJWarehouseDto batchRVirtualInvoiceJWarehouseDto = v.get(i);
                BigDecimal thisAllNum = batchRVirtualInvoiceJWarehouseDto.getNum();
                BigDecimal thisTotalAmount = thisAllNum.multiply(priceAverage).setScale(2, RoundingMode.HALF_UP);

                KingDeePayCommonDetailDto kingDeePayCommonDetailDto = new KingDeePayCommonDetailDto();
                kingDeePayCommonDetailDtoList.add(kingDeePayCommonDetailDto);
                calcNum.add(kingDeePayCommonDetailDto);
                kingDeePayCommonDetailDto.setFMaterialId(batchRVirtualInvoiceJWarehouseDto.getSku());
                // 商品在当前发票行的对应开票总数
                kingDeePayCommonDetailDto.setFPriceQty(thisAllNum.abs().negate().toString());
                // 数组中商品的录票单价平均值
                kingDeePayCommonDetailDto.setFTaxPrice(priceAverage.abs().toString());
                kingDeePayCommonDetailDto.setFEntryTaxRate(taxRate);
                // erp 票的 明细
                KingDeePurchaseBackDto kingDeeInData = kingDeeData.get(batchRVirtualInvoiceJWarehouseDto.getOutInNo());
                if (Objects.isNull(kingDeeInData)) {
                    log.error("当前采购单红字有效虚拟票：{}未查到出库单信息{}",batchInvoiceDto.getVirtualInvoiceId(), k);
                    throw new KingDeeException("当前采购单红字有效虚拟票:"+batchInvoiceDto.getVirtualInvoiceId()+"未查到出库单信息");
                }
                kingDeePayCommonDetailDto.setF_QZOK_BDDJHID(k.toString()+"-"+batchRVirtualInvoiceJWarehouseDto.getWarehouseGoodsOutInItemId());
                kingDeePayCommonDetailDto.setFSourceType(KingDeeFormConstant.PURCHASE_BACK);
                // 价税合计 发票sku商品录入的总额
                kingDeePayCommonDetailDto.setFAllAmountFor_D(thisTotalAmount.toString());
                // 税额  [价税合计/(1+ 税率)]*税率
                BigDecimal taxAmouontFor = thisTotalAmount.multiply(taxRateNum).divide(BigDecimal.ONE.add(taxRateNum),2,RoundingMode.HALF_UP);
                kingDeePayCommonDetailDto.setFTaxAmountFor_D(taxAmouontFor.toString());
                // 发票不含税的金额 价税合计-税额
                kingDeePayCommonDetailDto.setFNoTaxAmountFor_D(thisTotalAmount.subtract(taxAmouontFor).toString());

                //三级dto
                List<KingDeePayCommonDetailLinkDto> kingDeePayCommonDetailLinkDtoList = new ArrayList<>();
                kingDeePayCommonDetailDto.setFEntityDetail_Link(kingDeePayCommonDetailLinkDtoList);

                BatchRVirtualInvoiceJWarehouseDto item = batchRVirtualInvoiceJWarehouseDto;

                // 入库单
                KingDeePurchaseBackDto kingDeePurchaseBackDto = kingDeeData.get(item.getOutInNo());
                List<KingDeePurchaseBackDetailDto> fInStockEntry = kingDeePurchaseBackDto.getFpurmrbentry();
                // 入库单明细
                Map<String, KingDeePurchaseBackDetailDto> fInStockEntry2Map = fInStockEntry.stream().collect(Collectors.toMap(KingDeePurchaseBackDetailDto::getF_qzok_bddjhid, a -> a, (k1, k2) -> k1));


                // 此条入库单明细
                KingDeePurchaseBackDetailDto kingDeePurchaseBackDetail = fInStockEntry2Map.get(item.getWarehouseGoodsOutInItemId().toString());
                if (Objects.isNull(kingDeePurchaseBackDetail)) {
                    log.error("virtualInvoiceId:{}未能查询到此出库单明细对应的金蝶出库单明细{}", batchInvoiceDto.getVirtualInvoiceId(),JSON.toJSONString(item));
                    throw new KingDeeException("virtualInvoiceId:"+batchInvoiceDto.getVirtualInvoiceId()+"未能查询到此出库单明细对应的金蝶出库单明细");
                }
                BatchVirtualInvoiceItemDto batchInvoiceDetailDto = invoiceDetail2Map.get(item.getVirtualInvoiceItemId());
                if (Objects.isNull(batchInvoiceDetailDto)) {
                    log.error("未能查到票的明细信息{}", JSON.toJSONString(item));
                    throw new KingDeeException("未能查到票的明细信息");
                }


                KingDeePayCommonDetailLinkDto kingDeePayCommonDetailLinkDto = new KingDeePayCommonDetailLinkDto();
                kingDeePayCommonDetailLinkDto.setFLinkId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FRuleId("AP_MRBToPayableMap");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FFlowLineId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableId("0");
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableName("T_PUR_MRBENTRY");
                // 金蝶出库单id
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSBillId(kingDeePurchaseBackDto.getFId());
                // 金蝶出库行id
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSId(kingDeePurchaseBackDetail.getFEntryId());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FBASICUNITQTYOld(item.getNum().abs().negate().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FBASICUNITQTY(item.getNum().abs().negate().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FStockBaseQty(item.getNum().abs().negate().toString());
                kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FStockBaseQtyOld(item.getNum().abs().negate().toString());
                if (i == v.size() - 1) {

                    // 减去最后一行 获取前面的已录入
                    BigDecimal previousAmount = priceAverage.multiply(totalNum.subtract(item.getNum())).setScale(2,RoundingMode.HALF_UP);
                    // 避免尾差
                    BigDecimal thisLine = totalAmount.subtract(previousAmount);
                    log.info("采购虚拟红票invoiceId:{},sku:{},已录入:{},最后一行:{}",batchInvoiceDto.getVirtualInvoiceId(),kingDeePayCommonDetailDto.getFMaterialId(),previousAmount,thisLine);
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_DOld(thisLine.toString());
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_D(thisLine.toString());
                } else {
                    // 均价*录入关系的数量
                    BigDecimal thisLine = priceAverage.multiply(item.getNum()).setScale(2,RoundingMode.HALF_UP);
                    log.info("采购虚拟红票invoiceId:{},sku:{},前几行金额:{}",batchInvoiceDto.getVirtualInvoiceId(),kingDeePayCommonDetailDto.getFMaterialId(),thisLine);
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_DOld(thisLine.toString());
                    kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_D(thisLine.toString());
                }

                kingDeePayCommonDetailLinkDtoList.add(kingDeePayCommonDetailLinkDto);

            }

            if (CollUtil.isEmpty(calcNum)) {
                return;
            }

            processTailDifferent(taxRateNum, calcNum, totalAmount);

        });

        return kingDeePayCommonDto;
    }

    private void processTailDifferent(BigDecimal taxRateNum, List<KingDeePayCommonDetailDto> calcNum, BigDecimal totalAmount) {
        BigDecimal logTotalAmount = calcNum.stream().map(x -> new BigDecimal(x.getFAllAmountFor_D())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 满足查额在0.01以内的
        if (totalAmount.subtract(logTotalAmount.abs()).abs().compareTo(new BigDecimal("0.01")) <= 0) {
            KingDeePayCommonDetailDto kingDeePayCommonDetailDto = calcNum.get(calcNum.size() - 1);

            BigDecimal subtract = totalAmount.subtract(logTotalAmount);
            BigDecimal bigDecimal = new BigDecimal(kingDeePayCommonDetailDto.getFAllAmountFor_D());
            BigDecimal total = bigDecimal.add(subtract);
            kingDeePayCommonDetailDto.setFAllAmountFor_D(total.toString());
            // 税额  [价税合计/(1+ 税率)]*税率
            BigDecimal taxAmouontFor = total.multiply(taxRateNum).divide(BigDecimal.ONE.add(taxRateNum),2,RoundingMode.HALF_UP);
            kingDeePayCommonDetailDto.setFTaxAmountFor_D(taxAmouontFor.toString());
            // 发票不含税的金额 价税合计-税额
            kingDeePayCommonDetailDto.setFNoTaxAmountFor_D(total.subtract(taxAmouontFor).toString());
        }
    }

    private void processOutInNo(List<KingDeePurchaseBackDto> all2Dto, String no) {
        List<KingDeePurchaseBackEntity> all = kingDeePurchaseBackMapper.findByAll(KingDeePurchaseBackQueryDto.builder().fBillNo(no).build());

        if (CollUtil.isNotEmpty(all)) {
            List<KingDeePurchaseBackDto> data = kingDeePurchaseBackConvertor.toDto(all);
            all2Dto.addAll(data);
        } else {
            // 先查金蝶 查不到查期金蝶
            List<KingDeePurchaseBackQueryResultDto> data = kingDeePurchaseBackApiService.queryByOutInNo(no);

            if (CollUtil.isNotEmpty(data)) {
                KingDeePurchaseBackDto kingDeePurchasePurchaseBackDto = new KingDeePurchaseBackDto();
                kingDeePurchasePurchaseBackDto.setFId(data.get(0).getFID());
                kingDeePurchasePurchaseBackDto.setFId(data.get(0).getFID());
                kingDeePurchasePurchaseBackDto.setFBillNo(data.get(0).getFBillNo());
                List<KingDeePurchaseBackDetailDto> entities = data.stream().map(x -> {
                    KingDeePurchaseBackDetailDto result = new KingDeePurchaseBackDetailDto();
                    result.setFEntryId(x.getFPURMRBENTRY_FEntryId());
                    result.setF_qzok_bddjhid(x.getF_QZOK_BDDJHID());
                    return result;
                }).collect(Collectors.toList());
                kingDeePurchasePurchaseBackDto.setFpurmrbentry(entities);
                all2Dto.add(kingDeePurchasePurchaseBackDto);
            }
        }
    }


    private void updateInvoiceOpenTime(BatchVirtualInvoiceDto batchInvoiceDto, List<BatchWarehouseGoodsOutInDto> batchWarehouseGoodsOutInDtos) {
        Optional<Date> max = batchWarehouseGoodsOutInDtos.stream().max(Comparator.comparing(BatchWarehouseGoodsOutInDto::getOutInTime)).map(BatchWarehouseGoodsOutInDto::getOutInTime);
        if (max.isPresent() && max.get().getTime() > batchInvoiceDto.getOpenInvoiceTime().getTime()) {
            batchInvoiceDto.setOpenInvoiceTime(max.get());
        }
    }


    private void processNum(List<BatchRVirtualInvoiceJWarehouseDto> rVirtualInvoiceJWarehouseDtoList) {
        // 处理num
        for (BatchRVirtualInvoiceJWarehouseDto dto : rVirtualInvoiceJWarehouseDtoList) {
            // 统一保留2位小数
            // 判断是否为入库单明细的最后一个 sql 倒排
            List<BatchRVirtualInvoiceJWarehouseDto> byOperateLogId = batchRVirtualInvoiceJWarehouseDtoMapper.findByWarehouseGoodsOutInItemId(dto.getWarehouseGoodsOutInItemId());

            List<BatchRInvoiceDetailJOperateLogDto> jOperateLogDtos = batchRInvoiceDetailJOperateLogDtoMapper.findByOperateLogId(dto.getWarehouseGoodsOutInItemId());
            boolean isLastInvoice = false;
            // 是否为最后一张票
            if (CollUtil.isNotEmpty(jOperateLogDtos)) {
                BatchRInvoiceDetailJOperateLogDto data = jOperateLogDtos.get(0);
                if (dto.getVirtualInvoiceItemId().equals(byOperateLogId.get(0).getVirtualInvoiceItemId()) ) {
                    if (data.getAddTime().getTime() > byOperateLogId.get(0).getAddTime().getTime()) {
                        isLastInvoice = true;
                    } else {
                        isLastInvoice = false;
                    }

                }
            } else {
                isLastInvoice=  dto.getVirtualInvoiceItemId().equals(byOperateLogId.get(0).getVirtualInvoiceItemId());
            }

            // 入库单明细数量是否已经用尽
            BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsOutInItemDto = batchWarehouseGoodsOutInItemDtoMapper.selectByPrimaryKey(Long.valueOf(dto.getWarehouseGoodsOutInItemId()));
            BigDecimal originalNum = batchWarehouseGoodsOutInItemDto.getNum().abs();

            BigDecimal allUseNum = byOperateLogId.stream().map(x -> x.getNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal used = jOperateLogDtos.stream().filter(Objects::nonNull).map(x -> x.getNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            allUseNum = allUseNum.add(used);
            // 用完 且当前明细为最后一个明细
            if (isLastInvoice && originalNum.compareTo(allUseNum) <= 0) {
                // 前面的四舍五入保留2位
                BigDecimal beforeThis = byOperateLogId.stream()
                        .filter(x -> !x.getVirtualInvoiceWarehouseId().equals(dto.getVirtualInvoiceWarehouseId()))
                        .map(x -> {
                            BigDecimal abs = x.getNum().abs();
                            if (abs.compareTo(new BigDecimal("0.01")) <= 0) {
                                return new BigDecimal("0.01");
                            } else {
                                return x.getNum().abs().setScale(2, RoundingMode.DOWN);
                            }
                        })
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal before = jOperateLogDtos.stream().filter(Objects::nonNull).map(x -> {
                    BigDecimal abs = x.getNum().abs();
                    if (abs.compareTo(new BigDecimal("0.01")) <= 0) {
                        return new BigDecimal("0.01");
                    } else {
                        return x.getNum().abs().setScale(2, RoundingMode.DOWN);
                    }
                }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                beforeThis = beforeThis.add(before);
                dto.setNum(originalNum.subtract(beforeThis));
            } else {

                if (dto.getNum().abs().compareTo(new BigDecimal("0.01")) <= 0) {
                    dto.setNum(new BigDecimal("0.01"));
                } else {
                    dto.setNum(dto.getNum().abs().setScale(2, RoundingMode.DOWN));
                }

            }

        }
    }


}
