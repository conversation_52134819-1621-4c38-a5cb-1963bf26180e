<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>美年艾迪康</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="0f8G2dOZtB4" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">订单号</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">美年订单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="M_SALEORDER_NO" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">客户名称</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">添加时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="FROM_UNIXTIME( A.ADD_TIME/ 1000,         '%Y-%m-%d' )" jdbctype="" data="" datatype="" oper="BETWEEN" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">艾迪康单号</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="ADK_SALEORDER_NO" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="" ></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">生效时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="VALID_TIME" placeholder="" style="" alias="FROM_UNIXTIME( A.VALID_TIME/ 1000,         '%Y-%m-%d' )" jdbctype="" data="" datatype="" oper="BETWEEN" ></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"></th>
            <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link" order="1" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}&amp;scene=0" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单号</th>
            <th item_name="ADK_SALEORDER_NO" name="ADK_SALEORDER_NO" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">艾迪康单号</th>
            <th item_name="M_SALEORDER_NO" name="M_SALEORDER_NO" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">美年单号</th>
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">添加时间</th>
            <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">生效时间</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="" groupby="">select SALEORDER_ID,SALEORDER_NO,ADK_SALEORDER_NO,M_SALEORDER_NO,TRADER_NAME,from_unixtime(ADD_TIME/1000) ADD_TIME,from_unixtime(VALID_TIME/1000) VALID_TIME from T_SALEORDER  A WHERE (SALEORDER_NO like 'DH%' OR SALEORDER_NO like 'ADK%' )</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>