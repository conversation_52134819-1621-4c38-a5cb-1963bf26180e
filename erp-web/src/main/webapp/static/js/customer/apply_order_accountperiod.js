$(function() {
    $("#amountChange").hide();
    $("#serBtn").hide();
    $("#amountAdd").show();
    $("#addBtn").css("background-color","blue");
    $("#uptBtn").css("background-color","white");
    $("#uptBtn").css("color","black");
    $("#addBtn").css("color","white");
});
function resetEmpty() {
    uptToAdd();
    $("#saleorderNo").val('');
    $("#insertApplyAmount").val(0);
    $("#settlementPeriod").val(0);
    $("#expectedMargin").val(0);
    $("#applyReason").text('');
}
function uptAccountPeriod() {
    addToUpt();
}
function addToUpt() {
    $("#saleorderNo").val("");
    $("#insertApplyAmount").val(0);
    $("#settlementPeriod").val("");
    $("#expectedMargin").val(0);
    $("#operateType").val(2);
    $("#applyReason").text("");
    $("#amountChange").show();
    $("#serBtn").show();
    $("#amountAdd").hide();
    $("#amountLabel").text("额度调整");
    $("#amountTips").text("--不调整可设为0。调低范围不可大于当前可用额");
    $("#uptBtn").css("background-color","blue");
    $("#addBtn").css("background-color","white");
    $("#addBtn").css("color","black");
    $("#uptBtn").css("color","white");
}
function uptToAdd() {
    $("#amountChange").hide();
    $("#serBtn").hide();
    $("#operateType").val(1);
    $("#amountAdd").show();
    $("#amountLabel").text("申请额度");
    $("#amountTips").text("--仅可输入正数。支持2位小数");
    $("#applyAmount").text(0);
    $("#addBtn").css("background-color","blue");
    $("#uptBtn").css("background-color","white");
    $("#uptBtn").css("color","black");
    $("#addBtn").css("color","white");
    $("#billPeriodId").val('');
}
$("#changeMount").change(function () {
    checkLogin();
    clearErroeMes();
    $("#changeMount").removeClass("errorbor");
    var changeMount = $("#changeMount").val();
    var finalApplyAmount = $("#finalApplyAmount").val();
    if(finalApplyAmount == "-"){
        finalApplyAmount = 0;
    }
    var changeType = $("#changeType").val();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if(changeMount.length>0 && !reg.test(changeMount)){
        warn2Tips("changeMount","调整额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
        return false;
    }
    if(changeType == "上调"){
        $("#applyAmount").text(Number(finalApplyAmount) + Number(changeMount));
    }else {
        var usefulAmount = $("#unReturnAmount").val();
        var applyAmount = (Number(finalApplyAmount) - Number(changeMount)).toFixed(2);
        $("#applyAmount").text(applyAmount);
        if(applyAmount < Number(usefulAmount)){
            warn2Tips("changeMount","额度降低的范围不可高于目前可用额度！");
            $("#changeMount").addClass("errorbor");
            return false;
        }
    }
});


$("#changeType").change(function () {
    checkLogin();
    clearErroeMes();
    $("#changeMount").removeClass("errorbor");
    var changeMount = $("#changeMount").val();
    var finalApplyAmount = $("#finalApplyAmount").val();
    if(finalApplyAmount == "-"){
        finalApplyAmount = 0;
    }
    var changeType = $("#changeType").val();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if(changeMount.length>0 && !reg.test(changeMount)){
        warn2Tips("changeMount","调整额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
        return false;
    }
    if(changeType == "上调"){
        $("#applyAmount").text(Number(finalApplyAmount) + Number(changeMount));
    }else {
        var usefulAmount = $("#unReturnAmount").val();
        var applyAmount = (Number(finalApplyAmount) - Number(changeMount)).toFixed(2);
        $("#applyAmount").text(applyAmount);
        if(applyAmount < Number(usefulAmount)){
            warn2Tips("changeMount","额度降低的范围不可高于目前可用额度！");
            $("#changeMount").addClass("errorbor");
            return false;
        }
    }
});
//提交表单校验
$('#myform').submit(function() {
    checkLogin();
    clearErroeMes();
    var insertApplyAmount = $("#insertApplyAmount").val();
    var changeMount = $("#changeMount").val();
    var operateType = $("#operateType").val();
    if(operateType == 1){
        $("#finalApplyAmount").val(Number(insertApplyAmount));
    }else {
        var changeType = $("#changeType").val();
        if(changeType == "上调"){
            $("#finalApplyAmount").val(changeMount);
        }else {
            $("#finalApplyAmount").val(-changeMount);
        }
    }
    jQuery.ajax({
        url:'./saveAccountPeriodApplyNew.do',
        data:$('#myform').serialize(),
        type:"POST",
        dataType : "json",
        beforeSend:function(){
            var saleorderNo = $("#saleorderNo").val();
            if(saleorderNo.length == 0){
                warn2Tips("saleorderNo","订单编号不能为空");
                return false;
            }
            var re = /^[0-9]+$/;
            var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
            if(insertApplyAmount.length>0 && !reg.test(insertApplyAmount)){
                warn2Tips("insertApplyAmount","申请额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
            var applyAmount = $("#applyAmount").text();
            if(operateType == 2){
                if(applyAmount.length > 0 && applyAmount < 0){
                    warn2Tips("changeMount","额度降低的范围不可高于目前可用额度！");
                    return false;
                }
            }
            var settlementPeriod = $("#settlementPeriod").val();
            if(settlementPeriod.length == 0){
                warn2Tips("settlementPeriod","结算周期不能为空");//文本框ID和提示用语
                return false;
            }
            if(settlementPeriod.length>0 && !re.test(settlementPeriod)){
                warn2Tips("settlementPeriod","结算周期输入错误！仅允许使用数字");//文本框ID和提示用语
                return false;
            }
            var expectedMargin = $("#expectedMargin").val();
            if(expectedMargin.length>0 && !reg.test(expectedMargin)){
                warn2Tips("expectedMargin","预期毛利率输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
            var applyReason = $("#applyReason").val();
            if(applyReason.length == 0){
                warn2Tips("applyReason","申请原因不能为空");//文本框ID和提示用语
                return false;
            }
        },
        success:function(data){
            if(data.code == 0){
                layer.alert(data.message,{
                    closeBtn: 0,
                    btn: ['确定'] //按钮
                }, function(){
                    var st=data.data.split(",");
                    var str=page_url+"/trader/customer/getFinanceAndAptitude.do?traderId="+st[0]+"&traderCustomerId="+st[1];
                    $("#finace").attr('href',str);
                    window.parent.location.reload();
                });
            }else{
                layer.alert(data.message);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
    return false;
});