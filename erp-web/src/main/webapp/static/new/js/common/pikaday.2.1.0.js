/* Pub Time: May 3, 2018 09:39:06, Version: 2.1.0 */(function (t, k) { "object" === typeof exports ? module.exports = k() : "function" === typeof define && define.amd ? define(function () { return k() }) : t.Pikaday = k() })(this, function () {
    var t = !!window.addEventListener, k = window.document, s = window.setTimeout, n = function (a, b, c, d) { t ? a.addEventListener(b, c, !!d) : a.attachEvent("on" + b, c) }, q = function (a, b, c, d) { t ? a.removeEventListener(b, c, !!d) : a.detachEvent("on" + b, c) }, y = function (a, b, c) {
        var d; if (k.createEvent) d = k.createEvent("HTMLEvents"), d.initEvent(b, !0, !1), d = o(d, c), a.dispatchEvent(d);
        else if (k.createEventObject) { d = k.createEventObject(); d = o(d, c); try { a.fireEvent("on" + b, d) } catch (e) { } }
    }, j = function (a, b) { return -1 !== (" " + a.className + " ").indexOf(" " + b + " ") }, z = function (a, b) { j(a, b) || (a.className = "" === a.className ? b : a.className + " " + b) }, u = function (a) { return /Array/.test(Object.prototype.toString.call(a)) }, h = function (a) { return /Date/.test(Object.prototype.toString.call(a)) && !isNaN(a.getTime()) }, v = function (a) { h(a) && a.setHours(0, 0, 0, 0) }, p = function (a, b) {
        return !a || !b ? null : a.getTime() > b.getTime() ?
            1 : a.getTime() === b.getTime() ? 0 : -1
    }, o = function (a, b, c) { var d, e; for (d in b) if ((e = void 0 !== a[d]) && "object" === typeof b[d] && void 0 === b[d].nodeName) h(b[d]) ? c && (a[d] = new Date(b[d].getTime())) : u(b[d]) ? c && (a[d] = b[d].slice(0)) : a[d] = o({}, b[d], c); else if (c || !e) a[d] = b[d]; return a }, A = function (a) { 0 > a.month && (a.year -= Math.ceil(Math.abs(a.month) / 12), a.month += 12); 11 < a.month && (a.year += Math.floor(Math.abs(a.month) / 12), a.month -= 12); return a }, w = {
        field: null, bound: void 0, containerTag: "calendar", position: "bottom left", format: "YYYY-MM-DD",
        defaultDate: null, setDefaultDate: !1, firstDay: 0, minDate: null, maxDate: null, yearRange: 10, minYear: 0, maxYear: 9999, minMonth: void 0, maxMonth: void 0, isRTL: !1, yearSuffix: "", showMonthAfterYear: !1, numberOfMonths: 1, mainCalendar: "left", timeRange: !1, rangeJoiner: " ", i18n: { months: "一月 二月 三月 四月 五月 六月 七月 八月 九月 十月 十一月 十二月".split(" "), weekdays: "周日 周一 周二 周三 周四 周五 周六".split(" "), weekdaysShort: "日 一 二 三 四 五 六".split(" ") }, onSelect: null,
        onOpen: null, onClose: null, onDraw: null, onFocus: null
    }, B = function (a, b, c) { for (b += a.firstDay; 7 <= b;)b -= 7; return c ? a.i18n.weekdaysShort[b] : a.i18n.weekdays[b] }, C = function (a, b, c, d, e, f, l, x, g) { if (l) return '<li class="empty"></li>'; l = []; f && l.push("disabled"); e && l.push("today"); d && l.push("selected"); x && l.push("active"); return '<li data-day="' + a + '" class="pika-button ' + l.join(" ") + '" data-pika-year="' + c + '" data-pika-month="' + b + '" data-pika-day="' + a + '" title="' + g + '">' + a + "</li>" }, m = function (a) {
        var b = this, c = b.config(a);
        b._rangeFinsh = !1; b._sd = null; b._ed = null; b._onMouseDown = function (a) {
            if (b._v) {
                var a = a || window.event, e = a.target || a.srcElement; if (e) {
                    if (j(e, "pika-day") && j(e.parentNode, "disabled")) return b._c = !0, !1; if (!j(e, "disabled")) if (j(e, "pika-button") && !j(e, "empty")) {
                        var f = new Date(e.getAttribute("data-pika-year"), e.getAttribute("data-pika-month"), e.getAttribute("data-pika-day")); if (c.timeRange) {
                            if (z(e, "selected"), null !== b._sd && !b._rangeFinsh ? (b._ed = f, b._sd > b._ed && (f = b._sd, b._sd = b._ed, b._ed = f), b.draw(), b._rangeFinsh =
                                !0) : (b._rangeFinsh = !1, b._sd = f, b._ed = f, b.draw(), "function" === typeof b._o.onSelect && b._o.onSelect.call(b, b.getDate())), b._rangeFinsh) { b.setDate([b._sd, b._ed]); c.bound && s(function () { b.hide() }, 100); return }
                        } else { b.setDate(f); c.bound && s(function () { b.hide() }, 100); return }
                    } else j(e, "pika-prev") ? b.prevMonth() : j(e, "pika-next") ? b.nextMonth() : j(e, "pika-today") && (b.setDate(b.format(new Date)), c.bound && s(function () { b.hide() }, 100)); if (j(e, "pika-select")) b._c = !0; else if (a.preventDefault) a.preventDefault(); else return a.returnValue =
                        !1
                }
            }
        }; b._onChange = function (a) { a = a || window.event; (a = a.target || a.srcElement) && (j(a, "pika-select-month") ? b.gotoMonth(a.value) : j(a, "pika-select-year") && b.gotoYear(a.value)) }; b._onInputChange = function (a) { if (a.firedBy !== b) { var e = (new Date).getFullYear(), a = new Date(Date.parse(c.field.value)); h(a) && -1 == c.field.value.indexOf(a.getFullYear()) && (a = new Date(Date.parse(c.field.value + "." + e))); b.setDate(h(a) ? a : null); b._v || b.show() } }; b._onInputFocus = "function" === typeof c.onFocus ? c.onFocus : function () { b.show() }; b._onInputClick =
            function () { b.show() }; b._onInputBlur = function () { c.timeRange ? (b.setDate(b.getDate()), b._rangeFinsh = !0) : (b._c || (b._b = s(function () { b.hide() }, 50)), b._c = !1) }; b._onClick = function (a) { var a = a || window.event, e = a = a.target || a.srcElement; if (a) { !t && j(a, "pika-select") && !a.onchange && (a.setAttribute("onchange", "return;"), n(a, "change", b._onChange)); do if (j(e, c.containerTag)) return; while (e = e.parentNode); b._v && a !== c.trigger && b.hide() } }; b.el = k.createElement("div"); b.el.className = c.containerTag + (c.isRTL ? " is-rtl" : ""); 1 <
                c.numberOfMonths && (b.el.className += " split-cols" + c.numberOfMonths); n(b.el, "mousedown", b._onMouseDown, !0); n(b.el, "change", b._onChange); c.field && (c.bound ? k.body.appendChild(b.el) : "input" === c.field.nodeName ? c.field.parentNode.insertBefore(b.el, c.field.nextSibling) : c.field.appendChild(b.el), n(c.field, "change", b._onInputChange), c.defaultDate || (c.defaultDate = new Date(Date.parse(c.field.value)), c.setDefaultDate = !0)); a = c.defaultDate; h(a) ? c.setDefaultDate ? (b.setDate(a, !0), b._sd = a) : b.gotoDate(a) : b.gotoDate(new Date);
        c.bound ? (this.hide(), b.el.className += " is-bound", n(c.trigger, "click", b._onInputClick), n(c.trigger, "focus", b._onInputFocus), n(c.trigger, "blur", b._onInputBlur)) : this.show()
    }; m.setDefault = function (a) { o({}, a, !0); w = o(w, a, !0) }; m.prototype = {
        config: function (a) {
            this._o || (this._o = o({}, w, !0)); a = o(this._o, a, !0); a.isRTL = !!a.isRTL; a.field = a.field && a.field.nodeName ? a.field : null; a.stopRangeClear = a.field && "input" !== a.field.nodeName ? !0 : !1; a.bound = !!(void 0 !== a.bound ? a.field && a.bound : a.field); a.trigger = a.trigger && a.trigger.nodeName ?
                a.trigger : a.field; a.disableDayFn = "function" === typeof a.disableDayFn ? a.disableDayFn : null; var b = parseInt(a.numberOfMonths, 10) || 1; a.numberOfMonths = 4 < b ? 4 : b; h(a.minDate) || (a.minDate = !1); h(a.maxDate) || (a.maxDate = !1); a.minDate && a.maxDate && a.maxDate < a.minDate && (a.maxDate = a.minDate = !1); a.minDate && (v(a.minDate), a.minYear = a.minDate.getFullYear(), a.minMonth = a.minDate.getMonth()); a.maxDate && (v(a.maxDate), a.maxYear = a.maxDate.getFullYear(), a.maxMonth = a.maxDate.getMonth()); u(a.yearRange) ? (b = (new Date).getFullYear() -
                    10, a.yearRange[0] = parseInt(a.yearRange[0], 10) || b, a.yearRange[1] = parseInt(a.yearRange[1], 10) || b) : (a.yearRange = Math.abs(parseInt(a.yearRange, 10)) || w.yearRange, 100 < a.yearRange && (a.yearRange = 100)); return a
        }, toString: function () { return this._o.timeRange ? (!h(this._sd) ? "" : this._sd.toDateString()) + this._o.rangeJoiner + (!h(this._ed) ? "" : this._ed.toDateString()) : !h(this._d) ? "" : this._d.toDateString() }, getDate: function () { return h(this._d) ? new Date(this._d.getTime()) : null }, setDate: function (a, b) {
            if (!a) return this._d =
                null, this.draw(); "string" === typeof a && (a = new Date(Date.parse(a))); if (h(a)) { var c = this._o.minDate, d = this._o.maxDate; h(c) && a < c ? a = c : h(d) && a > d && (a = d); this._d = new Date(a.getTime()); v(this._d); this.gotoDate(this._d); this._o.field && (this._o.field.value = this.toString(), y(this._o.field, "change", { firedBy: this })); !b && "function" === typeof this._o.onSelect && this._o.onSelect.call(this, this.getDate()) }
        }, gotoDate: function (a) {
            var b = !0, c = this._o.mainCalendar, d = this._o.minDate && new Date(this._o.minDate) || null, e = this._o.maxDate &&
                new Date(this._o.maxDate) || null; if (h(a)) {
                    if (this.calendars) { var b = new Date(this.calendars[0].year, this.calendars[0].month, 1), f = new Date(this.calendars[this.calendars.length - 1].year, this.calendars[this.calendars.length - 1].month, 1), l = a.getTime(); f.setMonth(f.getMonth() + 1); f.setDate(f.getDate() - 1); b = l < b.getTime() || f.getTime() < l } if (b && (this.calendars = [{ month: a.getMonth(), year: a.getFullYear(), date: a }], d && d > a && (this.calendars[0] = { month: d.getMonth(), year: d.getFullYear(), date: a }), e && e < a ? (this.calendars[0] =
                        { month: e.getMonth(), year: e.getFullYear(), date: a }, 1 < this._o.numberOfMonths && (this._o.mainCalendar = "right")) : this._o.mainCalendar = c, "right" === this._o.mainCalendar)) this.calendars[0].month += 1 - this._o.numberOfMonths; this.adjustCalendars()
                }
        }, adjustCalendars: function () { this.calendars[0] = A(this.calendars[0]); for (var a = 1; a < this._o.numberOfMonths; a++)this.calendars[a] = A({ month: this.calendars[0].month + a, year: this.calendars[0].year }); this.draw() }, gotoToday: function () { this.gotoDate(new Date) }, gotoMonth: function (a) {
            isNaN(a) ||
                (this.calendars[0].month = parseInt(a, 10), this.adjustCalendars())
        }, nextMonth: function () { this.calendars[0].month++; this.adjustCalendars() }, prevMonth: function () { this.calendars[0].month--; this.adjustCalendars() }, gotoYear: function (a) { isNaN(a) || (this.calendars[0].year = parseInt(a, 10), this.adjustCalendars()) }, setMinDate: function (a) { this._o.minDate = a; this._o.bound || (this._o.minYear = a.getFullYear(), this._o.minMonth = a.getMonth(), this.gotoDate(this.calendars[0].date)) }, setMaxDate: function (a) {
            this._o.maxDate = a;
            this._o.bound || (this._o.maxYear = a.getFullYear(), this._o.maxMonth = a.getMonth(), this.gotoDate(this.calendars[0].date))
        }, draw: function (a) {
            if (this._v || a) {
                var b = this._o, a = b.containerTag, c = b.minYear, d = b.maxYear, e = b.minMonth, f = b.maxMonth, l = ""; this._y <= c && (this._y = c, !isNaN(e) && this._m < e && (this._m = e)); this._y >= d && (this._y = d, !isNaN(f) && this._m > f && (this._m = f)); for (c = 0; c < b.numberOfMonths; c++) {
                    1 < b.numberOfMonths && (l += '<div class="' + a + '-split">'); for (var d = c, e = this.calendars[c].year, f = this.calendars[c].month, x =
                        this.calendars[0].year, g = void 0, h = void 0, k = void 0, i = this._o, j = e === i.minYear, m = e === i.maxYear, r = '<div class="' + i.containerTag + '-header pika-title">', p = void 0, g = void 0, n = !0, q = !0, k = [], g = 0; 12 > g; g++)k.push('<option value="' + (e === x ? g - d : 12 + g - d) + '"' + (g === f ? " selected" : "") + (j && g < i.minMonth || m && g > i.maxMonth ? "disabled" : "") + ">" + i.i18n.months[g] + "</option>"); p = '<span class="month">' + i.i18n.months[f] + '<select class="pika-select pika-select-month">' + k.join("") + "</select></span>"; u(i.yearRange) ? (g = i.yearRange[0],
                            h = i.yearRange[1] + 1) : (g = e - i.yearRange, h = 1 + e + i.yearRange); for (k = []; g < h && g <= i.maxYear; g++)g >= i.minYear && k.push('<option value="' + g + '"' + (g === e ? " selected" : "") + ">" + g + "</option>"); g = '<span class="year">' + e + i.yearSuffix + '<select class="pika-select pika-select-year">' + k.join("") + "</select></span>"; r = i.showMonthAfterYear ? r + ('<div class="title">' + g + p + "</div>") : r + ('<div class="title">' + p + g + "</div>"); if (j && (0 === f || i.minMonth >= f)) n = !1; if (m && (11 === f || i.maxMonth <= f)) q = !1; 0 === d && (r += '<div class="pre vd-icon icon-left pika-prev' +
                                (n ? "" : " disabled") + '"></div>'); d === this._o.numberOfMonths - 1 && (r += '<div class="next vd-icon icon-right pika-next' + (q ? "" : " disabled") + '"></div>'); l += r + "</div>" + this.render(this.calendars[c].year, this.calendars[c].month); 1 < b.numberOfMonths && (l += "</div>")
                } 1 === b.numberOfMonths && (a = l, c = this.format(new Date), c = b.minDate && b.minDate < new Date(c) ? '<div class="' + b.containerTag + '-current pika-today">Today:<span class="date pika-today">' + c + "</span></div>" : "", l = a + c); this.el.innerHTML = l; b.bound && "hidden" !== b.field.type &&
                    s(function () { b.trigger.focus() }, 1); if ("function" === typeof this._o.onDraw) { var o = this; s(function () { o._o.onDraw.call(o) }, 0) }
            }
        }, adjustPosition: function () {
            var a = this._o.trigger, b = a, c = this.el.offsetWidth, d = this.el.offsetHeight, e = window.innerWidth || k.documentElement.clientWidth, f = window.innerHeight || k.documentElement.clientHeight, l = window.pageYOffset || k.body.scrollTop || k.documentElement.scrollTop, h, g; if ("function" === typeof a.getBoundingClientRect) b = a.getBoundingClientRect(), h = b.left + window.pageXOffset,
                g = b.bottom + window.pageYOffset; else { h = b.offsetLeft; for (g = b.offsetTop + b.offsetHeight; b = b.offsetParent;)h += b.offsetLeft, g += b.offsetTop } if (h + c > e || -1 < this._o.position.indexOf("right") && 0 < h - c + a.offsetWidth) h = h - c + a.offsetWidth; if (g + d > f + l || -1 < this._o.position.indexOf("top") && 0 < g - d - a.offsetHeight) g = g - d - a.offsetHeight; this.el.style.cssText = ["position: absolute", "left: " + h + "px", "top: " + g + "px"].join(";")
        }, render: function (a, b) {
            var c = this._o, d = new Date, e = [31, 0 === a % 4 && 0 !== a % 100 || 0 === a % 400 ? 29 : 28, 31, 30, 31, 30, 31, 31,
                30, 31, 30, 31][b], f = (new Date(a, b, 1)).getDay(), l = [], k = []; v(d); c.beforeMonthRender && "function" === typeof c.beforeMonthRender && c.beforeMonthRender(a, b, d); 0 < c.firstDay && (f -= c.firstDay, 0 > f && (f += 7)); for (var g = e + f, j = g; 7 < j;)j -= 7; for (var g = g + (7 - j), m = j = 0; j < g; j++) {
                    var i = new Date(a, b, 1 + (j - f)), n = c.disableDayFn && c.disableDayFn(i), q = c.minDate && i < c.minDate || c.maxDate && i > c.maxDate || n, r = h(this._d) ? 0 === p(i, this._d) : !1, o = !1, s = 0 === p(i, d), n = n ? c.disableDayFnTitle || "" : "", t = j < f || j >= e + f; this._sd && !this._ed ? o = 0 === p(i, this._sd) :
                        this._sd && this._ed && (o = 0 === p(i, this._sd) || 0 === p(this._ed, i) ? !0 : 0 <= p(i, this._sd) && 0 <= p(this._ed, i)); if (0 === p(i, this._sd) || 0 === p(i, this._ed)) r = !0; k.push(C(1 + (j - f), b, a, r, s, q, t, o, n)); 7 === ++m && (l.push((c.isRTL ? k.reverse() : k).join("")), k = [], m = 0)
                } e = []; f = c.containerTag; for (d = 0; 7 > d; d++)e.push("<li>" + B(c, d, !0) + "</li>"); return '<ul class="' + f + '-weeks">' + (c.isRTL ? e.reverse() : e).join("") + '</ul><ul class="' + c.containerTag + '-days">' + l.join("") + "</ul>"
        }, isVisible: function () { return this._v }, show: function () {
            if (!this._v) {
                var a =
                    this.el, b; b = (" " + a.className + " ").replace(" is-hidden ", " "); b = b.trim ? b.trim() : b.replace(/^\s+|\s+$/g, ""); a.className = b; this._v = !0; this.draw(); this._o.bound && (n(k, "click", this._onClick), this.adjustPosition()); "function" === typeof this._o.onOpen && this._o.onOpen.call(this)
            }
        }, hide: function () { var a = this._v; !1 !== a && (this._o.bound && q(k, "click", this._onClick), this.el.style.cssText = "", z(this.el, "is-hidden"), this._v = !1, void 0 !== a && "function" === typeof this._o.onClose && this._o.onClose.call(this)) }, destroy: function () {
            this.hide();
            q(this.el, "mousedown", this._onMouseDown, !0); q(this.el, "change", this._onChange); this._o.field && (q(this._o.field, "change", this._onInputChange), this._o.bound && (q(this._o.trigger, "click", this._onInputClick), q(this._o.trigger, "focus", this._onInputFocus), q(this._o.trigger, "blur", this._onInputBlur))); this.el.parentNode && this.el.parentNode.removeChild(this.el); this._ed = this._sd = null
        }
    }; m.prototype._fireEvent = y; m.prototype._isDate = h; m.prototype._isArray = u; var D = "Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),
        E = "Sun Mon Tue Wed Thu Fri Sat".split(" "); m.prototype.toString = function () { return this._o.timeRange ? (!this._isDate(this._sd) ? "" : this.format(this._sd)) + this._o.rangeJoiner + (!this._isDate(this._ed) ? "" : this.format(this._ed)) : !this._isDate(this._d) ? "" : this.format(this._d) }; m.prototype.format = function (a) {
            var b, c = "YYYY-MM-DD" === this._o.format ? "MM dd, yyyy" : this._o.format; if (!c) return a.toLocaleString(); if (!this._isDate(a)) return ""; this._o.i18n.monthShortVal || (this._o.i18n.monthShortVal = D, this._o.i18n.weekShortVal =
                E); a = { "y+": a.getFullYear(), "m+": a.getMonth() + 1, "M+": a.getMonth(), "d+": a.getDate(), "W+": a.getDay() }; for (b in a) RegExp("(" + b + ")").test(c) && ("M+" === b && this._o.i18n.monthShortVal && (c = c.replace(RegExp.$1, this._o.i18n.monthShortVal[a[b]])), "W+" === b && this._o.i18n.monthShortVal && (c = c.replace(RegExp.$1, this._o.i18n.weekShortVal[a[b]])), c = c.replace(RegExp.$1, 1 === RegExp.$1.length || "y+" === b ? a[b] : ("00" + a[b]).substr(("" + a[b]).length))); return c
        }; var F = m.prototype.setDate, G = m.prototype.getDate; m.prototype.setDate =
            function (a, b) {
                if (this._isArray(a) && 2 === a.length) {
                    var c = a[0], d = a[1]; c && "string" === typeof c && (c = new Date(Date.parse(c))); d && "string" === typeof d && (d = new Date(Date.parse(d))); if (this._isDate(c) && this._isDate(d)) {
                        var e = this._o.minDate, f = this._o.maxDate; this._isDate(e) && c < e ? c = e : this._isDate(f) && d > f && (d = f); c.setHours(0, 0, 0, 0); d.setHours(0, 0, 0, 0); this._sd = new Date(c.getTime()); this._ed = new Date(d.getTime()); this.gotoDate(this._ed); this._rangeFinsh = !0; this._o.field && (this._o.field.value = this.toString(), this._fireEvent(this._o.field,
                            "change", { firedBy: this }))
                    } !b && "function" === typeof this._o.onSelect && this._o.onSelect.call(this, this.getDate()); this.draw()
                } else F.call(this, a, b)
            }; m.prototype.getDate = function () { return this._o.timeRange ? [this._isDate(this._sd) ? this.format(this._sd) : null, this._isDate(this._ed) ? this.format(this._ed) : null] : G.call(this) }; m.prototype.clearRange = function () { this._ed = this._sd = null; this.draw() }; return m
});
