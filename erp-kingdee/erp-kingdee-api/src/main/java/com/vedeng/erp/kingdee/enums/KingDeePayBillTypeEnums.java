package com.vedeng.erp.kingdee.enums;

/**
 * <AUTHOR>
 * @desc 付款单单据类型
 */
public enum KingDeePayBillTypeEnums {
    BUYORDER_PAY("付款单","FKDLX01_SYS","采购业务付款单"),
    OTHER_PAY("付款单","FKDLX02_SYS","其他业务付款单"),
    SALARY_PAY("付款单","FKDLX03_SYS","工资发放付款单"),
    EXPENSE_PAY("付款单","FKDLX04_SYS","费用报销付款单"),
    FUND_PAY("付款单","FKDLX05_SYS","资金上划付款单"),
    INSIDE_PAY("付款单","FKDLX06_SYS","内部利息付款单"),
    ALLOCATE_PAY("付款单","FKDLX07_SYS","资金调拨付款单"),
    SECURITY_PAY("付款单","FKDLX08_SYS","保证金付款单");
    /**
     * 单据
     */
    private final String orderType;

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    public String getOrderType() {
        return orderType;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    KingDeePayBillTypeEnums(String orderType, String code, String name){
        this.orderType = orderType;
        this.code = code;
        this.name = name;
    }
}
