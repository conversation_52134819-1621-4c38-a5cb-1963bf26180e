package com.wms.service.chain.step;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.PutSaleOrderDto;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.dto.StockAllocationResult;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 样品单出库
 */
@Service
public class SampleOutWmsRequestHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(SampleOutWmsRequestHandler.class);

    @Resource
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private WmsSendOrderMapper wmsSendOrderMapper;

    @Resource
    private WmsOutputOrderMapper wmsOutputOrderMapper;


    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Long sampleOutOrderId = context.get("sampleOutOrderId");

        List<StockAllocationResult> allocationResultList = context.get("allocationResultList");

        List<WmsOutputOrderGoods> outputOrderGoodList = context.get("outputOrderGoodList");

        WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.selectByPrimaryKey(sampleOutOrderId);
        SampleOutWmsRequestHandler bean = SpringUtil.getBean(SampleOutWmsRequestHandler.class);
        Integer wmsSendOrderId = bean.saveWmsSendOrder(wmsOutputOrder);
        PutSaleOrderDto putSaleOrderDto = new PutSaleOrderDto();
        putSaleOrderDto.setDocNo(wmsOutputOrder.getOrderNo());
        putSaleOrderDto.setOrderType(WmsInterfaceOrderType.SAMPLE_ORDER_OUT);
        putSaleOrderDto.setOrderTime(wmsOutputOrder.getAddTime());
        putSaleOrderDto.setExpectedShipmentTime1(wmsOutputOrder.getAppleOutDate());
        putSaleOrderDto.setSoReferenceA(wmsOutputOrder.getApplyerDepartment());
        putSaleOrderDto.setSoReferenceB(wmsOutputOrder.getApplyer());
        putSaleOrderDto.setConsigneeId("0");
        //
        putSaleOrderDto.setHedi07("A");
        putSaleOrderDto.setNotes(wmsOutputOrder.getLogisticCommnet());
        putSaleOrderDto.setConsigneeId(wmsOutputOrder.getBorrowTraderId() + Strings.EMPTY);
        putSaleOrderDto.setConsigneeName(wmsOutputOrder.getBorrowTraderName());
        String receiverAddress = wmsOutputOrder.getReceiverAddress();
        List<String> split = StrUtil.split(receiverAddress, StrUtil.SPACE);

        if (CollUtil.isNotEmpty(split)) {
            putSaleOrderDto.setConsigneeProvince(split.get(0));
            putSaleOrderDto.setConsigneeCity(split.size() >= 2 ? split.get(1) : "");
            putSaleOrderDto.setConsigneeDistrict(split.size() >= 3 ? split.get(2) : "");
        } else {
            putSaleOrderDto.setConsigneeProvince("");
            putSaleOrderDto.setConsigneeCity("");
            putSaleOrderDto.setConsigneeDistrict("");
        }

        putSaleOrderDto.setConsigneeAddress1(wmsOutputOrder.getDetailAddress());
        putSaleOrderDto.setConsigneeContact(wmsOutputOrder.getReceiver());
        putSaleOrderDto.setConsigneeTel1(wmsOutputOrder.getReceiverTelphone());

        putSaleOrderDto.setDetails(convertAllocationResult(allocationResultList, outputOrderGoodList));

        try {
            LOGGER.info("WMS样品出库单下传的 单号:{},请求:{}", wmsOutputOrder.getOrderNo(), JSON.toJSONString(putSaleOrderDto));
            //XxlJobLogger.log("WMS样品出库单下传的 单号:{},请求:{}", wmsOutputOrder.getOrderNo(), JSON.toJSONString(putSaleOrderDto));
            WmsInterface putSaleOrderOutputInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);

            WmsResponse response = putSaleOrderOutputInterface.request(putSaleOrderDto);

            if ("1".equals(response.getReturnFlag())) {
                WmsSendOrder updateWmsSendOrder = new WmsSendOrder();
                updateWmsSendOrder.setWmsSendOrderId(wmsSendOrderId);
                updateWmsSendOrder.setSendStatus(ErpConst.ONE);
                wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendOrder);
            }

            LOGGER.info("WMS样品出库单下传的 单号:{},响应:{}", wmsOutputOrder.getOrderNo(), JSON.toJSONString(response));
            //XxlJobLogger.log("WMS样品出库单下传的 单号:{},响应:{}", wmsOutputOrder.getOrderNo(), JSON.toJSONString(response));
        } catch (Exception e) {
            LOGGER.error("下发样品出库单失败error 单号:" + wmsOutputOrder.getOrderNo(), e);
            //XxlJobLogger.log("下发样品出库单失败error 单号:" + wmsOutputOrder.getOrderNo(), e);
        }

    }

    private List<PutSaleOrderGoodsDto> convertAllocationResult(List<StockAllocationResult> allocationResultList, List<WmsOutputOrderGoods> outputOrderGoodList) {

        if (CollectionUtils.isEmpty(allocationResultList)) {
            return null;
        }

        return allocationResultList.stream()
                .map(wmsOutputOrderGood -> {
                    PutSaleOrderGoodsDto putSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
                    putSaleOrderGoodsDto.setSku(wmsOutputOrderGood.getSku());
                    putSaleOrderGoodsDto.setQtyOrdered(wmsOutputOrderGood.getNum());
                    putSaleOrderGoodsDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(wmsOutputOrderGood.getLogicalWarehouseId()));
                    putSaleOrderGoodsDto.setDedi07(getWmsLendoutGoodId(outputOrderGoodList, wmsOutputOrderGood.getSku()) + StringUtils.EMPTY);
                    putSaleOrderGoodsDto.setLotAtt07("*");
                    return putSaleOrderGoodsDto;
                }).collect(Collectors.toList());

    }

    public Long getWmsLendoutGoodId(List<WmsOutputOrderGoods> outputOrderGoodList, String sku) {

        return outputOrderGoodList.stream()
                .filter(outputOrderGood -> outputOrderGood.getSkuNo().equals(sku))
                .map(outputOrderGood -> outputOrderGood.getId())
                .findFirst()
                .orElse(0L);

    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Integer saveWmsSendOrder(WmsOutputOrder wmsOutputOrder) {
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(WmsSendOrderTypeEnum.SAMPLE_OUT.getCode());
            Long id = wmsOutputOrder.getId();
            wmsSendOrder.setOrderId(Math.toIntExact(id));
            wmsSendOrder.setOrderNo(wmsOutputOrder.getOrderNo());
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if (oldInfo != null) {
                WmsSendOrder update = new WmsSendOrder();
                update.setSendStatus(0);
                update.setWmsSendOrderId(oldInfo.getWmsSendOrderId());
                wmsSendOrderMapper.updateByPrimaryKeySelective(update);
                return oldInfo.getWmsSendOrderId();
            }
            Integer userId = CurrentUser.getCurrentUser().getId();
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            LOGGER.error("saveWmsSendOrder error，下发样品出库单：{}", wmsOutputOrder.getOrderNo(), e);
            //XxlJobLogger.log("saveWmsSendOrder error，下发样品出库单：{}", wmsOutputOrder.getOrderNo(), e);
        }
        return wmsSendOrder.getWmsSendOrderId();
    }
}

