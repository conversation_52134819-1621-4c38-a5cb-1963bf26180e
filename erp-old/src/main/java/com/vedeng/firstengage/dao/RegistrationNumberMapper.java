package com.vedeng.firstengage.dao;

import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.firstengage.model.vo.RegisterSkuVo;
import com.wms.model.po.WmsSkuReg;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RegistrationNumberMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	int deleteByPrimaryKey(Integer registrationNumberId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	int insert(RegistrationNumber record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	int insertSelective(RegistrationNumber record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	RegistrationNumber selectByPrimaryKey(Integer registrationNumberId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	int updateByPrimaryKeySelective(RegistrationNumber record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	int updateByPrimaryKey(RegistrationNumber record);

	/**
	 * registrationNumberMapper根据输入查询注册证
	 * <p>Title: getRegistrationInfoByStr</p>  
	 * <p>Description: </p>  
	 * @param paramMap
	 * @return  
	 * <AUTHOR>
	 * @date 2019年3月21日
	 */
	List<RegistrationNumber> getRegistrationInfoByStr(Map<String, Object> paramMap);

	/**
	 * 查询注册证信息
	 * <p>Title: getRegistrationInfoById</p>  
	 * <p>Description: </p>  
	 * @param paramMap
	 * @return  
	 * <AUTHOR>
	 * @date 2019年3月27日
	 */
	RegistrationNumber getRegistrationInfoById(Map<String, Object> paramMap);

	/**
	 * @description 根据注册证号查询注册证信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/4/24
	 */
    RegistrationNumber getRegistrationInfoByNumber(String registrationNumber);

    /**
     * @description 刷临效期状态
     * <AUTHOR>
     * @param
     * @date 2019/5/28
     */
    void refreshFirstList(Map<String, Object> param);

	List<RegistrationNumber> getRefreshFirstList(Map<String, Object> paramMap);


    Integer dealstatus(Integer registrationNumberId);


    List<RegistrationNumber> getPCInfo();


    void insertList(List<RegistrationNumber> list);

	List<RegistrationNumber> getHisRegFromGoods();

	/**
	 * 根据skuid获取商品注册证信息 供下传wms使用
	 * @Param: [skuId]
	 * @Return: com.wms.model.po.WmsSkuReg
	 * @Author: Rivan
	 * @Date: 2020/7/31 10:03
	 */
	WmsSkuReg getWmsSkuRegData(Integer skuId);

	/**
	 * 全量获取商品注册证信息 下传wms
	 * @Param: []
	 * @Return: java.util.List<com.wms.model.po.WmsSkuReg>
	 * @Author: Rivan
	 * @Date: 2020/7/31 10:04
	 */
	List<WmsSkuReg> getWmsSkuRegList();

	List<WmsSkuReg> getWmsSkuRegListInSkuNoStr(@Param("skuNoStr") String skuNoStr);

	/**
	 * 根据首营编号获取其关联的注册证信息
	 *
	 * @param firstEngageId
	 * @return
	 */
	RegistrationNumber getRegistrationNumberByFirstEngageId(Integer firstEngageId);

	/**
	 * <b>Description:</b>分页查询注册证<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/25
	 */
	List<RegistrationNumber> getNumberListPage(Map<String,Object> param);

	/**
	 * <b>Description:</b>获取供应商注册证列表<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/30
	 */
    List<RegisterSkuVo> getSupplierRegisterListPage(Map<String,Object> param);

	List<RegistrationNumber> selectManufacturer();

	List<RegistrationNumber> queryByName(String name);

	String selectBymanufacturerId(Integer manufacturerId);
}