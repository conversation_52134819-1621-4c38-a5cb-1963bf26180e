<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/ui.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/selectProd.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/scene/detail.css">


<div class="page-wrap" id="page-container">
    <div class="page-detail-container">
        <div class="page-detail-header">
            <div class="header-title">{{ title }}</div>
            <div class="header-options">
                <ui-button type="primary" @click="submitScene">提交</ui-button>
            </div>
        </div>
        <div class="page-detail-content form-wrap" v-if="!isloading">
            <div class="card-block">
                <div class="card-title">基础信息</div>
                <ui-form-item label="场景名称" :must="true">
                    <div class="ui-col-4">
                        <ui-input
                            type="input"
                            maxlength="10"
                            v-model="sceneName"
                            placeholder="可按照医院性质新建场景，不超过10个字"
                            :error-msg="sceneNameError"
                            :errorable="!!sceneNameError"
                            @blur="validSceneName"
                        ></ui-input>
                    </div>
                </ui-form-item>
                <ui-form-item label="场景说明">
                    <ui-input
                        type="textarea"
                        maxlength="1000"
                        show-word-limit
                        v-model="sceneDescription"
                        height-auto
                        height="35px"
                        width="750px"
                        placeholder="可主要描述下该方案的建议和注意事项等。"
                    ></ui-input>
                </ui-form-item>
            </div>
            <div class="card-block">
                <div class="card-title">
                    <div class="title-txt">产品信息</div>
                    <div class="title-options">
                        <div class="option-item" @click="addCategory">
                            <i class="vd-ui_icon icon-add"></i>
                            <div class="item-txt">添加分类</div>
                        </div>
                    </div>
                </div>
                <div class="detail-prod-tabs-placeholder" v-show="tabInfo.length">
                    <div class="detail-prod-tabs">
                        <draggable v-model="tabInfo" class="tabs-list" ghost-class="placehodler" @sort="handlerTabSort">
                            <div class="tab-item sortable" @click="changeTab(item.index)" :class="{active: item.index === currentTabIndex}" v-for="(item, index) in tabInfo">
                                <div class="tab-txt">{{ item.name }}（{{ item.productInfoList.length }}）</div>
                                <div class="tab-option" @click.stop>
                                    <i class="vd-ui_icon icon-app-more"></i>
                                    <div class="tab-option-drop">
                                        <div class="tab-option-drop-item" @click="editCategory(item, index)">重命名</div>
                                        <div class="tab-option-drop-item" @click="copyCategory(item, index)">复制</div>
                                        <div class="tab-option-drop-item red" @click="deleteCategory(index, item)">删除</div>
                                    </div>
                                </div>
                            </div>
                        </draggable>
                    </div>
                </div>
                <div class="detail-prod-list-wrap">
                    <div class="detail-prod-options-placeholder" ref="optionWrap" v-show="tabInfo.length">
                        <div class="detail-prod-options" :class="{fixed: optionFixed}">
                            <ui-select-button>
                                <div @click="showSelectProd(1)">添加产品</div>
                                <template v-slot:drop>
                                    <div @click="showMultiAdd">批量sku添加</div>
                                </template>
                            </ui-select-button>
                            <ui-button @click="multiDeleteProduct">删除</ui-button>
                            <div class="prod-select-num" v-if="prodSelected.length">已选{{ prodSelected.length }}项</div>
                        </div>
                    </div>
                    <div v-show="currentProdList && currentProdList.length">
                        <ui-table 
                            :headers="prodListHeader" 
                            :list="currentProdList" 
                            ref="tableList" 
                            :left-fixed="false" 
                            :fixed-top="tableFixedTop"
                            :can-choose="true" 
                            table-size="small"
                            @selectchange="prodSelectChange"
                        >
                            <template v-slot:baseinfo="{ row }">
                                <div class="td-cnt-wrap">
                                    <div class="prod-info-wrap">
                                        <div class="prod-img">
                                            <img :src="row.basicInfo.imageUrl || VD_UI_GLOBAL.defaultImg" alt="">
                                        </div>
                                        <div class="prod-info">
                                            <div class="prod-sku" @click="gotoGoodsDetail(row.basicInfo.skuNo)">{{ row.basicInfo.skuNo }}</div>
                                            <div class="prod-name text-line-2" :title="row.basicInfo.skuName">{{ row.basicInfo.skuName }}</div>
                                            <div class="prod-detail">
                                                <div class="detail-label">品牌：</div>
                                                <div class="detail-txt">{{ row.basicInfo.brandName || '-' }}</div>
                                            </div>
                                            <div class="prod-detail">
                                                <div class="detail-label">型号/规格：</div>
                                                <div class="detail-txt">{{ row.basicInfo.modelOrSpec || '-' }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template v-slot:mainparam="{ row }">
                                <template v-if="row.mainParameters.mainParam && row.mainParameters.mainParam.length">
                                    <div class="td-cnt-wrap" v-html="row.mainParameters.mainParam.join('<br>')"></div>
                                </template>
                                <template v-else>-</template>
                            </template>
                            <template v-slot:priceinfo="{ row }">
                                <div class="td-cnt-wrap">
                                    <div class="prod-detail">
                                        <div class="detail-label">分销价：</div>
                                        <template v-if="row.priceInfo.priceStatus === 0">
                                            <div class="detail-txt">未核价</div>
                                        </template>
                                        <template v-else>
                                            <div class="detail-txt red" v-if="row.priceInfo.distributionPrice || row.priceInfo.distributionPrice === 0">{{ row.priceInfo.distributionPrice.toFixed(2) }}</div>
                                            <div class="detail-txt" v-else>-</div>
                                        </template>
                                    </div>
                                    <div class="prod-detail">
                                        <div class="detail-label">终端价：</div>
                                        <template v-if="row.priceInfo.priceStatus === 0">
                                            <div class="detail-txt">未核价</div>
                                        </template>
                                        <template v-else>
                                            <div class="detail-txt red" v-if="row.priceInfo.terminalPrice || row.priceInfo.terminalPrice === 0">{{ row.priceInfo.terminalPrice.toFixed(2) }}</div>
                                            <div class="detail-txt" v-else>-</div>
                                        </template>
                                    </div>
                                </div>
                            </template>
                            <template v-slot:saleinfo="{ row }">
                                <div class="td-cnt-wrap">
                                    <div class="prod-detail">
                                        <div class="detail-label">使用年限：</div>
                                        <div class="detail-txt">{{ row.afterSaleInfo.useLife || '-' }}</div>
                                    </div>
                                    <div class="prod-detail">
                                        <div class="detail-label">质保：</div>
                                        <div class="detail-txt">{{ row.afterSaleInfo.warrantyInfo || '-' }}</div>
                                    </div>
                                    <div class="prod-detail">
                                        <div class="detail-txt">{{ row.afterSaleInfo.isInstall == 1 ? '免费安装（偏远地区除外）' : '' }}</div>
                                    </div>
                                </div>
                            </template>
                            <template v-slot:prodstatus="{ row }">
                                <div class="td-cnt-wrap">
                                    <div class="prod-detail">
                                        <div class="detail-label">审核状态：</div>
                                        <div class="detail-txt red" :class="{orange: row.productStatus.auditStatus == 1, green: row.productStatus.auditStatus == 3}">{{ ['待完善', '审核中', '审核不通过', '审核通过', '删除', '待提交审核'][row.productStatus.auditStatus] }}</div>
                                    </div>
                                    <div class="prod-detail">
                                        <div class="detail-label">商品等级：</div>
                                        <div class="detail-txt" :class="{orange: row.productStatus.goodsLevelNo == 5}">{{ row.productStatus.goodsLevelNoLabel }}</div>
                                    </div>
                                    <div class="prod-detail">
                                        <div class="detail-label">商品挡位：</div>
                                        <div class="detail-txt">{{ row.productStatus.goodsPositionNoLabel }}</div>
                                    </div>
                                </div>
                            </template>
                            <template v-slot:manager="{ row }">
                                <div class="td-cnt-wrap">
                                    <div class="prod-detail">
                                        <div class="detail-label">产品经理：</div>
                                        <div class="detail-txt">
                                            <div class="user-wrap" v-if="row.productManager">
                                                <div class="user-avatar">
                                                    <img :src="row.productManager.aliasHeadPicture || VD_UI_GLOBAL.defaultImg" alt="">
                                                </div>
                                                <div class="user-name text-line-1">{{ row.productManager.username }}</div>
                                            </div>
                                            <div v-else>-</div>
                                        </div>
                                    </div>
                                    <div class="prod-detail">
                                        <div class="detail-label">产品助理：</div>
                                        <div class="detail-txt">
                                            <div class="user-wrap" v-if="row.productAssistant">
                                                <div class="user-avatar">
                                                    <img :src="row.productAssistant.aliasHeadPicture || VD_UI_GLOBAL.defaultImg" alt="">
                                                </div>
                                                <div class="user-name text-line-1">{{ row.productAssistant.username }}</div>
                                            </div>
                                            <div v-else>-</div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template v-slot:option="{ row }">
                                <div class="option-item" @click="replaceProd(row.basicInfo.skuNo)">替换产品</div>
                                <div class="option-item" @click="deleteProduct(row.basicInfo.skuNo, 1)">删除</div>
                            </template>
                        </ui-table>
                    </div>
                    <div class="prod-empty-wrap" v-if="!tabInfo.length || !(currentProdList && currentProdList.length)">
                        <div class="empty-img"></div>
                        <div class="empty-txt"><template v-if="!tabInfo.length">暂未添加分类</template><template v-else>暂未添加产品</template></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <ui-dialog
        :visible.sync="isShowEditDialog"
        title="添加分类"
        width="720px"
    >
        <div class="form-wrap label-width-2" v-if="isShowEditDialog">
            <ui-form-item label="分类名称" :must="true">
                <div class="ui-col-4">
                    <ui-input maxlength="10" v-model="editCategoryName" valid="categoryFrom_editCategoryName"></ui-input>
                </div>
                <div class="form-tip">- 可通过科室/兵种新建分类，不超过10个字</div>
            </ui-form-item>
        </div>
        <template slot="footer">
            <div class="dlg-form-footer">
                <ui-button type="primary" @click="saveCategory">保存</ui-button>
                <ui-button class="close" @click="isShowEditDialog = false">取消</ui-button>
            </div>
        </template>
    </ui-dialog>
    <ui-dialog
        :visible.sync="isShowMultiAdd"
        title="添加产品"
        width="720px"
    >
        <div class="multi-add-wrap">
            <div class="multi-add-l">
                <ui-input type="textarea" :errorable="!!multiAddErrorMsg" 
                :placeholder="`案例：\nV136266\nV166977`" 
                min-height="480px" resize="none" @blur="hanlderMultiAddInputBlur" v-model="multiValue"></ui-input>
            </div>
            <div class="multi-add-r">
                <div class="multi-add-tips">
                    可输入多个订货号，并使用回车换行区分；<br>
                    单次操作最多支持选择 200 个订货号；<br>
                    添加商品排序会与输入的订货号顺序保持一致；<br>
                    可直接复制粘贴EXCEL“列”，程序将会自动去除空行及订货号中的空格；
                </div>
                <div class="multi-error-wrap" v-if="multiAddErrorMsg">
                    <i class="vd-ui_icon icon-error2"></i>
                    <div class="error-txt">{{ multiAddErrorMsg }}</div>
                </div>
            </div>
        </div>
        <template slot="footer">
            <ui-button type="primary" @click="multiSkuAddProdConfirm">确定</ui-button>
            <ui-button @click="isShowMultiAdd = false">取消</ui-button>
        </template>
    </ui-dialog>
    <!-- 选产品 -->
    <ui-select-prod ref="selectProd" @select="handlerProdSelect"></ui-select-prod>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/vd-ui/selectProd.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/vd-ui/vuedraggable.js"></script>
<script src="${pageContext.request.contextPath}/static/js/scene/add.js?rnd=${resourceVersionKey}"></script>
