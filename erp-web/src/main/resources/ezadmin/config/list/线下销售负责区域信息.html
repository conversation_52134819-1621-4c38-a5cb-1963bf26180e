<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>线下销售负责区域信息</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">

</head>
<body id="customerRegionSale" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show=""  firstcol="numbers">
<div id="appendHead">
    <style type="text/css">
        /*.edit-table-head{*/
        /*    display: none;*/
        /*}*/
        /*button.ezopenbutton{*/
        /*    display: none !important;*/
        /*}*/
    </style>
    <script type="text/javascript">
        // $(document).ready(function() {
        //     var objVisitorId1 = $('#itemName-VISITOR_ID');
        //     // var xmel = $(objVisitorId)[0];
        //     // //var initdata=objVisitorId.attr("itemsJson");
        //     var initvalue1=objVisitorId1.attr("value");
        //     // 创建隐藏域
        //     var input = $('<input>').attr({
        //         type: 'hidden',
        //         id:'VISITOR_ID_HIDDEN',
        //         name: 'VISITOR_ID',
        //         value: initvalue1
        //     });
        //
        //     // 将隐藏域添加到form中
        //     $('#searchForm').append(input);
        // });
    </script>

</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <!--        <li class="layui-this"><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
            <!--        <li><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->

        </ul>
    </div>
    <form class="layui-form" id="search">




        <div class=" layui-inline ">
            <label class="layui-form-label">负责区域</label>
            <div class="layui-input-inline">
                <object   class=" layui-input list-search-item " type="search-region" name="REGION"  multiple="false"  placeholder="" style="" alias = "AREA.REGION_ID"   data="" datatype="" oper="in" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">线上销售</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select-share" name="USER_ID" placeholder="" style="" radio="true" alias="SALE.USER_ID" jdbctype="INTEGER" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">维护线上销售</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="GUISHU_ON"  placeholder="全部" jdbctype="BODY"
                        data='[{"K":"1","V":"是"},{"K":"2","V":"否"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">线下销售</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select-share" name="USER_ID_DOWN" placeholder="" style="" radio="true" alias="SALE.USER_ID_DOWN" jdbctype="INTEGER" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">维护线下销售</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="GUISHU_DOWN"  placeholder="全部" jdbctype="BODY"
                        data='[{"K":"1","V":"是"},{"K":"2","V":"否"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">是否有离职</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="LIZHI"  placeholder="全部" jdbctype="BODY"
                        data='[{"K":"1","V":"是"},{"K":"2","V":"否"}]'
                        datatype="json"></select>
            </div>
        </div>





    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <!--<button item_name="修改拜访人" name="修改拜访人" type="table">修改拜访人</button>-->
        <button item_name="添加/修改/清除区域关系" name="batchCreate" type="table" >添加/修改/清除区域关系</button>
<!--
        <button item_name="批量删除" name="batchDelete" type="table" >批量删除</button>
-->
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="PROVINCE_NAME" name="REGION_NAME" body="td-text"  data="" style="text-align:LEFT;min-width:80px;width: 80px;max-width: 180px;word-break: break-all;" head="18">省</th>
            <th item_name="CITY_NAME" name="CITY_NAME" body="td-text" url=""  data="" style="text-align:LEFT;width: 100px;min-width: 60px;max-width: 180px;" head="18" >市</th>
            <th item_name="AREA_NAME" name="AREA_NAME" body="td-text" url=""  data="" style="text-align:LEFT;width: 100px;min-width: 60px;max-width: 180px;" head="18" >区</th>
            <!--<th item_name="AREA_ID" name="AREA_ID" body="td-text" url=""  data="" style="text-align:LEFT;width: 100px;min-width: 60px;max-width: 180px;" head="18" >区ID</th>-->
            <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" url=""  data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18">部门</th>
            <th item_name="USERNAME" name="USERNAME" body="td-text" url=""  data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18">线上销售</th>
            <th item_name="USER_NAME_DOWN" name="USER_NAME_DOWN" body="td-text" url=""  data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18">线下销售</th>

            <th item_name="MOD_TIME" name="MOD_TIME" body="td-text" url=""  data="" style="" head="18">修改时间</th>
            <!--<th type="rowbutton" id="rowbutton" style="min-width: 60px">
                <button type="single" opentype="CONFIRM_AJAX" url="/ezadmin/form/doDelete-modifyCustomerRegionSale?ID=${ID}"
                        windowname="删除" name="MODEL" >删除
                </button>

            </th>-->

        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by AREA.REGION_ID asc" groupby=" group by AREA.REGION_ID ">
    StringBuilder sql=new StringBuilder("

       select
	PROVINCE.REGION_ID AS PROVINCE_ID,
	PROVINCE.REGION_NAME AS PROVINCE_NAME,
	CITY.REGION_ID AS CITY_ID,
	CITY.REGION_NAME AS CITY_NAME,
	AREA.REGION_ID  AS AREA_ID,
	AREA.REGION_NAME AS AREA_NAME,
    SALE.PUBLIC_CUSTOMER_REGION_RULES_ID AS ID,
	SALE.USER_ID,
	case WHEN TUSER.IS_DISABLED = 1 THEN concat(TUSER.USERNAME,'(离职)') ELSE TUSER.USERNAME END AS USERNAME,
    case WHEN TUSER1.IS_DISABLED = 1 THEN concat(TUSER1.USERNAME,'(离职)') ELSE TUSER1.USERNAME END AS USER_NAME_DOWN,
    TEMPU.ORG_NAME as ORG_NAME,
    DATE_FORMAT(FROM_UNIXTIME(SALE.MOD_TIME/1000), '%Y-%m-%d %H:%i:%s') AS MOD_TIME
from
	T_REGION AREA
LEFT JOIN T_REGION CITY ON
	AREA.PARENT_ID = CITY.REGION_ID
LEFT JOIN T_REGION PROVINCE ON
	CITY.PARENT_ID = PROVINCE.REGION_ID
LEFT JOIN T_CUSTOMER_REGION_SALE SALE  ON AREA.REGION_ID =SALE.REGION_ID
LEFT JOIN T_USER TUSER ON SALE.USER_ID = TUSER.USER_ID

LEFT JOIN T_USER TUSER1 ON SALE.USER_ID_DOWN = TUSER1.USER_ID
 LEFT JOIN (
  		SELECT TUSER1.USER_ID ,group_concat(d.ORG_NAME) as ORG_NAME   FROM T_USER TUSER1
	        LEFT JOIN
	        T_R_USER_POSIT UP
	            ON UP.USER_ID = TUSER1.USER_ID
	    left join
	        T_POSITION c
	            on  c.POSITION_ID = UP.POSITION_ID
	    left join
	        T_ORGANIZATION d
	            on  d.ORG_ID = c.ORG_ID

	            group by TUSER1.USER_ID
    ) TEMPU ON TUSER.USER_ID  =TEMPU.USER_ID



WHERE
	AREA.REGION_ID>100000
	AND AREA.REGION_TYPE = 3");
lizhi = $("LIZHI");
if ( isNotBlank("LIZHI","request")&amp;&amp; lizhi.toString().equals("1")) {
     sql.append (" AND (TUSER.IS_DISABLED =1 OR TUSER1.IS_DISABLED =1)");
}
if ( isNotBlank("LIZHI","request")&amp;&amp; lizhi.toString().equals("2")) {
     sql.append (" AND (TUSER.IS_DISABLED =0 AND TUSER1.IS_DISABLED =0)");
}
guishuOn = $("GUISHU_ON");
if ( isNotBlank("GUISHU_ON","request")&amp;&amp; guishuOn.toString().equals("1")) {
     sql.append (" AND (SALE.USER_ID IS NOT NULL AND  SALE.USER_ID>0) ");
}
if ( isNotBlank("GUISHU_ON","request")&amp;&amp; guishuOn.toString().equals("2")) {
     sql.append (" AND (SALE.USER_ID IS NULL OR  SALE.USER_ID=0) ");
}

guishuDown = $("GUISHU_DOWN");
if ( isNotBlank("GUISHU_DOWN","request")&amp;&amp; guishuDown.toString().equals("1")) {
     sql.append (" AND (SALE.USER_ID_DOWN IS NOT NULL AND  SALE.USER_ID_DOWN>0) ");
}
if ( isNotBlank("GUISHU_DOWN","request")&amp;&amp; guishuDown.toString().equals("2")) {
     sql.append (" AND (SALE.USER_ID_DOWN IS NULL OR  SALE.USER_ID_DOWN=0) ");
}



list=search(sql);


return list;</pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">

    <script>
        var userIdDownInit =  $("div[name='USER_ID_DOWN']").attr("value");
        console.log($("[name='USER_ID_DOWN']").val());
        console.log(userIdDownInit);
        var userIdOnInit =  $("div[name='USER_ID']").attr("value");
        console.log(userIdOnInit);
        function getCheckIds() {
            var ids = [];
            $("input[name='list-body-checkbox']:not(:disabled)").each(function () {
                if (this.checked) {
                    ids.push($(this).attr("_CHECK_ID_VALUE"));
                    //goodsIdArr+=','+$(this).attr("_CHECK_ID_VALUE");
                }
            })
            if(ids.length==0){
                return "";
            }
            return ids.join(',');
        }


        $("button[item_open_title='添加/修改/清除区域关系']").click(function(){
            // if(getCheckIdsUrl()==""){
            //     layer.alert("请选择需要修改拜访人的拜访记录");
            //     return false;
            // }
            // ///$(this).removeClass("ezopenbutton");
            // var s = getCheckIdsUrl();
            // console.log(s);
            openModel("/visitrecord/profile/modifyCustomerRegionSale.do","修改线下销售所负责的区域");
            return false;
        })

        // $("button[item_open_title='批量删除']").click(function(){
        //     var ids = getCheckIds();
        //     if(ids==""){
        //         layer.alert("请选择需要删除的记录");
        //         return false;
        //     }
        //     layer.confirm('确定要删除吗？', {
        //         btn: ['确定', '取消'],
        //         title: '提示'
        //     }, function(){
        //         // 用户点击确定按钮的回调函数
        //         $.ajax({
        //             type: "GET",
        //             url: "/ezadmin/form/doDelete-modifyCustomerRegionSale?ID="+ids,
        //             dataType: 'json',
        //             success: function (data) {
        //                 if(data.success){
        //                     layer.msg('删除成功', {icon: 1});
        //                     setTimeout(function(){
        //                         location.reload();
        //                     },1000);
        //                 }else{
        //                     layer.msg('删除失败', {icon: 2});
        //                 }
        //             }
        //         });
        //     }, function(){
        //         // 用户点击取消按钮的回调函数
        //         //layer.msg('已取消操作', {icon: 2});
        //     });
        //     return false;
        //
        //
        //
        //     // console.log(s);
        //     // openModel("/visitrecord/profile/modifyCustomerRegionSale.do","修改线下销售所负责的区域");
        //     // return false;
        // })

            //var visitorList = eval($("[name='VISITOR_ID']").attr("itemsjson"));
            var sharedSale = xmSelect.render({
                el: '#itemName-USER_ID',
                name:'USER_ID',
                style: {
                    minHeight: '26px',
                    height: '36px'
                },
                toolbar: {
                    show: false,
                    showIcon: false
                },
                size: 'medium',
                filterable: true,
                remoteSearch: true,
                prop: {
                    value: 'userId',
                    name: 'username'
                },
                theme:{
                    color: '#409eff'
                },
                data: [],
                delay: 500,
                radio: true,
                clickClose: true,
                on: function (data){
                    var arr = data.arr;
                    console.log(arr);
                },
                remoteMethod: function (val, cb, show) {
                    if (!val) {
                        return cb([]);
                    }
                    $.ajax({
                        type: "POST",
                        url: "/system/user/searchHasDelete.do?type=ALL",
                        data: {'username': val},
                        dataType: 'json',
                        success: function (data) {
                            cb(data.data)
                        }
                    });
                }
            });

            if(userIdOnInit.length >2){
                sharedSale.setValue([{"userId":userIdOnInit.substring(1,userIdOnInit.length-1),"username":userIdOnInit.substring(1,userIdOnInit.length-1)}]);
                $.ajax({
                    type: "POST",
                    url: "/system/user/searchByUserId.do",
                    data: {'userId': userIdOnInit.substring(1,userIdOnInit.length-1)},
                    dataType: 'json',
                    success: function (data) {
                        sharedSale.setValue(data.data);
                    }
                });
            }
            var sharedSaleDown = xmSelect.render({
                el: '#itemName-USER_ID_DOWN',
                name:'USER_ID_DOWN',
                style: {
                    minHeight: '26px',
                    height: '36px'
                },
                toolbar: {
                    show: false,
                    showIcon: false
                },
                size: 'medium',
                filterable: true,
                remoteSearch: true,
                prop: {
                    value: 'userId',
                    name: 'username'
                },
                theme:{
                    color: '#409eff'
                },
                data: [

                ],
                delay: 500,
                radio: true,
                clickClose: true,
                on: function (data){
                    var arr = data.arr;
                    console.log(arr);
                },
                remoteMethod: function (val, cb, show) {
                    if (!val) {
                        return cb([]);
                    }
                    $.ajax({
                        type: "POST",
                        url: "/system/user/searchHasDelete.do?type=ALL",
                        data: {'username': val},
                        dataType: 'json',
                        success: function (data) {
                            cb(data.data)
                        }
                    });
                }
            });
            if(userIdDownInit.length >2){
                sharedSaleDown.setValue([{"userId":userIdDownInit.substring(1,userIdDownInit.length-1),"username":userIdDownInit.substring(1,userIdDownInit.length-1)}]);
                $.ajax({
                    type: "POST",
                    url: "/system/user/searchByUserId.do",
                    data: {'userId': userIdDownInit.substring(1,userIdDownInit.length-1)},
                    dataType: 'json',
                    success: function (data) {
                        sharedSaleDown.setValue(data.data);
                    }
                });
            }


            // sharedSaleDown.setValue([{"userId":401,"username":"Kerwin.wang"}]);

          // })


    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>

    layui.use(function () {

    })
</script>
</body>
</html>