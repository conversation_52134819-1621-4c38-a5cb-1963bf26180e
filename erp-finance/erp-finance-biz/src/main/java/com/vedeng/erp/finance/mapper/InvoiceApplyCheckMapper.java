package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface InvoiceApplyCheckMapper {
    int deleteByPrimaryKey(Long invoiceApplyCheckId);

    int insert(InvoiceApplyCheckEntity record);

    int insertOrUpdate(InvoiceApplyCheckEntity record);

    int insertOrUpdateSelective(InvoiceApplyCheckEntity record);

    int insertSelective(InvoiceApplyCheckEntity record);

    InvoiceApplyCheckEntity selectByPrimaryKey(Long invoiceApplyCheckId);

    int updateByPrimaryKeySelective(InvoiceApplyCheckEntity record);

    int updateByPrimaryKey(InvoiceApplyCheckEntity record);

    int updateBatch(List<InvoiceApplyCheckEntity> list);

    int updateBatchSelective(List<InvoiceApplyCheckEntity> list);

    int batchInsert(@Param("list") List<InvoiceApplyCheckEntity> list);

    InvoiceApplyCheckEntity findByInvoiceApplyId(@Param("invoiceApplyId")Integer invoiceApplyId);

    void addApplyCheck(@Param("applyId") Integer applyId, @Param("collect") String collect);
}