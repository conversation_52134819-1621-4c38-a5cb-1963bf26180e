<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">

    <el-form :rules="rules" ref="traderCommunicateFeedbackForm" :model="traderCommunicateFeedbackEntity">
        <el-form-item prop="feedback">
            <el-input type="textarea"
                      :rows="8"
                      maxlength=200
                      v-model="traderCommunicateFeedbackEntity.feedback"
                      placeholder="请输入想要了解的信息"></el-input>
        </el-form-item>
        <el-form-item>
            <span>PS:被提及次数较多的，我们会考虑采纳并展示</span>
        </el-form-item>
        <el-form-item style="display: flex; justify-content: center;">
            <el-button type="primary" @click="add">提交</el-button>
        </el-form-item>
    </el-form>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCommunicateFeedback.js?rnd=${resourceVersionKey}"></script>


<script type="text/javascript">
    const viewInfo = {
        traderId: '${traderId}',
        traderCustomerId: '${traderCustomerId}',
    };


    new Vue({
        el: '#app',

        data() {
            return {
                traderCommunicateFeedbackEntity: {
                    traderId: viewInfo.traderId,
                    traderCustomerId: viewInfo.traderCustomerId
                },
                rules: {
                    'feedback': [
                        {required: true, message: '请输入想要了解的信息', trigger: 'blur'}
                    ]
                },
            }
        },

        created() {
        },

        mounted() {
            loadingApp()
        },

        methods: {

            add() {
                this.$refs['traderCommunicateFeedbackForm'].validate((valid) => {
                    if (valid) {
                        this.disabledForm = true;
                        axios({
                            url: '/traderCommunicateFeedback/add.do',
                            method: 'post',
                            data: this.traderCommunicateFeedbackEntity
                        }).then(res => {
                            if (res.data.code === 0) {
                                this.$message({
                                    message: '意见反馈提交成功',
                                    type: 'success'
                                });
                                setTimeout(() => {
                                    this.reInitCommun()
                                }, 1000)
                            } else {
                                this.$message({
                                    message: '提交失败，请重试',
                                    type: 'error'
                                })
                            }
                        }).finally(() => {
                            setTimeout(() => {
                                this.disabledForm = false;
                            }, 1000)
                        });
                    }
                });
            },

            reInitCommun() {
                layer.closeAll();
                parent.layer.closeAll();
            },

        },

    });

</script>

<style>


</style>
