package com.vedeng.erp.saleorder.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @desc api接口返回销售单dto
 */
@Data
public class SaleorderInfoDto {
    /**
     * 销售单id
     */
    private Integer saleorderId;
    /**
     * 销售单号
     */
    private String saleorderNo;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 金额
     */
    private BigDecimal totalAmount;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 生效时间
     */
    private Long validTime;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 发货状态
     */
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    private Long deliveryTime;

    /**
     * 收货状态
     */
    private Integer arrivalStatus;

    /**
     * 收货时间
     */
    private Long arrivalTime;

    /**
     * 确认单审核状态：0：待提交审核；1.审核中；2.审核通过；3.审核不通过、
     */
    private Integer confirmationFormAudit;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 开票方式
     */
    private Integer invoiceMethod;

    /**
     * 付款状态(收款状态) 0未付款 1部分付款 2全部付款
     */
    private Integer paymentStatus;

    /**
     * 订单实际金额
     */
    private BigDecimal realTotalAmount;

    /**
     * 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 开票状态0未开票 1部分开票 2全部开票
     */
    private Integer invoiceStatus;

    /**
     * 锁定状态0未锁定 1已锁定
     */
    private Integer lockedStatus;

    /**
     * 付款方式 字典库
     */
    private Integer paymentType;

    /**
     * 含有账期支付 0无 1有
     */
    private Integer haveAccountPeriod;

    private BigDecimal accountPeriodAmount;

    private BigDecimal retainageAmount;

    private Integer retainageAmountMonth;

    /**
     * 订单原金额
     */
    private BigDecimal originalAmount;

    /**
     * 订单类型0销售订单2备货订单3订货订单5耗材商城订单 6EL小医院 7集采线上订单 8集采线下订单 9线下直销订单
     */
    private Integer orderType;

    /**
     * 货票地址是否相同 0否 1是
     */
    private Integer isSameAddress;

    /**
     * 是否寄送发票 0否 1是
     */
    private Integer isSendInvoice;

    /**
     * 部门ID
     */
   private Integer orgId;

    /**
     * 归属ERP用户ID
     */
    private Integer userId;
    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 手机
     */
    private String traderContactMobile;

    /**
     * 电话
     */
    private String traderContactTelephone;

    /**
     * 联系地址ID
     */
    private Integer traderAddressId;

    /**
     * 联系地区
     */
    private String traderArea;

    /**
     * 联系地址
     */
    private String traderAddress;
    /**
     * 收货联系人ID
     */
    private Integer takeTraderContactId;

    /**
     * 收货联系人名称
     */
    private String takeTraderContactName;

    /**
     * 收货联系人手机
     */
    private String takeTraderContactMobile;

    /**
     * 收货联系人电话
     */
    private String takeTraderContactTelephone;

    /**
     * 收货地址
     */
    private String  takeTraderAddress;

    /**
     * 收票联系人ID
     */
    private Integer invoiceTraderContactId;

    /**
     * 收票联系人名称
     */
    private String invoiceTraderContactName;

    /**
     * 收票联系人手机
     */
    private String invoiceTraderContactMobile;

    /**
     * 收票联系人电话
     */
    private String invoiceTraderContactTelephone;

    /**
     * 收票地址
     */
    private String invoiceTraderAddress;

    /**
     * 支付方式：0线上、1线下
     */
    private Integer paymentMode;

    /**
     * 支付方式：1支付宝、2微信、3银行
     */
    private Integer payType;

    private List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtoList;

    private String contractUrl;


}
