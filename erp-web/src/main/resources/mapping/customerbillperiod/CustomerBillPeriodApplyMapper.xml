<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.customerbillperiod.dao.CustomerBillPeriodApplyMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply">
    <id column="BILL_PERIOD_APPLY_ID" jdbcType="BIGINT" property="billPeriodApplyId" />
    <result column="CUSTOMER_ID" jdbcType="BIGINT" property="customerId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="BILL_PERIOD_TYPE" jdbcType="TINYINT" property="billPeriodType" />
    <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType" />
    <result column="RELATED_ORDER_ID" jdbcType="INTEGER" property="relatedOrderId" />
    <result column="APPLY_AMOUNT" jdbcType="DECIMAL" property="applyAmount" />
    <result column="BILL_PERIOD_START" jdbcType="BIGINT" property="billPeriodStart" />
    <result column="BILL_PERIOD_END" jdbcType="BIGINT" property="billPeriodEnd" />
    <result column="SETTLEMENT_PERIOD" jdbcType="INTEGER" property="settlementPeriod" />
    <result column="EXPECTED_MARGIN" jdbcType="DECIMAL" property="expectedMargin" />
    <result column="APPLY_REASON" jdbcType="VARCHAR" property="applyReason" />
    <result column="BILL_PERIOD_ID" jdbcType="BIGINT" property="billPeriodId" />
    <result column="BEFORE_APPLY_AMOUNT" jdbcType="DECIMAL" property="beforeApplyAmount" />
    <result column="BEFORE_BILL_PERIOD_START" jdbcType="BIGINT" property="beforeBillPeriodStart" />
    <result column="BEFORE_BILL_PERIOD_END" jdbcType="BIGINT" property="beforeBillPeriodEnd" />
    <result column="BEFORE_SETTLEMENT_PERIOD" jdbcType="INTEGER" property="beforeSettlementPeriod" />
    <result column="CHECK_STATUS" jdbcType="TINYINT" property="checkStatus" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    BILL_PERIOD_APPLY_ID, CUSTOMER_ID, COMPANY_ID, BILL_PERIOD_TYPE, OPERATE_TYPE, RELATED_ORDER_ID,
    APPLY_AMOUNT, BILL_PERIOD_START, BILL_PERIOD_END, SETTLEMENT_PERIOD, EXPECTED_MARGIN, 
    APPLY_REASON, BILL_PERIOD_ID, BEFORE_APPLY_AMOUNT, BEFORE_BILL_PERIOD_START, BEFORE_BILL_PERIOD_END, 
    BEFORE_SETTLEMENT_PERIOD, CHECK_STATUS, CREATOR, ADD_TIME, UPDATER, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_CUSTOMER_BILL_PERIOD_APPLY
    where BILL_PERIOD_APPLY_ID = #{billPeriodApplyId,jdbcType=BIGINT}
  </select>
  <select id="getApplyListByCustomerId" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply">
    select *
    from T_CUSTOMER_BILL_PERIOD_APPLY where COMPANY_ID = #{companyId} and CUSTOMER_ID = #{customerId}
    ORDER BY  ADD_TIME  desc
  </select>

  <select id="getApplyTraderAmountBillVolistpage" parameterType="Map" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply">
    select *
    from T_CUSTOMER_BILL_PERIOD_APPLY where COMPANY_ID = 1 and CUSTOMER_ID = #{customerId}
    ORDER BY  BILL_PERIOD_APPLY_ID  desc
  </select>

  <insert id="insert" keyColumn="BILL_PERIOD_APPLY_ID" keyProperty="billPeriodApplyId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD_APPLY (CUSTOMER_ID, COMPANY_ID, BILL_PERIOD_TYPE, 
      OPERATE_TYPE, RELATED_ORDER_ID, APPLY_AMOUNT,
      BILL_PERIOD_START, BILL_PERIOD_END, SETTLEMENT_PERIOD, 
      EXPECTED_MARGIN, APPLY_REASON, BILL_PERIOD_ID, 
      BEFORE_APPLY_AMOUNT, BEFORE_BILL_PERIOD_START, 
      BEFORE_BILL_PERIOD_END, BEFORE_SETTLEMENT_PERIOD, 
      CHECK_STATUS, CREATOR, ADD_TIME, 
      UPDATER, MOD_TIME)
    values (#{customerId,jdbcType=BIGINT}, #{companyId,jdbcType=INTEGER}, #{billPeriodType,jdbcType=TINYINT}, 
      #{operateType,jdbcType=TINYINT}, #{relatedOrderId,jdbcType=INTEGER}, #{applyAmount,jdbcType=DECIMAL},
      #{billPeriodStart,jdbcType=BIGINT}, #{billPeriodEnd,jdbcType=BIGINT}, #{settlementPeriod,jdbcType=INTEGER}, 
      #{expectedMargin,jdbcType=DECIMAL}, #{applyReason,jdbcType=VARCHAR}, #{billPeriodId,jdbcType=BIGINT}, 
      #{beforeApplyAmount,jdbcType=DECIMAL}, #{beforeBillPeriodStart,jdbcType=BIGINT}, 
      #{beforeBillPeriodEnd,jdbcType=BIGINT}, #{beforeSettlementPeriod,jdbcType=INTEGER}, 
      #{checkStatus,jdbcType=TINYINT}, #{creator,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="BILL_PERIOD_APPLY_ID" keyProperty="billPeriodApplyId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD_APPLY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="billPeriodType != null">
        BILL_PERIOD_TYPE,
      </if>
      <if test="operateType != null">
        OPERATE_TYPE,
      </if>
      <if test="relatedOrderId != null">
        RELATED_ORDER_ID,
      </if>
      <if test="applyAmount != null">
        APPLY_AMOUNT,
      </if>
      <if test="billPeriodStart != null">
        BILL_PERIOD_START,
      </if>
      <if test="billPeriodEnd != null">
        BILL_PERIOD_END,
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD,
      </if>
      <if test="expectedMargin != null">
        EXPECTED_MARGIN,
      </if>
      <if test="applyReason != null">
        APPLY_REASON,
      </if>
      <if test="billPeriodId != null">
        BILL_PERIOD_ID,
      </if>
      <if test="beforeApplyAmount != null">
        BEFORE_APPLY_AMOUNT,
      </if>
      <if test="beforeBillPeriodStart != null">
        BEFORE_BILL_PERIOD_START,
      </if>
      <if test="beforeBillPeriodEnd != null">
        BEFORE_BILL_PERIOD_END,
      </if>
      <if test="beforeSettlementPeriod != null">
        BEFORE_SETTLEMENT_PERIOD,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="billPeriodType != null">
        #{billPeriodType,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedOrderId != null">
        #{relatedOrderId,jdbcType=INTEGER},
      </if>
      <if test="applyAmount != null">
        #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="billPeriodStart != null">
        #{billPeriodStart,jdbcType=BIGINT},
      </if>
      <if test="billPeriodEnd != null">
        #{billPeriodEnd,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="expectedMargin != null">
        #{expectedMargin,jdbcType=DECIMAL},
      </if>
      <if test="applyReason != null">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="billPeriodId != null">
        #{billPeriodId,jdbcType=BIGINT},
      </if>
      <if test="beforeApplyAmount != null">
        #{beforeApplyAmount,jdbcType=DECIMAL},
      </if>
      <if test="beforeBillPeriodStart != null">
        #{beforeBillPeriodStart,jdbcType=BIGINT},
      </if>
      <if test="beforeBillPeriodEnd != null">
        #{beforeBillPeriodEnd,jdbcType=BIGINT},
      </if>
      <if test="beforeSettlementPeriod != null">
        #{beforeSettlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply">
    update T_CUSTOMER_BILL_PERIOD_APPLY
    <set>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="billPeriodType != null">
        BILL_PERIOD_TYPE = #{billPeriodType,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedOrderId != null">
        RELATED_ORDER_ID = #{relatedOrderId,jdbcType=INTEGER},
      </if>
      <if test="applyAmount != null">
        APPLY_AMOUNT = #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="billPeriodStart != null">
        BILL_PERIOD_START = #{billPeriodStart,jdbcType=BIGINT},
      </if>
      <if test="billPeriodEnd != null">
        BILL_PERIOD_END = #{billPeriodEnd,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD = #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="expectedMargin != null">
        EXPECTED_MARGIN = #{expectedMargin,jdbcType=DECIMAL},
      </if>
      <if test="applyReason != null">
        APPLY_REASON = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="billPeriodId != null">
        BILL_PERIOD_ID = #{billPeriodId,jdbcType=BIGINT},
      </if>
      <if test="beforeApplyAmount != null">
        BEFORE_APPLY_AMOUNT = #{beforeApplyAmount,jdbcType=DECIMAL},
      </if>
      <if test="beforeBillPeriodStart != null">
        BEFORE_BILL_PERIOD_START = #{beforeBillPeriodStart,jdbcType=BIGINT},
      </if>
      <if test="beforeBillPeriodEnd != null">
        BEFORE_BILL_PERIOD_END = #{beforeBillPeriodEnd,jdbcType=BIGINT},
      </if>
      <if test="beforeSettlementPeriod != null">
        BEFORE_SETTLEMENT_PERIOD = #{beforeSettlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
    </set>
    where BILL_PERIOD_APPLY_ID = #{billPeriodApplyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply">
    update T_CUSTOMER_BILL_PERIOD_APPLY
    set CUSTOMER_ID = #{customerId,jdbcType=BIGINT},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      BILL_PERIOD_TYPE = #{billPeriodType,jdbcType=TINYINT},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      RELATED_ORDER_ID = #{relatedOrderId,jdbcType=INTEGER},
      APPLY_AMOUNT = #{applyAmount,jdbcType=DECIMAL},
      BILL_PERIOD_START = #{billPeriodStart,jdbcType=BIGINT},
      BILL_PERIOD_END = #{billPeriodEnd,jdbcType=BIGINT},
      SETTLEMENT_PERIOD = #{settlementPeriod,jdbcType=INTEGER},
      EXPECTED_MARGIN = #{expectedMargin,jdbcType=DECIMAL},
      APPLY_REASON = #{applyReason,jdbcType=VARCHAR},
      BILL_PERIOD_ID = #{billPeriodId,jdbcType=BIGINT},
      BEFORE_APPLY_AMOUNT = #{beforeApplyAmount,jdbcType=DECIMAL},
      BEFORE_BILL_PERIOD_START = #{beforeBillPeriodStart,jdbcType=BIGINT},
      BEFORE_BILL_PERIOD_END = #{beforeBillPeriodEnd,jdbcType=BIGINT},
      BEFORE_SETTLEMENT_PERIOD = #{beforeSettlementPeriod,jdbcType=INTEGER},
      CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT}
    where BILL_PERIOD_APPLY_ID = #{billPeriodApplyId,jdbcType=BIGINT}
  </update>
  <select id="getApplyAuditByCustomerId" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply">
    SELECT *
    FROM T_CUSTOMER_BILL_PERIOD_APPLY WHERE COMPANY_ID = #{companyId,jdbcType=TINYINT} AND CUSTOMER_ID = #{customerId,jdbcType=INTEGER}
    AND CHECK_STATUS = #{status,jdbcType=TINYINT}
  </select>
  <select id="getApplyDetailsDtoList" resultType="com.vedeng.customerbillperiod.dto.CustomerBillPeriodApplyDetailsDto">
    select a.*, t.TRADER_NAME as CUSTOMER_NAME, tc.CUSTOMER_NATURE, tc.CUSTOMER_TYPE
    from T_TRADER t
    join T_TRADER_CUSTOMER tc on t.TRADER_ID = tc.TRADER_ID
    left join T_CUSTOMER_BILL_PERIOD_APPLY a on tc.TRADER_CUSTOMER_ID = a.CUSTOMER_ID and a.COMPANY_ID = 1
    where t.COMPANY_ID = 1
    <if test="keywordOfCustomerName != null">
      and t.TRADER_NAME like CONCAT('%',#{queryDto.keywordOfCustomerName},'%' )
    </if>
    <if test="customerNature != null">
      and tc.CUSTOMER_NATURE = #{queryDto.customerNature}
    </if>
    <if test="checkStatus != null">
      and a.CHECK_STATUS = #{queryDto.checkStatus}
    </if>
    <if test="creators != null and creators.size > 0">
      and a.CREATOR in
      <foreach collection="queryDto.creators" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="applyTimeStart != null">
      and a.ADD_TIME &gt; #{queryDto.applyTimeStart}
    </if>
    <if test="applyTimeStart != null">
      and a.ADD_TIME &lt; #{queryDto.applyTimeEnd}
    </if>
    <if test="billPeriodType != null">
      and a.BILL_PERIOD_TYPE = #{queryDto.billPeriodType}
    </if>
  </select>

  <select id="getApplyInfoByBillPeriodId" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodApply">
    select * from T_CUSTOMER_BILL_PERIOD_APPLY where BILL_PERIOD_ID in
    <foreach collection="billPeriodIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>