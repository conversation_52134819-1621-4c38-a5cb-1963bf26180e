<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastTargetMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="TARGET_TYPE" jdbcType="TINYINT" property="targetType"/>
        <result column="TARGET_BUZ_ID" jdbcType="INTEGER" property="targetBuzId"/>
        <result column="TARGET_AMOUNT" jdbcType="DECIMAL" property="targetAmount"/>
        <result column="TARGET_MONTH" jdbcType="TINYINT" property="targetMonth"/>
        <result column="TARGET_YEAR" jdbcType="INTEGER" property="targetYear"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, TARGET_TYPE, TARGET_BUZ_ID, TARGET_AMOUNT, TARGET_MONTH, TARGET_YEAR, IS_DELETED,
        ADD_TIME, MOD_TIME, CREATOR, UPDATER
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_TARGET
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_TARGET
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_TARGET (TARGET_TYPE, TARGET_BUZ_ID, TARGET_AMOUNT,
        TARGET_MONTH, TARGET_YEAR, IS_DELETED,
        ADD_TIME, MOD_TIME, CREATOR,
        UPDATER)
        values (#{targetType,jdbcType=TINYINT}, #{targetBuzId,jdbcType=INTEGER}, #{targetAmount,jdbcType=DECIMAL},
        #{targetMonth,jdbcType=TINYINT}, #{targetYear,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT},
        #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
        #{updater,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_TARGET
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="targetType != null">
                TARGET_TYPE,
            </if>
            <if test="targetBuzId != null">
                TARGET_BUZ_ID,
            </if>
            <if test="targetAmount != null">
                TARGET_AMOUNT,
            </if>
            <if test="targetMonth != null">
                TARGET_MONTH,
            </if>
            <if test="targetYear != null">
                TARGET_YEAR,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="targetType != null">
                #{targetType,jdbcType=TINYINT},
            </if>
            <if test="targetBuzId != null">
                #{targetBuzId,jdbcType=INTEGER},
            </if>
            <if test="targetAmount != null">
                #{targetAmount,jdbcType=DECIMAL},
            </if>
            <if test="targetMonth != null">
                #{targetMonth,jdbcType=TINYINT},
            </if>
            <if test="targetYear != null">
                #{targetYear,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity">
        update T_BROADCAST_TARGET
        <set>
            <if test="targetType != null">
                TARGET_TYPE = #{targetType,jdbcType=TINYINT},
            </if>
            <if test="targetBuzId != null">
                TARGET_BUZ_ID = #{targetBuzId,jdbcType=INTEGER},
            </if>
            <if test="targetAmount != null">
                TARGET_AMOUNT = #{targetAmount,jdbcType=DECIMAL},
            </if>
            <if test="targetMonth != null">
                TARGET_MONTH = #{targetMonth,jdbcType=TINYINT},
            </if>
            <if test="targetYear != null">
                TARGET_YEAR = #{targetYear,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity">
        update T_BROADCAST_TARGET
        set TARGET_TYPE = #{targetType,jdbcType=TINYINT},
        TARGET_BUZ_ID = #{targetBuzId,jdbcType=INTEGER},
        TARGET_AMOUNT = #{targetAmount,jdbcType=DECIMAL},
        TARGET_MONTH = #{targetMonth,jdbcType=TINYINT},
        TARGET_YEAR = #{targetYear,jdbcType=INTEGER},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <!-- ========== 自定义查询方法 ========== -->

    <!-- 批量插入目标记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into T_BROADCAST_TARGET (TARGET_TYPE, TARGET_BUZ_ID, TARGET_AMOUNT,
        TARGET_MONTH, TARGET_YEAR, IS_DELETED,
        ADD_TIME, MOD_TIME, CREATOR, UPDATER)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.targetType,jdbcType=TINYINT}, #{record.targetBuzId,jdbcType=INTEGER}, #{record.targetAmount,jdbcType=DECIMAL},
            #{record.targetMonth,jdbcType=TINYINT}, #{record.targetYear,jdbcType=INTEGER}, #{record.isDeleted,jdbcType=TINYINT},
            #{record.addTime,jdbcType=TIMESTAMP}, #{record.modTime,jdbcType=TIMESTAMP}, #{record.creator,jdbcType=INTEGER},
            #{record.updater,jdbcType=INTEGER})
        </foreach>
    </insert>

    <!-- 根据年度和目标类型删除记录（覆盖式导入使用） -->
    <update id="deleteByYearAndType">
        update T_BROADCAST_TARGET
        set IS_DELETED = 1,
        MOD_TIME = NOW(),
        UPDATER = #{updater,jdbcType=INTEGER}
        where TARGET_YEAR = #{targetYear,jdbcType=INTEGER}
        and TARGET_TYPE = #{targetType,jdbcType=TINYINT}
        and IS_DELETED = 0
    </update>

    <!-- 查询目标列表（带目标对象名称） -->
    <select id="selectTargetsWithName" resultType="com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity">
        select
        t.ID as id,
        t.TARGET_TYPE as targetType,
        t.TARGET_BUZ_ID as targetBuzId,
        t.TARGET_AMOUNT as targetAmount,
        t.TARGET_MONTH as targetMonth,
        t.TARGET_YEAR as targetYear,
        t.IS_DELETED as isDeleted,
        t.ADD_TIME as addTime,
        t.MOD_TIME as modTime,
        t.CREATOR as creator,
        t.UPDATER as updater,
        case
        when t.TARGET_TYPE = 1 then u.USERNAME
        when t.TARGET_TYPE = 2 then g.DEPT_NAME
        when t.TARGET_TYPE = 3 then d.DEPT_NAME
        end as targetName
        from T_BROADCAST_TARGET t
        left join T_USER u on t.TARGET_TYPE = 1 and t.TARGET_BUZ_ID = u.USER_ID
        left join T_BROADCAST_DEPT g on t.TARGET_TYPE = 2 and t.TARGET_BUZ_ID = g.ID and g.IS_DELETED = 0
        left join T_BROADCAST_DEPT d on t.TARGET_TYPE = 3 and t.TARGET_BUZ_ID = d.ID and d.IS_DELETED = 0
        where t.IS_DELETED = 0
        and t.TARGET_YEAR = #{targetYear,jdbcType=INTEGER}
        <if test="targetType != null">
            and t.TARGET_TYPE = #{targetType,jdbcType=TINYINT}
        </if>
        <if test="targetName != null and targetName != ''">
            and (
            (t.TARGET_TYPE = 1 and u.USERNAME like concat('%', #{targetName,jdbcType=VARCHAR}, '%'))
            or (t.TARGET_TYPE = 2 and g.DEPT_NAME like concat('%', #{targetName,jdbcType=VARCHAR}, '%'))
            or (t.TARGET_TYPE = 3 and d.DEPT_NAME like concat('%', #{targetName,jdbcType=VARCHAR}, '%'))
            )
        </if>
        order by t.TARGET_TYPE, t.TARGET_BUZ_ID, t.TARGET_MONTH
    </select>

    <!-- 查询有数据的年度列表 -->
    <select id="selectAvailableYears" resultType="java.lang.Integer">
        select distinct TARGET_YEAR
        from T_BROADCAST_TARGET
        where IS_DELETED = 0
        order by TARGET_YEAR desc
    </select>
    
    <select id="selectBroadcastTargeByParams" resultType="com.vedeng.erp.broadcast.domain.entity.BroadcastTargetEntity">
    	select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_TARGET
        where IS_DELETED = 0
        <if test="year != null">
            and TARGET_YEAR = #{year,jdbcType=INTEGER}
        </if>
        <if test="month != null">
        	and TARGET_MONTH = #{month,jdbcType=TINYINT}
        </if>
        <if test="targetType != null">
        	and TARGET_TYPE = #{targetType,jdbcType=TINYINT}
        </if>
        <if test="targetBuzIdList != null and targetBuzIdList.size() > 0">
			and TARGET_BUZ_ID  in 
			<foreach collection="targetBuzIdList" item="targetBuzId" open="(" close=")" separator=",">
				#{targetBuzId,jdbcType=INTEGER}
			</foreach>
		</if>
		
	</select>
</mapper>