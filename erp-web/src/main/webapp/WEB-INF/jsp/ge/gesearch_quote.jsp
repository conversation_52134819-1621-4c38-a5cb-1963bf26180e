<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="搜索报价单" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/ge/gesearch_quote.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/newIndex.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="form-list  form-tips12">
    <div class="parts">
        <ul style="margin-top: 10px">
            <li>
                <div class="form-tips">
                    <label>报价单号：</label>
                </div>
                <form method="post" id="search" action="${pageContext.request.contextPath}/businesschance/ge/gesearchquote.do">
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" style="width: 300px" name="quoteorderNo" id="vdquoteorderNo" value="${quoteInfoDto.quoteorderNo}" autocomplete="off" placeholder="请输入报价单号">
                            <input type="hidden" value="${quoteInfoDto.quoteorderId}" id="vdquoteorderId">
                            <span class="bt-bg-style bt-small bg-light-blue" onclick="gesearch()">搜索</span>
                        </div>
                    </div>
                </form>
            </li>
        </ul>
        <span style="color: red;margin-left: 100px">${msg}</span>
        <input type="hidden" id="isCanCreate" value="${empty quoteInfoDto ? "0" : "1"}">
    </div>
    <c:if test="${showType ne 1}">
        <div class="parts" style="margin-top: 10px">
        <table style="margin-left: 100px;border-collapse:separate;border-spacing:0px 15px;">
            <tr>
                <td>终端医院名称：</td>
                <td><input type="text" name="terminalTraderName" id="vdterminalTraderName" style="border: 0px;width: 200px" readonly
                           value="${quoteInfoDto.customerNature eq 466 ? quoteInfoDto.traderName : quoteInfoDto.terminalTraderName}">
                    <input type="hidden" name="terminalTraderId" id="vdterminalTraderId" value="${quoteInfoDto.customerNature eq 466 ? quoteInfoDto.traderId : quoteInfoDto.terminalTraderId}">
                </td>
            </tr>
            <tr>
                <td>所属地区：</td>
                <td>
                    <input type="text" name="salesArea" id="vdsalesArea" style="border: 0px;width: 400px" readonly
                           value="${quoteInfoDto.customerNature eq 466 ? quoteInfoDto.area : quoteInfoDto.salesArea}">
                    <input type="hidden" name="salesAreaId" id="vdsalesAreaId" value="${quoteInfoDto.salesAreaId}">
                </td>
            </tr>
            <tr>
                <td>经销商名称：</td>
                <td>
                    <input type="text" name="traderName" id="vdtraderName" style="border: 0px;width: 200px" readonly
                           value="${quoteInfoDto.customerNature eq 466 ? '' : quoteInfoDto.traderName}">
                    <input type="hidden" name="traderId" id="vdtraderId" value="${quoteInfoDto.customerNature eq 466 ? '' : quoteInfoDto.traderId}">
                </td>
            </tr>
            <tr>
                <td>意向型号：</td>
                <td>
                    <c:forEach items="${quoteInfoDto.quoteGoodsInfoDtos}" var="quoteGood" varStatus="num">
                        <input type="radio" name="goodsnum" value="${num.count}" ${num.count eq 1 ? 'checked' : ''}>
                        <input type="text" id="goodsName_${num.count}" style="border: 0px;width: 500px" value="${quoteGood.goodsName}" readonly>
                        <br>
                        <input type="hidden" id="sku_${num.count}" value="${quoteGood.sku}">
                        <input type="hidden" id="goodsId_${num.count}" value="${quoteGood.goodsId}">
                    </c:forEach>
                </td>
            </tr>
        </table>
        <div style="text-align: center">
            <span class="bt-bg-style bt-small bg-light-grey" onclick="closeLayer()">取消</span>
            <span class="bt-bg-style bt-small bg-light-blue" onclick="confirmSelect()">确定</span>
        </div>
    </div>
    </c:if>
</div>
</body>
