package com.vedeng.goods.service.impl;

import com.vedeng.goods.dto.UnitKingDee;
import com.vedeng.goods.mapper.UnitKingDeeExtMapper;
import com.vedeng.goods.service.UnitKingDeeApiService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 金蝶单位
 * @author: yana.jiang
 * @date: 2022/11/30
 */
@Service
public class UnitKingDeeApiServiceImpl implements UnitKingDeeApiService {
    @Resource
    private UnitKingDeeExtMapper unitKingDeeExtMapper;
    @Override
    public List<UnitKingDee> getAllUnitKingDeeList(String name) {
        return unitKingDeeExtMapper.getAllUnitKingDeeList(name);
    }

    @Override
    public void addUnitKingDee(List<String> nameList, List<String> noList, Integer userId, String realName) {
        Map<String, UnitKingDee> unitMap = null;
        List<UnitKingDee> allList = getAllUnitKingDeeList("");
        if(!CollectionUtils.isEmpty(allList)){
            unitMap = allList.stream().collect(Collectors.toMap(UnitKingDee::getUnitKingDeeNo, Function.identity(), (x, y) -> y));
        }
        if(!CollectionUtils.isEmpty(nameList) && !CollectionUtils.isEmpty(noList)){
            List<UnitKingDee> list = new ArrayList<UnitKingDee>();
            for(int i = 0; i <nameList.size();i++){
                UnitKingDee unitKingDee = new UnitKingDee();
                unitKingDee.setUnitKingDeeName(nameList.get(i));
                unitKingDee.setUnitKingDeeNo(noList.get(i));
                unitKingDee.setAddTime(new Date());
                unitKingDee.setModTime(new Date());
                unitKingDee.setCreator(userId);
                unitKingDee.setCreatorName(realName);
                unitKingDee.setUpdater(userId);
                unitKingDee.setUpdaterName(realName);
                unitKingDee.setIsDelete(0);
                unitKingDee.setRemark("");
                unitKingDee.setUpdateRemark("");
                if(!CollectionUtils.isEmpty(unitMap) && unitMap.containsKey(noList.get(i))){
                    continue;
                }
                list.add(unitKingDee);
            }
            if(!CollectionUtils.isEmpty(list)){
                unitKingDeeExtMapper.batchInsert(list);
            }
        }
    }
}
