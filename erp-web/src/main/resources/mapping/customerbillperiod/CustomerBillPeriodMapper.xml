<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.customerbillperiod.dao.CustomerBillPeriodMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    <id column="BILL_PERIOD_ID" jdbcType="BIGINT" property="billPeriodId" />
    <result column="CUSTOMER_ID" jdbcType="BIGINT" property="customerId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="BILL_PERIOD_TYPE" jdbcType="TINYINT" property="billPeriodType" />
    <result column="RELATED_ORDER_ID" jdbcType="INTEGER" property="relatedOrderId" />
    <result column="APPLY_AMOUNT" jdbcType="DECIMAL" property="applyAmount" />
    <result column="BILL_PERIOD_START" jdbcType="BIGINT" property="billPeriodStart" />
    <result column="BILL_PERIOD_END" jdbcType="BIGINT" property="billPeriodEnd" />
    <result column="SETTLEMENT_PERIOD" jdbcType="INTEGER" property="settlementPeriod" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>

  <resultMap id="AvailableBillPeriodAmountMap"
             type="com.vedeng.customerbillperiod.dto.BillPeriodItem">

  </resultMap>
  <sql id="Base_Column_List">
    BILL_PERIOD_ID, CUSTOMER_ID, COMPANY_ID, BILL_PERIOD_TYPE, RELATED_ORDER_ID, APPLY_AMOUNT, 
    BILL_PERIOD_START, BILL_PERIOD_END, SETTLEMENT_PERIOD, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_CUSTOMER_BILL_PERIOD
    where BILL_PERIOD_ID = #{billPeriodId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_CUSTOMER_BILL_PERIOD
    where BILL_PERIOD_ID = #{billPeriodId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BILL_PERIOD_ID" keyProperty="billPeriodId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriod" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD (CUSTOMER_ID, COMPANY_ID, BILL_PERIOD_TYPE,
                                        RELATED_ORDER_ID, APPLY_AMOUNT, BILL_PERIOD_START,
                                        BILL_PERIOD_END, SETTLEMENT_PERIOD, ADD_TIME,
                                        CREATOR, MOD_TIME, UPDATER
    )
    values (#{customerId,jdbcType=BIGINT}, #{companyId,jdbcType=INTEGER}, #{billPeriodType,jdbcType=TINYINT},
            #{relatedOrderId,jdbcType=INTEGER}, #{applyAmount,jdbcType=DECIMAL}, #{billPeriodStart,jdbcType=BIGINT},
            #{billPeriodEnd,jdbcType=BIGINT}, #{settlementPeriod,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT},
            #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
           )
  </insert>
  <insert id="insertSelective" keyColumn="BILL_PERIOD_ID" keyProperty="billPeriodId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriod" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="billPeriodType != null">
        BILL_PERIOD_TYPE,
      </if>
      <if test="relatedOrderId != null">
        RELATED_ORDER_ID,
      </if>
      <if test="applyAmount != null">
        APPLY_AMOUNT,
      </if>
      <if test="billPeriodStart != null">
        BILL_PERIOD_START,
      </if>
      <if test="billPeriodEnd != null">
        BILL_PERIOD_END,
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="billPeriodType != null">
        #{billPeriodType,jdbcType=TINYINT},
      </if>
      <if test="relatedOrderId != null">
        #{relatedOrderId,jdbcType=INTEGER},
      </if>
      <if test="applyAmount != null">
        #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="billPeriodStart != null">
        #{billPeriodStart,jdbcType=BIGINT},
      </if>
      <if test="billPeriodEnd != null">
        #{billPeriodEnd,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    update T_CUSTOMER_BILL_PERIOD
    <set>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="billPeriodType != null">
        BILL_PERIOD_TYPE = #{billPeriodType,jdbcType=TINYINT},
      </if>
      <if test="relatedOrderId != null">
        RELATED_ORDER_ID = #{relatedOrderId,jdbcType=INTEGER},
      </if>
      <if test="applyAmount != null">
        APPLY_AMOUNT = #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="billPeriodStart != null">
        BILL_PERIOD_START = #{billPeriodStart,jdbcType=BIGINT},
      </if>
      <if test="billPeriodEnd != null">
        BILL_PERIOD_END = #{billPeriodEnd,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD = #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where BILL_PERIOD_ID = #{billPeriodId,jdbcType=BIGINT}
  </update>

  <select id="getCustomerBillPeriodListByCustomerId" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    select *
    from T_CUSTOMER_BILL_PERIOD where COMPANY_ID = #{companyId} and CUSTOMER_ID = #{customerId}
  </select>
  <select id="getCustomerBillPeriodListByCustomerIdAndRelatedOrderId" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    select *
    from T_CUSTOMER_BILL_PERIOD where COMPANY_ID = #{companyId} and CUSTOMER_ID = #{customerId} and BILL_PERIOD_TYPE = 3 and RELATED_ORDER_ID =
                                                                                                                           #{relatedOrderId}
  </select>
  <select id="getCustomerBillPeriodListByCreator" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    select *
    from T_CUSTOMER_BILL_PERIOD where COMPANY_ID = #{companyId} and CREATOR in
    <foreach collection="creators" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    <if test="startTime != null">
      and ifnull(MOD_TIME,ADD_TIME) &gt; #{startTime}
    </if>
    <if test="endTime != null">
      and ifnull(MOD_TIME,ADD_TIME) &lt; #{endTime}
    </if>
  </select>
  <select id="getCustomerIdListByCreator" resultType="java.lang.Long">
    select distinct CUSTOMER_ID
    from T_CUSTOMER_BILL_PERIOD where COMPANY_ID = #{companyId} and CREATOR in
    <foreach collection="creators" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="getCustomerBillPeriodListByCustomerIdAndType" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    select *
    from T_CUSTOMER_BILL_PERIOD where COMPANY_ID = #{companyId} and CUSTOMER_ID = #{customerId} and BILL_PERIOD_TYPE = #{type}
  </select>
  <select id="getAvailableAmountGroupByType"
          resultType="com.vedeng.customerbillperiod.dto.BillPeriodItem">
    select p.APPLY_AMOUNT AMOUNT, (p.APPLY_AMOUNT - ifnull(sum(ud.AMOUNT),0)) AVAILABLE_AMOUNT, p.BILL_PERIOD_TYPE,p.SETTLEMENT_PERIOD
    from T_CUSTOMER_BILL_PERIOD p
    left join T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud
    on p.BILL_PERIOD_ID = ud.BILL_PERIOD_ID
    where p.COMPANY_ID = #{companyId}
    and p.CUSTOMER_ID = #{customerId}
    and (
    (p.BILL_PERIOD_TYPE in (1, 2) and p.BILL_PERIOD_START &lt; #{currentTime} and p.BILL_PERIOD_END &gt; #{currentTime})
    <if test="orderId != null">
      or (p.BILL_PERIOD_TYPE = 3 and p.RELATED_ORDER_ID = #{orderId})
    </if>
    )
    group by p.BILL_PERIOD_ID
  </select>
  <select id="getAvailableAmountGroupByBillPeriod" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    select p.BILL_PERIOD_ID, (p.APPLY_AMOUNT - ifnull(sum(ud.AMOUNT),0)) APPLY_AMOUNT, p.BILL_PERIOD_TYPE, p.SETTLEMENT_PERIOD
    from T_CUSTOMER_BILL_PERIOD p
    left join T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud
    on p.BILL_PERIOD_ID = ud.BILL_PERIOD_ID and p.COMPANY_ID = #{companyId} and p.CUSTOMER_ID = #{customerId}
    where p.COMPANY_ID = #{companyId}
    and p.CUSTOMER_ID = #{customerId}
    and (
    p.BILL_PERIOD_TYPE in (1, 2)
    <if test="orderId != null">
      or (p.BILL_PERIOD_TYPE = 3 and p.RELATED_ORDER_ID = #{orderId})
    </if>
    )
    and p.BILL_PERIOD_START &lt; #{currentTime} and p.BILL_PERIOD_END &gt; #{currentTime}
    group by p.BILL_PERIOD_ID
  </select>
  <select id="batchGetCustomerBillPeriodById" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    select *
    from T_CUSTOMER_BILL_PERIOD where BILL_PERIOD_ID in
    <foreach collection="idList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <select id="getValidCustomerBillPeriodByType" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    select *
    from T_CUSTOMER_BILL_PERIOD
    where COMPANY_ID = #{companyId}
    and CUSTOMER_ID = #{customerId}
    and BILL_PERIOD_TYPE = #{type}
    and BILL_PERIOD_END &gt; #{currentTime}
  </select>
  <select id="getAvailableAmountByBillPeriodList" resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriod">
    select p.BILL_PERIOD_ID, (p.APPLY_AMOUNT - ifnull(sum(ud.AMOUNT), 0)) APPLY_AMOUNT
    from T_CUSTOMER_BILL_PERIOD p
           left join T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud on p.BILL_PERIOD_ID = ud.BILL_PERIOD_ID
    where p.BILL_PERIOD_ID in
    <foreach collection="billPeriodIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
    AND p.BILL_PERIOD_START &lt; #{currentTime} and p.BILL_PERIOD_END &gt; #{currentTime}
    GROUP BY p.BILL_PERIOD_ID
  </select>


</mapper>