package com.newtask.data.trader;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.TraderDateMapper;
import com.vedeng.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.Inet4Address;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 更新 交易SKU数 品牌覆盖数
 */
@Component
public class TraderSkuAndBrandNumSync extends AbstractDataSync {
    @Resource
    private TraderDateMapper traderDateMapper;

    @Override
    public void process(String startTime) {
        super.process(startTime);
    }

    /**
     * 查询某一时间段需要更新的客户
     * @param startTime
     * @return
     */
    @Override
    public List<Integer> loadBizId(Long startTime,Long endTime) {
        List<Integer> list=   traderDateMapper.getSkuAndBrandNumTraderId(
                DateUtil.convertString(startTime,null),DateUtil.convertString(endTime,null)
        ,startTime,endTime);
        return list;
    }

    /**
     * 根据 traderId查询 交易SKU数 品牌覆盖数
     * @param bizIds
     * @return
     */
    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        // 根据 traderId查 交易SKU数 品牌覆盖数
        if(CollectionUtils.isNotEmpty(bizIds)){
            List<Map<String, Object>> list = traderDateMapper.findSkuNumAndBrandNum(bizIds);
            return list;
        }
        return null;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if(CollectionUtils.isNotEmpty(dataList)){
            dataList.forEach(item->{
                Long nowDate = new Date().getTime();
                Integer traderId= NumberUtils.toInt(item.get("traderId")+"");
                //交易sku数量
                Integer orderSkuCount = NumberUtils.toInt(item.get("orderSkuCount")+"");
                //品牌覆盖数
                Integer orderBrandCount = NumberUtils.toInt(item.get("orderBrandCount")+"");

                makeExist(traderId,nowDate);
                traderDateMapper.updateSkuNumAndBrandNum(traderId,orderSkuCount,orderBrandCount,nowDate);
            });
        }
    }

    @Override
    public void makeExist(Integer id,Long nowDate) {
        Long count = traderDateMapper.makeExist(id);
        if (count <= 0) {
            //往宽表里添加数据
            traderDateMapper.insertTraderId(id,nowDate);
        }

    }
}
