package com.vedeng.erp.settlement.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import com.vedeng.erp.settlement.domain.entity.SettlementBillEntity;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface SettlementBillConvertor extends BaseMapStruct<SettlementBillEntity, SettlementBillDto> {
}
