package com.vedeng.mobile.system.model;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/19
 */
/**
 * 拜访记录表
 */
public class VisitRecord {
    /**
     * 序号
     */
    private Integer id;

    /**
     * 计划拜访时间
     */
    private Date planVisitDate;

    /**
     * 拜访人
     */
    private Integer visitorId;

    /**
     * 拜访目标 (A新客开发B商机跟进C老客客情维护D签约会员E产品推广，以逗号隔开)
     */
    private String visitTarget;

    /**
     * 客户所在地区-省CODE
     */
    private Integer provinceCode;

    /**
     * 客户所在地区-省名称
     */
    private String provinceName;

    /**
     * 客户所在地区-市CODE
     */
    private Integer cityCode;

    /**
     * 客户所在地区-市名称
     */
    private String cityName;

    /**
     * 客户所在地区-区CODE-20240407 ERP_SV_2024_17版本 拜访计划优化需求新增
     */
    private Integer areaCode;

    /**
     * 客户所在地区-区名称-20240407 ERP_SV_2024_17版本 拜访计划优化需求新增
     */
    private String areaName;

    /**
     * 部门ID，暂时没存值-默认一律为0
     */
    private Integer orgId;

    /**
     *  '部门分组名称（GROUP1 6大区，GROUP2 应急3部门）',
     */
    private String orgGroup;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户来源方式 (1erp2终端库3天眼查)
     */
    private Integer customerFrom;

    /**
     * 客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端)
     */
    private Integer customerNature;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 实际拜访时间
     */
    private Date actualVisitDate;

    /**
     * 是否打卡 (Y是N否)
     */
    private String cardOff;

    /**
     * 打卡照片
     */
    private String pictureList;

    /**
     * 拜访内容-联系人信息-姓名
     */
    private String contactName;

    /**
     * 拜访内容-联系人信息-手机
     */
    private String contactMobile;

    /**
     * 拜访内容-联系人信息-电话
     */
    private String contactTele;

    /**
     * 拜访内容-联系人信息-职位
     */
    private String contactPosition;

    /**
     * 沟通事项-讲解PPT (默认否，Y是N否)
     */
    private String showPpt;

    /**
     * 沟通事项-邀请客户注册 (默认否，Y是N否)
     */
    private String inviteReg;

    /**
     * 沟通事项-邀请客户注册-手机号
     */
    private String regMobile;

    /**
     * 沟通事项-邀请客户注册-联系人ID
     */
    private Integer traderContractId;

    /**
     * 沟通事项-沟通情况
     */
    private String commucateContent;

    /**
     * 沟通事项-预计下次拜访时间
     */
    private Date nextVisitDate;

    /**
     * 沟通事项-是否产生商机 (Y是N否)
     */
    private String createBusinessChange;

    /**
     * 拜访结果，拜访成功Y拜访事项缺失N
     */
    private String visitSuccess;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 添加人
     */
    private Integer addUserId;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 更新人
     */
    private Integer modUserId;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 商机表ID
     */
    private Integer bussinessChanceId;

    /**
     * 商机编号
     */
    private String bussinessChanceNo;

    /**
     * 拜访人用户名
     */
    private String visitorName;

    /**
     * 打卡时间
     */
    private String cardTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getPlanVisitDate() {
        return planVisitDate;
    }

    public void setPlanVisitDate(Date planVisitDate) {
        this.planVisitDate = planVisitDate;
    }

    public Integer getVisitorId() {
        return visitorId;
    }

    public void setVisitorId(Integer visitorId) {
        this.visitorId = visitorId;
    }

    public String getVisitTarget() {
        return visitTarget;
    }

    public void setVisitTarget(String visitTarget) {
        this.visitTarget = visitTarget;
    }

    public Integer getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(Integer provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCityCode() {
        return cityCode;
    }

    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCustomerFrom() {
        return customerFrom;
    }

    public void setCustomerFrom(Integer customerFrom) {
        this.customerFrom = customerFrom;
    }

    public Integer getCustomerNature() {
        return customerNature;
    }

    public void setCustomerNature(Integer customerNature) {
        this.customerNature = customerNature;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getTraderCustomerId() {
        return traderCustomerId;
    }

    public void setTraderCustomerId(Integer traderCustomerId) {
        this.traderCustomerId = traderCustomerId;
    }

    public Date getActualVisitDate() {
        return actualVisitDate;
    }

    public void setActualVisitDate(Date actualVisitDate) {
        this.actualVisitDate = actualVisitDate;
    }

    public String getCardOff() {
        return cardOff;
    }

    public void setCardOff(String cardOff) {
        this.cardOff = cardOff;
    }

    public String getPictureList() {
        return pictureList;
    }

    public void setPictureList(String pictureList) {
        this.pictureList = pictureList;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getContactTele() {
        return contactTele;
    }

    public void setContactTele(String contactTele) {
        this.contactTele = contactTele;
    }

    public String getContactPosition() {
        return contactPosition;
    }

    public void setContactPosition(String contactPosition) {
        this.contactPosition = contactPosition;
    }

    public String getShowPpt() {
        return showPpt;
    }

    public void setShowPpt(String showPpt) {
        this.showPpt = showPpt;
    }

    public String getInviteReg() {
        return inviteReg;
    }

    public void setInviteReg(String inviteReg) {
        this.inviteReg = inviteReg;
    }

    public String getRegMobile() {
        return regMobile;
    }

    public void setRegMobile(String regMobile) {
        this.regMobile = regMobile;
    }

    public Integer getTraderContractId() {
        return traderContractId;
    }

    public void setTraderContractId(Integer traderContractId) {
        this.traderContractId = traderContractId;
    }

    public String getCommucateContent() {
        return commucateContent;
    }

    public void setCommucateContent(String commucateContent) {
        this.commucateContent = commucateContent;
    }

    public Date getNextVisitDate() {
        return nextVisitDate;
    }

    public void setNextVisitDate(Date nextVisitDate) {
        this.nextVisitDate = nextVisitDate;
    }

    public String getCreateBusinessChange() {
        return createBusinessChange;
    }

    public void setCreateBusinessChange(String createBusinessChange) {
        this.createBusinessChange = createBusinessChange;
    }

    public String getVisitSuccess() {
        return visitSuccess;
    }

    public void setVisitSuccess(String visitSuccess) {
        this.visitSuccess = visitSuccess;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getAddUserId() {
        return addUserId;
    }

    public void setAddUserId(Integer addUserId) {
        this.addUserId = addUserId;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getModUserId() {
        return modUserId;
    }

    public void setModUserId(Integer modUserId) {
        this.modUserId = modUserId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getBussinessChanceId() {
        return bussinessChanceId;
    }

    public void setBussinessChanceId(Integer bussinessChanceId) {
        this.bussinessChanceId = bussinessChanceId;
    }

    public String getBussinessChanceNo() {
        return bussinessChanceNo;
    }

    public void setBussinessChanceNo(String bussinessChanceNo) {
        this.bussinessChanceNo = bussinessChanceNo;
    }

    public String getVisitorName() {
        return visitorName;
    }

    public void setVisitorName(String visitorName) {
        this.visitorName = visitorName;
    }

    public String getCardTime() {
        return cardTime;
    }

    public void setCardTime(String cardTime) {
        this.cardTime = cardTime;
    }

    public Integer getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(Integer areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgGroup() {
        return orgGroup;
    }

    public void setOrgGroup(String orgGroup) {
        this.orgGroup = orgGroup;
    }
}