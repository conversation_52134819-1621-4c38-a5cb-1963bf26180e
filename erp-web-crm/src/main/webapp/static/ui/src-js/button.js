Vue.component('ui-button', {
    template: `<div class="vd-ui-button"
        @click="handleClick"
        :disabled="buttonDisabled"
        :class="[
            type ? 'vd-ui-button--' + type : '',
            buttonSize ? 'vd-ui-button--' + buttonSize : '',
            {
                'is-disabled': buttonDisabled,
                'is-loading': loading,
            }
        ]"
    >
        <i 
            ref="loading" 
            class="vd-ui_icon icon-loading"
            :class="{'loading':loading}"
            v-if="loading"
        ></i>
        <i class="vd-ui_icon"
            :class="icon"
            v-if="icon && !loading"
        ></i>
        <span v-if="$slots.default">
            <slot></slot>
        </span>
    </div>`,
    props: {
        // 按钮类型
        type: {
            type: String,
            default: ''
        },
        // 按钮尺寸 large small
        size: String,
        icon: {
            type: String,
            default: ''
        },
        loading: Boolean,
        disabled: Boolean,
    },
    computed: {
        buttonSize() {
            return this.size
        },
        buttonDisabled() {
            return this.disabled
        }
    },
    mounted(){
       
    },
    methods: {
        handleClick(event) {
            this.$emit('click', event)
        },
    }
})

Vue.component('ui-select-button', {
    template: `<div class="vd-ui-select-button" :class="{open: isShowMoreAddBtns}" v-if="type==='button'">
        <div class="ui-select-btn-txt">
            <slot></slot>
        </div>
        <div class="ui-select-btn-more" @click="showMore">
            <i class="vd-ui_icon icon-down"></i>
        </div>
        <div class="ui-select-btn-drop">
            <slot name="drop"><slot>
        </div>
    </div>
    <div class="vd-ui-select-link" :class="{open: isShowMoreAddBtns}" v-else>
        <div class="ui-select-link-trigger" @click="showMore">
            <div class="ui-link-txt">{{ placeholder }}</div>
            <i class="vd-ui_icon icon-down"></i>
        </div>
        <div class="ui-select-btn-drop">
            <slot><slot>
        </div>
    </div>`,
    props: {
       type: {
            type: String,
            default: 'button' //'button' or 'text'
       },
       placeholder: {
            type: String,
            default: '请选择'
       }
    },
    data() {
        return {
            isShowMoreAddBtns: false
        }
    },
    computed: {
        
    },
    mounted(){
        document.addEventListener('click', () => {
            setTimeout(() => {
                this.isShowMoreAddBtns = false;
            }, 50)
        })
    },
    methods: {
        handleClick(event) {
            this.$emit('click', event)
        },
        showMore() {
            setTimeout(() => {
                this.isShowMoreAddBtns = !this.isShowMoreAddBtns;
            }, 200)
        }
    }
})