package com.vedeng.mobile.system.service.Impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.Page;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.system.common.enums.UserOrgEnums;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.VisitDeleteDto;
import com.vedeng.erp.trader.dto.VisitInputDto;
import com.vedeng.erp.trader.dto.VisitSearchDto;
import com.vedeng.erp.trader.service.VisitRecordApiService;
import com.vedeng.mobile.service.SaleOrderMobileAggregateApiService;
import com.vedeng.mobile.system.dto.TraderCustomerInputDto;
import com.vedeng.mobile.system.dto.VisitCardInputDto;
import com.vedeng.mobile.system.dto.VisitConfigDto;
import com.vedeng.mobile.system.dto.VisitContentInputDto;
import com.vedeng.mobile.system.mapper.MobileBussinessChanceMapper;
import com.vedeng.mobile.system.mapper.MobileCommunicateRecordMapper;
import com.vedeng.mobile.system.mapper.TraderContactMapper;
import com.vedeng.mobile.system.mapper.TraderCustomerForMobileMapper;
import com.vedeng.mobile.system.mapper.VisitRecordLogMapper;
import com.vedeng.mobile.system.mapper.VisitRecordMapper;
import com.vedeng.mobile.system.model.BussinessChanceEntity;
import com.vedeng.mobile.system.model.CommunicateRecord;
import com.vedeng.mobile.system.model.TraderContact;
import com.vedeng.mobile.system.model.VisitRecord;
import com.vedeng.mobile.system.model.VisitRecordLog;
import com.vedeng.mobile.system.service.VisitRecordService;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
@Service
@Slf4j
public class VisitRecordServiceImpl implements VisitRecordService {


    @Autowired
    private VisitRecordMapper visitRecordMapper;

    @Autowired
    private VisitRecordLogMapper visitRecordLogMapper;

    @Autowired
    @Qualifier("traderCustomerForMobileMapper")
    private TraderCustomerForMobileMapper traderCustomerForMobileMapper;

    @Autowired
    private UserApiService userService;

    @Autowired
    private SaleOrderMobileAggregateApiService saleOrderMobileAggregateApiService;

    @Autowired
    private VisitRecordApiService visitRecordApiService;

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;
    
    @Value("${mobile.visitConfigList:[]}")
    private String visitConfigList;
    
 
    
    @Override
    public PageInfo<VisitInputDto> searchVisitRecordListPage(VisitSearchDto visitSearchDto, Page page) {
        if(StringUtils.isNotEmpty(visitSearchDto.getSearchFrom()) && "2".equals(visitSearchDto.getSearchFrom())){
            PageHelper.startPage(page.getPageNo(), page.getPageSize(),true,false,false);
            List<VisitInputDto> searchCustomerList = visitRecordMapper.searchVisitRecordListPageByCustomerName(visitSearchDto,page.getPageNo(),page.getPageSize());
            return new PageInfo<>(searchCustomerList);
        }

        visitSearchDto.setCustomerType(427);
        PageParam<VisitSearchDto> pageParam = new PageParam<>();
        pageParam.setPageNum(visitSearchDto.getPageNo());
        pageParam.setPageSize(visitSearchDto.getPageSize());
        pageParam.setParam(visitSearchDto);
        pageParam.setOrderBy(visitSearchDto.getOrderBy());
        if(StrUtil.isEmpty(pageParam.getOrderBy())){
            pageParam.setOrderBy("PLAN_VISIT_DATE DESC");
        }
        return visitRecordApiService.searchVisitRecordListPage(pageParam);
    }

    /**
     * 查询拜访计划
     * @param id
     * @return
     */
    @Override
    public VisitInputDto queryVisitRecordById(Integer id) {
        return visitRecordMapper.selectVisitInputDtoByPrimaryKey(id);
    }

    public VisitRecord findRecent180DaysPeriod(List<VisitRecord> tempList) {
        VisitRecord visitRecord = null;
        LocalDate lastDate = null;
        for (VisitRecord record : tempList) {
            LocalDate currentDate = record.getAddTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (lastDate != null && !currentDate.isBefore(lastDate.minusDays(180))) {
                return visitRecord;
//                return lastDate;
            }
            lastDate = currentDate;
            visitRecord = record;
        }
//        return lastDate;
        return  visitRecord;
    }

    @Override
    public VisitRecord queryLastVisitRecord(String customerName){
        List<VisitRecord> recordLast  =  visitRecordMapper.selectByCustomerNameForAreaCode(customerName);
        if(CollectionUtils.isEmpty(recordLast)){
            return null;
        }
        return recordLast.get(0);
    }



    @Override
    public VisitRecord checkTraderCanCreate(TraderCustomerInputDto inputDto){
        //先取最近的一次拜访计划
        List<VisitRecord> recordLast = visitRecordMapper.selectByCustomerName(inputDto.getTraderName());
        if(CollectionUtils.isEmpty(recordLast)){//没有，可以直接创建
            return null;
        }
        VisitRecord recordLastOne = recordLast.get(0);
        if(recordLastOne.getAddUserId().equals(inputDto.getUserId())){//如果最近的一次拜访人就是当前操作人，则可以直接创建
            return null;
        }
        //拜访计划的创建时间，上一次的
        Date planCreateTime = recordLastOne.getAddTime();
        //如果是ERP客户，取客户的办公地址：
        Integer workAreaId = traderCustomerForMobileMapper.getTraderWorkAreaByCustomerName(inputDto.getTraderName());
        if(workAreaId == null ){//如果不是ERP客户或者没取到客户的办公地址
            workAreaId = recordLastOne.getAreaCode();//取上一次的拜访时的地址
        }
        //判断是否继承了该区域
        if(workAreaId !=null && workAreaId > 0){
            String jichenTimeStr = traderCustomerForMobileMapper.getCustomerRegionSale(workAreaId,inputDto.getUserId());
            //继承的时间-即负责这个区域的开始时间，还要判断该区域的销售是不是当前人
            if(StringUtils.isNotEmpty(jichenTimeStr)){
                // 解析字符串为LocalDateTime
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime jichenTime = LocalDateTime.parse(jichenTimeStr, formatter);
                // 将Date转换为LocalDateTime
                LocalDateTime planCreateTimeAsLocalDateTime = planCreateTime.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
                // 检查jichenTime是否在planCreateTime之后
                if (jichenTime.isAfter(planCreateTimeAsLocalDateTime)) {
                    //
                    log.info("继承时间：{},拜访计划时间:{}",jichenTimeStr,planCreateTimeAsLocalDateTime);
                    return null;
                }
            }
        }

        LocalDate lastDate = recordLastOne.getAddTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = lastDate.plusDays(180); // 从lastDate开始+180天。
        LocalDate currentDate = LocalDate.now(); // 获取当前日期
        if(!currentDate.isAfter(endDate)){//如果未超过180天,不允许创建新的拜访计划
            return recordLastOne;
        }

        //saleOrderMobileAggregateApiService.
        SaleorderInfoDto saleorderInfoDto = saleOrderMobileAggregateApiService.querySaleorderByCustomerName(inputDto.getTraderName());
        if(saleorderInfoDto != null){
            LocalDate lastOrderDate = (new Date(saleorderInfoDto.getAddTime())).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if(lastOrderDate.isAfter(lastDate) && lastOrderDate.isBefore(endDate)){
                //在180天内下过单
                return recordLastOne;
            }
        }
        return null;

        //以下是原逻辑  -http://jira.ivedeng.com/browse/VDERP-17022
//        Integer lastAddUserId = null;
////        Integer lastAddUserId = recordLast.get(0).getAddUserId();
//        List<VisitRecord> tempList = new ArrayList<>();
//        //如果有了，查找这个拜访人/?创建人
//        for(VisitRecord record:recordLast){
//            if(lastAddUserId == null){
//                lastAddUserId = record.getAddUserId();
//                tempList.add(record);
//                continue;
//            }
//            if(lastAddUserId.equals(record.getAddUserId())){
//                tempList.add(record);
//                continue;
//            }else{ //如果遇到不一样的人，直接跳出
//                break;
//            }
//        }
//        //
//        VisitRecord lastRecord= findRecent180DaysPeriod(tempList);//上一条拜访记录对应的180天周期的拜访计划
//        LocalDate lastDate = lastRecord.getAddTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//        LocalDate currentDate = LocalDate.now(); // 获取当前日期
//        LocalDate endDate = lastDate.plusDays(180); // 从lastDate开始+180天。
//        boolean isBetween = currentDate.isAfter(lastDate) && lastDate.isBefore(endDate);
//        if (isBetween) {//在180天内
//            return lastRecord;
//        }
//        return null;
    }

//    public static void main(String[] args) {
//        LocalDate currentDate = LocalDate.now(); // 获取当前日期
//        LocalDate strD180 = currentDate.minusDays(180);
//System.out.println(strD180.toString());
//
//
//    }

    /**
     * 如果是erp客户，需要将办公地址更新到办公地址表中。
     * 并判断是否一致，不一致时才更新
     * @param visitInputDto
     */
    public void insertTraderWorkArea(VisitInputDto visitInputDto){
        if(visitInputDto.getTraderCustomerId()!= null && visitInputDto.getTraderCustomerId()>0){
            //创建拜访计划时，发现是erp客户，进入更新办公地址逻辑
            Integer workAreaId = traderCustomerForMobileMapper.getTraderWorkAreaByTraderId(visitInputDto.getTraderId());
            if(workAreaId == null){
                log.info("更新客户办公地址:{}",visitInputDto.getTraderId());
                traderCustomerForMobileMapper.insertTraderWorkAreaByTraderId(visitInputDto.getTraderId(),
                        visitInputDto.getAreaCode(),visitInputDto.getProvinceName()+" "+visitInputDto.getCityName()+" "+visitInputDto.getAreaName() );
            }else if(workAreaId != null || !visitInputDto.getAreaCode().equals(workAreaId)) {
                log.info("更新客户办公地址:{}", visitInputDto.getTraderId());
                traderCustomerForMobileMapper.updateTraderWorkAreaByTraderId(visitInputDto.getTraderId(),
                        visitInputDto.getAreaCode(), visitInputDto.getProvinceName() + " " + visitInputDto.getCityName() + " " + visitInputDto.getAreaName());
            }else{
                log.info("地址一致，暂不更新");
            }
        }

    }


    /**
     * 保存拜访计划
     * @param visitInputDto
     * @return
     */
    @Transactional
    @Override
    public VisitInputDto insertVisitRecord(VisitInputDto visitInputDto) {
        if(ObjectUtil.isNotEmpty(visitInputDto.getId())){//编辑操作
            VisitRecord record =  visitRecordMapper.selectByPrimaryKey(visitInputDto.getId());
            record.setCityCode(visitInputDto.getCityCode());
            record.setCityName(visitInputDto.getCityName());
            record.setAreaCode(visitInputDto.getAreaCode());
            record.setAreaName(visitInputDto.getAreaName());
            record.setOrgId(visitInputDto.getOrgId());
            record.setOrgGroup(visitInputDto.getOrgGroup());
            record.setTraderId(visitInputDto.getTraderId());
            record.setTraderCustomerId(visitInputDto.getTraderCustomerId());
            record.setCustomerName(visitInputDto.getCustomerName());
            record.setCustomerNature(visitInputDto.getCustomerNature());
            record.setCustomerFrom(visitInputDto.getCustomerFrom());
            record.setPlanVisitDate(visitInputDto.getPlanVisitDate());
            record.setProvinceCode(visitInputDto.getProvinceCode());
            record.setProvinceName(visitInputDto.getProvinceName());
            record.setVisitTarget(visitInputDto.getVisitTarget());

            record.setModTime(new Date());
            record.setModUserId(visitInputDto.getModUserId());
            visitRecordMapper.updateByPrimaryKey(record);
            insertTraderWorkArea(visitInputDto);
            //记录日志
            VisitRecordLog recordLog = new VisitRecordLog();
            recordLog.setUserId(visitInputDto.getUserId());
            recordLog.setDescription(JSONObject.toJSONString(visitInputDto));//先记录原始用户参数
            recordLog.setOperationTime(new Date());
            recordLog.setOperationType("A1");//编辑拜访记录
            visitRecordLogMapper.insertSelective(recordLog);
            return visitInputDto;
        }
        VisitRecordLog recordLog = new VisitRecordLog();
        recordLog.setUserId(visitInputDto.getUserId());
        recordLog.setDescription(JSONObject.toJSONString(visitInputDto));//先记录原始用户参数
        recordLog.setOperationTime(new Date());
        recordLog.setOperationType("A1");//创建拜访记录

        Date now = new Date();
        visitInputDto.setCardTime(null);
        visitInputDto.setVisitSuccess("N");
        visitInputDto.setAddTime(now);
        visitInputDto.setModTime(now);
        visitInputDto.setAddUserId(visitInputDto.getUserId());
        visitInputDto.setModUserId(visitInputDto.getUserId());
        visitInputDto.setVisitorId(visitInputDto.getUserId());
        visitInputDto.setIsDelete(0);
        UserDto userDto = userService.getUserById(visitInputDto.getUserId());

        visitInputDto.setVisitorName(userDto.getUsername());
        VisitRecord record = new VisitRecord()  ;

        BeanUtils.copyProperties(visitInputDto,record);
        visitRecordMapper.insert(record);
        insertTraderWorkArea(visitInputDto);

        recordLog.setRecordId(record.getId());
        visitRecordLogMapper.insert(recordLog);
        visitInputDto.setId(record.getId());

        //VDERP-17057  【客户档案】ERP客户档案时间轴 创建拜访计划
        visitTrack(visitInputDto);
        
        return visitInputDto;
    }

    
	private void visitTrack(VisitInputDto visitInputDto) {
		try {
			Map<String, Object> trackParams = new HashMap<>();
	        //${visitorName}(${visitorNumer})计划${planVisitDateStr}拜访(${visitTargetNames})
	        Integer userId = visitInputDto.getUserId();
	        UserDto userDto = userService.getUserById(userId);
	        trackParams.put("track_user", userDto);
	        trackParams.put("visitorName", userDto.getUsername());
	        trackParams.put("visitorNumer", userDto.getNumber());
	        String planVisitDateStr = DateUtil.formatChineseDate(visitInputDto.getPlanVisitDate(), false, false);
	        trackParams.put("planVisitDateStr", planVisitDateStr);
	        String visitTargetNames = "";
	        if (StringUtils.isNotBlank(visitInputDto.getVisitTarget())) {
	            List<VisitConfigDto> visitConfigDtoList = JSONObject.parseArray(visitConfigList, VisitConfigDto.class);
	            //将visitConfigDtoList转换为map，key为visitType，value为visitName
	            Map<String, String> visitConfigMap = visitConfigDtoList.stream().collect(Collectors.toMap(VisitConfigDto::getVisitType, VisitConfigDto::getVisitName));
	            //将VisitInputDto.getVisitTarget()按英文逗号分割，并将visitConfigList转换成map，获取对应的中文名称
	            String[] visitTargetArr = visitInputDto.getVisitTarget().split(",");
	            List<String> visitTargetNameList = new ArrayList<>();
	            for (String visitTarget : visitTargetArr) {
	                visitTargetNameList.add(visitConfigMap.get(visitTarget));
	            }
	            visitTargetNames = StringUtils.join(visitTargetNameList, "，");
	        }
	        trackParams.put("visitTargetNames", visitTargetNames);
	        //客户ID
	        trackParams.put("traderId", visitInputDto.getTraderId());
	        TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.PRE_SALE_NEW_VISITE);
	        TrackParamsData trackParamsData = new TrackParamsData();
	        trackParamsData.setTrackParams(trackParams);
	        trackParamsData.setTrackTime(new Date());
	        trackParamsData.setTrackResult(R.success());
	        trackParamsData.setEventTrackingEnum(EventTrackingEnum.PRE_SALE_NEW_VISITE);
	        trackStrategy.track(trackParamsData);
		}catch(Exception e) {
			log.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.PRE_SALE_NEW_VISITE.getArchivedName(),e);
		}
	}
	
	
    @Transactional
    @Override
    public VisitInputDto editVisitRecord(VisitInputDto visitInputDto) {
        VisitRecordLog recordLog = new VisitRecordLog();
        UserDto userDto = userService.getUserById(visitInputDto.getUserId());

        VisitRecord record =  visitRecordMapper.selectByPrimaryKey(visitInputDto.getId());

        recordLog.setUserId(visitInputDto.getUserId());
        recordLog.setDescription(JSONObject.toJSONString(visitInputDto));//先记录原始用户参数
        recordLog.setOperationTime(new Date());
        recordLog.setOperationType("A6");//编辑拜访记录
        recordLog.setRecordId(record.getId());
        visitRecordLogMapper.insert(recordLog);

        Date now = new Date();
        visitInputDto.setAddTime(now);
        record.setModTime(now);
        record.setModUserId(visitInputDto.getUserId());
        record.setPlanVisitDate(visitInputDto.getPlanVisitDate());
        record.setVisitTarget(visitInputDto.getVisitTarget());
        record.setCustomerName(visitInputDto.getCustomerName());
        record.setTraderId(visitInputDto.getTraderId());
        record.setTraderCustomerId(visitInputDto.getTraderCustomerId());
        record.setCustomerNature(visitInputDto.getCustomerNature());
        record.setProvinceCode(visitInputDto.getProvinceCode());
        record.setProvinceName(visitInputDto.getProvinceName());
        record.setCityCode(visitInputDto.getCityCode());
        record.setCityName(visitInputDto.getCityName());
        record.setAreaCode(visitInputDto.getAreaCode());
        record.setAreaName(visitInputDto.getAreaName());
        record.setVisitorId(visitInputDto.getUserId());
        record.setVisitorName(userDto.getUsername());
        visitRecordMapper.updateByPrimaryKey(record);
        return queryVisitRecordById(record.getId());
     }
//
//     //测试Copilot
//    public static void main(String[] args) {
//        int x =1;
//        int y =2;
//        //定义 t,t为x+y,并将t输出到控制台
//        int t = x+y;
//        System.out.println(t);
//        //判断t是否为偶数，是则输出“偶数”，否则输出“奇数”
//        if(t%2==0) {
//            System.out.println("偶数");
//        }else {
//
//
//    }



    /**
     * 保存打卡记录，并生成
     * @param visitCardInputDto
     * @return
     */
    @Override
    @Transactional
    public VisitInputDto saveVisitCard(VisitCardInputDto visitCardInputDto) {

        UserDto userDto = userService.getUserById(visitCardInputDto.getUserId());
        Date now = new Date();
        String nowStr = DateUtil.format(now,"yyyy-MM-dd HH:mm:ss");
        VisitRecord record =  visitRecordMapper.selectByPrimaryKey(visitCardInputDto.getId());
        record.setCardOff("Y");
        record.setPictureList( StringUtils.join(visitCardInputDto.getPicList(),","));
        record.setCardTime(nowStr);
        record.setActualVisitDate(now);
        record.setModTime(now);
        record.setModUserId(visitCardInputDto.getUserId());
        record.setVisitorId(visitCardInputDto.getUserId());
        record.setVisitorName(userDto.getUsername());
        visitRecordMapper.updateByPrimaryKeySelective(record);

        VisitRecordLog recordLog = new VisitRecordLog();
        recordLog.setUserId(visitCardInputDto.getUserId());
        recordLog.setDescription(JSONObject.toJSONString(visitCardInputDto));//先记录原始用户参数
        recordLog.setOperationTime(now);
        recordLog.setOperationType("A2");//打卡
        recordLog.setRecordId(visitCardInputDto.getId());
        visitRecordLogMapper.insert(recordLog);

        return queryVisitRecordById(record.getId());

    }


    private  void createBussinessChance(VisitRecord record,VisitContentInputDto visitContentInputDto,
                                        UserDto userDto,Date now,Integer traderContactId){
        BussinessChanceEntity bussinessChanceEntity = new BussinessChanceEntity();
        bussinessChanceEntity.setType(TYPE_SALE_ADD);
        bussinessChanceEntity.setInquiry(INQUIRY_SALE_ADD);
        bussinessChanceEntity.setCommunication(INQUIRY_VISIT);
        bussinessChanceEntity.setOrgId(userDto.getOrgId());
        //SOURCE = 新/老
        if (record.getVisitTarget().indexOf("A")>-1) {
            bussinessChanceEntity.setSource(SOURCE_SALE_ADD_NEW);
        } else {
            bussinessChanceEntity.setSource(SOURCE_SALE_ADD_OLD);
        }
        bussinessChanceEntity.setCompanyId(1);
        bussinessChanceEntity.setCheckTraderName(record.getCustomerName());
        bussinessChanceEntity.setCheckTraderContactName(visitContentInputDto.getContactName());
        bussinessChanceEntity.setCheckTraderContactMobile(visitContentInputDto.getContactMobile());
        bussinessChanceEntity.setCheckTraderContactTelephone(visitContentInputDto.getContactTele());
        bussinessChanceEntity.setReceiveTime(now.getTime());
        bussinessChanceEntity.setAssignTime(now.getTime());
        if(visitContentInputDto.getNextVisitDate()!=null){
            bussinessChanceEntity.setOrderTime(visitContentInputDto.getNextVisitDate().getTime());//预计成单时间为下次拜访时间
        }else{
            bussinessChanceEntity.setOrderTime(now.getTime()+7*24*3600*1000);//VDERP-16425 【需求变更】预计下次拜访时间是选择近期不做拜访的时候，同步商机中，预计成单日期变更为时间+7天
        }
        bussinessChanceEntity.setAmount(new BigDecimal(1));
        bussinessChanceEntity.setAddTime(now.getTime());
        bussinessChanceEntity.setCreator(visitContentInputDto.getUserId());
        bussinessChanceEntity.setUpdater(visitContentInputDto.getUserId());
        bussinessChanceEntity.setModTime(now.getTime());
        bussinessChanceEntity.setProductCommentsSale(visitContentInputDto.getCommucateContent());

        bussinessChanceEntity.setTraderId(record.getTraderId());
        bussinessChanceEntity.setTraderName(record.getCustomerName());
        bussinessChanceEntity.setContent(null);

        bussinessChanceEntity.setMobile(visitContentInputDto.getContactMobile());
        bussinessChanceEntity.setTelephone(visitContentInputDto.getContactTele());
        bussinessChanceEntity.setTraderContactName(visitContentInputDto.getContactName());
        if(record.getTraderId()!=null && record.getTraderId()>0) {//如果是erp的客户-设置联系人ID
            bussinessChanceEntity.setTraderContactId(traderContactId);
        }
        bussinessChanceEntity.setUserId(visitContentInputDto.getUserId());
        bussinessChanceEntity.setStatus(6);//将商机状态设置为进行中
        bussinessChanceEntity.setSystemBusinessLevel(941);//商机等级枚举类
        bussinessChanceEntity.setSystemOrderRate(953);//商机成单几率枚举类 40%
        bussinessChanceEntity.setCompletion(84);//商机完成度
        mobileBussinessChanceMapper.insertSelective(bussinessChanceEntity);



        // 商机编号
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.BUSINESS_CHANCE, NoGeneratorBean.builder().id(bussinessChanceEntity.getBussinessChanceId()).numberOfDigits(9).build());
        String code = new BillNumGenerator().distribution(billGeneratorBean);
        BussinessChanceEntity toNo = new BussinessChanceEntity();
        toNo.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());
        toNo.setBussinessChanceNo(code);
        mobileBussinessChanceMapper.updateByPrimaryKeySelective(toNo);
        record.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());
        record.setBussinessChanceNo(code);


        CommunicateRecord communicateRecordDto = new CommunicateRecord();
        communicateRecordDto.setTraderId(record.getTraderId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try{
            communicateRecordDto.setBegintime(sdf.parse(record.getCardTime()).getTime());
        } catch (ParseException e) {
            log.error("打卡时间解析错误",e);
            communicateRecordDto.setBegintime(now.getTime());
        }
        communicateRecordDto.setAddTime(now.getTime());
        communicateRecordDto.setEndtime(now.getTime()+1*60*60*1000); //当前时间+1小时
        communicateRecordDto.setContact(visitContentInputDto.getContactName());
        communicateRecordDto.setContactMob(visitContentInputDto.getContactMobile());
        if(visitContentInputDto.getNextVisitDate()!=null){
            communicateRecordDto.setNextContactDate(new Date(visitContentInputDto.getNextVisitDate().getTime()-3*24*3600*1000));
            communicateRecordDto.setNoneNextDate(0);
        }else{
            communicateRecordDto.setNextContactDate(null);
            communicateRecordDto.setNoneNextDate(1);
        }
        communicateRecordDto.setCommunicateType(244);//关联类型为商机
        communicateRecordDto.setRelatedId(bussinessChanceEntity.getBussinessChanceId());
        communicateRecordDto.setContentSuffix(visitContentInputDto.getCommucateContent());
        communicateRecordDto.setCreator(visitContentInputDto.getUserId());
        communicateRecordDto.setCompanyId(1);

        mobileCommunicateRecordMapper.insertSelective(communicateRecordDto);
    }

    private  Integer createTraderContract(Integer traderId,VisitContentInputDto visitContentInputDto,Date now){
        Integer traderContactId = null;
        //联系人职位同步联系人信息映射关系（根据联系人姓名判定）：
        //客户已有联系人：直接替换原联系人职位；
        //客户新的联系人：根据联系人姓名、手机、电话、职位等信息新增联系人信息；
        //非客户：不同步联系人信息；
        //其中职位根据客户类型同步数值
        //客户类型为临床医疗分销：老板、采购负责人、采购专员、销售负责人、销售专员、财务负责人、财务专员、物流负责人、物流专员、商务负责人、商务专员、售后负责人、售后专员、其他；
        //客户类型为临床医疗终端：总经理、院长、副院长、采购负责人、采购专员、科室主任、科室医生、护士长、护士、运营负责人、运营专员、财务负责人、财务专员、设备科长、医工、库房负责人、库房专员、咨询师；
        //手机	手机
        //电话	电话
        //联系人职位	职位
        //TODO 处理联系人信息
        //先根据traderId和name查询是否存在，存在即更新，不存在即新增
        TraderContact traderContact = new TraderContact();
        traderContact.setTraderId(traderId);
        traderContact.setName(StringUtils.trim(visitContentInputDto.getContactName()));
        traderContact.setMobile(StringUtils.trim(visitContentInputDto.getContactMobile()));
        traderContact.setPosition(StringUtils.trim(visitContentInputDto.getContactPosition()));
        traderContact.setTelephone(StringUtils.trim(visitContentInputDto.getContactTele()));
        traderContact.setUpdater(visitContentInputDto.getUserId());
        traderContact.setModTime(now.getTime());
        traderContact.setIsOnJob(1);
        traderContact.setIsEnable(1);
        log.info("查询是否需要更新联系人");
        List<TraderContact> oldContactList = traderContactMapper.findByTraderIdAndName(traderContact);
        if(CollectionUtils.isNotEmpty(oldContactList)){
            traderContactId = oldContactList.get(0).getTraderContactId();
            log.info("存在同名的联系人，判断为需要更新联系人信息");
            traderContact.setTraderContactId(oldContactList.get(0).getTraderContactId());//取第一条联系人的记录进行更新，忽略重名的情况
            traderContactMapper.updateContractInfoByTraderContractId(traderContact);

        }else{
            //
            log.info("不存在同名的联系人，判断需要新增联系人信息");
            traderContact.setAddTime(now.getTime());
            traderContact.setCreator(visitContentInputDto.getUserId());
            traderContactMapper.insertSelective(traderContact);
            traderContactId = traderContact.getTraderContactId();
        }
        return traderContactId;
    }

    /**
     * 保存拜访内容
     * @param visitContentInputDto
     * @return
     */
    @Override
    @Transactional
    public VisitInputDto saveVisitContent(VisitContentInputDto visitContentInputDto) {



        Date now = new Date();
        VisitRecord record =  visitRecordMapper.selectByPrimaryKey(visitContentInputDto.getId());
        //联系人信息
        record.setContactName(visitContentInputDto.getContactName());
        record.setContactMobile(visitContentInputDto.getContactMobile());
        record.setContactTele(visitContentInputDto.getContactTele());
        record.setContactPosition(visitContentInputDto.getContactPosition());

        //沟通事项
        record.setShowPpt(visitContentInputDto.getShowPpt());
        record.setInviteReg(visitContentInputDto.getInviteReg());
        record.setTraderContractId(visitContentInputDto.getTraderContractId());
        record.setRegMobile(visitContentInputDto.getRegMobile());

        record.setCommucateContent(visitContentInputDto.getCommucateContent());



        Integer traderContactId = null;//联系人ID  创建或更新

        if(record.getTraderId()!=null && record.getTraderId()>0){//如果是来自ERP的客户
            traderContactId = createTraderContract(record.getTraderId(),visitContentInputDto,now);
        }
//        if("Y".equals(visitContentInputDto.getInviteReg())){//判断是否邀请客户注册
//            //TODO 处理客户注册逻辑
//        }
        UserDto userDto = userService.getUserById(record.getVisitorId());

        if("Y".equals(visitContentInputDto.getCreateBusinessChange()) && !"Y".equals(record.getCreateBusinessChange())){
            //TODO 处理商机逻辑
            createBussinessChance(record,visitContentInputDto,userDto,now,traderContactId);
            //工作台-精准营销活动，需要更新活动-销售任务中的是否已沟通更新为已沟通。-Kerwin VDERP-15559
            // marketPlanApiService.updateMarketPlanTraderSendMsg(record.getTraderId());

        }
        Integer qudaoType = 465;// 465分销466终端;
        //处理拜访结果
        if(record.getVisitTarget() != null  && record.getVisitTarget().indexOf("A")>-1 && qudaoType.equals(record.getCustomerNature())){
            if("Y".equals(visitContentInputDto.getInviteReg())
                    && "Y".equals(visitContentInputDto.getShowPpt()) ){//判断是否邀请客户注册
                record.setVisitSuccess("Y");
            }else{
                record.setVisitSuccess("N");
            }
        }else{
            record.setVisitSuccess("Y");
        }
        record.setCreateBusinessChange(visitContentInputDto.getCreateBusinessChange());
        record.setNextVisitDate(visitContentInputDto.getNextVisitDate());
        record.setModUserId(visitContentInputDto.getUserId());
        record.setModTime(now);
        record.setVisitorId(visitContentInputDto.getUserId());
        record.setVisitorName(userDto.getUsername());
        visitRecordMapper.updateByPrimaryKey(record);

        VisitRecordLog recordLog = new VisitRecordLog();
        recordLog.setUserId(visitContentInputDto.getUserId());
        recordLog.setDescription(JSONObject.toJSONString(visitContentInputDto));//先记录原始用户参数
        recordLog.setOperationTime(now);
        recordLog.setOperationType("A3");//填写拜访内容
        recordLog.setRecordId(visitContentInputDto.getId());
        visitRecordLogMapper.insert(recordLog);

        completeTrack(record);
        
        return queryVisitRecordById(record.getId());
    }

	private void completeTrack(VisitRecord record) {
		try {
			Map<String, Object> trackParams = new HashMap<>();
	        Integer userId = record.getVisitorId();
	        UserDto userDto = userService.getUserById(userId);
	        trackParams.put("track_user", userDto);
	        trackParams.put("visitorName", userDto.getUsername());
	        trackParams.put("visitorNumer", userDto.getNumber());
	        //查询cardTime
	        Date cardTime = DateUtils.parseDate(record.getCardTime(), DateUtils.DATE_FORMAT_19);
	        String actualVisitDateStr = DateUtil.formatChineseDate(cardTime, false, false);
	        trackParams.put("actualVisitDateStr", actualVisitDateStr);
	        String visitTargetNames = "";
	        if (StringUtils.isNotBlank(record.getVisitTarget())) {
	            List<VisitConfigDto> visitConfigDtoList = JSONObject.parseArray(visitConfigList, VisitConfigDto.class);
	            //将visitConfigDtoList转换为map，key为visitType，value为visitName
	            Map<String, String> visitConfigMap = visitConfigDtoList.stream().collect(Collectors.toMap(VisitConfigDto::getVisitType, VisitConfigDto::getVisitName));
	            //将VisitInputDto.getVisitTarget()按英文逗号分割，并将visitConfigList转换成map，获取对应的中文名称
	            String[] visitTargetArr = record.getVisitTarget().split(",");
	            List<String> visitTargetNameList = new ArrayList<>();
	            for (String visitTarget : visitTargetArr) {
	                visitTargetNameList.add(visitConfigMap.get(visitTarget));
	            }
	            visitTargetNames = StringUtils.join(visitTargetNameList, "，");
	        }
	        trackParams.put("visitTargetNames", visitTargetNames);
	        //客户ID
	        trackParams.put("traderId", record.getTraderId());
	        //获取讲解PPT和用户注册
	        StringBuilder contactInfoBuilder = new StringBuilder("");
        	if("Y".equals(record.getShowPpt()) && "Y".equals(record.getInviteReg())){
        		contactInfoBuilder.append("，");
        		contactInfoBuilder.append("完成了PPT讲解");
        		contactInfoBuilder.append("、");
        		contactInfoBuilder.append("客户注册");
        	}
        	if("Y".equals(record.getShowPpt()) && "N".equals(record.getInviteReg())){
        		contactInfoBuilder.append("，");
        		contactInfoBuilder.append("完成了PPT讲解");
        	}
        	if("N".equals(record.getShowPpt()) && "Y".equals(record.getInviteReg())){
        		contactInfoBuilder.append("，");
        		contactInfoBuilder.append("完成了客户注册");
        	}
        	if(StringUtils.isNotEmpty(contactInfoBuilder.toString())) {
        		trackParams.put("contactInfo", contactInfoBuilder.toString());
        	}else {
        		trackParams.put("contactInfo", "。");
        	}
	        TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.PRE_SALE_COMPLETE_VISITE);
	        
	        
	        TrackParamsData trackParamsData = new TrackParamsData();
	        trackParamsData.setTrackParams(trackParams);
	        trackParamsData.setTrackTime(new Date());
	        trackParamsData.setTrackResult(R.success());
	        trackParamsData.setEventTrackingEnum(EventTrackingEnum.PRE_SALE_COMPLETE_VISITE);
	        trackStrategy.track(trackParamsData);
		}catch(Exception e) {
			log.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.PRE_SALE_COMPLETE_VISITE.getArchivedName(),e);
		}
	}


    @Qualifier("mobileBussinessChanceMapper")
    @Autowired
    private MobileBussinessChanceMapper mobileBussinessChanceMapper;

    @Autowired
    private MobileCommunicateRecordMapper mobileCommunicateRecordMapper;


    /**
     * 渠道类型 老客户
     */
    public static final Integer SOURCE_SALE_ADD_OLD = 366;

    /**
     * 渠道类型 新客户
     */
    public static final Integer SOURCE_SALE_ADD_NEW = 477;
    /**
     * 询价行为 销售新增商机
     */
    public static final Integer INQUIRY_SALE_ADD = 4062;

    /**
     * 询价行为 渠道名称-拜访
     */
    public static final Integer INQUIRY_VISIT = 4991;
    /**
     * 商机类型 销售新增商机
     */
    public static final Integer TYPE_SALE_ADD = 392;

    @Autowired
    private TraderContactMapper traderContactMapper;


    /**
     * 编辑拜访记录
     * 拜访事项缺失，可以重新编辑拜访记录；
     * 是否产生商机若已经选择过是后，不可以编辑“是否产生商机”字段，此字段置灰；若之前选择否时，可以编辑“是否产生商机”字段；
     * @param visitContentInputDto
     * @return
     */
    @Override
    public VisitInputDto editVisitContent(VisitContentInputDto visitContentInputDto) {
        Date now = new Date();
        VisitRecord record =  visitRecordMapper.selectByPrimaryKey(visitContentInputDto.getId());
        //联系人信息
        record.setContactName(visitContentInputDto.getContactName());
        record.setContactMobile(visitContentInputDto.getContactMobile());
        record.setContactTele(visitContentInputDto.getContactTele());
        record.setContactPosition(visitContentInputDto.getContactPosition());

        //沟通事项
        record.setShowPpt(visitContentInputDto.getShowPpt());
        record.setInviteReg(visitContentInputDto.getInviteReg());
        record.setTraderContractId(visitContentInputDto.getTraderContractId());
        record.setRegMobile(visitContentInputDto.getRegMobile());


        Integer traderContactId = null;//联系人ID  创建或更新

        if(record.getTraderId()!=null && record.getTraderId() >0){//判断是否邀请客户注册
            //TODO 处理客户注册逻辑
            traderContactId = createTraderContract(record.getTraderId(),visitContentInputDto,now);
        }

        UserDto userDto = userService.getUserById(record.getVisitorId());
        if( !"Y".equals(record.getCreateBusinessChange())  && "Y".equals(visitContentInputDto.getCreateBusinessChange()) ){
            //TODO 处理商机逻辑
            createBussinessChance(record,visitContentInputDto,userDto,now,traderContactId);
        }
        Integer qudaoType = 465;// 465分销466终端;
        //处理拜访结果
        if(record.getVisitTarget() != null  && record.getVisitTarget().indexOf("A")>-1 && qudaoType.equals(record.getCustomerNature())){
            if("Y".equals(visitContentInputDto.getInviteReg())
                    && "Y".equals(visitContentInputDto.getShowPpt()) ){//判断是否邀请客户注册
                record.setVisitSuccess("Y");
            }else{
                record.setVisitSuccess("N");
            }
        }else{
            record.setVisitSuccess("Y");
        }
        record.setCommucateContent(visitContentInputDto.getCommucateContent());
        record.setCreateBusinessChange(visitContentInputDto.getCreateBusinessChange());
        record.setNextVisitDate(visitContentInputDto.getNextVisitDate());
        record.setModUserId(visitContentInputDto.getUserId());
        record.setModTime(now);


        visitRecordMapper.updateByPrimaryKey(record);

        VisitRecordLog recordLog = new VisitRecordLog();
        recordLog.setUserId(visitContentInputDto.getUserId());
        recordLog.setDescription(JSONObject.toJSONString(visitContentInputDto));//先记录原始用户参数
        recordLog.setOperationTime(now);
        recordLog.setOperationType("A5");//编辑拜访记录
        recordLog.setRecordId(visitContentInputDto.getId());
        visitRecordLogMapper.insert(recordLog);

        return queryVisitRecordById(record.getId());
    }

    /**
     *
     * @return
     */
    @Override
    public boolean canCheckTraderDetail(UserOrgEnums userOrgEnums, VisitInputDto record, Integer userId, List<Integer> userIds) {
        return record.getCustomerFrom() == 1 && record.getTraderId() != null && visitRecordApiService.checkIfTraderExists(userIds, record.getTraderId(),427);
    }


    /**
     * 删除拜访计划
     * @param deleteDto
     * @return
     */
    @Override
    public boolean deleteVisitRecord(VisitDeleteDto deleteDto) {
        Date now = new Date();
        VisitRecord record =  visitRecordMapper.selectByPrimaryKey(deleteDto.getId());
        if(record == null){
            log.error("删除拜访计划失败{}",new Object[]{deleteDto});
            return false;
        }
        record.setModUserId(deleteDto.getUserId());
        record.setModTime(now);
        record.setIsDelete(1);//打上删除标识
        visitRecordMapper.updateByPrimaryKeySelective(record);


        VisitRecordLog recordLog = new VisitRecordLog();
        recordLog.setUserId(deleteDto.getUserId());
        recordLog.setDescription(JSONObject.toJSONString(deleteDto));//先记录原始用户参数
        recordLog.setOperationTime(now);
        recordLog.setOperationType("A4");//删除拜访计划
        recordLog.setRecordId(deleteDto.getId());
        visitRecordLogMapper.insert(recordLog);

        return true;

    }
}
