package com.vedeng.goods.manager.extension.handler;

import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.impl.MaintainDataPrice;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class GoodsPriceTodoHandler extends BaseGoodsTodoHandler {

    @Resource
    private MaintainDataPrice maintainDataPrice;
    @Resource
    private BasePriceService basePriceService;

    @Override
    public int getServiceId() {
        return PRICING_SERVICE_ID;
    }

    @Override
    public ITodoInstance getITodoInstance() {
        return maintainDataPrice;
    }

    @Override
    public boolean isDone() {
        SkuPriceInfoDetailResponseDto skuPriceInfoQuery = basePriceService.findSkuPriceInfoBySkuNo(getSkuNo());
        if (skuPriceInfoQuery != null && CollectionUtils.isNotEmpty(skuPriceInfoQuery.getPurchaseList())) {
            return true;
        }
        return false;
    }

    @Override
    public void onHandler() {
        if (!isDone()) {
            addGoodsTodoRecord(getGoodId(), getSkuNo());
        }
    }

    @Override
    public void finish(Object param) {
        if (param instanceof String) {
            String skuNo = (String) param;
            if (StringUtils.isNotBlank(skuNo)) {
                Integer skuId;
                try {
                    skuId = Integer.valueOf(skuNo.trim().substring(1));
                } catch (Exception e) {
                    logger.error("处理贝登售后政策待办时解析skuId失败", e);
                    return;
                }
                maintainDataPrice.finish(skuId);
            }
        }
    }
}
