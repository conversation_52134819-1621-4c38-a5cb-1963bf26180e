package com.vedeng.trader.group.deal;

import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.trader.group.BaseBehavior;
import com.vedeng.trader.group.model.SaleorderSum;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.BitSet;
import java.util.List;

/**
 * <b>Description:</b>最近成交时间行为<br>
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class DealRecentlyBehavior extends BaseBehavior {
    // 运行时注入Mapper
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private SaleorderMapper saleorderMapper=(SaleorderMapper) context.getBean(SaleorderMapper.class);
    @Override
    public BitSet calculate() {
        BitSet res=getBaseTraderSet();
        SaleorderSum query=new SaleorderSum();
        query.setPeriod(DateUtil.getStartAndEndPointForDays(getPeriod()));
        List<Integer> traderIds=saleorderMapper.getTraderIdsByDealRecently(query);
        BitSet dealSet=new BitSet();
        addBitSet(dealSet,traderIds);
        res.and(dealSet);
        return res;
    }
}
