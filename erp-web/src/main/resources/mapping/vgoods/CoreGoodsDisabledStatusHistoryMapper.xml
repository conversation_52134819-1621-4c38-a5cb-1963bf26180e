<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreGoodsDisabledStatusHistoryMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreGoodsDisabledStatusHistory">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="GOODS_NO" jdbcType="VARCHAR" property="goodsNo" />
    <result column="GOODS_TYPE" jdbcType="TINYINT" property="goodsType" />
    <result column="DISABLED_STATUS" jdbcType="TINYINT" property="disabledStatus" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
  </resultMap>

  <select id="getCreatorAndTime" resultType="com.vedeng.goods.model.dto.CoreGoodsDisabledStatusHistoryDTO">
    SELECT H.CREATOR, H.ADD_TIME, H.GOODS_NO
    FROM V_CORE_GOODS_DISABLED_STATUS_HISTORY H
    WHERE H.GOODS_NO = #{goodsNo,jdbcType=VARCHAR} AND H.GOODS_TYPE = #{goodsType,jdbcType=TINYINT}
    ORDER BY H.ID DESC
    LIMIT 1
  </select>


  <insert id="batchInsert" parameterType="java.util.List">
    insert into V_CORE_GOODS_DISABLED_STATUS_HISTORY (GOODS_NO,GOODS_TYPE, DISABLED_STATUS, REASON,CREATOR, ADD_TIME)
    values
    <foreach collection ="list" item="item" separator =",">
    (#{item.goodsNo,jdbcType=VARCHAR},#{item.goodsType,jdbcType=INTEGER}, #{item.disabledStatus,jdbcType=INTEGER}, #{item.reason,jdbcType=VARCHAR},#{item.creator,jdbcType=INTEGER}, #{item.addTime,jdbcType=BIGINT})
    </foreach>
  </insert>

  <insert id="insert" parameterType="com.vedeng.goods.model.CoreGoodsDisabledStatusHistory">
    insert into V_CORE_GOODS_DISABLED_STATUS_HISTORY (GOODS_NO,GOODS_TYPE, DISABLED_STATUS, REASON,CREATOR, ADD_TIME)
    values
      (#{goodsNo,jdbcType=VARCHAR}, #{goodsType,jdbcType=INTEGER}, #{disabledStatus,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR},#{creator,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT})
  </insert>

</mapper>