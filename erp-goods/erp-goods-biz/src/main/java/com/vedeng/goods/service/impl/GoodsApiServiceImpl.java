package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialSubHeadEntityDto;
import com.vedeng.erp.kingdee.service.KingDeeMaterialApiService;
import com.vedeng.goods.dto.*;
import com.vedeng.goods.mapper.CoreSkuMapper;
import com.vedeng.goods.mapper.VGoodsMapper;
import com.vedeng.goods.query.RelatedSkuQuery;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.goods.service.SkuSceneService;
import com.vedeng.goods.vo.CoreSkuVo;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @title: GoodsApiServiceImpl
 * <AUTHOR>
 * @Date: 2021/10/24 9:53
 */
@Service("GoodsAPIService")
@Slf4j
public class GoodsApiServiceImpl implements GoodsApiService {
    @Autowired
    private VGoodsMapper vGoodsMapper;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private SkuSceneService skuSceneService;

    @Autowired(required = false)
    private KingDeeMaterialApiService kingDeeMaterialApiService;

    @Override
    public List<GoodsDTO> queryGoodsForWindow(QueryGoodsDTO queryGoodsDTO) {
        return vGoodsMapper.queryGoodsForWindow(queryGoodsDTO);
    }

    @Override
    public List<CoreSkuVo> selectAllSkuByKeywords(RelatedSkuQuery relatedSkuQuery) {
        return coreSkuMapper.selectAllSkuByKeywords(relatedSkuQuery);
    }

    @Override
    public boolean querySkusHaveExclusive(List<String> skuNos) {

        if (CollectionUtils.isEmpty(skuNos)) {
            return false;
        }
        Integer num = coreSkuMapper.selectSkusHaveExclusive(skuNos);
        return Objects.nonNull(num) && num > 0;
    }

    @Override
    public CoreSkuVo getGoodsInfoBySkuId(Integer skuId) {
        return coreSkuMapper.getGoodsInfoBySkuId(skuId);
    }

    @Override
    public List<CoreSkuVo> getGoodsInfoBySkuNos(List<String> skuNos) {
        List<CoreSkuVo> goodsInfoBySkuNos = coreSkuMapper.getGoodsInfoBySkuNos(skuNos);
        return goodsInfoBySkuNos;
    }

    @Override
    public List<CoreSkuVo> getAllVirtualGoodsInfo(Integer status) {
        return coreSkuMapper.getAllVirtualGoodsInfo(status);
    }

    @Override
    public List<ProductManageAndAsistDto> batchQueryProductManageAndAsist(List<String> skus) {
        if (CollUtil.isEmpty(skus)) {
            return Collections.emptyList();
        }
        return coreSkuMapper.batchQueryProductManageAndAsist(skus);
    }

    @Override
    public KingDeeSkuInfoDto getSkuInfoBySkuId(Integer skuId) {
        return coreSkuMapper.getKingDeeSkuInfoBySkuId(skuId, null).get(0);
    }

    @Override
    public KingDeeMaterialDto getPushSkuInfoToKingDee(KingDeeSkuInfoDto skuInfoDto) {
        KingDeeMaterialDto kingDeeMaterialDto = new KingDeeMaterialDto();
        kingDeeMaterialDto.setFNumber(skuInfoDto.getSkuNo());
        kingDeeMaterialApiService.query(kingDeeMaterialDto);
        // 组装金蝶数据
        if (kingDeeMaterialDto.getKingDeeMaterialEntityId() == null) {
            kingDeeMaterialDto.setKingDeeBizEnums(KingDeeBizEnums.saveMaterial);
            kingDeeMaterialDto.setFmaterialid("0");
        } else {
            kingDeeMaterialDto.setKingDeeBizEnums(KingDeeBizEnums.updateMaterial);
        }

        kingDeeMaterialDto.setFNumber(skuInfoDto.getSkuNo());
        kingDeeMaterialDto.setFName(skuInfoDto.getSkuName());

        if (skuInfoDto.getModel() == null && skuInfoDto.getSpec() != null) {
            kingDeeMaterialDto.setFSpecification(skuInfoDto.getSpec());
        } else if (skuInfoDto.getModel() != null && skuInfoDto.getSpec() == null) {
            kingDeeMaterialDto.setFSpecification(skuInfoDto.getModel());
        } else {
            if (skuInfoDto.getSpuType() == 316 || skuInfoDto.getSpuType() == 1008) {
                kingDeeMaterialDto.setFSpecification(skuInfoDto.getModel());
            } else if (skuInfoDto.getSpuType() == 317 || skuInfoDto.getSpuType() == 318 || skuInfoDto.getSpuType() == 653) {
                kingDeeMaterialDto.setFSpecification(skuInfoDto.getSpec());
            } else {
                kingDeeMaterialDto.setFSpecification("");
            }
        }

        kingDeeMaterialDto.setFQzokDhh(skuInfoDto.getSkuNo());
        kingDeeMaterialDto.setFQzokGyswlbm(skuInfoDto.getMaterialCode());
        kingDeeMaterialDto.setFQzokPpptext(skuInfoDto.getBrandName());
        kingDeeMaterialDto.setFQzokZczhtext(skuInfoDto.getRegistrationNumber());
        kingDeeMaterialDto.setFQzokSpyyyjfltext(skuInfoDto.getFirstCategoryName());
        kingDeeMaterialDto.setFQzokSpyyejfltext(skuInfoDto.getSecondCategoryName());
        kingDeeMaterialDto.setFQzokSpyysjfltext(skuInfoDto.getThirdCategoryName());
        kingDeeMaterialDto.setFQzokSpyysijfltext("-");
        kingDeeMaterialDto.setFQzokSpyywjfltext("-");
        kingDeeMaterialDto.setFQzokFylqxyjfltext(skuInfoDto.getNoMedicalFirstTypeName());
        kingDeeMaterialDto.setFQzokFylqxejfltext(skuInfoDto.getNoMedicalSecondTypeName());
        kingDeeMaterialDto.setFTaxcategoryCodeId(skuInfoDto.getTaxCategoryNo());

        KingDeeMaterialSubHeadEntityDto subHeadEntity = new KingDeeMaterialSubHeadEntityDto();
        subHeadEntity.setFErpClsID("1");
        switch (skuInfoDto.getSpuType()) {
            case 316:
                subHeadEntity.setFCategoryID("CHLB03_SYS");
                break;
            case 317:
            case 653:
                subHeadEntity.setFCategoryID("CHLB02_SYS");
                break;
            case 318:
                subHeadEntity.setFCategoryID("CHLB01_SYS");
                break;
            case 319:
                subHeadEntity.setFCategoryID("");
                break;
            case 1008:
                subHeadEntity.setFCategoryID("CHLB08_SYS");
                break;
            case 4282:
                subHeadEntity.setFCategoryID("CHLB06_SYS");
                break;
            case 4283:
                subHeadEntity.setFCategoryID("CHLB07_SYS");
                break;
            default:
        }
        subHeadEntity.setFBaseUnitId(skuInfoDto.getUnitKingDeeNo());
        kingDeeMaterialDto.setSubHeadEntity(subHeadEntity);
        return kingDeeMaterialDto;
    }

    @Override
    public Integer getCheckPassSkuNum() {
        return coreSkuMapper.getCheckPassSkuNum();
    }

    @Override
    public List<KingDeeSkuInfoDto> getBatchInitSkuToKingDee(Integer limit) {
        return coreSkuMapper.getKingDeeSkuInfoBySkuId(null, limit);
    }

    @Override
    public List<Integer> getAllSkuId() {
        return coreSkuMapper.getAllSkuId();
    }

    @Override
    public List<CoreSkuVo> findBySkuBySkuIds(List<Integer> skuIds) {
        return coreSkuMapper.findBySkuIdIn(skuIds);
    }

    @Override
    public List<Integer> findAllSpecialSkuIds() {
        return coreSkuMapper.findAllSpecialSkus();
    }

    @Override
    public String getSkuTaxNo(Integer goodsId) {
        String taxCategoryNo = coreSkuMapper.getTaxCategoryNo(goodsId);
        if (StrUtil.isEmpty(taxCategoryNo)) {
            return "";
        }
        return StrUtil.padAfter(taxCategoryNo, 19, "0");
    }

    @Override
    public List<SkuSceneDto> getSceneAndCategory() {
        return skuSceneService.getSceneAndCategory();
    }

    @Override
    public Map<String, Object> skuTipMap(String skuNo) {
        return coreSkuMapper.skuTipMap(skuNo);
    }


}
