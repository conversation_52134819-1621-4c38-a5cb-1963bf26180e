package com.vedeng.erp.trader.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;
import com.vedeng.erp.business.mapper.BusinessLeadsMapper;
import com.vedeng.erp.business.mapper.BussinessChanceMapper;
import com.vedeng.erp.buyorder.service.BuyorderInfoQueryService;
import com.vedeng.erp.market.api.MarketPlanApiService;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.dto.TagDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.TagService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.api.CommunicateVoiceTaskApi;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.constant.TraderConstant;
import com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity;
import com.vedeng.erp.trader.domain.entity.TraderContactEntity;
import com.vedeng.erp.trader.domain.entity.TraderTagEntity;
import com.vedeng.erp.trader.dto.CommunicateAiSummaryApiDto;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecord;
import com.vedeng.erp.trader.dto.CommunicateTelRecordDto;
import com.vedeng.erp.trader.dto.CommunicateTelRecordParams;
import com.vedeng.erp.trader.dto.CommunicateVoiceTaskDto;
import com.vedeng.erp.trader.dto.FollowBindingTelParams;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;
import com.vedeng.erp.trader.mapper.CommunicateRecordMapper;
import com.vedeng.erp.trader.mapper.QuoteorderMapper;
import com.vedeng.erp.trader.mapper.TraderContactMapper;
import com.vedeng.erp.trader.mapper.TraderTagMapper;
import com.vedeng.erp.trader.mapstruct.CommunicateRecordConvertor;
import com.vedeng.erp.trader.service.CommunicateRecordService;
import com.vedeng.erp.trader.service.CommunicateSummaryApiService;
import com.vedeng.system.dao.QCellCoreMapper;
import com.vedeng.system.model.QCellCore;
import com.vedeng.trader.model.CommunicateRecord;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import groovy.util.logging.Slf4j;

/**
 * 沟通记录(CommunicateRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-12 15:44:48
 */
@Service
@Slf4j
public class CommunicateRecordServiceImpl implements CommunicateRecordService {

    private static final Logger log = LoggerFactory.getLogger(CommunicateRecordServiceImpl.class);
    @Autowired
    private CommunicateRecordMapper communicateRecordMapper;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    @Qualifier("newTraderContactMapper")
    private TraderContactMapper traderContactDaoMapper;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private CommunicateRecordConvertor communicateRecordConvertor;

    /**
     * 老标签服务 抽离的新类
     */
    @Autowired
    private TagService tagService;

    @Autowired
    private TraderTagMapper traderTagMapper;

    @Autowired
    private BussinessChanceMapper bussinessChanceMapper;

    @Autowired
    private QuoteorderMapper quoteorderMapper;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private BuyorderInfoQueryService buyorderInfoQueryService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Autowired
    private BusinessLeadsMapper businessLeadsMapper;

    @Autowired
    private MarketPlanApiService marketPlanApiService;

    @Autowired
    private CommunicateSummaryApiService communicateSummaryApiService;

    @Autowired
    private CommunicateVoiceTaskApi communicateVoiceTaskApi;
    
    @Autowired
    private QCellCoreMapper qCellCoreMapper;

    private static final String voiceStatusGptSuccess = "9";
    
    @Value("${erp_url}")
    private String erpUrl;
    
    @Value("${phone_prefix_array}")
	private String phonePrefixArray;

    @Override
    public PageInfo<CommunicateRecordDto> page(PageParam<CommunicateRecordDto> recordDtoPageParam) {

        PageInfo<CommunicateRecordDto> dtoPageInfo = PageHelper.startPage(recordDtoPageParam).doSelectPageInfo(() -> communicateRecordMapper.findByAll(recordDtoPageParam.getParam()));

        //沟通记录基础信息
        List<CommunicateRecordDto> communicateRecordDtoListBaseInfo = dtoPageInfo.getList();

        if (CollUtil.isNotEmpty(communicateRecordDtoListBaseInfo)) {

            //创建人id集合
            List<Integer> creatorIds = communicateRecordDtoListBaseInfo.stream().map(CommunicateRecordDto::getCreator).collect(Collectors.toList());

            //沟通记录id集合
            List<Integer> recordIds = communicateRecordDtoListBaseInfo.stream().map(CommunicateRecordDto::getCommunicateRecordId).collect(Collectors.toList());
            //联系人集合
            List<Integer> traderContactIds = communicateRecordDtoListBaseInfo.stream().map(CommunicateRecordDto::getTraderContactId).collect(Collectors.toList());
            //沟通目的
            List<Integer> optionList = communicateRecordDtoListBaseInfo.stream().map(CommunicateRecordDto::getCommunicateGoal).collect(Collectors.toList());
            //沟通方式
            List<Integer> modeList = communicateRecordDtoListBaseInfo.stream().map(CommunicateRecordDto::getCommunicateMode).collect(Collectors.toList());

            List<Integer> optionModeList = new ArrayList<>();
            optionModeList.addAll(optionList);
            optionModeList.addAll(modeList);

            //创建人信息
            List<UserDto> userInfoByUserIds = userApiService.getUserInfoByUserIds(creatorIds);


            //获取联系人信息
            List<TraderContactDto> traderContacts = null;
            if (CollUtil.isNotEmpty(traderContactIds)) {
                traderContacts = traderContactDaoMapper.getTraderContacts(traderContactIds);
            }

            //沟通记录 沟通目的、方式
            List<SysOptionDefinitionDto> sysOptionDefinitionDtos = null;
            if (CollUtil.isNotEmpty(optionModeList)) {
                sysOptionDefinitionDtos = sysOptionDefinitionApiService.getByIds(optionModeList);
            }

            Map<Integer, List<Integer>> typeToIdsMap = new HashMap<>();
            typeToIdsMap.put(TraderConstant.ID_244, new ArrayList<>());
            typeToIdsMap.put(TraderConstant.ID_245, new ArrayList<>());
            typeToIdsMap.put(TraderConstant.ID_246, new ArrayList<>());
            typeToIdsMap.put(TraderConstant.ID_247, new ArrayList<>());
            typeToIdsMap.put(TraderConstant.ID_248, new ArrayList<>());
            typeToIdsMap.put(TraderConstant.ID_4083, new ArrayList<>());

            //沟通记录 沟通ID（获取标签）

            for (CommunicateRecordDto recordDto : communicateRecordDtoListBaseInfo) {

                if (recordDto.getCommunicateAiSummaryApiDto() != null && recordDto.getCommunicateAiSummaryApiDto().getCommunicateSummaryId() != null) {
                    CommunicateAiSummaryApiDto communicateAiSummaryApiDto = communicateSummaryApiService
                            .getByCommunicateSummaryId(recordDto.getCommunicateAiSummaryApiDto().getCommunicateSummaryId());
                    recordDto.setCommunicateAiSummaryApiDto(communicateAiSummaryApiDto);
                }

                //创建人姓名
                for (UserDto userDto : userInfoByUserIds) {
                    if (userDto.getUserId().equals(recordDto.getCreator())) {
                        recordDto.setCreatorName(userDto.getUsername());
                        recordDto.setCreatorPic(userDto.getAliasHeadPicture());
                        break;
                    }
                }

                //联系人
                if (CollUtil.isNotEmpty(traderContacts)) {
                    for (TraderContactDto contact : traderContacts) {
                        if (null != recordDto.getTraderContactId()
                                && recordDto.getTraderContactId().equals(contact.getTraderContactId())) {
                            recordDto.setContactName(contact.getName());
                            recordDto.setTraderContactMobile(contact.getMobile());
                            recordDto.setTraderContactTelephone(contact.getTelephone());
                            StringBuffer sb = new StringBuffer();
                            //联系人联系方式
                            if (StringUtils.isNotBlank(contact.getTelephone()) && StringUtils.isNotBlank(contact.getMobile())) {
                                sb.append(contact.getTelephone()).append("|")
                                        .append(contact.getMobile());
                            } else {
                                if (StringUtils.isNotBlank(contact.getTelephone())) {
                                    sb.append(contact.getTelephone());
                                }
                                if (StringUtils.isNotBlank(contact.getMobile())) {
                                    sb.append(contact.getMobile());
                                }
                            }
                            recordDto.setPhone(sb.toString());

                            //部门
                            if (StringUtils.isNotBlank(contact.getDepartment())) {
                                recordDto.setContactDepartment(contact.getDepartment().length() > 5 ? contact.getDepartment().substring(0, 5) : contact.getDepartment());
                            }

                            //职位
                            if (StringUtils.isNotBlank(contact.getDepartment())) {
                                recordDto.setContactPosition(contact.getPosition().length() > 5 ? contact.getPosition().substring(0, 5) : contact.getPosition());
                            }

                            break;
                        }

                    }
                }

                //沟通目的，方法
                if (CollUtil.isNotEmpty(sysOptionDefinitionDtos)) {
                    for (SysOptionDefinitionDto definition : sysOptionDefinitionDtos) {
                        if (null != recordDto.getCommunicateGoal()
                                && recordDto.getCommunicateGoal().equals(definition.getSysOptionDefinitionId())) {
                            recordDto.setCommunicateGoalName(definition.getTitle());

                        }
                        if (null != recordDto.getCommunicateMode()
                                && recordDto.getCommunicateMode().equals(definition.getSysOptionDefinitionId())) {
                            recordDto.setCommunicateModeName(definition.getTitle());
                        }
                    }
                }
                // 标签处理
                queryTag(recordDto);

                // 处理不同沟通类型关联的ERP业务单号(不连表查)
                if (!Objects.isNull(typeToIdsMap.get(recordDto.getCommunicateType()))) {
                    typeToIdsMap.get(recordDto.getCommunicateType()).add(recordDto.getRelatedId());
                }

            }
            this.handleRecordRelatedOrderNo(communicateRecordDtoListBaseInfo, typeToIdsMap);
        }
        return dtoPageInfo;
    }

    /**
     * 查询沟通记录关联的ERP业务单号
     *
     * @param recordDtoList CommunicateRecordDto
     */
    private void handleRecordRelatedOrderNo(List<CommunicateRecordDto> recordDtoList, Map<Integer, List<Integer>> typeToIdsMap) {
        Map<Integer, Function<List<Integer>, List<Map<String, Object>>>> typeToMapperFuncMap = new HashMap<>();
        typeToMapperFuncMap.put(TraderConstant.ID_244, bussinessChanceMapper::getCommunicateChanceInfo);
        typeToMapperFuncMap.put(TraderConstant.ID_245, quoteorderMapper::getCommunicateQuoteInfo);
        typeToMapperFuncMap.put(TraderConstant.ID_246, saleOrderApiService::getCommunicateChanceInfo);
        typeToMapperFuncMap.put(TraderConstant.ID_247, buyorderInfoQueryService::getCommunicateBuyOrderInfo);
        typeToMapperFuncMap.put(TraderConstant.ID_248, afterSalesApiService::getCommunicateAfterSaleInfo);
        typeToMapperFuncMap.put(TraderConstant.ID_4083, businessLeadsMapper::getCommunicateLeadsInfo);
        typeToIdsMap.forEach((type, ids) -> {
            if (!ids.isEmpty()) {
                List<Map<String, Object>> orderList = typeToMapperFuncMap.get(type).apply(ids);
                if (orderList != null) {
                    orderList.forEach(order -> {
                        recordDtoList.stream()
                                .filter(c -> c.getCommunicateType().equals(type) && c.getRelatedId().equals(Integer.valueOf(order.get("RELATED_ID").toString())))
                                .forEach(c -> c.setOrderNo(order.get("ORDER_NO").toString()));
                    });
                }
            }
        });
    }

    /**
     * 查询 标签
     *
     * @param recordDto
     */
    private void queryTag(CommunicateRecordDto recordDto) {

        List<TagDto> communicateTags = traderTagMapper.getCommunicateTags(recordDto.getCommunicateRecordId());
        recordDto.setAllTag(communicateTags);
        if (CollUtil.isNotEmpty(communicateTags)) {
            List<TagDto> tagList = new ArrayList<>();
            List<TagDto> userTag = new ArrayList<>();
            for (TagDto tag : communicateTags) {

                if (tag.getIsRecommend() != null) {
                    if (tag.getIsRecommend()) {
                        tagList.add(tag);
                    } else {
                        userTag.add(tag);
                    }
                } else {
                    userTag.add(tag);
                }

            }

            recordDto.setSysTagUserCheck(tagList);

            if (!CollectionUtils.isEmpty(userTag)) {
                String[] strings = userTag.stream().map(TagDto::getTagName).toArray(String[]::new);
                recordDto.setUserTag(strings);
            } else {
                recordDto.setUserTag(new String[0]);
            }

            if (!CollectionUtils.isEmpty(tagList)) {
                Integer[] ids = tagList.stream().map(TagDto::getTagId).toArray(Integer[]::new);
                recordDto.setSysTag(ids);
            } else {
                recordDto.setSysTag(new Integer[0]);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(CommunicateRecordDto communicateRecordDto) {
        log.info("erp新增沟通记录，入参：{}", JSON.toJSONString(communicateRecordDto));
        ValidatorUtils.validate(communicateRecordDto, AddGroup.class);
        CommunicateRecordEntity communicateRecordEntity = communicateRecordConvertor.toEntity(communicateRecordDto);
        //兼容商机库没有客户也要新增沟通记录的需求VDERP-15440
        if (StringUtils.isNotBlank(communicateRecordDto.getTraderContactMobile())
                && (Objects.isNull(communicateRecordDto.getTraderId()) || 0 == communicateRecordDto.getTraderId())
                && StringUtils.isNotBlank(communicateRecordDto.getTraderContactNameNew())) {
            TraderContactEntity traderContactEntity = new TraderContactEntity();
            traderContactEntity.setName(communicateRecordDto.getTraderContactNameNew());
            traderContactEntity.setMobile(communicateRecordDto.getTraderContactMobile());
            traderContactEntity.setTraderId(0);
            traderContactDaoMapper.insertSelective(traderContactEntity);
            communicateRecordEntity.setTraderContactId(traderContactEntity.getTraderContactId());
        }
        communicateRecordEntity.setIsDone(communicateRecordEntity.getNextContactDate() != null ? 0 : 1);
        boolean hasTag = (Objects.nonNull(communicateRecordDto.getUserTag()) && (communicateRecordDto.getUserTag().length > 0)) || (Objects.nonNull(communicateRecordDto.getSysTag()) && communicateRecordDto.getSysTag().length > 0);
        if (hasTag) {
            communicateRecordEntity.setContactContent("1");
        }

        //VDERP-15625
        //销售管理-商机库/商机列表添加沟通记录中增加商机精准度
        if (communicateRecordDto.getBusinessChanceAccuracy() != null) {
            BussinessChanceEntity bussinessChanceEntity = new BussinessChanceEntity();
            bussinessChanceEntity.setBussinessChanceId(communicateRecordDto.getRelatedId());
            bussinessChanceEntity.setModTime(DateUtil.sysTimeMillis());
            bussinessChanceEntity.setBusinessChanceAccuracy(communicateRecordDto.getBusinessChanceAccuracy());
            bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChanceEntity);
        }
        log.info("erp新增沟通记录，落表：{}", JSON.toJSONString(communicateRecordEntity));
        communicateRecordMapper.insertSelective(communicateRecordEntity);

        //工作台-精准营销活动，需要更新活动-销售任务中的是否已沟通更新为已沟通。-Kerwin VDERP-15559
        marketPlanApiService.updateMarketPlanTraderSendMsg(communicateRecordEntity.getTraderId());

        CommunicateRecordEntity update = new CommunicateRecordEntity();
        update.setCommunicateType(communicateRecordDto.getCommunicateType());
        update.setNextContactDate(new Date());
        update.setRelatedId(communicateRecordDto.getRelatedId());
        update.setIsDone(1);
        communicateRecordMapper.updateIsDoneByNextContactDateAndRelatedIdAndCommunicateType(update);

        // 用户 自己添加的标签
        List<TagDto> tagTist = new ArrayList<>();
        if (Objects.nonNull(communicateRecordDto.getUserTag())) {
            if (communicateRecordDto.getUserTag().length > 0) {
                for (String name : communicateRecordDto.getUserTag()) {
                    TagDto tagDto = new TagDto();
                    tagDto.setTagType(32);
                    tagDto.setCompanyId(1);
                    tagDto.setTagName(name);
                    tagTist.add(tagDto);
                }
            }
            tagTist = tagService.batchAdd(tagTist);
        }

        // 系统标签 和用户标签 关联通话记录
        if (Objects.nonNull(communicateRecordDto.getSysTag())) {
            List<TraderTagEntity> list = new ArrayList<>();
            if (communicateRecordDto.getSysTag().length > 0) {
                for (Integer id : communicateRecordDto.getSysTag()) {
                    TraderTagEntity traderTagEntity = new TraderTagEntity();
                    traderTagEntity.setTagId(id);
                    traderTagEntity.setTraderId(communicateRecordEntity.getCommunicateRecordId());
                    traderTagEntity.setTraderType(3);
                    list.add(traderTagEntity);
                }
            }

            if (!CollectionUtils.isEmpty(tagTist)) {
                List<Integer> userTagIds = tagTist.stream()
                        .map(TagDto::getTagId).filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(userTagIds)) {
                    userTagIds.forEach(c -> {
                        TraderTagEntity traderTagEntity = new TraderTagEntity();
                        traderTagEntity.setTagId(c);
                        traderTagEntity.setTraderId(communicateRecordEntity.getCommunicateRecordId());
                        traderTagEntity.setTraderType(3);
                        list.add(traderTagEntity);
                    });
                }
            }
            if (!CollectionUtils.isEmpty(list)) {
                traderTagMapper.batchInsert(list);
            }
        }
    }

    @Override
    public CommunicateRecordDto getOne(CommunicateRecordDto communicateRecordDto) {
        CommunicateRecordEntity communicateRecordEntity = communicateRecordMapper.selectByPrimaryKey(communicateRecordDto);
        CommunicateRecordDto communicate = communicateRecordConvertor.toDto(communicateRecordEntity);
        if (Objects.nonNull(communicate)) {
            Integer belongUserId = communicate.getBelongUserId();
            if (Objects.nonNull(belongUserId)) {
                UserDto userBaseInfo = userApiService.getUserBaseInfo(belongUserId);
                communicate.setBelongPic(Objects.nonNull(userBaseInfo) ? userBaseInfo.getAliasHeadPicture() : "");
            }
            // 处理标签
            queryTag(communicate);
            // ai分析内容
            if (StrUtil.isNotBlank(communicate.getCoidUri())) {
                CommunicateVoiceTaskDto taskDto = communicateVoiceTaskApi.selectByCommunicateRecordIdAndSence(communicate.getCommunicateRecordId(),
                        AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode());
                if (Objects.nonNull(taskDto) && voiceStatusGptSuccess.equals(taskDto.getVoiceStatus())) {
                    List<VoiceFieldResultDto> voiceFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                            communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
                            AiConstant.CODE_GROUP_SUMMARY);
                    if (CollUtil.isNotEmpty(voiceFieldList)) {
                        communicate.setAiSummaryAnalysis(voiceFieldList.stream().map(d -> d.getFieldName() + ":" + (StrUtil.isBlank(d.getFieldResult()) ? "" : d.getFieldResult())).collect(Collectors.joining("\n")));
                    }
                    List<VoiceFieldResultDto> voiceToDoFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                            communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
                            AiConstant.CODE_GROUP_TODOTASK);
                    if (CollUtil.isNotEmpty(voiceToDoFieldList)) {
                        communicate.setAiTodoAnalysis(voiceToDoFieldList.stream().map(d -> d.getFieldName() + ":" + (StrUtil.isBlank(d.getFieldResult()) ? "" : d.getFieldResult())).collect(Collectors.joining("\n")));
                    }
                }
            }
        }
        return communicate;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(CommunicateRecordDto communicateRecordDto) {

        ValidatorUtils.validate(communicateRecordDto, UpdateGroup.class);
        CommunicateRecordEntity communicateRecordEntity = communicateRecordConvertor.toEntity(communicateRecordDto);
        communicateRecordEntity.setIsDone(communicateRecordEntity.getNextContactDate() != null ? 0 : 1);
        boolean hasTag = (Objects.nonNull(communicateRecordDto.getUserTag()) && (communicateRecordDto.getUserTag().length > 0)) || (Objects.nonNull(communicateRecordDto.getSysTag()) && communicateRecordDto.getSysTag().length > 0);
        if (hasTag) {
            communicateRecordEntity.setContactContent("1");
        }
        communicateRecordMapper.updateByPrimaryKeySelective(communicateRecordEntity);

        // 用户 自己添加的标签
        List<TagDto> tagTist = new ArrayList<>();
        if (Objects.nonNull(communicateRecordDto.getUserTag())) {
            if (communicateRecordDto.getUserTag().length > 0) {

                for (String name : communicateRecordDto.getUserTag()) {
                    TagDto tagDto = new TagDto();
                    tagDto.setTagType(32);
                    tagDto.setCompanyId(1);
                    tagDto.setTagName(name);
                    tagTist.add(tagDto);
                }
            }
            tagTist = tagService.batchAdd(tagTist);
        }

        // 系统标签 和用户标签 关联通话记录
        if (Objects.nonNull(communicateRecordDto.getSysTag())) {
            List<TraderTagEntity> list = new ArrayList<>();
            if (communicateRecordDto.getSysTag().length > 0) {
                for (Integer id : communicateRecordDto.getSysTag()) {
                    TraderTagEntity traderTagEntity = new TraderTagEntity();
                    traderTagEntity.setTagId(id);
                    traderTagEntity.setTraderId(communicateRecordEntity.getCommunicateRecordId());
                    traderTagEntity.setTraderType(3);
                    list.add(traderTagEntity);
                }
            }

            if (!CollectionUtils.isEmpty(tagTist)) {
                List<Integer> userTagIds = tagTist.stream()
                        .map(TagDto::getTagId).filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(userTagIds)) {
                    userTagIds.forEach(c -> {
                        TraderTagEntity traderTagEntity = new TraderTagEntity();
                        traderTagEntity.setTagId(c);
                        traderTagEntity.setTraderId(communicateRecordEntity.getCommunicateRecordId());
                        traderTagEntity.setTraderType(3);
                        list.add(traderTagEntity);
                    });
                }
            }
            if (!CollectionUtils.isEmpty(list)) {
                TraderTagEntity update = new TraderTagEntity();
                update.setTraderType(3);
                update.setTraderId(communicateRecordEntity.getCommunicateRecordId());
                traderTagMapper.deleteByTraderIdAndTraderType(update);
                traderTagMapper.batchInsert(list);
            }
        }

    }

    @Override
    public Integer selectByRelatedIdAndCommunicateType(CommunicateRecordDto communicateRecordDto) {
        if (Objects.isNull(communicateRecordDto)) {
            return null;
        }
        return communicateRecordMapper.selectByRelatedIdAndCommunicateType(communicateRecordDto);
    }

    @Override
    public List<CommunicateRecordDto> findByBegintimeBetween(Long minBegintime, Long maxBegintime) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        List<CommunicateRecordEntity> byBegintimeBetween = communicateRecordMapper.findByAddTimeBetweenAndCreator(minBegintime, maxBegintime, currentUser.getId());
        return communicateRecordConvertor.toDto(byBegintimeBetween);
    }

	@Override
	public CommunicateTelRecordDto getTelList(PageParam<CommunicateTelRecordParams> communicateTelRecordParamsPage) {
		Page<CommunicateTelRecord> page = PageHelper.startPage(communicateTelRecordParamsPage);
		CommunicateTelRecordParams  communicateTelRecordParams  = communicateTelRecordParamsPage.getParam();
		log.info("===================startTime:{}",System.currentTimeMillis());
		List<CommunicateTelRecord>  communicateTelRecordList = communicateRecordMapper.getTelList(communicateTelRecordParams);
		log.info("====================endTime:{}",System.currentTimeMillis());
		for (CommunicateTelRecord communicateTelRecord : communicateTelRecordList) {
			String contact = communicateTelRecord.getContact();
			String contactName = contact;
			String contactNickName = "";
			String contactNameRegex = ".*[：:](.*?)[）)]+$"; 
		    String contactNickNameRegex = "(.*?)(?=[（(])"; 
		    if (StringUtils.isNotBlank(contact) && (contact.contains("(") || contact.contains("（"))) {
	            try {
	                Pattern namePattern = Pattern.compile(contactNameRegex);
	                Matcher nameMatcher = namePattern.matcher(contact);
	                if (nameMatcher.find()) {
	                    contactName = nameMatcher.group(1).trim();  
	                }
	            } catch (Exception e) {
	                log.warn("灵犀调用通话记录，解析用户名时异常，检查源数据拼接格式是否发生变更");
	            }

	            try {
	                Pattern nicknamePattern = Pattern.compile(contactNickNameRegex);
	                Matcher nicknameMatcher = nicknamePattern.matcher(contact);
	                if (nicknameMatcher.find()) {
	                    contactNickName = nicknameMatcher.group(1).trim();  
	                }
	            } catch (Exception e) {
	                log.warn("灵犀调用通话记录，解析昵称时异常，检查源数据拼接格式是否发生变更");
	            }
	        }
		    if(Objects.nonNull(communicateTelRecord.getCoidUri())){
		    	communicateTelRecord.setAiWindowUrl(erpUrl+"/system/call/getrecordplayForAi.do?communicateRecordId="+communicateTelRecord.getCommunicateRecordId());
            }
		    String sphone=getShortPhone(communicateTelRecord.getPhone());
		    //归属地设置
			qzone(communicateTelRecord, sphone);
		    communicateTelRecord.setPhone(sphone);;
		    communicateTelRecord.setContactName(contactName);
		    communicateTelRecord.setContactNickName(contactNickName);
		    communicateTelRecord.setCreatorAvatarUrl(sphone);
		}
		CommunicateTelRecordDto communicateTelRecordDto = new CommunicateTelRecordDto();
		communicateTelRecordDto.setCommunicateTelRecordList(communicateTelRecordList);
		Integer total = Integer.parseInt(String.valueOf(page.getTotal()));
		communicateTelRecordDto.setTotal(total);		
		return communicateTelRecordDto;
	}

	private void qzone(CommunicateTelRecord c, String sphone) {
		//号码归属地
		String regEx = "1[345789]{1}\\d{9}$";
		// 编译正则表达式
		Pattern pattern = Pattern.compile(regEx);
		Matcher matcher = pattern.matcher(sphone);
		if (matcher.matches()) {
			// 电话

			String phoneStr = sphone.substring(0, 7);
			QCellCore qCellCoreByPhone = this.getQCellCoreByPhone(phoneStr);
			if(null != qCellCoreByPhone){
				c.setPhoneArea(qCellCoreByPhone.getProvince()+qCellCoreByPhone.getCity());
			}
		} else if (c.getPhone().length() == 8) {
			c.setPhoneArea("江苏南京");
		}else{
			QCellCore qCellCoreByCode = null;
			if(null != c.getPhone() && c.getPhone().length() > 4){
				String code = c.getPhone().substring(0, 4);
				qCellCoreByCode = this.getQCellCoreByCode(code);
			}
			if(null != qCellCoreByCode){
				c.setPhoneArea(qCellCoreByCode.getProvince()+qCellCoreByCode.getCity());
			}else{
				if(null != c.getPhone() && c.getPhone().length() > 3){
					String code2 = c.getPhone().substring(0, 3);
					QCellCore qCellCoreByCode2 = this.getQCellCoreByCode(code2);
					if(null != qCellCoreByCode2){
						c.setPhoneArea(qCellCoreByCode2.getProvince()+qCellCoreByCode2.getCity());
					}
				}
			}
		}
	}
	
	private QCellCore getQCellCoreByPhone(String phone) {
		return qCellCoreMapper.getQCellCoreByPhone(phone);
	}
	
	public QCellCore getQCellCoreByCode(String code) {
		return qCellCoreMapper.getQCellCoreByCode(code);
	}

	/**
	 * 截取前缀
	 * @param orginPhone 0315366165999
	 * @return  15366165999
	 */
	private String getShortPhone(String orginPhone) {
		if(orginPhone==null){
			return "";
		}
		List<String> phonePrefixArrayList = JSON.parseArray(phonePrefixArray, String.class);
		String phoneNumber = orginPhone;
		Optional<String> optional = phonePrefixArrayList.stream().filter(item -> phoneNumber.startsWith(item)).findFirst();
		if (optional.isPresent()){
			orginPhone=orginPhone.substring(2);
		}
		return orginPhone;
	}

	@Override
	@Transactional
	public List<Integer> followBindingTel(FollowBindingTelParams followBindingTelParams) {
		List<Integer> communicateRecordCanNotBindingIdList = new ArrayList<>();
		List<Integer> communicateRecordIdList = followBindingTelParams.getCommunicateRecordIdList();
		List<CommunicateRecordEntity> communicateRecordEntityList = communicateRecordMapper.findByCommunicateRecordIdIn(communicateRecordIdList);
		for (CommunicateRecordEntity communicateRecordEntity : communicateRecordEntityList) {
			if(communicateRecordEntity.getRelatedId() != 0) {
				communicateRecordCanNotBindingIdList.add(communicateRecordEntity.getCommunicateRecordId());
			}else {
				//对沟通记录进行绑定
				CommunicateRecordEntity communicateRecordEntityUpdate = new CommunicateRecordEntity();
				communicateRecordEntityUpdate.setCommunicateRecordId(communicateRecordEntity.getCommunicateRecordId());
				communicateRecordEntityUpdate.setModTime(new Date().getTime()); 
				communicateRecordEntityUpdate.setCommunicateType(followBindingTelParams.getCommunicateType());				
				communicateRecordEntityUpdate.setRelatedId(followBindingTelParams.getRelatedId());
				int success = communicateRecordMapper.updateByPrimaryKeySelective(communicateRecordEntityUpdate);
				if(success<1) {
					//更新失败，按已绑定处理
					communicateRecordCanNotBindingIdList.add(communicateRecordEntity.getCommunicateRecordId());
				}
			}
		}
		return communicateRecordCanNotBindingIdList;
	}
}
