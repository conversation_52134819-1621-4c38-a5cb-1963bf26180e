package com.vedeng.trader.group.model;

import com.vedeng.trader.model.Period;

import java.math.BigDecimal;

public class SaleorderSum {
    private Period period;
    private BigDecimal lowAmount;
    private BigDecimal highAmount;

    private Integer lowTimes;
    private Integer highTimes;

    public Period getPeriod() {
        return period;
    }

    public void setPeriod(Period period) {
        this.period = period;
    }

    public BigDecimal getLowAmount() {
        return lowAmount;
    }

    public void setLowAmount(BigDecimal lowAmount) {
        this.lowAmount = lowAmount;
    }

    public BigDecimal getHighAmount() {
        return highAmount;
    }

    public void setHighAmount(BigDecimal highAmount) {
        this.highAmount = highAmount;
    }

    public Integer getLowTimes() {
        return lowTimes;
    }

    public void setLowTimes(Integer lowTimes) {
        this.lowTimes = lowTimes;
    }

    public Integer getHighTimes() {
        return highTimes;
    }

    public void setHighTimes(Integer highTimes) {
        this.highTimes = highTimes;
    }
}
