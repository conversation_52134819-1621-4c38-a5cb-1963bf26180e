package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeExpressCostCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressCostCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressCostConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeExpressCostRepository;
import com.vedeng.erp.kingdee.service.KingDeeExpressCostApiService;
import com.vedeng.erp.kingdee.service.KingDeeExpressCostService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KingDeeExpressCostServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeExpressCostEntity,
        KingDeeExpressCostDto,
        KingDeeExpressCostCommand,
        KingDeeExpressCostRepository,
        KingDeeExpressCostConvertor,
        KingDeeExpressCostCommandConvertor
        > implements KingDeeExpressCostService,KingDeeExpressCostApiService {
    @Override
    public boolean getIsAutoSubmitAndAudit(KingDeeExpressCostDto kingDeeExpressCostDto){
        return Boolean.FALSE;
    }
}
