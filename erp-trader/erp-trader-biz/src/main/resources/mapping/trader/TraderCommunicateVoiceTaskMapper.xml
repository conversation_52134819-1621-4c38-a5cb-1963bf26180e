<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCommunicateVoiceTaskMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.CommunicateVoiceTaskEntity">
    <!--@mbg.generated-->
    <!--@Table T_COMMUNICATE_VOICE_TASK-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId" />
    <result column="COID_LENGTH" jdbcType="INTEGER" property="coidLength" />
    <result column="COID_URI" jdbcType="VARCHAR" property="coidUri" />
    <result column="VOICE_STATUS" jdbcType="VARCHAR" property="voiceStatus" />
    <result column="VOICE_STATUS_REQUEST_URI" jdbcType="VARCHAR" property="voiceStatusRequestUri" />
    <result column="UPLOAD_TIMESTAMP" jdbcType="BIGINT" property="uploadTimestamp" />
    <result column="VOICE_TIMESTAMP" jdbcType="BIGINT" property="voiceTimestamp" />
    <result column="GPT_TIMESTAMP" jdbcType="BIGINT" property="gptTimestamp" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="VOICE_TEXT_ORDER" jdbcType="LONGVARCHAR" property="voiceTextOrder" />
    <result column="VOICE_TEXT_PARSE" jdbcType="LONGVARCHAR" property="voiceTextParse" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="PROMPT_TOKENS" jdbcType="BIGINT" property="promptTokens" />
    <result column="COMPLETION_TOKENS" jdbcType="BIGINT" property="completionTokens" />
    <result column="TOTAL_TOKENS" jdbcType="BIGINT" property="totalTokens" />
    <result column="SENCE_CODE" jdbcType="VARCHAR" property="senceCode" />
    <result column="GPT_VERSION" jdbcType="VARCHAR" property="gptVersion" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, COMMUNICATE_RECORD_ID, COID_LENGTH, COID_URI, VOICE_STATUS, VOICE_STATUS_REQUEST_URI, 
    UPLOAD_TIMESTAMP, VOICE_TIMESTAMP, GPT_TIMESTAMP, ADD_TIME, MOD_TIME, REMARK, VOICE_TEXT_ORDER, 
    VOICE_TEXT_PARSE, ORDER_ID, PROMPT_TOKENS, COMPLETION_TOKENS, TOTAL_TOKENS, SENCE_CODE, 
    GPT_VERSION
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_COMMUNICATE_VOICE_TASK
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_COMMUNICATE_VOICE_TASK
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateVoiceTaskEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_COMMUNICATE_VOICE_TASK (COMMUNICATE_RECORD_ID, COID_LENGTH, 
      COID_URI, VOICE_STATUS, VOICE_STATUS_REQUEST_URI, 
      UPLOAD_TIMESTAMP, VOICE_TIMESTAMP, GPT_TIMESTAMP, 
      ADD_TIME, MOD_TIME, REMARK, 
      VOICE_TEXT_ORDER, VOICE_TEXT_PARSE, 
      ORDER_ID, PROMPT_TOKENS, COMPLETION_TOKENS, 
      TOTAL_TOKENS, SENCE_CODE, GPT_VERSION
      )
    values (#{communicateRecordId,jdbcType=INTEGER}, #{coidLength,jdbcType=INTEGER}, 
      #{coidUri,jdbcType=VARCHAR}, #{voiceStatus,jdbcType=VARCHAR}, #{voiceStatusRequestUri,jdbcType=VARCHAR}, 
      #{uploadTimestamp,jdbcType=BIGINT}, #{voiceTimestamp,jdbcType=BIGINT}, #{gptTimestamp,jdbcType=BIGINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{voiceTextOrder,jdbcType=LONGVARCHAR}, #{voiceTextParse,jdbcType=LONGVARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{promptTokens,jdbcType=BIGINT}, #{completionTokens,jdbcType=BIGINT}, 
      #{totalTokens,jdbcType=BIGINT}, #{senceCode,jdbcType=VARCHAR}, #{gptVersion,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateVoiceTaskEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_COMMUNICATE_VOICE_TASK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID,
      </if>
      <if test="coidLength != null">
        COID_LENGTH,
      </if>
      <if test="coidUri != null and coidUri != ''">
        COID_URI,
      </if>
      <if test="voiceStatus != null and voiceStatus != ''">
        VOICE_STATUS,
      </if>
      <if test="voiceStatusRequestUri != null and voiceStatusRequestUri != ''">
        VOICE_STATUS_REQUEST_URI,
      </if>
      <if test="uploadTimestamp != null">
        UPLOAD_TIMESTAMP,
      </if>
      <if test="voiceTimestamp != null">
        VOICE_TIMESTAMP,
      </if>
      <if test="gptTimestamp != null">
        GPT_TIMESTAMP,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="remark != null and remark != ''">
        REMARK,
      </if>
      <if test="voiceTextOrder != null and voiceTextOrder != ''">
        VOICE_TEXT_ORDER,
      </if>
      <if test="voiceTextParse != null and voiceTextParse != ''">
        VOICE_TEXT_PARSE,
      </if>
      <if test="orderId != null and orderId != ''">
        ORDER_ID,
      </if>
      <if test="promptTokens != null">
        PROMPT_TOKENS,
      </if>
      <if test="completionTokens != null">
        COMPLETION_TOKENS,
      </if>
      <if test="totalTokens != null">
        TOTAL_TOKENS,
      </if>
      <if test="senceCode != null and senceCode != ''">
        SENCE_CODE,
      </if>
      <if test="gptVersion != null and gptVersion != ''">
        GPT_VERSION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="communicateRecordId != null">
        #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="coidLength != null">
        #{coidLength,jdbcType=INTEGER},
      </if>
      <if test="coidUri != null and coidUri != ''">
        #{coidUri,jdbcType=VARCHAR},
      </if>
      <if test="voiceStatus != null and voiceStatus != ''">
        #{voiceStatus,jdbcType=VARCHAR},
      </if>
      <if test="voiceStatusRequestUri != null and voiceStatusRequestUri != ''">
        #{voiceStatusRequestUri,jdbcType=VARCHAR},
      </if>
      <if test="uploadTimestamp != null">
        #{uploadTimestamp,jdbcType=BIGINT},
      </if>
      <if test="voiceTimestamp != null">
        #{voiceTimestamp,jdbcType=BIGINT},
      </if>
      <if test="gptTimestamp != null">
        #{gptTimestamp,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="voiceTextOrder != null and voiceTextOrder != ''">
        #{voiceTextOrder,jdbcType=LONGVARCHAR},
      </if>
      <if test="voiceTextParse != null and voiceTextParse != ''">
        #{voiceTextParse,jdbcType=LONGVARCHAR},
      </if>
      <if test="orderId != null and orderId != ''">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="promptTokens != null">
        #{promptTokens,jdbcType=BIGINT},
      </if>
      <if test="completionTokens != null">
        #{completionTokens,jdbcType=BIGINT},
      </if>
      <if test="totalTokens != null">
        #{totalTokens,jdbcType=BIGINT},
      </if>
      <if test="senceCode != null and senceCode != ''">
        #{senceCode,jdbcType=VARCHAR},
      </if>
      <if test="gptVersion != null and gptVersion != ''">
        #{gptVersion,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateVoiceTaskEntity">
    <!--@mbg.generated-->
    update T_COMMUNICATE_VOICE_TASK
    <set>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="coidLength != null">
        COID_LENGTH = #{coidLength,jdbcType=INTEGER},
      </if>
      <if test="coidUri != null and coidUri != ''">
        COID_URI = #{coidUri,jdbcType=VARCHAR},
      </if>
      <if test="voiceStatus != null and voiceStatus != ''">
        VOICE_STATUS = #{voiceStatus,jdbcType=VARCHAR},
      </if>
      <if test="voiceStatusRequestUri != null and voiceStatusRequestUri != ''">
        VOICE_STATUS_REQUEST_URI = #{voiceStatusRequestUri,jdbcType=VARCHAR},
      </if>
      <if test="uploadTimestamp != null">
        UPLOAD_TIMESTAMP = #{uploadTimestamp,jdbcType=BIGINT},
      </if>
      <if test="voiceTimestamp != null">
        VOICE_TIMESTAMP = #{voiceTimestamp,jdbcType=BIGINT},
      </if>
      <if test="gptTimestamp != null">
        GPT_TIMESTAMP = #{gptTimestamp,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="voiceTextOrder != null and voiceTextOrder != ''">
        VOICE_TEXT_ORDER = #{voiceTextOrder,jdbcType=LONGVARCHAR},
      </if>
      <if test="voiceTextParse != null and voiceTextParse != ''">
        VOICE_TEXT_PARSE = #{voiceTextParse,jdbcType=LONGVARCHAR},
      </if>
      <if test="orderId != null and orderId != ''">
        ORDER_ID = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="promptTokens != null">
        PROMPT_TOKENS = #{promptTokens,jdbcType=BIGINT},
      </if>
      <if test="completionTokens != null">
        COMPLETION_TOKENS = #{completionTokens,jdbcType=BIGINT},
      </if>
      <if test="totalTokens != null">
        TOTAL_TOKENS = #{totalTokens,jdbcType=BIGINT},
      </if>
      <if test="senceCode != null and senceCode != ''">
        SENCE_CODE = #{senceCode,jdbcType=VARCHAR},
      </if>
      <if test="gptVersion != null and gptVersion != ''">
        GPT_VERSION = #{gptVersion,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateVoiceTaskEntity">
    <!--@mbg.generated-->
    update T_COMMUNICATE_VOICE_TASK
    set COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      COID_LENGTH = #{coidLength,jdbcType=INTEGER},
      COID_URI = #{coidUri,jdbcType=VARCHAR},
      VOICE_STATUS = #{voiceStatus,jdbcType=VARCHAR},
      VOICE_STATUS_REQUEST_URI = #{voiceStatusRequestUri,jdbcType=VARCHAR},
      UPLOAD_TIMESTAMP = #{uploadTimestamp,jdbcType=BIGINT},
      VOICE_TIMESTAMP = #{voiceTimestamp,jdbcType=BIGINT},
      GPT_TIMESTAMP = #{gptTimestamp,jdbcType=BIGINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      VOICE_TEXT_ORDER = #{voiceTextOrder,jdbcType=LONGVARCHAR},
      VOICE_TEXT_PARSE = #{voiceTextParse,jdbcType=LONGVARCHAR},
      ORDER_ID = #{orderId,jdbcType=VARCHAR},
      PROMPT_TOKENS = #{promptTokens,jdbcType=BIGINT},
      COMPLETION_TOKENS = #{completionTokens,jdbcType=BIGINT},
      TOTAL_TOKENS = #{totalTokens,jdbcType=BIGINT},
      SENCE_CODE = #{senceCode,jdbcType=VARCHAR},
      GPT_VERSION = #{gptVersion,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=BIGINT}
  </update>

  <select id="getCommunicateVoiceTaskByTaskId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_COMMUNICATE_VOICE_TASK
    where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
    LIMIT 1
  </select>

  <select id="getCommunicateVoiceTaskByTaskIdAndSence" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_COMMUNICATE_VOICE_TASK
    where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
    AND SENCE_CODE = #{sence,jdbcType=VARCHAR}
    LIMIT 1
  </select>

</mapper>