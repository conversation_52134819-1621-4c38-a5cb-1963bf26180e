package com.vedeng.order.service.impl;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.orderstrategy.BuyorderStrategyContext;
import com.vedeng.common.orderstrategy.StrategyContext;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.OrderCommonService;
import org.activiti.editor.language.json.converter.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class OrderCommonServiceImpl implements OrderCommonService {

    private final static Logger LOGGER = LoggerFactory.getLogger(OrderCommonServiceImpl.class);

    private static final ExecutorService EXECUTOR = new ThreadPoolExecutor(2, 2,
            0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(1024),
            new ThreadFactoryBuilder().setNameFormat("updateOrderDataTime-pool-%d").build(), new ThreadPoolExecutor.AbortPolicy());

    @Autowired
    private BuyorderStrategyContext buyorderStrategyContext;
    @Resource
    private BuyorderMapper buyorderMapper;
    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;
    @Resource
    private SaleorderMapper saleorderMapper;
    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;
    @Resource
    private AfterSalesMapper afterSalesMapper;
    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private AfterSalesService afterSalesService;

    @Override
    public void updateBuyOrderDataUpdateTime(Integer orderId, Integer orderDetailId, String operateType) {
        EXECUTOR.execute(() -> {
            LOGGER.info("采购单更新updataTime info:{}", orderId + "," + orderDetailId + "," + operateType);
            if (orderId == null && orderDetailId == null) {
                return;
            }
            try {
                if (null != orderId) {
                    buyorderMapper.updateDataTimeByOrderId(orderId);

                    List<BuyorderGoodsVo> buyorderGoodsList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIds(orderId);
                    for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsList) {
                        buyorderGoodsMapper.updateDataTimeByDetailId(buyorderGoodsVo.getBuyorderGoodsId());
                    }
                } else if (orderDetailId != null) {
                    buyorderMapper.updateDataTimeByDetailId(orderDetailId);
                    buyorderGoodsMapper.updateDataTimeByDetailId(orderDetailId);
                }

                //采购单关联订单更新订单
                if (operateType.equals(OrderDataUpdateConstant.BUY_VP_ORDER_GENERATE)) {
                    List<Integer> saleorderGoodsIdList = buyorderMapper.getSaleorderGooodsIdByBuyorderId(orderId);
                    if (CollectionUtils.isNotEmpty(saleorderGoodsIdList)) {
                        for (Integer saleGoodsId : saleorderGoodsIdList) {
                            updateSaleOrderDataUpdateTime(null, saleGoodsId, OrderDataUpdateConstant.SALE_ORDER_BUYORDER_OPT);
                        }
                    }
                }
                //执行采购订单金额计算策略
                buyorderStrategyContext.executeAll(buyorderStrategyContext.add(BuyorderStrategyContext.BUYORDER_AMOUNT_STRATEGY, operateType), orderId);
            } catch (Exception e) {
                LOGGER.error("采购单更新updataTime orderId:" + orderId + ",orderDetailId:" + orderDetailId + ",operateType:" + operateType + "error", e);
            }
        });
    }


    @Override
    public void updateSaleOrderDataUpdateTime(Integer orderId, Integer orderDetailId, String operateType) {
        EXECUTOR.execute(() -> {
            LOGGER.info("销售单更新updataTime info:{}", orderId + "," + orderDetailId + "," + operateType);
            try {
                //如果是订单维度更新则订单和订单商品表一起更新
                //如果是订单商品更新则更新订单和相应商品
                if (orderId == null && orderDetailId == null) {
                    return;
                }
                if (orderId != null && orderDetailId == null) {
                    //更新订单和商品
                    saleorderMapper.updateDataTimeByOrderId(orderId);
                    List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getGoodsPriceList(orderId);
                    for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
                        saleorderGoodsMapper.updateDataTimeByDetailId(saleorderGoods.getSaleorderGoodsId());
                    }
//						saleorderGoodsMapper.updateDataTimeByOrderId(orderId);
                } else if (orderDetailId != null) {
                    //更新订单和当前商品
                    saleorderGoodsMapper.updateDataTimeByDetailId(orderDetailId);
                    saleorderMapper.updateDataTimeByDetailId(orderDetailId);
                }
                Integer saleorderId = orderId;
                if (orderId == null && orderDetailId != null) {
                    SaleorderGoods saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsInfoById(orderDetailId);
                    saleorderId = saleorderGoods.getSaleorderId();
                }
                StrategyContext strategyContext = new StrategyContext();
                //执行订单金额计算策略
                strategyContext.add(StrategyContext.AMOUNT_STRATEGY, operateType);
                //推送VS订单到前台PC
                //strategyContext.add(StrategyContext.PUSH_PC_STRATEGY,operateType);
                strategyContext.executeAll(saleorderId);

            } catch (Exception e) {
                LOGGER.error("销售单更新updataTime orderId:" + orderId + ",orderDetailId:" + orderDetailId + ",operateType:" + operateType + "error", e);
            }
        });
    }


    @Override
    public void updateAfterOrderDataUpdateTime(Integer afterSaleOrderId, Integer afterSaleOrderDetailId, String operateType) {
        EXECUTOR.execute(() -> {
            LOGGER.info("售后单更新updataTime info1:{}", afterSaleOrderId + "," + afterSaleOrderDetailId + "," + operateType);

            try {
                AfterSales afterSales = null;
                if (afterSaleOrderId == null && afterSaleOrderDetailId == null) {
                    return;
                }
                if (null != afterSaleOrderId) {
                    LOGGER.info("售后单更新updataTime info2:{}", afterSaleOrderId + "," + operateType);
                    afterSalesMapper.updateDataTimeByOrderId(afterSaleOrderId);
                    afterSalesGoodsMapper.updateDataTimeByOrderId(afterSaleOrderId);
                    afterSales = afterSalesMapper.getAfterSalesById(afterSaleOrderId);
                } else if (afterSaleOrderDetailId != null) {
                    LOGGER.info("售后单更新updataTime info3:{}", afterSaleOrderDetailId + "," + operateType);
                    afterSalesMapper.updateDataTimeByDetailId(afterSaleOrderDetailId);
                    afterSalesGoodsMapper.updateDataTimeByDetailId(afterSaleOrderDetailId);
                    afterSales = afterSalesMapper.getAfterSalesTypeByAfterSalesGoodsId(afterSaleOrderDetailId);
                }

                //subjectType 535 销售 type 542 销售订单退票  540 销售订单换货  539 销售订单退货  543 销售订单退款
                //subjectType 536 采购 type 546 采购订单退货  547 采购订单换货  3321 未履约赔付   3322送货投诉
                //3323 安装投诉 3324 维修投诉
                if (afterSales != null) {
                    Integer subjectType = afterSales.getSubjectType();
                    Integer type = afterSales.getType();
                    if (subjectType.equals(535)) {
                        if (type.equals(542) || type.equals(540) || type.equals(539) || type.equals(543)) {
                            updateSaleOrderDataUpdateTime(afterSales.getOrderId(), null, OrderDataUpdateConstant.SALE_ORDER_AFTERODER_OPT);
                        }
                    } else if (subjectType.equals(536)) {
                        if (type.equals(546) || type.equals(547)) {
                            updateBuyOrderDataUpdateTime(afterSales.getOrderId(), null, OrderDataUpdateConstant.BUY_ORDER_AFTERORDER_OPY);
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.error("售后单更新updataTime afterSaleOrderId:" + afterSaleOrderId + ",afterSaleOrderDetailId:" + afterSaleOrderDetailId + ",operateType:" + operateType + "error", e);
            }
        });
    }

    @PreDestroy
    public void destroy() {
        EXECUTOR.shutdown();
    }
}
