<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>通话记录（总机）</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="hOpA1LytBLU" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">手机号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="PHONE" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">拨号时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="FROM_UNIXTIME(A.ADD_TIME/1000)" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="ID" name="ID" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">ID</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">拨号人</th>
      <th item_name="PHONE" name="PHONE" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">手机号</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">拨号时间</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by A.ADD_TIME desc" groupby="">

       select A.COMMUNICATE_RECORD_ID ID,FROM_UNIXTIME(A.ADD_TIME/1000) ADD_TIME,
       B.USERNAME,A.PHONE
from T_COMMUNICATE_RECORD_CALLCENTER A LEFT JOIN T_USER B ON A.CREATOR=B.USER_ID
where  FROM_UNIXTIME(A.ADD_TIME/1000) > DATE_SUB(NOW(),INTERVAL 1 WEEK)
      </pre> count: <pre id="count" class="layui-code">SELECT 10000</pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>