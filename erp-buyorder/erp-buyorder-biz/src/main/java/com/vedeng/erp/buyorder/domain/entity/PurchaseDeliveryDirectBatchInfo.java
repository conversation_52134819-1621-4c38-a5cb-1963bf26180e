package com.vedeng.erp.buyorder.domain.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
 * <AUTHOR>
public class PurchaseDeliveryDirectBatchInfo implements Serializable {
    private Integer purchaseDeliveryDirectBatchInfoId;

    /**
     * 采购单ID
     */
    private Integer buyorderId;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 更新者
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;

    public Integer getPurchaseDeliveryDirectBatchInfoId() {
        return purchaseDeliveryDirectBatchInfoId;
    }

    public void setPurchaseDeliveryDirectBatchInfoId(Integer purchaseDeliveryDirectBatchInfoId) {
        this.purchaseDeliveryDirectBatchInfoId = purchaseDeliveryDirectBatchInfoId;
    }

    public Integer getBuyorderId() {
        return buyorderId;
    }

    public void setBuyorderId(Integer buyorderId) {
        this.buyorderId = buyorderId;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}