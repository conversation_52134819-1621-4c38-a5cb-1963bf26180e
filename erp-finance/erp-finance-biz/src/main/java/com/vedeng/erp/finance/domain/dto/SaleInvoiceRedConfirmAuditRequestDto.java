package com.vedeng.erp.finance.domain.dto;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.utils.TaxesUtil;
import lombok.Data;

/**
 * 红字确认单审核入参
 */
@Data
public class SaleInvoiceRedConfirmAuditRequestDto implements ITaxesParam {

    /**
     * 纳税人识别号
     */
    private String nsrsbh;

    /**
     * 红字信息表确认单id
     */
    private String uuid;

    /**
     * Y-同意；N-拒绝
     */
    private String qrlx;

    public SaleInvoiceRedConfirmAuditRequestDto() {
        this.nsrsbh = TaxesUtil.taxesConfig.taxNo;
    }
}
