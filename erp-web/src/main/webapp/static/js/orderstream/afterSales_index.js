$(function () {
    $('.J-line-wrap').each(function () {
        var $lineWrap = $(this);
        var lineData = $lineWrap.data('json');

        $.each(lineData, function (i, item) {
            var status = item.status || 0;
            var statusClass = ['', 't-line-done', 't-line-doing', 't-line-async'][status];
            var jumpDom = item.jump ? 'data-jump="' + item.jump + '"' : '';
            var tipDom = item.tip ? '<div class="t-line-item-tip">' + item.tip + '</div>' : '';
            var lockDom = item.lock ? '<div class="t-line-lock"><i class="lock-icon"></i><div class="t-line-lock-txt">'+ item.lock +'</div></div>' : '';

            $lineWrap.append(
                '<div class="t-line-item ' + statusClass + '" '+ jumpDom +'>' +
                '<div class="t-line-item-icon">'  + '</div>' +
                '<div class="t-line-item-cnt">' +
                '<div class="t-line-item-txt">' + item.label + '</div>' +
                tipDom +
                lockDom +
                '</div>' +
                '</div>'
            )

            if(item.fail ===1){
                $lineWrap.addClass('red-line');
            }
        })

        $('[data-jump]', $lineWrap).click(function () {
            var id = $(this).data('jump');
            console.log(id);
            var top = $(id).offset().top;

            $('html,body').animate({ scrollTop: top - 50 }, 300);
        })
    })

})