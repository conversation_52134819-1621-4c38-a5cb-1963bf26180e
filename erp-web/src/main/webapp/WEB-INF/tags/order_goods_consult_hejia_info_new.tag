<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="item" type="java.lang.Object" required="true" %>
<%@attribute name="userList" type="java.util.List" required="false" %>
<%@attribute name="skuNoAndPriceMap" type="java.util.Map" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<c:set var="tagNameKey" value="${item.sku}_tagName" />
<c:set var="priceKey" value="${item.sku}_price" />
<div class="detail-info-field width2">
    <c:if test="${ not empty skuNoAndPriceMap[item.sku]}">
    <div class="detail-info-label">${skuNoAndPriceMap[tagNameKey]}核价参考：</div>
    <div class="detail-info-txt">
        ￥${skuNoAndPriceMap[priceKey]}
    </div>
    </c:if>
    <c:if test="${empty skuNoAndPriceMap[item.sku]}">
        <div class="detail-info-label">${skuNoAndPriceMap[tagNameKey]}核价参考：</div>
        <div class="detail-info-txt">
           未核价
        </div>
    </c:if>
</div>
<div class="detail-info-field width2">
    <div class="detail-info-label">客户上次购价：</div>
    <div class="detail-info-txt">
        ￥${item.lastOrderPrice}
    </div>
</div>
<div class="detail-info-field width2">
    <div class="detail-info-label">可用/总库存：</div>
    <div class="detail-info-txt">
        <span id="orderOccupy_stockNum_${item.saleorderGoodsId}"></span>
    </div>
</div>
    <!-- 核价参考价：${skuNoAndPriceMap[item.sku]}<br/>
    客户上次购买价格：${item.lastOrderPrice}
    <br />
    <%--采购到货时长（工作日）：${item.perchaseTime}<br/>--%>
可用/总库存：<span id="orderOccupy_stockNum_${item.saleorderGoodsId}"></span> -->

