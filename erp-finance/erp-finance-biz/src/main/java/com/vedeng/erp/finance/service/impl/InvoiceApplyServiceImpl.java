package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterSaleSkuInfoDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDetailApiDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.entity.*;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.InvoiceApplyTypeEnum;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.enums.InvoiceTypeEnum;
import com.vedeng.erp.finance.enums.YNEnum;
import com.vedeng.erp.finance.facade.ApplyInvoiceFacade;
import com.vedeng.erp.finance.mapper.*;
import com.vedeng.erp.finance.mapstruct.CheckRuleConvertor;
import com.vedeng.erp.finance.mapstruct.InvoiceApplyConvertor;
import com.vedeng.erp.finance.mapstruct.InvoiceApplyDetailConvertor;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.erp.finance.service.InvoiceNumApiService;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.settlement.mapper.CapitalBillMapper;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.goods.service.GoodsApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InvoiceApplyServiceImpl implements InvoiceApplyService, InvoiceApplyApiService {

    @Autowired
    InvoiceApplyMapper invoiceApplyMapper;
    @Autowired
    InvoiceApplyDetailMapper invoiceApplyDetailMapper;
    @Autowired
    InvoiceApplyConvertor invoiceApplyConvertor;
    @Autowired
    InvoiceApplyDetailConvertor invoiceApplyDetailConvertor;
    @Autowired
    private AfterSalesApiService afterSalesApiService;
    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private InvoiceMapper invoiceMapper;
    @Autowired
    private CapitalBillMapper capitalBillMapper;
    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;
    @Autowired
    private CheckRuleMapper checkRuleMapper;
    @Autowired
    private CheckRuleConvertor checkRuleConvertor;
    @Autowired
    private InvoiceApplyReasonSnapshotMapper invoiceApplyReasonSnapshotMapper;

    @Autowired
    private ApplyInvoiceFacade applyInvoiceFacade;
    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;
    @Autowired
    private InvoiceNumApiService invoiceNumApiService;
    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;

    @Autowired
    private GoodsApiService goodsApiService;



    @Override
    public PageInfo<InvoiceApplyDto> getWaitInvoiceApply(Page page,String saleOrderNo) {
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        List<InvoiceApplyDto> invoiceApplyDtos = invoiceApplyMapper.getWaitInvoiceApply(saleOrderNo);
        List<Integer> idList = invoiceApplyDtos.stream().map(InvoiceApplyDto::getInvoiceApplyId).collect(Collectors.toList());
        if (CollUtil.isEmpty(idList)){
            log.info("未查询到待开票数据");
            return new PageInfo<>(invoiceApplyDtos);
        }
        List<InvoiceApplyDetailDto> byInvoiceApplyIdIn = invoiceApplyDetailMapper.findByInvoiceApplyIdIn(idList);
        if(CollUtil.isNotEmpty(invoiceApplyDtos)){
            invoiceApplyDtos.forEach(s -> {
                List<InvoiceApplyDetailDto> invoiceApplyDetailDtoList = byInvoiceApplyIdIn.stream().filter(i -> i.getInvoiceApplyId().equals(s.getInvoiceApplyId())).collect(Collectors.toList());
                s.setInvoiceApplyDetailDtoList(invoiceApplyDetailDtoList);
            });
        }
        return new PageInfo<>(invoiceApplyDtos);
    }

    /**
     * 安调待开票分页查询
     *
     * @param page
     * @param param
     * @return
     */
    @Override
    public PageInfo<InvoiceApplyDto> getAtWaitInvoiceApply(Page page, String param) {
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        List<InvoiceApplyDto> invoiceApplyDtos = invoiceApplyMapper.getAtWaitInvoiceApply(param);
        List<Integer> idList = invoiceApplyDtos.stream().map(InvoiceApplyDto::getInvoiceApplyId).collect(Collectors.toList());
        if (CollUtil.isEmpty(idList)){
            log.info("未查询到待开票数据");
            return new PageInfo<>(invoiceApplyDtos);
        }
        List<InvoiceApplyDetailDto> byInvoiceApplyIdIn = invoiceApplyDetailMapper.findAtByInvoiceApplyIdIn(idList);
        if(CollUtil.isNotEmpty(invoiceApplyDtos)){
            invoiceApplyDtos.forEach(s -> {
                List<InvoiceApplyDetailDto> invoiceApplyDetailDtoList = byInvoiceApplyIdIn.stream().filter(i -> i.getInvoiceApplyId().equals(s.getInvoiceApplyId())).collect(Collectors.toList());
                s.setInvoiceApplyDetailDtoList(invoiceApplyDetailDtoList);
            });
        }
        return new PageInfo<>(invoiceApplyDtos);
    }

    /**
     * 根据申请id获取开票申请
     * @param invoiceApplyId
     * @return
     */
    @Override
    public InvoiceApplyDto getInvoiceApply(Integer invoiceApplyId) {
        InvoiceApplyEntity invoiceApplyEntity = invoiceApplyMapper.selectByPrimaryKey(invoiceApplyId);
        if(InvoiceTypeEnum.SALE_ORDER.getCode().equals(invoiceApplyEntity.getType())){
            InvoiceApplyDto invoiceApplyDto = invoiceApplyMapper.getInvoiceApply(invoiceApplyId);
            List<InvoiceApplyDetailDto> byInvoiceApplyIdIn = invoiceApplyDetailMapper.findByInvoiceApplyIdIn(Collections.singletonList(invoiceApplyId));
            invoiceApplyDto.setInvoiceApplyDetailDtoList(byInvoiceApplyIdIn);
            return invoiceApplyDto;
        }
        if(InvoiceTypeEnum.AFTER_ORDER.getCode().equals(invoiceApplyEntity.getType())){
            InvoiceApplyDto invoiceApplyDto = invoiceApplyMapper.getAtInvoiceApply(invoiceApplyId);
            List<InvoiceApplyDetailDto> byInvoiceApplyIdIn = invoiceApplyDetailMapper.findAtByInvoiceApplyIdIn(Collections.singletonList(invoiceApplyId));
            invoiceApplyDto.setInvoiceApplyDetailDtoList(byInvoiceApplyIdIn);
            return invoiceApplyDto;
        }
        log.error("{}开票申请类型未匹配到:{}", invoiceApplyId,JSONObject.toJSONString(invoiceApplyEntity));
        return new InvoiceApplyDto();
    }

    /**
     * 查询安调开票申请
     * @param invoiceApplyId
     * @return
     */
    @Override
    public InvoiceApplyDto getInvoiceApplyAt(Integer invoiceApplyId) {
        InvoiceApplyDto invoiceApplyDto = invoiceApplyMapper.getAtInvoiceApply(invoiceApplyId);
        List<InvoiceApplyDetailDto> byInvoiceApplyIdIn = invoiceApplyDetailMapper.findAtByInvoiceApplyIdIn(Collections.singletonList(invoiceApplyId));
        invoiceApplyDto.setInvoiceApplyDetailDtoList(byInvoiceApplyIdIn);
        return invoiceApplyDto;
    }

    @Override
    public AfterSaleApplyInfoDto afterSaleApplyInfo(Integer afterSalesId) {
        AfterSalesDto afterSalesDto = afterSalesApiService.getAfterSalesById(afterSalesId);
        if (Objects.isNull(afterSalesDto)) {
            throw new ServiceException("未查询到售后单信息");
        }
        AfterSalesDetailApiDto afterSalesDetailApiDto = afterSalesApiService.getAfterSalesDetailByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetailApiDto)) {
            throw new ServiceException("未查询到售后单详情信息");
        }
        List<AfterSalesGoodsDto> afterSalesGoodsDtoList = afterSalesApiService.findGoodsByAfterSalesIdAndGoodsType(afterSalesId, FinanceConstant.ONE);
        if (CollUtil.isEmpty(afterSalesGoodsDtoList)) {
            throw new ServiceException("未查询到售后单商品信息");
        }
        Integer invoiceType = afterSalesDetailApiDto.getInvoiceType();
        BigDecimal serviceAmount = afterSalesDetailApiDto.getServiceAmount();
        AfterSalesGoodsDto afterSalesGoodsDto = afterSalesGoodsDtoList.get(0);
        BigDecimal num = BigDecimal.valueOf(afterSalesGoodsDto.getNum());
        BigDecimal tax = InvoiceTaxTypeEnum.getTax(invoiceType);
        String desc = InvoiceTaxTypeEnum.getDesc(invoiceType);
        // 主表
        AfterSaleApplyInfoDto afterSaleApplyInfoDto = new AfterSaleApplyInfoDto();
        afterSaleApplyInfoDto.setAfterSalesDetailId(afterSalesDetailApiDto.getAfterSalesDetailId());
        afterSaleApplyInfoDto.setAfterSalesId(afterSalesDetailApiDto.getAfterSalesId());
        afterSaleApplyInfoDto.setInvoiceType(invoiceType);
        afterSaleApplyInfoDto.setInvoiceTypeStr(desc);
        afterSaleApplyInfoDto.setTaxRate(tax);
        afterSaleApplyInfoDto.setSaleorderId(afterSalesDto.getOrderId());
        afterSaleApplyInfoDto.setInvoiceComments(afterSalesDto.getAfterSalesNo());
        // 明细表
        AfterSaleApplyInfoDto.AfterSaleApplyInfoGoodsDto afterSaleApplyInfoGoodsDto = getAfterSaleApplyInfoGoodsDto(afterSalesGoodsDto, serviceAmount, num);
        afterSaleApplyInfoDto.setAfterSaleApplyInfoGoodsDtos(Collections.singletonList(afterSaleApplyInfoGoodsDto));
        return afterSaleApplyInfoDto;
    }

    private  AfterSaleApplyInfoDto.AfterSaleApplyInfoGoodsDto getAfterSaleApplyInfoGoodsDto(AfterSalesGoodsDto afterSalesGoodsDto, BigDecimal serviceAmount, BigDecimal num) {
        AfterSaleApplyInfoDto.AfterSaleApplyInfoGoodsDto afterSaleApplyInfoGoodsDto = new AfterSaleApplyInfoDto.AfterSaleApplyInfoGoodsDto();
        afterSaleApplyInfoGoodsDto.setAfterSalesGoodsId(afterSalesGoodsDto.getAfterSalesGoodsId());
        afterSaleApplyInfoGoodsDto.setGoodsName(afterSalesGoodsDto.getSkuName());
        AfterSaleSkuInfoDto sku = afterSalesApiService.getGoodsNameModelAndSpecByAfterSalesGoodsId(afterSalesGoodsDto.getAfterSalesGoodsId());
        AfterSaleSkuInfoDto skuInfoDto = Optional.ofNullable(sku).orElse(new AfterSaleSkuInfoDto());
        String specStr;
        List<Integer> typeList = Arrays.asList(316,317,318,1008);
        if ((Integer.valueOf(316).equals(skuInfoDto.getSpuType()) || Integer.valueOf(1008).equals(skuInfoDto.getSpuType()))
                || (!typeList.contains(skuInfoDto.getSpuType()) && StrUtil.isNotBlank(skuInfoDto.getModel()))){
            specStr=StrUtil.isEmpty(skuInfoDto.getModel())?"":skuInfoDto.getModel();
        }else {
            specStr=StrUtil.isEmpty(skuInfoDto.getSpec())?"":skuInfoDto.getSpec();
        }
        afterSaleApplyInfoGoodsDto.setSpec(specStr);


        String skuTaxNo = goodsApiService.getSkuTaxNo(afterSalesGoodsDto.getGoodsId());
        if (StrUtil.isNotEmpty(skuTaxNo)) {
            afterSaleApplyInfoGoodsDto.setTaxCategoryNo(skuTaxNo);
        }
        afterSaleApplyInfoGoodsDto.setUnitName("次");
        afterSaleApplyInfoGoodsDto.setPrice(serviceAmount.divide(num, 2, RoundingMode.HALF_UP));
        afterSaleApplyInfoGoodsDto.setApplyNum(num);
        afterSaleApplyInfoGoodsDto.setApplyAmount(serviceAmount);
        return afterSaleApplyInfoGoodsDto;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void afterSaleApply(AfterSaleApplyDto afterSaleApplyDto) {
        if (Objects.isNull(afterSaleApplyDto) || Objects.isNull(afterSaleApplyDto.getAfterSalesId()) || CollUtil.isEmpty(afterSaleApplyDto.getAfterSaleApplyInfoGoodsDtos())) {
            log.info("售后开票申请,参数错误");
            throw new ServiceException("参数错误");
        }
        Integer afterSalesId = afterSaleApplyDto.getAfterSalesId();
        AfterSalesDto afterSalesDto = afterSalesApiService.getAfterSalesById(afterSalesId);
        if (Objects.isNull(afterSalesDto)) {
            log.info("售后开票申请,未查询到售后单信息,afterSalesId:{}", afterSalesId);
            throw new ServiceException("未查询到售后单信息");
        }
        Integer subjectType = afterSalesDto.getSubjectType();
        Integer type = afterSalesDto.getType();
        if (!FinanceConstant.ID_535.equals(subjectType) && !FinanceConstant.ID_537.equals(subjectType)) {
            log.info("售后开票申请,售后单类型错误,subjectType:{}", subjectType);
            throw new ServiceException("售后单类型错误:不是销售或第三方售后");
        }
        AfterSalesDetailApiDto afterSalesDetailApiDto = afterSalesApiService.getAfterSalesDetailByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetailApiDto)) {
            log.info("售后开票申请,未查询到售后单详情信息,afterSalesId:{}", afterSalesId);
            throw new ServiceException("未查询到售后单详情信息");
        }
        Integer invoiceType = afterSalesDetailApiDto.getInvoiceType();
        BigDecimal serviceAmount = afterSalesDetailApiDto.getServiceAmount();
        // 前置检查
        afterSaleApplyBeforeCheck(serviceAmount, afterSalesDto, afterSalesId);

        // 1.校验数据
        afterSaleApplyCheck(subjectType, afterSalesDto, invoiceType);

        // 2.新增发票申请数据
        afterSaleApplyInsert(afterSaleApplyDto, afterSalesId, invoiceType);

        // 3.同步更新开票状态
        afterSaleApplyUpdate(afterSalesId, type);
    }

    @Override
    public List<InvoiceApplyDto> getByRelatedIdAndType(Integer relatedId, Integer type) {
        return invoiceApplyMapper.getByRelatedIdAndType(relatedId, type);
    }

    @Override
    public List<InvoiceApplyDto> getByAdvanceAndAdvanceValidStatus(Integer advance, Integer advanceValidStatus) {
        return invoiceApplyMapper.getByAdvanceAndAdvanceValidStatus(advance,advanceValidStatus);
    }

    @Override
    public List<InvoiceCheckRuleDto> getCheckRuleList(Integer ruleType) {
        List<CheckRuleEntity> checkRuleEntities = checkRuleMapper.queryByRuleType(ruleType);
        List<InvoiceCheckRuleDto> invoiceCheckRuleDtos = checkRuleConvertor.toDto(checkRuleEntities);
        return invoiceCheckRuleDtos;
    }

    @Override
    public void updateInvoiceApplyStatus(Integer status,Integer invoiceApplyId) {
        InvoiceEntity invoiceEntity = new InvoiceEntity();
        invoiceEntity.setInvoiceApplyId(invoiceApplyId);
        invoiceEntity.setValidStatus(status);
        invoiceMapper.updateByPrimaryKeySelective(invoiceEntity);
    }

    /**
     * 校验
     * @param invoiceApplyDto
     */
    @Override
    public CheckInvoiceApplyResponseDto checkInvoiceApply(InvoiceApplyDto invoiceApplyDto) {
        CheckInvoiceApplyResponseDto traderFinanceDto = applyInvoiceFacade.checkApplyInvoice(invoiceApplyDto);
        return traderFinanceDto;

    }

    @Override
    public CheckInvoiceApplyResponseDto checkInvoiceApplyAfter(Integer afterSalesId) {
        CheckInvoiceApplyResponseDto traderFinanceDto = applyInvoiceFacade.checkApplyInvoiceAfter(afterSalesId);
        return traderFinanceDto;
    }

    @Override
    public Boolean showButtonApplyInvoice(Integer saleOrderId) {
        try {
            applyInvoiceFacade.showButtonApplyInvoice(saleOrderId);
        }catch (ServiceException e){
            log.info("不展示申请开票：{}",e.getMessage());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 规则校验
     * @param saleOrderId
     */
    @Override
    public void ruleCheck(Integer saleOrderId) {
        InvoiceCheckResultDto invoiceCheckResultDto = applyInvoiceFacade.checkCheckRule(saleOrderId);
        if (!invoiceCheckResultDto.getSuccess()){
            log.info("规则校验失败");
            throw new ServiceException("规则校验失败");
        }
    }

    @Override
    public List<InvoiceApplyDto> getApplyNoPeerBySalesId(Integer saleOrderId) {
        return invoiceApplyMapper.getApplyNoPeerBySalesId(saleOrderId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveInvoiceApply(InvoiceApplyDto invoiceApply, List<InvoiceApplyDetailDto> details) {
        InvoiceApplyEntity entity = new InvoiceApplyEntity();
        entity.setCompanyId(invoiceApply.getCompanyId());
        entity.setInvoiceProperty(invoiceApply.getInvoiceProperty());
        entity.setApplyMethod(invoiceApply.getApplyMethod());
        entity.setType(invoiceApply.getType());
        entity.setRelatedId(invoiceApply.getRelatedId());
        entity.setIsAuto(invoiceApply.getIsAuto());
        entity.setYyValidStatus(invoiceApply.getYyValidStatus());
        entity.setComments(invoiceApply.getComments());
        entity.setInvoiceInfoType(invoiceApply.getInvoiceInfoType());
        entity.setAddTime(System.currentTimeMillis());
        entity.setCreator(ErpConstant.ONE);
        entity.setModTime(System.currentTimeMillis());
        entity.setUpdater(ErpConstant.ONE);
        entity.setInvoiceMessage(invoiceApply.getInvoiceMessage());
        int entityResult = invoiceApplyMapper.insertSelective(entity);
        if (!FinanceConstant.ONE.equals(entityResult)) {
            log.info("新增开票申请失败,entity:{}", JSON.toJSONString(entity));
            throw new ServiceException("新增开票申请失败");
        }
        List<InvoiceApplyDetailEntity> detailEntities = details.stream().map(dto -> {
            InvoiceApplyDetailEntity detailEntity = new InvoiceApplyDetailEntity();
            detailEntity.setInvoiceApplyId(entity.getInvoiceApplyId());
            detailEntity.setDetailgoodsId(dto.getDetailgoodsId());
            detailEntity.setPrice(dto.getPrice());
            detailEntity.setNum(dto.getNum());
            detailEntity.setTotalAmount(dto.getTotalAmount());
            detailEntity.setProductName(dto.getProductName());
            detailEntity.setSpecModel(StrUtil.isNotBlank(dto.getSpecModel()) ? dto.getSpecModel() : "");
            detailEntity.setUnit(dto.getUnit());
            detailEntity.setTaxRate(dto.getTaxRate());
            detailEntity.setTaxAmount(dto.getTaxAmount());
            detailEntity.setTaxExclusiveAmount(dto.getTaxExclusiveAmount());
            detailEntity.setTaxExclusivePrice(dto.getTaxExclusivePrice());
            return detailEntity;
        }).collect(Collectors.toList());
        int detailEntityResult = invoiceApplyDetailMapper.batchInsert(detailEntities);
        if (detailEntityResult != detailEntities.size()) {
            log.info("新增开票申请明细失败,detailEntities:{}", JSON.toJSONString(detailEntities));
            throw new ServiceException("新增开票申请明细失败");
        }
    }

    private void afterSaleApplyBeforeCheck(BigDecimal serviceAmount, AfterSalesDto afterSalesDto, Integer afterSalesId) {
        if (Objects.isNull(serviceAmount) || serviceAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("售后开票申请,售后服务费为0，无法开票,serviceAmount:{}", serviceAmount);
            throw new ServiceException("售后服务费为0，无法开票");
        }
        //BigDecimal capitalBill = capitalBillMapper.getAftersaleServiceAmountBill(afterSalesDto.getAfterSalesNo(), afterSalesId);
        //if (Objects.isNull(capitalBill) || capitalBill.compareTo(BigDecimal.ZERO) <= 0) {
        //    log.info("售后开票申请,售后服务费未收款，无法开票,afterSalesNo:{},afterSalesId:{}", afterSalesDto.getAfterSalesNo(), afterSalesId);
        //    throw new ServiceException("售后服务费未收款，无法开票");
        //}
        InvoiceApplyDto invoiceApply = invoiceApplyMapper.getAftersaleInvoiceApplyByRelatedIdLast(afterSalesId);
        if (Objects.nonNull(invoiceApply) &&
                (!FinanceConstant.ONE.equals(invoiceApply.getValidStatus()) && !FinanceConstant.TWO.equals(invoiceApply.getValidStatus())) &&
                !FinanceConstant.TWO.equals(invoiceApply.getAdvanceValidStatus())) {
            throw new ServiceException("已存在进行中开票申请");
        }
    }

    private void afterSaleApplyUpdate(Integer afterSalesId, Integer type) {
        List<InvoiceDto> afterInvoiceList = invoiceMapper.getAfterInvoiceList(afterSalesId, FinanceConstant.ID_504, FinanceConstant.ONE);
        int status = 0;
        if (FinanceConstant.ID_539.equals(type) || FinanceConstant.ID_540.equals(type)) {
            // 退货换货
            if (CollUtil.isEmpty(afterInvoiceList)) {
                // 存在开票申请，不存在开票记录，开票状态为未开票
                status = FinanceConstant.ONE;
            } else {
                // 存在开票申请，存在开票记录，开票状态为全部开票
                status = FinanceConstant.TWO;
            }
        } else {
            // 销售、第三方安调
            if (CollUtil.isNotEmpty(afterInvoiceList)) {
                BigDecimal makeOutInvoice = afterInvoiceList.stream().map(InvoiceDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (makeOutInvoice.compareTo(BigDecimal.ZERO) == 0) {
                    status = FinanceConstant.ONE;
                } else {
                    status = FinanceConstant.TWO;
                }
            } else {
                status = FinanceConstant.ONE;
            }
        }
        afterSalesApiService.saveMakeOutInvoiceStatus(afterSalesId, status);
    }

    private void afterSaleApplyCheck(Integer subjectType, AfterSalesDto afterSalesDto, Integer invoiceType) {
        OrderFinanceInfoDto orderFinanceInfoDto = null;
        if (FinanceConstant.ID_535.equals(subjectType)) {
            orderFinanceInfoDto = saleOrderApiService.getSaleorderFinanceInfo(afterSalesDto.getOrderId());
        } else if (FinanceConstant.ID_537.equals(subjectType)) {
            orderFinanceInfoDto = afterSalesApiService.getAftersaleFinanceInfo(afterSalesDto.getAfterSalesId());
        }
        if (Objects.isNull(orderFinanceInfoDto)) {
            log.info("售后开票申请,未查询到售后订单关联的财务信息,afterSalesId:{}", afterSalesDto.getAfterSalesId());
            throw new ServiceException("未查询到售后订单关联的财务信息");
        }
        // 判断发票类型
        if (!InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType)) {
            // 增值税普通发票
            if (orderFinanceInfoDto.getTraderName().contains(FinanceConstant.COMPANY) || orderFinanceInfoDto.getTraderName().contains(FinanceConstant.ENTERPRISE)) {
                if (StrUtil.isBlank(orderFinanceInfoDto.getTaxNum())) {
                    // 税务登记号
                    log.info("售后开票申请,客户开票资料不全,无法开具增值税普通发票,orderFinanceInfoDto:{}", JSON.toJSONString(orderFinanceInfoDto));
                    throw new ServiceException("客户开票资料不全，无法开具增值税普通发票，请完善信息！");
                }
            }
        } else if (InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType)) {
            // 增值税专用发票
            // 注册地址、注册电话、开户银行、银行账号、税务登记号、一般纳税人资质
            if (StrUtil.isBlank(orderFinanceInfoDto.getRegAddress()) || StrUtil.isBlank(orderFinanceInfoDto.getRegTel())
                    || StrUtil.isBlank(orderFinanceInfoDto.getBank()) || StrUtil.isBlank(orderFinanceInfoDto.getBankAccount())
                    || StrUtil.isBlank(orderFinanceInfoDto.getTaxNum()) || StrUtil.isBlank(orderFinanceInfoDto.getAverageTaxpayerUri())) {
                log.info("售后开票申请,客户开票资料不全,无法开具增值税专用发票,orderFinanceInfoDto:{}", JSON.toJSONString(orderFinanceInfoDto));
                throw new ServiceException("客户开票资料不全，无法开具增值税专用发票，请完善信息！");
            }
        }
    }

    private void afterSaleApplyInsert(AfterSaleApplyDto afterSaleApplyDto, Integer afterSalesId, Integer invoiceType) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        InvoiceApplyEntity entity = new InvoiceApplyEntity();
        entity.setCompanyId(FinanceConstant.ONE);
        entity.setInvoiceProperty(FinanceConstant.THREE);
        entity.setApplyMethod(FinanceConstant.FOUR);
        entity.setType(FinanceConstant.ID_504);
        entity.setRelatedId(afterSalesId);
        entity.setIsAuto(FinanceConstant.FOUR);
        entity.setYyValidStatus(FinanceConstant.ONE);
        entity.setComments(afterSaleApplyDto.getInvoiceComments());
        entity.setInvoiceInfoType(afterSaleApplyDto.getInvoiceInfoType());
        entity.setAddTime(System.currentTimeMillis());
        entity.setCreator(currentUser.getId());
        entity.setModTime(System.currentTimeMillis());
        entity.setUpdater(currentUser.getId());
        entity.setInvoiceMessage(afterSaleApplyDto.getInvoiceMessage());
        entity.setAdvanceValidReason(afterSaleApplyDto.getAdvanceValidReason());
        entity.setIsAdvance(StrUtil.isNotBlank(afterSaleApplyDto.getAdvanceValidReason()) ? ErpConstant.ONE : ErpConstant.ZERO);

        int entityResult = invoiceApplyMapper.insertSelective(entity);
        if (!FinanceConstant.ONE.equals(entityResult)) {
            log.info("新增开票申请失败,entity:{}", JSON.toJSONString(entity));
            throw new ServiceException("新增开票申请失败");
        }

        BigDecimal taxRate = InvoiceTaxTypeEnum.getTax(invoiceType);
        List<InvoiceApplyDetailEntity> detailEntities = afterSaleApplyDto.getAfterSaleApplyInfoGoodsDtos().stream().map(dto -> {
            InvoiceApplyDetailEntity detailEntity = new InvoiceApplyDetailEntity();
            detailEntity.setInvoiceApplyId(entity.getInvoiceApplyId());
            detailEntity.setDetailgoodsId(dto.getAfterSalesGoodsId());
            detailEntity.setPrice(dto.getPrice());
            detailEntity.setNum(dto.getApplyNum());
            detailEntity.setTotalAmount(dto.getApplyAmount());
            detailEntity.setProductName(dto.getGoodsName());
            detailEntity.setSpecModel(dto.getSpec());
            detailEntity.setUnit(dto.getUnitName());
            detailEntity.setTaxRate(taxRate);
            // 售后税额 = （售后价税合计/（1+订单税率））*订单税率, 先乘后除保留两位小数
            BigDecimal taxAmount = (dto.getApplyAmount().multiply(taxRate)).divide(taxRate.add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            detailEntity.setTaxAmount(taxAmount);
            BigDecimal taxExclusiveAmount = dto.getApplyAmount().subtract(taxAmount);
            detailEntity.setTaxExclusiveAmount(taxExclusiveAmount);
            detailEntity.setTaxExclusivePrice(taxExclusiveAmount.divide(dto.getApplyNum(), 2, RoundingMode.HALF_UP));

            return detailEntity;
        }).collect(Collectors.toList());
        int detailEntityResult = invoiceApplyDetailMapper.batchInsert(detailEntities);
        if (detailEntityResult != detailEntities.size()) {
            log.info("新增开票申请明细失败,detailEntities:{}", JSON.toJSONString(detailEntities));
            throw new ServiceException("新增开票申请明细失败");
        }
        //新增提前开票原因
        log.info("新增售后开票申请,填写开票申请原因,入参:{}",JSONUtil.toJsonStr(afterSaleApplyDto.getInvoiceCheckResultDetailDtoList()));
        if (CollUtil.isNotEmpty(afterSaleApplyDto.getInvoiceCheckResultDetailDtoList())){
            afterSaleApplyDto.getInvoiceCheckResultDetailDtoList().forEach(d -> {
                InvoiceApplyReasonSnapshotEntity snapshotEntity = new InvoiceApplyReasonSnapshotEntity();
                snapshotEntity.setInvoiceApplyId(entity.getInvoiceApplyId());
                snapshotEntity.setRuleCode(d.getRuleCode());
                snapshotEntity.setRuleName(d.getRuleName());
                snapshotEntity.setRuleContent(d.getRuleContent());
                snapshotEntity.setPromptText(d.getPromptText());
                snapshotEntity.setApplyReason(d.getApplyReason());
                snapshotEntity.setIsDelete(ErpConstant.F);
                invoiceApplyReasonSnapshotMapper.insertSelective(snapshotEntity);
            });
            String collect = afterSaleApplyDto.getInvoiceCheckResultDetailDtoList().stream()
                    .sorted(Comparator.comparing(InvoiceCheckResultDto.InvoiceCheckResultDetailDto::getRuleCode))
                    .map(InvoiceCheckResultDto.InvoiceCheckResultDetailDto::getRuleCode)
                    .collect(Collectors.joining(","));
            invoiceCheckApiService.addApplyCheck(entity.getInvoiceApplyId(),collect);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void auditPassInvoiceApply(Integer invoiceApplyId) {
        InvoiceApplyEntity entity = new InvoiceApplyEntity();
        entity.setInvoiceApplyId(invoiceApplyId);
        entity.setValidStatus(ErpConstant.AUDIT_PASS);
        log.info("更新审核状态：{}",JSON.toJSON(entity));
        invoiceApplyMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public String getSaleOrderNoByInvoiceApplyId(Integer invoiceApplyId) {
        return invoiceApplyMapper.getSaleOrderNoByInvoiceApplyId(invoiceApplyId);
    }

    /**
     * 根据开票申请id查询
     *
     * @param invoiceApplyId InvoiceApplyDto
     * @return 开票申请主键
     */
    @Override
    public String getAfterSalesNoByInvoiceApplyId(Integer invoiceApplyId) {
        return invoiceApplyMapper.getAfterSalesNoByInvoiceApplyId(invoiceApplyId);
    }

    /**
     * 根据开票申请明细id查询
     *
     * @param invoiceApplyDetailId InvoiceApplyDetailDto
     * @return 开票申请明细主键
     */
    @Override
    public InvoiceApplyDetailDto getInvoiceApplyDetailById(Integer invoiceApplyDetailId) {
        return invoiceApplyDetailMapper.getByInvoiceApplyDetailId(invoiceApplyDetailId);
    }

    @Override
    public List<InvoiceApplyDetailDto> getByInvoiceApplyId(List<Integer> invoiceApplyIdList) {
        return invoiceApplyDetailConvertor.toDto(invoiceApplyDetailMapper.selectByInvoiceApplyIdIn(invoiceApplyIdList));
    }

    @Override
    public InvoiceApplyDto getAfterSaleInvoiceApplyInfo(Integer invoiceApplyId) {
        InvoiceApplyDto applyDto = invoiceApplyConvertor.toDto(invoiceApplyMapper.selectByPrimaryKey(invoiceApplyId));
        List<InvoiceApplyDetailDto> details = invoiceApplyDetailMapper.getDetailByInvoiceApplyId(invoiceApplyId);
        //售后开票
        if (FinanceConstant.ID_504.equals(applyDto.getType())){
            //发票类型 afterSalesDetail中
            AfterSalesDetailApiDto afterSalesDetail = afterSalesApiService.getAfterSalesDetailByAfterSalesId(applyDto.getRelatedId());
            if (ObjectUtil.isNull(afterSalesDetail)) {
                log.info("查看开票申请,未查询到售后订单明细,invoiceApply:{}",JSONUtil.toJsonStr(applyDto));
            }else {
                applyDto.setInvoiceType(afterSalesDetail.getInvoiceType());
            }
            //开票方式固定 自动数电
            applyDto.setInvoiceMethod(applyDto.getIsAuto());
            details.forEach(d -> {
                AfterSaleSkuInfoDto sku = afterSalesApiService.getGoodsNameModelAndSpecByAfterSalesGoodsId(d.getDetailgoodsId());
                AfterSaleSkuInfoDto skuInfoDto = Optional.ofNullable(sku).orElse(new AfterSaleSkuInfoDto());
                if (StrUtil.isBlank(d.getProductName())){
                    d.setProductName(skuInfoDto.getSkuName());
                }
                if(!d.getProductName().equals(skuInfoDto.getSkuName())){
                    d.setOldProductName(skuInfoDto.getSkuName());
                }
                String specStr = "";
//                List<Integer> typeList = Arrays.asList(316,317,318,1008);
//                if ((Integer.valueOf(316).equals(skuInfoDto.getSpuType()) || Integer.valueOf(1008).equals(skuInfoDto.getSpuType()))
//                        || (!typeList.contains(skuInfoDto.getSpuType()) && StrUtil.isNotBlank(skuInfoDto.getModel()))){
//                    specStr=StrUtil.isEmpty(skuInfoDto.getModel())?"":skuInfoDto.getModel();
//                }else {
//                    specStr=StrUtil.isEmpty(skuInfoDto.getSpec())?"":skuInfoDto.getSpec();
//                }
                String specModel = StrUtil.isEmpty(d.getSpecModel())?"":d.getSpecModel().trim();
                if(!specStr.equals(specModel)){
                    d.setOldSpec(specStr);
                }
                d.setAppliedNum(BigDecimal.ZERO);
                d.setTNNum(BigDecimal.ZERO);
                BigDecimal invoicedNum = invoiceApplyMapper.getInvoicedNumAfter(d.getDetailgoodsId());
                d.setInvoicedNum(Objects.isNull(invoicedNum) ? BigDecimal.ZERO : invoicedNum);
                SysOptionDefinitionDto optionDefinitionById = sysOptionDefinitionApiService.getOptionDefinitionById(applyDto.getInvoiceType());
                if (ObjectUtil.isNotNull(optionDefinitionById)){
                    BigDecimal rate = new BigDecimal(optionDefinitionById.getComments()).multiply(new BigDecimal(100)).stripTrailingZeros();
                    d.setTaxRateStr(rate+"%");
                }
            });
        }
        //销售开票
        if (FinanceConstant.ID_505.equals(applyDto.getType())){
            SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderId(applyDto.getRelatedId());
            if (ObjectUtil.isNull(saleOrder)){
                log.info("查看开票申请,未能查询到销售单,invoiceApply:{}",JSONUtil.toJsonStr(saleOrder));
            }else {
                applyDto.setInvoiceMethod(applyDto.getIsAuto());
                applyDto.setInvoiceType(saleOrder.getInvoiceType());
            }
            Map<Integer, InvoiceNumApiDto.SalesOrderInvoiceDto> invoiceDtoMap = invoiceNumApiService.getInvoiceNum(applyDto.getRelatedId()).stream().collect(Collectors.toMap(InvoiceNumApiDto.SalesOrderInvoiceDto::getSalesOrderGoodsId, x -> x, (k1, k2) -> k1));
            details.forEach(d -> {
                SaleOrderGoodsDetailDto goodsDto = saleOrderGoodsApiService.getBySaleOrderGoodsId(d.getDetailgoodsId());
                String specStr;
                List<Integer> typeList = Arrays.asList(316,317,318,1008);
                if ((Integer.valueOf(316).equals(goodsDto.getSpuType()) || Integer.valueOf(1008).equals(goodsDto.getSpuType()))
                        || (!typeList.contains(goodsDto.getSpuType()) && StrUtil.isNotBlank(goodsDto.getModel()))){
                    specStr=StrUtil.isEmpty(goodsDto.getModel())?"":goodsDto.getModel().trim();
                }else {
                    specStr=StrUtil.isEmpty(goodsDto.getSpec())?"":goodsDto.getSpec().trim();
                }
                if (StrUtil.isBlank(d.getProductName())){
                    d.setProductName(goodsDto.getGoodsName());
                    d.setUnit(goodsDto.getUnitName());
                    d.setSpecModel(specStr);
                }
                if(!d.getProductName().equals(goodsDto.getGoodsName())){
                    d.setOldProductName(goodsDto.getGoodsName());
                }
                String specModel = StrUtil.isEmpty(d.getSpecModel())?"":d.getSpecModel().trim();
                if(!specStr.equals(specModel)){
                    d.setOldSpec(specStr);
                }
                d.setTNNum(invoiceDtoMap.get(d.getDetailgoodsId()).getArrivalNum());
                if (DateUtil.formatDate(invoiceDtoMap.get(d.getDetailgoodsId()).getTime()).startsWith("1970")){
                    d.setTNDateStr("");
                }else {
                    d.setTNDateStr(DateUtil.formatDate(invoiceDtoMap.get(d.getDetailgoodsId()).getTime()));
                }
                BigDecimal invoicedNum = invoiceApplyMapper.getInvoicedNum(d.getDetailgoodsId());
                d.setInvoicedNum(Objects.isNull(invoicedNum)?BigDecimal.ZERO:invoicedNum);
                SysOptionDefinitionDto optionDefinitionById = sysOptionDefinitionApiService.getOptionDefinitionById(saleOrder.getInvoiceType());
                if (ObjectUtil.isNotNull(optionDefinitionById) && ObjectUtil.isNotNull(optionDefinitionById.getComments())){
                    BigDecimal rate = new BigDecimal(optionDefinitionById.getComments()).multiply(new BigDecimal(100)).stripTrailingZeros();
                    d.setTaxRateStr(rate+"%");
                }
            });

        }
        applyDto.setInvoiceApplyDetailDtoList(details);
        applyDto.setSnapshotDtoList(invoiceApplyMapper.getApplyReasonSnapshot(invoiceApplyId));

        return applyDto;
    }

    /**
     * 根据销售单id查询
     *
     * @param saleOrderId SaleOrderDto
     * @return 开票申请主键
     */
    @Override
    public List<InvoiceApplyDto> getInvoiceApplyBySaleOrderId(Integer saleOrderId) {
        if (Objects.isNull(saleOrderId)){
            return null;
        }
        InvoiceApplyEntity invoiceApplyEntity = new InvoiceApplyEntity();
        invoiceApplyEntity.setRelatedId(saleOrderId);
        // 销售开票
        invoiceApplyEntity.setType(InvoiceApplyTypeEnum.SALE_ORDER.getCode());
        // 开票中，注意底层的SQL判断
        invoiceApplyEntity.setIsOpening(YNEnum.Y.getCode());
        List<InvoiceApplyEntity> invoiceApplyEntities = invoiceApplyMapper.selectInvoiceApplyByEntity(invoiceApplyEntity);
        List<InvoiceApplyDto> invoiceApplyDtos = invoiceApplyConvertor.toDto(invoiceApplyEntities);
        return invoiceApplyDtos;
    }

    @Override
    public List<InvoiceApplyDto> getInvoiceApplyByAfterSaleId(Integer afterSaleId) {
        if (Objects.isNull(afterSaleId)){
            return null;
        }
        InvoiceApplyEntity invoiceApplyEntity = new InvoiceApplyEntity();
        invoiceApplyEntity.setRelatedId(afterSaleId);
        // 销售开票
        invoiceApplyEntity.setType(InvoiceApplyTypeEnum.AFTER_ORDER.getCode());
        // 开票中，注意底层的SQL判断
        invoiceApplyEntity.setIsOpening(YNEnum.Y.getCode());
        List<InvoiceApplyEntity> invoiceApplyEntities = invoiceApplyMapper.selectInvoiceApplyByEntity(invoiceApplyEntity);
        List<InvoiceApplyDto> invoiceApplyDtos = invoiceApplyConvertor.toDto(invoiceApplyEntities);
        return invoiceApplyDtos;
    }

    @Override
    public PageInfo<InvoiceApplyDto> getAdvanceInvoiceApply(Page page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> invoiceApplyMapper.queryAdvanceInvoiceApply());
    }

    @Override
    public void doAdvanceAuditPass(InvoiceApplyDto update) {
        InvoiceApplyEntity invoiceApplyEntity = invoiceApplyConvertor.toEntity(update);
        invoiceApplyMapper.updateByPrimaryKeySelective(invoiceApplyEntity);
    }

    @Override
    public void updateManualCreateType(Integer invoiceApplyId) {
        InvoiceApplyEntity invoiceApplyEntity = new InvoiceApplyEntity();
        invoiceApplyEntity.setInvoiceApplyId(invoiceApplyId);
        invoiceApplyEntity.setCreateType(ErpConstant.INVOICE_CREATE_TYPE_MANUAL);
        invoiceApplyMapper.updateByPrimaryKeySelective(invoiceApplyEntity);
    }
}
