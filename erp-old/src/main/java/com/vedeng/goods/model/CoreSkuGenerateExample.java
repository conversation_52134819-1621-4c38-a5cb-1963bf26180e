package com.vedeng.goods.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CoreSkuGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public CoreSkuGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSkuIdIsNull() {
            addCriterion("SKU_ID is null");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNotNull() {
            addCriterion("SKU_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualTo(Integer value) {
            addCriterion("SKU_ID =", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualTo(Integer value) {
            addCriterion("SKU_ID <>", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThan(Integer value) {
            addCriterion("SKU_ID >", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SKU_ID >=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThan(Integer value) {
            addCriterion("SKU_ID <", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualTo(Integer value) {
            addCriterion("SKU_ID <=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdIn(List<Integer> values) {
            addCriterion("SKU_ID in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotIn(List<Integer> values) {
            addCriterion("SKU_ID not in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdBetween(Integer value1, Integer value2) {
            addCriterion("SKU_ID between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SKU_ID not between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNull() {
            addCriterion("SPU_ID is null");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNotNull() {
            addCriterion("SPU_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualTo(Integer value) {
            addCriterion("SPU_ID =", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualTo(Integer value) {
            addCriterion("SPU_ID <>", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThan(Integer value) {
            addCriterion("SPU_ID >", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SPU_ID >=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThan(Integer value) {
            addCriterion("SPU_ID <", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualTo(Integer value) {
            addCriterion("SPU_ID <=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIn(List<Integer> values) {
            addCriterion("SPU_ID in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotIn(List<Integer> values) {
            addCriterion("SPU_ID not in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ID between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ID not between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIsNull() {
            addCriterion("CHECK_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIsNotNull() {
            addCriterion("CHECK_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andCheckStatusEqualTo(Integer value) {
            addCriterion("CHECK_STATUS =", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotEqualTo(Integer value) {
            addCriterion("CHECK_STATUS <>", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusGreaterThan(Integer value) {
            addCriterion("CHECK_STATUS >", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("CHECK_STATUS >=", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusLessThan(Integer value) {
            addCriterion("CHECK_STATUS <", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusLessThanOrEqualTo(Integer value) {
            addCriterion("CHECK_STATUS <=", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIn(List<Integer> values) {
            addCriterion("CHECK_STATUS in", values, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotIn(List<Integer> values) {
            addCriterion("CHECK_STATUS not in", values, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusBetween(Integer value1, Integer value2) {
            addCriterion("CHECK_STATUS between", value1, value2, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("CHECK_STATUS not between", value1, value2, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("MODEL is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("MODEL is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("MODEL =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("MODEL <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("MODEL >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("MODEL >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("MODEL <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("MODEL <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("MODEL like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("MODEL not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("MODEL in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("MODEL not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("MODEL between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("MODEL not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andSpecIsNull() {
            addCriterion("SPEC is null");
            return (Criteria) this;
        }

        public Criteria andSpecIsNotNull() {
            addCriterion("SPEC is not null");
            return (Criteria) this;
        }

        public Criteria andSpecEqualTo(String value) {
            addCriterion("SPEC =", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecNotEqualTo(String value) {
            addCriterion("SPEC <>", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecGreaterThan(String value) {
            addCriterion("SPEC >", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecGreaterThanOrEqualTo(String value) {
            addCriterion("SPEC >=", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecLessThan(String value) {
            addCriterion("SPEC <", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecLessThanOrEqualTo(String value) {
            addCriterion("SPEC <=", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecLike(String value) {
            addCriterion("SPEC like", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecNotLike(String value) {
            addCriterion("SPEC not like", value, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecIn(List<String> values) {
            addCriterion("SPEC in", values, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecNotIn(List<String> values) {
            addCriterion("SPEC not in", values, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecBetween(String value1, String value2) {
            addCriterion("SPEC between", value1, value2, "spec");
            return (Criteria) this;
        }

        public Criteria andSpecNotBetween(String value1, String value2) {
            addCriterion("SPEC not between", value1, value2, "spec");
            return (Criteria) this;
        }

        public Criteria andSkuNoIsNull() {
            addCriterion("SKU_NO is null");
            return (Criteria) this;
        }

        public Criteria andSkuNoIsNotNull() {
            addCriterion("SKU_NO is not null");
            return (Criteria) this;
        }

        public Criteria andSkuNoEqualTo(String value) {
            addCriterion("SKU_NO =", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoNotEqualTo(String value) {
            addCriterion("SKU_NO <>", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoGreaterThan(String value) {
            addCriterion("SKU_NO >", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoGreaterThanOrEqualTo(String value) {
            addCriterion("SKU_NO >=", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoLessThan(String value) {
            addCriterion("SKU_NO <", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoLessThanOrEqualTo(String value) {
            addCriterion("SKU_NO <=", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoLike(String value) {
            addCriterion("SKU_NO like", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoNotLike(String value) {
            addCriterion("SKU_NO not like", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoIn(List<String> values) {
            addCriterion("SKU_NO in", values, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoNotIn(List<String> values) {
            addCriterion("SKU_NO not in", values, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoBetween(String value1, String value2) {
            addCriterion("SKU_NO between", value1, value2, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoNotBetween(String value1, String value2) {
            addCriterion("SKU_NO not between", value1, value2, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNameIsNull() {
            addCriterion("SKU_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSkuNameIsNotNull() {
            addCriterion("SKU_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSkuNameEqualTo(String value) {
            addCriterion("SKU_NAME =", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotEqualTo(String value) {
            addCriterion("SKU_NAME <>", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThan(String value) {
            addCriterion("SKU_NAME >", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThanOrEqualTo(String value) {
            addCriterion("SKU_NAME >=", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThan(String value) {
            addCriterion("SKU_NAME <", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThanOrEqualTo(String value) {
            addCriterion("SKU_NAME <=", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLike(String value) {
            addCriterion("SKU_NAME like", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotLike(String value) {
            addCriterion("SKU_NAME not like", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameIn(List<String> values) {
            addCriterion("SKU_NAME in", values, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotIn(List<String> values) {
            addCriterion("SKU_NAME not in", values, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameBetween(String value1, String value2) {
            addCriterion("SKU_NAME between", value1, value2, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotBetween(String value1, String value2) {
            addCriterion("SKU_NAME not between", value1, value2, "skuName");
            return (Criteria) this;
        }

        public Criteria andShowNameIsNull() {
            addCriterion("SHOW_NAME is null");
            return (Criteria) this;
        }

        public Criteria andShowNameIsNotNull() {
            addCriterion("SHOW_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andShowNameEqualTo(String value) {
            addCriterion("SHOW_NAME =", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameNotEqualTo(String value) {
            addCriterion("SHOW_NAME <>", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameGreaterThan(String value) {
            addCriterion("SHOW_NAME >", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameGreaterThanOrEqualTo(String value) {
            addCriterion("SHOW_NAME >=", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameLessThan(String value) {
            addCriterion("SHOW_NAME <", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameLessThanOrEqualTo(String value) {
            addCriterion("SHOW_NAME <=", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameLike(String value) {
            addCriterion("SHOW_NAME like", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameNotLike(String value) {
            addCriterion("SHOW_NAME not like", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameIn(List<String> values) {
            addCriterion("SHOW_NAME in", values, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameNotIn(List<String> values) {
            addCriterion("SHOW_NAME not in", values, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameBetween(String value1, String value2) {
            addCriterion("SHOW_NAME between", value1, value2, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameNotBetween(String value1, String value2) {
            addCriterion("SHOW_NAME not between", value1, value2, "showName");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNull() {
            addCriterion("MATERIAL_CODE is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNotNull() {
            addCriterion("MATERIAL_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualTo(String value) {
            addCriterion("MATERIAL_CODE =", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualTo(String value) {
            addCriterion("MATERIAL_CODE <>", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThan(String value) {
            addCriterion("MATERIAL_CODE >", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("MATERIAL_CODE >=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThan(String value) {
            addCriterion("MATERIAL_CODE <", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("MATERIAL_CODE <=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLike(String value) {
            addCriterion("MATERIAL_CODE like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotLike(String value) {
            addCriterion("MATERIAL_CODE not like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIn(List<String> values) {
            addCriterion("MATERIAL_CODE in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotIn(List<String> values) {
            addCriterion("MATERIAL_CODE not in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeBetween(String value1, String value2) {
            addCriterion("MATERIAL_CODE between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("MATERIAL_CODE not between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andSupplyModelIsNull() {
            addCriterion("SUPPLY_MODEL is null");
            return (Criteria) this;
        }

        public Criteria andSupplyModelIsNotNull() {
            addCriterion("SUPPLY_MODEL is not null");
            return (Criteria) this;
        }

        public Criteria andSupplyModelEqualTo(String value) {
            addCriterion("SUPPLY_MODEL =", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelNotEqualTo(String value) {
            addCriterion("SUPPLY_MODEL <>", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelGreaterThan(String value) {
            addCriterion("SUPPLY_MODEL >", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelGreaterThanOrEqualTo(String value) {
            addCriterion("SUPPLY_MODEL >=", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelLessThan(String value) {
            addCriterion("SUPPLY_MODEL <", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelLessThanOrEqualTo(String value) {
            addCriterion("SUPPLY_MODEL <=", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelLike(String value) {
            addCriterion("SUPPLY_MODEL like", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelNotLike(String value) {
            addCriterion("SUPPLY_MODEL not like", value, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelIn(List<String> values) {
            addCriterion("SUPPLY_MODEL in", values, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelNotIn(List<String> values) {
            addCriterion("SUPPLY_MODEL not in", values, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelBetween(String value1, String value2) {
            addCriterion("SUPPLY_MODEL between", value1, value2, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andSupplyModelNotBetween(String value1, String value2) {
            addCriterion("SUPPLY_MODEL not between", value1, value2, "supplyModel");
            return (Criteria) this;
        }

        public Criteria andIsStockupIsNull() {
            addCriterion("IS_STOCKUP is null");
            return (Criteria) this;
        }

        public Criteria andIsStockupIsNotNull() {
            addCriterion("IS_STOCKUP is not null");
            return (Criteria) this;
        }

        public Criteria andIsStockupEqualTo(String value) {
            addCriterion("IS_STOCKUP =", value, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupNotEqualTo(String value) {
            addCriterion("IS_STOCKUP <>", value, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupGreaterThan(String value) {
            addCriterion("IS_STOCKUP >", value, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupGreaterThanOrEqualTo(String value) {
            addCriterion("IS_STOCKUP >=", value, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupLessThan(String value) {
            addCriterion("IS_STOCKUP <", value, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupLessThanOrEqualTo(String value) {
            addCriterion("IS_STOCKUP <=", value, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupLike(String value) {
            addCriterion("IS_STOCKUP like", value, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupNotLike(String value) {
            addCriterion("IS_STOCKUP not like", value, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupIn(List<String> values) {
            addCriterion("IS_STOCKUP in", values, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupNotIn(List<String> values) {
            addCriterion("IS_STOCKUP not in", values, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupBetween(String value1, String value2) {
            addCriterion("IS_STOCKUP between", value1, value2, "isStockup");
            return (Criteria) this;
        }

        public Criteria andIsStockupNotBetween(String value1, String value2) {
            addCriterion("IS_STOCKUP not between", value1, value2, "isStockup");
            return (Criteria) this;
        }

        public Criteria andWikiHrefIsNull() {
            addCriterion("WIKI_HREF is null");
            return (Criteria) this;
        }

        public Criteria andWikiHrefIsNotNull() {
            addCriterion("WIKI_HREF is not null");
            return (Criteria) this;
        }

        public Criteria andWikiHrefEqualTo(String value) {
            addCriterion("WIKI_HREF =", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefNotEqualTo(String value) {
            addCriterion("WIKI_HREF <>", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefGreaterThan(String value) {
            addCriterion("WIKI_HREF >", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefGreaterThanOrEqualTo(String value) {
            addCriterion("WIKI_HREF >=", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefLessThan(String value) {
            addCriterion("WIKI_HREF <", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefLessThanOrEqualTo(String value) {
            addCriterion("WIKI_HREF <=", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefLike(String value) {
            addCriterion("WIKI_HREF like", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefNotLike(String value) {
            addCriterion("WIKI_HREF not like", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefIn(List<String> values) {
            addCriterion("WIKI_HREF in", values, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefNotIn(List<String> values) {
            addCriterion("WIKI_HREF not in", values, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefBetween(String value1, String value2) {
            addCriterion("WIKI_HREF between", value1, value2, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefNotBetween(String value1, String value2) {
            addCriterion("WIKI_HREF not between", value1, value2, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterIsNull() {
            addCriterion("TECHNICAL_PARAMETER is null");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterIsNotNull() {
            addCriterion("TECHNICAL_PARAMETER is not null");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterEqualTo(String value) {
            addCriterion("TECHNICAL_PARAMETER =", value, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterNotEqualTo(String value) {
            addCriterion("TECHNICAL_PARAMETER <>", value, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterGreaterThan(String value) {
            addCriterion("TECHNICAL_PARAMETER >", value, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterGreaterThanOrEqualTo(String value) {
            addCriterion("TECHNICAL_PARAMETER >=", value, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterLessThan(String value) {
            addCriterion("TECHNICAL_PARAMETER <", value, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterLessThanOrEqualTo(String value) {
            addCriterion("TECHNICAL_PARAMETER <=", value, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterLike(String value) {
            addCriterion("TECHNICAL_PARAMETER like", value, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterNotLike(String value) {
            addCriterion("TECHNICAL_PARAMETER not like", value, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterIn(List<String> values) {
            addCriterion("TECHNICAL_PARAMETER in", values, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterNotIn(List<String> values) {
            addCriterion("TECHNICAL_PARAMETER not in", values, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterBetween(String value1, String value2) {
            addCriterion("TECHNICAL_PARAMETER between", value1, value2, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andTechnicalParameterNotBetween(String value1, String value2) {
            addCriterion("TECHNICAL_PARAMETER not between", value1, value2, "technicalParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterIsNull() {
            addCriterion("PERFORMANCE_PARAMETER is null");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterIsNotNull() {
            addCriterion("PERFORMANCE_PARAMETER is not null");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterEqualTo(String value) {
            addCriterion("PERFORMANCE_PARAMETER =", value, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterNotEqualTo(String value) {
            addCriterion("PERFORMANCE_PARAMETER <>", value, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterGreaterThan(String value) {
            addCriterion("PERFORMANCE_PARAMETER >", value, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterGreaterThanOrEqualTo(String value) {
            addCriterion("PERFORMANCE_PARAMETER >=", value, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterLessThan(String value) {
            addCriterion("PERFORMANCE_PARAMETER <", value, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterLessThanOrEqualTo(String value) {
            addCriterion("PERFORMANCE_PARAMETER <=", value, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterLike(String value) {
            addCriterion("PERFORMANCE_PARAMETER like", value, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterNotLike(String value) {
            addCriterion("PERFORMANCE_PARAMETER not like", value, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterIn(List<String> values) {
            addCriterion("PERFORMANCE_PARAMETER in", values, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterNotIn(List<String> values) {
            addCriterion("PERFORMANCE_PARAMETER not in", values, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterBetween(String value1, String value2) {
            addCriterion("PERFORMANCE_PARAMETER between", value1, value2, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andPerformanceParameterNotBetween(String value1, String value2) {
            addCriterion("PERFORMANCE_PARAMETER not between", value1, value2, "performanceParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterIsNull() {
            addCriterion("SPEC_PARAMETER is null");
            return (Criteria) this;
        }

        public Criteria andSpecParameterIsNotNull() {
            addCriterion("SPEC_PARAMETER is not null");
            return (Criteria) this;
        }

        public Criteria andSpecParameterEqualTo(String value) {
            addCriterion("SPEC_PARAMETER =", value, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterNotEqualTo(String value) {
            addCriterion("SPEC_PARAMETER <>", value, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterGreaterThan(String value) {
            addCriterion("SPEC_PARAMETER >", value, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterGreaterThanOrEqualTo(String value) {
            addCriterion("SPEC_PARAMETER >=", value, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterLessThan(String value) {
            addCriterion("SPEC_PARAMETER <", value, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterLessThanOrEqualTo(String value) {
            addCriterion("SPEC_PARAMETER <=", value, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterLike(String value) {
            addCriterion("SPEC_PARAMETER like", value, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterNotLike(String value) {
            addCriterion("SPEC_PARAMETER not like", value, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterIn(List<String> values) {
            addCriterion("SPEC_PARAMETER in", values, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterNotIn(List<String> values) {
            addCriterion("SPEC_PARAMETER not in", values, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterBetween(String value1, String value2) {
            addCriterion("SPEC_PARAMETER between", value1, value2, "specParameter");
            return (Criteria) this;
        }

        public Criteria andSpecParameterNotBetween(String value1, String value2) {
            addCriterion("SPEC_PARAMETER not between", value1, value2, "specParameter");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdIsNull() {
            addCriterion("BASE_UNIT_ID is null");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdIsNotNull() {
            addCriterion("BASE_UNIT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdEqualTo(Integer value) {
            addCriterion("BASE_UNIT_ID =", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdNotEqualTo(Integer value) {
            addCriterion("BASE_UNIT_ID <>", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdGreaterThan(Integer value) {
            addCriterion("BASE_UNIT_ID >", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("BASE_UNIT_ID >=", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdLessThan(Integer value) {
            addCriterion("BASE_UNIT_ID <", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("BASE_UNIT_ID <=", value, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdIn(List<Integer> values) {
            addCriterion("BASE_UNIT_ID in", values, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdNotIn(List<Integer> values) {
            addCriterion("BASE_UNIT_ID not in", values, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("BASE_UNIT_ID between", value1, value2, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("BASE_UNIT_ID not between", value1, value2, "baseUnitId");
            return (Criteria) this;
        }

        public Criteria andMinOrderIsNull() {
            addCriterion("MIN_ORDER is null");
            return (Criteria) this;
        }

        public Criteria andMinOrderIsNotNull() {
            addCriterion("MIN_ORDER is not null");
            return (Criteria) this;
        }

        public Criteria andMinOrderEqualTo(BigDecimal value) {
            addCriterion("MIN_ORDER =", value, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderNotEqualTo(BigDecimal value) {
            addCriterion("MIN_ORDER <>", value, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderGreaterThan(BigDecimal value) {
            addCriterion("MIN_ORDER >", value, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("MIN_ORDER >=", value, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderLessThan(BigDecimal value) {
            addCriterion("MIN_ORDER <", value, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderLessThanOrEqualTo(BigDecimal value) {
            addCriterion("MIN_ORDER <=", value, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderIn(List<BigDecimal> values) {
            addCriterion("MIN_ORDER in", values, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderNotIn(List<BigDecimal> values) {
            addCriterion("MIN_ORDER not in", values, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("MIN_ORDER between", value1, value2, "minOrder");
            return (Criteria) this;
        }

        public Criteria andMinOrderNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("MIN_ORDER not between", value1, value2, "minOrder");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthIsNull() {
            addCriterion("GOODS_LENGTH is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthIsNotNull() {
            addCriterion("GOODS_LENGTH is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthEqualTo(BigDecimal value) {
            addCriterion("GOODS_LENGTH =", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthNotEqualTo(BigDecimal value) {
            addCriterion("GOODS_LENGTH <>", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthGreaterThan(BigDecimal value) {
            addCriterion("GOODS_LENGTH >", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_LENGTH >=", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthLessThan(BigDecimal value) {
            addCriterion("GOODS_LENGTH <", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthLessThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_LENGTH <=", value, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthIn(List<BigDecimal> values) {
            addCriterion("GOODS_LENGTH in", values, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthNotIn(List<BigDecimal> values) {
            addCriterion("GOODS_LENGTH not in", values, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_LENGTH between", value1, value2, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsLengthNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_LENGTH not between", value1, value2, "goodsLength");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthIsNull() {
            addCriterion("GOODS_WIDTH is null");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthIsNotNull() {
            addCriterion("GOODS_WIDTH is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthEqualTo(BigDecimal value) {
            addCriterion("GOODS_WIDTH =", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthNotEqualTo(BigDecimal value) {
            addCriterion("GOODS_WIDTH <>", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthGreaterThan(BigDecimal value) {
            addCriterion("GOODS_WIDTH >", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_WIDTH >=", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthLessThan(BigDecimal value) {
            addCriterion("GOODS_WIDTH <", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthLessThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_WIDTH <=", value, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthIn(List<BigDecimal> values) {
            addCriterion("GOODS_WIDTH in", values, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthNotIn(List<BigDecimal> values) {
            addCriterion("GOODS_WIDTH not in", values, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_WIDTH between", value1, value2, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsWidthNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_WIDTH not between", value1, value2, "goodsWidth");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightIsNull() {
            addCriterion("GOODS_HEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightIsNotNull() {
            addCriterion("GOODS_HEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightEqualTo(BigDecimal value) {
            addCriterion("GOODS_HEIGHT =", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightNotEqualTo(BigDecimal value) {
            addCriterion("GOODS_HEIGHT <>", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightGreaterThan(BigDecimal value) {
            addCriterion("GOODS_HEIGHT >", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_HEIGHT >=", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightLessThan(BigDecimal value) {
            addCriterion("GOODS_HEIGHT <", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("GOODS_HEIGHT <=", value, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightIn(List<BigDecimal> values) {
            addCriterion("GOODS_HEIGHT in", values, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightNotIn(List<BigDecimal> values) {
            addCriterion("GOODS_HEIGHT not in", values, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_HEIGHT between", value1, value2, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andGoodsHeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GOODS_HEIGHT not between", value1, value2, "goodsHeight");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNull() {
            addCriterion("PACKAGE_LENGTH is null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNotNull() {
            addCriterion("PACKAGE_LENGTH is not null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH =", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH <>", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThan(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH >", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH >=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThan(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH <", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_LENGTH <=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_LENGTH in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_LENGTH not in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_LENGTH between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_LENGTH not between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNull() {
            addCriterion("PACKAGE_WIDTH is null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNotNull() {
            addCriterion("PACKAGE_WIDTH is not null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH =", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH <>", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThan(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH >", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH >=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThan(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH <", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_WIDTH <=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_WIDTH in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_WIDTH not in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_WIDTH between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_WIDTH not between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNull() {
            addCriterion("PACKAGE_HEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNotNull() {
            addCriterion("PACKAGE_HEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT =", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT <>", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThan(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT >", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT >=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThan(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT <", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PACKAGE_HEIGHT <=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_HEIGHT in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotIn(List<BigDecimal> values) {
            addCriterion("PACKAGE_HEIGHT not in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_HEIGHT between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PACKAGE_HEIGHT not between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightIsNull() {
            addCriterion("NET_WEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andNetWeightIsNotNull() {
            addCriterion("NET_WEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andNetWeightEqualTo(BigDecimal value) {
            addCriterion("NET_WEIGHT =", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightNotEqualTo(BigDecimal value) {
            addCriterion("NET_WEIGHT <>", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightGreaterThan(BigDecimal value) {
            addCriterion("NET_WEIGHT >", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("NET_WEIGHT >=", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightLessThan(BigDecimal value) {
            addCriterion("NET_WEIGHT <", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("NET_WEIGHT <=", value, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightIn(List<BigDecimal> values) {
            addCriterion("NET_WEIGHT in", values, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightNotIn(List<BigDecimal> values) {
            addCriterion("NET_WEIGHT not in", values, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("NET_WEIGHT between", value1, value2, "netWeight");
            return (Criteria) this;
        }

        public Criteria andNetWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("NET_WEIGHT not between", value1, value2, "netWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIsNull() {
            addCriterion("GROSS_WEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIsNotNull() {
            addCriterion("GROSS_WEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andGrossWeightEqualTo(BigDecimal value) {
            addCriterion("GROSS_WEIGHT =", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotEqualTo(BigDecimal value) {
            addCriterion("GROSS_WEIGHT <>", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightGreaterThan(BigDecimal value) {
            addCriterion("GROSS_WEIGHT >", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("GROSS_WEIGHT >=", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLessThan(BigDecimal value) {
            addCriterion("GROSS_WEIGHT <", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("GROSS_WEIGHT <=", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIn(List<BigDecimal> values) {
            addCriterion("GROSS_WEIGHT in", values, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotIn(List<BigDecimal> values) {
            addCriterion("GROSS_WEIGHT not in", values, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GROSS_WEIGHT between", value1, value2, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("GROSS_WEIGHT not between", value1, value2, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("UNIT_ID is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("UNIT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Integer value) {
            addCriterion("UNIT_ID =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Integer value) {
            addCriterion("UNIT_ID <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Integer value) {
            addCriterion("UNIT_ID >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("UNIT_ID >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Integer value) {
            addCriterion("UNIT_ID <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("UNIT_ID <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Integer> values) {
            addCriterion("UNIT_ID in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Integer> values) {
            addCriterion("UNIT_ID not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("UNIT_ID between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("UNIT_ID not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andChangeNumIsNull() {
            addCriterion("CHANGE_NUM is null");
            return (Criteria) this;
        }

        public Criteria andChangeNumIsNotNull() {
            addCriterion("CHANGE_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andChangeNumEqualTo(Long value) {
            addCriterion("CHANGE_NUM =", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumNotEqualTo(Long value) {
            addCriterion("CHANGE_NUM <>", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumGreaterThan(Long value) {
            addCriterion("CHANGE_NUM >", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumGreaterThanOrEqualTo(Long value) {
            addCriterion("CHANGE_NUM >=", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumLessThan(Long value) {
            addCriterion("CHANGE_NUM <", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumLessThanOrEqualTo(Long value) {
            addCriterion("CHANGE_NUM <=", value, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumIn(List<Long> values) {
            addCriterion("CHANGE_NUM in", values, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumNotIn(List<Long> values) {
            addCriterion("CHANGE_NUM not in", values, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumBetween(Long value1, Long value2) {
            addCriterion("CHANGE_NUM between", value1, value2, "changeNum");
            return (Criteria) this;
        }

        public Criteria andChangeNumNotBetween(Long value1, Long value2) {
            addCriterion("CHANGE_NUM not between", value1, value2, "changeNum");
            return (Criteria) this;
        }

        public Criteria andPackingListIsNull() {
            addCriterion("PACKING_LIST is null");
            return (Criteria) this;
        }

        public Criteria andPackingListIsNotNull() {
            addCriterion("PACKING_LIST is not null");
            return (Criteria) this;
        }

        public Criteria andPackingListEqualTo(String value) {
            addCriterion("PACKING_LIST =", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListNotEqualTo(String value) {
            addCriterion("PACKING_LIST <>", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListGreaterThan(String value) {
            addCriterion("PACKING_LIST >", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListGreaterThanOrEqualTo(String value) {
            addCriterion("PACKING_LIST >=", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListLessThan(String value) {
            addCriterion("PACKING_LIST <", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListLessThanOrEqualTo(String value) {
            addCriterion("PACKING_LIST <=", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListLike(String value) {
            addCriterion("PACKING_LIST like", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListNotLike(String value) {
            addCriterion("PACKING_LIST not like", value, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListIn(List<String> values) {
            addCriterion("PACKING_LIST in", values, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListNotIn(List<String> values) {
            addCriterion("PACKING_LIST not in", values, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListBetween(String value1, String value2) {
            addCriterion("PACKING_LIST between", value1, value2, "packingList");
            return (Criteria) this;
        }

        public Criteria andPackingListNotBetween(String value1, String value2) {
            addCriterion("PACKING_LIST not between", value1, value2, "packingList");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentIsNull() {
            addCriterion("AFTER_SALE_CONTENT is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentIsNotNull() {
            addCriterion("AFTER_SALE_CONTENT is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentEqualTo(String value) {
            addCriterion("AFTER_SALE_CONTENT =", value, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentNotEqualTo(String value) {
            addCriterion("AFTER_SALE_CONTENT <>", value, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentGreaterThan(String value) {
            addCriterion("AFTER_SALE_CONTENT >", value, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentGreaterThanOrEqualTo(String value) {
            addCriterion("AFTER_SALE_CONTENT >=", value, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentLessThan(String value) {
            addCriterion("AFTER_SALE_CONTENT <", value, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentLessThanOrEqualTo(String value) {
            addCriterion("AFTER_SALE_CONTENT <=", value, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentLike(String value) {
            addCriterion("AFTER_SALE_CONTENT like", value, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentNotLike(String value) {
            addCriterion("AFTER_SALE_CONTENT not like", value, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentIn(List<String> values) {
            addCriterion("AFTER_SALE_CONTENT in", values, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentNotIn(List<String> values) {
            addCriterion("AFTER_SALE_CONTENT not in", values, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentBetween(String value1, String value2) {
            addCriterion("AFTER_SALE_CONTENT between", value1, value2, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContentNotBetween(String value1, String value2) {
            addCriterion("AFTER_SALE_CONTENT not between", value1, value2, "afterSaleContent");
            return (Criteria) this;
        }

        public Criteria andQaYearsIsNull() {
            addCriterion("QA_YEARS is null");
            return (Criteria) this;
        }

        public Criteria andQaYearsIsNotNull() {
            addCriterion("QA_YEARS is not null");
            return (Criteria) this;
        }

        public Criteria andQaYearsEqualTo(String value) {
            addCriterion("QA_YEARS =", value, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsNotEqualTo(String value) {
            addCriterion("QA_YEARS <>", value, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsGreaterThan(String value) {
            addCriterion("QA_YEARS >", value, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsGreaterThanOrEqualTo(String value) {
            addCriterion("QA_YEARS >=", value, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsLessThan(String value) {
            addCriterion("QA_YEARS <", value, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsLessThanOrEqualTo(String value) {
            addCriterion("QA_YEARS <=", value, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsLike(String value) {
            addCriterion("QA_YEARS like", value, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsNotLike(String value) {
            addCriterion("QA_YEARS not like", value, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsIn(List<String> values) {
            addCriterion("QA_YEARS in", values, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsNotIn(List<String> values) {
            addCriterion("QA_YEARS not in", values, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsBetween(String value1, String value2) {
            addCriterion("QA_YEARS between", value1, value2, "qaYears");
            return (Criteria) this;
        }

        public Criteria andQaYearsNotBetween(String value1, String value2) {
            addCriterion("QA_YEARS not between", value1, value2, "qaYears");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneIsNull() {
            addCriterion("STORAGE_CONDITION_ONE is null");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneIsNotNull() {
            addCriterion("STORAGE_CONDITION_ONE is not null");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneEqualTo(Integer value) {
            addCriterion("STORAGE_CONDITION_ONE =", value, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneNotEqualTo(Integer value) {
            addCriterion("STORAGE_CONDITION_ONE <>", value, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneGreaterThan(Integer value) {
            addCriterion("STORAGE_CONDITION_ONE >", value, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneGreaterThanOrEqualTo(Integer value) {
            addCriterion("STORAGE_CONDITION_ONE >=", value, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneLessThan(Integer value) {
            addCriterion("STORAGE_CONDITION_ONE <", value, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneLessThanOrEqualTo(Integer value) {
            addCriterion("STORAGE_CONDITION_ONE <=", value, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneIn(List<Integer> values) {
            addCriterion("STORAGE_CONDITION_ONE in", values, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneNotIn(List<Integer> values) {
            addCriterion("STORAGE_CONDITION_ONE not in", values, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneBetween(Integer value1, Integer value2) {
            addCriterion("STORAGE_CONDITION_ONE between", value1, value2, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionOneNotBetween(Integer value1, Integer value2) {
            addCriterion("STORAGE_CONDITION_ONE not between", value1, value2, "storageConditionOne");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoIsNull() {
            addCriterion("STORAGE_CONDITION_TWO is null");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoIsNotNull() {
            addCriterion("STORAGE_CONDITION_TWO is not null");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoEqualTo(String value) {
            addCriterion("STORAGE_CONDITION_TWO =", value, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoNotEqualTo(String value) {
            addCriterion("STORAGE_CONDITION_TWO <>", value, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoGreaterThan(String value) {
            addCriterion("STORAGE_CONDITION_TWO >", value, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoGreaterThanOrEqualTo(String value) {
            addCriterion("STORAGE_CONDITION_TWO >=", value, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoLessThan(String value) {
            addCriterion("STORAGE_CONDITION_TWO <", value, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoLessThanOrEqualTo(String value) {
            addCriterion("STORAGE_CONDITION_TWO <=", value, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoLike(String value) {
            addCriterion("STORAGE_CONDITION_TWO like", value, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoNotLike(String value) {
            addCriterion("STORAGE_CONDITION_TWO not like", value, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoIn(List<String> values) {
            addCriterion("STORAGE_CONDITION_TWO in", values, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoNotIn(List<String> values) {
            addCriterion("STORAGE_CONDITION_TWO not in", values, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoBetween(String value1, String value2) {
            addCriterion("STORAGE_CONDITION_TWO between", value1, value2, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andStorageConditionTwoNotBetween(String value1, String value2) {
            addCriterion("STORAGE_CONDITION_TWO not between", value1, value2, "storageConditionTwo");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitIsNull() {
            addCriterion("EFFECTIVE_DAY_UNIT is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitIsNotNull() {
            addCriterion("EFFECTIVE_DAY_UNIT is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitEqualTo(Integer value) {
            addCriterion("EFFECTIVE_DAY_UNIT =", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitNotEqualTo(Integer value) {
            addCriterion("EFFECTIVE_DAY_UNIT <>", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitGreaterThan(Integer value) {
            addCriterion("EFFECTIVE_DAY_UNIT >", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("EFFECTIVE_DAY_UNIT >=", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitLessThan(Integer value) {
            addCriterion("EFFECTIVE_DAY_UNIT <", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitLessThanOrEqualTo(Integer value) {
            addCriterion("EFFECTIVE_DAY_UNIT <=", value, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitIn(List<Integer> values) {
            addCriterion("EFFECTIVE_DAY_UNIT in", values, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitNotIn(List<Integer> values) {
            addCriterion("EFFECTIVE_DAY_UNIT not in", values, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitBetween(Integer value1, Integer value2) {
            addCriterion("EFFECTIVE_DAY_UNIT between", value1, value2, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDayUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("EFFECTIVE_DAY_UNIT not between", value1, value2, "effectiveDayUnit");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIsNull() {
            addCriterion("EFFECTIVE_DAYS is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIsNotNull() {
            addCriterion("EFFECTIVE_DAYS is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysEqualTo(String value) {
            addCriterion("EFFECTIVE_DAYS =", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotEqualTo(String value) {
            addCriterion("EFFECTIVE_DAYS <>", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysGreaterThan(String value) {
            addCriterion("EFFECTIVE_DAYS >", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysGreaterThanOrEqualTo(String value) {
            addCriterion("EFFECTIVE_DAYS >=", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysLessThan(String value) {
            addCriterion("EFFECTIVE_DAYS <", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysLessThanOrEqualTo(String value) {
            addCriterion("EFFECTIVE_DAYS <=", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysLike(String value) {
            addCriterion("EFFECTIVE_DAYS like", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotLike(String value) {
            addCriterion("EFFECTIVE_DAYS not like", value, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysIn(List<String> values) {
            addCriterion("EFFECTIVE_DAYS in", values, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotIn(List<String> values) {
            addCriterion("EFFECTIVE_DAYS not in", values, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysBetween(String value1, String value2) {
            addCriterion("EFFECTIVE_DAYS between", value1, value2, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andEffectiveDaysNotBetween(String value1, String value2) {
            addCriterion("EFFECTIVE_DAYS not between", value1, value2, "effectiveDays");
            return (Criteria) this;
        }

        public Criteria andQaRuleIsNull() {
            addCriterion("QA_RULE is null");
            return (Criteria) this;
        }

        public Criteria andQaRuleIsNotNull() {
            addCriterion("QA_RULE is not null");
            return (Criteria) this;
        }

        public Criteria andQaRuleEqualTo(String value) {
            addCriterion("QA_RULE =", value, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleNotEqualTo(String value) {
            addCriterion("QA_RULE <>", value, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleGreaterThan(String value) {
            addCriterion("QA_RULE >", value, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleGreaterThanOrEqualTo(String value) {
            addCriterion("QA_RULE >=", value, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleLessThan(String value) {
            addCriterion("QA_RULE <", value, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleLessThanOrEqualTo(String value) {
            addCriterion("QA_RULE <=", value, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleLike(String value) {
            addCriterion("QA_RULE like", value, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleNotLike(String value) {
            addCriterion("QA_RULE not like", value, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleIn(List<String> values) {
            addCriterion("QA_RULE in", values, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleNotIn(List<String> values) {
            addCriterion("QA_RULE not in", values, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleBetween(String value1, String value2) {
            addCriterion("QA_RULE between", value1, value2, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaRuleNotBetween(String value1, String value2) {
            addCriterion("QA_RULE not between", value1, value2, "qaRule");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceIsNull() {
            addCriterion("QA_OUT_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceIsNotNull() {
            addCriterion("QA_OUT_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceEqualTo(BigDecimal value) {
            addCriterion("QA_OUT_PRICE =", value, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceNotEqualTo(BigDecimal value) {
            addCriterion("QA_OUT_PRICE <>", value, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceGreaterThan(BigDecimal value) {
            addCriterion("QA_OUT_PRICE >", value, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("QA_OUT_PRICE >=", value, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceLessThan(BigDecimal value) {
            addCriterion("QA_OUT_PRICE <", value, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("QA_OUT_PRICE <=", value, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceIn(List<BigDecimal> values) {
            addCriterion("QA_OUT_PRICE in", values, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceNotIn(List<BigDecimal> values) {
            addCriterion("QA_OUT_PRICE not in", values, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("QA_OUT_PRICE between", value1, value2, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaOutPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("QA_OUT_PRICE not between", value1, value2, "qaOutPrice");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeIsNull() {
            addCriterion("QA_RESPONSE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeIsNotNull() {
            addCriterion("QA_RESPONSE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeEqualTo(Long value) {
            addCriterion("QA_RESPONSE_TIME =", value, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeNotEqualTo(Long value) {
            addCriterion("QA_RESPONSE_TIME <>", value, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeGreaterThan(Long value) {
            addCriterion("QA_RESPONSE_TIME >", value, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("QA_RESPONSE_TIME >=", value, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeLessThan(Long value) {
            addCriterion("QA_RESPONSE_TIME <", value, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeLessThanOrEqualTo(Long value) {
            addCriterion("QA_RESPONSE_TIME <=", value, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeIn(List<Long> values) {
            addCriterion("QA_RESPONSE_TIME in", values, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeNotIn(List<Long> values) {
            addCriterion("QA_RESPONSE_TIME not in", values, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeBetween(Long value1, Long value2) {
            addCriterion("QA_RESPONSE_TIME between", value1, value2, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andQaResponseTimeNotBetween(Long value1, Long value2) {
            addCriterion("QA_RESPONSE_TIME not between", value1, value2, "qaResponseTime");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineIsNull() {
            addCriterion("HAS_BACKUP_MACHINE is null");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineIsNotNull() {
            addCriterion("HAS_BACKUP_MACHINE is not null");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineEqualTo(String value) {
            addCriterion("HAS_BACKUP_MACHINE =", value, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineNotEqualTo(String value) {
            addCriterion("HAS_BACKUP_MACHINE <>", value, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineGreaterThan(String value) {
            addCriterion("HAS_BACKUP_MACHINE >", value, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineGreaterThanOrEqualTo(String value) {
            addCriterion("HAS_BACKUP_MACHINE >=", value, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineLessThan(String value) {
            addCriterion("HAS_BACKUP_MACHINE <", value, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineLessThanOrEqualTo(String value) {
            addCriterion("HAS_BACKUP_MACHINE <=", value, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineLike(String value) {
            addCriterion("HAS_BACKUP_MACHINE like", value, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineNotLike(String value) {
            addCriterion("HAS_BACKUP_MACHINE not like", value, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineIn(List<String> values) {
            addCriterion("HAS_BACKUP_MACHINE in", values, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineNotIn(List<String> values) {
            addCriterion("HAS_BACKUP_MACHINE not in", values, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineBetween(String value1, String value2) {
            addCriterion("HAS_BACKUP_MACHINE between", value1, value2, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andHasBackupMachineNotBetween(String value1, String value2) {
            addCriterion("HAS_BACKUP_MACHINE not between", value1, value2, "hasBackupMachine");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceIsNull() {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceIsNotNull() {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceEqualTo(BigDecimal value) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE =", value, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceNotEqualTo(BigDecimal value) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE <>", value, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceGreaterThan(BigDecimal value) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE >", value, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE >=", value, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceLessThan(BigDecimal value) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE <", value, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE <=", value, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceIn(List<BigDecimal> values) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE in", values, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceNotIn(List<BigDecimal> values) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE not in", values, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE between", value1, value2, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andSupplierExtendGuaranteePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("SUPPLIER_EXTEND_GUARANTEE_PRICE not between", value1, value2, "supplierExtendGuaranteePrice");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidIsNull() {
            addCriterion("CORE_PARTS_PRICE_FID is null");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidIsNotNull() {
            addCriterion("CORE_PARTS_PRICE_FID is not null");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidEqualTo(Integer value) {
            addCriterion("CORE_PARTS_PRICE_FID =", value, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidNotEqualTo(Integer value) {
            addCriterion("CORE_PARTS_PRICE_FID <>", value, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidGreaterThan(Integer value) {
            addCriterion("CORE_PARTS_PRICE_FID >", value, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidGreaterThanOrEqualTo(Integer value) {
            addCriterion("CORE_PARTS_PRICE_FID >=", value, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidLessThan(Integer value) {
            addCriterion("CORE_PARTS_PRICE_FID <", value, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidLessThanOrEqualTo(Integer value) {
            addCriterion("CORE_PARTS_PRICE_FID <=", value, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidIn(List<Integer> values) {
            addCriterion("CORE_PARTS_PRICE_FID in", values, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidNotIn(List<Integer> values) {
            addCriterion("CORE_PARTS_PRICE_FID not in", values, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidBetween(Integer value1, Integer value2) {
            addCriterion("CORE_PARTS_PRICE_FID between", value1, value2, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andCorePartsPriceFidNotBetween(Integer value1, Integer value2) {
            addCriterion("CORE_PARTS_PRICE_FID not between", value1, value2, "corePartsPriceFid");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsIsNull() {
            addCriterion("RETURN_GOODS_CONDITIONS is null");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsIsNotNull() {
            addCriterion("RETURN_GOODS_CONDITIONS is not null");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsEqualTo(Integer value) {
            addCriterion("RETURN_GOODS_CONDITIONS =", value, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsNotEqualTo(Integer value) {
            addCriterion("RETURN_GOODS_CONDITIONS <>", value, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsGreaterThan(Integer value) {
            addCriterion("RETURN_GOODS_CONDITIONS >", value, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsGreaterThanOrEqualTo(Integer value) {
            addCriterion("RETURN_GOODS_CONDITIONS >=", value, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsLessThan(Integer value) {
            addCriterion("RETURN_GOODS_CONDITIONS <", value, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsLessThanOrEqualTo(Integer value) {
            addCriterion("RETURN_GOODS_CONDITIONS <=", value, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsIn(List<Integer> values) {
            addCriterion("RETURN_GOODS_CONDITIONS in", values, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsNotIn(List<Integer> values) {
            addCriterion("RETURN_GOODS_CONDITIONS not in", values, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsBetween(Integer value1, Integer value2) {
            addCriterion("RETURN_GOODS_CONDITIONS between", value1, value2, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andReturnGoodsConditionsNotBetween(Integer value1, Integer value2) {
            addCriterion("RETURN_GOODS_CONDITIONS not between", value1, value2, "returnGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsIsNull() {
            addCriterion("FREIGHT_INTRODUCTIONS is null");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsIsNotNull() {
            addCriterion("FREIGHT_INTRODUCTIONS is not null");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsEqualTo(String value) {
            addCriterion("FREIGHT_INTRODUCTIONS =", value, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsNotEqualTo(String value) {
            addCriterion("FREIGHT_INTRODUCTIONS <>", value, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsGreaterThan(String value) {
            addCriterion("FREIGHT_INTRODUCTIONS >", value, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsGreaterThanOrEqualTo(String value) {
            addCriterion("FREIGHT_INTRODUCTIONS >=", value, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsLessThan(String value) {
            addCriterion("FREIGHT_INTRODUCTIONS <", value, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsLessThanOrEqualTo(String value) {
            addCriterion("FREIGHT_INTRODUCTIONS <=", value, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsLike(String value) {
            addCriterion("FREIGHT_INTRODUCTIONS like", value, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsNotLike(String value) {
            addCriterion("FREIGHT_INTRODUCTIONS not like", value, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsIn(List<String> values) {
            addCriterion("FREIGHT_INTRODUCTIONS in", values, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsNotIn(List<String> values) {
            addCriterion("FREIGHT_INTRODUCTIONS not in", values, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsBetween(String value1, String value2) {
            addCriterion("FREIGHT_INTRODUCTIONS between", value1, value2, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andFreightIntroductionsNotBetween(String value1, String value2) {
            addCriterion("FREIGHT_INTRODUCTIONS not between", value1, value2, "freightIntroductions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsIsNull() {
            addCriterion("EXCHANGE_GOODS_CONDITIONS is null");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsIsNotNull() {
            addCriterion("EXCHANGE_GOODS_CONDITIONS is not null");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsEqualTo(String value) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS =", value, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsNotEqualTo(String value) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS <>", value, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsGreaterThan(String value) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS >", value, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsGreaterThanOrEqualTo(String value) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS >=", value, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsLessThan(String value) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS <", value, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsLessThanOrEqualTo(String value) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS <=", value, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsLike(String value) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS like", value, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsNotLike(String value) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS not like", value, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsIn(List<String> values) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS in", values, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsNotIn(List<String> values) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS not in", values, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsBetween(String value1, String value2) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS between", value1, value2, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsConditionsNotBetween(String value1, String value2) {
            addCriterion("EXCHANGE_GOODS_CONDITIONS not between", value1, value2, "exchangeGoodsConditions");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodIsNull() {
            addCriterion("EXCHANGE_GOODS_METHOD is null");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodIsNotNull() {
            addCriterion("EXCHANGE_GOODS_METHOD is not null");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodEqualTo(String value) {
            addCriterion("EXCHANGE_GOODS_METHOD =", value, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodNotEqualTo(String value) {
            addCriterion("EXCHANGE_GOODS_METHOD <>", value, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodGreaterThan(String value) {
            addCriterion("EXCHANGE_GOODS_METHOD >", value, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodGreaterThanOrEqualTo(String value) {
            addCriterion("EXCHANGE_GOODS_METHOD >=", value, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodLessThan(String value) {
            addCriterion("EXCHANGE_GOODS_METHOD <", value, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodLessThanOrEqualTo(String value) {
            addCriterion("EXCHANGE_GOODS_METHOD <=", value, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodLike(String value) {
            addCriterion("EXCHANGE_GOODS_METHOD like", value, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodNotLike(String value) {
            addCriterion("EXCHANGE_GOODS_METHOD not like", value, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodIn(List<String> values) {
            addCriterion("EXCHANGE_GOODS_METHOD in", values, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodNotIn(List<String> values) {
            addCriterion("EXCHANGE_GOODS_METHOD not in", values, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodBetween(String value1, String value2) {
            addCriterion("EXCHANGE_GOODS_METHOD between", value1, value2, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andExchangeGoodsMethodNotBetween(String value1, String value2) {
            addCriterion("EXCHANGE_GOODS_METHOD not between", value1, value2, "exchangeGoodsMethod");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsIsNull() {
            addCriterion("GOODS_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsIsNotNull() {
            addCriterion("GOODS_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsEqualTo(String value) {
            addCriterion("GOODS_COMMENTS =", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsNotEqualTo(String value) {
            addCriterion("GOODS_COMMENTS <>", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsGreaterThan(String value) {
            addCriterion("GOODS_COMMENTS >", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("GOODS_COMMENTS >=", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsLessThan(String value) {
            addCriterion("GOODS_COMMENTS <", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsLessThanOrEqualTo(String value) {
            addCriterion("GOODS_COMMENTS <=", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsLike(String value) {
            addCriterion("GOODS_COMMENTS like", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsNotLike(String value) {
            addCriterion("GOODS_COMMENTS not like", value, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsIn(List<String> values) {
            addCriterion("GOODS_COMMENTS in", values, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsNotIn(List<String> values) {
            addCriterion("GOODS_COMMENTS not in", values, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsBetween(String value1, String value2) {
            addCriterion("GOODS_COMMENTS between", value1, value2, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andGoodsCommentsNotBetween(String value1, String value2) {
            addCriterion("GOODS_COMMENTS not between", value1, value2, "goodsComments");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`STATUS` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`STATUS` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`STATUS` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`STATUS` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`STATUS` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`STATUS` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`STATUS` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`STATUS` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Date value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Date value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Date value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Date value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Date> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Date> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIsNull() {
            addCriterion("CHECK_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIsNotNull() {
            addCriterion("CHECK_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCheckTimeEqualTo(Date value) {
            addCriterion("CHECK_TIME =", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotEqualTo(Date value) {
            addCriterion("CHECK_TIME <>", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeGreaterThan(Date value) {
            addCriterion("CHECK_TIME >", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CHECK_TIME >=", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeLessThan(Date value) {
            addCriterion("CHECK_TIME <", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeLessThanOrEqualTo(Date value) {
            addCriterion("CHECK_TIME <=", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIn(List<Date> values) {
            addCriterion("CHECK_TIME in", values, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotIn(List<Date> values) {
            addCriterion("CHECK_TIME not in", values, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeBetween(Date value1, Date value2) {
            addCriterion("CHECK_TIME between", value1, value2, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotBetween(Date value1, Date value2) {
            addCriterion("CHECK_TIME not between", value1, value2, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckerIsNull() {
            addCriterion("CHECKER is null");
            return (Criteria) this;
        }

        public Criteria andCheckerIsNotNull() {
            addCriterion("CHECKER is not null");
            return (Criteria) this;
        }

        public Criteria andCheckerEqualTo(Integer value) {
            addCriterion("CHECKER =", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerNotEqualTo(Integer value) {
            addCriterion("CHECKER <>", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerGreaterThan(Integer value) {
            addCriterion("CHECKER >", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerGreaterThanOrEqualTo(Integer value) {
            addCriterion("CHECKER >=", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerLessThan(Integer value) {
            addCriterion("CHECKER <", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerLessThanOrEqualTo(Integer value) {
            addCriterion("CHECKER <=", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerIn(List<Integer> values) {
            addCriterion("CHECKER in", values, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerNotIn(List<Integer> values) {
            addCriterion("CHECKER not in", values, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerBetween(Integer value1, Integer value2) {
            addCriterion("CHECKER between", value1, value2, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerNotBetween(Integer value1, Integer value2) {
            addCriterion("CHECKER not between", value1, value2, "checker");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdIsNull() {
            addCriterion("OPERATE_INFO_ID is null");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdIsNotNull() {
            addCriterion("OPERATE_INFO_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_ID =", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdNotEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_ID <>", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdGreaterThan(Integer value) {
            addCriterion("OPERATE_INFO_ID >", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_ID >=", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdLessThan(Integer value) {
            addCriterion("OPERATE_INFO_ID <", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdLessThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_ID <=", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdIn(List<Integer> values) {
            addCriterion("OPERATE_INFO_ID in", values, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdNotIn(List<Integer> values) {
            addCriterion("OPERATE_INFO_ID not in", values, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_INFO_ID between", value1, value2, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdNotBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_INFO_ID not between", value1, value2, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonIsNull() {
            addCriterion("DELETE_REASON is null");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonIsNotNull() {
            addCriterion("DELETE_REASON is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonEqualTo(String value) {
            addCriterion("DELETE_REASON =", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonNotEqualTo(String value) {
            addCriterion("DELETE_REASON <>", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonGreaterThan(String value) {
            addCriterion("DELETE_REASON >", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonGreaterThanOrEqualTo(String value) {
            addCriterion("DELETE_REASON >=", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonLessThan(String value) {
            addCriterion("DELETE_REASON <", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonLessThanOrEqualTo(String value) {
            addCriterion("DELETE_REASON <=", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonLike(String value) {
            addCriterion("DELETE_REASON like", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonNotLike(String value) {
            addCriterion("DELETE_REASON not like", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonIn(List<String> values) {
            addCriterion("DELETE_REASON in", values, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonNotIn(List<String> values) {
            addCriterion("DELETE_REASON not in", values, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonBetween(String value1, String value2) {
            addCriterion("DELETE_REASON between", value1, value2, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonNotBetween(String value1, String value2) {
            addCriterion("DELETE_REASON not between", value1, value2, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonIsNull() {
            addCriterion("LAST_CHECK_REASON is null");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonIsNotNull() {
            addCriterion("LAST_CHECK_REASON is not null");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonEqualTo(String value) {
            addCriterion("LAST_CHECK_REASON =", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonNotEqualTo(String value) {
            addCriterion("LAST_CHECK_REASON <>", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonGreaterThan(String value) {
            addCriterion("LAST_CHECK_REASON >", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonGreaterThanOrEqualTo(String value) {
            addCriterion("LAST_CHECK_REASON >=", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonLessThan(String value) {
            addCriterion("LAST_CHECK_REASON <", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonLessThanOrEqualTo(String value) {
            addCriterion("LAST_CHECK_REASON <=", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonLike(String value) {
            addCriterion("LAST_CHECK_REASON like", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonNotLike(String value) {
            addCriterion("LAST_CHECK_REASON not like", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonIn(List<String> values) {
            addCriterion("LAST_CHECK_REASON in", values, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonNotIn(List<String> values) {
            addCriterion("LAST_CHECK_REASON not in", values, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonBetween(String value1, String value2) {
            addCriterion("LAST_CHECK_REASON between", value1, value2, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonNotBetween(String value1, String value2) {
            addCriterion("LAST_CHECK_REASON not between", value1, value2, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoIsNull() {
            addCriterion("TAX_CATEGORY_NO is null");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoIsNotNull() {
            addCriterion("TAX_CATEGORY_NO is not null");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoEqualTo(String value) {
            addCriterion("TAX_CATEGORY_NO =", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoNotEqualTo(String value) {
            addCriterion("TAX_CATEGORY_NO <>", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoGreaterThan(String value) {
            addCriterion("TAX_CATEGORY_NO >", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoGreaterThanOrEqualTo(String value) {
            addCriterion("TAX_CATEGORY_NO >=", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoLessThan(String value) {
            addCriterion("TAX_CATEGORY_NO <", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoLessThanOrEqualTo(String value) {
            addCriterion("TAX_CATEGORY_NO <=", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoLike(String value) {
            addCriterion("TAX_CATEGORY_NO like", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoNotLike(String value) {
            addCriterion("TAX_CATEGORY_NO not like", value, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoIn(List<String> values) {
            addCriterion("TAX_CATEGORY_NO in", values, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoNotIn(List<String> values) {
            addCriterion("TAX_CATEGORY_NO not in", values, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoBetween(String value1, String value2) {
            addCriterion("TAX_CATEGORY_NO between", value1, value2, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andTaxCategoryNoNotBetween(String value1, String value2) {
            addCriterion("TAX_CATEGORY_NO not between", value1, value2, "taxCategoryNo");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceIsNull() {
            addCriterion("JX_MARKET_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceIsNotNull() {
            addCriterion("JX_MARKET_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceEqualTo(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE =", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceNotEqualTo(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE <>", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceGreaterThan(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE >", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE >=", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceLessThan(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE <", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("JX_MARKET_PRICE <=", value, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceIn(List<BigDecimal> values) {
            addCriterion("JX_MARKET_PRICE in", values, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceNotIn(List<BigDecimal> values) {
            addCriterion("JX_MARKET_PRICE not in", values, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("JX_MARKET_PRICE between", value1, value2, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxMarketPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("JX_MARKET_PRICE not between", value1, value2, "jxMarketPrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceIsNull() {
            addCriterion("JX_SALE_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceIsNotNull() {
            addCriterion("JX_SALE_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceEqualTo(BigDecimal value) {
            addCriterion("JX_SALE_PRICE =", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceNotEqualTo(BigDecimal value) {
            addCriterion("JX_SALE_PRICE <>", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceGreaterThan(BigDecimal value) {
            addCriterion("JX_SALE_PRICE >", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("JX_SALE_PRICE >=", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceLessThan(BigDecimal value) {
            addCriterion("JX_SALE_PRICE <", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("JX_SALE_PRICE <=", value, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceIn(List<BigDecimal> values) {
            addCriterion("JX_SALE_PRICE in", values, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceNotIn(List<BigDecimal> values) {
            addCriterion("JX_SALE_PRICE not in", values, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("JX_SALE_PRICE between", value1, value2, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxSalePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("JX_SALE_PRICE not between", value1, value2, "jxSalePrice");
            return (Criteria) this;
        }

        public Criteria andJxFlagIsNull() {
            addCriterion("JX_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andJxFlagIsNotNull() {
            addCriterion("JX_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andJxFlagEqualTo(Integer value) {
            addCriterion("JX_FLAG =", value, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagNotEqualTo(Integer value) {
            addCriterion("JX_FLAG <>", value, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagGreaterThan(Integer value) {
            addCriterion("JX_FLAG >", value, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("JX_FLAG >=", value, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagLessThan(Integer value) {
            addCriterion("JX_FLAG <", value, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagLessThanOrEqualTo(Integer value) {
            addCriterion("JX_FLAG <=", value, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagIn(List<Integer> values) {
            addCriterion("JX_FLAG in", values, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagNotIn(List<Integer> values) {
            addCriterion("JX_FLAG not in", values, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagBetween(Integer value1, Integer value2) {
            addCriterion("JX_FLAG between", value1, value2, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andJxFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("JX_FLAG not between", value1, value2, "jxFlag");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("`SOURCE` is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("`SOURCE` is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("`SOURCE` =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("`SOURCE` <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("`SOURCE` >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("`SOURCE` >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("`SOURCE` <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("`SOURCE` <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("`SOURCE` in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("`SOURCE` not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("`SOURCE` between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("`SOURCE` not between", value1, value2, "source");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated do_not_delete_during_merge Sat Aug 10 17:13:52 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SKU
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}