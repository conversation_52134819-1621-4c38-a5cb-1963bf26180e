function deliveryOrderReturnSubmit () {
	checkLogin();
	var customerNature = $("#customerNature").val();
	if(customerNature== 0){
		layer.alert("请选择客户性质",{ icon: 2 });
		return;
	}

	//间接客户，
	if(customerNature==2){
		//
		var customerSecondType = $("#customerSecondType").val();
		if(customerSecondType ==0){
			layer.alert("请选择客户细分类",{ icon: 2 });
			return;
		}
		if(customerSecondType !=3){
			layer.alert("间接客户，客户细分类只能选择分销商",{ icon: 2 });
			return;
		}

	}
	//直接客户
	if(customerNature==1){

		//终端客户分类
		var customerClass = $("#customerClass").val();
		if(customerClass==0){
			layer.alert("请选择终端客户分类",{ icon: 2 });
			return;
		}
		//客户细分类
		var customerSecondType = $("#customerSecondType").val();
		if(customerSecondType==0){
			layer.alert("请选择客户细分类",{ icon: 2 });
			return;
		}
		//客户细分类如果为医疗卫生机构，医疗机构分类不能为空
		if(customerSecondType==1){
			var customerThirdType = $("#customerThirdType").val();
			if(customerThirdType==0){
				layer.alert("请选择医疗机构分类",{ icon: 2 });
				return;
			}
			//医疗机构分类如果为医院，医院等级不可为空
			if(customerThirdType==1){
				var hospitalLever = $("#hospitalLever").val();
				if(hospitalLever==0){
					layer.alert("请选择医院等级",{ icon: 2 });
					return;
				}
			}
		}
		//直接客户不能选择分销商
		if(customerSecondType ==3){
			layer.alert("直接客户，客户细分类只能选择医疗卫生机构或者非医疗卫生机构",{ icon: 2 });
			return;
		}
	}


	$.ajax({
		url:'./save.do',
		data:$('#traderFinanciaForm').serialize(),
		type:"POST",
		dataType : "json",
		async: false,
		success:function(data)
		{
			if(data.code==0){
				window.parent.location.reload();
			}else{
				layer.alert(data.message);
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}else{
				layer.alert(data.message);
			}
		}
	});

}

//初始只有一个客户性质
//直接客户 显示终端客户分类 和 客户细分类
//间接客户 显示客户细分类
//客户性质 customerNature
//终端客户分类 customerClass
//所属集团 groupName
//客户细分类 customerSecondType
//医疗机构分类 customerThirdType
//医院等级  hospitalLever
//所属医院共体 hospitalName


//客户性质选择
function customerNatureSelect() {
	var customerNature = $("#customerNature").val();
	//间接客户
	if(customerNature==2){
		//客户细分类
		$('#customerSecondType').val(0);
		$('#customerSecondTypeLi').show();
		$('#customerClass').val(0);
		$('#customerClassLi').hide();
		$('#groupName').val('');
		$('#groupNameLi').hide();
		$('#customerThirdType').val(0);
		$('#customerThirdTypeLi').hide();
		$('#hospitalLever').val(0);
		$('#hospitalLeverLi').hide();
		$('#hospitalName').val('');
		$('#hospitalNameLi').hide();
	}else{
		//客户细分类
		$('#customerSecondType').val(0);
		$('#customerSecondTypeLi').show();
		$('#customerClass').val(0);
		//终端客户分类
		$('#customerClassLi').show();
		$('#groupName').val('');
		$('#groupNameLi').hide();
		$('#customerThirdType').val(0);
		$('#customerThirdTypeLi').hide();
		$('#hospitalLever').val(0);
		$('#hospitalLeverLi').hide();
		$('#hospitalName').val('');
		$('#hospitalNameLi').hide();
	}
}

//客户细分类
function customerSecondTypeSelect(){
	var customerSecondType = $("#customerSecondType").val();
	//分销商、非医疗卫生机构
	if(customerSecondType==3 || customerSecondType==2){
		$("#customerThirdType").val(0);
		$('#customerThirdTypeLi').hide();
		$('#hospitalLever').val(0);
		$('#hospitalLeverLi').hide();
		$('#hospitalName').val('');
		$('#hospitalNameLi').hide();
	}
	//
	else if(customerSecondType==1){
		$('#customerThirdType').val(0);
		$('#customerThirdTypeLi').show();
		$('#hospitalLever').val(0);
		$('#hospitalLeverLi').hide();
		$('#hospitalName').val('');
		$('#hospitalNameLi').hide();
	}
}

//终端客户分类
function customerClassSelect(){
	var customerClass = $("#customerClass").val();
	if(customerClass==3){
		$('#groupName').val('');
		$('#groupNameLi').show();
	}else{
		$('#groupName').val('');
		$('#groupNameLi').hide();
	}
}

//医疗机构分类
function customerThirdTypeSelect(){
	var customerThirdType = $("#customerThirdType").val();
	//医院
	if(customerThirdType==1){
		$('#hospitalLever').val(0);
		$('#hospitalLeverLi').show();
		$('#hospitalName').val('');
		$('#hospitalNameLi').show();
	}else{
		$('#hospitalLever').val(0);
		$('#hospitalLeverLi').hide();
		$('#hospitalName').val('');
		$('#hospitalNameLi').hide();
	}
}

















