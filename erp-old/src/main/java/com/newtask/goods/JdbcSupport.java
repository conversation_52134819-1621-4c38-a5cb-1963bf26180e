package com.newtask.goods;

import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PreDestroy;
import javax.sql.DataSource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> [<EMAIL>]
 */
public abstract class JdbcSupport extends AbstractJobHandler implements InitializingBean {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    private final static int DEFAULT_MAX_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;

    private PlatformTransactionManager transactionManager;

    protected final static int DEFAULT_TASK_SIZE = 1000;

    protected JdbcTemplate jdbcTemplate;

    final AtomicInteger count = new AtomicInteger(0);

    protected ThreadPoolExecutor executorService;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if (isThreadPoolClosed()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "The internal thread pool was closed");
        }

        try {
            processData();
        } catch (Exception e) {
            logger.error("定时任务job:{}, 更新历史记录失败", getJobName(), e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 批量跟新数据表存储条件历史记录
     */
    protected abstract void processData();

    protected abstract String getJobName();

    protected PlatformTransactionManager getTransactionManager() {
        Objects.requireNonNull(transactionManager, "transactionManager is null");
        return transactionManager;
    }

    protected int countTableTotalEntry(DBTableType table) {
        return jdbcTemplate.queryForObject("select count(*) from " + table.getTableName(), Integer.class);
    }

    protected int countTableTotalEntry(String sql) {
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }


    protected <T> List<T> query(String sql, RowMapper<T> rowMapper) {
        return jdbcTemplate.query(sql, rowMapper);
    }

    protected int doBatchUpdateWithSqlList(List<String> sqlListToUpdate) {
        if (sqlListToUpdate == null || sqlListToUpdate.isEmpty()) {
            return 0;
        }

        int[] updatedRowsResult = jdbcTemplate.batchUpdate(sqlListToUpdate.toArray(new String[0]));
        int actualUpdatedRowCount = 0;
        for (int updatedRowsFromEach : updatedRowsResult) {
            actualUpdatedRowCount += updatedRowsFromEach;
        }

        if (sqlListToUpdate.size() != actualUpdatedRowCount) {
            logger.warn("更新数据条与实际变动条数不一致，expectCount: {}, actualCount: {}", sqlListToUpdate.size(), actualUpdatedRowCount);
        }

        return actualUpdatedRowCount;
    }


    @SuppressWarnings("all")
    @Override
    public void afterPropertiesSet() throws Exception {
        Object returnedBean = SpringContextHolder.getBean("dataSourceTarget");
        if (!(returnedBean instanceof DataSource)) {
            throw new IllegalStateException("Not found beanName: dataSourceTarget from the bean container ");
        }
        DataSource dataSource = (DataSource) returnedBean;
        jdbcTemplate = new JdbcTemplate(dataSource);
        transactionManager = new DataSourceTransactionManager(dataSource);
        executorService = new ThreadPoolExecutor(DEFAULT_MAX_POOL_SIZE/2, DEFAULT_MAX_POOL_SIZE, 10, TimeUnit.MINUTES, new LinkedBlockingQueue<>());
        executorService.allowCoreThreadTimeOut(true);
    }

    private boolean isThreadPoolClosed() {
        return executorService.isShutdown();
    }


    /**
     * Releases the internal thread pool.
     */
    @PreDestroy
    public void releaseThreadPool() {
        if (!isThreadPoolClosed()) {
            executorService.shutdownNow();
        }
    }

    protected abstract class BaseSQLOperationTask implements Runnable {

        protected final int offset;
        protected final int size;
        private final CountDownLatch countDownLatch;

        public BaseSQLOperationTask(int offset, int size, CountDownLatch countDownLatch) {
            if (offset < 0 || size < 0) {
                throw new IllegalArgumentException("offset or size is not positive");
            }
            this.offset = offset;
            this.size = size;
            this.countDownLatch = countDownLatch;
        }

        @Override
        public void run() {
            TransactionTemplate transactionTemplate = new TransactionTemplate(getTransactionManager());
            try {
                transactionTemplate.execute(status -> {
                    doBatchUpdate();
                    return null;
                });
            } catch (Exception e) {
                logger.error("批量更新商品存储条件历史记录失败", e);
            } finally {
                countDownLatch.countDown();
            }
        }

        /**
         * 批量更新
         */
        protected abstract void doBatchUpdate();

    }

}
