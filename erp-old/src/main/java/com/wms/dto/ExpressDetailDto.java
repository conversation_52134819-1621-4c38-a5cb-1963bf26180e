package com.wms.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 快递详情
 */
public class ExpressDetailDto {
    /**
     * 包裹运单号
     */
    private String DeliveryNo;

    /**
     * 包裹重量
     */
    private BigDecimal DeliveryWeight;

    /**
     * 包裹体积
     */
    private String DeliveryCube;

    /**
     * 预留字段
     */
    private String UserDefine1;

    /**
     * 预留字段
     */
    private String UserDefine2;

    /**
     * 预留字段
     */
    private String UserDefine3;

    /**
     * 预留字段
     */
    private String UserDefine4;

    /**
     * 预留字段
     */
    private String UserDefine5;

    /**
     * 包裹单数据
     */
    private List<ExpressDataDto> packItem;

    public String getDeliveryNo() {
        return DeliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        DeliveryNo = deliveryNo;
    }

    public BigDecimal getDeliveryWeight() {
        return DeliveryWeight;
    }

    public void setDeliveryWeight(BigDecimal deliveryWeight) {
        DeliveryWeight = deliveryWeight;
    }

    public String getDeliveryCube() {
        return DeliveryCube;
    }

    public void setDeliveryCube(String deliveryCube) {
        DeliveryCube = deliveryCube;
    }

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return UserDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        UserDefine2 = userDefine2;
    }

    public String getUserDefine3() {
        return UserDefine3;
    }

    public void setUserDefine3(String userDefine3) {
        UserDefine3 = userDefine3;
    }

    public String getUserDefine4() {
        return UserDefine4;
    }

    public void setUserDefine4(String userDefine4) {
        UserDefine4 = userDefine4;
    }

    public String getUserDefine5() {
        return UserDefine5;
    }

    public void setUserDefine5(String userDefine5) {
        UserDefine5 = userDefine5;
    }

    public List<ExpressDataDto> getPackItem() {
        return packItem;
    }

    public void setPackItem(List<ExpressDataDto> packItem) {
        this.packItem = packItem;
    }

    @Override
    public String toString() {
        return "ExpressDetailDto{" +
                "DeliveryNo='" + DeliveryNo + '\'' +
                ", DeliveryWeight=" + DeliveryWeight +
                ", DeliveryCube='" + DeliveryCube + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine2='" + UserDefine2 + '\'' +
                ", UserDefine3='" + UserDefine3 + '\'' +
                ", UserDefine4='" + UserDefine4 + '\'' +
                ", UserDefine5='" + UserDefine5 + '\'' +
                ", packItem=" + packItem +
                '}';
    }
}
