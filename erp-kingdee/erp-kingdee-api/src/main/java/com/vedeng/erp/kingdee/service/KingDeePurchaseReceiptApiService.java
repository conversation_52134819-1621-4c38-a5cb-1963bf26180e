package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseReceiptQueryResultDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 采购金蝶入库单
 * @date 2023/3/7 16:09
 **/
public interface KingDeePurchaseReceiptApiService extends KingDeeMqBaseService<KingDeePurchaseReceiptDto> {


    /**
     * 查询
     * @param outInNo
     * @return
     */
    List<KingDeePurchaseReceiptQueryResultDto> queryByOutInNo(String outInNo);
}
