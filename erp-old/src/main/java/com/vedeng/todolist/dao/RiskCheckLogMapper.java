package com.vedeng.todolist.dao;

import com.vedeng.todolist.model.RiskCheckLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RiskCheckLogMapper {
    int deleteByPrimaryKey(Integer riskCheckLogId);

    int insert(RiskCheckLog record);

    int insertSelective(RiskCheckLog record);

    RiskCheckLog selectByPrimaryKey(Integer riskCheckLogId);

    int updateByPrimaryKeySelective(RiskCheckLog record);

    int updateByPrimaryKey(RiskCheckLog record);


    /**
     * 根据业务属性：订单类型，获取已完成但未触发的待办事项订单号
     * @param riskCheckTodoListBuzPropertyEnum 风控待办事项业务属性枚举类
     * @return 销售订单号
     */
    List<String> getUnTriggerOrderOfHasFinished(String riskCheckTodoListBuzPropertyEnum);

    /**
     * 根据业务冗余字段、业务属性，更新风控待办事项的触发状态
     * @param riskCheckLog 更新风控信息
     */
    void finishTriggerOfRiskCheckLog(RiskCheckLog riskCheckLog);

    void deleteByBuzTypeAndBuzIdAndBuzExtra(@Param("buzType") Integer buzType, @Param("buzId") Integer buzId, @Param("buzExtra") String buzExtra);

    /**
     * 根据业务冗余字段查找一条风控记录
     * @param buzExtra 业务字段
     * @return 风控记录
     */
    RiskCheckLog getRiskCheckLogByBuzExtra(String buzExtra);

    /**
     * 根据订单号查询是否还有已完成但未触发的订单
     * @param orderNo 订单号
     * @return 结果
     */
    List<RiskCheckLog> getUnTriggerOrderOfHasFinishedByOrderNo(String orderNo);

    /**
     * 根据订单号查询是否有未完成且未触发的订单
     * @param orderNo 订单号
     * @return 结果
     */
    List<RiskCheckLog> getUnTriggerOrderOfUnFinishedByOrderNo(String orderNo);

    Integer getUnTriggeredCountByPropertyGroupByBuzExtra(String riskCheckBuzProperty);
}