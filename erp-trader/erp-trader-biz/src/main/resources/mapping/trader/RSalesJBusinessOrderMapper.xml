<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.RSalesJBusinessOrderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.RSalesJBusinessOrderEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_SALES_J_BUSINESS_ORDER-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="BUSINESS_ID" jdbcType="INTEGER" property="businessId" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="SALE_USER_ID" jdbcType="INTEGER" property="saleUserId" />
    <result column="SALE_USER_NAME" jdbcType="VARCHAR" property="saleUserName" />
    <result column="IS_DELETED" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, BUSINESS_TYPE, 
    BUSINESS_ID, BUSINESS_NO, SALE_USER_ID, SALE_USER_NAME, IS_DELETED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_SALES_J_BUSINESS_ORDER
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_SALES_J_BUSINESS_ORDER
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJBusinessOrderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_SALES_J_BUSINESS_ORDER (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      BUSINESS_TYPE, BUSINESS_ID, BUSINESS_NO, 
      SALE_USER_ID, SALE_USER_NAME, IS_DELETED
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=INTEGER}, #{businessId,jdbcType=INTEGER}, #{businessNo,jdbcType=VARCHAR}, 
      #{saleUserId,jdbcType=INTEGER}, #{saleUserName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJBusinessOrderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_SALES_J_BUSINESS_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="businessId != null">
        BUSINESS_ID,
      </if>
      <if test="businessNo != null and businessNo != ''">
        BUSINESS_NO,
      </if>
      <if test="saleUserId != null">
        SALE_USER_ID,
      </if>
      <if test="saleUserName != null and saleUserName != ''">
        SALE_USER_NAME,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null and businessNo != ''">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="saleUserId != null">
        #{saleUserId,jdbcType=INTEGER},
      </if>
      <if test="saleUserName != null and saleUserName != ''">
        #{saleUserName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJBusinessOrderEntity">
    <!--@mbg.generated-->
    update T_R_SALES_J_BUSINESS_ORDER
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null and businessNo != ''">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="saleUserId != null">
        SALE_USER_ID = #{saleUserId,jdbcType=INTEGER},
      </if>
      <if test="saleUserName != null and saleUserName != ''">
        SALE_USER_NAME = #{saleUserName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJBusinessOrderEntity">
    <!--@mbg.generated-->
    update T_R_SALES_J_BUSINESS_ORDER
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      SALE_USER_ID = #{saleUserId,jdbcType=INTEGER},
      SALE_USER_NAME = #{saleUserName,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_SALES_J_BUSINESS_ORDER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessType != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.businessType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.businessId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessNo != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.businessNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALE_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleUserId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.saleUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALE_USER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleUserName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.saleUserName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.isDeleted,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_SALES_J_BUSINESS_ORDER
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, BUSINESS_TYPE, 
      BUSINESS_ID, BUSINESS_NO, SALE_USER_ID, SALE_USER_NAME, IS_DELETED)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.businessType,jdbcType=INTEGER}, #{item.businessId,jdbcType=INTEGER}, #{item.businessNo,jdbcType=VARCHAR}, 
        #{item.saleUserId,jdbcType=INTEGER}, #{item.saleUserName,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=INTEGER}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-01-05-->
  <select id="findByBusinessId" resultType="com.vedeng.erp.common.dto.RSalesJBusinessOrderDto">
    select
    TRSJBO.*,
    TU.REAL_NAME as saleUserCnName,
    TU1.REAL_NAME as creatorCnName
    from T_R_SALES_J_BUSINESS_ORDER TRSJBO
    left join T_USER_DETAIL TU on TRSJBO.SALE_USER_ID = TU.USER_ID  AND TU.USER_ID>0
    left join T_USER_DETAIL TU1 on TRSJBO.CREATOR = TU1.USER_ID  AND TU1.USER_ID>0
    where BUSINESS_ID=#{businessId,jdbcType=INTEGER}
      AND BUSINESS_TYPE =1
      and IS_DELETED = 0
  </select>

  <select id="findByBusinessIdAndTypeAndUserId" parameterType="com.vedeng.erp.common.dto.RSalesJBusinessOrderDto" resultType="com.vedeng.erp.common.dto.RSalesJBusinessOrderDto">
    select
      TRSJBO.*,
      TU.REAL_NAME as saleUserCnName,
      TU1.REAL_NAME as creatorCnName
    from T_R_SALES_J_BUSINESS_ORDER TRSJBO
           left join T_USER_DETAIL TU on TRSJBO.SALE_USER_ID = TU.USER_ID  AND TU.USER_ID>0
           left join T_USER_DETAIL TU1 on TRSJBO.CREATOR = TU1.USER_ID  AND TU1.USER_ID>0
    where BUSINESS_ID=#{businessDto.businessId,jdbcType=INTEGER}
      AND BUSINESS_TYPE =#{businessDto.businessType,jdbcType=INTEGER}
      AND SALE_USER_ID = #{businessDto.saleUserId,jdbcType=INTEGER}
      and IS_DELETED = 0
  </select>


<!--auto generated by MybatisCodeHelper on 2024-01-05-->
  <update id="updateIsDeletedById">
    update T_R_SALES_J_BUSINESS_ORDER
    set IS_DELETED = 1
    where ID=#{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-01-08-->
  <delete id="deleteById">
    delete from T_R_SALES_J_BUSINESS_ORDER
    where ID=#{id,jdbcType=INTEGER}
  </delete>
  
<!--auto generated by MybatisCodeHelper on 2024-08-14-->
  <select id="findByBusinessTypeAndSaleUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_R_SALES_J_BUSINESS_ORDER
    where BUSINESS_TYPE=#{businessType,jdbcType=INTEGER} and SALE_USER_ID=#{saleUserId,jdbcType=INTEGER}
  </select>


  <!-- 不依赖任何T_R_SALES_J_BUSINESS_ORDER表的标签计算查询 -->
  <select id="calculateShareTagsWithoutBusinessJOrder" resultType="com.vedeng.erp.common.dto.RSalesJBusinessOrderDto">
    /* 线下销售协作人 - 根据归属销售在销售关系配置中查找对应的线下销售 */
    SELECT DISTINCT
           ''                     as id,
           null                   as addTime,
           RUR.OFFLINE_SALES_ID   AS saleUserId,
           TU.USERNAME            AS saleUserName,
           TUD.ALIAS_HEAD_PICTURE as saleAliasHeadPicture,
           1                      as shareTag -- 线下销售
    FROM ROLE_USER_REGION_CONFIG RUR
           JOIN T_BUSINESS_LEADS TBL
                ON TBL.ID = #{businessId,jdbcType=INTEGER} AND RUR.ONLINE_SALES_ID = TBL.BELONGER_ID
           LEFT JOIN T_USER TU ON RUR.OFFLINE_SALES_ID = TU.USER_ID
           LEFT JOIN T_USER_DETAIL TUD ON TU.USER_ID = TUD.USER_ID
    WHERE RUR.OFFLINE_SALES_ID > 0

    UNION

    /* 产线负责人逻辑
       第一步: 当商机关联"商品分类"以后，首先根据"商品分类人员配置"找到"业务人员"去重；
       第二步: 用"业务人员"在"销售关系配置"中查询"产线人员"是否关联了"线上销售"
              - 如果没有关联线上销售，则设置为协作人
              - 如果关联了线上销售，且线上销售包含了线索归属销售，则设置为协作人
              - 如果关联了线上销售，但线上销售不包含线索归属销售，则不设置为协作人
    */
    SELECT 
           ''                     as id,  
           null                   as addTime,
           RUC.USER_ID            AS saleUserId,
           TU.USERNAME            AS saleUserName,
           TUD.ALIAS_HEAD_PICTURE as saleAliasHeadPicture,
           2                      as shareTag -- 产线负责人
          
    FROM ROLE_USER_CATEGORY_CONFIG RUC -- 商品分类人员配置
           JOIN T_BUSINESS_LEADS TBL ON TBL.ID = #{businessId,jdbcType=INTEGER}
           LEFT JOIN T_USER TU ON RUC.USER_ID = TU.USER_ID
           LEFT JOIN T_USER_DETAIL TUD ON TU.USER_ID = TUD.USER_ID
    WHERE EXISTS(
      -- 第一步: 检查线索是否关联了商品分类，找到对应的业务人员
            SELECT 1
            FROM T_BUSINESS_ORDER_CATEGORY TBOC
            WHERE TBOC.BUSINESS_ID = #{businessId,jdbcType=INTEGER}
              AND TBOC.BUSINESS_TYPE = 0 -- 线索类型
              AND FIND_IN_SET(TBOC.CATEGORY_ID, RUC.CATEGORY_IDS) > 0
      )
      AND (
        -- 检查是否没有关联线上销售
        NOT EXISTS (
          SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR2
          WHERE RUR2.PRODUCTION_USER_ID = RUC.USER_ID
        )
        OR 
        -- 或者关联了线上销售且是该线索的归属销售
        EXISTS (
          SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR3
          WHERE RUR3.PRODUCTION_USER_ID = RUC.USER_ID
          AND RUR3.ONLINE_SALES_ID = TBL.BELONGER_ID
        )
      )
    UNION

    /* 手动添加的协作人 - 不属于前两种情况的所有分享记录 */
    SELECT 
      TSJ.ID as id,
      TSJ.ADD_TIME as addTime,
      TSJ.SALE_USER_ID,
      TSJ.SALE_USER_NAME,
      TU.ALIAS_HEAD_PICTURE as saleAliasHeadPicture,
      3 as shareTa -- 手动添加
    FROM T_R_SALES_J_BUSINESS_ORDER TSJ
    LEFT JOIN T_USER_DETAIL TU ON TSJ.SALE_USER_ID = TU.USER_ID AND TU.USER_ID > 0
    WHERE TSJ.BUSINESS_ID = #{businessId,jdbcType=INTEGER}
      AND TSJ.BUSINESS_TYPE = 5  -- 线索类型
      AND TSJ.IS_DELETED = 0
  </select>

  <!-- 不依赖任何T_R_SALES_J_BUSINESS_ORDER表的商机标签计算查询 -->
  <select id="calculateShareTagsWithoutBusinessJOrderForChance" resultType="com.vedeng.erp.common.dto.RSalesJBusinessOrderDto">
    /* 线下销售协作人 - 根据归属销售在销售关系配置中查找对应的线下销售 */
    SELECT DISTINCT
      ''                     as id,
      null                   as addTime,
      RUR.OFFLINE_SALES_ID AS saleUserId,
      TU.USERNAME AS saleUserName,
      TUD.ALIAS_HEAD_PICTURE as saleAliasHeadPicture,
      1 as shareTag -- 线下销售
    FROM ROLE_USER_REGION_CONFIG RUR
    JOIN T_BUSSINESS_CHANCE TBC ON TBC.BUSSINESS_CHANCE_ID = #{businessId,jdbcType=INTEGER} AND RUR.ONLINE_SALES_ID = TBC.USER_ID
    LEFT JOIN T_USER TU ON RUR.OFFLINE_SALES_ID = TU.USER_ID
    LEFT JOIN T_USER_DETAIL TUD ON TU.USER_ID = TUD.USER_ID
    WHERE RUR.OFFLINE_SALES_ID > 0

    UNION
    
    /* 产线负责人逻辑
       第一步: 当商机关联"商品分类"以后，首先根据"商品分类人员配置"找到"业务人员"去重；
       第二步: 用"业务人员"在"销售关系配置"中查询"产线人员"是否关联了"线上销售"
              - 如果没有关联线上销售，则设置为协作人
              - 如果关联了线上销售，且线上销售包含了商机归属销售，则设置为协作人
              - 如果关联了线上销售，但线上销售不包含商机归属销售，则不设置为协作人
    */
    SELECT 
      ''                     as id,
      null                   as addTime,
      RUC.USER_ID AS saleUserId,  
      TU.USERNAME AS saleUserName,
      TUD.ALIAS_HEAD_PICTURE as saleAliasHeadPicture,
      2 as shareTag -- 产线负责人
    FROM ROLE_USER_CATEGORY_CONFIG RUC -- 商品分类人员配置
    JOIN T_BUSSINESS_CHANCE TBC ON TBC.BUSSINESS_CHANCE_ID = #{businessId,jdbcType=INTEGER}
    LEFT JOIN T_USER TU ON RUC.USER_ID = TU.USER_ID
    LEFT JOIN T_USER_DETAIL TUD ON TU.USER_ID = TUD.USER_ID
    WHERE EXISTS (
      -- 第一步: 检查商机是否关联了商品分类，找到对应的业务人员
      SELECT 1 FROM T_BUSINESS_ORDER_CATEGORY TBOC
      WHERE TBOC.BUSINESS_ID = #{businessId,jdbcType=INTEGER}
      AND TBOC.BUSINESS_TYPE = 1  -- 商机类型
      AND FIND_IN_SET(TBOC.CATEGORY_ID, RUC.CATEGORY_IDS) > 0
    )
    AND (
      -- 检查是否没有关联线上销售
      NOT EXISTS (
        SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR2
        WHERE RUR2.PRODUCTION_USER_ID = RUC.USER_ID
      )
      OR 
      -- 或者关联了线上销售且是该商机的归属销售
      EXISTS (
        SELECT 1 FROM ROLE_USER_REGION_CONFIG RUR3
        WHERE RUR3.PRODUCTION_USER_ID = RUC.USER_ID
        AND RUR3.ONLINE_SALES_ID = TBC.USER_ID
      )
    )
    
    UNION
    
    /* 手动添加的协作人 - 不属于前两种情况的所有分享记录 */
    SELECT 
      TSJ.ID as id, 
      TSJ.ADD_TIME as addTime,
      TSJ.SALE_USER_ID,
      TSJ.SALE_USER_NAME,
      TU.ALIAS_HEAD_PICTURE as saleAliasHeadPicture,
      3 as shareTag  -- 手动添加
    FROM T_R_SALES_J_BUSINESS_ORDER TSJ
    LEFT JOIN T_USER_DETAIL TU ON TSJ.SALE_USER_ID = TU.USER_ID AND TU.USER_ID > 0
    WHERE TSJ.BUSINESS_ID = #{businessId,jdbcType=INTEGER}
      AND TSJ.BUSINESS_TYPE = 1  -- 商机类型
      AND TSJ.IS_DELETED = 0
  </select>

</mapper>