package com.vedeng.infrastructure.esign.service;

import com.vedeng.infrastructure.esign.domain.dto.ElectronicSignRecordDto;
import com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ElectronicSignRecordService {


    ElectronicSignRecordEntity selectByPrimaryKey(Integer electronicSignRecordId);


    /**
     * 发送签章成功
     *
     * @param electronicSignRecordDto electronicSignRecordDto
     */
    void sendSuccess(ElectronicSignRecordDto electronicSignRecordDto);

    /**
     * 发送签章失败
     *
     * @param electronicSignRecordDto electronicSignRecordDto
     */
    void sendFail(ElectronicSignRecordDto electronicSignRecordDto);


    /**
     * 签章成功
     *
     * @param businessId businessId
     * @param businessId businessId
     */
    void signSuccess(String businessId, Integer businessType);

    /**
     * 签章失败
     *
     * @param businessId   businessId
     * @param businessType businessType
     * @param businessType error
     */
    void signFail(String businessId, Integer businessType, String error);

    /**
     * 重试次数+1
     * @param electronicSignRecordEntity
     */
    void msgRetryNumAdd(ElectronicSignRecordEntity electronicSignRecordEntity);

    List<ElectronicSignRecordEntity> getFail(Integer businessType);

    List<ElectronicSignRecordEntity> getElectronicSignRecord(String businessId, Integer businessType);

}
