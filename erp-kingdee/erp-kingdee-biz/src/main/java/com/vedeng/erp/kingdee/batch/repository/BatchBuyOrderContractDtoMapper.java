package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchBuyOrderContractDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BatchBuyOrderContractDtoMapper {

    List<BatchBuyOrderContractDto> queryBuyOrderContract(BatchBuyOrderContractDto dto);

    List<BatchBuyOrderContractDto> queryBuyOrderContractFile(BatchBuyOrderContractDto dto);
}
