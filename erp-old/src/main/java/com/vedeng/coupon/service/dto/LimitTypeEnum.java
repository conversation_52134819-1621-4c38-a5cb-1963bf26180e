package com.vedeng.coupon.service.dto;

public enum LimitTypeEnum {

    ALL_TYPE("全品类",1),
    LIMIT_CATEGORY("限品类",2),
    LIMIT_BRAND("限品牌",3),
    LIMIT_SKUS("限商品",4);

    private String name;

    private int value;

    LimitTypeEnum(String name,int value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static String getNameByValue(int value){

        String name = "";

        for(LimitTypeEnum enumItem : LimitTypeEnum.values()){
            if(enumItem.getValue() == value){
                name = enumItem.getName();
                break;
            }
        }

        return name;
    }
}
