<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.BaseAttributeValueMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.BaseAttributeValue">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 08 13:52:43 CST 2019.
    -->
    <id column="BASE_ATTRIBUTE_VALUE_ID" jdbcType="INTEGER" property="baseAttributeValueId" />
    <result column="BASE_ATTRIBUTE_ID" jdbcType="INTEGER" property="baseAttributeId" />
    <result column="SORT" jdbcType="INTEGER" property="sort" />
    <result column="ATTR_VALUE" jdbcType="VARCHAR" property="attrValue" />
    <result column="UNIT_ID" jdbcType="INTEGER" property="unitId" />
    <result column="IS_DELETED" jdbcType="BIT" property="isDeleted" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 08 13:52:43 CST 2019.
    -->
    BASE_ATTRIBUTE_VALUE_ID, BASE_ATTRIBUTE_ID, SORT, ATTR_VALUE, UNIT_ID, IS_DELETED, 
    ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 08 13:52:43 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from V_BASE_ATTRIBUTE_VALUE
    where BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValueId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 08 13:52:43 CST 2019.
    -->
    delete from V_BASE_ATTRIBUTE_VALUE
    where BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValueId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.BaseAttributeValue">
    insert into V_BASE_ATTRIBUTE_VALUE (BASE_ATTRIBUTE_VALUE_ID, BASE_ATTRIBUTE_ID,
      SORT, ATTR_VALUE, UNIT_ID, 
      IS_DELETED, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER)
    values (#{baseAttributeValueId,jdbcType=INTEGER}, #{baseAttributeId,jdbcType=INTEGER}, 
      #{sort,jdbcType=INTEGER}, #{attrValue,jdbcType=VARCHAR}, #{unitId,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.BaseAttributeValue">
    insert into V_BASE_ATTRIBUTE_VALUE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="baseAttributeValueId != null">
        BASE_ATTRIBUTE_VALUE_ID,
      </if>
      <if test="baseAttributeId != null">
        BASE_ATTRIBUTE_ID,
      </if>
      <if test="sort != null">
        SORT,
      </if>
      <if test="attrValue != null">
        ATTR_VALUE,
      </if>
      <if test="unitId != null">
        UNIT_ID,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="baseAttributeValueId != null">
        #{baseAttributeValueId,jdbcType=INTEGER},
      </if>
      <if test="baseAttributeId != null">
        #{baseAttributeId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="attrValue != null">
        #{attrValue,jdbcType=VARCHAR},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.BaseAttributeValue">
    update V_BASE_ATTRIBUTE_VALUE
    <set>
      <if test="baseAttributeId != null">
        BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="attrValue != null">
        ATTR_VALUE = #{attrValue,jdbcType=VARCHAR},
      </if>
      <if test="unitId != null">
        UNIT_ID = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValueId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.BaseAttributeValue">
    update V_BASE_ATTRIBUTE_VALUE
    set BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER},
      SORT = #{sort,jdbcType=INTEGER},
      ATTR_VALUE = #{attrValue,jdbcType=VARCHAR},
      UNIT_ID = #{unitId,jdbcType=INTEGER},
      IS_DELETED = #{isDeleted,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValueId,jdbcType=INTEGER}
  </update>

  <insert id="insertAttributeValByParam" parameterType="java.util.Map">
    insert into
        V_BASE_ATTRIBUTE_VALUE
        (
          BASE_ATTRIBUTE_ID,
          SORT,
          ATTR_VALUE,
          UNIT_ID,
          IS_DELETED,
          ADD_TIME,
          CREATOR,
          MOD_TIME,
          UPDATER
        )
    values
      <foreach collection="list" item="baseAttributeValue" separator=",">
        <trim prefix="(" suffix=")" suffixOverrides=",">
          #{baseAttributeVo.baseAttributeId, jdbcType=INTEGER},
          #{baseAttributeValue.sort,jdbcType=INTEGER},
          #{baseAttributeValue.attrValue,jdbcType=VARCHAR},
          <if test="baseAttributeVo.isUnit == 1">
            #{baseAttributeValue.unitId,jdbcType=INTEGER},
          </if>
          <if test="baseAttributeVo.isUnit == 0 || baseAttributeVo.isUnit == null">
            0,
          </if>
          0,
          #{nowDate,jdbcType=TIMESTAMP},
          #{baseAttributeVo.creator,jdbcType=INTEGER},
          #{nowDate,jdbcType=TIMESTAMP},
          #{baseAttributeVo.updater,jdbcType=INTEGER}
        </trim>
      </foreach>
  </insert>

  <update id="deleteAttrValByParam" parameterType="java.util.Map">
    UPDATE
        V_BASE_ATTRIBUTE_VALUE
    SET
        IS_DELETED = 1,
        UPDATER = #{baseAttributeVo.updater, jdbcType=INTEGER},
        MOD_TIME = #{nowDate,jdbcType=TIMESTAMP}
    WHERE
        BASE_ATTRIBUTE_ID = #{baseAttributeVo.baseAttributeId, jdbcType=INTEGER}
  </update>

  <select id="getAttrValueByAttrId" parameterType="java.util.Map" resultType="com.vedeng.goods.model.vo.BaseAttributeValueVo">
    select
    <include refid="Base_Column_List" />
    from V_BASE_ATTRIBUTE_VALUE
    where BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER}
    AND IS_DELETED = #{isDeleted,jdbcType=BIT}
  </select>

  <update id="updateAttributeValByParamBatch" parameterType="java.util.Map">
      UPDATE
        V_BASE_ATTRIBUTE_VALUE
        <trim prefix="SET" suffixOverrides=",">
          <trim prefix="ATTR_VALUE = CASE" suffix="END,">
            <foreach collection="list" item="baseAttributeValue" index="index">
              <if test="baseAttributeValue.baseAttributeValueId != null">
                when BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValue.baseAttributeValueId} then #{baseAttributeValue.attrValue}
              </if>
            </foreach>
          </trim>
          <trim prefix="SORT = CASE" suffix="END,">
            <foreach collection="list" item="baseAttributeValue" index="index">
              <if test="baseAttributeValue.baseAttributeValueId != null">
                when BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValue.baseAttributeValueId} then #{baseAttributeValue.sort}
              </if>
            </foreach>
          </trim>

            <trim prefix="UNIT_ID = CASE" suffix="END,">
              <foreach collection="list" item="baseAttributeValue" index="index">
                <if test="baseAttributeValue.baseAttributeValueId != null">
                  <if test="baseAttributeVo.isUnit == 1">
                    when BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValue.baseAttributeValueId} then #{baseAttributeValue.unitId}
                  </if>
                  <if test="baseAttributeVo.isUnit == 0 || baseAttributeVo.isUnit == null">
                    when BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValue.baseAttributeValueId} then 0
                  </if>
                </if>
              </foreach>
            </trim>

          <trim prefix="UPDATER = CASE" suffix="END,">
            <foreach collection="list" item="baseAttributeValue" index="index">
              <if test="baseAttributeValue.baseAttributeValueId != null">
                when BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValue.baseAttributeValueId} then #{updater, jdbcType=INTEGER}
              </if>
            </foreach>
          </trim>
          <trim prefix="MOD_TIME = CASE" suffix="END,">
            <foreach collection="list" item="baseAttributeValue" index="index">
              <if test="baseAttributeValue.baseAttributeValueId != null">
                when BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValue.baseAttributeValueId} then #{nowDate,jdbcType=TIMESTAMP}
              </if>
            </foreach>
          </trim>
        </trim>
    WHERE
      BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER}
      <if test="list != null and list.size()>0">
        AND BASE_ATTRIBUTE_VALUE_ID IN
        <foreach collection="list" item="baseAttributeValue" open="(" close=")" separator="," index="index">
          #{baseAttributeValue.baseAttributeValueId,jdbcType=INTEGER}
        </foreach>
      </if>
  </update>
  
  <update id="deleteAttrValByParamBatch" parameterType="java.util.Map">
    UPDATE
        V_BASE_ATTRIBUTE_VALUE
    SET
        IS_DELETED = #{isDeleted,jdbcType=BIT},
        UPDATER = #{updater, jdbcType=INTEGER},
        MOD_TIME = #{nowDate,jdbcType=TIMESTAMP}
    WHERE
      BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER}
      <if test="list != null and list.size()>0">
        AND BASE_ATTRIBUTE_VALUE_ID IN
        <foreach collection="list" item="baseAttributeValue" open="(" close=")" separator="," index="index">
           #{baseAttributeValue.baseAttributeValueId,jdbcType=INTEGER}
        </foreach>
      </if>
  </update>

  <select id="getBaseAttributeValueVoList" parameterType="java.util.Map" resultType="com.vedeng.goods.model.vo.BaseAttributeValueVo">
    SELECT
      a.BASE_ATTRIBUTE_VALUE_ID, a.BASE_ATTRIBUTE_ID, a.SORT, a.ATTR_VALUE, a.UNIT_ID, a.IS_DELETED,
      a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,b.UNIT_NAME
    FROM V_BASE_ATTRIBUTE_VALUE a
    LEFT JOIN T_UNIT b ON b.UNIT_ID = a.UNIT_ID AND b.IS_DEL = #{baseAttributeValueVo.isDeleted,jdbcType=BIT}
    WHERE IS_DELETED = #{baseAttributeValueVo.isDeleted,jdbcType=BIT}
    <if test="list != null and list.size() > 0">
      AND BASE_ATTRIBUTE_ID IN
      <foreach collection="list" item="baseAttributeVo" open="(" close=")" index="index" separator=",">
        #{baseAttributeVo.baseAttributeId,jdbcType=INTEGER}
      </foreach>
    </if>
    ORDER BY
      a.SORT
  </select>
  <select id="getBaseAttributeValueVoListByAttrId" parameterType="com.vedeng.goods.model.vo.BaseAttributeValueVo" resultType="com.vedeng.goods.model.vo.BaseAttributeValueVo">
    SELECT
      a.BASE_ATTRIBUTE_VALUE_ID, a.BASE_ATTRIBUTE_ID, a.SORT, a.ATTR_VALUE, a.UNIT_ID, b.UNIT_NAME,
      COUNT(DISTINCT c.CATEGORY_ATTR_VALUE_MAPPING_ID) AS VALUE_CATEGORY_NUM
    FROM V_BASE_ATTRIBUTE_VALUE a
    LEFT JOIN T_UNIT b ON b.UNIT_ID = a.UNIT_ID AND b.IS_DEL = #{isDeleted,jdbcType=BIT}
    LEFT JOIN V_CATEGORY_ATTR_VALUE_MAPPING c ON a.BASE_ATTRIBUTE_VALUE_ID = c.BASE_ATTRIBUTE_VALUE_ID AND c.IS_DELETED = #{isDeleted,jdbcType=BIT}
    WHERE a.IS_DELETED = #{isDeleted,jdbcType=BIT}
    AND a.BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER}
    GROUP BY
       a.BASE_ATTRIBUTE_VALUE_ID, a.BASE_ATTRIBUTE_ID, a.SORT, a.ATTR_VALUE, a.UNIT_ID, b.UNIT_NAME
    ORDER BY
    a.SORT
  </select>
  <select id="getAttrValueListByCategoryId" parameterType="java.util.Map" resultType="com.vedeng.goods.model.vo.BaseAttributeValueVo">
    SELECT DISTINCT
      a.CATEGORY_ATTR_VALUE_MAPPING_ID,
      a.BASE_ATTRIBUTE_ID,
      a.BASE_ATTRIBUTE_VALUE_ID,
      b.ATTR_VALUE,
      c.UNIT_NAME
    FROM
        V_CATEGORY_ATTR_VALUE_MAPPING a
    LEFT JOIN V_BASE_ATTRIBUTE_VALUE b ON a.BASE_ATTRIBUTE_VALUE_ID = b.BASE_ATTRIBUTE_VALUE_ID
    LEFT JOIN T_UNIT c ON b.UNIT_ID = c.UNIT_ID AND c.IS_DEL = #{isDeleted, jdbcType=INTEGER}
    AND b.IS_DELETED = #{isDeleted, jdbcType=INTEGER}
    WHERE
        a.IS_DELETED = #{isDeleted, jdbcType=INTEGER}
        <if test="list != null and list.size()>0">
          AND
          a.BASE_CATEGORY_ID IN
          <foreach collection="list" index="index" open="(" close=")" item="baseCategoryVo" separator=",">
            #{baseCategoryVo.baseCategoryId, jdbcType=INTEGER}
          </foreach>
        </if>
    ORDER BY
       b.SORT
  </select>
  <select id="getBaseAttributeValueVoListASC" parameterType="java.util.Map" resultType="com.vedeng.goods.model.vo.BaseAttributeValueVo">
    SELECT
    a.BASE_ATTRIBUTE_VALUE_ID, a.BASE_ATTRIBUTE_ID, a.SORT, a.ATTR_VALUE, a.UNIT_ID, a.IS_DELETED,
    a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,b.UNIT_NAME
    FROM V_BASE_ATTRIBUTE_VALUE a
    LEFT JOIN T_UNIT b ON b.UNIT_ID = a.UNIT_ID AND b.IS_DEL = #{baseAttributeValueVo.isDeleted,jdbcType=BIT}
    WHERE IS_DELETED = #{baseAttributeValueVo.isDeleted,jdbcType=BIT}
    <if test="list != null and list.size() > 0">
      AND BASE_ATTRIBUTE_ID IN
      <foreach collection="list" item="baseAttributeVo" open="(" close=")" index="index" separator=",">
        #{baseAttributeVo.baseAttributeId,jdbcType=INTEGER}
      </foreach>
    </if>
    ORDER BY
    a.SORT
  </select>
  <select id="getDeletedAttrValueNumByIds" parameterType="java.util.Map" resultType="java.lang.Integer">
    SELECT
      COUNT(1)
    FROM
        V_BASE_ATTRIBUTE_VALUE
    WHERE IS_DELETED = #{isDeleted, jdbcType=INTEGER}
      <if test="list!=null and list.size()>0">
        AND
        BASE_ATTRIBUTE_VALUE_ID IN
        <foreach collection="list" index="index" open="(" close=")" item="attrValueId" separator=",">
          #{attrValueId, jdbcType=INTEGER}
        </foreach>
      </if>
  </select>
  <select id="listAttrValueByAttrIds" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_BASE_ATTRIBUTE_VALUE
    where
      <if test="deleted != null">
        IS_DELETED =  #{deleted, jdbcType=TINYINT}
      </if>
      and BASE_ATTRIBUTE_ID  in
      <foreach collection="attributeIdSet" index="index" open="(" close=")" item="attrId" separator=",">
        #{attrId, jdbcType=INTEGER}
      </foreach>
  </select>

  <select id="listAttributeValueByIds" parameterType="list" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_BASE_ATTRIBUTE_VALUE
    where BASE_ATTRIBUTE_VALUE_ID in
    <foreach collection="attributeValueIdList" index="index" open="(" close=")" item="attributeValueId" separator=",">
      #{attributeValueId, jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="listAttributeValueBySkuIds" parameterType="list"  resultMap="BaseResultMap">
    select
    av.BASE_ATTRIBUTE_VALUE_ID, av.BASE_ATTRIBUTE_ID, av.SORT, av.ATTR_VALUE, av.UNIT_ID, av.IS_DELETED,
    av.ADD_TIME, av.CREATOR, av.MOD_TIME, av.UPDATER
    from V_SKU_ATTR_MAPPING map left join  V_BASE_ATTRIBUTE_VALUE av on map.BASE_ATTRIBUTE_VALUE_ID = av.BASE_ATTRIBUTE_VALUE_ID
    where map.SKU_ID in
    <foreach collection="skuIdList" index="index" open="(" close=")" item="skuId" separator=",">
      #{skuId, jdbcType=INTEGER}
    </foreach>
    and map.STATUS=1 and av.IS_DELETED=0
  </select>
</mapper>