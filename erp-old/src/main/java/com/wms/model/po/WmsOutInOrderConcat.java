package com.wms.model.po;

import java.util.Date;

public class WmsOutInOrderConcat {
    /** 主键  OUT_IN_ORDER_CONCAT_ID **/
    private Integer outInOrderConcatId;

    /** 出库单号  OUT_ORDER_NO **/
    private String outOrderNo;

    /** 出库单id  OUT_ORDER_ID **/
    private Integer outOrderId;

    /** 出库单类型  0销售单  1销售换货    OUT_ORDER_TYPE **/
    private Integer outOrderType;

    /** 入库单号  IN_ORDER_NO **/
    private String inOrderNo;

    /** 入库单id  IN_ORDER_ID **/
    private Integer inOrderId;

    /** 入库单类型  0采购单  1售后  IN_ORDER_TYPE **/
    private Integer inOrderType;

    /** 是否删除 0否 1是  IS_DELETE **/
    private Integer isDelete;

    /** 添加时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  UPDATE_TIME **/
    private Date updateTime;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /**   主键  OUT_IN_ORDER_CONCAT_ID   **/
    public Integer getOutInOrderConcatId() {
        return outInOrderConcatId;
    }

    /**   主键  OUT_IN_ORDER_CONCAT_ID   **/
    public void setOutInOrderConcatId(Integer outInOrderConcatId) {
        this.outInOrderConcatId = outInOrderConcatId;
    }

    /**   出库单号  OUT_ORDER_NO   **/
    public String getOutOrderNo() {
        return outOrderNo;
    }

    /**   出库单号  OUT_ORDER_NO   **/
    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo == null ? null : outOrderNo.trim();
    }

    /**   出库单id  OUT_ORDER_ID   **/
    public Integer getOutOrderId() {
        return outOrderId;
    }

    /**   出库单id  OUT_ORDER_ID   **/
    public void setOutOrderId(Integer outOrderId) {
        this.outOrderId = outOrderId;
    }

    /**   出库单类型  0销售单  1销售换货    OUT_ORDER_TYPE   **/
    public Integer getOutOrderType() {
        return outOrderType;
    }

    /**   出库单类型  0销售单  1销售换货    OUT_ORDER_TYPE   **/
    public void setOutOrderType(Integer outOrderType) {
        this.outOrderType = outOrderType;
    }

    /**   入库单号  IN_ORDER_NO   **/
    public String getInOrderNo() {
        return inOrderNo;
    }

    /**   入库单号  IN_ORDER_NO   **/
    public void setInOrderNo(String inOrderNo) {
        this.inOrderNo = inOrderNo == null ? null : inOrderNo.trim();
    }

    /**   入库单id  IN_ORDER_ID   **/
    public Integer getInOrderId() {
        return inOrderId;
    }

    /**   入库单id  IN_ORDER_ID   **/
    public void setInOrderId(Integer inOrderId) {
        this.inOrderId = inOrderId;
    }

    /**   入库单类型  0采购单  1售后  IN_ORDER_TYPE   **/
    public Integer getInOrderType() {
        return inOrderType;
    }

    /**   入库单类型  0采购单  1售后  IN_ORDER_TYPE   **/
    public void setInOrderType(Integer inOrderType) {
        this.inOrderType = inOrderType;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public Integer getIsDelete() {
        return isDelete;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**   添加时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   添加时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  UPDATE_TIME   **/
    public Date getUpdateTime() {
        return updateTime;
    }

    /**   更新时间  UPDATE_TIME   **/
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}