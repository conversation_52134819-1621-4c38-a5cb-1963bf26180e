package com.vedeng.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodUseTypeEnum;
import com.vedeng.customerbillperiod.dto.*;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodManagementDetailService;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodService;
import com.vedeng.finance.constant.CapitalBillBussinessTypeEnum;
import com.vedeng.finance.constant.CapitalBillOrderTypeEnum;
import com.vedeng.finance.dao.CapitalBillMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.enums.PaymentTypeEnum;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.OrderAccountPeriodService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_INVOICE;

/**
 * <AUTHOR>
 * 订单账期服务
 */
@Service("orderAccountPeriodService")
public class OrderAccountPeriodServiceImpl implements OrderAccountPeriodService {

    Logger logger = LoggerFactory.getLogger(OrderAccountPeriodServiceImpl.class);

    @Autowired
    private CustomerBillPeriodService customerBillPeriodService;

    @Autowired
    private CustomerBillPeriodManagementDetailService customerBillPeriodManagementDetailService;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private ExpressMapper expressMapper;

    @Resource
    private InvoiceMapper invoiceMapper;

    @Resource
    private CapitalBillMapper capitalBillMapper;

    @Autowired
    private InvoiceService invoiceService;

    @Override
    public void dealCloseOrderCustomerBillPeriodOfOrder(Saleorder saleorder) throws CustomerBillPeriodException {
        logger.info("dealCustomerBillPeriodUseDetailOfOrder saleorder:{}", JSON.toJSONString(saleorder));
        if (saleorder == null || saleorder.getSaleorderId() == null) {
            return;
        }
        Saleorder orderInfo = saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());
        Long customerId = saleorderMapper.getCustomerIdByOrderId(orderInfo.getSaleorderId());
        if (customerId == null) {
            logger.error("检索订单客户ID信息时告警 orderNo:{}", orderInfo.getSaleorderNo());
            return;
        }
        CustomerBillPeriodUseDetailOfOrder customerBillPeriodInfo =
                customerBillPeriodService.getUsedCustomerBillPeriodAmountByOrderId(orderInfo.getCompanyId(),
                        customerId, Long.valueOf(orderInfo.getSaleorderId()));
        logger.info("处理关闭订单获取订单账期信息 orderNo:{},customerBillPeriodInfo:{}", orderInfo.getSaleorderNo(), JSON.toJSONString(customerBillPeriodInfo));
        if (customerBillPeriodInfo.getCurrentUsedAmount().compareTo(BigDecimal.ZERO) < 1) {
            logger.info("关闭的订单暂无需要释放的账期占用 orderNo:{}", orderInfo.getSaleorderNo());
            return;
        }
        CustomerBillPeriodUnfreezingDto customerBillPeriodUnfreezingDto = new CustomerBillPeriodUnfreezingDto();
        customerBillPeriodUnfreezingDto.setCompanyId(orderInfo.getCompanyId());
        customerBillPeriodUnfreezingDto.setCustomerId(customerId);
        customerBillPeriodUnfreezingDto.setOrderId(Long.valueOf(orderInfo.getSaleorderId()));
        customerBillPeriodUnfreezingDto.setUnfreezingType(CustomerBillPeriodUseTypeEnum.CLOSE_ORDER.getCode());
        customerBillPeriodUnfreezingDto.setRelatedId(Long.valueOf(orderInfo.getSaleorderId()));
        customerBillPeriodUnfreezingDto.setUnfreezingAmount(customerBillPeriodInfo.getCurrentUsedAmount());
        customerBillPeriodUnfreezingDto.setAddTime(System.currentTimeMillis());
        logger.info("解冻客户账期占用信息 customerBillPeriodUnfreezingDto:{}", JSON.toJSONString(customerBillPeriodUnfreezingDto));
        customerBillPeriodService.unfreezingCustomerBillPeriodAmount(customerBillPeriodUnfreezingDto);
    }

    @Override
    public void dealCustomerBillPeriodManagement(Integer saleOrderId, Integer managementType, Integer relatedId, BigDecimal amount) throws CustomerBillPeriodException {
        logger.info("dealCustomerBillPeriodManagement start saleOrderId:{},managementType:{},relatedId:{}", saleOrderId, managementType, relatedId);
        if (saleOrderId == null || managementType == null || relatedId == null) {
            return;
        }
        Saleorder orderInfo = saleorderMapper.getSaleOrderById(saleOrderId);
        if (orderInfo == null) {
            logger.error("生成账期逾期编码订单信息异常 orderId:{}", saleOrderId);
            throw new CustomerBillPeriodException("生成账期逾期编码订单信息异常 orderId:" + saleOrderId);
        }

        //物流作废时，调用生成负数逾期管理编码的接口；其他场景调用生成正向逾期管理编码接口
        if (CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS_INVALID.getCode().equals(managementType)){
            RollbackCustomerBillPeriodManagementDetailDto rollbackCustomerBillPeriodManagementDetailDto =
                    new RollbackCustomerBillPeriodManagementDetailDto();
            rollbackCustomerBillPeriodManagementDetailDto.setCompanyId(orderInfo.getCompanyId());
            rollbackCustomerBillPeriodManagementDetailDto.setCustomerId(saleorderMapper.getCustomerIdByOrderId(saleOrderId));
            rollbackCustomerBillPeriodManagementDetailDto.setOrderId(Long.valueOf(saleOrderId));
            rollbackCustomerBillPeriodManagementDetailDto.setRelatedId(Long.valueOf(relatedId));
            rollbackCustomerBillPeriodManagementDetailDto.setType(managementType);
            rollbackCustomerBillPeriodManagementDetailDto.setAmount(amount);
            rollbackCustomerBillPeriodManagementDetailDto.setAddTime(System.currentTimeMillis());
            logger.info("生成负数的客户账期管理明细编码 rollbackCustomerBillPeriodManagementDetailDto:{}",
                    JSON.toJSONString(rollbackCustomerBillPeriodManagementDetailDto));
            customerBillPeriodManagementDetailService.rollbackCustomerBillPeriodManagementDetail(rollbackCustomerBillPeriodManagementDetailDto);
        } else {
            GenerateCustomerBillPeriodManagementDetailDto generateCustomerBillPeriodManagementDetailDto = new GenerateCustomerBillPeriodManagementDetailDto();
            generateCustomerBillPeriodManagementDetailDto.setAmount(amount);
            generateCustomerBillPeriodManagementDetailDto.setCompanyId(orderInfo.getCompanyId());
            generateCustomerBillPeriodManagementDetailDto.setCustomerId(saleorderMapper.getCustomerIdByOrderId(saleOrderId));
            generateCustomerBillPeriodManagementDetailDto.setOrderId(Long.valueOf(saleOrderId));
            generateCustomerBillPeriodManagementDetailDto.setOrderNo(orderInfo.getSaleorderNo());
            generateCustomerBillPeriodManagementDetailDto.setType(managementType);
            generateCustomerBillPeriodManagementDetailDto.setAddTime(System.currentTimeMillis());
            generateCustomerBillPeriodManagementDetailDto.setRelatedId(Long.valueOf(relatedId));
            logger.info("生成客户账期管理明细编码 generateCustomerBillPeriodManagementDetailDto:{}", JSON.toJSONString(generateCustomerBillPeriodManagementDetailDto));
            customerBillPeriodManagementDetailService.generateCustomerBillPeriodManagementDetail(generateCustomerBillPeriodManagementDetailDto);

            if (ORDER_INVOICE.getCode().equals(managementType)){
                logger.info("生成客户账期节点为开票，不进行特殊商品账期编码的处理 saleOrderId:{},managementType:{},relatedId:{}", saleOrderId, managementType, relatedId);
                return;
            }

            logger.info("wms发货生成账期编码时处理特殊商品信息 saleOrderId:{}, relatedId:{}", saleOrderId, relatedId);
            if (CollectionUtils.isNotEmpty(expressMapper.getExpressIdsThisDeliveryBefore(saleOrderId, relatedId))){
                logger.info("本次发货不是该订单的第一次物流信息 saleOrderId:{}, relatedId:{}", saleOrderId, relatedId);
                return;
            }

            BigDecimal specialGoodsAmount = saleorderMapper.getSpecialGoodsAmountByOrderId(saleOrderId);
            if (specialGoodsAmount == null || specialGoodsAmount.compareTo(BigDecimal.ZERO) < 1){
                logger.info("本次发货订单无特殊商品或者特殊商品的实际金额为0");
                return;
            }
            GenerateCustomerBillPeriodManagementDetailDto periodManagementForDelivery = new GenerateCustomerBillPeriodManagementDetailDto();
            BeanUtils.copyProperties(generateCustomerBillPeriodManagementDetailDto, periodManagementForDelivery);
            periodManagementForDelivery.setAmount(specialGoodsAmount);
            logger.info("wms发货生成发货账期编码 periodManagementForDelivery:{}", JSON.toJSONString(periodManagementForDelivery));
            customerBillPeriodManagementDetailService.generateCustomerBillPeriodManagementDetail(periodManagementForDelivery);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealOrderCustomerBillPeriodWithAddCapitalBill(CapitalBill capitalBill) throws CustomerBillPeriodException {
        logger.info("添加流水处理订单冻结和占用的账期金额释放 capitalBill:{}", JSON.toJSONString(capitalBill));
        if (capitalBill == null || capitalBill.getCapitalBillDetail() == null) {
            throw new CustomerBillPeriodException("添加流水处理订单冻结和占用的账期金额释放参数异常");
        }
        if (!CapitalBillOrderTypeEnum.SALE_ORDER.getCode().equals(capitalBill.getCapitalBillDetail().getOrderType())) {
            logger.info("添加流水非销售订单类型 type:{}", capitalBill.getCapitalBillDetail().getOrderType());
            return;
        }

        Integer orderId = capitalBill.getCapitalBillDetail().getRelatedId();
        Long customerId = saleorderMapper.getCustomerIdByOrderId(orderId);

        switch (CapitalBillBussinessTypeEnum.getTypeEnumByCode(capitalBill.getCapitalBillDetail().getBussinessType())) {
            case ORDER_RECEIPT: {
                logger.info("新增流水操作类型为订单收款 orderId:{}", orderId);
                // 进行订单占用
                occupancyCustomerBillPeriodAmount(capitalBill.getAmount() ,orderId.longValue(), customerId,1, false);
                return;
            }
            case ORDER_REFUND: {
                logger.info("新增流水操作类型为退款 orderId:{}", orderId);
                return;
            }
            case CREDIT_PAYMENT: {
                logger.info("新增流水操作类型为信用还款 orderId:{}", orderId);
                break;
            }
            case WITHDRAW_FOR: {
                logger.info("新增流水操作类型为退款 orderId:{}", orderId);
                return;
            }
            default: {
                logger.warn("新增流水操作类型错误 warn type:{}", capitalBill.getCapitalBillDetail().getBussinessType());
                throw new CustomerBillPeriodException("新增流水操作类型错误 type:" + capitalBill.getCapitalBillDetail().getBussinessType());
            }
        }
    }


    @Override
    public void unfreezingBillPeriodByCreditRepayment(Integer orderId, BigDecimal creditRepaymentAmount, Integer capitalBillId) throws CustomerBillPeriodException {
        logger.info("添加流水处理订单冻结和占用的账期金额释放 orderId:{}, creditRepaymentAmount:{}, capitalBillId:{}", orderId, creditRepaymentAmount, capitalBillId);
        if (orderId == null){
            throw new CustomerBillPeriodException("添加流水处理订单冻结和占用的账期金额释放缺失");
        }
        if (creditRepaymentAmount == null || BigDecimal.ZERO.compareTo(creditRepaymentAmount) == 0){
            logger.info("添加流水暂无需要释放的账期金额 orderId:{}", orderId);
            return;
        }

        Long customerId = saleorderMapper.getCustomerIdByOrderId(orderId);

        CustomerBillPeriodUseDetailOfOrder customerBillPeriodInfo = customerBillPeriodService.getUsedCustomerBillPeriodAmountByOrderId(ErpConst.ONE, customerId, Long.valueOf(orderId));
        logger.info("添加流水处理订单账期信息 orderId:{}, customerBillPeriodInfo:{}", orderId, JSON.toJSONString(customerBillPeriodInfo));

        if (customerBillPeriodInfo.getCurrentUsedAmount().compareTo(BigDecimal.ZERO) < 1) {
            logger.info("销售订单新增流水订单不存在需要处理的账期内容 orderId:{}, currentUsedAmount:{}", orderId, customerBillPeriodInfo.getCurrentUsedAmount());
            return;
        }

        CustomerBillPeriodUnfreezingDto customerBillPeriodUnfreezingDto = new CustomerBillPeriodUnfreezingDto();
        customerBillPeriodUnfreezingDto.setUnfreezingType(CustomerBillPeriodUseTypeEnum.REPAYMENT.getCode());
        customerBillPeriodUnfreezingDto.setCompanyId(ErpConst.ONE);
        customerBillPeriodUnfreezingDto.setCustomerId(customerId);
        customerBillPeriodUnfreezingDto.setOrderId(Long.valueOf(orderId));
        customerBillPeriodUnfreezingDto.setRelatedId(Long.valueOf(capitalBillId));
        customerBillPeriodUnfreezingDto.setUnfreezingAmount(creditRepaymentAmount);
        customerBillPeriodUnfreezingDto.setAddTime(System.currentTimeMillis());
        logger.info("添加流水处理订单冻结和占用的账期金额释放 customerBillPeriodUnfreezingDto:{}", JSON.toJSONString(customerBillPeriodUnfreezingDto));
        customerBillPeriodService.unfreezingCustomerBillPeriodAmount(customerBillPeriodUnfreezingDto);
    }

    @Override
    public void unfreezingBillPeriodByCancelValidSaleOrder(Integer saleOrderId) {
        logger.info("销售订单：{}撤销生效，解冻其占用的账期金额", saleOrderId);
        Saleorder orderInfo = saleorderMapper.getSaleOrderById(saleOrderId);
        Long customerId = saleorderMapper.getCustomerIdByOrderId(orderInfo.getSaleorderId());
        if (customerId == null) {
            logger.error("检索订单客户ID信息时告警 orderNo:{}", orderInfo.getSaleorderNo());
            return;
        }
        CustomerBillPeriodUseDetailOfOrder customerBillPeriodInfo =
                customerBillPeriodService.getUsedCustomerBillPeriodAmountByOrderId(orderInfo.getCompanyId(),
                        customerId, Long.valueOf(orderInfo.getSaleorderId()));
        if (customerBillPeriodInfo.getCurrentUsedAmount().compareTo(BigDecimal.ZERO) < 1) {
            logger.info("撤销生效的订单暂无需要解冻的账期 orderNo:{}", orderInfo.getSaleorderNo());
            return;
        }
        CustomerBillPeriodUnfreezingDto customerBillPeriodUnfreezingDto = new CustomerBillPeriodUnfreezingDto();
        customerBillPeriodUnfreezingDto.setCompanyId(orderInfo.getCompanyId());
        customerBillPeriodUnfreezingDto.setCustomerId(customerId);
        customerBillPeriodUnfreezingDto.setOrderId(Long.valueOf(orderInfo.getSaleorderId()));
        customerBillPeriodUnfreezingDto.setUnfreezingType(CustomerBillPeriodUseTypeEnum.CANCEL_VALID.getCode());
        customerBillPeriodUnfreezingDto.setRelatedId(Long.valueOf(orderInfo.getSaleorderId()));
        customerBillPeriodUnfreezingDto.setUnfreezingAmount(customerBillPeriodInfo.getCurrentUsedAmount());
        customerBillPeriodUnfreezingDto.setAddTime(System.currentTimeMillis());
        logger.info("解冻客户账期占用信息 customerBillPeriodUnfreezingDto:{}", JSON.toJSONString(customerBillPeriodUnfreezingDto));
        customerBillPeriodService.unfreezingCustomerBillPeriodAmount(customerBillPeriodUnfreezingDto);
    }

    @Override
    public void dealAccountPeriodOverdueCodeByConfirmAfterSales(Integer afterSalesId) throws CustomerBillPeriodException {
        logger.info("售后完结处理账期逾期编码 start afterSalesId:{}", afterSalesId);
        if (afterSalesId == null) {
            throw new CustomerBillPeriodException("售后完结处理账期逾期编码参数错误");
        }

        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        logger.info("售后完结处理账期逾期编码售后信息 afterSales:{}", JSON.toJSONString(afterSales));

        if (afterSales == null) {
            throw new CustomerBillPeriodException("售后完结处理账期逾期编码售后信息错误");
        }

        if (!SysOptionConstant.ID_535.equals(afterSales.getSubjectType())) {
            logger.info("售后完结订单非销售订单跳过本次处理 afterSalesNo:{}", afterSales.getAfterSalesNo());
            return;
        }

        Saleorder saleOrderInfo = saleorderMapper.getSaleOrderById(afterSales.getOrderId());
        logger.info("售后完结处理账期逾期编码销售订单信息 saleOrderInfo:{}", JSON.toJSONString(saleOrderInfo));
        if (saleOrderInfo == null) {
            logger.error("售后完结处理账期逾期编码销售订单信息错误 orderId:{}", afterSales.getOrderId());
            throw new CustomerBillPeriodException("后完结处理账期逾期编码销售订单信息错误 orderId:" + afterSales.getOrderId());
        }

        Long customerId = saleorderMapper.getCustomerIdByOrderId(saleOrderInfo.getSaleorderId());

        if (!SysOptionConstant.ID_542.equals(afterSales.getType())) {
            logger.info("售后完结处理账期逾期编码售后订单非退票类型跳过处理 afterSalesNo:{}, type:{}", afterSales.getAfterSalesNo(), afterSales.getType());
            return;
        }

        RollbackCustomerBillPeriodManagementDetailDto periodManagementDetailDto = new RollbackCustomerBillPeriodManagementDetailDto();
        periodManagementDetailDto.setCompanyId(afterSales.getCompanyId());
        periodManagementDetailDto.setCustomerId(customerId);
        periodManagementDetailDto.setOrderId(Long.valueOf(saleOrderInfo.getSaleorderId()));

        if (SysOptionConstant.ID_539.equals(afterSales.getType())) {
            periodManagementDetailDto.setAmount(afterSalesMapper.getGoodsNeedRefundAmountByAfterSaleId(afterSalesId));
            periodManagementDetailDto.setType(CustomerBillPeriodOverdueManageDetailTypeEnum.AFTER_SALES_RETURN.getCode());
        } else if (SysOptionConstant.ID_542.equals(afterSales.getType())) {
            periodManagementDetailDto.setAmount(afterSalesMapper.getInvoiceNeedRefundAmountByAfterSaleId(afterSalesId));
            periodManagementDetailDto.setType(CustomerBillPeriodOverdueManageDetailTypeEnum.AFTER_SALES_INVOICE.getCode());
        } else {
            logger.error("售后完结处理账期逾期编码售后订单非退票和退货类型错误 afterSalesNo:{}, type:{}", afterSales.getAfterSalesNo(), afterSales.getType());
            throw new CustomerBillPeriodException("售后完结处理账期逾期编码售后订单非退票和退货类型错误");
        }

        logger.info("售后完结处理账期逾期编码销售订单信息订单账期信息 orderNo:{},periodManagementDetailDto:{}",
                saleOrderInfo.getSaleorderNo(), JSON.toJSONString(periodManagementDetailDto));
        if (periodManagementDetailDto.getAmount().compareTo(BigDecimal.ZERO) < 1) {
            logger.info("售后完结处理账期逾期编码销售订单售后金额异常告警 orderNo:{}", saleOrderInfo.getSaleorderNo());
            return;
        }

        periodManagementDetailDto.setRelatedId(Long.valueOf(afterSalesId));
        periodManagementDetailDto.setAddTime(System.currentTimeMillis());
        logger.info("售后完结最终处理账期逾期编码 periodManagementDetailDto:{}", JSON.toJSONString(periodManagementDetailDto));
        customerBillPeriodManagementDetailService.rollbackCustomerBillPeriodManagementDetail(periodManagementDetailDto);
    }

    @Override
    public void dealAccountPeriodOverdueCodeByRefundInvoice(Invoice invoice) throws CustomerBillPeriodException {
        logger.info("售后退票处理账期逾期编码 start invoice:{}", JSON.toJSONString(invoice));

        if (invoice == null || invoice.getInvoiceId() == null ||  invoice.getAfterSalesId() == null){
            throw new CustomerBillPeriodException("售后退票处理账期逾期编码参数异常告警");
        }
        Integer afterSalesId = invoice.getAfterSalesId();

        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        logger.info("售后退票处理账期逾期编码售后信息 afterSales:{}", JSON.toJSONString(afterSales));

        if (afterSales == null) {
            throw new CustomerBillPeriodException("售后退票处理账期逾期编码售后信息错误");
        }

        if (!SysOptionConstant.ID_535.equals(afterSales.getSubjectType())) {
            logger.info("售后退票订单非销售订单跳过本次处理 afterSalesNo:{}", afterSales.getAfterSalesNo());
            return;
        }

        if(SysOptionConstant.ID_539.equals(afterSales.getType())){
            logger.info("售后退货单不处理退票信息 afterSalesNo:{}", afterSales.getAfterSalesNo());
            return;
        }

        Saleorder saleOrderInfo = saleorderMapper.getSaleOrderById(afterSales.getOrderId());
        logger.info("售后退票处理账期逾期编码销售订单信息 saleOrderInfo:{}", JSON.toJSONString(saleOrderInfo));
        if (saleOrderInfo == null) {
            logger.error("售后退票处理账期逾期编码销售订单信息错误 orderId:{}", afterSales.getOrderId());
            throw new CustomerBillPeriodException("售后退票处理账期逾期编码销售订单信息错误 orderId:" + afterSales.getOrderId());
        }

        Invoice invoiceInfo = invoiceMapper.selectByPrimaryKey(invoice.getInvoiceId());
        if (invoiceInfo == null){
            logger.error("售后退票发票信息ID异常告警 invoiceId:{}", invoice.getInvoiceId());
            throw new CustomerBillPeriodException("售后退票发票信息ID异常告警 invoiceId:{}"+ invoice.getInvoiceId());
        }

        Long customerId = saleorderMapper.getCustomerIdByOrderId(saleOrderInfo.getSaleorderId());

        RollbackCustomerBillPeriodManagementDetailDto periodManagementDetailDto = new RollbackCustomerBillPeriodManagementDetailDto();
        periodManagementDetailDto.setCompanyId(afterSales.getCompanyId());
        periodManagementDetailDto.setCustomerId(customerId);
        periodManagementDetailDto.setOrderId(Long.valueOf(saleOrderInfo.getSaleorderId()));
        periodManagementDetailDto.setAmount(invoiceInfo.getAmount().abs());
        periodManagementDetailDto.setType(CustomerBillPeriodOverdueManageDetailTypeEnum.AFTER_SALES_INVOICE.getCode());

        logger.info("售后退票处理账期逾期编码销售订单信息订单账期信息 orderNo:{},periodManagementDetailDto:{}",
                saleOrderInfo.getSaleorderNo(), JSON.toJSONString(periodManagementDetailDto));
        if (periodManagementDetailDto.getAmount().compareTo(BigDecimal.ZERO) < 1) {
            logger.info("售后退票处理账期逾期编码销售订单售后金额异常告警 orderNo:{}", saleOrderInfo.getSaleorderNo());
            return;
        }

        periodManagementDetailDto.setRelatedId(Long.valueOf(afterSalesId));
        periodManagementDetailDto.setAddTime(System.currentTimeMillis());
        logger.info("售后退票最终处理账期逾期编码 periodManagementDetailDto:{}", JSON.toJSONString(periodManagementDetailDto));
        customerBillPeriodManagementDetailService.rollbackCustomerBillPeriodManagementDetail(periodManagementDetailDto);
    }

    @Override
    public void dealOrderCustomerBillPeriodWithRefundOperation(AfterSalesVo afterSales) throws CustomerBillPeriodException {
        logger.info("执行退款运算处理订单冻结和占用的账期金额释放开始 afterSales:{}", JSON.toJSONString(afterSales));
        if (afterSales == null || afterSales.getRefundPeriodAmount() == null){
            throw new CustomerBillPeriodException("执行退款运算处理订单冻结和占用的账期金额释放参数错误");
        }
        if (afterSales.getRefundPeriodAmount().compareTo(BigDecimal.ZERO) < 1){
            logger.info("执行退款运算处理订单冻结和占用的账期金额释放的退款账期金额无需退还 afterSalesId:{}, refundPeriodAmount:{}", afterSales.getAfterSalesId(), afterSales.getRefundPeriodAmount());
            return;
        }

        CustomerBillPeriodUnfreezingDto customerBillPeriodUnfreezingDto = new CustomerBillPeriodUnfreezingDto();
        customerBillPeriodUnfreezingDto.setCompanyId(afterSales.getCompanyId());
        customerBillPeriodUnfreezingDto.setCustomerId(saleorderMapper.getCustomerIdByOrderId(afterSales.getSaleorderId()));
        customerBillPeriodUnfreezingDto.setOrderId(Long.valueOf(afterSales.getSaleorderId()));
        customerBillPeriodUnfreezingDto.setUnfreezingType(CustomerBillPeriodUseTypeEnum.AFTER_SALES_RETURN.getCode());
        customerBillPeriodUnfreezingDto.setRelatedId(Long.valueOf(afterSales.getAfterSalesId()));
        customerBillPeriodUnfreezingDto.setUnfreezingAmount(afterSales.getRefundPeriodAmount());
        customerBillPeriodUnfreezingDto.setAddTime(System.currentTimeMillis());
        logger.info("执行退款运算处理订单冻结和占用的账期金额释放 customerBillPeriodUnfreezingDto:{}", JSON.toJSONString(customerBillPeriodUnfreezingDto));
        customerBillPeriodService.unfreezingCustomerBillPeriodAmount(customerBillPeriodUnfreezingDto);
    }

    @Override
    public Boolean freezingBillPeriodAmountWhenOrderValid(Saleorder saleOrderQuery) {
        Objects.requireNonNull(saleOrderQuery, "dbSaleOrder is null ...");

        if (Objects.isNull(saleOrderQuery.getPaymentType()) || PaymentTypeEnum.PAY_BEFORE.getType().equals(saleOrderQuery.getPaymentType())) {
            logger.info("order pay type is null or invalid ... ");
            return false;
        }

        long orderId = saleOrderQuery.getSaleorderId().longValue();
        Integer companyId = saleOrderQuery.getCompanyId();
        Long customerId = saleorderMapper.getCustomerIdByOrderId(saleOrderQuery.getSaleorderId());

        if (Objects.isNull(customerId)) {
            throw new RuntimeException("not found customerId from order, please check ... orderId :" + saleOrderQuery.getSaleorderId());
        }

        CustomerBillPeriodFreezingDto freezingDto = new CustomerBillPeriodFreezingDto();
        freezingDto.setCustomerId(customerId);
        freezingDto.setCompanyId(companyId);
        freezingDto.setFreezingAmount(saleOrderQuery.getAccountPeriodAmount());
        freezingDto.setSettlementType(saleOrderQuery.getBillPeriodSettlementType());
        freezingDto.setAddTime(System.currentTimeMillis());
        freezingDto.setOrderId(orderId);

        boolean success = true;
        try {
            customerBillPeriodService.freezingCustomerBillPeriodAmount(freezingDto);
            logger.info("freezing billPeriod success , freezingDto :{}", freezingDto);
        } catch (CustomerBillPeriodException e) {
            success = false;
            // 暂时先打日志
            logger.error("order freezing failed ... saleOrderInfo :{} , errorMsg :{}", saleOrderQuery, e.getMessage(), e);
        }
        return success;
    }

    @Override
    public void occupyBillPeriodAmount(Saleorder saleOrderQuery) {
        long orderId = saleOrderQuery.getSaleorderId().longValue();
        Integer paymentType = saleOrderQuery.getPaymentType();
        Integer companyId = saleOrderQuery.getCompanyId();
        Long customerId = saleorderMapper.getCustomerIdByOrderId(saleOrderQuery.getSaleorderId());

        // 100%账期支付 进行占用
        if (PaymentTypeEnum.PAY_0.getType().equals(paymentType)) {
            occupancyCustomerBillPeriodAmount(BigDecimal.ZERO, orderId, customerId, companyId, true);
        }
    }

    @Override
    public boolean isPartialPayment(Integer paymentType) {
        return Objects.nonNull(paymentType) &&
                paymentType > PaymentTypeEnum.PAY_BEFORE.getType() &&
                paymentType <= PaymentTypeEnum.PAY_OTHER.getType();
    }

    // & --------------------------------------------------------- private method

    /**
     *  全部账期支付： 直接占用
     *  部分账期支付：待客户付款金额大于订单的预付金额，进行账期占用
     * @param orderId 订单ID
     * @param customerId 客户ID
     * @param companyId 公司Id
     * @param isFullPaymentPeriod  是否全部账期支付
     * @throws CustomerBillPeriodException
     */
    private void occupancyCustomerBillPeriodAmount(BigDecimal curPayAmount, Long orderId, Long customerId, Integer companyId, boolean isFullPaymentPeriod) {
        Objects.requireNonNull(orderId, "orderId is null ...");
        Objects.requireNonNull(customerId, "customerId is null ...");

        boolean isOccupancy = true;

        if (!isFullPaymentPeriod) {
            Saleorder saleOrderInfo = capitalBillMapper.getSaleorderCapitalById(orderId.intValue());
            if (Objects.isNull(saleOrderInfo) || !isPartialPayment(saleOrderInfo.getPaymentType())) {
                logger.warn("check method input fail ... ");
                return;
            }

            // 预付金额
            BigDecimal prepaidAmount = saleOrderInfo.getPrepaidAmount();
            if (prepaidAmount == null) {
                prepaidAmount = BigDecimal.ZERO;
            }
            // 已付金额 需要获取最新流水
            BigDecimal receivedAmount = saleOrderInfo.getReceivedAmount();
            if (receivedAmount == null) {
                receivedAmount = BigDecimal.ZERO;
            }
            if (curPayAmount == null) {
                curPayAmount = BigDecimal.ZERO;
            }

            if (receivedAmount.add(curPayAmount).compareTo(prepaidAmount) < 0) {
                isOccupancy = false;
            }
        }

        if (isOccupancy) {
            CustomerBillPeriodOccupancyDto occupancyDto = new CustomerBillPeriodOccupancyDto();

            occupancyDto.setCompanyId(companyId);
            occupancyDto.setOrderId(orderId);
            occupancyDto.setCustomerId(customerId);

            try {
                List<CustomerBillPeriodUseDetail> detailList = customerBillPeriodService.getOccupancyCustomerBillPeriodUseDetail(occupancyDto);
                if (CollectionUtils.isNotEmpty(detailList)) {
                    logger.info("该订单已经被占用, detail :{}", occupancyDto);
                    return;
                }

                customerBillPeriodService.occupancyCustomerBillPeriodFrozenAmount(occupancyDto);

                List<Invoice> invoiceInfoList = invoiceService.getInvoiceInfoByRelatedId(orderId.intValue(),
                        SysOptionConstant.ID_505);
                // 判断是否开票，如果开票进行逾期
                if (CollectionUtils.isNotEmpty(invoiceInfoList)) {
                    for (Invoice invoice : invoiceInfoList) {
                        Integer invoiceId = invoice.getInvoiceId();
                        logger.info("生成账期逾期编码为开票类型 invoiceId:{}", invoiceId);
                        Invoice invoiceInfo = invoiceMapper.getInvoiceBaseInfoByInvoiceId(invoiceId);
                        if (invoiceInfo == null) {
                            logger.error("开票生成账期逾期编码时发票信息不存在 error invoiceId:{}", invoiceId);
                            throw new CustomerBillPeriodException("开票生成账期逾期编码时发票信息不存在 invoiceId:" + invoiceId);
                        }
                        dealCustomerBillPeriodManagement(orderId.intValue(),
                                ORDER_INVOICE.getCode(),
                                invoice.getInvoiceId(),
                                invoiceInfo.getAmount());
                    }
                }

            } catch (CustomerBillPeriodException e) {

                logger.warn("order({}) occupancy failed ...  , errorMsg :{}", orderId, e.getMessage(), e);
            }
        }

    }
}
