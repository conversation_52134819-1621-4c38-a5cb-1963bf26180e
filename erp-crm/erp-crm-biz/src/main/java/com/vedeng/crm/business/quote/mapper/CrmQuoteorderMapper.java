package com.vedeng.crm.business.quote.mapper;
import com.vedeng.crm.business.business.domain.entity.CrmBusinessChanceEntity;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteShareDto;
import org.apache.ibatis.annotations.Param;

import com.vedeng.crm.business.quote.domain.dto.QuoteOrderGoodsExportVo;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity;

import java.util.List;
import java.util.Map;

public interface CrmQuoteorderMapper {
    int deleteByPrimaryKey(Integer quoteorderId);

    int insertSelective(CrmQuoteOrderEntity record);

    CrmQuoteOrderEntity selectByPrimaryKey(Integer quoteorderId);

    CrmQuoteOrderEntity selectByBusinessChanceId(Integer businessChanceId);

    int updateByPrimaryKeySelective(CrmQuoteOrderEntity record);

    int updateByPrimaryKey(CrmQuoteOrderEntity record);

    List<QuoteOrderGoodsExportVo> selectQuoteOrderGoodsExportVo(@Param("quoteorderId") Integer quoteorderId,@Param("isNeeds") Boolean isNeeds);

    CrmQuoteOrderEntity findByBussinessChanceId(@Param("bussinessChanceId") Integer bussinessChanceId);

    CrmQuoteOrderEntity getEffectOrders(@Param("traderId") Integer traderId);

    /**
     * 判断授权书申请表的授权产品数量
     * @param quoteorderId
     * @return
     */
    int getAuthorizationSum(@Param(value = "quoteorderId") Integer quoteorderId,@Param(value = "applyStatus") Integer applyStatus);


    CrmBusinessChanceEntity selectBusinessChanceByQuoteorderId(Integer quoteorderId);

    CrmQuoteShareDto getQuoteShardInfoById(@Param("quoteorderId") Integer quoteorderId);

    Integer getQuoteWithNoSkuInfoById(@Param("quoteorderId") Integer quoteorderId);


    /**
     * 查询报价单下是否有未审核通过的商品
     * @param quoteorderId
     * @return
     */
    List<Map<String,Object>> checkQuoteOrderSkuIfCheckStatus3(@Param("quoteorderId") Integer quoteorderId);

    /**
     * 更新选择的用户信息
     * @param crmQuoteOrderEntity
     * @return
     */
	int updateQuoteOrderForBuildUserIds(CrmQuoteOrderEntity crmQuoteOrderEntity);

}
