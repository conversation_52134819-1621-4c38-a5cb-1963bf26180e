package com.vedeng.erp.aftersale.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.AfterSalesInstallstion;
import com.vedeng.aftersales.model.vo.*;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.Company;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.billsync.task.model.entity.BankBillExtDo;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.DbRestInterfaceConstant;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.*;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.aftersale.domain.dto.AfterSaleDto;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesFollowUpRecordDto;
import com.vedeng.erp.aftersale.domain.dto.ProductSelectionDispatchDto;
import com.vedeng.erp.aftersale.domain.dto.YxbSnDto;
import com.vedeng.erp.aftersale.service.*;
import com.vedeng.erp.finance.dto.DownloadInvoice;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.finance.service.PayApplyAutoPayApi;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDetailDto;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDto;
import com.vedeng.finance.model.*;
import com.vedeng.finance.model.vo.PayApplyVo;
import com.vedeng.finance.service.BankBillService;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.goods.model.Goods;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.service.WarehouseGoodsOutService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;
import com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord;
import com.vedeng.orderstream.aftersales.model.AfterSalesFollowUpRecord;
import com.vedeng.orderstream.aftersales.model.AfterSalesRevenueRecord;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.*;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.TraderFinance;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.service.TraderCustomerService;
import com.vedeng.trader.service.TraderSupplierService;
import com.wms.constant.CancelReasonConstant;
import com.wms.service.CancelTypeService;
import com.wms.service.LogicalSaleorderChooseService;
import org.activiti.editor.language.json.converter.util.CollectionUtils;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/12 11:24
 * @desc :
 */
@Controller
@RequestMapping("/order/afterSalesCommon")
public class AfterSalesCommonController extends BaseController {

    public static Logger logger = LoggerFactory.getLogger(AfterSalesCommonController.class);

    @Autowired
    private OrgService orgService;

    @Autowired
    private AfterSalesFollowUpRecordService afterSalesFollowUpRecordService;

    @Autowired
    private AfterSalesRevenueRecordService afterSalesRevenueRecordService;

    @Autowired
    private AfterSalesExpenditureRecordService afterSalesExpenditureRecordService;

    @Autowired
    private AfterSalesService afterSalesOrderService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private AfterSalesInfoService afterSalesInfoService;

    @Autowired
    private SaleorderService saleorderService;

    @Value("${oss_url}")
    protected String domain;

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;

    @Autowired
    private AfterSalesCommonService afterSalesCommonService;

    @Autowired
    private PayApplyService payApplyService;

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Autowired
    private VerifiesRecordService verifiesRecordService;


    @Autowired
    private UserService userService;

    @Autowired
    private BankBillService bankBillService;

    @Autowired
    private CapitalBillService capitalBillService;

    @Autowired
    private CompanyService companyService;

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private InvoiceService invoiceService;

    @Resource
    private TraderSupplierService traderSupplierService;

    @Autowired
    private AfterSalesService afterSalesService;


    @Autowired
    private AuthService authService;

    @Value("${finance_role_name}")
    private String financeRoleNameStr;

    @Autowired
    @Qualifier("vedengSoapService")
    private VedengSoapService vedengSoapService;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private SysOptionDefinitionService sysOptionDefinitionService;

    @Autowired
    private AfterSalesInstallServiceRecordDetailService afterSalesInstallServiceRecordDetailService;

    @Autowired
    private AttachmentService attachmentService;

    @Autowired
    private WarehouseGoodsOutService warehouseGoodsOutService;

    @Autowired
    private FullyDigitalInvoiceApiService fullyDigitalInvoiceApiService;

    @Value("${oss_http}")
    private String ossHttp;
    @Autowired
    private TraderMapper traderMapper;

    @Autowired
    private CapitalBillApiService capitalBillApiService;

    @Autowired
    private PayApplyAutoPayApi payApplyAutoPayApi;

    @Autowired
    private PayApplyApiService payApplyApiService;

    /**
     * 新增售后跟进记录页面
     *
     * @param request
     * @param afterSalesFollowUpRecord
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addFollowUpRecord")
    public ModelAndView addFollowUpRecord(HttpServletRequest request, AfterSalesFollowUpRecord afterSalesFollowUpRecord) {

        ModelAndView mv = new ModelAndView();

        mv.setViewName("orderstream/aftersales/add_followUp_record");

        try {

            //获取部门信息,默认不加前缀
            List<Organization> orgList = orgService.getOrgList(ErpConst.ZERO, ErpConst.NJ_COMPANY_ID, false);

            if (Objects.nonNull(afterSalesFollowUpRecord.getRecordId())) {
                afterSalesFollowUpRecord = afterSalesFollowUpRecordService.getAfterSalesFollowUpRecordById(afterSalesFollowUpRecord.getRecordId());
            }
            mv.addObject("afterSalesFollowUpRecord", afterSalesFollowUpRecord);
            mv.addObject("orgList", orgList);

        } catch (Exception e) {
            logger.error("售后跟进记录 error", e);
        }

        return mv;

    }

    /**
     * 取消下派接口
     */
    @ResponseBody
    @RequestMapping(value = "/cancelDispatch")
    @NoNeedAccessAuthorization
    public ResultInfo<?> cancelDispatch(@RequestBody ProductSelectionDispatchDto productSelectionDispatchDto) {
        if (afterSalesCommonService.cancelDispatch(productSelectionDispatchDto)) {
            return ResultInfo.success("取消下派成功");
        }
        return ResultInfo.error("取消下派失败");
    }

    /**
     * 下派确认接口
     */
    @ResponseBody
    @RequestMapping(value = "/confirmationDispatch")
    @NoNeedAccessAuthorization
    public ResultInfo<?> confirmationDispatch(@RequestBody AfterSaleDto afterSaleDto) {
        if (afterSalesCommonService.confirmationDispatch(afterSaleDto)) {
            return ResultInfo.success("下派成功");
        }
        return ResultInfo.error("下派失败");
    }
    /**
     * 下派商品选择
     */
    @RequestMapping(value = "/productSelectionDispatch")
    @NoNeedAccessAuthorization
    public ModelAndView productSelectionDispatch(Integer afterSalesId) {
        //根据售后单id获取售后商品列表
        List<AfterSalesGoodsVo> atGoodsInfoByAfterSalesId = afterSalesOrderService.getAtGoodsInfoByAfterSalesId(afterSalesId);
        ModelAndView mv = new ModelAndView();
        List<ProductSelectionDispatchDto> products = new ArrayList<>();
        atGoodsInfoByAfterSalesId.forEach(x->{
            ProductSelectionDispatchDto productSelectionDispatchDto = ProductSelectionDispatchDto.
                    builder()
                    .afterSalesId(afterSalesId)
                    .productId(x.getGoodsId())
                    .productBrand(x.getBrandName())
                    .productModel(x.getModel())
                    .productName(x.getGoodsName())
                    .afterSalesQuantity(x.getNum())
                    .sku(x.getSku())
                    .build();
            products.add(productSelectionDispatchDto);
        });
        mv.addObject("products", JSON.toJSONString(products));
        mv.setViewName("orderstream/aftersales/product_selection_dispatch");
        return mv;

    }
    /**
     * 提供接口给医修帮保存新增售后跟进记录页面
     *
     * @param afterSalesFollowUpRecordDto
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveFollowUpRecordForYXB")
    @NoNeedAccessAuthorization
    public ResultInfo<?> saveFollowUpRecordForYXB(@RequestBody  AfterSalesFollowUpRecordDto afterSalesFollowUpRecordDto) {

        try {

            if (ObjectUtils.isEmpty(afterSalesFollowUpRecordDto.getAfterSalesNo())){
                logger.error("保存售后跟进记录 afterSalesNo is null，操作失败！");
                return ResultInfo.error("保存售后跟进记录失败,售后单号为空！");
            }
            afterSalesFollowUpRecordDto.setAddTime(System.currentTimeMillis());

            //新增数据
            Boolean s = afterSalesCommonService.saveYxbFollowRecord(afterSalesFollowUpRecordDto);
            if (!s){
                return ResultInfo.error("保存售后跟进记录失败,售后单号为空");
            }
            return ResultInfo.success("保存售后跟进记录成功");
        }catch (Exception e){
            logger.error("保存售后跟进记录 error", e);
            return ResultInfo.error("保存售后跟进记录,error！");
        }

    }

    /**
     * 保存新增售后跟进记录页面
     *
     * @param request
     * @param afterSalesFollowUpRecord
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveFollowUpRecord")
    public ResultInfo<?> saveFollowUpRecord(HttpServletRequest request, AfterSalesFollowUpRecord afterSalesFollowUpRecord) {

        try {

            if (ObjectUtils.isEmpty(afterSalesFollowUpRecord.getAfterSalesId())){
                logger.error("保存售后跟进记录 afterSalesId is null，操作失败！");
                return ResultInfo.error("保存售后跟进记录失败！");
            }

            if (Objects.nonNull(afterSalesFollowUpRecord.getRecordId())) {
                afterSalesFollowUpRecord.setUpdater(getSessionUser(request).getUserId());
                afterSalesFollowUpRecord.setModeTime(System.currentTimeMillis());
                afterSalesFollowUpRecordService.updateAfterSalesFollowUpRecordSelectById(afterSalesFollowUpRecord);
                return ResultInfo.success();
            }

            afterSalesFollowUpRecord.setCreator(getSessionUser(request).getUserId());
            afterSalesFollowUpRecord.setAddTime(System.currentTimeMillis());

            //新增数据
            afterSalesFollowUpRecordService.insert(afterSalesFollowUpRecord);

            return ResultInfo.success();

        }catch (Exception e){
            logger.error("保存售后跟进记录 error", e);
            return ResultInfo.error("保存售后跟进记录,error！");
        }

    }


    /**
     * 新增售后收入记录
     *
     * @param request
     * @param afterSalesRevenueRecord
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addRevenueRecord")
    public ModelAndView addRevenueRecord(HttpServletRequest request, AfterSalesRevenueRecord afterSalesRevenueRecord) {

        ModelAndView mv = new ModelAndView();

        mv.setViewName("orderstream/aftersales/add_revenue_record");

        try {

            //查询费用类型(3399)字典表子集ListID
            List<SysOptionDefinition> typeList = getSysOptionDefinitionList(SysOptionConstant.AFTER_SALES_COST_TYPE);

            mv.addObject("typeList", typeList);
            mv.addObject("afterSalesRevenueRecord", afterSalesRevenueRecord);

        } catch (Exception e) {
            logger.error("新增售后收入记录 error", e);
        }

        return mv;

    }

    /**
     * 保存新增售后收入记录页面
     *
     * @param request
     * @param afterSalesRevenueRecord
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveRevenueRecord")
    public ResultInfo<?> saveRevenueRecord(HttpServletRequest request, AfterSalesRevenueRecord afterSalesRevenueRecord) {

        try {

            if (ObjectUtils.isEmpty(afterSalesRevenueRecord.getAfterSalesId())){
                logger.error("保存售后收入记录 afterSalesId is null，操作失败！");
                return ResultInfo.error("保存售后收入记录失败！");
            }

            afterSalesRevenueRecord.setCreator(getSessionUser(request).getUserId());
            afterSalesRevenueRecord.setAddTime(System.currentTimeMillis());

            //新增数据
            afterSalesRevenueRecordService.insert(afterSalesRevenueRecord);

            return ResultInfo.success();

        }catch (Exception e){
            logger.error("保存售后收入记录 error", e);
            return ResultInfo.error("保存售后收入记录,error！");
        }

    }

    /**
     * 新增售后支出记录
     *
     * @param request
     * @param afterSalesExpenditureRecord
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addExpenditureRecord")
    public ModelAndView addExpenditureRecord(HttpServletRequest request, AfterSalesExpenditureRecord afterSalesExpenditureRecord) {

        ModelAndView mv = new ModelAndView();
        mv.setViewName("orderstream/aftersales/add_expenditure_record");

        try {

            //获取部门信息
            List<Organization> orgList = orgService.getOrgList(ErpConst.ZERO, ErpConst.NJ_COMPANY_ID, false);

            mv.addObject("orgList", orgList);

            //查询费用类型(3399)字典表子集ListID
            List<SysOptionDefinition> typeList = getSysOptionDefinitionList(SysOptionConstant.AFTER_SALES_COST_TYPE);

            mv.addObject("typeList", typeList);
            mv.addObject("afterSalesExpenditureRecord", afterSalesExpenditureRecord);

        } catch (Exception e) {
            logger.error("新增售后支出记录 error", e);
        }

        return mv;

    }

    /**
     * 保存新增售后支出记录页面
     *
     * @param request
     * @param afterSalesExpenditureRecord
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveExpenditureRecord")
    public ResultInfo<?> saveExpenditureRecord(HttpServletRequest request, AfterSalesExpenditureRecord afterSalesExpenditureRecord) {

        try {

            if (ObjectUtils.isEmpty(afterSalesExpenditureRecord.getAfterSalesId())){
                logger.error("保存售后支出记录 afterSalesId is null，操作失败！");
                return ResultInfo.error("保存售后支出记录失败！");
            }

            afterSalesExpenditureRecord.setCreator(getSessionUser(request).getUserId());
            afterSalesExpenditureRecord.setAddTime(System.currentTimeMillis());

            //新增数据
            afterSalesExpenditureRecordService.insert(afterSalesExpenditureRecord);

            return ResultInfo.success();

        }catch (Exception e){
            logger.error("保存售后支出记录 error", e);
            return ResultInfo.error("保存售后支出记录,error！");
        }

    }

    /**
     * 编辑售后信息
     *
     * @param request
     * @param afterSalesVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/editAfterSalesInfo")
    public ModelAndView editAfterSalesInfo(HttpServletRequest request, AfterSalesVo afterSalesVo) {

        ModelAndView mv = new ModelAndView();

        mv.setViewName("orderstream/aftersales/edit_afterSales_info");

        try {
            //售后服务地区

            if (ObjectUtils.notEmpty(afterSalesVo.getAreaId())) {
                regionInfo(mv, afterSalesVo.getAreaId());
            }

            //获取售后详情信息
            if(null == afterSalesVo || ObjectUtils.isEmpty(afterSalesVo.getAfterSalesId())){
                logger.error("编辑售后信息,AfterSalesCommonController.editAfterSalesInfo,afterSalesId is null");
                return fail(mv);
            }

            mv.addObject("afterSalesVo", afterSalesVo);
            //获取附件，回显
            afterSalesVo.setAttachmentList(afterSalesCommonService.getAttachmentListByParam(afterSalesVo));

            AfterSalesDetailVo vo = afterSalesOrderService.getAfterSalesDetailByAfterSalesId(afterSalesVo.getAfterSalesId());
            mv.addObject("afterSalesDetailVo", vo);

            //获取部门信息
            List<Organization> orgList = orgService.getOrgList(ErpConst.ZERO, ErpConst.NJ_COMPANY_ID, false);
            mv.addObject("orgList", orgList);

            //获取售后原因信息
            List<SysOptionDefinition> reasonList = getSysOptionDefinitionList(SysOptionConstant.AFTER_SALES_REASON_UPGRADE);
            mv.addObject("reasonList", reasonList);

            mv.addObject("domain", domain);

            //获取售后报单人信息
            TraderContact traderContact = new TraderContact();
            traderContact.setTraderId(vo.getTraderId());
            traderContact.setTraderType(ErpConst.ONE);
            traderContact.setIsEnable(ErpConst.ONE);

            List<TraderContact> traderContactList = afterSalesInfoService.getTraderContact(traderContact);

            if (CollectionUtils.isNotEmpty(traderContactList)) {
                mv.addObject("traderContactList", traderContactList);
            }

        } catch (Exception e) {
            logger.error("新增售后支出记录 error", e);
        }

        return mv;

    }

    /**
     * 保存售后信息
     * AfterSalesVo afterSalesDetailVo afterSalesDetailVo is not used because of the region
     * But it can be replaced
     *
     * @param request
     * @param afterSalesVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveAfterSalesInfo")
    public ResultInfo<?> saveAfterSalesInfo(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                            @RequestParam(required = false, value = "fileName") String[] fileName,
                                            @RequestParam(required = false, value = "fileUri") String[] fileUri) {

        try{
            if(null == afterSalesVo){
                logger.error("保存售后信息 afterSalesVo is null，操作失败！");
                return ResultInfo.error("保存售后信息失败！");
            }

            if (ObjectUtils.isEmpty(afterSalesVo.getAfterSalesId())){
                logger.error("保存售后信息 afterSalesId is null，操作失败！");
                return ResultInfo.error("保存售后信息失败！");
            }

            afterSalesVo.setAddTime(DateUtil.sysTimeMillis());

            afterSalesVo.setCreator(getSessionUser(request) == null ? ErpConst.TWO : getSessionUser(request).getUserId());

            //添加附件
            if (fileName != null && fileUri != null) {

                Boolean res = afterSalesCommonService.saveAttachment(afterSalesVo, fileName, fileUri);

                if (!res) {
                    return ResultInfo.error("保存售后信息附件,失败！afterSalesId:" + afterSalesVo.getAfterSalesId());
                }
            }

            AfterSalesDetailVo vo = new AfterSalesDetailVo();
            vo.setAfterSalesDetailId(afterSalesVo.getAfterSalesDetailId());

            if (ObjectUtils.isEmpty(afterSalesVo.getAfterSalesDetailId())) {
                //获取detailId
                vo = afterSalesOrderService.getAfterSalesDetailByAfterSalesId(afterSalesVo.getAfterSalesId());
            }

            //获取售后单报单人 信息
            if (ObjectUtils.notEmpty(afterSalesVo.getTraderContactId())) {
                TraderContact traderContact = afterSalesInfoService.selectTraderContactInfoByPrimaryKey(afterSalesVo.getTraderContactId());
                vo.setTraderContactName(traderContact.getName());
                vo.setTraderContactMobile(traderContact.getMobile());
                vo.setTraderContactTelephone(traderContact.getTelephone());
            }

            //获取地区名（限安调/维修/第三方）
            if (ObjectUtils.notEmpty(afterSalesVo.getZone())) {
                afterSalesVo.setArea(afterSalesCommonService.getAddressByAreaId(afterSalesVo.getZone()));
                vo.setAreaId(afterSalesVo.getZone());
            }

            //需要更新的数据
            //售后原因
            vo.setReason(afterSalesVo.getReason());
            //第一责任部门
            vo.setFirstResponsibleDepartment(afterSalesVo.getFirstResponsibleDepartment());
            //详情说明
            vo.setComments(afterSalesVo.getComments());
            //售后单报单人
            vo.setTraderContactId(afterSalesVo.getTraderContactId());
            //售后联系人
            vo.setAfterConnectUserName(afterSalesVo.getAfterConnectUserName());
            //售后联系电话
            vo.setAfterConnectPhone(afterSalesVo.getAfterConnectPhone());
            //售后服务地址|收货地址
            vo.setAddress(afterSalesVo.getAddress());
            //售后服务地区
            vo.setArea(afterSalesVo.getArea());

            //新增detail数据
            afterSalesInfoService.updateByPrimaryKeyDetailInfo(vo);

            return ResultInfo.success();

        }catch (Exception e){
            logger.error("保存售后信息 error", e);
            return ResultInfo.error("保存售后信息,error！afterSalesId:"+ afterSalesVo.getAfterSalesId()+" afterSalesDetailId:"+afterSalesVo.getAfterSalesDetailId());
        }

    }

    /**
     * <b>Description:</b><br> 订单合同回传初始化
     *
     * @param request
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年7月24日 下午2:19:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/contractReturnInit")
    public ModelAndView contractReturnInit(HttpServletRequest request, Integer afterSalesId) {

        ModelAndView mv = new ModelAndView();

        mv.addObject("afterSalesId", afterSalesId);
        mv.setViewName("orderstream/aftersales/contract_return");

        return mv;

    }

    /**
     * <b>Description:</b><br> 订单合同回传保存
     *
     * @param request
     * @param attachment
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年7月24日 下午2:55:15
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/contractReturnSave")
    @SystemControllerLog(operationType = "add", desc = "订单合同回传保存")
    public ResultInfo<?> contractReturnSave(HttpServletRequest request, Attachment attachment) {

        User user = getSessionUser(request);

        if (attachment != null) {

            if (afterSalesCommonService.checkPic(attachment.getUri())) {
                attachment.setAttachmentType(SysOptionConstant.ID_460);
            }

        } else {
            attachment.setAttachmentType(SysOptionConstant.ID_461);
        }

        if (user != null) {
            attachment.setCreator(user.getUserId());
            attachment.setAddTime(DateUtil.sysTimeMillis());
        }

        return saleorderService.saveSaleorderAttachment(attachment);

    }

    /**
     * 自建售后安调/维修页面
     * 售后管理-售后列表-新增售后按钮
     *
     * @param request
     * @param flag
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addSelfAfterSalesAtWx")

    public ModelAndView addSelfAfterSalesAtWx(HttpServletRequest request, String flag) {

        ModelAndView mv = new ModelAndView();

        try {
            List<Region> provinceList = regionService.getRegionByParentId(ErpConst.ONE);

            mv.addObject("provinceList", provinceList);
            mv.addObject("domain", domain);

            mv.addObject("flag", ObjectUtils.isEmpty(flag) ? "at" : flag);

            mv.setViewName("orderstream/aftersales/add_self_afterSales_atwx");

        } catch (Exception e) {
            logger.error("自建售后安调/维修页面 error", e);
        }
        return mv;
    }

    /**
     * 保存自建售后安调/维修页面
     *
     * @param request
     * @param afterSalesVo
     * @param fileName
     * @param fileUri
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveSelfAfterSalesAtWx")

    public ModelAndView saveSelfAfterSalesAtWx(HttpServletRequest request, AfterSalesVo afterSalesVo,
                                               @RequestParam(required = false, value = "fileName") String[] fileName,
                                               @RequestParam(required = false, value = "fileUri") String[] fileUri) {

        ModelAndView mav = new ModelAndView();

        User user = getSessionUser(request);

        try {

            afterSalesVo.setAttachName(fileName);
            afterSalesVo.setAttachUri(fileUri);

            //第三方
            afterSalesVo.setSubjectType(SysOptionConstant.ID_537);
            afterSalesVo.setAtferSalesStatus(ErpConst.ZERO);
            afterSalesVo.setServiceUserId(user.getUserId());
            afterSalesVo.setStatus(ErpConst.ZERO);
            afterSalesVo.setValidStatus(ErpConst.ZERO);
            afterSalesVo.setDomain(domain);
            afterSalesVo.setCompanyId(ErpConst.NJ_COMPANY_ID);

            //第三方退款
            if (AfterSalesProcessEnum.THIRD_AMOUNT_RETURN.getCode().equals(afterSalesVo.getType())) {
                afterSalesVo.setRefundAmountStatus(ErpConst.ONE);
            }

            ResultInfo<?> res = afterSalesOrderService.saveAddAfterSales(afterSalesVo, user);

            if (ErpConst.ZERO.equals(res.getCode())) {
                mav.addObject("url", "/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + res.getData());
                return success(mav);
            } else {
                return fail(mav);
            }

        }catch (Exception e){
            logger.error("保存自建售后安调/维修页面 error", e);
            return fail(mav);
        }

    }

    /**
     * <b>Description:</b><br> 保存编辑售后
     *
     * @param request
     * @param afterSalesVo
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月11日 下午1:27:56
     */
    @ResponseBody
    @RequestMapping(value = "/saveEditAfterSales")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑售后")
    public ModelAndView saveEditAfterSales(HttpServletRequest request, AfterSalesVo afterSalesVo, String beforeParams,
                                           @RequestParam(required = false, value = "fileName") String[] fileName,
                                           @RequestParam(required = false, value = "fileUri") String[] fileUri) {

        User user = getSessionUser(request);

        ModelAndView mav = new ModelAndView();

        if (!afterSalesService.checkWhenEditAfterSales(afterSalesVo.getAfterSalesId())) {
            mav.addObject("message", "保存操作失败：" + "售后单在待确认状态下才支持编辑操作");
            return fail(mav);
        }

        // add by franlin.wu for [公司ID] at 2018-12-19 begin
        if (null != user) {
            // 公司ID
            afterSalesVo.setCompanyId(ErpConst.NJ_COMPANY_ID);
        }
        // add by franlin.wu for [公司ID] at 2018-12-19 end

        afterSalesVo.setAttachName(fileName);
        afterSalesVo.setAttachUri(fileUri);
        afterSalesVo.setDomain(domain);

        //本处适应第三方
        afterSalesVo.setTraderType(ErpConst.ONE);

        if (ObjectUtils.notEmpty(afterSalesVo.getAfterSalesId())) {

            AfterSales afterSale = afterSalesOrderService.getAfterSalesById(afterSalesVo.getAfterSalesId());

            if (null != afterSale && (AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSale.getType()) || AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSale.getType()))) {
                afterSalesVo.setTraderType(ErpConst.THREE);
            }

        }

        afterSalesVo.setSubjectType(SysOptionConstant.ID_537);//第三方

        if (AfterSalesProcessEnum.THIRD_AMOUNT_RETURN.getCode().equals(afterSalesVo.getType())) {
            afterSalesVo.setRefundAmountStatus(ErpConst.ONE);
        }


        try {

            if (StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSalesVo.getType())) {


                //订单已复核或完成，无法取消 false 取消失败
                if (!cancelTypeService.cancelOutSaleOutMethod(afterSalesVo.getOrderNo(), CancelReasonConstant.EDIT_AFTER_ORDER)) {
                    mav.addObject("message", "物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
                    return fail(mav);
                }

            }

            /*ResultInfo<?> res = afterSalesOrderService.saveEditAfterSales(afterSalesVo, user);
            if(ErpConst.ZERO.equals(res.getCode())){
                if(StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSalesVo.getType())){
                    logicalSaleorderChooseService.putSaleAfterReturnStartGoods(afterSalesVo.getOrderNo(),user);
                }
                mav.addObject("url", "./viewAfterSalesDetail.do?afterSalesId=" + res.getData());
                return success(mav);
            }else{
                return fail(mav);
            }*/
            //From the DB migration
            afterSalesVo.setUpdater(user.getUserId());
            afterSalesVo.setModTime(DateUtil.sysTimeMillis());

            //必须到市
            if (ObjectUtils.notEmpty(afterSalesVo.getZone())) {
                afterSalesVo.setArea(afterSalesCommonService.getAddressByAreaId(afterSalesVo.getZone()));
            }

            Integer res = afterSalesCommonService.saveEditAfterSales(afterSalesVo);

            if (res > 0) {

                logger.info("操作成功：afterId" + res);

                //更新售后单updataTime
                orderCommonService.updateAfterOrderDataUpdateTime(res, null, OrderDataUpdateConstant.AFTER_ORDER_GENERATE);

                logger.info("更新售后单：" + afterSalesVo.getType());

                if (StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSalesVo.getType())) {
                    logicalSaleorderChooseService.putSaleAfterReturnStartGoods(afterSalesVo.getOrderNo(), user);
                }

                mav.addObject("url", "/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + res);
                return success(mav);

            } else {
                logger.info("操作失败");
                return fail(mav);
            }
            //		mav.addObject("refresh", "true_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项
            //		mav.addObject("url","./getAfterSalesPage.do");

        } catch (Exception e) {
            logger.error("保存编辑售后失败error 售后单号:" + afterSalesVo.getAfterSalesId(), e);
            return fail(mav);
        }

    }

    /**
     * <b>Description:</b><br> 退票材料页面
     *
     * @param request
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年7月24日 下午2:19:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/ticketReturnInit")
    public ModelAndView ticketReturnInit(HttpServletRequest request, Integer afterSalesId) {

        ModelAndView mv = new ModelAndView();

        mv.addObject("afterSalesId", afterSalesId);
        mv.setViewName("orderstream/aftersales/ticket_return");

        return mv;

    }

    /**
     * <b>Description:</b><br> 编辑售后服务费
     *
     * @param request
     * @param
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月24日 下午2:55:14
     */
    @ResponseBody
    @RequestMapping(value = "/editInstallstionPage")
    public ModelAndView editInstallstionPage(HttpServletRequest request, AfterSalesDetail afterSalesDetail, String flag) throws IOException {

        ModelAndView mav = new ModelAndView("orderstream/aftersales/edit_afterSales_installation");

        if (ObjectUtils.notEmpty(flag) && !"bth".equals(flag)) {
            /*AfterSalesDetailVo afterSalesDetailVo = afterSalesOrderService.getAfterSalesDetailVoByParam(afterSalesDetail);*/
            //From the DB migration
            AfterSalesDetailVo afterSalesDetailVo = afterSalesCommonService.getAfterSalesDetailVoByParam(afterSalesDetail);

            mav.addObject("afterSalesDetail", afterSalesDetailVo);
            mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(afterSalesDetailVo)));
        } else {
            mav.addObject("afterSalesDetail", afterSalesDetail);
            mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(afterSalesDetail)));
        }

        mav.addObject("flag", flag);

        return mav;

    }

    /**
     * <b>Description:</b><br> 保存编辑的售后服务费
     *
     * @param request
     * @param afterSalesDetail
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月25日 上午8:46:23
     */
    @ResponseBody
    @RequestMapping(value = "/saveEditInstallstion")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑的售后服务费")
    public ResultInfo<?> saveEditInstallstion(HttpServletRequest request, AfterSalesDetail afterSalesDetail,
                                              String beforeParams, String invoiceTraderContactIds, String invoiceTraderAddressIds) {
        if (afterSalesDetail.getIsSendInvoice() == null) {
            afterSalesDetail.setIsSendInvoice(ErpConst.ONE);
        }

        if (ObjectUtils.notEmpty(invoiceTraderAddressIds)) {
            String[] addresses = invoiceTraderAddressIds.split("\\|");
            if (addresses.length > 0 && ObjectUtils.notEmpty(addresses[0])) {
                afterSalesDetail.setInvoiceTraderAddressId(Integer.valueOf(addresses[0]));
            }
            if (addresses.length > 1 && ObjectUtils.notEmpty(addresses[1])) {
                afterSalesDetail.setInvoiceTraderArea(addresses[1]);
            }
            if (addresses.length > 2 && ObjectUtils.notEmpty(addresses[2])) {
                afterSalesDetail.setInvoiceTraderAddress(addresses[2]);
            }
        }

        if (ObjectUtils.notEmpty(invoiceTraderContactIds)) {
            String[] contacts = invoiceTraderContactIds.split("\\|");
            if (contacts.length > 0 && ObjectUtils.notEmpty(contacts[0])) {
                afterSalesDetail.setInvoiceTraderContactId(Integer.valueOf(contacts[0]));
            }
            if (contacts.length > 1 && ObjectUtils.notEmpty(contacts[1])) {
                afterSalesDetail.setInvoiceTraderContactName(contacts[1]);
            }
            if (contacts.length > 2) {
                if (ObjectUtils.notEmpty(contacts[2])) {
                    afterSalesDetail.setInvoiceTraderContactTelephone(contacts[2]);
                } else {
                    afterSalesDetail.setInvoiceTraderContactTelephone("");
                }

            }
            if (contacts.length > 3 && ObjectUtils.notEmpty(contacts[3])) {
                afterSalesDetail.setInvoiceTraderContactMobile(contacts[3]);
            }
        }

        ResultInfo<?> res = afterSalesOrderService.saveEditInstallstion(afterSalesDetail);

        if (res == null) {
            return new ResultInfo<>();
        }

        // 收款节点二，刷新状态
        afterSalesCommonService.saveCollectionAmountAtStatus(afterSalesDetail.getAfterSalesId());

        return res;

    }

    /**
     * 售后安调-维修-新增工程师
     * use old code
     *
     * @param request
     * @param afterSales
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addAfterSalesAtWxEngineer")
    public ModelAndView addAfterSalesAtWxEngineer(HttpServletRequest request, AfterSalesVo afterSales, Integer isAtwx) {

        ModelAndView mav = new ModelAndView();

        mav.addObject("isAtwx", isAtwx);

        if (!SysOptionConstant.ID_537.equals(afterSales.getSubjectType())) {

            //获取商品
            /*List<AfterSalesGoodsVo> list = afterSalesOrderService.getAfterSalesInstallstionVoByParam(afterSales);*/
            //From the DB migration
            List<AfterSalesGoodsVo> list = afterSalesService.getAfterSalesGoodsVoListByParamNew(afterSales);

            mav.addObject("list", list);
            mav.setViewName("orderstream/aftersales/add_afterSales_atwx_engineer");
        } else {
            //第三方安调
            mav.setViewName("orderstream/aftersales/add_afterSales_engineer");
        }

        AfterSalesInstallstionVo afterSalesInstallstionVo = new AfterSalesInstallstionVo();
        Calendar c = Calendar.getInstance();

        mav.addObject("end", c.getTimeInMillis());
        c.add(Calendar.DAY_OF_MONTH, ErpConst.ONE);

        mav.addObject("start", c.getTimeInMillis());

        afterSalesInstallstionVo.setAfterSalesId(afterSales.getAfterSalesId());
        afterSalesInstallstionVo.setAreaId(afterSales.getAreaId());

        mav.addObject("afterSalesInstallstionVo", afterSalesInstallstionVo);

        return mav;

    }

    /**
     * 保存新增售后产品和工程师
     * use old code
     *
     * @param request
     * @param afterSalesInstallstionVo
     * @param afterSaleNums
     * @param start
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveAddAfterSalesEngineer")
    @SystemControllerLog(operationType = "add", desc = "保存新增售后产品和工程师的关系")
    public ModelAndView saveAddAfterSalesEngineer(HttpServletRequest request, AfterSalesInstallstionVo afterSalesInstallstionVo,
                                                  @RequestParam(required = false, value = "afterSaleNums") String[] afterSaleNums, String start, Integer isAtwx) {
        ModelAndView mav = new ModelAndView();

        User user = getSessionUser(request);

        //选中的产品ID和售后数量
        afterSalesInstallstionVo.setAfterSalesNums(afterSaleNums);
        //新增-当前时间
        afterSalesInstallstionVo.setServiceTime(DateUtil.convertLong(start, "yyyy-MM-dd"));

        //售后安调ID
        if (ObjectUtils.isEmpty(afterSalesInstallstionVo.getAfterSalesInstallstionId())) {
            afterSalesInstallstionVo.setAddTime(DateUtil.sysTimeMillis());
            afterSalesInstallstionVo.setCreator(user.getUserId());
        }

        afterSalesInstallstionVo.setModTime(DateUtil.sysTimeMillis());
        afterSalesInstallstionVo.setUpdater(user.getUserId());

        afterSalesInstallstionVo.setIsAtwx(isAtwx);
        int res = afterSalesCommonService.saveAfterSalesEngineer(afterSalesInstallstionVo);

        if (res > 0) {

            mav.addObject("refresh", "true_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项

            if (AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(afterSalesInstallstionVo.getType()) || AfterSalesProcessEnum.AFTERSALES_WX.getCode().equals(afterSalesInstallstionVo.getType())
                    || AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(afterSalesInstallstionVo.getType()) || AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(afterSalesInstallstionVo.getType())) {
                mav.addObject("url", "/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + afterSalesInstallstionVo.getAfterSalesId() + "&traderType=1");
            } else {
                mav.addObject("url", "/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + afterSalesInstallstionVo.getAfterSalesId());
            }

            //需求变更
            afterSalesCommonService.savePayAmountAtStatuts(afterSalesInstallstionVo.getAfterSalesId());

            return success(mav);

        } else if (res == 0) {
            return fail(mav);
        } else {
            mav.addObject("message", "此工程师已存在！");
            return fail(mav);
        }

    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/viewbaseinfo")
    public String viewBaseInfo(HttpServletRequest request, Goods goods) {

        return "redirect:/goods/vgoods/viewSku.do?skuId=" + goods.getGoodsId();

    }

    /**
     * reusing old code
     * <b>Description:</b><br> 查询工程师的分页信息
     *
     * @param request
     * @param afterSales
     * @return
     * @throws UnsupportedEncodingException
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月27日 下午1:52:24
     */
    @ResponseBody
    @RequestMapping(value = "/getEngineerPage")
    public ModelAndView getEngineerPage(HttpServletRequest request, AfterSalesVo afterSales,
                                        @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                        @RequestParam(required = false) Integer pageSize) throws UnsupportedEncodingException {

        ModelAndView mav = new ModelAndView("/orderstream/aftersales/add_afterSales_engineer_page");

        afterSales.setName(URLDecoder.decode(URLDecoder.decode(afterSales.getSearchName(), "UTF-8"), "UTF-8"));
        afterSales.setSearchName(URLDecoder.decode(URLDecoder.decode(afterSales.getSearchName(), "UTF-8"), "UTF-8"));

        Page page = getPageTag(request, pageNo, pageSize);

        afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);
        Map<String, Object> map = afterSalesCommonService.getEngineerPage(afterSales, page);

        if (!map.isEmpty()) {

            if (map.containsKey("list")) {
                List<EngineerVo> list = (List<EngineerVo>) map.get("list");
                mav.addObject("list", list);
            }

            if (map.containsKey("page")) {
                page = (Page) map.get("page");
                mav.addObject("page", page);
            }

        }

        mav.addObject("afterSales", afterSales);

        return mav;

    }

    /**
     * <b>Description:</b><br> 编辑款项退还信息
     *
     * @param afterSalesVo
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月23日 上午11:30:44
     */
    @ResponseBody
    @FormToken(save = true)
    @RequestMapping(value = "/editAfterSalesPayApply")
    public ModelAndView editAfterSalesPayApply(AfterSalesVo afterSalesVo) {

        ModelAndView mav = new ModelAndView("/orderstream/aftersales/add_afterSales_payApply");

        try {

            //本处适应第三方
            afterSalesVo.setTraderType(ErpConst.ONE);
            if (ObjectUtils.notEmpty(afterSalesVo.getAfterSalesId())) {

                AfterSales afterSale = afterSalesOrderService.getAfterSalesById(afterSalesVo.getAfterSalesId());

                if (null != afterSale && (AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSale.getType()) || AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSale.getType()))) {
                    afterSalesVo.setTraderType(ErpConst.THREE);
                }

            }

            afterSalesVo.setCompanyId(ErpConst.NJ_COMPANY_ID);
            AfterSalesVo afterSalesInfo = afterSalesService.getAfterSalesVoDetail(afterSalesVo);

            mav.addObject("afterSales", afterSalesInfo);
            mav.addObject("canApplyAmount", afterSalesInfo.getFinalRefundableAmount()
                    .subtract(afterSalesService.getHaveRefundedAmount(afterSalesInfo)));
            mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(afterSalesVo)));

        } catch (Exception e) {
            logger.error("editAfterSalesPayApply warn afterSalesVo:{}", JSON.toJSONString(afterSalesVo));
        }

        return mav;

    }

    /**
     * <b>Description:</b><br>
     * 保存售后申请付款
     *
     * @param request
     * @param payApplyVo
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年8月30日 下午6:12:54
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveApplyPayment")
    @SystemControllerLog(operationType = "add", desc = "保存申请付款")
    public ResultInfo<?> saveApplyPayment(HttpServletRequest request, PayApplyVo payApplyVo) {

        logger.info("saveApplyPayment start payApplyVo:{}", JSON.toJSONString(payApplyVo));
        ResultInfo resultInfo = null;

        try {

            User user = getSessionUser(request);

            //保存售后付款申请
            resultInfo = afterSalesOrderService.saveRefundApplyPay(payApplyVo, user, true);

            if (!ErpConst.ZERO.equals(resultInfo.getCode())) {
                return ResultInfo.error();
            }

            payApplyVo.setPayApplyId((Integer) resultInfo.getData());
            //发起付款申请审批流
            saveApplyApprovalProcess(payApplyVo, user, request);
            resultInfo = new ResultInfo(ErpConst.ZERO, "发起付款申请审核成功");
        } catch (Exception e) {
            logger.error("saveApplyPayment error", e);
            resultInfo = ResultInfo.error("任务完成操作失败：" + e.getMessage());
        }
        return resultInfo;

    }

    /**
     * <b>Description:</b><br>
     * 售后付款申请发起审批流
     *
     * @param payApplyVo, user, request
     * @return com.vedeng.common.model.ResultInfo
     * @Note <b>Author:</b> Thor <br>
     * <b>Date:</b> 2021/10/18 13:49
     */
    private void saveApplyApprovalProcess(PayApplyVo payApplyVo, User user, HttpServletRequest request) {

        try {

            Map<String, Object> variableMap = new HashMap<String, Object>();
            // 开始生成流程(如果没有taskId表示新流程需要生成)
            AfterSalesVo afterSalesVo = new AfterSalesVo();
            afterSalesVo.setAfterSalesId(payApplyVo.getRelatedId());
            afterSalesVo.setCompanyId(ErpConst.NJ_COMPANY_ID);
            //本处适应第三方
            afterSalesVo.setTraderType(ErpConst.ONE);

            if (ObjectUtils.notEmpty(afterSalesVo.getAfterSalesId())) {

                AfterSales afterSale = afterSalesOrderService.getAfterSalesById(afterSalesVo.getAfterSalesId());

                if (null != afterSale && (AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSale.getType()) || AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSale.getType()))) {
                    afterSalesVo.setTraderType(ErpConst.THREE);
                }

            }

            AfterSalesVo afterSalesInfo = afterSalesService.getAfterSalesVoDetail(afterSalesVo);

            PayApply payApplyInfo = payApplyService.getPayApplyInfo(payApplyVo.getPayApplyId());

            payApplyInfo.setOrderNo(afterSalesInfo.getAfterSalesNo());
            payApplyInfo.setPayApplyId(payApplyVo.getPayApplyId());

            variableMap.put("payApply", payApplyInfo);
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("processDefinitionKey", "paymentVerify");
            variableMap.put("businessKey", "paymentVerify_" + payApplyInfo.getPayApplyId());
            variableMap.put("relateTableKey", payApplyInfo.getPayApplyId());
            variableMap.put("relateTable", "T_PAY_APPLY");
            variableMap.put("orgId", user.getOrgId());
            //售后付款申请
            variableMap.put("applyPayType", ErpConst.ONE);
            //售后付款为0
            variableMap.put("needManagerVerify", ErpConst.ZERO);
            // 流程条件标识
            variableMap.put("activitiType", "PaymentVerify");
            if (StringUtil.isNotBlank(paymentVerifyVersionId)) {
                //指定付款申请的审核流程版本--灰度测试使用
                actionProcdefService.createProcessInstanceByVersionId(request, paymentVerifyVersionId,
                        "paymentVerify_" + payApplyInfo.getPayApplyId(), variableMap);
            } else {
                actionProcdefService.createProcessInstance(request, "paymentVerify",
                        "paymentVerify_" + payApplyInfo.getPayApplyId(), variableMap);
            }
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "paymentVerify_" + payApplyInfo.getPayApplyId());

            if (historicInfo.get("endStatus") != "审核完成") {

                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();

                Authentication.setAuthenticatedUserId(user.getUsername());

                Map<String, Object> variables = new HashMap<String, Object>();
                // 设置审核完成监听器回写参数
                variables.put("tableName", "T_PAY_APPLY");
                variables.put("id", "PAY_APPLY_ID");
                variables.put("idValue", payApplyInfo.getPayApplyId());
                variables.put("key", "VALID_STATUS");
                variables.put("value", ErpConst.ONE);
                // 回写数据的表在db中
                variables.put("db", ErpConst.TWO);

                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        user.getUsername(), variables);

                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, ErpConst.ZERO);
                }

            }

        } catch (Exception e) {
            logger.error("saveRealRefundAmountApplyPay:", e);
            throw new ServiceException("任务完成操作失败：" + e.getMessage());

        }

    }

    /**
     * <b>Description:</b><br> 申请付款页面
     *
     * @param request
     * @param afterSales
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年7月24日 下午2:19:47
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/applyPayPage")
    public ModelAndView applyPayPage(HttpServletRequest request, AfterSalesVo afterSales) {

        ModelAndView mv = new ModelAndView();

        /*afterSales = afterSalesOrderService.getAfterSalesApplyPay(afterSales);*/
        afterSales = afterSalesCommonService.getAfterSalesApplyPay(afterSales);
        mv.addObject("afterSales", afterSales);

        //只允许一个工程师
        if (CollectionUtils.isNotEmpty(afterSales.getAfterSalesInstallstionVoList())) {

            List<AfterSalesInstallstionVo> list = new ArrayList<>();

            list.add(afterSales.getAfterSalesInstallstionVoList().get(0));
            afterSales.setAfterSalesInstallstionVoList(list);

        }

        //销售|第三方
        if (SysOptionConstant.ID_535.equals(afterSales.getTraderSubject()) || SysOptionConstant.ID_537.equals(afterSales.getTraderSubject())) {
            mv.setViewName("orderstream/aftersales/apply_pay_at");
        } else {

            // 获取银行帐号列表
            TraderFinance tf = new TraderFinance();
            tf.setTraderId(afterSales.getTraderId());
            tf.setTraderType(ErpConst.TWO);

            //获取财务交易者信息，T_TRADER_FINANCE
            List<TraderFinance> traderFinance = traderCustomerService.getTraderCustomerFinanceListNew(tf);
            //获取银行账号信息
            mv.addObject("traderFinance", traderFinance);

            // 获取对应供应商主信息
            TraderSupplier traderSupplier = new TraderSupplier();
            traderSupplier.setTraderId(afterSales.getTraderId());
            TraderSupplierVo supplierInfo = traderSupplierService.getSupplierInfoByTraderSupplierNew(traderSupplier);

            //账号余额（amount），ID（traderSupplierId）
            mv.addObject("supplierInfo", supplierInfo);

            mv.setViewName("orderstream/buyorder/apply_pay_buyorder");
        }

        return mv;

    }

    /**
     * <b>Description:</b><br> 新增保存申请付款
     *
     * @param request
     * @return
     * @Note <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年7月24日 下午2:19:47
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveApplyPay")
    @SystemControllerLog(operationType = "add", desc = "新增保存申请付款")
    public ModelAndView saveApplyPay(HttpServletRequest request, PayApplyVo payApplyVo) {

        User user = getSessionUser(request);

        ModelAndView mav = new ModelAndView();

        String traderFinance = request.getParameter("traderFinance");

        if (ObjectUtils.notEmpty(traderFinance)) {

            String[] str = traderFinance.split(",");

            if (str.length >= 1 && ObjectUtils.notEmpty(str[0])) {
                payApplyVo.setBank(str[0]);
            }

            if (str.length >= 2 && ObjectUtils.notEmpty(str[1])) {
                payApplyVo.setBankAccount(str[1]);
            }

            if (str.length >= 3 && ObjectUtils.notEmpty(str[2])) {
                payApplyVo.setBankCode(str[2]);
            }

            if (str.length >= 5 && ObjectUtils.notEmpty(str[4])) {
                payApplyVo.setTraderFinanceId(Integer.valueOf(str[4]));
            }

        }

        mav.addObject("refresh", "true_false_true");//是否关闭当前页，是否刷新当前页，是否刷新上层页面----三个参数为必传项

        if (payApplyVo.getTraderType() == null || "".equals(payApplyVo.getTraderType())) {//第三方申请付款后跳转页面
            mav.addObject("url", "/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + payApplyVo.getRelatedId());
        } else {//销售售后安调维修申请付款后跳转页面
            mav.addObject("url", "/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=" + payApplyVo.getRelatedId() + "&traderType=" + payApplyVo.getTraderType());
        }

        //ResultInfo<?> res = afterSalesOrderService.saveApplyPay(payApplyVo, user);
        ResultInfo<?> res = afterSalesCommonService.saveApplyPay(payApplyVo, user);

        AfterSalesVo afterSalesVo = new AfterSalesVo();
        afterSalesVo.setAfterSalesId(payApplyVo.getRelatedId());

        //AfterSalesVo afterSalesInfo= afterSalesOrderService.getAfterSalesVoListById(afterSalesVo);
        AfterSalesVo afterSalesInfo = afterSalesCommonService.getAfterSalesVoListById(afterSalesVo);

        try {

            //付款申请ID
            Integer payApplyId = (Integer) res.getData();

            // 调用自动制单公共校验接口，根据结果更新AUTO_BILL字段
            try {
                List<PayApplyCreateBillDto> payApplyCreateBillDtoList = payApplyAutoPayApi.findPayApply(payApplyId);
                if (CollUtil.isNotEmpty(payApplyCreateBillDtoList)) {
                    payApplyAutoPayApi.createBillRuleCheck(payApplyCreateBillDtoList.get(0));
                    payApplyApiService.updateAutoBill(payApplyId, ErpConst.ONE);
                }
            } catch (ServiceException e) {
                log.info("售后单调用自动制单公共校验接口不满足自动制单:{}", e.getMessage());
            } catch (Exception e) {
                log.info("售后单调用自动制单公共校验接口异常", e);
            }

            PayApply payApplyInfo = payApplyService.getPayApplyInfo(payApplyId);

            if (afterSalesInfo != null) {
                payApplyInfo.setOrderNo(afterSalesInfo.getAfterSalesNo());
            }

            Map<String, Object> variableMap = new HashMap<String, Object>();
            //开始生成流程(如果没有taskId表示新流程需要生成)
            variableMap.put("payApply", payApplyInfo);
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("processDefinitionKey", "paymentVerify");
            variableMap.put("businessKey", "paymentVerify_" + payApplyInfo.getPayApplyId());
            variableMap.put("relateTableKey", payApplyInfo.getPayApplyId());
            variableMap.put("relateTable", "T_PAY_APPLY");
            variableMap.put("orgId", user.getOrgId());
            //售后付款申请
            variableMap.put("applyPayType", ErpConst.ONE);
            //售后付款为0
            variableMap.put("needManagerVerify", ErpConst.ZERO);
            //流程条件标识
            variableMap.put("activitiType", "PaymentVerify");
            if (StringUtil.isNotBlank(paymentVerifyVersionId)) {
                //指定付款申请的审核流程版本--灰度测试使用
                actionProcdefService.createProcessInstanceByVersionId(request, paymentVerifyVersionId,
                        "paymentVerify_" + payApplyInfo.getPayApplyId(), variableMap);
            } else {
                actionProcdefService.createProcessInstance(request, "paymentVerify",
                        "paymentVerify_" + payApplyInfo.getPayApplyId(), variableMap);
            }
            //默认申请人通过
            //根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "paymentVerify_" + payApplyInfo.getPayApplyId());

            if (historicInfo.get("endStatus") != "审核完成") {

                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());

                Map<String, Object> variables = new HashMap<String, Object>();
                //设置审核完成监听器回写参数
                variables.put("tableName", "T_PAY_APPLY");
                variables.put("id", "PAY_APPLY_ID");
                variables.put("idValue", payApplyInfo.getPayApplyId());
                variables.put("key", "VALID_STATUS");
                variables.put("value", 1);
                //回写数据的表在db中
                variables.put("db", 2);

                //默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "", user.getUsername(), variables);

                //如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, ErpConst.ZERO);
                }

            }

        } catch (Exception e) {

            logger.error("saveApplyPay:", e);

            mav.addObject("message", "付款申请流程出错");

            return fail(mav);

        }
        if (ErpConst.ZERO.equals(res.getCode())) {

            //判断付款状态
            //付款节点二，
            afterSalesCommonService.savePayAmountAtStatuts(afterSalesInfo.getAfterSalesId());

            return success(mav);

        } else {

            return fail(mav);

        }

    }

    /**
     * <b>Description:</b><br> 财务售后收款
     *
     * @param request
     * @param afterDetailVo
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年11月3日 下午1:55:30
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addFinanceAfterCapital")
    public ModelAndView addFinanceAfterCapital(HttpServletRequest request, AfterSalesDetailVo afterDetailVo,
                                               @RequestParam(required = false, value = "billType") Integer billType,
                                               @RequestParam(required = false, value = "payApplyId") Integer payApplyId,
                                               @RequestParam(required = false, value = "taskId") String taskId,
                                               @RequestParam(required = false, value = "pageType") Integer pageType) {

        ModelAndView mv = new ModelAndView();

        //AfterSalesDetailVo afterSalesDetailVo = invoiceAfterService.getAfterCapitalBillInfo(afterDetailVo);
        AfterSalesDetailVo afterSalesDetailVo = afterSalesCommonService.getAfterCapitalBillInfo(afterDetailVo);

        //获取订单基本信息
        if (afterSalesDetailVo != null && afterSalesDetailVo.getSubjectType() != null) {

            if (SysOptionConstant.ID_535.equals(afterSalesDetailVo.getSubjectType().intValue())) {//销售

                switch (AfterSalesProcessEnum.getInstance(afterSalesDetailVo.getAfterType())) {
                    case AFTERSALES_TH:
                        if (ErpConst.ONE.equals(billType.intValue())) {

                            if (afterSalesDetailVo.getTraderId() != null) {

                                //收款
                                Saleorder so = new Saleorder();
                                so.setSaleorderId(afterSalesDetailVo.getOrderId());
                                Saleorder saleorder = saleorderService.getBaseSaleorderInfo(so);
                                //暂不使用,待确认
                                //Saleorder saleorder = baseSaleOrderService.getBaseSaleorderInfo(so);

                                mv.addObject("paymentComments", saleorder == null ? "" : saleorder.getPaymentComments());

                            }

                            mv.setViewName("/orderstream/aftersales/add_after_capital_sale_sk");

                        } else if (ErpConst.TWO.equals(billType.intValue())) {
                            //退款
                            mv.setViewName("/orderstream/aftersales/add_after_capital_sale_tk");
                        }
                        break;
                    case AFTERSALES_HH:
                        if (null != afterSalesDetailVo.getTraderId()) {
                            //账户余额
                            TraderCustomerVo customer = traderCustomerService.getTraderCustomerInfo(afterSalesDetailVo.getTraderId());
                            mv.addObject("amount", customer == null ? 0 : customer.getAmount());
                        }

                        mv.setViewName("/orderstream/aftersales/add_after_capital_sale_sk");//收款

                        break;
                    case AFTERSALES_AT:
                    case AFTERSALES_WX:
                    case AFTERSALES_ATY:
                    case AFTERSALES_ATN:
                        if (ErpConst.ONE.equals(billType.intValue())) {//收款
                            mv.setViewName("orderstream/aftersales/add_after_capital_sale_sk");//收款
                        } else if (ErpConst.TWO.equals(billType.intValue())) {//付款

                            //获取付款信息
                            PayApply payApply = payApplyService.getPayApplyInfo(afterDetailVo.getPayApplyId());

                            //获取card和mobile
                            payApply = payApplyService.getPayApplyMoreInfo(afterDetailVo.getAfterSalesId(), payApply);

                            mv.addObject("payApply", payApply);

                            //mv.setViewName("finance/after/add_after_capital_pay");//付款
                            mv.setViewName("orderstream/aftersales/add_after_capital_pay");//付款

                        }
                        break;
                    case AFTERSALES_TK:
                        mv.setViewName("orderstream/aftersales/add_after_capital_sale_tk");//退款
                        break;
                    case THIRD_AMOUNT_RETURN:
                        mv.setViewName("finance/after/add_after_capital_other_out");
                        break;
                    default:
                        break;
                }
            } else if (afterSalesDetailVo.getSubjectType().intValue() == SysOptionConstant.ID_537) {   //第三方
                switch (AfterSalesProcessEnum.getInstance(afterSalesDetailVo.getAfterType())) {
                    case AFTERASALES_THIRD_AT:
                    case AFTERASALES_THIRD_WX: {
                        if (billType != null && ErpConst.TWO.equals(billType.intValue())) {

                            //付款
                            //获取付款信息
                            PayApply payApply = payApplyService.getPayApplyInfo(afterDetailVo.getPayApplyId());

                            //获取card和mobile
                            payApply = payApplyService.getPayApplyMoreInfo(afterDetailVo.getAfterSalesId(), payApply);

                            mv.addObject("payApply", payApply);

                            //订单付款
                            /*mv.setViewName("finance/after/add_after_capital_pay");*/
                            mv.setViewName("orderstream/aftersales/add_after_capital_pay");//付款

                        } else {
                            //订单收款
                            /*mv.setViewName("finance/after/add_after_capital_other_in");*/
                            mv.setViewName("orderstream/aftersales/add_after_capital_other_in");
                        }
                        break;
                    }
                    /*case THIRD_AMOUNT_RETURN: {
                        mv.setViewName("finance/after/add_after_capital_other_out");//退款
                        break;
                    }*/
                }
            }

            // 获取付款交易方式
            List<SysOptionDefinition> payTypeName = getSysOptionDefinitionList(SysOptionConstant.ID_640);

            mv.addObject("payTypeName", payTypeName);
            mv.addObject("pageType", pageType);
            mv.addObject("afterSalesDetailVo", afterSalesDetailVo);
            //付款申请ID
            mv.addObject("payApplyId", payApplyId);

            mv.addObject("payPlayInfo", payApplyService.getPayApplyInfo(payApplyId));

            //流程节点ID
            mv.addObject("taskId", taskId);
            //交易方式
            List<SysOptionDefinition> traderModeList = getSysOptionDefinitionList(SysOptionConstant.ID_519);
            mv.addObject("traderModeList", traderModeList);

            //业务类型
            List<SysOptionDefinition> bussinessTypeList = getSysOptionDefinitionList(524);
            mv.addObject("bussinessTypeList", bussinessTypeList);

        }

        return mv;

    }

    /**
     * <b>Description:</b><br>
     * 保存新增售后的资金流水
     *
     * @param request
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年9月13日 下午5:48:07
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveAddAfterCapitalBill")
    @SystemControllerLog(operationType = "add", desc = "售后付款保存新增的资金流水")
    public ResultInfo<?> saveAddAfterCapitalBill(HttpServletRequest request, CapitalBill capitalBill, PayApply payApply,
                                                 String taskId, Integer paymentType) {
        log.info("售后付款保存新增的资金流水,capitalBill:{},payApply:{}",JSON.toJSONString(capitalBill),JSON.toJSONString(payApply));
        User user = getSessionUser(request);
        PayApply payApplyInfo = payApplyService.getPayApplyInfo(payApply.getPayApplyId());
        // 付款申请状态
        if (ObjectUtils.notEmpty(paymentType) && SysOptionConstant.ID_641.equals(paymentType)) {
            // 如果付款是641南京银行银企直连支付
            // 获取付款申请主表信息（根据ID）
            payApplyInfo.setComments(payApply.getComments());
            ResultInfo<?> res = bankBillService.addBankPayApply(payApplyInfo);

            if (SysOptionConstant.MINUS_ONE.equals(res.getCode())) {
                return res;
            }

        }
        payApply.setTraderName(payApplyInfo.getTraderName());
        payApply.setValidStatus(ErpConst.ONE);
        payApply.setValidTime(DateUtil.sysTimeMillis());
        payApply.setUpdater(user.getUserId());
        payApply.setModTime(DateUtil.sysTimeMillis());
        payApply.setComments(null);

        // 修改付款申请审核状态
        //ResultInfo<?> result = payApplyService.payApplyPass(payApply);
        ResultInfo<?> result = afterSalesCommonService.payApplyPass(payApply);

        if (user != null) {
            capitalBill.setCreator(user.getUserId());
            capitalBill.setAddTime(DateUtil.sysTimeMillis());
            capitalBill.setCompanyId(ErpConst.NJ_COMPANY_ID);
        }

        // 归属销售
        User belongUser = new User();

        // 安调维修工程师主键AFTER_SALES_INSTALLSTION_ID
        Integer afterSalesInstallstionId = ErpConst.ZERO;
        //售后付款,capitalBillDetail的traderId取供应商的traderId
        Integer payTraderId = null;
        if (capitalBill.getCapitalBillDetail().getTraderId() != null) {

            if (ErpConst.ONE.equals(capitalBill.getCapitalBillDetail().getOrderType())) {

                belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.ONE);// 1客户，2供应商

                if (belongUser != null && belongUser.getUserId() != null) {
                    belongUser = userService.getUserById(belongUser.getUserId());
                }

            } else if (ErpConst.TWO.equals(capitalBill.getCapitalBillDetail().getOrderType())) {

                belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.TWO);// 1客户，2供应商

                if (belongUser != null && belongUser.getUserId() != null) {
                    belongUser = userService.getUserById(belongUser.getUserId());
                }

            } else if (ErpConst.THREE.equals(capitalBill.getCapitalBillDetail().getOrderType())) {

                // 售后类型，需要从售后单查是关联采购还是销售
                AfterSales afterSales = new AfterSales();

                afterSales.setAfterSalesId(capitalBill.getCapitalBillDetail().getRelatedId());
                afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);

                AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoListById(afterSales);

                if (SysOptionConstant.ID_535.equals(afterSalesVo.getSubjectType())) {

                    // 销售
                    belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.ONE);// 1客户，2供应商

                    if (belongUser != null && belongUser.getUserId() != null) {
                        belongUser = userService.getUserById(belongUser.getUserId());
                    }

                    // 销售安调
                    if (AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(afterSalesVo.getType()) || AfterSalesProcessEnum.AFTERSALES_WX.getCode().equals(afterSalesVo.getType()) ||
                            AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(afterSalesVo.getType()) || AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(afterSalesVo.getType())) {

                        // 将安调对应的工程师查出
                        List<PayApplyDetail> payApplyDetailList =
                                payApplyService.getPayApplyDetailList(payApply.getPayApplyId());

                        if (payApplyDetailList != null && payApplyDetailList.size() != ErpConst.ZERO) {
                            afterSalesInstallstionId = payApplyDetailList.get(0).getDetailgoodsId();
                        }

                    }

                } else if (SysOptionConstant.ID_536.equals(afterSalesVo.getSubjectType())) {

                    // 采购
                    belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.TWO);// 1客户，2供应商

                    if (belongUser != null && belongUser.getUserId() != null) {
                        belongUser = userService.getUserById(belongUser.getUserId());
                    }

                } else if (SysOptionConstant.ID_537.equals(afterSalesVo.getSubjectType())) {

                    // 第三方

                    // 销售安调
                    if (AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSalesVo.getType()) || AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSalesVo.getType())) {
                        // 将安调对应的工程师查出
                        //List<PayApplyDetail> payApplyDetailList = payApplyService.getPayApplyDetailList(payApply.getPayApplyId());

                        List<PayApplyDetail> payApplyDetailList = afterSalesCommonService.getPayApplyDetailList(payApply.getPayApplyId());

                        if (payApplyDetailList != null && !ErpConst.ZERO.equals(payApplyDetailList.size())) {
                            afterSalesInstallstionId = payApplyDetailList.get(0).getDetailgoodsId();
                        }

                    }
                }

                //销售售后付款traderId为供应商id
                if ((afterSalesVo.getSubjectType() == 535 || afterSalesVo.getSubjectType() == 537) &&
                        (afterSalesVo.getType() == 584 || afterSalesVo.getType() == 4090 ||afterSalesVo.getType() == 4091 || afterSalesVo.getType() == 550|| afterSalesVo.getType() == 585) && null != payApply.getTraderName()){
                    Trader traderByName = traderMapper.findTraderByName(payApply.getTraderName());
                    if (null != traderByName){
                        payTraderId = traderByName.getTraderId();
                    }
                }
            }

        }
        try {

            capitalBill.setCurrencyUnitId(ErpConst.ONE);
            capitalBill.setTraderTime(DateUtil.sysTimeMillis());
            capitalBill.setTraderType(capitalBill.getTraderType() == null ? ErpConst.ONE : capitalBill.getTraderType());// 默認收入

            // 获取当前用户公司信息
            Company companyInfo = companyService.getCompanyByCompangId(ErpConst.NJ_COMPANY_ID);

            if (capitalBill.getPayee() == null) {
                capitalBill.setPayee(companyInfo.getCompanyName());
            } else {
                capitalBill.setPayer(companyInfo.getCompanyName());
            }

            List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();

            CapitalBillDetail capitalBillDetail2 = new CapitalBillDetail();
            capitalBillDetail2.setAmount(capitalBill.getAmount());
            capitalBillDetail2.setBussinessType(capitalBill.getCapitalBillDetail().getBussinessType());
            capitalBillDetail2.setOrderType(capitalBill.getCapitalBillDetail().getOrderType());
            capitalBillDetail2.setRelatedId(capitalBill.getCapitalBillDetail().getRelatedId());
            capitalBillDetail2.setOrderNo(capitalBill.getCapitalBillDetail().getOrderNo());
            capitalBillDetail2.setTraderId(null == payTraderId ? capitalBill.getCapitalBillDetail().getTraderId() : payTraderId);
            capitalBillDetail2.setTraderType(null == payTraderId ? capitalBill.getCapitalBillDetail().getTraderType() : 2);
            capitalBillDetail2.setAfterSalesInstallstionId(afterSalesInstallstionId);

            if (belongUser != null && belongUser.getUserId() != null) {
                capitalBillDetail2.setUserId(belongUser.getUserId());
            }

            if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                capitalBillDetail2.setOrgName(belongUser.getOrgName());
                capitalBillDetail2.setOrgId(belongUser.getOrgId());
            }

            capitalBillDetails.add(capitalBillDetail2);
            capitalBill.setCapitalBillDetails(capitalBillDetails);

            CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();

            capitalBillDetailInfo.setAmount(capitalBill.getAmount());
            capitalBillDetailInfo.setBussinessType(capitalBill.getCapitalBillDetail().getBussinessType());
            capitalBillDetailInfo.setOrderType(capitalBill.getCapitalBillDetail().getOrderType());
            capitalBillDetailInfo.setRelatedId(capitalBill.getCapitalBillDetail().getRelatedId());
            capitalBillDetailInfo.setOrderNo(capitalBill.getCapitalBillDetail().getOrderNo());
            capitalBillDetailInfo.setTraderId(null == payTraderId ? capitalBill.getCapitalBillDetail().getTraderId() : payTraderId);
            capitalBillDetailInfo.setTraderType(null == payTraderId ? capitalBill.getCapitalBillDetail().getTraderType() : 2);
            capitalBillDetailInfo.setAfterSalesInstallstionId(afterSalesInstallstionId);

            if (belongUser != null && belongUser.getUserId() != null) {
                capitalBillDetailInfo.setUserId(belongUser.getUserId());
            }

            if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
                capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
            }

            capitalBill.setCapitalBillDetail(capitalBillDetailInfo);
            log.info("capitalBillService.saveAddCapitalBill:{}",JSON.toJSONString(capitalBill));
            ResultInfo<?> resultInfo = capitalBillService.saveAddCapitalBill(capitalBill);

            try {
                Map<String,Object> result_map = (Map<String,Object>) resultInfo.getData();
                Integer capitalBillId = Integer.parseInt(result_map.get("capitalBillId").toString());
                log.info("售后付款申请手动点击通过,保存资金流水成功 capitalBillId:{},payApplyId:{}", capitalBillId, payApply.getPayApplyId());
                if (Objects.nonNull(payApply.getPayApplyId()) && Objects.nonNull(capitalBillId)) {
                    capitalBillApiService.saveRPayApplyJCapitalBill(payApply.getPayApplyId(), capitalBillId);
                }
            } catch (Exception e) {
                log.error("售后付款申请手动点击通过,保存付款申请与资金流水关系异常", e);
            }

            //暂不修改
            //ResultInfo<?> resultInfo = afterSalesCommonService.saveAddCapitalBill(capitalBill);

            // 添加完执行流程审核
            Map<String, Object> variables = new HashMap<String, Object>();

            variables.put("pass", true);

            // 审批操作
            try {

                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId,
                        request.getParameter("comments"), user.getUsername(), variables);

                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, ErpConst.ZERO);
                }

                //付款节点三，
                afterSalesCommonService.savePayAmountAtStatuts(capitalBill.getCapitalBillDetail().getRelatedId());
                log.info("resultInfo:{}",JSON.toJSONString(resultInfo));
                return resultInfo;

            } catch (Exception e) {
                logger.error("saveAddAfterCapitalBill 1:", e);
                return ResultInfo.error("任务完成操作失败，请刷新后重试。");
            }
        } catch (Exception e) {
            logger.error("saveAddAfterCapitalBill 2:", e);
            return ResultInfo.error("数据发生变动，请刷新后重试。");
        }

    }

    /**
     * <b>Description:</b><br>
     * 保存新增的资金流水
     *
     * @param request
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     * <b>Date:</b> 2017年9月13日 下午5:48:07
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveAddCapitalBill")
    @SystemControllerLog(operationType = "add", desc = "保存新增的资金流水")
    public ResultInfo<?> saveAddCapitalBill(HttpServletRequest request, CapitalBill capitalBill,
                                            @RequestParam(required = false, value = "afterType") Integer afterType) {
        User user = getSessionUser(request);
        log.info("保存新增的资金流水,capitalBill入参:{}", JSONUtil.toJsonStr(capitalBill));
        if (null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {

            /// add by Randy.Xu 2021/3/26 15:01 .Desc: . begin
            // VDERP-5839 数据越权修复   只可以财务人员操作
            Boolean checkFlag = authService.checkUserInRole(user, financeRoleNameStr);

            if (!checkFlag) {
                logger.info("销售越权操作:接口[orderstream/aftersales/saveAddCapitalBill],行为[非财务人员操作],操作人{}", user.getUsername());
            }

            // add by Randy.Xu 2021/3/26 15:01 .Desc: . end
        }

        if (user != null) {
            capitalBill.setCreator(user.getUserId());
            capitalBill.setAddTime(DateUtil.sysTimeMillis());
            capitalBill.setCompanyId(ErpConst.NJ_COMPANY_ID);
        }

        // 归属销售
        User belongUser = new User();

        if (capitalBill.getCapitalBillDetail().getTraderId() != null) {

            if (ErpConst.ONE.equals(capitalBill.getCapitalBillDetail().getOrderType())) {

                belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.ONE);// 1客户，2供应商

                if (belongUser != null && belongUser.getUserId() != null) {
                    belongUser = userService.getUserById(belongUser.getUserId());
                }

            } else if (ErpConst.TWO.equals(capitalBill.getCapitalBillDetail().getOrderType())) {

                belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.TWO);// 1客户，2供应商

                if (belongUser != null && belongUser.getUserId() != null) {
                    belongUser = userService.getUserById(belongUser.getUserId());
                }

            } else if (ErpConst.THREE.equals(capitalBill.getCapitalBillDetail().getOrderType())) {

                // 售后类型，需要从售后单查是关联采购还是销售
                AfterSales afterSales = new AfterSales();

                afterSales.setAfterSalesId(capitalBill.getCapitalBillDetail().getRelatedId());
                afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);

                AfterSalesVo afterSalesVo = afterSalesCommonService.getAfterSalesVoListById(afterSales);

                if (SysOptionConstant.ID_535.equals(afterSalesVo.getSubjectType())) {
                    // 销售
                    belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.ONE);// 1客户，2供应商

                    if (belongUser != null && belongUser.getUserId() != null) {
                        belongUser = userService.getUserById(belongUser.getUserId());
                    }

                } else if (SysOptionConstant.ID_536.equals(afterSalesVo.getSubjectType())) {

                    // 采购
                    belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.TWO);// 1客户，2供应商

                    if (belongUser != null && belongUser.getUserId() != null) {
                        belongUser = userService.getUserById(belongUser.getUserId());
                    }

                }

            }

        }

        try {

            // 销售售后新增收款记录-交易方式为银行
            if (ErpConst.ONE.equals(capitalBill.getTraderType()) &&
                    Objects.nonNull(capitalBill.getCapitalBillDetail()) &&
                    ErpConst.THREE.equals(capitalBill.getCapitalBillDetail().getOrderType()) &&
                    SysOptionConstant.ID_521.equals(capitalBill.getTraderMode()) &&
                    StringUtils.isNotBlank(capitalBill.getReceiveRecordBankNum())) {
                List<BankBillExtDo> bankBillExtDoList = bankBillService.findBankByTranFlow(capitalBill.getReceiveRecordBankNum());
                if (CollUtil.isEmpty(bankBillExtDoList)) {
                    return ResultInfo.error("根据银行流水号未找到对应的银行流水信息");
                } else if (bankBillExtDoList.size() > 1) {
                    return ResultInfo.error("银行流水号匹配多条流水，请确认银行流水号正确或联系研发处理");
                } else {
                    BankBillExtDo bankBillExtDo = bankBillExtDoList.get(0);
                    BigDecimal resultAmount = bankBillExtDo.getAmt().subtract(bankBillExtDo.getMatchedAmount()).subtract(capitalBill.getAmount());
                    if (resultAmount.compareTo(BigDecimal.ZERO) < 0) {
                        return ResultInfo.error("交易金额大于流水可结算金额");
                    }

                    capitalBill.setBankBillId(bankBillExtDo.getBankBillId());
                    capitalBill.setTranFlow(bankBillExtDo.getTranFlow());
                }
            }

            // 销售售后新增收款记录-交易方式为支付宝
            if (ErpConst.ONE.equals(capitalBill.getTraderType()) &&
                    Objects.nonNull(capitalBill.getCapitalBillDetail()) &&
                    ErpConst.THREE.equals(capitalBill.getCapitalBillDetail().getOrderType()) &&
                    SysOptionConstant.ID_520.equals(capitalBill.getTraderMode()) &&
                    StringUtils.isNotBlank(capitalBill.getReceiveRecordAlipayNum())) {
                List<BankBillExtDo> bankBillExtDoList = bankBillService.findAlipayByCapitalSearchFlow(capitalBill.getReceiveRecordAlipayNum());
                if (CollUtil.isEmpty(bankBillExtDoList)) {
                    log.info("销售售后新增收款记录-交易方式为支付宝,根据资金匹配流水号未查询到支付宝流水信息,暂时将支付宝订单号保存到CAPITAL_SEARCH_FLOW,{}", capitalBill.getReceiveRecordAlipayNum());
                    capitalBill.setCapitalSearchFlow(capitalBill.getReceiveRecordAlipayNum());
                } else if (bankBillExtDoList.size() > 1) {
                    return ResultInfo.error("支付宝订单号匹配多条流水，请确认支付宝订单号正确或联系研发处理");
                } else {
                    BankBillExtDo bankBillExtDo = bankBillExtDoList.get(0);
                    BigDecimal resultAmount = bankBillExtDo.getAmt().subtract(bankBillExtDo.getMatchedAmount()).subtract(capitalBill.getAmount());
                    if (resultAmount.compareTo(BigDecimal.ZERO) < 0) {
                        return ResultInfo.error("交易金额大于流水可结算金额");
                    }

                    capitalBill.setBankBillId(bankBillExtDo.getBankBillId());
                    capitalBill.setTranFlow(bankBillExtDo.getTranFlow());
                    capitalBill.setCapitalSearchFlow(capitalBill.getReceiveRecordAlipayNum());
                }
            }

            capitalBill.setCurrencyUnitId(ErpConst.ONE);
            capitalBill.setTraderTime(DateUtil.sysTimeMillis());
            capitalBill.setTraderType(capitalBill.getTraderType() == null ? ErpConst.ONE : capitalBill.getTraderType());// 默認收入

            // 获取当前用户公司信息
            Company companyInfo = companyService.getCompanyByCompangId(ErpConst.NJ_COMPANY_ID);

            capitalBill.setPayee(companyInfo.getCompanyName());

            List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();

            CapitalBillDetail capitalBillDetail2 = new CapitalBillDetail();
            capitalBillDetail2.setAmount(capitalBill.getAmount());
            capitalBillDetail2.setBussinessType(capitalBill.getCapitalBillDetail().getBussinessType());
            capitalBillDetail2.setOrderType(capitalBill.getCapitalBillDetail().getOrderType());
            capitalBillDetail2.setRelatedId(capitalBill.getCapitalBillDetail().getRelatedId());
            capitalBillDetail2.setOrderNo(capitalBill.getCapitalBillDetail().getOrderNo());
            capitalBillDetail2.setTraderId(capitalBill.getCapitalBillDetail().getTraderId());
            capitalBillDetail2.setTraderType(capitalBill.getCapitalBillDetail().getTraderType());
            capitalBillDetail2.setUserId(capitalBill.getCapitalBillDetail().getUserId());

            if (belongUser != null) {
                capitalBillDetail2.setOrgName(belongUser.getOrgName());
                capitalBillDetail2.setOrgId(belongUser.getOrgId());
            }

            capitalBillDetails.add(capitalBillDetail2);
            capitalBill.setCapitalBillDetails(capitalBillDetails);

            CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();

            capitalBillDetailInfo.setAmount(capitalBill.getAmount());
            capitalBillDetailInfo.setBussinessType(capitalBill.getCapitalBillDetail().getBussinessType());
            capitalBillDetailInfo.setOrderType(capitalBill.getCapitalBillDetail().getOrderType());
            capitalBillDetailInfo.setRelatedId(capitalBill.getCapitalBillDetail().getRelatedId());
            capitalBillDetailInfo.setOrderNo(capitalBill.getCapitalBillDetail().getOrderNo());
            capitalBillDetailInfo.setTraderId(capitalBill.getCapitalBillDetail().getTraderId());
            capitalBillDetailInfo.setTraderType(capitalBill.getCapitalBillDetail().getTraderType());
            capitalBillDetailInfo.setUserId(capitalBill.getCapitalBillDetail().getUserId());

            if (belongUser != null) {
                capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
                capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
            }

            capitalBill.setCapitalBillDetail(capitalBillDetailInfo);

            //暂不修改
            //ResultInfo<?> resultInfo = afterSalesCommonService.saveAddCapitalBill(capitalBill);
            ResultInfo<?> resultInfo = capitalBillService.saveAddCapitalBill(capitalBill);

            if (ErpConst.ONE.equals(ErpConst.NJ_COMPANY_ID) && ErpConst.ZERO.equals(resultInfo.getCode())
                    && ErpConst.ONE.equals(capitalBillDetailInfo.getOrderType())) {

                vedengSoapService.orderSyncWeb(capitalBill.getCapitalBillDetail().getRelatedId());

                Saleorder saleorder = new Saleorder();

                saleorder.setSaleorderId(capitalBill.getCapitalBillDetail().getRelatedId());

                //调用库存服务
                int i = warehouseStockService.updateOccupyStockService(saleorder, ErpConst.ZERO);

                logicalSaleorderChooseService.chooseLogicalSaleorder(saleorder, user);

            }

            //收款节点三，修改收款状态,此处判断收款状态
            afterSalesCommonService.saveCollectionAmountAtStatus(capitalBill.getCapitalBillDetail().getRelatedId());

            return resultInfo;

        }
        catch (Exception e) {
            logger.error("saveAddCapitalBill:", e);
        }

        return new ResultInfo<>();

    }

    /**
     * <b>Description:</b><br>
     * 财务退款业务操作
     *
     * @param request
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> duke <br>
     * <b>Date:</b> 2017年11月2日 下午3:08:13
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveRefundCapitalBill")
    @SystemControllerLog(operationType = "add", desc = "财务退款业务")
    public ResultInfo<?> saveRefundCapitalBill(HttpServletRequest request, CapitalBill capitalBill, PayApply payApply,
                                               String taskId, Integer paymentType) {

        User user = getSessionUser(request);

        ResultInfo<?> resultInfo = new ResultInfo<>();

        // add by fralin.wu for [耗材售后退款] at 2018-12-21 begin
        // 售后线上售后退款标志 默认 不是
        boolean afterSalesIsNotOnlineFlag = true;
        // add by fralin.wu for [耗材售后退款] at 2018-12-21 end

        // 归属人员
        User belongUser = new User();

        if (capitalBill.getCapitalBillDetail().getTraderId() != null) {

            if (ErpConst.ONE.equals(capitalBill.getCapitalBillDetail().getOrderType())) {

                // 销售
                belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.ONE);// 1客户，2供应商

                if (belongUser != null && belongUser.getUserId() != null) {
                    belongUser = userService.getUserById(belongUser.getUserId());
                }

            } else if (ErpConst.THREE.equals(capitalBill.getCapitalBillDetail().getOrderType())) {

                // 售后类型，需要从售后单查是关联采购还是销售
                AfterSales afterSales = new AfterSales();
                afterSales.setAfterSalesId(capitalBill.getCapitalBillDetail().getRelatedId());
                afterSales.setCompanyId(ErpConst.NJ_COMPANY_ID);

                //AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoListById(afterSales);
                AfterSalesVo afterSalesVo = afterSalesCommonService.getAfterSalesVoListById(afterSales);

                if (SysOptionConstant.ID_535.equals(afterSalesVo.getSubjectType())) {

                    // 销售
                    belongUser = userService.getUserByTraderId(capitalBill.getCapitalBillDetail().getTraderId(), ErpConst.ONE);// 1客户，2供应商

                    if (belongUser != null && belongUser.getUserId() != null) {
                        belongUser = userService.getUserById(belongUser.getUserId());
                    }

                    // add by franlin.wu for [耗材售后线上退款] at 2018-12-21 begin
                    if (ErpConst.ONE.equals(afterSalesVo.getSource()) && SysOptionConstant.ID_539.equals(afterSalesVo.getType())) {
                        // 请求头
                        Map<String, String> headers = new HashMap<String, String>();
                        headers.put("version", "v1");
                        // 响应公司ID
                        afterSalesVo.setCompanyId(ErpConst.NJ_COMPANY_ID);

                        // 返回响应结果
                        TypeReference<ResultInfo<?>> resultType = new TypeReference<ResultInfo<?>>() {
                        };

                        // 售后线上退款接口
                        resultInfo = HttpRestClientUtil.post(restDbUrl + DbRestInterfaceConstant.DB_REST_FINANCE_OPERATE_AFTER_SALE_ONLINE_REFUND, resultType, headers, afterSalesVo);

                        logger.info("售后线上退款：" + afterSalesVo.getAfterSalesNo() + "\t" + capitalBill.getCapitalBillDetail().getRelatedId() + resultInfo);

                        // 成功  或 0为该场景业务异常场景
                        if (null != resultInfo && (ErpConst.ZERO.equals(resultInfo.getCode()))) {
                            afterSalesIsNotOnlineFlag = false;
                        } else if (null != resultInfo && ErpConst.ONE.equals(resultInfo.getCode())) {
                            return ResultInfo.error(resultInfo.getMessage());
                        }

                    }
                    // add by franlin.wu for [售后线上退款] at 2018-12-21 end
                }
            }

        }

        if (null != paymentType && SysOptionConstant.ID_641.equals(paymentType)) {

            // 如果付款是641南京银行银企直连支付
            // 获取付款申请主表信息（根据ID）
            PayApply payApplyInfo = payApplyService.getPayApplyInfo(payApply.getPayApplyId());

            payApplyInfo.setComments(payApply.getComments());

            ResultInfo<?> res = bankBillService.addBankPayApply(payApplyInfo);

            if (SysOptionConstant.MINUS_ONE.equals(res.getCode())) {
                return res;
            }

        }

        payApply.setValidStatus(ErpConst.ONE);
        payApply.setValidTime(DateUtil.sysTimeMillis());
        payApply.setUpdater(user.getUserId());
        payApply.setModTime(DateUtil.sysTimeMillis());
        payApply.setComments(null);

        // 修改付款申请审核状态
        /*ResultInfo<?> result = payApplyService.payApplyPass(payApply);*/
        ResultInfo<?> result = afterSalesCommonService.payApplyPass(payApply);

        // add by franlin.wu for [售后线上退款] at 2018-12-21 begin
        if (afterSalesIsNotOnlineFlag) {

            // add by franlin.wu for [售后线上退款] at 2018-12-21 end
            // 获取当前用户公司信息
            Company companyInfo = companyService.getCompanyByCompangId(ErpConst.NJ_COMPANY_ID);

            if (user != null) {
                capitalBill.setCreator(user.getUserId());
                capitalBill.setAddTime(DateUtil.sysTimeMillis());
                capitalBill.setCompanyId(ErpConst.NJ_COMPANY_ID);
            }

            capitalBill.setCurrencyUnitId(ErpConst.ONE);// 货币单位ID
            capitalBill.setTraderTime(DateUtil.sysTimeMillis());
            capitalBill.setTraderType(capitalBill.getTraderType() == null ? ErpConst.TWO : capitalBill.getTraderType());// 默認支出

            capitalBill.setPayer(companyInfo.getCompanyName());// 付款方

            if (belongUser != null) {
                capitalBill.getCapitalBillDetail().setOrgName(belongUser.getOrgName());
                capitalBill.getCapitalBillDetail().setOrgId(belongUser.getOrgId());
            }

            resultInfo = capitalBillService.saveRefundCapitalBill(capitalBill);

            //resultInfo = afterSalesCommonService.saveRefundCapitalBillNew(capitalBill);
        }


        //售后财务审核添加流水刷新售后单退款状态信息
        logger.info("售后财务审核添加流水刷新售后单退款状态信息 payApplyId:{}", payApply.getPayApplyId());
        afterSalesOrderService.refreshAmountRefundStatus(
                payApplyService.getPayApplyInfo(payApply.getPayApplyId()).getRelatedId());

        Map<String, Object> variables = new HashMap<String, Object>();

        variables.put("pass", true);

        // 审批操作
        try {
            Integer status = ErpConst.ZERO;
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId,
                    request.getParameter("comments"), user.getUsername(), variables);

            // 如果未结束添加审核对应主表的审核状态
            if (!complementStatus.getData().equals("endEvent")) {
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }

            return resultInfo;
        } catch (Exception e) {
            logger.error("saveRefundCapitalBill:", e);
            return ResultInfo.error("任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * <b>Description:</b><br> 开票申请
     *
     * @param request
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年8月28日 上午11:20:24
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/openInvoiceApply")
    public ModelAndView openInvoiceApply(HttpServletRequest request, InvoiceApply invoiceApply) {

        ModelAndView mv = new ModelAndView();

        mv.addObject("invoiceApply", invoiceApply);
        mv.setViewName("orderstream/aftersales/aftersale_invoice_apply");

        return mv;

    }

    /**
     * <b>Description:</b><br> 保存开票申请
     *
     * @param request
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年8月28日 上午11:20:24
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveOpenInvoiceApply")
    @SystemControllerLog(operationType = "add", desc = "保存开票申请")
    public ResultInfo<?> saveOpenInvoiceApply(HttpServletRequest request, InvoiceApply invoiceApply) {

        User user = getSessionUser(request);

        if (user != null) {
            invoiceApply.setCreator(user.getUserId());
            invoiceApply.setAddTime(DateUtil.gainNowDate());
            invoiceApply.setCompanyId(ErpConst.NJ_COMPANY_ID);
            invoiceApply.setUpdater(user.getUserId());
            invoiceApply.setModTime(DateUtil.gainNowDate());
            invoiceApply.setYyValidStatus(ErpConst.ONE);
        }

        //ResultInfo<?> res = afterSalesOrderService.saveOpenInvoceApply(invoiceApply);

        try {

            int res = afterSalesCommonService.saveOpenInvoceApply(invoiceApply);

            if (res > 0) {

                //开票节点二，更新开票状态
                afterSalesCommonService.saveMakeOutInvoiceCompleteData(invoiceApply.getRelatedId());

                return ResultInfo.success();

            } else if (res == -2) {

                return ResultInfo.error("客户开票资料不全，无法开具增值税普通发票，请完善信息！");

            } else if (res == -3) {

                return ResultInfo.error("客户开票资料不全，无法开具增值税专用发票，请完善信息！！");

            }

            return ResultInfo.error();

        } catch (Exception e) {
            logger.error("saveOpenInvoiceApply error:{}", e);
            return new ResultInfo<>();
        }

    }


    public void regionInfo(ModelAndView mav, Integer areaId) {

        Region region = regionService.getRegionByRegionId(areaId);

        if (ErpConst.ONE.equals(region.getRegionType())) {// 省级
            mav.addObject("province", areaId);
        } else if (ErpConst.TWO.equals(region.getRegionType())) {

            List<Region> cityList = regionService.getRegionByParentId(region.getParentId());

            mav.addObject("cityList", cityList);
            mav.addObject("city", areaId);
            mav.addObject("province", region.getParentId());
        } else if (ErpConst.THREE.equals(region.getRegionType())) {

            List<Region> zoneList = regionService.getRegionByParentId(region.getParentId());

            mav.addObject("zoneList", zoneList);
            mav.addObject("zone", areaId);

            Region cyregion = regionService.getRegionByRegionId(zoneList.get(ErpConst.ZERO).getParentId());

            List<Region> cityList = regionService.getRegionByParentId(cyregion.getParentId());

            mav.addObject("cityList", cityList);
            mav.addObject("city", region.getParentId());
            mav.addObject("province", cityList.get(ErpConst.ZERO).getParentId());
        }

        List<Region> provinceList = regionService.getRegionByParentId(ErpConst.ONE);

        mav.addObject("provinceList", provinceList);

    }

    /**
     * 初始化寄送发票页面
     *
     * @param afterSalesVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/initExpressSave")
    public ModelAndView initExpressSave(AfterSalesVo afterSalesVo, String invoiceIdArr) {

        ModelAndView modelAndView = new ModelAndView("orderstream/aftersales/add_express_info");

        modelAndView.addObject("afterSalesId", afterSalesVo.getAfterSalesId());

        List<Integer> invoiceIdList = JSON.parseArray(invoiceIdArr, Integer.class);

        modelAndView.addObject("invoiceIdArr", JSON.toJSONString(invoiceIdList));
        modelAndView.addObject("logisticsList", getLogisticsList(ErpConst.ONE));

        return modelAndView;

    }


    /**
     * 初始化售后单处理信息页面
     *
     * @param afterSalesId
     * @param afterSalesInvoiceId
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/initInvoiceHandleInfo")
    public ModelAndView initInvoiceHandleInfo(@RequestParam(required = false, value = "afterSalesId") Integer afterSalesId,
                                              @RequestParam(required = false, value = "afterSalesInvoiceId") Integer afterSalesInvoiceId) {

        ModelAndView modelAndView = new ModelAndView("orderstream/aftersales/add_handle_info");

        modelAndView.addObject("afterSalesId", afterSalesId);
        modelAndView.addObject("afterSalesInvoiceId", afterSalesInvoiceId);

        return modelAndView;

    }


    /**
     * 售后发票处理信息保存
     *
     * @param afterSalesId
     * @param afterSalesInvoiceId
     * @param handleStatus
     * @param handleComments
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/invoiceHandleInfoSave")
    public ResultInfo invoiceHandleInfoSave(@RequestParam(required = false, value = "afterSalesId") Integer afterSalesId,
                                            @RequestParam(required = false, value = "afterSalesInvoiceId") Integer afterSalesInvoiceId,
                                            @RequestParam(required = false, value = "handleStatus") Integer handleStatus,
                                            @RequestParam(required = false, value = "handleComments") String handleComments) {

        if (afterSalesId == null || handleStatus == null) {
            return ResultInfo.error("订单处理信息保存缺少相应参数");
        }

        return afterSalesOrderService.saveInvoiceHandleInfo(afterSalesId, afterSalesInvoiceId, handleStatus, handleComments);

    }


    /**
     * <b>Description:</b><br> 财务-售后-安调-新增发票
     *
     * @param request
     * @param afterSalesGoodsVo
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月30日 下午6:22:39
     */
    @ResponseBody
    @RequestMapping(value = "/addAfterInvoiceAt")
    public ModelAndView addAfterInvoiceAt(HttpServletRequest request, AfterSalesGoodsVo afterSalesGoodsVo) {

        ModelAndView mv = new ModelAndView();

        //获取售后安调开具发票信息
        //afterSalesGoodsVo = invoiceAfterService.getAfterOpenInvoiceInfoAt(afterSalesGoodsVo);
        afterSalesGoodsVo = afterSalesCommonService.getAfterOpenInvoiceInfoAt(afterSalesGoodsVo);

        if (afterSalesGoodsVo != null) {

            mv.addObject("afterSalesGoodsVo", afterSalesGoodsVo);

            //发票类型集合
            List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);

            mv.addObject("invoiceTypeList", invoiceTypeList);

        }

        mv.setViewName("orderstream/aftersales/add_after_invoice_at");

        return mv;

    }

    /**
     * <b>Description:</b><br> 保存-财务-售后-安调-开票信息（服务费）
     *
     * @param request
     * @param invoice
     * @param invoiceDetail
     * @return
     * @Note <b>Author:</b> duke
     * <br><b>Date:</b> 2017年10月31日 下午2:03:45
     */
    @ResponseBody
    @RequestMapping(value = "/saveAfterOpenInvoiceAt")
    @SystemControllerLog(operationType = "add", desc = "保存财务-售后-安调-开票信息")
    public ResultInfo saveAfterOpenInvoiceAt(HttpServletRequest request, Invoice invoice, InvoiceDetail invoiceDetail) {
        String openDate = "";
        if (ErpConst.THREE.equals(invoice.getInvoiceProperty())) {
            openDate = fullyDigitalInvoiceApiService.getBlueInvoiceOpenDate(invoice.getInvoiceNo());
            if (StrUtil.isEmpty(openDate)) {
                logger.error("saveAfterOpenInvoiceAt:数电发票：{}，未查询到开票日期", invoice.getInvoiceNo());
                return new ResultInfo(-1, "数电发票未查询到信息，无法保存");
            }
        }

        //校验发票是否在航信采购发票得流转状态
        ResultInfo<?> hxInvoiceInfo = invoiceService.getHxInvoiceInfo(invoice);

        if (hxInvoiceInfo != null) {
            return hxInvoiceInfo;
        }

        try {

            User user = getSessionUser(request);

            if (user != null) {
                invoice.setCompanyId(ErpConst.NJ_COMPANY_ID);
                invoice.setCreator(user.getUserId());
                invoice.setAddTime(DateUtil.gainNowDate());
                invoice.setUpdater(user.getUserId());
                invoice.setModTime(DateUtil.gainNowDate());
            }

            //一个售后订单只允许有一个安调服务
            invoice.setDetailGoodsIdList(Arrays.asList(invoiceDetail.getDetailgoodsId()));
            invoice.setInvoiceNumList(Arrays.asList(invoiceDetail.getNum()));
            invoice.setInvoicePriceList(Arrays.asList(invoiceDetail.getPrice()));
            invoice.setInvoiceTotleAmountList(Arrays.asList(invoiceDetail.getTotalAmount()));

            invoice.setType(SysOptionConstant.ID_504);//售后
            invoice.setValidStatus(ErpConst.ONE);//售后开票，默认通过
            invoice.setTag(ErpConst.ONE);//开票

            //ResultInfo<?> result = invoiceAfterService.saveAfterOpenInvoiceAt(invoice);
            ResultInfo<?> result = afterSalesCommonService.saveAfterOpenInvoiceAt(invoice);

            //更新收货单的开票状态
            afterSalesCommonService.saveMakeOutInvoiceCompleteData(invoice.getRelatedId());

            if (ErpConst.THREE.equals(invoice.getInvoiceProperty())) {
                DownloadInvoice data = new DownloadInvoice();
                data.setInvoiceNo(invoice.getInvoiceNo());
                data.setInvoiceDate(openDate);
                data.setSkipCheckExtend(Boolean.TRUE);
                fullyDigitalInvoiceApiService.downloadInvoice(data);
            }
            return result;

        } catch (Exception e) {
            logger.error("saveAfterOpenInvoiceAt:", e);
            return new ResultInfo<>();
        }

    }

    /**
     * <b>Description:</b><br>
     * 打开新增直发出入库记录页面
     *
     * @param afterSalesGoodsVo
     * @return org.springframework.web.servlet.ModelAndView
     * @Note <b>Author:</b> Thor <br>
     * <b>Date:</b> 2021/10/22 13:49
     */
    @FormToken(save = true)
    @RequestMapping("/addDirectStockInfo")
    public ModelAndView addDirectStockInfo(AfterSalesGoodsVo afterSalesGoodsVo, Integer type) {

        ModelAndView mv = new ModelAndView();

        mv.addObject("afterSalesGoodsVo", afterSalesGoodsVo);
        mv.addObject("type", type);
        mv.setViewName("orderstream/aftersales/add_afterSales_direct");

        return mv;

    }

    @FormToken(remove = true)
    @RequestMapping("/saveDirectStockInfo")
    @ResponseBody
    public ResultInfo saveDirectStockInfo(AfterSalesDirectInfo afterSalesDirectInfo, HttpServletRequest request) {

        User user = getSessionUser(request);

        int i = afterSalesCommonService.saveDirectStockInfo(afterSalesDirectInfo, user);

        if (i == 0) {
            return ResultInfo.error("保存失败");
        }

        return ResultInfo.success("保存成功");

    }

    /**
     * <b>Description:</b><br> 跳转到发送派单通知页面
     *
     * @param request
     * @param afterSalesInstallstionVo
     * @return
     * @throws UnsupportedEncodingException
     * @Note <b>Author:</b> Jerry
     * <br><b>Date:</b> 2018年1月29日 上午11:43:58
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/toSendInstallstionSmsPage")
    public ModelAndView toSendInstallstionSmsPage(HttpServletRequest request, AfterSalesInstallstionVo afterSalesInstallstionVo) throws UnsupportedEncodingException {

        ModelAndView mav = new ModelAndView("/aftersales/order/add_afterSales_sms");

        afterSalesInstallstionVo.setName(URLDecoder.decode(afterSalesInstallstionVo.getName(), "UTF-8"));
        afterSalesInstallstionVo.setTypeName(URLDecoder.decode(afterSalesInstallstionVo.getTypeName(), "UTF-8"));

        mav.addObject("asiv", afterSalesInstallstionVo);

        return mav;

    }

    /**
     * <b>Description:</b><br> 编辑产品与工程师信息页面
     *
     * @param request
     * @param afterSalesInstallstionVo
     * @return
     * @throws IOException
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月24日 下午2:55:14
     */
    @ResponseBody
    @RequestMapping(value = "/editEngineerPage")
    public ModelAndView editEngineerPage(HttpServletRequest request, AfterSalesInstallstionVo afterSalesInstallstionVo) throws IOException {

        ModelAndView mav = new ModelAndView();

        Integer isAtwx = afterSalesInstallstionVo.getIsAtwx();
        if (afterSalesInstallstionVo.getSubjectType() != null && afterSalesInstallstionVo.getSubjectType() != 537) {
            mav.setViewName("order/saleorder/edit_afterSales_engineer");
        } else {
            mav.setViewName("aftersales/order/edit_afterSales_engineer");

        }

        Integer areaId = afterSalesInstallstionVo.getAreaId();

        //afterSalesInstallstionVo = afterSalesOrderService.getAfterSalesInstallstionVo(afterSalesInstallstionVo);
        afterSalesInstallstionVo = afterSalesCommonService.getAfterSalesInstallstionVo(afterSalesInstallstionVo);

        Calendar c = Calendar.getInstance();

        mav.addObject("end", c.getTimeInMillis());

        c.add(Calendar.DAY_OF_MONTH, 1);

        mav.addObject("start", c.getTimeInMillis());

        afterSalesInstallstionVo.setAreaId(areaId);
        // 2633行会清空这个值 因此需要先取出来
        afterSalesInstallstionVo.setIsAtwx(isAtwx);

        mav.addObject("afterSalesInstallstionVo", afterSalesInstallstionVo);
        mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(afterSalesInstallstionVo)));

        return mav;

    }

    /**
     * <b>Description:</b><br>
     * 编辑评分
     *
     * @param request
     * @param afterSalesInstallstion
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年9月25日 上午10:00:28
     */
    @ResponseBody
    @RequestMapping(value = "/editscore")
    public ModelAndView editScore(HttpServletRequest request, AfterSalesInstallstion afterSalesInstallstion) {

        if (null == afterSalesInstallstion || afterSalesInstallstion.getAfterSalesInstallstionId() <= 0) {
            return pageNotFound(request);
        }

        ModelAndView mv = new ModelAndView("orderstream/aftersales/edit_score");

        //AfterSalesInstallstion salesInstallstion = afterSalesInstallstionService.getAfterSalesInstallstion(afterSalesInstallstion);
        AfterSalesInstallstion salesInstallstion = afterSalesCommonService.getAfterSalesInstallstion(afterSalesInstallstion);

        mv.addObject("afterSalesInstallstion", salesInstallstion);

        try {
            mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(salesInstallstion)));
        } catch (Exception e) {
            logger.error("editscore:", e);
        }

        return mv;

    }

    /**
     * <b>Description:</b><br>
     * 保存编辑评分
     *
     * @param request
     * @param afterSalesInstallstion
     * @return
     * @Note <b>Author:</b> Jerry <br>
     * <b>Date:</b> 2017年9月25日 上午10:00:54
     */
    @ResponseBody
    @RequestMapping(value = "/saveeditscore")
    @SystemControllerLog(operationType = "edit", desc = "保存工程师评分")
    public ResultInfo saveEditScore(HttpServletRequest request, AfterSalesInstallstion afterSalesInstallstion) {

        if (null == afterSalesInstallstion || afterSalesInstallstion.getAfterSalesInstallstionId() <= 0) {
            return new ResultInfo();
        }

        User user = getSessionUser(request);

        Long time = DateUtil.sysTimeMillis();

        afterSalesInstallstion.setScoreUserId(user.getUserId());
        afterSalesInstallstion.setScoreTime(time);

        return afterSalesCommonService.saveInstallstionScore(afterSalesInstallstion);

    }

    @FormToken(save = true)
    @RequestMapping("/initAddServiceRecord")
    public ModelAndView initAddServiceRecord(Integer aftersalesId) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("/orderstream/aftersales/add_afterSales_at_service");
        //获取验收结论的字典记录
        List<SysOptionDefinition> checkConclusionList = sysOptionDefinitionService.getSysOptionDefinitionListByParentId(SysOptionConstant.ID_4084);
        mv.addObject("checkConclusionList", checkConclusionList);
        mv.addObject("aftersalesId", aftersalesId);
        List<AfterSalesInstallServiceRecordDetailDto> detailDtoList = afterSalesCommonService.getAfterSalesInstallGoodsInfoAdd(aftersalesId);
        mv.addObject("detailDtoList", detailDtoList);
        return mv;
    }

    /**
     * 打开售后安调上传页面
     */
    @RequestMapping("/preAddServiceRecordAttachment")
    public ModelAndView preAddServiceRecordAttachment() {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("orderstream/aftersales/afterSales_at_service_upload");
        return mv;
    }

    /**
     * 保存服务记录
     */
    @RequestMapping("/saveAddServiceRecord")
    @ResponseBody
    public ResultInfo<?> saveAddServiceRecord(@RequestBody AfterSalesInstallServiceRecordDto dto) {
        if (CollUtil.isEmpty(dto.getDetail())) {
            return ResultInfo.error("保存失败");
        }

        for (AfterSalesInstallServiceRecordDetailDto d : dto.getDetail()) {
            // 手填sn码校验
            if (StrUtil.isNotEmpty(d.getSupplCode())) {
                boolean exists = warehouseGoodsOutService.isExistsSnInWarehouseGoodsOut(d.getSupplCode(), CollUtil.newArrayList(
                        WarehouseGoodsOutEnum.SALE_WAREHOUSE_OUT.getErpCode(),
                        WarehouseGoodsOutEnum.SALE_EXCHANGE_WAREHOUSE_OUT.getErpCode()));

                if (exists) {
                    return ResultInfo.error("该SN码已存在于系统中");
                }
                boolean flag = afterSalesInstallServiceRecordDetailService.querySupplCodeIsExtend(d.getSupplCode());
                if (flag) {
                    return ResultInfo.error("该SN码已存在于系统中");
                }
            }
        }

        try {
            afterSalesCommonService.saveServiceRecord(dto);
        } catch (Exception e) {
            logger.error("保存售后安调服务记录失败", e);
            return ResultInfo.error("保存失败");
        }
        return ResultInfo.success();
    }


    /**
     * 接受医修帮来的安调记录
     * @param dto
     * @return
     */
    @RequestMapping("/saveYxbRecord")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> saveYxbRecord(@RequestBody YxbSnDto dto) {

        log.info("saveYxbRecord 入参：{}", JSON.toJSONString(dto));

        if (StrUtil.isEmpty(dto.getErpOrderNo())||CollUtil.isEmpty(dto.getDetail())) {
            return R.error("参数异常");
        }

        AfterSales afterSales = afterSalesCommonService.queryAfterSalesByNo(dto.getErpOrderNo());
        if (Objects.isNull(afterSales)) {
            log.info("yxb 传递erp售后单号异常：{}",JSON.toJSONString(dto));

            return R.error(BaseResponseCode.YXB_SN_ORDER_ERROR);
        }

        // 订单类型过滤
        if (afterSales.getType() != 4090 && afterSales.getType() != 4091) {
            log.info("yxb 传递erp售后单类型不符合：{}",JSON.toJSONString(dto));
            return R.error(BaseResponseCode.YXB_SN_ORDER_ERROR);
        }
        List<YxbSnDto.SnDetail> errorList = new LinkedList<>();
        List<AfterSalesInstallServiceRecordDetailDto> canSaveList = new LinkedList<>();
        // 查询售后商品，排除已经记录了安调的 并计算可填入的sn
        List<AfterSalesInstallServiceRecordDetailDto> afterSalesInstallGoodsInfoAdd = afterSalesCommonService.getAfterSalesInstallGoodsInfoAdd(afterSales.getAfterSalesId());
        List<YxbSnDto.SnDetail> detail = dto.getDetail();
        if (CollUtil.isEmpty(afterSalesInstallGoodsInfoAdd)) {
            detail.forEach(d -> {
                d.setMsg("当前售后单无可录sku");
                errorList.add(d);
            });
            dto.setDetail(errorList);
            log.info("saveYxbRecord 返回：{}", JSON.toJSONString(dto));
            return R.success("sn异常",dto);
        }

        processYxbSaveList(errorList,  canSaveList, afterSalesInstallGoodsInfoAdd, detail);

        if (CollUtil.isEmpty(canSaveList)) {
            dto.setDetail(errorList);
            log.info("saveYxbRecord 返回：{}", JSON.toJSONString(errorList));
            return R.success(dto);
        }
        AfterSalesInstallServiceRecordDto save = new AfterSalesInstallServiceRecordDto();
        save.setCheckDate(dto.getCheckDate());
        save.setCheckConclusion(dto.getCheckConclusion());
        save.setCheckType(dto.getCheckType());
        save.setAfterSalesId(afterSales.getAfterSalesId());
        save.setDetail(canSaveList);
        save.setFileNames("");
        save.setFileUris("");

        try {
            List<String> strings = afterSalesCommonService.yxbFileSava(dto.getFileList());



            if (CollUtil.isNotEmpty(strings)) {
                List<String> fileUris = new LinkedList<>();
                for (int i = 0; i < strings.size(); i++) {
                    fileUris.add("yxb_file_"+afterSales.getAfterSalesId() + i);
                }
                save.setFileNames(String.join(",", fileUris));
                save.setFileUris(String.join(",", strings));
            }
            afterSalesCommonService.saveServiceRecord(save);
        } catch (Exception e) {
            logger.error("保存售后安调服务记录失败", e);
            return R.error("保存失败");
        }


        if (CollUtil.isEmpty(errorList)) {
            return R.success();
        }
        dto.setDetail(errorList);
        log.info("saveYxbRecord 返回：{}", JSON.toJSONString(dto));
        return R.success(dto);
    }

    private void processYxbSaveList(List<YxbSnDto.SnDetail> errorList, List<AfterSalesInstallServiceRecordDetailDto> canSaveList, List<AfterSalesInstallServiceRecordDetailDto> afterSalesInstallGoodsInfoAdd, List<YxbSnDto.SnDetail> detail) {
        Map<String, List<AfterSalesInstallServiceRecordDetailDto>> canEditSku2Sku = afterSalesInstallGoodsInfoAdd.stream().collect(Collectors.groupingBy(AfterSalesInstallServiceRecordDetailDto::getSku));
        Map<String, List<YxbSnDto.SnDetail>> detail2Sku = detail.stream().collect(Collectors.groupingBy(YxbSnDto.SnDetail::getSku));


        detail2Sku.forEach((k,v)->{
            List<YxbSnDto.SnDetail> rightList = new LinkedList<>();
            List<AfterSalesInstallServiceRecordDetailDto> canEditList = canEditSku2Sku.get(k);
            // 校验补充码
            if (CollUtil.isEmpty(canEditList)) {

                for (YxbSnDto.SnDetail d : v) {
                    d.setMsg("当前售后单无可录sku");
                    errorList.add(d);
                }

            } else {
                List<String> sn = canEditList.stream().map(AfterSalesInstallServiceRecordDetailDto::getSnList).filter(CollUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());

                for (YxbSnDto.SnDetail d : v) {
                    if (StrUtil.isEmpty(d.getSn())) {
                        d.setMsg("SN码不能为空");
                        errorList.add(d);
                        continue;
                    }
                    String dSn = d.getSn();
                    boolean contains = sn.contains(dSn);
                    if (!contains) {
                        boolean exists = warehouseGoodsOutService.isExistsSnInWarehouseGoodsOut(d.getSn(), CollUtil.newArrayList(
                                WarehouseGoodsOutEnum.SALE_WAREHOUSE_OUT.getErpCode(),
                                WarehouseGoodsOutEnum.SALE_EXCHANGE_WAREHOUSE_OUT.getErpCode()));
                        boolean flag = afterSalesInstallServiceRecordDetailService.querySupplCodeIsExtend(d.getSn());

                        if (exists||flag) {
                            d.setMsg("该SN码已存在于系统中");
                            errorList.add(d);
                            continue;
                        }

                    }
                    if (rightList.size() < canEditList.size()) {

                        AfterSalesInstallServiceRecordDetailDto detailDto = new AfterSalesInstallServiceRecordDetailDto();
                        BeanUtil.copyProperties(canEditList.get(0),detailDto);
                        if (contains) {
                            detailDto.setSerialNumber(d.getSn());
                            detailDto.setSupplCode("");
                        } else {
                            detailDto.setSupplCode(d.getSn());
                            detailDto.setSerialNumber("");
                        }
                        canSaveList.add(detailDto);
                        rightList.add(d);
                    } else {
                        d.setMsg("超出当前sku可录数量");
                        errorList.add(d);
                    }

                }
            }
            rightList.clear();

        });
    }

    /**
     *  初始化编辑售后安调服务记录页面
     */
    @FormToken(save = true)
    @RequestMapping("/initEditServiceRecord")
    public ModelAndView initEditServiceRecord(Integer afterSalesServiceId) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("/orderstream/aftersales/edit_afterSales_at_service");
        //获取验收结论的字典记录
        List<SysOptionDefinition> checkConclusionList = sysOptionDefinitionService.getSysOptionDefinitionListByParentId(SysOptionConstant.ID_4084);
        mv.addObject("checkConclusionList", checkConclusionList);
        //编辑页面，查询已维护信息
        afterSalesCommonService.getAfterSalesInstallInfoEdit(afterSalesServiceId, mv);
        mv.addObject("afterSalesServiceId", afterSalesServiceId);
        return mv;
    }

    /**
     * 编辑服务记录信息
     */
    @RequestMapping("/saveEditServiceRecord")
    @ResponseBody
    public ResultInfo saveEditServiceRecord(@RequestBody AfterSalesInstallServiceRecordDto dto) {

        try {
            afterSalesCommonService.updateServiceRecord(dto);
        } catch (Exception e) {
            logger.error("编辑服务记录信息错误", e);
            return ResultInfo.error("编辑失败");
        }
        return ResultInfo.success();
    }

    /**
     * 删除安调服务记录
     *
     * @param afterSalesServiceId 安调服务记录id
     */
    @RequestMapping("/deleteServiceRecord")
    @ResponseBody
    public ResultInfo deleteServiceRecord(Integer afterSalesServiceId) {
        int i = afterSalesInstallServiceRecordDetailService.deleteServiceRecord(afterSalesServiceId);
        if (i > 0) {
            return new ResultInfo(0, "操作成功");
        } else {
            return new ResultInfo();
        }
    }

    /**
     * 删除安调服务记录详情
     *
     * @param afterSalesServiceId 安调服务记录id
     */
    @RequestMapping("/deleteServiceRecordDetail")
    @ResponseBody
    public ResultInfo deleteServiceRecordDetail(Integer afterSalesServiceId,Integer afterSalesInstallDetailId) {
        logger.info("删除安调服务记录详情,afterSalesServiceId:{},afterSalesInstallDetailId:{}",afterSalesServiceId,afterSalesInstallDetailId);
        int i = afterSalesInstallServiceRecordDetailService.deleteServiceRecordDetail(afterSalesServiceId,afterSalesInstallDetailId);
        if (i > 0) {
            return new ResultInfo(0, "操作成功",i);
        } else {
            return new ResultInfo(0, "操作成功",i);
        }
    }


    @RequestMapping("/viewAttachment")
    @ResponseBody
    public ModelAndView viewAttachment(Integer afterSalesServiceId) {
        ModelAndView mv = new ModelAndView("orderstream/aftersales/preview_direct_file");
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(SysOptionConstant.ID_460);
        attachment.setAttachmentFunction(SysOptionConstant.ID_4092);
        attachment.setRelatedId(afterSalesServiceId);
        List<Attachment> attachmentList = attachmentService.queryAttachmentList(attachment);
        mv.addObject("oss_http", ossHttp);
        mv.addObject("attachmentList", attachmentList);
        return mv;
    }

}
