package com.vedeng.system.service;



import com.vedeng.authorization.model.Organization;

import java.util.List;

public interface OrganizationService {
    /**
     * <b>Description:</b>获取某个组织及其所有子部门<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/11/13
     */
    List<Organization> getOrganizationChild(Organization organization);

    /**
     * <b>Description:</b>根据组织名称获取组织信息<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/8
     */
    Organization getOrganizationByName(String orgName);

    /**
     * <b>Description:</b>通过部门名称查询部门及子部门id<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    List<Integer> getChildOrgByName(String orgName);

    /**
     * B2b 子部门查询到B2B的所有部门集合
     * @param orgId
     * @param orgIds B2B部门集合
     * @param names 部门集合
     * @return
     */
    void queryOrgNameB2B(Integer orgId,List<Integer> orgIds,List<String> names);

    /**
     * 获取部门信息
     * @param orgIds
     * @return
     */
    List<Organization> selectOrganizationNameByOrgids(List<Integer> orgIds);
}
