package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 红字确认单申请入参
 *
 */
@Data
public class SaleInvoiceRedConfirmApplyRequestDto extends SaleInvoiceRedConfirmInitRequestDto {

    /**
     * 冲红原因代码
     * 01- 开票有误
     * 02- 销货退回，
     * 03- 服务中止，
     * 04- 销售折让
     */
    private String chyyDm;

    /**
     * 红字冲销金额
     */
    private String hzcxje;

    /**
     * 红字冲销税额
     */
    private String hzcxse;

    /**
     * 红字确认单明细数量
     */
    private Integer hzqrdmxsl;

    /**
     * 明细
     */
    private List<Hzqrxxmx> hzqrxxmxList;

    @Data
    public static class Hzqrxxmx {
        /**
         * 蓝字明细序号
         */
        private Integer lzmxxh;

        /**
         * 原发票金额
         */
        private String oldje;

        /**
         * 原发票商品数量
         */
        private String oldfpspsl;

        /**
         * 原发票商品单价
         */
        private String oldfpspdj;

        /**
         * 原发票含税发票商品单价
         */
        private String taxfpspdj;

        /**
         * 原发票含税金额
         */
        private String taxje;

        /**
         * 红冲单价
         */
        private String fpspdj;

        /**
         * 红冲数量
         */
        private String fpspsl;

        /**
         * 红冲金额
         */
        private String je;

        /**
         * 红冲税额
         */
        private String se;
    }
}
