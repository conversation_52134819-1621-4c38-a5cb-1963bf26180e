package com.vedeng.order.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.Subscribe;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.business.dto.CloseAuditDto;
import com.vedeng.system.service.VerifiesRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;


@Component
@Slf4j
public class BusinessCloseAuditListener implements IObserver{

    @Autowired
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    private ActionProcdefService actionProcdefService;

    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void close(CloseAuditDto closeAuditDto) {
        log.info("关闭审核监听器:{}", JSON.toJSON(closeAuditDto));
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", closeAuditDto.getPass());
        //审批操作
        try {
            Integer status;
            if(closeAuditDto.getPass()){
                //如果审核通过
                status = 1;
            }else{
                //如果审核不通过
                status = 2;
                variables.put("value", 0);
                verifiesRecordService.saveVerifiesInfo(closeAuditDto.getTaskId(),status);
            }

            ResultInfo<?> complementStatus = actionProcdefService.complementTask(null,closeAuditDto.getTaskId(),closeAuditDto.getRemark(),closeAuditDto.getCurrentUser(),variables);
            //如果未结束添加审核对应主表的审核状态
            if(!complementStatus.getData().equals("endEvent")){
                verifiesRecordService.saveVerifiesInfo(closeAuditDto.getTaskId(),status);
            }
            closeAuditDto.setResult(Boolean.TRUE);
        } catch (Exception e) {
            closeAuditDto.setResult(Boolean.FALSE);
            log.error("business chnace complementTask:", e);
        }
    }
}
