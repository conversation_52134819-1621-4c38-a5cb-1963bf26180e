<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.CoreSpuMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.CoreSpu">
        <id property="spuId" column="SPU_ID" jdbcType="INTEGER"/>
        <result property="categoryId" column="CATEGORY_ID" jdbcType="INTEGER"/>
        <result property="brandId" column="BRAND_ID" jdbcType="INTEGER"/>
        <result property="spuNo" column="SPU_NO" jdbcType="VARCHAR"/>
        <result property="spuName" column="SPU_NAME" jdbcType="VARCHAR"/>
        <result property="showName" column="SHOW_NAME" jdbcType="VARCHAR"/>
        <result property="spuLevel" column="SPU_LEVEL" jdbcType="BOOLEAN"/>
        <result property="status" column="STATUS" jdbcType="BOOLEAN"/>
        <result property="spuType" column="SPU_TYPE" jdbcType="INTEGER"/>
        <result property="firstEngageId" column="FIRST_ENGAGE_ID" jdbcType="INTEGER"/>
        <result property="registrationIcon" column="REGISTRATION_ICON" jdbcType="VARCHAR"/>
        <result property="wikiHref" column="WIKI_HREF" jdbcType="VARCHAR"/>
        <result property="operateInfoFlag" column="OPERATE_INFO_FLAG" jdbcType="BOOLEAN"/>
        <result property="checkStatus" column="CHECK_STATUS" jdbcType="INTEGER"/>
        <result property="operateInfoId" column="OPERATE_INFO_ID" jdbcType="INTEGER"/>
        <result property="hospitalTags" column="HOSPITAL_TAGS" jdbcType="VARCHAR"/>
        <result property="addTime" column="ADD_TIME" jdbcType="TIMESTAMP"/>
        <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
        <result property="modTime" column="MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
        <result property="checkTime" column="CHECK_TIME" jdbcType="TIMESTAMP"/>
        <result property="checker" column="CHECKER" jdbcType="INTEGER"/>
        <result property="deleteReason" column="DELETE_REASON" jdbcType="VARCHAR"/>
        <result property="lastCheckReason" column="LAST_CHECK_REASON" jdbcType="VARCHAR"/>
        <result property="assignmentManagerId" column="ASSIGNMENT_MANAGER_ID" jdbcType="INTEGER"/>
        <result property="assignmentAssistantId" column="ASSIGNMENT_ASSISTANT_ID" jdbcType="INTEGER"/>
        <result property="apparatusType" column="APPARATUS_TYPE" jdbcType="TINYINT"/>
        <result property="storageConditionTemperature" column="STORAGE_CONDITION_TEMPERATURE" jdbcType="BOOLEAN"/>
        <result property="storageConditionTemperatureLowerValue" column="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionTemperatureUpperValue" column="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionHumidityLowerValue" column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionHumidityUpperValue" column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionOthers" column="STORAGE_CONDITION_OTHERS" jdbcType="VARCHAR"/>
        <result property="technicalParameterNames" column="TECHNICAL_PARAMETER_NAMES" jdbcType="VARCHAR"/>
        <result property="secondLevelSpuType" column="SECOND_LEVEL_SPU_TYPE" jdbcType="INTEGER"/>
        <result property="specsModel" column="SPECS_MODEL" jdbcType="VARCHAR"/>
        <result property="medicalInstrumentCatalogIncluded" column="MEDICAL_INSTRUMENT_CATALOG_INCLUDED"
                jdbcType="BOOLEAN"/>
        <result property="goodsLevelNo" column="GOODS_LEVEL_NO" jdbcType="INTEGER"/>
        <result property="goodsPositionNo" column="GOODS_POSITION_NO" jdbcType="INTEGER"/>
        <result property="disabledReason" column="DISABLED_REASON" jdbcType="VARCHAR"/>
        <result property="taxClassificationCode" column="TAX_CLASSIFICATION_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        SPU_ID,CATEGORY_ID,BRAND_ID,
        SPU_NO,SPU_NAME,SHOW_NAME,
        SPU_LEVEL,STATUS,SPU_TYPE,
        FIRST_ENGAGE_ID,REGISTRATION_ICON,WIKI_HREF,
        OPERATE_INFO_FLAG,CHECK_STATUS,OPERATE_INFO_ID,
        HOSPITAL_TAGS,ADD_TIME,CREATOR,
        MOD_TIME,UPDATER,CHECK_TIME,
        CHECKER,DELETE_REASON,LAST_CHECK_REASON,
        ASSIGNMENT_MANAGER_ID,ASSIGNMENT_ASSISTANT_ID,APPARATUS_TYPE,
        STORAGE_CONDITION_TEMPERATURE,STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE,STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE,
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,STORAGE_CONDITION_OTHERS,
        TECHNICAL_PARAMETER_NAMES,SECOND_LEVEL_SPU_TYPE,SPECS_MODEL,
        MEDICAL_INSTRUMENT_CATALOG_INCLUDED,GOODS_LEVEL_NO,GOODS_POSITION_NO,
        DISABLED_REASON,TAX_CLASSIFICATION_CODE
    </sql>
    <select id="queryRegistrationNumberBySpuId" resultType="com.vedeng.goods.dto.RegistrationNumberSpuDto">
        select c.REGISTRATION_NUMBER_ID,
               c.REGISTRATION_NUMBER,
               c.MANAGE_CATEGORY_LEVEL,
               c.PRODUCT_COMPANY_ID,
               c.PRODUCTION_ADDRESS,
               c.PRODUCT_CHINESE_NAME,
               c.PRODUCT_ENGLISH_NAME,
               c.PRODUCT_CATEGORY_ID,
               c.PRODUCT_CATEGORY_NAME,
               c.MODEL,
               c.ISSUING_DATE,
               c.EFFECTIVE_DATE,
               c.DEAL_STATUS,
               c.APPROVAL_DEPARTMENT,
               c.TRADEMARK,
               c.ZIP_CODE,
               c.REGISTERED_AGENT,
               c.REGISTERED_AGENT_ADDRESS,
               c.PRO_PERF_STRU_AND_COMP,
               c.PRODUCT_USE_RANGE,
               c.OTHER_CONTENTS,
               c.PRODUCT_STANDARDS,
               c.TYPE,
               c.PRODUCTION_OR_COUNTRY,
               c.REMARKS,
               c.AFTERSALE_SERVICE_ORG,
               c.CHANGE_DATE,
               c.CHANGE_CONTENTS,
               c.IS_RELEASE,
               c.EXPECTED_USAGE,
               c.STORAGE_COND_AND_EFFECTIVE_DATE,
               c.MAIN_PRO_PERF_STRU_AND_COMP,
               c.ADD_TIME,
               c.CREATOR,
               c.MOD_TIME,
               c.UPDATER,
               c.ATTACHMENT,
               c.CATEGORY,
               c.STORAGE_AND_EXPIRY_DATE,
               c.IS_SUBCONTRACT_PRODUCTION,
               c.PRODUCTION_ENTERPRISE_NAME,
               c.BC_ISSUE_DATE,
               c.PRODUCT_COMPANY_LICENCE,
               c.MANUFACTURER_ID,
               b.FIRST_ENGAGE_ID
        from V_CORE_SPU a
         left join T_FIRST_ENGAGE b on a.FIRST_ENGAGE_ID = b.FIRST_ENGAGE_ID
         left join T_REGISTRATION_NUMBER c on b.REGISTRATION_NUMBER_ID = c.REGISTRATION_NUMBER_ID
        where
            a.SPU_ID = #{spuId,jdbcType=INTEGER}
    </select>

    <select id="queryRegistraAndManufactureBySpuId" resultType="com.vedeng.goods.dto.RegistrationNumberSpuDto">
        select
                c.REGISTRATION_NUMBER,
                MM.MANUFACTURER_NAME,
                PC.PRODUCT_COMPANY_CHINESE_NAME as PRODUCT_CHINESE_NAME
             from V_CORE_SPU a
        left join T_FIRST_ENGAGE b on a.FIRST_ENGAGE_ID = b.FIRST_ENGAGE_ID
        left join T_REGISTRATION_NUMBER c on b.REGISTRATION_NUMBER_ID = c.REGISTRATION_NUMBER_ID
        LEFT JOIN T_MANUFACTURER MM ON MM.MANUFACTURER_ID = c.MANUFACTURER_ID
        left join T_PRODUCT_COMPANY PC on c.PRODUCT_COMPANY_ID = PC.PRODUCT_COMPANY_ID
        where
                 a.SPU_ID = #{spuId,jdbcType=INTEGER}
    </select>

    <update id="updateStatusAndCheckStatusBySpuIds" parameterType="java.util.List">
        update V_CORE_SPU
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="CHECK_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.checkStatus != null">
                        when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.checkStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            `STATUS` = true
        </trim>
        where SPU_ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.spuId,jdbcType=INTEGER}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2023-11-01-->
    <update id="updateTaxClassificationCodeByCategoryIdIn">
        update V_CORE_SPU
        set TAX_CLASSIFICATION_CODE=#{updatedTaxClassificationCode,jdbcType=VARCHAR},
            MOD_TIME=now(),
            UPDATER=#{userId,jdbcType=INTEGER}
        where
        CATEGORY_ID in
        <foreach item="item" index="index" collection="categoryIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>
