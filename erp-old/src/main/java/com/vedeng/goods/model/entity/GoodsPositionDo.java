package com.vedeng.goods.model.entity;

import com.vedeng.goods.supprot.BaseGoodsDo;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsPositionDo extends BaseGoodsDo {

    private Integer priority;

    private String positionName;

    private String description;

    /**
     * 签约模式
     *
     * @see com.vedeng.goods.enums.GoodsSignContractModeEnum
     */
    private Integer signContractMode;

}