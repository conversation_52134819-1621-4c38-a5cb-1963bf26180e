<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="作废审核" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
 <div class="formpublic">
            <form method="post" action="<%= basePath %>/order/quote/authorizationAbandon.do" id="zfform">
                <ul>

                    <li>
                        <span style="color: red">是否作废授权书申请，此操作不可逆！请谨慎操作！</span>
                    </li>

                </ul>
                <textarea name="comments" id="comment" style="margin-left: 85px;width: 300px"></textarea>


                <div class="form-item" style="margin-top: 10px;margin-left: 85px">
                    <div class="item-fields">
                        <div class="pos_rel f_left">
                        <input type="file" class="uploadErp" id='lwfile' name="lwfile"  onchange="uploadFile(this,1);">
                        <input type="text" class="input-larger" style="width: 200px" id="name_1" name="name" readonly="readonly" value=""
                               placeholder="使用pdf、jpg、word或excel等文件，不允许超过2MB"  onclick="lwfile.click();">
                        <input type="hidden" name="uri" id="uri_1" value="" />
                        <input type="hidden" name="" id="domain" value="" />
                        <label class="bt-bg-style bt-small bg-light-blue ml8" type="file" class="f_left">上传附件</label>
                        </div>
                        <div class='clear'></div>
                    </div>

                </div>



                <div class="add-tijiao tcenter" style="margin: 10px auto">
                	<input type="hidden" name="formToken" value="${formToken}"/>
                	<input type="hidden" value="${authorizationApplyId}" name="authorizationApplyId">
                    <button type="button" class="bg-light-green" onclick="zuofei()">确定</button>

                </div>
           </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/quote/authorization_index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>