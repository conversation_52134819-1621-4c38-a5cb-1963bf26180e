@import (less) '../common.css';

.vd-ui-input {
    display: inline-block;
    vertical-align: top;
    position: relative;
    .vd-ui-input__icon {
        font-size: 16px;
        color: @Text-3;
        position: relative;
        top:2.5px;
        margin: 0 5px;
        &::after {
            content: "";
            height: 100%;
            width: 0;
            display: inline-block;
            vertical-align: middle;
        }
    }
    .icon-type{
        position:absolute;
        font-size: 16px;
        margin-left: 10px;
        left: 100%;
        top:50%;
        transform: translateY(-50%);
    }
    .vd-ui-input__inner {
        color: @Text-4;
        background-color: @Text-7;
        border: solid 1px @Gray-7;
        border-radius: 3px;
        height: 30px;
        padding: 0px 10px;
        font-size: 12px;
        width: 100%;
        transition: border-color .1s linear;

        &::placeholder {
            color: @Text-2;
            font-size: 12px;
        }

        &:hover {
            border-color: @Gray-9;
        }

        
    }

    &.vd-ui-input--error {
        >.vd-ui-input__inner {
            border-color: #e64545;
        }
    }

    .vd-ui-input-text{
        color: #999;
        margin-top: 5px;
        position: absolute;
    }
    &.vd-ui-input--large {
        .vd-ui-input__inner {
            height: 42px;
            padding: 0px 10px;
            font-size: 16px;
        }
    }

    &.vd-ui-input--small {
        .vd-ui-input__inner {
            height: 26px;
            padding: 0px 10px;
            font-size: 12px;
        }
    }

    &.is-disabled {
        .vd-ui-input__inner {
            background-color: @Gray-2;
            border-color: @Gray-5;
            color: @Text-2;
            cursor: not-allowed;
        }
    }
    &.is-focus {
        .vd-ui-input__inner {
            border-color: @Brand-6;
        }
    }
 
    &.vd-ui-input-error{
        .vd-ui-input__inner {
            
            border-color: @Crimson-6;;
           
        }
    }
    &.vd-ui-input--prefix {
        
        .vd-ui-input__inner {
            padding-left: 36px;
        }

        .vd-ui-input__prefix {
            position: absolute;
            top: 0;
            left: 5px;
            max-height: 35px;
            height: 100%;
            pointer-events: none;
        }
    }

    &.vd-ui-input--suffix {
        .vd-ui-input__inner {
            padding-right: 36px;
        }

        .vd-ui-input__suffix {
            position: absolute;
            top: 0;
            right: 5px;
            max-height: 35px;
            height: 100%;
            pointer-events: none;
            .vd-ui-input__suffix-inner{
                .vd-ui-input__icon{
                    pointer-events: all;
                    cursor: pointer;
                }
                .icon-error2{

                }
            }
        }
    }

    &.vd-ui-input-group {
        display: inline-table;
        .vd-ui-input__inner {
            flex:1;
        }
        &.vd-ui-input-group--prepend {
            .vd-ui-input-group__prepend {
                background-color: @Gray-2;
                vertical-align: middle;
                display: table-cell;
                position: relative;
                border: 1px solid @Gray-7;
                border-radius: 4px;
                padding: 0 10px;
                white-space: nowrap;
                border-right: 0;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
                text-align: center;
            }

            .vd-ui-input__inner {
                display: table-cell;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }

        &.vd-ui-input-group--append {
            .vd-ui-input-group__append {
                background-color: @Gray-2;
                vertical-align: middle;
                display: table-cell;
                position: relative;
                border: 1px solid @Gray-7;
                border-radius: 4px;
                padding: 0 10px;
                white-space: nowrap;
                border-left: 0;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                text-align: center;
                .icon{
                    font-size: 16px;
                    margin:0 5px;
                    position:relative;
                    top:1px;
                }
            }

            .vd-ui-input__inner {
                display: table-cell;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
        }
    }
    .vd-ui-input--error {
        color:@Crimson-6;

        .vd-ui-input-error--icon{
            font-size: 16px;
            margin-right: 5px;
            line-height: 1;
            margin-top: 1px;
        }
        .vd-ui-input-error--errmsg{
            margin:0px;
        }
    }
}

.vd-ui-textarea {
    position: relative;
    display: inline-block;
    
    .vd-ui-textarea__inner {
        width: 100%;
        min-height: 30px;
        height: 30px;
        font-size: 12px;
        display: block;
        resize: vertical;
        color: @Text-4;
        background-color: @Text-7;
        border: solid 1px @Gray-7;
        border-radius: 3px;
        padding: 5px 10px;
        transition: border-color .1s linear;
        .scrollbar;
        &::placeholder {
            color: @Text-2;
            font-size: 12px;
        }

        &:hover {
            border-color: @Gray-9;
        }

        &:focus {
            border-color: @Brand-6;
        }
    }
    &.is-disabled {
        .vd-ui-textarea__inner {
            background-color: @Gray-2;
            border-color: @Gray-5;
            color: @Text-2;
            cursor: not-allowed;
        }
    }
    .vd-ui-textarea-place {
        position: relative;
        display: inline-block;
        vertical-align: top;

        .vd-ui-input__count {
            color: @Text-2;
            position: absolute;
            bottom: 0;
            left: calc(100% + 5px);
            text-align: right;
            white-space: nowrap;
    
            &.upper-limit {
                color: @OrangeRed-6;
            }
        }
    }
}

.vd-ui-number-range {
    display: flex;
    align-items: center;

    .range-gap {
        margin: 0 5px 0 4px;
    }
}

.vd-ui-number-input {
    display: flex;
    position: relative;

    .vd-ui-button {
        width: 20px;
        height: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        line-height: 1;
        color: #999;
        user-select: none;
        
        &.left {
            border-radius: 3px 0 0 3px;
        }
        
        &.right {
            border-radius: 0 3px 3px 0;
        }
    }

    .vd-ui-input {
        flex: 1;
        margin: 0 -1px;
        position: relative;
        z-index: 1;
        
        .vd-ui-input__inner {
            border-radius: 0;
            text-align: center;
            padding: 0 5px;
        }
    }

    .bubble-tip-wrap {
        bottom: 32px;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0;
        z-index: -1;
        pointer-events: none;
        transition: opacity .22s ease;

        &.show {
            opacity: 1;
            z-index: 11;
            pointer-events: all;
        }
    }
}