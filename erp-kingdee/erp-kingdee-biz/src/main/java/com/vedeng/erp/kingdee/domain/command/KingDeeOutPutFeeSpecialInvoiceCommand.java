package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶销项 专票 实际接受参数类
 * @date 2022/11/09 10:11
 */
@Data
public class KingDeeOutPutFeeSpecialInvoiceCommand {

    /**
     * 单据内码
     */
    private String FID;
    /**
     * 贝登erp对应的单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 发票号
     */
    private String FINVOICENO;
    /**
     * 发票代码
     */
    private String F_QZOK_FPDM;
    /**
     * 发票日期  2022-09-07 00:00:00
     */
    private String FINVOICEDATE;
    /**
     * 业务日期 2022-09-07 00:00:00
     */
    private String FDATE;
    /**
     * 往来单位类型 BD_Customer
     */
    private String FCONTACTUNITTYPE;

    /**
     * 客户
     */
    private KingDeeNumberCommand FCONTACTUNIT = new KingDeeNumberCommand();
    /**
     * 销售组织
     */
    private KingDeeNumberCommand FSALEORGID = new KingDeeNumberCommand();
    /**
     * 结算组织
     */
    private KingDeeNumberCommand FSETTLEORGID = new KingDeeNumberCommand();
    /**
     * 单据状态
     */
    private String FDOCUMENTSTATUS;
    /**
     * 红蓝字标识
     */
    private String FRedBlue;

    /**
     * 归属业务单号
     */
    private String f_QZOK_PZGSYWDH;
    /**
     * 发票明细
     */
    private List<OutPutFeeSpecialInvoiceDetailCommand> FSALEEXINVENTRY = new ArrayList<>();

    @Getter
    @Setter
    public static class OutPutFeeSpecialInvoiceDetailCommand {
        /**
         * 费用编码
         */
        private KingDeeNumberCommand FEXPENSEID = new KingDeeNumberCommand();
        /**
         * 含税单价
         */
        private BigDecimal FAUXTAXPRICE;
        /**
         * 数量
         */
        private String FPRICEQTY;
        /**
         * 税率
         */
        private String FTAXRATE;
        /**
         * 贝登单据行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 源单类型
         */
        private String FSRCBILLTYPEID;

        /**
         * 费用对应的物料代码
         */
        private KingDeeNumberCommand F_QZOK_WLBM = new KingDeeNumberCommand();


        /**
         * 关联关系表
         */
        private List<OutPutFeeSpecialInvoiceDetailLinkCommand> FSALEEXINVENTRY_LINK = new ArrayList<>();
    }

    @Getter
    @Setter
    public static class OutPutFeeSpecialInvoiceDetailLinkCommand {
        /**
         * 实体主键
         */
        private String FLinkId;
        /**
         *
         */
        private String FSALEEXINVENTRY_Link_FFlowId;
        /**
         * 转换规则 源单是费用应收单：IV_ReceivableToSaleExInv_Entry
         */
        private String FSALEEXINVENTRY_Link_FFlowLineId;
        /**
         * 转换规则 源单是费用应收单：IV_ReceivableToSaleExInv_Entry
         */
        private String FSALEEXINVENTRY_Link_FRuleId;
        /**
         * 源单表内码
         */
        private String FSALEEXINVENTRY_Link_FSTableId;
        /**
         * 源单表 源单费用应收单：t_AR_receivableEntry
         */
        private String FSALEEXINVENTRY_Link_FSTableName;
        /**
         * 源单内码 源单的表头表单的内码
         */
        private String FSALEEXINVENTRY_Link_FSBillId;
        /**
         * 源单分录内码 源单的表体行内码
         */
        private String FSALEEXINVENTRY_Link_FSId;
        /**
         * 原始携带金额
         */
        private BigDecimal FSALEEXINVENTRY_Link_FALLAMOUNTFOROLD;
        /**
         * 修改携带量（实开金额）
         */
        private BigDecimal FSALEEXINVENTRY_Link_FALLAMOUNTFOR;
    }

}
