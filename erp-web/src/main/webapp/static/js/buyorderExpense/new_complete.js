//采购订单审核
function buyOrderAudit(){

	$.ajax({
		type: "POST",
		url: "/old/buyorderExpense/complementTaskForBuyOrder.do",
		data: $('#complement').serialize(),
		dataType:'json',
		success: function(data){
			if(data.code == 0){
				layer.close(index);
				window.parent.location.href = page_url + '/buyorderExpense/details.do?buyorderExpenseId='+data.data;
			}else{
				layer.confirm(data.message,{btn: ['确认']},function () {
					window.parent.location.reload();
				});

			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

//其他类型的单据审核操作 比如付款申请 采购单修改申请
function otherOrderSubmit(){
    //订单审核
    $.ajax({
        type: "POST",
        url: "/order/newBuyorder/complementBuyorderModifyApply.do",
        data: $('#complement').serialize(),
        dataType:'json',
        success: function(data){
            if(data.status == 1){
                layer.close(index);
                window.parent.location.href = page_url + '/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId='+data.data.buyorderId
            }else{
                window.parent.location.reload();
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function complementTask(){
	debugger
	checkLogin();
	var comment = $("input[name='comment']").val()
	var taskId = $("input[name='taskId']").val()
	var pass = $("input[name='pass']").val()
	var type = $("input[name='type']").val()
	var buyorderId = $("input[name='buyorderExpenseId']").val();

	if(pass =="false" && comment == ""){
		warnTips("comment","请填写备注");
		return false;
	}
	if(comment.length > 256){
		warnTips("comment","备注内容不允许超过256个字符");
		return false;
	}

	//采购订单的操作
	if(type == 3){

		// var purchasePriceChange = $("#purchasePriceChange").val();
		// var tips = $("#tips").val();
		//
		// if(purchasePriceChange == 1){
		// 	layer.confirm(tips, {
		// 		btn: ['确定','取消']
		// 	}, function(){
		// 		buyOrderAudit();
		// 	}, function(){
		// 	});
		// 	return;
		// }

		buyOrderAudit();
    }else if(type == 1){
        otherOrderSubmit();
	}else if(type == 2){
		//售后审核
		$.ajax({
			type: "POST",
			url: "./complementAfterSaleTask.do",
			data: $('#complement').serialize(),
			dataType:'json',
			success: function(data){
				refreshPageList(data)
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		
	}
		
		

}