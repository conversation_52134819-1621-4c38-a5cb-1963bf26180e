package com.vedeng.common.constant;

public enum BuyOrderStatusEnum {
    WAIT_CONFIRM(0, "待确认"),
    UNDER_WAY(1, "进行中"),
    FINISHED(2, "已完结"),
    CLOSED(3, "已关闭")
    ;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String describe;


    BuyOrderStatusEnum(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }
}
