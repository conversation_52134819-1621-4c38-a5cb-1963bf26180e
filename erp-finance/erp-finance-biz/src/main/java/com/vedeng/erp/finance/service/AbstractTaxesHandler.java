package com.vedeng.erp.finance.service;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * 主抽象类 （统一入口）
 */
@Slf4j
public abstract class AbstractTaxesHandler implements TaxesApiService {

    public abstract ITaxesResult openapi(ITaxesParam taxesParam, TaxesInterfaceCodeEnum taxesInterfaceCodeEnum);
}
