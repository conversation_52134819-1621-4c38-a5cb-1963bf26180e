package com.wms.service.validator;


import com.wms.model.dto.ValidatorResult;

import java.util.ArrayList;
import java.util.List;

public class WMSValidatorChain<T> implements Validator<T> {

    private List<Validator> validatorChains = new ArrayList<Validator>();

    public void add(Validator validator){
        validatorChains.add(validator);
    }

    @Override
    public ValidatorResult validator(T ... requestData) throws Exception{
        if(validatorChains.size() == 0){
            return ValidatorResult.newBuild().setResult(true);
        }

        ValidatorResult validatorResult = ValidatorResult.newBuild().setResult(true);

        for(Validator validator : validatorChains){

            validatorResult = validator.validator(requestData);

            if(validatorResult.getResult() == false){
                break;
            }
        }

        return validatorResult;
    }
}
