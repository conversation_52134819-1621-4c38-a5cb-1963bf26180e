package com.vedeng.erp.saleorder.vo;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.base.R;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Map;

@Data
public class WorkbenchBusinessAnalysisVO {
    /** 到款完成率纯数字 */
    private BigDecimal paymentCompletionRateNumber;
    /** 到款完成率 */
    private String paymentCompletionRate;
    /** 到款目标 */
    private String paymentTarget;
    /** 实际到款 */
    private String actualPayment;

    /** 今日到款 */
    private String todayPayment;

    /** 毛利率 */
    private String grossProfitRate;
    /** 自有品牌销售占比 */
    private String ownBrandSalesRatio;
    /** 预计成单商机金额 */
    private String expectedOrderOpportunityAmount;
    /** 商机满足率 */
    private String opportunitySatisfactionRate;
    /** 线索数 */
    private Integer clueNum;
    /**
     * 商机   ：opportunity
     * 初步洽淡：preliminaryNegotiation
     * 初步方案：preliminaryPlan
     * 最终方案：finalPlan
     * 赢单   ：winOrder
     */
    /** 验证阶段信息，key为阶段数字，value为验证信息 */
    private Map<String, WorkbenchBusinessAnalysisValidationInfo> validationInfoMap;


    public static WorkbenchBusinessAnalysisVO test(){
        WorkbenchBusinessAnalysisVO vo = new WorkbenchBusinessAnalysisVO();
        vo.setPaymentCompletionRateNumber(new BigDecimal("0.8"));
        vo.setPaymentCompletionRate("80%");
        vo.setPaymentTarget("1000000");
        vo.setActualPayment("800000");
        vo.setGrossProfitRate("15%");
        vo.setOwnBrandSalesRatio("30%");
        vo.setExpectedOrderOpportunityAmount("1200000");
        vo.setOpportunitySatisfactionRate("90%");
        vo.setClueNum(50);

        Map<String, WorkbenchBusinessAnalysisValidationInfo> validationInfoMap = new java.util.HashMap<>();
        WorkbenchBusinessAnalysisValidationInfo opportunity = new WorkbenchBusinessAnalysisValidationInfo();
        opportunity.setNum(40);
        opportunity.setConversionRateNumber(new BigDecimal("0.6"));
        opportunity.setConversionRate("60%");
        opportunity.setConversionRateAvgNumber(new BigDecimal("0.55"));
        opportunity.setConversionRateAvg("55%");

        validationInfoMap.put("opportunity", opportunity);

        WorkbenchBusinessAnalysisValidationInfo preliminaryNegotiation = new WorkbenchBusinessAnalysisValidationInfo();
        preliminaryNegotiation.setNum(30);
        preliminaryNegotiation.setConversionRateNumber(new BigDecimal("0.75"));
        preliminaryNegotiation.setConversionRate("75%");
        preliminaryNegotiation.setConversionRateAvg("70%");
        preliminaryNegotiation.setConversionRateAvgNumber(new BigDecimal("0.7"));
        validationInfoMap.put("preliminaryNegotiation", preliminaryNegotiation);

        WorkbenchBusinessAnalysisValidationInfo preliminaryPlan = new WorkbenchBusinessAnalysisValidationInfo();
        preliminaryPlan.setNum(20);
        preliminaryPlan.setConversionRateNumber(new BigDecimal("0.66"));
        preliminaryPlan.setConversionRate("66%");
        preliminaryPlan.setConversionRateAvg("65%");
        preliminaryPlan.setConversionRateAvgNumber(new BigDecimal("0.65"));
        validationInfoMap.put("preliminaryPlan", preliminaryPlan);

        WorkbenchBusinessAnalysisValidationInfo finalPlan = new WorkbenchBusinessAnalysisValidationInfo();
        finalPlan.setNum(10);
        finalPlan.setConversionRateNumber(new BigDecimal("0.5"));
        finalPlan.setConversionRate("50%");
        finalPlan.setConversionRateAvg("48%");
        finalPlan.setConversionRateAvgNumber(new BigDecimal("0.48"));
        validationInfoMap.put("finalPlan", finalPlan);

        WorkbenchBusinessAnalysisValidationInfo winOrder = new WorkbenchBusinessAnalysisValidationInfo();
        winOrder.setNum(5);
        winOrder.setConversionRateNumber(new BigDecimal("0.25"));
        winOrder.setConversionRate("25%");
        winOrder.setConversionRateAvg("20%");
        winOrder.setConversionRateAvgNumber(new BigDecimal("0.2"));
        validationInfoMap.put("winOrder", winOrder);
        vo.setValidationInfoMap(validationInfoMap);
        return vo;
    }

    public static void main(String[] args) {
        System.out.println(JSON.toJSONString(R.success(WorkbenchBusinessAnalysisVO.test())));
    }
}
