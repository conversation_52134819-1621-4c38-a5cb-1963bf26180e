package com.vedeng.kpi.service;

import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;

import java.util.List;

/**
 * 五行商机统计service.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/7/6 11:19 上午.
 * @author: Tomcat.Hui.
 */
public interface KpiChanceService{

    /**
     * 查询历史数据(分配到的商机数量).
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:19 上午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.lang.Integer.
     * @throws: .
     */
    Integer getUserBussinessChancesNum(KpiDataQueryDto query);

}
