package com.vedeng.erp.businessclues.api;

import com.vedeng.erp.businessclues.dto.BusinessCluesDto;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.businessclues.api
 * @Date 2022/3/17 14:50
 */
public interface BusinessCluesApi {

    /**
     * 更新线索关联的商机id
     * @param businessCluesDto
     */
    void updateBusinessCluesChanceId(BusinessCluesDto businessCluesDto);

    /**
     * 根据线索Id查询是否关联了商机
     * @param businessCluesId
     * @return
     */
    BusinessCluesDto selectBusinessChanceByCluesId(Integer businessCluesId);
}
