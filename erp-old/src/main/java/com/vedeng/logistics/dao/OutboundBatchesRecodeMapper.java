package com.vedeng.logistics.dao;

import com.vedeng.erp.saleorder.dto.ApproveMsgDto;
import com.vedeng.erp.saleorder.dto.RefuseMsgDto;
import com.vedeng.logistics.model.OutboundBatchesRecode;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;


@Named("outboundBatchesRecodeMapper")
public interface OutboundBatchesRecodeMapper {
    void insertOrUpdate(OutboundBatchesRecode outboundBatchesRecode);

    /**
     * 根据批次号集合获取批次数据
     */
    List<OutboundBatchesRecode> findAllByBatchNoList(List<String> batchNo);

    void approveAuditStatusById(@Param("approveList") List<ApproveMsgDto> approveList,@Param("nowTime") Long nowTime);

    void refuseAuditStatusById(@Param("r") RefuseMsgDto r,@Param("nowTime") Long nowTime);

    OutboundBatchesRecode getById(@Param("batchId") Integer batchId);

    OutboundBatchesRecode getByBatchNo(@Param("batchNo") String batchNo);

    int updateAuditStatusById(@Param("batchId") Integer batchId, @Param("status") int status);


    int updateCommentsById(@Param("batchId") Integer batchId);


    int updateCommentsDiyById(@Param("batchId") Integer batchId, @Param("comments") String comments);

    int updateUploadStatusById(@Param("batchId") Integer batchId, @Param("status") int status);

    int updateUploadStatusByBatchNo(@Param("batchNo") String batchNo, @Param("status") int status);

    int updateOnlineConfirmByBatchNos(@Param("batchNos") List<String> batchNos, @Param("onlineConfirm") Integer onlineConfirm,
                                      @Param("uploadStatus") Integer uploadStatus, @Param("auditStatus") Integer auditStatus);
}
