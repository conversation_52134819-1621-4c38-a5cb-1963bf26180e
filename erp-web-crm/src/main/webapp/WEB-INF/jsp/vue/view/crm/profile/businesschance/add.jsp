<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>CRM</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/businessChance.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <input type="hidden" id="isHiddenLayout" value="1">
    <jsp:include page="../common/head_import.jsp?staticResourceVersion=${requestScope.staticResourceVersion}"></jsp:include>

    <!-- 商机id 仅编辑 -->
    <input type="hidden" id="businesschanceId" value="${param.id}" />

    <!-- 线索id 线索转商机 -->
    <input type="hidden" id="businessLeadsId" value="${param.leadsid}" />
    <!-- 线索是否有沟通记录 Y有N没有 -->
    <input type="hidden" id="followUp" value="${followUp}" />


    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container">
            <div class="page-main">

                <div class="businesschance-add-container">

                    <div class="top-featurn" :style="{'top': fixedTop+'px'}">
                        <div class="inner-header">
                            <h1 class="inner-header-title">{{ isEdit? (isBusiness ? '编辑商机' : '编辑线索') : '新建线索/商机' }}</h1>
                            <ui-button @click="submit" type="primary">提交</ui-button>
                        </div>
                    </div>

                    <div class="form-wrap" v-if="!pageLoading">
                        <div class="form-top-tip" v-if="!isBusiness">
                            <i class="vd-ui_icon icon-info2"></i>
                            <div class="tip-cnt">系统会跟进您填写的内容自动识别生成线索或商机，具体字段会在表单中提示。</div>
                        </div>
                        <div class="detail-top-card" v-if="isEdit">
                            <div class="detail-top-item" v-if="isBusiness">
                                <div class="item-label">商机编号</div>
                                <div class="item-txt">{{ bussinessChanceNo || '-' }}</div>
                            </div>
                            <div class="detail-top-item" v-else>
                                <div class="item-label">线索编号</div>
                                <div class="item-txt">{{ leadsNo || '-' }}</div>
                            </div>
                            <div class="detail-top-item">
                                <div class="item-label">来源</div>
                                <div class="item-txt">{{ {391: '总机', 392: '销售', 394: '自有商城', 6012: '拜访'}[clueType] }}</div>
                            </div>
                            <div class="detail-top-item">
                                <div class="item-label">归属销售</div>
                                <div class="item-txt">{{ belonger || '-' }}</div>
                            </div>
                            <div class="detail-top-item">
                                <div class="item-label">创建时间</div>
                                <div class="item-txt">{{ addTime || '-' }}</div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-title">客户及终端信息</div>
                            <div>
                                <ui-form-item label="客户名称" :must="true">
                                    <div class="ui-col-12">
                                        <ui-tyc
                                            width="490px"
                                            maxlength="100"
                                            v-model="traderName"
                                            :need-tyc="true"
                                            :need-disable="true"
                                            :tyc-flag="tycFlag"
                                            :sale-name="traderId ? (belonger || '') : ''"
                                            @change="handlerTrader"
                                            valid="AddBusinessChance_traderName"
                                        ></ui-tyc>
                                    </div>
                                </ui-form-item>
                                <ui-form-item label="联系人" :must="isBusiness">
                                    <ui-phone-related
                                        width="323px"
                                        placeholder="已建档客户，可输入姓名搜索"
                                        v-model="traderContactName"
                                        :trader-id="traderId"
                                        :accurate-match="false"
                                        @change="handlerContact"
                                        type="contact"
                                        :novalid="true"
                                        :maxlength="20"
                                        valid="AddBusinessChance_traderContactName"
                                    ></ui-phone-related>
                                </ui-form-item>
                                <ui-form-item label="手机">
                                    <ui-phone-related
                                        width="323px"
                                        placeholder="已建档客户，可输入手机号搜索"
                                        v-model="mobile"
                                        :trader-id="traderId"
                                        :accurate-match="false"
                                        @blur="phone_Blur"
                                        :error-msg="phoneMsg"
                                        @change="handlerPhone"
                                    ></ui-phone-related>
                                </ui-form-item>
                                <ui-form-item label="固话">
                                    <ui-input 
                                        type="number" width="323px" placeholder="需加上区号，可支持20位数字"
                                        v-model="telephone" @blur="telephone_Blur" maxlength="20"
                                        :errorable="Boolean(telephoneMsg)" :error-msg="telephoneMsg" 
                                    ></ui-input>
                                </ui-form-item>
                                <ui-more-contact
                                    label="其他联系方式"
                                    width="323px"
                                    placeholder="请输入联系方式名称"
                                    v-model="otherContactInfo"
                                    @change="handlerMoreContact"
                                ></ui-more-contact>
                                <ui-form-item label="终端名称">
                                    <ui-select-related 
                                        type="text" 
                                        width="323px" 
                                        v-model="terminalTraderName" 
                                        :disabled="isFromTyc"
                                        :remote-info="remoteInfo"
                                        :remote-data-parse="remoteDataParse"
                                        @change="handlerTerminalName"
                                    ></ui-select-related>
                                </ui-form-item>
                                <ui-form-item label="终端性质" :text="true">
                                    <ui-radio-group
                                        :list="radioList1"
                                        :value.sync="terminalTraderNature"
                                        clearable
                                    ></ui-radio-group>
                                </ui-form-item>
                                <ui-form-item label="终端地区">
                                    <ui-cascader
                                        width="323px"
                                        class="margin"
                                        :data="addressData"
                                        v-model="area"
                                        clearable
                                        filterable
                                        :need-trigger="true"
                                        @change="handleArea"
                                    ></ui-cascader>
                                </ui-form-item>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-title">商机详情</div>
                            <div>
                                <div class="form-chance-block-placeholder" :class="{'form-chance-block': !isBusiness,active: !isBusiness && isBusinessAllInput}">
                                    <div class="form-chance-tip" v-if="!isBusiness">
                                        <i class="vd-ui_icon icon-info1"></i><i class="vd-ui_icon icon-yes1"></i>录入该区域内容提交后可自动转商机
                                    </div>
                                    <ui-form-item label="业务类型" :must="isBusiness" :text="true">
                                        <div class="change-type-inner">
                                            <ui-radio-group
                                                :list="radioList2"
                                                :value.sync="businessType"
                                                valid="AddBusinessChance_businessType"
                                                @change="checkBusinessInput"
                                            ></ui-radio-group>
                                            <ui-tip width="380px" position="rt" icon="info2" font="small" :nofixed="true">
                                                小产品：预计总金额5万元以下商机；<br>
                                                大单品：预计总金额5万元及以上的产品（彩超、DR、CT等）；<br>
                                                综合项目：5个产品以上的商机；<br>
                                                AED产品：所有AED单品及AED项目（含AED橱柜）；<br>
                                                应急：所有应急终端范围的业务；
                                            </ui-tip>
                                        </div>
                                    </ui-form-item>
                                    <ui-form-item label="产品信息" :must="isBusiness">
                                        <ui-input
                                            type="textarea"
                                            width="780px"
                                            height="30px"
                                            height-auto
                                            maxlength="500"
                                            show-word-limit
                                            placeholder="请描述客户产品需求"
                                            v-model="productCommentsSale"
                                            valid="AddBusinessChance_productCommentsSale"
                                            @blur="checkBusinessInput"
                                        ></ui-input>
                                    </ui-form-item>
                                    <ai-category-choose v-if="!isAiloading" ref="aicategory" :default-data="defaultAiData" @change="handlerAiCategoryChange"></ai-category-choose>
                                    <ui-form-item label="预计成单金额" :must="isBusiness">
                                        <ui-input
                                            width="150px"
                                            placeholder=""
                                            maxlength="11"
                                            v-model="amount"
                                            valid="AddBusinessChance_amount"
                                            @blur="checkBusinessInput"
                                        ></ui-input><span class="price-dw">元</span>
                                    </ui-form-item>
                                    <ui-form-item label="预计成单日期" :must="isBusiness">
                                        <ui-date-picker
                                            type="date"
                                            width="350px"
                                            v-model="orderTimeDate"
                                            placeholder=""
                                            valid="AddBusinessChance_orderTimeDate"
                                            @change="checkBusinessInput"
                                        ></ui-date-picker>
                                    </ui-form-item>
                                </div>
                                <ui-form-item label="标签" :text="true">
                                    <ui-checkbox-group
                                        :list="checkboxList1"
                                        :values.sync="tagIds"
                                        @change="handlerTags"
                                    ></ui-checkbox-group>
                                </ui-form-item>
                                <ui-form-item label="客情关系" :text="true">
                                    <ui-checkbox-group
                                        :list="checkboxList2"
                                        :values.sync="customerRelationship"
                                        @change="handlerCustomerRelationship"
                                    ></ui-checkbox-group>
                                </ui-form-item>
                                <ui-form-item label="采购方式" :text="true">
                                    <ui-radio-group
                                        :list="radioList3"
                                        :value.sync="purchasingType"
                                        clearable
                                        @change="handlerPurchasing"
                                    ></ui-radio-group>
                                </ui-form-item>
                                <ui-form-item label="招投标阶段" :text="true" v-if="purchasingType == 406">
                                    <ui-radio-group
                                        :list="radioList4"
                                        :value.sync="biddingPhase"
                                        clearable
                                    ></ui-radio-group>
                                </ui-form-item>
                                <ui-form-item label="招标参数" :text="true" v-if="purchasingType == 406">
                                    <ui-radio-group
                                        :list="radioList5"
                                        :value.sync="biddingParameter"
                                        clearable
                                    ></ui-radio-group>
                                </ui-form-item>
                                <ui-form-item label="备注">
                                    <ui-input
                                        type="textarea"
                                        maxlength="500"
                                        placeholder="可输入客户诉求、特殊情况等"
                                        show-word-limit
                                        v-model="comments"
                                        height-auto
                                        height="30px"
                                        width="780px"
                                    ></ui-input>
                                </ui-form-item>
                            </div>
                        </div>


                        <div class="card" v-if="!isEdit">
                            <div class="card-title">跟进记录</div>
                            <div>
                                <!-- <ui-form-item label="跟进类型" :must="true" :text="true">
                                    <ui-radio-group
                                        :list="FollowUpTypeS"
                                        :value.sync="followUpType"
                                        valid="AddBusinessChance_followUpType"
                                    ></ui-radio-group>
                                </ui-form-item>
                                <ui-form-item label="跟进时间" :must="true">
                                    <ui-date-picker
                                        type="datetime"
                                        width="350px"
                                        v-model="beginTimeDate"
                                        placeholder=""
                                        valid="AddBusinessChance_beginTimeDate"
                                    ></ui-date-picker>
                                </ui-form-item>
                                <ui-form-item label="跟进内容" :must="true">
                                    <ui-input
                                        type="textarea"
                                        width="823px"
                                        height="35px"
                                        height-auto
                                        maxlength="200"
                                        show-word-limit
                                        v-model="contentSuffix"
                                        placeholder=""
                                        valid="AddBusinessChance_contentSuffix"
                                    ></ui-input>
                                </ui-form-item> -->
                                <ui-form-item label="待跟进时间" :must="true">
                                    <ui-date-picker
                                        type="date"
                                        width="350px"
                                        placeholder=""
                                        v-model="nextContactDate"
                                        :disabled="Boolean(noneNextDate)"
                                        valid="AddBusinessChance_nextContactDate"
                                    ></ui-date-picker>
                                    <!-- <ui-checkbox 
                                        label="暂无待跟进时间"
                                        class="next-data" 
                                        :checked.sync="noneNextDate" 
                                        @change="handlerNext" 
                                    ></ui-checkbox> -->
                                </ui-form-item>
                                <ui-form-item label="待跟进事项" :must="true">
                                    <ui-input
                                        width="823px"
                                        type="textarea"
                                        maxlength="200"
                                        show-word-limit
                                        height-auto
                                        height="30px"
                                        width="823px"
                                        placeholder=""
                                        v-model="nextContactContent"
                                        :disabled="Boolean(noneNextDate)"
                                        valid="AddBusinessChance_nextContactContent"
                                    ></ui-input>
                                </ui-form-item>
                            </div>
                        </div>
                        <div class="card" v-if="isEdit && clueType != 392">
                            <div class="card-title">渠道信息</div>
                            <template v-if="!isBusiness && clueType == 391 && GLOBAL.auth('C0102')">
                                <ui-form-item label="询价行为" :must="true" :text="true">
                                    <div class="ui-col-12">
                                        <ui-radio-group
                                            :list="SysOptions"
                                            :value.sync="inquiry"
                                            valid="AddBusinessChance_inquiry"
                                        ></ui-radio-group>
                                    </div>
                                </ui-form-item>
                                <ui-form-item label="渠道类型" :must="true" :text="true">
                                    <div class="ui-col-12">
                                        <ui-radio-group
                                            :list="Sources"
                                            :value.sync="source"
                                            :showId="true"
                                            @change="changeSource"
                                            valid="AddBusinessChance_source"
                                        ></ui-radio-group>
                                    </div>
                                </ui-form-item>
                                <template></template>
                                <ui-form-item label="渠道名称" :must="true" :text="true" v-if="SourceNames.length">
                                    <div class="ui-col-12">
                                        <ui-radio-group
                                            :list="SourceNames"
                                            :value.sync="communication"
                                            valid="AddBusinessChance_communication"
                                        ></ui-radio-group>
                                    </div>
                                </ui-form-item>
                                <ui-form-item label="地区" :must="true">
                                    <ui-cascader
                                        width="323px"
                                        class="margin"
                                        :data="addressData"
                                        v-model="leadsArea"
                                        clearable
                                        filterable
                                        each
                                        @change="handleLeadsArea"
                                        valid="AddBusinessChance_leadsArea"
                                    ></ui-cascader>
                                </ui-form-item>
                                <!-- <ui-form-item label="三级分类" :must="true">
                                    <div class="ui-col-6">
                                        <ui-search-related
                                            base-url="/crm/category/public/findThreeCategory"
                                            width="490"
                                            :clearable="true"
                                            v-model="CategoryContent"
                                            need-history="crm_category_history"
                                            valid="AddBusinessChance_CategoryContent"
                                        ></ui-search-related>
                                    </div>
                                </ui-form-item> -->
                            </template>
                            <template v-else>
                                <ui-form-item label="询价行为" :text="true">{{ inquiryName || '-' }}</ui-form-item>
                                <ui-form-item label="渠道类型" :text="true">{{ sourceName || '-' }}</ui-form-item>
                                <ui-form-item label="渠道名称" :text="true">{{ communicationName || '-' }}</ui-form-item>
                                <ui-form-item label="地区" :text="true">{{ leadsAreaNames.length ? leadsAreaNames.join(' / ') : '-' }}</ui-form-item>
                                <!-- <ui-form-item label="三级分类" :text="true">
                                    <div class="ui-col-10" v-if="CategoryContent">
                                        <p v-for="p in CategoryContent.split('&&')" :key="p" class="show-font">{{ p }}</p>
                                    </div>
                                    <template v-else>-</template>
                                </ui-form-item> -->
                            </template>
                            <ui-form-item label="咨询入口" :text="true">{{ entrancesName || '-' }}</ui-form-item>
                            <ui-form-item label="功能" :text="true">{{ functionsName || '-' }}</ui-form-item>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/businesschanceAdd.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
