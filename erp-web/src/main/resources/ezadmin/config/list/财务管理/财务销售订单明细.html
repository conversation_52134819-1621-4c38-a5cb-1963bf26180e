<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>财务销售订单明细</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="dL2Dygi7wMw" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">订单编号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">可发货时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="SATISFYDELIVERYTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.SATISFY_DELIVERY_TIME = 0, NULL, a.SATISFY_DELIVERY_TIME) / 1000,  '%Y-%m-%d')" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">生效时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="VALIDTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.VALID_TIME = 0, NULL, a.VALID_TIME) / 1000, '%Y-%m-%d')" jdbctype="" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">订单状态</label>
     <div class="layui-input-inline"><xm class=" layui-input list-search-item " type="select" name="STATUS" placeholder="" style="" alias="a" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="EQ" validate_rules="" validate_messages=""></xm>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ADDTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.ADD_TIME = 0, NULL, a.ADD_TIME) / 1000, '%Y-%m-%d')" jdbctype="" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">发货状态</label>
     <div class="layui-input-inline"><xm class=" layui-input list-search-item " type="select" name="DELIVERY_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;} ]" datatype="JSON" oper="" validate_rules="" validate_messages=""></xm>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">普发/直发</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="" name="DELIVERY_DIRECT" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;直发&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;普发&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper="" validate_rules="" validate_messages=""></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">收款状态</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="" name="PAYMENT_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="" validate_rules="" validate_messages=""></select>
     </div>
    </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">确认单审核状态</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="xm-select" name="CONFIRMATION_FORM_AUDIT" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="IN"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">合同审核状态</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="xm-select" name="CONTRACT_VERIFY_STATUS_TD" placeholder="" style="" alias="CASE
		WHEN DATA.CONTRACT_VERIFY_STATUS is null THEN
		'5' ELSE DATA.CONTRACT_VERIFY_STATUS
	END" jdbctype="" datatype="JSON" data='[{"V":"未回传","K":"5"},{"V":"待审核","K":"4"},{"V":"审核中","K":"0"},{"V":"审核通过","K":"1"},{"V":"审核不通过","K":"2"}]' datatype="JSON" oper="IN"></object>
           </div>
       </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="导出后请关闭此窗口" name="导出后请关闭此窗口" url="http://ezadmin.ivedeng.com/ezadmin/list/export-dL2Dygi7wMw" opentype="_BLANK_PARAM" windowname="导出搜索结果" style type="table">导出后请关闭此窗口</button>
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单编号</th>
      <th item_name="SALEORDER_GOODS_ID" name="SALEORDER_GOODS_ID" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单商品ID</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:300px" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
      <th item_name="CUSTOMER_LEVEL" name="CUSTOMER_LEVEL" body="td-select" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select SYS_OPTION_DEFINITION_ID AS K,TITLE as V from T_SYS_OPTION_DEFINITION  a WHERE a.PARENT_ID=11" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="" order="">客户等级</th>
      <th item_name="首次合作时间" name="首次合作时间" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">首次合作时间</th>
      <th item_name="客户注册省份" name="客户注册省份" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户注册省份</th>
      <th item_name="CUSTOMER_TYPE" name="CUSTOMER_TYPE" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;科研医疗&quot;,&quot;K&quot;:&quot;426&quot;},{&quot;V&quot;:&quot;临床医疗&quot;,&quot;K&quot;:&quot;427&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户类型</th>
      <th item_name="CUSTOMER_NATURE" name="CUSTOMER_NATURE" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;分销&quot;,&quot;K&quot;:&quot;465&quot;},{&quot;V&quot;:&quot;终端&quot;,&quot;K&quot;:&quot;466&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户性质</th>
      <th item_name="ADDTIMESTR" name="ADDTIMESTR" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建时间</th>
      <th item_name="创建时归属销售" name="创建时归属销售" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建时归属销售</th>
      <th item_name="创建时归属部门" name="创建时归属部门" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建时归属部门</th>
      <th item_name="当前归属销售" name="当前归属销售" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">当前归属销售</th>
      <th item_name="当前归属部门" name="当前归属部门" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:200px" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">当前归属部门</th>
      <th item_name="VALIDTIMESTR" name="VALIDTIMESTR" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">生效时间</th>
      <th item_name="订单状态" name="订单状态" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单状态</th>
      <th item_name="销售地区省" name="销售地区省" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售地区省</th>
      <th item_name="销售地区市" name="销售地区市" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售地区市</th>
      <th item_name="销售地区区" name="销售地区区" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售地区区</th>
      <th item_name="票种" name="票种" body="td-select" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select SYS_OPTION_DEFINITION_ID AS `K`,TITLE as `V` from T_SYS_OPTION_DEFINITION  a WHERE a.PARENT_ID=428" style="min-width:180px" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">票种</th>
      <th item_name="订单原始总额" name="订单原始总额" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单原始总额</th>
      <th item_name="订单实际金额" name="订单实际金额" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单实际金额</th>
      <th item_name="订单退货金额" name="订单退货金额" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单退货金额</th>
      <th item_name="订单已收款金额" name="订单已收款金额" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单已收款金额</th>
      <th item_name="剩余未还账期金额" name="剩余未还账期金额" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">剩余未还账期金额</th>
      <th item_name="收款状态" name="收款状态" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">收款状态</th>
      <th item_name="开票状态" name="开票状态" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;2&quot;} ]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">开票状态</th>
      <th item_name="采购状态" name="采购状态" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未采购&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分采购&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部采购&quot;,&quot;K&quot;:&quot;2&quot;} ]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购状态</th>
      <th item_name="发货状态" name="发货状态" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;} ]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">发货状态</th>
      <th item_name="收货状态" name="收货状态" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;} ]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">收货状态</th>
      <th item_name="SKU" name="SKU" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订货号</th>
      <th item_name="商品名称" name="商品名称" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:350px" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品名称</th>
      <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">品牌</th>
      <th item_name="MODEL" name="MODEL" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">型号</th>
      <th item_name="MATERIAL_CODE" name="MATERIAL_CODE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">物料编码</th>
      <th item_name="普发直发" name="普发直发" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;直发&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;普发&quot;,&quot;K&quot;:&quot;0&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">普发/直发</th>
      <th item_name="含税销售单价" name="含税销售单价" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">含税销售单价</th>
      <th item_name="含税采购单价" name="含税采购单价" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">含税采购单价</th>
      <th item_name="NUM" name="NUM" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">SKU原始数量</th>
      <th item_name="SKU退货数量" name="SKU退货数量" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">SKU退货数量</th>
      <th item_name="SKU实际数量" name="SKU实际数量" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">SKU实际数量</th>
      <th item_name="实际发货数量" name="实际发货数量" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">实际发货数量</th>
      <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">单位</th>
      <th item_name="销售额含税" name="销售额含税" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售额含税</th>
      <th item_name="采购额含税" name="采购额含税" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购额含税</th>
      <th item_name="实际发货销售额含税" name="实际发货销售额含税" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">实际发货销售额含税</th>
      <th item_name="实际发货采购额含税" name="实际发货采购额含税" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">实际发货采购额含税</th>
      <th item_name="采购订单" name="采购订单" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购订单</th>
      <th item_name="DELIVERY_NUM" name="DELIVERY_NUM" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售单产品发货数量</th>
      <th item_name="BASE_CATEGORY_NAME1" name="BASE_CATEGORY_NAME1" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">贝登一级分类</th>
      <th item_name="BASE_CATEGORY_NAME2" name="BASE_CATEGORY_NAME2" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">贝登二级分类</th>
      <th item_name="BASE_CATEGORY_NAME3" name="BASE_CATEGORY_NAME3" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">贝登三级分类</th>
      <th item_name="商品类型" name="商品类型" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;配件&quot;,&quot;K&quot;:&quot;1008&quot;},{&quot;V&quot;:&quot;设备&quot;,&quot;K&quot;:&quot;316&quot;},{&quot;V&quot;:&quot;耗材&quot;,&quot;K&quot;:&quot;317&quot;},{&quot;V&quot;:&quot;试剂&quot;,&quot;K&quot;:&quot;318&quot;},{&quot;V&quot;:&quot;其他&quot;,&quot;K&quot;:&quot;319&quot;},{&quot;V&quot;:&quot;高值耗材&quot;,&quot;K&quot;:&quot;653&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品类型</th>
      <th item_name="商品级别" name="商品级别" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;其他产品&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;核心产品&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;临时产品&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品级别</th>
      <th item_name="SPU_MANAGER" name="SPU_MANAGER" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">产品经理</th>
      <th item_name="SPU_ASS" name="SPU_ASS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">产品助理</th>
      <th item_name="TERMINAL_PRICE" name="TERMINAL_PRICE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">终端价</th>
      <th item_name="DISTRIBUTION_PRICE" name="DISTRIBUTION_PRICE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">分销价</th>
      <th item_name="SPEC" name="SPEC" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">规格</th>
      <th item_name="不含税销售单价" name="不含税销售单价" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">不含税销售单价</th>
      <th item_name="不含税采购单价" name="不含税采购单价" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">不含税采购单价</th>
      <th item_name="不含税收入" name="不含税收入" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">不含税收入</th>
      <th item_name="不含税成本" name="不含税成本" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">不含税成本</th>
      <th item_name="价格中心成本价" name="价格中心成本价" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">价格中心成本价</th>
      <th item_name="手填成本价" name="手填成本价" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">手填成本价</th>
      <th item_name="订单可发货时间" name="订单可发货时间" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单可发货时间</th>
      <th item_name="销售终端名称" name="销售终端名称" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售终端名称</th>
      <th item_name="商品备注" name="商品备注" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品备注</th>
      <th item_name="销售内部备注" name="销售内部备注" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售内部备注</th>

     <th item_name="CONTRACT_VERIFY_STATUS_TD" name="CONTRACT_VERIFY_STATUS_TD"  type="td-text"   placeholder="" style="" alias="" jdbctype=""   head="th"   edit_flag="" edit_express="" edit_plugin="" empty_show="未上传" >合同审核状态</th>
         <th item_name="CONFIRMATION_FORM_AUDIT" name="CONFIRMATION_FORM_AUDIT" body="td-select" url="" opentype="MODEL" windowname=""  data="[{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">确认单审核状态</th>


     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="" groupby="">SELECT c.TERMINAL_PRICE,
       c.DISTRIBUTION_PRICE,a.CONFIRMATION_FORM_AUDIT,
       c.COST_PRICE 价格中心成本价,
       a.SALEORDER_ID                                                                          ID,
       b.SALEORDER_GOODS_ID,
       a.SALEORDER_NO,
       a.TRADER_NAME,
       a.PAYMENT_STATUS*1 收款状态,
       CC.CUSTOMER_LEVEL ,
       CC.CUSTOMER_TYPE,
       CC.CUSTOMER_NATURE,
       FROM_UNIXTIME(IF(a.ADD_TIME = 0, NULL, a.ADD_TIME) / 1000, '%Y-%m-%d')     AS  addTimeStr,

       CR.USERNAME                                                                         AS 创建时归属销售,

                  (
           SELECT F.ORG_NAME
           FROM T_R_USER_POSIT D
                    LEFT JOIN T_POSITION E ON D.POSITION_ID = E.POSITION_ID
                    LEFT JOIN T_ORGANIZATION F ON E.ORG_ID = F.ORG_ID
           WHERE D.USER_ID = CR.USER_ID
           LIMIT 1
       )                                                                                   AS  创建时归属部门,
       ccc.username 当前归属销售,
       (
           SELECT F.ORG_NAME
           FROM T_R_USER_POSIT D
                    LEFT JOIN T_POSITION E ON D.POSITION_ID = E.POSITION_ID
                    LEFT JOIN T_ORGANIZATION F ON E.ORG_ID = F.ORG_ID
           WHERE D.USER_ID = ccc.USER_ID
           LIMIT 1
       )                                                                                   AS  当前归属部门,
       FROM_UNIXTIME(IF(a.VALID_TIME = 0, NULL, a.VALID_TIME) / 1000, '%Y-%m-%d') AS  validTimeStr,
       a.STATUS*1 订单状态 ,
       R1.REGION_NAME                                                                          REGION_NAME1,
       R2.REGION_NAME                                                                          REGION_NAME2,
       R3.REGION_NAME                                                                          客户注册省份,
       IFNULL(R3.REGION_NAME, IFNULL(R2.REGION_NAME, IFNULL(R1.REGION_NAME, R33.REGION_NAME))) 销售地区省,
       CASE
           WHEN R2.REGION_NAME IS NULL
               THEN ''
           WHEN R3.REGION_NAME IS NULL
               THEN R1.REGION_NAME
           ELSE IFNULL(R2.REGION_NAME, '') END
                                                                                               销售地区市,
       CASE
           WHEN R3.REGION_NAME IS NULL
               THEN ''
           ELSE R1.REGION_NAME END                                                             销售地区区,


       a.INVOICE_TYPE 票种,

       a.TOTAL_AMOUNT 订单原始总额,
       a.REAL_TOTAL_AMOUNT 订单实际金额,
       a.TOTAL_AMOUNT-IFNULL(a.REAL_TOTAL_AMOUNT,0) 订单退货金额,
       round( b.PRICE* IFNULL(b.AFTER_RETURN_NUM, 0) ,2)  订单退货金额2,
       IFNULL(a.REAL_RETURN_AMOUNT, 0)       实退金额,


       IFNULL(a.real_pay_amount, 0)                                                            订单已收款金额
        ,


       IFNULL(DATA.LEFT_AMOUNT_PERIOD, 0)                                                      剩余未还账期金额,
       a.INVOICE_STATUS*1 开票状态,
       a.PURCHASE_STATUS*1 采购状态,
       a.DELIVERY_STATUS*1 发货状态,
       a.ARRIVAL_STATUS*1 收货状态 ,
       b.GOODS_NAME 商品名称,
       b.SKU,
       b.BRAND_NAME,
       b.MODEL,
       c.SPEC,
       c.MATERIAL_CODE,
       b.DELIVERY_DIRECT*1 普发直发,

       b.PRICE 含税销售单价,

       b.BUY_PRICE 含税采购单价,

       round(b.PRICE / (e.COMMENTS + 1), 2)                                                    不含税销售单价,

       round(IFNULL(b.BUY_PRICE, 0) / (e.COMMENTS + 1), 2)                                     不含税采购单价,
       b.NUM,

       IFNULL(b.AFTER_RETURN_NUM, 0)                                                       AS  SKU退货数量,
       b.NUM - IFNULL(b.AFTER_RETURN_NUM, 0)                                               AS  SKU实际数量,
       b.UNIT_NAME,

       (b.PRICE * b.NUM - IFNULL(b.AFTER_RETURN_AMOUNT, 0))                                AS  销售额含税,

       b.BUY_PRICE * (b.NUM - IFNULL(b.AFTER_RETURN_NUM, 0))                                   采购额含税,

       (round(b.PRICE / (e.COMMENTS + 1), 2) * b.NUM - IFNULL(b.AFTER_RETURN_AMOUNT, 0))
                                                                                           AS  不含税收入,

       round(IFNULL(b.BUY_PRICE, 0) / (e.COMMENTS + 1), 2) * b.NUM - ifnull(b.AFTER_RETURN_AMOUNT, 0)
                                                                                               不含税成本,

       b.DELIVERY_NUM,
       C3.BASE_CATEGORY_NAME                                                                   BASE_CATEGORY_NAME3,
       C2.BASE_CATEGORY_NAME                                                                   BASE_CATEGORY_NAME2,
       C1.BASE_CATEGORY_NAME                                                                   BASE_CATEGORY_NAME1,

       P.SPU_TYPE 商品类型,
       P.SPU_LEVEL*1 商品级别,
       AAA.USERNAME                                                                            SPU_MANAGER,
       CCC.USERNAME                                                                            SPU_ASS,
      FROM_UNIXTIME(IF(TRADERDATA.FIRST_TRADER_TIME  = 0, NULL, TRADERDATA.FIRST_TRADER_TIME ) / 1000,
                     '%Y-%m-%d')                                                                       首次合作时间,
       b.REFERENCE_COST_PRICE 手填成本价
        ,
       FROM_UNIXTIME(IF(a.SATISFY_DELIVERY_TIME = 0, NULL, a.SATISFY_DELIVERY_TIME) / 1000,
                     '%Y-%m-%d')                                                  AS  订单可发货时间,
       a.TERMINAL_TRADER_NAME 销售终端名称,
       b.GOODS_COMMENTS 商品备注,
       a.COMMENTS 销售内部备注,

              CASE
		WHEN DATA.CONTRACT_VERIFY_STATUS is null or  DATA.CONTRACT_VERIFY_STATUS='5' THEN
		'未回传'
              when DATA.CONTRACT_VERIFY_STATUS='4' then '待审核'

              when DATA.CONTRACT_VERIFY_STATUS='2' then '审核不通过'
              when DATA.CONTRACT_VERIFY_STATUS='1' then '审核通过'
              when DATA.CONTRACT_VERIFY_STATUS='0' then '审核中'
	END CONTRACT_VERIFY_STATUS_TD,

        b.BUYORDER_NOS AS 采购订单
,b.VIEW_REAL_DELIVERY_NUM 实际发货数量,b.VIEW_REAL_ARRIVAL_NUM 实际收货数量
,round(b.PRICE  , 2) * b.VIEW_REAL_DELIVERY_NUM  实际发货销售额含税 , round(b.BUY_PRICE , 2) * b.VIEW_REAL_DELIVERY_NUM 实际发货采购额含税
FROM T_SALEORDER a
         LEFT JOIN T_R_TRADER_J_USER bbb ON a.TRADER_ID = bbb.TRADER_ID and bbb.TRADER_TYPE=1
         LEFT JOIN T_USER ccc ON bbb.USER_ID = ccc.USER_ID
         LEFT JOIN T_USER CR ON CR.USER_ID = a.CREATOR
         LEFT JOIN T_SALEORDER_GOODS b ON a.SALEORDER_ID = b.SALEORDER_ID
    AND b.IS_DELETE = 0
         LEFT JOIN V_CORE_SKU c ON b.GOODS_ID = c.SKU_ID
         LEFT JOIN V_CORE_SPU P ON c.SPU_ID = P.spu_id
         LEFT JOIN V_BASE_CATEGORY C3 ON P.CATEGORY_ID = C3.BASE_CATEGORY_ID
    AND C3.IS_DELETED = 0
         LEFT JOIN V_BASE_CATEGORY C2 ON C2.BASE_CATEGORY_ID = C3.PARENT_ID
    AND C2.IS_DELETED = 0
         LEFT JOIN V_BASE_CATEGORY C1 ON C1.BASE_CATEGORY_ID = C2.PARENT_ID
    AND C1.IS_DELETED = 0
         LEFT JOIN T_SYS_OPTION_DEFINITION d ON d.SYS_OPTION_DEFINITION_ID = a.PAYMENT_TYPE
         LEFT JOIN T_SYS_OPTION_DEFINITION e ON e.SYS_OPTION_DEFINITION_ID = a.INVOICE_TYPE
         LEFT JOIN T_REGION region3 ON region3.region_id = a.SALES_AREA_ID
         LEFT JOIN T_REGION region2 ON region2.region_id = region3.parent_id AND region2.REGION_ID &gt; 100000
         LEFT JOIN T_REGION region1 ON region1.region_id = region2.parent_id AND region1.REGION_ID &gt; 100000
         LEFT JOIN T_TRADER TRA ON TRA.TRADER_ID = a.TRADER_ID
         LEFT JOIN T_TRADER_CUSTOMER CC ON CC.TRADER_ID = a.TRADER_ID


         LEFT JOIN T_REGION R1 ON R1.region_id = TRA.AREA_ID
         LEFT JOIN T_REGION R2 ON R2.region_id = R1.parent_id AND R2.REGION_ID &gt; 100000
         LEFT JOIN T_REGION R3 ON R3.region_id = R2.parent_id AND R3.REGION_ID &gt; 100000



         LEFT JOIN T_REGION R11 ON R11.region_id = a.TAKE_TRADER_ADDRESS_ID
         LEFT JOIN T_REGION R22 ON R22.region_id = R11.parent_id AND R22.REGION_ID &gt; 100000
         LEFT JOIN T_REGION R33 ON R33.region_id = R22.parent_id AND R33.REGION_ID &gt; 100000


         LEFT JOIN T_USER AAA ON AAA.USER_ID = P.ASSIGNMENT_MANAGER_ID
         LEFT JOIN T_USER CCC ON CCC.USER_ID = P.ASSIGNMENT_ASSISTANT_ID
         LEFT JOIN T_SALEORDER_DATA DATA ON DATA.SALEORDER_ID = a.SALEORDER_ID
        LEFT JOIN T_TRADER_DATA TRADERDATA ON TRADERDATA.TRADER_ID=TRA.TRADER_ID

WHERE a.ORDER_TYPE != 2
  AND a.COMPANY_ID = 1
  AND a.STATUS != 3
  and a.VALID_STATUS = 1 AND b.IS_DELETE = 0</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code">SELECT count(1)

FROM T_SALEORDER a

         LEFT JOIN T_SALEORDER_GOODS b ON a.SALEORDER_ID = b.SALEORDER_ID

         LEFT JOIN T_SALEORDER_DATA DATA ON DATA.SALEORDER_ID = a.SALEORDER_ID


WHERE a.ORDER_TYPE != 2
  AND a.COMPANY_ID = 1
  AND a.STATUS != 3
  and a.VALID_STATUS = 1 AND b.IS_DELETE = 0</pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>