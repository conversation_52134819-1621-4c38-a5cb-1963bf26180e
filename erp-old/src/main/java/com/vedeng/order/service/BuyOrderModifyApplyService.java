package com.vedeng.order.service;

import com.vedeng.authorization.model.User;

import java.util.Map;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface BuyOrderModifyApplyService {


    /**
     *
     * @param variables
     * @param audited
     */
    void processBuyOrderModifyApplyFinished(Map<String, Object> variables, boolean audited,User user);

    /**
     * 采购修改记录
     *
     * @param saleOrderId
     * @param applyId
     * @param validStatus
     */
    void syncModifyApplyInfoToSalesSide(Integer saleOrderId, Integer applyId, Integer validStatus,Boolean isWmsCancel);

    /**
     * 判断新旧采购修改订单
     * @param buyOrderModifyApplyId
     */
    Integer judgeIsNewBuyOrderModifyApply(Integer buyOrderModifyApplyId) throws InterruptedException;

    /**
     * 取消销售单wms出库
     * @param saleOrderNo
     * @return
     */
    boolean cancelOutStorageRequestOfSaleOrder(String saleOrderNo);
}
