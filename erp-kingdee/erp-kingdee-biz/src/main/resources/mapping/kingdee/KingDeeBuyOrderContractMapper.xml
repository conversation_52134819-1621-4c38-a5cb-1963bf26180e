<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeBuyOrderContractMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeBuyOrderContractEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_BUYORDER_CONTRACT-->
    <id column="KING_DEE_BUYORDER_CONTRACT_ID" jdbcType="INTEGER" property="kingDeeBuyOrderContractId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_ID" jdbcType="INTEGER" property="fId" />
    <result column="F_QZOK_OrgId" jdbcType="VARCHAR" property="fQzokOrgid" />
    <result column="F_QZOK_HTH" jdbcType="VARCHAR" property="fQzokHth" />
    <result column="F_QZOK_HTRQ" jdbcType="VARCHAR" property="fQzokHtrq" />
    <result column="F_QZOK_HTJE" jdbcType="VARCHAR" property="fQzokHtje" />
    <result column="F_QZOK_SLL" jdbcType="VARCHAR" property="fQzokSll" />
    <result column="F_QZOK_DDH" jdbcType="VARCHAR" property="fQzokDdh" />
    <result column="FBillNo" jdbcType="VARCHAR" property="FBillNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_BUYORDER_CONTRACT_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, F_ID, F_QZOK_OrgId, F_QZOK_HTH, F_QZOK_HTRQ, F_QZOK_HTJE, F_QZOK_SLL, 
    F_QZOK_DDH,FBillNo
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_BUYORDER_CONTRACT
    where KING_DEE_BUYORDER_CONTRACT_ID = #{kingDeeBuyOrderContractId,jdbcType=INTEGER}
  </select>

  <select id="getFidByDDH" resultType="java.lang.Integer">
    select F_ID from KING_DEE_BUYORDER_CONTRACT where F_QZOK_DDH = #{buyOrderNo,jdbcType=VARCHAR}
  </select>
</mapper>