package com.newtask.util;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * author：wuxu
 * 时间工具类
 */
@Slf4j
public class TimeUtils {

    /**
     * 获取当天还剩多少时间戳
     * @return
     */
    public static long getTime(){
        LocalDateTime midnight = LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        long millSeconds = ChronoUnit.MILLIS.between(LocalDateTime.now(),midnight);
        return millSeconds;
    }

    //获取当前时间 yyyy-mm-dd
    public static String getTimeStr(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(new Date());
        return format;

    }

    //时间戳转Date
    public static Date timeStampToDate(Long time){
        SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStr = format.format(time);
        Date dateTime = null;
        try {
            dateTime = format.parse(timeStr);
        } catch (ParseException e) {
            log.error("【timeStampToDate】处理异常",e);
        }
        return dateTime;
    }

}
