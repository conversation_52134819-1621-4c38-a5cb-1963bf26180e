package com.vedeng.erp.system.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 字典表
 * @date 2022/7/15 15:32
 */
@ExceptionController
@RestController
@RequestMapping("/sysOptionDefinition")
@Slf4j
public class SysOptionDefinitionApi {

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @RequestMapping(value = "/getByParentCode", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> getByParentCode(@RequestParam String parentCode) {
        return R.success(sysOptionDefinitionApiService.getOptionDefinitionListByParentCode(parentCode));
    }

    @RequestMapping(value = "/get/parentId", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> getByParentCode(@RequestParam Integer parentId) {
        return R.success(sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(parentId)));
    }
}