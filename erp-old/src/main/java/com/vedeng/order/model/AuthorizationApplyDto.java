package com.vedeng.order.model;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AuthorizationApplyDto extends AuthorizationApply {
    private Integer authorizationDay;

    private String sjNum;

    private String bjNum;

    private String ddNum;

    private Boolean isCurrentUserCanCheck;


    public String getAddTimeFormatted() {
        return DateUtil.formatDateTime(new Date(super.getAddTime()));
    }
}
