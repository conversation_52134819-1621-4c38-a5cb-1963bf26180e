package com.vedeng.erp.saleorder.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;

import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalCustomerDto;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalDto;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.service.OrderTerminalService;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.system.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 订单终端信息控制器
 * @Date 2023/9/5 15:10
 */
@Slf4j
@Controller
@RequestMapping("/order/terminal")
public class OrderTerminalController extends BaseController {

    @Autowired
    private OrderTerminalService orderTerminalService;

    @Autowired
    private OrderTerminalApiService orderTerminalApiService;

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    /**
     * 搜索终端的弹窗
     *
     * @param scene           区分入口来处理不同的回调方法，1： 销售订单列表(2订单详情页)；0：商机、报价、订单编辑页；
     * @param hasTerminalInfo 是否已维护了终端信息（仅针对销售订单列表使用） 1：已维护 0： 未维护
     * @param businessId      业务单据id（目前仅针对销售订单列表）
     * @return ModelAndView
     */
    @RequestMapping(value = "/dialog")
    @NoNeedAccessAuthorization
    public ModelAndView dealerEditView(Integer hasTerminalInfo, Integer scene, @RequestParam(defaultValue = "0") Integer businessId) {
        ModelAndView view = new ModelAndView("vue/view/orderterminal/terminal_info_search");
        view.addObject("hasTerminalInfo", hasTerminalInfo);
        view.addObject("scene", scene);
        view.addObject("businessId", businessId);
        List<Region> provinceList = regionService.getRegionByParentId(ErpConst.ONE);
        view.addObject("provinceList", provinceList);
        view.addObject("natureOptions", JSON.toJSON(sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(5600))));
        return view;
    }

    /**
     * 选择终端的弹窗
     *
     * @param scene 场景1： 订单详情页
     * @param businessId  业务单据id（目前仅针对销售订单列表）
     * @return
     */
    @RequestMapping(value = "/selectTerminalFrame")
    @NoNeedAccessAuthorization
    public ModelAndView selectTerminalFrame(Integer scene,Integer businessId) {
        ModelAndView view = new ModelAndView("terminal/selectTerminal");
        view.addObject("scene", scene);
        view.addObject("businessId", businessId);
//        List<Region> provinceList = regionService.getRegionByParentId(ErpConst.ONE);
//        view.addObject("provinceList", provinceList);
        view.addObject("natureOptionsList", sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(5600)));
        return view;
    }

    /**
     * 获取终端类型
     * @return
     */
    @RequestMapping(value = "/natureOptionList")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<List<SysOptionDefinitionDto>> selectTerminalFrame() {
        return R.success(sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(5600)));
    }


    /**
     * 搜索终端的弹窗-For销售订单搜索页
     *
     * @param scene           区分入口来处理不同的回调方法，1： 销售订单列表；0：商机、报价、订单编辑页；2订单详情页
     * @param hasTerminalInfo 是否已维护了终端信息（仅针对销售订单列表使用） 1：已维护 0： 未维护
     * @param businessId      业务单据id（目前仅针对销售订单列表）
     * @return ModelAndView
     */
    @RequestMapping(value = "/dialogForSaleOrderDetail")
    @NoNeedAccessAuthorization
    public ModelAndView dialogForSaleOrderDetail(Integer hasTerminalInfo, Integer scene, @RequestParam(defaultValue = "0") Integer businessId) {
        ModelAndView view = new ModelAndView("vue/view/orderterminal/terminal_info_search2");
        view.addObject("hasTerminalInfo", hasTerminalInfo);
        view.addObject("scene", scene);
        view.addObject("businessId", businessId);
        return view;
    }

    /**
     * 搜索终端信息（只从大数据终端库）
     *
     * @param terminalTraderName 輸入的終端名稱
     * @param pageSize           每页条数
     * @param pageNum            当前页
     * @return 分页的OrderTerminalDto
     */
    @RequestMapping(value = "/searchOneData")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<PageInfo> searchOneDataTerminal(String terminalTraderName, Integer pageSize, Integer pageNum) {
        return R.success(orderTerminalService.getOneDataTerminalInfo(terminalTraderName, pageSize, pageNum));
    }


    /**
     * 搜索终端信息（天眼查）
     *
     * @param terminalTraderName 輸入的終端名稱
     * @param pageSize           每页条数
     * @param pageNum            当前页
     * @return 分页的OrderTerminalDto
     */
    @RequestMapping(value = "/searchTyc")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<PageInfo> searchTycTerminal(String terminalTraderName, @RequestParam(name="pageSize" ,defaultValue = "20")  Integer pageSize, @RequestParam(name="pageNum" ,defaultValue = "1") Integer pageNum) {
        return R.success(orderTerminalService.getTycTerminalInfo(terminalTraderName, pageSize, pageNum));
    }

    /**
     * 根据业务类型和业务单据id查询终端信息
     *
     * @param businessId   单据id
     * @param businessType 业务类型
     * @return OrderTerminalDto
     */
    @RequestMapping(value = "/businessId")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<OrderTerminalDto> getBusinessIdAndType(Integer businessId, Integer businessType) {
        return R.success(orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(businessId, businessType));
    }

    /**
     * 保存销售单终端信息
     *
     * @param saleOrderTerminalDto SaleOrderTerminalDto
     * @return R
     */
    @RequestMapping(value = "/add")
    @NoNeedAccessAuthorization
    @ResponseBody
    @NoRepeatSubmit
    public R<?> saveSaleOrderTerminal(@RequestBody SaleOrderTerminalDto saleOrderTerminalDto, HttpServletRequest request) {
        try{
            User user = getSessionUser(request);
            saleOrderTerminalDto.setCreator(user!=null?user.getUserId():0);
            saleOrderTerminalDto.setCreatorName(user!=null?user.getUsername():"");
            saleOrderTerminalDto.setUpdater(user!=null?user.getUserId():0);
            saleOrderTerminalDto.setUpdaterName(user!=null?user.getUsername():"");
            orderTerminalService.saveSaleOrderTerminal(saleOrderTerminalDto);
            return R.success();
        }catch (Exception e){
            log.error("保存终端异常",e);
            return R.error("保存终端失败");
        }


    }

    @RequestMapping(value = "/querySaleOrderTerminal")
    @NoNeedAccessAuthorization
    @ResponseBody
    @NoRepeatSubmit
    public R<Map<String,Object>> querySaleOrderTerminal(Integer id) {
        try{
            Map<String,Object> result = new HashMap<>();
            List<SysOptionDefinitionDto> optionDefinitionDtos  = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(5600));
            result.put("naturnList",optionDefinitionDtos);
            if(id != null){
                SaleOrderTerminalDto terminalDto  = orderTerminalService.findSaleOrderTerminalById(id);
                result.put("terminal",terminalDto);
            }
            return R.success(result);
        }catch (Exception e){
            log.error("查询终端信息",e);
            return R.error("查询终端信息失败");
        }
    }


    /**
     * 新版保存销售单终端信息-前端独立页面
     * @param saleOrderTerminalDto
     * @param request
     * @return
     */
    @RequestMapping(value = "/addNew")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<?> saveSaleOrderTerminalNew(@RequestBody SaleOrderTerminalDto saleOrderTerminalDto, HttpServletRequest request) {
        try{
            User user = getSessionUser(request);
            saleOrderTerminalDto.setCreator(user!=null?user.getUserId():0);
            saleOrderTerminalDto.setCreatorName(user!=null?user.getUsername():"");
            saleOrderTerminalDto.setUpdater(user!=null?user.getUserId():0);
            saleOrderTerminalDto.setUpdaterName(user!=null?user.getUsername():"");
            int id  = orderTerminalService.saveSaleOrderTerminalForNew(saleOrderTerminalDto);
            return R.success(id);
        }catch (ServiceException e){
            return R.error(e.getMessage());
        }
        catch (Exception e){
            log.error("保存终端异常",e);
            return R.error("保存终端失败");
        }
    }

    /**
     * 查询销售订单对应的终端列表
     * @param saleOrderId
     * @return
     */
    @RequestMapping(value = "/listForSaleOrder")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<?> listForSaleOrder(Integer saleOrderId) {
        try{
            List<SaleOrderTerminalDto> dtoList = orderTerminalService.findSaleOrderTerminalBySaleOrderId(saleOrderId);
            return R.success(dtoList);
        }
        catch (Exception e){
            log.error("查询终端异常",e);
            return R.error("查询终端异常");
        }
    }

    @RequestMapping(value = "/deleteForSaleOrder")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<?> deleteForSaleOrder(Integer id) {
        try{
            CurrentUser user = CurrentUser.getCurrentUser();
            boolean result = orderTerminalService.deleteTerminalForSaleOrderId(id,user);
            if(result){
                return R.success("删除成功");
            }
            return R.error("删除失败");
        }
        catch (Exception e){
            log.error("查询终端异常",e);
            return R.error("查询终端异常");
        }
    }

    /**
     * 根据销售单id查询终端信息
     *
     * @param saleOrderId 销售单id
     * @return SaleOrderTerminalDto
     */
    @RequestMapping(value = "/queryBySaleOrderId")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<SaleOrderTerminalDto> queryBySaleOrderId(Integer saleOrderId) {
        return R.success(orderTerminalService.getBySaleOrderId(saleOrderId));
    }

    /**
     * 根据销售单id查询终端性质
     */
    @RequestMapping(value = "/getNatureBySaleOrderId")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<Integer> getNatureBySaleOrderId(Integer saleOrderId) {
        return R.success(orderTerminalService.getNatureBySaleOrderId(saleOrderId));
    }

    /**
     * 销售订单列表 备注客户弹窗
     *
     * @param hasCustomerInfo 是否已维护过备注客户信息 0：否， 1：是
     * @param businessId      销售单id
     * @return ModelAndView
     */
    @RequestMapping(value = "/customer/page")
    public ModelAndView searchCustomerPage(Integer businessId, Integer hasCustomerInfo) {
        ModelAndView view = new ModelAndView("vue/view/orderterminal/customer_search");
        view.addObject("hasCustomerInfo", hasCustomerInfo);
        view.addObject("businessId", businessId);
        return view;
    }

    /**
     * 根据销售单id查询终端备注客户信息
     *
     * @param saleOrderId 销售单id
     * @return SaleOrderTerminalCustomerDto
     */
    @RequestMapping(value = "/customer/saleOrderId")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<SaleOrderTerminalCustomerDto> getSaleOrderTerminalCustomerByOrderId(Integer saleOrderId) {
        return R.success(orderTerminalService.getSaleOrderTerminalCustomerByOrderId(saleOrderId));
    }

    /**
     * 保存销售单终端客户信息
     *
     * @param terminalCustomerDto SaleOrderTerminalCustomerDto
     * @return R
     */
    @NoRepeatSubmit
    @RequestMapping(value = "/customer/save")
    @ResponseBody
    public R<?> saveSaleOrderTerminalCustomer(@RequestBody SaleOrderTerminalCustomerDto terminalCustomerDto) {
        orderTerminalService.saveSaleOrderTerminalCustomer(terminalCustomerDto);
        return R.success();
    }
}