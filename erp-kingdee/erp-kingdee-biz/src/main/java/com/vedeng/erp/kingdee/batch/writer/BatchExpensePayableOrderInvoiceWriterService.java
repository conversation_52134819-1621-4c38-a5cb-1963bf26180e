package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchPayExpensesDto;
import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeeInPutFeePlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeInPutFeeSpecialInvoiceApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购应付单以及票推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchExpensePayableOrderInvoiceWriterService extends BaseWriter<BatchPayExpensesDto> {

    @Autowired
    private KingDeeInPutFeeSpecialInvoiceApiService kingDeeInPutFeeSpecialInvoiceApiService;

    @Autowired
    private KingDeeInPutFeePlainInvoiceApiService kingDeeInPutFeePlainInvoiceApiService;


    @Override
    public void doWrite(BatchPayExpensesDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("BatchExpensePayableOrderInvoiceWriterService:采购费用票推送{}", JSON.toJSONString(item));


        // 专票推送
        if (item.getInPutFeeSpecialInvoiceDto() != null) {
            this.specialWrite(item);
        }

        // 普票推送
        if (item.getInPutFeePlainInvoiceDto() != null) {
            this.plainWrite(item);
        }

    }


    /**
     * 专票
     */
    private void specialWrite(BatchPayExpensesDto item) {
        InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto = item.getInPutFeeSpecialInvoiceDto();
        log.info("采购费用专票入参-->{}" , JSON.toJSONString(inPutFeeSpecialInvoiceDto));
        inPutFeeSpecialInvoiceDto.setKingDeeBizEnums(KingDeeBizEnums.saveInPutFeeSpecialInvoice);
        kingDeeInPutFeeSpecialInvoiceApiService.register(inPutFeeSpecialInvoiceDto,true);

    }


    /**
     * 普票
     */
    private void plainWrite(BatchPayExpensesDto item) {
        InPutFeePlainInvoiceDto dto = item.getInPutFeePlainInvoiceDto();
        log.info("采购费用普票入参-->{}" , JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveInPutFeePlainInvoice);
        kingDeeInPutFeePlainInvoiceApiService.register(dto,true);
    }
}
