<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.mapper.BrandMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.Brand" >
    <!--          -->
    <id column="BRAND_ID" property="brandId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="BRAND_NATURE" property="brandNature" jdbcType="BIT" />
    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
    <result column="BRAND_NAME_EN" property="brandNameEn" jdbcType="VARCHAR" />
    <result column="MANUFACTURER" property="manufacturer" jdbcType="VARCHAR" />
    <result column="BRAND_WEBSITE" property="brandWebsite" jdbcType="VARCHAR" />
    <result column="OWNER" property="owner" jdbcType="VARCHAR" />
    <result column="LOGO_DOMAIN" property="logoDomain" jdbcType="VARCHAR" />
    <result column="LOGO_URI" property="logoUri" jdbcType="VARCHAR" />
    <result column="SORT" property="sort" jdbcType="INTEGER" />
    <result column="INITIAL_CN" property="initialCn" jdbcType="VARCHAR" />
    <result column="INITIAL_EN" property="initialEn" jdbcType="VARCHAR" />
    <result column="SOURCE" property="source" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="OSS_RESOURCE_ID" property="ossResourceId" jdbcType="VARCHAR" />
    <result column="ORIGINAL_FILEPATH" property="originalFilepath" jdbcType="VARCHAR" />
    <result column="SYN_SUCCESS" property="synSuccess" jdbcType="INTEGER" />
    <result column="COST_TIME" property="costTime" jdbcType="BIGINT" />
    <result column="BRAND_NAME_CN" property="brandNameCn" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.vedeng.goods.domain.entity.Brand" extends="BaseResultMap" >
    <!--          -->
    <result column="DESCRIPTION" property="description" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    BRAND_ID, COMPANY_ID, BRAND_NATURE, BRAND_NAME, BRAND_NAME_EN, MANUFACTURER, BRAND_WEBSITE, 
    OWNER, LOGO_DOMAIN, LOGO_URI, SORT, INITIAL_CN, INITIAL_EN, SOURCE, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER, IS_DELETE, COMMENTS, COMPANY_NAME, FILE_NAME, OSS_RESOURCE_ID, 
    ORIGINAL_FILEPATH, SYN_SUCCESS, COST_TIME, BRAND_NAME_CN
  </sql>
  <sql id="Blob_Column_List" >
    <!--          -->
    DESCRIPTION
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_BRAND
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_BRAND
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.domain.entity.Brand" >
    <!--          -->
    insert into T_BRAND (BRAND_ID, COMPANY_ID, BRAND_NATURE, 
      BRAND_NAME, BRAND_NAME_EN, MANUFACTURER, 
      BRAND_WEBSITE, OWNER, LOGO_DOMAIN, 
      LOGO_URI, SORT, INITIAL_CN, 
      INITIAL_EN, SOURCE, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER, 
      IS_DELETE, COMMENTS, COMPANY_NAME, 
      FILE_NAME, OSS_RESOURCE_ID, ORIGINAL_FILEPATH, 
      SYN_SUCCESS, COST_TIME, BRAND_NAME_CN, 
      DESCRIPTION)
    values (#{brandId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{brandNature,jdbcType=BIT}, 
      #{brandName,jdbcType=VARCHAR}, #{brandNameEn,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{brandWebsite,jdbcType=VARCHAR}, #{owner,jdbcType=VARCHAR}, #{logoDomain,jdbcType=VARCHAR}, 
      #{logoUri,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{initialCn,jdbcType=VARCHAR}, 
      #{initialEn,jdbcType=VARCHAR}, #{source,jdbcType=BIT}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=BIT}, #{comments,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{ossResourceId,jdbcType=VARCHAR}, #{originalFilepath,jdbcType=VARCHAR}, 
      #{synSuccess,jdbcType=INTEGER}, #{costTime,jdbcType=BIGINT}, #{brandNameCn,jdbcType=VARCHAR}, 
      #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.domain.entity.Brand" >
    <!--          -->
    insert into T_BRAND
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="brandId != null" >
        BRAND_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="brandNature != null" >
        BRAND_NATURE,
      </if>
      <if test="brandName != null" >
        BRAND_NAME,
      </if>
      <if test="brandNameEn != null" >
        BRAND_NAME_EN,
      </if>
      <if test="manufacturer != null" >
        MANUFACTURER,
      </if>
      <if test="brandWebsite != null" >
        BRAND_WEBSITE,
      </if>
      <if test="owner != null" >
        OWNER,
      </if>
      <if test="logoDomain != null" >
        LOGO_DOMAIN,
      </if>
      <if test="logoUri != null" >
        LOGO_URI,
      </if>
      <if test="sort != null" >
        SORT,
      </if>
      <if test="initialCn != null" >
        INITIAL_CN,
      </if>
      <if test="initialEn != null" >
        INITIAL_EN,
      </if>
      <if test="source != null" >
        SOURCE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="companyName != null" >
        COMPANY_NAME,
      </if>
      <if test="fileName != null" >
        FILE_NAME,
      </if>
      <if test="ossResourceId != null" >
        OSS_RESOURCE_ID,
      </if>
      <if test="originalFilepath != null" >
        ORIGINAL_FILEPATH,
      </if>
      <if test="synSuccess != null" >
        SYN_SUCCESS,
      </if>
      <if test="costTime != null" >
        COST_TIME,
      </if>
      <if test="brandNameCn != null" >
        BRAND_NAME_CN,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="brandId != null" >
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="brandNature != null" >
        #{brandNature,jdbcType=BIT},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandNameEn != null" >
        #{brandNameEn,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null" >
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="brandWebsite != null" >
        #{brandWebsite,jdbcType=VARCHAR},
      </if>
      <if test="owner != null" >
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="logoDomain != null" >
        #{logoDomain,jdbcType=VARCHAR},
      </if>
      <if test="logoUri != null" >
        #{logoUri,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="initialCn != null" >
        #{initialCn,jdbcType=VARCHAR},
      </if>
      <if test="initialEn != null" >
        #{initialEn,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        #{source,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null" >
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="ossResourceId != null" >
        #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null" >
        #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="synSuccess != null" >
        #{synSuccess,jdbcType=INTEGER},
      </if>
      <if test="costTime != null" >
        #{costTime,jdbcType=BIGINT},
      </if>
      <if test="brandNameCn != null" >
        #{brandNameCn,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.Brand" >
    <!--          -->
    update T_BRAND
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="brandNature != null" >
        BRAND_NATURE = #{brandNature,jdbcType=BIT},
      </if>
      <if test="brandName != null" >
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandNameEn != null" >
        BRAND_NAME_EN = #{brandNameEn,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null" >
        MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="brandWebsite != null" >
        BRAND_WEBSITE = #{brandWebsite,jdbcType=VARCHAR},
      </if>
      <if test="owner != null" >
        OWNER = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="logoDomain != null" >
        LOGO_DOMAIN = #{logoDomain,jdbcType=VARCHAR},
      </if>
      <if test="logoUri != null" >
        LOGO_URI = #{logoUri,jdbcType=VARCHAR},
      </if>
      <if test="sort != null" >
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="initialCn != null" >
        INITIAL_CN = #{initialCn,jdbcType=VARCHAR},
      </if>
      <if test="initialEn != null" >
        INITIAL_EN = #{initialEn,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        SOURCE = #{source,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null" >
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="ossResourceId != null" >
        OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalFilepath != null" >
        ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
      </if>
      <if test="synSuccess != null" >
        SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
      </if>
      <if test="costTime != null" >
        COST_TIME = #{costTime,jdbcType=BIGINT},
      </if>
      <if test="brandNameCn != null" >
        BRAND_NAME_CN = #{brandNameCn,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.goods.domain.entity.Brand" >
    <!--          -->
    update T_BRAND
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      BRAND_NATURE = #{brandNature,jdbcType=BIT},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      BRAND_NAME_EN = #{brandNameEn,jdbcType=VARCHAR},
      MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      BRAND_WEBSITE = #{brandWebsite,jdbcType=VARCHAR},
      OWNER = #{owner,jdbcType=VARCHAR},
      LOGO_DOMAIN = #{logoDomain,jdbcType=VARCHAR},
      LOGO_URI = #{logoUri,jdbcType=VARCHAR},
      SORT = #{sort,jdbcType=INTEGER},
      INITIAL_CN = #{initialCn,jdbcType=VARCHAR},
      INITIAL_EN = #{initialEn,jdbcType=VARCHAR},
      SOURCE = #{source,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
      SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
      COST_TIME = #{costTime,jdbcType=BIGINT},
      BRAND_NAME_CN = #{brandNameCn,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=LONGVARCHAR}
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.Brand" >
    <!--          -->
    update T_BRAND
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      BRAND_NATURE = #{brandNature,jdbcType=BIT},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      BRAND_NAME_EN = #{brandNameEn,jdbcType=VARCHAR},
      MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      BRAND_WEBSITE = #{brandWebsite,jdbcType=VARCHAR},
      OWNER = #{owner,jdbcType=VARCHAR},
      LOGO_DOMAIN = #{logoDomain,jdbcType=VARCHAR},
      LOGO_URI = #{logoUri,jdbcType=VARCHAR},
      SORT = #{sort,jdbcType=INTEGER},
      INITIAL_CN = #{initialCn,jdbcType=VARCHAR},
      INITIAL_EN = #{initialEn,jdbcType=VARCHAR},
      SOURCE = #{source,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
      ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
      SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
      COST_TIME = #{costTime,jdbcType=BIGINT},
      BRAND_NAME_CN = #{brandNameCn,jdbcType=VARCHAR}
    where BRAND_ID = #{brandId,jdbcType=INTEGER}
  </update>
  <select id="queryBrandNameById" resultType="java.lang.String">
    SELECT BRAND_NAME
    FROM T_BRAND
    WHERE BRAND_ID = #{brandId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-08-16-->
  <select id="selectByBrandNameLike" resultType="com.vedeng.goods.dto.BrandFrontDto">
        select
        BRAND_ID, BRAND_NAME
        from T_BRAND
        where BRAND_NAME like concat('%',#{likeBrandName,jdbcType=VARCHAR},'%') and IS_DELETE = 0 and COMPANY_ID = 1
        limit #{limit,jdbcType=INTEGER}
    </select>

  <select id="selectByBrandIds" resultType="com.vedeng.goods.dto.BrandFrontDto">
    select
    BRAND_ID, BRAND_NAME
    from T_BRAND
    where BRAND_ID in
    <foreach collection="lists" item="iitem" open="(" separator="," close=")">
      #{iitem,jdbcType=INTEGER}
    </foreach>
    and IS_DELETE = 0

  </select>
  <select id="getByBrandIdList" resultType="com.vedeng.goods.dto.BrandFrontDto">
    select
      BRAND_ID, BRAND_NAME
    from T_BRAND
    where BRAND_ID in
    <foreach item="brandId" index="index" collection="brandIdList" open="(" separator="," close=")">
      #{brandId,jdbcType=INTEGER}
    </foreach>

  </select>
  <select id="queryAllBrand" resultType="com.vedeng.goods.dto.BrandFrontDto">
    select
    BRAND_ID, BRAND_NAME
    from T_BRAND
    where IS_DELETE = 0
limit 2000
  </select>
</mapper>