package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 付款退款单
 *
 * <AUTHOR>
 */

@Getter
@Setter
@WriteBackField(needBackField = {"FID"})
public class KingDeePayRefundBillDto extends KingDeeMqBaseDto {

    /**
     * ID
     */
    private Integer id;

    /**
     * 单据内码
     */
    @WriteBackField
    @KingDeeID
    private String fId;

    /**
     * 单据编号
     */
    @BusinessID
    private String fBillNo;

    /**
     * 单据类型id
     */
    private String fBillTypeId;

    /**
     * 单据日期
     */
    private String fDate;

    /**
     * 往来单位类型
     */
    private String fContactUnitType;

    /**
     * 往来单位
     */
    private String fContactUnit;

    /**
     * 业务类型
     */
    private String fBusinessType;

    /**
     * 付款单位类型
     */
    private String fPayUnitType;

    /**
     * 结算组织
     */
    private String fSettleOrgId;

    /**
     * 采购组织
     */
    private String fPurchaseOrgId;

    /**
     * 付款组织
     */
    private String fPayOrgId;

    /**
     * 是否转款
     */
    private String fQzokSfzk;

    /**
     * 银行流水号
     */
    private String fQzokLsh;

    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtid;

    /**
     * "往来单位" 所选择资金流水的订单单号
     * T_CAPITAL_BILL_DETAIL.ORDER_NO（订单单号）
     */
    private String FQzokPzgsywdh;

    /**
     * 明细行
     */
    List<KingDeePayRefundEntryDto> frefundbillentry;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.PAY_REFUND;
    }

    public KingDeePayRefundBillDto() {
        this.fBillTypeId = "FKTKDLX01_SYS";
        this.fSettleOrgId = "101";
        this.fPurchaseOrgId = "101";
        this.fPayOrgId = "101";
        this.fBusinessType = "2";
        this.fQzokSfzk = "2";
    }
}
