package com.vedeng.api.standard.adapter.peerlist.dto;

import com.vedeng.api.standard.core.exception.ApiStandardException;
import lombok.Data;

/**
 * 查询直发同行单请求DTO
 */
@Data
public class PeerListQueryRequest {

    /**
     * 采购单ID
     */
    private Integer buyorderId;

    /**
     * 验证请求参数
     */
    public void validate() {
        if (buyorderId == null || buyorderId <= 0) {
            throw ApiStandardException.dataConversionError("采购单ID不能为空且必须大于0");
        }
    }
} 
