package com.vedeng.erp.saleorder.controller;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.ezadmin.common.utils.Utils;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserDetailMapper;
import com.vedeng.authorization.model.Company;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.model.UserDetail;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.DigitToChineseUppercaseNumberUtils;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalDto;
import com.vedeng.erp.saleorder.dto.SaleorderAdditionalClauseDto;
import com.vedeng.erp.saleorder.service.OrderAdditionalClauseApiService;
import com.vedeng.erp.saleorder.service.OrderAdditionalClauseService;
import com.vedeng.erp.saleorder.service.OrderTerminalService;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.finance.service.TraderAccountPeriodApplyService;
import com.vedeng.goods.dao.BrandGenerateMapper;
import com.vedeng.goods.dto.RegistrationNumberSpuDto;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.dto.CoreSpuBaseDTO;
import com.vedeng.goods.query.RegistrationNumberSpuQuery;
import com.vedeng.goods.service.*;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.LogisticsService;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.controller.SaleorderController;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.BussinessChanceService;
import com.vedeng.order.service.QuoteService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.order.service.impl.RemarkComponentServiceImpl;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.*;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.service.TraderCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 订单流项目 - 销售订单 - 打印合同模板
 * @since 2021/10/8
 */
@Slf4j
@Controller
@RequestMapping("/orderstream/saleorder")
public class PrintContractTemplateController extends BaseController {

    @Autowired
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    @Qualifier("historyService")
    protected HistoryService historyService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("saleorderService")
    protected SaleorderService saleorderService;

    @Autowired
    @Qualifier("expressService")
    protected ExpressService expressService;

    @Autowired
    @Qualifier("quoteService")
    protected QuoteService quoteService;

    @Autowired
    @Qualifier("bussinessChanceService")
    protected BussinessChanceService bussinessChanceService;

    @Autowired
    @Qualifier("orgService")
    protected OrgService orgService;

    @Autowired
    @Qualifier("userService")
    protected UserService userService;

    @Autowired
    @Qualifier("traderCustomerService")
    protected TraderCustomerService traderCustomerService;

    @Autowired
    @Qualifier("goodsService")
    protected GoodsService goodsService;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    @Qualifier("regionService")
    protected RegionService regionService;

    @Autowired
    @Qualifier("tagService")
    protected TagService tagService;

    @Autowired
    @Qualifier("logisticsService")
    protected LogisticsService logisticsService;

    @Autowired
    @Qualifier("ftpUtilService")
    protected FtpUtilService ftpUtilService;

    @Autowired
    @Qualifier("invoiceService")
    protected InvoiceService invoiceService;

    @Resource
    protected AfterSalesService afterSalesOrderService;

    @Autowired
    @Qualifier("capitalBillService")
    protected CapitalBillService capitalBillService;

    @Autowired
    @Qualifier("accountPeriodService")
    protected TraderAccountPeriodApplyService accountPeriodService;

    @Autowired
    @Qualifier("warehouseOutService")
    protected WarehouseOutService warehouseOutService;

    @Autowired
    @Qualifier("verifiesRecordService")
    protected VerifiesRecordService verifiesRecordService;

    @Autowired
    @Qualifier("userDetailMapper")
    protected UserDetailMapper userDetailMapper;

    @Autowired
    @Qualifier("goodsChannelPriceService")
    protected GoodsChannelPriceService goodsChannelPriceService;

    @Autowired
    @Qualifier("goodsSettlementPriceService")
    protected GoodsSettlementPriceService goodsSettlementPriceService;

    @Autowired
    @Qualifier("vedengSoapService")
    protected VedengSoapService vedengSoapService;

    @Autowired
    @Qualifier("companyService")
    protected CompanyService companyService;

    @Autowired
    @Qualifier("categoryService")
    protected CategoryService categoryService;

    @Resource
    protected CategoryAttributeService categoryAttributeService;

    @Autowired
    @Qualifier("roleService")
    protected RoleService roleService;

    @Autowired
    @Qualifier("paramsConfigValueService")
    protected ParamsConfigValueService paramsConfigValueService;

    @Autowired
    @Qualifier("hcSaleorderService")
    protected HcSaleorderService hcSaleorderService;

    @Value("${api_url}")
    protected String apiUrl;

    @Autowired
    @Qualifier("warehouseStockService")
    protected WarehouseStockService warehouseStockService;

    @Autowired
    @Qualifier("webAccountMapper")
    protected WebAccountMapper webAccountMapper;

    @Value("${hc.order.ownerids}")
    private String ownerIds;

    @Value("${retention_money_uptime}")
    private Long retentionMoneyUptime;

    @Value("${GE_TRADER_SKU}")
    protected String geTraderSku;

    @Autowired
    VerifiesInfoMapper verifiesInfoMapper;

    @Autowired
    BrandGenerateMapper brandGenerateMapper;

    @Autowired
    RegistrationNumberApiService registrationNumberApiService;


    @Value("${deadlineOfAddAutoCheckedComment}")
    private Long deadlineOfAddAutoCheckedComment;

    @Autowired
    private BaseGoodsService baseGoodsService;

    @Autowired
    protected RemarkComponentServiceImpl remarkComponentService;

    @Autowired
    protected UnitService unitService;
    @Autowired
    private OrderTerminalService orderTerminalService;

    private static final String ADDITIONAL_CLAUSE_OF_SALE_CONTRACT = "%s产品为非医疗器械，不按照《医疗器械监督管理条例》的规定进行管理。";
    private static final String ADDITIONAL_CLAUSE_PLACEHOLDER = "无";


    @Value("${special_sku_list_in_contract}")
    private String specialSkuListInContract;
    @Autowired
    private OrderAdditionalClauseApiService orderAdditionalClauseApiService;

    /**
     * {重构}打印销售合同模板
     *
     * @param request
     * @param saleorder
     * @param autoGenerate
     * @return
     * @see SaleorderController#printOrder(javax.servlet.http.HttpServletRequest, com.vedeng.order.model.Saleorder, java.lang.Boolean)
     */
    @ResponseBody
    @RequestMapping("/contract_template/print")
    @NoNeedAccessAuthorization
    public ModelAndView printContractTemplate(HttpServletRequest request,
                                              Saleorder saleorder,
                                              @RequestParam(required = false) Boolean noStamp,
                                              @RequestParam(required = false) Boolean autoGenerate,
                                              ModelAndView mv) {
        mv.addObject("noStamp",noStamp);
        User currentLoginUser = getSessionUser(request);
        saleorder.setCompanyId(currentLoginUser == null ? 1 : currentLoginUser.getCompanyId());
        logger.info("{重构}打印销售单合同入参：saleorder:{}", JSON.toJSONString(saleorder));
        // 获取销售单详情便于生成合同相关信息
        SaleorderVo saleorderVo = saleorderService.getOrderInfo4PrintContractTemplate(saleorder);
       // saleorderVo.setAdditionalClause(StringUtil.isBlank(saleorderVo.getAdditionalClause()) ? ADDITIONAL_CLAUSE_PLACEHOLDER : saleorderVo.getAdditionalClause());
        //附加条款（新）
        SaleorderAdditionalClauseDto queryDto=new SaleorderAdditionalClauseDto();
        queryDto.setSaleorderId(saleorderVo.getSaleorderId());
        SaleorderAdditionalClauseDto result=orderAdditionalClauseApiService.getSaleorderAdditionalClauseByOrderId(queryDto);
        saleorderVo.setAdditionalClause(Utils.trimEmptyDefault(result.getAdditionalClauseFinalShow(),"1、无"));
        logger.info("{重构}打印销售单合同查询结果：saleorderVo:{}",JSON.toJSONString(saleorderVo));
        // 获取销售人员信息 traderType：1客户、2供应商
        User salesUser = userService.getUserByTraderId(saleorderVo.getTraderId(), 1);
        UserDetail userDetail = userDetailMapper.getUserDetail(salesUser.getUserId());

        // 根据userId查询用户信息
        String username = "";
        if (!NumberUtil.isZeroOrNull(saleorderVo.getUserId())) {
            User saleUser = userService.getUserById(saleorderVo.getUserId());
            username = (saleUser == null) ? "" : saleUser.getUsername();
        }

        // 获取订单下的产品信息
        Saleorder saleorderParam = new Saleorder();
        saleorderParam.setSaleorderId(saleorder.getSaleorderId());
        List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(saleorderParam);
        logger.info("{重构}打印销售单合同查询结果：saleorderGoodsList:{}",JSON.toJSONString(saleorderGoodsList));
        // 销售合同中管是否包含医疗器械类型的SKU
        mv.addObject("apparatusType", 0);
        for (SaleorderGoods saleOrderGood : saleorderGoodsList) {
            logger.info("{重构}打印销售单合同查询结果：saleOrderGood:{}",JSON.toJSONString(saleOrderGood));
            if (StringUtil.isEmpty(saleOrderGood.getSku())) {
                logger.info("{重构}打印销售单合同查询结果：sku为空");
                continue;
            }
            CoreSpuGenerate spu = vGoodsService.findSpuInfoBySkuNo(saleOrderGood.getSku());
            if (spu == null) {
                logger.info("{重构}打印销售单合同查询结果：spu为空");
                continue;
            }
            CoreSpuBaseDTO coreSpuDto = baseGoodsService.selectSpuBaseById(spu.getSpuId());
            // 注册证号不为null表示为医疗机械
            if (StringUtils.isNotBlank(coreSpuDto.getRegistrationNumber())) {
                logger.info("{重构}打印销售单合同查询结果：医疗器械");
                mv.addObject("apparatusType", 1);
                // 补充注册证号和生产厂商
                completeRegistraAndManufacture(saleOrderGood,spu.getSpuId());
                break;
            }
        }

        int goodsLength = saleorderGoodsList.size();
        //

//        // ERP_2020_LV_98 新增条款文字“XXX产品为非医疗器械，不按照《医疗器械监督管理条例》的规定进行管理
//        if (CollectionUtils.isNotEmpty(saleorderGoodsList)) {
//
//            Map<Integer, String> goodsIdAndGoodsNamMap = new LinkedHashMap<>();
//            saleorderGoodsList.forEach(item -> goodsIdAndGoodsNamMap.put(item.getGoodsId(), item.getGoodsName()));
//            final List<Integer> skuIdList = vGoodsService.listSkuNameForSaleContract(goodsIdAndGoodsNamMap.keySet());
//
//            List<String> skuNameList = new ArrayList<>();
//            for (Integer skuId : skuIdList) {
//                String goodsName = goodsIdAndGoodsNamMap.get(skuId);
//                if (StringUtils.isNotEmpty(goodsName)) {
//                    skuNameList.add(goodsName);
//                }
//            }
//
//            if (!skuNameList.isEmpty()) {
//                final String additionClause = String.format(ADDITIONAL_CLAUSE_OF_SALE_CONTRACT,
//                        String.join(ErpConst.Symbol.COMMA_IN_CN, skuNameList));
//                if (ADDITIONAL_CLAUSE_PLACEHOLDER.equals(saleorderVo.getAdditionalClause())) {
//                    saleorderVo.setAdditionalClause(additionClause);
//                } else {
//                    saleorderVo.setAdditionalClause(saleorderVo.getAdditionalClause() + "<br>" + additionClause);
//                }
//            }
//
//
//
//        }

        mv.addObject("goodsLength", goodsLength);
        mv.addObject("saleorderGoodsList", saleorderGoodsList);

        //订单中含有指定特殊sku时，订单合同隐藏"除以上不可抗拒的因素，乙方延迟交货给甲方造成损失的，从延迟交付的第十（10）个工作日起，承担延迟交付的违约责任，每工作日违约金额为乙方延迟交付产品已付金额的0.3%，违约金上限为乙方延迟交付产品已付金额的3%。"文本
        boolean contractContainSpecialSku = false;
        List<String> specialSkuInContract = Arrays.stream(specialSkuListInContract.split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> skuListInSaleorder = saleorderGoodsList.stream().map(SaleorderGoods::getSku).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(specialSkuInContract) && CollectionUtils.isNotEmpty(skuListInSaleorder)){
            skuListInSaleorder.retainAll(specialSkuInContract);
            contractContainSpecialSku = CollectionUtils.isNotEmpty(skuListInSaleorder);
        }
        mv.addObject("contractContainSpecialSku",contractContainSpecialSku);

        Long currTime = DateUtil.sysTimeMillis();
        BigDecimal pageTotalPrice = new BigDecimal(0.00);
        BigDecimal zioe = pageTotalPrice;
        Integer flag = -1;
        for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
            if (saleorderGoods.getHaveInstallation() == 1) {
                flag = 1;
            }
            String price = getCommaFormat(saleorderGoods.getPrice());
            if (!price.contains(".")) {
                price += ".00";
            }
            saleorderGoods.setPrices(price);
            // 判断是否为HC订单
            if (saleorderVo != null && saleorderVo.getOrderType() != null && (saleorderVo.getOrderType() == 1 || saleorderVo.getOrderType() == 5)) {
                String allprice = getCommaFormat(saleorderGoods.getMaxSkuRefundAmount());
                if (!allprice.contains(".")) {
                    allprice += ".00";
                }
                saleorderGoods.setAllPrice(allprice);
                pageTotalPrice = pageTotalPrice
                        .add(saleorderGoods.getMaxSkuRefundAmount());

            } else {
                String allprice = getCommaFormat(
                        saleorderGoods.getPrice().multiply(new BigDecimal(saleorderGoods.getNum())));
                if (!allprice.contains(".")) {
                    allprice += ".00";
                }
                saleorderGoods.setAllPrice(allprice);
                pageTotalPrice = pageTotalPrice
                        .add(saleorderGoods.getPrice().multiply(new BigDecimal(saleorderGoods.getNum())));
            }
        }
        String totalAmount = getCommaFormat(pageTotalPrice);
        if (!totalAmount.contains(".")) {
            totalAmount += ".00";
        }
        mv.addObject("totalAmount", totalAmount);
        mv.addObject("totalAmountBigDecimal", pageTotalPrice);
        try {
            mv.addObject("chineseNumberTotalPrice",
                    pageTotalPrice.compareTo(zioe) > 0 ? DigitToChineseUppercaseNumberUtils.numberToChineseNumber(pageTotalPrice) : null);
        } catch (ShowErrorMsgException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        mv.addObject("invoiceTypes", invoiceTypes);
        // 运费类型
        List<SysOptionDefinition> yfTypes = getSysOptionDefinitionList(469);
        mv.addObject("yfTypes", yfTypes);

        // 合同中质保比例
        try {
            if (saleorderVo.getPaymentType() == 424) {
                mv.addObject("prepaidPercent", saleorderVo.getPrepaidAmount().divide(pageTotalPrice, 4, BigDecimal.ROUND_HALF_UP));
                mv.addObject("accountPrepaidPercent", (saleorderVo.getAccountPeriodAmount().subtract(saleorderVo.getRetentionMoney()))
                        .divide(pageTotalPrice, 4, BigDecimal.ROUND_HALF_UP));
                mv.addObject("retetionPercent", saleorderVo.getRetentionMoney().divide(pageTotalPrice, 4, BigDecimal.ROUND_HALF_UP));
            } else if (saleorderVo.getPaymentType() != 419 && new BigDecimal(0).compareTo(saleorderVo.getRetentionMoney()) == -1 &&
                    saleorderVo.getRetentionMoney().compareTo(saleorderVo.getAccountPeriodAmount()) == -1) {
                mv.addObject("retetionPercent",
                        saleorderVo.getRetentionMoney().divide(pageTotalPrice, 4, BigDecimal.ROUND_HALF_UP));
                mv.addObject("prepaidPercent",
                        (saleorderVo.getAccountPeriodAmount().subtract(saleorderVo.getRetentionMoney())).divide(pageTotalPrice, 4, BigDecimal.ROUND_HALF_UP));
            }
        } catch (Exception e) {
            logger.error("计算质保金比例 error ", e);
        }

        // 判断含安调显示内容
        Integer haveInstallationSize = saleorderGoodsList.stream()
                .filter(saleorderGood -> saleorderGood.getHaveInstallation() != null && saleorderGood.getHaveInstallation() == 1)
                .collect(Collectors.toSet()).size();

        if (haveInstallationSize == saleorderGoodsList.size()) {
            // 含安调
            mv.addObject("haveInstallationFlag", 2);
        } else if (haveInstallationSize == 0) {
            // 不含安调
            mv.addObject("haveInstallationFlag", 0);
        } else {
            // 部分安调
            mv.addObject("haveInstallationFlag", 1);
        }

        // 获取公司信息
        Company company = companyService.getCompanyByCompangId(salesUser.getCompanyId());

        mv.addObject("company", company);
        mv.addObject("flag", flag);
        mv.addObject("currTime", DateUtil.convertString(currTime, "yyyy-MM-dd"));
        mv.addObject("detail", userDetail);
        mv.addObject("username", username);
        mv.addObject("saleorderGoodsList", saleorderGoodsList);
        mv.addObject("saleorderVo", saleorderVo);
        mv.addObject("autoGenerate", autoGenerate);
        mv.addObject("retentionMoneyUptime", retentionMoneyUptime);
        //处理合同日期
        Date contractDate = 0 == saleorderVo.getValidTime() ? new Date() : new Date(saleorderVo.getValidTime());
        List<String> contractDateList = Arrays.asList(Integer.toString(cn.hutool.core.date.DateUtil.year(contractDate))
                ,String.format("%02d",cn.hutool.core.date.DateUtil.month(contractDate)+1)
                ,String.format("%02d",cn.hutool.core.date.DateUtil.dayOfMonth(contractDate)));
        mv.addObject("contractDateList",contractDateList);

        //销售区域：http://wiki.ivedeng.com/pages/viewpage.action?pageId=300843031
        List<SaleOrderTerminalDto> saleOrderTerminalDtoList= orderTerminalService.findSaleOrderTerminalBySaleOrderId(saleorderVo.getSaleorderId());
        String saleOrderSalesArea="";
        if(CollectionUtils.isNotEmpty(saleOrderTerminalDtoList)){
            for (int i = 0; i < saleOrderTerminalDtoList.size(); i++) {

                SaleOrderTerminalDto saleOrderTerminalDto = saleOrderTerminalDtoList.get(i);
                if(StringUtils.isNotBlank(saleOrderTerminalDto.getProvinceName())||
                        StringUtils.isNotBlank(saleOrderTerminalDto.getCityName())
                ){
                    saleOrderSalesArea= Utils.trimNull(saleOrderTerminalDto.getProvinceName())+Utils.trimNull(saleOrderTerminalDto.getCityName());
                    break;
                }
            }
            mv.addObject("saleOrderSalesArea",saleOrderSalesArea);
        }
        mv.setViewName("orderstream/saleorder/contract_template");
        return mv;
    }

    /**
     * 由于快照信息更新存在延迟，导致订单合同上的产品信息补全，需要补全产品信息
     * @param saleOrderGood
     * @param spuId
     */
    private void completeRegistraAndManufacture(SaleorderGoods saleOrderGood, Integer spuId) {
        if (StringUtils.isNotBlank(saleOrderGood.getRegistrationNumber()) && StringUtils.isNotBlank(saleOrderGood.getManufacturerName())){
            return;
        }
        log.info("需要补全产品信息：{}",spuId);
        RegistrationNumberSpuQuery build = RegistrationNumberSpuQuery.builder().spuId(spuId).build();
        RegistrationNumberSpuDto registrationNumberSpuDto = registrationNumberApiService.queryRegistraAndManufactureBySpuId(build);
        if (Objects.isNull(registrationNumberSpuDto)){
            log.error("根据SPU补全产品信息时未查询到基础信息spuId:{}",spuId);
            return;
        }
        if (StringUtils.isBlank(saleOrderGood.getRegistrationNumber())){
            log.info("需要补全注册证号");
            saleOrderGood.setRegistrationNumber(registrationNumberSpuDto.getRegistrationNumber());
        }
        if (StringUtils.isBlank(saleOrderGood.getManufacturerName())){
            log.info("需要补全生产厂家");
            saleOrderGood.setManufacturerName(registrationNumberSpuDto.getManufacturerName());
        }
    }

    // 每3位中间添加逗号的格式化显示
    private static String getCommaFormat(BigDecimal value) {
        return getFormat(",###.##", value);
    }

    // 自定义数字格式方法
    private static String getFormat(String style, BigDecimal value) {
        DecimalFormat df = new DecimalFormat();
        df.applyPattern(style);// 将格式应用于格式化器
        return df.format(value.doubleValue());
    }

}
