<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增客户" scope="application" />
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<script type="text/javascript" src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/layui.js"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/webjars/ezadmin/plugins/layui/css/layui.css"/>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/cascader.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<!-- 模糊搜索下拉框css引入 -->
<link rel="stylesheet" href="<%=basePath%>static/libs/searchableSelect/jquery.searchableSelect.css" />
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/labelMark.css">
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<%@ include file="../../../vue/view/common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/select2.css" />

<style>
	i {
		background: none !important;;
	}
	.el-cascader__tags{
		/* width:50px;*/
		/*padding-top: 5px;*/
	}
	.list-pages-search .el-cascader {
		line-height: 26px;
		height: 26px;
		width: 70%
	}
	.el-cascader__search-input {
		margin-top: 2px ;
		font-size: 12px;
	}
	.el-cascader .el-input .el-input__inner {
		height: 28px;
		line-height: 22px;
	}

	.el-cascader i, .el-cascader-panel i {
		height: auto;
		background: none;
	}

	.el-cascader__tags .el-tag .el-icon-close {
		-webkit-box-flex: 0;
		-ms-flex: none;
		flex: none;
		background-color: #C0C4CC !important;
	}

	.el-cascader .el-cascader__tags {
		flex-wrap: inherit;
	}
	.el-cascader__search-input{
		min-width: 10px;
	}
	.el-cascader-menu{
		width : 350px !important;
		min-width: 300px;
		height: 150px;
	}
	/*修改菜单高度*/
	.el-cascader-menu__wrap {
		height: 200px;
	}
	.el-input__suffix-inner{
		margin-top: 10px;
	}
</style>
<div style="text-align: right;padding-right: 10px;padding-bottom: 12px;">
	<el-button type="primary" style="color: #00a0e9" onclick="doHelp()">帮助</el-button>
</div>
<div class="pt10">
	<form method="post"
		action="${pageContext.request.contextPath}/trader/customer/saveadd.do" id="customerForm">
		<div class="parts">
			<div class="baseinforcontainer pb20">
				<div class="border overflow-hidden">
					<div class="baseinfor f_left">基本信息</div>
				</div>
				<div class="baseinforeditform">
					<ul>
						<li>
							<div class="infor_name">
								<span>*</span>
								<lable>客户名称</lable>
							</div>
							<div class="f_left">
								<input type="hidden" name="formToken" value="${formToken}"/>
								<input type="hidden" name="optType" id="optType" value="${optType}"/>
								<input type="text" class="input-largest errobor"
									name="traderName" id="traderName" value="${traderName }"/>
									<span  id="eye" style="line-height:24px;" onclick="upperCase();" class="bt-small bt-border-style border-blue "
							layerParams='{"width":"40%","height":"320px","title":"客户列表","link":"<%= basePath %>trader/customer/queryEyeCheck.do?type=1"}'>天眼查接口查询</span>
								<span style="color: red">如无法查询，可手动填写。</span>
							</div>
						</li>
						<li>
							<div class="infor_name">
								<span>*</span>
								<lable>注册地区</lable>
							</div>
							<div class="f_left">
								<select class="input-middle mr6" name="province">
									<option value="0">请选择</option>
									<c:if test="${not empty provinceList }">
										<c:forEach items="${provinceList }" var="province">
											<option value="${province.regionId }">${province.regionName }</option>
										</c:forEach>
									</c:if>
								</select> <select class="input-middle mr6" name="city">
									<option value="0">请选择</option>
								</select> <select class="input-middle mr6" name="zone" id="zone">
									<option value="0">请选择</option>
								</select>
							</div>
						</li>
						<li>
							<div class="infor_name">
								<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
								<lable>仓库地区</lable>
							</div>
							<div class="f_left">

								<select class="input-middle mr6" name="warehouseProvince" id="warehouseProvince">
									<option value="0">请选择</option>
									<c:if test="${not empty provinceList }">
										<c:forEach items="${provinceList }" var="province">
											<option value="${province.regionId }"
													<c:if test="${ not empty provinceRegion &&  province.regionId == warehouseProvinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
										</c:forEach>
									</c:if>
								</select>

								<select class="input-middle mr6" name="warehouseCity"  id="warehouseCity">
									<option value="0">请选择</option>
									<c:if test="${not empty warehouseCityList }">
										<c:forEach items="${warehouseCityList }" var="city">
											<option value="${city.regionId }"
													<c:if test="${ not empty cityRegion &&  city.regionId == warehouseCityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
										</c:forEach>
									</c:if>
								</select>

								<select class="input-middle mr6" name="warehouseZone" id="warehouseZone">
									<option value="0">请选择</option>
									<c:if test="${not empty warehouseZoneList }">
										<c:forEach items="${warehouseZoneList }" var="zone">
											<option value="${zone.regionId }"
													<c:if test="${ not empty zoneRegion &&  zone.regionId == warehouseZoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
										</c:forEach>
									</c:if>
								</select>

								<input type="text" class="input-small mt0 heit18" style="width:300px;height: 26px" placeholder="请填写详细地址信息" id="warehouseDetailAddress" name="warehouseDetailAddress" value="${traderCustomer.trader.warehouseDetailAddress}"/>
								<span style="color: red">
									若客户暂无仓库，可填写客户注册地址或经营地址。
								</span>
							</div>
						</li>
						<li id="attr_brfore_lis">
							<div class="infor_name">
								<span>*</span>
								<lable>归属平台</lable>
							</div>
							<div class="f_left">
								<div id="belongPlatform_div">
									<select class="input-middle mr6" name="belongPlatform"
											 id="belong_Platform">
										<option <c:if test="${belongPlatformOfUser==null}">selected="selected"</c:if> value="0">请选择</option>
										<option  <c:if test="${belongPlatformOfUser==1}">selected="selected"</c:if> value="1">贝登医疗</option>
										<option  <c:if test="${belongPlatformOfUser==2}">selected="selected"</c:if> value="2">医械购</option>
										<option <c:if test="${belongPlatformOfUser==3}">selected="selected"</c:if>  value="3">科研购</option>
<%--										<option <c:if test="${belongPlatformOfUser==5}">selected="selected"</c:if>  value="5">其他</option>--%>
									</select>
                                    <span style="color: red">
										默认选择"贝登医疗"，科研选择"科研购"
								    </span>
								</div>

							</div>
						</li>
						<li id="attr_brfore_li">
							<div class="infor_name">
								<span>*</span>
								<lable>客户类型</lable>
							</div>
							<div class="f_left">
								<div id="customer_category_div">
									<select class="input-middle mr6" name="customer_category"
										onchange="changeCate(this);" id="customer_category">
										<option selected="selected" value="0">请选择</option>
									</select>
								</div>
							</div>
						</li>
                        <li class="dealer5" style="display: none">
                            <div class="infor_name mt0">
                                <lable>客户有效性</lable>
                            </div>
                            <div class="f_left inputradio">
                                <ul>
                                    <li><input type="radio" name="effectiveness" checked  value="1"> <label>有效</label></li>
                                    <li><input type="radio" name="effectiveness" value="0"> <label>无效</label></li>
									<li class="queryInvalidReasonList" hidden>
										<div class="input-middle f_left" style="width: 400px;">
											<input type="hidden" id="invalidReason" name="invalidReason" value=""  >
											<input id="queryInvalidReasonListSelect" name="queryInvalidReasonListSelect" style="width: 100%">
										</div>
									</li>
									<li class="reasonInput" hidden>
										<input type="text" 	id="otherReason" name="otherReason" placeholder="请填写具体原因（不超过50字）" style="width:620px;margin-left: 10px">
									</li>
                                </ul>
                            </div>
                        </li>



						<li class="specialli dealer5" alt="fenxiao">
							<div class="infor_name">
								<label>经营区域</label>
							</div>
							<div class="readyadd f_left">
								<div class="career-one">
									<select class="input-middle f_left mr8"
										name="bussiness_province">
										<option value="0">请选择</option>
										<c:if test="${not empty provinceList }">
											<c:forEach items="${provinceList }" var="province">
												<option value="${province.regionId }">${province.regionName }</option>
											</c:forEach>
										</c:if>
									</select> <select class="input-middle f_left mr8" name="bussiness_city">
										<option value="0">请选择</option>
									</select> <select class="input-middle f_left mr8" name="bussiness_zone">
										<option value="0">请选择</option>
									</select>
									<div
										class="f_left bt-bg-style bg-light-blue bt-smaller mr8 addaddress"
										onclick="addBussinessArea();" id="addBussinessArea">添加</div>
								</div>
								<div class="addtags mt8 addaddresstags ">
									<ul id="bussinessArea_show">
									</ul>
								</div>
							</div>
						</li>

						<li class="specialli" alt="workAreaLi">
							<div class="infor_name">
								<span>*</span>
								<label>办公区域</label>
							</div>
							<div class="readyadd f_left">
								<div class="career-one">
									<select class="input-middle f_left mr8"
											name="work_province" id="work_province">
										<option value="0">请选择</option>
										<c:if test="${not empty provinceList }">
											<c:forEach items="${provinceList }" var="province">
												<option value="${province.regionId }">${province.regionName }</option>
											</c:forEach>
										</c:if>
									</select> <select class="input-middle f_left mr8" name="work_city" id="work_city">
									<option value="0">请选择</option>
								</select> <select class="input-middle f_left mr8" name="work_zone" id="work_zone">
									<option value="0">请选择</option>
								</select>

								</div>
								<div class="addtags mt8 addaddresstags " id="workArea_show">

								</div>
							</div>
						</li>

						<li alt="fenxiao">
							<div class="infor_name ">
								<label>经营品牌</label>
							</div>
							<div class="f_left inputradio">
								<div class="career-one">
									<input class="input-middle f_left mr8 mb8 mt0"
										placeholder="请输入关键词查询" name="bussinessBrandKey" />
									<div
										class="f_left bt-bg-style bg-light-blue bt-small mr8 searchbrand"
										onclick="searchBussinessBrand()">搜索</div>
									<select class="input-middle f_left mr8 mb8 mt0"
										name="bussinessBrands">
									</select>
									<div
										class="f_left bt-bg-style bg-light-blue bt-small mr8 addbrand"
										onclick="addBussinessBrand()" id="addBussinessBrand">添加</div>
								</div>
								<div class="addtags addbrandtags">
									<ul id="bussinessBrand_show">
									</ul>
								</div>
							</div>
						</li>
						<li alt="fenxiao">
							<div class="infor_name ">
								<label>代理品牌</label>
							</div>
							<div class="f_left inputradio">
								<div class="career-one">
									<input class="input-middle f_left mr8 mt0"
										placeholder="请输入关键词查询" name="agentBrandKey" />
									<div
										class="f_left bt-bg-style bg-light-blue bt-small mr8 searchbrand"
										onclick="searchAgentBrand()">搜索</div>
									<select class="input-middle f_left mr8" name="agentBrands">
									</select>
									<div
										class="f_left bt-bg-style bg-light-blue bt-small mr8 addbrand"
										onclick="addAgentBrand()" id="addAgentBrand">添加</div>
								</div>
								<div class="addtags addbrandtags mt8">
									<ul id="agentBrand_show">
									</ul>
								</div>
							</div>
						</li>
						<li alt="fenxiao">
							<div class="infor_name mt3">
								<label>业务模式</label>
							</div>
							<div class="f_left">
								<span class="mr4">直销比例 </span> <input type="text"
									class="input-smallest mr8 " name="traderCustomer.directSelling" /><span
									class="mr8">%</span> <span class="mr4">批发比例</span> <input
									type="text" class="input-smallest mr8"
									name="traderCustomer.wholesale" /><span id="wholesale">%</span>
							</div>
						</li>
						<li alt="fenxiao">
							<div class="infor_name mt0">
								<label>销售模式</label>
							</div>
							<div class="f_left inputradio" id="salesModel_div"></div>
						</li>
						<li class="dealerNotShow">
							<div class="infor_name mt0">
								<label>员工人数</label>
							</div>
							<div class="f_left inputradio">
								<select class="input-middle f_left mr8"
									name="traderCustomer.employees">
									<option selected="selected" value="0">请选择</option>
								</select>
							</div>
						</li>
						<li class="dealerNotShow">
							<div class="infor_name mt0">
								<label>年销售额</label>
							</div>
							<div class="f_left inputradio">
								<select class="input-middle f_left mr8"
									name="traderCustomer.annualSales">
									<option selected="selected" value="0">请选择</option>
								</select>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>
		<input type="hidden" id="theDataOfThePage" name="theDataOfThePage" value="">
		<div style="display: none" id="dealerData">
			<jsp:include page="/traderCustomer/dealerEditView.do" />
		</div>

		<c:if test="${not empty tycInfo }">
<div class="parts ">
	<div class="title-container">
		<div class="table-title nobor">天眼查同步信息</div>
	</div>
	<table class="table">
		<tbody>
			<tr>
				<td>
					<div class="form-list form-tips7">
						<form method="post" action="">
							<ul>
								<li>
									<div class="form-tips">
										<lable>注册地址</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<input type="text" class="input-largest" name="traderFinance.regAddress" value="${tycInfo.regLocation }">
										</div>
									</div>
								</li>
								<li>
									<div class="form-tips">
										<lable>税务登记号</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<input type="text" class="input-largest" name="traderFinance.taxNum" value="${tycInfo.taxNumber }">
										</div>
									</div>
								</li>
								<li>
									<div class="form-tips">
										<lable>组织机构代码</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<input type="text" class="input-largest" name="traderCustomer.orgNumber"  value="${tycInfo.orgNumber }">
										</div>
									</div>
								</li>
								<li>
									<div class="form-tips">
										<lable>统一社会信用代码</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<input type="text" class="input-largest" name="traderCustomer.creditCode" value="${tycInfo.creditCode }">
										</div>
									</div>
								</li>
								<li>
									<div class="form-tips">
										<lable>注册日期</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<input class="Wdate" type="text" placeholder="请选择日期"
												onClick="WdatePicker()" autocomplete="off" style="width: 140px;" name="traderCustomer.registeredDateStr" value=<date:date value="${tycInfo.estiblishTime}" format="yyyy-MM-dd" />>
										</div>
									</div>
								</li>
								<li>
									<div class="form-tips">
										<lable>注册资金</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<input type="text" class="input-middle" id="registeredCapital" name="traderCustomer.registeredCapital" value="${tycInfo.regCapital }"> <!-- <span>万</span> -->
										</div>
									</div>
								</li>
								<li>
									<div class="form-tips">
										<lable>经营范围</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<textarea class="input-largest"
												rows="6"  name="traderCustomer.businessScope">${tycInfo.businessScope }</textarea>
										</div>
									</div>
								</li>
								<li>
									<div class="form-tips">
										<lable>历史名称</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<span name="traderCustomer.businessScope">
											<c:if test="${not empty tycInfo.historyNames }">
											${tycInfo.historyNames }
											</c:if>
											<c:if test="${ empty tycInfo.historyNames }">
											--
											</c:if>
											</span>
										</div>
									</div>
								</li>
								<li>
									<div class="form-tips">
										<lable>天眼查更新时间</lable>
									</div>
									<div class="f_left">
										<div class="form-blanks">
											<span><date:date value ="${tycInfo.updateTime}"/></span>
										</div>
									</div>
								</li>
							</ul>
						</form>
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</div>
</c:if>
		<div class="baseinforcontainer mt10 pb20" id="oneDataManagement" style="display: none" >
			<div class="border overflow-hidden">
				<div class="baseinfor f_left">终端同步信息</div>
			</div>
			<div class="baseinforeditform ">
				<ul>
					<li>
						<div class="infor_name mt0">
							<lable>经营状态</lable>
						</div>
						<div class="f_left " >
							<input type="text" class="input-largest"  id="managementForms" name="traderCustomer.traderCustomerMarketingDto.managementForms" value=""/>
						</div>
					</li>
					<li>
						<div class="infor_name mt0">
							<lable>法人代表</lable>
						</div>
						<div class="f_left" >
							<input type="text" class="input-largest" id="legalRepresentative" name="traderCustomer.traderCustomerMarketingDto.legalRepresentative" value=""/>
						</div>
					</li>
					<li>
						<div class="infor_name mt0">
							<lable>床位数</lable>
						</div>
						<div class="f_left">
							<input type="text" class="input-largest" id="bedNumber" name="traderCustomer.traderCustomerMarketingDto.bedNumber" value=""/>
						</div>
					</li>
					<li>
						<div class="infor_name mt0">
							<lable>科室</lable>
						</div>
						<div class="f_left">
							<input type="text" class="input-largest" id="hospitalDepartment" name="traderCustomer.traderCustomerMarketingDto.hospitalDepartment" value=""/>
						</div>
					</li>
				</ul>
			</div>
		</div>

		<br>
		<br>
		<div class="add-tijiao tcenter">
			<input type="hidden" name="isLcfx" value="0">
		    <input type="hidden" name="traderCustomerCategoryIds" id="traderCustomerCategoryIds">
		    <input type="hidden" name="traderCustomer.traderCustomerCategoryId" id="traderCustomerCategoryId" value="0">
			<c:if test="${optType != null}">
				<div class="sucsess-ok J-submit" style="display: inline-block;">提交</div>
			</c:if>
			<c:if test="${optType == null}">
				<button type="submit" class="J-add">提交</button>
			</c:if>
		</div>

		<input type="hidden" name="isAiAssistant" value="${isAiAssistant}"/>
		<input type="hidden" name="aiCommunicateRecordId" value="${aiCommunicateRecordId}"/>

	</form>
</div>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/customer/add_customer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">



	function initRegProCityArea() {

		var regLocation = "";//获取页面是否有经过天眼查带过来的注册地址
		var regAddress = $('input[name="traderFinance.regAddress"]');
		if (regAddress.length > 0) {
			regLocation = regAddress.val();
			//第一步，先判断地址中，是否有省份
			var hasProvince = false;
			var provinceNameDefault = "";
			var cityNameDefault = "";
			var areaNameDefault = "";
			if(regLocation.startsWith("北京")){  //先判断是否是四个直辖市
				provinceNameDefault = "北京市";
				cityNameDefault = "北京市";
				regLocation = regLocation.replaceAll("北京市","");
			} else if(regLocation.startsWith("天津")){
				provinceNameDefault = "天津市";
				cityNameDefault = "天津市";
				regLocation = regLocation.replaceAll("天津市","");
			}else if(regLocation.startsWith("上海")){
				provinceNameDefault = "上海市";
				cityNameDefault = "上海市";
				regLocation = regLocation.replaceAll("上海市","");
			}else if(regLocation.startsWith("重庆")){
				provinceNameDefault = "重庆市";
				cityNameDefault = "重庆市";
				regLocation = regLocation.replaceAll("重庆市","");
			}else{
				$("select[name=\"province\"] option").each(function() {
					var optionText = $(this).text();
					var optionSuf = optionText.replaceAll("省","").replaceAll("自治区","");
					if(regLocation.startsWith(optionSuf)){
						provinceNameDefault = optionText;
						if(regLocation.startsWith(optionText)){
							regLocation = regLocation.replaceAll(optionText,"");
						}else{
							regLocation = regLocation.replaceAll(optionSuf,"");
						}
						return false;//跳出该each循环
					}
				});
			}
			console.log("省份是："+provinceNameDefault);
			console.log("城市是："+cityNameDefault);
			if(provinceNameDefault == ''){//未匹配到省份，再匹配一次城市，有可能地址是	岳阳县新开镇胜天村长塘组- 实际是岳阳市。
				console.log("地址中不包含省份，未匹配到。以下将按城市进行匹配");
				$.ajax({
					type : "POST",
					url : page_url+"/system/region/getRegionCityAll.do",
					dataType : 'json',
					async:true,
					success : function(data) {
						var provinceCode = 0;
						$.each(data.listData,function(i,n){
							var regionName = data.listData[i]['regionName']; //岳阳市   regLocation 岳阳县新开镇胜天村长塘组 regionName  岳阳市
							var cityNameSuf = regionName.replaceAll("县","").replaceAll("市","");
							if(regLocation.startsWith(regionName) || regLocation.startsWith(cityNameSuf)){
								cityNameDefault = regionName;
								console.log("匹配到的城市是:"+cityNameDefault);
								regLocation = regLocation.startsWith(regionName)?regLocation.replaceAll(regionName,""):regLocation.replaceAll(cityNameSuf,"");
								provinceCode =data.listData[i]['parentId'];
								return false;
							}
							//$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
						});
						$('select[name="province"]').val(provinceCode);
						$("select[name='province']").trigger('change');

						$('select[name="warehouseProvince"]').val(provinceCode);
						$("select[name='warehouseProvince']").trigger('change');


						setTimeout(function () {
							$("select[name='city'] option:contains('"+cityNameDefault+"')").prop("selected", true);
							$("select[name='city']").trigger('change');

							$("select[name='warehouseCity'] option:contains('"+cityNameDefault+"')").prop("selected", true);
							$("select[name='warehouseCity']").trigger('change');



							setTimeout(function(){  //计算区的选择逻辑
								$("select[name=\"zone\"] option").each(function() {
									var optionText = $(this).text();
									var optionSuf = optionText.replaceAll("区","").replaceAll("自治州","").replaceAll("市","");
									regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
									regLocation = regLocation.startsWith("自治州")?regLocation.replace("自治州",""):regLocation;
									regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;

									if(regLocation.startsWith(optionSuf)){//optionSuf  相城
										console.log("匹配到的区是:"+optionText);
										areaNameDefault = optionText;
										//江苏苏州相城经济开发区（澄波路） 相城经济开发区（澄波路）
										regLocation = regLocation.replace(optionSuf,'');
										regLocation = regLocation.replace('区','');
										regLocation = regLocation.replace('自治区','');
										regLocation = regLocation.replace('县','');

										return false;//跳出该each循环
									}
								});
								if(areaNameDefault!=''){
									$("select[name='zone'] option:contains('"+areaNameDefault+"')").prop("selected", true);
									//regLocation = regLocation.replace(areaNameDefault,'');
									//console.log(regLocation);

									$("select[name='warehouseZone'] option:contains('"+areaNameDefault+"')").prop("selected", true);
									console.log(regLocation);
									// regLocation = regLocation.replace(areaNameDefault,'');
									$("#warehouseDetailAddress").val(regLocation);

								}
							},500);
						},500);

					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
						}
					}
				});
			}else{//如果省份已经匹配到了
				$("select[name='province'] option:contains('"+provinceNameDefault+"')").prop("selected", true);
				$("select[name='province']").trigger('change');

				$("select[name='warehouseProvince'] option:contains('"+provinceNameDefault+"')").prop("selected", true);
				$("select[name='warehouseProvince']").trigger('change');

				setTimeout(function () {
					if(cityNameDefault ==''){
						$("select[name=\"city\"] option").each(function() {
							var optionText = $(this).text();
							var optionSuf = optionText.replaceAll("市","").replaceAll("县","").replaceAll("自治州","");
							if(regLocation.startsWith(optionSuf)){
								cityNameDefault = optionText;
								console.log("匹配到的城市是:"+cityNameDefault);
								regLocation = regLocation.startsWith(optionText)?regLocation.replaceAll(optionText,""):regLocation.replaceAll(optionSuf,"");
								regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
								regLocation = regLocation.startsWith("自治州")?regLocation.replace("自治州",""):regLocation;
								regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;
								return false;//跳出该each循环
							}
						});
						if(cityNameDefault  == ''){ //如果仍然没有匹配到城市
							return ;
						}
					}
					console.log("城市是"+cityNameDefault);
					$("select[name='city'] option:contains('"+cityNameDefault+"')").prop("selected", true);
					$("select[name='city']").trigger('change');

					$("select[name='warehouseCity'] option:contains('"+cityNameDefault+"')").prop("selected", true);
					$("select[name='warehouseCity']").trigger('change');


					setTimeout(function(){  //计算区的选择逻辑
						$("select[name=\"zone\"] option").each(function() {
							var optionText = $(this).text();
							var optionSuf = optionText.replaceAll("区","").replaceAll("镇","");
							regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
							regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;
							if(regLocation.startsWith(optionSuf)){
								areaNameDefault = optionText;
								console.log("匹配到的区是:"+areaNameDefault);
								//江苏苏州相城经济开发区（澄波路） 相城经济开发区（澄波路）
								regLocation = regLocation.replace(optionSuf,'');
								regLocation = regLocation.replace('区','');
								regLocation = regLocation.replace('自治区','');
								regLocation = regLocation.replace('县','');

								return false;//跳出该each循环
							}
						});
						if(areaNameDefault!=''){
							console.log("区是"+areaNameDefault);
							$("select[name='zone'] option:contains('"+areaNameDefault+"')").prop("selected", true);
							$("select[name='warehouseZone'] option:contains('"+areaNameDefault+"')").prop("selected", true);
							console.log(regLocation);
							// regLocation = regLocation.replace(areaNameDefault,'');
							$("#warehouseDetailAddress").val(regLocation);
						}
					},500);
				},500);


			}
		}

	}


	$(function(){
		$("select[name='warehouseProvince']").change(function(){
			checkLogin();
			var regionId = $(this).val();
			if(regionId > 0){
				$.ajax({
					type : "POST",
					url : page_url+"/system/region/getregion.do",
					data :{'regionId':regionId},
					dataType : 'json',
					success : function(data) {
						debugger;
						$option = "<option value='0'>请选择</option>";
						$.each(data.listData,function(i,n){
							$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
						});
						$("select[name='warehouseCity'] option:gt(0)").remove();
						$("select[name='warehouseZone'] option:gt(0)").remove();
						$("#warehouseCity").val("0").trigger("change");
						$("#warehouseZone").val("0").trigger("change");

						$("select[name='warehouseCity']").html($option);
						$("select[name='warehouseZone']").html("<option value='0'>请选择</option>");
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}else if(regionId==0){
				$("select[name='warehouseCity'] option:gt(0)").remove();
				$("select[name='warehouseZone'] option:gt(0)").remove();
			}
		});

		$("select[name='warehouseCity']").change(function(){
			checkLogin();
			var regionId = $(this).val();
			if(regionId > 0){
				$.ajax({
					type : "POST",
					url : page_url+"/system/region/getregion.do",
					data :{'regionId':regionId},
					dataType : 'json',
					success : function(data) {
						$option = "<option value='0'>请选择</option>";
						$.each(data.listData,function(i,n){
							$option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
						});
						$("select[name='warehouseZone'] option:gt(0)").remove();

						$("select[name='warehouseZone']").html($option);
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}
		});
		initRegProCityArea();
	});
</script>
<%@ include file="../../common/footer.jsp"%>
