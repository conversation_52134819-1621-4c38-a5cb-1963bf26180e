Vue.component('ui-time-panel', {
    template: `
        <div class="vd-ui-time-panel">
            <!-- 头 只在日期+时间选择器显示 -->
            <div class="vd-ui-time-panel__header" v-if="type=='datetime'">
                {{ showTime }}
            </div>

            <!-- 内容 -->
            <div 
                class="vd-ui-time-panel__content"
                :style="{borderBottom: type=='time'? 'solid 1px #E1E5E8' : 'none'}"
            >
                <ui-time-table
                    :type="type"
                    :date="date"
                    @pick="pickTime"
                ></ui-time-table>
            </div>

            <!-- 脚 -->
            <div class="vd-ui-time-panel__footer" v-if="type=='time'">
                <button
                    type="button"
                    class="time-cancel"
                    @click="handleNow"
                >此时</button>
                <button
                    type="button"
                    class="time-confirm"
                    @click="handleConfirm"
                >确定</button>
            </div>
        </div>
    `,

    props: {
        type: {
            type: String,
            default: 'time'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        selectableRange: String,   // 时间范围
        defaultValue: {},


        disabledDate: {},
        cellClassName: {},
        minDate: {},
        maxDate: {},
    },

    data() {
        return {
            val: '',
            // format: 'HH:mm:ss',
            selectionRange: [0, 2],
            needInitAdjust: true,

            date: new Date(),
        };
    },

    computed: {
        // 整体时间
        showTime () {
            return util_date.format(this.date, 'time');   // 这里要用点击后随着动的值，不能用选中的固定value值
        },
    },

    watch: {
        value: {
            handler (val) {
                if (util_date.isDate(val)) {
                    this.date = val;
                }
                else if (typeof(val) == 'string' && util_date.checkToTime(val)) {
                    this.date = util_date.timeToDate(val);
                } else {
                    this.date = this.getDefaultValue();
                }
            },
            immediate: true
        },

        defaultValue(val) {
            if (!util_date.isDate(this.value)) {
                this.date = val ? new Date(val) : new Date();
            }
        }
    },

    created () {
    },

    methods: {
        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },
        // 选中时分秒
        pickTime (val) {
            this.date = val;
            this.$emit('pick', this.date);
        },
        // 此刻
        handleNow () {
            let date = new Date();
            this.$emit('pick', date, true);
        },
        // 确认
        handleConfirm () {
            let date = this.date || null;
            this.$emit('pick', date, true);  // 最后一个true 标识，关闭弹窗
        },
    },
})