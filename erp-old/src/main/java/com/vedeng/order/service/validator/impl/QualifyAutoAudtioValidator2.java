package com.vedeng.order.service.validator.impl;

import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.validator.dto.QualifyAutoAudtioException;
import org.springframework.stereotype.Service;

/**

 *  match:若采购订单产品【SPU信息中"是否在《医疗器械分类目录》"字段为"是"，"是否有注册证/备案凭证"字段为"无"】
 *  validator:
 *      该订单直接转为人工审核
 *  备注输出内容为：订单中产品缺少注册证/备案凭证
 */
@Service
public class QualifyAutoAudtioValidator2 extends AbstractQualifyAutoAudtioValidator{

    @Override
    public boolean isMatch(BuyorderGoodsVo buyOrderGoods) {

        CoreSpuGenerate coreSpuGenerate = super.getSpuInfo(buyOrderGoods.getSku());

        //是否在《医疗器械分类目录》
        boolean isMedicalInstrumentCatalogIncluded = coreSpuGenerate.getMedicalInstrumentCatalogIncluded() == 1;

        //是否有注册证
        boolean hasRegistrationCert = super.hasRegistrationCert(buyOrderGoods.getSku());

        return isMedicalInstrumentCatalogIncluded && !hasRegistrationCert ? true : false;
    }

    @Override
    public String validator(BuyorderGoodsVo buyOrderGoods) throws QualifyAutoAudtioException {
        throw new QualifyAutoAudtioException("订单中产品缺少注册证/备案凭证");
    }
    
    @Override
    public boolean isMatch(Integer traderId, String skuNo) {
        CoreSpuGenerate coreSpuGenerate = super.getSpuInfo(skuNo);

        //是否在《医疗器械分类目录》
        boolean isMedicalInstrumentCatalogIncluded = coreSpuGenerate.getMedicalInstrumentCatalogIncluded() == 1;

        //是否有注册证
        boolean hasRegistrationCert = super.hasRegistrationCert(skuNo);

        return isMedicalInstrumentCatalogIncluded && !hasRegistrationCert ? true : false;
    }

    @Override
    public String validator(Integer traderId, String skuNo) throws QualifyAutoAudtioException {
        throw new QualifyAutoAudtioException("订单中产品缺少注册证/备案凭证");
    }
}
