package com.newtask.celery;

import com.newtask.celery.cache.JoinPointCache;
import com.newtask.celery.exception.CeleryInvokeException;
import com.newtask.celery.model.JobStatusEnum;
import com.newtask.celery.model.JoinPointModel;
import com.newtask.celery.model.TaskJob;
import com.newtask.celery.service.TaskJobService;
import com.newtask.celery.cache.HandleLockCache;
import com.newtask.celery.utils.ReflectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.LinkedBlockingQueue;

import static com.newtask.celery.AsyncConstants.*;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/12 12:32
 */
@Slf4j
@Service
public class CeleryWorker extends LifecycleSupport {

    @Resource
    private ReflectionUtil reflectionUtil;

    @Resource
    private TaskJobService taskJobService;

    /**
     * 暂时使用阻塞队列，如果后期压力过大，改为非阻塞多线程取
     */
    private final LinkedBlockingQueue<TaskJob> queue = new LinkedBlockingQueue<>();

    @Override
    protected void onStart() {
        if (!ASYNC_WORKER) {
            return;
        }

        new Thread(
                () -> {
                    while (true) {
                        try {
                            String uuid = UUID.randomUUID().toString().replace("-", "");
                            log.info("开始执行 doTaskJob 方法,uuid:{}",uuid);
                            TaskJob take = queue.take();
                            doTaskJob(take);
                            log.info("结束执行 doTaskJob 方法,uuid:{}",uuid);
                        } catch (Throwable e) {
                            mySleep(ERROR_SLEEP_TIME);
                        }
                    }
                }).start();
    }

    @Override
    protected void onStop() {
    }

    protected void doTaskJob(TaskJob taskJob) {
        JobStatusEnum result = null;
        for (int i = 0; i < MAX_RETRY_SIZE; i++) {
            result = realDoTaskJob(taskJob);
            if (!JobStatusEnum.FAIL.equals(result)) {
                break;
            }
        }

        saveWorkResult(taskJob, result);
    }

    private JobStatusEnum realDoTaskJob(TaskJob taskJob) {
        String[] handle = taskJob.getHandleIndex().split(HANDLE_REG);
        if (handle.length == 2) {
            try {
                if (JoinPointCache.haveCache(taskJob.getUuid())) {
                    // 本地缓存执行
                    JoinPointModel jointPointModel = JoinPointCache.getJointPointModel(taskJob.getUuid());
                    ProceedingJoinPoint proceedingJoinPoint = jointPointModel.getProceedingJoinPoint();
                    proceedingJoinPoint.proceed(jointPointModel.getParams());
                } else {
                    // 加锁
                    HandleLockCache.lock(taskJob.getUuid());

                    // 回调执行
                    reflectionUtil.invokeService(handle[0], handle[1], taskJob.getInputArgs());
                }

                return JobStatusEnum.SUCCESS;

            } catch (CeleryInvokeException cix) {
                taskJob.setErrorMsg("reflection callback execution failed, msg :{}" + cix.getMessage());
                return JobStatusEnum.FAIL;
            } catch (Throwable e) {
                log.error("handle async error, async ：{} ,job error :{}", taskJob, e.getMessage(), e);
                taskJob.setErrorMsg("ex :" + e.getClass().getName() + "msg: " + e.getMessage());
                return JobStatusEnum.FAIL;
            } finally {

                HandleLockCache.unlock(taskJob.getUuid());
            }

        } else {
            taskJob.setErrorMsg("Incorrect handleIndex format" + Arrays.toString(handle));
            return JobStatusEnum.ERROR;
        }

    }

    protected void add(TaskJob taskJob) {
        queue.add(taskJob);
    }

    private void saveWorkResult(TaskJob job, JobStatusEnum jobStatus) {
        // 回写状态
        if (jobStatus.equals(JobStatusEnum.SUCCESS)) {
            removeStatus(job.getJobId());
        } else {
            putStatus(job.getJobId(), jobStatus.getKey());
        }

        job.setStatus(jobStatus.getKey());
        job.setModTime(System.currentTimeMillis());

        taskJobService.updateTaskJob(job);
    }

}
