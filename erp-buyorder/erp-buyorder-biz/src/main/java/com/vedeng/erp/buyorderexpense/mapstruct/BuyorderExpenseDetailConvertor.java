package com.vedeng.erp.buyorderexpense.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDetailDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.OrderRemarkDto;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description dto转entity
 * @date 2022/7/12 10:45
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface BuyorderExpenseDetailConvertor extends BaseMapStruct<BuyorderExpenseDetailEntity, BuyorderExpenseDetailDto> {


    @Override
    @Mapping(target = "orderDesc", source = "orderDesc", qualifiedByName = "jsonArrayToObject")
    BuyorderExpenseDetailDto toDto(BuyorderExpenseDetailEntity entity);

    @Override
    @Mapping(target = "orderDesc", source = "orderDesc", qualifiedByName = "objectToJsonArray")
    BuyorderExpenseDetailEntity toEntity(BuyorderExpenseDetailDto dto);

    /**
     * entity 中JSONArray 转 原对象
     * @param source JSONArray
     * @return List<BusinessChanceGoodsDto> dto中的对象
     */
    @Named("jsonArrayToObject")
    default List<OrderRemarkDto> jsonArrayToObject(JSONArray source) {
        if (CollUtil.isEmpty(source)) {
            return Collections.emptyList();
        }
        return source.toJavaList(OrderRemarkDto.class);
    }
    /**
     * dto 原对象中 转 JSONArray
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("objectToJsonArray")
    default JSONArray objectToJsonArray(List<OrderRemarkDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }

}
