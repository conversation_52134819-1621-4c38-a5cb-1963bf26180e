package com.vedeng.billsync.dao;

import com.vedeng.billsync.task.model.entity.generate.TmpWechatBillDataDo;

public interface TmpWechatBillDataMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TmpWechatBillDataDo record);

    int insertSelective(TmpWechatBillDataDo record);

    TmpWechatBillDataDo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TmpWechatBillDataDo record);

    int updateByPrimaryKey(TmpWechatBillDataDo record);
}