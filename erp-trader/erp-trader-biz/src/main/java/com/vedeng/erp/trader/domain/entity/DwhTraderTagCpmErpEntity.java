package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
    * 大数据客户终端经营大类
    */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwhTraderTagCpmErpEntity extends BaseEntity implements Serializable {
    /**
    * 交易者ID
    */
    private Integer traderId;

    /**
    * 客户ID
    */
    private Integer traderCustomerId;

    /**
    * ERP机构性质
    */
    private String institutionNature;

    /**
    * ERP营销客户类型
    */
    private String traderCustomerMarketingType;

    /**
    * ERP机构评级
    */
    private String institutionLevel;

    /**
    * ERP机构类型
    */
    private String institutionType;

    /**
    * ERP机构类型子集
    */
    private String institutionTypeChild;

    /**
    * 主营商品三级分类ID合集
    */
    private String mainL3CategoryIds;

    /**
    * ETL DAY
    */
    private Date etlDay;

    private static final long serialVersionUID = 1L;
}