package com.vedeng.customerbillperiod.dao;

import com.vedeng.customerbillperiod.dto.CustomerBillPeriodApplyDetailsDto;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodApplyQueryDto;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodApply;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.access.method.P;

import java.util.List;
import java.util.Map;

public interface CustomerBillPeriodApplyMapper {

    int insert(CustomerBillPeriodApply record);

    int insertSelective(CustomerBillPeriodApply record);

    CustomerBillPeriodApply selectByPrimaryKey(Long billPeriodApplyId);

    int updateByPrimaryKeySelective(CustomerBillPeriodApply record);

    /**
     * 查询客户的账期申请记录
     * @param companyId 公司ID
     * @param customerId 客户ID
     * @return 申请记录
     */
    List<CustomerBillPeriodApply> getApplyListByCustomerId(@Param("companyId") Integer companyId, @Param("customerId") Long customerId);


    /**
     * 查询客户的账期申请记录
     * @param map 客户信息
     * @return 申请记录
     */
    List<CustomerBillPeriodApply> getApplyTraderAmountBillVolistpage(Map<String, Object> map);


    /**
     * <b>Description:</b><br>
     * 根据客户及公司id查询及状态查询
     * 
     * @param companyId 公司ID, customerId 客户ID, status 账期申请状态
     * @return com.vedeng.customerbillperiod.model.CustomerBillPeriodApply
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/27 10:10
     */
    List<CustomerBillPeriodApply>getApplyAuditByCustomerId(@Param("companyId") Integer companyId, @Param("customerId") Integer customerId,@Param("status")Integer status);


    /**
     * 查询客户的账期申请记录列表
     * @param queryDto 查询类
     * @return 账期申请记录列表
     */
    List<CustomerBillPeriodApplyDetailsDto> getApplyDetailsDtoList(@Param("queryDto") CustomerBillPeriodApplyQueryDto queryDto);

    /**
     * <b>Description:</b><br>
     * 根据生效账期id获取账期申请信息
     *
     * @param billPeriodIds
     * @return java.util.List<com.vedeng.customerbillperiod.model.CustomerBillPeriodApply>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/8/10 16:28
     */
    List<CustomerBillPeriodApply>getApplyInfoByBillPeriodId(@Param("billPeriodIds")List<Long>billPeriodIds);
}