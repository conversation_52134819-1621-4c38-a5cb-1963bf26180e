package com.newtask.finance;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 刷新发票校验数据
 */
@JobHandler(value = "InvoiceCheckTask")
@Component
public class InvoiceCheckTask extends AbstractJobHandler {

    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        invoiceCheckApiService.refreshInvoiceApplyCheckData();
        return ReturnT.SUCCESS;
    }


}

