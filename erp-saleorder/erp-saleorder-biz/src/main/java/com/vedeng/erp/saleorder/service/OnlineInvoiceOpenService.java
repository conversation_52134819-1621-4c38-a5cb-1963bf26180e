package com.vedeng.erp.saleorder.service;

import com.vedeng.erp.saleorder.exception.OnlineInvoiceOpenException;
import com.vedeng.erp.saleorder.model.dto.ExpressReceiptDto;
import com.vedeng.erp.saleorder.model.dto.InvoiceOpenInfoDto;

import java.util.List;

/**
 * 在线开票服务层
 *
 * <AUTHOR>
 */
public interface OnlineInvoiceOpenService {

    /**
     * 开票前的数据处理
     *
     * @param invoiceOpenInfoDto
     * @throws Exception
     */
    void dealDataBeforeInvoiceApply(InvoiceOpenInfoDto invoiceOpenInfoDto) throws Exception;

    /**
     * 在线开票处理业务
     *
     * @param invoiceOpenInfoDto
     * @throws OnlineInvoiceOpenException
     */
    void dealInvoiceOpenBusiness(InvoiceOpenInfoDto invoiceOpenInfoDto) throws Exception;

    /**
     * 在线签收处理业务
     *
     * @param expressReceiptList
     * @throws Exception
     */
    void dealExpressReceiptBusiness(List<ExpressReceiptDto> expressReceiptList) throws Exception;

    /**
     * 补偿在线确认附件
     * @param id
     */
    void compenseFile(Integer id);
}
