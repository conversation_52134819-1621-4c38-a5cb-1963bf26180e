package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesGoodsEntity;
import java.util.List;

import com.vedeng.erp.buyorder.dto.BuyOrderSaleOrderGoodsDetailDto;
import com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto;
import com.vedeng.erp.mobile.dto.AfterSalesGoodsListResultDto;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/1/5 16:11
 **/
public interface AfterSalesGoodsBizMapper {
    /**
     * delete by primary key
     * @param afterSalesGoodsId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer afterSalesGoodsId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(AfterSalesGoodsEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(AfterSalesGoodsEntity record);

    /**
     * select by primary key
     * @param afterSalesGoodsId primary key
     * @return object by primary key
     */
    AfterSalesGoodsEntity selectByPrimaryKey(Integer afterSalesGoodsId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AfterSalesGoodsEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AfterSalesGoodsEntity record);

    int batchInsert(@Param("list") List<AfterSalesGoodsEntity> list);

    /**
     * 查询售后商品中的虚拟商品
     * @param afterSalesId 售后单id
     * @return List<AfterSalesGoodsEntity>
     */
    List<AfterSalesGoodsEntity> selectByAfterSalesIdAndIsVirtual(@Param("afterSalesId")Integer afterSalesId);

    /**
     * 查询虚拟商品售后数量
     * @param buyOrderSaleOrderGoodsDetailDtoList
     * @return
     */
    List<RBuyorderExpenseJSaleorderDto> expSaleorderAfterNum(@Param("buyOrderSaleOrderGoodsDetailDtoList")List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList);

    List<AfterSalesGoodsListResultDto> getAfterSalesGoodsListByAfterSalesIds(@Param("afterSalesIds") List<Integer> afterSalesIds);

    List<AfterSalesGoodsListResultDto> getAfterSalesGoodsListByAfterSalesIdAndGoodTypeSpecial(@Param("afterSalesId") Integer afterSalesId);
}