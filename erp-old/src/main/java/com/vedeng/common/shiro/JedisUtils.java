package com.vedeng.common.shiro;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.vedeng.common.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.exceptions.JedisException;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <b>Description:</b><br>
 * jedis工具类
 * 
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 *       <b>PackageName:</b> com.vedeng.common.util <br>
 *       <b>ClassName:</b> JedisUtils <br>
 *       <b>Date:</b> 2017年5月3日 下午6:13:02
 */
public class JedisUtils {

	public static final int INFINITE_TIMEOUT = 0;

	private static Logger logger = LoggerFactory.getLogger(JedisUtils.class);
	@Resource(name = "jedisPool")
	//private static JedisPool jedisPool = SpringContextHolder.getBean(JedisPool.class);
	private static JedisSentinelPool jedisPool = SpringContextHolder.getBean(JedisSentinelPool.class);

	// public static final String KEY_PREFIX = "redis.";

	/**
	 * 获取缓存
	 * 
	 * @param key
	 *            键
	 * @return 值
	 */
	public static String get(String key) {
		Transaction t = Cat.newTransaction("Redis", "JedisUtils"+"get|"+key);
		try {

			String value = null;
			Jedis jedis = null;
			try {
				jedis = getResource();
				if (jedis.exists(key)) {
					value = jedis.get(key);
					value = StringUtils.isNotBlank(value) && !"nil".equalsIgnoreCase(value) ? value : null;
					logger.debug("get {} = {}", key, value);
				}
			} catch (Exception e) {
				logger.error("get  {} = {}" + key, e);
				// logger.warn("get {} = {}", key, value, e);
			} finally {
				returnResource(jedis);
			}
			t.setSuccessStatus();
			return value;
		} catch (Throwable e) {
			t.setStatus(e);
			Cat.logError(e);
			throw e;
		} finally {
			t.complete();
		}
	}

	/**
	 *当redis数据为null时不能完全避免冲突
	 * @return
	 */
	public static long increaseExpressNo() {
		long value = System.currentTimeMillis();
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists("ExpressNo_bussinessNo")) {
				value = jedis.incr("ExpressNo_bussinessNo");
			}else{
				jedis.set("ExpressNo_bussinessNo",value+"");
			}
			returnResource(jedis);
		} catch (Exception e) {
			logger.error("get  {} = {}" + "ExpressNo_bussinessNo", e);
			// logger.warn("get {} = {}", key, value, e);
			returnBrokenResource(jedis);
		}
		return value;
	}

	/**
	 * 获取缓存
	 * 
	 * @param key
	 *            键
	 * @return 值
	 */
	public static Object getObject(String key) {
        Transaction t = Cat.newTransaction("Redis", "JedisUtils"+"get|"+key);
        try {

            Object value = null;
            Jedis jedis = null;
            try {
                jedis = getResource();
                if (jedis.exists(getBytesKey(key))) {
                    value = toObject(jedis.get(getBytesKey(key)));
                    logger.debug("getObject {} = {}", key, value);
                }
            } catch (Exception e) {
                logger.error("get  {} = {}" + key, e);
                // logger.warn("get {} = {}", key, value, e);
            } finally {
                returnResource(jedis);
            }
            t.setSuccessStatus();
            return value;
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
	}

	/**
	 * 设置缓存
	 * 
	 * @param key
	 *            键
	 * @param value
	 *            值
	 * @param cacheSeconds
	 *            超时时间，0为不超时
	 * @return
	 */
	public static String set(String key, String value, int cacheSeconds) {
        Transaction t = Cat.newTransaction("Redis", "JedisUtils"+"setExpire|"+key);
        try {
            String result = null;
            Jedis jedis = null;
            try {
                jedis = getResource();
                result = jedis.set(key, value);
                if (cacheSeconds != INFINITE_TIMEOUT) {
                    jedis.expire(key, cacheSeconds);
                }
                logger.debug("set {} = {}", key, value);
            } catch (Exception e) {
                logger.error("set {} = {}" + key + value, e);
            } finally {
                returnResource(jedis);
            }
            t.setSuccessStatus();
            return value;
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
	}






	/**
	 * 获取Map缓存
	 * 
	 * @param key
	 *            键
	 * @return 值
	 */
	public static Map<String, String> getMap(String key) {
        Transaction t = Cat.newTransaction("Redis", "JedisUtils"+"hgetAll|"+key);
        try {
            Map<String, String> value = null;
            Jedis jedis = null;
            try {
                jedis = getResource();
                if (jedis.exists(key)) {
                    value = jedis.hgetAll(key);
                    logger.debug("getMap {} = {}", key, value);
                }
            } catch (Exception e) {
                logger.error("getSet {} = {}" + key + value, e);
                // logger.warn("getMap {} = {}", key, value, e);
            } finally {
                returnResource(jedis);
            }
            t.setSuccessStatus();
            return value;
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
	}


	/**
	 * 删除缓存
	 * 
	 * @param key
	 *            键
	 * @return
	 */
	public static long del(String key) {
        Transaction t = Cat.newTransaction("Redis", "JedisUtils"+"del|"+key);
        try {
            long result = 0;
            Jedis jedis = null;
            try {
                jedis = getResource();
                if (jedis.exists(key)) {
                    result = jedis.del(key);
                    logger.debug("del {}", key);
                } else {
                    logger.debug("del {} not exists", key);
                }
            } catch (Exception e) {
                logger.error("del {}", key, e);
            } finally {
                returnResource(jedis);
            }
            t.setSuccessStatus();
            return result;
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
	}



	/**
	 * 缓存是否存在
	 * 
	 * @param key
	 *            键
	 * @return
	 */
	public static boolean exists(String key) {
        Transaction t = Cat.newTransaction("Redis", "JedisUtils"+"exists|"+key);
        try {
            boolean result = false;
            Jedis jedis = null;
            try {
                jedis = getResource();
                result = jedis.exists(key);
                logger.debug("exists {}", key);
            } catch (Exception e) {
                logger.error("exists {}", key, e);
            } finally {
                returnResource(jedis);
            }
            t.setSuccessStatus();
            return result;
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
	}



	/**
	 * 获取资源
	 * 
	 * @return
	 * @throws JedisException
	 */
	public static Jedis getResource() throws JedisException {
		Jedis jedis = null;
		try {
			jedis = jedisPool.getResource();
			// logger.debug("getResource.", jedis);
		} catch (JedisException e) {
			logger.error("getResource.", e);
			returnBrokenResource(jedis);
			throw e;
		}
		return jedis;
	}

	/**
	 * 归还资源
	 * 
	 * @param jedis
 	 */
	public static void returnBrokenResource(Jedis jedis) {
		if (jedis != null&&!jedis.isConnected() ) {
			jedisPool.returnBrokenResource(jedis);
		}
	}

	/**
	 * 释放资源
	 * 
	 * @param jedis
 	 */
	public static void returnResource(Jedis jedis) {
		if (jedis != null&&jedis.isConnected()) {
			jedisPool.returnResource(jedis);
		}
	}

	/**
	 * 获取byte[]类型Key
	 * 
	 * @param object
	 * @return
	 */
	public static byte[] getBytesKey(Object object) {
		if (object instanceof String) {
			try {
				return object.toString().getBytes("UTF-8");
			} catch (UnsupportedEncodingException e) {
				return null;
			}
		} else {
			return ObjectUtils.serialize(object);
		}
	}



	/**
	 * byte[]型转换Object
	 * 
	 * @param bytes
	 * @return
	 */
	public static Object toObject(byte[] bytes) {
		return ObjectUtils.unserialize(bytes);
	}


	/**
	 * <b>Description:</b><br>
	 * 反序列化list
	 * 
	 * @param bytes
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年5月5日 下午2:22:55
	 */
	public static List<?> toList(byte[] bytes) {
		return ObjectUtils.unserializeList(bytes);
	}

    public static Long incr(String key) {
        Transaction t = Cat.newTransaction("Redis", "JedisUtils"+"incr|"+key);
        try {
            long value = 0l;
            Jedis jedis = null;
            try {
                jedis = getResource();
                //if (jedis.exists(key)) {
                    value = jedis.incr(key);
                //}
            } catch (Exception e) {
                logger.error("get  {} = {}" + key, e);
                // logger.warn("get {} = {}", key, value, e);
                returnBrokenResource(jedis);
            } finally {
                returnResource(jedis);
            }
            t.setSuccessStatus();
            return value;
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
    }

	public static void expire(String key, int i) {
        Transaction t = Cat.newTransaction("Redis", "JedisUtils"+"expire|"+key);
        try {
            Jedis jedis = null;
            try {
                jedis = getResource();
                if (jedis.exists(key)) {
                     jedis.expire(key,i);
                }
            } catch (Exception e) {
                logger.error("get  {} = {}" + key, e);
                // logger.warn("get {} = {}", key, value, e);
                //returnBrokenResource(jedis);
            } finally {
                returnResource(jedis);
            }
            t.setSuccessStatus();
         } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
	}
}
