package com.vedeng.activiti.taskassign;

import com.pricecenter.constant.VerifyStatusEnum;
import com.vedeng.system.service.UserService;
import com.wms.service.WmsSurplusinService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 * @ClassName SurplusInOrderExecutionListener.java
 * @Description TODO
 * @createTime 2020年09月22日 15:37:00
 */
public class SurplusInOrderExecutionListener implements ExecutionListener {

    private static Logger logger = LoggerFactory.getLogger(SurplusInOrderExecutionListener.class);

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private UserService userService = (UserService) context.getBean("userService");
    private WmsSurplusinService wmsSurplusinService = (WmsSurplusinService) context.getBean("wmsSurplusinService");

    @Override
    public void notify(DelegateExecution execution) throws Exception {

        String businessKey = execution.getVariable("businessKey").toString();
        Integer surlushInOrderId =  Integer.valueOf(businessKey.split("_")[1]);
        boolean pass = Boolean.valueOf(execution.getVariable("pass").toString());

        wmsSurplusinService.updatesurplusInOrderAuditStatus(surlushInOrderId,pass ? VerifyStatusEnum.Approved.getValue() : VerifyStatusEnum.Reject.getValue());

        if(pass){
            wmsSurplusinService.putWmsSurplusInOrder(surlushInOrderId);
        }
    }
}
