package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeSalesVatPlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeStorageInCommand;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 其他入库  command和dto转化
 * @description:
 * @author: Neil.yang
 * @date: 2022/11/10
 */
@Mapper(componentModel = "spring")
public interface KingDeeStorageInCommandConvertor extends BaseCommandMapStruct<KingDeeStorageInCommand, KingDeeStorageInDto> {

    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtId")
    @Mapping(target = "FStockDirect", source = "FStockDirect")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "FSupplierId.FNumber", source = "FSupplierId")
    @Mapping(target = "FDeptId.FNumber", source = "FDeptId")
    @Mapping(target = "f_QZOK_KH.FNumber", source = "FQzokKh")
    @Override
    KingDeeStorageInCommand toCommand(KingDeeStorageInDto dto);

    @Mapping(target = "FQty", source = "FQty")
    @Mapping(target = "FPrice", source = "FPrice")
    @Mapping(target = "FAmount", source = "FAmount")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    @Mapping(target = "f_QZOK_SQLX", source = "FQzokSqlx")
    @Mapping(target = "f_QZOK_SFZF", source = "FQzokSfzf")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhId")
    @Mapping(target = "FMaterialId.FNumber", source = "FMaterialId")
    @Mapping(target = "FStockId.FNumber", source = "FStockId")
    @Mapping(target = "FTAXPRICE", source = "ftaxprice")
    @Mapping(target = "FENTRYTAXRATE", source = "fentrytaxrate")
    @Mapping(target = "FENTRYID", source = "FEntryId")
    KingDeeStorageInCommand.FEntity toCommand(KingDeeStorageInDetailDto dto);
}
