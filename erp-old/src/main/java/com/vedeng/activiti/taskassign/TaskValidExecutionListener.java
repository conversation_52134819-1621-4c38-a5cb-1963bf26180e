package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.service.KingDeeCustomerApiService;
import com.vedeng.erp.kingdee.service.KingDeeSupplierApiService;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.SyncDataTypeEnum;
import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.SyncDataErpDto;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.*;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsResponse;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.*;

public class TaskValidExecutionListener implements ExecutionListener {
    public static final String TRADER_SUPPLIER_VERIFY = "traderSupplierVerify";
    private static final Logger LOGGER = LoggerFactory.getLogger(TaskValidExecutionListener.class);

    public static final String BUYORDER_VERIFY = "buyorderVerify";
    public static final String BUYORDER_EXPENSE_VERIFY = "buyorderExpenseVerify";
    public static final String BUSINESS_KEY = "businessKey";
    public static final String CLOSE_QUOTEORDER_VERIFY = "closeQuoteorderVerify";
    public static final String CONTRACT_RETURN_VERIFY = "contractReturnVerify";
    public static final String SALEORDER_INFO = "saleorderInfo";
    public static final String SALEORDER_VERIFY = "saleorderVerify";
    public static final String EDIT_SALEORDER_VERIFY = "editSaleorderVerify";
    public static final String BD_ORDER_AUTO_VERIFY = "bd_order_auto_verify";
    public static final String SALEORDER_MODIFY_AUDIT = "saleorderModifyAudit";
    public static final String HC_ORDER_AUTO_VERIFY = "hc_order_auto_verify";
    public static final String TRADER_CUSTOMER_VERIFY = "traderCustomerVerify";
    public static final String TRADER_SUPPLIER_VO = "traderSupplierVo";
    public static final String TRADER_CUSTOMER_VO = "traderCustomerVo";
    public static final String EXPENSE_AFTER_SALES_VERIFY = "expenseAfterSalesVerify";
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private final ActionProcdefService actionProcdefService = (ActionProcdefService) context.getBean("actionProcdefService");

    private final VerifiesRecordService verifiesRecordService = (VerifiesRecordService) context.getBean("verifiesRecordService");

    private final TraderMapper traderMapper = (TraderMapper) context.getBean("traderMapper");

    private final SaleorderService saleorderService = (SaleorderService) context.getBean("saleorderService");

    private final QuoteService quoteService = (QuoteService) context.getBean("quoteService");

    private final BussinessChanceService bussinessChanceService = (BussinessChanceService) context.getBean("bussinessChanceService");

    private final BuyorderService buyorderService = (BuyorderService) context.getBean("buyorderService");

    private final BuyorderExpenseApiService buyorderExpenseApiService = (BuyorderExpenseApiService) context.getBean("buyorderExpenseServiceImpl");

    private final ExpenseAfterSalesApiService expenseAfterSalesApiService = (ExpenseAfterSalesApiService) context.getBean("expenseAfterSalesServiceImpl");

    private final KingDeeCustomerApiService kingDeeCustomerApiService = (KingDeeCustomerApiService) context.getBean("kingDeeCustomerServiceImpl");

    private final WMSInterfaceFactory wmsInterfaceFactory = (WMSInterfaceFactory) context.getBean("wmsInterfaceFactory");

    private SaleorderMapper saleorderMapper = (SaleorderMapper) context.getBean("saleorderMapper");

    private LogicalSaleorderChooseService logicalSaleorderChooseService = (LogicalSaleorderChooseService) context.getBean("logicalSaleorderChooseService");

    private TraderCustomerApiService traderCustomerApiService = (TraderCustomerApiService) context.getBean("traderCustomerBaseServiceImpl");

    private TraderSupplierApiService traderSupplierApiService = (TraderSupplierApiService) context.getBean("traderSupplierServiceImpl");

    private KingDeeSupplierApiService kingDeeSupplierApiService = (KingDeeSupplierApiService) context.getBean("kingDeeSupplierServiceImpl");

    /**
     * 销售订单电子签章实现类
     */
    private final AbstractElectronicSignHandle saleOrderElectronicSignHandle = (AbstractElectronicSignHandle) context.getBean("saleOrderElectronicSignHandle");

    /**
     * 采购订单电子签章实现类
     */
    private final AbstractElectronicSignHandle buyOrderElectronicSignHandle = (AbstractElectronicSignHandle) context.getBean("buyOrderElectronicSignHandle");
    /**
     * 采购费用单电子签章实现类
     */
    private final AbstractElectronicSignHandle buyOrderExpenseElectronicSignHandle = (AbstractElectronicSignHandle) context.getBean("buyOrderExpenseElectronicSignHandle");

    private final SaleorderDataSyncService saleorderDataSyncService = (SaleorderDataSyncService) context.getBean("saleorderDataSyncService");

    //SaleOrderServiceImpl
    private final SaleOrderApiService saleOrderApiService = (SaleOrderApiService) context.getBean("saleOrderServiceImpl");


    //待同步数据埋点服务，销售订单审核通过时，只要是子公司，均自动判断是否生成物流确认单信息
    private final SyncDataErpApiService syncDataErpApiService = (SyncDataErpApiService) context.getBean("syncDataErpApiServiceImpl");


    /**
     * 销售订单(账期类型)付款计划
     */
    public static final List PAYMENT_TYPE_FOR_PAYMENT_DAYS = Collections.unmodifiableList(Lists.newArrayList(
            OrderConstant.PREPAY_80_PERCENT,
            OrderConstant.PREPAY_50_PERCENT ,
            OrderConstant.PREPAY_30_PERCENT,
            OrderConstant.PREPAY_0_PERCENT));

    /**
     * 根据穿参通用回写主表中状态
     */
    @Override
    public void notify(DelegateExecution execution) throws Exception {
        //审核完成后修主表参数状态
        if ((String) execution.getVariable("tableName") != null) {
            String tableName = (String) execution.getVariable("tableName");
            String id = (String) execution.getVariable("id");
            Integer idValue = (Integer) execution.getVariable("idValue");
            String key = (String) execution.getVariable("key");
            Integer value = (Integer) execution.getVariable("value");
            Integer db = (Integer) execution.getVariable("db");
            actionProcdefService.updateInfo(tableName, id, idValue, key, value, db);
            if (null != execution.getVariable("key1")) {
                String key1 = (String) execution.getVariable("key1");
                Integer value1 = (Integer) execution.getVariable("value1");
                actionProcdefService.updateInfo(tableName, id, idValue, key1, value1, db);
            }
        }
        //添加审核对应主表的审核状态
        if ((Integer) execution.getVariable("verifyStatus") != null) {
            verifiesRecordService.saveVerifiesInfo(execution.getId(), (Integer) execution.getVariable("verifyStatus"));
        } else {
            verifiesRecordService.saveVerifiesInfo(execution.getId(), 1);
        }

        String processDefinitionKey = (String) execution.getVariable("processDefinitionKey");
        Integer idValue = (Integer) execution.getVariable("idValue");

        //如果是供应商\客户资料审核通过 则下传至WMS

        if ((TRADER_CUSTOMER_VERIFY.equals(processDefinitionKey) || TRADER_SUPPLIER_VERIFY.equals(processDefinitionKey)) && "审核完成".equals(execution.getCurrentActivityName())) {
            Trader trader = null;
            if (TRADER_CUSTOMER_VERIFY.equals(processDefinitionKey)) {
                TraderCustomerVo traderBaseInfo = (TraderCustomerVo) execution.getVariable(TRADER_CUSTOMER_VO);
                trader = traderMapper.getTraderByTraderId(traderBaseInfo.getTraderId());
                //客户
                trader.setCustomerType(1);

                // 客户审核通过推送金蝶
                KingDeeCustomerDto kingDeeCustomerInfo = traderCustomerApiService.getKingDeeCustomerInfo(traderBaseInfo.getTraderCustomerId());
                kingDeeCustomerApiService.register(kingDeeCustomerInfo);
                LOGGER.info("客户信息审核通过,推送至金蝶，客户id：{}", traderBaseInfo.getTraderCustomerId());
            }
            if (TRADER_SUPPLIER_VERIFY.equals(processDefinitionKey)) {
                TraderSupplierVo traderBaseInfo = (TraderSupplierVo) execution.getVariable(TRADER_SUPPLIER_VO);
                trader = traderMapper.getTraderByTraderId(traderBaseInfo.getTraderId());
                //供应商
                trader.setCustomerType(2);

                // 推送供应商信息至金蝶
                KingDeeSupplierDto kingDeeSupplierInfo = traderSupplierApiService.getKingDeeSupplierInfo(traderBaseInfo.getTraderSupplierId());
                kingDeeSupplierApiService.register(kingDeeSupplierInfo);
                LOGGER.info("供应商信息审核通过,推送至金蝶，供应商id：{}", traderBaseInfo.getTraderSupplierId());
            }

            try {
                LOGGER.info("WMS客户资料下传接口的请求:" + JSON.toJSONString(trader));
                WmsInterface putCustomerInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_CUSTOMER);
                WmsResponse response = putCustomerInterface.request(trader);
                LOGGER.info("WMS客户资料下传接口的响应:" + JSON.toJSONString(response));
            } catch (Exception e) {
                LOGGER.error("供应商客户资料审核通过,请求下传WMS接口报错", e);
            }
        }


        //订单审核通过后更新快照信息
        int OrderId = 0;
        try {
            //销售单审核通过
            if (SALEORDER_VERIFY.equals(processDefinitionKey)) {
                OrderId = idValue;
                //更新商品快照信息
                saleorderService.updateSaleorderGoodsSnapshotInfo(OrderId);
            }
            //HC订单自动审核通过
            if (HC_ORDER_AUTO_VERIFY.equals(processDefinitionKey) || BD_ORDER_AUTO_VERIFY.equals(processDefinitionKey)) {
                String businessKey = execution.getVariable(BUSINESS_KEY).toString();
                OrderId = Integer.parseInt(businessKey.split("_")[4]);
                //更新商品快照信息
                saleorderService.updateSaleorderGoodsSnapshotInfo(OrderId);
            }
            //采购订单审核通过
            if (BUYORDER_VERIFY.equals(processDefinitionKey)) {
                String businessKey = execution.getVariable(BUSINESS_KEY).toString();
                OrderId = Integer.parseInt(businessKey.split("_")[1]);
                //更新商品快照信息
                buyorderService.updateBuyorderGoodsSnapshotInfo(OrderId);
                //更新主表审核状态
                buyorderService.updateVerifyStatus(OrderId, 1);
                // 更新同时生成的采购费用单
                BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(OrderId);
                if (Objects.nonNull(buyorderExpenseDto)) {
                    buyorderExpenseApiService.audit(buyorderExpenseDto.getBuyorderExpenseId());
                }

            }
            //采购费用订单订单审核通过
            if (BUYORDER_EXPENSE_VERIFY.equals(processDefinitionKey)) {
                String businessKey = execution.getVariable(BUSINESS_KEY).toString();
                OrderId = Integer.parseInt(businessKey.split("_")[1]);
                //更新主表审核状态
                buyorderExpenseApiService.audit(OrderId);
//                buyorderExpenseApiService.updateDeliveryStatusAndArrivalStatus(OrderId);
            }
            // 采购费用售后订单审核通过
            if (EXPENSE_AFTER_SALES_VERIFY.equals(processDefinitionKey)) {
                String businessKey = execution.getVariable(BUSINESS_KEY).toString();
                OrderId = Integer.parseInt(businessKey.split("_")[1]);
                //更新主表审核状态
                expenseAfterSalesApiService.audit((long) OrderId);
            }
        } catch (Exception e) {
            LOGGER.error("更新订单商品快照信息失败，订单id：{}，错误：", OrderId, e);
        }
        // 订单审核通过或者修改订单审核通过后  更新订单的合同模板
        if (SALEORDER_VERIFY.equals(processDefinitionKey) || EDIT_SALEORDER_VERIFY.equals(processDefinitionKey)
                || SALEORDER_MODIFY_AUDIT.equals(processDefinitionKey) || BD_ORDER_AUTO_VERIFY.equals(processDefinitionKey)) {
            int saleorderId = 0;
            if (SALEORDER_VERIFY.equals(processDefinitionKey)) {
                saleorderId = idValue;
            } else if (BD_ORDER_AUTO_VERIFY.equals(processDefinitionKey)) {
                saleorderId = idValue;
            } else {
                //根据订单修改申请id查询订单id
                saleorderId = saleorderService.getSaleorderIdByOrderModifyId(idValue);
            }
            BaseCompanyInfoDto baseCompanyInfoDto = saleOrderApiService.checkSignCompanyInfoBySaleOrderId(saleorderId);
            if(baseCompanyInfoDto !=null){//如果是子公司订单
                SaleorderInfoDto saleorderInfoDto  = saleOrderApiService.getBySaleOrderId(saleorderId);
                //确认单自动生成同步业务
                String dataType = SyncDataTypeEnum.SALEORDER_AUTO_COMFIRM_ORDER.getDataType();
                //需要同步给子公司
                SyncDataErpDto syncDataErpDto = new SyncDataErpDto();
                syncDataErpDto.setBusinessNo(saleorderInfoDto.getSaleorderNo());
                syncDataErpDto.setTargetErp(baseCompanyInfoDto.getCompanyShortName());
                syncDataErpDto.setBusinessType(dataType);
                syncDataErpDto.setRequestContent("");
                syncDataErpDto.setProcessStatus(ErpConst.ZERO);
                syncDataErpApiService.insert(syncDataErpDto);

            }


            try {
                // 调用电子签章
                BusinessInfo businessInfo = new BusinessInfo();
                businessInfo.setOperator("系统");
//                ElectronicSignParam electronicSignParam = ElectronicSignParam
//                        .builder()
//                        .flowType(2)
//                        .saleOrderId(saleorderId)
//                        .businessInfo(businessInfo)
//                        .electronicSignBusinessEnums(ElectronicSignBusinessEnums.SALE_ORDER)
//                        .build();
                ElectronicSignParam electronicSignParam = saleOrderElectronicSignHandle.buildElectronicSignParam(saleorderId,businessInfo);
                saleOrderElectronicSignHandle.electronicSign(electronicSignParam);
            } catch (Exception e) {
                LOGGER.error("电子签章：调用电子签章异常，销售订单id：{}，错误：", saleorderId, e);
            }
        }

        // 采购单审核通过或者修改采购单审核通过后，调用电子签章
        if (BUYORDER_VERIFY.equals(processDefinitionKey)) {
            int buyOrderId = 0;
            try {
                String businessKey = execution.getVariable(BUSINESS_KEY).toString();

                buyOrderId = Integer.parseInt(businessKey.split("_")[1]);
                // 调用电子签章
                BusinessInfo businessInfo = new BusinessInfo();
                businessInfo.setOperator("系统");

                ElectronicSignParam electronicSignParam = buyOrderElectronicSignHandle.buildElectronicSignParam(buyOrderId,businessInfo);
                buyOrderElectronicSignHandle.electronicSign(electronicSignParam);
//                ElectronicSignParam electronicSignParam = ElectronicSignParam
//                        .builder()
//                        .flowType(2)
//                        .buyOrderId(buyOrderId)
//                        .businessInfo(businessInfo)
//                        .electronicSignBusinessEnums(ElectronicSignBusinessEnums.BUY_ORDER)
//                        .build();
//                buyOrderElectronicSignHandle.electronicSign(electronicSignParam);
            } catch (Exception e) {
                LOGGER.error("调用电子签章异常，采购订单id：{}，错误：", buyOrderId, e);
            }
        }
        // 采购费用单审核通过或者修改采购单审核通过后，调用电子签章
        if (BUYORDER_EXPENSE_VERIFY.equals(processDefinitionKey)) {
            int buyOrderExpenseId = 0;
            try {
                String businessKey = execution.getVariable(BUSINESS_KEY).toString();

                buyOrderExpenseId = Integer.parseInt(businessKey.split("_")[1]);
                // 调用电子签章
                BusinessInfo businessInfo = new BusinessInfo();
                businessInfo.setOperator("系统");
                ElectronicSignParam electronicSignParam = ElectronicSignParam
                        .builder()
                        .flowType(2)
                        .buyOrderExpenseId(buyOrderExpenseId)
                        .businessInfo(businessInfo)
                        .electronicSignBusinessEnums(ElectronicSignBusinessEnums.BUY_ORDER_EXPENSE)
                        .build();
                buyOrderExpenseElectronicSignHandle.electronicSign(electronicSignParam);
            } catch (Exception e) {
                LOGGER.error("调用电子签章异常，采购费用订单id：{}，错误：", buyOrderExpenseId, e);
            }
        }

        //VDERP-5010 关闭报价审核通过后，联动关闭商机
        if (CLOSE_QUOTEORDER_VERIFY.equals(processDefinitionKey)) {
            Quoteorder quoteInfoByKey = quoteService.getQuoteInfoByKey(idValue);
            if (Objects.nonNull(quoteInfoByKey.getBussinessChanceId())) {
                BussinessChance bussinessChance = bussinessChanceService.relateCloseBussChance(quoteInfoByKey.getBussinessChanceId(), "SYS_AUTO_CLOSE_TYPE_2", 2);
                LOGGER.info("关闭报价审核通过后，联动关闭商机：" + JSON.toJSONString(bussinessChance));
            }
        }

        //合同回传
        try {
            if (CONTRACT_RETURN_VERIFY.equals(processDefinitionKey)) {
                Saleorder saleOrderInfo = (Saleorder) execution.getVariable(SALEORDER_INFO);
                //除集采线下订单 所有线上订单 (JCF、JCO、BD、HC、DH 剔除)
                // 发送短信
                if (!OrderConstant.ORDER_TYPE_JCF.equals(saleOrderInfo.getOrderType()) && !OrderConstant.ORDER_TYPE_JCO.equals(saleOrderInfo.getOrderType())
                        && !OrderConstant.ORDER_TYPE_BD.equals(saleOrderInfo.getOrderType()) && !OrderConstant.ORDER_TYPE_HC.equals(saleOrderInfo.getOrderType())
                        && !OrderConstant.ORDER_TYPE_DH.equals(saleOrderInfo.getOrderType())) {
                    saleOrderInfo.setConfirmStatus(ErpConst.ONE);
                    saleOrderInfo.setConfirmTime(new Date());
                    saleOrderInfo.setModTime(System.currentTimeMillis());
                    saleorderService.updateConfirmTimeAndStatus(saleOrderInfo);
                }
                // 对T_SALEORDER_DATA（CONTRACT_VERIFY_STATUS）做实时更新
                saleorderDataSyncService.syncContractStatusBySaleorderId(saleOrderInfo.getSaleorderId(),"valid");
                // 如果是销售订单（账期类型）,则触发下发wms
                Saleorder saleOrder = saleorderMapper.getSaleOrderById(saleOrderInfo.getSaleorderId());
                if (Objects.nonNull(saleOrder) && PAYMENT_TYPE_FOR_PAYMENT_DAYS.contains(saleOrder.getPaymentType()) && OrderConstant.ORDER_STATUS_PROCESSING.equals(saleOrder.getStatus()) &&
                        OrderConstant.ORDER_ALL_PAYMENT.equals(saleOrder.getPaymentStatus()) && OrderConstant.ORDER_STATUS_VALID.equals(saleOrder.getValidStatus()) &&
                        !OrderConstant.ORDER_STATUS_INIT0.equals(saleOrder.getTakeTraderAddressId()) && Objects.nonNull(saleOrder.getTakeTraderAddress()) &&
                        OrderConstant.UN_LOCKED.equals(saleOrder.getLockedStatus())) {
                    LOGGER.info("合同回传且审核通过，则触发销售订单（账期类型）下发WMS操作：SaleorderNo:{},OrderType:{},Status:{},PaymentStatus:{}",
                            saleOrder.getSaleorderNo(), saleOrder.getOrderType(), saleOrder.getStatus(), saleOrder.getPaymentStatus());
                    try {
                        logicalSaleorderChooseService.chooseLogicalSaleorder(saleOrder, null);
                    } catch (Exception e) {
                        LOGGER.error("合同回传且审核通过，则触发销售订单（账期类型）下发WMS操作，发生异常：订单号={}，异常信息={}", saleOrder.getSaleorderNo(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("合同回传时更新状态失败：异常信息", e);
        }


    }
}

