package com.vedeng.erp.buyorder.domain.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class PeerListBuyorderGoods implements Serializable{

	private static final long serialVersionUID = 1L;

	@ExcelIgnore
    private Integer index;

    /**
     * 存储条件
     */
    @ExcelIgnore
    private String storageCondition;
    /**
     * 快递详情号
     */
//    @ExcelProperty(value = "物流明细ID",index = 11)
    @ExcelProperty(index = 11)
    private Integer expressDetailId;

    /**
     * 是否是医疗器械
     */
    @ExcelIgnore
    private String medicalInstrumentCatalogIncluded;

    @ExcelIgnore
    private Integer isManageVedengCode;

    @ExcelIgnore
    private Integer isEnableValidityPeriod;

    /**
     * 产品类型
     */
    @ExcelIgnore
    private Integer spuType;

    /**
     * 生产许可证
     */
//    @ExcelProperty(value = "生产企业许可证号/备案凭证号",index = 8)
    @ExcelProperty(index = 8)
    private String productCompanyLicence;

//    @ExcelProperty(value = "数量",index = 4)
    @ExcelProperty(index = 4)
    private Integer arrivalCount;

    @ExcelIgnore
    private Integer dnum;
    @ExcelIgnore
	private Integer buyorderGoodsId;
    @ExcelIgnore
    private Integer buyorderId;
    @ExcelIgnore
    private Integer goodsId;

//    @ExcelProperty(value = "订货号",index = 1)
    @ExcelProperty(index = 1)
    private String sku;

//    @ExcelProperty(value = "产品名称",index = 2)
    @ExcelProperty(index = 2)
    private String goodsName;



//    @ExcelProperty(value = "规格（型号）",index = 3)
    @ExcelProperty(index = 3)
    private String model;

//    @ExcelProperty(value = "单位",index = 5)
    @ExcelProperty(index = 5)
    private String unitName;

    /**
     * 生产批次号/序列号
     */
    @ExcelProperty(index = 6)
//    @ExcelProperty(value = "生产批号/序列号",index = 6)
    private String batchNumber;

/*    *//**
     * 规格
     *//*
    private String spec;*/

    /**
     * 注册证号/备案凭证号
     */
//    @ExcelProperty(value = "注册证号/备案凭证号",index = 9)
    @ExcelProperty(index = 9)
    private String registrationNumber;

    /**
     * 生产企业名称
     */
//    @ExcelProperty(value = "生产企业",index = 7)
    @ExcelProperty(index = 7)
    private String manufacturerName;
    @ExcelIgnore
    private Integer oldNum;
    @ExcelIgnore
    private Integer expressId;
    @ExcelIgnore
    private String brandName;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @com.alibaba.excel.annotation.format.DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ExcelProperty(index=12)
    private Date manufactureDateTime;

    /**
     * 失效日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @com.alibaba.excel.annotation.format.DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(index = 13)
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date invalidDateTime;
}