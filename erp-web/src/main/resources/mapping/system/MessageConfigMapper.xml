<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.MessageConfigMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.MessageConfig" >
    <id column="MESSAGE_CONFIG_ID" property="messageConfigId" jdbcType="INTEGER" />
    <result column="ACTION_ID" property="actionId" jdbcType="INTEGER" />
    <result column="CONDITION_TYPE" property="conditionType" jdbcType="BIT" />
    <result column="CONDITION_FUNCTION_ID" property="conditionFunctionId" jdbcType="INTEGER" />
    <result column="USER_IDS" property="userIds" jdbcType="VARCHAR" />
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    MESSAGE_CONFIG_ID, ACTION_ID, CONDITION_TYPE, CONDITION_FUNCTION_ID, USER_IDS, TITLE, 
    CONTENT, IS_ENABLE, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_MESSAGE_CONFIG
    where MESSAGE_CONFIG_ID = #{messageConfigId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_MESSAGE_CONFIG
    where MESSAGE_CONFIG_ID = #{messageConfigId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.system.model.MessageConfig" >
    insert into T_MESSAGE_CONFIG (MESSAGE_CONFIG_ID, ACTION_ID, CONDITION_TYPE, 
      CONDITION_FUNCTION_ID, USER_IDS, TITLE, 
      CONTENT, IS_ENABLE, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{messageConfigId,jdbcType=INTEGER}, #{actionId,jdbcType=INTEGER}, #{conditionType,jdbcType=BIT}, 
      #{conditionFunctionId,jdbcType=INTEGER}, #{userIds,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{isEnable,jdbcType=BIT}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.system.model.MessageConfig" >
    insert into T_MESSAGE_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="messageConfigId != null" >
        MESSAGE_CONFIG_ID,
      </if>
      <if test="actionId != null" >
        ACTION_ID,
      </if>
      <if test="conditionType != null" >
        CONDITION_TYPE,
      </if>
      <if test="conditionFunctionId != null" >
        CONDITION_FUNCTION_ID,
      </if>
      <if test="userIds != null" >
        USER_IDS,
      </if>
      <if test="title != null" >
        TITLE,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="messageConfigId != null" >
        #{messageConfigId,jdbcType=INTEGER},
      </if>
      <if test="actionId != null" >
        #{actionId,jdbcType=INTEGER},
      </if>
      <if test="conditionType != null" >
        #{conditionType,jdbcType=BIT},
      </if>
      <if test="conditionFunctionId != null" >
        #{conditionFunctionId,jdbcType=INTEGER},
      </if>
      <if test="userIds != null" >
        #{userIds,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.system.model.MessageConfig" >
    update T_MESSAGE_CONFIG
    <set >
      <if test="actionId != null" >
        ACTION_ID = #{actionId,jdbcType=INTEGER},
      </if>
      <if test="conditionType != null" >
        CONDITION_TYPE = #{conditionType,jdbcType=BIT},
      </if>
      <if test="conditionFunctionId != null" >
        CONDITION_FUNCTION_ID = #{conditionFunctionId,jdbcType=INTEGER},
      </if>
      <if test="userIds != null" >
        USER_IDS = #{userIds,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where MESSAGE_CONFIG_ID = #{messageConfigId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.system.model.MessageConfig" >
    update T_MESSAGE_CONFIG
    set ACTION_ID = #{actionId,jdbcType=INTEGER},
      CONDITION_TYPE = #{conditionType,jdbcType=BIT},
      CONDITION_FUNCTION_ID = #{conditionFunctionId,jdbcType=INTEGER},
      USER_IDS = #{userIds,jdbcType=VARCHAR},
      TITLE = #{title,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where MESSAGE_CONFIG_ID = #{messageConfigId,jdbcType=INTEGER}
  </update>
</mapper>