<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="/static/vue/vd-ui/ui.css">
<link rel="stylesheet" href="/static/vue/receiptnotice/list.css">
<title>人员管理</title>

<div class="user-container list-container" id="page-container">

    <div class="list-top">
        <h1 class="title">人员管理</h1>
        <ui-button type="primary" width="100px" @click="addRow">新建</ui-button>
    </div>

    <div class="list-filter form-wrap">
        <div class="list-row">
            <div class="margin-r20">
                <ui-form-item label="用户名" label-width="58px">
                    <ui-input v-model="filter_user" clearable></ui-input> 
                </ui-form-item>
            </div>
            <div class="margin-r20">
                <ui-form-item label="AED销售" label-width="74px">
                    <ui-select 
                        :remote="true" 
                        :remote-info="saleRemoteInfo"
                        placeholder="全部" 
                        v-model="filter_sale" 
                        :default-label="filter_saleName"
                        clearable 
                        @change="handlerChange"
                    ></ui-select>
                </ui-form-item>
            </div>
            <div class="filter-btns">
                <ui-button type="primary" width="100px" @click="handlerSearch">搜索</ui-button>
                <ui-button type="link" class="margin-l10" width="100px" @click="handlerReset">重置</ui-button>
            </div>
        </div>
    </div>

    <div class="list-wrap">
        <el-table 
            ref="userData" 
            :data="list" 
            border size="small"
            :header-cell-style="{ textAlign: 'center', background: '#ECF1F5' }"
            :cell-style="{ textAlign: 'center' }"
            style="width: 100%"
        >
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="erpUsername" label="用户名" width=""></el-table-column>
            <el-table-column prop="erpRealName" label="员工姓名" width=""></el-table-column>
            <el-table-column prop="aedUsername" label="AED销售"></el-table-column>
            <el-table-column prop="creatorRealName" label="创建人" width="180"></el-table-column>
            <el-table-column prop="addTime" label="创建时间" width="180"></el-table-column>
            <el-table-column fixed="right" label="操作" width="120">
                <template slot-scope="scope">
                    <el-button @click.native.prevent="editRow(scope.row)" type="text" size="mini">编辑</el-button>
                    <el-button @click.native.prevent="deleteRow(scope.row, scope.$index)" type="text" size="mini">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="list-pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageNum"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>
    </div>

    <ui-dialog
        :title="editData? '编辑': '新建'"
        :visible.sync="isShowEditUser"
        width="680px"
        align="center"
        scroll="in"
    >
        <div v-if="isShowEditUser" class="form-wrap label-width-2">
            <ui-form-item label="用户" :must="true">
                <ui-select
                    :remote="true" 
                    :remote-info="userRremoteInfo"
                    placeholder="请选择" 
                    v-model="user" 
                    clearable 
                    :disabled="editData"
                    :default-label="defaultUserName"
                    valid="editUser_user"
                ></ui-select>
            </ui-form-item>
            <ui-form-item label="AED销售" :must="true">
                <ui-select
                    :remote="true" 
                    :remote-info="saleRemoteInfo" 
                    placeholder="请选择" 
                    v-model="sale" 
                    clearable 
                    :default-label="defaultSale"
                    valid="editUser_sale"
                ></ui-select>
            </ui-form-item>
        </div>
        <template slot="footer">
            <div class="dlg-form-footer">
                <ui-button @click="confirm" type="primary">保存</ui-button>
                <ui-button @click="isShowEditUser = false" class="close">取消</ui-button>
            </div>
        </template>
    </ui-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/receiptnotice/user.js?rnd=${resourceVersionKey}"></script>