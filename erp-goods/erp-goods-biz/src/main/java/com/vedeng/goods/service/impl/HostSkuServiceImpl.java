package com.vedeng.goods.service.impl;

import com.alibaba.fastjson.JSON;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.goods.dto.*;
import com.vedeng.goods.service.HostSkuService;
import com.vedeng.goods.vo.OpHostWordVo;
import com.vedeng.goods.feign.op.RemoteOpApiService;
import com.vedeng.goods.mapper.NewCoreSkuGenerateMapper;
import com.vedeng.goods.mapper.NewCoreSpuGenerateMapper;
import com.vedeng.goods.mapper.VHSHostSkuMapper;
import com.vedeng.goods.mapper.VHostWordMapper;
import com.vedeng.op.api.dto.category.QueryCategoryRequest;
import com.vedeng.op.api.dto.hotword.HotWordListDTO;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName HostSkuServiceImpl.java
 * @Description TODO 热词SKU相关业务
 * @createTime 2021年12月31日 15:33:00
 */
@Service
public class HostSkuServiceImpl implements HostSkuService {

    public static final Logger logger = LoggerFactory.getLogger(HostSkuServiceImpl.class);

    public static final String OP_USER = "opUser";

    @Autowired
    private NewCoreSpuGenerateMapper newCoreSpuGenerateMapper;

    @Autowired
    private NewCoreSkuGenerateMapper newCoreSkuGenerateMapper;

    @Autowired
    private VHostWordMapper hostWordMapper;

    @Autowired
    private VHSHostSkuMapper hostSkuMapper;

    @Autowired
    private RemoteOpApiService remoteOpApiService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveHostByopPushMsg(VHostWordDTO hostWordDTO, List<String> skuList) {
        logger.info("saveHostByopPushMsg info:{},skuList:{}",hostWordDTO.toString(),skuList.toString());
        Integer opHostWordId = hostWordDTO.getOpHostWordId();
        Date date = new Date();
        hostWordDTO.setUpdater(OP_USER);
        hostWordDTO.setModeTime(date);

        VHostWordDTO vHostWord = hostWordMapper.getHostWordByOpHostId(opHostWordId);
        if(vHostWord != null){
            hostWordDTO.setHostWordId(vHostWord.getHostWordId());
            hostWordMapper.updateByPrimaryKeySelective(hostWordDTO);
        }else{
            hostWordDTO.setCreator(OP_USER);
            hostWordDTO.setAddTime(date);
            hostWordMapper.insertSelective(hostWordDTO);
        }

        VHSHostSkuDTOExample searchExample = new VHSHostSkuDTOExample();
        searchExample.createCriteria().andOpHostWordIdEqualTo(opHostWordId).andIsDeleteEqualTo(0);
        List<VHSHostSkuDTO> oldSkuDto = hostSkuMapper.selectByExample(searchExample);

        List<String> oldSkuList = oldSkuDto.stream().map( VHSHostSkuDTO::getSku).collect(Collectors.toList());
        List<String> needDeleteSku = oldSkuList.stream().filter(old -> !skuList.contains(old)).distinct().collect(Collectors.toList());
        logger.info("saveHostByopPushMsg needDeleteSku:{}",needDeleteSku.toString());

        if(!CollectionUtils.isEmpty(needDeleteSku)){
             VHSHostSkuDTOExample updateExample = new  VHSHostSkuDTOExample();
            updateExample.createCriteria().andOpHostWordIdEqualTo(opHostWordId).andIsDeleteEqualTo(0).andSkuIn(needDeleteSku);
             VHSHostSkuDTO updateSku = new  VHSHostSkuDTO();
            updateSku.setIsDelete(1);
            updateSku.setModeTime(date);
            updateSku.setUpdater(OP_USER);
            hostSkuMapper.updateByExampleSelective(updateSku,updateExample);
        }

        List<String> needSaveSku = skuList.stream().filter(newSku -> !oldSkuList.contains(newSku)).distinct().collect(Collectors.toList());
        logger.info("saveHostByopPushMsg needSaveSku:{}",needSaveSku.toString());
        for (String sku : needSaveSku) {
             VHSHostSkuDTO vhsHostSku = hostSkuMapper.getSkuHostByOpIdAndSku(sku,opHostWordId);
            if(vhsHostSku != null){
                 VHSHostSkuDTO update = new  VHSHostSkuDTO();
                update.setHostSkuId(vhsHostSku.getHostSkuId());
                update.setIsDelete(0);
                update.setUpdater(OP_USER);
                update.setModeTime(date);
                hostSkuMapper.updateByPrimaryKeySelective(update);
            }else{
                 VHSHostSkuDTO insert = new  VHSHostSkuDTO();
                insert.setSku(sku);
                insert.setOpHostWordId(opHostWordId);
                insert.setAddTime(date);
                insert.setModeTime(date);
                insert.setCreator(OP_USER);
                insert.setUpdater(OP_USER);
                insert.setIsDelete(0);
                hostSkuMapper.insertSelective(insert);
            }
        }

    }

    @Override
    public List< VHostWordDTO> queryHotWordByCategory(@NonNull Integer spuId, @NonNull Integer skuId) {

         CoreSkuGenerate coreSkuGenerate = newCoreSkuGenerateMapper.selectByPrimaryKey(skuId);
         CoreSpuGenerate coreSpuGenerate = newCoreSpuGenerateMapper.selectByPrimaryKey(spuId);
        Integer categoryId = coreSpuGenerate.getCategoryId();
        if (categoryId == null) {
            return Collections.emptyList();
        }
        List< VHostWordDTO> result = new ArrayList<>();
        Map<Integer,  VHostWordDTO> collect = new HashMap<>();
        try {
            // 调用op接口
            QueryCategoryRequest queryCategoryRequest = new QueryCategoryRequest();
            queryCategoryRequest.setCategoryId(categoryId);
            RestfulResult<List<HotWordListDTO>> opHotWordDTORestfulResult = remoteOpApiService.queryHotWordListByCategoryId(queryCategoryRequest);
            logger.info("op接口queryHotWordByCategoryId 入参:{} 返回:{}",JSON.toJSONString(queryCategoryRequest), JSON.toJSONString(opHotWordDTORestfulResult));
            if (opHotWordDTORestfulResult.isSuccess()) {
                List<HotWordListDTO> opHotWordDtos = opHotWordDTORestfulResult.getData();
                if (CollectionUtils.isEmpty(opHotWordDtos)) {
                    opHotWordDtos = Collections.emptyList();
                }
                result = opHotWordDtos.stream().map(opWord -> {
                     VHostWordDTO vHostWordDTO = new  VHostWordDTO();
                    vHostWordDTO.setOpHostWordId(Math.toIntExact(opWord.getHotWordId()));
                    vHostWordDTO.setWordName(opWord.getHotWordStr());
                    return vHostWordDTO;
                }).collect(Collectors.toList());
                collect = result.stream().collect(Collectors.toMap( VHostWordDTO::getOpHostWordId, opHotWordDTO -> opHotWordDTO, (e1, e2) -> e1));
            }
        } catch (Exception e) {
            logger.error("调用op接口异常：", e);
        }
        // 已经关联上的
        List< VHostWordDTO> erpHas = new ArrayList<>();
        List< VHSHostSkuDTO> vhsHostSkuDtoList=new ArrayList<>();
        if (coreSkuGenerate != null && !StringUtils.isEmpty(coreSkuGenerate.getSkuNo())) {
             VHSHostSkuDTOExample queryRelationBySku = new  VHSHostSkuDTOExample();
            queryRelationBySku.createCriteria().andSkuEqualTo(coreSkuGenerate.getSkuNo()).andIsDeleteEqualTo(0);
            vhsHostSkuDtoList = hostSkuMapper.selectByExample(queryRelationBySku);
            if (!CollectionUtils.isEmpty(collect)&&!CollectionUtils.isEmpty(vhsHostSkuDtoList)) {
                Map<Integer,  VHostWordDTO> finalCollect = collect;
                vhsHostSkuDtoList.forEach(c->{
                     VHostWordDTO vHostWordDto = finalCollect.get(c.getOpHostWordId());
                    if (null != vHostWordDto) {
                        vHostWordDto.setChecked(true);
                    } else {
                         VHostWordDTOExample query = new  VHostWordDTOExample();
                        query.createCriteria().andOpHostWordIdEqualTo(c.getOpHostWordId());
                        List< VHostWordDTO> hostWordDTOS = hostWordMapper.selectByExample(query);
                        if (!CollectionUtils.isEmpty(hostWordDTOS)) {
                            hostWordDTOS.stream().forEach(b -> b.setChecked(true));
                            erpHas.addAll(hostWordDTOS);
                        }

                    }
                });
            }
        }
        // op异常显示之前已经关联的
        if (CollectionUtils.isEmpty(result) && !CollectionUtils.isEmpty(vhsHostSkuDtoList)) {
            List<Integer> opIds = vhsHostSkuDtoList.stream().map( VHSHostSkuDTO::getOpHostWordId).collect(Collectors.toList());
             VHostWordDTOExample queryWord = new  VHostWordDTOExample();
            queryWord.createCriteria().andOpHostWordIdIn(opIds);
            List< VHostWordDTO> hostWords = hostWordMapper.selectByExample(queryWord);
            if (!CollectionUtils.isEmpty(hostWords)) {
                hostWords.forEach(c -> c.setChecked(true));
                result.addAll(hostWords);
            }

        }
        result.addAll(erpHas);
        return result;
    }

    @Override
    public List<OpHostWordVo> queryHotWordInErpBySkuNo(@NonNull String skuNo) {
         VHSHostSkuDTOExample query = new  VHSHostSkuDTOExample();
        query.createCriteria().andSkuEqualTo(skuNo).andIsDeleteEqualTo(0);
        List< VHSHostSkuDTO> vhsHostSkuDtos = hostSkuMapper.selectByExample(query);
        if (CollectionUtils.isEmpty(vhsHostSkuDtos)) {
            return Collections.emptyList();
        }
        List<Integer> collect = vhsHostSkuDtos.stream().map( VHSHostSkuDTO::getOpHostWordId).collect(Collectors.toList());
        Map<Integer,  VHSHostSkuDTO> collect1 = vhsHostSkuDtos.stream().collect(Collectors.toMap( VHSHostSkuDTO::getOpHostWordId, e -> e, (e1, e2) -> e1));
         VHostWordDTOExample queryHotWordInfo = new  VHostWordDTOExample();
        queryHotWordInfo.createCriteria().andOpHostWordIdIn(collect);
        List< VHostWordDTO> list = hostWordMapper.selectByExample(queryHotWordInfo);

        // 封装对象
        return list.stream().map(c -> {
            OpHostWordVo opHostWordVo = new OpHostWordVo();
            opHostWordVo.setHostWordId(c.getOpHostWordId());
             VHSHostSkuDTO vhsHostSkuDTO = collect1.get(c.getOpHostWordId());
            opHostWordVo.setIsDelete(vhsHostSkuDTO.getIsDelete());
            opHostWordVo.setWordName(c.getWordName());
            return opHostWordVo;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Integer> saveHotWord(List<String> hotWords) {
        List< VHostWordDTO> hostWordDtos = new ArrayList<>();
        hotWords.stream().forEach(c -> {
            String[] split = c.split(":");
            if (split.length == 2) {
                 VHostWordDTO vHostWordDTO = new  VHostWordDTO();
                vHostWordDTO.setOpHostWordId(Integer.valueOf(split[0]));
                vHostWordDTO.setWordName(split[1]);
                hostWordDtos.add(vHostWordDTO);
            }
        });
        ArrayList< VHostWordDTO> collect = hostWordDtos.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing( VHostWordDTO::getOpHostWordId))), ArrayList::new));
        collect.stream().forEach(hostWordDTO -> {
            Integer opHostWordId = hostWordDTO.getOpHostWordId();
            Date date = new Date();
            hostWordDTO.setUpdater(OP_USER);
            hostWordDTO.setModeTime(date);
             VHostWordDTOExample queryByOpId = new  VHostWordDTOExample();
            queryByOpId.createCriteria().andOpHostWordIdEqualTo(opHostWordId);
            long count = hostWordMapper.countByExample(queryByOpId);
            if (count > 0) {
                hostWordDTO.setIsDelete(0);
                hostWordMapper.updateByExampleSelective(hostWordDTO,queryByOpId);
            } else {
                hostWordDTO.setCreator(OP_USER);
                hostWordDTO.setAddTime(date);
                hostWordMapper.insertSelective(hostWordDTO);
            }
        });
        return hostWordDtos.stream().map( VHostWordDTO::getOpHostWordId).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindGoodsHotWords(List<Integer> hotWords, @NonNull Integer skuId,@NonNull String username) {

        logger.info("热词==》sku:{}商品关联热词：{}",skuId,hotWords);

        Date date = new Date();

        // 先查询出已有的
         CoreSkuGenerate coreSkuGenerate = newCoreSkuGenerateMapper.selectByPrimaryKey(skuId);
        if (coreSkuGenerate != null && !StringUtils.isEmpty(coreSkuGenerate.getSkuNo())) {
             VHSHostSkuDTOExample queryRelationBySku = new  VHSHostSkuDTOExample();
            // sku 所有的关联热词关系
            queryRelationBySku.createCriteria().andSkuEqualTo(coreSkuGenerate.getSkuNo());
            List< VHSHostSkuDTO> vhsHostSkuDTOS = hostSkuMapper.selectByExample(queryRelationBySku);
            if (vhsHostSkuDTOS == null) {
                vhsHostSkuDTOS = Collections.emptyList();
            }
            // 当前包含的所有的（含义删除的关系）
            Map<Integer,  VHSHostSkuDTO> oldBindMapToOpId = vhsHostSkuDTOS.stream().collect(Collectors.toMap( VHSHostSkuDTO::getOpHostWordId, vhsHostSkuDTO -> vhsHostSkuDTO, (e1, e2) -> e1));
            List<Integer> allBind = vhsHostSkuDTOS.stream().map( VHSHostSkuDTO::getOpHostWordId).collect(Collectors.toList());
            // 之前删除的
            List< VHSHostSkuDTO> notDelete = vhsHostSkuDTOS.stream().filter(c ->  c.getIsDelete() == 0).collect(Collectors.toList());
            List<Integer> notDeleteId = notDelete.stream().map( VHSHostSkuDTO::getOpHostWordId).collect(Collectors.toList());
            // 未删除的
            List< VHSHostSkuDTO> hasDelete = vhsHostSkuDTOS.stream().filter(c -> c.getIsDelete() == 1).collect(Collectors.toList());
            List<Integer> hasDeleteId = hasDelete.stream().map( VHSHostSkuDTO::getOpHostWordId).collect(Collectors.toList());
            // 完全没有需要新增
            List<Integer> needAdd = hotWords.stream().filter(c -> !allBind.contains(c)).collect(Collectors.toList());

            // 这次需要删除的
            List<Integer> needDelete = notDeleteId.stream().filter(c -> !hotWords.contains(c)).collect(Collectors.toList());
            // 之前删除的需要恢复的
            List<Integer> rollback = hasDeleteId.stream().filter(hotWords::contains).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(needDelete)) {
                needDelete.forEach(c -> {
                     VHSHostSkuDTO vhsHostSkuDTO = oldBindMapToOpId.get(c);
                     VHSHostSkuDTO update = new  VHSHostSkuDTO();
                    update.setHostSkuId(vhsHostSkuDTO.getHostSkuId());
                    update.setIsDelete(1);
                    update.setUpdater(username);
                    update.setModeTime(date);
                    hostSkuMapper.updateByPrimaryKeySelective(update);
                });
            }
            // 新增
            if (!CollectionUtils.isEmpty(needAdd)) {
                needAdd.forEach(c -> {
                     VHSHostSkuDTO add = new  VHSHostSkuDTO();
                    add.setOpHostWordId(c);
                    add.setSku(coreSkuGenerate.getSkuNo());
                    add.setIsDelete(0);
                    add.setAddTime(date);
                    add.setModeTime(date);
                    add.setCreator(username);
                    add.setUpdater(username);
                    hostSkuMapper.insertSelective(add);
                });
            }
            // 删除恢复
            if (!CollectionUtils.isEmpty(rollback)) {
                rollback.forEach(c -> {
                     VHSHostSkuDTO vhsHostSkuDTO = oldBindMapToOpId.get(c);
                     VHSHostSkuDTO update = new  VHSHostSkuDTO();
                    update.setHostSkuId(vhsHostSkuDTO.getHostSkuId());
                    update.setIsDelete(0);
                    update.setUpdater(username);
                    update.setModeTime(date);
                    hostSkuMapper.updateByPrimaryKeySelective(update);
                });
            }


        }

    }
}
