package com.vedeng.erp.kingdee.batch.common.listener;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ItemReadListener;
import org.springframework.batch.core.SkipListener;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 读监听器
 * @date 2023/2/22 10:22
 */
@Component
@Slf4j
public class BaseReadListener<T> implements ItemReadListener<T> {

    @Override
    public void beforeRead() {
        StepExecution stepExecution = StepSynchronizationManager.getContext().getStepExecution();
        log.info("step 读取之前：执行步骤id：{},执行步骤名称：{} ", stepExecution.getId(), stepExecution.getStepName());
    }

    @Override
    public void afterRead(T item) {
        StepExecution stepExecution = StepSynchronizationManager.getContext().getStepExecution();
        log.info("step 读取之后： 执行步骤id：{},执行步骤名称：{} 读取数据:{}", stepExecution.getId(), stepExecution.getStepName(), JSONObject.toJSONString(item));

    }

    @Override
    public void onReadError(Exception ex) {
        StepExecution stepExecution = StepSynchronizationManager.getContext().getStepExecution();
        log.error("step 执行步骤id：{},执行步骤名称：{} 读取异常", stepExecution.getId(), stepExecution.getStepName(), ex);
    }

}
