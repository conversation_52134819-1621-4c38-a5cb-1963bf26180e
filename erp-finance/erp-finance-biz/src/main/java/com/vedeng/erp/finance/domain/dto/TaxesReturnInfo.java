package com.vedeng.erp.finance.domain.dto;

import cn.hutool.core.util.StrUtil;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import lombok.Data;

/**
 * ERP接口
 */
@Data
public class TaxesReturnInfo implements ITaxesResult {

    /**
     * 返回码
     * <p>
     * 0000 返回成功
     * 0001 未获取登录cookie信息
     * 0002 认证失败
     * 9999 登录失效
     * 500 接口超时（已重试且未获取到发票号）
     */
    private String returnCode;

    /**
     * 返回信息
     */
    private String returnMessage;

    /**
     * 是否成功
     */
    private Boolean isSuccess;

    public TaxesReturnInfo(){
        this.isSuccess = Boolean.FALSE;
    }

    public String getReturnMessage() {
        return StrUtil.isNotEmpty(this.returnMessage) ? this.returnMessage : TaxesReturnCodeEnum.getMsg(this.returnCode);
    }
}
