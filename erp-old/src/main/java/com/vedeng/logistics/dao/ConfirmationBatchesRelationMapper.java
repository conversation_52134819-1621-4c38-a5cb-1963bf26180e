package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.ConfirmationBatchesRelation;

import javax.inject.Named;
import java.util.List;


@Named("confirmationBatchesRelationMapper")
public interface ConfirmationBatchesRelationMapper {

    /**
     * 根据条件查询
     * 确认单-批次关系
     * @param vo
     * @return
     */
    List<ConfirmationBatchesRelation> selectAllByRelation(ConfirmationBatchesRelation vo);


    /**
     * 根据销售订单id条件查询
     * 确认单-批次关系
     * @param saleorderId
     * @return
     */
    List<ConfirmationBatchesRelation> selectAllBySaleorderId(Integer saleorderId);


    /**
     * 根据确认单id集合批量删除
     * @param idList
     * @return
     */
    int deleteByConfirmationIdBatch(List<Integer> idList);

    int insert(ConfirmationBatchesRelation vo);

    int insertSelective(ConfirmationBatchesRelation vo);
}
