.management-wrapper {
  max-width: 1280px;
  min-width: 960px;
  margin: 0 auto;
  padding: 20px 0;
  display: flex;
}
.management-wrapper .mw-left {
  flex: 1;
  margin-right: 20px;
}
.management-wrapper .mw-left .common-box {
  background-color: #fff;
  margin-bottom: 10px;
}
.management-wrapper .mw-left .common-box:last-child {
  margin-bottom: 0;
}
.management-wrapper .mw-left .common-box.mb0 {
  margin-bottom: 0;
}
.management-wrapper .mw-left .common-box.pt20 {
  padding-top: 20px;
}
.management-wrapper .mw-left .common-box .cb-title {
  padding: 10px 20px;
  font-size: 24px;
  border-bottom: solid 1px #EDF0F2;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-title {
  font-size: 18px;
  padding: 10px 20px 0;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10px;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item {
  margin: 10px 10px 0;
  padding: 10px;
  width: calc(50% - 20px);
  height: 120px;
  border: solid 1px #EDF0F2;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .title {
  margin-bottom: 5px;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content {
  display: flex;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-left {
  width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 圆形进度条 */
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-left .circle-father-div {
  width: 70px;
  height: 70px;
  position: relative;
  background-color: #f3f3f3;
  border-radius: 50%;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-left .circle-father-div .percent-div {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  position: absolute;
  clip: rect(0, 70px, 70px, 35px);
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-left .circle-father-div .percent-div.clip-auto {
  clip: rect(auto, auto, auto, auto);
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-left .circle-father-div .percent-div .percent-left {
  position: absolute;
  width: 70px;
  height: 70px;
  border: 5px solid #13BF13;
  box-sizing: border-box;
  border-radius: 50%;
  clip: rect(0, 35px, 70px, 0);
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-left .circle-father-div .percent-div .percent-right {
  position: absolute;
  width: 70px;
  height: 70px;
  border: 5px solid #13BF13;
  box-sizing: border-box;
  border-radius: 50%;
  clip: rect(0, 70px, 70px, 35px);
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-left .circle-father-div .percent-div .percent-right.wth0 {
  width: 0;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-left .circle-father-div .num {
  position: absolute;
  box-sizing: border-box;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 18px;
  left: 5px;
  top: 5px;
  border-radius: 50%;
  background-color: #fff;
  color: #13BF13;
  z-index: 1;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-right {
  flex: 1;
}
.management-wrapper .mw-left .common-box .cb-content .control-box .control-box-content .cbc-list .cbc-item .content .c-right .month-num {
  margin-top: 10px;
}
.management-wrapper .mw-left .common-box .cb-content .goods-box {
  padding-bottom: 20px;
}
.management-wrapper .mw-left .common-box .cb-content .goods-box .goods-box-content .gbc-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10px;
}
.management-wrapper .mw-left .common-box .cb-content .goods-box .goods-box-content .gbc-list .gbc-item {
  margin: 10px 10px 0;
  width: calc(50% - 20px);
}
.management-wrapper .mw-left .common-box .cb-content .goods-box .goods-box-content .gbc-list .gbc-item .task-title {
  font-size: 18px;
  margin-bottom: 10px;
}
.management-wrapper .mw-left .common-box .cb-content .goods-box .goods-box-content .gbc-list .gbc-item .task-list {
  height: 120px;
  border: solid 1px #EDF0F2;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.management-wrapper .mw-left .common-box .cb-content .goods-box .goods-box-content .gbc-list .gbc-item .task-list .task-item {
  text-align: center;
}
.management-wrapper .mw-left .common-box .cb-content .goods-box .goods-box-content .gbc-list .gbc-item .task-list .task-item .item-num a {
  font-size: 24px;
  color: #0099ff;
  text-decoration: underline;
}
.management-wrapper .mw-left .common-box .cb-content .goods-box .goods-box-content .gbc-list .gbc-item .task-list .task-item .item-num a.red {
  color: #E64545;
}
.management-wrapper .mw-left .common-box .cb-content .goods-box .goods-box-content .gbc-list .gbc-item .task-list .task-item .item-num a:hover {
  color: #ff6600;
}
.management-wrapper .mw-left .common-box .cb-content .table-box {
  padding: 20px;
  border-bottom: solid 1px #EDF0F2;
}
.management-wrapper .mw-left .common-box .cb-content .table-box:last-child {
  border-bottom: none;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-title {
  font-size: 18px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-title .select {
  min-width: 150px;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-content .thead {
  background-color: #F5F7FA;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-content .thead .thead-tr {
  display: flex;
  border-top: solid 1px #EDF0F2;
  border-left: solid 1px #EDF0F2;
  border-bottom: solid 1px #EDF0F2;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-content .thead .thead-tr .thead-th {
  color: #999999;
  text-align: center;
  flex: 1;
  padding: 10px 0;
  border-right: solid 1px #EDF0F2;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-content .tbody .tbody-tr {
  display: flex;
  border-left: solid 1px #EDF0F2;
  border-bottom: solid 1px #EDF0F2;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-content .tbody .tbody-tr .tbody-td {
  text-align: center;
  flex: 1;
  padding: 5px 0;
  border-right: solid 1px #EDF0F2;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-content .tbody .tbody-tr .tbody-td .can-click {
  color: #0099ff;
  text-decoration: underline;
}
.management-wrapper .mw-left .common-box .cb-content .table-box .tb-content .tbody .tbody-tr .tbody-td .can-click:hover {
  color: #ff6600;
}
.management-wrapper .mw-right {
  width: 350px;
}
.management-wrapper .mw-right .message-total {
  padding: 10px 20px;
  background-color: #fff;
  display: flex;
  margin-bottom: 10px;
}
.management-wrapper .mw-right .message-total .mt-left {
  font-size: 18px;
  display: flex;
  align-items: center;
}
.management-wrapper .mw-right .message-total .mt-right {
  flex: 1;
  text-align: center;
}
.management-wrapper .mw-right .message-total .mt-right .num a {
  font-size: 24px;
  font-weight: 700;
  color: #E64545;
  text-decoration: underline;
}
.management-wrapper .mw-right .message-total .mt-right .num a:hover {
  color: #ff6600;
}
.management-wrapper .mw-right .month-rank {
  padding: 10px 20px;
  background-color: #fff;
  margin-bottom: 10px;
}
.management-wrapper .mw-right .month-rank:last-child {
  margin-bottom: 0;
}
.management-wrapper .mw-right .month-rank .rank-title {
  font-size: 18px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.management-wrapper .mw-right .month-rank .rank-title .icon-up {
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 20px;
  color: #999999;
  cursor: pointer;
  transform: rotate(0);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.management-wrapper .mw-right .month-rank .rank-title .icon-up.down {
  transform: rotate(180deg);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.management-wrapper .mw-right .month-rank .rank-title .icon-up:hover {
  color: #ff6600;
}
.management-wrapper .mw-right .month-rank .rank-content .rank-table .rt-thead .tr {
  display: flex;
}
.management-wrapper .mw-right .month-rank .rank-content .rank-table .rt-thead .tr .th {
  color: #999999;
  flex: 1;
  text-align: center;
  height: 35px;
  line-height: 35px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 2.5px;
}
.management-wrapper .mw-right .month-rank .rank-content .rank-table .rt-thead .tr .th.number {
  padding: 0;
  flex: none;
  width: 32px;
}
.management-wrapper .mw-right .month-rank .rank-content .rank-table .rt-tbody {
  max-height: 175px;
  overflow-y: auto;
}
.management-wrapper .mw-right .month-rank .rank-content .rank-table .rt-tbody .tr {
  display: flex;
  border-bottom: solid 1px #EDF0F2;
}
.management-wrapper .mw-right .month-rank .rank-content .rank-table .rt-tbody .tr:hover {
  background-color: #F5F7FA;
}
.management-wrapper .mw-right .month-rank .rank-content .rank-table .rt-tbody .tr .td {
  flex: 1;
  text-align: center;
  height: 35px;
  line-height: 35px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 2.5px;
}
.management-wrapper .mw-right .month-rank .rank-content .rank-table .rt-tbody .tr .td.number {
  padding: 0;
  flex: none;
  width: 32px;
}

.month-rank .rank-content {
  max-height: 215px;
  overflow-y: auto;
}
