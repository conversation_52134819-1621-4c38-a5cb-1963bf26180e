package com.vedeng.erp.saleorder.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4 14:55
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderTerminalDto extends BaseDto {

    /**
     * 主键
     */
    private Integer orderTerminalId;

    /**
     * 业务id
     */
    private Integer businessId;

    /**
     * 业务类型
     * 0:销售订单
     * 1:商机单
     * 2:报价单
     * 3:线索
     */
    private Integer businessType;

    /**
     * 编号
     */
    private String businessNo;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 大数据终端id（通过大数据查询出对应的终端信息）
     */
    private String dwhTerminalId;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditIdentifier;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 是否删除 （0否1是）
     */
    private Integer isDeleted;

    /**
     * 省
     */
    private Integer provinceId;

    /**
     * 市
     */
    private Integer cityId;

    /**
     * 区
     */
    private Integer areaId;

    /**
     * 省名
     */
    private String provinceName = "";

    /**
     * 市名
     */
    private String cityName = "";

    /**
     * 区名
     */
    private String areaName = "";

    /**
     * 省市区+详细地址拼接
     */
    private String address;

    /**
     * 贝登标准：机构性质
     * eg.0 公立、1 非公
     */
    private String hosModel = "";

    /**
     * 机构性质
     */
    private String hosModelName;

    /**
     * 贝登标准：机构评级
     * eg.
     * 0	一级医院
     * 1	二级医院
     * 2	三级医院
     * 3	未定级医院
     * 4	社区卫生服务中心（站）
     * 5	乡镇卫生院
     * 6	诊所（医务室）
     * 7	村卫生室
     * 8	应急三级医院
     * 9	应急二级医院
     * 10	应急基层医疗
     */
    private String hosLevel;

    /**
     * 机构评级
     */
    private String hosLevelName;

    private Integer terminalTraderNature;

    private Integer companyType;

    /**
     * 终端性质描述
     */
    private String natureTypeName;
}
