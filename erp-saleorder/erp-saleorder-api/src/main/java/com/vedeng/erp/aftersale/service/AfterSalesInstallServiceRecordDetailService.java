package com.vedeng.erp.aftersale.service;

import com.vedeng.erp.aftersale.vo.AfterSalesInstallServiceRecordVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.service
 * @Date 2022/5/11 16:10
 */
public interface AfterSalesInstallServiceRecordDetailService {

    /**
     * 根据售后单id查询安调服务信息集合
     * @param afterSalesId
     * @return
     */
    List<AfterSalesInstallServiceRecordVo> getInstallDetailList(Integer afterSalesId);

    /**
     * 删除安调服务记录
     * @param afterSalesServiceId
     * @return
     */
    int deleteServiceRecord(Integer afterSalesServiceId);

    /**
     * 删除安调服务记录详情
     * @param afterSalesServiceId
     * @return
     */
    int deleteServiceRecordDetail(Integer afterSalesServiceId,Integer afterSalesInstallDetailId);


    /**
     * 查询安调中未删除的安调记录的补充码是否存在
     * @param supplCode
     * @return
     */
    boolean querySupplCodeIsExtend(String supplCode);
}
