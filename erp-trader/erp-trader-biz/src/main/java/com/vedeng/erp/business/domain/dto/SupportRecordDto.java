package com.vedeng.erp.business.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupportRecordDto {
    /**
     * 主键
     */
    private Long businessChanceSupportRecordId;

    /**
     * 商机ID
     */
    private Integer businessChanceId;

    /**
     * 商机等级 SABC
     */
    private String businessLevel;

    /**
     * 评估成交金额（元）
     */
    private BigDecimal amount;

    /**
     * 评估成交时间 yyyy-MM-dd
     */
    private String orderDate;

    /**
     * 下次支持时间 yyyy-MM-dd
     */
    private String nextSupportDate;

    /**
     * 支持内容
     */
    private String content;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 支持时间 yyyy-MM-dd HH:mm:ss
     */
    private String supportTime;

    /**
     * 支持人
     */
    private String supportUserName;
}
