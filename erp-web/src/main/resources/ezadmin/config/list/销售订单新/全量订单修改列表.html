<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>(全量)修改订单列表</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="rVfog0mUc_M" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">审核状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="STATUS" placeholder="全部" style="" alias="IFNULL( e.STATUS, 3 )" jdbctype="" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">订单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="请输入" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">修改申请单编号</label>
           <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_MODIFY_APPLY_NO" placeholder="请输入" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SALEORDER_MODIFY_APPLY_NO" name="SALEORDER_MODIFY_APPLY_NO" body="td-link" order="0" url="orderstream/saleorder/viewModifyApply.do?saleorderModifyApplyId=${SALEORDER_MODIFY_APPLY_ID}&amp;saleorderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">修改申请单编号</th>
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link" order="0" url="/orderstream/saleorder/detail.do?saleOrderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">订单号</th>
      <th item_name="ORG_ID" name="ORG_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select
		ORG_ID K,ORG_NAME V
		from T_ORGANIZATION" style="" head="18">销售部门</th>
      <th item_name="USER_ID" name="USER_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select USER_ID K,USERNAME V FROM T_USER" style="" head="18">归属销售</th>
      <th item_name="PAYMENT_STATUS" name="PAYMENT_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">收款状态</th>
      <th item_name="DELIVERY_STATUS" name="DELIVERY_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">发货状态</th>
      <th item_name="INVOICE_STATUS" name="INVOICE_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">开票状态</th>
      <th item_name="VERIFY_STATUS" name="VERIFY_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" head="18">审核状态</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by b.ADD_TIME DESC" groupby="">StringBuilder sql=new StringBuilder("
SELECT
	b.SALEORDER_MODIFY_APPLY_ID,
	b.SALEORDER_MODIFY_APPLY_NO,
	a.SALEORDER_ID,
	a.SALEORDER_NO,
	f.USER_ID,
	a.TRADER_ID,
        a.ORG_ID,
	a.PAYMENT_STATUS*1 PAYMENT_STATUS,
	a.DELIVERY_STATUS*1 DELIVERY_STATUS,
	a.INVOICE_STATUS*1 INVOICE_STATUS,
	IFNULL( e.STATUS, 3 ) AS VERIFY_STATUS
FROM
	T_SALEORDER a
	INNER JOIN T_SALEORDER_MODIFY_APPLY b ON a.SALEORDER_ID = b.SALEORDER_ID
	LEFT JOIN T_VERIFIES_INFO e ON b.SALEORDER_MODIFY_APPLY_ID = e.RELATE_TABLE_KEY
	AND e.RELATE_TABLE = 'T_SALEORDER_MODIFY_APPLY'
	LEFT JOIN T_R_TRADER_J_USER f ON f.TRADER_ID = a.TRADER_ID and  f.TRADER_TYPE = 1
WHERE
	1 = 1
");

list=search(sql);
return list;</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>