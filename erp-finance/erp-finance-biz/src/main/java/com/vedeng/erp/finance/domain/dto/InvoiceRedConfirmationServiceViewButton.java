package com.vedeng.erp.finance.domain.dto;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.Button;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 红字确认单相关按钮操作
 * @date 2023/10/21 15:48
 */
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceRedConfirmationServiceViewButton extends Button {



    private List<InvoiceRedConfirmationServiceViewButton> buttons = new ArrayList<>(16);
    private InvoiceRedConfirmationDto dto;
    public static final String RELATE_AFTESALE="关联售后";
    public static final String UPDATE_NAME = "更新";
    public static final String INVALID_NAME = "作废";
    public static final String CANCEL_NAME = "撤销";
    public static final String INVOICE_NAME = "开票";
    public static final String REJECT_NAME = "拒绝";
    public static final String AGREE_NAME = "同意";
    public static final String APPLY_NAME = "申请";


    public InvoiceRedConfirmationServiceViewButton(InvoiceRedConfirmationDto dto) {
        this.dto = dto;
    }

    public List<InvoiceRedConfirmationServiceViewButton> initButtons() {
        return this.buttons;
    }


    public InvoiceRedConfirmationServiceViewButton checkAssociatedAfterSalesButton() {
        InvoiceRedConfirmationServiceViewButton button = new InvoiceRedConfirmationServiceViewButton();
        button.setButton(ErpConstant.SEVEN);
        button.setButtonName(RELATE_AFTESALE);
        button.setNeedConfirm(false);
        button.setShow(true);
        button.setViewType(ErpConstant.ONE);
        button.setUrl("/invoice/redConfirmation/associate.do?invoiceRedConfirmationId="+dto.getInvoiceRedConfirmationId());
        buttons.add(button);
        boolean checkStatus = InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY.getCode().equals(dto.getRedConfirmationStatus())
                || InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM.getCode().equals(dto.getRedConfirmationStatus());
        boolean checkEvent = StrUtil.isEmpty(dto.getAfterSaleBusinessOrderNo());
        boolean show = checkStatus && checkEvent;
        // 非已确认或已申请 不是购方
        if (!show) {
            button.setShow(false);
        }
        return this;
    }

    public InvoiceRedConfirmationServiceViewButton checkUpdateButton() {
        InvoiceRedConfirmationServiceViewButton button = new InvoiceRedConfirmationServiceViewButton();
        button.setButton(ErpConstant.SIX);
        button.setButtonName(UPDATE_NAME);
        button.setNeedConfirm(false);
        button.setShow(true);
        button.setUrl("/redConfirm/api/updateByDetail.do");
        buttons.add(button);
        // 非已确认或已申请
        if (!InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY.getCode().equals(dto.getRedConfirmationStatus())
                && !InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM.getCode().equals(dto.getRedConfirmationStatus())) {
            button.setShow(false);
        }
        return this;
    }

    public InvoiceRedConfirmationServiceViewButton checkVoidButton() {
        InvoiceRedConfirmationServiceViewButton button = new InvoiceRedConfirmationServiceViewButton();
        button.setButton(ErpConstant.FIVE);
        button.setButtonName(INVALID_NAME);
        button.setNeedConfirm(true);
        button.setShow(true);
        button.setUrl("/redConfirm/api/invalid.do");
        buttons.add(button);
        // 非已确认
        if (!InvoiceRedConfirmationStateEnum.INIT.getCode().equals(dto.getRedConfirmationStatus())) {
            button.setShow(false);
        }
        return this;
    }

    public InvoiceRedConfirmationServiceViewButton checkRevokedButton() {
        InvoiceRedConfirmationServiceViewButton button = new InvoiceRedConfirmationServiceViewButton();
        button.setButton(ErpConstant.FOUR);
        button.setButtonName(CANCEL_NAME);
        button.setNeedConfirm(true);
        button.setShow(true);
        button.setUrl("/redConfirm/api/cancel.do");
        buttons.add(button);
        // 非已申请 确认 以及不是销方不显示 发起方非销方
        boolean checkStatus = InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM.getCode().equals(dto.getRedConfirmationStatus())
                || InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY.getCode().equals(dto.getRedConfirmationStatus());
        boolean checkConfirm = InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM.getCode().equals(dto.getRedConfirmationStatus()) && ErpConstant.ZERO.equals(dto.getConfirmPersonType());
        boolean checkApply = InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY.getCode().equals(dto.getRedConfirmationStatus()) && ErpConstant.ZERO.equals(dto.getInputPersonType());
        boolean show = checkStatus && (checkConfirm || checkApply);
        if (!show) {
            button.setShow(false);
        }
        return this;
    }

    public InvoiceRedConfirmationServiceViewButton checkInvoiceButton() {
        InvoiceRedConfirmationServiceViewButton button = new InvoiceRedConfirmationServiceViewButton();
        button.setButton(ErpConstant.THREE);
        button.setButtonName(INVOICE_NAME);
        button.setNeedConfirm(true);
        button.setShow(true);
        button.setUrl("/redConfirm/api/open.do");
        buttons.add(button);
        // 非已确认
        if (!InvoiceRedConfirmationStateEnum.HAVE_BEEN_CONFIRM.getCode().equals(dto.getRedConfirmationStatus())) {
            button.setShow(false);
        }
        return this;
    }

    public InvoiceRedConfirmationServiceViewButton checkRejectButton() {
        InvoiceRedConfirmationServiceViewButton button = new InvoiceRedConfirmationServiceViewButton();
        button.setButton(ErpConstant.TWO);
        button.setButtonName(REJECT_NAME);
        button.setShow(true);
        button.setNeedConfirm(true);
        button.setUrl("/redConfirm/api/audit.do");
        buttons.add(button);
        // 非已申请 以及不是购方不显示
        if (!InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY.getCode().equals(dto.getRedConfirmationStatus())
                || !ErpConstant.ONE.equals(dto.getInputPersonType())) {
            button.setShow(false);
        }
        return this;
    }

    public InvoiceRedConfirmationServiceViewButton checkAgreeButton() {
        InvoiceRedConfirmationServiceViewButton button = new InvoiceRedConfirmationServiceViewButton();
        button.setButton(ErpConstant.ONE);
        button.setButtonName(AGREE_NAME);
        button.setNeedConfirm(true);
        button.setShow(true);
        button.setUrl("/redConfirm/api/audit.do");
        buttons.add(button);

        boolean show = InvoiceRedConfirmationStateEnum.HAVE_BEEN_APPLY.getCode().equals(dto.getRedConfirmationStatus()) && ErpConstant.ONE.equals(dto.getInputPersonType())
                && StrUtil.isNotEmpty(dto.getAfterSaleBusinessOrderNo());
        // 非已申请 以及不是购方不显示 以及没关联售后单
        if (!show) {
            button.setShow(false);
        }
        return this;
    }

    public InvoiceRedConfirmationServiceViewButton checkApplyButton() {
        InvoiceRedConfirmationServiceViewButton button = new InvoiceRedConfirmationServiceViewButton();
        button.setButton(ErpConstant.ZERO);
        button.setButtonName(APPLY_NAME);
        button.setNeedConfirm(false);
        button.setShow(true);
        button.setViewType(ErpConstant.ONE);
        button.setUrl("/invoice/redConfirmation/apply.do?invoiceRedConfirmationId="+dto.getInvoiceRedConfirmationId());
        buttons.add(button);
        if (!InvoiceRedConfirmationStateEnum.INIT.getCode().equals(dto.getRedConfirmationStatus())) {
            button.setShow(false);
        }
        return this;
    }

}
