package com.vedeng.trader.dao;

import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.order.model.dto.AssociatedCustomerGroupCountDto;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.TraderFinance;
import com.vedeng.trader.model.dto.AssignCustomerInfo;
import com.vedeng.trader.model.dto.TraderBaseInfoDto;
import com.vedeng.trader.model.vo.CustomerBillPeriodApplyItemVo;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderFinanceVo;
import com.vedeng.trader.service.search.ListCustomerQuery;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TraderCustomerMapper {

    TraderCustomer selectByPrimaryKey(Integer traderCustomerId);

    /**
     * <b>Description:</b>更新是否为盈利机构的状态<br>
     * @param traderId 客户的标识
     * @param isProfit 是否为盈利机构
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:2019/9/12</b>
     */
    int updateCustomerIsProfit(@Param("traderId") Integer traderId, @Param("isProfit")Integer isProfit);

    /**
     * <b>Description:</b>分页查询客户列表信息，根据资质审核状态删选<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/9/12
     */
    List<TraderCustomer> getTraderCustomerIdsListPage(Map<String,Object> paramMap);
    /**
     * <b>Description:</b>根据组织部门分页获取客户信息<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/11/26
     */
    List<TraderCustomer> getTraderCustomerByOrganizationsListPage(Map<String,Object> paramMap);

    TraderCustomer getTraderCustomerByTraderId(@Param("traderId")Integer traderId);

    TraderCustomer getBaseCustomerByTraderId(@Param("traderId")Integer traderId);
    /**
     * <b>Description:分页查询客户列表</b><br>
     * @param paramMap 分页信息
     * @return 客户列表信息
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:2019/10/29</b>
     */
    List<TraderCustomer> getTraderCustomerListPage(Map<String,Object> paramMap);
    /**
     * <b>Description:分页查询客户列表</b><br>
     * @param paramMap 分页信息
     * @return 客户列表信息
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:2019/10/29</b>
     */
    List<TraderCustomer> getYXGCustomerListPage(Map<String,Object> paramMap);

    /**
     * <b>Description:</b>根据客户标识更新客户分类字段<br>
     * @param trader 客户信息
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/10/30
     */
    int updateCustomerCategoryById(TraderCustomer trader);

    /**
     * <b>Description:</b>根据客户标识更新客户等级<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2019/10/30
     */
    int updateCustomerLevelById(TraderCustomer traderCustomer);

    /**
     * <b>Description:</b><br> 获取客户信息
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月26日 下午2:02:46
     */
    TraderCustomerVo getCustomerInfo(@Param("traderId")Integer traderId);

    TraderCustomerVo getCustomerByTraderName(String traderName);

    /**
    *   获取符合条件的会员
    * @Author:strange
    * @Date:18:25 2020-04-08
    */
    List<Integer> getTraderCustomerIsMember();

    List<Integer> getTraderCustomerIsMemberForAssign(@Param("traderIds") List<Integer> traderIds);

    /**
    *更新为会员
    * @Author:strange
    * @Date:18:43 2020-04-08
    */
    Integer updateIsVedengMember(@Param("list")List<Integer> customerIdList,@Param("status")  Integer status);
    TraderCustomer getTraderCustomerById(@Param("traderId") Integer traderId);


    TraderCustomer getTraderCustomerByPayApply(Integer payApplyId);

    void updateTraderCustomerAmount(TraderCustomer tc);

    List<TraderCustomerVo> getTraderByTraderName(@Param("traderList") List<Trader> traderList,@Param("companyId") Integer companyId);

    int updatePriceLimtList(@Param("traderCustomerList") List<TraderCustomerVo> traderCustomerList);

    int delPriceLimitTrader(@Param("traderCustomer") TraderCustomerVo traderCustomerVo);

    TraderCustomerVo getTraderCustomerByTraderName(@Param("traderName") String traderName);


    /**
     * <b>Description:</b>根据客户基本信息分页获取客户标签<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/26
     */
    List<Integer> getTraderIdsByBaseAttributesListPage(Map<String,Object> map);

    /**
     * @description: 搜索客户列表（弹框简单版）
     * @return:
     * @author: Strange
     * @date: 2020/6/11
     **/
    List<TraderCustomerVo> searchCustomerListPage(Map<String, Object> map);


    Trader getTraderByTraderCustomerId(@Param("traderCustomerId")Integer traderCustomerId);

    Integer getCustomerNatureBySaleorderId(Integer saleorderId);

    Integer updateCategoryIdAndVipPrice(TraderCustomer traderCustomer);

    /**
     * 根据客户id查询客户资质审核状态
     * @param traderId 客户id
     * @return 资质审核状态
     */
    Integer getTraderCertificateCheckStatusByTraderId(Integer traderId);


    /**
     * 根据客户编号查询集团客户或其子公司
     *
     * @param traderId
     * @param selfOrChild 1.总部, 2.分院（目前只有两级层级）
     * @param belongPlatformNo
     * @return
     */
    List<TraderCustomerVo> listGroupCustomer(@Param("traderId") Integer traderId, @Param("selfOrChild") Integer selfOrChild, @Param("belongPlatformNo") Integer belongPlatformNo);

    /**
     * <b>Description:</b><br> 获取客户账期信息（使用次数，账期信息）
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月5日 上午11:29:30
     */
    List<TraderCustomerVo> getTraderCustomerAccountPeriodInfo(@Param("traderId")Integer traderId);

    /**
     * <b>Description:</b><br> 获取销售信息
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月26日 下午2:11:26
     */
    TraderCustomerVo getCustomerSaleorderInfo(@Param("traderId")Integer traderId);

    /**
     * <b>Description:</b><br> 获取客户账期未还金额
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月31日 下午4:45:29
     */
    BigDecimal getTraderCustomerUsedAccountPeriodAmount(@Param("traderId")Integer traderId);

    /**
     * <b>Description:</b><br> 获取客户订单账期占用金额（未支付）
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月31日 下午5:12:23
     */
    BigDecimal getTraderCustomerZYAccountPeriodAmount(@Param("traderId")Integer traderId);

    /**
     * <b>Description:</b><br> 获取客户账期使用次数
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年10月31日 下午4:44:03
     */
    Integer getTraderCustomerAccountPeriodTimes(@Param("traderId")Integer traderId);

    /**
     * <b>Description:</b><br> 获取客户基本表信息
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年7月5日 下午5:41:58
     */
    TraderCustomerVo getCustomerByTraderId(@Param("traderId")Integer traderId);

    /**
     * <b>Description:</b><br> 获取报价信息
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月26日 下午2:11:16
     */
    TraderCustomerVo getCustomerQuoteorderInfo(@Param("traderId")Integer traderId);

    /**
     * <b>Description:</b><br> 获取客户-供应商账期申请记录
     * @param traderId
     * @param traderType
     * @return
     * @Note
     * <b>Author:</b> duke
     * <br><b>Date:</b> 2018年2月3日 上午10:16:16
     */
    List<TraderAccountPeriodApply> getTraderAccountPeriodApplyList(@Param("traderId")Integer traderId, @Param("traderType")Integer traderType);

    /**
     * <b>Description:</b><br> 查询客户的银行帐号
     * @param traderFinance
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月31日 下午1:15:29
     */
    List<TraderFinance> getTraderCustomerFinanceList(TraderFinance traderFinance);


    /**
     * <b>Description:</b><br> 查询客户的财务信息
     * @param traderFinance
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月31日 下午1:15:29
     */
    TraderFinanceVo getTraderCustomerFinance(TraderFinanceVo traderFinance);

    /**
     * 根据覆盖订单数据过滤客户id
     * @param traderIds
     * @param categoryIds
     * @param departments
     * @return
     */
    List<Integer> filterTraderIdsByCoverData(@Param("traderIds") List<Integer> traderIds , @Param("categoryIds") List<String> categoryIds ,@Param("departments") List<String> departments);

    /**
     * 账期申请获取客户ID
     * @param tapa
     * @return
     */
    List<TraderCustomer> getCustomerByInfo(TraderAccountPeriodApply tapa);

    /**
     * 通过客户ID获取客户信息
     * @param customerId
     * @return
     */
    CustomerBillPeriodApplyItemVo getCustomerInfoByCustomerId(@Param("customerId") Integer customerId);

    /**
     * 获取订单账期类型集合
     * @param relatedId
     * @return
     */
    List<Integer> getBillTypeByOrderId(@Param("relatedId") Integer relatedId);

    /**
     * 获取当前客户分群下所有traderId
     * <AUTHOR>
     * @Date 4:07 下午 2020/5/30
     * @Param
     * @return
     **/
    List<Integer> getTraderIdByTraderGroupId(Integer traderGroupId);


    /**
     * <b>Description:</b><br> 获取分页
     * @param
     * @param
     * @Note
     * <b>Author:</b>
     * <br><b>Date:</b>
     */
    List<TraderCustomerVo> getCustomerVolistpage(@Param("listCustomerQuery")  ListCustomerQuery listCustomerQuery, @Param("page") Page page);


    /**
     * <b>Description:</b><br> 客户分页列表批量获取报价信息
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月26日 下午2:11:16
     */
    List<TraderCustomerVo> getCustomerQuoteorderInfoByTraderCustomerVo(@Param("traderIdList") List<Integer> traderIdList);

    /**
     * <b>Description:</b><br> 批量获取销售信息
     * @param
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年6月26日 下午2:11:26
     */
    List<TraderCustomerVo> getCustomerSaleorderInfoList(@Param("traderIdList") List<Integer> traderIdList);

    /**
     * 获取客户分群id
     * <AUTHOR>
     * @Date 4:32 下午 2020/5/30
     * @Param
     * @return
     **/
    List<TraderCustomerVo> getTraderGroupIdListByTraderId(@Param("traderIdList") List<Integer> traderIdList);

    List<TraderCustomerVo> searchTraderCustomerListPage(Map<String, Object> map);

    TraderCustomer getTraderCustomer(TraderCustomer traderCustomer);

    /**
     * <b>Description:</b><br> 编辑客户
     * @param traderCustomer
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年5月17日 上午11:33:43
     */
    int update(TraderCustomer traderCustomer);

    TraderCustomerVo getTraderCustomerInfo(Integer traderId);

    List<Integer> getTraderIdByTime(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    int updateHasQuoted( @Param("countToUpdate") List<Integer> countToUpdate);

    List<Integer> getTraderIdByTimeAndValidStatus(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    int updateIsCooperated(@Param("countToUpdate") List<Integer> countToUpdate);

    int clearRelationBetweenTraderCustomer(Integer traderId);

    List<TraderCustomerVo> getPublicCustomerVolistpage(@Param("listCustomerQuery")  ListCustomerQuery listCustomerQuery, @Param("page") Page page);

    /**
     * 更新客户预警次数
     * @param traderCustomerIds
     * @return
     */
    int updatePublicCustomerEarlyWarningCount(@Param("traderCustomerIds") List<Integer> traderCustomerIds);

    /**
     * 重置客户预警次数
     * @param customerIds
     * @return
     */
    int resetCustomerEarlyWarningCount(@Param("customerIds") List<Integer> customerIds);

    /**
     * 检索分组下的客户数量
     * @param associatedCustomerGroupList
     * @return
     */
    List<AssociatedCustomerGroupCountDto> getTraderCountByAssociatedCustomerGroup(@Param("associatedCustomerGroupList") List<Long> associatedCustomerGroupList);

    List<Integer> selectRelatedIdByAssociatedCustomerGroup(@Param("associatedCustomerGroup") Long associatedCustomerGroup);

    /**
     * 公海规则改变后 重置所有大于0的客户 预警次数
     */
    int resetAllWarningCount();

    List<TraderCustomer> getTraderCustomerListByAssociatedCustomerGroup(@Param("associatedCustomerGroup") Long associatedCustomerGroup);

    /**
     * 获取公海列表里的原归属销售
     * @return
     */
    List<User> getPublicCustomerRecord();

    TraderBaseInfoDto getTraderBaseInfo(@Param("traderId") Integer traderId);

    /**
     * 根据customerId查询客户的归属平台
     */
    Integer getTraderBelongByCustomerId(@Param("customerId") Integer customerId);

    /**
     * 获取审核通过的客户
     *
     * @param traderId
     * @return
     */
    TraderCustomer selectTraderCustomer(@Param("traderId") Integer traderId);

    AssignCustomerInfo queryAssignInfo(@Param("traderId") Integer traderId);
}
