package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeePayExpensesCommand;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应付单明细  贝登dto 转 金蝶 command
 * @date
 */
@Mapper(componentModel = "spring")
public interface KingDeePayExpensesCommandConvertor extends BaseCommandMapStruct<KingDeePayExpensesCommand,KingDeePayExpensesDto> {

    @Mapping(target = "FID", source = "FId")
    @Mapping(target = "FDATE", source = "FDate")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtId")
    @Mapping(target = "FISPRICEEXCLUDETAX", source = "FIsPriceExcludeTax")
    @Mapping(target = "FBUSINESSTYPE", source = "FBusinessType")
    @Mapping(target = "FISTAX", source = "FIsTax")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeId")
    @Mapping(target = "FSUPPLIERID.FNumber", source = "FSupplierId")
    @Mapping(target = "FCURRENCYID.FNumber", source = "FCurrencyId")
    @Mapping(target = "FSETTLEORGID.FNumber", source = "FSettleOrgId")
    @Mapping(target = "FPAYORGID.FNumber", source = "FPayOrgId")
    @Override
    KingDeePayExpensesCommand toCommand(KingDeePayExpensesDto dto);

    @Mapping(target = "FCOSTID.FNumber", source = "FCOSTID")
    @Mapping(target = "FCOSTDEPARTMENTID.FNumber", source = "FCOSTDEPARTMENTID")
    @Mapping(target = "f_QZOK_WLBM.FNumber", source = "f_QZOK_WLBM")
    KingDeePayExpensesCommand.KingDeePayExpensesEntityCommand toCommand(KingDeePayExpensesDetailDto dto);

}
