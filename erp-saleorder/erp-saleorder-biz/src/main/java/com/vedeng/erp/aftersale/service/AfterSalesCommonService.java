package com.vedeng.erp.aftersale.service;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.AfterSalesInstallstion;
import com.vedeng.aftersales.model.vo.AfterSalesDetailVo;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesInstallstionVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.Action;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.erp.aftersale.domain.dto.*;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDetailDto;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDto;
import com.vedeng.finance.model.*;
import com.vedeng.finance.model.vo.PayApplyVo;
import com.vedeng.order.model.Saleorder;
import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/15 16:19
 * @desc :
 */
public interface AfterSalesCommonService {

    /**
     * 保存工程师
     * @param afterSalesInstallstionVo
     * @return
     */
    int saveAfterSalesEngineer(AfterSalesInstallstionVo afterSalesInstallstionVo);

    /**
     * 获取工程师分页信息
     * @param afterSales
     * @param page
     * @return
     */
    Map<String, Object> getEngineerPage(AfterSalesVo afterSales, Page page);

    /**
     * 获取地址
     * @param areaId
     * @return
     */
    String getAddressByAreaId(Integer areaId);

    /**
     * <b>Description:</b><br>
     * 保存直发出入库信息
     *
     * @param afterSalesDirectInfo, user
     * @return int
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/22 15:00
     */
    int saveDirectStockInfo(AfterSalesDirectInfo afterSalesDirectInfo, User user);

    /**
     * 根据订单id查询对应的售后订单列表
     * @param afterSalesVo
     * @return
     */
    List<AfterSalesVo> getAfterSalesVoListByOrderId(AfterSalesVo afterSalesVo);

    /**
     * 订单锁定是更新预警状态
     *
     * @param saleorderId
     * @return
     */
    ResultInfo updateLockSaleorderWarning(Integer saleorderId);

    /**
     * 保存工程师评价
     * @param afterSalesInstallstion
     * @return
     */
    ResultInfo saveInstallstionScore(AfterSalesInstallstion afterSalesInstallstion);

    /**
     * 保存收款状态
     * @param afterSalesId
     * @param status
     */
    void saveCollectionAmountStatus(Integer afterSalesId, Integer status);

    /**
     * 保存付款状态
     * @param afterSalesId
     * @param status
     */
    void savePayAmountStatuts(Integer afterSalesId, Integer status);

    /**
     * 保存开票状态
     * @param afterSalesId
     * @param status
     */
    void saveMakeOutInvoiceStatus(Integer afterSalesId, Integer status);

    /**
     *
     * <b>Description:</b><br> 根据id查询售后订单
     * @param afterSales
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2017年10月18日 下午4:03:25
     */
    AfterSalesVo getAfterSalesVoListById(AfterSales afterSales);

    /**
     * <b>Description:</b><br> 保存新增的资金流水
     * @param capitalBill
     * @return
     * @Note
     * <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年9月13日 下午5:48:37
     */
    ResultInfo<?> saveAddCapitalBill(CapitalBill capitalBill);

    /**
     *  From the DB migration
     *  <b>Description:</b><br> 保存新增的资金流水
     * @param capitalBill
     * @return
     */
    ResultInfo<?> saveAddCaptitalBillDB(CapitalBill capitalBill) throws Exception;

    /**
     * 保存收款状态-安调
     * @param relatedId
     */
    void saveCollectionAmountAtStatus(Integer relatedId);

    /**
     *
     * <b>Description:</b><br> 审核操作
     * @param request
     * @param taskId
     * @param comment 备注信息
     * @param assignee 审核人（非必填可以用null）
     * @param variables 需要传的参数集合Map
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年11月28日 下午6:18:52
     */
    ResultInfo<?> complementTask(HttpServletRequest request, String taskId, String comment,String assignee,Map<String, Object> variables);

    /**
     * 申请发票-更新开票状态
     * @param relatedId
     */
    void saveMakeOutInvoiceCompleteData(Integer relatedId);

    void addTask(List<Action> list);

    /**
     * 新增action
     * @param string2
     * @param moduleName
     * @param controllerName
     */
    int addTask(String string2, String moduleName, String controllerName);

    /**
     * 是否存在同名actionName
     * @param actionName
     * @param moduleName
     * @param controllerName
     * @return
     */
    Boolean getTask(String actionName, String moduleName, String controllerName);


    /**
     * 刷新付款状态
     * @param afterSalesId
     */
    void savePayAmountAtStatuts(Integer afterSalesId);

    /**
     * 获取附件
     * @param afterSalesVo
     * @return
     */
    List<Attachment> getAttachmentListByParam(AfterSalesVo afterSalesVo);

    /**
     * 保存附件
     * @param afterSalesVo
     * @param fileName
     * @param fileUri
     * @return
     */
    Boolean saveAttachment(AfterSalesVo afterSalesVo, String[] fileName, String[] fileUri);

    Boolean checkPic(String picUrl);


    /**
     * 保存编辑售后
     * @param afterSalesVo
     * @return
     */
    Integer saveEditAfterSales(AfterSalesVo afterSalesVo) throws Exception;

    /**
     * 获取字段表，ByParentID
     * @param parentId
     * @return
     */
    List<SysOptionDefinition> getSysOptionDefinitionList(Integer parentId);

    /**
     * <b>Description:</b><br> 查询售后服务费信息
     * @param afterSalesDetail
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2018年4月16日 上午11:04:41
     */
    AfterSalesDetailVo getAfterSalesDetailVoByParam(AfterSalesDetail afterSalesDetail);


    /**
     * <b>Description:</b><br> 付款申请通过
     * @param payApply
     * @return
     * @Note
     */
    ResultInfo<?> payApplyPass(PayApply payApply);

    /**
     * <b>Description:</b><br> 财务退款业务操作
     * From the DB migration
     * @param capitalBill
     * @return
     */
    ResultInfo<?> saveRefundCapitalBillNew(CapitalBill capitalBill);

    /**
     * <b>Description:</b><br> 获取售后安调维修申请付款页面的信息--工程师和安调维修费用
     * @param afterSales
     * @return
     * @Note
     */
    AfterSalesVo getAfterSalesApplyPay(AfterSalesVo afterSales);

    /**
     * <b>Description:</b><br> 保存申请付款
     * @param payApplyVo
     * @param user
     * @return
     */
    ResultInfo<?> saveApplyPay(PayApplyVo payApplyVo, User user);

    /**
     * <b>Description:</b><br> 根据售后ID获取交易对象信息
     * @param afterDetailVo
     * @return
     */
    AfterSalesDetailVo getAfterCapitalBillInfo(AfterSalesDetailVo afterDetailVo);

    /**
     * <b>Description:</b><br> 获取付款申请详情列表信息（根据ID）
     * @param payApplyId
     * @return
     */
    List<PayApplyDetail> getPayApplyDetailList(Integer payApplyId);

    /**
     *
     * <b>Description:</b><br> 根据订单ID刷新付款状态
     * @param saleorderId
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年10月13日 下午4:06:14
     */
    ResultInfo updatePaymentStatusBySaleorderId(Integer saleorderId, CapitalBill cb);

    ResultInfo saveAddCapitalBillSimple(CapitalBill cb, Integer traderType, Integer traderMode, Integer bussinessType, BigDecimal amount, Integer detailTraderType, Integer traderId);

    void putOrderPayStatustoHC(Saleorder saleorder);

    /**
     * <b>Description:</b><br> 根据订单ID刷新收款状态
     * @param buyorderId
     * @return
     * @Note
     * <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年10月16日 下午3:27:10
     */
    ResultInfo updatePaymentStatusByBuyorderId(Integer buyorderId, CapitalBill cb);

    /**
     * <b>Description:</b><br> 保存售后开票申请
     * From the DB migration
     * @param invoiceApply
     * @return
     */
    int saveOpenInvoceApply(InvoiceApply invoiceApply);

    /**
     * <b>Description:</b><br> 获取售后安调开具发票信息
     * @param afterSalesGoodsVo
     * @return
     */
    AfterSalesGoodsVo getAfterOpenInvoiceInfoAt(AfterSalesGoodsVo afterSalesGoodsVo);

    /**
     * <b>Description:</b><br> 保存财务售后安调开票信息
     * @param invoice
     * @return
     */
    ResultInfo<?> saveAfterOpenInvoiceAt(Invoice invoice);

    /**
     * <b>Description:</b><br> 编辑查询工程师与售后产品信息
     * @param afterSalesInstallstionVo
     * @return
     */
    AfterSalesInstallstionVo getAfterSalesInstallstionVo(AfterSalesInstallstionVo afterSalesInstallstionVo);

    /**
     * <b>Description:</b><br> 获取安调信息
     * @param afterSalesInstallstion
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年9月26日 下午2:53:58
     */
    AfterSalesInstallstion getAfterSalesInstallstion(AfterSalesInstallstion afterSalesInstallstion);

    /**
     * 影响范围
     *  打开新增售后安调服务记录页面
     * <AUTHOR>
     * @desc 根据售后单id获取需要维护的设备的信息
     * @param aftersalesId
     * @return
     */
    List<AfterSalesInstallServiceRecordDetailDto> getAfterSalesInstallGoodsInfoAdd(Integer aftersalesId);

    /**
     * 保存售后安调服务记录
     * @param  dto
     */
    void saveServiceRecord(AfterSalesInstallServiceRecordDto dto);

    /**
     * 查询保存的安调服务记录
     */
    void getAfterSalesInstallInfoEdit(Integer afterSalesServiceId,ModelAndView mv);

    /**
     * 修改售后安调服务记录
     */
    void updateServiceRecord(AfterSalesInstallServiceRecordDto dto);

    List<AfterSalesInstallServiceRecordDetail> selectDetail(Integer id);

    /**
     * 医修帮取消下派接口
     * @param productSelectionDispatchDto
     * @return
     */
    Boolean cancelDispatch(ProductSelectionDispatchDto productSelectionDispatchDto);

    /**
     * 医修帮下派接口
     * @param afterSaleDto
     * @return
     */
    Boolean confirmationDispatch(AfterSaleDto afterSaleDto);

    /**
     * 推送售后单关闭完结状态给医修帮
     * @param afterSalesToYxbDto
     * @return
     */
    Boolean closeOrCompletedYxb(AfterSalesToYxbDto afterSalesToYxbDto);
    /**
     * 推送售后单付款交易记录给医修帮
     */
    void saveYxbPayRecord(AfterSalesToYxbDto afterSalesToYxbDto);
    /**
     * 医修帮回传跟进记录
     */
    Boolean  saveYxbFollowRecord(AfterSalesFollowUpRecordDto afterSalesFollowUpRecordDto);

    /**
     * 查询售后单信息
     *
     * @param afterSalesNo 售后单号
     * @return AfterSales 售后单
     */
    AfterSales queryAfterSalesByNo(String afterSalesNo);

    List<String> yxbFileSava(List<String> urls);

    void modifyAfterSalesContactInfo(ModifyAfterSalesContactInfoDto modifyAfterSalesContactInfoDto);

    void afterSalesEvaluationReturn(AfterSalesEvaluationReturnDto afterSalesEvaluationReturnDto);

    /**
     * 售后退款信息
     * @param afterSalesId
     * @return
     */
    AfterSalesRefundInfoDto refundDetail(Integer afterSalesId);

    /**
     * 修改售后退款信息
     * @param afterSalesRefundInfoDto
     */
    void updateRefundInfo(AfterSalesRefundInfoDto afterSalesRefundInfoDto, HttpServletRequest request);

    /**
     * 编辑售后退款校验
     * @param afterSalesId
     */
    void updateRefundCheck(Integer afterSalesId);

    /**
     * 查看售后退款校验
     * @param afterSalesId
     */
    void detailRefundCheck(Integer afterSalesId);
}
