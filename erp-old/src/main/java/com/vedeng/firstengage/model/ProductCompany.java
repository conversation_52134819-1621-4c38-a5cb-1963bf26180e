package com.vedeng.firstengage.model;

public class ProductCompany {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ID
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    private Integer productCompanyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_CHINESE_NAME
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    private String productCompanyChineseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ENGLISH_NAME
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    private String productCompanyEnglishName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ADDRESS
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    private String productCompanyAddress;


    /**
     * 生产企业许可证号或备案凭证编号
     * @auther: duke.li
     * @date: 2019/6/21 14:03
     */
    @Deprecated
    private String productCompanyLicence;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ID
     *
     * @return the value of T_PRODUCT_COMPANY.PRODUCT_COMPANY_ID
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    public Integer getProductCompanyId() {
        return productCompanyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ID
     *
     * @param productCompanyId the value for T_PRODUCT_COMPANY.PRODUCT_COMPANY_ID
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    public void setProductCompanyId(Integer productCompanyId) {
        this.productCompanyId = productCompanyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_CHINESE_NAME
     *
     * @return the value of T_PRODUCT_COMPANY.PRODUCT_COMPANY_CHINESE_NAME
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    public String getProductCompanyChineseName() {
        return productCompanyChineseName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_CHINESE_NAME
     *
     * @param productCompanyChineseName the value for T_PRODUCT_COMPANY.PRODUCT_COMPANY_CHINESE_NAME
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    public void setProductCompanyChineseName(String productCompanyChineseName) {
        this.productCompanyChineseName = productCompanyChineseName == null ? null : productCompanyChineseName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ENGLISH_NAME
     *
     * @return the value of T_PRODUCT_COMPANY.PRODUCT_COMPANY_ENGLISH_NAME
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    public String getProductCompanyEnglishName() {
        return productCompanyEnglishName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ENGLISH_NAME
     *
     * @param productCompanyEnglishName the value for T_PRODUCT_COMPANY.PRODUCT_COMPANY_ENGLISH_NAME
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    public void setProductCompanyEnglishName(String productCompanyEnglishName) {
        this.productCompanyEnglishName = productCompanyEnglishName == null ? null : productCompanyEnglishName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ADDRESS
     *
     * @return the value of T_PRODUCT_COMPANY.PRODUCT_COMPANY_ADDRESS
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    public String getProductCompanyAddress() {
        return productCompanyAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRODUCT_COMPANY.PRODUCT_COMPANY_ADDRESS
     *
     * @param productCompanyAddress the value for T_PRODUCT_COMPANY.PRODUCT_COMPANY_ADDRESS
     *
     * @mbg.generated Wed Mar 20 18:36:27 CST 2019
     */
    public void setProductCompanyAddress(String productCompanyAddress) {
        this.productCompanyAddress = productCompanyAddress == null ? null : productCompanyAddress.trim();
    }

    public String getProductCompanyLicence() {
        return productCompanyLicence;
    }

    public void setProductCompanyLicence(String productCompanyLicence) {
        this.productCompanyLicence = productCompanyLicence;
    }
}