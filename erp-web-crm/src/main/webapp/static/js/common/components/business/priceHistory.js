
Vue.component('price-history', {
    template: ` <div>
        <div style="width:1px;height:1px;pointer-events:none;opacity:0;" ref="placeholder"></div>
        <div class="price-history-wrap" ref="wrap" :class="{show: show || ontouch}" @mouseenter="ontouch=true" @mouseleave="ontouch=false" :style="positionStyle">
            <div class="price-history-table">
                <div class="price-history-tr">
                    <div class="price-history-th">修改人</div>
                    <div class="price-history-th txt-right">修改金额</div>
                    <div class="price-history-th txt-right">修改时间</div>
                </div>
                <div class="price-history-tbody" v-if="show || ontouch">
                    <template v-if="loading">
                        <div class="price-history-loading">
                            <i class="vd-ui_icon icon-loading"></i>
                            <div class="loading-txt">加载中...</div>
                        </div>
                    </template>
                    <template v-else>
                        <template v-if="list && list.length">
                            <div class="price-history-tr" v-for="(priceItem, priceIndex) in list">
                                <div class="price-history-td">
                                    <div class="option-user-info">
                                        <div class="user-avatar">
                                            <img :src="priceItem.aliasHeadPicture || GLOBAL.defaultAvatar" alt=""/>
                                        </div>
                                        <div class="user-name text-line-1" :title="priceItem.updaterName">{{ priceItem.updaterName }}</div>
                                    </div>
                                </div>
                                <div class="price-history-td txt-right">
                                    <div class="price-txt">{{ priceItem.priceTxt }}</div>
                                </div>
                                <div class="price-history-td txt-right txt-grey">{{ priceItem.formatAddTime }}</div>
                            </div>
                        </template>
                        <div v-else class="price-history-empty">
                            无修改记录
                        </div>
                    </template>
                </div>
            </div>
        </div>
    <div>`,
    props: {
        show: {
            type: Boolean,
            default: false  
        },
        loading: {
            type: Boolean,
            default: false
        },
        list: {
            type: Array,
            default() {
                return [];
            }
        },
        reset: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        show() {
            if(this.show) {
                this.showCnt();
            }
        },
        reset() {
            if(this.reset && this.show) {
                this.showCnt();
                console.log('calc')
            }
        }
    },
    data() {
        return {
            positionStyle: '',
            ontouch: false
        }
    },
    mounted() {
        // window.addEventListener('scroll', this.showCnt)
    },
    methods: {
        showCnt() {
            document.body.append(this.$refs.wrap);
            let wrapPosition = this.$refs.placeholder.getBoundingClientRect();
            let top = wrapPosition.top - 16 + window.scrollY;
            this.positionStyle = `position: absolute;top:${top}px;left:${wrapPosition.left + 5 + wrapPosition.width/2}px;`
        },
        hideCnt() {
            // this.isShow = false;
        }
    }
})