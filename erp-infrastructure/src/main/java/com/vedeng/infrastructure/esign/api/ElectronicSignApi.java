package com.vedeng.infrastructure.esign.api;

import com.vedeng.base.api.dto.EsignReqDTO;
import com.vedeng.base.api.dto.EsignResDTO;
import com.vedeng.base.api.dto.reqParam.*;
import com.vedeng.base.api.dto.resParam.DoubleSignFlowDto;
import com.vedeng.base.api.dto.resParam.FlowDto;
import com.vedeng.base.api.dto.resParam.PositionInfo;
import com.vedeng.base.api.service.EsignKeyApi;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 电子签章
 * @date 2021/12/9 14:42
 */
@FeignApi(serverName = ServerConstants.BASE_SERVER)
public interface ElectronicSignApi extends EsignKeyApi {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/uploadFile")
    @Override
    RestfulResult<FileInfo> uploadFile(@RequestBody UploadDto var1);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/searchKeywordsPosition")
    @Override
    RestfulResult<PositionInfo> searchKeywordsPosition(@RequestBody FileInfo var1);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/createFlowOneStep")
    @Override
    RestfulResult<FlowDto> createFlowOneStep(@RequestBody FlowDto var1);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/createFlowOneStepForDoubleCompany")
    @Override
    RestfulResult<FlowDto> createFlowOneStepForDoubleCompany(@RequestBody DoubleSignFlowDto doubleSignFlowDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/queryFileAndSendToErp")
    @Override
    void queryFileAndSendToErp(SignCallbackDto var1);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/querySignUrls")
    RestfulResult<EsignResDTO> querySignUrls(@RequestBody EsignReqDTO esignReqDTO);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/queryEsignAccount")
    @Override
    RestfulResult<EAccount> queryEsignAccount(@RequestBody EAccount var1);


    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/revokeSignFlow")
    @Override
    RestfulResult<Boolean> revokeSignFlow(@RequestBody BusinessInfo var1);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /esign/mqCallback")
    @Override
    RestfulResult<Integer> mqCallback(@RequestBody CallbackDto var1);

}
