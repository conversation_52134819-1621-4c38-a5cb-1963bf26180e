<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.SaleOrderMapper">
    <select id="getSkuFairValueSaleOrderList" resultType="com.vedeng.erp.saleorder.dto.SaleorderInfoDto">
        SELECT ts.SALEORDER_NO,
               ts.VALID_TIME,
               tsg.PRICE
        FROM T_SALEORDER ts
                 LEFT JOIN T_SALEORDER_GOODS tsg ON
            tsg.SALEORDER_ID = ts.SALEORDER_ID
        WHERE ts.VALID_TIME > 1546272000000
          AND ts.VALID_STATUS = 1
          AND tsg.PRICE > 0
          AND tsg.IS_DELETE = 0
          AND ts.DELIVERY_STATUS != 0
          AND tsg.GOODS_ID = #{goodsId,jdbcType=INTEGER}
        ORDER BY ts.VALID_TIME DESC
        LIMIT 10
    </select>

    <select id="getCommunicateSaleOrderInfo" resultType="java.util.Map">
        SELECT C.SALEORDER_ID AS RELATED_ID,
               C.SALEORDER_NO AS ORDER_NO
        FROM T_SALEORDER C
        WHERE C.SALEORDER_ID IN
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <update id="updateSaleorderStatus" parameterType="com.vedeng.erp.saleorder.dto.SaleorderInfoDto">
        update T_SALEORDER
        <set>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="deliveryStatus != null">
                DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
            </if>
            <if test="deliveryTime != null">
                DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
            </if>
            <if test="arrivalStatus != null">
                ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
            </if>
            <if test="arrivalTime != null">
                ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
            </if>
        </set>
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </update>

    <update id="updateConfirmationFormAudit" parameterType="com.vedeng.erp.saleorder.dto.SaleorderInfoDto">
        update T_SALEORDER
        <set>
            <if test="confirmationFormAudit != null">
                CONFIRMATION_FORM_AUDIT = #{confirmationFormAudit,jdbcType=INTEGER},
            </if>
        </set>
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER};
    </update>

    <select id="selectByTraderName" resultType="com.vedeng.erp.saleorder.dto.SaleorderInfoDto">
        select a.*
        from T_SALEORDER a
        WHERE a.company_id = 1
          AND a.VALID_STATUS = 1
          AND a.TRADER_NAME = #{traderName,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="selectLastSaleOrderByTraderName" resultType="com.vedeng.erp.saleorder.dto.SaleorderInfoDto">
        select a.*
        from T_SALEORDER a
        WHERE a.company_id = 1
          AND a.VALID_STATUS = 1
          AND a.STATUS NOT IN (2,3)
          AND a.TRADER_NAME = #{traderName,jdbcType=VARCHAR}
            ORDER BY SALEORDER_ID DESC
        limit 1
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.domain.entity.SaleorderEntity">
        update T_SALEORDER
        <set>
            <if test="quoteorderId != null">
                QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                PARENT_ID = #{parentId,jdbcType=INTEGER},
            </if>
            <if test="saleorderNo != null">
                SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
            </if>
            <if test="mSaleorderNo != null">
                M_SALEORDER_NO = #{mSaleorderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                `SOURCE` = #{source,jdbcType=BOOLEAN},
            </if>
            <if test="creatorOrgId != null">
                CREATOR_ORG_ID = #{creatorOrgId,jdbcType=INTEGER},
            </if>
            <if test="creatorOrgName != null">
                CREATOR_ORG_NAME = #{creatorOrgName,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=INTEGER},
            </if>
            <if test="orgName != null">
                ORG_NAME = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=INTEGER},
            </if>
            <if test="validOrgId != null">
                VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
            </if>
            <if test="validOrgName != null">
                VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
            </if>
            <if test="validUserId != null">
                VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
            </if>
            <if test="validStatus != null">
                VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
            </if>
            <if test="validTime != null">
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=BOOLEAN},
            </if>
            <if test="purchaseStatus != null">
                PURCHASE_STATUS = #{purchaseStatus,jdbcType=BOOLEAN},
            </if>
            <if test="lockedStatus != null">
                LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceTime != null">
                INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
            </if>
            <if test="paymentStatus != null">
                PAYMENT_STATUS = #{paymentStatus,jdbcType=BOOLEAN},
            </if>
            <if test="paymentTime != null">
                PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
            </if>
            <if test="deliveryStatus != null">
                DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
            </if>
            <if test="deliveryTime != null">
                DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
            </if>
            <if test="isCustomerArrival != null">
                IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=BOOLEAN},
            </if>
            <if test="arrivalStatus != null">
                ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
            </if>
            <if test="arrivalTime != null">
                ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
            </if>
            <if test="serviceStatus != null">
                SERVICE_STATUS = #{serviceStatus,jdbcType=BOOLEAN},
            </if>
            <if test="haveAccountPeriod != null">
                HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=BOOLEAN},
            </if>
            <if test="isPayment != null">
                IS_PAYMENT = #{isPayment,jdbcType=BOOLEAN},
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="customerType != null">
                CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
            </if>
            <if test="customerNature != null">
                CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
            </if>
            <if test="traderName != null">
                TRADER_NAME = #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="traderContactName != null">
                TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
            </if>
            <if test="traderContactMobile != null">
                TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="traderContactTelephone != null">
                TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
            </if>
            <if test="traderAddressId != null">
                TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
            </if>
            <if test="traderAreaId != null">
                TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
            </if>
            <if test="traderArea != null">
                TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
            </if>
            <if test="traderAddress != null">
                TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
            </if>
            <if test="traderComments != null">
                TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderId != null">
                TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderName != null">
                TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactId != null">
                TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderContactName != null">
                TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactMobile != null">
                TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderContactTelephone != null">
                TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderAddressId != null">
                TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderAreaId != null">
                TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
            </if>
            <if test="takeTraderArea != null">
                TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
            </if>
            <if test="takeTraderAddress != null">
                TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
            </if>
            <if test="isSendInvoice != null">
                IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceTraderId != null">
                INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
            </if>
            <if test="invoiceTraderName != null">
                INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTraderContactId != null">
                INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
            </if>
            <if test="invoiceTraderContactName != null">
                INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTraderContactMobile != null">
                INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTraderContactTelephone != null">
                INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTraderAddressId != null">
                INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
            </if>
            <if test="invoiceTraderAreaId != null">
                INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId,jdbcType=INTEGER},
            </if>
            <if test="invoiceTraderArea != null">
                INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTraderAddress != null">
                INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
            </if>
            <if test="salesAreaId != null">
                SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
            </if>
            <if test="salesArea != null">
                SALES_AREA = #{salesArea,jdbcType=VARCHAR},
            </if>
            <if test="terminalTraderId != null">
                TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
            </if>
            <if test="terminalTraderName != null">
                TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
            </if>
            <if test="terminalTraderType != null">
                TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
            </if>
            <if test="invoiceType != null">
                INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="freightDescription != null">
                FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
            </if>
            <if test="deliveryType != null">
                DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
            </if>
            <if test="deliveryMethod != null">
                DELIVERY_METHOD = #{deliveryMethod,jdbcType=INTEGER},
            </if>
            <if test="logisticsId != null">
                LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
            </if>
            <if test="paymentType != null">
                PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
            </if>
            <if test="prepaidAmount != null">
                PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
            </if>
            <if test="accountPeriodAmount != null">
                ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
            </if>
            <if test="periodDay != null">
                PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
            </if>
            <if test="logisticsCollection != null">
                LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BOOLEAN},
            </if>
            <if test="retainageAmount != null">
                RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
            </if>
            <if test="retainageAmountMonth != null">
                RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
            </if>
            <if test="paymentComments != null">
                PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
            </if>
            <if test="additionalClause != null">
                ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
            </if>
            <if test="logisticsComments != null">
                LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
            </if>
            <if test="financeComments != null">
                FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="invoiceComments != null">
                INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDirect != null">
                DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BOOLEAN},
            </if>
            <if test="supplierClause != null">
                SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
            </if>
            <if test="haveAdvancePurchase != null">
                HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=BOOLEAN},
            </if>
            <if test="advancePurchaseStatus != null">
                ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=BOOLEAN},
            </if>
            <if test="advancePurchaseComments != null">
                ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
            </if>
            <if test="advancePurchaseTime != null">
                ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
            </if>
            <if test="isUrgent != null">
                IS_URGENT = #{isUrgent,jdbcType=BOOLEAN},
            </if>
            <if test="urgentAmount != null">
                URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
            </if>
            <if test="haveCommunicate != null">
                HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BOOLEAN},
            </if>
            <if test="prepareComments != null">
                PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
            </if>
            <if test="marketingPlan != null">
                MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
            </if>
            <if test="statusComments != null">
                STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
            </if>
            <if test="syncStatus != null">
                SYNC_STATUS = #{syncStatus,jdbcType=BOOLEAN},
            </if>
            <if test="logisticsApiSync != null">
                LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=BOOLEAN},
            </if>
            <if test="logisticsWxsendSync != null">
                LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=BOOLEAN},
            </if>
            <if test="satisfyInvoiceTime != null">
                SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
            </if>
            <if test="satisfyDeliveryTime != null">
                SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
            </if>
            <if test="isSalesPerformance != null">
                IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=BOOLEAN},
            </if>
            <if test="salesPerformanceTime != null">
                SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
            </if>
            <if test="salesPerformanceModTime != null">
                SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
            </if>
            <if test="isDelayInvoice != null">
                IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceMethod != null">
                INVOICE_METHOD = #{invoiceMethod,jdbcType=BOOLEAN},
            </if>
            <if test="lockedReason != null">
                LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
            </if>
            <if test="costUserIds != null">
                COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
            </if>
            <if test="ownerUserId != null">
                OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
            </if>
            <if test="invoiceEmail != null">
                INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
            </if>
            <if test="paymentMode != null">
                PAYMENT_MODE = #{paymentMode,jdbcType=BOOLEAN},
            </if>
            <if test="payType != null">
                PAY_TYPE = #{payType,jdbcType=BOOLEAN},
            </if>
            <if test="isApplyInvoice != null">
                IS_APPLY_INVOICE = #{isApplyInvoice,jdbcType=BOOLEAN},
            </if>
            <if test="applyInvoiceTime != null">
                APPLY_INVOICE_TIME = #{applyInvoiceTime,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="adkSaleorderNo != null">
                ADK_SALEORDER_NO = #{adkSaleorderNo,jdbcType=VARCHAR},
            </if>
            <if test="createMobile != null">
                CREATE_MOBILE = #{createMobile,jdbcType=VARCHAR},
            </if>
            <if test="bdtraderComments != null">
                BDTRADER_COMMENTS = #{bdtraderComments,jdbcType=VARCHAR},
            </if>
            <if test="closeComments != null">
                CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
            </if>
            <if test="bdMobileTime != null">
                BD_MOBILE_TIME = #{bdMobileTime,jdbcType=BIGINT},
            </if>
            <if test="webTakeDeliveryTime != null">
                WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
            </if>
            <if test="actionId != null">
                ACTION_ID = #{actionId,jdbcType=INTEGER},
            </if>
            <if test="isCoupons != null">
                IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
            </if>
            <if test="couponmoney != null">
                COUPONMONEY = #{couponmoney,jdbcType=DECIMAL},
            </if>
            <if test="originalAmount != null">
                ORIGINAL_AMOUNT = #{originalAmount,jdbcType=DECIMAL},
            </if>
            <if test="elSaleordreNo != null">
                EL_SALEORDRE_NO = #{elSaleordreNo,jdbcType=VARCHAR},
            </if>
            <if test="isPrintout != null">
                IS_PRINTOUT = #{isPrintout,jdbcType=BOOLEAN},
            </if>
            <if test="outIsFlag != null">
                OUT_IS_FLAG = #{outIsFlag,jdbcType=BOOLEAN},
            </if>
            <if test="updateDataTime != null">
                UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
            </if>
            <if test="realPayAmount != null">
                REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
            </if>
            <if test="realReturnAmount != null">
                REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
            </if>
            <if test="realTotalAmount != null">
                REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
            </if>
            <if test="sendToPc != null">
                SEND_TO_PC = #{sendToPc,jdbcType=BOOLEAN},
            </if>
            <if test="retentionMoney != null">
                RETENTION_MONEY = #{retentionMoney,jdbcType=DECIMAL},
            </if>
            <if test="retentionMoneyDay != null">
                RETENTION_MONEY_DAY = #{retentionMoneyDay,jdbcType=INTEGER},
            </if>
            <if test="isSameAddress != null">
                IS_SAME_ADDRESS = #{isSameAddress,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceSendNode != null">
                INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=BOOLEAN},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="nopaybackAmount != null">
                NOPAYBACK_AMOUNT = #{nopaybackAmount,jdbcType=DECIMAL},
            </if>
            <if test="contractUrl != null">
                CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
            </if>
            <if test="autoAudit != null">
                AUTO_AUDIT = #{autoAudit,jdbcType=INTEGER},
            </if>
            <if test="isRisk != null">
                IS_RISK = #{isRisk,jdbcType=BOOLEAN},
            </if>
            <if test="riskComments != null">
                RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
            </if>
            <if test="riskTime != null">
                RISK_TIME = #{riskTime,jdbcType=BIGINT},
            </if>
            <if test="groupCustomerId != null">
                GROUP_CUSTOMER_ID = #{groupCustomerId,jdbcType=INTEGER},
            </if>
            <if test="isContractReturn != null">
                IS_CONTRACT_RETURN = #{isContractReturn,jdbcType=TINYINT},
            </if>
            <if test="isDeliveryorderReturn != null">
                IS_DELIVERYORDER_RETURN = #{isDeliveryorderReturn,jdbcType=TINYINT},
            </if>
            <if test="deliveryClaim != null">
                DELIVERY_CLAIM = #{deliveryClaim,jdbcType=BOOLEAN},
            </if>
            <if test="deliveryDelayTime != null">
                DELIVERY_DELAY_TIME = #{deliveryDelayTime,jdbcType=BIGINT},
            </if>
            <if test="billPeriodSettlementType != null">
                BILL_PERIOD_SETTLEMENT_TYPE = #{billPeriodSettlementType,jdbcType=TINYINT},
            </if>
            <if test="isNew != null">
                IS_NEW = #{isNew,jdbcType=TINYINT},
            </if>
            <if test="confirmStatus != null">
                CONFIRM_STATUS = #{confirmStatus,jdbcType=INTEGER},
            </if>
            <if test="confirmTime != null">
                CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
            </if>
            <if test="contractNoStampUrl != null">
                CONTRACT_NO_STAMP_URL = #{contractNoStampUrl,jdbcType=VARCHAR},
            </if>
            <if test="onlineReceiptStatus != null">
                ONLINE_RECEIPT_STATUS = #{onlineReceiptStatus,jdbcType=BOOLEAN},
            </if>
            <if test="confirmationFormUpload != null">
                CONFIRMATION_FORM_UPLOAD = #{confirmationFormUpload,jdbcType=BOOLEAN},
            </if>
            <if test="confirmationFormAudit != null">
                CONFIRMATION_FORM_AUDIT = #{confirmationFormAudit,jdbcType=BOOLEAN},
            </if>
            <if test="confirmationSubmitTime != null">
                CONFIRMATION_SUBMIT_TIME = #{confirmationSubmitTime,jdbcType=BIGINT},
            </if>
            <if test="prepareReaseonType != null">
                PREPARE_REASEON_TYPE = #{prepareReaseonType,jdbcType=INTEGER},
            </if>
            <if test="contractReas != null">
                CONTRACT_REAS = #{contractReas,jdbcType=VARCHAR},
            </if>
            <if test="terminalTraderNature != null">
                TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
            </if>
        </set>
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </update>

    <select id="findSaleOrderInvoiceListBySaleorderId" resultType="com.vedeng.erp.saleorder.dto.SaleOrderInvoiceDto">
        SELECT
               A.INVOICE_ID AS "invoiceId",
               A.INVOICE_NO AS "invoiceNo",
               A.INVOICE_CODE AS "invoiceCode",
               A.ADD_TIME AS "addTime",
               A.OSS_FILE_URL AS "ossFileUrl"
        FROM T_INVOICE A
        WHERE A.RELATED_ID = #{saleorderId,jdbcType=INTEGER} AND A.TYPE = 505 AND A.COLOR_TYPE = 2 limit 500;

    </select>


    <!--auto generated by MybatisCodeHelper on 2023-09-25-->
    <select id="findBySaleorderId" resultType="com.vedeng.erp.saleorder.domain.entity.SaleorderEntity">
        select SALEORDER_ID,
        INVOICE_STATUS,
               CONTRACT_URL,
        TRADER_CONTACT_MOBILE,TRADER_CONTACT_TELEPHONE,TRADER_CONTACT_ID,TRADER_CONTACT_NAME,
        TAKE_TRADER_CONTACT_MOBILE,TAKE_TRADER_CONTACT_TELEPHONE,TAKE_TRADER_CONTACT_NAME,TAKE_TRADER_CONTACT_ID,
        INVOICE_TRADER_CONTACT_MOBILE,INVOICE_TRADER_CONTACT_TELEPHONE,INVOICE_TRADER_CONTACT_ID,INVOICE_TRADER_CONTACT_NAME,
        SALEORDER_NO,
        INVOICE_TYPE,
        SALEORDER_ID,
        TRADER_ID,TRADER_NAME,
        ORDER_TYPE,
        INVOICE_METHOD,
        PAYMENT_STATUS,
        VALID_STATUS,
        STATUS,
        LOCKED_STATUS,
        IS_SAME_ADDRESS,
        IS_SEND_INVOICE,
        ADD_TIME,
        USER_ID,
        REAL_TOTAL_AMOUNT,
        PAYMENT_TYPE,
        HAVE_ACCOUNT_PERIOD,
        ORIGINAL_AMOUNT,
        CONFIRMATION_FORM_AUDIT,
        TOTAL_AMOUNT,
        TERMINAL_TRADER_NATURE,ADDITIONAL_CLAUSE,
        CONTRACT_URL
        from T_SALEORDER
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-09-25-->
    <select id="findInvoiceTraderContactMobileBySaleorderId" resultType="java.lang.String">
        select INVOICE_TRADER_CONTACT_MOBILE
        from T_SALEORDER
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <select id="getSaleOrderListPage" resultType="com.vedeng.erp.mobile.dto.SaleOrderListResultDto">
        select TS.SALEORDER_ID as saleorderId,
               TS.SALEORDER_NO as saleorderNo,
               TSD.SUB_STATUS as orderStatus,
               TS.TRADER_NAME as traderName,
               TSD.CURRENT_USER_ID as currentUserId,
               TU.USERNAME    as currentUserName,
               TS.CONTRACT_URL as contractUrl,
               TS.INVOICE_METHOD as invoiceMethod,
               TS.IS_SEND_INVOICE as isSendInvoice
        from T_SALEORDER TS
                 left join T_SALEORDER_DATA TSD on TS.SALEORDER_ID = TSD.SALEORDER_ID
                 left join T_R_SALES_J_TRADER RJS on TS.TRADER_ID=RJS.TRADER_ID
                 left join T_USER TU on TU.USER_ID = TSD.CURRENT_USER_ID
        where TS.COMPANY_ID = 1
          and TS.`ORDER_TYPE` IN (0, 1, 5, 7, 8, 9)
        <if test="searchKey != null and searchKey != ''">
          and (TS.TRADER_NAME like concat('%', #{searchKey, jdbcType=VARCHAR}, '%') or TS.SALEORDER_NO like concat('%', #{searchKey, jdbcType=VARCHAR}, '%'))
        </if>
        <if test="orderStatusList != null and orderStatusList.size() != 0">
            and TSD.SUB_STATUS in
            <foreach item="item" index="index" collection="orderStatusList"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="currentOrgIdList != null and currentOrgIdList.size() != 0">
            and TSD.CURRENT_ORG_ID in
            <foreach item="item" index="index" collection="currentOrgIdList"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="currentUserIdList != null and currentUserIdList.size() != 0">
            and (TSD.CURRENT_USER_ID in
                <foreach item="item" index="index" collection="currentUserIdList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
                or
                RJS.SALE_USER_ID in
                <foreach item="item" index="index" collection="currentUserIdList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            )
        </if>
        <if test="currentOrgIdList == null and currentUserIdList == null ">
            and 1=2
        </if>
        order by TS.SALEORDER_ID desc
    </select>

    <select id="getBySaleOrderId" resultType="com.vedeng.erp.mobile.dto.SaleOrderDetailInfoDto">
        select TS.SALEORDER_ID                                                                   as saleorderId,
               TS.SALEORDER_NO                                                                   as saleorderNo,
               TSD.SUB_STATUS                                                                    as orderStatus,
               TS.TRADER_NAME                                                                    as traderName,
               TSD.CURRENT_USER_ID                                                               as currentUserId,
               TU.USERNAME                                                                       as currentUserName,
               TS.COMMENTS                                                                       as comments,
               FROM_UNIXTIME(IF(TS.ADD_TIME = 0, NULL, TS.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s') as addTime,
               TS.CONTRACT_URL                                                                   as contractUrl,
               TS.TAKE_TRADER_CONTACT_NAME                                                       as takeTraderContactName,
               TS.TAKE_TRADER_CONTACT_MOBILE                                                     as takeTraderContactMobile,
               TS.TAKE_TRADER_AREA                                                               as takeTraderArea,
               TS.TAKE_TRADER_ADDRESS                                                            as takeTraderAddress,
               TS.INVOICE_TRADER_NAME                                                            as invoiceTraderName,
               TSOD.TITLE                                                                        as invoiceType,
               TS.INVOICE_TRADER_CONTACT_NAME                                                    as invoiceTraderContactName,
               TS.INVOICE_TRADER_CONTACT_MOBILE                                                  as invoiceTraderContactMobile,
               TTC.EMAIL                                                                         as invoiceTraderContactEmail,
               TS.INVOICE_TRADER_AREA                                                            as invoiceTraderArea,
               TS.INVOICE_TRADER_ADDRESS                                                         as invoiceTraderAddress,
               TS.INVOICE_METHOD                                                                 as invoiceMethod,
               TS.IS_SEND_INVOICE                                                                as isSendInvoice
        from T_SALEORDER TS
                 left join T_SALEORDER_DATA TSD on TS.SALEORDER_ID = TSD.SALEORDER_ID
                 left join T_USER TU on TU.USER_ID = TSD.CURRENT_USER_ID
                 left join T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TS.INVOICE_TYPE
                 left join T_TRADER_CONTACT TTC on TTC.TRADER_CONTACT_ID = TS.INVOICE_TRADER_CONTACT_ID
        where TS.COMPANY_ID = 1
          and TS.SALEORDER_ID = #{saleorderId, jdbcType=INTEGER}
    </select>

    <select id="getRealTotalAmountOfSaleOrder" resultType="java.math.BigDecimal">
        SELECT if(COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE IN(1, 5),b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0) <![CDATA[ < ]]> 0,
        0,
        COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE IN(1, 5),b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0))
        FROM T_SALEORDER a
        LEFT JOIN
        (SELECT sum(bb.NUM * cc.PRICE) AS tk_amount,
        sum(bb.SKU_REFUND_AMOUNT) AS SKU_REFUND_AMOUNT,
        aa.ORDER_ID
        FROM T_AFTER_SALES aa
        LEFT JOIN T_AFTER_SALES_GOODS bb
        ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
        LEFT JOIN T_SALEORDER_GOODS cc
        ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
        WHERE     aa.TYPE = 539
        AND aa.SUBJECT_TYPE = 535
        AND aa.VALID_STATUS = 1
        AND aa.ATFER_SALES_STATUS = 2
        AND bb.GOODS_TYPE = 0 <!-- VDERP-7492 计算实际金额，需要剔除手续费 -->
        AND aa.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
        GROUP BY aa.ORDER_ID) AS b
        ON a.SALEORDER_ID = b.ORDER_ID
        WHERE a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>
    <select id="getByAfterSaleId" resultType="com.vedeng.erp.saleorder.domain.entity.SaleorderEntity">
        SELECT
            ts.*
        FROM
            T_AFTER_SALES tas
                LEFT JOIN T_SALEORDER ts ON
                tas.ORDER_ID = ts.SALEORDER_ID
        WHERE
            tas.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>

    <select id="getSaleorderInfoBySaleorderIds" resultType="com.vedeng.erp.saleorder.domain.entity.SaleorderEntity">
        SELECT
            ts.*
        FROM
            T_SALEORDER ts
        WHERE
            ts.SALEORDER_ID IN
            <foreach item="item" index="index" collection="saleorderIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
    </select>

    <select id="getSaleorderFinanceInfo" resultType="com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto">
        SELECT A.SALEORDER_ID,
        A.INVOICE_TYPE,
        A.TRADER_ID,
        A.TRADER_NAME,
        A.ORDER_TYPE,
        B.TAX_NUM,
        B.REG_ADDRESS,
        B.REG_TEL,
        B.BANK,
        B.BANK_ACCOUNT,
        B.AVERAGE_TAXPAYER_URI,
        B.BANK_CODE,
        D.REGION_TYPE,
        V.STATUS AS FINANCE_CHECK_STATUS,
        T.SOURCE AS TRADER_SOURCE
        FROM T_SALEORDER A
        LEFT JOIN T_TRADER T ON A.TRADER_ID=T.TRADER_ID
        LEFT JOIN T_TRADER_FINANCE B
        ON A.TRADER_ID = B.TRADER_ID AND B.TRADER_TYPE = 1
        LEFT JOIN T_VERIFIES_INFO V
        ON V.RELATE_TABLE_KEY = B.TRADER_FINANCE_ID AND V.RELATE_TABLE='T_TRADER_FINANCE'
        LEFT JOIN T_TRADER_ADDRESS C
        ON     A.TRADER_ID = C.TRADER_ID
        AND A.INVOICE_TRADER_ADDRESS_ID = C.TRADER_ADDRESS_ID
        AND C.TRADER_TYPE = 1
        AND C.IS_ENABLE = 1
        LEFT JOIN T_REGION D ON C.AREA_ID = D.REGION_ID
        WHERE A.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
        limit 1
    </select>

    <select id="querySaleOrderIdBySettlement" resultType="java.lang.Integer" parameterType="com.vedeng.erp.saleorder.dto.SaleOrderQueryDto">
        select a.SALEORDER_ID from T_SALEORDER a
        where a.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
        and a.IS_DELETE = 0
        and a.STATUS = #{status,jdbcType=INTEGER}
        and a.PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER}
        and a.HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=INTEGER}
        and a.LOCKED_STATUS = #{lockedStatus,jdbcType=INTEGER}
        and a.REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL}
        and a.TRADER_NAME = #{traderName,jdbcType=VARCHAR}
        and a.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
        and a.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
    </select>

    <select id="getContractVerifyStatusBySaleOrderId" resultType="java.lang.Integer">
        select CONTRACT_VERIFY_STATUS
        from T_SALEORDER_DATA
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <select id="getSaleOrderInvoiceTraderContactName" resultType="java.lang.String">
        select INVOICE_TRADER_CONTACT_NAME
        from T_SALEORDER
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <select id="getSaleorderCanInvoiceListPage" resultType="com.vedeng.erp.saleorder.dto.SaleorderInfoDto">
        SELECT ts.*
        FROM T_SALEORDER ts
                 left join (select RELATED_ID,
                                   max(INVOICE_APPLY_ID) as INVOICE_APPLY_ID
                            from T_INVOICE_APPLY
                            where TYPE = 505
                            group by RELATED_ID) tiaa on ts.SALEORDER_ID = tiaa.RELATED_ID
                 left join T_INVOICE_APPLY tia on tiaa.INVOICE_APPLY_ID = tia.INVOICE_APPLY_ID
        WHERE ts.VALID_STATUS = 1
          AND ts.VALID_TIME >= 1640966400000
          AND ts.COMPANY_ID = 1
          AND ts.STATUS in (1, 2)
          AND ts.INVOICE_STATUS != 2
          AND ts.LOCKED_STATUS = 0
          AND ts.ORDER_TYPE != 2
          AND ts.IS_DELAY_INVOICE = 0
          AND (tia.VALID_STATUS is null or NOT (
        (tia.IS_ADVANCE = 1 AND tia.ADVANCE_VALID_STATUS IN (0, 1) AND tia.VALID_STATUS = 0)
        OR
        (tia.IS_ADVANCE = 0 AND tia.VALID_STATUS = 0)))
          AND ts.REAL_TOTAL_AMOUNT > 0
            <if test="saleorderNo != null and saleorderNo != ''">
                AND ts.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
            </if>
        group by ts.SALEORDER_ID
        order by ts.SALEORDER_ID desc
    </select>

    <select id="getBelongB2bSaleOrderIdBySaleOrderIds" resultType="java.lang.Integer">
        SELECT SO.SALEORDER_ID
        FROM T_SALEORDER SO
        LEFT JOIN T_R_TRADER_J_USER A ON SO.TRADER_ID = A.TRADER_ID
        LEFT JOIN T_USER B ON A.USER_ID = B.USER_ID
        LEFT JOIN T_R_USER_POSIT C ON B.USER_ID = C.USER_ID
        LEFT JOIN T_POSITION D ON C.POSITION_ID = D.POSITION_ID
        LEFT JOIN T_ORGANIZATION E ON D.ORG_ID = E.ORG_ID
        WHERE SO.SALEORDER_ID IN
        <foreach collection="orderIds" index="index" item="saleorderId" open="(" separator="," close=")">
            #{saleorderId, jdbcType=INTEGER}
        </foreach>
        AND E.ORG_NAME LIKE 'B2B事业部%'
        GROUP BY SO.SALEORDER_ID
    </select>

    <select id="getByAfterSaleNo" resultType="com.vedeng.erp.saleorder.dto.SaleorderInfoDto">
        select a.*
        from T_SALEORDER a
        WHERE a.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
    </select>


    <select id="getSaleorderContractStatus" resultType="java.lang.Boolean">
        SELECT CASE
        WHEN TS.HAVE_ACCOUNT_PERIOD = 1
        AND (TSD.CONTRACT_VERIFY_STATUS != 1 or TSD.CONTRACT_VERIFY_STATUS is null)
        AND TSD.LEFT_AMOUNT_PERIOD != 0
        AND TTPW.TRADER_ID IS NULL
        THEN 1
        ELSE 0
        END
        FROM T_SALEORDER TS
        JOIN T_SALEORDER_DATA TSD ON TSD.SALEORDER_ID = TS.SALEORDER_ID
        LEFT JOIN T_TRADER_PERIOD_WHITE TTPW ON TTPW.TRADER_ID = TS.TRADER_ID and TTPW.IS_DEL = 0
        WHERE TS.SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
    </select>



    <select id="getSaleOrderGoodsImage" resultType="com.vedeng.erp.saleorder.dto.SaleOrderGoodsImageDto">
        SELECT SKU.SKU_NO,
               CONCAT(
                       CASE
                           WHEN ATTACHMENT.DOMAIN LIKE '%ivedeng%' THEN 'http://'
                           ELSE 'https://'
                           END,
                       ATTACHMENT.DOMAIN,
                       ATTACHMENT.URI
               ) as IMG_URL
        FROM V_CORE_SKU SKU
                 LEFT JOIN V_CORE_OPERATE_INFO INFO ON SKU.SKU_ID = INFO.SKU_ID
                 LEFT JOIN T_GOODS_ATTACHMENT ATTACHMENT ON ATTACHMENT.STATUS = 1  AND ATTACHMENT.ATTACHMENT_TYPE = 1001  AND ATTACHMENT.GOODS_ID = INFO.SKU_ID
        WHERE
            SKU.SKU_NO IN
        <foreach collection="skuNoList" index="index" item="skuNo" open="(" separator="," close=")">
            #{skuNo, jdbcType=VARCHAR}
        </foreach>
          AND INFO.OPERATE_INFO_TYPE = 2
        GROUP BY SKU.SKU_NO
        UNION ALL
        SELECT SKU.SKU_NO,
               CONCAT(
                       CASE
                           WHEN ATTACHMENT.DOMAIN LIKE '%ivedeng%' THEN 'http://'
                           ELSE 'https://'
                           END,
                       ATTACHMENT.DOMAIN,
                       ATTACHMENT.URI
               ) as IMG_URL
        FROM V_CORE_SKU SKU
                 LEFT JOIN V_CORE_OPERATE_INFO INFO ON SKU.SKU_ID = INFO.SKU_ID
                 LEFT JOIN T_GOODS_ATTACHMENT ATTACHMENT ON ATTACHMENT.STATUS = 1  AND ATTACHMENT.ATTACHMENT_TYPE = 1002 AND ATTACHMENT.GOODS_ID = INFO.SPU_ID
        WHERE
            SKU.SKU_NO in
        <foreach collection="skuNoList" index="index" item="skuNo" open="(" separator="," close=")">
            #{skuNo, jdbcType=VARCHAR}
        </foreach>
          AND INFO.OPERATE_INFO_TYPE = 1
        GROUP BY SKU.SKU_NO
    </select>

</mapper>
