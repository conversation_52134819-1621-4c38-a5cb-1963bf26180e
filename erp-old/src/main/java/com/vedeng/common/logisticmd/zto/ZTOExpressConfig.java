package com.vedeng.common.logisticmd.zto;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.system.model.SysOptionDefinition;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class ZTOExpressConfig {

    private final static String APOLLO_PROPS_KEY = "zto_epress_config";

    public final static int NORMAL_PRINTER_MAC_ADDR = 700;
    public final static int NORMAL_PRINTER_NAME = 701;

    public final static int PRO_PRINTER_MAC_ADDR = 702;
    public final static int PRO_PRINTER_NAME = 703;


    private final static ZTOExpressConfig INSTANCE = new ZTOExpressConfig();

    public static ZTOExpressConfig getInstance() {
        return INSTANCE;
    }

    private List<SysOptionDefinition> sysOptionList = Collections.emptyList();

    private ZTOExpressConfig() {
        loadConfigFromApolloConfigServer();
    }

    public List<SysOptionDefinition> getSysOptionList() {
        return sysOptionList;
    }

    private void loadConfigFromApolloConfigServer() {
        String configValuesInJson = ConfigService.getAppConfig().getProperty(APOLLO_PROPS_KEY, null);
        if (configValuesInJson == null) {
            throw new IllegalStateException("加载中通快递配置信失败");
        }

        List<SysOptionDefinition> parsedValueList;
        try {
            parsedValueList = JSON.parseArray(configValuesInJson, SysOptionDefinition.class);
        } catch (Exception e) {
            throw new IllegalStateException("解析json配置信息时发生错误");
        }

        if (parsedValueList == null || parsedValueList.isEmpty()) {
            throw new IllegalStateException("解析Json后中通快递配置信息为空");
        }

        sysOptionList = Collections.unmodifiableList(parsedValueList);
    }
}
