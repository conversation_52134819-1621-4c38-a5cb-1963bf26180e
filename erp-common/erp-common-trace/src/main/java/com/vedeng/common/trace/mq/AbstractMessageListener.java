package com.vedeng.common.trace.mq;

import com.rabbitmq.client.Channel;
import com.vedeng.core.trace.constant.MdcConstant;
import com.vedeng.core.trace.util.MdcHelper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;

/**
 * 消费监听器
 */
@Slf4j
public abstract class AbstractMessageListener implements ChannelAwareMessageListener {

    // 实际处理的业务
    protected abstract void doBusiness(Message message, Channel channel) throws Exception;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            MdcHelper.initTrace(null);
            log.info("Listener实现类：{}监听器自动生成traceId", this.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("监听AbstractMessageListener中MdcHelper.initTrace报错，但不影响业务流程", e);
        }
        try {
            log.info("执行业务doBusiness开始");
            // 处理业务
            this.doBusiness(message,channel);
            log.info("执行业务doBusiness结束");
        } catch (Exception e){
            log.error("onMessage exception",e);
            throw e;
        } finally {
            try {
                MdcHelper.clear();
            } catch (Exception e) {
                log.error("监听执行AbstractMessageListener中LogHelper.clear报错，但不影响业务流程", e);
            }
        }
    }
}
