package com.newtask.celery;

import com.newtask.celery.model.JobStatusEnum;
import com.newtask.celery.model.TaskJob;
import com.newtask.celery.service.TaskJobService;
import com.vedeng.common.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.newtask.celery.AsyncConstants.*;
import static com.newtask.celery.utils.RedisKeyUtils.queueLockMapKey;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/9 12:47
 */
@Slf4j(topic = "erp_async")
@Service
public class CeleryBroker extends LifecycleSupport {

    @Resource
    private TaskJobService taskJobService;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private CeleryWorker celeryWorker;

    public static final int QUEUE_REFRESH_TIME = 60 * 1000;

    private static final int QUEUE_HANDLE_SLEEP = 5 * 1000;

    private final HashMap<Integer, QueueListenerThread> queueThreadMap = new HashMap<>(16);

    private static final String ERROR_MSG_PRE = "erp async error:";

    private static final String NODE_ID = UUID.randomUUID().toString();

    private static final String K_QUEUE_LOCK_MAP = queueLockMapKey();

    private static final int QUEUE_LOCK_EXPIRE_TIME = 70 * 1000;

    private static final String NODE_LOCK_REG = "_";

    @Override
    protected void onStart() {
        broker();
    }

    @Override
    protected void onStop() {
    }

    private void broker() {

        new Thread(() -> {
            while (true) {
                try {
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    log.info("开始执行 refreshQueueThread 方法,uuid:{}",uuid);

                    long refreshTime = System.currentTimeMillis();
                    refreshQueueThread();

                    long sleepTime = refreshTime - System.currentTimeMillis() + QUEUE_REFRESH_TIME;
                    if (sleepTime > 0) {
                        mySleep(sleepTime);
                    }
                    log.info("结束执行 refreshQueueThread 方法,uuid:{}",uuid);
                } catch (Throwable e) {
                    log.error(ERROR_MSG_PRE  , e);
                    mySleep(ERROR_SLEEP_TIME);
                }
            }
        }, "erpAsyncBroker").start();
    }

    private void refreshQueueThread() {
        markTmpDelete();

        Set<Integer> queueList = taskJobService.getActiveQueueSet();
        for (Integer queue : queueList) {
            // real
            realRefreshQueue(queue);
        }

        removeEndThread();
    }


    /**
     * @param queue 当前队列
     */
    private void realRefreshQueue(Integer queue) {
        String val = redisUtils.hget(K_QUEUE_LOCK_MAP, queue);
        if (val != null) {
            String[] split = val.split(NODE_LOCK_REG);
            if (split.length != 2) {
                throw new RuntimeException("failed to parse queue lock key ...");
            }

            String redisNodeId = split[0];
            // 判断key是否超时间
            if ((System.currentTimeMillis() - Long.parseLong(split[1])) > QUEUE_LOCK_EXPIRE_TIME) {
                // 锁超时
                log.warn("当前活跃队列锁超时，释放锁 queue :{}", queue);
                redisUtils.hdel(K_QUEUE_LOCK_MAP, queue + "");
                return;
            }
            if (redisNodeId.equals(NODE_ID)) {
                // 续命标记
                redisUtils.hset(K_QUEUE_LOCK_MAP, queue, NODE_ID + NODE_LOCK_REG + System.currentTimeMillis());
            }

        } else {
            if (!redisUtils.hsetnx(K_QUEUE_LOCK_MAP, queue + "", NODE_ID + NODE_LOCK_REG + System.currentTimeMillis())) {
                return;
            }
        }

        QueueListenerThread thread = queueThreadMap.get(queue);
        if (thread != null) {
            thread.setQueue(queue);
        } else {
            thread = new QueueListenerThread("async-" + queue, queue);
            queueThreadMap.put(queue, thread);
            thread.start();
        }

    }

    /**
     *  实现二 哈希分发队列， 多台node处理不同队列，不需要分布式锁
     */
    private void refreshQueueThread2() {
        markTmpDelete();

        Set<Integer> queueList = taskJobService.getActiveQueueSet();
        for (Integer queue : queueList) {
            QueueListenerThread thread = queueThreadMap.get(queue);
            if (thread != null) {
                thread.setQueue(queue);
            } else {
                thread = new QueueListenerThread("async-" + queue, queue);
                queueThreadMap.put(queue, thread);
                thread.start();
            }
        }

        removeEndThread();
    }

    private void markTmpDelete() {
        for (Map.Entry<Integer, QueueListenerThread> entry : queueThreadMap.entrySet()) {
            entry.getValue().tmpDelete = true;
        }
    }

    private void removeEndThread() {
        Iterator<Map.Entry<Integer, QueueListenerThread>> it = queueThreadMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<Integer, QueueListenerThread> entry = it.next();
            QueueListenerThread thread = entry.getValue();

            if (!thread.isAlive() || thread.tmpDelete) {
                it.remove();
                if (thread.tmpDelete) {
                    thread.isRun = false;
                }

                // 解锁
                redisUtils.hdel(K_QUEUE_LOCK_MAP, entry.getKey() + "");
            }
        }
    }


    private class QueueListenerThread extends Thread {
        /**
         * 标记删除
         */
        private boolean tmpDelete = false;

        /**
         * 必须在单线程中操作，否则需要用 volatile修饰
         */
        private boolean isRun = true;

        private Integer queueKey;

        private long lastId = 0L;

        public QueueListenerThread(String name, Integer queueKey) {
            super(name);
            this.queueKey = queueKey;
        }

        public void setQueue(Integer queueKey) {
            this.queueKey = queueKey;
            this.tmpDelete = false;
        }

        @Override
        public void run() {
            log.info("queue({}) thread start ...", queueKey);
            while (isRun) {
                try {
                    List<TaskJob> bufferList = taskJobService.listTaskJobByQueue(queueKey, lastId);

                    if (bufferList.size() == 0) {
                        // 当前队列已做完，进行移除
                        taskJobService.removeFinishQueue(queueKey);
                        mySleep(QUEUE_HANDLE_SLEEP);
                        lastId = 0L;
                    } else {
                        List<Long> keyList = bufferList.stream().map(TaskJob::getJobId).collect(Collectors.toList());

                        for (TaskJob job : bufferList) {
                            Long jobId = job.getJobId();
                            Integer curStatus = getStatus(jobId);

                            if (JobStatusEnum.RUNNING.getKey().equals(curStatus)) {
                                log.info("this job({}) is running , parse ...", jobId);
                                continue;
                            }
                            putStatus(jobId, JobStatusEnum.RUNNING.getKey());

                            JobStatusEnum jobStatus = notifyTaskJob(job);

                            if (!JobStatusEnum.RUNNING.getKey().equals(jobStatus.getKey())) {
                                // 标记状态
                                putStatus(jobId, jobStatus.getKey());

                                job.setStatus(jobStatus.getKey());
                                taskJobService.updateTaskJob(job);
                            }
                        }
                        // 标记最大值
                        lastId = Collections.max(keyList);
                    }
                } catch (Throwable e) {
                    log.error(ERROR_MSG_PRE + e.getMessage(), e);
                    mySleep(ERROR_SLEEP_TIME);
                }
            }

            log.info("queue({}) thread end ...", queueKey);
        }


        private JobStatusEnum notifyTaskJob(TaskJob taskJob) {
            if (Objects.isNull(taskJob)) {
                return JobStatusEnum.ERROR;
            }

            long logTime = System.currentTimeMillis();
            // 失效校验
            if (taskJob.getValidMillis() > 0) {
                if ((logTime - taskJob.getAddTime()) > taskJob.getValidMillis()) {
                    taskJob.setErrorMsg("event execution has exceeded the validity period ...");
                    return JobStatusEnum.INVALID;
                }
            }

            boolean executable = true;
            // 幂等校验
            if (taskJob.isCheckIdempotent()) {

                executable = !taskJobService.isRunSuccess(taskJob.getUuid());
            }

            JobStatusEnum result = null;
            if (executable) {
                if (ASYNC_WORKER) {
                    celeryWorker.add(taskJob);
                } else {
                    celeryWorker.doTaskJob(taskJob);
                }

                result = JobStatusEnum.RUNNING;
            } else {
                taskJob.setErrorMsg("idempotent check failed, please check ...");
                result = JobStatusEnum.INVALID;
            }

            return result;
        }


    }


}
