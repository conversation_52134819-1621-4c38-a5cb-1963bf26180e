package com.vedeng.common.service.impl;

import java.io.IOException;

import com.common.constants.Contant;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 
 * <b>Description:</b><br> 天眼查接口调用
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.common.util
 * <br><b>ClassName:</b> HttpSendUtil
 * <br><b>Date:</b> 2018年1月11日 上午8:51:03
 */
@Component
public class HttpSendUtil {
	public static Logger logger = LoggerFactory.getLogger(HttpSendUtil.class);

	private static String DETAILURL = "https://open.api.tianyancha.com/services/v4/open/baseinfoV2.json?name=";
	private static String CUSTOMERLISTURL = "https://open.api.tianyancha.com/services/v4/open/searchV2.json?word=";

	@Value("${tyc_api_url_search}")
	private String tycApiUrlSearch;

	@Value("${tyc_api_url_baseinfo}")
	private String getTycApiUrlBaseinfo;

	@Value("${tyc_api_token}")
	private String tycApiToken;

	/**
	 * 
	 * <b>Description:</b><br> 调用接口
	 * @param type
	 * @param customerName
	 * @return
	 * @Note
	 * <b>Author:</b> scott
	 * <br><b>Date:</b> 2018年1月11日 上午9:03:12
	 */
	public String queryDetails(Integer type,String customerName)
    {
    	//logger.info("天眼查接口调用"+type+customerName);
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpGet httpGet = null;
		if(type==1){
			httpGet = new HttpGet(tycApiUrlSearch+customerName);
		}else{
			httpGet = new HttpGet(getTycApiUrlBaseinfo+customerName);
		}
	    httpGet.setHeader("Authorization", tycApiToken);
	    CloseableHttpResponse response1 = null; 
	    String result= "";  
	    try {
	    	response1 = httpclient.execute(httpGet);
	        HttpEntity entity1 = response1.getEntity();
	        result = EntityUtils.toString(entity1);
	        //slogger.info("天眼查查询，请求体：{}结果：{}",customerName,result);
	    } catch (ClientProtocolException e) {
			logger.error(Contant.ERROR_MSG, e);
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
		} finally {
	        try {
				response1.close();
			} catch (IOException e) {
				logger.error(Contant.ERROR_MSG, e);
			} catch (Exception ex){
	        	logger.error(Contant.ERROR_MSG, ex);
			}
	    }
	    return result;  
    }  
}
