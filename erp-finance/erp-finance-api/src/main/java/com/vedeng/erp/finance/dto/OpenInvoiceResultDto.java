package com.vedeng.erp.finance.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发票结果返回dto
 * @date 2023/9/26 19:39
 */
@Data
public class OpenInvoiceResultDto {

    public static final String SUCCESS_MSG = "成功";
    public static final String ERROR_MSG = "未知异常";
    /**
     * 是否开票成功
     */
    private boolean isSuccess;

    /**
     * 返回信息
     */
    private String msg;

    public OpenInvoiceResultDto success() {
        OpenInvoiceResultDto openInvoiceResultDto = new OpenInvoiceResultDto();
        openInvoiceResultDto.setSuccess(true);
        openInvoiceResultDto.setMsg(SUCCESS_MSG);
        return openInvoiceResultDto;
    }

    public OpenInvoiceResultDto success(String msg) {
        OpenInvoiceResultDto openInvoiceResultDto = new OpenInvoiceResultDto();
        openInvoiceResultDto.setSuccess(true);
        openInvoiceResultDto.setMsg(msg);
        return openInvoiceResultDto;
    }

    public OpenInvoiceResultDto fail() {
        OpenInvoiceResultDto openInvoiceResultDto = new OpenInvoiceResultDto();
        openInvoiceResultDto.setSuccess(false);
        openInvoiceResultDto.setMsg(ERROR_MSG);
        return openInvoiceResultDto;
    }

    public OpenInvoiceResultDto fail(String msg) {
        OpenInvoiceResultDto openInvoiceResultDto = new OpenInvoiceResultDto();
        openInvoiceResultDto.setSuccess(false);
        openInvoiceResultDto.setMsg(msg);
        return openInvoiceResultDto;
    }
}
