package com.vedeng.goods.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @date created in 2020/7/11 11:32
 */
public class CoreSpu {


    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer categoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer brandId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_NO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String spuNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String spuName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SHOW_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String showName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_LEVEL
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuLevel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer firstEngageId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.REGISTRATION_ICON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String registrationIcon;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.WIKI_HREF
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String wikiHref;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.OPERATE_INFO_FLAG
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer operateInfoFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CHECK_STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer checkStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.OPERATE_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer operateInfoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.HOSPITAL_TAGS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String hospitalTags;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CHECK_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date checkTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CHECKER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer checker;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.DELETE_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String deleteReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.LAST_CHECK_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String lastCheckReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.ASSIGNMENT_MANAGER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer assignmentManagerId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.ASSIGNMENT_ASSISTANT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer assignmentAssistantId;


    /**
     * 禁用原因
     */
    private String disabledReason;

    public String getDisabledReason() {
        return disabledReason;
    }

    public void setDisabledReason(String disabledReason) {
        this.disabledReason = disabledReason;
    }

    public Integer getSpuId() {
        return spuId;
    }

    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getSpuNo() {
        return spuNo;
    }

    public void setSpuNo(String spuNo) {
        this.spuNo = spuNo;
    }

    public String getSpuName() {
        return spuName;
    }

    public void setSpuName(String spuName) {
        this.spuName = spuName;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public Integer getSpuLevel() {
        return spuLevel;
    }

    public void setSpuLevel(Integer spuLevel) {
        this.spuLevel = spuLevel;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSpuType() {
        return spuType;
    }

    public void setSpuType(Integer spuType) {
        this.spuType = spuType;
    }

    public Integer getFirstEngageId() {
        return firstEngageId;
    }

    public void setFirstEngageId(Integer firstEngageId) {
        this.firstEngageId = firstEngageId;
    }

    public String getRegistrationIcon() {
        return registrationIcon;
    }

    public void setRegistrationIcon(String registrationIcon) {
        this.registrationIcon = registrationIcon;
    }

    public String getWikiHref() {
        return wikiHref;
    }

    public void setWikiHref(String wikiHref) {
        this.wikiHref = wikiHref;
    }

    public Integer getOperateInfoFlag() {
        return operateInfoFlag;
    }

    public void setOperateInfoFlag(Integer operateInfoFlag) {
        this.operateInfoFlag = operateInfoFlag;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Integer getOperateInfoId() {
        return operateInfoId;
    }

    public void setOperateInfoId(Integer operateInfoId) {
        this.operateInfoId = operateInfoId;
    }

    public String getHospitalTags() {
        return hospitalTags;
    }

    public void setHospitalTags(String hospitalTags) {
        this.hospitalTags = hospitalTags;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public Integer getChecker() {
        return checker;
    }

    public void setChecker(Integer checker) {
        this.checker = checker;
    }

    public String getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    public String getLastCheckReason() {
        return lastCheckReason;
    }

    public void setLastCheckReason(String lastCheckReason) {
        this.lastCheckReason = lastCheckReason;
    }

    public Integer getAssignmentManagerId() {
        return assignmentManagerId;
    }

    public void setAssignmentManagerId(Integer assignmentManagerId) {
        this.assignmentManagerId = assignmentManagerId;
    }

    public Integer getAssignmentAssistantId() {
        return assignmentAssistantId;
    }

    public void setAssignmentAssistantId(Integer assignmentAssistantId) {
        this.assignmentAssistantId = assignmentAssistantId;
    }

    @Override
    public String toString() {
        return "CoreSpu{" +
                "spuId=" + spuId +
                ", spuType=" + spuType +
                '}';
    }
}
