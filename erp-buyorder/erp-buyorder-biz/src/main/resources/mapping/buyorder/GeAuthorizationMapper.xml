<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.GeAuthorizationMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.GeAuthorization">
    <id column="AUTHORIZATION_ID" jdbcType="INTEGER" property="authorizationId" />
    <result column="AUTHORIZATION_NO" jdbcType="VARCHAR" property="authorizationNo" />
    <result column="GE_BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="geBussinessChanceId" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    AUTHORIZATION_ID, AUTHORIZATION_NO, GE_BUSSINESS_CHANCE_ID, `TYPE`, content, IS_DELETE, 
    `STATUS`, CREATOR, CREATOR_NAME, ADD_TIME, UPDATER, UPDATER_NAME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_GE_AUTHORIZATION
    where AUTHORIZATION_ID = #{authorizationId,jdbcType=INTEGER}
  </select>
  <select id="countGeBussinessChanceHaveNoCloseGeAuthorization" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorization" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include>
    from T_GE_AUTHORIZATION
    where GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId}
    and  STATUS != 4

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_GE_AUTHORIZATION
    where AUTHORIZATION_ID = #{authorizationId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AUTHORIZATION_ID" keyProperty="authorizationId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorization" useGeneratedKeys="true">
    insert into T_GE_AUTHORIZATION (AUTHORIZATION_NO, GE_BUSSINESS_CHANCE_ID, 
      `TYPE`, content, IS_DELETE, 
      `STATUS`, CREATOR, CREATOR_NAME, 
      ADD_TIME, UPDATER, UPDATER_NAME, 
      UPDATE_TIME)
    values (#{authorizationNo,jdbcType=VARCHAR}, #{geBussinessChanceId,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="AUTHORIZATION_ID" keyProperty="authorizationId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorization" useGeneratedKeys="true">
    insert into T_GE_AUTHORIZATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authorizationNo != null">
        AUTHORIZATION_NO,
      </if>
      <if test="geBussinessChanceId != null">
        GE_BUSSINESS_CHANCE_ID,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="authorizationNo != null">
        #{authorizationNo,jdbcType=VARCHAR},
      </if>
      <if test="geBussinessChanceId != null">
        #{geBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorization">
    update T_GE_AUTHORIZATION
    <set>
      <if test="authorizationNo != null">
        AUTHORIZATION_NO = #{authorizationNo,jdbcType=VARCHAR},
      </if>
      <if test="geBussinessChanceId != null">
        GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where AUTHORIZATION_ID = #{authorizationId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.GeAuthorization">
    update T_GE_AUTHORIZATION
    set AUTHORIZATION_NO = #{authorizationNo,jdbcType=VARCHAR},
      GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER},
      `TYPE` = #{type,jdbcType=INTEGER},
      content = #{content,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      `STATUS` = #{status,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where AUTHORIZATION_ID = #{authorizationId,jdbcType=INTEGER}
  </update>
</mapper>