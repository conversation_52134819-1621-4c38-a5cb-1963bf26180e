<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.QuoteorderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.QuoteorderEntity">
    <!--@mbg.generated-->
    <!--@Table T_QUOTEORDER-->
    <id column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId" />
    <result column="QUOTEORDER_NO" jdbcType="VARCHAR" property="quoteorderNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="CUSTOMER_TYPE" jdbcType="INTEGER" property="customerType" />
    <result column="CUSTOMER_NATURE" jdbcType="INTEGER" property="customerNature" />
    <result column="IS_NEW_CUSTOMER" jdbcType="BOOLEAN" property="isNewCustomer" />
    <result column="CUSTOMER_LEVEL" jdbcType="VARCHAR" property="customerLevel" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="IS_POLICYMAKER" jdbcType="TINYINT" property="isPolicymaker" />
    <result column="PURCHASING_TYPE" jdbcType="INTEGER" property="purchasingType" />
    <result column="PAYMENT_TERM" jdbcType="INTEGER" property="paymentTerm" />
    <result column="PURCHASING_TIME" jdbcType="INTEGER" property="purchasingTime" />
    <result column="PROJECT_PROGRESS" jdbcType="VARCHAR" property="projectProgress" />
    <result column="FOLLOW_ORDER_STATUS" jdbcType="TINYINT" property="followOrderStatus" />
    <result column="FOLLOW_ORDER_STATUS_COMMENTS" jdbcType="VARCHAR" property="followOrderStatusComments" />
    <result column="FOLLOW_ORDER_TIME" jdbcType="BIGINT" property="followOrderTime" />
    <result column="SALES_AREA_ID" jdbcType="INTEGER" property="salesAreaId" />
    <result column="SALES_AREA" jdbcType="VARCHAR" property="salesArea" />
    <result column="TERMINAL_TRADER_ID" jdbcType="INTEGER" property="terminalTraderId" />
    <result column="TERMINAL_TRADER_NAME" jdbcType="VARCHAR" property="terminalTraderName" />
    <result column="TERMINAL_TRADER_TYPE" jdbcType="INTEGER" property="terminalTraderType" />
    <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
    <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount" />
    <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
    <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay" />
    <result column="LOGISTICS_COLLECTION" jdbcType="BOOLEAN" property="logisticsCollection" />
    <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount" />
    <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth" />
    <result column="VALID_STATUS" jdbcType="BOOLEAN" property="validStatus" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
    <result column="PERIOD" jdbcType="INTEGER" property="period" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="FREIGHT_DESCRIPTION" jdbcType="INTEGER" property="freightDescription" />
    <result column="ADDITIONAL_CLAUSE" jdbcType="VARCHAR" property="additionalClause" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="IS_SEND" jdbcType="BOOLEAN" property="isSend" />
    <result column="SEND_TIME" jdbcType="BIGINT" property="sendTime" />
    <result column="IS_REPLAY" jdbcType="BOOLEAN" property="isReplay" />
    <result column="REPLAY_TIME" jdbcType="BIGINT" property="replayTime" />
    <result column="REPLAY_USER_ID" jdbcType="INTEGER" property="replayUserId" />
    <result column="HAVE_COMMUNICATE" jdbcType="BOOLEAN" property="haveCommunicate" />
    <result column="CONSULT_STATUS" jdbcType="TINYINT" property="consultStatus" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CLOSE_REASON_ID" jdbcType="INTEGER" property="closeReasonId" />
    <result column="CLOSE_REASON_COMMENT" jdbcType="VARCHAR" property="closeReasonComment" />
    <result column="QUOTED_ALARM_MODE" jdbcType="TINYINT" property="quotedAlarmMode" />
    <result column="SALESMAN_ALARM_LEVEL" jdbcType="TINYINT" property="salesmanAlarmLevel" />
    <result column="PURCHASER_ALARM_LEVEL" jdbcType="TINYINT" property="purchaserAlarmLevel" />
    <result column="TERMINAL_TYPE" jdbcType="INTEGER" property="terminalType" />
    <result column="LINK_BD_STATUS" jdbcType="TINYINT" property="linkBdStatus" />
  </resultMap>
  
  <resultMap id="BaseResultAuthorizationMap" type="com.vedeng.erp.trader.domain.entity.AuthorizationApiApply" >
		<id column="AUTHORIZATION_APPLY_ID" property="authorizationApplyId" jdbcType="INTEGER" />
		<result column="AUTHORIZATION_APPLY_NUM" property="authorizationApplyNum" jdbcType="VARCHAR" />
		<result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER" />
		<result column="SKU_ID" property="skuId" jdbcType="INTEGER" />
		<result column="PURCHASE_OR_BIDDING" property="purchaseOrBidding" jdbcType="VARCHAR" />
		<result column="PRODUCT_COMPANY" property="productCompany" jdbcType="VARCHAR" />
		<result column="NATURE_OF_OPERATION" property="natureOfOperation" jdbcType="INTEGER" />
		<result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
		<result column="SKU_NAME" property="skuName" jdbcType="VARCHAR" />
		<result column="SKU_MODEL" property="skuModel" jdbcType="VARCHAR" />
		<result column="DISTRIBUTIONS_TYPE" property="distributionsType" jdbcType="INTEGER" />
		<result column="AUTHORIZED_COMPANY" property="authorizedCompany" jdbcType="VARCHAR" />
		<result column="PURCHASE_PROJECT_NAME" property="purchaseProjectName" jdbcType="VARCHAR" />
		<result column="PURCHASE_PROJECT_NUM" property="purchaseProjectNum" jdbcType="VARCHAR" />
		<result column="FILE_TYPE" property="fileType" jdbcType="INTEGER" />
		<result column="AFTERSALES_COMPANY" property="aftersalesCompany" jdbcType="VARCHAR" />
		<result column="BEGIN_TIME" property="beginTime" jdbcType="BIGINT" />
		<result column="END_TIME" property="endTime" jdbcType="BIGINT" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
		<result column="CREATOR" property="creator" jdbcType="INTEGER"/>
		<result column="UPDATOR" property="updator" jdbcType="INTEGER" />
		<result column="APPLY_PERSON" property="applyPerson" jdbcType="VARCHAR" />
		<result column="REVIEWER" property="reviewer" jdbcType="VARCHAR" />
		<result column="DESCRIBED" property="described" jdbcType="VARCHAR" />
		<result column="NUM" property="num" jdbcType="INTEGER" />
		<result column="APPLY_STATUS" property="applyStatus" jdbcType="INTEGER" />
		<result column="YEAR_AND_MONTH" property="yearAndMonth" jdbcType="VARCHAR" />
		<result column="STANDARD_TEMPLATE" property="standardTemplate" jdbcType="INTEGER" />
		<result column="COMMENT" property="comment" jdbcType="VARCHAR" />
		<result column="APPLY_YEAR" property="applyYear" jdbcType="VARCHAR" />
		<result column="APPLY_MONTH" property="applyMonth" jdbcType="VARCHAR" />
		<result column="APPLY_DAY" property="applyDay" jdbcType="VARCHAR" />
		<result column="STANDARD_TEMPLATE" property="standardTemplate" jdbcType="INTEGER" />
	</resultMap>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    QUOTEORDER_ID, BUSSINESS_CHANCE_ID, QUOTEORDER_NO, COMPANY_ID, `SOURCE`, ORG_ID, 
    USER_ID, TRADER_ID, TRADER_NAME, AREA, CUSTOMER_TYPE, CUSTOMER_NATURE, IS_NEW_CUSTOMER, 
    CUSTOMER_LEVEL, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE, TELEPHONE, TRADER_ADDRESS_ID, 
    ADDRESS, IS_POLICYMAKER, PURCHASING_TYPE, PAYMENT_TERM, PURCHASING_TIME, PROJECT_PROGRESS, 
    FOLLOW_ORDER_STATUS, FOLLOW_ORDER_STATUS_COMMENTS, FOLLOW_ORDER_TIME, SALES_AREA_ID, 
    SALES_AREA, TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, PAYMENT_TYPE, 
    PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, LOGISTICS_COLLECTION, RETAINAGE_AMOUNT, 
    RETAINAGE_AMOUNT_MONTH, VALID_STATUS, VALID_TIME, TOTAL_AMOUNT, PERIOD, INVOICE_TYPE, 
    FREIGHT_DESCRIPTION, ADDITIONAL_CLAUSE, COMMENTS, IS_SEND, SEND_TIME, IS_REPLAY, 
    REPLAY_TIME, REPLAY_USER_ID, HAVE_COMMUNICATE, CONSULT_STATUS, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER, CLOSE_REASON_ID, CLOSE_REASON_COMMENT, QUOTED_ALARM_MODE, SALESMAN_ALARM_LEVEL, 
    PURCHASER_ALARM_LEVEL, TERMINAL_TYPE, LINK_BD_STATUS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_QUOTEORDER
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_QUOTEORDER
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="QUOTEORDER_ID" keyProperty="quoteorderId" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER (BUSSINESS_CHANCE_ID, QUOTEORDER_NO, 
      COMPANY_ID, `SOURCE`, ORG_ID, 
      USER_ID, TRADER_ID, TRADER_NAME, 
      AREA, CUSTOMER_TYPE, CUSTOMER_NATURE, 
      IS_NEW_CUSTOMER, CUSTOMER_LEVEL, TRADER_CONTACT_ID, 
      TRADER_CONTACT_NAME, MOBILE, TELEPHONE, 
      TRADER_ADDRESS_ID, ADDRESS, IS_POLICYMAKER, 
      PURCHASING_TYPE, PAYMENT_TERM, PURCHASING_TIME, 
      PROJECT_PROGRESS, FOLLOW_ORDER_STATUS, FOLLOW_ORDER_STATUS_COMMENTS, 
      FOLLOW_ORDER_TIME, SALES_AREA_ID, SALES_AREA, 
      TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, 
      PAYMENT_TYPE, PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, 
      PERIOD_DAY, LOGISTICS_COLLECTION, RETAINAGE_AMOUNT, 
      RETAINAGE_AMOUNT_MONTH, VALID_STATUS, VALID_TIME, 
      TOTAL_AMOUNT, PERIOD, INVOICE_TYPE, 
      FREIGHT_DESCRIPTION, ADDITIONAL_CLAUSE, COMMENTS, 
      IS_SEND, SEND_TIME, IS_REPLAY, 
      REPLAY_TIME, REPLAY_USER_ID, HAVE_COMMUNICATE, 
      CONSULT_STATUS, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, CLOSE_REASON_ID, 
      CLOSE_REASON_COMMENT, QUOTED_ALARM_MODE, SALESMAN_ALARM_LEVEL, 
      PURCHASER_ALARM_LEVEL, TERMINAL_TYPE, LINK_BD_STATUS
      )
    values (#{bussinessChanceId,jdbcType=INTEGER}, #{quoteorderNo,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=INTEGER}, #{source,jdbcType=TINYINT}, #{orgId,jdbcType=INTEGER},
      #{userId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{customerType,jdbcType=INTEGER}, #{customerNature,jdbcType=INTEGER}, 
      #{isNewCustomer,jdbcType=BOOLEAN}, #{customerLevel,jdbcType=VARCHAR}, #{traderContactId,jdbcType=INTEGER}, 
      #{traderContactName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, 
      #{traderAddressId,jdbcType=INTEGER}, #{address,jdbcType=VARCHAR}, #{isPolicymaker,jdbcType=TINYINT},
      #{purchasingType,jdbcType=INTEGER}, #{paymentTerm,jdbcType=INTEGER}, #{purchasingTime,jdbcType=INTEGER}, 
      #{projectProgress,jdbcType=VARCHAR}, #{followOrderStatus,jdbcType=TINYINT}, #{followOrderStatusComments,jdbcType=VARCHAR},
      #{followOrderTime,jdbcType=BIGINT}, #{salesAreaId,jdbcType=INTEGER}, #{salesArea,jdbcType=VARCHAR}, 
      #{terminalTraderId,jdbcType=INTEGER}, #{terminalTraderName,jdbcType=VARCHAR}, #{terminalTraderType,jdbcType=INTEGER}, 
      #{paymentType,jdbcType=INTEGER}, #{prepaidAmount,jdbcType=DECIMAL}, #{accountPeriodAmount,jdbcType=DECIMAL}, 
      #{periodDay,jdbcType=INTEGER}, #{logisticsCollection,jdbcType=BOOLEAN}, #{retainageAmount,jdbcType=DECIMAL}, 
      #{retainageAmountMonth,jdbcType=INTEGER}, #{validStatus,jdbcType=BOOLEAN}, #{validTime,jdbcType=BIGINT}, 
      #{totalAmount,jdbcType=DECIMAL}, #{period,jdbcType=INTEGER}, #{invoiceType,jdbcType=INTEGER}, 
      #{freightDescription,jdbcType=INTEGER}, #{additionalClause,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, 
      #{isSend,jdbcType=BOOLEAN}, #{sendTime,jdbcType=BIGINT}, #{isReplay,jdbcType=BOOLEAN}, 
      #{replayTime,jdbcType=BIGINT}, #{replayUserId,jdbcType=INTEGER}, #{haveCommunicate,jdbcType=BOOLEAN}, 
      #{consultStatus,jdbcType=TINYINT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{closeReasonId,jdbcType=INTEGER}, 
      #{closeReasonComment,jdbcType=VARCHAR}, #{quotedAlarmMode,jdbcType=TINYINT}, #{salesmanAlarmLevel,jdbcType=TINYINT},
      #{purchaserAlarmLevel,jdbcType=TINYINT}, #{terminalType,jdbcType=INTEGER}, #{linkBdStatus,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="QUOTEORDER_ID" keyProperty="quoteorderId" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bussinessChanceId != null">
        BUSSINESS_CHANCE_ID,
      </if>
      <if test="quoteorderNo != null">
        QUOTEORDER_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="area != null">
        AREA,
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE,
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE,
      </if>
      <if test="isNewCustomer != null">
        IS_NEW_CUSTOMER,
      </if>
      <if test="customerLevel != null">
        CUSTOMER_LEVEL,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="isPolicymaker != null">
        IS_POLICYMAKER,
      </if>
      <if test="purchasingType != null">
        PURCHASING_TYPE,
      </if>
      <if test="paymentTerm != null">
        PAYMENT_TERM,
      </if>
      <if test="purchasingTime != null">
        PURCHASING_TIME,
      </if>
      <if test="projectProgress != null">
        PROJECT_PROGRESS,
      </if>
      <if test="followOrderStatus != null">
        FOLLOW_ORDER_STATUS,
      </if>
      <if test="followOrderStatusComments != null">
        FOLLOW_ORDER_STATUS_COMMENTS,
      </if>
      <if test="followOrderTime != null">
        FOLLOW_ORDER_TIME,
      </if>
      <if test="salesAreaId != null">
        SALES_AREA_ID,
      </if>
      <if test="salesArea != null">
        SALES_AREA,
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID,
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME,
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT,
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="periodDay != null">
        PERIOD_DAY,
      </if>
      <if test="logisticsCollection != null">
        LOGISTICS_COLLECTION,
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT,
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
      <if test="period != null">
        PERIOD,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION,
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="isSend != null">
        IS_SEND,
      </if>
      <if test="sendTime != null">
        SEND_TIME,
      </if>
      <if test="isReplay != null">
        IS_REPLAY,
      </if>
      <if test="replayTime != null">
        REPLAY_TIME,
      </if>
      <if test="replayUserId != null">
        REPLAY_USER_ID,
      </if>
      <if test="haveCommunicate != null">
        HAVE_COMMUNICATE,
      </if>
      <if test="consultStatus != null">
        CONSULT_STATUS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="closeReasonId != null">
        CLOSE_REASON_ID,
      </if>
      <if test="closeReasonComment != null">
        CLOSE_REASON_COMMENT,
      </if>
      <if test="quotedAlarmMode != null">
        QUOTED_ALARM_MODE,
      </if>
      <if test="salesmanAlarmLevel != null">
        SALESMAN_ALARM_LEVEL,
      </if>
      <if test="purchaserAlarmLevel != null">
        PURCHASER_ALARM_LEVEL,
      </if>
      <if test="terminalType != null">
        TERMINAL_TYPE,
      </if>
      <if test="linkBdStatus != null">
        LINK_BD_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bussinessChanceId != null">
        #{bussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNo != null">
        #{quoteorderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="isNewCustomer != null">
        #{isNewCustomer,jdbcType=BOOLEAN},
      </if>
      <if test="customerLevel != null">
        #{customerLevel,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="isPolicymaker != null">
        #{isPolicymaker,jdbcType=TINYINT},
      </if>
      <if test="purchasingType != null">
        #{purchasingType,jdbcType=INTEGER},
      </if>
      <if test="paymentTerm != null">
        #{paymentTerm,jdbcType=INTEGER},
      </if>
      <if test="purchasingTime != null">
        #{purchasingTime,jdbcType=INTEGER},
      </if>
      <if test="projectProgress != null">
        #{projectProgress,jdbcType=VARCHAR},
      </if>
      <if test="followOrderStatus != null">
        #{followOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="followOrderStatusComments != null">
        #{followOrderStatusComments,jdbcType=VARCHAR},
      </if>
      <if test="followOrderTime != null">
        #{followOrderTime,jdbcType=BIGINT},
      </if>
      <if test="salesAreaId != null">
        #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null">
        #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null">
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null">
        #{logisticsCollection,jdbcType=BOOLEAN},
      </if>
      <if test="retainageAmount != null">
        #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="period != null">
        #{period,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null">
        #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="additionalClause != null">
        #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isSend != null">
        #{isSend,jdbcType=BOOLEAN},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=BIGINT},
      </if>
      <if test="isReplay != null">
        #{isReplay,jdbcType=BOOLEAN},
      </if>
      <if test="replayTime != null">
        #{replayTime,jdbcType=BIGINT},
      </if>
      <if test="replayUserId != null">
        #{replayUserId,jdbcType=INTEGER},
      </if>
      <if test="haveCommunicate != null">
        #{haveCommunicate,jdbcType=BOOLEAN},
      </if>
      <if test="consultStatus != null">
        #{consultStatus,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="closeReasonId != null">
        #{closeReasonId,jdbcType=INTEGER},
      </if>
      <if test="closeReasonComment != null">
        #{closeReasonComment,jdbcType=VARCHAR},
      </if>
      <if test="quotedAlarmMode != null">
        #{quotedAlarmMode,jdbcType=TINYINT},
      </if>
      <if test="salesmanAlarmLevel != null">
        #{salesmanAlarmLevel,jdbcType=TINYINT},
      </if>
      <if test="purchaserAlarmLevel != null">
        #{purchaserAlarmLevel,jdbcType=TINYINT},
      </if>
      <if test="terminalType != null">
        #{terminalType,jdbcType=INTEGER},
      </if>
      <if test="linkBdStatus != null">
        #{linkBdStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER
    <set>
      <if test="bussinessChanceId != null">
        BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNo != null">
        QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="isNewCustomer != null">
        IS_NEW_CUSTOMER = #{isNewCustomer,jdbcType=BOOLEAN},
      </if>
      <if test="customerLevel != null">
        CUSTOMER_LEVEL = #{customerLevel,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="isPolicymaker != null">
        IS_POLICYMAKER = #{isPolicymaker,jdbcType=TINYINT},
      </if>
      <if test="purchasingType != null">
        PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
      </if>
      <if test="paymentTerm != null">
        PAYMENT_TERM = #{paymentTerm,jdbcType=INTEGER},
      </if>
      <if test="purchasingTime != null">
        PURCHASING_TIME = #{purchasingTime,jdbcType=INTEGER},
      </if>
      <if test="projectProgress != null">
        PROJECT_PROGRESS = #{projectProgress,jdbcType=VARCHAR},
      </if>
      <if test="followOrderStatus != null">
        FOLLOW_ORDER_STATUS = #{followOrderStatus,jdbcType=BOOLEAN},
      </if>
      <if test="followOrderStatusComments != null">
        FOLLOW_ORDER_STATUS_COMMENTS = #{followOrderStatusComments,jdbcType=VARCHAR},
      </if>
      <if test="followOrderTime != null">
        FOLLOW_ORDER_TIME = #{followOrderTime,jdbcType=BIGINT},
      </if>
      <if test="salesAreaId != null">
        SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null">
        SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null">
        LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BOOLEAN},
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="period != null">
        PERIOD = #{period,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isSend != null">
        IS_SEND = #{isSend,jdbcType=BOOLEAN},
      </if>
      <if test="sendTime != null">
        SEND_TIME = #{sendTime,jdbcType=BIGINT},
      </if>
      <if test="isReplay != null">
        IS_REPLAY = #{isReplay,jdbcType=BOOLEAN},
      </if>
      <if test="replayTime != null">
        REPLAY_TIME = #{replayTime,jdbcType=BIGINT},
      </if>
      <if test="replayUserId != null">
        REPLAY_USER_ID = #{replayUserId,jdbcType=INTEGER},
      </if>
      <if test="haveCommunicate != null">
        HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BOOLEAN},
      </if>
      <if test="consultStatus != null">
        CONSULT_STATUS = #{consultStatus,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="closeReasonId != null">
        CLOSE_REASON_ID = #{closeReasonId,jdbcType=INTEGER},
      </if>
      <if test="closeReasonComment != null">
        CLOSE_REASON_COMMENT = #{closeReasonComment,jdbcType=VARCHAR},
      </if>
      <if test="quotedAlarmMode != null">
        QUOTED_ALARM_MODE = #{quotedAlarmMode,jdbcType=TINYINT},
      </if>
      <if test="salesmanAlarmLevel != null">
        SALESMAN_ALARM_LEVEL = #{salesmanAlarmLevel,jdbcType=TINYINT},
      </if>
      <if test="purchaserAlarmLevel != null">
        PURCHASER_ALARM_LEVEL = #{purchaserAlarmLevel,jdbcType=TINYINT},
      </if>
      <if test="terminalType != null">
        TERMINAL_TYPE = #{terminalType,jdbcType=INTEGER},
      </if>
      <if test="linkBdStatus != null">
        LINK_BD_STATUS = #{linkBdStatus,jdbcType=TINYINT},
      </if>
    </set>
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKeyForBusinessChance" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderEntity">
    update T_QUOTEORDER
    <set>
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      AREA = #{area,jdbcType=VARCHAR},
      CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR}
    </set>
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER
    set BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER},
      QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      AREA = #{area,jdbcType=VARCHAR},
      CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      IS_NEW_CUSTOMER = #{isNewCustomer,jdbcType=BOOLEAN},
      CUSTOMER_LEVEL = #{customerLevel,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      ADDRESS = #{address,jdbcType=VARCHAR},
      IS_POLICYMAKER = #{isPolicymaker,jdbcType=TINYINT},
      PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
      PAYMENT_TERM = #{paymentTerm,jdbcType=INTEGER},
      PURCHASING_TIME = #{purchasingTime,jdbcType=INTEGER},
      PROJECT_PROGRESS = #{projectProgress,jdbcType=VARCHAR},
      FOLLOW_ORDER_STATUS = #{followOrderStatus,jdbcType=TINYINT},
      FOLLOW_ORDER_STATUS_COMMENTS = #{followOrderStatusComments,jdbcType=VARCHAR},
      FOLLOW_ORDER_TIME = #{followOrderTime,jdbcType=BIGINT},
      SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BOOLEAN},
      RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      PERIOD = #{period,jdbcType=INTEGER},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      IS_SEND = #{isSend,jdbcType=BOOLEAN},
      SEND_TIME = #{sendTime,jdbcType=BIGINT},
      IS_REPLAY = #{isReplay,jdbcType=BOOLEAN},
      REPLAY_TIME = #{replayTime,jdbcType=BIGINT},
      REPLAY_USER_ID = #{replayUserId,jdbcType=INTEGER},
      HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BOOLEAN},
      CONSULT_STATUS = #{consultStatus,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      CLOSE_REASON_ID = #{closeReasonId,jdbcType=INTEGER},
      CLOSE_REASON_COMMENT = #{closeReasonComment,jdbcType=VARCHAR},
      QUOTED_ALARM_MODE = #{quotedAlarmMode,jdbcType=TINYINT},
      SALESMAN_ALARM_LEVEL = #{salesmanAlarmLevel,jdbcType=TINYINT},
      PURCHASER_ALARM_LEVEL = #{purchaserAlarmLevel,jdbcType=TINYINT},
      TERMINAL_TYPE = #{terminalType,jdbcType=INTEGER},
      LINK_BD_STATUS = #{linkBdStatus,jdbcType=TINYINT}
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </update>

  <select id="selectQuoteorderIdByBusinessChanceId" resultType="java.lang.Integer">
    select QUOTEORDER_ID from T_QUOTEORDER where BUSSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER}
  </select>
  
  <select id="getCommunicateQuoteInfo" resultType="java.util.Map">
      select QUOTEORDER_ID AS RELATED_ID,
             QUOTEORDER_NO AS ORDER_NO
      from T_QUOTEORDER
      where QUOTEORDER_ID IN
      <foreach item="item" index="index" collection="list"
               open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
  </select>

  <select id="getQuoteorderEntityByQuotationNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
	<include refid="Base_Column_List" />
 	from T_QUOTEORDER where QUOTEORDER_NO = #{quotationNo,jdbcType=VARCHAR}
  </select>
  
  <sql id="Base_Column_List2" >
    AUTHORIZATION_APPLY_ID, AUTHORIZATION_APPLY_NUM, QUOTEORDER_ID, SKU_ID, PURCHASE_OR_BIDDING, PRODUCT_COMPANY,
    NATURE_OF_OPERATION, BRAND_NAME, SKU_NAME, SKU_MODEL, DISTRIBUTIONS_TYPE, AUTHORIZED_COMPANY, PURCHASE_PROJECT_NAME,
    PURCHASE_PROJECT_NUM, FILE_TYPE, AFTERSALES_COMPANY, BEGIN_TIME, END_TIME,
    ADD_TIME, MOD_TIME, CREATOR, UPDATOR, APPLY_PERSON, REVIEWER, DESCRIBED,NUM,APPLY_STATUS,YEAR_AND_MONTH,STANDARD_TEMPLATE,COMMENT,APPLY_YEAR,APPLY_MONTH,APPLY_DAY,STANDARD_TEMPLATE
  	</sql>
  
  <select id="getAuthorizationApplyByNum" resultMap="BaseResultAuthorizationMap">
		select <include refid="Base_Column_List2" /> from
		T_AUTHORIZATION_APPLY
		where AUTHORIZATION_APPLY_NUM=#{authorizationApplyNum,jdbcType=VARCHAR}
  </select>

</mapper>