package com.vedeng.kingdee.rabbitmq.producers;

import com.alibaba.fastjson.JSON;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶消息生产者
 * @date 2023/9/8 13:05
 */
@Component
@Slf4j
public class KingDeeMsgProducer implements KingDeeMsgService {

    @Autowired
    @Qualifier(value = "kingDeeRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送消息
     *
     * @param kingDeeEventMsg 消息对象
     */
    @Override
    public void senMsg(KingDeeEventMsgDto kingDeeEventMsg) {
        try {
            log.info("向金蝶mq发送消息[{}]", JSON.toJSONString(kingDeeEventMsg));
            rabbitTemplate.convertAndSend(KingDeeConstant.KING_DEE_EXCHANGE,
                    KingDeeConstant.KING_DEE_ROUTING_KEY, kingDeeEventMsg);
        } catch (Exception e) {
            log.error("金蝶:mq事件注册异常", e);
        }
    }
}
