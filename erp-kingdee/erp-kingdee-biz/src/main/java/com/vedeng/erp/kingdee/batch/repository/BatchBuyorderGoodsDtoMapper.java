package com.vedeng.erp.kingdee.batch.repository;
import java.util.Collection;

import com.vedeng.erp.kingdee.dto.AfterSaleGoodsIdQueryReqDto;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.kingdee.batch.dto.BatchBuyorderGoodsDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/11/24 15:26
 **/
public interface BatchBuyorderGoodsDtoMapper {

    BatchBuyorderGoodsDto selectByPrimaryKey(Integer buyorderGoodsId);

    /**
     * 只查询未删除商品
     * @param buyorderId
     * @return
     */
    List<BatchBuyorderGoodsDto> selectByBuyorderIdNotDelete(@Param("buyorderId")Integer buyorderId);

    /**
     * 根据sku查找最近已生效的采购订单的一个商品
     * @param sku
     * @return
     */
    BatchBuyorderGoodsDto selectBySkuValidLately(String sku);

    List<BatchBuyorderGoodsDto> findByBuyorderGoodsIdInAndIsDelete(@Param("buyorderGoodsIdCollection")List<Integer> buyorderGoodsIdCollection,@Param("isDelete")Integer isDelete);

    /**
     * 条件检索售后商品ID
     * @param afterSaleGoodsIdQueryReqDto
     * @return
     */
    Integer getAfterGoodsIdByCondition(AfterSaleGoodsIdQueryReqDto afterSaleGoodsIdQueryReqDto);

    List<BatchBuyorderGoodsDto> findHaveInstallationByBuyorderGoodsId(@Param("buyorderGoodsId") Integer buyorderGoodsId);

    List<BatchBuyorderGoodsDto> findBySkuInAndBuyorderId(@Param("skuCollection") Collection<String> skuCollection, @Param("buyorderNo") String buyorderNo);


}