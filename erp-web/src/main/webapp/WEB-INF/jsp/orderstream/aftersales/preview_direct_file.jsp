<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="同行单预览" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/sku_composite_view.css?rnd=${resourceVersionKey}">

<div class="detail-wrap" style="margin-top: 80px;margin-left: 30px">
    <div class=" overflow-hidden">
        <div class="table-title nobor">
            服务凭证预览&nbsp;&nbsp;&nbsp;&nbsp;
            </br>
        </div>
        <c:choose>
            <c:when test="${not empty attachmentList }">
                <div class="detail-simple-wrap J-detail-simple">
                    <div class="detail-simple-list">
                        <div class="simple-item">
                            <div class="simple-cnt">
                                <table class="table table-base">
                                    <tr>
                                        <td>
                                            <div class="info-pic">
                                                <c:forEach var="attachments" items="${attachmentList }">
                                                    <div class="info-pic-item J-show-big"
                                                         data-src="${oss_http}${attachments.domain }${attachments.uri }">
                                                        <img style="width:100%;height:100%"
                                                             src="${oss_http}${attachments.domain }${attachments.uri }"
                                                             alt="">
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </c:when>
            <c:otherwise>
                <div  class="table-title nobor">
                    <span style="text-align: center">暂无服务凭证信息！</span>
                </div>
            </c:otherwise>
        </c:choose>
    </div>
    <script type="text/javascript">
        $(function () {
            //查看大图
            GLOBAL.showLargePic('.J-show-big')
        });
    </script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp" %>