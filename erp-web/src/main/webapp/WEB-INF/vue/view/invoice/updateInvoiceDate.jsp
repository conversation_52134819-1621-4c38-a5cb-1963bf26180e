<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<style>
    .reqButton {
        display: block;
        text-align: center;
        padding-top: 270px;
    }
</style>
<div id="app" style="height: 100%">
    <el-row>
        <el-form :inline="true">
            <el-select style="display: none">
            </el-select>
            <el-col :span="12">
                <el-form-item label="发票号：">
                    {{invoice.invoiceNo}}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="发票金额：">
                    {{numFilter(invoice.amount)}}
                </el-form-item>
            </el-col>
                <el-form-item label="红蓝字：">
                    <c:choose>
                        <c:when test="${invoice.colorType eq 1 and invoice.isEnable eq 1}"><!--红字有效-->
                            红字有效
                        </c:when>
                        <c:when test="${invoice.colorType eq 2 and invoice.isEnable eq 0}"><!--蓝字作废-->
                            蓝字作废
                        </c:when>
                        <c:otherwise>
                            蓝字有效
                        </c:otherwise>
                    </c:choose>
                </el-form-item>
            <el-col :span="12">
                <el-form-item label="关联订单：">
                    {{invoice.buyorderNo}}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="开票方：">
                    {{invoice.traderName}}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="申请日期：">
                    <el-date-picker
<%--                            @focus="getTop($event)"--%>
                            v-model="addTime"
                            :disabled="addTime == null || addTime == ''"
                            type="date"
                            size="small"
                            value-format="yyyy-MM-dd"
                            :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="审核日期：">
                    <el-date-picker
<%--                            @focus="getTop($event)"--%>
                            v-model="validTime"
                            :disabled="validTime == null || validTime == ''"
                            align="right"
                            type="date"
                            size="small"
                            value-format="yyyy-MM-dd"
                            :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="认证日期：">
                    <el-date-picker
<%--                            @focus="getTop($event)"--%>
                            v-model="authTime"
                            :disabled="authTime == null || authTime == ''"
                            align="right"
                            type="date"
                            size="small"
                            value-format="yyyy-MM-dd"
                            :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
            </el-col>
            </el-form>
    </el-row>
    <%--按钮--%>
    <span class="reqButton">
        <el-button plain @click="reInit">关闭</el-button>
        <el-button type="primary" plain @click="submit">提交</el-button>
    </span>
</div>

<script type="text/javascript">
    const vm = new Vue({
        el: '#app',
        data() {
            return {
                invoice: {
                },
                addTime: '',
                validTime: '',
                authTime: '',
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    }
                }
            }
        },
        created() {
            this.invoice = ${invoice}
            this.addTime = this.invoice.addTimeStr
            this.authTime = this.invoice.authTimeStr
            this.validTime = this.invoice.validTimeStr

        },
        methods: {
            getTop(e){
                // 获取事件触发的元素距离可视窗口的顶部距离 + 元素自身的高度
                let top =  e.$el.getBoundingClientRect().top + 30 + 'px'
                // 之所以使用定时器是因为，元素渲染需要一点时间
                setTimeout(()=>{
                    // 获取正在显示的下拉框
                    let down = document.querySelector('div[x-placement]')
                    // 设置定位为绝对定位（基于body）
                    down.style.position = 'absolute'
                    // 设置与顶部的距离
                    down.style.top = top
                    // 获取所有隐藏的下拉框
                    let panel = document.querySelectorAll('.el-picker-panel')
                    // 循环给所有隐藏下拉框更改样式
                    for(var i =0;i<panel.length;i++){
                        panel[i].style.position = 'absolute'
                        panel[i].style.top = top
                    }
                })
            },
            /*提交*/
            submit() {
                //发送请求
                let invoice = {
                    addTimeLabel: this.addTime,
                    validTimeLabel: this.validTime,
                    authTimeLabel: this.authTime,
                    addTimeStr: this.invoice.addTimeStr,
                    authTimeStr: this.invoice.authTimeStr,
                    validTimeStr: this.invoice.validTimeStr,
                    validStatus: this.invoice.validStatus,
                    voucherNo: this.invoice.voucherNo,
                    invoiceId: this.invoice.invoiceId,
                };
                axios({
                    url: './doUpdateInvoiceDate.do',
                    method: 'post',
                    data: invoice,
                    headers: {
                        'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
                    }
                }).then(res => {
                    if(res.data.code === 0){
                        this.$message({
                            message: res.data.message,
                            type: 'success'
                        });
                    }else if(res.data.code === -1){
                        this.$message({
                            message: res.data.message,
                            type: 'error'
                        });
                    }
                    setTimeout(() => {
                        this.reInit();
                        parent.location.reload();
                        },1500)
                    })

            },
            /*取消*/
            reInit() {
                layer.closeAll();
                parent.layer.closeAll();
            },

            // 保留两位小数
            numFilter(value){
                let realVal = parseFloat(value).toFixed(2);
                return realVal;
            },
        }
    });
</script>
