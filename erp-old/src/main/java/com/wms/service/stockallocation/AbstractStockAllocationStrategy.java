package com.wms.service.stockallocation;

import com.alibaba.fastjson.JSON;
import com.vedeng.logistics.model.WarehouseStock;
import com.wms.constant.LogicalEnum;
import com.wms.dto.StockAllocationRequest;
import com.wms.dto.StockAllocationResult;
import com.wms.service.chain.step.LendOutOrderHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public abstract class AbstractStockAllocationStrategy implements StockAllocationStrategy{


    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractStockAllocationStrategy.class);

    //样品库id
    public static final Integer SAMPLE_WAREHOUSE = 1706;
    //库存分配逻辑
    @Override
    public List<StockAllocationResult> stockAllocation(StockAllocationRequest stockAllocationRequest) throws Exception{


        Map<Integer, WarehouseStock> skuLogicStockMap = getLogicalStockMapInfo(stockAllocationRequest);

        LOGGER.info("逻辑仓库存信息map集合-------->" + JSON.toJSONString(skuLogicStockMap));

        List<Integer> stockAllocationOrder = getStockAllocationOrder();

        List<StockAllocationResult> stockAllocationResultList = new ArrayList<>();

        //待处理的sku
        String skuNo = stockAllocationRequest.getSku();

        //待分配的数量
        int toAllocateNum = stockAllocationRequest.getNum();

        //当前分配的逻辑仓的index
        int currentIndex = 0;

        while (toAllocateNum > 0 && (currentIndex <= stockAllocationOrder.size() - 1)) {

            //如果是最后一个逻辑仓 就直接分配满
            if(currentIndex == (stockAllocationOrder.size() - 1)){
                addAllocateResult(stockAllocationResultList, skuNo, stockAllocationOrder.get(currentIndex),toAllocateNum);
                break;
            }

            //获取sku在逻辑仓中的库存
            int availableStockNum = skuLogicStockMap.get(stockAllocationOrder.get(currentIndex)).getAvailableStockNum();

            if(availableStockNum == 0){
                currentIndex++;
                continue;
            }

            //当前逻辑仓库满足条件
            if(availableStockNum >= toAllocateNum){
                addAllocateResult(stockAllocationResultList, skuNo, stockAllocationOrder.get(currentIndex),toAllocateNum);
                toAllocateNum = 0;
                break;
            }

            addAllocateResult(stockAllocationResultList, skuNo, stockAllocationOrder.get(currentIndex),availableStockNum);

            toAllocateNum -= availableStockNum;
            currentIndex++;

        }

        //如果还有待分配的数量,直接抛异常
        /*if(toAllocateNum > 0){
            throw new Exception("商品的库存不足，无法分配库存");
            //addAllocateResult(stockAllocationResultList, skuNo, LogicalEnum.HG.getLogicalWarehouseId(),toAllocateNum);
        }*/

        return stockAllocationResultList;
    }

    protected abstract List<Integer> getStockAllocationOrder();

    protected abstract Map<Integer, WarehouseStock> getLogicalStockMapInfo(StockAllocationRequest stockAllocationRequest);


    private void addAllocateResult(List<StockAllocationResult> stockAllocationResultList, String skuNo, int logicalWarehouseId,int allocateNum) {
        StockAllocationResult stockAllocationResult = new StockAllocationResult();
        stockAllocationResult.setSku(skuNo);
        stockAllocationResult.setLogicalWarehouseId(logicalWarehouseId);
        stockAllocationResult.setNum(allocateNum);
        stockAllocationResultList.add(stockAllocationResult);
    }

    @Override
    public List<StockAllocationResult> lentOutStockAllocation(StockAllocationRequest stockAllocationRequest) throws Exception {

        List<StockAllocationResult> stockAllocationResultList = new ArrayList<>();

        //待处理的sku
        String skuNo = stockAllocationRequest.getSku();

        //待分配的数量
        int toAllocateNum = stockAllocationRequest.getNum();

        addAllocateResult(stockAllocationResultList,skuNo,SAMPLE_WAREHOUSE,toAllocateNum);

        return stockAllocationResultList;
    }
}
