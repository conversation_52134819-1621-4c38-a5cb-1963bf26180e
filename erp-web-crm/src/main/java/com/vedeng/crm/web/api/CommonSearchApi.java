package com.vedeng.crm.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.system.domain.entity.CommonSearchEntity;
import com.vedeng.erp.system.dto.CommonSearchDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.CommonSearchService;
import com.vedeng.erp.system.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/17
 */
@ExceptionController
@RestController
@RequestMapping(value = {"/crm/common/profile","/crm/common/m"})
@Slf4j
public class CommonSearchApi {

    @Autowired
    private CommonSearchService commonSearchService;

    /**
     * 返回全部用户的信息
     * @return
     */
    @RequestMapping("/searchList")
    public R<?> searchList(@RequestBody CommonSearchDto commonSearchDto){
        CurrentUser user = CurrentUser.getCurrentUser();
        commonSearchDto.setUserId(user.getId());
        commonSearchDto.setSearchType("LIST");
        List<CommonSearchEntity> listList =   commonSearchService.selectListByUserIdAndSearchFrom(commonSearchDto);

        commonSearchDto.setSearchType("QUERY");
        List<CommonSearchEntity> queryList =   commonSearchService.selectListByUserIdAndSearchFrom(commonSearchDto);

        commonSearchDto.setSearchType("RESULT");
        List<CommonSearchEntity> resultList =   commonSearchService.selectListByUserIdAndSearchFrom(commonSearchDto);

        Map<String,Object> resultMap = new HashMap<String,Object>();
        resultMap.put("conditionList",listList);
        resultMap.put("queryMap", CollectionUtils.isNotEmpty(queryList)?queryList.get(0):null);
        resultMap.put("resultMap", CollectionUtils.isNotEmpty(resultList)?resultList.get(0):null);
        return R.success(resultMap);
    }

    @RequestMapping("/saveSearchConfig")
    public R<?> save(@RequestBody CommonSearchDto commonSearchDto){
        CurrentUser user = CurrentUser.getCurrentUser();
        commonSearchDto.setUserId(user.getId());
        int result = commonSearchService.saveOrUpdate(commonSearchDto);
        return R.success("保存成功",result);
    }

    @RequestMapping("/deleteSearchConfig")
    public R<?> delete(@RequestBody CommonSearchDto commonSearchDto){
        CurrentUser user = CurrentUser.getCurrentUser();
        CommonSearchEntity entity =  commonSearchService.selectByPrimaryKey(Long.valueOf(commonSearchDto.getId()));
        if(entity == null || !entity.getUserId().equals(user.getId())){
            return R.error("删除失败，请刷新后重试");
        }
        commonSearchDto.setUserId(user.getId());
        commonSearchService.deleteByPrimaryKey(commonSearchDto.getId());
        return R.success("删除成功");
    }


    @Autowired
    private RegionService regionService;

    /**
     * 获取组装好的省市区三级级联数据
     * @return list
     */
    @RequestMapping(value = "/getRegionAll")
    @NoNeedAccessAuthorization
    public R<?> getCascaderRegionOptions() {
        return R.success(regionService.getCascaderRegionOptions());
    }



}
