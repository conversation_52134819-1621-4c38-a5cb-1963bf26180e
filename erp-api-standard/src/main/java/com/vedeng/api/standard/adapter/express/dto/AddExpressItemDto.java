package com.vedeng.api.standard.adapter.express.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 *    //  buyOrderGoodsId | 本次发货数 | 单价 | SKU _  buyOrderGoodsId | 本次发货数 | 单价 | SKU2
 *     private String id_num_price;
 *
 *     //  buyOrderGoodsId | 本次发货数 | 已发货数量 | 商品总数 _ buyOrderGoodsId | 本次发货数 | 已发货数量 | 商品总数
 *     private String id_sendN_sendedN_sumN;
 */
@Data
public class AddExpressItemDto {
    
    private Integer buyOrderGoodsId;

    //商品总数
    private BigDecimal num;

    // 本次发货数
    private BigDecimal sendNum;
    
    private BigDecimal price;
    
    private String sku;
}
