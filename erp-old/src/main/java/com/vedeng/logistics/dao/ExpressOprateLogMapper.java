package com.vedeng.logistics.dao;


import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.ExpressOprateLog;

import java.util.List;

public interface ExpressOprateLogMapper {
    int deleteByPrimaryKey(Integer rExpressOperateLog);

    int insert(ExpressOprateLog record);

    int insertSelective(ExpressOprateLog record);

    ExpressOprateLog selectByPrimaryKey(Integer rExpressOperateLog);

    int updateByPrimaryKeySelective(ExpressOprateLog record);

    int updateByPrimaryKey(ExpressOprateLog record);
    /**
     * 
     * <b>Description:</b>批量保存
     * @param elList
     * @return int
     * @Note
     * <b>Author：</b> scott.zhu
     * <b>Date:</b> 2018年11月15日 下午1:36:42
     */
    int insertSelectiveBatch(List<ExpressOprateLog> elList);
    /**
     * 
     * <b>Description:</b>根据快递详情id查询列表
     * @param dList
     * @return List<ExpressOprateLog>
     * @Note
     * <b>Author：</b> scott.zhu
     * <b>Date:</b> 2018年11月15日 下午2:21:00
     */
    List<ExpressOprateLog> getExpressOprateLogList(List<ExpressDetail> dList);
    /**
     * 
     * <b>Description:</b>删除绑定关系
     * @param dList
     * @return int
     * @Note
     * <b>Author：</b> scott.zhu
     * <b>Date:</b> 2018年11月15日 下午2:31:02
     */
    int deleteByPrimaryKeyBatch(List<ExpressDetail> dList);
}