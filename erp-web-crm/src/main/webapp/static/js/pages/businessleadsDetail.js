
void function () {
    new Vue({
        el: '#page-container',
        data: {
            pageLoading: true, // 页面加载状态
            businessLeadsId: '', // 线索id
            isEdit: false, // true:编辑状态  false:新建
            fixedTop: layout_hidden_value ? 0 : 50, //表头悬浮高度，根据有没有头部判断

            asideList: [
                { name: '跟进记录', icon: 'msg', id: 1 },
                { name: '任务记录', icon: 'record', id: 3 },
                { name: '操作记录', icon: 'time', id: 2 },
                { name: '协作人', icon: 'user', id: 4 },
                // { name: '任务列表', icon: 'record', id: 3 },
            ],
            asideIndex: 1,

            detail: {},
            STATUS: {
                "0": "待分配",
                "1": "待跟进",
                "2": "跟进中",
                "3": "已关闭",
                "4": "已商机",
            },
            leadsDetailHeaders: [
                { key: "leadsNo", label: "线索编号", width: "190px" },
                { key: "belonger", label: "归属销售", width: "200px", avatar: 'belongerPic' },
                { key: "inquireTime", label: "创建时间", width: "200px" },
                { key: "inquireType", label: "线索类型", width: "140px" },
                { key: "inquireSku", label: "产品信息", width: "410px" },
            ],
            LeadMergeList: [], // 合并线索

            // 分配线索
            isShowBelongerDialog: false, //是否展示分配人弹层
            editId: [],
            newBelonger: '',
            allUserRemoteInfo: {
                url: '/crm/user/profile/getAllUserInfo',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },

            // 关闭线索
            isShowCloseDialog: false,
            CloseReasonTypes: [
                { value: '1', label: '无法取得联系' },
                { value: '2', label: '招投标无授权' },
                { value: '3', label: '产品不在经营范围' },
                { value: '4', label: '产品不在经营区域' },
                { value: '5', label: '仅咨询技术问题' },
                { value: '6', label: '客户没有购买意愿' },
                { value: '7', label: '没有竞争力、价格没优势' },
                { value: '8', label: '其他' }
            ],
            closeReasonType: '', // 关闭类型
            closeReasonTypeName: '',
            closeRemark: '', // 关闭原因
            cardLineNum: '', //一行展示的数量
            defaultAiData: {}, //ai解析分类数据
            taskNum: 0, //任务数量
        },
        computed: {
            area () {
                let arr = [];
                this.detail.province && arr.push(this.detail.province);
                this.detail.city && arr.push(this.detail.city);
                this.detail.county && arr.push(this.detail.county);
                return arr.join(' / ');
            },
            // 状态是否 已关闭或已商机
            hidnStatus () {
                return this.detail.followStatus == 3 || this.detail.followStatus == 4
            },

            // 三级分类
            categoryShow () {
                let content = this.detail.content || '';
                return content.split('&&');
            },
            // 其他联系方式
            otherContactShow () {
                let otherContact = this.detail.otherContactInfo || '';
                return otherContact.split('##');
            },
            //标签
            tagsShow () {
                let arr = [];
                if (this.detail.tags && this.detail.tags.length) {
                    this.detail.tags.forEach(item => {
                        if (item.name) {
                            arr.push(item.name);
                        }
                    })
                }
                return arr.join(' / ');
            },
        },
        created () {
            this.businessLeadsId = document.getElementById('businessLeads-id').value || '';

            GLOBAL.showGlobalLoading();
            this.initData();
            this.getAiData();
        },
        mounted() {
            this.$nextTick(() => {
                let isdelete = GLOBAL.getQuery('isdelete') == 1;

                if(isdelete) {
                    this.showCloseLeadsDialog();
                    window.history.replaceState(null, null, '/crm/businessLeads/profile/detail?id=' + this.businessLeadsId);
                }
            })

            this.getTaskNum();
        },
        methods: {
            async initData () {
                // 详情
                let { data: resData } = await this.$axios.get(`/crm/presales/profile/detail?dataId=${this.businessLeadsId}&type=1`)
                if (resData.success) {
                    console.log('详情数据', resData.data);
                    this.detail = resData.data || {};
                } else {
                    this.$message.error(resData.message);
                }

                if (!layout_hidden_value) {
                    document.title = (this.detail.traderName || '') + '线索查看';
                }
                this.pageLoading = false;
                GLOBAL.hideGlobalLoading();

                // 合并线索
                let { data: resData1 } = await this.$axios.post(`/crm/businessLeads/profile/getLeadMerge?id=${this.businessLeadsId}`);
                if (resData1.success) {
                    console.log('合并线索:', resData1.data);
                    this.LeadMergeList = resData1.data || [];
                }
            },
            getAiData() {
                this.$axios.post('/crm/category/getByBusiness?businessId=' + this.businessLeadsId + '&businessType=0').then(({ data }) => {
                    if(data.success) {
                        if(data.data.matchedCategories && data.data.matchedCategories.length) {
                            // this.aiValue.keywords = data.data.keywords ? data.data.keywords.split(',') : [];
                            // this.aiValue.categoryIds = data.data.matchedCategories.split(',');

                            this.defaultAiData = {
                                keywords: data.data.keywords || [],
                                list: data.data.matchedCategories,
                                word: this.productCommentsSale
                            }
                        }
                    }
                })
            },
            getTaskNum() {
                this.$axios.post('/crm/task/profile/getCurrentUserTodoCount', {
                    bizList: [{
                        bizType: 2,
                        bizId: this.businessLeadsId
                    }],
                    listType: 1
                }).then(({data}) => {
                    if(data.success) {
                        let taskNum = data.data || 0;
                        this.taskNum = taskNum > 99 ? '99+' : taskNum;
                    }
                })
            },
            // 转商机
            // toBusinessChance () {
            //     if (GLOBAL.auth('C0108')) {
            //         GLOBAL.link({name:'转商机', url: `/crm/businessChance/profile/add?leadsid=${this.businessLeadsId}`, multi: true})
            //     } else {
            //         GLOBAL.showNoAuth();
            //     }
            // },
            // 编辑
            handlerEdit () {
                if (GLOBAL.auth('C0102')) {
                    window.location.href = `/crm/businessChance/profile/add?id=${this.businessLeadsId}&type=1`;
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            /* 分配 */
            handlerBelonger () {
                let ids = [this.businessLeadsId];
                if (!ids || !ids.length) {
                    this.$message.warn('请选择需要操作的内容');
                } else {
                    this.editId = ids;
                    this.showBelongerDialog();
                }
            },
            showBelongerDialog() {
                this.isShowBelongerDialog = true;
                this.newBelonger = '';

                this.$form.rules({
                    newBelonger: {
                        required: '请选择新归属人'
                    },
                }, 'belongerForm', this)
            },
            setBelonger() {
                if (this.$form.validForm('belongerForm')) {
                    GLOBAL.showGlobalLoading();
                    this.$axios.post('/crm/businessLeads/profile/assign', {
                        ids: this.editId,
                        userId: this.newBelonger
                    }).then(({ data }) => {
                        this.isShowBelongerDialog = false;
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.$message.success('分配成功');
                            setTimeout(()=> {
                                window.location.reload();
                            }, 2000);
                        } else {
                            this.$message.warn(data.message || '网络异常');
                        }
                    })
                }
            },
            /* 关闭线索 */
            showCloseLeadsDialog() {
                if (GLOBAL.auth('C0109')) {
                    this.closeReasonType = '';
                    this.closeReasonTypeName = '';
                    this.closeRemark = ''
                    this.isShowCloseDialog = true;
                    this.$form.rules({
                        closeReasonType: {
                            required: '请选择关闭原因'
                        },
                    }, 'CloseLeads', this);
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            handlerCloseReasonTypeSelect (data) {
                this.closeReasonTypeName = data.selected.label;
            },
            // 关闭线索
            closeTheLeads () {
                if (this.$form.validForm('CloseLeads')) {
                    GLOBAL.showGlobalLoading();
                    this.$axios.post('/crm/businessLeads/profile/closedLeads', {
                        id: this.businessLeadsId,
                        closeReasonType: this.closeReasonType,
                        closeReasonTypeName: this.closeReasonTypeName,
                        closeReason: this.closeRemark,
                    }).then(({ data }) => {
                        this.isShowCloseDialog = false;
                        GLOBAL.hideGlobalLoading();
                        if (data.success) {
                            this.$message.success('关闭成功');
                            setTimeout(()=> {
                                window.location.reload();
                            }, 2000)
                        } else {
                            this.$message.warn(data.message || '网络异常');
                        }
                    })
                }
            },

            // 跟进记录-操作记录-协作人列表--弹层
            openDialog (id) {
                switch(id) {
                    case 1: 
                        this.$refs.followRecordListDialog.open({
                            relatedId: this.businessLeadsId,
                            traderId: this.detail.traderId,
                            traderName: this.detail.traderName,
                            belongerId: this.detail.belongerId,
                            belonger: this.detail.belonger,
                            belongerPic: this.detail.belongPic,
                            traderNameLink: this.detail.traderNameLink,
                            traderNameInnerLink: this.detail.traderNameInnerLink,
                            tycFlag: this.detail.tycFlag,
                            contact: this.detail.contact,
                            traderContactId: this.detail.traderContactId,
                            phone: this.detail.phone,
                            telePhone: this.detail.telephone,
                            communicateType: 4109
                        });
                        break;
                    case 2:
                        this.$refs.operationLogDialog.open({
                            bizTypeEnum: '01', // 业务类型 01线索池 02商机库 03 报价单
                            relatedId: this.businessLeadsId, // 业务id
                        });
                        break;
                    case 3:
                        this.$refs.renwuDialog.open({
                            bizType: '2', //  1:商机 2:线索 3:报价
                            relatedId: this.businessLeadsId, // 业务id
                            disabled: this.detail.followStatus == 3
                        });
                        break;
                    case 4:
                        this.$refs.partnerListDialog.open({
                            relatedId: this.businessLeadsId, // 业务id
                            businessType: 5, // 1.商机 2.报价 3.订单 4.售后 5.线索
                            belongerId: this.detail.belongerId,
                            businessNo: this.detail.leadsNo,
                        });
                        break;
                }
            },

            /* dialog内更新数据，panel同步变化 */
            refreshFollowList () { // 跟进记录
                if (this.$refs.followRecordList) {
                    this.$refs.followRecordList.initData();
                }
            },
            refreshPartnerList () { // 协同人
                if (this.$refs.partnerList) {
                    this.$refs.partnerList.initData();
                }
            },
            refreshRenwuPanel () {
                if (this.$refs.renwuList) {
                    this.$refs.renwuList.initData();
                } else {
                    this.getTaskNum();
                }
            },
            // 天眼查详情
            openTyc () {
                this.$refs.tycDetail.open(this.detail.traderName);
            },

            callNumber_ (num) {
                GLOBAL.callNumber({
                    phone: num || '',
                    traderId: this.detail.traderId || 0,
                    traderType: 1,
                    callType: 7,
                    orderId: this.businessLeadsId || 0,
                    traderContactId: this.detail.traderContactId || 0
                })
            },
            handlerRecordAdd (data) {
                console.log(data)
                if(data && data.noneNextDate === 1) {
                    let _this = this;

                    this.$popup.warn({
                        message: '无下次跟进时间，是否需要关闭该条线索？',
                        buttons: [{
                            txt: '关闭线索',
                            btnClass: 'delete',
                            callback() {
                               _this.showCloseLeadsDialog();
                            }
                        },
                        {
                            txt: '无需关闭',
                        }]
                    })
                }
            },
            addVisitPlan() {
                if (GLOBAL.auth('C0401')) {
                    GLOBAL.link({ name: '新建拜访计划', url: '/crm/visitRecord/profile/add?bizid=' + this.businessLeadsId + '&type=1', multi: true })
                } else {
                    GLOBAL.showNoAuth();
                }
            }
        }
    })
}.call(this);