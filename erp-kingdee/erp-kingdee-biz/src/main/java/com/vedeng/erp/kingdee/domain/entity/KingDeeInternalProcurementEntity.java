package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 内部采销
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_INTERNAL_PROCUREMENT")
public class KingDeeInternalProcurementEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer internalProcurementId;

    /**
     * 单据内码
     */
    private int fid;
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;

    /**
     * ERP的单据ID
     */
    private String fQzokBddjtid;
    /**
     * 单据日期
     */
    private String fQzokDate;
    /**
     * 业务类型
     */
    private String fQzokYwlx;
    /**
     * 组织信息
     */
    private String fQzokOrgId;

    /**
     * 是否完全推送到erp
     */
    private Integer pushStatus;

    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntity;

    @Column(jdbcType = "VARCHAR")
    private JSONArray fQzokEntity;

}