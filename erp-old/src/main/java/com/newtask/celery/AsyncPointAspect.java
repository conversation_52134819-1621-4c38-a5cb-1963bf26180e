package com.newtask.celery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.newtask.celery.cache.JoinPointCache;
import com.newtask.celery.exception.CeleryAspectException;
import com.newtask.celery.model.JoinPointModel;
import com.newtask.celery.model.TaskJob;
import com.newtask.celery.service.TaskJobService;
import com.newtask.celery.cache.HandleLockCache;
import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import static com.newtask.celery.AsyncConstants.HANDLE_REG;


/**
 * 异步记录aop
 *
 * <AUTHOR>
 */

@Slf4j
@Aspect
@Component
public class AsyncPointAspect {

    @Resource
    private TaskJobService taskJobService;

    @Pointcut("@annotation(com.newtask.celery.CeleryAsync)")
    public void controllerAspect() {
    }


    @Around("controllerAspect()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        Object returnValue = null;

        String uuid = null;
        boolean async = true;
        try {
            TaskJob taskJob = buildAsyncTask(joinPoint);
            uuid = taskJob.getUuid();

            // 缓存 joinPoint
            cacheJoinPoint(uuid, joinPoint);

            if (HandleLockCache.isAsyncRun(uuid)) {
                async = false;
                log.warn("异步任务正在执行"+ JsonUtils.translateToJson(taskJob));
            } else {
                taskJobService.addTaskJob(taskJob);
            }

        } catch (CeleryAspectException cex) {
            // build fail , 直接执行
            async = false;
        } catch (Throwable e) {
            log.error("async handle error msg :{}", e.getMessage(), e);
        }


        if (!async) {
            // 执行方法内容
            returnValue = joinPoint.proceed(joinPoint.getArgs());
        }

        return returnValue;
    }

    private synchronized void cacheJoinPoint(String uuid, ProceedingJoinPoint joinPoint) {
        if (JoinPointCache.haveCache(uuid)) {
            return;
        }
        JoinPointModel joinPointModel = new JoinPointModel();
        joinPointModel.setParams(joinPoint.getArgs());
        joinPointModel.setProceedingJoinPoint(joinPoint);
        JoinPointCache.put(uuid, joinPointModel);
    }


    private TaskJob buildAsyncTask(ProceedingJoinPoint joinPoint) {
        // 目标类
        TaskJob taskJob = new TaskJob();
        try {
            String methodName = joinPoint.getSignature().getName();
            // 记录handle
            String handleIndex = getHandleIndex(joinPoint, methodName);
            CeleryAsync celeryAsync = getCeleryAsync(joinPoint, methodName);
            Objects.requireNonNull(celeryAsync, "获取注解CeleryAsync失败！");

            // 记录handle
            if (StringUtils.isBlank(handleIndex)) {
                throw new CeleryAspectException("获取任务对应异步方法失败");
            }

            taskJob.setHandleIndex(handleIndex);
            // 记录 key = value
            String body = buildInputBody(joinPoint);
            // 保存入参对象
            taskJob.setInputArgs(SerializationUtils.serialize(joinPoint.getArgs()));
            if (StringUtils.isNotEmpty(body)) {
                taskJob.setInputParams(body.trim());
            }
            taskJob.setQueue(celeryAsync.queue().getId());
            taskJob.setCheckIdempotent(celeryAsync.checkIdempotent());
            taskJob.setValidMillis(celeryAsync.validMillis());
            taskJob.setAddTime(System.currentTimeMillis());

            // 设置唯一标识
            String uuid = UUID.nameUUIDFromBytes((handleIndex + body).getBytes()).toString();
            taskJob.setUuid(uuid);

            return taskJob;
        } catch (Throwable e) {
            log.error("buildAsyncTask fail , msg :{} ", e.getMessage(), e);
            throw new CeleryAspectException("buildAsyncTask fail , msg :" + e.getMessage());
        }

    }


    private static CeleryAsync getCeleryAsync(ProceedingJoinPoint joinPoint, String methodName) throws NoSuchMethodException {
        Class<?> targetClass = joinPoint.getTarget().getClass();

        Class<?>[] parameterTypes = ((MethodSignature) joinPoint.getSignature()).getParameterTypes();

        Method objMethod = targetClass.getMethod(methodName, parameterTypes);

        return objMethod.getDeclaredAnnotation(CeleryAsync.class);
    }


    private static String getHandleIndex(ProceedingJoinPoint joinPoint, String methodName) {
        // 可以优化为二级缓存
        String beanId = SpringContextHolder.getBeanIdByType(joinPoint.getSignature().getDeclaringType());
        if (StringUtils.isBlank(beanId)) {
            throw new CeleryAspectException("async 获取beanId失败！！");
        }

        return beanId + HANDLE_REG + methodName;
    }

    /**
     * 获取入参 json
     *
     * @param joinPoint
     * @return k=v
     */
    /**
     * 获取入参 json
     *
     * @param joinPoint
     * @return k=v
     */
    private static String buildInputBody(ProceedingJoinPoint joinPoint) {
        // 参数名
        String[] paramNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        // 参数值
        Object[] args = joinPoint.getArgs();

        if (null == paramNames || null == args) {
            return null;
        }
        if (args.length > 0 && args.length == paramNames.length) {
            Map<String, Object> inputParamMap = new LinkedHashMap<>(paramNames.length);
            for (int i = 0; i < args.length; i++) {
                inputParamMap.put(paramNames[i], args[i]);
            }

            return JSON.toJSONString(inputParamMap);
        }

        return null;
    }
}