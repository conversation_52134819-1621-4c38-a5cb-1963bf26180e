<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="编辑备货" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="form-list">
    <form method="post" id="editBhSaleorderForm" action="./saveEditBhSaleorder.do" onsubmit="return checkEditBhSaleorderForm();">
        <ul>
            <li>
                <div class="form-tips">
                    <lable style="color: red;">*</lable>
                    <lable>申请原因</lable>
                </div>
                <select class="input-middle" name="prepareReaseonType" id="prepareReaseonType"  onchange="updateReaseon(this)">
                    <option value="-1">请选择</option>
                    <c:forEach var="list" items="${reaseonType}">
                        <option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.prepareReaseonType eq list.sysOptionDefinitionId}">selected="selected"</c:if>>${list.title}</option>
                    </c:forEach>
                </select>
            </li>
            <li  id="prepareCommentsDiv" style="display: none;">
                <div class="infor_name infor_name96">
                    <label></label>
                </div>
                <div class="form-item " >
                    <div class="form-fields form-blanks ">
                        <div class="form-col col-6">
                            <textarea class="input-textarea" name="prepareComments" id="prepareComments">${saleorder.prepareComments}</textarea>
                        </div>
                    </div>
                </div>
            </li>
            <div id="prepareCommentsErr" style="clear:both"></div>
            <li>
                <div class="form-tips">
                    <lable>后期销售方案</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" name="marketingPlan" id="marketingPlan" value="${saleorder.marketingPlan}" />
                    </div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
        	<input type="hidden" name="saleorderId" value="${saleorder.saleorderId}"/>
        	<input type="hidden" name="beforeParams" value='${beforeParams}'>
            <button type="submit">提交</button>
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </form>
</div>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/bh_edit_saleorder_base_info.js?rnd=${resourceVersionKey}'></script>
<%@ include file="../../common/footer.jsp"%>
