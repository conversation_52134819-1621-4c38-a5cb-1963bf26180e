
html #layui_layer_skinlayercss {
    display: none;
    position: absolute;
    width: 1989px;
}
.layui-layer, .layui-layer-shade {
    pointer-events: auto;
    position: fixed;
}
.layui-layer-shade {
    height: 100%;
    left: 0;
    top: 0;
    width: 100%;
}
.layui-layer {
    animation-duration: 0.3s;
    animation-fill-mode: both;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);
    left: 50%;
    margin: 0;
    padding: 0;
    top: 150px;
}
.layui-layer-close {
    position: absolute;
}
.layui-layer-content {
    position: relative;
}
.layui-layer-border {
    border: 1px solid rgba(0, 0, 0, 0.3);
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
}
.layui-layer-moves {
    background-color: rgba(255, 255, 255, 0.3);
    border: 3px solid rgba(0, 0, 0, 0.5);
    cursor: move;
    position: absolute;
}
.layui-layer-load {
    background: #fff url("default/loading-0.gif") no-repeat scroll center center;
}
.layui-layer-ico {
    background: rgba(0, 0, 0, 0) url("default/icon.png") no-repeat scroll 0 0;
}
.layui-layer-btn a, .layui-layer-dialog .layui-layer-ico, .layui-layer-setwin a {
    display: inline-block;
    vertical-align: top;
}
@keyframes bounceIn {
0% {
    opacity: 0;
    transform: scale(0.5);
}
100% {
    opacity: 1;
    transform: scale(1);
}
}
@keyframes bounceIn {
0% {
    opacity: 0;
    transform: scale(0.5);
}
100% {
    opacity: 1;
    transform: scale(1);
}
}
.layui-anim {
    animation-name: bounceIn;
}
@keyframes bounceOut {
100% {
    opacity: 0;
    transform: scale(0.7);
}
30% {
    transform: scale(1.03);
}
0% {
    transform: scale(1);
}
}
@keyframes bounceOut {
100% {
    opacity: 0;
    transform: scale(0.7);
}
30% {
    transform: scale(1.03);
}
0% {
    transform: scale(1);
}
}
.layui-anim-close {
    animation-duration: 0.2s;
    animation-name: bounceOut;
}
@keyframes zoomInDown {
0% {
    animation-timing-function: ease-in-out;
    opacity: 0;
    transform: scale(0.1) translateY(-2000px);
}
60% {
    animation-timing-function: ease-out;
    opacity: 1;
    transform: scale(0.475) translateY(60px);
}
}
@keyframes zoomInDown {
0% {
    animation-timing-function: ease-in-out;
    opacity: 0;
    transform: scale(0.1) translateY(-2000px);
}
60% {
    animation-timing-function: ease-out;
    opacity: 1;
    transform: scale(0.475) translateY(60px);
}
}
.layui-anim-01 {
    animation-name: zoomInDown;
}
@keyframes fadeInUpBig {
0% {
    opacity: 0;
    transform: translateY(2000px);
}
100% {
    opacity: 1;
    transform: translateY(0px);
}
}
@keyframes fadeInUpBig {
0% {
    opacity: 0;
    transform: translateY(2000px);
}
100% {
    opacity: 1;
    transform: translateY(0px);
}
}
.layui-anim-02 {
    animation-name: fadeInUpBig;
}
@keyframes zoomInLeft {
0% {
    animation-timing-function: ease-in-out;
    opacity: 0;
    transform: scale(0.1) translateX(-2000px);
}
60% {
    animation-timing-function: ease-out;
    opacity: 1;
    transform: scale(0.475) translateX(48px);
}
}
@keyframes zoomInLeft {
0% {
    animation-timing-function: ease-in-out;
    opacity: 0;
    transform: scale(0.1) translateX(-2000px);
}
60% {
    animation-timing-function: ease-out;
    opacity: 1;
    transform: scale(0.475) translateX(48px);
}
}
.layui-anim-03 {
    animation-name: zoomInLeft;
}
@keyframes rollIn {
0% {
    opacity: 0;
    transform: translateX(-100%) rotate(-120deg);
}
100% {
    opacity: 1;
    transform: translateX(0px) rotate(0deg);
}
}
@keyframes rollIn {
0% {
    opacity: 0;
    transform: translateX(-100%) rotate(-120deg);
}
100% {
    opacity: 1;
    transform: translateX(0px) rotate(0deg);
}
}
.layui-anim-04 {
    animation-name: rollIn;
}
@keyframes fadeIn {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
.layui-anim-05 {
    animation-name: fadeIn;
}
@keyframes shake {
0%, 100% {
    transform: translateX(0px);
}
10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
}
20%, 40%, 60%, 80% {
    transform: translateX(10px);
}
}
@keyframes shake {
0%, 100% {
    transform: translateX(0px);
}
10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
}
20%, 40%, 60%, 80% {
    transform: translateX(10px);
}
}
.layui-anim-06 {
    animation-name: shake;
}
@keyframes fadeIn {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
.layui-layer-title {
    background-color: #f8f8f8;
    border-bottom: 1px solid #eee;
    color: #333;
    font-size: 14px;
    height: 42px;
    line-height: 42px;
    overflow: hidden;
    padding: 0 80px 0 20px;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.layui-layer-setwin {
    font-size: 0;
    line-height: initial;
    position: absolute;
    right: 15px;
    top: 15px;
}
.layui-layer-setwin a {
    font-size: 12px;
    height: 16px;
    margin-left: 10px;
    position: relative;
    width: 16px;
}
.layui-layer-setwin .layui-layer-min cite {
    background-color: #2e2d3c;
    cursor: pointer;
    height: 2px;
    left: 0;
    margin-top: -1px;
    position: absolute;
    top: 50%;
    width: 14px;
}
.layui-layer-setwin .layui-layer-min:hover cite {
    background-color: #2d93ca;
}
.layui-layer-setwin .layui-layer-max {
    background-position: -32px -40px;
}
.layui-layer-setwin .layui-layer-max:hover {
    background-position: -16px -40px;
}
.layui-layer-setwin .layui-layer-maxmin {
    background-position: -65px -40px;
}
.layui-layer-setwin .layui-layer-maxmin:hover {
    background-position: -49px -40px;
}
.layui-layer-setwin .layui-layer-close1 {
    background-position: 0 -40px;
    cursor: pointer;
}
.layui-layer-setwin .layui-layer-close1:hover {
    opacity: 0.7;
}
.layui-layer-setwin .layui-layer-close2 {
    background-position: -150px -31px;
    height: 30px;
    margin-left: 0;
    position: absolute;
    right: -28px;
    top: -28px;
    width: 30px;
}
.layui-layer-setwin .layui-layer-close2:hover {
    background-position: -181px -31px;
}
.layui-layer-btn {
    padding: 0 10px 12px;
    pointer-events: auto;
    text-align: right;
}
.layui-layer-btn a {
    background-color: #f1f1f1;
    border: 1px solid #dedede;
    border-radius: 2px;
    color: #333;
    cursor: pointer;
    font-weight: 400;
    height: 28px;
    line-height: 28px;
    margin: 0 6px;
    padding: 0 15px;
    text-decoration: none;
}
.layui-layer-btn a:hover {
    opacity: 0.9;
    text-decoration: none;
}
.layui-layer-btn a:active {
    opacity: 0.7;
}
.layui-layer-btn .layui-layer-btn0 {
    background-color: #2e8ded;
    border-color: #4898d5;
    color: #fff;
}
.layui-layer-dialog {
    min-width: 260px;
}
.layui-layer-dialog .layui-layer-content {
    font-size: 14px;
    line-height: 24px;
    overflow: auto;
    padding: 20px;
    position: relative;
    word-break: break-all;
}
.layui-layer-dialog .layui-layer-content .layui-layer-ico {
    height: 30px;
    left: 15px;
    position: absolute;
    top: 16px;
    width: 30px;
}
.layui-layer-ico1 {
    background-position: -30px 0;
}
.layui-layer-ico2 {
    background-position: -60px 0;
}
.layui-layer-ico3 {
    background-position: -90px 0;
}
.layui-layer-ico4 {
    background-position: -120px 0;
}
.layui-layer-ico5 {
    background-position: -150px 0;
}
.layui-layer-ico6 {
    background-position: -180px 0;
}
.layui-layer-rim {
    border: 6px solid rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    box-shadow: none;
}
.layui-layer-msg {
    border: 1px solid #d3d4d3;
    box-shadow: none;
    min-width: 180px;
}
.layui-layer-hui {
    background-color: rgba(0, 0, 0, 0.6);
    border: medium none;
    color: #fff;
    min-width: 100px;
}
.layui-layer-hui .layui-layer-content {
    padding: 12px 25px;
    text-align: center;
}
.layui-layer-dialog .layui-layer-padding {
    padding: 20px 20px 20px 55px;
    text-align: left;
}
.layui-layer-page .layui-layer-content {
    overflow: auto;
    position: relative;
}
.layui-layer-iframe .layui-layer-btn, .layui-layer-page .layui-layer-btn {
    padding-top: 10px;
}
.layui-layer-nobg {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
}
.layui-layer-iframe .layui-layer-content {
    overflow: hidden;
}
.layui-layer-iframe iframe {
    display: block;
    width: 100%;
}
.layui-layer-loading {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    border: medium none;
    border-radius: 100%;
    box-shadow: none;
}
.layui-layer-loading .layui-layer-content {
    background: rgba(0, 0, 0, 0) url("default/loading-0.gif") no-repeat scroll 0 0;
    height: 24px;
    width: 60px;
}
.layui-layer-loading .layui-layer-loading1 {
    background: rgba(0, 0, 0, 0) url("default/loading-1.gif") no-repeat scroll 0 0;
    height: 37px;
    width: 37px;
}
.layui-layer-ico16, .layui-layer-loading .layui-layer-loading2 {
    background: rgba(0, 0, 0, 0) url("default/loading-2.gif") no-repeat scroll 0 0;
    height: 32px;
    width: 32px;
}
.layui-layer-tips {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    border: medium none;
    box-shadow: none;
}
.layui-layer-tips .layui-layer-content {
    background-color: #f90;
    border-radius: 3px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 12px;
    line-height: 22px;
    min-width: 12px;
    padding: 5px 10px;
    position: relative;
}
.layui-layer-tips .layui-layer-close {
    right: -2px;
    top: -1px;
}
.layui-layer-tips i.layui-layer-TipsG {
    border-color: transparent;
    border-style: dashed;
    border-width: 8px;
    height: 0;
    position: absolute;
    width: 0;
}
.layui-layer-tips i.layui-layer-TipsB, .layui-layer-tips i.layui-layer-TipsT {
    border-right-color: #f90;
    border-right-style: solid;
    left: 5px;
}
.layui-layer-tips i.layui-layer-TipsT {
    bottom: -8px;
}
.layui-layer-tips i.layui-layer-TipsB {
    top: -8px;
}
.layui-layer-tips i.layui-layer-TipsL, .layui-layer-tips i.layui-layer-TipsR {
    border-bottom-color: #f90;
    border-bottom-style: solid;
    top: 1px;
}
.layui-layer-tips i.layui-layer-TipsR {
    left: -8px;
}
.layui-layer-tips i.layui-layer-TipsL {
    right: -8px;
}
.layui-layer-lan[type="dialog"] {
    min-width: 280px;
}
.layui-layer-lan .layui-layer-title {
    background: #4476a7 none repeat scroll 0 0;
    border: medium none;
    color: #fff;
}
.layui-layer-lan .layui-layer-lan .layui-layer-btn {
    border-top: 1px solid #e9e7e7;
    padding: 10px;
    text-align: right;
}
.layui-layer-lan .layui-layer-btn a {
    background: #bbb5b5 none repeat scroll 0 0;
    border: medium none;
}
.layui-layer-lan .layui-layer-btn .layui-layer-btn1 {
    background: #c9c5c5 none repeat scroll 0 0;
}
.layui-layer-molv .layui-layer-title {
    background: #009f95 none repeat scroll 0 0;
    border: medium none;
    color: #fff;
}
.layui-layer-molv .layui-layer-btn a {
    background: #009f95 none repeat scroll 0 0;
}
.layui-layer-molv .layui-layer-btn .layui-layer-btn1 {
    background: #92b8b1 none repeat scroll 0 0;
}
