package com.vedeng.erp.kingdee.batch.tasklet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.tasklet.BaseTasklet;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.handle.InvoiceWarehouseOutInHandle;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售侧 发票和出入库关系绑定tasklet
 * @date 2023/1/31 13:54
 */
@Service
@Slf4j
public class SaleOrderInvoiceWarehouseOutInTasklet extends BaseTasklet {

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;
    @Autowired
    private InvoiceWarehouseOutInHandle invoiceWarehouseOutInHandle;


    @Override
    public void doExec(Map<String, Object> jobParameters) throws Exception {
        log.info("销售红字发票和入库关系绑定开始");
        String beginTime = (String) jobParameters.get("beginTime");
        String endTime = (String) jobParameters.get("endTime");
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 销售开票
                .type(505)
                ._pagesize(Integer.MAX_VALUE)
                ._skiprows(0)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        List<BatchInvoiceDto> batchInvoiceDtoList = batchInvoiceDtoMapper.saleorderRedInvoiceFindByAll(batchInvoiceDto);

        if(CollUtil.isNotEmpty(batchInvoiceDtoList)){
            invoiceWarehouseOutInHandle.bindSaleOrderRedInvoiceAndWarehouseInRelation(batchInvoiceDtoList);
        }

    }
}
