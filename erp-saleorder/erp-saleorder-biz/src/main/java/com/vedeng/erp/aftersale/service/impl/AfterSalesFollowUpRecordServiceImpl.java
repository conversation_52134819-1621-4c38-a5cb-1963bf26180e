package com.vedeng.erp.aftersale.service.impl;

import com.vedeng.orderstream.aftersales.dao.AfterSalesFollowUpRecordMapper;
import com.vedeng.orderstream.aftersales.model.AfterSalesFollowUpRecord;
import com.vedeng.erp.aftersale.service.AfterSalesFollowUpRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/13 20:29
 * @desc :
 */
@Service
public class AfterSalesFollowUpRecordServiceImpl implements AfterSalesFollowUpRecordService {

    @Qualifier("afterSalesFollowUpRecordMapper")
    @Autowired
    private AfterSalesFollowUpRecordMapper afterSalesFollowUpRecordMapper;

    @Override
    public int insert(AfterSalesFollowUpRecord afterSalesFollowUpRecord) {

        return afterSalesFollowUpRecordMapper.insert(afterSalesFollowUpRecord);

    }

    @Override
    public List<AfterSalesFollowUpRecord> selectByAfterSalesId(Integer afterSalesId) {

        return afterSalesFollowUpRecordMapper.selectByAfterSalesId(afterSalesId);

    }

    @Override
    public AfterSalesFollowUpRecord getAfterSalesFollowUpRecordById(Integer recordId) {
        return afterSalesFollowUpRecordMapper.selectByPrimaryKey(recordId);
    }

    @Override
    public void updateAfterSalesFollowUpRecordSelectById(AfterSalesFollowUpRecord afterSalesFollowUpRecord) {
        afterSalesFollowUpRecordMapper.updateByPrimaryKeySelective(afterSalesFollowUpRecord);
    }
}
