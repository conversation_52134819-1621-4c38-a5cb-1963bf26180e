package com.vedeng.logistics.model;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName LogincalPermission.java
 * @Description TODO 逻辑仓展示权限
 * @createTime 2020年07月06日 11:45:00
 */
public class LogincalPermission implements Serializable {

    private Integer orgId;

    private List<Integer> permissions;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<Integer> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<Integer> permissions) {
        this.permissions = permissions;
    }

    @Override
    public String toString() {
        return "LogincalPermission{" +
                "orgId=" + orgId +
                ", permissions=" + permissions +
                '}';
    }
}
