package com.vedeng.order.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: TODO报价咨询记录表实体类
 * @date 2021/8/3 13:47
 */
@Data
public class QuoteorderConsultDetail {

    private Integer quoteorderConsultDetailId;

    private Integer quoteorderId;//报价单号

    private Integer quoteorderConsultId;//报价咨询表编号

    private String skuNo;

    private String otherContent;

    private Integer quoteConsultType;

    private Long addTime;

    private Integer creator;

    private Integer isDelete;

    private Long modTime;

    private Integer updater;

}
