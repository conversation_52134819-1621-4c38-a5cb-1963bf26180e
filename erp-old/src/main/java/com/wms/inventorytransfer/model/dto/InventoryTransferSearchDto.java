package com.wms.inventorytransfer.model.dto;

import java.io.Serializable;

/**
 * 库存转移单搜索数据传输对象
 *
 * <AUTHOR>
 * @date 2020/7/10 16:35:27
 */
public class InventoryTransferSearchDto implements Serializable {
    /**
     * 库存转移单单号
     */
    private String inventoryTransferNo;

    /**
     * 库存转移单类型
     */
    private Integer type;

    /**
     * '转移单状态  （1进行中  2已完结）',
     */
    private Integer status;

    /**
     * 关联单号
     */
    private String orderNo;

    /**
     * 搜索开始时间
     */
    private String searchBeginTime;

    /**
     * 搜索结束时间
     */
    private String searchEndTime;

    /**
     * 搜索开始时间戳
     */
    private Long startTime;

    /**
     * 搜索结束时间戳
     */
    private Long endTime;

    public String getInventoryTransferNo() {
        return inventoryTransferNo;
    }

    public void setInventoryTransferNo(String inventoryTransferNo) {
        this.inventoryTransferNo = inventoryTransferNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSearchBeginTime() {
        return searchBeginTime;
    }

    public void setSearchBeginTime(String searchBeginTime) {
        this.searchBeginTime = searchBeginTime;
    }

    public String getSearchEndTime() {
        return searchEndTime;
    }

    public void setSearchEndTime(String searchEndTime) {
        this.searchEndTime = searchEndTime;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
