<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.OrganizationMapper">
	<resultMap id="BaseResultMap" type="com.vedeng.authorization.model.Organization">
		<id column="ORG_ID" property="orgId" jdbcType="INTEGER" />
		<result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
		<result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
		<result column="ORG_NAME" property="orgName" jdbcType="VARCHAR" />
		<result column="LEVEL" property="level" jdbcType="INTEGER" />
		<result column="TYPE" property="type" jdbcType="INTEGER" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />

		<result column="UPDATER" property="updater" jdbcType="INTEGER" />
		<result column="IS_DELETED" property="isDeleted" jdbcType="INTEGER" />
	</resultMap>
	<resultMap id="VoResultMap" type="com.vedeng.authorization.model.vo.OrganizationVo" extends="BaseResultMap">
	</resultMap>
	<sql id="Base_Column_List">
		ORG_ID, COMPANY_ID, PARENT_ID, ORG_NAME, LEVEL, TYPE, ADD_TIME, CREATOR, MOD_TIME,
		UPDATER,IS_DELETED
	</sql>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from T_ORGANIZATION
		where ORG_ID = #{orgId,jdbcType=INTEGER}
	</select>
	<select id="selectOrgByKey" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.Organization">
		select
		<include refid="Base_Column_List" />
		from T_ORGANIZATION
		where ORG_ID = #{orgId,jdbcType=INTEGER}
	</select>
	<delete id="delete" parameterType="java.lang.Integer">
		 UPDATE T_ORGANIZATION SET IS_DELETED=1
		where ORG_ID = #{orgId,jdbcType=INTEGER}
	</delete>
	<delete id="deleteByOrgId">
		UPDATE T_ORGANIZATION SET IS_DELETED=1
		where ORG_ID in
		<foreach item="orgId" index="index" collection="list" open="(" separator="," close=")">
			#{orgId}
		</foreach>
	</delete>
	<insert id="insert" parameterType="com.vedeng.authorization.model.Organization">
		insert into T_ORGANIZATION
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="orgId != null">
				ORG_ID,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="parentId != null">
				PARENT_ID,
			</if>
			<if test="orgName != null">
				ORG_NAME,
			</if>
			<if test="level != null">
				LEVEL,
			</if>
			<if test="type != null">
				TYPE,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="orgId != null">
				#{orgId,jdbcType=INTEGER},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null">
				#{parentId,jdbcType=INTEGER},
			</if>
			<if test="orgName != null">
				#{orgName,jdbcType=VARCHAR},
			</if>
			<if test="level != null">
				#{level,jdbcType=INTEGER},
			</if>
			<if test="type != null">
				#{type,jdbcType=INTEGER},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	<update id="update" parameterType="com.vedeng.authorization.model.Organization">
		update T_ORGANIZATION
		<set>
			<if test="companyId != null">
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null">
				PARENT_ID = #{parentId,jdbcType=INTEGER},
			</if>
			<if test="orgName != null">
				ORG_NAME = #{orgName,jdbcType=VARCHAR},
			</if>
			<if test="level != null">
				LEVEL = #{level,jdbcType=INTEGER},
			</if>
			<if test="type != null">
				TYPE = #{type,jdbcType=INTEGER},
			</if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
		</set>
		where ORG_ID = #{orgId,jdbcType=INTEGER}
	</update>
	<select id="findOrgsByParentId" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		T_ORGANIZATION
		WHERE
		PARENT_ID = #{parentId,jdbcType=INTEGER}
		and
		COMPANY_ID = #{companyId,jdbcType=INTEGER}
		order by
		ORG_ID
	</select>

	<!-- getByOrg -->
	<select id="getByOrg" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.Organization">
		select
		<include refid="Base_Column_List" />
		from T_ORGANIZATION
		<where>
			1=1
			<if test="companyId != null">
				and COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			<if test="orgName != null" >
				and ORG_NAME = #{orgName,jdbcType=VARCHAR}
			</if>
			<if test="parentId != null" >
				and PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
			<if test="orgId != null" >
				and ORG_ID = #{orgId,jdbcType=INTEGER}
			</if>
		</where>
		limit 1
	</select>

	<!-- getOrgList -->
	<select id="getOrgList" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from T_ORGANIZATION
		<where>
			COMPANY_ID = #{companyId,jdbcType=INTEGER}
			<if test="parentId !=null and parentId > 0">
				AND PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
		</where>
	</select>

	<!-- getOrgList -->
	<select id="getOrgListNotDelete" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from T_ORGANIZATION
		<where>
			COMPANY_ID = #{companyId,jdbcType=INTEGER}
			AND IS_DELETED!=1
			<if test="parentId !=null and parentId > 0">
				AND PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
		</where>
	</select>

	<!-- 查询销售部门group by去除重复 -->
	<select id="getSalesOrgList" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT A.ORG_ID,
			   A.COMPANY_ID,
			   A.PARENT_ID,
			   A.ORG_NAME ,
			   A.LEVEL,
			   A.ADD_TIME,
			   A.CREATOR,
			   A.MOD_TIME,
			   A.UPDATER
		FROM T_ORGANIZATION A LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID
							  left join T_ORGANIZATION C ON A.PARENT_ID=C.ORG_ID
							  left join T_ORGANIZATION D ON C.PARENT_ID=D.ORG_ID

		WHERE  A.COMPANY_ID =1 AND B.TYPE = 310 AND A.IS_DELETED!=1
		GROUP BY A.ORG_ID
		ORDER BY D.PARENT_ID,C.PARENT_ID ASC, A.PARENT_ID ASC,A.ADD_TIME
	</select>
	<select id="getSalesOrgListByParentId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT A.ORG_ID,
			   A.COMPANY_ID,
			   A.PARENT_ID,
			   A.ORG_NAME ,
			   A.LEVEL,
			   A.ADD_TIME,
			   A.CREATOR,
			   A.MOD_TIME,
			   A.UPDATER
		FROM T_ORGANIZATION A LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID
		WHERE  A.COMPANY_ID =1 AND B.TYPE = 310 AND A.IS_DELETED!=1
		and A.PARENT_ID=#{parentId}
		GROUP BY A.ORG_ID
		ORDER BY A.ORG_NAME
	</select>

	<select id="getQuoteOrgList" resultMap="BaseResultMap">
		SELECT A.ORG_ID,
		A.COMPANY_ID,
		A.PARENT_ID,
		A.ORG_NAME,
		A.LEVEL,
		A.ADD_TIME,
		A.CREATOR,
		A.MOD_TIME,
		A.UPDATER
		FROM T_ORGANIZATION A LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID
		WHERE B.TYPE = #{orgType,jdbcType=INTEGER}
		AND A.ORG_ID IN
		<foreach collection="orgList" index="index" item="value" open="(" close=")" separator=",">
			#{value,jdbcType=INTEGER}
		</foreach>
		GROUP BY A.ORG_ID
	</select>

	<!-- 根据用户Id查询所在部门，LIMIT 1只取第一个部门 -->
	<select id="getOrgNameByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT A.ORG_ID,
			   A.COMPANY_ID,
			   A.PARENT_ID,
			   A.ORG_NAME,
			   A.LEVEL,
			   A.ADD_TIME,
			   A.CREATOR,
			   A.MOD_TIME,
			   A.UPDATER
		FROM T_ORGANIZATION A
				 LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID
				 LEFT JOIN T_R_USER_POSIT C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.USER_ID = #{userId,jdbcType=INTEGER}
		LIMIT 1
	</select>

	<select id="getOrgNameById" resultType="java.lang.String" parameterType="java.lang.Integer">
		select
		ORG_NAME
		from T_ORGANIZATION
		<where>
			1=1
			<if test="orgId != null" >
				and ORG_ID = #{orgId,jdbcType=INTEGER}
			</if>
		</where>
	</select>

	<select id="getOrgListByPositType" resultType="com.vedeng.authorization.model.Organization">
		SELECT A.*
		FROM T_ORGANIZATION A WHERE A.TYPE = #{typeId,jdbcType=INTEGER}
		<if test="companyId != null and companyId != 0">
			and A.COMPANY_ID = #{companyId}
		</if>
	</select>

	<select id="getOrgByList" parameterType="java.util.List" resultType="com.vedeng.authorization.model.Organization">
		SELECT A.*
		FROM T_ORGANIZATION A WHERE A.ORG_ID IN
		<foreach collection="orgIdList" item="orgId" index="num" separator="," open="(" close=")">
			#{orgId,jdbcType=INTEGER}
		</foreach>
	</select>

	<select id="getOrganizationVoList" resultMap="VoResultMap" parameterType="com.vedeng.authorization.model.Organization">
		select
		<include refid="Base_Column_List" />
		from T_ORGANIZATION
		<where>
			1=1
			<if test="companyId != null">
				and COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			<if test="parentId != null" >
				and PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
			<if test="level != null">
				and LEVEL = #{level,jdbcType=INTEGER}
			</if>
			<if test="type != null">
				and TYPE = #{type,jdbcType=INTEGER}
			</if>
		</where>
	</select>

	<select id="getOrgIdsList" resultType="java.lang.Integer" parameterType="com.vedeng.authorization.model.Organization">
		select
		ORG_ID
		from T_ORGANIZATION
		<where>
			1=1
			<if test="companyId != null">
				and COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			<if test="parentId != null" >
				and PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
			<if test="level != null">
				and LEVEL = #{level,jdbcType=INTEGER}
			</if>
			<if test="type != null">
				and TYPE = #{type,jdbcType=INTEGER}
			</if>
		</where>
	</select>

	<select id="getAllOrgIdList" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		select
			ORG_ID
		from T_ORGANIZATION where FIND_IN_SET(ORG_ID,getOrganizationChildList(#{parentId}))
	</select>

	<!-- 根据客户ID查询客户负责人信息 -->
	<select id="getUserByTraderIdList" resultType="com.vedeng.authorization.model.User" parameterType="java.util.List">
		SELECT DISTINCT A.USER_ID,
		A.COMPANY_ID,
		A.USERNAME,
		A.NUMBER,
		A.PASSWORD,
		A.SALT,
		A.PARENT_ID,
		A.IS_DISABLED,
		A.DISABLED_REASON,
		A.LAST_LOGIN_TIME,
		A.LAST_LOGIN_IP,
		A.ADD_TIME,
		A.CREATOR,
		A.MOD_TIME,
		A.UPDATER,
		TR.TRADER_ID
		FROM T_USER A LEFT JOIN T_R_TRADER_J_USER TR ON TR.USER_ID = A.USER_ID
		WHERE TR.TRADER_ID IN
		<foreach collection="traderIdList" item="traderId" open="(" close=")" separator=",">
			#{traderId,jdbcType=INTEGER}
		</foreach>
		<if test="traderType != null">
			AND A.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
		</if>
	</select>
	<select id="getTraderUserAndOrgByTraderId" resultType="com.vedeng.authorization.model.User" parameterType="java.lang.Integer">
		SELECT A.USER_ID,
		B.USERNAME,
		E.ORG_NAME,
		D.ORG_ID,
		A.TRADER_ID
		FROM T_R_TRADER_J_USER A
		INNER JOIN T_USER B ON A.USER_ID = B.USER_ID
		LEFT JOIN T_R_USER_POSIT C ON B.USER_ID = C.USER_ID
		LEFT JOIN T_POSITION D ON C.POSITION_ID = D.POSITION_ID
		LEFT JOIN T_ORGANIZATION E ON D.ORG_ID = E.ORG_ID
		WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
		<if test="traderType != null">
			AND A.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
		</if>
		LIMIT 1
	</select>
	<select id="getUserOrgList" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT
			DISTINCT org.ORG_ID,org.ORG_NAME
		FROM
			T_USER tu
				LEFT JOIN T_R_USER_POSIT up ON tu.USER_ID = up.USER_ID
				LEFT JOIN T_POSITION po ON up.POSITION_ID=po.POSITION_ID
				LEFT JOIN T_ORGANIZATION org ON org.ORG_ID = po.ORG_ID
		where tu.COMPANY_ID=#{companyId,jdbcType=INTEGER} ORDER BY org.ORG_ID asc
	</select>
	<select id="getParentOrgListByList" parameterType="java.util.List" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM
		T_ORGANIZATION a
		WHERE 1=1
		<if test="list != null and list.size() > 0">
			AND a.ORG_ID IN
			<foreach collection="list" item="org" index="index" open="(" close=")" separator=",">
				#{org.parentId,jdbcType=INTEGER}
			</foreach>
		</if>
	</select>
	<select id="getUserListBtOrgId" resultType="com.vedeng.authorization.model.User">
		SELECT
		d.USER_ID,
		d.USERNAME
		FROM
		T_ORGANIZATION a
		LEFT JOIN T_POSITION b ON a.ORG_ID = b.ORG_ID AND b.TYPE = #{type,jdbcType=INTEGER}
		LEFT JOIN T_R_USER_POSIT c ON c.POSITION_ID = b.POSITION_ID
		LEFT JOIN T_USER d ON c.USER_ID = d.USER_ID
		WHERE 1=1
		AND a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
		<if test="orgIdList != null and orgIdList.size()>0">
			AND a.ORG_ID IN
			<foreach collection="orgIdList" index="index" item="orgId" separator="," open="(" close=")">
				#{orgId,jdbcType=INTEGER}
			</foreach>
		</if>
		AND d.USER_ID IS NOT NULL
		ORDER BY
		d.USERNAME
	</select>

	<!--待采购 下拉框-->
	<select id="getOrgListByPositTypes" resultMap="BaseResultMap">
		select distinct T.* from T_USER U
									 LEFT JOIN T_R_USER_ROLE RB ON U.USER_ID=RB.USER_ID
									 LEFT JOIN T_ROLE R ON RB.ROLE_ID=R.ROLE_ID
									 LEFT JOIN T_R_USER_POSIT B ON U.USER_ID = B.USER_ID
									 LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
									 LEFT JOIN T_ORGANIZATION T on C.ORG_ID=T.ORG_ID
		WHERE R.ROLE_NAME IN ('产品专员','产品主管','产品总监')
		  AND U.IS_DISABLED='0'
		  AND U.COMPANY_ID=#{companyId}
	</select>
	<select id="getOrgByTraderId" resultType="com.vedeng.authorization.model.User" parameterType="java.lang.Integer">
		SELECT A.USER_ID,
		B.USERNAME,
		E.ORG_NAME,
		A.TRADER_ID
		FROM T_R_TRADER_J_USER A
		INNER JOIN T_USER B ON A.USER_ID = B.USER_ID
		LEFT JOIN T_R_USER_POSIT C ON B.USER_ID = C.USER_ID
		LEFT JOIN T_POSITION D ON C.POSITION_ID = D.POSITION_ID
		LEFT JOIN T_ORGANIZATION E ON D.ORG_ID = E.ORG_ID
		WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
		<if test="traderType != null">
			AND A.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
		</if>
	</select>
	<select id="getOrgNameListByUserId" parameterType="java.lang.Integer" resultType="com.vedeng.authorization.model.User">
		SELECT A.ORG_ID,
			   A.COMPANY_ID,
			   A.PARENT_ID,
			   A.ORG_NAME,
			   A.LEVEL,
			   A.ADD_TIME,
			   A.CREATOR,
			   A.MOD_TIME,
			   A.UPDATER
		FROM T_ORGANIZATION A
				 LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID
				 LEFT JOIN T_R_USER_POSIT C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.USER_ID = #{userId,jdbcType=INTEGER}

	</select>

	<select id="getOrgIdListByUserId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		SELECT B.ORG_ID
		FROM T_POSITION B
				 LEFT JOIN T_R_USER_POSIT C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.USER_ID = #{userId,jdbcType=INTEGER}
	</select>

	<select id="getFinanceFlagByUserId" resultType="com.vedeng.authorization.model.User">
		SELECT
			T1.USERNAME
		FROM
			T_USER T1
				LEFT JOIN T_R_USER_POSIT T2 ON T1.USER_ID = T2.USER_ID
				AND T1.USER_ID = #{userId}
				LEFT JOIN T_POSITION T3 ON T2.POSITION_ID = T3.POSITION_ID
				LEFT JOIN T_ORGANIZATION T4 ON T3.ORG_ID = T4.ORG_ID
				LEFT JOIN T_ORGANIZATION T5 ON T4.PARENT_ID = T5.ORG_ID
		WHERE
			T4.ORG_NAME = '南京财务部'
		   OR T5.ORG_NAME = '南京财务部'
	</select>


	<!--根据部门父类查询其子类-->
	<resultMap id="BaseOrganization" type="com.vedeng.authorization.model.vo.OrganizationVo">
		<id column="ORG_ID" property="orgId" jdbcType="INTEGER" />
		<result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
		<result column="ORG_NAME" property="orgName" jdbcType="VARCHAR" />
		<collection property="organizations" column="ORG_ID" select="childOrganization"/>
	</resultMap>
	<select id="childOrganization" resultMap="BaseOrganization">
		select ORG_ID,COMPANY_ID,ORG_NAME from T_ORGANIZATION
		where PARENT_ID=#{orgId} AND IS_DELETED = 0
	</select>
	<select id="getAllOrganizationOfCompany" resultType="com.vedeng.authorization.model.Organization">
		SELECT * FROM T_ORGANIZATION WHERE COMPANY_ID = #{companyId}
	</select>
	<select id="getOrgIdByOrgName" resultType="java.lang.Integer">
		SELECT ORG_ID FROM T_ORGANIZATION WHERE ORG_NAME = #{orgName} and COMPANY_ID = #{companyId} limit 1
	</select>

	<select id="getOrganizationsByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT A.ORG_ID,
			   A.COMPANY_ID,
			   A.PARENT_ID,
			   A.ORG_NAME,
			   A.LEVEL,
			   A.ADD_TIME,
			   A.CREATOR,
			   A.MOD_TIME,
			   A.UPDATER
		FROM T_ORGANIZATION A
				 LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID
				 LEFT JOIN T_R_USER_POSIT C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.USER_ID = #{userId,jdbcType=INTEGER}
	</select>
	<select id="getNewUserListBtOrgId" resultType="com.vedeng.authorization.model.User">
		SELECT
		d.USER_ID,
		d.USERNAME
		FROM
		T_ORGANIZATION a
		LEFT JOIN T_POSITION b ON a.ORG_ID = b.ORG_ID AND b.TYPE = #{type,jdbcType=INTEGER}
		LEFT JOIN T_R_USER_POSIT c ON c.POSITION_ID = b.POSITION_ID
		LEFT JOIN T_USER d ON c.USER_ID = d.USER_ID
		WHERE 1=1
		AND a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
		<if test="orgIdList != null and orgIdList.size()>0">
			AND a.ORG_ID IN
			<foreach collection="orgIdList" index="index" item="orgId" separator="," open="(" close=")">
				#{orgId,jdbcType=INTEGER}
			</foreach>
		</if>
		AND d.USER_ID IS NOT NULL
		GROUP BY d.USER_ID
		ORDER BY
		d.USER_ID
	</select>
	<select id="getSalesOrgListByStruct" parameterType="java.lang.Integer" resultMap="BaseResultMap">
				SELECT
			(CASE
				WHEN C.PARENT_ID = 0 THEN A.ORG_ID
				WHEN D.PARENT_ID = 0 THEN C.ORG_ID
				WHEN E.PARENT_ID = 0 THEN D.ORG_ID
				ELSE E.ORG_ID END
			) AS P1,
			(CASE
				WHEN C.PARENT_ID = 0 THEN 0
				WHEN D.PARENT_ID = 0 THEN A.ORG_ID
				WHEN E.PARENT_ID = 0 THEN C.ORG_ID
				ELSE D.ORG_ID END
			) AS P2,
			(CASE
				WHEN C.PARENT_ID = 0 THEN 0
				WHEN D.PARENT_ID = 0 THEN 0
				WHEN E.PARENT_ID = 0 THEN A.ORG_ID
				ELSE C.ORG_ID END
			) AS P3,
			(CASE
				WHEN C.PARENT_ID = 0 THEN 0
				WHEN D.PARENT_ID = 0 THEN 0
				WHEN E.PARENT_ID = 0 THEN 0
				ELSE A.ORG_ID END
			) AS P4,
			A.*
		FROM T_ORGANIZATION A
				LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID
				left join T_ORGANIZATION C ON A.PARENT_ID=C.ORG_ID
				left join T_ORGANIZATION D ON C.PARENT_ID=D.ORG_ID
				left join T_ORGANIZATION E ON D.PARENT_ID=E.ORG_ID
		WHERE  A.COMPANY_ID =1 AND B.TYPE = #{orgType} AND A.IS_DELETED!=1
		GROUP BY A.ORG_ID
		ORDER BY P1,P2,P3,P4,A.ADD_TIME
	</select>
	<select id="getSupplyChainFlagByUserId" resultType="com.vedeng.authorization.model.User">
		SELECT
			T1.USERNAME
		FROM
			T_USER T1
				LEFT JOIN T_R_USER_POSIT T2 ON T1.USER_ID = T2.USER_ID
				AND T1.USER_ID = #{userId}
				LEFT JOIN T_POSITION T3 ON T2.POSITION_ID = T3.POSITION_ID
				LEFT JOIN T_ORGANIZATION T4 ON T3.ORG_ID = T4.ORG_ID
				LEFT JOIN T_ORGANIZATION T5 ON T4.PARENT_ID = T5.ORG_ID
		WHERE
			T4.ORG_NAME = '供应链管理部'
		   OR T5.ORG_NAME = '供应链管理部'
		LIMIT 1
	</select>
	<select id="getOrgName" resultType="com.vedeng.authorization.model.Organization">
		SELECT
			b.`LEVEL`,
			b.ORG_NAME,
			a.PARENT_ID
		FROM
			T_ORGANIZATION a
			LEFT JOIN T_ORGANIZATION b ON a.PARENT_ID = b.ORG_ID
		WHERE
			a.ORG_ID = #{orgId}

	</select>
	<select id="getCurrentLevel" resultType="com.vedeng.authorization.model.Organization">
		SELECT
			a.`LEVEL`,
			a.`TYPE`,
			a.ORG_ID,
			b.COMPANY_ID
		FROM
			T_POSITION a
			LEFT JOIN T_ORGANIZATION b ON a.ORG_ID = b.ORG_ID
		WHERE
			a.ORG_ID = #{orgId}
			AND a.type IN ( SELECT type FROM T_ORGANIZATION c WHERE c.ORG_ID = #{orgId} )
	</select>

	<select id="selectOrganizationNameByOrgids" resultType="com.vedeng.authorization.model.Organization">
		SELECT *
		FROM T_ORGANIZATION a
		where  a.ORG_ID in
		<foreach collection="list" close=")" open="(" separator="," item="orgId">
			#{orgId}
		</foreach>
	</select>

    <select id="selectAllOrgId" resultType="java.lang.Integer" parameterType="com.vedeng.authorization.model.Organization">
        SELECT
            ORG_ID
        FROM
            T_ORGANIZATION
        WHERE
            `TYPE` = #{type}
            AND COMPANY_ID = #{companyId}
    </select>
	<select id="queryOrgByName" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT
            ORG_ID
        FROM
            T_ORGANIZATION
        WHERE
            `ORG_NAME` like concat("%",#{orgName},"%")
            AND COMPANY_ID = 1
			AND TYPE = 310
    </select>
	<select id="getUserIdByOrgId" resultType="java.lang.Integer">
		SELECT
			T1.USER_ID
		FROM
			T_USER T1
		LEFT JOIN T_R_USER_POSIT T2 ON T1.USER_ID = T2.USER_ID
		LEFT JOIN T_POSITION T3 ON T2.POSITION_ID = T3.POSITION_ID
		WHERE
			T3.ORG_ID = #{orgId}
	</select>

	<select id="getAllOrganization" resultType="com.vedeng.authorization.model.Organization">
		SELECT * FROM T_ORGANIZATION WHERE IS_DELETED = 0 and COMPANY_ID = 1;
	</select>

	<select id="getAllOrganizationOnly" resultType="com.vedeng.authorization.model.Organization">
		SELECT ORG_ID,PARENT_ID,ORG_NAME FROM T_ORGANIZATION WHERE IS_DELETED = 0 and COMPANY_ID = 1;
	</select>


	<!--=====================================================================================================================================================================================-->
	<select id="getOrgIdByUserId" resultType="com.vedeng.authorization.model.Organization">
		SELECT O.ORG_ID, O.PARENT_ID, O.LEVEL
		FROM T_R_USER_POSIT RUP
		LEFT JOIN T_POSITION P ON RUP.POSITION_ID = P.POSITION_ID
		LEFT JOIN T_ORGANIZATION O ON P.ORG_ID = O.ORG_ID
		WHERE O.IS_DELETED != 1 AND RUP.USER_ID = #{userId,jdbcType=INTEGER}
		GROUP BY RUP.USER_ID
	</select>
	<select id="getOriganizationByParentId" resultType="com.vedeng.authorization.model.Organization">
		SELECT A.ORG_ID,
			   A.PARENT_ID,
			   A.ORG_NAME ,
			   A.LEVEL
		FROM T_ORGANIZATION A
		where A.PARENT_ID = #{parentId}
		AND A.COMPANY_ID =1 AND A.IS_DELETED!=1
		GROUP BY A.ORG_ID
		ORDER BY A.PARENT_ID ASC,A.ADD_TIME
	</select>
    <select id="getParentIdById" resultType="java.lang.Integer">
		select  PARENT_ID
		from T_ORGANIZATION
		WHERE ORG_ID = #{orgId,jdbcType=INTEGER}
	</select>
	<select id="queryOrgNameB2B" resultType="java.lang.String">
		SELECT  GROUP_CONCAT(a.ORG_NAME order by a.ORG_ID SEPARATOR '/')
		from T_ORGANIZATION a
		where   a.IS_DELETED = 0 and   a.TYPE =310 and
		a.ORG_ID in(
		SELECT DISTINCT T2.ORG_ID
		FROM (
		SELECT DISTINCT
		@r AS t1_id,
		(SELECT @r:= PARENT_ID FROM T_ORGANIZATION WHERE
		IS_DELETED = 0
		and TYPE = 310
		and ORG_ID = t1_id) AS PARENT_ID
		FROM
		(SELECT @r:= #{orgId}) vars,
		T_ORGANIZATION h
		WHERE h.LEVEL >= 3 ) T1
		JOIN T_ORGANIZATION T2
		ON T1.t1_id = T2.ORG_ID )
	</select>

    <select id="getUserIdsByOrgIds" resultType="java.lang.Integer">
		SELECT
			A.USER_ID
		FROM
			T_USER A
			LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
			LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		WHERE
			C.ORG_ID IN
		<foreach item="orgId" index="index" collection="orgIds" open="(" separator="," close=")">
			#{orgId,jdbcType=INTEGER}
		</foreach>
		GROUP BY
			A.USER_ID
	</select>

	<select id="getOrganizationByTraderId" resultMap="BaseResultMap">
		SELECT E.*
		FROM T_R_TRADER_J_USER A
		INNER JOIN T_USER B ON A.USER_ID = B.USER_ID
		LEFT JOIN T_R_USER_POSIT C ON B.USER_ID = C.USER_ID
		LEFT JOIN T_POSITION D ON C.POSITION_ID = D.POSITION_ID
		LEFT JOIN T_ORGANIZATION E ON D.ORG_ID = E.ORG_ID
		WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
		AND A.TRADER_TYPE = 1
		LIMIT 1
    </select>
</mapper>