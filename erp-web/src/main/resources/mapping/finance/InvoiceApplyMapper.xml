<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.InvoiceApplyMapper" >
    <resultMap id="BaseResultMap" type="com.vedeng.finance.model.InvoiceApply" >
        <id column="INVOICE_APPLY_ID" property="invoiceApplyId" jdbcType="INTEGER" />
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
        <!-- <result column="INVOICE_PROPERTY" property="invoiceProperty" jdbcType="INTEGER"/> -->
        <result column="TYPE" property="type" jdbcType="INTEGER" />
        <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
        <result column="IS_AUTO" property="isAuto" jdbcType="BIT" />
        <result column="IS_ADVANCE" property="isAdvance" jdbcType="BIT" />
        <result column="ADVANCE_VALID_STATUS" property="advanceValidStatus" jdbcType="BIT" />
        <result column="ADVANCE_VALID_USERID" property="advanceValidUserid" jdbcType="BIGINT"/>
        <result column="ADVANCE_VALID_TIME" property="advanceValidTime" jdbcType="BIGINT" />
        <result column="ADVANCE_VALID_COMMENTS" property="advanceValidComments" jdbcType="VARCHAR" />
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
        <result column="VALID_COMMENTS" property="validComments" jdbcType="VARCHAR" />
        <result column="INVOICE_ST_STATUS" property="invoiceStStatus" jdbcType="BIT" />
        <result column="INVOICE_PRINT_STATUS" property="invoicePrintStatus"	jdbcType="BIT" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="APPLY_METHOD" property="applyMethod" jdbcType="INTEGER" />
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
        <!-- add by Tomcat.Hui 2019/9/3 19:34 .Desc:VDERP-1325 分批开票 查询发票明细对象 . start -->
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
        <!-- add by Tomcat.Hui 2019/9/3 19:34 .Desc:VDERP-1325 分批开票 查询发票明细对象 . end -->
        <!-- add by Tomcat.Hui 2019/9/3 19:34 .Desc:VDERP-1214 开票申请界面优化 . start -->
        <!-- add by Tomcat.Hui 2019/9/23 11:16 .Desc:VDERP-1214 开票申请界面优化 增加订单的 INVOICE_COMMENTS字段.  -->
        <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR" />
        <!-- add by Tomcat.Hui 2019/9/3 19:34 .Desc:VDERP-1214 开票申请界面优化 . end -->
        <!-- add by Tomcat.Hui 2019/11/5 16:44 .Desc:VDERP-1325 分批开票 . start -->
        <result column="APPLY_AMOUNT" property="applyAmount" jdbcType="DECIMAL" />
        <!-- add by Tomcat.Hui 2019/11/5 16:44 .Desc:VDERP-1325 分批开票 . end -->
        <result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
        <result column="WMS_ORDER_NO" property="wmsOrderNo" jdbcType="VARCHAR" />
        <result column="ERP_ORDER_NO" property="erpOrderNo" jdbcType="VARCHAR" />
        <result column="IS_SEND_WMS" property="isSendWms" jdbcType="BIT" />

        <result column="SIGNER_ID" property="signerId" jdbcType="INTEGER" />
        <result column="SIGNER_NAME" property="signerName" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="ResultMap" type="com.vedeng.finance.model.InvoiceApply">
        <id column="INVOICE_APPLY_ID" property="invoiceApplyId" jdbcType="INTEGER" />
        <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
        <result column="SALEORDER_NO" property="saleorderNo" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
        INVOICE_APPLY_ID, COMPANY_ID, <!-- INVOICE_PROPERTY, --> TYPE, RELATED_ID, IS_AUTO, IS_ADVANCE, ADVANCE_VALID_STATUS, ADVANCE_VALID_USERID,
        ADVANCE_VALID_TIME, ADVANCE_VALID_COMMENTS, VALID_STATUS, VALID_TIME,
        VALID_COMMENTS, INVOICE_ST_STATUS, INVOICE_PRINT_STATUS, ADD_TIME,APPLY_METHOD,
        CREATOR, MOD_TIME, UPDATER,EXPRESS_ID,WMS_ORDER_NO,ERP_ORDER_NO,IS_SEND_WMS,
        SIGNER_ID,SIGNER_NAME
    </sql>
    <insert id="insert" parameterType="com.vedeng.finance.model.InvoiceApply" useGeneratedKeys="true" keyProperty="invoiceApplyId">
        insert into T_INVOICE_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceApplyId != null">
                INVOICE_APPLY_ID,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
            <!-- <if test="invoiceProperty != null" >
                INVOICE_PROPERTY,
            </if> -->
            <if test="type != null">
                TYPE,
            </if>
            <if test="applyMethod != null">
                APPLY_METHOD,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="isAuto != null">
                IS_AUTO,
            </if>
            <if test="isAdvance != null">
                IS_ADVANCE,
            </if>
            <if test="advanceValidStatus != null">
                ADVANCE_VALID_STATUS,
            </if>
            <if test="advanceValidTime != null">
                ADVANCE_VALID_TIME,
            </if>
            <if test="advanceValidComments != null">
                ADVANCE_VALID_COMMENTS,
            </if>
            <if test="validStatus != null">
                VALID_STATUS,
            </if>
            <if test="validTime != null">
                VALID_TIME,
            </if>
            <if test="validComments != null">
                VALID_COMMENTS,
            </if>
            <if test="invoiceStStatus != null">
                INVOICE_ST_STATUS,
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="yyValidStatus != null">
                YY_VALID_STATUS,
            </if>
            <if test="expressId != null">
                EXPRESS_ID,
            </if>
            <if test="wmsOrderNo != null">
                WMS_ORDER_NO,
            </if>
            <if test="erpOrderNo != null">
                ERP_ORDER_NO,
            </if>
            <if test="reasons != null">
                REASONS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceApplyId != null">
                #{invoiceApplyId,jdbcType=INTEGER},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=INTEGER},
            </if>
            <!-- <if test="invoiceProperty != null" >
                 #{invoiceProperty,jdbcType=INTEGER},
            </if> -->
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>

            <if test="applyMethod != null">
                #{applyMethod,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="isAuto != null">
                #{isAuto,jdbcType=BIT},
            </if>
            <if test="isAdvance != null">
                #{isAdvance,jdbcType=BIT},
            </if>
            <if test="advanceValidStatus != null">
                #{advanceValidStatus,jdbcType=BIT},
            </if>
            <if test="advanceValidTime != null">
                #{advanceValidTime,jdbcType=BIGINT},
            </if>
            <if test="advanceValidComments != null">
                #{advanceValidComments,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=BIT},
            </if>
            <if test="validTime != null">
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null">
                #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStStatus != null">
                #{invoiceStStatus,jdbcType=BIT},
            </if>
            <if test="invoicePrintStatus != null">
                #{invoicePrintStatus,jdbcType=BIT},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="yyValidStatus != null">
                #{yyValidStatus,jdbcType=BIT},
            </if>
            <if test="expressId != null">
                #{expressId,jdbcType=INTEGER},
            </if>
            <if test="wmsOrderNo != null">
                #{wmsOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="erpOrderNo != null">
                #{erpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="reasons != null">
                #{reasons,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from T_INVOICE_APPLY
        where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </select>
    <update id="update" parameterType="com.vedeng.finance.model.InvoiceApply">
        update T_INVOICE_APPLY
        <set>
            <if test="companyId != null" >
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                TYPE = #{type,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="isAuto != null">
                IS_AUTO = #{isAuto,jdbcType=BIT},
            </if>
            <if test="isAdvance != null">
                IS_ADVANCE = #{isAdvance,jdbcType=BIT},
            </if>
            <if test="advanceValidStatus != null">
                ADVANCE_VALID_STATUS = #{advanceValidStatus,jdbcType=BIT},
            </if>
            <if test="advanceValidTime != null">
                ADVANCE_VALID_TIME = #{advanceValidTime,jdbcType=BIGINT},
            </if>
            <if test="yyValidTime !=null ">
                YY_VALID_TIME = #{yyValidTime,jdbcType=BIGINT},
            </if>
            <if test="yyValidUserid !=null ">
                YY_VALID_USERID = #{yyValidUserid,jdbcType=INTEGER},
            </if>
            <if test="advanceValidComments != null">
                ADVANCE_VALID_COMMENTS = #{advanceValidComments,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null">
                VALID_STATUS = #{validStatus,jdbcType=BIT},
            </if>
            <if test="validUserId != null">
                VALID_USERID = #{validUserId,jdbcType=INTEGER},
            </if>
            <if test="validTime != null">
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null">
                VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStStatus != null">
                INVOICE_ST_STATUS = #{invoiceStStatus,jdbcType=BIT},
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </update>

    <select id="getInvoiceApplyByExpressId" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" /> FROM T_INVOICE_APPLY A WHERE A.EXPRESS_ID =  #{expressId,jdbcType=INTEGER}
    </select>

    <select id="getInvoiceApplyByExpressIdFaile" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" /> FROM T_INVOICE_APPLY A WHERE A.EXPRESS_ID =  #{expressId,jdbcType=INTEGER} AND A.VALID_STATUS <![CDATA[ <> ]]> 2
    </select>

    <!--查询所有开票申请-->
    <select id="getAllSaleInvoiceApplyList" resultMap="ResultMap">
        SELECT
            A.INVOICE_APPLY_ID,
            A.RELATED_ID,
            B.SALEORDER_NO,
            B.TRADER_ID,
            B.TRADER_NAME,
            B.ORG_ID,
            B.INVOICE_TYPE,
            C.TITLE AS invoiceTypeStr,
            A.IS_AUTO,
            A.IS_ADVANCE,
            A.ADVANCE_VALID_STATUS,
            A.ADVANCE_VALID_TIME,
            A.ADVANCE_VALID_COMMENTS,
            A.VALID_STATUS,
            A.VALID_USERID,
            A.VALID_TIME,
            ABS(B.TOTAL_AMOUNT) - ABS(IFNULL(D.AMOUNT, 0)) AS TOTAL_AMOUNT,
            B.PAYMENT_STATUS,
            B.DELIVERY_STATUS,
            B.ARRIVAL_STATUS,
            B.ARRIVAL_TIME,
            B.SERVICE_STATUS,
            E.CUSTOMER_LEVEL,
            E.IS_COLLECT,
            A.IS_SIGN,
            A.CREATOR,
            A.ADD_TIME,
            A.UPDATER,
            A.MOD_TIME,
            B.INVOICE_COMMENTS,
            A.APPLY_METHOD,
            SUM(DE.TOTAL_AMOUNT) AS APPLY_AMOUNT
        FROM
            T_INVOICE_APPLY A
                INNER JOIN T_SALEORDER B ON A.RELATED_ID = B.SALEORDER_ID
                LEFT JOIN T_TRADER_CUSTOMER E ON B.TRADER_ID = E.TRADER_ID
                AND E.IS_ENABLE = 1
                LEFT JOIN T_SYS_OPTION_DEFINITION C ON B.INVOICE_TYPE = C.SYS_OPTION_DEFINITION_ID
                AND C. STATUS = 1
                AND C.PARENT_ID = 428
                LEFT JOIN (
                SELECT
                    SUM(N.SKU_REFUND_AMOUNT) AS AMOUNT,
                    M.ORDER_ID
                FROM
                    T_AFTER_SALES M
                        LEFT JOIN T_AFTER_SALES_GOODS N ON M.AFTER_SALES_ID = N.AFTER_SALES_ID
                WHERE
                    M.TYPE = 539
                  AND M.SUBJECT_TYPE = 535
                  AND M.VALID_STATUS = 1
                  AND M.ATFER_SALES_STATUS IN (1, 2)
                GROUP BY
                    M.ORDER_ID
            ) D ON D.ORDER_ID = B.SALEORDER_ID
                LEFT JOIN T_INVOICE_APPLY_DETAIL DE ON DE.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
                LEFT JOIN T_INVOICE TI ON TI.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
                LEFT JOIN T_INVOICE_DETAIL TD ON TD.INVOICE_ID = TI.INVOICE_ID
        WHERE
            B.INVOICE_STATUS <![CDATA[ <> ]]> 2
          AND (
                    A.APPLY_METHOD <![CDATA[ <> ]]> 2
                OR A.APPLY_METHOD IS NULL
            )
          AND A.COMPANY_ID = 1
          AND A.TYPE = 505
          AND A.YY_VALID_STATUS = 1
          AND A.VALID_STATUS = 0
          AND (
                (A.IS_ADVANCE = 0)
                OR (
                            A.IS_ADVANCE = 1
                        AND A.ADVANCE_VALID_STATUS = 1
                    )
            )
        GROUP BY
            A.INVOICE_APPLY_ID
        HAVING
            SUM(IFNULL(TD.TOTAL_AMOUNT, 0)) = 0
        ORDER BY
            E.IS_COLLECT DESC,

            IF (
                        A.CREATOR = 0,
                        B.ARRIVAL_TIME,
                        A.ADD_TIME
                ) ASC
    </select>
    <!--根据订单id查找开票申请-->
    <select id="getInvoiceApplyIdsBySaleOrderIds" resultType="java.lang.Integer">
        select distinct INVOICE_APPLY_ID from T_INVOICE_APPLY INNER JOIN T_SALEORDER ON T_INVOICE_APPLY.RELATED_ID = T_SALEORDER.SALEORDER_ID where SALEORDER_NO in
        <foreach collection="saleOrderNoList" item="saleorderNo" open="(" close=")" separator=",">
            #{saleorderNo}
        </foreach>
    </select>
    <!--改变标记状态-->
    <update id="changeIsSign">
        update T_INVOICE_APPLY set IS_SIGN=1  where INVOICE_APPLY_ID in
        <foreach collection="endInvoiceApplyIds" item="endInvoiceApplyId" open="(" close=")" separator=",">
            #{endInvoiceApplyId,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="updateInvoiceApplyReasons" parameterType="com.vedeng.finance.model.InvoiceApply">
        UPDATE T_INVOICE_APPLY
        SET REASONS = #{reasons,jdbcType=VARCHAR}
        WHERE
            INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </update>

    <select id="getInvoiceAppliesByGoodsPeer" resultMap="BaseResultMap">
        SELECT
            T2.INVOICE_APPLY_ID,T2.RELATED_ID,T2.WMS_ORDER_NO,T2.ERP_ORDER_NO
        FROM
            T_SALEORDER T1
                LEFT JOIN T_INVOICE_APPLY T2 ON T1.SALEORDER_ID = T2.RELATED_ID
                AND T2.INVOICE_ST_STATUS = 1
        WHERE
            T1.ORDER_TYPE = 5
          AND T1.INVOICE_METHOD = 3
          AND T1.IS_SEND_INVOICE = 1
          AND T1.IS_SAME_ADDRESS = 1
          AND T2.TYPE = 505
        GROUP BY
            T2.INVOICE_APPLY_ID
        ORDER BY
            T2.ADD_TIME DESC
    </select>

    <select id="getInvoiceApplyInfoByInvoiceId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        SELECT
            T1.INVOICE_APPLY_ID,
            T1.APPLY_METHOD,
            T1.TYPE,
            T1.WMS_ORDER_NO,
            T1.ERP_ORDER_NO,
            T1.IS_SEND_WMS
        FROM
            T_INVOICE_APPLY T1
                LEFT JOIN T_INVOICE T2 ON T1.INVOICE_APPLY_ID = T2.INVOICE_APPLY_ID
        WHERE
            T2.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
    </select>

    <update id="signIsSendWmsFlag">
        UPDATE T_INVOICE_APPLY
        SET IS_SEND_WMS = 1
        WHERE
            INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </update>

    <update id="updateUrageByOrderNumber">
        UPDATE T_INVOICE_APPLY
        SET URAGE = #{urage,jdbcType=INTEGER}
        WHERE
            RELATED_ID = #{orderId,jdbcType=VARCHAR}
    </update>

    <select id="getInvoiceAppliesBySaleorderId" resultType="com.vedeng.finance.model.InvoiceApplyDetail">
        SELECT
            T3.INVOICE_APPLY_DETAIL_ID,
            T3.INVOICE_APPLY_ID,
            T3.DETAILGOODS_ID,
            T3.NUM
        FROM
            T_SALEORDER T1
                INNER JOIN T_INVOICE_APPLY T2 ON T1.SALEORDER_ID = T2.RELATED_ID
                INNER JOIN T_INVOICE_APPLY_DETAIL T3 ON T2.INVOICE_APPLY_ID = T3.INVOICE_APPLY_ID
        WHERE
            T1.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
          AND T2.VALID_STATUS = 1
    </select>

    <select id="getAftersaleInvoiceApplyByRelatedId" parameterType="java.lang.Integer" resultType="com.vedeng.finance.model.InvoiceApply">
        SELECT A.*
        FROM T_INVOICE_APPLY A
        WHERE A.TYPE = 504 AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        ORDER BY A.ADD_TIME DESC LIMIT 1
    </select>

    <update id="updateInvoiceProperty">
        update T_INVOICE_APPLY
        set INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER},
            IS_AUTO          = #{isAuto,jdbcType=INTEGER}
        where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </update>

    <insert id="insertApplyReasonSnapshot" keyColumn="INVOICE_APPLY_REASON_ID" keyProperty="invoiceApplyReasonId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity" useGeneratedKeys="true">
        insert into T_INVOICE_APPLY_REASON_SNAPSHOT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceApplyId != null">
                INVOICE_APPLY_ID,
            </if>
            <if test="ruleCode != null and ruleCode != ''">
                RULE_CODE,
            </if>
            <if test="ruleName != null and ruleName != ''">
                RULE_NAME,
            </if>
            <if test="ruleContent != null and ruleContent != ''">
                RULE_CONTENT,
            </if>
            <if test="promptText != null and promptText != ''">
                PROMPT_TEXT,
            </if>
            <if test="applyReason != null and applyReason != ''">
                APPLY_REASON,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                UPDATE_REMARK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceApplyId != null">
                #{invoiceApplyId,jdbcType=INTEGER},
            </if>
            <if test="ruleCode != null and ruleCode != ''">
                #{ruleCode,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null and ruleName != ''">
                #{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="ruleContent != null and ruleContent != ''">
                #{ruleContent,jdbcType=VARCHAR},
            </if>
            <if test="promptText != null and promptText != ''">
                #{promptText,jdbcType=VARCHAR},
            </if>
            <if test="applyReason != null and applyReason != ''">
                #{applyReason,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                #{updateRemark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>