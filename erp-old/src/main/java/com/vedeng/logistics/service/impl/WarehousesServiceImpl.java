package com.vedeng.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.CompanyMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Company;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.dao.*;
import com.vedeng.logistics.eums.WarehouseGoodsInEnum;
import com.vedeng.logistics.model.*;
import com.vedeng.logistics.model.outIn.OutInBaseInfo;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.logistics.service.AfterSalesService;
import com.vedeng.logistics.service.WarehousesService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.RegionService;
import com.wms.constant.WmsInputOrderTypeConstant;
import com.wms.constant.WmsOutputOrderTypeConstant;
import com.wms.dao.WmsInputOrderMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.model.po.WmsInputOrder;
import com.wms.model.po.WmsOutputOrder;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderItemMapper;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("warehousesService")
public class WarehousesServiceImpl extends BaseServiceimpl implements WarehousesService {
	public static Logger logger = LoggerFactory.getLogger(WarehousesServiceImpl.class);

	@Autowired
	@Qualifier("companyMapper")
	private CompanyMapper companyMapper;

	@Autowired
	@Qualifier("regionService")
	private RegionService regionService;

	@Autowired
	private BarcodeMapper barcodeMapper;


	@Resource
	private AfterSalesService afterSalesService;

	@Autowired
	private com.vedeng.aftersales.service.AfterSalesService afterSalesOrderService;

	@Autowired
	@Qualifier("warehouseGoodsStatusMapper")
	private WarehouseGoodsStatusMapper warehouseGoodsStatusMapper;

	@Resource
	private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

	@Resource
	private BuyorderGoodsMapper buyorderGoodsMapper;

	@Resource
	private WmsOutputOrderMapper wmsOutputOrderMapper;

	@Resource
	private WmsInputOrderMapper wmsInputOrderMapper;

	@Autowired
	private VgoodsService vgoodsService;

	@Autowired
	private RedisUtils redisUtils;

	@Resource
	private SaleorderGoodsMapper saleorderGoodsMapper;

	@Resource
	private WarehouseGoodsOutInMapper warehouseGoodsOutInMapper;

	@Resource
	private BuyorderMapper buyorderMapper;

	@Resource
	private SaleorderMapper saleorderMapper;

	@Resource
	private SysOptionDefinitionMapper sysOptionDefinitionMapper;

	@Resource
	private UserMapper userMapper;

	@Resource
	private AfterSalesMapper afterSalesMapper;

	@Resource
	private LogisticsMapper logisticsMapper;

	@Autowired
	private GoodsMapper goodsMapper;

	@Autowired
	private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;

	@Autowired
	private WmsUnitConversionOrderItemMapper wmsUnitConversionOrderItemMapper;


	@Override
	public Map<String, Object> getWarehouseList(Warehouse warehouses, Page page) {
		List<Warehouse> list = null;
		Map<String,Object> map = new HashMap<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<Warehouse>>> TypeRef = new TypeReference<ResultInfo<List<Warehouse>>>() {};
		String url=httpUrl + "warehouses/getwarehouseslistpage.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url,warehouses,clientId,clientKey, TypeRef,page);
			list = (List<Warehouse>) result.getData();
			for(int i=0;i<list.size();i++){
				//数据处理(地区)
				Integer areaId = list.get(i).getAreaId();
				if(areaId > 0){
					String region = (String) regionService.getRegion(areaId, 2);
					list.get(i).setAreaName(region);
				}
			}
			page = result.getPage();
			map.put("list", list);
			map.put("page", page);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return map;
	}

	@Override
	public Company getCompanyName(int companyId) {
		return companyMapper.selectByPrimaryKey(companyId);
	}

	@Override
	public Warehouse getWarehouseByName(Warehouse warehouses, HttpSession session) {
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		warehouses.setCompanyId(user.getCompanyId());
		// 接口调用
		String url = httpUrl + "warehouses/getwarehousebyname.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Warehouse>> TypeRef2 = new TypeReference<ResultInfo<Warehouse>>() {};
		try {
			ResultInfo<Warehouse> result2 = (ResultInfo<Warehouse>) HttpClientUtils.post(url, warehouses, clientId, clientKey,TypeRef2);
			if (null == result2) {
				return null;
			}
			Warehouse res = (Warehouse) result2.getData();
			if(null != res){
				//数据处理(地区)
				Integer areaId = res.getAreaId();
				if(areaId > 0){
					String region = (String) regionService.getRegion(areaId, 2);
					res.setAreaName(region);
				}
			}
			return res;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public ResultInfo saveWarehouse(Warehouse warehouses, HttpServletRequest request, HttpSession session) {
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();
		//仓库的基本信息
		warehouses.setCompanyId(user.getCompanyId());
		warehouses.setIsEnable(ErpConst.ONE);
		if(Integer.parseInt(request.getParameter("zone")) > 0){
			warehouses.setAreaId(Integer.parseInt(request.getParameter("zone")));
			warehouses.setAreaIds(request.getParameter("province")+","+request.getParameter("city")+","+request.getParameter("zone"));
		}else{
			warehouses.setAreaId(Integer.parseInt(request.getParameter("city")));
			warehouses.setAreaIds(request.getParameter("province")+","+request.getParameter("city")+","+request.getParameter("zone"));
		}
		warehouses.setAddTime(time);
		warehouses.setCreator(user.getUserId());
		warehouses.setModTime(time);
		warehouses.setUpdater(user.getUserId());
		warehouses.setWarehouseName((String) request.getParameter("warehouseName"));
		warehouses.setAddress((String) request.getParameter("wareAddress"));
		warehouses.setComments((String) request.getParameter("comments"));
		// 接口调用
		String url = httpUrl + "warehouses/savewarehouse.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Warehouse>> TypeRef2 = new TypeReference<ResultInfo<Warehouse>>() {};
		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, warehouses, clientId, clientKey, TypeRef2);
			return result2;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public ResultInfo<?> disableWarehouse(Warehouse warehouses) {

        ResultInfo<?> result = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Warehouse>> TypeRef = new TypeReference<ResultInfo<Warehouse>>() {};
		String url=httpUrl + "warehouses/disablewarehouse.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, warehouses,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public Warehouse getWarehouseById(Warehouse warehouses){
		// 接口调用
		String url = httpUrl + "warehouses/getwarehousebyid.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Warehouse>> TypeRef2 = new TypeReference<ResultInfo<Warehouse>>() {};
		try {
			ResultInfo<Warehouse> result2 = (ResultInfo<Warehouse>) HttpClientUtils.post(url, warehouses, clientId, clientKey,TypeRef2);
			if (null == result2) {
				return null;
			}
			Warehouse res = (Warehouse) result2.getData();
			if(null != res){
				//数据处理(地区)
				Integer areaId = res.getAreaId();
				System.out.println("areaId&&"+areaId);
				if(areaId > 0){
					String region = (String) regionService.getRegion(areaId, 2);
					res.setAreaName(region);
				}
			}
			return res;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public ResultInfo<?> editWarehouse(Warehouse warehouses, HttpServletRequest request, HttpSession session) {
		ResultInfo<?> result = null;
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();
		if(Integer.parseInt(request.getParameter("zone")) > 0){
			warehouses.setAreaId(Integer.parseInt(request.getParameter("zone")));
			warehouses.setAreaIds(request.getParameter("province")+","+request.getParameter("city")+","+request.getParameter("zone"));
		}else{
			warehouses.setAreaId(Integer.parseInt(request.getParameter("city")));
			warehouses.setAreaIds(request.getParameter("province")+","+request.getParameter("city")+","+request.getParameter("zone"));
		}
		warehouses.setCreator(user.getUserId());
		warehouses.setModTime(time);
		warehouses.setUpdater(user.getUserId());
		warehouses.setWarehouseName((String) request.getParameter("warehouseName"));
		warehouses.setAddress((String) request.getParameter("wareAddress"));
		warehouses.setComments((String) request.getParameter("comments"));
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "warehouses/editwarehouse.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, warehouses,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public List<Warehouse> getAllWarehouse(Warehouse warehouses) {
		List<Warehouse> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<Warehouse>>> TypeRef = new TypeReference<ResultInfo<List<Warehouse>>>() {};
		String url=httpUrl + "warehouses/getallwarehouse.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url,warehouses,clientId,clientKey, TypeRef);
			list = (List<Warehouse>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	public Warehouse getGoodsList(Warehouse warehouses) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Warehouse>> TypeRef2 = new TypeReference<ResultInfo<Warehouse>>() {};
		String url=httpUrl + "warehouses/getgoodslist.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url,warehouses,clientId,clientKey, TypeRef2);
			if (null == result) {
				return null;
			}
			Warehouse res = (Warehouse) result.getData();
			return res;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public List<Warehouse> getLsWarehouse(Warehouse warehouses) {
		List<Warehouse> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<Warehouse>>> TypeRef = new TypeReference<ResultInfo<List<Warehouse>>>() {};
		String url=httpUrl + "warehouses/getlswarehouse.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url,warehouses,clientId,clientKey, TypeRef);
			list = (List<Warehouse>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	public Barcode getBarcodeInfoById(Integer barcodeId) {
		Barcode barcode = barcodeMapper.selectByPrimaryKey(barcodeId);
		return barcode;
	}

	@Override
	public List<WarehouseGoodsStatus> getErrorStockGoodsList() {
		List<WarehouseGoodsStatus> list = new ArrayList<>();
		List<WarehouseStock> allGoodsIdList = warehouseGoodsStatusMapper.getAllStockId();
		for (WarehouseStock warehouseStock : allGoodsIdList) {
			//获取老库存表数据
			List<WarehouseGoodsStatus> statuslist = warehouseGoodsStatusMapper.getWarehouseStatusByGoodsId(warehouseStock.getGoodsId());
			//真实库存量
			List<WarehouseGoodsStatus> logList = warehouseGoodsStatusMapper.getReallGoodsStockNumByGoodsId(warehouseStock.getGoodsId());
			//处理判断错误数据
			if(CollectionUtils.isNotEmpty(logList)){
				for (WarehouseGoodsStatus oldInfo : statuslist) {
					for (WarehouseGoodsStatus newInfo : logList) {
						if (isWarehouseGoodsStatusSame(oldInfo, newInfo)) {
							oldInfo.setSameFalg(true);
							if (!oldInfo.getNum().equals(newInfo.getNum())) {
								logger.info("getErrorStockGoods WarehouseGoodsStatusId:{},oldNum:{}", oldInfo.getWarehouseGoodsStatusId(), oldInfo.getNum());
								oldInfo.setNum(newInfo.getNum());
								list.add(oldInfo);
							}
						}
					}
				}
			}
			for (WarehouseGoodsStatus oldInfo : statuslist) {
				if(!oldInfo.getSameFalg()&&oldInfo.getNum() != 0 ){
					logger.info("getErrorStockGoods WarehouseGoodsStatusId:{},oldNum:{}",oldInfo.getWarehouseGoodsStatusId(),oldInfo.getNum());
					oldInfo.setNum(0);
					list.add(oldInfo);
				}
			}
		}
		return list;
	}

	@Override
	public Integer updateStockNumById(List<WarehouseGoodsStatus> StockGoodsList) {
		Integer num = 0;
		if(CollectionUtils.isNotEmpty(StockGoodsList)) {
			for (WarehouseGoodsStatus warehouseGoodsStatus : StockGoodsList) {
			num += warehouseGoodsStatusMapper.updateStockNumById(warehouseGoodsStatus);
			}
		}
		return num;
	}

	private boolean isWarehouseGoodsStatusSame(WarehouseGoodsStatus oldInfo, WarehouseGoodsStatus newInfo) {
		if(!oldInfo.getWarehouseId().equals(newInfo.getWarehouseId())){
			return false;
		}else if(!oldInfo.getStorageRoomId().equals(newInfo.getStorageRoomId())){
			return false;
		}else if(!oldInfo.getStorageAreaId().equals(newInfo.getStorageAreaId())){
			return false;
		}else if(!oldInfo.getStorageLocationId().equals(newInfo.getStorageLocationId())){
			return false;
		}else if(!oldInfo.getStorageRackId().equals(newInfo.getStorageRackId())){
			return false;
		}
		return true;
	}
	@Override
	public void savePriceInfoToWareHouseLog(List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList) {
		logger.info("savePriceInfoToWareHouseLog warehouseGoodsOperateLogList:{}", JSON.toJSONString(warehouseGoodsOperateLogList));
		try {
			if(CollectionUtils.isEmpty(warehouseGoodsOperateLogList)){
				return;
			}
			WarehouseGoodsOperateLog warehouseGoodsOperateLog = warehouseGoodsOperateLogList.get(0);
			Integer operateType = warehouseGoodsOperateLog.getOperateType();
			Integer logType = warehouseGoodsOperateLog.getLogType();
			if(logType.equals(1)){
				return;
			}
			//获取成本数据
			List<WarehouseGoodsOperateLog> list = getPriceInfoLogList(warehouseGoodsOperateLogList, operateType);
			logger.info("savePriceInfoToWareHouseLog 入库成本价:{}",list.toString());
			if(CollectionUtils.isEmpty(list)){
				return;
			}
			for (WarehouseGoodsOperateLog goodsOperateLog : list) {
				warehouseGoodsOperateLogMapper.saveCostPrice(goodsOperateLog);
				warehouseGoodsOperateLogMapper.saveNewCostPrice(goodsOperateLog);
			}
		}catch (Exception e){
			logger.error("savePriceInfoToWareHouseLog error",e);
		}
	}

	private boolean isWarehouseOutBoolean(Integer operateType) {
		return StockOperateTypeConst.WAREHOUSE_OUT.equals(operateType) ||
				StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_OUT.equals(operateType)||
				StockOperateTypeConst.BUYORDER_WAREHOUSE_BACK_OUT.equals(operateType)||
				StockOperateTypeConst.BUYORDER_WAREHOUSE_CHANGE_OUT.equals(operateType)||
				StockOperateTypeConst.LENDOUT_WAREHOUSE_OUT.equals(operateType)||
				StockOperateTypeConst.SCRAPED_WAREHOUSE_OUT.equals(operateType) ||
				StockOperateTypeConst.SCRAPED_WAREHOUSE_OUT_L.equals(operateType)||
				StockOperateTypeConst.SURPLUS_WAREHOUSE_OUT.equals(operateType);
	}


	public List<WarehouseGoodsOperateLog> getPriceInfoLogList(List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList, Integer operateType) {
		List<WarehouseGoodsOperateLog> list = new ArrayList<>();

		if(StockOperateTypeConst.WAREHOUSE_IN.equals(operateType)){
			//采购单
			list = getBuyOrderPriceInfo(warehouseGoodsOperateLogList);

		}else if(StockOperateTypeConst.BUYORDER_WAREHOUSE_CHANGE_IN.equals(operateType)){
			//采购售后换货入库
			list = getBuyOrderChangePriceInfo(warehouseGoodsOperateLogList);

		}else if(StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_IN.equals(operateType) ||
				StockOperateTypeConst.ORDER_WAREHOUSE_BACK_IN.equals(operateType)){
			//销售售后入库
			list = getAfterOrderPriceInfo(warehouseGoodsOperateLogList);

		}else if(StockOperateTypeConst.LENDOUT_WAREHOUSE_IN.equals(operateType)){
			//外借入库
//			list = getLendOutPriceInfo(warehouseGoodsOperateLogList);
			list = getAvgPriceInfo(warehouseGoodsOperateLogList);
			for (WarehouseGoodsOperateLog log : list) {
				log.setCostPrice(log.getNewCostPrice());
			}

		}else if(StockOperateTypeConst.INVENTORY_WAREHOUSE_IN.equals(operateType)){
			//转移入库
			list = getAvgPriceInfo(warehouseGoodsOperateLogList);
			for (WarehouseGoodsOperateLog log : list) {
				log.setCostPrice(log.getNewCostPrice());
			}

		}else if(StockOperateTypeConst.SURPLUS_WAREHOUSE_IN.equals(operateType)){
			//盘盈入库
			list = getAvgPriceInfo(warehouseGoodsOperateLogList);
			for (WarehouseGoodsOperateLog log : list) {
				log.setCostPrice(log.getNewCostPrice());
			}
		} else if (StockOperateTypeConst.UNIT_CONVERSION_IN.equals(operateType)){
			warehouseGoodsOperateLogList.forEach(warehouseGoodsOperateLog -> {
				WmsUnitConversionOrderItem wmsUnitConversionOrderItem = wmsUnitConversionOrderItemMapper.selectByPrimaryKey(warehouseGoodsOperateLog.getRelatedId());
				if (Objects.isNull(wmsUnitConversionOrderItem)){
					logger.error("库存转换单入库商品信息异常 id:{}", warehouseGoodsOperateLog.getRelatedId());
					throw new RuntimeException("库存转换单入库商品信息异常 id:" + warehouseGoodsOperateLog.getRelatedId());
				}
				warehouseGoodsOperateLog.setCostPrice(wmsUnitConversionOrderItem.getTargetPrice());
				warehouseGoodsOperateLog.setNewCostPrice(wmsUnitConversionOrderItem.getTargetPrice());
			});
			return warehouseGoodsOperateLogList;
		}
		return list;
	}


	private List<WarehouseGoodsOperateLog> getLendOutPriceInfo(List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList) {
		HashMap<Integer,Integer> relatedIds = new HashMap<>();
		for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : warehouseGoodsOperateLogList) {
			relatedIds.put(warehouseGoodsOperateLog.getRelatedId(),warehouseGoodsOperateLog.getGoodsId());
		}
		for (Integer relatedId : relatedIds.keySet()) {
			Integer goodsId = relatedIds.get(relatedId);
			//获取外借出库时使用条码入库信息
			List<WarehouseGoodsOperateLog> list = warehouseGoodsOperateLogMapper.getLendOutWhareHouserInList(relatedId,goodsId);
			BigDecimal amount = BigDecimal.ZERO;
			int totalNum = 0;
			List<WarehouseGoodsOperateLog> list2 = new ArrayList<>();
			//通过所有出库使用条码计算平均值
			for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : list) {
				Integer operateType = warehouseGoodsOperateLog.getOperateType();
				list2.add(warehouseGoodsOperateLog);
				List<WarehouseGoodsOperateLog> priceInfoLogList = getPriceInfoLogList(list2, operateType);
				if(CollectionUtils.isNotEmpty(priceInfoLogList)) {
					WarehouseGoodsOperateLog log = priceInfoLogList.get(0);
					amount = amount.add(log.getCostPrice());
					totalNum++;
				}
				list2.clear();

			}
			BigDecimal costPrice = amount.divide(new BigDecimal(totalNum),2, BigDecimal.ROUND_HALF_UP);
			for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : warehouseGoodsOperateLogList) {
				if(warehouseGoodsOperateLog.getRelatedId().equals(relatedId)) {
					logger.info("getLendOutPriceInfo relatedId:{},price:{}",relatedId,costPrice.toString());
					warehouseGoodsOperateLog.setCostPrice(costPrice);
				}
			}
		}
		return warehouseGoodsOperateLogList;
	}

	private List<WarehouseGoodsOperateLog> getAfterOrderPriceInfo(List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList) {
		//获取关联销售单id
		List<WarehouseGoodsOperateLog> result = warehouseGoodsOperateLogMapper.getSaleOrderRelateId(warehouseGoodsOperateLogList);
		Set<Integer> relatedIds = new TreeSet<>();
		HashMap<Integer,BigDecimal> map = new HashMap<>();
		for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : result) {
			Integer relatedId = warehouseGoodsOperateLog.getRelatedId();
			relatedIds.add(relatedId);
		}
		logger.info("getAfterOrderPriceInfo售后单关联销售单id:{}",relatedIds.toString());
		for (Integer relatedId : relatedIds) {
//			Map<String, Object> costPriceMap = vgoodsService.getCostPrice(relatedId);
			BigDecimal buyPrice = saleorderGoodsMapper.getCostPrice(relatedId);
			map.put(relatedId,buyPrice);
		}
		for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : result) {
			BigDecimal price = map.get(warehouseGoodsOperateLog.getRelatedId());
			if(price == null){
				price = BigDecimal.ZERO;
			}
			logger.info("getAfterOrderPriceInfo price:{},relatedId:{}",price.toString(),warehouseGoodsOperateLog.getRelatedId());
			warehouseGoodsOperateLog.setCostPrice(price);
			warehouseGoodsOperateLog.setNewCostPrice(price);
		}
		return result;
	}

	private List<WarehouseGoodsOperateLog> getBuyOrderChangePriceInfo(List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList) {
		List<WarehouseGoodsOperateLog> priceList =  warehouseGoodsOperateLogMapper.getBuyOrderChangePrice(warehouseGoodsOperateLogList);
		return priceList;
	}

	private List<WarehouseGoodsOperateLog> getBuyOrderPriceInfo(List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList) {
		List<WarehouseGoodsOperateLog> priceList = warehouseGoodsOperateLogMapper.getBuyOrderPrice(warehouseGoodsOperateLogList);
		Map<Integer, BigDecimal> pricemap = priceList.stream().collect(Collectors.toMap
				(WarehouseGoodsOperateLog::getRelatedId, WarehouseGoodsOperateLog::getCostPrice));
		for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : warehouseGoodsOperateLogList) {
			BigDecimal cgprice = pricemap.get(warehouseGoodsOperateLog.getRelatedId());
			if(cgprice == null || warehouseGoodsOperateLog.getWarehouseGoodsOperateLogId() == null){
				continue;
			}
			warehouseGoodsOperateLog.setCostPrice(cgprice);
			warehouseGoodsOperateLog.setNewCostPrice(cgprice);
		}
		return warehouseGoodsOperateLogList;
	}

	private List<WarehouseGoodsOperateLog> getAvgPriceInfo(List<WarehouseGoodsOperateLog> list) {
		Map<Integer,BigDecimal> skuPrice = new HashMap<>();
		for (WarehouseGoodsOperateLog log : list) {
			skuPrice.put(log.getGoodsId(),BigDecimal.ZERO);
		}
		//计算近期20个采购单的平均价
		for (Integer goodsId : skuPrice.keySet()) {
			List<BuyorderGoods> buyorderGoodsList =	buyorderGoodsMapper.getAvgPriceByGoodsId(goodsId);
			BigDecimal totalPrice = BigDecimal.ZERO;
			if(CollectionUtils.isNotEmpty(buyorderGoodsList)){
				int num = 0;
				for (BuyorderGoods buyorderGoods : buyorderGoodsList) {
					totalPrice = totalPrice.add(buyorderGoods.getTotalAmount());
					num += buyorderGoods.getNum();
				}
				BigDecimal totalNum = new BigDecimal(num);
				BigDecimal divide = totalPrice.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
				skuPrice.put(goodsId,divide);
			}else{
				skuPrice.put(goodsId,BigDecimal.ZERO);
			}
		}

		for (WarehouseGoodsOperateLog log : list) {
			BigDecimal newCostPrice = skuPrice.get(log.getGoodsId());
			log.setNewCostPrice(newCostPrice);
		}

		return list;
	}

	@Override
	public void updateInLogIsUse(List<WarehouseGoodsOperateLog> list,Integer isUse) {
		try {
			if(!CollectionUtils.isNotEmpty(list)){
				return;
			}
			Integer operateType = list.get(0).getOperateType();
			if(isWarehouseOutBoolean(operateType)){
				if (isUse.equals(0)){
					for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : list) {
						WarehouseGoodsOperateLog outlog = warehouseGoodsOperateLogMapper.getLogByWarehouseGoodsOperateLogId(warehouseGoodsOperateLog);
						warehouseGoodsOperateLog.setNum(-outlog.getNum());
					}
				}
				//出库类型
				for (WarehouseGoodsOperateLog log : list) {
					warehouseGoodsOperateLogMapper.updateOutIsUseAndLastStockNum(log);
				}
//				int i = warehouseGoodsOperateLogMapper.updateIsUse(list,isUse);
			}else if(isUse.equals(1)){
				//入库类型
				warehouseGoodsOperateLogMapper.updateInIsUseAndLastStockNum(list);
			}
		}catch (Exception e){
			logger.error("updateInLogIsUse error",e);
		}
	}

	@Override
	public int updateWarehouseLogIsUse() {
		int j = warehouseGoodsOperateLogMapper.updateBadHistoryLogIsUse();
		//获取使用过的条码
		List<WarehouseGoodsOperateLog> list = warehouseGoodsOperateLogMapper.getIsUseLog();
		int count = 0;
		List<WarehouseGoodsOperateLog> list1 = new ArrayList<>();
		do {
			for (int i = count; i < count+1000; i++) {
				if(i==list.size()){
					break;
				}
				list1.add(list.get(i));
			}
			 j += warehouseGoodsOperateLogMapper.updateIsUse(list1,1);
			count += 1000;
			list1.clear();
		}while (count < list.size());
		return j;
	}

	@Override
	public void saveOutLogPrice(List<WarehouseGoodsOperateLog> outList) {
		for (WarehouseGoodsOperateLog log : outList) {
			if(log.getOperateType().equals(2)) {
				BigDecimal buyPrice = saleorderGoodsMapper.getCostPrice(log.getRelatedId());
				log.setCostPrice(buyPrice);
				log.setNewCostPrice(buyPrice);
			}else{
				List<WarehouseGoodsOperateLog> warehouseInlogList = warehouseGoodsOperateLogMapper.getWarehouseInlog(log);
				if(CollectionUtils.isEmpty(warehouseInlogList)){
					log.setCostPrice(BigDecimal.ZERO);
					log.setNewCostPrice(BigDecimal.ZERO);
				}else{
					WarehouseGoodsOperateLog warehouseGoodsOperateLog = warehouseInlogList.get(0);
					log.setCostPrice(warehouseGoodsOperateLog.getCostPrice());
					log.setNewCostPrice(warehouseGoodsOperateLog.getNewCostPrice());
				}
			}
			warehouseGoodsOperateLogMapper.saveCostPrice(log);
			warehouseGoodsOperateLogMapper.saveNewCostPrice(log);

//			System.out.println("cost : "+ log.getCostPrice() +", new :"+ log.getNewCostPrice());
		}
	}

	@Override
	public OutInBaseInfo getOutInBaseInfo(Long warehouseGoodsOutInId) {
		WarehouseGoodsOutIn warehouseGoodsOutIn = warehouseGoodsOutInMapper.selectByPrimaryKey(warehouseGoodsOutInId);
		OutInBaseInfo outInBaseInfo = new OutInBaseInfo();
		if (Objects.nonNull(warehouseGoodsOutIn)) {
			outInBaseInfo.setSource(warehouseGoodsOutIn.getSource());
			//入库单号
			outInBaseInfo.setOutInNo(warehouseGoodsOutIn.getOutInNo());
			//wms入库单号
			outInBaseInfo.setWmsNo(warehouseGoodsOutIn.getWmsNo());
			//关联单号
			outInBaseInfo.setRelateNo(warehouseGoodsOutIn.getRelateNo());
			//入库单类型
			outInBaseInfo.setOutInType(warehouseGoodsOutIn.getOutInType());
			//入库时间
			outInBaseInfo.setOutInTime(DateUtil.DateToString(warehouseGoodsOutIn.getOutInTime(),DateUtil.TIME_FORMAT));
			//单据主键id
			outInBaseInfo.setWarehouseGoodsOutInId(warehouseGoodsOutIn.getWarehouseGoodsOutInId());
			outInBaseInfo.setIsVirture(warehouseGoodsOutIn.getIsVirture());
			outInBaseInfo.setUpdateRemark(warehouseGoodsOutIn.getUpdateRemark());
			//物流、到货、运费、供应商信息
			if (StringUtils.isNotBlank(warehouseGoodsOutIn.getRelateNo())) {
				//采购入库   采购赠品入库
				if (WarehouseGoodsInEnum.PURCHASE_IN.getErpCode().equals(outInBaseInfo.getOutInType()) || WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode().equals(outInBaseInfo.getOutInType())) {
					Buyorder buyorder = buyorderMapper.selectBuyOrderByRalateNo(warehouseGoodsOutIn.getRelateNo());
					if(Objects.nonNull(buyorder)){
						outInBaseInfo.setValidTime(buyorder.getValidTime());
						fillOutInBaseInfo(outInBaseInfo, buyorder);
						outInBaseInfo.setBuyorderId(buyorder.getBuyorderId());
						if(WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode().equals(outInBaseInfo.getOutInType())){
							outInBaseInfo.setAdditionalClause("");
						}
					}
				} else if (WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN.getErpCode().equals(outInBaseInfo.getOutInType())) {
					//采购换货入库（售后）
					AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(warehouseGoodsOutIn.getRelateNo());
					if(Objects.nonNull(afterSalesByNo)){
						outInBaseInfo.setValidTime(afterSalesByNo.getValidTime());
					}
					if (Objects.nonNull(afterSalesByNo) && Objects.nonNull(afterSalesByNo.getOrderId())) {
						Buyorder buyorder = buyorderMapper.selectBuyOrderByOrderId(afterSalesByNo.getOrderId());
						if(Objects.nonNull(buyorder)){
							fillOutInBaseInfo(outInBaseInfo, buyorder);
							outInBaseInfo.setBuyorderId(afterSalesByNo.getAfterSalesId());
						}
					}
				}else if (WarehouseGoodsInEnum.UNIT_CONVERSION_IN.getErpCode().equals(outInBaseInfo.getOutInType())) {
					// 入库
					WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(outInBaseInfo.getRelateNo());
					if(Objects.nonNull(wmsUnitConversionOrder)){
						outInBaseInfo.setOwner(wmsUnitConversionOrder.getCreatorName());
						outInBaseInfo.setBuyorderId(wmsUnitConversionOrder.getWmsUnitConversionOrderId());
					}

				}else if(WarehouseGoodsInEnum.LENDOUT_WAREHOUSE_IN.getErpCode().equals(outInBaseInfo.getOutInType())){
					//查询外借单
					WmsOutputOrder ouputOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(outInBaseInfo.getRelateNo(), WmsOutputOrderTypeConstant.LENDOUT);
					//单据归属人
					if(Objects.nonNull(ouputOrder)){
						outInBaseInfo.setOwner(ouputOrder.getCreator());
						outInBaseInfo.setBuyorderId(Integer.valueOf(ouputOrder.getId().toString()));
					}
				}else if(WarehouseGoodsInEnum.RECEIVE_WAREHOUSE_OUT.getErpCode().equals(outInBaseInfo.getOutInType())){
					//查询盘盈单
					WmsInputOrder inputOrder = wmsInputOrderMapper.getWmsOutputOrderByOrderNoAndType(outInBaseInfo.getRelateNo(), WmsInputOrderTypeConstant.INPUT);
					//单据归属人
					if (Objects.nonNull(inputOrder) && Objects.nonNull(inputOrder.getCreator())) {
						User user = userMapper.getUserByUserId(inputOrder.getCreator());
						outInBaseInfo.setOwner(user.getUsername());
						outInBaseInfo.setBuyorderId(inputOrder.getWmsInputOrderId());
					}
				}else if(WarehouseGoodsInEnum.ORDER_WAREHOUSE_CHANGE_IN.getErpCode().equals(outInBaseInfo.getOutInType())
						|| WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode().equals(outInBaseInfo.getOutInType())){
					//销售售后换货入库   销售售后退货单
					AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(warehouseGoodsOutIn.getRelateNo());
					if (Objects.nonNull(afterSalesByNo) && Objects.nonNull(afterSalesByNo.getOrderId())) {
						outInBaseInfo.setBuyorderId(afterSalesByNo.getAfterSalesId());
						//单据归属人
						Integer creatorId = afterSalesByNo.getCreator();
						if (Objects.nonNull(creatorId)) {
							User user = userMapper.getUserByUserId(creatorId);
							outInBaseInfo.setOwner(user.getUsername());
						}
						Saleorder saleorder = saleorderMapper.selectSaleOrderByOrderId(afterSalesByNo.getOrderId());
						if (Objects.nonNull(saleorder)) {
							outInBaseInfo.setTraderId(saleorder.getTraderId());
							outInBaseInfo.setTraderName(saleorder.getTraderName());
							outInBaseInfo.setTraderAddressDetail(saleorder.getTakeTraderArea() + saleorder.getTakeTraderAddress());
						}
						if (ErpConst.ONE.equals(outInBaseInfo.getIsVirture())) {
							//虚拟入库单
							outInBaseInfo.setArrivalStatus(2);
						} else {
							AfterSalesVo afterSales = new AfterSalesVo();
							afterSales.setAfterSalesId(afterSalesByNo.getAfterSalesId());
							afterSales.setTraderType(1);
							AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
							afterSalesService.setAfterSalesTHInfo(afterSalesVo);
							List<AfterSalesGoodsVo> normalGoodsList = afterSalesVo.getNormalGoodsList();
							List<AfterSalesGoodsVo> directGoodsList = afterSalesVo.getDirectGoodsList();
							if (!CollectionUtils.isEmpty(normalGoodsList)) {
//							到货状态：
//							全部到货：售后页所有商品状态都为“全部入库/无入库”
//							部分到货：售后页存在一个或多个商品为“未入库”
//							未到货：售后页所有商品都为“未入库”

								//到货状态
								List<AfterSalesGoodsVo> listAll = normalGoodsList.stream().filter(o -> 7 == o.getInStockStatus() || 4 == o.getInStockStatus()).collect(Collectors.toList());
								List<AfterSalesGoodsVo> listZero = normalGoodsList.stream().filter(o -> 5 == o.getInStockStatus()).collect(Collectors.toList());
								if (listAll.size() == normalGoodsList.size()) {
									//全部到货
									outInBaseInfo.setArrivalStatus(2);
								} else if (listZero.size() == normalGoodsList.size()) {
									//未到货
									outInBaseInfo.setArrivalStatus(0);
								} else {
									//部分到货
									outInBaseInfo.setArrivalStatus(1);
								}

							} else if (!CollectionUtils.isEmpty(directGoodsList)) {
//							到货状态：
//							全部到货：售后页所有商品状态都为“全部入库/无入库”
//							部分到货：售后页存在一个或多个商品为“未入库”
//							未到货：售后页所有商品都为“未入库”

								//到货状态
								List<AfterSalesGoodsVo> listAll = directGoodsList.stream().filter(o -> 7 == o.getInStockStatus() || 4 == o.getInStockStatus()).collect(Collectors.toList());
								List<AfterSalesGoodsVo> listZero = directGoodsList.stream().filter(o -> 5 == o.getInStockStatus()).collect(Collectors.toList());
								if (listAll.size() == directGoodsList.size()) {
									//全部到货
									outInBaseInfo.setArrivalStatus(2);
								} else if (listZero.size() == directGoodsList.size()) {
									//未到货
									outInBaseInfo.setArrivalStatus(0);
								} else {
									//部分到货
									outInBaseInfo.setArrivalStatus(1);
								}
							}
						}
					}
				}
			}
		}
		return outInBaseInfo;
	}

	/**
	 * 物流、到货、运费、供应商信息
	 *
	 * @param outInBaseInfo
	 * @param buyorder
	 */
	private void fillOutInBaseInfo(OutInBaseInfo outInBaseInfo, Buyorder buyorder) {
		//到货状态
		outInBaseInfo.setArrivalStatus(buyorder.getArrivalStatus());
		//补充条款
		outInBaseInfo.setAdditionalClause(buyorder.getAdditionalClause());
		//单据归属人
		Integer creatorId = buyorder.getCreator();
		if (Objects.nonNull(creatorId)) {
			User user = userMapper.getUserByUserId(creatorId);
			outInBaseInfo.setOwner(user.getUsername());
		}
		outInBaseInfo.setBuyorderId(buyorder.getBuyorderId());
		outInBaseInfo.setTraderId(buyorder.getTraderId());
		outInBaseInfo.setTraderName(buyorder.getTraderName());
		outInBaseInfo.setTraderAddressDetail(buyorder.getTraderArea()+" "+buyorder.getTraderAddress());
		outInBaseInfo.setIsGift(buyorder.getIsGift());

		//普发
		if("WMS".equals(outInBaseInfo.getSource()) && (WarehouseGoodsInEnum.PURCHASE_IN.getErpCode().equals(outInBaseInfo.getOutInType()) || WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode().equals(outInBaseInfo.getOutInType()))){
			//运费说明
			List<Integer> idList = new ArrayList<>();
			idList.add(buyorder.getFreightDescription());
			List<SysOptionDefinition> definitionList = sysOptionDefinitionMapper.findBySysOptionDefinitionIdIn(idList);
			if (!CollectionUtils.isEmpty(definitionList)) {
				outInBaseInfo.setFreightDescription(definitionList.get(0).getTitle());
			}
			//物流公司
			Logistics logistics = logisticsMapper.getLogisticsById(buyorder.getLogisticsId());
			if (Objects.nonNull(logistics)) {
				outInBaseInfo.setOutInCompany(logistics.getName());
			}
			//物流备注
			outInBaseInfo.setLogisticsComments(buyorder.getLogisticsComments());
		}
		if(!"WMS".equals(outInBaseInfo.getSource())){
			outInBaseInfo.setWmsNo("");
		}
		outInBaseInfo.setIsGift(buyorder.getIsGift());
	}

	@Override
	public List<OutInDetail> getOutInDetailList(Long validTime, String outInNo, String wmsNo, Integer outInType, Integer traderId) {
		List<OutInDetail> outInDetailList;

		outInDetailList = warehouseGoodsOperateLogMapper.getOperateList(outInNo,wmsNo,outInType);

		if(!CollectionUtils.isEmpty(outInDetailList)){
			List<Integer> goodsIdList = outInDetailList.stream().map(OutInDetail::getGoodsId).collect(Collectors.toList());
			List<Map<String, Object>> maps = goodsMapper.skuTipList(goodsIdList);
			List<OutInDetail> finalOutInDetailList = outInDetailList;
			maps.forEach(sku->{
				String sku_id = sku.get("SKU_ID").toString();
				finalOutInDetailList.stream().filter(o->sku_id.equals(o.getGoodsId().toString())).forEach(detail->{
					detail.setSkuId(Integer.valueOf(sku.get("SKU_ID").toString()));
					detail.setSpuId(Integer.valueOf(sku.get("SPU_ID").toString()));
					detail.setSkuNo(sku.get("SKU_NO").toString());
					if(Objects.nonNull(sku.get("SHOW_NAME"))){
						detail.setSkuName(sku.get("SHOW_NAME").toString());
					}
					if(Objects.nonNull(sku.get("BRAND_NAME"))){
						detail.setBrandName(sku.get("BRAND_NAME").toString());
					}
					if(Objects.nonNull(sku.get("SPEC"))){
						detail.setSpec(sku.get("SPEC").toString());
					}
					if(Objects.nonNull(sku.get("MODEL"))){
						detail.setModel(sku.get("MODEL").toString());
					}
					if(Objects.nonNull(sku.get("REGISTRATION_NUMBER"))){
						detail.setRegistrationNumber(sku.get("REGISTRATION_NUMBER").toString());
					}
					if(Objects.nonNull(sku.get("UNIT_NAME"))){
						detail.setUnitName(sku.get("UNIT_NAME").toString());
					}
					if(Objects.nonNull(sku.get("SPU_TYPE"))){
						detail.setSpuType((Integer)sku.get("SPU_TYPE"));
					}
				});
			});
		}
		if(!CollectionUtils.isEmpty(outInDetailList) && (WarehouseGoodsInEnum.PURCHASE_IN.getErpCode().equals(outInType) || WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN.getErpCode().equals(outInType) || WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode().equals(outInType)) || WarehouseGoodsInEnum.ORDER_WAREHOUSE_CHANGE_IN.getErpCode().equals(outInType) || WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode().equals(outInType)){
			outInDetailList.forEach(sku->{
				// 授权类型
				String authType = buyorderMapper.findByTraderSupplyIdAndValidStartTimeBeforeAndValidEndTimeAfter(traderId, validTime, sku.getSkuNo());
				sku.setAuthType(authType);
			});
		}
		return outInDetailList;
	}
}
