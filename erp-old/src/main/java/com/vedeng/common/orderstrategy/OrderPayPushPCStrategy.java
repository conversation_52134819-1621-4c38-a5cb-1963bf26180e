package com.vedeng.common.orderstrategy;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.PCOrderStatusEnum;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.model.WebAccount;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class OrderPayPushPCStrategy extends OrderStrategy {



    @Autowired
    private WebAccountMapper webAccountMapper;
    @Value("${mjx_url}")
    protected String mjxUrl;

    @Resource
    private SaleorderSyncService saleorderSyncService;
    @Override
    public void execute(Object object) {
        //
        Saleorder saleorder = (Saleorder) object;
        if (saleorder == null || saleorder.getOrderType() != 0) {
            return;
        }
        //
        logger.info("pushVSOrder: 付款" + saleorder.getSaleorderNo() + "\t" + saleorder.getTraderContactMobile() + "\t" + saleorder.getSendToPc());
        WebAccount web = webAccountMapper.getWenAccountInfoByMobile(saleorder.getTraderContactMobile());
        //没有推送 或者 已经推送且已经生效且已经关联
//        boolean needToSendToPc = saleorder.getSendToPc() == null || saleorder.getSendToPc() == 1 || web == null
//                &&saleorder.getValidStatus()!=null&&saleorder.getValidStatus()==1;
        if (web==null) {
            logger.info("pushVSOrder: 付款 不需要推送" +  saleorder.getSendToPc() + "\t" + JSON.toJSONString(web) + "\t" +saleorder.getValidStatus());
            return;
        }
        OrderData orderData2=new OrderData();//待确认订单
        orderData2.setSsoAccountId(web.getSsoAccountId());
        orderData2.setTraderId(saleorder.getTraderId());
        orderData2.setCompanyId(saleorder.getTraderId());
        orderData2.setOrderNo(saleorder.getSaleorderNo());
        if (saleorder.getPaymentStatus() != null && saleorder.getPaymentStatus() == 1) {
            orderData2.setOrderStatus(PCOrderStatusEnum.HALF_PAID.status());//7、部分付款
        } else if (saleorder.getPaymentStatus() != null && saleorder.getPaymentStatus() == 0) {
            orderData2.setOrderStatus(PCOrderStatusEnum.PRE_PAY.status());
        }else if(saleorder.getPaymentStatus() != null && saleorder.getPaymentStatus() == 2){
            orderData2.setOrderStatus(PCOrderStatusEnum.PRE_DELIVERY.status());
        }
        orderData2.setAccountId(web.getWebAccountId());

        saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId()
                , com.vedeng.erp.saleorder.enums.PCOrderStatusEnum.get(orderData2.getOrderStatus()),
                SaleorderSyncEnum.PUSH_VS);

    }





}
