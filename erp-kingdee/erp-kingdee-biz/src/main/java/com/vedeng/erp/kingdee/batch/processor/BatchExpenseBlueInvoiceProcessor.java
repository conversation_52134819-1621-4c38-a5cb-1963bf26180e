package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/12 14:28
 */
@Service
@Slf4j
public class BatchExpenseBlueInvoiceProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeOutPutFeePlainAndSpecialInvoiceDto> {

    private static final String SPECIAL_INVOICE = "专用发票";

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeReceiveFeeService kingDeeReceiveFeeService;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Override
    public KingDeeOutPutFeePlainAndSpecialInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        if (StrUtil.isBlank(batchInvoiceDto.getInvoiceNo()) || StrUtil.isBlank(batchInvoiceDto.getInvoiceCode())) {
            log.warn("销售单费用商品蓝票无发票号或者发票代码:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 排除蓝字作废
        List<BatchInvoiceDto> blueDisEnableByInvoiceCodeAndInvoiceNo = batchInvoiceDtoMapper.findBlueDisEnableByInvoiceCodeAndInvoiceNo(batchInvoiceDto.getInvoiceCode(), batchInvoiceDto.getInvoiceNo(), 505);
        if (CollUtil.isNotEmpty(blueDisEnableByInvoiceCodeAndInvoiceNo)) {
            log.info("当前蓝票存在蓝字作废{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 筛选出费用商品的蓝票信息
        List<BatchInvoiceDetailDto> allInvoiceDetailDtoList;
        List<BatchInvoiceDetailDto> expenseInvoiceDetailList;
        if (CollectionUtils.isEmpty(batchInvoiceDto.getBatchInvoiceDetailDtoList())) {
            // 发票明细为空，则表示是非历史数据，直接取数据库查询发票明细
            log.info("开始处理销售费用商品蓝票明细：{}", JSON.toJSONString(batchInvoiceDto));
            allInvoiceDetailDtoList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        } else {
            // 否则，则证明是从Excel中读取的发票明细
            log.info("开始处理21-22历史销售费用商品蓝票明细：{}", JSON.toJSONString(batchInvoiceDto));
            allInvoiceDetailDtoList = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        }
        expenseInvoiceDetailList = allInvoiceDetailDtoList.stream().filter(coreSkuVo -> coreSkuVo.getIsVirtureSku() != null && coreSkuVo.getIsVirtureSku() == 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(expenseInvoiceDetailList)) {
            // 当前发票关联的销售单商品全是实物商品，不符合费用票推送条件，直接返回
            log.info("当前发票关联的销售单商品全是实物商品，不符合费用票推送条件:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 查询推送过的应收单
        List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable = kingDeeReceiveFeeService.getKingDeeReceiveFee(batchInvoiceDto.getInvoiceId().toString());
        if (CollectionUtils.isEmpty(kingDeeSaleReceivable)) {
            log.info("未查询到已推送的费用应收单,无法推送销售单费用商品蓝票信息: {}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        KingDeeOutPutFeePlainAndSpecialInvoiceDto outPutFeePlainAndSpecialInvoiceDto = new KingDeeOutPutFeePlainAndSpecialInvoiceDto();

        if (batchInvoiceDto.getInvoiceTypeName().contains(SPECIAL_INVOICE)) {
            OutPutFeeSpecialInvoiceDto query = new OutPutFeeSpecialInvoiceDto();
            query.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            // 判断是否数据已存在
            boolean old = kingDeeBaseApi.isExist(query);
            if (old) {
                log.info("销售费用蓝票专用发票,金蝶数据已存在:{}", JSON.toJSONString(query));
                return null;
            }

            // 专票
            log.info("开始构造销售费用蓝票专用发票：{}", JSON.toJSONString(batchInvoiceDto));
            outPutFeePlainAndSpecialInvoiceDto.setSpecial(true);
            OutPutFeeSpecialInvoiceDto specialInvoiceDto = new OutPutFeeSpecialInvoiceDto();
            specialInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            specialInvoiceDto.setFid("0");
            specialInvoiceDto.setFRedBlue("0");
            specialInvoiceDto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime())));
            specialInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            specialInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime())));
            specialInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
            specialInvoiceDto.setFcontactunit(batchInvoiceDto.getTraderCustomerId().toString());
            specialInvoiceDto.setFQzokPzgsywdh(batchInvoiceDto.getOrderNo());//VDERP-16094 票对接金蝶增加入参：归属业务单号

            // 二级
            List<OutPutFeeSpecialInvoiceDetailDto> fSaleEntry = new ArrayList<>();
            expenseInvoiceDetailList.forEach(detail -> {
                OutPutFeeSpecialInvoiceDetailDto invoiceDetailDto = new OutPutFeeSpecialInvoiceDetailDto();
                invoiceDetailDto.setFexpenseid(detail.getUnitKingDeeNo());
                invoiceDetailDto.setFpriceqty(detail.getNum().toString());
                invoiceDetailDto.setFauxtaxprice(detail.getTotalAmount().divide(detail.getNum(), 2, RoundingMode.HALF_UP));
                invoiceDetailDto.setFtaxrate(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString());
                invoiceDetailDto.setFQzokBddjhid(detail.getInvoiceDetailId().toString());
                invoiceDetailDto.setFQzokYsddh(batchInvoiceDto.getOrderNo());
                invoiceDetailDto.setFQzokGsywdh(batchInvoiceDto.getOrderNo());
                invoiceDetailDto.setFQzokYwlx("销售");
                invoiceDetailDto.setF_QZOK_WLBM(detail.getSku());

                // 三级
                KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDto = CollUtil.getFirst(kingDeeSaleReceivable);
                KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDtoDetail = kingDeeSaleReceivable.stream()
                        .filter(q -> q.getF_QZOK_BDDJHID().equals(detail.getInvoiceDetailId().toString())).findFirst().orElse(null);
                if (Objects.isNull(kingDeeReceiveQueryResultDtoDetail)) {
                    log.error("无法查询应收单明细单{}", JSON.toJSONString(detail));
                    throw new KingDeeException("未能查到票的无法查询应收单明细单");
                }
                OutPutFeeSpecialInvoiceDetailLinkDto linkDto = new OutPutFeeSpecialInvoiceDetailLinkDto();
                linkDto.setFsaleexinventryLinkFsbillid(kingDeeReceiveQueryResultDto.getFID());
                linkDto.setFsaleexinventryLinkFsid(kingDeeReceiveQueryResultDtoDetail.getFEntityDetail_FEntryId());
                linkDto.setFsaleexinventryLinkFallamountforold(detail.getTotalAmount());
                linkDto.setFsaleexinventryLinkFallamountfor(detail.getTotalAmount());

                List<OutPutFeeSpecialInvoiceDetailLinkDto> detailLinkList = Collections.singletonList(linkDto);
                invoiceDetailDto.setFSALEEXINVENTRY_LINK(detailLinkList);
                fSaleEntry.add(invoiceDetailDto);
            });
            specialInvoiceDto.setFSALEEXINVENTRY(fSaleEntry);
            outPutFeePlainAndSpecialInvoiceDto.setOutPutFeeSpecialInvoiceDto(specialInvoiceDto);
        } else {
            OutPutFeePlainInvoiceDto query = new OutPutFeePlainInvoiceDto();
            query.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            // 判断是否数据已存在
            boolean old = kingDeeBaseApi.isExist(query);
            if (old) {
                log.info("销售费用蓝票普通发票,金蝶数据已存在:{}", JSON.toJSONString(query));
                return null;
            }

            // 普票
            log.info("开始构造销售费用蓝票普通发票：{}", JSON.toJSONString(batchInvoiceDto));
            outPutFeePlainAndSpecialInvoiceDto.setSpecial(false);
            OutPutFeePlainInvoiceDto plainInvoiceDto = new OutPutFeePlainInvoiceDto();
            plainInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            plainInvoiceDto.setFid("0");
            plainInvoiceDto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime())));
            plainInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime())));
            plainInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            plainInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
            plainInvoiceDto.setFcontactunit(batchInvoiceDto.getTraderCustomerId().toString());
            plainInvoiceDto.setFQzokPzgsywdh(batchInvoiceDto.getOrderNo());//VDERP-16094 票对接金蝶增加入参：归属业务单号
            plainInvoiceDto.setFRedBlue("0");
            // 二级
            List<OutPutFeePlainInvoiceDetailDto> plainInvoiceDetailDtoList = new ArrayList<>();
            expenseInvoiceDetailList.forEach(detail -> {
                OutPutFeePlainInvoiceDetailDto tempDetail = new OutPutFeePlainInvoiceDetailDto();
                tempDetail.setFexpenseid(detail.getUnitKingDeeNo());
                tempDetail.setFtaxrate(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString());
                tempDetail.setFpriceqty(detail.getNum().toString());
                tempDetail.setFauxtaxprice(detail.getTotalAmount().divide(detail.getNum(), 2, RoundingMode.HALF_UP));
                tempDetail.setFQzokBddjhid(detail.getInvoiceDetailId().toString());
                tempDetail.setFQzokYsddh(batchInvoiceDto.getOrderNo());
                tempDetail.setFQzokGsywdh(batchInvoiceDto.getOrderNo());
                tempDetail.setFQzokYwlx("销售");
                tempDetail.setF_QZOK_WLBM(detail.getSku());

                // 三级
                KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDto = CollUtil.getFirst(kingDeeSaleReceivable);
                KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDtoDetail = kingDeeSaleReceivable.stream()
                        .filter(q -> q.getF_QZOK_BDDJHID().equals(detail.getInvoiceDetailId().toString())).findFirst().orElse(null);
                if (Objects.isNull(kingDeeReceiveQueryResultDtoDetail)) {
                    log.error("无法查询应收单明细单{}", JSON.toJSONString(detail));
                    throw new KingDeeException("未能查到票的无法查询应收单明细单");
                }
                OutPutFeePlainInvoiceDetailLinkDto linkDto = new OutPutFeePlainInvoiceDetailLinkDto();
                linkDto.setFsaleexinventryLinkFsbillid(kingDeeReceiveQueryResultDto.getFID());
                linkDto.setFsaleexinventryLinkFsid(kingDeeReceiveQueryResultDtoDetail.getFEntityDetail_FEntryId());
                linkDto.setFsaleexinventryLinkFallamountforold(detail.getTotalAmount());
                linkDto.setFsaleexinventryLinkFallamountfor(detail.getTotalAmount());
                List<OutPutFeePlainInvoiceDetailLinkDto> linkDtoList = Collections.singletonList(linkDto);
                tempDetail.setFSALEEXINVENTRY_LINK(linkDtoList);
                plainInvoiceDetailDtoList.add(tempDetail);
            });
            plainInvoiceDto.setFSALEEXINVENTRY(plainInvoiceDetailDtoList);
            outPutFeePlainAndSpecialInvoiceDto.setOutPutFeePlainInvoiceDto(plainInvoiceDto);
        }
        log.info("销售费用蓝票构造成功：{}", JSON.toJSONString(outPutFeePlainAndSpecialInvoiceDto));
        return outPutFeePlainAndSpecialInvoiceDto;
    }
}