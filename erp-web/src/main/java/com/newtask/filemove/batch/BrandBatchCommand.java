package com.newtask.filemove.batch;

import com.google.common.collect.Lists;
import com.newtask.filemove.AbstractFileMoveInterface;
import com.newtask.filemove.dto.CommonConstant;

import com.newtask.filemove.dto.FileMoveDto;
import com.newtask.filemove.theadpool.BrandTask;
import com.newtask.filemove.theadpool.FileMoveThreadPool;
import com.newtask.filemove.theadpool.TraderCertificateFileMoveTask;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.goods.model.Brand;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 品牌的批处理命令
 */
@Service
public class BrandBatchCommand extends AbstractFileMoveInterface {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrandBatchCommand.class);

    @Resource
    private FileMoveDao fileMoveDao;

    protected String getCommandType() {
        return CommonConstant.BATCH;
    }

    protected List<FileMoveDto> getFileMoveInfoByPageSize(int start, int pageSize) {

        List<Brand> brandList = fileMoveDao.getBrandByPage(start,pageSize);

        if(CollectionUtils.isEmpty(brandList)){
            return null;
        }

        return Lists.transform(brandList, (entity) -> {
                                                FileMoveDto fileMoveDto = new FileMoveDto();
                                                fileMoveDto.setId(entity.getBrandId());
                                                fileMoveDto.setDomain(entity.getLogoDomain());
                                                fileMoveDto.setUri(entity.getLogoUri());
                                                return fileMoveDto;
                                            });
    }

    @Override
    protected Runnable getFileMoveTask(List<FileMoveDto> fileMoveDtoList) {
        return new BrandTask(fileMoveDtoList);
    }

    @Override
    protected ExecutorService getExecutorService() {
        return FileMoveThreadPool.getTraderCertificateThreadPool();
    }
}
