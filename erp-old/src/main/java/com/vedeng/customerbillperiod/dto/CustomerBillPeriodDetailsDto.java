package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/23 09 58
 * @Description: 客户账期详情
 */
@Data
public class CustomerBillPeriodDetailsDto {

    private Long billPeriodId;

    /**
     * 账期类型，1正式账期，2临时账期，3订单账期
     */
    private Integer billPeriodType;

    /**
     * 订单账期关联的订单ID
     */
    private Integer relatedOrderId;

    /**
     * 账期申请的额度
     */
    private BigDecimal applyAmount;

    /**
     * 账期有效期开始时间
     */
    private Long billPeriodStart;

    /**
     * 账期有效期截止时间
     */
    private Long billPeriodEnd;


    /**
     * 账期结算周期
     */
    private Integer settlementPeriod;

    /**
     * 剩余可用额度
     */
    private BigDecimal availableAmount;

    /**
     * 未归还账期总额度
     */
    private BigDecimal unreturnedAmount;

    /**
     * 逾期金额
     */
    private BigDecimal overDueAmount;

    /**
     * 使用次数
     */
    private Integer countOfUsed;

    /**
     * 逾期次数
     */
    private Integer countOfOverDue;


}
