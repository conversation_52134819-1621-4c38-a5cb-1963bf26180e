package com.vedeng.order.controller;

import cn.hutool.core.lang.Assert;
import com.vedeng.activiti.model.AssigneeVo;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.dto.AuditRecordDto;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/old/afterBuyorder")
@Slf4j
public class AfterBuyorderVerifyController {

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    private UserService userService;

    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    private AfterSalesService afterSalesService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;
    /**
     * 获取采购仅退票售后单审核状态
     * <AUTHOR>
     * @param afterSalesId
     * @return
     */
    @RequestMapping("/getBuyorderAfterCheckStatus")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> getBuyorderExpenseAfterSalesCheckStatus(Integer afterSalesId) {
        AfterSales as = new AfterSales();
        as.setAfterSalesId(afterSalesId);
        AfterSales afterSales = afterSalesService.selectById(as);
        Map<String, Object> stringObjectMap = this.buyorderAfterCheckStatus(afterSalesId);
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        boolean isOperator = false;
        if (afterSales.getCreator().equals(currentUser.getId())) {
            isOperator = true;
        }

        User user = userService.getUserById(afterSales.getCreator());
        if (currentUser.getId().equals(user.getParentId())) {
            isOperator = true;
        }

        // 所有的审核记录
        List<AuditRecordDto> auditRecordDtos = this.bindAuditRecordData(stringObjectMap);

        boolean isCheckUser = false;
        if (stringObjectMap.get("verifyUsers") != null) {
            String[] verifyUsers = stringObjectMap.get("verifyUsers").toString().split(",");
            for (String verify : verifyUsers) {
                if (verify.contains(currentUser.getUsername())) {
                    isCheckUser = true;
                    break;
                }
            }
        }

        Map<String, Object> result = new HashMap<>(5);
        Task taskInfo = (Task) stringObjectMap.get("taskInfo");
        result.put("taskInfo", taskInfo == null ? 0 : taskInfo.getId());
        result.put("isCheckUser", isCheckUser);
        result.put("isOperator", isOperator);
        result.put("auditRecordDtos",auditRecordDtos);
        return R.success(result);
    }

    private Map<String,Object> buyorderAfterCheckStatus(Integer afterSalesId){
        Map<String, Object> result = new HashMap<>();
        if (afterSalesId == null) {
            return result;
        }

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                "afterSalesVerify" + "_" + afterSalesId);
        result.put("taskInfo", historicInfo.get("taskInfo"));
        result.put("startUser", historicInfo.get("startUser") != null ?
                userService.getRealNameByUserName((historicInfo.get("startUser").toString())) : null);
        result.put("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        result.put("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historicActivityInstance = setAssignRealNames(result, historicInfo);

        boolean permoissionsFlag = false;
        if (historicInfo != null && historicInfo.get("startUser") != null) {
            User startUser = userService.getByUsername(historicInfo.get("startUser").toString(), 1);
            permoissionsFlag = startUser != null ? riskCheckService.isOrgFlag(startUser, ErpConst.QUALITY_ORG) : false;
        }
        result.put("permoissionsFlag", permoissionsFlag);
        result.put("historicActivityInstance", historicActivityInstance);
        Object commentMap = historicInfo.get("commentMap");
        result.put("commentMap", commentMap);
        Task taskInfoPay = (Task) historicInfo.get("taskInfo");
        String verifyUsersPay = null;
        if (null != taskInfoPay) {

            Map candidateUserMap = (Map) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfoPay.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());

                verifyUsersPay = StringUtils.join(userNameList, ",");

            }
        }
        String verifyUsers = getVerifyUserRealNames(verifyUsersPay);
        result.put("verifyUsers", verifyUsers);

        return result;
    }

    /**
     * 仅退票售后订单审核记录封装
     *
     * @param data
     * @return
     */
    private List<AuditRecordDto> bindAuditRecordData(Map<String, Object> data) {
        List<AuditRecordDto> result = new ArrayList<>();

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>) data.get("historicActivityInstance");

        String startUser = (String) data.get("startUser");
        String verifyUsers = (String) data.get("verifyUsers");
        ArrayList<AssigneeVo> assigneeVos = (ArrayList<AssigneeVo>) data.get("assigneeVos");
        Map commentMap = (Map) data.get("commentMap");
        //计算审核流开始节点的个数
        int beginFlag = 0;
        if (CollectionUtils.isNotEmpty(historicActivityInstance)) {
            for (int i = 0; i < historicActivityInstance.size(); i++) {
                HistoricActivityInstance c = historicActivityInstance.get(i);

                // 操作人
                if (StringUtils.isNotEmpty(c.getActivityName())) {
                    AuditRecordDto auditRecordDto = new AuditRecordDto();
                    if ("startEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperator(startUser == null ? "" : startUser);
                    } else if ("intermediateThrowEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperator("");
                    } else {

                        if (Objects.isNull(c.getEndTime())) {
                            auditRecordDto.setOperator(verifyUsers);
                        } else {
                            auditRecordSetOperator(assigneeVos, c, auditRecordDto);
                        }
                    }

                    // 时间
                    auditRecordDto.setOperationTime(c.getEndTime());

                    // 操作
                    if ("startEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperation("开始");
                    } else if ("intermediateThrowEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperation("结束");
                    } else {
                        auditRecordDto.setOperation(c.getActivityName() == null ? "" : c.getActivityName());
                    }

                    // 备注
                    String remark = (String) commentMap.get(c.getTaskId());
                    auditRecordDto.setRemark(remark == null ? "" : remark);
                    result.add(auditRecordDto);
                }
            }
        }
        List<AuditRecordDto> auditRecordDtoList = new ArrayList<>();
        List<AuditRecordDto> startList = result.stream().filter(item -> item.getOperation().equals("开始")).collect(Collectors.toList());
        if(startList.size() > 1){
            int flag = 0;
            for(int i = 0;i < result.size();i++){
                if(result.get(i).getOperation().equals("开始")){
                    flag ++ ;
                }
                if(flag == startList.size()){
                    if(result.get(result.size() - 1).getOperation().equals("驳回")){
                        result.get(result.size() - 1).setOperation("审核不通过");
                    }
                    auditRecordDtoList = result.subList(i, result.size());
                    break;
                }
            }
            return auditRecordDtoList;
        }else {
            if(CollectionUtils.isNotEmpty(result) && result.get(result.size() - 1).getOperation().equals("驳回")){
                result.get(result.size() - 1).setOperation("审核不通过");
            }
            return result;
        }
    }

    private void auditRecordSetOperator(ArrayList<AssigneeVo> assigneeVos, HistoricActivityInstance c, AuditRecordDto auditRecordDto){
        for (AssigneeVo assigneeVo : assigneeVos) {
            if (StringUtils.isNotEmpty(assigneeVo.getAssignee())) {
                if (assigneeVo.getAssignee().equals(c.getAssignee())) {
                    auditRecordDto.setOperator(assigneeVo.getRealName());
                }
            }
        }
        if (StringUtils.isEmpty(auditRecordDto.getOperator())) {
            auditRecordDto.setOperator("");
        }
    }

    /**
     * 设置工作流审核人全称
     *
     * @param result
     * @param historicInfo
     * @return
     */
    protected List<HistoricActivityInstance> setAssignRealNames(Map<String, Object> result, Map<String, Object> historicInfo) {
        ArrayList<AssigneeVo> assigneeVos = new ArrayList<>();

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>) historicInfo
                .get("historicActivityInstance");
        if (CollectionUtils.isNotEmpty(historicActivityInstance)) {
            for (HistoricActivityInstance historicActivityInstanceInfo : historicActivityInstance) {
                if (StringUtil.isBlank(historicActivityInstanceInfo.getAssignee())) {
                    continue;
                }
                AssigneeVo assigneeVo = new AssigneeVo();
                assigneeVo.setAssignee(historicActivityInstanceInfo.getAssignee());
                assigneeVo.setRealName(userService.getRealNameByUserName((historicActivityInstanceInfo.getAssignee())));
                assigneeVos.add(assigneeVo);
            }
        }
        ArrayList<AssigneeVo> assigneeVosResult = assigneeVos.stream()
                .filter(assigneeVo -> StringUtil.isNotBlank(assigneeVo.getAssignee()))
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(AssigneeVo::getAssignee))), ArrayList::new));
        result.put("assigneeVos", assigneeVosResult);
        return historicActivityInstance;
    }

    /**
     * 获取多个审核人员真实姓名
     *
     * @param verifyUsers
     * @return
     */
    protected String getVerifyUserRealNames(String verifyUsers) {
        if (verifyUsers == null) {
            return null;
        }
        List<String> userNames = Arrays.stream(verifyUsers.split(","))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)) {
            return verifyUsers;
        }
        StringBuffer realNames = new StringBuffer();
        userNames.forEach(userName -> {
            realNames.append(userService.getRealNameByUserName(userName)).append(",");
        });
        realNames.deleteCharAt(realNames.length() - 1);
        return realNames.toString();
    }


    @ResponseBody
    @RequestMapping(value = "/editApplyAudit")
    @SystemControllerLog(operationType = "edit", desc = "采购退票售后申请审核")
    @NoNeedAccessAuthorization
    public ResultInfo<?> editApplyAudit(HttpServletRequest request, Integer afterSalesId, String taskId) {
        Assert.notNull(afterSalesId, "采购退票售后发起审核售后单id不可为空");
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        AfterSalesVo as = new AfterSalesVo();
        as.setAfterSalesId(afterSalesId);
        as.setTraderType(ErpConst.TWO);
        as.setCompanyId(ErpConst.ONE);
        AfterSalesVo afterSalesInfo = afterSalesService.getbuyorderAfterSalesVoDetail(as);
        try {
            Map<String, Object> variableMap = new HashMap<String, Object>();

            // 开始生成流程(如果没有taskId表示新流程需要生成)
            if (taskId.equals("0")) {
                variableMap.put("afterSalesInfo", afterSalesInfo);
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "afterSalesVerify");
                variableMap.put("businessKey", "afterSalesVerify_" + afterSalesInfo.getAfterSalesId());
                variableMap.put("relateTableKey", afterSalesInfo.getAfterSalesId());
                variableMap.put("relateTable", "T_AFTER_SALES");
                variableMap.put("orgId", user.getOrgId());
                actionProcdefService.createProcessInstance(request, "afterSalesVerify",
                        "afterSalesVerify_" + afterSalesInfo.getAfterSalesId(), variableMap);
            }
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    "afterSalesVerify_" + afterSalesInfo.getAfterSalesId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                // 设置审核完成监听器回写参数
                variables.put("tableName", "T_AFTER_SALES");
                variables.put("id", "AFTER_SALES_ID");
                variables.put("idValue", afterSalesInfo.getAfterSalesId());
                variables.put("key", "STATUS");
                variables.put("value", 2);
                // 回写数据的表在db中
                variables.put("db", 2);
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }
                // 发起审核时更新售后单审核状态
                actionProcdefService.updateInfo("T_AFTER_SALES", "AFTER_SALES_ID", afterSalesInfo.getAfterSalesId(), "STATUS",
                        1, 2);
            }

            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            log.error("editApplyAudit:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    /**
     * 采购仅退票售后审核
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/complement")
    @NoNeedAccessAuthorization
    public ModelAndView complement(String taskId, Boolean pass, Integer type, Integer afterSalesId) {
        ModelAndView mv = new ModelAndView();

        //防止多个用户点击审核操作，导致空指针的异常，如果查询不到对应的task，则弹出提示框，刷新父页面
        Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            mv.addObject("error_tips", "该采购仅退票售后单审核状态已发生变化，将刷新采购单售后详情页");
            mv.setViewName("order/buyorder/parent_reload");
            return mv;
        }

        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("type", type);
        if (null != afterSalesId) {
            mv.addObject("afterSalesId", afterSalesId);
        } else {
            mv.addObject("afterSalesId", 0);
        }
        mv.setViewName("orderstream/buyorder/complement");
        return mv;
    }

    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/complementAfterSaleTask")
    @SystemControllerLog(operationType = "add", desc = "仅退票采购售后审核操作")
    @NoNeedAccessAuthorization
    public ResultInfo<?> complementAfterSaleTask(HttpServletRequest request, String taskId, String comment, Boolean pass,
                                                 HttpSession session, Integer afterSalesId) {
        // 获取session中user信息
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        ResultInfo backResultInfo = new ResultInfo(0, "操作成功", afterSalesId);
        //审批操作
        try {

            if (!pass) {
                //如果审核不通过
                //获取任务的Service，设置和获取流程变量
                TaskService taskService = processEngine.getTaskService();
                //如果未结束添加审核对应主表的审核状态

                verifiesRecordService.saveVerifiesInfo(taskId, 2);

                //更新主表审核状态
                actionProcdefService.updateInfo("T_AFTER_SALES", "AFTER_SALES_ID", afterSalesId, "STATUS",
                        3, 2);
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
            //如果审核没结束添加审核对应主表的审核状态
            if (!"endEvent".equals(complementStatus.getData())) {
                Integer status = 0;
                if (pass) {
                    //如果审核通过
                    status = 0;
                } else {
                    //如果审核不通过
                    status = 2;
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }
            return backResultInfo;
        } catch (Exception e) {
            log.error(" activity error complementAfterSaleTask:" + afterSalesId, e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }
}
