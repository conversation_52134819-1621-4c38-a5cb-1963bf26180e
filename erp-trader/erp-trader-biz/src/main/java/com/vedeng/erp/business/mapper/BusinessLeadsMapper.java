package com.vedeng.erp.business.mapper;

import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.domain.entity.BusinessLeadsEntity;
import com.vedeng.erp.leads.dto.MergeLeadsDto;
import com.vedeng.erp.system.dto.UserDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface BusinessLeadsMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BusinessLeadsEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BusinessLeadsEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    BusinessLeadsEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BusinessLeadsEntity record);

    int updateByPrimaryKeyListSelective(BusinessLeadsEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BusinessLeadsEntity record);

    /**
     * 批量可选更新
     *
     * @param list the updated record
     * @return update count
     */
    int updateBatchSelective(List<BusinessLeadsEntity> list);

    /**
     * 批量插入
     *
     * @param list object by primary key
     * @return object by primary key
     */
    int batchInsert(List<BusinessLeadsEntity> list);

    /**
     * 根据条件查询
     *
     * @param businessLeadsDto 查询参数
     * @return list object by primary key
     */
    List<BusinessLeadsDto> findByAllToPage(BusinessLeadsDto businessLeadsDto);

    List<BusinessLeadsDto> getLeadsListByDtoToday(BusinessLeadsDto businessLeadsDto);


    /**
     * 查询线索集合
     * @param businessLeadsDto 参数
     * @return list
     */
    List<BusinessLeadsDto> getLeadsListByDto(BusinessLeadsDto businessLeadsDto);

    /**
     * 查询沟通记录关联的线索信息（id 单号）
     *
     * @param list 线索id集合
     * @return id 单号
     */
    List<Map<String, Object>> getCommunicateLeadsInfo(@Param("list") List<Integer> list);

    List<BusinessLeadsEntity> getLeadMergeByParentLeadNo(@Param("leadNo") String leadNo);

    BusinessLeadsEntity findByLeadsNo(@Param("leadsNo") String leadsNo);

    List<BusinessLeadsEntity> findWaitForMergeList(MergeLeadsDto mergeLeadsDto);

    BusinessLeadsEntity selectOneByAll(BusinessLeadsEntity businessLeadsEntity);

    /**
     * 根据条件，查询线索
     * @param businessLeadsDto
     * @return
     */
	List<BusinessLeadsDto> getLeadsListByParams(BusinessLeadsDto businessLeadsDto);

    List<UserDto> findAllCreateUser(@Param("name") String name);

    List<UserDto> findAllBelongUser(@Param("name") String name);

    List<UserDto> findAllShareUser(@Param("name") String name);
    
    BusinessLeadsEntity findByBusinessChanceId(@Param("businessChanceId")Integer businessChanceId);

    List<UserDto> findAllBelongUserForLeads(@Param("name") String name, @Param("allSubordinateUserIdList") List<Integer> allSubordinateUserIdList, @Param("userId") Integer userId);

    List<UserDto> findAllCreatorUserForLeads(@Param("name") String name, @Param("allSubordinateUserIdList") List<Integer> allSubordinateUserIdList, @Param("currentUserId") Integer currentUserId);
}