package com.vedeng.erp.kingdee.batch.chain.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileAppender;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.chain.CommonSaleBackOutInBindMethod;
import com.vedeng.erp.kingdee.batch.chain.SaleBackOutInBindChain;
import com.vedeng.erp.kingdee.batch.chain.context.SaleBackOutInBindContext;
import com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/01/15 15:26
 * @description 第四步销售退货出入库单绑定逻辑：SN、批次号无需对上，绑定无SN、无批次号的出库单商品
 */
@Service
@Slf4j
@Order(4)
public class FourSaleBackOutInBindChain extends CommonSaleBackOutInBindMethod implements SaleBackOutInBindChain {
    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;

    /**
     * 第三次SN和批次号未匹配List
     */
    private List<BatchWarehouseGoodsOutInItemDto> snAndBatchList3;

    /**
     * 匹配后剩余未匹配SN和批次号的出库商品
     */
    private List<BatchWarehouseGoodsOutInItemDto> remainSnBatchOutGoods;

    /**
     * 上一次遍历的入库单单号
     */
    private String outInNo;

    /**
     * 匹配SN和批次号后剩余未匹配的入库商品数量
     */
    private BigDecimal remainSnBatchInNum = BigDecimal.ZERO;

    private Long validTime;

    @Override
    public void handler(SaleBackOutInBindContext context)  {
        setBase(context);

        // 第三次匹配依然没有匹配上SN和批次号的入库单商品
        if (snAndBatchList3.size() > 0) {
            remainSnBatchOutGoods.clear();
            outInNo = null;

            in:
            for (BatchWarehouseGoodsOutInItemDto inGoods : snAndBatchList3) {
                // 有SN的出库商品
                List<BatchWarehouseGoodsOutInItemDto> noSnOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, true, false, validTime);
                // 有批次号的出库商品
                List<BatchWarehouseGoodsOutInItemDto> noBatchOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, false, true, validTime);
                // 有SN和有批次号的出库商品
                List<BatchWarehouseGoodsOutInItemDto> hasBatchOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, true, true, validTime);

                if (CollUtil.isEmpty(noSnOutItems) && CollUtil.isEmpty(noBatchOutItems) && CollUtil.isEmpty(hasBatchOutItems)) {
                    log.error("第四次绑定失败，入库单商品未有对应的出库商品，入库单商品详情：{}", JSON.toJSONString(inGoods));
                    continue;
                }

                // 匹配SN和批次号后剩余未匹配的入库商品数量，不为0时需要继续遍历其他出库单商品
                remainSnBatchInNum = BigDecimal.ZERO;

                // 该入库商品的SKU
                String inSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByIn(inGoods.getOutInNo(), inGoods.getRelatedId());
                // 对应的入库单
                BatchWarehouseGoodsOutInDto oneInOrder = getOutInOrder(inGoods);

                // 第三次遍历后无SN无批次号的入库单商品依然有未匹配上的情况
                List<BatchWarehouseGoodsOutInItemDto> allItem = CollUtil.unionAll(noSnOutItems, noBatchOutItems,hasBatchOutItems);
                if (StringUtils.isNotEmpty(outInNo) && outInNo.equals(inGoods.getOutInNo())) {
                    matchFirst(inGoods, remainSnBatchOutGoods);
                    for (BatchWarehouseGoodsOutInItemDto snBatchOutGoods : remainSnBatchOutGoods) {
                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(snBatchOutGoods.getOutInNo(), snBatchOutGoods.getRelatedId());
                        // SKU匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 对应出库单
                            BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(snBatchOutGoods);

                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), snBatchOutGoods.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                this.bind(3, inGoods.getNum(), snBatchOutGoods.getNum(), bindOutIn, snBatchOutGoods, false);
                                if (inGoods.getNum().compareTo(snBatchOutGoods.getNum()) <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                this.bind(3, remainSnBatchInNum, snBatchOutGoods.getNum(), bindOutIn, snBatchOutGoods, true);
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                }

                outInNo = inGoods.getOutInNo();

                int index = 0;
                matchFirst(inGoods, allItem);
                for (BatchWarehouseGoodsOutInItemDto noSnAndBatchOutItem : allItem) {
                    index++;
                    BigDecimal remainOutNum = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(noSnAndBatchOutItem.getWarehouseGoodsOutInDetailId(), true);
                    // remainOutNum的备份，用于对比remainOutNum数量是否变化，未变化时是所有出库单商品都未匹配上的情况
                    BigDecimal copyRemainOutNum = new BigDecimal(String.valueOf(remainOutNum));
                    if (remainOutNum.compareTo(BigDecimal.ZERO) > 0) {

                        // 根据出库单商品找到出库单
                        BatchWarehouseGoodsOutInDto oneOutOrder = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(noSnAndBatchOutItem.getOutInNo(), 2);

                        if (oneOutOrder == null) {
                            continue;
                        }
                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(noSnAndBatchOutItem.getOutInNo(), noSnAndBatchOutItem.getRelatedId());
                        // SKU匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 基础插入数据
                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(
                                    oneInOrder.getRelateNo(),
                                    inGoods.getRelatedId(),
                                    oneOutOrder.getWarehouseGoodsOutInId(),
                                    noSnAndBatchOutItem.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(),
                                    inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(3, inGoods.getNum(), remainOutNum, bindOutIn, noSnAndBatchOutItem, false);
                                if (inGoods.getNum().compareTo(remainOutNum) <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(3, remainSnBatchInNum, remainOutNum, bindOutIn, noSnAndBatchOutItem, true);
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                    // 一次都没有匹配上，且可用的出库单数量都为0
                    if (remainOutNum.compareTo(BigDecimal.ZERO) == 0 && remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == allItem.size()) {
                        remainSnBatchInNum = inGoods.getNum();
                        log.info("第四次绑定，符合条件的出库单商品数量为0，随后尝试根据商品类型匹配出库单商品：{}", JSON.toJSONString(inGoods));
                    }
                    // 一次都没有匹配上正确的SKU的情况
                    else if (copyRemainOutNum.compareTo(remainOutNum) == 0 && remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == allItem.size()) {
                        remainSnBatchInNum = inGoods.getNum();
                        log.info("第四次绑定，未匹配上正确的SKU：{}", JSON.toJSONString(inGoods));
                    }
                }

                // 第四次遍历后无SN无批次号的入库单商品依然有未匹配上的情况:异常处理
                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) > 0) {
                    log.error("第四次绑定失败，入库单商品：{}，剩余未匹配数量：{}", JSON.toJSONString(inGoods), remainSnBatchInNum);
                }
            }

        }
    }

    @Override
    public void doNext(SaleBackOutInBindContext context) {
    }

    @Override
    public void setBase(SaleBackOutInBindContext context) {
        snAndBatchList3 = context.getSnAndBatchList3();
        remainSnBatchOutGoods = context.getRemainSnBatchOutGoods();
        outInNo = context.getOutInNo();
        validTime = context.getValidTime();
    }

    @Override
    public void bind(int type, BigDecimal inGoodsNum, BigDecimal outGoodsNum, BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn, BatchWarehouseGoodsOutInItemDto outGoods, boolean isRemain) {
        if (inGoodsNum.compareTo(outGoodsNum) == 0) {
            // 数量正好能匹配上
            bindOutIn.setNum(inGoodsNum);
            if (type == 3) {
                if (isRemain) {
                    this.remainSnBatchInNum = BigDecimal.ZERO;
                }
                log.info("第四次绑定，无SN无批次号入库商品匹配无SN无批次号出库商品数量正好，{}", JSON.toJSONString(bindOutIn));
            }
            // 入库小于出库，出库商品多余的数量保存到list中下次使用
        } else if (inGoodsNum.compareTo(outGoodsNum) < 0) {
            bindOutIn.setNum(inGoodsNum);
            BatchWarehouseGoodsOutInItemDto remainGoods = new BatchWarehouseGoodsOutInItemDto();
            BeanUtils.copyProperties(outGoods, remainGoods);
            remainGoods.setNum(outGoodsNum.subtract(inGoodsNum));
            if (type == 3) {
                if (isRemain) {
                    this.remainSnBatchInNum = BigDecimal.ZERO;
                }
                log.info("第四次绑定，无SN无批次号入库商品匹配无SN无批次号出库商品，入库小于出库，{}", JSON.toJSONString(bindOutIn));
            }
            // 入库大于出库，多余的数量保存到list中继续下次遍历
        } else if (inGoodsNum.compareTo(outGoodsNum) > 0) {
            bindOutIn.setNum(outGoodsNum);
            if (type == 3) {
                this.remainSnBatchInNum = inGoodsNum.subtract(outGoodsNum);
                log.info("第四次绑定，无SN无批次号入库商品匹配无SN无批次号出库商品，入库大于出库，{}", JSON.toJSONString(bindOutIn));
            }
        }
        batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.insertSelective(bindOutIn);
    }

}
