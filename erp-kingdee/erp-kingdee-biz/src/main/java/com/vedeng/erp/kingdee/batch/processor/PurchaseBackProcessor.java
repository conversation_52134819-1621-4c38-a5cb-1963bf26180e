package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchSkuSupplyAuthDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseBackQueryResultDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 采购退货出库 处理类
 * @date 2022/11/18 13:55
 */

@Service
@Slf4j
public class PurchaseBackProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeePurchaseBackDto> {

    @Autowired
    private BatchBuyorderDtoMapper buyorderDtoMapper;

    @Autowired
    private BatchSkuSupplyAuthDtoMapper batchSkuSupplyAuthDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;


    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;


    @Override
    public KingDeePurchaseBackDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {

        log.info("PurchaseBackProcessorService.process：{}" , JSON.toJSONString(batchWarehouseGoodsOutInDto));

        log.info("查询是否推送金蝶出库单");
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.PURCHASE_BACK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").compare("=").left("(").right(")").value(batchWarehouseGoodsOutInDto.getOutInNo()).logic("AND").build());
        queryParam.setFilterString(queryFilterDtos);
        List<KingDeePurchaseBackQueryResultDto> query = kingDeeBaseApi.query(queryParam, KingDeePurchaseBackQueryResultDto.class);
        log.info("采购出库单查询金蝶结果：{}",JSON.toJSONString(query));
        if (CollUtil.isNotEmpty(query)) {
            log.info("已推送金蝶数据，无需推送");
            return null;
        }
        // 查询售后单信息
        BatchAfterSalesDto byAfterSalesNoAndSubjectType = batchAfterSalesDtoMapper.findByAfterSalesNoAndSubjectType(batchWarehouseGoodsOutInDto.getRelateNo(), 536);

        if (Objects.isNull(byAfterSalesNoAndSubjectType)) {
            throw new KingDeeException("未查到此售后"+batchWarehouseGoodsOutInDto.getRelateNo()+"信息");
        }
        // 查询供营商 信息 采购单
        BatchBuyorderDto batchBuyorderDto = buyorderDtoMapper.selectByBuyorderNo(byAfterSalesNoAndSubjectType.getOrderNo());
        if (Objects.isNull(batchBuyorderDto)) {
            throw new KingDeeException("未查到此售后的采购单"+byAfterSalesNoAndSubjectType.getOrderNo()+"信息");
        }
        List<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtoList = byAfterSalesNoAndSubjectType.getBatchAfterSalesGoodsDtoList();

        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = StrUtil.isEmpty(batchWarehouseGoodsOutInDto.getRate()) ? "0.00" : decimalFormat.format(new BigDecimal(batchWarehouseGoodsOutInDto.getRate()).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));

        Map<Integer, BatchAfterSalesGoodsDto> batchAfterSalesGoods2Map = batchAfterSalesGoodsDtoList.stream().collect(Collectors.toMap(BatchAfterSalesGoodsDto::getAfterSalesGoodsId, c -> c, (k1, k2) -> k1));
        KingDeePurchaseBackDto dto = new KingDeePurchaseBackDto();
        dto.setFId("0");
        dto.setFBillTypeId("TLD01_SYS");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFStockOrgId(KingDeeConstant.ORG_ID.toString());
        dto.setFSupplierId(batchBuyorderDto.getTraderSupplierId().toString());
        dto.setFMrType("B");
        dto.setFBusinessType("CG");
        dto.setFMrMode("B");


        List<KingDeePurchaseBackDetailDto>  FEntityDetail = new ArrayList<>();
        dto.setFpurmrbentry(FEntityDetail);
        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.warn("未能查到出库单子单信息");
            return null;
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l->{
            BatchAfterSalesGoodsDto batchAfterSalesGoodsDto = batchAfterSalesGoods2Map.get(l.getRelatedId());
            KingDeePurchaseBackDetailDto  kingDeePurchaseBackDetailDto = new KingDeePurchaseBackDetailDto();
            if (Objects.nonNull(batchAfterSalesGoodsDto)) {
                // 授权类型
                String authType = batchSkuSupplyAuthDtoMapper.findByTraderSupplyIdAndValidStartTimeBeforeAndValidEndTimeAfter(batchBuyorderDto.getTraderSupplierId(), batchBuyorderDto.getValidTime(), batchAfterSalesGoodsDto.getSku());
                kingDeePurchaseBackDetailDto.setF_qzok_sqlx(StrUtil.isEmpty(authType)?"":authType);
                kingDeePurchaseBackDetailDto.setFMaterialId(batchAfterSalesGoodsDto.getSku());
                kingDeePurchaseBackDetailDto.setFTaxPrice(batchAfterSalesGoodsDto.getPrice().toString());
                kingDeePurchaseBackDetailDto.setF_qzok_sfzf(batchAfterSalesGoodsDto.getDeliveryDirect().equals(0) ? "否" : "是");

            } else {
                log.error("未查到当前采购售后出库原始商品信息:{}",JSON.toJSONString(l));
                throw new KingDeeException("未查到当前采购售后出库原始商品信息");
            }
            kingDeePurchaseBackDetailDto.setFRmrealqty(l.getNum().abs().toString());
            kingDeePurchaseBackDetailDto.setFStockId("ck9999");
            kingDeePurchaseBackDetailDto.setFEntryTaxRate(taxRate);
            kingDeePurchaseBackDetailDto.setFNote("-");
            kingDeePurchaseBackDetailDto.setF_qzok_ysddh(byAfterSalesNoAndSubjectType.getOrderNo());
            kingDeePurchaseBackDetailDto.setF_qzok_gsywdh(byAfterSalesNoAndSubjectType.getAfterSalesNo());
            kingDeePurchaseBackDetailDto.setF_qzok_ywlx("采购退货");
            kingDeePurchaseBackDetailDto.setF_qzok_pch(l.getBatchNumber());
            kingDeePurchaseBackDetailDto.setF_qzok_xlh(l.getBarcodeFactory());

            kingDeePurchaseBackDetailDto.setF_qzok_bddjhid(l.getWarehouseGoodsOutInDetailId().toString());
            kingDeePurchaseBackDetailDto.setFsourcetype("STK_InStock");
            FEntityDetail.add(kingDeePurchaseBackDetailDto);

        });

        return dto;
    }

}
