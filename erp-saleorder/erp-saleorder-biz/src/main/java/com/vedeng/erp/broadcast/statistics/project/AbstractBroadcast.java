package com.vedeng.erp.broadcast.statistics.project;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.broadcast.domain.dto.BroadcastDeptConfigDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity;
import com.vedeng.erp.broadcast.mapdao.BroadcastContentConfigMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastDeptConfigMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastGlobalConfigMapper;
import com.vedeng.erp.broadcast.mapdao.BroadcastTargetMapper;
import com.vedeng.erp.broadcast.service.BroadcastTarget;
import com.vedeng.erp.broadcast.service.QrMessage;
import com.vedeng.erp.common.broadcast.MessageSubjectEnum;
import com.vedeng.erp.common.broadcast.StatDateRangeEnum;
import com.vedeng.erp.common.broadcast.StatisticsTypeEnum;
import com.vedeng.erp.common.broadcast.config.BroadcastDeptConfigStatistics;
import com.vedeng.erp.common.broadcast.config.BroadcastGlobalConfigStatistics;
import com.vedeng.erp.common.broadcast.config.GlobalConfig;
import com.vedeng.erp.common.broadcast.param.QwMessageParam;
import com.vedeng.erp.common.broadcast.param.TargetOrgAndUser;
import com.vedeng.erp.common.broadcast.param.TimePeriod;

/**
 * 播报项目抽象
 * @ClassName:  AbstractBroadcast   
 * @author: Neil.yang
 * @date:   2025年6月6日 下午3:38:09    
 * @Copyright:
 */
public abstract class AbstractBroadcast {
	
	//日志
	public static Logger LOGGER = LoggerFactory.getLogger(AbstractBroadcast.class);
	
	private BroadcastGlobalConfigMapper broadcastGlobalConfigMapper;
	
	private BroadcastDeptConfigMapper broadcastDeptConfigMapper;
	
	private BroadcastContentConfigMapper broadcastContentConfigMapper;
    
    protected BroadcastTarget broadcastTarget;
    
    protected BroadcastTargetMapper broadcastTargetMapper;
    
    @Value("${broad_qw_message_url}")
    public String qwUrl;
    
    @Value("${broad_show_log:true}")
    public boolean showLog;
    
    private QrMessage qrMessage;
    
    @Autowired
    public final void setBroadcastGlobalConfigMapper(BroadcastGlobalConfigMapper broadcastGlobalConfigMapper) {
        this.broadcastGlobalConfigMapper = broadcastGlobalConfigMapper;
    }
    
    @Autowired
    public final void setBroadcastDeptConfigMapper(BroadcastDeptConfigMapper broadcastDeptConfigMapper) {
        this.broadcastDeptConfigMapper = broadcastDeptConfigMapper;
    }
    
    @Autowired
    public final void setMapper(BroadcastGlobalConfigMapper broadcastGlobalConfigMapper) {
        this.broadcastGlobalConfigMapper = broadcastGlobalConfigMapper;
    }
    
    @Autowired
    public final void setBroadcastContentConfigMapper(BroadcastContentConfigMapper broadcastContentConfigMapper) {
        this.broadcastContentConfigMapper = broadcastContentConfigMapper;
    }
    
    @Autowired
    public final void setBroadcastTarget(BroadcastTarget broadcastTarget) {
        this.broadcastTarget = broadcastTarget;
    }
    
    @Autowired
    public final void setBroadcastTargetMapper(BroadcastTargetMapper broadcastTargetMapper) {
        this.broadcastTargetMapper = broadcastTargetMapper;
    }
    
    @Autowired
    public final void setQrMessage(QrMessage qrMessage) {
        this.qrMessage = qrMessage;
    }
    
    /**
     * 获取消息的主体（用于计算统计范围）
     * @param globalConfig 
     * @return
     */
    public abstract List<MessageSubjectEnum> getMessageSubjectList(@Nullable GlobalConfig globalConfig);


    /**
     * 获取消息展示行数（多于此或者字数超过512返回多个消息）
     * @return 消息展示行数
     */
    public abstract int getLineNum();

    /**
     * 是否存表数据（除自定义播报项目，其他都不存表）
     * @return 是否存表数据
     */
    public abstract boolean isSaveDb();
    
    /**
     * 根据播报项目，获取指定的符合的播报目标
     * @return
     */
    public abstract List<BroadcastDeptConfigStatistics> getBroadcastDeptConfigByProject(List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList);

    /**
     * 子类实现主逻辑（排行数据保存和返回）
     * @param globalConfig 全局配置
     * @param targetOrgAndUserMap 小组、部门关联的erp组织信息
     * @param deptId 播报目标ID(parent==0的数据)
     * @param amountStep 金额梯度（只在个人日到款使用）
     * @param isUserDefine 是否用户自定义播报【1：是；0：否】
     * @param timePeriod 时间区间
     * @param statDateRange 统计的是个人、小组、部门，给自定义播报使用
     * @param isSendQwMessageFlag 是否发送企微消息
     * @return
     */
    public abstract List<QwMessageParam> execute(GlobalConfig globalConfig, Map<MessageSubjectEnum, List<TargetOrgAndUser>> targetOrgAndUserMap,Integer deptId, Integer amountStep,Integer isUserDefine,TimePeriod timePeriod,StatDateRangeEnum statDateRange, boolean isSendQwMessageFlag);
    
    
    /**
     * 获取全局播报配置
     * @return 全局配置列表
     */
    public GlobalConfig getGlobalConfigList(){
    	//获取全局播报设置
    	GlobalConfig globalConfig = new GlobalConfig();
    	//全局配置
    	BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = getGlobalConfig();
    	globalConfig.setBroadcastGlobalConfigStatistics(broadcastGlobalConfigStatistics);
    	//部门配置
    	List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList = getDeptConfig();
    	globalConfig.setBroadcastDeptConfigStatisticsList(broadcastDeptConfigStatisticsList);
    	//图片配置
    	List<BroadcastContentConfigEntity> broadcastContentConfigStatisticsList = getContentConfig();
    	globalConfig.setBroadcastContentConfigStatisticsList(broadcastContentConfigStatisticsList);
    	
    	return globalConfig;
    }


    /**
     * 方法执行入口（模板方法）
     * @param timePeriod 时间区间
     * @param isSendQwMessageFlag 是否发送企微播报
     */
    public void invocation(TimePeriod timePeriod,boolean isSendQwMessageFlag) {
        try {
            //执行各类抽象方法
            GlobalConfig configs = getGlobalConfigList();
            LOGGER.info("获取配置configs信息：{}",JSON.toJSONString(configs));
            //获取播报项目需要发送到哪些播报目标类
            List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList = configs.getBroadcastDeptConfigStatisticsList();
            if(CollectionUtils.isEmpty(broadcastDeptConfigStatisticsList)) {
            	return;
            }
            //获取对应播报项目的播报列表
            broadcastDeptConfigStatisticsList = getBroadcastDeptConfigByProject(broadcastDeptConfigStatisticsList);
            //遍历播报目标配置表，查询哪些播报目标需要进行播报项目的播报
            for (BroadcastDeptConfigStatistics broadcastDeptConfigStatistics : broadcastDeptConfigStatisticsList) {
            	//根据播报项目，获取对应的数据
				Integer deptId = broadcastDeptConfigStatistics.getBroadcastDeptId();
				LOGGER.info("执行播报目标dept_id：{},dept_name:{}",broadcastDeptConfigStatistics.getBroadcastDeptId(),broadcastDeptConfigStatistics.getBroadcastDeptName());
            	//获取播报目标要发送的消息主体，例如播报小群只需要播报个人，播报大群需要播报个人、小组、部门
            	List<MessageSubjectEnum> messageSendEnumList =  broadcastTarget.getMessageSubjectList(deptId);
            	
            	//获取播报项目要统计的类型，例如日播报只需要播报个人，周播报需要播报个人、小组、部门
            	List<MessageSubjectEnum> subjects = getMessageSubjectList(configs);
            	if(CollectionUtils.isEmpty(subjects)) {
            		LOGGER.info("未配置播报项目要统计的类型，跳过...");
            		continue;
            	}
            	//根据消息主体，获取播报目标的业务部门ID列表
            	Map<MessageSubjectEnum,List<TargetOrgAndUser>> targetOrgAndUserMap = new HashMap<>();
            	for (MessageSubjectEnum messageSubjectEnum : subjects) {
            		//消息主体是个人
					if(messageSubjectEnum == MessageSubjectEnum.SALES_SINGLE && messageSendEnumList.contains(MessageSubjectEnum.SALES_SINGLE)) {
						targetOrgAndUserMap.put(MessageSubjectEnum.SALES_SINGLE, broadcastTarget.getOrgIdBySingle(deptId));
					}
					//消息是主体是小组（大群）
					if(messageSubjectEnum == MessageSubjectEnum.SALES_TEAM  && messageSendEnumList.contains(MessageSubjectEnum.SALES_TEAM)) {
						targetOrgAndUserMap.put(MessageSubjectEnum.SALES_TEAM, broadcastTarget.getOrgIdByTeam(deptId));
					}
					//消息是主体是部门（大群）
					if(messageSubjectEnum == MessageSubjectEnum.SALES_DEPT  && messageSendEnumList.contains(MessageSubjectEnum.SALES_DEPT)) {
						targetOrgAndUserMap.put(MessageSubjectEnum.SALES_DEPT, broadcastTarget.getOrgIdByDept(deptId));
					}
				}
            	LOGGER.info("执行播报项目：AbstractBroadcast：{}，播报目标:broadcastTarget：{},统计的范围：{},要发送的主体：{}",this.getClass().getName(),broadcastTarget.getClass().getName(),JSON.toJSONString(subjects),JSON.toJSONString(messageSendEnumList));
            	LOGGER.info("开始执行获取播报排行信息...");
            	Integer amountStep = broadcastTarget.getAmountStep(configs,deptId);
            	List<QwMessageParam> qwMessageParamList = execute(configs,targetOrgAndUserMap,deptId,amountStep,0,timePeriod,null,isSendQwMessageFlag);
            	if(CollectionUtils.isEmpty(qwMessageParamList)) {
            		LOGGER.info("未查询到需要播报的企微信息，直接跳过...");
            		//未查询到需要播报的企微信息，直接跳过
            		continue;
            	}
            	if(isSendQwMessageFlag) {
            		LOGGER.info("开始播报企微信息：{}",JSON.toJSONString(qwMessageParamList));
            		String webHookUrl = broadcastTarget.getWebHook(configs,deptId);
            		qrMessage.sendMessage(qwMessageParamList,webHookUrl);
            	}
			}
        } catch (Exception e) {
            LOGGER.error("到款通知执行失败",e);
        }
    }
    
    
    //获取全局播报配置
    private BroadcastGlobalConfigStatistics getGlobalConfig() {
    	BroadcastGlobalConfigEntity broadcastGlobalConfigEntity = broadcastGlobalConfigMapper.selectGlobalConfig();
    	if(Objects.isNull(broadcastGlobalConfigEntity)) {
    		return null;
    	}
		BroadcastGlobalConfigStatistics broadcastGlobalConfigStatistics = new BroadcastGlobalConfigStatistics();
    	broadcastGlobalConfigStatistics.setBroadcastTitleAed(broadcastGlobalConfigEntity.getBroadcastTitleAed());
    	broadcastGlobalConfigStatistics.setBroadcastTitleCustom(broadcastGlobalConfigEntity.getBroadcastTitleCustom());
    	broadcastGlobalConfigStatistics.setBroadcastTitleDay(broadcastGlobalConfigEntity.getBroadcastTitleDay());
    	broadcastGlobalConfigStatistics.setBroadcastTitleMonth(broadcastGlobalConfigEntity.getBroadcastTitleMonth());
    	broadcastGlobalConfigStatistics.setBroadcastTitleWeek(broadcastGlobalConfigEntity.getBroadcastTitleWeek());
    	broadcastGlobalConfigStatistics.setBroadcastTitleZy(broadcastGlobalConfigEntity.getBroadcastTitleZy());
    	
    	//转化AED-订货号
    	List<String> aedSkuIds = new ArrayList<>();
    	if(!StringUtils.isEmpty(broadcastGlobalConfigEntity.getAedSkuIds())) {
    		aedSkuIds = Arrays.asList(broadcastGlobalConfigEntity.getAedSkuIds().split(","));
    	}
    	broadcastGlobalConfigStatistics.setAedSkuIds(aedSkuIds);
    	
    	//转化排除的销售ID
    	List<String> excludeSaleIds = new ArrayList<>();
    	if(!StringUtils.isEmpty(broadcastGlobalConfigEntity.getExcludeSaleIds())) {
    		excludeSaleIds = Arrays.asList(broadcastGlobalConfigEntity.getExcludeSaleIds().split(","));
    	}
    	broadcastGlobalConfigStatistics.setExcludeSaleIds(excludeSaleIds);
    	
    	//转化客户
    	List<String> excludeTraderIds = new ArrayList<>();
    	if(!StringUtils.isEmpty(broadcastGlobalConfigEntity.getExcludeTraderIds())) {
    		excludeTraderIds = Arrays.asList(broadcastGlobalConfigEntity.getExcludeTraderIds().split(","));
    	}
    	broadcastGlobalConfigStatistics.setExcludeTraderIds(excludeTraderIds);
    	
    	//转化品牌
    	List<String> statBrandIds = new ArrayList<>();
    	if(!StringUtils.isEmpty(broadcastGlobalConfigEntity.getStatBrandIds())) {
    		statBrandIds = Arrays.asList(broadcastGlobalConfigEntity.getStatBrandIds().split(","));
    	}
    	broadcastGlobalConfigStatistics.setStatBrandIds(statBrandIds);
    	//转化分类
    	List<String> statCategoryIds = new ArrayList<>();
    	if(!StringUtils.isEmpty(broadcastGlobalConfigEntity.getStatCategoryIds())) {
    		statCategoryIds = Arrays.asList(broadcastGlobalConfigEntity.getStatCategoryIds().split(","));
    	}
    	broadcastGlobalConfigStatistics.setStatCategoryIds(statCategoryIds);
    	//转化SKU
    	List<String> statSkuIds = new ArrayList<>();
    	if(!StringUtils.isEmpty(broadcastGlobalConfigEntity.getStatSkuIds())) {
    		statSkuIds = Arrays.asList(broadcastGlobalConfigEntity.getStatSkuIds().split(","));
    	}
    	broadcastGlobalConfigStatistics.setStatSkuIds(statSkuIds);
    	
    	broadcastGlobalConfigStatistics.setTopnDept(broadcastGlobalConfigEntity.getTopnDept());
    	broadcastGlobalConfigStatistics.setTopnUser(broadcastGlobalConfigEntity.getTopnUser());
    	//播报对象
    	List<MessageSubjectEnum> messageSubjectList = new ArrayList<>();
    	String statTarget = broadcastGlobalConfigEntity.getStatTarget();
    	if(!StringUtils.isEmpty(statTarget)) {
    		String[] statTargets = statTarget.split(",");
    		for (String target : statTargets) {
				if("1".equals(target)) {
					messageSubjectList.add(MessageSubjectEnum.SALES_SINGLE);
				}
				if("2".equals(target)) {
					messageSubjectList.add(MessageSubjectEnum.SALES_TEAM);
				}
				if("3".equals(target)) {
					messageSubjectList.add(MessageSubjectEnum.SALES_DEPT);
				}
			}
    	}
    	broadcastGlobalConfigStatistics.setMessageSubjectList(messageSubjectList);
    	
    	//播报时间范围
    	Integer statDateRange = broadcastGlobalConfigEntity.getStatDateRange();
    	if(statDateRange == 1) {
    		broadcastGlobalConfigStatistics.setStatDateRange(StatDateRangeEnum.STATISTICS_DAY);
    	}
    	if(statDateRange == 2) {
    		broadcastGlobalConfigStatistics.setStatDateRange(StatDateRangeEnum.STATISTICS_WEEK);
    	}
    	if(statDateRange == 3) {
    		broadcastGlobalConfigStatistics.setStatDateRange(StatDateRangeEnum.STATISTICS_MONTH);
    	}
    	
    	//统计维度
    	Integer statType = broadcastGlobalConfigEntity.getStatType();
    	if(statType == 1) {
    		broadcastGlobalConfigStatistics.setStatisticsType(StatisticsTypeEnum.WAREHOUSE_INCOME);
    	}
    	if(statType == 2) {
    		broadcastGlobalConfigStatistics.setStatisticsType(StatisticsTypeEnum.WAREHOUSE_SALES_NUM);
    	}
    	if(statType == 3) {
    		broadcastGlobalConfigStatistics.setStatisticsType(StatisticsTypeEnum.WAREHOUSE_SALES_VD_AMOUNT);
    	}
		return broadcastGlobalConfigStatistics;
	}
    
    //获取部门配置
    private List<BroadcastDeptConfigStatistics> getDeptConfig() {
    	List<BroadcastDeptConfigStatistics> broadcastDeptConfigStatisticsList = new ArrayList<>();
    	List<BroadcastDeptConfigDto> broadcastDeptConfigDtoList = broadcastDeptConfigMapper.selectAll();
    	for (BroadcastDeptConfigDto broadcastDeptConfigDto : broadcastDeptConfigDtoList) {
    		BroadcastDeptConfigStatistics broadcastDeptConfigStatistics = new BroadcastDeptConfigStatistics();
    		broadcastDeptConfigStatistics.setBroadcastDeptId(broadcastDeptConfigDto.getBroadcastDeptId());
    		broadcastDeptConfigStatistics.setBroadcastDeptName(broadcastDeptConfigDto.getBroadcastDeptName());
    		broadcastDeptConfigStatistics.setDayFlag(broadcastDeptConfigDto.getDayFlag());
    		broadcastDeptConfigStatistics.setWeekFlag(broadcastDeptConfigDto.getWeekFlag());
    		broadcastDeptConfigStatistics.setMonthFlag(broadcastDeptConfigDto.getMonthFlag());
    		broadcastDeptConfigStatistics.setAedFlag(broadcastDeptConfigDto.getAedFlag());
    		broadcastDeptConfigStatistics.setZyFlag(broadcastDeptConfigDto.getZyFlag());
    		broadcastDeptConfigStatistics.setCustomFlag(broadcastDeptConfigDto.getCustomFlag());
    		broadcastDeptConfigStatistics.setAmountStep(broadcastDeptConfigDto.getAmountStep());
    		broadcastDeptConfigStatistics.setWebhook(broadcastDeptConfigDto.getWebhook());
    		broadcastDeptConfigStatisticsList.add(broadcastDeptConfigStatistics);
		}
		return broadcastDeptConfigStatisticsList;
	}
    
    //图片配置
    private  List<BroadcastContentConfigEntity> getContentConfig(){
    	return broadcastContentConfigMapper.selectByCondition(null, null, null, null, null, null, null);
    }
}

