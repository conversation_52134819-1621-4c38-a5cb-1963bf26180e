package com.vedeng.erp.system.common.annotation;

import com.vedeng.erp.system.common.enums.CustomDataLogSaveTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperBizTypeEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义新增数据日志注解
 * 注解的方法需要return新增的数据主键集合
 *
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CustomDataLogAnnotation {

    /**
     * 成功结果模版
     * 格式:成功新增{}条数据 {}：是操作数据条数
     */
    String successTpl() default "成功新增{}条数据";

    /**
     * 失败结果模版
     * 格式:新增{}条数据失败 {}：是操作数据条数
     */
    String failTpl() default "新增{}条数据失败";

    /**
     * 自定义操作类型枚举类
     */
    CustomDataOperBizTypeEnums operBizType();

    /**
     * 插入类型
     */
    CustomDataLogSaveTypeEnums dataLogType();
}
