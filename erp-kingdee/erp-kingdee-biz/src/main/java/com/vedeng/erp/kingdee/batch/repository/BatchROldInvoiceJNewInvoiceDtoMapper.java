package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchROldInvoiceJNewInvoiceDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/12/7 13:33
 **/
public interface BatchROldInvoiceJNewInvoiceDtoMapper {

    /**
     * select by primary key
     * @param rOldInvoiceJNewInvoiceId primary key
     * @return object by primary key
     */
    BatchROldInvoiceJNewInvoiceDto selectByPrimaryKey(Integer rOldInvoiceJNewInvoiceId);

    /**
     * 原始蓝票
     * @param newInvoiceId
     * @return
     */
    List<BatchROldInvoiceJNewInvoiceDto> findByNewInvoiceId(Integer newInvoiceId);

    List<Integer> findByNewInvoiceIds(@Param("list") List<Integer> newInvoiceIds);





}