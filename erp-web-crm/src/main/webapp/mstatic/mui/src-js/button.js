Vue.component('vd-ui-button', {
    template: `<div class="vd-ui-button" :class="[btnClass, {'button-loading': loading}]" @click="handlerClick">
        <slot></slot>
    </div>`,
    props: {
        type: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
        };
    },
    computed: {
        btnClass() {
            return {
                primary: 'button-primary',
                'primary-line': 'button-primary-line',
                danger: 'button-danger'
            }[this.type] || '';
        }
    },
    mounted() {

    },
    methods: {
        handlerClick(e) {
            this.$emit('click', e);
        }
    }
})