package com.vedeng.infrastructure.kingdee.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/19 17:35
 **/
@Data
public class KingDeeFileDataResultDto {

    private KingDeeErrorResult Result;


    @Data
    public static class KingDeeErrorResult{
        private KingDeeErrorResponseStatus ResponseStatus;
        private Integer StartIndex;
        private Boolean IsLast;
        private Integer FileSize;
        private String FileName;
        private String FilePart;
        private String Message;
    }

    @Data
    public static class KingDeeErrorResponseStatus{
        /**
         * 失败code
         */
        private Integer errorCode;
        /**
         * 是否成功，通常为否
         */
        private Boolean isSuccess;
        /**
         * 接口调用失败的异常信息
         */
        private List<KingDeeErrors> errors;
        /**
         * 错误代码MsgCode
         * 0默认
         * 1上下文丢失
         * 2没有权限
         * 3操作标识为空
         * 4异常
         * 5单据标识为空
         * 6数据库操作失败
         * 7许可错误
         * 8参数错误
         * 9指定字段/值不存在
         * 10未找到对应数据
         * 11验证失败
         * 12不可操作
         * 13网控冲突
         */
        private Integer msgCode;
    }

    @Data
    public static class KingDeeErrors{
        /**
         * 字段名称
         */
        private String fieldName;
        /**
         * 错误信息
         */
        private String message;

        private Integer dIndex;
    }
}
