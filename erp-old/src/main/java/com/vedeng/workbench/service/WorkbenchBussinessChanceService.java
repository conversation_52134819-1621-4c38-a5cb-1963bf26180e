package com.vedeng.workbench.service;

import com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto;
import com.vedeng.workbench.model.HeaddataSummaryDto;
import com.vedeng.workbench.model.dto.*;

import java.util.List;

public interface WorkbenchBussinessChanceService {

    /**
     * .
     *
     * @param dataQueryDto
     * @jira: . VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: .获取本周预计成单
     * @version: 1.0.
     * @date: 2020/10/30 8:51.
     * @author: <PERSON><PERSON>.
     * @return: List<WorkbenchChanceInfo>.
     * @throws: .
     */
    List<WorkbenchBussinessChanceExtDto> getThisWeekExpectOrderChanceListById(WorkbenchDataQueryDto dataQueryDto);



    /**
     * .
     *
     * @param dataQueryDto
     * @jira: .
     * @notes: 查询今日待交流商机
     * @version: 1.0.
     * @date: 2020/11/2 10:37.
     * @author: Randy<PERSON><PERSON>.
     * @return: java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>.
     * @throws: .
     */
    List<WorkbenchBussinessChanceExtDto> getTodayToCommunicateBussinessChanceById(WorkbenchDataQueryDto dataQueryDto);


    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 今日新增商机（00：00-24：00）
     * @version: 1.0.
     * @date: 2020/11/2 12:51.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>.
     * @throws:  .
     */
    List<WorkbenchBussinessChanceExtDto> getTodayNewAddBussinessChnaceListById(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 查询主管之下的预计本周成单相关信息
     * @version: 1.0.
     * @date: 2020/11/3 13:43.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.ExpectThisWeekOrderSummaryDto>.
     * @throws:  .
     */
    List<ExpectThisWeekOrderSummaryDto> getExpectThisWeekOrderSummaryDtoListByIds(WorkbenchDataQueryDto dataQueryDto);


    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取主管的本周新增大项目
     * @version: 1.0.
     * @date: 2020/11/3 17:59.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>.
     * @throws:  .
     */
    List<WorkbenchBussinessChanceExtDto> getThisWeekNewAddImportChanceListByIds(WorkbenchDataQueryDto dataQueryDto);


    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取主管的2周未联系商机
     * @version: 1.0.
     * @date: 2020/11/3 19:31.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>.
     * @throws:  .
     */
    List<WorkbenchBussinessChanceExtDto> getTwoDayUncontactChanceListByIds(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取主管的本周核心商品商机丢单
     * @version: 1.0.
     * @date: 2020/11/4 9:03.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>.
     * @throws:  .
     */
    List<WorkbenchBussinessChanceExtDto> getThisWeekCoreGoodfailChanceListByIds(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取本周预计但未到款商机
     * @version: 1.0.
     * @date: 2020/11/4 13:16.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>.
     * @throws:  .
     */
    List<WorkbenchBussinessChanceExtDto> getThisWeekExpectFailChanceListByIds(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获得部门的预计本周成单结果集
     * @version: 1.0.
     * @date: 2020/11/4 16:17.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.ExpectThisWeekOrderSummaryDto>.
     * @throws:  .
     */
    List<ExpectThisWeekOrderSummaryDto> getDeptExpectThisWeekOrderSummaryDtoListByIds(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取部门预警商机总和
     * @version: 1.0.
     * @date: 2020/11/4 17:04.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.WarningBussinessChanceSummaryDto>.
     * @throws:  .
     */
    List<WarningBussinessChanceSummaryDto> getWarningBussinessChanceSummaryDtoList(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取待沟通商机对象for主管
     * @version: 1.0.
     * @date: 2020/11/4 19:30.
     * @author: Randy.Xu.
     * @param
     * @return: java.util.List<com.vedeng.workbench.model.dto.CommunicateSummaryDto>.
     * @throws:  .
     */
    List<CommunicateSummaryDto> getCommunicateBussinessChanceByIds(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取待沟通商机对象for经理或总监
     * @version: 1.0.
     * @date: 2020/11/4 22:02.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.CommunicateSummaryDto>.
     * @throws:  .
     */
    List<CommunicateSummaryDto> getDeptCommunicateBussinessChanceByIds(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取主管数据总览
     * @version: 1.0.
     * @date: 2020/11/5 9:37.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.DataOverviewSummaryDto>.
     * @throws:  .
     */
    List<DataOverviewSummaryDto> getDataOverviewSummaryDtoListByIds(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira:  VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获取经理和总监数据总览
     * @version: 1.0.
     * @date: 2020/11/5 11:04.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.DataOverviewSummaryDto>.
     * @throws:  .
     */
    List<DataOverviewSummaryDto> getDeptDataOverviewSummaryDtoListByIds(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira:  VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 获得表头数据
     * @version: 1.0.
     * @date: 2020/11/5 15:24.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: com.vedeng.workbench.model.HeaddataSummaryDto.
     * @throws:  .
     */
    HeaddataSummaryDto getHeaddataSummaryDto(WorkbenchDataQueryDto dataQueryDto);

    /**
     * VDERP-3073【商机管理中心】ERP销售工作台
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/5 18:23.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto>.
     * @throws:  .
     */
    List<BussinessStatusEchartsDto> getDeptBussinessChanceStatusDtoList(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: 主管下的重点商机
     * @version: 1.0.
     * @date: 2020/11/6 9:28.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.SalersDwhWorkbenchOverviewExtDto>.
     * @throws:  .
     */
    List<SalersDwhWorkbenchOverviewExtDto> getSalersImportantBussinessChances(WorkbenchDataQueryDto dataQueryDto);

    /**
     * .
     * @jira: VDERP-3073【商机管理中心】ERP销售工作台
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/6 10:41.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto>.
     * @throws:  .
     */
    List<DwhWorkbenchOverviewDto> getDeptImportantBussinessChances(WorkbenchDataQueryDto dataQueryDto);


    /**
     * 获取直接下属对象的信息
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/8 5:37.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: java.util.List<com.vedeng.workbench.model.dto.SubObjectDo>.
     * @throws:  .
     */
    List<SubObjectDo> getSubObject(WorkbenchDataQueryDto dataQueryDto);

    /**
     * 获取指定的对象的商机状态分布
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/8 9:11.
     * @author: Randy.Xu.
     * @param null
     * @return: .
     * @throws:  .
     */
    List<BussinessStatusEchartsDto> getBussinessChanceStatus(WorkbenchDataQueryDto dataQueryDto);
}
