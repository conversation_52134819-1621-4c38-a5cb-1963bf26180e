package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchExpressDto;
import com.vedeng.erp.kingdee.batch.dto.BatchGenerateDirectPurchaseOutAndInOrdersDto;
import com.vedeng.erp.kingdee.batch.processor.BatchGenerateDirectPurchaseOutAndInOrdersProcessor;
import com.vedeng.erp.kingdee.batch.writer.BatchGenerateDirectPurchaseOutAndInOrdersWriter;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 生成直发采购出库单和入库单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/10 14:20
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class GenerateDirectPurchaseOutAndInBatchJob extends BaseJob {

    @Autowired
    private BatchGenerateDirectPurchaseOutAndInOrdersProcessor batchGenerateDirectPurchaseOutAndInOrdersProcessor;
    @Autowired
    private BatchGenerateDirectPurchaseOutAndInOrdersWriter batchGenerateDirectPurchaseOutAndInOrdersWriter;


    public Job generateDirectPurchaseOutAndInFlowJob() {
        return jobBuilderFactory.get("generateDirectPurchaseOutAndInFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(generateDirectPurchaseOutAndInOrders())
                .build();
    }

    /**
     * 生成直发采购出库单和入库单
     * @return
     */
    private Step generateDirectPurchaseOutAndInOrders() {
        return stepBuilderFactory.get("生成直发采购出库单和入库单")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchExpressDto, BatchGenerateDirectPurchaseOutAndInOrdersDto>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(generateDirectPurchaseOutAndInOrdersReader(null, null))
                .processor(batchGenerateDirectPurchaseOutAndInOrdersProcessor)
                .writer(batchGenerateDirectPurchaseOutAndInOrdersWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchExpressDto> generateDirectPurchaseOutAndInOrdersReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {

        DateTime now = DateUtil.date();
        BatchExpressDto batchExpressDto = BatchExpressDto
                .builder()
                .isEnable(KingDeeConstant.ONE)
                .businessType(KingDeeConstant.ID_515)
                .arrivalStatus(KingDeeConstant.TWO)
                .beginTime(beginTime == null ? DateUtil.offsetDay(now, -1).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? now.getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchExpressDto.class.getSimpleName(), "queryDirectPurchaseArrivedExpress", batchExpressDto);
    }

}