package com.vedeng.goods.manager;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.goods.manager.rule.GoodsValidTypeEnum;
import com.vedeng.goods.manager.rule.GoodsValidatedRule;
import com.vedeng.goods.service.GoodsCommonService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsValidationRuleLoader implements ConfigChangeListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(GoodsValidationRuleLoader.class);

    private final static List<String> PROP_NAME_KEYS = Arrays.asList(GoodsValidTypeEnum.SPU.getApolloConfigKey(),
            GoodsValidTypeEnum.SKU.getApolloConfigKey());

    private final static GoodsValidationRuleLoader INSTANCE = new GoodsValidationRuleLoader();

    private final ConcurrentHashMap<String, List<GoodsValidatedRule>> goodsValidRuleCache = new ConcurrentHashMap<>(2);

    private GoodsValidationRuleLoader() {
        registerApolloConfigListener(this);
    }

    public static GoodsValidationRuleLoader getInstance() {
        return INSTANCE;
    }

    private void registerApolloConfigListener(ConfigChangeListener listener) {
        ConfigService.getAppConfig().addChangeListener(listener, Sets.newHashSet(PROP_NAME_KEYS));
    }

    /**
     * Loads goods validation rule.
     *
     * @param key
     * @param goodsLevelNo
     * @return
     */
    public GoodsValidatedRule loadFromApolloConfig(Integer key, Integer goodsLevelNo) {
        Preconditions.checkArgument(NumberUtil.isPositive(goodsLevelNo), "goodsLevelNo is not positive");
       // Preconditions.checkArgument(StringUtils.isNotEmpty(key), "key is empty");
      //  List< GoodsValidatedRule > goodsValidatedRules= SpringContextHolder.getBean(GoodsCommonService.class) .getAllValidatedRule(key);
//        Optional<GoodsValidatedRule> optional = loadFromApolloConfig(key).stream()
//                .filter(rule -> rule.getGoodsLevel().equals(goodsLevelNo)).findFirst();
        return  SpringContextHolder.getBean(GoodsCommonService.class).getValidatedRule(key,goodsLevelNo);
    }

    /**
     * Loads goods validation rule.
     *
     * @param key
     * @return
     */
    @Deprecated
    private List<GoodsValidatedRule> loadFromApolloConfig(String key) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(key), "property key is required");

        List<GoodsValidatedRule> goodsValidatedRules = goodsValidRuleCache.get(key);
        if (CollectionUtils.isNotEmpty(goodsValidatedRules)) {
            return goodsValidatedRules;
        }

        String rulesInJson = ConfigService.getAppConfig().getProperty(key, "");
        return deserializeAndPutGoodsValidatedRule(key, rulesInJson);
    }


    private List<GoodsValidatedRule> deserializeAndPutGoodsValidatedRule(String key, String valueInJson) {
        if (StringUtils.isEmpty(valueInJson)) {
            throw new IllegalStateException(String.format("Failed to retrieve property key[%s] from Apollo config center", key));
        }

        List<GoodsValidatedRule> goodsValidatedRules = deserializeJson2Object(valueInJson);
        goodsValidRuleCache.put(key, goodsValidatedRules);
        return goodsValidatedRules;
    }


    private List<GoodsValidatedRule> deserializeJson2Object(String valueInJson) {
        List<GoodsValidatedRule> goodsValidationRules;
        try {
            goodsValidationRules = JSONObject.parseArray(valueInJson, GoodsValidatedRule.class);
        } catch (Exception e) {
            LOGGER.error("反序列化商品校验规则时发生错误", e);
            throw new IllegalStateException("反序列化时发生错误", e);
        }

        return goodsValidationRules;
    }


    @Override
    public void onChange(ConfigChangeEvent changeEvent) {
        for (String interestKey : PROP_NAME_KEYS) {
            ConfigChange configChange = changeEvent.getChange(interestKey);
            if (configChange == null) {
                continue;
            }
            String newValue = configChange.getNewValue();
            deserializeAndPutGoodsValidatedRule(interestKey, newValue);
        }
    }
}
