/**
 * 初始化数据
 * @param data
 * @returns {*}
 */
function viewData(data) {
    return axios({
        url: '/goods/finance/api/goodsFinanceInfoData.do',
        method: 'post',
        params: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

/**
 * 保存商品财务信息
 * @param data
 */
function saveGoodsFinanceInfo(data){
    // 非医疗器械商品  医疗器械细分类、用途、产线均应为空
    if (data.isMedicalEquipment != 1){
        data.medicalEquipmentType = ''
        data.medicalEquipmentUse = ''
        data.medicalEquipmentLine = ''
    }
    return axios({
        url: '/goods/finance/api/saveGoodsFinanceInfo.do',
        method: 'put',
        params: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

/**
 * 保存商品财务信息
 * @param data
 */
function audit(data){
    return axios({
        url: '/goods/finance/api/audit.do',
        method: 'POST',
        params: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}