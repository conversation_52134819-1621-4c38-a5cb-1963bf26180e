package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWmsInputOrderDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 盘盈入库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchInventoryProfitProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeInventoryProfitDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchWmsInputOrderDtoMapper  batchWmsInputOrderDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;


    @Override
    public KingDeeInventoryProfitDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        // 主表
        KingDeeInventoryProfitDto dto = new KingDeeInventoryProfitDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());

        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("盘盈入库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        log.info("盘盈入库单,BatchWarehouseOutInProcessorService.process:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
        // erp原始单据 盘盈入库单
        BatchWmsInputOrderDto wmsInputOrderDto = batchWmsInputOrderDtoMapper.findByOrderNo(batchWarehouseGoodsOutInDto.getRelateNo());
        if (wmsInputOrderDto == null) {
            return null;
        }

        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));

        Map<Integer, BatchWmsInputOrderGoodsDto> map = wmsInputOrderDto.getBatchWmsInputOrderGoodsDtoList().stream()
                .collect(Collectors.toMap(BatchWmsInputOrderGoodsDto::getGoodsId, c -> c, (k1, k2) -> k1));
        dto.setFId("0");
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDateTime(batchWarehouseGoodsOutInDto.getOutInTime()));

        // 详细
        List<KingDeeInventoryProfitDetailDto> detailList = new ArrayList<>();
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(d -> {
            KingDeeInventoryProfitDetailDto detailDto = new KingDeeInventoryProfitDetailDto();
            BatchWmsInputOrderGoodsDto wmsInputOrderGoodsDto = map.get(d.getGoodsId());
            detailDto.setFMaterialId(wmsInputOrderGoodsDto.getSkuNo());
            detailDto.setFBaseGainQty(d.getNum().toString());
            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokPch(d.getVedengBatchNumber());
            detailDto.setFQzokXlh(d.getBarcodeFactory());
            detailList.add(detailDto);
        });
        dto.setFBillEntry(detailList);
        return dto;
    }


}
