<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
            http://www.springframework.org/schema/beans
            http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

	<!-- jedis pool配置 -->
	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
		<!--最大连接数 -->
		<property name="maxTotal" value="${redis.maxTotal}" />
		<!--最大空闲连接数 -->
		<property name="maxIdle" value="${redis.maxIdle}" />
		<!--初始化连接数 -->
		<property name="minIdle" value="${redis.minIdle}" />
		<!--最大等待时间 -->
		<property name="maxWaitMillis" value="${redis.maxWaitMillis}" />
		<!--对拿到的connection进行validateObject校验 -->
		<property name="testOnBorrow" value="${redis.testOnBorrow}" />
		<!--在进行returnObject对返回的connection进行validateObject校验 -->
		<property name="testOnReturn" value="${redis.testOnReturn}" />
		<!--定时对线程池中空闲的链接进行validateObject校验 -->
		<property name="testWhileIdle" value="true" />
	</bean>
	<!-- 生产环境配置 -->
	<bean id="jedisPool" class="redis.clients.jedis.JedisPool"
		destroy-method="destroy">
		<constructor-arg index="0" ref="jedisPoolConfig" />
		<constructor-arg index="1" value="${redis.host}" />
		<constructor-arg index="2" value="${redis.port}" />
		<constructor-arg index="3" value="${redis.timeout}" />
		<constructor-arg index="4" value="${redis.pass}" />
	</bean>

	<bean id="redisUtils" class="com.vedeng.common.redis.RedisUtils" />
	<!--<bean id="changeRedisData" class="com.vedeng.common.redis.ChangeRedisData" />-->

	<bean id="connectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory"
		p:host-name="${redis.host}" p:port="${redis.port}" p:pool-config-ref="jedisPoolConfig" />
		
	<bean id="redisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">
		<property name="connectionFactory" ref="connectionFactory" />
	</bean>

	<!-- 项目启动后加载方法 sqlSessionTemplate：spring-mvc.xml中 -->
	<!--<bean id="initRedisData" class="com.vedeng.common.redis.RefreshRedis" init-method="initRedisData">
		<property name="sqlSessionTemplate" ref="sqlSessionTemplate" />
	</bean>-->
</beans>