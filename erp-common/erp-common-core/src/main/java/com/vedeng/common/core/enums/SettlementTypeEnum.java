package com.vedeng.common.core.enums;

import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 结算单类型
 * @date 2023/12/6 13:54
 */
public enum SettlementTypeEnum {

    /**
     * 返利
     */
    REBATE_APPLY("REBATE_APPLY", "返利申请", SupplierAssetEnum.rebate),
    REBATE_PAY("REBATE_PAY", "返利支付", SupplierAssetEnum.rebate),
    REBATE_REFUND("REBATE_REFUND", "返利退款", SupplierAssetEnum.rebate),

    /**
     * 信用
     */
    CREDIT_PAY("CREDIT_PAY", "信用支付", SupplierAssetEnum.credit),
    CREDIT_REPAYMENT("CREDIT_REPAYMENT", "信用还款", SupplierAssetEnum.credit),
    CREDIT_REFUND("CREDIT_REFUND", "信用退款", SupplierAssetEnum.credit),

    /**
     * 余额
     */
    BALANCE_PAY("BALANCE_PAY", "余额支付", SupplierAssetEnum.balance),
    BALANCE_REFUND("BALANCE_REFUND", "余额退款", SupplierAssetEnum.balance),

    /**
     * 银行
     */
    BANK_PAY("BANK_PAY", "银行支付", SupplierAssetEnum.bank),
    BANK_REFUND("BANK_REFUND", "银行退款", SupplierAssetEnum.bank),

    ;


    private final String code;

    private final String type;
    private final SupplierAssetEnum supplierAssetEnum;

    SettlementTypeEnum(String code, String type, SupplierAssetEnum supplierAssetEnum) {
        this.code = code;
        this.type = type;
        this.supplierAssetEnum = supplierAssetEnum;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public SupplierAssetEnum getSupplierAssetEnum() {
        return supplierAssetEnum;
    }

    /**
     * 获取结算单类型枚举
     *
     * @param code 业务类型
     * @return KingDeeBizEnums
     */
    public static SettlementTypeEnum getEnum(String code) {
        return Arrays.stream(SettlementTypeEnum.values())
                .filter(enums -> enums.getCode().equals(code))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的结算单类型"));

    }


}
