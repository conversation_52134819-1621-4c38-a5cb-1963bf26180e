package com.vedeng.erp.broadcast.mapdao;

import java.util.List;


import com.vedeng.erp.broadcast.domain.entity.BroadcastLogEntity;

/**
 * 播报日志Mapper
 */
public interface BroadcastLogMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastLogEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastLogEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastLogEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastLogEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastLogEntity record);

    /**
     * 根据条件获取日志表
     * @param broadcastLog
     * @return
     */
	List<BroadcastLogEntity> selectByParams(BroadcastLogEntity broadcastLog);
}
