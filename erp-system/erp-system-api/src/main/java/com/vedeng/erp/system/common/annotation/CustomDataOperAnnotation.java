package com.vedeng.erp.system.common.annotation;

import com.vedeng.erp.system.common.enums.CustomDataOperBizTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperTypeEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义新增数据日志注解
 * 注解的方法需要return新增的数据主键集合
 *
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CustomDataOperAnnotation {

    /**
     * 参数
     */
    String parameter() default "id";

    /**
     * 自定义业务类型枚举类
     */
    CustomDataOperBizTypeEnums operBizType();

    /**
     * 操作类型
     */
    CustomDataOperTypeEnums dataOperType();
}
