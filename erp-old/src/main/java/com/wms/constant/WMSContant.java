package com.wms.constant;

public class WMSContant {

    public static final String WMS_APP_SECRET = "12345678";

    /**
     * 客户信息同步
     */
    public static final String PUT_CUSTOMER = "CUSTOMER";

    /**
     * sku信息同步
     */
    public static final String PUT_SKU = "SKU";

    /**
     * 注册信息同步
     */
    public static final String PUT_REG = "REG";

    /**
     * 资质信息同步
     */
    public static final String PUT_QLA = "QLA";


    /**
     * 换货单下传接口
     */
    public static final String PUT_EXG_DATA = "EXG";


    /**
     * 入库单下传接口
     */
    public static final String PUT_PURCHASE_ORDER = "PO";
    /**
     * 入库单取消接口
     */
    public static final String CANCEL_PO = "POC";

    /**
     * 入库单回传接口
     */
    public static final String PUT_PURCHASE_ORDER_CALLBACK = "ASNRT";

    /**
     * 出库单下传接口
     */
    public static final String PUT_ORIGINAL_SALESORDER = "SALES";

    /**
     * 出库单回传接口
     */
    public static final String SORT = "SORT";

    /**
     * 库存调整单下传接口
     */
    public static final String PUT_INVENTORY_ADJUSRMENT = "ADJCK";

    /**
     * 库存调整单回传接口
     */
    public static final String PUT_INVENTORY_ADJUSRMENT_CALLBACK = "ADJRT";

    /**
     * 库存转移单回传接口
     */
    public static final String PUT_INVENTORY_TRANSFER_CALLBACK = "INVTR";

    /**
     * 出库单撤销接口
     */
    public static final String CANCEL_ORGGINCAL_SALESORDER = "SOC";

    /**
     * ERP下传是否开发票接口
     */
    public static final String PUT_INVOICE_DATA = "INVO";

    /**
     * WMS触发ERP开票
     */
    public static final String OPEN_INVOICE_CALLBACK = "INVOCF";

    /**
     * 出库包裹回传接口
     */
    public static final String PUT_SOPAGD_CALLBACK = "SOPAG";
    /**
     * 查询WMS库存
     */
    public static final String QUERT_WMS_SKUSTOCK = "INVQY";

    /**
     * 下传发票图片接口
     */
    public static final String PUT_PATH_DATA = "PATH";

    /**
     * 商品长宽高体积
     */
    public static final String PUT_LWH_DATA = "LWH";

    /**
     * 入库厂家SN码回传接口
     */
    public static final String IN_SN_CODE_CALLBACK = "ASNSN";

    /**
     * 出库厂家SN码回传接口
     */
    public static final String OUT_SN_CODE_CALLBACK = "SOSN";

    /**
     * 出库修改接口
     */
    public static final String MODIFY_SALEORDER = "SOMODIFY";


    /**
     * 入库单类型-直发采购单
     */
    public static final String DELIVERY_DIRECT_IN  = "ZP";

    /**
     * 外接单下发接口
     */
    public static final String PUT_LEND_OUT_DATA = "LEND_OUT";

}
