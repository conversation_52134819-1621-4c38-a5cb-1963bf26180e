package com.wms.constant;

/**
 * <AUTHOR>
 * @ClassName CancelCodeConstant.java
 * @Description TODO  wms撤销出入库接口返回常量
 * @createTime 2020年08月10日 19:36:00
 */
public class CancelCodeConstant {

    //000 表示撤消成功
    public static final String SUCCESS_CODE = "000";
    //999其它类数据错误，取消失败
    public static final String DATA_ERROE_CODE = "999";

    //--------出库单撤销-------------
    //991订单已复核或完成，无法取消
    public static final String COMPLETE_CODE = "991";
    //992WMS中不存在该单,请核对后取消
    public static final String OUTNO_EXIST_CODE = "992";
    //993订单已完成，无法取消
    public static final String COMPLETE_ALL_CODE = "993";
    //--------出库单撤销-------------

    //--------入库单撤销-------------
    //991无效货主！
    public static final String INVALID_CODE = "991";
    //992取消单号为空
    public static final String NO_EMTY_CODE = "992";
    //993订单不存在
    public static final String INNO_EXIST_CODE = "993";
    //994订单已开始作业，无法取消
    public static final String WORKING_CODE = "994";
    //995订单已收货，无法取消！
    public static final String REAL_ARR_CODE ="995";

    //998订单已上架，无法取消！
    public static final String FINISH_RECEIVE_CODE ="998";

    //--------入库单撤销-------------
}
