package com.vedeng.erp.aftersale.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesToYxbDto;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesToYxbEntity;
import com.vedeng.erp.aftersale.mapper.AfterSalesToYxbMapper;
import com.vedeng.erp.aftersale.mapstruct.AfterSalesToYxbConvertor;
import com.vedeng.erp.aftersale.service.AfterSalesToYxbApiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * AfterSalesToYxb的dao包装Service
 */
@Service
public class AfterSalesToYxbServiceImpl implements AfterSalesToYxbApiService {
    @Resource
    private AfterSalesToYxbMapper afterSalesToYxbMapper;
    @Resource
    private AfterSalesToYxbConvertor afterSalesToYxbConvertor;
    @Override
    public List<AfterSalesToYxbDto> selectByConditionDispatch(AfterSalesToYxbEntity afterSalesToYxbEntity) {
        List<AfterSalesToYxbEntity> afterSalesToYxbEntities = afterSalesToYxbMapper.selectByCondition(afterSalesToYxbEntity);
        List<AfterSalesToYxbDto> afterSalesToYxbDtos = afterSalesToYxbConvertor.toDto(afterSalesToYxbEntities);
        return afterSalesToYxbDtos;
    }

    @Override
    public int insertCancelAfterSalesToYxb(AfterSalesToYxbEntity afterSalesToYxbEntity) {
        return  afterSalesToYxbMapper.insert(afterSalesToYxbEntity);
    }

    @Override
    public int logicDeleteAfterCancel(Integer afterSalesId) {
        return afterSalesToYxbMapper.updateIsDeleteByAfterSalesOrderIdAndInterfaceTypeInt(afterSalesId, ErpConst.ZERO);
    }

    @Override
    public PageInfo<AfterSalesToYxbDto> selectByForDownTask(Page page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> afterSalesToYxbMapper.selectByForDownTask());
    }

    @Override
    public PageInfo<AfterSalesToYxbDto> selectForPayPushTask(Page page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> afterSalesToYxbMapper.selectForPayPushTask());
    }
}
