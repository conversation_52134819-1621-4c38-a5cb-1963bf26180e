package com.vedeng.api.standard.core.exception;

import com.vedeng.api.standard.core.BaseResponseCode;

/**
 * 认证异常类
 * 用于处理用户认证和授权相关的异常
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public class AuthenticationException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 构造函数 - 使用默认认证错误码
     */
    public AuthenticationException(String message) {
        super(message);
        this.code = BaseResponseCode.TOKEN_ERROR.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数 - 自定义错误码和消息
     */
    public AuthenticationException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数 - 使用响应码枚举
     */
    public AuthenticationException(BaseResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
    }
    
    /**
     * 构造函数 - 包含原因异常
     */
    public AuthenticationException(String message, Throwable cause) {
        super(message, cause);
        this.code = BaseResponseCode.TOKEN_ERROR.getCode();
        this.message = message;
    }
    
    /**
     * 静态工厂方法 - 用户未登录
     */
    public static AuthenticationException notLogin() {
        return new AuthenticationException(BaseResponseCode.TOKEN_ERROR);
    }
    
    /**
     * 静态工厂方法 - 用户未登录，自定义消息
     */
    public static AuthenticationException notLogin(String message) {
        return new AuthenticationException(BaseResponseCode.TOKEN_ERROR.getCode(), message);
    }
    
    /**
     * 静态工厂方法 - 权限不足
     */
    public static AuthenticationException permissionDenied() {
        return new AuthenticationException(BaseResponseCode.PERMISSION_DENIED);
    }
    
    /**
     * 静态工厂方法 - 权限不足，自定义消息
     */
    public static AuthenticationException permissionDenied(String message) {
        return new AuthenticationException(BaseResponseCode.PERMISSION_DENIED.getCode(), message);
    }
    
    /**
     * 静态工厂方法 - 用户不存在
     */
    public static AuthenticationException userNotExist() {
        return new AuthenticationException(BaseResponseCode.USER_NOT_EXIST);
    }

    /**
     * 静态工厂方法 - 用户不存在，自定义消息
     */
    public static AuthenticationException userNotExist(String message) {
        return new AuthenticationException(BaseResponseCode.USER_NOT_EXIST.getCode(), message);
    }
    
    /**
     * 静态工厂方法 - 用户已被禁用
     */
    public static AuthenticationException userDisabled() {
        return new AuthenticationException(BaseResponseCode.USER_DISABLED);
    }
    
    // Getter methods
    public Integer getCode() {
        return code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    @Override
    public String toString() {
        return "AuthenticationException{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
