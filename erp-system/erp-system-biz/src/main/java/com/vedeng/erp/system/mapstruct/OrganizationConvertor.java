package com.vedeng.erp.system.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.system.domain.dto.BankDto;
import com.vedeng.erp.system.domain.entity.BankEntity;
import com.vedeng.erp.system.domain.entity.OrganizationEntity;
import com.vedeng.erp.system.dto.OrganizationDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description: dto entity转换类
 * @date 2022/7/21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface OrganizationConvertor extends BaseMapStruct<OrganizationEntity, OrganizationDto> {
}
