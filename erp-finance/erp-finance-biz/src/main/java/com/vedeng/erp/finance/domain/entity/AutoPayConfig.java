package com.vedeng.erp.finance.domain.entity;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自动付款配置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AutoPayConfig {
    /**
    * 主键
    */
    private Integer autoPayConfigId;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 创建者ID
    */
    private Integer creator;

    /**
    * 修改者ID
    */
    private Integer updater;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者名称
    */
    private String updaterName;

    /**
    * 启用付款申请自动制单
    */
    private Integer enableAutoPay;

    /**
    * 启用采购合同审核状态
    */
    private Integer enableContractAudit;

    /**
    * 启用供应商白名单
    */
    private Integer enableSupplierWhitelist;

    /**
    * 供应商白名单
    */
    private String supplierWhitelist;

    /**
    * 启用推送金蝶付款单自动审核
    */
    private Integer enableKingDeeAutoAudit;

    /**
    * 付款限额：时间规则
    */
    private String payApplyTime;

    /**
    * 付款限额
    */
    private String payLimit;

    /**
    * 付款银行：时间规则
    */
    private String payBankTime;

    /**
    * 付款银行
    */
    private String payBank;
}