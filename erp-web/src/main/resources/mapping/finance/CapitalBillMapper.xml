<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.CapitalBillMapper">
	<resultMap id="BaseResultMap" type="com.vedeng.finance.model.CapitalBill">
		<id column="CAPITAL_BILL_ID" property="capitalBillId" jdbcType="INTEGER" />
		<result column="BANK_BILL_ID" property="bankBillId" jdbcType="INTEGER" />
		<result column="CAPITAL_BILL_NO" property="capitalBillNo" jdbcType="VARCHAR" />
		<result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
		<result column="TRADER_TIME" property="traderTime" jdbcType="BIGINT" />
		<result column="TRADER_SUBJECT" property="traderSubject" jdbcType="BIT" />
		<result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
		<result column="TRADER_MODE" property="traderMode" jdbcType="INTEGER" />
		<result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
		<result column="CURRENCY_UNIT_ID" property="currencyUnitId" jdbcType="INTEGER" />
		<result column="PAYER" property="payer" jdbcType="VARCHAR" />
		<result column="PAYEE" property="payee" jdbcType="VARCHAR" />
		<result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="TRAN_FLOW" property="tranFlow" jdbcType="VARCHAR" />
		<result column="PAYER_BANK_ACCOUNT" property="payerBankAccount" jdbcType="VARCHAR" />
		<result column="PAYEE_BANK_ACCOUNT" property="payeeBankAccount" jdbcType="VARCHAR" />
		<result column="PAYER_BANK_NAME" property="payerBankName" jdbcType="VARCHAR" />
		<result column="PAYEE_BANK_NAME" property="payeeBankName" jdbcType="VARCHAR" />
		<result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
		<result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
		<result column="RELATED_ORDER_ID" property="relatedOrderId" jdbcType="INTEGER"/>
		<result column="RELATED_ORDER_NO" property="relatedOrderNo" jdbcType="VARCHAR" />
		<result column="RECEIPT_URL" property="receiptUrl" jdbcType="VARCHAR"/>
	</resultMap>
	<sql id="Base_Column_List">
		CAPITAL_BILL_ID, BANK_BILL_ID, CAPITAL_BILL_NO, COMPANY_ID, TRADER_TIME,
		TRADER_SUBJECT, TRADER_TYPE,
		TRADER_MODE, AMOUNT, CURRENCY_UNIT_ID, PAYER, PAYEE, COMMENTS, ADD_TIME, CREATOR,
		TRAN_FLOW, PAYER_BANK_ACCOUNT, PAYEE_BANK_ACCOUNT, PAYER_BANK_NAME,
		PAYEE_BANK_NAME
	</sql>

	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from T_CAPITAL_BILL
		where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
	</select>
	<insert id="insert" parameterType="com.vedeng.finance.model.CapitalBill">
		insert into T_CAPITAL_BILL (CAPITAL_BILL_ID, BANK_BILL_ID,
		CAPITAL_BILL_NO, COMPANY_ID,
		TRADER_TIME, TRADER_SUBJECT, TRADER_TYPE,
		TRADER_MODE, AMOUNT, CURRENCY_UNIT_ID, PAYER, PAYEE,
		COMMENTS, ADD_TIME, CREATOR, TRAN_FLOW, PAYER_BANK_ACCOUNT, PAYEE_BANK_ACCOUNT,
		PAYER_BANK_NAME, PAYEE_BANK_NAME,COMPANY_ID
		)
		values (#{capitalBillId,jdbcType=INTEGER}, #{bankBillId,jdbcType=INTEGER},
		#{capitalBillNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER},
		#{traderTime,jdbcType=BIGINT}, #{traderSubject,jdbcType=BIT},
		#{traderType,jdbcType=BIT},
		#{traderMode,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{currencyUnitId,jdbcType=INTEGER},
		#{payer,jdbcType=VARCHAR}, #{payee,jdbcType=VARCHAR},
		#{comments,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT},
		#{creator,jdbcType=INTEGER},
		#{tranFlow,jdbcType=VARCHAR}, #{payerBankAccount,jdbcType=VARCHAR},
		#{payeeBankAccount,jdbcType=VARCHAR},
		#{payerBankName,jdbcType=VARCHAR}, #{payeeBankName,jdbcType=VARCHAR},
		#{companyId,jdbcType=INTEGER}
		)
	</insert>
	<insert id="insertSelective" parameterType="com.vedeng.finance.model.CapitalBill"
			useGeneratedKeys="true" keyProperty="capitalBillId">
		insert into T_CAPITAL_BILL
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="capitalBillId != null">
				CAPITAL_BILL_ID,
			</if>
			<if test="bankBillId != null">
				BANK_BILL_ID,
			</if>
			<if test="capitalBillNo != null">
				CAPITAL_BILL_NO,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="traderTime != null">
				TRADER_TIME,
			</if>
			<if test="traderSubject != null">
				TRADER_SUBJECT,
			</if>
			<if test="traderType != null">
				TRADER_TYPE,
			</if>
			<if test="traderMode != null">
				TRADER_MODE,
			</if>
			<if test="amount != null">
				AMOUNT,
			</if>
			<if test="currencyUnitId != null">
				CURRENCY_UNIT_ID,
			</if>
			<if test="payer != null">
				PAYER,
			</if>
			<if test="payee != null">
				PAYEE,
			</if>
			<if test="comments != null">
				COMMENTS,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="tranFlow != null">
				TRAN_FLOW,
			</if>
			<if test="payerBankAccount != null">
				PAYER_BANK_ACCOUNT,
			</if>
			<if test="payeeBankAccount != null">
				PAYEE_BANK_ACCOUNT,
			</if>
			<if test="payerBankName != null">
				PAYER_BANK_NAME,
			</if>
			<if test="payeeBankName != null">
				PAYEE_BANK_NAME,
			</if>
			<if test="paymentType != null">
				PAYMENT_TYPE,
			</if>
			<if test="capitalSearchFlow != null">
				CAPITAL_SEARCH_FLOW,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="capitalBillId != null">
				#{capitalBillId,jdbcType=INTEGER},
			</if>
			<if test="bankBillId != null">
				#{bankBillId,jdbcType=INTEGER},
			</if>
			<if test="capitalBillNo != null">
				#{capitalBillNo,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="traderTime != null">
				#{traderTime,jdbcType=BIGINT},
			</if>
			<if test="traderSubject != null">
				#{traderSubject,jdbcType=BIT},
			</if>
			<if test="traderType != null">
				#{traderType,jdbcType=BIT},
			</if>
			<if test="traderMode != null">
				#{traderMode,jdbcType=INTEGER},
			</if>
			<if test="amount != null">
				#{amount,jdbcType=DECIMAL},
			</if>
			<if test="currencyUnitId != null">
				#{currencyUnitId,jdbcType=INTEGER},
			</if>
			<if test="payer != null">
				#{payer,jdbcType=VARCHAR},
			</if>
			<if test="payee != null">
				#{payee,jdbcType=VARCHAR},
			</if>
			<if test="comments != null">
				#{comments,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="tranFlow != null">
				#{tranFlow,jdbcType=VARCHAR},
			</if>
			<if test="payerBankAccount != null">
				#{payerBankAccount,jdbcType=VARCHAR},
			</if>
			<if test="payeeBankAccount != null">
				#{payeeBankAccount,jdbcType=VARCHAR},
			</if>
			<if test="payerBankName != null">
				#{payerBankName,jdbcType=VARCHAR},
			</if>
			<if test="payeeBankName != null">
				#{payeeBankName,jdbcType=VARCHAR},
			</if>
			<if test="paymentType != null">
				#{paymentType,jdbcType=INTEGER},
			</if>
			<if test="capitalSearchFlow != null">
				#{capitalSearchFlow,jdbcType=INTEGER}
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.finance.model.CapitalBill">
		update T_CAPITAL_BILL
		<set>
			<if test="bankBillId != null">
				BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
			</if>
			<if test="capitalBillNo != null">
				CAPITAL_BILL_NO = #{capitalBillNo,jdbcType=VARCHAR},
			</if>
			<if test="companyId != null">
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="traderTime != null">
				TRADER_TIME = #{traderTime,jdbcType=BIGINT},
			</if>
			<if test="traderSubject != null">
				TRADER_SUBJECT = #{traderSubject,jdbcType=BIT},
			</if>
			<if test="traderType != null">
				TRADER_TYPE = #{traderType,jdbcType=BIT},
			</if>
			<if test="traderMode != null">
				TRADER_MODE = #{traderMode,jdbcType=INTEGER},
			</if>
			<if test="amount != null">
				AMOUNT = #{amount,jdbcType=DECIMAL},
			</if>
			<if test="currencyUnitId != null">
				CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
			</if>
			<if test="payer != null">
				PAYER = #{payer,jdbcType=VARCHAR},
			</if>
			<if test="payee != null">
				PAYEE = #{payee,jdbcType=VARCHAR},
			</if>
			<if test="comments != null">
				COMMENTS = #{comments,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="tranFlow != null">
				TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
			</if>
			<if test="payerBankAccount != null">
				PAYER_BANK_ACCOUNT = #{payerBankAccount,jdbcType=VARCHAR},
			</if>
			<if test="payeeBankAccount != null">
				PAYEE_BANK_ACCOUNT = #{payeeBankAccount,jdbcType=VARCHAR},
			</if>
			<if test="payerBankName != null">
				PAYER_BANK_NAME = #{payerBankName,jdbcType=VARCHAR},
			</if>
			<if test="payeeBankName != null">
				PAYEE_BANK_NAME = #{payeeBankName,jdbcType=VARCHAR},
			</if>
		</set>
		where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.vedeng.finance.model.CapitalBill">
		update T_CAPITAL_BILL
		set BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
		CAPITAL_BILL_NO = #{capitalBillNo,jdbcType=VARCHAR},
		COMPANY_ID = #{companyId,jdbcType=INTEGER},
		TRADER_TIME = #{traderTime,jdbcType=BIGINT},
		TRADER_SUBJECT = #{traderSubject,jdbcType=BIT},
		TRADER_TYPE = #{traderType,jdbcType=BIT},
		TRADER_MODE = #{traderMode,jdbcType=INTEGER},
		AMOUNT = #{amount,jdbcType=DECIMAL},
		CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
		PAYER = #{payer,jdbcType=VARCHAR},
		PAYEE = #{payee,jdbcType=VARCHAR},
		COMMENTS = #{comments,jdbcType=VARCHAR},
		ADD_TIME = #{addTime,jdbcType=BIGINT},
		CREATOR = #{creator,jdbcType=INTEGER},
		TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
		PAYER_BANK_ACCOUNT = #{payerBankAccount,jdbcType=VARCHAR},
		PAYEE_BANK_ACCOUNT = #{payeeBankAccount,jdbcType=VARCHAR},
		PAYER_BANK_NAME = #{payerBankName,jdbcType=VARCHAR},
		PAYEE_BANK_NAME = #{payeeBankName,jdbcType=VARCHAR}
		where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
	</update>

	<!-- 财务：收款记录列表：计总 -->
	<select id="getNewCollectionRecordCount" parameterType="Map" resultType="java.util.Map">
		SELECT COUNT(*) AS TOTAL_RECORD,
		GROUP_CONCAT(RELATED_ID, '-', OPERATION_TYPE) AS SALE_ORDER_ID_STR,	<!-- 计算实际金额使用，售后订单的实际金额就是服务费 -->
		IFNULL(SUM(AMOUNT),0) AS AMOUNT
		FROM (SELECT C.COMPANY_ID,
		C.CAPITAL_BILL_ID,
		B.CAPITAL_BILL_DETAIL_ID,
		B.ORDER_TYPE,
		B.RELATED_ID,
		B.ORDER_NO,
		A.TOTAL_AMOUNT,
		IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, -ABS(C.AMOUNT), ABS(C.AMOUNT)) AS AMOUNT,
		1 AS OPERATION_TYPE
		FROM T_SALEORDER A
		INNER JOIN T_CAPITAL_BILL_DETAIL B
		ON A.SALEORDER_ID = B.RELATED_ID
		INNER JOIN T_CAPITAL_BILL C
		ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		AND A.COMPANY_ID = C.COMPANY_ID
		WHERE     B.TRADER_TYPE = 1	<!-- 经销商 -->
		AND B.ORDER_TYPE = 1	<!-- 销售订单 -->
		AND C.TRADER_TYPE IN (1, 4)	<!-- 收入转入 -->
		AND CONCAT(C.TRADER_TYPE,
		'-',
		B.BUSSINESS_TYPE,
		'-',
		C.TRADER_SUBJECT,
		'-',
		C.TRADER_MODE) <![CDATA[ <> ]]> ('3-526-1-527')	<!-- 应财务要求：排除账期支付 -->
		<include refid="newCapitalBill_Sale_Where_Sql" />
		UNION ALL
		SELECT C.COMPANY_ID,
		C.CAPITAL_BILL_ID,
		B.CAPITAL_BILL_DETAIL_ID,
		B.ORDER_TYPE,
		D.SALEORDER_ID AS RELATED_ID,
		D.SALEORDER_NO AS ORDER_NO,
		D.TOTAL_AMOUNT,
		FROM (SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             B.CAPITAL_BILL_DETAIL_ID,
		             B.ORDER_TYPE,
		             B.RELATED_ID,
		             B.ORDER_NO,
		             B.BUSSINESS_TYPE,
		             A.TRADER_ID,
		             A.TRADER_NAME,
		             A.TOTAL_AMOUNT,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, -ABS(C.AMOUNT), ABS(C.AMOUNT)) AS AMOUNT,
		             C.PAYER,
		             C.TRADER_SUBJECT,
		             C.TRADER_MODE,
		             C.COMMENTS,
		             C.TRADER_TIME,
		             C.CREATOR,
		             A.DELIVERY_STATUS,
		             A.ARRIVAL_STATUS,
		             A.ARRIVAL_TIME,
		             A.DELIVERY_TIME,
		             0 AS RELATED_ORDER_ID,
		             '' AS RELATED_ORDER_NO,
		             A.ORG_ID,
		             A.USER_ID,
		             1 AS OPERATION_TYPE
		      FROM T_SALEORDER A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B
		              ON A.SALEORDER_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		      WHERE     B.TRADER_TYPE = 1	<!-- 经销商 -->
		            AND B.ORDER_TYPE = 1	<!-- 销售订单 -->
		            AND C.TRADER_TYPE IN (1, 4)	<!-- 收入转入 -->
		            AND CONCAT(C.TRADER_TYPE,
		                       '-',
		                       B.BUSSINESS_TYPE,
		                       '-',
		                       C.TRADER_SUBJECT,
		                       '-',
		                       C.TRADER_MODE) <![CDATA[ <> ]]> ('3-526-1-527')	<!-- 应财务要求：排除账期支付 -->
		            <include refid="newCapitalBill_Sale_Where_Sql" />
		      UNION ALL
		      SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             B.CAPITAL_BILL_DETAIL_ID,
		             B.ORDER_TYPE,
		             D.SALEORDER_ID AS RELATED_ID,
		             D.SALEORDER_NO AS ORDER_NO,
		             B.BUSSINESS_TYPE,
		             D.TRADER_ID,
		             D.TRADER_NAME,
		             D.TOTAL_AMOUNT,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, -ABS(C.AMOUNT), ABS(C.AMOUNT)) AS AMOUNT,
		             C.PAYER,
		             C.TRADER_SUBJECT,
		             C.TRADER_MODE,
		             C.COMMENTS,
		             C.TRADER_TIME,
		             C.CREATOR,
		             D.DELIVERY_STATUS,
		             D.ARRIVAL_STATUS,
		             D.ARRIVAL_TIME,
		             D.DELIVERY_TIME,
		             A.AFTER_SALES_ID AS RELATED_ORDER_ID,
		             A.AFTER_SALES_NO AS RELATED_ORDER_NO,
		             D.ORG_ID,
		             D.USER_ID,
		             1 AS OPERATION_TYPE
		      FROM T_AFTER_SALES A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B
		              ON A.AFTER_SALES_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		           INNER JOIN T_SALEORDER D ON A.ORDER_ID = D.SALEORDER_ID
		      WHERE     A.TYPE IN (539, 543)	<!-- 退货、退款 -->
		            AND A.SUBJECT_TYPE = 535	<!-- 销售单售后 -->
		            AND B.TRADER_TYPE = 1	<!-- 经销商 -->
		            AND B.ORDER_TYPE = 3	<!-- 售后订单 -->
		            AND CONCAT(C.TRADER_TYPE,
		                       '-',
		                       B.BUSSINESS_TYPE,
		                       '-',
		                       C.TRADER_SUBJECT,
		                       '-',
		                       C.TRADER_MODE) = ('5-531-1-530')	<!-- 应财务要求，退还到客户或余额的记录，只展示一条（退到余额的记录） -->
		            AND B.BUSSINESS_TYPE = 531	<!-- 退款 -->
		            <include refid="newCapitalBill_After_Where_Sql" />
		      UNION ALL
		      SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             B.CAPITAL_BILL_DETAIL_ID,
		             B.ORDER_TYPE,
		             A.AFTER_SALES_ID AS RELATED_ID,
		             A.AFTER_SALES_NO AS ORDER_NO,
		             B.BUSSINESS_TYPE,
		             D.TRADER_ID,
		             E.TRADER_NAME,
		             D.SERVICE_AMOUNT AS TOTAL_AMOUNT,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, -ABS(C.AMOUNT), ABS(C.AMOUNT)) AS AMOUNT,
		             C.PAYER,
		             C.TRADER_SUBJECT,
		             C.TRADER_MODE,
		             C.COMMENTS,
		             C.TRADER_TIME,
		             C.CREATOR,
		             99 AS DELIVERY_STATUS,
		             99 AS ARRIVAL_STATUS,
		             '' AS ARRIVAL_TIME,
		             '' AS DELIVERY_TIME,
		             A.ORDER_ID AS RELATED_ORDER_ID,
		             A.ORDER_NO AS RELATED_ORDER_NO,
		             '' AS ORG_ID,
		             A.SERVICE_USER_ID AS USER_ID,
		             2 AS OPERATION_TYPE
		      FROM T_AFTER_SALES A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B
		              ON A.AFTER_SALES_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		           INNER JOIN T_AFTER_SALES_DETAIL D
		              ON A.AFTER_SALES_ID = D.AFTER_SALES_ID
		           LEFT JOIN T_TRADER E ON D.TRADER_ID = E.TRADER_ID
		           <!-- LEFT JOIN T_SALEORDER F ON A.ORDER_ID = F.SALEORDER_ID -->
		      WHERE     A.TYPE IN (539, 540, 541, 584, 550, 585,4090,4091)	<!-- 退货、换货、安调、维修  第三方安调、维修 -->
		            AND A.SUBJECT_TYPE IN (535, 537)	<!-- 销售售后、第三方 -->
		            AND B.TRADER_TYPE = 1	<!-- 经销商 -->
		            AND B.ORDER_TYPE = 3	<!-- 售后订单 -->
		            AND B.BUSSINESS_TYPE = 526	<!-- 收款 -->
		            AND B.BUSSINESS_TYPE <![CDATA[ <> ]]> 531	<!-- 退款 -->
		            <include refid="newCapitalBill_Other_Where_Sql" />
			) P
		ORDER BY P.CAPITAL_BILL_DETAIL_ID DESC
	</select>
	<sql id="newCapitalBill_Sale_Where_Sql">
		<!-- 公司ID -->
		<if test="capitalBill.companyId!=null and capitalBill.companyId != 0">
			AND C.COMPANY_ID = #{capitalBill.companyId,jdbcType=INTEGER}
		</if>
		<!-- 订单号 -->
		<if test="capitalBill.capitalBillDetail != null and capitalBill.capitalBillDetail.orderNo != null and capitalBill.capitalBillDetail.orderNo != ''">
			AND B.ORDER_NO LIKE CONCAT('%',#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR},'%')
		</if>
		<!-- 客户名称 -->
		<if test="capitalBill.saleorder != null and capitalBill.saleorder.traderName != null and capitalBill.saleorder.traderName != ''">
			AND A.TRADER_NAME LIKE CONCAT('%',#{capitalBill.saleorder.traderName,jdbcType=VARCHAR},'%')
		</if>
		<!-- 交易名称 -->
		<if test="capitalBill.payer!=null and capitalBill.payer!=''">
			AND C.PAYER LIKE CONCAT('%',#{capitalBill.payer,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 备注 -->
		<if test="capitalBill.comments!=null and capitalBill.comments!=''">
			AND C.COMMENTS LIKE	CONCAT('%',#{capitalBill.comments,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 交易主体 -->
		<if test="capitalBill.traderSubject != null and capitalBill.traderSubject != -1">
			AND C.TRADER_SUBJECT = #{capitalBill.traderSubject,jdbcType=BIT}
		</if>
		<!-- 交易方式 -->
		<if test="capitalBill.traderMode!=null and capitalBill.traderMode!=-1">
			AND C.TRADER_MODE = #{capitalBill.traderMode,jdbcType=INTEGER}
		</if>
		<!-- 业务类型 -->
		<if test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.bussinessType!=null and capitalBill.capitalBillDetail.bussinessType!=-1">
			AND B.BUSSINESS_TYPE = #{capitalBill.capitalBillDetail.bussinessType,jdbcType=INTEGER}
		</if>
		<!-- 销售部门 -->
		<if test="capitalBill.orgId != null and capitalBill.orgId != -1">
			AND A.ORG_ID = #{capitalBill.orgId,jdbcType=INTEGER}
		</if>
		<!-- 客户 -->
		<if test="capitalBill.traderIdList != null and capitalBill.traderIdList.size > 0">
			AND A.TRADER_ID IN
				<foreach collection="capitalBill.traderIdList" item="traderId" separator="," open="(" close=")">
					#{traderId,jdbcType=INTEGER}
				</foreach>
		</if>
		<!-- 发货状态 -->
		<if test="capitalBill.deliveryStatus != null and capitalBill.deliveryStatus != -1">
			AND A.DELIVERY_STATUS = #{capitalBill.deliveryStatus,jdbcType=INTEGER}
		</if>
		<!-- 到货状态 -->
		<if test="capitalBill.arrivalStatus != null and capitalBill.arrivalStatus != -1">
			AND A.ARRIVAL_STATUS = #{capitalBill.arrivalStatus,jdbcType=INTEGER}
		</if>
		<!-- 交易开始时间 -->
		<if test="capitalBill.searchBegintime!=null and capitalBill.searchBegintime != 0">
			AND C.TRADER_TIME &gt;= #{capitalBill.searchBegintime,jdbcType=BIGINT}
		</if>
		<!-- 交易结束时间 -->
		<if test="capitalBill.searchEndtime != null and capitalBill.searchEndtime != 0">
			AND C.TRADER_TIME &lt;= #{capitalBill.searchEndtime,jdbcType=BIGINT}
		</if>
		<!-- 搜索起始金额 -->
		<if test="capitalBill.searchBeginAmount != null and capitalBill.searchBeginAmount != 0">
			AND C.AMOUNT &gt;= #{capitalBill.searchBeginAmount,jdbcType=DECIMAL}
		</if>
		<!-- 搜索结束金额 -->
		<if test="capitalBill.searchEndAmount != null and capitalBill.searchEndAmount != 0">
			AND C.AMOUNT &lt;= #{capitalBill.searchEndAmount,jdbcType=DECIMAL}
		</if>
	</sql>
	<sql id="newCapitalBill_After_Where_Sql">
		<!-- 公司ID -->
		<if test="capitalBill.companyId!=null and capitalBill.companyId != 0">
			AND C.COMPANY_ID = #{capitalBill.companyId,jdbcType=INTEGER}
		</if>
		<!-- 订单号 -->
		<if test="capitalBill.capitalBillDetail != null and capitalBill.capitalBillDetail.orderNo != null and capitalBill.capitalBillDetail.orderNo != ''">
			AND D.SALEORDER_NO LIKE CONCAT('%',#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR},'%')
		</if>
		<!-- 客户名称 -->
		<if test="capitalBill.saleorder != null and capitalBill.saleorder.traderName != null and capitalBill.saleorder.traderName != ''">
			AND D.TRADER_NAME LIKE CONCAT('%',#{capitalBill.saleorder.traderName,jdbcType=VARCHAR},'%')
		</if>
		<!-- 交易名称 -->
		<if test="capitalBill.payer!=null and capitalBill.payer!=''">
			AND C.PAYER LIKE CONCAT('%',#{capitalBill.payer,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 备注 -->
		<if test="capitalBill.comments!=null and capitalBill.comments!=''">
			AND C.COMMENTS LIKE	CONCAT('%',#{capitalBill.comments,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 交易主体 -->
		<if test="capitalBill.traderSubject != null and capitalBill.traderSubject != -1">
			AND C.TRADER_SUBJECT = #{capitalBill.traderSubject,jdbcType=BIT}
		</if>
		<!-- 交易方式 -->
		<if test="capitalBill.traderMode!=null and capitalBill.traderMode!=-1">
			AND C.TRADER_MODE = #{capitalBill.traderMode,jdbcType=INTEGER}
		</if>
		<!-- 业务类型 -->
		<if test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.bussinessType!=null and capitalBill.capitalBillDetail.bussinessType!=-1">
			AND B.BUSSINESS_TYPE = #{capitalBill.capitalBillDetail.bussinessType,jdbcType=INTEGER}
		</if>
		<!-- 销售部门 -->
		<if test="capitalBill.orgId != null and capitalBill.orgId != -1">
			AND D.ORG_ID = #{capitalBill.orgId,jdbcType=INTEGER}
		</if>
		<!-- 客户 -->
		<if test="capitalBill.traderIdList != null and capitalBill.traderIdList.size > 0">
			AND D.TRADER_ID IN
				<foreach collection="capitalBill.traderIdList" item="traderId" separator="," open="(" close=")">
					#{traderId,jdbcType=INTEGER}
				</foreach>
		</if>
		<!-- 发货状态 -->
		<if test="capitalBill.deliveryStatus != null and capitalBill.deliveryStatus != -1">
			AND D.DELIVERY_STATUS = #{capitalBill.deliveryStatus,jdbcType=INTEGER}
		</if>
		<!-- 到货状态 -->
		<if test="capitalBill.arrivalStatus != null and capitalBill.arrivalStatus != -1">
			AND D.ARRIVAL_STATUS = #{capitalBill.arrivalStatus,jdbcType=INTEGER}
		</if>
		<!-- 交易开始时间 -->
		<if test="capitalBill.searchBegintime!=null and capitalBill.searchBegintime != 0">
			AND C.TRADER_TIME &gt;= #{capitalBill.searchBegintime,jdbcType=BIGINT}
		</if>
		<!-- 交易结束时间 -->
		<if test="capitalBill.searchEndtime != null and capitalBill.searchEndtime != 0">
			AND C.TRADER_TIME &lt;= #{capitalBill.searchEndtime,jdbcType=BIGINT}
		</if>
		<!-- 搜索起始金额 -->
		<if test="capitalBill.searchBeginAmount != null and capitalBill.searchBeginAmount != 0">
			AND C.AMOUNT &gt;= #{capitalBill.searchBeginAmount,jdbcType=DECIMAL}
		</if>
		<!-- 搜索结束金额 -->
		<if test="capitalBill.searchEndAmount != null and capitalBill.searchEndAmount != 0">
			AND C.AMOUNT &lt;= #{capitalBill.searchEndAmount,jdbcType=DECIMAL}
		</if>
	</sql>
	<sql id="newCapitalBill_Other_Where_Sql">
		<!-- 公司ID -->
		<if test="capitalBill.companyId!=null and capitalBill.companyId != 0">
			AND C.COMPANY_ID = #{capitalBill.companyId,jdbcType=INTEGER}
		</if>
		<!-- 订单号 -->
		<if test="capitalBill.capitalBillDetail != null and capitalBill.capitalBillDetail.orderNo != null and capitalBill.capitalBillDetail.orderNo != ''">
			AND A.AFTER_SALES_NO LIKE CONCAT('%',#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR},'%')
		</if>
		<!-- 客户名称 -->
		<if test="capitalBill.saleorder != null and capitalBill.saleorder.traderName != null and capitalBill.saleorder.traderName != ''">
			AND E.TRADER_NAME LIKE CONCAT('%',#{capitalBill.saleorder.traderName,jdbcType=VARCHAR},'%')
		</if>
		<!-- 交易名称 -->
		<if test="capitalBill.payer!=null and capitalBill.payer!=''">
			AND C.PAYER LIKE CONCAT('%',#{capitalBill.payer,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 备注 -->
		<if test="capitalBill.comments!=null and capitalBill.comments!=''">
			AND C.COMMENTS LIKE	CONCAT('%',#{capitalBill.comments,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 交易主体 -->
		<if test="capitalBill.traderSubject != null and capitalBill.traderSubject != -1">
			AND C.TRADER_SUBJECT = #{capitalBill.traderSubject,jdbcType=BIT}
		</if>
		<!-- 交易方式 -->
		<if test="capitalBill.traderMode!=null and capitalBill.traderMode!=-1">
			AND C.TRADER_MODE = #{capitalBill.traderMode,jdbcType=INTEGER}
		</if>
		<!-- 业务类型 -->
		<if test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.bussinessType!=null and capitalBill.capitalBillDetail.bussinessType!=-1">
			AND B.BUSSINESS_TYPE = #{capitalBill.capitalBillDetail.bussinessType,jdbcType=INTEGER}
		</if>
		<!-- 销售部门 -->
		<if test="capitalBill.orgId != null and capitalBill.orgId != -1 and capitalBill.orgId > -100">
			AND 1 <![CDATA[ <> ]]> 1
		</if>
		<if test="capitalBill.optUserId != null and capitalBill.optUserId != -1">
			AND A.SERVICE_USER_ID = #{capitalBill.optUserId,jdbcType=INTEGER}
		</if>
		<!-- 客户 -->
		<!-- <if test="capitalBill.traderIdList != null and capitalBill.traderIdList.size > 0">
			AND D.TRADER_ID IN
				<foreach collection="capitalBill.traderIdList" item="traderId" separator="," open="(" close=")">
					#{traderId,jdbcType=INTEGER}
				</foreach>
		</if> -->
		<!-- 发货状态 -->
		<if test="capitalBill.deliveryStatus != null and capitalBill.deliveryStatus != -1">
			AND  1 <![CDATA[ <> ]]> 1
		</if>
		<!-- 到货状态 -->
		<if test="capitalBill.arrivalStatus != null and capitalBill.arrivalStatus != -1">
			AND 1 <![CDATA[ <> ]]> 1
		</if>
		<!-- 交易开始时间 -->
		<if test="capitalBill.searchBegintime!=null and capitalBill.searchBegintime != 0">
			AND C.TRADER_TIME &gt;= #{capitalBill.searchBegintime,jdbcType=BIGINT}
		</if>
		<!-- 交易结束时间 -->
		<if test="capitalBill.searchEndtime != null and capitalBill.searchEndtime != 0">
			AND C.TRADER_TIME &lt;= #{capitalBill.searchEndtime,jdbcType=BIGINT}
		</if>
		<!-- 搜索起始金额 -->
		<if test="capitalBill.searchBeginAmount != null and capitalBill.searchBeginAmount != 0">
			AND C.AMOUNT &gt;= #{capitalBill.searchBeginAmount,jdbcType=DECIMAL}
		</if>
		<!-- 搜索结束金额 -->
		<if test="capitalBill.searchEndAmount != null and capitalBill.searchEndAmount != 0">
			AND C.AMOUNT &lt;= #{capitalBill.searchEndAmount,jdbcType=DECIMAL}
		</if>
	</sql>


	<!-- 新的付款记录列表 -->
	<select id="getNewPaymentRecordListPage" parameterType="Map" resultMap="BaseResultMap">
		SELECT P.*
		FROM (SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             B.CAPITAL_BILL_DETAIL_ID,
		             B.ORDER_TYPE,
		             B.RELATED_ID,
		             B.ORDER_NO,
		             A.TRADER_ID,
		             A.TRADER_NAME,
		             C.PAYEE,
		             C.TRADER_SUBJECT,
		             C.TRADER_MODE,
		             B.BUSSINESS_TYPE,
		             A.TOTAL_AMOUNT,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, ABS(C.AMOUNT), -ABS(C.AMOUNT)) AS AMOUNT,
		             C.TRADER_TIME,
		             C.COMMENTS,
		             C.CREATOR,
		             0 AS AFTER_SALES_PAYMENT_TYPE,
		             0 AS RELATED_ORDER_ID,
		             '' AS RELATED_ORDER_NO,
		             1 AS OPERATION_TYPE
		      FROM T_BUYORDER A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.BUYORDER_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		      WHERE     C.TRADER_TYPE IN (2, 5)	<!-- 2:支出，5转出 -->
		            AND B.TRADER_TYPE = 2	<!-- 供应商 -->
		            AND B.ORDER_TYPE = 2	<!-- 采购单 -->
		            AND CONCAT(C.TRADER_TYPE,
		                       '-',
		                       B.BUSSINESS_TYPE,
		                       '-',
		                       C.TRADER_SUBJECT,
		                       '-',
		                       C.TRADER_MODE) <![CDATA[ <> ]]> ('3-525-1-527')	<!-- 应财务要求，排除账期支付的记录 -->
		            <include refid="newPaymentRecord_Where_Sql" />
		            <include refid="newPaymentRecord_Buy_Where_Sql" />
		      UNION ALL
		      SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             B.CAPITAL_BILL_DETAIL_ID,
		             B.ORDER_TYPE,
		             D.BUYORDER_ID AS RELATED_ID,
		             D.BUYORDER_NO AS ORDER_NO,
		             D.TRADER_ID,
		             D.TRADER_NAME,
		             C.PAYEE,
		             C.TRADER_SUBJECT,
		             C.TRADER_MODE,
		             B.BUSSINESS_TYPE,
		             D.TOTAL_AMOUNT,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, ABS(C.AMOUNT), -ABS(C.AMOUNT)) AS AMOUNT,
		             C.TRADER_TIME,
		             C.COMMENTS,
		             C.CREATOR,
		             0 AS AFTER_SALES_PAYMENT_TYPE,
		             A.AFTER_SALES_ID AS RELATED_ORDER_ID,
		             A.AFTER_SALES_NO AS RELATED_ORDER_NO,
		             1 AS OPERATION_TYPE
		      FROM T_AFTER_SALES A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B
		              ON A.AFTER_SALES_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		           INNER JOIN T_BUYORDER D ON A.ORDER_ID = D.BUYORDER_ID
		      WHERE     A.TYPE IN (546, 549)	<!-- 采购售后退货、退款 -->
		            AND A.SUBJECT_TYPE = 536	<!-- 采购售后单 -->
		            AND B.TRADER_TYPE = 2	<!-- 供应商 -->
		            AND B.ORDER_TYPE = 3	<!-- 售后单 -->
		            AND CONCAT(C.TRADER_TYPE,
		                       '-',
		                       B.BUSSINESS_TYPE,
		                       '-',
		                       C.TRADER_SUBJECT,
		                       '-',
		                       C.TRADER_MODE) <![CDATA[ <> ]]> ('3-531-1-529')	<!-- 应财务要求，去除退还账期的记录 -->
		            AND C.TRADER_TYPE <![CDATA[ <> ]]> 3	<!-- 转移 -->
		            AND B.BUSSINESS_TYPE <![CDATA[ <> ]]> 529	<!-- 退还信用 -->
		            AND B.BUSSINESS_TYPE = 531	<!-- 退款 -->
		            <include refid="newPaymentRecord_Where_Sql" />
		            <include refid="newPaymentRecord_After_Where_Sql" />
		      UNION ALL
		      SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             B.CAPITAL_BILL_DETAIL_ID,
		             B.ORDER_TYPE,
		             A.AFTER_SALES_ID AS RELATED_ID,
		             A.AFTER_SALES_NO AS ORDER_NO,
		             D.TRADER_ID,
		             E.TRADER_NAME,
		             C.PAYEE,
		             C.TRADER_SUBJECT,
		             C.TRADER_MODE,
		             B.BUSSINESS_TYPE,
		             D.SERVICE_AMOUNT AS TOTAL_AMOUNT,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, ABS(C.AMOUNT), -ABS(C.AMOUNT)) AS AMOUNT,
		             C.TRADER_TIME,
		             C.COMMENTS,
		             C.CREATOR,
		             A.SUBJECT_TYPE AS AFTER_SALES_PAYMENT_TYPE,
		             A.ORDER_ID AS RELATED_ORDER_ID,
		             A.ORDER_NO AS RELATED_ORDER_NO,
		             2 AS OPERATION_TYPE
		      FROM T_AFTER_SALES A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B
		              ON A.AFTER_SALES_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		           INNER JOIN T_AFTER_SALES_DETAIL D
		              ON A.AFTER_SALES_ID = D.AFTER_SALES_ID
		           LEFT JOIN T_TRADER E ON D.TRADER_ID = E.TRADER_ID
		           <!-- LEFT JOIN T_SALEORDER F ON A.ORDER_ID = F.SALEORDER_ID -->
		      WHERE     A.TYPE IN (546, 547, 541, 584, 550, 585,4090,4091)	<!-- 采购单退货、换货，销售安调、维修,第三方安调、维修 -->
		            AND A.SUBJECT_TYPE IN (535, 536, 537)	<!-- 销售、采购 、第三方 -->
		            AND B.ORDER_TYPE = 3	<!-- 售后单 -->
		            AND B.BUSSINESS_TYPE = 525	<!-- 订单付款 -->
		            AND B.BUSSINESS_TYPE <![CDATA[ <> ]]> 531	<!-- 退款 -->
		            <include refid="newPaymentRecord_Where_Sql" />
		            <include refid="newPaymentRecord_Other_Where_Sql" />
			) P
		ORDER BY P.CAPITAL_BILL_DETAIL_ID DESC
	</select>
	<sql id="newPaymentRecord_Buy_Where_Sql">
		<!-- 订单号 -->
		<if test="capitalBill.capitalBillDetail != null and capitalBill.capitalBillDetail.orderNo != null and capitalBill.capitalBillDetail.orderNo != ''">
			AND B.ORDER_NO LIKE CONCAT('%',#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 供应商 -->
		<if test="capitalBill.buyorder != null and capitalBill.buyorder.traderName != null and capitalBill.buyorder.traderName != ''">
			AND A.TRADER_NAME LIKE CONCAT('%',#{capitalBill.buyorder.traderName,jdbcType=VARCHAR},'%' )
		</if>
	</sql>
	<sql id="newPaymentRecord_After_Where_Sql">
		<!-- 订单号 -->
		<if test="capitalBill.capitalBillDetail != null and capitalBill.capitalBillDetail.orderNo != null and capitalBill.capitalBillDetail.orderNo != ''">
			AND D.BUYORDER_NO LIKE CONCAT('%',#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 供应商 -->
		<if test="capitalBill.buyorder != null and capitalBill.buyorder.traderName !=null and capitalBill.buyorder.traderName != ''">
			AND D.TRADER_NAME LIKE CONCAT('%',#{capitalBill.buyorder.traderName,jdbcType=VARCHAR},'%' )
		</if>
	</sql>
	<sql id="newPaymentRecord_Other_Where_Sql">
		<!-- 订单号 -->
		<if test="capitalBill.capitalBillDetail != null and capitalBill.capitalBillDetail.orderNo != null and capitalBill.capitalBillDetail.orderNo != ''">
			AND A.AFTER_SALES_NO LIKE CONCAT('%',#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 供应商 -->
		<if test="capitalBill.buyorder != null and capitalBill.buyorder.traderName !=null and capitalBill.buyorder.traderName != ''">
			AND E.TRADER_NAME LIKE CONCAT('%',#{capitalBill.buyorder.traderName,jdbcType=VARCHAR},'%' )
		</if>
	</sql>
	<sql id="newPaymentRecord_Where_Sql">
		<!-- 公司ID -->
		<if test="capitalBill.companyId != null and capitalBill.companyId != 0">
			AND C.COMPANY_ID = #{capitalBill.companyId}
		</if>
		<!-- 收款方 -->
		<if test="capitalBill.payee != null and capitalBill.payee != ''">
			AND C.PAYEE LIKE CONCAT('%',#{capitalBill.payee,jdbcType=VARCHAR},'%')
		</if>
		<!-- 交易主体 -->
		<if test="capitalBill.traderSubject != null and capitalBill.traderSubject != -1">
			AND C.TRADER_SUBJECT = #{capitalBill.traderSubject,jdbcType=BIT}
		</if>
		<!-- 交易方式 -->
		<if test="capitalBill.traderMode != null and capitalBill.traderMode != -1">
			AND C.TRADER_MODE = #{capitalBill.traderMode,jdbcType=INTEGER}
		</if>
		<!-- 业务类型 -->
		<if test="capitalBill.capitalBillDetail != null and capitalBill.capitalBillDetail.bussinessType != null and capitalBill.capitalBillDetail.bussinessType != -1">
			AND B.BUSSINESS_TYPE = #{capitalBill.capitalBillDetail.bussinessType,jdbcType=INTEGER}
		</if>
		<!-- 备注 -->
		<if test="capitalBill.comments != null and capitalBill.comments != ''">
			AND C.COMMENTS LIKE CONCAT('%',#{capitalBill.comments,jdbcType=VARCHAR},'%' )
		</if>
		<!-- 付款开始时间 -->
		<if test="capitalBill.searchBegintime != null and capitalBill.searchBegintime != 0">
			AND C.TRADER_TIME &gt;= #{capitalBill.searchBegintime,jdbcType=BIGINT}
		</if>
		<!-- 付款结束时间 -->
		<if test="capitalBill.searchEndtime != null and capitalBill.searchEndtime != 0">
			AND C.TRADER_TIME &lt;= #{capitalBill.searchEndtime,jdbcType=BIGINT}
		</if>
		<!-- 付款金额范围 -->
		<if test="capitalBill.searchBeginAmount != null and capitalBill.searchBeginAmount != 0">
			AND C.AMOUNT &gt;= #{capitalBill.searchBeginAmount,jdbcType=DECIMAL}
		</if>
		<!-- 付款金额范围 -->
		<if test="capitalBill.searchEndAmount != null and capitalBill.searchEndAmount != 0">
			AND C.AMOUNT &lt;= #{capitalBill.searchEndAmount,jdbcType=DECIMAL}
		</if>
	</sql>
	

	<select id="getNewPaymentRecordCount" parameterType="Map" resultType="java.util.Map">
		SELECT COUNT(*) AS TOTAL_RECORD,
		       GROUP_CONCAT(RELATED_ID, '-', OPERATION_TYPE) AS BUY_ORDER_ID_STR,
		       SUM(AMOUNT) AS AMOUNT
		FROM (SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             B.RELATED_ID,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, ABS(C.AMOUNT), -ABS(C.AMOUNT)) AS AMOUNT,
		             1 AS OPERATION_TYPE
		      FROM T_BUYORDER A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.BUYORDER_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		      WHERE     C.TRADER_TYPE IN (2, 5)	<!-- 2:支出，5转出 -->
		            AND B.TRADER_TYPE = 2	<!-- 供应商 -->
		            AND B.ORDER_TYPE = 2	<!-- 采购单 -->
		            AND CONCAT(C.TRADER_TYPE,
		                       '-',
		                       B.BUSSINESS_TYPE,
		                       '-',
		                       C.TRADER_SUBJECT,
		                       '-',
		                       C.TRADER_MODE) <![CDATA[ <> ]]> ('3-525-1-527')	<!-- 应财务要求，排除账期支付的记录 -->
		            <include refid="newPaymentRecord_Where_Sql" />
		            <include refid="newPaymentRecord_Buy_Where_Sql" />
		      UNION ALL
		      SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             D.BUYORDER_ID AS RELATED_ID,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, ABS(C.AMOUNT), -ABS(C.AMOUNT)) AS AMOUNT,
		             1 AS OPERATION_TYPE
		      FROM T_AFTER_SALES A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B
		              ON A.AFTER_SALES_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		           INNER JOIN T_BUYORDER D ON A.ORDER_ID = D.BUYORDER_ID
		      WHERE     A.TYPE IN (546, 549)	<!-- 采购售后退货、退款 -->
		            AND A.SUBJECT_TYPE = 536	<!-- 采购售后单 -->
		            AND B.TRADER_TYPE = 2	<!-- 供应商 -->
		            AND B.ORDER_TYPE = 3	<!-- 售后单 -->
		            AND CONCAT(C.TRADER_TYPE,
		                       '-',
		                       B.BUSSINESS_TYPE,
		                       '-',
		                       C.TRADER_SUBJECT,
		                       '-',
		                       C.TRADER_MODE) <![CDATA[ <> ]]> ('3-531-1-529')	<!-- 应财务要求，去除退还账期的记录 -->
		            AND C.TRADER_TYPE <![CDATA[ <> ]]> 3	<!-- 转移 -->
		            AND B.BUSSINESS_TYPE <![CDATA[ <> ]]> 529	<!-- 退还信用 -->
		            AND B.BUSSINESS_TYPE = 531	<!-- 退款 -->
		            <include refid="newPaymentRecord_Where_Sql" />
		            <include refid="newPaymentRecord_After_Where_Sql" />
		      UNION ALL
		      SELECT C.COMPANY_ID,
		             C.CAPITAL_BILL_ID,
		             A.AFTER_SALES_ID AS RELATED_ID,
		             IF(C.TRADER_TYPE = 2 OR C.TRADER_TYPE = 5, ABS(C.AMOUNT), -ABS(C.AMOUNT)) AS AMOUNT,
		             2 AS OPERATION_TYPE
		      FROM T_AFTER_SALES A
		           INNER JOIN T_CAPITAL_BILL_DETAIL B
		              ON A.AFTER_SALES_ID = B.RELATED_ID
		           INNER JOIN T_CAPITAL_BILL C
		              ON     B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		                 AND A.COMPANY_ID = C.COMPANY_ID
		           INNER JOIN T_AFTER_SALES_DETAIL D
		              ON A.AFTER_SALES_ID = D.AFTER_SALES_ID
		           LEFT JOIN T_TRADER E ON D.TRADER_ID = E.TRADER_ID
		           <!-- LEFT JOIN T_SALEORDER F ON A.ORDER_ID = F.SALEORDER_ID -->
		      WHERE     A.TYPE IN (546, 547, 541, 584, 550, 585,4090,4091)	<!-- 采购单退货、换货，销售安调、维修,第三方安调、维修 -->
		            AND A.SUBJECT_TYPE IN (535, 536, 537)	<!-- 销售、采购 、第三方 -->
		            AND B.ORDER_TYPE = 3	<!-- 售后单 -->
		            AND B.BUSSINESS_TYPE = 525	<!-- 订单付款 -->
		            AND B.BUSSINESS_TYPE <![CDATA[ <> ]]> 531	<!-- 退款 -->
		            <include refid="newPaymentRecord_Where_Sql" />
		            <include refid="newPaymentRecord_Other_Where_Sql" />
			) P
	</select>


	<select id="getPeriodCapitalBill" resultMap="BaseResultMap">
		select
		a.*
		from
		T_CAPITAL_BILL a
		left join
		T_CAPITAL_BILL_DETAIL b
		on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		where
		b.ORDER_TYPE = #{orderType,jdbcType=BIT}
		and
		b.RELATED_ID = #{orderId,jdbcType=INTEGER}
		and
		b.BUSSINESS_TYPE in (533)
		order by
		a.ADD_TIME asc
	</select>

	<select id="getPeriodPayCapitalBill" resultMap="BaseResultMap">
		select
		a.*
		from
		T_CAPITAL_BILL a
		left join
		T_CAPITAL_BILL_DETAIL b
		on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		where
		b.ORDER_TYPE = #{orderType,jdbcType=BIT}
		and
		b.RELATED_ID = #{orderId,jdbcType=INTEGER}
		and
		a.TRADER_MODE = 527
	</select>

	<sql id="Capital_Bill_Where_Sql">
		<if
			test="capitalBill.capitalBillNo!=null and capitalBill.capitalBillNo!=''">
			<!-- 记账编号 -->
			and a.CAPITAL_BILL_NO = #{capitalBill.capitalBillNo,jdbcType=VARCHAR}
		</if>
		<if test="capitalBill.tranFlow!=null and capitalBill.tranFlow!=''">
			<!-- 流水号 -->
			and a.TRAN_FLOW = #{capitalBill.tranFlow,jdbcType=VARCHAR}
		</if>
		<if
			test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.bussinessType!=null and capitalBill.capitalBillDetail.bussinessType!=-1">
			<!-- 业务类型 -->
			and b.BUSSINESS_TYPE =
			#{capitalBill.capitalBillDetail.bussinessType,jdbcType=INTEGER}
		</if>
		<if
			test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.orderNo!=null and capitalBill.capitalBillDetail.orderNo!=''">
			<!-- 业务单据 -->
			and b.ORDER_NO =
			#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR}
		</if>
		<!-- if test="capitalBill.payer!=null and capitalBill.payer!=''"> and a.PAYER 
			like CONCAT('%',#{capitalBill.payer,jdbcType=VARCHAR},'%' ) </if> <if test="capitalBill.payerBankAccount!=null 
			and capitalBill.payerBankAccount!=''"> and a.PAYER_BANK_ACCOUNT = #{capitalBill.payerBankAccount,jdbcType=VARCHAR} 
			</if -->
		<if test="capitalBill.payee!=null and capitalBill.payee!=''">
			<!-- 交易名称 -->
			and (a.PAYEE like
			CONCAT('%',#{capitalBill.payee,jdbcType=VARCHAR},'%' ) or a.PAYER
			like CONCAT('%',#{capitalBill.payee,jdbcType=VARCHAR},'%' ))
		</if>
		<if
			test="capitalBill.payeeBankAccount!=null and capitalBill.payeeBankAccount!=''">
			<!-- 交易帐号 -->
			and (a.PAYEE_BANK_ACCOUNT =
			#{capitalBill.payeeBankAccount,jdbcType=VARCHAR} or
			a.PAYER_BANK_ACCOUNT =
			#{capitalBill.payeeBankAccount,jdbcType=VARCHAR})
		</if>
		<if test="capitalBill.traderType!=null and capitalBill.traderType!=-1">
			<!-- 交易类型 -->
			and a.TRADER_TYPE = #{capitalBill.traderType,jdbcType=BIT}
		</if>
		<if
			test="capitalBill.traderSubject!=null and capitalBill.traderSubject!=-1">
			<!-- 交易主体 -->
			and a.TRADER_SUBJECT = #{capitalBill.traderSubject,jdbcType=BIT}
		</if>
		<if test="capitalBill.traderMode!=null and capitalBill.traderMode!=-1">
			<!-- 交易方式 -->
			and a.TRADER_MODE = #{capitalBill.traderMode,jdbcType=INTEGER}
		</if>
		<if
			test="capitalBill.searchBeginAmount!=null and capitalBill.searchBeginAmount!=0">
			and a.AMOUNT &gt;=
			#{capitalBill.searchBeginAmount,jdbcType=DECIMAL}
		</if>
		<if
			test="capitalBill.searchEndAmount!=null and capitalBill.searchEndAmount!=0">
			and a.AMOUNT &lt;=
			#{capitalBill.searchEndAmount,jdbcType=DECIMAL}
		</if>
		<if test="capitalBill.comments!=null and capitalBill.comments!=''">
			<!-- 备注 -->
			and a.COMMENTS like
			CONCAT('%',#{capitalBill.comments,jdbcType=VARCHAR},'%' )
		</if>
		<if
			test="capitalBill.searchBegintime!=null and capitalBill.searchBegintime!=0">
			and a.TRADER_TIME &gt;=
			#{capitalBill.searchBegintime,jdbcType=BIGINT}
		</if>
		<if test="capitalBill.searchEndtime!=null and capitalBill.searchEndtime!=0">
			and a.TRADER_TIME &lt;=
			#{capitalBill.searchEndtime,jdbcType=BIGINT}
		</if>
		<if test="capitalBill.companyId!=null and capitalBill.companyId!=-1">
			<!-- 公司 -->
			and a.COMPANY_ID = #{capitalBill.companyId,jdbcType=INTEGER}
		</if>
		<if
			test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.traderId!=null and capitalBill.capitalBillDetail.traderId!=0">
			<!-- 交易者id -->
			and b.TRADER_ID =
			#{capitalBill.capitalBillDetail.traderId,jdbcType=INTEGER}
		</if>
		<if
			test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.traderType!=null and capitalBill.capitalBillDetail.traderType!=0">
			<!-- 交易者类型 -->
			and b.TRADER_TYPE =
			#{capitalBill.capitalBillDetail.traderType,jdbcType=BIT}
		</if>
	</sql>

	<!-- 资金流水列表（分页） -->
	<!-- resultMap type="com.vedeng.model.finance.CapitalBill" id="capitalBillDetailResultMap" 
		extends="BaseResultMap"> <collection property="capitalBillDetails" javaType="list" 
		ofType="com.vedeng.model.finance.CapitalBillDetail"> <id property="capitalBillDetailId" 
		column="CAPITAL_BILL_DETAIL_ID" /> <result property="bussinessType" column="BUSSINESS_TYPE" 
		/> <result property="orderType" column="ORDER_TYPE" /> <result property="orderNo" 
		column="ORDER_NO" /> <result property="relatedId" column="RELATED_ID" /> 
		<result property="amount" column="DETAIL_AMOUNT" /> </collection> </resultMap -->
	<resultMap type="com.vedeng.finance.model.CapitalBill" id="capitalBillDetailResultMap"
		extends="BaseResultMap">
		<association property="capitalBillDetail"
			javaType="com.vedeng.finance.model.CapitalBillDetail">
			<result property="bussinessType" column="BUSSINESS_TYPE" />
			<result property="orderType" column="ORDER_TYPE" />
			<result property="orderNo" column="ORDER_NO" />
			<result property="relatedId" column="RELATED_ID" />
			<result property="amount" column="DETAIL_AMOUNT" />
			<result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
			<result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
		</association>
	</resultMap>
	<select id="getCapitalBillListPage" resultMap="capitalBillDetailResultMap"
		parameterType="Map">
		select
		a.* , b.CAPITAL_BILL_DETAIL_ID, b.BUSSINESS_TYPE, b.ORDER_TYPE,
		b.ORDER_NO, b.RELATED_ID, b.AMOUNT as DETAIL_AMOUNT
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		<where>
			1 = 1
			<include refid="Capital_Bill_Where_Sql" />
		</where>
		order by a.ADD_TIME DESC
	</select>

	<select id="getCapitalBillTotal" resultType="Map" parameterType="Map">
		select
		CONCAT(b.ORDER_TYPE, '') as ORDER_TYPE_STR, sum(a.AMOUNT) as TOTAL_AMOUNTS
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		<where>
			1 = 1
			<include refid="Capital_Bill_Where_Sql" />
		</where>
		group by b.ORDER_TYPE
	</select>

	<resultMap type="com.vedeng.finance.model.CapitalBill" id="capitalBillListResult"
		extends="BaseResultMap">
		<association property="capitalBillDetail"
			javaType="com.vedeng.finance.model.CapitalBillDetail">
			<id column="CAPITAL_BILL_DETAIL_ID" property="capitalBillDetailId"
				jdbcType="INTEGER" />
			<result column="BUSSINESS_TYPE" property="bussinessType"
				jdbcType="INTEGER" />
			<result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
		</association>
	</resultMap>

	<select id="getCapitalBillListByOrderNo" resultMap="capitalBillListResult">
		select
			a.*, b.CAPITAL_BILL_DETAIL_ID, b.BUSSINESS_TYPE, b.ORDER_NO
		from T_CAPITAL_BILL a
				 join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
         where b.ORDER_NO = #{orderNo,jdbcType=VARCHAR}
		<if test="bankTag != null and bankTag == 5">
			and a.TRADER_MODE = 522
		</if>
		<if test="bankTag != null and bankTag == 4">
			and a.TRADER_MODE = 520
		</if>
		<if test="flag1 != null and flag1 == 0">
			and a.TRADER_TYPE  = 2
		</if>
		<if test="flag1 != null and flag1 == 1">
			and a.TRADER_TYPE  = 1
		</if>
		and b.BUSSINESS_TYPE !=679
		and abs(a.AMOUNT) = #{amt,jdbcType=DECIMAL}

	</select>

	<select id="matchAlipayCapitalBillByCapitalSearchFlow" resultMap="capitalBillListResult">
		select
			*
		from
		T_CAPITAL_BILL
		where
		(BANK_BILL_ID is null or BANK_BILL_ID = 0)
		and (TRAN_FLOW is null or TRAN_FLOW = '')
		and TRADER_MODE = 520
		and TRADER_TYPE = 1
		and CAPITAL_SEARCH_FLOW = #{capitalSearchFlow,jdbcType=VARCHAR}
	</select>

	<!-- 获取资金流水（不分页） -->
	<select id="getCapitalBillList" resultMap="capitalBillListResult"
		parameterType="com.vedeng.finance.model.CapitalBill">
		select
		a.*, b.CAPITAL_BILL_DETAIL_ID, b.BUSSINESS_TYPE, b.ORDER_NO,c.RECEIPT_URL
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		left join T_BANK_BILL c ON a.BANK_BILL_ID = c.BANK_BILL_ID
		<where>
			1=1
			<if
				test="capitalBillDetail.bussinessType != null and capitalBillDetail.bussinessType != ''">
				<!-- 业务类型 -->
				AND b.BUSSINESS_TYPE =
				#{capitalBillDetail.bussinessType,jdbcType=INTEGER}
			</if>
			<if
				test="capitalBillDetail.orderType != null and capitalBillDetail.orderType != ''">
				<!-- 订单类型 -->
				AND b.ORDER_TYPE = #{capitalBillDetail.orderType,jdbcType=BIT}
			</if>
			<if
				test="capitalBillDetail.relatedId != null and capitalBillDetail.relatedId != ''">
				<!-- 关联表ID -->
				AND b.RELATED_ID = #{capitalBillDetail.relatedId,jdbcType=INTEGER}
			</if>
			<if
				test="capitalBillDetail.traderId != null and capitalBillDetail.traderId != ''">
				<!-- 关联表ID -->
				AND b.TRADER_ID = #{capitalBillDetail.traderId,jdbcType=INTEGER}
			</if>
			<if
				test="capitalBillDetail.traderType != null and capitalBillDetail.traderType != ''">
				<!-- 关联表ID -->
				AND b.TRADER_TYPE = #{capitalBillDetail.traderType,jdbcType=BIT}
			</if>
		</where>
		order by a.CAPITAL_BILL_ID DESC
	</select>

	<sql id="Payment_Record_Where_Sql">
		a.TRADER_TYPE = 2
		<if test="capitalBill.companyId!=null and capitalBill.companyId!=0">
			and a.COMPANY_ID = #{capitalBill.companyId}
		</if>
		<if
			test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.orderNo!=null and capitalBill.capitalBillDetail.orderNo!=''">
			<!-- 订单号 -->
			and b.ORDER_NO =
			#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR}
		</if>
		<if
			test="capitalBill.buyorder!=null and capitalBill.buyorder.traderName!=null and capitalBill.buyorder.traderName!=''">
			<!-- 供应商 -->
			and c.TRADER_NAME like
			CONCAT('%',#{capitalBill.buyorder.traderName,jdbcType=VARCHAR},'%' )
		</if>
		<if test="capitalBill.payee!=null and capitalBill.payee!=''">
			<!-- 收款方 -->
			and a.PAYEE like CONCAT('%',#{capitalBill.payee,jdbcType=VARCHAR},'%'
			)
		</if>
		<if
			test="capitalBill.traderSubject!=null and capitalBill.traderSubject!=-1">
			<!-- 交易主体 -->
			and a.TRADER_SUBJECT = #{capitalBill.traderSubject,jdbcType=BIT}
		</if>
		<if test="capitalBill.traderMode!=null and capitalBill.traderMode!=-1">
			<!-- 交易方式 -->
			and a.TRADER_MODE = #{capitalBill.traderMode,jdbcType=INTEGER}
		</if>
		<if
			test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.bussinessType!=null and capitalBill.capitalBillDetail.bussinessType!=-1">
			<!-- 业务类型 -->
			and b.BUSSINESS_TYPE =
			#{capitalBill.capitalBillDetail.bussinessType,jdbcType=INTEGER}
		</if>
		<if test="capitalBill.comments!=null and capitalBill.comments!=''">
			<!-- 备注 -->
			and a.COMMENTS like
			CONCAT('%',#{capitalBill.comments,jdbcType=VARCHAR},'%' )
		</if>
		<if
			test="capitalBill.searchBegintime!=null and capitalBill.searchBegintime!=0">
			and a.TRADER_TIME &gt;=
			#{capitalBill.searchBegintime,jdbcType=BIGINT}
		</if>
		<if test="capitalBill.searchEndtime!=null and capitalBill.searchEndtime!=0">
			and a.TRADER_TIME &lt;=
			#{capitalBill.searchEndtime,jdbcType=BIGINT}
		</if>
		<if
			test="capitalBill.searchBeginAmount!=null and capitalBill.searchBeginAmount!=0">
			and a.AMOUNT &gt;=
			#{capitalBill.searchBeginAmount,jdbcType=DECIMAL}
		</if>
		<if
			test="capitalBill.searchEndAmount!=null and capitalBill.searchEndAmount!=0">
			and a.AMOUNT &lt;=
			#{capitalBill.searchEndAmount,jdbcType=DECIMAL}
		</if>
	</sql>


	<sql id="Payment_Record_Aftersales_Where_Sql">
		a.TRADER_TYPE = 2
		<if test="capitalBill.companyId!=null and capitalBill.companyId!=0">
			and a.COMPANY_ID = #{capitalBill.companyId}
		</if>
		<if
			test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.orderNo!=null and capitalBill.capitalBillDetail.orderNo!=''">
			<!-- 订单号 -->
			and b.ORDER_NO =
			#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR}
		</if>
		<if
			test="capitalBill.buyorder!=null and capitalBill.buyorder.traderName!=null and capitalBill.buyorder.traderName!=''">
			<!-- 供应商 -->
			and 1 = 2
		</if>
		<if test="capitalBill.payee!=null and capitalBill.payee!=''">
			<!-- 收款方 -->
			and a.PAYEE like CONCAT('%',#{capitalBill.payee,jdbcType=VARCHAR},'%'
			)
		</if>
		<if
			test="capitalBill.traderSubject!=null and capitalBill.traderSubject!=-1">
			<!-- 交易主体 -->
			and a.TRADER_SUBJECT = #{capitalBill.traderSubject,jdbcType=BIT}
		</if>
		<if test="capitalBill.traderMode!=null and capitalBill.traderMode!=-1">
			<!-- 交易方式 -->
			and a.TRADER_MODE = #{capitalBill.traderMode,jdbcType=INTEGER}
		</if>
		<if
			test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.bussinessType!=null and capitalBill.capitalBillDetail.bussinessType!=-1">
			<!-- 业务类型 -->
			and b.BUSSINESS_TYPE =
			#{capitalBill.capitalBillDetail.bussinessType,jdbcType=INTEGER}
		</if>
		<if test="capitalBill.comments!=null and capitalBill.comments!=''">
			<!-- 备注 -->
			and a.COMMENTS like
			CONCAT('%',#{capitalBill.comments,jdbcType=VARCHAR},'%' )
		</if>
		<if
			test="capitalBill.searchBegintime!=null and capitalBill.searchBegintime!=0">
			and a.TRADER_TIME &gt;=
			#{capitalBill.searchBegintime,jdbcType=BIGINT}
		</if>
		<if test="capitalBill.searchEndtime!=null and capitalBill.searchEndtime!=0">
			and a.TRADER_TIME &lt;=
			#{capitalBill.searchEndtime,jdbcType=BIGINT}
		</if>
		<if
			test="capitalBill.searchBeginAmount!=null and capitalBill.searchBeginAmount!=0">
			and a.AMOUNT &gt;=
			#{capitalBill.searchBeginAmount,jdbcType=DECIMAL}
		</if>
		<if
			test="capitalBill.searchEndAmount!=null and capitalBill.searchEndAmount!=0">
			and a.AMOUNT &lt;=
			#{capitalBill.searchEndAmount,jdbcType=DECIMAL}
		</if>
	</sql>

	<!-- 获取付款记录（分页） -->
	<select id="getPaymentRecordListPage" resultMap="BaseResultMap"
		parameterType="Map">
		select * from (
		select
		a.* , b.ORDER_TYPE, b.ORDER_NO, b.RELATED_ID, b.TRADER_ID,
		b.BUSSINESS_TYPE, c.TRADER_NAME
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		join T_BUYORDER c on b.RELATED_ID = c.BUYORDER_ID
		<where>
			b.ORDER_TYPE = 2 and
			<include refid="Payment_Record_Where_Sql" />
		</where>

		union all

		select
		a.* , b.ORDER_TYPE, b.ORDER_NO, b.RELATED_ID, b.TRADER_ID,
		b.BUSSINESS_TYPE, '' as TRADER_NAME
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		join T_AFTER_SALES c on b.RELATED_ID = c.AFTER_SALES_ID
		<where>
			b.ORDER_TYPE = 3 and
			<include refid="Payment_Record_Aftersales_Where_Sql" />
		</where>
		) m order by m.ADD_TIME desc

	</select>

	<!-- 订单已付款总额 -->
	<select id="getOrderPaymentTotalAmount" parameterType="Map"
		resultType="java.math.BigDecimal">
		select
		COALESCE(sum(if(a.TRADER_TYPE=1 or a.TRADER_TYPE=4,ABS(a.AMOUNT),-ABS(a.AMOUNT))),0) as paymentAmount
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		join T_BUYORDER c on b.RELATED_ID = c.BUYORDER_ID
		<where>
			a.TRADER_TYPE in (1,2,4,5)
			and
			<include refid="Payment_Record_Where_Sql" />
		</where>
	</select>

	<!-- 本次付款总额 -->
	<select id="getThisPaymentTotalAmount" parameterType="Map"
		resultType="java.math.BigDecimal">
		select
		sum(a.amount) as paymentAmount
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		join T_BUYORDER c on b.RELATED_ID = c.BUYORDER_ID
		<where>
			a.TRADER_TYPE in (1,2,4,5)
			and
			<include refid="Payment_Record_Where_Sql" />
		</where>
	</select>

	<!-- 获取收款记录（分页） -->
	<select id="getCollectionRecordListPage" resultMap="BaseResultMap" parameterType="Map">
		select
		a.* , b.ORDER_TYPE, b.ORDER_NO, b.RELATED_ID, b.BUSSINESS_TYPE,
		c.TRADER_ID, c.TRADER_NAME, c.ORG_ID, c.USER_ID, c.DELIVERY_STATUS,
		c.ARRIVAL_STATUS, c.TOTAL_AMOUNT,
		c.ARRIVAL_TIME,
		c.DELIVERY_TIME
		from T_CAPITAL_BILL a
		join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		join T_SALEORDER c on b.RELATED_ID = c.SALEORDER_ID
		<where>
			a.TRADER_TYPE = 1 and b.ORDER_TYPE = 1
			<if test="capitalBill.companyId!=null and capitalBill.companyId!=0">
				<!-- 订单号 -->
				and a.COMPANY_ID = #{capitalBill.companyId}
			</if>
			<if
				test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.orderNo!=null and capitalBill.capitalBillDetail.orderNo!=''">
				<!-- 订单号 -->
				and b.ORDER_NO =
				#{capitalBill.capitalBillDetail.orderNo,jdbcType=VARCHAR}
			</if>
			<if
				test="capitalBill.saleorder!=null and capitalBill.saleorder.traderName!=null and capitalBill.saleorder.traderName!=''">
				<!-- 客户名称 -->
				and c.TRADER_NAME like
				CONCAT('%',#{capitalBill.saleorder.traderName,jdbcType=VARCHAR},'%'
				)
			</if>
			<if test="capitalBill.payer!=null and capitalBill.payer!=''">
				<!-- 交易名称 -->
				and a.PAYER like
				CONCAT('%',#{capitalBill.payer,jdbcType=VARCHAR},'%' )
			</if>
			<if test="capitalBill.comments!=null and capitalBill.comments!=''">
				<!-- 备注 -->
				and a.COMMENTS like
				CONCAT('%',#{capitalBill.comments,jdbcType=VARCHAR},'%' )
			</if>
			<if
				test="capitalBill.traderSubject!=null and capitalBill.traderSubject!=-1">
				<!-- 交易主体 -->
				and a.TRADER_SUBJECT = #{capitalBill.traderSubject,jdbcType=BIT}
			</if>
			<if test="capitalBill.traderMode!=null and capitalBill.traderMode!=-1">
				<!-- 交易方式 -->
				and a.TRADER_MODE = #{capitalBill.traderMode,jdbcType=INTEGER}
			</if>
			<if
				test="capitalBill.capitalBillDetail!=null and capitalBill.capitalBillDetail.bussinessType!=null and capitalBill.capitalBillDetail.bussinessType!=-1">
				<!-- 业务类型 -->
				and b.BUSSINESS_TYPE =
				#{capitalBill.capitalBillDetail.bussinessType,jdbcType=INTEGER}
			</if>
			<if test="capitalBill.orgId != null and capitalBill.orgId != -1">
				<!-- 销售部门 -->
				AND c.ORG_ID = #{capitalBill.orgId,jdbcType=INTEGER}
			</if>
			<if
				test="capitalBill.traderIdList != null and capitalBill.traderIdList.size > 0">
				AND c.TRADER_ID IN
				<foreach collection="capitalBill.traderIdList" item="traderId"
					separator="," open="(" close=")">
					#{traderId,jdbcType=INTEGER}
				</foreach>
			</if>
			<if
				test="capitalBill.deliveryStatus!=null and capitalBill.deliveryStatus!=-1">
				<!-- 发货状态 -->
				and c.DELIVERY_STATUS =
				#{capitalBill.deliveryStatus,jdbcType=INTEGER}
			</if>
			<if
				test="capitalBill.arrivalStatus!=null and capitalBill.arrivalStatus!=-1">
				<!-- 到货状态 -->
				and c.ARRIVAL_STATUS = #{capitalBill.arrivalStatus,jdbcType=INTEGER}
			</if>
			<if
				test="capitalBill.searchBegintime!=null and capitalBill.searchBegintime!=0">
				and a.TRADER_TIME &gt;= #{capitalBill.searchBegintime,jdbcType=BIGINT}
			</if>
			<if
				test="capitalBill.searchEndtime!=null and capitalBill.searchEndtime!=0">
				and a.TRADER_TIME &lt;= #{capitalBill.searchEndtime,jdbcType=BIGINT}
			</if>
			<if
				test="capitalBill.searchBeginAmount!=null and capitalBill.searchBeginAmount!=0">
				and a.AMOUNT &gt;= #{capitalBill.searchBeginAmount,jdbcType=DECIMAL}
			</if>
			<if
				test="capitalBill.searchEndAmount!=null and capitalBill.searchEndAmount!=0">
				and a.AMOUNT &lt;= #{capitalBill.searchEndAmount,jdbcType=DECIMAL}
			</if>
		</where>
		order by a.ADD_TIME DESC
	</select>

	<!-- 根据销售订单ID查询订单账期付款额 -->
	<select id="getCapitalListBySaleorderId" parameterType="java.util.List"
		resultType="com.vedeng.finance.model.CapitalBill">
		SELECT
		s.SALEORDER_ID AS RELATED_ID,
		(IF(cbd.AMOUNT IS NULL, 0, ABS(cbd.AMOUNT))-IF(cbd.bAMOUNT IS NULL,
		0,ABS(cbd.bAMOUNT))) as AMOUNT
		FROM
		T_SALEORDER s
		LEFT JOIN (
		SELECT a.RELATED_ID,ABS(a.AMOUNT),SUM(ABS(b.AMOUNT)) as
		bAMOUNT FROM
		T_CAPITAL_BILL_DETAIL a
		LEFT JOIN T_CAPITAL_BILL_DETAIL b on a.RELATED_ID = b.RELATED_ID AND
		b.BUSSINESS_TYPE = 533 AND b.ORDER_TYPE = 1
		WHERE
		a.BUSSINESS_TYPE = 532 AND a.ORDER_TYPE = 1
		AND a.RELATED_ID IN
		<foreach collection="saleorderIdList" item="saleorderId" open="("
			close=")" separator=",">
			#{saleorderId,jdbcType=INTEGER}
		</foreach>
		GROUP BY a.RELATED_ID
		) cbd on s.SALEORDER_ID = cbd.RELATED_ID
		WHERE
		s.SALEORDER_ID IN
		<foreach collection="saleorderIdList" item="saleorderId" open="("
			close=")" separator=",">
			#{saleorderId,jdbcType=INTEGER}
		</foreach>
	</select>

	<select id="getSaleorderCapitalById" resultType="com.vedeng.order.model.Saleorder"
		parameterType="java.lang.Integer">
		SELECT * FROM (SELECT
		COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(b.tk_amount),0),0) as REAL_AMOUNT
		,a.SALEORDER_ID,
			a.SALEORDER_NO,
			a.TOTAL_AMOUNT,
			a.PREPAID_AMOUNT,
			a.HAVE_ACCOUNT_PERIOD,
			a.PAYMENT_STATUS,
			a.ACCOUNT_PERIOD_AMOUNT,
			a.TRADER_ID,
			a.PAYMENT_TYPE
		FROM
			T_SALEORDER a
		left JOIN
		(
			SELECT
				sum(bb.NUM*cc.PRICE) as tk_amount,aa.ORDER_ID
			FROM
				T_AFTER_SALES aa
			left JOIN
				T_AFTER_SALES_GOODS bb
			ON
				aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
			left JOIN
				T_SALEORDER_GOODS cc
			ON
				bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
			WHERE
				aa.TYPE = 539
			AND
				aa.SUBJECT_TYPE = 535
			AND
				aa.VALID_STATUS = 1
			AND
				aa.ATFER_SALES_STATUS in (1,2)
			AND
				aa.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
			GROUP BY
				aa.ORDER_ID
			) as b
			ON
			a.SALEORDER_ID = b.ORDER_ID
		WHERE
			a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
			) AS s 
			LEFT JOIN (SELECT
			d.SALEORDER_ID,
			if(sum(IF(b.TRADER_TYPE = 1 or b.TRADER_TYPE = 4,ABS(c.AMOUNT),-(ABS(c.AMOUNT)))) is NULL,0,sum(IF(b.TRADER_TYPE = 1 or b.TRADER_TYPE = 4,ABS(c.AMOUNT),-(ABS(c.AMOUNT))))) AS RECEIVED_AMOUNT
			FROM
			 T_CAPITAL_BILL b
			LEFT JOIN T_CAPITAL_BILL_DETAIL c ON c.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN T_SALEORDER d ON c.RELATED_ID = d.SALEORDER_ID
			WHERE
				b.TRADER_TYPE != 3
			AND
				c.ORDER_TYPE = 1
			AND d.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
			GROUP BY d.SALEORDER_ID) sinfo on s.SALEORDER_ID = sinfo.SALEORDER_ID
  </select>
  <!-- 今年已完成 -->
  <select id="getEchartDataVoComplete" resultType="java.math.BigDecimal" parameterType="com.vedeng.homepage.model.vo.EchartsDataVo">
  	SELECT
  	 	COALESCE(SUM(B.AMOUNT),0)/10000
  	FROM T_CAPITAL_BILL A
  	LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
  	LEFT JOIN T_SALEORDER C ON C.SALEORDER_ID = B.RELATED_ID
  	WHERE B.ORDER_TYPE = 1 AND A.TRADER_TYPE IN (1,4) AND FROM_UNIXTIME(A.TRADER_TIME/1000,'%Y') = YEAR(NOW())
  	<if test="companyId != null">
  		and A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  	</if>
  	<if test="orgIdList != null">
  		and C.ORG_ID IN 
  		<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			#{orgId,jdbcType=INTEGER}
		</foreach>
  	</if>
  	GROUP BY FROM_UNIXTIME(TRADER_TIME/1000,'%m')
  	ORDER BY FROM_UNIXTIME(TRADER_TIME/1000,'%m')
  </select>
  
  <!-- 上一年已完成 -->
  <select id="getEchartDataVoCompleteLastYear" resultType="java.math.BigDecimal" parameterType="com.vedeng.homepage.model.vo.EchartsDataVo">
  	SELECT
  	 	COALESCE(SUM(B.AMOUNT),0)/10000
  	FROM T_CAPITAL_BILL A
  	LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
  	LEFT JOIN T_SALEORDER C ON C.SALEORDER_ID = B.RELATED_ID
  	WHERE B.ORDER_TYPE = 1 AND A.TRADER_TYPE IN (1,4) AND FROM_UNIXTIME(A.TRADER_TIME/1000,'%Y') = YEAR(DATE_SUB(NOW(),INTERVAL 1 YEAR))
  	<if test="companyId != null">
  		and A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  	</if>
  	<if test="orgIdList != null">
  		and C.ORG_ID IN 
  		<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			#{orgId,jdbcType=INTEGER}
		</foreach>
  	</if>
  	GROUP BY FROM_UNIXTIME(TRADER_TIME/1000,'%m')
  	ORDER BY FROM_UNIXTIME(TRADER_TIME/1000,'%m')
  </select>
  
  <select id="getSaleEngineerThisMonthMoney" resultType="java.math.BigDecimal" parameterType="com.vedeng.finance.model.CapitalBill">
  	SELECT
  	 	COALESCE(SUM(B.AMOUNT),0)/10000
  	FROM T_CAPITAL_BILL A
  	LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
  	WHERE A.TRADER_TYPE IN (1,4) AND FROM_UNIXTIME(A.TRADER_TIME/1000,'%Y') = YEAR(NOW()) AND FROM_UNIXTIME(A.TRADER_TIME/1000,'%m') = MONTH(NOW())
  			AND B.TRADER_TYPE = 1 AND B.ORDER_TYPE = 1
  	<if test="companyId != null">
  		and A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  	</if>
  	<if test="userId != null and userId != 0">
  		and B.USER_ID = #{userId,jdbcType=INTEGER}
  	</if>
  </select>
  
  <select id="getSaleEngineerAllMoney" resultType="java.math.BigDecimal" parameterType="com.vedeng.finance.model.CapitalBill">
  	SELECT 
  	 	 COALESCE(SUM(B.AMOUNT),0)/10000
  	FROM T_CAPITAL_BILL A
  	LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
  	WHERE A.TRADER_TYPE IN (1,4) AND B.TRADER_TYPE = 1 AND B.ORDER_TYPE = 1 AND B.TRADER_ID != 0
  	<if test="companyId != null">
  		and A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  	</if>
  	<if test="userId != null and userId != 0">
  		and B.USER_ID = #{userId,jdbcType=INTEGER}
  	</if>
  </select>
  
  <select id="getSaleEngineerTurnoverCustomers" resultType="java.lang.Integer" parameterType="com.vedeng.finance.model.CapitalBill">
  	SELECT
  	 	B.TRADER_ID
  	FROM T_CAPITAL_BILL A
  	LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
  	WHERE B.TRADER_TYPE = 1 AND B.ORDER_TYPE = 1 AND B.TRADER_ID != 0
  	<if test="companyId != null">
  		and A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  	</if>
  	<if test="userId != null and userId != 0">
  		and B.USER_ID = #{userId,jdbcType=INTEGER}
  	</if>
  	GROUP BY B.TRADER_ID
  </select>
  
  <select id="getSaleEngineerManyTurnoverCustomers" resultType="java.lang.Integer" parameterType="com.vedeng.finance.model.CapitalBill">
  	SELECT
  	 B.TRADER_ID
  	FROM T_CAPITAL_BILL A
  	LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
  	WHERE B.TRADER_TYPE = 1 AND B.ORDER_TYPE = 1 AND B.TRADER_ID != 0
  	<if test="companyId != null">
  		and A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  	</if>
  	<if test="userId != null and userId != 0">
  		and B.USER_ID = #{userId,jdbcType=INTEGER}
  	</if>
  	GROUP BY B.TRADER_ID HAVING COUNT(B.RELATED_ID) <![CDATA[ > ]]> 1
  </select>


	<resultMap type="com.vedeng.finance.model.CapitalBill" id="waitSyncCapitalBillResult"
		extends="BaseResultMap">
		<association property="capitalBillDetail"
			javaType="com.vedeng.finance.model.CapitalBillDetail">
			<id column="CAPITAL_BILL_DETAIL_ID" property="capitalBillDetailId"
				jdbcType="INTEGER" />
			<result column="BUSSINESS_TYPE" property="bussinessType"
				jdbcType="INTEGER" />
			<result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER" />
			<result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
			<result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
			<result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
			<result column="TRADER_TYPE_ID" property="traderType"
				jdbcType="INTEGER" />
			<result column="USER_ID" property="userId" jdbcType="INTEGER" />
		</association>
	</resultMap>
	<!-- 获取待同步交易记录数据 -->
	<select id="waitSyncCapitalBillList" resultMap="waitSyncCapitalBillResult"
		parameterType="com.vedeng.finance.model.CapitalBill">
		select
		c.*, d.CAPITAL_BILL_DETAIL_ID,
		d.BUSSINESS_TYPE,d.ORDER_TYPE, d.ORDER_NO, d.RELATED_ID, d.TRADER_ID, d.TRADER_TYPE as
		TRADER_TYPE_ID, d.USER_ID
		from T_CAPITAL_BILL c
		join T_CAPITAL_BILL_DETAIL d on c.CAPITAL_BILL_ID = d.CAPITAL_BILL_ID
		where c.CAPITAL_BILL_ID NOT IN (
		SELECT
		a.CAPITAL_BILL_ID
		FROM
		T_CAPITAL_BILL a
		JOIN T_DATA_SYNC_STATUS b ON a.CAPITAL_BILL_ID = b.RELATED_ID
		WHERE
		b.SOURCE_TABLE = 'T_CAPITAL_BILL'
		AND b.GOAL_TYPE = 591
		AND b. STATUS = 1
		)
		order by c.CAPITAL_BILL_ID DESC;
	</select>

	<select id="getCapitalBillById" parameterType="com.vedeng.finance.model.CapitalBill"
		resultType="com.vedeng.finance.model.vo.CapitalBillVo">
		select
		a.CAPITAL_BILL_ID, a.BANK_BILL_ID, a.CAPITAL_BILL_NO, a.COMPANY_ID, a.TRADER_TIME,
		a.TRADER_SUBJECT, a.TRADER_TYPE,
		a.TRADER_MODE, a.AMOUNT, a.CURRENCY_UNIT_ID, a.PAYER, a.PAYEE, a.COMMENTS,
		a.ADD_TIME, a.CREATOR, a.TRAN_FLOW, a.PAYER_BANK_ACCOUNT,
		a.PAYEE_BANK_ACCOUNT, a.PAYER_BANK_NAME, a.PAYEE_BANK_NAME,
		b.BUSSINESS_TYPE, b.ORDER_TYPE, b.ORDER_NO, b.RELATED_ID
		from
		T_CAPITAL_BILL a
		left join
		T_CAPITAL_BILL_DETAIL b
		on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		where
		a.CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
	</select>

	<select id="getAftersaleServiceAmountBill" parameterType="com.vedeng.finance.model.CapitalBill"
		resultType="java.math.BigDecimal">
		select
		COALESCE(SUM(b.AMOUNT),0)
		from
		T_CAPITAL_BILL a
		left join
		T_CAPITAL_BILL_DETAIL b
		on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		where
		a.TRADER_TYPE = 1 and b.ORDER_TYPE = 3 and b.BUSSINESS_TYPE = 526
		and b.ORDER_NO = #{orderNo,jdbcType=VARCHAR}
		and b.RELATED_ID = #{relatedId,jdbcType=INTEGER}
	</select>

	<select id="getzhifubaoAmount" resultType="java.math.BigDecimal">
		SELECT
		COALESCE (sum(ABS(a.AMOUNT)), 0)
		FROM
		T_CAPITAL_BILL a
		JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		WHERE
		b.BUSSINESS_TYPE = 679 and b.RELATED_ID =
		#{saleorderId,jdbcType=INTEGER}
	</select>

	<select id="isAccountPeriod" parameterType="java.lang.Integer"
		resultType="java.lang.Integer">
		SELECT
		count(*)
		FROM
		T_CAPITAL_BILL a
		JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		WHERE
		a.TRADER_MODE = 527
		AND b.BUSSINESS_TYPE = 526
		AND b.ORDER_TYPE = #{orderType,jdbcType=INTEGER}
		AND b.RELATED_ID = #{relatedId,jdbcType=INTEGER}
	</select>
	<select id="getAfterReturnCapitalBillList" parameterType="com.vedeng.finance.model.CapitalBill"
		resultMap="capitalBillListResult">
		<!-- 销售 -->
		<if test="operationType != null and operationType == 'finance_sale_detail'">
			SELECT C.*,
			B.CAPITAL_BILL_DETAIL_ID,
			B.BUSSINESS_TYPE,
			B.ORDER_NO
			FROM T_AFTER_SALES A
			INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID = B.RELATED_ID
			INNER JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
			WHERE A.ORDER_ID = #{capitalBillDetail.relatedId,jdbcType=INTEGER}
			AND A.SUBJECT_TYPE = 535
			AND C.TRADER_TYPE IN (3,5)	<!-- 转移、转出 -->
			AND C.TRADER_MODE IN (521, 529,530)	<!-- 退还银行、信用、余额、支付宝 -->
			AND B.ORDER_TYPE = 3	<!-- 订单类型3售后 -->
			<!-- 交易者ID -->
			<if
				test="capitalBillDetail.traderId != null and capitalBillDetail.traderId != ''">
				AND B.TRADER_ID = #{capitalBillDetail.traderId,jdbcType=INTEGER}
			</if>
			<!-- 所属类型 1::经销商（包含终端）2:供应商 -->
			<if
				test="capitalBillDetail.traderType != null and capitalBillDetail.traderType != ''">
				AND B.TRADER_TYPE = #{capitalBillDetail.traderType,jdbcType=BIT}
			</if>
			AND B.BUSSINESS_TYPE IN (531,533)	<!-- 退款,信用还款 -->
			AND A.VALID_STATUS = 1	<!-- 生效 -->
			AND A.ATFER_SALES_STATUS IN (1, 2)	<!-- 进行中，已完结 -->
			ORDER BY B.CAPITAL_BILL_ID DESC
		</if>
		<!-- 采购 -->
		<if test="operationType != null and operationType == 'finance_buy_detail'">
			SELECT C.*,
			B.CAPITAL_BILL_DETAIL_ID,
			B.BUSSINESS_TYPE,
			B.ORDER_NO
			FROM T_AFTER_SALES A
			INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID = B.RELATED_ID
			INNER JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
			WHERE A.ORDER_ID = #{capitalBillDetail.relatedId,jdbcType=INTEGER}
			AND A.SUBJECT_TYPE = 536
			AND C.TRADER_TYPE IN (1, 3, 4)	<!-- 收入、转移、转入 -->
			AND C.TRADER_MODE IN (521, 529, 530, 10000)	<!-- 退还银行、信用、余额 -->
			AND B.ORDER_TYPE = 3	<!-- 订单类型3售后 -->
			<!-- 交易者ID -->
			<if
				test="capitalBillDetail.traderId != null and capitalBillDetail.traderId != ''">
				AND B.TRADER_ID = #{capitalBillDetail.traderId,jdbcType=INTEGER}
			</if>
			<!-- 所属类型 1::经销商（包含终端）2:供应商 -->
			<if
				test="capitalBillDetail.traderType != null and capitalBillDetail.traderType != ''">
				AND B.TRADER_TYPE = #{capitalBillDetail.traderType,jdbcType=BIT}
			</if>
			AND B.BUSSINESS_TYPE IN (531,533)	<!-- 退款,信用还款 -->
			AND A.VALID_STATUS = 1	<!-- 生效 -->
			AND A.ATFER_SALES_STATUS IN (1, 2)	<!-- 进行中，已完结 -->
			ORDER BY B.CAPITAL_BILL_ID DESC
		</if>
	</select>

	<select id="getCapitalBillTreaderIdList" parameterType="com.vedeng.finance.model.CapitalBill"
		resultType="java.lang.Integer">
		SELECT
		distinct a.TRADER_ID
		FROM T_CAPITAL_BILL_DETAIL a
		INNER JOIN T_CAPITAL_BILL b ON
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		WHERE b.COMPANY_ID = 1
		AND a.ORDER_TYPE in (1,3)
		and a.TRADER_TYPE = 1
		<!-- 查询交易时间 -->
		<if test="startTime != null">
			and b.ADD_TIME <![CDATA[>=]]>
			#{startTime,jdbcType=BIGINT}
		</if>
		<if test="endTime != null">
			and b.ADD_TIME <![CDATA[<=]]>
			#{endTime,jdbcType=BIGINT}
		</if>

	</select>
	
	<select id="getCapitalBillByTranFlow" parameterType="com.vedeng.finance.model.CapitalBill"
		resultType="com.vedeng.finance.model.CapitalBill">
		select
			<include refid="Base_Column_List" />
		from T_CAPITAL_BILL
		where 
			TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR}
		and
			TRADER_SUBJECT = 2
		and
			TRADER_TYPE = #{traderType,jdbcType=BIT}
		and
			TRADER_MODE = #{traderMode,jdbcType=INTEGER}
		LIMIT 1
	</select>
	
	<select id="getTradeModeByOrderNo" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT
			b.TRADER_MODE
		FROM
			T_CAPITAL_BILL_DETAIL a
		LEFT JOIN T_CAPITAL_BILL b ON b.CAPITAL_BILL_ID = a.CAPITAL_BILL_ID
		WHERE
			a.ORDER_NO =#{saleorderNo,jdbcType=VARCHAR}
		ORDER BY b.TRADER_TIME DESC
		LIMIT 1
	</select>

	<select id="getLastestTime" resultType="java.lang.Long">
		SELECT MAX(t1.TRADER_TIME)
			FROM T_CAPITAL_BILL t1
			INNER JOIN T_CAPITAL_BILL_DETAIL t2
			ON t1.CAPITAL_BILL_ID = t2.CAPITAL_BILL_ID
			WHERE t2.ORDER_NO = #{saleorderNo}
	</select>
	<select id="getPayedAmountOfSaleorder" resultType="java.math.BigDecimal">
		select sum(a.AMOUNT)
		from (
				 select a.AMOUNT
				 from T_CAPITAL_BILL a
						  join
					  T_CAPITAL_BILL_DETAIL b
					  on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
				 WHERE b.ORDER_TYPE = 1
				   AND b.RELATED_ID = #{saleOrderId}
				 union all
				 SELECT -C.AMOUNT
				 FROM T_AFTER_SALES A
						  INNER JOIN
					  T_CAPITAL_BILL_DETAIL B
					  ON A.AFTER_SALES_ID = B.RELATED_ID
						  INNER JOIN
					  T_CAPITAL_BILL C
					  ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
				 WHERE A.ORDER_ID = #{saleOrderId}
				   AND A.SUBJECT_TYPE = 535
				   AND C.TRADER_TYPE IN (
										 3, 5
					 )
				   AND C.TRADER_MODE IN (
										 521, 529, 530
					 )
				   AND B.ORDER_TYPE = 3
				   AND B.BUSSINESS_TYPE IN (
											531, 533
					 )
				   AND A.VALID_STATUS = 1
				   AND A.ATFER_SALES_STATUS IN (
												1, 2
					 )
			 ) a
	</select>

    <select id="getSaleOrderCreditPaymentRecord" resultType="java.lang.Integer">
		SELECT
			COUNT( b.CAPITAL_BILL_ID )
		FROM
			T_CAPITAL_BILL b
			LEFT JOIN T_CAPITAL_BILL_DETAIL bd ON b.CAPITAL_BILL_ID = bd.CAPITAL_BILL_ID
		WHERE
			b.TRADER_MODE = 527
			AND b.TRADER_TYPE = 3
			AND bd.ORDER_TYPE = 1
			AND bd.TRADER_TYPE = 1
			AND bd.BUSSINESS_TYPE = 526
			AND bd.RELATED_ID = #{saleOrderId,jdbcType=INTEGER};
	</select>

    <update id="updateCapitalBill">
		update T_CAPITAL_BILL
		set BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
		where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
	</update>

	<update id="updateCapitalBillByBankBillIdAndTranFlow">
		update T_CAPITAL_BILL
		<set>
			<if test="bankBillId != null">
				BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER},
			</if>
			<if test="tranFlow != null">
				TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
			</if>
		</set>
		where CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER}
		<!-- 交易类型 1收入2支出 -->
		and TRADER_TYPE in (1,2)
		<!--交易方式字典库 支付宝520 微信 522-->
		and TRADER_MODE in (520,522)
	</update>


	<update id="batchUpdateCapitalBill">
		update T_CAPITAL_BILL
		set BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
		where CAPITAL_BILL_ID in
		<foreach collection="capitalBillIdList" open="(" close=")" separator="," item="id">
			#{id,jdbcType=INTEGER}
		</foreach>
	</update>

	<select id="queryBuyorderExpenseAfterReturnCapitalBillList" parameterType="com.vedeng.finance.model.CapitalBill"
			resultMap="capitalBillListResult">
		SELECT C.*,
		B.CAPITAL_BILL_DETAIL_ID,
		B.BUSSINESS_TYPE,
		B.ORDER_NO
		FROM T_EXPENSE_AFTER_SALES A
		INNER JOIN T_EXPENSE_AFTER_SALES_STATUS D ON A.EXPENSE_AFTER_SALES_ID = D.EXPENSE_AFTER_SALES_ID
		INNER JOIN T_CAPITAL_BILL_DETAIL B ON A.EXPENSE_AFTER_SALES_ID = B.RELATED_ID
		INNER JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
		WHERE A.BUYORDER_EXPENSE_ID = #{capitalBillDetail.relatedId,jdbcType=INTEGER}
		AND A.EXPENSE_AFTER_SALES_TYPE = 4121
		AND C.TRADER_TYPE IN (1, 3, 4)	<!-- 收入、转移、转入 -->
		AND C.TRADER_MODE IN (521, 529, 530)	<!-- 退还银行、信用、余额 -->
		AND B.ORDER_TYPE = 5	<!-- 订单类型5采购费用售后 -->
		<!-- 交易者ID -->
		<if test="capitalBillDetail.traderId != null and capitalBillDetail.traderId != ''">
			AND B.TRADER_ID = #{capitalBillDetail.traderId,jdbcType=INTEGER}
		</if>
		<!-- 所属类型 1::经销商（包含终端）2:供应商 -->
		<if test="capitalBillDetail.traderType != null and capitalBillDetail.traderType != ''">
			AND B.TRADER_TYPE = #{capitalBillDetail.traderType,jdbcType=BIT}
		</if>
		AND B.BUSSINESS_TYPE IN (531,533)	<!-- 退款,信用还款 -->
		AND D.VALID_STATUS = 1	<!-- 生效 -->
		AND D.AFTER_SALES_STATUS IN (1, 2)	<!-- 进行中，已完结 -->
		ORDER BY B.CAPITAL_BILL_ID DESC
	</select>

</mapper>