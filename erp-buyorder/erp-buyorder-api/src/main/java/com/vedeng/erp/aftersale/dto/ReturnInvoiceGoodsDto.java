package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 财务退票录票模块商品信息
 *
 * <AUTHOR>
@Data
public class ReturnInvoiceGoodsDto  {

    /**
     * 费用售后表主键ID
     */
    private Long expenseAfterSalesId;

    /**
     * 费用单明细表主键ID
     */
    private Integer buyorderExpenseItemId;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 型号
     */
    private String model;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 品牌名
     */
    private String brandName;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     *
     */
    private Integer goodsId;

    /**
     * 本次退票数
     */
    private BigDecimal returnNum;

    /**
     * 退货数量
     */
    private Integer afterReturnNum;

    /**
     * 采购单数量
     */
    private Integer num;

    /**
     * 采购单价
     */
    private BigDecimal price;

    /**
     * 票单价
     */
    private BigDecimal invoicePrice;

    /**
     * 需要退的金额
     */
    private BigDecimal totalAmount;

    /**
     * 输入的当前次sku的退票金额
     */
    private BigDecimal amount;

    /**
     * 退票状态 0未退票 1已退票 2退票中
     */
    private Integer returnInvoiceStatus;

}