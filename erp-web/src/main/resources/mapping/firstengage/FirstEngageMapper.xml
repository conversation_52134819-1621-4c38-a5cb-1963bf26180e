<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.firstengage.dao.FirstEngageMapper">
  
  <resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.FirstEngage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 11:35:21 CST 2019.
    -->
    <id column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="REGISTRATION_NUMBER_ID" jdbcType="INTEGER" property="registrationNumberId" />
    <result column="GOODS_TYPE" jdbcType="INTEGER" property="goodsType" />
    <result column="BRAND_TYPE" jdbcType="INTEGER" property="brandType" />
    <result column="STANDARD_CATEGORY_TYPE" jdbcType="BIT" property="standardCategoryType" />
    <result column="NEW_STANDARD_CATEGORY_ID" jdbcType="INTEGER" property="newStandardCategoryId" />
    <result column="OLD_STANDARD_CATEGORY_ID" jdbcType="INTEGER" property="oldStandardCategoryId" />
    <result column="MANAGE_CATEGORY_LEVEL" jdbcType="INTEGER" property="manageCategoryLevel" />
    <result column="EFFECTIVE_DAYS" jdbcType="INTEGER" property="effectiveDays" />
    <result column="EFFECTIVE_DAY_UNIT" jdbcType="INTEGER" property="effectiveDayUnit" />
    <result column="STATUS" jdbcType="BIT" property="status" />
    <result column="SIGNATURE" jdbcType="BIT" property="signature" />
    <result column="IS_DELETED" jdbcType="BIT" property="isDeleted" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="MOD_TIME_STR" jdbcType="VARCHAR" property="modTimeStr" />
    <result column="CHECK_AGAIN" property="checkAgain" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="CONDITION_ONE" jdbcType="VARCHAR" property="conditionOne" />
      <result column="TEMPERATURE" jdbcType="VARCHAR" property="temperature" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="PRODUCT_COMPANY_CHINESE_NAME" jdbcType="VARCHAR" property="productCompanyChineseName" />
    <result column="NEW_STANDARD_CATEGORY_NAME" jdbcType="VARCHAR" property="newStandardCategoryName" />
    <result column="OLD_STANDARD_CATEGORY_NAME" jdbcType="VARCHAR" property="oldStandardCategoryName" />
    <result column="REGISTRATION_EFFECTIVE_DATE" jdbcType="BIGINT" property="registrationEffectiveDate" />
    <result column="DEAL_STATUS" jdbcType="INTEGER" property="dealStatus" />
      <!--未处理的问题数量-->
      <result column="COUNT_OF_UNDEAL" jdbcType="VARCHAR" property="countOfUndeal"/>
    <!--存储条件标准化 since ERP_SV_2020_61-->
      <result column="STORAGE_CONDITION_TEMPERATURE" jdbcType="BIT" property="storageConditionTemperature" />
      <result column="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionTemperatureLowerValue" />
      <result column="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionTemperatureUpperValue" />
      <result column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityLowerValue" />
      <result column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityUpperValue" />
      <result column="STORAGE_CONDITION_OTHERS" jdbcType="VARCHAR" property="storageConditionOthers" />

      <result column="ASSIGNMENT_MANAGER_ID" jdbcType="INTEGER" property="assignmentManagerId" />
      <result column="ASSIGNMENT_ASSISTANT_ID" jdbcType="INTEGER" property="assignmentAssistantId" />

      <result column="MANUFACTURER_ID" jdbcType="INTEGER" property="manufacturerId" />
      <result column="MANUFACTURER_NAME" jdbcType="INTEGER" property="manufacturerName" />
      <result column="USERNAME" jdbcType="INTEGER" property="userName" />
      <result column="IS_UPLOAD" jdbcType="INTEGER" property="isUpload" />


<!--      <result column="" jdbcType="INTEGER" ></result>-->
    <!-- 商品信息 -->
    <collection property="goodsList" ofType="com.vedeng.goods.model.Goods">
    	<result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    </collection>

  </resultMap>



    <resultMap id="MedicalCategoryMap" type="com.vedeng.firstengage.model.SimpleMedicalCategory">
        <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
        <result column="NEW_STANDARD_CATEGORY_ID" jdbcType="INTEGER" property="newMedicalCategory" />
        <result column="OLD_STANDARD_CATEGORY_ID" jdbcType="INTEGER" property="oldMedicalCategory" />
        <result column="MANAGE_CATEGORY_LEVEL" jdbcType="INTEGER" property="manageCategory" />
        <result column="BASE_CATEGORY_TYPE" jdbcType="INTEGER" property="instrumentType" />
    </resultMap>
  <sql id="Base_Column_List">
    FIRST_ENGAGE_ID, REGISTRATION_NUMBER_ID, GOODS_TYPE, BRAND_TYPE, STANDARD_CATEGORY_TYPE, CHECK_AGAIN,
    NEW_STANDARD_CATEGORY_ID, OLD_STANDARD_CATEGORY_ID, EFFECTIVE_DAYS, EFFECTIVE_DAY_UNIT,
    STATUS,SIGNATURE, IS_DELETED, ADD_TIME, CREATOR, MOD_TIME, UPDATER, COMMENTS, CONDITION_ONE, TEMPERATURE,
    <include refid="GoodsStorageConditionColumn"/>
  </sql>
    <sql id="GoodsStorageConditionColumn">
        STORAGE_CONDITION_TEMPERATURE, STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE, STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE,
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
        STORAGE_CONDITION_OTHERS
    </sql>
    <select id="getSimpleMedicalCategory" resultMap="MedicalCategoryMap">
        select
        U.SKU_NO,SC.SYS_OPTION_DEFINITION_ID AS NEW_STANDARD_CATEGORY_ID, E.OLD_STANDARD_CATEGORY_ID ,
        N.MANAGE_CATEGORY_LEVEL,C.BASE_CATEGORY_TYPE,N.REGISTRATION_NUMBER_ID,E.STANDARD_CATEGORY_TYPE
        from V_CORE_SKU U
        LEFT JOIN V_CORE_SPU P ON U.SPU_ID=P.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE E ON P.FIRST_ENGAGE_ID=E.FIRST_ENGAGE_ID
        LEFT JOIN T_STANDARD_CATEGORY SC ON SC.STANDARD_CATEGORY_ID=E.NEW_STANDARD_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY C ON P.CATEGORY_ID=C.BASE_CATEGORY_ID
        LEFT JOIN T_REGISTRATION_NUMBER N ON E.REGISTRATION_NUMBER_ID=N.REGISTRATION_NUMBER_ID
        where U.SKU_NO =#{skuNo}
    </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_FIRST_ENGAGE
    where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_FIRST_ENGAGE
    where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.firstengage.model.FirstEngage">
    insert into T_FIRST_ENGAGE (FIRST_ENGAGE_ID, REGISTRATION_NUMBER_ID, 
      GOODS_TYPE, BRAND_TYPE, STANDARD_CATEGORY_TYPE, 
      NEW_STANDARD_CATEGORY_ID, OLD_STANDARD_CATEGORY_ID, 
      EFFECTIVE_DAYS, STATUS, 
      IS_DELETED, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, COMMENTS
      )
    values (#{firstEngageId,jdbcType=INTEGER}, #{registrationNumberId,jdbcType=INTEGER}, 
      #{goodsType,jdbcType=INTEGER}, #{brandType,jdbcType=INTEGER}, #{standardCategoryType,jdbcType=BIT}, 
      #{newStandardCategoryId,jdbcType=INTEGER}, #{oldStandardCategoryId,jdbcType=INTEGER}, 
      #{effectiveDays,jdbcType=BIGINT}, #{status,jdbcType=BIT}, 
      #{isDeleted,jdbcType=BIT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{comments,jdbcType=VARCHAR}
      )
  </insert>
  
  <insert id="insertSelective" parameterType="com.vedeng.firstengage.model.FirstEngage" useGeneratedKeys="true" keyProperty="firstEngageId">
    insert into T_FIRST_ENGAGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID,
      </if>
      <if test="registrationNumberId != null">
        REGISTRATION_NUMBER_ID,
      </if>
      <if test="goodsType != null">
        GOODS_TYPE,
      </if>
      <if test="brandType != null">
        BRAND_TYPE,
      </if>
      <if test="standardCategoryType != null">
        STANDARD_CATEGORY_TYPE,
      </if>
      <if test="newStandardCategoryId != null">
        NEW_STANDARD_CATEGORY_ID,
      </if>
      <if test="oldStandardCategoryId != null">
        OLD_STANDARD_CATEGORY_ID,
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS,
      </if>
      <if test="sortDays != null">
        SORT_DAYS,
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT,
      </if>
      <if test="conditionOne != null">
        CONDITION_ONE,
      </if>
        <if test="temperature != null">
            TEMPERATURE,
        </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      <if test="comments != null">
        COMMENTS,
      </if>
        <if test="storageConditionTemperature != null">
            STORAGE_CONDITION_TEMPERATURE,
        </if>
        <if test="storageConditionTemperature== 4">
            STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE,
        </if>
        <if test="storageConditionTemperature== 4">
            STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE,
        </if>
        <if test="storageConditionHumidityLowerValue != null">
            STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
        </if>
        <if test="storageConditionHumidityUpperValue != null">
            STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
        </if>
        <if test="storageConditionOthers != null">
            STORAGE_CONDITION_OTHERS,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="firstEngageId != null">
        #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="registrationNumberId != null">
        #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="brandType != null">
        #{brandType,jdbcType=INTEGER},
      </if>
      <if test="standardCategoryType != null">
        #{standardCategoryType,jdbcType=BIT},
      </if>
      <if test="newStandardCategoryId != null">
        #{newStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="oldStandardCategoryId != null">
        #{oldStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="effectiveDays != null">
        #{effectiveDays,jdbcType=BIGINT},
      </if>
      <if test="sortDays != null">
        #{sortDays, jdbcType=INTEGER},
      </if>
      <if test="effectiveDayUnit != null">
        #{effectiveDayUnit, jdbcType=INTEGER},
      </if>
      <if test="conditionOne != null">
        #{conditionOne,jdbcType=INTEGER},
      </if>
        <if test="temperature != null">
            #{temperature,jdbcType=VARCHAR},
        </if>

      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      unix_timestamp(now())*1000,
      #{creator,jdbcType=INTEGER},
      unix_timestamp(now())*1000,
      #{creator,jdbcType=INTEGER},
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
        <if test="storageConditionTemperature != null">
            #{storageConditionTemperature,jdbcType=BIT},
        </if>
        <if test="storageConditionTemperature== 4">
             #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
        </if>
        <if test="storageConditionTemperature== 4">
            #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
        </if>
        <if test="storageConditionHumidityLowerValue != null">
            #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
        </if>
        <if test="storageConditionHumidityUpperValue != null">
             #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
        </if>
        <if test="storageConditionOthers != null">
           #{storageConditionOthers,jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.firstengage.model.FirstEngage">
    update T_FIRST_ENGAGE
    <set>
      <if test="registrationNumberId != null">
        REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="goodsType != null">
        GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="brandType != null">
        BRAND_TYPE = #{brandType,jdbcType=INTEGER},
      </if>
      <if test="standardCategoryType != null">
        STANDARD_CATEGORY_TYPE = #{standardCategoryType,jdbcType=BIT},
      </if>
      <if test="newStandardCategoryId != null">
        NEW_STANDARD_CATEGORY_ID = #{newStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="oldStandardCategoryId != null">
        OLD_STANDARD_CATEGORY_ID = #{oldStandardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS = #{effectiveDays,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=BIT},
      </if>
        <if test="signature != null">
            SIGNATURE = #{signature,jdbcType=BIT},
        </if>
      <if test="sortDays != null">
        SORT_DAYS = #{sortDays, jdbcType=INTEGER},
      </if>

      <if test="checkAgain != null">
        CHECK_AGAIN = #{checkAgain,jdbcType=BIT},
      </if>

      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=INTEGER},
      </if>
      <if test="conditionOne != null">
        CONDITION_ONE = #{conditionOne,jdbcType=INTEGER},
      </if>
        <if test="temperature != null">
            TEMPERATURE = #{temperature,jdbcType=VARCHAR},
        </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
        MOD_TIME = unix_timestamp(now())*1000,
      	UPDATER = #{updater,jdbcType=INTEGER},
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionTemperature != null">
        STORAGE_CONDITION_TEMPERATURE = #{storageConditionTemperature,jdbcType=TINYINT},
      </if>
      <if test="storageConditionTemperature != null">
          STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE = #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTemperature != null">
          STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE = #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
      </if>
        <if test="storageConditionHumidityLowerValue != null">
            STORAGE_CONDITION_HUMIDITY_LOWER_VALUE= #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
        </if>
        <if test="storageConditionHumidityUpperValue != null">
            STORAGE_CONDITION_HUMIDITY_UPPER_VALUE= #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
        </if>
        <if test="storageConditionOthers != null">
            STORAGE_CONDITION_OTHERS = #{storageConditionOthers,jdbcType=VARCHAR},
        </if>
    </set>
    where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </update>

    <update id="updateSelective" parameterType="com.vedeng.firstengage.model.FirstEngage">
        update T_FIRST_ENGAGE
        <set>
            <if test="registrationNumberId != null">
                REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null">
                GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
            </if>
            <if test="brandType != null">
                BRAND_TYPE = #{brandType,jdbcType=INTEGER},
            </if>
            <if test="standardCategoryType != null">
                STANDARD_CATEGORY_TYPE = #{standardCategoryType,jdbcType=BIT},
            </if>
            <if test="newStandardCategoryId != null">
                NEW_STANDARD_CATEGORY_ID = #{newStandardCategoryId,jdbcType=INTEGER},
            </if>
            <if test="oldStandardCategoryId != null">
                OLD_STANDARD_CATEGORY_ID = #{oldStandardCategoryId,jdbcType=INTEGER},
            </if>
            <if test="effectiveDays != null">
                EFFECTIVE_DAYS = #{effectiveDays,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=BIT},
            </if>
            <if test="sortDays != null">
                SORT_DAYS = #{sortDays, jdbcType=INTEGER},
            </if>

            <if test="checkAgain != null">
                CHECK_AGAIN = #{checkAgain,jdbcType=BIT},
            </if>

            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=BIT},
            </if>
            <if test="effectiveDayUnit != null">
                EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=INTEGER},
            </if>
            <if test="conditionOne != null">
                CONDITION_ONE = #{conditionOne,jdbcType=INTEGER},
            </if>
            <if test="temperature != null">
                TEMPERATURE = #{temperature,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="storageConditionTemperature != null">
                STORAGE_CONDITION_TEMPERATURE = #{storageConditionTemperature,jdbcType=TINYINT},
            </if>
            <if test="storageConditionTemperature != null">
                STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE = #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
            </if>
            <if test="storageConditionTemperature != null">
                STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE = #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
            </if>
            <if test="storageConditionHumidityLowerValue != null">
                STORAGE_CONDITION_HUMIDITY_LOWER_VALUE= #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
            </if>
            <if test="storageConditionHumidityUpperValue != null">
                STORAGE_CONDITION_HUMIDITY_UPPER_VALUE= #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
            </if>
            <if test="storageConditionOthers != null">
                STORAGE_CONDITION_OTHERS = #{storageConditionOthers,jdbcType=VARCHAR},
            </if>
        </set>
        where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
    </update>
  
  
  <update id="updateByPrimaryKey" parameterType="com.vedeng.firstengage.model.FirstEngage">
    update T_FIRST_ENGAGE
    set REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      BRAND_TYPE = #{brandType,jdbcType=INTEGER},
      STANDARD_CATEGORY_TYPE = #{standardCategoryType,jdbcType=BIT},
      NEW_STANDARD_CATEGORY_ID = #{newStandardCategoryId,jdbcType=INTEGER},
      OLD_STANDARD_CATEGORY_ID = #{oldStandardCategoryId,jdbcType=INTEGER},
      EFFECTIVE_DAYS = #{effectiveDays,jdbcType=BIGINT},
      STATUS = #{status,jdbcType=BIT},
      IS_DELETED = #{isDeleted,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      COMMENTS = #{comments,jdbcType=VARCHAR}
    where FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </update>
  
  <select id="getStatusCountByParam" resultType="java.util.Map">
    SELECT
        COALESCE(COUNT(a.`STATUS`), 0) as total,
        COALESCE(SUM(IF(a.`STATUS` = 1, 1, 0)), 0) AS one,
        COALESCE(SUM(IF(a.`STATUS` = 3, 1, 0)), 0) AS two,
        COALESCE(SUM(IF(a.`STATUS` = 2, 1, 0)), 0) AS three,
        COALESCE(SUM(IF(a.`STATUS` = 5, 1, 0)), 0) AS five
    FROM
        T_FIRST_ENGAGE a
    LEFT JOIN T_REGISTRATION_NUMBER b ON a.REGISTRATION_NUMBER_ID = b.REGISTRATION_NUMBER_ID
    LEFT JOIN T_PRODUCT_COMPANY c ON b.PRODUCT_COMPANY_ID = c.PRODUCT_COMPANY_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION e ON a.NEW_STANDARD_CATEGORY_ID = e.SYS_OPTION_DEFINITION_ID
    AND e.`STATUS` = 1

     WHERE
         1 = 1
     AND a.IS_DELETED = 0
   </select>
    <!-- LEFT JOIN T_GOODS f ON a.FIRST_ENGAGE_ID = f.FIRST_ENGAGE_ID
      AND f.IS_ON_SALE = 1
      AND f.IS_DISCARD = 0-->
   <!-- 首营列表 -->
  <select id="getFirstEngageInfoList" resultMap="BaseResultMap">
		SELECT
			a.FIRST_ENGAGE_ID,
			b.REGISTRATION_NUMBER,
			b.REGISTRATION_NUMBER_ID,
			c.PRODUCT_COMPANY_CHINESE_NAME,
			a.NEW_STANDARD_CATEGORY_ID,
			<!-- d.TITLE AS NEW_STANDARD_CATEGORY_NAME, -->
			e.TITLE AS OLD_STANDARD_CATEGORY_NAME,
			b.EFFECTIVE_DATE AS REGISTRATION_EFFECTIVE_DATE,
			a.EFFECTIVE_DAYS,
			COALESCE(a.EFFECTIVE_DAY_UNIT, 1) AS EFFECTIVE_DAY_UNIT,
			a.MOD_TIME,
			a.`STATUS`,
			b.DEAL_STATUS,
			b.MANAGE_CATEGORY_LEVEL,
            us.USERNAME,
            pro.COUNT_OF_UNDEAL
		FROM
			T_FIRST_ENGAGE a
		LEFT JOIN T_REGISTRATION_NUMBER b ON a.REGISTRATION_NUMBER_ID = b.REGISTRATION_NUMBER_ID
		LEFT JOIN T_PRODUCT_COMPANY c ON b.PRODUCT_COMPANY_ID = c.PRODUCT_COMPANY_ID
		LEFT JOIN T_SYS_OPTION_DEFINITION e ON a.OLD_STANDARD_CATEGORY_ID = e.SYS_OPTION_DEFINITION_ID AND e.`STATUS` = 1
        LEFT JOIN (
            SELECT
            trfr.REGISTRATION_NUMBER_ID,
            COUNT(IF (trfr.STATE = 0,trfr.REGISTRATION_FEEDBACK_RECORD_ID,NULL)) COUNT_OF_UNDEAL,
            COUNT(IF (trfr.STATE = 1,trfr.REGISTRATION_FEEDBACK_RECORD_ID,NULL)) PROBLEM_FINISH_NUM,
            COUNT(trfr.REGISTRATION_FEEDBACK_RECORD_ID) PROBLEM_TOTAL_NUM
            FROM
            T_REGISTRATION_FEEDBACK_RECORD trfr
            GROUP BY
            trfr.REGISTRATION_NUMBER_ID
        ) pro ON
        b.REGISTRATION_NUMBER_ID = pro.REGISTRATION_NUMBER_ID
        LEFT JOIN (
            SELECT
                tfe.FIRST_ENGAGE_ID,
                group_concat(DISTINCT tu.USERNAME SEPARATOR '&amp;') USERNAME
            FROM
                T_FIRST_ENGAGE tfe
            LEFT JOIN V_CORE_SPU vcs on
                tfe.FIRST_ENGAGE_ID = vcs.FIRST_ENGAGE_ID
            LEFT JOIN T_USER tu on
                vcs.ASSIGNMENT_MANAGER_ID = tu.USER_ID
                OR vcs.ASSIGNMENT_ASSISTANT_ID = tu.USER_ID
            WHERE
                tfe.IS_DELETED = 0
            AND vcs.STATUS = 1
            AND tu.IS_DISABLED = 0
            <if test="null != firstEngage.userId and firstEngage.userId !=-1">
            AND (vcs.ASSIGNMENT_MANAGER_ID = #{firstEngage.userId,jdbcType=INTEGER} or vcs.ASSIGNMENT_ASSISTANT_ID = #{firstEngage.userId,jdbcType=INTEGER})
            </if>
            GROUP BY
                tfe.FIRST_ENGAGE_ID
        ) us on
        a.FIRST_ENGAGE_ID = us.FIRST_ENGAGE_ID

		WHERE
			1 = 1
		AND a.IS_DELETED = 0
		<!-- 首映信息审核状态 -->
		<if test="null != status">
			AND a.`STATUS` = #{status, jdbcType=INTEGER}
		</if>

        <!-- 已修改 -->
		<!--&lt;!&ndash; 首营信息有效期起始时间 &ndash;&gt;-->
		<if test="null != firstEngageDateStart">
			AND b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{firstEngageDateStart, jdbcType=BIGINT}
		</if>
		<!--&lt;!&ndash; firstEndTime 首营信息有效期结束时间 &ndash;&gt;-->
		<if test="null != firstEngageDateEnd">
			AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageDateEnd, jdbcType=BIGINT}
		</if>

        <if test="null != firstEngageSDateStart or null != firstEngageThDateStart or null != firstEngageODateStart or null != firstEngageEDateEnd">

            <trim prefix="AND( " suffix=" )" prefixOverrides="OR">
                <if test="null != firstEngageSDateStart">
                   OR(   b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{firstEngageSDateStart, jdbcType=BIGINT}
                </if>
                <if test="null != firstEngageSDateEnd">
                  AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageSDateEnd, jdbcType=BIGINT})
                </if>
                <if test="null != firstEngageThDateStart">
                   OR(   b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{firstEngageThDateStart, jdbcType=BIGINT}
                </if>
                <if test="null != firstEngageThDateEnd">
                  AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageThDateEnd, jdbcType=BIGINT})
                </if>
                <if test="null != firstEngageODateStart">
                   OR(   b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{firstEngageODateStart, jdbcType=BIGINT}
                </if>
                <if test="null != firstEngageODateEnd">
                  AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageODateEnd, jdbcType=BIGINT})
                </if>
                <if test="null != firstEngageEDateEnd">
                  OR b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageEDateEnd, jdbcType=BIGINT}
                </if>
              </trim>
        </if>

		<!--&lt;!&ndash; 首营信息有效期起始时间 &ndash;&gt;-->
		<if test="null != registrationStartTime">
			AND b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{registrationStartTime, jdbcType=BIGINT}
		</if>
		<!--&lt;!&ndash; firstEndTime 首营信息有效期结束时间 &ndash;&gt;-->
		<if test="null != registrationEndTime">
			AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{registrationEndTime, jdbcType=BIGINT}
		</if>

		<if test="null != firstEngage.dealStatus and firstEngage.dealStatus != -1">
			AND b.DEAL_STATUS = #{firstEngage.dealStatus, jdbcType=INTEGER}
		</if>
      <if test="null != keyWords and keyWords != '' ">
		<if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 1 ">
			AND (b.REGISTRATION_NUMBER LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%') OR c.PRODUCT_COMPANY_CHINESE_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%'))
		</if>

		<if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 2">
			AND b.REGISTRATION_NUMBER LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
		</if>

		<if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 3">
			AND c.PRODUCT_COMPANY_CHINESE_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
		</if>
      </if>
      <!-- 归属人 -->
      <if test="null != firstEngage.userId and firstEngage.userId != -1">
          AND us.USERNAME IS NOT NULL
      </if>
      <!-- 有问题未处理 -->
      <if test="null != firstEngage.unDealStatus and firstEngage.unDealStatus == 0">
          AND pro.PROBLEM_TOTAL_NUM IS NOT NULL
          AND pro.COUNT_OF_UNDEAL > 0
      </if>
      <!-- 有问题已处理 -->
      <if test="null != firstEngage.unDealStatus and firstEngage.unDealStatus == 2">
          AND pro.PROBLEM_TOTAL_NUM IS NOT NULL
          AND pro.PROBLEM_FINISH_NUM = pro.PROBLEM_TOTAL_NUM
      </if>
      <!-- 无问题 -->
      <if test="null != firstEngage.unDealStatus and firstEngage.unDealStatus == 1">
          AND pro.PROBLEM_TOTAL_NUM IS NULL
      </if>

      GROUP BY
      a.FIRST_ENGAGE_ID
        ORDER BY

        <if test="null != firstEngage.timeSort and firstEngage.timeSort == 1">
          a.MOD_TIME DESC,
        </if>

        <if test="null != firstEngage.timeSort and firstEngage.timeSort == 2">
          a.MOD_TIME ASC,
        </if>

        <if test="null != firstEngage.timeSort and firstEngage.timeSort == 3">
          b.EFFECTIVE_DATE DESC,
        </if>

        <if test="null != firstEngage.timeSort and firstEngage.timeSort == 4">
          b.EFFECTIVE_DATE ASC,
        </if>

        <if test="null != firstEngage.timeSort and firstEngage.timeSort == 5">
          CASE when a.SORT_DAYS = 0
            then 0
            else 1
            end desc,
            a.SORT_DAYS asc,
        </if>

        <if test="null != firstEngage.timeSort and firstEngage.timeSort == 6">
          CASE when a.SORT_DAYS = 0
            then 0
            else 1
            end desc,
            a.SORT_DAYS desc,
        </if>
          a.FIRST_ENGAGE_ID DESC
  </select>

    <select id="getAssignment" parameterType="integer" resultMap="BaseResultMap">
        SELECT c.USERNAME AS ASSIGNMENT_MANAGER_USERNAME,
               d.USERNAME AS ASSIGNMENT_ASSISTANT_USERNAME
        FROM T_FIRST_ENGAGE a
                 LEFT JOIN V_CORE_SPU b on a.FIRST_ENGAGE_ID = b.FIRST_ENGAGE_ID
                 LEFT JOIN T_USER c on b.ASSIGNMENT_MANAGER_ID = c.USER_ID
                 LEFT JOIN T_USER d on b.ASSIGNMENT_ASSISTANT_ID = d.USER_ID
        WHERE
            a.FIRST_ENGAGE_ID = #{FirstEngageId,jdbcType=INTEGER}
        GROUP BY
            a.FIRST_ENGAGE_ID
    </select>

    <select id="getProductCompanyInfoList" resultMap="BaseResultMap">
        select

            b.ASSIGNMENT_MANAGER_ID,
            b.ASSIGNMENT_ASSISTANT_ID,
            d.MANUFACTURER_ID,
            f.USERNAME,
            d.IS_UPLOAD,
           -- e.related_id,
            -- e.ATTACHMENT_TYPE,
            d.MANUFACTURER_NAME
            from  T_FIRST_ENGAGE a
            left join V_CORE_SPU b
            on a.FIRST_ENGAGE_ID = b.FIRST_ENGAGE_ID
            left join T_REGISTRATION_NUMBER c
            on a.REGISTRATION_NUMBER_ID = c.REGISTRATION_NUMBER_ID
            left join T_MANUFACTURER d on d.MANUFACTURER_id = c.MANUFACTURER_ID
            left join T_USER f on b.ASSIGNMENT_MANAGER_ID = f.user_id
            WHERE
			1 = 1
		    AND a.IS_DELETED = 0
            AND d.MANUFACTURER_ID is not null
            <if test="null != keyWords and keyWords != '' ">
                AND (d.MANUFACTURER_NAME like CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%'))
            </if>

            <if test=" firstEngage.isUpload !=null ">
                AND d.IS_UPLOAD=#{firstEngage.isUpload,jdbcType=INTEGER}
            </if>

            <if test="firstEngage.assignmentManagerId !=null">
                AND f.USER_ID=#{firstEngage.assignmentManagerId}
            </if>

    </select>

    <select id="findUserName" resultType="com.vedeng.authorization.model.User">
        seLect user_id,username  from  T_USER order by username asc
    </select>



    <select id="getFirstEngageInfoList_COUNT" resultType="Long">
        SELECT
       count(1)
        FROM
        T_FIRST_ENGAGE a
        LEFT JOIN T_REGISTRATION_NUMBER b ON a.REGISTRATION_NUMBER_ID = b.REGISTRATION_NUMBER_ID
        LEFT JOIN T_PRODUCT_COMPANY c ON b.PRODUCT_COMPANY_ID = c.PRODUCT_COMPANY_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION e ON a.OLD_STANDARD_CATEGORY_ID = e.SYS_OPTION_DEFINITION_ID AND e.`STATUS` = 1
        WHERE
        1 = 1
        AND a.IS_DELETED = 0
        <!-- 首映信息审核状态 -->
        <if test="null != status">
            AND a.`STATUS` = #{status, jdbcType=INTEGER}
        </if>

        <!--&lt;!&ndash; 首营信息有效期起始时间 &ndash;&gt;-->
        <if test="null != firstEngageDateStart">
            AND b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{firstEngageDateStart, jdbcType=BIGINT}
        </if>
        <!--&lt;!&ndash; firstEndTime 首营信息有效期结束时间 &ndash;&gt;-->
        <if test="null != firstEngageDateEnd">
            AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageDateEnd, jdbcType=BIGINT}
        </if>
        <if test="null != firstEngageSDateStart or null != firstEngageThDateStart or null != firstEngageODateStart or null != firstEngageEDateEnd">

            <trim prefix="AND( " suffix=" )" prefixOverrides="OR">
                <if test="null != firstEngageSDateStart">
                   OR(   b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{firstEngageSDateStart, jdbcType=BIGINT}
                </if>
                <if test="null != firstEngageSDateEnd">
                  AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageSDateEnd, jdbcType=BIGINT})
                </if>
                <if test="null != firstEngageThDateStart">
                   OR(   b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{firstEngageThDateStart, jdbcType=BIGINT}
                </if>
                <if test="null != firstEngageThDateEnd">
                  AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageThDateEnd, jdbcType=BIGINT})
                </if>
                <if test="null != firstEngageODateStart">
                   OR(   b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{firstEngageODateStart, jdbcType=BIGINT}
                </if>
                <if test="null != firstEngageODateEnd">
                  AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageODateEnd, jdbcType=BIGINT})
                </if>
                <if test="null != firstEngageEDateEnd">
                  OR b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{firstEngageEDateEnd, jdbcType=BIGINT}
                </if>
              </trim>
        </if>
        <!--&lt;!&ndash; 首营信息有效期起始时间 &ndash;&gt;-->
        <if test="null != registrationStartTime">
            AND b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{registrationStartTime, jdbcType=BIGINT}
        </if>
        <!--&lt;!&ndash; firstEndTime 首营信息有效期结束时间 &ndash;&gt;-->
        <if test="null != registrationEndTime">
            AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{registrationEndTime, jdbcType=BIGINT}
        </if>

        <if test="null != firstEngage.dealStatus and firstEngage.dealStatus != -1">
            AND b.DEAL_STATUS = #{firstEngage.dealStatus, jdbcType=INTEGER}
        </if>

        <if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 1">
            AND (b.REGISTRATION_NUMBER LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%') OR c.PRODUCT_COMPANY_CHINESE_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%'))
        </if>

        <if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 2">
            AND b.REGISTRATION_NUMBER LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
        </if>

        <if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 3">
            AND c.PRODUCT_COMPANY_CHINESE_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
        </if>
</select>



    <!-- 查询各状态数量 -->
    <select id="getCountAll" resultType="com.vedeng.goods.model.GoodsCount">
        select `STATUS`,count(*) as count from T_FIRST_ENGAGE GROUP BY `STATUS`
    </select>

  <!-- 首营列表 -->
  <select id="getOverDateCount" resultType="java.lang.Integer">
		SELECT
			count(*)
		FROM
			T_FIRST_ENGAGE a
		LEFT JOIN T_REGISTRATION_NUMBER b ON a.REGISTRATION_NUMBER_ID = b.REGISTRATION_NUMBER_ID
		LEFT JOIN T_PRODUCT_COMPANY c ON b.PRODUCT_COMPANY_ID = c.PRODUCT_COMPANY_ID
		WHERE
			1 = 1
		AND a.IS_DELETED = 0
		<!-- 首映信息审核状态 -->
		<if test="null != status">
			AND a.`STATUS` = #{status, jdbcType=INTEGER}
		</if>

		<!-- 首营信息有效期起始时间 -->
		<if test="null != overDateStart">
			AND b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{overDateStart, jdbcType=BIGINT}
		</if>
		<!-- firstEndTime 首营信息有效期结束时间 -->
		<if test="null != overDateEnd">
			AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{overDateEnd, jdbcType=BIGINT}
		</if>

		<!-- 首营信息有效期起始时间 -->
		<if test="null != registrationStartTime">
			AND b.EFFECTIVE_DATE <![CDATA[ >= ]]> #{registrationStartTime, jdbcType=BIGINT}
		</if>
		<!-- firstEndTime 首营信息有效期结束时间 -->
		<if test="null != registrationEndTime">
			AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{registrationEndTime, jdbcType=BIGINT}
		</if>

		<if test="null != firstEngage.dealStatus and firstEngage.dealStatus != -1">
			AND b.DEAL_STATUS = #{firstEngage.dealStatus, jdbcType=INTEGER}
		</if>

		<if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 1">
			AND (b.REGISTRATION_NUMBER LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%') OR c.PRODUCT_COMPANY_CHINESE_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%'))
		</if>

		<if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 2">
			AND b.REGISTRATION_NUMBER LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
		</if>

		<if test="null != firstEngage.searchStatus and firstEngage.searchStatus == 3">
			AND c.PRODUCT_COMPANY_CHINESE_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%')
		</if>


  </select>


    <select id="getOldStandardId" resultType="java.lang.Integer">
        SELECT SYS_OPTION_DEFINITION_ID from T_SYS_OPTION_DEFINITION where TITLE = #{cellval, jdbcType=INTEGER}
        limit 1
    </select>

    <select id="getNewStandardId" resultType="java.lang.Integer">
        SELECT  a.STANDARD_CATEGORY_ID  FROM `T_STANDARD_CATEGORY` a LEFT JOIN T_STANDARD_CATEGORY b on a.PARENT_ID=b.standard_category_id
        LEFT JOIN T_STANDARD_CATEGORY c on b.PARENT_ID=c.STANDARD_CATEGORY_ID
        where a.CATEGORY_NAME like CONCAT(#{cc,jdbcType=VARCHAR},'%')
        and b.CATEGORY_NAME like CONCAT(#{cb,jdbcType=VARCHAR},'%')
        and c.CATEGORY_NAME like CONCAT(#{ca,jdbcType=VARCHAR},'%')
        limit 1
    </select>

    <insert id="addProductCompany" parameterType="com.task.model.ReadFirst" useGeneratedKeys="true" keyProperty="productCompanyId">
        insert into T_PRODUCT_COMPANY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productCompanyChineseName != null">
                PRODUCT_COMPANY_CHINESE_NAME,
            </if>
            <if test="productEnglishName != null">
                PRODUCT_COMPANY_ENGLISH_NAME,
            </if>
            <if test="productCompanyAddress != null">
                PRODUCT_COMPANY_ADDRESS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productCompanyChineseName != null">
                #{productCompanyChineseName,jdbcType=VARCHAR},
            </if>
            <if test="productEnglishName != null">
                #{productEnglishName,jdbcType=VARCHAR},
            </if>
            <if test="productCompanyAddress != null">
                #{productCompanyAddress,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="addRegisterNumber" parameterType="com.task.model.ReadFirst" useGeneratedKeys="true" keyProperty="registrationNumberId">
        insert into T_REGISTRATION_NUMBER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="registrationNumber != null">
                REGISTRATION_NUMBER,
            </if>
            <if test="manageCategoryLevel != null">
                MANAGE_CATEGORY_LEVEL,
            </if>
            <if test="productCompanyId != null">
                PRODUCT_COMPANY_ID,
            </if>
            <if test="productionAddress != null">
                PRODUCTION_ADDRESS,
            </if>
            <if test="productChineseName != null">
                PRODUCT_CHINESE_NAME,
            </if>
            <if test="productEnglishName != null">
                PRODUCT_ENGLISH_NAME,
            </if>
            <if test="productCategoryId != null">
                PRODUCT_CATEGORY_ID,
            </if>
            <if test="productCategoryName != null">
                PRODUCT_CATEGORY_NAME,
            </if>
            <if test="model != null">
                MODEL,
            </if>
            <if test="issuingDate != null">
                ISSUING_DATE,
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE,
            </if>
            <if test="approvalDepartment != null">
                APPROVAL_DEPARTMENT,
            </if>
            <if test="trademark != null">
                TRADEMARK,
            </if>
            <if test="zipCode != null">
                ZIP_CODE,
            </if>
            <if test="registeredAgent != null">
                REGISTERED_AGENT,
            </if>
            <if test="registeredAgentAddress != null">
                REGISTERED_AGENT_ADDRESS,
            </if>
            <if test="proPerfStruAndComp != null">
                PRO_PERF_STRU_AND_COMP,
            </if>
            <if test="productUseRange != null">
                PRODUCT_USE_RANGE,
            </if>
            <if test="otherContents != null">
                OTHER_CONTENTS,
            </if>
            <if test="productStandards != null">
                PRODUCT_STANDARDS,
            </if>
            <if test="productionOrCountry != null">
                PRODUCTION_OR_COUNTRY,
            </if>
            <if test="remarks != null">
                REMARKS,
            </if>
            <if test="aftersaleServiceOrg != null">
                AFTERSALE_SERVICE_ORG,
            </if>
            <if test="changeDate != null">
                CHANGE_DATE,
            </if>
            <if test="changeContents != null">
                CHANGE_CONTENTS,
            </if>
            <if test="isRelease != null">
                IS_RELEASE,
            </if>
            <if test="expectedUsage != null">
                EXPECTED_USAGE,
            </if>
            <if test="storageCondAndEffectiveDate != null">
                STORAGE_COND_AND_EFFECTIVE_DATE,
            </if>
            <if test="mainProPerfStruAndComp != null">
                MAIN_PRO_PERF_STRU_AND_COMP,
            </if>
            ADD_TIME,
            MOD_TIME,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="registrationNumber != null">
                #{registrationNumber,jdbcType=VARCHAR},
            </if>
            <if test="manageCategoryLevel != null">
                #{manageCategoryLevel,jdbcType=INTEGER},
            </if>
            <if test="productCompanyId != null">
                #{productCompanyId,jdbcType=INTEGER},
            </if>
            <if test="productionAddress != null">
                #{productionAddress,jdbcType=VARCHAR},
            </if>
            <if test="productChineseName != null">
                #{productChineseName,jdbcType=VARCHAR},
            </if>
            <if test="productEnglishName != null">
                #{productEnglishName,jdbcType=VARCHAR},
            </if>
            <if test="productCategoryId != null">
                #{productCategoryId,jdbcType=INTEGER},
            </if>
            <if test="productCategoryName != null">
                #{productCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                #{model,jdbcType=VARCHAR},
            </if>
            <if test="issuingDate != null">
                #{issuingDate,jdbcType=BIGINT},
            </if>
            <if test="effectiveDate != null">
                #{effectiveDate,jdbcType=BIGINT},
            </if>
            <if test="approvalDepartment != null">
                #{approvalDepartment,jdbcType=VARCHAR},
            </if>
            <if test="trademark != null">
                #{trademark,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="registeredAgent != null">
                #{registeredAgent,jdbcType=VARCHAR},
            </if>
            <if test="registeredAgentAddress != null">
                #{registeredAgentAddress,jdbcType=VARCHAR},
            </if>
            <if test="proPerfStruAndComp != null">
                #{proPerfStruAndComp,jdbcType=VARCHAR},
            </if>
            <if test="productUseRange != null">
                #{productUseRange,jdbcType=VARCHAR},
            </if>
            <if test="otherContents != null">
                #{otherContents,jdbcType=VARCHAR},
            </if>
            <if test="productStandards != null">
                #{productStandards,jdbcType=VARCHAR},
            </if>
            <if test="productionOrCountry != null">
                #{productionOrCountry,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="aftersaleServiceOrg != null">
                #{aftersaleServiceOrg,jdbcType=VARCHAR},
            </if>
            <if test="changeDate != null">
                #{changeDate,jdbcType=BIGINT},
            </if>
            <if test="changeContents != null">
                #{changeContents,jdbcType=VARCHAR},
            </if>
            <if test="isRelease != null">
                #{isRelease,jdbcType=BIT},
            </if>
            <if test="expectedUsage != null">
                #{expectedUsage,jdbcType=VARCHAR},
            </if>
            <if test="storageCondAndEffectiveDate != null">
                #{storageCondAndEffectiveDate,jdbcType=VARCHAR},
            </if>
            <if test="mainProPerfStruAndComp != null">
                #{mainProPerfStruAndComp,jdbcType=VARCHAR},
            </if>
            unix_timestamp(now())*1000,
            unix_timestamp(now())*1000,
        </trim>
    </insert>

    <update id="updateExcelRegisterNumber" parameterType="com.task.model.ReadFirst" >
       update T_REGISTRATION_NUMBER set

            <if test="manageCategoryLevel != null">
                MANAGE_CATEGORY_LEVEL=#{manageCategoryLevel,jdbcType=INTEGER},
            </if>
            <if test="productCompanyId != null">
                PRODUCT_COMPANY_ID=#{productCompanyId,jdbcType=INTEGER},
            </if>
            <if test="productionAddress != null">
                PRODUCTION_ADDRESS =#{productionAddress,jdbcType=VARCHAR},
            </if>
            <if test="productChineseName != null">
                PRODUCT_CHINESE_NAME= #{productChineseName,jdbcType=VARCHAR},
            </if>
            <if test="productEnglishName != null">
                PRODUCT_ENGLISH_NAME=#{productEnglishName,jdbcType=VARCHAR},
            </if>
            <if test="productCategoryId != null">
                PRODUCT_CATEGORY_ID=  #{productCategoryId,jdbcType=INTEGER},
            </if>
            <if test="productCategoryName != null">
                PRODUCT_CATEGORY_NAME= #{productCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                MODEL= #{model,jdbcType=VARCHAR},
            </if>
            <if test="issuingDate != null">
                ISSUING_DATE= #{issuingDate,jdbcType=BIGINT},
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE= #{effectiveDate,jdbcType=BIGINT},
            </if>
            <if test="approvalDepartment != null">
                APPROVAL_DEPARTMENT= #{approvalDepartment,jdbcType=VARCHAR},
            </if>
            <if test="trademark != null">
                TRADEMARK= #{trademark,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZIP_CODE= #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="registeredAgent != null">
                REGISTERED_AGENT=  #{registeredAgent,jdbcType=VARCHAR},
            </if>
            <if test="registeredAgentAddress != null">
                REGISTERED_AGENT_ADDRESS= #{registeredAgentAddress,jdbcType=VARCHAR},
            </if>
            <if test="proPerfStruAndComp != null">
                PRO_PERF_STRU_AND_COMP= #{proPerfStruAndComp,jdbcType=VARCHAR},
            </if>
            <if test="productUseRange != null">
                PRODUCT_USE_RANGE=#{productUseRange,jdbcType=VARCHAR},
            </if>
            <if test="otherContents != null">
                OTHER_CONTENTS= #{otherContents,jdbcType=VARCHAR},
            </if>
            <if test="productStandards != null">
                PRODUCT_STANDARDS= #{productStandards,jdbcType=VARCHAR},
            </if>
            <if test="productionOrCountry != null">
                PRODUCTION_OR_COUNTRY= #{productionOrCountry,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                REMARKS= #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="aftersaleServiceOrg != null">
                AFTERSALE_SERVICE_ORG= #{aftersaleServiceOrg,jdbcType=VARCHAR},
            </if>
            <if test="changeDate != null">
                CHANGE_DATE=   #{changeDate,jdbcType=BIGINT},
            </if>
            <if test="changeContents != null">
                CHANGE_CONTENTS=  #{changeContents,jdbcType=VARCHAR},
            </if>
            <if test="isRelease != null">
                IS_RELEASE= #{isRelease,jdbcType=BIT},
            </if>
            <if test="expectedUsage != null">
                EXPECTED_USAGE= #{expectedUsage,jdbcType=VARCHAR},
            </if>
            <if test="storageCondAndEffectiveDate != null">
                STORAGE_COND_AND_EFFECTIVE_DATE=#{storageCondAndEffectiveDate,jdbcType=VARCHAR},
            </if>
            <if test="mainProPerfStruAndComp != null">
                MAIN_PRO_PERF_STRU_AND_COMP=  #{mainProPerfStruAndComp,jdbcType=VARCHAR},
            </if>
            ADD_TIME= unix_timestamp(now())*1000,
            MOD_TIME= unix_timestamp(now())*1000
            where  REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR}
    </update>

    <insert id="addFirstInfo" parameterType="com.task.model.ReadFirst">
        insert into T_FIRST_ENGAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="registrationNumberId != null">
                REGISTRATION_NUMBER_ID,
            </if>
            <if test="goodsType != null">
                GOODS_TYPE,
            </if>
            <if test="brandType != null">
                BRAND_TYPE,
            </if>
            <if test="standardCategoryType != null">
                STANDARD_CATEGORY_TYPE,
            </if>
            <if test="newStandardCategoryId != null">
                NEW_STANDARD_CATEGORY_ID,
            </if>
            <if test="oldStandardCategoryId != null">
                OLD_STANDARD_CATEGORY_ID,
            </if>
            <if test="effectiveDays != null">
                EFFECTIVE_DAYS,
            </if>
            <if test="sortDays != null">
                SORT_DAYS,
            </if>
            <if test="effectiveDayUnit != null">
                EFFECTIVE_DAY_UNIT,
            </if>
            <if test="conditionOne != null">
                CONDITION_ONE,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            ADD_TIME,
            MOD_TIME,
            <if test="comments != null">
                COMMENTS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="registrationNumberId != null">
                #{registrationNumberId,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null">
                #{goodsType,jdbcType=INTEGER},
            </if>
            <if test="brandType != null">
                #{brandType,jdbcType=INTEGER},
            </if>
            <if test="standardCategoryType != null">
                #{standardCategoryType,jdbcType=BIT},
            </if>
            <if test="newStandardCategoryId != null">
                #{newStandardCategoryId,jdbcType=INTEGER},
            </if>
            <if test="oldStandardCategoryId != null">
                #{oldStandardCategoryId,jdbcType=INTEGER},
            </if>
            <if test="effectiveDays != null">
                #{effectiveDays,jdbcType=BIGINT},
            </if>
            <if test="sortDays != null">
                #{sortDays, jdbcType=INTEGER},
            </if>
            <if test="effectiveDayUnit != null">
                #{effectiveDayUnit, jdbcType=INTEGER},
            </if>
            <if test="conditionOne != null">
                #{conditionOne,jdbcType=INTEGER},
            </if>

            <if test="status != null">
                #{status,jdbcType=BIT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BIT},
            </if>
            unix_timestamp(now())*1000,
            unix_timestamp(now())*1000,
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateExcelFirstEngage" parameterType="com.task.model.ReadFirst">
        update T_FIRST_ENGAGE set

            <if test="registrationNumberId != null">
                REGISTRATION_NUMBER_ID=#{registrationNumberId,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null">
                GOODS_TYPE= #{goodsType,jdbcType=INTEGER},
            </if>
            <if test="brandType != null">
                BRAND_TYPE= #{brandType,jdbcType=INTEGER},
            </if>
            <if test="standardCategoryType != null">
                STANDARD_CATEGORY_TYPE= #{standardCategoryType,jdbcType=BIT},
            </if>
            <if test="newStandardCategoryId != null">
                NEW_STANDARD_CATEGORY_ID=  #{newStandardCategoryId,jdbcType=INTEGER},
            </if>
            <if test="oldStandardCategoryId != null">
                OLD_STANDARD_CATEGORY_ID=  #{oldStandardCategoryId,jdbcType=INTEGER},
            </if>
            <if test="effectiveDays != null">
                EFFECTIVE_DAYS= #{effectiveDays,jdbcType=BIGINT},
            </if>
            <if test="sortDays != null">
                SORT_DAYS=  #{sortDays, jdbcType=INTEGER},
            </if>
            <if test="effectiveDayUnit != null">
                EFFECTIVE_DAY_UNIT= #{effectiveDayUnit, jdbcType=INTEGER},
            </if>
            <if test="conditionOne != null">
                CONDITION_ONE= #{conditionOne,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                STATUS= #{status,jdbcType=BIT},
            </if>
            <if test="isDeleted != null">
                IS_DELETED= #{isDeleted,jdbcType=BIT},
            </if>
            <if test="comments != null">
                COMMENTS= #{comments,jdbcType=VARCHAR},
            </if>
            ADD_TIME= unix_timestamp(now())*1000,
            MOD_TIME= unix_timestamp(now())*1000

        where FIRST_ENGAGE_ID=#{firstEngageId,jdbcType=INTEGER}
    </update>


  <!-- 搜索内容 -->
  <select id="getFirstSearchInfo" resultType="com.vedeng.firstengage.model.FirstEngageSearchInfo">
		SELECT
			a.CONTENT
		FROM
			T_FIRST_ENGAGE_SEARCH_INFO a
		WHERE
			a.USER_ID = #{userId, jdbcType=INTEGER}
		GROUP BY
			a.CONTENT
		ORDER BY
			a.FIRST_ENGAGE_SEARCH_INFO_ID DESC
  </select>

    <update id="deleteFirstEngage" parameterType="map">
		UPDATE 
			T_FIRST_ENGAGE a
		SET 
			a.IS_DELETED = 1,
			a.UPDATER = #{userId, jdbcType=INTEGER},
			a.MOD_TIME = unix_timestamp(now())*1000,
			a.COMMENTS = #{comment, jdbcType=VARCHAR}
		WHERE
			a.FIRST_ENGAGE_ID IN 
			<foreach collection="firstEngageArray" index="index" item="item" open="(" separator="," close=")">
	            #{item, jdbcType=INTEGER}
	        </foreach>
	        <if test="null != status">
                AND a.`STATUS` <![CDATA[ != ]]> #{status, jdbcType=INTEGER}
            </if>
	        <if test="null != isDelete">
                AND a.IS_DELETED = #{isDelete, jdbcType=INTEGER}
            </if>
  </update>
    <select id="getFirstEngageInfoBySkuNo" resultType="com.vedeng.firstengage.model.FirstEngage">
        SELECT
            E.*
        FROM  V_CORE_SKU U
        LEFT JOIN V_CORE_SPU P ON U.SPU_ID=P.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE E ON P.FIRST_ENGAGE_ID=E.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER N ON E.REGISTRATION_NUMBER_ID=N.REGISTRATION_NUMBER_ID
        where U.SKU_NO =#{skuNo}
    </select>


  <select id="getSkuNoListByFirstEngageId" resultType="java.lang.String" parameterType="java.lang.Integer">
        SELECT
            sku.SKU_NO skuNo
        FROM
            T_FIRST_ENGAGE rn
            INNER JOIN V_CORE_SPU spu ON rn.FIRST_ENGAGE_ID = spu.FIRST_ENGAGE_ID
            INNER JOIN V_CORE_SKU sku ON sku.SPU_ID = spu.SPU_ID
         where rn.FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
  </select>
    <select id="getRegistrationAttachment" resultType="com.vedeng.system.model.Attachment">
        select
        ATTACHMENT_ID, ATTACHMENT_TYPE, ATTACHMENT_FUNCTION, RELATED_ID, NAME, DOMAIN, URI,
        ALT, SORT, IS_DEFAULT, ADD_TIME, CREATOR, SUFFIX
        from T_ATTACHMENT A
        where ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
        and RELATED_ID = #{registrationNumberId,jdbcType=INTEGER}
        and IS_DELETED = 0
        and ATTACHMENT_FUNCTION = #{attachmentFunction,jdbcType=INTEGER}
    </select>
    <select id="getSpuByFirstEngageId" resultType="java.lang.Integer">
        SELECT
            spu.SPU_ID spuId
        FROM
            T_FIRST_ENGAGE rn
            INNER JOIN V_CORE_SPU spu ON rn.FIRST_ENGAGE_ID = spu.FIRST_ENGAGE_ID
         where rn.FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER}
    </select>

    <select id="getRegistrationNumber" resultType="com.vedeng.firstengage.model.FirstEngageInfo">
        SELECT
            a.FIRST_ENGAGE_ID firstEngageId,
            a.REGISTRATION_NUMBER_ID registrationNumberId,
            a.NEW_STANDARD_CATEGORY_ID newStandardCategoryId,
            a.OLD_STANDARD_CATEGORY_ID oldStandardCategoryId
        FROM
            T_FIRST_ENGAGE a
        LEFT JOIN
            T_REGISTRATION_NUMBER b
        ON
            a.REGISTRATION_NUMBER_ID = b.REGISTRATION_NUMBER_ID
        WHERE a.IS_DELETED = 0  AND  b.MANAGE_CATEGORY_LEVEL = #{level, jdbcType=INTEGER}
    </select>
</mapper>