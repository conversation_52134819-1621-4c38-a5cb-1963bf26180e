package com.vedeng.trader.dao;


import com.vedeng.trader.model.RTraderLabelJTrader;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RTraderLabelJTraderMapper {
    int deleteByPrimaryKey(Long rTraderLabelJTraderId);

    int insert(RTraderLabelJTrader record);

    int insertSelective(RTraderLabelJTrader record);

    RTraderLabelJTrader selectByPrimaryKey(Long rTraderLabelJTraderId);

    int updateByPrimaryKeySelective(RTraderLabelJTrader record);

    int updateByPrimaryKey(RTraderLabelJTrader record);

    /**
     * <b>Description:</b>根据标签id集合获取客户id集合<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    List<Integer> getTraderIdsByLabelIds(List<Integer> labelIds);

    /**
     * <b>Description:</b>根据标签id分页获取客户id<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/28
     */
    List<Integer> getTraderIdsListPage(Map<String,Object> map);

    /**
     * <b>Description:</b>根据标签id和客户id集合删除<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/28
     */
    int deleteByLabelIdAndTraderIds(@Param("labelId")Integer labelId,@Param(value = "traderIds") List<Integer> traderIds);

    /**
     * <b>Description:</b>根据标签id集合和客户id删除集合<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/8/6
     */
    int deleteByTraderIdAndLabelIds(@Param("traderId")Integer traderId,@Param(value = "labelIds")List<Integer> labelIds);

}