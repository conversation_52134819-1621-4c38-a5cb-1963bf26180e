package com.wms.service.listenner;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AfterReturnAuditFinishLister.java
 * @Description TODO  销售退货单审核生效处理器
 * @createTime 2020年08月11日 15:56:00
 */
@Service("putAfterReturnAuditFinishLister")
public class PutAfterReturnAuditFinishLister extends AbstractErpListenner{

    public static Logger logger = LoggerFactory.getLogger(PutAfterReturnAuditFinishLister.class);

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private AfterSalesDetailMapper afterSalesDetailMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Transactional
    @Override
    protected void doOnActionHappen(Object[] params) throws Exception {
        AfterSalesVo afterSalesInfo = (AfterSalesVo)params[0];

        boolean auditPass = (boolean)params[1];

        User user = (User)params[2];

        AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesInfo.getAfterSalesId());
        AfterSalesGoods afterGoods = new AfterSalesGoods();
        afterGoods.setAfterSalesId(afterSales.getAfterSalesId());
        List<AfterSalesGoodsVo> afterSalesGoodsVosList = afterSalesGoodsMapper.getAftersalesGoodsList(afterGoods);
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(afterSales.getOrderId());
        saleorder = saleorderMapper.getSaleOrderById(afterSales.getOrderId());
        List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
        //判断是否供应链维护虚拟商品，过滤
        saleorderGoodsList = saleorderGoodsList.stream().filter(item -> {
            CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(item.getGoodsId());
            if(ErpConst.ONE.equals(coreSku.getIsVirtureSku())){
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        if(!OrderConstant.ORDER_ALL_PAYMENT.equals(saleorder.getPaymentStatus())){
            return;
        }
        //审核通过
        if(auditPass){
            List<PutPurchaseOrderGoodsDto> putdetails = new ArrayList<>();
            if(CollectionUtils.isEmpty(afterSalesGoodsVosList)){
                return;
            }
            saleorderGoodsList.stream().forEach(saleorderGoods -> {
                dealWithAfterGoods(putdetails,saleorderGoods,afterSalesGoodsVosList);
            });

            //销售退货下发wms
            putAfterReturnWMS(afterSales,putdetails,user);
        }
    }


    private void dealWithAfterGoods(List<PutPurchaseOrderGoodsDto> putdetails,
                                        SaleorderGoods saleorderGoods,
                                        List<AfterSalesGoodsVo> afterSalesGoodsVosList) {
        //本次售后商品
        AfterSalesGoodsVo afterSalesGoods = afterSalesGoodsVosList.stream()
                .filter(afterSalesGood -> afterSalesGood.getOrderDetailId().equals(saleorderGoods.getSaleorderGoodsId()))
                .findFirst()
                .orElse(null);

        //本次没有售后
        if(afterSalesGoods == null){ return;}
        if(afterSalesGoods.getDeliveryDirect().equals(1) ||
                afterSalesGoods.getGoodsId().equals(GoodsConstants.FREIGHT)){return;}

        int historyReturnNum = getHistoryReturnNum(saleorderGoods.getSaleorderGoodsId());
        // 本次退货数量
        int nowReturnNum = afterSalesGoods.getNum();
        // 当前销售单剩余总数 = 销售单原始销售数量 - 历史退货单的退货数量
        int curTotal = saleorderGoods.getNum() - historyReturnNum;
        // 当前客户剩余数量 = 销售已出货数量 - 历史已经退回数量 - 当前售后单退回数量
        int curNoReturnNum = saleorderGoods.getDeliveryNum() - getHistoryArrivalNum(saleorderGoods.getSaleorderGoodsId()) - afterSalesGoods.getArrivalNum();
        // 本次退货数量 - （X- X中已出库的数量），只下传大于0的数量
        int resultNum = (curTotal - curNoReturnNum) - nowReturnNum;

        logger.info("销售退货单审核生效处理器 售后单商品id:{},resultNum:{},num:{},historyReturnNum:{},DeliveryNum:{},getArrivalNum:{}",
                afterSalesGoods.getAfterSalesGoodsId(),resultNum,saleorderGoods.getNum(), historyReturnNum,saleorderGoods.getDeliveryNum(),afterSalesGoods.getArrivalNum());

        if(resultNum < 0){
            //退货入库
            PutPurchaseOrderGoodsDto putPurchaseOrderGoodsDto = new PutPurchaseOrderGoodsDto();
            putPurchaseOrderGoodsDto.setSku(saleorderGoods.getSku());
            putPurchaseOrderGoodsDto.setOrderedQty(Math.abs(resultNum));
            putPurchaseOrderGoodsDto.setDedi04(afterSalesGoods.getAfterSalesGoodsId().toString());
            putdetails.add(putPurchaseOrderGoodsDto);
        }

    }

    private int getHistoryArrivalNum(Integer saleOrderGoodsId) {
        return afterSalesGoodsMapper.getSaleorderAfterSaleArrivalGoods(saleOrderGoodsId);
    }

    private int getHistoryReturnNum(Integer saleorderGoodsId) {
        return   afterSalesGoodsMapper.getSaleorderAftersaleReturnGoods(saleorderGoodsId);
    }

    private void putAfterReturnWMS(AfterSales afterSales,  List<PutPurchaseOrderGoodsDto> details, User user) {
            logger.info("WMS销售退货单下发 单号:{},商品详情数据长度:{}",afterSales.getAfterSalesNo(),details.size());
            if(CollectionUtils.isEmpty(details)){
                return;
            }
            AfterSalesDetail afterSalesDetailVo = afterSalesDetailMapper.selectadtbyid(afterSales);
            PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();
            putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_SALE_RETURN);
            putPurchaseOrderDto.setDocNo(afterSales.getAfterSalesNo());
            putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(afterSales.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
            putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
            putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

            putPurchaseOrderDto.setSupplierId(afterSalesDetailVo.getTraderId());
            putPurchaseOrderDto.setSupplierName(afterSalesDetailVo.getTraderName());
            putPurchaseOrderDto.setHedi01(afterSales.getOrderNo());

            if(afterSales.getServiceUserId() != null && afterSales.getServiceUserId() != 0){
                User userByUserId = userMapper.getUserInfoByUserId(afterSales.getServiceUserId());
                putPurchaseOrderDto.setPoReferenceA(StringUtil.isEmpty(userByUserId.getOrgName()) ? "/" : userByUserId.getOrgName() );
                putPurchaseOrderDto.setPoReferenceB(userByUserId.getUsername());
            }else{
                putPurchaseOrderDto.setPoReferenceA(StringUtil.isEmpty(user.getOrgName()) ? "/" : user.getOrgName() );
                putPurchaseOrderDto.setPoReferenceB(user.getUsername());
            }


            putPurchaseOrderDto.setDetails(details);
            logger.info("WMS销售退货单下发接口返回的请求: putPurchaseOrderDto:{}" + JSON.toJSONString(putPurchaseOrderDto));

            //下发存表，V_WMS_SEND_ORDER
            WmsSendOrder wmsSendOrder = logicalSaleorderChooseService.getWmsSendOrder(afterSales.getAfterSalesNo(),afterSales.getAfterSalesId(), ErpConst.THREE,user);
            try {
                WmsInterface putcharseOrderInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);
                WmsResponse response = putcharseOrderInterface.request(putPurchaseOrderDto);
                if( response.getReturnFlag().equals(0)){
                    //throw new Exception("销售退货单下发失败 单号"+afterSales.getAfterSalesNo()+",失败原因:"+response.getReturnDesc());
                    logger.info("销售退货单下发失败 单号"+afterSales.getAfterSalesNo()+",失败原因:"+response.getReturnDesc());
                }else{
                    //下发成功，更新V_WMS_SEND_ORDER状态
                    logicalSaleorderChooseService.saveWmsSendOrder(wmsSendOrder);
                }
                logger.info("WMS销售退货单下发接口返回的响应:" + JSON.toJSONString(response));
            }catch (Exception e){
                logger.error("销售退货下发wms失败,{}",e);
            }
    }
}
