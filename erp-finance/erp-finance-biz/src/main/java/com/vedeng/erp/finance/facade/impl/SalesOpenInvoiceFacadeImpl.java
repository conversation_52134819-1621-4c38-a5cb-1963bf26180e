package com.vedeng.erp.finance.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.common.exception.InvoiceException;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenResponseDto;
import com.vedeng.erp.finance.domain.dto.TaxesReturnInfo;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.InvoiceSystemInterfaceTypeEnum;
import com.vedeng.erp.finance.enums.NotifyTypeEnum;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.facade.SalesOpenInvoiceFacade;
import com.vedeng.erp.finance.notify.manager.NotifyServiceManager;
import com.vedeng.erp.finance.service.*;
import com.vedeng.erp.trader.dto.TraderFinanceDto;
import com.vedeng.erp.trader.service.TraderFinanceApiService;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.wxrobot.constants.WxRobotMsgTemple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 开票 门面层
 */
@Service
@Slf4j
public class SalesOpenInvoiceFacadeImpl implements SalesOpenInvoiceFacade {
    @Autowired
    private TaxClassificationService taxClassificationService;

    @Autowired
    private TraderFinanceApiService traderFinanceApiService;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Autowired
    private TaxesOpenApiService taxesOpenApiService;

    @Autowired
    private InvoiceTaxSystemRecordService invoiceTaxSystemRecordService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private FullyDigitalInvoiceService fullyDigitalInvoiceService;

    private static final String ERROR_FORMAT = "数电发票销项票开票:当前发票申请已开票，无需重新开票,发票申请id：{}";


    @Autowired
    private NotifyServiceManager notifyServiceManager;

    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;

    /**
     * 校验是否可开票
     *
     * @param invoiceApplyDto 发票申请
     */
    @Override
    public void checkOpenInvoice(InvoiceApplyDto invoiceApplyDto) {
        // 校验发票申请id
        Optional.ofNullable(invoiceApplyDto)
                .map(InvoiceApplyDto::getInvoiceApplyId)
                .orElseThrow(() -> new ServiceException("发票申请或者发票申请id不能为空!"));
        // 是否可开票
        List<InvoiceDto> invoiceList = invoiceService.findByInvoiceApply(invoiceApplyDto.getInvoiceApplyId());
        if (CollUtil.isNotEmpty(invoiceList)) {
            String errorMsg = StrUtil.format(ERROR_FORMAT, invoiceApplyDto.getInvoiceApplyId());
            throw new InvoiceException(errorMsg);
        }
        this.checkValidStatus(invoiceApplyDto);
        // 是否可开票
        boolean isCanOpen = this.isCanOpenBlueInvoice(invoiceApplyDto);
        if (!isCanOpen) {
            String errorMsg = StrUtil.format(ERROR_FORMAT, invoiceApplyDto.getInvoiceApplyId());
            throw new InvoiceException(errorMsg);
        }
        List<InvoiceApplyDetailDto> invoiceApplyDetailDtoList = invoiceApplyDto.getInvoiceApplyDetailDtoList();
        // 订单实际待开票金额为零
        if (CollUtil.isEmpty(invoiceApplyDetailDtoList)) {
            throw new InvoiceException("订单实际待开票金额为零!");
        }
        // 税收分类编码校验
        for (InvoiceApplyDetailDto invoiceApplyDetailDto : invoiceApplyDetailDtoList) {
            String taxCategoryNo = invoiceApplyDetailDto.getTaxCategoryNo();
            // 税收分类编码不能为空
            Optional.ofNullable(taxCategoryNo)
                    .orElseThrow(() ->
                            new InvoiceException(StrUtil.format("商品名称：{},编号:{},税收分类编码为空！", invoiceApplyDetailDto.getSkuName(), invoiceApplyDetailDto.getSkuNo())));
            // 获取税收分类编码信息
            TaxcodeClassificationDto taxClassificationDto = taxClassificationService.findByCode(taxCategoryNo);
            String classificationAbbreviation = Optional.ofNullable(taxClassificationDto)
                    .map(TaxcodeClassificationDto::getClassificationAbbreviation)
                    .orElseThrow(() ->
                            new InvoiceException(StrUtil.format("税收分类编码{},未找到!", taxCategoryNo)));
            // 税收分类简称
            invoiceApplyDetailDto.setTaxCategorySimpleName(classificationAbbreviation);
        }
        // 购方信息check
        List<TraderFinanceDto> traderFinanceDtoList = traderFinanceApiService.findByTraderIdAndTraderType(invoiceApplyDto.getTraderId(), ErpConstant.ONE);
        if (CollUtil.isEmpty(traderFinanceDtoList)) {
            log.error("无购方信息,财务申请单{},客户id{}", invoiceApplyDto.getInvoiceApplyId(), invoiceApplyDto.getTraderId());
            throw new InvoiceException(StrUtil.format("财务申请单{},客户id{},无购方信息，需处理", invoiceApplyDto.getInvoiceApplyId(),
                    invoiceApplyDto.getTraderId()));
        }
        if (traderFinanceDtoList.size() > 1) {
            log.error("财务申请单{},客户id{},购方信息大于1条,仅提醒", invoiceApplyDto.getInvoiceApplyId(), invoiceApplyDto.getTraderId());
        }

        invoiceApplyDto.setTraderFinanceDto(CollUtil.getFirst(traderFinanceDtoList));
    }

    /**
     * 校验审核状态
     * @param invoiceApplyDto data
     */
    private void checkValidStatus(InvoiceApplyDto invoiceApplyDto) {
        InvoiceApplyDto invoiceApply = invoiceApplyService.getInvoiceApply(invoiceApplyDto.getInvoiceApplyId());
        // 已通过
        if (ErpConstant.ONE.equals(invoiceApply.getValidStatus())) {
            throw new InvoiceException("审核状态为通过,请刷新页面");
        }
        // 已驳回
        if (ErpConstant.TWO.equals(invoiceApply.getValidStatus())) {
            throw new InvoiceException("审核状态为驳回,请刷新页面");
        }
    }

    /**
     * 调用税金系统接口
     *
     * @param invoiceApplyDto 发票申请
     * @return 税金系统接口返回值
     */
    @Override
    public SaleInvoiceOpenResponseDto invokeSaleOrderInvoiceApi(InvoiceApplyDto invoiceApplyDto) {
        log.info("数电发票开蓝票:调用税金系统接口,参数:{}", JSON.toJSONString(invoiceApplyDto));
        // 组装税金系统发票接口参数
        SaleInvoiceOpenRequestDto saleInvoiceOpenRequestDto = this.assembleInvoice(invoiceApplyDto);
        // 调用税金系统接口
        ITaxesResult openapi = taxesOpenApiService.openapi(saleInvoiceOpenRequestDto, TaxesInterfaceCodeEnum.OPEN);
        log.info("数电发票开蓝票:调用税金系统接口,返回值:{}", JSON.toJSONString(openapi));
        SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto = (SaleInvoiceOpenResponseDto) openapi;
        if (!saleInvoiceOpenResponseDto.getIsSuccess()) {
            // 调用失败
            log.info("数电发票开蓝票:返回失败");
            return saleInvoiceOpenResponseDto;
        }
        // 调用成功，但发票号无的情况，返回失败
        if (StrUtil.isBlank(saleInvoiceOpenResponseDto.getFphm())) {
            log.info("数电发票开蓝票:返回成功，但无发票号");
            throw new InvoiceException(saleInvoiceOpenResponseDto);
        }
        // 调用成功，更新税金系统接口调用记录表
        this.openBlueInvoiceInvokeSuccess(saleInvoiceOpenRequestDto, saleInvoiceOpenResponseDto);
        return saleInvoiceOpenResponseDto;
    }

    @Override
    public void handOverInvoice(InvoiceDto invoiceDto) {
        // 短信交付
        notifyServiceManager.notify(NotifyTypeEnum.SMS,invoiceDto);
        // 邮件交付
        notifyServiceManager.notify(NotifyTypeEnum.MAIL,invoiceDto);
        // 微信交付
        notifyServiceManager.notify(NotifyTypeEnum.WXAPP,invoiceDto);
    }

    @Override
    public void openBlueInvoiceFail(InvoiceApplyDto invoiceApplyDto, InvoiceException invoiceException) {
        TaxesReturnInfo taxesReturnInfo = invoiceException.getTaxesReturnInfo();
        SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto = new SaleInvoiceOpenResponseDto();
        saleInvoiceOpenResponseDto.setReturnMessage(taxesReturnInfo.getReturnMessage());
        saleInvoiceOpenResponseDto.setReturnCode(taxesReturnInfo.getReturnCode());
        // 组装税金系统接口税金记录表 - 销项票开具
        InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto = invoiceTaxSystemRecordService.fromTaxesApiInfoInitInvoiceTaxSystemRecord(
                null,
                saleInvoiceOpenResponseDto);

        invoiceTaxSystemRecordDto.setBusinessId(invoiceApplyDto.getInvoiceApplyId().toString());

        // 判断当前是否存在
        InvoiceTaxSystemRecordDto isExist = this.getInvoiceTaxSystemRecordDto(invoiceApplyDto, InvoiceSystemInterfaceTypeEnum.SALE_OPEN, null);
        if (Objects.nonNull(isExist)) {
            invoiceTaxSystemRecordDto.setInvoiceTaxSystemRecordId(isExist.getInvoiceTaxSystemRecordId());
        }
        invoiceTaxSystemRecordService.fail(invoiceTaxSystemRecordDto);
        // 接口返回失败 企业微信群告警
        fullyDigitalInvoiceService.retryOut(invoiceTaxSystemRecordDto,
                StrUtil.format(WxRobotMsgTemple.OPEN_INVOICE_FAIL_ERROR,
                        invoiceApplyDto.getInvoiceApplyId().toString(),
                        saleInvoiceOpenResponseDto.getReturnMessage()));
    }

    /**
     * 调用成功，更新税金系统接口调用记录表
     *
     * @param saleInvoiceOpenRequestDto  税金系统接口请求参数
     * @param saleInvoiceOpenResponseDto 税金系统接口返回参数
     */
    private void openBlueInvoiceInvokeSuccess(SaleInvoiceOpenRequestDto saleInvoiceOpenRequestDto,
                                              SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto) {
        // 获取税金系统接口调用记录表 是否有数据 当有数据 无论是否失败，更新为成功状态
        InvoiceTaxSystemRecordDto whetherExist = new InvoiceTaxSystemRecordDto();
        whetherExist.setBusinessId(saleInvoiceOpenRequestDto.getData().getOrderNo());
        whetherExist.setInterfaceType(InvoiceSystemInterfaceTypeEnum.SALE_OPEN.getType());
        InvoiceTaxSystemRecordDto exist = invoiceTaxSystemRecordService.isExist(whetherExist);
        if (exist != null) {
            exist.setBodyOrg(JSON.toJSONString(saleInvoiceOpenRequestDto));
            exist.setResult(JSON.toJSONString(saleInvoiceOpenResponseDto));
            exist.setInvoiceNo(saleInvoiceOpenResponseDto.getFphm());
            log.info("更新税金系统接口调用记录表:{}",JSON.toJSONString(exist));
            invoiceTaxSystemRecordService.success(exist);
            // 调用销项票下载
            this.invokeBlueInvoiceInvoiceDown(saleInvoiceOpenResponseDto, saleInvoiceOpenRequestDto.getData().getOrderNo());
            return;
        }
        // 生成税金系统接口税金记录表 - 销项票开具
        InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto = invoiceTaxSystemRecordService.fromTaxesApiInfoInitInvoiceTaxSystemRecord(saleInvoiceOpenRequestDto,
                saleInvoiceOpenResponseDto);
        invoiceTaxSystemRecordDto.setBusinessId(saleInvoiceOpenRequestDto.getData().getOrderNo());
        invoiceTaxSystemRecordDto.setInvoiceNo(saleInvoiceOpenResponseDto.getFphm());
        log.info("更新税金系统接口调用记录表:{}",JSON.toJSONString(invoiceTaxSystemRecordDto));
        invoiceTaxSystemRecordService.success(invoiceTaxSystemRecordDto);

        // 调用销项票下载
        this.invokeBlueInvoiceInvoiceDown(saleInvoiceOpenResponseDto, saleInvoiceOpenRequestDto.getData().getOrderNo());
    }

    /**
     * 调用销项票下载
     *
     * @param saleInvoiceOpenResponseDto
     * @param businessId
     */
    private void invokeBlueInvoiceInvoiceDown(SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto, String businessId) {
        DownloadInvoice downloadInvoice = new DownloadInvoice();
        downloadInvoice.setInvoiceNo(saleInvoiceOpenResponseDto.getFphm());
        downloadInvoice.setInvoiceDate(saleInvoiceOpenResponseDto.getKprq());
        downloadInvoice.setInvoiceApplyId(businessId);
        fullyDigitalInvoiceService.downloadInvoice(downloadInvoice);
    }


    /**
     * 是否可开票
     *
     * @param invoiceApplyDto
     * @return
     */
    private boolean isCanOpenBlueInvoice(InvoiceApplyDto invoiceApplyDto) {
        // 需要失败重试的税金任务记录表,无成功的表示需要重试
        InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto = this.getInvoiceTaxSystemRecordDto(invoiceApplyDto, InvoiceSystemInterfaceTypeEnum.SALE_OPEN,
                FinanceConstant.SUCCESS);
        return ErpConstant.WAIT_AUDIT.equals(invoiceApplyDto.getValidStatus()) && invoiceTaxSystemRecordDto == null;
    }

    /**
     * 获取发票税金任务记录
     *
     * @param invoiceApplyDto
     * @param invoiceSystemInterfaceTypeEnum
     * @param isSuccess
     * @return
     */
    private InvoiceTaxSystemRecordDto getInvoiceTaxSystemRecordDto(InvoiceApplyDto invoiceApplyDto,
                                                                   InvoiceSystemInterfaceTypeEnum invoiceSystemInterfaceTypeEnum,
                                                                   String isSuccess) {
        InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto = new InvoiceTaxSystemRecordDto();
        invoiceTaxSystemRecordDto.setBusinessId(invoiceApplyDto.getInvoiceApplyId().toString());
        invoiceTaxSystemRecordDto.setInterfaceType(invoiceSystemInterfaceTypeEnum.getType());
        invoiceTaxSystemRecordDto.setRunningStatus(isSuccess);
        List<InvoiceTaxSystemRecordDto> invoiceTaxSystemRecordDtoList = invoiceTaxSystemRecordService
                .findInvoiceTaxSystemRecordDto(invoiceTaxSystemRecordDto);
        return CollUtil.getFirst(invoiceTaxSystemRecordDtoList);
    }

    private SaleInvoiceOpenRequestDto assembleInvoice(InvoiceApplyDto invoiceApplyDto) {
        log.info("组装税金系统发票接口参数:发票申请:{}", JSON.toJSONString(invoiceApplyDto));

        List<InvoiceApplyDetailDto> detailDtoList = invoiceApplyDto.getInvoiceApplyDetailDtoList();

        SaleInvoiceOpenRequestDto saleInvoiceOpenRequestDto = new SaleInvoiceOpenRequestDto();
        SaleInvoiceOpenRequestDto.DataInfo dataInfo = new SaleInvoiceOpenRequestDto.DataInfo();
        saleInvoiceOpenRequestDto.setData(dataInfo);
        dataInfo.setOrderNo(invoiceApplyDto.getInvoiceApplyId().toString());
        dataInfo.setFppzDm(invoiceApplyDto.getInvoiceTypeCode());

        TraderFinanceDto traderFinanceDto = invoiceApplyDto.getTraderFinanceDto();

        dataInfo.setGmfnsrsbh(traderFinanceDto.getTaxNum());
        dataInfo.setGmfmc(traderFinanceDto.getTraderName());
        dataInfo.setGmfdz(traderFinanceDto.getRegAddress());
        dataInfo.setGmflxdh(traderFinanceDto.getRegTel());
        dataInfo.setGmfkhh(traderFinanceDto.getBank());
        dataInfo.setGmfyhzh(traderFinanceDto.getBankAccount());
        dataInfo.setSpsl(detailDtoList.size());
        dataInfo.setHsbz("1");
        dataInfo.setSfzsgmfyhzh("N");
        dataInfo.setSfzsxsfyhzh("N");
        // 开票备注
        dataInfo.setBz(invoiceApplyDto.getComments());

        List<SaleInvoiceOpenRequestDto.Mxzb> mxzbList = new ArrayList<>();
        dataInfo.setMxzbList(mxzbList);
        detailDtoList.forEach(d -> {
            // 明细
            SaleInvoiceOpenRequestDto.Mxzb mxzb = new SaleInvoiceOpenRequestDto.Mxzb();
            mxzb.setXh(d.getInvoiceApplyDetailId());
            mxzb.setFphxzDm("0");
            // 产品名称
            mxzb.setXmmc(d.getProductName());
            mxzb.setSpfwjc(d.getTaxCategorySimpleName());
            mxzb.setSphfwssflhbbm(d.getTaxCategoryNo());
            mxzb.setHwhyslwfwmc(StrUtil.format("*{}*{}", mxzb.getSpfwjc(), mxzb.getXmmc()));
            // 规格型号
            mxzb.setGgxh(d.getSpecModel());
            // 单位
            mxzb.setDw(d.getUnit());
            mxzb.setSpsl(this.formatNumberToString(d.getNum()));
            mxzb.setHsdj(this.formatNumberToDecimal(d.getPrice()));
            mxzb.setHsje(d.getTotalAmount());
            // 税率
            mxzb.setSlv(d.getTaxRate().toPlainString());

            // 税额
            mxzb.setSe(d.getTaxAmount());
            // 不含税金额
            mxzb.setBhsje(d.getTaxExclusiveAmount());
            mxzb.setJe(mxzb.getBhsje());

            // 不含税单价
            mxzb.setBhsdj(this.formatNumberToDecimal(d.getTaxExclusivePrice()));
            mxzb.setDj(this.formatNumberToDecimal(mxzb.getBhsdj()));
            mxzbList.add(mxzb);
        });

        dataInfo.setHjje(mxzbList.stream().map(SaleInvoiceOpenRequestDto.Mxzb::getJe).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        dataInfo.setHjse(mxzbList.stream().map(SaleInvoiceOpenRequestDto.Mxzb::getSe).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        dataInfo.setJshj(mxzbList.stream().map(SaleInvoiceOpenRequestDto.Mxzb::getHsje).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).toPlainString());
        dataInfo.setJehj(0);

        return saleInvoiceOpenRequestDto;
    }

    private BigDecimal formatNumberToDecimal(BigDecimal formatNum) {
        if (Objects.isNull(formatNum)){
            return null;
        }
        String formatStr = formatNum.stripTrailingZeros().toPlainString();
        BigDecimal format = new BigDecimal(formatStr);
        return format;
    }

    private String formatNumberToString(BigDecimal formatNum) {
        if (Objects.isNull(formatNum)){
            return null;
        }
        String formatStr = formatNum.stripTrailingZeros().toPlainString();
        return formatStr;
    }

    /**
     * 计算不含税单价
     *
     * @param applyDetailDto
     * @param mxzb
     * @return
     */
    private BigDecimal getTaxExclusivePrice(InvoiceApplyDetailDto applyDetailDto,SaleInvoiceOpenRequestDto.Mxzb mxzb){
        if(BigDecimal.ZERO.compareTo(applyDetailDto.getTaxExclusivePrice()) != 0){
            return applyDetailDto.getTaxExclusivePrice();
        }
        BigDecimal taxExclusivePrice = mxzb.getBhsje().divide(applyDetailDto.getNum(), 8, RoundingMode.HALF_UP);
        return taxExclusivePrice;
    }

    /**
     * 计算税额
     *
     * @param mxzb
     * @return
     */
    private BigDecimal getTaxAmount(InvoiceApplyDetailDto applyDetailDto,SaleInvoiceOpenRequestDto.Mxzb mxzb) {
        if(BigDecimal.ZERO.compareTo(applyDetailDto.getTaxAmount()) != 0){
            return applyDetailDto.getTaxAmount();
        }
        BigDecimal taxAmount = mxzb.getHsje().multiply(new BigDecimal(mxzb.getSlv())).divide(BigDecimal.ONE.add(new BigDecimal(mxzb.getSlv())), 2, RoundingMode.HALF_UP);
        return taxAmount;
    }

    /**
     * 获取规格型号
     *
     * @param applyDetailDto
     * @return
     */
    private String getGGXH(InvoiceApplyDetailDto applyDetailDto) {
        if(StrUtil.isNotBlank(applyDetailDto.getSpecModel())){
            return applyDetailDto.getSpecModel();
        }
        String model = applyDetailDto.getModel();
        String spec = applyDetailDto.getSpec();
        String ggxhTpl = "{}{}";
        if (StrUtil.isNotBlank(model) && StrUtil.isNotBlank(spec)) {
            ggxhTpl = "{}/{}";
        }
        String ggxh = StrUtil.format(ggxhTpl, StrUtil.nullToEmpty(model), StrUtil.nullToEmpty(spec));
        return ggxh;
    }

}
