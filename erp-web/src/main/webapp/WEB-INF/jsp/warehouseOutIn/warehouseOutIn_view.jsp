<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>入库单详情</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/out_in.css?rnd=${resourceVersionKey}">

    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css" />
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css" />
    <!-- 模糊搜索下拉框css引入 -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/libs/searchableSelect/jquery.searchableSelect.css" />
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/labelMark.css">
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js"></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/call.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/movinghead.js?rnd=${resourceVersionKey}'></script>
    <style>
        .dlg-confirm i{
            background: none;
        }
        .del-wrap i {
            background: none;
        }

        .tip-wrap i{
            background: none;
        }

        .history {
            font: 12px / 1.5 "Microsoft YaHei", Arial, sans-serif;
        }
        .status-item{
            word-wrap:break-word;
            word-break:break-word;
            padding: 10px 2px 10px 0;
            text-align: center;
            margin: 1px;
        }

    </style>
    <%
        String path = request.getContextPath();
        String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                + path + "/";
    %>
</head>

<body>
    <div class="detail-wrap">
        <div class="detail-block">
            <div class="block-title title-container title-container-blue">基本信息</div>
            <div class="detail-table">
                <div class="table-item">
                    <div class="table-th">入库单号：</div>
                    <div class="table-td">
                        ${outInBaseInfo.outInNo}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">WMS入库单号：</div>
                    <div class="table-td">
                        ${outInBaseInfo.wmsNo}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">关联单号：</div>
                    <div class="table-td">
                        <c:if test="${outInBaseInfo.updateRemark eq '21-22其他'}">
                            ${outInBaseInfo.relateNo}
                        </c:if>
                        <c:if test="${outInBaseInfo.updateRemark ne '21-22其他'}">
                            <%--     采购换货入库--%>
                            <c:if test="${outInBaseInfo.outInType eq 8}">
                                <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                        "link":"./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=${outInBaseInfo.buyorderId}"}'> ${outInBaseInfo.relateNo}</a>
                            </c:if>
                            <%--    采购入库--%>
                            <c:if test="${outInBaseInfo.outInType eq 1 || outInBaseInfo.outInType eq 17}">
                                <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                            "link":"./order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${outInBaseInfo.buyorderId}"}'> ${outInBaseInfo.relateNo}</a>
                            </c:if>
                            <%--    外借入库--%>
                            <c:if test="${outInBaseInfo.outInType eq 9}">
                                <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                            "link":"./wms/commodityLendOut/detail.do?lendOutId=${outInBaseInfo.buyorderId}"}'>${outInBaseInfo.relateNo}</a>
                            </c:if>
                            <%--    盘盈入库--%>
                            <c:if test="${outInBaseInfo.outInType eq 12}">
                                <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                            "link":"./wms/surplusIn/applyIndex.do?wmsInputOrderId=${outInBaseInfo.buyorderId}"}'>${outInBaseInfo.relateNo}</a>
                            </c:if>
                            <%--  销售退货入库--%>
                            <c:if test="${outInBaseInfo.outInType eq 5}">
                                <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
													"link":"./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=${outInBaseInfo.buyorderId}"}'> ${outInBaseInfo.relateNo}</a>
                            </c:if>
                            <%--  销售换货入库--%>
                            <c:if test="${outInBaseInfo.outInType eq 3}">
                                <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
													"link":"./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=${outInBaseInfo.buyorderId}"}'> ${outInBaseInfo.relateNo}</a>
                            </c:if>

                            <c:if test="${outInBaseInfo.outInType eq 20}">
                                <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
													"link":"/wmsUnitConversion/detail.do?wmsUnitConversionOrderId=${outInBaseInfo.buyorderId}"}'> ${outInBaseInfo.relateNo}</a>
                            </c:if>
                        </c:if>
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">入库单类型：</div>
                    <div class="table-td">
                            <c:choose>
                                <c:when test="${outInBaseInfo.outInType == 1}">
                                    采购入库单
                                </c:when>
                                <c:when test="${outInBaseInfo.outInType == 8}">
                                    采购售后换货入库单
                                </c:when>
                                <c:when test="${outInBaseInfo.outInType == 9}">
                                    外借归还入库
                                </c:when>
                                <c:when test="${outInBaseInfo.outInType == 12}">
                                    盘盈入库单
                                </c:when>
                                <c:when test="${outInBaseInfo.outInType == 17}">
                                    采购赠品入库单
                                </c:when>
                                <c:when test="${outInBaseInfo.outInType == 3}">
                                    销售换货入库单
                                </c:when>
                                <c:when test="${outInBaseInfo.outInType == 5}">
                                    销售退货入库单
                                </c:when>
                                <c:when test="${outInBaseInfo.outInType == 20}">
                                    库存转换入库
                                </c:when>
                            </c:choose>
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">单据归属：</div>
                    <div class="table-td">
                        ${outInBaseInfo.owner}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">到货状态：</div>
                    <div class="table-td">
                        <c:choose>
                            <c:when test="${outInBaseInfo.arrivalStatus == 0}">
                                未收货
                            </c:when>
                            <c:when test="${outInBaseInfo.arrivalStatus == 1}">
                                部分收货
                            </c:when>
                            <c:when test="${outInBaseInfo.arrivalStatus == 2}">
                                全部收货
                            </c:when>
                        </c:choose>
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">指定物流公司：</div>
                    <div class="table-td">
                        <c:choose>
                            <c:when test="${outInBaseInfo.isVirture eq 1}">

                            </c:when>
                            <c:otherwise>
                                ${outInBaseInfo.outInCompany}
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">运费说明：</div>
                    <div class="table-td">
                        <c:choose>
                            <c:when test="${outInBaseInfo.isVirture eq 1}">

                            </c:when>
                            <c:otherwise>
                                ${outInBaseInfo.freightDescription}
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="table-item item-col">
                    <div class="table-th">物流备注：</div>
                    <div class="table-td">
                        <c:choose>
                            <c:when test="${outInBaseInfo.isVirture eq 1}">

                            </c:when>
                            <c:otherwise>
                                ${outInBaseInfo.logisticsComments}
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <div class="table-item item-col">
                    <div class="table-th">补充条款：</div>
                    <div class="table-td">
                        <c:choose>
                            <c:when test="${outInBaseInfo.isVirture eq 1}">

                            </c:when>
                            <c:otherwise>
                                ${outInBaseInfo.additionalClause}
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>

            </div>
        </div>
        <div class="detail-block">
            <div class="block-title title-container title-container-blue">发货方信息</div>
            <div class="detail-table">
                <div class="table-item item-col">
                    <div class="table-th">发货方名称：</div>
                    <div class="table-td">
                        ${outInBaseInfo.traderName}
                    </div>
                </div>
                <div class="table-item item-col">
                    <div class="table-th">发货方地址：</div>
                    <div class="table-td">
                        ${outInBaseInfo.traderAddressDetail}
                    </div>
                </div>

            </div>
        </div>
        <div class="detail-block">
            <div class="block-title title-container title-container-blue">入库记录</div>
            <div class="status-title">
                <div class="status-item">序号</div>
                <div class="status-item">订货号</div>
                <div class="status-item">产品名称</div>
                <div class="status-item">品牌</div>
                <div class="status-item">型号/规格</div>
                <div class="status-item">实际收货数量</div>
                <div class="status-item">单位</div>
                <div class="status-item">贝登批次码</div>
                <div class="status-item">生产日期</div>
                <div class="status-item">有效期至</div>
                <div class="status-item">入库时间</div>
                <div class="status-item">批次号</div>
                <div class="status-item">SN码</div>
                <div class="status-item">授权书类型</div>
                <div class="status-item">灭菌批号</div>
                <div class="status-item">注册证号</div>
            </div>
            <div class="status-list">
                <c:choose>
                    <c:when test="${not empty outInDetailList}">
                        <c:forEach items="${outInDetailList}" var="var" varStatus="index">
                            <c:if test="${index.count==5}">
                                <div class="status-more J-optional-more">
                            </c:if>
                            <div class="status-cnt">
                                <div class="status-item">${index.index+1}</div>
                                <div class="status-item">${var.skuNo}</div>

                                <c:choose>
                                    <c:when test="${outInBaseInfo.outInType eq 5  && var.isGift eq 1}">
                                        <div class="status-item">
                                            <a href="${pageContext.request.contextPath}/goods/goods/viewbaseinfo.do?goodsId=${var.skuId}">
                                                <img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" /> ${var.skuName}
                                            </a>
                                        </div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="status-item">
                                            <a href="${pageContext.request.contextPath}/goods/goods/viewbaseinfo.do?goodsId=${var.skuId}">
                                                    ${var.skuName}
                                            </a>
                                        </div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="status-item">${var.brandName}</div>

                                <div class="status-item">
                                    <c:choose>
                                        <c:when test="${var.spuType=='317' || var.spuType=='318'}">${var.spec}</c:when>
                                        <c:when test="${var.spuType=='316' || var.spuType=='1008'}">${var.model}</c:when>
                                        <c:otherwise>${var.model}/${var.spec}</c:otherwise>
                                    </c:choose>
                                </div>
                                <div class="status-item">${var.num}</div>
                                <div class="status-item">${var.unitName}</div>
                                <c:choose>
                                    <%--    销售退货  销售换货  直发--%>
                                    <c:when test="${( outInBaseInfo.outInType eq 5 || outInBaseInfo.outInType eq 3 )  && outInBaseInfo.source eq 'ERP'}">
                                        <div class="status-item"></div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="status-item">${var.vedengBatchNumer}</div>
                                    </c:otherwise>
                                </c:choose>

                                <c:choose>
                                    <%--     采购换货入库   采购入库   直发--%>
                                    <c:when test="${((outInBaseInfo.outInType eq 8 || outInBaseInfo.outInType eq 1) && outInBaseInfo.source eq 'ERP') || outInBaseInfo.isVirture eq 1}">
                                        <div class="status-item">见内部包装说明</div>
                                        <div class="status-item">见内部包装说明</div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="status-item">${var.productDateStr}</div>
                                        <div class="status-item">${var.expirationDateStr}</div>
                                    </c:otherwise>
                                </c:choose>

                                <div class="status-item">${var.checkStatusTimeStr}</div>
                                <div class="status-item">${var.batchNumber}</div>
                                <div class="status-item">${var.barcodeFactory}</div>
                                <div class="status-item">${var.authType}</div>
                                <c:choose>
                                    <%--    销售退货  销售换货  直发--%>
                                    <c:when test="${( outInBaseInfo.outInType eq 5 || outInBaseInfo.outInType eq 3 )  && outInBaseInfo.source eq 'ERP'}">
                                        <div class="status-item"></div>
                                    </c:when>
                                    <c:otherwise>
                                        <div class="status-item">${var.sterilizationBatchNo}</div>
                                    </c:otherwise>
                                </c:choose>
                                <div class="status-item">${var.registrationNumber}</div>
                            </div>
                            <c:if test="${outInDetailList.size()== index.count && index.count>=5 }">
                                </div>
                            </c:if>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="status-cnt">
                            <div class="status-item" style="text-align: center;">暂无入库记录！</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
            <c:if test="${fn:length(outInDetailList) gt 4}">
            <div class="detail-optional J-toggle-show">
                <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
                <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
            </div>
            </c:if>
        </div>
        <div class="detail-block">
            <div class="title-container title-container title-container-blue">
                <div class="table-title nobor">入库附件</div>
                <div class="title-click nobor pop-new-data" layerParams='{"width":"520px","height":"200px","title":"入库附件","link":"./toAddOutInAttach.do?warehouseGoodsOutInId=${outInBaseInfo.warehouseGoodsOutInId}"}'>新增附件</div>
            </div>
            <div class="status-title">
                <div class="status-item">序号</div>
                <div class="status-item">附件名称</div>
                <div class="status-item">操作人</div>
                <div class="status-item">上传时间</div>
                <div class="status-item">操作</div>
            </div>
            <div class="status-list">
                <c:choose>
                    <c:when test="${not empty attachmentList}">
                        <c:forEach items="${attachmentList}" var="var" varStatus="index">
                            <c:if test="${index.count==5}">
                                <div class="status-more J-optional-more">
                            </c:if>
                            <div class="status-cnt">
                                <div class="status-item">${index.index+1}</div>
                                <div class="status-item"><a  href="http://${var.domain}${var.uri}" target="_blank">${var.name}</a></div>
                                <div class="status-item">${var.creatorName}</div>
                                <div class="status-item">${var.addTimeStr}</div>
                                <div class="status-item">
                                    <span class="bt-smaller bt-border-style border-red" onclick="del('${var.attachmentId}')">删除</span>
                                </div>
                            </div>
                            <c:if test="${attachmentList.size()== index.count && index.count>=5 }">
                                </div>
                            </c:if>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="status-cnt">
                            <div class="status-item" style="text-align: center;">暂无入库附件！</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
            <c:if test="${fn:length(attachmentList) gt 4}">
                <div class="detail-optional J-toggle-show">
                    <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
                    <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
                </div>
            </c:if>
        </div>
        <div class="detail-block">
            <div class="block-title title-container title-container-blue">入库验收报告</div>
            <div class="status-title">
                <div class="status-item">序号</div>
                <div class="status-item">附件名称</div>
                <div class="status-item">附件来源</div>
                <div class="status-item">上传时间</div>
            </div>
            <div class="status-list">
                <c:choose>
                    <c:when test="${not empty attachList}">
                        <c:forEach items="${attachList}" var="var" varStatus="index">
                            <c:if test="${index.count==5}">
                                <div class="status-more J-optional-more">
                            </c:if>
                            <div class="status-cnt">
                                <div class="status-item">${index.index+1}</div>
                                <div class="status-item"><a  href="http://${var.domain}${fn:replace(var.uri,'display' , 'download')}">${var.name}</a></div>
                                <div class="status-item">ERP</div>
                                <div class="status-item">${var.addTimeStr}</div>
                            </div>
                            <c:if test="${attachList.size()== index.count && index.count>=5 }">
                                </div>
                            </c:if>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="status-cnt">
                            <div class="status-item" style="text-align: center;">暂无入库验收报告！</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
            <c:if test="${fn:length(attachList) gt 4}">
                <div class="detail-optional J-toggle-show">
                    <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
                    <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
                </div>
            </c:if>
        </div>
    </div>
</body>
    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.PrintArea.js'></script>

    <script>
        //展开收起
        $('.J-toggle-show').click(function () {
            var $optionalWrap = $(this).siblings().find('.J-optional-more');
            var isShow = $optionalWrap.hasClass('show');
            var $less = $(this).find('.J-less');
            var $more = $(this).find('.J-more');

            if (isShow) {
                $optionalWrap.removeClass('show').slideUp(200);
                $less.hide();
                $more.show();
            } else {
                $optionalWrap.addClass('show').slideDown(200);
                $less.show();
                $more.hide();
            }
        })

        //附件删除
        function del(attachmentId) {
            layer.confirm("您是否确认删除？", {
                btn: ['确定', '取消'] //按钮
            }, function () {
                $.ajax({
                    type: "POST",
                    url: page_url + "/warehouseOutIn/delAttachmentId.do",
                    data: {
                        'attachmentId': attachmentId
                    },
                    dataType: 'json',
                    success: function (data) {
                        parent.layer.close(index);
                        window.location.reload();
                    },
                    error: function (data) {
                      layer.alert("删除失败")
                    }
                });
            }, function () {
            });
        }
    </script>
