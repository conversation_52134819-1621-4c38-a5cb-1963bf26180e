package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.alibaba.fastjson.JSON;
import com.google.common.base.CharMatcher;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SpuOccupyInvalidatorError;

/**
 * .(2)	所属的所有SKU在所有逻辑库的占用为0；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 17:22.
 * @author: Randy<PERSON>.
 */
@Service
@Slf4j
public class SpuOccupyInvalidator extends GoodsInvalidatorChain {


    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        List<String> skuNoList = getSkuNoList();
        if(CollectionUtils.isEmpty(skuNoList)) {
            return ;
        }
        Map<String, List<WarehouseStock>> logicalStockInfo = warehouseStockService.getLogicalStockInfo(skuNoList);
        log.info("获得查询库存服务结果,查询参数:"+ JSON.toJSONString(skuNoList)+"，查询结果："+JSON.toJSONString(logicalStockInfo));
        StringBuffer skus = new StringBuffer();
        for (String skuNo : skuNoList) {
            List<WarehouseStock> warehouseStocks = logicalStockInfo.get(skuNo);
            if (CollectionUtils.isNotEmpty(warehouseStocks)) {
                Optional<WarehouseStock> first = warehouseStocks.stream().filter(e -> e.getOccupyNum() != 0).findFirst();
                if (first.isPresent()) {
                    skus.append(skuNo);
                    skus.append("，");
                }
            }
        }
        if (skus.length() > 0) {
            String skusList = SpuOccupyInvalidatorError.getMessage().replace("skus", skus.substring(0,skus.length()-1));
            throw new InvalidatorChainException(SpuOccupyInvalidatorError.getCode(), skusList);
        }

    }
}
