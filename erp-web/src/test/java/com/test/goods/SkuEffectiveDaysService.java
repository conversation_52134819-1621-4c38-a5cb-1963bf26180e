package com.test.goods;

import com.beust.jcommander.internal.Maps;
import com.google.common.collect.Range;
import com.test.stock.SpeicalWarehouseProcessor;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.command.EffectDayResult;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.model.CoreSkuGenerate;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName SkuEffectiveDaysService.java
 * @Description TODO
 * @createTime 2020年10月20日 13:53:00
 */
public class SkuEffectiveDaysService extends SpeicalWarehouseProcessor {

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    private   EffectDayResult generateNearTermWarnDays2(int effectiveDays){
        Map<Range, EffectDayResult> map = Maps.newHashMap();
        map.put(Range.atLeast(1095),new EffectDayResult(365,120));
        map.put(Range.closedOpen(365,1095),new EffectDayResult(180,90));
        map.put(Range.closedOpen(180,365),new EffectDayResult(120,60));
        map.put(Range.closedOpen(90,180),new EffectDayResult(60,30));
        map.put(Range.closedOpen(30,90),new EffectDayResult(20,10));
        map.put(Range.closedOpen(20,30),new EffectDayResult(10,5));
        map.put(Range.closedOpen(0,20),new EffectDayResult(7,5));
        return map.entrySet().stream().filter(e -> e.getKey().contains(effectiveDays))
                .map(e -> e.getValue())
                .findFirst()
                .orElse(null);
    }
    @Test
    public void test() throws Exception{
        FileInputStream fileInputStream = new FileInputStream("/Users/<USER>/Downloads/科研购.txt");
        InputStreamReader isr = new InputStreamReader(fileInputStream);
        BufferedReader br = new BufferedReader(isr);
        FileOutputStream fileOutputStream = new FileOutputStream("/Users/<USER>/Downloads/科研购.sql");
        OutputStreamWriter osw = new OutputStreamWriter(fileOutputStream);
        BufferedWriter bw = new BufferedWriter(osw);
        String str = null;
        Map<String,String> map = new HashMap<>();
        List<String> skuList = new ArrayList<>();
        int  count = 0;
        while((str = br.readLine() )!= null) {
            String[] s = str.split("\t");
            map.put(s[0].trim(), s[1].trim());
            skuList.add(s[0].trim());
        }
        List<CoreSkuGenerate> skuGenerateList = coreSkuGenerateMapper.getSkuListByNo(skuList);
        for (CoreSkuGenerate coreSkuGenerate : skuGenerateList) {
            if(coreSkuGenerate != null && StringUtil.isBlank(coreSkuGenerate.getEffectiveDays())){
                StringBuilder res = new StringBuilder();
                String effectiveDays = map.get(coreSkuGenerate.getSkuNo());
                EffectDayResult effectDayResult = generateNearTermWarnDays2(Integer.valueOf(effectiveDays));
                StringBuilder append = res.append("UPDATE V_CORE_SKU SET IS_ENABLE_VALIDITY_PERIOD =1,EFFECTIVE_DAY_UNIT=1")
                        .append(",EFFECTIVE_DAYS = ").append(effectiveDays)
                        .append(",NEAR_TERM_WARN_DAYS =").append(effectDayResult.getNearTermWarnDays())
                        .append(",OVER_NEAR_TERM_WARN_DAYS =").append(effectDayResult.getOverNearTermWarnDays())
                        .append(" WHERE SKU_ID=").append(coreSkuGenerate.getSkuId())
                        .append(";");
                bw.newLine();
                bw.write(append.toString());
                bw.flush();
                count++;
            }
        }
        System.out.println("总计:"+count);

        fileInputStream.close();
        fileOutputStream.close();
        isr.close();
        br.close();
        osw.close();
        bw.close();
    }
}
