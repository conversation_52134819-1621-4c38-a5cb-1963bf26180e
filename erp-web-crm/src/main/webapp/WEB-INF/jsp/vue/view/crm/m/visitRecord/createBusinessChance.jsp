<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>新建商机</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover">
    <link rel="stylesheet" href="/mstatic/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/mui/mui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/css/pages/visitRecord.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <jsp:include page="./head_import_im.jsp"></jsp:include>
    <input type="hidden" id="visitRecordId" name="visitRecordId" value="${param.visitRecordId}"/>

    <div class="page-wrap" id="page-container" style="padding-bottom: 53px;">
        <div class="page-container">
            <div class="page-main">
                <div class="business-page-wrap">

                    <div class="form-wrap" v-if="!pageLoading" style="padding-top: 10px;">
                        <div class="form-section">
                            <div class="form-section-title">商机信息</div>
                            <div class="form-card">
                                <ui-form-item label="客户名称" no-padding>
                                    <ui-trader
                                        maxlength="100"
                                        v-model="traderName"
                                        placeholder="请输入"
                                        :need-tyc="true"
                                        :trader-info="traderInfo"
                                        :need-trader-status="false"
                                        :need-trader-detail="false"
                                        @change="handlerTrader"
                                    ></ui-trader>
                                </ui-form-item>
                                <ui-form-item label="业务类型" must>
                                    <ui-radio-group
                                        :list="BusinessTypeRadio"
                                        v-model="businessType"
                                        @change="businessTypeChange"
                                    ></ui-radio-group>
                                </ui-form-item>
                                <ui-form-item label="产品信息" must no-padding vertical>
                                    <ui-textarea
                                        maxlength="500"
                                        show-word-limit
                                        placeholder="请描述客户产品需求"
                                        v-model="goodsInfo"
                                        height="54px"
                                        height-auto
                                    ></ui-textarea>
                                </ui-form-item>
                                <ui-form-item label="预计成单金额" must no-padding>
                                    <ui-input 
                                        v-model="amount"
                                        maxlength="11" 
                                        placeholder="请输入" 
                                    >
                                        <span class="price-dw">元</span>
                                    </ui-input>
                                </ui-form-item>
                                <ui-form-item label="预计成单日期" must no-padding>
                                    <ui-form-date-picker
                                        title="预计成单日期"
                                        v-model="orderTime"
                                        :disabled-date="disabledDate"
                                        placeholder="请选择"
                                        @change="handlerDateChange"
                                    ></ui-form-date-picker>
                                </ui-form-item>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="form-section-title">联系人信息</div>
                            <div class="form-card">
                                <ui-form-item label="手机" no-padding>
                                    <ui-phone-related
                                        v-model="phone"
                                        placeholder="建档客户可输入手机号搜索"
                                        :trader-id="traderId"
                                        @change="handlerPhone"
                                    ></ui-phone-related>
                                </ui-form-item>
                                <ui-form-item label="固话" no-padding>
                                    <ui-input 
                                        v-model="telephone" 
                                        type="tel" 
                                        maxlength="20" 
                                        placeholder="需加上区号，可支持20位数字" 
                                    >
                                        <i v-if="otherContact.length < 5" class="vd-ui_icon icon-add icon" @click="addContact"></i>
                                    </ui-input>
                                </ui-form-item>
                                <ui-other-contact-dialog ref="otherContactDialog" @change="handlerAddContact"></ui-other-contact-dialog>
                                <div class="other-contact-list">
                                    <div class="other-contact-item-wrap" v-for="(item, index) in otherContact"  :key="item.id + '' + index">
                                        <ui-form-item :label="item.label" no-padding>
                                            <div class="other-contact-item">
                                                <ui-input 
                                                    class="contact-input"
                                                    maxlength="50"
                                                    v-model="item.value"
                                                    @input="handlerAddOnInput"
                                                ></ui-input>
                                                <div class="contact-del" @click="handlerAddDelete(index)">
                                                    <i class="vd-ui_icon icon-delete"></i>
                                                </div>
                                            </div>
                                        </ui-form-item>
                                    </div>
                                </div>
                                <ui-form-item label="联系人" must no-padding>
                                    <ui-input 
                                        v-model="contact" 
                                        placeholder="请输入" 
                                        maxlength="20" 
                                        @change="handlerContact"
                                    ></ui-input>
                                </ui-form-item>
                            </div>
                        </div>
                    </div>

                    <div class="form-fixed-bottom">
                        <div class="submit-btn padding">
                            <vd-ui-button @click="submit" type="primary">提交</vd-ui-button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <script src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js"></script>
    <script src="/mstatic/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/mui/mui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/pages/visitRecord/createBusinessChance.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
