package com.vedeng.crm.visitrecord.domain.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/1
 */
@Data
@Setter
@Getter
public class VisitCloseDto {

    /**
     * 拜访计划的ID
     */
    private Integer id;

    /** 关闭原因 */
    private Integer closeReasonType;

    /** 关闭原因说明 */
    private String closeReasonContent;
}
