package com.vedeng.order.model.coupon;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @date $
 */
public class CouponOrderDetailData implements Serializable {

    private static final long serialVersionUID = 1L;
    //订单号
    private String orderNo;
    //客户id
    private Integer traderId;
    //客户名称
    private String traderName;
    //优惠券id
    private Integer couponId;
    //优惠券劵码
    private String couponCode;
    //订单原金额
    private BigDecimal originalAmount;
    //订单实付金额
    private BigDecimal realPayAmount;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public BigDecimal getRealPayAmount() {
        return realPayAmount;
    }

    public void setRealPayAmount(BigDecimal realPayAmount) {
        this.realPayAmount = realPayAmount;
    }

    @Override
    public String toString() {
        return "CouponOrderDetailData{" +
                "orderNo='" + orderNo + '\'' +
                ", traderId=" + traderId +
                ", traderName='" + traderName + '\'' +
                ", couponId=" + couponId +
                ", couponCode='" + couponCode + '\'' +
                ", originalAmount=" + originalAmount +
                ", realPayAmount=" + realPayAmount +
                '}';
    }
}
