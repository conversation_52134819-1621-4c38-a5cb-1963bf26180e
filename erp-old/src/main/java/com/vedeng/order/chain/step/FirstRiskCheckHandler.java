package com.vedeng.order.chain.step;

import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.trader.dto.RegistrationProductionModeDto;
import com.vedeng.erp.trader.service.RegistrationProductionApiService;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.dao.ProductCompanyMapper;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.model.ProductCompany;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.firstengage.model.SimpleMedicalCategory;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.order.chain.AbstractRiskHandlerStep;
import com.vedeng.order.chain.RiskEnum;
import com.vedeng.order.chain.RiskHandlerKeyConstant;
import com.vedeng.order.chain.model.RiskModel;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.wms.service.chain.HandlerStepContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName FirstRiskCheckHandler.java
 * @Description TODO 首营风控信息校验
 * @createTime 2020年12月08日 15:48:00
 */
@Service
public class FirstRiskCheckHandler extends AbstractRiskHandlerStep {

    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Resource
    private RegistrationNumberMapper registrationNumberMapper;

    @Resource
    private ProductCompanyMapper productCompanyMapper;
    @Resource
    ManufacturerMapper manufacturerMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private RegistrationProductionApiService registrationProductionApiService;

    @Override
    public RiskModel doDealWith(HandlerStepContext context) {
        RiskModel riskModel = context.get(RiskHandlerKeyConstant.RISK_MODEL);
        String sku = context.get(RiskHandlerKeyConstant.SKU);

        //注册证号/备案凭证号、注册人/备案人名称、产品名称（注册证/备案凭证）、管理类别、规格、型号（注册证/备案凭证）、
        // 批准日期/备案日期、国标类型、注册证附件/备案凭证附件、是否委托生产、生产企业名称、生产企业生产许可证号或备案凭证编号、
        // 受委托生产的企业名称、受委托生产企业生产许可证号或备案凭证编号
        try {

            SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(sku);
            if(simpleMedicalCategory == null || simpleMedicalCategory.getRegistrationNumberId() == null){
                //非医疗器械
                return riskModel;
            }

            FirstEngage firstEngage = firstEngageMapper.getFirstEngageInfoBySkuNo(sku);

            RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(simpleMedicalCategory.getRegistrationNumberId());

            //调整为对应的生产企业的关联，可能有多个的情况下，进行校验
            List<RegistrationProductionModeDto> productionModeDtoList = registrationProductionApiService.queryRegistrationProductionMode(firstEngage.getRegistrationNumberId());
//            firstEngage.setManufacturerList(productionModeDtoList);

            ProductCompany productCompany = productCompanyMapper.selectByPrimaryKey(registrationNumber.getProductCompanyId());

            Map<String,Object> paramMap = new HashMap<>();
            // 附件类型
            paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
            List<Integer> attachmentFunction = new ArrayList<>();
            // 注册证附件/备案凭证附件
            attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
            paramMap.put("attachmentFunction", attachmentFunction);
            paramMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
            // 所有附件
            List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);

            boolean flag = true;

            if(StringUtil.isBlank(registrationNumber.getRegistrationNumber())){
                //注册证号/备案凭证号
                flag = false;
            }else if(StringUtil.isBlank(registrationNumber.getProductChineseName())){
                //产品名称（注册证/备案凭证）
                flag = false;
            }else if(StringUtil.isBlank(registrationNumber.getModel())){
                //规格、型号（注册证/备案凭证）
                flag = false;
            }else if(registrationNumber.getManageCategoryLevel() == null || registrationNumber.getManageCategoryLevel().equals(0)){
                //管理类别
                flag = false;
            }else if(productCompany == null || StringUtil.isBlank(productCompany.getProductCompanyChineseName())){
                //注册人/备案人名称
                flag = false;
            }else if(registrationNumber.getIssuingDate() == null || registrationNumber.getIssuingDate().equals(0L)){
                //批准日期/备案日期
                flag = false;
            }else if(firstEngage.getStandardCategoryType() == null ||
                    (firstEngage.getStandardCategoryType().equals(1) && firstEngage.getNewStandardCategoryId().equals(0)) ||
                    (firstEngage.getStandardCategoryType().equals(2) && firstEngage.getOldStandardCategoryId().equals(0))){
                //国标类型
                flag = false;
            }else if(CollectionUtils.isEmpty(attachments)){
                //注册证附件/备案凭证附件
                flag = false;
            }else if(CollectionUtils.isEmpty(productionModeDtoList)) {
                //生产企业名称
                //受委托生产的企业名称
                flag = false;
//            } else if(StringUtil.isBlank(manufacturer.getProductCompanyLicence()) && StringUtil.isBlank(manufacturer.getRecordCertificateLicence())){
            }else if(!checkManufactuer(productionModeDtoList)){
                //受委托生产企业生产许可证号或备案凭证编号
                //生产企业生产许可证号或备案凭证编号
                flag = false;
            }

            if(!flag){
                setRiskModel(riskModel);
            }
            return riskModel;
        }catch (Exception e){
            setRiskModel(riskModel);
            return riskModel;
        }
    }
    public boolean checkManufactuer(List<RegistrationProductionModeDto> productionModeDtoList)
    {
        if(CollectionUtils.isEmpty(productionModeDtoList)){
            return false;
        }
        for(RegistrationProductionModeDto productionModeDto : productionModeDtoList){
            Integer manufacturerId = productionModeDto.getManufacturerId();
            Manufacturer manufacturer1= manufacturerMapper.selectByPrimaryKey(manufacturerId);
            if(manufacturer1 == null || (
                    StringUtil.isBlank(manufacturer1.getProductCompanyLicence() ) &&
                            StringUtil.isBlank(manufacturer1.getRecordCertificateLicence() ) )){
              return false;
            }
         }
        return true;
    }

    private void setRiskModel(RiskModel riskModel) {
        Set<RiskEnum> riskEnumList = riskModel.getRiskEnumList();
        riskEnumList.add(RiskEnum.FIRST_RISK);
        riskEnumList.add(RiskEnum.SKU_SPU_FIRST_RISK);
        riskModel.setRiskEnumList(riskEnumList);
        riskModel.setFirstRiskMessage("商品质量风控信息不完整");
        riskModel.setIsRisk(false);
        riskModel.setIsOneChek(false);
    }
}
