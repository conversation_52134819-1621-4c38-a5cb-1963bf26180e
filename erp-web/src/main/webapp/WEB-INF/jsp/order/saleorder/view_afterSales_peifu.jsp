<%@ page language="java" contentType="text/html; charset=UTF-8"
		 pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:choose>
	<c:when test="${afterSalesVo.type eq 3321}">
		<c:set var="title" value="售后详情-未履约赔付" scope="application" />
	</c:when>
	<c:when test="${afterSalesVo.type eq 3322}">
		<c:set var="title" value="售后详情-送货投诉" scope="application" />
	</c:when>
	<c:when test="${afterSalesVo.type eq 3323}">
		<c:set var="title" value="售后详情-安装投诉" scope="application" />
	</c:when>
	<c:when test="${afterSalesVo.type eq 3324}">
		<c:set var="title" value="售后详情-维修投诉" scope="application" />
	</c:when>
	<c:otherwise>
		<c:set var="title" value="售后详情" scope="application" />
	</c:otherwise>
</c:choose>




<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/aftersales/order/view_afterSales.js?rnd=${resourceVersionKey}'></script>
<div class="main-container">
	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">
				基本信息
			</div>
		</div>
		<table class="table">
			<tbody>
			<tr>
				<td class="wid20">订单号</td>
				<td>${afterSalesVo.afterSalesNo}</td>
				<td class="wid20">订单状态</td>
				<td>
					<c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
					<c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
					<c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
					<c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
				</td>
			</tr>
			<tr>
				<td>创建者</td>
				<td>${afterSalesVo.creatorName}</td>
				<td>创建时间</td>
				<td><date:date value ="${afterSalesVo.addTime}"/></td>
			</tr>
			<tr>
				<td>生效状态</td>
				<td>
					<c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
					<c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
				</td>
				<td>生效时间</td>
				<td><date:date value ="${afterSalesVo.validTime}"/></td>
			</tr>
			<tr>
				<td>审核状态</td>
				<td>
					<c:if test="${afterSalesVo.status eq 0}">待审核</c:if>
					<c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
					<c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
					<c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
				</td>
				<td>售后类型</td>
				<td class="warning-color1">${afterSalesVo.typeName}</td>
			</tr>
			</tbody>
		</table>
	</div>

	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">
				售后申请
			</div>
		</div>
		<table class="table">
			<tbody>
			<tr>
				<td class="wid20">联系人</td>
				<td>${afterSalesVo.traderContactName}</td>
				<td>电话</td>
				<td>
					${afterSalesVo.traderContactTelephone}
					<c:if test="${not empty afterSalesVo.traderContactTelephone}">
						<i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
					</c:if>
				</td>
			</tr>
			<tr>
				<td>手机</td>
				<td>
					${afterSalesVo.traderContactMobile}
					<c:if test="${not empty afterSalesVo.traderContactMobile}">
						<i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
					</c:if>
				</td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td>详情说明</td>
				<td colspan="3" class="text-left">${afterSalesVo.comments}</td>
			</tr>
			<tr>
				<td>附件</td>
				<td colspan="3" class="text-left">
					<%@ include file="view_afterSales_files.jsp"%>
				</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">
				所属订单
			</div>
		</div>
		<table class="table">
			<tbody>
			<tr>
				<td class="wid20">订单号</td>
				<td >
					<div class="customername pos_rel">
                               <span class="brand-color1 addtitle" style="float:none;" tabTitle='{"num":"viewsaleorder${afterSalesVo.orderNo}","title":"订单信息",
                               		"link":"./order/saleorder/view.do?saleorderId=${afterSalesVo.orderId}"}'>${afterSalesVo.orderNo}</span><i class="iconbluemouth"></i>
						<div class="pos_abs customernameshow" style="display: none;">
							付款状态：<c:if test="${afterSalesVo.paymentStatus eq 0}">未付款</c:if>
							<c:if test="${afterSalesVo.paymentStatus eq 1}">部分付款</c:if>
							<c:if test="${afterSalesVo.paymentStatus eq 2}">全部付款</c:if><br>
							发货状态：<c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
							<c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
							<c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if><br>
							开票状态：<c:if test="${afterSalesVo.invoiceStatus eq 0}">未开票</c:if>
							<c:if test="${afterSalesVo.invoiceStatus eq 1}">部分开票</c:if>
							<c:if test="${afterSalesVo.invoiceStatus eq 2}">全部开票</c:if><br>
							收货状态：<c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
							<c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
							<c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if>
						</div>
					</div>
				</td>
				<td class="wid20">订单金额</td>
				<td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00" maxFractionDigits="2" /></td>
			</tr>
			<tr>
				<td>部门</td>
				<td>${afterSalesVo.orgName}</td>
				<td>归属销售</td>
				<td>${afterSalesVo.userName}</td>
			</tr>
			<tr>
				<td>订单状态</td>
				<td>
					<c:if test="${afterSalesVo.saleorderStatus eq 0}">待确认</c:if>
					<c:if test="${afterSalesVo.saleorderStatus eq 1}">进行中</c:if>
					<c:if test="${afterSalesVo.saleorderStatus eq 2}">已完结</c:if>
					<c:if test="${afterSalesVo.saleorderStatus eq 3}">已关闭</c:if>
				</td>
				<td>生效时间</td>
				<td><date:date value ="${afterSalesVo.saleorderValidTime}"/></td>
			</tr>
			<tr>
				<td>客户名称</td>
				<td>
					<div class="customername pos_rel">
                                  <span class="brand-color1 addtitle" style="float:none;" tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"客户信息",
										"link":"./trader/customer/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}</span><i class="iconbluemouth"></i>
						<div class="pos_abs customernameshow" style="display: none;">
							客户性质：<c:if test="${afterSalesVo.customerNature eq 465}">分销</c:if>
							<c:if test="${afterSalesVo.customerNature eq 466}">终端</c:if><br>
							交易次数：${afterSalesVo.orderCount}<br>
							交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00" maxFractionDigits="2" /><br>
							上次交易日期：<date:date value ="${afterSalesVo.lastOrderTime}"/>
						</div>
					</div>
				</td>
				<td>客户等级</td>
				<td>
					${afterSalesVo.customerLevelStr}
				</td>
			</tr>
			</tbody>
		</table>

		<div class="table-buttons">
			<c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2}">
				<form action="" method="post" id="myform">
					<input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
					<input type="hidden" name="orderId" value="${afterSalesVo.orderId}"/>
					<input type="hidden" name="subjectType" value="${afterSalesVo.subjectType}"/>
					<input type="hidden" name="type" value="${afterSalesVo.type}"/>
					<input type="hidden" name="formToken" value="${formToken}"/>
					<c:if test="${isSupply ne 1}">
						<c:choose>
							<c:when test="${afterSalesVo.status == 0 || afterSalesVo.status == 3}">
								<input type="hidden" name="taskId" value="${taskInfo.id == null ?0: taskInfo.id}"/>
								<c:if test="${isSupply ne 1}">
									<button type="button" class="bt-bg-style bg-light-green bt-small" onclick="applyAudit(${empty afterSalesVo.afterSalesGoodsList ? 1 : 0});">申请审核</button>
									<button type="button" class="bt-bg-style bg-light-green bt-small" onclick="editAfterSales(1);">编辑</button>
								</c:if>
							</c:when>
							<c:when test="${afterSalesVo.status == 1}">
								<c:if test="${(null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]}">
									<c:choose>
										<c:when test="${taskInfo.assignee == curr_user.username or candidateUserMap['belong']}">
											<c:if test="${(!empty taskInfo.getName()) and  (taskInfo.getName() eq '售后主管审核')  and (!empty afterSalesVo.type) and (afterSalesVo.type eq 540)}">
												<button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="check(${taskInfo.id},${afterSalesVo.afterSalesId})">审核通过</button>
												<button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=2"}'>审核不通过</button>
											</c:if>

											<c:if test="${(empty taskInfo.getName()) or (taskInfo.getName() ne '售后主管审核') or (empty afterSalesVo.type) or (afterSalesVo.type ne 540)}">
												<button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=true&type=2"}'>审核通过</button>
												<button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=2"}'>审核不通过</button>
											</c:if>
										</c:when>
										<c:otherwise>
											<button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
										</c:otherwise>
									</c:choose>
								</c:if>
							</c:when>
							<c:when test="${afterSalesVo.status == 2}">
								<c:if test="${(null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id])}">
									<c:if test="${afterSalesVo.status eq 2}">
										<button type="button" class="bt-bg-style bg-light-green bt-small pop-new-data"
												layerParams='{"width":"780px","height":"310px","title":"选择售后原因","link":"../order/saleorderComplete.do?afterSalesId=${afterSalesVo.afterSalesId}&type=${afterSalesVo.type}&orderId=${afterSalesVo.orderId }&subjectType=${afterSalesVo.subjectType }&formToken=${formToken }&traderId=${afterSalesVo.traderId }"}'>确认完成</button>
									</c:if>
									<c:if test="${afterSalesVo.status ne 1 && afterSalesVo.closeStatus eq 1}">
										<button type="button" class="bt-bg-style bg-light-orange bt-small pop-new-data"
												layerParams='{"width":"780px","height":"310px","title":"选择售后原因","link":"../order/causeOfAfterSales.do?afterSalesId=${afterSalesVo.afterSalesId}&type=${afterSalesVo.type}&orderId=${afterSalesVo.orderId }&subjectType=${afterSalesVo.subjectType }&formToken=${formToken }&traderId=${afterSalesVo.traderId }"}'>关闭订单</button>
									</c:if>
									<c:if test="${afterSalesVo.status eq 0 || afterSalesVo.status eq 3}">
										<input type="hidden" name="taskId" value="${taskInfo.id == null ?0: taskInfo.id}"/>
										<button type="button" class="bt-bg-style bg-light-green bt-small" onclick="applyAudit();">申请审核</button>
										<button type="button" class="bt-bg-style bg-light-green bt-small" onclick="editAfterSales(1);">编辑</button>
									</c:if>
								</c:if>
								<c:if test="${(null!=taskInfoOver and null!=taskInfoOver.getProcessInstanceId() and null!=taskInfoOver.assignee) or !empty candidateUserMapOver[taskInfoOver.id]}">
									<c:choose>
										<c:when test="${taskInfoOver.assignee == curr_user.username or candidateUserMapOver['belong']}">
											<button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=true&type=2"}'>审核通过</button>
											<button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=false&type=2"}'>审核不通过</button>
										</c:when>
										<c:otherwise>
											<button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
										</c:otherwise>
									</c:choose>
								</c:if>
							</c:when>
						</c:choose>
					</c:if>
				</form>
			</c:if>
		</div>
	</div>

	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">产品</div>
		</div>
		<table class="table  table-style10">
			<thead>
			<tr>
				<th class="wid18">产品名称</th>
				<th class="wid10">品牌</th>
				<th class="wid8">型号</th>
				<th class="wid8">物料编码</th>
				<th class="wid8">采购订单</th>
				<th class="wid5">销售价</th>
				<th class="wid8">数量</th>
				<th class="wid5">单位</th>
				<c:if test="${afterSalesVo.type eq 3321}">
				<th class="wid6">货期承诺天数</th>
				</c:if>
			</tr>
			</thead>
			<tbody>
			<c:if test="${not empty afterSalesVo.afterSalesGoodsList}">
				<c:forEach items="${afterSalesVo.afterSalesGoodsList}" var="asg" varStatus="sttaus">
					<tr>
						<td class="text-left">
							<div class="customername pos_rel">
								<c:if test="${asg.isActionGoods==1}"><span style="color:red;">【活动】</span></c:if>
								<span class="brand-color1 addtitle" style="float:none;"
									  tabTitle='{"num":"viewgoods${list.goodsId}","link":"<%= basePath %>/goods/goods/viewbaseinfo.do?goodsId=${asg.goodsId}",
                                       					"title":"产品信息"}'>${asg.goodsName}</span><i class="iconbluemouth"></i>
								<br>${asg.sku}
								<div class="pos_abs customernameshow" style="display: none;">
									售前服务标签：${asg.labelNames}<br/>
									货期承诺天数：<c:choose>
													<c:when test="${asg.perfermenceDeliveryTime != null && asg.perfermenceDeliveryTime ne -1}">
														${asg.perfermenceDeliveryTime}天
													</c:when>
													<c:otherwise>-</c:otherwise>
												</c:choose>
								</div>
							</div>
						</td>
						<td>${asg.brandName}</td>
						<td>${asg.materialCode}</td>
						<td>${asg.model}</td>
						<td>


							<c:if test="${not empty asg.buyorderNos}">
								<c:forEach items="${asg.buyorderNos}" var="buyorder">
									<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./order/buyorder/viewBuyordersh.do?buyorderId=${buyorder.buyorderId}","title":"订单信息"}'>${buyorder.buyorderNo}</a><br>
								</c:forEach>
							</c:if>


						</td>
						<td>${asg.saleorderPrice}</td>
						<td>${asg.saleorderNum}</td>
						<td>${asg.unitName}</td>
						<c:if test="${afterSalesVo.type eq 3321}">
						<td>
							<c:choose>
								<c:when test="${asg.perfermenceDeliveryTime != null && asg.perfermenceDeliveryTime ne -1}">
									${asg.perfermenceDeliveryTime}天
								</c:when>
								<c:otherwise>-</c:otherwise>
							</c:choose>
						</td>
						</c:if>
					</tr>
				</c:forEach>
			</c:if>
			<c:if test="${empty afterSalesVo.afterSalesGoodsList}">
				<tr>
					<td colspan="8">暂无记录</td>
				</tr>
			</c:if>
			</tbody>
		</table>
	</div>

	<div class="parts">
		<div class="title-container title-container-green">
			<div class="table-title nobor">
				物流信息
			</div>
			<a class="title-click" href="javascript:void(0);"  onclick="toWarehouseDetail()">查看出库记录</a>
		</div>
		<table class="table  table-style6">
			<thead>
			<tr>
				<th class="">快递公司</th>
				<th class="">快递单号</th>
				<th class="wid10">发货时间</th>
				<%--                        <th class="wid8">运费</th>--%>
				<th>商品</th>
				<th>备注</th>
				<%--<th class="wid10">操作人</th>--%>
				<th class="wid10">快递状态</th>
				<th>操作</th>
			</tr>
			</thead>
			<tbody id="wl">
			<c:forEach var="express" items="${expressList}">
				<tr>
					<td>${express.logisticsName}</td>
					<td>${express.logisticsNo}</td>
					<td><date:date value ="${express.deliveryTime}" format="yyyy-MM-dd"/></td>
						<%--                        &lt;%&ndash;<td>--%>
						<%--                        	<c:set var="amount" value="0.00"></c:set>--%>
						<%--                        	<c:forEach var="expressDetails" items="${express.expressDetail}">--%>
						<%--                        		<c:set var="amount" value="${amount + expressDetails.amount}"></c:set>--%>
						<%--                        	</c:forEach>--%>
						<%--                        	${amount}--%>
						<%--                        </td>&ndash;%&gt;--%>
					<td class="text-left">
						<c:forEach var="expressDetails" items="${express.expressDetail}">
							<%--                            	<div>${spuMap[expressDetails.goodsId]}&nbsp;&nbsp;&nbsp;${expressDetails.num} ${expressDetails.unitName} </div>--%>
							<div>${expressDetails.goodName}&nbsp;&nbsp;&nbsp;${expressDetails.num} ${expressDetails.unitName} </div>
						</c:forEach>
					</td>
					<td>${express.logisticsComments}</td>
						<%-- <td>${express.updaterUsername}</td>--%>
					<td>
						<c:if test="${express.arrivalStatus == 0}">
							未收货
						</c:if>
						<c:if test="${express.arrivalStatus == 1}">
							部分收货
						</c:if>
						<c:if test="${express.arrivalStatus == 2}">
							全部收货
						</c:if>
					</td>
					<td>
						<div class="customername pos_rel">
							<div class="brand-color1">
								<i class="bt-smaller bt-border-style border-blue pop-new-data" layerParams='{"width":"40%","height":"420px","title":"查看物流","link":"<%=basePath%>warehouse/warehousesout/queryExpressInfo.do?logisticsNo=${express.logisticsNo}"}'>查看物流</i>
							</div>
							<div class="pos_abs customernameshow mouthControlPos">
								最新信息：${express.contentNew}
							</div>
						</div>
					</td>
				</tr>
			</c:forEach>
			<c:if test="${!empty expressList}">
				<tr>
					<td colspan="7" class="allchosetr text-left">
						<!-- 总运费 -->
						<c:set var="allamount" value="0.00"></c:set>
						<!-- 总数量 -->
						<c:set var="allarrivalnum" value="0"></c:set>
						<c:forEach var="express" items="${expressList}">
							<c:set var="amount" value="0.00"></c:set>
							<c:set var="arrivalnum" value="0"></c:set>
							<c:forEach var="expressDetails" items="${express.expressDetail}">
								<c:set var="amount" value="${amount + expressDetails.amount}"></c:set>
								<c:set var="arrivalnum" value="${arrivalnum + expressDetails.num}"></c:set>
							</c:forEach>
							<c:set var="allamount" value="${allamount + amount}"></c:set>
							<c:set var="allarrivalnum" value="${allarrivalnum + arrivalnum}"></c:set>
						</c:forEach>
						<c:set var="allnum" value="0"></c:set>
						<c:forEach var="bgv" items="${saleorderGoodsList}" varStatus="num">
							<c:set var="allnum" value="${allnum + bgv.num}"></c:set>
							<c:set var="allDeliveryNum" value="${allDeliveryNum + bgv.deliveryNum}"></c:set>
						</c:forEach>
						运费总额：<span class="mr10">${allamount}</span>商品总数：<span class="">${allnum}</span>
						已发货总数：<span class="mr10">${allDeliveryNum}</span><span class="warning-color1">待发货数量：${allnum-allDeliveryNum}</span>
					</td>
				</tr>
			</c:if>
			<c:if test="${empty expressList}">
				<tr>
					<td colspan="7">暂无物流信息记录</td>
				</tr>
			</c:if>

			</tbody>
		</table>
	</div>


	<%--回访记录模块--%>
	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">
				回访记录
			</div>
			<c:if test="${afterSalesVo.validStatus eq 1}">
				<div class="title-click nobor  pop-new-data" layerParams='{"width":"1000px","height":"800px","title":"新增回访记录",
                   "link":"<%= basePath %>/order/aftersalesUpgrade/addReturnVisitRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增回访记录</div>
			</c:if>
		</div>
		<div class="parts">
			<table class="table">
				<thead>
				<tr>
					<th>回访客户姓名</th>
					<th>回访客户性质</th>
					<th>回访客户号码</th>
					<th>回访状态</th>
					<th>回访时间</th>
					<th>回访部门</th>
					<th>回访人</th>
					<th>录音ID</th>
					<th>最终分值</th>
					<th>回访结果</th>
					<th>备注</th>
				</tr>
				</thead>
				<tbody>
				<c:forEach items="${returnVisitRecordList}" var="record" varStatus="status">
					<tr>
						<td>${record.customerName}</td>
						<td>
							<c:if test="${record.customerNature eq 1}">终端</c:if>
							<c:if test="${record.customerNature eq 2}">经销商</c:if>
						</td>
						<td>${record.customerMobile}</td>
						<td>
							<c:if test="${record.customerNature eq 1}">已回访</c:if>
							<c:if test="${record.customerNature eq 2}">不知情</c:if>
							<c:if test="${record.customerNature eq 3}">拒绝回答</c:if>
							<c:if test="${record.customerNature eq 4}">拒接</c:if>
							<c:if test="${record.customerNature eq 5}">不知情</c:if>
						</td>
						<td><date:date value="${record.addTime}" format="yyyy-MM-dd HH:mm:ss" /></td>
						<td>
							<c:if test="${record.returnVisitDepartment eq 1}">医修帮</c:if>
							<c:if test="${record.returnVisitDepartment eq 2}">售后</c:if>
						</td>
						<td>${record.creatorName}</td>
						<td>${record.soundRecordId}</td>
						<td>
							<div class="customername pos_rel">
								<span class="brand-color1 addtitle" style="float:none;">${record.totalScore}</span>
								<i class="iconbluemouth"></i>
								<div class="pos_abs customernameshow" style="display: none;">
									服务响应分值：${record.serviceResponseScore}<br>
									服务态度分值：${record.serviceAttitudeScore}<br>
									服务能力分值：${record.serviceCapabilityScore}<br>
									是否有投诉：<c:if test="${record.isComplaint eq 1}">有</c:if><c:if test="${record.isComplaint eq 0}">无</c:if><br>
									是否有推荐：<c:if test="${record.isRecommend eq 1}">有</c:if><c:if test="${record.isRecommend eq 0}">无</c:if>
								</div>
							</div>
						</td>
						<td>
							<c:if test="${record.totalScore >= 80}">满意</c:if>
							<c:if test="${record.totalScore < 80}">不满意</c:if>
						</td>
						<td>${record.comments}</td>
					</tr>
				</c:forEach>
				<c:if test="${empty returnVisitRecordList}">
					<tr>
						<td colspan='11'>暂无记录！</td>
					</tr>
				</c:if>
				</tbody>
			</table>
		</div>

	</div>

	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">
				审核记录
			</div>
		</div>
		<table class="table table-bordered table-striped table-condensed table-centered">
			<thead>
			<tr>
				<th>操作人</th>
				<th>操作时间</th>
				<th>操作事项</th>
				<th>备注</th>
			</tr>
			</thead>
			<tbody>
			<c:if test="${null!=historicActivityInstance}">
				<c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
					<c:if test="${not empty  hi.activityName}">
						<tr>
							<td>
								<c:choose>
									<c:when test="${hi.activityType == 'startEvent'}">
										${startUser}
									</c:when>
									<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
									</c:when>
									<c:otherwise>
										<c:if test="${historicActivityInstance.size() == status.count}">
											${verifyUsers}
										</c:if>
										<c:if test="${historicActivityInstance.size() != status.count}">
											<c:forEach items="${assigneeVos}" var="assigneeVo">
												<c:if test="${assigneeVo.assignee eq hi.assignee}">
													${assigneeVo.realName}
												</c:if>
											</c:forEach>
											<%--      ${hi.assignee}--%>
										</c:if>
									</c:otherwise>
								</c:choose>


							</td>
							<td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
							<td>
								<c:choose>
									<c:when test="${hi.activityType == 'startEvent'}">
										开始
									</c:when>
									<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
										结束
									</c:when>
									<c:otherwise>
										${hi.activityName}
									</c:otherwise>
								</c:choose>
							</td>
							<td class="font-red">${commentMap[hi.taskId]}</td>
						</tr>
					</c:if>
				</c:forEach>
			</c:if>
			<c:if test="${null==historicActivityInstance}">
				<!-- 查询无结果弹出 -->
				<tr>
					<td colspan='4'>暂无记录！</td>
				</tr>
			</c:if>
			</tbody>
		</table>
	</div>
</div>
<script>
	function toWarehouseDetail(){
		$.ajax({
			async:true,
			url:page_url+'/order/saleorder/toWarehouseDetail.do',
			data:{"saleorderId":${saleorderId}},
			type:"POST",
			dataType : "json",
			success:function(data){
				if(data.code==0){
					var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
					var id = 0;
					var uri = page_url+'/warehouse/warehousesout/detailJump.do?saleorderId=${saleorderId}';
					var item = { 'id': id, 'name': "查看出库记录", 'url': uri, 'closable': true };
					self.parent.closableTab.addTab(item);
					self.parent.closableTab.resizeMove();
					$(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
				}else{
					layer.alert("出库记录为空，直发商品需要出库记录可联系物流部处理。")
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	}
</script>
</body>

</html>