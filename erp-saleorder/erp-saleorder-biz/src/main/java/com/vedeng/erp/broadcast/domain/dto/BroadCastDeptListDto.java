package com.vedeng.erp.broadcast.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import java.util.Date;

/**
 * 播报部门列表DTO
 * 专门用于分页查询展示，字段扁平化设计，提高查询性能
 * 对应部门管理页面的表格数据展示
 * 采用两级结构设计：一级部门(大区/业务部) + 二级小组
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BroadCastDeptListDto {
    
    /**
     * 部门ID
     * 对应T_BROADCAST_DEPT表的主键ID
     */
    private Integer id;
    
    /**
     * 父级部门ID
     * 对应T_BROADCAST_DEPT表的PARENT_ID字段
     * 代表该小组所属的一级部门ID
     */
    private Integer deptId;
    
    /**
     * 一级部门名称
     * 如果是一级部门：显示部门名称（如：东北大区、AED业务部）
     * 如果是二级小组：显示所属的一级部门名称（如：东北大区、华东大区）
     */
    private String deptName;
    
    /**
     * 二级小组名称
     * 如果是一级部门：为null
     * 如果是二级小组：显示小组名称（如：线上组、线下组）
     */
    private String groupName;
    
    /**
     * AED用户ID列表
     * 配置的AED用户ID，逗号分隔
     */
    private String aedUserIds;
    
    /**
     * AED用户名列表
     * AED用户的用户名，逗号分隔，用于页面显示
     * 前端可通过分割此字段计算用户数量
     */
    private String aedUsernames;
    
    /**
     * 创建人
     * 创建此部门的用户姓名
     */
    private String creatorRealName;
    
    /**
     * 创建时间
     * 部门的创建时间，用于页面显示
     * 格式：yyyy-MM-dd HH:mm:ss
     * 对应数据库字段：ADD_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
    
    /**
     * 修改时间
     * 部门的最后修改时间，用于页面显示
     * 格式：yyyy-MM-dd HH:mm:ss
     * 对应数据库字段：MOD_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modTime;
    
    // ========== 内部使用字段，不对外暴露 ==========
    
    /**
     * 创建人ID
     * 内部使用，用于权限控制和审计
     */
    private Integer creator;
    
    /**
     * 更新人ID
     * 内部使用，用于权限控制和审计
     */
    private Integer updater;
    
    /**
     * 是否删除
     * 0-正常，1-已删除
     */
    private Integer isDeleted;
} 