.visit-detail-wrap {
  position: relative;
  padding-top: 60px;
  padding-right: 360px;
}
.visit-detail-wrap .visible-xs {
  display: none;
}
.visit-detail-wrap .chanceDetail-header-wrap {
  width: 100%;
  min-width: 760px;
  height: 60px;
  background: #fff;
  border-bottom: solid 1px #E1E5E8;
  position: fixed;
  top: 50px;
  z-index: 9;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content {
  flex: 1;
  min-width: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-left {
  display: flex;
  align-items: center;
  padding: 0 15px;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-left .title {
  font-size: 20px;
  font-weight: 700;
  color: #333333;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-left .status {
  height: 27px;
  line-height: 27px;
  padding: 0 10px;
  border-radius: 3px;
  color: #FF6600;
  background-color: rgba(255, 102, 0, 0.1);
  margin-left: 15px;
  font-size: 14px;
  margin-top: 1px;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-left .status.status1 {
  background: #ffede1;
  color: #F60;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-left .status.status2 {
  background: #e1f3ff;
  color: #09F;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-left .status.status3 {
  background: #E3F7E3;
  color: #13BF13;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-left .status.status4 {
  background: #E3EAF0;
  color: #1A4D80;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-right {
  display: flex;
  align-items: center;
  padding-right: 10px;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-content .header-right .vd-ui-button {
  margin-right: 10px;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .header-md-aside {
  position: relative;
  width: 360px;
  display: flex;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .header-md-aside::before {
  content: "";
  height: 72px;
  width: 1px;
  background: #E1E5E8;
  position: absolute;
  left: 0;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .vd-ui-title-tip-wrap {
  flex: 1;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .header-xs-aside {
  display: none;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item {
  height: 60px;
  position: relative;
  flex: 1;
  background-size: 24px 24px;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item::before {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  content: "";
  z-index: 1;
  background-size: 24px 24px;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.msg {
  background-image: url('../../image/icon/msg.svg');
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.msg::before {
  background-image: url('../../image/icon/msg-active.svg');
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.time {
  background-image: url('../../image/icon/time.svg');
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.time::before {
  background-image: url('../../image/icon/time-active.svg');
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.record {
  background-image: url('../../image/icon/record.svg');
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.record::before {
  background-image: url('../../image/icon/record-active.svg');
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.user {
  background-image: url('../../image/icon/user.svg');
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.user::before {
  background-image: url('../../image/icon/user-active.svg');
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active,
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item:hover {
  background-image: none;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active::before,
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item:hover::before {
  opacity: 1;
}
.visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active::after {
  content: "";
  display: block;
  width: 30px;
  height: 2px;
  background: #09f;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.visit-detail-wrap .deatil-page-top-bg {
  height: 150px;
  background: linear-gradient(to bottom, #ddeefa 0%, #f5f7fa 100%);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}
.visit-detail-wrap .deatil-page-top-bg.finish {
  background: linear-gradient(to bottom, #e0f2e4 0%, #f5f7fa 100%);
}
.visit-detail-wrap .deatil-page-top-bg.close {
  background: linear-gradient(to bottom, #E3EAF0 0%, #f5f7fa 100%);
}
.visit-detail-wrap .deatil-page-top-bg.wait {
  background: linear-gradient(to bottom, #ffede1 0%, #f5f7fa 100%);
}
.visit-detail-wrap .detail-page-wrap {
  padding: 0 20px 20px;
  position: relative;
}
.visit-detail-wrap .detail-page-wrap .chance-step {
  height: 90px;
  max-width: 1240px;
  min-width: 960px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}
.visit-detail-wrap .detail-page-wrap .main {
  position: relative;
  z-index: 2;
}
.visit-detail-wrap .detail-page-wrap .main .show-font {
  margin-top: 10px;
}
.visit-detail-wrap .detail-page-wrap .main .show-font:first-child {
  margin-top: 0;
}
.visit-detail-wrap .detail-page-wrap .right-aside {
  width: 360px;
  background: #fff;
  position: fixed;
  right: 0;
  top: 50px;
  z-index: 3;
  min-height: 500px;
  bottom: 0;
  padding-top: 60px;
}
.visit-detail-wrap .detail-page-wrap .right-aside .right-aside-inner {
  height: 100%;
  border-left: solid 1px #E1E5E8;
}
.visit-detail-wrap .detail-page-wrap .other-contant-gap {
  margin-bottom: 5px;
}
.visit-detail-wrap .detail-page-wrap .trader-detail {
  display: flex;
  align-items: center;
  color: #999;
  margin-top: 5px;
  flex-wrap: wrap;
}
.visit-detail-wrap .detail-page-wrap .trader-detail .detail-item {
  margin-right: 21px;
  position: relative;
}
.visit-detail-wrap .detail-page-wrap .trader-detail .detail-item::before {
  content: '';
  position: absolute;
  width: 1px;
  height: 12px;
  top: 3px;
  right: -10px;
  background: #e1e5e8;
}
.visit-detail-wrap .detail-page-wrap .trader-detail .detail-item:last-child {
  margin-right: 0;
}
.visit-detail-wrap .detail-page-wrap .trader-detail .detail-item:last-child::before {
  display: none;
}
.visit-detail-wrap .card-top-tip {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  color: #f60;
}
.visit-detail-wrap .card-top-tip .icon-caution1 {
  line-height: 1;
  font-size: 16px;
  margin-right: 5px;
}
.visit-detail-wrap .card-top-option {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
}
.visit-detail-wrap .card-top-option .option-item {
  color: #09f;
  cursor: pointer;
  margin-left: 10px;
  display: flex;
  align-items: center;
}
.visit-detail-wrap .card-top-option .option-item .icon-add {
  font-size: 16px;
  line-height: 1;
  margin-right: 5px;
}
.visit-detail-wrap .card-top-option .option-item:hover {
  color: #f60;
}
.visit-detail-wrap .no-padding {
  margin: 0 -20px -20px;
}
.visit-detail-wrap .map-address-wrap {
  display: flex;
  align-items: flex-start;
}
.visit-detail-wrap .map-address-wrap .ui-view-map-wrap {
  margin-top: -3px;
  margin-left: 10px;
}
.visit-detail-wrap .user-label-wrap {
  padding: 5px;
  background: #F5F7FA;
  border-radius: 3px;
  display: inline-block;
}
.visit-detail-wrap .user-label-list {
  margin-bottom: -10px;
  display: flex;
  flex-wrap: wrap;
}
.visit-detail-wrap .user-label-list .user-label-wrap {
  margin-right: 10px;
  margin-bottom: 10px;
}
.visit-detail-wrap .pic-list-tip {
  color: #999;
  margin-top: 5px;
}
.visit-status-tag {
  line-height: 22px;
  padding: 0 5px;
  border-radius: 2px;
  display: inline-block;
}
.visit-status-tag.status-1 {
  background: #FFEDE0;
  color: #FF6600;
}
.visit-status-tag.status-2 {
  background: #E0F3FF;
  color: #0099FF;
}
.visit-status-tag.status-3 {
  background: #E3F7E3;
  color: #13BF13;
}
.visit-status-tag.status-4 {
  background: #E3EAF0;
  color: #1A4D80;
}
.vd-ui-td .visit-status-tag {
  margin: -2px 0;
}
.vd-ui-td .visit-address-wrap {
  display: flex;
  align-items: center;
}
.vd-ui-td .visit-address-wrap .visit-address-txt {
  flex: 1;
  margin-right: 10px;
}
.trader-tip-info {
  display: flex;
  align-items: center;
  color: #999;
  margin-top: 5px;
}
.trader-tip-info .trader-info-item {
  margin-right: 21px;
  position: relative;
}
.trader-tip-info .trader-info-item::before {
  content: "";
  width: 1px;
  height: 12px;
  position: absolute;
  right: -10px;
  top: 3px;
  background: #e1e5e8;
}
.trader-tip-info .trader-info-item:last-child {
  margin-right: 0;
}
.trader-tip-info .trader-info-item:last-child::before {
  display: none;
}
.long-form-footer {
  padding-left: 210px;
}
.visit-form-wrap .more-contact-component .add-contact {
  padding-left: 210px;
}
.visit-form-wrap .more-contact-component .vd-ui-input {
  width: 236px !important;
}
.visit-form-wrap .more-contact-component .contact-list .contact-item .delete {
  left: 446px;
}
.visit-form-wrap .vd-ui-select {
  width: 100%;
}
.visit-form-wrap .visit-form-trader .trader-name {
  display: flex;
}
.visit-form-wrap .visit-form-trader .trader-detail {
  display: flex;
  align-items: center;
  color: #999;
  margin-top: 5px;
}
.visit-form-wrap .visit-form-trader .trader-detail .detail-item {
  margin-right: 21px;
  position: relative;
}
.visit-form-wrap .visit-form-trader .trader-detail .detail-item::before {
  content: '';
  position: absolute;
  width: 1px;
  height: 12px;
  top: 3px;
  right: -10px;
  background: #e1e5e8;
}
.visit-form-wrap .visit-form-trader .trader-detail .detail-item:last-child {
  margin-right: 0;
}
.visit-form-wrap .visit-form-trader .trader-detail .detail-item:last-child::before {
  display: none;
}
.visitor-form-container .business-info-wrap {
  color: #999;
  margin-top: 5px;
}
.visitor-form-container .business-info-wrap .info-item {
  margin-top: 0;
}
.visitor-form-container .business-info-wrap .business-info-tip {
  display: flex;
  color: #f60;
  margin-top: 5px;
}
.visitor-form-container .business-info-wrap .business-info-tip .icon-caution1 {
  font-size: 16px;
  line-height: 1;
  margin-right: 5px;
  margin-top: 1px;
}
.visitor-form-container .business-info-wrap .business-info-tip .tip-txt {
  flex: 1;
}
.visitor-form-container .map-address-wrap {
  display: flex;
  align-items: flex-start;
}
.visitor-form-container .map-address-wrap .ui-view-map-wrap {
  margin-left: 10px;
}
.visitor-form-container .map-address-wrap .ui-view-map-wrap .ui-view-map-btn {
  line-height: 30px;
}
.form-status-tip {
  display: flex;
  align-items: center;
  margin-top: 5px;
}
.form-status-tip .vd-ui_icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
}
.form-status-tip.tip-warn {
  color: #f60;
}
.form-status-tip.tip-success {
  color: #13BF13;
}
@media screen and (max-width: 1366px) {
  .visit-detail-wrap {
    padding-right: 0;
  }
  .visit-detail-wrap .hidden-xs {
    display: none;
  }
  .visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .header-md-aside {
    display: none;
  }
  .visit-detail-wrap .chanceDetail-header-wrap .header-main .header-aside-wrap .header-xs-aside {
    position: relative;
    width: 64px;
    margin-left: -10px;
    display: flex;
  }
}
