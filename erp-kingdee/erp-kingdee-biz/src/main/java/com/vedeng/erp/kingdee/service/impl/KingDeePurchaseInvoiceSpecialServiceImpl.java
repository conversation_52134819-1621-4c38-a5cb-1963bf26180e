package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceVoucherMapper;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatSpecialInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatSpecialInvoiceConvertor;
import com.vedeng.erp.kingdee.repository.KingDeePurchaseVatSpecialInvoiceRepository;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInvoiceSpecialApiService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInvoiceSpecialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 其他入库单接口实现类
 * @date 2023/3/6 9:00
 **/
@Service
@Slf4j
public class KingDeePurchaseInvoiceSpecialServiceImpl extends KingDeeBaseServiceImpl<
        KingDeePurchaseVatSpecialInvoiceEntity,
        PurchaseVatSpecialInvoiceDto,
        KingDeePurchaseVatSpecialInvoiceCommand,
        KingDeePurchaseVatSpecialInvoiceRepository,
        KingDeePurchaseVatSpecialInvoiceConvertor,
        KingDeePurchaseVatSpecialInvoiceCommandConvertor> implements KingDeePurchaseInvoiceSpecialService, KingDeePurchaseInvoiceSpecialApiService {

    @Autowired
    private BatchInvoiceVoucherMapper batchInvoiceVoucherMapper;

    @Override
    public void savePost(Object... objects) {
        PurchaseVatSpecialInvoiceDto d = getD(objects);
        BatchInvoiceVoucherDto build = BatchInvoiceVoucherDto.builder().invoiceId(d.getFQzokBddjtid()).build();
        List<BatchInvoiceVoucherDto> batchInvoiceVoucherDtos = batchInvoiceVoucherMapper.selectByInvoiceId(build.getInvoiceId());
        if (CollUtil.isEmpty(batchInvoiceVoucherDtos)) {
            log.info("记录采购蓝票推送金蝶{}", JSON.toJSONString(build));
            batchInvoiceVoucherMapper.insertSelective(build);
        }

    }

}
