void function () {
    // math.config({
    //     number: 'BigNumber'
    // })

    new Vue({
        el: '#page-container',
        data: {
            flowOrderId: '',
            flowOrderNo: '',
            baseOrderId: '',
            auditStatus: '',
            baseBusinessNo: '',
            baseBusinessType: '',
            pushDirection: '',
            sourceErp: '',
            isEdit: false,
            pageloading: true,
            supplierData: [],
            paymentData: [{
                payType: 1,
                invoiceType: '',
            }],
            goodsData: [],
            tableCustomTh: [{
                label: 'item1'
            }],
            supplierOptions: [],
            supplierLoading: false,
            payTypeList: [{
                label: '先款后货，预付100%',
                value: 0,
            }, {
                label: '自定义',
                value: 1,
            }],
            invoiceTypeList: [],
            isShowGoodsTable: true,
            defaultSupplierGoodsData: {},
            //批量编辑加价率
            rateDialogFormValue: undefined,
            rateDialogVisible: false,
            isRateformError: '',
            rateEditIndex: -1,
            supplierTableKey: 1,
            totalAddRate: {},
            totalProfitRate: {},
            submitFlag: false,
            supplierError: false,
            goodsError: false,
            cansubmit: true,
            isShowLeft: true,
            sourceErpName: '',
        },
        computed: {
            supplierShowList() {
                return JSON.parse(JSON.stringify(this.supplierData)).reverse();
            }
        },
        created() {
            this.baseBusinessNo = this.getQuery('baseBusinessNo');
            this.baseBusinessType = this.getQuery('baseBusinessType');
            this.pushDirection = this.getQuery('pushDirection');
            this.sourceErp = this.getQuery('sourceErp');
            this.flowOrderId = this.getQuery('flowOrderId');
            if(this.flowOrderId) {
                this.isEdit = true;
            }

            this.initPageData();

            if(this.baseBusinessType == 1) {
                document.title = '采购单';
            } else {
                document.title = '销售单';
            }
        },
        methods: {
            getQuery(key) {
                let params = new URLSearchParams(window.location.search);
                return params.get(key) || '';
            },
            initPageData() {
                let loading = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.6)',
                    customClass: 'global-loading'
                });

                let url = '';

                if(this.isEdit) {
                    url = '/flowOrder/get.do?flowOrderId=' + this.flowOrderId;
                    axios.get(url).then(({ data }) => {
                        loading.close();
    
                        this.initData(data)
                    })
                } else {
                    url = '/flowOrder/init.do?baseBusinessNo=' + this.baseBusinessNo + '&baseBusinessType=' + this.baseBusinessType + 
                            '&pushDirection=' + this.pushDirection + '&sourceErp=' + this.sourceErp;
                    axios.post(url).then(({ data }) => {
                        loading.close();
    
                        this.initData(data)
                    })
                }


                axios.post('/flowOrder/getInvoiceType.do').then(({ data }) => {
                    if (data.code === 0) {
                        this.invoiceTypeList = data.data;
                    }
                })
            },
            initData(data) {
                if (data.code === 0) {
                    if(this.isEdit) {
                        this.baseBusinessNo = data.data.baseOrderNo;
                        this.baseBusinessType = data.data.baseBusinessType;
                        this.pushDirection = data.data.pushDirection || '';
                        this.sourceErp = data.data.sourceErp || '';
                    }
                    this.flowOrderId = data.data.flowOrderId;
                    this.flowOrderNo = data.data.flowOrderNo;
                    this.baseOrderId = data.data.baseOrderId;
                    this.sourceErpName = data.data.sourceErpName || '南京贝登医疗股份有限公司';

                    this.supplierData = data.data.flowNodeDto || [];
                    this.supplierData.forEach(item => {
                        item.isInvoice = !!item.openInvoice;
                        item.prevTraderId = item.traderId;
                    })

                    console.log(this.supplierData)

                    this.goodsData = data.data.flowOrderDetailList || [];

                    this.goodsData.forEach(item => {
                        if(!item.flowNodeOrderDetailPriceDtoList) {
                            item.flowNodeOrderDetailPriceDtoList = [{
                                flowNodeOrderPriceId: '',
                                flowOrderDetailId: item.flowOrderDetailId,
                                nodeLevel: '',
                                price: 0,
                                markupRate: 0,
                                grossProfitRate: '',
                            }];
                        }
                    })

                    this.pageloading = false;
                } else {
                    this.$alert(data.message, '', {
                        confirmButtonText: '我知道了'
                    });
                }
            },
            addSupplier() {
                if (this.supplierData.length >= 9) {
                    return;
                }

                this.supplierData.push({
                    flowNodeId: '',
                    flowOrderId: '',
                    openInvoice: 1,
                    nodeLevel: '',
                    paymentMethod: 0,
                    invoiceType: 972,
                    amount: 0,
                    creditPayment: 0,
                    balance: 0,
                    balanceDueDate: 0,
                    traderId: '',
                    traderName: '',
                    traderContactId: '',
                    traderContactName: '',
                    traderContactPhone: '',
                    traderAddressId: '',
                    traderContactAddress: '',
                    receiverTraderContactId: '',
                    receiverName: '',
                    receiverPhone: '',
                    receiverAddressId: '',
                    receiverAddress: '',
                    invoiceReceiverTraderContactId: '',
                    invoiceReceiverName: '',
                    invoiceReceiverPhone: '',
                    invoiceReceiverAddressId: '',
                    invoiceReceiverAddress: '',
                    isInvoice: true,
                    prevTraderId: ''
                })

                this.goodsData.forEach(item => {
                    item.flowNodeOrderDetailPriceDtoList.push({
                        flowNodeOrderPriceId: '',
                        flowOrderDetailId: '',
                        flowNodeId: '',
                        nodeLevel: '',
                        price: 0,
                        markupRate: 0,
                        grossProfitRate: '',
                    })
                })
            },
            //是否开票选中
            isInvoiceChange(index) {
                console.log(this.supplierData[index].isInvoice)
                this.supplierData[index].openInvoice = this.supplierData[index].isInvoice ? 1 : 0;

                //这个key没有啥用，纯粹为了改变一下值，使得数据刷新，不然element的checkbox组件值不更新（因为绑定的对象太深）
                this.supplierTableKey++;
            },
            parseSupplierList(list) {
                list = list || [];
                list.forEach(item => {
                    if (!item.riskPass) {
                        item.disabled = true;
                    }

                    item.label = item.traderName;
                    item.value = item.traderId;
                })

                return list;
            },
            async supplierChange(index, data) {
                console.log(data)
                if (this.supplierData[index].traderId == this.supplierData[index].prevTraderId) {
                    return;
                }

                //供应商选择赋值并清空后面联动字段的值
                this.supplierData[index] = Object.assign(this.supplierData[index], {
                    traderName: data.selected.label,
                    prevTraderId: data.selected.value,
                    traderContactId: '',
                    traderContactName: '',
                    traderContactPhone: '',
                    traderAddressId: '',
                    traderContactAddress: '',
                    receiverTraderContactId: '',
                    receiverName: '',
                    receiverPhone: '',
                    receiverAddressId: '',
                    receiverAddress: '',
                    invoiceReceiverTraderContactId: '',
                    invoiceReceiverName: '',
                    invoiceReceiverPhone: '',
                    invoiceReceiverAddressId: '',
                    invoiceReceiverAddress: '',
                })

                // 自动获取默认联系人联系地址
                await axios.get('/flowOrder/getTraderContact.do?keywords=&traderType=' + (this.baseBusinessType == 1 ? 2: 1) + '&traderId=' + this.supplierData[index].traderId).then(({data}) => {
                    if(data.code == 0 && data.data.length) {
                        data.data.forEach(item => {
                            if(item.isDefault == 1) {
                                this.supplierData[index].traderContactId = item.traderContactId;
                                this.supplierData[index].traderContactName = item.contactName;
                                this.supplierData[index].traderContactPhone = item.contactPhone;
                                this.supplierData[index].receiverTraderContactId = item.traderContactId;
                                this.supplierData[index].receiverName = item.contactName;
                                this.supplierData[index].receiverPhone = item.contactPhone;
                              
                                if(this.baseBusinessType == 2) {
                                    this.supplierData[index].invoiceReceiverTraderContactId = item.traderContactId;
                                    this.supplierData[index].invoiceReceiverName = item.contactName;
                                    this.supplierData[index].invoiceReceiverPhone = item.contactPhone;
                                }
                            }
                        })
                    }
                })

                await axios.get('/flowOrder/getTraderAddress.do?keywords=&traderType=' + (this.baseBusinessType == 1 ? 2: 1) + '&traderId=' + this.supplierData[index].traderId).then(({data}) => {
                    if(data.code == 0 && data.data.length) {
                        data.data.forEach(item => {
                            if(item.isDefault == 1) {
                                this.supplierData[index].traderAddressId = item.traderAddressId;
                                this.supplierData[index].traderContactAddress = item.region + ' ' +item.address;
                                this.supplierData[index].receiverAddressId = item.traderAddressId;
                                this.supplierData[index].receiverAddress = item.region + ' ' +item.address;
                              
                                // if(this.baseBusinessType == 2) {
                                //     this.supplierData[index].invoiceReceiverAddressId = item.traderAddressId;
                                //     this.supplierData[index].invoiceReceiverAddress = item.region + ' ' +item.address;
                                // }
                            }
                        })
                    }
                })

                console.log(this.supplierData[index])

                this.$set(this.supplierData, this.supplierData);

                this.checkSubmit();
            },
            parseContactList(list) {
                list = list || [];
                list.forEach(item => {
                    item.label = item.contactName + ' ' + item.contactPhone;
                    item.value = item.traderContactId;
                })

                return list;
            },
            parseAddressList(list) {
                list = list || [];
                list.forEach(item => {
                    item.label = item.region + ' ' + item.address;
                    item.value = item.traderAddressId;
                })

                return list;
            },
            contactChange(index, data) {
                this.supplierData[index].traderContactName = data.selected.contactName || '';
                this.supplierData[index].traderContactPhone = data.selected.contactPhone || '';
                this.checkSubmit();
            },
            receiverChange(index, data) {
                this.supplierData[index].receiverName = data.selected.contactName || '';
                this.supplierData[index].receiverPhone = data.selected.contactPhone || '';
                this.checkSubmit();
            },
            invoiceReceiverChange(index, data) {
                this.supplierData[index].invoiceReceiverName = data.selected.label;
                this.supplierData[index].invoiceReceiverPhone = data.selected.contactPhone || '';
                this.checkSubmit();
            },
            traderAddressChange(index, data) {
                this.supplierData[index].traderContactAddress = data.selected.label;
                this.checkSubmit();
            },
            receiverAddressChange(index, data) {
                this.supplierData[index].receiverAddress = data.selected.label;
                this.checkSubmit();
            },
            // invoiceReceiverAddressChange(index, data) {
            //     this.supplierData[index].invoiceReceiverAddress = data.selected.label;
            //     this.checkSubmit();
            // },
            deleteSupplier(index) {
                let supplierInfo = this.supplierData[index];
                let supplierName = supplierInfo.traderName ? '“' + supplierInfo.traderName + '”' : ''

                this.$confirm(`删除${supplierName}后，相关节点的数据会被对应删除，是否确定删除?`, '', {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    confirmButtonClass: 'el-button--danger',
                    type: 'warning'
                }).then(() => {
                    this.goodsData.forEach(item => {
                        item.flowNodeOrderDetailPriceDtoList.splice(item.flowNodeOrderDetailPriceDtoList.length - 1, 1)
                    })
                    this.supplierData.splice(index, 1);
                }).catch(() => {

                });
            },
            goodsPriceChange(priceIndex, goodsIndex, type) {
                let calculatePriceFromMarkupRate = type || 0;

                this.goodsData.forEach((goodsItem, gIndex) => {
                    goodsItem.flowNodeOrderDetailPriceDtoList.forEach((priceItem, pIndex) => {
                        if(priceIndex == pIndex && (goodsIndex == gIndex || goodsIndex == 'all')) {
                            priceItem.calculatePriceFromMarkupRate = '1' + calculatePriceFromMarkupRate;
                        } else {
                            priceItem.calculatePriceFromMarkupRate = '00';
                        }
                    })
                })

                let reqData = this.getReqData();

                axios.post('/flowOrder/calculateNodeAmount.do', reqData).then(({data}) => {
                    if(data.code === 0) {
                        if(goodsIndex == 'all') {
                            this.goodsData = data.data.flowOrderDetailList;
                        } else {
                            this.goodsData[goodsIndex].flowNodeOrderDetailPriceDtoList = data.data.flowOrderDetailList[goodsIndex].flowNodeOrderDetailPriceDtoList;
                        }

                        this.supplierData.forEach((item, index) => {
                            this.payPriceChange(index, 'total')
                        })

                        this.triggerGoodsTableInit();
                    }
                });

                this.checkSubmit();
            },
            getGoodsSummary(params) {
                console.log(params)


                let sums = [];

                params.columns.forEach((item, index) => {
                    if (index == 0) {
                        sums[index] = '合计';
                        return;
                    }

                    if (item.label === '数量') {
                        let sum = 0;
                        params.data.forEach(dataItem => {
                            sum += dataItem.quantity;
                        })
                        sums[index] = sum;
                    } 
                    // else if (item.label && (item.label.indexOf('采购价') !== -1 || item.label.indexOf('销售价') !== -1)) {
                    //     let sum = 0;
                    //     let hasEmpty = false;
                    //     console.log('columns:', item)
                    //     params.data.forEach(dataItem => {
                    //         let sindex = item.property - 1;
                    //         let price = 0;

                    //         if(this.baseBusinessType == 1) {
                    //             if(dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex]) {
                    //                 price = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex].price;
                    //             }
                    //         } else {
                    //             price = dataItem.flowNodeOrderDetailPriceDtoList[sindex].price;
                    //         }

                    //         if (price && price != 0) {
                    //             console.log(price)
                    //             sum += price;
                    //         } else {
                    //             hasEmpty = true;
                    //         }
                    //     })

                    //     if (hasEmpty) {
                    //         sum = '-'
                    //     } else {
                    //         sum = this.toFixed2(sum);
                    //     }

                    //     sums[index] = sum;
                    // } 
                    else if (item.label && (item.label.indexOf('采购金额') !== -1 || item.label.indexOf('销售金额') !== -1)) {
                        let sum = 0;
                        let hasEmpty = false;

                        params.data.forEach(dataItem => {
                            let sindex = item.property - 1;
                            let price = 0;
                        
                            if(this.baseBusinessType == 1) {
                                if(dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex]) {
                                    price = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex].price;
                                }
                            } else {
                                price = dataItem.flowNodeOrderDetailPriceDtoList[sindex].price;
                            }

                            if (price && price != 0) {
                                sum += dataItem.quantity * price;
                            } else {
                                hasEmpty = true;
                            }
                        })

                        if (hasEmpty) {
                            sum = '-'
                        } else {
                            sum = this.toFixed2(sum);
                        }

                        sums[index] = sum;
                    } else if (item.label && item.label.indexOf('加价率') !== -1) {
                        let sum = 0;
                        let hasEmpty = false;
                        let totalPrice = 0;
                        let nextTotalPrice = 0;

                        let sindex = item.property - 1;
                        params.data.forEach(dataItem => {
                            let price = 0;
                            let nextPrice = 0;

                            if(this.baseBusinessType == 1) {
                                if(dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - sindex] && dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex]) {
                                    price = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - sindex].price;
                                    nextPrice = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex].price;
                                }
                            } else {
                                if(sindex === 0) {
                                    hasEmpty = true;
                                    price = '';
                                } else {
                                    price = dataItem.flowNodeOrderDetailPriceDtoList[sindex - 1].price;
                                    nextPrice = dataItem.flowNodeOrderDetailPriceDtoList[sindex].price;
                                }
                            }
                            
                            if (price && price != 0 && nextPrice && nextPrice != 0) {
                                totalPrice += parseFloat(price) * parseFloat(dataItem.quantity);
                                nextTotalPrice += parseFloat(nextPrice) * parseFloat(dataItem.quantity);
                            } else {
                                hasEmpty = true;
                            }
                        })

                        if (hasEmpty) {
                            sum = '-'
                        } else {
                            if(totalPrice) {
                                sum = this.toFixed2((nextTotalPrice - totalPrice) / totalPrice * 100) + '%';
                            } else {
                                sum = '-';
                            }
                        }

                        this.$set(this.totalAddRate, sindex, sum)
                        sums[index] = sum;
                    } else if (item.label && item.label.indexOf('毛利率') !== -1) {
                        let sindex = item.property - 1;
                        let sum = 0;
                        console.log('maolilv', sindex)
                        if(sindex < 1) {
                            sum = '-'
                        } else {
                            let hasEmpty = false;
                            let totalPrice = 0;
                            let nextTotalPrice = 0;
    
                            params.data.forEach(dataItem => {
                                price = 0;
                                nextPrice = 0;

                                if(this.baseBusinessType == 1) {
                                    if(dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - sindex] && dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex]) {
                                        price = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - sindex].price;
                                        nextPrice = dataItem.flowNodeOrderDetailPriceDtoList[this.supplierData.length - 1 - sindex].price;
                                    }
                                } else {
                                    price = dataItem.flowNodeOrderDetailPriceDtoList[sindex - 1].price;
                                    nextPrice = dataItem.flowNodeOrderDetailPriceDtoList[sindex].price;
                                }
    
                                if (price) {
                                    totalPrice += parseFloat(price) * parseFloat(dataItem.quantity);
                                    nextTotalPrice += parseFloat(nextPrice) * parseFloat(dataItem.quantity);
                                } else {
                                    hasEmpty = true;
                                }
                            })
                            console.log(hasEmpty, nextTotalPrice)
                            if (hasEmpty) {
                                sum = '-'
                            } else {
                                if(nextTotalPrice) {
                                    sum = this.toFixed2((nextTotalPrice - totalPrice) / nextTotalPrice * 100) + '%';
                                } else {
                                    sum = '-';
                                }
                            }
    
                        }
                        console.log(sum)
                        this.$set(this.totalProfitRate, sindex, sum)
                        sums[index] = sum;
                    } else {
                        sums[index] = '';
                    }

                })

                return sums;
            },
            showRateDialog(index) {
                this.rateEditIndex = index;
                this.rateDialogFormValue = undefined;
                this.isRateformError = '';
                this.rateDialogVisible = true;
            },
            validRateDialogForm() {
                if (!this.rateDialogFormValue && this.rateDialogFormValue !== 0) {
                    this.isRateformError = '请填写加价率';
                    return false;
                } else {
                    this.isRateformError = '';
                    return true;
                }
            },
            multiAddRate() {
                if (this.validRateDialogForm()) {
                    this.goodsData.forEach((item, index) => {
                        item.flowNodeOrderDetailPriceDtoList[this.rateEditIndex].markupRate = this.rateDialogFormValue;
                    })

                    this.rateDialogVisible = false;

                    this.goodsPriceChange(this.rateEditIndex, 'all', 1)
                }
            },
            triggerGoodsTableInit() {
                console.log('aaa')
                this.$set(this.goodsData, this.goodsData);
            },
            //计算采购金额
            calcGoodsItemLevelTotal(index, item) { 
                let price = item.flowNodeOrderDetailPriceDtoList[index].price;
                if(item.quantity && (price || price === 0)) {
                    return this.toFixed2(item.quantity * price);
                } else {
                    return '-'
                }
            },
            toFixed2(num){
                return Math.round(num * 100) / 100;
            }, 
            payPriceChange(index, type) {
                let item = this.supplierData[index];
                let total = 0;
                let hasEmpty = false;

                this.goodsData.forEach(goods => {
                    let price = goods.flowNodeOrderDetailPriceDtoList[index].price;
                    if(price || price === 0) {
                        total += goods.quantity * goods.flowNodeOrderDetailPriceDtoList[index].price;
                    } else {
                        hasEmpty = true;
                    }
                })

                if(!hasEmpty) {
                    if(type === 'amount') {
                        this.supplierData[index].creditPayment = this.toFixed2(total - item.amount - item.balance);
                    }

                    if(type === 'creditPayment' || type === 'balance' || type === 'total') {
                        this.supplierData[index].amount = this.toFixed2(total - item.creditPayment - item.balance);
                    }
                }
            },
            handlerPaymethodChange(index) {
                console.log(this.supplierData[index].paymentMethod)
                if(this.supplierData[index].paymentMethod == 0) {
                    this.supplierData[index].creditPayment = 0;
                    this.supplierData[index].balance = 0;

                    this.payPriceChange(index, 'total')
                }
            },
            getReqData() {
                this.goodsData.forEach(item => {
                    item.flowNodeOrderDetailPriceDtoList.forEach((item1, index1) => {
                        item1.nodeLevel = index1 + 1;
                    })
                })

                // ???
                let reqData = {
                    flowOrderId: this.flowOrderId,
                    flowOrderNo: this.flowOrderNo,
                    baseOrderId: this.baseOrderId,
                    baseOrderNo: this.baseBusinessNo,
                    baseBusinessType: this.baseBusinessType,
                    flowOrderDetailList: this.goodsData,
                    flowNodeDto: this.supplierData,
                    pushDirection: this.pushDirection,
                    sourceErp: this.sourceErp
                }
                
                return reqData;
            },
            checkSubmit() {
                if(!this.submitFlag) {
                    return true;
                }

                let supplierflag = true;
                this.supplierData.forEach((item, index) => {
                    let mustKeys = ['traderId', 'traderContactName', 'traderContactAddress', 'receiverName', 'receiverAddress'];

                    if(this.baseBusinessType == 2) {
                        mustKeys = mustKeys.concat(['invoiceReceiverName'])
                    }

                    mustKeys.forEach(key => {
                        if(!item[key]) {
                            supplierflag = false;
                        }
                    })

                    item.nodeLevel = index + 1;
                })

                let goodsflag = true;
                this.goodsData.forEach(item => {
                    item.flowNodeOrderDetailPriceDtoList.forEach(item => {
                        if(!item.price || item.price == 0){
                            goodsflag = false;
                        }
                    })
                })

                if(!supplierflag || !goodsflag) {
                    this.supplierError = !supplierflag;
                    this.goodsError = !goodsflag;
                    return false;
                } else {
                    this.supplierError = false;
                    this.goodsError = false;
                    return true;
                }
            },
            submit() {
                if(!this.cansubmit) {
                    return false;
                }
                
                this.submitFlag = true;

                if(this.checkSubmit()) {
                    this.cansubmit = false;
                    let url = '/flowOrder/add.do';

                    if(this.flowOrderId) {
                        url = '/flowOrder/update.do'
                    }

                    axios.post(url, this.getReqData()).then(({data}) => {
                        if(data.code === 0) {
                            this.$message.success('操作成功');

                            setTimeout(() => {
                                //跳转详情页面
                                let url = '';
                                let id = this.flowOrderId || data.data;
                                if(this.baseBusinessType == 1) {
                                    url = '/flowOrder/buyOrderDetail.do?flowOrderId=' + id;
                                } else {
                                    url = '/flowOrder/saleOrderDetail.do?flowOrderId=' + id;
                                }

                                window.location.href = url;
                            })
                        } else {
                            this.$message.warn(data.message);
                            this.cansubmit = true;
                        }
                    })
                }
            }
            
        }
    })
}.call(this);