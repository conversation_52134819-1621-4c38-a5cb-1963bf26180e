package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeNeedPayCommand;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayEntityDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeNeedPayCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeNeedPayConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeNeedPayAdjustMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 金蝶  应付 余额调整单测试类
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeNeedPay {

    @Autowired
    private KingDeeNeedPayCommandConvertor needPayCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeNeedPayAdjustMapper kingDeeNeedPayMapper;

    @Autowired
    private KingDeeNeedPayConvertor dtoConvertor;

    @Test
    public void KingDeeNeedPay() {

        KingDeeNeedPayDto dto = new KingDeeNeedPayDto();
        //单据内码 0：表示新增 非0：云星空系统单据FID值，表示修改
        dto.setFid("0");
        //单据号
        dto.setFBillNo("AA1234");
        //单据日期
        dto.setFQzokDate("2022-11-08");
        //组织代码
        dto.setFQzokJg("101");
        //供应商
        dto.setFQzokBase("ven0006");

        List<KingDeeNeedPayEntityDto> eachEntityList = new ArrayList<>();
        KingDeeNeedPayEntityDto each1 = new KingDeeNeedPayEntityDto();
        //原始订单号
        each1.setFQzokYsddh("BD112233");
        //归属业务单号
        each1.setFQzokGsywdh("BD998877");
        //业务类型 应该结合实际场景传递
        each1.setFQzokYwlx("ADD");
        each1.setFQzokTzje(new BigDecimal(100));
        eachEntityList.add(each1);
        dto.setFEntity(eachEntityList);

        System.out.println("dto-->"+JSON.toJSON(dto));
        //转换为金蝶入参
        KingDeeNeedPayCommand command = needPayCommandConvertor.toCommand(dto);
        System.out.println("入参commad-->"+JSON.toJSON(command));
        //调用金蝶方法
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();

        System.out.println("金蝶返回-->"+JSON.toJSON(successEntities));

        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            dto.setFid(successEntity.getId());
            //金蝶业务数据入表
            kingDeeNeedPayMapper.insertSelective(dtoConvertor.toEntity(dto));
        System.out.println("finish-->");
        }

        //调用记录 - 1 失败
        //{"fid":0,"formId":"QZOK_BOS_YSTZD","fQzokDate":"2022-11-08","fQzokBase":"测试供应商","fEntity":[{"fQzokGsywdh":"BD998877","fQzokYwlx":"ADD","fQzokYsddh":"BD112233","fQzokTzje":100}],"fQzokJg":"101","fBillNo":"DANJU66666","kingDeeBizEnums":"saveNeedPay"}
        //入参commad-->{"f_QZOK_Base":{"fNumber":"测试供应商"},"f_QZOK_JG":{"fNumber":"101"},"fEntity":[{"f_QZOK_GSYWDH":"BD998877","f_QZOK_YSDDH":"BD112233","f_QZOK_YWLX":"ADD","f_QZOK_TZJE":"100"}],"fBillNo":"DANJU66666"}

        //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":"F_VPFN_KH","Message":"字段“客户”是必填项","DIndex":0},{"FieldName":"F_VPFN_Date","Message":"字段“日期”是必填项","DIndex":0},{"FieldName":"F_VPFN_JG","Message":"字段“机构”是必填项","DIndex":0}],"SuccessEntitys":[],"SuccessMessages":[],"MsgCode":11},"Id":"","Number":"","NeedReturnData":[{}]}}]
        //金蝶返回-->[]

        //调用记录 -2  失败 2022-11-10 6:45  根据示例调用，报错重复
        //{"fid":0,"formId":"QZOK_BOS_YSTZD","fQzokDate":"2022-11-08","fQzokBase":"ven0006","fEntity":[{"fQzokGsywdh":"BD998877","fQzokYwlx":"ADD","fQzokYsddh":"BD112233","fQzokTzje":100}],"fQzokJg":"101","fBillNo":"1234","kingDeeBizEnums":"saveNeedPay"}
        //入参commad-->{"f_QZOK_Base":{"fNumber":"ven0006"},"f_QZOK_JG":{"fNumber":"101"},"fEntity":[{"f_QZOK_GSYWDH":"BD998877","f_QZOK_YSDDH":"BD112233","f_QZOK_YWLX":"ADD","f_QZOK_TZJE":"100"}],"fBillNo":"1234"}

        //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":null,"Message":"编码【FBillNo:1234】重复","DIndex":0}],"SuccessEntitys":[],"SuccessMessages":[],"MsgCode":11}}}]
        //金蝶返回-->[]

        //调用记录 -3  失败 2022-11-10 6:45  修改billNo 莫名其妙的报错
        //{"fid":0,"formId":"QZOK_BOS_YSTZD","fQzokDate":"2022-11-08","fQzokBase":"ven0006","fEntity":[{"fQzokGsywdh":"BD998877","fQzokYwlx":"ADD","fQzokYsddh":"BD112233","fQzokTzje":100}],"fQzokJg":"101","fBillNo":"AA1234","kingDeeBizEnums":"saveNeedPay"}
        //入参commad-->{"f_QZOK_Base":{"fNumber":"ven0006"},"f_QZOK_JG":{"fNumber":"101"},"fEntity":[{"f_QZOK_GSYWDH":"BD998877","f_QZOK_YSDDH":"BD112233","f_QZOK_YWLX":"ADD","f_QZOK_TZJE":"100"}],"fBillNo":"AA1234"}

        //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":"F_VPFN_KH","Message":"字段“客户”是必填项","DIndex":0},{"FieldName":"F_VPFN_Date","Message":"字段“日期”是必填项","DIndex":0},{"FieldName":"F_VPFN_JG","Message":"字段“机构”是必填项","DIndex":0}],"SuccessEntitys":[],"SuccessMessages":[],"MsgCode":11},"Id":"","Number":"","NeedReturnData":[{}]}}]
        //金蝶返回-->[]

        //调用记录 - 4 失败 接口值调整 ，1-3接口地址传错 QZOK_BOS_YSTZD -> QZOK_BOS_YFTZD  11-10 15：33 用户许可数量可能不够，当初订许可的时候也是先订一些，不够再加的，说好的 失败 有图
        //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":null,"Message":"在运行时不允许加载标识为“QZOK_BOS_YFTZD”的业务对象。\r\nServer stack trace: \r\n   在 Kingdee.BOS.App.Core


        //调用记录 - 5 失败 回复调整了许可，再次调用报错
        //{"fID":"0","f_QZOK_Base":{"fNumber":"ven0006"},"f_QZOK_JG":{"fNumber":"101"},"fEntity":[{"f_QZOK_GSYWDH":"BD998877","f_QZOK_YSDDH":"BD112233","f_QZOK_YWLX":"ADD","f_QZOK_TZJE":100}],"fBillNo":"AA1234","f_QZOK_Date":"2022-11-08"}

        //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":null,"Message":"在运行时不允许加载标识为“QZOK_BOS_YFTZD”的业务对象。\r\nServer stack trace: \r\n   在 Kingdee.BOS.App.Core

        //调用记录 -6 成功 再次尝试
        //{"fID":"0","f_QZOK_Base":{"fNumber":"ven0006"},"f_QZOK_JG":{"fNumber":"101"},"fEntity":[{"f_QZOK_GSYWDH":"BD998877","f_QZOK_YSDDH":"BD112233","f_QZOK_YWLX":"ADD","f_QZOK_TZJE":100}],"fBillNo":"AA1234","f_QZOK_Date":"2022-11-08"}

        //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":100024,"Number":"AA1234","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":100024,"Number":"AA1234","NeedReturnData":[{}]}}]
        //金蝶返回-->[{"number":"AA1234","dIndex":0,"id":"100024"}]
    }
}
