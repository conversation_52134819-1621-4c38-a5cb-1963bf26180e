package com.vedeng.erp.statistic.mapper;

import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNum;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNumExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNumKey;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticAedNumWithBLOBs;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeMonth;
import com.vedeng.erp.common.broadcast.param.UserOrgInfo;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BroadcastStatisticAedNumMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    long countByExample(BroadcastStatisticAedNumExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int deleteByExample(BroadcastStatisticAedNumExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(BroadcastStatisticAedNumKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int insert(BroadcastStatisticAedNumWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int insertSelective(BroadcastStatisticAedNumWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    List<BroadcastStatisticAedNumWithBLOBs> selectByExampleWithBLOBs(BroadcastStatisticAedNumExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    List<BroadcastStatisticAedNum> selectByExample(BroadcastStatisticAedNumExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    BroadcastStatisticAedNumWithBLOBs selectByPrimaryKey(BroadcastStatisticAedNumKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") BroadcastStatisticAedNumWithBLOBs record, @Param("example") BroadcastStatisticAedNumExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") BroadcastStatisticAedNumWithBLOBs record, @Param("example") BroadcastStatisticAedNumExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") BroadcastStatisticAedNum record, @Param("example") BroadcastStatisticAedNumExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(BroadcastStatisticAedNumWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(BroadcastStatisticAedNumWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_AED_NUM
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(BroadcastStatisticAedNum record);
    
    /**
     * 批量插入月AED出库量
     * @param broadcastStatisticAedNumList
     */
    void batchInsert(@Param("broadcastStatisticAedNumList") List<BroadcastStatisticAedNumWithBLOBs> broadcastStatisticAedNumList);

	/**
	 * 根据用户ID获取用户组织信息
	 * @param userId
	 * @return
	 */
	UserOrgInfo getUserOrgIdInfo(@Param("userId") Integer userId);
}