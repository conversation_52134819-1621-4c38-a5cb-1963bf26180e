package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeStorageOutCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeSupplierCommand;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 其他入出库  command和dto转化
 * @description:
 * @author: <PERSON>.yang
 * @date: 2022/11/10
 */
@Mapper(componentModel = "spring")
public interface KingDeeStorageOutCommandConvertor extends BaseCommandMapStruct<KingDeeStorageOutCommand, KingDeeStorageOutDto> {


    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtId")
    @Mapping(target = "FStockDirect", source = "FStockDirect")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "FCustId.FNumber", source = "FCustId")
    @Mapping(target = "FDeptId.FNumber", source = "FDeptId")
    @Override
    KingDeeStorageOutCommand toCommand(KingDeeStorageOutDto dto);

    @Mapping(target = "FQty", source = "FQty")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    @Mapping(target = "f_QZOK_SQLX", source = "FQzokSqlx")
    @Mapping(target = "f_QZOK_SFZF", source = "FQzokSfzf")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "FMaterialId.FNumber", source = "FMaterialId")
    @Mapping(target = "FStockId.FNumber", source = "FStockId")
    KingDeeStorageOutCommand.FEntity toCommand(KingDeeStorageOutDetailDto dto);
}
