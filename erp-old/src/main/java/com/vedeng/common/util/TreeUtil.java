package com.vedeng.common.util;


import com.vedeng.common.model.TreeNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:  树形结构封装工具类
 * @Author:       allenyll
 * @Date:         2021/4/14 下午5:14
 * @Version:      1.0
 */
public class TreeUtil {

    /**
     * 两层循环实现建树
     *
     * @param nodes 传入的树节点列表
     * @return
     */
    public static <T extends TreeNode> List<T> build(List<T> nodes, Object root){
        if(nodes == null){
            return null;
        }
        List<T> trees = new ArrayList<T>();
        if (nodes.size() == 1) {
            trees.add(nodes.get(0));
        } else {
            for(T node:nodes){
                // 如果parentId是根结点id，该节点是其他节点的根结点
                if(root.equals(node.getParentId())){
                    trees.add(node);
                }

                // 获取该节点的所有子节点
                for(T nodeChild:nodes){
                    if(nodeChild.getParentId().equals(node.getId())){
                        if(node.getChild() == null){
                            node.setChild(new ArrayList<>());
                        }
                        node.add(nodeChild);
                    }
                }
            }
        }
        return trees;
    }

}
