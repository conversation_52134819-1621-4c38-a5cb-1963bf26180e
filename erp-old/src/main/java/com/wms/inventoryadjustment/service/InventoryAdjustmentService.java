package com.wms.inventoryadjustment.service;

import com.vedeng.common.page.Page;
import com.vedeng.common.service.BaseService;
import com.wms.inventoryadjustment.model.dto.InventoryAdjustmentSearchDto;
import com.wms.inventoryadjustment.model.vo.InventoryAdjustmentDetailVo;
import com.wms.inventoryadjustment.model.vo.InventoryAdjustmentVo;

import java.util.List;
import java.util.Map;

/**
 * 库存调整服务层
 *
 * <AUTHOR>
 * @date 2020/7/29 13:15:28
 */
public interface InventoryAdjustmentService extends BaseService {
    /**
     * @return
     * @describe 获取库存整单信息
     * <AUTHOR>
     * @date 2020/7/29 13:16:15
     */
    Map<String, Object> getInventoryAdjustmentVos(InventoryAdjustmentSearchDto inventoryAdjustmentSearchDto, Page page);

    /**
     * 通过ID获取库存调整单信息
     *
     * @param inventoryAdjustmentId
     * @return
     * <AUTHOR>
     * @date 2020/7/29 13:40:28
     */
    InventoryAdjustmentVo getInventoryAdjustmentById(Integer inventoryAdjustmentId);

    /**
     * @return
     * @describe 获取库存调整单详情信息
     * <AUTHOR>
     * @date 2020/7/29 13:17:15
     */
    List<InventoryAdjustmentDetailVo> getInventoryAdjustmentDetailsById(Integer inventoryAdjustmentId);
}
