<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">
<!-- 引入脚本 -->
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/iconfont_business.js?rnd=${resourceVersionKey}"></script>
<style scoped>
    .el-divider--horizontal {
        margin: 8px 0;
        background: 0 0;
        border-top: 3px dashed #e8eaec;
    }

    .icon_business {
        width: 1em;
        height: 1em;
        vertical-align: -0.15em;
        fill: currentColor;
        overflow: hidden;
    }
</style>
<div id="app" style="display: none;">
    <el-form :rules="rules" ref="businessChanceDto" :model="businessChanceDto">
        <el-form-item label="商机等级:" style="width: 250px;float: left">
            <el-row :gutter="10">
                <el-col :span="0.8">
                    <span v-if="systemBusinessLevel==939"
                          style="color: #00a0e9;font-size:35px">S</span>
                    <span v-if="systemBusinessLevel==940"
                          style="color: #00a0e9;font-size:35px">A</span>
                    <span v-if="systemBusinessLevel==941"
                          style="color: #00a0e9;font-size:35px">B</span>
                    <span v-if="systemBusinessLevel==942"
                          style="color: #00a0e9;font-size:35px">C</span>
                </el-col>
                <el-select v-model="businessChanceDto.bussinessLevel" style="width: 100px" size="small" clearable
                           placeholder="请选择">
                    <el-option
                            v-for="item in bussinessLevels"
                            :key="item.sysOptionDefinitionId"
                            :label="item.title"
                            :value="item.sysOptionDefinitionId">
                    </el-option>
                </el-select>
                <div style="display: contents">
                    <el-tooltip content="商机等级人工校正值，可手动维护" placement="right" effect="light">
                        <i class="el-icon-warning-outline" style="color: red"></i>
                    </el-tooltip>
                </div>

            </el-row>
        </el-form-item>
        <el-form-item label="成单几率:" style="width:300px;float: left">
            <el-row :gutter="10">
                <el-col :span="0.8">
                    <span v-if="systemOrderRate==952" style="color: #00a0e9;font-size:35px">20%</span>
                    <span v-if="systemOrderRate==953" style="color: #00a0e9;font-size:35px">40%</span>
                    <span v-if="systemOrderRate==954" style="color: #00a0e9;font-size:35px">60%</span>
                    <span v-if="systemOrderRate==955" style="color: #00a0e9;font-size:35px">80%</span>
                    <span v-if="systemOrderRate==956"
                          style="color: #00a0e9;font-size:35px">100%</span>
                </el-col>
                <el-select v-model="businessChanceDto.orderRate" style="width: 100px" size="small" placeholder="请选择"
                           clearable>
                    <el-option
                            v-for="item in orderRates"
                            :key="item.sysOptionDefinitionId"
                            :label="item.title"
                            :value="item.sysOptionDefinitionId">
                    </el-option>
                </el-select>
                <div style="display: contents">
                    <el-tooltip content="成单几率人工校正值，可手动维护" placement="right" effect="light">
                        <i class="el-icon-warning-outline" style="color: red"></i>
                    </el-tooltip>
                </div>
            </el-row>
        </el-form-item>
        <el-form-item label="完整度:" style="display: inline-block; width: 200px">
            <el-row>
                <el-col :span="0.8">
                    <span style="color: #00a0e9;font-size:35px">{{completion}}%</span>
                </el-col>
            </el-row>
        </el-form-item>

        <el-form-item label="商机标签:" label-width="100px" prop="checkedCities">
            <span slot="label">
               <el-tooltip content="最多同时选择三个标签" placement="top">
                    <i class="el-icon-question"></i>
                </el-tooltip>商机标签:
            </span>

            <el-checkbox-group v-model="businessChanceDto.checkedCities" :max="3" @change="bindNum">
                <el-checkbox v-for="(item,inedx) in items" :label="item.id" :key="inedx">
                    <el-tag :style="{color: item.cssClass,borderColor:item.cssClass}" effect="plain">
                        {{item.name}}
                    </el-tag>
                </el-checkbox>
            </el-checkbox-group>
        </el-form-item>
        <el-divider content-position="left"
                    style="margin: 8px 0;background: 0 0;border-top: 3px dashed #e8eaec"></el-divider>
        <i class="el-icon-s-custom-outline"></i>
        <svg class="icon_business" aria-hidden="true" style="font-size: 45px;color: #999999">
            <use xlink:href="#icon-kehu"></use>
        </svg>
        <span style="font-size: 25px;color: #999999">客户信息</span>
        <br>
        <br>
        <el-form-item label="客户名称:" label-width="150px" prop="traderName">
            <span slot="label" style="color: #00a0e9;font-weight: bold">
                客户名称:
            </span>

            <el-select
                    v-model="businessChanceDto.traderName"
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入关键词"
                    :remote-method="remoteTraderMethod"
                    style="width: 300px;"
                    @blur="selectBlur($event)"
                    @clear="traderNameClear"
                    :loading="loading">

                <el-option
                        v-for="item in queryTrader"
                        :key="item.traderId"
                        :label="item.traderName"
                        :value="item.traderId"
                        @click.native="traderNameSelect(item)"

                        :disabled="!item.belong">
                    <span style="float: left">{{ item.traderName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.saleName }}</span>
                </el-option>
            </el-select>

            <el-button type="text" style="margin-left: 20px"  @click="addTrader">
                <span>新增客户</span>
            </el-button>

            <%--<el-link href="https://element.eleme.io" :underline="false" type="primary"--%>
            <%--         target="_blank">新增客户--%>
            <%--</el-link>--%>
        </el-form-item>
        <el-form-item label-width="150px">
            <el-checkbox v-model="traderCheck" @change="traderCheckBind">暂无客户名称</el-checkbox>
        </el-form-item>

        <el-row>
            <el-col :span="6">
                <el-form-item label="客户类型:" label-width="150px">
                    <span></span><span style="color: #606266">{{customerTypeStr}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="5">


                <el-form-item label="地区:">
                    <span v-if="businessChanceDto.traderId!=null&&businessChanceDto.traderId!=0" style="color: #606266">{{businessChanceDto.checkTraderArea}}</span>
                    <el-cascader v-if="businessChanceDto.traderId==null||businessChanceDto.traderId==0"
                                 ref="cascader"
                                 :options="areaOptions"
                                 :props="{ checkStrictly: true }"
                                 style="width: 300px;"
                                 v-model="areaSelectedOptions"
                                 @change="getCheckedAddressNodes"
                                 @clear="addressClear"
                                 clearable></el-cascader>
                </el-form-item>
            </el-col>
        </el-row>


        <div v-if="businessChanceDto.traderId!=null&&businessChanceDto.traderId!==''&&businessChanceDto.traderId!=0">
            <el-form-item label="客户联系人:" label-width="150px" prop="traderContactName">
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    客户联系人:
                </span>

                <el-select
                        v-model="businessChanceDto.traderContactNameShow"
                        filterable
                        clearable
                        reserve-keyword
                        placeholder="请选择联系人"
                        style="width: 300px;"
                        @clear="traderContactClear"
                >
                    <el-option
                            v-for="item in traderConcatDatas"
                            :key="item.traderContactId"
                            :label="item.name"
                            :value="item.traderContactId"
                            @click.native="traderConcatSelect(item)"
                    >
                        <span style="float: left">{{ item.name + '/' + item.mobile }}</span>
                    </el-option>

                </el-select>
                <span class="title-click  bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                      id="concat" onclick="addTraderContract()"
                      style="margin-left:24px;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                      :layerParams="'{&quot;width&quot;:&quot;50%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;添加联系人&quot;,&quot;link&quot;:&quot;/orderstream/saleorder/addContact.do?indexId=3&amp;customerNature='+this.customerNature +'&amp;traderId='+businessChanceDto.traderId+'&quot;}'"><span
                        v-if="businessChanceDto.traderId!=null">添加联系人</span></span>

            </el-form-item>

        </div>
        <div v-if="businessChanceDto.traderId==null||businessChanceDto.traderId==''||businessChanceDto.traderId==0">
            <el-row>

                <el-col :span="4">
                    <el-form-item label="客户联系人:" label-width="150px"
                                  prop="traderContactName">
                        <span slot="label" style="color: #00a0e9;font-weight: bold">
                            客户联系人:
                        </span>

                        <el-input
                                placeholder="请输入姓名"
                                v-model="businessChanceDto.traderContactName"
                                style="width:200px"
                                @input="buildCommunicationData"
                                clearable>
                        </el-input>

                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item style="float: left" label="手机:" label-width="150px" prop="mobile">
                        <span slot="label" style="color: #00a0e9;font-weight: bold">
                            手机:
                        </span>
                        <el-input
                                placeholder="请输入手机号"
                                v-model="businessChanceDto.mobile"
                                style="width:150px"
                                @input="buildCommunicationData"
                                clearable>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label="电话:" label-width="150px" prop="telephone">
                        <el-input
                                placeholder="请输入电话"
                                style="width:150px"
                                v-model="businessChanceDto.telephone"
                                @input="buildCommunicationData"
                                clearable>
                        </el-input>
                    </el-form-item>
                </el-col>

            </el-row>

        </div>
        <el-row>

            <el-col :span="1.5">
                <el-form-item label="联系人情况:" label-width="150px">
                    <el-radio v-model="businessChanceDto.isPolicymaker" :label="1">采购关键人</el-radio>
                </el-form-item>
            </el-col>

            <el-col :span="1.5">
                <el-form-item label-width="10px">
                    <el-radio v-model="businessChanceDto.isPolicymaker" :label="2">非采购关键人</el-radio>
                </el-form-item>
            </el-col>

            <el-col :span="1">
                <el-form-item label-width="10px" prop="">
                    <el-radio v-model="businessChanceDto.isPolicymaker" :label="3">其他</el-radio>
                </el-form-item>
            </el-col>
            <el-col :span="4">
                <el-form-item label-width="10px" prop="other">
                    <el-input
                            placeholder="选择其他时候必填"
                            style="width:200px"
                            v-model="businessChanceDto.other"
                            maxlength="15"
                    >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-form-item label-width="150px" label="终端名称:">
            <el-input
                    v-model="orderTerminalDto.terminalName"
                    placeholder="搜索选择终端"
                    maxlength="30"
                    style="width: 300px"
                    disabled="true">
            </el-input>
            <el-button type="primary" @click="searchTerminal()" v-if="!showRepeatSearch">搜索</el-button>
            <el-button type="primary" @click="repeatSearch()" v-if="showRepeatSearch">重新搜索</el-button>
        </el-form-item>

        <el-divider content-position="left"
                    style="margin: 8px 0;background: 0 0;border-top: 3px dashed #e8eaec"></el-divider>
        <i class="el-icon-s-custom-outline"></i>
        <svg class="icon_business" aria-hidden="true" style="font-size: 45px;color: #999999">
            <use xlink:href="#icon-24gl-cartFull6"></use>
        </svg>
        <span style="font-size: 25px;color: #999999">产品信息</span> <%--{{businessChanceDto.traderId}}--%>

        <div v-show="businessChanceDto.traderId!=null&&businessChanceDto.traderId!==''&&businessChanceDto.traderId!=0">
            <span class="title-click   bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                  style="float: right;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                  :layerParams="'{&quot;width&quot;:&quot;80%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;添加产品&quot;,&quot;link&quot;:&quot;/order/quote/addQuoteGoods.do?optType=1&amp;traderId='+businessChanceDto.traderId+'&quoteorderId=&quot;}'">添加</span>

        </div>
        <div v-show="businessChanceDto.traderId==null||businessChanceDto.traderId==''||businessChanceDto.traderId==0">
              <span class="title-click   bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                    style="float: right;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                    layerParams="{&quot;width&quot;:&quot;80%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;添加产品&quot;,&quot;link&quot;:&quot;/order/quote/addQuoteGoods.do?optType=1&amp;traderId=1&quoteorderId=&quot;}">添加</span>

        </div>
        <br>
        <div v-if="(businessChanceDto.bussinessChanceId!=null||businessChanceDto.businessLeadsId!=null)&&businessChanceDto.type!=null&&businessChanceDto.type!=392">
            <el-form-item label="产品备注(总机):" label-width="120px">
                <span>{{businessChanceDto.productComments}}</span>
            </el-form-item>
            <el-form-item label="询价产品:" label-width="120px">
                <span>{{businessChanceDto.content}}</span>
            </el-form-item>
        </div>
        <el-form-item label="产品备注(销售):" prop="productCommentsSale">
             <span slot="label" style="color: #00a0e9;font-weight: bold">
                            产品备注(销售):
                        </span>
            <el-input
                    type="textarea"
                    style="width: 600px"
                    maxlength="500"
                    rows="10"
                    v-model="businessChanceDto.productCommentsSale"
                    placeholder="请输入产品信息"
            >
            </el-input>
        </el-form-item>

        <template v-if="businessChanceDto.businessChanceGoodsDtos.length!=0">
            <el-table
                    :data="businessChanceDto.businessChanceGoodsDtos"
                    :header-cell-style="{textAlign: 'center'}"
                    border
                    style="width: 100%">
                <el-table-column
                        label="产品名称"
                <%--width="180"--%>
                        align="center"
                >
                    <template slot-scope="scope">
                        <span style="margin-left: 10px">{{ scope.row.goodsName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="订货号"
                        width="100"
                        align="center"
                >
                    <template slot-scope="scope">
                        <span size="medium">{{ scope.row.sku }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="品牌"
                        width="150"
                        align="center"
                >
                    <template slot-scope="scope">
                        <span style="margin-left: 10px">{{ scope.row.brandName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="型号"
                        width="150"
                        align="center"
                >
                    <template slot-scope="scope">
                        <span style="margin-left: 10px">{{ scope.row.model }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="单位"
                        align="center"
                        width="80">
                    <template slot-scope="scope">
                        <span style="margin-left: 10px">{{ scope.row.unitName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="报价"
                        align="center"
                        width="150"
                >
                    <template slot-scope="scope">
                        <el-input
                                oninput="value=value.replace(/[^0-9.]/g,'')"
                                v-model="scope.row.price"
                        <%--@input="calcTotal(scope.row)"--%>
                        >
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column
                        label="数量"
                        align="center"
                        width="100">
                    <template slot-scope="scope">
                        <el-input
                                oninput="value=value.replace(/[^\d]|^[0]/g, '')"
                                v-model="scope.row.num"
                        <%--@input="calcTotal(scope.row)"--%>
                        >
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column
                        label="含安调"
                        align="center"
                        width="160">
                    <template slot-scope="scope">
                        <el-radio v-model="scope.row.haveInstallation" :label="true">是</el-radio>
                        <el-radio v-model="scope.row.haveInstallation" :label="false">否</el-radio>
                    </template>
                </el-table-column>
                <el-table-column
                        label="货期（天）"
                        align="center"
                        width="100">
                    <template slot-scope="scope">
                        <el-input
                                v-model="scope.row.deliveryCycle"
                        >
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column
                        label="直发"
                        align="center"
                        width="160">
                    <template slot-scope="scope">
                        <el-radio v-model="scope.row.deliveryDirect" :label="true">是</el-radio>
                        <el-radio v-model="scope.row.deliveryDirect" :label="false">否</el-radio>
                    </template>
                </el-table-column>
                <el-table-column
                        label="总额"
                        align="center"
                        width="150">
                    <template slot-scope="scope">
                        <%--<span >{{scope.row.total}}</span><br>--%>
                        <span>{{calcTotal(scope.row)}}</span><br>
                    </template>
                </el-table-column>
                <%--<el-table-column
                        label="核价参考"
                        width="280">
                    <template slot-scope="scope">
                        核价参考价:<span>{{scope.row.channelPrice}}</span><br>
                        参考价格:<span>{{scope.row.channelPrice}}</span><br>
                        参考货期:<span>{{scope.row.referenceDeliveryCycle}}</span><br>
                        结算价:<span>{{scope.row.settlementPrice}}</span>
                    </template>
                </el-table-column>--%>
                <el-table-column label="操作" width="100" fixed="right">
                    <template slot-scope="scope">
                        <el-button
                                size="mini"
                                type="danger"
                                @click="handleDelete(scope.$index, scope.row)">删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <span style="color: red;float: right">总{{tableNum}}件,总金额{{tableAmount}}元</span>
        </template>
        <template v-else>
        </template>
        <br>
        <br>
        <div v-if="businessChanceDto.bussinessChanceId==null">
            <el-divider content-position="left"
                        style="margin: 8px 0;background: 0 0;border-top: 3px dashed #e8eaec"></el-divider>
            <i class="el-icon-s-custom-outline"></i>
            <svg class="icon_business" aria-hidden="true" style="font-size: 45px;color: #999999">
                <use xlink:href="#icon-kefu"></use>
            </svg>
            <span style="font-size: 25px;color: #999999">沟通记录</span>

            <div v-show="businessChanceDto.traderId!=null&&businessChanceDto.traderId!==''&&businessChanceDto.traderId!=0">
                <el-form-item label="联系人:" label-width="150px" prop="communicateRecordDto.traderContactNameView">
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    联系人:
                </span>

                    <el-select
                            v-model="businessChanceDto.communicateRecordDto.traderContactNameView"
                            filterable
                            clearable
                            reserve-keyword
                            placeholder="请选择联系人"
                            style="width: 300px;"
                            @clear="traderContactClearComm"
                    >
                        <el-option
                                v-for="item in traderConcatDatas"
                                :key="item.traderContactId"
                                :label="item.name"
                                :value="item.traderContactId"
                                @click.native="traderConcatSelectComm(item)"
                        >
                            <span style="float: left">{{ item.name + ' ' + item.mobile + ' ' + item.telephone}}</span>
                        </el-option>

                    </el-select>
                    <span class="title-click  bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                          style="margin-left:24px;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                          :layerParams="'{&quot;width&quot;:&quot;50%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;添加联系人&quot;,&quot;link&quot;:&quot;/orderstream/saleorder/addContact.do?indexId=3&amp;traderId='+businessChanceDto.traderId+'&quot;}'"><span
                            v-show="businessChanceDto.traderId!=null"
                            onclick="changeNewConcatFlag(1)">添加联系人</span></span>

                </el-form-item>

            </div>

            <div v-show="businessChanceDto.traderId==null||businessChanceDto.traderId==''||businessChanceDto.traderId==0">
                <el-form-item label="联系人:" label-width="150px" prop="communicateRecordDto.traderContactNameView">
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    联系人:
                </span>

                    <el-select
                            v-model="businessChanceDto.communicateRecordDto.traderContactNameView"
                            filterable
                            clearable
                            reserve-keyword
                            placeholder="请选择联系人"
                            style="width: 300px;"
                            @clear="traderContactClearComm"
                    >
                        <el-option
                                v-for="item in bindCommunicationData"
                                :key="item.id"
                                :label="item.traderContactName"
                                :value="item.type"
                                @click.native="traderConcatSelectCommNoTraderId(item)"
                        >
                            <span style="float: left">{{ item.traderContactName + ' ' + item.type }}</span>
                        </el-option>

                    </el-select>

                </el-form-item>

            </div>

            <el-form-item label="沟通时间:" label-width="150px" prop="communicateRecordDto.time">
                <el-date-picker
                        v-model="businessChanceDto.communicateRecordDto.time"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="沟通开始时间"
                        end-placeholder="沟通结束时间">

                </el-date-picker>
            </el-form-item>

            <el-form-item label="沟通内容:" label-width="150px" prop="communicateRecordDto.contentSuffix">
                <el-input
                        v-model="businessChanceDto.communicateRecordDto.contentSuffix"
                        type="textarea"
                        placeholder="沟通内容最多输入200个汉字"
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
                <br>
                <div style="width: 500px">
                    <el-tag
                            v-for="item in sysTagUserCheck"
                            closable
                            :key="item.tagId"
                            style="margin-right: 8px;cursor : pointer"
                    <%--@click.native="sysTagClick(item)"--%>
                            @close="handlesysTagUserClose(item)"
                            effect="light">
                        {{ item.tagName }}
                    </el-tag>
                </div>
                <div style="width: 500px">
                    <el-tag
                            v-for="item in businessChanceDto.communicateRecordDto.userTag"
                            :key="item"
                            closable
                            type="success"
                            style="margin-right: 8px;cursor : pointer"
                            @close="handleUserTagClose(item)"
                            effect="light">
                        {{ item }}
                    </el-tag>
                </div>
                <span @click="showTag=!showTag"><i class="el-icon-edit-outline" style="color: #00a0e9">标签</i></span>
                <div v-if="showTag">
                    <div style="width: 500px">
                        <el-tag
                                v-for="item in sysTag"
                                :key="item.tagId"
                                style="margin-right: 8px;cursor : pointer"
                                @click.native="sysTagClick(item)"
                                effect="light">
                            {{ item.tagName }}
                        </el-tag>
                        <br>
                        <el-input
                                class="input-new-tag"
                                v-if="inputVisible_tag"
                                v-model="inputTagValue"
                                ref="saveTagInput"
                                size="small"
                                @keyup.enter.native="handleInputConfirm"
                                @blur="handleInputConfirm"
                        >
                        </el-input>
                        <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 如果标签中没有您所需要的，请在此处自行填写
                        </el-button>
                    </div>
                </div>


            </el-form-item>

            <el-form-item label="下次沟通时间:" label-width="150px" prop="communicateRecordDto.nextContactDate">
                <el-date-picker
                        v-model="businessChanceDto.communicateRecordDto.nextContactDate"
                        type="date"
                        :disabled="businessChanceDto.communicateRecordDto.noneNextDate"
                        placeholder="选择日期时间">
                </el-date-picker>
                <el-checkbox v-model="businessChanceDto.communicateRecordDto.noneNextDate"
                             @change="communicateRecordNextBind">暂无下次沟通记录
                </el-checkbox>
            </el-form-item>

            <el-form-item label="下次沟通内容:" label-width="150px" prop="">
                <el-input
                        v-model="businessChanceDto.communicateRecordDto.nextContactContent"
                        type="textarea"
                        placeholder="沟通内容最多输入200个汉字"
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
            </el-form-item>

            <el-form-item label="备注:" label-width="150px" prop="">
                <el-input
                        v-model="businessChanceDto.communicateRecordDto.comments"
                        type="textarea"
                        placeholder="备注最多输入200个汉字"
                        style="width: 500px"
                        maxlength="200"
                        rows="3"
                >
                </el-input>
            </el-form-item>
        </div>
        <el-divider content-position="left"
                    style="margin: 8px 0;background: 0 0;border-top: 3px dashed #e8eaec"></el-divider>
        <i class="el-icon-s-custom-outline"></i>
        <svg class="icon_business" aria-hidden="true" style="font-size: 45px;color: #999999">
            <use xlink:href="#icon-qiandai"></use>
        </svg>
        <span style="font-size: 25px;color: #999999">商机信息</span>

        <br>
        <el-form-item label="商机阶段:" label-width="150px" prop="">
            <el-radio-group v-model="businessChanceDto.bussinessStage">
                <el-radio v-for="(item,inedx) in bussinessStageList" :label="item.sysOptionDefinitionId" :key="inedx">
                    {{item.title}}
                </el-radio>
            </el-radio-group>
        </el-form-item>

        <el-form-item label="渠道名称:" label-width="150px" prop="">
            <el-radio-group v-model="businessChanceDto.communication">
                <el-radio v-for="(item,inedx) in communications" :label="item.sysOptionDefinitionId" :key="inedx">
                    {{item.title}}
                </el-radio>
            </el-radio-group>
        </el-form-item>

        <el-form-item label="询价类型:" label-width="150px" prop="">
            <el-radio-group v-model="businessChanceDto.enquiryType">
                <el-radio v-for="(item,inedx) in enquiryTypeList" :label="item.sysOptionDefinitionId" :key="inedx">
                    {{item.title}}
                </el-radio>
            </el-radio-group>
        </el-form-item>

        <el-form-item label="采购方式:" label-width="150px" prop="tenderTime">
            <el-radio v-model="businessChanceDto.purchasingType" :label="405">直接采购</el-radio>
            <el-radio v-model="businessChanceDto.purchasingType" :label="406">招投标采购</el-radio>
            -招标时间:
            <el-date-picker
                    v-model="businessChanceDto.tenderTime"
                    type="date"
                    placeholder="选择日期时间"
            >
            </el-date-picker>
        </el-form-item>

        <el-form-item label="采购时间:" label-width="150px">
            <el-radio-group v-model="businessChanceDto.purchasingTime">
                <el-radio v-for="(item,inedx) in purchasingTimeList" :label="item.sysOptionDefinitionId" :key="inedx">
                    {{item.title}}
                </el-radio>
            </el-radio-group>
        </el-form-item>

        <el-form-item label="预计金额:" label-width="150px" prop="amount">
            <el-input
                    style="width: 200px"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                    v-model="businessChanceDto.amount"
            >
            </el-input>
        </el-form-item>

        <el-form-item label="预计成单时间:" label-width="150px" prop="orderTime">
            <el-date-picker
                    v-model="businessChanceDto.orderTime"
                    type="date"
                    :picker-options="pickerOptions"
                    placeholder="选择日期"
                    format="yyyy 年 MM 月 dd 日"
                    value-format="timestamp">
            </el-date-picker>
        </el-form-item>


        <el-form-item style="text-align: center">
            <el-button @click="closeThis">取消</el-button>
            <el-button type="primary" @click="onSubmit('businessChanceDto')" :disabled="formDisabled">确定</el-button>
        </el-form-item>

    </el-form>

</div>

<script src="${pageContext.request.contextPath}/static/api/trader/businessChance.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    const viewInfo = {
        creatername: '${creatername}',
        creater: '${creater}',
        businessChanceId: '${businessChanceId}',
        businessLeadsId: '${businessLeadsId}',
        traderId:'${traderId}',
        isAiAssistant:'${isAiAssistant}',
        aiCommunicateRecordId:'${aiCommunicateRecordId}',
    }
    var flag = true;

   var vueObject = new Vue({
        el: '#app',
        created() {
            this.initData();
        },
        data() {
            var checkOther = (rule, value, callback) => {
                if (this.businessChanceDto.isPolicymaker == 3) {
                    if (!this.businessChanceDto.other) {
                        return callback(new Error('选择其他文本不可为空'));
                    } else {
                        if (this.businessChanceDto.other.length > 15) {
                            return callback(new Error('最多输入15个字'));
                        }
                    }

                }
                callback();
            };
            var checkTenderTime = (rule, value, callback) => {
                // debugger
                if (this.businessChanceDto.purchasingType == "406") {
                    if (!this.businessChanceDto.tenderTime) {
                        return callback(new Error('选择招投标采购招标时间不可为空'));
                    }
                }
                callback();
            };
            var checkOrderTime = (rule, value, callback) => {
                // debugger
                if (!this.businessChanceDto.orderTime || this.businessChanceDto.orderTime === 0) {
                    return callback(new Error('请输入预计成单时间'));
                }
                callback();
            };

            var dateTime = new Date().getTime();
            var dateTimeEnd = new Date(dateTime + 1000 * 60 * 2).getTime();

            return {
                pickerOptions: {
                    disabledDate(time) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24);
                        return time.getTime() < date;
                    },
                    shortcuts: [{
                        text: '今天',
                        onClick(picker) {
                            picker.$emit('pick', new Date());
                        }
                    }]
                },
                // 表单重复提交验证
                formDisabled:false,
                // 地址名暂存区
                province: null,
                city: null,
                county: null,
                // 地区三级联动
                areaOptions: [],
                // 地区选中数据
                areaSelectedOptions: [],
                // 显示隐藏
                showTag: false,
                // 是否输入框
                inputVisible_tag: false,
                // 输入的值暂存区
                inputTagValue: '',
                // 系统沟通标签
                sysTag: [],
                // 选中
                sysTagUserCheck: [],
                // 沟通记录的新增联系人的标志
                concatFlag: 0,
                tableAmount: 12,
                tableNum: 12,
                traderCheck: false,
                customerTypeStr: '-',
                customerNature: 0,
                rules: {
                    amount: [
                        {required: true, message: '请输入预计金额', trigger: 'blur'}
                    ],
                    orderTime: [
                        {
                            validator: checkOrderTime,
                            type: 'date',
                            required: true,
                            message: '请输入预计成单时间',
                            trigger: 'blur'
                        }
                    ],
                    tenderTime: [
                        {validator: checkTenderTime, trigger: ['change', 'blur']}
                    ],
                    productCommentsSale: [
                        {required: true, message: '产品备注不可为空', trigger: 'blur'}
                    ],
                    traderName: [
                        {required: true, message: '请输入客户名称', trigger: 'change'},
                        {max: 50, message: '最大支持输入50个字', trigger: ['blur','change']}
                    ],
                    'communicateRecordDto.nextContactDate': [
                        {type: 'date', required: true, message: '请输入下次沟通时间', trigger: 'change'}
                    ],
                    'communicateRecordDto.contentSuffix': [
                        {required: true, message: '请输入沟通内容', trigger: 'change'}
                    ],
                    'communicateRecordDto.time': [
                        {type: 'array', required: true, message: '请输入沟通时间', trigger: 'change'}
                    ],
                    /*checkedCities: [
                        {type: 'array', required: true, message: '请选择商机标签', trigger: 'change'}
                    ],*/
                    traderContactName: [
                        {required: true, message: '请完善联系人信息', trigger: ['change', 'blur']}
                        // {max: 10, message: '最大支持输入10个字', trigger: ['blur','change']}
                    ],
                    other: [
                        {validator: checkOther, trigger: 'blur'}
                    ],

                    'communicateRecordDto.traderContactNameView': [
                        {required: true, message: '请选择联系人', trigger: 'blur'},
                    ],
                    'communicateRecordDto.contactMob': [
                        {required: true, message: '请输入手机号', trigger: 'blur'},
                        {
                            pattern: /^1([38][0-9]|4[014-9]|[59][0-35-9]|6[2567]|7[0-8])\d{8}$/,
                            message: '请输入合法手机号',
                            trigger: 'blur'
                        }
                    ],
                    mobile: [
                        {required: true, message: '请输入手机号', trigger: 'blur'},
                        {
                            pattern: /^1([38][0-9]|4[014-9]|[59][0-35-9]|6[2567]|7[0-8])\d{8}$/,
                            message: '请输入合法手机号',
                            trigger: 'blur'
                        }
                    ],
                    telephone: [
                        {
                            pattern:  /^(0[1,2]{1}\d{1}-?\d{8})|(0[3-9]{1}\d{2}-?\d{7})/,
                            message: '请输入合法电话号码',
                            trigger: ['blur','change']
                        }
                    ]

                },
                bindCommunicationData: [],
                contact: '',
                completion: 0,
                systemOrderRate: null,
                systemBusinessLevel: null,
                bussinessStageList: [],
                purchasingTimeList: [],
                communications: [],
                enquiryTypeList: [],
                traderConcatDatas: [],
                businessChanceDto: {
                    isPolicymaker: null,
                    other: null,
                    traderId: null,
                    checkTraderArea: null,
                    traderName: null,
                    traderNameShow: '',
                    completion: null,
                    systemOrderRate: null,
                    systemBusinessLevel: null,
                    bussinessLevel: null,
                    traderContactId: null,
                    traderContactName: null,
                    terminalTraderType: null,
                    mobile: null,
                    telephone: null,
                    type: null,
                    resource: null,
                    checkTraderArea: '-',
                    businessChanceGoodsDtos: [],
                    tenderTime: '',
                    enquiryType: null,
                    purchasingType: null,
                    checkedCities: [],
                    communicateRecordDto: {
                        traderContactNameView: null,
                        time: [dateTime, dateTimeEnd],
                        noneNextDate: false,
                        contact: null,
                        contactMob: null,
                        contentSuffix: null,
                        sysTag: [],
                        userTag: [],
                        nextContactDate: null
                    }
                },

                orderTerminalDto: {
                    terminalName: ''
                },
                showRepeatSearch: false,

                loading: false,
                items: [],
                bussinessLevels: [],
                orderRates: [],
                queryTrader: []
            };
        },
        mounted() {
            loadingApp()
            sendThis(this);
        },
        watch: {
            businessChanceDto: {
                handler(newData, oldData) {
                    var data = JSON.parse(JSON.stringify(newData))
                    data.tagIds = data.checkedCities.join(",");
                    data.communicateRecordDto = null;
                    if (flag) {
                        flag = false;
                        setTimeout(() => calc(data).then(res => {
                            if (res.data.code == 0) {
                                this.completion = res.data.data.completion;
                                this.systemOrderRate = res.data.data.systemOrderRate;
                                this.systemBusinessLevel = res.data.data.systemBusinessLevel;
                            } else {
                                this.$message.warning("系统计算值接口异常");
                            }
                            flag = true;
                        }), 3000);

                    }


                },
                deep: true
                // ,
                // immediate:true
            }
        },

        methods: {
            // 终端信息搜索弹窗
            searchTerminal() {
                layer.open({
                    type: 2,
                    content: '/order/terminal/dialog.do?scene=0&hasTerminalInfo=0',
                    area: ['70%', '80%'],
                    title :'终端信息'
                });
            },

            repeatSearch() {
                this.orderTerminalDto = {
                    terminalName: ''
                };
                this.searchTerminal();
            },

            async initData() {
                await getCommunication(477).then(result => {
                    this.communications = result.data.data;
                })

                await getCommunication(410).then(result => {
                    this.purchasingTimeList = result.data.data;
                })
                await getCommunication(948).then(result => {
                    this.enquiryTypeList = result.data.data;
                })
                await getCommunication(943).then(result => {
                    this.bussinessStageList = result.data.data;
                })

                await getCommunication(938).then(result => {
                    this.bussinessLevels = result.data.data;
                })
                await getCommunication(951).then(result => {
                    this.orderRates = result.data.data;
                })
                await getBusinessTags().then(result => {
                    this.items = result.data.data;
                })

                await getCommunicationTag().then(result => {
                    this.sysTag = result.data.data;
                })

                getTerminalInfo({'businessId': viewInfo.businessChanceId, 'businessType': 1}).then(res => {
                    if (res.data.data != null) {
                        this.orderTerminalDto = res.data.data;
                        this.showRepeatSearch = true;
                    }
                })

                // 编辑线索基本工作
                this.doUpdateBusinessChance()

                // 线索转商机 基础工作
                if (viewInfo.businessLeadsId != null && viewInfo.businessLeadsId != '') {
                    getCommunication(4112).then(result => {
                        this.communications = result.data.data;
                    })
                    leadsConverData(viewInfo.businessLeadsId).then(res => {
                        let data = {...res.data.data, checkedCities: []};
                        if (data.bussinessLevel == 0) {
                            data.bussinessLevel = null
                        }
                        if (data.orderRate == 0) {
                            data.orderRate = null
                        }
                        if (data.traderId != null && data.traderId != 0) {
                            getTraderConcatDat(data.traderId).then(res => {
                                this.traderConcatDatas = res.data.data.list
                            });
                        }
                        data.traderContactNameShow = data.traderContactName + " " + data.mobile;
                        data.communicateRecordDto = this.businessChanceDto.communicateRecordDto;
                        data.communicateRecordDto.concat = this.businessChanceDto.traderContactName;
                        data.communicateRecordDto.concatMob = this.businessChanceDto.mobile;
                        data.businessChanceGoodsDtos = []
                        data.businessLeadsId = viewInfo.businessLeadsId
                        this.businessChanceDto = data;
                        this.initTraderData()
                        this.buildCommunicationData();
                    })
                }

                // 携带客户id的自动赋值相关值
                if (viewInfo.traderId != null && viewInfo.traderId != '') {

                    this.businessChanceDto.traderId = viewInfo.traderId
                    if (this.businessChanceDto.traderId != null && this.businessChanceDto.traderId != 0) {

                        getTraderCustomerInfo(this.businessChanceDto.traderId).then(res=>{

                            if (res.data.data != null) {
                                this.customerTypeStr = res.data.data.customerTypeStr;
                                this.customerNature = res.data.data.customerNature;
                                this.businessChanceDto.areaIds = res.data.data.areaIds;
                                this.businessChanceDto.areaId = res.data.data.areaId;
                                this.businessChanceDto.traderId = res.data.data.traderId;
                                this.businessChanceDto.userId = res.data.data.saleId;
                                this.businessChanceDto.traderName = res.data.data.traderName
                                this.businessChanceDto.checkTraderArea = res.data.data.address;
                                getTraderConcatDat(this.businessChanceDto.traderId).then(res => {
                                    this.traderConcatDatas = res.data.data.list

                                    if (this.traderConcatDatas!=null&&this.traderConcatDatas.length > 0) {
                                        this.businessChanceDto.traderContactName = this.traderConcatDatas[0].name;
                                        this.businessChanceDto.traderContactNameShow = this.traderConcatDatas[0].name + " " + this.traderConcatDatas[0].mobile;
                                        this.businessChanceDto.traderContactId = this.traderConcatDatas[0].traderContactId;
                                        this.businessChanceDto.mobile = this.traderConcatDatas[0].mobile;
                                        this.businessChanceDto.telephone = this.traderConcatDatas[0].telephone;

                                        this.businessChanceDto.communicateRecordDto.traderContactNameView = this.traderConcatDatas[0].name + " " + this.traderConcatDatas[0].mobile + " " + this.traderConcatDatas[0].telephone;
                                        this.businessChanceDto.communicateRecordDto.traderContactId = this.traderConcatDatas[0].traderContactId;
                                    } else {
                                        this.businessChanceDto.traderContactName = '';
                                        this.businessChanceDto.traderContactNameShow = '';
                                        this.businessChanceDto.traderContactId = '';
                                        this.businessChanceDto.mobile = '';
                                        this.businessChanceDto.telephone = '';

                                        this.businessChanceDto.communicateRecordDto.traderContactNameView = '';
                                        this.businessChanceDto.communicateRecordDto.traderContactId = '';
                                    }

                                });
                            }
                        })

                        this.addressClear();
                    }

                }

                getCascaderRegionOptions().then(res => {
                    this.areaOptions = res.data.data;
                    if (this.businessChanceDto.bussinessChanceId != null || this.businessChanceDto.businessLeadsId != null) {
                        if (this.businessChanceDto.traderId == 0 || this.businessChanceDto.traderId == null) {
                            debugger
                            if (this.businessChanceDto.areaIds != null) {
                                let strings = this.businessChanceDto.areaIds.split(",");
                                let address = [];
                                if (strings.length != null && strings.length > 0) {
                                    strings.forEach(item => {
                                        address.push(+item);
                                    });
                                }
                                this.areaSelectedOptions = address;
                            }
                        }
                    }
                });


            },
            doUpdateBusinessChance() {
                if (viewInfo.businessChanceId != null && viewInfo.businessChanceId != '') {
                    updateSelectOneData(viewInfo.businessChanceId).then(res => {
                        var tagIds = res.data.data.tagIds.split(',');
                        var array = []
                        var data = {...res.data.data};

                        if (tagIds.length > 0) {
                            tagIds.forEach(a => {
                                if (this.items.find(i => {
                                    return i.id == Number(a)
                                })) {
                                    array.push(+a);
                                }
                            })

                            data = {...data, checkedCities: array};
                        }

                        if (data.bussinessLevel == 0) {
                            data.bussinessLevel = null
                        }
                        if (data.orderRate == 0) {
                            data.orderRate = null
                        }
                        if (data.traderId != null && data.traderId != 0) {
                            getTraderConcatDat(data.traderId).then(res => {
                                this.traderConcatDatas = res.data.data.list
                            });
                        }
                        if (data.source != null && data.source != 0) {
                            getCommunication(data.source).then(result => {
                                this.communications = result.data.data;
                            })
                        }
                        data.traderContactNameShow = data.traderContactName + " " + data.mobile;
                        data = {...data, checkedCities: array};
                        this.businessChanceDto = data;
                        this.completion = this.businessChanceDto.completion;
                        this.systemBusinessLevel = this.businessChanceDto.systemBusinessLevel;
                        this.systemOrderRate = this.businessChanceDto.systemOrderRate;

                        this.initTraderData()
                    })
                }
            },
            initTraderData() {
                if (this.businessChanceDto.traderId != null && this.businessChanceDto.traderId != 0) {
                    getTraderCustomerInfo(this.businessChanceDto.traderId).then(res=>{

                        if (res.data.data != null) {
                            this.customerTypeStr = res.data.data.customerTypeStr;
                            this.customerNature = res.data.data.customerNature;
                            this.businessChanceDto.areaIds = res.data.data.areaIds;
                            this.businessChanceDto.areaId = res.data.data.areaId;
                        }
                    })

                    getTraderConcatDat(this.businessChanceDto.traderId).then(res => {
                        this.traderConcatDatas = res.data.data.list
                    });
                }
            },
            getCheckedAddressNodes() {
                let addressNode = this.$refs['cascader'].getCheckedNodes();

                this.businessChanceDto.areaId = null;
                this.province = null;
                this.city = null;
                this.county = null;
                addressNode[0].pathNodes.forEach(node => {
                    if (node.level === 1) {
                        this.province = node.value;
                    }
                    if (node.level === 2) {
                        this.city = node.value
                    }
                    if (node.level === 3) {
                        this.county = node.value
                        this.businessChanceDto.areaId = node.value
                    }
                });

            },
            buildCommunicationData() {
                var array = []
                if (this.businessChanceDto.traderContactName != null
                    && this.businessChanceDto.traderContactName !== ''
                    && this.businessChanceDto.mobile != null
                    && this.businessChanceDto.mobile !== '') {
                    let mob = {
                        id: 1,
                        traderContactName: this.businessChanceDto.traderContactName,
                        type: this.businessChanceDto.mobile,

                    };
                    array.push(mob)
                }
                if (this.businessChanceDto.traderContactName != null
                    && this.businessChanceDto.traderContactName !== ''
                    && this.businessChanceDto.telephone != null
                    && this.businessChanceDto.telephone !== '') {
                    let data = {
                        id: 2,
                        traderContactName: this.businessChanceDto.traderContactName,
                        type: this.businessChanceDto.telephone

                    };
                    array.push(data)
                }
                this.bindCommunicationData = array;

            },
            // 点击触发输入
            showInput() {
                this.inputVisible_tag = true;
                this.$nextTick(_ => {
                    this.$refs.saveTagInput.$refs.input.focus();
                });
            },
            handleInputConfirm() {
                debugger
                let inputValue = this.inputTagValue;
                if (inputValue) {
                    if (this.businessChanceDto.communicateRecordDto.userTag.find(i => i == inputValue)) {
                        this.$message.warning("选择的标签值已存在");
                    } else {
                        this.businessChanceDto.communicateRecordDto.userTag.push(inputValue);
                    }

                }
                this.inputVisible_tag = false;
                this.inputTagValue = '';
            },
            //删除系统标签
            handlesysTagUserClose(item) {
                debugger
                let indexNum = null;
                for (let i = 0; i < this.sysTagUserCheck.length; i++) {
                    if (this.sysTagUserCheck[i].tagId==item.tagId) {
                        indexNum = i;
                    }
                }
                if (indexNum != null) {
                    this.sysTagUserCheck.splice(indexNum, 1);
                }

                this.businessChanceDto.communicateRecordDto.sysTag.splice(this.businessChanceDto.communicateRecordDto.sysTag.indexOf(item.tagId), 1);
            },
            // 删除用户标签
            handleUserTagClose(item) {
                this.businessChanceDto.communicateRecordDto.userTag.splice(this.businessChanceDto.communicateRecordDto.userTag.indexOf(item), 1);
            },

            // 标签点击事件
            sysTagClick(item) {

                debugger
                if (this.businessChanceDto.communicateRecordDto.sysTag.find(i => i == item.tagId)) {
                    this.$message.warning("选择的标签值已存在");
                } else {
                    this.businessChanceDto.communicateRecordDto.sysTag.push(item.tagId)
                    this.sysTagUserCheck.push(item)
                }

            },
            bindNum() {
                if (this.businessChanceDto.checkedCities.length == 3) {
                    this.$message.warning("最多同时选择3个标签");
                }
            },
            onSubmit(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.businessChanceDto.orderTerminalDto = this.orderTerminalDto;
                        let data = JSON.parse(JSON.stringify(this.businessChanceDto));
                        data.tagIds = data.checkedCities.join(",");
                        if (data.traderId == null || data.traderId == 0) {
                            data.areaIds = this.areaSelectedOptions.join(",");
                        }

                        this.formDisabled = true;
                        if (this.businessChanceDto.bussinessChanceId == null || this.businessChanceDto.bussinessChanceId == '') {
                            if (data.communicateRecordDto.noneNextDate) {
                                data.communicateRecordDto.noneNextDate = 1;
                            } else {
                                data.communicateRecordDto.noneNextDate = 0;
                            }
                            if (data.communicateRecordDto.time.length > 1) {
                                data.communicateRecordDto.begintime = this.businessChanceDto.communicateRecordDto.time[0]
                                data.communicateRecordDto.endtime = this.businessChanceDto.communicateRecordDto.time[1]
                            }
                            data.isAiAssistant = viewInfo.isAiAssistant;
                            data.aiCommunicateRecordId = viewInfo.aiCommunicateRecordId;
                            add(data).then(res => {


                                if (res.data.code == 0) {



                                    if (res.data.data.bussinessChanceId != null && res.data.data.quoteorderDto != null && res.data.data.quoteorderDto.quoteorderId != null) {
                                        this.$message({
                                            message: '商机：' + res.data.data.bussinessChanceNo + '保存成功，同时满足转报价，已生成报价单:' + res.data.data.quoteorderDto.quoteorderNo + " 即将跳转报价单",
                                            type: 'success',
                                            onClose: () => {
                                                openTab("报价单详情", '/order/quote/getQuoteDetail.do?quoteorderId=' + res.data.data.quoteorderDto.quoteorderId + '&viewType=2')
                                                this.closeThis()
                                            }
                                        });

                                    }
                                    if (res.data.data.bussinessChanceId != null && (res.data.data.quoteorderDto == null || res.data.data.quoteorderDto.quoteorderId == null)) {
                                        this.$message({
                                            message: '商机：' + res.data.data.bussinessChanceNo + '保存成功',
                                            type: 'success',
                                            onClose: () => {
                                                openTab("商机详情", '/businessChance/details.do?id=' + res.data.data.bussinessChanceId)
                                                this.closeThis()
                                            }
                                        });

                                    }
                                } else {
                                    this.$message({
                                        message: res.data.message,
                                        type: 'error',
                                    });
                                    this.formDisabled = false;
                                }
                            });
                        } else {
                            update(data).then(res => {
                                if (res.data.code == 0) {

                                    if (res.data.data.bussinessChanceId != null && res.data.data.quoteorderDto != null && res.data.data.quoteorderDto.quoteorderId != null) {
                                        this.$message({
                                            message: '商机：' + res.data.data.bussinessChanceNo + '更新成功，同时满足转报价，已生成报价单:' + res.data.data.quoteorderDto.quoteorderNo + " 即将跳转报价单",
                                            type: 'success',
                                            onClose: () => {
                                                openTab("报价单详情", '/order/quote/getQuoteDetail.do?quoteorderId=' + res.data.data.quoteorderDto.quoteorderId + '&viewType=2')
                                                this.closeThis()
                                            }
                                        });

                                    }
                                    if (res.data.data.bussinessChanceId != null && res.data.data.quoteorderDto == null) {
                                        this.$message({
                                            message: '商机：' + res.data.data.bussinessChanceNo + '更新成功',
                                            type: 'success',
                                            onClose: () => {
                                                openTab("商机详情", '/businessChance/details.do?id=' + res.data.data.bussinessChanceId)
                                                this.closeThis()
                                            }
                                        });
                                    }

                                } else {
                                    this.$message({
                                        message: res.data.message,
                                        type: 'error',
                                    });
                                    this.formDisabled = false;
                                }
                            });
                        }


                    }
                })
            },
            traderConcatSelect(item) {
                this.businessChanceDto.traderContactNameShow = item.name + ' ' + item.mobile
                this.businessChanceDto.traderContactName = item.name
                this.businessChanceDto.traderContactId = item.traderContactId
                this.businessChanceDto.mobile = item.mobile;
                this.businessChanceDto.telephone = item.telephone;
                this.businessChanceDto.communicateRecordDto.contact = item.name
                this.businessChanceDto.communicateRecordDto.contactMob = item.mobile

                this.businessChanceDto.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile + " " + item.telephone
            },
            traderConcatSelectComm(item) {
                this.businessChanceDto.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile + " " + item.telephone
                this.businessChanceDto.communicateRecordDto.traderContactId = item.traderContactId
            },
            traderConcatSelectCommNoTraderId(item) {
                this.businessChanceDto.communicateRecordDto.traderContactNameView = item.traderContactName + " " + item.type
                this.businessChanceDto.communicateRecordDto.contact = item.traderContactName
                this.businessChanceDto.communicateRecordDto.contactMob = item.type
            },
            traderNameSelect(value) {
                if (value.belong) {
                    // debugger
                    this.businessChanceDto.traderId = value.traderId;
                    this.businessChanceDto.userId = value.saleId;
                    this.businessChanceDto.traderName = value.traderName
                    this.businessChanceDto.checkTraderArea = value.address;
                    this.businessChanceDto.areaIds = value.areaIds;
                    this.businessChanceDto.areaId = value.areaId;
                    this.customerTypeStr = value.customerTypeStr;
                    this.customerNature = value.customerNature;
                    getTraderConcatDat(this.businessChanceDto.traderId).then(res => {
                        this.traderConcatDatas = res.data.data.list

                        if (this.traderConcatDatas!=null&&this.traderConcatDatas.length > 0) {
                            this.businessChanceDto.traderContactName = this.traderConcatDatas[0].name;
                            this.businessChanceDto.traderContactNameShow = this.traderConcatDatas[0].name + " " + this.traderConcatDatas[0].mobile;
                            this.businessChanceDto.traderContactId = this.traderConcatDatas[0].traderContactId;
                            this.businessChanceDto.mobile = this.traderConcatDatas[0].mobile;
                            this.businessChanceDto.telephone = this.traderConcatDatas[0].telephone;

                            this.businessChanceDto.communicateRecordDto.traderContactNameView = this.traderConcatDatas[0].name + " " + this.traderConcatDatas[0].mobile + " " + this.traderConcatDatas[0].telephone;
                            this.businessChanceDto.communicateRecordDto.traderContactId = this.traderConcatDatas[0].traderContactId;
                        } else {
                            this.businessChanceDto.traderContactName = '';
                            this.businessChanceDto.traderContactNameShow = '';
                            this.businessChanceDto.traderContactId = '';
                            this.businessChanceDto.mobile = '';
                            this.businessChanceDto.telephone = '';

                            this.businessChanceDto.communicateRecordDto.traderContactNameView = '';
                            this.businessChanceDto.communicateRecordDto.traderContactId = '';
                        }

                    });
                    this.addressClear();
                }

            },
            traderContactClear() {
                this.businessChanceDto.traderContactName = ''
                this.businessChanceDto.traderContactId = null
                this.businessChanceDto.mobile = '';
                this.businessChanceDto.telephone = '';

                this.businessChanceDto.communicateRecordDto.traderContactNameView = null;
                this.businessChanceDto.communicateRecordDto.traderContactId = null;
                this.businessChanceDto.userId = null;
            },
            // 清除联系人信息
            traderContactClearComm() {
                this.businessChanceDto.traderContactNameShow = null;
                this.businessChanceDto.communicateRecordDto.traderContactNameView = null;
                this.businessChanceDto.communicateRecordDto.traderContactId = null;
                this.businessChanceDto.communicateRecordDto.contact = null;
                this.businessChanceDto.communicateRecordDto.contactMob = null;
            },
            addressClear() {
                this.areaSelectedOptions = [];
                this.province = null;
                this.city = null;
                this.county = null;
                this.businessChanceDto.areaId = null
            },
            traderNameClear() {
                this.businessChanceDto.traderId = null
                this.businessChanceDto.checkTraderArea = '-';
                this.businessChanceDto.areaIds = null;
                this.businessChanceDto.areaId = 0;
                this.businessChanceDto.traderName = '';
                this.customerTypeStr = '-';
                this.customerNature = 0;
                this.traderConcatDatas = [];
                this.traderContactClear();
                this.traderContactClearComm()
                this.bindCommunicationData = [];
                this.addressClear();

            },
            remoteTraderMethod(query) {
                //客户名称远程搜索
                if (query) {
                    this.loading = true;

                    getTraderRemoteList({"name": query})
                        .then(res => {
                            this.loading = false;
                            this.queryTrader = res.data.data;
                        });
                } else {
                    this.loading = false;
                    this.queryTrader = [];
                }
            },
            selectBlur(e) {
                // debugger
                this.businessChanceDto.traderName = e.target.value
                this.businessChanceDto.traderId = null
                this.businessChanceDto.checkTraderArea = '-';
                this.customerTypeStr = '-';
                this.customerNature = 0;
            },
            traderCheckBind() {


                if (this.traderCheck) {
                    this.traderNameClear();
                    this.rules.traderName = [{required: false, message: '', trigger: 'change'}
                        , {max: 50, message: '最大支持输入50个字', trigger: ['blur', 'change']}];
                } else {
                    this.rules.traderName = [{required: true, message: '请输入客户名称', trigger: 'change'}
                        , {max: 50, message: '最大支持输入50个字', trigger: ['blur', 'change']}];
                }

            },
            communicateRecordNextBind() {
                if (this.businessChanceDto.communicateRecordDto.noneNextDate) {
                    this.businessChanceDto.communicateRecordDto.nextContactDate =null;
                    this.rules = {
                        ...this.rules,
                        'communicateRecordDto.nextContactDate': [{
                            // type: 'date',
                            required: false,
                            message: '',
                            trigger: 'change'
                        }]
                    };
                } else {
                    this.businessChanceDto.communicateRecordDto.nextContactDate =null;
                    this.rules = {
                        ...this.rules,
                        'communicateRecordDto.nextContactDate': [{
                            // type: 'date',
                            required: true,
                            message: '请输入下次沟通时间',
                            trigger: 'change'
                        }]
                    };
                }
            },
            calcTotal(row) {
                if (row.num == null || row.price == null) {
                    row.total = '';
                } else {
                    row.total = Number(row.num * row.price).toFixed(2);
                }
                this.tableNum = this.calcTableNum();
                this.tableAmount = this.calcTableAmount();
                return row.total;
            },
            calcTableNum() {

                let total = 0;
                if (this.businessChanceDto.businessChanceGoodsDtos.length > 0) {
                    this.businessChanceDto.businessChanceGoodsDtos.map(item => {
                        if (item.num != null) {
                            total += Number(item.num);
                        }
                    })
                } else {
                    return total;
                }
                return total;

            },
            calcTableAmount() {

                let total = 0;
                if (this.businessChanceDto.businessChanceGoodsDtos.length > 0) {
                    this.businessChanceDto.businessChanceGoodsDtos.map(item => {
                        total += Number(item.total);

                    })
                } else {
                    return Number(total).toFixed(2);
                }
                return Number(total).toFixed(2);

            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            addTrader() {
                openTab("新增客户", '/trader/customer/add.do')
            },
            handleDelete(index, row) {
                this.$confirm('确认删除此产品?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.businessChanceDto.businessChanceGoodsDtos.splice(index, 1)
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });

            }

        }

    });


    function receiveTerminal(terminal) {
        // 在这里处理传递过来的参数
        var existId = vueObject.orderTerminalDto.orderTerminalId;
        vueObject.showRepeatSearch = true;
        vueObject.orderTerminalDto = terminal;
        vueObject.orderTerminalDto.orderTerminalId = existId;
    }
</script>
