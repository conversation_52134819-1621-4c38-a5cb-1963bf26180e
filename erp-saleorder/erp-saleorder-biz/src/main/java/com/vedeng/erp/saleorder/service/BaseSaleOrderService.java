package com.vedeng.erp.saleorder.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.util.StringUtil;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.constant.*;
import com.vedeng.common.core.utils.ServletUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.orderstrategy.OrderAmountStrategy;
import com.vedeng.common.service.BaseService;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.erp.saleorder.constant.PaymentTypeEnum;
import com.vedeng.erp.saleorder.constant.SalesOrderTypeEnum;
import com.vedeng.erp.saleorder.constant.TraderBelongPlatformEnum;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.enums.OrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SubStatusEnum;
import com.vedeng.erp.saleorder.strategy.OrderTerminalContext;
import com.vedeng.erp.saleorder.strategy.SaleOrderTerminalStrategy;
import com.vedeng.erp.saleorder.vo.AuditRecordInstanceVo;
import com.vedeng.erp.saleorder.vo.OrderStatusNode;
import com.vedeng.erp.saleorder.vo.SaleOrderProcessNode;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.model.GoodsChannelPrice;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.service.GoodsChannelPriceService;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.logistics.dao.ExpressDetailMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.order.api.constant.InvoiceMethodEnum;
import com.vedeng.order.dao.*;
import com.vedeng.order.enums.SaleOrderTypeEnum;
import com.vedeng.order.model.*;
import com.vedeng.order.model.dto.LabelQueryDto;
import com.vedeng.order.model.dto.RemarkComponentDto;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.model.vo.SkuVo;
import com.vedeng.order.service.*;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.dto.GoodSalePrice;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.price.service.PriceInfoDealWithService;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.constant.CancelReasonConstant;
import com.wms.service.CancelTypeService;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.vedeng.common.constant.CommonConstants.CREATE_SALE_ORDER_SUCCESS;

/**
 * @Author: daniel
 * @Date: 2021/10/8 13 33
 * @Description: 销售订单抽象服务类
 */
@Slf4j
@Service
public class BaseSaleOrderService {

    Logger logger= LoggerFactory.getLogger(BaseSaleOrderService.class);

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private BaseService baseService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private UserService userService;

    @Autowired
    private RiskCheckService riskCheckService;

    @Value("${deadlineOfAddAutoCheckedComment}")
    private Long deadlineOfAddAutoCheckedComment;

    @Autowired
    private SaleorderService saleorderService;

    @Resource
    private SaleorderCouponMapper saleorderCouponMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private SaleorderDataService saleorderDataService;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private InvoiceMapper invoiceMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private RBuyorderSaleorderMapper rBuyorderSaleorderMapper;

    @Autowired
    private OrderAccountPeriodService orderAccountPeriodService;

    @Autowired
    private CapitalBillService capitalBillService;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private QuoteorderGoodsMapper quoteGoodsMapper;

    @Autowired
    private VerifiesInfoMapper verifiesInfoMapper;

    @Autowired
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;

    @Resource
    private SaleorderModifyApplyMapper saleorderModifyApplyMapper;

    @Resource
    private SaleorderModifyApplyGoodsMapper saleorderModifyApplyGoodsMapper;

    @Autowired
    private RemarkComponentMapper remarkComponentMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private GoodsChannelPriceService goodsChannelPriceService;

    @Resource
    private PriceInfoDealWithService priceInfoDealWithService;

    @Resource
    private OrderNoDict orderNoDict;
    @Autowired
    private TraderMapper traderMapper;

    @Autowired
    private QuoteorderMapper quoteorderMapper;

    @Autowired
    private GoodsMapper goodsMapper;

    @Resource
    private TraderCustomerService traderCustomerService;

    @Resource
    private SaleorderDataSyncService saleorderDataSyncService;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Resource
    private BasePriceService basePriceService;

    @Resource
    private BussinessChanceMapper bussinessChanceMapper;

    @Resource
    private VerifiesRecordService verifiesRecordService;

    @Resource
    protected RegionService regionService;

    @Resource
    private OrderAmountStrategy orderAmountStrategy;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private ExpressDetailMapper expressDetailMapper;

    @Autowired
    @Qualifier("saleOrderElectronicSignHandle")
    private AbstractElectronicSignHandle saleOrderElectronicSignHandle;

    @Autowired
    private SaleOrderTerminalStrategy saleOrderTerminalStrategy;

    public Saleorder getSaleOrderBaseInfo(Integer saleOrderId){
        return saleorderMapper.getSaleOrderBaseInfo(saleOrderId);
    }

    /**
     * 获取销售订单商品的已采购数量
     * @param saleOrderId 销售订单id
     * @return 已采购商品数量
     * 销售单商品已采购数量 = 部分付款/全部付款的采购单商品 - 采购单对应的已完结的退货售后单商品
     */
    public List<SaleorderGoods> getPurchasedCountOfSaleOrder(Integer saleOrderId){
        //获取订单商品的实际数量
        return getRealSaleOrderGoodsListExceptAfterSales(saleOrderId)
                .stream()
                .peek(item -> {
                    //获取销售订单对应的VP采购订单的已采购数量
                    Integer purchasedCount = buyorderGoodsMapper.getPurchasedCountOfSaleOrderGoods(item.getSaleorderGoodsId());
                    item.setBuyNum(purchasedCount == null ? 0 : purchasedCount);
                })
                .collect(Collectors.toList());
    }


    /**
     * 获取销售订单的实际金额（减去售后完结的售后单）
     * @param saleOrderId 订单id
     * @return 订单实际金额
     */
    public BigDecimal getRealTotalAmountOfSaleOrder(Integer saleOrderId){
        return saleorderMapper.getRealTotalAmountExceptAfterSalesFinished(saleOrderId);
    }


    /**
     * 获取订单的实际订单商品数量（除去售后退货完结的商品）
     * @param saleOrderId 销售订单id
     * @return 订单商品集合
     */
    protected List<SaleorderGoods> getRealSaleOrderGoodsListExceptAfterSales(Integer saleOrderId){
        //售后退货完结的商品
        Map<Integer, Integer> afterSalesGoodsMap = afterSalesGoodsMapper.getAfterSalesGoodsCountOfSaleOrderRefund(saleOrderId)
                .stream()
                .collect(Collectors.toMap(AfterSalesGoods::getOrderDetailId,AfterSalesGoods::getNum));

        return saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleOrderId)
                .stream()
                .filter(saleOrderGoods -> saleOrderGoods.getSaleorderGoodsId() != null)
                .map(SaleorderGoods::clone)
                .peek(saleOrderGoods -> {
                    if (afterSalesGoodsMap.containsKey(saleOrderGoods.getSaleorderGoodsId())){
                        int num = saleOrderGoods.getNum() - afterSalesGoodsMap.get(saleOrderGoods.getSaleorderGoodsId());
                        saleOrderGoods.setNum(num);
                        saleOrderGoods.setAfterReturnNum(afterSalesGoodsMap.get(saleOrderGoods.getSaleorderGoodsId()));
                    }
                })
                .collect(Collectors.toList());
    }

    public void createSaleOrder(Object o){

    }


    /**
     * 销售订单撤销生效
     * 1、订单状态回到待确认、未生效
     * 2、删除verifiesInfo中的审核记录
     * 3、订单合同审核状态变更，审核通过->审核不通过
     * @param saleorder 订单信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelValidOfSaleOrder(Saleorder saleorder){
        long start=System.currentTimeMillis();
        Integer saleOrderId = saleorder.getSaleorderId();
        saleorderMapper.cancelValidOfSaleOrder(saleOrderId,System.currentTimeMillis());
        VerifiesInfo querySaleOrderVerify = new VerifiesInfo();
        querySaleOrderVerify.setRelateTable("T_SALEORDER");
        querySaleOrderVerify.setVerifiesType(623);
        querySaleOrderVerify.setRelateTableKey(saleOrderId);
        Optional.ofNullable(verifiesInfoMapper.getVerifiesInfoByParam(querySaleOrderVerify))
                .ifPresent(item -> {
                    log.info("撤销销售订单生效，删除销售订单审核记录，item={}", JSON.toJSONString(item));
                    verifiesInfoMapper.deleteByPrimaryKey(item.getVerifiesInfoId());
                        });

        //销售合同回传审核状态重置
        VerifiesInfo queryContactReturnVerify = new VerifiesInfo();
        queryContactReturnVerify.setRelateTable("T_SALEORDER");
        queryContactReturnVerify.setVerifiesType(868);
        queryContactReturnVerify.setRelateTableKey(saleOrderId);
        Optional.ofNullable(verifiesInfoMapper.getVerifiesInfoByParam(queryContactReturnVerify))
                .ifPresent(item -> {
                    if (item.getStatus() == 0){
                        //审核中的合同审核，自动改为审核不通过
                        VerifiesInfo rejectInfo = new VerifiesInfo();
                        rejectInfo.setVerifiesInfoId(item.getVerifiesInfoId());
                        rejectInfo.setStatus(2);
                        rejectInfo.setVerifyUsername("njadmin");
                        verifiesInfoMapper.updateByPrimaryKey(rejectInfo);

                        String taskId = actionProcdefService.getLeastTaskOfBusinessKey("contractReturnVerify_" + saleOrderId);
                        Map<String,Object> variableMap = new HashMap<>();
                        variableMap.put("pass", false) ;
                        actionProcdefService.completeTaskByAdmin(taskId,"订单撤销生效，导致合同回传审核状态变化",variableMap);

                    } else if (item.getStatus() == 1){
                        //审核通过的合同审核，删除verifiesInfo记录
                        log.info("撤销销售订单生效，合同审核通过，删除合同回传审核记录，item={}", JSON.toJSONString(item));
                        verifiesInfoMapper.deleteByPrimaryKey(item.getVerifiesInfoId());

                        //历史审核记录，添加备注
                        List<HistoricActivityInstance> list = actionProcdefService.getHistoricActivityInstanceListByBusinessKey(
                                "contractReturnVerify_" + saleOrderId);
                        String taskId = null;
                        String processInstanceId = null;
                        if (CollectionUtils.isEmpty(list) || !"审核完成".equals(list.get(list.size() - 1).getActivityName())){
                            log.error("撤销销售订单生效，合同审核通过，删除合同回传审核记录，未找到审核完成的审核记录，item={}", JSON.toJSONString(item));
                            return;
                        }
                        for (int i = list.size() - 1; i >= 0; i--) {
                            if (!StringUtils.isEmpty(list.get(i).getTaskId())){
                                taskId = list.get(i).getTaskId();
                                processInstanceId = list.get(i).getProcessInstanceId();
                                break;
                            }
                        }

                        if (!StringUtils.isEmpty(taskId) && !StringUtils.isEmpty(processInstanceId)){
                            log.info("撤销销售订单生效，合同审核通过，删除合同回传审核记录，添加审核备注，item={}，taskId={}，processInstanceId={}", JSON.toJSONString(item), taskId, processInstanceId);
                            actionProcdefService.manualAddCommentForTask(processInstanceId,taskId,"订单撤销生效，导致合同回传审核状态变化");
                        }
                    } else if (item.getStatus() == 2){
                        //审核不通过，重置为待审核状态
                        log.info("撤销销售订单生效，合同审核不通过，删除合同回传审核记录，item={}", JSON.toJSONString(item));
                        verifiesInfoMapper.deleteByPrimaryKey(item.getVerifiesInfoId());
                    }
                });

        // 确认单：撤销订单时 1.将确认时间更新为空 2.状态更新为0 3.清空合同url
        if(!OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_JCO.equals(saleorder.getOrderType())
                && !OrderConstant.ORDER_TYPE_BD.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_HC.equals(saleorder.getOrderType())
                && !OrderConstant.ORDER_TYPE_DH.equals(saleorder.getOrderType())){
            logger.info("电子签章：撤销订单时,更新确认单状态开始");
            Saleorder confirmTimeAndStatus = new Saleorder();
            confirmTimeAndStatus.setConfirmTime(null);
            confirmTimeAndStatus.setConfirmStatus(ErpConst.STATUS_STATE.NO);
            confirmTimeAndStatus.setSaleorderId(saleOrderId);
            confirmTimeAndStatus.setModTime(System.currentTimeMillis());
            logger.info("电子签章：撤销订单时,更新确认单状态结束");
            saleorderMapper.updateConfirmTimeAndStatus(confirmTimeAndStatus);
        }

        logger.info("电子签章：撤销订单时,更新合同url开始");
        Saleorder signSaleOrder = new Saleorder();
        signSaleOrder.setContractUrl(null);
        signSaleOrder.setContractNoStampUrl(null);
        signSaleOrder.setSaleorderId(saleOrderId);
        signSaleOrder.setModTime(System.currentTimeMillis());
        saleorderMapper.updateSaleOrderContractUrl(signSaleOrder);
        logger.info("电子签章：撤销订单时,更新合同url结束");

        // 电子签章：撤销订单时 1.终止电子签章流程
        logger.info("电子签章：撤销订单时,终止电子签章流程开始");
        HttpServletRequest request =  ServletUtils.getRequest();
        User user = (User)request.getSession().getAttribute(ErpConst.CURR_USER);
        BusinessInfo businessInfo = new BusinessInfo();
        businessInfo.setOperator(user.getUsername());
        businessInfo.setOrderNo(saleorder.getSaleorderNo());
        if(checkSignCompanyInfo(saleOrderId)){
            businessInfo.setBusinessName(ElectronicSignBusinessEnums.MULT_SALEORDER_SEAL.getType().toString());
        }else{
            businessInfo.setBusinessName(ElectronicSignBusinessEnums.SALE_ORDER.getType().toString());
        }
        businessInfo.setBusinessType(1);
        saleOrderElectronicSignHandle.revokeSignFlow(businessInfo);
        logger.info("电子签章：撤销订单时,终止电子签章流程结束");

        //账期撤销生效
        orderAccountPeriodService.unfreezingBillPeriodByCancelValidSaleOrder(saleOrderId);

        //重新计算合同状态
        saleorderDataSyncService.syncContractStatusBySaleorderId(saleOrderId,"cancelvalid");

        long end=System.currentTimeMillis();
        if((end-start)>10000){
            logger.error("事务过长");
        }
    }

    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;


    private boolean checkSignCompanyInfo(Integer saleOrderId) {
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleorder == null) {
            log.error("发起电子签单时，多主体，该订单未找到");
            return false;
        }

        BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(saleorder.getTraderName());
        if (baseCompanyInfoDto == null) {
            return false;
        }
        return true;

    }


    /**
     * 使用客户账期，100%进行信用支付
     * @param saleOrderId 订单id
     * @param user 操作用户
     * @param request 请求
     */
    @Transactional
    public void addCapitalBillByCustomerBillPeriod(Integer saleOrderId, User user, HttpServletRequest request) throws Exception {
        long start=System.currentTimeMillis();
        Saleorder saleOrderInfo = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleOrderInfo.getIsNew() == 0 || saleOrderInfo.getPaymentType() != 423){
            throw new Exception("该接口只适用于订单流100%账期支付的新订单");
        }
        //获取订单客户的归属销售
        User belongUser = new User();
        if (saleOrderInfo.getTraderId() != null){
            belongUser = userService.getUserInfoByTraderId(saleOrderInfo.getTraderId(),1);
        }

        //如果是账期支付，占用已冻结的账期
        orderAccountPeriodService.occupyBillPeriodAmount(saleOrderInfo);

        //添加信用支付流水
        if (saleOrderInfo.getPrepaidAmount().compareTo(BigDecimal.ZERO) == 0) {

            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleOrderInfo.getSaleorderId());
            if (saleorder.getRetainageAmount() == null || saleOrderInfo.getRetainageAmount().compareTo(BigDecimal.ZERO) == 0) {
                //如果尾款等于0.付款状态为全部付款
                saleorder.setPaymentStatus(2);
            } else {
                //如果尾款不等于0.付款状态为部分付款
                saleorder.setPaymentStatus(1);
            }
            saleorder.setAccountPeriodAmount(saleOrderInfo.getAccountPeriodAmount());
            saleorder.setSatisfyDeliveryTime(DateUtil.sysTimeMillis());
            saleorder.setPaymentTime(DateUtil.sysTimeMillis());
            //添加流水
            CapitalBill capitalBill = new CapitalBill();
            capitalBill.setCompanyId(user.getCompanyId());
            //信用支付
            capitalBill.setTraderMode(527);
            capitalBill.setCurrencyUnitId(1);
            capitalBill.setTraderTime(DateUtil.sysTimeMillis());
            //交易类型 转移
            capitalBill.setTraderType(3);
            capitalBill.setPayer(saleOrderInfo.getTraderName());
            capitalBill.setPayee(user.getCompanyName());

            List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
            CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
            //订单类型   销售订单
            capitalBillDetail.setOrderType(1);
            capitalBillDetail.setOrderNo(saleOrderInfo.getSaleorderNo());
            capitalBillDetail.setRelatedId(saleOrderInfo.getSaleorderId());
            //所属类型  经销商（包含终端）
            capitalBillDetail.setTraderType(1);
            capitalBillDetail.setTraderId(saleOrderInfo.getTraderId());
            capitalBillDetail.setUserId(saleOrderInfo.getUserId());
            //业务类型  订单收款
            capitalBillDetail.setBussinessType(526);
            capitalBillDetail.setAmount(saleOrderInfo.getAccountPeriodAmount());
            if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                capitalBillDetail.setOrgName(belongUser.getOrgName());
                capitalBillDetail.setOrgId(belongUser.getOrgId());
            }
            capitalBillDetails.add(capitalBillDetail);
            capitalBill.setCapitalBillDetails(capitalBillDetails);
            CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();
            capitalBillDetailInfo.setOrderType(1);
            capitalBillDetailInfo.setOrderNo(saleOrderInfo.getSaleorderNo());
            capitalBillDetailInfo.setRelatedId(saleOrderInfo.getSaleorderId());
            capitalBillDetailInfo.setTraderType(1);
            capitalBillDetailInfo.setTraderId(saleOrderInfo.getTraderId());
            capitalBillDetailInfo.setUserId(saleOrderInfo.getUserId());
            //业务类型  订单收款
            capitalBillDetailInfo.setBussinessType(526);
            capitalBillDetailInfo.setAmount(saleOrderInfo.getAccountPeriodAmount());
            if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
                capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
            }
            capitalBill.setCapitalBillDetail(capitalBillDetailInfo);
            //添加当前登陆人
            capitalBill.setCreator(user.getUserId());
            if(request==null){
                saleorderService.saveEditSaleorderInfo(saleorder);
            }else{
                saleorderService.saveEditSaleorderInfo(saleorder, request, request == null ? null : request.getSession());
            }
            saleorderService.updateSaleGoodsByAllSpecialGoods(saleOrderInfo.getSaleorderId());
            //如果是VS订单，不发送update到前-
            if (saleOrderInfo.getOrderType() == 0) {
                capitalBill.setBdPayStatusFlag(false);
            }
            capitalBillService.saveCapitalBill(capitalBill);
        }
        long end=System.currentTimeMillis();
        if((end-start)>10000){
            logger.error("事务过长");
        }

    }


    /**
     * 销售订单审核记录
     * @param saleOrderId 订单id
     * @param autoAudit 是否自动审核
     * @return 审核记录
     */
    public List<AuditRecordInstanceVo> getAuditRecordListOfSaleOrderVerify(Integer saleOrderId, Integer autoAudit){
        Saleorder saleorder = saleorderMapper.getSaleOrderBaseInfo(saleOrderId);
        String businessKey = "saleorderVerify_";
        if(autoAudit == 1){
            if(ErpConst.ONE.equals(saleorder.getOrderType())){
                businessKey = "bd_order_auto_verify_";
            }else {
                businessKey = "hc_order_auto_verify_";
            }
        }
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, businessKey + saleOrderId);
        List<HistoricActivityInstance> historicActivityInstances = (List<HistoricActivityInstance>) historicInfo.get("historicActivityInstance");
        User startUser = historicInfo.get("startUser") == null ? null : userService.getByUsername(historicInfo.get("startUser").toString(), ErpConst.NJ_COMPANY_ID);

        //VDERP-4086 质量管理】针对自动审核通过的销售订单，添加备注信息
        //对于历史订单，如果是资质自动审核的，那么补充对应的备注
        if (saleorder.getAddTime() < deadlineOfAddAutoCheckedComment){
            List<HistoricActivityInstance> instances = historicActivityInstances.stream().filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getActivityName())).collect(Collectors.toList());
            Map<String, Object> commentMap = (Map<String, Object>) historicInfo.get("commentMap");
            commentMap = saleorderService.checkAutoCheckOfSaleorderByVerifyLog(instances,commentMap,saleOrderId);
            historicInfo.put("commentMap",commentMap);
        }

        List<AuditRecordInstanceVo> targetAuditRecordInstanceVoList = new ArrayList<>();
        List<AuditRecordInstanceVo> originAuditRecordInstanceVoList = getAuditRecordListByTemplate(historicInfo);

        //在各个审核流程开始之前拼接质量风控审核记录
        for (AuditRecordInstanceVo item : originAuditRecordInstanceVoList){
            if ("开始".equals(item.getOperateInstance())){
                targetAuditRecordInstanceVoList.add(new AuditRecordInstanceVo("质量官",item.getOperateTime(),"质量风控审核", null));
                if (saleorder.getIsRisk() == 1 || saleorder.getIsRisk() == 3){
                    targetAuditRecordInstanceVoList.add(new AuditRecordInstanceVo("质量官",item.getOperateTime(),"驳回", saleorder.getRiskComments()));
                    boolean isQualityOrgOfStartUser = startUser != null && riskCheckService.isOrgFlag(startUser, ErpConst.QUALITY_ORG);
                    if (isQualityOrgOfStartUser){
                        String operatorName = userService.getRealNameByUserName(startUser.getUsername());
                        targetAuditRecordInstanceVoList.add(new AuditRecordInstanceVo(operatorName,item.getOperateTime(),"质量风控审核",null));
                        targetAuditRecordInstanceVoList.add(new AuditRecordInstanceVo(operatorName,item.getOperateTime(),"审核通过","<span style='color: red'>人工审核通过</span>"));
                    } else {
                        targetAuditRecordInstanceVoList.add(new AuditRecordInstanceVo("质量官",item.getOperateTime(),"质量风控审核",null));
                    }
                }
            }
            targetAuditRecordInstanceVoList.add(item);
        }

        long lastAuditRecordTime = 0;
        for (int i = originAuditRecordInstanceVoList.size() - 1; i >= 0 ; i--) {
            if (originAuditRecordInstanceVoList.get(i).getOperateTime() != null){
                lastAuditRecordTime = DateUtil.convertLong(originAuditRecordInstanceVoList.get(i).getOperateTime(),null);
                break;
            }
        }

        if(saleorder.getIsRisk() == 1 || saleorder.getIsRisk() == 3) {
            if (saleorder.getRiskTime() > lastAuditRecordTime) {
                targetAuditRecordInstanceVoList.add(new AuditRecordInstanceVo("质量官", DateUtil.convertString(saleorder.getRiskTime(), ""), "质量风控审核", null));
                targetAuditRecordInstanceVoList.add(new AuditRecordInstanceVo("质量官", DateUtil.convertString(saleorder.getRiskTime(), ""), "驳回",
                        saleorder.getRiskComments()));
            }
        }

        return targetAuditRecordInstanceVoList;
    }

    public List<AuditRecordInstanceVo> getAuditRecordListOfContractReturnVerify(Integer saleOrderId){
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "contractReturnVerify_" + saleOrderId);
        return getAuditRecordListByTemplate(historicInfo);
    }

    public List<AuditRecordInstanceVo> getAuditRecordListOfConfirmationOrderVerify(Integer batchId){
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "confirmationOrderVerify_" + batchId);
        return getAuditRecordListByTemplate(historicInfo);
    }


    private List<AuditRecordInstanceVo> getAuditRecordListByTemplate( Map<String, Object> historicInfo){
        List<HistoricActivityInstance> historicActivityInstances = (List<HistoricActivityInstance>) historicInfo.get("historicActivityInstance");
        Map<String, Object> commentMap = (Map<String, Object>) historicInfo.get("commentMap");
        User startUser = historicInfo.get("startUser") == null ? null : userService.getByUsername(historicInfo.get("startUser").toString(), ErpConst.NJ_COMPANY_ID);

        List<AuditRecordInstanceVo> instanceVos = new ArrayList<>();

        Task taskInfo = (Task) historicInfo.get("taskInfo");
        // 当前审核人
        String verifyUsers = null;
        List<String> verifyUserList = new ArrayList<>();
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            verifyUsers = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUsers"));
            String verifyUser = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUserList"));
            if (null != verifyUser) {
                verifyUserList = Arrays.asList(verifyUser.split(","));
            }
        }
        List<String> verifyUsersList = new ArrayList<>();
        if (verifyUsers != null && !verifyUserList.isEmpty()) {
            verifyUsersList = Arrays.asList(verifyUsers.split(","));
        }

        for (int i = 0; i < historicActivityInstances.size(); i++) {
            HistoricActivityInstance item = historicActivityInstances.get(i);
            AuditRecordInstanceVo instanceVo = new AuditRecordInstanceVo();
            if (StringUtils.isEmpty(item.getActivityName())){
                continue;
            }
            //操作人
            if ("startEvent".equals(item.getActivityType())){
                instanceVo.setOperatorName(startUser == null ? "" : userService.getRealNameByUserName(startUser.getUsername()));
            } else if ("intermediateThrowEvent".equals(item.getActivityType())){
                instanceVo.setOperatorName(null);
            } else {
                if (i == historicActivityInstances.size() -1){
                    for (String userName : verifyUsersList){
                        if (verifyUserList.contains(userName)){
                            instanceVo.setOperatorName("<span class='font-green'>" + userName + "</span>");
                        } else {
                            instanceVo.setOperatorName("<span>" + userName + "</span>");
                        }
                    }
                    if (verifyUsersList.isEmpty() && StringUtils.isEmpty(item.getAssignee())){
                        instanceVo.setOperatorName(verifyUsers);
                    }
                }
                if (StringUtils.isEmpty(instanceVo.getOperatorName())){
                    instanceVo.setOperatorName(userService.getRealNameByUserName(item.getAssignee()));
                }
            }

            //操作时间
            instanceVo.setOperateTime(item.getEndTime() == null ? null : DateUtil.DateToString(item.getEndTime(),DateUtil.TIME_FORMAT));
            //操作事项
            String operateInstance = item.getActivityName();
            if ("startEvent".equals(item.getActivityType())){
                operateInstance = "开始";
            } else if ("intermediateThrowEvent".equals(item.getActivityType())){
                operateInstance = "结束";
            }
            instanceVo.setOperateInstance(operateInstance);
            //备注
            if (commentMap.containsKey(item.getTaskId())){
                instanceVo.setComment("<span style='color: red'>" + commentMap.get(item.getTaskId()) + "</span>");
            }
            instanceVos.add(instanceVo);
        }
        return instanceVos;
    }



    private String getVerifyUserRealNames(String verifyUsers){
        if (StringUtils.isEmpty(verifyUsers)){
            return null;
        }
        return Arrays.stream(verifyUsers.split(","))
                .filter(Objects::nonNull)
                .map(item -> userService.getRealNameByUserName(item))
                .collect(Collectors.joining(","));
    }

    public Saleorder saveSaleOrder(Saleorder saleorder){
        // 新增订单主信息
        Integer i = saleorderMapper.insertSelective(saleorder);
        Integer saleorderId = saleorder.getSaleorderId();
        if (i == 1) {
            Saleorder saleorderExtra = saleorderMapper.getBaseSaleorderInfo(saleorderId);
            saleorderExtra.setSaleorderId(saleorderId);

            saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(saleorderId, 3));

            //订单流二期升级--根据客户的注册平台自动创建对应订单编号等start
            //1贝登医疗vs2医械购zxf6集采jc
            Trader trader = traderMapper.selectByPrimaryKey(saleorderExtra.getTraderId());
            if(StringUtils.isEmpty( saleorderExtra.getTraderName())){
                saleorderExtra.setTraderName(trader.getTraderName());
            }
            switch (TraderBelongPlatformEnum.getInstance(trader.getBelongPlatform())){
                case YXG:{
                    //zxf
                    saleorderExtra.setOrderType(9);
                    break;
                }
                case JC:{
                    //jcf
                    saleorderExtra.setOrderType(8);
                    // 订单结算主体为收票客户
                    saleorderExtra.setGroupCustomerId(saleorder.getTraderId());
                    populateTerminalCustomerInfo(saleorderExtra);
                    break;
                }
                default:{
                    //vs
                    saleorderExtra.setOrderType(0);
                    break;
                }
            }
            //订单流二期升级--根据客户的注册平台自动创建对应订单编号等end
            saleorderExtra.setTakeTraderId(saleorderExtra.getTraderId());
            saleorderExtra.setTakeTraderName(saleorderExtra.getTraderName());
            saleorderExtra.setTakeTraderContactId(saleorderExtra.getTraderContactId());
            saleorderExtra.setTakeTraderContactName(saleorderExtra.getTraderContactName());
            saleorderExtra.setTakeTraderContactMobile(saleorderExtra.getTraderContactMobile());
            saleorderExtra.setTakeTraderContactTelephone(saleorderExtra.getTraderContactTelephone());
            saleorderExtra.setTakeTraderAddressId(saleorderExtra.getTraderAddressId());

            saleorderExtra.setInvoiceTraderId(saleorderExtra.getTraderId());
            saleorderExtra.setInvoiceTraderName(saleorderExtra.getTraderName());
            saleorderExtra.setInvoiceTraderContactId(saleorderExtra.getTraderContactId());
            saleorderExtra.setInvoiceTraderContactName(saleorderExtra.getTraderContactName());
            saleorderExtra.setInvoiceTraderContactMobile(saleorderExtra.getTraderContactMobile());
            saleorderExtra.setInvoiceTraderContactTelephone(saleorderExtra.getTraderContactTelephone());
            saleorderExtra.setInvoiceTraderAddressId(saleorderExtra.getTraderAddressId());
            saleorderExtra.setCreateMobile(saleorderExtra.getTraderContactMobile());
            saleorderMapper.updateByPrimaryKeySelective(saleorderExtra);
            saleorder = saleorderExtra;
            return saleorderExtra;
        }
        return new Saleorder();
    }

    private void setSaleorderInfo(Saleorder saleorder,Saleorder saleorderExtra){
        //订单流二期升级--根据客户的注册平台自动创建对应订单编号等start
        //1贝登医疗vs2医械购zxf6集采jc
        Trader trader = traderMapper.selectByPrimaryKey(saleorderExtra.getTraderId());
        switch (TraderBelongPlatformEnum.getInstance(trader.getBelongPlatform())){
            case YXG:{
                //zxf
                saleorderExtra.setOrderType(9);
                saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(saleorder.getSaleorderId(), 25));
                break;
            }
            case JC:{
                //jcf
                saleorderExtra.setOrderType(8);
                saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(saleorder.getSaleorderId(), 24));
                // 订单结算主体为收票客户
                saleorderExtra.setGroupCustomerId(saleorder.getTraderId());
                populateTerminalCustomerInfo(saleorderExtra);
                break;
            }
            default:{
                //vs
                saleorderExtra.setOrderType(0);
                saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(saleorder.getSaleorderId(), 3));
                break;
            }
        }
    }

    private void setSaleorderNo(Saleorder saleorder,Saleorder saleorderExtra){
        switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
            case ZXF:{
                //zxf
                saleorderExtra.setOrderType(9);
                saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(saleorder.getSaleorderId(), 25));
                break;
            }
            case JCF:{
                //jcf
                saleorderExtra.setOrderType(8);
                saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(saleorder.getSaleorderId(), 24));
                break;
            }
            default:{
                break;
            }
        }
    }

    /**
     * <AUTHOR>
     * @desc 设置订单的类型
     * @param saleorder
     */
    public void setSaleorderType(Saleorder saleorder){
        //1贝登医疗vs2医械购zxf6集采jc
        Trader trader = traderMapper.selectByPrimaryKey(saleorder.getTraderId());
        switch (TraderBelongPlatformEnum.getInstance(trader.getBelongPlatform())){
            case YXG:{
                //zxf
                saleorder.setOrderType(9);
                break;
            }
            case JC:{
                //jcf
                saleorder.setOrderType(8);
                // 订单结算主体为收票客户
                saleorder.setGroupCustomerId(saleorder.getTraderId());
                populateTerminalCustomerInfo(saleorder);
                break;
            }
            default:{
                //vs
                saleorder.setOrderType(0);
                break;
            }
        }
    }
    private void populateTerminalCustomerInfo(Saleorder orderInfo) {
        TraderCustomer traderCustomer = new TraderCustomer();
        traderCustomer.setTraderId(orderInfo.getGroupCustomerId());
        TraderCustomerVo customerQuery = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
        if (customerQuery != null && SysOptionConstant.CUSTOMER_NATURE_TERMINAL.equals(orderInfo.getCustomerNature())) {
            // 终端类型
            orderInfo.setTerminalTraderType(customerQuery.getCustomerType());
            // 终端名称
            orderInfo.setTerminalTraderName(customerQuery.getTrader().getTraderName());
            // 销售区域
            orderInfo.setSalesArea((String) regionService.getRegion(customerQuery.getAreaId(), 2));
            Region region1 = regionService.getRegionByRegionId(customerQuery.getAreaId());
            if(region1 == null || region1.getRegionCode() == null){
                logger.info("订单创建查询销售区域id查询为空，客户id:{}",orderInfo.getTraderId());
            }else {
                orderInfo.setSalesAreaId(Integer.parseInt(region1.getRegionCode()));
            }
        }
    }

    public Saleorder saveEditSaleOrderInfo(Saleorder saleorder) {

        if(!StringUtils.isEmpty(saleorder.getDeliveryDelayTimeStr())){
            saleorder.setDeliveryDelayTime(DateUtil.convertLong(saleorder.getDeliveryDelayTimeStr(),DateUtil.DATE_FORMAT));
        }
        saleorderMapper.updateByPrimaryKeySelective(saleorder);

        if("2".equals(saleorder.getOptType())){
            //代表是审核通过或者生效
            orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_VAILD);
        }else{
            orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_EDIT);
        }

        return saleorder;

    }


    /**
     * 获取订单详情（db代码迁移到erp中）
     * @param saleorder
     * @return
     */
    public Saleorder getBaseSaleorderInfo(Saleorder saleorder,ModelAndView mv) {
        Integer saleorderId = saleorder.getSaleorderId();
        Saleorder sd = saleorderMapper.getSaleOrderBaseInfo(saleorderId);
        // add by franlin.wu for[耗材商城订单管理新增耗材订单类型] at 2018-12-06 begin
        if (null != sd) {
            // 根据订单ID查询优惠券
            SaleorderCoupon sc = saleorderCouponMapper.selectBySaleorderId(saleorder.getSaleorderId());
            // 存在优惠券
            if (null != sc) {
                // 设置优惠券信息
                sd.setCouponType(sc.getCouponType());
                sd.setCouponAmount(sc.getDenomination());
            }
        }
        // add by franlin.wu for[耗材商城订单管理新增耗材订单类型] at 2018-12-06 end
        // 查询订单产生的售后单数
        int cnt = saleorderMapper.getAfterSaleCnt(saleorderId);
        if (cnt > 0) {
            sd.setIsAftersale(1);
        }
        sd.setAccountPayable(saleorderDataService.getPeriodAmount(sd.getSaleorderId()));
        sd.setPaymentAmount(saleorderDataService.getPaymentAmount(sd.getSaleorderId()));
        // 操作类型为订单详情的时候，查询开票条件（未锁定+已生效：开票申请：
        // 钱全部收，对应售后均关闭或无售后，客户全部收货，退货金额+已开票金额小于订单金额）
        if (sd.getValidStatus() == 1 && sd.getStatus() != 3  && sd.getLockedStatus() == 0) {
            // 未锁定+已生效+未关闭
            // 根据BDERPDEV-270需求修改：2019-03-28
            // 实际已收款金额,对公+银行+（交易客户名称一致）
            BigDecimal orderPayAmount = saleorderDataService.getOpenInvoiceOrderAmount(saleorderId, sd.getTraderName());
            orderPayAmount = orderPayAmount == null ? (new BigDecimal(0)) : orderPayAmount;

            // 订单金额（总额-退款金额：包含退货、退款）
            BigDecimal realAmount = saleorderDataService.getRealAmount(saleorderId);
            realAmount = realAmount == null ? (new BigDecimal(0)) : realAmount;
            boolean b = true;
            if (realAmount.compareTo(new BigDecimal(0)) == 0) {
                // 全部被售后
                // 全部被售后，不允许开票
                sd.setIsOpenInvoice(0);
                b = false;
            }
            // add by Tomcat.Hui 2019/11/25 10:49 .Desc: VDERP-1325 订单款项是否满足开票条件. start
            /**
             * 当该订单在申请时满足条件“（1）该订单全部收款（2）对公收款的金额（且交易名称需要跟合同名称一致）≥订单实际金额”时，则显示“注：订单款项已满足开票条件”，字体颜色为黑；
             * 若不满足条件则显示“注：订单款项未满足开票条件”，且文字标红。
             * */
            if (orderPayAmount.compareTo(realAmount) >= 0	) {
                sd.setMeetInvoiceConditions(1);
            }
            // add by Tomcat.Hui 2019/11/25 10:49 .Desc: VDERP-1325 订单款项是否满足开票条件. end

            if (b) {
                /*
                 * //订单实际金额小于等于订单收款金额（允许开票申请）
                 * if(realAmount.compareTo(orderPayAmount.add(
                 * orderPeriodAmount)) <= 0 &&
                 * sd.getArrivalStatus().equals(2)){//结果是:-1 小于,0 等于,1
                 * 大于-----客户全部收货
                 */
                // 订单实际对公(销售)已付款金额（包含账期支付）大于等于 订单实际金额（允许开票申请）
                if (orderPayAmount.compareTo(realAmount) >= 0 && sd.getArrivalStatus().equals(2)) {// 结果是:-1
                    // 小于,0
                    // 等于,1
                    // 大于-----客户全部收货
                    // 验证销售单未完成的售后单
                    Integer afterNum = invoiceMapper.getSaleOrderAfterStatus(sd.getCompanyId(), saleorderId, 2);
                    // 对应售后关闭
                    if (afterNum != null && afterNum.equals(0)) {
                        b = false;// 到此，不允许提前开票
                        // 查询是否有开票申请记录
                        Map<String, Integer> map = invoiceMapper.getOrderOpenInvoiceApply(saleorderId);
                        if (Integer.valueOf(map.get("NUM_WAIT") + "") > 0) {// 存在待审核的--不允许再次申请
                            logger.info("开票按钮2：{}-{}-realAmount{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice(),realAmount);
                            sd.setIsOpenInvoice(2);// 不允许开票
                        } else if (Integer.valueOf(map.get("NUM_PASS") + "") >= 0) {// 存在审核通过的记录--验证已开票金额
                            BigDecimal amount = new BigDecimal(0);
                            // 根据订单号查询已开票金额
                            Invoice invoice = invoiceMapper.getSaleInvoiceAmount(saleorderId);
                            // BigDecimal amount =
                            // invoiceMapper.getInvoiceRecord(saleorderId);//根据销售单号查询售后退货总金额
                            if (null == invoice || null == invoice.getAmount()) {
                                amount = new BigDecimal(0);
                            } else {
                                amount = invoice.getAmount();
                            }
                            if (amount.doubleValue() < realAmount.doubleValue()) {// 已开票金额小于订单实际收款金额
                                logger.info("开票按钮1：{}-{}-amount{}-realAmount{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice(),amount,realAmount);
                                sd.setIsOpenInvoice(1);// 允许开票
                            } else {// 全部开票
                                logger.info("开票按钮5：{}-{}-amount{}-realAmount{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice(),amount,realAmount);
                                sd.setIsOpenInvoice(5);// 不允许开票
                            }
                        } else {// 不存在审核通过的记录（未开过票）
                            logger.info("开票按钮1：{}{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice());
                            sd.setIsOpenInvoice(1);// 允许开票（还未开票）
                        }
                    }
                }
            }
            // 允许提前开票申请
            if (b && null == sd.getIsOpenInvoice()) {
                // 钱未付完全-或存在售后未关闭-或客户未全部收货
                // 查询是否有提前开票申请记录
                Map<String, Integer> map = invoiceMapper.getOrderOpenInvoiceApply(saleorderId);
                if (Integer.valueOf(map.get("NUM_WAIT") + "") > 0) {// 存在待审核的--不允许再次申请
                    logger.info("提前开票按钮4：{}-{} -realAmount{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice(),realAmount);

                    sd.setIsOpenInvoice(4);// 不允许开票
                } else if (Integer.valueOf(map.get("NUM_PASS") + "") >= 0) {// 存在审核通过的记录--验证已开票金额
                    BigDecimal amount = new BigDecimal(0);
                    // 根据订单号查询已开票金额
                    Invoice invoice = invoiceMapper.getSaleInvoiceAmount(saleorderId);
                    if (null == invoice || null == invoice.getAmount()) {
                        amount = new BigDecimal(0);
                    } else {
                        amount = invoice.getAmount();
                    }
                    if (amount.doubleValue() < realAmount.doubleValue()) {// 已开票金额小于订单实际收款金额
                        logger.info("提前开票按钮3：{}-{} -realAmount{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice(),realAmount);

                        sd.setIsOpenInvoice(3);// 允许开票
                    } else {// 全部开票
                        logger.info("提前开票按钮5：{}-{} -realAmount{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice(),realAmount);

                        sd.setIsOpenInvoice(5);// 不允许开票
                    }
                } else {// 不存在审核通过的记录（未开过票）
                    logger.info("提前开票按钮33：{}-{} -realAmount{}",saleorder.getSaleorderNo(),  saleorder.getIsOpenInvoice(),realAmount);

                    sd.setIsOpenInvoice(3);// 允许开票
                }
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(saleorder.getOptType()) && saleorder.getOptType().equals("modifyApplyInit")) {
            Map<String, Integer> map = invoiceMapper.getOrderOpenInvoiceApply(saleorderId);
            BigDecimal amount = new BigDecimal(0);
            // 根据订单号查询已开票金额
            Invoice invoice = invoiceMapper.getSaleInvoiceAmount(saleorderId);
            if (null == invoice || null == invoice.getAmount()) {
                amount = new BigDecimal(0);
            } else {
                amount = invoice.getAmount();
            }
            if (amount.compareTo(BigDecimal.ZERO) == 1){
                sd.setIsHaveInvoice(1);
            }else {
                sd.setIsHaveInvoice(0);
            }
            //查询发票寄送状态
            if(sd.getIsHaveInvoice().equals(ErpConst.ONE)){
                //有开票的情况下查询发票寄送信息
                List<Invoice> saleInvoices = invoiceMapper.getInvoiceListBySaleorderId(sd.getSaleorderId());
                //是否部分寄送
                Integer isPartDelive = 0;
                if(ErpConst.TWO.equals(sd.getInvoiceStatus())){
                    //全部开票
                    for(Invoice saleInvoice : saleInvoices){
                        //查询发票的快递信息
                        List<ExpressDetail> expressDetails = expressDetailMapper.queryByRelateAndType(saleInvoice.getInvoiceId(),SysOptionConstant.ID_497);
                        if(CollectionUtils.isEmpty(expressDetails)){
                            //为空则为未寄送
                            isPartDelive = 1;
                        }
                    }
                    mv.addObject("isPartDelive",isPartDelive);
                }else {
                    //未全部开票也算作未全部寄送的情况
                    isPartDelive = 1;
                    mv.addObject("isPartDelive",isPartDelive);
                }
            }
            // 没有申请中的开票申请
            if (Integer.valueOf(map.get("NUM_WAIT") + "") > 0 ) {
                sd.setIsHaveInvoiceApply(1);
            }else if (Integer.valueOf(map.get("NUM_PASS") + "") >= 0){
                sd.setIsHaveInvoiceApply(2);
            } else {
                sd.setIsHaveInvoiceApply(0);
            }
        }
        return sd;
    }

    // status: 0待确认（默认）、1进行中、2已完结、3已关闭
    // PAYMENT_STATUS:  0未付款 1部分付款 2全部付款
    // DELIVERY_STATUS: 0未发货 1部分发货 2全部发货
    // ARRIVAL_STATUS: 0未收货 1部分收货 2全部收货
    // INVOICE_STATUS: 0未开票 1部分开票 2全部开票
    // VERIFY_STATUS: 3待提交、0审核中、1审核通过、2审核不通过
    /**
     * 0待确认：新增、报价转订单且未提交审核的订单；
     * 1待审核：提交审核的订单；子节点：审核中、审核通过、审核不通过
     * 2待收款：审核通过已生效的订单；子节点：未收款、部分收款、全部收款
     * 3待发货：全部收款的订单；子节点：未发货、部分发货、全部发货
     * 4待收货：已发货（含：部分发货、全部发货）的订单，即有物流信息但快递状态“未收货”“部分收货”的订单；子节点：未收货、部分收货、全部收货
     * 5待开票：全部收货或提前开票（通过）的订单；子节点：未开票、部分开票、全部开票
     * 6已完结：票：全部开票（*寄送）+货：全部收货+款：全部收款（未还账期=0），订单完结
     * 7已关闭：已关闭订单
     */
    public SubStatusEnum calSubStatusById(Integer saleOrderId){
        Saleorder saleorder=getSaleOrderBaseInfo(saleOrderId);
        SubStatusEnum resultSubStatus=SubStatusEnum.TO_BE_CONFIRMED;
        if ("0".equals(saleorder.getStatus()+"")  && "3".equals(saleorder.getVerifyStatus()+"")) {
            resultSubStatus=SubStatusEnum.TO_BE_CONFIRMED;
        }
        if ("0".equals(saleorder.getVerifyStatus()+"")) {
           resultSubStatus=SubStatusEnum.TO_AUDIT;
        }
        if ("1".equals(saleorder.getVerifyStatus()+"") && "1".equals(saleorder.getValidStatus()+"")) {
           resultSubStatus=SubStatusEnum.TO_COLLECTION;
        }
        if ("1".equals(saleorder.getStatus()+"") && "2".equals(saleorder.getPaymentStatus()+"")) {
           resultSubStatus=SubStatusEnum.TO_SEND_GOODS;
        }
        if ("1".equals(saleorder.getStatus()+"") && !"0".equals(saleorder.getDeliveryStatus()+"") && !"2".equals(saleorder.getArrivalStatus()+"")) {
           resultSubStatus=SubStatusEnum.TO_THE_GOODS;
        }
        if ("1".equals(saleorder.getStatus()+"") && ("2".equals(saleorder.getArrivalStatus()+"") || ("1".equals(saleorder.getIsAdvance()+"") && "1".equals(saleorder.getAdvanceValidStatus()+"")))){
           resultSubStatus=SubStatusEnum.TO_MAKE_OUT_INVOICE;
        }
        if ("2".equals(saleorder.getStatus()+"")){
           resultSubStatus=SubStatusEnum.FINISHED;
        }
        if ("3".equals(saleorder.getStatus()+"")) {
           resultSubStatus=SubStatusEnum.CLOSE;
        }
        return resultSubStatus;
    }

    /**
     * 通过采购订单id获取订单详情
     * @param buyOrderId
     */
    public Buyorder getBuyOrderInfoByBuyOrderId(Integer buyOrderId){
        return buyorderMapper.selectByPrimaryKey(buyOrderId);
    }

    /**
     * 采购单id获取对应销售单商品ID
     * @param buyOrderId
     * @return
     */
    public List<RBuyorderSaleorder> getRBuyorderSaleorderListByParam(Integer buyOrderId){
        return rBuyorderSaleorderMapper.getRBuyorderSaleorderListByParam(buyOrderId);
    }




    public ResultInfo saveSaleorderGoods(SaleorderGoods saleorderGoods) {
        Integer saleorderGoodsId = saleorderGoods.getSaleorderGoodsId();
        //一个订单下相同商品只允许一个为非赠品一个为赠品
        Saleorder sOrder = new Saleorder();
        sOrder.setSaleorderId(saleorderGoods.getSaleorderId());
        List<SaleorderGoods> goodsList = saleorderGoodsMapper.selectSaleorderGoodsListBySaleorderId(sOrder);
        for (SaleorderGoods good : goodsList) {
            if (good.getGoodsId().equals(saleorderGoods.getGoodsId())){
                if (good.getPrice().compareTo(new BigDecimal("0")) == 0 && saleorderGoods.getPrice().compareTo(new BigDecimal("0")) == 0){
                    return new ResultInfo(-1, "已有相同订货号的赠品，不允许重复添加！");
                }
                if (!(good.getPrice().compareTo(new BigDecimal("0")) == 0) && !(saleorderGoods.getPrice().compareTo(new BigDecimal("0")) == 0)){
                    return new ResultInfo(-1, "已有相同订货号的产品，不允许重复添加！");
                }
            }
        }
        int i;

        Saleorder sal = saleorderMapper.getSaleorderInfoById(saleorderGoods.getSaleorderId());

        if (saleorderGoodsId != null) {
            SaleorderGoods goods = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoodsId);

            boolean  hcOrderPriceChanged= "0".equals(goods.getIsCoupons()) && sal!=null && (5==sal.getOrderType() || 1==sal.getOrderType()) && saleorderGoods.getPrice().compareTo(goods.getPrice())!=0;
            if(hcOrderPriceChanged){
                logger.warn("修改订单的总价：+"+goods.getIsCoupons()+sal.getSaleorderNo()+"" +
                        "\t"+saleorderGoods.getPrice()+"\t"+goods.getPrice());
                if(saleorderGoods.getNum()!=null) {
                    saleorderGoods.setMaxSkuRefundAmount(saleorderGoods.getPrice().multiply(new BigDecimal(saleorderGoods.getNum())));
                }
            }
            if (saleorderGoods.getPrice() != null && saleorderGoods.getPrice().compareTo(new BigDecimal("0")) == 0){
                saleorderGoods.setIsGift(1);
            }
            if (saleorderGoods.getPrice() != null && saleorderGoods.getPrice().compareTo(new BigDecimal("0")) != 0){
                saleorderGoods.setIsGift(0);
            }
            i = saleorderGoodsMapper.updateByPrimaryKeySelective(saleorderGoods);


        } else {
            if (saleorderGoods.getPrice() != null && saleorderGoods.getPrice().compareTo(new BigDecimal("0")) == 0){
                saleorderGoods.setIsGift(1);
            }
            i = saleorderGoodsMapper.insertSelective(saleorderGoods);
        }

        //更新商品快照信息
        saleorderGoodsMapper.updateSaleorderGoodsSnapshotInfo(saleorderGoods);
          // 报价相关
//        syncSaleorderGoods2Quoteorder(saleorderGoods.getSaleorderId());

        if (i == 1) {
            // 修改终端信息
//            saleorderGoodsMapper.updateSaleTerminalInfo(saleorderGoods);
            // VDERP-15595
//            OrderTerminalContext orderTerminalContext = new OrderTerminalContext();
//            orderTerminalContext.setOrderTerminalStrategy(saleOrderTerminalStrategy);
//            OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
//            orderTerminalDto.setBusinessType(ErpConst.ZERO);
//            orderTerminalDto.setBusinessId(saleorderGoods.getSaleorderId());
//            orderTerminalDto.setBusinessNo(Objects.nonNull(sal) ? sal.getSaleorderNo() : "");
//            orderTerminalDto.setTerminalName(saleorderGoods.getTerminalTraderName());
//            orderTerminalDto.setDwhTerminalId(saleorderGoods.getDwhTerminalId());
//            orderTerminalDto.setUnifiedSocialCreditIdentifier(saleorderGoods.getUnifiedSocialCreditIdentifier());
//            orderTerminalDto.setOrganizationCode(saleorderGoods.getOrganizationCode());
//            orderTerminalDto.setProvinceId(saleorderGoods.getProvinceId());
//            orderTerminalDto.setProvinceName(saleorderGoods.getProvinceName());
//            orderTerminalDto.setCityId(saleorderGoods.getCityId());
//            orderTerminalDto.setCityName(saleorderGoods.getCityName());
//            orderTerminalDto.setAreaId(saleorderGoods.getAreaId());
//            orderTerminalDto.setAreaName(saleorderGoods.getAreaName());
//            orderTerminalContext.executeStrategy(orderTerminalDto);

            // 验证销售订单商品是否含有直发
            Saleorder saleorder = new Saleorder();
            int n = saleorderGoodsMapper.vailSaleorderIsDirect(saleorderGoods.getSaleorderId());
            saleorder.setSaleorderId(saleorderGoods.getSaleorderId());
            if (n > 0) {
                saleorder.setDeliveryDirect(1);
            } else {
                saleorder.setDeliveryDirect(0);
            }
            saleorderMapper.updateByPrimaryKeySelective(saleorder);
            sal = saleorderMapper.getSaleorderInfoById(saleorder.getSaleorderId());

            if(sal.getOrderType()==1 && saleorderGoodsId!=null) {
                SaleorderGoods goods = saleorderGoodsMapper.getSaleorderGoodsInfoById(saleorderGoodsId);
                QuoteorderGoods qg = new QuoteorderGoods();
                qg.setPrice(saleorderGoods.getPrice());
                qg.setNum(saleorderGoods.getNum());
                qg.setDeliveryDirect(saleorderGoods.getDeliveryDirect());
                qg.setDeliveryDirectComments(saleorderGoods.getDeliveryDirectComments());
                qg.setGoodsComments(saleorderGoods.getGoodsComments());
                qg.setHaveInstallation(saleorderGoods.getHaveInstallation());
                qg.setGoodsId(goods.getGoodsId());
                qg.setQuoteorderId(sal.getQuoteorderId());
                int l = quoteGoodsMapper.updateQuoteGoodsInfo(qg);
            }
            // 单价和数据均大于零时，并且新的产品金额不等于原来的--修改付款方式
            Integer k = saleorderGoodsMapper.vailSaleorderTotle(saleorderGoods);
            if (k > 0) {
                BigDecimal price = saleorderGoods.getPrice() == null ? (new BigDecimal(0)) : saleorderGoods.getPrice();
                if (price.doubleValue() >= 0 && saleorderGoods.getNum() >= 0) {
                    // 修改主表中总金额
                    saleorderGoods.setUpdateTotalAmoutType(updateTotalAmoutType(sal));
                    int m = saleorderGoodsMapper.updateSaleorderTotalNew(saleorderGoods);
                    if (m > 0) {
                        return new ResultInfo(0, "操作成功");
                    }
                }
            }
            return new ResultInfo(0, "操作成功");
        }

        return new ResultInfo();
    }

    /**
     * 更新总金额的方式
     * 1.原来的方式 单价*数量
     * 2.使用优惠券的方式 最大退款金额的相加
     * @param saleorder
     * @return
     */
    private Integer updateTotalAmoutType(Saleorder saleorder) {

        int updateTotalAmoutType = 1;

        //如果是使用了优惠券的BD订单
        if(saleorder.getOrderType() == Constants.BD_ORDER && saleorder.getIsCoupons() == 1){
            updateTotalAmoutType = 2;
        }

        //如果是使用了优惠券的HC订单
        if(saleorder.getOrderType().equals(Constants.HC_ORDER) && saleorder.getIsCoupons() == 1){
            updateTotalAmoutType = 2;
        }

        return updateTotalAmoutType;
    }

    private void syncSaleorderGoods2Quoteorder(Integer saleorderId){
        //同步订单新增商品到报价单
        Integer quoteorderId = saleorderMapper.getSaleOrderById(saleorderId).getQuoteorderId();
        if (quoteorderId == null || quoteorderId == 0){
            return;
        }
        Map<Integer,Integer> quoteOrderGoodsMap = new HashMap<>();
        List<QuoteorderGoods> quoteorderGoodsList = quoteGoodsMapper.getAllQuoteOrderGoods(quoteorderId);
        if (quoteorderGoodsList.size() > 0){
            for (QuoteorderGoods item : quoteorderGoodsList){
                if (!quoteOrderGoodsMap.containsKey(item.getGoodsId())){
                    quoteOrderGoodsMap.put(item.getGoodsId(),item.getIsDelete());
                } else {
                    if (item.getIsDelete() == 0){
                        quoteOrderGoodsMap.put(item.getGoodsId(),0);
                    }
                }
            }
        }
        List<QuoteorderGoods> toAddQuoteorderGoods = new ArrayList<>();
        List<Integer> toUpdateQuoteorderGoods = new ArrayList<>();
        saleorderGoodsMapper.getValidSaleorderGoodsBySaleorderId(saleorderId).forEach(
                item -> {
                    if (!quoteOrderGoodsMap.containsKey(item.getGoodsId())){
                        //新增
                        QuoteorderGoods var = new QuoteorderGoods();
                        BeanUtils.copyProperties(item,var);
                        var.setQuoteorderId(quoteorderId);
                        var.setIsTemp(0);
                        toAddQuoteorderGoods.add(var);
                    } else if (quoteOrderGoodsMap.containsKey(item.getGoodsId()) && quoteOrderGoodsMap.get(item.getGoodsId()) == 1){
                        //更新
                        toUpdateQuoteorderGoods.add(item.getGoodsId());
                    }
                }
        );
        if (toUpdateQuoteorderGoods.size() > 0){
            quoteGoodsMapper.updateQuoteorderGoods2Valid(toUpdateQuoteorderGoods,quoteorderId);
        }
        toAddQuoteorderGoods.forEach(
                item -> quoteGoodsMapper.insertQuoteGoods(item)
        );
    }


    public SaleorderModifyApply modifyApplySave(SaleorderModifyApply saleorderModifyApply,User user){
        //wms出库相关操作
        operateWms(saleorderModifyApply,user);
        //保存修改信息
        saveSaleOrderModifyApply(saleorderModifyApply);
        return null;
    }

    public void operateWms(SaleorderModifyApply saleorderModifyApply,User user){
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderModifyApply.getSaleorderId());
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
        saleorder = saleorderMapper.getSaleOrderById(saleorderModifyApply.getSaleorderId());
        //是否修改了发货信息和直发状态
        boolean isSendWmsflag = saleorderService.isSendWMS(saleorderModifyApply,saleorder,saleorderGoods);
        boolean isModifyOrderOutput = saleorderService.isModifyOrderOutput(saleorderModifyApply,saleorder);
        if(isSendWmsflag){
            try {

                // flag true 取消成功 false 取消失败
                boolean flag = cancelTypeService.cancelOutSaleOutMethod(saleorder.getSaleorderNo(), CancelReasonConstant.EDIT_ORDER);
                if(!flag){
//                    request.setAttribute("cancel","fail");
                    saleorderModifyApply.setCancelStatus(ErpConst.ZERO);
                }else {
                    saleorderModifyApply.setIsWmsCancel(1);
                }
                logger.info("ERP取消出库单至WMS的 单号:{}, 响应:{}",saleorder.getSaleorderNo(), flag);
            } catch (Exception e) {
                logger.error("ERP取消出库单至WMS的请求接口报错", e);
            }
        } else if(isModifyOrderOutput){
            try {
                if(!logicalSaleorderChooseServiceImpl.modifyOrderOutput(saleorder.getSaleorderId(),saleorderModifyApply,user)){
//                    request.setAttribute("cancel","fail");
                    saleorderModifyApply.setCancelStatus(ErpConst.ZERO);
                    logger.info("ERP修改销售单信息失败，WMS拒绝修改");
                }
                logger.info("ERP修改销售单信息成功");
            } catch (Exception e) {
                logger.error("ERP取消出库单至WMS的请求接口报错", e);
            }
        }
    }


    public SaleorderModifyApply saveSaleOrderModifyApply(SaleorderModifyApply saleorderModifyApply) {
        // 获取原订单主信息
        Saleorder saleorderInfo = saleorderMapper.selectByPrimaryKey(saleorderModifyApply.getSaleorderId());

        // 新增主信息
        saleorderModifyApply.setOldTakeTraderContactId(saleorderInfo.getTakeTraderContactId());
        saleorderModifyApply.setOldTakeTraderContactName(saleorderInfo.getTakeTraderContactName());
        saleorderModifyApply.setOldTakeTraderContactMobile(saleorderInfo.getTakeTraderContactMobile());
        saleorderModifyApply.setOldTakeTraderContactTelephone(saleorderInfo.getTakeTraderContactTelephone());
        saleorderModifyApply.setOldTakeTraderAddressId(saleorderInfo.getTakeTraderAddressId());
        saleorderModifyApply.setOldTakeTraderArea(saleorderInfo.getTakeTraderArea());
        saleorderModifyApply.setOldTakeTraderAddress(saleorderInfo.getTakeTraderAddress());
        saleorderModifyApply.setOldLogisticsComments(saleorderInfo.getLogisticsComments());
        saleorderModifyApply.setOldInvoiceTraderContactId(saleorderInfo.getInvoiceTraderContactId());
        saleorderModifyApply.setOldInvoiceTraderContactName(saleorderInfo.getInvoiceTraderContactName());
        saleorderModifyApply.setOldInvoiceTraderContactMobile(saleorderInfo.getInvoiceTraderContactMobile());
        saleorderModifyApply.setOldInvoiceTraderContactTelephone(saleorderInfo.getInvoiceTraderContactTelephone());
        saleorderModifyApply.setOldInvoiceTraderAddressId(saleorderInfo.getInvoiceTraderAddressId());
        saleorderModifyApply.setOldInvoiceTraderArea(saleorderInfo.getInvoiceTraderArea());
        saleorderModifyApply.setOldInvoiceTraderAddress(saleorderInfo.getInvoiceTraderAddress());
        saleorderModifyApply.setOldInvoiceComments(saleorderInfo.getInvoiceComments());
        saleorderModifyApply.setOldInvoiceType(saleorderInfo.getInvoiceType());
        saleorderModifyApply.setOldIsSendInvoice(saleorderInfo.getIsSendInvoice());
        saleorderModifyApply.setOldIsDelayInvoice(saleorderInfo.getIsDelayInvoice());
        saleorderModifyApply.setOldInvoiceMethod(saleorderInfo.getInvoiceMethod());

        saleorderModifyApply.setOldDeliveryMethod(saleorderInfo.getDeliveryMethod());
        saleorderModifyApply.setOldDeliveryType(saleorderInfo.getDeliveryType());

        saleorderModifyApply.setOldDeliveryClaim(saleorderInfo.getDeliveryClaim());
        saleorderModifyApply.setOldDeliveryDelayTime(saleorderInfo.getDeliveryDelayTime());

        Integer i = saleorderModifyApplyMapper.insertSelective(saleorderModifyApply);
        if (i == 1) {
            Integer saleorderModifyApplyId = saleorderModifyApply.getSaleorderModifyApplyId();
            // 申请修改单号
            SaleorderModifyApply saleorderExtra = new SaleorderModifyApply();
            saleorderExtra.setSaleorderModifyApplyId(saleorderModifyApplyId);
            saleorderExtra.setSaleorderModifyApplyNo(orderNoDict.getOrderNum(saleorderModifyApplyId, 12));
            saleorderModifyApply.setSaleorderModifyApplyNo(saleorderExtra.getSaleorderModifyApplyNo());
            saleorderModifyApplyMapper.updateByPrimaryKeySelective(saleorderExtra);

            List<Integer> saleorderGoodsIdList = new ArrayList<>();
            if (null != saleorderModifyApply.getGoodsList()) {
                List<SaleorderModifyApplyGoods> list = saleorderModifyApply.getGoodsList();
                for (SaleorderModifyApplyGoods saleorderGoods : list) {
                    SaleorderModifyApplyGoods newGoods = new SaleorderModifyApplyGoods();
                    newGoods.setSaleorderModifyApplyId(saleorderModifyApplyId);
                    newGoods.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
                    newGoods.setDeliveryDirect(saleorderGoods.getDeliveryDirect());
                    newGoods.setDeliveryDirectComments(saleorderGoods.getDeliveryDirectComments());
                    newGoods.setGoodsComments(saleorderGoods.getGoodsComments());
                    newGoods.setOldDeliveryDirect(saleorderGoods.getOldDeliveryDirect());
                    newGoods.setOldDeliveryDirectComments(saleorderGoods.getOldDeliveryDirectComments());
                    newGoods.setOldGoodsComments(saleorderGoods.getOldGoodsComments());
                    saleorderModifyApplyGoodsMapper.insertSelective(newGoods);
                    if(saleorderGoods.getDeliveryDirect() != saleorderGoods.getOldDeliveryDirect()){
                        saleorderGoodsIdList.add(saleorderGoods.getSaleorderGoodsId());
                    }
                    if (Constants.ONE.equals(saleorderModifyApply.getIsNew()) && org.apache.commons.lang3.StringUtils.isNoneBlank(saleorderGoods.getLabelData())){
                        //保存内部备注
                        RemarkComponentDto remarkComponentDto  = JSON.parseObject(saleorderGoods.getLabelData(),RemarkComponentDto.class);
                        remarkComponentDto.getLabelQuery().setRelationId(saleorderModifyApplyId);
                        saveInsideComments(remarkComponentDto);
                    }
                }
            }
            saleorderModifyApply.setSaleorderModifyApplyId(saleorderModifyApplyId);

            // 对应销售订单需要锁定
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleorderModifyApply.getSaleorderId());
            saleorder.setLockedStatus(1);
            int x = saleorderMapper.updateByPrimaryKeySelective(saleorder);
            updateLockSaleorderWarning(saleorderModifyApply.getSaleorderId());
            return saleorderModifyApply;
        } else {
            return null;
        }
    }

    public void updateLockSaleorderWarning(Integer saleorderId) {
        try{
            List<SaleorderGoods> saleorderGoodsLists = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(saleorderGoodsLists)){
                for (SaleorderGoods sg : saleorderGoodsLists) {
                    SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
                    saleorderGoodsVo.setSaleorderGoodsId(sg.getSaleorderGoodsId());
                    saleorderGoodsVo.setWarnLevel(null);
                    saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
                }
            }
        }catch(Exception e){
            logger.error("锁定销售单时，清除预警失败，销售单ID：{},错误原因：{}",saleorderId,e);
        }
    }

    public void updateUnlockSaleOrderWarning(Integer saleorderId) {
        try{
            long nowTime = DateUtil.gainNowDate();
            List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(saleorderGoodsList)){
                for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
                    if (saleorderGoods.getAgingTime() != null && saleorderGoods.getAgingTime() > 0) {
                        SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
                        saleorderGoodsVo.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
                        saleorderGoodsVo.setWarnLevel(null);
                        saleorderGoodsVo.setAging(0);
                        saleorderGoodsVo.setAgingTime(nowTime);
                        saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
                    }
                }
            }
        }catch (Exception e){
            logger.error("解锁时,更新销售单预警状态失败，销售单id:{},错误：{}",saleorderId,e);
        }
    }

    public void saveInsideComments(RemarkComponentDto labelDataDto) {
        ResultInfo<?> resultInfo = new ResultInfo<>(0, "操作成功");
        LabelQuery labelQuery = labelDataDto.getLabelQuery();
        List<LabelQueryDto> queryDtoList = labelDataDto.getData();
        // 根据格式 编写默认值方法
        if (org.apache.commons.collections.CollectionUtils.isEmpty(queryDtoList) && labelQuery != null) {
            if(labelQuery.getRelationId() != null && StringUtil.isNotEmpty(labelQuery.getSkuNo()) && labelQuery.getScene() != null){
                // 默认值处理 预先判断是否已经存在关联关系
                List<RemarkComponent> remarkComponentList = remarkComponentMapper.selectComponentRelationList(labelQuery);
                // 默认值类型 1采购要求, 2供应商要求, 5 专项发货
//				ArrayList<Integer> typeList = new ArrayList<>(); typeList.add(1);typeList.add(2);typeList.add(5);
                ArrayList<Integer> typeList = remarkComponentMapper.selectType();

                typeList.stream().forEach(typeItem->{
                    long countOfTypeOne = remarkComponentList.stream()
                            .filter(item -> typeItem.equals(item.getType()) && !Integer.valueOf(0).equals(item.getParentId()))
                            .count();
                    if(countOfTypeOne == 0L){
                        ComponentRelation componentRelation = ComponentRelation
                                .builder()
                                .scene(labelQuery.getScene())
                                .relationId(labelQuery.getRelationId())
                                .skuNo(labelQuery.getSkuNo())
                                .skuName(labelQuery.getSkuName())
                                .time(0L)
                                .build();
                        if(Integer.valueOf(1).equals(typeItem)){
                            componentRelation.setComponentId(2);
                            remarkComponentMapper.saveComponentRelation(componentRelation);
                            if(RemarkComponentSceneEnum.SALE_ORDER.getCode().equals(labelQuery.getScene())){
                                saleorderMapper.updateSaleorderRemarkComponent(componentRelation);
                            }
                        }
                        if(Integer.valueOf(2).equals(typeItem)){
                            componentRelation.setComponentId(5);
                            remarkComponentMapper.saveComponentRelation(componentRelation);
                        }
                        if(Integer.valueOf(5).equals(typeItem)){
                            componentRelation.setComponentId(17);
                            remarkComponentMapper.saveComponentRelation(componentRelation);
                        }
                    }
                });
            }
        }
        for (LabelQueryDto queryDto : queryDtoList) {
            List<LabelQueryDto> childList = queryDto.getChild();
            if (org.apache.commons.collections.CollectionUtils.isEmpty(childList)) {
                continue;
            }
            for (LabelQueryDto child : childList) {
                if (!child.getSelected()) {
                    continue;
                }
                List<SkuVo> skuVoList = child.getSkuList();
                if (org.apache.commons.collections.CollectionUtils.isEmpty(skuVoList)) {
                    continue;
                }
                for (SkuVo sku : skuVoList) {
                    if (!sku.getChoose()) {
                        continue;
                    }
                    ComponentRelation componentRelation = new ComponentRelation();
                    componentRelation.setScene(labelQuery.getScene());
                    componentRelation.setComponentId(child.getId());
                    componentRelation.setRelationId(labelQuery.getRelationId());
                    componentRelation.setSkuNo(sku.getSkuNo());
                    componentRelation.setSkuName(sku.getSkuName());
                    componentRelation.setTime(DateUtil.convertLong(child.getDate(), "yyyy-MM-dd"));
                    componentRelation.setReason(child.getReason());
                    remarkComponentMapper.saveComponentRelation(componentRelation);
                    if (child.getParentId() == 1 && !labelQuery.getScene().equals(RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode())) {
                        // 更新订单产品明细表的采购要求
                        saleorderMapper.updateSaleorderRemarkComponent(componentRelation);
                    }
                    // 维护销售单SKU中专向发货标记字段 (专向发货 - 是)
                    if(child.getId() == 16 && !labelQuery.getScene().equals(RemarkComponentSceneEnum.BUSINESS_CHANCE.getCode())){
                        saleorderMapper.updateSaleOrderFlagRemarkComponent(componentRelation);
                    }
                }
            }
        }
    }


    public List<SaleOrderProcessNode> getSaleOrderProcessNodeList(ModelAndView mv,Integer saleOrderId){
        List<SaleOrderProcessNode> processNodeList = new ArrayList<>();
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        OrderStatusEnum currentOrderStatus = this.getMainProcessNodeOfSaleOrder(saleOrderId);
        if(OrderStatusEnum.TO_BE_INVOICED.equals(currentOrderStatus)){
            //待开票节点下---票货同行与wms交互失败的情况需要重新展示开票按钮
            mv.addObject("isShowInvoiceBtn",ErpConst.ONE);
        }
        OrderStatusEnum[] orderStatusEnumValues = OrderStatusEnum.values();
        for (OrderStatusEnum item : orderStatusEnumValues) {
            SaleOrderProcessNode node = new SaleOrderProcessNode();
            node.setLabel(item.getDesc());
            if (item.getStatus() < currentOrderStatus.getStatus()) {
                node.setStatus(1);
            } else if (item.getStatus().equals(currentOrderStatus.getStatus())) {
                node.setStatus(2);
            } else {
                node.setStatus(0);
            }

            processNodeList.add(node);
        }

        if(SalesOrderTypeEnum.JCF.getCode().equals(saleorder.getOrderType())){
            //JCF订单需要校验
            if(ErpConst.FOUR.equals(saleorder.getStatus())){
                //标准展示待客户确认
                processNodeList.get(0).setTip("待客户确认");
            }else {
                processNodeList.get(0).setTip("待确认");
            }
        }
        String[] prefixOfNodeTips = new String[]{"未","部分","全部"};
        if (saleorder.getPaymentStatus() > 0) {
            if (currentOrderStatus.getStatus() < OrderStatusEnum.TO_BE_PAYMENT.getStatus()){
                processNodeList.get(2).setStatus(3);
            }
        }
        if (processNodeList.get(2).getStatus() != 0) {
            processNodeList.get(2).setTip(prefixOfNodeTips[saleorder.getPaymentStatus()] + "收款");
        }
        if (saleorder.getDeliveryStatus() > 0) {
            if (currentOrderStatus.getStatus() < OrderStatusEnum.TO_BE_DELIVERED.getStatus()){
                processNodeList.get(3).setStatus(3);
            }
        }
        if (processNodeList.get(3).getStatus() != 0) {
            processNodeList.get(3).setTip(prefixOfNodeTips[saleorder.getDeliveryStatus()] + "发货");
        }
        if (saleorder.getArrivalStatus() > 0) {
            if (currentOrderStatus.getStatus() < OrderStatusEnum.TO_BE_RECEIVED.getStatus()){
                processNodeList.get(4).setStatus(3);
            }
        }
        if (processNodeList.get(4).getStatus() != 0) {
            processNodeList.get(4).setTip(prefixOfNodeTips[saleorder.getArrivalStatus()] + "收货");
        }
        if (saleorder.getInvoiceStatus() > 0 || getInvoiceInAdvance(saleorder)) {
            if (currentOrderStatus.getStatus() < OrderStatusEnum.TO_BE_INVOICED.getStatus()){
                processNodeList.get(5).setStatus(3);
            }
        }
        if (processNodeList.get(5).getStatus() != 0) {
            processNodeList.get(5).setTip(prefixOfNodeTips[saleorder.getInvoiceStatus()] + "开票");
        }

        int orderCheckStatus = getSaleorderCheckStatusFromVerifiesInfo("T_SALEORDER",saleOrderId,623,saleorder);

        //根据订单审核状态更新待审核节点的lock和tips
        String lock = null;
        if (orderCheckStatus == 0) {
            processNodeList.get(1).setTip("审核中");
            lock = "订单审核中";
        } else if (orderCheckStatus == 1){
            processNodeList.get(1).setTip("审核通过");
        } else if (orderCheckStatus == 2){
            processNodeList.get(1).setTip("审核不通过");
        }

        if (saleorder.getLockedStatus() == 1 && !StringUtils.isEmpty(saleorder.getLockedReason())){
            if ("订单审核".equals(saleorder.getLockedReason())){
                lock = "订单审核中";
            } else if (saleorder.getLockedReason().startsWith("售后")){
                lock = "订单售后中";
            } else if ("订单修改审核".equals(saleorder.getLockedReason())){
                lock = "订单修改中";
            }
        }
        if (!StringUtils.isEmpty(lock)){
            processNodeList.get(currentOrderStatus.getStatus()).setLock(lock);
        }

        if (saleorder.getStatus() == 3){
            processNodeList.remove(6);
        } else {
            processNodeList.remove(7);
        }
        mv.addObject("processNodeList",JSONArray.fromObject(processNodeList));
        return processNodeList;
    }

    /**
     * 提前开票(通过)
     * 来源DB
     */
    private Boolean getInvoiceInAdvance(Saleorder saleOrder) {
        try{
            boolean result = false;
            if(ErpConst.ONE.equals(saleOrder.getStatus())){
                if (( ErpConst.ZERO.equals(saleOrder.getLockedStatus()) && ErpConst.ONE.equals(saleOrder.getValidStatus())))
                {
                    Saleorder querySaleOrder = new Saleorder();
                    querySaleOrder.setSaleorderId(saleOrder.getSaleorderId());
                    querySaleOrder.setOptType("orderDetail");
                    Saleorder saleOrderInfo = saleorderService.getBaseSaleorderInfo(querySaleOrder);
                    if (ErpConst.FOUR.equals(saleOrderInfo.getIsOpenInvoice())){
                        result = true;
                    }
                }
            }
            return result;
        } catch (Exception e){
            log.error("【getInvoiceInAdvance】处理异常",e);
            return false;
        }
    }

    public OrderStatusEnum getMainProcessNodeOfSaleOrder(Integer saleOrderId){
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleorder == null) {
            return OrderStatusEnum.TO_BE_CONFIRMED;
        }
        int orderCheckStatus = getSaleorderCheckStatusFromVerifiesInfo("T_SALEORDER",saleOrderId,623,saleorder);
        int orderStatus = saleorder.getStatus();
        //订单进度当前进行中的主节点
        OrderStatusEnum mainProcessNode = OrderStatusEnum.TO_BE_CONFIRMED;
        if (orderStatus == 0 && orderCheckStatus >= 0){
            mainProcessNode = OrderStatusEnum.TO_BE_AUDITED;
        } else if (orderStatus == 1){
            List<OrderStatusNode> mainProcessNodeList = new ArrayList<>();
            mainProcessNodeList.add(new OrderStatusNode(OrderStatusEnum.TO_BE_PAYMENT,saleorder.getPaymentStatus()));
            mainProcessNodeList.add(new OrderStatusNode(OrderStatusEnum.TO_BE_DELIVERED,saleorder.getDeliveryStatus()));
            mainProcessNodeList.add(new OrderStatusNode(OrderStatusEnum.TO_BE_RECEIVED,saleorder.getArrivalStatus()));
            mainProcessNodeList.add(new OrderStatusNode(OrderStatusEnum.TO_BE_INVOICED,saleorder.getInvoiceStatus()));

            Iterator<OrderStatusNode> iterator = mainProcessNodeList.iterator();
            while (iterator.hasNext()){
                if (iterator.next().getStatus() == 2){
                    iterator.remove();
                } else {
                    break;
                }
            }
            // todo 代码上下效果一样
            for (OrderStatusNode node : mainProcessNodeList){
                if (node.getStatus() == 2){
                    mainProcessNodeList.remove(node);
                } else {
                    break;
                }
            }
            if (mainProcessNodeList.isEmpty()){
                mainProcessNode = OrderStatusEnum.TO_BE_INVOICED;
            } else {
                mainProcessNode = mainProcessNodeList.get(0).getOrderStatusEnum();
            }
        } else if (orderStatus == 2){
            mainProcessNode = OrderStatusEnum.FINISHED;
        } else if (orderStatus == 3){
            mainProcessNode = OrderStatusEnum.CLOSE;
        }
        return mainProcessNode;
    }


    /**
     * 从verifies_info表中查询订单审核流程的审核状态
     * @param relatedTable 表
     * @param relatedTableKey 表主键
     * @param type 审核类型
     * @return 审核状态
     */
    public Integer getSaleorderCheckStatusFromVerifiesInfo(String relatedTable, Integer relatedTableKey, Integer type, Saleorder saleorder){
        VerifiesInfo verifiesInfoQuery = new VerifiesInfo();
        verifiesInfoQuery.setRelateTable(relatedTable);
        verifiesInfoQuery.setRelateTableKey(relatedTableKey);
        verifiesInfoQuery.setVerifiesType(type);
        List<VerifiesInfo> verifiesInfosOfSaleorder = verifiesInfoMapper.getVerifiesInfo(verifiesInfoQuery);
        int orderCheckStatus = CollectionUtils.isEmpty(verifiesInfosOfSaleorder) ? -1 : verifiesInfosOfSaleorder.get(0).getStatus();
        if (saleorder.getIsRisk() == 1){
            if (CollectionUtils.isEmpty(verifiesInfosOfSaleorder)){
                orderCheckStatus = 0;
            }
            if (!CollectionUtils.isEmpty(verifiesInfosOfSaleorder) && verifiesInfosOfSaleorder.get(0).getStatus() == 2){
                //VDERP-10426 订单被风控、质量部解除风控、审核过程被驳回。此时要根据被风控的时间和审核状态更新的时间作对比，如果风控时间大于审核更新时间，则订单处于提交审核中；否则订单状态处理未提交审核。
                if (verifiesInfosOfSaleorder.get(0).getModTime() < saleorder.getRiskTime()){
                    orderCheckStatus = 0;
                }
            }
        }
        return orderCheckStatus;
    }


    public Map<String,String> getsaleordergoodsPriceInfo(Saleorder saleorder){
        saleorder = saleorderMapper.getSaleOrderBaseInfo(saleorder.getSaleorderId());
        List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleOrderGoodsList(saleorder);
        TraderCustomer customer = traderCustomerMapper.getTraderCustomerByTraderId(saleorder.getTraderId());
        if (null != customer) {
            // 计算核价信息
            saleorderGoodsList = goodsChannelPriceService.getSaleChannelPriceList(saleorder.getSalesAreaId(),
                    saleorder.getCustomerNature(), customer.getOwnership(), saleorderGoodsList);
        }
        //核价信息应用 add by brianna 2020/5/29 start
        List<GoodSalePrice> goodSalePriceList = saleorderGoodsList.stream().map(saleorderGood -> {
            return new GoodSalePrice(saleorderGood.getSku(),saleorderGood.getChannelPrice());
        }).collect(Collectors.toList());
        //核价信息应用 add by brianna 2020/5/29 end
        return priceInfoDealWithService.dealWithGoodsSalePrice(saleorder.getTraderId() ,goodSalePriceList);
    }

    /**
     * @Desc 新增销售单同步创建报价单
     * <AUTHOR>
     */
    public ResultInfo createOrderSyncCreateQuote(Integer saleorderId){
        Saleorder saleorderExtra = saleorderMapper.getBaseSaleorderInfo(saleorderId);
        //不存在报价单的情况下，新增报价单
        if(saleorderExtra.getQuoteorderId() == null || ErpConst.ZERO.equals(saleorderExtra.getQuoteorderId())){

            //保存到报价表
            Quoteorder record = new Quoteorder();
            record.setQuoteorderNo(saleorderExtra.getSaleorderNo());
            record.setCompanyId(saleorderExtra.getCompanyId());
            record.setSource(3);
            record.setOrgId(saleorderExtra.getOrgId());
            record.setUserId(saleorderExtra.getUserId());
            record.setTraderId(saleorderExtra.getTraderId());
            record.setTraderName(saleorderExtra.getTraderName());

            record.setCustomerType(saleorderExtra.getCustomerType());
            record.setCustomerNature(saleorderExtra.getCustomerNature());
            //判断是不是新客户
            if(quoteorderMapper.getEffectOrders(saleorderId, 1)!=null) {
                record.setIsNewCustomer(0);
            }else {
                record.setIsNewCustomer(1);
            }
            record.setTerminalTraderName(saleorderExtra.getTerminalTraderName());
            record.setTerminalTraderId(saleorderExtra.getTerminalTraderId());
            record.setTerminalTraderType(saleorderExtra.getTerminalTraderType());
            TraderCustomerVo traderCustomerVo = traderCustomerMapper.getCustomerInfo(saleorderExtra.getTraderId());
            if (traderCustomerVo != null && traderCustomerVo.getCustomerLevel() > 0){
                record.setCustomerLevel(sysOptionDefinitionMapper.selectByPrimaryKey(traderCustomerVo.getCustomerLevel()).getTitle());
            }
            record.setArea(baseService.getAddressByAreaId(traderCustomerVo.getAreaId()));
            record.setTraderContactId(saleorderExtra.getTraderContactId());
            record.setTraderContactName(saleorderExtra.getTraderContactName());
            record.setMobile(saleorderExtra.getTraderContactMobile());
            record.setTelephone(saleorderExtra.getTraderContactTelephone());
            record.setTraderAddressId(saleorderExtra.getTraderAddressId());
            record.setAddress(saleorderExtra.getTraderAddress());
            record.setIsPolicymaker(0);
            record.setPeriod(14);
            record.setFollowOrderStatus(1);
            record.setSalesAreaId(saleorderExtra.getSalesAreaId());
            record.setSalesArea(saleorderExtra.getSalesArea());
            record.setTotalAmount(saleorderExtra.getTotalAmount());
            record.setAddTime(saleorderExtra.getAddTime());
            record.setCreator(saleorderExtra.getCreator());
            record.setPaymentType(419);
            record.setPrepaidAmount(saleorderExtra.getPrepaidAmount());//预付金额
            record.setInvoiceType(saleorderExtra.getInvoiceType());
            record.setFreightDescription(saleorderExtra.getFreightDescription());
            record.setAdditionalClause(saleorderExtra.getAdditionalClause());
            record.setComments(saleorderExtra.getComments());
            logger.info("创建报价单信息{},", JSON.toJSONString(record));
            quoteorderMapper.saveQuote(record);
            Integer quoteorderId = record.getQuoteorderId();
            //更新订单表里的报价表id
            saleorderExtra.setQuoteorderId(quoteorderId);
            List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorderId);
            for (SaleorderGoods goods : saleorderGoods) {
                QuoteorderGoods quoteorderGoods =  new QuoteorderGoods();
                GoodsVo goodsInfo = goodsMapper.getGoodsInfoByGoodsId(goods.getGoodsId());
                quoteorderGoods.setAddTime(goods.getAddTime());
                quoteorderGoods.setGoodsId(goods.getGoodsId());
                quoteorderGoods.setSku(goods.getSku());
                quoteorderGoods.setGoodsName(goods.getGoodsName());
                quoteorderGoods.setBrandName(goods.getBrandName());
                quoteorderGoods.setModel(goods.getModel());
                quoteorderGoods.setUnitName(goods.getUnitName());
                quoteorderGoods.setPrice(goods.getPrice());
                quoteorderGoods.setDeliveryCycle("14");
                quoteorderGoods.setDeliveryDirect(3);
                if (goodsInfo != null){
                    quoteorderGoods.setRegistrationNumber(goodsInfo.getRegistrationNumber());
                    quoteorderGoods.setSupplierName(goodsInfo.getSupplierName());
                }
                quoteorderGoods.setCreator(saleorderExtra.getCreator());
                quoteorderGoods.setNum(goods.getNum());
                quoteorderGoods.setDeliveryDirect(0);//直发
                quoteorderGoods.setQuoteorderId(quoteorderId);
                quoteorderGoods.setGoodsComments(goods.getGoodsComments());
                logger.info("创建报价单商品信息{},", JSON.toJSONString(quoteorderGoods));
                quoteGoodsMapper.insertQuoteGoods(quoteorderGoods);
            }
            int j = saleorderMapper.updateQuoteorderIdById(saleorderExtra);
            return new ResultInfo(0,"同步创建报价单成功",record);
        }else {
            return new ResultInfo(0,"销售单已有报价单，无需创建");
        }
    }

    public ResultInfo<Saleorder> saveRePurchaseOrder(Saleorder saleorder){
        logger.info("创建复购订单信息{},", JSON.toJSONString(saleorder));
        // 新增订单主信息
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(saleorder.getGoodsList())){
            saleorder.setSaleorderId(null);
            Integer i = saleorderMapper.insertSelective(saleorder);
            Integer orderId = saleorder.getSaleorderId();
            saleorder.setSaleorderId(orderId);
            //获取所选商品对应的最新订单的基本信息
            int saleOrderId = saleorder.getGoodsList().get(0).getSaleorderId();
            Saleorder orderInfo = saleorderMapper.selectByPrimaryKey(saleOrderId);

            TraderCustomerVo traderCustomerVo = traderCustomerMapper.getTraderCustomerInfo(saleorder.getTraderId());
            switch (TraderBelongPlatformEnum.getInstance(traderCustomerVo.getBelongPlatform())){
                case YXG:{
                    //zxf
                    saleorder.setSaleorderNo(orderNoDict.getOrderNum(orderId, 25));
                    saleorder.setOrderType(SaleOrderTypeEnum.ZXF.getType());
                    saleorder.setIsSameAddress(orderInfo.getIsSameAddress());
                    break;
                }
                case JC:{
                    //jcf
                    saleorder.setSaleorderNo(orderNoDict.getOrderNum(orderId, 24));
                    saleorder.setOrderType(SaleOrderTypeEnum.JCF.getType());
                    Integer traderIdToUse = SaleOrderTypeEnum.isJcOrder(orderInfo.getOrderType()) ? orderInfo.getGroupCustomerId() : orderInfo.getTraderId();
                    saleorder.setGroupCustomerId(traderIdToUse);
                    saleorder.setIsSameAddress(orderInfo.getIsSameAddress());
                    saleorder.setStatus(ErpConst.FOUR);
                    saleorder.setConfirmTime(new Date());
                    saleorder.setConfirmStatus(1);
                    break;
                }
                default:{
                    //vs
                    saleorder.setSaleorderNo(orderNoDict.getOrderNum(orderId, 3));
                    break;
                }
            }
            //客户信息
            //获取客户的默认联系人和地址信息
            saleorder.setTraderName(traderCustomerVo.getTraderName());
            saleorder.setCustomerType(traderCustomerVo.getCustomerType());
            saleorder.setCustomerNature(traderCustomerVo.getCustomerNature());
            //有默认值取默认值，没默认值取最新一笔订单的客户信息
            if (traderCustomerVo.getTraderContactId() != null){
                saleorder.setTraderContactId(traderCustomerVo.getTraderContactId());
                saleorder.setTraderContactName(traderCustomerVo.getTraderContactName());
                saleorder.setTraderContactMobile(traderCustomerVo.getTraderContactMobile());
                saleorder.setTraderContactTelephone(traderCustomerVo.getTraderContactTelephone());
            }else {
                saleorder.setTraderContactId(orderInfo.getTraderContactId());
                saleorder.setTraderContactName(orderInfo.getTraderContactName());
                saleorder.setTraderContactMobile(orderInfo.getTraderContactMobile());
                saleorder.setTraderContactTelephone(orderInfo.getTraderContactTelephone());
            }
            if (traderCustomerVo.getTraderAddressId() != null){
                saleorder.setTraderAddressId(traderCustomerVo.getTraderAddressId());
                saleorder.setTraderAreaId(traderCustomerVo.getTraderAreaId());
                saleorder.setTraderAddress(traderCustomerVo.getTraderAddress());
                if (traderCustomerVo.getTraderAreaId() > 0){
                    saleorder.setTraderArea(baseService.getAddressByAreaId(traderCustomerVo.getTraderAreaId()));
                }
            }else {
                saleorder.setTraderAddressId(orderInfo.getTraderAddressId());
                saleorder.setTraderAreaId(orderInfo.getTraderAreaId());
                saleorder.setTraderAddress(orderInfo.getTraderAddress());
                saleorder.setTraderArea(orderInfo.getTraderArea());
            }
            saleorder.setIsSendInvoice(orderInfo.getIsSendInvoice());
            saleorder.setDeliveryType(orderInfo.getDeliveryType());
            saleorder.setDeliveryMethod(orderInfo.getDeliveryMethod());
            saleorder.setIsPrintout(orderInfo.getIsPrintout());
            saleorder.setLogisticsId(orderInfo.getLogisticsId());
            saleorder.setFreightDescription(orderInfo.getFreightDescription());
            saleorder.setLogisticsComments(orderInfo.getLogisticsComments());
            saleorder.setDeliveryClaim(orderInfo.getDeliveryClaim());
            saleorder.setDeliveryDelayTime(orderInfo.getDeliveryDelayTime());


            saleorder.setCreateMobile(orderInfo.getTraderContactMobile());
            saleorder.setIsSendInvoice(orderInfo.getIsSendInvoice());
            saleorder.setInvoiceType(orderInfo.getInvoiceType());
            // 全部调整为自动数电发票 VDERP-15896
            saleorder.setInvoiceMethod(ErpConst.FOUR);
            saleorder.setInvoiceComments(orderInfo.getInvoiceComments());
            saleorder.setIsDelayInvoice(orderInfo.getIsDelayInvoice());




            //设置收货、收票客户联系人地址信息
            List<Integer> saleOrderIdList = saleorder.getGoodsList().stream().map(SaleorderGoods::getSaleorderId).collect(Collectors.toList());
            //获取非耗材订单信息
            List<Saleorder> saleorders = saleorderMapper.getNotHcSaleOrderInfo(saleOrderIdList);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(saleorders)) {
                //勾选了多笔订单，取到最新一笔订单是HC订单时，过滤跳过HC订单，继续取下一笔最新订单的收货信息、收票信息
                int saleorderId = saleorders.stream().map(Saleorder::getSaleorderId).max(Integer::compareTo).get();
                //最新非hc订单
                Saleorder saleorderInfo = saleorderMapper.selectByPrimaryKey(saleorderId);
                setTraderContactAndAddressInfo(saleorder,saleorderInfo);
            }else {
                //勾选了一笔订单且仅HC订单，收票信息、收货信息按客户带出
                setTraderContactAndAddress(saleorder,orderInfo,traderCustomerVo);
            }
            BigDecimal totalAmount = new BigDecimal("0");
            //保存订单商品
            for (SaleorderGoods saleorderGoods : saleorder.getGoodsList()) {
                saleorderGoods.setCreator(saleorder.getCreator());
                saleorderGoods.setAddTime(System.currentTimeMillis());
                saleorderGoods.setUpdateDataTime(new Date());
                saleorderGoods.setUpdater(saleorder.getCreator());
                saleorderGoods.setModTime(System.currentTimeMillis());
                saleorderGoods.setSaleorderId(orderId);
                saleorderGoods.setSaleorderGoodsId(null);
                saleorderGoodsMapper.insertSelective(saleorderGoods);
                totalAmount = totalAmount.add(saleorderGoods.getPrice().multiply(new BigDecimal(saleorderGoods.getNum())));
            }
            saleorder.setTotalAmount(totalAmount);
            //付款计划
            setPaymentPlan(saleorder,orderInfo);
            saleorderMapper.updateByPrimaryKeySelective(saleorder);
            orderAmountStrategy.execute(saleorder);
        }

        return ResultInfo.success(CREATE_SALE_ORDER_SUCCESS,saleorder);
    }

    private void setTraderContactAndAddress(Saleorder saleorder,Saleorder orderInfo,TraderCustomerVo traderCustomerVo){
        //收货信息
        saleorder.setTakeTraderId(orderInfo.getTraderId());
        saleorder.setTakeTraderName(orderInfo.getTraderName());
        //有默认值取默认值，没默认值取最新一笔订单的客户信息
        if (traderCustomerVo.getTraderContactId() != null){
            saleorder.setTakeTraderContactId(traderCustomerVo.getTraderContactId());
            saleorder.setTakeTraderContactName(traderCustomerVo.getTraderContactName());
            saleorder.setTakeTraderContactMobile(traderCustomerVo.getTraderContactMobile());
            saleorder.setTakeTraderContactTelephone(traderCustomerVo.getTraderContactTelephone());
        }else {
            saleorder.setTakeTraderContactId(orderInfo.getTraderContactId());
            saleorder.setTakeTraderContactName(orderInfo.getTraderContactName());
            saleorder.setTakeTraderContactMobile(orderInfo.getTraderContactMobile());
            saleorder.setTakeTraderContactTelephone(orderInfo.getTraderContactTelephone());
        }
        if (traderCustomerVo.getTraderAddressId() != null){
            saleorder.setTakeTraderAddressId(traderCustomerVo.getTraderAddressId());
            saleorder.setTakeTraderAreaId(traderCustomerVo.getTraderAreaId());
            saleorder.setTakeTraderAddress(traderCustomerVo.getTraderAddress());
            if (traderCustomerVo.getTraderAreaId() > 0){
                saleorder.setTakeTraderArea(baseService.getAddressByAreaId(traderCustomerVo.getTraderAreaId()));
            }
        }else {
            saleorder.setTraderAddressId(orderInfo.getTraderAddressId());
            saleorder.setTraderAreaId(orderInfo.getTraderAreaId());
            saleorder.setTraderAddress(orderInfo.getTraderAddress());
            saleorder.setTraderArea(orderInfo.getTraderArea());
        }
        //收票信息
        saleorder.setInvoiceTraderId(orderInfo.getTraderId());
        saleorder.setInvoiceTraderName(orderInfo.getTraderName());
        if (traderCustomerVo.getTraderContactId() != null){
            saleorder.setInvoiceTraderContactId(traderCustomerVo.getTraderContactId());
            saleorder.setInvoiceTraderContactName(traderCustomerVo.getTraderContactName());
            saleorder.setInvoiceTraderContactMobile(traderCustomerVo.getTraderContactMobile());
            saleorder.setInvoiceTraderContactTelephone(traderCustomerVo.getTraderContactTelephone());
        }else {
            saleorder.setInvoiceTraderContactId(orderInfo.getTraderContactId());
            saleorder.setInvoiceTraderContactName(orderInfo.getTraderContactName());
            saleorder.setInvoiceTraderContactMobile(orderInfo.getTraderContactMobile());
            saleorder.setInvoiceTraderContactTelephone(orderInfo.getTraderContactTelephone());
        }
        if (traderCustomerVo.getTraderAddressId() != null){
            saleorder.setInvoiceTraderAddressId(traderCustomerVo.getTraderAddressId());
            saleorder.setInvoiceTraderAreaId(traderCustomerVo.getTraderAreaId());
            saleorder.setInvoiceTraderAddress(traderCustomerVo.getTraderAddress());
            if (traderCustomerVo.getTraderAreaId() > 0){
                saleorder.setInvoiceTraderArea(baseService.getAddressByAreaId(traderCustomerVo.getTraderAreaId()));
            }
        }else {
            saleorder.setInvoiceTraderAddressId(orderInfo.getTraderAddressId());
            saleorder.setInvoiceTraderAreaId(orderInfo.getTraderAreaId());
            saleorder.setInvoiceTraderAddress(orderInfo.getTraderAddress());
            saleorder.setInvoiceTraderArea(orderInfo.getTraderArea());
        }
    }

    private void setTraderContactAndAddressInfo(Saleorder saleorder,Saleorder orderInfo){
        //收货信息
        saleorder.setTakeTraderId(orderInfo.getTakeTraderId());
        saleorder.setTakeTraderName(orderInfo.getTakeTraderName());
        saleorder.setTakeTraderContactId(orderInfo.getTakeTraderContactId());
        saleorder.setTakeTraderContactName(orderInfo.getTakeTraderContactName());
        saleorder.setTakeTraderContactMobile(orderInfo.getTakeTraderContactMobile());
        saleorder.setTakeTraderContactTelephone(orderInfo.getTraderContactTelephone());
        saleorder.setTakeTraderAddressId(orderInfo.getTakeTraderAddressId());
        saleorder.setTakeTraderAreaId(orderInfo.getTakeTraderAreaId());
        saleorder.setTakeTraderArea(orderInfo.getTakeTraderArea());
        saleorder.setTakeTraderAddress(orderInfo.getTakeTraderAddress());
        //收票信息
        saleorder.setInvoiceTraderId(orderInfo.getInvoiceTraderId());
        saleorder.setInvoiceTraderName(orderInfo.getInvoiceTraderName());
        saleorder.setInvoiceTraderContactId(orderInfo.getInvoiceTraderContactId());
        saleorder.setInvoiceTraderContactName(orderInfo.getInvoiceTraderContactName());
        saleorder.setInvoiceTraderContactMobile(orderInfo.getInvoiceTraderContactMobile());
        saleorder.setInvoiceTraderContactTelephone(orderInfo.getInvoiceTraderContactTelephone());
        saleorder.setInvoiceTraderAddressId(orderInfo.getInvoiceTraderAddressId());
        saleorder.setInvoiceTraderAreaId(orderInfo.getInvoiceTraderAreaId());
        saleorder.setInvoiceTraderArea(orderInfo.getInvoiceTraderArea());
        saleorder.setInvoiceTraderAddress(orderInfo.getInvoiceTraderAddress());
    }

    private void setPaymentPlan(Saleorder saleorder,Saleorder orderInfo){
        saleorder.setPaymentType(orderInfo.getPaymentType());
        if (PaymentTypeEnum.PAY_IN_ADVANCE_100.getType().equals(orderInfo.getPaymentType())){
            saleorder.setPrepaidAmount(saleorder.getTotalAmount());
            saleorder.setHaveAccountPeriod(0);
        }else if (PaymentTypeEnum.GOODS_IN_ADVANCE_80.getType().equals(orderInfo.getPaymentType())){
            saleorder.setPrepaidAmount(saleorder.getTotalAmount().multiply(new BigDecimal("0.80")));
            saleorder.setAccountPeriodAmount(saleorder.getTotalAmount().multiply(new BigDecimal("0.20")));
            saleorder.setHaveAccountPeriod(1);
        }else if (PaymentTypeEnum.GOODS_IN_ADVANCE_50.getType().equals(orderInfo.getPaymentType())){
            saleorder.setPrepaidAmount(saleorder.getTotalAmount().multiply(new BigDecimal("0.50")));
            saleorder.setAccountPeriodAmount(saleorder.getTotalAmount().multiply(new BigDecimal("0.50")));
            saleorder.setHaveAccountPeriod(1);
        }else if (PaymentTypeEnum.GOODS_IN_ADVANCE_30.getType().equals(orderInfo.getPaymentType())){
            saleorder.setPrepaidAmount(saleorder.getTotalAmount().multiply(new BigDecimal("0.30")));
            saleorder.setAccountPeriodAmount(saleorder.getTotalAmount().multiply(new BigDecimal("0.70")));
            saleorder.setHaveAccountPeriod(1);
        }else if (PaymentTypeEnum.GOODS_IN_ADVANCE_0.getType().equals(orderInfo.getPaymentType())){
            saleorder.setPrepaidAmount(saleorder.getTotalAmount().multiply(new BigDecimal("0.00")));
            saleorder.setAccountPeriodAmount(saleorder.getTotalAmount());
            saleorder.setHaveAccountPeriod(1);
        }else if (PaymentTypeEnum.CUSTOMIZE.getType().equals(orderInfo.getPaymentType())){
            saleorder.setPrepaidAmount(new BigDecimal("0.00"));
        }
        saleorder.setRetentionMoney(new BigDecimal("0.00"));
    }

    public void setQuoteOrderInfo(Quoteorder quoteorder,Integer bussinessChanceId,Saleorder saleorder){

        Quoteorder quoteorderExtra = new Quoteorder();
        quoteorderExtra.setQuoteorderId(quoteorder.getQuoteorderId());
        quoteorderExtra.setBussinessChanceId(bussinessChanceId);
        quoteorderExtra.setIsPolicymaker(ErpConst.ONE);
        quoteorderExtra.setValidStatus(ErpConst.ONE);
        quoteorderExtra.setValidTime(System.currentTimeMillis());
        quoteorderExtra.setPurchasingTime(411);
        quoteorderExtra.setPaymentTerm(408);
        quoteorderExtra.setPaymentType(saleorder.getPaymentType());
        quoteorderExtra.setPrepaidAmount(saleorder.getPrepaidAmount());
        quoteorderExtra.setAccountPeriodAmount(saleorder.getAccountPeriodAmount());
        quoteorderExtra.setFollowOrderTime(System.currentTimeMillis());
        quoteorderMapper.updateQuote(quoteorderExtra);
        //保存审核状态
        VerifiesInfo verifiesInfo = new VerifiesInfo();
        verifiesInfo.setRelateTable("T_QUOTEORDER");
        verifiesInfo.setRelateTableKey(quoteorder.getQuoteorderId());
        verifiesInfo.setStatus(ErpConst.ONE);
        //对应字典表quoteVerify
        verifiesInfo.setVerifiesType(612);
        verifiesInfo.setAddTime(System.currentTimeMillis());
        verifiesInfo.setModTime(System.currentTimeMillis());
        verifiesRecordService.saveVerifiesInformation(verifiesInfo);
    }

    public ResultInfo<?> syncCreateBusiness(Saleorder saleorder){
        Integer saleorderId = saleorder.getSaleorderId();
        Saleorder saleorderExtra = saleorderMapper.getBaseSaleorderInfo(saleorderId);
        BussinessChanceVo bussinessChance = new BussinessChanceVo();
        bussinessChance.setStatus(ErpConst.THREE);
        bussinessChance.setCompanyId(saleorderExtra.getCompanyId());
        bussinessChance.setOrderTime(saleorderExtra.getAddTime());
        bussinessChance.setUserId(saleorderExtra.getUserId());
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorderId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(saleorderGoods)) {
            String goodsName = saleorderGoods.stream().limit(3).map(SaleorderGoods::getGoodsName).reduce((a, b) -> a + "、" + b).get();
            bussinessChance.setGoodsName(saleorderGoods.get(0).getGoodsName());
            bussinessChance.setContent(goodsName);
            bussinessChance.setGoodsBrand(saleorderGoods.get(0).getBrandName());
        }
        TraderCustomerVo traderCustomerInfo = traderCustomerService.getTraderCustomerInfo(saleorderExtra.getTraderId());
        setBussinessTraderInfo(bussinessChance, saleorderExtra);
        bussinessChance.setAddTime(DateUtil.sysTimeMillis());
        bussinessChance.setReceiveTime(DateUtil.sysTimeMillis());
        bussinessChance.setAssignTime(DateUtil.sysTimeMillis());
        bussinessChance.setTraderId(saleorderExtra.getTraderId());
        bussinessChance.setTraderName(saleorderExtra.getTraderName());
        bussinessChance.setAreaId(traderCustomerInfo.getAreaId());
        bussinessChance.setAreaIds(traderCustomerInfo.getAreaIds());
        bussinessChance.setAmount(saleorderExtra.getTotalAmount());
        bussinessChance.setCheckTraderArea(traderCustomerInfo.getAddress());
        //类型为销售新增商机
        bussinessChance.setType(392);
        bussinessChance.setSource(SysOptionConstant.ID_366);
        bussinessChance.setBussinessLevel(939);
        bussinessChance.setBussinessStage(947);
        bussinessChance.setOrderRate(956);
        bussinessChance.setSource(366);
        User user = new User();
        user.setUserId(saleorderExtra.getCreator());

        User createUser = userService.getUserById(saleorderExtra.getCreator());
        if(createUser!=null){
            bussinessChance.setOrgId(createUser.getOrgId());
        }
        logger.info("同步创建商机信息：{}",JSON.toJSONString(bussinessChance));
        bussinessChanceMapper.insertSelective(bussinessChance);
        if (org.apache.commons.lang3.StringUtils.isBlank(bussinessChance.getBussinessChanceNo())){
            bussinessChance.setBussinessChanceNo(orderNoDict.getOrderNum(bussinessChance.getBussinessChanceId(), Constants.ONE));
        }
        bussinessChance.setOldChanceNo(bussinessChance.getBussinessChanceNo());
        bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChance);
        return ResultInfo.success("商机创建成功",bussinessChance);
    }

    private void setBussinessTraderInfo(BussinessChanceVo bussinessChance,Saleorder saleorder) {
        bussinessChance.setCheckTraderName(saleorder.getTraderName());
        bussinessChance.setCheckMobile(saleorder.getTraderContactMobile());
        bussinessChance.setCheckTraderContactName(saleorder.getTraderContactName());
        bussinessChance.setCheckTraderContactTelephone(saleorder.getTraderContactTelephone());
        //销售新增商机
        bussinessChance.setTraderContactName(saleorder.getTraderContactName());
        bussinessChance.setMobile(saleorder.getTraderContactMobile());
        bussinessChance.setTelephone(saleorder.getTraderContactTelephone());

    }

    public Map<String,BigDecimal> getSkuPriceInfo(List<String> skuNos,Integer traderId){
        List<PriceInfoResponseDto> priceInfoResponses = basePriceService.batchFindPriceInfo(skuNos,traderId);
        Map<String, PriceInfoResponseDto> priceInfoMap = null;
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(priceInfoResponses)) {
            priceInfoMap = priceInfoResponses.stream().collect(Collectors.toMap(PriceInfoResponseDto::getSkuNo, PriceInfoResponseDto -> PriceInfoResponseDto));
        }
        Map<String,BigDecimal> skuPriceMap = new HashMap<>();
        for (String skuNo : skuNos) {
            if (priceInfoMap != null && priceInfoMap.containsKey(skuNo)){
                PriceInfoResponseDto priceInfoResponseDto = priceInfoMap.get(skuNo);
                if (ErpConst.ZERO.equals(priceInfoResponseDto.getPriceType())){
                    skuPriceMap.put(priceInfoResponseDto.getSkuNo(),BigDecimal.ZERO);
                    continue;
                }
                if (priceInfoResponseDto.getContractPrice() != null) {
                    skuPriceMap.put(priceInfoResponseDto.getSkuNo(),priceInfoResponseDto.getContractPrice());
                }
                TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(traderId);
                if (traderCustomer != null && traderCustomer.getCustomerNature() != null) {
                    Integer customerNature = traderCustomer.getCustomerNature();
                    if (customerNature.equals(SysOptionConstant.ID_465) && priceInfoResponseDto.getDistributionPrice() != null) {
                        skuPriceMap.put(priceInfoResponseDto.getSkuNo(),priceInfoResponseDto.getDistributionPrice());
                    } else if (customerNature.equals(SysOptionConstant.ID_466) && priceInfoResponseDto.getTerminalPrice() != null) {
                        skuPriceMap.put(priceInfoResponseDto.getSkuNo(),priceInfoResponseDto.getTerminalPrice());
                    }
                }
            }else {
                // 产品核算价
                Integer type = 1;// 采购
                Map<String, Integer> map = new HashMap<String, Integer>();
                map.put("goodsId",Integer.valueOf(skuNo.substring(1,skuNo.length())));
                map.put("type", type);
                List<GoodsChannelPrice> goodsChannelPriceList = goodsChannelPriceService.getGoodsChannelPriceByGoodsId(map);
                if(org.apache.commons.collections.CollectionUtils.isNotEmpty(goodsChannelPriceList)){
                    // 如果核价信息存在，取第一条的成本价
                    skuPriceMap.put(skuNo,goodsChannelPriceList.get(0).getCostPrice());
                }
            }

        }
        return skuPriceMap;
    }


    public List<SaleorderGoods>  calculateGoodsBuyStatus(List<SaleorderGoodsVo> saleorderGoodsVoList){

        List<SaleorderGoods> resultList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(saleorderGoodsVoList)){

            //旧订单商品
            List<SaleorderGoodsVo> isNotNewOrderGoods = saleorderGoodsVoList.stream().filter(item-> item.getIsNew() ==null || item.getIsNew() == 0).collect(Collectors.toList());


            //新订单流商品
            List<SaleorderGoodsVo> isNewOrderGoods = saleorderGoodsVoList.stream().filter(item-> item.getIsNew() !=null && item.getIsNew() == 1).collect(Collectors.toList());

            // 直发，普发算法不同，普发涉及可能从备货出库
            if (!CollectionUtils.isEmpty(isNotNewOrderGoods)) {
                // 2018-06-04原来方法需采数量未排除备货出的数量
                // 直发仍采用原来算法
                List<BuyorderGoodsVo> bgvList = buyorderGoodsMapper.getBuyorderGoodsSumBySaleorderGoodsIds(isNotNewOrderGoods);

                List<AfterSalesGoodsVo> asvList = afterSalesGoodsMapper.getPurchaseAfterSalesNum(isNotNewOrderGoods);// 销售单对应的售后退货

                List<AfterSalesGoodsVo> buyasvList = afterSalesGoodsMapper.getBuyorderAfterSalesNum(isNotNewOrderGoods);// 采购对应的售后退货
                // 排除销售退货数量
                for (SaleorderGoodsVo sgv : isNotNewOrderGoods) {
                    for (AfterSalesGoodsVo sasv : asvList) {
                        if (sgv.getSaleorderGoodsId().intValue() == sasv.getOrderDetailId().intValue()) {
                            sgv.setNum(sgv.getNum() - sasv.getAfterSalesNum());
                            break;
                        }
                    }
                }
                // 排除采购退货数量
                for (BuyorderGoodsVo bgv : bgvList) {
                    for (AfterSalesGoodsVo asv : buyasvList) {
                        if (bgv.getSaleorderGoodsId().intValue() == asv.getSaleorderGoodsId().intValue()) {
                            bgv.setBuynum(bgv.getBuynum() - asv.getAfterSalesNum());// 销售商品，对应实际已采购数量
                            break;
                        }
                    }
                }

                // 以下为普发商品算法
                // 2018-06-04 --只查询普发的记录

                // 销售产品出库数量
                List<SaleorderGoodsVo> outStockList = warehouseGoodsOperateLogMapper
                        .batchOutStockBySaleorderGoodsIdList(isNotNewOrderGoods);

                // 采购入库数量
                List<SaleorderGoodsVo> buyInStockList = warehouseGoodsOperateLogMapper
                        .batchBuyGoodsInStockBySaleorderGoodsIdList(isNotNewOrderGoods);

                // 采购未出库数量
                List<SaleorderGoodsVo> buyNotStockList = warehouseGoodsOperateLogMapper
                        .batchBuyGoodsNotStockBySaleorderGoodsIdList(isNotNewOrderGoods);

                // 排除销售退货数量
                for (SaleorderGoodsVo sgv : isNotNewOrderGoods) {
                    for (BuyorderGoodsVo bgv : bgvList) {
                        if (sgv.getSaleorderGoodsId().intValue() == bgv.getSaleorderGoodsId().intValue()) {
                            sgv.setBuyNum(bgv.getBuynum());
                            break;
                        }
                    }

                    for (SaleorderGoodsVo sv : outStockList) {
                        if (sgv.getSaleorderGoodsId().intValue() == sv.getSaleorderGoodsId().intValue()) {
                            sgv.setSaleGoodsOut(sv.getSaleGoodsOut());
                            break;
                        }
                    }

                    for (SaleorderGoodsVo sv : buyInStockList) {
                        if (sgv.getSaleorderGoodsId().intValue() == sv.getSaleorderGoodsId().intValue()) {
                            sgv.setBuyGoodsIn(sv.getBuyGoodsIn());
                            break;
                        }
                    }

                    for (SaleorderGoodsVo sv : buyNotStockList) {
                        if (sgv.getSaleorderGoodsId().intValue() == sv.getSaleorderGoodsId().intValue()) {
                            sgv.setBuyGoodsNotOut(sv.getBuyGoodsNotOut());
                            break;
                        }
                    }

                }

                for (SaleorderGoodsVo sgv : isNotNewOrderGoods) {
                    if (sgv.getDeliveryDirect() != null && sgv.getDeliveryDirect() == 1
                            && (sgv.getNum() > (sgv.getBuyNum() == null ? 0 : sgv.getBuyNum()))) {

                    } else if (sgv.getDeliveryDirect() != null && sgv.getDeliveryDirect() == 0
                            && (sgv.getNum() > (sgv.getBuyNum() == null ? 0 : sgv.getBuyNum()))
                            && (sgv.getNum() > (sgv.getSaleGoodsOut() == null ? 0 : sgv.getSaleGoodsOut()))) {
                        Integer saleGoodsOut = sgv.getSaleGoodsOut() == null ? 0 : sgv.getSaleGoodsOut();
                        Integer buyGoodsNotOut = sgv.getBuyGoodsNotOut() == null ? 0 : sgv.getBuyGoodsNotOut();
                        Integer buyNotInStock = (sgv.getBuyNum() == null ? 0 : sgv.getBuyNum())
                                - (sgv.getBuyGoodsIn() == null ? 0 : sgv.getBuyGoodsIn());
                        Integer heji = saleGoodsOut + buyGoodsNotOut + buyNotInStock;
                        sgv.setBuyNum(heji);
                    }
                }

                resultList.addAll(isNotNewOrderGoods);
            }

            if (!CollectionUtils.isEmpty(isNewOrderGoods)){

                Set<Integer> saleorderIds = new HashSet<>();

                isNewOrderGoods.forEach(item->saleorderIds.add(item.getSaleorderId()));

                saleorderIds.forEach(item->{
                    //新订单流计算规则
                    List<SaleorderGoods> saleorderGoodsList = getPurchasedCountOfSaleOrder(item);

                    resultList.addAll(saleorderGoodsList);
                });
            }
        }


        if (!CollectionUtils.isEmpty(resultList)){
            for (SaleorderGoods sgv : resultList) {
                sgv.setBuyNum(sgv.getBuyNum() == null ? 0 : sgv.getBuyNum());

                //计算上商品采购状态
                if (sgv.getBuyNum() == 0){
                    sgv.setPurchaseStatusData(0);
                }else if (sgv.getBuyNum() < sgv.getNum()) {
                    sgv.setPurchaseStatusData(1);
                }else{
                    sgv.setPurchaseStatusData(2);
                }
            }
        }

        return resultList;
    }

    public Saleorder saveCopyOrder(Saleorder saleorder){
        Integer saleOrderId = saleorder.getSaleorderId();
        // 新增订单主信息
        Saleorder order = new Saleorder();
        order.setAddTime(System.currentTimeMillis());
        order.setModTime(System.currentTimeMillis());
        order.setCreator(saleorder.getCreator());
        order.setCreatorOrgId(saleorder.getCreatorOrgId());
        order.setCreatorOrgName(saleorder.getCreatorOrgName());
        order.setCompanyId(saleorder.getCompanyId());
        order.setStatus(saleorder.getStatus());
        order.setIsNew(ErpConst.ONE);
        order.setOrderType(saleorder.getOrderType());
        if (OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType())) {
            order.setConfirmStatus(saleorder.getConfirmStatus());
            order.setConfirmTime(saleorder.getConfirmTime());
        }
        // 终端类型
        order.setTerminalTraderType(saleorder.getCustomerType());
        // 终端名称
        order.setTerminalTraderName(saleorder.getTerminalTraderName());
        // 销售区域
        order.setSalesArea(saleorder.getSalesArea());

        order.setSalesAreaId(saleorder.getSalesAreaId());


        order.setUserId(saleorder.getUserId());


        order.setOrgId(saleorder.getOrgId());
        order.setOrgName(saleorder.getOrgName());


        //客户信息
        order.setTraderId(saleorder.getTraderId());
        order.setTraderName(saleorder.getTraderName());
        order.setCustomerType(saleorder.getCustomerType());
        order.setCustomerNature(saleorder.getCustomerNature());
        order.setTraderContactId(saleorder.getTraderContactId());
        order.setTraderContactName(saleorder.getTraderContactName());
        order.setTraderContactMobile(saleorder.getTraderContactMobile());
        order.setTraderContactTelephone(saleorder.getTraderContactTelephone());
        order.setTraderAddressId(saleorder.getTraderAddressId());
        order.setTraderAreaId(saleorder.getTraderAreaId());
        order.setTraderAddress(saleorder.getTraderAddress());
        order.setTraderArea(saleorder.getTraderArea());

        //收货信息
        order.setTakeTraderId(saleorder.getTakeTraderId());
        order.setTakeTraderName(saleorder.getTakeTraderName());
        order.setTakeTraderContactId(saleorder.getTakeTraderContactId());
        order.setTakeTraderContactName(saleorder.getTakeTraderContactName());
        order.setTakeTraderContactMobile(saleorder.getTakeTraderContactMobile());
        order.setTakeTraderContactTelephone(saleorder.getTraderContactTelephone());
        order.setTakeTraderAddressId(saleorder.getTakeTraderAddressId());
        order.setTakeTraderAreaId(saleorder.getTakeTraderAreaId());
        order.setTakeTraderArea(saleorder.getTakeTraderArea());
        order.setTakeTraderAddress(saleorder.getTakeTraderAddress());
        order.setDeliveryType(saleorder.getDeliveryType());
        order.setDeliveryMethod(saleorder.getDeliveryMethod());
        order.setIsPrintout(saleorder.getIsPrintout());
        order.setLogisticsId(saleorder.getLogisticsId());
        order.setFreightDescription(saleorder.getFreightDescription());
        order.setLogisticsComments(saleorder.getLogisticsComments());
        order.setDeliveryClaim(saleorder.getDeliveryClaim());
        order.setDeliveryDelayTime(saleorder.getDeliveryDelayTime());
        //收票信息
        order.setInvoiceTraderId(saleorder.getInvoiceTraderId());
        order.setInvoiceTraderName(saleorder.getInvoiceTraderName());
        order.setInvoiceTraderContactId(saleorder.getInvoiceTraderContactId());
        order.setInvoiceTraderContactName(saleorder.getInvoiceTraderContactName());
        order.setInvoiceTraderContactMobile(saleorder.getInvoiceTraderContactMobile());
        order.setInvoiceTraderContactTelephone(saleorder.getInvoiceTraderContactTelephone());
        order.setInvoiceTraderAddressId(saleorder.getInvoiceTraderAddressId());
        order.setInvoiceTraderAreaId(saleorder.getInvoiceTraderAreaId());
        order.setInvoiceTraderArea(saleorder.getInvoiceTraderArea());
        order.setInvoiceTraderAddress(saleorder.getInvoiceTraderAddress());
        order.setCreateMobile(saleorder.getTraderContactMobile());
        order.setIsSendInvoice(saleorder.getIsSendInvoice());

        // VDERP-15956 【数电发票】医械购订单详情页-复制功能兼容数电/发票税率类型复制兼容
        order.setInvoiceMethod(ErpConst.FOUR);
        if (saleorder.getInvoiceType() != 971 && saleorder.getInvoiceType() != 972) {
            order.setInvoiceType(972);
        } else {
            order.setInvoiceType(saleorder.getInvoiceType());
        }
        order.setInvoiceComments(saleorder.getInvoiceComments());
        order.setIsDelayInvoice(saleorder.getIsDelayInvoice());
        order.setIsSameAddress(saleorder.getIsSameAddress());
        order.setInvoiceSendNode(saleorder.getInvoiceSendNode());

        order.setDeliveryDirect(saleorder.getDeliveryDirect());
        Integer traderIdToUse = SaleOrderTypeEnum.isJcOrder(saleorder.getOrderType()) ? saleorder.getGroupCustomerId() : saleorder.getTraderId();
        order.setGroupCustomerId(traderIdToUse);

       // order.setAdditionalClause(saleorder.getAdditionalClause());
        order.setComments(saleorder.getComments());


        int i = saleorderMapper.insertSelective(order);
        if (i == 1) {
            Saleorder saleorderExtra = saleorderMapper.getBaseSaleorderInfo(order.getSaleorderId());
            switch (SalesOrderTypeEnum.getInstance(order.getOrderType())){
                case ZXF:{
                    //zxf
                    saleorderExtra.setOrderType(9);
                    saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(order.getSaleorderId(), 25));
                    break;
                }
                case JCF:{
                    //jcf
                    saleorderExtra.setOrderType(8);
                    saleorderExtra.setSaleorderNo(orderNoDict.getOrderNum(order.getSaleorderId(), 24));
                    break;
                }
                default:{
                    break;
                }
            }
            BigDecimal totalAmount = new BigDecimal("0");
            //获取订单商品信息
            List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsByOrderId(saleOrderId);
            if (!CollectionUtils.isEmpty(saleorderGoodsList)){
                //内部备注
                LabelQuery labelQuery = new LabelQuery();
                labelQuery.setRelationId(saleOrderId);
                labelQuery.setScene(RemarkComponentSceneEnum.SALE_ORDER.getCode());
                List<ComponentRelation> componentRelations = remarkComponentMapper.getComponentRelationListByRelationId(labelQuery);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(componentRelations)) {
                    for (ComponentRelation componentRelation : componentRelations) {
                        componentRelation.setScene(RemarkComponentSceneEnum.SALE_ORDER.getCode());
                        componentRelation.setRelationId(order.getSaleorderId());
                        remarkComponentMapper.saveComponentRelation(componentRelation);
                    }
                }


                for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
                    SaleorderGoods orderGood = new SaleorderGoods();
                    orderGood.setGoodsId(saleorderGoods.getGoodsId());
                    orderGood.setSku(saleorderGoods.getSku());
                    orderGood.setGoodsName(saleorderGoods.getGoodsName());
                    orderGood.setBrandName(saleorderGoods.getBrandName());
                    orderGood.setModel(saleorderGoods.getModel());
                    orderGood.setUnitName(saleorderGoods.getUnitName());
                    orderGood.setPrice(saleorderGoods.getPrice());
                    orderGood.setRealPrice(saleorderGoods.getRealPrice());
                    orderGood.setPurchasingPrice(saleorderGoods.getPurchasingPrice());
                    orderGood.setCurrencyUnitId(saleorderGoods.getCurrencyUnitId());
                    orderGood.setNum(saleorderGoods.getNum());
                    orderGood.setDeliveryCycle(saleorderGoods.getDeliveryCycle());
                    orderGood.setDeliveryCycle(saleorderGoods.getDeliveryCycle());
                    orderGood.setDeliveryDirect(saleorderGoods.getDeliveryDirect());
                    orderGood.setDeliveryDirectComments(saleorderGoods.getDeliveryDirectComments());
                    orderGood.setGoodsComments(saleorderGoods.getGoodsComments());
                    orderGood.setInsideComments(saleorderGoods.getInsideComments());
                    orderGood.setCreator(order.getCreator());
                    orderGood.setAddTime(System.currentTimeMillis());
                    orderGood.setUpdateDataTime(new Date());
                    orderGood.setUpdater(order.getCreator());
                    orderGood.setModTime(System.currentTimeMillis());
                    orderGood.setHaveInstallation(saleorderGoods.getHaveInstallation());
                    orderGood.setSaleorderId(order.getSaleorderId());
                    totalAmount = totalAmount.add(new BigDecimal(saleorderGoods.getNum()).multiply(saleorderGoods.getPrice()));
                    saleorderGoodsMapper.insertSelective(orderGood);
                }
            }
            saleorderExtra.setTotalAmount(totalAmount);
            saleorderExtra.setRealTotalAmount(totalAmount);
            //付款计划
            setPaymentPlan(saleorderExtra,saleorder);
            saleorderMapper.updateByPrimaryKeySelective(saleorderExtra);
            saleorder.setSaleorderId(order.getSaleorderId());
            return saleorderExtra;
        }
        return new Saleorder();
    }
}
