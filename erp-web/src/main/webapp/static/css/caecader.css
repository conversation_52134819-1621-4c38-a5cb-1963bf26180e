.scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.scrollbar::-webkit-scrollbar-track {
    background: transparent;
    width: 6px;
    height: 6px;
}

.scrollbar::-webkit-scrollbar-thumb {
    background: #D7DADE;
    width: 6px;
    height: 6px;
    border-radius: 3px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
    background: #BABFC2;
}

.scrollbar::-webkit-scrollbar-thumb:active {
    background: #969B9E;
}




.vd-ui-cascader {
    position: relative;
    width: 100%;
    height: 100%;
}

.vd-ui-cascader i {
    background: none;
}

.vd-ui-cascader .bd-icon {
    background: none;
}

.vd-ui-cascader .vd-ui-cascader-wrapper {
    position: relative;
    width: 100%;
}

.vd-ui-cascader .vd-ui-cascader-wrapper>.vd-ui-input {
    width: 100%;
    display: inline-block;
    vertical-align: top;
    position: relative;
}

.vd-ui-cascader .vd-ui-cascader-wrapper>.vd-ui-input>input {
    box-sizing: border-box;
    width: 100%;
    color: #333;
    background-color: #fff;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    height: 33px;
    padding: 0px 36px 0 10px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.1s linear;
    cursor: pointer;
}

.vd-ui-cascader .vd-ui-cascader-wrapper>.vd-ui-input>input.focus {
    border-color: #09f;
}

.vd-ui-cascader .vd-ui-cascader-wrapper>.vd-ui-input>input::-webkit-input-placeholder {
    color: #999;
}

.vd-ui-cascader .vd-ui-cascader-wrapper>.vd-ui-input .icon {
    position: absolute;
    right: 9px;
    top: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.vd-ui-cascader .vd-ui-cascader-wrapper>.vd-ui-input .icon>i {
    font-size: 16px;
    color: #666;
    cursor: pointer;
    line-height: 33px;
    transition: 0.19s;
}

.vd-ui-cascader .vd-ui-cascader-wrapper>.vd-ui-input .icon .icon-search {
    pointer-events: none;
}

.vd-ui-cascader .vd-ui-cascader-wrapper>.vd-ui-input .icon.rotate {
    transform: rotate(180deg);
    transition: 0.22s;
}

.vd-ui-cascader .vd-ui-cascader-panel-wrap {
    display: inline-flex;
    position: absolute;
    z-index: 1500;
}

.vd-ui-cascader .vd-ui-cascader-modal {
    display: inline-flex;
    position: absolute;
    z-index: 1500;
    display: none;
    left: 0;
}

.vd-ui-cascader .vd-ui-cascader-modal.width {
    width: 100%;
}

.vd-ui-cascader .vd-ui-cascader-modal.appear-up {
    transform-origin: center bottom;
    animation: appear 0.22s ease-out;
}

.vd-ui-cascader .vd-ui-cascader-modal.appear-down {
    transform-origin: center top;
    animation: appear 0.22s ease-out;
}

.vd-ui-cascader .vd-ui-cascader-modal .loading-li {
    box-sizing: border-box;
    width: 300px;
    overflow: auto;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    background-color: #fff;
    padding: 5px 0px;
    list-style-type: none;
    margin: 0;
}

.vd-ui-cascader .vd-ui-cascader-modal .loading-li p {
    padding-left: 10px;
    height: 29px;
    line-height: 29px;
}

.vd-ui-cascader .vd-ui-cascader-modal .loading-li p i {
    animation: loading 1.8s linear infinite;
    display: inline-block;
    position: relative;
    top: 2px;
    font-size: 16px;
    margin-right: 5px;
    color: #09F;
}

.vd-ui-cascader .vd-ui-cascader-modal .failed-li {
    box-sizing: border-box;
    width: 300px;
    overflow: auto;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    background-color: #fff;
    padding: 5px 0px;
    list-style-type: none;
    margin: 0;
}

.vd-ui-cascader .vd-ui-cascader-modal .failed-li p {
    padding-left: 10px;
    height: 29px;
    line-height: 29px;
}

.vd-ui-cascader .vd-ui-cascader-modal .failed-li p i {
    position: relative;
    top: 2px;
    font-size: 16px;
    color: #E64545;
    margin-right: 5px;
}

.vd-ui-cascader .vd-ui-cascader-modal .failed-li p .reload {
    color: #09F;
    cursor: pointer;
}

.vd-ui-cascader .suggestion-list {
    width: 100%;
    box-sizing: border-box;
    overflow: auto;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    background-color: #ffffff;
    padding: 5px 0px;
    list-style-type: none;
    margin: 0;
    display: none;
}

.vd-ui-cascader .suggestion-list .filter-list {
    padding: 0 10px;
    max-height: 200px;
    overflow-y: auto;
}

.vd-ui-cascader .suggestion-list .filter-list .filter-item {
    height: 33px;
    line-height: 33px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.vd-ui-cascader .suggestion-list .filter-list .filter-item.active {
    color: #09f;
}

.vd-ui-cascader .suggestion-list .no-filter {
    padding: 5px 0;
    color: #999;
    text-align: center;
}

.vd-ui-cascader .select-wrap {
    display: inline-flex;
}

@keyframes appear {
    0% {
        opacity: 0;
        -webkit-transform: scale(1, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        opacity: 1;
    }
}

@keyframes loading {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.vd-ui-cascader-node {
    width: 200px;
    height: 310px;
    box-sizing: border-box;
    overflow: auto;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    background-color: #fff;
    padding: 5px 0px;
    list-style-type: none;
    margin: 0;
}

.vd-ui-cascader-node.multiple {
    width: 200px !important;
}

.vd-ui-cascader-node.appear-up {
    transform-origin: center bottom;
    animation: appear 0.22s ease-out;
}

.vd-ui-cascader-node.appear-down {
    transform-origin: center top;
    animation: appear 0.22s ease-out;
}

.vd-ui-cascader-node li {
    width: 100%;
    cursor: pointer;
    padding: 0px 10px;
    padding-right: 36px;
    height: 33px;
    box-sizing: border-box;
    line-height: 33px;
    position: relative;
    display: flex;
    align-items: center;
    background: #fff !important;
    text-align: left;
    border-top: 0;
}


.vd-ui-cascader-node li:hover {
    background: #f5f7fa !important;
}

.vd-ui-cascader-node li p {
    margin: 0;
    flex: 1;
    font-size: 14px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.vd-ui-cascader-node li .J-opt-icon>i {
    background: none;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
}

.vd-ui-cascader-node li .J-opt-icon .icon-selected2 {
    font-size: 16px;
    display: none;
}

.vd-ui-cascader-node li.selected {
    color: #09f;
}

.vd-ui-cascader-node li.selected .icon-selected2 {
    display: block;
}

.ui-cascader-checkbox-wrap {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    border: solid 1px #ddd;
    border-radius: 3px;
    margin-right: 5px;
}

.ui-cascader-checkbox-wrap.active {
    border: solid 1px #09f;
    background: #09f;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ui-cascader-checkbox-wrap.active .icon-selected2 {
    font-size: 12px;
    color: #fff;
}

.ui-cascader-checkbox-wrap.active .icon-deduct {
    font-size: 12px;
    color: #fff;
}