package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.erp.saleorder.api.QuoteInfoService;
import com.vedeng.erp.saleorder.dao.QuSaleHistMapper;
import com.vedeng.erp.saleorder.dao.QuoteInfoMapper;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.dto.QuoteGoodsInfoDto;
import com.vedeng.erp.saleorder.dto.QuoteInfoDto;
import com.vedeng.erp.saleorder.model.dto.QuSaleHist;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.SaleorderGoods;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 报价单api类
 */
@Service("QuoteInfoService")
public class QuoteInfoServiceImpl extends BaseSaleOrderService implements QuoteInfoService {

    @Resource
    private QuoteInfoMapper quoteInfoMapper;

    @Resource
    private QuSaleHistMapper quSaleHistMapper;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Override
    public QuoteInfoDto getQuoteInfoForGe(String quoteorderNo) {
        QuoteInfoDto quoteInfoDto = quoteInfoMapper.queryInfoByNo(quoteorderNo);
        if(quoteInfoDto != null){
            List<QuoteGoodsInfoDto> quoteGoodsInfoDtos = quoteInfoMapper.queryGoodsInfoById(quoteInfoDto.getQuoteorderId());
            quoteInfoDto.setQuoteGoodsInfoDtos(quoteGoodsInfoDtos);
        }
        return quoteInfoDto;
    }

    @Override
    public void saveQuSaleHist(Integer quoteorderId, Integer saleorderId) {
        List<QuoteGoodsInfoDto> quoteGoodsInfoDtoList = quoteInfoMapper.queryGoodsInfoById(quoteorderId);

        List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorderId);
        HashMap<String,QuSaleHist> map = new HashMap<>();
        for (QuoteGoodsInfoDto quoteGoodsInfoDto : quoteGoodsInfoDtoList) {
            if (quoteGoodsInfoDto.getGoodsId() > 0){
                QuSaleHist quSaleHist = new QuSaleHist();
                quSaleHist.setSaleorderId(saleorderId);
                quSaleHist.setQuoteorderId(quoteorderId);
                quSaleHist.setSku(quoteGoodsInfoDto.getSku());
                quSaleHist.setGoodsId(quoteGoodsInfoDto.getGoodsId());
                quSaleHist.setQuNum(quoteGoodsInfoDto.getNum());
                quSaleHist.setSaleNum(0);
                quSaleHist.setPrice(quoteGoodsInfoDto.getPrice());
                quSaleHist.setCreator(1);
                quSaleHist.setUpdater(1);
                map.put(quoteGoodsInfoDto.getSku(),quSaleHist);
            }
        }

        for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
            QuSaleHist quSaleHist = map.get(saleorderGoods.getSku());
            if(quSaleHist != null){
                quSaleHist.setSaleNum(saleorderGoods.getNum());
            }
        }

        for (QuSaleHist value : map.values()) {
            quSaleHistMapper.insertSelective(value);
        }
    }

    @Override
    public boolean isExistQuSaleHist(Integer saleorderId) {
        List<QuSaleHist> histList = quSaleHistMapper.getHistInfoBySaleorderId(saleorderId);
        if(CollectionUtils.isNotEmpty(histList)){
            return true;
        }
        return false;
    }
}
