package com.vedeng.erp.trader.service.impl;

import com.vedeng.erp.trader.domain.entity.DwhTraderTagChangeErpEntity;
import com.vedeng.erp.trader.mapper.DwhTraderTagChangeErpMapper;
import com.vedeng.erp.trader.service.DwhTraderTagChangeErpService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 大数据客户标签更新记录表,表服务实现类
 */
@Service
public class DwhTraderTagChangeErpServiceImpl implements DwhTraderTagChangeErpService {
    @Resource
    private DwhTraderTagChangeErpMapper dwhTraderTagChangeErpMapper;
    @Override
    public int insert(DwhTraderTagChangeErpEntity record) {
        if (record != null) {
            return dwhTraderTagChangeErpMapper.insert(record);
        }
        return 0;
    }

    @Override
    public List<DwhTraderTagChangeErpEntity> selectByOperateTime(LocalDateTime operateTimeBefore, LocalDateTime operateTimeAfter) {
        List<DwhTraderTagChangeErpEntity> dwhTraderTagChangeErpEntities = dwhTraderTagChangeErpMapper.selectByOperateTime(operateTimeBefore, operateTimeAfter);
        return dwhTraderTagChangeErpEntities;
    }
}
