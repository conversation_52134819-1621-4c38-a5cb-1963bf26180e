package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.model.User;
import com.vedeng.system.dao.MessageMapper;
import com.vedeng.todolist.dto.MessageTodoDto;
import com.vedeng.todolist.dto.SupplyChainAdminTodoDtoBuild;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 未读消息列表
 */
@Service
public class MessageTodoAdminVisitor implements TodoVisitor<SupplyChainAdminTodoDtoBuild>{

    @Resource
    private MessageMapper messageMapper;

    @Override
    public void visitor(SupplyChainAdminTodoDtoBuild supplyChainAdminTodoDtoBuild) {

        User user = ThreadLocalContext.get("userInfo");

        MessageTodoDto messageTodoDto = new MessageTodoDto();
        messageTodoDto.setUnReadMessageCount(messageMapper.getUnMessageCount(user.getUserId()));
        messageTodoDto.setUnReadMessageList(messageMapper.getUnReadMessageListTop10(user.getUserId()));

        supplyChainAdminTodoDtoBuild.get().setMessageTodoDto(messageTodoDto);
    }

}
