.card-head-info {
    padding: 15px;
    font-size: 16px;
    font-weight: 700;
    background: #fff;
}

.card-form-wrap {
    padding: 10px;

    .card-form-block {
        border-radius: 5px;
        background: #fff;
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }

        .card-form-title {
            padding: 10px;

            .must {
                color: #E64545;
            }
        }

        .card-location-wrap {
            padding: 0 10px 10px 10px;
        }

        .card-error {
            color: #e64545;
            display: flex;
            align-items: center;

            .icon-error2 {
                font-size: 16px;
                margin-right: 5px;
            }
        }

        .card-location-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 15px;
            background: #F5F7FA;
            border-radius: 3px;
        
            .info-l {
                .info-txt {
                    margin-bottom: 5px;
                    display: flex;
                    align-items: center;

                    .icon-yes2 {
                        font-size: 16px;
                        margin-right: 5px;
                        color: #13BF13;
                    }

                    &.done {
                        color: #13BF13;
                        font-weight: 700;
                    }

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

            .info-r {
                display: flex;
                align-items: center;
                color: #09f;

                .icon-address {
                    font-size: 16px;
                    margin-right: 5px;
                }
            }
        }

        .card-location-btn {
            margin-top: 10px;

            @keyframes loading {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            .btn-inner {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;

                .vd-ui_icon {
                    font-size: 16px;
                    margin-right: 5px;
                
                    &.icon-loading {
                        animation: loading 2s linear infinite;
                    }
                }
            }
        }

        .card-upload-wrap {
            padding: 0 10px 10px 10px;
        }

        .form-tip {
            color: #999;
            margin-top: 5px;
        }
    }
}

.card-footer {
    background: #fff;
    padding: 10px;
    border-top: solid 1px #EBEFF2;
    position: fixed;
    bottom: 0;
    width: 100%;
    max-width: 768px;
    padding-bottom: calc(10px + constant(safe-area-inset-bottom));
    padding-bottom: calc(10px + env(safe-area-inset-bottom));;
}

.card-reload-tip {
    display: flex;
    align-items: center;
    margin-top: 15px;
    padding-bottom: 5px;

    .icon-info1 {
        font-size: 16px;
        color: #666;
        margin-right: 5px;
    }

    .tip-link {
        color: #09f;
        margin-left: 10px;
    }
}