package com.vedeng.erp.broadcast.mapdao;


import com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity;

/**
 * 全局播报配置Mapper
 */
public interface BroadcastGlobalConfigMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastGlobalConfigEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastGlobalConfigEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastGlobalConfigEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastGlobalConfigEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastGlobalConfigEntity record);

    BroadcastGlobalConfigEntity selectGlobalConfig();
}
