package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSalesTrader;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSalesTraderMapper {
    int deleteByPrimaryKey(Integer afterSalesTraderId);

    int insert(AfterSalesTrader record);

    int insertSelective(AfterSalesTrader record);

    AfterSalesTrader selectByPrimaryKey(Integer afterSalesTraderId);

    int updateByPrimaryKeySelective(AfterSalesTrader record);

    int updateByPrimaryKey(AfterSalesTrader record);
    
    List<AfterSalesTrader> getAfterSalesTraderList(@Param("afterSalesId") Integer afterSalesId);
    
    /**
     * <b>Description:</b><br> 插入前判断是否存在同名称同类型的
     * @param record
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2018年9月5日 上午9:09:12
     */
    int getAfterSalesTrader(AfterSalesTrader record);
}