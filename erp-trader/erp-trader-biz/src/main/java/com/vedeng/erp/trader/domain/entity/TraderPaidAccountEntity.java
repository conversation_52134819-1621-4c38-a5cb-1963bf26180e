package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 交易者已交易账号
 * @date 2022/8/11 15:51
 */
@Getter
@Setter
public class TraderPaidAccountEntity extends BaseEntity {

    /**
     * 主键
     */
    private Integer traderPaidAccountId;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 交易者银行交易账号
     */
    private String traderPaidAccount;

    /**
     * 是否删除
     */
    private Integer isDel;


}
