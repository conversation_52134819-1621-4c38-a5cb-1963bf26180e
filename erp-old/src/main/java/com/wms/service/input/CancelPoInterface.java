package com.wms.service.input;

import com.vedeng.common.util.StringUtil;
import com.wms.constant.WMSContant;
import com.wms.dto.CancelPoDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

/**
 * 取消订单接口
 */
@Service
public class CancelPoInterface extends AbstractWmsInterface {


    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMessageId(WMSContant.CANCEL_PO);
        wmsRequest.setMethod(WMSContant.CANCEL_PO);
    }

    /**
     * 获取请求参数
     * @return
     */
    @Override
    protected JSONObject getXmlDate(Object ... param) {

        CancelPoDto cancelPoDto = (CancelPoDto)param[0];

        JSONObject item = new JSONObject();
        String warehouseId = cancelPoDto.getWarehouseId();
        if(StringUtil.isBlank(warehouseId)){
            warehouseId = "NJ01";
        }
        item.put("customerId","VEDENG");
        item.put("warehouseId",warehouseId);

        item.put("docNo",cancelPoDto.getDocNo());
        item.put("poType",cancelPoDto.getPoType());
        item.put("erpCancelReason",cancelPoDto.getErpCancelReason());

        JSONObject ordernoJsonObj = new JSONObject();
        ordernoJsonObj.put("ordernos",item);

        JSONObject dataJsonObj = new JSONObject();
        dataJsonObj.put("data",ordernoJsonObj);

        return dataJsonObj;
    }

    /**
     * 解析响应数据，如果有的话
     * @param returnObject
     * @return
     */
    @Override
    protected String parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
