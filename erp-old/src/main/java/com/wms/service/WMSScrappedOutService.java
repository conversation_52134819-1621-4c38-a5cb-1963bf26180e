package com.wms.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.wms.model.dto.AddScrappedOutDto;
import com.wms.model.dto.WMSScrappedOutQueryDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WMSScrappedOutOrder;
import com.wms.model.po.WmsOutputOrder;

import java.util.List;
/**
 * 报废出库单
 */
public interface WMSScrappedOutService {

    List<WMSScrappedOutOrder> queryScrapedOutlistPage(WMSScrappedOutQueryDto scrappedOutQueryDto, Page page);

    Integer addScrappedOutOrder(AddScrappedOutDto scrappedOutDto, User user);

    WMSScrappedOutOrder findScrappedOutById(Long scrappedOutId);

    List<WmsOutputOrderGoodsDto> queryOutputGoodsByScrappedOutId(Long scrappedOutOrderId);

    void putScrappedOutOrder(Integer scrappedOutOrderId) throws Exception;

    void addWmsLogicalOrderGodos(User user, Integer scrappedOutOrderId);

    WmsOutputOrder getScrappedOrderByNo(String scrappedNo);

    List<WarehouseGoodsOperateLog> getWlogList(Integer wmsOutputOrderId, Integer logOperateType);
}
