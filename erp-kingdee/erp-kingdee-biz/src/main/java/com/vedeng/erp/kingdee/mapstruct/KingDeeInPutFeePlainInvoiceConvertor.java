package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeePlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDto;
import org.mapstruct.*;

import java.util.List;

/**
 * 金蝶 进项票 dto 转 entity
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeInPutFeePlainInvoiceConvertor extends BaseMapStruct<KingDeeInPutFeePlainInvoiceEntity,InPutFeePlainInvoiceDto> {
    /**
     * KingDeeOutPutFeePlainInvoiceEntity
     *
     * @param dto OutPutFeePlainInvoiceDto
     * @return KingDeeOutPutFeePlainInvoiceEntity
     */
    @Mapping(target = "fpurexpinventry", source = "FPUREXPINVENTRY", qualifiedByName = "listToString")
    @Override
    KingDeeInPutFeePlainInvoiceEntity toEntity(InPutFeePlainInvoiceDto dto);

    /**
     * dto 原List 转 JSON
     *
     * @param source 对象
     * @return String String
     */
    @Named("listToString")
    default String listToString(List<InPutFeePlainInvoiceDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSON.toJSONString(source);
    }
}
