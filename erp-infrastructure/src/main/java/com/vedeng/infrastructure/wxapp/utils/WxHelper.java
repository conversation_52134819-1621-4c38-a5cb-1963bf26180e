package com.vedeng.infrastructure.wxapp.utils;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import com.vedeng.common.redis.utils.RedisUtil;
import java.util.concurrent.TimeUnit;

import javax.security.auth.login.FailedLoginException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * WxHelper
 */
@Slf4j
public class WxHelper {

    public static final String APP_ID = ConfigService.getAppConfig().getProperty("wx.corp-id", "");
    public static final String CRM_PC_AGENT_ID = ConfigService.getAppConfig().getProperty("wx.agent-id", "");
    public static final String CRM_PC_CORPSECRET = ConfigService.getAppConfig().getProperty("wx.corp-secret", "");

    // Redis key前缀
    private static final String REDIS_WX_ACCESS_TOKEN_KEY = "wx:access_token";
    private static final String REDIS_WX_EXPIRES_IN_KEY = "wx:expires_in";
    private static final String REDIS_WX_TICKET_KEY_PREFIX = "wx:ticket:";
    
    public static final String ERR_CODE_KEY = "errcode";
    
    /**
     * 获取企业微信认证token的工具类，token缓存多次使用
     */
    private static String getAccessToken(boolean isRefresh, int retryCount) {
        long newTime = System.currentTimeMillis();
        
        // 从Redis获取token和过期时间
        String accessToken = RedisUtil.StringOps.get(REDIS_WX_ACCESS_TOKEN_KEY);
        String expiresInStr = RedisUtil.StringOps.get(REDIS_WX_EXPIRES_IN_KEY);
        Long expiresIn = expiresInStr != null ? Long.parseLong(expiresInStr) : null;
        
        if (StrUtil.isNotEmpty(accessToken) && expiresIn != null && expiresIn > newTime && !isRefresh) {
            return accessToken;
        } else {
            String tokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("corpid", APP_ID);
            paramMap.put("corpsecret", CRM_PC_CORPSECRET);
            String s = HttpUtil.get(tokenUrl, paramMap);
            JSONObject tokenJson = JSONObject.parseObject(s);
            if (0 == tokenJson.getInteger(ERR_CODE_KEY)) {
                accessToken = tokenJson.getString("access_token");
                expiresIn = newTime + tokenJson.getLong("expires_in") * 1000;
                
                // 存储到Redis,设置过期时间为2小时
                RedisUtil.StringOps.setEx(REDIS_WX_ACCESS_TOKEN_KEY, accessToken, 2, TimeUnit.HOURS);
                RedisUtil.StringOps.setEx(REDIS_WX_EXPIRES_IN_KEY, String.valueOf(expiresIn), 2, TimeUnit.HOURS);
                
                return accessToken;
            } else {
                if (retryCount > 0) {
                    log.info("获取accessToken失败，重试次数：" + retryCount);
                    return getAccessToken(true, retryCount - 1);
                } else {
                    throw new ServiceException("获取accessToken失败");
                }
            }
        }
    }


    public static String getJsApiTicket(String type) {
        String accessToken = getAccessToken(false, 3);
        if (!StrUtil.isEmpty(accessToken)) {
            String key = accessToken;
            if ("agent_config".equals(type)) {
                key = type + "_" + accessToken;
            }
            
            // 从Redis中获取ticket
            String ticketKey = REDIS_WX_TICKET_KEY_PREFIX + key;
            String ticketJson = RedisUtil.StringOps.get(ticketKey);
            Ticket ticket = null;
            if (ticketJson != null) {
                ticket = JSON.parseObject(ticketJson, Ticket.class);
            }
            
            if (ticket != null && isTicketValid(ticket)) {
                return ticket.getTicket();
            }
            
            ticket = getJsApiTicketFromWeChatPlatform(accessToken, type);
            if (ticket != null) {
                // 存储到Redis，设置7200秒(2小时)过期
                RedisUtil.StringOps.setEx(ticketKey, 
                    JSON.toJSONString(ticket), 
                    2, 
                    TimeUnit.HOURS);
                return ticket.getTicket();
            }
        }
        return null;
    }

    private static boolean isTicketValid(Ticket ticket) {
        return ticket.getExpiresIn() > System.currentTimeMillis();
    }

    private static Ticket getJsApiTicketFromWeChatPlatform(String accessToken, String type) {
        String url;
        if ("agent_config".equals(type)) {
            url = "https://qyapi.weixin.qq.com/cgi-bin/ticket/get?access_token=" + accessToken + "&type=" + type;
        } else {
            url = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=" + accessToken;
        }
        try {
            String body = HttpUtil.get(url);
            log.info("ticketContent = " + body);
            if (!StrUtil.isEmpty(body)) {
                JSONObject object = JSON.parseObject(body);
                if (object.getIntValue("errcode") == 0) {
                    Ticket ticket = new Ticket();
                    ticket.setTicket(object.getString("ticket"));
                    ticket.setExpiresIn(System.currentTimeMillis() + object.getLongValue("expires_in") * 1000);
                    return ticket;
                }
            }
        } catch (Exception e) {
            log.error("Failed to get jsapi ticket from WeChat platform", e);
        }
        return null;
    }

    public static String getJSSDKSignature(String ticket, String nonceStr, long timestamp, String url) throws NoSuchAlgorithmException {
        String unEncryptStr = "jsapi_ticket=" + ticket + "&noncestr=" + nonceStr + "&timestamp=" + timestamp + "&url=" + url;
        MessageDigest sha = MessageDigest.getInstance("SHA");
        byte[] cipherBytes = sha.digest(unEncryptStr.getBytes());
        return Hex.encodeHexString(cipherBytes);
    }

    /**
     * 获取微信用户登录信息
     * @param code
     * @return
     */
    public static String getUserInfo(String code){
        String userInfoUrl =String.format("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=%s&code=%s",getAccessToken(false,3),code) ;
        String dataStr = HttpUtil.get(userInfoUrl,30000);
        JSONObject data = JSON.parseObject(dataStr);
        Integer errorCode = (Integer) data.get("errcode");
        log.info("根据code获取用户信息：{}",JSON.toJSONString(data));
        if(errorCode != 0 ) {
            if(errorCode==40029){
                log.error("获取用户信息失败,原因：code只能消费一次或者code需要在有效期间消费（5分钟），过期自动失效");
                return "codeExpire";
            }else if(errorCode == 50001) {
                log.error("获取用户信息失败,原因：redirect_url未登记可信域名,查看：https://developer.work.weixin.qq.com/document/path/90313#%E9%94%99%E8%AF%AF%E7%A0%81%EF%BC%9A50001");
            }
            else{
                log.error("获取用户信息失败,原因：{}", String.valueOf(data.get("errmsg")));
            }
            return null;
        }
        String userId = String.valueOf(data.get("userid"));
        return userId;
    }


    public static File downloadFile(String mediaId ) throws IOException {
        String downloadUrl =String.format("https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=%s&media_id=%s",getAccessToken(false,3),mediaId) ;
        HttpURLConnection conn = (HttpURLConnection) new URL(downloadUrl).openConnection();
        conn.setRequestMethod("GET");

        try {
            // 获取并解析响应头
            Map<String, List<String>> headers = conn.getHeaderFields();
            String fileName = extractFileNameFromHeaders(headers);

            // 构建输出文件路径
            String outputDir =  System.getProperty("java.io.tmpdir");
            File outputFile = new File(outputDir, fileName);

            // 下载文件
            try (InputStream inputStream = conn.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(outputFile)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            return outputFile;
        }catch (Exception e){
            log.error("获取微信素材失败", e);
            return null;
        }finally {
            conn.disconnect();
        }

    }

    private static String extractFileNameFromHeaders(Map<String, List<String>> headers) throws IOException {
        // 使用不区分大小写的方式查找 Content-Disposition 头
        String targetHeader = "content-disposition"; // 统一转换为小写进行匹配
        String matchedHeaderKey = null;

        for (String headerKey : headers.keySet()) {
            if (headerKey!= null && headerKey.toLowerCase().equals(targetHeader)) { // 忽略大小写比较
                matchedHeaderKey = headerKey;
                break;
            }
        }


        List<String> cdHeaders = headers.get(matchedHeaderKey);
        Pattern pattern = Pattern.compile("filename=\"([^\\\"]+)\"");

        for (String cd : cdHeaders) {
            Matcher matcher = pattern.matcher(cd);
            if (matcher.find()) {
                String encodedName = matcher.group(1);
                return URLDecoder.decode(encodedName, StandardCharsets.UTF_8.name());
            }
        }

        throw new IOException("Invalid Content-Disposition format");
    }

    public static void downloadWxFile(String mediaId,String destination){
        String downloadUrl =String.format("https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=%s&media_id=%s",getAccessToken(false,3),mediaId) ;
        try {
            downloadFile(downloadUrl,destination);
        } catch (IOException e) {
            log.error("downloadWxFile IOException",e);
            throw new ServiceException("下载文件失败");
        }

    }


    private static void downloadFile(String downloadUrl, String destination) throws IOException {
        HttpURLConnection conn = (HttpURLConnection) new URL(downloadUrl).openConnection();
        conn.setRequestMethod("GET");

        // 输入流读取文件
        InputStream inputStream = conn.getInputStream();
        FileOutputStream outputStream = new FileOutputStream(destination);

        byte[] buffer = new byte[4096];
        int bytesRead;

        // 将文件写入本地
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }

        outputStream.close();
        inputStream.close();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WxConfig {
        private String corpid;
        private String agentid;
        private long timestamp;
        private String nonceStr;
        private String signature;
        private String ssoUrl;
        private String appUrl;
    }

    @Data
    public static class Ticket {
        private String ticket;
        private Long expiresIn;
    }

}
