package com.vedeng.erp.kingdee.enums;

import java.util.Objects;

public enum KingdeeIgnoreUnitTypeEnums {
    CSTOMER(1,"BD_Customer"),
    SUPPLIER(2,"BD_Supplier"),
    BANK(4,"BD_BANK"),
    OTHER(3,"FIN_OTHERS");

    private Integer code;
    private String unitType;

    KingdeeIgnoreUnitTypeEnums(Integer code, String unitType) {
        this.code = code;
        this.unitType = unitType;
    }
    public static String getUnitType(Integer code){
        for (KingdeeIgnoreUnitTypeEnums value : KingdeeIgnoreUnitTypeEnums.values()) {
            if (Objects.equals(value.code, code)){
                return value.unitType;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }
}
