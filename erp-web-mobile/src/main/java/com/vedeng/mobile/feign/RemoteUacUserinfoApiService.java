package com.vedeng.mobile.feign;

import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.uac.api.dto.UserDTO;
import com.vedeng.uac.api.dto.UserInfoDto;
import com.vedeng.uac.api.dto.UserLoginInfo;
import feign.Body;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignApi(serverName = "uacServer")
public interface RemoteUacUserinfoApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/uac/getUacUserInfo")
    UserInfoDto getUacUserInfo(@RequestBody UserDTO userDTO);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/uac/getUacUserLoginInfo")
    @Body("{userName}")
    UserLoginInfo getUacUserLoginInfo(@Param("userName") String userName);
}
