package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.UnitKingDee;

public interface UnitKingDeeMapper {
    /**
     * delete by primary key
     * @param unitKingDeeId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer unitKingDeeId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(UnitKingDee record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UnitKingDee record);

    /**
     * select by primary key
     * @param unitKingDeeId primary key
     * @return object by primary key
     */
    UnitKingDee selectByPrimaryKey(Integer unitKingDeeId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UnitKingDee record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UnitKingDee record);
}