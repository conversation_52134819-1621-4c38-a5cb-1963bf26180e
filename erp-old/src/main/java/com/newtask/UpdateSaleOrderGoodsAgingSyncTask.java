package com.newtask;


import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.AgingTypeEnum;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.WarnLevelEnum;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.SaleOrderWarnVo;
import com.vedeng.order.model.SaleOrderWarning;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.UpdateSaleOrderGoodsAgingService;
import com.vedeng.system.service.UserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 更新待采购订单列表，产品时效状态和预警等级
 * @Author: Davis.yu
 * @Date: 2021/4/13 下午4:30
 * @Version: 1.0
 */
@Component
@JobHandler(value = "UpdateSaleOrderGoodsAgingSyncTask")
public class UpdateSaleOrderGoodsAgingSyncTask extends AbstractJobHandler {

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private UpdateSaleOrderGoodsAgingService updateSaleOrderGoodsAgingService;

    @Autowired
    private UserService userService;

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateSaleOrderGoodsAgingSyncTask.class);

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        LOGGER.info("开始执行更新待采购订单列表，产品时效状态和预警等级任务,时间:{}", param);
        XxlJobLogger.log("开始执行更新待采购订单列表，产品时效状态和预警等级任务,时间:{}", param);
        GoodsVo goodsVo = new GoodsVo();
        goodsVo.setOrderType(-1);
        goodsVo.setCompanyId(1);
        goodsVo.setIsJob(0);
        // 获取立即采购的列表数据
        goodsVo.setComponentId(2);
        Page page = Page.newBuilder(1, 10, "");
        Map<String, Object> buyOrderMap = buyorderService.getSaleorderGoodsVoListPage(goodsVo, page);

        if (buyOrderMap == null) {
            return new ReturnT<>(0, "暂无数据处理");
        }
        long nowTime = DateUtil.gainNowDate();

        List<SaleorderVo> list = (List<SaleorderVo>) buyOrderMap.get("list");
        // 剔除备货单 剔除锁定订单
        List<SaleorderVo> saleOrderVoList = list.stream().filter(item -> item.getOrderType() != 2).collect(Collectors.toList())
                .stream().filter(item -> (item.getLockedStatus() == null || item.getLockedStatus() == 0)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(saleOrderVoList)) {
            return new ReturnT<>(0, "暂无数据处理");
        }
        //根据订单号查询归属人
        for (SaleorderVo saleorderVo : saleOrderVoList) {
            Map<String, Map<String, List<SaleorderGoodsVo>>> userOrderMap = new HashMap<>();
            List<SaleorderGoodsVo> sgvList = saleorderVo.getSgvList();
            if (CollectionUtils.isEmpty(sgvList)) {
                continue;
            }
            for (SaleorderGoodsVo sgv : sgvList) {
                // 采购要求为暂缓采购或者无需采购，剔除
//                if (sgv.getComponentId() != null && (sgv.getComponentId() == 3 || sgv.getComponentId() == 14)) {
//                    continue;
//                }
                // 没有可采购时间不处理
                if (sgv.getAgingTime() == null) {
                    continue;
                }
                List<User> userList = updateSaleOrderGoodsAgingService.getOrderAssistant(sgv);
                if (CollectionUtils.isEmpty(userList)) {
                    continue;
                }
                for (User user : userList) {
                    sgv.setUserId(user.getUserId());
                    sgv.setUserName(user.getUsername());
                    long startTime = sgv.getAgingTime();
                    long agingTime = nowTime - startTime;
                    int hour = (int) (agingTime / (3600 * 1000));
                    // 正常状态，啥都不操作
                    if (hour < 8) {
                        continue;
                    }
                    // 查询该条记录是否已经发生过预警
                    XxlJobLogger.log("处理订单详情：{}", sgv);
                    SaleOrderWarning saleOrderWarning = null;
                    List<SaleOrderWarning> saleOrderWarningList = updateSaleOrderGoodsAgingService.getHistorySaleOrderWarn(sgv);
                    if (!CollectionUtils.isEmpty(saleOrderWarningList)) {
                        saleOrderWarning = saleOrderWarningList.get(0);
                    }
                    // 时效状态正常
                    if (8 <= hour && hour < 12) { //临期
                        // 订单是预警状态
                        if (saleOrderWarning != null) {
                            // 预警记录也是临期，不作处理
                            if (AgingTypeEnum.APPROACH.getCode() == saleOrderWarning.getAging()) {
                                continue;
                            }
                        }
                        sgv.setWarnStatus(ErpConst.ONE);
                        sgv.setWarnLevel(WarnLevelEnum.THREE_L.getCode());
                        sgv.setAging(AgingTypeEnum.APPROACH.getCode());
                        sgv.setIsWarn(ErpConst.ONE);
                        sgv.setOverDue(0);
                        dealMap(userOrderMap, sgv, user, ErpConst.ONE);
                    } else if (hour >= 12) { // 逾期
                        if (saleOrderWarning != null) {
                            if (AgingTypeEnum.OVERDUE.getCode() == saleOrderWarning.getAging()) {
                                // 如果之前发的是逾期，判断是否逾期是否超过12个小时
                                long warnTime = saleOrderWarning.getWarnTime();
                                int time = (int) ((nowTime - warnTime) / (3600 * 1000));
                                if (time >= 12) {
                                    sgv.setWarnStatus(ErpConst.THREE);
                                    sgv.setWarnLevel(WarnLevelEnum.ONE_L.getCode());
                                    sgv.setAging(AgingTypeEnum.OVERDUE.getCode());
                                    sgv.setIsWarn(ErpConst.ONE);
                                    Integer overDue = saleOrderWarning.getOverDue();
                                    sgv.setOverDue(overDue + 1);
                                    dealMap(userOrderMap, sgv, user, ErpConst.THREE);
                                    String username = user.getUsername();
                                    if (overDue > 0) {
                                        for (int i = 0; i < overDue + 1; i++) {
                                            // 查询上级,给上级发消息
                                            List<User> pUserList = userService.getUserParentDetailInfo(user.getUserId());
                                            if (CollectionUtils.isEmpty(pUserList)) {
                                                break;
                                            }
                                            User pUser = new User();
                                            boolean breakFlag = false;
                                            for (User _user : pUserList) {
                                                // 总监级别，不在往上级通知
                                                if (_user.getPositLevel() == 442) {
                                                    breakFlag = true;
                                                }
                                                pUser = _user;
                                            }
                                            user = pUser;
                                            // 设置名称为订单助理名称，发送消息使用
                                            pUser.setUsername(username);
                                            dealMap(userOrderMap, sgv, pUser, ErpConst.THREE);
                                            // 如果上级中包含总监职位，停止发送
                                            if (breakFlag) {
                                                break;
                                            }
                                        }
                                    }
                                }
                                continue;
                            }
                        }
                        sgv.setWarnLevel(WarnLevelEnum.TWO_L.getCode());
                        sgv.setAging(AgingTypeEnum.OVERDUE.getCode());
                        sgv.setIsWarn(ErpConst.ONE);
                        sgv.setOverDue(1);
                        dealMap(userOrderMap, sgv, user, ErpConst.TWO);
                        // 查询上级,给上级发消息
                        List<User> pUserList = userService.getUserParentDetailInfo(user.getUserId());
                        if (CollectionUtils.isEmpty(pUserList)) {
                            continue;
                        }
                        User pUser = pUserList.get(0);
                        // 设置名称为订单助理名称，发送消息使用
                        pUser.setUsername(user.getUsername());
                        dealMap(userOrderMap, sgv, pUser, ErpConst.TWO);
                    }
                }
            }

            if (userOrderMap == null) {
                continue;
            }

            Set<Map.Entry<String, Map<String, List<SaleorderGoodsVo>>>> userOrderEntrySet = userOrderMap.entrySet();
            for (Map.Entry<String, Map<String, List<SaleorderGoodsVo>>> userOrderEntry : userOrderEntrySet) {
                Map<String, List<SaleorderGoodsVo>> saleOrderGoodsMap = userOrderEntry.getValue();
                String userKey = userOrderEntry.getKey();
                Integer userId = Integer.parseInt(userKey.split("_")[0]);
                String userName = userKey.split("_")[1];
                Set<Map.Entry<String, List<SaleorderGoodsVo>>> mapEntrySet = saleOrderGoodsMap.entrySet();
                for (Map.Entry<String, List<SaleorderGoodsVo>> entry : mapEntrySet) {
                    SaleOrderWarnVo saleOrderWarnVo = new SaleOrderWarnVo();
                    String key = entry.getKey();
                    Integer warnStatus = Integer.parseInt(key.split("_")[0]);
                    String saleOrderNo = key.split("_")[1];
                    saleOrderWarnVo.setUserId(userId);
                    saleOrderWarnVo.setUsername(userName);
                    saleOrderWarnVo.setWarnStatus(warnStatus);
                    saleOrderWarnVo.setSaleorderNo(saleOrderNo);
                    List<SaleorderGoodsVo> saleOrderList = entry.getValue();
                    saleOrderWarnVo.setSaleorderGoodsVoList(saleOrderList);
                    updateSaleOrderGoodsAgingService.sendSaleOrder(saleOrderWarnVo);
                }
            }
        }

        LOGGER.info("结束执行更新待采购订单列表，产品时效状态和预警等级任务,时间:{}", param);
        XxlJobLogger.log("结束执行更新待采购订单列表，产品时效状态和预警等级任务,时间:{}", param);

        return SUCCESS;

    }

    /**
     * 处理发送消息人员与订单的关联关系
     * Map<userId_userName, Map<warnStatus_saleorderNo, List<SaleorderGoodsVo>>>
     * @param userOrderMap 处理完成订单详情集合
     * @param sgv 当前订单
     * @param user 当前用户
     * @param i 处理级别
     */
    private void dealMap(Map<String, Map<String, List<SaleorderGoodsVo>>> userOrderMap, SaleorderGoodsVo sgv, User user, int i) {
        Map<String, List<SaleorderGoodsVo>> saleOrderGoodsMap;
        String key = i + "_" + sgv.getSaleorderNo();
        String userKey = user.getUserId() + "_" + user.getUsername();
        if (userOrderMap != null && userOrderMap.containsKey(userKey)) {
            saleOrderGoodsMap = userOrderMap.get(userKey);
            List<SaleorderGoodsVo> saleOrderGoodsVoList;
            if (saleOrderGoodsMap != null && saleOrderGoodsMap.containsKey(key)) {
                saleOrderGoodsVoList = saleOrderGoodsMap.get(key);
            } else {
                saleOrderGoodsVoList = new ArrayList<>();
            }
            saleOrderGoodsVoList.add(sgv);
            saleOrderGoodsMap.put(key, saleOrderGoodsVoList);
        } else {
            saleOrderGoodsMap = new HashMap<>();
            List<SaleorderGoodsVo> saleOrderGoodsVoList = new ArrayList<>();
            saleOrderGoodsVoList.add(sgv);
            saleOrderGoodsMap.put(key, saleOrderGoodsVoList);
        }
        userOrderMap.put(userKey, saleOrderGoodsMap);
    }

}
