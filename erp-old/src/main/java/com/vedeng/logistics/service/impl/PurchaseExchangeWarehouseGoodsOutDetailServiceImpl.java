package com.vedeng.logistics.service.impl;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.eums.WarehouseOutInSourceEnum;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderAddress;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 采购换货出库
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/15 20:32
 **/
@Service
@Slf4j
public class PurchaseExchangeWarehouseGoodsOutDetailServiceImpl extends BaseWarehouseGoodsOutDetailService{
    /**
     * 采购换货售后详情页 URL
     */
    private final String DETAIL_URL_AFTER_PURCHASE_EXCHANGE = "/order/newBuyorder/viewAfterSalesDetail.do?traderType=2&afterSalesId=";

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;


    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo warehouseGoodsOutVo = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.PURCHASE_EXCHANGE_WAREHOUSE_OUT.getErpCode());
        if(warehouseGoodsOutVo != null){
            // 查询出库单据 关联的售后单信息
            warehouseGoodsOutRelateBuyOrderAfterSalesInfo(warehouseGoodsOutVo);
        }
        return warehouseGoodsOutVo;
    }

    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.PURCHASE_EXCHANGE_WAREHOUSE_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(relatedNo);
        String url = "";
        if (afterSales != null){
            url = REDIRECT_URL_PREFIX + DETAIL_URL_AFTER_PURCHASE_EXCHANGE + afterSales.getAfterSalesId();
        }
        return url;
    }

    public void warehouseGoodsOutRelateBuyOrderAfterSalesInfo(WarehouseGoodsOutVo warehouseGoodsOutVo){
        String afterSalesNo = warehouseGoodsOutVo.getRelateNo();
        log.info("warehouseGoodsOutRelateBuyOrderAfterSalesInfo 出库单{} 关联查询 采购售后单 {} 信息", warehouseGoodsOutVo.getOutInNo(), afterSalesNo);
        if (StringUtils.isNotBlank(afterSalesNo)){
            AfterSalesVo afterSalesQuery = new AfterSalesVo();
            afterSalesQuery.setAfterSalesNo(afterSalesNo);
            AfterSalesVo afterSales = afterSalesMapper.viewAfterSalesDetailBuyorderByAfterSalesNo(afterSalesQuery);
            if (afterSales != null){
                // 归属人 收货客户 收货联系人信息
                warehouseGoodsOutVo.setBelongUserId(afterSales.getCreator());
                warehouseGoodsOutVo.setTakeTraderContactName(afterSales.getTraderContactName());
                warehouseGoodsOutVo.setTakeTraderContactTelephone(afterSales.getTraderContactTelephone());
                warehouseGoodsOutVo.setTakeTraderContactMobile(afterSales.getTraderContactMobile());
                String traderName = "";
                Integer traderId = afterSales.getTraderId();
                if(traderId != null){
                    Trader trader = traderMapper.selectByPrimaryKey(traderId);
                    if(trader != null && trader.getTraderName() != null){
                        traderName = trader.getTraderName();
                    }
                }
                warehouseGoodsOutVo.setTakeTraderName(traderName);

                // 收货地址 收货地区
                // 直发 取 供应商库房地址, 普发取 收货地址
                String takeTraderArea = "";
                String takeTraderAddress = "";
                if (WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource().equalsIgnoreCase(warehouseGoodsOutVo.getSource())){
                    if(traderId != null){
                        TraderSupplier traderSupplier = new TraderSupplier();
                        traderSupplier.setTraderId(traderId);
                        TraderSupplierVo traderSupplierBaseInfo = traderSupplierMapper.getTraderSupplierBaseInfo(traderSupplier);
                        if(traderSupplierBaseInfo != null){
                            takeTraderArea = (String) regionService.getRegion(traderSupplierBaseInfo.getWarehouseAreaId(), 2);
                            takeTraderAddress = traderSupplierBaseInfo.getWarehouseAddress();
                        }
                    }

                    // 直发 出库时间 取 出库单主表时间
                    List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogList = warehouseGoodsOutVo.getWarehouseGoodsOutLogList();
                    if(CollectionUtils.isNotEmpty(warehouseGoodsOutLogList)){
                        for (WarehouseGoodsOutLogVo warehouseGoodsOutLogVo : warehouseGoodsOutLogList) {
                            warehouseGoodsOutLogVo.setAddTimeStr(warehouseGoodsOutVo.getOutInTimeStr());
                        }
                    }
                } else if(WarehouseOutInSourceEnum.NORMAL_DELIVERY.getSource().equalsIgnoreCase(warehouseGoodsOutVo.getSource())){
                    Integer areaId = afterSales.getAreaId();
                    Integer addressId = afterSales.getAddressId();
                    takeTraderArea = (String) regionService.getRegion(areaId, 2);
                    TraderAddress addressInfo = traderAddressMapper.getAddressInfoById(addressId, TRADER_ADDRESS_ENABLE);
                    if(addressInfo != null && addressInfo.getAddress() != null){
                        takeTraderAddress = addressInfo.getAddress();
                    }
                }
                warehouseGoodsOutVo.setTakeArea(takeTraderArea);
                warehouseGoodsOutVo.setTakeAddress(takeTraderAddress);

                // 查询售后商品, 对金额，数量累计求和
                BigDecimal orderTotalAmount = new BigDecimal(0);
                if (afterSales.getAfterSalesId() != null){
                    AfterSalesGoods afterSalesGoodsQuery = new AfterSalesGoods();
                    afterSalesGoodsQuery.setAfterSalesId(afterSales.getAfterSalesId());
                    List<AfterSalesGoodsVo> afterSalesGoodsVoList = afterSalesGoodsMapper.getBuyorderAfterSalesGoodsVosList(afterSalesGoodsQuery);
                    for (AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodsVoList) {
                        orderTotalAmount = orderTotalAmount.add(afterSalesGoodsVo.getPrice().multiply(new BigDecimal(afterSalesGoodsVo.getNum())));
                    }
                }
                // 查询用户部门
                if(afterSales.getCreator() != null){
                    User belongUser = userMapper.selectByPrimaryKey(afterSales.getCreator());
                    warehouseGoodsOutVo.setBelongUserName(belongUser.getUsername());
                    warehouseGoodsOutVo.setBelongUserOrgName(belongUser.getOrgName());
                }
                warehouseGoodsOutVo.setOrderTotalAmount(orderTotalAmount);
            }
        }
    }
}
