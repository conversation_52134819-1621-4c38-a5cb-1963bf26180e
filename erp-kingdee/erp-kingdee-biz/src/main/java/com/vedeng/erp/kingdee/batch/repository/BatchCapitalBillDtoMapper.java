package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/2 14:29
 */
@Repository
public interface BatchCapitalBillDtoMapper {

    /**
     * 查询余额支付流水
     *
     * @param capitalBillQueryDto 查询条件
     * @return List<BatchCapitalBillDto> 符合条件的流水信息
     */
    List<BatchCapitalBillDto> findByAll(BatchCapitalBillDto capitalBillQueryDto);

    /**
     * 查询销售售后退款至余额流水
     *
     * @param capitalBillQueryDto 查询条件
     * @return List<BatchCapitalBillDto> 符合条件的流水信息
     */
    List<BatchCapitalBillDto> afterSaleToBalanceReader(BatchCapitalBillDto capitalBillQueryDto);

    /**
     * 查询客户余额支付销售订单流水
     */
    List<BatchCapitalBillDto> saleOrderPayByBalanceReader(BatchCapitalBillDto capitalBillQueryDto);

    /**
     * 查询客户余额支付销售售后手续费流水
     */
    List<BatchCapitalBillDto> afterSalePayByBalanceReader(BatchCapitalBillDto capitalBillQueryDto);

    List<BatchCapitalBillDto> afterSalePayByBalanceNegativeReader(BatchCapitalBillDto capitalBillQueryDto);

    /**
     * 查询采购售后供应商余额收款流水
     * @param capitalBillQueryDto
     * @return
     */
    List<BatchCapitalBillDto> buyOrderAfterSaleBalanceReader(BatchCapitalBillDto capitalBillQueryDto);

    /**
     * 返利余额支付
     * @param capitalBillQueryDto
     * @return
     */
    List<BatchCapitalBillDto> buyOrderSettleAfterSaleBalanceReader(BatchCapitalBillDto capitalBillQueryDto);

    /**
     * 根据银行流水id查询财务添加的交易流水
     * <AUTHOR>
     * @param bankBillId
     * @return
     */
    List<BatchCapitalBillDto> queryCapitalBillByBankBillId(@Param("bankBillId") Integer bankBillId);

    Long getFirstCapitalBillTraderTime(Integer afterSalesId);

    /**
     * 支付宝微信，根据单号关联T_CAPITAL_BILL_DETAIL
     * @param bankBillId
     * @return
     */
    List<BatchCapitalBillDto> queryCapitalBillRelateOrderNo(@Param("bankBillId") Integer bankBillId);

    List<BatchCapitalBillDto> queryCapitalBillByTranFlow(@Param("tranFlow") String tranFlow);

    /**
     * 根据银行流水id查询关联的erp订单号
     *
     * @param bankBillId 银行流水id
     * @return erp订单号
     */
    String getOrderNoByBankBillId(@Param("bankBillId") Integer bankBillId);

    CapitalBillDetailDto getFirstDetailByBankBillId(Integer bankBillId);

    /**
     * 根据银行流水id与交易类型查询交易时间在2023-09-01 00:00:00之后的资金流水
     */
    List<BatchCapitalBillDto> queryByTraderTypeAndBankBillId(@Param("traderType") Integer traderType, @Param("bankBillId") Integer bankBillId);
}
