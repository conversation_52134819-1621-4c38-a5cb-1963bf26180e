package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchSaleorderGoodsDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity;
import com.vedeng.erp.kingdee.dto.KingDeeInstallServiceRecordDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeInstallServiceRecordMapper;
import com.vedeng.erp.kingdee.service.KingDeeInstallServiceRecordService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 安调记录 处理类
 * @date 2023/02/18 14:00
 */

@Service
@Slf4j
public class BatchInstallServiceRecordProcessor implements ItemProcessor<BatchAfterSalesInstallServiceRecordDetailDto, KingDeeInstallServiceRecordDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;
    @Autowired
    private BatchSaleorderGoodsDtoMapper batchSaleorderGoodsDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;
    @Autowired
    private KingDeeInstallServiceRecordService kingDeeInstallServiceRecordService;
    @Autowired
    private KingDeeInstallServiceRecordMapper kingDeeInstallServiceRecordMapper;


    @Override
    public KingDeeInstallServiceRecordDto process(BatchAfterSalesInstallServiceRecordDetailDto dto) throws Exception {

        List<KingDeeInstallServiceRecordEntity> byFBillNo = kingDeeInstallServiceRecordMapper.findByFBillNo(String.valueOf(dto.getAfterSalesServiceDetailId()));
        if (CollUtil.isNotEmpty(byFBillNo)) {
            return null;
        }

        log.info("BatchInstallServiceRecordProcessor.process() start:{}", JSON.toJSONString(dto));

        KingDeeInstallServiceRecordDto queryDto = KingDeeInstallServiceRecordDto.builder().FBillNo(String.valueOf(dto.getAfterSalesServiceDetailId())).build();
        if (kingDeeBaseApi.isExist(queryDto)) {
            log.info("安调记录已经推送到金蝶:{}", JSON.toJSONString(dto));
            return null;
        }

        // 查询对应的售后单信息
        BatchAfterSalesDto batchAfterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesIdAndSubjectType(dto.getAfterSalesId(), KingDeeConstant.ID_535);
        if (Objects.isNull(batchAfterSalesDto)) {
            log.error("查询售后单信息失败,afterSalesId:{}", dto.getAfterSalesId());
            return null;
        }
        List<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtoList = batchAfterSalesDto.getBatchAfterSalesGoodsDtoList();
        if (CollUtil.isEmpty(batchAfterSalesGoodsDtoList)) {
            log.error("查询售后单商品信息失败,afterSalesId:{}", dto.getAfterSalesId());
            return null;
        }

        Optional<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtoOptional = batchAfterSalesGoodsDtoList.stream()
                .filter(goods -> dto.getAfterSalesGoodsId().equals(goods.getAfterSalesGoodsId()))
                .findFirst();
        if (!batchAfterSalesGoodsDtoOptional.isPresent()) {
            log.error("未查询到安调单商品关联的售后单商品信息,afterSalesGoodsId:{},batchAfterSalesGoodsDtoList:{}",
                    dto.getAfterSalesGoodsId(), JSON.toJSONString(batchAfterSalesGoodsDtoList));
            return null;
        }

        if (StringUtils.isBlank(dto.getSerialNumber()) && StringUtils.isBlank(dto.getSupplCode())) {
            log.error("安调单商品序列号为空,afterSalesGoodsId:{},afterSalesServiceDetailId:{}", dto.getAfterSalesGoodsId(), dto.getAfterSalesServiceDetailId());
            return null;
        }

        BatchSaleorderGoodsDto saleorderGoodsDto = batchSaleorderGoodsDtoMapper.selectBysaleorderGoodsId(batchAfterSalesGoodsDtoOptional.get().getOrderDetailId());
        List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList = new ArrayList<>();
        String tempSku = "";
        //普发 安调选择的SN或者自定义的SN
        if (KingDeeConstant.ZERO.equals(saleorderGoodsDto.getDeliveryDirect())) {
            tempSku = StringUtils.isBlank(dto.getSerialNumber()) ? dto.getSupplCode() : dto.getSerialNumber();
            List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemDtoList = batchWarehouseGoodsOutInItemDtoMapper.findByBarcodeFactory(tempSku);

            log.info("查询安调单商品对应的销售单普发商品的出库记录,销售出库{}", JSON.toJSONString(warehouseGoodsOutInItemDtoList));
            if (CollUtil.isNotEmpty(warehouseGoodsOutInItemDtoList)) {
                warehouseGoodsOutInItemList.addAll(warehouseGoodsOutInItemDtoList);
            }

        }
        //直发出库的商品入参则是对应的直发出库单中的SN
        if (KingDeeConstant.ONE.equals(saleorderGoodsDto.getDeliveryDirect())) {
            List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInBySaleOrderGoodsId = batchWarehouseGoodsOutInItemDtoMapper.getWarehouseGoodsOutInBySaleOrderGoodsId(saleorderGoodsDto.getSaleorderGoodsId());
            log.info("查询安调单商品对应的直发销售单商品的出库记录,销售出库{}", JSON.toJSONString(warehouseGoodsOutInBySaleOrderGoodsId));

            if (CollUtil.isNotEmpty(warehouseGoodsOutInBySaleOrderGoodsId)) {
                for (BatchWarehouseGoodsOutInItemDto warehouseGoodsOutInItemDto : warehouseGoodsOutInBySaleOrderGoodsId) {
                    if (StrUtil.isEmpty(warehouseGoodsOutInItemDto.getBarcodeFactory())) {
                        log.error("查询安调单商品对应的直发销售单商品的出库记录,sn码为空{}", JSON.toJSONString(warehouseGoodsOutInItemDto));
                        continue;
                    }
                    boolean isExist = kingDeeInstallServiceRecordService.byXlhIsExist(warehouseGoodsOutInItemDto.getBarcodeFactory());
                    if (!isExist) {
                        tempSku = warehouseGoodsOutInItemDto.getBarcodeFactory();
                        warehouseGoodsOutInItemList.add(warehouseGoodsOutInItemDto);
                        break;
                    }
                }
            }
        }

        String FQzokCkdh = "";
        if (CollUtil.isNotEmpty(warehouseGoodsOutInItemList)) {
            // 如果查询到多条出库记录，则按时间倒序取第一条
            BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsOutInItemDto = CollUtil.getFirst(warehouseGoodsOutInItemList);
            FQzokCkdh = StrUtil.isBlank(batchWarehouseGoodsOutInItemDto.getOutInNo())?"":batchWarehouseGoodsOutInItemDto.getOutInNo();
        }


        return KingDeeInstallServiceRecordDto.builder()
                .fid("0")
                .FBillNo(String.valueOf(dto.getAfterSalesServiceDetailId()))
                .FQzokDate(DateUtil.formatDate(dto.getAddTime()))
                .FQzokOrgid(KingDeeConstant.ORG_ID.toString())
                .FQzokYssj(DateUtil.formatDate(dto.getCheckDate()))
                .FQzokQssj(DateUtil.formatDate(dto.getCheckDate()))
                .FQzokBcfwsl(BigDecimal.ONE)
                .FQzokYsfs(dto.getCheckTypeName())
                .FQzokYsjl(dto.getCheckConclusionName())
                .FQzokYsddh(batchAfterSalesDto.getOrderNo())
                .FQzokGsywdh(batchAfterSalesDto.getAfterSalesNo())
                .FQzokYwlx("安调验收单")
                .FQzokWlbm(dto.getSku())
                .FQzokXlh(tempSku)
                .FQzokCkdh(FQzokCkdh)
                .build();
    }

}
