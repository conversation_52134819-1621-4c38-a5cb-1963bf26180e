package com.vedeng.erp.saleorder.enums;

public enum PCOrderStatusEnum {

   // 0审核中、1待确认、2待付款、7、部分付款、3待发货、4待收货、5、已完成 6、已取消
   INIT(0), CHECK(1),PRE_PAY(2),HALF_PAID(7),PRE_DELIVERY(3),PRE_RECEIVE(4),FINISH(5),CANCEL(6);
    private int status=0;
    private PCOrderStatusEnum(int s){
        status=s;
    }
    public int status(){
        return this.status;
    }
    public static PCOrderStatusEnum get(Integer status){
        for(PCOrderStatusEnum v:values()){
            if(v.status()==status){
                return v;
            }
        }
        return null;
    }
}
