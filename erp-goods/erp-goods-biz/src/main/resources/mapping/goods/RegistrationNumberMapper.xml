<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.RegistrationNumberMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.RegistrationNumber">
            <id property="registrationNumberId" column="REGISTRATION_NUMBER_ID" jdbcType="INTEGER"/>
            <result property="registrationNumber" column="REGISTRATION_NUMBER" jdbcType="VARCHAR"/>
            <result property="manageCategoryLevel" column="MANAGE_CATEGORY_LEVEL" jdbcType="INTEGER"/>
            <result property="productCompanyId" column="PRODUCT_COMPANY_ID" jdbcType="INTEGER"/>
            <result property="productionAddress" column="PRODUCTION_ADDRESS" jdbcType="VARCHAR"/>
            <result property="productChineseName" column="PRODUCT_CHINESE_NAME" jdbcType="VARCHAR"/>
            <result property="productEnglishName" column="PRODUCT_ENGLISH_NAME" jdbcType="VARCHAR"/>
            <result property="productCategoryId" column="PRODUCT_CATEGORY_ID" jdbcType="INTEGER"/>
            <result property="productCategoryName" column="PRODUCT_CATEGORY_NAME" jdbcType="VARCHAR"/>
            <result property="model" column="MODEL" jdbcType="VARCHAR"/>
            <result property="issuingDate" column="ISSUING_DATE" jdbcType="BIGINT"/>
            <result property="effectiveDate" column="EFFECTIVE_DATE" jdbcType="BIGINT"/>
            <result property="dealStatus" column="DEAL_STATUS" jdbcType="BOOLEAN"/>
            <result property="approvalDepartment" column="APPROVAL_DEPARTMENT" jdbcType="VARCHAR"/>
            <result property="trademark" column="TRADEMARK" jdbcType="VARCHAR"/>
            <result property="zipCode" column="ZIP_CODE" jdbcType="VARCHAR"/>
            <result property="registeredAgent" column="REGISTERED_AGENT" jdbcType="VARCHAR"/>
            <result property="registeredAgentAddress" column="REGISTERED_AGENT_ADDRESS" jdbcType="VARCHAR"/>
            <result property="proPerfStruAndComp" column="PRO_PERF_STRU_AND_COMP" jdbcType="VARCHAR"/>
            <result property="productUseRange" column="PRODUCT_USE_RANGE" jdbcType="VARCHAR"/>
            <result property="otherContents" column="OTHER_CONTENTS" jdbcType="VARCHAR"/>
            <result property="productStandards" column="PRODUCT_STANDARDS" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="BOOLEAN"/>
            <result property="productionOrCountry" column="PRODUCTION_OR_COUNTRY" jdbcType="VARCHAR"/>
            <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>
            <result property="aftersaleServiceOrg" column="AFTERSALE_SERVICE_ORG" jdbcType="VARCHAR"/>
            <result property="changeDate" column="CHANGE_DATE" jdbcType="BIGINT"/>
            <result property="changeContents" column="CHANGE_CONTENTS" jdbcType="VARCHAR"/>
            <result property="isRelease" column="IS_RELEASE" jdbcType="BOOLEAN"/>
            <result property="expectedUsage" column="EXPECTED_USAGE" jdbcType="VARCHAR"/>
            <result property="storageCondAndEffectiveDate" column="STORAGE_COND_AND_EFFECTIVE_DATE" jdbcType="VARCHAR"/>
            <result property="mainProPerfStruAndComp" column="MAIN_PRO_PERF_STRU_AND_COMP" jdbcType="VARCHAR"/>
            <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
            <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
            <result property="modTime" column="MOD_TIME" jdbcType="BIGINT"/>
            <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
            <result property="attachment" column="ATTACHMENT" jdbcType="VARCHAR"/>
            <result property="category" column="CATEGORY" jdbcType="TINYINT"/>
            <result property="storageAndExpiryDate" column="STORAGE_AND_EXPIRY_DATE" jdbcType="VARCHAR"/>
            <result property="isSubcontractProduction" column="IS_SUBCONTRACT_PRODUCTION" jdbcType="TINYINT"/>
            <result property="productionEnterpriseName" column="PRODUCTION_ENTERPRISE_NAME" jdbcType="VARCHAR"/>
            <result property="bcIssueDate" column="BC_ISSUE_DATE" jdbcType="BIGINT"/>
            <result property="productCompanyLicence" column="PRODUCT_COMPANY_LICENCE" jdbcType="VARCHAR"/>
            <result property="manufacturerId" column="MANUFACTURER_ID" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        REGISTRATION_NUMBER_ID,REGISTRATION_NUMBER,MANAGE_CATEGORY_LEVEL,
        PRODUCT_COMPANY_ID,PRODUCTION_ADDRESS,PRODUCT_CHINESE_NAME,
        PRODUCT_ENGLISH_NAME,PRODUCT_CATEGORY_ID,PRODUCT_CATEGORY_NAME,
        MODEL,ISSUING_DATE,EFFECTIVE_DATE,
        DEAL_STATUS,APPROVAL_DEPARTMENT,TRADEMARK,
        ZIP_CODE,REGISTERED_AGENT,REGISTERED_AGENT_ADDRESS,
        PRO_PERF_STRU_AND_COMP,PRODUCT_USE_RANGE,OTHER_CONTENTS,
        PRODUCT_STANDARDS,TYPE,PRODUCTION_OR_COUNTRY,
        REMARKS,AFTERSALE_SERVICE_ORG,CHANGE_DATE,
        CHANGE_CONTENTS,IS_RELEASE,EXPECTED_USAGE,
        STORAGE_COND_AND_EFFECTIVE_DATE,MAIN_PRO_PERF_STRU_AND_COMP,ADD_TIME,
        CREATOR,MOD_TIME,UPDATER,
        ATTACHMENT,CATEGORY,STORAGE_AND_EXPIRY_DATE,
        IS_SUBCONTRACT_PRODUCTION,PRODUCTION_ENTERPRISE_NAME,BC_ISSUE_DATE,
        PRODUCT_COMPANY_LICENCE,MANUFACTURER_ID
    </sql>
</mapper>
