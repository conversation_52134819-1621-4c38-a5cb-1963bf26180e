<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="新增售后收入记录" scope="application"/>
<%@ include file="../../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/pages/goods/basecategory/add.css?rnd=${resourceVersionKey}">
<script type="text/javascript"
        src='<%=basePath%>static/js/orderstream/aftersales/add_return_visit_record.js?rnd=${resourceVersionKey}'></script>

<body>
    <div class="formpublic">
    <form method="post" action="" id="addReturnVisitRecord">
        <input type="hidden" name="creator" value="${user.userId}"/>
        <input type="hidden" name="afterSalesId" value="${afterSalesId}"/>
        <input type="hidden" name="formToken" value="${formToken}"/>

        <div class="form-wrap">
            <div class="form-container base-form form-span-8">

                <div class="form-block">
                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>回访客户姓名：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <input type="text" autocomplete="off" name="customerName" placeholder="请输入回访客户姓名"
                                       valid-max="16" class="input-text">
                                <div id="customerNameError"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>回访客户性质：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <select class="input-text" name="customerNature" id="customerNature">
                                    <option value="1">终端</option>
                                    <option value="2">经销商</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>回访客户号码：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <input type="text" autocomplete="off" name="customerMobile" placeholder="请输入回访客户联系号码"
                                       valid-max="16" class="input-text">
                                <div id="customerMobileError"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>回访状态：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <select class="input-text" name="status" id="status">
                                    <option value="1">已回访</option>
                                    <option value="2">不知情</option>
                                    <option value="3">拒绝回答</option>
                                    <option value="4">拒接</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>回访部门：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <select class="input-text" name="returnVisitDepartment" id="returnVisitDepartment">
                                    <option value="1">医修帮</option>
                                    <option value="2">售后</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">录音ID：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <input type="text" autocomplete="off" name="soundRecordId" placeholder="请输入录音ID"
                                       valid-max="16" class="input-text">
                                <div id="soundRecordIdError"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>服务响应分值：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <input type="text" autocomplete="off" name="serviceResponseScore" onchange="calculateTotalScore()"
                                       placeholder="请输入0-100之间的整数数值" valid-max="16" class="input-text">
                                <div id="serviceResponseScoreError"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>服务态度分值：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <input type="text" autocomplete="off" name="serviceAttitudeScore" onchange="calculateTotalScore()"
                                       placeholder="请输入0-100之间的整数数值" valid-max="16" class="input-text">
                                <div id="serviceAttitudeScoreError"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>服务能力分值：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <input type="text" autocomplete="off" name="serviceCapabilityScore" onchange="calculateTotalScore()"
                                       placeholder="请输入0-100之间的整数数值" valid-max="16" class="input-text">
                                <div id="serviceCapabilityScoreError"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否有投诉：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <select class="input-text" name="isComplaint" id="isComplaint" onchange="calculateTotalScore()">
                                    <option value="0">无</option>
                                    <option value="1">有</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>是否有推荐：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <select class="input-text" name="isRecommend" id="isRecommend" onchange="calculateTotalScore()">
                                    <option value="0">无</option>
                                    <option value="1">有</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">最终分值：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <input class="input-text" type="text" name="totalScore" id="totalScore" readonly="readonly">
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">回访结果：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                                <input class="input-text" type="text" id="result" readonly="readonly">
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">备注：</div>
                        <div class="form-fields">
                            <div class="form-col col-8">
                            <textarea class="input-textarea" name="comments" id="comments" cols="30"
                                      rows="10"></textarea>
                                <div id="commentsError"></div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="form-btn">
                    <div class="form-fields">
                        <button type="submit" id="submit" class="btn btn-blue btn-large">保存</button>
                        <button type="button" class="dele btn btn-large" id="cancle">取消</button>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>
</body>

<%@ include file="../../common/footer.jsp" %>