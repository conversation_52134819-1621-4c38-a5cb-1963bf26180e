package com.vedeng.logistics.model;

import com.vedeng.common.model.FileInfo;
import com.vedeng.system.model.Attachment;

import java.io.Serializable;
import java.util.List;

/**
 * 确认单记录表
 * @TableName T_CONFIRMATION_FORM_RECODE
 */

public class ConfirmationFormRecode implements Serializable {
    /**
     * 确认单ID
     */
    private Integer id;

    /**
     * 确认单名称
     */
    private String confirmationName;

    /**
     * 确认单类型  1:销售  2:物流
     */
    private Integer confirmationType;

    /**
     * 确认单文件ID,文件ID数组
     */
    private String fileId;

    /**
     * 确认单文件,文件名数组
     */
    private String fileName;

    /**
     * 备注
     */
    private String comments;

    /**
     * 是否删除,0未删除,1已删除
     */
    private Integer isEnable;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 操作人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 销售订单id
     */
    private Integer saleOrderId;

    private static final long serialVersionUID = 1L;

    /**
     * 确认单附件url集合
     */
    private List<String> urlList;

    /**
     * 确认单附件pdf集合
     */
    private List<String> pdfList;

    public List<String> getPdfList() {
        return pdfList;
    }

    public void setPdfList(List<String> pdfList) {
        this.pdfList = pdfList;
    }

    /**
     * 出库批次记录表
     */
    private List<OutboundBatchesRecode> batchesRecodes;

    /**
     * 确认单附件
     */
    private List<Attachment> attachments;

    /**
     * 确认单附件回显
     */
    private List<com.vedeng.infrastructure.file.domain.Attachment> attachmentsBack;
    /**
     * 确认单附件 配合前端接收用
     */
    private List<FileInfo> fileInfos;

    public List<com.vedeng.infrastructure.file.domain.Attachment> getAttachmentsBack() {
        return attachmentsBack;
    }

    public void setAttachmentsBack(List<com.vedeng.infrastructure.file.domain.Attachment> attachmentsBack) {
        this.attachmentsBack = attachmentsBack;
    }

    public List<FileInfo> getFileInfos() {
        return fileInfos;
    }

    public void setFileInfos(List<FileInfo> fileInfos) {
        this.fileInfos = fileInfos;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public List<OutboundBatchesRecode> getBatchesRecodes() {
        return batchesRecodes;
    }

    public void setBatchesRecodes(List<OutboundBatchesRecode> batchesRecodes) {
        this.batchesRecodes = batchesRecodes;
    }

    public List<String> getUrlList() {
        return urlList;
    }

    public void setUrlList(List<String> urlList) {
        this.urlList = urlList;
    }

    public Integer getSaleOrderId() {
        return saleOrderId;
    }

    public void setSaleOrderId(Integer saleOrderId) {
        this.saleOrderId = saleOrderId;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConfirmationName() {
        return confirmationName;
    }

    public void setConfirmationName(String confirmationName) {
        this.confirmationName = confirmationName;
    }

    public Integer getConfirmationType() {
        return confirmationType;
    }

    public void setConfirmationType(Integer confirmationType) {
        this.confirmationType = confirmationType;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}
