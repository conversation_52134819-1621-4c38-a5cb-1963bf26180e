
package com.vedeng.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.StringUtil;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodSettlementTypeEnum;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.model.Goods;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.ComponentRelation;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderCoupon;
import com.vedeng.order.model.vo.OrderConnectVo;
import com.vedeng.order.service.OrderAndProductConnectService;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <b>Description:</b><br>
 * <b>Author: Franlin</b>
 * @fileName OrderAndProductConnectServiceImpl.java
 * <br><b>Date: 2018年8月23日 下午3:31:38 </b>
 *
 */
@Service("orderAndProductConnectService")
public class OrderAndProductConnectServiceImpl extends BaseServiceimpl implements OrderAndProductConnectService
{
	public static Logger logger = LoggerFactory.getLogger(OrderAndProductConnectServiceImpl.class);

	@Resource
	private UserMapper userMapper;

	@Autowired
	@Qualifier("saleorderMapper")
	private SaleorderMapper saleorderMapper;

	@Autowired
	@Qualifier("goodsMapper")
	private GoodsMapper goodsMapper;

	@Autowired
	@Qualifier("saleorderGoodsMapper")
	private SaleorderGoodsMapper saleorderGoodsMapper;

	@Autowired
	@Qualifier("traderCustomerMapper")
	private TraderCustomerMapper traderCustomerMapper;

	@Autowired
	private WarehouseStockService warehouseStockService;

	@Autowired
	private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;
	@Resource
	private OrderCommonService orderCommonService;


	@SuppressWarnings("unchecked")
	@Override
	public ResultInfo<List<OrderConnectVo>> queryOrderAndProductConnectOrderNo(OrderConnectVo reqVo)
	{
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<OrderConnectVo>>> TypeRef = new TypeReference<ResultInfo<List<OrderConnectVo>>>(){};
		String url = httpUrl + "order/productConnect/queryOrderAndProductConnectOrderNo.htm";
		ResultInfo<List<OrderConnectVo>> result = null;

		try
		{
			result = (ResultInfo<List<OrderConnectVo>>) HttpClientUtils.post(url, reqVo, clientId, clientKey, TypeRef);
		}
		catch (IOException e)
		{
			logger.error(Contant.ERROR_MSG, e);
		}
		if(null == result)
			return new ResultInfo<List<OrderConnectVo>>();

		List<OrderConnectVo> resultList = new LinkedList<OrderConnectVo>();
		List<OrderConnectVo> orderList = (List<OrderConnectVo>) result.getData();
		if(null != orderList)
		{
			User user = new User();
			for(OrderConnectVo vo : orderList)
			{
				if(null == vo || null == vo.getOrderConnectNo())
					continue;
				if(null != vo.getCreatorId())
				{
					user.setUserId(vo.getCreatorId());
					User userInfo = userMapper.getUser(user);
					if(null != userInfo)
					{
						vo.setCreatorName(userInfo.getUsername());
					}
				}
				resultList.add(vo);
			}
		}
		result.setData(resultList);
		// 设置数据

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	public int saveHCOrder(Saleorder order, List<SaleorderCoupon> couponList) throws Exception {
		logger.info("saveHCOrder 入参：Saleorder:{},couponList:{}", JSON.toJSONString(order),JSON.toJSONString(couponList));
		// 根据traderId查询所属客户
		TraderCustomerVo traderCustomer = traderCustomerMapper.getCustomerInfo(order.getTraderId());
		if (traderCustomer == null) {
			logger.info("保存hc订单失败,ERP中无订单所属客户,orderNo:{},traderId:{}",order.getSaleorderNo(),order.getTraderId());
			return 0;
		}
		order.setCustomerType(traderCustomer.getCustomerType());// 客户类型
		order.setCustomerNature(traderCustomer.getCustomerNature());// 客户性质

        // 设置订单归属人默认为客户对应的归属销售
        User user = saleorderMapper.getUserInfoByTraderId(order.getTraderId());// 1客户，2供应商

        if(user == null){
            user = userMapper.getUserByName("haocai.vedeng");
        }

        if (user != null) {
            order.setUserId(user.getUserId());// 订单归属人
            order.setOwnerUserId(user.getUserId());// 订单归属人
            order.setOrgName(user.getOrgName());// 订单归属人部门
            order.setOrgId(114);//医械购诊所业务部线上开发组 TODO 根据人来修改
        }

		//如果发票邮箱为空
		if(StringUtil.isEmpty(order.getInvoiceEmail())){
			order.setInvoiceEmail("<EMAIL>");
		}

		//如果是自动自动电子发票 则票货地址一致
		if(order.getInvoiceMethod() != null && order.getInvoiceMethod() == 3){
			order.setIsSameAddress(1);
		}

		// 插入订单
		order.setIsSendInvoice(order.getIsApplyInvoice());
/*
		//耗材订单自动审核流程
		order.setAutoAudit(1);
*/
		//VDERP-7306, 新增默认为2 产品开票
		//Integer billPeriodSettlementType = order.getBillPeriodSettlementType()==null || order.getBillPeriodSettlementType() ==0 ?2:order.getBillPeriodSettlementType();
		order.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());

		//设置订单确认时间 确认状态
		order.setConfirmTime(new Date());
		order.setConfirmStatus(ErpConst.STATUS_STATE.SOME);
		int i = saleorderMapper.insertSelective(order);
		if (i == 1) {
			for (int x = 0; x < order.getGoodsList().size(); x++) {
				order.getGoodsList().get(x).setSaleorderId(order.getSaleorderId());
				Goods goods = goodsMapper.selectGoodsExtendInfo(order.getGoodsList().get(x).getSku());
				if (null != goods) {
					// 单位名称
					order.getGoodsList().get(x).setUnitName(goods.getUnitName());
					order.getGoodsList().get(x).setGoodsId(goods.getGoodsId());
				}
				order.getGoodsList().get(x).setDeliveryCycle("14天");
				// 插入商品
				int j = saleorderGoodsMapper.insertSelective(order.getGoodsList().get(x));

				//更新快照信息
				saleorderGoodsMapper.updateSaleorderGoodsSnapshotInfo(order.getGoodsList().get(x));

				{
					// 添加内部备注默认值
					ComponentRelation componentRelation = ComponentRelation
							.builder()
							.scene(Integer.valueOf(0))
							.relationId(order.getSaleorderId())
							.skuNo(order.getGoodsList().get(x).getSku())
							.skuName(order.getGoodsList().get(x).getGoodsName())
							.time(0L)
							.build();
					logger.info("耗材添加内部备注默认值 - ComponentRelation:{}", componentRelation);
					saleorderGoodsMapper.insertInnerInsideDefault(componentRelation);
				}

				if (j <= 0) {
				    logger.info("保存订单商品信息发生错误,订单号:{}",order.getSaleorderNo());
					throw new Exception("保存订单商品信息发生错误");
				}
			}
			if (couponList != null && couponList.size() > 0) {
				// 优惠券
				for (int x = 0; x < couponList.size(); x++) {
					couponList.get(x).setSaleorderId(order.getSaleorderId());
				}
				int j = saleorderGoodsMapper.insertOrderCouponBatch(couponList);
				if (j <= 0) {
                    logger.info("保存订单优惠券信息发生错误,订单号:{}",order.getSaleorderNo());
					throw new Exception("保存订单优惠券信息发生错误");
				}
			}
			order.setOperateType(StockOperateTypeConst.START_ORDER);
//			int i1 = warehouseStockService.updateOccupyStockService(order,0);
			logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(order,user);
			//VDERP-2263   订单售后采购改动通知
			orderCommonService.updateSaleOrderDataUpdateTime(order.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_GENERATE);
			return 1;
		}
		logger.info("保存hc订单失败,插入数据库失败,订单号:{}",order.getSaleorderNo());
		return 0;
	}


}

