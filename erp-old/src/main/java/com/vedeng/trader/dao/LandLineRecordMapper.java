package com.vedeng.trader.dao;

import com.vedeng.trader.model.LandLineCallRecord;
import com.vedeng.trader.model.dto.RecentLandLineRecordDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

/**
 * 座机通话记录Mapper
 *
 * <AUTHOR>
 */
@Named("landLineRecordMapper")
public interface LandLineRecordMapper {
    /**
     * 新增 座机通话记录
     *
     * @param landLineCallRecord
     * @return
     */
    int insert(LandLineCallRecord landLineCallRecord);

    /**
     * 获取最近该号码5通电话通话时长大于2min的记录
     *
     * @param phone
     * @return
     */
    RecentLandLineRecordDto getRecentRecordWithCondition(String phone);

    /**
     * 主键获取通话信息
     *
     * @param recordId
     * @return
     */
    LandLineCallRecord getRecordInfoById(Long recordId);

    /**
     * 获取该电话两年来呼出接通的最新记录
     *
     * @param phone
     * @return
     */
    LandLineCallRecord getConnectLineRecent2Years(String phone);

    /**
     * 获取该号码这条线路今天未接通电话的次数
     *
     * @param phone
     * @param lineCode
     * @return
     */
    Integer getToadyUnConnectTimesByLineCode(@Param("phone") String phone, @Param("lineCode") Integer lineCode);


    /**
     * 批量获取座机记录信息
     *
     * @param recordIds
     * @return
     */
    List<LandLineCallRecord> getLandLineCallRecordByIds(@Param("recordIds") List<Long> recordIds);

    /**
     * 获取今天未处理的座机记录
     *
     * @return
     */
    List<LandLineCallRecord> getUnHandleRecordListToday();

    /**
     * 同步座机通话信息
     *
     * @param landLineCallRecord
     * @return
     */
    int syncLandLineRecordInfo(@Param("landLineCallRecord") LandLineCallRecord landLineCallRecord);

    /**
     * 获取被叫号码线路今天座机记录(时间倒序)
     *
     * @param phone
     * @param lineCode
     * @return
     */
    List<LandLineCallRecord> getPhoneToadyLineRecords(@Param("phone") String phone, @Param("lineCode") Integer lineCode);
}
