package com.vedeng.api.standard.adapter.buyorder.dto;

import com.vedeng.api.standard.approval.ApprovalRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购单审核请求
 * 专门用于审核操作，只包含审核相关字段
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuyOrderApprovalRequest extends BaseBuyOrderRequest implements ApprovalRequest {

    private static final long serialVersionUID = 1L;

    // ========== 审核核心字段 ==========
    /**
     * 任务ID（审核时必填）
     */
    private String taskId;

    /**
     * 审核通过标志（true-通过，false-拒绝）
     */
    private Boolean pass;

    /**
     * 审核备注/意见
     */
    private String comment;

    // ========== 审核详细信息 ==========
    /**
     * 提交备注
     */
    private String submitRemark;

    /**
     * 审核备注
     */
    private String approveRemark;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 删除原因
     */
    private String deleteReason;

    @Override
    public String getOperationType() {
        return "approve";
    }

    @Override
    public void validate() {
        super.validate();
        
        if (getBuyOrderId() == null) {
            throw new IllegalArgumentException("采购单ID不能为空");
        }
        
        if (taskId == null || taskId.trim().isEmpty()) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        if (pass == null) {
            throw new IllegalArgumentException("审核结果不能为空");
        }
        
        // 如果是拒绝，必须提供拒绝原因
        if (!pass && (rejectReason == null || rejectReason.trim().isEmpty())) {
            throw new IllegalArgumentException("拒绝审核时必须提供拒绝原因");
        }
    }

    /**
     * 创建用于审核通过的请求对象
     */
    public static BuyOrderApprovalRequest forApprove(Integer buyOrderId, String taskId, String approveRemark) {
        BuyOrderApprovalRequest request = new BuyOrderApprovalRequest();
        request.setBuyOrderId(buyOrderId);
        request.setTaskId(taskId);
        request.setPass(true);
        request.setApproveRemark(approveRemark);
        request.setComment(approveRemark);
        return request;
    }

    /**
     * 创建用于审核拒绝的请求对象
     */
    public static BuyOrderApprovalRequest forReject(Integer buyOrderId, String taskId, String rejectReason) {
        BuyOrderApprovalRequest request = new BuyOrderApprovalRequest();
        request.setBuyOrderId(buyOrderId);
        request.setTaskId(taskId);
        request.setPass(false);
        request.setRejectReason(rejectReason);
        request.setComment(rejectReason);
        return request;
    }

    /**
     * 创建用于提交审核的请求对象
     */
    public static BuyOrderApprovalRequest forSubmit(Integer buyOrderId, String submitRemark) {
        BuyOrderApprovalRequest request = new BuyOrderApprovalRequest();
        request.setBuyOrderId(buyOrderId);
        request.setSubmitRemark(submitRemark);
        return request;
    }
}
