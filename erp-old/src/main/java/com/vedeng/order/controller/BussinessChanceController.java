package com.vedeng.order.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SpecialSalesEnum;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.dwh.constant.DwhConstants;
import com.vedeng.dwh.model.dto.DwhErpSubDeptDto;
import com.vedeng.dwh.model.dto.DwhErpUserDto;
import com.vedeng.dwh.model.generate.DwmNjUserOrgDfDo;
import com.vedeng.dwh.service.DwhThreadLocalService;
import com.vedeng.erp.businessclues.api.BusinessCluesApi;
import com.vedeng.erp.businessclues.dto.BusinessCluesDto;
import com.vedeng.erp.saleorder.api.AutoRegistrationService;
import com.vedeng.erp.system.dto.BussinessChanceMessageVo;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.erp.trader.api.CommunicateVoiceTaskApi;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.dto.CommunicateVoiceTaskDto;
import com.vedeng.erp.trader.dto.SpecialSalesDto;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;
import com.vedeng.erp.trader.service.SpecialSalesApiService;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.goods.model.dto.CoreSkuBaseDTO;
import com.vedeng.goods.model.vo.BaseCategoryVo;
import com.vedeng.goods.model.vo.CategoryAttrValueMappingVo;
import com.vedeng.goods.service.BaseCategoryService;
import com.vedeng.goods.service.BaseGoodsService;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.order.dao.RemarkComponentMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.enums.BusinessChanceAccuracyErpOldEnum;
import com.vedeng.order.model.*;
import com.vedeng.order.model.dto.ImBcAddDTO;
import com.vedeng.order.model.dto.NewSourceDicDto;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.order.model.vo.QuoteorderVo;
import com.vedeng.order.service.BussinessChanceService;
import com.vedeng.order.service.QuoteService;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.MessageUser;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.Tag;
import com.vedeng.system.service.*;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.dto.TraderBaseInfoDto;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import net.sf.json.JSONObject;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 商机
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 *       <b>PackageName:</b> com.vedeng.order.controller <br>
 *       <b>ClassName:</b> BussinessChanceController <br>
 *       <b>Date:</b> 2017年6月22日 上午8:52:06
 */
@Controller
@RequestMapping("/order/bussinesschance")
public class BussinessChanceController extends BaseController {

    @Autowired // 自动装载
	private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

	@Autowired
	@Qualifier("actionProcdefService")
	private ActionProcdefService actionProcdefService;

	@Autowired
	@Qualifier("verifiesRecordService")
	private VerifiesRecordService verifiesRecordService;

	@Autowired
	@Qualifier("bussinessChanceService")
	private BussinessChanceService bussinessChanceService;

	@Autowired
	@Qualifier("regionService")
	private RegionService regionService;// 自动注入regionService

	@Autowired
	@Qualifier("userService")
	private UserService userService;

	@Autowired
	@Qualifier("traderCustomerService")
	private TraderCustomerService traderCustomerService;
	@Autowired
	@Qualifier("tagService")
	private TagService tagService;

	@Autowired
	@Qualifier("messageService")
	private MessageService messageService;

	@Autowired
	private SysOptionDefinitionMapper sysOptionDefinitionMapper;

	@Autowired
	private QuoteService quoteService;

	@Autowired
	private SaleorderGoodsMapper saleorderGoodsMapper;

	@Autowired
    private BaseCategoryService baseCategoryService;

	@Autowired
	private BaseGoodsService baseGoodsService;

	@Autowired
	private DwhThreadLocalService dwhThreadLocalService;

	@Autowired
	private AuthService authService;

	@Autowired
	private RemarkComponentMapper remarkComponentMapper;

	@Autowired
	private AutoRegistrationService autoRegistrationService;

	@Autowired
	private TraderMapper traderMapper;

	@Autowired
	private BusinessCluesApi businessCluesApi;

	@Autowired
	private SysOptionDefinitionApiService sysOptionDefinitionApiService;

	@Value("${bnc_inquiry_reduction_handle}")
	private String inquiryMap;

	private final static String KEY_PREFIX_DATA_DICTIONARY_LIST = "redis_dictionary_buss_list:";

	@Autowired
	private SpecialSalesApiService specialSalesApiService;

	@Autowired
	private BussinessChanceMapper bussinessChanceMapper;

    @Autowired
    private UserWorkApiService userWorkApiService;

	//科研特麦帮 字典值
	private static final Integer SYS_OPTION_TMB = 4359;
	//科研购事业部
	private static final Integer SCIENCE_ORGID = 36;
	//科研购所属COMPANY_ID
	private static final Integer SCIENCE_COMPANY_ID = 1;

    private static final Integer NOT_MERGED = 0;//商机未被合并，也未合并别人

	@Autowired
	private CommunicateVoiceTaskApi communicateVoiceTaskApi;

	/**
	 * <b>Description:</b><br>
	 * 售后商机首页
	 *
	 * @param request
	 * @param bussinessChance
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年6月22日 上午8:57:03
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "serviceindex")
	public ModelAndView serviceIndex(HttpServletRequest request, BussinessChanceVo bussinessChanceVo,
			@RequestParam(required = false, defaultValue = "1") Integer pageNo,
			@RequestParam(required = false) Integer pageSize, HttpSession session) {
		User user = getSessionUser(session);
		ModelAndView mv = new ModelAndView();
		Page page = getPageTag(request, pageNo, pageSize);
		bussinessChanceVo.setCompanyId(user.getCompanyId());

		// 商机类型
		List<SysOptionDefinition> typeList = getSysOptionDefinitionList(SysOptionConstant.ID_390);
		mv.addObject("typeList", typeList);

		List<SysOptionDefinition> newtypeList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_390),bncServiceTypeIds);
		mv.addObject("newtypeList", newtypeList);
		//商机来源
		List<SysOptionDefinition> sourceList = getSysOptionDefinitionList(SysOptionConstant.ID_365);
		mv.addObject("sourceList", sourceList);
		//询价方式
		List<SysOptionDefinition> communicationList=getSysOptionDefinitionList(SysOptionConstant.ID_376);
		mv.addObject("communicationList",communicationList);

		List<SysOptionDefinition> entrancesList = sysOptionDefinitionMapper.getSysOptionDefinitionByParentTitle("咨询入口");
		mv.addObject("entrancesList",entrancesList);

		List<SysOptionDefinition> functionsList = sysOptionDefinitionMapper.getSysOptionDefinitionByParentTitle("功能");
		mv.addObject("functionsList",functionsList);


		List<Integer> salePositionType = new ArrayList<>();
		salePositionType.add(SysOptionConstant.ID_310);//销售
		salePositionType.add(312);//市场
		salePositionType.add(311);//产品
		user.setPositType(0);
		List<User> marketUserList = userService.getMyUserList(user, salePositionType, false);
		List<User> newMarketUserList = userService.exincludeLiZhiUser(marketUserList);
		mv.addObject("userList", newMarketUserList);
		// 地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mv.addObject("provinceList", provinceList);

		if (null != bussinessChanceVo.getProvince() && bussinessChanceVo.getProvince() > 0) {
			List<Region> cityList = regionService.getRegionByParentId(bussinessChanceVo.getProvince());
			mv.addObject("cityList", cityList);
		}

		if (null != bussinessChanceVo.getCity() && bussinessChanceVo.getCity() > 0) {
			List<Region> zoneList = regionService.getRegionByParentId(bussinessChanceVo.getCity());
			mv.addObject("zoneList", zoneList);
		}

		if (null != bussinessChanceVo.getNewType() && bussinessChanceVo.getNewType() > 0) {
			List<SysOptionDefinition> inquiryList = getBussInquiryData(bussinessChanceVo.getNewType());
			mv.addObject("inquiryList", inquiryList);

		}

		if (null != bussinessChanceVo.getNewSource() && bussinessChanceVo.getNewSource() > 0) {
			List<SysOptionDefinition> newCommunicationList = getBussInquiryData(bussinessChanceVo.getNewSource());
			mv.addObject("newCommunicationList", newCommunicationList);
		}

		if (null != bussinessChanceVo.getInquiry() && bussinessChanceVo.getInquiry() > 0) {
			List<SysOptionDefinition> newSourceList = getBussInquiryData(bussinessChanceVo.getInquiry());
			mv.addObject("newSourceList", newSourceList);

		}

		if (null != bussinessChanceVo.getUserId() && bussinessChanceVo.getUserId() > 0) {
			List<Integer> userIds = new ArrayList<>();
			userIds.add(bussinessChanceVo.getUserId());
			bussinessChanceVo.setUserIds(userIds);
		}

		// 时间处理
		if (null != bussinessChanceVo.getStarttime() && bussinessChanceVo.getStarttime() != "") {
			bussinessChanceVo.setStarttimeLong(DateUtil.convertLong(bussinessChanceVo.getStarttime(), "yyyy-MM-dd"));
		}
		if (null != bussinessChanceVo.getEndtime() && bussinessChanceVo.getEndtime() != "") {
			bussinessChanceVo.setEndtimeLong(DateUtil.convertLong(bussinessChanceVo.getEndtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}

		// 地区处理
		if (null != bussinessChanceVo.getZone() && bussinessChanceVo.getZone() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getZone());
		} else if (null != bussinessChanceVo.getCity() && bussinessChanceVo.getCity() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getCity());
		} else if (null != bussinessChanceVo.getProvince() && bussinessChanceVo.getProvince() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getProvince());
		}

		List<BussinessChanceVo> bussinessChanceList = null;
		Map<String, Object> map = bussinessChanceService.getServiceBussinessChanceListPage(bussinessChanceVo, page);

		bussinessChanceList = (List<BussinessChanceVo>) map.get("list");

		mv.addObject("bussinessChanceVo", bussinessChanceVo);
		mv.addObject("bussinessChanceList", bussinessChanceList);
		mv.addObject("page", (Page) map.get("page"));
		mv.setViewName("order/bussinesschance/service_index");
		return mv;
	}

	/**
	 * 筛选在范围内的字典选项
	 */
	private List<SysOptionDefinition> getSysOptionIncluded(List<SysOptionDefinition> options,String ids){
		List<Integer> idsList= JSON.parseArray(ids,Integer.class);
		return options.stream().filter(e->idsList.contains(e.getSysOptionDefinitionId())).collect(Collectors.toList());
	}

	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "newServiceindex")
	public ModelAndView newServiceIndex(HttpServletRequest request, BussinessChanceVo bussinessChanceVo,
			@RequestParam(required = false, defaultValue = "1") Integer pageNo,
			@RequestParam(required = false) Integer pageSize, HttpSession session,Integer isSearch) {
		User user = getSessionUser(session);
		ModelAndView mv = new ModelAndView();
		Page page = getPageTag(request, pageNo, pageSize);
		bussinessChanceVo.setCompanyId(user==null?1:user.getCompanyId());

		//商机来源
		List<SysOptionDefinition> sourceList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_365),bncServiceSourceIds);
		mv.addObject("sourceList", sourceList);
		//询价方式
		List<SysOptionDefinition> communicationList=getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_376),bncServiceEnquiryIds);
		mv.addObject("communicationList",communicationList);

		List<SysOptionDefinition> entrancesList = sysOptionDefinitionMapper.getSysOptionDefinitionByParentTitle("咨询入口");
		mv.addObject("entrancesList",entrancesList);

		List<SysOptionDefinition> functionsList = sysOptionDefinitionMapper.getSysOptionDefinitionByParentTitle("功能");
		mv.addObject("functionsList",functionsList);


		List<Integer> salePositionType = new ArrayList<>();
		salePositionType.add(SysOptionConstant.ID_310);//销售
		salePositionType.add(312);//市场
		salePositionType.add(311);//产品
		user.setPositType(0);
		List<User> marketUserList = userService.getMyUserList(user, salePositionType, false);
		List<User> newMarketUserList = userService.exincludeLiZhiUser(marketUserList);
		mv.addObject("userList", newMarketUserList);
		// 地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mv.addObject("provinceList", provinceList);

		if (null != bussinessChanceVo.getProvince() && bussinessChanceVo.getProvince() > 0) {
			List<Region> cityList = regionService.getRegionByParentId(bussinessChanceVo.getProvince());
			mv.addObject("cityList", cityList);
		}

		if (null != bussinessChanceVo.getCity() && bussinessChanceVo.getCity() > 0) {
			List<Region> zoneList = regionService.getRegionByParentId(bussinessChanceVo.getCity());
			mv.addObject("zoneList", zoneList);
		}

		// 商机类型
		List<SysOptionDefinition> typeList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_390),bncServiceTypeIds);
		mv.addObject("typeList", typeList);

		// VDERP-15213 询价行为(不同字典组下的相同名字的字典项需要合并处理)
		this.handleQueryDictionary(typeList, mv, bussinessChanceVo);

		if (null != bussinessChanceVo.getUserId() && bussinessChanceVo.getUserId() > 0) {
			List<Integer> userIds = new ArrayList<>();
			userIds.add(bussinessChanceVo.getUserId());
			bussinessChanceVo.setUserIds(userIds);
		}

		// 时间处理
		if (StringUtil.isNotBlank(bussinessChanceVo.getStarttime())) {
			bussinessChanceVo.setStarttimeLong(DateUtil.convertLong(bussinessChanceVo.getStarttime(), "yyyy-MM-dd"));
		}else if(!ErpConst.ONE.equals(isSearch)){
			Long stamps=DateUtil.getDateBefore(new Date(),6);
			bussinessChanceVo.setStarttime(DateUtil.convertString(stamps,"yyyy-MM-dd"));
			bussinessChanceVo.setStarttimeLong(DateUtil.convertLong(bussinessChanceVo.getStarttime(),"yyyy-MM-dd"));
			bussinessChanceVo.setTimeType(1);
		}
		if (StringUtil.isNotBlank(bussinessChanceVo.getEndtime())) {
			bussinessChanceVo.setEndtimeLong(DateUtil.convertLong(bussinessChanceVo.getEndtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}

		// 地区处理
		if (null != bussinessChanceVo.getZone() && bussinessChanceVo.getZone() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getZone());
		} else if (null != bussinessChanceVo.getCity() && bussinessChanceVo.getCity() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getCity());
		} else if (null != bussinessChanceVo.getProvince() && bussinessChanceVo.getProvince() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getProvince());
		}

		List<BussinessChanceVo> bussinessChanceList;
		Map<String, Object> map = bussinessChanceService.getServiceBussinessChanceListPage(bussinessChanceVo, page);

		bussinessChanceList = (List<BussinessChanceVo>) map.get("list");

		mv.addObject("bussinessChanceVo", bussinessChanceVo);
		mv.addObject("bussinessChanceList", bussinessChanceList);
		mv.addObject("page", (Page) map.get("page"));
		mv.setViewName("order/bussinesschance/new_service_index");
		return mv;
	}

	/**
	 * 处理商机列表筛选项的字典项
	 *
	 * @param typeList          商机类型,作为父级
	 * @param mv                ModelAndView
	 * @param bussinessChanceVo 查询参数
	 */
	@SuppressWarnings("unchecked")
	private void handleQueryDictionary(List<SysOptionDefinition> typeList, ModelAndView mv, BussinessChanceVo bussinessChanceVo) {
		// VDERP-15213 询价行为(不同字典组下的相同名字的字典项需要合并处理)
		List<SysOptionDefinitionDto> inquiryList = sysOptionDefinitionApiService.getByParentIdList(typeList.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
		Map<String, String> specialKey = JSON.parseObject(inquiryMap, Map.class);
		// "即时通讯"
		List<Integer> instantMessaging = Arrays.stream(specialKey.get("instantMessaging").split(",")).map(Integer::parseInt).collect(Collectors.toList());
		// "留言"
		List<Integer> leaveWord = Arrays.stream(specialKey.get("leaveWord").split(",")).map(Integer::parseInt).collect(Collectors.toList());
		List<SysOptionDefinitionDto> newInquiryList = inquiryList.stream().filter(item -> !(instantMessaging.contains(item.getSysOptionDefinitionId()) || leaveWord.contains(item.getSysOptionDefinitionId())))
				.collect(Collectors.toList());
		newInquiryList.add(new SysOptionDefinitionDto(-1, "即时通讯簿", null, null));
		newInquiryList.add(new SysOptionDefinitionDto(-2, "留言", null, null));
		mv.addObject("inquiryList", newInquiryList);


		// 渠道类型
		List<SysOptionDefinitionDto> newSourceList = sysOptionDefinitionApiService.getByParentIdList(inquiryList.stream().map(SysOptionDefinitionDto::getSysOptionDefinitionId).collect(Collectors.toList()));
		//新客户询价、老客户询价
		SysOptionDefinitionDto sys366 = sysOptionDefinitionApiService.getOptionDefinitionById(366);
		SysOptionDefinitionDto sys477 = sysOptionDefinitionApiService.getOptionDefinitionById(477);
		newSourceList.add(sys366);
		newSourceList.add(sys477);
		mv.addObject("newSourceList", newSourceList.stream().sorted(Comparator.comparing(SysOptionDefinitionDto::getSort)).collect(Collectors.toList()));

		// 渠道名称（只有当form表单查询时 渠道类型 不为空，才会同时查下级 渠道名称 的选项，否则是通过js监听渠道类型筛选项变动时调接口查）
		if (null != bussinessChanceVo.getNewSource() && bussinessChanceVo.getNewSource() > 0) {
			List<SysOptionDefinitionDto> newCommunicationList = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(bussinessChanceVo.getNewSource()));
			if (bussinessChanceVo.getNewSource().equals(366) || bussinessChanceVo.getNewSource().equals(477)) {
				List<String> list = Arrays.asList(inquiryValue.split(","));
				List<SysOptionDefinitionDto> inquiryList2 = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(SysOptionConstant.ID_376));
				Map<String, SysOptionDefinitionDto> map = inquiryList2.stream().collect(Collectors.toMap(SysOptionDefinitionDto::getTitle, s -> s));
				for (String str : list) {
					newCommunicationList.add(map.get(str));
				}
			}
			mv.addObject("newCommunicationList", newCommunicationList);
		}

		if (ErpConst.MINUS_ONE.equals(bussinessChanceVo.getInquiry())) {
			bussinessChanceVo.setInquirySpecial(instantMessaging);
		}
		if (ErpConst.MINUS_TWO.equals(bussinessChanceVo.getInquiry())) {
			bussinessChanceVo.setInquirySpecial(leaveWord);
		}
	}

	/**
	 * <b>Description:</b><br>
	 * 销售商机首页
	 *
	 * @param request
	 * @param bussinessChanceVo
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年6月22日 上午8:57:06
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "saleindex")
	public ModelAndView saleIndex(HttpServletRequest request, BussinessChanceVo bussinessChanceVo,
								  TraderCustomer traderCustomer, @RequestParam(required = false, defaultValue = "1") Integer pageNo,
								  @RequestParam(required = false) Integer pageSize) {
		User user = getSessionUser(request);
		bussinessChanceVo.setCompanyId(user.getCompanyId());
		ModelAndView mv = new ModelAndView();
		//判断是否是B2B的销售
		Boolean saleAndB2BFlagByUserId = userService.getSaleAndB2BFlagByUserId(user);
		int isSaleFlag = 0;
		if(saleAndB2BFlagByUserId){
			isSaleFlag = 1;
		}
		mv.addObject("isSaleFlag", isSaleFlag);

		Page page = getPageTag(request, pageNo, pageSize);
		//如果查询条件是“关闭时间”，则把商机状态设为关闭
		if(null != bussinessChanceVo.getTimeType() && bussinessChanceVo.getTimeType() == 5){
			//把商机状态设为关闭
			bussinessChanceVo.setStatus(4);
		}

		if(traderCustomer.getTraderCustomerId() != null && traderCustomer.getTraderId() != null){
			TraderCustomerVo traderCustomerVo = new TraderCustomerVo();
			traderCustomerVo.setTraderCustomerId(traderCustomer.getTraderCustomerId());
			traderCustomerVo.setTraderId(traderCustomer.getTraderId());
			traderCustomerVo.setBelongPlatform(traderMapper.getTraderByTraderId(traderCustomer.getTraderId()).getBelongPlatform());
			mv.addObject("traderCustomer",traderCustomerVo);
		}

		// 商机类型
		List<SysOptionDefinition> typeList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_390),bncServiceTypeIds);
		mv.addObject("typeList", typeList);
		//商机来源
		List<SysOptionDefinition> sourceList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_365),bncServiceSourceIds);
		mv.addObject("sourceList", sourceList);
		//询价方式
		List<SysOptionDefinition> communicationList=getSysOptionDefinitionList(SysOptionConstant.ID_376);
		mv.addObject("communicationList",communicationList);
		//咨询入口
		List<SysOptionDefinition> entrancesList = sysOptionDefinitionMapper.getSysOptionDefinitionByParentTitle("咨询入口");
		mv.addObject("entrancesList",entrancesList);

		//功能
		List<SysOptionDefinition> functionsList = sysOptionDefinitionMapper.getSysOptionDefinitionByParentTitle("功能");
		mv.addObject("functionsList",functionsList);


		//商机等级
        List<SysOptionDefinition> bussinessLevelList = getSysOptionDefinitionList(SysOptionConstant.ID_938);
        mv.addObject("bussinessLevelList", bussinessLevelList);
        //成单机率
        List<SysOptionDefinition> orderRateList = getSysOptionDefinitionList(SysOptionConstant.ID_951);
        mv.addObject("orderRateList", orderRateList);
        //商机阶段
        List<SysOptionDefinition> bussinessStageList = getSysOptionDefinitionList(SysOptionConstant.ID_943);
        mv.addObject("bussinessStageList", bussinessStageList);
		// 地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mv.addObject("provinceList", provinceList);

		if (null != bussinessChanceVo.getProvince() && bussinessChanceVo.getProvince() > 0) {
			List<Region> cityList = regionService.getRegionByParentId(bussinessChanceVo.getProvince());
			mv.addObject("cityList", cityList);
		}

		if (null != bussinessChanceVo.getCity() && bussinessChanceVo.getCity() > 0) {
			List<Region> zoneList = regionService.getRegionByParentId(bussinessChanceVo.getCity());
			mv.addObject("zoneList", zoneList);
		}

		// VDERP-15213 询价行为(不同字典组下的相同名字的字典项需要合并处理)
		this.handleQueryDictionary(typeList, mv, bussinessChanceVo);

		// 查询用户集合
		List<Integer> userIds = new ArrayList<>();

		// 查询当前用户下所有职位类型为310的员工
		List<User> userList = new ArrayList<>();
		if(ObjectUtils.notEmpty(traderCustomer.getTraderId())){
			User us = userService.getUserByTraderId(traderCustomer.getTraderId(), 1);
			if(us != null){
				userList.add(us);
			}
		}else{
			List<Integer> positionType = new ArrayList<>();
			positionType.add(SysOptionConstant.ID_310);//销售
			userList = userService.getMyUserList(user, positionType, false);
			mv.addObject("userList", userList);
		}
		if (null == bussinessChanceVo.getUserId() || bussinessChanceVo.getUserId() <= 0) {
			for(User u : userList){
				userIds.add(u.getUserId());
			}
			bussinessChanceVo.setUserIds(userIds);
		}else{
			userIds.add(bussinessChanceVo.getUserId());
			if (userIds.size() > 0) {
				bussinessChanceVo.setUserIds(userIds);
			}else{//名下无用户
				userIds.add(-1);
				bussinessChanceVo.setUserIds(userIds);
			}
		}

		// 预计成单时间处理
		if (null != bussinessChanceVo.getCdstarttime() && !"".equals(bussinessChanceVo.getCdstarttime())) {
			bussinessChanceVo.setCdstarttimeLong(DateUtil.convertLong(bussinessChanceVo.getCdstarttime(), "yyyy-MM-dd"));
		}
		if (null != bussinessChanceVo.getCdendtime() && !"".equals(bussinessChanceVo.getCdendtime())) {
			bussinessChanceVo.setCdendtimeLong(DateUtil.convertLong(bussinessChanceVo.getCdendtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}
		//默认前3个月
		// 查询商机开始时间的前3个月
		//仅在页面新打开的时候给它设置默认值，不然会导致每次点击搜索都会覆盖掉用户选择的值
		if(null == bussinessChanceVo.getTimeType()){
			bussinessChanceVo.setTimeType(1);
			bussinessChanceVo.setStarttime(DateUtil.DatePreMonth(new Date(),-3,null));
		}
		// 时间处理
        if (null != bussinessChanceVo.getStarttime() && !"".equals(bussinessChanceVo.getStarttime())) {
            bussinessChanceVo.setStarttimeLong(DateUtil.convertLong(bussinessChanceVo.getStarttime(), "yyyy-MM-dd"));
        }
        if (null != bussinessChanceVo.getEndtime() && !"".equals(bussinessChanceVo.getEndtime())) {
            bussinessChanceVo.setEndtimeLong(DateUtil.convertLong(bussinessChanceVo.getEndtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
        }

		// 地区处理
		if (null != bussinessChanceVo.getZone() && bussinessChanceVo.getZone() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getZone());
		} else if (null != bussinessChanceVo.getCity() && bussinessChanceVo.getCity() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getCity());
		} else if (null != bussinessChanceVo.getProvince() && bussinessChanceVo.getProvince() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getProvince());
		}
		bussinessChanceVo.setCurrUserId(user.getUserId());
		List<BussinessChanceVo> bussinessChanceList;

		Map<String, Object> map = bussinessChanceService.getSaleBussinessChanceListPage(bussinessChanceVo, page);

		bussinessChanceList = (List<BussinessChanceVo>) map.get("list");
		BigDecimal totalAmount = new BigDecimal(0);
		if(CollectionUtils.isNotEmpty(bussinessChanceList)){
		    totalAmount =  bussinessChanceList.get(0).getTotalAmount();
		}
		if(totalAmount == null){
		    totalAmount = new BigDecimal(0);
		}
		//客户分群信息
		List<Integer> traderIds = bussinessChanceList.stream().map(BussinessChance::getTraderId).collect(Collectors.toList());
		Map<Integer, RTraderGroupJTrader> traderGroupMap = getTraderGroup(traderIds);
		mv.addObject("traderGroupMap", traderGroupMap);
		mv.addObject("totalAmount", totalAmount);
		mv.addObject("bussinessChanceVo", bussinessChanceVo);
		mv.addObject("bussinessChanceList", bussinessChanceList);
		mv.addObject("page", (Page) map.get("page"));

		if (null != traderCustomer.getTraderId() && traderCustomer.getTraderId() > 0) {
			mv.addObject("method", "bussinesschance");
			TraderCustomer customerInfoByTraderCustomer = traderCustomerService.getCustomerInfoByTraderCustomer(traderCustomer);
			mv.addObject("customerInfoByTraderCustomer", customerInfoByTraderCustomer);
		}

		mv.setViewName("order/bussinesschance/sale_index");
		return mv;
	}

	@RequestMapping(value = "/linkBncChoosePage")
	public ModelAndView linkBncChoosePage(HttpServletRequest request, BussinessChanceVo bussinessChanceVo,
										  TraderCustomer traderCustomer, @RequestParam(required = false, defaultValue = "1") Integer pageNo,
										  @RequestParam(required = false) Integer pageSize, HttpSession session,
										  Integer first,Integer exceptId){

		User user = getSessionUser(request);
		bussinessChanceVo.setCompanyId(user.getCompanyId());
		ModelAndView mv = new ModelAndView();
		mv.addObject("first",first);
		if(ErpConst.ZERO.equals(first)){
			bussinessChanceVo.setTraderId(null);
		}
//		try {
//            bussinessChanceVo.setBncLink(URLDecoder.decode(bussinessChanceVo.getBncLink(), "UTF-8"));
//        }catch (Exception ex){
//		    logger.error("linkBncChoosePage,param:{},error:{}",bussinessChanceVo.getBncLink(),ex);
//        }
		Page page = getPageTag(request, pageNo, pageSize);

		//如果查询条件是“关闭时间”，则把商机状态设为关闭
		if(null != bussinessChanceVo.getTimeType() && bussinessChanceVo.getTimeType() == 5){
			//把商机状态设为关闭
			bussinessChanceVo.setStatus(4);
		}

		// 商机类型
		List<SysOptionDefinition> typeList = getSysOptionDefinitionList(SysOptionConstant.ID_390);
		mv.addObject("typeList", typeList);
		//商机来源
		List<SysOptionDefinition> sourceList = getSysOptionDefinitionList(SysOptionConstant.ID_365);
		mv.addObject("sourceList", sourceList);
		//商机等级
		List<SysOptionDefinition> bussinessLevelList = getSysOptionDefinitionList(SysOptionConstant.ID_938);
		mv.addObject("bussinessLevelList", bussinessLevelList);
		//成单机率
		List<SysOptionDefinition> orderRateList = getSysOptionDefinitionList(SysOptionConstant.ID_951);
		mv.addObject("orderRateList", orderRateList);
		//商机阶段
		List<SysOptionDefinition> bussinessStageList = getSysOptionDefinitionList(SysOptionConstant.ID_943);
		mv.addObject("bussinessStageList", bussinessStageList);
		// 查询用户集合
		List<Integer> userIds = new ArrayList<>();

			userIds.add(user.getUserId());
			if (userIds.size() > 0) {
				bussinessChanceVo.setUserIds(userIds);
			}else{//名下无用户
				userIds.add(-1);
				bussinessChanceVo.setUserIds(userIds);
			}


		// 预计成单时间处理
		if (null != bussinessChanceVo.getCdstarttime() && bussinessChanceVo.getCdstarttime() != "") {
			bussinessChanceVo.setCdstarttimeLong(DateUtil.convertLong(bussinessChanceVo.getCdstarttime(), "yyyy-MM-dd"));
		}
		if (null != bussinessChanceVo.getCdendtime() && bussinessChanceVo.getCdendtime() != "") {
			bussinessChanceVo.setCdendtimeLong(DateUtil.convertLong(bussinessChanceVo.getCdendtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}
		//默认前3个月
		// 查询商机开始时间的前3个月
//		bussinessChanceVo.setTimeType(1);
//		if(StringUtils.isBlank(bussinessChanceVo.getStarttime())){
//			bussinessChanceVo.setStarttime(DateUtil.DatePreMonth(new Date(),-3,null));
//		}
		// 时间处理
		if (null != bussinessChanceVo.getStarttime() && bussinessChanceVo.getStarttime() != "") {
			bussinessChanceVo.setStarttimeLong(DateUtil.convertLong(bussinessChanceVo.getStarttime(), "yyyy-MM-dd"));
		}
		if (null != bussinessChanceVo.getEndtime() && bussinessChanceVo.getEndtime() != "") {
			bussinessChanceVo.setEndtimeLong(DateUtil.convertLong(bussinessChanceVo.getEndtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}
		bussinessChanceVo.setCurrUserId(user.getUserId());
		List<BussinessChanceVo> bussinessChanceList = null;
		bussinessChanceVo.setIsForLink(ErpConst.ONE);
		Map<String, Object> map = bussinessChanceService.getSaleBussinessChanceListPage(bussinessChanceVo, page);

		bussinessChanceList = (List<BussinessChanceVo>) map.get("list");
		mv.addObject("bussinessChanceVo", bussinessChanceVo);
		mv.addObject("bussinessChanceList", bussinessChanceList);
		mv.addObject("page", (Page) map.get("page"));
		mv.addObject("exceptId",exceptId);
		mv.setViewName("order/bussinesschance/bc_link_choose");
		return mv;
	}
	/**
	 * <b>Description:</b><br>
	 * 批量分配商机
	 *
	 * @param request
	 * @param session
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年6月22日 下午4:14:49
	 */
	@SuppressWarnings("rawtypes")
	@ResponseBody
	@RequestMapping(value = "assignbussinesschance")
	@SystemControllerLog(operationType = "edit",desc = "批量分配商机")
	public ResultInfo assignBussinessChance(HttpServletRequest request, @RequestParam(required = false, value="ids") String parameter, @RequestParam(required = false, value="nos") String bussinessChanceNos, BussinessChanceVo bussinessChanceVo)
	{
		//String parameter = request.getParameter("ids");
		String[] parameterValues = parameter.split(",");
		List<Integer> ids = new ArrayList<Integer>();
		for (String v : parameterValues)
		{
			ids.add(Integer.parseInt(v));
		}
		bussinessChanceVo.setBussinessChanceIds(ids);

		String[] parameterNoValues = bussinessChanceNos.split(",");
		List<String> nos = new ArrayList<String>();
		for (String str : parameterNoValues)
		{
			nos.add(str);
		}
		bussinessChanceVo.setBussinessChanceNos(nos);

		ResultInfo resultInfo = bussinessChanceService.assignBussinessChance(bussinessChanceVo, request.getSession());
		return resultInfo;
	}

	@RequestMapping("getRadioData")
	@ResponseBody
	@NoNeedAccessAuthorization
	public ResultInfo getRadioData(@RequestBody Integer parentId){
		List<SysOptionDefinition> bussInquiryData = getBussInquiryData(parentId);
		return ResultInfo.success(bussInquiryData);
	}

	@RequestMapping("getRadioDataById")
	@ResponseBody
	@NoNeedAccessAuthorization
	public ResultInfo getRadioDataById(@RequestParam("parentId") Integer parentId){
		List<SysOptionDefinition> bussInquiryData = getBussInquiryData(parentId);
		return ResultInfo.success(bussInquiryData);
	}

	/**
	 * <b>Description:</b><br>
	 * 新增售后商机
	 *
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月23日 上午9:29:26
	 */
	@SuppressWarnings("unchecked")
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "addServiceBussinessChance")
	public ModelAndView addServiceBussinessChance(HttpServletRequest request, TraderCustomer traderCustomer,BussinessChanceVo bussinessChanceVo) {
		User user = getSessionUser(request);
		ModelAndView mav = new ModelAndView("order/bussinesschance/add_afterAalesBussinessChance");
		// 商机商品分类
		List<SysOptionDefinition> goodsTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_387);
		mav.addObject("goodsTypeList", goodsTypeList);

		// 省级地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mav.addObject("provinceList", provinceList);
        mav.addObject("typeBdId",bncTypeBdId);
        bussinessChanceVo.setType(391);
        bussinessChanceVo.setInquiry(4002);

        // 询价行为(这边只将“总机询价”下的作为页面展示项)
		List<SysOptionDefinitionDto> inquiryData = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(391));
		mav.addObject("inquiryData", inquiryData);

		// 渠道类型（这边需要查询出所有的“询价行为”，即非总机询价的，作为“渠道”的父级，保证“渠道”的选项是全的）
		List<SysOptionDefinition> typeList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_390),bncServiceTypeIds);
		List<SysOptionDefinitionDto> allInquiryList = sysOptionDefinitionApiService.getByParentIdList(typeList.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
		List<Integer> newInquiryIdList = allInquiryList.stream().map(SysOptionDefinitionDto::getSysOptionDefinitionId).collect(Collectors.toList());
		List<SysOptionDefinitionDto> newSourceList = sysOptionDefinitionApiService.getByParentIdList(newInquiryIdList);
		mav.addObject("bussSource", newSourceList);

		// 渠道名称
		List<SysOptionDefinitionDto> newCommunicationList = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(newSourceList.get(0).getSysOptionDefinitionId()));
		mav.addObject("communications", newCommunicationList);

		// 带入CC呼起页信息
		Integer traderId = traderCustomer.getTraderId();
		if(traderId != null) {
			bussinessChanceVo.setTraderId(traderId);
			TraderCustomerVo traderCustomerVo = traderCustomerService.getTraderCustomerInfo(traderId);
			TraderContact queryTraderContact = new TraderContact();
			queryTraderContact.setTraderId(traderId);
			Map<String, Object> searchNewCustomerPageList = traderCustomerService.searchNewCustomerPageList(traderCustomerVo, null);
			if (searchNewCustomerPageList != null && CollectionUtils.isNotEmpty((List<TraderCustomerVo>)searchNewCustomerPageList.get("searchCustomerList"))){
				List<TraderCustomerVo> traderCustomerVos = ((List<TraderCustomerVo>) searchNewCustomerPageList.get("searchCustomerList"));
				traderCustomerVos.forEach(traderCustomerInfo -> {
					bussinessChanceVo.setTraderContactName(traderCustomerInfo.getName());
					bussinessChanceVo.setUserId(traderCustomerInfo.getPersonalId());
					bussinessChanceVo.setMobile(traderCustomerInfo.getTraderContactMobile());
					bussinessChanceVo.setTelephone(traderCustomerInfo.getTraderContactTelephone());
					bussinessChanceVo.setOtherContact(traderCustomerInfo.getTraderContactMobile2());
					setRegionInfo(mav, traderCustomerInfo.getAreaIds());
				});
			}

			mav.addObject("traderCustomerVo", traderCustomerVo);
		}

		// 查询所有职位类型为310的员工
		List<Integer> positionType = new ArrayList<>();
		positionType.add(SysOptionConstant.ID_310);//销售
		positionType.add(312);//市场
		positionType.add(311);//产品
		user.setPositType(0);
		List<User> userList = userService.getMyUserList(user, positionType, false);
		List<User> newMarketUserList = userService.exincludeLiZhiUser(userList);
//		mv.addObject("userList", newMarketUserList);
		mav.addObject("userList", newMarketUserList);
		bussinessChanceVo.setAttachmentDomain(bussinessChanceService.getUploadDomain());
		bussinessChanceVo.setReceiveTime(DateUtil.sysTimeMillis());
		mav.addObject("bussinessChanceVo", bussinessChanceVo);
		return mav;
	}

	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "addServiceBussinessChanceForIm")
	public ModelAndView addServiceBussinessChanceForIm(HttpServletRequest request, TraderCustomer traderCustomer, BussinessChanceVo bussinessChanceVo, ImBcAddDTO imBcAddDTO) {
		User user = getSessionUser(request);
		ModelAndView mav = new ModelAndView("order/bussinesschance/add_im_bussinessChance");
		String pageFrom = bussinessChanceVo.getPageFrom();
		if(pageFrom.length() > 1000){
			pageFrom = pageFrom.substring(0,1000);
		}
		mav.addObject("pageFrom",pageFrom);
		mav.addObject("cookieId",bussinessChanceVo.getTrackId());
		// 商机商品分类
//		traderCustomer=new TraderCustomer();
//		traderCustomer.setTraderId(148980);
//
//		imBcAddDTO.setSkuNo("V501522");
//		imBcAddDTO.setCategoryId(12198);
//		imBcAddDTO.setSource(1);
//		imBcAddDTO.setFromType(1);
//        imBcAddDTO.setKeyword("123");

		bussinessChanceVo.setSource(SysOptionConstant.ID_4058);
		initContent(mav,bussinessChanceVo,imBcAddDTO);
		bussinessChanceVo.setCommunication(newGetRealSource(imBcAddDTO.getSource()));
		List<SysOptionDefinition> goodsTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_387);
		mav.addObject("goodsTypeList", goodsTypeList);

		// 商机来源(排除被隐藏的商机来源)
		List<SysOptionDefinition> sourceList = getSysOptionDefinitionList(SysOptionConstant.ID_4056);
		mav.addObject("sourceList", sourceList);

		// 询价方式(排除被隐藏的询价方式)
		List<SysOptionDefinition> inquiryList = getSysOptionDefinitionList(SysOptionConstant.ID_4058);
		mav.addObject("inquiryList", inquiryList);

		// 省级地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mav.addObject("provinceList", provinceList);


		/**
		 * 带入CC呼起页信息
		 */
		Integer traderId = traderCustomer.getTraderId();
		if(traderId != null && traderId>0) {
			bussinessChanceVo.setTraderId(traderId);
			TraderCustomerVo traderCustomerVo = traderCustomerService.getTraderCustomerInfo(traderId);
			TraderContact queryTraderContact = new TraderContact();
			queryTraderContact.setTraderId(traderId);
			Map<String, Object> searchNewCustomerPageList = traderCustomerService.searchNewCustomerPageList(traderCustomerVo, null);
			if (searchNewCustomerPageList != null && CollectionUtils.isNotEmpty((List<TraderCustomerVo>)searchNewCustomerPageList.get("searchCustomerList"))){
				List<TraderCustomerVo> traderCustomerVos = ((List<TraderCustomerVo>) searchNewCustomerPageList.get("searchCustomerList"));
				traderCustomerVos.stream().forEach(traderCustomerInfo -> {
//					bussinessChanceVo.setTraderContactName(traderCustomerInfo.getName());
					bussinessChanceVo.setUserId(traderCustomerInfo.getPersonalId());
//					bussinessChanceVo.setMobile(traderCustomerInfo.getTraderContactMobile());
//					bussinessChanceVo.setTelephone(traderCustomerInfo.getTraderContactTelephone());
//					bussinessChanceVo.setOtherContact(traderCustomerInfo.getTraderContactMobile2());
					setRegionInfo(mav, traderCustomerInfo.getAreaIds());
				});
			}

			mav.addObject("traderCustomerVo", traderCustomerVo);
		}

		if(ErpConst.ONE.equals(imBcAddDTO.getFromType())){
			bussinessChanceVo.setUserId(user.getUserId());
		}else{
			imBcAddDTO.setFromType(ErpConst.ZERO);
		}
		mav.addObject("fromType",imBcAddDTO.getFromType());
		// 查询所有职位类型为310的员工
		List<Integer> positionType = new ArrayList<>();
		positionType.add(SysOptionConstant.ID_310);//销售
		positionType.add(312);//市场
		positionType.add(311);//产品
		user.setPositType(0);
		List<User> userList = userService.getMyUserList(user, positionType, false);
		mav.addObject("userList", userList);
		bussinessChanceVo.setAttachmentDomain(bussinessChanceService.getUploadDomain());
		bussinessChanceVo.setReceiveTime(DateUtil.sysTimeMillis());
		bussinessChanceVo.setMobile(null);
		mav.addObject("bussinessChanceVo", bussinessChanceVo);
		return mav;
	}

	/**
	 * <b>Description:</b>根据前台商机来源码获取erp中商机来源值<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/8/19
	 */
	private Integer getRealSource(Integer source){
		if(ErpConst.ONE.equals(source)){
			return bcSourceBdPc;
		}else if(ErpConst.TWO.equals(source)){
			return bcSourceBdApp;
		}else if(ErpConst.THREE.equals(source)){
			return bcSourceBdM;
		}else{
			return 0;
		}
	}

	private Integer newGetRealSource(Integer source){
		if(ErpConst.ONE.equals(source)){
			return ErpConst.bc_source_bd_pc;
		}else if(ErpConst.TWO.equals(source)){
			return ErpConst.bc_source_bd_app;
		}else if(ErpConst.THREE.equals(source)){
			return ErpConst.bc_source_bd_m;
		}else{
			return 0;
		}
	}

	private void initContent(ModelAndView mv,BussinessChanceVo bussinessChanceVo,ImBcAddDTO imBcAddDTO){
		if(imBcAddDTO==null){
			return;
		}
		StringBuilder sb=new StringBuilder();
		if(StringUtil.isNotBlank(imBcAddDTO.getSkuNo())) {
			CoreSkuBaseDTO sku = baseGoodsService.selectSkuBaseByNo(imBcAddDTO.getSkuNo());
			if (sku != null) {
				sb.append(sku.getBrandName());
				sb.append(GoodsConstants.ON_SALE_SEPERATOR);
				sb.append(sku.getSkuNo()+" ");
				sb.append(sku.getShowName());
				sb.append(GoodsConstants.ON_SALE_SEPERATOR);
				sb.append(sku.getModel());
				bussinessChanceVo.setProductComments(sb.toString());
			}
		} else if(imBcAddDTO.getCategoryId()!=null){
            String categoryName=baseCategoryService.getAllLevelCategoryNameById(imBcAddDTO.getCategoryId());
            if(StringUtil.isBlank(categoryName)){
            	return;
			}
            if(StringUtil.isBlank(bussinessChanceVo.getContent())){
            	bussinessChanceVo.setProductComments(categoryName);
			}else{
            	bussinessChanceVo.setProductComments(bussinessChanceVo.getContent()+"&&"+categoryName);
			}
		}else if(StringUtil.isNotBlank(imBcAddDTO.getKeyword())){
//		    mv.addObject("isKey",1);
		    bussinessChanceVo.setProductComments(imBcAddDTO.getKeyword());
        }
	}
	private void setRegionInfo(ModelAndView mav, String areaIds) {
		if (areaIds != null && !"".equals(areaIds)) {
			String areaids[] = areaIds.split(",");
			if (areaids.length == 3) {
				Integer province = Integer.valueOf(areaids[0]);
				mav.addObject("province", province);
				// 市级地区
				List<Region> cityList = regionService.getRegionByParentId(province);
				mav.addObject("cityList", cityList);
				Integer city = Integer.valueOf(areaids[1]);
				mav.addObject("city", city);
				// 县区级地区
				List<Region> zoneList = regionService.getRegionByParentId(city);
				mav.addObject("zoneList", zoneList);
				Integer zone = Integer.valueOf(areaids[2]);
				mav.addObject("zone", zone);
			} else if (areaids.length == 2) {
				Integer province = Integer.valueOf(areaids[0]);
				mav.addObject("province", province);
				// 市级地区
				List<Region> cityList = regionService.getRegionByParentId(province);
				mav.addObject("cityList", cityList);
				Integer city = Integer.valueOf(areaids[1]);
				mav.addObject("city", city);
			} else if (areaids.length == 1) {
				Integer province = Integer.valueOf(areaids[0]);
				mav.addObject("province", province);
			}
		}
	}

	/**
	 * @describe 通过父类ID获取被排除的值的集合
	 * @param SysOptionDefinitionId  父类ID
	 * @param exceptValue 被排除的字符串
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/11 14:20:20
	 */
	private List<SysOptionDefinition> getSysOptionDefinitionsExceptValue(Integer SysOptionDefinitionId, String exceptValue) {
		List<SysOptionDefinition> list = bussinessChanceService.getSysOptionDefinitionListByParentId(SysOptionDefinitionId);
		List<String> exceptValues = Arrays.asList(exceptValue.split(","));
		if (CollectionUtils.isNotEmpty(exceptValues)){
			exceptValues.stream().forEach(exceptValueStr -> {
				Iterator<SysOptionDefinition> sysOptionDefinitionIterator = list.iterator();
				while (sysOptionDefinitionIterator.hasNext()){
					if (exceptValueStr.equals(sysOptionDefinitionIterator.next().getTitle())){
						sysOptionDefinitionIterator.remove();
						break;
					}
				}
			});
		}
		return list;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存新增售后商机
	 *
	 * @param request
	 * @param province
	 * @param city
	 * @param zone
	 * @param fileName
	 * @param time
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月26日 下午2:01:47
	 */
	@FormToken(remove=true)
	@SuppressWarnings("rawtypes")
	@ResponseBody
	@RequestMapping(value = "saveAddServiceBussinessChance")
	@SystemControllerLog(operationType = "add",desc = "保存新增售后商机")
	public ModelAndView saveAddServiceBussinessChance(HttpSession session, Integer province, Integer city, Integer zone, String time, BussinessChance bussinessChance, Attachment attachment)
	{
	    logger.info("售后新增商机 bussinessChance:{}" + bussinessChance.toString());
		User user = getSessionUser(session);
		bussinessChance.setCompanyId(user.getCompanyId());
		ModelAndView mav = new ModelAndView();
		try
		{
			if (province != 0 && city != 0 && zone != 0)
			{
				bussinessChance.setAreaId(zone);
				bussinessChance.setAreaIds(province + "," + city + "," + zone);
			}
			else if (province != 0 && city != 0 && zone == 0)
			{
				bussinessChance.setAreaId(city);
				bussinessChance.setAreaIds(province + "," + city);
			}
			else if (province != 0 && city == 0 && zone == 0)
			{
				bussinessChance.setAreaId(province);
				bussinessChance.setAreaIds(province.toString());
			}
			// modify by Franlin at 2018-07-31 for[581 短信发送机制：未登陆erp的销售员，立即发送短信，在线的销售员如果10分钟未处理商机询价，推送短信] begin
			Long receiveTime = null;
			if (time != null && !"".equals(time))
			{
				receiveTime = DateUtil.convertLong(time, DateUtil.TIME_FORMAT);
			}
			if(null == receiveTime)
			{
				receiveTime = DateUtil.sysTimeMillis();
			}
			bussinessChance.setReceiveTime(receiveTime);

			boolean isSaleSelfCreate = user.getUserId().equals(bussinessChance.getUserId());//是否销售自建
            //根据商机联系方式查找销售
            Integer newSaleUserId = isSaleSelfCreate?bussinessChance.getUserId():bussinessChanceService.checkBusiness2OtherSaleUser(bussinessChance.getUserId());
            bussinessChance.setUserId(newSaleUserId);

			// modify by Franlin at 2018-07-31 for[581 短信发送机制：未登陆erp的销售员，立即发送短信，在线的销售员如果10分钟未处理商机询价，推送短信] end
//			bussinessChance.setType(SysOptionConstant.ID_391);// 商机类型:总机询价
			ResultInfo rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, attachment);
			if (null != rs && rs.getCode() == 0)
			{
				JSONObject json = JSONObject.fromObject(rs.getData());
				BussinessChance bc = (BussinessChance) JSONObject.toBean(json, BussinessChance.class);
				bussinessChanceService.sendMessageIfMerge(bussinessChance.getBussinessChanceId(),bc);

                BussinessChance bussinessChanceSaved = bussinessChanceService.getBusinessChanceByChanceNo(bc.getBussinessChanceNo());
                //判断当前是否为休息时间:
               // boolean isWorkTime = userWorkApiService.isWorkTime();
                logger.info("商机推送是否推送微信消息:{},{},{}",new Object[]{bc.getBussinessChanceNo(),bussinessChanceSaved.getMergeStatus(),isSaleSelfCreate});
                if(bussinessChanceSaved.getMergeStatus() ==NOT_MERGED && !isSaleSelfCreate){
                    Long timeNow = System.currentTimeMillis();
                    BussinessChanceMessageVo messageVo = new BussinessChanceMessageVo();
                    messageVo.setBussinessNo(bc.getBussinessChanceNo());
                    messageVo.setCustomerName(StringUtils.isEmpty(bussinessChance.getTraderName())?"":bussinessChance.getTraderName());
                    messageVo.setMobile((StringUtils.isEmpty(bussinessChance.getTraderContactName())?"":bussinessChance.getTraderContactName())+" "+  (StringUtils.isEmpty(bussinessChance.getMobile())?"":bussinessChance.getMobile()));
                    messageVo.setSendTime(DateUtil.convertString(timeNow,null));
                    messageVo.setRemark(StringUtils.isEmpty(bussinessChance.getProductComments())?"":bussinessChance.getProductComments());
                    try{
                        userWorkApiService.sendMsg(newSaleUserId,messageVo);
                    }catch (Exception e){
                        logger.error("非工作日推送消息失败",e);
                    }
                }


				mav.addObject("url", "./toAfterSalesDetailPage.do?bussinessChanceId=" + bc.getBussinessChanceId());
				return success(mav);
			} else {
				return fail(mav);
			}
		} catch (Exception e) {
			logger.error("saveAddServiceBussinessChance:", e);
			return fail(mav);
		}
	}

	/**
	 * <b>Description:</b><br>
	 * 保存编辑售后商机
	 *
	 * @param request
	 * @param province
	 * @param city
	 * @param zone
	 * @param fileName
	 * @param time
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月26日 下午2:01:47
	 */
	@SuppressWarnings("rawtypes")
	@ResponseBody
	@RequestMapping(value = "saveEditServiceBussinessChance")
	@SystemControllerLog(operationType = "edit",desc = "保存编辑售后商机")
	public ModelAndView saveEditServiceBussinessChance(HttpSession session, Integer province, Integer city, Integer zone,String beforeParams,
			String time, BussinessChance bussinessChance, Attachment attachment) {
		logger.info("保存编辑售后商机 bussinessChance:{}" + bussinessChance.toString());
		User user = getSessionUser(session);
		ModelAndView mav = new ModelAndView();
		try {
			if (province != 0 && city != 0 && zone != 0) {
				bussinessChance.setAreaId(zone);
				bussinessChance.setAreaIds(province + "," + city + "," + zone);
			} else if (province != 0 && city != 0 && zone == 0) {
				bussinessChance.setAreaId(city);
				bussinessChance.setAreaIds(province + "," + city);
			} else if (province != 0 && city == 0 && zone == 0) {
				bussinessChance.setAreaId(province);
				bussinessChance.setAreaIds(province.toString());
			}else{
				bussinessChance.setAreaId(0);
				bussinessChance.setAreaIds("");
			}
			if (time != null && !"".equals(time)) {
				bussinessChance.setReceiveTime(DateUtil.convertLong(time, DateUtil.TIME_FORMAT));
			}
			bussinessChance.setReceiveTime(DateUtil.convertLong(time, DateUtil.TIME_FORMAT));
//			bussinessChance.setType(SysOptionConstant.ID_391);// 商机类型:总机询价
			ResultInfo rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, attachment);
			if (null != rs && rs.getCode() == 0) {
				JSONObject json = JSONObject.fromObject(rs.getData());
				BussinessChance bc = (BussinessChance) JSONObject.toBean(json, BussinessChance.class);

				mav.addObject("url", "./toAfterSalesDetailPage.do?bussinessChanceId=" + bc.getBussinessChanceId());

				return success(mav);
			} else {
				return fail(mav);
			}
		} catch (Exception e) {
			logger.error("saveEditServiceBussinessChance:", e);
			return fail(mav);
		}
	}

	/**
	 * <b>Description:</b><br>
	 * 关闭售后商机
	 *
	 * @param session
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 上午9:45:20
	 */
	@SuppressWarnings("all")
	@ResponseBody
	@RequestMapping(value = "closeSreviceBussnessChance")
	@SystemControllerLog(operationType = "edit",desc = "关闭售后商机")
	public ResultInfo closeSreviceBussnessChance(HttpSession session, BussinessChance bussinessChance) {
		User user = getSessionUser(session);
		ResultInfo rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, null);
		if (rs.getCode() == 0) {
			rs.setData(bussinessChance.getBussinessChanceId());
		}
		return rs;
	}

	/**
	 * <b>Description:</b><br>
	 * 查询售后商机详情
	 *
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月27日 下午6:42:42
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "toAfterSalesDetailPage")
	public ModelAndView toAfterSalesDetailPage(BussinessChance bussinessChance, HttpServletRequest request,
			@RequestParam(required = false, defaultValue = "1") Integer pageNo,
			@RequestParam(required = false) Integer pageSize) {
		ModelAndView mav = new ModelAndView("order/bussinesschance/view_afterAalesBussinessChance");
		pageSize = 10;
		Page page = getPageTag(request, pageNo, pageSize);
        bussinessChanceService.convertBussinessChanceIfMerged(bussinessChance);
		Map<String, Object> map = bussinessChanceService.getAfterSalesDetail(bussinessChance, page);
		if (map.containsKey("bussinessChanceVo")) {
			mav.addObject("bussinessChanceVo", (BussinessChanceVo) map.get("bussinessChanceVo"));
		}
		if (map.containsKey("communicateList")) {
			mav.addObject("communicateList", (List<CommunicateRecord>) map.get("communicateList"));
		}
		mav.addObject("page", page);
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 跳转编辑售后商机页面
	 *
	 * @param bussinessChance
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月27日 下午6:42:42
	 */
	@ResponseBody
	@RequestMapping(value = "toAfterSalesEditPage")
	public ModelAndView toAfterSalesEditPage(BussinessChance bussinessChance, HttpServletRequest request,HttpSession session) throws IOException {
		ModelAndView mav = new ModelAndView("order/bussinesschance/edit_afterAalesBussinessChance");
		// 商机商品分类
		List<SysOptionDefinition> goodsTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_387);
		mav.addObject("goodsTypeList", goodsTypeList);

		// 商机来源
//		List<SysOptionDefinition> scoureList = getSysOptionDefinitionsExceptValue(SysOptionConstant.ID_365,sourceExceptValue);
//		mav.addObject("scoureList", scoureList);
//
//		// 询价方式
//		List<SysOptionDefinition> inquiryList = getSysOptionDefinitionsExceptValue(SysOptionConstant.ID_376,inquiryExceptValue);
//		mav.addObject("inquiryList", inquiryList);

		// 省级地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mav.addObject("provinceList", provinceList);
//		mav.addObject("sourceZj",bncSourceZj);
//		mav.addObject("sourceBd",bncSourceBd);
		mav.addObject("typeBdId",bncTypeBdId);
		// 查询所有销售
		User user = getSessionUser(session);
		List<User> userList = userService.getUserByPositType(SysOptionConstant.ID_310,user.getCompanyId());
		List<User> notDisabledUserList = userList.stream().filter(u -> ErpConst.ZERO.equals(u.getIsDisabled())).collect(Collectors.toList());
		mav.addObject("userList", notDisabledUserList);
		BussinessChanceVo bcv = bussinessChanceService.toAfterSalesEditPage(bussinessChance);
		bcv.setAttachmentDomain(bussinessChanceService.getUploadDomain());
		mav.addObject("bussinessChanceVo", bcv);

		// 询价行为(这边只将“总机询价”下的作为页面展示项)
		List<SysOptionDefinitionDto> inquiryData = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(391));
		mav.addObject("inquiryData", inquiryData);

		// 渠道类型（这边需要查询出所有的“询价行为”，即非总机询价的，作为“渠道”的父级，保证“渠道”的选项是全的）
		List<SysOptionDefinition> typeList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_390),bncServiceTypeIds);
		List<SysOptionDefinitionDto> allInquiryList = sysOptionDefinitionApiService.getByParentIdList(typeList.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
		List<Integer> newInquiryIdList = allInquiryList.stream().map(SysOptionDefinitionDto::getSysOptionDefinitionId).collect(Collectors.toList());
		List<SysOptionDefinitionDto> newSourceList = sysOptionDefinitionApiService.getByParentIdList(newInquiryIdList);
		mav.addObject("bussSource", newSourceList);

		// 渠道名称
		List<SysOptionDefinitionDto> newCommunicationList = sysOptionDefinitionApiService.getByParentIdList(Collections.singletonList(bcv.getSource()));
		mav.addObject("communications", newCommunicationList);

		String areaIds = bcv.getAreaIds();
		setRegionInfo(mav, areaIds);
		mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bcv)));
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 跳转到关闭售后商机页面
	 *
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月29日 下午5:20:25
	 */
	@ResponseBody
	@RequestMapping(value = "toCloseAfterSalesPage")
	public ModelAndView toCloseAfterSalesPage(BussinessChance bussinessChance) {
		ModelAndView mav = new ModelAndView("order/bussinesschance/close_afterAalesBussinessChance");
		// 关闭商机原因
		List<SysOptionDefinition> closeList = getSysOptionDefinitionList(SysOptionConstant.ID_395);
		mav.addObject("closeList", closeList);

		mav.addObject("bussinessChance", bussinessChance);
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 新增销售商机
	 *
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月23日 上午9:29:26
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "addSalesBussinessChance")
	public ModelAndView  addSalesBussinessChance(TraderCustomer traderCustomer) {
		ModelAndView mav = new ModelAndView("order/bussinesschance/add_salesBussinessChance");
		// 商机商品分类
		List<SysOptionDefinition> goodsTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_387);
		mav.addObject("goodsTypeList", goodsTypeList);

		// 询价方式
		List<SysOptionDefinition> scoureList = getSysOptionDefinitionList(SysOptionConstant.ID_376);
		mav.addObject("inquiryList", scoureList);

		TraderContact traderContact = new TraderContact();
		// 联系人
		traderContact.setTraderId(traderCustomer.getTraderId());
		traderContact.setIsEnable(ErpConst.ONE);
		traderContact.setTraderType(ErpConst.ONE);
		List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

		TraderCustomerVo traderCustomerVo = traderCustomerService.getTraderCustomerVo(traderCustomer);

		mav.addObject("traderCustomer", traderCustomerVo);
		mav.addObject("contactList", contactList);
		BussinessChanceVo bcv = new BussinessChanceVo();
		bcv.setReceiveTime(DateUtil.sysTimeMillis());
		mav.addObject("bussinessChanceVo", bcv);
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存新增销售商机
	 *
	 * @param session
	 * @param time
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 下午2:05:44
	 */
	@FormToken(remove=true)
	@SuppressWarnings("rawtypes")
	@ResponseBody
	@RequestMapping(value = "saveAddSalesBussinessChance")
	@SystemControllerLog(operationType = "add",desc = "保存新增销售商机")
	public ModelAndView saveAddSalesBussinessChance(HttpSession session, String time, BussinessChance bussinessChance,
			TraderCustomer traderCustomer) {
		User user = getSessionUser(session);
		bussinessChance.setCompanyId(user.getCompanyId());
		ModelAndView mav = new ModelAndView();
		try {
			if (time != null && !"".equals(time)) {
				bussinessChance.setReceiveTime(DateUtil.convertLong(time, DateUtil.TIME_FORMAT));
			}
			ResultInfo rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, null);
			if (null != rs && rs.getCode() == 0) {
				JSONObject json = JSONObject.fromObject(rs.getData());
				BussinessChance bc = (BussinessChance) JSONObject.toBean(json, BussinessChance.class);
				mav.addObject("url",
						"./toSalesDetailPage.do?bussinessChanceId=" + bc.getBussinessChanceId() + "&traderId="
								+ traderCustomer.getTraderId());
				return success(mav);
			} else {
				return fail(mav);
			}
		} catch (Exception e) {
			logger.error("saveAddSalesBussinessChance:", e);
			return fail(mav);
		}
	}

	/**
	 * <b>Description:</b><br>
	 * 保存编辑销售商机
	 *
	 * @param session
	 * @param time
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 下午2:05:44
	 */
	@SuppressWarnings("rawtypes")
	@ResponseBody
	@RequestMapping(value = "saveEditSalesBussinessChance")
	@SystemControllerLog(operationType = "edit",desc = "保存编辑销售商机")
	public ModelAndView saveEditSalesBussinessChance(HttpSession session, String time, BussinessChance bussinessChance,String beforeParams,
			TraderCustomer traderCustomer) {
		User user = getSessionUser(session);
		ModelAndView mav = new ModelAndView();
		try {
			if (time != null && !"".equals(time)) {
				bussinessChance.setReceiveTime(DateUtil.convertLong(time, DateUtil.TIME_FORMAT));
			}
			ResultInfo rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, null);
			if (null != rs && rs.getCode() == 0) {
				JSONObject json = JSONObject.fromObject(rs.getData());
				BussinessChance bc = (BussinessChance) JSONObject.toBean(json, BussinessChance.class);
				mav.addObject("url",
						"./toSalesDetailPage.do?bussinessChanceId=" + bc.getBussinessChanceId() + "&traderId="
								+ traderCustomer.getTraderId());
				return success(mav);
			} else {
				return fail(mav);
			}
		} catch (Exception e) {
			logger.error("saveEditSalesBussinessChance:", e);
			return fail(mav);
		}
	}


	@RequestMapping(value = "chance/detail/dialog")
	public ModelAndView getChanceDetailDialog(HttpServletRequest request,BussinessChance bussinessChance){
		ModelAndView mv=new ModelAndView();
		if(bussinessChance==null||bussinessChance.getBussinessChanceId()==null){
			return fail(mv);
		}
		mv.setViewName("order/bussinesschance/bussiness_chance_detail_dialog");
		Page page = getPageTag(request, 1, 1);
		Map<String, Object> map = bussinessChanceService.getAfterSalesDetail(bussinessChance, page);
		mv.addObject("bussinessChanceVo", map.get("bussinessChanceVo"));
		return mv;
	}
	/**
	 * <b>Description:</b><br>
	 * 查询销售商机详情
	 *
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月27日 下午6:42:42
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "toSalesDetailPage")
	public ModelAndView toSalesDetailPage(BussinessChance bussinessChance, TraderCustomer traderCustomer,
			HttpServletRequest request, @RequestParam(required = false, defaultValue = "1") Integer pageNo,
			@RequestParam(required = false) Integer pageSize, @RequestParam(required = false, defaultValue = "0") Integer messageId)
	{
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		// modify by Franlin at 2018-08-13 for[4508 从消息弹框点进去查看该消息，仍是未读状态] begin
		if(null != messageId && 0 != messageId)
		{
			try
			{
				MessageUser messageUser = new MessageUser();
				messageUser.setIsView(1);// 已读
				if(null != user)
				{
					messageUser.setUserId(user.getUserId());
				}
				messageUser.setMessageId(messageId);
				messageService.modifyMessageViewStatus(messageUser);
			}
			catch(Exception e)
			{
				logger.error("toSalesDetailPage:", e);
			}
		}

		return new ModelAndView("redirect:"+lxcrmUrl +"/crm/businessChance/profile/detail?id="+bussinessChance.getBussinessChanceId());


		// modify by Franlin at 2018-08-13 for[4508 从消息弹框点进去查看该消息，仍是未读状态] end

//		ModelAndView mav = new ModelAndView("order/bussinesschance/view_salesBussinessChance");
//
//
//		// add by Randy.Xu 2021/3/26 16:14 .Desc: . begin
//		// VDERP-5839 数据越权  只可以看到自己及下属的商机。
//		if(authService.checkUserIsSale(user)) {
//			List<User> userListByorderId = authService.getUserListByorderId(bussinessChance.getBussinessChanceId(), authService.BUSSCHANCE_TYPE);
//			Boolean checkFlag = authService.existOrNot(user, userListByorderId);
//			if (checkFlag) {
//				logger.info("销售越权操作:接口[order/bussinesschance/toSalesDetailPage],行为[查看非自己及下属的商机],操作人{}",user.getUsername());
//			}
//		}
//		// add by Randy.Xu 2021/3/26 16:14 .Desc: . end
//
//		pageSize = 20;
//		Page page = getPageTag(request, pageNo, pageSize);
//		bussinessChanceService.convertBussinessChanceIfMerged(bussinessChance);
//		Integer existquoteflag= quoteService.isExistBussinessChanceId(bussinessChance.getBussinessChanceId());
//		mav.addObject("existquoteflag",existquoteflag);
//		Map<String, Object> map = bussinessChanceService.getAfterSalesDetail(bussinessChance, page);
//		if (map.containsKey("bussinessChanceVo")) {
//			BussinessChanceVo bcv = (BussinessChanceVo) map.get("bussinessChanceVo");
//			if (bcv.getFirstViewTime() == 0) {// 保存第一次访问商机详情时间
//				BussinessChance bc = new BussinessChance();
//				bc.setBussinessChanceId(bussinessChance.getBussinessChanceId());
//				bc.setFirstViewTime(DateUtil.sysTimeMillis());
//				bc.setUpdater(Objects.isNull(user) ? 1 : user.getUserId());
//				bc.setModTime(DateUtil.sysTimeMillis());
//				bussinessChanceService.updateFirstViewTime(bc);
//				log.info("更新商机的首次查看时间：{}", com.alibaba.fastjson.JSONObject.toJSONString(bc));
//			}
//			//商机精准度 VDERP-15625
//			if (bcv.getBusinessChanceAccuracy() != null) {
//				bcv.setBusinessChanceAccuracyShow(BusinessChanceAccuracyErpOldEnum.getTextByCode(bcv.getBusinessChanceAccuracy()));
//			}
//			//判断是否是科研购事业部的
//			Boolean belongPlatfromByOrgAndUser = userService.getBelongPlatfromByOrgAndUser(user, SCIENCE_ORGID, SCIENCE_COMPANY_ID);
//			mav.addObject("belongPlatfromByOrgAndUser", belongPlatfromByOrgAndUser ? 1 : 0);
//			//商机精准度 VDERP-15625
//            List<BncLink> list=JSON.parseArray(bcv.getBncLink(),BncLink.class);
//			mav.addObject("linkBnc",list);
//			mav.addObject("bussinessChanceVo", bcv);
//			//客户分群信息
//			Map<Integer, RTraderGroupJTrader> traderGroupMap = getTraderGroup(Arrays.asList(bcv.getTraderId()));
//			mav.addObject("traderGroupMap",traderGroupMap);
//			if ((traderCustomer.getTraderCustomerId() == null || traderCustomer.getTraderCustomerId() == 0)
//					&& (bcv.getTraderId() != null && bcv.getTraderId() != 0)) {
//				TraderCustomerVo traderCustomerVo = traderCustomerService.getCustomerBussinessInfo(bcv.getTraderId());
//				mav.addObject("traderCustomer", traderCustomerVo);
//			}else{
//				mav.addObject("traderCustomer", traderCustomer);
//			}
//		}
//		if (map.containsKey("communicateList")) {
//			mav.addObject("communicateList", (List<CommunicateRecord>) map.get("communicateList"));
//		}
//
//
//
//		Map<String, Object> historicInfo=actionProcdefService.getHistoric(processEngine, "closeBussinesschanceVerify_"+ bussinessChance.getBussinessChanceId());
//		Task taskInfo = (Task) historicInfo.get("taskInfo");
//		mav.addObject("taskInfo", historicInfo.get("taskInfo"));
//		mav.addObject("startUser", historicInfo.get("startUser"));
//		mav.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
//		// 最后审核状态
//		mav.addObject("endStatus",historicInfo.get("endStatus"));
//		mav.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
//		mav.addObject("commentMap", historicInfo.get("commentMap"));
//		String endStatus = (String) historicInfo.get("endStatus");
//		boolean	endStatusflag = true;
//		if(endStatus != null && endStatus.equals("主管审核")){
//			endStatusflag= false;
//		}
//		mav.addObject("endStatusflag",endStatusflag);
//		String verifyUsers = null;
//	    	if(null!=taskInfo){
//	    	    Map<String, Object> taskInfoVariables= actionProcdefService.getVariablesMap(taskInfo);
//	    	    verifyUsers = (String) taskInfoVariables.get("verifyUsers");
//	    	}
//	    	mav.addObject("verifyUsers", verifyUsers);
//		mav.addObject("page", page);
//		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 跳转编辑销售商机页面
	 *
	 * @param bussinessChance
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月27日 下午6:42:42
	 */
	@ResponseBody
	@RequestMapping(value = "toSalesEditPage")
	public ModelAndView toSalesEditPage(BussinessChance bussinessChance, TraderCustomer traderCustomer) throws IOException {
		ModelAndView mav = new ModelAndView("order/bussinesschance/edit_salesBussinessChance");
		// 商机商品分类
		List<SysOptionDefinition> goodsTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_387);
		mav.addObject("goodsTypeList", goodsTypeList);

		// 询价方式
//		List<SysOptionDefinition> scoureList = getSysOptionDefinitionList(SysOptionConstant.ID_376);
		List<String> list = Arrays.asList(inquiryValue.split(","));
		List<SysOptionDefinition> inquiryList =  bussinessChanceService.getSysOptionDefinitionListByParentId(SysOptionConstant.ID_376);
		Map<String,SysOptionDefinition> map = inquiryList.stream().collect(Collectors.toMap(SysOptionDefinition::getTitle, s -> s));
		List<SysOptionDefinition> newinquiryList = new ArrayList<>();
		for (String str : list) {
			newinquiryList.add(map.get(str));
		}
		mav.addObject("inquiryList", newinquiryList);

		TraderContact traderContact = new TraderContact();
		// 联系人
		traderContact.setTraderId(traderCustomer.getTraderId());
		traderContact.setIsEnable(ErpConst.ONE);
		traderContact.setTraderType(ErpConst.ONE);
		List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

		// traderCustomer=traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
		TraderCustomerVo traderCustomerVo = traderCustomerService.getTraderCustomerVo(traderCustomer);

		mav.addObject("traderCustomer", traderCustomerVo);
		mav.addObject("contactList", contactList);

		BussinessChanceVo bcv = bussinessChanceService.toAfterSalesEditPage(bussinessChance);
		mav.addObject("bussinessChanceVo", bcv);
		mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bcv)));
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 跳转到编辑商机备注页面
	 * @param bussinessChance
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月29日 下午5:20:25
	 */
	@ResponseBody
	@RequestMapping(value = "editCommentsPage")
	public ModelAndView editCommentsPage(BussinessChance bussinessChance) throws IOException {
		ModelAndView mav = new ModelAndView("order/bussinesschance/edit_comments");
		String comments = URLDecoder.decode(URLDecoder.decode(bussinessChance.getComments(), "UTF-8"), "UTF-8");
		bussinessChance.setComments(comments);
		mav.addObject("bussinessChance", bussinessChance);
		mav.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(bussinessChance)));
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存销售商机备注
	 *
	 * @param session
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 上午9:45:20
	 */
	@SuppressWarnings("all")
	@ResponseBody
	@RequestMapping(value = "saveSalesBussnessChanceComments")
	@SystemControllerLog(operationType = "edit",desc = "保存销售商机备注")
	public ResultInfo saveSalesBussnessChanceComments(HttpSession session, BussinessChance bussinessChance,String beforeParams) {
		User user = getSessionUser(session);
		bussinessChance.setOrgId(user.getOrgId());
		ResultInfo rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, null);
		if (rs.getCode() == 0) {
			rs.setData(bussinessChance.getBussinessChanceId());
		}
		return rs;
	}

	/**
	 * <b>Description:</b><br>
	 * 跳转到关闭销售商机页面
	 *
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月29日 下午5:20:25
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "toCloseSalesPage")
	public ModelAndView toCloseSalesPage(BussinessChance bussinessChance, TraderCustomer traderCustomer,String taskId) {
		ModelAndView mav = new ModelAndView("order/bussinesschance/close_salesBussinessChance");
		// 关闭商机原因
		List<SysOptionDefinition> closeList = getSysOptionDefinitionList(SysOptionConstant.ID_395);
		// 商机作废原因
		List<SysOptionDefinition> zfList = getSysOptionDefinitionList(SysOptionConstant.ID_961);
		mav.addObject("zfList", zfList);
		mav.addObject("closeList", closeList);
		mav.addObject("taskId", taskId);
		mav.addObject("bussinessChance", bussinessChance);
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 关闭销售商机
	 *
	 * @param session
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 上午9:45:20
	 */
	@FormToken(remove=true)
	@SuppressWarnings("all")
	@ResponseBody
	@RequestMapping(value = "closeSalesBussnessChance")
	@SystemControllerLog(operationType = "edit",desc = "关闭销售商机")
	public ResultInfo closeSalesBussnessChance(HttpServletRequest request,HttpSession session, BussinessChance bussinessChance,String taskId) {
		log.info("关闭销售商机：{}",JSON.toJSON(bussinessChance));
		User user = getSessionUser(session);
		if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
			List<User> userListByorderId = authService.getUserListByorderId(bussinessChance.getBussinessChanceId(), authService.BUSSCHANCE_TYPE);
			if (authService.existOrNot(user, userListByorderId)) {
				logger.info("销售越权操作:接口[order/bussinesschance/closeSalesBussnessChance],行为[关闭非自己及下属的商机],操作人{}",user.getUsername());
			}
		}
		// 关闭原因
		SysOptionDefinitionDto sysOptionDefinition = sysOptionDefinitionApiService.getOptionDefinitionById(bussinessChance.getStatusComments());
		String reason1 = sysOptionDefinition.getTitle();
		// 其他原因
		String reason2 = bussinessChance.getOtherReason();
		// 作废原因
		String reason3 = "";
		if (bussinessChance.getCancelReason() != null){
			SysOptionDefinitionDto sysOptionDefinitionDto = sysOptionDefinitionApiService.getOptionDefinitionById(bussinessChance.getCancelReason());
			reason3 = sysOptionDefinitionDto.getTitle();
		}
		// 关闭备注
		String reason4 = bussinessChance.getClosedComments();

		StringBuilder sb = new StringBuilder();
		sb.append(reason1).append(" ");
		if (StringUtils.isNotBlank(reason2)) {
			sb.append(reason2).append(" ");
		}
		if (StringUtils.isNotBlank(reason3)) {
			sb.append(reason3).append(" ");
		}
		sb.append(reason4).append(" ");
		String closeReason = sb.toString();

		//走审核流程,原处理过程取消
		bussinessChance.setOrgId(user.getOrgId());
		bussinessChance.setCloseCheckStatus(1);
		ResultInfo rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, null);
		Map<String, Object> paraMap = bussinessChanceService.getAfterSalesDetail(bussinessChance, null);
		BussinessChanceVo bussinessChanceVo = (BussinessChanceVo) paraMap.get("bussinessChanceVo");
		bussinessChance.setBussinessChanceNo(bussinessChanceVo.getBussinessChanceNo());
		bussinessChance.setTraderId(bussinessChanceVo.getTraderId());
		if (rs.getCode() == 0) {
			rs.setData(bussinessChance.getBussinessChanceId());
		}
			try {
	    		Map<String, Object> variableMap = new HashMap<String, Object>();
	    		//开始生成流程(如果没有taskId表示新流程需要生成)
	    		if(StringUtils.isBlank(taskId) || taskId.equals("0")){
	    		    variableMap.put("bussinessChance", bussinessChance);
	    		    variableMap.put("currentAssinee", user.getUsername());
	    		    variableMap.put("processDefinitionKey","closeBussinesschanceVerify");
	    		    variableMap.put("businessKey","closeBussinesschanceVerify_" + bussinessChance.getBussinessChanceId());
	    		    variableMap.put("relateTableKey",bussinessChance.getBussinessChanceId());
	    		    variableMap.put("relateTable", "T_BUSSINESS_CHANCE");
	    		    variableMap.put("orgId", user.getOrgId());
	    		    actionProcdefService.createProcessInstance(request,"closeBussinesschanceVerify","closeBussinesschanceVerify_" + bussinessChance.getBussinessChanceId(),variableMap);
	    		}
	    		//默认申请人通过
	    		//根据BusinessKey获取生成的审核实例
	    		Map<String, Object> historicInfo=actionProcdefService.getHistoric(processEngine, "closeBussinesschanceVerify_"+ bussinessChance.getBussinessChanceId());
        	    		if(historicInfo.get("endStatus") != "审核完成"){
        	    		Task taskInfo = (Task) historicInfo.get("taskInfo");
        	    		taskId = taskInfo.getId();
        	    		Authentication.setAuthenticatedUserId(user.getUsername());
        	    		Map<String, Object> variables = new HashMap<String, Object>();
        	    		//设置审核完成监听器回写参数
        	    		variables.put("tableName", "T_BUSSINESS_CHANCE");
        	    		variables.put("id", "BUSSINESS_CHANCE_ID");
        	    		variables.put("idValue", bussinessChance.getBussinessChanceId());
        	    		variables.put("key", "STATUS");
        	    		variables.put("key1","CLOSE_CHECK_STATUS");
        	    		//关闭
        	    		variables.put("value", 4);
        	    		//当前商机的状态，如果审核驳回，则商机待审核状态更新为0
						variables.put("value1",0);
        	    		//回写数据的表在db中
        	    		variables.put("db", 2);
        	    		//默认审批通过
        	    		ResultInfo<?> complementStatus = actionProcdefService.complementTask(request,taskId,closeReason,user.getUsername(),variables);
        	    		//如果未结束添加审核对应主表的审核状态
                		if(!complementStatus.getData().equals("endEvent")){
                		    verifiesRecordService.saveVerifiesInfo(taskId,0);
                		}
	    		}
				return  new ResultInfo<>(0,"操作成功",bussinessChance.getBussinessChanceId());
			} catch (Exception e) {
				logger.error("closeSalesBussnessChance:", e);
				return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
			}

	}

	/**
	 * <b>Description:</b><br>
	 * 确认客户页面
	 *
	 * @param bussinessChance
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 下午4:52:49
	 */
	@ResponseBody
	@RequestMapping(value = "confirmCustomer")
	public ModelAndView confirmCustomer(BussinessChance bussinessChance, TraderCustomer traderCustomer) {
		ModelAndView mav = new ModelAndView("order/bussinesschance/confirm_customer_index");
		mav.addObject("bussinessChance", bussinessChance);
		mav.addObject("traderCustomer", traderCustomer);
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 确认客户页面搜索客户
	 *
	 * @param customerName
	 * @param request
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 下午5:26:47
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "/getCustomersByName")
	public ModelAndView getCustomersByName(String customerName, HttpServletRequest request,
			BussinessChance bussinessChance, TraderCustomer traderCustomer,
			@RequestParam(required = false, defaultValue = "1") Integer pageNo,
			@RequestParam(required = false, defaultValue = "10") Integer pageSize) {
		ModelAndView mav = new ModelAndView("order/bussinesschance/confirm_customer_index");
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		Page page = getPageTag(request, pageNo, 10);
		TraderCustomerVo tcv = new TraderCustomerVo();
		tcv.setName(customerName);
		tcv.setCompanyId(user.getCompanyId());
		//搜索只能搜索当前名下的客户
		// 查询所有职位类型为310的员工
		List<Integer> positionType = new ArrayList<>();
		positionType.add(SysOptionConstant.ID_310);//销售
		List<User> userList = userService.getMyUserList(user, positionType, false);
//		tcv.setTraderList(userService.getTraderIdListByUserList(userList, ErpConst.ONE.toString()));

		Map<String, Object> map = traderCustomerService.getTraderCustomerVoPage(tcv, page,userList);
		List<TraderCustomerVo> list = (List<TraderCustomerVo>) map.get("list");
		page = (Page) map.get("page");
		mav.addObject("list", list);
		mav.addObject("page", page);
		mav.addObject("customerName", customerName);
		mav.addObject("traderCustomer", traderCustomer);
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 跳转到选择后确认客户页面
	 *
	 * @param bussinessChance
	 * @param traderCustomer
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年7月3日 下午2:29:56
	 */
	@ResponseBody
	@RequestMapping(value = "/viewConfirmCustomer")
	public ModelAndView viewConfirmCustomer(BussinessChance bussinessChance, TraderCustomer traderCustomer) {
		ModelAndView mav = new ModelAndView("order/bussinesschance/view_confirm_customer");
		TraderContact traderContact = new TraderContact();
		// 联系人
		traderContact.setTraderId(traderCustomer.getTraderId());
		traderContact.setIsEnable(ErpConst.ONE);
		traderContact.setTraderType(ErpConst.ONE);
		List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

		// traderCustomer=traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
		TraderCustomerVo traderCustomerVo = traderCustomerService.getTraderCustomerVo(traderCustomer);
		mav.addObject("bussinessChance", bussinessChance);
		mav.addObject("traderCustomer", traderCustomerVo);
		mav.addObject("contactList", contactList);
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 确认客户页面添加联系人
	 *
	 * @param bussinessChance
	 * @param traderCustomer
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年7月3日 下午4:05:19
	 */
	@ResponseBody
	@RequestMapping(value = "/addConfirmCustomer")
	public ModelAndView addConfirmCustomer(BussinessChance bussinessChance, TraderCustomer traderCustomer) {
		ModelAndView mav = new ModelAndView("order/bussinesschance/add_confirm_customer");
		TraderCustomerVo traderCustomerVo = traderCustomerService.getTraderCustomerVo(traderCustomer);
		mav.addObject("bussinessChance", bussinessChance);
		mav.addObject("traderCustomer", traderCustomerVo);
		return mav;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存确认后的客户信息,并返回客户的主键id
	 *
	 * @param bussinessChance
	 * @param session
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 下午5:35:35
	 */
	@SuppressWarnings("all")
	@ResponseBody
	@RequestMapping(value = "saveConfirmCustomer")
	@SystemControllerLog(operationType = "edit",desc = "保存确认后的客户信息")
	public ResultInfo saveConfirmCustomer(BussinessChance bussinessChance, HttpSession session) {
		User user = getSessionUser(session);
		if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
			List<User> userListByorderId = authService.getUserListByorderId(bussinessChance.getBussinessChanceId(), authService.BUSSCHANCE_TYPE);
			if (authService.existOrNot(user, userListByorderId)) {
				logger.info("销售越权操作:接口[order/bussinesschance/saveConfirmCustomer],行为[编辑并确认非自己及下属的客户],操作人{}",user.getUsername());
			}
		}
		TraderCustomerVo tcv = bussinessChanceService.saveConfirmCustomer(bussinessChance, user, null);
		ResultInfo rs = null;
		if (tcv != null) {
			rs = new ResultInfo(0, "操作成功！",
					bussinessChance.getBussinessChanceId() + "," + tcv.getTraderId() + "," + tcv.getTraderCustomerId());
			// -------------关联成功 联系人注册 商机关联客户，商机的客户所属平台为贝登医疗，客户性质为分销 --------
			if (bussinessChance.getTraderContactId() != null) {
				autoRegistrationService.doRegistration(bussinessChance.getTraderContactId(),false);
			}
			// -------------关联成功 联系人注册--------
		} else {
			rs = new ResultInfo(1, "操作失败！");
		}
		return rs;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存确认后的客户信息,并返回客户的主键id,新增客户联系人
	 *
	 * @param bussinessChance
	 * @param session
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 下午5:35:35
	 */
	@SuppressWarnings("all")
	@ResponseBody
	@RequestMapping(value = "addSaveConfirmCustomer")
	@SystemControllerLog(operationType = "edit",desc = "保存确认后的客户信息，新增客户联系人")
	public ResultInfo saveConfirmCustomer(BussinessChance bussinessChance, TraderContact traderContact,
			HttpSession session) {
		User user = getSessionUser(session);
		TraderCustomerVo tcv = bussinessChanceService.saveConfirmCustomer(bussinessChance, user, traderContact);
		ResultInfo rs = null;
		if (tcv != null) {
			if(tcv.getTraderCustomerId() == -1){
				rs = new ResultInfo(-1, "该客户已存在相同联系人");
			}else{
				// 商机关联客户，商机的客户所属平台为贝登医疗，客户性质为分销
				// 新增的有回写联系人id
				if (bussinessChance.getTraderContactId() != null) {
					autoRegistrationService.doRegistration(bussinessChance.getTraderContactId(),false);
				}
				// 注册======
				rs = new ResultInfo(0, "操作成功！",
						bussinessChance.getBussinessChanceId() + "," + tcv.getTraderId() + "," + tcv.getTraderCustomerId());
			}
		} else {
			rs = new ResultInfo(1, "操作失败！");
		}
		return rs;
	}

	/**
	 * <b>Description:</b><br>
	 * 销售新增沟通记录
	 *
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年6月30日 上午10:17:31
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "addCommunicatePage")
	public ModelAndView addCommunicatePage(BussinessChance bussinessChance, TraderCustomer traderCustomer,
			HttpServletRequest request) {
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		ModelAndView mav = new ModelAndView("order/bussinesschance/add_communicate");
		TraderContact traderContact = new TraderContact();
		// 联系人
		List<TraderContact> contactList = new ArrayList<>();
		if(traderCustomer.getTraderId()!=null && traderCustomer.getTraderId()!=0){
		    traderContact.setTraderId(traderCustomer.getTraderId());
	        traderContact.setIsEnable(ErpConst.ONE);
	        traderContact.setTraderType(ErpConst.ONE);
	        contactList = traderCustomerService.getTraderContact(traderContact);
		}
		//判断是否是科研购事业部的
		Boolean belongPlatfromByOrgAndUser = userService.getBelongPlatfromByOrgAndUser(user, SCIENCE_ORGID, SCIENCE_COMPANY_ID);
		mav.addObject("belongPlatfromByOrgAndUser", belongPlatfromByOrgAndUser ? 1 : 0);
		// 客户标签
		Tag tag = new Tag();
		tag.setTagType(SysOptionConstant.ID_32);
		tag.setIsRecommend(ErpConst.ONE);
		tag.setCompanyId(user.getCompanyId());

		Integer pageNo = 1;
		Integer pageSize = 10;

		Page page = getPageTag(request, pageNo, pageSize);
		Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

		try {
			TraderBaseInfoDto traderBaseInfo = getTraderBaseInfo(bussinessChance);
			mav.addObject("traderBaseInfo", traderBaseInfo);
		} catch (Exception e) {
			logger.error("添加客户名称信息error ", e);
		}

		mav.addObject("traderCustomer", traderCustomer);
		mav.addObject("bussinessChance", bussinessChance);
		mav.addObject("contactList", contactList);

		addCommunicateGoalList2ModelView(mav);

		CommunicateRecord communicate = new CommunicateRecord();
		communicate.setBegintime(DateUtil.sysTimeMillis());
		communicate.setEndtime(DateUtil.sysTimeMillis()+2*60*1000);
		mav.addObject("communicateRecord", communicate);
		// 沟通方式
		List<SysOptionDefinition> communicateList = getSysOptionDefinitionList(SysOptionConstant.ID_23);
		mav.addObject("communicateList", communicateList);
		//当前时间
		mav.addObject("nowDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 1).getTime());
		//15天后的时间
        mav.addObject("hideDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 30).getTime());
		mav.addObject("tagList", (List<Tag>) tagMap.get("list"));
		mav.addObject("page", (Page) tagMap.get("page"));
		return mav;
	}

	/**
	 * 获取商机客户基本信息
	 *
	 * @param bussinessChance
	 */
	private TraderBaseInfoDto getTraderBaseInfo(BussinessChance bussinessChance) {
		TraderBaseInfoDto traderBaseInfoDto = new TraderBaseInfoDto();
		if (bussinessChance == null || bussinessChance.getBussinessChanceId() == null){
			return traderBaseInfoDto;
		}
		BussinessChance bussinessChanceInfo = bussinessChanceService.getBussinessChanceInfo(bussinessChance);
		if (bussinessChanceInfo == null){
			return traderBaseInfoDto;
		}
		bussinessChance.setType(bussinessChanceInfo.getType());
		bussinessChance.setBusinessChanceAccuracy(bussinessChanceInfo.getBusinessChanceAccuracy());
		if (StringUtils.isBlank(bussinessChanceInfo.getCheckTraderName())){
			traderBaseInfoDto.setTraderName(bussinessChanceInfo.getTraderName());
		}else {
			traderBaseInfoDto.setTraderName(bussinessChanceInfo.getCheckTraderName());
		}
		if (bussinessChanceInfo.getUserId() != null){
			try {
				traderBaseInfoDto.setSalesNameStr(userService.getUserNameByUserId(bussinessChanceInfo.getUserId()));
			} catch (Exception e) {
				logger.error("沟通信息添加销售名称error", e);
			}
		}
		return traderBaseInfoDto;
	}

	/**
	 * <b>Description:</b><br>
	 * 编辑沟通记录
	 *
	 * @param communicateRecord
	 * @param request
	 * @param session
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年5月24日 下午1:31:13
	 */
	@ResponseBody
	@RequestMapping(value = "/editcommunicate")
	public ModelAndView editCommunicate(CommunicateRecord communicateRecord, TraderCustomer traderCustomer,
			BussinessChance bussinessChance, HttpServletRequest request, HttpSession session) throws IOException {
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		ModelAndView mv = new ModelAndView("order/bussinesschance/edit_communicate");
		CommunicateRecord communicate = traderCustomerService.getCommunicate(communicateRecord);
		communicate.setTraderCustomerId(communicateRecord.getTraderCustomerId());
		communicate.setTraderId(communicateRecord.getTraderId());

		TraderContact traderContact = new TraderContact();
		List<TraderContact> contactList = new ArrayList<>();
		// 联系人
		if(communicateRecord.getTraderId()!=0){
			traderContact.setTraderId(communicateRecord.getTraderId());
			traderContact.setIsEnable(ErpConst.ONE);
			traderContact.setTraderType(ErpConst.ONE);
			contactList = traderCustomerService.getTraderContact(traderContact);
		}else {
			traderContact.setTraderContactId(0);
			traderContact.setIsEnable(ErpConst.ONE);
			traderContact.setName(communicate.getContact());
			traderContact.setMobile(communicate.getContactMob());
			contactList.add(traderContact);
		}

		addCommunicateGoalList2ModelView(mv);

		// 客户标签
		Tag tag = new Tag();
		tag.setTagType(SysOptionConstant.ID_32);
		tag.setIsRecommend(ErpConst.ONE);
		tag.setCompanyId(user.getCompanyId());

		Integer pageNo = 1;
		Integer pageSize = 10;

		Page page = getPageTag(request, pageNo, pageSize);
		Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

		mv.addObject("communicateRecord", communicate);

		mv.addObject("contactList", contactList);
		//当前时间
		mv.addObject("nowDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 1).getTime());
        //15天后的时间
		mv.addObject("hideDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 30).getTime());
		mv.addObject("tagList", (List<Tag>) tagMap.get("list"));
		mv.addObject("page", (Page) tagMap.get("page"));
		mv.addObject("method", "communicaterecord");
		mv.addObject("traderCustomer", traderCustomer);
		mv.addObject("bussinessChance", bussinessChance);
		mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(communicate)));
		//判断是否是科研购事业部的
		Boolean belongPlatfromByOrgAndUser = userService.getBelongPlatfromByOrgAndUser(user, SCIENCE_ORGID, SCIENCE_COMPANY_ID);
		mv.addObject("belongPlatfromByOrgAndUser", belongPlatfromByOrgAndUser ? 1 : 0);
		try {
			TraderBaseInfoDto traderBaseInfo = getTraderBaseInfo(bussinessChance);
			mv.addObject("traderBaseInfo", traderBaseInfo);
		} catch (Exception e) {
			logger.error("添加客户名称信息error", e);
		}

		if(StringUtils.isNotBlank(communicate.getCoidUri())){
			String voiceStatusGptSuccess = "9";//Gpt解析成功
			CommunicateVoiceTaskDto taskDto = communicateVoiceTaskApi.selectByCommunicateRecordIdAndSence(communicate.getCommunicateRecordId(),
					AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode());

			mv.addObject("communicateTypeName",AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getName());

			if(taskDto != null && voiceStatusGptSuccess.equals(taskDto.getVoiceStatus()) ){
				List<VoiceFieldResultDto> voiceFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
						communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
						AiConstant.CODE_GROUP_SUMMARY);
				mv.addObject("voiceFieldList", voiceFieldList);

				List<VoiceFieldResultDto> voiceToDoFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
						communicate.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicate.getCommunicateType()).getCode(),
						AiConstant.CODE_GROUP_TODOTASK);
				mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
			}else{
				String voiceStatusIgnore = "-1";//忽略的状态
				if(taskDto == null){
					//如果解析任务不存在，即历史数据或忽略
				}else if(voiceStatusIgnore.equals(taskDto.getVoiceStatus())){
					//忽略，则不做任务数据展示
				} else{  //非以上情况，即如果解析任务存在，但是在解析中，则展示AI解析中...
					List<VoiceFieldResultDto> voiceFieldList = new ArrayList<>();
					VoiceFieldResultDto tipVoiceField = new VoiceFieldResultDto();
					tipVoiceField.setFieldName("提示");
					tipVoiceField.setFieldResult("AI解析中...");
					voiceFieldList.add(tipVoiceField);
					mv.addObject("voiceFieldList", voiceFieldList);

					List<VoiceFieldResultDto> voiceToDoFieldList = new ArrayList<>();
					VoiceFieldResultDto tipVoiceToDoField = new VoiceFieldResultDto();
					tipVoiceToDoField.setFieldName("提示");
					tipVoiceToDoField.setFieldResult("AI解析中...");
					voiceToDoFieldList.add(tipVoiceToDoField);
					mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
				}
			}
		}
		return mv;
	}


	private void addCommunicateGoalList2ModelView(ModelAndView mv){
		// 沟通方式
		List<SysOptionDefinition> communicateList = getSysOptionDefinitionList(SysOptionConstant.ID_23);
		mv.addObject("communicateList", communicateList);

		List<SysOptionDefinition> communicateGoalList = new ArrayList<>();
		//VDERP-3323 沟通目的按照指定排序
		Map<Integer, SysOptionDefinition> communicateGoalMap = getSysOptionDefinitionList(SysOptionConstant.ID_24).stream().collect(Collectors.toMap(SysOptionDefinition::getSysOptionDefinitionId,item -> item));
		Integer[]  communicateGoalIdArray = new Integer[]{468,636,635,637,638,265};
		for (int i = 0; i < communicateGoalIdArray.length; i++) {
			if (communicateGoalMap.containsKey(communicateGoalIdArray[i])){
				communicateGoalList.add(communicateGoalMap.get(communicateGoalIdArray[i]));
			}
		}
		mv.addObject("communicateGoalList", communicateGoalList);
	}

	/**
	 * <b>Description:</b><br>
	 * 保存新增沟通
	 *
	 * @param communicateRecord
	 * @param request
	 * @param session
	 * @return
	 * @throws Exception
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年5月24日 下午2:36:53
	 */
	@FormToken(remove=true)
	@SuppressWarnings("all")
	@ResponseBody
	@RequestMapping(value = "/saveaddcommunicate")
	@SystemControllerLog(operationType = "add",desc = "保存商机新增沟通记录")
	public ResultInfo saveAddCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,
			HttpSession session) throws Exception {
		User user = getSessionUser(session);
		if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
			Integer ownerUserIdByorderId = authService.getOwnerUserIdByorderId(communicateRecord.getBussinessChanceId(), authService.BUSSCHANCE_TYPE);
			if (Objects.nonNull(user) && !user.getUserId().equals(ownerUserIdByorderId)) {
				logger.info("销售越权操作:接口[order/bussinesschance/saveaddcommunicate],行为[在非自己归属的商机下编辑商机并添加沟通记录],操作人{}",user.getUsername());
			}
		}
		Boolean record = false;
		communicateRecord.setCompanyId(user.getCompanyId());
		communicateRecord.setCommunicateType(SysOptionConstant.ID_244);// 询价
		communicateRecord.setRelatedId(communicateRecord.getBussinessChanceId());
		if (null != communicateRecord.getCommunicateRecordId() && communicateRecord.getCommunicateRecordId() > 0) {
			record = traderCustomerService.saveEditCommunicate(communicateRecord, request, session);
		} else {
			record = traderCustomerService.saveAddCommunicate(communicateRecord, request, session);
			if(record){
				BussinessChance query=new BussinessChance();
				query.setBussinessChanceId(communicateRecord.getBussinessChanceId());
				query=bussinessChanceService.getBussinessChanceInfo(query);
				bussinessChanceService.updateBcStatusByTerm(query);
			}
		}
		if (record) {
			return new ResultInfo(0, "操作成功！", communicateRecord.getBussinessChanceId() + "," + communicateRecord.getTraderId());
		} else {
			return new ResultInfo(1, "操作失败！");
		}

	}

	/**
	 * <b>Description:</b><br>
	 * 保存商机编辑沟通记录
	 *
	 * @param communicateRecord
	 * @param request
	 * @param session
	 * @return
	 * @throws Exception
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年5月24日 下午2:36:53
	 */
	@SuppressWarnings("all")
	@ResponseBody
	@RequestMapping(value = "/saveeditcommunicate")
	@SystemControllerLog(operationType = "edit",desc = "保存商机编辑沟通记录")
	public ResultInfo saveEditCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,String beforeParams,
			HttpSession session) throws Exception {
		Boolean record = false;
		communicateRecord.setCommunicateType(SysOptionConstant.ID_244);// 询价
		communicateRecord.setRelatedId(communicateRecord.getBussinessChanceId());
		if (null != communicateRecord.getCommunicateRecordId() && communicateRecord.getCommunicateRecordId() > 0) {
			record = traderCustomerService.saveEditCommunicate(communicateRecord, request, session);
		} else {
			record = traderCustomerService.saveAddCommunicate(communicateRecord, request, session);
		}
		if (record) {
			return new ResultInfo(0, "操作成功！", communicateRecord.getBussinessChanceId() + "," + communicateRecord.getTraderId());
		} else {
			return new ResultInfo(1, "操作失败！");
		}

	}

	/**
	 * <b>Description:</b><br> 确认审核页面
	 * @param session
	 * @param taskId
	 * @param pass
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年1月3日 下午4:27:02
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/complement")
	public ModelAndView complement(HttpSession session, String taskId, Boolean pass) {
		ModelAndView mv = new ModelAndView();
		mv.addObject("taskId", taskId);
		mv.addObject("pass", pass);
		mv.setViewName("order/bussinesschance/complement");
		return mv;
	}

	/**
	 *
	 * <b>Description:</b><br>
	 * 审核操作
	 *
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2017年11月10日 下午1:39:42
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "/complementTask")
	@SystemControllerLog(operationType = "edit",desc = "商机审核操作")
	public ResultInfo<?> complementTask(HttpServletRequest request, String taskId, String comment,String reason,Boolean pass,
			HttpSession session) {
		Map<String, Object> variables = new HashMap<String, Object>();
		variables.put("pass", pass);
		variables.put("reason", reason);
		//审批操作
		try {
		    	Integer status = 0;
			if(pass){
			    //如果审核通过
			     status = 0;
			}else{
			    //如果审核不通过
			    status = 2;
			    verifiesRecordService.saveVerifiesInfo(taskId,status);
			}

		    User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		    ResultInfo<?> complementStatus = actionProcdefService.complementTask(request,taskId,comment,user.getUsername(),variables);
		    //如果未结束添加审核对应主表的审核状态
    		    if(!complementStatus.getData().equals("endEvent")){
    		    verifiesRecordService.saveVerifiesInfo(taskId,status);
    		    }
		    return new ResultInfo(0, "操作成功");
		} catch (Exception e) {
			logger.error("business chnace complementTask:", e);
			return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
		}
	}
	
	@Autowired
	private TraderCustomerApiService traderCustomerApiService;
	/**
     *
     * <b>Description:</b>新增联系人
     * @param bussinessChance
     * @param traderCustomer
     * @param request
     * @return ModelAndView
     * @Note
     * <b>Author：</b> scott.zhu
     * <b>Date:</b> 2019年3月1日 上午9:30:51
     */
    //@FormToken(save=true)
    @ResponseBody
    @RequestMapping(value = "/addTraderContact")
    public ModelAndView addTraderContact(BussinessChance bussinessChance, TraderCustomer traderCustomer,
            HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        // 联系人
        if(traderCustomer.getTraderId()!=null && traderCustomer.getTraderId()!=0){
			TraderCustomerDto traderCustomerDto = traderCustomerApiService.getTraderCustomerInfoByTraderId(traderCustomer.getTraderId());
			traderCustomer.setCustomerNature(traderCustomerDto.getCustomerNature());
            //查询客户信息
            mv.setViewName("trader/customer/add_contact");
            mv.addObject("traderCustomer", traderCustomer);
        }else{
            mv.setViewName("order/bussinesschance/add_contact");
        }
        return mv;
    }
    /**
     *
     * <b>Description:</b>更新商机情况（新增/编辑）
     * @param bussinessChance
     * @param request
     * @return ModelAndView
     * @Note
     * <b>Author：</b> scott.zhu
     * <b>Date:</b> 2019年3月4日 下午12:59:56
     */
    @FormToken(save=true)
    @ResponseBody
    @RequestMapping(value = "/addBussinessStatus")
    public ModelAndView addBussinessStatus(BussinessChance bussinessChance,Integer type,
            HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        BussinessChance bc = bussinessChanceService.getBussinessChanceInfo(bussinessChance);
        //商机等级
        List<SysOptionDefinition> bussinessLevelList = getSysOptionDefinitionList(SysOptionConstant.ID_938);
        mv.addObject("bussinessLevelList", bussinessLevelList);
        //商机阶段
        List<SysOptionDefinition> bussinessStageList = getSysOptionDefinitionList(SysOptionConstant.ID_943);
        mv.addObject("bussinessStageList", bussinessStageList);
        //询价类型
        List<SysOptionDefinition> enquiryTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_948);
        mv.addObject("enquiryTypeList", enquiryTypeList);
        //成单机率
        List<SysOptionDefinition> orderRateList = getSysOptionDefinitionList(SysOptionConstant.ID_951);
        mv.addObject("orderRateList", orderRateList);
        //当前时间
        mv.addObject("nowDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 1).getTime());

        mv.addObject("bussinessChance", bc);
        mv.addObject("type", type);
        mv.setViewName("order/bussinesschance/add_bussinessStatus");
        return mv;
    }
    /**
     *
     * <b>Description:</b>更新的商机信息
     * @param bussinessChance
     * @param request
     * @param session
     * @return
     * @throws Exception ResultInfo
     * @Note
     * <b>Author：</b> scott.zhu
     * <b>Date:</b> 2019年3月4日 下午2:07:22
     */
    @FormToken(remove=true)
    @ResponseBody
    @RequestMapping(value = "/saveAddBussinessStatus")
    public ResultInfo saveAddBussinessStatus(BussinessChance bussinessChance, HttpServletRequest request,
            HttpSession session) throws Exception {
        User user = getSessionUser(session);

		// add by Randy.Xu 2021/4/9 15:04 .Desc: . begin
		//VDERP-5839 数据越权  只可以编辑自己及下属的商机。
		if(authService.checkUserIsSale(user)) {
			List<User> userListByorderId = authService.getUserListByorderId(bussinessChance.getBussinessChanceId(), authService.BUSSCHANCE_TYPE);
			Boolean checkFlag = authService.existOrNot(user, userListByorderId);
			if (checkFlag) {
				logger.info("销售越权操作:接口[order/bussinesschance/saveAddBussinessStatus],行为[编辑非自己及下属的商机],操作人{}",user.getUsername());
			}
		}
		// add by Randy.Xu 2021/4/9 15:04 .Desc: . end

        Boolean record = false;
        if(StringUtils.isNotBlank(bussinessChance.getOrderTimeStr())){
        	bussinessChance.setOrderTime(DateUtil.convertLong(bussinessChance.getOrderTimeStr() + " 00:00:00", ""));
        }
        record = bussinessChanceService.saveAddBussinessStatus(bussinessChance);
        if (record) {
            return new ResultInfo(0, "操作成功！",bussinessChance);
        } else {
            return new ResultInfo(1, "操作失败！");
        }

    }
 	/**
 	 * @description: 商机新增聚合页
 	 * @return:
 	 * @author: Strange
 	 * @date:  2020/6/5
 	 **/
	@FormToken(save = true)
    @ResponseBody
    @RequestMapping("/newAddBussinesChance")
	public ModelAndView newAddBussinesChance(TraderCustomerVo traderCustomer,HttpServletRequest request,Integer bussinessChanceId,
											 String optType,String linkBncStr,Integer isLink, Integer isClues, Integer businessCluesId){
		ModelAndView mv = new ModelAndView();
		//VDERP-10360 线索转商机
        mv.addObject("isClues", isClues);
        mv.addObject("businessCluesId", businessCluesId);
		if (ErpConst.ONE.equals(isClues)) {
			// 查询当前线索是否已关联过商机了
			BusinessCluesDto businessCluesDto = businessCluesApi.selectBusinessChanceByCluesId(businessCluesId);
			if (!Objects.isNull(businessCluesDto.getBusinessChanceId())) {
				mv.addObject("message", "当前线索已创建商机，请返回线索列表页刷新重试");
				return fail(mv);
			}
		}

		BussinessChance bussinessChance = new BussinessChance();
		TraderCustomerVo traderCustomerVo = new TraderCustomerVo();
		Integer traderId = traderCustomer.getTraderId();
		//修改商机
		List<BncLink> list=null;
		if(bussinessChanceId != null && "edit".equals(optType)){
			bussinessChance.setBussinessChanceId(bussinessChanceId);
			bussinessChance = bussinessChanceService.getBussinessChanceInfo(bussinessChance);
			traderId = bussinessChance.getTraderId();
		}
		if(ErpConst.ONE.equals(isLink)){
			list= JSON.parseArray(linkBncStr,BncLink.class);
			if(CollectionUtils.isNotEmpty(list)) {
				Integer maxId = 0;
				for(BncLink b:list){
					if(b!=null&&b.getId()!=null&&b.getId()>maxId){
						maxId=b.getId();
					}
				}
				if(maxId>0){
					bussinessChance.setBussinessChanceId(maxId);
					bussinessChance = bussinessChanceService.getBussinessChanceInfo(bussinessChance);
					if(bussinessChanceId != null && "edit".equals(optType)){
						bussinessChance.setBussinessChanceId(bussinessChanceId);
					}else{
						bussinessChance.setBussinessChanceId(null);
					}
				}
			}
			bussinessChance.setBncLink(linkBncStr);
		}
		if(traderId != null && traderId !=0) {
			bussinessChance.setTraderId(traderId);
			Integer traderContactId = traderCustomer.getTraderContactId();
			if(traderContactId != null){
				bussinessChance.setTraderContactId(traderContactId);
			}
			traderCustomerVo = traderCustomerService.getTraderCustomerInfo(traderId);
			List<TraderCustomerCategory> customerCategories = traderCustomerVo.getCustomerCategories();
			StringBuffer sb = new StringBuffer();
			if(!org.springframework.util.CollectionUtils.isEmpty(customerCategories)){
				for (TraderCustomerCategory customerCategory : customerCategories) {
					sb = sb.append(customerCategory.getCustomerCategoryName()).append(" ");
				}
			}else{
				sb = sb.append(traderCustomerVo.getCustomerTypeStr()).append(" ").append(traderCustomerVo.getCustomerNatureStr());
			}
			traderCustomerVo.setCustomerTypeStr(sb.toString());
			TraderContact traderContact = new TraderContact();
			traderContact.setTraderId(traderId);
			traderContact.setIsEnable(1);
			List<TraderContact> traderContactList = traderCustomerService.getTraderContact(traderContact);
			if(CollectionUtils.isNotEmpty(traderContactList) && traderContactList.size() ==1) {
				mv.addObject("contactCount",1);
			}
			mv.addObject("traderContactList",traderContactList);
		}
		//设置选择框信息
		setChooseInfo(mv,bussinessChanceId,optType,isLink,bussinessChance.getSource(),bussinessChance.getCommunication());
		// 省级地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mv.addObject("provinceList", provinceList);
		mv.addObject("traderCustomerVo",traderCustomerVo);
		mv.addObject("bussinessChance",bussinessChance);
		mv.addObject("optType",optType);
		mv.addObject("linkBnc",list);
		mv.addObject("isLink",isLink);
		mv.setViewName("order/bussinesschance/add_salesBussinessChanceNew");
		return mv;
	}


	@FormToken(save = true)
    @ResponseBody
    @RequestMapping("/newAddBussinesChanceForIm")
	public ModelAndView newAddBussinesChanceForIm(ImBcAddDTO imBcAddDTO,HttpServletRequest request,Integer bussinessChanceId,String optType){
		ModelAndView mv = new ModelAndView();
//		imBcAddDTO.setTraderId(148980);
//		imBcAddDTO.setMobile("18262815230");
//		imBcAddDTO.setSkuNo("V501687");
//		imBcAddDTO.setSource(1);
		final BussinessChance bussinessChance = new BussinessChance();
		bussinessChance.setSource(getRealSource(imBcAddDTO.getSource()));
		TraderCustomerVo traderCustomerVo = new TraderCustomerVo();
		Integer traderId = imBcAddDTO.getTraderId();
		//修改商机
//		if(bussinessChanceId != null && "edit".equals(optType)){
//			bussinessChance.setBussinessChanceId(bussinessChanceId);
//			bussinessChance = bussinessChanceService.getBussinessChanceInfo(bussinessChance);
//			traderId = bussinessChance.getTraderId();
//		}
		if(traderId != null && traderId !=0) {

//			Integer traderContactId = traderCustomer.getTraderContactId();
//			if(traderContactId != null){
//				bussinessChance.setTraderContactId(traderContactId);
//			}
            traderCustomerVo = traderCustomerService.getTraderCustomerInfo(traderId);
            if (traderCustomerVo != null) {
                bussinessChance.setTraderId(traderId);
                List<TraderCustomerCategory> customerCategories = traderCustomerVo.getCustomerCategories();
                StringBuffer sb = new StringBuffer();
                if (!org.springframework.util.CollectionUtils.isEmpty(customerCategories)) {
                    for (TraderCustomerCategory customerCategory : customerCategories) {
                        sb = sb.append(customerCategory.getCustomerCategoryName()).append(" ");
                    }
                } else {
                    sb = sb.append(traderCustomerVo.getCustomerTypeStr()).append(" ").append(traderCustomerVo.getCustomerNatureStr());
                }
                traderCustomerVo.setCustomerTypeStr(sb.toString());
                TraderContact traderContact = new TraderContact();
                traderContact.setTraderId(traderId);
                traderContact.setIsEnable(1);
                List<TraderContact> traderContactList = traderCustomerService.getTraderContact(traderContact);
                bussinessChance.setCommunication(imBcCommunication);
                traderContactList.stream().forEach(e -> {
                    if (e.getMobile() != null && e.getMobile().equals(imBcAddDTO.getMobile())) {
                        bussinessChance.setTraderContactId(e.getTraderContactId());
                    }
                });
                if (CollectionUtils.isNotEmpty(traderContactList) && traderContactList.size() == 1) {
                    mv.addObject("contactCount", 1);
                }
                if (StringUtil.isNotBlank(imBcAddDTO.getSkuNo())) {
                    CoreSkuBaseDTO sku = baseGoodsService.selectSkuBaseByNo(imBcAddDTO.getSkuNo());
                    if (sku != null) {
                        mv.addObject("skuId", sku.getSkuId());
                    }
                }
                mv.addObject("traderContactList", traderContactList);
            }
        }
		//设置选择框信息
		setChooseInfo(mv, bussinessChanceId, optType, 0, null, null);
		// 省级地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mv.addObject("provinceList", provinceList);
		mv.addObject("traderCustomerVo",traderCustomerVo);
		mv.addObject("bussinessChance",bussinessChance);
		mv.addObject("optType",optType);
		mv.setViewName("order/bussinesschance/add_im_sale_bc");
		return mv;
	}

	/**
	 * @description:设置复选框元素
	 * @return:
	 * @author: Strange
	 * @date: 2020/6/9
	 **/
	private void setChooseInfo(ModelAndView mv, Integer bussinessChanceId, String optType,Integer isLink,Integer com,Integer haveComm) {
		// 商机商品分类
		List<SysOptionDefinition> goodsTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_387);
		mv.addObject("goodsTypeList", goodsTypeList);
		//商机阶段
		List<SysOptionDefinition> bussinessStageList = getSysOptionDefinitionList(SysOptionConstant.ID_943);
		mv.addObject("bussinessStageList", bussinessStageList);
		//询价类型
		List<SysOptionDefinition> enquiryTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_948);
		mv.addObject("enquiryTypeList", enquiryTypeList);
		//成单机率
		List<SysOptionDefinition> orderRateList = getSysOptionDefinitionList(SysOptionConstant.ID_951);
		mv.addObject("orderRateList", orderRateList);
		// 商机来源
		List<SysOptionDefinition> scoureList = getSysOptionDefinitionList(SysOptionConstant.ID_365);
		mv.addObject("scoureList", scoureList);
		//采购方式
		List<SysOptionDefinition> purchasingTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_404);
		mv.addObject("purchasingTypeList", purchasingTypeList);
		//付款条件
		List<SysOptionDefinition> payList = getSysOptionDefinitionList(SysOptionConstant.ID_407).stream().sorted(Comparator.comparing(SysOptionDefinition::getSysOptionDefinitionId)).collect(Collectors.toList());
		mv.addObject("payList", payList);
		//付款方式
		List<SysOptionDefinition> payTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
		mv.addObject("payTypeList", payTypeList);
		//采购时间
		List<SysOptionDefinition> purchasingTimeList = getSysOptionDefinitionList(SysOptionConstant.ID_410);
		mv.addObject("purchasingTimeList", purchasingTimeList);
		//发票类型
		List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);
		mv.addObject("invoiceTypeList", invoiceTypeList);
		//运费说明
		List<SysOptionDefinition> freightDescriptionList = getSysOptionDefinitionList(SysOptionConstant.ID_469);
		mv.addObject("freightDescriptionList", freightDescriptionList);
		//商机等级
		List<SysOptionDefinition> bussinessLevelList = getSysOptionDefinitionList(SysOptionConstant.ID_938);
		for (SysOptionDefinition sysOptionDefinition : bussinessLevelList) {
			if(sysOptionDefinition.getTitle().equals("S")){
				sysOptionDefinition.setTitle("S非常重要");
			}else if(sysOptionDefinition.getTitle().equals("A")){
				sysOptionDefinition.setTitle("A重要");
			}else if(sysOptionDefinition.getTitle().equals("B")){
				sysOptionDefinition.setTitle("B一般");
			}else if(sysOptionDefinition.getTitle().equals("C")){
				sysOptionDefinition.setTitle("C无效");
			}
		}
		mv.addObject("bussinessLevelList", bussinessLevelList);
		// 询价方式
		List<String> list = Arrays.asList(inquiryValue.split(","));
		List<SysOptionDefinition> inquiryList =  bussinessChanceService.getSysOptionDefinitionListByParentId(SysOptionConstant.ID_376);
		Map<String,SysOptionDefinition> map = inquiryList.stream().collect(Collectors.toMap(SysOptionDefinition::getTitle, s -> s));
		List<SysOptionDefinition> newinquiryList = new ArrayList<>();
		for (String str : list) {
			newinquiryList.add(map.get(str));
		}
		mv.addObject("inquiryList", newinquiryList);
		if (bussinessChanceId != null && "edit".equals(optType)||ErpConst.ONE.equals(isLink)){
			if (!Objects.isNull(com)) {
				List<SysOptionDefinition> sysOptionDefinitionListByParentId = bussinessChanceService.getSysOptionDefinitionListByParentId(com);
				if (!Objects.isNull(haveComm)) {
					SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(haveComm);
					if (sysOptionDefinition != null && !Objects.isNull(sysOptionDefinition.getSysOptionDefinitionId())) {
						Set<Integer> collect = sysOptionDefinitionListByParentId.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toSet());
						if (!collect.contains(sysOptionDefinition.getSysOptionDefinitionId())) {
							sysOptionDefinitionListByParentId.add(sysOptionDefinition);
						}
						// 预防脏数据问题
						Set<Integer> collect2 = inquiryList.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toSet());
						if (!collect2.contains(sysOptionDefinition.getSysOptionDefinitionId())) {
							inquiryList.add(sysOptionDefinition);
						}
					}

				}
				// 新老数据兼容 source 为新值的时候，communication 走联动下的数据
				if (com >= 4000) {
					inquiryList = sysOptionDefinitionListByParentId;
				}
			}
			mv.addObject("inquiryList", inquiryList);
		}
	}

	/**
	 * @description:保存商机和报价单
	 * @return:
	 * @author: Strange
	 * @date: 2020/6/10
	 **/
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping("/saveBussinessChanceAndQuoteOrder")
	public ResultInfo saveBussinessChanceAndQuoteOrder(HttpServletRequest request, HttpSession session, String orderTimeStr, BussinessChanceVo bussinessChance,
													   QuoteorderVo quote, String optType, String applyType, Integer isClues, Integer businessCluesId,Integer TMH) {
		User user = getSessionUser(session);
		bussinessChance.setCompanyId(user.getCompanyId());
		if (ErpConst.ONE.equals(TMH)){
			bussinessChance.setCommunication(SYS_OPTION_TMB);
		}
		ResultInfo rs = new ResultInfo();
		try {
			if (orderTimeStr != null && !"".equals(orderTimeStr)) {
				bussinessChance.setOrderTime(DateUtil.convertLong(orderTimeStr, DateUtil.DATE_FORMAT));
			}
			TraderCustomerVo traderCustomerInfo = traderCustomerService.getTraderCustomerInfo(bussinessChance.getTraderId());
			bussinessChance.setUserId(user.getUserId());
			if (StringUtils.isBlank(optType) || !"edit".equalsIgnoreCase(optType)) {
				//销售自增商机，询价产品
				//询价产品字段取产品的产品名称，最多三个。用“、”符号拼接，超过字段长度限制的自动截断】，取第一个产品回填基本信息模块的“产品名称”、“产品品牌”、”产品分类“等字段
				if (CollectionUtils.isNotEmpty(quote.getQuoteorderGoods())) {
					String goodsName = quote.getQuoteorderGoods().stream().limit(3).map(QuoteorderGoods::getGoodsName).reduce((a, b) -> a + "、" + b).get();
					bussinessChance.setGoodsName(quote.getQuoteorderGoods().get(0).getGoodsName());
					bussinessChance.setContent(goodsName);
					bussinessChance.setGoodsBrand(quote.getQuoteorderGoods().get(0).getBrandName());
				}
			}

			setBussinessTraderInfo(bussinessChance, traderCustomerInfo, optType);

			//保存商机单
			rs = saveBussiness(bussinessChance, optType, user, rs, isClues, businessCluesId);
			Integer bussinessChanceId;
			if ("edit".equals(optType)) {
				bussinessChanceId = bussinessChance.getBussinessChanceId();
			} else {
				JSONObject json = JSONObject.fromObject(rs.getData());
				BussinessChance bc = (BussinessChance) JSONObject.toBean(json, BussinessChance.class);
				bussinessChanceId = bc.getBussinessChanceId();
			}
			logger.info("保存商机成功 id:{}", bussinessChanceId);
			quote.setBussinessChanceId(bussinessChanceId);
			if (quoteService.isExistBussinessChanceId(quote.getBussinessChanceId()) > 0) {
				rs.setCode(-1);
				rs.setMessage("该商机已存在报价单生成报价单失败");
				return rs;
			}
			//保存报价单
			Integer quoteId = saveQuoteorder(request, session, quote, applyType, user, traderCustomerInfo);
			Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
			map.put("quoteId", quoteId);
			map.put("applyType", applyType);
			rs.setData(map);
			return rs;
		} catch (Exception e) {
			logger.error("saveBussinessChanceAndQuoteOrder error:", e);
			rs.setMessage("出现异常，请联系管理员");
			rs.setCode(-2);
			return rs;
		}
	}

	private Integer saveQuoteorder(HttpServletRequest request, HttpSession session, QuoteorderVo quote, String applyType, User user,TraderCustomerVo traderCustomerInfo) {
		quoteService.setQuteOrderUserInfo(quote, user);
		setQuoteorderInfo(quote,traderCustomerInfo);
		logger.info("开始保存报价单 quote:{}",quote.toString());
		ResultInfo<Quoteorder> quoteorderResultInfo = quoteService.saveQuote(quote);

		if(quoteorderResultInfo != null && quoteorderResultInfo.getCode().equals(0) ){
			Quoteorder quoteorder = quoteorderResultInfo.getData();

			//如果是'特麦帮'商机名称的报价单，存入T_SPECIAL_SALES
			BussinessChance bussinessChance = bussinessChanceMapper.selectByPrimaryKey(quoteorder.getBussinessChanceId());
			if (SYS_OPTION_TMB.equals(bussinessChance.getCommunication())){
				SpecialSalesDto specialSalesDto = new SpecialSalesDto();
				specialSalesDto.setRelateId(quoteorder.getQuoteorderId());
				specialSalesDto.setRelateNo(quoteorder.getQuoteorderNo());
				specialSalesDto.setRelateType(SpecialSalesEnum.QUOTEORDER.getCode());
				specialSalesDto.setIsDelete(ErpConst.ZERO);
				specialSalesApiService.insertSpecialSales(specialSalesDto);
			}

			List<QuoteorderGoods> quoteorderGoods = quote.getQuoteorderGoods();
			if(CollectionUtils.isNotEmpty(quoteorderGoods)){
				Attachment ach = new Attachment();
				for (QuoteorderGoods quoteorderGood : quoteorderGoods) {
					setQuoteorderGoodsUserInfo(user, ach, quoteorderGood);
					quoteorderGood.setQuoteorderId(quoteorder.getQuoteorderId());
					if(quoteorderGood.getGoodsId() == null || StringUtil.isBlank(quoteorderGood.getSku())){
						quoteorderGood.setIsTemp(1);
					}
					quoteService.saveQuoteGoods(quoteorderGood,ach);
				}

			}
			if("saveAndapply".equals(applyType)){
				quoteService.editApplyValidQuoteorder(request,quoteorder,"0",session);
			}
			return quoteorder.getQuoteorderId();
		}
		return 0;
	}

	private void saleDefaultComments(QuoteorderGoods quoteorderGoods) {
		// 报价默认内部备注
		LabelQuery labelQuery = new LabelQuery();
		labelQuery.setScene(Integer.valueOf(1));
		labelQuery.setRelationId(quoteorderGoods.getQuoteorderId());
		labelQuery.setSkuNo(quoteorderGoods.getSku());
		List<RemarkComponent> remarkComponents = remarkComponentMapper.selectComponentRelationList(labelQuery);

		if(CollectionUtils.isEmpty(remarkComponents)){
			ComponentRelation componentRelation = new ComponentRelation();
			componentRelation.setScene(Integer.valueOf(1));
			componentRelation.setRelationId(quoteorderGoods.getQuoteorderId());
			componentRelation.setSkuNo(quoteorderGoods.getSku());
			componentRelation.setSkuName(quoteorderGoods.getGoodsName());
			componentRelation.setTime(0L);
			saleorderGoodsMapper.insertInnerInsideDefault(componentRelation);
		}
	}

	private void setQuoteorderGoodsUserInfo(User user, Attachment ach, QuoteorderGoods quoteorderGood) {
		if(user != null) {
		   quoteorderGood.setCreator(user.getUserId());
		   quoteorderGood.setAddTime(DateUtil.sysTimeMillis());

		   quoteorderGood.setUpdater(user.getUserId());
		   quoteorderGood.setModTime(DateUtil.sysTimeMillis());

		   ach.setAddTime(DateUtil.sysTimeMillis());
		   ach.setCreator(user.getUserId());
	   }
	}

	private ResultInfo saveBussiness(BussinessChanceVo bussinessChance, String optType, User user, ResultInfo rs, Integer isClues, Integer businessCluesId) {
		if("edit".equals(optType)){
			bussinessChance.setModTime(DateUtil.sysTimeMillis());
			logger.info("saveBussiness edit：{}",bussinessChance.toString());
			Boolean aBoolean = bussinessChanceService.saveAddBussinessStatus(bussinessChance);
			if(aBoolean){
				rs.setCode(0);
			}
		}else {
			bussinessChance.setAddTime(DateUtil.sysTimeMillis());
			bussinessChance.setReceiveTime(DateUtil.sysTimeMillis());
			bussinessChance.setAssignTime(DateUtil.sysTimeMillis());
			//类型为销售新增商机
			bussinessChance.setType(392);
			//  VDERP-8191询价行为为销售新增
			bussinessChance.setInquiry(4062);
			rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, null);
			if(rs != null && rs.getCode().equals(0)) {
				JSONObject json = JSONObject.fromObject(rs.getData());
				BussinessChance bc = (BussinessChance) JSONObject.toBean(json, BussinessChance.class);
				bussinessChance.setBussinessChanceId(bc.getBussinessChanceId());
				bussinessChance.setUserId(null);
				bussinessChanceService.saveAddBussinessStatus(bussinessChance);

				//VDERP-10360 线索转商机 新增商机成功后，将更新线索关联的商机id
				if (ErpConst.ONE.equals(isClues)) {
					BusinessCluesDto businessCluesDto = BusinessCluesDto.builder().businessCluesId(businessCluesId)
																				  .businessChanceId(bc.getBussinessChanceId())
																				  .build();
					businessCluesApi.updateBusinessCluesChanceId(businessCluesDto);
				}

			}
		}
		return rs;
	}

	private void setQuoteorderInfo(QuoteorderVo quote,TraderCustomerVo traderCustomerInfo) {
		if(traderCustomerInfo != null){
			SysOptionDefinition sysOptionDefinition = getSysOptionDefinition(Integer.valueOf(traderCustomerInfo.getCustomerLevel()));
			if(sysOptionDefinition!=null){
				quote.setCustomerLevel(sysOptionDefinition.getTitle());
			}
			quote.setCustomerNature(traderCustomerInfo.getCustomerNature());
			quote.setCustomerType(traderCustomerInfo.getCustomerType());
			quote.setTraderName(traderCustomerInfo.getTraderName());
			quote.setArea(traderCustomerInfo.getAddress());
			quote.setSalesAreaId(traderCustomerInfo.getAreaId());
			quote.setTerminalTraderId(traderCustomerInfo.getTraderId());
			quote.setTerminalTraderName(traderCustomerInfo.getTraderName());
			quote.setTerminalTraderType(traderCustomerInfo.getCustomerType());
			TraderContact traderContact = traderCustomerService.getTraderContactById(quote.getTraderContactId());
			quote.setTraderContactName(traderContact.getName());
			quote.setMobile(traderContact.getMobile());
			quote.setTelephone(traderContact.getTelephone());
			quote.setAddress(traderContact.getAddress());
			quote.setTraderAddressId(traderContact.getAreaId());
			List<QuoteorderGoods> quoteorderGoods = quote.getQuoteorderGoods();
			BigDecimal totalAmount = BigDecimal.ZERO;
			if(CollectionUtils.isNotEmpty(quoteorderGoods)) {
				for (QuoteorderGoods quoteorderGood : quoteorderGoods) {
					totalAmount = totalAmount.add(quoteorderGood.getPrice().multiply(new BigDecimal(quoteorderGood.getNum())));
				}
			}
			quote.setTotalAmount(totalAmount);
		}
	}

	private void setBussinessTraderInfo(BussinessChanceVo bussinessChance,TraderCustomerVo traderCustomerInfo, String optType) {
		bussinessChance.setCheckTraderName(traderCustomerInfo.getTraderName());
		bussinessChance.setCheckTraderArea(traderCustomerInfo.getAddress());
		Integer traderContactId = bussinessChance.getTraderContactId();
		if(traderContactId != null){
			TraderContact traderContact = traderCustomerService.getTraderContactById(traderContactId);
			bussinessChance.setCheckMobile(traderContact.getMobile());
			bussinessChance.setCheckTraderContactName(traderContact.getName());
			bussinessChance.setCheckTraderContactTelephone(traderContact.getTelephone());
			if (StringUtils.isBlank(optType) || !"edit".equalsIgnoreCase(optType)){
				//销售新增商机
				bussinessChance.setTraderContactName(traderContact.getName());
				bussinessChance.setMobile(traderContact.getMobile());
				bussinessChance.setTelephone(traderContact.getTelephone());
			} else {
				//编辑自主询价、总机分配的商机，不更新商机基础数据
				bussinessChance.setTraderContactName(null);
				bussinessChance.setMobile(null);
				bussinessChance.setTelephone(null);
				bussinessChance.setTraderName(null);
				bussinessChance.setAreas(null);
				bussinessChance.setAreaId(null);
			}
		}
	}

	/**
	 * 获取客户是否有未处理报价中商机
	 * @param traderId
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/checkTraderHasHistoryBussiness")
	public ResultInfo checkTraderHasHistoryBussiness(Integer traderId){
		ResultInfo resultInfo = new ResultInfo();
		List<BussinessChanceVo> list =  bussinessChanceService.getTraderHasHistoryBussiness(traderId);
		if(CollectionUtils.isEmpty(list)){
			resultInfo.setCode(1);
			resultInfo.setMessage("当前客户没有未处理和报价中商机");
			return resultInfo;
		}
		return resultInfo;
	}

	/**
	 * 客户历史未完成商机列表
	 * @param request
	 * @param pageNo
	 * @param pageSize
	 * @param bussinessChanceVo
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/traderHistoryBussinessIndex")
	public ModelAndView traderHistoryBussinessIndex(HttpServletRequest request,
													@RequestParam(required = false, defaultValue = "1") Integer pageNo,
										   @RequestParam(required = false, defaultValue = "10") Integer pageSize,BussinessChanceVo bussinessChanceVo){
		ModelAndView mv = new ModelAndView();
		Page page = getPageTag(request, pageNo, pageSize);
		List<BussinessChanceVo> list = bussinessChanceService.getTraderHistoryListPage(bussinessChanceVo,page);
		// 商机类型
		List<SysOptionDefinition> typeList = getSysOptionDefinitionList(SysOptionConstant.ID_390);
		mv.addObject("typeList", typeList);
		//商机来源
		List<SysOptionDefinition> sourceList = getSysOptionDefinitionList(SysOptionConstant.ID_365);
		mv.addObject("sourceList", sourceList);
		mv.addObject("page",page);
		mv.addObject("bussinessChanceList",list);
		mv.addObject("bussinessChanceVo",bussinessChanceVo);
		mv.setViewName("order/bussinesschance/traderhistory_index");
		return mv;
	}

	/**
	 * 获取关联商机和报价单信息
	 * @param bussinessChanceId
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/getBussinessChanceAndQuoteInfo")
	public ResultInfo getBussinessChanceAndQuoteInfo(Integer bussinessChanceId){
		return bussinessChanceService.getBussinessChanceAndQuoteInfo(bussinessChanceId);
	}
    /**
     * @describe 选择需要添加的商品产品分类
     * @param request
     * @param baseCategoryVo
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2020/6/12 16:26:03
     */
    @ResponseBody
    @RequestMapping("/addBussinessChanceProduct")
    public ModelAndView addBussinessChanceProduct(HttpServletRequest request, BaseCategoryVo baseCategoryVo,
                                                  @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                  @RequestParam(required = false, defaultValue = "10") Integer pageSize){
        ModelAndView modelAndView = new ModelAndView("order/bussinesschance/add_bussinessChance_product");
        Page page = getPageTag(request, pageNo, pageSize);
        List<BaseCategoryVo> baseCategoryVos = null;
        ArrayList<BaseCategoryVo> baseCategoryVoResult = null;
        try {
            // 未删除状态
            baseCategoryVo.setIsDeleted(CommonConstants.IS_DELETE_0);
            // 获取一级分类列表
            baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_1);
            List<BaseCategoryVo> firstCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,null);
            List<BaseCategoryVo> secondCategoryList = null;
            List<BaseCategoryVo> thirdCategoryList = null;
            List<CategoryAttrValueMappingVo> categoryAttrValueMappingVoList = null;
            if (CollectionUtils.isNotEmpty(firstCategoryList)){
                // 获取二级分类列表
                baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_2);
                secondCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,null);
                if (CollectionUtils.isNotEmpty(secondCategoryList)){
                    // 获取三级分类列表
                    baseCategoryVo.setBaseCategoryLevel(CommonConstants.CATEGORY_LEVEL_3);
                    thirdCategoryList = baseCategoryService.getCategoryListPage(baseCategoryVo,null);
                    if (CollectionUtils.isEmpty(thirdCategoryList) || thirdCategoryList.get(0) == null ||
                            (thirdCategoryList != null && thirdCategoryList.size() == 1 && thirdCategoryList.get(0) != null
                                    && thirdCategoryList.get(0).getBaseCategoryId() == null)) {
                        thirdCategoryList = null;
                    }else{
                        // 获取三级分类下引用的属性关联列表
                        categoryAttrValueMappingVoList =  baseCategoryService.getCategoryAttrValueMappingVoList(thirdCategoryList);
                    }
                }
            }
            baseCategoryVos = doBaseCategoryLevel(firstCategoryList, secondCategoryList, thirdCategoryList, categoryAttrValueMappingVoList);
            //处理分页信息
            baseCategoryVoResult = getBaseCategoryVoRelust(pageNo, pageSize, page, baseCategoryVos);
        } catch (Exception e) {
            logger.error("addBussinessChanceProduct", e);
        }
        modelAndView.addObject("baseCategoryVo",baseCategoryVo);
        modelAndView.addObject("baseCategoryVos",baseCategoryVoResult);
        modelAndView.addObject("page",page);
        return modelAndView;
    }

    private ArrayList<BaseCategoryVo> getBaseCategoryVoRelust(@RequestParam(required = false, defaultValue = "1") Integer pageNo, @RequestParam(required = false, defaultValue = "10") Integer pageSize, Page page, List<BaseCategoryVo> baseCategoryVos) {
        ArrayList<BaseCategoryVo> baseCategoryVoResult;
        baseCategoryVoResult = new ArrayList<>();
        Integer beginIndex = (pageNo - 1) * pageSize;
        Integer endIndex = pageNo * pageSize -1;
        Integer index = 0;
        if (CollectionUtils.isNotEmpty(baseCategoryVos)){
            for (BaseCategoryVo firstCategory : baseCategoryVos) {
                if (CollectionUtils.isNotEmpty(firstCategory.getSecondCategoryList())){
                    for (BaseCategoryVo secondCategory : firstCategory.getSecondCategoryList()) {
                        if (CollectionUtils.isNotEmpty(secondCategory.getThirdCategoryList())){
                            for (BaseCategoryVo thirdCategory : secondCategory.getThirdCategoryList()) {
                                if (index >= beginIndex && index <= endIndex){
                                    BaseCategoryVo categoryVo = new BaseCategoryVo();
                                    categoryVo.setBaseCategoryName(firstCategory.getBaseCategoryName() + "/" + secondCategory.getBaseCategoryName()+ "/" + thirdCategory.getBaseCategoryName());
                                    categoryVo.setTotalProductNum(thirdCategory.getCoreProductNum() + thirdCategory.getTemporaryProductNum() + thirdCategory.getOtherProductNum());
                                    baseCategoryVoResult.add(categoryVo);
                                }
                                index ++ ;
                            }
                        } else {
                            if (index >= beginIndex && index <= endIndex) {
                                BaseCategoryVo categoryVo = new BaseCategoryVo();
                                categoryVo.setBaseCategoryName(firstCategory.getBaseCategoryName() + "/" + secondCategory.getBaseCategoryName());
                                setTotalProductNum(categoryVo);
                                baseCategoryVoResult.add(categoryVo);
                            }
                            index++;
                        }
                    }
                } else {
                    if (index >= beginIndex && index <= endIndex){
                        BaseCategoryVo categoryVo = new BaseCategoryVo();
                        categoryVo.setBaseCategoryName(firstCategory.getBaseCategoryName());
                        setTotalProductNum(categoryVo);
                        baseCategoryVoResult.add(categoryVo);
                    }
                    index ++;
                }
            }
        }
        page.setTotalRecord(index);
        page.setTotalPage((((double) index / (double) pageSize) > (index / pageSize) ? index / pageSize + 1 : index / pageSize));
        return baseCategoryVoResult;
    }

    private void setTotalProductNum(BaseCategoryVo categoryVo) {
        Integer coreProductNum = categoryVo.getCoreProductNum() == null ? 0 : categoryVo.getCoreProductNum();
        Integer temporaryProductNum = categoryVo.getTemporaryProductNum() == null ? 0 : categoryVo.getTemporaryProductNum();
        Integer otherProductNum = categoryVo.getOtherProductNum() == null ? 0 : categoryVo.getOtherProductNum();
        categoryVo.setTotalProductNum(coreProductNum + temporaryProductNum + otherProductNum);
    }


    /**
     * @describe 设置分类的登记归属
     * @param firstCategoryList
     * @param secondCategoryList
     * @param thirdCategoryList
     * @param categoryAttrValueMappingVoList
     * @return
     * <AUTHOR>
     * @date 2020/6/12 17:37:28
     */
    private List<BaseCategoryVo> doBaseCategoryLevel(List<BaseCategoryVo> firstCategoryList,
                                                     List<BaseCategoryVo> secondCategoryList,List<BaseCategoryVo> thirdCategoryList,
                                                     List<CategoryAttrValueMappingVo> categoryAttrValueMappingVoList){
        try{
            //先将三级分类关联的属性列表关联到三级分类中
            if (CollectionUtils.isNotEmpty(thirdCategoryList)){
                for (BaseCategoryVo thirdCategory : thirdCategoryList) {
                    if (thirdCategory != null){
                        List<CategoryAttrValueMappingVo> attributeVoList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(categoryAttrValueMappingVoList)){
                            for (CategoryAttrValueMappingVo categoryAttrValueMappingVo : categoryAttrValueMappingVoList) {
                                if (categoryAttrValueMappingVoList != null){
                                    if (thirdCategory.getBaseCategoryId().equals(categoryAttrValueMappingVo.getBaseCategoryId())){
                                        attributeVoList.add(categoryAttrValueMappingVo);
                                    }
                                }
                            }
                        }
                        thirdCategory.setCategoryAttrValueMappingVoList(attributeVoList);
                    }
                }
            }

            //先将三级分类关联到二级分类中
            setBaseCategoryInfo(secondCategoryList, thirdCategoryList,2);
            //再将二级分类关联到一级分类中
            setBaseCategoryInfo(firstCategoryList,secondCategoryList,1);

            return firstCategoryList;
        }catch (Exception e){
            logger.error("处理分类级别关联",e);
            return null;
        }
    }

    /**
     * @describe 设置父等级的属性信息
     * @param parentCategoryList
     * @param categoryList
     * @param level
     * <AUTHOR>
     * @date 2020/6/12 17:38:22
     */
    private void setBaseCategoryInfo(List<BaseCategoryVo> parentCategoryList, List<BaseCategoryVo> categoryList,Integer level) {
        if (CollectionUtils.isNotEmpty(parentCategoryList)) {
            for (BaseCategoryVo parentCategory : parentCategoryList) {
                if (parentCategory != null){
                    List<BaseCategoryVo> categoryVoList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(categoryList)) {
                        categoryList.stream().forEach(category -> {
                            if (category !=  null){
                                if (parentCategory.getBaseCategoryId().equals(category.getParentId())) {
                                    categoryVoList.add(category);
                                    Integer coreProductNum = category.getCoreProductNum() == null ? 0 : category.getCoreProductNum();
                                    Integer temporaryProductNum = category.getTemporaryProductNum() == null ? 0 : category.getTemporaryProductNum();
                                    Integer otherProductNum = category.getOtherProductNum() == null ? 0 : category.getOtherProductNum();
                                    parentCategory.setCoreProductNum(category.getCoreProductNum() == null ? coreProductNum : category.getCoreProductNum() + coreProductNum);
                                    parentCategory.setTemporaryProductNum(category.getTemporaryProductNum() == null ? temporaryProductNum : category.getTemporaryProductNum() + temporaryProductNum);
                                    parentCategory.setOtherProductNum(category.getOtherProductNum() == null ? otherProductNum : category.getOtherProductNum() + otherProductNum);
                                }
                            }
                        });
                    }
                    switch (level){
                        case 1:{
                            parentCategory.setSecondCategoryList(categoryVoList);
                            break;
                        }
                        case 2: {
                            parentCategory.setThirdCategoryList(categoryVoList);
                            break;
                        }
                        default:break;
                    }
                }
            }
        }
    }

    @ResponseBody
    @RequestMapping("chance/merge/old/user")
    public ResultInfo<User> getOldChanceOwnUser(String mobile){
        return bussinessChanceService.getOldChanceOwner(mobile);
    }

    /**
     * @describe  手动添加产品分类页面
     * @return
     * <AUTHOR>
     * @date 2020/6/15 15:41:22
     */
    @ResponseBody
    @RequestMapping("editBussinessChanceProduct")
    public ModelAndView editBussinessChanceProduct(){
        return new ModelAndView("order/bussinesschance/edit_bussinessChance_product");
    }


	// add by Randy.Xu 2020/11/25 15:58 .Desc: . begin
	/**
	 * 预计本周成单接口
	 * @jira: .
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2020/11/25 18:07.
	 * @author: Randy.Xu.
	 * @param request
	 * @param bussinessChanceVo
	 * @param traderCustomer
	 * @param pageNo
	 * @param pageSize
	 * @param session
	 * @return: org.springframework.web.servlet.ModelAndView.
	 * @throws:  .
	 */
	@ResponseBody
	@RequestMapping(value = "expectChance")
	public ModelAndView expectChance(HttpServletRequest request, BussinessChanceVo bussinessChanceVo,
								  TraderCustomer traderCustomer, @RequestParam(required = false, defaultValue = "1") Integer pageNo,
								  @RequestParam(required = false) Integer pageSize, HttpSession session) {
		User user = getSessionUser(session);


		bussinessChanceVo.setCompanyId(user.getCompanyId());
		ModelAndView mv = new ModelAndView();

		Page page = getPageTag(request, pageNo, pageSize);

		//判断是否是B2B的销售
		Boolean saleAndB2BFlagByUserId = userService.getSaleAndB2BFlagByUserId(user);
		int isSaleFlag = 0;
		if(saleAndB2BFlagByUserId){
			isSaleFlag = 1;
		}
		mv.addObject("isSaleFlag",isSaleFlag);

		//如果查询条件是“关闭时间”，则把商机状态设为关闭
		if(null != bussinessChanceVo.getTimeType() && bussinessChanceVo.getTimeType() == 5){
			//把商机状态设为关闭
			bussinessChanceVo.setStatus(4);
		}

		// 商机类型
		List<SysOptionDefinition> typeList = getSysOptionDefinitionList(SysOptionConstant.ID_390);
		mv.addObject("typeList", typeList);
		//商机来源
		List<SysOptionDefinition> sourceList = getSysOptionDefinitionList(SysOptionConstant.ID_365);
		mv.addObject("sourceList", sourceList);
		//询价方式
		List<SysOptionDefinition> communicationList=getSysOptionDefinitionList(SysOptionConstant.ID_376);
		mv.addObject("communicationList",communicationList);
		//咨询入口
		List<SysOptionDefinition> entrancesList = sysOptionDefinitionMapper.getSysOptionDefinitionByParentTitle("咨询入口");
		mv.addObject("entrancesList",entrancesList);

		//功能
		List<SysOptionDefinition> functionsList = sysOptionDefinitionMapper.getSysOptionDefinitionByParentTitle("功能");
		mv.addObject("functionsList",functionsList);


		//商机等级
		List<SysOptionDefinition> bussinessLevelList = getSysOptionDefinitionList(SysOptionConstant.ID_938);
		mv.addObject("bussinessLevelList", bussinessLevelList);
		//成单机率
		List<SysOptionDefinition> orderRateList = getSysOptionDefinitionList(SysOptionConstant.ID_951);
		mv.addObject("orderRateList", orderRateList);
		//商机阶段
		List<SysOptionDefinition> bussinessStageList = getSysOptionDefinitionList(SysOptionConstant.ID_943);
		mv.addObject("bussinessStageList", bussinessStageList);
		// 地区
		List<Region> provinceList = regionService.getRegionByParentId(1);
		mv.addObject("provinceList", provinceList);

		if (null != bussinessChanceVo.getProvince() && bussinessChanceVo.getProvince() > 0) {
			List<Region> cityList = regionService.getRegionByParentId(bussinessChanceVo.getProvince());
			mv.addObject("cityList", cityList);
		}

		if (null != bussinessChanceVo.getCity() && bussinessChanceVo.getCity() > 0) {
			List<Region> zoneList = regionService.getRegionByParentId(bussinessChanceVo.getCity());
			mv.addObject("zoneList", zoneList);
		}


		//		 查询当前用户下所有职位类型为310的员工
		List<User> userList = new ArrayList<>();
		if(traderCustomer != null && ObjectUtils.notEmpty(traderCustomer.getTraderId())){
			User us = userService.getUserByTraderId(traderCustomer.getTraderId(), 1);
			if(us != null){
				userList.add(us);
			}
		}else{
			List<Integer> positionType = new ArrayList<>();
			positionType.add(SysOptionConstant.ID_310);//销售
			userList = userService.getMyUserList(user, positionType, false);
			mv.addObject("userList", userList);
		}

		// 查询用户集合
		List<Integer> userIds = new ArrayList<>();

		//获取搜索类型以及搜索的id
		Integer searchType = bussinessChanceVo.getSearchType();
		Integer searchId = bussinessChanceVo.getSearchId();


		if(searchId !=null) {
			//页面回显
			bussinessChanceVo.setUserId(searchId);
			//搜索指定下属
			if (searchType.equals(0)) {
				userIds.add(searchId);
			} else if (searchType.equals(1)) {
				//查询区域
				Map<DwhErpSubDeptDto, List<DwhErpSubDeptDto>> userOrganization = dwhThreadLocalService.getUserOrganization(user.getUserId());
				DwhErpUserDto userInfo = dwhThreadLocalService.getUserInfo(user.getUserId());
				if (DwhConstants.ZG.equals(userInfo.getPositionName())){
					//销售主管
					if(searchId != -1){
						userIds.add(searchId);
					}else{
                        List<DwhErpSubDeptDto> deptDtos = userOrganization.entrySet().stream()
                                .flatMap(e -> e.getValue().stream()).collect(Collectors.toList());

                        List<DwhErpUserDto> users = deptDtos.stream()
                                .flatMap(s -> s.getSubUsers().stream()).collect(Collectors.toList());

                        List<Integer> ids = users.stream().map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());
						bussinessChanceVo.setUserId(null);
						userIds.addAll(ids);
					}
				}
				else if (DwhConstants.JL.equals(userInfo.getPositionName())) {

					//经理进行搜索
					List<Integer> ids = userOrganization.entrySet().stream().flatMap(x -> x.getValue().stream())
							.filter(e -> e.getDepartId().equals(searchId)).flatMap(o -> o.getSubUsers().stream())
							.map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());
					userIds.addAll(ids);
				} else if (DwhConstants.XSZJ.equals(userInfo.getPositionName())) {
					List<Integer> ids = userOrganization.entrySet().stream().filter(x -> x.getKey().getDepartId().equals(searchId)).flatMap(x -> x.getValue().stream().flatMap(e -> e.getSubUsers().stream())).map(DwmNjUserOrgDfDo::getUserId).collect(Collectors.toList());
					userIds.addAll(ids);
				}
			}
			bussinessChanceVo.setUserIds(userIds);
		}else{
			//搜索总计
			if (null == bussinessChanceVo.getUserId() || bussinessChanceVo.getUserId() <= 0) {

				for (User u : userList) {
					userIds.add(u.getUserId());
				}

				bussinessChanceVo.setUserIds(userIds);
			} else {
				userIds.add(bussinessChanceVo.getUserId());
				if (userIds.size() > 0) {
					bussinessChanceVo.setUserIds(userIds);
				} else {//名下无用户
					userIds.add(-1);
					bussinessChanceVo.setUserIds(userIds);
				}
			}
		}




		// 预计成单时间处理
		if (null != bussinessChanceVo.getCdstarttime() && bussinessChanceVo.getCdstarttime() != "") {
			bussinessChanceVo.setCdstarttimeLong(DateUtil.convertLong(bussinessChanceVo.getCdstarttime(), "yyyy-MM-dd"));
		}
		if (null != bussinessChanceVo.getCdendtime() && bussinessChanceVo.getCdendtime() != "") {
			bussinessChanceVo.setCdendtimeLong(DateUtil.convertLong(bussinessChanceVo.getCdendtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}
		//默认前3个月
		// 查询商机开始时间的前3个月
		if(null == bussinessChanceVo.getTimeType()) {
			//默认查询创建时间
			bussinessChanceVo.setTimeType(1);
		}
		if(StringUtils.isBlank(bussinessChanceVo.getStarttime())){
			bussinessChanceVo.setStarttime("2019-11-1");
		}
		// 时间处理
		if (null != bussinessChanceVo.getStarttime() && bussinessChanceVo.getStarttime() != "") {
			bussinessChanceVo.setStarttimeLong(DateUtil.convertLong(bussinessChanceVo.getStarttime(), "yyyy-MM-dd"));
		}
		if (null != bussinessChanceVo.getEndtime() && bussinessChanceVo.getEndtime() != "") {
			bussinessChanceVo.setEndtimeLong(DateUtil.convertLong(bussinessChanceVo.getEndtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}

		// 地区处理
		if (null != bussinessChanceVo.getZone() && bussinessChanceVo.getZone() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getZone());
		} else if (null != bussinessChanceVo.getCity() && bussinessChanceVo.getCity() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getCity());
		} else if (null != bussinessChanceVo.getProvince() && bussinessChanceVo.getProvince() > 0) {
			bussinessChanceVo.setAreaId(bussinessChanceVo.getProvince());
		}
		bussinessChanceVo.setCurrUserId(user.getUserId());
		List<BussinessChanceVo> bussinessChanceList = null;


		BigDecimal totalAmount = new BigDecimal(0);
//		if(CollectionUtils.isEmpty(bussinessChanceVo.getLevelList())){
			Map<String, Object> map = bussinessChanceService.getSaleBussinessChanceListPage(bussinessChanceVo, page);
			bussinessChanceList = (List<BussinessChanceVo>) map.get("list");
			if(CollectionUtils.isNotEmpty(bussinessChanceList)){
				totalAmount =  bussinessChanceList.get(0).getTotalAmount();
			}
			if(totalAmount == null){
				totalAmount = new BigDecimal(0);
			}
//		}else{
//			for (Integer integer : bussinessChanceVo.getLevelList()) {
//				bussinessChanceVo.setBussinessLevel(integer);
//				Map<String, Object> map = bussinessChanceService.getSaleBussinessChanceListPage(bussinessChanceVo, page);
//				List<BussinessChanceVo> newbussinessChanceList = (List<BussinessChanceVo>) map.get("list");
//				if(CollectionUtils.isNotEmpty(bussinessChanceList)){
//					totalAmount = totalAmount.add(bussinessChanceList.get(0).getTotalAmount()) ;
//					bussinessChanceList.addAll(newbussinessChanceList);
//				}
//				if(totalAmount == null){
//					totalAmount = new BigDecimal(0);
//				}
//
//			}
//		}

//		BigDecimal totalAmount = new BigDecimal(0);
		if(CollectionUtils.isNotEmpty(bussinessChanceList)){
			totalAmount =  bussinessChanceList.get(0).getTotalAmount();
		}
		if(totalAmount == null){
			totalAmount = new BigDecimal(0);
		}
		mv.addObject("totalAmount", totalAmount);
		mv.addObject("bussinessChanceVo", bussinessChanceVo);
		mv.addObject("bussinessChanceList", bussinessChanceList);
		mv.addObject("page", (Page) map.get("page"));

		if (null != traderCustomer && null != traderCustomer.getTraderId() && traderCustomer.getTraderId() > 0) {
			mv.addObject("method", "bussinesschance");
			TraderCustomer customerInfoByTraderCustomer = traderCustomerService.getCustomerInfoByTraderCustomer(traderCustomer);
			mv.addObject("customerInfoByTraderCustomer", customerInfoByTraderCustomer);
		}

		mv.setViewName("order/bussinesschance/sale_index");
		return mv;
	}
	// add by Randy.Xu 2020/11/25 15:58 .Desc: . end

	/**
	 * <b>Description:</b><br>
	 * 跳转编辑备注页面
	 *
	 * @param bussinessEditChance
	 * @return
	 * @throws IOException
	 * @Note <b>Author:</b> wuxu <br>
	 *       <b>Date:</b> 2021年3月4日 上午9:42
	 */
	@ResponseBody
	@RequestMapping(value = "toSalesEditComments")
	public ModelAndView toSalesEditComments(BussinessChance bussinessChance, TraderCustomer traderCustomer) throws IOException {
		ModelAndView mav = new ModelAndView("order/bussinesschance/edit_salesBussinessEditChance");
		String productComments = bussinessChance.getProductComments();
		productComments  = URLDecoder.decode(productComments,"UTF-8");
		bussinessChance.setProductComments(productComments);
		mav.addObject("bussinessChanceVo",bussinessChance);
		mav.addObject("traderCustomer",traderCustomer);
		return mav;
	}

	//保存编辑产品备注(主机)
	@ResponseBody
	@RequestMapping(value = "saveEditCommentsChance")
	@SystemControllerLog(operationType = "edit",desc = "保存编辑销售商机")
	public ResultInfo saveEditCommentsChance(HttpSession session, String time, BussinessChance bussinessChance ,
													 TraderCustomer traderCustomer) {
			User user = getSessionUser(session);
			int num = bussinessChanceService.updateEditComments(bussinessChance);
			if(num ==1){
				return new ResultInfo(0, "操作成功！",bussinessChance);
			}else{
				return new ResultInfo(1, "操作失败！");
			}

	}

	/**
	 * 获取商机询价选择项
	 * @param parentId 父级id
	 * @return
	 */
	public List<SysOptionDefinition> getBussInquiryData(Integer parentId) {
		if (Objects.isNull(parentId)) {
			return Collections.emptyList();
		}

		List<SysOptionDefinition> bussInquiryData = bussinessChanceService.getBussInquiryData(parentId);
		if (CollectionUtils.isNotEmpty(bussInquiryData)) {
			bussInquiryData = bussInquiryData.stream().sorted(Comparator.comparing(SysOptionDefinition::getSort)).collect(Collectors.toList());
		}
		logger.info("查询商机渠道数据：{}", JSON.toJSONString(bussInquiryData));
		return bussInquiryData;
	}

	@RequestMapping(value = "/editNewSourceDic")
	public ModelAndView editNewSourceDic(HttpSession session){
		//获取并组装数据
		ModelAndView mv = new ModelAndView("vue/view/businesschance/editNewSourceDic");
		// 商机类型
		List<SysOptionDefinition> typeList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_390),bncServiceTypeIds);
		List<SysOptionDefinition> inquiryListAll = sysOptionDefinitionMapper.getSysOptionDefinitionByParentsId(typeList.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
		List<SysOptionDefinition> newSourceListAll = sysOptionDefinitionMapper.getSysOptionDefinitionByParentsId(inquiryListAll.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
		List<NewSourceDicDto> newSourceList = bussinessChanceService.getNewSource(newSourceListAll);
		mv.addObject("newSourceList", JSONUtil.toJsonStr(newSourceList));
		return mv;
	}

	/**
	 * 新增/编辑	分类
	 * @param sysOptionDefinition
	 * @return
	 */
	@RequestMapping("/addNewSource")
	@ResponseBody
	@ExcludeAuthorization
	public R<?> addNewSource(@RequestBody SysOptionDefinition sysOptionDefinition){
		String redisKey = dbType + KEY_PREFIX_DATA_DICTIONARY_LIST;
		//根据主键判断是新增还是修改
		if(null == sysOptionDefinition.getSysOptionDefinitionId()){
			List<SysOptionDefinition> typeList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_390),bncServiceTypeIds);
			List<SysOptionDefinition> inquiryListAll = sysOptionDefinitionMapper.getSysOptionDefinitionByParentsId(typeList.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
			List<SysOptionDefinition> newSourceAll = sysOptionDefinitionMapper.getSysOptionDefinitionByParentsId(inquiryListAll.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
			Optional<SysOptionDefinition> optional = newSourceAll.stream().filter(i -> StringUtils.equals(i.getTitle(), sysOptionDefinition.getTitle())).findAny();
			if (optional.isPresent()){
				return R.error("渠道类型不能重复");
			}
			sysOptionDefinition.setParentId(CollUtil.getFirst(inquiryListAll).getSysOptionDefinitionId());
			sysOptionDefinition.setStatus(1);
			sysOptionDefinitionMapper.insertSelective(sysOptionDefinition);
			RedisUtil.KeyOps.delete(redisKey+sysOptionDefinition.getParentId());
			logger.info("删除渠道名称缓存,redisKey:{},parentId:{}",redisKey,sysOptionDefinition.getParentId());
		}else {
			SysOptionDefinition oldDto = sysOptionDefinitionMapper.selectByPrimaryKey(sysOptionDefinition.getSysOptionDefinitionId());
			List<SysOptionDefinition> typeList = getSysOptionIncluded(getSysOptionDefinitionList(SysOptionConstant.ID_390),bncServiceTypeIds);
			List<SysOptionDefinition> inquiryListAll = sysOptionDefinitionMapper.getSysOptionDefinitionByParentsId(typeList.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
			List<SysOptionDefinition> newSourceAll = sysOptionDefinitionMapper.getSysOptionDefinitionByParentsId(inquiryListAll.stream().map(SysOptionDefinition::getSysOptionDefinitionId).collect(Collectors.toList()));
			Optional<SysOptionDefinition> optional = newSourceAll.stream().filter(i -> StringUtils.equals(i.getTitle(), sysOptionDefinition.getTitle()) && !i.getSysOptionDefinitionId().equals(sysOptionDefinition.getSysOptionDefinitionId())).findAny();
			if (optional.isPresent() && !optional.get().getSysOptionDefinitionId().equals(sysOptionDefinition.getSysOptionDefinitionId())){
				return R.error("渠道类型不能重复");
			}
			sysOptionDefinitionMapper.updateByPrimaryKeySelective(sysOptionDefinition);
			RedisUtil.KeyOps.delete(redisKey+oldDto.getParentId());
			logger.info("删除渠道类型缓存,redisKey:{},parentId:{}",redisKey,oldDto.getParentId());
		}
		return R.success();
	}

	/**
	 * 新增/编辑	渠道
	 * @param sysOptionDefinition
	 * @return
	 */
	@RequestMapping("/addChild")
	@ResponseBody
	@ExcludeAuthorization
	public R<?> addChild(@RequestBody SysOptionDefinition sysOptionDefinition){
		String redisKey = dbType + KEY_PREFIX_DATA_DICTIONARY_LIST;
		//根据主键判断是新增还是修改
		if(null == sysOptionDefinition.getSysOptionDefinitionId()){
			List<SysOptionDefinition> oldSys = sysOptionDefinitionMapper.getSysOptionDefinitionByTitleAndParentId(sysOptionDefinition.getTitle(),sysOptionDefinition.getParentId());
			if (CollUtil.isNotEmpty(oldSys)){
				return R.error("同一分类下渠道名称不能重复");
			}
			sysOptionDefinition.setStatus(1);
			sysOptionDefinitionMapper.insertSelective(sysOptionDefinition);
			RedisUtil.KeyOps.delete(redisKey+sysOptionDefinition.getParentId());
			logger.info("删除渠道名称缓存,redisKey:{},parentId:{}",redisKey,sysOptionDefinition.getParentId());
		}else {
			List<SysOptionDefinition> oldSys = sysOptionDefinitionMapper.getSysOptionDefinitionByTitleAndParentId(sysOptionDefinition.getTitle(),sysOptionDefinition.getParentId());
			Optional<SysOptionDefinition> optional = oldSys.stream().filter(i -> StringUtils.equals(i.getTitle(), sysOptionDefinition.getTitle()) && !i.getSysOptionDefinitionId().equals(sysOptionDefinition.getSysOptionDefinitionId())).findAny();
			if (optional.isPresent() && !optional.get().getSysOptionDefinitionId().equals(sysOptionDefinition.getSysOptionDefinitionId())){
				return R.error("同一分类下渠道名称不能重复");
			}
			sysOptionDefinitionMapper.updateByPrimaryKeySelective(sysOptionDefinition);
			RedisUtil.KeyOps.delete(redisKey+sysOptionDefinition.getParentId());
			logger.info("删除渠道名称缓存,redisKey:{},parentId:{}",redisKey,sysOptionDefinition.getParentId());
		}
		return R.success();
	}


}
