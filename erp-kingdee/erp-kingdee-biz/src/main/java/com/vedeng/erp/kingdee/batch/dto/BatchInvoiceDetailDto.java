package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * 发票详情
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchInvoiceDetailDto {
    private Integer invoiceDetailId;

    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 订单详情ID
     */
    private Integer detailgoodsId;

    /**
     * 开票单价
     */
    private BigDecimal price;

    /**
     * 开票数量
     */
    private BigDecimal num;

    /**
     * 开票总额
     */
    private BigDecimal totalAmount;

    /**
     * 修改后的商品名称
     */
    private String changedGoodsName;

    /**
     * 金蝶费用类别编号
     */
    private String unitKingDeeNo;

    /**
     * 商品id
     */
    private Integer goodsId;
    /**
     * 订货号
     */
    private String sku;

    /**
     * 是否是虚拟商品
     */
    private Integer isVirtureSku;

    /**
     * @组合对象@ 判断是否再21-22 的采购费用内
     */
    private Boolean hasExpense = Boolean.FALSE;

    private Integer afterSalesId;
}