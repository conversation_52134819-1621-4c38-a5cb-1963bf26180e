package com.newtask.finance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.system.SystemUtil;
import com.itextpdf.text.pdf.BaseFont;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.service.AcceptanceBillService;
import com.vedeng.erp.settlement.service.BankBillService;
import com.vedeng.erp.settlement.service.CapitalBillService;
import com.vedeng.infrastructure.bank.api.domain.DraftDetailReqBody;
import com.vedeng.infrastructure.bank.api.domain.DraftDetailResponse;
import com.vedeng.infrastructure.bank.api.domain.DraftHoldingBillsQryBody;
import com.vedeng.infrastructure.bank.api.domain.DraftHoldingBillsQryResponse;
import com.vedeng.infrastructure.bank.api.service.AcceptanceBillApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 更新承兑汇票
 * @date 2024/9/9 13:20
 */
@JobHandler(value = "AcceptanceBillUpdateTask")
@Component
@Slf4j
public class AcceptanceBillUpdateTask extends AbstractJobHandler {

    @Autowired
    private AcceptanceBillService acceptanceBillService;
    @Autowired
    private AcceptanceBillApiService acceptanceBillApiService;

    @Value("${custAccount}")
    private String custAccount;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("开始执行AcceptanceBillUpdateTask");

        List<AcceptanceBillEntity> acceptanceBillNonTerminatedState = acceptanceBillService.findAcceptanceBillNonTerminatedState();
        if (CollUtil.isNotEmpty(acceptanceBillNonTerminatedState)) {
            acceptanceBillNonTerminatedState.forEach(bill -> {

                DraftDetailReqBody detailReqBody = new DraftDetailReqBody();
                detailReqBody.setTrnId(IdUtil.simpleUUID());
                detailReqBody.setCustAccount(custAccount);
                detailReqBody.setBillNo(bill.getBillNumber());
                detailReqBody.setBillRangeStart(bill.getSubBillIntervalStart());
                detailReqBody.setBillRangeEnd(bill.getSubBillIntervalEnd());
                DraftDetailResponse draftDetailResponse = acceptanceBillApiService.b2eNbsDraftDetail(detailReqBody);

                if (draftDetailResponse.isSuccess()) {
                    // 生成承兑汇票
                    DraftDetailResponse.DraftDetailResBody xDataBody = draftDetailResponse.getXDataBody();
                    DraftDetailResponse.BillInfo billInfo = xDataBody.getBillInfo();
                    bill.setBillStatus(mapBillStatus(billInfo.getBillStatus()));
                    acceptanceBillService.updateByPrimaryKeySelective(bill);
                }
            });

        }
        log.info("开始执行贴现状态检查任务-refreshDraftDiscount");
        this.acceptanceBillService.refreshDraftDiscount();

        log.info("开始执行自动签收任务-autoSignup");
        this.acceptanceBillService.autoSignup();

        log.info("结束执行AcceptanceBillUpdateTask");
        return ReturnT.SUCCESS;
    }


    private Integer mapBillStatus(String billStatus) {
        switch (billStatus) {
            // 已出票
            case "CS01":
                return 1;
            // 已承兑
            case "CS02":
                return 2;
            // 已收票
            case "CS03":
                return 3;
            // 已到期
            case "CS04":
                return 4;
            // 已终止
            case "CS05":
                return 5;
            // 已结清
            case "CS06":
                return 6;
            default:
                return null;
        }
    }

}
