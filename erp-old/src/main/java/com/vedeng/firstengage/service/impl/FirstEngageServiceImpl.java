package com.vedeng.firstengage.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.common.util.*;
import com.vedeng.docSync.enums.DocSyncEventEnum;
import com.vedeng.docSync.service.SyncGoodsService;

import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.goods.LogTypeEnum;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.erp.trader.dto.RegistrationProductionModeDto;
import com.vedeng.erp.trader.service.RegistrationProductionApiService;
import com.vedeng.firstengage.dao.*;
import com.vedeng.firstengage.model.*;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.goods.dao.FirstEngageGenerateMapper;
import com.vedeng.goods.dao.LogCheckGenerateMapper;
import com.vedeng.goods.enums.GoodsStorageConditionOthersEnum;
import com.vedeng.goods.model.*;
import com.vedeng.goods.service.ManufacturerService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.order.model.vo.FirstEngageInfoDto;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.StandardCategoryMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.ThreeCertificatesStamp;
import com.vedeng.system.service.UserService;
import com.vedeng.ty.api.req.ReqProductDto;
import com.vedeng.ty.api.res.ResProductInfoDto;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsQLA;
import com.wms.model.po.WmsSkuReg;
import com.wms.service.util.GlobalThreadPool;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 首营信息
 * <p>Title: FirstEngageServiceImpl</p>
 * <p>Description: </p>
 * <AUTHOR>
 * @date 2019年3月20日
 */
@Service("firstEngageService")
public class FirstEngageServiceImpl implements FirstEngageService{
	public static Logger logger = LoggerFactory.getLogger(FirstEngageServiceImpl.class);

	@Value("${file_url}")
	protected String domain;

	@Value("${ty_ali_url}")
	protected String tyAliUrl;
	@Value("${redis_dbtype}")
	protected String dbType;
	@Autowired
	private FirstEngageMapper firstEngageMapper;

	@Autowired
	@Qualifier("registrationNumberMapper")
	private RegistrationNumberMapper registrationNumberMapper;

	@Autowired
	@Qualifier("productCompanyMapper")
	private ProductCompanyMapper productCompanyMapper;


	@Autowired
	@Qualifier("firstEngageStorageConditionMapper")
	private FirstEngageStorageConditionMapper firstEngageStorageConditionMapper;

	@Autowired
	@Qualifier("standardCategoryMapper")
	private StandardCategoryMapper standardCategoryMapper;

	@Autowired
	@Qualifier("sysOptionDefinitionMapper")
	private SysOptionDefinitionMapper sysOptionDefinitionMapper;

	@Autowired
	@Qualifier("firstEngageSearchInfoMapper")
	private FirstEngageSearchInfoMapper firstEngageSearchInfoMapper;

	@Autowired
	private AttachmentMapper attachmentMapper;


	@Value("${api_http}")
	protected String api_http;

	@Resource
	LogCheckGenerateMapper logCheckGenerateMapper;

	@Resource
	FirstEngageGenerateMapper firstEngageGenerateMapper;

	@Autowired
	private GoodsRegisrationExpiryDateMapper goodsRegisrationExpiryDateMapper;

	@Autowired
	private RegistrationAttachmentHistoryMapper registrationAttachmentHistoryMapper;

	@Resource
	private ManufacturerService manufacturerService;

	@Value("${registration_certificate}")
	protected String registrationCertificateStr;

	@Autowired
	private WMSInterfaceFactory wmsInterfaceFactory;

	@Resource
	private SyncGoodsService syncGoodsService;

	@Autowired
	private RoleMapper roleMapper;

	@Autowired
	@Qualifier("vgoodsServiceImpl")
	private VgoodsService vgoodsService;

	@Autowired
	private UserService userService;

	/**
	 * 批量插入数量
	 */
	private static final int INSERT_NUM = 800;


	/**
	 * 获取商品首营列表
	 */
	@Override
	public Map<String, Object> getFirstEngageInfoListPage(Map<String, Object> paramMap, Page page, FirstEngage firstEngage1) {

//		updateRegister();

		// 如果关键词搜索不为空，保存数据库
		if(null != paramMap.get("keyWords") && EmptyUtils.isNotBlank(paramMap.get("keyWords").toString())){
			Map<String, Object> delMap = new HashMap<>();
			delMap.put("keyWords", paramMap.get("keyWords").toString());
			delMap.put("userId", Integer.valueOf(paramMap.get("userId").toString()));
			// 先删除已经存在的关键词
			firstEngageSearchInfoMapper.deleteByContent(delMap);

			// 再将搜索内容添加搜索记录中
			firstEngageSearchInfoMapper.insertSelectiveInfo(delMap);
		}
		// 查询各个状态对应的数量
		Map<String, Integer> countMap = firstEngageMapper.getStatusCountByParam(paramMap);

		Map<String, Object> resultMap = new HashMap<>();
		// 全部
		resultMap.put("total", 0);
		// 待审核
		resultMap.put("one", 0);
		// 审核通过
		resultMap.put("two", 0);
		// 审核不通过
		resultMap.put("three", 0);
		//待提交审核
		resultMap.put("waitToPre",0);
		if(null != countMap){
			// 全部
			resultMap.put("total", countMap.get("total"));
			// 待审核
			resultMap.put("one", countMap.get("one"));
			// 审核通过
			resultMap.put("two", countMap.get("two"));
			// 审核不通过
			resultMap.put("three", countMap.get("three"));
			resultMap.put("waitToPre",countMap.get("five"));
		}


		// 6月 时间戳
		Long firstEngageDateStart = DateUtil.convertLong(DateUtil.getDayOfMonth(0), DateUtil.DATE_FORMAT);
		// 6个月前
		Long firstEngageDateEnd = DateUtil.convertLong(DateUtil.getDayOfMonth(6), DateUtil.DATE_FORMAT);
		paramMap.put("overDateStart", firstEngageDateStart);
		paramMap.put("overDateEnd", firstEngageDateEnd);
		paramMap.put("pageNo",page.getPageNo());
		paramMap.put("pageSize",page.getPageSize());

		// 查询即将过期注册证数量
//		resultMap.put("overDateCount", firstEngageMapper.getOverDateCount(paramMap));
		com.github.pagehelper.Page page1=PageHelper.startPage(page.getPageNo(),page.getPageSize());
		// 首营列表
		List<FirstEngage> firstEngageList = firstEngageMapper.getFirstEngageInfoList(paramMap);

//		List<FirstEngage> list = firstEngageMapper.getFirstEngageInfoList(paramMap);
//		page1.setTotal(list.size());

		// 为首营列表添加选中归属人信息，未处理问题的数量信息
		for (FirstEngage firstEngage : firstEngageList) {
			if(firstEngage.getCountOfUndeal() == null || firstEngage.getCountOfUndeal() == 0){
				firstEngage.setUnDealStatus(1);
			}else {
				firstEngage.setUnDealStatus(0);
			}
		}
		//获取总数
		List<GoodsCount> maps = firstEngageMapper.getCountAll();
		Integer total = 0;
		Integer one = 0;
		Integer two = 0;
		Integer three = 0;
		for (GoodsCount s : maps){
			Integer status = s.getStatus();
			Integer count = s.getCount();
			if(status == 1)
				one = count;
			if(status == 3)
				two = count;
			if(status == 2)
				three = count;
		}
		total = one + two + three;

		resultMap.put("firstEngageList", firstEngageList);
		if(CollectionUtils.isNotEmpty(firstEngageList)){
			// 总条数
			page.setTotalRecord(total);
			// 判断审核状态
			if(null == paramMap.get("status")){
				resultMap.put("total", total);
			}
			// 待审核
			if(null != paramMap.get("status") && "1".equals(paramMap.get("status").toString())){
				resultMap.put("one", one);
			}
			// 审核通过
			if(null != paramMap.get("status") && "3".equals(paramMap.get("status").toString())){
				resultMap.put("two", two);
			}
			// 审核不通过
			if(null != paramMap.get("status") && "2".equals(paramMap.get("status").toString())){
				resultMap.put("three", three);
			}

			// 分页数据
//			List<FirstEngage> firstEngageList = firstEngageListT.subList(
//					(page.getPageNo() - 1) * page.getPageSize(), page.getTotalRecord() -
//							(page.getPageNo()) * page.getPageSize() >= 0 ? (page.getPageNo()) * page.getPageSize() : page.getTotalRecord());
			// 根据新国标分类id查询新国标分类
			List<Map<String, Object>> mapList = standardCategoryMapper.getStandardCategoryStrMap(firstEngageList);

			int size = firstEngageList.size();
			for (int i = 0; i < size; i++) {
				FirstEngage firstEngage = firstEngageList.get(i);

				// 首营更新时间
				if(null != firstEngage.getModTime() && firstEngage.getModTime() > 0){

					// 判断是否是今天
					if(DateUtil.isToday(DateUtil.convertString(firstEngage.getModTime(), DateUtil.DATE_FORMAT))){
						// 今天返回时间
						firstEngage.setModTimeStr(DateUtil.convertString(firstEngage.getModTime(), DateUtil.TIME_FORMAT_T));
					}

					// 不是返回日期
					else{
						firstEngage.setModTimeStr(DateUtil.convertString(firstEngage.getModTime(), DateUtil.DATE_FORMAT));
					}
				}else{
					firstEngage.setModTimeStr("--");
				}

				// 注册证至
				if(null != firstEngage.getRegistrationEffectiveDate() && firstEngage.getRegistrationEffectiveDate() > 0){
					firstEngage.setRegistrationEffectiveDateStr(DateUtil.convertString(firstEngage.getRegistrationEffectiveDate(), DateUtil.DATE_FORMAT));
				}else{
					firstEngage.setRegistrationEffectiveDateStr("--");
				}

				// 新国标分类赋值
				if(CollectionUtils.isNotEmpty(mapList)){
					int mapSize = mapList.size();
					for (int j = 0; j < mapSize; j++) {
						Map<String, Object> mapRes = mapList.get(j);
						// 如果根据新国标分类id能获取到数据
						if(firstEngage.getNewStandardCategoryId() > 0 && firstEngage.getNewStandardCategoryId().toString().equals(mapRes.get("standardCategoryId").toString())){
							firstEngage.setNewStandardCategoryName(mapRes.get("categoryName").toString());
						}
					}
				}
			}

			resultMap.put("firstEngageList", firstEngageList);
		}else{
			// 判断审核状态
			if(null == paramMap.get("status")){
				resultMap.put("total", total);
			}
			// 待审核
			if(null != paramMap.get("status") && "1".equals(paramMap.get("status").toString())){
				resultMap.put("one", one);
			}
			// 审核通过
			if(null != paramMap.get("status") && "3".equals(paramMap.get("status").toString())){
				resultMap.put("two", two);
			}
			// 审核不通过
			if(null != paramMap.get("status") && "2".equals(paramMap.get("status").toString())){
				resultMap.put("three", three);
			}
		}

		// 全部归属人列表
		List<User> assignments = userService.selectAllAssignUser();
		// 选中归属人（产品经理&产品助理）
		resultMap.put("assignments",assignments);

		page.setTotalRecord(NumberUtils.toInt(page1.getTotal()+""));

		resultMap.put("page", page);

		return resultMap;
	}

	/**
	 * 获取搜索列表
	 */
	@Override
	public List<FirstEngageSearchInfo> getFirstSearchInfo(Map<String, Object> paramMap) {

		return firstEngageMapper.getFirstSearchInfo(paramMap);
	}


	/**
	 * 删除首映商品信息
	 */
	@Override
	public Integer deleteFirstEngage(Map<String, Object> paramMap) {
		Integer[] ids  = (Integer[])paramMap.get("firstEngageArray");
		for(Integer id : ids){
			//将首营对应的生产企业关系删除掉
			FirstEngage firstEngage =  firstEngageMapper.selectByPrimaryKey(id);
			if(null != firstEngage && firstEngage.getRegistrationNumberId() !=null){
				registrationProductionApiService.deleteRegistrationProductionMode(firstEngage.getRegistrationNumberId());
			}
		}

		return firstEngageMapper.deleteFirstEngage(paramMap);
	}


	/**
	 * 根据输入查询注册证
	 */
	@Override
	public List<RegistrationNumber> getRegistrationInfoByStr(Map<String, Object> paramMap) {

		return registrationNumberMapper.getRegistrationInfoByStr(paramMap);
	}


	/**
	 * 添加商品首营信息
	 */
//	@Transactional
//	@Override
//	public Integer addFirstEngageInfo(FirstEngage firstEngage, User sessUser) {
//
//		RegistrationNumber registrationNumber = firstEngage.getRegistration();
//		Long addTime = new Date().getTime();
//		// 判断注册证信息是否为空
//		if (null == registrationNumber) {
//			return 0;
//		}
//
//
///***************************** 企业信息 **********************************************************/
//		// 公司信息
//		ProductCompany productCompany = registrationNumber.getProductCompany();
//		if (null == productCompany || EmptyUtils.isBlank(productCompany.getProductCompanyChineseName())) {
//			return 0;
//		}
//
//		// 判断公司id是否为空，如果不为空，编辑，如果为空，新增
//		if (null != productCompany.getProductCompanyId()) {
//			Integer updateCompanyResult = productCompanyMapper.updateByPrimaryKeySelective(productCompany);
//			// 修改失败
//			if (updateCompanyResult <= 0) {
//				return 0;
//			}
//		}
//		// 如果为空，则新增公司信息
//		else {
//			List<Map<String, Object>> existCompanys = productCompanyMapper.getallcompany(productCompany.getProductCompanyChineseName());
//			if (CollectionUtils.isNotEmpty(existCompanys)) {
//				for (Map<String, Object> map : existCompanys) {
//					Integer id = Integer.parseInt(map.get("value") + "");
//					registrationNumber.setProductCompanyId(id);
//					productCompany.setProductCompanyId(id);
//					productCompanyMapper.updateByPrimaryKeySelective(productCompany);
//					break;
//				}
//			} else {
//				// 1--添加企业公司信息
//				Integer addCompanyResult = productCompanyMapper.insertSelective(productCompany);
//				// 如果增失败
//				if (addCompanyResult <= 0) {
//					return 0;
//				}
//				// 新增成功，返回企业公司id
//				registrationNumber.setProductCompanyId(productCompany.getProductCompanyId());
//			}
//		}
//
//
///***************************** 注册证信息 **********************************************************/
//		// 将日期转成时间戳  批准日期
//		if (EmptyUtils.isNotBlank(registrationNumber.getIssuingDateStr())) {
//			registrationNumber.setIssuingDate(DateUtil.convertLong(registrationNumber.getIssuingDateStr(), DateUtil.DATE_FORMAT));
//		}
//		// 有效期至
//		// VDERP-8552 调整一类注册证的时间为9999-12-31
//		if (registrationNumber.getManageCategoryLevel().equals(CommonConstants.CLASS_ONE)) {
//			registrationNumber.setEffectiveDate(CommonConstants.INDEFINITELY_DATE);
//		} else {
//			registrationNumber.setEffectiveDate(DateUtil.convertLong(registrationNumber.getEffectiveDateStr().replace(CommonConstants.EFFECTIVE_DATE, ""), DateUtil.DATE_FORMAT));
//		}
//
//		// 变更日期 changeDateStr
//		if (EmptyUtils.isNotBlank(registrationNumber.getChangeDateStr())) {
//			registrationNumber.setChangeDate(DateUtil.convertLong(registrationNumber.getChangeDateStr(), DateUtil.DATE_FORMAT));
//		}
//
//		if (StringUtil.isNotBlank(registrationNumber.getBcIssueDateStr())) {
//			registrationNumber.setBcIssueDate(DateUtil.convertLong(registrationNumber.getBcIssueDateStr(), DateUtil.DATE_FORMAT));
//		}
//
//		// 判断注册证id是否存在
//		if (null != registrationNumber.getRegistrationNumberId()) {
//			registrationNumber.setUpdater(sessUser.getUserId());
//			// 修改注册证信息
//			Integer updateRegistrationResult = registrationNumberMapper.updateByPrimaryKeySelective(registrationNumber);
//			// 编辑失败
//			if (updateRegistrationResult <= 0) {
//				return 0;
//			}
//			saveNumberAttchmentHistory(registrationNumber, sessUser.getUserId());
//		}
//		// 新增
//		else {
//			// 添加注册证之前查询是否有重复的
//			RegistrationNumber registrationNumber1 = registrationNumberMapper.getRegistrationInfoByNumber(registrationNumber.getRegistrationNumber());
//			if (null != registrationNumber1) {
//				return 0;
//			}
//			registrationNumber.setCreator(sessUser.getUserId());
//			// 2--新增注册证信息
//			Integer addRegistrationResult = registrationNumberMapper.insertSelective(registrationNumber);
//			// 如果增失败
//			if (addRegistrationResult <= 0) {
//				return 0;
//			}
//		}
//		firstEngage.setRegistrationNumberId(registrationNumber.getRegistrationNumberId());
//
///***************************** 添加附件信息 *****************************************************************************/
//
//		//保存证书有效期
//		saveRegistrationExpiryDate(registrationNumber);
//
//		//用于存页面上编辑了哪些照片 删除使用 只删页面上传来的照片 目前页面上只传了 一种 注册证附件/备案凭证附件
//		List<Integer> attachmentFunction = new ArrayList<>();
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1010);
//		// 注册证
//		List<Attachment> attachmentsList = new ArrayList<>();
//		// 1.先删除之前的附件信息
//		Map<String, Object> attachmentMap = new HashMap<>();
//		// 参数：注册证id
//		attachmentMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
//		attachmentMap.put("manufacturerId", registrationNumber.getRegistrationNumberId()); //为了避免冲突 这里用了两个值来接收同一个id
//		// 首营附件信息模块
//		attachmentMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
//		attachmentMap.put("attachmentFunction", attachmentFunction);
//		// 删除存在的附件信息
//		attachmentMapper.updataByParamNew(attachmentMap);
//
//		// 注册证附件/备案凭证附件
//		if (CollectionUtils.isNotEmpty(registrationNumber.getZczAttachments())) {
//			attachmentsList.addAll(registrationNumber.getZczAttachments());
//		}
//
//		// 注册证附件/备案凭证附件源文件
//		if (CollectionUtils.isNotEmpty(registrationNumber.getZczyAttachments())) {
//			attachmentsList.addAll(registrationNumber.getZczyAttachments());
//		}
//
//		// 营业执照
//		if (CollectionUtils.isNotEmpty(registrationNumber.getYzAttachments())) {
//			attachmentsList.addAll(registrationNumber.getYzAttachments());
//		}
//
//		// 说明书
//		if (CollectionUtils.isNotEmpty(registrationNumber.getSmsAttachments())) {
//			attachmentsList.addAll(registrationNumber.getSmsAttachments());
//		}
//		// 生产企业生产许可证
//		if (CollectionUtils.isNotEmpty(registrationNumber.getScAttachments())) {
//			attachmentsList.addAll(registrationNumber.getScAttachments());
//		}
//		// 注册登记表附件
//		if (CollectionUtils.isNotEmpty(registrationNumber.getDjbAttachments())) {
//			attachmentsList.addAll(registrationNumber.getDjbAttachments());
//		}
//		// 2.再添加新增的附件信息
//		if (CollectionUtils.isNotEmpty(attachmentsList)) {
//			// 添加人
//			attachmentMap.put("userId", sessUser.getUserId());
//			//
//			attachmentMap.put("attachmentsList", attachmentsList);
//			int delAttachmentRes = 0;
//			for (Attachment attachment : attachmentsList) {
//				attachment.setCreator(sessUser.getUserId());
//				attachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_974);
//				attachment.setRelatedId(registrationNumber.getRegistrationNumberId());
//				attachment.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE);
//				attachment.setAddTime(addTime);
//				String name = attachment.getName();
//				if (name != null && name.length() > 128) {
//					attachment.setName(("文件名太长，请修改") + name.substring(name.length() - 30));
//				}
//				attachmentMapper.insertSelective(attachment);
//				delAttachmentRes++;
//			}
//			if (delAttachmentRes < 0) {
//				return 0;
//			}
//		}
//
///***************************** 新增首营信息 **********************************************************/
//
//		firstEngage.setCreator(sessUser.getUserId());
//		// 如果是器械
//		if (null != firstEngage.getGoodsType() && firstEngage.getGoodsType().equals(316)) {
//			firstEngage.setEffectiveDays(0);
//			firstEngage.setEffectiveDayUnit(1);
//		}
//		//  选择旧过表 则将 新国标id设为0  反之  互斥
//		if (2 == firstEngage.getStandardCategoryType()) {
//			firstEngage.setNewStandardCategoryId(0);
//		} else if (1 == firstEngage.getStandardCategoryType()) {
//			firstEngage.setOldStandardCategoryId(0);
//		}
//
//		// 保存修改首营信息，默认状态为待提交审核
//		firstEngage.setStatus(GoodsCheckStatusEnum.WAIT_TO_PRE.getStatus());
//
//		// 转换注册证至
//		if (CommonConstants.EFFECTIVE_DAY_UNIT_1.equals(firstEngage.getEffectiveDayUnit())) {
//			firstEngage.setSortDays(firstEngage.getEffectiveDays());
//		}
//		// 判断单位 月
//		if (CommonConstants.EFFECTIVE_DAY_UNIT_2.equals(firstEngage.getEffectiveDayUnit())) {
//			firstEngage.setSortDays(firstEngage.getEffectiveDays().intValue() * 30);
//		}
//		// 判断单位 年
//		if (CommonConstants.EFFECTIVE_DAY_UNIT_3.equals(firstEngage.getEffectiveDayUnit())) {
//			firstEngage.setSortDays(firstEngage.getEffectiveDays().intValue() * 365);
//		}
//		// 如果首营信息id不为空，修改操作
//		if (null != firstEngage.getFirstEngageId()) {
//			firstEngage.setUpdater(sessUser.getUserId());
//			firstEngage.setSignature(0);
//			Integer updateFirstEngageResult = firstEngageMapper.updateByPrimaryKeySelective(firstEngage);
//			// 修改失败
//			if (updateFirstEngageResult <= 0) {
//				return 0;
//			}
//		}
//
//		// 3--新增首营信息
//		else {
//			Integer addFirstEngageResult = firstEngageMapper.insertSelective(firstEngage);
//			// 如果增失败
//			if (addFirstEngageResult <= 0) {
//				return 0;
//			}
//		}
//		return firstEngage.getFirstEngageId();
//	}

	/**
	 * 添加商品首营信息
	 */
	@Transactional
	@Override
	public Integer addFirstEngageInfo(FirstEngage firstEngage, User sessUser) {
		RegistrationNumber registrationNumber = firstEngage.getRegistration();
		if (registrationNumber == null) {
			return 0;
		}

		ProductCompany productCompany = registrationNumber.getProductCompany();

		// 处理公司信息
		if (this.isProductCompanyInvalid(productCompany)) {
			return 0;
		}
		Integer companyId = this.processCompanyInfo(productCompany, registrationNumber);
		if (companyId == null) {
			return 0;
		}

		try
		{
			//注册证对应的生产企业由1个改为支持多个之后，原来的T_REGISTRATION_NUMBER 保留一条记录，暂无使用
			if(CollectionUtils.isNotEmpty(firstEngage.getManufacturerList()) && firstEngage.getRegistration() !=null){
				//生产模式: 0自行生产, 1委托生产
				Integer productionMode = firstEngage.getManufacturerList().get(0).getProductionMode();
				//生产企业名称
				String enterpriseName = firstEngage.getManufacturerList().get(0).getEnterpriseName();
				//生产企业ID
				Integer manufacturerId = firstEngage.getManufacturerList().get(0).getManufacturerId();
				RegistrationNumber regisObj = firstEngage.getRegistration() ;
				regisObj.setIsSubcontractProduction(productionMode);
				regisObj.setManufacturerId(manufacturerId);
				regisObj.setProductionEnterpriseName(enterpriseName);
			}
		}catch (Exception e){
			logger.error("生产企业转换失败",e);
		}



		// 处理注册证信息
		if (!this.processRegistrationInfo(registrationNumber, sessUser)) {
			return 0;
		}
		firstEngage.setRegistrationNumberId(registrationNumber.getRegistrationNumberId());

		// 处理附件信息
		if (!this.processAttachments(registrationNumber, sessUser.getUserId())) {
			return 0;
		}

		// 处理首营信息
		this.processFirstEngageInfo(firstEngage, sessUser);

		return this.saveOrUpdateFirstEngage(firstEngage, sessUser);
	}

	/**
	 * 检查产品公司信息的有效性。
	 * @param productCompany 产品公司对象
	 * @return 若公司信息无效，则返回 true，否则返回 false
	 */
	private boolean isProductCompanyInvalid(ProductCompany productCompany) {
		return productCompany == null || EmptyUtils.isBlank(productCompany.getProductCompanyChineseName());
	}

	/**
	 * 处理公司信息，包括更新或新增公司信息。
	 * @param productCompany 产品公司对象
	 * @param registrationNumber 注册证对象
	 * @return 成功返回公司 ID，否则返回 null
	 */
	private Integer processCompanyInfo(ProductCompany productCompany, RegistrationNumber registrationNumber) {
		Integer companyId = productCompany.getProductCompanyId();
		if (companyId != null) {
			return updateCompanyInfo(productCompany) > 0 ? companyId : null;
		}
		return addOrUpdateCompanyInfo(productCompany, registrationNumber);
	}

	/**
	 * 更新公司信息。
	 * @param productCompany 产品公司对象
	 * @return 更新结果，成功返回更新行数，失败返回 0
	 */
	private Integer updateCompanyInfo(ProductCompany productCompany) {
		return productCompanyMapper.updateByPrimaryKeySelective(productCompany);
	}

	/**
	 * 新增或更新公司信息，如果公司已存在则进行更新，否则新增。
	 * @param productCompany 产品公司对象
	 * @param registrationNumber 注册证对象
	 * @return 成功返回公司 ID，否则返回 null
	 */
	private Integer addOrUpdateCompanyInfo(ProductCompany productCompany, RegistrationNumber registrationNumber) {
		List<Map<String, Object>> existingCompanies = productCompanyMapper.getallcompany(productCompany.getProductCompanyChineseName());
		if (CollectionUtils.isNotEmpty(existingCompanies)) {
			Integer id = Integer.parseInt(existingCompanies.get(0).get("value").toString());
			registrationNumber.setProductCompanyId(id);
			productCompany.setProductCompanyId(id);
			productCompanyMapper.updateByPrimaryKeySelective(productCompany);
			return id;
		}
		if (productCompanyMapper.insertSelective(productCompany) <= 0) {
			return null;
		}
		registrationNumber.setProductCompanyId(productCompany.getProductCompanyId());
		return productCompany.getProductCompanyId();
	}


	/**
	 * 将日期字符串转换为时间戳。
	 * @param dateStr 日期字符串
	 * @return 转换后的时间戳，若为空则返回 null
	 */
	private Long parseDateString(String dateStr) {
		return EmptyUtils.isNotBlank(dateStr) ? DateUtil.convertLong(dateStr, DateUtil.DATE_FORMAT) : null;
	}

	/**
	 * 确定注册证的有效日期，如果是一级管理类别则设为永久日期。
	 * @param registrationNumber 注册证对象
	 * @return 确定的有效日期时间戳
	 */
	private Long determineEffectiveDate(RegistrationNumber registrationNumber) {
		if (CommonConstants.CLASS_ONE.equals(registrationNumber.getManageCategoryLevel())) {
			return CommonConstants.INDEFINITELY_DATE;
		}
		return parseDateString(registrationNumber.getEffectiveDateStr().replace(CommonConstants.EFFECTIVE_DATE, ""));
	}

	/**
	 * 处理注册证信息，根据注册证 ID 执行新增或更新操作。
	 * @param registrationNumber 注册证对象
	 * @param sessUser 当前用户
	 * @return 成功返回 true，否则返回 false
	 */
	private boolean processRegistrationInfo(RegistrationNumber registrationNumber, User sessUser) {
		registrationNumber.setIssuingDate(this.parseDateString(registrationNumber.getIssuingDateStr()));
		registrationNumber.setEffectiveDate(this.determineEffectiveDate(registrationNumber));
		registrationNumber.setChangeDate(this.parseDateString(registrationNumber.getChangeDateStr()));
		registrationNumber.setBcIssueDate(this.parseDateString(registrationNumber.getBcIssueDateStr()));
		checkDealStatus(registrationNumber);
		if (registrationNumber.getRegistrationNumberId() != null) {
			registrationNumber.setUpdater(sessUser.getUserId());
			if (registrationNumberMapper.updateByPrimaryKeySelective(registrationNumber) <= 0) {
				return false;
			}
			this.saveNumberAttchmentHistory(registrationNumber, sessUser.getUserId());
		} else {
			if (registrationNumberMapper.getRegistrationInfoByNumber(registrationNumber.getRegistrationNumber()) != null) {
				return false;
			}
			registrationNumber.setCreator(sessUser.getUserId());
			if (registrationNumberMapper.insertSelective(registrationNumber) <= 0) {
				return false;
			}
		}
		return true;
	}

	private void checkDealStatus(RegistrationNumber registrationNumberNew){
		RegistrationNumber registrationNumberObj = registrationNumberMapper.getRegistrationInfoByNumber(registrationNumberNew.getRegistrationNumber());
		//判断effectiveDate，Long类型的时间戳，毫秒时间戳，判断是否大于当前时间，且dealStatus为1
		if(registrationNumberObj !=null &&  ErpConst.ONE.equals(registrationNumberObj.getDealStatus())){//如果处理状态是待处理，且有效期大于当前时间
			//`EFFECTIVE_DATE` bigint(13) unsigned NOT NULL DEFAULT '0' COMMENT '有效期(截止日期)',
			//`DEAL_STATUS` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '过期处理状态 0 未到期 1待处理 2已处理',
			// 获取当前时间
			Calendar calendar = Calendar.getInstance();
			// 在当前时间基础上加6个月
			calendar.add(Calendar.MONTH, 6);
			// 获取6个月后的时间戳
			long sixMonthsLaterMillis = calendar.getTimeInMillis();
			if(registrationNumberNew.getEffectiveDate() !=null && registrationNumberNew.getEffectiveDate()>sixMonthsLaterMillis){
				registrationNumberNew.setDealStatus(ErpConst.TWO);
			}
		}
	}

	/**
	 * 处理附件信息，先删除旧的附件，再添加新的附件。
	 * @param registrationNumber 注册证对象
	 * @param userId 用户 ID
	 * @return 成功返回 true，否则返回 false
	 */
	private boolean processAttachments(RegistrationNumber registrationNumber, Integer userId) {
		List<Attachment> attachmentsList = this.gatherAttachments(registrationNumber);
		Map<String, Object> attachmentMap = this.prepareAttachmentMap(registrationNumber, userId);
		Long addTime = System.currentTimeMillis();
        attachmentMapper.updataByParamNew(attachmentMap);
		for (Attachment attachment : attachmentsList) {
			this.populateAttachmentInfo(attachment, userId, registrationNumber.getRegistrationNumberId(),addTime);
			if (attachmentMapper.insertSelective(attachment) <= 0) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 收集注册证的所有附件。
	 * @param registrationNumber 注册证对象
	 * @return 所有附件的列表
	 */
	private List<Attachment> gatherAttachments(RegistrationNumber registrationNumber) {
		List<Attachment> attachments = new ArrayList<>();
		attachments.addAll(Optional.ofNullable(registrationNumber.getZczAttachments()).orElse(Collections.emptyList()));
		attachments.addAll(Optional.ofNullable(registrationNumber.getZczyAttachments()).orElse(Collections.emptyList()));
		attachments.addAll(Optional.ofNullable(registrationNumber.getYzAttachments()).orElse(Collections.emptyList()));
		attachments.addAll(Optional.ofNullable(registrationNumber.getSmsAttachments()).orElse(Collections.emptyList()));
		attachments.addAll(Optional.ofNullable(registrationNumber.getScAttachments()).orElse(Collections.emptyList()));
		attachments.addAll(Optional.ofNullable(registrationNumber.getDjbAttachments()).orElse(Collections.emptyList()));
		attachments.addAll(Optional.ofNullable(registrationNumber.getLabelAttachments()).orElse(Collections.emptyList()));
		attachments.addAll(Optional.ofNullable(registrationNumber.getLabelSourceAttachments()).orElse(Collections.emptyList()));
		return attachments;
	}

	/**
	 * 准备附件信息映射，包括注册证 ID、制造商 ID、附件类型等。
	 * @param registrationNumber 注册证对象
	 * @param userId 用户 ID
	 * @return 附件信息的映射
	 */
	private Map<String, Object> prepareAttachmentMap(RegistrationNumber registrationNumber, Integer userId) {
		Map<String, Object> attachmentMap = new HashMap<>();
		attachmentMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
		attachmentMap.put("manufacturerId", registrationNumber.getRegistrationNumberId());
		attachmentMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
		attachmentMap.put("attachmentFunction", Arrays.asList(
				CommonConstants.ATTACHMENT_FUNCTION_975,
				CommonConstants.ATTACHMENT_FUNCTION_1010,
				CommonConstants.ATTACHMENT_FUNCTION_5608,
				CommonConstants.ATTACHMENT_FUNCTION_5609
		));
		attachmentMap.put("userId", userId);
		return attachmentMap;
	}

	/**
	 * 填充附件信息，设置创建者、附件类型、相关 ID 等信息。
	 * @param attachment 附件对象
	 * @param userId 用户 ID
	 * @param registrationId 注册证 ID
	 * @param addTime 添加时间
	 */
	private void populateAttachmentInfo(Attachment attachment, Integer userId, Integer registrationId, Long addTime) {
		attachment.setCreator(userId);
		attachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_974);
		attachment.setRelatedId(registrationId);
		attachment.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE);
		attachment.setAddTime(addTime);
		if (attachment.getName() != null && attachment.getName().length() > 128) {
			attachment.setName("文件名太长，请修改" + attachment.getName().substring(attachment.getName().length() - 30));
		}
	}

	/**
	 * 处理首营信息，包括设置有效期天数、状态等。
	 * @param firstEngage 首营对象
	 * @param sessUser 当前用户
	 */
	private void processFirstEngageInfo(FirstEngage firstEngage, User sessUser) {
		firstEngage.setCreator(sessUser.getUserId());
		if (firstEngage.getGoodsType() != null && firstEngage.getGoodsType().equals(316)) {
			firstEngage.setEffectiveDays(0);
			firstEngage.setEffectiveDayUnit(1);
		}
		if (firstEngage.getStandardCategoryType() == 2) {
			firstEngage.setNewStandardCategoryId(0);
		} else if (firstEngage.getStandardCategoryType() == 1) {
			firstEngage.setOldStandardCategoryId(0);
		}
		firstEngage.setStatus(GoodsCheckStatusEnum.WAIT_TO_PRE.getStatus());
		firstEngage.setSortDays(computeSortDays(firstEngage));
	}

	/**
	 * 计算排序天数，根据有效期单位转换成相应的天数。
	 * @param firstEngage 首营对象
	 * @return 计算后的排序天数
	 */
	private Integer computeSortDays(FirstEngage firstEngage) {
		if (CommonConstants.EFFECTIVE_DAY_UNIT_1.equals(firstEngage.getEffectiveDayUnit())) {
			return firstEngage.getEffectiveDays();
		} else if (CommonConstants.EFFECTIVE_DAY_UNIT_2.equals(firstEngage.getEffectiveDayUnit())) {
			return firstEngage.getEffectiveDays() * 30;
		} else if (CommonConstants.EFFECTIVE_DAY_UNIT_3.equals(firstEngage.getEffectiveDayUnit())) {
			return firstEngage.getEffectiveDays() * 365;
		}
		return 0;
	}

	/**
	 * 保存或更新首营信息，如果已存在则执行更新操作，否则新增。
	 * @param firstEngage 首营对象
	 * @param sessUser 当前用户
	 * @return 成功返回首营 ID，否则返回 0
	 */
	private Integer saveOrUpdateFirstEngage(FirstEngage firstEngage, User sessUser) {
		if (firstEngage.getFirstEngageId() != null) {
			firstEngage.setUpdater(sessUser.getUserId());
			firstEngage.setSignature(0);
			for(RegistrationProductionModeDto dto:firstEngage.getManufacturerList()){
				dto.setRegistrationNumberId(firstEngage.getRegistrationNumberId());
				dto.setAddTime(new Date());
				dto.setCreator(sessUser.getUserId());
				dto.setCreatorName(sessUser.getUsername());
				dto.setModTime(new Date());
				dto.setUpdater(sessUser.getUserId());
				dto.setUpdaterName(sessUser.getUsername());
			}
			this.registrationProductionApiService.saveRegistrationProductionMode(firstEngage.getRegistrationNumberId(),firstEngage.getManufacturerList());
			return firstEngageMapper.updateByPrimaryKeySelective(firstEngage) > 0 ? firstEngage.getFirstEngageId() : 0;
		}
		Integer firstEngageId = firstEngageMapper.insertSelective(firstEngage) > 0 ? firstEngage.getFirstEngageId() : 0;
		if(firstEngageId>0 && CollectionUtils.isNotEmpty(firstEngage.getManufacturerList())){
			for(RegistrationProductionModeDto dto:firstEngage.getManufacturerList()){
				dto.setRegistrationNumberId(firstEngage.getRegistrationNumberId());
				dto.setAddTime(new Date());
				dto.setCreator(sessUser.getUserId());
				dto.setCreatorName(sessUser.getUsername());
				dto.setModTime(new Date());
				dto.setUpdater(sessUser.getUserId());
				dto.setUpdaterName(sessUser.getUsername());
			}
			this.registrationProductionApiService.saveRegistrationProductionMode(firstEngage.getRegistrationNumberId(),firstEngage.getManufacturerList());
		}
		return firstEngageId;

	}

	@Autowired
	private RegistrationProductionApiService registrationProductionApiService;


	private void saveRegistrationExpiryDate(RegistrationNumber rn){
		goodsRegisrationExpiryDateMapper.deleteByRegistrationNumberId(rn.getRegistrationNumberId());
		List<GoodsRegisrationExpiryDate> dateList=new ArrayList<>();
        addExpiryDate(rn.getYzStartTimeStr(),rn.getYzEndTimeStr(),1000,rn.getRegistrationNumberId(),dateList);
        addExpiryDate(rn.getDjbStartTimeStr(),rn.getDjbEndTimeStr(),980,rn.getRegistrationNumberId(),dateList);
        addExpiryDate(rn.getScStartTimeStr(),rn.getScEndTimeStr(),978,rn.getRegistrationNumberId(),dateList);
        if(CollectionUtils.isEmpty(dateList)){
        	return;
		}
        dateList.stream().forEach(item->{
        	goodsRegisrationExpiryDateMapper.insertSelective(item);
		});
	}


	private void addExpiryDate(String startTimeStr,String endTimeStr,Integer type,Integer registrationNumberId,List<GoodsRegisrationExpiryDate> dateList){
		if(StringUtil.isBlank(startTimeStr)&&StringUtil.isBlank(endTimeStr)){
			return;
		}
		Long startTime=DateUtil.convertLong(startTimeStr,DateUtil.DATE_FORMAT);
		Long endTime=DateUtil.convertLong(endTimeStr,DateUtil.DATE_FORMAT);
		if(startTime==null&&endTime==null){
			return;
		}
		Long currentTime=System.currentTimeMillis();
		GoodsRegisrationExpiryDate date=new GoodsRegisrationExpiryDate();
		date.setAddTime(currentTime);
		date.setStartTime(startTime);
		date.setEndTime(endTime);
		date.setType(type);
		date.setRegistrationNumberId(registrationNumberId);
		dateList.add(date);
	}
	/**
	 * @description 校验首营信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/28
	 */
	@Override
	public void initFirstEngageInfo(FirstEngage firstEngage) throws ShowErrorMsgException {

		if(null == firstEngage){
			throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "请填写首营信息！");
		}

		// 注册证
		if(firstEngage.getRegistration() == null){
			throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "注册证信息！");
		}

		// 注册证号
        if(firstEngage.getRegistration().getRegistrationNumber() == null){
			throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "请选择或输入注册证号/备案凭证号！");
		}else{
			// 参数集
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("registrationStr1", firstEngage.getRegistration().getRegistrationNumber());

			// 如果当前操作是编辑首营信息，去重要排除当前编辑被引用的首营id
			if(null != firstEngage.getFirstEngageId()){
				paramMap.put("firstEngageId", firstEngage.getFirstEngageId());
			}
			List<RegistrationNumber> res = registrationNumberMapper.getRegistrationInfoByStr(paramMap);
			// 如果已经被引用，报错
			if(CollectionUtils.isNotEmpty(res)){
				throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "该注册证号/备案凭证号已经被引用！");
			}
		}

	}

	@Override
	public ResultInfo dealstatus(Integer registrationNumberId) {

		Integer res = registrationNumberMapper.dealstatus(registrationNumberId);
		if(res <= 0){
			return new ResultInfo(CommonConstants.FAIL_CODE, "操作失败");
		}

		return new ResultInfo(CommonConstants.SUCCESS_CODE, "操作成功");
	}

	/**
	 * @description 首营信息审核
	 * <AUTHOR>
	 * @param
	 * @date 2019/6/10
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void checkFirstengage(HttpServletRequest request, FirstEngage firstEngage, User sessUser) throws ShowErrorMsgException {
		// 临时文件存放地址
		String path = request.getSession().getServletContext().getRealPath("/upload/attachment");
		// 首营信息id
		if(null == firstEngage || null == firstEngage.getFirstEngageId()){
			throw new ShowErrorMsgException("找不到对应的首营信息！");
		}
		if (CommonConstants.FIRST_ENGAGE_STATUS_2.equals(firstEngage.getStatus()) && EmptyUtils.isBlank(firstEngage.getReason())) {
			throw new ShowErrorMsgException("审核不通过原因不能为空！");
		}
		FirstEngage originFirstEngage = firstEngageMapper.selectByPrimaryKey(firstEngage.getFirstEngageId());

		if (GoodsCheckStatusEnum.PRE.getStatus() == firstEngage.getStatus() && !manufacturerService.isValidByRegistrationNumber(originFirstEngage.getRegistrationNumberId())) {
			throw new ShowErrorMsgException("此注册证的【生产厂商】未审核通过，无法提交注册证审核！");
		}

		Integer signature = firstEngage.getSignature() == null ? 0 : firstEngage.getSignature();
		// 修改审核状态
		FirstEngage firstEngage1 = new FirstEngage();
		firstEngage1.setFirstEngageId(firstEngage.getFirstEngageId());
		firstEngage1.setStatus(firstEngage.getStatus());
		firstEngage1.setUpdater(sessUser.getUserId());
		if (firstEngage.getStatus() == 1) {
			firstEngage1.setSignature(signature);
		}
		firstEngageMapper.updateByPrimaryKeySelective(firstEngage1);

		// 添加审核记录
		vgoodsService.generateCheckLogAndSave(firstEngage.getFirstEngageId(),firstEngage.getStatus(),firstEngage.getReason(),LogTypeEnum.FIRSTENGAGE.getLogType());
		sendMessageWhenFirstEngageCheck(originFirstEngage,firstEngage.getStatus());
		sendRegisterAndEngageInfo(path, firstEngage.getFirstEngageId());

		// 审核通过，同时发起电子签章
		if ((firstEngage.getStatus() == 3) &&  originFirstEngage.getSignature() != null && originFirstEngage.getSignature() != 0) {
			try {
				ThreeCertificatesStamp threeCertificatesStamp = SpringContextHolder.getBean("threeCertificatesStamp");
				threeCertificatesStamp.certificatesStampRegistration(firstEngage.getFirstEngageId(), sessUser);
			} catch (Exception e) {
				logger.error(e.toString());
			}
		}
	}

	private void sendMessageWhenFirstEngageCheck(FirstEngage firstEngage, Integer checkStatus){
		int messageTemplateId = 0;
		List<Integer> userIdList = new ArrayList<>();
		Map<String,String> paramMap = new HashMap<>(1);
		paramMap.put("registrationNumber",registrationNumberMapper.selectByPrimaryKey(firstEngage.getRegistrationNumberId()).getRegistrationNumber());
		if (checkStatus == 1){
			messageTemplateId = 148;
			userIdList = roleMapper.getUserIdByRoleName(GoodsConstants.GOODS_CHECK_ROLE,1);

		} else if (checkStatus == 2){
			messageTemplateId = 150;
			if (firstEngage.getUpdater() != null && firstEngage.getUpdater() > 0) {
				userIdList.add(firstEngage.getUpdater());
			} else {
				userIdList.add(firstEngage.getCreator());
			}
		} else if (checkStatus == 3){
			messageTemplateId = 149;
			if (firstEngage.getUpdater() != null && firstEngage.getUpdater() > 0) {
				userIdList.add(firstEngage.getUpdater());
			} else {
				userIdList.add(firstEngage.getCreator());
			}
		}
		MessageUtil.sendMessage(messageTemplateId,userIdList,paramMap,"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId="+firstEngage.getFirstEngageId());
	}

	/**
	 * 发送注册证和首营信息
	 * @param firstEngageId
	 */
	private void sendRegisterAndEngageInfo(String path, Integer firstEngageId) {

		//首营信息
		FirstEngage firstEngageUpdate = firstEngageMapper.selectByPrimaryKey(firstEngageId);

		if(!CommonConstants.FIRST_ENGAGE_STATUS_3.equals(firstEngageUpdate.getStatus())){
			return;
		}

		List<String> skuNoList = firstEngageMapper.getSkuNoListByFirstEngageId(firstEngageId);

		if(CollectionUtils.isEmpty(skuNoList)){
			return;
		}

        GlobalThreadPool.submitMessage(() -> {
			String uuid = UUID.randomUUID().toString().replace("-", "");
			logger.info("开始执行商品注册证下传WMS、商品资质下传WMS，uuid:{}",uuid);
            //商品注册证下传WMS
            try {

                List<WmsSkuReg> wmsSkuRegList = getRegisterListReq(skuNoList,firstEngageUpdate.getRegistrationNumberId());

                if(CollectionUtils.isNotEmpty(wmsSkuRegList)){

                    logger.info("ERP下传商品多注册证至WMS的请求:" + JSON.toJSONString(wmsSkuRegList));
                    WmsInterface putSkuInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_REG);
                    WmsResponse response = putSkuInterface.request(wmsSkuRegList.toArray());
                    logger.info("ERP下传商品多注册证至WMS的响应:" + JSON.toJSONString(response));

                }

            }catch (Exception e){
                logger.error("ERP下传商品多注册证至WMS请求接口报错",e);
            }

            //商品资质下传WMS
            try {
                List<WmsQLA> wmsQLAList = getWmsQlaListReq(skuNoList,firstEngageUpdate.getRegistrationNumberId());
                if(CollectionUtils.isNotEmpty(wmsQLAList)){
                    logger.info("ERP下传商品资质至WMS的请求:" + JSON.toJSONString(wmsQLAList));
                    WmsInterface putQalInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_QLA);
                    WmsResponse response = putQalInterface.request(wmsQLAList.toArray());
                    logger.info("ERP下传商品资质至WMS的响应:" + JSON.toJSONString(response));
                }
            }catch (Exception e){
                logger.error("ERP下传商品资质至WMS请求接口报错",e);
            }
			logger.info("开始执行商品注册证下传WMS、商品资质下传WMS，uuid:{}",uuid);
        });


		//同步至资料库
		try {
			syncGoodsService.syncGoods2Doc(DocSyncEventEnum.FIRST_ENGAGE_SYNC.getType(), firstEngageId, firstEngageUpdate.getRegistrationNumberId());

		} catch (Exception e) {
			logger.error("同步资料库注册证审核信息失败", e);
		}

	}

    /**
     * 获取资质图片列表
     * @param skuNoList
     * @param registrationNumberId
     * @return
     */
    private List<WmsQLA> getWmsQlaListReq(List<String> skuNoList, Integer registrationNumberId) {

        List<WmsQLA> attachmentList = this.attachmentMapper.getWmsQlaListByRegisterNumberId(registrationNumberId);

        if(CollectionUtils.isEmpty(attachmentList)){
			return null;
		}

		List<WmsQLA> wmsQLAList = new ArrayList<>();

		skuNoList.stream().forEach(skuNo -> {

			attachmentList.stream().forEach(ata->{
				WmsQLA wmsQLA = new WmsQLA();
				BeanUtils.copyProperties(ata,wmsQLA);
				wmsQLA.setSkuNO(skuNo);
				wmsQLAList.add(wmsQLA);
			});

		});

		return wmsQLAList;
    }

    /**
	 * 获取发送注册证的请求
	 * @param skuNoList
	 * @param registrationNumberId
	 * @return
	 */
	private List<WmsSkuReg> getRegisterListReq(List<String> skuNoList,Integer registrationNumberId) {

		if(registrationNumberId == null){
			return null;
		}

		RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(registrationNumberId);

		List<WmsSkuReg> wmsSkuReqList = new ArrayList<>();

		skuNoList.stream().forEach(skuNo->{
			WmsSkuReg wmsSkuReg = new WmsSkuReg();
			wmsSkuReg.setSkuNo(skuNo);
			wmsSkuReg.setMemo(registrationNumber.getRemarks());
			wmsSkuReg.setRegistrationNumber(registrationNumber.getRegistrationNumber());
			wmsSkuReg.setApprovalNoValidFrom(registrationNumber.getIssuingDate());
			wmsSkuReg.setApprovalNoValidTo(registrationNumber.getEffectiveDate());
			wmsSkuReqList.add(wmsSkuReg);
		});

		return wmsSkuReqList;
	}

	@Override
	public List<LogCheckGenerate> listSkuCheckLog(Integer firstEngageId) {
		LogCheckGenerateExample example=new LogCheckGenerateExample();
		example.createCriteria().andLogBizIdEqualTo(firstEngageId).andLogTypeEqualTo(LogTypeEnum.FIRSTENGAGE.getLogType());
		// 操作后会同时生成两条ADD_TIME一样的V_LOG_CHECK记录，单纯根据ADD_TIME倒序排列，只能按照两条一组的规律倒序排列，但每组内的两条数据还需要再次排列，排列规则暂定为按ID正序，此规则不够科学！
		example.setOrderByClause(" ADD_TIME desc,LOG_ID desc ");
		return logCheckGenerateMapper.selectByExample(example);
	}

	@Override
	public Integer getOldStandardId(String cellval) {

		return firstEngageMapper.getOldStandardId(cellval);
	}

	@Override
	public Integer getNewStandardId(Map<String, Object> param) {
		return firstEngageMapper.getNewStandardId(param);
	}


    @Override
    public List<FirstEngage> test(Map<String, Object> paramMap) {
        List<FirstEngage> firstEngageListT = firstEngageMapper.getFirstEngageInfoList(paramMap);
        return firstEngageListT;
    }

	@Override
	public FirstEngage getFirstSearchDetailOnlyZczAttachment(Map<String, Object> paramMap,Integer firstEngageId) {
		// 返回信息
		FirstEngage firstEngage = firstEngageMapper.selectByPrimaryKey(firstEngageId);
		if(firstEngage==null){
			return null;
		}
		// 新国标
		if( null != firstEngage.getNewStandardCategoryId() && firstEngage.getNewStandardCategoryId() > 0){
			List<FirstEngage> firstEngageList = new ArrayList<>();
			firstEngageList.add(firstEngage);
			// 根据新国标分类id查询新国标分类
			List<Map<String, Object>> mapList = standardCategoryMapper.getStandardCategoryStrMap(firstEngageList);
			if(CollectionUtils.isNotEmpty(mapList)){
				firstEngage.setNewStandardCategoryName(mapList.get(0).get("categoryName").toString());
			}
		}

		// 旧国标
		if(null != firstEngage.getOldStandardCategoryId() && firstEngage.getOldStandardCategoryId() > 0){
			SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(firstEngage.getOldStandardCategoryId());
			firstEngage.setOldStandardCategoryName(sysOptionDefinition.getTitle());
		}


		// 存储条件
		List<FirstEngageStorageCondition> storageCondition = firstEngageStorageConditionMapper.selectByParam(paramMap);
		if(CollectionUtils.isNotEmpty(storageCondition)){
			firstEngage.setStorageCondition(storageCondition);
		}

		// 注册证信息
		RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(firstEngage.getRegistrationNumberId());

		// 转换时间 批准日期
		if(null != registrationNumber.getIssuingDate() && registrationNumber.getIssuingDate() > 0){
			registrationNumber.setIssuingDateStr(DateUtil.convertString(registrationNumber.getIssuingDate(), DateUtil.DATE_FORMAT));
		}
		// 转换时间 有效期至
		if(null != registrationNumber.getEffectiveDate() && registrationNumber.getEffectiveDate() > 0){
			registrationNumber.setEffectiveDateStr(DateUtil.convertString(registrationNumber.getEffectiveDate(), DateUtil.DATE_FORMAT));
		}
		// 转换时间 有效期至
		if(null != registrationNumber.getChangeDate() && registrationNumber.getChangeDate() > 0){
			registrationNumber.setChangeDateStr(DateUtil.convertString(registrationNumber.getChangeDate(), DateUtil.DATE_FORMAT));
		}

		// 附件类型
		paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
		List<Integer> attachmentFunction = new ArrayList<>();
		// 注册证附件/备案凭证附件
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
		//注册证源文件/备案凭证源文件
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1010);

//		// 说明书
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_976);
//		// 营业执照
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1000);
//		// 生产企业卫生许可证
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_977);
//		// 生产企业生产许可证
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_978);
//		// 商标注册证
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_979);
//		// 注册登记表附件
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_980);
//		// 产品图片（单包装/大包装）
//		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_981);
		paramMap.put("attachmentFunction", attachmentFunction);
		paramMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
		// 所有附件
		List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);

		if(CollectionUtils.isNotEmpty(attachments)){
			// 注册证附件/备案凭证附件
			List<Attachment> zczAttachments = new ArrayList<>();
			// 编辑注册证附件信息
			List<Map<String, Object>> zczMapList = new ArrayList<>();

			//注册证源文件/备案凭证源文件
			List<Attachment> zczyAttachments= new ArrayList<>();


			// 营业执照
			List<Attachment> yzAttachments = new ArrayList<>();
			List<Map<String, Object>> yzMapList = new ArrayList<>();

			// 说明书
			List<Attachment> smsAttachments = new ArrayList<>();
			List<Map<String, Object>> smsMapList = new ArrayList<>();

			// 生产企业卫生许可证
			List<Attachment> wsAttachments = new ArrayList<>();
			List<Map<String, Object>> wsMapList = new ArrayList<>();

			// 生产企业生产许可证
			List<Attachment> scAttachments = new ArrayList<>();
			List<Map<String, Object>> scMapList = new ArrayList<>();

			// 商标注册证
			List<Attachment> sbAttachments = new ArrayList<>();
			List<Map<String, Object>> sbMapList = new ArrayList<>();

			// 注册登记表附件
			List<Attachment> djbAttachments = new ArrayList<>();
			List<Map<String, Object>> djbMapList = new ArrayList<>();

			// 产品图片（单包装/大包装）
			List<Attachment> cpAttachments = new ArrayList<>();
			List<Map<String, Object>> cpMapList = new ArrayList<>();

			int size = attachments.size();
			for (int i = 0; i < size; i++) {
				Attachment attachment = attachments.get(i);
				// 编辑注册证附件信息
				Map<String, Object> attachmentMap = new HashMap<>();
				attachmentMap.put("message", "操作成功");
//				attachmentMap.put("httpUrl", api_http+domain);
				attachmentMap.put("httpUrl", api_http+attachment.getDomain());
				// uri
				String uri = attachment.getUri();
				if(EmptyUtils.isEmpty(uri)){
					continue;
				}
				String[] uriArray = uri.split("/");
				String fileName = uriArray[uriArray.length-1];
				String fileNameTemp = "/" + fileName;
				// 文件后缀
				String[] prefixArray = fileNameTemp.split("\\.");
				String prefix = prefixArray[prefixArray.length-1];
				// 去除路径名
				String filePath = uri.replaceAll(fileNameTemp, "");
				attachmentMap.put("fileName", fileName);
				attachmentMap.put("filePath", filePath);
				attachmentMap.put("prefix", prefix);

				// 将注册证分组
				if(CommonConstants.ATTACHMENT_FUNCTION_975.equals(attachment.getAttachmentFunction())){
					zczAttachments.add(attachment);
					zczMapList.add(attachmentMap);
				}
				//注册证源文件
				if (CommonConstants.ATTACHMENT_FUNCTION_1010.equals(attachment.getAttachmentFunction())){
					zczyAttachments.add(attachment);
				}

				// 营业执照
				if(CommonConstants.ATTACHMENT_FUNCTION_1000.equals(attachment.getAttachmentFunction())){
					yzAttachments.add(attachment);
					yzMapList.add(attachmentMap);
				}
				// 说明书
				if(CommonConstants.ATTACHMENT_FUNCTION_976.equals(attachment.getAttachmentFunction())){
					smsAttachments.add(attachment);
					smsMapList.add(attachmentMap);
				}
				// 生产企业卫生许可证
				if(CommonConstants.ATTACHMENT_FUNCTION_977.equals(attachment.getAttachmentFunction())){
					wsAttachments.add(attachment);
					wsMapList.add(attachmentMap);
				}
				// 生产企业生产许可证
				if(CommonConstants.ATTACHMENT_FUNCTION_978.equals(attachment.getAttachmentFunction())){
					scAttachments.add(attachment);
					scMapList.add(attachmentMap);
				}
				// 商标注册证
				if(CommonConstants.ATTACHMENT_FUNCTION_979.equals(attachment.getAttachmentFunction())){
					sbAttachments.add(attachment);
					sbMapList.add(attachmentMap);
				}
				// 注册登记表附件
				if(CommonConstants.ATTACHMENT_FUNCTION_980.equals(attachment.getAttachmentFunction())){
					djbAttachments.add(attachment);
					djbMapList.add(attachmentMap);
				}
				// 产品图片（单包装/大包装）
				if(CommonConstants.ATTACHMENT_FUNCTION_981.equals(attachment.getAttachmentFunction())){
					cpAttachments.add(attachment);
					cpMapList.add(attachmentMap);
				}
			}
			firstEngage.setZczMapList(zczMapList);
			firstEngage.setYzMapList(yzMapList);
			firstEngage.setSmsMapList(smsMapList);
			firstEngage.setWsMapList(wsMapList);
			firstEngage.setScMapList(scMapList);
			firstEngage.setSbMapList(sbMapList);
			firstEngage.setDjbMapList(djbMapList);
			firstEngage.setCpMapList(cpMapList);

			registrationNumber.setZczAttachments(zczAttachments);
			registrationNumber.setYzAttachments(yzAttachments);
			registrationNumber.setSmsAttachments(smsAttachments);
			registrationNumber.setWsAttachments(wsAttachments);
			registrationNumber.setScAttachments(scAttachments);
			registrationNumber.setSbAttachments(sbAttachments);
			registrationNumber.setDjbAttachments(djbAttachments);
			registrationNumber.setCpAttachments(cpAttachments);
			registrationNumber.setZczyAttachments(zczyAttachments);
		}
		registrationNumber.setAttachments(attachments);

// 企业信息

		ProductCompany productCompany = productCompanyMapper.selectByPrimaryKey(registrationNumber.getProductCompanyId());
		registrationNumber.setProductCompany(productCompany);
		firstEngage.setRegistration(registrationNumber);
		firstEngage.setRegistrationNumber(registrationNumber!=null?registrationNumber.getRegistrationNumber():"");
		firstEngage.setProductCompanyChineseName(productCompany!=null? StringUtils.isBlank(productCompany.getProductCompanyChineseName())?productCompany.getProductCompanyEnglishName():productCompany.getProductCompanyChineseName():"");
		firstEngage.setEffectStartDate(registrationNumber.getIssuingDateStr());
		firstEngage.setEffectEndDate(registrationNumber.getEffectiveDateStr());
		//管理类别
		firstEngage.setManageCategoryLevelShow(
				registrationNumber.getManageCategoryLevel()+""
		);
		firstEngage.setRegistration(registrationNumber);
		return firstEngage;
	}

	@Override
	public List<Attachment> queryRegistrationAttachmentList(String addTime) {
		Map<String,Object> paramMap = new HashMap<>();
		paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
		List<Integer> attachmentFunction = new ArrayList<>();
		// 注册证附件/备案凭证附件
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
		paramMap.put("attachmentFunction", attachmentFunction);
		if (StringUtils.isNotBlank(addTime)){
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-mm-dd HH:mm:ss");
			try {
				paramMap.put("addTime", simpleDateFormat.parse(addTime + " 00:00:00").getTime());
			} catch (ParseException e) {
				logger.error("转换日期异常：", e);
			}

		}
		List<Attachment> attachmentList = attachmentMapper.getAttachmentsList(paramMap);
		return attachmentList;
	}

	@Override
	public FirstEngageInfoDto getFirstEngageInfoBySkuNo(String skuNo) {
		return firstEngageGenerateMapper.getFirstEngageInfoBySkuNo(skuNo);
	}


	/*
	 * 查询注册证信息
	 * <p>Title: getRegistrationNumberInfoById</p>
	 * <p>Description: </p>
	 * @param paramMap
	 * @return
	 * @see com.vedeng.firstengage.service.FirstEngageService#getRegistrationNumberInfoById(java.util.Map)
	 */
	@Override
	public RegistrationNumber getRegistrationNumberInfoById(Map<String, Object> paramMap) {
		RegistrationNumber registrationNumber = registrationNumberMapper.getRegistrationInfoById(paramMap);
		// 处理日期
		if(null != registrationNumber){
			// 批准日期
			if(null != registrationNumber.getIssuingDate() && registrationNumber.getIssuingDate() > 0){
				registrationNumber.setIssuingDateStr(DateUtil.convertString(registrationNumber.getIssuingDate(), DateUtil.DATE_FORMAT));
			}
			// 有效期至
			if(null != registrationNumber.getEffectiveDate() && registrationNumber.getEffectiveDate() > 0){
				registrationNumber.setEffectiveDateStr(DateUtil.convertString(registrationNumber.getEffectiveDate(), DateUtil.DATE_FORMAT));
			}
		}
		return registrationNumber;
	}


	/**
	 * 新国标分类
	 */
	@Override
	public List<Map<String, Object>> getNewStandardCategory(Map<String, Object> paramMap) {

		// 查询新国标分类
		List<StandardCategory> newStandCategoryList = standardCategoryMapper.getNewStandardCategory(paramMap).stream().sorted((u1, u2) -> u1.getCategoryName().compareTo(u2.getCategoryName())).collect(Collectors.toList());
		if(CollectionUtils.isNotEmpty(newStandCategoryList)){
			// 一级新国标分类
			List<Map<String, Object>> categoryOneList = new ArrayList<>();
			// 一级分类
			int oneSize = newStandCategoryList.size();
			for (int i = 0; i < oneSize; i++) {
				StandardCategory categoryOne = newStandCategoryList.get(i);
				Map<String, Object> oneMap = new HashMap<>();
				// 名称--前段定义--label
				oneMap.put("label", categoryOne.getCategoryName());
				// id--前段定义--value
				oneMap.put("value", categoryOne.getStandardCategoryId().toString());

				// 二级分类
				if(CollectionUtils.isNotEmpty(categoryOne.getStandardCategoryList())){
					// 二级新国标分类
					List<Map<String, Object>> categoryTwoList = new ArrayList<>();
					int twoSize = categoryOne.getStandardCategoryList().size();
					for (int j = 0; j < twoSize; j++) {
						StandardCategory categoryTwo = categoryOne.getStandardCategoryList().get(j);
						Map<String, Object> twoMap = new HashMap<>();
						// 名称--前段定义--label
						twoMap.put("label", categoryTwo.getCategoryName());
						// id--前段定义--value
						twoMap.put("value", categoryTwo.getStandardCategoryId().toString());
						// 三级分类
						if(CollectionUtils.isNotEmpty(categoryTwo.getStandardCategoryList())){
							// 三级新国标分类
							List<Map<String, Object>> categoryThreeList = new ArrayList<>();
							int threeSize = categoryTwo.getStandardCategoryList().size();
							for (int k = 0; k < threeSize; k++) {
								StandardCategory categoryThree = categoryTwo.getStandardCategoryList().get(k);
								Map<String, Object> threeMap = new HashMap<>();
								// 名称--前段定义--label
								threeMap.put("label", categoryThree.getCategoryName());
								// id--前段定义--value
								threeMap.put("value", categoryThree.getStandardCategoryId().toString());
								categoryThreeList.add(threeMap);
							}
							twoMap.put("child", categoryThreeList);
						}
						// 如果为null，返回null
						else{
							twoMap.put("child", null);
						}
						categoryTwoList.add(twoMap);
					}
					oneMap.put("child", categoryTwoList);
				}
				// 如果为null，返回null
				else{
					oneMap.put("child", null);
				}
				categoryOneList.add(oneMap);
			}
			return categoryOneList;
		}
		return null;
	}

	@Transactional(readOnly = true, rollbackFor = Exception.class)
	@Override
	public FirstEngage getFirstSearchBaseInfo( Integer firstEngageId) {
		// 返回信息
		FirstEngage firstEngage = firstEngageMapper.selectByPrimaryKey(firstEngageId);
		if(firstEngage==null|| CommonConstants.IS_DELETE_1.equals(firstEngage.getIsDeleted())){
			return null;
		}
		// 新国标
		if(null != firstEngage.getNewStandardCategoryId() && firstEngage.getNewStandardCategoryId() > 0){
			List<FirstEngage> firstEngageList = new ArrayList<>();
			firstEngageList.add(firstEngage);
			// 根据新国标分类id查询新国标分类
			List<Map<String, Object>> mapList = standardCategoryMapper.getStandardCategoryStrMap(firstEngageList);
			if(CollectionUtils.isNotEmpty(mapList)){
				firstEngage.setNewStandardCategoryName(mapList.get(0).get("categoryName").toString());
			}
		}
		// 旧国标
		if(null != firstEngage.getOldStandardCategoryId() && firstEngage.getOldStandardCategoryId() > 0){
			SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(firstEngage.getOldStandardCategoryId());
			firstEngage.setOldStandardCategoryName(sysOptionDefinition.getTitle());
		}
		// 注册证信息
		RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(firstEngage.getRegistrationNumberId());
		// 转换时间 批准日期
		if(null != registrationNumber.getIssuingDate() && registrationNumber.getIssuingDate() > 0){
			registrationNumber.setIssuingDateStr(DateUtil.convertString(registrationNumber.getIssuingDate(), DateUtil.DATE_FORMAT));
		}
		// 转换时间 有效期至
		if(null != registrationNumber.getEffectiveDate() && registrationNumber.getEffectiveDate() > 0){
			registrationNumber.setEffectiveDateStr(DateUtil.convertString(registrationNumber.getEffectiveDate(), DateUtil.DATE_FORMAT));
		}
		// 转换时间 有效期至
		if(null != registrationNumber.getChangeDate() && registrationNumber.getChangeDate() > 0){
			registrationNumber.setChangeDateStr(DateUtil.convertString(registrationNumber.getChangeDate(), DateUtil.DATE_FORMAT));
		}

		// 企业信息
		ProductCompany productCompany = productCompanyMapper.selectByPrimaryKey(registrationNumber.getProductCompanyId());
		registrationNumber.setProductCompany(productCompany);
		firstEngage.setRegistration(registrationNumber);
		firstEngage.setRegistrationNumber(registrationNumber!=null?registrationNumber.getRegistrationNumber():"");
		firstEngage.setProductCompanyChineseName(productCompany!=null? StringUtils.isBlank(productCompany.getProductCompanyChineseName())?productCompany.getProductCompanyEnglishName():productCompany.getProductCompanyChineseName():"");
		firstEngage.setEffectStartDate(registrationNumber.getIssuingDateStr());
		firstEngage.setEffectEndDate(registrationNumber.getEffectiveDateStr());
		//管理类别
		firstEngage.setManageCategoryLevelShow(
				 registrationNumber.getManageCategoryLevel()+""
		);
		return firstEngage;
	}
	//@Override
	//public FirstEngage getFirstSearchDetail(Map<String, Object> paramMap, Integer firstEngageId) {
	//
	//
	//	// 返回信息
	//	FirstEngage firstEngage = firstEngageMapper.selectByPrimaryKey(firstEngageId);
	//	if(firstEngage == null){
	//		logger.warn("根据主键查询首营信息失败,key=" + firstEngageId );
	//		return null;
	//	}
	//
	//	if(StringUtils.isNotEmpty(firstEngage.getStorageConditionOthers())){
	//		firstEngage.setStorageConditionOthersArray(GoodsStorageConditionOthersEnum.covert2OthersCodes(firstEngage.getStorageConditionOthers()));
	//	}
	//
	//	// 新国标
	//	if(null != firstEngage.getNewStandardCategoryId() && firstEngage.getNewStandardCategoryId() > 0){
	//		List<FirstEngage> firstEngageList = new ArrayList<>();
	//		firstEngageList.add(firstEngage);
	//		// 根据新国标分类id查询新国标分类
	//		List<Map<String, Object>> mapList = standardCategoryMapper.getStandardCategoryStrMap(firstEngageList);
	//		if(CollectionUtils.isNotEmpty(mapList)){
	//			firstEngage.setNewStandardCategoryName(mapList.get(0).get("categoryName").toString());
	//		}
	//	}
	//
	//	// 旧国标
	//	if(null != firstEngage.getOldStandardCategoryId() && firstEngage.getOldStandardCategoryId() > 0){
	//		SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(firstEngage.getOldStandardCategoryId());
	//		firstEngage.setOldStandardCategoryName(sysOptionDefinition.getTitle());
	//	}
	//
	//	// 注册证信息
	//	RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(firstEngage.getRegistrationNumberId());
	//
	//
	//	//初始化有效期
	//	initExpiryDate(registrationNumber);
	//	// 转换时间 批准日期
	//	if(null != registrationNumber.getIssuingDate() && registrationNumber.getIssuingDate() > 0){
	//		registrationNumber.setIssuingDateStr(DateUtil.convertString(registrationNumber.getIssuingDate(), DateUtil.DATE_FORMAT));
	//	}
	//	// 转换时间 有效期至
	//	if(null != registrationNumber.getEffectiveDate() && registrationNumber.getEffectiveDate() > 0){
	//		registrationNumber.setEffectiveDateStr(DateUtil.convertString(registrationNumber.getEffectiveDate(), DateUtil.DATE_FORMAT));
	//	}
	//	// 转换时间 有效期至
	//	if(null != registrationNumber.getChangeDate() && registrationNumber.getChangeDate() > 0){
	//		registrationNumber.setChangeDateStr(DateUtil.convertString(registrationNumber.getChangeDate(), DateUtil.DATE_FORMAT));
	//	}
	//
	//	if(null != registrationNumber.getBcIssueDate() && registrationNumber.getBcIssueDate() > 0){
	//		registrationNumber.setBcIssueDateStr(DateUtil.convertString(registrationNumber.getBcIssueDate(), DateUtil.DATE_FORMAT));
	//	}
	//
	//	// 附件类型
	//	paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
	//	List<Integer> attachmentFunction = new ArrayList<>();
	//	// 注册证附件/备案凭证附件
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
	//	// 注册证附件/备案凭证附件源文件
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1010);
	//	// 说明书
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_976);
	//
	//	// 生产企业卫生许可证
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_977);
	//
	//	// 商标注册证
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_979);
	//	// 产品图片（单包装/大包装）
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_981);
	//
	//
    //    // 营业执照(新)
    //    attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1307);//原来1000
    //    // 生产企业生产许可证（新）
    //    attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1306); //原来978
    //    // 注册登记表附件（即生产企业生产登记表（新））
    //    attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1305); //原来980
	//
	//	//新增四种照片类型
	//	//注册证附件/备案凭证附件（贝）
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1301); //上传使用
	//	//营业执照（贝）
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1302);
	//	//生产企业生产许可证或备案凭证(贝)
	//	attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1303);
    //    // 注册登记表附件（即生产企业生产登记表（贝））
    //    attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1304);
	//	paramMap.put("attachmentFunction", attachmentFunction);
	//	paramMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
	//	// 所有附件
	//	List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);
	//
	//	if(CollectionUtils.isNotEmpty(attachments)){
	//		// 注册证附件/备案凭证附件
	//		List<Attachment> zczAttachments = new ArrayList<>();
	//		// 注册证附件/备案凭证附件源文件
	//		List<Attachment> zczyAttachments = new ArrayList<>();
	//		List<Map<String, Object>> zczyMapList = new ArrayList<>();
	//		// 编辑注册证附件信息
	//		List<Map<String, Object>> zczMapList = new ArrayList<>();
	//		// 说明书
	//		List<Attachment> smsAttachments = new ArrayList<>();
	//		List<Map<String, Object>> smsMapList = new ArrayList<>();
	//
	//		// 生产企业卫生许可证
	//		List<Attachment> wsAttachments = new ArrayList<>();
	//		List<Map<String, Object>> wsMapList = new ArrayList<>();
	//		// 商标注册证
	//		List<Attachment> sbAttachments = new ArrayList<>();
	//		List<Map<String, Object>> sbMapList = new ArrayList<>();
	//		// 产品图片（单包装/大包装）
	//		List<Attachment> cpAttachments = new ArrayList<>();
	//		List<Map<String, Object>> cpMapList = new ArrayList<>();
	//
	//
    //        // 注册登记表附件（新）
    //        List<Attachment> djbAttachments = new ArrayList<>();
    //        List<Map<String, Object>> djbMapList = new ArrayList<>();
	//
	//		//注册证附件/备案凭证附件（贝）
	//		List<Attachment> zcZBAttachments = new ArrayList<>();
	//		List<Map<String, Object>> zcZBMapList = new ArrayList<>();
	//
 	//		int size = attachments.size();
	//		for (int i = 0; i < size; i++) {
	//			Attachment attachment = attachments.get(i);
	//			// 编辑注册证附件信息
	//			Map<String, Object> attachmentMap = new HashMap<>();
	//			attachmentMap.put("message", "操作成功");
	//			attachmentMap.put("httpUrl", api_http+attachment.getDomain());
	//			// uri
	//			String uri = attachment.getUri();
	//			if(EmptyUtils.isEmpty(uri)){
	//				continue;
	//			}
	//			String[] uriArray = uri.split("/");
	//			String fileName = uriArray[uriArray.length-1];
	//			String fileNameTemp = "/" + fileName;
	//			// 文件后缀
	//			String[] prefixArray = fileNameTemp.split("\\.");
	//			String prefix = prefixArray[prefixArray.length-1];
	//			// 去除路径名
	//			String filePath = uri.replaceAll(fileNameTemp, "");
	//			attachmentMap.put("fileName", fileName);
	//			if(!EmptyUtils.isEmpty(attachment.getName())){
	//				attachmentMap.put("name",attachment.getName());
	//			}
	//			attachmentMap.put("filePath", uri);
	//			attachmentMap.put("prefix", prefix);
	//			attachmentMap.put("domain",attachment.getDomain());
	//			attachmentMap.put("suffix",attachment.getSuffix());
	//			// 将注册证分组
	//			if(CommonConstants.ATTACHMENT_FUNCTION_975.equals(attachment.getAttachmentFunction())){
	//				zczAttachments.add(attachment);
	//				zczMapList.add(attachmentMap);
	//			}
	//			// 将注册证源文件分组
	//			if(CommonConstants.ATTACHMENT_FUNCTION_1010.equals(attachment.getAttachmentFunction())){
	//				zczyAttachments.add(attachment);
	//				attachmentMap.put("fileName", attachment.getName());
	//				zczyMapList.add(attachmentMap);
	//			}
	//
	//			//注册证附件/备案凭证附件（贝）
	//			if(CommonConstants.ATTACHMENT_FUNCTION_1301.equals(attachment.getAttachmentFunction())){
	//				zcZBAttachments.add(attachment);
	//				zcZBMapList.add(attachmentMap);
	//			}
	//
    //            // 注册登记表附件
    //            if(CommonConstants.ATTACHMENT_FUNCTION_1305.equals(attachment.getAttachmentFunction())){
    //                djbAttachments.add(attachment);
    //                djbMapList.add(attachmentMap);
    //            }
	//			// 说明书
	//			if(CommonConstants.ATTACHMENT_FUNCTION_976.equals(attachment.getAttachmentFunction())){
	//				smsAttachments.add(attachment);
	//				smsMapList.add(attachmentMap);
	//			}
	//			// 生产企业卫生许可证
	//			if(CommonConstants.ATTACHMENT_FUNCTION_977.equals(attachment.getAttachmentFunction())){
	//				wsAttachments.add(attachment);
	//				wsMapList.add(attachmentMap);
	//			}
	//
	//			// 商标注册证
	//			if(CommonConstants.ATTACHMENT_FUNCTION_979.equals(attachment.getAttachmentFunction())){
	//				sbAttachments.add(attachment);
	//				sbMapList.add(attachmentMap);
	//			}
	//
	//			// 产品图片（单包装/大包装）
	//			if(CommonConstants.ATTACHMENT_FUNCTION_981.equals(attachment.getAttachmentFunction())){
	//				cpAttachments.add(attachment);
	//				cpMapList.add(attachmentMap);
	//			}
	//		}
	//		firstEngage.setZczMapList(zczMapList);
	//		firstEngage.setSmsMapList(smsMapList);
	//		firstEngage.setWsMapList(wsMapList);
	//		firstEngage.setSbMapList(sbMapList);
	//		firstEngage.setDjbMapList(djbMapList);
	//		firstEngage.setCpMapList(cpMapList);
	//		firstEngage.setZczyMapList(zczyMapList);
	//		//新增
    //        //注册证附件/备案凭证附件（贝）
	//		firstEngage.setZcZBMapList(zcZBMapList);
	//
	//		registrationNumber.setZczAttachments(zczAttachments);
	//		registrationNumber.setSmsAttachments(smsAttachments);
	//		registrationNumber.setWsAttachments(wsAttachments);
	//		registrationNumber.setSbAttachments(sbAttachments);
	//		registrationNumber.setDjbAttachments(djbAttachments);
	//		registrationNumber.setCpAttachments(cpAttachments);
	//		registrationNumber.setZczyAttachments(zczyAttachments);
	//
	//		//新增
    //        //注册证附件/备案凭证附件（贝）
	//		registrationNumber.setZcZBAttachments(zcZBAttachments);
	//	}
	//
	//	registrationNumber.setAttachments(attachments);
	//	// 企业信息
	//	ProductCompany productCompany = productCompanyMapper.selectByPrimaryKey(registrationNumber.getProductCompanyId());
	//	registrationNumber.setProductCompany(productCompany);
	//	firstEngage.setRegistration(registrationNumber);
	//
	//	return firstEngage;
	//}


	@Override
	public FirstEngage getFirstSearchDetail(Map<String, Object> paramMap, Integer firstEngageId) {
		// 查询首营信息
		FirstEngage firstEngage = firstEngageMapper.selectByPrimaryKey(firstEngageId);
		if (firstEngage == null) {
			logger.warn("根据主键查询首营信息失败, key=" + firstEngageId);
			return null;
		}

		// 设置存储条件其他信息
		setStorageConditionOthers(firstEngage);

		// 设置新国标分类信息
		setNewStandardCategory(firstEngage);

		// 设置旧国标分类信息
		setOldStandardCategory(firstEngage);

		// 获取并初始化注册证信息
		RegistrationNumber registrationNumber = registrationNumberMapper.selectByPrimaryKey(firstEngage.getRegistrationNumberId());
		initExpiryAndDateInfo(registrationNumber);

		// 获取所有附件
		paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
		List<Integer> attachmentFunctions = Arrays.asList(
				CommonConstants.ATTACHMENT_FUNCTION_975,
				CommonConstants.ATTACHMENT_FUNCTION_1010,
				CommonConstants.ATTACHMENT_FUNCTION_976,
				CommonConstants.ATTACHMENT_FUNCTION_977,
				CommonConstants.ATTACHMENT_FUNCTION_979,
				CommonConstants.ATTACHMENT_FUNCTION_981,
				CommonConstants.ATTACHMENT_FUNCTION_1307,
				CommonConstants.ATTACHMENT_FUNCTION_1306,
				CommonConstants.ATTACHMENT_FUNCTION_1305,
				CommonConstants.ATTACHMENT_FUNCTION_1301,
				CommonConstants.ATTACHMENT_FUNCTION_1302,
				CommonConstants.ATTACHMENT_FUNCTION_1303,
				CommonConstants.ATTACHMENT_FUNCTION_1304,
				CommonConstants.ATTACHMENT_FUNCTION_5608,
				CommonConstants.ATTACHMENT_FUNCTION_5609
		);
		paramMap.put("attachmentFunction", attachmentFunctions);
		paramMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
		List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);

		// 处理附件信息
		Map<String, List<Map<String, Object>>> attachmentMapLists = this.processAttachments(attachments);
		this.populateFirstEngageAttachmentInfo(firstEngage, attachmentMapLists);
		this.populateRegistrationAttachmentInfo(registrationNumber, attachments, attachmentMapLists);

		// 设置企业信息
		ProductCompany productCompany = productCompanyMapper.selectByPrimaryKey(registrationNumber.getProductCompanyId());
		registrationNumber.setProductCompany(productCompany);
		firstEngage.setRegistration(registrationNumber);

		return firstEngage;
	}

	/**
	 * 设置存储条件其他信息
	 */
	private void setStorageConditionOthers(FirstEngage firstEngage) {
		if (StringUtils.isNotEmpty(firstEngage.getStorageConditionOthers())) {
			firstEngage.setStorageConditionOthersArray(GoodsStorageConditionOthersEnum.covert2OthersCodes(firstEngage.getStorageConditionOthers()));
		}
	}

	/**
	 * 设置新国标分类信息
	 */
	private void setNewStandardCategory(FirstEngage firstEngage) {
		if (firstEngage.getNewStandardCategoryId() != null && firstEngage.getNewStandardCategoryId() > 0) {
			List<Map<String, Object>> mapList = standardCategoryMapper.getStandardCategoryStrMap(Collections.singletonList(firstEngage));
			if (CollectionUtils.isNotEmpty(mapList)) {
				firstEngage.setNewStandardCategoryName(mapList.get(0).get("categoryName").toString());
			}
		}
	}

	/**
	 * 设置旧国标分类信息
	 */
	private void setOldStandardCategory(FirstEngage firstEngage) {
		if (firstEngage.getOldStandardCategoryId() != null && firstEngage.getOldStandardCategoryId() > 0) {
			SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(firstEngage.getOldStandardCategoryId());
			firstEngage.setOldStandardCategoryName(sysOptionDefinition.getTitle());
		}
	}

	/**
	 * 初始化注册证的有效期和日期信息
	 */
	private void initExpiryAndDateInfo(RegistrationNumber registrationNumber) {
		initExpiryDate(registrationNumber);
		// 设置日期字符串，进行判空校验
		if (registrationNumber.getIssuingDate() != null && registrationNumber.getIssuingDate() > 0) {
			registrationNumber.setIssuingDateStr(DateUtil.convertString(registrationNumber.getIssuingDate(), DateUtil.DATE_FORMAT));
		}
		if (registrationNumber.getEffectiveDate() != null && registrationNumber.getEffectiveDate() > 0) {
			registrationNumber.setEffectiveDateStr(DateUtil.convertString(registrationNumber.getEffectiveDate(), DateUtil.DATE_FORMAT));
		}
		if (registrationNumber.getChangeDate() != null && registrationNumber.getChangeDate() > 0) {
			registrationNumber.setChangeDateStr(DateUtil.convertString(registrationNumber.getChangeDate(), DateUtil.DATE_FORMAT));
		}
		if (registrationNumber.getBcIssueDate() != null && registrationNumber.getBcIssueDate() > 0) {
			registrationNumber.setBcIssueDateStr(DateUtil.convertString(registrationNumber.getBcIssueDate(), DateUtil.DATE_FORMAT));
		}
	}

	/**
	 * 处理附件并分组
	 */
	private Map<String, List<Map<String, Object>>> processAttachments(List<Attachment> attachments) {
		Map<String, List<Map<String, Object>>> attachmentMapLists = new HashMap<>();

		attachments.forEach(attachment -> {
			String groupName = getAttachmentGroup(attachment.getAttachmentFunction());
			Map<String, Object> attachmentMap = createAttachmentMap(attachment);
			attachmentMapLists.computeIfAbsent(groupName, k -> new ArrayList<>()).add(attachmentMap);
		});

		return attachmentMapLists;
	}


	private static final Map<Integer, String> ATTACHMENT_GROUP_MAP = new HashMap<>();

	static {
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_975, "zczMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_1010, "zczyMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_976, "smsMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_977, "wsMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_979, "sbMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_981, "cpMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_1301, "zcZBMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_1305, "djbMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_5608, "labelMapList");
		ATTACHMENT_GROUP_MAP.put(CommonConstants.ATTACHMENT_FUNCTION_5609, "labelSourceMapList");

	}

	/**
	 * 获取附件组名
	 */
	private String getAttachmentGroup(Integer attachmentFunction) {
		return ATTACHMENT_GROUP_MAP.getOrDefault(attachmentFunction, "otherMapList");
	}

	/**
	 * 创建附件映射
	 */
	private Map<String, Object> createAttachmentMap(Attachment attachment) {
		Map<String, Object> attachmentMap = new HashMap<>();
		attachmentMap.put("message", "操作成功");
		attachmentMap.put("httpUrl", api_http + attachment.getDomain());
		String uri = attachment.getUri();
		if (EmptyUtils.isNotEmpty(uri)) {
			String[] uriArray = uri.split("/");
			String fileName = uriArray[uriArray.length - 1];
			String fileNameTemp = "/" + fileName;
			// 文件后缀
			String[] prefixArray = fileNameTemp.split("\\.");
			String prefix = prefixArray[prefixArray.length - 1];
			attachmentMap.put("fileName", fileName);
			if (!EmptyUtils.isEmpty(attachment.getName())) {
				attachmentMap.put("name", attachment.getName());
			}
			attachmentMap.put("filePath", uri);
			attachmentMap.put("prefix", prefix);
			attachmentMap.put("domain", attachment.getDomain());
			attachmentMap.put("suffix", attachment.getSuffix());
		}
		return attachmentMap;
	}

	/**
	 * 填充首营附件信息
	 */
	private void populateFirstEngageAttachmentInfo(FirstEngage firstEngage, Map<String, List<Map<String, Object>>> attachmentMapLists) {
		firstEngage.setZczMapList(attachmentMapLists.get("zczMapList"));
		firstEngage.setSmsMapList(attachmentMapLists.get("smsMapList"));
		firstEngage.setWsMapList(attachmentMapLists.get("wsMapList"));
		firstEngage.setSbMapList(attachmentMapLists.get("sbMapList"));
		firstEngage.setDjbMapList(attachmentMapLists.get("djbMapList"));
		firstEngage.setCpMapList(attachmentMapLists.get("cpMapList"));
		firstEngage.setZczyMapList(attachmentMapLists.get("zczyMapList"));
		firstEngage.setZcZBMapList(attachmentMapLists.get("zcZBMapList"));
		firstEngage.setLabelMapList(attachmentMapLists.get("labelMapList"));
		firstEngage.setLabelSourceMapList(attachmentMapLists.get("labelSourceMapList"));
	}

	/**
	 * 填充注册证附件信息
	 */
	private void populateRegistrationAttachmentInfo(RegistrationNumber registrationNumber, List<Attachment> attachments, Map<String, List<Map<String, Object>>> attachmentMapLists) {

		registrationNumber.setZczAttachments(extractAttachmentsByGroup(attachments, "zczMapList"));
		registrationNumber.setSmsAttachments(extractAttachmentsByGroup(attachments, "smsMapList"));
		registrationNumber.setWsAttachments(extractAttachmentsByGroup(attachments, "wsMapList"));
		registrationNumber.setSbAttachments(extractAttachmentsByGroup(attachments, "sbMapList"));
		registrationNumber.setDjbAttachments(extractAttachmentsByGroup(attachments, "djbMapList"));
		registrationNumber.setCpAttachments(extractAttachmentsByGroup(attachments, "cpMapList"));
		registrationNumber.setZczyAttachments(extractAttachmentsByGroup(attachments, "zczyMapList"));
		registrationNumber.setZcZBAttachments(extractAttachmentsByGroup(attachments, "zcZBMapList"));
		registrationNumber.setLabelAttachments(extractAttachmentsByGroup(attachments,"labelMapList"));
		registrationNumber.setLabelSourceAttachments(extractAttachmentsByGroup(attachments,"labelSourceMapList"));

		registrationNumber.setAttachments(attachments);
	}

	/**
	 * 根据组名提取附件
	 */
	private List<Attachment> extractAttachmentsByGroup(List<Attachment> attachments, String groupName) {
		return attachments.stream().filter(attachment -> groupName.equals(getAttachmentGroup(attachment.getAttachmentFunction()))).collect(Collectors.toList());
	}


	/**
	 * 保存注册证附件上传历史
	 */
	private void saveNumberAttchmentHistory(RegistrationNumber nowNumber,Integer userId){
		RegistrationNumber oldNumber=new RegistrationNumber();
		Integer id=nowNumber.getRegistrationNumberId();

		oldNumber.setRegistrationNumberId(id);
		getRegisterAttachment(oldNumber);
		Long addTime=System.currentTimeMillis();
		compareAndSave(nowNumber.getZczAttachments(),oldNumber.getZczAttachments(),id,userId,addTime);
		compareAndSave(nowNumber.getYzAttachments(),oldNumber.getYzAttachments(),id,userId,addTime);
		compareAndSave(nowNumber.getScAttachments(),oldNumber.getScAttachments(),id,userId,addTime);
		compareAndSave(nowNumber.getDjbAttachments(),oldNumber.getDjbAttachments(),id,userId,addTime);
		compareAndSave(nowNumber.getSmsAttachments(),oldNumber.getSmsAttachments(),id,userId,addTime);
	}

	/**
	 * 比对注册证附件前后变化
	 */
	private void compareAndSave(List<Attachment> now,List<Attachment> pre,Integer registrationNumberId,Integer userId,Long addTime){
		if(isAttachmentChange(now,pre)){
			saveAttchmentHistory(pre,registrationNumberId,userId,addTime);
		}
	}

	/**
	 *保存注册证历史附件信息
	 */
	private void saveAttchmentHistory(List<Attachment> attachments,Integer registrationNumberId,Integer userId,Long addTime){
		if(CollectionUtils.isEmpty(attachments)){
			return;
		}
		List<RegistrationAttachmentHistory> list=new ArrayList<>();
		attachments.forEach(item->{
			RegistrationAttachmentHistory h=new RegistrationAttachmentHistory();
			BeanUtils.copyProperties(item,h);
			h.setAddTime(addTime);
			h.setCreator(userId);
			h.setRegistrationNumberId(registrationNumberId);
			h.setAttachmentType(item.getAttachmentFunction());
			list.add(h);
		});
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		list.forEach(e->{
			registrationAttachmentHistoryMapper.insertSelective(e);
		});
	}
	/**
	 * 获取注册证图片
	 */
	private void getRegisterAttachment(RegistrationNumber registrationNumber){
		Map<String, Object> paramMap =new HashMap<>();
		// 附件类型
		paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
		List<Integer> attachmentFunction = new ArrayList<>();
		// 注册证附件/备案凭证附件
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
		// 说明书
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_976);
		// 营业执照
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1000);
		// 生产企业卫生许可证
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_977);
		// 生产企业生产许可证
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_978);
		// 商标注册证
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_979);
		// 注册登记表附件
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_980);
		// 产品图片（单包装/大包装）
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_981);
		paramMap.put("attachmentFunction", attachmentFunction);
		paramMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
		List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);
		// 注册证附件/备案凭证附件
		List<Attachment> zczAttachments = new ArrayList<>();

		// 营业执照
		List<Attachment> yzAttachments = new ArrayList<>();

		// 说明书
		List<Attachment> smsAttachments = new ArrayList<>();

		// 生产企业生产许可证
		List<Attachment> scAttachments = new ArrayList<>();

		// 注册登记表附件
		List<Attachment> djbAttachments = new ArrayList<>();

		if(CollectionUtils.isEmpty(attachments)){
			return;
		}
		attachments.forEach(item->{
			if(item==null){return;}
			// 将注册证分组
			if(CommonConstants.ATTACHMENT_FUNCTION_975.equals(item.getAttachmentFunction())){
				zczAttachments.add(item);

			}

			// 营业执照
			if(CommonConstants.ATTACHMENT_FUNCTION_1000.equals(item.getAttachmentFunction())){
				yzAttachments.add(item);
			}
			// 说明书
			if(CommonConstants.ATTACHMENT_FUNCTION_976.equals(item.getAttachmentFunction())){
				smsAttachments.add(item);
			}
			// 生产企业生产许可证
			if(CommonConstants.ATTACHMENT_FUNCTION_978.equals(item.getAttachmentFunction())){
				scAttachments.add(item);
			}

			// 注册登记表附件
			if(CommonConstants.ATTACHMENT_FUNCTION_980.equals(item.getAttachmentFunction())){
				djbAttachments.add(item);
			}
		});

		registrationNumber.setZczAttachments(zczAttachments);
		registrationNumber.setYzAttachments(yzAttachments);
		registrationNumber.setSmsAttachments(smsAttachments);
		registrationNumber.setScAttachments(scAttachments);
		registrationNumber.setDjbAttachments(djbAttachments);
	}

	/**
	 * 判断新url是否与原来的图片相同
	 */
	private boolean isAttachmentChange(List<Attachment> now,List<Attachment> pre){

		if(CollectionUtils.isEmpty(pre)){
			return false;
		}
		if(CollectionUtils.isEmpty(now)){
			return true;
		}
		if(now.size()!=pre.size()){
			return true;
		}
		for(Attachment n:now) {
			boolean isChange=true;
			for (Attachment att : pre) {
				if (n.getUri().equals(att.getUri())) {
					isChange=false;
				}
			}
			if(isChange){
				return isChange;
			}
		}
		return false;
	}
	/**
	 * <b>Description:</b>初始化注册证证书有效期<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/23
	 */
	private void initExpiryDate(RegistrationNumber rn){
		if(rn==null||rn.getRegistrationNumberId()==null){
			return;
		}
		List<GoodsRegisrationExpiryDate> dateList=goodsRegisrationExpiryDateMapper.getListByRegistrationNumberId(rn.getRegistrationNumberId());
		if(CollectionUtils.isEmpty(dateList)){
			return;
		}
		dateList.stream().forEach(item->{
			if(item==null||item.getType()==null){
				return;
			}
			//营业执照有效期
			if(CommonConstants.ATTACHMENT_FUNCTION_1000.equals(item.getType())){
				rn.setYzStartTimeStr(getDateStr(item.getStartTime()));
				rn.setYzEndTimeStr(getDateStr(item.getEndTime()));
			}//生产许可证有效期
			else if(CommonConstants.ATTACHMENT_FUNCTION_978.equals(item.getType())){
				rn.setScStartTimeStr(getDateStr(item.getStartTime()));
				rn.setScEndTimeStr(getDateStr(item.getEndTime()));
			}//登记表有效期
			else if(CommonConstants.ATTACHMENT_FUNCTION_980.equals(item.getType())){
				rn.setDjbStartTimeStr(getDateStr(item.getStartTime()));
				rn.setDjbEndTimeStr(getDateStr(item.getEndTime()));
			}
		});
	}

	/**
	 * <b>Description:</b>日期转换<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/23
	 */
	private String getDateStr(Long time){
		if(time==null){
			return null;
		}
		return DateUtil.convertString(time,DateUtil.DATE_FORMAT);
	}
	/**
	 * @description 根据名称查询新国标分类
	 * <AUTHOR>
	 * @param
	 * @date 2019/4/24
	 */
	@Override
	public List<Map<String, Object>> getNewStandardCategoryByName(Map<String, Object> paramMap) {

		List<StandardCategory> standardCategoryList = standardCategoryMapper.getNewStandardCategoryByName(paramMap);
		// 如果新国标分类不为空
		if(CollectionUtils.isNotEmpty(standardCategoryList)){
			List<Map<String, Object>> listMap = new ArrayList<>();
			int newStandSize = standardCategoryList.size();
			for (int i=0; i<newStandSize; i++){
				Map<String, Object> map = new HashMap<>();
				StandardCategory standardCategory = standardCategoryList.get(i);
				// 新国标名称
				map.put("label", standardCategory.getCategoryName());
				// 最小id
				map.put("value", standardCategory.getStandardCategoryId());
				listMap.add(map);
			}
			return listMap;
		}
		return null;
	}

	/**
	 * @description 新国标分类
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/21
	 */
	@Override
	public List<StandardCategory> getallStandard() {
		Map<String,Object> paramMap= Maps.newHashMap();
		paramMap.put("status","1");
		paramMap.put("parentId","0");
		return standardCategoryMapper.getNewStandardCategory(paramMap);
	}

	/**
	 * @description 所有公司
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/21
	 */
	@Override
	public List<Map<String,Object>>  getallcompany(String productCompanyName) {

		return productCompanyMapper.getallcompany(productCompanyName);
	}

	@Override
	public List<RegistrationNumber> getRefreshFirstList(Map<String, Object> paramMap) {

		return registrationNumberMapper.getRefreshFirstList(paramMap);
	}

	/**
	 * @description 刷临效期状态
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/28
	 */
	@Override
	public void refreshFirstList(List<RegistrationNumber> list) {
		Map<String, Object> param = new HashMap<>();
		param.put("list", list);
		registrationNumberMapper.refreshFirstList(param);
	}


	@Override
	public RegistrationNumber getRegistrationNumberByFirstEngageId(Integer firstEngageId) {
		if (firstEngageId == null || firstEngageId <= 0) {
			return null;
		}
		return registrationNumberMapper.getRegistrationNumberByFirstEngageId(firstEngageId);
	}

	@Override
	public boolean associateWithRegistrationCert(Integer firstEngageId) {
		if (firstEngageId == null || firstEngageId <= 0) {
			return false;
		}
		return getRegistrationNumberByFirstEngageId(firstEngageId) != null;
	}

	@Override
	public RegistrationNumber getRegisterNumberFromTyResDTO(ResProductInfoDto dto) {
		if(StringUtil.isBlank(dto.getProductNum())){
			return null;
		}
		RegistrationNumber registrationNumber=new RegistrationNumber();
		registrationNumber.setRegistrationNumber(dto.getProductNum());
		registrationNumber.setCategory(getRegisterNumberCategory(dto.getProductNum()));
		registrationNumber.setProductionAddress(dto.getFactoryAddress());
		registrationNumber.setRegisteredAgent(dto.getAgentName());
		registrationNumber.setRegisteredAgentAddress(dto.getAgentAddress());
		registrationNumber.setRegisteredAgentAddress(dto.getAgentAddress());
		registrationNumber.setProductChineseName(dto.getProductName());
		registrationNumber.setManageCategoryLevel(getManagelevel(dto.getManagerCategory()));
		registrationNumber.setModel(dto.getProductModel());
		registrationNumber.setProPerfStruAndComp(dto.getProductStructure());
		registrationNumber.setProductUseRange(dto.getProductUseRange());
		registrationNumber.setAttachment(dto.getAttachFile());
		registrationNumber.setStorageAndExpiryDate(dto.getStorageCondition());
		registrationNumber.setOtherContents(dto.getOtherContent());
		registrationNumber.setRemarks(dto.getRemark());
		registrationNumber.setApprovalDepartment(StringUtil.isBlank(dto.getApprovalDepartment())?dto.getRecordDepartment():dto.getApprovalDepartment());
		String issueDateStr=null;
		if(StringUtil.isNotBlank(dto.getRecordDate())){
			issueDateStr=dto.getRecordDate();
		}else if(StringUtil.isNotBlank(dto.getApprovalDate())){
			issueDateStr=dto.getApprovalDate();
		}
		try {
			registrationNumber.setIssuingDate(StringUtil.isBlank(issueDateStr) ? 0 : DateUtil.convertLong(dto.getRecordDate(), DateUtil.DATE_FORMAT));
			registrationNumber.setEffectiveDate(StringUtil.isBlank(dto.getValidDate()) ? 0 : DateUtil.convertLong(dto.getValidDate(), DateUtil.DATE_FORMAT));
		}catch (Exception ex){
			logger.error("同步天眼查，时间日期格式错误",ex);
		}
		registrationNumber.setChangeContents(dto.getChanges());
		ProductCompany productCompany=new ProductCompany();
		productCompany.setProductCompanyChineseName(dto.getPeopleName());
		productCompany.setProductCompanyAddress(dto.getProductAddress());
		registrationNumber.setProductCompany(productCompany);
		return registrationNumber;
	}

	/**
	 * <b>Description:</b>根据名称获取注册证管理类别<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/27
	 */
	private Integer getManagelevel(String level){
		if("第一类".equals(level)){
			return 968;
		}else if("第二类".equals(level)){
			return 969;
		}else if("第三类".equals(level)){
			return 970;
		}
		return 0;
	}

	/**
	 * <b>Description:</b>根据注册证号获取注册证类别<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/27
	 */
	private Integer getRegisterNumberCategory(String num){
		Integer a =num.indexOf("备");
		Integer b =num.indexOf("国");
		Integer c =num.indexOf("准");
		Integer d =num.indexOf("进");
		Integer f =num.indexOf("许");
		if(a>-1){
			if(b>-1){
				return 1;
			}
			return 3;
		}
		if(c>-1){
			if(d>-1||f>-1){
				return 2;
			}
			return 0;
		}
		return 0;
	}
	private void updateRegister(){

		// 爬虫注册证数据
		List<RegistrationNumber> list = registrationNumberMapper.getPCInfo();

		// 批量插入
		int size = list.size();
		int toIndex = INSERT_NUM;
		for (int i = 0; i < size; i += INSERT_NUM){
			if(i + INSERT_NUM > size){
				toIndex = size - i;
			}
			List<RegistrationNumber> list2 = list.subList(i, i + toIndex);
			// 生产企业
			productCompanyMapper.insertList(list2);
			// 插入操作
			registrationNumberMapper.insertList(list2);
		}
	}


	// 同步老数据
	private void synchronizationGoods(){
		List<RegistrationNumber> list = registrationNumberMapper.getHisRegFromGoods();
	}

	@Override
	public ResultInfo<ResProductInfoDto> getTyRegisterCertificate(String registerNumberNo) {
		if(StringUtil.isBlank(registerNumberNo)){
			return ResultInfo.error("请先输入注册证号/备案凭证号");
		}
		String url=tyAliUrl+"/tianyan/getProductByQueryNum";
		ReqProductDto param=new ReqProductDto();
		param.setQueryNum(registerNumberNo);
		com.alibaba.fastjson.TypeReference<RestfulResult<ResProductInfoDto>> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult<ResProductInfoDto>>() {
		};
		RestfulResult<ResProductInfoDto> res = HttpRestClientUtil.restPost(url, typeReference, null, param);
		if(res==null||!res.isSuccess()){
			return ResultInfo.error("从天眼系统同步信息失败");
		}
		if(res.getData()==null){
			return ResultInfo.error("未查询到该注册证");
		}
		return ResultInfo.success(res.getData());
	}

	@Override
	public List<AttachmentHistoryLine> getNumberAttachmentHistoryLine(Integer registerNumberId,Integer isSubContract) {
		List<RegistrationAttachmentHistory> historyList=registrationAttachmentHistoryMapper.getListByRegistrationNumberId(registerNumberId);
		List<AttachmentHistoryLine> lineList=new ArrayList<>();
		if(CollectionUtils.isEmpty(historyList)){
			return lineList;
		}
		LinkedHashMap<Long,AttachmentHistoryLine> map=new LinkedHashMap<>();
		historyList.stream().forEach(item->{
			if(item==null||item.getAddTime()==null){
				return;
			}
			AttachmentHistoryLine line=map.get(item.getAddTime());
			if(line==null){
				line=new AttachmentHistoryLine();
				line.setAddTime(item.getAddTime());
				line.setTitle(new HashSet<>());
				line.setAttachments(new ArrayList<>());
				User user=userService.getUserById(item.getCreator());
				line.setUserName(user.getUsername()+"("+user.getRealName()+")");
				map.put(item.getAddTime(),line);
			}
			line.getTitle().add(getTitle(item.getAttachmentType(),isSubContract));
			line.getAttachments().add(item);
		});
		if(map.size()==0){
			return lineList;
		}
		Iterator it = map.entrySet().iterator();
		while (it.hasNext()) {
			Map.Entry entry = (Map.Entry) it.next();
			AttachmentHistoryLine line=(AttachmentHistoryLine)entry.getValue();
			;
			line.setTitleStr(StringUtils.join(line.getTitle().toArray(),"、"));
			lineList.add(line);
		}
		return lineList;
	}

	/**
	 * <b>Description:</b>根据附件类型获取附件名称<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/25
	 */
	private String getTitle(Integer type,Integer isSubcontract){
		int index=-1;
		if(isSubcontract==null){
			isSubcontract=0;
		}
		for(int i=0;i<CommonConstants.REGISTER_ATTACHMENT_TYPE.length;i++){
			if(CommonConstants.REGISTER_ATTACHMENT_TYPE[i].equals(type)){
				index=i;
				break;
			}
		}
		if(index==-1){
			return null;
		}
		if(isSubcontract==0){
			return CommonConstants.REGISTER_NUMBER_TITLE[index];
		}else{
			return CommonConstants.REGISTER_NUMBER_TITLE_ONE[index];
		}
	}

	/**
	 * 添加商品首营信息
	 */
	@Transactional
	@Override
	public int addZCZJB(FirstEngage firstEngage, User sessUser) {

		RegistrationNumber registrationNumber = firstEngage.getRegistration();
		// 判断注册证信息是否为空
		if(null == registrationNumber){
			return 0;
		}

		List<Integer> attachmentFunction = new ArrayList<>();//存的是贝登公章（贝） 1301
		//保存证书有效期
		saveRegistrationExpiryDate(registrationNumber);
		// 注册证
		List<Attachment> attachmentsList = new ArrayList<>();
		//要插入的照片类型
		attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_1301);
		// 1.先删除之前的附件信息
		Map<String, Object> attachmentMap = new HashMap<>();
		// 参数：注册证id
		attachmentMap.put("registrationNumberId", registrationNumber.getRegistrationNumberId());
		attachmentMap.put("manufacturerId", registrationNumber.getRegistrationNumberId()); //为了避免冲突 这里用了两个值来接收同一个id
		// 首营附件信息模块
		attachmentMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
		attachmentMap.put("attachmentFunction", attachmentFunction);
		// 删除存在的加盖贝登公章 目前就只有 注册证附件
		//将 附件表中的 注册证附件/备案凭证附件 （贝） 状态更新为失效
		//将之前的附件状态更新为  1301
		attachmentMapper.updataByParamNew(attachmentMap);

		// 注册证附件/备案凭证附件（贝）
		if(CollectionUtils.isNotEmpty(registrationNumber.getZcZBAttachments())){
			attachmentsList.addAll(registrationNumber.getZcZBAttachments());
		}
		int  num =0;
		// 2.再添加新增的附件信息
		if(CollectionUtils.isNotEmpty(attachmentsList)){
			// 添加人
			attachmentMap.put("userId", sessUser.getUserId());
			//
			attachmentMap.put("attachmentsList", attachmentsList);
//			Integer delAttachmentRes = attachmentMapper.insertAttachmentList(attachmentMap);

			for (Attachment attachment : attachmentsList) {
				attachment.setCreator(sessUser.getUserId());
				attachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_974);
				attachment.setRelatedId(registrationNumber.getRegistrationNumberId());
				attachment.setIsDeleted(0);
				num =attachmentMapper.insertSelective(attachment);
			}
			if(num < 0){
				return 0;
			}
		}
		return num;

	}

    @Override
	public List<RegistrationNumber> getAllManufacturer(){
		List<RegistrationNumber> list = null;
		try {

			String ALL_MANUFACTURER = dbType + ErpConst.ALL_MANUFACTURER;
			if (JedisUtils.exists(ALL_MANUFACTURER)) {
				String json = JedisUtils.get(ALL_MANUFACTURER);
//				JSONArray jsonArray = JSONArray.fromObject(json);
//				list = (List<RegistrationNumber>) JSONArray.toCollection(jsonArray, RegistrationNumber.class);
				list = JSONArray.parseArray(json, RegistrationNumber.class);
			} else {
//				list = registrationNumberMapper.selectManufacturer();
//
//				JedisUtils.set(ALL_MANUFACTURER, JsonUtils.convertConllectionToJsonStr(list), 600);
				list = registrationNumberMapper.selectManufacturer(); //20240203 切换成fastjson
				JedisUtils.set(ALL_MANUFACTURER, JSONArray.toJSONString(list), 600);
			}
			return list;
		}catch (Exception e){
			logger.error("",e);
			//redis异常
			return registrationNumberMapper.selectManufacturer();
		}
	}


	@Override
	public List<RegistrationNumber> queryByName(String name) {

		return registrationNumberMapper.queryByName(name);
	}

	@Override
	public String selectBymanufacturerId(Integer manufacturerId) {
		return registrationNumberMapper.selectBymanufacturerId(manufacturerId);
	}
}
