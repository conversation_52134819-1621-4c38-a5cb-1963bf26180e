// 删除
function deleteRow(row){
    $(row).parent().parent().remove();
}

$(function () {
    $("[name=submit]").click(function () {
        checkLogin();
        warnTips("goodsError","");

        var scrapType = $("#scrapType").val();
        if(scrapType == '' ||  scrapType == null){
            layer.alert("报废品分类不能为空");
            return false;
        }
        var scrapLevel = $("#scrapLevel").val();
        if( scrapLevel == '' || scrapLevel == null){
            layer.alert("报废品级别不能为空");
            return false;
        }
        //已去除
        // var scrapDealType = $("#scrapDealType").val();
        // if(scrapDealType == '' || scrapDealType == null){
        //     layer.alert("报废处理方式不能为空");
        //     return false;
        // }
        var orgId = $("#orgId").val();
        if ( orgId == "") {
            layer.alert("申请部门不能为空");
            return false;
        }
        if ($("#userId").val() == "") {
            layer.alert("申请人不能为空");
            return false;
        }

        var trs = $('#thisTimeUpdateTbody tr');

        if(trs.length == 0){
            layer.alert("已选出库产品不能为空");
            return false;
        }

        if($("#appleOutDate").val() == ''){
            layer.alert("申请出库日期不能为空");
            return false;
        }
        if($("#remark").val().length > 200){
            layer.alert("备注信息，最多支持200汉字");
            return false;
        }

        var skuNos = [];
        var re = /^[0-9]+$/;
        for(var i = 0;i < trs.length;i++){
            var skuNo = $(trs[i]).find('td').eq(0).find('input').eq(0).val();
            var outputNum = $(trs[i]).find('td').eq(6).find('input').eq(0).val();
            var id = "#stockNum_"+skuNo;
            var stockNum = $(id).html();
            if(skuNos.includes(skuNo)){
                layer.alert("订货号:"+skuNo+"已添加，不可以添加重复数据!");
                return false;
            }
            if(outputNum == ''){
                layer.alert("订货号:"+skuNo+"出库数量不能为空");
                return false;
            }
            if(!re.test(outputNum)){
                layer.alert("订货号:"+skuNo+"出库数量必须为正整数");
                return false;
            }
            if(parseInt(outputNum) == 0){
                layer.alert("订货号:"+skuNo+"出库数量必须大于0");
                return false;
            }
            if(parseInt(outputNum) > parseInt(stockNum)){
                warnTips("goodsError",skuNo+" 出库数量大于库存量，请重新填写！");
                return false;
            }
            skuNos.push(skuNo);
        }
    })
})

//给列表赋值
function setGoodsData(skuNo,showName,brandName,model,unitName,stockNum) {

    var trs = $('#thisTimeUpdateTbody tr');
    var skuNos = [];
    for(var i = 0;i < trs.length;i++){
        var skuNo1 = $(trs[i]).find('td').eq(0).find('input').eq(0).val();
        skuNos.push(skuNo1);
    }
    closeTab();
    if(skuNos.includes(skuNo)){
        debugger;
        var id = "#stock_"+skuNo;
        $(id).attr("value",'');
        $(id).val("");
        return;
    }
    var tbody = $("#thisTimeUpdateTbody");
    var tr = $("<tr>\n" +
        "     <td >\n" +
        "     " +skuNo+ "\n" +
        "       <input type=\"hidden\" name=\"skuNo\" value=\""+skuNo+"\">\n" +
        "     </td>\n" +
        "     <td>"+showName+"</td>\n" +
        "     <td>"+brandName+"</td>\n" +
        "     <td>"+model+"</td>\n" +
        "     <td>"+unitName+"</td>\n" +
        "     <td id=\"stockNum_"+skuNo+"\">"+stockNum+"</td>\n" +
        "     <td><input style='width: 120px' type=\"text\" class=\"input-middle\" id=\"stock_"+skuNo+"\" name=\"outputNum\" value="+stockNum+"></td>\n" +
        "     <td><a href=\"#\" onclick=\"deleteRow(this)\">删除</a></td>\n" +
        "     </tr>");
    tbody.append(tr);
}

function closeTab() {
    layer.closeAll();
}

function changespan() {
    $("#orgtext").html("申请部门");
    $("#usertext").html("申请人");
}