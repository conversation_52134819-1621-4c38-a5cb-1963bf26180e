package com.vedeng.erp.saleorder.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
    * 订单复制和复购关系表
    */
@Data
public class SaleorderCopyRecordEntity extends BaseEntity {
    /**
    * 主键
    */
    private Long saleorderCopyRecordId;

    /**
    * 销售单ID
    */
    private Integer saleorderId;

    /**
    * 来源ID
    */
    private Integer fromSaleorderId;

    /**
    * 渠道：1-复制订单 2-键复购
    */
    private Integer channel;

    /**
    * 删除 1是 0否
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}
