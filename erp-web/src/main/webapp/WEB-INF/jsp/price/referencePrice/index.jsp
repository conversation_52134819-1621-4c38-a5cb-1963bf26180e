<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="参考价格维护" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<div class="main-container">
	<div class="list-pages-search">
		<form method="post" id="search" action="<%=basePath%>price/referencePrice/index.do">
			<ul>
				<li>
					<label class="infor_name">关键词</label>
					<input type="text" class="input-middle" placeholder="请输入订货号/商品名称" name="priceKeyWord" id="priceKeyWord" value="${queryDto.priceKeyWord}"/>
				</li>

				<li>
					<label class="infor_name">归属产品经理</label>
					<select class="input-middle f_left" name="productManagerName">
						<option value="" >全部</option>
						<c:forEach var="manage" items="${managerUserList}">
							<option value="${manage.username}" <c:if test="${queryDto.productManagerName == manage.username}">selected="selected"</c:if> >${manage.username}</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">归属产品助理</label>
					<select class="input-middle f_left" name="productAssistantName">
						<option value="">全部</option>
						<c:forEach var="ass" items="${assUserList}">
							<option value="${ass.username}" <c:if test="${queryDto.productAssistantName == ass.username}">selected="selected"</c:if> >${ass.username}</option>
						</c:forEach>
					</select>
				</li>

				<li>
					<label class="infor_name">是否已核价</label>
					<select class="input-middle f_left" name="alreadyPriced">
						<option value="">全部</option>
						<option value="0" <c:if test="${queryDto.alreadyPriced == '0'}">selected="selected"</c:if>>未核价</option>
						<option value="1" <c:if test="${queryDto.alreadyPriced == '1'}">selected="selected"</c:if>>已核价</option>
					</select>
				</li>

			</ul>
			<div class="tcenter">

				<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>

				<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>

				<button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
						layerparams='{"width":"500px","height":"200px","title":"批量核价","link":"./batchUploadPage.do"}'>批量核价</button>
			</div>
		</form>
	</div>
	<div class="normal-list-page">
		<table class="table table-bordered table-striped table-condensed table-centered">
			<thead>
			<tr>
				<th class="wid6">序号</th>
				<th class="wid6">订货号</th>
				<th class="wid6">商品名称</th>
				<th class="wid6">单位</th>
				<th class="wid6">产品归属</th>
				<th class="wid6">终端参考价</th>
				<th class="wid6">经销参考价</th>
				<th class="wid6">集团参考价</th>
				<th class="wid6">更新时间</th>
				<th class="wid10">操作</th>
			</tr>
			</thead>
			<tbody>
			<c:forEach var="referencePrice" items="${referencePriceList}"  varStatus="num">
				<tr>
					<td>${num.count}</td>
					<td>${referencePrice.skuNo}</td>
					<td>
						<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${priceChangeApply.skuNo}","link":"./goods/goods/viewbaseinfo.do?goodsId=${fn:substringAfter(referencePrice.skuNo,'V')}","title":"产品信息"}'>${referencePrice.skuName}
						</a>
					</td>
					<td>${referencePrice.unitName}</td>
					<td>
						<c:choose>
							<c:when test="${referencePrice.belongProductManager == '' && referencePrice.belongProductAssit == ''}">
								-
							</c:when>
							<c:otherwise>
								${referencePrice.belongProductManager}&${referencePrice.belongProductAssit}
							</c:otherwise>
						</c:choose>
					</td>

					<td>


						<c:choose>
							<c:when test="${referencePrice.referenceTerminalPrice == null}">
								-
							</c:when>
							<c:otherwise>
								${myfn:toString(referencePrice.referenceTerminalPrice)}
							</c:otherwise>
						</c:choose>
					</td>
					<td>
						<c:choose>
							<c:when test="${referencePrice.referenceDistributionPrice == null}">
								-
							</c:when>
							<c:otherwise>
								${myfn:toString(referencePrice.referenceDistributionPrice)}
							</c:otherwise>
						</c:choose>
					</td>
					<td>
						<c:choose>
							<c:when test="${referencePrice.referenceGroupPrice == null}">
								-
							</c:when>
							<c:otherwise>
								${myfn:toString(referencePrice.referenceGroupPrice)}
							</c:otherwise>
						</c:choose>
					</td>

					<td>
						<c:choose>
							<c:when test="${referencePrice.modTime == null}">
								-
							</c:when>
							<c:otherwise>
								${referencePrice.modTime}
							</c:otherwise>
						</c:choose>
					</td>

					<td>
						<span class="edit-user" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./price/referencePrice/toEdit.do?skuNo=${referencePrice.skuNo}&referencePriceId=${referencePrice.id}","title":"编辑"}' onclick="toEdit('${referencePrice.skuNo}',this)">编辑</span>

						<span class="edit-user" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./price/referencePrice/detail.do?skuNo=${referencePrice.skuNo}&referencePriceId=${referencePrice.id}","title":"详情"}' onclick="toDetail('${referencePrice.id}',this)">详情</span>
					</td>
				</tr>
			</c:forEach>
			<c:if test="${empty referencePriceList}">
				<tr>
					<td colspan='9'>查询无结果！请尝试使用其它搜索条件。</td>
				</tr>
			</c:if>
			</tbody>
		</table>
	</div>
	<tags:page page="${page}" />
</div>

<script type="text/javascript">

	function toDetail(referencePriceId,span) {
		$.ajax({
			async:true,
			url:page_url+'/price/basePriceMaintain/toDetailValidator.do',
			data:{"skuPriceChangeApplyId":referencePriceId},
			type:"POST",
			dataType : "json",
			success:function(data){
				if(data.code==0){
					openTab(span);
				}else{
					layer.alert(data.message)
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	}

	function toEdit(skuNo,span){

		$.ajax({
			async:true,
			url:page_url+'/price/referencePrice/toEditValidator.do',
			data:{"skuNo":skuNo},
			type:"POST",
			dataType : "json",
			success:function(data){
				if(data.code==0){
					openTab(span);
				}else{
					layer.alert(data.message)
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	}

	function openTab(span){
		var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
		var newPageId;
		var tabTitle = $(span).attr('tabTitle');
		if (typeof(tabTitle) == 'undefined') {
			alert('参数错误');
		} else {
			tabTitle = $.parseJSON(tabTitle);
		}
		var id = tabTitle.num;
		// var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
		var name = tabTitle.title;
		var uri = tabTitle.link;
		var closable = 1;
		var item = { 'id': id, 'name': name, 'url': uri, 'closable': closable == 1 ? true : false };
		self.parent.closableTab.addTab(item);
		self.parent.closableTab.resizeMove();
		$(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
	}

	
</script>

<script type="text/javascript"
		src="${pageContext.request.contextPath}/static/js/price/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
