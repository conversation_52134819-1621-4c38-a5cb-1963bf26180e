package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.batch.common.exception.BatchException;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.domain.command.KingDeeSalesVatSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.KingDeeRedInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSalesVatPlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSalesVatPlainInvoiceConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSalesVatSpecialInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSalesVatSpecialInvoiceConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSalesVatPlainInvoiceMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSalesVatSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatPlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatSpecialInvoiceApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售负向发票推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchSaleOrderAfterSaleInvoiceWriter extends BaseWriter<KingDeeRedInvoiceDto> {

    @Autowired
    private KingDeeSalesVatPlainInvoiceApiService kingDeeSalesVatPlainInvoiceApiService;

    @Autowired
    private KingDeeSalesVatSpecialInvoiceApiService kingDeeSalesVatSpecialInvoiceApiService;

    @Override
    public void doWrite(KingDeeRedInvoiceDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售红票：{}", JSON.toJSONString(item));
        KingDeeSalesVatSpecialInvoiceDto kingDeeSalesVatSpecialInvoiceDto = item.getKingDeeSalesVatSpecialInvoiceDto();
        KingDeeSalesVatPlainInvoiceDto kingDeeSalesVatPlainInvoiceDto = item.getKingDeeSalesVatPlainInvoiceDto();
        this.pushVatSpecialInvoice(kingDeeSalesVatSpecialInvoiceDto);
        this.pushVatPlainInvoice(kingDeeSalesVatPlainInvoiceDto);
    }

    private void pushVatPlainInvoice(KingDeeSalesVatPlainInvoiceDto kingDeeSalesVatPlainInvoiceDto) {
        if (kingDeeSalesVatPlainInvoiceDto != null) {
            kingDeeSalesVatPlainInvoiceDto.setKingDeeBizEnums(KingDeeBizEnums.saveSalesVatPlainInvoice);
            kingDeeSalesVatPlainInvoiceApiService.register(kingDeeSalesVatPlainInvoiceDto, true);
        }
    }

    private void pushVatSpecialInvoice(KingDeeSalesVatSpecialInvoiceDto kingDeeSalesVatSpecialInvoiceDto) {
        if (kingDeeSalesVatSpecialInvoiceDto != null) {
            kingDeeSalesVatSpecialInvoiceDto.setKingDeeBizEnums(KingDeeBizEnums.saveCommonSpecialInvoice);
            kingDeeSalesVatSpecialInvoiceApiService.register(kingDeeSalesVatSpecialInvoiceDto, true);
        }
    }
}
