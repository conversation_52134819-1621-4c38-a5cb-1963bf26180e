package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.dto.KingDeeWorkflowDto;
import com.vedeng.erp.kingdee.service.KingDeeWorkflowApiService;
import com.vedeng.erp.kingdee.service.KingDeeWorkflowService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.WorkflowAuditExtCommand;
import com.vedeng.infrastructure.kingdee.service.impl.KingDeeMqBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;

@Service
@Slf4j
public class KingDeeWorkflowServiceImpl extends KingDeeMqBaseServiceImpl<KingDeeWorkflowDto> implements KingDeeWorkflowService, KingDeeWorkflowApiService {

    @Value("${kingdee.userName}")
    private String userName;

    @Override
    public void submit(KingDeeWorkflowDto dto) {
        OperateExtCommand operateExtCommand =
                new OperateExtCommand(dto.getFormId(),
                        "",
                        KingDeeConstant.ORG_ID.toString(),
                        Arrays.asList(dto.getPayApplyId()));

        log.info("调用付款单提交接口入参：{}", JSONObject.toJSONString(operateExtCommand));
        ArrayList<SuccessEntity> successSubmit = kingDeeBaseApi.submit(operateExtCommand);
        log.info("调用付款单提交接口出参：{}", JSONObject.toJSONString(successSubmit));
        if (CollUtil.isNotEmpty(successSubmit)) {
            log.info("调用付款单提交审核成功");
        } else {
            log.info("调用付款单提交审核失败");
            throw new KingDeeException("调用付款单提交审核失败");
        }
    }

    @Override
    public void audit(KingDeeWorkflowDto dto) {
        WorkflowAuditExtCommand workflowAuditExtCommand =
                new WorkflowAuditExtCommand(
                        dto.getFormId(),
                        Arrays.asList(dto.getPayApplyId()),
                        1,
                        "自动通过",userName);
        // 调用审批流接口
        log.info("调用审批流接口入参：{}", JSONObject.toJSONString(workflowAuditExtCommand));
        RepoStatus repoStatus = kingDeeBaseApi.workflowAudit(workflowAuditExtCommand);
        log.info("调用审批流接口出参：{}", JSONObject.toJSONString(repoStatus));
        if (repoStatus.isIsSuccess()) {
            ArrayList<SuccessEntity> successEntitys = repoStatus.getSuccessEntitys();
            if (CollUtil.isNotEmpty(successEntitys)) {
                log.info("审批流审核成功");
            } else {
                throw new KingDeeException(StrUtil.format("金蝶审核失败,errors:{}",JSON.toJSON(repoStatus.getErrors())));

            }
        }else {
            throw new KingDeeException(StrUtil.format("金蝶审核失败,errors:{}",JSON.toJSON(repoStatus.getErrors())));
        }
    }
}
