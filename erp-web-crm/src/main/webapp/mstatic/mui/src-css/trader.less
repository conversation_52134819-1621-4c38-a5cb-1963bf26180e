
/* Common */
.erp-loading {
    color: #666;
    height: 39px;
    line-height: 37px;
    padding: 0px 10px;
    overflow: hidden;
    i {
        animation: loading 1.8s linear  infinite;
        display: inline-block;
        position: relative;
        top: 1px;
        font-size: 16px;
        margin-right: 5px;
        color: #09F;
    }
}

.erp-load-fail {
    height: 39px;
    line-height: 39px;
    padding: 0px 10px;

    i {
        position: relative;
        top: 2px;
        font-size: 16px;
        color: #E64545;
        margin-right: 5px;
    }
}

.erp-load-empty {
    text-align: center;
    padding: 100px 0;

    > i {
        display: block;
        font-size: 32px;
        color: #09f;
        margin-bottom: 10px;
    }

    > img {
        width: 223px;
        height: 120px;
        margin-bottom: 14px;
    }
    > p {
        color: #999;
    }
}

/* 客户名称 */
.trader-show-wrap {
    padding-left: 10px;
    
    .trader-show {
        overflow: hidden;

        .input-value {
            font-size: 12px;
            color: #000;
            height: 38px;
            line-height: 38px;
            padding-right: 38px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .other-info {
            padding-right: 10px;
            margin-top: -5px;
            margin-bottom: 10px;

            .other-item {
                color: #999;
            }
        }
    }

    .trader-icon {
        width: 38px;
        height: 38px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 9;

        > .icon-tianyancha {
            font-size: 16px;
            color: #09f;
        }
        > .icon-search {
            font-size: 16px;
            color: #666;
        }
    }
}


/* 客户名称erp查询弹层 */
.trader-erp-panel {

    .erp-search-list {
        height: calc(100vh - 201px);
        height: calc(100vh - 201px - constant(safe-area-inset-bottom));
        height: calc(100vh - 201px - env(safe-area-inset-bottom));

        .search-related {
            height: 100%;

            .related-list {
                height: 100%;
                overflow-y: auto;
                border-bottom: solid 1px #EBEFF2;

                .local-data {
                    font-size: 12px;
                    color: #999;
                    line-height: 30px;
                    padding: 0 10px;
                }

                .related-item {
                    padding: 10px;
                    display: flex;
                    align-items: center;

                    .related-item-left {
                        flex: 1;
                        min-width: 0;
                        display: flex;
                        align-items: center;

                        .trader-select {
                            height: 16px;
                            flex-shrink: 0;
                            margin-right: 5px;

                            > i {
                                font-size: 16px;
                                color: #ccc;
                            }
                        }

                        .name {}

                        .icon {
                            font-size: 16px;
                            color: #0084FF;
                            margin-left: 5px;
                        }
                    }

                    .related-item-right {
                        width: 90px;
                        margin-left: 10px;
                        flex-shrink: 0;
                        color: #999;
                        text-align: right;
                    }

                    &.active {
                        background: #E0F3FF;

                        .related-item-left .trader-select > i {
                            color: #09f;
                        }    
                    }
                }
            }
        }
    }

    .erp-search-footer {

    }
}


/* 客户等级 */
.ui-trader-level-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    border-radius: 3px;

    @level: S, S1, S2, A1, A2, A3, B1, B2, B3, C1, C2, C3, D;
    each(@level, {
        &.level-@{value} {
            background: url('/mstatic/image/traderLevel/@{value}.svg') no-repeat;
            background-size: cover;
        }
    })
}



/* 天眼查列表 */
.tyc-list-panel {
    // max-height: calc(100vh - 45px - 50px);
    height: calc(100vh - 45px - 50px);

    .tyc-list-wrap {
        background: #F5F7FA;
        padding: 10px;
        // max-height: calc(100vh - 45px - 53px - 53px);
        height: calc(100vh - 45px - 53px - 53px);
        overflow-y: auto;

        .tyc-list {
            position: relative;
            overflow-y: auto;

            .tyc-item {
                background: #fff;
                border-radius: 5px;
                padding: 10px 10px 5px 0;
                display: flex;
                margin-bottom: 5px;

                &:last-child {
                    margin-bottom: 0;
                }

                .tyc-select {
                    width: 36px;
                    flex-shrink: 0;
                    font-size: 16px;
                    color: #ccc;
                    text-align: center;
                }

                &.active {
                    background: #E0F3FF;

                    .tyc-select {
                        color: #09f;
                    }
                }

                .item-right {
                    flex: 1;
                    min-width: 0;

                    .name {
                        font-size: 14px;
                        font-weight: 700;
                        margin-bottom: 5px;
                    }

                    .attrs {
                        display: flex;
                        flex-wrap: wrap;

                        .attr-item {
                            flex: 0 0 50%;
                            min-width: 0;
                            font-size: 12px;
                            font-weight: 400;
                            color: #000;
                            margin-bottom: 5px;

                            .label {
                                color: #aaa;
                            }
                        }
                    }
                }
            }
        }

    }
}


/* 天眼查详情 */
.tyc-detail-panel {
    max-height: calc(100vh - 45px - 53px);
    overflow-y: auto;

    .tyc-detail-top {
        background: #fff;
        padding: 10px 15px;

        .tyc-name {
            font-size: 14px;
            font-weight: 700;
        }

        .tyc-tags {
            font-size: 0;
            margin-top: 10px;
            margin-bottom: -5px;
            display: flex;
            flex-wrap: wrap;

            .tag {
                padding: 2px 5px;
                background: #E0F3FF;
                border-radius: 2px;
                font-size: 12px;
                color: #09f;
                margin-right: 5px;
                margin-bottom: 5px;
            }
        }
    }

    .tyc-detail-bottom {
        padding: 10px;
        background: #F5F7FA;

        .tyc-attr {
            background: #fff;
            border-radius: 5px;
            padding-right: 10px;
        }
    }
}





/* 详情·客户名称回显 */
.vd-ui-trader-name-wrap {

    .trader-name-wrap {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .vd-ui-trader-txt {
            flex: 1;
            min-width: 0;
            margin-right: 10px;
        }
    
        .vd-ui_icon {
            width: 32px;
            flex-shrink: 0;
            font-size: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
    
            &.icon-tyc {
                color: #0084FF;
                padding-left: 4px;
            }
    
            &.icon-baidu {
                color: #2932E1;
                padding-right: 4px;
            }
        }
    }
}



