package com.vedeng.erp.kingdee.enums;

/**
 * <AUTHOR>
 * @desc 付款单结算方式枚举
 */
public enum KingDeePayBillSettleTypeEnum {
    CASH_SETTLETYPE("JSFS01_SYS","现金"),
    CASH_CHEQUE_SETTLETYPE("JSFS02_SYS","现金支票"),
    TRANSFER_CHEQUE_SETTLETYPE("JSFS03_SYS","转账支票"),
    TELEGRAPHIC_SETTLETYPE("JSFS04_SYS","电汇"),
    MAIL_SETTLETYPER("JSFS05_SYS","信汇"),
    COMMERCIAL_BILL_SETTLETYPE("JSFS06_SYS","商业承兑汇票"),
    BANK_BILL_SETTLETYPE("JSFS07_SYS","银行承兑汇票"),
    BANK_BILL_SETTLETYPE_NEW("12","银行承兑汇票"),
    CREDIT_SETTLETYPE("JSFS08_SYS","信用证"),
    NOTES_SETTLETYPE("JSFS09_SYS","应收票据背书"),
    INSIDE_INTEREST_SETTLETYPE("JSFS10_SYS","内部利息结算"),
    RETURN_SETTLETYPE("JSFS12_SYS","票据退票"),
    CONCENTRATE_SETTLETYPE("JSFS21_SYS","集中结算"),
    SECURITY_SETTLETYPE("JSFS22_SYS","保证金转货款"),
    WECHAT_SETTLETYPE("JSFS31_SYS","微信"),
    ALIPAY_SETTLETYPE("JSFS32_SYS","支付宝"),
    SUPPLY_SETTLETYPE("JSFS36_SYS","供应链票据"),
    INSIDE_TURN_SETTLETYPE("JSFS41_SYS","内部转销");
    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    KingDeePayBillSettleTypeEnum(String code, String name){
        this.code = code;
        this.name = name;
    }
}
