package com.vedeng.erp.kingdee.batch.common.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.JobParameters;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: job监听器
 * @date 2022/10/21 12:41
 */
@Component
@Slf4j
public class JobListener implements JobExecutionListener {

    @Override
    public void beforeJob(JobExecution jobExecution) {
        log.info("任务开始执行.");
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        JobParameters jobParameters = jobExecution.getJobParameters();
        if (jobExecution.getStatus() == BatchStatus.FAILED) {
            log.error("任务执行失败{}",JSON.toJSONString(jobParameters));
            return;
        }
        if (jobExecution.getStatus() == BatchStatus.COMPLETED) {
            log.info("任务执行成功{}",JSON.toJSONString(jobParameters));
        }
    }
}
