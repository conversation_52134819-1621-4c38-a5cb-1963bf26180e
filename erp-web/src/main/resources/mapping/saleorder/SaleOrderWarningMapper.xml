<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.SaleOrderWarningMapper" >

	<select id="getHistorySaleOrderWarn" parameterType="com.vedeng.order.model.vo.SaleorderGoodsVo" resultType="com.vedeng.order.model.SaleOrderWarning">
		SELECT
			WARNING_ID,
			USER_ID,
			SALEORDER_ID,
			SALEORDER_GOODS_ID,
			AGING,
			WARN_TIME,
			WARN_LEVEL,
		    OVER_DUE
		FROM
			T_SALEORDER_WARNING T
		WHERE
			T.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
		  AND T.USER_ID = #{userId,jdbcType=INTEGER}
		  AND T.IS_DETELE = 0
	</select>

	<delete id="deleteSaleOrderWarning" parameterType="com.vedeng.order.model.vo.SaleorderGoodsVo">
		UPDATE T_SALEORDER_WARNING SET IS_DETELE = 1 WHERE SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
	</delete>

	<insert id="insertSaleOrderWarning" parameterType="com.vedeng.order.model.SaleOrderWarning">
		INSERT INTO T_SALEORDER_WARNING (
			USER_ID, SALEORDER_ID, SALEORDER_GOODS_ID, AGING, WARN_TIME, WARN_LEVEL, OVER_DUE, IS_DETELE
		) VALUES (
			#{userId,jdbcType=INTEGER},  #{saleorderId,jdbcType=INTEGER}, #{saleorderGoodsId,jdbcType=INTEGER}, #{aging,jdbcType=INTEGER}, #{warnTime,jdbcType=BIGINT}, #{warnLevel,jdbcType=INTEGER},#{overDue,jdbcType=INTEGER}, 0
	    )
	</insert>

	<select id="getOrderAssistant" parameterType="com.vedeng.order.model.vo.SaleorderGoodsVo" resultType="com.vedeng.authorization.model.User">
		SELECT
			TOAR.ORDER_ASSISTANT_USER_ID AS USER_ID,
			TU.USERNAME,
			YUD.EMAIL
		FROM
			T_ORDER_ASSISTANT_RELATION TOAR
				LEFT JOIN T_USER TU ON TU.USER_ID = TOAR.ORDER_ASSISTANT_USER_ID
				LEFT JOIN T_USER_DETAIL YUD ON TOAR.ORDER_ASSISTANT_USER_ID = YUD.USER_ID
		WHERE
			TOAR.IS_DELETE = 0
		  AND TOAR.PRODUCT_MANAGER_USER_ID = (
			SELECT
				T1.ASSIGNMENT_MANAGER_ID AS ID
			FROM
				V_CORE_SKU T
					LEFT JOIN V_CORE_SPU T1 ON T1.SPU_ID = T.SPU_ID
			WHERE
				T.SKU_ID = #{goodsId,jdbcType=INTEGER} )

		  AND TOAR.PRODUCT_ASSITANT_USER_ID = (
			SELECT
				T1.ASSIGNMENT_ASSISTANT_ID AS ID
			FROM
				V_CORE_SKU T
					LEFT JOIN V_CORE_SPU T1 ON T1.SPU_ID = T.SPU_ID
			WHERE
				T.SKU_ID = #{goodsId,jdbcType=INTEGER}  )
	</select>

	<update id="updateSaleOrderWarning" parameterType="com.vedeng.order.model.SaleOrderWarning">
		UPDATE T_SALEORDER_WARNING SET
		   AGING =  #{aging,jdbcType=INTEGER},
		   WARN_TIME =  #{warnTime,jdbcType=BIGINT},
		   WARN_LEVEL = #{warnLevel,jdbcType=INTEGER},
		   OVER_DUE = #{overDue,jdbcType=INTEGER}
		WHERE SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
		AND USER_ID = #{userId,jdbcType=INTEGER}
		AND SALEORDER_ID =  #{saleorderId,jdbcType=INTEGER}
		AND IS_DETELE = 0
	</update>

</mapper>

