<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.market.dao.MarketPlanTraderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.market.domain.entity.MarketPlanTrader">
    <!--@mbg.generated-->
    <!--@Table T_MARKETING_PLAN_TRADER-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="PLAN_ID" jdbcType="INTEGER" property="planId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="PROMOTION_PRIORITY" jdbcType="VARCHAR" property="promotionPriority" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="SEND_MSG" jdbcType="INTEGER" property="sendMsg" />
    <result column="IS_DEL" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PLAN_ID, TRADER_ID, TRADER_CUSTOMER_ID, PROMOTION_PRIORITY, CREATOR, ADD_TIME, 
    UPDATOR, MOD_TIME, SEND_MSG, IS_DEL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_MARKETING_PLAN_TRADER
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_MARKETING_PLAN_TRADER
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.market.domain.entity.MarketPlanTrader" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_MARKETING_PLAN_TRADER (PLAN_ID, TRADER_ID, TRADER_CUSTOMER_ID, 
      PROMOTION_PRIORITY, CREATOR, ADD_TIME, 
      UPDATOR, MOD_TIME, SEND_MSG, 
      IS_DEL)
    values (#{planId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, 
      #{promotionPriority,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updator,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{sendMsg,jdbcType=INTEGER}, 
      #{isDel,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.market.domain.entity.MarketPlanTrader" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_MARKETING_PLAN_TRADER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="planId != null">
        PLAN_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="promotionPriority != null">
        PROMOTION_PRIORITY,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="sendMsg != null">
        SEND_MSG,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="planId != null">
        #{planId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="promotionPriority != null">
        #{promotionPriority,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendMsg != null">
        #{sendMsg,jdbcType=INTEGER},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.market.domain.entity.MarketPlanTrader">
    <!--@mbg.generated-->
    update T_MARKETING_PLAN_TRADER
    <set>
      <if test="planId != null">
        PLAN_ID = #{planId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="promotionPriority != null">
        PROMOTION_PRIORITY = #{promotionPriority,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendMsg != null">
        SEND_MSG = #{sendMsg,jdbcType=INTEGER},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.market.domain.entity.MarketPlanTrader">
    <!--@mbg.generated-->
    update T_MARKETING_PLAN_TRADER
    set PLAN_ID = #{planId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      PROMOTION_PRIORITY = #{promotionPriority,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      SEND_MSG = #{sendMsg,jdbcType=INTEGER},
      IS_DEL = #{isDel,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <select id="findMarketPlanTrader" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_MARKETING_PLAN_TRADER
    where PLAN_ID = #{planId}
    AND TRADER_ID=#{traderId}
    AND TRADER_CUSTOMER_ID=#{traderCustomerId}
  </select>
</mapper>