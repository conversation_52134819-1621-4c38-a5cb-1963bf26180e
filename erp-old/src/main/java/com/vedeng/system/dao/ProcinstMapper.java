package com.vedeng.system.dao;

import java.util.List;
import java.util.Map;

public interface ProcinstMapper {

    List<String> getBuyOrderIdsByCurrentOperateUser(Map<String, Object> map);

    List<String> getBuyOrderIdsByProcessIds(Map<String, Object> processIds);

    List<Map<String,Object>> getActHiInfoByTaskId(Map<String,Object> map);

    int updateActHiActinstInfoByTaskId(Map<String,Object> map);

    int updateActHiTaskinstInfoById(Map<String,Object> map);
}
