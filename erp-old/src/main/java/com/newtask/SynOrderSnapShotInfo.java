package com.newtask;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.page.Page;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.SaleorderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description com.newtask
 * @Date 2021/9/9 16:04
 */

@JobHandler(value = "SynOrderSnapShotInfo")
@Component
public class SynOrderSnapShotInfo extends AbstractJobHandler {

    @Resource
    SaleorderService saleorderService;

    @Resource
    SaleorderMapper saleorderMapper;

    @Resource
    BuyorderService buyorderService;

    @Resource
    BuyorderMapper buyorderMapper;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if (StringUtils.isNotBlank(param)) {
            String[] params = param.split(",");
            Integer orderId = Integer.parseInt(params[0]);
            // 1 销售订单  2 采购订单
            String type = params[1];
            if ("1".equals(type)) {
                saleorderService.updateSaleorderGoodsSnapshotInfo(orderId);
            }
            if ("2".equals(type)) {
                buyorderService.updateBuyorderGoodsSnapshotInfo(orderId);
            }
        } else {
            //更新2021年的订单
            //查询并处理所有审核通过的销售订单
            dealSnapShotInfo(1);
            //查询并处理所有审核通过的采购订单
            dealSnapShotInfo(2);

        }

        return ReturnT.SUCCESS;
    }


    public void dealSnapShotInfo(Integer orderType) {
        int currentPage = ErpConst.ONE;
        int maxPage;
        Map<String, Object> pageParam = new HashMap<String, Object>();
        Page page = new Page(currentPage, 1000);
        if (1 == orderType) {
            do {
                page.setPageNo(currentPage);
                pageParam.put("page", page);
                List<Saleorder> saleorderList = saleorderMapper.getSaleorderListPage(pageParam);
                if (CollectionUtils.isNotEmpty(saleorderList)){
                    saleorderList.forEach(item->{
                        saleorderService.updateSaleorderGoodsSnapshotInfo(item.getSaleorderId());
                    });
                }
                maxPage = page.getTotalPage();
                currentPage++;

            } while (currentPage <= maxPage);
        }

        if (2 == orderType) {
            do {
                page.setPageNo(currentPage);
                pageParam.put("page", page);
                List<Buyorder> buyorderList = buyorderMapper.getSaleorderListPage(pageParam);
                maxPage = page.getTotalPage();
                currentPage++;
                if (CollectionUtils.isNotEmpty(buyorderList)){
                    buyorderList.forEach(item->{
                        saleorderService.updateSaleorderGoodsSnapshotInfo(item.getBuyorderId());
                    });
                }
            } while (currentPage <= maxPage);
        }

    }
}
