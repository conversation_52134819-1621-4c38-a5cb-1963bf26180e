<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>供应商余额</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="T4WtOK_Rme0" datasource="erp-reportdatasource" fixednumber="0" append_column_url="" append_row_url="" empty_show="" firstcol>
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">供应商名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">是否启用</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " name="IS_ENABLE" placeholder="" style="" alias="sup" jdbctype="" data="status" datatype="" oper=""></select>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
    <BUTTON item_name="导出所有" name="导出所有" url="/ezadmin/list/export-T4WtOK_Rme0" opentype="_BLANK_PARAM" windowname="" style type="table">导出所有</BUTTON>
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton"></th> 
      <th item_name="TRADER_ID" name="TRADER_ID" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">ID</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">供应商名称</th>
      <th item_name="AMOUNT" name="AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">余额</th>
      <th item_name="IS_ENABLE" name="IS_ENABLE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="" data="status" style="" head="18">是否启用</th>
     </tr>
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="" groupby="">
        SELECT sup.TRADER_SUPPLIER_ID,t.TRADER_NAME,sup.AMOUNT ,sup.IS_ENABLE
FROM T_TRADER_SUPPLIER sup LEFT JOIN T_TRADER t ON sup.TRADER_ID=t.TRADER_ID
where     t.CREATOR  not in (2,341,897,751,659,660)</pre> count: <pre id="count" class="layui-code"></pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>