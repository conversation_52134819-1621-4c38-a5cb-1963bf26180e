package com.newtask.celery;

import com.newtask.celery.model.CeleryQueueEnum;

import java.lang.annotation.*;

/**
 * @CeleryAsync 异步任务注解
 *
 * 1.如果在同一个类中调用，会变同步执行, 请将方法单独抽到另一个service
 * 2.使用该注解的方法返回值默认为null ,如果需要返回值进行处理，请使用 @Async(Future)
 *
 * @Author: Cherny.chen
 * @Create: 2021/7/9 12:46
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CeleryAsync {

    /**
     * queue name
     * 指定执行队列 参考 {@link CeleryQueueEnum}
     * @return
     */
    CeleryQueueEnum queue() default CeleryQueueEnum.DEFAULT;

    /**
     * 超时时间
     *
     * @return
     */
    int validMillis() default 0;

    /**
     * 是否幂等校验
     *
     * @return
     */
    boolean checkIdempotent() default false;

}
