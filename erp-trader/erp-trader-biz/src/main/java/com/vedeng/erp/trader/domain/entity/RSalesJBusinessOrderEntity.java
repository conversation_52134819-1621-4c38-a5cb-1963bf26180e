package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 销售人员和业务数据关系表
 */
@Getter
@Setter
public class RSalesJBusinessOrderEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 业务类型
     * 1.商机
     * 2.报价
     * 3.订单
     * 4.售后
     */
    private Integer businessType;

    /**
     * 业务ID
     */
    private Integer businessId;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 销售人员ID
     */
    private Integer saleUserId;

    /**
     * 销售人员名称
     */
    private String saleUserName;

    /**
     * 协作人标签
     * 1: 手动添加
     * 2: 线下销售
     * 3: 产线负责人
     */
    private Integer shareTag;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}