package com.vedeng.flash.service.warningtask;

import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.dto.EarlyWarningTicksDto;
import com.vedeng.flash.dto.FollowUpDto;
import com.vedeng.flash.model.EarlyWarningTask;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.List;

@Service
public abstract class AbstractWarningtaskService implements WarningtaskService {

    @Resource
    private EarlyWarningTaskMapper earlyWarningTaskMapper;


    /**
     * 创建预警
     */
    @Override
    public void create(EarlyWarningTaskDto earlyWarningTaskDto) {

        EarlyWarningTask earlyWarningTask = new EarlyWarningTask();

        customPropertySet(earlyWarningTask,earlyWarningTaskDto);

        earlyWarningTaskMapper.insert(earlyWarningTask);

        if(earlyWarningTaskDto.getNotice() == 1){
            sendMessageForWarningtaskCreate(earlyWarningTask);
        }
    }

    /**
     * 预警任务超过24小时
     */
    @Override
    public void warningtaskOver12Hour(Long earlyWarningTaskId) {

    }

    /**
     * 预警任务逾期
     */
    @Override
    public void warningtaskOverdue(Long earlyWarningTaskId){

    }

    /**
     * 关闭预警
     */
    @Override
    public void close(Long earlyWarningTaskId) {

        EarlyWarningTask earlyWarningTask = new EarlyWarningTask();
        earlyWarningTask.setEarlyWarningTaskId(earlyWarningTaskId);
        earlyWarningTaskMapper.updateByPrimaryKey(earlyWarningTask);
    }

    /**
     * 批量新增
     * @param earlyWarningTaskList
     */
    @Override
    public void createBatch(List<EarlyWarningTaskDto> earlyWarningTaskList){
        earlyWarningTaskMapper.insertBatch(earlyWarningTaskList);
    };

    /**
     * 批量更新
     * @param earlyWarningTaskList
     */
    @Override
    public void updateBatch(List<EarlyWarningTaskDto> earlyWarningTaskList){
        earlyWarningTaskMapper.updateBatch(earlyWarningTaskList);
    };

    /**
     * 自定义一些属性
     * @param earlyWarningTask
     */
    protected abstract void customPropertySet(EarlyWarningTask earlyWarningTask,EarlyWarningTaskDto earlyWarningTaskDto);

    /**
     * 告警任务创建的时候发送消息
     * @param earlyWarningTask
     */
    protected void sendMessageForWarningtaskCreate(EarlyWarningTask earlyWarningTask){}

}
