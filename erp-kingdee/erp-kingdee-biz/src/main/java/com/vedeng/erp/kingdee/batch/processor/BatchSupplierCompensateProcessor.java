package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchSupplierFinanceDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 供应商补偿处理器
 */
@Service
@Slf4j
public class BatchSupplierCompensateProcessor implements ItemProcessor<BatchSupplierFinanceDto, KingDeeSupplierDto> {

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Override
    public KingDeeSupplierDto process(BatchSupplierFinanceDto batchSupplierFinanceDto) throws Exception {
        if (Objects.isNull(batchSupplierFinanceDto) || Objects.isNull(batchSupplierFinanceDto.getTraderSupplierId())) {
            log.info("供应商补偿处理器，traderSupplierId为空，不进行处理");
            return null;
        }
        KingDeeSupplierDto kingDeeSupplierDto = traderSupplierApiService.getKingDeeSupplierInfo(batchSupplierFinanceDto.getTraderSupplierId());
        if (Objects.isNull(kingDeeSupplierDto)) {
            log.info("供应商补偿处理器，traderSupplierId:{}，未查询到供应商信息", batchSupplierFinanceDto.getTraderSupplierId());
            return null;
        }
        if (KingDeeBizEnums.updateSupplier.equals(kingDeeSupplierDto.getKingDeeBizEnums())) {
            log.info("供应商补偿处理器，traderSupplierId:{}，供应商信息为更新，不进行处理", batchSupplierFinanceDto.getTraderSupplierId());
            return null;
        }
        log.info("供应商补偿处理器，traderSupplierId:{}，kingDeeSupplierDto:{}", batchSupplierFinanceDto.getTraderSupplierId(), JSON.toJSONString(kingDeeSupplierDto));
        return kingDeeSupplierDto;
    }
}