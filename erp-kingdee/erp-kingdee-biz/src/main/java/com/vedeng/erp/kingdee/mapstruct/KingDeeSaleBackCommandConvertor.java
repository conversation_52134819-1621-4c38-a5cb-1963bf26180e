package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseBackCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleBackCommand;
import com.vedeng.erp.kingdee.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售退货单 dto  https://www.yuque.com/manhuo/gf1570/vprm9l
 * @date
 */
@Mapper(componentModel = "spring")
public interface KingDeeSaleBackCommandConvertor extends BaseCommandMapStruct<KingDeeSaleBackCommand,KingDeeSaleReturnStockDto> {
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeID")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FSaleOrgId.FNumber", source = "FSaleOrgId")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "FRetcustId.FNumber", source = "FRetcustId")
    @Mapping(target = "FEntity", source = "FEntity")
    KingDeeSaleBackCommand toCommand(KingDeeSaleReturnStockDto dto);
    @Mapping(target = "FMATERIALID.FNumber", source = "fmaterialid")
    @Mapping(target = "FRealQty", source = "FRealQty")
    @Mapping(target = "FSTOCKID.FNumber", source = "fstockid")
    @Mapping(target = "FTAXPRICE", source = "ftaxprice")
    @Mapping(target = "FENTRYTAXRATE", source = "fentrytaxrate")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "f_QZOK_SFZF", source = "FQzokSfzf")
    @Mapping(target = "FSrcBillTypeID", source = "FSrcBillTypeID")
    @Mapping(target = "FEntity_Link", source = "fentityLink")
    KingDeeSaleBackCommand.FEntity toCommand(KingDeeSaleReturnStockDetailDto dto);
    @Mapping(target = "FLinkId", source = "FLinkId")
    @Mapping(target = "FEntity_Link_FRuleId", source = "fentityLinkFruleid")
    @Mapping(target = "FEntity_Link_FFlowLineId", source = "fentityLinkFflowlineid")
    @Mapping(target = "FEntity_Link_FSTableId", source = "fentityLinkFstableid")
    @Mapping(target = "FEntity_Link_FSTableName", source = "fentityLinkFstablename")
    @Mapping(target = "FEntity_Link_FSBillId", source = "fentityLinkFsbillid")
    @Mapping(target = "FEntity_Link_FSId", source = "fentityLinkFsid")
    @Mapping(target = "FEntity_Link_FBASEUNITQTYOLD", source = "fentityLinkFbaseunitqtyold")
    @Mapping(target = "FEntity_Link_FBASEUNITQTY", source = "fentityLinkFbaseunitqty")
    @Mapping(target = "FEntity_Link_FSALBASEQTYOLD", source = "fentityLinkFsalbaseqtyold")
    @Mapping(target = "FEntity_Link_FSALBASEQTY", source = "fentityLinkFsalbaseqty")
    KingDeeSaleBackCommand.FEntity.FEntityLink toCommand(KingDeeSaleReturnStockDetailLink dto);

}
