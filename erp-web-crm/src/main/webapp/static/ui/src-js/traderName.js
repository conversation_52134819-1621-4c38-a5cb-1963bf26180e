Vue.component('ui-trader-name', {
    template: `<div class="vd-ui-trader-wrap" :class="{'txt-flex': info.nameFlex}">
            <template v-if="info.traderName">
                <span 
                    v-if="info.traderNameLink" 
                    @click="gotoTraderInfo" 
                    class="vd-ui-trader-txt vd-ui-trader-link"
                    :class="{'text-line-1': info.nameFlex}"
                    :title="info.nameFlex ? info.traderName : ''"
                >{{info.traderName}}
                </span>
                <span v-else class="vd-ui-trader-txt" :class="{'text-line-1': info.nameFlex}" :title="info.nameFlex ? info.traderName : ''">
                    {{info.traderName}}
                </span>
                <i v-if="info.tycFlag == 'Y'" @click.stop="openTyc" class="vd-ui-trader-tyc vd-ui_icon icon-tianyancha"></i>
                <i v-if="info.baidu" @click.stop="openBaidu" class="vd-ui-trader-baidu vd-ui_icon icon-baidu2" title="百度搜索客户信息"></i>
            </template>
            <template v-else>-</template>
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
    </div>`,
    props: {
        info: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {
        };
    },
    computed: {

    },
    mounted() {

    },
    methods: {
        openTyc() {
            this.$refs.tycDetail.open(this.info.traderName);
        },
        openBaidu() {
            window.open('https://www.baidu.com/s?wd=' + this.info.traderName)
        },
        gotoTraderInfo() {
            if(this.info.needValid) {
                GLOBAL.showGlobalLoading();
                this.$axios.get('/crm/trader/profile/queryTraderForClick?traderId=' + this.info.traderId).then(({data}) => {
                    GLOBAL.hideGlobalLoading();
                    if(data.success) {
                        GLOBAL.link({name:'客户名称', url: this.info.traderNameInnerLink, link: this.info.traderNameLink, nohost: true})
                    } else {
                        this.$popup.warn({
                            message: data.message,
                            buttons: [{
                                txt: '我知道了',
                                btnClass: 'confirm',
                            }]
                        })
                    }
                })
            } else {
                GLOBAL.link({name:'客户名称', url: this.info.traderNameInnerLink, link: this.info.traderNameLink, nohost: true})
            }
        }
    }
});

Vue.component('ui-trader-grade', {
    template: `<div class="vd-ui-trader-grade" :class="'lv-' + grade" :title="grade"></div>`,
    props: {
        grade: {
            type: String,
            default: ''
        }
    }
})

Vue.component('ui-call', {
    template: `<div class="vd-ui-call-wrap">
        <div class="vd-ui-call-cnt" :class="{link: layout_hidden_value}" v-if="tel" @click="call">
            <i class="vd-ui_icon icon-call2"></i><span class="vd-ui-call-txt">{{ tel }}</span>
        </div>
        <template v-if="status">
            <div class="ui-tel-tip warn" v-if="status == 2">
                <i class="vd-ui_icon icon-caution1"></i>
                <div class="ui-tel-tip-txt">该手机号未注册贝登商城</div>
            </div>
            <div class="ui-tel-tip success" v-if="status == 1">
                <i class="vd-ui_icon icon-yes1"></i>
                <div class="ui-tel-tip-txt">该手机号已注册贝登商城</div>
            </div>
        </template>
    </div>
    <div v-else>-</div>
    `,
    props: {
        tel: {
            type: String,
            default: ''
        },
        callType: {
            type: Number,
            default: 0
        },
        traderId: {
            type: Number,
            default: 0
        },
        orderId: {
            type: Number,
            default: 0
        },
        traderContactId: {
            type: Number,
            default: 0
        },
        status: {
            type: Number,
            default: 0
        }
    },
    methods: {
        call() {
            if(layout_hidden_value) {
                GLOBAL.callNumber({
                    phone: this.tel,
                    traderId: this.traderId,
                    traderType: 1,
                    callType: this.callType, //  7线索1商机2销售订单3报价4售后5采购订单//没有就传 0
                    orderId: this.orderId,
                    traderContactId: this.traderContactId
                })
            }
        }
    }
})