package com.wms.model.po;

import com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra;
import lombok.Data;

@Data
public class WmsOutputOrderGoods {
    private Long id;

    private Long wmsOutputOrderId;

    private String skuNo;

    private Integer outputNum;

    private String expectReturnedTime;

    private String addTime;

    private String updateTime;

    private Integer isDelete;

    private Integer alreadyOutputNum;

    private String lastOutputTime;

    private Integer outStatus;

    private Integer alreadyInputNum;

    private String lastInputTime;

    private Integer inputStatus;

    /**
     * 盘亏出库添加的
     * 逻辑仓id
     */
    private Integer logicalWarehouseId;

    /**
     * 仓库名
     */
    private String logicalName;


    private String unitName;

    private WmsSampleOrderGoodsExtra sampleOrderGoodsExtra;

}