package com.vedeng.erp.settlement.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.finance.domain.entity.CapitalBillSettlementTypeEntity;
import com.vedeng.erp.finance.dto.CapitalBillSettlementTypeDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description dto转entity
 * @date 2022/7/12 10:45
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CapitalBillSettlementTypeConvertor extends BaseMapStruct<CapitalBillSettlementTypeEntity, CapitalBillSettlementTypeDto> {
}
