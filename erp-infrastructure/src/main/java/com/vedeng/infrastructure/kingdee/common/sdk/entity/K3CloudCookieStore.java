package com.vedeng.infrastructure.kingdee.common.sdk.entity;


import java.util.HashMap;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/entity/K3CloudCookieStore.class */
public class K3CloudCookieStore {
    String SID;
    HashMap<String, Cookie> cookies;

    public String getSID() {
        return this.SID;
    }

    public void setSID(String sID) {
        this.SID = sID;
    }

    public HashMap<String, Cookie> getCookies() {
        if (this.cookies == null) {
            this.cookies = new HashMap<>();
        }
        return this.cookies;
    }

    public void setCookies(HashMap<String, Cookie> cookies) {
        this.cookies = cookies;
    }
}
