package com.vedeng.erp.market.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.ezadmin.common.utils.StringUtils;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.market.domain.entity.MarketPlan;
import com.vedeng.erp.market.domain.vo.MarketPlanTraderResponseVo;
import com.vedeng.erp.market.service.MarketService;
import com.vedeng.erp.trader.service.CommunicateRecordApiService;
import com.vedeng.order.model.Saleorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/market/sale")
public class MarketPlanController extends BaseController {

    @Autowired
    private MarketService marketService;

    @Autowired
    private CommunicateRecordApiService communicateRecordApiService;



    /**
     * 精准营销运营活动
     *
     */
    @NoNeedAccessAuthorization
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping("/marketplandetail")
    public ModelAndView marketplandetail(HttpServletRequest request, @RequestParam(required = false, defaultValue = "1") Integer planId,
                                                @RequestParam(value = "sendMsg",required = false,defaultValue = "") Integer sendMsg,
                                                @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                @RequestParam(required = false, defaultValue = "10") Integer pageSize
                                               ){
        ModelAndView modelAndView = new ModelAndView("market/marketplandetail/marketplandetaillist");
        User user = getSessionUser(request);
        Page page = getPageTag(request, pageNo, pageSize);
        MarketPlan marketPlan = marketService.queryMarketPlanByPlanId(planId);
        List<MarketPlanTraderResponseVo> planTraderResponseVoList =marketService.queryMarketPlanTraderList(planId,sendMsg,user.getUserId(),page);
        if(CollectionUtil.isNotEmpty(planTraderResponseVoList)){
            for(MarketPlanTraderResponseVo responseVo:planTraderResponseVoList){
                String contactMob = responseVo.getContactMob();
                if(StringUtils.startsWith(contactMob,"|")){
                    contactMob = contactMob.substring(1,contactMob.length());
                }
                if(StringUtils.endsWithIgnoreCase(contactMob,"|")){
                    contactMob = contactMob.substring(0,contactMob.length()-1);
                }
                responseVo.setContactMob(contactMob);

                List<MarketPlan> marketPlanList = marketService.queryMarketPlanTongqi(responseVo.getPlanId(),user.getUserId()
                        ,responseVo.getTraderId(),responseVo.getTraderCustomerId());
                responseVo.setRelatePlanList(CollectionUtil.isNotEmpty(marketPlanList)?marketPlanList:new ArrayList<>());
                if(responseVo.getLastCommId() != null){
                    responseVo.setTag(communicateRecordApiService.getTag(responseVo.getLastCommId()));
                }
            }
        }
        modelAndView.addObject("marketPlan",marketPlan);
        modelAndView.addObject("planTraderResponseVoList",planTraderResponseVoList);
        String jsonStr = marketPlan.getMarketingScriptListJson();
        List<String> scriptList = JSONArray.parseArray(jsonStr,String.class);
        modelAndView.addObject("scriptList",scriptList);
        modelAndView.addObject("page",page);
        modelAndView.addObject("planId",planId);
        modelAndView.addObject("sendMsg",sendMsg);
        return modelAndView;
    }

    /**
     * 计算销售下待沟通的客户数量
     *
     * @param request HttpServletRequest
     * @return ResultInfo
     */
    @ResponseBody
    @RequestMapping(value = "/getMarketPlanNum")
    @NoNeedAccessAuthorization
    public ResultInfo getPerformanceEvaluationInfo(HttpServletRequest request) {
        User user = getSessionUser(request);
        return ResultInfo.success(marketService.queryUserMarketPlanTraderList(user.getUserId()));
    }


}
