package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 银承中收减免审批单号查询返回
 * @date 2024/11/6 17:12
 */
@Getter
@Setter
@XmlRootElement(name = "CMBC")
@XmlAccessorType(XmlAccessType.FIELD)
public class B2eQueryTaskNoRes extends BankResponse {

    /**
     * 响应体
     */
    @XmlElement(name = "xDataBody")
    private B2eQueryTaskNoResultResBody xDataBody;


    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class B2eQueryTaskNoResultResBody {

        private String svrId;

        private String trnId;

        @XmlElementWrapper(name = "List")
        @XmlElement(name = "Map")
        private List<B2eQueryTaskNo> list;

    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class B2eQueryTaskNo {

        private String taskNo;
        private String serialCode;

    }
}
