package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderCustomerErpDto;

/**
 * <AUTHOR>
 * @description erp Trader Customer
 * @date 2023/8/11 14:47
 **/
public interface TraderCustomerErpApiService {

    /**
     * 保存客户信息接口
     * @param traderCustomerErpDto 数据
     */
    void addContainsOtherData(TraderCustomerErpDto traderCustomerErpDto);

    /**
     * 根据名字查询
     * @param traderName 客户名
     * @return  traderId，TraderCustomerId
     */
    TraderCustomerErpDto queryByName(String traderName);

    /**
     * 更新客户信息
     * @param traderCustomerErpDto 客户信息
     */
    void updateContainsOtherData(TraderCustomerErpDto traderCustomerErpDto);

    /**
     * 获取客户数据
     * @param traderCustomerId
     * @return
     */
    TraderCustomerErpDto queryByTraderCustomerId(Integer traderCustomerId);
}
