<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>表格-文本链接</title>
</head>
<body id="td-linktext"  type="td">
<table>
    <tr id="content">
        <td  th:title="${dataInDb}" th:ITEM_NAME="${itemName}"  th:COLOR_CIRCLE="${COLOR_CIRCLE}" th:ITEM_ID="${itemId}"
             th:class="${'layui-text layui-elip  ezadmin-td ezadmin-td-'+itemId}" th:style="${itemStyle}" >
            <a th:if="${not #strings.isEmpty(itemUrl)}"  href="#" th:tab-id="${itemName+'-'+itemId}"
               th:ITEM_ID="${itemName+''+itemId}"
               th:ITEM_URL="${itemUrl}"
               th:ITEM_OPEN_TYPE="${openType}"
               th:ITEM_OPEN_TITLE="${windowName}" class="ezopenbutton ">
                [[${dataInDb}]]
            </a>
			<div th:remove="tag" th:if="${#strings.isEmpty(itemUrl)}">[[${dataInDb}]]</div>
        </td>
    </tr></table>
</body>
</html>
