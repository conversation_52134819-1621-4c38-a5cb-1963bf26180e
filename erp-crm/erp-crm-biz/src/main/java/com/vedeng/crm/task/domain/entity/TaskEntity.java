package com.vedeng.crm.task.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 任务记录表
 */
@Getter
@Setter
public class TaskEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long taskId;

    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 业务类型，1：商机 2：线索 3：报价
     */
    private Integer bizType;

    /**
     * 业务ID
     */
    private Integer bizId;

    /**
     * 主-任务类型
     */
    private Integer mainTaskType;

    /**
     * 子-任务类型 逗号分割
     */
    private String subTaskType;

    /**
     * 处理状态：0处理中 1已处理 2关闭
     */
    private Integer doneStatus;

    /**
     * 提交时间
     */
    private Date commitTime;

    /**
     * 截止时间
     */
    private Date deadline;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 业务编号
     */
    private String bizNo;


}