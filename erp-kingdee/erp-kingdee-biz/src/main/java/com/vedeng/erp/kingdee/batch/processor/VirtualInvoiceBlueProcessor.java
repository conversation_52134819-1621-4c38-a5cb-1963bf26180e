package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 返利蓝虚拟票 处理类
 * @date 2022/11/18 13:55
 */

@Service
@Slf4j
public class VirtualInvoiceBlueProcessor implements ItemProcessor<BatchSettlementBillDto, List<BatchVirtualInvoiceDto>> {


    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

    @Autowired
    private BatchSettlementBillItemDtoMapper batchSettlementBillItemDtoMapper;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchVirtualInvoiceDtoMapper batchVirtualInvoiceDtoMapper;

    @Autowired
    private BatchAfterSalesGoodsDtoMapper batchAfterSalesGoodsDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;


    @Override
    public List<BatchVirtualInvoiceDto> process(BatchSettlementBillDto batchSettlementBillDto) throws Exception {

        log.info("VirtualInvoiceBlueProcessor.process：{}", JSON.toJSONString(batchSettlementBillDto));


        List<BatchSettlementBillItemDto> batchSettlementBillItemDtoList = batchSettlementBillItemDtoMapper.selectBySettleBillIdAndInvoiceStatusBlue(batchSettlementBillDto.getSettleBillId());

        if (CollUtil.isEmpty(batchSettlementBillItemDtoList)) {
            log.info("未开票结算明细为空：{}", JSON.toJSONString(batchSettlementBillDto));
            return null;
        }

        Map<Integer, BatchSettlementBillItemDto> batchSettlementBillItem2Map = batchSettlementBillItemDtoList.stream().collect(Collectors.toMap(BatchSettlementBillItemDto::getBusinessItemId, x -> x, (k1, k2) -> k1));
        Set<Integer> integers = batchSettlementBillItem2Map.keySet();
        List<Integer> buyOrderGoodsIds = CollUtil.newArrayList(integers);
        List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList = batchBuyorderGoodsDtoMapper.findByBuyorderGoodsIdInAndIsDelete(buyOrderGoodsIds, 0);

        if (CollUtil.isEmpty(buyOrderGoodsDtoList)) {
            log.error("采购明细为空：{}", JSON.toJSONString(buyOrderGoodsIds));
            return null;
        }

        List<BatchBuyorderGoodsDto> batchBuyOrderGoodsArrivalList = getBuyOrderArrivalList(buyOrderGoodsIds, buyOrderGoodsDtoList);

        Map<Integer, BatchBuyorderGoodsDto> buyOrderGoods2Map = buyOrderGoodsDtoList.stream().collect(Collectors.toMap(BatchBuyorderGoodsDto::getBuyorderGoodsId, x -> x, (k1, k2) -> k1));

        // 按行校验
        List<BatchSettlementBillItemDto> matchAllRebate = batchSettlementBillItemDtoList.stream().filter(x -> {
            BatchBuyorderGoodsDto batchBuyorderGoodsDto = buyOrderGoods2Map.get(x.getBusinessItemId());
            if (Objects.isNull(batchBuyorderGoodsDto)) {
                return false;
            }
            Integer num = Optional.ofNullable(batchBuyorderGoodsDto.getNum()).orElse(0);
            BigDecimal price = Optional.ofNullable(batchBuyorderGoodsDto.getPrice()).orElse(BigDecimal.ZERO);
            BigDecimal amount = price.multiply(new BigDecimal(num));
            batchBuyorderGoodsDto.setAmount(amount);
            x.setBuyOrderPrice(price);
            return x.getAmount().compareTo(amount) == 0;
        }).collect(Collectors.toList());


        List<BatchSettlementBillItemDto> matchPartialRebate = batchSettlementBillItemDtoList.stream().filter(x -> {
            BatchBuyorderGoodsDto batchBuyorderGoodsDto = buyOrderGoods2Map.get(x.getBusinessItemId());
            if (Objects.isNull(batchBuyorderGoodsDto)) {
                return false;
            }
            Integer num = Optional.ofNullable(batchBuyorderGoodsDto.getNum()).orElse(0);
            BigDecimal price = Optional.ofNullable(batchBuyorderGoodsDto.getPrice()).orElse(BigDecimal.ZERO);
            BigDecimal amount = price.multiply(new BigDecimal(num));
            batchBuyorderGoodsDto.setAmount(amount);
            return x.getAmount().compareTo(amount) != 0;
        }).collect(Collectors.toList());


        // 找票信息
        List<BatchInvoiceDto> invoiceDtoList = batchInvoiceDtoMapper.selectBuyOrderBlueInvoiceByRelatedId(batchSettlementBillItemDtoList.get(0).getBusinessId());
        List<BatchInvoiceDto> enableInvoice = getEnableInvoice(invoiceDtoList);

        List<BatchInvoiceDetailDto> invoiceDetailByInvoiceIdList = Collections.emptyList();
        Map<Integer, List<BatchInvoiceDetailDto>> invoiceDetail2Map = new HashMap<>();
        if (CollUtil.isNotEmpty(enableInvoice)) {
            // 有实际票 全部返利聚合（排除录票错误的）
            List<Integer> invoiceIds = enableInvoice.stream().map(BatchInvoiceDto::getInvoiceId).collect(Collectors.toList());
            invoiceDetailByInvoiceIdList = batchInvoiceDetailDtoMapper.getInvoiceDetailByInvoiceIdList(invoiceIds);

            invoiceDetail2Map = invoiceDetailByInvoiceIdList.stream().collect(Collectors.groupingBy(BatchInvoiceDetailDto::getDetailgoodsId));
        }

        List<BatchInvoiceDto> enableInvoiceChecked = enableInvoice;
        List<BatchSettlementBillItemDto> matchAllRebateChecked = matchAllRebate;

        if (CollUtil.isNotEmpty(matchAllRebate)) {

            Set<Integer> errorInvoices = new HashSet<>();
            List<BatchSettlementBillItemDto> errorData = checkAndGetErrorData(matchAllRebate, invoiceDetail2Map, errorInvoices);

            // 排除异常数据
            matchAllRebateChecked = getMatchAllRebateChecked(invoiceDetail2Map, matchAllRebateChecked,batchBuyOrderGoodsArrivalList);
            // 排除异常票
            enableInvoiceChecked = getEnableInvoiceChecked(enableInvoiceChecked, errorInvoices, errorData);
        }

        List<BatchVirtualInvoiceDto> result = new LinkedList<>();
        // 全部返利
        log.info("全部返利依托的满足条件的明细行数据：{}",JSON.toJSONString(matchAllRebateChecked));
        if (CollUtil.isNotEmpty(matchAllRebateChecked)) {
            result.addAll(fullRebate(matchAllRebateChecked, buyOrderGoodsDtoList));
        }

        // 部分返利 依据实物票生成
        log.info("部分返利依托的满足条件的实物票数据：{}",JSON.toJSONString(enableInvoiceChecked));
        if (CollUtil.isNotEmpty(enableInvoiceChecked)) {
            Map<Integer, BatchSettlementBillItemDto> matchPartialRebate2Map = matchPartialRebate.stream().collect(Collectors.toMap(BatchSettlementBillItemDto::getBusinessItemId, x -> x, (k1, k2) -> k1));
            result.addAll(partialRebate(matchPartialRebate2Map, buyOrderGoods2Map, enableInvoiceChecked, invoiceDetailByInvoiceIdList));
        }


        if (CollUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    private List<BatchBuyorderGoodsDto> getBuyOrderArrivalList(List<Integer> buyOrderGoodsIds, List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList) {
        List<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtos = batchAfterSalesGoodsDtoMapper.selectByOrderDetailIdsAndTotalAfterNum(buyOrderGoodsIds, 546, 536);
        List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutInItemDtos = batchWarehouseGoodsOutInItemDtoMapper.selectByRelatedIdsAndOperateType(buyOrderGoodsIds, ErpConstant.ONE);

        List<BatchBuyorderGoodsDto> batchBuyOrderGoodsArrivalList = buyOrderGoodsDtoList.stream().filter(x -> {
            BigDecimal realNum = new BigDecimal(x.getNum().toString());
            if (CollUtil.isNotEmpty(batchAfterSalesGoodsDtos)) {
                Optional<BatchAfterSalesGoodsDto> first = batchAfterSalesGoodsDtos.stream().filter(y -> y.getOrderDetailId().equals(x.getBuyorderGoodsId())).findFirst();
                if (first.isPresent()) {
                    realNum = realNum.subtract(new BigDecimal(first.get().getNum().toString()));
                }
            }
            BigDecimal arrivalNum = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(batchWarehouseGoodsOutInItemDtos)) {
                Optional<BatchWarehouseGoodsOutInItemDto> first = batchWarehouseGoodsOutInItemDtos.stream().filter(y -> y.getRelatedId().equals(x.getBuyorderGoodsId())).findFirst();
                if (first.isPresent()) {
                    arrivalNum = first.get().getNum();
                }
            }
            x.setRealNum(realNum);
            return realNum.compareTo(arrivalNum) <= 0 && realNum.compareTo(BigDecimal.ZERO) > 0;

        }).collect(Collectors.toList());
        return batchBuyOrderGoodsArrivalList;
    }

    private List<BatchInvoiceDto> getEnableInvoiceChecked(List<BatchInvoiceDto> enableInvoiceChecked, Set<Integer> errorInvoices, List<BatchSettlementBillItemDto> errorData) {
        if (CollUtil.isNotEmpty(errorData)) {
            log.error("业务数据异常：存在异常录入的全部返利的票明细：{}", JSON.toJSONString(errorData));
            enableInvoiceChecked = enableInvoiceChecked.stream().filter(b -> !errorInvoices.contains(b.getInvoiceId())).collect(Collectors.toList());
        }
        return enableInvoiceChecked;
    }

    private List<BatchSettlementBillItemDto> getMatchAllRebateChecked(Map<Integer, List<BatchInvoiceDetailDto>> invoiceDetail2Map, List<BatchSettlementBillItemDto> matchAllRebateChecked, List<BatchBuyorderGoodsDto> batchBuyOrderGoodsArrivalList) {

        if (CollUtil.isEmpty(batchBuyOrderGoodsArrivalList)) {
            return Collections.emptyList();
        }
        matchAllRebateChecked = matchAllRebateChecked.stream().filter(x -> {
            List<BatchInvoiceDetailDto> batchInvoiceDetailDtos = invoiceDetail2Map.get(x.getBusinessItemId());
            Optional<BatchBuyorderGoodsDto> first = batchBuyOrderGoodsArrivalList.stream().filter(y -> y.getBuyorderGoodsId().equals(x.getBusinessItemId())).findFirst();
            first.ifPresent(a -> x.setNumber(a.getRealNum()));
            return CollUtil.isEmpty(batchInvoiceDetailDtos)&&first.isPresent();
        }).collect(Collectors.toList());

        return matchAllRebateChecked;
    }

    /**
     * 获取异常数据 全部返利的明细但是录票了的
     *
     * @param matchAllRebate    全部返利行
     * @param invoiceDetail2Map 票明细
     * @param errorInvoices     异常票集合
     * @return List<BatchSettlementBillItemDto> 异常明细行
     */
    private List<BatchSettlementBillItemDto> checkAndGetErrorData(List<BatchSettlementBillItemDto> matchAllRebate, Map<Integer, List<BatchInvoiceDetailDto>> invoiceDetail2Map, Set<Integer> errorInvoices) {

        return matchAllRebate.stream().filter(x -> {
            List<BatchInvoiceDetailDto> batchInvoiceDetailDtos = invoiceDetail2Map.get(x.getBusinessItemId());
            if (CollUtil.isNotEmpty(batchInvoiceDetailDtos)) {
                Set<Integer> collect = batchInvoiceDetailDtos.stream().map(BatchInvoiceDetailDto::getInvoiceId).collect(Collectors.toSet());
                errorInvoices.addAll(collect);
            }
            return CollUtil.isNotEmpty(batchInvoiceDetailDtos);
        }).collect(Collectors.toList());
    }

    /**
     * 排除作废票 以及生成虚拟票的
     *
     * @param invoiceDtoList 所有蓝票
     * @return List<BatchInvoiceDto>
     */
    private List<BatchInvoiceDto> getEnableInvoice(List<BatchInvoiceDto> invoiceDtoList) {

        if (CollUtil.isEmpty(invoiceDtoList)) {
            return Collections.emptyList();
        }
        List<BatchInvoiceDto> voidInvoice = invoiceDtoList.stream().filter(x -> x.getIsEnable().equals(ErpConstant.ZERO)).collect(Collectors.toList());
        List<Integer> invoiceIds = invoiceDtoList.stream().map(BatchInvoiceDto::getInvoiceId).collect(Collectors.toList());
        List<BatchVirtualInvoiceDto> batchVirtualInvoiceDtoList = batchVirtualInvoiceDtoMapper.selectBySourceInvoiceIds(invoiceIds);
        Map<Integer, List<BatchVirtualInvoiceDto>> virtualInvoice2Map = batchVirtualInvoiceDtoList.stream().collect(Collectors.groupingBy(BatchVirtualInvoiceDto::getSourceInvoiceId));
        return invoiceDtoList.stream().filter(x -> {
            boolean enable = x.getIsEnable().equals(ErpConstant.ONE)&& ErpConstant.ONE.equals(x.getValidStatus());
            if (!enable) {
                return false;
            }
            Optional<BatchInvoiceDto> first = voidInvoice.stream().filter(a -> a.getInvoiceNo().equals(x.getInvoiceNo()) && a.getInvoiceCode().equals(x.getInvoiceCode())).findFirst();
            return !first.isPresent() && CollUtil.isEmpty(virtualInvoice2Map.get(x.getInvoiceId()));
        }).collect(Collectors.toList());
    }

    /**
     * 部分返利
     *
     * @param batchSettlementBillItem2Map  返利明细
     * @param buyOrderGoods2Map            商品
     * @param enableInvoice                有效票
     * @param invoiceDetailByInvoiceIdList 票明细
     * @return List<BatchVirtualInvoiceDto> 虚拟票
     */
    private List<BatchVirtualInvoiceDto> partialRebate(Map<Integer, BatchSettlementBillItemDto> batchSettlementBillItem2Map, Map<Integer, BatchBuyorderGoodsDto> buyOrderGoods2Map, List<BatchInvoiceDto> enableInvoice, List<BatchInvoiceDetailDto> invoiceDetailByInvoiceIdList) {

        Map<Integer, List<BatchInvoiceDetailDto>> invoiceDetail2Map = invoiceDetailByInvoiceIdList.stream().collect(Collectors.groupingBy(BatchInvoiceDetailDto::getInvoiceId));

        return enableInvoice.stream().map(x -> {

            List<BatchInvoiceDetailDto> detailDtoList = invoiceDetail2Map.get(x.getInvoiceId());

            List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtoList = detailDtoList.stream().map(a -> {

                BatchSettlementBillItemDto batchSettlementBillItemDto = batchSettlementBillItem2Map.get(a.getDetailgoodsId());
                if (Objects.isNull(batchSettlementBillItemDto)) {
                    // 非返利数据跳过
                    return null;
                }

                BatchVirtualInvoiceItemDto virtualInvoiceItemDto = new BatchVirtualInvoiceItemDto();
                virtualInvoiceItemDto.setSourceInvoiceItemId(a.getInvoiceDetailId());
                virtualInvoiceItemDto.setPrice(batchSettlementBillItemDto.getBuyOrderPrice());
                BigDecimal billItemDtoPrice = batchSettlementBillItemDto.getPrice();
                BatchBuyorderGoodsDto batchBuyorderGoodsDto = buyOrderGoods2Map.get(a.getDetailgoodsId());
                BigDecimal batchBuyOrderGoodsPrice = batchBuyorderGoodsDto.getPrice().abs();
                virtualInvoiceItemDto.setTotalAmount(a.getTotalAmount().multiply(billItemDtoPrice).divide(batchBuyOrderGoodsPrice.subtract(billItemDtoPrice),2,RoundingMode.HALF_UP));
                BigDecimal num = virtualInvoiceItemDto.getTotalAmount().divide(virtualInvoiceItemDto.getPrice(), 2, RoundingMode.HALF_UP);
                virtualInvoiceItemDto.setNum(num);
                virtualInvoiceItemDto.setBusinessOrderItemId(batchSettlementBillItemDto.getBusinessItemId());
                return virtualInvoiceItemDto;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            if (CollUtil.isEmpty(virtualInvoiceItemDtoList)) {
                // 整张蓝票无返利明细
                return null;
            }

            BatchVirtualInvoiceDto batchVirtualInvoiceDto = new BatchVirtualInvoiceDto();
            BatchSettlementBillItemDto value = batchSettlementBillItem2Map.entrySet().stream().findAny().get().getValue();
            batchVirtualInvoiceDto.setSourceInvoiceId(x.getInvoiceId());
            batchVirtualInvoiceDto.setInvoiceNo(x.getInvoiceNo() + "-FL");
            batchVirtualInvoiceDto.setInvoiceCode(x.getInvoiceCode());
            batchVirtualInvoiceDto.setInvoiceProperty(x.getInvoiceProperty());
            batchVirtualInvoiceDto.setInvoiceType(x.getInvoiceType());
            batchVirtualInvoiceDto.setRatio(x.getRatio());
            batchVirtualInvoiceDto.setSettleBillId(value.getSettleBillId());
            batchVirtualInvoiceDto.setBusinessType(1);
            batchVirtualInvoiceDto.setBusinessOrderId(value.getBusinessId());
            batchVirtualInvoiceDto.setBusinessOrderNo(value.getBusinessNo());
            batchVirtualInvoiceDto.setColorType(2);
            batchVirtualInvoiceDto.setOpenInvoiceTime(new Date());
            // 计算返利生成的虚拟票明细的值
            BigDecimal amount = virtualInvoiceItemDtoList.stream().map(BatchVirtualInvoiceItemDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            batchVirtualInvoiceDto.setAmount(amount);
            batchVirtualInvoiceDto.setVirtualInvoiceItemDtoList(virtualInvoiceItemDtoList);
            return batchVirtualInvoiceDto;
        }).filter(Objects::nonNull).collect(Collectors.toCollection(LinkedList::new));

    }


    /**
     * 全部返利
     * 发票号：订单号后10位再加后缀-FL，发票金额：订单金额，税额：发票金额*13%普通发票，红蓝字：蓝字有效，不含税金额：发票金额-税额，票种：13%普通发票 发票代码：000000 发票类型：纸质
     *
     * @param batchSettlementBillItemDtoList 返利明细
     * @param buyOrderGoodsDtoList           商品明细
     * @return List<BatchVirtualInvoiceDto> 虚拟票
     */
    private List<BatchVirtualInvoiceDto> fullRebate(List<BatchSettlementBillItemDto> batchSettlementBillItemDtoList, List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList) {

        BatchVirtualInvoiceDto batchVirtualInvoiceDto = new BatchVirtualInvoiceDto();

        String businessNo = batchSettlementBillItemDtoList.get(0).getBusinessNo();
        String virtualInvoiceNo = getVirtualInvoiceNo(businessNo);
        batchVirtualInvoiceDto.setInvoiceNo(virtualInvoiceNo);
        batchVirtualInvoiceDto.setInvoiceCode("000000");
        batchVirtualInvoiceDto.setInvoiceProperty(1);
        batchVirtualInvoiceDto.setInvoiceType(InvoiceTaxTypeEnum.PLAIN_INVOICE_13.getCode());
        batchVirtualInvoiceDto.setRatio(InvoiceTaxTypeEnum.PLAIN_INVOICE_13.getTax());
        batchVirtualInvoiceDto.setBusinessType(1);
        batchVirtualInvoiceDto.setBusinessOrderId(batchSettlementBillItemDtoList.get(0).getBusinessId());
        batchVirtualInvoiceDto.setBusinessOrderNo(batchSettlementBillItemDtoList.get(0).getBusinessNo());
        batchVirtualInvoiceDto.setColorType(2);
        batchVirtualInvoiceDto.setSettleBillId(batchSettlementBillItemDtoList.get(0).getSettleBillId());

        batchVirtualInvoiceDto.setOpenInvoiceTime(new Date());
        batchVirtualInvoiceDto.setSourceInvoiceId(0);

        List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtoList = batchSettlementBillItemDtoList.stream().map(x -> {
            BatchVirtualInvoiceItemDto a = new BatchVirtualInvoiceItemDto();
            a.setNum(x.getNumber());
            a.setPrice(x.getBuyOrderPrice().abs());
            a.setBusinessOrderItemId(x.getBusinessItemId());
            a.setTotalAmount(a.getPrice().multiply(a.getNum()));
            return a;
        }).collect(Collectors.toList());

        batchVirtualInvoiceDto.setVirtualInvoiceItemDtoList(virtualInvoiceItemDtoList);
        BigDecimal allAmount = virtualInvoiceItemDtoList.stream().map(BatchVirtualInvoiceItemDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        batchVirtualInvoiceDto.setAmount(allAmount);
        return CollUtil.newLinkedList(batchVirtualInvoiceDto);
    }

    /**
     * 全部返利票号
     *
     * @param sourceNo 单号
     * @return String
     */
    private String getVirtualInvoiceNo(String sourceNo) {
        return StrUtil.sub(sourceNo, -10, sourceNo.length())+"-FL";
    }

}
