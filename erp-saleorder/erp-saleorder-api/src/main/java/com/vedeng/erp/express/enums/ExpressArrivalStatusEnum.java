package com.vedeng.erp.express.enums;

/**
 * 快递签收状态：0 2  没有部分签收
 */
public enum ExpressArrivalStatusEnum {


    WAIT(0, "未签收"),

    ALL(2,"已签收")
    ;

    private Integer code;

    private String desc;

    ExpressArrivalStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getExpressArrivalNameByCode(Integer code){
        for (ExpressArrivalStatusEnum value : ExpressArrivalStatusEnum.values()) {
            if (value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return "-";
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
