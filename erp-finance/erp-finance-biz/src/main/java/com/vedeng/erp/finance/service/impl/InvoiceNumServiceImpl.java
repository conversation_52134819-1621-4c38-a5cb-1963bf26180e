package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.EventBus;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.dto.InvoiceDetailDto;
import com.vedeng.erp.finance.dto.InvoiceNumApiDto;
import com.vedeng.erp.finance.mapper.InvoiceDetailMapper;
import com.vedeng.erp.finance.service.InvoiceNumApiService;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/22 8:48
 **/
@Service
@Slf4j
public class InvoiceNumServiceImpl implements InvoiceNumApiService {

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Autowired
    private InvoiceDetailMapper newInvoiceDetailMapper;



    @Override
    public List<InvoiceNumApiDto.SalesOrderInvoiceDto> getInvoiceNum(Integer saleOrderId) {

        log.info("getInvoiceNum saleOrderId:{}",saleOrderId);

        List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtoList = saleOrderGoodsApiService.getSalesOrderGoodsByOrderId(saleOrderId);
        List<Integer> goodsList = saleOrderGoodsDetailDtoList.stream().map(SaleOrderGoodsDetailDto::getSaleorderGoodsId).collect(Collectors.toList());
        if (CollUtil.isEmpty(goodsList)) {
            log.error("getInvoiceNum goodsList is empty,saleOrderId:{}",saleOrderId);
            return CollUtil.newArrayList();
        }
        InvoiceNumApiDto apiDto = new InvoiceNumApiDto();
        apiDto.setSaleOrderId(saleOrderId);
        apiDto.setGoodsIds(goodsList);
        eventBusCenter.post(apiDto);
        Map<Integer, SaleOrderGoodsDetailDto> goodsDetailDtoMap = saleOrderGoodsDetailDtoList.stream().collect(Collectors.toMap(SaleOrderGoodsDetailDto::getSaleorderGoodsId, x -> x, (k1, k2) -> k1));
        List<InvoiceNumApiDto.SalesOrderInvoiceDto> list = apiDto.getGoodsList();

        List<AfterSalesGoodsDto> afterSalesNum = afterSalesApiService.getAfterSalesNum(saleOrderId);

        List<InvoiceDetailDto> realInvoiceNum = newInvoiceDetailMapper.findRealInvoiceNum(goodsList);
        list.forEach(x->{
            log.info("销售单明细：{}",x.getSalesOrderGoodsId());
            SaleOrderGoodsDetailDto saleOrderGoodsDetailDto = goodsDetailDtoMap.get(x.getSalesOrderGoodsId());
            Integer num = saleOrderGoodsDetailDto.getNum();
            x.setSalesOrderNum(new BigDecimal(num));
            x.setIsVirtualSku(saleOrderGoodsDetailDto.getIsVirtureSku());
            x.setSku(saleOrderGoodsDetailDto.getSku());
            processTN(x, saleOrderGoodsDetailDto);
            processAfterSalesNum(afterSalesNum, x);
            log.info("arrivalNum:{},salesOrderNum:{},afterSalesNum:{}",x.getArrivalNum(),x.getSalesOrderNum(),x.getAfterSalesNum());
            BigDecimal realNum = x.getSalesOrderNum().subtract(x.getArrivalNum());
            // 退货数量 > 订单数量 - 收货T+N数量 ==>  收货T+N数量 - 绝对值（（订单数量 - 收货T+N数量）- 退货数量）
            if (x.getAfterSalesNum().compareTo(realNum) > 0) {
                BigDecimal abs = realNum.subtract(x.getAfterSalesNum()).abs();
                BigDecimal realArrivalNum = x.getArrivalNum().subtract(abs);
                x.setArrivalNum(realArrivalNum);
                log.info("退货数量 > 订单数量 - 收货T+N数量 ==>  收货T+N数量 - 绝对值（（订单数量 - 收货T+N数量）- 退货数量）,realArrivalNum:{}",realArrivalNum);
            }
            processInvoiceNum(realInvoiceNum, x);
            // 实际收货T+N数量 - 实际开票数量
            BigDecimal lastNum = x.getArrivalNum().subtract(x.getInvoiceNum());
            if (BigDecimal.ZERO.compareTo(lastNum) > 0) {
                lastNum = BigDecimal.ZERO;
            }
            log.info("lastNum:{}",lastNum);
            x.setArrivalNum(lastNum);


        });

        log.info("getInvoiceNum result:{}", JSON.toJSONString(list));

        return list;
    }

    private void processInvoiceNum(List<InvoiceDetailDto> realInvoiceNum, InvoiceNumApiDto.SalesOrderInvoiceDto x) {
        // 开票
        if (CollUtil.isNotEmpty(realInvoiceNum)) {

            BigDecimal realInvoice = realInvoiceNum.stream()
                    .filter(a -> a.getDetailgoodsId().equals(x.getSalesOrderGoodsId()))
                    .map(InvoiceDetailDto::getNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            x.setInvoiceNum(realInvoice);
        }
        log.info("invoiceNum:{}",x.getInvoiceNum());
    }

    private void processTN(InvoiceNumApiDto.SalesOrderInvoiceDto x, SaleOrderGoodsDetailDto saleOrderGoodsDetailDto) {
        String afterSaleServiceLabels = saleOrderGoodsDetailDto.getAfterSaleServiceLabels();
        // 计算 N
        if (StrUtil.isNotEmpty(afterSaleServiceLabels) && afterSaleServiceLabels.contains("S6")) {
            x.setN(0);
        }
        log.info("N:{}", x.getN());

        Long arrivalTime = saleOrderGoodsDetailDto.getArrivalTime();
        Date date = new Date(arrivalTime);
        // 抹去时分秒
        date = DateUtil.beginOfDay(date);
        log.info("签收日期:{}",DateUtil.formatDateTime(date));
        // 满足T+N 日期 4的时候跳过周六周日
        if (x.getN().equals(4)) {
            Date newDate = addDaysSkipWeekend(date, x.getN());
            x.setTime(newDate);
        } else {
            DateTime dateTime = DateUtil.offsetDay(date, x.getN());
            x.setTime(dateTime);
        }
        log.info("time:{}", x.getTime());
        // 实物商品 系统日期 <   满足收货T+N日期：满足收货T+N 的收货数量 = 0
        if (!ErpConstant.ONE.equals(x.getIsVirtualSku())) {
            if (System.currentTimeMillis() < x.getTime().getTime()) {
                x.setArrivalNum(BigDecimal.ZERO);
            }
        }

        if (ErpConstant.ONE.equals(x.getIsVirtualSku())) {
            // 虚拟商品到货时间为0
            x.setTime(new Date(0));
            log.info("虚拟商品到货时间为0 time:{}", x.getTime());
        }
    }

    private void processAfterSalesNum(List<AfterSalesGoodsDto> afterSalesNum, InvoiceNumApiDto.SalesOrderInvoiceDto x) {
        // 售后
        if (CollUtil.isNotEmpty(afterSalesNum)) {

            BigDecimal afterSaleNum = afterSalesNum.stream()
                    .filter(a -> a.getOrderDetailId().equals(x.getSalesOrderGoodsId()))
                    .map(a -> new BigDecimal(a.getNum())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            x.setAfterSalesNum(afterSaleNum);

        }
    }


    public static Date addDaysSkipWeekend(Date date, int days) {
        int addedDays = 0;
        while (addedDays < days) {
            date = DateUtil.offsetDay(date,  1);
            if (DateUtil.dayOfWeek(date) != 1 && DateUtil.dayOfWeek(date) != 7) {
                addedDays++;
            }
        }
        return date;
    }


}
