$(function () {
    //校验
    // var canSubmit = true;
    var addSpuRules={
        goodsLevelNo:{
            required: true,
        },
        goodsPositionNo:{
            required: true,
        },
        categoryId: {
            required: true
        },
        brandId: {
            required: true
        },
        spuType: {
            required: true
        },
        firstEngageId: {
            required: true
        },
        assignmentAssistantId: {
            required: true
        },
        assignmentManagerId: {
            required: true
        },
        spuName: {
            required: true,
            maxlength: 128
        },
        showName: {
            required: true,
            maxlength: 128
        },
        specsModel:{
            required: true,
        },
        registrationIcon: {
            maxlength: 128
        },
        hasRegistrationCert:{
            required: true,
        },
        secondLevelSpuType:{
            required: true,
        },
        medicalInstrumentCatalogIncluded:{
            required: true,
        },
        storageConditionTemperature: {
            required: true
        },
        storageConditionTemperatureLowerValue: {
            required: true,
            range: [-100,100],
            checkTem: true,
        },
        storageConditionTemperatureUpperValue: {
            required: true,
            range:[-100,100],
            checkTem: true,
        },
        storageConditionHumidityLowerValue: {
            required: true,
            digits: true,
            range: [0,100],
            checkHum: true,
        },
        storageConditionHumidityUpperValue: {
            required: true,
            digits: true,
            range: [0,100],
            checkHum: true,
        },
        storageConditionOthersArray:{
            required: true,
        },
        wikiHref:{
            required: true,
        },
        noMedicalFirstType: {
            required: true,
        },
        // taxClassificationCode: {
        //     required: true,
        //     maxlength: 19
        // }
    };
    var updateRules={
        goodsLevelNo:{
            required: true,
        },
        goodsPositionNo:{
            required: true,
        },
        categoryId: {
            required: true
        },
        brandId: {
            required: true
        },
        spuType: {
            required: true
        },
        firstEngageId: {
            required: true
        },
        spuName: {
            required: true,
            maxlength: 128
        },
        showName: {
            required: true,
            maxlength: 128
        },
        specsModel:{
            required: true,
            maxlength: 512,
        },
        departmentIds: {
            required: true
        },
        registrationIcon: {
            maxlength: 128
        },
        hasRegistrationCert:{
            required: true,
        },
        secondLevelSpuType:{
            required: true,
        },
        medicalInstrumentCatalogIncluded:{
            required: true,
        },
        storageConditionTemperature: {
            required: true
        },
        storageConditionTemperatureLowerValue: {
            required: true,
            range: [-100,100],
            checkTem: true,
        },
        storageConditionTemperatureUpperValue: {
            required: true,
            range:[-100,100],
            checkTem: true,
        },
        storageConditionHumidityLowerValue: {
            required: true,
            digits: true,
            range: [0,100],
            checkHum: true,
        },
        storageConditionHumidityUpperValue: {
            required: true,
            digits: true,
            range: [0,100],
            checkHum: true,
        },
        storageConditionOthersArray:{
            required: true,
        },
        wikiHref:{
            required: true,
        },
        noMedicalFirstType: {
            required: true,
        },
        // taxClassificationCode: {
        //     required: true,
        //     maxlength: 19
        // }
    };
    var addMessages={
        goodsLevelNo:{
            required: '请选择商品等级',
        },
        goodsPositionNo:{
            required: '请选择商品档位',
        },
        categoryId: {
            required: '请选择分类'
        },
        brandId: {
            required: '请选择商品品牌'
        },
        spuType: {
            required: '请选择商品类型'
        },
        firstEngageId: {
            required: '请选择注册证/备案信息'
        },
        spuName: {
            required: '请输入产品名称（注册证/备案凭证）'
        },
        specsModel:{
            required: '请输入规格、型号（注册证/备案凭证）',
            maxlength: '规格、型号（注册证/备案凭证)超过512个字符',
        },
        showName: {
            required: '请填写spu名称'
        },
        assignmentAssistantId: {
            required: '请选择归属的产品助理'
        },
        assignmentManagerId: {
            required: '请选择归属产品经理'
        },
        storageConditionTemperature: {
            required: '请选择存储条件(温度)'
        },
        storageConditionTemperatureLowerValue: {
            required: '请填写温度范围',
            range: '请正确填写，温度范围-100-100',
        },
        storageConditionTemperatureUpperValue: {
            required: '请填写温度范围',
            range: '请正确填写，温度范围-100-100',
        },
        storageConditionHumidityLowerValue:{
            required: '请填写湿度范围',
            digits: '请输入正确填写，湿度范围0-100（正整数）',
            range: '请输入正确填写，湿度范围0-100（正整数）',
        },
        storageConditionHumidityUpperValue:{
            required: '请填写湿度范围',
            digits: '请输入正确填写，湿度范围0-100（正整数）',
            range: '请输入正确填写，湿度范围0-100（正整数）',
        },
        storageConditionOthersArray:{
            required: '请选择存储条件其他',
        },
        hasRegistrationCert:{
            required: '请选择是否有注册证/备案凭证',
        },
        secondLevelSpuType:{
            required: '请选择是否为大型医疗器械',
        },
        medicalInstrumentCatalogIncluded:{
            required: '请选择是否在《医疗器械分类目录》'
        },
        wikiHref:{
            required: "请输入WIKI连接",
        },
        noMedicalFirstType: {
            required: "请选择非医疗器械一级分类",
        },
        // taxClassificationCode: {
        //     required: '请输入税收编码',
        // }
    };

    let updateMessages={
        goodsLevelNo:{
            required: '请选择商品等级',
        },
        goodsPositionNo:{
            required: '请选择商品档位',
        },
        categoryId: {
            required: '请选择分类'
        },
        spuLevel: {
            required: '请选择商品等级'
        },
        brandId: {
            required: '请选择商品品牌'
        },
        spuType: {
            required: '请选择商品类型'
        },
        firstEngageId: {
            required: '请选择注册证/备案信息'
        },
        spuName: {
            required: '请输入产品名称（注册证/备案凭证）'
        },
        specsModel:{
            required: '请输入规格、型号（注册证/备案凭证）',
            maxlength: '规格、型号（注册证/备案凭证)超过512个字符',
        },
        showName: {
            required: '请填写spu名称'
        },
        storageConditionTemperature: {
            required: '请选择存储条件(温度)'
        },
        storageConditionTemperatureLowerValue: {
            required: '请填写温度范围',
            range: '请正确填写，温度范围-100-100',
        },
        storageConditionTemperatureUpperValue: {
            required: '请填写温度范围',
            range: '请正确填写，温度范围-100-100',
        },
        storageConditionHumidityLowerValue:{
            required: '请填写湿度范围',
            digits: '请输入正确填写，湿度范围0-100（正整数）',
            range: '请输入正确填写，湿度范围0-100（正整数）',
        },
        storageConditionHumidityUpperValue:{
            required: '请填写湿度范围',
            digits: '请输入正确填写，湿度范围0-100（正整数）',
            range: '请输入正确填写，湿度范围0-100（正整数）',
        },
        storageConditionOthersArray:{
            required: '请选择存储条件其他',
        },
        hasRegistrationCert:{
            required: '请选择是否有注册证/备案凭证',
        },
        secondLevelSpuType:{
            required: '请选择是否为大型医疗器械',
        },
        medicalInstrumentCatalogIncluded:{
            required: '请选择是否在《医疗器械分类目录》'
        },
        wikiHref:{
            required: "请输入WIKI连接",
        },
        noMedicalFirstType: {
            required: "请选择非医疗器械一级分类",
        },
        // taxClassificationCode: {
        //     required: '请输入税收编码',
        // }
    };

    var spuId=$("#spuId").val();
    var rules=addSpuRules;
    var messages=addMessages;
    if(spuId!=undefined&&spuId>0){
        rules=updateRules
        messages=updateMessages;
    }

    $('.assign').bind("change", function () {
        var user_id=($(this).val());
        if (user_id != undefined && user_id != '') {
            $(this).valid()
        }
    });

    var validator = $('.J-form').validate({
        errorWrap: true,
        rules: rules ,
        messages: messages,
        groups: {
            storageConditionHumidity: "storageConditionHumidityLowerValue storageConditionHumidityUpperValue",
            storageConditionTemperature: "storageConditionTemperatureLowerValue  storageConditionTemperatureUpperValue",
        },
        submitHandler: function (form) {
            //同时满足新增spu、器械类型、更换首营时走校验接口
            if($('input[name=spuId]').val()=='' && $('input[name=hasRegistrationCert]').val()==1 && $('input[name=firstEngageId]').val() !== initialFirstEngageId) {
                //器械类型才会关联注册证
                checkSpuNameWhenSwitchRegisterNumberCert(form, submitSpuDetailsForm)
            } else {
                submitSpuDetailsForm(form)
            }
        }
    });

    function submitSpuDetailsForm(form) {
        debugger;
        $('vd-tip tip-red error-tips-wrapper').hide();
        var content = '';
        var checkStatus = $('#checkStatus').val();
        var isPushedSpu = $('#isPushedSpu').val();
        console.log('checkStatus' + checkStatus + 'isPushedSpu' + isPushedSpu);
        if (checkStatus == 3 && isPushedSpu == 'true'){
            content = '审核通过后，针对需重推的SKU，系统会执行一次自动推送！';
        } else {
            content = '商品信息有改动，是否确认操作';
        }


        //提交表单回调函数
        if ($.trim($('[name=spuId]').val())) {
            //VDERP-4293 spu修改后提交默认为待提交审核状态，修改提交提示信息，原提示消息为：商品信息有改动，会自动将所有子商品设置待审核状态，上架商品会自动下架，是否确认操作
            var dialog = artDialog.confirm(content, '', {
                fn: function () {
                    window.localStorage.setItem('addSpuSuccess', true);
                    setCateHistory(historyName);
                    asyncSubmitFrom(form)
                }, text: '确认'
            }, {
                fn: function () {
                    dialog.close();
                }, text: '取消'
            });
        } else {
            window.localStorage.setItem('addSpuSuccess', true);
            setCateHistory(historyName);
            asyncSubmitFrom(form)
        }
    }

    var supUpdatedFlag = $('#spuId').val() > 0;

    function asyncSubmitFrom(form) {
        $.ajax({
            async: false,
            url: page_url+'/goods/vgoods/saveSpu.do',
            type:"POST",
            data:$('#form_submit').serialize(),
            dataType:"json",
            success: function(data){
                if (data != null && data.code === 0) {
                    if(supUpdatedFlag){
                        var spuId = parseInt(data.data.spuId)
                        if(!isNaN(spuId) && spuId > 0){
                            window.location.href = page_url+'/goods/vgoods/viewSpu.do?spuId='+spuId
                        }
                    }else {
                        $('.first-step').hide()
                        $('.second-step').hide()
                        $('.end-step').show();
                        $('.J-addSpu-detail').show();
                        $('#spuIdAftSuccess').val(data.data.spuId);
                        $('#viewSupName').text(data.data.spuName);
                        $('#viewCategory').text(data.data.categoryName);
                        $('#viewLevel').text(data.data.goodsLevelName);
                        $('#viewPosition').text(data.data.goodsPositionName);

                        setStep(2)
                    }
                } else {
                    var errorMessage = data.message;
                    if(errorMessage==null || errorMessage==''){
                        errorMessage='系统发生错误，请联系管理员。'
                    }

                    if($('.error-tips').length > 0){
                        $('.error-tips').text(errorMessage)
                        $('.error-tips-wrapper').css('display','block')
                    }
                }
            },
            error:function (data) {
                console.log(data)
            }
        });
    }

    Select.use('.J-select');

    //重新渲染属性
    var refreshAttr = function (data) {
        if (data.length) {
            $('.J-attr-list').empty();
            $.each(data, function (i, item) {
                $('.J-attr-list').append(
                    '<label class="input-wrap"><input type="checkbox" name="baseAttributeIds" value="' + item.baseAttributeId + '">' +
                    '<span class="input-ctnr"></span>' + item.baseAttributeName +
                    '</label >'
                );
            })

            $('.J-attr-wrap').show();
            initMainAttribute();
            checkBox()
            spuTypeChange()
        } else {
            $('.J-attr-list').empty();
            $('.J-attr-wrap').hide();
        }
    }

    //初始化页面时，没有属性值，隐藏属性模块
    if ($('.J-attr-wrap').length && $('.J-attr-wrap input:checkbox').length) {
        $('.J-attr-wrap').show();
        initMainAttribute();
        checkBox();
        spuTypeChange()
    } else {
        $('.J-attr-wrap').hide();
    }


    function initMainAttribute(){
        var firstLevelGoodsType = $('.J-prod-type:checked').val();
        if ($("input[name='baseAttributeIds']:checked").length>0 &&(firstLevelGoodsType==317 || firstLevelGoodsType==318)){
            $('.J-main-attr-wrap').show();
        }else {
            $('.J-main-attr-wrap').hide();
        }
        // $("input[name='baseAttributeIds'][checked]").each(function() {
        //     debugger
        //     if (true == $(this).attr("checked")) {
        //
        //         $('.J-attr-list').append(
        //             '<label class="input-wrap"><input type="checkbox" name="mainAttributeIds" value="' + item.baseAttributeId + '">' +
        //             '<span class="input-ctnr"></span>' + item.baseAttributeName +
        //             '</label >'
        //         );
        //     }
        // })
    }

    function checkIsRepeat(){
        var c = $("input[name='primaryAttributeIds']");
        for (var i = 0; i < c.length; i++) {
            if (c[i].value == this.value){
                return true;
            }
        }
        return false;
    }
    function checkBox(){
        var s = $("input[name='baseAttributeIds']");
        s.each(function() {
            $(this).click(function(){
                var firstLevelGoodsType = $('.J-prod-type:checked').val();
                if(this.checked==true && (firstLevelGoodsType==317 || firstLevelGoodsType==318)){
                    if (checkIsRepeat()){
                        return;
                    }
                    var a = $(this).parent().text().trim();
                    var b = "primaryAttribute"+this.value
                    $('.J-main-attr-list').append(
                        '<label class="input-wrap '+b+'"><input type="checkbox" name="primaryAttributeIds" value="' + this.value + '">' +
                        '<span class="input-ctnr"></span>' + a +
                        '</label >'
                    );
                }else {
                    var b = "primaryAttribute"+this.value
                    $(".J-main-attr-list "+"."+b).remove()
                }
                initMainAttribute()
            });
        });

    }

    function spuTypeChange(){
        var s = $("input[name='spuType']");

        s.each(function() {
            $(this).click(function(){
                var spuType = $(this).val();
                if (spuType == 317 || spuType == 318){
                    $('.J-main-attr-list').empty();
                    var a = $("input[name='baseAttributeIds']:checked").length
                    $("input[name='baseAttributeIds']:checked").each(function(){
                        var a = $(this).parent().text().trim();
                        var b = "primaryAttribute"+this.value
                        $('.J-main-attr-list').append(
                            '<label class="input-wrap '+b+'"><input type="checkbox" name="primaryAttributeIds" value="' + this.value + '">' +
                            '<span class="input-ctnr"></span>' + a +
                            '</label >'
                        );

                    });
                    initMainAttribute();

                }else {
                    $('.J-main-attr-list').empty();
                    $('.J-main-attr-wrap').hide();
                }
                // debugger
                // var firstLevelGoodsType = $('.J-prod-type:checked').val();
                // if(this.checked==true && (firstLevelGoodsType==317 || firstLevelGoodsType==318)){
                //     var a = $(this).parent().text().trim();
                //     var b = "mainAttribute"+this.value
                //     $('.J-main-attr-list').append(
                //         '<label class="input-wrap '+b+'"><input type="checkbox" name="mainAttributeIds" value="' + this.value + '">' +
                //         '<span class="input-ctnr"></span>' + a +
                //         '</label >'
                //     );
                // }else {
                //     var b = "mainAttribute"+this.value
                //     $(".J-main-attr-list "+"."+b).remove()
                // }
            });
        });

    }







    //实例化选择分类
    var historyName = null;
    var setCateHistory = function (category) {
        var history = JSON.parse(localStorage.getItem('prod_category') || "[]") || [];

        if (category) {
            var historyList = [];
            $.each(history, function (i, item) {
                if (historyList.length < 9 && category.value != item.value) {
                    historyList.push(item);
                }
            });

            localStorage.setItem('prod_category', JSON.stringify([category].concat(historyList)));
        }
    };
    new DialogSearch({
        el: '.J-category-select',
        async: true,
        dataUrl: page_url + '/category/base/getCategoryList.do',
        input: '.J-category-value',
        label: '.J-category-select .J-text',
        searchUrl: page_url + '/category/base/getCategoryListByKeyWords.do',
        params: 'keyWords',
        placeholder: '请输入商品名/分类名/国际码',
        needTab: ['商品分类', '历史分类'],
        dataparse: function (data) {
            var resData = [];

            $.each(data.listData, function (i, lv1) {
                var lv1item = {
                    label: lv1.baseCategoryName,
                    value: lv1.baseCategoryId,
                    child: []
                };

                if (lv1.secondCategoryList) {
                    $.each(lv1.secondCategoryList, function (ii, lv2) {
                        var lv2item = {
                            label: lv2.baseCategoryName,
                            value: lv2.baseCategoryId,
                            child: []
                        };

                        if (lv2.thirdCategoryList) {
                            $.each(lv2.thirdCategoryList, function (iii, lv3) {
                                lv2item.child.push({
                                    label: lv3.baseCategoryName,
                                    value: lv3.baseCategoryId,
                                    baseCategoryType: lv3.baseCategoryType
                                })
                            })
                        }

                        if (lv2item.child.length) {
                            lv1item.child.push(lv2item);
                        }
                    })
                }

                if (lv1item.child.length) {
                    resData.push(lv1item);
                }
            });

            return resData;
        },
        searchList: [
            {
                label: '分类',
                name: 'label',
                // width: '380px'
            },
            // {
            //     label: '分类类型',
            //     name: 'typeName',
            //     width: ''
            // }
        ],
        parseSearchData: function (res) {
            var resData = [];

            $.each(res.listData, function (i, item) {
                resData.push({
                    value: item.baseCategoryId,
                    label: item.categoryJoinName,
                    baseCategoryType: item.baseCategoryType,
                    // typeName: item.baseCategoryType == 1 ? '医疗器械' : '非医疗器械'
                })
            })

            return resData;
        },
        historyName: 'prod_category',
        onselect: function (obj, params) {
            if ($('.J-attr-wrap').length) {
                $.ajax({
                    url: page_url + '/goods/vgoods/getAttributeInfoByCategoryId.do',
                    data: {
                        categoryId: obj.value
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code == 0) {
                            refreshAttr(res.data || []);
                        }
                    }
                })
            }

            historyName = {
                value: obj.value,
                label: obj.label,
                baseCategoryType: params.baseCategoryType,
                // typeName: params.baseCategoryType == 1 ? '医疗器械' : '非医疗器械'
            };

            setTimeout(function(){
                $('.J-category-value').valid();
            }, 100);
        }
    })

    //spu自动带入
    var $common = $('.J-common-name');
    var setProdName = function () {
        var $brand = $('.J-brandId');
        var $prodName = $('.J-prod-name');
        var level = $('.J-spu-level').val();

        if (level != 2) {
            if ($common.length && $common.val() && $brand.val() && !$prodName.val()) {
                $prodName.val($.trim($('.J-brand-wrap .J-text').html()) +' '+ $common.val()).valid();
            }
        } else {
            if ($brand.val() && !$prodName.val()) {
                $prodName.val($.trim($('.J-brand-wrap .J-text').html())).valid();
            }
        }
    };

    if ($common.length) {
        $common.blur(function () {
            setProdName();
        })
    }


    /** 标签 */
    //设置传给后台的标签的值
    var setTagVal = function () {
        var values = [];
        $('.J-tag-wrap .J-tag-item').each(function () {
            values.push($(this).data('value'));
        })

        $('.J-tag-value').val(values.join('@_@'));
    };

    //新增一个标签
    var addTag = function (val) {
        $('.J-tag-wrap').append('<div class="tag-item J-tag-item" data-value="' + val + '">' + val + '<i class="vd-icon icon-delete J-tag-del"></i></div>');
        setTagVal();
    };

    //初始化已添加的标签
    var tagVal = $('.J-tag-value').val();

    if (tagVal) {
        $.each(tagVal.split('@_@'), function (i, obj) {
            addTag(obj);
        });
    }

    //隐藏添加标签的输入框
    var hideTagInput = function () {
        $('.J-tag-new').hide();
        $('.J-tag-add').show();
        $('.J-tag-input').val('');
    };

    //点击添加标签
    $('.J-tag-add').click(function () {
        $('.J-tag-new').show();
        $(this).hide();
    })

    //点击取消
    $('.J-tag-add-cancel').click(function () {
        hideTagInput();
    })

    //点击确定
    $('.J-tag-add-confirm').click(function () {
        var value = $.trim($('.J-tag-input').val());
        if (value) {
            addTag(value);
            hideTagInput();
        } else {
            $('.J-tag-input').focus();
        }
    })

    //输入框回车
    $('.J-tag-input').keydown(function (e) {
        if (e.keyCode === 13 || e.keyCode === 108) {
            addTag($.trim($(this).val()));
            hideTagInput();
            return false;
        }
    })

    //删除已添加的标签
    $('.J-tag-wrap').on('click', '.J-tag-del', function () {
        $(this).parents('.J-tag-item:first').remove();
        setTagVal();
    })

    //点击建议的标签
    $('.J-tag-list').on('click', '.J-tag-item', function () {
        addTag($(this).data('value'));
    })

    //初始化品牌信息  
    new SuggestSelect({
        placeholder: '请选择商品品牌',
        wrap: '.J-brand-wrap',
        input: '.J-brandId',
        searchUrl: page_url + '/firstengage/brand/brandName.do?pageSize=10',
        asyncSearch: true,
        asyncSearchName: 'brandName',
        dataparse: function (data) {
            var resData = [];
            $.each(data.listData, function (i, obj) {
                resData.push({
                    label: obj.brandName,
                    value: obj.brandId
                })
            })

            return resData;
        },
        onchange: function (data) {
            $('.J-brandId').valid();
            setProdName();
        }
    })

    if ($('.J-brand-wrap').data('name')){
        $('.J-brand-wrap .J-text').html($('.J-brand-wrap').data('name'));
    }

    //商品类型切换
    var checkProdType = function () {
        var firstLevelGoodsType = $('.J-prod-type:checked').val();

        //是否展示"是否为大型医疗器械"选项
        largeMedicalInstrumentSwitch(firstLevelGoodsType)
    };

    //切换商品类型
    $('.J-prod-type').change(function () {
        checkProdType();
    });


    var autoRegistrationCertNameEnable = $('input[name=firstEngageId]').val()==''||$('input[name=firstEngageId]').val()=='0'

    //首营信息
    var firstTmpl = template($('.J-first-tmpl').html());
    var refreshFirstenage = function (id, relabel) {
        if (id == ''|| id <= 0) {
            return
        }

        $.ajax({
            url: page_url + '/goods/vgoods/getFirstEngageById.do',
            data: {
                firstEngageId: id
            },
            dataType: 'json',
            success: function (res) {
                if (res.code == 0) {
                    $('.J-first-detail').empty();
                    $('.J-first-detail').append(firstTmpl(res.data)).show();
                    if (relabel) {
                        $('.J-firstenage-select .J-text').html(res.data.registrationNumber);
                    }

                    //if any, then 自动代入注册证的产品名称
                    if (res.data.productChineseName) {
                        var productChineseName = res.data.productChineseName;
                        //当注册证中的产品名称不为空时，自动代入到spu的通用名和
                        var brandFullName = $('.J-brand-wrap .select-selected .J-text ').html();
                        if(brandFullName !=='' && $('input[name=brandId]').val()!==''){
                            $('input[name=showName]').val(brandFullName.trim() + " " + productChineseName.trim())
                        }

                        if(autoRegistrationCertNameEnable){
                            $('input[name=spuName]').val(productChineseName.trim())
                        }
                    }
                    //if any, then 自动代入注册证的规格、型号
                    if (res.data.specsModel) {
                        if (autoRegistrationCertNameEnable) {
                            $('input[name=specsModel]').val(res.data.specsModel)
                        }
                    }

                    autoRegistrationCertNameEnable=true
                }else {
                    artDialog.alert('加载注册证信息失败，请重新选择注册信息', '系统错误');
                }
            },error:function(res){
                artDialog.alert("系统异常,请联系管理员", '系统错误');
                console.log(res)
            }
        })
    };

    /**
     * 页面初始化时，加载注册证信息
     */
    if ($('.J-firstenage-value').val()!==''&&$('.J-firstenage-value').val() != 0) {
        refreshFirstenage($('.J-firstenage-value').val(), true);
    }

    new DialogSelect({
        el: '.J-firstenage-select',
        title: '请选择注册证号/备案凭证号',
        placeholder: '请输入注册证号/备案凭证号/生产企业/商品品牌/产品名称',
        input: '.J-firstenage-value',
        searchList: [{
            label: '注册证号/备案凭证号',
            name: 'registrationNumber',
            width: '150px'
        },
        {
            label: '生产企业',
            name: 'productCompanyChineseName',
            width: '150px'
        },
        {
            label: '商品品牌',
            name: 'brandName',
            width: ''
        },
        {
            label: '产品名称（中文）',
            name: 'productChineseName',
            width: ''
        }],
        dataUrl: page_url + '/goods/vgoods/searchFirstEngages.do?',
        searchName: 'searchValue',
        valueName: 'firstEngageId',
        searchInitVal: function(){
            return $('.J-brandId').val() ? $.trim($('.J-brand-wrap .J-text').html()) : ''
        },
        onselect: function (id, obj) {
            refreshFirstenage(id);
            $('.J-firstenage-select .J-text').html(obj.registrationNumber);
            $('.J-firstenage-value').valid();
        }
    })

    window.localStorage.setItem('addSpuSuccess', '')

    //增加关闭提示
    GLOBAL.addtip();


    var notRestItemName =['goodsLevelNo', 'goodsPositionNo']

    /**
     * 获取验证SKU字段规则
     */
    function loadGoodsValidationRule() {
        var goodsLevelNo = parseInt($('select[name=goodsLevelNo]').val());
        if(isNaN(goodsLevelNo) || goodsLevelNo<=0) {
            return
        }

        $('span.must').each(function () {
            $(this).show();
        });

        $.ajax({
            url: page_url + '/goods/vgoods/goodsValidatedRule.do',
            data: {
                targetValidType: 1,
                goodsLevelNo: goodsLevelNo
            },
            dataType: 'json',
            success: function (res) {
                if(res !== null && res.code===0){
                    //动态生成页面校验规则
                    var ruleInJson = res.data;

                    var basicItems = ruleInJson.basicItems;

                    next:
                        for (var i=0; i < basicItems.length; i++) {
                            var elem = basicItems[i];
                            var required = elem .required;

                            var propertyName = elem.propertyName;
                            if (propertyName == null || propertyName === '') {
                                continue
                            }
                            var target = $("[name=" + propertyName + "]")
                            if (target.length===0) {
                                console.info("not found property - name: "+ propertyName);
                                continue
                            }

                            if (!required) {
                                var removeFlag = target.rules('remove', 'required');
                                if (removeFlag.require) {
                                    continue next;
                                }

                                var parentNode = target.parent();
                                while (parentNode.length > 0) {
                                    if (parentNode.hasClass('form-fields')) {
                                        break
                                    }
                                    parentNode=parentNode.parent()
                                }

                                if (!parentNode.length) {
                                    continue
                                }

                                var targetSpan = parentNode.prev().find('.must')
                                if( targetSpan.length > 0) {
                                    targetSpan.hide();
                                }
                            } else {
                                target.rules('add',{
                                    required : true,
                                    messages : {
                                        required: elem.message
                                    }
                                });
                            }
                        }
                } else {
                    artDialog.alert(res.message);
                }
            },error:function(res){
                artDialog.alert("系统异常,请联系管理员", '系统错误');
            }
        });
    }

    //编辑页面自动代入校验规则
    if (supUpdatedFlag) {
        loadGoodsValidationRule();

        //编辑页面切换等级时，异步加载规则
        $('#goodsLevelSelect').change(function () {
            loadGoodsValidationRule();
        });
    } else {
        //新增页面
        var addSpuSteps = steps({
            el: "#addSteps",
            space: 1000,
            center: true,
            data: [
                { title: "SPU创建", description: ""},
                { title: "信息配置" , description: ""},
                { title: "完成", description: ""}
            ],
            active: 0
        });

        $('#nextStepBtn').click(function () {
            var elements=["input[name=categoryId]", "select[name=goodsLevelNo]", "select[name=goodsPositionNo]"];
            console.log("elemnts:", elements);
            var allSuccess=true;
            for(var i = 0; i < elements.length; i++) {
                var flag = $(".J-form").validate().element($(elements[i]));
                if(allSuccess && !flag) {
                    allSuccess=false
                }
            }

            if(!allSuccess){
                return
            }
            // let categoryId = document.getElementsByName('categoryId')[0].value;
            // console.log("categoryId:", categoryId);
            //
            // //加载商品等级
            // $.ajax({
            //     url: page_url + '/goodsCategory/getByBaseCategoryId.do',
            //     dataType: 'json',
            //     data: {"baseCategoryId": categoryId},
            //     success: function (res) {
            //         if (res.code == 0 && res.data != null) {
            //             document.getElementsByName('taxClassificationCode')[0].value = res.data.taxClassificationCode;
            //             document.getElementsByName('taxCodeSimpleName')[0].value = res.data.taxCodeSimpleName;
            //         }
            //     }
            // });

            $('#first-step-tips').hide();
            //加载页面校验规则
            loadGoodsValidationRule();
            setStep(1)
            $('.first-step').hide()
            $('.second-step').show()
        });

        $('#lastStepBtn').click(function () {
            var dialog = artDialog.confirm('返回上一步后已填写内容将会丢失，确定返回？', '', {
                fn: function () {
                    setStep(0)
                    $('.first-step').show()
                    $('.second-step').hide()

                    $('span.must').each(function () {
                        $(this).show();
                    });

                    $('#first-step-tips').show();
                    //清除历史错误提示信息
                    validator.resetForm();

                    //清空表单已填写数据
                    $(".J-form").find('input[type=text]').each(function() {
                        var currentName = $(this).attr('name')
                        if (notRestItemName.indexOf(currentName) >= 0) {
                            return
                        } else {
                            $(this).val('');
                        }
                    });

                }, text: '确认'
            }, {
                fn: function () {
                    dialog.close();
                }, text: '取消'
            });
        });

        function setStep(step) {
            addSpuSteps.setActive(step);
        }


        var goodsLevelMap = new Map;
        var goodsPositionMap = new Map;

        //加载商品等级
        $.ajax({
            url: page_url + '/goods/vgoods/listGoodsLevel.do',
            dataType: 'json',
            success: function (res) {
                if (res.code == 0 && res.data != null) {
                    $('select[name=goodsLevelNo]').prepend("<option value>请选择</option>");
                    for (var i = 0; i < res.data.length; i++) {
                        goodsLevelMap.set(res.data[i].id, res.data[i]);
                        var opt="<option value=\'"+res.data[i].id+"\'>"+res.data[i].uniqueIdentifier +"-"+ res.data[i].levelName + "</option>";
                        $('select[name=goodsLevelNo]').append(opt);
                    }
                    Select.use('.J-select-level');
                }
            }
        });


        //加载商品档位
        $.ajax({
            url: page_url + '/goods/vgoods/listGoodsPosition.do',
            dataType: 'json',
            success: function (res) {
                if (res.code == 0 && res.data != null) {
                    $('select[name=goodsPositionNo]').prepend("<option value>请选择</option>");
                    for (var i = 0; i < res.data.length; i++) {
                        goodsPositionMap.set(res.data[i].id, res.data[i]);
                        var opt="<option value=\'"+res.data[i].id+"\' >"+res.data[i].positionName+"</option>";
                        $('select[name=goodsPositionNo]').append(opt);
                    }
                    Select.use('.J-select-position');
                }
            }
        });


        $('select[name=goodsLevelNo]').change(function () {
            var target= $(this).parent().find('.form-fields-tip-blue');
            var value=$(this).val();
            var levelInfo = goodsLevelMap.get(parseInt(value));
            if (levelInfo==null) {
                target.hide();
                return
            }

            var todoItems=levelInfo.todoItems;

            var todoItemsContent ='';
            if (todoItems.length > 0) {
                for (var i = 0; i < todoItems.length; i++) {
                    todoItemsContent+= "<span class=\"spu-badge spu-bg-blue\" style='margin-left: 2px'>"+todoItems[i]+"</span>"
                }
            }

            if (todoItemsContent==='') {
                todoItemsContent = '无';
            }
            var tip = "&nbsp;&nbsp;等级说明：" + levelInfo.description + "<br>&nbsp;&nbsp;必办事项："+ todoItemsContent;
            target.html(tip);
            target.show();
        });

        $('select[name=goodsPositionNo]').change(function () {
            var target= $(this).parent().find('.form-fields-tip-blue');
            var value=$(this).val();
            var positionInfo = goodsPositionMap.get(parseInt(value));
            if (positionInfo==null) {
                target.hide();
                return
            }
            var tip = "&nbsp;&nbsp;档位说明：" + positionInfo.description + "<br>&nbsp;&nbsp;档位规则：<span class=\"spu-badge spu-bg-blue\">"+positionInfo.signContractModeRule+"</span>";
            target.html(tip);
            target.show();
        });

        $('#addSkuBtn').click(function () {
            window.location.href = page_url+'/goods/vgoods/addSku.do?spuId='+$('#spuIdAftSuccess').val();
        });

        $('#viewSpuBtn').click(function () {
            window.location.href = page_url+'/goods/vgoods/viewSpu.do?spuId='+$('#spuIdAftSuccess').val();
        });
    }
    chooseNoMedicalType();
})

/**
 * 有无注册证切换开关
 */
function registrationCertSwitch() {
    var switchFlag=$("input[name='hasRegistrationCert']:checked").val()
    if(switchFlag==='1'){
        $('.J-first-block').css("display","block")
        $('input[name="firstEngageId"]').removeAttr("disabled")
    }else {
        $('.J-first-block').css("display","none")
        $('input[name="firstEngageId"]').attr("disabled",true)
    }

    registrationCertNameAndSpecSwitch(switchFlag)

    //是否展示"是否为大型医疗器械"选项
    largeMedicalInstrumentSwitch($('.J-prod-type:checked').val())
}

function chooseNoMedicalType() {
    let medicalInstrumentCatalogIncluded = $("input[name='medicalInstrumentCatalogIncluded']:checked").val();
    if (medicalInstrumentCatalogIncluded === '0') {
        $('.firstType').css("display","block");
        $('.secondType').css("display","block");
        $('select[name=noMedicalFirstType]').removeAttr("disabled");
        $('select[name=noMedicalSecondType]').removeAttr("disabled");
    } else {
        $('.firstType').css("display","none");
        $('.secondType').css("display","none");
        $('select[name=noMedicalFirstType]').attr("disabled",true);
        $('select[name=noMedicalSecondType]').attr("disabled",true);
    }

}

function changeNoMedicalSecondType() {
    let spuType = $("input[name='spuType']:checked").val();
    let medicalInstrumentCatalogIncluded = $("input[name='medicalInstrumentCatalogIncluded']:checked").val();
    let noMedicalFirstType = $("select[name='noMedicalFirstType']").val();
    if (medicalInstrumentCatalogIncluded == 0 && noMedicalFirstType == 1) {
        if (spuType == 316) {
            $('#noMedicalSecondType').val('3');
        } else if (spuType == 1008) {
            $('#noMedicalSecondType').val('1');
        } else if (spuType == 317 || spuType == 318) {
            $('#noMedicalSecondType').val('2');
        } else {
            $('#noMedicalSecondType').val('0');
        }

    }
}


/**
 * 展示/隐藏产品名称和规格型号
 * @param on
 */
function registrationCertNameAndSpecSwitch(on) {
    if(on==='1'){
        $('.J-registration-specs').css('display','block')
        $('.J-registration-name').css('display','block')
        $('input[name=spuName]').removeAttr("disabled")
        $('input[name=specsModel]').removeAttr("disabled")
    }else {
        $('.J-registration-specs').css('display','none')
        $('.J-registration-name').css('display','none')
        $('input[name=spuName]').attr("disabled",true)
        $('input[name=specsModel]').attr("disabled",true)
    }
}

//页面加载阶段触发
registrationCertNameAndSpecSwitch($("input[name='hasRegistrationCert']:checked").val())

function largeMedicalInstrumentSwitch(firstLevelGoodsType) {
    var hasRegistrationCert = $("input[name='hasRegistrationCert']:checked").val()=='1'

    //当"商品类型"，决定二级商品类型（目前只有"大型医疗器械"，便于后期扩展）
    if( firstLevelGoodsType==316 && hasRegistrationCert) {
        $('.J-large-facility-block').css("display","block")
    }else {
        $('.J-large-facility-block').css("display","none")
        //当"商品类型"不为"设备'时，将二级商品类型（目前只有"大型医疗器械"，便于后期扩展）置为"否'
        $.each($('input[name=secondLevelSpuType]') , function (i, item) {
            if(item.value == 0){
                item.checked=true
            }
        })
    }
}

//初始首营的编号
initialFirstEngageId = $('input[name=firstEngageId]').val();

function checkSpuNameWhenSwitchRegisterNumberCert(form, callback) {
    var hasSpuCount=-1;

    //产看spu名称是否重复
    $.ajax({
        async: false,
        type:'get',
        url: page_url + '/goods/vgoods/checkSpuNameIfDuplicate.do',
        data: {
            firstEngageId: $('input[name=firstEngageId]').val()
        },
        dataType: 'json',
        success: function (res) {
            if (res.code === 0) {
                hasSpuCount=parseInt(res.data)
            }else {
                hasSpuCount = -1;
            }
        },error: function(data){
            console.log("请求接口失败:"+data)
        }
    });

    if (hasSpuCount > 0) {
        var dialog = artDialog.tip('该注册证下已经注册了'+ hasSpuCount +'个spu,请确认该spu是否重复创建', '提示', {
            cancel: function () {
                var selector =$('#simulativeLink')
                if (selector !== undefined) {
                    selector.text("");
                    selector.append("<a id='queryGoodsListLink' tabTitle='{\"num\":\"viewCategory\",\"link\":\"/goods/vgoods/list.do?firstEngageId= "+ $('input[name=firstEngageId]').val() +"\", \"title\":\"新商品流列表\", \"random\": \"1\"}' href=\"javascript:void(0);\"></a>");
                }
                var targetLink =document.getElementById('queryGoodsListLink');
                if(targetLink!==undefined){
                    targetLink.click();
                }
            },
                cancelVal: '核查SPU',
                ok: function () {
                if(callback !== undefined){
                    callback(form);
                }
                dialog.close();
            }, okVal: '继续提交'
        });
    }else if(hasSpuCount === 0) {
        if(callback !== undefined){
            callback(form);
        }
    }else {
        artDialog.alert('校验spu名称时发生错误，请重试！', '系统错误');
    }
}


//关闭当前页面
$('.close-spu-edit').click(function () {
    if(parentWindow.find('.title .active .glyphicon').length > 0){
        var closeTab = parentWindow.find('.title .active .glyphicon').get(0);
        $(closeTab).trigger('click');
    }
});
