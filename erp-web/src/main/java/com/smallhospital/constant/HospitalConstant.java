package com.smallhospital.constant;


public class HospitalConstant {

    /**
     * 终止合同审核中销售主管审核
     */
    public static final Integer SALEMANAGER_TERMINATE_STATUS=1;

    /**
     * 合同未终止
     */
    public static final Integer CONTRACT_NOT_TERMINATION=0;
    /**
     * 终止合同审核中客户审核
     */
    public static final Integer CUSTOMER_TERMINATE_STATUS=2;

    /**
     * 终止合同审核通过
     */
    public static final Integer TERMINATE_CHECK_PASS=3;
    /**
     * 终止合同审核不通过
     */
    public static final Integer TERMINATE_CHECK_REJECT=4;


    public static final Integer CONTRACT_SYNC_SUCCESS=1;
    /**
     * 终止合同审核中销售主管审核类型
     */
    public static final Integer MANAGER_TERMINATE_CHECK_TYPE=1;
    /**
     * 终止合同审核中客户审核类型
     */
    public static final Integer CUSTOMER_TERMINATE_CHECK_TYPE=2;

    /**
     * 审核通过
     */
    public static final Integer AUDIT_STATUS_PASSED=2;
    /**
     * 审核不通过
     */
    public static final Integer AUDIT_STATUS_REJECTED=3;

    /**
     * 合同未被修改
     */
    public static final Integer CONTRACT_UNMODIFY=0;

    /**
     * 合同正在修改中
     */
    public static final Integer CONTRACT_ONMODIFY=1;
    /**
     * 合同待审核
     */
    public static final Integer CONTRACT_NOT_AUDIT=0;

    /**
     * 合同有效的
     */
    public static final Integer CONTRACT_IS_OFFECTIVE=1;
    /**
     * 合同无效的
     */
    public static final Integer CONTRACT_NOT_OFFECTIVE=0;
    /**
     * 审核通过
     */
    public static final Integer CONTRACT_AUDIT_PASS=2;
    /**
     * 审核不通过
     */
    public static final Integer CONTRACT_AUDIT_UNPASS=3;

    /**
     * 合同产品未同步
     */
    public static final Integer CONTRACT_PRO_UNSYNC=0;
    /**
     * 合同未同步
     */
    public static final Integer CONTRACT_UNSYNC=0;

    /**
     * 合同未确认
     */
    public static final Integer CONTRACT_UNCONFIRM=0;
}
