!function(undefined){"use strict";var _global,utils_extend=function(o,n,override){for(var key in n)!n.hasOwnProperty(key)||o.hasOwnProperty(key)&&!override||(o[key]=n[key]);return o},steps=function(options){return new _steps(options)};function _steps(options){this._initial(options)}_steps.prototype={constructor:this,_initial:function(options){var _this=this,defined={el:"",data:[],props:{title:"title",description:"description",status:"status",side:"side",icon:"icon",customHtml:"customHtml"},space:null,direction:"horizontal",sides:"single",iconType:"number",center:!1,active:0,dataSetStatus:!1,defaultClass:"step-default-class",finishClass:"step-finish-class",finishLine:!0,finishLineClass:"step-finish-line-bg",customClass:""};return this.options=utils_extend(defined,options,!0),this.options.el?this.options.data&&0!=this.options.data.length?(this.options.dataLength=this.options.data.length,void this.render()):(alert("请传入'data'参数"),!1):(alert("请传入'el'参数"),!1)},getBoxClass:function(){var _this=this,boxClass="";return"horizontal"==this.options.direction.toLowerCase()?(boxClass+="steps-horizontal",this.options.center&&(boxClass+=" steps-center")):"vertical"==this.options.direction.toLowerCase()?boxClass+="steps-vertical":alert("参数'direction'错误"),boxClass},getParentNode:function(){var _this=this,resultEl;return"object"==typeof this.options.el?resultEl=this.options.el:"string"==typeof this.options.el&&(resultEl=document.querySelector(this.options.el)),resultEl},render:function(){var _this=this,boxHtml="",parentNode=_this.getParentNode();parentNode.className=parentNode.className+_this.options.customClass,_this.options.boxClass=_this.getBoxClass(),boxHtml+='<div class="steps {{boxClass}}">'.replace("{{boxClass}}",_this.options.boxClass);var stepsStartHtml="",stepsCentertHtml="",stepsEndtHtml="",stepsStartStyle="",stepsStartClass="",stepsCenterClass="",stepsEndStyle="",stepsEndClass="";if("single"==_this.options.sides.toLowerCase()||"end-sigle"==_this.options.sides.toLowerCase()?(stepsStartStyle+="display: none;","vertical"==_this.options.direction.toLowerCase()&&(stepsEndStyle+="flex-basis: 100%;")):"start-single"==_this.options.sides.toLowerCase()&&(stepsEndStyle+="display: none;","vertical"==_this.options.direction.toLowerCase()&&(stepsStartStyle+="flex-basis: 100%;")),"horizontal"==_this.options.direction.toLowerCase()?(stepsStartClass="steps-row steps-row-start",stepsCenterClass="steps-row steps-row-center",stepsEndClass="steps-row steps-row-end"):"vertical"==_this.options.direction.toLowerCase()&&(stepsStartClass="steps-column steps-column-start",stepsCenterClass="steps-column steps-column-center",stepsEndClass="steps-column steps-column-end"),stepsStartHtml+=stepsStartStyle?'<div class="'+stepsStartClass+'" style="{{stepsStartStyle}}">'.replace("{{stepsStartStyle}}",stepsStartStyle):'<div class="'+stepsStartClass+'">',stepsCentertHtml+='<div class="'+stepsCenterClass+'">',stepsEndtHtml+=stepsEndStyle?'<div class="'+stepsEndClass+'" style="{{stepsEndStyle}}">'.replace("{{stepsEndStyle}}",stepsEndStyle):'<div class="'+stepsEndClass+'">',_this.options.data.forEach(function(currentValue,index,array){var stepBox;(currentValue[_this.options.props.status]||0==currentValue[_this.options.props.status])&&(_this.options.dataSetStatus=!0),stepBox=(stepBox='<div class="step {{stepClass}}" style="{{stepStyle}}">{{stepHtml}}</div>'.replace("{{stepClass}}",_this.options.defaultClass+" "+(currentValue[_this.options.props.status]?_this.options.finishClass:!_this.options.dataSetStatus&&index<=_this.options.active?_this.options.finishClass:""))).replace("{{stepStyle}}",_this.getStepStyle(index).join(""));var stepIconClass="",stepIconInnerClass="",stepIconInnerText="";"number"==_this.options.iconType.toLowerCase()?(stepIconInnerClass="step-icon-number",stepIconInnerText=index+1):"bullets"==_this.options.iconType.toLowerCase()?stepIconInnerClass="step-icon-bullets":"custom"==_this.options.iconType.toLowerCase()&&(stepIconClass="step-icon-custom-box",stepIconInnerClass="step-icon-custom",stepIconInnerText=currentValue[_this.options.props.icon]?currentValue[_this.options.props.icon]:index+1);var stepHead='<div class="step-head"><div class="step-line {{finishLineClass}}"></div><div class="step-icon {{stepIconClass}}"><div class="{{stepIconInnerClass}}">{{stepIconInnerText}}</div></div></div>'.replace("{{stepIconClass}}",stepIconClass).replace("{{stepIconInnerClass}}",stepIconInnerClass).replace("{{stepIconInnerText}}",stepIconInnerText),stepStartBody='<div class="step-body" style="{{bodyHidden}}">'+(currentValue[_this.options.props.customHtml]?currentValue[_this.options.props.customHtml]:"")+'<div class="step-description">{{stepDesc}}</div><div class="step-title">{{stepTitle}}</div></div>'.replace("{{stepTitle}}",currentValue[_this.options.props.title]).replace("{{stepDesc}}",currentValue[_this.options.props.description]),stepEndBody=('<div class="step-body" style="{{bodyHidden}}"><div class="step-title">{{stepTitle}}</div><div class="step-description">{{stepDesc}}</div>'+(currentValue[_this.options.props.customHtml]?currentValue[_this.options.props.customHtml]:"")+"</div>").replace("{{stepTitle}}",currentValue[_this.options.props.title]).replace("{{stepDesc}}",currentValue[_this.options.props.description]),bodyHidden="visibility: hidden;max-height: 100%;",bodyCenterHidden="visibility: hidden;height: 0;";"start-single"==_this.options.sides.toLowerCase()?(stepsStartHtml+=stepBox.replace("{{stepHtml}}","horizontal"==_this.options.direction?stepStartBody.replace("{{bodyHidden}}",""):stepEndBody.replace("{{bodyHidden}}","")),stepsEndtHtml+=index==_this.options.dataLength-1?stepBox.replace("{{stepHtml}}",stepEndBody.replace("{{bodyHidden}}",bodyHidden)):stepBox.replace("{{stepHtml}}","")):currentValue[_this.options.props.side]&&"single"!=currentValue[_this.options.props.side]&&"end-single"!=currentValue[_this.options.props.side]?"start-single"==currentValue[_this.options.props.side]&&(stepsStartHtml+=stepBox.replace("{{stepHtml}}","horizontal"==_this.options.direction?stepStartBody.replace("{{bodyHidden}}",""):stepEndBody.replace("{{bodyHidden}}","")),stepsEndtHtml+=index==_this.options.dataLength-1?stepBox.replace("{{stepHtml}}",stepEndBody.replace("{{bodyHidden}}",bodyHidden)):stepBox.replace("{{stepHtml}}","")):(stepsStartHtml+=index==_this.options.dataLength-1?stepBox.replace("{{stepHtml}}","horizontal"==_this.options.direction?stepStartBody.replace("{{bodyHidden}}",bodyHidden):stepEndBody.replace("{{bodyHidden}}",bodyHidden)):stepBox.replace("{{stepHtml}}",""),stepsEndtHtml+=stepBox.replace("{{stepHtml}}",stepEndBody.replace("{{bodyHidden}}",""))),stepsCentertHtml+=stepBox.replace("{{stepHtml}}","horizontal"==_this.options.direction?stepHead+stepEndBody.replace("{{bodyHidden}}",bodyCenterHidden):stepHead)}),stepsStartHtml+="</div>",stepsCentertHtml+="</div>",stepsEndtHtml+="</div>",_this.options.dataSetStatus)stepsCentertHtml=stepsCentertHtml.replace(/{{finishLineClass}}/g,"");else if(_this.options.finishLine)for(var i=0,len=_this.options.dataLength;i<len;i++)stepsCentertHtml=i<_this.options.active?stepsCentertHtml.replace(/{{finishLineClass}}/,_this.options.finishLineClass):stepsCentertHtml.replace(/{{finishLineClass}}/,"");else stepsCentertHtml=stepsCentertHtml.replace(/{{finishLineClass}}/g,"");parentNode.innerHTML=boxHtml+stepsStartHtml+stepsCentertHtml+stepsEndtHtml+"</div>","vertical"==_this.options.direction&&_this.resetVerticalLine()},getStepStyle:function(index){var _this=this,style=[],space="number"==typeof this.options.space?this.options.space+"px":this.options.space?this.options.space:100/(this.options.dataLength-(this.options.center?0:1))+"%";return style.push("flex-basis:"+space+";"),"vertical"==this.options.direction?style:(this.options.center||index!=this.options.dataLength-1||(style.length=0,style.push("flex-basis: auto!important;"),style.push("flex-shrink: 0;"),style.push("flex-grow: 0;"),style.push("max-width:"+100/this.options.dataLength+"%;")),style)},resetVerticalLine:function(){var _this=this,parentNode=this.getParentNode(),setHeight;"start-single"==this.options.sides||"two-sides"==this.options.sides?(setHeight=window.getComputedStyle(parentNode.querySelector(".steps-column-start").querySelector(".step:last-child")).height,parentNode.querySelector(".steps-column-center").querySelector(".step:last-child").style.flexBasis=setHeight):"single"!=this.options.sides.toLowerCase()&&"end-sigle"!=this.options.sides.toLowerCase()||(setHeight=window.getComputedStyle(parentNode.querySelector(".steps-column-end").querySelector(".step:last-child")).height,parentNode.querySelector(".steps-column-center").querySelector(".step:last-child").style.flexBasis=setHeight)},setActive:function(num){var _this=this;this.options.dataSetStatus?alert("参数'data'中已设置'status',参数'active'已停用"):(this.options.active=num,this.render())},getActive:function(){var _this=this;if(!this.options.dataSetStatus)return this.options.active;alert("参数'data'中已设置'status',参数'active'已停用")}},_global=function(){return this||(0,eval)("this")}(),"undefined"!=typeof module&&module.exports?module.exports=steps:"function"==typeof define&&define.amd?define(function(){return steps}):!("steps"in _global)&&(_global.steps=steps)}();