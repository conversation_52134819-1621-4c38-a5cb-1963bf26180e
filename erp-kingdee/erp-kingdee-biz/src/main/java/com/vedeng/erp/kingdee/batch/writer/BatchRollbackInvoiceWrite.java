package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceProcessDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 回滚发票处理器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchRollbackInvoiceWrite extends BaseWriter<BatchRollbackInvoiceProcessDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Override
    public void doWrite(BatchRollbackInvoiceProcessDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("金蝶回滚发票处理器 list:{}", JSON.toJSONString(item));

        OperateExtCommand operateExtCommand = new OperateExtCommand(item.getFormId(), item.getIds(), item.getOrgId(), null);
        kingDeeBaseApi.unAudit(operateExtCommand);
        ArrayList<SuccessEntity> delete = kingDeeBaseApi.delete(operateExtCommand);
        if (item.getIsPay()) {
            if (CollUtil.isNotEmpty(delete)) {

                List<BatchRInvoiceDetailJOperateLogDto> byInvoiceIds = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceIds(Collections.singletonList(item.getInvoiceId()));

                if (CollUtil.isNotEmpty(byInvoiceIds)) {
                    log.info("金蝶回滚发票删除货票关系,invoiceId:{},data:{}",item.getInvoiceId(),JSON.toJSONString(byInvoiceIds));
                    List<Integer> collect = byInvoiceIds.stream().map(BatchRInvoiceDetailJOperateLogDto::getRInvoiceDetailJOperateLogId).collect(Collectors.toList());
                    batchRInvoiceDetailJOperateLogDtoMapper.deleteByRInvoiceDetailJOperateLogId(collect);
                }

            }
        }

    }
}
