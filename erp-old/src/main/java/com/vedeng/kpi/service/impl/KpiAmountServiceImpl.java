package com.vedeng.kpi.service.impl;

import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiGroupConfigDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiDataQueryDto;
import com.vedeng.kpi.service.KpiAmountService;
import com.vedeng.kpi.service.KpiBaseService;
import com.vedeng.kpi.share.KpiCommonConstant;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 五行业绩处理.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/7/9 10:13 上午.
 * @author: Tomcat.Hui.
 */
@Service
public class KpiAmountServiceImpl extends KpiBaseService implements KpiAmountService {

    @Override
    public void dataHandler(KpiUserInfoDto userInfo) throws Exception {

        String kpiDateStr = getDateStr(userInfo.getKpiDateEnd());
        log.info("开始更新用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 "
                + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ);
        try {

            KpiDataQueryDto query = kpiParamTransService.baseParamsToQuery(userInfo);
            KpiDailyCountDo kpiDailyCount = kpiParamTransService.baseParamsToCount(userInfo);

            query.setUserIds(kpiLoadingCache.getGroupUserIdsByUserId(query.getUserId()));

            if (query.getUserIds().size() == 0) {
                throw new ShowErrorMsgException("用户 " + userInfo.getUserIdName() + " 所在团队用户数量为0") ;
            }

            if (! query.getUserIds().contains(userInfo.getUserId())) {
                throw new ShowErrorMsgException("用户 " + userInfo.getUserIdName() + " 所在团队用户数量为0") ;
            }

            //获取团队数据
            List<KpiDataQueryDto> groupKpiList = getGroupOrderAmountMonth();
            BigDecimal kpiAmount = groupKpiList.stream()
                    .filter(u -> u.getUserId().equals(userInfo.getUserId()))
                    .findFirst().map(KpiDataQueryDto::getKpiAmount).orElse(new NullKpiDataQueryDto().getKpiAmount());
            log.info("用户 " + userInfo.getUserIdName() + " 本次计算绩效值 " + kpiAmount);

            kpiDailyCount.setKpiAmount(null == kpiAmount ? BigDecimal.ZERO : kpiAmount);
            kpiDailyCount.setKpiAmountProgress(new BigDecimal(countAmountProgress(kpiAmount,userInfo.getUserId(),kpiDateStr))
                    .setScale(8, RoundingMode.HALF_UP));
            kpiDailyCount.setKpiAmountScore(new BigDecimal(countCurrentUserScore(userInfo,kpiDailyCount,groupKpiList,kpiDateStr))
                    .setScale(8, RoundingMode.HALF_UP));
            Boolean result = insertOrUpdateCount(kpiDailyCount);
            log.info("用户 " + userInfo.getUserIdName() + " 保存本次统计结果: " + result);

            log.info("更新用户 " + userInfo.getUserIdName() + " " + kpiDateStr + " 日 "
                    + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ + " 结束");

        }catch (Exception e){
           // log.error("用户 " + userInfo.getUserIdName() + "统计单个用户绩效数据出现异常: " , e);
            throw e;
        }
    }

    /**
     * 计算得分.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes:  得分=个人业绩完成率/团队平均业绩完成率*加权值（加权值=该项指标的考核比例*100）
     *          团队的业绩完成度=团队中个人的业绩完成度求和/团队的人数.
     * @version: 1.0.
     * @date: 2020/6/8 9:43 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @param kpiDailyCount: .
     * @param groupKpiList: .
     * @return: java.lang.Double.
     * @throws: .
     */
    protected Double countCurrentUserScore(KpiUserInfoDto userInfo,
                                           KpiDailyCountDo kpiDailyCount,
                                           List<KpiDataQueryDto> groupKpiList,
                                           String monthStr) {
        try {
            KpiGroupConfigDto groupConfig = kpiLoadingCache
                    .getGroupConfig(userInfo.getGroupId());

            BigDecimal weight = getWeight(userInfo.getGroupId(),KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ);

            log.info("用户 " + userInfo.getUserIdName() + " 所属团队 " + userInfo.getGroupName() + " "
                    + monthStr + " 月团队权重 " + weight);
            log.info("用户 " + userInfo.getUserIdName() + " 开始计算团队平均完成率............ ");
            Double averageKpiProgress = groupKpiList.stream()
                    .map(u -> countAmountProgress(u.getKpiAmount(),u.getUserId(),monthStr))
                    .collect(Collectors.summingDouble(Double::doubleValue)) /
                    kpiLoadingCache.getGroupUserIdsByUserId(userInfo.getUserId()).size();
            log.info("用户 " + userInfo.getUserIdName() + " 计算团队平均完成率结束............ ");
            log.info("用户 " + userInfo.getUserIdName() + " 团队 " + groupConfig.getGroupName() + " "
                    + monthStr + " 月团队平均进度 " + averageKpiProgress);
            Double result = averageKpiProgress.equals(new Double(0)) ?
                    0.0 : kpiDailyCount.getKpiAmountProgress().doubleValue() / averageKpiProgress * weight.doubleValue();
            log.info("用户 " + userInfo.getUserIdName() + " 本次计算得分 " + result);
            return result;
        }catch (Exception e){
            log.error("用户 " + userInfo.getUserIdName() + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ + " 计算个人得分出现异常: ",e);
            throw e;
        }
    }

    @Override
    public List<KpiDataQueryDto> getSendNotPaid(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getSendNotPaid(query);
    }

    @Override
    public List<KpiDataQueryDto> getPaidNotKpi(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getPaidNotKpi(query);
    }

    /**
     * 计算绩效完成率.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/4 9:33 下午.
     * @author: Tomcat.Hui.
     * @param amount: 绩效值.
     * @return: java.math.BigDecimal.
     * @throws: .
     */
    private Double countAmountProgress(BigDecimal amount,Integer userId,String monthStr){
        String userIdName = userId + "/" + kpiLoadingCache.getUserConfig(userId).getUserName();
        try {
            try {
                monthStr = getStartDateStr(monthStr);
            }catch (Exception e){
                log.error("用户 " + userIdName + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ + " 转换时间出现异常: ",e);
            }
            BigDecimal goalAmount = kpiLoadingCache.getHisTargetByUser(userId,monthStr);
            log.info("用户 " + userIdName + " " + monthStr +  " 月目标绩效 " + goalAmount + " 万");
            Double result = new BigDecimal(goalAmount.doubleValue()).compareTo(BigDecimal.ZERO) == 0
                    ? 0.0 : amount.doubleValue() / (goalAmount.doubleValue() * 10000);
            log.info("用户 " + userIdName + " 本次计算绩效进度 " + result);
            return result;
        }catch (Exception e){
            log.error("用户 " + userIdName + " " + KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ + " 计算个人得分出现异常: ",e);
            throw e;
        }
    }



}
