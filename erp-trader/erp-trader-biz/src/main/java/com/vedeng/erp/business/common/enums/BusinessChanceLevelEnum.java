package com.vedeng.erp.business.common.enums;

import lombok.Getter;

@Getter
public enum BusinessChanceLevelEnum {
    /**
     * S
     */
    S(939,"S"),
    /**
     * A
     */
    A(940,"A"),

    /**
     * B
     */
    B(941,"B"),

    /**
     * C
     */
    C(942,"C");

    /**
     * code
     */
    private final Integer code;

    /**
     * 等级
     */
    private final String title;

    BusinessChanceLevelEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }


    public static String getTitleByCode(Integer code) {
        for (BusinessChanceLevelEnum businessChanceLevelEnum : BusinessChanceLevelEnum.values()) {
            if (businessChanceLevelEnum.getCode().equals(code)) {
                return businessChanceLevelEnum.getTitle();
            }
        }
        return "";
    }

}
