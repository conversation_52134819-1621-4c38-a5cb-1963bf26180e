package com.vedeng.erp.saleorder.domain;

import java.math.BigDecimal;
import java.util.Date;

public class SaleorderAdditionalClause {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Integer saleorderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_NO
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String saleorderNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE_NOS
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String additionalClauseNos;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.INSTALLATION_ADDRESS
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String installationAddress;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_103
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String prodName103;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_105
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String prodName105;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.TRADER_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String traderName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Integer saleCityId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Integer saleProvinceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String saleProvinceName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String saleCityName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.TERMINAL_TRADER_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String terminalTraderName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.SN_CODE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String snCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.PREPAID_REAGENT_AMOUNT
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private BigDecimal prepaidReagentAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private String additionalClause;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.CREATOR
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.UPDATER
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Integer updater;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADD_TIME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Date addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.MODE_TIME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Date modeTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER_ADDITIONAL_CLAUSE.IS_DELETE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    private Integer isDelete;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.ID
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.ID
     *
     * @param id the value for T_SALEORDER_ADDITIONAL_CLAUSE.ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_ID
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Integer getSaleorderId() {
        return saleorderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_ID
     *
     * @param saleorderId the value for T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_NO
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_NO
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getSaleorderNo() {
        return saleorderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_NO
     *
     * @param saleorderNo the value for T_SALEORDER_ADDITIONAL_CLAUSE.SALEORDER_NO
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setSaleorderNo(String saleorderNo) {
        this.saleorderNo = saleorderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE_NOS
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE_NOS
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getAdditionalClauseNos() {
        return additionalClauseNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE_NOS
     *
     * @param additionalClauseNos the value for T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE_NOS
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setAdditionalClauseNos(String additionalClauseNos) {
        this.additionalClauseNos = additionalClauseNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.INSTALLATION_ADDRESS
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.INSTALLATION_ADDRESS
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getInstallationAddress() {
        return installationAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.INSTALLATION_ADDRESS
     *
     * @param installationAddress the value for T_SALEORDER_ADDITIONAL_CLAUSE.INSTALLATION_ADDRESS
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setInstallationAddress(String installationAddress) {
        this.installationAddress = installationAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_103
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_103
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getProdName103() {
        return prodName103;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_103
     *
     * @param prodName103 the value for T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_103
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setProdName103(String prodName103) {
        this.prodName103 = prodName103;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_105
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_105
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getProdName105() {
        return prodName105;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_105
     *
     * @param prodName105 the value for T_SALEORDER_ADDITIONAL_CLAUSE.PROD_NAME_105
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setProdName105(String prodName105) {
        this.prodName105 = prodName105;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.TRADER_NAME
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.TRADER_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getTraderName() {
        return traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.TRADER_NAME
     *
     * @param traderName the value for T_SALEORDER_ADDITIONAL_CLAUSE.TRADER_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_ID
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Integer getSaleCityId() {
        return saleCityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_ID
     *
     * @param saleCityId the value for T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setSaleCityId(Integer saleCityId) {
        this.saleCityId = saleCityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_ID
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Integer getSaleProvinceId() {
        return saleProvinceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_ID
     *
     * @param saleProvinceId the value for T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_ID
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setSaleProvinceId(Integer saleProvinceId) {
        this.saleProvinceId = saleProvinceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_NAME
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getSaleProvinceName() {
        return saleProvinceName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_NAME
     *
     * @param saleProvinceName the value for T_SALEORDER_ADDITIONAL_CLAUSE.SALE_PROVINCE_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setSaleProvinceName(String saleProvinceName) {
        this.saleProvinceName = saleProvinceName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_NAME
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getSaleCityName() {
        return saleCityName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_NAME
     *
     * @param saleCityName the value for T_SALEORDER_ADDITIONAL_CLAUSE.SALE_CITY_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setSaleCityName(String saleCityName) {
        this.saleCityName = saleCityName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.TERMINAL_TRADER_NAME
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.TERMINAL_TRADER_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getTerminalTraderName() {
        return terminalTraderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.TERMINAL_TRADER_NAME
     *
     * @param terminalTraderName the value for T_SALEORDER_ADDITIONAL_CLAUSE.TERMINAL_TRADER_NAME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setTerminalTraderName(String terminalTraderName) {
        this.terminalTraderName = terminalTraderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SN_CODE
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.SN_CODE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getSnCode() {
        return snCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.SN_CODE
     *
     * @param snCode the value for T_SALEORDER_ADDITIONAL_CLAUSE.SN_CODE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setSnCode(String snCode) {
        this.snCode = snCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.PREPAID_REAGENT_AMOUNT
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.PREPAID_REAGENT_AMOUNT
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public BigDecimal getPrepaidReagentAmount() {
        return prepaidReagentAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.PREPAID_REAGENT_AMOUNT
     *
     * @param prepaidReagentAmount the value for T_SALEORDER_ADDITIONAL_CLAUSE.PREPAID_REAGENT_AMOUNT
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setPrepaidReagentAmount(BigDecimal prepaidReagentAmount) {
        this.prepaidReagentAmount = prepaidReagentAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public String getAdditionalClause() {
        return additionalClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE
     *
     * @param additionalClause the value for T_SALEORDER_ADDITIONAL_CLAUSE.ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setAdditionalClause(String additionalClause) {
        this.additionalClause = additionalClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.CREATOR
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.CREATOR
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.CREATOR
     *
     * @param creator the value for T_SALEORDER_ADDITIONAL_CLAUSE.CREATOR
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.UPDATER
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.UPDATER
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.UPDATER
     *
     * @param updater the value for T_SALEORDER_ADDITIONAL_CLAUSE.UPDATER
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADD_TIME
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.ADD_TIME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.ADD_TIME
     *
     * @param addTime the value for T_SALEORDER_ADDITIONAL_CLAUSE.ADD_TIME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.MODE_TIME
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.MODE_TIME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Date getModeTime() {
        return modeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.MODE_TIME
     *
     * @param modeTime the value for T_SALEORDER_ADDITIONAL_CLAUSE.MODE_TIME
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.IS_DELETE
     *
     * @return the value of T_SALEORDER_ADDITIONAL_CLAUSE.IS_DELETE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER_ADDITIONAL_CLAUSE.IS_DELETE
     *
     * @param isDelete the value for T_SALEORDER_ADDITIONAL_CLAUSE.IS_DELETE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
}