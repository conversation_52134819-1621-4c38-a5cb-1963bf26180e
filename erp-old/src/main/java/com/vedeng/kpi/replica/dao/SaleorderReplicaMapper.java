package com.vedeng.kpi.replica.dao;

import com.vedeng.order.model.Saleorder;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/6/15 15:25
 */
@Named("saleorderReplicaMapper")
public interface SaleorderReplicaMapper {

    Saleorder getSaleorderDetails(@Param("saleorderNo") String saleorderNo);

    Saleorder getSaleorderDetailsBySaleorderId(Integer saleorderId);

    BigDecimal getTotalAmountOfSaleorder(@Param("saleorderNo") String saleorderNo);

    String getSaleorderNoBySaleorderGoodsId(@Param("saleorderGoodsId") Integer saleorderGoodsId);

    Saleorder getSaleorderByAfterSalesId(Integer afterSalesId);

    List<Integer> getSaleorderIdByAddTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("startSaleorderId") Integer startSaleorderId, @Param("offSet") Integer offSet, @Param("limit") Integer limit);

    List<Integer> getSaleorderIdByValidTimeOrModTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("startSaleorderId") Integer startSaleorderId, @Param("offSet") Integer offSet, @Param("limit") Integer limit);

    List<Saleorder> getSaleorderBySaleorderIdList(List<Integer> saleorderIdList);

    /**
     * 获取客户计入业绩订单个数
     * @param traderId 客户id
     * @return 个数
     */
    int getCountOfSaleorderInSalePerformanceByTraderId(@Param("traderId") Integer traderId, @Param("salePerformanceTime") Long salePerformanceTime, @Param("saleorderNo") String saleorderNo);

}
