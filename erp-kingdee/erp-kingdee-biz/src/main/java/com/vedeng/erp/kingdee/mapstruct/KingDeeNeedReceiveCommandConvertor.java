package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeNeedReceiveCommand;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveEntityDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 金蝶 应付余额调整单 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeeNeedReceiveCommandConvertor extends BaseCommandMapStruct<KingDeeNeedReceiveCommand,KingDeeNeedReceiveDto> {

    /**
     * KingDeeNeedReceiveCommand
     *
     * @param dto KingDeeNeedReceiveEntityDto
     * @return KingDeeNeedReceiveCommand
     */
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_VPFN_Date", source = "FVpfnDate")
    @Mapping(target = "f_VPFN_JG.FNumber", source = "FVpfnJg")
    @Mapping(target = "f_VPFN_KH.FNumber", source = "FVpfnKh")
    @Mapping(target = "FEntity",source = "FEntityList")
    @Override
    KingDeeNeedReceiveCommand toCommand(KingDeeNeedReceiveDto dto);

    /**
     * KingDeeNeedReceiveEntityCommand
     *
     * @param dto KingDeeNeedReceiveEntityDto
     * @return KingDeeNeedReceiveEntityCommand
     */
    @Mapping(target = "f_VPFN_YSDDH", source = "FVpfnYsddh")
    @Mapping(target = "f_VPFN_GSYWDH", source = "FVpfnGsywdh")
    @Mapping(target = "f_VPFN_YWLX", source = "FVpfnYwlx")
    @Mapping(target = "f_VPFN_TZJE", source = "FVpfnTzje")
    KingDeeNeedReceiveCommand.KingDeeNeedReceiveEntityCommand toCommand(KingDeeNeedReceiveEntityDto dto);

}

