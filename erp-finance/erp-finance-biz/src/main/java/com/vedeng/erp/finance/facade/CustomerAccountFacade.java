package com.vedeng.erp.finance.facade;

import com.vedeng.erp.finance.dto.RPayApplyJBankReceiptDto;
import com.vedeng.erp.finance.dto.CustomerAccountReqDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountApiDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountQueryReqDto;

import java.util.List;

public interface CustomerAccountFacade {

    /**
     * 售后提交根据回单创建客户账户
     * @param saleOrderId
     */
    void tryCreateCustomerAccount(Integer saleOrderId);

    /**
     * @param customerBankAccountId
     * @param queryReqDto
     * @param queryReqDto 账户类型 1银行 2微信 3支付宝
     */
    void updateLastUseTime(Long customerBankAccountId, CustomerBankAccountQueryReqDto queryReqDto);

    /**
     * 获取客户账户信息
     */
    List<CustomerBankAccountApiDto>  getCustomerBankAccount(CustomerAccountReqDto customerAccountReqDto);

    /**
     * 创建关联关系
     */
    void tryCreateRelation(RPayApplyJBankReceiptDto rPayApplyJBankReceiptDto);

    void tryCreateBankAlias(Integer payApplyId,Integer bankId);
}
