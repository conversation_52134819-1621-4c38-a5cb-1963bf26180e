package com.vedeng.goods.common.util;

/**
 * <AUTHOR>
 */
public class ByteUtil {

    /**
     * 根据int获取字节码数组(size:3)
     *
     * @param i 参数
     * @return 字节码数组
     */
    public static int[] getBitArrayByIntNumber(Integer i) {

        int[] result = {0, 0, 0};
        if (i == null) {
            return result;
        }
        result[0] = i & 1;
        result[1] = i >>> 1 & 1;
        result[2] = i >>> 2 & 1;
        return result;
    }
}
