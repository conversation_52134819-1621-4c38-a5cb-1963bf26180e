<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.business.mapper.BusinessChanceSeekHelpMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp">
    <!--@mbg.generated-->
    <!--@Table T_BUSINESS_CHANCE_SEEK_HELP-->
    <id column="BUSINESS_CHANCE_SEEK_HELP_ID" jdbcType="BIGINT" property="businessChanceSeekHelpId" />
    <result column="BUSINESS_CHANCE_ID" jdbcType="INTEGER" property="businessChanceId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUSINESS_CHANCE_SEEK_HELP_ID, BUSINESS_CHANCE_ID, USER_ID, CONTENT, `STATUS`, IS_DELETE, 
    ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUSINESS_CHANCE_SEEK_HELP
    where BUSINESS_CHANCE_SEEK_HELP_ID = #{businessChanceSeekHelpId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_BUSINESS_CHANCE_SEEK_HELP
    where BUSINESS_CHANCE_SEEK_HELP_ID = #{businessChanceSeekHelpId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BUSINESS_CHANCE_SEEK_HELP_ID" keyProperty="businessChanceSeekHelpId" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SEEK_HELP (BUSINESS_CHANCE_ID, USER_ID, CONTENT, 
      `STATUS`, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values (#{businessChanceId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="BUSINESS_CHANCE_SEEK_HELP_ID" keyProperty="businessChanceSeekHelpId" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SEEK_HELP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceId != null">
        BUSINESS_CHANCE_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessChanceId != null">
        #{businessChanceId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp">
    <!--@mbg.generated-->
    update T_BUSINESS_CHANCE_SEEK_HELP
    <set>
      <if test="businessChanceId != null">
        BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where BUSINESS_CHANCE_SEEK_HELP_ID = #{businessChanceSeekHelpId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp">
    <!--@mbg.generated-->
    update T_BUSINESS_CHANCE_SEEK_HELP
    set BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where BUSINESS_CHANCE_SEEK_HELP_ID = #{businessChanceSeekHelpId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BUSINESS_CHANCE_SEEK_HELP
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BUSINESS_CHANCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.businessChanceId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.userId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.content,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where BUSINESS_CHANCE_SEEK_HELP_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.businessChanceSeekHelpId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BUSINESS_CHANCE_SEEK_HELP
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BUSINESS_CHANCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessChanceId != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.businessChanceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.userId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.content != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.content,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when BUSINESS_CHANCE_SEEK_HELP_ID = #{item.businessChanceSeekHelpId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where BUSINESS_CHANCE_SEEK_HELP_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.businessChanceSeekHelpId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="BUSINESS_CHANCE_SEEK_HELP_ID" keyProperty="businessChanceSeekHelpId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SEEK_HELP
    (BUSINESS_CHANCE_ID, USER_ID, CONTENT, `STATUS`, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, 
      CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessChanceId,jdbcType=INTEGER}, #{item.userId,jdbcType=INTEGER}, #{item.content,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="BUSINESS_CHANCE_SEEK_HELP_ID" keyProperty="businessChanceSeekHelpId" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SEEK_HELP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceSeekHelpId != null">
        BUSINESS_CHANCE_SEEK_HELP_ID,
      </if>
      BUSINESS_CHANCE_ID,
      USER_ID,
      CONTENT,
      `STATUS`,
      IS_DELETE,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      CREATOR_NAME,
      UPDATER,
      UPDATER_NAME,
      UPDATE_REMARK,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceSeekHelpId != null">
        #{businessChanceSeekHelpId,jdbcType=BIGINT},
      </if>
      #{businessChanceId,jdbcType=INTEGER},
      #{userId,jdbcType=INTEGER},
      #{content,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER},
      #{isDelete,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updater,jdbcType=INTEGER},
      #{updaterName,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="businessChanceSeekHelpId != null">
        BUSINESS_CHANCE_SEEK_HELP_ID = #{businessChanceSeekHelpId,jdbcType=BIGINT},
      </if>
      BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="BUSINESS_CHANCE_SEEK_HELP_ID" keyProperty="businessChanceSeekHelpId" parameterType="com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_CHANCE_SEEK_HELP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceSeekHelpId != null">
        BUSINESS_CHANCE_SEEK_HELP_ID,
      </if>
      <if test="businessChanceId != null">
        BUSINESS_CHANCE_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessChanceSeekHelpId != null">
        #{businessChanceSeekHelpId,jdbcType=BIGINT},
      </if>
      <if test="businessChanceId != null">
        #{businessChanceId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="businessChanceSeekHelpId != null">
        BUSINESS_CHANCE_SEEK_HELP_ID = #{businessChanceSeekHelpId,jdbcType=BIGINT},
      </if>
      <if test="businessChanceId != null">
        BUSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    
<!--auto generated by MybatisCodeHelper on 2024-05-14-->
  <select id="countConsultationPendingByUserId" resultType="java.lang.Integer">
    select count(1)
    from T_BUSINESS_CHANCE_SEEK_HELP
    where USER_ID = #{userId,jdbcType=INTEGER}
    and IS_DELETE = 0
    and STATUS = 0
  </select>
    
  <select id="getConsultationPendingInfo"
            resultType="com.vedeng.erp.business.domain.dto.ConsultationPendingResponseDto">
    select TBCSH.BUSINESS_CHANCE_SEEK_HELP_ID,
           TBC.BUSSINESS_CHANCE_NO, 
           TBC.BUSSINESS_CHANCE_ID, 
           TBCSH.ADD_TIME,
           TBCSH.CREATOR_NAME,
           TBCSH.CONTENT,
           TBCSH.STATUS
    from T_BUSINESS_CHANCE_SEEK_HELP TBCSH
           left join T_BUSSINESS_CHANCE TBC  on TBC.BUSSINESS_CHANCE_ID = TBCSH.BUSINESS_CHANCE_ID
    where
      TBCSH.IS_DELETE = 0
      and TBCSH.USER_ID = #{param.userId,jdbcType=INTEGER}
    <if test="param.businessNo != null and param.businessNo != ''">
      and TBC.BUSSINESS_CHANCE_NO like concat('%',#{param.businessNo,jdbcType=VARCHAR},'%')
    </if>
    <if test="param.creatorName != null and param.creatorName != ''">
      and TBCSH.CREATOR_NAME like concat('%',#{param.creatorName,jdbcType=VARCHAR},'%')
    </if>
    <if test="param.status != null and param.status != -1">
      and TBCSH.STATUS = #{param.status,jdbcType=INTEGER}
    </if>
    <if test="param.addTime != null and param.addTime.size() > 0">
      and TBCSH.ADD_TIME between #{param.addTime[0],jdbcType=TIMESTAMP} and #{param.addTime[1],jdbcType=TIMESTAMP}
    </if>
    </select>
  
<!--auto generated by MybatisCodeHelper on 2024-05-23-->
  <update id="updateStatusByBusinessChanceSeekHelpId">
    update T_BUSINESS_CHANCE_SEEK_HELP
    set `STATUS`=#{updatedStatus,jdbcType=INTEGER},MOD_TIME = now()
    where BUSINESS_CHANCE_SEEK_HELP_ID=#{businessChanceSeekHelpId,jdbcType=BIGINT}
  </update>
</mapper>