package com.wms.service.other;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.command.EffectDayResult;
import com.vedeng.goods.enums.CuringTypeEnum;
import com.vedeng.goods.enums.GoodsStorageConditionOthersEnum;
import com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum;
import com.vedeng.goods.enums.RegularMaintainTypeEnum;
import com.vedeng.goods.utils.GoodsUtils;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsRequest;
import com.wms.model.po.WmsCoreSku;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 商品档案下传
 */
@Service
public class PutSkuInterface extends AbstractWmsInterface {


    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.PUT_SKU);
        wmsRequest.setMessageId(WMSContant.PUT_SKU);
    }

    @Override
    protected JSONObject getXmlDate(Object... param) {

        if(param == null ||param.length == 0){
            return null;
        }

        JSONArray headArray = new JSONArray();

        Arrays.stream(param).forEach(paramItem -> {

            WmsCoreSku coreSkuGenerate = (WmsCoreSku)paramItem;

            JSONObject headItem = parseSaleOrderToJsonObject(coreSkuGenerate);

            headArray.add(headItem);
        });

        JSONObject headObject = new JSONObject();
        headObject.put("header",headArray);
        return headObject;
    }

    private JSONObject parseSaleOrderToJsonObject(WmsCoreSku wmsCoreSku) {

        JSONObject headItem = new JSONObject();

        //WmsCoreSku wmsCoreSku=coreSkuGenerateMapper.getInputSkuToWmsBySkuId(sku.getSkuId());

        //货主id
        headItem.put("customerId","VEDENG");
        //sku_no
        headItem.put("sku",wmsCoreSku.getSkuNo());
        //16位新商品编码
        headItem.put("skuDescr2","VEDENG");
        //商品名称
        headItem.put("skuDescr1",wmsCoreSku.getShowName()==null?"VEDENG":wmsCoreSku.getShowName());

        //商品条形码
        headItem.put("alternate_sku1",wmsCoreSku.getGoodsBarcode());

        //激活标记
        headItem.put("activeFlag", wmsCoreSku.getStatus()==1? "Y":"N");

        // 最小售卖单位名称  BASE_UNIT_ID T_unit
        headItem.put("skuBarcode",wmsCoreSku.getUnitName()==null?"skuBarcode":wmsCoreSku.getUnitName());

        //  t_brand 表  BRAND_ID
        // 品牌名称 BRAND_NAME
        headItem.put("alternateSkuA",wmsCoreSku.getBrandName());
        // 商品品牌  BRAND_NATURE 国产品牌：1 进口品牌：2
        headItem.put("alternateSkuB",wmsCoreSku.getBrandNature());

        // 管理类别 T_REGISTRATION_NUMBER MANAGE_CATEGORY_LEVEL
        headItem.put("skuGroup1", wmsCoreSku.getManageCategoryLevel() == null ? "VEDENG" : wmsCoreSku.getManageCategoryLevel());
        // 商品类型 T_CORE_SPU SPU_TYPE
        headItem.put("skuGroup2",wmsCoreSku.getSpuType()==null? ErpConst.SPU_TYPE_QT :wmsCoreSku.getSpuType());

        //规格
        headItem.put("skuGroup3",wmsCoreSku.getSpec());

        //储运温度
        if(GoodsStorageConditionTemperatureEnum.OTHERS.getCode().equals(wmsCoreSku.getStorageConditionOne())){
            headItem.put("skuGroup4",wmsCoreSku.getStorageConditionOneLowerValue() + "度-" + wmsCoreSku.getStorageConditionOneUpperValue() + "度");
        }else{
            headItem.put("skuGroup4",GoodsStorageConditionTemperatureEnum.getNameByCode(wmsCoreSku.getStorageConditionOne()));
        }

        //存储条件（其他）
        if(StringUtil.isNotEmpty(wmsCoreSku.getStorageConditionTwo())){
            headItem.put("skuGroup5",Arrays.stream(wmsCoreSku.getStorageConditionTwo().split(","))
                                            .map(conditionTwo -> GoodsStorageConditionOthersEnum.getNameByCode(conditionTwo))
                                            .collect(Collectors.joining(","))
                        );
        }

        // 生产企业 T_PRODUCT_COMPANY PRODUCT_COMPANY_CHINESE_NAME
        headItem.put("skuGroup7",StringUtils.isBlank(wmsCoreSku.getProductCompanyChineseName()) ? "VEDENG" :wmsCoreSku.getProductCompanyChineseName());

        //制造商型号
        headItem.put("skuGroup6",wmsCoreSku.getModel());

        // 生产企业生产许可证号/备案凭证号 注册证号信息表 REGISTRATION_NUMBER
        headItem.put("skuGroup8",StringUtils.isBlank(wmsCoreSku.getRegistrationNumber()) ? "VEDENG" : wmsCoreSku.getRegistrationNumber());
        // 是否启用效期管理
        headItem.put("shelfLifeFlag",wmsCoreSku.getIsEnableValidityPeriod()==null || wmsCoreSku.getIsEnableValidityPeriod() == 0 ? "N" : "Y");
        //有效期（天数）
        headItem.put("shelfLife",StringUtils.isBlank(wmsCoreSku.getEffectiveDays()) ? 0 : wmsCoreSku.getEffectiveDays());
        if(StringUtils.isBlank(wmsCoreSku.getEffectiveDays())){
            //超近效期控制天数
            headItem.put("shelfLifeAlertDays",0);
            //近效期预警天数
            headItem.put("inboundLifeDays",0);
        }else{
            EffectDayResult result= GoodsUtils.generateNearTermWarnDays(NumberUtils.toInt(wmsCoreSku.getEffectiveDays()));
            //超近效期控制天数
            headItem.put("shelfLifeAlertDays",result.getOverNearTermWarnDays() );
            //近效期预警天数
            headItem.put("inboundLifeDays",result.getNearTermWarnDays());

        }
        //维护原因
        headItem.put("maintenanceReason",wmsCoreSku.getRegularMaintainReason());

        //养护类型（质管专用）
        String specialMaintenance=null;
        if(wmsCoreSku.getCuringType()!=null&&wmsCoreSku.getCuringType()==0){
            specialMaintenance="Y";
        }else if (wmsCoreSku.getCuringType()!=null&&wmsCoreSku.getCuringType()==1){
            specialMaintenance="N";
        }else {
            specialMaintenance="0";
        }
        headItem.put("specialMaintenance",specialMaintenance);

        //定期维护
        if(wmsCoreSku.getRegularMaintainType() != null){
            headItem.put("reservedField12", RegularMaintainTypeEnum.getMessageByType(wmsCoreSku.getRegularMaintainType()));
        }

        //是否必须检测报告
        if (wmsCoreSku.getIsNeedTestReprot()!=null){
            headItem.put("printMedicineQcReport",wmsCoreSku.getIsNeedTestReprot() == 1 ? "Y" : "N");
        }
        //是否套件
        if (wmsCoreSku.getIsKit()!=null){
            headItem.put("kitFlag",wmsCoreSku.getIsKit()==1 ? "Y" : "N");
        }

        //是否厂家赋SN码
        headItem.put("reservedField02",wmsCoreSku.getIsFactorySnCode()==null||wmsCoreSku.getIsFactorySnCode()==0?"N":"Y");

        //是否管理贝登追溯码
        headItem.put("reservedField03",wmsCoreSku.getIsManageVedengCode()==null||wmsCoreSku.getIsManageVedengCode()==0? "N": "Y");

        //是否异形品
        headItem.put("reservedField04",wmsCoreSku.getIsBadGoods() == null || wmsCoreSku.getIsBadGoods() == 0 ? "N" : "Y");

        //是否启用多级包装管理
        headItem.put("reservedField05",wmsCoreSku.getIsEnableMultistagePackage() == null || wmsCoreSku.getIsEnableMultistagePackage() ==0 ? "N": "Y");

        //中包装数量
        headItem.put("reservedField06",wmsCoreSku.getMidPackageNum() == null ? 0 : wmsCoreSku.getMidPackageNum());

        //箱包装数量
        headItem.put("reservedField07",wmsCoreSku.getBoxPackageNum()==null ? 0 : wmsCoreSku.getBoxPackageNum());

        //有哪几个套件
        headItem.put("reservedField08",StringUtils.isBlank(wmsCoreSku.getKitDesc()) ? "VEDENG" : wmsCoreSku.getKitDesc());

        //是否启用厂家批号
        headItem.put("reservedField10",wmsCoreSku.getIsEnableFactoryBatchnum() == null || wmsCoreSku.getIsEnableFactoryBatchnum() == 0? "N": "Y");

        //套件中子件的SN码是否必须一致
        headItem.put("reservedField11",wmsCoreSku.getIsSameSnCode() == null || wmsCoreSku.getIsSameSnCode() == 0? "N": "Y");

        //备注
        headItem.put("notes",wmsCoreSku.getGoodsComments());

        return headItem;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
