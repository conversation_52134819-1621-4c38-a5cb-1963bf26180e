package com.vedeng.infrastructure.kingdee.common.sdk.utils;

import java.util.Base64;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/utils/Base64Utils.class */
public class Base64Utils {
    public static String encodingToBase64(byte[] buffer) {
        try {
            return new String(Base64.getEncoder().encode(buffer)).toString();
        } catch (Exception e) {
            throw e;
        }
    }

    public static byte[] decodingFromBase64(String base64) {
        try {
            return Base64.getDecoder().decode(base64.getBytes());
        } catch (Exception e) {
            throw e;
        }
    }
}
