package com.vedeng.common.key;

import java.io.IOException;
import java.io.UnsupportedEncodingException;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;
 


public class BASE64Coding {    
    private static BASE64Encoder encoder = new BASE64Encoder();
    private static BASE64Decoder decoder = new BASE64Decoder();
    public BASE64Coding() {}    
    public static String encode (String s)   
    {    
        return encoder.encode(s.getBytes());   
    }   
    public static String encode (String s, String charSet) throws UnsupportedEncodingException   
    {    
        return encoder.encode(s.getBytes(charSet));   
    }
    public static String encode (byte []bytes)   
    {    
        return encoder.encode(bytes);   
    }
    public static String decode (String s){    
        try {   
            byte[] temp = decoder.decodeBuffer(s);   
            return new String(temp);   
            } catch (IOException ioe) {    
                // handler   
            }   
        return s;   
    }
    
    public static byte[] decodeByte (String s){    
        try {   
            byte[] temp = decoder.decodeBuffer(s);   
            return  temp;   
            } catch (IOException ioe) {    
                // handler   
            }   
        return null;   
    }
    
 // 解密
    public static String Decrypt(String sSrc, String sKey) throws Exception {
        try {
            // 判断Key是否正确
            if (sKey == null) {
               
                return null;
            }
            // 判断Key是否为16位
            if (sKey.length() != 16) { 
                return null;
            }
            byte[] raw = sKey.getBytes("UTF-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec iv = new IvParameterSpec("0102030405060708"
                    .getBytes());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] encrypted1 = null;// BASE64Decoder.decodeBuffer(sSrc);//先用base64解密
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original);
                return originalString;
            } catch (Exception e) { 
                return null;
            }
        } catch (Exception ex) { 
            return null;
        }
    }
}