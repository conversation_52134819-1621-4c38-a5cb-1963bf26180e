<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.qualityReport.dao.TmpTraderValidtimeExtMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.qualityReport.model.TmpTraderValidtimeExtDo">
    <id column="TMP_TRADER_VALIDTIME_ID" jdbcType="INTEGER" property="tmpTraderValidtimeId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime" />
    <result column="VALID_STATUS" jdbcType="TINYINT" property="validStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    TMP_TRADER_VALIDTIME_ID, TRADER_ID, VALID_TIME, VALID_STATUS
  </sql>
  <select id="getAllValidTraderSupplier" resultType="com.vedeng.qualityReport.model.TmpTraderValidtimeExtDo">
    SELECT TS.TRADER_ID AS traderId,MAX(AHP.END_TIME_) AS validTime,VI.STATUS AS validStatus
    FROM  T_TRADER_SUPPLIER TS
    INNER JOIN T_VERIFIES_INFO VI ON VI.RELATE_TABLE = 'T_TRADER_SUPPLIER' AND VI.RELATE_TABLE_KEY = TS.TRADER_SUPPLIER_ID AND VI.STATUS =1  AND VI.VERIFIES_TYPE = 619
    LEFT JOIN ACT_HI_PROCINST AHP ON AHP.BUSINESS_KEY_ = CONCAT('traderSupplierVerify_',TS.TRADER_SUPPLIER_ID)
    GROUP BY TS.TRADER_SUPPLIER_ID,AHP.BUSINESS_KEY_
  </select>
  <select id="getAllValidTraderCustomer" resultType="com.vedeng.qualityReport.model.TmpTraderValidtimeExtDo">
        SELECT TTCC.TRADER_ID AS traderId,MAX(AHP.END_TIME_) AS validTime,VI.STATUS AS validStatus
        FROM  T_TRADER_CUSTOMER TTCC
        INNER JOIN T_VERIFIES_INFO VI ON VI.RELATE_TABLE = 'T_TRADER_CUSTOMER' AND VI.RELATE_TABLE_KEY = TTCC.TRADER_CUSTOMER_ID AND VI.STATUS =1  AND VI.VERIFIES_TYPE = 617
        LEFT JOIN ACT_HI_PROCINST AHP ON AHP.BUSINESS_KEY_ = CONCAT('traderCustomerVerify_',TTCC.TRADER_CUSTOMER_ID)
        GROUP BY TTCC.TRADER_CUSTOMER_ID,AHP.BUSINESS_KEY_
  </select>
    <select id="getTraderCustomerValidTime"
            resultType="com.vedeng.qualityReport.model.TmpTraderValidtimeExtDo">
        SELECT TTCC.TRADER_ID AS traderId,MAX(AHP.END_TIME_) AS validTime,VI.STATUS AS validStatus
        FROM  T_TRADER_CUSTOMER TTCC
        INNER JOIN T_VERIFIES_INFO VI ON VI.RELATE_TABLE = 'T_TRADER_CUSTOMER' AND VI.RELATE_TABLE_KEY = TTCC.TRADER_CUSTOMER_ID AND VI.STATUS =1  AND VI.VERIFIES_TYPE = 617
        LEFT JOIN ACT_HI_PROCINST AHP ON AHP.BUSINESS_KEY_ = CONCAT('traderCustomerVerify_',TTCC.TRADER_CUSTOMER_ID)
        WHERE
          TTCC.TRADER_CUSTOMER_ID = #{traderCustomerId}
        GROUP BY TTCC.TRADER_CUSTOMER_ID,AHP.BUSINESS_KEY_
    </select>
    <select id="getTraderSupplyValidTime" resultType="com.vedeng.qualityReport.model.TmpTraderValidtimeExtDo">
        SELECT TS.TRADER_ID AS traderId,MAX(AHP.END_TIME_) AS validTime,VI.STATUS AS validStatus
        FROM  T_TRADER_SUPPLIER TS
        INNER JOIN T_VERIFIES_INFO VI ON VI.RELATE_TABLE = 'T_TRADER_SUPPLIER' AND VI.RELATE_TABLE_KEY = TS.TRADER_SUPPLIER_ID AND VI.STATUS =1  AND VI.VERIFIES_TYPE = 619
        LEFT JOIN ACT_HI_PROCINST AHP ON AHP.BUSINESS_KEY_ = CONCAT('traderSupplierVerify_',TS.TRADER_SUPPLIER_ID)
        WHERE
          TS.TRADER_SUPPLIER_ID = #{traderSupplierId}
        GROUP BY TS.TRADER_SUPPLIER_ID,AHP.BUSINESS_KEY_
    </select>
    <select id="getcount" resultType="java.lang.Integer">
         SELECT count(1)
        FROM  T_TRADER_CUSTOMER TTCC
        INNER JOIN T_VERIFIES_INFO VI ON VI.RELATE_TABLE = 'T_TRADER_CUSTOMER' AND VI.RELATE_TABLE_KEY = TTCC.TRADER_CUSTOMER_ID AND VI.STATUS =1  AND VI.VERIFIES_TYPE = 617
        LEFT JOIN ACT_HI_PROCINST AHP ON AHP.BUSINESS_KEY_ = CONCAT('traderCustomerVerify_',TTCC.TRADER_CUSTOMER_ID)
        WHERE
          TTCC.TRADER_CUSTOMER_ID = #{traderCustomerId}
        GROUP BY TTCC.TRADER_CUSTOMER_ID,AHP.BUSINESS_KEY_
    </select>
</mapper>