package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SingleAddGoodsRequestDto{

    /**
     * 报价ID
     */
    @NotNull(message = "quoteorderId不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    /**
     * 客户需求id
     */
    private Long quoteorderNeedsId;

    /**
     * 报价商品id
     */
    private Integer quoteorderGoodsId;

    /**
     * 最新sku
     */
    @NotBlank(message = "skuNo不能为空", groups = DefaultGroup.class)
    private String skuNo;

    /**
     * 原sku
     */
    private String oldSkuNo;

}
