package com.vedeng.erp.system.domain.dto;

import java.util.Date;

public class ChangeLog {
    /** 主键id  CHANGE_LOG_ID **/
    private Integer changeLogId;

    /** 关联业务表名  RELATED_TABLE **/
    private String relatedTable;

    /** 关联业务id  RELATED_ID **/
    private Integer relatedId;

    /** 内容  LOG_MESSAGE **/
    private String logMessage;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MODE_TIME **/
    private Date modeTime;

    /** 是否删除 0否 1是  IS_DELETE **/
    private Boolean isDelete;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /**   主键id  CHANGE_LOG_ID   **/
    public Integer getChangeLogId() {
        return changeLogId;
    }

    /**   主键id  CHANGE_LOG_ID   **/
    public void setChangeLogId(Integer changeLogId) {
        this.changeLogId = changeLogId;
    }

    /**   关联业务表名  RELATED_TABLE   **/
    public String getRelatedTable() {
        return relatedTable;
    }

    /**   关联业务表名  RELATED_TABLE   **/
    public void setRelatedTable(String relatedTable) {
        this.relatedTable = relatedTable == null ? null : relatedTable.trim();
    }

    /**   关联业务id  RELATED_ID   **/
    public Integer getRelatedId() {
        return relatedId;
    }

    /**   关联业务id  RELATED_ID   **/
    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    /**   内容  LOG_MESSAGE   **/
    public String getLogMessage() {
        return logMessage;
    }

    /**   内容  LOG_MESSAGE   **/
    public void setLogMessage(String logMessage) {
        this.logMessage = logMessage == null ? null : logMessage.trim();
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MODE_TIME   **/
    public Date getModeTime() {
        return modeTime;
    }

    /**   更新时间  MODE_TIME   **/
    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public Boolean getIsDelete() {
        return isDelete;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}