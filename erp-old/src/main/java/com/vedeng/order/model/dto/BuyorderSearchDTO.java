package com.vedeng.order.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/5/28 13:39:15
 */
public class BuyorderSearchDTO implements Serializable {
    /**
     * 录票商品的ID
     */
    private Integer invoiceDetailId;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 销方名称
     */
    private String salerName;

    /**
     * 税率，971:13%增值税普通发票,972:13%增值税专用发票，681：16%增值税普通发票， 682：16%增值税专用发票，683：6%增值税普通发票，684：6%增值税专用发票, 685:3%增值税普通发票， 686：3%增值税专用发票, 687:0%增值税普通发票
     */
    private Integer taxRate;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 开票时间
     */
    private Long createTime;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 订单号
     */
    private String buyorderNo;

    /**
     * 搜索开始时间
     */
    private Long startTime;

    /**
     * 搜索结束时间
     */
    private Long endTime;

    /**
     * 字典库对应售票种类
     */
    private Integer invoiceType;

    /**
     * 航信发票ID
     */
    private Integer hxInvoiceId;

    /**
     * '发票种类：0普票，1专票',
     */
    private String invoiceCategory;

    /**
     * 是否为第一次搜索
     */
    private Integer firstSearch;

    public Integer getFirstSearch() {
        return firstSearch;
    }

    public void setFirstSearch(Integer firstSearch) {
        this.firstSearch = firstSearch;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getSalerName() {
        return salerName;
    }

    public void setSalerName(String salerName) {
        this.salerName = salerName;
    }

    public Integer getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(Integer taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getBuyorderNo() {
        return buyorderNo;
    }

    public void setBuyorderNo(String buyorderNo) {
        this.buyorderNo = buyorderNo;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public Integer getInvoiceDetailId() {
        return invoiceDetailId;
    }

    public void setInvoiceDetailId(Integer invoiceDetailId) {
        this.invoiceDetailId = invoiceDetailId;
    }

    public Integer getHxInvoiceId() {
        return hxInvoiceId;
    }

    public void setHxInvoiceId(Integer hxInvoiceId) {
        this.hxInvoiceId = hxInvoiceId;
    }
}
