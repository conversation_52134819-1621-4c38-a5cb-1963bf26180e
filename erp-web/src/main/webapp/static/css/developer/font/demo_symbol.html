
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <script src="iconfont.js"></script>

    <style type="text/css">
        .icon {
          /* 通过设置 font-size 来改变图标大小 */
          width: 1em; height: 1em;
          /* 图标和文字相邻时，垂直对齐 */
          vertical-align: -0.15em;
          /* 通过设置 color 来改变 SVG 的颜色/fill */
          fill: currentColor;
          /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
             normalize.css 中也包含这行 */
          overflow: hidden;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-qianduanyingyongmoban"></use>
                    </svg>
                    <div class="name">前端应用模版</div>
                    <div class="fontclass">#icon-qianduanyingyongmoban</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-sun"></use>
                    </svg>
                    <div class="name">sun</div>
                    <div class="fontclass">#icon-sun</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yingtao"></use>
                    </svg>
                    <div class="name">樱桃</div>
                    <div class="fontclass">#icon-yingtao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-sun1"></use>
                    </svg>
                    <div class="name">sun</div>
                    <div class="fontclass">#icon-sun1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-keai"></use>
                    </svg>
                    <div class="name">可爱</div>
                    <div class="fontclass">#icon-keai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-chongdiandianluguzhang"></use>
                    </svg>
                    <div class="name">充电电路故障</div>
                    <div class="fontclass">#icon-chongdiandianluguzhang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-hulianwang"></use>
                    </svg>
                    <div class="name">互联网</div>
                    <div class="fontclass">#icon-hulianwang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-jiezhi-copy"></use>
                    </svg>
                    <div class="name">戒指</div>
                    <div class="fontclass">#icon-jiezhi-copy</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xigua"></use>
                    </svg>
                    <div class="name">西瓜</div>
                    <div class="fontclass">#icon-xigua</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-icon-test"></use>
                    </svg>
                    <div class="name">海豚</div>
                    <div class="fontclass">#icon-icon-test</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tubiaozhizuomoban-"></use>
                    </svg>
                    <div class="name">化学</div>
                    <div class="fontclass">#icon-tubiaozhizuomoban-</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-moon_and"></use>
                    </svg>
                    <div class="name">moon_and</div>
                    <div class="fontclass">#icon-moon_and</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dongwuyuan"></use>
                    </svg>
                    <div class="name">动物园</div>
                    <div class="fontclass">#icon-dongwuyuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dongwutubiao-shangse-changjinglu"></use>
                    </svg>
                    <div class="name">动物图标-上色-长颈鹿</div>
                    <div class="fontclass">#icon-dongwutubiao-shangse-changjinglu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dongwutubiao-shangse-zhu"></use>
                    </svg>
                    <div class="name">动物图标-上色-猪</div>
                    <div class="fontclass">#icon-dongwutubiao-shangse-zhu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-nvsheng"></use>
                    </svg>
                    <div class="name">女生</div>
                    <div class="fontclass">#icon-nvsheng</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-jiezhi"></use>
                    </svg>
                    <div class="name">戒指1</div>
                    <div class="fontclass">#icon-jiezhi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shoulian"></use>
                    </svg>
                    <div class="name">手链</div>
                    <div class="fontclass">#icon-shoulian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-bangbangtang"></use>
                    </svg>
                    <div class="name">米妮烘焙坊 </div>
                    <div class="fontclass">#icon-bangbangtang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-buding"></use>
                    </svg>
                    <div class="name">布丁</div>
                    <div class="fontclass">#icon-buding</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-binggan"></use>
                    </svg>
                    <div class="name">饼干</div>
                    <div class="fontclass">#icon-binggan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-beizidangao"></use>
                    </svg>
                    <div class="name">杯子蛋糕</div>
                    <div class="fontclass">#icon-beizidangao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-baomihua"></use>
                    </svg>
                    <div class="name">爆米花</div>
                    <div class="fontclass">#icon-baomihua</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-bingqilin"></use>
                    </svg>
                    <div class="name">冰淇淋</div>
                    <div class="fontclass">#icon-bingqilin</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-canju"></use>
                    </svg>
                    <div class="name">餐具</div>
                    <div class="fontclass">#icon-canju</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-guozhi"></use>
                    </svg>
                    <div class="name">果汁</div>
                    <div class="fontclass">#icon-guozhi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-hanbao"></use>
                    </svg>
                    <div class="name">汉堡</div>
                    <div class="fontclass">#icon-hanbao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-niujiaomianbao"></use>
                    </svg>
                    <div class="name">牛角面包</div>
                    <div class="fontclass">#icon-niujiaomianbao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-kafei"></use>
                    </svg>
                    <div class="name">咖啡</div>
                    <div class="fontclass">#icon-kafei</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-niunai"></use>
                    </svg>
                    <div class="name">牛奶</div>
                    <div class="fontclass">#icon-niunai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-dangao"></use>
                    </svg>
                    <div class="name">蛋糕</div>
                    <div class="fontclass">#icon-dangao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tangguo"></use>
                    </svg>
                    <div class="name">糖果</div>
                    <div class="fontclass">#icon-tangguo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tusi"></use>
                    </svg>
                    <div class="name">吐司</div>
                    <div class="fontclass">#icon-tusi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-pisa"></use>
                    </svg>
                    <div class="name">披萨</div>
                    <div class="fontclass">#icon-pisa</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-tiantianquan"></use>
                    </svg>
                    <div class="name">甜甜圈</div>
                    <div class="fontclass">#icon-tiantianquan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xuegao"></use>
                    </svg>
                    <div class="name">雪糕</div>
                    <div class="fontclass">#icon-xuegao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-xiangjiao"></use>
                    </svg>
                    <div class="name">香蕉</div>
                    <div class="fontclass">#icon-xiangjiao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-ningmeng"></use>
                    </svg>
                    <div class="name">柠檬</div>
                    <div class="fontclass">#icon-ningmeng</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-liulian"></use>
                    </svg>
                    <div class="name">榴莲</div>
                    <div class="fontclass">#icon-liulian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-lizhi"></use>
                    </svg>
                    <div class="name">荔枝</div>
                    <div class="fontclass">#icon-lizhi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-caomei"></use>
                    </svg>
                    <div class="name">草莓</div>
                    <div class="fontclass">#icon-caomei</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-mihoutao"></use>
                    </svg>
                    <div class="name">猕猴桃</div>
                    <div class="fontclass">#icon-mihoutao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-putao"></use>
                    </svg>
                    <div class="name">葡萄</div>
                    <div class="fontclass">#icon-putao</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-shanzhu"></use>
                    </svg>
                    <div class="name">山竹</div>
                    <div class="fontclass">#icon-shanzhu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-youzi"></use>
                    </svg>
                    <div class="name">柚子</div>
                    <div class="fontclass">#icon-youzi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yingtao1"></use>
                    </svg>
                    <div class="name">樱桃</div>
                    <div class="fontclass">#icon-yingtao1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-yezi"></use>
                    </svg>
                    <div class="name">椰子</div>
                    <div class="fontclass">#icon-yezi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-mangguo"></use>
                    </svg>
                    <div class="name">芒果</div>
                    <div class="fontclass">#icon-mangguo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-boluo"></use>
                    </svg>
                    <div class="name">菠萝</div>
                    <div class="fontclass">#icon-boluo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-chengzi"></use>
                    </svg>
                    <div class="name">橙子</div>
                    <div class="fontclass">#icon-chengzi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-ziyuan"></use>
                    </svg>
                    <div class="name">桃子</div>
                    <div class="fontclass">#icon-ziyuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-lanmei"></use>
                    </svg>
                    <div class="name">蓝莓</div>
                    <div class="fontclass">#icon-lanmei</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-ziyuan1"></use>
                    </svg>
                    <div class="name">香梨</div>
                    <div class="fontclass">#icon-ziyuan1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-pingguo"></use>
                    </svg>
                    <div class="name">苹果</div>
                    <div class="fontclass">#icon-pingguo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-ziyuan2"></use>
                    </svg>
                    <div class="name">火龙果</div>
                    <div class="fontclass">#icon-ziyuan2</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-mengbanhangkonghangtian-xingxingstar"></use>
                    </svg>
                    <div class="name">萌版航空航天-星星star</div>
                    <div class="fontclass">#icon-mengbanhangkonghangtian-xingxingstar</div>
                </li>
            
        </ul>


        <h2 id="symbol-">symbol引用</h2>
        <hr>

        <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
        这种用法其实是做了一个svg的集合，与另外两种相比具有如下特点：</p>
        <ul>
          <li>支持多色图标了，不再受单色限制。</li>
          <li>通过一些技巧，支持像字体那样，通过<code>font-size</code>,<code>color</code>来调整样式。</li>
          <li>兼容性较差，支持 ie9+,及现代浏览器。</li>
          <li>浏览器渲染svg的性能一般，还不如png。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-symbol-">第一步：引入项目下面生成的symbol代码：</h3>
        <pre><code class="lang-js hljs javascript"><span class="hljs-comment">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;</span></code></pre>
        <h3 id="-css-">第二步：加入通用css代码（引入一次就行）：</h3>
        <pre><code class="lang-js hljs javascript">&lt;style type=<span class="hljs-string">"text/css"</span>&gt;
.icon {
   width: <span class="hljs-number">1</span>em; height: <span class="hljs-number">1</span>em;
   vertical-align: <span class="hljs-number">-0.15</span>em;
   fill: currentColor;
   overflow: hidden;
}
&lt;<span class="hljs-regexp">/style&gt;</span></code></pre>
        <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
        <pre><code class="lang-js hljs javascript">&lt;svg <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"icon"</span> aria-hidden=<span class="hljs-string">"true"</span>&gt;<span class="xml"><span class="hljs-tag">
  &lt;<span class="hljs-name">use</span> <span class="hljs-attr">xlink:href</span>=<span class="hljs-string">"#icon-xxx"</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">use</span>&gt;</span>
</span>&lt;<span class="hljs-regexp">/svg&gt;
        </span></code></pre>
    </div>
</body>
</html>
