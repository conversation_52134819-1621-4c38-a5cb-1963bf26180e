package com.vedeng.erp.system.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.system.mapper.FlowOrderDetailMapper;
import com.vedeng.erp.system.domain.entity.FlowOrderDetailEntity;
import com.vedeng.erp.system.service.FlowOrderDetailService;
@Service
public class FlowOrderDetailServiceImpl implements FlowOrderDetailService{

    @Autowired
    private FlowOrderDetailMapper flowOrderDetailMapper;

    @Override
    public int deleteByPrimaryKey(Long flowOrderDetailId) {
        return flowOrderDetailMapper.deleteByPrimaryKey(flowOrderDetailId);
    }

    @Override
    public int insert(FlowOrderDetailEntity record) {
        return flowOrderDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(FlowOrderDetailEntity record) {
        return flowOrderDetailMapper.insertSelective(record);
    }

    @Override
    public FlowOrderDetailEntity selectByPrimaryKey(Long flowOrderDetailId) {
        return flowOrderDetailMapper.selectByPrimaryKey(flowOrderDetailId);
    }

    @Override
    public int updateByPrimaryKeySelective(FlowOrderDetailEntity record) {
        return flowOrderDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(FlowOrderDetailEntity record) {
        return flowOrderDetailMapper.updateByPrimaryKey(record);
    }

}
