function copyUrl(str) {
    var oInput = $("#es_"+str);
    oInput.select(); // 选择对象
    document.execCommand("Copy"); // 执行浏览器复制命令
    layer.alert('复制成功');
}
function openNewLayer() {
    //layer.closeAll();
    parent.layer.alert('签章链接生成中，请1分钟后刷新查看', function(index){
        //parent.layer.close(index);
        parent.layer.close(index);
        location.reload();
        //parent.layer.closeAll();
        //parent.location.reload();
        //layer.closeAll();
    });
}