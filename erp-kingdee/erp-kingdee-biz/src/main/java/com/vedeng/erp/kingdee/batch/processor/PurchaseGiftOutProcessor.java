package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInResultDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 采购赠品售后退货出库
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PurchaseGiftOutProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto> {

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;
    @Autowired
    private BatchBuyorderDtoMapper batchBuyorderDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public KingDeeStorageInDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        log.info("PurchaseGiftOutProcessorService.process：{}" ,JSON.toJSONString(batchWarehouseGoodsOutInDto));

        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_MISCELLANEOUS);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").compare("=").left("(").right(")").value(batchWarehouseGoodsOutInDto.getOutInNo()).logic("AND").build());
        queryParam.setFilterString(queryFilterDtos);
        List<KingDeeStorageInResultDto> query = kingDeeBaseApi.query(queryParam, KingDeeStorageInResultDto.class);
        log.info("采购赠品出库单查询金蝶结果：{}",JSON.toJSONString(query));
        if (CollUtil.isNotEmpty(query)) {
            log.info("已推送金蝶数据，无需推送");
            return null;
        }
        // 根据入库单关系 获取售后单
        BatchAfterSalesDto batchAfterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesNoAndSubjectType(
                batchWarehouseGoodsOutInDto.getRelateNo(), 536);
        if (Objects.isNull(batchAfterSalesDto)) {
            log.warn("未查到采购赠品单售后单信息");
            return null;
        }
        BatchBuyorderDto batchBuyorderDto = batchBuyorderDtoMapper.selectByBuyorderNo(batchAfterSalesDto.getOrderNo());
        if (Objects.isNull(batchBuyorderDto)) {
            log.warn("未查到采购赠品单售后单原采购单信息");
            return null;
        }
        log.info("采购赠品单售后退货,组装数据,采购单号:{},采购单信息:{}",batchAfterSalesDto.getOrderNo(), JSONUtil.toJsonStr(batchBuyorderDto));
        Map<Integer, BatchAfterSalesGoodsDto> map = batchAfterSalesDto.getBatchAfterSalesGoodsDtoList()
                .stream().collect(Collectors.toMap(BatchAfterSalesGoodsDto::getAfterSalesGoodsId, c -> c, (k1, k2) -> k1));

        KingDeeStorageInDto dto = new KingDeeStorageInDto();
        dto.setFId("0");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFStockDirect("RETURN");
        dto.setFSupplierId(batchBuyorderDto.getTraderSupplierId().toString());
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());


        List<KingDeeStorageInDetailDto> detailDtoList = new ArrayList<>();
        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.error("未能查到出库单no:{}子单信息",JSON.toJSONString(batchWarehouseGoodsOutInDto.getOutInNo()));
            throw new KingDeeException("未能查到出库单no:" + JSON.toJSONString(batchWarehouseGoodsOutInDto.getOutInNo())+"子单信息");
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l -> {
            KingDeeStorageInDetailDto detailDto = new KingDeeStorageInDetailDto();
            BatchAfterSalesGoodsDto afterSalesGoodsDto = map.get(l.getRelatedId());
            if (Objects.isNull(afterSalesGoodsDto)) {
                log.error("采购赠品售后未能查到具体的商品明细：{}",JSON.toJSONString(l));
                throw new KingDeeException("采购赠品售后未能查到具体的商品明细");
            }
            detailDto.setFMaterialId(afterSalesGoodsDto.getSku());
            detailDto.setFStockId("CK9999");
            BigDecimal num = l.getNum().abs();
            detailDto.setFQty(num.toString());
            detailDto.setFPrice("0.01");
            detailDto.setFAmount(new BigDecimal("0.01").multiply(num).toString());
            detailDto.setFQzokYsddh(batchAfterSalesDto.getOrderNo());
            //VDERP-14859 调整 setFQzokXlh setFQzokSfzf
            detailDto.setFQzokGsywdh(batchAfterSalesDto.getAfterSalesNo());
            detailDto.setFQzokYwlx("采购赠品售后退货出库");
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            detailDto.setFQzokSfzf(afterSalesGoodsDto.getDeliveryDirect().equals(0) ? "否" : "是");
            detailDto.setFQzokBddjhId(l.getWarehouseGoodsOutInDetailId().toString());
            detailDto.setFQzokSqlx("");
            detailDtoList.add(detailDto);
        });

        dto.setFEntity(detailDtoList);
        return dto;
    }

}
