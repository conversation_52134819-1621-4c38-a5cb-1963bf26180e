package com.vedeng.erp.trader.common.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/4 19:21
 **/
public enum TraderCustomerTerminalAuditEnum {

    DO_AUDIT(0,"审核中"),
    PASS(1,"审核通过"),
    REJECTED(2,"驳回")
    ;


    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    TraderCustomerTerminalAuditEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
