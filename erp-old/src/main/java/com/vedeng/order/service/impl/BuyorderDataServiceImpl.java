package com.vedeng.order.service.impl;

import com.vedeng.common.model.ResultAssist;
import com.vedeng.finance.model.BuyorderData;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.BuyorderDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service("buyorderDataService")
public class BuyorderDataServiceImpl implements BuyorderDataService {
    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Override
    public BigDecimal getPaymentAmount(Integer buyorderId) {
        return buyorderMapper.getPaymentAmount(buyorderId);
    }

    @Override
    public BigDecimal getLackAccountPeriodAmount(Integer buyorderId) {
        return buyorderMapper.getLackAccountPeriodAmount(buyorderId);
    }

    @Override
    public BigDecimal getInvoiceAmount(Integer buyorderId) {
        return buyorderMapper.getInvoiceAmount(buyorderId);
    }

    @Override
    public BuyorderData getBuyorderData(Integer buyorderId) {
        BuyorderData buyorderData = new BuyorderData();
        buyorderData.setPaymentAmount(this.getPaymentAmount(buyorderId));
        buyorderData.setLackAccountPeriodAmount(this.getLackAccountPeriodAmount(buyorderId));
        buyorderData.setInvoiceAmount(this.getInvoiceAmount(buyorderId));
        buyorderData.setPeriodAmount(this.getPeriodAmount(buyorderId));
        buyorderData.setRealAmount(this.getRealAmount(buyorderId));
        return buyorderData;
    }

    @Override
    public Integer getDeliveryNum(Integer buyorderGoodsId) {
        return buyorderGoodsMapper.getDeliveryNum(buyorderGoodsId);
    }

    @Override
    public List<BuyorderData> getBuyorderDatas(List<Integer> buyorderIds) {
        return buyorderMapper.getBuyorderDatas(buyorderIds);
    }

    @Override
    public BigDecimal getRealAmount(Integer buyorderId) {
        return buyorderMapper.getRealAmount(buyorderId);
    }

    @Override
    public List<ResultAssist> getNewRealAmount(List<Integer> buyOrderIdList, Integer companyId) {
        return buyorderMapper.getNewRealAmount(buyOrderIdList, companyId);
    }

    @Override
    public BigDecimal getPeriodAmount(Integer buyorderId) {
        return buyorderMapper.getPeriodAmount(buyorderId);
    }

    @Override
    public BigDecimal getHaveInvoiceNums(Integer buyorderGoodsId) {
        return buyorderMapper.getHaveInvoiceNums(buyorderGoodsId);
    }

    @Override
    public BigDecimal getRealPreAmount(Integer buyorderId) {
        return buyorderMapper.getRealPreAmount(buyorderId);
    }

    @Override
    public BigDecimal getPaymentAmountDB(Integer buyorderId) {
        return buyorderMapper.getPaymentAmountDB(buyorderId);
    }

    @Override
    public BigDecimal getAfterSaleServiceAmount(Integer buyorderId) {
        return buyorderMapper.getAfterSaleServiceAmount(buyorderId);
    }

    @Override
    public BigDecimal getPaymentAndPeriodAmount(Integer buyorderId) {
        return buyorderMapper.getPaymentAndPeriodAmount(buyorderId);
    }

    @Override
    public List<BuyorderGoodsVo> getHaveInvoiceNumsByList(List<BuyorderGoodsVo> bgvList) {
        // TODO Auto-generated method stub
        return buyorderMapper.getHaveInvoiceNumsByList(bgvList);
    }

}
