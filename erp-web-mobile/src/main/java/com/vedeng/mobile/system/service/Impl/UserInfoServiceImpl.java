package com.vedeng.mobile.system.service.Impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.mobile.feign.RemoteUacUserinfoApiService;
import com.vedeng.mobile.system.mapper.ErpMobilUserMapper;
import com.vedeng.mobile.system.service.UserInfoService;
import com.vedeng.uac.api.dto.UserLoginInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserInfoServiceImpl implements UserInfoService {

    @Autowired
    private ErpMobilUserMapper mobilUserMapper;

    @Autowired
    private RemoteUacUserinfoApiService remoteUacUserinfoApiService;


    @Override
    public String getUserErpMobile(Integer userId) {
        return mobilUserMapper.getMobileByUserId(userId);
    }

    @Override
    public UserLoginInfo userLoginInfo(String userName) {
        UserLoginInfo userLoginInfo;
        try {
            log.info("掌上小贝: 调用uac查询用户登录信息，入参{}", userName);
            userLoginInfo = remoteUacUserinfoApiService.getUacUserLoginInfo(userName);
            log.info("掌上小贝: 调用uac查询用户登录信息，返回值{}", JSON.toJSONString(userLoginInfo));
        } catch (Exception e) {
            log.error("查询uac登录信息异常", e);
            throw new ServiceException(BaseResponseCode.USER_ACCOUNT_EXCEPTION);
            //try {
            //    userLoginInfo = userMapper.userLoginInfo(userName);
            //    log.info("掌上小贝:feign调用查询账户登录信息失败，降级查询erp信息进行登录校验,erp用户信息[{}]", JSON.toJSON(userLoginInfo));
            //} catch (Exception ex) {
            //    log.error("查询erp登录信息异常", ex);
            //    throw new ServiceException(BaseResponseCode.SYSTEM_BUSY);
            //}
        }
        return userLoginInfo;
    }
}
