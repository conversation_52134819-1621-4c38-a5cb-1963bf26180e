package com.vedeng.crm.api;

import com.vedeng.crm.dto.CategoryMatchDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: ProductKeywordApiService
 * @date 2025/4/16 12:22
 */
public interface ProductKeywordApiService {

    /**
     * @param input
     * @return java.util.List<java.lang.String>
     * @description: 提取关键字
     * @date 2025/4/16 12:22
     */
    List<String> extractKeywords(String input);

    /**
     * @param keywords
     * @return java.util.List<com.vedeng.crm.dto.CategoryMatchDto.CategoryMatch>
     * @description: 匹配分类
     * @date 2025/4/16 12:22
     */
    List<CategoryMatchDto.CategoryMatch> matchCategories(List<String> keywords);

    /**
     * @param businessId
     * @param businessType
     * @return com.vedeng.crm.dto.CategoryMatchDto
     * @description: 获取分类
     * @date 2025/4/16 12:22
     */
    CategoryMatchDto getCategoryByBusiness(Integer businessId, Integer businessType);
}
