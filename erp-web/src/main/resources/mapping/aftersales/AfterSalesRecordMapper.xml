<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSalesRecordMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSalesRecord" >
    <id column="AFTER_SALES_RECORD_ID" property="afterSalesRecordId" jdbcType="INTEGER" />
    <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  
  <resultMap type="com.vedeng.aftersales.model.vo.AfterSalesRecordVo" id="VoResultMap" extends="BaseResultMap">
  
  </resultMap>
  
  <sql id="Base_Column_List" >
    AFTER_SALES_RECORD_ID, AFTER_SALES_ID, CONTENT, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_RECORD
    where AFTER_SALES_RECORD_ID = #{afterSalesRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_AFTER_SALES_RECORD
    where AFTER_SALES_RECORD_ID = #{afterSalesRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.aftersales.model.AfterSalesRecord" >
    insert into T_AFTER_SALES_RECORD (AFTER_SALES_RECORD_ID, AFTER_SALES_ID, 
      CONTENT, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER)
    values (#{afterSalesRecordId,jdbcType=INTEGER}, #{afterSalesId,jdbcType=INTEGER}, 
      #{content,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="afterSalesRecordId" parameterType="com.vedeng.aftersales.model.AfterSalesRecord" >
    insert into T_AFTER_SALES_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="afterSalesRecordId != null" >
        AFTER_SALES_RECORD_ID,
      </if>
      <if test="afterSalesId != null" >
        AFTER_SALES_ID,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="afterSalesRecordId != null" >
        #{afterSalesRecordId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null" >
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" useGeneratedKeys="true" keyProperty="afterSalesRecordId" parameterType="com.vedeng.aftersales.model.AfterSalesRecord" >
    update T_AFTER_SALES_RECORD
    <set >
      <if test="afterSalesId != null" >
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where AFTER_SALES_RECORD_ID = #{afterSalesRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.AfterSalesRecord" >
    update T_AFTER_SALES_RECORD
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where AFTER_SALES_RECORD_ID = #{afterSalesRecordId,jdbcType=INTEGER}
  </update>
  
  <select id="getAfterSalesRecordVoList" parameterType="com.vedeng.aftersales.model.AfterSalesRecord" resultMap="VoResultMap">
  	select
  		<include refid="Base_Column_List" />
  	from T_AFTER_SALES_RECORD
  	where 1=1
  	<if test="afterSalesId != null">
  		and AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  	</if>
  </select>
</mapper>