<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="saleInvoiceList" type="java.util.List" required="true" %>
<%@attribute name="invoiceTypes" type="java.util.List" required="true" %>
<%@attribute name="saleorderDataInfo" type="java.util.Map" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>


<div class="parts content1" liname="发票信息" id="发票信息">
    <div class="title-container title-container-yellow">
        <div class="table-title nobor">
            发票信息
        </div>
        <c:set var="exitFor" value="0" />
        <c:forEach var="list" items="${saleInvoiceList}" varStatus="num">
            <c:if test="${list.invoiceProperty eq 2 and exitFor eq 0}">
                <c:set var="exitFor" value="1" />
                <div class="title-click nobor pop-new-data" layerparams='{"width":"650px","height":"360px","title":"电子票推送","link":"<%=basePath%>finance/invoice/invoiceSmsAndMailInit.do?relatedId=${saleorder.saleorderId}"}'>电子票推送</div>
            </c:if>
        </c:forEach>
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th>发票号</th>
            <th>发票代码</th>
            <th>票种</th>
            <th style="width: 80px">红蓝字</th>
            <th style="width: 80px">发票金额</th>
            <th style="width: 130px">操作人</th>
            <th style="width: 130px">操作时间</th>
            <th>快递公司</th>
            <th style="width: 140px">快递单号</th>
            <th style="width: 80px">快递状态</th>
            <th style="width: 120px">操作</th>
        </tr>
        </thead>
        <tbody>
        <c:set var="invoiceAmount" value="0.00" /><!-- 已开票总额 -->
        <c:forEach var="list" items="${saleInvoiceList}" varStatus="num">
            <tr>
                <td>
                    <c:if test="${empty list.invoiceNo}">
                        <span class="bt-small bg-light-blue bt-bg-style" onclick="batchDownEInvoice()">下载电子发票</span>
                    </c:if>
                    <c:if test="${not empty list.invoiceNo}">
                        ${list.invoiceNo}
                        <c:if test="${list.invoiceProperty eq 2}">
                            <font color='red'>[电]</font>
                        </c:if>
                    </c:if>
                </td>
                <td>${list.invoiceCode}</td>
                <td>
                    <c:forEach var="invoiceList" items="${invoiceTypes}" varStatus="status">
                        <c:if test="${invoiceList.sysOptionDefinitionId eq list.invoiceType}">${invoiceList.title}</c:if>
                    </c:forEach>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${list.colorType eq 1}">
                            <c:choose>
                                <c:when test="${list.isEnable eq 0}">
                                    <span style="color: red">红字作废</span>
                                </c:when>
                                <c:otherwise>
                                    红字有效
                                </c:otherwise>
                            </c:choose>
                        </c:when>
                        <c:otherwise>
                            <c:choose>
                                <c:when test="${list.isEnable eq 0}">
                                    <span style="color: red">蓝字作废</span>
                                </c:when>
                                <c:otherwise>
                                    蓝字有效
                                </c:otherwise>
                            </c:choose>
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <fmt:formatNumber type="number" value="${list.amount}" pattern="0.00" maxFractionDigits="2" />
                    <c:set var="invoiceAmount" value="${invoiceAmount + list.amount}" />
                </td>
                <td>${list.creatorName}</td>
                <td><date:date value="${list.addTime}" format="yyyy.MM.dd HH:mm:ss"/></td>
                <td>${list.express.logisticsCompanyName}</td>
                <td>${list.express.logisticsNo}</td>
                <td>
                    <c:choose>
                        <c:when test="${list.express.arrivalStatus eq 0}">未收货</c:when>
                        <c:when test="${list.express.arrivalStatus eq 1}">部分收货</c:when>
                        <c:when test="${list.express.arrivalStatus eq 2}">全部收货</c:when>
                    </c:choose>
                </td>
                <td>
                    <a href= "javascript:void(0);"
                       onclick="viewAndDownloadInvoice('${list.ossFileUrl}','${list.invoiceCode}','${list.invoiceNo}',
                               '${list.invoiceId}','${saleorder.invoiceMethod}',${saleorder.isSendInvoice})">下载发票</a>
                </td>
            </tr>
        </c:forEach>
        <tr>
            <td colspan="11" style="text-align: left; background: #eaf2fd;">
                已开票总额：<fmt:formatNumber type="number" value="${invoiceAmount}" pattern="0.00" maxFractionDigits="2" />
                &nbsp;&nbsp;&nbsp;&nbsp;
                <span style="color:red">未开票总额：<fmt:formatNumber type="number" value="${saleorderDataInfo['realAmount'] - invoiceAmount}" pattern="0.00" maxFractionDigits="2" /></span>
            </td>
        </tr>
        </tbody>
    </table>
</div>