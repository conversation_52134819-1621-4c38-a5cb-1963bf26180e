package com.vedeng.goods.mapper;

import com.vedeng.goods.dto.VHSHostSkuDTO;
import com.vedeng.goods.dto.VHSHostSkuDTOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository("hostSkuMapper")
public interface VHSHostSkuMapper {
    long countByExample(VHSHostSkuDTOExample example);

    int deleteByExample(VHSHostSkuDTOExample example);

    int deleteByPrimaryKey(Integer hostSkuId);

    int insert(VHSHostSkuDTO record);

    int insertSelective(VHSHostSkuDTO record);

    List<VHSHostSkuDTO> selectByExample(VHSHostSkuDTOExample example);

    VHSHostSkuDTO selectByPrimaryKey(Integer hostSkuId);

    int updateByExampleSelective(@Param("record") VHSHostSkuDTO record, @Param("example") VHSHostSkuDTOExample example);

    int updateByExample(@Param("record") VHSHostSkuDTO record, @Param("example") VHSHostSkuDTOExample example);

    int updateByPrimaryKeySelective(VHSHostSkuDTO record);

    int updateByPrimaryKey(VHSHostSkuDTO record);

    VHSHostSkuDTO getSkuHostByOpIdAndSku(@Param("sku") String sku, @Param("opHostWordId") Integer opHostWordId);
}
