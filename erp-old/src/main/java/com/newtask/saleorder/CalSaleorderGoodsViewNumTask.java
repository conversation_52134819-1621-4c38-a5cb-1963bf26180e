package com.newtask.saleorder;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.activiti.editor.language.json.converter.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@JobHandler(value = "CalSaleorderGoodsViewNumTask")
@Component
public class CalSaleorderGoodsViewNumTask extends AbstractJobHandler {

	Logger log = LoggerFactory.getLogger(CalSaleorderGoodsViewNumTask.class);

	@Autowired
	SaleorderGoodsMapper saleorderGoodsMapper;

	@Override
	public ReturnT<String> doExecute(String param) throws Exception {
		List<SaleorderGoodsVo> list=saleorderGoodsMapper.calViewNum();
		for (int i = 0; i < 10; i++) {
			if(CollectionUtils.isNotEmpty(list)){
				XxlJobLogger.log("第"+i+"次计算,数量："+list.size());
				list.forEach(item->{
					saleorderGoodsMapper.updateViewNumber(item);
				});
				list=saleorderGoodsMapper.calViewNum();
			}else{
				break;
			}
		}
		return SUCCESS;
	}

}
