package com.vedeng.temporal.workflow.activity.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.core.http.SystemApiClientProxy;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.entity.FlowOrderEntity;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.exception.ExceptionHandler;
import com.vedeng.temporal.mapper.TemporalFlowOrderMapper;
import com.vedeng.temporal.service.FlowNodeBasedCompanyService;
import com.vedeng.temporal.service.FlowOrderInfoManagementService;
import com.vedeng.temporal.workflow.activity.PurchaseOrderActivity;
import com.vedeng.temporal.workflow.activity.core.UniversalActivityTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 采购订单 Activity 实现类 - 极简版
 * <p>
 * 架构优化说明：
 * - 使用 UniversalBusinessTemplate 处理所有业务逻辑：固定参数、API调用、异常处理、通知发送
 * - 每个方法都是独立的 Activity，由 Temporal 管理重试
 * - 直接使用 CompanyBusinessRequest 传递数据，自动解析 JSON
 * <p>
 * 业务流程：
 * 1. createPurchaseOrder - 创建采购订单，返回订单ID
 * 2. submitPurchaseOrder - 提交审核，需要订单ID
 * 3. approvePurchaseOrder - 审核通过，需要订单ID
 * 4. queryPurchaseOrderStatus - 查询状态，用于流程控制
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0 (极简化架构)
 * @since 2025-01-18
 */
@Component
@Slf4j
public class PurchaseOrderActivityImpl implements PurchaseOrderActivity {

    @Autowired
    private UniversalActivityTemplate universalActivityTemplate;

    @Autowired
    private TemporalFlowOrderMapper temporalFlowOrderMapper;

    @Autowired
    private ExceptionHandler exceptionHandler;

    @Autowired
    private FlowNodeBasedCompanyService flowNodeBasedCompanyService;

    @Autowired
    private FlowOrderInfoManagementService flowOrderInfoManagementService;

    @Autowired
    private SystemApiClientProxy systemApiClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public CompanyBusinessResponse createPurchaseOrder(CompanyBusinessRequest request) {
        setUserNameFromFlowOrder(request);
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
                UniversalActivityTemplate.BusinessOperationConfig.create()
                        .operationName("创建采购订单")
                        .apiPath("/api/v1/buyorder/create.do")
                        .dataPreparer(this::preparePurchaseOrderData)
                        .resultExtractor(result -> {
                            if (result.containsKey("buyorderId")) {
                                Object buyorderId = result.get("buyorderId");
                                return buyorderId != null ? buyorderId.toString() : null;
                            }
                            return null;
                        });

        // 使用异常优先模式，失败时直接抛出异常
        return universalActivityTemplate.execute(request, config);
    }

    @Override
    public CompanyBusinessResponse updatePurchaseOrder(CompanyBusinessRequest request) {
        setUserNameFromFlowOrder(request);
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
                UniversalActivityTemplate.BusinessOperationConfig.create()
                        .operationName("更新采购订单")
                        .apiPath("/api/v1/buyorder/update.do")
                        .dataPreparer(this::prepareUpdateData)
                        .resultExtractor(result -> "更新采购订单成功");

        // 使用异常优先模式，失败时直接抛出异常
        return universalActivityTemplate.execute(request, config);
    }

    @Override
    public CompanyBusinessResponse submitPurchaseOrderForApproval(CompanyBusinessRequest request) {
        setUserNameFromFlowOrder(request);
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
                UniversalActivityTemplate.BusinessOperationConfig.create()
                        .operationName("提交采购订单审核")
                        .apiPath("/api/v1/buyorder/submit.do")
                        .dataPreparer(this::prepareSubmitData); // 提交操作不需要返回特定ID

        // 使用异常优先模式，失败时直接抛出异常
        return universalActivityTemplate.execute(request, config);
    }

    @Override
    public CompanyBusinessResponse approvePurchaseOrder(CompanyBusinessRequest request) {
        setUserNameFromFlowOrder(request);
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
                UniversalActivityTemplate.BusinessOperationConfig.create()
                        .operationName("审核采购订单")
                        .apiPath("/api/v1/buyorder/approve.do")
                        .dataPreparer(this::prepareApproveData); // 审核操作不需要返回特定ID

        // 使用异常优先模式，失败时直接抛出异常
        return universalActivityTemplate.execute(request, config);
    }

    @Override
    public CompanyBusinessResponse createFlowOrderInfoRecord(CompanyBusinessRequest request) {
        try {
            log.info("开始创建采购订单FlowOrderInfo记录，业务ID: {}, 下游公司: {}",
                    request.getBusinessId(), request.getTargetCompanyCode());

            // 解析业务数据获取订单ID
            Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
            String orderId = (String) businessData.get("orderId");

            if (!StringUtils.hasText(orderId)) {
                String errorMsg = "创建FlowOrderInfo记录失败：订单ID为空";
                log.error("{}, 业务ID: {}", errorMsg, request.getBusinessId());
                return CompanyBusinessResponse.failure(errorMsg, "ORDER_ID_EMPTY");
            }

            // 1. 先查询获取采购单号（在下游公司查询）
            String purchaseOrderNo = queryPurchaseOrderNumber(orderId, request.getTargetCompanyCode());
            if (!StringUtils.hasText(purchaseOrderNo)) {
                String errorMsg = "无法获取采购单号";
                log.error("{}, 订单ID: {}, 公司: {}", errorMsg, orderId, request.getTargetCompanyCode());
                return CompanyBusinessResponse.failure(errorMsg, "PURCHASE_ORDER_NO_NOT_FOUND");
            }

            log.info("查询获取采购单号成功，订单ID: {}, 采购单号: {}", orderId, purchaseOrderNo);

            // 2. 获取流程节点ID（使用下游公司）
            Long flowNodeId = flowNodeBasedCompanyService.getFlowNodeId(
                    request.getBusinessId(), request.getTargetCompanyCode());

            if (flowNodeId == null) {
                String errorMsg = "无法获取流程节点ID";
                log.error("{}, 业务ID: {}, 公司: {}", errorMsg,
                        request.getBusinessId(), request.getTargetCompanyCode());
                return CompanyBusinessResponse.failure(errorMsg, "FLOW_NODE_ID_NOT_FOUND");
            }

            // 3. 创建FlowOrderInfo记录（使用采购单号作为业务单号，下游公司）
            boolean success = flowOrderInfoManagementService.createFlowOrderInfoForPurchaseOrder(
                    flowNodeId, purchaseOrderNo, request.getTargetCompanyCode());

            if (success) {
                log.info("采购订单FlowOrderInfo记录创建成功，业务ID: {}, 采购单号: {}, flowNodeId: {}",
                        request.getBusinessId(), purchaseOrderNo, flowNodeId);
                return CompanyBusinessResponse.success("FlowOrderInfo记录创建成功", purchaseOrderNo);
            } else {
                String errorMsg = "FlowOrderInfo记录创建失败";
                log.error("{}, 业务ID: {}, 采购单号: {}", errorMsg,
                        request.getBusinessId(), purchaseOrderNo);
                return CompanyBusinessResponse.failure(errorMsg, "FLOW_ORDER_INFO_CREATE_FAILED");
            }

        } catch (Exception e) {
            log.error("创建采购订单FlowOrderInfo记录异常，业务ID: {}",
                    request.getBusinessId(), e);
            return exceptionHandler.handleBusinessException(e,
                    "创建FlowOrderInfo记录",
                    request.getBusinessId(),
                    request.getTargetCompanyCode());
        }
    }

    @Override
    public boolean hasNextFlowNode(CompanyBusinessRequest request) {
        try {
            log.info("检查是否存在下一个流程节点，业务ID: {}, 当前公司: {}",
                    request.getBusinessId(), request.getTargetCompanyCode());

            // 获取完整的流程节点序列（不包含0级公司）
            Long flowOrderId = Long.valueOf(request.getBusinessId());
            List<String> companySequence = flowNodeBasedCompanyService.getCompanySequenceFromFlowNodeOnly(flowOrderId);

            String currentCompany = request.getTargetCompanyCode();

            if (companySequence == null || companySequence.isEmpty()) {
                log.info("没有找到任何流程节点，当前公司: {}", currentCompany);
                return false;
            }

            // 查找当前公司在序列中的位置
            int currentIndex = -1;
            for (int i = 0; i < companySequence.size(); i++) {
                if (currentCompany.equals(companySequence.get(i))) {
                    currentIndex = i;
                    break;
                }
            }

            if (currentIndex == -1) {
                log.info("当前公司不在流程节点序列中，当前公司: {}, 序列: {}", currentCompany, companySequence);
                return false;
            }

            // 检查是否还有下一个节点
            boolean hasNext = currentIndex < companySequence.size() - 1;

            log.info("流程节点检查结果，当前公司: {}, 位置: {}/{}, 有下一节点: {}, 完整序列: {}",
                    currentCompany, currentIndex + 1, companySequence.size(), hasNext, companySequence);

            return hasNext;

        } catch (Exception e) {
            log.error("检查下一个流程节点异常，业务ID: {}, 当前公司: {}",
                    request.getBusinessId(), request.getTargetCompanyCode(), e);
            String businessContext = "BusinessId=" + request.getBusinessId() +
                    ", Company=" + request.getTargetCompanyCode();
            throw BusinessProcessException.retryable(
                    "检查流程节点失败: " + e.getMessage(),
                    "FLOW_NODE_CHECK_ERROR",
                    businessContext);
        }
    }

    /**
     * 查询采购单号
     * 通过订单ID查询获取实际的采购单号
     * 使用systemApiClient调用采购单查询接口
     *
     * @param orderId     订单ID
     * @param companyCode 公司代码
     * @return 采购单号
     */
    private String queryPurchaseOrderNumber(String orderId, String companyCode) {
        try {
            log.info("开始查询采购单号，订单ID: {}, 公司: {}", orderId, companyCode);

            // 构建查询请求数据，假设API需要orderId参数
            Map<String, Object> queryData = new HashMap<>();
            queryData.put("buyorderId", orderId);

            // 调用采购单查询接口
            String purchaseResult = systemApiClient
                    .withCompany(companyCode)
                    .withUser("system")
                    .postToSystemApi("/api/v1/buyorder/query.do", queryData);

            if (StringUtils.hasText(purchaseResult)) {
                // 解析API返回结果，提取采购单号
                String purchaseOrderNo = extractPurchaseOrderNoFromResult(purchaseResult);

                if (StringUtils.hasText(purchaseOrderNo)) {
                    log.info("查询采购单号成功，订单ID: {}, 采购单号: {}", orderId, purchaseOrderNo);
                    return purchaseOrderNo;
                } else {
                    log.error("无法从API返回结果中提取采购单号，订单ID: {}", orderId);
                    return null;
                }
            } else {
                log.error("采购单查询API返回空结果，订单ID: {}", orderId);
                return null;
            }

        } catch (Exception e) {
            log.error("查询采购单号异常，订单ID: {}, 公司: {}", orderId, companyCode, e);
            return null;
        }
    }

    /**
     * 查询采购单完整商品信息
     * 通过订单ID查询获取采购单的所有商品详情，用于获取正确的buyorderGoodsId
     *
     * @param orderId     订单ID
     * @param companyCode 公司代码
     * @return 以SKU为key的采购商品信息Map
     */
    private Map<String, Map<String, Object>> queryPurchaseOrderDetails(String orderId, String companyCode) {
        try {
            log.info("开始查询采购单完整商品信息，订单ID: {}, 公司: {}", orderId, companyCode);

            // 构建查询请求数据
            Map<String, Object> queryData = new HashMap<>();
            queryData.put("buyorderId", orderId);

            // 调用采购单查询接口
            String purchaseResult = systemApiClient
                    .withCompany(companyCode)
                    .withUser("system")
                    .postToSystemApi("/api/v1/buyorder/query.do", queryData);

            if (StringUtils.hasText(purchaseResult)) {
                // 解析API返回结果，提取采购单商品信息
                Map<String, Map<String, Object>> purchaseGoodsMap = extractPurchaseOrderDetailsFromResult(purchaseResult);

                if (purchaseGoodsMap != null && !purchaseGoodsMap.isEmpty()) {
                    log.info("查询采购单商品信息成功，订单ID: {}, 商品数量: {}", orderId, purchaseGoodsMap.size());
                    return purchaseGoodsMap;
                } else {
                    log.error("无法从API返回结果中提取采购单商品信息，订单ID: {}", orderId);
                    return new HashMap<>();
                }
            } else {
                log.error("采购单查询API返回空结果，订单ID: {}", orderId);
                return new HashMap<>();
            }

        } catch (Exception e) {
            log.error("查询采购单商品信息异常，订单ID: {}, 公司: {}", orderId, companyCode, e);
            return new HashMap<>();
        }
    }

    /**
     * 从API返回结果中提取采购单商品详情
     * 解析采购单查询接口返回的商品列表，构建SKU到商品信息的映射
     *
     * @param apiResult API返回的JSON字符串
     * @return 以SKU为key的商品信息Map
     */
    private Map<String, Map<String, Object>> extractPurchaseOrderDetailsFromResult(String apiResult) {
        try {
            // 解析响应为Map对象
            Map<String, Object> apiResponseMap = objectMapper.readValue(apiResult, Map.class);

            // 检查API调用是否成功
            Integer code = (Integer) apiResponseMap.get("code");
            String message = (String) apiResponseMap.get("message");
            Object data = apiResponseMap.get("data");

            if (code == null || !code.equals(0)) {
                String errorMsg = String.format("采购单查询失败: %s (错误码: %d)",
                        message, code != null ? code : -1);
                log.error("{}, 返回结果: {}", errorMsg, apiResult);
                return new HashMap<>();
            }

            // 从data中提取商品列表
            if (data instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) data;

                Object goodsListObj = dataMap.get("buyorderGoodsApiDtos");
                if (goodsListObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> goodsList = (List<Map<String, Object>>) goodsListObj;

                    Map<String, Map<String, Object>> result = new HashMap<>();

                    for (Map<String, Object> goods : goodsList) {
                        String sku = (String) goods.get("sku");
                        String buyorderGoodsId = goods.get("buyorderGoodsId").toString();
                        Map<String, Object> normalizedGoods = new HashMap<>(goods);
                        normalizedGoods.put("buyorderGoodsId", buyorderGoodsId);
                        normalizedGoods.put("sku", sku);
                        result.put(sku, normalizedGoods);
                    }

                    log.info("成功解析采购单商品信息，有效商品数: {}", result.size());
                    return result;
                }
            }

            log.error("API返回成功但无法提取采购单商品列表");
            return new HashMap<>();

        } catch (Exception e) {
            log.error("解析采购单商品信息异常: {}", apiResult, e);
            return new HashMap<>();
        }
    }

    /**
     * 查询销售订单完整商品信息
     * 通过销售单号查询获取销售订单的所有商品详情，用于获取正确的销售订单商品ID
     *
     * @param saleOrderNo 销售单号
     * @param companyCode 公司代码
     * @return 以SKU为key的销售订单商品信息Map
     */
    private Map<String, Map<String, Object>> querySaleOrderDetails(String saleOrderNo, String companyCode) {
        try {
            log.info("开始查询销售订单完整商品信息，销售单号: {}, 公司: {}", saleOrderNo, companyCode);

            // 构建查询请求数据
            Map<String, Object> queryData = new HashMap<>();
            queryData.put("saleOrderNo", saleOrderNo);

            // 调用销售订单查询接口
            String saleOrderResult = systemApiClient
                    .withCompany(companyCode)
                    .withUser("system")
                    .postToSystemApi("/api/v1/saleorder/query.do", queryData);

            if (StringUtils.hasText(saleOrderResult)) {
                // 解析API返回结果，提取销售订单商品信息
                Map<String, Map<String, Object>> saleOrderGoodsMap = extractSaleOrderDetailsFromResult(saleOrderResult);

                if (saleOrderGoodsMap != null && !saleOrderGoodsMap.isEmpty()) {
                    log.info("查询销售订单商品信息成功，销售单号: {}, 商品数量: {}", saleOrderNo, saleOrderGoodsMap.size());
                    return saleOrderGoodsMap;
                } else {
                    log.error("无法从API返回结果中提取销售订单商品信息，销售单号: {}", saleOrderNo);
                    return new HashMap<>();
                }
            } else {
                log.error("销售订单查询API返回空结果，销售单号: {}", saleOrderNo);
                return new HashMap<>();
            }

        } catch (Exception e) {
            log.error("查询销售订单商品信息异常，销售单号: {}, 公司: {}", saleOrderNo, companyCode, e);
            return new HashMap<>();
        }
    }

    /**
     * 从API返回结果中提取销售订单商品详情
     * 解析销售订单查询接口返回的商品列表，构建SKU到商品信息的映射
     *
     * @param apiResult API返回的JSON字符串
     * @return 以SKU为key的销售订单商品信息Map
     */
    private Map<String, Map<String, Object>> extractSaleOrderDetailsFromResult(String apiResult) {
        try {
            // 解析响应为Map对象
            Map<String, Object> apiResponseMap = objectMapper.readValue(apiResult, Map.class);

            // 检查API调用是否成功
            Integer code = (Integer) apiResponseMap.get("code");
            String message = (String) apiResponseMap.get("message");
            Object data = apiResponseMap.get("data");

            if (code == null || !code.equals(0)) {
                String errorMsg = String.format("销售订单查询失败: %s (错误码: %d)",
                        message, code != null ? code : -1);
                log.error("{}, 返回结果: {}", errorMsg, apiResult);
                return new HashMap<>();
            }

            // 从data中提取商品列表
            if (data instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) data;

                Object goodsListObj = dataMap.get("saleOrderGoodsDetailDtoList");
                if (goodsListObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> goodsList = (List<Map<String, Object>>) goodsListObj;

                    Map<String, Map<String, Object>> result = new HashMap<>();

                    for (Map<String, Object> goods : goodsList) {
                        String sku = (String) goods.get("skuNo");
                        Object saleorderGoodsIdObj = goods.get("saleorderGoodsId");
                        // 统一字段名
                        Map<String, Object> normalizedGoods = new HashMap<>(goods);
                        normalizedGoods.put("saleorderGoodsId", saleorderGoodsIdObj.toString());
                        normalizedGoods.put("sku", sku);
                        result.put(sku, normalizedGoods);
                    }

                    log.info("成功解析销售订单商品信息，有效商品数: {}", result.size());
                    return result;
                }
            }

            log.error("API返回成功但无法提取销售订单商品列表");
            return new HashMap<>();

        } catch (Exception e) {
            log.error("解析销售订单商品信息异常: {}", apiResult, e);
            return new HashMap<>();
        }
    }

    /**
     * 从API返回结果中提取采购单号
     * 按照标准ApiResponse格式解析：code=0表示成功，字段名为buyorderNo
     *
     * @param apiResult API返回的JSON字符串
     * @return 采购单号
     */
    private String extractPurchaseOrderNoFromResult(String apiResult) {
        try {
            // 解析响应为Map对象
            Map<String, Object> apiResponseMap = objectMapper.readValue(apiResult, Map.class);

            // 检查API调用是否成功 (基于ApiResponse标准格式：code=0表示成功)
            Integer code = (Integer) apiResponseMap.get("code");
            String message = (String) apiResponseMap.get("message");
            Object data = apiResponseMap.get("data");

            if (code == null || !code.equals(0)) {
                String errorMsg = String.format("采购单查询失败: %s (错误码: %d)",
                        message, code != null ? code : -1);
                log.error("{}, 返回结果: {}", errorMsg, apiResult);
                return null;
            }

            // 从data中提取采购单号
            if (data instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) data;

                // 提取buyorderNo字段
                Object buyorderNoObj = dataMap.get("buyorderNo");
                if (buyorderNoObj != null) {
                    String buyorderNo = buyorderNoObj.toString();
                    if (StringUtils.hasText(buyorderNo)) {
                        return buyorderNo;
                    }
                }
            }

            log.error("API返回成功但无法提取采购单号");
            return null;

        } catch (Exception e) {
            log.error("解析API返回结果异常: {}", apiResult, e);
            return null;
        }
    }


    // ========== 私有数据准备方法 ==========

    /**
     * 从流转单中获取创建人信息并设置到请求的用户名字段
     * 如果创建人为空，则使用默认值 "system"
     *
     * @param request 业务请求对象
     */
    private void setUserNameFromFlowOrder(CompanyBusinessRequest request) {
        // 获取流转单创建人信息
        FlowOrderEntity flowOrder = queryFlowOrderEntity(request.getBusinessId());
        String creatorName = flowOrder.getCreatorName();
        if (creatorName == null || creatorName.trim().isEmpty()) {
            creatorName = "system"; // 默认值
        }
        request.setUserName(creatorName);
    }

    /**
     * 统一的业务数据解析和订单ID获取
     */
    private String extractOrderId(CompanyBusinessRequest request) {
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        return (String) businessData.get("orderId");
    }

    /**
     * 准备采购订单创建数据
     */
    private Map<String, Object> preparePurchaseOrderData(CompanyBusinessRequest request) {
        Map<String, Object> orderData = new HashMap<>();
        orderData.put("saleorderNo", request.getExtendedProperties().get("saleOrderNo"));
        return orderData;
    }

    /**
     * 准备提交审核数据
     */
    private Map<String, Object> prepareSubmitData(CompanyBusinessRequest request) {
        Map<String, Object> submitData = new HashMap<>();
        String orderId = extractOrderId(request);
        if (orderId != null) {
            submitData.put("buyorderId", orderId);
        }
        return submitData;
    }

    /**
     * 准备审核数据
     */
    private Map<String, Object> prepareApproveData(CompanyBusinessRequest request) {
        Map<String, Object> approveData = new HashMap<>();
        String orderId = extractOrderId(request);
        if (orderId != null) {
            approveData.put("buyorderId", orderId);
        }
        approveData.put("approveTime", System.currentTimeMillis());
        approveData.put("approveResult", "APPROVED");
        approveData.put("approveReason", "流程自动审核");
        return approveData;
    }

    /**
     * 准备更新数据
     */
    private Map<String, Object> prepareUpdateData(CompanyBusinessRequest request) {
        try {
            String orderId = extractOrderId(request);
            String flowOrderId = request.getBusinessId();
            String targetCompany = request.getTargetCompanyCode();

            // 查询所有必要数据
            FlowOrderEntity flowOrder = queryFlowOrderEntity(flowOrderId);

            // 获取下一级公司（供应商）代码
            String supplierCompany = getNextCompanyInSequence(flowOrderId, targetCompany);
            log.info("目标公司: {}, 供应商公司（下一级）: {}", targetCompany, supplierCompany);

            // 获取供应商信息（从下一级公司）
            Map<String, Object> supplierInfo = null;
            if (supplierCompany != null) {
                log.info("开始获取供应商信息 - supplierCompany: {} (可能是公司编码或供应商名称)", supplierCompany);

                supplierInfo = temporalFlowOrderMapper.selectFlowNodeCompleteInfo(Long.valueOf(flowOrderId), supplierCompany);
                if (supplierInfo == null) {
                    log.warn("未找到供应商信息 - supplierCompany: {}, flowOrderId: {}, 将使用默认供应商信息",
                            supplierCompany, flowOrderId);
                } else {
                    String companyShortName = (String) supplierInfo.get("COMPANY_SHORT_NAME");
                    boolean isSubcompany = companyShortName != null && !companyShortName.trim().isEmpty();
                    log.info("成功获取供应商信息 - supplierCompany: {}, traderId: {}, traderName: {}, 类型: {}",
                            supplierCompany, supplierInfo.get("TRADER_ID"), supplierInfo.get("TRADER_NAME"),
                            isSubcompany ? "子公司" : "外部供应商");
                }
            } else {
                log.info("当前公司是最后一个节点，无下一级供应商，将使用默认供应商信息");
            }

            // 获取销售订单号
            String saleOrderNo = (String) request.getExtendedProperties().get("saleOrderNo");
            log.info("获取销售订单号: {}", saleOrderNo);

            // 直接构建请求参数，传入供应商信息
            return buildCompleteRequestMapWithDirectQuery(orderId, supplierInfo, flowOrderId, targetCompany, supplierCompany, saleOrderNo, flowOrder);

        } catch (Exception e) {
            log.error("构建采购订单更新请求失败, 业务ID: {}", request.getBusinessId(), e);
            exceptionHandler.handleAndThrowActivityException(e, "构建采购订单更新请求",
                    request.getBusinessId(), request.getTargetCompanyCode());
            return null;
        }
    }

    /**
     * 获取公司序列中当前公司的下一级公司（供应商）
     *
     * @param flowOrderId    流转单ID
     * @param currentCompany 当前公司代码
     * @return 下一级公司代码，如果是最后一个节点则返回null
     */
    private String getNextCompanyInSequence(String flowOrderId, String currentCompany) {
        try {
            log.info("开始获取下一级公司，flowOrderId: {}, currentCompany: {}", flowOrderId, currentCompany);

            // 获取完整的流程节点序列（不包含0级公司）
            Long flowOrderIdLong = Long.valueOf(flowOrderId);
            List<String> companySequence = flowNodeBasedCompanyService.getCompanySequenceFromFlowNodeOnly(flowOrderIdLong);

            if (companySequence == null || companySequence.isEmpty()) {
                log.warn("没有找到任何流程节点，flowOrderId: {}", flowOrderId);
                return null;
            }

            // 查找当前公司在序列中的位置
            int currentIndex = -1;
            for (int i = 0; i < companySequence.size(); i++) {
                if (currentCompany.equals(companySequence.get(i))) {
                    currentIndex = i;
                    break;
                }
            }

            if (currentIndex == -1) {
                log.warn("当前公司不在流程节点序列中，currentCompany: {}, 序列: {}", currentCompany, companySequence);
                return null;
            }

            // 检查是否有下一个节点
            if (currentIndex < companySequence.size() - 1) {
                String nextCompany = companySequence.get(currentIndex + 1);
                log.info("找到下一级公司，当前公司: {}, 下一级公司: {}, 位置: {}/{}, 完整序列: {}",
                        currentCompany, nextCompany, currentIndex + 1, companySequence.size(), companySequence);
                return nextCompany;
            } else {
                log.info("当前公司是最后一个节点，没有下一级公司，currentCompany: {}, 位置: {}/{}, 完整序列: {}",
                        currentCompany, currentIndex + 1, companySequence.size(), companySequence);
                return null;
            }

        } catch (Exception e) {
            log.error("获取下一级公司异常，flowOrderId: {}, currentCompany: {}", flowOrderId, currentCompany, e);
            return null;
        }
    }

    /**
     * 解析业务数据 JSON 字符串
     */
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (StrUtil.isBlank(businessDataJson)) {
            return new HashMap<>();
        }

        try {
            return JSONUtil.toBean(businessDataJson, Map.class);
        } catch (Exception e) {
            log.error("解析业务数据JSON失败: {}", businessDataJson, e);
            return new HashMap<>();
        }
    }


    /**
     * 直接构建商品相关字段 - 消除中间对象转换
     * 包含商品明细主体、商品数组参数、商品其他参数等22个字段
     */
    private void buildGoodsRelatedFields(Map<String, Object> requestMap,
                                         String orderId, String flowOrderId, String targetCompany, String supplierCompany,
                                         String saleOrderNo, FlowOrderEntity flowOrder) {

        executeWithExceptionHandling("构建商品相关字段", () -> {
            Long flowOrderIdLong = Long.valueOf(flowOrderId);

            // 首先获取远程采购单商品信息 - 这是获取正确buyorderGoodsId的关键
            log.info("开始获取远程采购单商品信息, baseOrderId: {}, targetCompany: {}", orderId, targetCompany);
            Map<String, Map<String, Object>> remotePurchaseGoodsMap = queryPurchaseOrderDetails(orderId, targetCompany);
            if (remotePurchaseGoodsMap.isEmpty()) {
                log.error("远程采购单商品信息为空，可能导致buyorderGoodsId获取失败");
            } else {
                log.info("成功获取远程采购单商品信息, 商品数量: {}", remotePurchaseGoodsMap.size());
            }

            // 获取远程销售订单商品信息 - 这是获取正确销售订单商品ID的关键
            log.info("开始获取远程销售订单商品信息, saleOrderNo: {}, targetCompany: {}", saleOrderNo, targetCompany);
            Map<String, Map<String, Object>> remoteSaleOrderGoodsMap = querySaleOrderDetails(saleOrderNo, targetCompany);
            if (remoteSaleOrderGoodsMap.isEmpty()) {
                log.error("远程销售订单商品信息为空，可能导致销售订单商品ID获取失败");
            } else {
                log.info("成功获取远程销售订单商品信息, 商品数量: {}", remoteSaleOrderGoodsMap.size());
            }


            // 查询本地数据源
            List<Map<String, Object>> flowOrderDetails = temporalFlowOrderMapper.selectFlowOrderDetails(flowOrderIdLong);
            List<Map<String, Object>> priceInfoList = temporalFlowOrderMapper.selectFlowNodePrices(flowOrderIdLong, supplierCompany);
            List<Map<String, Object>> buyorderTimeData = temporalFlowOrderMapper.selectBuyorderGoodsByOrderId(Long.valueOf(flowOrder.getBaseOrderId()));

            if (flowOrderDetails == null || flowOrderDetails.isEmpty()) {
                throw new IllegalStateException("未找到流转单商品明细 - flowOrderId: " + flowOrderId);
            }

            // 构建价格信息Map
            Map<Object, Map<String, Object>> priceInfoMap = priceInfoList != null ?
                    priceInfoList.stream().collect(Collectors.toMap(
                            info -> info.get("SKU_ID"),
                            info -> info,
                            (existing, replacement) -> existing)) :
                    new HashMap<>();

            // 构建时间信息Map
            Map<String, Map<String, Object>> timeInfoMap = buyorderTimeData != null ?
                    buyorderTimeData.stream()
                            .filter(goods -> goods.get("SKU") != null && !goods.get("SKU").toString().trim().isEmpty())
                            .collect(Collectors.toMap(
                                    goods -> (String) goods.get("SKU"),
                                    goods -> goods,
                                    (existing, replacement) -> existing)) :
                    new HashMap<>();

            // 构建所有商品相关字段 - 使用远程采购商品信息和销售订单商品信息
            buildAllGoodsFields(requestMap, flowOrderDetails, priceInfoMap, timeInfoMap, remotePurchaseGoodsMap, remoteSaleOrderGoodsMap);

            return null;
        });
    }

    /**
     * 从原始数据直接构建所有商品字段
     * 使用远程采购单商品信息获取正确的buyorderGoodsId
     * 使用远程销售订单商品信息获取正确的销售订单商品ID
     */
    private void buildAllGoodsFields(Map<String, Object> requestMap,
                                     List<Map<String, Object>> flowOrderDetails,
                                     Map<Object, Map<String, Object>> priceInfoMap,
                                     Map<String, Map<String, Object>> timeInfoMap,
                                     Map<String, Map<String, Object>> remotePurchaseGoodsMap,
                                     Map<String, Map<String, Object>> remoteSaleOrderGoodsMap) {

        // 收集所有字段的构建器
        List<Map<String, Object>> goodsList = new ArrayList<>();
        List<String> buyorderGoodsIds = new ArrayList<>();
        List<String> buySumList = new ArrayList<>();
        List<String> isGiftGoodsList = new ArrayList<>();
        List<String> priceList = new ArrayList<>();
        List<String> sendGoodsTimeStrList = new ArrayList<>();
        List<String> receiveGoodsTimeStrList = new ArrayList<>();
        List<String> deliveryCycleList = new ArrayList<>();
        List<String> isHaveAuthList = new ArrayList<>();
        List<String> goodsCommentsList = new ArrayList<>();
        List<String> installationList = new ArrayList<>();
        List<String> insideCommentsList = new ArrayList<>();
        List<String> dbBuyNumList = new ArrayList<>();

        // 第一个商品的单独字段
        String firstBuyorderGoodsId = "";
        String firstPrice = "0.00";
        String firstSendTime = "";
        String firstReceiveTime = "";
        String firstDeliveryCycle = "";

        int totalGoodsNum = 0;

        // 遍历流转单明细，直接构建所有字段
        for (int i = 0; i < flowOrderDetails.size(); i++) {
            Map<String, Object> detail = flowOrderDetails.get(i);
            Object skuIdObj = detail.get("SKU_ID");
            String skuNo = (String) detail.get("SKU_NO");

            // 获取价格信息
            Map<String, Object> priceInfo = priceInfoMap.get(skuIdObj);
            BigDecimal price = BigDecimal.ZERO;
            if (priceInfo != null && priceInfo.get("PRICE") != null) {
                price = new BigDecimal(priceInfo.get("PRICE").toString());
            }

            // 获取时间信息
            Map<String, Object> timeInfo = timeInfoMap.get(skuNo);
            Long sendGoodsTime = null;
            Long receiveGoodsTime = null;
            String deliveryCycle = "";

            if (timeInfo != null) {
                Object sendTime = timeInfo.get("SEND_GOODS_TIME");
                Object receiveTime = timeInfo.get("RECEIVE_GOODS_TIME");
                if (sendTime != null && !sendTime.toString().equals("0")) {
                    sendGoodsTime = Long.valueOf(sendTime.toString());
                }
                if (receiveTime != null && !receiveTime.toString().equals("0")) {
                    receiveGoodsTime = Long.valueOf(receiveTime.toString());
                }
                deliveryCycle = (String) timeInfo.get("DELIVERY_CYCLE");
                if (deliveryCycle == null) deliveryCycle = "";
            }

            // 基础数据 - 从远程采购单商品信息中通过SKU匹配获取正确的buyorderGoodsId
            String buyorderGoodsId = "";
            Map<String, Object> remotePurchaseGoods = remotePurchaseGoodsMap.get(skuNo);
            if (remotePurchaseGoods != null && remotePurchaseGoods.get("buyorderGoodsId") != null) {
                buyorderGoodsId = remotePurchaseGoods.get("buyorderGoodsId").toString();
            } else {
                log.error("远程采购单中未找到SKU对应的商品信息, SKU: {}", skuNo);
            }

            Integer quantity = extractInteger(detail.get("QUANTITY"));
            int num = quantity != null ? quantity : 0;

            // 构建goodsList中的商品对象
            Map<String, Object> buyorderGoods = new HashMap<>();
            if (!buyorderGoodsId.isEmpty()) {
                buyorderGoods.put("buyorderGoodsId", Integer.valueOf(buyorderGoodsId));
            }
            if (skuIdObj != null) {
                buyorderGoods.put("goodsId", Integer.valueOf(skuIdObj.toString()));
            }
            buyorderGoods.put("goodsName", detail.get("PRODUCT_NAME"));
            buyorderGoods.put("sku", skuNo);
            buyorderGoods.put("brandName", detail.get("BRAND"));
            buyorderGoods.put("model", detail.get("MODEL"));
            buyorderGoods.put("unitName", detail.get("UNIT"));
            buyorderGoods.put("num", num);
            buyorderGoods.put("price", price);
            buyorderGoods.put("sendGoodsTime", sendGoodsTime);
            buyorderGoods.put("receiveGoodsTime", receiveGoodsTime);
            buyorderGoods.put("deliveryCycle", deliveryCycle);
            buyorderGoods.put("insideComments", "");
            buyorderGoods.put("installation", "");
            buyorderGoods.put("goodsComments", "");
            buyorderGoods.put("isHaveAuth", 0);
            buyorderGoods.put("isGift", 0);
            buyorderGoods.put("referPrice", BigDecimal.ZERO);
            buyorderGoods.put("rebateAmount", BigDecimal.ZERO);
            buyorderGoods.put("rebatePrice", BigDecimal.ZERO);
            buyorderGoods.put("rebateNum", BigDecimal.ZERO);
            buyorderGoods.put("actualPurchasePrice", price);
            goodsList.add(buyorderGoods);


            buyorderGoodsIds.add(buyorderGoodsId);
            buySumList.add(buyorderGoodsId + "|" + num);
            isGiftGoodsList.add(buyorderGoodsId + "|0");
            priceList.add(buyorderGoodsId + "|" + price.intValue());
            sendGoodsTimeStrList.add(buyorderGoodsId + "|" + formatTimeToDateString(sendGoodsTime));
            receiveGoodsTimeStrList.add(buyorderGoodsId + "|" + formatTimeToDateString(receiveGoodsTime));
            deliveryCycleList.add(buyorderGoodsId + "|" + deliveryCycle);
            isHaveAuthList.add(buyorderGoodsId + "|0");
            goodsCommentsList.add(buyorderGoodsId + "|");
            installationList.add(buyorderGoodsId + "|");
            insideCommentsList.add(buyorderGoodsId + "|");
            // 获取销售订单商品ID - 通过SKU匹配远程销售订单商品信息
            String saleorderGoodsId = "";
            Map<String, Object> remoteSaleOrderGoods = remoteSaleOrderGoodsMap.get(skuNo);
            if (remoteSaleOrderGoods != null && remoteSaleOrderGoods.get("saleorderGoodsId") != null) {
                saleorderGoodsId = remoteSaleOrderGoods.get("saleorderGoodsId").toString();
            } else {
                log.error("远程销售订单中未找到SKU对应的商品信息, SKU: {}", skuNo);
            }

            // 构建dbBuyNumList字段 - 使用真实的销售订单商品ID
            if (!saleorderGoodsId.isEmpty()) {
                dbBuyNumList.add(buyorderGoodsId + "|" + saleorderGoodsId + "|" + num);
            } else {
                log.error("销售订单商品ID为空，跳过dbBuyNumList构建, SKU: {}", skuNo);
            }


            // 第一个有效商品的特殊字段
            if (firstBuyorderGoodsId.isEmpty() && !buyorderGoodsId.isEmpty()) {
                firstBuyorderGoodsId = buyorderGoodsId;
                firstPrice = price.toString();
                firstSendTime = formatTimeToDateString(sendGoodsTime);
                firstReceiveTime = formatTimeToDateString(receiveGoodsTime);
                firstDeliveryCycle = deliveryCycle;
            }

            totalGoodsNum += num;
        }

        // 数据完整性验证
        int validBuyorderGoodsCount = buyorderGoodsIds.size();
        int totalDetailsCount = flowOrderDetails.size();

        if (validBuyorderGoodsCount == 0) {
            log.error("严重错误：所有商品的buyorderGoodsId都为空，无法更新采购订单！");
            throw new IllegalStateException("无法获取任何有效的采购订单商品ID，更新操作失败");
        }

        if (firstBuyorderGoodsId.isEmpty()) {
            log.error("第一个商品的buyorderGoodsId为空，这可能导致更新请求失败");
        }

        log.info("商品字段构建完成: 总商品数={}, 有效buyorderGoodsId数={}, 总数量={}",
                totalDetailsCount, validBuyorderGoodsCount, totalGoodsNum);


        // 设置所有字段到requestMap
        // ========== 商品明细主体 (1个字段) ==========
        requestMap.put("goodsList", goodsList);

        // ========== 商品数组参数 (13个字段) ==========
        requestMap.put("buyorderGoodsId", firstBuyorderGoodsId);
        requestMap.put("buySum", buySumList);
        requestMap.put("isGiftGoods", isGiftGoodsList);
        requestMap.put("xprice", firstPrice);
        requestMap.put("price", priceList);
        requestMap.put("xSendGoodsTime", firstSendTime);
        requestMap.put("sendGoodsTimeStr", sendGoodsTimeStrList);
        requestMap.put("xReceiveGoodsTime", firstReceiveTime);
        requestMap.put("receiveGoodsTimeStr", receiveGoodsTimeStrList);
        requestMap.put("deliveryCycleDispaly", firstDeliveryCycle);
        requestMap.put("deliveryCycle", deliveryCycleList);
        requestMap.put("isHaveAuthInput", 1);
        requestMap.put("isHaveAuth", isHaveAuthList);

        // ========== 商品其他参数 (8个字段) ==========
        requestMap.put("goodsCommentsDispaly", "");
        requestMap.put("goodsComments", goodsCommentsList);
        requestMap.put("installationDispaly", "");
        requestMap.put("installation", installationList);
        requestMap.put("insideCommentsDispaly", "");
        requestMap.put("insideComments", insideCommentsList);
        requestMap.put("xactualPurchasePrice", "0.00");
        requestMap.put("dbBuyNum", dbBuyNumList);
        requestMap.put("saleorderGoodsNum", totalGoodsNum);
    }

    // ========== 第二阶段：数据查询方法 ==========

    /**
     * 通用异常处理方法
     */
    private <T> T executeWithExceptionHandling(String operation, Supplier<T> supplier) {
        try {
            log.info("执行数据准备:{}", operation);
            return supplier.get();
        } catch (Exception e) {
            log.error("{}失败", operation, e);
            throw new IllegalStateException(operation + "失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询业务流转单基础信息
     */
    private FlowOrderEntity queryFlowOrderEntity(String flowOrderId) {
        return executeWithExceptionHandling("查询流转单信息", () -> {
            FlowOrderEntity entity = temporalFlowOrderMapper.selectByFlowOrderId(Long.valueOf(flowOrderId));
            if (entity == null) {
                throw new IllegalStateException("未找到流转单信息: " + flowOrderId);
            }
            if (StrUtil.isBlank(entity.getFlowOrderNo())) {
                throw new IllegalStateException("流转单编号为空: " + flowOrderId);
            }
            return entity;
        });
    }


    /**
     * 类型转换辅助方法
     */
    private Long extractLong(Object obj) {
        return obj != null ? Long.valueOf(obj.toString()) : null;
    }

    private Integer extractInteger(Object obj) {
        return obj != null ? Integer.valueOf(obj.toString()) : null;
    }


    // ========== 第四阶段：数据组装方法 ==========

    /**
     * 计算原始流转单中所有商品的合计金额
     * 用于设置prepaidAmount字段的实际金额
     */
    private BigDecimal calculateTotalGoodsAmount(String flowOrderId, String targetCompany) {
        try {
            Long flowOrderIdLong = Long.valueOf(flowOrderId);

            // 查询流转单商品明细
            List<Map<String, Object>> flowOrderDetails = temporalFlowOrderMapper.selectFlowOrderDetails(flowOrderIdLong);
            List<Map<String, Object>> priceInfoList = temporalFlowOrderMapper.selectFlowNodePrices(flowOrderIdLong, targetCompany);

            if (flowOrderDetails == null || flowOrderDetails.isEmpty()) {
                log.warn("流转单商品明细为空，返回默认金额 flowOrderId: {}", flowOrderId);
                return new BigDecimal("0.00");
            }

            // 构建价格信息Map
            Map<Object, Map<String, Object>> priceInfoMap = priceInfoList != null ?
                    priceInfoList.stream().collect(Collectors.toMap(
                            info -> info.get("SKU_ID"),
                            info -> info,
                            (existing, replacement) -> existing)) :
                    new HashMap<>();

            BigDecimal totalAmount = BigDecimal.ZERO;

            // 遍历商品明细计算总金额
            for (Map<String, Object> detail : flowOrderDetails) {
                Object skuIdObj = detail.get("SKU_ID");
                Integer quantity = extractInteger(detail.get("QUANTITY"));
                int num = quantity != null ? quantity : 1;

                // 获取价格信息
                Map<String, Object> priceInfo = priceInfoMap.get(skuIdObj);
                BigDecimal price = BigDecimal.ZERO;
                if (priceInfo != null && priceInfo.get("PRICE") != null) {
                    price = new BigDecimal(priceInfo.get("PRICE").toString());
                }

                // 计算当前商品金额：价格 × 数量
                BigDecimal itemAmount = price.multiply(new BigDecimal(num));
                totalAmount = totalAmount.add(itemAmount);

                String skuNo = (String) detail.get("SKU_NO");
                log.debug("商品金额计算: SKU={}, 价格={}, 数量={}, 小计={}", skuNo, price, num, itemAmount);
            }

            log.info("流转单商品总金额计算完成: flowOrderId={}, 总金额={}", flowOrderId, totalAmount);
            return totalAmount;

        } catch (Exception e) {
            log.error("计算流转单商品总金额失败: flowOrderId={}", flowOrderId, e);
            return new BigDecimal("0.00");
        }
    }

    /**
     * 构建完整的采购订单请求参数Map - 终极优化版本：直接从数据库查询构建
     * 消除中间对象，直接从查询结果构建所有字段
     *
     * @param orderId       订单ID
     * @param supplierInfo  供应商信息（来自下一级公司）
     * @param flowOrderId   流转单ID
     * @param targetCompany 目标公司代码
     * @param saleOrderNo   销售订单号
     * @param flowOrder     流转单实体
     * @return 采购订单请求参数Map
     */
    private Map<String, Object> buildCompleteRequestMapWithDirectQuery(
            String orderId, Map<String, Object> supplierInfo,
            String flowOrderId, String targetCompany, String supplierCompany,
            String saleOrderNo, FlowOrderEntity flowOrder) {

        return executeWithExceptionHandling("构建完整采购订单参数", () -> {
            Map<String, Object> requestMap = new HashMap<>();

            // ========== 基础信息 (15个字段) ==========
            requestMap.put("isNew", 0);  // 更新操作
            requestMap.put("deliveryDirect", 1);
            requestMap.put("buyorderId", orderId != null ? Long.valueOf(orderId) : null);
            requestMap.put("status", 0);
            requestMap.put("goodsIds", "");
            requestMap.put("saleorderGoodsIds", "");
            requestMap.put("delBuyGoodsIds", "");
            requestMap.put("orderType", 0);
            requestMap.put("isGift", 0);
            requestMap.put("isBhOrder", false);

            // ========== 供应商信息 (10个字段) - 从supplierInfo获取（下一级公司） ==========
            // 如果有供应商信息，使用supplierInfo；否则使用默认值
            Map<String, Object> supplierData = supplierInfo != null ? supplierInfo : new HashMap<>();
            String traderName = (String) supplierData.get("TRADER_NAME");
            if (traderName == null) traderName = ""; // 防止空值

            // 记录供应商信息使用情况
            if (supplierInfo != null) {
                log.info("使用下一级公司供应商信息 - traderId: {}, traderName: {}",
                        supplierData.get("TRADER_ID"), traderName);
            } else {
                log.info("使用默认供应商信息（无下一级公司或信息获取失败）");
            }

            requestMap.put("searchTraderName", traderName);
            requestMap.put("traderSupplierId", "");
            requestMap.put("traderId", extractLong(supplierData.get("TRADER_ID")));
            requestMap.put("traderName", traderName);
            requestMap.put("periodBalance", 0);
            requestMap.put("validRebateCharge", 0);
            requestMap.put("traderContactStr", buildTraderContactStr(supplierData));
            requestMap.put("traderAddressStr", buildTraderAddressStr(supplierData));
            requestMap.put("traderComments", "");

            // ========== 直接查询并构建商品相关字段 ==========
            buildGoodsRelatedFields(requestMap, orderId, flowOrderId, targetCompany, supplierCompany,  saleOrderNo, flowOrder);

            // ========== 费用与支付 (10个字段) - 使用商品实际总金额 ==========
            requestMap.put("buyorderExpenseId", "");
            requestMap.put("paymentType", 419);
            requestMap.put("bankAcceptance", "");
            // 计算原始流转单中所有商品的合计金额作为预付金额
            BigDecimal totalGoodsAmount = calculateTotalGoodsAmount(flowOrderId, targetCompany);
            requestMap.put("prepaidAmount", totalGoodsAmount.toString());
            requestMap.put("accountPeriodAmount", 0);
            requestMap.put("haveAccountPeriod", 0);
            requestMap.put("retainageAmount", 0);
            requestMap.put("retainageAmountMonth", "");
            requestMap.put("paymentComments", "");

            // ========== 发票信息 (2个字段) ==========
            requestMap.put("invoiceType", 972);
            requestMap.put("invoiceComments", "");

            // ========== 物流信息 (3个字段) ==========
            requestMap.put("logisticsId", 0);
            requestMap.put("freightDescription", 470);
            requestMap.put("logisticsComments", "");
            requestMap.put("firstSendReceiveFlag", 1);

            // ========== 其他信息 (2个字段) ==========
            requestMap.put("additionalClause", "");
            requestMap.put("comments", "");

            log.info("完整采购订单参数构建完成 - orderId: {}, supplier: {}, 参数: {}",
                    orderId, traderName, JSONUtil.toJsonStr(requestMap));

            return requestMap;
        });
    }


    /**
     * 格式化时间戳为日期字符串
     */
    private String formatTimeToDateString(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        try {
            LocalDate date = Instant.ofEpochMilli(timestamp)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            log.error("时间格式转换失败: {}", timestamp, e);
            return "";
        }
    }


    /**
     * 通用的分隔符字符串构建方法
     */
    private String buildDelimitedString(Object... values) {
        return Arrays.stream(values)
                .map(v -> v != null ? v.toString() : "")
                .collect(Collectors.joining("|"));
    }

    /**
     * 构建供应商联系人字符串 - 使用nodeInfo数据
     */
    private String buildTraderContactStr(Map<String, Object> nodeInfo) {
        if (nodeInfo == null || nodeInfo.get("TRADER_CONTACT_ID") == null) {
            return "";
        }
        return buildDelimitedString(extractInteger(nodeInfo.get("TRADER_CONTACT_ID")),
                nodeInfo.get("TRADER_CONTACT_NAME"),
                nodeInfo.get("TRADER_CONTACT_PHONE")) + "|";
    }

    /**
     * 构建供应商地址字符串 - 使用nodeInfo数据
     */
    private String buildTraderAddressStr(Map<String, Object> nodeInfo) {
        if (nodeInfo == null || nodeInfo.get("TRADER_ADDRESS_ID") == null) {
            return "";
        }
        return buildDelimitedString(extractInteger(nodeInfo.get("TRADER_ADDRESS_ID")),
                "", // area字段暂时为空
                nodeInfo.get("TRADER_CONTACT_ADDRESS"));
    }


}
