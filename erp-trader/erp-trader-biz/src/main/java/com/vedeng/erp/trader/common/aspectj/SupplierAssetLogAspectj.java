package com.vedeng.erp.trader.common.aspectj;

import com.vedeng.erp.trader.common.annotation.SupplierAssetLogAnnotation;
import com.vedeng.erp.trader.dto.SupplierAssetChangeDto;
import com.vedeng.erp.trader.domain.dto.SupplierAssetDto;
import com.vedeng.erp.trader.domain.dto.SupplierAssetLogDto;
import com.vedeng.erp.trader.mapper.SupplierAssetLogMapper;
import com.vedeng.erp.trader.mapstruct.SupplierAssetLogConvertor;
import com.vedeng.erp.trader.service.SupplierAssetService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 资产变化日志切面
 * @date 2023/11/23 13:51
 */
@Aspect
@Component
@Slf4j
public class SupplierAssetLogAspectj {

    @Autowired
    SupplierAssetService supplierAssetService;
    @Autowired
    SupplierAssetLogMapper supplierAssetLogMapper;
    @Autowired
    SupplierAssetLogConvertor supplierAssetLogConvertor;

    /**
     * 配置织入点
     */
    @Pointcut("@annotation(logAnnotation)")
    public void logPointCut(SupplierAssetLogAnnotation logAnnotation) {
    }


    @Around(value = "logPointCut(logAnnotation)", argNames = "pjp,logAnnotation")
    public void around(ProceedingJoinPoint pjp, SupplierAssetLogAnnotation logAnnotation) throws Throwable {
        log.info("资产变化日志切面-->执行方法前处理");
        // 新增资产变化日志
        SupplierAssetLogDto supplierAssetLogDto = new SupplierAssetLogDto();
        // 参数转换
        SupplierAssetChangeDto supplierAssetChangeDto = (SupplierAssetChangeDto) pjp.getArgs()[0];


        supplierAssetLogDto.setTraderSupplierId(supplierAssetChangeDto.getTraderSupplierId());
        supplierAssetLogDto.setBusinessNo(supplierAssetChangeDto.getBusinessNumber());
        supplierAssetLogDto.setSourceType(supplierAssetChangeDto.getBusinessSourceType().getType());
        supplierAssetLogDto.setAssetType(supplierAssetChangeDto.getSupplierAsset().getCode());
        supplierAssetLogDto.setOperateNote(supplierAssetChangeDto.getRemark());
        supplierAssetLogDto.setChangeType(logAnnotation.type());
        supplierAssetLogDto.setChangeCount(supplierAssetChangeDto.getQuantity());

        SupplierAssetDto supplierAssetDto = supplierAssetService.getSupplierAsset(supplierAssetChangeDto.getTraderSupplierId(),
                supplierAssetChangeDto.getSupplierAsset().getCode());

        if (supplierAssetDto == null) {
            //变化前资产
            supplierAssetLogDto.setChangeBeforeTotal(BigDecimal.ZERO);
            //变化前可用资产
            supplierAssetLogDto.setChangeBeforeApply(BigDecimal.ZERO);
            //变化前占用资产
            supplierAssetLogDto.setChangeBeforeOccupy(BigDecimal.ZERO);
        } else {

            //变化前总资产
            supplierAssetLogDto.setChangeBeforeTotal(supplierAssetDto.getAsset());
            //变化前可用资产
            supplierAssetLogDto.setChangeBeforeApply(supplierAssetDto.getApplyAsset());
            //变化前占用资产
            supplierAssetLogDto.setChangeBeforeOccupy(supplierAssetDto.getOccupyAsset());
        }
        pjp.proceed();
        SupplierAssetDto supplierAssetDtoChange = supplierAssetService.getSupplierAsset(supplierAssetChangeDto.getTraderSupplierId(),
                supplierAssetChangeDto.getSupplierAsset().getCode());
        //变化后总资产
        supplierAssetLogDto.setChangeAfterTotal(supplierAssetDtoChange.getAsset());
        //变化后可用资产
        supplierAssetLogDto.setChangeAfterApply(supplierAssetDtoChange.getApplyAsset());
        //变化后占用资产
        supplierAssetLogDto.setChangeAfterOccupy(supplierAssetDtoChange.getOccupyAsset());

        //变化的总资产
        supplierAssetLogDto.setChangeCountTotal(supplierAssetLogDto.getChangeAfterTotal().subtract(supplierAssetLogDto.getChangeBeforeTotal()));
        //变化的可用资产
        supplierAssetLogDto.setChangeCountApply(supplierAssetLogDto.getChangeAfterApply().subtract(supplierAssetLogDto.getChangeBeforeApply()));
        //变化的占用资产
        supplierAssetLogDto.setChangeCountOccupy(supplierAssetLogDto.getChangeAfterOccupy().subtract(supplierAssetLogDto.getChangeBeforeOccupy()));
        supplierAssetLogDto.setSupplierAssetId(supplierAssetDtoChange.getSupplierAssetId());
        supplierAssetLogMapper.insertSelective(supplierAssetLogConvertor.toEntity(supplierAssetLogDto));
    }

}
