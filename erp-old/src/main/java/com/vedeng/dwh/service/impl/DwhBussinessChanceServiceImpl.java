package com.vedeng.dwh.service.impl;

import com.vedeng.dwh.externaldb.dao.DwhWorkbenchMapper;
import com.vedeng.dwh.model.dto.DwhQueryParamsDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto;
import com.vedeng.dwh.service.DwhBussinessChanceService;
import com.vedeng.workbench.model.HeaddataSummaryDto;
import com.vedeng.workbench.model.dto.WorkbenchDataQueryDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@SuppressWarnings("Duplicates")
@Service
public class DwhBussinessChanceServiceImpl implements DwhBussinessChanceService {

    @Resource
    private DwhWorkbenchMapper workbenchMapper;

    //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
    private List<Integer> statusList = Arrays.asList(0,1,2,3,4,6,7);

    // add by <PERSON>.Xu 2020/11/7 20:52 .Desc: . begin
    private List<Integer> partStatusList = Arrays.asList(0,1,2,3,6);
    // add by Randy.Xu 2020/11/7 20:52 .Desc: . end


    @Override
    public DwhWorkbenchOverviewDto getHisBussinessChanceOverviewByUser(Integer userId, Long startTime, Long endTime) {
        return getUserOverview(userId, 1,partStatusList, startTime, endTime);
    }

    @Override
    public DwhWorkbenchOverviewDto getYesterdayBussinessChanceOverviewByUser(Integer userId, Long startTime, Long endTime) {
        return getUserOverview(userId, 1,statusList, startTime, endTime);
    }

    @Override
    public DwhWorkbenchOverviewDto getExpectBussinessChanceOverviewByUser(Integer userId, Long startTime, Long endTime) {
        return getUserOverview(userId, 2, statusList, startTime, endTime);
    }

    @Override
    public List<DwhWorkbenchOverviewDto> getBussinessChanceStatusOverviewByUser(Integer userId, Long startTime, Long endTime) {
        DwhQueryParamsDto queryParams = new DwhQueryParamsDto();
        queryParams.setStartTimeMillions(startTime);
        queryParams.setEndTimeMillions(endTime);
        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        queryParams.setStatus(statusList);
        queryParams.setUserId(userId);
        return workbenchMapper.getBussinessStatus(queryParams);
    }

    @Override
    public List<DwhWorkbenchDto> getImportantBussinessChancesByUser(Integer userId, Long startTime, Long endTime) {
        DwhQueryParamsDto queryParams = new DwhQueryParamsDto();
        queryParams.setStartTimeMillions(startTime);
        queryParams.setEndTimeMillions(endTime);
        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        queryParams.setStatus(partStatusList);
        queryParams.setUserId(userId);
        return workbenchMapper.getImportBussinessChance(queryParams);
    }

    // changed by Randy.Xu   .Desc: begin
    @Override
    public List<DwhWorkbenchOverviewDto> getHisBussinessChanceOverviewByDept(List<Integer> orgIds,Integer type,Long startTime, Long endTime) {
        DwhQueryParamsDto queryParams = new DwhQueryParamsDto();
        queryParams.setStartTimeMillions(startTime);
        queryParams.setEndTimeMillions(endTime);
        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        queryParams.setStatus(partStatusList);
        queryParams.setOverviewType(1);
        queryParams.setUserType(type);
        queryParams.setOrgIds(orgIds);
        return workbenchMapper.getLeaderBussinessChanceOverview(queryParams);
    }

    @Override
    public List<DwhWorkbenchOverviewDto> getYesterdayBussinessChanceOverviewByDept(List<Integer> orgIds,Integer type,Long startTime, Long endTime) {
        DwhQueryParamsDto queryParams = new DwhQueryParamsDto();
        queryParams.setStartTimeMillions(startTime);
        queryParams.setEndTimeMillions(endTime);
        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        queryParams.setStatus(statusList);
        queryParams.setOverviewType(1);
        queryParams.setUserType(type);
        queryParams.setOrgIds(orgIds);
        return workbenchMapper.getLeaderBussinessChanceOverview(queryParams);
    }
    // changed by Randy.Xu   .Desc: end

    @Override
    public List<DwhWorkbenchOverviewDto> getBussinessChanceStatusOverviewByDept(List<Integer> orgIds,Integer type, Long startTime, Long endTime) {
        DwhQueryParamsDto queryParams = new DwhQueryParamsDto();
        queryParams.setStartTimeMillions(startTime);
        queryParams.setEndTimeMillions(endTime);
        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        queryParams.setStatus(statusList);
        queryParams.setUserType(type);
        queryParams.setOrgIds(orgIds);
        return workbenchMapper.getLeaderBussinessStatus(queryParams);
    }

    @Override
    public List<DwhWorkbenchOverviewDto> getImportantBussinessChancesByDept(List<Integer> orgIds,Integer type,Long startTime, Long endTime) {
        DwhQueryParamsDto queryParams = new DwhQueryParamsDto();
        queryParams.setStartTimeMillions(startTime);
        queryParams.setEndTimeMillions(endTime);
        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        queryParams.setStatus(partStatusList);
        queryParams.setUserType(type);
        queryParams.setOrgIds(orgIds);
        return workbenchMapper.getLeaderImportBussinessChance(queryParams);
    }

    // add by Randy.Xu 2020/11/5 16:32 .Desc: . begin
    @Override
    public List<DwhWorkbenchOverviewDto> getExpectBussinessChanceOverviewByDept(List<Integer> orgIds, Integer type, Long startTime, Long endTime) {
        DwhQueryParamsDto queryParams = new DwhQueryParamsDto();
        queryParams.setStartTimeMillions(startTime);
        queryParams.setEndTimeMillions(endTime);
        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        queryParams.setStatus(statusList);
        queryParams.setUserType(type);
        queryParams.setOverviewType(2);
        queryParams.setOrgIds(orgIds);
        return workbenchMapper.getLeaderBussinessChanceOverview(queryParams);
    }


    // add by Randy.Xu 2020/11/5 16:32 .Desc: . end



    /**
     * 获取时间段内商机汇总.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 2:45 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhWorkbenchDto>.
     * @throw: .
     */
    private DwhWorkbenchOverviewDto getUserOverview(Integer userId, Integer overview, List<Integer> status, Long startTime, Long endTime) {
        DwhQueryParamsDto queryParams = new DwhQueryParamsDto();
        queryParams.setOverviewType(overview);
        queryParams.setStartTimeMillions(startTime);
        queryParams.setEndTimeMillions(endTime);
        //商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
        queryParams.setStatus(status);
        queryParams.setUserId(userId);
        return workbenchMapper.getUserBussinessChanceOverview(queryParams);
    }


}
