package com.vedeng.erp.kingdee.batch.common.enums;

import java.util.Arrays;
import java.util.List;

public enum ChoosePayObjectEnum {
    PURCHASE(517,"采购"),
    AFTER_SALE(518,"售后"),
    PURCHASE_FEE(4125,"采购费用"),
    THIRD_OBJECT(537,"第三方"),
    SALES(535,"销售"),
    AFTER_SALES_AT(541,"销售订单安调"),
    AFTER_SALES_WX(584,"销售订单维修"),
    AFTER_SALES_HT_AT(4090,"销售订单合同安调"),
    AFTER_SALES_FJ_AT(4091,"销售订单附加服务安调"),

    ;
    private final Integer sysOptionId;
    private final String desc;

    public static final String BD_SUPPLIER = "BD_Supplier"; //付款单
    public static final String BD_CUSTOMER = "BD_Customer"; //收款退款单
    ChoosePayObjectEnum(Integer sysOptionId, String desc) {
        this.sysOptionId = sysOptionId;
        this.desc = desc;
    }

    public Integer getSysOptionId() {
        return sysOptionId;
    }

    public String getDesc() {
        return desc;
    }

    //获取针对供应商的TYPE集合
    public static List<Integer> supplierTypeList(){
        return Arrays.asList(ChoosePayObjectEnum.AFTER_SALES_AT.sysOptionId,
                             ChoosePayObjectEnum.AFTER_SALES_WX.sysOptionId,
                             ChoosePayObjectEnum.AFTER_SALES_HT_AT.sysOptionId,
                             ChoosePayObjectEnum.AFTER_SALES_FJ_AT.sysOptionId);
    }
}
