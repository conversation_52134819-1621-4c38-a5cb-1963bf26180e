package com.vedeng.temporal.notification;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;

/**
 * 通知上下文
 * 收集发送通知所需的所有信息
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-10
 */
@Data
@Builder
public class NotificationContext {
    
    /**
     * 业务ID
     */
    private String businessId;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 目标公司代码
     */
    private String targetCompany;
    
    /**
     * 源公司代码
     */
    private String sourceCompany;
    
    /**
     * 操作名称
     */
    private String operationName;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 异常类型
     */
    private String exceptionType;
    
    /**
     * 发生位置
     */
    private String location;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 创建空的上下文
     */
    @JsonIgnore
    public static NotificationContext empty() {
        return NotificationContext.builder().build();
    }

    /**
     * 从业务请求创建上下文
     */
    @JsonIgnore
    public static NotificationContext fromRequest(com.vedeng.temporal.domain.dto.CompanyBusinessRequest request, String operationName) {
        return NotificationContext.builder()
                .businessId(request.getBusinessId())
                .businessType(request.getBusinessType())
                .targetCompany(request.getTargetCompanyCode())
                .sourceCompany(request.getSourceCompanyCode())
                .operationName(operationName)
                .location("Activity层")
                .retryCount(0)
                .build();
    }
    
    /**
     * 设置失败信息
     */
    public NotificationContext withFailure(String errorMessage, String errorCode, Long duration) {
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.duration = duration;
        return this;
    }
    
    /**
     * 设置异常信息
     */
    public NotificationContext withException(Exception e, Long duration) {
        this.exceptionType = e.getClass().getSimpleName();
        this.errorMessage = e.getMessage();
        this.duration = duration;
        return this;
    }
}
