package com.vedeng.orderstream.aftersales.dao;

import com.vedeng.orderstream.aftersales.model.AfterSalesRevenueRecord;
import com.vedeng.orderstream.aftersales.model.AfterSalesRevenueRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named("afterSalesRevenueRecordMapper")
public interface AfterSalesRevenueRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int countByExample(AfterSalesRevenueRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int deleteByExample(AfterSalesRevenueRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int deleteByPrimaryKey(Integer recordId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int insert(AfterSalesRevenueRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int insertSelective(AfterSalesRevenueRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    List<AfterSalesRevenueRecord> selectByExample(AfterSalesRevenueRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    AfterSalesRevenueRecord selectByPrimaryKey(Integer recordId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int updateByExampleSelective(@Param("record") AfterSalesRevenueRecord record, @Param("example") AfterSalesRevenueRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int updateByExample(@Param("record") AfterSalesRevenueRecord record, @Param("example") AfterSalesRevenueRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int updateByPrimaryKeySelective(AfterSalesRevenueRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_REVENUE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int updateByPrimaryKey(AfterSalesRevenueRecord record);

    /**
     * Obtain after sales follow-up records
     * Using parameter afterSalesId
     * @simgo Tue Oct 12 11:17:55 CST 2021
     * @param afterSalesId
     * @return
     */
    List<AfterSalesRevenueRecord> selectByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);
}