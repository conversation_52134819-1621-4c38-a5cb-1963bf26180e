package com.wms.service.chain;


import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public abstract class AbstractHandlerStep implements HandlerStep {

    private HandlerStep next;

    public HandlerStep getNext() {
        return next;
    }

    public void setNext(HandlerStep next) {
        this.next = next;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealWith(HandlerStepContext context) throws Exception{

        //先处理
        this.doDealWith(context);

        if(next != null){
            next.dealWith(context);
        }
    }

    protected abstract void doDealWith(HandlerStepContext context) throws Exception;
}
