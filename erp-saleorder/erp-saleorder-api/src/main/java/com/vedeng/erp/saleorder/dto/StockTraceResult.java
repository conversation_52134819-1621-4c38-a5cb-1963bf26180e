package com.vedeng.erp.saleorder.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName StockTraceResult.java
 * @Description TODO 货流结果
 * @createTime 2022年09月05日 10:24:00
 */
@Data
public class StockTraceResult {
    /**
     * 原单号
     */
    private String originalOrderNo;
    /**
     * sku
     */
    private String sku;
    /**
     * 直接目标单号
     */
    private String tagerOrderNo;
    /**
     * 跟踪单号
     */
    private String soruceOrderNos;
    /**
     * 原单物流时间
     */
    private String originalTime;
    /**
     * 直接目标单物流时间
     */
    private String tagerTime;
    /**
     * 跟踪单物流时间
     */
    private String soruceTime;
    /**
     * sn码
     */
    private String barcodeFactory;
    /**
     * 批次号
     */
    private String batchNumber;
}
