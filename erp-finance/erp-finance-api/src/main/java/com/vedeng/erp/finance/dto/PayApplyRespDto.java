package com.vedeng.erp.finance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayApplyRespDto {

    private Integer isBill;//是否制单 0否1是
    private Integer payApplyId;
    private Integer payType;//付款方式
    private Integer relatedId;
    private String buyorderNo;//订单号采购订单
    private String afterSalesNo;//订单号售后订单
    private Integer verifyStatus;//审核状态
    private List<String> verifyUsernameList;//当前审核人
    private String buyorderTraderName;//供应商名称
    private BigDecimal amount;//申请金额
    private String addTime;// 申请时间
    private String creatorName;//申请人
    private String traderName;//收款名称
    private Integer traderSubject;//交易主体
    private Integer is_528;// 是否余额支付
    private String comments;//内部付款备注
    private String bankRemark;//银行回单备注
    private String payBankTypeName;//付款银行
    private Integer accountType;//往来单位类型	0客户1供应商
    private Integer autoBill;//满足自动制单
    private Integer billMethod;//制单方式
    private String billTime;//制单时间

    //统计字段
    private String totalRecord;//全部结果-订单数
    private String payApplyTotalAmount;//全部结果-申请总额
    private String payApplyPayTotalAmount;//全部结果-付款总额
    private String pageNum;//本页-订单数
    private String pageApplyAmount;//本页-申请总额
    private String pagePayAmount;//本页-付款总额
}
