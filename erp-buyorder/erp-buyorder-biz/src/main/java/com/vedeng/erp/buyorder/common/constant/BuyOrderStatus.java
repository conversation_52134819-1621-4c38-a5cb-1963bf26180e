package com.vedeng.erp.buyorder.common.constant;

/**
 * <AUTHOR>
 * @Description com.vedeng.orderstream.buyorder.constant
 * @Date 2021/10/16 11:25
 */
public class BuyOrderStatus {

    public final static String WAIT_CONFIRM = "待确认";
    public final static String VALIDATING = "审核中";
    public final static String WAIT_PAYMENT = "待付款";
    public final static String WAIT_DELIVERY = "待发货";
    public final static String WAIT_RECEIVE_GOODS = "待收货";
    public final static String WAIT_RECEIVE_INVOICE = "待收票";
    public final static String COMPLETED = "已完结";
    public final static String CLOSED = "已关闭";

    public final static String START_AUDIT = "开始审核";
    public final static String APPLICANT = "申请人";
    public final static String PM_VALIDATING = "产品经理或助理审核";
    public final static String QUALITY_AUTOMATIC_VALIDATING = "资质自动审核";
    public final static String QUALITY_DEPART_VALIDATING = "质量管理部审核";
    public final static String FINANCIAL_VOUCHER = "财务制单";
    public final static String FINANCIAL_AUDIT = "财务审核";
    public final static String FINISHED = "审核完成";
    public final static String REJECT = "驳回";


}
