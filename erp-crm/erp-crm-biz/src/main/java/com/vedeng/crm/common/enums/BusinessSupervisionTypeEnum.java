package com.vedeng.crm.common.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 督导类型
 * <AUTHOR>
 */
public enum BusinessSupervisionTypeEnum {


    /**
     * 商机论证阶段
     */
    KEY_INFORMATION_COLLECTION("101","关键信息收集","OpportunityValidation"),

    CONFIRM_BUDGET("102","确认预算","OpportunityValidation"),

    CONFIRM_OPPORTUNITY_LEVEL("103","确认商机等级，商机阶段","OpportunityValidation"),

    COMPETITOR_ANALYSIS("104","竞争对手分析","OpportunityValidation"),


    /**
     * 产品方案阶段
     */
    PRODUCT_INQUIRY("201","询价报备授权","ProductSolution"),

    PRELIMINARY_PRODUCT_PLAN("202","初步方案","ProductSolution"),

    SPECIAL_PRICE_APPLICATION("203","特价申请","ProductSolution"),

    BUSINESS_TRIP_SUPPORT("204","出差支持","ProductSolution"),

    SITE_SURVEY("205","场地勘查","ProductSolution"),

    DEMO_UNIT_USAGE("206","样机试用","ProductSolution"),

    MODEL_HOSPITAL_VISIT("207","样板医院参观","ProductSolution"),

    CLINICAL_TRAINING("208","临床培训","ProductSolution"),

    PRODUCT_PLAN_REVISION("209","产品方案修正","ProductSolution");



    private final String value;
    private final String label;
    private final String type;

    BusinessSupervisionTypeEnum(String value, String label, String type) {
        this.value = value;
        this.label = label;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public String getType() {
        return type;
    }


    /**
     * 根据 type 获取所有匹配的枚举值
     * @param type 类型
     * @return 匹配的枚举值列表
     */
    public static List<BusinessSupervisionTypeEnum> getEnumsByType(String type) {
        return Arrays.stream(BusinessSupervisionTypeEnum.values())
                .filter(enumValue -> enumValue.getType().equals(type))
                .collect(Collectors.toList());
    }

    public static BusinessSupervisionTypeEnum getBusinessSupervisionTypeEnum(String code) {
        return Arrays.stream(BusinessSupervisionTypeEnum.values())
                .filter(enumValue -> enumValue.getValue().equals(code))
                .findFirst()
                .orElse(null);
    }
}
