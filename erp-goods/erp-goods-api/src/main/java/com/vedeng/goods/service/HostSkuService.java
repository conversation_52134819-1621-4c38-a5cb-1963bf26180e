package com.vedeng.goods.service;

import com.vedeng.goods.dto.VHostWordDTO;
import com.vedeng.goods.vo.OpHostWordVo;
import com.vedeng.goods.dto.VHostWordDTO;
import lombok.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName HostSkuService.java
 * @Description TODO 热词SKU相关业务
 * @createTime 2021年12月31日 15:32:00
 */
public interface HostSkuService {

    /**
     * @description: 保存op推送热词信息
     * @author: Strange
     * @date: 2022/1/1
     **/
    void saveHostByopPushMsg(@NonNull VHostWordDTO hostWordDTO, @NonNull List<String> skuDTOList);

    /**
     * 根据商品的spu查询分类id更具分类id去op中查询分类关联热词,
     * 根据sku信息查询已经绑定到商品上的热词
     * @param spuId spuId
     * @param skuId skuId
     * @return erp热词对象集合
     */
    List<VHostWordDTO> queryHotWordByCategory(@NonNull Integer spuId, @NonNull Integer skuId);

    /**
     * 推送到op的此商品的热词集合
     * @param skuNo sku
     * @return 给op的数据
     */
    List<OpHostWordVo> queryHotWordInErpBySkuNo(@NonNull String skuNo);

    /**
     * 三级分类查询时候保存没有的热词
     * @param hostWord opId(op中热词id) ：word
     * @return opId 集合
     */
    List<Integer> saveHotWord(List<String> hostWord);

    /**
     * 绑定更新sku对应热词
     * 原来没有绑定
     * 原来有现在没有删除
     * @param hotWords opHostWordId集合
     * @param skuId skuId
     */
    void bindGoodsHotWords(List<Integer> hotWords, @NonNull Integer skuId,@NonNull String username);
}
