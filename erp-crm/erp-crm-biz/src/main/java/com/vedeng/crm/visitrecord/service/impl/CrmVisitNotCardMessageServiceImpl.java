package com.vedeng.crm.visitrecord.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.visitrecord.domain.dto.VisitEditInputDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitTongxingUserVo;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/10
 */
@Service("CrmVisitNotCardMessageServiceImpl")
public class CrmVisitNotCardMessageServiceImpl extends BaseMessageSendServiceImpl {


    public void sendMessage(VisitRecordVo visitRecordVo, Integer recordId, List<VisitTongxingUserVo> tongxingUserList , Integer messageReceiveUserId) {
        String tongxingNamesStr = "";
        if (CollectionUtils.isNotEmpty(tongxingUserList)) {
            List<String> tongXingUserNames = CollectionUtil.isEmpty(tongxingUserList) ? new ArrayList<>()
                    : (tongxingUserList.stream().map(VisitTongxingUserVo::getUserName).collect(Collectors.toList()));
            tongxingNamesStr = StringUtils.join(tongXingUserNames,"，");
        }
        //创建拼接消息，消息模板如下（没有同行人时，消息通知内无同行人字段）：
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append("拜访编号：").append(visitRecordVo.getVisitRecordNo()).append("\n");
        messageBuilder.append("拜访客户：").append(visitRecordVo.getCustomerName()).append("\n");
        messageBuilder.append("拜访目标：").append(visitRecordVo.getVisitTargetStr()).append("\n");
        messageBuilder.append("计划拜访时间：").append(DateUtil.format(visitRecordVo.getPlanVisitDate(),"yyyy-MM-dd") ).append("\n");
        messageBuilder.append("拜访人：").append(visitRecordVo.getVisitorName()).append("\n");
        // 如果有同行人则添加同行人信息
        if (StringUtils.isNotEmpty(tongxingNamesStr)) {
            messageBuilder.append("同行人：").append(tongxingNamesStr).append("\n");
        }
        UserDto userCreateDto = userApiService.getUserBaseInfo(visitRecordVo.getAddUserId());
        messageBuilder.append("创建人：").append(userCreateDto.getUsername());
        String message = messageBuilder.toString();

        UserDto userReceiveDto = userApiService.getUserBaseInfo(messageReceiveUserId);
        SendMessageDto sendMessageDto = new SendMessageDto();
        sendMessageDto.setUrl(jumpService.getMjumpUrl(crmApplicationMessageJumpUrl + "/crm/visitRecord/m/detail?id=" + recordId,
                lxcrmUrl + "/crm/visitRecord/profile/detail?id=" + recordId,
                JumpErpTitleEnum.VISIT_RECORD_DETAIL));
        sendMessageDto.setUserNumber(userReceiveDto.getNumber());
        sendMessageDto.setFormat(message);
        sendCardMsg(sendMessageDto, "今日有拜访尚未打卡");


    }
}
