@import (less) '../common.css';

.vd-ui-table-wrap {
    width: 100%;

    .vd-ui-wrap-is-left {

        .vd-ui-th,
        .vd-ui-td {
            transition: box-shadow 0.3s;

            &.sticky-item {
                position: sticky;
                left: 0;
                z-index: 3;

                &:hover {
                    z-index: 4;
                }
            }
        }
    }

    .vd-ui-wrap-is-right {

        .vd-ui-th,
        .vd-ui-td {
            transition: box-shadow 0.3s;

            &:last-child {
                position: sticky;
                right: 0;
                z-index: 3;
            }
        }
    }

    .vd-ui-table-setting {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        display: flex;
        align-items: center;
        padding: 0 10px;
        border-left: 1px solid @Gray-4;
        cursor: pointer;
        font-size: 16px;
        background: @Gray-2;

        &:hover {
            background: @Gray-3;
        }

        .vd-ui-table-setting-icon {
            transform: rotate(90deg);
        }

        &.vd-ui-table-setting-right {
            right: -6px;
            z-index: 11;
        }
    }

    .vd-ui-table-th-cnt {
        display: flex;
        align-items: center;
        margin: -8px -10px;

        .vd-ui-table-th-inner {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 10px 0 9px 10px;
            cursor: pointer;

            &:hover {
                background: @Gray-3;
            }

            .vd-ui-table-th-txt {
                flex: 1;
            }
        }

        .vd-ui-table-filter {
            cursor: pointer;
            color: #BABFC2;

            .icon-filter {
                width: 24px;
                font-size: 16px;
                height: 37px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            &:hover,
            &.active {
                background: #EBEFF2;
                color: #999;
            }

            &.selected {
                color: #FF6600;
            }
        }

        &.th-cnt-sum {
            .vd-ui-table-th-txt {
                padding-right: 30px;
            }
            
            .vd-ui-table-th-sum {
                position: absolute;
                width: 112px;
                height: 37px;
                left: 0;
                top: 0;
        
                .vd-ui-tip-icon-sum {
                    width: 112px;
                    height: 37px;
                    background-image: url(../image/common/icon-sum.svg);
                    background-size: 16px 16px;
                    background-position: right center;
                    background-repeat: no-repeat;
                }

                .vd-ui-tip-wrap {
                    &.tr {
                        .vd-ui-tip-cnt {
                            bottom: 37px;
                            padding-bottom: 7px;
                            left: 86px;
                
                            .vd-ui-tip-inner::before {
                                bottom: -12px;
                                left: 12px;
                                border-top-color: #fff;
                            }
                        }
                    }
                }
            }
        }

        &:hover {
            &.th-cnt-sum {
                .vd-ui-table-th-sum {
                    .vd-ui-tip-icon-sum {
                        background-image: url(../image/common/icon-sum-hover.svg);
                    }
                }
            }
        }
    }

    .vd-ui-table-sort {
        padding: 0 4px;

        .vd-ui-sort-icon {
            display: block;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 6px solid @Gray-5;

            &.vd-ui-sort-icon-down {
                transform: rotate(180deg);
                margin-top: 2px;
            }

            &.vd-ui-sort-icon-active {
                border-bottom-color: @OrangeRed-6;
            }
        }

    }

    .vd-ui-th-tip-wrap {
        display: flex;
        align-items: center;

        .vd-ui-th-txt {
            margin-right: 5px;
        }

        .vd-ui_icon {
            color: #999;
            font-size: 16px;
            line-height: 1;
            vertical-align: -2px;
        }
    }

    .vd-ui-table-header {
        overflow: hidden;
        position: relative;

        &.header-fixed {
            position: fixed;
            top: 0;
            z-index: 11;

            .vd-ui-table-th-cnt {
                &.th-cnt-sum {
                    .vd-ui-tip-wrap {
                        &.tr {
                            .vd-ui-tip-cnt {
                                bottom: auto;
                                top: 37px;
                                padding-top: 6px;
                                padding-bottom: 0;
                    
                                .vd-ui-tip-inner::before {
                                    top: -12px;
                                    bottom: auto;
                                    left: 12px;
                                    border-color: transparent;
                                    border-bottom-color: #fff;
                                }
                            }
                        }
                    }
                }
            }
        }

        &.vd-ui-wrap-right-scroll-fixed {
            .vd-ui-th {
                &:nth-last-child(2) {
                    position: sticky;
                    border-right-color: @Gray-2;
                }

                &:last-child {
                    border-left-color: @Gray-2;
                }
            }

            .vd-ui-table.vd-ui-table-right-fixed {
                .vd-ui-th {
                    &:nth-last-child(2) {
                        border-left: 0;

                        &:after {
                            position: absolute;
                            top: -1px;
                            right: -1px;
                            width: calc(100% - 1px);
                            height: 100%;
                            content: "";
                            pointer-events: none;
                        }

                        &:before {
                            position: absolute;
                            top: 0;
                            right: 0;
                            bottom: -1px;
                            width: 100%;
                            transform: translate(-100%);
                            transition: box-shadow 0.3s;
                            content: "";
                            pointer-events: none;
                            box-shadow: inset -10px 0 8px -8px #00000026;
                        }
                    }

                    &:last-child {
                        border-left: 1px solid @Gray-2;

                        &:after {
                            background: @Gray-2;
                            left: -2px;
                            top: 1px;
                            height: calc(100% - 2px);
                        }

                        &:before {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    .vd-ui-table-body {
        overflow: auto;
        position: relative;
        .scrollbar;

        &.vd-ui-wrap-scroll {
            overflow-y: auto;
        }
    }

    .vd-ui-table-container {
        width: 100%;
        overflow: auto;

        &.vd-table-width-border {
            border-left: 1px solid #E1E5E8;
            border-right: 1px solid #E1E5E8;

            .ui-table-list-empty {
                border-bottom: 1px solid #E1E5E8;
            }
        }

        &.vd-table-border {
            .vd-ui-table .vd-ui-td {
                border: 1px solid #E1E5E8;

                &:first-child {
                    border-left: 0;
                }

                &:last-child {
                    border-right: 0;
                }
            }

            .vd-ui-table-body {
                &.vd-ui-wrap-is-right, &.vd-ui-wrap-is-left {
                    margin-top: -1px;
                }
            }

            &.vd-table-border-nofix {
                .vd-ui-table-body {
                    margin-top: 0;
                }
            }
        }

        .ui-table-list-empty {
            padding: 100px 0;
            text-align: center;

            .empty-img {
                width: 110px;
                height: 95px;
                background-image: url(../image/list-empty.svg);
                background-size: 100% 100%;
                margin: 0 auto 20px auto;
            }

        }
    }

    .vd-ui-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        text-align: left;

        &.vd-ui-table-left-fixed {

            .vd-ui-th,
            .vd-ui-td {
                &.sticky-item {
                    &:after {
                        position: absolute;
                        top: 0;
                        left: -1px;
                        width: 1px;
                        height: 100%;
                        content: "";
                        background: #E1E5E8;
                        pointer-events: none;
                    }
                }

                &.last-fixed {
                    &:after {
                        position: absolute;
                        top: 0;
                        left: -1px;
                        width: 1px;
                        height: 100%;
                        content: "";
                        background: #E1E5E8;
                        pointer-events: none;
                    }

                    &:before {
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: -1px;
                        width: 100%;
                        transform: translate(100%);
                        transition: box-shadow 0.3s;
                        content: "";
                        pointer-events: none;
                        box-shadow: inset 10px 0 8px -8px #00000026;
                    }
                }
            }

            .vd-ui-th {
                &:first-child {
                    border-right: 0;
                }

                &:nth-child(2) {
                    border-left: 0;
                }
            }
        }

        &.vd-ui-table-right-fixed {

            .vd-ui-th,
            .vd-ui-td {
                &:last-child {
                    &:after {
                        position: absolute;
                        top: 0;
                        right: -1px;
                        width: 1px;
                        height: 100%;
                        content: "";
                        pointer-events: none;
                        background: #e1e5e8;
                    }

                    &:before {
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: -1px;
                        width: 100%;
                        transform: translate(-100%);
                        transition: box-shadow 0.3s;
                        content: "";
                        pointer-events: none;
                        box-shadow: inset -10px 0 8px -8px #00000026;
                    }
                }
            }

            .vd-ui-th {
                &:last-child {
                    border-left: 0;
                }
            }
        }

        .vd-ui-th {
            padding: 8px 10px;
            background: @Gray-2;
            font-weight: normal;
            border: solid 1px @Gray-4;
            color: @Text-2;
            position: relative;
            z-index: 2; 
            
            &.align-right {
                text-align: right;
            }
            
            &.align-center {
                text-align: center;
            }

            &:first-child {
                border-left: 0;
            }

            &:last-child {
                border-right: 0;
            }

            &.vd-ui-th-bar {
                border-left-color: @Gray-2;
                z-index: 1;
            }

            &.vd-ui-th-bar-prev {
                border-right-color: @Gray-2;
            }
        }

        .vd-ui-td {
            padding: 8px 10px;
            border-bottom: solid 1px @Gray-3;
            background: @Text-7;

            &.align-right {
                text-align: right;
            }

            &.align-center {
                text-align: center;
            }

            &.vertical-center {
                vertical-align: middle;
            }

            .option-wrap {
                display: flex;
                align-items: center;
            }

            .td-left-tip {
                position: absolute;
                top: 10px;
                left: 10px;
            }

            .table-edit {
                color: #09f;
                cursor: pointer;
                margin-right: 20px;
                position: relative;

                &::before {
                    content: '';
                    width: 1px;
                    height: 14px;
                    background: #e1e5e8;
                    position: absolute;
                    right: -10px;
                    top: 4px;
                }

                &:last-child {
                    margin-right: 0;

                    &::before {
                        display: none;
                    }
                }

                &:hover {
                    color: #f60;
                }

                &.disabled {
                    color: #999;
                    cursor: not-allowed;
                }
            }

            &.can-edit {
                position: relative;

                .ui-table-td-edit-wrap {
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 36px;
                    height: 34px;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                    cursor: pointer;
                    background: #fff;
                    display: none;
                    color: #666;

                    .vd-ui_icon {
                        pointer-events: none;
                    }

                    &:hover {
                        color: #f60;
                    }

                    &.disabled {
                        cursor: not-allowed;
                        color: #ccc;

                        &:hover {
                            color: #ccc;
                        }
                    }
                }

                &:hover {
                    .ui-table-td-edit-wrap {
                        display: flex;
                    }
                }
            }

            &.td-copy-wrap {
                position: relative;

                .td-copy-option {
                    position: absolute;
                    left: 0;
                    top: 0;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                    cursor: pointer;
                    background: #fff;
                    display: none;
                    color: #666;
                    width: 36px;
                    height: 34px;

                    &:hover {
                        color: #f60;
                    }
                }
        
                &:hover {
                    .td-copy-option {
                        display: flex;
                    }
                }
            }
        }

        tr {
            &:hover {
                .vd-ui-td {
                    background: #EBEFF2;

                    .ui-table-td-edit-wrap, .td-copy-option {
                        background: #EBEFF2;
                    }
                }
            }

            &.on-select {
                .vd-ui-td {
                    background: #E0F3FF;

                    .ui-table-td-edit-wrap, .td-copy-option {
                        background: #E0F3FF;
                    }
                }

                &:hover {
                    .vd-ui-td {
                        background: #E0F3FF;

                        .ui-table-td-edit-wrap, .td-copy-option {
                            background: #E0F3FF;
                        }
                    }
                }

                // &:hover {
                //     .vd-ui-td {
                //         background: #FAFBFC;
                //     }
                // }
            }
        }
    }

    .vd-ui-checkbox-wrap {
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        

        .vd-ui-checkbox-item .vd-ui-checkbox-icon {
            margin: 0;
        }
    }

    .ui-table-footer-scroll {
        overflow: auto;
        position: fixed;
        z-index: 80;
        bottom: 10px;

        &::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
            width: 10px;
            height: 10px;
        }

        &::-webkit-scrollbar-thumb {
            background: #D7DADE;
            width: 10px;
            height: 10px;
            border-radius: 5px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: #BABFC2;
        }

        &::-webkit-scrollbar-thumb:active {
            background: #969B9E;
        }

        .ui-table-footer-scroll-inner {
            height: 1px;
        }

        &.hidden {
            opacity: 0;
            z-index: -1;
        }
    }

    .td-link {
        color: #09f;
        cursor: pointer;

        &:hover {
            color: #f60;
        }
    }

    .vd-ui-user-wrap {
        margin: -1px 0;
    }

    .ui-view-map-wrap {
        margin: -3px 0;
    }

    .vd-ui-trader-grade {
        margin: -1px 0;
    }

    .avatar-item-wrap {
        display: flex;
        align-items: center;
        margin: -1px 0;

        .avatar-img {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            overflow: hidden;
            margin-right: 5px;
            position: relative;

            img {
                max-width: 100%;
                max-height: 100%;

                &.error {
                    width: 0;
                    height: 0;

                    &:before {
                        content: '';
                        position: absolute;
                        width: 20px;
                        height: 20px;
                        left: 0;
                        top: 0;
                        background-image: url(../image/crm-user-avatar.svg);
                        background-size: 100% 100%;
                    }
                }
            }
        }
    }

    .tel-item-wrap {
        display: flex;
        align-items: center;
        color: #09f;
        cursor: pointer;

        .icon-call {
            font-size: 16px;
            margin-right: 5px;
            line-height: 1;
        }

        &:hover {
            color: #f60;
        }

        &.normal {
            cursor: default;
            color: #333;

            .icon-call {
                color: #999;
            }

            &:hover {
                color: #333;
            }
        }
    }
}

.ui-table-filter-drop {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 111;
    background: #fff;
    border: solid 1px #BABFC2;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
    width: 300px;

    .ui-table-filter-item {
        &:hover {
            background: @Gray-2;
        }

        .vd-ui-checkbox-item {
            padding: 0 10px;
            display: flex;
            align-items: center;
            height: 33px;
            cursor: pointer;

            .vd-ui-checkbox-icon {
                margin-right: 10px;
            }
        }
    }

    .ui-table-filter-drop-list {
        padding-bottom: 10px;

        .ui-table-filter-drop-inner {
            max-height: 330px;
            overflow: auto;
            overscroll-behavior: contain;
            .scrollbar();
        }
    }

    .ui-table-filter-drop-search {
        padding: 10px;

        .vd-ui-input {
            .vd-ui-input__inner {
                border-top: 0;
                border-left: 0;
                border-right: 0;
                border-radius: 0;
            }
        }
    }

    .ui-table-filter-drop-footer {
        padding: 10px;
        display: flex;
        justify-content: flex-end;
        border-top: solid 1px #EBEFF2;

        .vd-ui-button {
            margin-left: 10px;
        }
    }

    .ui-table-filter-drop-empty {
        padding: 10px 0 20px 0;
        text-align: center;
        color: #999
    }
}