package com.vedeng.activiti.taskassign;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.activiti.ProcessInstanceContext;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.goods.model.Goods;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.logistics.model.FileDelivery;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryVO;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.BuyorderModifyApplyVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyOrderModifyApplyService;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.wms.service.WmsSampleOutService;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.history.HistoricProcessInstance;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import top.ezadmin.plugins.parser.MapParser;
import top.ezadmin.plugins.parser.parse.ResultModel;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

public class TaskMessageExecutionListener implements ExecutionListener {

    private static Logger logger = LoggerFactory.getLogger(TaskMessageExecutionListener.class);

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private UserService userService = (UserService) context.getBean("userService");

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
    private BuyorderService buyorderService = context.getBean("buyorderService", BuyorderService.class);

    private SaleorderMapper saleorderMapper = context.getBean("saleorderMapper", SaleorderMapper.class);
    private BuyorderMapper buyorderMapper = context.getBean("buyorderMapper", BuyorderMapper.class);
    private AfterSalesMapper afterSalesMapper = context.getBean("afterSalesMapper", AfterSalesMapper.class);

    private BuyorderExpenseApiService buyorderExpenseApiService = (BuyorderExpenseApiService) context.getBean("buyorderExpenseServiceImpl");

    private BuyOrderModifyApplyService buyOrderModifyApplyService = context.getBean("buyOrderModifyApplyServiceImpl", BuyOrderModifyApplyService.class);

    private WmsSampleOutService wmsSampleOutService = context.getBean("wmsSampleOutServiceImpl", WmsSampleOutService.class);
    
    private PayApplyApiService payApplyApiService = context.getBean("payApplyApiServiceImpl", PayApplyApiService.class);
    //企微发送消息
    private UacWxUserInfoApiService uacWxUserInfoApiService = context.getBean("uacWxUserInfoApiService", UacWxUserInfoApiService.class);

    private String erp_url;
    // 根据穿参通用回写主表中状态
    public void notify(DelegateExecution execution) throws Exception {
        User user = null;
        Environment env = context.getEnvironment();
        String erpUrl= env.getProperty("erp_url");
        String lxcrmUrlPublicSendMsg= env.getProperty("lxcrmUrlPublic")+"crm/wx/transfer?targetUrl=";

        if(RequestContextHolder.getRequestAttributes() == null){
            user = userService.getByUsername("njadmin",1);
        }else{
            ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = ra.getRequest();
            user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            if(user == null){
                user = userService.getByUsername("njadmin",1);
            }
        }

        // 申请人名称
        String preAssignee = execution.getVariable("currentAssinee").toString();
        User assigneeInfo = userService.getByUsernameEnable(preAssignee, user.getCompanyId());
        HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
        String processInstanceId = execution.getProcessInstanceId();
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        String processDefinitionKey = historicProcessInstance.getProcessDefinitionKey();
        // 把list转为Map
        Map<String, Object> variables = execution.getVariables();

        List<Integer> varifyUserList = new ArrayList<>();

        // 准备发送参数
        // 模板ID
        Integer messageTemplateId = null;

        Map<String, String> map = new HashMap<>();
        String url = null;
        if (processDefinitionKey.equals("traderCustomerVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                // 消息模板编号N002
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 2;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                // 消息模板编号N001
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 60;
            }
            if (null != variables.get("traderCustomerVo")) {
                TraderCustomerVo traderCustomerVo = (TraderCustomerVo) variables.get("traderCustomerVo");
                map.put("traderName", traderCustomerVo.getTrader().getTraderName());
                url = "./trader/customer/baseinfo.do?traderId=" + traderCustomerVo.getTraderId();
            }
        } else if (processDefinitionKey.equals("traderSupplierVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                // 消息模板编号N002
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 63;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                // 消息模板编号N001
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 62;
            }
            if (null != variables.get("traderSupplierVo")) {
                TraderSupplierVo traderSupplierVo = (TraderSupplierVo) variables.get("traderSupplierVo");
                map.put("traderName", traderSupplierVo.getTrader().getTraderName());
                url = "./trader/supplier/baseinfo.do?traderId=" + traderSupplierVo.getTrader().getTraderId();
            }
        } else if (processDefinitionKey.equals("quoteVerify")) {
            Integer type = null;
            if (execution.getCurrentActivityName().equals("驳回")) {
                // 消息模板编号N005
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 5;
                type = 2;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                // 消息模板编号N004
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 4;
                type = 3;
            }
            if (null != variables.get("quoteorder")) {
                Quoteorder quoteorder = (Quoteorder) variables.get("quoteorder");
                map.put("quoteorderNo", quoteorder.getQuoteorderNo());
                url = "./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorder.getQuoteorderId() + "&viewType="
                        + type;
            }
        } else if (processDefinitionKey.equals("saleorderVerify")
                || processDefinitionKey.equals("hc_order_auto_verify") || processDefinitionKey.equals("bd_order_auto_verify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                // 消息模板编号N008
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 8;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                // 消息模板编号N007
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 7;
            }
            if (null != variables.get("saleorderInfo")) {
                Saleorder saleorder = (Saleorder) variables.get("saleorderInfo");
                map.put("saleorderNo", saleorder.getSaleorderNo());
                 if (OrderConstant.ORDER_TYPE_JCO.equals(saleorder.getOrderType()) || OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType())) {
                    url = "./order/jc/view.do?saleorderId=" + saleorder.getSaleorderId();
                } else {
                    url = "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
                }
                // 2019-02-25 耗材订单跳转不同duke
                if(saleorder.getOrderType() != null && "5".equals(saleorder.getOrderType().toString())) {
                	url = "./order/hc/hcOrderDetailsPage.do?saleorderId=" + saleorder.getSaleorderId();
                }
            }
        } else if (processDefinitionKey.equals("goodsVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N013
                messageTemplateId = 13;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N056
                messageTemplateId = 56;
            }
            if (null != variables.get("goods")) {
                Goods goods = (Goods) variables.get("goods");
                map.put("sku", goods.getSku());
                url = "./goods/goods/viewbaseinfo.do?goodsId=" + goods.getGoodsId();
            }
        } else if (processDefinitionKey.equals("customerPeriodVerify")) {
            if (null != variables.get("traderType")) {
                TraderAccountPeriodApply traderAccountPeriodApply =
                        (TraderAccountPeriodApply) variables.get("traderAccountPeriodApply");
                map.put("traderName", traderAccountPeriodApply.getTraderName());
                if (variables.get("traderType").equals(1)) {
                    // 经销商（客户）
                    if (execution.getCurrentActivityName().equals("驳回")) {
                        varifyUserList.add(assigneeInfo.getUserId());
                        // 消息模板编号N023
                        messageTemplateId = 23;
                        url = "./trader/customer/baseinfo.do?traderId=" + traderAccountPeriodApply.getTraderId();

                    } else if (execution.getCurrentActivityName().equals("审核完成")) {
                        varifyUserList.add(assigneeInfo.getUserId());
                        // 消息模板编号N022
                        messageTemplateId = 22;
                        url = "./trader/customer/baseinfo.do?traderId=" + traderAccountPeriodApply.getTraderId();
                    }
                } else {
                    // 供应商
                    if (execution.getCurrentActivityName().equals("驳回")) {
                        varifyUserList.add(assigneeInfo.getUserId());
                        // 消息模板编号N025
                        messageTemplateId = 25;
                        url = "./trader/supplier/baseinfo.do?traderId=" + traderAccountPeriodApply.getTraderId();
                    } else if (execution.getCurrentActivityName().equals("审核完成")) {
                        varifyUserList.add(assigneeInfo.getUserId());
                        // 消息模板编号N024
                        messageTemplateId = 24;
                        url = "./trader/supplier/baseinfo.do?traderId=" + traderAccountPeriodApply.getTraderId();
                    }

                }

            }

        } else if (ProcessConstants.APPLY_ACCOUNT_PERIOD_PROC_KEY.equals(processDefinitionKey)) {
            map.put("traderName", MapUtils.getString(variables, ProcessInstanceContext.ATTR_NAME_CUSTOMER_NAME));
            if (execution.getCurrentActivityName().contains("完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N022
                messageTemplateId = 22;
                url = "./trader/customer/baseinfo.do?traderId=" + MapUtils.getString(variables,ProcessInstanceContext.ATTR_NAME_TRADER_ID);
            } else if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N023
                messageTemplateId = 23;
                url = "./trader/customer/baseinfo.do?traderId=" + MapUtils.getString(variables, ProcessInstanceContext.ATTR_NAME_TRADER_ID);
            }
        }else if (processDefinitionKey.equals("afterSalesVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N029
                messageTemplateId = 29;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N028
                messageTemplateId = 28;
            }
            List<User> userList = userService.getUserByUserIds(varifyUserList);
            Integer type = 0;
            for (User u : userList) {
                for (Position p : u.getPositions()) {
                    type = p.getType();
                }
            }
            if (null != variables.get("afterSalesInfo")) {
                AfterSalesVo afterSalesVo = (AfterSalesVo) variables.get("afterSalesInfo");
                map.put("afterSalesNo", afterSalesVo.getAfterSalesNo());
                // 发送人是销售
                if (type == 310) {
                    url = "./order/saleorder/viewAfterSalesDetail.do?afterSalesId=" + afterSalesVo.getAfterSalesId();
                    // 发送人是采购
                } else if (type == 311) {
                    if(ErpConst.ONE.equals(afterSalesVo.getIsNew())){
                        url = "./order/newBuyorder/viewAfterSalesDetail.do?afterSalesId="
                                + afterSalesVo.getAfterSalesId() + "&traderType=2";
                    }else {
                        url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + afterSalesVo.getAfterSalesId()
                                + "&traderType=2";
                    }
                } else {
                    // 类型是销售
                    if (afterSalesVo.getSubjectType() == 535) {
                        url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId="
                                + afterSalesVo.getAfterSalesId() + "&traderType=1";
                        // 类型是采购
                    } else if (afterSalesVo.getSubjectType() == 536) {
                        if(ErpConst.ONE.equals(afterSalesVo.getIsNew())){
                            url = "./order/newBuyorder/viewAfterSalesDetail.do?afterSalesId="
                                    + afterSalesVo.getAfterSalesId() + "&traderType=2";
                        }else {
                            url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId="
                                    + afterSalesVo.getAfterSalesId() + "&traderType=2";
                        }
                    } else {
                        url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId="
                                + afterSalesVo.getAfterSalesId();
                    }
                }
            }
        } else if (processDefinitionKey.equals("expenseAfterSalesVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N029
                messageTemplateId = 29;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N028
                messageTemplateId = 28;
            }
            if (null != variables.get("expenseAfterSales")) {
                ExpenseAfterSalesDto afterSalesVo = (ExpenseAfterSalesDto) variables.get("expenseAfterSales");
                map.put("afterSalesNo", afterSalesVo.getExpenseAfterSalesNo());
                url = "/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=" + afterSalesVo.getExpenseAfterSalesId();

            }
        } else if ("wmsUnitConversionVerify".equals(processDefinitionKey)) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 252;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                messageTemplateId = 251;
            }
            map.put("wmsUnitConversionOrderNo", variables.get("conversionOrderNo").toString());
            url = "/wmsUnitConversion/detail.do?wmsUnitConversionOrderId=" + variables.get("conversionOrderId").toString();
        } else if (processDefinitionKey.equals("bhSaleorderVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N037
                messageTemplateId = 37;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N036
                messageTemplateId = 36;
            }
            if (null != variables.get("saleorderInfo")) {
                Saleorder saleorder = (Saleorder) variables.get("saleorderInfo");
                map.put("saleorderNo", saleorder.getSaleorderNo());
                url = "./order/saleorder/viewBhSaleorder.do?saleorderId=" + saleorder.getSaleorderId();
            }
        } else if (processDefinitionKey.equals("closeQuoteorderVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N047
                messageTemplateId = 47;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N046
                messageTemplateId = 46;
            }
            if (null != variables.get("quoteorder")) {
                Quoteorder quoteorder = (Quoteorder) variables.get("quoteorder");
                map.put("quoteorderNo", quoteorder.getQuoteorderNo());
                url = "./order/quote/getQuoteDetail.do?quoteorderId=" + quoteorder.getQuoteorderId() + "&viewType=2";
            }
        } else if (processDefinitionKey.equals("closeBussinesschanceVerify")) {
            if (execution.getCurrentActivityName().equals("驳回通知销售")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N050
                messageTemplateId = 50;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N049
                messageTemplateId = 49;
            } else if (execution.getCurrentActivityName().equals("驳回通知主管")) {
                User parent = userService.getUserParentInfo(assigneeInfo.getUsername(), user.getCompanyId());
                varifyUserList.add(parent.getpUserId());
                // 消息模板编号N050
                messageTemplateId = 50;
            }
            if (null != variables.get("bussinessChance")) {
                BussinessChance bussinessChance = (BussinessChance) variables.get("bussinessChance");
                map.put("bussinessChanceNo", bussinessChance.getBussinessChanceNo());
//                url = "./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId="
//                        + bussinessChance.getBussinessChanceId() + "&traderId=" + bussinessChance.getTraderId();
                url = "/businessChance/details.do?id=" + bussinessChance.getBussinessChanceId();
            }
        } else if (processDefinitionKey.equals("buyorderVerify") || processDefinitionKey.equals("buyorderVerify_HC")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N016
                messageTemplateId = 16;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N015
                messageTemplateId = 15;
            }
            if (null != variables.get("buyorderInfo")) {
                BuyorderVo buyorderInfo = (BuyorderVo) variables.get("buyorderInfo");
                map.put("buyorderNo", buyorderInfo.getBuyorderNo());
                if (execution.getCurrentActivityName().equals("驳回")) {

                    url = "./order/buyorder/viewBuyorder.do?buyorderId=" + buyorderInfo.getBuyorderId();

                } else if (execution.getCurrentActivityName().equals("审核完成")) {

                    url = "./order/buyorder/viewBuyordersh.do?buyorderId=" + buyorderInfo.getBuyorderId();
                }
            }
        } else if (ProcessConstants.BUYORDER_EXPENSE_KEY.equals(processDefinitionKey)) {

            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N238
                messageTemplateId = 238;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N237
                messageTemplateId = 237;
            }
            if (null != variables.get("buyorderExpenseInfo")) {
                BuyorderExpenseDto buyorderExpenseInfo = (BuyorderExpenseDto) variables.get("buyorderExpenseInfo");
                map.put("orderNo", buyorderExpenseInfo.getBuyorderExpenseNo());
                if (execution.getCurrentActivityName().equals("驳回")) {

                    url = "./buyorderExpense/details.do?buyorderExpenseId=" + buyorderExpenseInfo.getBuyorderExpenseId();

                } else if (execution.getCurrentActivityName().equals("审核完成")) {

                    url = "./buyorderExpense/details.do?buyorderExpenseId=" + buyorderExpenseInfo.getBuyorderExpenseId();
                }
            }

        } else if (processDefinitionKey.equals("fileDeliveryVerify") || processDefinitionKey.equals("fileDeliveryNew")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N053
                messageTemplateId = 53;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N052
                messageTemplateId = 52;
            }
            if (null != variables.get("fileDelivery")) {
                FileDelivery fileDelivery = (FileDelivery) variables.get("fileDelivery");
                map.put("fileDeliveryNo", fileDelivery.getFileDeliveryNo());
                url = "./logistics/filedelivery/getFileDeliveryDetail.do?fileDeliveryId="
                        + fileDelivery.getFileDeliveryId();
            }
            if (null != variables.get("fileDeliveryNew")) {
            	FileDeliveryVO fileDeliveryVO = (FileDeliveryVO) variables.get("fileDeliveryNew");
                map.put("fileDeliveryNo", fileDeliveryVO.getFileDeliveryNo());
                url = "./logistics/filedeliveryNew/getFileDeliveryDetail.do?fileDeliveryId="
                        + fileDeliveryVO.getFileDeliveryId();
            }
        } else if (processDefinitionKey.equals("paymentVerify")) {
            boolean flag = false;
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N055
                messageTemplateId = 55;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N070
                messageTemplateId = 70;
                flag = true;
            }
            if (null != variables.get("payApply")) {
                PayApply payApply = (PayApply) variables.get("payApply");
                map.put("buyorderNo", payApply.getOrderNo());
//                String no = payApply.getOrderNo().substring(0, 2);
                AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(payApply.getOrderNo());
                Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(payApply.getOrderNo());
                if (afterSales != null) {
                    if (payApply.getOrderNo().isEmpty()) {
                        url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + payApply.getRelatedId();
                    } else {
                        url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + payApply.getRelatedId()
                                + "&traderType=1";
                    }
                } else if(buyorder != null){
                    if (execution.getCurrentActivityName().equals("财务审核")
                            || execution.getCurrentActivityName().equals("财务制单")) {
                        url = "./finance/buyorder/viewBuyorder.do?buyorderId=" + payApply.getRelatedId();
                    } else {
                        url = "./order/buyorder/viewBuyordersh.do?buyorderId=" + payApply.getRelatedId();
                    }
                }else {
                    url = "./buyorderExpense/details.do?buyorderExpenseId=" + payApply.getRelatedId();
                }
                // 审核结束发送企微消息
                try {
                    payApplyApiService.sendWxMsgAfterAudit(payApply.getPayApplyId(), flag, assigneeInfo.getUserId());
                } catch (Exception e) {
                    logger.info("付款申请审核通过或驳回发送企微消息异常", e);
                }
            }
        } else if (processDefinitionKey.equals("editSaleorderVerify") || processDefinitionKey.equals("saleorderModifyAudit")) {

            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N066
                messageTemplateId = 66;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {

                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N065
                messageTemplateId = 65;
            }
            if (null != variables.get("saleorderModifyApplyInfo")) {
                SaleorderModifyApply saleorderModifyApply =
                        (SaleorderModifyApply) variables.get("saleorderModifyApplyInfo");
                map.put("saleorderNo", saleorderModifyApply.getSaleorderModifyApplyNo());

                if (ErpConst.ONE.equals(variables.get("isNew"))) {
                    url = "./orderstream/saleorder/viewModifyApply.do?saleorderModifyApplyId="
                            + saleorderModifyApply.getSaleorderModifyApplyId() + "&saleorderId="
                            + saleorderModifyApply.getSaleorderId();
                } else {
                    url = "./order/saleorder/viewModifyApply.do?saleorderModifyApplyId="
                            + saleorderModifyApply.getSaleorderModifyApplyId() + "&saleorderId="
                            + saleorderModifyApply.getSaleorderId();
                }

            }
        } else if (processDefinitionKey.equals("earlyBuyorderVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N059
                messageTemplateId = 59;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N058
                messageTemplateId = 58;
            }
            if (null != variables.get("saleorderInfo")) {
                Saleorder saleorder = (Saleorder) variables.get("saleorderInfo");
                map.put("saleorderNo", saleorder.getSaleorderNo());
                url = "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
            }
        } else if (processDefinitionKey.equals("invoiceVerify")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N021
                messageTemplateId = 21;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N011
                messageTemplateId = 11;
            }
            if (null != variables.get("invoiceApply")) {
                InvoiceApply invoiceApply = (InvoiceApply) variables.get("invoiceApply");
                if (invoiceApply.getType().equals(504)) {
                    map.put("saleorderNo", invoiceApply.getAfterSalesNo());
                    url = "./finance/after/getFinanceAfterSaleDetail.do?afterSalesId=" + invoiceApply.getRelatedId()
                            + "&eFlag=cw";
                } else {
                    map.put("saleorderNo", invoiceApply.getSaleorderNo());
                    url = "./finance/invoice/viewSaleorder.do?saleorderId=" + invoiceApply.getRelatedId();
                }
            }
        } else if (processDefinitionKey.equals("editTraderCustomerName")) {
            if (execution.getCurrentActivityName().equals("驳回")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N074
                messageTemplateId = 74;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N073
                messageTemplateId = 73;
            }
            if (null != variables.get("trader")) {
                Trader trader = (Trader) variables.get("trader");
                map.put("traderName", trader.getTraderNameBefore());
                url = "./trader/customer/baseinfo.do?traderId=" + trader.getTraderId();
            }
        } else if (ProcessConstants.MODIFY_BUYORDER_APPLY_PROC_KEY.equals(processDefinitionKey)) {

            boolean audited = ProcessConstants.DEFAULT_AUDITED_ACTIVITY_NAME.equals(execution.getCurrentActivityName());
            boolean rejected = ProcessConstants.DEFAULT_REJECTED_ACTIVITY_NAME.equals(execution.getCurrentActivityName());

            if (rejected) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N078
                messageTemplateId = 78;
            } else if (audited) {
                varifyUserList.add(assigneeInfo.getUserId());
                // 消息模板编号N077
                messageTemplateId = 77;
            }

            boolean finished = audited | rejected;
            //到达终止节点
            if (finished && null != variables.get("buyorderModifyApplyInfo")) {

                BuyorderModifyApplyVo buyorderModifyApplyInfo = (BuyorderModifyApplyVo) variables.get("buyorderModifyApplyInfo");
                map.put("buyorderNo", buyorderModifyApplyInfo.getBuyorderModifyApplyNo());
                url = "./order/buyorder/viewModifyApply.do?buyorderModifyApplyId=" + buyorderModifyApplyInfo.getBuyorderModifyApplyId();

                if (audited) {
                    // VDERP-8849 【需求变更】【功能】【ERP】【采购订单】采购订单修改发货方式审核通过后发送站内信给销售订单归属销售，造成站内信发送重复
                    buyorderService.sendMsgVerifyByCompareNewOldDeliveryDirect(buyorderModifyApplyInfo.getBuyorderModifyApplyId());
                    buyorderService.sendMsgByCompareNewOldSkuPrice(buyorderModifyApplyInfo.getBuyorderModifyApplyId());
                }

                //采购单申请审核完结时处理销售、采购与WMS的交互
                buyOrderModifyApplyService.processBuyOrderModifyApplyFinished(variables, audited, user);
            }
        } else if (processDefinitionKey.equals("overAfterSalesVerify")) {
            if (null != variables.get("afterSalesVo")) {
                AfterSalesVo afterSalesInfo = (AfterSalesVo) variables.get("afterSalesVo");
                if (execution.getCurrentActivityName().equals("驳回")) {
                    if (afterSalesInfo != null && afterSalesInfo.getVerifiesType().equals(0)) {
                        // 关闭
                        messageTemplateId = 82;
                    } else if (afterSalesInfo != null && afterSalesInfo.getVerifiesType().equals(1)) {
                        // 完成
                        messageTemplateId = 85;
                    }
                } else if (execution.getCurrentActivityName().equals("审核完成")) {
                    if (afterSalesInfo != null && afterSalesInfo.getVerifiesType().equals(0)) {
                        // 关闭
                        messageTemplateId = 81;
                    } else if (afterSalesInfo != null && afterSalesInfo.getVerifiesType().equals(1)) {
                        // 完成
                        messageTemplateId = 84;
                    }
                }
                varifyUserList.add(assigneeInfo.getUserId());
                map.put("afterSalesNo", afterSalesInfo.getAfterSalesNo());
                // 类型是销售
                if (afterSalesInfo.getSubjectType() == 535) {
                    url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + afterSalesInfo.getAfterSalesId()
                            + "&traderType=1";
                    // 类型是采购
                } else if (afterSalesInfo.getSubjectType() == 536) {
                    url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + afterSalesInfo.getAfterSalesId()
                            + "&traderType=2";
                } else {
                    url = "./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + afterSalesInfo.getAfterSalesId();
                }

            }

        } else if (processDefinitionKey.equals("contractReturnVerify")) {
            contractReturnVerify(  execution,  erpUrl,  lxcrmUrlPublicSendMsg, preAssignee,  assigneeInfo);
            return;
        } else if (processDefinitionKey.equals("priceChangeApply")) {

            varifyUserList.add(assigneeInfo.getUserId());

            // 消息模板编号106
            String firstVerifyPirce = variables.get("firstVerifyPirce").toString();
            if (execution.getCurrentActivityName().equals("审核不通过")) {
                messageTemplateId = "1".equals(firstVerifyPirce) ? 108 : 147;
            } else if (execution.getCurrentActivityName().equals("审核通过")) {
                messageTemplateId = "1".equals(firstVerifyPirce) ? 107 : 146;
            }

            map.put("skuNo", variables.get("skuNo").toString());
            url = "./price/basePriceMaintain/detail.do?skuPriceChangeApplyId=" + variables.get("skuPriceChangeApplyId").toString();
        } else if (processDefinitionKey.equals("testshouquan")) {
            varifyUserList.add(assigneeInfo.getUserId());
            if (execution.getCurrentActivityName().equals("通过")) {
                messageTemplateId = 131;
            } else if (execution.getCurrentActivityName().equals("驳回")) {
                messageTemplateId = 115;
            }
            map.put("AuthorizationNum", variables.get("authorizationNum").toString());
            url = "./order/quote/authorizationExamine.do?authorizationApplyId=" + variables.get("authorizationApplyId").toString();

        } else if (processDefinitionKey.equals("lendOutAudit")) {

            varifyUserList.add(assigneeInfo.getUserId());

            if (execution.getCurrentActivityName().equals("审核不通过")) {
                messageTemplateId = 122;
            } else if (execution.getCurrentActivityName().equals("审核通过")) {
                messageTemplateId = 121;
            }

            map.put("lendOutOrderNo", variables.get("lendOutOrderNo").toString());
            url = "./wms/commodityLendOut/detail.do?lendOutId=" + variables.get("lendOutOrderId").toString();

        } else if (processDefinitionKey.equals("surplusInOrderAudit")) {

            varifyUserList.add(assigneeInfo.getUserId());

            String evn = ConfigService.getAppConfig().getProperty("surplusin_order_audit", "PROD");

            if ("fat".equals(evn)) {
                // 消息模板编号137
                if (execution.getCurrentActivityName().equals("驳回")) {
                    messageTemplateId = 139;
                } else if (execution.getCurrentActivityName().equals("审核通过")) {
                    messageTemplateId = 138;
                }
            } else {
                // 消息模板编号137
                if (execution.getCurrentActivityName().equals("驳回")) {
                    messageTemplateId = 136;
                } else if (execution.getCurrentActivityName().equals("审核通过")) {
                    messageTemplateId = 135;
                }
            }

            map.put("surplusInOrderNo", variables.get("surplusInOrderNo").toString());
            url = "./wms/surplusIn/applyIndex.do?wmsInputOrderId=" + variables.get("wmsInputOrderId").toString();
        } else if (processDefinitionKey.equals("inventoryOutAudit")) {

            varifyUserList.add(assigneeInfo.getUserId());
            if (execution.getCurrentActivityName().equals("驳回")) {
                messageTemplateId = 233;
            } else if (execution.getCurrentActivityName().equals("审核通过")) {
                messageTemplateId = 234;
            }

            map.put("orderNo", variables.get("orderNo").toString());
            url = "./wms/inventoryOut/details.do?inventoryOutOrderId=" + variables.get("wmsOutPutOrderId").toString();
        } else if (processDefinitionKey.equals("afterSaleStandardVerify")) {

            varifyUserList.add(assigneeInfo.getUserId());

            if (execution.getCurrentActivityName().equals("驳回")) {
                messageTemplateId = 159;
            } else if (execution.getCurrentActivityName().equals("审核通过")) {
                messageTemplateId = 158;
            }

            map.put("skuNo", variables.get("skuNo").toString());
            url = "./aftersale/serviceStandard/detail.do?skuNo=" + variables.get("skuNo").toString();
        } else if (processDefinitionKey.equals("overBuyorderAfterSalesVerify")) {

            varifyUserList.add(assigneeInfo.getUserId());

            if (execution.getCurrentActivityName().equals("驳回")) {
                messageTemplateId = 212;
            } else if (execution.getCurrentActivityName().equals("审核完成")) {
                messageTemplateId = 211;
            }

            map.put("aftersalesNo", variables.get("aftersalesNo").toString());
            url = "./order/newBuyorder/viewAfterSalesDetail.do?traderType=2&afterSalesId=" + variables.get("afterSalesId").toString();
        } else if (processDefinitionKey.equals("receiveOutAudit")){

            varifyUserList.add(assigneeInfo.getUserId());
            if (execution.getCurrentActivityName().equals("驳回")){
                messageTemplateId = 246;
            }else if (execution.getCurrentActivityName().equals("审核完成")){
                messageTemplateId = 247;
            }

            map.put("receiveOutOrderNo",variables.get("receiveOutOrderNo").toString());
            url = "./wms/receiveOut/receiveDetail.do?receiveOutId="+variables.get("receiveOutOrderId").toString();
        } else if (processDefinitionKey.equals("sampleOutAudit")){
            varifyUserList.add(assigneeInfo.getUserId());
            if (execution.getCurrentActivityName().equals("驳回")){
                messageTemplateId = 262;
            }else if (execution.getCurrentActivityName().equals("审核完成")){
                messageTemplateId = 261;
                List<Integer> allMessageUserIdList = wmsSampleOutService.getAllMessageUserIdList(Long.parseLong(variables.get("sampleOutOrderId").toString()));
                if (CollectionUtils.isNotEmpty(allMessageUserIdList)){
                    varifyUserList.addAll(allMessageUserIdList);
                }
                logger.info("样品申请单审核通过信息参数 varifyUserList:{}", JSON.toJSONString(varifyUserList));
            }
            map.put("sampleOrderNo",variables.get("sampleOutOrderNo").toString());
            url = "./wms/sampleOut/detail.do?sampleOrderId="+variables.get("sampleOutOrderId").toString();
        }
        else if ((processDefinitionKey.equals("confirmationOrderVerify"))){
            confirmationOrderVerify(  execution,  erpUrl,  lxcrmUrlPublicSendMsg, preAssignee,  assigneeInfo);
            return;
        }
        MessageUtil.sendMessage(messageTemplateId, varifyUserList, map, url, preAssignee);//

    }

    private void sendWx(String body,String toUser,String title,String url){
        try {
            if (StringUtils.isBlank(toUser)) {
                logger.error("消息推送没有找到接收者工号{}", title);
                return;
            }
            WxCpMessage wxCpMessage = new WxCpMessage();
            wxCpMessage.setToUser(toUser);
            wxCpMessage.setMsgType("textcard");
            wxCpMessage.setTitle(title);
            wxCpMessage.setDescription(body);
            wxCpMessage.setUrl(url);
            wxCpMessage.setBtnTxt("详情");
            uacWxUserInfoApiService.sendToUser(wxCpMessage);
        }catch (Exception e){
            logger.error("企微消息推送失败{}", title,e);
        }
    }

    /**
     * 确认单审核重构
     * @param execution
     * @param erpUrl
     * @param lxcrmUrlPublicSendMsg
     * @param preAssignee
     * @param assigneeInfo
     * @throws UnsupportedEncodingException
     */
    private void confirmationOrderVerify(DelegateExecution execution,String erpUrl,String lxcrmUrlPublicSendMsg,
                                      String preAssignee,User assigneeInfo ) throws UnsupportedEncodingException {
        Integer messageTemplateId=6012;
        List<Integer> varifyUserList = new ArrayList<>();//审核人
        Map<String, String> map = new HashMap<>();//参数
        Map<String, Object> variables = execution.getVariables();
        //驳回原因
        String rejectReason= Objects.isNull(variables.get("rejectReason"))?"":variables.get("rejectReason")+"";
        String url="";
        if (execution.getCurrentActivityName().equals("驳回")) {
            logger.info("审核流中获取到 驳回原因：{}",rejectReason);
            // 对T_SALEORDER_DATA（CONTRACT_VERIFY_STATUS）做实时更新
            if (Objects.nonNull(variables.get("saleorderNo"))) {
                map.put("saleOrderNo", ""+variables.get("saleorderNo"));
                map.put("traderName", ""+variables.get("traderName"));
                map.put("rejectReason", rejectReason);
                url = "./order/saleorder/view.do?saleorderId=" +variables.get("saleorderId");
            }
            varifyUserList.add(assigneeInfo.getUserId());


            String format = StrUtil.format(ErpConstant.QUERENDAN_REJECTED_MSG_TEMPLATE,
                    variables.get("traderName"), variables.get("saleorderNo"),rejectReason);
            String url2=erpUrl+"/index.do?target="+ URLEncoder.encode("/order/saleorder/view.do?saleorderId=" +variables.get("saleorderId"),"UTF-8");
            sendWx(format,assigneeInfo.getNumber(),"确认单审批提醒",lxcrmUrlPublicSendMsg+URLEncoder.encode(url2,"UTF-8"));
        }
        logger.info("TaskMessageExecutionListener,确认单审核流 messageTemplateId:{}," +
                "varifyUserList:{},map:{},url:{}",messageTemplateId,varifyUserList,map,url);
        MessageUtil.sendMessage(messageTemplateId, varifyUserList, map, url, preAssignee);//
    }
    /**
     * 合同回传审核重构
     * @param execution
     * @param erpUrl
     * @param lxcrmUrlPublicSendMsg
     * @param preAssignee
     * @param assigneeInfo
     * @throws UnsupportedEncodingException
     */
    private void contractReturnVerify(DelegateExecution execution,String erpUrl,String lxcrmUrlPublicSendMsg,
                                      String preAssignee,User assigneeInfo ) throws UnsupportedEncodingException {
        Integer messageTemplateId=6013;//模版
        List<Integer> varifyUserList = new ArrayList<>();//审核人
        Map<String, String> map = new HashMap<>();//参数
        String url="";
        Map<String, Object> variables = execution.getVariables();
        //驳回原因
        String rejectReason= Objects.isNull(variables.get("rejectReason"))?"":variables.get("rejectReason")+"";
        Saleorder saleorder = (Saleorder) variables.get("saleorderInfo");

        if (execution.getCurrentActivityName().equals("驳回")) {
            logger.info("审核流中获取到 驳回原因：{} 订单ID{}",rejectReason,variables.get("saleorderId"));
            // 对T_SALEORDER_DATA（CONTRACT_VERIFY_STATUS）做实时更新
            if (Objects.nonNull(variables.get("saleorderInfo"))) {
                saleorderMapper.updateContractVerifyStatus(saleorder.getSaleorderId(), OrderConstant.CONTRACT_VERIFY_REJECT);
                //发送企微消息
                String format = StrUtil.format(ErpConstant.HETONGHUICHUAN_REJECTED_MSG_TEMPLATE,
                        saleorder.getTraderName(),saleorder.getSaleorderNo(),rejectReason);
                String url2=erpUrl+"/index.do?target="+ URLEncoder.encode("/order/saleorder/view.do?saleorderId=" +saleorder.getSaleorderId(),"UTF-8");
                sendWx(format,assigneeInfo.getNumber()+"","合同审批提醒",lxcrmUrlPublicSendMsg+URLEncoder.encode(url2,"UTF-8"));
            }
            varifyUserList.add(assigneeInfo.getUserId());
            // 消息模板编号N088
            messageTemplateId = 6013;

        }
        if (null != variables.get("saleorderInfo")) {
            map.put("saleOrderNo", saleorder.getSaleorderNo());
            map.put("saleorderId", saleorder.getSaleorderId()+"");
            map.put("traderName", saleorder.getTraderName());
            map.put("rejectReason",rejectReason);
            url = "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId();
        }
        MessageUtil.sendMessage(messageTemplateId, varifyUserList, map, url, preAssignee);//
    }



}
