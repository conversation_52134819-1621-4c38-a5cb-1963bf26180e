function batchApplyApi(param) {
    return axios({
        url: '/redConfirm/api/apply.do',
        method: 'post',
        data: param
    })
}
function batchAgreeApi(param) {
    return axios({
        url: '/redConfirm/api/audit.do',
        method: 'post',
        data: param
    })
}
function batchInvoiceApi(param) {
    return axios({
        url: '/redConfirm/api/open.do',
        method: 'post',
        data: param
    })
}
function batchCancelApi(param) {
    return axios({
        url: '/redConfirm/api/cancel.do',
        method: 'post',
        data: param
    })
}function batchInvalidApi(param) {
    return axios({
        url: '/redConfirm/api/invalid.do',
        method: 'post',
        data: param
    })
}

function getTypeNum() {
    return axios({
        url: '/redConfirm/api/typeNum.do',
        method: 'get'
    })
}

function globalUpdateApi() {

    return axios({
        url: '/redConfirm/api/globalUpdate.do',
        method: 'get'
    })
}