<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.phoneticWriting.dao.ModificationRecordMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.phoneticWriting.model.ModificationRecord" >
    <!--          -->
    <id column="MODIFICATION_RECORD_ID" property="modificationRecordId" jdbcType="INTEGER" />
    <result column="CONTROVERSIAL_CONTENT" property="controversialContent" jdbcType="VARCHAR" />
    <result column="MODIFY_CONTENT" property="modifyContent" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="INTEGER" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="IS_DEL" property="isDel" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    MODIFICATION_RECORD_ID,  CONTROVERSIAL_CONTENT, MODIFY_CONTENT, STATE, RELATED_ID,TYPE,IS_DEL,
    ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_MODIFICATION_RECORD
    where MODIFICATION_RECORD_ID = #{modificationRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_MODIFICATION_RECORD
    where MODIFICATION_RECORD_ID = #{modificationRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.phoneticWriting.model.ModificationRecord" >
    <!--          -->
    insert into T_MODIFICATION_RECORD (MODIFICATION_RECORD_ID,  CONTROVERSIAL_CONTENT,
      MODIFY_CONTENT, TYPE,STATE, RELATED_ID,IS_DEL,
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{modificationRecordId,jdbcType=INTEGER},  #{controversialContent,jdbcType=VARCHAR},
      #{modifyContent,jdbcType=VARCHAR},#{type,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER},#{isDel,jdbcType=INTEGER}
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.phoneticWriting.model.ModificationRecord" >
    <!--          -->
    insert into T_MODIFICATION_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="modificationRecordId != null" >
        MODIFICATION_RECORD_ID,
      </if>
      <if test="controversialContent != null" >
        CONTROVERSIAL_CONTENT,
      </if>
      <if test="modifyContent != null" >
        MODIFY_CONTENT,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="isDel != null" >
        IS_DEL,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="modificationRecordId != null" >
        #{modificationRecordId,jdbcType=INTEGER},
      </if>
      <if test="controversialContent != null" >
        #{controversialContent,jdbcType=VARCHAR},
      </if>
      <if test="modifyContent != null" >
        #{modifyContent,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="isDel != null" >
        #{isDel,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.phoneticWriting.model.ModificationRecord" >
    <!--          -->
    update T_MODIFICATION_RECORD
    <set >
      <if test="controversialContent != null" >
        CONTROVERSIAL_CONTENT = #{controversialContent,jdbcType=VARCHAR},
      </if>
      <if test="modifyContent != null" >
        MODIFY_CONTENT = #{modifyContent,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        TYPE = #{type,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="isDel != null" >
        IS_DEL = #{isDel,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where MODIFICATION_RECORD_ID = #{modificationRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.phoneticWriting.model.ModificationRecord" >
    <!--          -->
    update T_MODIFICATION_RECORD
    set
      CONTROVERSIAL_CONTENT = #{controversialContent,jdbcType=VARCHAR},
      MODIFY_CONTENT = #{modifyContent,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      TYPE = #{type,jdbcType=INTEGER},
      IS_DEL = #{isDel,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where MODIFICATION_RECORD_ID = #{modificationRecordId,jdbcType=INTEGER}
  </update>
    <!--&lt;!&ndash;根据沟通记录id查询录音文本信息&ndash;&gt;
    <select id="getMrList" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from T_MODIFICATION_RECORD
        where RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>-->

    <select id="getModificationRecordListPage" parameterType="java.util.Map" resultType="com.vedeng.phoneticWriting.model.vo.ModificationRecordVo">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            T_MODIFICATION_RECORD
        WHERE
            IS_DEL = 0
        AND
            TYPE = 1

            <if test="map.mrVo.addTimeStartStr != null and map.mrVo.addTimeStartStr != ''">
              AND  ADD_TIME >=  #{map.mrVo.addTimeStart,jdbcType=BIGINT}
            </if>
            <if test="map.mrVo.addTimeEndStr != null and map.mrVo.addTimeEndStr != ''">
              AND  ADD_TIME <![CDATA[ <= ]]>  #{map.mrVo.addTimeEnd,jdbcType=BIGINT}
            </if>
            <if test="map.mrVo.modTimeStartStr != null and map.mrVo.modTimeStartStr != ''">
              AND  MOD_TIME >=  #{map.mrVo.modTimeStart,jdbcType=BIGINT}
            </if>
            <if test="map.mrVo.modTimeEndStr != null and map.mrVo.modTimeEndStr != ''">
              AND  MOD_TIME <![CDATA[ <= ]]>  #{map.mrVo.modTimeEnd,jdbcType=BIGINT}
            </if>
            <if test="map.mrVo.creator != null and map.mrVo.creator != -1">
              AND
              CREATOR = #{map.mrVo.creator,jdbcType=INTEGER}
            </if>
            <if test="map.mrVo.updater != null and map.mrVo.updater != -1">
              AND
              UPDATER = #{map.mrVo.updater,jdbcType=INTEGER}
            </if>

            <if test="map.mrVo.controversialContent != null and map.mrVo.controversialContent != ''">
              AND
              CONTROVERSIAL_CONTENT LIKE CONCAT('%',#{map.mrVo.controversialContent,jdbcType=VARCHAR},'%')
            </if>

            <if test="map.mrVo.modifyContent != null and map.mrVo.modifyContent != ''">
              AND
              MODIFY_CONTENT LIKE CONCAT('%',#{map.mrVo.modifyContent,jdbcType=VARCHAR},'%')
            </if>
        ORDER BY ADD_TIME DESC
    </select>
    <!--查询所有的争议规则-->
    <select id="getMrInfoList" resultMap="BaseResultMap" parameterType="com.vedeng.trader.model.CommunicateRecord" >
      SELECT
      <include refid="Base_Column_List" />
      FROM
      T_MODIFICATION_RECORD
      WHERE IS_DEL = 0 AND ( RELATED_ID IN (#{communicateRecordId,jdbcType=INTEGER}, 0) or TYPE = 1)
      ORDER BY MODIFICATION_RECORD_ID ASC
   </select>

  <select id="getTheSameRecord" parameterType="java.lang.String" resultType="java.lang.Integer">
      SELECT
          COUNT( MODIFICATION_RECORD_ID )
      FROM
          T_MODIFICATION_RECORD
      WHERE
          CONTROVERSIAL_CONTENT = #{controversialContent,jdbcType=VARCHAR}
      and IS_DEL = 0
  </select>
  <!--查询当前录音下存不存在相同的争议内容-->
  <select id="getTheSameRecordById" parameterType="com.vedeng.phoneticWriting.model.ModificationRecord" resultType="java.lang.Integer">
      SELECT
          COUNT( MODIFICATION_RECORD_ID )
      FROM
          T_MODIFICATION_RECORD
      WHERE
          CONTROVERSIAL_CONTENT = #{controversialContent,jdbcType=VARCHAR}
      and IS_DEL = 0
      and RELATED_ID = #{relatedId,jdbcType=INTEGER}
  </select>

  <select id="getTheSameRecordByNotId" parameterType="com.vedeng.phoneticWriting.model.ModificationRecord" resultType="java.lang.Integer">
      SELECT
          COUNT( MODIFICATION_RECORD_ID )
      FROM
          T_MODIFICATION_RECORD
      WHERE
          CONTROVERSIAL_CONTENT = #{controversialContent,jdbcType=VARCHAR}
      and IS_DEL = 0
      and RELATED_ID not in  (#{relatedId,jdbcType=INTEGER})
  </select>

  <update id="delRecord" parameterType="java.lang.Integer">
     update
      T_MODIFICATION_RECORD
     set
      IS_DEL = 1
     where
       MODIFICATION_RECORD_ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>