package com.vedeng.erp.kingdee.batch.common.enums;

/**
 *业务类型标识
 * <AUTHOR>
 * @date $
 */
public class StockOperateTypeConst {
    /**
     *  0关闭订单
     */
    public final static Integer COLES_ORDER = 0;
    /**
     *  100 保存订单
     */
    public final static Integer START_ORDER = 100;
    /**
     * 101 订单选择逻辑仓
     **/
    public final static Integer CORDER_HOOSE_LOGICAL = 101;
    /**
     *  539 销售售后退货
     */
    public final static Integer AFTERORDER_BACK_FINSH = 539;
    /**
     *  540 销售售后换货
     */
    public final static Integer AFTERORDER_CHANGE_FINSH = 540;
    /**
    *  1采购入库
    */
    public final static Integer WAREHOUSE_IN = 1;
    /**
     *  2销售出库
     */
    public final static Integer WAREHOUSE_OUT = 2;
    /**
     *  3销售换货入库
     */
    public final static Integer ORDER_WAREHOUSE_CHANGE_IN = 3;
    /**
     * 4销售换货出库
     *
     */
    public final static Integer ORDER_WAREHOUSE_CHANGE_OUT = 4;
    /**
     *  5销售退货入库
     */
    public final static Integer ORDER_WAREHOUSE_BACK_IN = 5;
    /**
     *  6采购退货出库
     */
    public final static Integer BUYORDER_WAREHOUSE_BACK_OUT = 6;
    /**
     *  7采购换货出库
     */
    public final static Integer BUYORDER_WAREHOUSE_CHANGE_OUT = 7;
    /**
     *  8采购换货入库
     */
    public final static Integer BUYORDER_WAREHOUSE_CHANGE_IN = 8;
    /**
     *  9外借入库
     */
    public final static Integer LENDOUT_WAREHOUSE_IN = 9;
    /**
     *  10外借出库
     */
    public final static Integer LENDOUT_WAREHOUSE_OUT = 10;
    /**
     * 转移入库
     */
    public final static Integer INVENTORY_WAREHOUSE_IN = 11;
    /**
     * 盘盈入库
     */
    public final static Integer SURPLUS_WAREHOUSE_IN = 12;
    /**
     * 报废出库
     */
    public final static Integer SCRAPED_WAREHOUSE_OUT = 13;
    /**
     * 领用出库
     */
    public final static Integer SCRAPED_WAREHOUSE_OUT_L = 14;

    /**
     *
     * 调整盘亏出库
     */
    public final static Integer SURPLUS_WAREHOUSE_OUT = 15;

    /**
     * 盘亏出库
     */
    public final static Integer INVENTORY_WAREHOUSE_OUT = 16;

    /**
     * 样品出库
     */
    public final static Integer SAMPLE_ORDER_OUT = 18;

    /**
     * 库存转换单出库 19 不用17的原因是对接金蝶的采购赠品入库在是用常量的时候新表中使用了17 避免通用方法里复用17（也算是个屎坑）
     */
    public final static Integer UNIT_CONVERSION_OUT = 19;

    /**
     * 库存转换单入库 20
     */
    public final static Integer UNIT_CONVERSION_IN = 20;

    /**
     * 546 采购退货
     */
    public final static Integer AFTERBUYORDER_BACK_FINSH = 546;
    /**
     * 547 采购换货
     */
    public final static Integer AFTERBUYORDER_CHANGE_FINSH = 547;

    /**
     * 100 活动库存转移
     */
    public final static Integer INVENTORY_TRANSFER_ACTION = 100;
}
