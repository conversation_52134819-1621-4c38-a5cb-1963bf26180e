<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app">
    <%--图片上传--%>
    <el-row :gutter="20" class="baseModel">
        <el-col :span="4">
            <div class="grid-content bg-purple">
                合同回传
            </div>
        </el-col>
        <el-col :span="20">
            <div class="grid-content bg-purple-light" style="height:180px;overflow: auto">
                <div class="box">
                    <el-upload
                            action="/orderstream/saleorder/uploadImg.do"
                            :file-list="fileList"
                            limit="10"
                            multiple
                            :on-exceed="outOfLimit"
                            :before-upload="beforeUpload"
                            :on-change="onChange"
                            :on-success="onSuccess"
                            :on-remove="onChange"
                            accept=".jpg,.jpeg,.png,.pdf,.PDF">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-upload>
                </div>
                <div slot="tip" class="box" style="color: #909399">
                    友情提示：<br/>
                    1、上传文件格式可以是jpg、png、pdf等格式;<br/>
                    2、上传文件不要超过10MB;<br/>
                    3、最多上传10个文件;
                </div>
            </div>
        </el-col>
    </el-row>
    <el-divider></el-divider>


    <%--按钮--%>
    <span style="display: block;text-align: center;" class="baseModel">
            <el-button type="primary" @click="submit" :disabled="disSubmit">提交审核</el-button>
            <el-button @click="reInit">关闭</el-button>
        </span>
</div>

<script type="text/javascript">

    let buyorderId = ${buyorderId};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                isIndeterminate: false,
                checkAll: false,
                checkedBatch: [],
                fileList: [],
                rules: {
                },
                // popupIndex: 1,
                disSubmit: false,
            }
        },
        created() {
            // sendThis(this);
        },
        methods: {
            onChange(file,fileList) {
                console.log(fileList)
                this.fileList = fileList;

            },
            onSuccess(res, file, fileList) {
                console.log(fileList)
                this.fileList = fileList;
            },
            beforeUpload(file) {
                //类型限制
                let fileType = false;
                let arr = [];
                arr.push('application/pdf')
                arr.push('image/jpeg')
                arr.push('image/png')
                if (arr.includes(file.type)){
                    fileType = true;
                }
                //大小限制
                const isLt10M = file.size/1024/1024 < 10
                if (!isLt10M) {
                    this.$message.error('上传文件不要超过10MB');
                }
                if (!fileType){
                    this.$message.error('无法查看其他文件格式的文档');
                }
                return isLt10M && fileType;
            },
            outOfLimit() {
                this.$message({
                    message: '最多上传10个文件',
                    type: 'error'
                });
            },

            /*提交*/
            submit() {
                if (this.fileList.length === 0){
                    this.$message.error("请上传附件")
                    return;
                }
                //大小限制
                let totalSize = 0;
                this.fileList.forEach(file =>{
                    totalSize += file.size/1024/1024
                });
                if (totalSize > 10){
                    this.$message.error('上传文件不要超过10MB');
                    totalSize = 0;
                    return;
                }
                var checkUpload = false;
                this.fileList.forEach((file=>{
                    console.log("status:"+file.status)
                    if(file.status != "success"){

                        checkUpload = true;
                    }
                }));
                if(checkUpload){
                    console.log("cc"+checkUpload)
                    this.$message.error("文件上传中，请等待文件上传完成！");
                    return;
                }
                this.disSubmit = true;
                let data = JSON.parse(JSON.stringify({
                    buyorderId: buyorderId,
                    attachmentList: [],
                    altType: ${altType}
                }));
                this.fileList.forEach(file => {
                    data.attachmentList.push(file.response)
                })
                //发送请求
                axios({
                    url: '/order/buyorder/contractReturnSave.do',
                    method: 'post',
                    data: data
                }).then(res => {
                    this.disSubmit = false;
                    if (res.data.code === 0){
                        this.reInit();
                        parent.window.location.reload();
                    }else {
                        this.$message.error(res.data.message)
                    }
                });

            },
            /*取消*/
            reInit() {
                // this.popupIndex = -1;
                layer.closeAll();
                parent.layer.closeAll();
            },
        }
    });
</script>

<style>
    .baseModel {
        padding-top: 20px;
        padding-left: 10px;
        font-size: 13px;
    }

    .box {
        width: 200px;
        height: 180px;
        float: left;
        padding-right: 50px;
    }


</style>
