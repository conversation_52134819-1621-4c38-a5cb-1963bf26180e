package com.vedeng.common.constant;

import com.vedeng.common.util.StringUtil;

/**
 * @Description:
 * @Author:       davis
 * @Date:         2021/4/16 下午4:43
 * @Version:      1.0
 */
public enum RemarkComponentTypeEnum {
    BUY_ORDER(1 , "采购要求"),
    SUPPLIER(2, "供应商要求"),
    DELIVERY_REQUIRE(3, "发货要求"),
    DELIVERY_TYPE(4, "发货方式"),
    SPECIAL_DELIVERY_REQUIRE(5,"专向发货");

    RemarkComponentTypeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    private Integer type;

    private String msg;

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param type
     * @return
     */
    public static String codeToMessage(Integer type){
        if(type == null){
            return "";
        }
        for(RemarkComponentTypeEnum remarkComponentType : RemarkComponentTypeEnum.values()){
            if(type.equals(remarkComponentType.getType())){
                return remarkComponentType.getMsg();
            }
        }
        return "";
    }

}
