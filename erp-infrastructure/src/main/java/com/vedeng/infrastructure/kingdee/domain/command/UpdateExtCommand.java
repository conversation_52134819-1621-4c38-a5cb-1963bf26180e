package com.vedeng.infrastructure.kingdee.domain.command;


import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶扩展Update参数对象
 * @date 2022/8/25 18:55
 */
public class UpdateExtCommand<T> extends BaseCommand {


    private int Creator;

    private ArrayList<String> NeedReturnFields = new ArrayList<>();

    private ArrayList<String> NeedUpDateFields = new ArrayList<>();


    /**
     * 是否自动调整字段顺序
     */
    private Boolean isAutoAdjustField = true;

    /**
     * 是否删除已存在的分录
     */
    private Boolean isDeleteEntry = false;

    private T Model;

    public UpdateExtCommand(String formId) {
        super(formId);
    }


    public UpdateExtCommand(T t, String formId) {
        super(formId);
        this.Model = t;
    }

    public UpdateExtCommand(T t, String formId, ArrayList<String> needReturnFields) {
        super(formId);
        this.Model = t;
        NeedReturnFields = needReturnFields;
    }

    public UpdateExtCommand(T t, String formId, int creator, ArrayList<String> needReturnFields, ArrayList<String> needUpDateFields) {
        super(formId);
        this.Model = t;
        Creator = creator;
        NeedReturnFields = needReturnFields;
        NeedUpDateFields = needUpDateFields;
    }

    public UpdateExtCommand(T model, String formId, ArrayList<String> needReturnFields, Boolean isDeleteEntry) {
        super(formId);
        NeedReturnFields = needReturnFields;
        this.isDeleteEntry = isDeleteEntry;
        this.Model = model;
    }

    public Boolean getAutoAdjustField() {
        return isAutoAdjustField;
    }

    public int getCreator() {
        return Creator;
    }

    public ArrayList<String> getNeedReturnFields() {
        return NeedReturnFields;
    }

    public ArrayList<String> getNeedUpDateFields() {
        return NeedUpDateFields;
    }

    public Boolean getDeleteEntry() {
        return isDeleteEntry;
    }

    public void buildBaseUpdateExtCommand(UpdateExtCommand<T> updateExtCommand, T t, ArrayList<String> needReturnFields) {
        updateExtCommand.Model = t;
        updateExtCommand.NeedReturnFields = needReturnFields;
    }

    public UpdateExtCommand<T> buildUpdateExtCommandIsDeleteEntry(UpdateExtCommand<T> updateExtCommand, Boolean isDeleteEntry) {
        updateExtCommand.isDeleteEntry = isDeleteEntry;
        return updateExtCommand;
    }
}
