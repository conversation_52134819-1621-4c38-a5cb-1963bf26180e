<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>精准营销活动</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="marketplan" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead">

</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <!--        <li class="layui-this"><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
            <!--        <li><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->

        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">任务名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="PLAN_NAME" placeholder="模糊搜索" style="" alias="plan" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">营销类型</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="CONTENT_TYPE" placeholder="" style="" alias="plan" jdbctype="" data="[{&quot;V&quot;:&quot;商品推广&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;客户运营&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;品牌推广&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;用户运营&quot;,&quot;K&quot;:&quot;4&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">推广渠道</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="PROMOTION_CHANNELS" placeholder="" style="" alias="plan" jdbctype="" data="[{&quot;V&quot;:&quot;1对1沟通&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">

            <th item_name="PLAN_NAME" name="PLAN_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" >任务名称</th>
            <th item_name="CONTENT_TYPE" name="CONTENT_TYPE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" >营销类型</th>
            <th item_name="CREATOR_NAME" name="CREATOR_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" >创建人</th>
            <th item_name="PLAN_CREATE_TIME" name="PLAN_CREATE_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">创建时间</th>
            <th item_name="EFFORT_TIME" name="EFFORT_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">生效范围</th>
            <th item_name="PROMOTION_CHANNELS" name="PROMOTION_CHANNELS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">推广渠道</th>
            <th item_name="CUSTOMER_SUM" name="CUSTOMER_SUM" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">客户总数量</th>
            <th item_name="PROCESSER" name="PROCESSER" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">待触达客户数</th>
            <th item_name="OPERATION" name="OPERATION" body="td-link-color" order="0" url="/market/sale/marketplandetail.do?planId=${ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">操作</th>

        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by plan.ID DESC" groupby=" GROUP BY plan.ID">StringBuilder sql=new StringBuilder("select ");

	sql.append("
                plan.ID,
	PLAN_NAME,
	CASE CONTENT_TYPE
	WHEN 1 THEN '商品推广'
	WHEN 2 THEN '客户运营'
	WHEN 3 THEN '品牌推广'
	WHEN 4 THEN '用户运营'
	ELSE CONTENT_TYPE END AS CONTENT_TYPE,

	CREATOR_NAME,
	DATE_FORMAT(PLAN_CREATE_TIME, '%Y/%m/%d %H:%i:%s') AS PLAN_CREATE_TIME,
	CONCAT(DATE_FORMAT(PLAN_CREATE_TIME, '%Y/%m/%d'),'-',DATE_FORMAT(PLAN_END_TIME, '%Y/%m/%d'))  AS EFFORT_TIME,

	CASE PROMOTION_CHANNELS
	WHEN 1 THEN '1对1沟通'
	WHEN 2 THEN '图片'
	ELSE PROMOTION_CHANNELS
END AS PROMOTION_CHANNELS,
count(tmpt.ID) AS CUSTOMER_SUM,
count(tmpt.ID)-SUM(CASE tmpt.SEND_MSG WHEN 1 THEN 1 ELSE 0 END ) AS PROCESSER,
                '查看清单' AS  OPERATION

FROM
	T_MARKETING_PLAN plan
                	JOIN T_MARKETING_PLAN_TRADER tmpt ON plan.ID =tmpt.PLAN_ID
	JOIN T_R_TRADER_J_USER tu ON tmpt.TRADER_ID=tu.TRADER_ID  AND tu.USER_ID=");
                sql.append($$("EZ_SESSION_USER_ID_KEY") );

                sql.append("
where
    plan.PLAN_STATUS =1
    AND plan.PLAN_START_TIME  <=   now()
    and plan.PLAN_END_TIME   >=  NOW()
    and plan.ID IN (
	SELECT
		PLAN_ID
	from
		T_MARKETING_PLAN_TRADER pt
 	JOIN T_R_TRADER_J_USER tu ON pt.TRADER_ID=tu.TRADER_ID
	where tu.USER_ID=");
                sql.append($$("EZ_SESSION_USER_ID_KEY") );
                sql.append(")" );

list=search(sql);
return list;</pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">

    <script>

        $(function(){


            // $("td[item_name=DELIVERY_STATUS]").each(function(e){
            //     if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
            // })
            // $("td[item_name=INVOICE_STATUS]").each(function(e){
            //     if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
            // })
            //
            // $("td[item_name=DELIVERY_DIRECT]").each(function(e){
            //     if($(this).text().indexOf("是")>=0){ $(this).css("color","red")}
            // })

        })


    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>