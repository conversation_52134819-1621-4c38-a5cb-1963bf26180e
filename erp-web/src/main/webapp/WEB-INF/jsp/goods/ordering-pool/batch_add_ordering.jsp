<%--
  Created by IntelliJ IDEA.
  User: admin
  Date: 2021/5/25
  Time: 8:59
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="批量导入定品" scope="application" />
<%@ include file="../../common/common.jsp"%>
<html>
<head>
</head>
<body>
<div class="form-list  form-tips4 ml7">
    <form id="batchAddOrdering" method="post" enctype="multipart/form-data">
        <ul>
            <li style="margin-bottom:8px;">
                <div class="form-tips">
                    <lable>请上传表格</lable>
                </div>
                <div class='f_left'>
                    <div class="form-blanks">
                        <div class="pos_rel">
                            <input type="file" class="upload_file" style="display: none;" name="safile" id="safile">
                            <input type="text" class="input-middle" id="uri" placeholder="请上传excel" name="uri" readonly="readonly">
                            <label class="bt-bg-style bt-middle bg-light-blue" type="file" onclick="return $('#safile').click();">浏览</label>
                        </div>
                    </div>
                    <div class="pop-friend-tips" style="margin: 7px 0 0 0;">
                        如果您没有标准模板，请<a href="<%= basePath %>static/template/批量导入定品模板.xlsx">下载标准模板</a>
                    </div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <button type="button" class="bt-bg-style bg-deep-green" onclick="subForm();">提交</button>
            <button class="dele" type="button" id="close-layer" onclick="cancelSave()">取消</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>
</body>
</html>
<script>
    $(document).ready(function(){
        $("input[name='safile']").on('change', function( e ){
            //e.currentTarget.files 是一个数组，如果支持多个文件，则需要遍历
            let name = e.currentTarget.files[0].name;
            $("#uri").val(name);
        });
    })
    function subForm(){
        checkLogin();
        if($("#safile").val() == undefined || $("#safile").val() == ""){
            layer.alert("请选择需要上传的文件！");$("#uri").focus();
            return;
        }
        $("#batchAddOrdering").ajaxSubmit({
            async : false,
            url : './batchSaveOrdering.do',
            data : $("#batchAddOrdering").serialize(),
            type : "POST",
            dataType : "json",
            success : function(data) {
                if(data.code==0){
                    if(data.data.repeatCount == 0){
                        layer.alert(
                            '定品池已成功加入'+(data.data.totalCount-data.data.repeatCount)+'条SKU！<br />',
                            {   /*icon: 1,*/
                                closeBtn: 0,
                                btnAlign: 'c',
                                title:'提示:',
                                area : ['400px', '180px']},
                            function () {
                                window.parent.search();
                            });
                    }else{
                        layer.alert(
                            '定品池已成功加入'+(data.data.totalCount-data.data.repeatCount)+'条SKU！<br />'
                            +'注：此次导入数据中有'+data.data.repeatCount+'条SKU与定品池重复。',
                            {   /*icon: 1,*/
                                closeBtn: 0,
                                btnAlign: 'c',
                                title:'提示:',
                                area : ['400px', '180px']},
                            function () {
                                window.parent.search();
                            });
                    }
                }else{
                    layer.alert(
                        data.message,
                        {
                            closeBtn: 0,
                            btnAlign: 'c',
                            title:'提示:',
                            area : ['400px', '180px']},
                        function () {
                            layer.closeAll();
                        });
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
                if(data.status ==400){
                    layer.alert("当前操作失败")
                }
                if(data.status ==500 || data.status ==501){
                    layer.alert("当前操作失败")
                }
            }
        });
    }

    function cancelSave() {
        parent.layer.closeAll();
    }
</script>
