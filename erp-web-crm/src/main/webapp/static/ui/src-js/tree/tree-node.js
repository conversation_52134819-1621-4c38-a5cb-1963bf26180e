Vue.component('ui-tree-node', {
    template: `
        <div class="vd-ui-tree-node">

            <!-- 当前组织 -->
            <div class="ui-tree-node-department" @click="toggleUser">
                <div class="ui-tree-node-dept-checkbox"
                    :class="{'active': item.checked || item.indeterminate}"
                    @click.stop="clickDepartment"
                >
                    <i class="vd-ui_icon icon-selected2" v-if="item.checked"></i>
                    <i class="vd-ui_icon icon-deduct" v-else-if="item.indeterminate"></i>
                </div>
                <div class="icon-arrow">
                    <i class="vd-ui_icon icon-right"></i>
                </div>
                <img class="icon-file" src="/static/image/common/file.svg" />
                <span class="department-name">{{ item.departmentName }}</span>
            </div>

            <!-- 人员列表 -->
            <div class="ui-tree-node-user" v-if="item.userList.length && item.isOpen">
                <ul class="ul-tree-node-user-list">
                    <li
                        class="ul-tree-node-user-item" 
                        v-for="user in item.userList" :key="'user' + item.departmentId + user.userId"
                        @click="clickUser(user)"
                    >
                        <div class="ui-tree-node-user-checkbox"
                            :class="{'active': Tree.selectIds.includes(user.userId)}"
                        >
                            <i class="vd-ui_icon icon-selected2"></i>
                        </div>
                        <img class="ui-tree-node-user-avatar" :src="user.aliasHeadPicture || GLOBAL.defaultAvatar" onerror="this.src='/static/image/crm-user-avatar.svg'" v-if="avatar" />
                        <span class="ui-tree-node-user-name">{{ user.userName }}</span>
                </ul>
            </div>

            <div class="ui-tree-node-child" v-if="item.childDepartment && item.childDepartment.length && item.isOpen">
                <ui-tree-node
                    :item="childItem"
                    v-for="childItem in item.childDepartment"
                    :key="childItem.departmentId"
                    @pick="pick"
                    :avatar="avatar"
                ></ui-tree-node>
            </div>

        </div>
    `,
    props: {
        item: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        avatar: {
            type: Boolean,
            default: true
        }
    },
    inject: {
        Tree: {
            default: ''
        }
    },
    data() {
        return {
        }
    },
    mounted() {
    },
    methods: {
        // 展开组织
        toggleUser() {
            this.item.isOpen = !this.item.isOpen;
        },
        // 选用户
        clickUser(user) {
            if (this.Tree.selectIds.includes(user.userId)) {
                let index = this.Tree.selectIds.indexOf(user.userId);
                this.Tree.selectIds.splice(index, 1);
                let index2 = this.Tree.selectList.findIndex(f => f.userId == user.userId);
                this.Tree.selectList.splice(index2, 1);
            } else {
                this.Tree.selectIds.push(user.userId);
                this.Tree.selectList.push(user);
            }
            this.$emit('pick', user.userName);
        },
        // 选组织
        clickDepartment() {
            let userList = this.item.userList || [];
            let childs = this.item.childDepartment || [];

            if (this.item.indeterminate) {
                this.item.indeterminate = false;
                this.item.checked = true;
                // 子集全部选中
                userList.length && this.changeUserStatus(userList, true);
                childs.length && this.changeChildStatus(childs, true);
            } else if (this.item.checked) {
                this.item.indeterminate = false;
                this.item.checked = false;
                // 子集全部取消选中
                userList.length && this.changeUserStatus(userList, false);
                childs.length && this.changeChildStatus(childs, false);
            } else {
                this.item.indeterminate = false;
                this.item.checked = true;
                // 子集全部选中
                userList.length && this.changeUserStatus(userList, true);
                childs.length && this.changeChildStatus(childs, true);
            }
            this.$emit('pick', this.item.departmentName);
        },
        // 修改用户选项状态
        toggleUserChoose(item, bool) {
            if (bool) {
                if (!this.Tree.selectIds.includes(item.userId)) {
                    this.Tree.selectIds.push(item.userId);
                    this.Tree.selectList.push(item);
                }
            } else {
                let index = this.Tree.selectIds.indexOf(item.userId);
                this.Tree.selectIds.splice(index, 1);
                let index2 = this.Tree.selectList.findIndex(f => f.userId == item.userId);
                this.Tree.selectList.splice(index2, 1);
            }
        },
        // 子集: 全选/全不选
        changeUserStatus(arr, bool) {
            arr.forEach(item=> {
                this.toggleUserChoose(item, bool);
            })
        },

        changeChildStatus (arr, bool) {
            arr.forEach(item=> {
                this.changeUserStatus(item.userList, bool);

                if (item.childDepartment && item.childDepartment.length) {
                    this.changeChildStatus(item.childDepartment, bool);
                }
            })
        },
        pick(data) {
            this.$emit('pick', data);
        }
    }
})