package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.service.KingDeeStorageInApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description 单位转换入库
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchUnitConversionInWriter extends BaseWriter<KingDeeStorageInDto> {


    @Autowired
    private KingDeeStorageInApiService kingDeeStorageInApiService;

    @Override
    public void doWrite(KingDeeStorageInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("单位转换入库:{}", JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveStorageIn);
        kingDeeStorageInApiService.register(dto,true);

    }


}
