package com.vedeng.aftersales.model;

import java.io.Serializable;
import java.util.Date;

public class AfterSalesInvoice implements Serializable{
    /**
	 * @Fields serialVersionUID : TODO
	 */
	private static final long serialVersionUID = 1L;

	private Integer afterSalesInvoiceId;

    private Integer afterSalesId;

    private Integer invoiceId;

    private Integer isRefundInvoice;

    private Integer status;
    
    private Integer isSendInvoice;

    private Date sendTime;//发票寄送时间

    /**
     * 类型
     */
    private Integer type;

    /**
     * 处理状态 0无处理状态 1未处理 2部分处理 3全部处理
     */
    private Integer handleStatus;

    /**
     * 操作处理备注
     */
    private String handleComments;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人ID
     */
    private Integer updater;
    /**
     * 采购售后仅退票（发票号）
     */
    private String invoiceNo;

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Integer getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(Integer handleStatus) {
        this.handleStatus = handleStatus;
    }

    public String getHandleComments() {
        return handleComments;
    }

    public void setHandleComments(String handleComments) {
        this.handleComments = handleComments;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getAfterSalesInvoiceId() {
        return afterSalesInvoiceId;
    }

    public void setAfterSalesInvoiceId(Integer afterSalesInvoiceId) {
        this.afterSalesInvoiceId = afterSalesInvoiceId;
    }

    public Integer getAfterSalesId() {
        return afterSalesId;
    }

    public void setAfterSalesId(Integer afterSalesId) {
        this.afterSalesId = afterSalesId;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Integer getIsRefundInvoice() {
        return isRefundInvoice;
    }

    public void setIsRefundInvoice(Integer isRefundInvoice) {
        this.isRefundInvoice = isRefundInvoice;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

	public Integer getIsSendInvoice() {
		return isSendInvoice;
	}

	public void setIsSendInvoice(Integer isSendInvoice) {
		this.isSendInvoice = isSendInvoice;
	}

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }
}