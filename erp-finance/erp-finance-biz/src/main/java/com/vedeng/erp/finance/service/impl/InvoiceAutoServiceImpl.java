package com.vedeng.erp.finance.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.dto.OpenInvoiceResultDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.InvoiceMethodEnum;
import com.vedeng.erp.finance.enums.SalesOpenInvoiceTypeEnum;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceAutoService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/18 9:38
 **/
@Service
@Slf4j
public class InvoiceAutoServiceImpl implements InvoiceAutoService {

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;

    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;

    @Autowired
    private FullyDigitalInvoiceApiService fullyDigitalInvoiceApiService;


    private static final int CORE_POOL_SIZE = 2;
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 2;
    private static final long KEEP_ALIVE_TIME = 60L;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(MAX_POOL_SIZE * 1000, true),
            new ThreadFactoryBuilder().setNameFormat("invoiceOpenService-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy());

    @Override
    public void doPageOpen() throws InterruptedException {

        boolean lock = RedissonLockUtils.tryLock(ErpConstant.INVOICING_OPEN_TASK,0,2, TimeUnit.HOURS);
        if (!lock) {
            log.info("定时任务加锁失败，触发开票,存在处理中的开票业务");
            return;
        }
        boolean lockAfter = RedissonLockUtils.tryLock(ErpConstant.INVOICING_AFTERMARKET_OPEN_TASK,0,2, TimeUnit.HOURS);
        if (!lockAfter) {
            log.info("定时任务加锁失败，触发开票,存在处理中的开票业务");
            RedissonLockUtils.unlock(ErpConstant.INVOICING_OPEN_TASK);
            return;
        }
        Page<Object> invoicePage = new Page<>(1, 100);
        PageInfo<InvoiceApplyDto> data = invoiceApplyApiService.getWaitInvoiceApply(invoicePage,null);
        long total = data.getTotal();
        Page<Object> invoiceAfterPage = new Page<>(1, 100);
        PageInfo<InvoiceApplyDto> pageInfo = invoiceApplyApiService.getAtWaitInvoiceApply(invoiceAfterPage, "");
        long pageInfoTotal = pageInfo.getTotal();
        long allTotal = total + pageInfoTotal;

        RedisUtil.KeyOps.delete(ErpConstant.INVOICING_OPEN_TOTAL);
        RedisUtil.KeyOps.delete(ErpConstant.INVOICING_OPEN_LOAD);
        RedisUtil.StringOps.incrBy(ErpConstant.INVOICING_OPEN_TOTAL, allTotal);
        RedisUtil.StringOps.incrBy(ErpConstant.INVOICING_OPEN_LOAD, 0);
        openSaleOrderInvoice(data);
        openAfterSaleOrderInvoice(pageInfo);

    }

    private void openAfterSaleOrderInvoice(PageInfo<InvoiceApplyDto> pageInfo) {
        try {
            int pageSize = 100;
            int pageNum = 1;
            pageNum = pageInfo.getPages();
            while (true) {
                // 分页查询所有未开票的售后安调单
                Page<Object> page = new Page<>(pageNum, pageSize);
                PageInfo<InvoiceApplyDto> pageInfos = invoiceApplyApiService.getAtWaitInvoiceApply(page, "");

                List<CompletableFuture<Void>> futures = pageInfos.getList().stream().map(invoiceApplyDto ->
                        CompletableFuture.runAsync(() -> {
                            InvoiceCheckRequestDto invoiceCheckRequestDto = getInvoiceCheckRequestDto(invoiceApplyDto);
                            invoiceCheckRequestDto.setCheckChainEnum(CheckChainEnum.INVOICE_OPEN_AFTER);
                            InvoiceCheckResultDto invoiceCheckResultDto = invoiceCheckApiService.openCheck(invoiceCheckRequestDto);

                            if (!invoiceCheckResultDto.getSuccess()) {
                                RedisUtil.StringOps.incrBy(ErpConstant.INVOICING_OPEN_LOAD, 1);
                                return;
                            }

                            OpenInvoiceResultDto openInvoiceResultDto = fullyDigitalInvoiceApiService.openSaleInvoice(invoiceApplyDto, SalesOpenInvoiceTypeEnum.AT_OPEN_INVOICE);
                            if (!openInvoiceResultDto.isSuccess()) {
                                log.error("数电发票开票定时任务异常{}", openInvoiceResultDto.getMsg());
                            }
                            RedisUtil.StringOps.incrBy(ErpConstant.INVOICING_OPEN_LOAD, 1);
                        }, executor) // 使用自定义的线程池
                ).collect(Collectors.toList());

                // 等待所有任务完成
                futures.forEach(future -> {
                    try {
                        future.get();
                    } catch (Exception e) {
                        log.error("数电发票开票定时任务异常:", e);
                    }
                });

                if (!pageInfo.isHasPreviousPage()) {
                    break;
                }
                pageNum = pageInfo.getPrePage();
            }
        } catch (Exception e) {
            log.error("数电发票开票定时任务异常:", e);
        }finally {
            RedissonLockUtils.unlock(ErpConstant.INVOICING_AFTERMARKET_OPEN_TASK);
        }
    }

    private void openSaleOrderInvoice(PageInfo<InvoiceApplyDto> data) {
        try {
            int pageSize = 100;
            int pageNum = 1;
            // 拉取需要开票的开票申请
            pageNum = data.getPages();
            while (true) {
                log.info("分页执行进度 {},{}", pageSize, pageNum);
                Page<Object> page = new Page<>(pageNum, pageSize);
                PageInfo<InvoiceApplyDto> invoiceApplyDtoPageInfo = invoiceApplyApiService.getWaitInvoiceApply(page,null);
                // 调用开票

                List<CompletableFuture<Void>> futures = invoiceApplyDtoPageInfo.getList().stream().map(invoiceApplyDto ->
                        CompletableFuture.runAsync(() -> {
                            Integer invoiceMethod = invoiceApplyDto.getApplyMethod();
                            if (!InvoiceMethodEnum.TICKET_AND_FREIGH.getApplyMethodCode().equals(invoiceMethod)) {
                                InvoiceCheckRequestDto invoiceCheckRequestDto = getInvoiceCheckRequestDto(invoiceApplyDto);
                                invoiceCheckRequestDto.setCheckChainEnum(CheckChainEnum.INVOICE_OPEN_SALES);
                                InvoiceCheckResultDto invoiceCheckResultDto = invoiceCheckApiService.openCheck(invoiceCheckRequestDto);
                                if (!invoiceCheckResultDto.getSuccess()) {
                                    RedisUtil.StringOps.incrBy(ErpConstant.INVOICING_OPEN_LOAD, 1);
                                    return;
                                }
                            }

                            OpenInvoiceResultDto openInvoiceResultDto = fullyDigitalInvoiceApiService.openSaleInvoice(invoiceApplyDto, SalesOpenInvoiceTypeEnum.SALE_OPEN_INVOICE);
                            if (!openInvoiceResultDto.isSuccess()) {
                                log.error("数电发票开票定时任务异常{}", openInvoiceResultDto.getMsg());
                            }
                            RedisUtil.StringOps.incrBy(ErpConstant.INVOICING_OPEN_LOAD, 1);
                        }, executor) // 使用自定义的线程池
                ).collect(Collectors.toList());

                // 等待所有任务完成
                futures.forEach(future -> {
                    try {
                        future.get();
                    } catch (Exception e) {
                        log.error("数电发票开票定时任务异常:", e);
                    }
                });

                if (!invoiceApplyDtoPageInfo.isHasPreviousPage()) {
                    break;
                }

                pageNum = invoiceApplyDtoPageInfo.getPrePage();
                log.info("分页查询进度 {},{}", pageSize, pageNum);
            }
        } catch (Exception e) {
            log.error("数电发票开票定时任务异常:",e);
        } finally {
            RedissonLockUtils.unlock(ErpConstant.INVOICING_OPEN_TASK);
        }
    }


    private static InvoiceCheckRequestDto getInvoiceCheckRequestDto(InvoiceApplyDto invoiceApply) {
        InvoiceCheckRequestDto invoiceCheckRequestDto = new InvoiceCheckRequestDto();
        invoiceCheckRequestDto.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
        invoiceCheckRequestDto.setInvoiceProperty(invoiceApply.getInvoiceProperty());
        invoiceCheckRequestDto.setType(invoiceApply.getType());
        invoiceCheckRequestDto.setRelatedId(invoiceApply.getRelatedId());
        invoiceCheckRequestDto.setInvoiceInfoType(invoiceApply.getInvoiceInfoType());
        invoiceCheckRequestDto.setInvoiceMessage(invoiceApply.getInvoiceMessage());
        invoiceCheckRequestDto.setDetailList(invoiceApply.getInvoiceApplyDetailDtoList().stream().map(invoiceApplyDetailDto -> {
            InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto invoiceCheckRequestDetailDto = new InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto();
            invoiceCheckRequestDetailDto.setInvoiceApplyDetailId(invoiceApplyDetailDto.getInvoiceApplyDetailId());
            invoiceCheckRequestDetailDto.setDetailGoodsId(invoiceApplyDetailDto.getDetailgoodsId());
            invoiceCheckRequestDetailDto.setPrice(invoiceApplyDetailDto.getPrice());
            invoiceCheckRequestDetailDto.setNum(invoiceApplyDetailDto.getNum());
            invoiceCheckRequestDetailDto.setTotalAmount(invoiceApplyDetailDto.getTotalAmount());
            return invoiceCheckRequestDetailDto;
        }).collect(Collectors.toList()));
        return invoiceCheckRequestDto;
    }
}
