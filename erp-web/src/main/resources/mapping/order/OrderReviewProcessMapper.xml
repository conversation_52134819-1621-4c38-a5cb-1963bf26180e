<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.OrderReviewProcessMapper" >
    <insert id="insertSelective">
        insert into T_ORDER_REVIEW_PRECESS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">
                TYPE,
            </if>
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="businessKey != null">
                BUSINESS_KEY,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=INTEGER},
            </if>
            <if test="businessKey != null">
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="getAllOrderReviewProcess" resultType="com.vedeng.order.model.po.OrderReviewProcessPo">
        SELECT
            *
        FROM
            T_ORDER_REVIEW_PRECESS
        WHERE
            IS_DELETE = 0
    </select>
</mapper>
