package com.vedeng.erp.finance.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 红字确认单返回参数
 */
@Getter
@Setter
public class RedConfirmResponseDto implements Serializable {

    private static final long serialVersionUID = -6281966088178896832L;

    /**
     * 红字确认单ID
     */
    private Integer invoiceRedConfirmationId;

    /**
     * 失败原因
     */
    private String failReason;

    public RedConfirmResponseDto(){

    }

    public RedConfirmResponseDto(Integer invoiceRedConfirmationId,String failReason){
        this.invoiceRedConfirmationId = invoiceRedConfirmationId;
        this.failReason = failReason;
    }
}
