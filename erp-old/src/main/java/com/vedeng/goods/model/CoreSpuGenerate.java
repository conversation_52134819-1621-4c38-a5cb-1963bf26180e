package com.vedeng.goods.model;

import java.util.Date;

public class CoreSpuGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer categoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer brandId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_NO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String spuNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String spuName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SHOW_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String showName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_LEVEL
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     * @deprecated ERP_LV_2020_105 移除来商品级别
     */
    @Deprecated
    private Integer spuLevel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.SPU_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuType;

    /**
     * 二级产品类型（目前只有“大型医疗设备”，其一级类别为“设备”）
     *
     * @since ERP_LV_2020_98
     */
    private Integer secondLevelSpuType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer firstEngageId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.REGISTRATION_ICON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String registrationIcon;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.WIKI_HREF
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String wikiHref;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.OPERATE_INFO_FLAG
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer operateInfoFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CHECK_STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer checkStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.OPERATE_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer operateInfoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.HOSPITAL_TAGS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String hospitalTags;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CHECK_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date checkTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.CHECKER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer checker;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.DELETE_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String deleteReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.LAST_CHECK_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String lastCheckReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.ASSIGNMENT_MANAGER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer assignmentManagerId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SPU.ASSIGNMENT_ASSISTANT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer assignmentAssistantId;

    /**
     * 器械类型，1医疗器械，2非医疗器械
     *
     * @since ERP_LV_2020_56
     */
    private Integer apparatusType;


    /**
     * 存储条件（温度)
     *
     * @since ERP_SV_2020_61
     * @see com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum
     */
    private Integer storageConditionTemperature;

    /**
     * 存储条件（温度,单位：摄氏度），其他温度范围值存储较小值
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionTemperatureLowerValue;

    /**
     * 存储条件（温度，单位：摄氏度），其他温度范围值存储较大值
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionTemperatureUpperValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较小的值
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionHumidityLowerValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较大的
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionHumidityUpperValue;

    /**
     * 存储条件（其他）
     *
     * @since ERP_SV_2020_61
     * @see com.vedeng.goods.enums.GoodsStorageConditionOthersEnum
     */
    private String storageConditionOthers;

    /**
     * 关联sku的技术参数名称列表（用逗号分隔）
     *
     * @since ERP_LV_2020_86
     */
    private String technicalParameterNames;

    /**
     * 规格、型号
     *
     * @since ERP_LV_2020_98
     */
    private String specsModel;

    /**
     * 是否在《医疗器械分类目录》，1是，2否
     *
     * @since ERP_LV_2020_98
     */
    private Integer medicalInstrumentCatalogIncluded;


    /**
     * 商品级别
     *
     * @since ERP_LV_2020_105
     */
    private Integer goodsLevelNo;

    /**
     * 商品档位
     *
     * @since  ERP_LV_2020_105
     */
    private Integer goodsPositionNo;

    /**
     * 税收分类编码
     */
    private String taxClassificationCode;

    public String getTaxClassificationCode() {
        return taxClassificationCode;
    }

    public void setTaxClassificationCode(String taxClassificationCode) {
        this.taxClassificationCode = taxClassificationCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.SPU_ID
     *
     * @return the value of V_CORE_SPU.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuId() {
        return spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.SPU_ID
     *
     * @param spuId the value for V_CORE_SPU.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.CATEGORY_ID
     *
     * @return the value of V_CORE_SPU.CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.CATEGORY_ID
     *
     * @param categoryId the value for V_CORE_SPU.CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.BRAND_ID
     *
     * @return the value of V_CORE_SPU.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBrandId() {
        return brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.BRAND_ID
     *
     * @param brandId the value for V_CORE_SPU.BRAND_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.SPU_NO
     *
     * @return the value of V_CORE_SPU.SPU_NO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getSpuNo() {
        return spuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.SPU_NO
     *
     * @param spuNo the value for V_CORE_SPU.SPU_NO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuNo(String spuNo) {
        this.spuNo = spuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.SPU_NAME
     *
     * @return the value of V_CORE_SPU.SPU_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getSpuName() {
        return spuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.SPU_NAME
     *
     * @param spuName the value for V_CORE_SPU.SPU_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuName(String spuName) {
        this.spuName = spuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.SHOW_NAME
     *
     * @return the value of V_CORE_SPU.SHOW_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getShowName() {
        return showName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.SHOW_NAME
     *
     * @param showName the value for V_CORE_SPU.SHOW_NAME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setShowName(String showName) {
        this.showName = showName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.SPU_LEVEL
     *
     * @return the value of V_CORE_SPU.SPU_LEVEL
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuLevel() {
        return spuLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.SPU_LEVEL
     *
     * @param spuLevel the value for V_CORE_SPU.SPU_LEVEL
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuLevel(Integer spuLevel) {
        this.spuLevel = spuLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.STATUS
     *
     * @return the value of V_CORE_SPU.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.STATUS
     *
     * @param status the value for V_CORE_SPU.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.SPU_TYPE
     *
     * @return the value of V_CORE_SPU.SPU_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuType() {
        return spuType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.SPU_TYPE
     *
     * @param spuType the value for V_CORE_SPU.SPU_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuType(Integer spuType) {
        this.spuType = spuType;
    }

    public Integer getSecondLevelSpuType() {
        return secondLevelSpuType;
    }

    public void setSecondLevelSpuType(Integer secondLevelSpuType) {
        this.secondLevelSpuType = secondLevelSpuType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.FIRST_ENGAGE_ID
     *
     * @return the value of V_CORE_SPU.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getFirstEngageId() {
        return firstEngageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.FIRST_ENGAGE_ID
     *
     * @param firstEngageId the value for V_CORE_SPU.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setFirstEngageId(Integer firstEngageId) {
        this.firstEngageId = firstEngageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.REGISTRATION_ICON
     *
     * @return the value of V_CORE_SPU.REGISTRATION_ICON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getRegistrationIcon() {
        return registrationIcon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.REGISTRATION_ICON
     *
     * @param registrationIcon the value for V_CORE_SPU.REGISTRATION_ICON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setRegistrationIcon(String registrationIcon) {
        this.registrationIcon = registrationIcon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.WIKI_HREF
     *
     * @return the value of V_CORE_SPU.WIKI_HREF
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getWikiHref() {
        return wikiHref;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.WIKI_HREF
     *
     * @param wikiHref the value for V_CORE_SPU.WIKI_HREF
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setWikiHref(String wikiHref) {
        this.wikiHref = wikiHref;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.OPERATE_INFO_FLAG
     *
     * @return the value of V_CORE_SPU.OPERATE_INFO_FLAG
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getOperateInfoFlag() {
        return operateInfoFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.OPERATE_INFO_FLAG
     *
     * @param operateInfoFlag the value for V_CORE_SPU.OPERATE_INFO_FLAG
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOperateInfoFlag(Integer operateInfoFlag) {
        this.operateInfoFlag = operateInfoFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.CHECK_STATUS
     *
     * @return the value of V_CORE_SPU.CHECK_STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCheckStatus() {
        return checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.CHECK_STATUS
     *
     * @param checkStatus the value for V_CORE_SPU.CHECK_STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.OPERATE_INFO_ID
     *
     * @return the value of V_CORE_SPU.OPERATE_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getOperateInfoId() {
        return operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.OPERATE_INFO_ID
     *
     * @param operateInfoId the value for V_CORE_SPU.OPERATE_INFO_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOperateInfoId(Integer operateInfoId) {
        this.operateInfoId = operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.HOSPITAL_TAGS
     *
     * @return the value of V_CORE_SPU.HOSPITAL_TAGS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getHospitalTags() {
        return hospitalTags;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.HOSPITAL_TAGS
     *
     * @param hospitalTags the value for V_CORE_SPU.HOSPITAL_TAGS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setHospitalTags(String hospitalTags) {
        this.hospitalTags = hospitalTags;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.ADD_TIME
     *
     * @return the value of V_CORE_SPU.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.ADD_TIME
     *
     * @param addTime the value for V_CORE_SPU.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.CREATOR
     *
     * @return the value of V_CORE_SPU.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.CREATOR
     *
     * @param creator the value for V_CORE_SPU.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.MOD_TIME
     *
     * @return the value of V_CORE_SPU.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.MOD_TIME
     *
     * @param modTime the value for V_CORE_SPU.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.UPDATER
     *
     * @return the value of V_CORE_SPU.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.UPDATER
     *
     * @param updater the value for V_CORE_SPU.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.CHECK_TIME
     *
     * @return the value of V_CORE_SPU.CHECK_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getCheckTime() {
        return checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.CHECK_TIME
     *
     * @param checkTime the value for V_CORE_SPU.CHECK_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.CHECKER
     *
     * @return the value of V_CORE_SPU.CHECKER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getChecker() {
        return checker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.CHECKER
     *
     * @param checker the value for V_CORE_SPU.CHECKER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setChecker(Integer checker) {
        this.checker = checker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.DELETE_REASON
     *
     * @return the value of V_CORE_SPU.DELETE_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getDeleteReason() {
        return deleteReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.DELETE_REASON
     *
     * @param deleteReason the value for V_CORE_SPU.DELETE_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.LAST_CHECK_REASON
     *
     * @return the value of V_CORE_SPU.LAST_CHECK_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getLastCheckReason() {
        return lastCheckReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.LAST_CHECK_REASON
     *
     * @param lastCheckReason the value for V_CORE_SPU.LAST_CHECK_REASON
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setLastCheckReason(String lastCheckReason) {
        this.lastCheckReason = lastCheckReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.ASSIGNMENT_MANAGER_ID
     *
     * @return the value of V_CORE_SPU.ASSIGNMENT_MANAGER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getAssignmentManagerId() {
        return assignmentManagerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.ASSIGNMENT_MANAGER_ID
     *
     * @param assignmentManagerId the value for V_CORE_SPU.ASSIGNMENT_MANAGER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAssignmentManagerId(Integer assignmentManagerId) {
        this.assignmentManagerId = assignmentManagerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SPU.ASSIGNMENT_ASSISTANT_ID
     *
     * @return the value of V_CORE_SPU.ASSIGNMENT_ASSISTANT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getAssignmentAssistantId() {
        return assignmentAssistantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SPU.ASSIGNMENT_ASSISTANT_ID
     *
     * @param assignmentAssistantId the value for V_CORE_SPU.ASSIGNMENT_ASSISTANT_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAssignmentAssistantId(Integer assignmentAssistantId) {
        this.assignmentAssistantId = assignmentAssistantId;
    }

    /**
     * 禁用原因
     */
    private String disabledReason;

    /**
     * 非医疗器械一级分类
     */
    private Integer noMedicalFirstType;

    /**
     * 非医疗器械二级分类
     */
    private Integer noMedicalSecondType;

    public Integer getNoMedicalSecondType() {
        return noMedicalSecondType;
    }

    public void setNoMedicalSecondType(Integer noMedicalSecondType) {
        this.noMedicalSecondType = noMedicalSecondType;
    }

    public Integer getNoMedicalFirstType() {
        return noMedicalFirstType;
    }

    public void setNoMedicalFirstType(Integer noMedicalFirstType) {
        this.noMedicalFirstType = noMedicalFirstType;
    }

    public String getDisabledReason() {
        return disabledReason;
    }

    public void setDisabledReason(String disabledReason) {
        this.disabledReason = disabledReason;
    }

    public Integer getApparatusType() {
        return apparatusType;
    }

    public void setApparatusType(Integer apparatusType) {
        this.apparatusType = apparatusType;
    }

    public Integer getStorageConditionTemperature() {
        return storageConditionTemperature;
    }

    public void setStorageConditionTemperature(Integer storageConditionTemperature) {
        this.storageConditionTemperature = storageConditionTemperature;
    }

    public Float getStorageConditionTemperatureLowerValue() {
        return storageConditionTemperatureLowerValue;
    }

    public void setStorageConditionTemperatureLowerValue(Float storageConditionTemperatureLowerValue) {
        this.storageConditionTemperatureLowerValue = storageConditionTemperatureLowerValue;
    }

    public Float getStorageConditionTemperatureUpperValue() {
        return storageConditionTemperatureUpperValue;
    }

    public void setStorageConditionTemperatureUpperValue(Float storageConditionTemperatureUpperValue) {
        this.storageConditionTemperatureUpperValue = storageConditionTemperatureUpperValue;
    }

    public Float getStorageConditionHumidityLowerValue() {
        return storageConditionHumidityLowerValue;
    }

    public void setStorageConditionHumidityLowerValue(Float storageConditionHumidityLowerValue) {
        this.storageConditionHumidityLowerValue = storageConditionHumidityLowerValue;
    }

    public Float getStorageConditionHumidityUpperValue() {
        return storageConditionHumidityUpperValue;
    }

    public void setStorageConditionHumidityUpperValue(Float storageConditionHumidityUpperValue) {
        this.storageConditionHumidityUpperValue = storageConditionHumidityUpperValue;
    }

    public String getStorageConditionOthers() {
        return storageConditionOthers;
    }

    public void setStorageConditionOthers(String storageConditionOthers) {
        this.storageConditionOthers = storageConditionOthers;
    }

    public String getTechnicalParameterNames() {
        return technicalParameterNames;
    }

    public void setTechnicalParameterNames(String technicalParameterNames) {
        this.technicalParameterNames = technicalParameterNames;
    }

    public String getSpecsModel() {
        return specsModel;
    }

    public void setSpecsModel(String specsModel) {
        this.specsModel = specsModel;
    }

    public Integer getMedicalInstrumentCatalogIncluded() {
        return medicalInstrumentCatalogIncluded;
    }

    public void setMedicalInstrumentCatalogIncluded(Integer medicalInstrumentCatalogIncluded) {
        this.medicalInstrumentCatalogIncluded = medicalInstrumentCatalogIncluded;
    }

    public Integer getGoodsLevelNo() {
        return goodsLevelNo;
    }

    public void setGoodsLevelNo(Integer goodsLevelNo) {
        this.goodsLevelNo = goodsLevelNo;
    }

    public Integer getGoodsPositionNo() {
        return goodsPositionNo;
    }

    public void setGoodsPositionNo(Integer goodsPositionNo) {
        this.goodsPositionNo = goodsPositionNo;
    }
}