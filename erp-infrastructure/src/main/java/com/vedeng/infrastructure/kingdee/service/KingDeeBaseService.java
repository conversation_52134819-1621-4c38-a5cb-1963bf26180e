package com.vedeng.infrastructure.kingdee.service;

import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import org.checkerframework.checker.units.qual.C;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶基础能力接口
 * @date 2022/8/27 12:40
 */
public interface KingDeeBaseService<D> {


    /**
     * 新增
     *
     * @param d d
     */
    void save(D d);

    /**
     * 查询
     * 返回金蝶id 和 本地金蝶表数据id
     * @param d d
     */
    void query(D d);

    /**
     * 修改
     *
     * @param d d
     */
    void update(D d);

    /**
     * 修改 反审核
     *
     * @param d d
     */
    void updateAndUnAudit(D d);

    /**
     * 提交 审核
     * @param d
     */
    void submitAndAudit(D d);
    /**
     * 修改 反审核 提交
     *
     * @param d d
     */
    void updateAndUnAuditAndSubmit(D d);

    /**
     * 删除
     *
     * @param d d
     */
    void delete(D d);

    /**
     * 是否存在
     *
     * @param d d
     * @return 是否存在
     */
    Boolean isExist(D d);


    /**
     * erp本地是否存在
     *
     * @param d d
     * @return Boolean 是否存在
     */
    Boolean localIsExist(D d);

    /**
     * 金蝶是否存在
     *
     * @param d d
     * @return Boolean 是否存在
     */
    Boolean kingDeeIsExist(D d);

    /**
     * 附件上传
     *
     * @param d
     */
    void upload(D d);

    /**
     * save方法前置处理器
     *
     * @param objects objects
     */
    default void savePre(Object... objects) {

    }

    /**
     * save方法后置处理器
     *
     * @param objects objects
     */
    default void savePost(Object... objects) {

    }

    /**
     * 金蝶 save方法前置处理器
     *
     * @param objects objects
     * @return ArrayList<String>
     */
    default ArrayList<String> kingDeeSavePre(Object... objects) {
        return null;
    }

    /**
     * 金蝶 save方法后置处理器
     *
     * @param objects objects
     */
    default void kingDeeSavePost(Object... objects) {

    }


    /**
     * update方法前置处理器
     *
     * @param objects objects
     */
    default void updatePre(Object... objects) {

    }

    /**
     * update 方法后置处理器
     *
     * @param objects objects
     */
    default void updatePost(Object... objects) {

    }


    /**
     * 金蝶 update方法前置处理器
     *
     * @param objects objects
     * @return ArrayList<String>
     */
    default ArrayList<String> kingDeeUpdatePre(Object... objects) {
        return null;
    }

    /**
     * 金蝶 update方法后置处理器
     *
     * @param objects objects
     */
    default void kingDeeUpdatePost(Object... objects) {

    }

    /**
     * 金蝶 save 方法是否自动提交审核
     * @return
     */
    default boolean getIsAutoSubmitAndAudit(D d){
        return Boolean.TRUE;
    };
}
