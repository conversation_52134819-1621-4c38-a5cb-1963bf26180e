package com.vedeng.goods.dao;

import com.vedeng.goods.model.FirstEngageGenerate;
import com.vedeng.goods.model.FirstEngageGenerateExample;
import java.util.List;

import com.vedeng.order.model.vo.FirstEngageInfoDto;
import org.apache.ibatis.annotations.Param;

public interface FirstEngageGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(FirstEngageGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(FirstEngageGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer firstEngageId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(FirstEngageGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(FirstEngageGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<FirstEngageGenerate> selectByExample(FirstEngageGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    FirstEngageGenerate selectByPrimaryKey(Integer firstEngageId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") FirstEngageGenerate record, @Param("example") FirstEngageGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") FirstEngageGenerate record, @Param("example") FirstEngageGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(FirstEngageGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(FirstEngageGenerate record);

    FirstEngageInfoDto getFirstEngageInfoBySkuNo(@Param("skuNo") String skuNo);

    FirstEngageInfoDto getInfoByFirstEngageId(Integer logBizId);
}