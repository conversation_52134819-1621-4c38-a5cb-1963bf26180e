package com.newtask.customerBillPeriodtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.MessageTemplateConstans;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.customerbillperiod.model.vo.CustomerBillPeriodOverdueManagementDetailVo;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodManagementDetailService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.trader.model.model.vo.CustomerBillPeriodOverdueManagementDetailTaskVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName CustomeBillPeriodOverdueDeliverTask.java
 * @Description TODO 账期逾期管理
 * @createTime 2021年07月27日 16:44:00
 */
@Component
@JobHandler(value = "CustomeBillPeriodOverdueTask")
public class CustomeBillPeriodOverdueTask extends AbstractJobHandler {

    @Resource
    private CustomerBillPeriodManagementDetailService customerBillPeriodManagementDetailService;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        //即将逾期
        List<CustomerBillPeriodOverdueManagementDetailVo> nearOverdueList =  customerBillPeriodManagementDetailService.getCustomerBillPeriodOverDueInfoByOverdueDay(-3);

        //已逾期
        List<CustomerBillPeriodOverdueManagementDetailVo> overduedList =  customerBillPeriodManagementDetailService.getCustomerBillPeriodOverDueInfoByOverdueDay(0);


        XxlJobLogger.log("CustomeBillPeriodOverdueTask处理逾期数据: dealList:{}", JSON.toJSONString(overduedList));
        //处理逾期数据
        customerBillPeriodManagementDetailService.dealOverDuedcustomerBill(overduedList);

        sendTimeoutMessage(nearOverdueList, MessageTemplateConstans.NEAR_OVERDE_TEMPLATE_ID);
        sendTimeoutMessage(overduedList,MessageTemplateConstans.OVERDUED_TEMPLATE_ID);


        return SUCCESS;
    }



    private void sendTimeoutMessage(List<CustomerBillPeriodOverdueManagementDetailVo> list, int messageTemplate){
        //订单号分组求逾期总额
        List<CustomerBillPeriodOverdueManagementDetailTaskVo> detailVoList = list.stream().filter(item->item.getAddTime() > 1630944000000L)
                .collect(Collectors.groupingBy(CustomerBillPeriodOverdueManagementDetailVo :: getSaleorderId)).
           values().stream().map(value -> {
            CustomerBillPeriodOverdueManagementDetailTaskVo result = new CustomerBillPeriodOverdueManagementDetailTaskVo();
            result.setUnreturnedAmount(BigDecimal.ZERO);
            //获取当前订单客户信息
            Saleorder saleorder = saleorderMapper.getSaleOrderTraderById(value.get(0).getSaleorderId());
            result.setSaleorderNo(saleorder.getSaleorderNo());
            result.setTraderName(saleorder.getTraderName());
            result.setUserId(saleorder.getUserId());

            for (CustomerBillPeriodOverdueManagementDetailVo detailVo : value) {
                result.setUnreturnedAmount(detailVo.getUnreturnedAmount().add(result.getUnreturnedAmount()));
            }
            return result;
        }).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(detailVoList)){
            return;
        }

        for(CustomerBillPeriodOverdueManagementDetailTaskVo detailVo : detailVoList){
            HashMap<String,String> map = new HashMap();
            List<Integer> users = new ArrayList<>();
            users.add(detailVo.getUserId());
            map.put("saleorderNo",detailVo.getSaleorderNo());
            map.put("traderName",detailVo.getTraderName());
            map.put("unreturnedAmount",detailVo.getUnreturnedAmount().toString());
            MessageUtil.sendMessage2(messageTemplate,users,map,
                    "./finance/accountperiod/getCustomerAccountListPage.do?traderName=" + detailVo.getTraderName()+"&saleorderNo="+detailVo.getSaleorderNo() ,"njadmin");
        }
    }
}
