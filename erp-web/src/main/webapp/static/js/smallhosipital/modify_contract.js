$(function() {
    setSkuData();

    $(document).on('click', '.J-layer-close', function () {
        layer.closeAll();
    });

    var editId = null;
    //删除地址
    $(document).on('click', '.J-sku-del', function () {
        var $this = $(this);
        layer.confirm("您是否确认删除？", function() {
            $this.parents('.J-sku-item:first').remove();
            setSkuData();
            layer.closeAll();
        })
    });

//编辑地址
    $(document).on('click', '.J-sku-edit', function () {
        var data = $(this).parents('.J-sku-item').data('json');
        editId = $(this).parents('.J-sku-item').index();
        console.log(editId);
        layer.open({
            shadeClose: false, //点击遮罩关闭
            //area: 'auto',
            area: [500, 250],
            title: '编辑sku',
            btn: null,
            content: $('.J-add-sku-tmpl').html(),
            success: function () {
                $('.J-sku-id').val(data.skuId);
                $('.J-price').val(data.contractPrice);
            }
        });

    });

    $(document).on('click', '.J-sku-add', function () {
        var priceReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        var contractPrice = $("#contractPrice").val();
        if(contractPrice ==''){
            layer.alert("合同金额不能为空!");
            return false;
        }
        if(!priceReg.test(contractPrice)){
            layer.alert("合同金额输入错误！仅允许使用数字，最多精确到小数后两位");
            return false;
        }
        var item = $('.J-sku-list .J-sku-item').eq(editId);
        var data = item.data('json');
        data.contractPrice=contractPrice;
        item.data("json",data);
        item.children(".price").html(data.contractPrice);
        setSkuData();
        layer.closeAll();
    })
})

window.batchAddSku=function (skus) {
    var skuTmpl = template($('.J-sku-tmpl').html());
    if(skus==undefined||skus==null||skus.length<1){
        return;
    }
    var preSkusStr=$('.J-sku-data').val()
    if(preSkusStr!=undefined){
        var preSkus=JSON.parse(preSkusStr);
        if(preSkus!=undefined&&preSkus.length>0){
            for(var i=0;i<skus.length;i++)
                for(var j=0;j<preSkus.length;j++){
                    if(skus[i].skuId==preSkus[j].skuId){
                        layer.closeAll();
                        layer.alert("商品V"+skus[i].skuId+"已存在，无法新增");
                        return;
                    }
                }


        }
    }
    skus.forEach(function (e) {
        var index=1;
        let items=$('.J-sku-item');
        if(items!=undefined&&items.length>0){
            index=items.length+1;
        }
        e.index=index;
        $('.J-sku-list').append(skuTmpl({data: e}));
    })
    setSkuData();
    layer.closeAll();
}



var setSkuData = function(){
    var dataArr = [];
    $('.J-sku-item').each(function () {
        dataArr.push($(this).data('json'));
    });

    $('.J-sku-data').val(JSON.stringify(dataArr));
};
