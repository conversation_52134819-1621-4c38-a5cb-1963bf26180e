package com.vedeng.order.service.validator.impl;

import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.validator.QualifyAutoAudtioValidator;
import com.vedeng.order.service.validator.dto.QualifyAutoAudtioException;
import com.vedeng.order.service.validator.dto.ValidaterResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Service
public class QualifyAutoAudtioValidatorChain {
    public static Logger logger = LoggerFactory.getLogger(QualifyAutoAudtioValidatorChain.class);

    private List<QualifyAutoAudtioValidator> qualifyAutoAudtioValidatorList = new ArrayList<>();

    @Autowired
    private QualifyAutoAudtioValidator1 qualifyAutoAudtioValidator1;

    @Autowired
    private QualifyAutoAudtioValidator2 qualifyAutoAudtioValidator2;

    @Autowired
    private QualifyAutoAudtioValidator3 qualifyAutoAudtioValidator3;

    @Autowired
    private QualifyAutoAudtioValidator4 qualifyAutoAudtioValidator4;

    @Autowired
    private QualifyAutoAudtioValidator5 qualifyAutoAudtioValidator5;

    @Autowired
    private QualifyAutoAudtioValidator6 qualifyAutoAudtioValidator6;

    @Autowired
    private QualifyAutoAudtioValidator7 qualifyAutoAudtioValidator7;

    @Autowired
    private QualifyAutoAudtioValidator8 qualifyAutoAudtioValidator8;

    @Autowired
    private QualifyAutoAudtioValidator9 qualifyAutoAudtioValidator9;

    @Autowired
    private QualifyAutoAudtioValidator10 qualifyAutoAudtioValidator10;

    @Autowired
    private QualifyAutoAudtioContextBuild qualifyAutoAudtioContextBuild;

    @PostConstruct
    private void build(){
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator1);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator2);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator3);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator4);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator5);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator6);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator7);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator8);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator9);
        qualifyAutoAudtioValidatorList.add(qualifyAutoAudtioValidator10);
    }

    public ValidaterResult validator(BuyorderGoodsVo buyorderGoodsVo) {

        qualifyAutoAudtioContextBuild.buildContext(buyorderGoodsVo);

        ValidaterResult validatorResult = new ValidaterResult();
        int i=0;
        for(QualifyAutoAudtioValidator qualifyAutoAudtioValidator : qualifyAutoAudtioValidatorList){
            i++;
            logger.info("采购单自动审核 第{}个Validator {} {}",i,buyorderGoodsVo.getBuyorderId(),buyorderGoodsVo.getBuyorderGoodsId());
            //只要找到第一个匹配的校验器 就直接返回
            if(qualifyAutoAudtioValidator.isMatch(buyorderGoodsVo)){
                try {
                    validatorResult.setAuditPassMessage(qualifyAutoAudtioValidator.validator(buyorderGoodsVo));
                    logger.info("采购单自动审核 第{}个Validator 匹配成功 {} {} {}",i,buyorderGoodsVo.getBuyorderId(),
                            buyorderGoodsVo.getBuyorderGoodsId(),validatorResult.getAuditPassMessage());

                } catch (QualifyAutoAudtioException e) {
                    validatorResult.setValidatePass(false);
                    validatorResult.setAuditFailMessage(e.getMessage());
                    logger.info("采购单自动审核 第{}个Validator 匹配失败 {} {} {}",i,buyorderGoodsVo.getBuyorderId(),
                            buyorderGoodsVo.getBuyorderGoodsId(),validatorResult.getAuditFailMessage());
                }catch(IllegalArgumentException e){
                    validatorResult.setValidatePass(false);
                    validatorResult.setAuditFailMessage(e.getMessage());
                    logger.info("采购单自动审核 第{}个Validator 匹配失败 {} {} {}",i,buyorderGoodsVo.getBuyorderId(),
                            buyorderGoodsVo.getBuyorderGoodsId(),validatorResult.getAuditFailMessage(),e);
                    throw e;
                }catch(Exception e){
                    validatorResult.setValidatePass(false);
                    validatorResult.setAuditFailMessage("第"+i+"个检测器异常,请联系管理员");
                    logger.error("采购单自动审核 第"+i+"个检测器异常",e);
                    throw e;
                }
                break;
            }
        }

        return validatorResult;
    }

    /**
     * 基于traderId和skuNo的验证方法
     * @param traderId 交易员ID
     * @param skuNo 商品编号
     * @return 验证结果
     */
    public ValidaterResult validator(Integer traderId, String skuNo) {
        // 构建验证上下文
        qualifyAutoAudtioContextBuild.buildContext(traderId, skuNo);

        ValidaterResult validatorResult = new ValidaterResult();
        int i=0;
        for(QualifyAutoAudtioValidator qualifyAutoAudtioValidator : qualifyAutoAudtioValidatorList){
            i++;
            logger.info("采购单自动审核 第{}个Validator traderId:{} skuNo:{}",i, traderId, skuNo);
            // 只要找到第一个匹配的校验器 就直接返回
            if(qualifyAutoAudtioValidator.isMatch(traderId, skuNo)){
                try {
                    validatorResult.setAuditPassMessage(qualifyAutoAudtioValidator.validator(traderId, skuNo));
                    logger.info("采购单自动审核 第{}个Validator 匹配成功 traderId:{} skuNo:{} {}",i, 
                            traderId, skuNo, validatorResult.getAuditPassMessage());

                } catch (QualifyAutoAudtioException e) {
                    validatorResult.setValidatePass(false);
                    validatorResult.setAuditFailMessage(e.getMessage());
                    logger.info("采购单自动审核 第{}个Validator 匹配失败 traderId:{} skuNo:{} {}",i,
                            traderId, skuNo, validatorResult.getAuditFailMessage());
                } catch(IllegalArgumentException e){
                    validatorResult.setValidatePass(false);
                    validatorResult.setAuditFailMessage(e.getMessage());
                    logger.info("采购单自动审核 第{}个Validator 匹配失败 traderId:{} skuNo:{} {}",i,
                            traderId, skuNo, validatorResult.getAuditFailMessage(),e);
                    throw e;
                } catch(Exception e){
                    validatorResult.setValidatePass(false);
                    validatorResult.setAuditFailMessage("第"+i+"个检测器异常,请联系管理员");
                    logger.error("采购单自动审核 第"+i+"个检测器异常",e);
                    throw e;
                }
                break;
            }
        }

        return validatorResult;
    }
}
