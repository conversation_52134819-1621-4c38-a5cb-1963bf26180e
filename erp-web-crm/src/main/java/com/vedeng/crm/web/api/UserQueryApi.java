package com.vedeng.crm.web.api;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.crm.web.api.dto.UserQueryApiDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.uac.api.dto.OrganizationResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nullable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/17
 */
@ExceptionController
@RestController
@RequestMapping(value = {"/crm/user/profile", "/crm/user/m"})
@Slf4j
public class UserQueryApi {


    @Autowired
    private UserApiService userApiService;

    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;

    /**
     * 返回全部用户的信息
     *
     * @return
     */
    @RequestMapping("/getAllUserInfo")
    public R<List<UserDto>> getAllUserInfo(@Nullable @RequestBody UserQueryApiDto userQueryApiDto) {
        String name = (userQueryApiDto != null) ? userQueryApiDto.getName() : "";
        List<UserDto> userList = userApiService.getAllUserNotDisabled(name);
        return R.success(userList);
    }

    @RequestMapping("/getFullDepartmentTree")
    public R<OrganizationResDTO> getFullDepartmentTree() {
        RestfulResult<OrganizationResDTO> result = uacWxUserInfoApiService.getFullDepartmentTree();
        if (result.isSuccess()) {
            return R.success(result.getData());
        }
        return R.success(new OrganizationResDTO());
    }


}
