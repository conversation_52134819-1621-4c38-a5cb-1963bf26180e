package com.newtask.data.saleorderdata;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.report.dao.SaleorderDataSelectMapper;
import com.newtask.data.dto.SaleoderDataDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 初始化订单是否自动审核
 */
@Component
public class SaleorderAutoCheckSync extends AbstractDataSync {

    @Resource
    SaleorderDataMapper saleorderDataMapper;
    @Resource
    SaleorderDataSelectMapper saleorderDataSelectMapper;
    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {

        List<Integer> saleorderIdsList = saleorderDataSelectMapper.getSaleorderIdsPreTime(startTime, endTime);

        return saleorderIdsList;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> autoCheck = null;
        if (CollectionUtils.isNotEmpty(bizIds)) {
            autoCheck = saleorderDataSelectMapper.findAutoCheckBySaleorderId(bizIds);
        }
        return autoCheck;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            long timeMillis = System.currentTimeMillis();
            List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
            dataList.forEach(item -> {
                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                Integer saleorderId = NumberUtils.toInt(item.get("saleorderId").toString());
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setAutoCheck(NumberUtils.toLong(item.get("autoCheck")+""));
                saleoderDataDtos.add(saleoderDataDto);
                // 判断saleorderId是否存在
                makeExist(saleorderId, timeMillis);
            });
            // 根据id修改firstPayTime
            saleorderDataMapper.updateSaleorderAutoCheck(saleoderDataDtos, timeMillis);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {

        Long count = saleorderDataMapper.getSaleorderById(id);
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }

    }
}
