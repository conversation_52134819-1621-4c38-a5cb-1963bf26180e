<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceVoucherMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE_VOUCHER-->
    <id column="INVOICE_VOUCHER_ID" jdbcType="BIGINT" property="invoiceVoucherId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="INVOICE_ID" jdbcType="VARCHAR" property="invoiceId" />
    <result column="VOUCHER_NO" jdbcType="VARCHAR" property="voucherNo" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="VOUCHER_DATE" jdbcType="VARCHAR" property="voucherDate" />
    <result column="VOUCHER_URL" jdbcType="VARCHAR" property="voucherUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INVOICE_VOUCHER_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    INVOICE_ID, VOUCHER_NO, IS_DELETE, VOUCHER_DATE, VOUCHER_URL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_INVOICE_VOUCHER
    where INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_INVOICE_VOUCHER
    where INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="INVOICE_VOUCHER_ID" keyProperty="invoiceVoucherId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_VOUCHER (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      INVOICE_ID, VOUCHER_NO, IS_DELETE, 
      VOUCHER_DATE, VOUCHER_URL)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{invoiceId,jdbcType=VARCHAR}, #{voucherNo,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, 
      #{voucherDate,jdbcType=VARCHAR}, #{voucherUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="INVOICE_VOUCHER_ID" keyProperty="invoiceVoucherId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_VOUCHER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="invoiceId != null and invoiceId != ''">
        INVOICE_ID,
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        VOUCHER_NO,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        VOUCHER_DATE,
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        VOUCHER_URL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null and invoiceId != ''">
        #{invoiceId,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        #{voucherDate,jdbcType=VARCHAR},
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        #{voucherUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity">
    <!--@mbg.generated-->
    update T_INVOICE_VOUCHER
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null and invoiceId != ''">
        INVOICE_ID = #{invoiceId,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR},
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT}
  </update>

  <update id="updateFileUrlByVoucherUrlAndVoucherNo" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity">
    update T_INVOICE_VOUCHER
    set
        MOD_TIME = NOW(),
        VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR}
    where VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR} AND VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity">
    <!--@mbg.generated-->
    update T_INVOICE_VOUCHER
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      INVOICE_ID = #{invoiceId,jdbcType=VARCHAR},
      VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR},
      VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR}
    where INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE_VOUCHER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.invoiceId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="VOUCHER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.voucherNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="VOUCHER_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.voucherDate,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="VOUCHER_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.voucherUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where INVOICE_VOUCHER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceVoucherId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE_VOUCHER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceId != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.invoiceId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="VOUCHER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voucherNo != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.voucherNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VOUCHER_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voucherDate != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.voucherDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="VOUCHER_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voucherUrl != null">
            when INVOICE_VOUCHER_ID = #{item.invoiceVoucherId,jdbcType=BIGINT} then #{item.voucherUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where INVOICE_VOUCHER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceVoucherId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="INVOICE_VOUCHER_ID" keyProperty="invoiceVoucherId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_VOUCHER
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, INVOICE_ID, VOUCHER_NO, 
      IS_DELETE, VOUCHER_DATE, VOUCHER_URL)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.invoiceId,jdbcType=VARCHAR}, #{item.voucherNo,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, 
        #{item.voucherDate,jdbcType=VARCHAR}, #{item.voucherUrl,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="INVOICE_VOUCHER_ID" keyProperty="invoiceVoucherId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_VOUCHER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceVoucherId != null">
        INVOICE_VOUCHER_ID,
      </if>
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
      INVOICE_ID,
      VOUCHER_NO,
      IS_DELETE,
      VOUCHER_DATE,
      VOUCHER_URL,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceVoucherId != null">
        #{invoiceVoucherId,jdbcType=BIGINT},
      </if>
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
      #{invoiceId,jdbcType=VARCHAR},
      #{voucherNo,jdbcType=VARCHAR},
      #{isDelete,jdbcType=INTEGER},
      #{voucherDate,jdbcType=VARCHAR},
      #{voucherUrl,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="invoiceVoucherId != null">
        INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT},
      </if>
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      INVOICE_ID = #{invoiceId,jdbcType=VARCHAR},
      VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR},
      VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="INVOICE_VOUCHER_ID" keyProperty="invoiceVoucherId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_VOUCHER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceVoucherId != null">
        INVOICE_VOUCHER_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="invoiceId != null and invoiceId != ''">
        INVOICE_ID,
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        VOUCHER_NO,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        VOUCHER_DATE,
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        VOUCHER_URL,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceVoucherId != null">
        #{invoiceVoucherId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null and invoiceId != ''">
        #{invoiceId,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        #{voucherDate,jdbcType=VARCHAR},
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        #{voucherUrl,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="invoiceVoucherId != null">
        INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null and invoiceId != ''">
        INVOICE_ID = #{invoiceId,jdbcType=VARCHAR},
      </if>
      <if test="voucherNo != null and voucherNo != ''">
        VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="voucherDate != null and voucherDate != ''">
        VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR},
      </if>
      <if test="voucherUrl != null and voucherUrl != ''">
        VOUCHER_URL = #{voucherUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="findByInvoiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_INVOICE_VOUCHER
    where INVOICE_ID=#{invoiceId,jdbcType=VARCHAR}
  </select>

  <select id="getByVoucherDateAndNo" resultMap="BaseResultMap">
    select * from T_INVOICE_VOUCHER where VOUCHER_DATE = #{voucherDate,jdbcType=VARCHAR}
    and VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR}
    LIMIT 1
    </select>
</mapper>