<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastStatisticsMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.common.broadcast.statistics.StatisticsDto">
        <result column="orgId" jdbcType="INTEGER" property="orgId"/>
        <result column="userId" jdbcType="INTEGER" property="userId"/>
        <result column="totalAmount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="totalNum" jdbcType="INTEGER" property="totalNum"/>
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
        <result column="teamName" jdbcType="VARCHAR" property="teamName"/>
        <result column="teamId" jdbcType="INTEGER" property="teamId"/>
        <result column="deptName" jdbcType="VARCHAR" property="deptName"/>
        <result column="deptId" jdbcType="INTEGER" property="deptId"/>
    </resultMap>


    <select id="selectStatisticsAmountByPersonParams"  resultMap="BaseResultMap">
		
		select 
			sum(tab.totalAmount) totalAmount,
			tab.orgId,
            tab.userId userId,
			tu.USERNAME userName,
			tbd.DEPT_NAME teamName,
			tbd.ID teamId,
			tbd2.DEPT_NAME deptName,
			tbd2.ID deptId
		from  
			( SELECT sum(amount) totalAmount,orgId,userId
			  FROM 
			    (   #到款金额
					SELECT sum(TCBD.AMOUNT) amount, TCBD.ORG_ID orgId, TCBD.USER_ID userId
					FROM T_CAPITAL_BILL TCB
					LEFT JOIN T_CAPITAL_BILL_DETAIL TCBD ON TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID
					WHERE 1=1
							AND TCBD.ORDER_TYPE = 1
							AND TCBD.BUSSINESS_TYPE IN (526,533)
							AND TCBD.TRADER_TYPE IN (1,4)
							AND TCB.TRADER_MODE IN (520, 521, 522, 523, 528)
							AND TCB.PAYER NOT IN ('支付宝（中国）网络技术有限公司','财付通支付科技有限公司')
			                #时间配置 
			                and TCB.TRADER_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
							#ERP组织ID配置，小组、部门统计时，无需进行对org_id进行分组，只有计算个人时才需要org_id进行分组 
							#不存在需要添加的自定义销售
							<if test="(orgIdList != null and orgIdList.size() > 0) and (inUserIdList == null or inUserIdList.size()==0)">
								and TCBD.ORG_ID in 
								<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			      					#{orgId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
							#存在自定义添加的销售 添加自定义销售
							<if test="orgIdList != null and orgIdList.size() > 0 and inUserIdList != null and inUserIdList.size() > 0">
								and (TCBD.ORG_ID in 
								<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			      					#{orgId,jdbcType=INTEGER}
			      				</foreach>
			      				
			      				or (TCBD.USER_ID in
									<foreach collection="inUserIdList" item="userId" open="(" close=")" separator=",">
				      					#{userId,jdbcType=INTEGER}
				      				</foreach>
				      				<if test="inUserIdBelongOrgIdList != null and inUserIdBelongOrgIdList.size() > 0 ">
					      				AND TCBD.ORG_ID in
										<foreach collection="inUserIdBelongOrgIdList" item="inUserIdBelongOrgId" open="(" close=")" separator=",">
					      					#{inUserIdBelongOrgId,jdbcType=INTEGER}
					      				</foreach>
				      				</if>
				      				)
			      				)
							</if>
							
							#过滤公司 
							<if test="excludeTraderIds != null and excludeTraderIds.size() > 0">
								and TCBD.TRADER_ID not in 
								<foreach collection="excludeTraderIds" item="traderId" open="(" close=")" separator=",">
			      					#{traderId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							#过滤销售 
							<if test="excludeSaleIds != null and excludeSaleIds.size() > 0">
								and TCBD.USER_ID not in 
								<foreach collection="excludeSaleIds" item="saleId" open="(" close=")" separator=",">
			      					#{saleId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							<if test="outUserIdList != null and outUserIdList.size() > 0">
								and TCBD.USER_ID not in 
								<foreach collection="outUserIdList" item="userId" open="(" close=")" separator=",">
			      					#{userId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
					GROUP BY  TCBD.ORG_ID,TCBD.USER_ID
					
					UNION all 
			        
					#退款金额
					SELECT sum(TCBD.AMOUNT) amount,TCBD.ORG_ID orgId,TCBD.USER_ID userId
					FROM T_CAPITAL_BILL TCB
					LEFT JOIN T_CAPITAL_BILL_DETAIL TCBD ON TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES TAS ON TCBD.ORDER_NO = TAS.AFTER_SALES_NO AND TAS.TYPE IN (539,543) AND TCBD.ORDER_TYPE =3
					WHERE 1=1
							AND TCBD.BUSSINESS_TYPE = 531
							AND TCB.TRADER_MODE =530
							AND TCB.TRADER_TYPE IN (2,5)
			                #时间配置 
			                and TCB.TRADER_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
							#ERP组织ID配置，小组、部门统计时，无需进行对org_id进行分组，只有计算个人时才需要org_id进行分组 
							
							#不存在需要添加的自定义销售
							<if test="(orgIdList != null and orgIdList.size() > 0) and (inUserIdList == null or inUserIdList.size()==0)">
								and TCBD.ORG_ID in 
								<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			      					#{orgId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
							#存在自定义添加的销售 添加自定义销售
							<if test="orgIdList != null and orgIdList.size() > 0 and inUserIdList != null and inUserIdList.size() > 0">
								and (TCBD.ORG_ID in 
								<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			      					#{orgId,jdbcType=INTEGER}
			      				</foreach>
			      				
			      				or (TCBD.USER_ID in
									<foreach collection="inUserIdList" item="userId" open="(" close=")" separator=",">
				      					#{userId,jdbcType=INTEGER}
				      				</foreach>
				      				<if test="inUserIdBelongOrgIdList != null and inUserIdBelongOrgIdList.size() > 0 ">
					      				AND TCBD.ORG_ID in
										<foreach collection="inUserIdBelongOrgIdList" item="inUserIdBelongOrgId" open="(" close=")" separator=",">
					      					#{inUserIdBelongOrgId,jdbcType=INTEGER}
					      				</foreach>
				      				</if>
				      				)
			      				)
							</if>
							
							
							#过滤公司 
							<if test="excludeTraderIds != null and excludeTraderIds.size() > 0">
								and TCBD.TRADER_ID not in 
								<foreach collection="excludeTraderIds" item="traderId" open="(" close=")" separator=",">
			      					#{traderId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							#过滤销售 
							<if test="excludeSaleIds != null and excludeSaleIds.size() > 0">
								and TCBD.USER_ID not in 
								<foreach collection="excludeSaleIds" item="saleId" open="(" close=")" separator=",">
			      					#{saleId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							<if test="outUserIdList != null and outUserIdList.size() > 0">
								and TCBD.USER_ID not in 
								<foreach collection="outUserIdList" item="userId" open="(" close=")" separator=",">
			      					#{userId,jdbcType=INTEGER}
			      				</foreach>
							</if>
					GROUP BY  TCBD.ORG_ID ,TCBD.USER_ID
					) tab
				GROUP BY  orgId,userId
				) tab 
			LEFT JOIN  T_USER tu on tab.userId = tu.USER_ID
			LEFT JOIN  T_BROADCAST_DEPT_R_ERP_DEPT tbdred on tab.orgId = tbdred.ERP_DEPT_ID 
			LEFT JOIN  T_BROADCAST_DEPT tbd on tbd.ID = tbdred.BROADCAST_DEPT_ID
			LEFT JOIN  T_BROADCAST_DEPT tbd2 on tbd.PARENT_ID = tbd2.ID
			where tbdred.IS_DELETED =0 and tbd.IS_DELETED =0 and tbd2.IS_DELETED =0 and tu.IS_DISABLED=0
			GROUP BY userId ORDER BY   totalAmount DESC
        
    </select>
    
    <select id="selectStatisticsAmountByTeamParams"  resultMap="BaseResultMap">
		
		 SELECT sum(amount) totalAmount
			  FROM 
			    (   #到款金额
					SELECT sum(TCBD.AMOUNT) amount
					FROM T_CAPITAL_BILL TCB
					LEFT JOIN T_CAPITAL_BILL_DETAIL TCBD ON TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID
					WHERE 1=1
							AND TCBD.ORDER_TYPE = 1
							AND TCBD.BUSSINESS_TYPE IN (526,533)
							AND TCBD.TRADER_TYPE IN (1,4)
							AND TCB.TRADER_MODE IN (520, 521, 522, 523, 528)
							AND TCB.PAYER NOT IN ('支付宝（中国）网络技术有限公司','财付通支付科技有限公司')
			                #时间配置 
			                and TCB.TRADER_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
			                
							#不存在需要添加的自定义销售
							<if test="(orgIdList != null and orgIdList.size() > 0) and (inUserIdList == null or inUserIdList.size()==0)">
								and TCBD.ORG_ID in 
								<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			      					#{orgId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
							#存在自定义添加的销售 添加自定义销售
							<if test="orgIdList != null and orgIdList.size() > 0 and inUserIdList != null and inUserIdList.size() > 0">
								and (TCBD.ORG_ID in 
								<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			      					#{orgId,jdbcType=INTEGER}
			      				</foreach>
			      				
			      				or (TCBD.USER_ID in
									<foreach collection="inUserIdList" item="userId" open="(" close=")" separator=",">
				      					#{userId,jdbcType=INTEGER}
				      				</foreach>
				      				<if test="inUserIdBelongOrgIdList != null and inUserIdBelongOrgIdList.size() > 0 ">
					      				AND TCBD.ORG_ID in
										<foreach collection="inUserIdBelongOrgIdList" item="inUserIdBelongOrgId" open="(" close=")" separator=",">
					      					#{inUserIdBelongOrgId,jdbcType=INTEGER}
					      				</foreach>
				      				</if>
				      				)
			      				)
							</if>
							
							#排除自定义销售
							<if test="outUserIdList != null and outUserIdList.size() > 0">
								and TCBD.USER_ID not in 
								<foreach collection="outUserIdList" item="userId" open="(" close=")" separator=",">
			      					#{userId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
							#过滤销售 
							<if test="excludeSaleIds != null and excludeSaleIds.size() > 0">
								and TCBD.USER_ID not in 
								<foreach collection="excludeSaleIds" item="saleId" open="(" close=")" separator=",">
			      					#{saleId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
							#过滤公司 
							<if test="excludeTraderIds != null and excludeTraderIds.size() > 0">
								and TCBD.TRADER_ID not in 
								<foreach collection="excludeTraderIds" item="traderId" open="(" close=")" separator=",">
			      					#{traderId,jdbcType=INTEGER}
			      				</foreach>
							</if>
					
					UNION all 
			        
					#退款金额
					SELECT sum(TCBD.AMOUNT) amount
					FROM T_CAPITAL_BILL TCB
					LEFT JOIN T_CAPITAL_BILL_DETAIL TCBD ON TCB.CAPITAL_BILL_ID = TCBD.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES TAS ON TCBD.ORDER_NO = TAS.AFTER_SALES_NO AND TAS.TYPE IN (539,543) AND TCBD.ORDER_TYPE =3
					WHERE 1=1
							AND TCBD.BUSSINESS_TYPE = 531
							AND TCB.TRADER_MODE =530
							AND TCB.TRADER_TYPE IN (2,5)
			                #时间配置 
			                and TCB.TRADER_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
			                
							#不存在需要添加的自定义销售
							<if test="(orgIdList != null and orgIdList.size() > 0) and (inUserIdList == null or inUserIdList.size()==0)">
								and TCBD.ORG_ID in 
								<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			      					#{orgId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
							#存在自定义添加的销售 添加自定义销售
							<if test="orgIdList != null and orgIdList.size() > 0 and inUserIdList != null and inUserIdList.size() > 0">
								and (TCBD.ORG_ID in 
								<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
			      					#{orgId,jdbcType=INTEGER}
			      				</foreach>
			      				
			      				or (TCBD.USER_ID in
								<foreach collection="inUserIdList" item="userId" open="(" close=")" separator=",">
			      					#{userId,jdbcType=INTEGER}
			      				</foreach>
			      				<if test="inUserIdBelongOrgIdList != null and inUserIdBelongOrgIdList.size() > 0 ">
				      				AND TCBD.ORG_ID in
									<foreach collection="inUserIdBelongOrgIdList" item="inUserIdBelongOrgId" open="(" close=")" separator=",">
				      					#{inUserIdBelongOrgId,jdbcType=INTEGER}
				      				</foreach>
				      			</if>
			      				)
			      				)
							</if>
							
							#排除自定义销售
							<if test="outUserIdList != null and outUserIdList.size() > 0">
								and TCBD.USER_ID not in 
								<foreach collection="outUserIdList" item="userId" open="(" close=")" separator=",">
			      					#{userId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
							#过滤销售 
							<if test="excludeSaleIds != null and excludeSaleIds.size() > 0">
								and TCBD.USER_ID not in 
								<foreach collection="excludeSaleIds" item="saleId" open="(" close=")" separator=",">
			      					#{saleId,jdbcType=INTEGER}
			      				</foreach>
							</if>
							
							#过滤公司 
							<if test="excludeTraderIds != null and excludeTraderIds.size() > 0">
								and TCBD.TRADER_ID not in 
								<foreach collection="excludeTraderIds" item="traderId" open="(" close=")" separator=",">
			      					#{traderId,jdbcType=INTEGER}
			      				</foreach>
							</if>
					) tab
				ORDER BY   totalAmount DESC
        
    </select>
    
    <select id="selectStatisticsAedWarehouse"  resultMap="BaseResultMap">
		
		#查询出库量，业务需求场景，如果一个用户有2个部门，只要有一个部门属于营销中心的末级部门，该用户数据算作出库量
		#例如：用户属于erp组织A、组织B
		#组织AB都不属于营销中心的末级部门，该用户的数据不算
		#组织A属于营销中心的末级部门，B不属于，但该用户在组织AB下的数据都算，播报的时候，用户归属组为组织A所在的组
		
		SELECT 
		    SUM(num) totalNum, userId,userName
		FROM
		    (   #出库数量
				SELECT  IFNULL(SUM(ABS(twgoii.NUM)), 0) num, trtju.USER_ID userId,t.USERNAME userName
				FROM T_WAREHOUSE_GOODS_OUT_IN twgoi
				LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM twgoii ON twgoi.OUT_IN_NO = twgoii.OUT_IN_NO
				LEFT JOIN T_SALEORDER_GOODS tsg ON twgoii.RELATED_ID = tsg.SALEORDER_GOODS_ID
		        LEFT JOIN V_CORE_SKU A ON tsg.GOODS_ID = A.SKU_ID
		        LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
				LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
				LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
				LEFT JOIN T_SALEORDER ts ON ts.SALEORDER_ID = tsg.SALEORDER_ID
				LEFT JOIN T_TRADER tt ON tt.TRADER_ID = ts.TRADER_ID
				LEFT JOIN T_R_TRADER_J_USER trtju ON trtju.TRADER_ID = tt.TRADER_ID
				LEFT JOIN T_USER t on t.USER_ID = trtju.USER_ID
				WHERE
					twgoi.OUT_IN_TYPE = 2 and t.IS_DISABLED=0 and twgoi.IS_VIRTUAL = 0
					#时间配置 
					and twgoi.OUT_IN_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and twgoi.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
					#SKU的为"播报配置 - AED商品  
					<if test="aedSkuIds != null and aedSkuIds.size() > 0 and isUserDefine==0">
						and twgoii.GOODS_ID in 
						<foreach collection="aedSkuIds" item="aedSkuId" open="(" close=")" separator=",">
	      					#{aedSkuId,jdbcType=INTEGER}
	      				</foreach>
					</if>
		            #自定义商品 
		            <if test="statSkuIds != null and statSkuIds.size() > 0 and isUserDefine==1">
						and twgoii.GOODS_ID in 
						<foreach collection="statSkuIds" item="statSkuId" open="(" close=")" separator=",">
	      					#{statSkuId,jdbcType=INTEGER}
	      				</foreach>
					</if>
		            #自定义品牌
					<if test="statBrandIds != null and statBrandIds.size() > 0 and isUserDefine==1">
						and E.BRAND_ID in
						<foreach collection="statBrandIds" item="statBrandId" open="(" close=")" separator=",">
	      					#{statBrandId,jdbcType=INTEGER}
	      				</foreach>
					</if>
           			#自定义分类 
           			<if test="statCategoryIds != null and statCategoryIds.size() > 0 and isUserDefine==1">
						and CB.BASE_CATEGORY_ID in
						<foreach collection="statCategoryIds" item="statCategoryId" open="(" close=")" separator=",">
	      					#{statCategoryId,jdbcType=INTEGER}
	      				</foreach>
					</if>
		            #过滤销售 
					<if test="excludeSaleIds != null and excludeSaleIds.size() > 0">
						and trtju.USER_ID not in 
						<foreach collection="excludeSaleIds" item="saleId" open="(" close=")" separator=",">
	      					#{saleId,jdbcType=INTEGER}
	      				</foreach>
					</if>
					#排除自定义和添加自定义销售,改用代码处理
					
				GROUP BY trtju.USER_ID 
		        
		    UNION ALL 
		    
		        #退货入库数量
				SELECT  IFNULL(- SUM(ABS(twgoii.NUM)), 0) num, trtju.USER_ID userId,t.USERNAME userName
				FROM T_WAREHOUSE_GOODS_OUT_IN twgoi
				LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM twgoii ON twgoi.OUT_IN_NO = twgoii.OUT_IN_NO
		        LEFT JOIN V_CORE_SKU A ON twgoii.GOODS_ID = A.SKU_ID
		        LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
				LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
				LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
				LEFT JOIN T_AFTER_SALES tas ON twgoi.RELATE_NO = tas.AFTER_SALES_NO
				LEFT JOIN T_SALEORDER ts ON ts.SALEORDER_NO = tas.ORDER_NO
				LEFT JOIN T_TRADER tt ON tt.TRADER_ID = ts.TRADER_ID
				LEFT JOIN T_R_TRADER_J_USER trtju ON trtju.TRADER_ID = tt.TRADER_ID
				LEFT JOIN T_USER t on t.USER_ID = trtju.USER_ID
				WHERE
					twgoi.OUT_IN_TYPE = 5 and t.IS_DISABLED=0 and twgoi.IS_VIRTUAL = 0
					#时间配置 
					and twgoi.OUT_IN_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and twgoi.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
					#SKU的为"播报配置 - AED商品  
					<if test="aedSkuIds != null and aedSkuIds.size() > 0  and isUserDefine==0">
						and twgoii.GOODS_ID in 
						<foreach collection="aedSkuIds" item="aedSkuId" open="(" close=")" separator=",">
	      					#{aedSkuId,jdbcType=INTEGER}
	      				</foreach>
					</if>
		            #自定义商品 
		            <if test="statSkuIds != null and statSkuIds.size() > 0 and isUserDefine==1">
						and twgoii.GOODS_ID in 
						<foreach collection="statSkuIds" item="statSkuId" open="(" close=")" separator=",">
	      					#{statSkuId,jdbcType=INTEGER}
	      				</foreach>
					</if>
		            #自定义品牌
					<if test="statBrandIds != null and statBrandIds.size() > 0 and isUserDefine==1">
						and E.BRAND_ID in
						<foreach collection="statBrandIds" item="statBrandId" open="(" close=")" separator=",">
	      					#{statBrandId,jdbcType=INTEGER}
	      				</foreach>
					</if>
           			#自定义分类 
           			<if test="statCategoryIds != null and statCategoryIds.size() > 0 and isUserDefine==1">
						and CB.BASE_CATEGORY_ID in
						<foreach collection="statCategoryIds" item="statCategoryId" open="(" close=")" separator=",">
	      					#{statCategoryId,jdbcType=INTEGER}
	      				</foreach>
					</if>
		            #过滤销售 
					<if test="excludeSaleIds != null and excludeSaleIds.size() > 0">
						and trtju.USER_ID not in 
						<foreach collection="excludeSaleIds" item="saleId" open="(" close=")" separator=",">
	      					#{saleId,jdbcType=INTEGER}
	      				</foreach>
					</if>
					#排除自定义和添加自定义销售，改用代码处理
				GROUP BY trtju.USER_ID
			) tab
		GROUP BY userId
		ORDER BY totalNum DESC
    </select>
    
    <select id="selectStatisticsAedAmount"  resultMap="BaseResultMap">
		
		#查询出库金额，业务需求场景，如果一个用户有2个部门，只要有一个部门属于营销中心的末级部门，该用户数据算作出库金额
		#例如：用户属于erp组织A、组织B
		#组织AB都不属于营销中心的末级部门，该用户的数据不算
		#组织A属于营销中心的末级部门，B不属于，但该用户在组织AB下的数据都算，播报的时候，用户归属组为组织A所在的组
		
		SELECT 
		    SUM(amount) totalAmount, userId,userName
		FROM
		    (
				SELECT  IFNULL(ROUND(SUM(amount),2), 0) amount, userId,userName, goodsId
				FROM 
				  ( #出库金额
					SELECT  (ABS(twgoii.NUM) * tsg.PRICE) amount, trtju.USER_ID userId,t.USERNAME userName, tsg.GOODS_ID goodsId
					FROM T_WAREHOUSE_GOODS_OUT_IN twgoi
					LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM twgoii ON twgoi.OUT_IN_NO = twgoii.OUT_IN_NO
					LEFT JOIN T_SALEORDER_GOODS tsg ON twgoii.RELATED_ID = tsg.SALEORDER_GOODS_ID
		            LEFT JOIN V_CORE_SKU A ON tsg.GOODS_ID = A.SKU_ID
					LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
					LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
					LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
					LEFT JOIN T_SALEORDER ts ON ts.SALEORDER_ID = tsg.SALEORDER_ID
					LEFT JOIN T_TRADER tt ON tt.TRADER_ID = ts.TRADER_ID
					LEFT JOIN T_R_TRADER_J_USER trtju ON trtju.TRADER_ID = tt.TRADER_ID
					LEFT JOIN T_USER t on t.USER_ID = trtju.USER_ID
					WHERE
						twgoi.OUT_IN_TYPE = 2 and t.IS_DISABLED=0 and twgoi.IS_VIRTUAL = 0
		                #时间配置
		                and twgoi.OUT_IN_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and twgoi.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
						#非自定义才会有自有品牌条件
						<if test="isUserDefine==0">
							and  E.IS_OWN_BRAND =1 
						</if>
						#自定义商品
			            <if test="statSkuIds != null and statSkuIds.size() > 0 and isUserDefine==1">
							and twgoii.GOODS_ID in 
							<foreach collection="statSkuIds" item="statSkuId" open="(" close=")" separator=",">
		      					#{statSkuId,jdbcType=INTEGER}
		      				</foreach>
						</if>
			            #自定义品牌
						<if test="statBrandIds != null and statBrandIds.size() > 0 and isUserDefine==1">
							and E.BRAND_ID in
							<foreach collection="statBrandIds" item="statBrandId" open="(" close=")" separator=",">
		      					#{statBrandId,jdbcType=INTEGER}
		      				</foreach>
						</if>
	           			#自定义分类 
	           			<if test="statCategoryIds != null and statCategoryIds.size() > 0 and isUserDefine==1">
							and CB.BASE_CATEGORY_ID in
							<foreach collection="statCategoryIds" item="statCategoryId" open="(" close=")" separator=",">
		      					#{statCategoryId,jdbcType=INTEGER}
		      				</foreach>
						</if>
						#过滤销售 
						<if test="excludeSaleIds != null and excludeSaleIds.size() > 0">
							and trtju.USER_ID not in 
							<foreach collection="excludeSaleIds" item="saleId" open="(" close=")" separator=",">
		      					#{saleId,jdbcType=INTEGER}
		      				</foreach>
						</if>
						#排除自定义和添加自定义销售，改用代码处理
					) tab
				GROUP BY userId , goodsId 
		        
		        UNION ALL 
		        
		        SELECT IFNULL(- ROUND(SUM(amount),2), 0) amount, userId,userName, goodsId
				FROM
		        (   #退货入库金额
					SELECT (ABS(twgoii.NUM) * tsg.PRICE) amount, trtju.USER_ID userId,t.USERNAME userName, tsg.GOODS_ID goodsId
					FROM T_WAREHOUSE_GOODS_OUT_IN twgoi
					LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM twgoii ON twgoi.OUT_IN_NO = twgoii.OUT_IN_NO
		            LEFT JOIN V_CORE_SKU A ON twgoii.GOODS_ID = A.SKU_ID
					LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
					LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
					LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
					LEFT JOIN T_AFTER_SALES tas ON twgoi.RELATE_NO = tas.AFTER_SALES_NO
					LEFT JOIN T_AFTER_SALES_GOODS tasg ON twgoii.RELATED_ID = tasg.AFTER_SALES_GOODS_ID
					LEFT JOIN T_SALEORDER_GOODS tsg ON tsg.SALEORDER_GOODS_ID = tasg.ORDER_DETAIL_ID
					LEFT JOIN T_SALEORDER ts ON ts.SALEORDER_ID = tsg.SALEORDER_ID
					LEFT JOIN T_TRADER tt ON tt.TRADER_ID = ts.TRADER_ID
					LEFT JOIN T_R_TRADER_J_USER trtju ON trtju.TRADER_ID = tt.TRADER_ID
					LEFT JOIN T_USER t on t.USER_ID = trtju.USER_ID
					WHERE
						twgoi.OUT_IN_TYPE = 5  and t.IS_DISABLED=0 and twgoi.IS_VIRTUAL = 0
						#时间配置
		                and twgoi.OUT_IN_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and twgoi.OUT_IN_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
						#非自定义才会有自有品牌条件
						<if test="isUserDefine==0">
							and  E.IS_OWN_BRAND =1 
						</if>
						#自定义商品
			            <if test="statSkuIds != null and statSkuIds.size() > 0 and isUserDefine==1">
							and twgoii.GOODS_ID in 
							<foreach collection="statSkuIds" item="statSkuId" open="(" close=")" separator=",">
		      					#{statSkuId,jdbcType=INTEGER}
		      				</foreach>
						</if>
			            #自定义品牌
						<if test="statBrandIds != null and statBrandIds.size() > 0 and isUserDefine==1">
							and E.BRAND_ID in
							<foreach collection="statBrandIds" item="statBrandId" open="(" close=")" separator=",">
		      					#{statBrandId,jdbcType=INTEGER}
		      				</foreach>
						</if>
	           			#自定义分类 
	           			<if test="statCategoryIds != null and statCategoryIds.size() > 0 and isUserDefine==1">
							and CB.BASE_CATEGORY_ID in
							<foreach collection="statCategoryIds" item="statCategoryId" open="(" close=")" separator=",">
		      					#{statCategoryId,jdbcType=INTEGER}
		      				</foreach>
						</if>
						#过滤销售 
						<if test="excludeSaleIds != null and excludeSaleIds.size() > 0">
							and trtju.USER_ID not in 
							<foreach collection="excludeSaleIds" item="saleId" open="(" close=")" separator=",">
		      					#{saleId,jdbcType=INTEGER}
		      				</foreach>
						</if>
						#排除自定义和添加自定义销售，改用代码处理
						) tab
				GROUP BY userId , goodsId) tab
		GROUP BY userId ORDER BY totalAmount DESC
    </select>
    

</mapper>
