package com.wms.dao;

import com.wms.model.po.WmsInputOrder;
import com.wms.model.po.WmsSurplusInOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WmsInputOrderMapper {
    int deleteByPrimaryKey(Integer wmsInputOrderId);

    int insert(WmsInputOrder record);

    int insertSelective(WmsInputOrder record);

    WmsInputOrder selectByPrimaryKey(Integer wmsInputOrderId);

    int updateByPrimaryKeySelective(WmsInputOrder record);

    int updateByPrimaryKey(WmsInputOrder record);

    /**
     * @description: 列表查询
     * @return: List<WmsSurplusInOrder>
     * @author: Strange
     * @date: 2020/9/21
     **/
    List<WmsSurplusInOrder> querySurplusInlistPage(Map<String, Object> map);

    /**
     * @description: 单号获取入库单信息
     * @return: WmsInputOrder
     * @author: Strange
     * @date: 2020/9/22
     **/
    WmsInputOrder getWmsInputOrderByNo(String surplusNo);

    /**
     * sku（审核状态为待审核，审核中，审核通过，且入库状态为未入库，部分入库）的盘盈入库单
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/9/1 10:45.
     * @author: Randy.Xu.
     * @param skuNo
     * @return: java.util.List<com.wms.model.po.WmsInputOrder>.
     * @throws:  .
     */
    List<WmsInputOrder> getValidAndUnInInpuorderBySkuNos(@Param("skuNo") String skuNo);

    /**
     * 盘盈入库单
     *
     * @param relateNo
     * @param orderType
     * @return
     */
    WmsInputOrder getWmsOutputOrderByOrderNoAndType(@Param("relateNo") String relateNo, @Param("orderType") Integer orderType);
}