<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>CRM-线索</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/businessLeads.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>

    <jsp:include page="../common/head_import.jsp?staticResourceVersion=${requestScope.staticResourceVersion}"></jsp:include>

    <c:choose>
        <c:when test="${requestScope.pageType == '1'}">
            <%-- 此变量表示从线索商机页面新建，提交请求指向/crm/businessLeads/profile/addForSale -post --%>
            <input type="hidden" id="pageType" value="1">
        </c:when>
        <c:otherwise>
            <%-- 此变量为0时，表示总机新建线索，提交请求指向/crm/businessLeads/profile/add -post --%>
            <input type="hidden" id="pageType" value="0">
        </c:otherwise>
    </c:choose>

    <input type="hidden" id="isHiddenLayout" value="1">
    <!-- 线索id 仅编辑 -->
    <input type="hidden" id="businessLeadsId" value="${param.id}" />


    <!-- IM跳转过程中的参数 -->
    <!-- 客户ID和客户名称 -->
    <input type="hidden" id="traderId" value="${param.traderId}" />
    <input type="hidden" id="traderName" value="${traderName}" />
    <input type="hidden" id="mobile" value="${param.mobile}" />

    <!--询价行为 -->
    <input type="hidden" id="inquiryId" value="${inquiryId}" />

    <!-- 二级联动的渠道 sourceId为第一层，communicationId为第二层 -->
    <input type="hidden" id="sourceId" value="${sourceId}" />
    <input type="hidden" id="communicationId" value="${communicationId}" />

    <!--IM带过来的归属销售  -->
    <input type="hidden" id="belongerId" value="${belongerId}" />
    <input type="hidden" id="belonger" value="${belonger}" />
    <input type="hidden" id="belongerPic" value="${belongerPic}" />


    <div class="page-wrap" id="page-container" style="padding-bottom: 40px;">
        <page-header></page-header>
        <div class="page-container">
            <div class="page-main">

                <div class="businessLeads-add-container" >

                    <div class="top-featurn" :style="{'top': fixedTop+'px'}">
                        <div class="inner-header">
                            <h1 class="inner-header-title">{{ isEdit? '编辑线索': '新增线索' }}</h1>
                            <ui-button @click="submit" type="primary">提交</ui-button>
                        </div>
                    </div>

                    <div class="form-wrap" style="padding: 10px 20px 20px 20px;" v-if="!pageLoading">
                        <div class="card">
                            <div class="card-title">线索来源</div>
                            <div>
                                <ui-form-item label="线索类型" :must="true" :text="true">
                                    <div class="ui-col-6">
                                        <div>{{ clueTypeName }}</div>
                                    </div>
                                </ui-form-item>
                                <ui-form-item label="企微提醒" :must="true" :text="true">
                                    <div class="ui-col-12">
                                        <ui-radio-group
                                            :list="radioList1"
                                            :value.sync="sendVx"
                                            :disabled="isEdit"
                                        ></ui-radio-group>
                                        <p class="form-tip" v-show="sendVx == 'Y'">- 当选择“提醒”时会触发，企业微信卡片消息提醒，提醒人“归属销售”</p>
                                    </div>
                                </ui-form-item>
                                <ui-form-item v-show="clueType != 394" label="询价行为" :must="true" :text="true">
                                    <div class="ui-col-12">
                                        <ui-radio-group
                                            :list="SysOptions"
                                            :value.sync="inquiry"
                                            valid="editBusinessleads_inquiry"
                                        ></ui-radio-group>
                                    </div>
                                </ui-form-item>
                                <ui-form-item v-show="clueType != 394" label="渠道类型" :must="true" :text="true">
                                    <div class="ui-col-12">
                                        <ui-radio-group
                                            :list="Sources"
                                            :value.sync="source"
                                            :showId="true"
                                            @change="changeSource"
                                            valid="editBusinessleads_source"
                                        ></ui-radio-group>
                                    </div>
                                </ui-form-item>
                                <ui-form-item v-show="clueType != 394" label="渠道名称" :must="true" :text="true" v-if="SourceNames.length">
                                    <div class="ui-col-12">
                                        <ui-radio-group
                                            :list="SourceNames"
                                            :value.sync="communication"
                                            valid="editBusinessleads_communication"
                                        ></ui-radio-group>
                                    </div>
                                </ui-form-item>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-title">线索详情</div>
                            <div>
                                <!-- 编辑时 该组件 v-if 延迟回显 -->
                                <!-- <ui-form-item label="三级分类" :must="true">
                                    <div class="ui-col-6">
                                        <ui-search-related
                                            base-url="/crm/category/public/findThreeCategory"
                                            width="490"
                                            :clearable="true"
                                            v-model="CategoryContent"
                                            @change="handlerCategory"
                                            valid="editBusinessleads_CategoryContent"
                                            need-history="crm_category_history"
                                        ></ui-search-related>
                                    </div>
                                </ui-form-item> -->
                                <ui-form-item label="产品信息" :must="true">
                                    <ui-input
                                        type="textarea"
                                        maxlength="1000"
                                        show-word-limit
                                        v-model="goodsInfo"
                                        height-auto
                                        height="30px"
                                        width="823px"
                                        placeholder="请输入线索相关的产品信息，其他内容可填写至备注中"
                                        valid="editBusinessleads_goodsInfo"
                                        @blur="triggerAiInfo"
                                    ></ui-input>
                                </ui-form-item>
                                <ai-category-choose @change="handlerAiCategoryChange" ref="aicategory"></ai-category-choose>
                                <ui-form-item label="客户名称" :must="true">
                                    <div class="ui-col-12">
                                        <ui-tyc
                                            width="490px"
                                            placeholder="输入客户名称选择或启用天眼查询"
                                            v-model="traderName"
                                            :trader-id="traderId"
                                            :tyc-flag="tycFlag"
                                            @change="handlerTrader"
                                            valid="editBusinessleads_traderName"
                                        ></ui-tyc>
                                    </div>
                                </ui-form-item>
                                <ui-form-item label="联系人">
                                    <ui-phone-related
                                        width="323px"
                                        placeholder="已建档客户，可输入姓名搜索"
                                        v-model="contact"
                                        :trader-id="traderId"
                                        :accurate-match="false"
                                        @change="handlerContact"
                                        type="contact"
                                        :maxlength="20"
                                    ></ui-phone-related>
                                    <!-- <ui-input 
                                        v-model="contact" 
                                        width="323px" maxlength="20"
                                        placeholder="最多支持20个字" 
                                        @change="handlerContact"
                                    ></ui-input> -->
                                </ui-form-item>
                                <ui-form-item label="手机">
                                    <ui-phone-related
                                        width="323px"
                                        placeholder="仅支持11位手机号"
                                        v-model="phone"
                                        :trader-id="traderId"
                                        @blur="phone_Blur"
                                        :accurate-match="false"
                                        @change="handlerPhone"
                                        :error-msg="phoneMsg" 
                                    ></ui-phone-related>
                                </ui-form-item>
                                <ui-form-item label="固话">
                                    <ui-input 
                                        type="number" @blur="telephone_Blur" placeholder="需要输入区号最多支持20位"
                                        v-model="telephone" width="323px" maxlength="20"
                                        :errorable="Boolean(telephoneMsg)" :error-msg="telephoneMsg" 
                                    ></ui-input>
                                </ui-form-item>
                                <ui-more-contact
                                    label="其他联系方式"
                                    width="323px"
                                    placeholder="请输入联系方式名称"
                                    v-model="otherContactInfo"
                                    @change="handlerMoreContact"
                                ></ui-more-contact>
                                <ui-form-item label="地区" :must="true">
                                    <ui-cascader
                                        width="323px"
                                        class="margin"
                                        placeholder="请选择"
                                        :data="addressData"
                                        v-model="area"
                                        clearable
                                        filterable
                                        each
                                        @change="handleArea"
                                        valid="editBusinessleads_area"
                                    ></ui-cascader>
                                </ui-form-item>
                                <ui-form-item label="归属销售" :must="true">
                                    <ui-select
                                        width="323px"
                                        :remote="true"
                                        :avatar="true"
                                        placeholder="请输入英文名搜索"
                                        v-model="belongerId"
                                        :disabled="belongerDisabled"
                                        clearable
                                        :remote-info="allUserRemoteInfo"
                                        :default-label="belonger"
                                        valid="editBusinessleads_belongerId"
                                        @change="handlerBelonger"
                                    ></ui-select>
                                </ui-form-item>
                                <ui-form-item label="备注">
                                    <ui-input
                                        width="823px"
                                        type="textarea"
                                        maxlength="1000"
                                        placeholder="可输入客户诉求、特殊情况等"
                                        show-word-limit
                                        v-model="remark"
                                        height-auto
                                        height="30px"
                                        width="823px"
                                    ></ui-input>
                                </ui-form-item>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/businessleadsEdit.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
