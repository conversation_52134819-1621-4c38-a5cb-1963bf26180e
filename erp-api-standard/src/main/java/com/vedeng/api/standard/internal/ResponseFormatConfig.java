package com.vedeng.api.standard.internal;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * 响应格式配置类
 * 支持自定义响应格式的错误码和消息字段映射
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-21
 */
public class ResponseFormatConfig {
    
    /**
     * 成功标识字段名称集合
     */
    private final Set<String> successFields;
    
    /**
     * 错误码字段名称集合
     */
    private final Set<String> codeFields;
    
    /**
     * 错误消息字段名称集合
     */
    private final Set<String> messageFields;
    
    /**
     * 数据字段名称集合
     */
    private final Set<String> dataFields;
    
    /**
     * 成功的错误码值集合
     */
    private final Set<String> successCodes;
    
    /**
     * 成功的布尔值集合
     */
    private final Set<String> successBooleans;
    
    /**
     * 默认配置实例
     */
    private static final ResponseFormatConfig DEFAULT_INSTANCE = new ResponseFormatConfig();
    
    /**
     * 默认构造函数
     */
    public ResponseFormatConfig() {
        this.successFields = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("success", "ok")));
        this.codeFields = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("code", "errorCode", "errCode", "status", "statusCode")));
        this.messageFields = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("message", "msg", "errorMessage", "errMsg", "description", "desc")));
        this.dataFields = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("data", "result", "payload", "body", "content")));
        this.successCodes = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("0", "200", "0000", "SUCCESS")));
        this.successBooleans = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("true", "1", "yes", "success")));
    }
    
    /**
     * 私有构造函数，用于Builder
     */
    private ResponseFormatConfig(Set<String> successFields, Set<String> codeFields, 
                                Set<String> messageFields, Set<String> dataFields,
                                Set<String> successCodes, Set<String> successBooleans) {
        this.successFields = Collections.unmodifiableSet(new HashSet<>(successFields));
        this.codeFields = Collections.unmodifiableSet(new HashSet<>(codeFields));
        this.messageFields = Collections.unmodifiableSet(new HashSet<>(messageFields));
        this.dataFields = Collections.unmodifiableSet(new HashSet<>(dataFields));
        this.successCodes = Collections.unmodifiableSet(new HashSet<>(successCodes));
        this.successBooleans = Collections.unmodifiableSet(new HashSet<>(successBooleans));
    }
    
    /**
     * 获取默认配置实例
     * 
     * @return 默认配置
     */
    public static ResponseFormatConfig getDefault() {
        return DEFAULT_INSTANCE;
    }
    
    /**
     * 创建自定义配置构建器
     * 
     * @return 配置构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    // Getter methods
    public Set<String> getSuccessFields() {
        return successFields;
    }
    
    public Set<String> getCodeFields() {
        return codeFields;
    }
    
    public Set<String> getMessageFields() {
        return messageFields;
    }
    
    public Set<String> getDataFields() {
        return dataFields;
    }
    
    public Set<String> getSuccessCodes() {
        return successCodes;
    }
    
    public Set<String> getSuccessBooleans() {
        return successBooleans;
    }
    
    /**
     * 配置构建器
     */
    public static class Builder {
        private Set<String> successFields = new HashSet<>(Arrays.asList("success", "ok"));
        private Set<String> codeFields = new HashSet<>(Arrays.asList("code", "errorCode", "errCode", "status", "statusCode"));
        private Set<String> messageFields = new HashSet<>(Arrays.asList("message", "msg", "errorMessage", "errMsg", "description", "desc"));
        private Set<String> dataFields = new HashSet<>(Arrays.asList("data", "result", "payload", "body", "content"));
        private Set<String> successCodes = new HashSet<>(Arrays.asList("0", "200", "0000", "SUCCESS"));
        private Set<String> successBooleans = new HashSet<>(Arrays.asList("true", "1", "yes", "success"));
        
        /**
         * 添加成功标识字段
         */
        public Builder addSuccessField(String... fields) {
            this.successFields.addAll(Arrays.asList(fields));
            return this;
        }
        
        /**
         * 设置成功标识字段（覆盖默认值）
         */
        public Builder setSuccessFields(String... fields) {
            this.successFields = new HashSet<>(Arrays.asList(fields));
            return this;
        }
        
        /**
         * 添加错误码字段
         */
        public Builder addCodeField(String... fields) {
            this.codeFields.addAll(Arrays.asList(fields));
            return this;
        }
        
        /**
         * 设置错误码字段（覆盖默认值）
         */
        public Builder setCodeFields(String... fields) {
            this.codeFields = new HashSet<>(Arrays.asList(fields));
            return this;
        }
        
        /**
         * 添加消息字段
         */
        public Builder addMessageField(String... fields) {
            this.messageFields.addAll(Arrays.asList(fields));
            return this;
        }
        
        /**
         * 设置消息字段（覆盖默认值）
         */
        public Builder setMessageFields(String... fields) {
            this.messageFields = new HashSet<>(Arrays.asList(fields));
            return this;
        }
        
        /**
         * 添加数据字段
         */
        public Builder addDataField(String... fields) {
            this.dataFields.addAll(Arrays.asList(fields));
            return this;
        }
        
        /**
         * 设置数据字段（覆盖默认值）
         */
        public Builder setDataFields(String... fields) {
            this.dataFields = new HashSet<>(Arrays.asList(fields));
            return this;
        }
        
        /**
         * 添加成功错误码值
         */
        public Builder addSuccessCode(String... codes) {
            this.successCodes.addAll(Arrays.asList(codes));
            return this;
        }
        
        /**
         * 设置成功错误码值（覆盖默认值）
         */
        public Builder setSuccessCodes(String... codes) {
            this.successCodes = new HashSet<>(Arrays.asList(codes));
            return this;
        }
        
        /**
         * 添加成功布尔值
         */
        public Builder addSuccessBoolean(String... values) {
            this.successBooleans.addAll(Arrays.asList(values));
            return this;
        }
        
        /**
         * 设置成功布尔值（覆盖默认值）
         */
        public Builder setSuccessBooleans(String... values) {
            this.successBooleans = new HashSet<>(Arrays.asList(values));
            return this;
        }
        
        /**
         * 构建配置
         */
        public ResponseFormatConfig build() {
            return new ResponseFormatConfig(successFields, codeFields, messageFields, 
                                          dataFields, successCodes, successBooleans);
        }
    }
}