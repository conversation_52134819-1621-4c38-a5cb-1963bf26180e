package com.vedeng.common.shiro.cas;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.google.common.primitives.Ints;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.redis.RedisKeyUtils;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.shiro.cas.service.UacAccountFacadeService;
import com.vedeng.common.shiro.constant.SecurityConstants;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.system.service.PositService;
import com.vedeng.system.service.RoleService;
import com.vedeng.system.service.UserService;
import com.vedeng.uac.api.dto.PermissionDTO;
import io.buji.pac4j.realm.Pac4jRealm;
import io.buji.pac4j.subject.Pac4jPrincipal;
import io.buji.pac4j.token.Pac4jToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;

import org.pac4j.core.profile.CommonProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Slf4j
public class CustomPac4jRealm extends Pac4jRealm {

    private final static Logger LOGGER = LoggerFactory.getLogger(CustomPac4jRealm.class);

    private static final String IS_ADMIN_ATTR_NAME = "isAdmin";

    @Resource
    private UacAccountFacadeService uacAccountFacadeService;
    @Resource
    private UserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private PositService positService;

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken)
            throws AuthenticationException {
        LOGGER.info("start CustomPac4jRealm.doGetAuthenticationInfo");
        final Pac4jToken token = (Pac4jToken) authenticationToken;
        final LinkedHashMap<String, CommonProfile> profiles = token.getProfiles();

        if (MapUtils.isEmpty(profiles)) {
            throw new UnknownAccountException();
        }

        CommonProfile primaryProfile = profiles.entrySet().iterator().next().getValue();

        Integer accountId = Ints.tryParse(primaryProfile.getId());
        User userQuery = userService.getUserById(accountId);
        if (userQuery == null) {
            LOGGER.error("登录时根据用户编号未查询到用户信息, userId: {}", accountId);
            throw new UnknownAccountException();
        }
        List<Integer> orgIds=userService.getOrgIdsByUsersOrgIds(accountId);
        List<String> stringList = orgIds.stream().map(String::valueOf).collect(Collectors.toList());
        userQuery.setOrgIdsList( StringUtils.join(stringList,","));
        final List<Role> roleList = listRoleNames(accountId, primaryProfile.getUsername());
        Set<String> roleNameSet = new LinkedHashSet<>();
        for (Role role : roleList) {
            roleNameSet.add(role.getRoleName());
        }

        primaryProfile.addRoles(roleNameSet);

        final Set<String> permissions = listPermissions(accountId);

        // 角色名的集合
        if (CollectionUtils.isEmpty(roleList)) {
            JedisUtils.set(RedisKeyUtils.createRoleKey(userQuery.getUsername()), JsonUtils.convertConllectionToJsonStr(roleNameSet),
                    JedisUtils.INFINITE_TIMEOUT);
        }

        userQuery.setCompanyId(ErpConst.NJ_COMPANY_ID);
        userQuery.setCurrentLoginSystem(SecurityConstants.ERP_PLATFORM_NO);

        final Integer adminFlag = getAdminFlag(primaryProfile);

        //设置部门信息
        if (CasClientHelper.isAdminUser(adminFlag)) {
            userQuery.setPositions(null);
        } else {
            List<Position> positionList = positService.getPositionByUserId(userQuery.getUserId());
            if (CollectionUtils.isNotEmpty(positionList) && positionList.size() == 1) {
                Position positionToUse = positionList.get(0);
                userQuery.setPositType(positionToUse.getType());
                userQuery.setPositLevel(positionToUse.getLevel());
                userQuery.setOrgId(positionToUse.getOrgId());
                userQuery.setOrgName(positionToUse.getOrgName());

                List<User> myUserList = userService.getMyUserList(userQuery, Collections.singletonList(positionToUse.getType()), false);
                if(CollectionUtils.isNotEmpty(myUserList)){
                    userQuery.setSubUserIdList(myUserList.stream().map(User::getUserId).collect(Collectors.toList()));
                    LOGGER.info("账号：{},setSubUserIdList:{}",new Object[]{accountId,userQuery.getSubUserIdList()});
                    userQuery.setUsers(myUserList);
                }else{
                    LOGGER.info("账号可能有异常：{},{}",new Object[]{accountId, JSONObject.toJSONString(userQuery)});
                }
            }

            userQuery.setPositions(positionList);
        }

        userQuery.setIsAdmin(adminFlag);
        userQuery.setRoles(roleList);
        primaryProfile.addPermissions(permissions);

        //保存用户信息
        saveUserInfoIntoSession(userQuery);
        final Pac4jPrincipal principal = new Pac4jPrincipal(profiles, getPrincipalNameAttribute());
        final PrincipalCollection principalCollection = new SimplePrincipalCollection(principal, getName());
        return new SimpleAuthenticationInfo(principalCollection, profiles.hashCode());
    }


    private List<Role> listRoleNames(Integer userId, String username) {
        User userCondition = new User();
        userCondition.setUserId(userId);
        userCondition.setCompanyId(ErpConst.NJ_COMPANY_ID);
        userCondition.setUsername(username);
        return roleService.getUserRoles(userCondition);
    }

    private Set<String> listPermissions(Integer userId) {
        if (userId == null) {
            return Collections.emptySet();
        }
        List<PermissionDTO> permissionDTOS = uacAccountFacadeService.listPermission(userId);
        Set<String> permissionSet = Sets.newLinkedHashSetWithExpectedSize(permissionDTOS.size());
        for (PermissionDTO permissionDTO : permissionDTOS) {
            permissionSet.add(permissionDTO.getUri());
        }

        return permissionSet;
    }


    /**
     * todo 兼容性考虑，暂时这样处理吧
     * 兼容本地session
     *
     * @return
     */
    private void saveUserInfoIntoSession(User user) {
        Session session = SecurityUtils.getSubject().getSession();
        session.setAttribute(SecurityConstants.CURRENT_USER_ATTRIBUTE_NAME, user);

        // 使用新的当前登录用户对象,初始化当前登录用户信息
        CurrentUser currentUser = CurrentUser.builder().id(user.getUserId())
                .actualName(user.getRealName())
                .username(user.getUsername())
                .isAdmin(user.getIsAdmin())
                .companyId(user.getCompanyId())
                .positType(user.getPositType())
                .isDisabled(user.getIsDisabled())
                .positionTypes(user.getPositionTypes())
                .orgId(user.getOrgId())
                .orgName(user.getOrgName())
                .parentId(user.getParentId())
                .build();
        session.setAttribute(ErpConstant.CURRENT_USER, currentUser);
    }


    private Integer getAdminFlag(CommonProfile profile) {
        Integer adminFlag = null;
        try {
            adminFlag = Integer.valueOf(Objects.requireNonNull(profile.getAttribute(IS_ADMIN_ATTR_NAME)).toString());
        } catch (Exception e) {
            log.error("【getAdminFlag】处理异常",e);
        }
        return adminFlag == null ? SecurityConstants.NORMAL_USER_NO : adminFlag;
    }
}
