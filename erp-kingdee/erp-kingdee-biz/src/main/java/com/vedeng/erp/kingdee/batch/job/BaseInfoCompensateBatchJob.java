package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.writer.*;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 基础数据补偿任务
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BaseInfoCompensateBatchJob extends BaseJob {

    @Autowired
    private BatchMaterialCompensateProcessor batchMaterialCompensateProcessor;

    @Autowired
    private BatchMaterialCompensateWriter batchMaterialCompensateWriter;

    @Autowired
    private BatchCustomerCompensateProcessor batchCustomerCompensateProcessor;

    @Autowired
    private BatchCustomerCompensateWriter batchCustomerCompensateWriter;

    @Autowired
    private BatchSupplierCompensateProcessor batchSupplierCompensateProcessor;

    @Autowired
    private BatchSupplierCompensateWriter batchSupplierCompensateWriter;

    @Value("${material.save.exclude.sku}")
    private String materialSaveExcludeSku;



    public Job baseInfoCompensateFlowJob() {
        return jobBuilderFactory.get("baseInfoCompensateFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(materialCompensate())
                .next(customerCompensate())
                .next(supplierCompensate())
                .build();
    }

    /**
     * 商品（物料）补偿
     */
    private Step materialCompensate() {
        return stepBuilderFactory.get("materialCompensate")
                .<BatchCoreSkuDto, KingDeeMaterialDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchMaterialCompensateReader(null, null))
                .processor(batchMaterialCompensateProcessor)
                .writer(batchMaterialCompensateWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 客户信息补偿
     */
    private Step customerCompensate() {
        return stepBuilderFactory.get("customerCompensate")
                .<BatchCustomerFinanceDto, KingDeeCustomerDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchCustomerCompensateReader(null, null))
                .processor(batchCustomerCompensateProcessor)
                .writer(batchCustomerCompensateWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 供应商信息补偿
     */
    private Step supplierCompensate() {
        return stepBuilderFactory.get("supplierCompensate")
                .<BatchSupplierFinanceDto, KingDeeSupplierDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSupplierCompensateReader(null, null))
                .processor(batchSupplierCompensateProcessor)
                .writer(batchSupplierCompensateWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCoreSkuDto> batchMaterialCompensateReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                  @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCoreSkuDto batchCoreSkuDto = new BatchCoreSkuDto();
        batchCoreSkuDto.setBeginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime));
        batchCoreSkuDto.setEndTime(endTime == null ? null : DateUtil.parseDateTime(endTime));
        if (StrUtil.isNotBlank(materialSaveExcludeSku)) {
            batchCoreSkuDto.setMaterialSaveExcludeSkuList(Arrays.asList(materialSaveExcludeSku.split(",")));
        }
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCoreSkuDto.class.getSimpleName(), "queryMaterialCompensate", batchCoreSkuDto);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCustomerFinanceDto> batchCustomerCompensateReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                          @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCustomerFinanceDto customerDto = new BatchCustomerFinanceDto();
        customerDto.setBeginTimestamp(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime());
        customerDto.setEndTimestamp(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCustomerFinanceDto.class.getSimpleName(), "queryCustomerCompensate", customerDto);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSupplierFinanceDto> batchSupplierCompensateReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                          @Value("#{jobParameters['endTime']}") String endTime) {
        BatchSupplierFinanceDto supplierDto = new BatchSupplierFinanceDto();
        supplierDto.setBeginTimestamp(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime());
        supplierDto.setEndTimestamp(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSupplierFinanceDto.class.getSimpleName(), "querySupplierCompensate", supplierDto);
    }
}