package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleReturnStockEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @date 2023/1/9
* @apiNote 
*/
public interface KingDeeSaleReturnStockMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(KingDeeSaleReturnStockEntity record);

    int insertOrUpdate(KingDeeSaleReturnStockEntity record);

    int insertOrUpdateSelective(KingDeeSaleReturnStockEntity record);

    int insertSelective(KingDeeSaleReturnStockEntity record);

    KingDeeSaleReturnStockEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(KingDeeSaleReturnStockEntity record);

    int updateByPrimaryKey(KingDeeSaleReturnStockEntity record);

    int updateBatch(List<KingDeeSaleReturnStockEntity> list);

    int updateBatchSelective(List<KingDeeSaleReturnStockEntity> list);

    int batchInsert(@Param("list") List<KingDeeSaleReturnStockEntity> list);
}