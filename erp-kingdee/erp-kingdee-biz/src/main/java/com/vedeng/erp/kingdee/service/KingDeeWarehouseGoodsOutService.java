package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.batch.dto.*;

import java.util.List;
import java.util.Map;

/**
 * 出库服务类
 */
public interface KingDeeWarehouseGoodsOutService {


	/**
     * 生成采购直发对应的销售出库单
     *
     * @param user                  用户信息
     * @param buyorder              采购订单信息
     * @param id_sendN_sendedN_sumN 采购商品id_发货数量_已发数量_总数量
     * @param express               快递信息
     * @param splitOrNotMap
     */
	void insertSaleorderDirectOut(BatchUserDto user, BatchBuyorderDto buyorder, String id_sendN_sendedN_sumN, BatchExpressDto express, Map<Integer, Boolean> splitOrNotMap);

	/**
	 * 生成出库复核单
	 */
	void createWarehouseGoodsOutReport(BatchWarehouseGoodsOutInDto warehouse,List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList);

	/**
	 * 获取商品明细信息
	 */
	void querySkuInfo(BatchOutInDetailDto skuInfo, Integer skuId);

}
