<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="../../../common/common.jsp" %>
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/hx_invoice_common.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<style>
    body .layui-layer-btn .layui-layer-btn0{
        border-color: #E6E3E6;
        background-color: #FFF;
        color: black;
    }
    body .layui-layer-btn .layui-layer-btn1{
        border-color: #4898d5;
        background-color: #2e8ded;
        color: #fff;
    }

    body .layui-layer-btn .layui-layer-btn2{
        border-color: #4898d5;
        background-color: #2e8ded;
        color: #fff;
    }
</style>
<div class="layui-tab-item">
    <div class="searchfunc ">
        <form method="post" id="search" action="/finance/invoice/hx_invoice.do?id=3">
            <ul>
                <li>
                    <label class="keyword">关键词</label>
                    <input type="text" class="input-small" name="keyword" id="keyword"
                           value="${invoiceSearch.keyword}"/>
                </li>

                <li>
                    <label class="infor_name" style="overflow: visible">开票时间</label>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endAddDateStr\')}'})"
                           name="startAddDateStr" id="startAddDateStr"
                           value='<date:date value ="${startAddDateStr}" format="yyyy-MM-dd"/>'>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startAddDateStr\')}'})"
                           name="endAddDateStr" id="endAddDateStr"
                           value='<date:date value ="${endAddDateStr}" format="yyyy-MM-dd"/>'>
                </li>

                <li>
                    <label class="infor_name">发票总额</label>
                    <input class="f_left input-smaller96 mr5" type="text" name="invoiceAmountFrom"
                           id="invoiceAmountFrom" value='${invoiceSearch.invoiceAmountFrom}'
                           onchange="checkValue(this)">
                    <div class="gang">-</div>
                    <input class="f_left input-smaller96" type="text" name="invoiceAmountTo" id="invoiceAmountTo"
                           value='${invoiceSearch.invoiceAmountTo}' onchange="checkValue(this)">
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">查询</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
            </div>
        </form>
        <br>
        <div class="content">
<%--            <div class="inputfloat" style='margin:0px 0 15px 0;'>--%>
<%--                <input type="checkbox" id="check_All" name="check_All" onclick="checkallInvoice();"--%>
<%--                       value="checkbox_one">--%>
<%--                <span>全选</span>--%>
<%--                <span class="bt-bg-style bg-light-blue bt-small mr10" onclick="batchConfirmHxInvoice();">批量标记</span>--%>
<%--            </div>--%>
<%--            <div style='float: right'>--%>
<%--                <span class="bt-bg-style bg-light-blue bt-small mr10" onclick="batchConfirmHxInvoice();">容错配置</span>--%>
<%--            </div>--%>
            <div class="parts">
                <div  class='parts'>
                    <div class="title-container">
                        <div class="table-title nobor">
                            <input type="checkbox" id="check_All" name="check_All" onclick="checkallInvoice();"
                                   value="checkbox_one">
                            <span>全选</span>
                            <span class="bt-bg-style bg-light-blue bt-small mr10" onclick="batchConfirmHxInvoice();">批量标记</span>
                        </div>
                        <div class="title-click">
                            <span class="bt-bg-style bg-light-blue bt-small mr10" onclick="faultConfiguration();">容错配置</span>
                        </div>
                    </div>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th class="wid3">选择</th>
                            <th class="wid3">序号</th>
                            <th class="wid8">发票号码</th>
                            <th class="wid8">发票代码</th>
                            <th class="wid10">开票方名称</th>
                            <th class="wid8">发票总额</th>
                            <th class="wid8">开票时间</th>
                            <th class="wid5">开户行</th>
                            <th class="wid9">账号</th>
                            <th class="wid9">注册地址</th>
                            <th class="wid9">注册电话</th>
                            <th class="wid9">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:set var="lzyxAmount" value="0"></c:set><!-- 蓝字有效总额 -->
                        <c:set var="lzzfAmount" value="0"></c:set><!-- 蓝字作废总额 -->
                        <c:set var="hzyxAmount" value="0"></c:set><!-- 红字有效总额 -->


                        <c:set var="pageNum" value="0"></c:set><!-- 当前页记录数 -->
                        <c:forEach var="item" items="${list}" varStatus="status">
                            <c:set var="pageNum" value="${pageNum + 1}"></c:set>
                            <tr>
                                <td>
                                    <input type="checkbox" name="checkbox_one" onclick="checkOneHxInvoice()"
                                           value="${item.hxInvoiceId}">
                                </td>
                                <td>${status.count}</td>
                                <td>${item.invoiceNum}</td>
                                <td>${item.invoiceCode}</td>
                                <td>${item.salerName}</td>
                                <td>
                                    <fmt:formatNumber value="${item.amount}" type="numer" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                </td>
                                <td>
                                    <c:if test="${item.createTime != null}">
                                        <jsp:useBean id="createTimeDateValue" class="java.util.Date"/>
                                        <jsp:setProperty name="createTimeDateValue" property="time"
                                                         value="${item.createTime}"/>
                                        <fmt:formatDate value="${createTimeDateValue}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                    </c:if>
                                    <c:choose>
                                        <c:when test="${item.colorType == 1}">
                                            <c:set var="lzyxAmount" value="${lzyxAmount + item.amount}"></c:set>
                                        </c:when>
                                        <c:when test="${item.colorType == 2}">
                                            <c:set var="hzyxAmount" value="${hzyxAmount + item.amount}"></c:set>
                                        </c:when>
                                        <c:when test="${item.colorType == 3}">
                                            <c:set var="lzzfAmount" value="${lzzfAmount + item.amount}"></c:set>
                                        </c:when>
                                    </c:choose>
                                </td>
                                <td>${item.buyerBank} </td>
                                <td>${item.buyerBankAccount}</td>
                                <td>${item.buyerAddress}</td>
                                <td>${item.buyerTel}</td>
                                <td>
                                    <a onclick="viewAndDownloadHxInvoiceHref('${item.invoiceCode}','${item.invoiceNum}','${item.hxInvoiceId}',0)">
                                        <font style="color: #438DEF"
                                              id="viewInvocieFont${item.hxInvoiceId}">查看发票</font>
                                    </a>
                                    <span class="edit-user pop-new-data" layerparams='{"width":"600px","height":"220px","title":"标记发票状态",
											  "link":"<%=basePath%>finance/invoice/signHxInvoice.do?hxInvoiceId=${item.hxInvoiceId}&typeFlag=2"}'>
											标记
                                    </span>
                                </td>
                            </tr>
                            <tr style="display: none" id="imgTr${item.hxInvoiceId}">
                                <td colspan="12" height="350px" id="invoiceImg${item.hxInvoiceId}">
                                </td>
                            </tr>
                            <input id="imgFlag${item.hxInvoiceId}" value="0" type="hidden">
                            <input id="imgSrc${item.hxInvoiceId}" value="${item.attachment}" type="hidden">
                        </c:forEach>
                        </tbody>
                    </table>

                    <c:if test="${empty list}">
                        <!-- 查询无结果弹出 -->
                        <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
                    </c:if>
                </div>
            </div>
            <div>
                <tags:page page="${page}"/>
                <div class="clear"></div>
                <div class="fixtablelastline" style="height: initial;">
                    【全部结果 条目：${invoice.invoiceCount}
                    总金额：<fmt:formatNumber type="number" value="${invoice.amountCount == null ? 0 : invoice.amountCount}" pattern="0.00" maxFractionDigits="2" /> 】 【本页统计 条目：${pageNum}
                    总金额：<fmt:formatNumber type="number" value="${lzyxAmount - lzzfAmount - hzyxAmount}" pattern="0.00" maxFractionDigits="2" />】
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
<script>

    /**
     * 批量标记发票
     * */
    function batchConfirmHxInvoice() {
        var hxInvoiceIds = '';
        $("input[name='checkbox_one']").each(function () {
            if ($(this).is(":checked")) {
                hxInvoiceIds = hxInvoiceIds == '' ? $(this).val() : hxInvoiceIds + '&' + $(this).val();
            }
        });
        if (hxInvoiceIds.length == 0) {
            layer.alert('请选择需要标记的发票');
        } else {
            $.ajax({
                url: '/finance/invoice/signHxInvoice.do',
                data: {
                    typeFlag: 2,
                    hxInvoiceIds: hxInvoiceIds
                },
                type: 'post',
                dataType: "html",
                success: function (res) {
                    layer.open({
                        type: 1,
                        shade: 0.1,
                        area: ['600px', '220px'],
                        title: '发票标记',
                        content: res,
                        success: function (layero, index) {

                        }
                    });
                }
            })
        }
    }

    function faultConfiguration() {
        var open = layer.open({
            type: 2,
            title: '容错配置',
            area: ['60%', '90%'],
            // shadeClose: false,
            maxmin: true,
            content: '/finance/invoice/hxInvoiceFaultConfiguration.do',

            btnAlign: 'c',
            btn: ['取消', '仅保存', '保存并刷新'],
            // btn: ['btn1','btn2', 'btn3'],
            // 弹层外区域关闭
            btn1: function (index, layero) {
                layer.close(index)
            },
            btn2: function (index, layero) {
                // //点击确认触发 iframe 内容中的按钮提交
                // var submit = layero.find('iframe').contents().find("#layuiadmin-app-form-submit");
                // submit.click();
                var flag = window[layero.find('iframe')[0]['name']].isRepeat()
                // var flag = window["layui-layer-iframe" + index].isRepeat();
                if (flag != null && flag != undefined){
                    if (flag == 1){
                        layer.alert("字符内容不允许重复")
                    }else if (flag == 2){
                        layer.alert("开户行内容不允许重复")
                    }else if (flag == 3){
                        layer.alert("账户内容不允许重复")
                    }else if (flag == 4){
                        layer.alert("注册地址内容不允许重复")
                    }else if (flag == 5){
                        layer.alert("注册电话内容不允许重复")
                    }

                    return false;
                }
                var data = window[layero.find('iframe')[0]['name']].getConfigList();
                var root = new Object();
                root.type = 0;
                root.configList = data;
                $.ajax({
                    url: "/finance/invoice/saveConfig.do",
                    type: "POST",
                    contentType: 'application/json; charset=UTF-8',
                    dataType: "json",
                    async: false,
                    data: JSON.stringify(root),
                    success: function (res) {
                        if (res.code == 0) {
                            layer.msg("提交成功");
                            layer.close(index);
                            window.location.reload();
                        } else {
                            layer.msg('提交错误:' + data.msg);
                        }
                    },
                    error: function (data) {
                        layer.msg('提交错误:' + data.msg);
                    }

                });
                return false;
            },
            //按钮
            btn3: function (index, layero) {
                var flag = window[layero.find('iframe')[0]['name']].isRepeat();
                if (flag != null && flag != undefined){
                    if (flag == 1){
                        layer.alert("字符内容不允许重复")
                    }else if (flag == 2){
                        layer.alert("开户行内容不允许重复")
                    }else if (flag == 3){
                        layer.alert("账户内容不允许重复")
                    }else if (flag == 4){
                        layer.alert("注册地址内容不允许重复")
                    }else if (flag == 5){
                        layer.alert("注册电话内容不允许重复")
                    }

                    return false;
                }
                var data = window[layero.find('iframe')[0]['name']].getConfigList();
                layer.confirm("提交后，现有的异常票将按照新规则做一轮匹配整个过程将耗时几分钟，请耐心等待！", {
                    btn: ['取消', '确定']
                }, function (index1) {
                    layer.close(index1);
                }, function (index2) {
                    // layer.closeAll();
                    loadIndex = layer.load(1, {
                        shadeClose: false,
                        title: 'loading..',
                        shade: [0.5,'#000']
                    });
                    var root = new Object();
                    root.type = 1;
                    root.configList = data;
                    $.ajax({
                        url: "/finance/invoice/saveConfig.do",
                        type: "POST",
                        contentType: 'application/json; charset=UTF-8',
                        dataType: "json",
                        async: false,
                        data: JSON.stringify(root),
                        success: function (res) {
                            if (res.code == 0) {
                                layer.closeAll()
                                layer.close(loadIndex);
                                layer.msg(res.message);
                                window.location.reload();
                            } else {
                                layer.close(loadIndex);
                                layer.msg('提交错误:' + res.message);
                                return false;
                            }
                        },
                        error: function (res) {
                            layer.close(loadIndex);
                            layer.msg('提交错误:' + res.message);
                            window.location.reload();
                        }

                    });

                });
                return false;
            }
        })
    }




    /**
     * 重置搜索框内容
     */
    function searchReset() {
        $('#keyword').val('');
        $('#startAddDateStr').val('');
        $('#endAddDateStr').val('');
        $('#invoiceAmountFrom').val('');
        $('#invoiceAmountTo').val('');
    }

    /**
     * 校验数字框输入合法性
     */
    function checkValue(obj) {
        if ($(obj).val() != undefined && $(obj).val() != '') {
            var reg = /^\d+(?=\.{0,1}\d+$|$)/;
            if (!reg.test($(obj).val())) {
                layer.alert('请输入数字!');
                $(obj).val('');
            }
        }
    }

    /**
     * @description 全选
     */
    function checkallInvoice() {
        var checked = $("#check_All").is(":checked");
        var checkboxs = document.getElementsByName("checkbox_one");
        for (var i = 0; i < checkboxs.length; i++) {
            if (checked) {
                checkboxs[i].checked = true;
            } else {
                checkboxs[i].checked = false;
            }
        }
    }

    /**
     * @description 单选
     */
    function checkOneHxInvoice() {
        var checked = true;
        var checkboxs = document.getElementsByName("checkbox_one");
        for (var i = 0; i < checkboxs.length; i++) {
            if (!(checkboxs[i].checked)) {
                checked = false;
                break;
            }
        }
        $("#check_All").prop("checked", checked);
    }
</script>
<%@ include file="../../../common/footer.jsp" %>