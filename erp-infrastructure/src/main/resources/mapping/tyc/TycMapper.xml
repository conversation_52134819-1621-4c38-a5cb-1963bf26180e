<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.infrastructure.tyc.mapper.TycMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" >
    <id column="TRADER_INFO_TYC_ID" property="traderInfoTycId" jdbcType="INTEGER" />
    <result column="SYNC_TIME" property="syncTime" jdbcType="BIGINT" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="BIGINT" />
    <result column="FROM_TIME" property="fromTime" jdbcType="BIGINT" />
    <result column="TO_TIME" property="toTime" jdbcType="BIGINT" />
    <result column="TYPE" property="type" jdbcType="INTEGER" />
    <result column="CATEGORY_SCORE" property="categoryScore" jdbcType="INTEGER" />
    <result column="ID" property="id" jdbcType="BIGINT" />
    <result column="REG_NUMBER" property="regNumber" jdbcType="VARCHAR" />
    <result column="PERCENTILE_SCORE" property="percentileScore" jdbcType="INTEGER" />
    <result column="PHONE_NUMBER" property="phoneNumber" jdbcType="VARCHAR" />
    <result column="REG_CAPITAL" property="regCapital" jdbcType="VARCHAR" />
    <result column="REG_INSTITUTE" property="regInstitute" jdbcType="VARCHAR" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="REG_LOCATION" property="regLocation" jdbcType="VARCHAR" />
    <result column="INDUSTRY" property="industry" jdbcType="VARCHAR" />
    <result column="APPROVED_TIME" property="approvedTime" jdbcType="BIGINT" />
    <result column="ORG_APPROVED_INSTITUTE" property="orgApprovedInstitute" jdbcType="VARCHAR" />
    <result column="BUSINESS_SCOPE" property="businessScope" jdbcType="VARCHAR" />
    <result column="ORG_NUMBER" property="orgNumber" jdbcType="VARCHAR" />
    <result column="ESTIBLISH_TIME" property="estiblishTime" jdbcType="BIGINT" />
    <result column="REG_STATUS" property="regStatus" jdbcType="VARCHAR" />
    <result column="LEGAL_PERSON_NAME" property="legalPersonName" jdbcType="VARCHAR" />
    <result column="LEGAL_PERSON_ID" property="legalPersonId" jdbcType="BIGINT" />
    <result column="ACTUAL_CAPITAL" property="actualCapital" jdbcType="VARCHAR" />
    <result column="WEBSITE_LIST" property="websiteList" jdbcType="VARCHAR" />
    <result column="FLAG" property="flag" jdbcType="INTEGER" />
    <result column="CORRECT_COMPANY_ID" property="correctCompanyId" jdbcType="VARCHAR" />
    <result column="COMPANY_ORG_TYPE" property="companyOrgType" jdbcType="VARCHAR" />
    <result column="BASE" property="base" jdbcType="VARCHAR" />
    <result column="UPDATE_TIMES" property="updateTimes" jdbcType="BIGINT" />
    <result column="CREDIT_CODE" property="creditCode" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="BIGINT" />
    <result column="HISTORY_NAMES" property="historyNames" jdbcType="VARCHAR" />
    <result column="COMPANY_TYPE" property="companyType" jdbcType="INTEGER" />
    <result column="SOURCE_FLAG" property="sourceFlag" jdbcType="VARCHAR" />
    <result column="TAX_NUMBER" property="taxNumber" jdbcType="VARCHAR" />
    <result column="LOGO" property="logo" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" extends="BaseResultMap" >
    <result column="JSON_DATA" property="jsonData" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    TRADER_INFO_TYC_ID, SYNC_TIME, UPDATE_TIME, FROM_TIME, TO_TIME, TYPE, CATEGORY_SCORE, 
    ID, REG_NUMBER, PERCENTILE_SCORE, PHONE_NUMBER, REG_CAPITAL, REG_INSTITUTE, NAME, 
    REG_LOCATION, INDUSTRY, APPROVED_TIME, ORG_APPROVED_INSTITUTE, BUSINESS_SCOPE, ORG_NUMBER, 
    ESTIBLISH_TIME, REG_STATUS, LEGAL_PERSON_NAME, LEGAL_PERSON_ID, ACTUAL_CAPITAL, WEBSITE_LIST, 
    FLAG, CORRECT_COMPANY_ID, COMPANY_ORG_TYPE, BASE, UPDATE_TIMES, CREDIT_CODE, COMPANY_ID, 
    HISTORY_NAMES, COMPANY_TYPE, SOURCE_FLAG, TAX_NUMBER, LOGO, SOCIAL_STAFF_NUM
  </sql>
  <sql id="Blob_Column_List" >
    JSON_DATA
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_TRADER_INFO_TYC
    where TRADER_INFO_TYC_ID = #{traderInfoTycId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_TRADER_INFO_TYC
    where TRADER_INFO_TYC_ID = #{traderInfoTycId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" >
    insert into T_TRADER_INFO_TYC (TRADER_INFO_TYC_ID, SYNC_TIME, UPDATE_TIME, 
      FROM_TIME, TO_TIME, TYPE, 
      CATEGORY_SCORE, ID, REG_NUMBER, 
      PERCENTILE_SCORE, PHONE_NUMBER, REG_CAPITAL, 
      REG_INSTITUTE, NAME, REG_LOCATION, 
      INDUSTRY, APPROVED_TIME, ORG_APPROVED_INSTITUTE, 
      BUSINESS_SCOPE, ORG_NUMBER, ESTIBLISH_TIME, 
      REG_STATUS, LEGAL_PERSON_NAME, LEGAL_PERSON_ID, 
      ACTUAL_CAPITAL, WEBSITE_LIST, FLAG, 
      CORRECT_COMPANY_ID, COMPANY_ORG_TYPE, BASE, 
      UPDATE_TIMES, CREDIT_CODE, COMPANY_ID, 
      HISTORY_NAMES, COMPANY_TYPE, SOURCE_FLAG, 
      TAX_NUMBER, LOGO, JSON_DATA
      )
    values (#{traderInfoTycId,jdbcType=INTEGER}, #{syncTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, 
      #{fromTime,jdbcType=BIGINT}, #{toTime,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, 
      #{categoryScore,jdbcType=INTEGER}, #{id,jdbcType=BIGINT}, #{regNumber,jdbcType=VARCHAR}, 
      #{percentileScore,jdbcType=INTEGER}, #{phoneNumber,jdbcType=VARCHAR}, #{regCapital,jdbcType=VARCHAR}, 
      #{regInstitute,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{regLocation,jdbcType=VARCHAR}, 
      #{industry,jdbcType=VARCHAR}, #{approvedTime,jdbcType=BIGINT}, #{orgApprovedInstitute,jdbcType=VARCHAR}, 
      #{businessScope,jdbcType=VARCHAR}, #{orgNumber,jdbcType=VARCHAR}, #{estiblishTime,jdbcType=BIGINT}, 
      #{regStatus,jdbcType=VARCHAR}, #{legalPersonName,jdbcType=VARCHAR}, #{legalPersonId,jdbcType=BIGINT}, 
      #{actualCapital,jdbcType=VARCHAR}, #{websiteList,jdbcType=VARCHAR}, #{flag,jdbcType=INTEGER}, 
      #{correctCompanyId,jdbcType=VARCHAR}, #{companyOrgType,jdbcType=VARCHAR}, #{base,jdbcType=VARCHAR}, 
      #{updateTimes,jdbcType=BIGINT}, #{creditCode,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT}, 
      #{historyNames,jdbcType=VARCHAR}, #{companyType,jdbcType=INTEGER}, #{sourceFlag,jdbcType=VARCHAR}, 
      #{taxNumber,jdbcType=VARCHAR}, #{logo,jdbcType=VARCHAR}, #{jsonData,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" useGeneratedKeys="true" keyProperty="traderInfoTycId">
    insert into T_TRADER_INFO_TYC
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="traderInfoTycId != null" >
        TRADER_INFO_TYC_ID,
      </if>
      <if test="syncTime != null" >
        SYNC_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="fromTime != null" >
        FROM_TIME,
      </if>
      <if test="toTime != null" >
        TO_TIME,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="categoryScore != null" >
        CATEGORY_SCORE,
      </if>
      <if test="id != null" >
        ID,
      </if>
      <if test="regNumber != null" >
        REG_NUMBER,
      </if>
      <if test="percentileScore != null" >
        PERCENTILE_SCORE,
      </if>
      <if test="phoneNumber != null" >
        PHONE_NUMBER,
      </if>
      <if test="regCapital != null" >
        REG_CAPITAL,
      </if>
      <if test="regInstitute != null" >
        REG_INSTITUTE,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="regLocation != null" >
        REG_LOCATION,
      </if>
      <if test="industry != null" >
        INDUSTRY,
      </if>
      <if test="approvedTime != null" >
        APPROVED_TIME,
      </if>
      <if test="orgApprovedInstitute != null" >
        ORG_APPROVED_INSTITUTE,
      </if>
      <if test="businessScope != null" >
        BUSINESS_SCOPE,
      </if>
      <if test="orgNumber != null" >
        ORG_NUMBER,
      </if>
      <if test="estiblishTime != null" >
        ESTIBLISH_TIME,
      </if>
      <if test="regStatus != null" >
        REG_STATUS,
      </if>
      <if test="legalPersonName != null" >
        LEGAL_PERSON_NAME,
      </if>
      <if test="legalPersonId != null" >
        LEGAL_PERSON_ID,
      </if>
      <if test="actualCapital != null" >
        ACTUAL_CAPITAL,
      </if>
      <if test="websiteList != null" >
        WEBSITE_LIST,
      </if>
      <if test="flag != null" >
        FLAG,
      </if>
      <if test="correctCompanyId != null" >
        CORRECT_COMPANY_ID,
      </if>
      <if test="companyOrgType != null" >
        COMPANY_ORG_TYPE,
      </if>
      <if test="base != null" >
        BASE,
      </if>
      <if test="updateTimes != null" >
        UPDATE_TIMES,
      </if>
      <if test="creditCode != null" >
        CREDIT_CODE,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="historyNames != null" >
        HISTORY_NAMES,
      </if>
      <if test="companyType != null" >
        COMPANY_TYPE,
      </if>
      <if test="sourceFlag != null" >
        SOURCE_FLAG,
      </if>
      <if test="taxNumber != null" >
        TAX_NUMBER,
      </if>
      <if test="logo != null" >
        LOGO,
      </if>
      <if test="jsonData != null" >
        JSON_DATA,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="traderInfoTycId != null" >
        #{traderInfoTycId,jdbcType=INTEGER},
      </if>
      <if test="syncTime != null" >
        #{syncTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="fromTime != null" >
        #{fromTime,jdbcType=BIGINT},
      </if>
      <if test="toTime != null" >
        #{toTime,jdbcType=BIGINT},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="categoryScore != null" >
        #{categoryScore,jdbcType=INTEGER},
      </if>
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="regNumber != null" >
        #{regNumber,jdbcType=VARCHAR},
      </if>
      <if test="percentileScore != null" >
        #{percentileScore,jdbcType=INTEGER},
      </if>
      <if test="phoneNumber != null" >
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="regCapital != null" >
        #{regCapital,jdbcType=VARCHAR},
      </if>
      <if test="regInstitute != null" >
        #{regInstitute,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="regLocation != null" >
        #{regLocation,jdbcType=VARCHAR},
      </if>
      <if test="industry != null" >
        #{industry,jdbcType=VARCHAR},
      </if>
      <if test="approvedTime != null" >
        #{approvedTime,jdbcType=BIGINT},
      </if>
      <if test="orgApprovedInstitute != null" >
        #{orgApprovedInstitute,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null" >
        #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="orgNumber != null" >
        #{orgNumber,jdbcType=VARCHAR},
      </if>
      <if test="estiblishTime != null" >
        #{estiblishTime,jdbcType=BIGINT},
      </if>
      <if test="regStatus != null" >
        #{regStatus,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonName != null" >
        #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonId != null" >
        #{legalPersonId,jdbcType=BIGINT},
      </if>
      <if test="actualCapital != null" >
        #{actualCapital,jdbcType=VARCHAR},
      </if>
      <if test="websiteList != null" >
        #{websiteList,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        #{flag,jdbcType=INTEGER},
      </if>
      <if test="correctCompanyId != null" >
        #{correctCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgType != null" >
        #{companyOrgType,jdbcType=VARCHAR},
      </if>
      <if test="base != null" >
        #{base,jdbcType=VARCHAR},
      </if>
      <if test="updateTimes != null" >
        #{updateTimes,jdbcType=BIGINT},
      </if>
      <if test="creditCode != null" >
        #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="historyNames != null" >
        #{historyNames,jdbcType=VARCHAR},
      </if>
      <if test="companyType != null" >
        #{companyType,jdbcType=INTEGER},
      </if>
      <if test="sourceFlag != null" >
        #{sourceFlag,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null" >
        #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="logo != null" >
        #{logo,jdbcType=VARCHAR},
      </if>
      <if test="jsonData != null" >
        #{jsonData,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" >
    update T_TRADER_INFO_TYC
    <set >
      <if test="syncTime != null" >
        SYNC_TIME = #{syncTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="fromTime != null" >
        FROM_TIME = #{fromTime,jdbcType=BIGINT},
      </if>
      <if test="toTime != null" >
        TO_TIME = #{toTime,jdbcType=BIGINT},
      </if>
      <if test="type != null" >
        TYPE = #{type,jdbcType=INTEGER},
      </if>
      <if test="categoryScore != null" >
        CATEGORY_SCORE = #{categoryScore,jdbcType=INTEGER},
      </if>
      <if test="id != null" >
        ID = #{id,jdbcType=BIGINT},
      </if>
      <if test="regNumber != null" >
        REG_NUMBER = #{regNumber,jdbcType=VARCHAR},
      </if>
      <if test="percentileScore != null" >
        PERCENTILE_SCORE = #{percentileScore,jdbcType=INTEGER},
      </if>
      <if test="phoneNumber != null" >
        PHONE_NUMBER = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="regCapital != null" >
        REG_CAPITAL = #{regCapital,jdbcType=VARCHAR},
      </if>
      <if test="regInstitute != null" >
        REG_INSTITUTE = #{regInstitute,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="regLocation != null" >
        REG_LOCATION = #{regLocation,jdbcType=VARCHAR},
      </if>
      <if test="industry != null" >
        INDUSTRY = #{industry,jdbcType=VARCHAR},
      </if>
      <if test="approvedTime != null" >
        APPROVED_TIME = #{approvedTime,jdbcType=BIGINT},
      </if>
      <if test="orgApprovedInstitute != null" >
        ORG_APPROVED_INSTITUTE = #{orgApprovedInstitute,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null" >
        BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="orgNumber != null" >
        ORG_NUMBER = #{orgNumber,jdbcType=VARCHAR},
      </if>
      <if test="estiblishTime != null" >
        ESTIBLISH_TIME = #{estiblishTime,jdbcType=BIGINT},
      </if>
      <if test="regStatus != null" >
        REG_STATUS = #{regStatus,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonName != null" >
        LEGAL_PERSON_NAME = #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonId != null" >
        LEGAL_PERSON_ID = #{legalPersonId,jdbcType=BIGINT},
      </if>
      <if test="actualCapital != null" >
        ACTUAL_CAPITAL = #{actualCapital,jdbcType=VARCHAR},
      </if>
      <if test="websiteList != null" >
        WEBSITE_LIST = #{websiteList,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        FLAG = #{flag,jdbcType=INTEGER},
      </if>
      <if test="correctCompanyId != null" >
        CORRECT_COMPANY_ID = #{correctCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgType != null" >
        COMPANY_ORG_TYPE = #{companyOrgType,jdbcType=VARCHAR},
      </if>
      <if test="base != null" >
        BASE = #{base,jdbcType=VARCHAR},
      </if>
      <if test="updateTimes != null" >
        UPDATE_TIMES = #{updateTimes,jdbcType=BIGINT},
      </if>
      <if test="creditCode != null" >
        CREDIT_CODE = #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="historyNames != null" >
        HISTORY_NAMES = #{historyNames,jdbcType=VARCHAR},
      </if>
      <if test="companyType != null" >
        COMPANY_TYPE = #{companyType,jdbcType=INTEGER},
      </if>
      <if test="sourceFlag != null" >
        SOURCE_FLAG = #{sourceFlag,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null" >
        TAX_NUMBER = #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="logo != null" >
        LOGO = #{logo,jdbcType=VARCHAR},
      </if>
      <if test="jsonData != null" >
        JSON_DATA = #{jsonData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where TRADER_INFO_TYC_ID = #{traderInfoTycId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" >
    update T_TRADER_INFO_TYC
    set SYNC_TIME = #{syncTime,jdbcType=BIGINT},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      FROM_TIME = #{fromTime,jdbcType=BIGINT},
      TO_TIME = #{toTime,jdbcType=BIGINT},
      TYPE = #{type,jdbcType=INTEGER},
      CATEGORY_SCORE = #{categoryScore,jdbcType=INTEGER},
      ID = #{id,jdbcType=BIGINT},
      REG_NUMBER = #{regNumber,jdbcType=VARCHAR},
      PERCENTILE_SCORE = #{percentileScore,jdbcType=INTEGER},
      PHONE_NUMBER = #{phoneNumber,jdbcType=VARCHAR},
      REG_CAPITAL = #{regCapital,jdbcType=VARCHAR},
      REG_INSTITUTE = #{regInstitute,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      REG_LOCATION = #{regLocation,jdbcType=VARCHAR},
      INDUSTRY = #{industry,jdbcType=VARCHAR},
      APPROVED_TIME = #{approvedTime,jdbcType=BIGINT},
      ORG_APPROVED_INSTITUTE = #{orgApprovedInstitute,jdbcType=VARCHAR},
      BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      ORG_NUMBER = #{orgNumber,jdbcType=VARCHAR},
      ESTIBLISH_TIME = #{estiblishTime,jdbcType=BIGINT},
      REG_STATUS = #{regStatus,jdbcType=VARCHAR},
      LEGAL_PERSON_NAME = #{legalPersonName,jdbcType=VARCHAR},
      LEGAL_PERSON_ID = #{legalPersonId,jdbcType=BIGINT},
      ACTUAL_CAPITAL = #{actualCapital,jdbcType=VARCHAR},
      WEBSITE_LIST = #{websiteList,jdbcType=VARCHAR},
      FLAG = #{flag,jdbcType=INTEGER},
      CORRECT_COMPANY_ID = #{correctCompanyId,jdbcType=VARCHAR},
      COMPANY_ORG_TYPE = #{companyOrgType,jdbcType=VARCHAR},
      BASE = #{base,jdbcType=VARCHAR},
      UPDATE_TIMES = #{updateTimes,jdbcType=BIGINT},
      CREDIT_CODE = #{creditCode,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=BIGINT},
      HISTORY_NAMES = #{historyNames,jdbcType=VARCHAR},
      COMPANY_TYPE = #{companyType,jdbcType=INTEGER},
      SOURCE_FLAG = #{sourceFlag,jdbcType=VARCHAR},
      TAX_NUMBER = #{taxNumber,jdbcType=VARCHAR},
      LOGO = #{logo,jdbcType=VARCHAR},
      JSON_DATA = #{jsonData,jdbcType=LONGVARCHAR}
    where TRADER_INFO_TYC_ID = #{traderInfoTycId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" >
    update T_TRADER_INFO_TYC
    set SYNC_TIME = #{syncTime,jdbcType=BIGINT},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      FROM_TIME = #{fromTime,jdbcType=BIGINT},
      TO_TIME = #{toTime,jdbcType=BIGINT},
      TYPE = #{type,jdbcType=INTEGER},
      CATEGORY_SCORE = #{categoryScore,jdbcType=INTEGER},
      ID = #{id,jdbcType=BIGINT},
      REG_NUMBER = #{regNumber,jdbcType=VARCHAR},
      PERCENTILE_SCORE = #{percentileScore,jdbcType=INTEGER},
      PHONE_NUMBER = #{phoneNumber,jdbcType=VARCHAR},
      REG_CAPITAL = #{regCapital,jdbcType=VARCHAR},
      REG_INSTITUTE = #{regInstitute,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      REG_LOCATION = #{regLocation,jdbcType=VARCHAR},
      INDUSTRY = #{industry,jdbcType=VARCHAR},
      APPROVED_TIME = #{approvedTime,jdbcType=BIGINT},
      ORG_APPROVED_INSTITUTE = #{orgApprovedInstitute,jdbcType=VARCHAR},
      BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      ORG_NUMBER = #{orgNumber,jdbcType=VARCHAR},
      ESTIBLISH_TIME = #{estiblishTime,jdbcType=BIGINT},
      REG_STATUS = #{regStatus,jdbcType=VARCHAR},
      LEGAL_PERSON_NAME = #{legalPersonName,jdbcType=VARCHAR},
      LEGAL_PERSON_ID = #{legalPersonId,jdbcType=BIGINT},
      ACTUAL_CAPITAL = #{actualCapital,jdbcType=VARCHAR},
      WEBSITE_LIST = #{websiteList,jdbcType=VARCHAR},
      FLAG = #{flag,jdbcType=INTEGER},
      CORRECT_COMPANY_ID = #{correctCompanyId,jdbcType=VARCHAR},
      COMPANY_ORG_TYPE = #{companyOrgType,jdbcType=VARCHAR},
      BASE = #{base,jdbcType=VARCHAR},
      UPDATE_TIMES = #{updateTimes,jdbcType=BIGINT},
      CREDIT_CODE = #{creditCode,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=BIGINT},
      HISTORY_NAMES = #{historyNames,jdbcType=VARCHAR},
      COMPANY_TYPE = #{companyType,jdbcType=INTEGER},
      SOURCE_FLAG = #{sourceFlag,jdbcType=VARCHAR},
      TAX_NUMBER = #{taxNumber,jdbcType=VARCHAR},
      LOGO = #{logo,jdbcType=VARCHAR}
    where TRADER_INFO_TYC_ID = #{traderInfoTycId,jdbcType=INTEGER}
  </update>
  <!-- 通过客户名称获取信息 -->
   <select id="getTraderInfoTycByTraderName" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select
     <include refid="Base_Column_List" />
     ,
     <include refid="Blob_Column_List" />
    from T_TRADER_INFO_TYC
    where  NAME = #{name,jdbcType=VARCHAR}
  </select>

  <!-- 本地查询天眼查信息 -->
  <select id="getTcyLocalByTraderName" resultType="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" parameterType="java.util.ArrayList" >
    select
    <include refid="Base_Column_List" />
    from T_TRADER_INFO_TYC
    where NAME IN
    <foreach collection="traderNameList" item="name" index="index" open="(" separator="or" close=")">
         #{name}
    </foreach>
  </select>



  <!-- 根据客户name删除客户信息 -->
  <delete id="deleteByName" parameterType="com.vedeng.infrastructure.tyc.domain.dto.TraderInfoTyc" >
    delete from T_TRADER_INFO_TYC
    where NAME = #{name,jdbcType=VARCHAR}
  </delete>
</mapper>