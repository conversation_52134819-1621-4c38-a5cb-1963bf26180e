package com.vedeng.erp.buyorderexpense.domain.entity;

import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description 采购费用明细
 * <AUTHOR>
 * @date 2022/11/4 14:36
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class BuyorderExpenseItemEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer buyorderExpenseItemId;

    /**
     * 采购费用单ID
     */
    private Integer buyorderExpenseId;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 收货时间
     */
    private Date arrivalTime;

    /**
     * 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    private Date deliveryTime;

    /**
     * 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer invoiceStatus;

    /**
     * 收票时间
     */
    private Date invoiceTime;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;


}