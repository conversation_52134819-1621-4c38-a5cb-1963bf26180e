@import url(../../new/css/common/font/font.css);
html,
body {
  color: #333;
  font-size: 12px;
  line-height: 1.5;
}
body {
  overflow-x: hidden;
}
* {
  box-sizing: border-box;
}
.vd-icon {
  background: none;
  height: auto;
  margin-bottom: 0;
  line-height: 1;
}
.input-text {
  height: 33px;
  line-height: 31px;
  border: 1px solid #BABFC2;
  border-radius: 3px;
  padding: 0 10px;
  outline: none;
  font-size: 14px;
}
.input-text:hover {
  border-color: #969B9E;
}
.input-text:focus {
  border-color: #0099FF;
}
.input-text::placeholder {
  color: #999;
}
.btn {
  background: #0099FF;
  color: #fff;
  border: 1px solid #09f;
  height: 33px;
  line-height: 31px;
  display: inline-block;
  font-size: 14px;
  padding: 0 15px;
  border-radius: 3px;
  cursor: pointer;
}
.btn:hover {
  background: #0087E0;
  color: #fff;
}
.btn:active {
  background: #006CB3;
}
.btn.btn-gray {
  background: #f5f7fa;
  color: #333;
  border-color: #f5f7fa;
}
.btn.btn-gray:hover {
  border-color: #09f;
  color: #09f;
  background: #fff;
}
.btn.btn-s {
  height: 22px;
  font-size: 12px;
  line-height: 20px;
  padding: 0 13px;
}
.btn.btn-s.disabled {
  background: #F5F7FA;
  color: #999;
  cursor: not-allowed;
  border-color: #F5F7FA;
}
a {
  color: #09f;
}
a:hover {
  color: #f60;
}
.wrap {
  padding: 20px;
  padding-top: 73px;
}
.search-wrap {
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 11;
  width: 100%;
}
.search-wrap .search-inner {
  display: flex;
  align-items: center;
  position: relative;
}
.search-wrap .search-inner .select-lv {
  margin-right: 20px;
  font-size: 14px;
  width: 240px;
}
.search-wrap .search-inner .select-lv .select-selected .placeholder {
  color: #999;
}
.search-wrap .search-inner .search-history-list {
  position: absolute;
  background: #fff;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  border: solid 1px #BABFC2;
  top: 33px;
  width: 300px;
  white-space: nowrap;
  z-index: 1;
  padding: 5px 0;
}
.search-wrap .search-inner .search-history-list .search-history-item {
  padding: 0 10px;
  line-height: 33px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.search-wrap .search-inner .search-history-list .search-history-item:hover {
  background: #f5f7fa;
}
.search-wrap .search-inner .search-history-list .search-history-item:active {
  background: #ebeff2;
}
.search-wrap .search-inner .search-history-list .search-history-clear {
  text-align: right;
}
.search-wrap .search-inner .search-history-list .search-history-clear .clear-txt {
  padding: 10px;
  display: inline-block;
  font-size: 12px;
  color: #999;
  cursor: pointer;
  margin-bottom: -5px;
}
.search-wrap .search-inner .search-history-list .search-history-clear .clear-txt:hover {
  color: #f60;
}
.search-wrap .search-inner .search-input {
  width: 300px;
  margin-right: 10px;
}
.search-wrap .search-inner .btn {
  margin-right: 20px;
}
.search-wrap .search-inner .reset {
  font-size: 12px;
  cursor: pointer;
}
.search-wrap .search-inner .reset .vd-icon {
  font-size: 12px;
  margin-right: 5px;
}
.search-wrap .price-list {
  position: absolute;
  right: 20px;
  top: 20px;
  line-height: 33px;
}
.filter-wrap {
  margin-bottom: 20px;
  display: none;
}
.filter-wrap .filter-block {
  font-size: 12px;
  display: flex;
  border: solid 1px #EBEFF2;
  margin-bottom: -1px;
}
.filter-wrap .filter-block:nth-child(n+5) {
  display: none;
}
.filter-wrap .filter-block .filter-label {
  width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 9px 20px;
  text-align: right;
}
.filter-wrap .filter-block .filter-cnt {
  position: relative;
  flex: 1;
  padding-right: 139px;
}
.filter-wrap .filter-block .filter-list {
  display: flex;
  padding: 9px 0;
  flex-wrap: wrap;
  margin-right: -30px;
  margin-bottom: -10px;
  max-height: 34px;
  overflow: hidden;
}
.filter-wrap .filter-block .filter-list .filter-item {
  margin-right: 30px;
  margin-bottom: 10px;
  color: #09f;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.filter-wrap .filter-block .filter-list .filter-item .item-checkbox {
  margin-right: 5px;
  display: none;
}
.filter-wrap .filter-block .filter-list .filter-item .item-checkbox.icon-checkbox1 {
  color: #969B9E;
}
.filter-wrap .filter-block .filter-list .filter-item:hover {
  color: #f60;
}
.filter-wrap .filter-block .filter-list .filter-item:hover .item-checkbox.icon-checkbox1 {
  color: #09f;
}
.filter-wrap .filter-block .filter-list .filter-item:hover .item-checkbox.icon-checkbox2 {
  color: #09f;
}
.filter-wrap .filter-block .filter-option {
  position: absolute;
  right: 10px;
  top: 7px;
  display: flex;
  align-items: center;
}
.filter-wrap .filter-block .filter-option .option-item {
  padding: 1px 1px 1px 7px;
  border: 1px solid #F5F7FA;
  background: #F5F7FA;
  margin-right: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 2px;
}
.filter-wrap .filter-block .filter-option .option-item .vd-icon {
  font-size: 16px;
  transition: transform 0.22s ease;
}
.filter-wrap .filter-block .filter-option .option-item:last-child {
  margin-right: 0;
}
.filter-wrap .filter-block .filter-option .option-item:hover {
  color: #09f;
  border-color: #09f;
}
.filter-wrap .filter-block .filter-option .option-item:active {
  color: #006CB3;
  border-color: #006CB3;
}
.filter-wrap .filter-block .filter-option .btn {
  margin-right: 5px;
}
.filter-wrap .filter-block .filter-option .btn:last-child {
  margin-right: 0;
}
.filter-wrap .filter-block .less-txt {
  display: none;
}
.filter-wrap .filter-block .option-multi {
  display: none;
  background: #fff;
}
.filter-wrap .filter-block.show .filter-list {
  max-height: none;
}
.filter-wrap .filter-block.show .less-txt {
  display: inline;
}
.filter-wrap .filter-block.show .more-txt {
  display: none;
}
.filter-wrap .filter-block.show .option-item .icon-down {
  transform: rotate(180deg);
}
.filter-wrap .filter-block.multi .option-multi {
  display: flex;
}
.filter-wrap .filter-block.multi .filter-list .filter-item .item-checkbox {
  display: block;
}
.filter-wrap .filter-block.multi .filter-list .filter-item .item-checkbox.icon-checkbox2 {
  display: none;
}
.filter-wrap .filter-block.multi .filter-list .filter-item.checked .icon-checkbox1 {
  display: none;
}
.filter-wrap .filter-block.multi .filter-list .filter-item.checked .icon-checkbox2 {
  display: block;
}
.filter-wrap .filter-more {
  background: #F5F7FA;
  border: solid 1px #EBEFF2;
  height: 36px;
  text-align: center;
}
.filter-wrap .filter-more .filter-more-btn {
  padding: 6px 20px;
  border-radius: 0 0 2px 2px;
  border: solid 1px #EBEFF2;
  border-top: 0;
  background: #fff;
  display: inline-block;
  margin-top: -1px;
  cursor: pointer;
}
.filter-wrap .filter-more .filter-more-btn .vd-icon {
  font-size: 16px;
  vertical-align: -2px;
  margin-left: 5px;
  transition: all 0.22s ease;
}
.filter-wrap .filter-more .filter-more-btn:hover {
  color: #09f;
}
.filter-wrap .filter-less-btn-txt {
  display: none;
}
.filter-wrap.open .filter-block:nth-child(n+5) {
  display: flex;
}
.filter-wrap.open .filter-more-btn-txt {
  display: none;
}
.filter-wrap.open .filter-less-btn-txt {
  display: inline;
}
.filter-wrap.open .filter-more-btn .vd-icon {
  transform: rotate(180deg);
}
.list-filter-info {
  height: 46px;
}
.list-filter-info .list-filter-info-inner {
  display: flex;
  padding-bottom: 20px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-link {
  white-space: nowrap;
  color: #09f;
  cursor: pointer;
  display: flex;
  align-items: center;
  max-width: 200px;
  margin-right: 5px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-link .item-txt {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-link .icon-right {
  font-size: 16px;
  color: #999;
  margin-left: 5px;
  cursor: default;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-link:hover {
  color: #f60;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l.link-last .filter-item-link:last-child {
  color: #999;
  cursor: auto;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-link:last-child .icon-right {
  display: none;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-choose {
  white-space: nowrap;
  color: #f60;
  cursor: pointer;
  display: flex;
  align-items: center;
  max-width: 200px;
  margin-right: 5px;
  border: 1px dashed #d7dade;
  height: 22px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-choose .item-txt {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 8px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-choose .icon-delete {
  font-size: 16px;
  color: #999;
  margin-right: 5px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-choose:hover {
  border: 1px solid #f60;
}
.list-filter-info .list-filter-info-inner .list-filter-info-l .filter-item-choose:hover .icon-delete {
  color: #f60;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r {
  display: flex;
  white-space: nowrap;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .filter-list {
  display: flex;
  align-items: center;
  margin-right: 30px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .filter-list .filter-item {
  display: flex;
  align-items: center;
  color: #09f;
  margin-left: 20px;
  cursor: pointer;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .filter-list .filter-item .vd-icon {
  margin-right: 5px;
  color: #969B9E;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .filter-list .filter-item .vd-icon.icon-checkbox2 {
  color: #09f;
  display: none;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .filter-list .filter-item:hover {
  color: #f60;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .filter-list .filter-item:hover .icon-checkbox1 {
  color: #09f;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .filter-list .filter-item.checked .icon-checkbox1 {
  display: none;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .filter-list .filter-item.checked .icon-checkbox2 {
  display: block;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .page-simple-wrap {
  display: flex;
  margin-right: 10px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .option-btn {
  background: #F5F7FA;
  width: 26px;
  height: 26px;
  border: 1px solid #BABFC2;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 2px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .option-btn.btn-l {
  border-radius: 2px 0 0 2px;
  margin-right: -1px;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .option-btn.btn-r {
  border-radius: 0 2px 2px 0;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .option-btn:hover {
  background: #EBEFF2;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .option-btn:active {
  background: #E1E5E8;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .option-btn.disabled {
  cursor: not-allowed;
  background: #F5F7FA;
  border-color: #D7DADE;
  color: #999;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .list-setting {
  position: relative;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .list-setting .sort-tip {
  background: #2e2e2e;
  padding: 10px 10px 10px 15px;
  white-space: nowrap;
  color: #fff;
  border-radius: 3px;
  position: absolute;
  z-index: 12;
  top: 36px;
  right: -1px;
  display: none;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .list-setting .sort-tip::before {
  content: "";
  position: absolute;
  right: 8px;
  top: -10px;
  border: 5px solid transparent;
  border-bottom: 5px solid #2e2e2e;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .list-setting .sort-tip .icon-delete {
  font-size: 16px;
  vertical-align: -3px;
  margin-left: 5px;
  cursor: pointer;
  opacity: 0.6;
}
.list-filter-info .list-filter-info-inner .list-filter-info-r .list-setting .sort-tip .icon-delete:hover {
  opacity: 1;
}
.list-filter-info .list-filter-info-inner.fixed {
  position: fixed;
  top: 73px;
  width: 100%;
  background: #fff;
  z-index: 10;
  left: 0;
  padding: 0 20px 20px;
}
.list-wrap .list-cnt {
  overflow-x: auto;
}
.table {
  min-width: 100%;
  width: auto;
  border-spacing: 0;
  border-collapse: separate;
  margin-bottom: 0;
}
.table thead th {
  padding: 7px 10px;
  font-weight: normal;
  text-align: left;
  background: #EBEFF2;
  border: solid 1px #D7DADE;
  border-bottom: solid 1px #EBEFF2;
  white-space: nowrap;
}
.table thead th .th-wrap {
  display: flex;
  align-items: center;
}
.table thead th .th-wrap .th-txt {
  flex: 1;
}
.table thead th .th-wrap .th-sort {
  width: 6px;
  position: relative;
  margin-left: 5px;
  height: 18px;
}
.table thead th .th-wrap .th-sort::before,
.table thead th .th-wrap .th-sort::after {
  content: "";
  width: 6px;
  height: 4px;
  position: absolute;
  left: 0;
  background-size: 100% 100%;
  background-image: url(../../images/goodsSearch/up.svg);
}
.table thead th .th-wrap .th-sort::before {
  top: 4px;
}
.table thead th .th-wrap .th-sort::after {
  transform: rotate(180deg);
  top: 10px;
}
.table thead th.sort {
  cursor: pointer;
}
.table thead th.sort:hover {
  background: #E1E5E8;
}
.table thead th.sort.checked {
  background: #D7DADE;
}
.table thead th.sort.up .th-wrap .th-sort::after {
  display: none;
}
.table thead th.sort.down .th-wrap .th-sort::before {
  display: none;
}
.table tbody td {
  padding: 7px 10px;
  border: 1px solid #EBEFF2;
  text-align: left;
  white-space: nowrap;
  background: #fff;
}
.table tr:nth-child(2n) td {
  background: #FAFBFC;
}
.table tr:hover {
  background: #fff;
}
.table tr:hover:nth-child(2n) {
  background: #FAFBFC;
}
.table tr.active td {
  border-color: #FFD2B3;
  background: #FFEDE0;
}
.table tr.active:hover td {
  background: #FFEDE0;
}
.table tr.active:hover:nth-child(2n) td {
  background: #FFEDE0;
}
.prod-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-size: 100%;
  margin-right: 3px;
}
.prod-icon.icon-1 {
  background-image: url(../../images/goodsSearch/prod-icon-1.jpg);
}
.prod-icon.icon-2 {
  background-image: url(../../images/goodsSearch/prod-icon-2.jpg);
}
.prod-icon.icon-3 {
  background-image: url(../../images/goodsSearch/prod-icon-3.jpg);
}
.prod-icon.icon-4 {
  background-image: url(../../images/goodsSearch/prod-icon-4.jpg);
}
.prod-icon.icon-5 {
  background-image: url(../../images/goodsSearch/prod-icon-5.jpg);
}
.prod-icon.icon-6 {
  background-image: url(../../images/goodsSearch/prod-icon-6.jpg);
}
.table-fixed {
  position: fixed;
  top: 119px;
  width: calc(100% - 40px);
  margin: 0 20px;
  z-index: 10;
  overflow: hidden;
  display: none;
  left: 0;
}
.table-fixed .table-inner {
  overflow-y: hidden;
}
.table-cnt-fixed {
  position: fixed;
  top: 119px;
  left: 20px;
  z-index: 10;
  overflow: hidden;
  display: none;
}
.table-cnt-fixed .table {
  min-width: auto;
  margin: 0;
}
.table-cnt-fixed .table-head-inner {
  position: relative;
  z-index: 1;
  box-shadow: 10px 0 8px -8px rgba(0, 0, 0, 0.15);
}
.table-cnt-fixed .table-body-inner {
  margin-top: -2px;
  overflow: hidden;
  position: relative;
  box-shadow: 10px 0 8px -8px rgba(0, 0, 0, 0.15);
}
.empty-wrap {
  text-align: center !important;
  padding: 80px 0 !important;
}
.empty-wrap .icon-info2 {
  color: #09f;
  font-size: 48px;
}
.empty-wrap .empty-txt {
  margin-top: 20px;
  font-size: 14px;
}
.checkbox-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.checkbox-item .vd-icon {
  margin-right: 5px;
}
.checkbox-item .vd-icon.icon-checkbox1 {
  color: #969B9E;
}
.checkbox-item .vd-icon.icon-checkbox2 {
  color: #09f;
  display: none;
}
.checkbox-item .item-txt {
  color: #09f;
}
.checkbox-item.checked .vd-icon.icon-checkbox1 {
  display: none;
}
.checkbox-item.checked .vd-icon.icon-checkbox2 {
  display: block;
}
.checkbox-item:hover .item-txt {
  color: #f60;
}
.checkbox-item:hover .vd-icon.icon-checkbox1 {
  color: #09f;
}
.checkbox-item.disabled {
  cursor: not-allowed;
}
.checkbox-item.disabled .item-txt {
  color: #333;
}
.checkbox-item.disabled .vd-icon.icon-checkbox2 {
  color: #7FCCFF;
}
.dlg-setting-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  display: none;
  z-index: 999;
}
.dlg-setting-wrap .dlg-cnt {
  width: 720px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  border-radius: 5px;
  overflow: hidden;
  position: relative;
}
.dlg-setting-wrap .dlg-cnt .dlg-title {
  padding: 0 20px;
  font-size: 16px;
  background: #F5F7FA;
  line-height: 44px;
}
.dlg-setting-wrap .dlg-cnt .dlg-close {
  font-size: 24px;
  color: #CCCCCC;
  cursor: pointer;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  position: absolute;
  right: 0;
  top: 0;
}
.dlg-setting-wrap .dlg-cnt .dlg-close:hover {
  color: #333;
}
.dlg-setting-wrap .dlg-cnt .dlg-main {
  padding: 20px;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list-wrap {
  margin-bottom: 20px;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list-wrap:last-child {
  margin-bottom: 0;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list-wrap .check-list-title {
  color: #999;
  margin-bottom: 10px;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list {
  display: flex;
  flex-wrap: wrap;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list.noborder .checkbox-item-wrap:hover {
  border-color: transparent;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list.noborder .checkbox-item-wrap:hover .drag-icon {
  display: none;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list.noborder .checkbox-item:hover .item-txt {
  color: #09f;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item-wrap {
  background: #fff;
  width: 25%;
  position: relative;
  border: 1px solid transparent;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item-wrap.placeholder {
  border: 1px dashed #09f;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item-wrap:hover {
  border: solid 1px #E1E5E8;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item-wrap:hover .drag-icon {
  display: block;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item-wrap.disabled:hover {
  border-color: transparent;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item-wrap .drag-icon {
  width: 36px;
  height: 36px;
  background-image: url(../../images/goodsSearch/drag-icon.svg);
  background-size: 16px 16px;
  background-position: center;
  position: absolute;
  right: 0;
  top: 1px;
  cursor: move;
  display: none;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item-wrap .drag-icon:hover {
  background-image: url(../../images/goodsSearch/drag-icon-hover.svg);
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item {
  padding: 0 38px 0 10px;
  display: flex;
  align-items: center;
  height: 38px;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .check-list .checkbox-item .item-txt {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .dlg-footer .dlg-btns {
  display: flex;
  justify-content: flex-end;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .dlg-footer .dlg-btns .btn-gray {
  border-color: #BABFC2;
  margin-left: 10px;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .dlg-footer .dlg-btns .btn-gray:hover {
  background: #EBEFF2;
  color: #333;
}
.dlg-setting-wrap .dlg-cnt .dlg-main .dlg-footer .dlg-btns .btn-gray:active {
  background: #BABFC2;
}
.dlg-setting-wrap.dlg-tip .dlg-cnt {
  width: 480px;
}
.dlg-setting-wrap.dlg-tip .dlg-cnt .tip-cnt {
  padding-top: 24px;
}
.dlg-setting-wrap.dlg-tip .dlg-cnt .tip-img {
  height: 160px;
  background-image: url(../../images/goodsSearch/tip.gif);
  background-size: 100% 100%;
  margin-bottom: 20px;
}
.dlg-setting-wrap.dlg-tip .dlg-cnt .tip-txt {
  font-size: 14px;
  text-align: center;
  margin-bottom: 40px;
}
.J-pager {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.J-pager .pager-total-txt {
  margin-right: 10px;
  color: #999;
}
.J-pager .pager-total-txt span {
  color: #333;
}
.page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  background: rgba(255, 255, 255, 0.6);
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  display: none;
}
@keyframes scroll {
  100% {
    transform: rotate(360deg);
  }
}
.page-loading .img {
  width: 64px;
  height: 64px;
  background: url(../../images/goodsSearch/loading.svg);
  background-size: 100% 100%;
  animation: scroll 0.8s linear infinite;
}
.page-tip {
  display: flex;
  width: 100%;
  position: fixed;
  justify-content: center;
  top: 20px;
  z-index: -1;
  transition: all 0.22s ease;
  opacity: 0;
}
.page-tip .txt {
  background: #FFEDE0;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  font-size: 14px;
  color: #333;
  padding: 12px 20px 12px 15px;
  border-radius: 5px;
  display: flex;
  align-items: center;
}
.page-tip .txt .icon-caution2 {
  color: #f60;
  font-size: 20px;
  margin-right: 10px;
}
.page-tip.show {
  top: 100px;
  opacity: 1;
  z-index: 11;
}
.page-tip.tip-success .txt {
  background: #E3F7E3;
}
.page-tip.tip-success .icon-yes2 {
  font-size: 20px;
  margin-right: 10px;
  color: #13BF13;
}

.tip-wrap {
  position: relative;
}

.tip-wrap .tip-icon {
  color: #09f;
  cursor: pointer;
  vertical-align: -2px;
}

.tip-cnt-question {
  background: #fff;
  border-radius: 3px;
  padding: 10px;
  width: 200px;
  position: fixed;
  left: 20px;
  top: 15px;
  z-index: 111111;
  box-shadow: 0 3px 5px rgba(0,0,0,.1);
}
.fee-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-image: url(../../../static/images/goodsSearch/no-fee.svg);
  background-size: 100% 100%;
  margin-right: 5px;
  vertical-align: -3px;
}