package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayExpensesEntity;
import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayExpensesConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayExpensesMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购费用的应付单 以及 票的数据推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchExpensePayableOrderProcessorService extends BaseProcessor<BatchInvoiceDto, BatchPayExpensesDto> {

    public static final String SPECIAL_INVOICE = "专用发票";

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BatchBuyorderExpenseDtoMapper batchExpenseBuyorderExpenseDtoMapper;

    @Autowired
    private BatchBuyorderExpenseItemDtoMapper batchBuyorderExpenseItemDtoMapper;

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchRBuyorderExpenseJSaleorderDtoMapper batchRBuyorderExpenseJSaleorderDtoMapper;

    @Autowired
    private BatchRExpenseAfterSalesJSaleorderMapper batchRExpenseAfterSalesJSaleorderMapper;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;
    @Autowired
    private KingDeePayExpensesMapper kingDeePayExpensesMapper;

    @Autowired
    private KingDeePayExpensesConvertor kingDeePayExpensesConvertor;

    @Override
    public BatchPayExpensesDto doProcess(BatchInvoiceDto batchInvoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        // 应付单对象
        KingDeePayExpensesDto dto = new KingDeePayExpensesDto();
        dto.setFQzokBddjtId(batchInvoiceDto.getInvoiceId().toString());

        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("采购费用的应付单,数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }


        log.info("处理采购费用单 应付单{}", JSON.toJSONString(batchInvoiceDto));
        boolean special;
        if (StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains(SPECIAL_INVOICE)) {
            special = true;
            // 过滤未认证的专票
            if (!batchInvoiceDto.getIsAuth().equals(1)) {
                return null;
            }
        } else {
            special = false;
        }

        batchInvoiceDto.setBatchInvoiceDetailDtoList(batchInvoiceDetailDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId()));

        // 根据发票的订单详情ID查询所有的采购费用详情数据
        Set<Integer> invoiceDetailDtoSet = batchInvoiceDto.getBatchInvoiceDetailDtoList().stream().map(BatchInvoiceDetailDto::getDetailgoodsId).collect(Collectors.toSet());
        List<BatchBuyorderExpenseItemDto> buyorderExpenseItemDtoList = batchBuyorderExpenseItemDtoMapper.findByBuyorderExpenseItemIdIn(invoiceDetailDtoSet);
        if (CollUtil.isEmpty(buyorderExpenseItemDtoList)) {
            log.warn("无采购费用明细单,费用详细单据ids{}", invoiceDetailDtoSet);
            return null;
        }
        List<BatchRBuyorderExpenseJSaleorderDto> saleOrderAndExpense = batchRBuyorderExpenseJSaleorderDtoMapper.findByBuyorderExpenseId(batchInvoiceDto.getRelatedId());
        Map<Integer, List<BatchRBuyorderExpenseJSaleorderDto>> saleOrderAndExpense2Map = saleOrderAndExpense.stream().collect(Collectors.groupingBy(BatchRBuyorderExpenseJSaleorderDto::getBuyorderExpenseItemId));
        List<BatchRExpenseAfterSalesJSaleorderDto> afterSalesAndExpense = batchRExpenseAfterSalesJSaleorderMapper.findByBuyorderExpenseId(batchInvoiceDto.getRelatedId());
        Map<Integer, List<BatchRExpenseAfterSalesJSaleorderDto>> afterSalesAndExpense2Map = afterSalesAndExpense.stream().collect(Collectors.groupingBy(BatchRExpenseAfterSalesJSaleorderDto::getBuyorderExpenseItemId));
        // 将发票价格和数量合并到费用单信息中
        Map<Integer, List<BatchInvoiceDetailDto>> listMap = batchInvoiceDto.getBatchInvoiceDetailDtoList()
                .stream().collect(Collectors.groupingBy(BatchInvoiceDetailDto::getDetailgoodsId));

        List<Integer> byRelatedIdAndTypeAndColorType = batchInvoiceDtoMapper.findByRelatedIdAndTypeAndColorType(batchInvoiceDto);
        List<String> invoiceIds = byRelatedIdAndTypeAndColorType.stream().map(Object::toString).collect(Collectors.toList());
        List<KingDeePayExpensesEntity> byFQzokBddjtIds = kingDeePayExpensesMapper.findByFQzokBddjtIds(invoiceIds);
        List<KingDeePayExpensesDto> kingDeePayExpensesDtos = kingDeePayExpensesConvertor.toDto(byFQzokBddjtIds);
        List<KingDeePayExpensesDetailDto> oldRetaions = new ArrayList<>();
        if (CollUtil.isNotEmpty(kingDeePayExpensesDtos)) {
            oldRetaions = kingDeePayExpensesDtos.stream().map(KingDeePayExpensesDto::getFEntityDetail).filter(CollUtil::isNotEmpty)
                    .flatMap(List::stream).collect(Collectors.toList());
        }
        List<KingDeePayExpensesDetailDto> finalOldRetaions = oldRetaions;
        buyorderExpenseItemDtoList.forEach(b -> {
            List<BatchInvoiceDetailDto> batchInvoiceDetailDtos = listMap.get(b.getBuyorderExpenseItemId());
            if (CollUtil.isNotEmpty(batchInvoiceDetailDtos)) {
                BatchInvoiceDetailDto batchInvoiceDetailDto = CollUtil.getFirst(batchInvoiceDetailDtos);
                b.setInvoicePrice(batchInvoiceDetailDto.getPrice());
//                b.setNum(Convert.toInt(batchInvoiceDetailDto.getNum()));
                b.setPreciseNum(batchInvoiceDetailDto.getNum());
                List<Integer> ids = batchInvoiceDetailDtos.stream().map(BatchInvoiceDetailDto::getInvoiceDetailId).collect(Collectors.toList());
                b.setInvoiceDetailIds(ids);
                List<BatchRBuyorderExpenseJSaleorderDto> saleorderDtos = saleOrderAndExpense2Map.get(b.getBuyorderExpenseItemId());
                List<BatchRExpenseAfterSalesJSaleorderDto> afterSalesJSaleorderDtos = afterSalesAndExpense2Map.get(b.getBuyorderExpenseItemId());

                // 正向无关联关系跳过
                if (CollUtil.isEmpty(saleorderDtos)) {
                    return;
                }

                // 计算实际的去除售后数量
                saleorderDtos.forEach(x->{
                    Integer saleorderId = x.getSaleorderId();
                    if (CollUtil.isNotEmpty(afterSalesJSaleorderDtos)) {
                        BigDecimal sum = afterSalesJSaleorderDtos.stream().filter(k -> k.getSaleorderId().equals(saleorderId)).map(BatchRExpenseAfterSalesJSaleorderDto::getPreciseAfterSalesNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        x.setPreciseNum(x.getPreciseNum().subtract(sum));
                    }
                });
                List<BatchRBuyorderExpenseJSaleorderDto> newColl = BeanUtil.copyToList(saleorderDtos, BatchRBuyorderExpenseJSaleorderDto.class);
                List<BatchRBuyorderExpenseJSaleorderDto> realNumCollect = saleorderDtos.stream().filter(x -> x.getPreciseNum().compareTo(BigDecimal.ZERO)>0).collect(Collectors.toList());
                newColl.forEach(a->{
                    // 历史关系去除
                    BigDecimal bigDecimal = finalOldRetaions.stream()
                            .filter(x -> x.getF_QZOK_WLBM().equals(a.getSkuNo())&&x.getF_QZOK_GSXSDH().equals(a.getSaleorderNo()))
                            .map(x -> new BigDecimal(x.getFPriceQty()).abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    a.setPreciseNum(a.getPreciseNum().subtract(bigDecimal));
                });
                newColl = newColl.stream().filter(x -> x.getPreciseNum().compareTo(BigDecimal.ZERO)>0).collect(Collectors.toList());
                List<BatchPayExpenseSkuAndSaleOrderDto> batchPayExpenseSkuAndSaleOrderDtos = bindSkuAndSaleOrder(newColl, realNumCollect, afterSalesJSaleorderDtos, b.getPreciseNum());
                b.setBatchPayExpenseSkuAndSaleOrderDtoList(batchPayExpenseSkuAndSaleOrderDtos);
            }
        });


        BatchBuyorderExpenseDto build = BatchBuyorderExpenseDto.builder().buyorderExpenseId(batchInvoiceDto.getRelatedId()).build();
        List<BatchBuyorderExpenseDto> batchBuyorderExpenseDtoList = batchExpenseBuyorderExpenseDtoMapper.findByAll(build);
        if (CollUtil.isEmpty(batchBuyorderExpenseDtoList)) {
            log.warn("无采购费用单:{}", JSON.toJSONString(build));
            return null;
        }
        BatchBuyorderExpenseDto batchBuyorderExpenseDto = CollUtil.getFirst(batchBuyorderExpenseDtoList);
        log.info("采购费用单信息:{}", JSON.toJSONString(batchBuyorderExpenseDto));


        dto.setFId(0);
        dto.setFDate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getValidTime())));
        dto.setFSupplierId(batchBuyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderSupplierId().toString());
        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : special ? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)) : "0.00";
        InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto = null;
        InPutFeePlainInvoiceDto inPutFeePlainInvoiceDto = null;

        buyorderExpenseItemDtoList.forEach(x -> {
            BigDecimal invoicePrice = x.getInvoicePrice();
            List<BatchPayExpenseSkuAndSaleOrderDto> batchPayExpenseSkuAndSaleOrderDtoList = x.getBatchPayExpenseSkuAndSaleOrderDtoList();

            if (CollUtil.isEmpty(batchPayExpenseSkuAndSaleOrderDtoList)) {
                KingDeePayExpensesDetailDto detailDto = new KingDeePayExpensesDetailDto();
                detailDto.setFEntryTaxRate(taxRate);
                detailDto.setFCOSTID(x.getBuyorderExpenseItemDetailDto().getUnitKingDeeNo());
                detailDto.setFTaxPrice(invoicePrice.toString());
                detailDto.setFPriceQty(String.valueOf(x.getPreciseNum()));
                detailDto.setFINCLUDECOST(false);
                detailDto.setFCOSTDEPARTMENTID("BM9999");
                detailDto.setF_QZOK_GSXSDH("");
                // 由于贝登没有应付单，所以没有应付单明细id，将发票id拼接当成明细id传入+销售单号
                String detailIds = x.getInvoiceDetailIds().stream().sorted().map(String::valueOf).collect(Collectors.joining(StrUtil.DASHED));
                detailDto.setF_QZOK_BDDJHID(detailIds+StrUtil.DASHED+detailDto.getF_QZOK_GSXSDH());
                detailDto.setF_QZOK_WLBM(x.getBuyorderExpenseItemDetailDto().getSku());
                dto.getFEntityDetail().add(detailDto);
            } else {
                batchPayExpenseSkuAndSaleOrderDtoList.forEach(i->{
                    KingDeePayExpensesDetailDto detailDto = new KingDeePayExpensesDetailDto();
                    detailDto.setFEntryTaxRate(taxRate);
                    detailDto.setFCOSTID(x.getBuyorderExpenseItemDetailDto().getUnitKingDeeNo());
                    detailDto.setFTaxPrice(invoicePrice.toString());
                    detailDto.setFPriceQty(String.valueOf(i.getNum()));
                    detailDto.setFINCLUDECOST(false);
                    detailDto.setFCOSTDEPARTMENTID("BM9999");
                    detailDto.setF_QZOK_GSXSDH(i.getSaleOrderNo());
                    // 由于贝登没有应付单，所以没有应付单明细id，将发票id拼接当成明细id传入+销售单号
                    String detailIds = x.getInvoiceDetailIds().stream().sorted().map(String::valueOf).collect(Collectors.joining(StrUtil.DASHED));
                    detailDto.setF_QZOK_BDDJHID(detailIds+StrUtil.DASHED+detailDto.getF_QZOK_GSXSDH());
                    detailDto.setF_QZOK_WLBM(x.getBuyorderExpenseItemDetailDto().getSku());
                    dto.getFEntityDetail().add(detailDto);
                });
            }


        });

        return BatchPayExpensesDto.builder()
                .kingDeePayExpensesDto(dto)
                .inPutFeePlainInvoiceDto(inPutFeePlainInvoiceDto)
                .inPutFeeSpecialInvoiceDto(inPutFeeSpecialInvoiceDto)
                .build();
    }

    /**
     * 绑定
     * @param newColl 减去售后和之前票的数量的
     * @param realNumCollect 只减了售后的
     * @param afterSalesJSaleorderDtos 售后
     * @param skuNum
     * @return
     */
    private List<BatchPayExpenseSkuAndSaleOrderDto> bindSkuAndSaleOrder(List<BatchRBuyorderExpenseJSaleorderDto> newColl,List<BatchRBuyorderExpenseJSaleorderDto> realNumCollect, List<BatchRExpenseAfterSalesJSaleorderDto> afterSalesJSaleorderDtos, BigDecimal skuNum) {
        List<BatchPayExpenseSkuAndSaleOrderDto> result = new ArrayList<>();
        AtomicReference<BigDecimal> num = new AtomicReference<BigDecimal>(skuNum);

        doShare(newColl, result, num);

        if (num.get().compareTo(BigDecimal.ZERO) > 0) {
            doShare(realNumCollect, result, num);
        }


        if (num.get().compareTo(BigDecimal.ZERO) > 0) {
            doShareAfter(afterSalesJSaleorderDtos, result, num);
        }
        // 未分摊完走售后分y
        if (CollUtil.isNotEmpty(result)) {
            Map<String, List<BatchPayExpenseSkuAndSaleOrderDto>> collect = result.stream().collect(Collectors.groupingBy(BatchPayExpenseSkuAndSaleOrderDto::getSaleOrderNo));
            List<BatchPayExpenseSkuAndSaleOrderDto> merge = new ArrayList<>();
            collect.forEach((k,v)->{
                if (v.size() > 1) {
                    BigDecimal sum = v.stream().map(BatchPayExpenseSkuAndSaleOrderDto::getNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    BatchPayExpenseSkuAndSaleOrderDto build = BatchPayExpenseSkuAndSaleOrderDto.builder().skuNo(v.get(0).getSkuNo()).saleOrderId(v.get(0).getSaleOrderId()).saleOrderNo(v.get(0).getSaleOrderNo()).num(sum).build();
                    merge.add(build);
                } else {
                    merge.addAll(v);
                }
            });
            return merge;
        }

        return result;
    }

    private void doShareAfter(List<BatchRExpenseAfterSalesJSaleorderDto> afterSalesJSaleorderDtos, List<BatchPayExpenseSkuAndSaleOrderDto> result, AtomicReference<BigDecimal> num) {

        if (CollUtil.isNotEmpty(afterSalesJSaleorderDtos)) {
            List<BatchRExpenseAfterSalesJSaleorderDto> orderByAfterReturn = afterSalesJSaleorderDtos.stream().sorted(Comparator.comparing(BatchRExpenseAfterSalesJSaleorderDto::getExpenseAfterSalesId).reversed()).collect(Collectors.toList());
            orderByAfterReturn.forEach(x->{
                if (num.get().compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
                if (x.getPreciseAfterSalesNum().compareTo(num.get())>=0 ) {
                    BatchPayExpenseSkuAndSaleOrderDto build = BatchPayExpenseSkuAndSaleOrderDto.builder().skuNo(x.getSkuNo()).saleOrderId(x.getSaleorderId()).saleOrderNo(x.getSaleorderNo()).num(num.get()).build();
                    result.add(build);
                    num.set(BigDecimal.ZERO);
                } else {
                    BatchPayExpenseSkuAndSaleOrderDto build = BatchPayExpenseSkuAndSaleOrderDto.builder().skuNo(x.getSkuNo()).saleOrderId(x.getSaleorderId()).saleOrderNo(x.getSaleorderNo()).num(x.getPreciseAfterSalesNum()).build();
                    result.add(build);
                    num.set(num.get().subtract(x.getPreciseAfterSalesNum()));
                }
            });
        }
    }

    private void doShare(List<BatchRBuyorderExpenseJSaleorderDto> realNumCollect, List<BatchPayExpenseSkuAndSaleOrderDto> result, AtomicReference<BigDecimal> num) {

        if (CollUtil.isNotEmpty(realNumCollect)) {

            realNumCollect.forEach(x->{
                if (num.get().compareTo(BigDecimal.ZERO)<=0) {
                    return;
                }
                if (x.getPreciseNum().compareTo(num.get())>=0 ) {
                    BatchPayExpenseSkuAndSaleOrderDto build = BatchPayExpenseSkuAndSaleOrderDto.builder()
                            .skuNo(x.getSkuNo()).saleOrderId(x.getSaleorderId())
                            .saleOrderNo(x.getSaleorderNo()).num(num.get()).build();
                    result.add(build);
                    num.set(BigDecimal.ZERO);
                } else {
                    BatchPayExpenseSkuAndSaleOrderDto build = BatchPayExpenseSkuAndSaleOrderDto.builder()
                            .skuNo(x.getSkuNo()).saleOrderId(x.getSaleorderId())
                            .saleOrderNo(x.getSaleorderNo()).num(x.getPreciseNum()).build();
                    result.add(build);
                    num.set(num.get().subtract(x.getPreciseNum()));

                }

            });
        }
    }


}
