package com.vedeng.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.SnowFlakeUtils;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.finance.constant.HxInvoiceConstant;
import com.vedeng.finance.dao.HxInvoiceConfigMapper;
import com.vedeng.finance.dao.HxInvoiceDetailMapper;
import com.vedeng.finance.dao.HxInvoiceMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.dto.*;
import com.vedeng.finance.enums.*;
import com.vedeng.finance.model.HxInvoice;
import com.vedeng.finance.model.HxInvoiceConfig;
import com.vedeng.finance.model.HxInvoiceDetail;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.vo.InvoiceConfig;
import com.vedeng.finance.service.HxInvoiceService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.finance.util.HxHttpRequestUtil;
import com.vedeng.finance.util.WatermarkUtil;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.service.warningtask.ExpeditingTicketsService;
import com.vedeng.system.service.OssUtilsService;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date created in 2020/5/18 13:55
 */
@Service
public class HxInvoiceServiceImpl implements HxInvoiceService {

    private final static Logger logger = LoggerFactory.getLogger(HxInvoiceServiceImpl.class);

    @Value("${hx_invoice_server_address}")
    private String hxInvoiceServerAddress;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private HxInvoiceMapper hxInvoiceMapper;

    @Autowired
    private HxInvoiceDetailMapper hxInvoiceDetailMapper;

    @Resource
    private TraderMapper traderMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private HxInvoiceConfigMapper hxInvoiceConfigMapper;

    @Autowired
    private ExpeditingTicketsService expeditingTicketsService;

    @Value("${is_production_environment}")
    private Integer isProductionEnvironment;

    @Autowired
    private InvoiceApiService invoiceApiService;


    @Override
    public String downloadInvoiceImgFromHx(String invoiceType, String invoiceCode, String invoiceNum){
        HxDownloadImgRequestDTO hxDownloadImgRequestDTO = new HxDownloadImgRequestDTO(invoiceType,invoiceCode,invoiceNum);
        HxInvoiceResponse hxInvoiceResponse;
        try {
            hxInvoiceResponse = HxHttpRequestUtil.hxHttpClientRequest(hxInvoiceServerAddress + HxInvoiceConstant.HX_DOWNLOAD_IMG_URL,hxDownloadImgRequestDTO);
            if (!HxInvoiceConstant.HX_RESPONSE_SUCCESS.equals(hxInvoiceResponse.getReturnInfo().getReturnCode())){
                logger.warn("下载航信发票图片失败，发票代码：{}，发票号码：{}，错误信息：{}",invoiceCode,invoiceNum,hxInvoiceResponse.getReturnInfo().getReturnMessage());
                return null;
            }
            if (StringUtils.isBlank(hxInvoiceResponse.getData())){
                logger.error("下载航信发票图片失败，发票代码：{}，发票号码：{}",invoiceCode,invoiceNum);
                return null;
            }
            byte[] bytes = HxHttpRequestUtil.base64Decode2Img(hxInvoiceResponse.getData());
            //图片增加水印
            ByteArrayOutputStream outputStream = (ByteArrayOutputStream) WatermarkUtil.addWatermark(new ByteArrayInputStream(bytes),"贝登内部使用，仅供参考","jpg");
            if (outputStream == null){
                logger.error("发票代码：{}，发票号码：{}添加水印时发生错误",invoiceCode,invoiceNum);
                return null;
            }
            //上传OSS
            return ossUtilsService.uploadFileStream2Oss(new ByteArrayInputStream(outputStream.toByteArray()),"jpg");
        } catch (Exception e) {
            logger.info("下载航信发票图片信息错误，请求参数：{}，错误信息：", hxDownloadImgRequestDTO.toString(), e);
        }
        return null;
    }


    @Override
    public Boolean getHxIncomeInvoiceList(String start, String end){
        //进项票发票类型默认为“01”
        HxInvoiceRequest hxInvoiceRequest = new HxInvoiceRequest("01",start,end);
        HxInvoiceResponse hxInvoiceResponse;
        try {
            hxInvoiceResponse = HxHttpRequestUtil.hxHttpClientRequest(hxInvoiceServerAddress + HxInvoiceConstant.HX_INCOME_INVOICE_QUERY_LIST_URL,hxInvoiceRequest);
            if (!HxInvoiceConstant.HX_RESPONSE_SUCCESS.equals(hxInvoiceResponse.getReturnInfo().getReturnCode())){
                logger.warn("下载航信进项发票信息错误，请求参数：{}，错误信息：{}",hxInvoiceRequest.toString(),hxInvoiceResponse.toString());
                return null;
            }
            String jsonString = HxHttpRequestUtil.base64Decode2String(hxInvoiceResponse.getData());
            List<HxIncomeInvoiceDTO> hxIncomeInvoiceDTOList = JsonUtils.readValueByType(jsonString, new TypeReference<List<HxIncomeInvoiceDTO>>() {});
            if (hxIncomeInvoiceDTOList.size() > 0){
                convertAndSaveHxIncomeInvoice(hxIncomeInvoiceDTOList);
            }
            return true;
        } catch (IOException e) {
            logger.warn("下载航信进项发票信息错误，请求参数：{}，错误信息：",hxInvoiceRequest.toString(),e);
            return false;
        }
    }

    @Override
    public HxInvoiceAuthResponse authHxInvoice(String taxPayerNum, String invoiceCode, String invoiceNum, BigDecimal amount, Integer operationType, String invoiceCreateTime){
        HxInvoiceDeductionRequest deductionRequest = new HxInvoiceDeductionRequest();
        deductionRequest.setNsrsbh(taxPayerNum);
        HxInvoiceDeductionRequest.DeductionData deductionData = new HxInvoiceDeductionRequest.DeductionData(invoiceCode,invoiceNum,String.valueOf(operationType),String.format("%.2f",amount),invoiceCreateTime);
        deductionRequest.setData(Collections.singletonList(deductionData));
        HxInvoiceAuthResponse authResponse;
        try {
            HxInvoiceResponse hxInvoiceResponse = HxHttpRequestUtil.hxHttpClientRequest(hxInvoiceServerAddress + HxInvoiceConstant.HX_INVOICE_AUTH_URL,deductionRequest);
            if (!HxInvoiceConstant.HX_RESPONSE_SUCCESS.equals(hxInvoiceResponse.getReturnInfo().getReturnCode())){
                logger.warn("发票认证失败，请求参数：{}，错误信息：{}",deductionRequest.toString(),hxInvoiceResponse.getReturnInfo().getReturnMessage());
                authResponse = new HxInvoiceAuthResponse(-1,hxInvoiceResponse.getReturnInfo().getReturnMessage());
                return authResponse;
            }
            logger.info("发票认证成功，请求参数：{}，返回信息：{}",deductionRequest.toString(),JSON.toJSONString(hxInvoiceResponse));
            authResponse = new HxInvoiceAuthResponse(1,"");
            return authResponse;
        } catch (Exception e){
            logger.error("发票认证错误，请求参数：{}，错误信息：",deductionRequest.toString(),e);
            authResponse = new HxInvoiceAuthResponse(-1,"接口错误");
            return authResponse;
        }
    }

    @Override
    public void batchDownloadSaleInvoiceImg(Long start, Long end) {
        invoiceMapper.getSaleInvoiceOfNeedDownload(start,end)
                .parallelStream()
                .forEach(
                        invoice -> Optional.ofNullable(downloadInvoiceImgFromHx("0",invoice.getInvoiceCode(),invoice.getInvoiceNo()))
                                            .ifPresent(url -> invoiceMapper.updateInvoiceOssUrl(invoice.getInvoiceId(),url))
                );
    }

    private void initHxInvoiceConfig(List<HxInvoiceConfig> hxInvoiceConfigs){
        HxInvoiceConfigEnum[] values = HxInvoiceConfigEnum.values();
        for (HxInvoiceConfigEnum hxInvoiceConfigEnum : values) {
            int type = hxInvoiceConfigEnum.getType();
            if (hxInvoiceConfigEnum.getType().equals(1)){
                continue;
            }
            HxInvoiceConfig hxInvoiceConfig = new HxInvoiceConfig();
            hxInvoiceConfig.setConfigType(type);
            hxInvoiceConfig.setContent(HxInvoiceEnum.getName(type));
            hxInvoiceConfigs.add(hxInvoiceConfig);
        }
    }


    @Override
    public void convertAndSaveHxIncomeInvoice(List<HxIncomeInvoiceDTO> hxIncomeInvoiceDTOS){
        logger.info("处理保存航信发票信息start hxIncomeInvoiceDTOS:{}", JSON.toJSONString(hxIncomeInvoiceDTOS));
        List<HxInvoiceConfig> hxInvoiceConfigs = hxInvoiceConfigMapper.getInvoiceConfigByType(Arrays.asList(1,2,3,4,5));
        //设置默认值
        initHxInvoiceConfig(hxInvoiceConfigs);
        Map<Integer, List<HxInvoiceConfig>> finalConfigMap = hxInvoiceConfigs.stream().collect(Collectors.groupingBy(HxInvoiceConfig::getConfigType));
        hxIncomeInvoiceDTOS.parallelStream().forEach(hxInvoiceDto -> {
            Integer colorType = getColorTypeOfHxIncomeInvoice(hxInvoiceDto.getFpzt());

            // 过滤已存在类型发票
            if (Objects.nonNull(colorType) && hxInvoiceMapper.getHxInvoiceByCodeAndNum(hxInvoiceDto.getFpdm(), hxInvoiceDto.getFphm())
                    .stream().anyMatch(i -> colorType.equals(i.getColorType()))) {
                logger.info("当前类型航信票已存在本次跳过 hxInvoiceDto:{}", JSON.toJSONString(hxInvoiceDto));
                return;
            }

            logger.info("处理保存航信发票信息 :{}", JSON.toJSONString(hxInvoiceDto));
            HxInvoice hxInvoice = saveHxInvoiceInfo(finalConfigMap, hxInvoiceDto);

            //保存航信发票详情信息
            saveHxInvoiceDetailInfo(hxInvoiceDto, hxInvoice);

            //  航信发票拉取处理蓝字作废票
            dealBusiness4InvalidInvoice(hxInvoiceDto, colorType, hxInvoice);

            //保存发票图片相关
            saveHxInvoicePicInfo(hxInvoiceDto);

            //航信发票关联信息处理
            dealInvoiceInfoByHxInvoice(hxInvoiceDto);
        });
    }

    /**
     * 保存航信发票主体信息
     * @param finalConfigMap
     * @param hxInvoiceDto
     * @return
     */
    private HxInvoice saveHxInvoiceInfo(Map<Integer, List<HxInvoiceConfig>> finalConfigMap, HxIncomeInvoiceDTO hxInvoiceDto) {
        logger.info("保存航信发票主体信息 hxInvoiceDto:{}", JSON.toJSONString(hxInvoiceDto));
        HxInvoice hxInvoice = hxIncomeInvoiceConvert(hxInvoiceDto, finalConfigMap);
        hxInvoice.setInvoiceType(ErpConst.ONE);

        //发票校验保存
        hxInvoiceMapper.insertSelective(hxInvoiceCheck(hxInvoice, finalConfigMap));
        return hxInvoice;
    }

    /**
     * 保存发票图片相关
     *
     * @param hxInvoiceDto
     */
    private void saveHxInvoicePicInfo(HxIncomeInvoiceDTO hxInvoiceDto) {
        if (!ErpConst.ONE.equals(isProductionEnvironment)){
            return;
        }
        logger.info("保存发票图片相关 hxInvoiceDto:{}", JSON.toJSONString(hxInvoiceDto));
        List<HxInvoice> invoicesQuery = hxInvoiceMapper.getHxInvoiceByCodeAndNum(hxInvoiceDto.getFpdm(), hxInvoiceDto.getFphm());
        if (CollectionUtils.isEmpty(invoicesQuery)){
            return;
        }
        String ftpUrl = downloadInvoiceImgFromHx("1", hxInvoiceDto.getFpdm(), hxInvoiceDto.getFphm());
        if (StringUtils.isBlank(ftpUrl)){
            return;
        }
        invoicesQuery.forEach(item -> {
            HxInvoice toUpdate = new HxInvoice();
            toUpdate.setHxInvoiceId(item.getHxInvoiceId());
            toUpdate.setAttachment(ftpUrl);
            hxInvoiceMapper.updateByPrimaryKeySelective(toUpdate);
        });

    }

    /**
     * 航信发票拉取处理蓝字作废票
     * @param hxInvoiceDto
     * @param colorType
     * @param hxInvoice
     */
    private void dealBusiness4InvalidInvoice(HxIncomeInvoiceDTO hxInvoiceDto, Integer colorType, HxInvoice hxInvoice) {
        logger.info("航信发票拉取处理蓝字作废票 hxInvoiceDto:{}, hxInvoice:{}", JSON.toJSONString(hxInvoiceDto), JSON.toJSONString(hxInvoice));
        if (!HxInvoiceColorTypeEnum.BLUE_INVALID.getColorType().equals(colorType)) {
            logger.info("拉取发票不为作废票，不走作废流程处理 hxInvoiceDto:{}", JSON.toJSONString(hxInvoiceDto));
            return;
        }

        try {
            invoiceService.rollBackForBlueValidTicket(hxInvoice);
        } catch (Exception e) {
            logger.error("回滚蓝色作废处理失败， msg :{}", e.getMessage(), e);
        }


        try {
            //航信票作废处理催票信息
            dealBuyOrderEarlyWarningTicketTask(hxInvoice);
        } catch (Exception e) {
            logger.error("作废处理催票信息error hxInvoice:{}", JSON.toJSONString(hxInvoice), e);
        }

        try {
            logger.info("作废发票处理发票关联入库日志信息 hxInvoice:{}", JSON.toJSONString(hxInvoice));
            invoiceApiService.rollbackRelatedWarehousingLog(hxInvoice.getInvoiceNum(), hxInvoice.getInvoiceCode(), null);
        } catch (Exception e) {
            logger.error("作废发票处理发票关联入库日志信息error hxInvoice:{}", JSON.toJSONString(hxInvoice), e);
        }


        /**
         * VDERP-12506【ERP】【采购订单】航信系统拉取到蓝字作废发票后，已录的采购订单录票信息没有回退
         * 由于原始无效票设计问题，直接拉到的作废票要对无效票做补充
         */
        List<Integer> blueInvalidInvoiceIdList = hxInvoiceMapper.getHxInvoiceByCodeAndNum(hxInvoiceDto.getFpdm(), hxInvoiceDto.getFphm())
                .stream().filter(item -> ErpConst.ONE.equals(item.getColorType()))
                .map(HxInvoice::getHxInvoiceId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(blueInvalidInvoiceIdList)){
            blueInvalidInvoiceIdList.forEach(invoiceId -> {
                HxInvoice originHxInvoice4Update = new HxInvoice();
                originHxInvoice4Update.setHxInvoiceId(invoiceId);
                originHxInvoice4Update.setInvoiceStatus(HxInvoiceStatusEnum.INVALID.getStatus());
                logger.info("作废更新原始发票状态信息 originHxInvoice4Update:{}", JSON.toJSONString(originHxInvoice4Update));
                hxInvoiceMapper.updateByPrimaryKeySelective(originHxInvoice4Update);
            });
            return;
        }
        HxInvoice hxInvoiceInValid = new HxInvoice();
        BeanUtils.copyProperties(hxInvoice, hxInvoiceInValid);
        hxInvoiceInValid.setHxInvoiceId(null);
        hxInvoiceInValid.setColorType(ErpConst.ONE);
        hxInvoiceInValid.setAmount(hxInvoiceInValid.getAmount().abs());
        hxInvoiceInValid.setInvoiceStatus(HxInvoiceStatusEnum.INVALID.getStatus());
        logger.info("直接拉到的作废票要对无效票做补充 hxInvoiceInValid:{}", JSON.toJSONString(hxInvoiceInValid));
        hxInvoiceMapper.insertSelective(hxInvoiceInValid);
        saveHxInvoiceDetailInfo(hxInvoiceDto, hxInvoiceInValid);
    }

    /**
     * 航信票作废处理催票信息
     *
     * @param hxInvoice
     */
    private void dealBuyOrderEarlyWarningTicketTask(HxInvoice hxInvoice) {
        logger.info("航信票作废处理催票信息 hxInvoice:{}", JSON.toJSONString(hxInvoice));
        List<Integer> buyOrderValidInvoiceIdList = invoiceMapper.listInvoiceByInvoiceNoAndCode(hxInvoice.getInvoiceNum(), hxInvoice.getInvoiceCode())
                .stream().filter(item ->
                        InvoiceTypeEnum.BUY_ORDER.getType().equals(item.getType()) &&
                                ErpConst.ONE.equals(item.getValidStatus()) &&
                                ErpConst.TWO.equals(item.getColorType()) &&
                                ErpConst.ONE.equals(item.getIsEnable()))
                .map(Invoice::getInvoiceId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(buyOrderValidInvoiceIdList)){
            logger.info("航信票作废处理催票信息无需生成 hxInvoice:{}", JSON.toJSONString(hxInvoice));
            return;
        }

        HashMap<Integer, BigDecimal> detailGoodsIdInvoiceNumMap = new HashMap<>(16);
        buyOrderValidInvoiceIdList.forEach(invoiceId -> {
            invoiceMapper.getInvoiceListByInvoiceId(invoiceId)
                    .forEach(invoiceDetail -> {
                        detailGoodsIdInvoiceNumMap.put(invoiceDetail.getDetailgoodsId(),
                                detailGoodsIdInvoiceNumMap.containsKey(invoiceDetail.getDetailgoodsId()) ?
                                        detailGoodsIdInvoiceNumMap.get(invoiceId).add(invoiceDetail.getNum()) :
                                        invoiceDetail.getNum());
                    });
        });
        logger.info("航信票作废处理催票信息最终结果 detailGoodsIdInvoiceNumMap:{}", JSON.toJSONString(detailGoodsIdInvoiceNumMap));
        detailGoodsIdInvoiceNumMap.forEach((k, v) ->  {
            EarlyWarningTaskDto earlyWarningTaskDto = new EarlyWarningTaskDto();
            earlyWarningTaskDto.setRelateBusinessId(k);
            earlyWarningTaskDto.setUrgingTicketNum(v.intValue());
            expeditingTicketsService.create(earlyWarningTaskDto);
        });
    }

    /**
     * 保存航信发票详情信息
     *
     * @param hxInvoiceDto
     * @param hxInvoice
     */
    private void saveHxInvoiceDetailInfo(HxIncomeInvoiceDTO hxInvoiceDto, HxInvoice hxInvoice) {
        logger.info("保存航信发票详情信息 hxInvoiceDto:{},hxInvoice:{}", JSON.toJSONString(hxInvoiceDto), JSON.toJSONString(hxInvoice));
        int taxRateId = 0;
        if (CollectionUtils.isNotEmpty(hxInvoiceDto.getDetailList())) {
            for (HxIncomeInvoiceDetailDTO detailDTO : hxInvoiceDto.getDetailList()) {
                HxInvoiceDetail invoiceDetail = hxInvoiceDetailConvert(detailDTO, hxInvoice);
                hxInvoiceDetailMapper.insertSelective(invoiceDetail);
                if (taxRateId == 0) {
                    taxRateId = getTaxRateOfHxInvoice(detailDTO.getSlv());
                }
            }
        }
        if (taxRateId > 0){
            HxInvoice toUpdateHxInvoice = new HxInvoice();
            toUpdateHxInvoice.setHxInvoiceId(hxInvoice.getHxInvoiceId());
            toUpdateHxInvoice.setTaxRate(taxRateId);
            hxInvoiceMapper.updateByPrimaryKeySelective(toUpdateHxInvoice);
        }
    }

    /**
     * 拉取航信发票时处理ERP发票信息
     *
     * @param hxInvoiceDto
     * @return
     */
    boolean dealInvoiceInfoByHxInvoice(HxIncomeInvoiceDTO hxInvoiceDto){
        logger.info("拉取航信发票时处理ERP发票信息 hxInvoiceDto:{}", JSON.toJSONString(hxInvoiceDto));
        HxInvoice blueValiInvoice = hxInvoiceMapper.getHxInvoiceByCodeAndNum(hxInvoiceDto.getFpdm(), hxInvoiceDto.getFphm())
                .stream().filter(item -> ErpConst.ONE.equals(item.getColorType()))
                .findFirst()
                .orElse(null);
        if (!Objects.nonNull(blueValiInvoice)){
            return false;
        }

        logger.info("发票拉取关联其蓝字有效航信发票信息 blueValiInvoice:{}", JSON.toJSONString(blueValiInvoice));
        List<Invoice> invoices = invoiceMapper.getValidInvoiceByCondition(hxInvoiceDto.getFphm(), hxInvoiceDto.getFpdm());
        if (CollectionUtils.isEmpty(invoices)){
            return false;
        }
        invoiceMapper.setHxInvoiceInfoByPrimaryKeys(invoices.stream().map(Invoice :: getInvoiceId).collect(Collectors.toList()), blueValiInvoice.getHxInvoiceId());
        return true;
    }



    private HxInvoice hxIncomeInvoiceConvert(HxIncomeInvoiceDTO dto,Map<Integer,List<HxInvoiceConfig>> configMap) {
        HxInvoice hxInvoice = new HxInvoice();
        //目前进项票只有专票
        hxInvoice.setInvoiceCategory("1");
        hxInvoice.setInvoiceCode(dto.getFpdm());
        hxInvoice.setInvoiceNum(dto.getFphm());
        hxInvoice.setSalerTaxNum(dto.getXfsh());
        hxInvoice.setSalerName(dto.getXfmc());
        hxInvoice.setColorType(getColorTypeOfHxIncomeInvoice(dto.getFpzt()));
        if (StringUtils.isNotBlank(dto.getXfdzdh())) {
            String[] xfdzdhArray = dto.getXfdzdh().split(" ");
            if (xfdzdhArray.length >= 2) {
                hxInvoice.setSalerAddress(xfdzdhArray[0]);
                hxInvoice.setSalerTel(xfdzdhArray[1]);
            } else {
                hxInvoice.setSalerAddress(dto.getXfdzdh());
            }
        }
        if (StringUtils.isNotBlank(dto.getXfyhzh())) {
            String[] xfyhzhArray = dto.getXfyhzh().split(" ");
            if (xfyhzhArray.length >= 2) {
                hxInvoice.setSalerBank(xfyhzhArray[0]);
                hxInvoice.setSalerBankAccount(xfyhzhArray[1]);
            } else {
                hxInvoice.setSalerBank(dto.getXfyhzh());
            }
        }
        hxInvoice.setBuyerTaxNum(dto.getGfsh());
        hxInvoice.setBuyerName(dto.getGfmc());
        List<HxInvoiceConfig> characters = getConfig(configMap,1);
        if (StringUtils.isNotBlank(dto.getGfdzdh())) {
            String gfdzdh = filterExceptionCharacter(dto.getGfdzdh(),characters);
            List<HxInvoiceConfig> phones = getConfig(configMap,5);
            if (getContent(phones,gfdzdh) != null){
                hxInvoice.setBuyerAddress(gfdzdh.replace(getContent(phones,gfdzdh),""));
                hxInvoice.setBuyerTel(getContent(phones,gfdzdh));
            } else {
                hxInvoice.setBuyerAddress(dto.getGfdzdh());
            }
        }
        if (StringUtils.isNotBlank(dto.getGfyhzh())) {
            String gfyhzh = filterExceptionCharacter(dto.getGfyhzh(),characters);
            List<HxInvoiceConfig> accounts = getConfig(configMap,3);
            if (getContent(accounts,gfyhzh) != null){
                hxInvoice.setBuyerBank(gfyhzh.replace(getContent(accounts,gfyhzh),""));
                hxInvoice.setBuyerBankAccount(getContent(accounts,gfyhzh));
            } else {
                hxInvoice.setBuyerBank(dto.getGfyhzh());
            }
        }
        if (StringUtils.isNotBlank(dto.getKprq())) {
            hxInvoice.setCreateTime(DateUtil.convertLong(dto.getKprq(), "yyyy-MM-dd"));
        }
        if (StringUtils.isNotBlank(dto.getJe())) {
            hxInvoice.setInvoiceAmount(new BigDecimal(dto.getJe()));
        }
        if (StringUtils.isNotBlank(dto.getJe())){
            hxInvoice.setInvoiceAmount(new BigDecimal(dto.getJe()));
        }
        if (StringUtils.isNotBlank(dto.getSe())){
            hxInvoice.setTaxAmount(new BigDecimal(dto.getSe()));
        }
        if (StringUtils.isNotBlank(dto.getJshj())){
            hxInvoice.setAmount(new BigDecimal(dto.getJshj()));
        }
        hxInvoice.setComment(dto.getBz());
        hxInvoice.setCreator(dto.getKpr());
        hxInvoice.setChecker(dto.getFhr());
        hxInvoice.setPayee(dto.getSkr());
        hxInvoice.setAddTime(DateUtil.sysTimeMillis());
        return hxInvoice;
    }

    private List<HxInvoiceConfig> getConfig(Map<Integer,List<HxInvoiceConfig>> configMap,Integer type){
        List<HxInvoiceConfig> results = new ArrayList<>();
        if (configMap != null && configMap.containsKey(type)){
            results = configMap.get(type);
        }
        return results;
    }


    private HxInvoiceDetail hxInvoiceDetailConvert(HxIncomeInvoiceDetailDTO detailDTO, HxInvoice hxInvoice){
        HxInvoiceDetail hxInvoiceDetail = new HxInvoiceDetail();
        hxInvoiceDetail.setHxInvoiceId(hxInvoice.getHxInvoiceId());
        hxInvoiceDetail.setNumber(detailDTO.getXh());
        hxInvoiceDetail.setGoodsName(detailDTO.getSpmc());
        hxInvoiceDetail.setSpecification(detailDTO.getGgxh());
        hxInvoiceDetail.setUnit(detailDTO.getJldw());
        if (StringUtils.isNotBlank(detailDTO.getSl())){
            hxInvoiceDetail.setQuantity(Double.valueOf(detailDTO.getSl()));
        }
        hxInvoiceDetail.setTaxRate(getTaxRateOfHxInvoice(detailDTO.getSlv()));
        if (StringUtils.isNotBlank(detailDTO.getDj())){
            hxInvoiceDetail.setPrice(new BigDecimal(detailDTO.getDj()));
        }
        if (StringUtils.isNotBlank(detailDTO.getJe())){
            hxInvoiceDetail.setAmount(new BigDecimal(detailDTO.getJe()));
        }
        if (StringUtils.isNotBlank(detailDTO.getSe())){
            hxInvoiceDetail.setTaxAmount(new BigDecimal(detailDTO.getSe()));
        }
        return hxInvoiceDetail;
    }


    /**
     * 根据发票状态获得发票红蓝字
     * 发票状态为正常，则为蓝字有效；发票状态为作废，则为蓝字作废；发票状态为红冲，则为红字有效
     * @param fpzt 发票状态
     * @return 红蓝字
     */
    private Integer getColorTypeOfHxIncomeInvoice(String fpzt){
        if (StringUtils.isBlank(fpzt)){
            return null;
        }
        if ("0".equals(fpzt)){
            return 1;
        }
        if ("2".equals(fpzt)){
            return 3;
        }
        if ("3".equals(fpzt)){
            return 2;
        }
        if ("1".equals(fpzt)){
            return 4;
        }
        if ("4".equals(fpzt)){
            return 5;
        }
        return null;
    }

    private Integer getTaxRateOfHxInvoice(String taxRate){
        String taxRateString = taxRate.substring(0,taxRate.length()-1);
        return Integer.valueOf(taxRateString);
    }


    /**
     * 对进项发票进项检验
     * @param hxInvoice 发票
     * @return 校验过的发票信息
     */
    private HxInvoice hxInvoiceCheck(HxInvoice hxInvoice,Map<Integer,List<HxInvoiceConfig>> configMap){
        if (hxInvoice.getInvoiceType() == 0){
            return hxInvoice;
        }


        if (HxInvoiceColorTypeEnum.BLUE_VALID.getColorType().equals(hxInvoice.getColorType()) && checkHxInvoiceHasInput(hxInvoice)){
            hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.RECORDED.getStatus());
            return hxInvoice;
        }

        if (hxInvoice.getColorType() == null || hxInvoice.getColorType() > 3) {
            hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.EXCEPTION.getStatus());
            return hxInvoice;
        }

        if (!HxInvoiceColorTypeEnum.BLUE_VALID.getColorType().equals(hxInvoice.getColorType())){
            hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.NEGATIVE_NUMBER.getStatus());
            return hxInvoice;
        }
        if (!checkInvoiceAberrant(hxInvoice,configMap)){
            hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.EXCEPTION.getStatus());
            return hxInvoice;
        }
        if (!checkInvoiceMatchTraderSupply(hxInvoice)){
            hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.UNCLAIM.getStatus());
            return hxInvoice;
        }
        hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.WAIT_RECORD.getStatus());
        return hxInvoice;
    }


    /**
     * 判断拉取的发票是否已经录入系统（待审核、审核通过），并且录入金额是否大于都等于发票金额
     * @param hxInvoice 航信推送的进项发票
     * @return 是否完成录入
     */
    private Boolean checkHxInvoiceHasInput(HxInvoice hxInvoice){
        BigDecimal hasInputAmount = invoiceMapper.getValidInvoiceByCondition(hxInvoice.getInvoiceNum(),hxInvoice.getInvoiceCode())
                .stream().filter(item -> !ErpConst.TWO.equals(item.getValidStatus()))
                .map(Invoice::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return hasInputAmount.add(BigDecimal.ONE).compareTo(hxInvoice.getAmount()) > -1;
    }

    /**
     * 判断发票信息是否异常
     * @param hxInvoice 发票信息
     * @return 是否异常，true：准确，false：异常
     */
    private Boolean checkInvoiceAberrant(HxInvoice hxInvoice,Map<Integer,List<HxInvoiceConfig>> configMap){

        //注册地址
        List<String> addressList = configMap.get(HxInvoiceConfigEnum.ADDRESS.getType()).stream()
                .map(HxInvoiceConfig::getContent)
                .collect(Collectors.toList());
        if (!addressList.contains(hxInvoice.getBuyerAddress())){
            return false;
        }
        //注册电话
        List<String> phoneList = configMap.get(HxInvoiceConfigEnum.PHONE.getType()).stream()
                .map(HxInvoiceConfig::getContent)
                .collect(Collectors.toList());
        if (!phoneList.contains(hxInvoice.getBuyerTel())){
            return false;
        }
        //开户行
        List<String> bankList = configMap.get(HxInvoiceConfigEnum.BANK.getType()).stream()
                .map(HxInvoiceConfig::getContent)
                .collect(Collectors.toList());
        if (!bankList.contains(hxInvoice.getBuyerBank())){
            return false;
        }
        //账户
        List<String> accountList = configMap.get(HxInvoiceConfigEnum.ACCOUNT.getType()).stream()
                .map(HxInvoiceConfig::getContent)
                .collect(Collectors.toList());
        if (!accountList.contains(hxInvoice.getBuyerBankAccount())){
            return false;
        }


        return true;
    }

    /**
     * 判断发票销售方能不能匹配到erp供应商
     * @param hxInvoice 发票信息
     * @return 是否匹配，true：能匹配，false：不能匹配
     */
    private Boolean checkInvoiceMatchTraderSupply(HxInvoice hxInvoice){
        List<Trader> traders = traderMapper.getTraderSupplyByTraderName(hxInvoice.getSalerName().trim());
        if (traders.size() > 0){
            hxInvoice.setTraderId(traders.get(0).getTraderId());
            return true;
        }
        return false;
    }

    private String filterExceptionCharacter(String content,List<HxInvoiceConfig> characters){
        for (HxInvoiceConfig character : characters) {
            content = content.replace(character.getContent(),"");
        }
        return content;
    }

    private String getContent(List<HxInvoiceConfig> hxInvoiceConfigs,String gfdzdh){
        if (CollectionUtils.isEmpty(hxInvoiceConfigs)){
            return null;
        }
        for (HxInvoiceConfig hxInvoiceConfig : hxInvoiceConfigs) {
            if (gfdzdh.contains(hxInvoiceConfig.getContent())){
                return hxInvoiceConfig.getContent();
            }
        }
        return null;
    }

    @Override
    public ResultInfo saveInvoiceConfig(InvoiceConfig invoiceConfig, User user) {

        List<Integer> configTypes = Arrays.asList(1,2,3,4,5);
        Map<Integer, List<HxInvoiceConfig>> hxInvoiceConfigMap = invoiceConfig.getConfigList()
                .stream().collect(Collectors.groupingBy(HxInvoiceConfig::getConfigType));
        try {
            for (Integer configType : configTypes) {
                List<HxInvoiceConfig> hxInvoiceConfigs = hxInvoiceConfigMapper.getInvoiceConfigByType(Collections.singletonList(configType));
                Map<Long,HxInvoiceConfig> invoiceConfigMapBefore = hxInvoiceConfigs.stream().filter(item -> item.getUniqueId() != null).collect(Collectors.toMap(HxInvoiceConfig::getUniqueId,item -> item));
                if (hxInvoiceConfigMap.containsKey(configType)){
//                    List<HxInvoiceConfig> configs = hxInvoiceConfigMap.get(configType).stream().collect(
//                            collectingAndThen(
//                                    toCollection(() -> new TreeSet<>(Comparator.comparing(HxInvoiceConfig::getContent))), ArrayList::new)
//                    );
                    for (HxInvoiceConfig hxInvoiceConfig : hxInvoiceConfigMap.get(configType)) {
                        if (hxInvoiceConfig.getUniqueId() == null){
                            hxInvoiceConfig.setUniqueId(SnowFlakeUtils.uniqueLong());
                            hxInvoiceConfig.setAddTime(System.currentTimeMillis());
                            hxInvoiceConfig.setCreator(user.getUserId());
                            hxInvoiceConfigMapper.insertSelective(hxInvoiceConfig);
                        }else {
                            HxInvoiceConfig config1 = hxInvoiceConfigMapper.getHxInvoiceConfigByUniqueId(hxInvoiceConfig.getUniqueId());
                            if (config1 != null && !config1.getContent().equals(hxInvoiceConfig.getContent())){
                                hxInvoiceConfig.setUpdater(user.getUserId());
                                hxInvoiceConfig.setModTime(System.currentTimeMillis());
                                hxInvoiceConfigMapper.updateByUniqueIdSelective(hxInvoiceConfig);
                            }
                        }
                    }
                    Map<Long,HxInvoiceConfig> invoiceConfigMap = hxInvoiceConfigMap.get(configType).stream()
                            .filter(item -> item.getUniqueId() != null)
                            .collect(Collectors.toMap(HxInvoiceConfig::getUniqueId,item -> item));
                    //删除
                    if (!MapUtils.isEmpty(invoiceConfigMapBefore)){
                        List<Long> deleteList = new ArrayList<>(invoiceConfigMapBefore.keySet());
                        deleteList.removeAll(new ArrayList<>(invoiceConfigMap.keySet()));
                        if (CollectionUtils.isNotEmpty(deleteList)){
                            deleteList.stream().forEach(item ->{
                                hxInvoiceConfigMapper.deleteByUniqueId(item);
                            });
                        }
                    }
                }else {
                    hxInvoiceConfigMapper.deleteByConfigType(configType);
                }

            }
            if (invoiceConfig.getType().equals(1)){
                refreshHxInvoiceStatus(invoiceConfig.getConfigList());
                return new ResultInfo(0,"保存配置刷新成功");
            }else {
                return new ResultInfo(0,"保存配置成功");
            }

        }catch (Exception e){
            logger.error("保存配置信息时发生错误", e);
            return ResultInfo.error("保存配置信息时发生错误");
        }

    }
    public void refreshHxInvoiceStatus(List<HxInvoiceConfig> hxInvoiceConfigs) {
        List<HxInvoice> hxInvoices = hxInvoiceMapper.getInExceptionList();
        //设置默认值
        initHxInvoiceConfig(hxInvoiceConfigs);
        Map<Integer, List<HxInvoiceConfig>> finalConfigMap = hxInvoiceConfigs.stream().collect(Collectors.groupingBy(HxInvoiceConfig::getConfigType));
        if (CollectionUtils.isNotEmpty(hxInvoices)){
            hxInvoices.parallelStream().forEach(item -> {
                hxInInvoiceConvert(item,finalConfigMap);
                if (checkInvoiceAberrant(item,finalConfigMap)) {
                    HxInvoice hxInvoice = hxInvoiceReCheck(item);
                    hxInvoice.setBuyerBank(item.getBuyerBank());
                    hxInvoice.setBuyerBankAccount(item.getBuyerBankAccount());
                    hxInvoice.setBuyerAddress(item.getBuyerAddress());
                    hxInvoice.setBuyerTel(item.getBuyerTel());
                    hxInvoiceMapper.updateByPrimaryKeySelective(hxInvoice);
                }
            });
        }
    }

    private void hxInInvoiceConvert(HxInvoice hxInvoice, Map<Integer,List<HxInvoiceConfig>> configMap) {
        List<HxInvoiceConfig> characters = getConfig(configMap,1);
        if (StringUtils.isNotBlank(hxInvoice.getBuyerAddress())) {
            String gfyhzh = filterExceptionCharacter(hxInvoice.getBuyerAddress(),characters);
            List<HxInvoiceConfig> phones = getConfig(configMap,5);
            if (getContent(phones,gfyhzh) != null){
                hxInvoice.setBuyerAddress(gfyhzh.replace(getContent(phones,gfyhzh),""));
                hxInvoice.setBuyerTel(getContent(phones,gfyhzh));
            }else if (StringUtils.isNotBlank(hxInvoice.getBuyerTel())){
                hxInvoice.setBuyerAddress(filterExceptionCharacter(hxInvoice.getBuyerAddress(),characters));
                hxInvoice.setBuyerTel(filterExceptionCharacter(hxInvoice.getBuyerTel(),characters));
            }else {
                hxInvoice.setBuyerAddress(hxInvoice.getBuyerAddress());
            }
        }
        if (StringUtils.isNotBlank(hxInvoice.getBuyerBank())) {
            String gfyhzh = filterExceptionCharacter(hxInvoice.getBuyerBank(),characters);
            List<HxInvoiceConfig> accounts = getConfig(configMap,3);
            if (getContent(accounts,gfyhzh) != null){
                hxInvoice.setBuyerBank(gfyhzh.replace(getContent(accounts,gfyhzh),""));
                hxInvoice.setBuyerBankAccount(getContent(accounts,gfyhzh));
            } else if (StringUtils.isNotBlank(hxInvoice.getBuyerBankAccount())){
                hxInvoice.setBuyerBank(filterExceptionCharacter(hxInvoice.getBuyerBank(),characters));
                hxInvoice.setBuyerBankAccount(filterExceptionCharacter(hxInvoice.getBuyerBankAccount(),characters));
            } else {
                hxInvoice.setBuyerBank(hxInvoice.getBuyerBank());
            }
        }
    }


    private HxInvoice hxInvoiceReCheck(HxInvoice hxInvoice){
        if (hxInvoice.getInvoiceType() == 0){
            return hxInvoice;
        }

        if (!HxInvoiceColorTypeEnum.BLUE_VALID.getColorType().equals(hxInvoice.getColorType())){
            hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.NEGATIVE_NUMBER.getStatus());
            return hxInvoice;
        }

        if (!checkInvoiceMatchTraderSupply(hxInvoice)){
            hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.UNCLAIM.getStatus());
            return hxInvoice;
        }
        hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.WAIT_RECORD.getStatus());
        return hxInvoice;
    }

    @Override
    public Integer deleteInvoiceConfig() {
        return hxInvoiceConfigMapper.deleteInvoiceConfig();
    }
}



