
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增联系人" scope="application" />

<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js"></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/orderstream/saleorder/add_contact_new.js?rnd=${resourceVersionKey}"></script>
<style>
    .warning {
        color: #fc5151;
        padding-top: 4px;
        clear: both;
    }
</style>
<div class="formpublic" style="padding-top: 10px;">
    <form method="post" class="layui-form" action="" id="myform">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    姓名：
                </label>
                <div class="layui-input-inline">
                    <input type="text" name="name" id="name"  autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline" style="vertical-align:top">
                <label class="layui-form-label">性别：</label>
                <div class="layui-input-inline" style="width: 300px">
                    <input type="radio" name="sex" value="1" title="男" >
                    <input type="radio" name="sex" value="0" title="女">
                    <input type="radio" name="sex" value="2" title="未知" checked="">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    手机：
                </label>
                <div class="layui-input-inline">
                    <input type="tel" name="mobile"  id="mobile" autocomplete="off" class="layui-input" maxlength="11">
                </div>
            </div>
            <div class="layui-inline" style="vertical-align:top;margin-left: 50px;">
                <div class="layui-input-inline" >
                    <input type="tel" name="mobile2" placeholder="手机2" id="mobile2" autocomplete="off" class="layui-input" maxlength="11">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">电话：</label>
                <div class="layui-input-inline">
                    <input type="text" name="telephone" id="telephone" autocomplete="off" class="layui-input" >
                </div>
            </div>
        </div>
        <%--<div class="layui-form-item">--%>
            <%--<div class="layui-inline">--%>
                <%--<label class="layui-form-label">所在部门：</label>--%>
                <%--<div class="layui-input-block">--%>
                    <%--<input type="radio" name="org" value="采购部" title="采购部" lay-filter="department">--%>
                    <%--<input type="radio" name="org" value="销售部" title="销售部" lay-filter="department">--%>
                    <%--<input type="radio" name="org" value="生产部" title="生产部" lay-filter="department">--%>
                    <%--<input type="radio" name="org" value="财务部" title="财务部" lay-filter="department">--%>
                    <%--<input type="radio" name="org" value="仓储部" title="仓储部" lay-filter="department">--%>
                    <%--<input type="radio" name="org" value="研发部" title="研发部" lay-filter="department">--%>
                    <%--<input type="radio" name="org" value="商务部" title="商务部" lay-filter="department">--%>
                    <%--<input type="radio" name="org" value="研究院" title="研究院" lay-filter="department">--%>
                    <%--<input type="radio" name="org" value="其他" title="其他" lay-filter="department">--%>
                    <%--<input type="text" name="department" id="department"  value="" autocomplete="off" class="layui-input" placeholder="请输入部门" style="width: 190px;display: inline-block;display: none">--%>
                <%--</div>--%>
            <%--</div>--%>
        <%--</div>--%>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">职位：</label>
                <div class="layui-input-block">
                    <c:if test="${customerNature == 465}">
                        <input type="radio" name="pos" value="老板" title="老板" lay-filter="position">
                        <input type="radio" name="pos" value="采购负责人" title="采购负责人" lay-filter="position">
                        <input type="radio" name="pos" value="采购专员" title="采购专员" lay-filter="position">
                        <input type="radio" name="pos" value="销售负责人" title="销售负责人" lay-filter="position">
                        <input type="radio" name="pos" value="销售专员" title="销售专员" lay-filter="position">
                        <input type="radio" name="pos" value="财务负责人" title="财务负责人" lay-filter="position">
                        <input type="radio" name="pos" value="财务专员" title="财务专员" lay-filter="position">
                        <input type="radio" name="pos" value="物流负责人" title="物流负责人" lay-filter="position">
                        <input type="radio" name="pos" value="物流专员" title="物流专员" lay-filter="position">
                        <input type="radio" name="pos" value="商务负责人" title="商务负责人" lay-filter="position">
                        <input type="radio" name="pos" value="商务专员" title="商务专员" lay-filter="position">
                        <input type="radio" name="pos" value="售后负责人" title="售后负责人" lay-filter="position">
                        <input type="radio" name="pos" value="售后专员" title="售后专员" lay-filter="position">
                        <input type="radio" name="pos" value="其他" title="其他" lay-filter="position">
                    </c:if>
                    <c:if test="${customerNature == 466}">
                        <input type="radio" name="pos" value="总经理" title="总经理" lay-filter="position">
                        <input type="radio" name="pos" value="院长" title="院长" lay-filter="position">
                        <input type="radio" name="pos" value="副院长" title="副院长" lay-filter="position">
                        <input type="radio" name="pos" value="采购负责人" title="采购负责人" lay-filter="position">
                        <input type="radio" name="pos" value="采购专员" title="采购专员" lay-filter="position">
                        <input type="radio" name="pos" value="科室主任" title="科室主任" lay-filter="position">
                        <input type="radio" name="pos" value="科室医生" title="科室医生" lay-filter="position">
                        <input type="radio" name="pos" value="护士长" title="护士长" lay-filter="position">
                        <input type="radio" name="pos" value="护士" title="护士" lay-filter="position">
                        <input type="radio" name="pos" value="运营负责人" title="运营负责人" lay-filter="position">
                        <input type="radio" name="pos" value="运营专员" title="运营专员" lay-filter="position">
                        <input type="radio" name="pos" value="财务负责人" title="财务负责人" lay-filter="position">
                        <input type="radio" name="pos" value="财务专员" title="财务专员" lay-filter="position">
                        <input type="radio" name="pos" value="设备科长" title="设备科长" lay-filter="position">
                        <input type="radio" name="pos" value="医工" title="医工" lay-filter="position">
                        <input type="radio" name="pos" value="库房负责人" title="库房负责人" lay-filter="position">
                        <input type="radio" name="pos" value="库房专员" title="库房专员" lay-filter="position">
                        <input type="radio" name="pos" value="咨询师" title="咨询师" lay-filter="position">
                    </c:if>
                    <input type="text" name="position" id="position" autocomplete="off" value="" class="layui-input" placeholder="请输入职位" style="width: 190px;display: inline-block;display: none">

                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">邮箱：</label>
                <div class="layui-input-inline">
                    <input type="text" name="email" id="email"  autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">QQ：</label>
                <div class="layui-input-inline">
                    <input type="text" name="qq" id="qq"  autocomplete="off" class="layui-input" maxlength="16">
                </div>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注：</label>
            <div class="layui-input-block">
                <textarea  class="layui-textarea" name="comments" id="comments" style="width: 80%;"></textarea>
            </div>
        </div>


        <input type="hidden" name="traderId" value="${traderId}" id="traderId">
        <input type="hidden" name="isDefault" value="0"/>
        <input type="hidden" name="traderType" value="1"/>
        <input type="hidden" name="indexId" value="${indexId}" id="indexId">
        <input type="hidden" name="formToken" value="${formToken}"/>
        <%--        <div class="add-tijiao tcenter">--%>
        <%--            <button type="submit">提交</button>--%>
        <%--            <button type="button" class="dele" id="close-layer">取消</button>--%>
        <%--        </div>--%>
        <div class="layui-form-item">
            <div class="" style="text-align:center">

                <button type="reset" class="layui-btn layui-btn-primary" id="cancel">取消</button>
                <button type="submit" class="layui-btn layui-btn-normal"  id="submit" lay-filter="lay-form">确定</button>
            </div>
        </div>
    </form>
</div>
<script>
    layui.use(['form', 'layedit', 'laydate','jquery'], function() {
        var form = layui.form,
            $ = layui.jquery
            , layer = layui.layer;

        form.on('radio(department)', function (data) {
            if (data.value === '其他'){
                $("#department").val("")
                $("#department").show();
            }else {
                delWarnTips("department");
                $("#department").val(data.value)
                $("#department").hide();
            }
        });

        form.on('radio(position)', function (data) {
            debugger
            if (data.value === '其他'){
                $("#position").val("")
                $("#position").show();
            }else {
                delWarnTips("position");
                $("#position").val(data.value)
                $("#position").hide();
            }
        });

        $("#cancel").click(function (){
            parent.layer.closeAll();
        })

    })
</script>
</body>

</html>

