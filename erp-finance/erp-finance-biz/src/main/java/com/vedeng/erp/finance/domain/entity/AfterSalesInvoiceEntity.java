package com.vedeng.erp.finance.domain.entity;

import java.io.Serializable;

import lombok.Data;

/**
 * 售后退票
 */
@Data
public class AfterSalesInvoiceEntity implements Serializable {
    private Integer afterSalesInvoiceId;

    private Integer afterSalesId;

    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 是否需退票0否1是
     */
    private Integer isRefundInvoice;

    /**
     * 退票状态0未退票1已退票
     */
    private Integer status;

    /**
     * 是否寄回发票 0否 1是
     */
    private Integer isSendInvoice;

    /**
     * 处理状态 0未处理 1已处理
     */
    private Integer handleStatus;

    /**
     * 操作处理原因
     */
    private String handleComments;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 发票号(采购仅退票)
     */
    private String invoiceNo;

    private static final long serialVersionUID = 1L;
}