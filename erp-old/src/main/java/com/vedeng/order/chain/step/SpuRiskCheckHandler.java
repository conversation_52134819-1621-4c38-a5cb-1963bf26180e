package com.vedeng.order.chain.step;

import com.vedeng.common.util.StringUtil;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.model.SimpleMedicalCategory;
import com.vedeng.goods.command.SpuAddCommand;
import com.vedeng.goods.dao.CoreSpuGenerateMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.order.chain.AbstractRiskHandlerStep;
import com.vedeng.order.chain.RiskEnum;
import com.vedeng.order.chain.RiskHandlerKeyConstant;
import com.vedeng.order.chain.model.RiskModel;
import com.wms.service.chain.HandlerStepContext;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName SpuRiskCheckHandler.java
 * @Description TODO spu风控信息校验
 * @createTime 2020年12月08日 15:47:00
 */
@Service
public class SpuRiskCheckHandler extends AbstractRiskHandlerStep {

    @Resource
    private CoreSpuMapper coreSpuMapper;

    @Resource
    private CoreSpuGenerateMapper coreSpuGenerateMapper;

    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Override
    public RiskModel doDealWith(HandlerStepContext context) {
        RiskModel riskModel = context.get(RiskHandlerKeyConstant.RISK_MODEL);
        String sku = context.get(RiskHandlerKeyConstant.SKU);
        //是否在《医疗器械分类目录》、产品名称（注册证/备案凭证）,规格、型号（注册证/备案凭证）
        boolean flag = true;
        SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(sku);

        if(simpleMedicalCategory == null || simpleMedicalCategory.getRegistrationNumberId() == null){
            //非医疗器械
            return riskModel;
        }

        CoreSpu spu = coreSpuMapper.getSpuBySku(sku);
        if(spu != null){
            CoreSpuGenerate generate = coreSpuGenerateMapper.selectByPrimaryKey(spu.getSpuId());
            if(generate == null){
                flag = false;
            }else if(generate.getMedicalInstrumentCatalogIncluded() == null){
                //是否在《医疗器械分类目录》
                flag = false;
            }else if(StringUtil.isBlank(generate.getSpuName())){
                //产品名称（注册证/备案凭证）
                flag = false;
            }else if(StringUtil.isBlank(generate.getSpecsModel())){
                //规格、型号（注册证/备案凭证）
                flag = false;
            }
        }else{
            flag = false;
        }

        if(!flag){
            Set<RiskEnum> riskEnumList = riskModel.getRiskEnumList();
            riskEnumList.add(RiskEnum.SPU_RISK);
            riskEnumList.add(RiskEnum.SKU_SPU_FIRST_RISK);
            riskModel.setRiskEnumList(riskEnumList);
            riskModel.setSpuRiskMessage("商品质量风控信息不完整");
            riskModel.setIsRisk(false);
            riskModel.setIsOneChek(false);

        }

        return riskModel;
    }
}
