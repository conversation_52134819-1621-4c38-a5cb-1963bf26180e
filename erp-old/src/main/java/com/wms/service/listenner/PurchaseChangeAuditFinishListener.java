package com.wms.service.listenner;

import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.SpecialDeliveryEnum;
import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderModifyApplyVo;
import com.vedeng.order.service.BuyorderService;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 采购单变更审核完成监听器
 */
@Service
public class PurchaseChangeAuditFinishListener extends AbstractErpListenner{

    public static Logger logger = LoggerFactory.getLogger(PurchaseChangeAuditFinishListener.class);

    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private DoPutService doPutService;

    @Override
    protected void doOnActionHappen(Object[] params) throws Exception {

        BuyorderModifyApplyVo buyorderModifyApply = (BuyorderModifyApplyVo) params[0];

        //是否审核通过
        boolean auditResult = Boolean.TRUE.equals(params[1]);

        //是否直发
        boolean isDirect = CommonConstants.ON.equals(buyorderModifyApply.getDeliveryDirect());


        logger.info("采购单:"+buyorderModifyApply.getBuyorderNo()+",变更审核完成处理----------------start");

        //原始的采购单
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderModifyApply.getBuyorderId());

        //审核不通过 重新下发原先的采购单
        if(!auditResult){
            logger.info("采购单:"+buyorderModifyApply.getBuyorderNo()+",变更审核不通过----------------");
//            sendPurchaseOrder(buyorder,buyorder.getLogisticsComments());
            doPutService.doPutPurchaseOrderMethod(buyorder.getBuyorderId(), buyorder.getLogisticsComments());
        }else {
            // TODO 审核通过 处理虚拟商品自动收货
            buyorderService.handleBuyOrderVirtualGoodsArrival(buyorder.getBuyorderId());

            //审核通过且直发 无需下发WMS
            if (isDirect) {
                logger.info("采购单:"+buyorderModifyApply.getBuyorderNo()+",变更审核通过,直发不下发WMS----------------");
            }else {
                logger.info("采购单:"+buyorderModifyApply.getBuyorderNo()+",变更审核通过,普法下发WMS----------------");
//                sendPurchaseOrder(buyorder,buyorderModifyApply.getLogisticsComments());
                doPutService.doPutPurchaseOrderMethod(buyorder.getBuyorderId(), buyorderModifyApply.getLogisticsComments());
            }
        }
    }


    /**
     * 下发采购入库单
     * @param buyorder
     * @return
     */
    /*private void sendPurchaseOrder(Buyorder buyorder, String logisticComment) throws Exception{

        PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();

        putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
        putPurchaseOrderDto.setDocNo(WmsCommonUtil.addTimestampForOrderNo(buyorder.getBuyorderNo()));
        putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(buyorder.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

        putPurchaseOrderDto.setSupplierId(buyorder.getTraderId());
        putPurchaseOrderDto.setSupplierName(buyorder.getTraderName());

        User user = userMapper.getUserByUserId(buyorder.getCreator());
        putPurchaseOrderDto.setPoReferenceA(user.getPositionName());
        putPurchaseOrderDto.setPoReferenceB(user.getUsername());

        putPurchaseOrderDto.setNotes(logisticComment);

        List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();

        List<BuyorderGoodsVo> buyorderGoodsVoListByBuyorderIdNoSpecial = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorder.getBuyorderId());
        buyorderGoodsVoListByBuyorderIdNoSpecial.stream()
                .forEach(buyOrderGood -> {

                    PutPurchaseOrderGoodsDto detailItem = new PutPurchaseOrderGoodsDto();
                    detailItem.setSku(buyOrderGood.getSku());

                    //采购单数量 - 采购单实际到货数量 - 已完结售后数量
                    detailItem.setOrderedQty(buyOrderGood.getNum()
                            - buyOrderGood.getArrivalNum()
                            - afterSalesService.getFinishAfterSaleNum(buyOrderGood.getBuyorderGoodsId()));
                    detailItem.setDedi04(buyOrderGood.getBuyorderGoodsId() + StringUtils.EMPTY);

                    if (SpecialDeliveryEnum.NEED_SPECIAL_DELIVERY.getCode().equals(
                            buyorderGoodsMapper.getSpecialDeliveryByRelatedId(buyOrderGood.getBuyorderGoodsId()))){
                        detailItem.setLotAtt07(buyorder.getBuyorderNo());
                    }

                    if(detailItem.getOrderedQty() > 0){
                        details.add(detailItem);
                    }
                });

        //入库单的详情
        putPurchaseOrderDto.setDetails(details);

        // 若采购订单中只包含特殊商品，则整个订单不下传WMS
        if(CollectionUtils.isEmpty(details)){
            return;
        }

        //wms采购入库单
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);
        wmsInterface.request(putPurchaseOrderDto);
    }*/

}
