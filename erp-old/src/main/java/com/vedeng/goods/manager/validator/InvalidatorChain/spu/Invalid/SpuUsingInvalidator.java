package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSpu;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SpuUsingInvalidatorError;

/**
 * .
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/2 9:24.
 * @author: Randy<PERSON>.
 */
@Service
public class SpuUsingInvalidator extends GoodsInvalidatorChain {


    @Resource
    CoreSpuMapper coreSpuMapper;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        CoreSpu coreSpu = coreSpuMapper.getSpuInfoBySpuNo(spuNo);
        if(coreSpu != null && coreSpu.getStatus() == 1){
            return ;
        }
        throw new InvalidatorChainException(SpuUsingInvalidatorError.getCode(),SpuUsingInvalidatorError.getMessage());
    }
}
