package com.vedeng.erp.kingdee.batch.repository;
import java.util.List;

import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/11/28 19:14
 **/
public interface BatchAttachmentDtoMapper {
    int deleteByPrimaryKey(Integer attachmentId);

    int insert(BatchAttachmentDto record);

    int insertSelective(BatchAttachmentDto record);

    BatchAttachmentDto selectByPrimaryKey(Integer attachmentId);

    int updateByPrimaryKeySelective(BatchAttachmentDto record);

    int updateByPrimaryKey(BatchAttachmentDto record);

    /**
     * 条件查询
     *
     * @param batchAttachmentDto
     * @return
     */
    List<BatchAttachmentDto> findByAll(BatchAttachmentDto batchAttachmentDto);

    /**
     * 入库单 验收附件查寻
     * @param batchAttachmentDto  attachmentFunction attachmentType relatedId 必传
     * @return
     */
    BatchAttachmentDto purchaseInfindByQuery(BatchAttachmentDto batchAttachmentDto);



}