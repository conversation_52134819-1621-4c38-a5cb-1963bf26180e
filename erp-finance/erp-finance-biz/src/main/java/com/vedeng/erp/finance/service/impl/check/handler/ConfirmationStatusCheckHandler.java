package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.mapper.InvoiceMapper;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 确认单状态校验
 */
@Service
@Slf4j
public class ConfirmationStatusCheckHandler extends AbstractCheckHandler {

    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;
    @Autowired
    private InvoiceMapper invoiceMapper;

    /**
     * 限制时间-2023-01-01 00:00:00
     */
    private static final Long LIMIT_TIME = 1672502400000L;
    /**
     * 线上订单
     */
    public static final List<Integer> ONLINE_ORDER = Collections.unmodifiableList(Lists.newArrayList(1, 5, 7));
    private static final BigDecimal LIMIT_AMOUNT_ONE = BigDecimal.valueOf(5000);
    private static final BigDecimal LIMIT_AMOUNT_TWO = BigDecimal.valueOf(50000);

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("确认单状态校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        Boolean invoiceApplyButtonShow = sysOptionDefinitionApiService.getInvoiceApplyButtonShow(InvoiceApplyCheckRuleEnum.CONFIRMATION_EFFECTIVE_ONLY_LAST);
        log.info("确认单状态校验-销售,确认单仅最后一次申请生效开关:{}", invoiceApplyButtonShow);
        SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderId(invoiceCheckRequestDto.getRelatedId());
        if (Objects.isNull(saleOrder)) {
            throw new ServiceException("确认单状态校验-销售,订单不存在");
        }
        BigDecimal realTotalAmount = saleOrder.getRealTotalAmount();
        if (invoiceApplyButtonShow) {
            if (CollUtil.isEmpty(invoiceCheckRequestDto.getDetailList())) {
                log.info("确认单状态校验-销售,确认单仅最后一次申请开关开启,申请明细为空");
                return;
            }
            BigDecimal salesOrderBlueInvoiceAmount = invoiceMapper.getSalesOrderBlueInvoiceAmount(invoiceCheckRequestDto.getRelatedId());
            BigDecimal salesOrderRedInvoiceAmount = invoiceMapper.getSalesOrderRedInvoiceAmount(invoiceCheckRequestDto.getRelatedId());
            BigDecimal applyTotalAmount = getDetailTotalAmount(invoiceCheckRequestDto);
            if (applyTotalAmount.compareTo(realTotalAmount.subtract(salesOrderBlueInvoiceAmount.subtract(salesOrderRedInvoiceAmount))) < 0) {
                log.info("确认单状态校验-销售,确认单仅最后一次申请开关开启,不是最后一次申请,applyTotalAmount:{},realTotalAmount:{},salesOrderBlueInvoiceAmount:{},salesOrderRedInvoiceAmount:{}",
                        applyTotalAmount, realTotalAmount, salesOrderBlueInvoiceAmount, salesOrderRedInvoiceAmount);
                return;
            }
        }
        BigDecimal totalAmount = saleOrder.getTotalAmount();
        Long addTime = saleOrder.getAddTime();
        Integer haveAccountPeriod = saleOrder.getHaveAccountPeriod();
        Integer orderType = saleOrder.getOrderType();
        // 订单创建日期小于2023-01-01 00:00:00
        // 或者账期类型为是
        // 或者订单原金额小于5000
        // 则确认单状态校验不限制
        if (addTime < LIMIT_TIME || ErpConstant.ONE.equals(haveAccountPeriod) || LIMIT_AMOUNT_ONE.compareTo(totalAmount) > 0) {
            log.info("确认单状态校验-销售,不限制,totalAmount:{},addTime:{},haveAccountPeriod:{},orderType:{}", totalAmount, addTime, haveAccountPeriod, orderType);
            return;
        }
        Integer confirmationFormAudit = saleOrder.getConfirmationFormAudit();
        log.info("确认单状态校验-销售,限制,confirmationFormAudit:{},addTime:{},haveAccountPeriod:{},totalAmount:{},orderType:{}", confirmationFormAudit, addTime, haveAccountPeriod, totalAmount, orderType);
        if (!ErpConstant.TWO.equals(confirmationFormAudit)) {
            String contractVerifyStatusStr = getConfirmationFormAuditStr(confirmationFormAudit);
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), contractVerifyStatusStr);
            buildResult(invoiceCheckResultDto, promptText);
        }
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
    }

    private String getConfirmationFormAuditStr(Integer confirmationFormAudit) {
        String confirmationFormAuditStr;
        switch (confirmationFormAudit) {
            case 0:
                confirmationFormAuditStr = "待提交审核";
                break;
            case 1:
                confirmationFormAuditStr = "审核中";
                break;
            case 2:
                confirmationFormAuditStr = "审核通过";
                break;
            case 3:
                confirmationFormAuditStr = "审核不通过";
                break;
            default:
                confirmationFormAuditStr = "";
                break;
        }
        return confirmationFormAuditStr;
    }

}
