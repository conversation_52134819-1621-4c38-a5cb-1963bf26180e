package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 银行流水结算方式表
 * <AUTHOR>
 */
@Getter
@Setter
public class BankBillSettlementEntity extends BaseEntity {

    /**
     * 主键
     */
    private Long bankBillSettlementId;

    /**
    * 银行流水ID
    */
    private Integer bankBillId;

    /**
    * 结算方式 1.现金结算 2.银行转账 3.银行承兑汇票 4.商业承兑汇票 5.微信 6.支付宝')
    */
    private Integer settlementMethod;
}