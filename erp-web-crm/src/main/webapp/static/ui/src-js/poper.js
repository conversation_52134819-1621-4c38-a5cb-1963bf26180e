Vue.component('ui-poper', {
    template: `<div class="ui-poper-wrap" ref="popwrap" :class="{hidden: !show}" :style="stylePosition">
        <slot></slot>
    </div>`,
    props: {
        show: {
            type: Boolean,
            default: false
        },
        errorable: {
            type: Boolean,
            default: false
        },
        position: {
            type: String,
            default: ''
        },
        el: {
            type: Object,
            default() {
                return null
            }
        },
        autoWidth: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            dropPosition: {},
            parent: null
        }
    },
    watch: {
        show() {
            if (this.show) {
                document.body.append(this.$refs.popwrap);
                setTimeout(() => {
                    this.calcPosition();
                }, 0)
            } else {
                this.$refs.popwrap.remove();
            }
        },
        position() {
            setTimeout(() => {
                this.calcPosition();
            }, 0)
        }
    },
    computed: {
        stylePosition() {
            let dropPosition = this.dropPosition;
            return 'width:' + dropPosition.width + ';left:' +  (dropPosition.left ? dropPosition.left + 'px' : 'auto') + ';right:' +  (dropPosition.right ? dropPosition.right + 'px;' : 'auto;') +  'top:' +  dropPosition.top + 'px;z-index:' + dropPosition.zindex
        }
    },
    mounted() {
        let parent = this.getScrollParent(this.$parent.$el);
        
        if(parent) {
            parent.addEventListener('scroll', this.calcPosition);
        }
        window.addEventListener('scroll', this.calcPosition);
        window.addEventListener('resize', this.calcPosition);

        this.parent = parent;
    },
    methods: {
        calcPosition() {
            if(!this.show) {
                return;
            }

            let el = this.el ||  this.$parent.$el;
            let elPosition = el.getBoundingClientRect();

            console.log(elPosition)

            let top = elPosition.top + elPosition.height;
            if(this.position === 'top') {
                top = elPosition.top
            }
            
            if(this.errorable) {
                top = top - 26;
            }
            console.log(elPosition.top, this.$refs.popwrap.offsetHeight, document.body.scrollHeight)
            if(elPosition.top + this.$refs.popwrap.offsetHeight > window.innerHeight) {
                top =  elPosition.top - this.$refs.popwrap.offsetHeight
            }
            
            let width = this.listWidth || (el.offsetWidth + 'px');

            if(this.position === 'middle') {
                let poperWidth = this.$refs.popwrap.offsetWidth;
                let elWidth = el.offsetWidth
                width = (poperWidth > elWidth + 82 ? poperWidth : elWidth + 82) + 'px';
                top =  elPosition.top;
            }

            if(this.autoWidth) {
                width = "auto"
            }

            if(window.innerWidth - elPosition.left < this.$refs.popwrap.offsetWidth + 10) {
                this.dropPosition = {
                    width: width,
                    top: top,
                    left: elPosition.left + elPosition.width - this.$refs.popwrap.offsetWidth,
                    zindex: this.parent === window ? 20 : 3000
                }
            } else {
                this.dropPosition = {
                    width: width,
                    top: top,
                    left: elPosition.left,
                    zindex: this.parent === window ? 20 : 3000
                }
            }

            
        },
        getScrollParent(element) {
            if (!element) {
                return null;
            }

            const overflowRegex = /(scroll|auto)/;
            const parent = element.parentElement;

            if (parent && overflowRegex.test(window.getComputedStyle(parent).overflow + window.getComputedStyle(parent).overflowY + window.getComputedStyle(parent).overflowX)) {
                return parent;
            }

            return this.getScrollParent(parent);
        }
    }
})