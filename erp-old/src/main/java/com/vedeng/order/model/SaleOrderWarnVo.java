package com.vedeng.order.model;

import com.vedeng.order.model.vo.SaleorderGoodsVo;
import lombok.Data;

import java.util.List;

/**
 * @Description:  采购预警处理
 * @Author:       davis
 * @Date:         2021/4/21 下午11:25
 * @Version:      1.0
 */
@Data
public class SaleOrderWarnVo {

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 预警状态
     */
    private Integer warnStatus;

    /**
     * 订单号
     */
    private String saleorderNo;

    /**
     * 时效状态
     */
    private Integer aging;

    /**
     * 预警等级
     */
    private Integer warnLevel;

    /**
     * 是否预警
     */
    private Integer isWarn;

    /**
     * 产品明细集合
     */
    private List<SaleorderGoodsVo> saleorderGoodsVoList;
}
