package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 10:58
 */
@Getter
@Setter
public class BatchSupplierFinanceDto extends BatchBaseDto {

    /**
     * TRADER_SUPPLIER_FINANCE_ID,
     */
    private Integer traderSupplierFinanceId;

    /**
     * 供应商ID
     */
    private Integer traderSupplierId;

    /**
     * 供应商名称
     */
    private String traderName;

    /**
     * 供应商类别
     */
    private String traderTypeName;

    /**
     * 最大时间
     */
    private Date beginTime;

    /**
     * 最小时间
     */
    private Date endTime;

    /**
     * 开始时间戳
     */
    private Long beginTimestamp;

    /**
     * 结束时间戳
     */
    private Long endTimestamp;
}