@import (less) '../common.css';

@keyframes loading {
    0% {
        transform: rotate(0deg)
    }

    100% {
        transform: rotate(360deg)
    }
}

.vd-ui-button {
    font-size: 12px;
    padding: 5px 14px;
    text-align: center;
    border-radius: 3px;
    border: 1px solid @Gray-7;
    display: inline-block;
    height: max-content;
    background-color: @Gray-2;
    color: @Text-4;
    transition: all .1s linear;
    cursor: pointer;
    position: relative;

    .vd-ui_icon {
        position: relative;
        top: 2px;
        font-size: 16px;
        margin-right: 3px;
        display: inline-block;
        line-height: 1;
        margin-left: -5px;
        vertical-align: -1px;
    }

    .loading {
        animation: loading 1.8s linear infinite;
    }

    &.vd-ui-button--large {
        font-size: 16px;
        padding: 7.5px 19px;

        .vd-ui_icon {
            font-size: 18px;
            margin-right: 7px;
        }
    }

    &.vd-ui-button--middle {
        font-size: 14px;
        padding: 5px 14px;

        .vd-ui_icon {
            font-size: 16px;
        }
    }

    &.vd-ui-button--small {
        font-size: 12px;
        padding: 3px 9px;

        .vd-ui_icon {
            font-size: 14px;
        }
    }

    &:hover {
        background-color: @Gray-3;
    }

    &:active {
        background-color: @Gray-4;
    }

    &.is-disabled {
        cursor: not-allowed;
        color: @Text-2;
        border-color: @Gray-5;
        background-color: @Gray-2;
    }

    // &:focus {
    //     background-color: @Gray-4;
    // }
    &.vd-ui-button--primary {
        border-color: @Brand-6;
        background-color: @Brand-6;
        color: #ffffff;

        &:hover {
            border-color: @Brand-7;
            background-color: @Brand-7;
        }

        &:active {
            border-color: @Brand-8;
            background-color: @Brand-8;
            transition: all .1s linear;
        }

        &.is-disabled {
            cursor: not-allowed;
            border-color: @Brand-3;
            background-color: @Brand-3;
        }

        &.is-loading {
            cursor: wait;
            border-color: @Brand-3;
            background-color: @Brand-3;
        }
    }

    &.vd-ui-button--success {
        border-color: @Green-6;
        background-color: @Green-6;
        color: #ffffff;

        &:hover {
            border-color: @Green-7;
            background-color: @Green-7;
        }

        &:active {
            border-color: @Green-8;
            background-color: @Green-8;
            transition: all .1s linear;
        }

        &.is-disabled {
            cursor: not-allowed;
            border-color: @Green-3;
            background-color: @Green-3;
        }
    }

    &.vd-ui-button--warning {
        border-color: @OrangeRed-6;
        background-color: @OrangeRed-6;
        color: #ffffff;

        &:hover {
            border-color: @OrangeRed-7;
            background-color: @OrangeRed-7;
        }

        &:active {
            border-color: @OrangeRed-8;
            background-color: @OrangeRed-8;
            transition: all .1s linear;
        }

        &.is-disabled {
            cursor: not-allowed;
            border-color: @OrangeRed-3;
            background-color: @OrangeRed-3;
        }
    }

    &.vd-ui-button--danger {
        border-color: @Crimson-6;
        background-color: @Crimson-6;
        color: #ffffff;

        &:hover {
            border-color: @Crimson-7;
            background-color: @Crimson-7;
        }

        &:active {
            border-color: @Crimson-8;
            background-color: @Crimson-8;
            transition: all .1s linear;
        }

        &.is-disabled {
            cursor: not-allowed;
            border-color: @Crimson-3;
            background-color: @Crimson-3;
        }
    }

    &.vd-ui-button--text {
        border: none;
        background-color: transparent;

        &:hover {
            color: @Brand-6;
        }

        &.is-disabled {
            cursor: not-allowed;
            color: @Text-2;
        }
    }

    &.vd-ui-button--danger-text {
        border: none;
        color: @Crimson-6;
        background-color: transparent;

        &:hover {
            color: @Crimson-7;
        }

        &.is-disabled {
            cursor: not-allowed;
            color: @Crimson-3;
        }
    }

    &.vd-ui-button--success-text {
        border: none;
        background-color: transparent;
        color: @Green-6;

        &:hover {
            color: @Green-7;
        }

        &.is-disabled {
            cursor: not-allowed;
            color: @Green-3;
        }
    }

    &.vd-ui-button--link-text {
        border: none;
        color: @Brand-6;
        background-color: transparent;

        &:hover {
            color: @Brand-7;
        }

        &.is-disabled {
            cursor: not-allowed;
            color: @Brand-3;
        }
    }

}

.vd-ui-select-button {
    display: flex;
    align-items: center;
    position: relative;
    margin-right: 10px;

    .ui-select-btn-txt {
        background: #0099FF;
        color: #fff;
        border-radius: 3px 0 0 3px;
        border-right: 1px solid #0087E0;
        cursor: pointer;
        font-size: 12px;

        &:hover {
            background: #0087E0;
        }

        &>div {
            padding: 6px 10px;
        }
    }

    .ui-select-btn-more {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        background: #0099FF;
        border-radius: 0 3px 3px 0;
        cursor: pointer;

        .icon-down {
            font-size: 16px;
            line-height: 1;
            transition: transform .22s ease;
        }

        &:hover {
            background: #0087E0;
        }
    }

    .ui-select-btn-drop {
        background: #fff;
        border-radius: 3px;
        border: 1px solid #BABFC2;    
        box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;  
        position: absolute;
        top: 30px;
        left: 0; 
        z-index: 9;
        width: 100%;
        padding: 5px 0;
        display: none;
        
        >div {
            padding: 6px 10px;
            cursor: pointer;

            &:hover {
                background: #f5f7fa;
            }
        }
    }

    &.open {
        .ui-select-btn-drop {
            display: block;
        }

        .ui-select-btn-more {
            .icon-down {
                transform: rotate(180deg);
            }
        }
    }
}

.vd-ui-select-link {
    position: relative;

    .ui-select-link-trigger {
        display: flex;
        align-items: center;
        color: #09f;
        cursor: pointer;
        font-size: 12px;

        &:hover {
            color: #f60;
        }

        .icon-down {
            font-size: 16px;
            line-height: 1;
            transition: transform .22s ease;
            margin-left: 3px;
        }
    }

    .ui-select-btn-drop {
        background: #fff;
        border-radius: 3px;
        border: 1px solid #BABFC2;    
        box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;  
        position: absolute;
        top: 18px;
        left: 0; 
        z-index: 9;
        width: 100%;
        padding: 5px 0;
        display: none;
        
        >div {
            padding: 6px 10px;
            cursor: pointer;

            &:hover {
                background: #f5f7fa;
            }
        }
    }

    &.open {
        .ui-select-btn-drop {
            display: block;
        }

        .icon-down {
            transform: rotate(180deg);
        }
    }

}
