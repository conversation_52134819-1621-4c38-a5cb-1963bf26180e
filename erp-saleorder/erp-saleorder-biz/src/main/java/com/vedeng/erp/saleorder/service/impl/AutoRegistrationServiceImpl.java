package com.vedeng.erp.saleorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.RegisterPlatformEnum;
import com.vedeng.common.exception.BusinessException;
import com.vedeng.erp.saleorder.api.AutoRegistrationService;
import com.vedeng.erp.saleorder.constant.CustomerNatureTypeEnum;
import com.vedeng.erp.saleorder.enums.WebAccountFromEnum;
import com.vedeng.trader.dao.*;
import com.vedeng.trader.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/12/27 8:53
 **/
@Service("autoRegistrationService")
@Slf4j
public class AutoRegistrationServiceImpl implements AutoRegistrationService {


    @Resource
    private TraderContactMapper traderContactMapper;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private WebAccountMapper webAccountMapper;

    @Resource
    private RTraderJUserMapper rTraderJUserMapper;

    @Autowired
    private ErpMsgProducer erpMsgProducer;

    @Override
    public boolean checkTraderContactRegistration(String mobile) {

        if (StringUtils.isEmpty(mobile)) {
            log.error("自动注册校验：联系人手机号为空！");
            throw new BusinessException("联系人手机号为空！");
        }
        WebAccount webAccount = new WebAccount();
        webAccount.setMobile(mobile);
        // 存在则已注册
        int mobileCount = webAccountMapper.getMobileCount(webAccount);
        return mobileCount > 0;
    }

    @Override
    public boolean sendFrontDeskRegistration(String registrationData) {

        // --------------MQ-------------
        log.info("自动注册推送前台消息start：{}",registrationData);
        erpMsgProducer.sendMsg(RabbitConfig.AUTOMATICALLY_REGISTER_EXCHANGE,RabbitConfig.AUTOMATICALLY_REGISTER_ROUTINGKEY,registrationData);
        log.info("自动注册推送前台消息end");
        return true;
    }

    @Override
    public boolean checkTraderBelongAndNature(Integer traderId) {

        log.info("自动注册==》客户信息校验，traderId：{}",traderId);
        if (Objects.isNull(traderId)) {
            throw new BusinessException("客户traderId为空！");
        }
        Trader trader = traderMapper.selectByPrimaryKey(traderId);
        if (!Objects.isNull(trader)) {
            Integer belongPlatform = trader.getBelongPlatform();
            if (belongPlatform == null) {
                log.info("客户的归属平台为空：traderId：{}",traderId);
                return false;
            }
            TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(traderId);
            if (null == traderCustomer) {
                return false;
            }
            Integer customerNature = traderCustomer.getCustomerNature();
            if (customerNature == null) {
                return false;
            }
            log.info("客户信息校验，belongPlatform：{}，customerNature：{}",belongPlatform,customerNature);
            if (Constants.ONE.equals(belongPlatform) && CustomerNatureTypeEnum.DISTRIBUTION.getType().equals(customerNature)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Integer relatedSellerByTraderId(Integer traderId) {

        if (Objects.isNull(traderId)) {
            log.error("客户id为空！");
            return null;
        }
        RTraderJUser userByTraderId = rTraderJUserMapper.getUserByTraderId(traderId);
        if (userByTraderId != null) {
            return userByTraderId.getUserId();
        }
        return null;
    }

    @Override
    public void doRegistration(Integer traderContactId,boolean isSaleOrder) {

        if (Objects.isNull(traderContactId)) {
            return;
        }
        // 订单的客户所属平台为贝登医疗 客户性质为分销
        TraderContact traderContact = traderContactMapper.selectByPrimaryKey(traderContactId);
        if (Objects.isNull(traderContact)||StringUtils.isEmpty(traderContact.getMobile())||Objects.isNull(traderContact.getTraderId())) {
            return;
        }
        if (this.checkTraderBelongAndNature(traderContact.getTraderId())) {
            Trader traderByTraderId = traderMapper.getTraderByTraderId(traderContact.getTraderId());
            log.info("自动注册==》客户traderId:{}符合所属平台为贝登医疗且客户性质为分销",traderByTraderId.getTraderId());
            // 1. 判断联系人是否注册 2. 未注册推送前台注册
            if (!this.checkTraderContactRegistration(traderContact.getMobile())) {
                log.info("自动注册==》客户联系人traderContactId:{},mobile：{}未注册",traderContactId,traderContact.getMobile());
                WebAccount webAccount = new WebAccount();
                if (traderByTraderId != null) {
                    webAccount.setCompanyName(traderByTraderId.getTraderName());
                }
                webAccount.setMobile(traderContact.getMobile());
                webAccount.setTraderId(traderContact.getTraderId());
                webAccount.setTraderContactId(traderContactId);
                if (isSaleOrder) {
                    webAccount.setFrom(WebAccountFromEnum.AUTO_ORDER.getCode());
                } else {
                    webAccount.setFrom(WebAccountFromEnum.AUTO_BUSINESS.getCode());
                }
                webAccount.setRegisterPlatform(RegisterPlatformEnum.BD.getRegistNo());
                String registerData = JSON.toJSONString(webAccount);
                log.info("自动注册==》联系人未注册发起注册：{}", registerData);
                this.sendFrontDeskRegistration(registerData);
            }
        }
    }
}
