package com.rabbitmq;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class MarketMsgProducer extends MsgProducer implements InitializingBean {

    @Autowired
    @Qualifier(value = "marketRabbitTemplate")
    private RabbitTemplate marketRabbitTemplate;

    @Override
    public void afterPropertiesSet() throws Exception {
        marketRabbitTemplate.setConfirmCallback(this);
        setRabbitTemplate(marketRabbitTemplate);
    }
}
