package com.vedeng.order.model;

import java.io.Serializable;

/**
 * 未成商机原因实体类
 * <AUTHOR>
 */
public class NotCreateBussinessChanceReason implements Serializable {
    private Integer notCreateBussinessChanceReasonId;

    /**
     * 客户Id
     */
    private Integer traderId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 未成商机原因,字典表
     */
    private Integer reasonId;

    /**
     * 详细原因
     */
    private String reasonDesc;

    /**
     * 对接员工Id
     */
    private Integer serviceUserId;

    /**
     * 来源，1为IM
     */
    private Byte source;

    /**
     * 是否删除，0未删除，1删除
     */
    private Byte isDelete;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 修改时间
     */
    private Long updateTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 修改人
     */
    private Integer updator;

    private static final long serialVersionUID = 1L;

    public Integer getNotCreateBussinessChanceReasonId() {
        return notCreateBussinessChanceReasonId;
    }

    public void setNotCreateBussinessChanceReasonId(Integer notCreateBussinessChanceReasonId) {
        this.notCreateBussinessChanceReasonId = notCreateBussinessChanceReasonId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getReasonId() {
        return reasonId;
    }

    public void setReasonId(Integer reasonId) {
        this.reasonId = reasonId;
    }

    public String getReasonDesc() {
        return reasonDesc;
    }

    public void setReasonDesc(String reasonDesc) {
        this.reasonDesc = reasonDesc;
    }

    public Integer getServiceUserId() {
        return serviceUserId;
    }

    public void setServiceUserId(Integer serviceUserId) {
        this.serviceUserId = serviceUserId;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Byte getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }
}