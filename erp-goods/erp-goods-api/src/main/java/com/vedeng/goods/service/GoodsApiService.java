package com.vedeng.goods.service;

import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.goods.dto.*;
import com.vedeng.goods.query.RelatedSkuQuery;
import com.vedeng.goods.vo.CoreSkuVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @title: GoodsApiService
 * <AUTHOR>
 * @Date: 2021/10/24 8:42
 */

public interface GoodsApiService {

    List<GoodsDTO> queryGoodsForWindow(QueryGoodsDTO queryGoodsDTO);

    /**
     * 根据sku信息查询所有sku（全量，包含禁用）
     */
    List<CoreSkuVo> selectAllSkuByKeywords(RelatedSkuQuery relatedSkuQuery);

    /**
     * 根据skuNo 判断集合中的sku是否有独家产品
     *
     * @param skuNos skuNo
     * @return boolean
     */
    boolean querySkusHaveExclusive(List<String> skuNos);

    /**
     * 根据skuId查询信息
     *
     * @param skuId skuId
     * @return CoreSkuVo
     */
    CoreSkuVo getGoodsInfoBySkuId(Integer skuId);

    List<CoreSkuVo> getGoodsInfoBySkuNos(List<String> skuNos);

    /**
     * 查询所有虚拟商品信息
     *
     * @param status 是否包含禁用商品，1：不包含 0：包含
     * @return 虚拟商品信息集合
     */
    List<CoreSkuVo> getAllVirtualGoodsInfo(Integer status);

    /**
     * 产品经理产品助理
     *
     * @param skus 商品
     * @return List<ProductManageAndAsistDto> 信息
     */
    List<ProductManageAndAsistDto> batchQueryProductManageAndAsist(List<String> skus);

    /**
     * 根据skuId查询 金蝶商品相关联的信息
     *
     * @param skuId skuId
     * @return KingDeeSkuInfoDto
     */
    KingDeeSkuInfoDto getSkuInfoBySkuId(Integer skuId);

    /**
     * 构造金蝶物料信息 入参
     *
     * @param skuInfoDto 查询到的sku信息
     * @return KingDeeMaterialDto
     */
    KingDeeMaterialDto getPushSkuInfoToKingDee(KingDeeSkuInfoDto skuInfoDto);

    /**
     * 获取审核通过的sku总数
     *
     * @return sku总数
     */
    Integer getCheckPassSkuNum();

    /**
     * 分批获取sku初始化信息推送金蝶
     *
     * @param limit 分页参数
     * @return List<KingDeeSkuInfoDto>
     */
    List<KingDeeSkuInfoDto> getBatchInitSkuToKingDee(Integer limit);

    /**
     * 查询所有的skuId
     *
     * @return 所有skuId集合
     */
    List<Integer> getAllSkuId();

    /**
     * 根据skuIds集合查询
     * @param skuIds skuIds
     * @return List<CoreSkuVo>
     */
    List<CoreSkuVo> findBySkuBySkuIds(List<Integer> skuIds);

    /**
     * 查找所有特殊虚拟商品（固定+不可见）
     * @return
     */
    List<Integer> findAllSpecialSkuIds();


    String getSkuTaxNo(Integer goodsId);

    /**
     * 获取方案和分类
     */
    List<SkuSceneDto> getSceneAndCategory();


    Map<String, Object> skuTipMap(String skuNo);
}
