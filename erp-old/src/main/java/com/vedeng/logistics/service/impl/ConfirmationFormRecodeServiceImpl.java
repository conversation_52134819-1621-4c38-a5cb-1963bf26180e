package com.vedeng.logistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.logistics.dao.ConfirmationBatchesRelationMapper;
import com.vedeng.logistics.dao.ConfirmationFormRecodeMapper;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.OutboundBatchesRecodeMapper;
import com.vedeng.logistics.model.ConfirmationBatchesRelation;
import com.vedeng.logistics.model.ConfirmationFormRecode;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.OutboundBatchesRecode;
import com.vedeng.logistics.service.ConfirmationFormRecodeService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import lombok.SneakyThrows;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 物流回传确认单service
 */
@Service("confirmationFormRecodeService")
public class ConfirmationFormRecodeServiceImpl extends BaseServiceimpl implements ConfirmationFormRecodeService {

    @Autowired
    @Qualifier("confirmationFormRecodeMapper")
    private ConfirmationFormRecodeMapper confirmationFormRecodeMapper;

    @Autowired
    @Qualifier("saleorderMapper")
    private SaleorderMapper saleorderMapper;

    @Resource
    private ExpressMapper expressMapper;


    @Autowired
    private UserService userService;

    @Autowired
    @Qualifier("confirmationBatchesRelationMapper")
    private ConfirmationBatchesRelationMapper confirmationBatchesRelationMapper;

    @Autowired
    @Qualifier("outboundBatchesRecodeMapper")
    private OutboundBatchesRecodeMapper outboundBatchesRecodeMapper;
    @Resource
    VerifiesRecordService verifiesRecordService;
    @Resource
    ProcessEngine processEngine;
    @Resource
    ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("attachmentMapper")
    private AttachmentMapper attachmentMapper;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    /**
     * 销售订单(账期类型)付款计划
     */
    public static final List PAYMENT_TYPE_FOR_PAYMENT_DAYS = Collections.unmodifiableList(Lists.newArrayList(
            OrderConstant.PREPAY_80_PERCENT,
            OrderConstant.PREPAY_50_PERCENT ,
            OrderConstant.PREPAY_30_PERCENT,
            OrderConstant.PREPAY_0_PERCENT));

    @Override
    @Transactional
    public void deleteConfirmationById(Integer confirmationId) {
        //是否改变订单状态
        boolean isChangeSaleOrder = false;
        logger.info("删除确认单:{}",confirmationId);
        //获取确认单批次关系表中所有关系
        ConfirmationBatchesRelation confirmationBatchesRelation = new ConfirmationBatchesRelation();
        confirmationBatchesRelation.setConfirmationId(confirmationId);
        List<ConfirmationBatchesRelation> confirmationBatchesRelations = confirmationBatchesRelationMapper.selectAllByRelation(confirmationBatchesRelation);
        //获取所有确认单关联批次
        List<String> batchList = confirmationBatchesRelations.stream().map(ConfirmationBatchesRelation::getBatchNo).collect(Collectors.toList());
        //删除操作
        ConfirmationFormRecode confirmationFormRecode = confirmationFormRecodeMapper.selectByPrimaryKey(confirmationId);
        Integer saleOrderId = confirmationFormRecode.getSaleOrderId();
        confirmationFormRecode.setIsEnable(CommonConstants.IS_DELETE_1);
        confirmationFormRecodeMapper.updateByPrimaryKey(confirmationFormRecode);
        ArrayList<Integer> confirmationIds = new ArrayList<>(1);
        confirmationIds.add(confirmationId);
        confirmationBatchesRelationMapper.deleteByConfirmationIdBatch(confirmationIds);


        //是否改变批次状态
        for (String batchNo : batchList) {
            //获取批次确认单关系，根据批次，如果没有 则批次改变为未上传状态
            ConfirmationBatchesRelation v1 = new ConfirmationBatchesRelation();
            v1.setBatchNo(batchNo);
            List<ConfirmationBatchesRelation> v2 = confirmationBatchesRelationMapper.selectAllByRelation(v1);
            if (v2.size()==0){
                outboundBatchesRecodeMapper.updateUploadStatusByBatchNo(batchNo,CommonConstants.STATUS_0);
                //批次状态被改变，销售订单为部分上传状态
                isChangeSaleOrder = true;

            }
        }

        //销售订单状态的改变
        if (isChangeSaleOrder){
            saleorderMapper.updateConfirmationFormUploadById(saleOrderId,CommonConstants.STATUS_1);
        }
        //判断是否是批次都为未上传，如果是的，销售订单改为未上传
        List<String> batchNos = getBatchNos(saleOrderId);
        List<OutboundBatchesRecode> allByBatchNoList = new ArrayList<>();
        if (batchNos.size()>0){
            allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNos);
        }

        //是否改变订单状态为未上传
        List<Integer> ups = allByBatchNoList.stream().map(OutboundBatchesRecode::getUploadStatus).collect(Collectors.toList());
        if (ups.size()>0&&!ups.contains(1)){
            saleorderMapper.updateConfirmationFormUploadById(saleOrderId,CommonConstants.STATUS_0);
        }

    }

    @Override
    public ResultInfo confirmationApprovedOrRejected(HttpServletRequest request, String taskId, String comment, Boolean pass) {
        User user = getSessionUser(request);
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        variables.put("updater", user.getUserId());
        variables.put("autoCheckAptitude", false);
        // 审批操作
        try {
            int statusInfo = 0;
            if (Boolean.FALSE.equals(pass)) {
                // 如果审核不通过
                statusInfo = 2;
                // 回写数据的表在db中
                variables.put("db", 2);
                verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
            }
            ResultInfo<?> complementStatus = null;
            if (Boolean.TRUE.equals(pass)) {
                TaskService taskService = processEngine.getTaskService();
                HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
                String processInstanceId = null;
                HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery()
                        .taskId(taskId).singleResult();
                processInstanceId = historicTaskInstance.getProcessInstanceId();
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(processInstanceId).singleResult();
                String businessKey = historicProcessInstance.getBusinessKey();
                List<HistoricVariableInstance> historicVariableInstanceList = historyService
                        .createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).list();
                // 把list转为Map
                Map<String, Object> variablesMap = new HashMap<>();
                for (HistoricVariableInstance hvi : historicVariableInstanceList) {
                    variablesMap.put(hvi.getVariableName(), hvi.getValue());
                }
                // 获取当前活动节点
                Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
                if (taskInfo == null) {
                    return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
                }
            }
            complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
            // 如果未结束添加审核对应主表的审核状态
            if (complementStatus != null) {
                // 审核节点操作失败
                if (complementStatus.getCode().equals(-1)) {
                    return complementStatus;
                }
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
                }
            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("complementTaskParallel:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }
    private boolean isSale(CurrentUser sessionUser) {
        //3.判断是否是销售
        Integer positType = sessionUser.getPositType();
        if (positType !=null &&
                !String.valueOf(positType).equals(String.valueOf(SysOptionConstant.ID_310)) ) {
            return false;
        }
        return true;
    }

    private boolean isLogistic(CurrentUser sessionUser) {
        //3.判断是否是物流
        Integer positType = sessionUser.getPositType();
        if (positType !=null &&
                !String.valueOf(positType).equals(String.valueOf(SysOptionConstant.ID_313)) ) {
            return false;
        }
        return true;
    }
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean uploadConfirmationForm(CurrentUser curr_user, ConfirmationFormRecode confirmationFormRecode, HttpServletRequest request) {
        logger.info("开始上传确认单,入参信息为:{}", JSON.toJSONString(confirmationFormRecode));
        long timeMillis = System.currentTimeMillis();
        List<FileInfo> attachments = confirmationFormRecode.getFileInfos();
        Integer saleOrderId = confirmationFormRecode.getSaleOrderId();
        List<String> fileName = attachments.stream().map(FileInfo::getFileName).collect(Collectors.toList());
        String fileNameBetter = String.join(",", fileName);
        //保存确认单
        confirmationFormRecode.setFileId("待填充");
        //确认单命名规则   文件名称加上时间
//        String fileNameBest = getFileNameBest(attachments);

        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CONFIRMATION_FORM_NAME, NoGeneratorBean.builder().dateFormat("yyMMddHHmmss").randomDigits(5).build());
        String code = new BillNumGenerator().distribution(billGeneratorBean);
        confirmationFormRecode.setConfirmationName(code);
        //判断职位
        Boolean logisticeFlagByUserId = isLogistic(curr_user);
        if (logisticeFlagByUserId){
            confirmationFormRecode.setConfirmationType(CommonConstants.CONFIRMATION_TYPE_2);
        }
        boolean sale = isSale(curr_user);
        if (sale){
            confirmationFormRecode.setConfirmationType(CommonConstants.CONFIRMATION_TYPE_1);
        }
        if (sale==false&&logisticeFlagByUserId==false){
            confirmationFormRecode.setConfirmationType(CommonConstants.CONFIRMATION_TYPE_4);
        }
        confirmationFormRecode.setFileName(fileNameBetter);
        confirmationFormRecode.setIsEnable(CommonConstants.IS_DELETE_0);
        confirmationFormRecode.setAddTime(timeMillis);
        confirmationFormRecode.setModTime(timeMillis);
        confirmationFormRecode.setCreator(curr_user.getId());
        confirmationFormRecode.setUpdater(curr_user.getId());
        confirmationFormRecodeMapper.insertSelective(confirmationFormRecode);
        Integer id = confirmationFormRecode.getId(); //获取插入后的新增id

        //保存确认单和批次关系
        List<OutboundBatchesRecode> batchesRecodes = confirmationFormRecode.getBatchesRecodes();
        batchesRecodes.stream().forEach(x->{
            //批次状态改变为已经上传
            outboundBatchesRecodeMapper.updateUploadStatusById(x.getId(),CommonConstants.STATUS_1);

            //驳回重新上传逻辑
            if (x.getAuditStatus()!=null&&x.getAuditStatus().equals(0)){
                outboundBatchesRecodeMapper.updateAuditStatusById(x.getId(),Constants.THREE);
                outboundBatchesRecodeMapper.updateCommentsById(x.getId());
                saleorderMapper.updateConfirmationFormAuditById(saleOrderId,Constants.ZERO);
            }

        });

        for (OutboundBatchesRecode outboundBatchesRecode : batchesRecodes) {
            ConfirmationBatchesRelation confirmationBatchesRelation = new ConfirmationBatchesRelation();
            confirmationBatchesRelation.setSaleorderId(saleOrderId);
            confirmationBatchesRelation.setConfirmationId(id);
            confirmationBatchesRelation.setBatchNo(outboundBatchesRecode.getBatchNo());
            confirmationBatchesRelation.setIsEnable(CommonConstants.IS_DELETE_0);
            confirmationBatchesRelation.setAddTime(timeMillis);
            confirmationBatchesRelation.setModTime(timeMillis);
            confirmationBatchesRelation.setCreator(curr_user.getId());
            confirmationBatchesRelation.setUpdater(curr_user.getId());
            confirmationBatchesRelationMapper.insert(confirmationBatchesRelation);
        }

        ArrayList<String> integers = new ArrayList<>();
        List<FileInfo> fileInfos = confirmationFormRecode.getFileInfos();
        //保存附件信息
        for (FileInfo f : fileInfos) {
            Attachment attachment = new Attachment();
            attachment.setDomain(f.getDomain());
            attachment.setName(f.getFileName());
            attachment.setFilePath(f.getFilePath());
            attachment.setUri(f.getOssUrl());
            attachment.setSuffix(f.getPrefix());
            attachment.setOssResourceId(f.getOssResourceId());
            attachment.setCreator(curr_user.getId());
            attachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_980);
            attachment.setAttachmentFunction(CommonConstants.ATTACHMENT_FUNCTION_1020);
            attachment.setRelatedId(id);
            attachment.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE);
            attachment.setAddTime(timeMillis);
            attachmentMapper.insertSelective(attachment);
            integers.add(String.valueOf(attachment.getAttachmentId()));
        }
        String attaIds = String.join(",", integers);
        confirmationFormRecode.setFileId(attaIds);
        confirmationFormRecodeMapper.updateByPrimaryKey(confirmationFormRecode);
        //a)订单为全部发货状态；
        return ((ConfirmationFormRecodeService) AopContext.currentProxy()).confirmationOrderAudit(request, timeMillis, saleOrderId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean uploadConfirmationFormForAutoConfirmOrder(ConfirmationFormRecode confirmationFormRecode) {
        Integer userId= 1;//用户默认使用admin账号
        logger.info("开始上传确认单,入参信息为:{}", JSON.toJSONString(confirmationFormRecode));
        long timeMillis = System.currentTimeMillis();
        List<FileInfo> attachments = confirmationFormRecode.getFileInfos();
        Integer saleOrderId = confirmationFormRecode.getSaleOrderId();
        List<String> fileName = attachments.stream().map(FileInfo::getFileName).collect(Collectors.toList());
        String fileNameBetter = String.join(",", fileName);
        //保存确认单
        confirmationFormRecode.setFileId("待填充");
        //确认单命名规则   文件名称加上时间
//        String fileNameBest = getFileNameBest(attachments);
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CONFIRMATION_FORM_NAME, NoGeneratorBean.builder().dateFormat("yyMMddHHmmss").randomDigits(5).build());
        String code = new BillNumGenerator().distribution(billGeneratorBean);
        confirmationFormRecode.setConfirmationName(code);
        confirmationFormRecode.setConfirmationType(CommonConstants.CONFIRMATION_TYPE_4);//类型改为其他
        confirmationFormRecode.setFileName(fileNameBetter);
        confirmationFormRecode.setIsEnable(CommonConstants.IS_DELETE_0);
        confirmationFormRecode.setAddTime(timeMillis);
        confirmationFormRecode.setModTime(timeMillis);
        confirmationFormRecode.setComments("自动审核通过");
        confirmationFormRecode.setCreator(1);
        confirmationFormRecode.setUpdater(1);
        confirmationFormRecodeMapper.insertSelective(confirmationFormRecode);
        Integer id = confirmationFormRecode.getId(); //获取插入后的新增id

        //保存确认单和批次关系
        List<OutboundBatchesRecode> batchesRecodes = confirmationFormRecode.getBatchesRecodes();
        batchesRecodes.stream().forEach(x->{
            //批次状态改变为已经上传
            outboundBatchesRecodeMapper.updateUploadStatusById(x.getId(),CommonConstants.STATUS_1);
            outboundBatchesRecodeMapper.updateAuditStatusById(x.getId(),CommonConstants.STATUS_1);
            //驳回重新上传逻辑
            if (x.getAuditStatus()!=null&&x.getAuditStatus().equals(0)){
                outboundBatchesRecodeMapper.updateAuditStatusById(x.getId(),Constants.THREE);
                outboundBatchesRecodeMapper.updateCommentsById(x.getId());
                saleorderMapper.updateConfirmationFormAuditById(saleOrderId,Constants.ZERO);
            }
        });

        for (OutboundBatchesRecode outboundBatchesRecode : batchesRecodes) {
            ConfirmationBatchesRelation confirmationBatchesRelation = new ConfirmationBatchesRelation();
            confirmationBatchesRelation.setSaleorderId(saleOrderId);
            confirmationBatchesRelation.setConfirmationId(id);
            confirmationBatchesRelation.setBatchNo(outboundBatchesRecode.getBatchNo());
            confirmationBatchesRelation.setIsEnable(CommonConstants.IS_DELETE_0);
            confirmationBatchesRelation.setAddTime(timeMillis);
            confirmationBatchesRelation.setModTime(timeMillis);
            confirmationBatchesRelation.setCreator(userId);
            confirmationBatchesRelation.setUpdater(userId);
            confirmationBatchesRelationMapper.insert(confirmationBatchesRelation);
        }

        ArrayList<String> integers = new ArrayList<>();
        List<FileInfo> fileInfos = confirmationFormRecode.getFileInfos();
        //保存附件信息
        for (FileInfo f : fileInfos) {
            Attachment attachment = new Attachment();
            attachment.setDomain(f.getDomain());
            attachment.setName(f.getFileName());
            attachment.setFilePath(f.getFilePath());
            attachment.setUri(f.getOssUrl());
            attachment.setSuffix(f.getPrefix());
            attachment.setOssResourceId(f.getOssResourceId());
            attachment.setCreator(userId);
            attachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_980);
            attachment.setAttachmentFunction(CommonConstants.ATTACHMENT_FUNCTION_1020);
            attachment.setRelatedId(id);
            attachment.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE);
            attachment.setAddTime(timeMillis);
            attachmentMapper.insertSelective(attachment);
            integers.add(String.valueOf(attachment.getAttachmentId()));
        }
        String attaIds = String.join(",", integers);
        confirmationFormRecode.setFileId(attaIds);
        confirmationFormRecodeMapper.updateByPrimaryKey(confirmationFormRecode);
        return true;
        //a)订单为全部发货状态；
        //return ((ConfirmationFormRecodeService) AopContext.currentProxy()).confirmationOrderAudit(request, timeMillis, saleOrderId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean confirmationOrderAudit(HttpServletRequest request, long timeMillis, Integer saleOrderId) {
        Saleorder baseSaleorderInfo = saleorderMapper.getBaseSaleorderInfo(saleOrderId);
        if (baseSaleorderInfo == null) {
            logger.info("确认单发起审核,未查询到订单信息：{}",saleOrderId);
            return false;
        }

        if(baseSaleorderInfo.getConfirmationFormAudit().equals(1)){
            //审核中先判断是否批次下所有的确认单都已经审核通过，如果都审核通过，销售单直接置为全部上传和审核通过
            List<String> batchNos = getBatchNos(saleOrderId);
            List<OutboundBatchesRecode> allByBatchNoList = new ArrayList<>();
            if (batchNos.size()>0){
                allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNos);
                logger.info("该订单下的所有批次：{}",allByBatchNoList);
                if (allByBatchNoList.size()>0 && allByBatchNoList.stream().allMatch(outboundBatchesRecode -> Constants.ONE.equals(outboundBatchesRecode.getAuditStatus()))){
                    saleorderMapper.updateConfirmationFormAuditById(saleOrderId,Constants.TWO);
                    saleorderMapper.updateConfirmationFormUploadById(saleOrderId,Constants.TWO);
                    logger.info("确认单发起审核,销售订单确认单审核状态为审核中,确认单批次都已经审核通过，确认单直接置为审核通过和上传：{}",saleOrderId);
                }
            }
            return true;
        }


        if (baseSaleorderInfo.getConfirmationFormAudit().equals(2)){
            logger.info("确认单发起审核,销售订单确认单审核状态为审核通过,无需再次发起审核：{}",saleOrderId);
            return false;
        }
        Saleorder saleorderUpdate = new Saleorder();
        List<String> batchNos = getBatchNos(saleOrderId);
        List<OutboundBatchesRecode> allByBatchNoList = new ArrayList<>();
        if (batchNos.size()>0){
            allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNos);
            logger.info("该订单下的所有批次：{}",allByBatchNoList);
            saleorderUpdate.setConfirmationFormUpload(
                    allByBatchNoList.stream().noneMatch(outboundBatchesRecode -> Constants.ONE.equals(outboundBatchesRecode.getUploadStatus())) ? 0 : 1);
        }else {
            logger.info("该订单下没有批次无需审核");
            saleorderUpdate.setConfirmationFormUpload(0);
        }
        Integer deliveryStatus = baseSaleorderInfo.getDeliveryStatus();




        saleorderUpdate.setSaleorderId(baseSaleorderInfo.getSaleorderId());
        if (deliveryStatus.equals(Constants.TWO)) {
            logger.info("确认单进入全部发货审核阶段：{}",baseSaleorderInfo);
            //b)确认单：每个快递都有确认单（包含：客户在线确认、物流上传确认单、销售上传确认单）。其中驳回批次认定为未上传确认单；

            //判断批次是否全部上传
            boolean conformationStauts = true;
            for (OutboundBatchesRecode outboundBatchesRecode : allByBatchNoList) {
                if (outboundBatchesRecode.getUploadStatus() != null && outboundBatchesRecode.getUploadStatus().equals(CommonConstants.STATUS_0)) {
                    conformationStauts = false;
                    logger.info("批次没有全部确认不触发审核：{}",baseSaleorderInfo.getSaleorderNo());
                }
            }
            if (CollectionUtils.isEmpty(allByBatchNoList)) {
                conformationStauts = false;
                logger.info("该订单下没有批次不触发审核：{}",baseSaleorderInfo.getSaleorderNo());
            }
            //过滤掉所有已通过的批次
            List<OutboundBatchesRecode> batchNoListBetter = allByBatchNoList.stream().filter(x -> {
                if (Constants.ONE.equals(x.getAuditStatus())) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            //发起审核
            if (conformationStauts) {
                logger.info("开始发起确认单审核：{}",baseSaleorderInfo.getSaleorderNo());
                //修改销售订单状态
                saleorderUpdate.setConfirmationFormUpload(Constants.TWO);
                saleorderUpdate.setConfirmationFormAudit(Constants.ONE);
                saleorderUpdate.setConfirmationSubmitTime(timeMillis);
                //触发审核

                //判断订单下批次是否都审核通过，如果都审核通过，订单状态改为审核通过
                List<OutboundBatchesRecode> batchOnlineConfirmList = allByBatchNoList.stream().filter(data -> Constants.ONE.equals(data.getAuditStatus())).collect(Collectors.toList());
                if (!PAYMENT_TYPE_FOR_PAYMENT_DAYS.contains(baseSaleorderInfo.getPaymentType()) && batchOnlineConfirmList.size() == allByBatchNoList.size()) {
                    logger.info("判断订单下批次是否都审核通过，如果都审核通过，订单状态改为审核通过：{}",baseSaleorderInfo.getSaleorderNo());
                    saleorderUpdate.setConfirmationFormAudit(Constants.TWO);
                    for (OutboundBatchesRecode o : batchNoListBetter) {
                        outboundBatchesRecodeMapper.updateAuditStatusById(o.getId(),1);
                    }
                } else {
                    for (OutboundBatchesRecode outboundBatchesRecode : batchNoListBetter) {
                        logger.info("触发审核，发送批次至质量工作台：{}",outboundBatchesRecode);
                        Boolean result = ((ConfirmationFormRecodeService) AopContext.currentProxy()).confirmationApplicationReview(request, outboundBatchesRecode.getId(),baseSaleorderInfo);
                    }
                }
            }
        }

        if (!(saleorderUpdate.getConfirmationFormUpload()==null&&saleorderUpdate.getConfirmationFormAudit()==null)){
            logger.info("确认单是否触发审核流程走完，修改订单状态为：{}",saleorderUpdate);
            saleorderMapper.updateByPrimaryKeySelective(saleorderUpdate);
        }

        return true;
    }

    private List<String> getBatchNos(Integer saleOrderId) {
        Express exQueryVo = new Express();
        //获取物流信息
        exQueryVo.setSaleorderId(saleOrderId);
        List<Express> expressInfoConfirmation = expressMapper.getExpressInfoConfirmation(exQueryVo);
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleOrderId);
        List<Express> buyExpressList = new ArrayList<>();
        if (saleorderGoods.size()>0){
            List<Integer> listSale = saleorderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
            exQueryVo.setRelatedIds(listSale);
            buyExpressList = expressMapper.getBuyExpressList(exQueryVo);
        }
        expressInfoConfirmation.addAll(buyExpressList);
        //根据物流信息获取批次号
        List<String> batchNos = expressInfoConfirmation.stream().map(Express::getBatchNo).distinct().collect(Collectors.toList());
        return batchNos;
    }

    @Override
    @SneakyThrows
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean confirmationApplicationReview(HttpServletRequest request, Integer batchId,Saleorder baseSaleorderInfo){
        // 1.创建并发起审核
        try {
            HashMap<String, Object> variableMap = new HashMap<>();
            User user = null;
            if (Objects.isNull(request)) {
                user = new User();
                user.setUserId(2);
                user.setUsername("njadmin");
                user.setCompanyId(1);
            } else {
                user = getSessionUser(request);
            }
            // 根据批次主键ID查询批次信息
            OutboundBatchesRecode outboundBatchesRecode = outboundBatchesRecodeMapper.getById(batchId);
            // 开始生成流程
            String businessKey = "confirmationOrderVerify_" + batchId;
            variableMap.put("outboundBatchesRecode", outboundBatchesRecode);
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("saleorderId", baseSaleorderInfo.getSaleorderId());
            variableMap.put("saleorderNo", baseSaleorderInfo.getSaleorderNo());
            variableMap.put("traderName", baseSaleorderInfo.getTraderName());
            variableMap.put("processDefinitionKey", "confirmationOrderVerify");
            variableMap.put("businessKey", businessKey);
            variableMap.put("relateTableKey", outboundBatchesRecode.getId());
            variableMap.put("relateTable", "T_OUTBOUND_BATCHES_RECODE");
            actionProcdefService.createProcessInstance(request, "confirmationOrderVerify", businessKey, variableMap);
            // 默认申请人通过,根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, businessKey);
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<>();
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        user.getUsername(), variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }
            }
            // 同步更新批次的审核状态
            outboundBatchesRecodeMapper.updateAuditStatusById(batchId, 2);
            return true;
        } catch (Exception e) {
            logger.error("确认单批次发起审核异常,confirmationApplicationReview:", e);
            throw new Exception("确认单批次发起审核异常");
        }
    }

}
