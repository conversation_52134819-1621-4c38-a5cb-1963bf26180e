package com.vedeng.order.chain.model;

import com.vedeng.order.chain.RiskEnum;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName RiskResultModel.java
 * @Description TODO 风控结果
 * @createTime 2020年12月08日 10:17:00
 */
@Data
public class RiskModel {
    //总控 是否开启风控
    private Boolean isCheck;

    //是否通过风控  true 通过  false 未通过
    private Boolean isRisk;

    //本次风控是否通过
    private Boolean isOneChek;

    private Set<RiskEnum> riskEnumList;

    private String traderRiskMessage;

    private String skuRiskMessage;

    private String firstRiskMessage;

    private String spuRiskMessage;

    private String supplierRiskMessage;

}
