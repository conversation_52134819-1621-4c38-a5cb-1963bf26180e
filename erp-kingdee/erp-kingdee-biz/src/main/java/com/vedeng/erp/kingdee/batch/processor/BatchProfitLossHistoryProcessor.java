package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.enums.OtherTypeConst;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderGoodsDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWmsOutputOrderDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 盘亏出库单
 * @date 2023/5/26 13:55
 */
@Service
@Slf4j
public class BatchProfitLossHistoryProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeProfitLossDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Override
    public KingDeeProfitLossDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        KingDeeProfitLossDto dto = new KingDeeProfitLossDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        //判断是否是21-22其他类型出入库
        if (!OtherTypeConst.UPDATE_REMARK_ORTHER_TYPE.equals(batchWarehouseGoodsOutInDto.getUpdateRemark())){
            return null;
        }
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("盘亏出库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        log.info("盘亏出库单,BatchWarehouseOutInProcessorService.process:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
        List<BatchWarehouseGoodsOutInItemDto> inItemDtos = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        Map<String, List<BatchWarehouseGoodsOutInItemDto>> itemsMap = inItemDtos.stream()
                .collect(Collectors.groupingBy(p -> p.getGoodsId() + "_" + p.getBarcodeFactory() + "_" + p.getBatchNumber()));
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(inItemDtos);


        // 主表
        dto.setFId("0");
        dto.setFDate(DateUtil.formatDateTime(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());

        // 详细
        List<KingDeeProfitLossDetailDto> detailList = new ArrayList<>();
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }
        for (Map.Entry<String, List<BatchWarehouseGoodsOutInItemDto>> entry : itemsMap.entrySet()) {
            KingDeeProfitLossDetailDto detailDto = new KingDeeProfitLossDetailDto();
            BatchWarehouseGoodsOutInItemDto d = CollUtil.getFirst(entry.getValue());
            BigDecimal sumNum = entry.getValue().stream()
                    .map(x->x.getNum().abs())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            detailDto.setFMaterialId("V"+d.getGoodsId());
            detailDto.setFBaseLossQty(sumNum.toString());
            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokPch(d.getVedengBatchNumber());
            detailDto.setFQzokXlh(d.getBarcodeFactory());
            detailDto.setFQzokSfzf("否");
            detailList.add(detailDto);
        }
        dto.setFBillEntry(detailList);
        return dto;
    }
}
