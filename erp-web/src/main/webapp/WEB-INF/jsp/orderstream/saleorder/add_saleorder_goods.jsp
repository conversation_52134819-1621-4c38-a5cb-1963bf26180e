<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%--<c:set var="title" value="添加产品" scope="application" />--%>
<%@ include file="../../common/common.jsp"%>
<%@ include file="../../component/remarkComponent.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script type="text/javascript" src='<%=basePath%>/static/js/orderstream/saleorder/add_order_goods.js?rnd=${resourceVersionKey}'></script>
<div class="formpublic formpublic1">
    <div>
        <!-- ------------产品数据列表--------start------- -->
        <div class="controlled" id="goodsListDiv">
            <!-- 搜索表格出来 -->
            <ul class="searchTable">
                <form class="layui-form" action="<%=basePath%>/orderstream/saleorder/addSaleorderGoods.do" method="get" onsubmit="return search()">
                    <input type="hidden"  name="saleorderId" id="saleorderId" value="${saleorderId}">
                    <input type="hidden"  name="scene" id="scene" value="${scene}">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width:110px;">
                                <span style="color: red">*</span>
                                产品名称：
                            </label>
                            <div class="layui-input-inline" style="width: 220px">
                                <input type="text" name="searchContent" placeholder="请输入产品名称/订货号等关键字" class="layui-input" value="${searchContent}">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width:110px;">品牌：</label>
                            <div class="layui-input-inline" style="width: 220px">
                                <input type="hidden" name="goodsBrandIdValue" id="goodsBrandIdValue" value="${goodsBrandIdValue}">
                                <select name="goodsBrandId" id="goodsBrandId" class="layui-input" lay-search>
                                    <%--<option value="">请选择商品类型</option>--%>
                                    <%--<c:forEach var="goodsBrand" items="${goodsBrandList}" varStatus="status">--%>
                                    <%--<option value="${goodsBrand.brandId}">${goodsBrand.brandName}</option>--%>
                                    <%--</c:forEach>--%>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width:110px;">商品类型：</label>
                            <div class="layui-input-inline" style="width: 220px">
                                <input type="hidden" name="goodsTypeValue" id="goodsTypeValue" value="${goodsTypeValue}">
                                <select name="goodsType" class="layui-input" id="goodsTypeId">
                                    <option value="">请选择商品类型</option>
                                    <option value="0">全部</option>
                                    <c:forEach var="goodsType" items="${goodsTypeList}" varStatus="status">
                                        <option value="${goodsType.sysOptionDefinitionId }" >${goodsType.title}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width:110px;">规格/型号：</label>
                            <div class="layui-input-inline" style="width: 220px">
                                <input type="text"  placeholder="请输入规格/型号" id="zxfTs" class="layui-input"
                                       name="typeSpecification" value="${typeSpecification}">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width:110px;">单位：</label>
                            <div class="layui-input-inline" style="width: 220px">
                                <input type="hidden" name="zxfunitNameValue" id="zxfunitNameValue" value="${zxfunitNameValue}">
                                <select  lay-search name="unitName" id="zxfunitName" style= "width:200px;height: 28px" >
                                    <option value="">请选择单位</option>
                                    <c:if test="${not empty unitList }">
                                        <c:forEach var="unit" items="${unitList}">
                                            <option value="${unit.unitName}">${unit.unitName}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item tcenter" >
                        <div class="layui-input-block">
                            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm">搜索</button>
                        </div>
                        <div class="warning J-error" style="display: none;">搜索条件不能为空!</div>
                    </div>

                </form>
                <div>
                    <table class="layui-hide" id="goodsListTable" lay-filter="goodsListTableFilter"></table>
                    <table
                            class="table table-bordered table-striped table-condensed table-centered mb10">
                        <thead>
                        <th class="table-smallest8">订货号</th>
                        <th>产品名称</th>
                        <th class="table-smallest">品牌</th>
                        <th class="table-smallest8">型号</th>
                        <th class="table-smallest8">单位</th>
                        <th class="table-smallest12">商品类型</th>
                        <th class="table-smallest12">商品等级</th>
                        <th class="table-smallest12">商品档位</th>
                        <th class="table-smallest5">库存</th>
                        <th class="table-smallest8">审核状态</th>
                        <th class="table-smallest6">选择</th>
                        </thead>
                        <tbody>
                        <c:forEach var="list" items="${goodsList}" varStatus="status">
                            <tr>
                                <td>${list.sku}</td>
                                <td><c:if test="${list.source eq 1}">
                                    <span style="color: red">【医械购】</span>
                                </c:if>${list.goodsName}</td>
                                <td>${list.brandName}</td>
                                <td>${list.model}</td>
                                <td>${list.unitName}</td>
                                <td>
                                    <c:if test="${list.goodsType eq 1008}">配件</c:if> <c:if
                                            test="${list.goodsType eq 318}">试剂</c:if> <c:if
                                            test="${list.goodsType eq 317}">耗材</c:if> <c:if
                                            test="${list.goodsType eq 316}">设备</c:if>

                                </td>
                                <td>${list.goodsLevelName}</td>
                                <td>${list.goodsPositionName}</td>
                                <td>${list.stockNum}</td>
                                <td><c:if test="${list.verifyStatus eq 0}">待完善</c:if> <c:if
                                        test="${list.verifyStatus eq 1}">审核中</c:if> <c:if
                                        test="${list.verifyStatus eq 2}">审核不通过</c:if> <c:if
                                        test="${list.verifyStatus eq 3}">审核通过</c:if>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${scene eq 8}">
                                            <%-- 新增虚拟商品场景 --%>
                                            <a href="javascript:void(0);" <c:if test="${list.verifyStatus ne 3 and list.parentId eq 1}"> title="新商品流信息不全，请补全商品信息"</c:if>
                                            onclick="virtureChooseGoods('${list.goodsId}','${list.sku}','${list.goodsName}','${list.proUserName}','${list.taxCategoryNo}')"
                                               style="color: rgb(51, 132, 239);">选择</a>
                                        </c:when>
                                        <c:otherwise>
                                            <!-- oldVerifyStatus 代表老商品流审核通过-->
                                            <c:if test="${list.verifyStatus eq 3}">
                                                <a href="javascript:void(0);" <c:if test="${list.verifyStatus ne 3 and list.parentId eq 1}"> title="新商品流信息不全，请补全商品信息"</c:if>
                                                   onclick="selectGoods('${list.goodsId}','${list.sku}','<c:out value='${list.goodsName}' escapeXml="true"></c:out>','${list.brandName}','${list.model}','${list.unitName}','${list.goodsLevelName}','','${list.verifyStatus}','${list.stockNum}','${scene}');"
                                                   style="color: rgb(51, 132, 239);">选择</a>
                                            </c:if>
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                            </tr>
                        </c:forEach>
                        <c:if test="${empty goodsList}">
                            <tr>
                                <td colspan="10">查询无结果！请尝试使用其他搜索条件。</td>
                            </tr>
                        </c:if>
                        </tbody>
                    </table>
                </div>
                </li>
                <tags:page page="${page}" optpage="n" />
                <div class="clear"></div>
            </ul>
        </div>
        <!-- ------------产品数据列表--------end------- -->
    </div>
</div>

