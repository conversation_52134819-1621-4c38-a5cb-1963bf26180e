package com.wms.service.chain.step;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.util.DateUtil;
import com.wms.constant.VerifyStatusEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.*;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockallocation.StockAllocationStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 外接单出库 逻辑单处理
 */
@Service
public class LendOutOrderHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(LendOutOrderHandler.class);

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;

    @Autowired
    @Qualifier("saleOrderStockAllocationStrategy")
    private StockAllocationStrategy stockAllocationStrategy;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Long lendOutOrderId = context.get("lendOutOrderId");


        //更新出库单
        WmsOutputOrder updateOrder = new WmsOutputOrder();
        updateOrder.setId(lendOutOrderId);
        updateOrder.setVerifyStatus(VerifyStatusEnum.Approved.getValue());

        String updateTime = DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
        updateOrder.setUpdateTime(updateTime);
        updateOrder.setApprovalTime(updateTime);

        LOGGER.info("更新外借单:"+lendOutOrderId+",的订单数据=====================:" + JSON.toJSONString(updateOrder));
        outputOrderMapper.updateByPrimaryKeySelective(updateOrder);

        List<WmsOutputOrderGoods> outputOrderGoodList =
                this.outputOrderGoodsMapper.queryOutputGoodsByLendOutId(lendOutOrderId);

        context.put("outputOrderGoodList",outputOrderGoodList);

        //库存分配结果
        List<StockAllocationResult> allocationResultList = new ArrayList<>();

        for(WmsOutputOrderGoods wmsOutputOrderGood : outputOrderGoodList){

            StockAllocationRequest stockAllocationRequest = new StockAllocationRequest();
            stockAllocationRequest.setNum(wmsOutputOrderGood.getOutputNum());
            stockAllocationRequest.setSku(wmsOutputOrderGood.getSkuNo());

            LOGGER.info("库存分配请求:" + JSON.toJSONString(stockAllocationRequest));

            allocationResultList.addAll(stockAllocationStrategy.lentOutStockAllocation(stockAllocationRequest));
        }

        LOGGER.info("外借单:"+lendOutOrderId+",的库存分配结果=====================:" + JSON.toJSONString(allocationResultList));
        context.put("allocationResultList",allocationResultList);

        allocationResultList.stream().forEach(allocationResult -> {

            //新增逻辑订单表数据
            Long realateId = getRelateId(outputOrderGoodList,allocationResult.getSku());
            List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = wmsLogicalOrdergoodsMapper.selectByRelateIdAndOperateType(realateId.intValue(), WmsLogicalOperateTypeEnum.LENDOUT_EXG.getOperateTypeCode());
            if (CollUtil.isNotEmpty(wmsLogicalOrdergoodsList)) {
                // 已有跳过
                return;
            }
            WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
            wmsLogicalOrdergoods.setRelatedId(realateId.intValue());
            wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.LENDOUT_EXG.getOperateTypeCode());
            wmsLogicalOrdergoods.setSku(allocationResult.getSku());
            wmsLogicalOrdergoods.setGoodsId(Integer.valueOf(allocationResult.getSku().substring(1)));
            wmsLogicalOrdergoods.setNum(allocationResult.getNum());
            wmsLogicalOrdergoods.setLogicalWarehouseId(allocationResult.getLogicalWarehouseId());
            wmsLogicalOrdergoods.setOccupyNum(allocationResult.getNum());
            wmsLogicalOrdergoods.setAddTime(new Date());
            wmsLogicalOrdergoods.setModeTime(new Date());

            LOGGER.info("外借单:"+lendOutOrderId+",新增逻辑订单数据=====================:" + JSON.toJSONString(wmsLogicalOrdergoods));
            wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);
        });
    }

    public Long getRelateId(List<WmsOutputOrderGoods> outputOrderGoodList,String sku){
        return outputOrderGoodList
                .stream()
                .filter(outputOrderGood -> {
                    return outputOrderGood.getSkuNo().equals(sku);
                }).map(outputOrderGood -> outputOrderGood.getId())
                .findFirst()
                .orElse(0L);
    }

}
