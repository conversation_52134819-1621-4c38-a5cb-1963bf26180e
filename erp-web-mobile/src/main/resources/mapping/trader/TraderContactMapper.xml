<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.mobile.system.mapper.TraderContactMapper">
    <sql id="Base_Column_List">
        TRADER_CONTACT_ID,
        TRADER_ID,
        TRADER_TYPE,
        IS_ENABLE,
        SEX,
        `NAME`,
        DEPARTMENT,
        `POSITION`,
        TELEPHONE,
        FAX,
        MOBILE,
        MOBILE2,
        EMAIL,
        QQ,
        WEIXIN,
        IS_ON_JOB,
        IS_DEFAULT,
        BIRTHDAY,
        IS_MARRIED,
        HAVE_CHILDREN,
        EDUCATION,
        `CHARACTER`,
        COMMENTS,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        IS_TOP
    </sql>
    <resultMap type="com.vedeng.mobile.system.model.TraderContact" id="TTraderContactMap">
        <result property="traderContactId" column="TRADER_CONTACT_ID" jdbcType="INTEGER"/>
        <result property="traderId" column="TRADER_ID" jdbcType="INTEGER"/>
        <result property="traderType" column="TRADER_TYPE" jdbcType="INTEGER"/>
        <result property="isEnable" column="IS_ENABLE" jdbcType="INTEGER"/>
        <result property="sex" column="SEX" jdbcType="INTEGER"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="department" column="DEPARTMENT" jdbcType="VARCHAR"/>
        <result property="position" column="POSITION" jdbcType="VARCHAR"/>
        <result property="telephone" column="TELEPHONE" jdbcType="VARCHAR"/>
        <result property="fax" column="FAX" jdbcType="VARCHAR"/>
        <result property="mobile" column="MOBILE" jdbcType="VARCHAR"/>
        <result property="mobile2" column="MOBILE2" jdbcType="VARCHAR"/>
        <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
        <result property="qq" column="QQ" jdbcType="VARCHAR"/>
        <result property="weixin" column="WEIXIN" jdbcType="VARCHAR"/>
        <result property="isOnJob" column="IS_ON_JOB" jdbcType="INTEGER"/>
        <result property="isDefault" column="IS_DEFAULT" jdbcType="INTEGER"/>
        <result property="birthday" column="BIRTHDAY" jdbcType="TIMESTAMP"/>
        <result property="isMarried" column="IS_MARRIED" jdbcType="INTEGER"/>
        <result property="haveChildren" column="HAVE_CHILDREN" jdbcType="INTEGER"/>
        <result property="education" column="EDUCATION" jdbcType="INTEGER"/>
        <result property="character" column="CHARACTER" jdbcType="VARCHAR"/>
        <result property="comments" column="COMMENTS" jdbcType="VARCHAR"/>
        <result property="addTime" column="ADD_TIME" jdbcType="INTEGER"/>
        <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
        <result property="modTime" column="MOD_TIME" jdbcType="INTEGER"/>
        <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
        <result property="isTop" column="IS_TOP" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询指定行数据-->
    <select id="findByTraderIdAndName" resultType="com.vedeng.mobile.system.model.TraderContact">
        select
        TRADER_CONTACT_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, SEX, NAME, DEPARTMENT, POSITION, TELEPHONE, FAX, MOBILE,
        MOBILE2, EMAIL, QQ, WEIXIN, IS_ON_JOB, IS_DEFAULT, BIRTHDAY, IS_MARRIED, HAVE_CHILDREN, EDUCATION, `CHARACTER`,
        COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_TOP
        from T_TRADER_CONTACT
        where
                TRADER_ID = #{traderId}
                and NAME = #{name}
    </select>

    <update id="updateContractInfoByTraderContractId" parameterType="com.vedeng.mobile.system.model.TraderContact">
        UPDATE T_TRADER_CONTACT
        SET
            NAME=#{name},
            POSITION=#{position},
            TELEPHONE=#{telephone},
            MOBILE=#{mobile},
            MOD_TIME=now(),
            UPDATER=#{updater}
        where
            TRADER_CONTACT_ID=#{traderContactId}
    </update>

    <!--查询指定行数据-->
    <select id="findByAll" resultType="com.vedeng.mobile.system.model.TraderContact">
        select
        TRADER_CONTACT_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, SEX, NAME, DEPARTMENT, POSITION, TELEPHONE, FAX, MOBILE,
        MOBILE2, EMAIL, QQ, WEIXIN, IS_ON_JOB, IS_DEFAULT, BIRTHDAY, IS_MARRIED, HAVE_CHILDREN, EDUCATION, `CHARACTER`,
        COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_TOP
        from T_TRADER_CONTACT
        <where>
            <if test="traderContactId != null">
                and TRADER_CONTACT_ID = #{traderContactId}
            </if>
            <if test="traderId != null">
                and TRADER_ID = #{traderId}
            </if>
            <if test="traderType != null">
                and TRADER_TYPE = #{traderType}
            </if>
            <if test="isEnable != null">
                and IS_ENABLE = #{isEnable}
            </if>
            <if test="sex != null">
                and SEX = #{sex}
            </if>
            <if test="name != null and name != ''">
                and NAME = #{name}
            </if>
            <if test="department != null and department != ''">
                and DEPARTMENT = #{department}
            </if>
            <if test="position != null and position != ''">
                and POSITION = #{position}
            </if>
            <if test="telephone != null and telephone != ''">
                and TELEPHONE = #{telephone}
            </if>
            <if test="fax != null and fax != ''">
                and FAX = #{fax}
            </if>
            <if test="mobile != null and mobile != ''">
                and MOBILE = #{mobile}
            </if>
            <if test="mobile2 != null and mobile2 != ''">
                and MOBILE2 = #{mobile2}
            </if>
            <if test="email != null and email != ''">
                and EMAIL = #{email}
            </if>
            <if test="qq != null and qq != ''">
                and QQ = #{qq}
            </if>
            <if test="weixin != null and weixin != ''">
                and WEIXIN = #{weixin}
            </if>
            <if test="isOnJob != null">
                and IS_ON_JOB = #{isOnJob}
            </if>
            <if test="isDefault != null">
                and IS_DEFAULT = #{isDefault}
            </if>
            <if test="birthday != null">
                and BIRTHDAY = #{birthday}
            </if>
            <if test="isMarried != null">
                and IS_MARRIED = #{isMarried}
            </if>
            <if test="haveChildren != null">
                and HAVE_CHILDREN = #{haveChildren}
            </if>
            <if test="education != null">
                and EDUCATION = #{education}
            </if>
            <if test="character != null and character != ''">
                and `CHARACTER` = #{character}
            </if>
            <if test="comments != null and comments != ''">
                and COMMENTS = #{comments}
            </if>
            <if test="addTime != null">
                and ADD_TIME = #{addTime}
            </if>
            <if test="creator != null">
                and CREATOR = #{creator}
            </if>
            <if test="modTime != null">
                and MOD_TIME = #{modTime}
            </if>
            <if test="updater != null">
                and UPDATER = #{updater}
            </if>
            <if test="isTop != null">
                and IS_TOP = #{isTop}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="traderContactId" useGeneratedKeys="true">
        insert into T_TRADER_CONTACT(TRADER_ID, TRADER_TYPE, IS_ENABLE, SEX, NAME, DEPARTMENT, POSITION, TELEPHONE, FAX,
                                     MOBILE, MOBILE2, EMAIL, QQ, WEIXIN, IS_ON_JOB, IS_DEFAULT, BIRTHDAY, IS_MARRIED,
                                     HAVE_CHILDREN, EDUCATION, CHARACTER, COMMENTS, ADD_TIME, CREATOR, MOD_TIME,
                                     UPDATER, IS_TOP)
        values (#{traderId}, #{traderType}, #{isEnable}, #{sex}, #{name}, #{department}, #{position}, #{telephone},
                #{fax}, #{mobile}, #{mobile2}, #{email}, #{qq}, #{weixin}, #{isOnJob}, #{isDefault}, #{birthday},
                #{isMarried}, #{haveChildren}, #{education}, #{character}, #{comments}, #{addTime}, #{creator},
                #{modTime}, #{updater}, #{isTop})
    </insert>
    <insert id="insertSelective" parameterType="com.vedeng.mobile.system.model.TraderContact" useGeneratedKeys="true" keyProperty="traderContactId">
        insert into T_TRADER_CONTACT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="traderContactId != null">
                TRADER_CONTACT_ID,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="traderType != null">
                TRADER_TYPE,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
            <if test="sex != null">
                SEX,
            </if>
            <if test="name != null">
                `NAME`,
            </if>
            <if test="department != null">
                DEPARTMENT,
            </if>
            <if test="position != null">
                `POSITION`,
            </if>
            <if test="telephone != null">
                TELEPHONE,
            </if>
            <if test="fax != null">
                FAX,
            </if>
            <if test="mobile != null">
                MOBILE,
            </if>
            <if test="mobile2 != null">
                MOBILE2,
            </if>
            <if test="email != null">
                EMAIL,
            </if>
            <if test="qq != null">
                QQ,
            </if>
            <if test="weixin != null">
                WEIXIN,
            </if>
            <if test="isOnJob != null">
                IS_ON_JOB,
            </if>
            <if test="isDefault != null">
                IS_DEFAULT,
            </if>
            <if test="birthday != null">
                BIRTHDAY,
            </if>
            <if test="isMarried != null">
                IS_MARRIED,
            </if>
            <if test="haveChildren != null">
                HAVE_CHILDREN,
            </if>
            <if test="education != null">
                EDUCATION,
            </if>
            <if test="character != null">
                `CHARACTER`,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="isTop != null">
                IS_TOP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="traderContactId != null">
                #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null">
                #{traderType,jdbcType=BIT},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=BIT},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=BIT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                #{position,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                #{fax,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="mobile2 != null">
                #{mobile2,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="qq != null">
                #{qq,jdbcType=VARCHAR},
            </if>
            <if test="weixin != null">
                #{weixin,jdbcType=VARCHAR},
            </if>
            <if test="isOnJob != null">
                #{isOnJob,jdbcType=BIT},
            </if>
            <if test="isDefault != null">
                #{isDefault,jdbcType=BIT},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="isMarried != null">
                #{isMarried,jdbcType=BIT},
            </if>
            <if test="haveChildren != null">
                #{haveChildren,jdbcType=BIT},
            </if>
            <if test="education != null">
                #{education,jdbcType=INTEGER},
            </if>
            <if test="character != null">
                #{character,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="isTop != null">
                #{isTop,jdbcType=BIT},
            </if>
        </trim>
    </insert>

<!--auto generated by MybatisCodeHelper on 2022-07-13-->
    <select id="selectByTraderContactId" resultMap="TTraderContactMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_CONTACT
        where TRADER_CONTACT_ID=#{traderContactId,jdbcType=INTEGER}
    </select>
    <select id="matchTraderContractSearch" resultType="com.vedeng.mobile.system.model.TraderContact">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_CONTACT
        where
            TRADER_ID = #{traderId}
        <if test="mergeFiled != null and mergeFiled != ''">
            and
            CONCAT(IFNULL(NAME,''),IFNULL(MOBILE,''),IFNULL(TELEPHONE,'')) LIKE concat('%',#{mergeFiled},'%')
        </if>

    </select>

    <select id="getTraderContacts" resultType="com.vedeng.mobile.system.model.TraderContact">
        select
        <include refid="Base_Column_List"/>
        from
        T_TRADER_CONTACT
        where
        1=1
        and TRADER_CONTACT_ID in
        <foreach item="traderContactId" index="index" collection="list" open="(" separator="," close=")">
            #{traderContactId}
        </foreach>

    </select>
    <select id="getSupplierContact" resultType="com.vedeng.mobile.system.model.TraderContact">
        SELECT
            B.TRADER_CONTACT_ID,
            B.TRADER_TYPE,
            B.NAME,
            B.SEX,
            B.MOBILE,
            B.FAX,
            B.EMAIL,
            A.TRADER_ID,
            A.TRADER_NAME,
            C.BANK,
            C.BANK_ACCOUNT,
            C.TAX_NUM
        FROM
            T_TRADER A
                LEFT JOIN T_TRADER_CONTACT B ON A.TRADER_ID = B.TRADER_ID
                AND B.TRADER_TYPE = 2
                AND B.IS_ENABLE = 1
                LEFT JOIN T_TRADER_FINANCE C ON A.TRADER_ID = C.TRADER_ID
                AND C.TRADER_TYPE = 2
        WHERE
            A.TRADER_ID = #{traderId}
    </select>
    <select id="getTraderContactInfoByIdList" resultType="com.vedeng.mobile.system.model.TraderContact">
        select
        tc.TRADER_CONTACT_ID,
        tc.TRADER_ID,
        tc.NAME,
        tc.EMAIL,
        tc.QQ,
        tc.WEIXIN,
        tc.SEX,
        tc.`POSITION`,
        tc.TELEPHONE,
        tc.MOBILE,
        tc.MOBILE2,
        tc.COMMENTS,
        tc.IS_ON_JOB,
        tc.IS_ENABLE,
        CASE
        WHEN (t.TRADER_NAME IS NULL
        OR t.TRADER_NAME = '')
        and (t1.TRADER_NAME IS NULL
        OR t1.TRADER_NAME = '') THEN ''
        WHEN v.STATUS IN (0, 1, 5) THEN CONCAT('已注册-会员', ',', COALESCE(wa.MOBILE, wa1.MOBILE))
        ELSE CONCAT('已注册', ',', COALESCE(wa.MOBILE, wa1.MOBILE))
        END AS MEMBER_FLAG
        from
        T_TRADER_CONTACT tc
        LEFT JOIN T_WEB_ACCOUNT wa ON
        tc.MOBILE = wa.MOBILE
        LEFT JOIN T_WEB_ACCOUNT wa1 ON
        tc.MOBILE2 = wa1.MOBILE
        LEFT JOIN T_TRADER_CUSTOMER tcr ON
        tcr.TRADER_ID = tc.TRADER_ID
        LEFT JOIN T_VERIFIES_INFO v on
        v.RELATE_TABLE_KEY = tcr.TRADER_CUSTOMER_ID
        and v.RELATE_TABLE = 'T_CUSTOMER_APTITUDE'
        LEFT JOIN T_TRADER t ON
        wa.TRADER_ID = t.TRADER_ID
        LEFT JOIN T_TRADER t1 ON
        wa1.TRADER_ID = t1.TRADER_ID
        where
        tc.TRADER_CONTACT_ID in
        <foreach item="traderContactId" index="index" collection="traderContactIdList" open="(" separator="," close=")">
            #{traderContactId,jdbcType=INTEGER}
        </foreach>
        GROUP BY
        tc.TRADER_CONTACT_ID
    </select>
    <select id="getLatestCommunicateContact" resultType="java.lang.Integer">
        select
            tc.TRADER_CONTACT_ID
        from
            T_TRADER_CONTACT tc
                LEFT JOIN T_COMMUNICATE_RECORD tcr ON
                    tcr.TRADER_CONTACT_ID = tc.TRADER_CONTACT_ID and tcr.TRADER_ID = #{traderId,jdbcType=INTEGER}
        where
            tc.TRADER_TYPE = 1
          and tc.TRADER_ID = #{traderId,jdbcType=INTEGER}
          and tc.IS_ON_JOB = 1
          and tc.IS_ENABLE = 1
          and tcr.COMMUNICATE_RECORD_ID is not null
        ORDER BY
            tcr.ADD_TIME DESC
        limit 1
    </select>
    <select id="getTraderContactListBySort" resultType="java.lang.Integer">
        select
            tc.TRADER_CONTACT_ID
        from
            T_TRADER_CONTACT tc
        WHERE
            tc.TRADER_ID = #{traderId,jdbcType=INTEGER}
            and tc.IS_ON_JOB = 1
            and tc.IS_ENABLE = 1
        ORDER BY
            (CASE
                 WHEN tc.`POSITION` = '销售负责人' THEN 1
                 WHEN tc.`POSITION` = '老板' THEN 2
                 WHEN tc.`POSITION` = '销售' THEN 3
                 WHEN tc.`POSITION` = '采购负责人' THEN 4
                 WHEN tc.`POSITION` = '采购' THEN 5
                 WHEN tc.`POSITION` = '商务' THEN 6
                 WHEN tc.`POSITION` = '物流负责人' THEN 7
                 WHEN tc.`POSITION` = '物流' THEN 8
                 ELSE 99
                END) ASC
        LIMIT 3
    </select>
</mapper>

