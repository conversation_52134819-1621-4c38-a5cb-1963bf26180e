package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.finance.dto.HxIncomeInvoiceDTO;
import com.vedeng.finance.model.HxInvoiceMockDto;
import com.vedeng.finance.service.HxInvoiceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Random;

/**
 * Mock航信发票信息
 *
 * <AUTHOR>
 * @date 2021/07/05
 */
@JobHandler("hxInvoiceMock")
@Component
public class HxInvoiceMockHandler extends AbstractJobHandler {
    private final static Logger logger = LoggerFactory.getLogger(HxInvoiceMockHandler.class);

    @Autowired
    private HxInvoiceService hxInvoiceService;

    @Value("${default_hxInvoice_mock}")
    private String defaultHxInvoiceMock;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        HxIncomeInvoiceDTO commonObject = JSON.parseObject(defaultHxInvoiceMock, HxIncomeInvoiceDTO.class);

        randomGenerateInvoice(s, commonObject);

        XxlJobLogger.log("Mock航信发票信息  参数:{}", s);
        mockHxInvoice(s, commonObject);

        return SUCCESS;
    }

    /**
     * 手动mock航信发票数据
     *
     * @param s
     * @param commonObject
     */
    private void mockHxInvoice(String s, HxIncomeInvoiceDTO commonObject) {
        if (StringUtil.isBlank(s)){
            return;
        }

        XxlJobLogger.log("Mock航次发票，发票信息用户填写" + s);
        logger.info("mock测试航信发票数据 s:{}", s);

        ArrayList<HxIncomeInvoiceDTO> hxIncomeInvoices = new ArrayList<>();
        JSON.parseArray(s, HxInvoiceMockDto.class).forEach(item -> {
            HxIncomeInvoiceDTO hxIncomeInvoiceDTO = new HxIncomeInvoiceDTO();
            BeanUtils.copyProperties(commonObject, hxIncomeInvoiceDTO);

            hxIncomeInvoiceDTO.setFpzt(StringUtil.isBlank(item.getFpzt()) ? hxIncomeInvoiceDTO.getFpzt() : item.getFpzt());

            hxIncomeInvoiceDTO.setFphm(StringUtil.isBlank(item.getInvoiceNo()) ? generateUID(8) : item.getInvoiceNo());
            hxIncomeInvoiceDTO.setFpdm(StringUtil.isBlank(item.getInvoiceCode()) ? generateUID(10) : item.getInvoiceCode());
            hxIncomeInvoiceDTO.setJshj(StringUtil.isBlank(item.getAmount()) ? generateUID(5) : item.getAmount());

            hxIncomeInvoiceDTO.setXfmc(StringUtil.isBlank(item.getXfmc()) ? hxIncomeInvoiceDTO.getXfmc() : item.getXfmc());
            hxIncomeInvoiceDTO.setXfdzdh(StringUtil.isBlank(item.getXfdzdh()) ? hxIncomeInvoiceDTO.getXfdzdh() : item.getXfdzdh());
            hxIncomeInvoiceDTO.setXfyhzh(StringUtil.isBlank(item.getXfyhzh()) ? hxIncomeInvoiceDTO.getXfyhzh() : item.getXfyhzh());

            hxIncomeInvoiceDTO.setGfsh(StringUtil.isBlank(item.getGfsh()) ? hxIncomeInvoiceDTO.getGfsh() : item.getGfsh());
            hxIncomeInvoiceDTO.setGfmc(StringUtil.isBlank(item.getGfmc()) ? hxIncomeInvoiceDTO.getGfmc() : item.getGfmc());
            hxIncomeInvoiceDTO.setGfdzdh(StringUtil.isBlank(item.getGfdzdh()) ? hxIncomeInvoiceDTO.getGfdzdh() : item.getGfdzdh());
            hxIncomeInvoiceDTO.setGfyhzh(StringUtil.isBlank(item.getGfyhzh()) ? hxIncomeInvoiceDTO.getGfyhzh() : item.getGfyhzh());

            hxIncomeInvoiceDTO.setKprq(StringUtil.isBlank(item.getKprq()) ? hxIncomeInvoiceDTO.getKprq() : item.getKprq());

            hxIncomeInvoiceDTO.setBz(StringUtil.isBlank(item.getBz()) ? hxIncomeInvoiceDTO.getBz() : item.getBz());

            hxIncomeInvoiceDTO.setKpr(StringUtil.isBlank(item.getKpr()) ? hxIncomeInvoiceDTO.getKpr() : item.getKpr());
            hxIncomeInvoiceDTO.setFhr(StringUtil.isBlank(item.getFhr()) ? hxIncomeInvoiceDTO.getFhr() : item.getFhr());
            hxIncomeInvoiceDTO.setSkr(StringUtil.isBlank(item.getSkr()) ? hxIncomeInvoiceDTO.getSkr() : item.getSkr());

            calculateInvoiceAmount(hxIncomeInvoiceDTO);
            hxIncomeInvoices.add(hxIncomeInvoiceDTO);
        });
        logger.info("Mock航信发票信息 hxIncomeInvoiceList:{}", JSON.toJSONString(hxIncomeInvoices));
        hxInvoiceService.convertAndSaveHxIncomeInvoice(hxIncomeInvoices);
    }

    /**
     * 计算发票金额数据
     *
     * @param hxIncomeInvoiceDTO
     */
    private void calculateInvoiceAmount(HxIncomeInvoiceDTO hxIncomeInvoiceDTO) {
        hxIncomeInvoiceDTO.setJe(new BigDecimal(hxIncomeInvoiceDTO.getJshj()).divide(new BigDecimal("1.13"), 2,  BigDecimal.ROUND_HALF_UP).toString());
        hxIncomeInvoiceDTO.setSe(new BigDecimal(hxIncomeInvoiceDTO.getJshj()).subtract(new BigDecimal(hxIncomeInvoiceDTO.getJe())).toString());
        hxIncomeInvoiceDTO.getDetailList().get(0).setJe(hxIncomeInvoiceDTO.getJe());
        hxIncomeInvoiceDTO.getDetailList().get(0).setSe(hxIncomeInvoiceDTO.getSe());
    }

    /**
     * 随机生成发票数据
     *
     * @param s
     * @param commonObject
     */
    private void randomGenerateInvoice(String s, HxIncomeInvoiceDTO commonObject) {
        if (!StringUtil.isBlank(s)){
            return;
        }
        XxlJobLogger.log("Mock航次发票，发票信息随机生成");
        HxIncomeInvoiceDTO hxIncomeInvoiceDTO = new HxIncomeInvoiceDTO();
        BeanUtils.copyProperties(commonObject, hxIncomeInvoiceDTO);
        hxIncomeInvoiceDTO.setFphm(generateUID(8));
        hxIncomeInvoiceDTO.setFpdm(generateUID(10));
        hxIncomeInvoiceDTO.setJshj(generateUID(5));
        calculateInvoiceAmount(hxIncomeInvoiceDTO);
        logger.info("Mock航信发票信息 hxIncomeInvoiceList:{}", JSON.toJSONString(hxIncomeInvoiceDTO));
        hxInvoiceService.convertAndSaveHxIncomeInvoice(Collections.singletonList(hxIncomeInvoiceDTO));
    }

    /**
     * 生成随机ID
     *
     * @param count 位数
     * @return
     */
    public static String generateUID(Integer count) {
        Random random = new Random();
        String result = "";
        for (int i = 0; i < count; i++) {
            //首字母不能为0
            result += (random.nextInt(count) + 1);
        }
        return result;
    }
}
