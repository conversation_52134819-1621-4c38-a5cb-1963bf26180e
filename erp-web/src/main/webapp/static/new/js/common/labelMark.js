void function () {
    var defaults = {
        el: '.J-lm-trigger',
        value: '.J-lm-value',
        multi: false,
        url: '',
        query: {},
        html: '',
        saveUrl:''
    };

    var LabelMark = function (config) {
        this.config = $.extend({}, defaults, config);
        this.__init();
        return this;
    }

    LabelMark.prototype = {
        constructor: 'LabelMark',
        __init: function () {
            // this.$currentClickItem = null;
            // this.$trigger = $(this.config.el);
            var _this = this;
            this.__initTmpl();
            $(document).on('click', '.J-lm-lv2-submit', function () {
                if ($('.J-lm-lv2-form').length && $('.J-lm-lv2-form').valid()) {
                    $('.J-lm-check-item.isOnClick').parent().find('.J-lm-check-item').each(function (i, item) {
                        var lv2ItemData = $(this).data('lv2');
                        lv2ItemData.date && (lv2ItemData.date = '');
                        lv2ItemData.reason && (lv2ItemData.reason = '');

                        $.each(lv2ItemData.skuList, function (i, item) {
                            lv2ItemData.skuList[i].choose = false;
                        })

                        $(this).data('lv2', lv2ItemData);
                        $(this).removeClass('selected');
                    });

                    var lv2Data = $('.J-lm-check-item.isOnClick').data('lv2');
                    var $form = $('.J-lm-lv2-form');
                    $.each(lv2Data.skuList, function (i) {
                        lv2Data.skuList[i].choose = false;
                    })

                    $.each($('.J-lm-lv2-prod-value', $form).val().split('@'), function (i, item) {
                        $.each(lv2Data.skuList, function (ii, item1) {
                            if (item == item1.skuNo) {
                                lv2Data.skuList[ii].choose = true;
                            }
                        })
                    });

                    if ($('[name=date]', $form).length) {
                        lv2Data.date = $('[name=date]', $form).val();
                    }
                    if ($('[name=reason]', $form).length) {
                        lv2Data.reason = $('[name=reason]', $form).val();
                    }

                    $('.J-lm-check-item.isOnClick').data('lv2', lv2Data);
                    $('.J-lm-check-item.isOnClick').addClass('selected');
                    $('.J-lm-lv2-dlg').remove();
                    _this.__checkSelectedData();
                    _this.__checkMainItem();
                }
            })

            $(document).on('click', '.J-lm-lv2-cancel', function () {
                $('.J-lm-lv2-dlg').remove();
            })
        },
        __initTmpl: function () {
            var _this = this;
            // this.$trigger.click(function () {
            if (_this.config.value) {
                _this.labelData = JSON.parse(_this.config.value);
                _this.__appendWrap();
            } else {
                $.ajax({
                    url: _this.config.url,
                    data: JSON.stringify(_this.config.query),
                    type: "POST",
                    dataType: "json",
                    contentType: "application/json",
                    success: function (res) {
                        if (res.code === 0) {
                            _this.labelData = res.data;
                            console.log(_this.labelData)
                            _this.__appendWrap();
                        }
                    }
                })
            }
            // });
        },
        __appendWrap: function () {
            var labelList = [];

            $.each(this.labelData.data, function (i, item) {
                var checkList = [];

                $.each(item.child, function (ii, checkItem) {
                    checkList.push('<div class="lm-checkbox-item ' + (checkItem.disabled ? 'disabled' : '') +  (checkItem.selected ? 'selected' : '') + ' J-lm-check-item" data-lv2=\'' + JSON.stringify(checkItem || {}) + '\'>' + checkItem.label + '</div>');
                });

                let tipLabel = '<div class="notmyneed customername pos_rel" style="display: inline">' +
                    '<i class="fa fa-exclamation-circle iconbluemouth contorlIcon" style="color: orange" ></i>' +
                    '<div class="pos_abs customernameshow mouthControlPos" label_left="-300" ' +
                    'style="font-size: 0.7em;width: 280px; top: 30px;color: #8A2BE2;padding: 2px;margin-left: -80px;">' +
                    '若业务需要，必须专向采购再发货，请选\"是\" </div></div>';
                if(item.label!='专向发货'){
                    tipLabel = '';
                }
                labelList.push(
                        '<div class="lm-main-item J-lm-main-item">' +
                            '<div class="item-label">' + item.label + tipLabel + '</div>'+
                            '<div class="item-field">' +
                                '<div class="lm-checkbox-list">' +
                                    checkList.join('') +
                                '</div>' +
                            '</div>' +
                        '</div>');
            });

            $('body').append('<div class="lm-wrap J-lm-lv1-wrap">' +
                    '<div class="lm-cnt">' +
                        '<i class="lm-close vd-icon icon-delete J-lm-lv1-cancel"></i>' +
                        '<div class="lm-title">内部备注</div>' +
                        '<div class="lm-main">' +
                            '<div class="lm-info-tip">' +
                                '<i class="info-icon vd-icon icon-caution2"></i>' +
                                '<div class="info-txt">请谨慎选择，供应链/物流将按照所选要求进行作业</div>' +
                            '</div>' +
                            '<div class="lm-main-block">' +
                                '<div class="lm-main-title">系统标签' +
                                    '<div class="lm-main-tip">系统标签选择后，系统将对采购/发货实现预警监管，销售可实时获取订单动态</div>' +
                                '</div>' +
                                '<div class="lm-main-list">' +
                                   labelList.join('') +
                                '</div>' +
                                '<div class="feedback-block J-lm-lv1-error" style="display: none;padding-left: 130px;">' +
                                    '<label class="error">系统标签所有类别必须要选一个值</label>' +
                                '</div>' +
                            '</div>' +
                            '<div class="lm-main-block">' +
                                '<div class="lm-main-title">人工备注' +
                                    '<div class="lm-main-tip">纯手工备注，系统将无法自动跟单，需人为跟踪</div>' +
                                '</div>' +
                                '<div class="lm-main-list">' +
                                    '<div class="lm-main-item">' +
                                        '<div class="item-label"></div>' +
                                        '<div class="item-field">' +
                                            '<div class="lm-checkbox-list">' +
                                                '<div class="lm-checkbox-item J-lm-man-made ' + (this.labelData.remark ? 'selected' : '') +'">人工备注</div>' +
                                            '</div>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                            '</div>' +
                            '<div class="lm-main-block">' +
                                '<div class="lm-main-preview J-lm-main-preview">' +
                                    '<div class="J-lm-pre-block1">' +

                                    '</div>' +
                                    '<div class="pre-item J-lm-pre-block2" style="display:none;">' +
                                        '<div class="item-label">人工备注：</div>' +
                                        '<div class="item-field">' +
                                            '<textarea class="input-edit">' + this.labelData.remark + '</textarea>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="lm-footer">' +
                            '<div class="lm-btn J-lm-lv1-confirm">确定</div>' +
                        '</div>' +
                    '</div>' +
                '</div>')

            this.__checkSelectedData();
            this.__bindEvent();
            bindInit();
        },
        __checkMainItem: function (flag) {
            var selected = $('.J-lm-check-item.selected');
            if (!this.config.query.hasRemark) {
                if (selected.length === this.labelData.data.length) {
                    $('.J-lm-lv1-error').hide();
                    return true;
                } else if (selected.length < this.labelData.data.length) {
                    if (flag) {
                        $('.J-lm-lv1-error').show();
                    }
                    return false;
                }
            } else {
                return true;
            }
        },
        __getSelectDomList: function(){
            var preList = [];
            var htmlList = [];
            $('.J-lm-main-item').each(function () {
                var $selected = $(this).find('.selected');
                if ($selected.length) {
                    var itemData = $selected.data('lv2');
                    var prods = [];
                    $.each(itemData.skuList, function (i, item) {
                        if (item.choose) {
                            prods.push(item.skuNo);
                        }
                    })

                    let tmp = $(this).find('.item-label').contents().filter(function() {
                        return this.nodeType === 3;
                    }).text();
                    // console.log(tmp);
                    // console.log($selected.html());
                    let temp2 = $selected.html() + '【产品：' + prods.join('/') + (itemData.date ? '预计日期：' + itemData.date : '') + (itemData.reason ? '原因：' + itemData.reason : '') + '】';
                    if(tmp == '专向发货'){
                        temp2 = $selected.html();
                    }
                    if ($selected.length) {
                        preList.push('<div class="pre-item">' +
                            '<div class="item-label">' + tmp +'</div>' +
                            '<div class="item-field">' + $selected.html() + '</div>' +
                        '</div>');
                        htmlList.push('<div class="pre-item">' +
                            '<div class="item-label">' + tmp + '</div>' +
                            '<div class="item-field">' + temp2 +'</div>' +
                        '</div>');

                    }
                }
            })

            return {
                preList: preList,
                htmlList: htmlList
            }
        },
        __checkSelectedData: function () {
            var _this = this
            var preList = this.__getSelectDomList().preList;

            $('.J-lm-pre-block1').empty().append(preList.join(''));

            var manmadeSelected = $('.J-lm-man-made').hasClass('selected');

            if (manmadeSelected) {
                $('.J-lm-pre-block2').show();
            } else {
                $('.J-lm-pre-block2').hide();
            }

            if (!preList.length && !manmadeSelected) {
                $('.J-lm-main-preview').hide();
            } else {
                $('.J-lm-main-preview').show();
            }
        },
        __getDefaultData: function(){
            var defaultData = this.labelData;

            defaultData.remark = '';
            var lv2DataList = [];
            $.each(defaultData.data, function(i, item){
                $.each(item.child, function(ii){
                    item.child[ii].date && (item.child[ii].date = '');
                    item.child[ii].selected = false;
                    item.child[ii].reason && (item.child[ii].reason = '');
                    $.each(item.child[ii].skuList, function (iii) {
                        item.child[ii].skuList[iii].choose = false;
                    })
                })

                lv2DataList.push(item);
            })
            defaultData.data = lv2DataList;
            return defaultData;
        },
        __bindEvent: function () {
            var _this = this;

            $('.J-lm-lv1-confirm').on('click', function () {
                if (_this.__checkMainItem(true)) {
                    var defaultData = _this.__getDefaultData();
                    $('.J-lm-check-item.selected').each(function(i){
                        var lv2Data = $(this).data('lv2')
                        $.each(defaultData.data, function(ii, item){
                            $.each(item.child, function (iii, item2) {
                                if (item2.id === lv2Data.id){
                                    defaultData.data[ii].child[iii] = $.extend({}, defaultData.data[ii].child[iii], lv2Data);
                                    defaultData.data[ii].child[iii].selected = true;
                                }
                            })
                        })
                    });

                    defaultData.remark = $('.J-lm-pre-block2 textarea').val() || '';
                    defaultData.labelQuery = _this.config.query;
                    var html = _this.__getSelectDomList().htmlList;
                    var componentHtml = '';
                    for (var k = 0; k < html.length; k++) {
                        componentHtml += html[k];
                    }
                    defaultData.labelQuery.componentHtml = componentHtml;
                    $(_this.config.el).attr('label_data', JSON.stringify(defaultData));
                    $(_this.config.el).val(defaultData.remark);
                    if (_this.config.multi) {
                        $(_this.config.el).siblings('.iconbluemouth').css('display','block');
                    }
                    if (_this.config.query.showComponentIcon) {
                        $(_this.config.el).siblings('.iconbluemouth').css('display','inline-block');
                    }
                    $(_this.config.el).siblings('.customernameshow').empty().append(componentHtml);
                    $('.J-lm-lv1-wrap').remove();
                    if (_this.config.saveUrl !== ''){
                        $.ajax({
                            url: _this.config.saveUrl,
                            data: JSON.stringify(defaultData),
                            type: "POST",
                            dataType: "json",
                            contentType: "application/json",
                            success: function (res) {
                                if (res.code === 0) {
                                    window.location.reload();
                                }
                            }
                        })
                    }

                }
            });

            $('.J-lm-lv1-cancel').on('click', function () {
                $('.J-lm-lv1-wrap').remove();
            });

            $('.J-lm-man-made').on('click', function () {
                if ($(this).hasClass('selected')) {
                    $(this).removeClass('selected');
                } else {
                    $(this).addClass('selected');
                }

                _this.__checkSelectedData();
            })

            $('.J-lm-check-item').on('click', function () {
                if ($(this).hasClass('disabled')) {
                    return;
                }
                $('.J-lm-check-item').removeClass('isOnClick');
                $(this).addClass('isOnClick');
                var lv2Data = $(this).data('lv2') || {};
                var prodsList = lv2Data.skuList;

                if ((prodsList.length > 1 && !_this.config.multi) || (lv2Data.needDate || lv2Data.needReason)) {
                    var prodDom = '';
                    var dateDom = '';
                    var reasonDom = '';
                    var prods = [];
                    $.each(prodsList, function (i, item) {
                        if (item.choose || _this.config.multi || prodsList.length == 1) {
                            prods.push(item.skuNo);
                        }
                    })

                    if (prodsList.length === 1 || _this.config.multi) {
                        prodDom = '<div class="lm-form-item">' +
                            '<div class="item-label"><span class="must">*</span>选择产品：</div>' +
                            '<div class="item-field">' +
                                '<div class="item-field-txt">'+prods.join(',')+'</div>' +
                                '<input type="hidden" class="J-lm-lv2-prod-value" value="' + prods.join('@') + '" name="prod">' +
                            '</div>' +
                        '</div>';
                    } else {
                        prodDom = '<div class="lm-form-item">' +
                                '<div class="item-label"><span class="must">*</span>选择产品：</div>' +
                                '<div class="item-field">' +
                                    '<div class="J-lm-lv2-prod-wrap"></div>' +
                                    '<input type="hidden" class="J-lm-lv2-prod-value" value="' + prods.join('@') +'" name="prod">' +
                                '</div>' +
                            '</div>';
                    }

                    if (lv2Data.needDate) {
                        dateDom = '<div class="lm-form-item">' +
                            '<div class="item-label"><span class="must">*</span>选择日期：</div>'+
                            '<div class="item-field">'+
                                '<div class="input-date-wrap input-date">'+
                                    '<input type="text" readonly class="input-text J-lm-date" value="' + (lv2Data.date || '') + '" name="date">'+
                                '</div>'+
                            '</div>'+
                        '</div>';
                    }

                    if (lv2Data.needReason) {
                        reasonDom = '<div class="lm-form-item">'+
                            '<div class="item-label"><span class="must">*</span>原因：</div>'+
                            '<div class="item-field">'+
                                '<textarea name="reason" class="input-textarea">'+(lv2Data.reason || '')+'</textarea>'+
                            '</div>'+
                        '</div>';
                    }

                    $('body').append('<div class="lm-wrap lm-choose J-lm-lv2-dlg">'+
                        '<div class="lm-cnt">'+
                            '<form class="lm-form J-lm-lv2-form">'+
                                prodDom +
                                dateDom +
                                reasonDom +
                                '</form>'+
                            '<div class="lm-footer">'+
                                '<div class="lm-btn J-lm-lv2-submit">确定</div>'+
                                '<div class="lm-btn btn-grey J-lm-lv2-cancel">取消</div>'+
                            '</div>'+
                        '</div>'+
                    '</div>');

                    $('.J-lm-lv2-dlg [name=reason]').blur(function(){
                        $(this).val($(this).val().replace(/'/g, '"'));
                    })

                    if (prodsList.length > 1 && !_this.config.multi) {
                        var selectData = [];
                        $.each(prodsList, function (i, item) {
                            selectData.push({
                                label: item.skuName,
                                value: item.skuNo
                            })
                        })

                        new SuggestSelect({
                            placeholder: '请选择',
                            wrap: '.J-lm-lv2-prod-wrap',
                            // async: true,
                            data: selectData,
                            input: '.J-lm-lv2-prod-value',
                            needDefault: false,
                            multi: true,
                            onchange: function (data) {
                                $('.J-lm-lv2-prod-value').valid();
                            }
                        })
                    }

                    if (lv2Data.needDate) {
                        new Pikaday({
                            format: 'yyyy-mm-dd',
                            field: $('.J-lm-date')[0],
                            minDate: new Date(),
                            firstDay: 1,
                            yearRange: [2000, 2200],
                            onSelect: function (val) {

                            }
                        });
                    }

                    $('.J-lm-lv2-form').validate({
                        rules: {
                            prod: {
                                required: true
                            },
                            date: {
                                required: true
                            },
                            reason: {
                                required: true
                            }
                        },
                        messages: {
                            prod: {
                                required: '请选择产品'
                            },
                            date: {
                                required: '请选择日期'
                            },
                            reason: {
                                required: '请填写原因'
                            }
                        }
                    })
                } else {
                    $(this).parent().find('.J-lm-check-item').each(function (i, item) {
                        var lv2ItemData = $(this).data('lv2');
                        lv2ItemData.date && (lv2ItemData.date = '');
                        lv2ItemData.reason && (lv2ItemData.reason = '');

                        $.each(lv2ItemData.skuList, function (i, item) {
                            lv2ItemData.skuList[i].choose = false;
                        })

                        $(this).data('lv2', lv2ItemData);
                        $(this).removeClass('selected');
                    });
                    var lv2Data = $(this).data('lv2');

                    $.each(lv2Data.skuList, function(i){
                        lv2Data.skuList[i].choose = true;
                    })

                    $(this).addClass('selected');
                    _this.__checkSelectedData();
                    _this.__checkMainItem();
                }
            })
        }
    }

    window.LabelMark = LabelMark;


}.call(this);


function bindInit() {

    $('.customername i').hover(function(){
        //解决需要悬浮框的元素在可见区域的底部不完全显示的问题；
        //思路：被选元素的位置，获取鼠标元素相对窗口的(x,y)坐标，此时元素上已绑定事件，
        var customernameshow=$(this).parents('.customername').find('.customernameshow');
        customernameshow.show();
        customernameshow.attr("position","relative");
    }, function() {
        var customernameshow=$(this).parents('.customername').find('.customernameshow');
        customernameshow.hide();
    })
}
