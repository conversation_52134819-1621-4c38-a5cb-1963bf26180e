package com.vedeng.order.model.vo;

import java.util.Date;
import java.util.List;

public class OrderLogisticsData {

   private Integer accountId; //账号id
    private String orderNo;//订单号
    private String logisticsNo	;//物流单号
    private Integer logisticsType;//物流类型 1、直发 2、普法
    private String logisticsCode;//快递公司编码
    private Integer state;//状态 101待发、102出库、3签收
    private Date checkoutTime;//出库时间
    private Date signTime;//签收时间

    public List<OrderLogisticsGoodsData> getOrderGoodsLogisticsList() {
        return orderGoodsLogisticsList;
    }

    public void setOrderGoodsLogisticsList(List<OrderLogisticsGoodsData> orderGoodsLogisticsList) {
        this.orderGoodsLogisticsList = orderGoodsLogisticsList;
    }

    List<OrderLogisticsGoodsData> orderGoodsLogisticsList;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Integer getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(Integer logisticsType) {
        this.logisticsType = logisticsType;
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getCheckoutTime() {
        return checkoutTime;
    }

    public void setCheckoutTime(Date checkoutTime) {
        this.checkoutTime = checkoutTime;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }
}
