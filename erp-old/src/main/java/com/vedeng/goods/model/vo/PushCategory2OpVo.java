package com.vedeng.goods.model.vo;



import java.util.List;
/**
 * @Description: 推送分类到op
 * @Auther: calvin
 * @Date: 2020/8/6 17:41
 */
public class PushCategory2OpVo {

    /**
     * 分类id
     */
    private Integer baseCategoryId;
    /**
     * 分类名称
     */
    private String baseCategoryName;
    /**
     * 分类等级
     */
    private Integer baseCategoryLevel;
    /**
     * 父分类id
     */
    private Integer parentCategoryId;
    /**
     * 父分类名称
     */
    private Integer parentCategoryName;
    /**
     * 分类下spu集合
     */
    private List<Spu> spuList;

    public Integer getBaseCategoryId() {
        return baseCategoryId;
    }

    public void setBaseCategoryId(Integer baseCategoryId) {
        this.baseCategoryId = baseCategoryId;
    }

    public String getBaseCategoryName() {
        return baseCategoryName;
    }

    public void setBaseCategoryName(String baseCategoryName) {
        this.baseCategoryName = baseCategoryName;
    }

    public Integer getBaseCategoryLevel() {
        return baseCategoryLevel;
    }

    public void setBaseCategoryLevel(Integer baseCategoryLevel) {
        this.baseCategoryLevel = baseCategoryLevel;
    }

    public Integer getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(Integer parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public Integer getParentCategoryName() {
        return parentCategoryName;
    }

    public void setParentCategoryName(Integer parentCategoryName) {
        this.parentCategoryName = parentCategoryName;
    }

    public List<Spu> getSpuList() {
        return spuList;
    }

    public void setSpuList(List<Spu> spuList) {
        this.spuList = spuList;
    }

    public static class Spu{
        /**
         * spu标识
         */
        private Integer spuId;
        /**
         * spu号
         */
        private String spuNo;

        public Integer getSpuId() {
            return spuId;
        }

        public void setSpuId(Integer spuId) {
            this.spuId = spuId;
        }

        public String getSpuNo() {
            return spuNo;
        }

        public void setSpuNo(String spuNo) {
            this.spuNo = spuNo;
        }
    }
}
