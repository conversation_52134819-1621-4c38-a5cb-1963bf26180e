<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.AuthorizationStorageMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.order.model.AuthorizationStorage">
        <id column="TEMPORARY_STORAGE_ID" property="temporaryStorageId" jdbcType="INTEGER"/>
        <result column="AUTHORIZATION_APPLY_NUM" property="authorizationApplyNum" jdbcType="VARCHAR"/>
        <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER"/>
        <result column="SKU_ID" property="skuId" jdbcType="INTEGER"/>
        <result column="PURCHASE_OR_BIDDING" property="purchaseOrBidding" jdbcType="VARCHAR"/>
        <result column="PRODUCT_COMPANY" property="productCompany" jdbcType="VARCHAR"/>
        <result column="NATURE_OF_OPERATION" property="natureOfOperation" jdbcType="INTEGER"/>
        <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR"/>
        <result column="SKU_NAME" property="skuName" jdbcType="VARCHAR"/>
        <result column="SKU_MODEL" property="skuModel" jdbcType="VARCHAR"/>
        <result column="DISTRIBUTIONS_TYPE" property="distributionsType" jdbcType="INTEGER"/>
        <result column="AUTHORIZED_COMPANY" property="authorizedCompany" jdbcType="VARCHAR"/>
        <result column="PURCHASE_PROJECT_NAME" property="purchaseProjectName" jdbcType="VARCHAR"/>
        <result column="PURCHASE_PROJECT_NUM" property="purchaseProjectNum" jdbcType="VARCHAR"/>
        <result column="FILE_TYPE" property="fileType" jdbcType="INTEGER"/>
        <result column="AFTERSALES_COMPANY" property="aftersalesCompany" jdbcType="VARCHAR"/>
        <result column="BEGIN_TIME" property="beginTime" jdbcType="BIGINT"/>
        <result column="END_TIME" property="endTime" jdbcType="BIGINT"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="UPDATOR" property="updator" jdbcType="INTEGER"/>
        <result column="DESCRIBED" property="described" jdbcType="VARCHAR"/>
        <result column="NUM" property="num" jdbcType="INTEGER"/>
        <result column="IS_DELETED" property="isDeleted" jdbcType="INTEGER"/>
        <result column="APPLY_YEAR" property="applyYear" jdbcType="VARCHAR"/>
        <result column="APPLY_MONTH" property="applyMonth" jdbcType="VARCHAR"/>
        <result column="APPLY_DAY" property="applyDay" jdbcType="VARCHAR"/>
        <result column="STANDARD_TEMPLATE" property="standardTemplate" jdbcType="INTEGER"/>
        <result column="NON_STANDARD_AUTHORIZATION_URL" property="nonStandardAuthorizationUrl" jdbcType="VARCHAR"/>
        <result column="NON_STANDARD_AUTHORIZATION_NAME" property="nonStandardAuthorizationName" jdbcType="VARCHAR"/>
        <result column="WHETHER_SIGN" property="whetherSign" jdbcType="INTEGER"/>
        <result column="AUTH_TYPE" property="authType" javaType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        TEMPORARY_STORAGE_ID,
        AUTHORIZATION_APPLY_NUM,
        QUOTEORDER_ID,
        SKU_ID,
        PURCHASE_OR_BIDDING,
        PRODUCT_COMPANY,
        NATURE_OF_OPERATION,
        BRAND_NAME,
        SKU_NAME,
        SKU_MODEL,
        DISTRIBUTIONS_TYPE,
        AUTHORIZED_COMPANY,
        PURCHASE_PROJECT_NAME,
        PURCHASE_PROJECT_NUM,
        FILE_TYPE,
        AFTERSALES_COMPANY,
        BEGIN_TIME,
        END_TIME,
        ADD_TIME,
        MOD_TIME,
        CREATOR,
        UPDATOR,
        DESCRIBED,
        NUM,
        IS_DELETED,
        APPLY_YEAR,
        APPLY_MONTH,
        APPLY_DAY,
        STANDARD_TEMPLATE,
        NON_STANDARD_AUTHORIZATION_URL,
        NON_STANDARD_AUTHORIZATION_NAME,
        WHETHER_SIGN,
        AUTH_TYPE,
        SEAL_TYPE
    </sql>


    <select id="getAuthorizationStorageInfoByQuoteOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AUTHORIZATION_TEMPORARY_STORAGE
        where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
          and AUTHORIZATION_APPLY_NUM is null
          and IS_DELETED = 0
        LIMIT 1
    </select>

    <select id="getAuthorizationStorageInfoListByQuoteOrderId" resultType="com.vedeng.order.model.AuthorizationStorage">
        select
        <include refid="Base_Column_List"/>
        from T_AUTHORIZATION_TEMPORARY_STORAGE
        where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
        and AUTHORIZATION_APPLY_NUM is null
        and IS_DELETED = 0
    </select>

    <insert id="insertAuthorizationStorageInfo" parameterType="com.vedeng.order.model.AuthorizationStorage" useGeneratedKeys="true" keyProperty="temporaryStorageId">
        insert into T_AUTHORIZATION_TEMPORARY_STORAGE(AUTHORIZATION_APPLY_NUM, QUOTEORDER_ID, SKU_ID,
                                                      PURCHASE_OR_BIDDING, PRODUCT_COMPANY,
                                                      NATURE_OF_OPERATION, BRAND_NAME, SKU_NAME, SKU_MODEL,
                                                      DISTRIBUTIONS_TYPE, AUTHORIZED_COMPANY, PURCHASE_PROJECT_NAME,
                                                      PURCHASE_PROJECT_NUM, FILE_TYPE, AFTERSALES_COMPANY, BEGIN_TIME,
                                                      END_TIME,
                                                      ADD_TIME, MOD_TIME, CREATOR, UPDATOR, DESCRIBED, NUM, APPLY_YEAR,
                                                      APPLY_MONTH, APPLY_DAY, STANDARD_TEMPLATE,
                                                      NON_STANDARD_AUTHORIZATION_URL,
                                                      NON_STANDARD_AUTHORIZATION_NAME,WHETHER_SIGN,AUTH_TYPE,SEAL_TYPE)
        values (#{authorizationApplyNum,jdbcType=VARCHAR}, #{quoteorderId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER},
                #{purchaseOrBidding,jdbcType=VARCHAR},
                #{productCompany,jdbcType=VARCHAR}, #{natureOfOperation,jdbcType=INTEGER},
                #{brandName,jdbcType=INTEGER}, #{skuName,jdbcType=INTEGER},
                #{skuModel,jdbcType=INTEGER}, #{distributionsType,jdbcType=INTEGER},
                #{authorizedCompany,jdbcType=VARCHAR}, #{purchaseProjectName,jdbcType=VARCHAR},
                #{purchaseProjectNum,jdbcType=VARCHAR}, #{fileType,jdbcType=INTEGER},
                #{aftersalesCompany,jdbcType=VARCHAR},
                #{beginTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT},
                #{modTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
                #{updator,jdbcType=INTEGER}, #{described,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER},
                #{applyYear,jdbcType=VARCHAR}, #{applyMonth,jdbcType=VARCHAR}, #{applyDay,jdbcType=VARCHAR},
                #{standardTemplate,jdbcType=INTEGER},
                #{nonStandardAuthorizationUrl,jdbcType=VARCHAR},
                #{nonStandardAuthorizationName,jdbcType=VARCHAR},
                #{whetherSign,jdbcType=INTEGER},
                #{authType,jdbcType=INTEGER},
                #{sealType,jdbcType=INTEGER}

        )
    </insert>

    <update id="updateAuthorizationStorageInfo" parameterType="com.vedeng.order.model.AuthorizationStorage">
        update T_AUTHORIZATION_TEMPORARY_STORAGE
        set AUTHORIZATION_APPLY_NUM = #{authorizationApplyNum,jdbcType=VARCHAR},
            QUOTEORDER_ID           = #{quoteorderId,jdbcType=INTEGER},
            SKU_ID                  = #{skuId,jdbcType=INTEGER},
            PURCHASE_OR_BIDDING     = #{purchaseOrBidding,jdbcType=VARCHAR},
            PRODUCT_COMPANY         = #{productCompany,jdbcType=VARCHAR},
            NATURE_OF_OPERATION     = #{natureOfOperation,jdbcType=INTEGER},
            BRAND_NAME              = #{brandName,jdbcType=INTEGER},
            SKU_NAME                = #{skuName,jdbcType=INTEGER},
            SKU_MODEL               = #{skuModel,jdbcType=INTEGER},
            DISTRIBUTIONS_TYPE      = #{distributionsType,jdbcType=INTEGER},
            AUTHORIZED_COMPANY      = #{authorizedCompany,jdbcType=VARCHAR},
            PURCHASE_PROJECT_NAME   = #{purchaseProjectName,jdbcType=VARCHAR},
            PURCHASE_PROJECT_NUM    = #{purchaseProjectNum,jdbcType=VARCHAR},
            FILE_TYPE               = #{fileType,jdbcType=INTEGER},
            AFTERSALES_COMPANY      = #{aftersalesCompany,jdbcType=VARCHAR},
            BEGIN_TIME              = #{beginTime,jdbcType=VARCHAR},
            END_TIME                = #{endTime,jdbcType=VARCHAR},
            MOD_TIME                = #{modTime,jdbcType=BIGINT},
            UPDATOR                 = #{updator,jdbcType=INTEGER},
            DESCRIBED               = #{described,jdbcType=VARCHAR},
            NUM                     = #{num,jdbcType=INTEGER},
            APPLY_YEAR              = #{applyYear,jdbcType=VARCHAR},
            APPLY_MONTH             = #{applyMonth,jdbcType=VARCHAR},
            APPLY_DAY               = #{applyDay,jdbcType=VARCHAR},
            STANDARD_TEMPLATE       = #{standardTemplate,jdbcType=INTEGER},
            NON_STANDARD_AUTHORIZATION_URL       = #{nonStandardAuthorizationUrl,jdbcType=VARCHAR},
            NON_STANDARD_AUTHORIZATION_NAME      = #{nonStandardAuthorizationName,jdbcType=VARCHAR},
            WHETHER_SIGN            = #{whetherSign,jdbcType=INTEGER},
            AUTH_TYPE               = #{authType,jdbcType=INTEGER},
            SEAL_TYPE               = #{sealType,jdbcType=INTEGER}

        where TEMPORARY_STORAGE_ID = #{temporaryStorageId,jdbcType=INTEGER}
          and IS_DELETED = 0
    </update>

    <update id="delAuthorizationStorageById">
        update T_AUTHORIZATION_TEMPORARY_STORAGE
        set IS_DELETED=1,
            MOD_TIME=#{time,jdbcType=BIGINT},
            UPDATOR   = #{userId,jdbcType=INTEGER}
        where TEMPORARY_STORAGE_ID = #{temporaryStorageId,jdbcType=INTEGER}
    </update>

    <select id="getTemporaryStorageByNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AUTHORIZATION_TEMPORARY_STORAGE
        where AUTHORIZATION_APPLY_NUM = #{authorizationApplyNum,jdbcType=VARCHAR}
          and IS_DELETED = 0
    </select>

    <update id="updateAuthorizationStorage">
        update T_AUTHORIZATION_TEMPORARY_STORAGE
        set IS_DELETED=1
        where AUTHORIZATION_APPLY_NUM = #{authorizationApplyNum,jdbcType=VARCHAR}
    </update>

    <select id="getAuthorizationStorageInfoByQuoteOrderIdAndNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AUTHORIZATION_TEMPORARY_STORAGE
        where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
          and AUTHORIZATION_APPLY_NUM = #{authorizationApplyNum,jdbcType=VARCHAR}
          and IS_DELETED = 0
    </select>
</mapper>