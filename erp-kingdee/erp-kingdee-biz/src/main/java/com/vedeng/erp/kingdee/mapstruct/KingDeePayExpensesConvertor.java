package com.vedeng.erp.kingdee.mapstruct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayExpensesEntity;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;

import cn.hutool.core.collection.CollUtil;

import java.util.Collections;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应付单明细  贝登dto 转 金蝶 command
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeePayExpensesConvertor extends BaseMapStruct<KingDeePayExpensesEntity, KingDeePayExpensesDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "FEntityDetail", source = "FEntityDetail", qualifiedByName = "expensesToJsonArray")
    @Override
    KingDeePayExpensesEntity toEntity(KingDeePayExpensesDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "FEntityDetail", source = "FEntityDetail", qualifiedByName = "expensesJsonArrayToList")
    @Override
    KingDeePayExpensesDto toDto(KingDeePayExpensesEntity entity);
	
    
    /**
     * entity 中JSONArray 转 原对象
     *
     * @param payBillEntryDtos JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("expensesJsonArrayToList")
    default List<KingDeePayExpensesDetailDto> entryJsonArrayToList(JSONArray payBillEntryDtos) {
        if (CollUtil.isEmpty(payBillEntryDtos)) {
            return Collections.emptyList();
        }
        return payBillEntryDtos.toJavaList(KingDeePayExpensesDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("expensesToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeePayExpensesDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
    
}
