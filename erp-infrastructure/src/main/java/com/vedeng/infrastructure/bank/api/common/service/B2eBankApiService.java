package com.vedeng.infrastructure.bank.api.common.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.infrastructure.bank.api.common.XmlConverter;
import com.vedeng.infrastructure.bank.api.domain.api.B2eFileUploadApiReq;
import com.vedeng.infrastructure.bank.api.domain.api.B2eFileUploadApiRes;
import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import com.vedeng.infrastructure.bank.api.domain.base.CMBCRequest;
import com.vedeng.infrastructure.bank.api.domain.B2eSignRes;
import com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankApiRecord;
import com.vedeng.infrastructure.bank.api.enums.BankApiType;
import com.vedeng.infrastructure.bank.api.mapper.MinshengBankApiRecordMapper;
import com.vedeng.infrastructure.bank.api.util.BankThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/10/11 16:21
 */
@Service
@Slf4j
public class B2eBankApiService {

    @Value("${bank.api.url:http://************:8080/eweb/b2e/connect.do}")
    private String bankApiUrl;
    @Value("${bank.api.fileUrl:http://obpgateway.cmbc.com.cn:10284/custom/b2e}")
    private String fileApiUrl;

    @Autowired
    private MinshengBankApiRecordMapper minshengBankApiRecordMapper;

    /**
     * 调用远程服务
     * 该方法负责将请求对象转换为XML格式，并发送到远程银行API，然后解析响应并将其转换为指定的响应对象
     *
     * @param requestObject 请求对象，将被转换为XML并发送到远程API
     * @param responseClass 响应对象的类，用于将XML响应解析为具体对象
     * @param <T>           请求对象的类型
     * @param <R>           响应对象的类型
     * @return 解析后的响应对象
     * @throws Exception 如果调用远程服务失败
     */
    @SuppressWarnings("unchecked")
    public <T extends CMBCRequest<?>, R extends BankResponse> R callRemote(T requestObject, Class<R> responseClass) {

        R responseObject = null;
        String apiName = BankApiType.getName(requestObject.getTrnCode());
        try {
            log.info("调用民生银行接口:{},请求参数:{}", apiName, JSON.toJSONString(requestObject));

            String xmlRequest = XmlConverter.convertToXml(requestObject);

            log.info("调用民生银行接口:{},请求参数XML:{}", apiName, xmlRequest);

            responseObject = responseClass.newInstance();

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(bankApiUrl)
                    .header("Content-type", "application/x-NS-BDES")
                    .body(xmlRequest)
                    .timeout(60000)
                    .execute();

            String res = response.body();
            log.info("调用民生银行接口:{},返回值XML:{} ", apiName, res);

            if (!response.isOk()) {
                log.error("调用民生银行接口:{},调用失败，响应内容: {}", apiName, res);
                responseObject.fail(responseObject, "调用民生银行接口【" + apiName + "】失败,响应内容: " + res);
                return responseObject;
            }

            responseObject = XmlConverter.convertToObject(res, responseClass);
            log.info("调用民生银行接口:{},返回值:{}", apiName, JSON.toJSONString(responseObject));

            this.insertBankApiRecord(xmlRequest, res, BankApiType.getCode(requestObject.getTrnCode()));

            responseObject.checkAndSetResponseHeader(responseObject, "调用民生银行接口【" + apiName + "】失败");
            return responseObject;
        } catch (Exception e) {
            log.error("调用民生银行接口失败", e);
            responseObject.fail(responseObject, "调用民生银行接口【" + apiName + "】失败" + e.getMessage());
            return responseObject;
        }
    }


    public B2eFileUploadApiRes upload(B2eFileUploadApiReq b2eFileUploadApiReq) {
        File file = null;
        try {
            file = this.downloadFile(b2eFileUploadApiReq.getOssUrl());
            log.info("调用民生银行文件上传文数据: {},{}", file.getName(), file.getPath());

            String url = fileApiUrl + "/" + BankApiType.B2E_UPLOAD.getTrnCode() + "?sequence=" + IdUtil.simpleUUID();
            log.info("调用民生银行文件上传请求URL: {}", url);

            // 创建POST请求
            HttpRequest request = HttpUtil.createPost(url).timeout(60000);
            request.header("Content-Type", "multipart/form-data");
            // 上传文件时传入 File 对象
            request.form("files", file);
            request.form("fileToken", b2eFileUploadApiReq.getFileToken());
            request.form("sceneId", b2eFileUploadApiReq.getSceneId());
            request.form("isUnzip", b2eFileUploadApiReq.getIsUnzip());

            log.info("调用民生银行文件上传请求参数: {}", JSON.toJSONString(request));

            // 发送请求
            HttpResponse response = request.execute();
            if (!response.isOk()) {
                log.error("调用民生银行文件上传失败！响应内容: {}", response.body());
                return B2eFileUploadApiRes.fail("文件上传失败：" + response.body());
            }

            log.info("调用民生银行文件上传：响应内容: {}", response.body());
            B2eFileUploadApiRes b2eFileUploadApiRes = JSON.parseObject(response.body(), B2eFileUploadApiRes.class);
            log.info("调用民生银行文件上传：返回结果: {}", JSON.toJSONString(b2eFileUploadApiRes));

            return b2eFileUploadApiRes;

        } catch (IOException e) {
            log.error("文件上传过程发生IO异常！", e);
            return B2eFileUploadApiRes.fail("文件上传失败！：" + e.getMessage());
        } catch (Exception e) {
            log.error("调用民生银行文件上传失败！", e);
            return B2eFileUploadApiRes.fail("文件上传失败！：" + e.getMessage());
        } finally {
            // 删除临时文件
            if (file != null && file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("临时文件已成功删除：{}", file.getAbsolutePath());
                } else {
                    log.warn("临时文件删除失败：{}", file.getAbsolutePath());
                }
            }
        }
    }


    /**
     * 发送签名请求
     *
     * @param fileSealhash 预申请接口返回的 fileSealhash 字段值
     * @return 协议签名值的响应内容
     */
    public B2eSignRes sendSignRequest(String fileSealhash) {
        log.info("调用民生银行发送签名请求fileSealhash:{}", fileSealhash);
        B2eSignRes b2eSignRes = new B2eSignRes();
        String apiName = BankApiType.getName(BankApiType.CMBC_SIGN_HASH.getTrnCode());
        try {
            HttpRequest request = HttpRequest.post(bankApiUrl)
                    .header("Accept-Encoding", "*")
                    .header("Business-Type", "CMBC_SIGN_HASH")
                    .header("Content-Type", "application/x-NS-BDES")
                    .body(fileSealhash);

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                log.error("调用民生银行发送签名请求失败！响应内容: {}", response);
                b2eSignRes.fail(b2eSignRes, "调用民生银行发送签名请求失败！响应内容: " + response);
                return b2eSignRes;
            }

            String body = response.body();
            log.info("调用民生银行发送签名请求返回值:{}", body);

            if (StrUtil.isEmpty(body)) {
                b2eSignRes.fail(b2eSignRes, "调用民生银行接口【" + apiName + "】失败，无返回数据");
                return b2eSignRes;
            }

            b2eSignRes = XmlConverter.convertToObject(body, B2eSignRes.class);
            log.info("调用民生银行接口:{},返回值:{}", apiName, JSON.toJSONString(b2eSignRes));

            this.insertBankApiRecord(fileSealhash, body, BankApiType.getCode(BankApiType.CMBC_SIGN_HASH.getTrnCode()));

            b2eSignRes.checkAndSetResponseHeader(b2eSignRes, "调用民生银行发送签名请求执行失败");

            return b2eSignRes;
        } catch (Exception e) {
            log.error("调用民生银行接口失败", e);
            b2eSignRes.fail(b2eSignRes, "调用民生银行接口【" + apiName + "】失败" + e.getMessage());
            return b2eSignRes;
        }
    }

    /**
     * 异步记录接口调用记录
     *
     * @param requestBody  请求体
     * @param responseBody responseBody
     * @param apiType      apiType
     */
    private void insertBankApiRecord(String requestBody, String responseBody, Integer apiType) {
        BankThreadPool.submit(() -> {
            MinshengBankApiRecord minshengBankApiRecord = new MinshengBankApiRecord();
            try {
                minshengBankApiRecord.setRequestBody(requestBody);
                //responseBody截位1000
                String response = StrUtil.sub(responseBody, 0, 1000);
                minshengBankApiRecord.setResponseBody(StrUtil.nullToEmpty(response));
                minshengBankApiRecord.setApiType(apiType);
                minshengBankApiRecordMapper.insertRecord(minshengBankApiRecord);
            } catch (Exception e) {
                log.error("异步保存民生银行操作记录失败", e);
            }
        });
    }


    private File downloadFile(String url) throws IOException {
        if (url == null || url.isEmpty()) {
            throw new IllegalArgumentException("URL cannot be null or empty");
        }

        HttpResponse response = HttpRequest.get(url.replace("/display", "/download")).execute();

        // 获取响应头中的 Content-Disposition
        String contentDisposition = response.header("Content-Disposition");

        // 默认文件名（如果获取不到文件名）
        String defaultFileName = "unknown_file";

        // 解析文件名
        String fileName = defaultFileName;
        if (contentDisposition != null && contentDisposition.contains("filename=")) {
            // 提取文件名，注意可能有双引号
            fileName = contentDisposition.split("filename=")[1].trim().replace("\"", "");
        }

        // 下载文件到本地
        byte[] fileContent = response.bodyBytes();

        // 使用 try-with-resources 确保 FileOutputStream 被正确关闭
        File file = new File(System.getProperty("java.io.tmpdir"), fileName);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(fileContent);
        }

        return file;
    }


    public static void main(String[] args) {
        B2eSignRes b2eSignRes = new B2eSignRes();
        String apiName = BankApiType.getName(BankApiType.CMBC_SIGN_HASH.getTrnCode());
        try {
            HttpRequest request = HttpRequest.post("http://************:8080/eweb/b2e/connect.do")
                    .header("Accept-Encoding", "*")
                    .header("Business-Type", "CMBC_SIGN_HASH")
                    .header("Content-Type", "application/x-NS-BDES")
                    .body("yqW6B4mXyNl57BKM4O4fU2173Rk=");

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                log.error("调用民生银行发送签名请求失败！响应内容: {}", response);
                b2eSignRes.fail(b2eSignRes, "调用民生银行发送签名请求失败！响应内容: " + response);
            }

            String body = response.body();
            log.info("调用民生银行发送签名请求返回值:{}", body);

            b2eSignRes = XmlConverter.convertToObject(body, B2eSignRes.class);
            log.info("调用民生银行接口:{},返回值:{}", apiName, JSON.toJSONString(b2eSignRes));

            b2eSignRes.checkAndSetResponseHeader(b2eSignRes, "调用民生银行发送签名请求执行失败");

        } catch (Exception e) {
            log.error("调用民生银行接口失败", e);
            b2eSignRes.fail(b2eSignRes, "调用民生银行接口【" + apiName + "】失败" + e.getMessage());
        }

        System.out.println(JSON.toJSONString(b2eSignRes));
    }
}
