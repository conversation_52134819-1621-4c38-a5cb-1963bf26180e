package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderContactDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/4 13:56
 **/
public interface TraderContactApiService {

    /**
     * 模糊查询
     */
    List<TraderContactDto> findLikeNameAndMobile(String keywords, Integer traderId, Integer traderType);

    /**
     * 保存客户联系人
     *
     * @param traderContactErpDto 数据
     */
    void add(TraderContactDto traderContactErpDto);

    /**
     * 更新客户联系人
     *
     * @param traderContactErpDto 数据
     */
    void edit(TraderContactDto traderContactErpDto);

    /**
     * 设为默认联系人
     *
     * @param traderId        traderId
     * @param traderContactId traderContactId
     */
    void setDefault(Integer traderId, Integer traderContactId);

    /**
     * 根据客户类别返回不同的职业集合
     *
     * @param traderId 客户id
     * @return List<String> 职位
     */
    List<String> getPosition(Integer traderId);

    /**
     * 启用禁用
     *
     * @param traderContactErpDto
     */
    void doEnable(TraderContactDto traderContactErpDto);

    /**
     * 置顶
     *
     * @param traderId        traderId
     * @param traderContactId traderContactId
     * @param top             top
     */
    void top(Integer traderId, Integer traderContactId, Integer top);
}
