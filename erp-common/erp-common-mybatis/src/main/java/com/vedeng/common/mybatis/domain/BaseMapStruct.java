package com.vedeng.common.mybatis.domain;

import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基础dto，model相互转换方法
 * @date 2022/3/4 14:41
 */
public interface BaseMapStruct<T, D> {

    /**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    T toEntity(D dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    D toDto(T entity);

    /**
     * DTO集合转Entity集合
     *
     * @param dtoList
     * @return
     */
    List<T> toEntity(List<D> dtoList);

    /**
     * Entity集合转DTO集合
     *
     * @param entityList
     * @return
     */
    List<D> toDto(List<T> entityList);

    /**
     * 修改
     * 将 dto 的所有不为null的值复制给t
     *
     * @param dto :
     * @param t   :
     * @return T
     * <AUTHOR>
     * @date 2020-09-18 下午 16:47
     */
    T update(D dto, @MappingTarget T t);

}