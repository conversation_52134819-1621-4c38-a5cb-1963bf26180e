package com.vedeng.authorization.service.impl;

import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import javax.annotation.Resource;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * 权限service实现类.
 * @jira: VDERP-5839 数据越权修复.
 * @notes: .
 * @version: 1.0.
 * @date: 2021/3/16 上午10:29.
 * @author: Tomcat.Hui.
 */
@Service
public class AuthServiceImpl implements AuthService {
    Logger logger = LoggerFactory.getLogger(AuthServiceImpl.class);

    @Resource
    private UserMapper userMapper;

    @Resource
    private RoleMapper roleMapper;

    @Autowired
    private CommunicateRecordMapper communicateRecordMapper;

    @Value("${finance_role_name}")
    private String financeRoleNameStr;

    @Override
    public User getTraderOwnerUser(Integer traderId, Integer traderType) {
        return userMapper.getUserByTraderId(traderId, traderType);
    }

    @Override
    public User getUserById(Integer userId) {
       return userMapper.selectByPrimaryKey(userId);
    }

    @Override
    public List<User> getUserByRoleNameList(List<String> rolenameList) {
        return roleMapper.getUserByRoleNameList(rolenameList);
    }

    @Override
    public Boolean checkUserInRole(User user,String roleNameStr) {
        List<String> financeRoleNameList = new ArrayList<>();
        if (roleNameStr.contains(",")) {
            String[] split = roleNameStr.split(",");
            financeRoleNameList.addAll(Arrays.asList(split));
        } else {
            financeRoleNameList.add(roleNameStr);
        }
        List<User> userByRoleNameList = this.getUserByRoleNameList(financeRoleNameList);
        if(user !=null && CollectionUtils.isNotEmpty(userByRoleNameList)){
            User checkUser = userByRoleNameList.stream().filter(e -> e.getUserId().equals(user.getUserId())).findAny().orElse(null);
            if (null != checkUser) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<User> getUserParentListById(Integer userId) {
        List<User> userList = new ArrayList<>();
        Set<String> userIdSes=new HashSet<>();
      //  while (true){
        for (int i=0;i<10;i++){
            User userParent = userMapper.getUserParentById(userId);
            if (Objects.isNull(userParent)){
                break;
            }
            userList.add(userParent);
            userId = userParent.getUserId();
            if (userParent.getParentId() == 0){
                break;
            }
            if(i>8){
                logger.error("循环上下级：：{}",userId);
            }
        }

      //  }
            return userList;
    }

    @Override
    public  List<User> getUserListByorderId(Integer id,String type){
        Integer ownerUserIdByorderId = getOwnerUserIdByorderId(id, type);
        if (Objects.isNull(ownerUserIdByorderId)){
            return null;
        }
        User user = new User();
        user.setUserId(ownerUserIdByorderId);
        List<User> userParentListById = getUserParentListById(ownerUserIdByorderId);
        userParentListById.add(user);
        User njadmin = new User();
        njadmin.setUserId(2);
        userParentListById.add(njadmin);
        return userParentListById;
    }

    @Override
    public Boolean existOrNot(User user, List<User> userList) {
        if (Objects.nonNull(user) && CollectionUtils.isNotEmpty(userList)){
            Optional<User> first = userList.stream().filter(item -> item.getUserId().equals(user.getUserId())).findFirst();
            if (first.isPresent()){
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Integer getOwnerUserIdByorderId(Integer id, String orderType) {
        Integer userId = 0;
        if (BUSSCHANCE_TYPE.equals(orderType)){
            userId = userMapper.getBussinessChanceOwnerById(id);
        }

        if (BH_SALEORDER_TYPE.equals(orderType)){
            userId = userMapper.getBHSaleOrderOwnerById(id);
        }

        if (SALEORDER_TYPE.equals(orderType)){
            userId = userMapper.getSaleorderOwnerById(id);

        }
        if (QUOTEORDER_TYPE.equals(orderType)){
            userId = userMapper.getQuoteorderOwnerById(id);
        }

        if (TRADER_CUSTOMER_TYPE.equals(orderType)){
            User userByTraderId = userMapper.getUserByTraderId(id, ErpConst.ONE);
            if (Objects.nonNull(userByTraderId)){
                userId = userByTraderId.getUserId();
            }
        }
        if (AUTHORIZATION_APPLY_TYPE.equals(orderType)){
            userId=userMapper.getAuthorizationApplyOwnerById(id);
        }
        return userId;
    }

    @Override
    public Integer getSaleorderIdByCommunicateRecordId(Integer communicateRecordId) {
        return communicateRecordMapper.getCommunicateRecordById(communicateRecordId);
    }

    @Override
    public boolean checkUserIsSale(User user) {
        // 销售
        if (user != null && user.getPositType() != null && user.getPositType() == 310) {
            return  true;
        }
        return false;
    }
}
