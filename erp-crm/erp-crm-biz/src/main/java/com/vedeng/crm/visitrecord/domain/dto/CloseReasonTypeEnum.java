package com.vedeng.crm.visitrecord.domain.dto;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/28
 */
public enum CloseReasonTypeEnum {
    CUSTOMER_TEMP_ABSENT(1, "客户临时出差/不在场"),
    CUSTOMER_CANCEL_LATION(2, "客户主动取消预约"),
    CUSTOMER_DEMAND_RESOLUTION(3, "客户需求已解决"),
    LEAD_BUSSINESS_CLOSED(4, "线索/商机已关闭"),
    SALE_PERSONAL_BUSY(5, "个人时间冲突"),
    NO_REPLY(6, "其他");


    private Integer closeReasonType;
    private String closeReasonTypeName;

    CloseReasonTypeEnum(Integer closeReasonType, String closeReasonTypeName) {
        this.closeReasonType = closeReasonType;
        this.closeReasonTypeName = closeReasonTypeName;
    }

    public Integer getCloseReasonType() {
        return closeReasonType;
    }

    public String getCloseReasonTypeName() {
        return closeReasonTypeName;
    }
}
