<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />

<style>
    .radio-item {
        display: block;
        margin-bottom: 15px;
        margin-inline: 25px;
        margin-top: 15px;
    }
</style>
<div id="app">
    <div style="padding: 50px">
        <el-table
                :data="traderCustomerTerminalList"
                border
                style="width: 100%">
            <el-table-column
                    prop="traderName"
                    label="客户名称"
                    >
                <template slot-scope="scope">
                    <el-link type="primary" :underline="false" @click="viewTraderDetail(scope.row)">{{scope.row.traderName}}</el-link>
                </template>
            </el-table-column>
            <el-table-column
                    prop="terminalName"
                    label="终端名称"
                   >
            </el-table-column>
            <el-table-column
                    label="建链依据"
                    width="180">
                <template slot-scope="scope">
                    <span v-if="scope.row.coidUri != null && scope.row.coidUri != ''">
                            <el-button type="text" style="padding: 0;" @click="playrecord(scope.row.coidUri)">播放</el-button>
                    </span>
                    <span v-else>
                        <span v-if="scope.row.communicateRecordId!=0">沟通记录ID：{{scope.row.communicateRecordId}}</span>
                    </span>
                </template>
            </el-table-column>
            <el-table-column
                    label="聊天截图">
                <template slot-scope="scope">
                    <template v-for="item in scope.row.pic2List">
                            <el-image
                                    style="width: 50px; height: 50px"
                                    :src="item"
                                    :preview-src-list="[item]"
                            >
                            </el-image> &nbsp;
                    </template>
                </template>
            </el-table-column>
            <el-table-column
                    prop="belongSalesUsername"
                    align="center"
                    label="归属销售"
                    width="180">
            </el-table-column>
            <el-table-column
                    label="创建时间"
                    align="center"
                    width="180">
                <template slot-scope="scope">
                    <span>{{parseTime(scope.row.addTime)}}</span>
                </template>
            </el-table-column>
            <el-table-column
                    <%--prop="name"--%>
                    align="center"
                    label="操作"
                    width="250">
                <template slot-scope="scope">
                    <el-button
                            size="mini"
                            type="primary"
                            @click="doPass(scope.row)" :loading="scope.row.subPassLoaging">审核通过</el-button>
                    <el-button
                            size="mini"
                            @click="dialogVisibleShow(scope.row)">审核不通过</el-button>
                </template>
            </el-table-column>
        </el-table>
        <br>
        <div style="text-align: center">
            <el-button
                    size="mini"
                    type="primary"
                    @click="goBack">返回</el-button>
        </div>
    </div>


    <el-dialog
            title="填写驳回原因"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose">

        <el-row>
            <el-col :span="4">
                驳回原因
            </el-col>
            <el-col :span="20">
                <el-input
                        placeholder="100字内" v-model="rejectDesc"
                        type="textarea"
                        :rows="2"
                        maxlength="100"
                        show-word-limit
                >
                </el-input>
            </el-col>
        </el-row>

        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="doReject">确 定</el-button>
        </span>
    </el-dialog>

</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const viewData={
        traderId : '${traderId}'
    }
    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {
            return {
                dialogVisible:false,
                rejectDesc:'',
                traderCustomerTerminalList:[],
                row: ''
            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            sendThis(this);
            traderTerminalAuditData({"traderId": viewData.traderId}).then(res => {
                var result = [];
                res.data.data.forEach(x=>{
                    result.push({
                        ...x,
                        subPassLoaging:false
                    })
                })
                this.traderCustomerTerminalList =result ;
            })
        },

        methods: {
            scrollTo(ref){
                let top = this.$refs[ref].$el.offsetTop;
                window.scrollTo(0, top);
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            viewTraderDetail(row) {
                openTab("客户详情", '/trader/customer/new/portrait.do?traderId=' + row.traderId);
            },
            goBack() {
                openTab("客户建链审核", '/traderChaining/list.do');
                this.closeThis()
            },
            clearOne(row) {
                let index = this.traderCustomerTerminalList.findIndex(i=>i.traderCustomerTerminalId == row.traderCustomerTerminalId);
                this.traderCustomerTerminalList.splice(index, 1);
            },
            doPass(row) {
                row.subPassLoaging = true;
                traderTerminalAuditPass({traderCustomerTerminalId:row.traderCustomerTerminalId}).then(res=>{
                    if (res.data.code != 0) {
                        this.$message.error(res.data.message);
                        row.subPassLoaging = false;
                    } else {
                        this.$message({
                            duration:500,
                            message: '审核成功',
                            type: 'success',
                        });
                        this.clearOne(row);
                    }
                })
            },
            handleClose() {
                this.dialogVisible = false;
                this.rejectDesc = '';
                this.row = '';
            },
            doReject() {

                if (this.rejectDesc == '') {
                    this.$message.error('请填写审核不通过原因');
                } else {
                    traderTerminalAuditReject({traderCustomerTerminalId:this.row.traderCustomerTerminalId,desc:this.rejectDesc}).then(res=>{
                        if (res.data.code != 0) {
                            this.$message.error(res.data.message);
                        } else {
                            this.$message({
                                message: '审核成功',
                                type: 'success',
                                duration:500,
                                onClose: () => {

                                    this.clearOne(this.row);
                                    this.handleClose();
                                }
                            });

                        }
                    });

                }

            },
            dialogVisibleShow(row) {
                this.dialogVisible = true;
                this.row = row
            }
        }
    })
</script>
