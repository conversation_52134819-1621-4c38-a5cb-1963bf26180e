package com.vedeng.firstengage.model;

import java.util.HashSet;
import java.util.List;

public class AttachmentHistoryLine {
    /**
     *添加时间
     */
    private Long addTime;
    /**
     * 修改名称
     */
    private HashSet<String> title;

    /**
     * 名称
     */
    private String titleStr;
    /**
     * 附件内容
     */
    private List<RegistrationAttachmentHistory> attachments;

    /**
     * 操作人名称
     */
    private String userName;

    public String getTitleStr() {
        return titleStr;
    }

    public void setTitleStr(String titleStr) {
        this.titleStr = titleStr;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public HashSet<String> getTitle() {
        return title;
    }

    public void setTitle(HashSet<String> title) {
        this.title = title;
    }

    public List<RegistrationAttachmentHistory> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<RegistrationAttachmentHistory> attachments) {
        this.attachments = attachments;
    }
}
