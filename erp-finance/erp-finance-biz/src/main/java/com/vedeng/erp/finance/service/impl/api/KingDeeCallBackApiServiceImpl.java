package com.vedeng.erp.finance.service.impl.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.dto.BuyOrderKingDeeDto;
import com.vedeng.erp.buyorder.dto.BuyOrderKingDeeRequestDto;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import com.vedeng.erp.buyorder.enums.BuyOrderPaymentTypeEnum;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.buyorder.service.BuyorderGoodsApiService;
import com.vedeng.erp.finance.domain.entity.InvoiceVoucherEntity;
import com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity;
import com.vedeng.erp.finance.dto.PayApplyPayBankResultDto;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.mapper.InvoiceVoucherMapper;
import com.vedeng.erp.finance.mapper.TransactionVoucherMapper;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.api.KingDeeCallbackApi;
import com.vedeng.erp.kingdee.dto.KingDeeHongRuiBuyOrderDto;
import com.vedeng.erp.kingdee.dto.KingDeeInvoiceVoucherDto;
import com.vedeng.erp.kingdee.dto.KingDeeOrderPaymentDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBankQueryDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeHongRuiBuyOrderGoodsDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeHongRuiBuyOrderQueryDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayBankResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeVoucherQueryResultDto;
import com.vedeng.erp.kingdee.enums.KingDeeVoucherEnum;
import com.vedeng.erp.settlement.domain.entity.BankBillEntity;
import com.vedeng.erp.settlement.domain.entity.CapitalBillEntity;
import com.vedeng.erp.settlement.mapper.BankBillMapper;
import com.vedeng.erp.settlement.mapper.CapitalBillMapper;
import com.vedeng.goods.dto.AfterSaleSupplyPolicyDto;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.dto.RegistrationNumberSpuDto;
import com.vedeng.goods.enums.AfterSaleInstallPolicyInstallTypeEnum;
import com.vedeng.goods.query.RegistrationNumberSpuQuery;
import com.vedeng.goods.service.AfterSaleSupplyPolicyApiService;
import com.vedeng.goods.service.CoreSkuApiService;
import com.vedeng.goods.service.RegistrationNumberApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶回调接口实现
 * @date 2022/5/20 15:15
 */
@RestController
@Slf4j
public class KingDeeCallBackApiServiceImpl implements KingDeeCallbackApi {


    @Autowired
    private InvoiceVoucherMapper invoiceVoucherMapper;

    @Autowired
    private TransactionVoucherMapper transactionVoucherMapper;

    @Autowired
    private CapitalBillMapper capitalBillMapper;

    @Autowired
    private BankBillMapper bankBillMapper;

    @Autowired
    protected KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BuyorderApiService buyorderApiService;

    @Autowired
    private BuyorderGoodsApiService buyorderGoodsApiService;

    @Autowired
    private CoreSkuApiService coreSkuApiService;

    @Autowired
    private RegistrationNumberApiService registrationNumberApiService;

    @Autowired
    private AfterSaleSupplyPolicyApiService afterSaleSupplyPolicyApiService;

    @Autowired
    private PayVedengBankApiService payVedengBankApiService;

    /**
     * {"invoiceId":"********","invoiceVoucher":"记-00001"}
     */
    @Override
    public R<?> invoiceVoucher(@RequestBody List<KingDeeInvoiceVoucherDto> kingDeeInvoiceVoucherDtoList) {
        log.info("金蝶回传凭证-发票,数据:{}", JSON.toJSONString(kingDeeInvoiceVoucherDtoList));
        if (CollUtil.isEmpty(kingDeeInvoiceVoucherDtoList)) {
            R.error("金蝶回传凭证-发票,参数不能为空");
        }
        List<InvoiceVoucherEntity> updateVoucherEntities = new ArrayList<>();
        List<InvoiceVoucherEntity> insertVoucherEntities = new ArrayList<>();
        kingDeeInvoiceVoucherDtoList.forEach(d -> {
            InvoiceVoucherEntity entity = invoiceVoucherMapper.findByInvoiceId(d.getInvoiceId());
            if (Objects.nonNull(entity)) {
                entity.setVoucherNo(d.getInvoiceVoucher());
                entity.setVoucherDate(d.getVoucherDate());
                entity.setIsDelete(ErpConstant.F);
                updateVoucherEntities.add(entity);
                log.info("金蝶回传凭证-发票,更新金蝶回传发票数据凭证号:{}", JSON.toJSONString(entity));
            } else {
                InvoiceVoucherEntity insertEntity = getInvoiceVoucherEntity(d);
                insertVoucherEntities.add(insertEntity);
                log.info("金蝶回传凭证-发票,插入金蝶回传发票数据凭证号:{}", JSON.toJSONString(insertEntity));
            }
        });
        if (CollUtil.isNotEmpty(updateVoucherEntities)) {
            log.info("金蝶回传凭证-发票,更新金蝶回传发票数据凭证号总量:{},{}",updateVoucherEntities.size(), JSON.toJSONString(updateVoucherEntities));
            invoiceVoucherMapper.updateBatchSelective(updateVoucherEntities);
        }
        if (CollUtil.isNotEmpty(insertVoucherEntities)) {
            log.info("金蝶回传凭证-发票,插入金蝶回传发票数据凭证号总量:{},{}",insertVoucherEntities.size(), JSON.toJSONString(insertVoucherEntities));
            invoiceVoucherMapper.batchInsert(insertVoucherEntities);
        }
        return R.success();
    }

    @Override
    public R<?> orderPayment(@RequestBody List<KingDeeOrderPaymentDto> kingDeeOrderPaymentDtoList) {
        log.info("金蝶回传凭证-款项,数据:{}", JSON.toJSONString(kingDeeOrderPaymentDtoList));
        if (CollUtil.isEmpty(kingDeeOrderPaymentDtoList)) {
            R.error("金蝶回传凭证-款项,参数不能为空");
        }
        List<KingDeeOrderPaymentDto> bankList = kingDeeOrderPaymentDtoList.stream().filter(dto -> KingDeeVoucherEnum.isBankByFormId(dto.getFormId())).collect(Collectors.toList());
        List<KingDeeOrderPaymentDto> balanceList = kingDeeOrderPaymentDtoList.stream().filter(dto -> KingDeeVoucherEnum.isBalanceByFormId(dto.getFormId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(balanceList)) {
            balanceVoucherHandler(balanceList);
        }
        if (CollUtil.isNotEmpty(bankList)) {
            bankVoucherHandler(bankList);
        }
        return R.success();
    }

    /**
     * 鸿瑞采购订单
     *
     * @param kingDeeHongRuiBuyOrderQueryDto
     * @return
     */
    @Override
    public R<PageInfo<KingDeeHongRuiBuyOrderDto>> pageQueryBuyOrder(@RequestBody KingDeeHongRuiBuyOrderQueryDto kingDeeHongRuiBuyOrderQueryDto) {
        PageInfo<KingDeeHongRuiBuyOrderDto> result = new PageInfo<>();

        PageParam<BuyOrderKingDeeRequestDto> pageParam = new PageParam<>();
        if (Objects.nonNull(kingDeeHongRuiBuyOrderQueryDto.getPageNum())){
            pageParam.setPageNum(kingDeeHongRuiBuyOrderQueryDto.getPageNum());
        }
        if (Objects.nonNull(kingDeeHongRuiBuyOrderQueryDto.getPageSize())){
            pageParam.setPageSize(kingDeeHongRuiBuyOrderQueryDto.getPageSize());
            if (kingDeeHongRuiBuyOrderQueryDto.getPageSize() > 50){
                pageParam.setPageSize(50);
            }
        }
        BuyOrderKingDeeRequestDto requestDto = new BuyOrderKingDeeRequestDto();
        if (StringUtils.isNotBlank(kingDeeHongRuiBuyOrderQueryDto.getBuyOrderNo())){
            requestDto.setBuyOrderNo(kingDeeHongRuiBuyOrderQueryDto.getBuyOrderNo());
        }

        if (StringUtils.isNotBlank((kingDeeHongRuiBuyOrderQueryDto.getBuyOrderTimeStart()))){
            requestDto.setBuyOrderTimeStart(DateUtil.parse(kingDeeHongRuiBuyOrderQueryDto.getBuyOrderTimeStart(), "yyyy-MM-dd HH:mm:ss").getTime());
        }
        if (StringUtils.isNotBlank((kingDeeHongRuiBuyOrderQueryDto.getBuyOrderTimeEnd()))){
            requestDto.setBuyOrderTimeEnd(DateUtil.parse(kingDeeHongRuiBuyOrderQueryDto.getBuyOrderTimeEnd(), "yyyy-MM-dd HH:mm:ss").getTime());
        }
        pageParam.setParam(requestDto);

        // 查询采购订单
        PageInfo<BuyOrderKingDeeDto> buyOrderKingDeeDtoPageInfo = buyorderApiService.pageList(pageParam);
        List<BuyOrderKingDeeDto> pageInfoList = buyOrderKingDeeDtoPageInfo.getList();
        List<KingDeeHongRuiBuyOrderDto> kingDeeHongRuiBuyOrderDtoList = new ArrayList<>();

        for (BuyOrderKingDeeDto buyOrderKingDeeDto : pageInfoList) {
            log.info("采购单信息：{}",JSON.toJSON(buyOrderKingDeeDto));
            KingDeeHongRuiBuyOrderDto kingDeeHongRuiBuyOrderDto = toConvertOrder(buyOrderKingDeeDto);
            List<BuyorderGoodsApiDto> buyorderGoodsApiDtos = buyorderGoodsApiService.queryBuyOrderGoodsList(buyOrderKingDeeDto.getBuyorderId());
            log.info("采购单商品信息：{}",JSON.toJSON(buyorderGoodsApiDtos));
            List<KingDeeHongRuiBuyOrderGoodsDto> kingDeeHongRuiBuyOrderGoodsDtoList = toConvertOrderGoods(buyorderGoodsApiDtos,buyOrderKingDeeDto);
            kingDeeHongRuiBuyOrderDto.setKingDeeHongRuiBuyOrderGoodsDtoList(kingDeeHongRuiBuyOrderGoodsDtoList);
            kingDeeHongRuiBuyOrderDtoList.add(kingDeeHongRuiBuyOrderDto);
        }

        result.setPageNum(buyOrderKingDeeDtoPageInfo.getPageNum());
        result.setPageSize(buyOrderKingDeeDtoPageInfo.getPageSize());
        result.setTotal(buyOrderKingDeeDtoPageInfo.getTotal());
        result.setList(kingDeeHongRuiBuyOrderDtoList);
        return R.success(result);
    }

    @Override
    public R<List<KingDeePayBankResultDto>> queryPayBank(@RequestBody KingDeePayBankQueryDto kingDeePayBankQueryDto) {
        List<PayApplyPayBankResultDto> payApplyPayBankResultDtos = payVedengBankApiService.queryPayBankByBuyOrderNo(kingDeePayBankQueryDto.getBuyOrderNo());
        List<KingDeePayBankResultDto> kingDeePayBankResultDtos = BeanUtil.copyToList(payApplyPayBankResultDtos, KingDeePayBankResultDto.class);

        return R.success(kingDeePayBankResultDtos);
    }

    /**
     * 装换订单数据
     * @param buyOrderKingDeeDto
     * @return
     */
    private KingDeeHongRuiBuyOrderDto toConvertOrder(BuyOrderKingDeeDto buyOrderKingDeeDto){
        KingDeeHongRuiBuyOrderDto kingDeeHongRuiBuyOrderDto = new KingDeeHongRuiBuyOrderDto();
        kingDeeHongRuiBuyOrderDto.setBuyOrderNo(buyOrderKingDeeDto.getBuyorderNo());
        kingDeeHongRuiBuyOrderDto.setBuyOrderDate(DateFormatUtils.format(buyOrderKingDeeDto.getValidTime(), "yyyy-MM-dd"));
        kingDeeHongRuiBuyOrderDto.setSupplierCode(String.valueOf(buyOrderKingDeeDto.getActualTraderSupplierId()));
        kingDeeHongRuiBuyOrderDto.setSaleUser(buyOrderKingDeeDto.getTraderContactName());
        kingDeeHongRuiBuyOrderDto.setSaleUserPhone(getSeparator(Arrays.asList(buyOrderKingDeeDto.getTraderContactTelephone(),buyOrderKingDeeDto.getTraderContactMobile())));
        kingDeeHongRuiBuyOrderDto.setPayType(BuyOrderPaymentTypeEnum.getSupplement(buyOrderKingDeeDto));
        kingDeeHongRuiBuyOrderDto.setInvoiceType(InvoiceTaxTypeEnum.getDesc(buyOrderKingDeeDto.getActualInvoiceType()));
        kingDeeHongRuiBuyOrderDto.setReceiver(buyOrderKingDeeDto.getTakeTraderContactName());
        kingDeeHongRuiBuyOrderDto.setReceiverPhone(getSeparator(Arrays.asList(buyOrderKingDeeDto.getTakeTraderContactMobile(),buyOrderKingDeeDto.getTakeTraderContactTelephone())));
        kingDeeHongRuiBuyOrderDto.setReceiverAddress(buyOrderKingDeeDto.getTakeTraderArea()+buyOrderKingDeeDto.getTakeTraderAddress());
        kingDeeHongRuiBuyOrderDto.setNeedInvoice(buyOrderKingDeeDto.getNeedInvoice());
        return kingDeeHongRuiBuyOrderDto;
    }

    private String getSeparator(List<String> list) {
        return StringUtils.join(list.stream().filter(e -> StringUtils.isNotBlank(e)).collect(Collectors.toList()), "/");
    }


    /**
     * 转换商品数据
     * @param buyorderGoodsApiDtos
     * @return
     */
    private List<KingDeeHongRuiBuyOrderGoodsDto> toConvertOrderGoods(List<BuyorderGoodsApiDto> buyorderGoodsApiDtos,BuyOrderKingDeeDto buyOrderKingDeeDto){
        List<KingDeeHongRuiBuyOrderGoodsDto> kingDeeHongRuiBuyOrderGoodsDtoList = new ArrayList<>();
        Integer buyorderInvoiceType = buyOrderKingDeeDto.getActualInvoiceType();
        Integer actualInvoiceType = buyOrderKingDeeDto.getInvoiceType();
        BigDecimal buyorderTax = InvoiceTaxTypeEnum.getTax(buyorderInvoiceType);
        BigDecimal actualTax = InvoiceTaxTypeEnum.getTax(actualInvoiceType);
        for (BuyorderGoodsApiDto buyorderGoodsApiDto : buyorderGoodsApiDtos) {
            CoreSkuDto coreSkuDtoBySkuNo = coreSkuApiService.getCoreSkuDtoBySkuNo(buyorderGoodsApiDto.getSku());
            KingDeeHongRuiBuyOrderGoodsDto kingDeeHongRuiBuyOrderGoodsDto = new KingDeeHongRuiBuyOrderGoodsDto();
            kingDeeHongRuiBuyOrderGoodsDto.setId(buyorderGoodsApiDto.getBuyorderGoodsId());
            kingDeeHongRuiBuyOrderGoodsDto.setSku(buyorderGoodsApiDto.getSku());
            kingDeeHongRuiBuyOrderGoodsDto.setSkuName(buyorderGoodsApiDto.getGoodsName());
            kingDeeHongRuiBuyOrderGoodsDto.setNum(String.valueOf(buyorderGoodsApiDto.getNum()));
            kingDeeHongRuiBuyOrderGoodsDto.setUnit(buyorderGoodsApiDto.getUnitName());
            BigDecimal actualPurchasePrice = Optional.ofNullable(buyorderGoodsApiDto).map(BuyorderGoodsApiDto::getActualPurchasePrice).orElse(BigDecimal.ZERO);
            kingDeeHongRuiBuyOrderGoodsDto.setBuyRealPrice(actualPurchasePrice.toPlainString());
            kingDeeHongRuiBuyOrderGoodsDto.setSalePrice(buyorderGoodsApiDto.getPrice().toPlainString());
            kingDeeHongRuiBuyOrderGoodsDto.setBuyRate(buyorderTax.toPlainString());
            kingDeeHongRuiBuyOrderGoodsDto.setSaleRate(actualTax.toPlainString());
            kingDeeHongRuiBuyOrderGoodsDto.setBuyTotal(actualPurchasePrice.multiply(new BigDecimal(String.valueOf(buyorderGoodsApiDto.getNum()))).toPlainString());
            kingDeeHongRuiBuyOrderGoodsDto.setSaleTotal(buyorderGoodsApiDto.getPrice().multiply(new BigDecimal(String.valueOf(buyorderGoodsApiDto.getNum()))).toPlainString());
            kingDeeHongRuiBuyOrderGoodsDto.setBrand(buyorderGoodsApiDto.getBrandName());
            kingDeeHongRuiBuyOrderGoodsDto.setModel(this.getModelSpec(coreSkuDtoBySkuNo.getSpuId(),buyorderGoodsApiDto.getModel(),buyorderGoodsApiDto.getSpec()));
            this.setGoodsRegistrationNumber(kingDeeHongRuiBuyOrderGoodsDto,coreSkuDtoBySkuNo.getSpuId());
            kingDeeHongRuiBuyOrderGoodsDto.setDeliveryDay(buyorderGoodsApiDto.getDeliveryCycle());
            this.setInstallPolicyAndWarrantyPeriod(kingDeeHongRuiBuyOrderGoodsDto,buyOrderKingDeeDto);
            kingDeeHongRuiBuyOrderGoodsDto.setRemark(buyorderGoodsApiDto.getInsideComments());
            kingDeeHongRuiBuyOrderGoodsDtoList.add(kingDeeHongRuiBuyOrderGoodsDto);
        }
        return kingDeeHongRuiBuyOrderGoodsDtoList;
    }

    private void setInstallPolicyAndWarrantyPeriod(KingDeeHongRuiBuyOrderGoodsDto kingDeeHongRuiBuyOrderGoodsDto,BuyOrderKingDeeDto buyOrderKingDeeDto) {
        if (Objects.isNull(buyOrderKingDeeDto.getTraderId())){
            kingDeeHongRuiBuyOrderGoodsDto.setInstallPolicy("/");
            kingDeeHongRuiBuyOrderGoodsDto.setWarrantyPeriod("/");
            return;
        }
        AfterSaleSupplyPolicyDto afterSaleSupplyPolicyDto = afterSaleSupplyPolicyApiService.queryBySkuNoAndTraderId(buyOrderKingDeeDto.getTraderId(),kingDeeHongRuiBuyOrderGoodsDto.getSku());
        if (Objects.isNull(afterSaleSupplyPolicyDto)){
            kingDeeHongRuiBuyOrderGoodsDto.setInstallPolicy("/");
            kingDeeHongRuiBuyOrderGoodsDto.setWarrantyPeriod("/");
            return;
        }
        // 安装政策
        setInstallPolicy(kingDeeHongRuiBuyOrderGoodsDto, afterSaleSupplyPolicyDto);

        //处理保修期
        kingDeeHongRuiBuyOrderGoodsDto.setWarrantyPeriod("/");
        if (StringUtils.isNotEmpty(afterSaleSupplyPolicyDto.getGuaranteePolicyHostGuaranteePeriod())) {
            kingDeeHongRuiBuyOrderGoodsDto.setWarrantyPeriod(afterSaleSupplyPolicyDto.getGuaranteePolicyHostGuaranteePeriod());
        }

    }

    private void setInstallPolicy(KingDeeHongRuiBuyOrderGoodsDto kingDeeHongRuiBuyOrderGoodsDto, AfterSaleSupplyPolicyDto afterSaleSupplyPolicyDto) {
        kingDeeHongRuiBuyOrderGoodsDto.setInstallPolicy("/");
        if(Objects.nonNull(afterSaleSupplyPolicyDto.getInstallPolicyInstallType())){
            if (AfterSaleInstallPolicyInstallTypeEnum.CHARGE.getType().equals(afterSaleSupplyPolicyDto.getInstallPolicyInstallType()) ||
                    AfterSaleInstallPolicyInstallTypeEnum.FREE.getType().equals(afterSaleSupplyPolicyDto.getInstallPolicyInstallType())) {
                kingDeeHongRuiBuyOrderGoodsDto.setInstallPolicy(AfterSaleInstallPolicyInstallTypeEnum.getNameByValue(afterSaleSupplyPolicyDto.getInstallPolicyInstallType()));
                return;
            }
        }

        if (afterSaleSupplyPolicyDto.getTechnicalDirectSupplyMaintain() != null &&
                afterSaleSupplyPolicyDto.getTechnicalDirectSupplyMaintain() == 1) {
            kingDeeHongRuiBuyOrderGoodsDto.setInstallPolicy("提供远程指导");
        }
    }


    private String getModelSpec(Integer spuType,String model,String spec){
        if (Objects.equals(spuType,316) || Objects.equals(spuType,1008)){
            return model;
        }
        if (Objects.equals(spuType,317) || Objects.equals(spuType,318)){
            return spec;
        }
        if (Objects.nonNull(model) && !Objects.equals(model,"")){
            return model;
        }
        return spec;
    }

    private void setGoodsRegistrationNumber(KingDeeHongRuiBuyOrderGoodsDto kingDeeHongRuiBuyOrderGoodsDto,Integer spuId){
        RegistrationNumberSpuQuery build = RegistrationNumberSpuQuery.builder().spuId(spuId).build();
        RegistrationNumberSpuDto registrationNumberSpuDto = registrationNumberApiService.queryRegistraAndManufactureBySpuId(build);
        if (Objects.isNull(registrationNumberSpuDto)){
            log.error("根据SPU补全产品信息时未查询到基础信息spuId:{}",spuId);
            kingDeeHongRuiBuyOrderGoodsDto.setRegistrationNumber("-");
            kingDeeHongRuiBuyOrderGoodsDto.setManufacturer("-");
            return;
        }
        kingDeeHongRuiBuyOrderGoodsDto.setRegistrationNumber(registrationNumberSpuDto.getRegistrationNumber());
        kingDeeHongRuiBuyOrderGoodsDto.setManufacturer(registrationNumberSpuDto.getProductChineseName());
    }

    private void balanceVoucherHandler(List<KingDeeOrderPaymentDto> balanceList) {
        log.info("金蝶回传凭证-款项,余额支付数据:{}", JSON.toJSONString(balanceList));
        List<TransactionVoucherEntity> updateList = new ArrayList<>();
        List<TransactionVoucherEntity> insertList = new ArrayList<>();
        balanceList.forEach(dto -> {
            KingDeeVoucherEnum kingDeeVoucherEnum = KingDeeVoucherEnum.getByFormId(dto.getFormId());
            TransactionVoucherEntity entity = transactionVoucherMapper.findByBillNo(dto.getBillNo());
            CapitalBillEntity capitalBillEntity = capitalBillMapper.findByCapitalBillNo(dto.getBillNo());
            Integer relateId = Objects.nonNull(capitalBillEntity) ? capitalBillEntity.getCapitalBillId() : 0;
            if (Objects.nonNull(entity)) {
                buildTransactionVoucherUpdateData(dto, kingDeeVoucherEnum, entity, relateId, updateList);
            } else {
                buildTransactionVoucherInsertData(dto, kingDeeVoucherEnum, relateId, insertList);
            }
        });
        if (CollUtil.isNotEmpty(updateList)) {
            transactionVoucherMapper.updateBatchSelective(updateList);
        }
        if (CollUtil.isNotEmpty(insertList)) {
            transactionVoucherMapper.batchInsert(insertList);
        }
    }

    private void bankVoucherHandler(List<KingDeeOrderPaymentDto> bankList) {
        log.info("金蝶回传凭证-款项,银行支付数据:{}", JSON.toJSONString(bankList));
        List<TransactionVoucherEntity> updateList = new ArrayList<>();
        List<TransactionVoucherEntity> insertList = new ArrayList<>();
        bankList.forEach(dto -> {
            KingDeeVoucherEnum kingDeeVoucherEnum = KingDeeVoucherEnum.getByFormId(dto.getFormId());
            TransactionVoucherEntity entity = transactionVoucherMapper.findByBillNo(dto.getBillNo());
            Integer bankBillId = getBankBillId(dto, kingDeeVoucherEnum);
            Integer relateId = Objects.nonNull(bankBillId) && !ErpConstant.ZERO.equals(bankBillId) ? bankBillId : 0;
            if (Objects.nonNull(entity)) {
                buildTransactionVoucherUpdateData(dto, kingDeeVoucherEnum, entity, relateId, updateList);
            } else {
                buildTransactionVoucherInsertData(dto, kingDeeVoucherEnum, relateId, insertList);
            }
        });
        if (CollUtil.isNotEmpty(updateList)) {
            transactionVoucherMapper.updateBatchSelective(updateList);
        }
        if (CollUtil.isNotEmpty(insertList)) {
            transactionVoucherMapper.batchInsert(insertList);
        }
    }

    private Integer getBankBillId(KingDeeOrderPaymentDto dto, KingDeeVoucherEnum kingDeeVoucherEnum) {
        log.info("金蝶回传凭证-款项,银行支付数据,获取银行流水id:{}", JSON.toJSONString(dto));
        Integer bankBillId = null;
        String tranFlow = getTranFlow(dto, kingDeeVoucherEnum);
        if (StrUtil.isBlank(tranFlow)) {
            log.info("金蝶回传凭证-款项,银行支付数据,流水号为空,不做处理:{}", JSON.toJSONString(dto));
            return null;
        }
        BankBillEntity bankBill = bankBillMapper.findByTranFlow(tranFlow);
        if (Objects.nonNull(bankBill)) {
            bankBillId = bankBill.getBankBillId();
        }
        log.info("金蝶回传凭证-款项,银行支付数据,流水号:{},银行流水id:{}", tranFlow, bankBillId);
        return bankBillId;
    }

    private String getTranFlow(KingDeeOrderPaymentDto dto, KingDeeVoucherEnum kingDeeVoucherEnum) {
        log.info("金蝶回传凭证-款项,银行支付数据,获取银行流水号:{}", JSON.toJSONString(dto));
        String tranFlow = "";
        switch (kingDeeVoucherEnum) {
            case AP_PAYBILL:
                tranFlow = transactionVoucherMapper.getTranFlowByKingDeePayBill(dto.getBillNo());
                break;
            case AR_RECEIVEBILL:
                tranFlow = transactionVoucherMapper.getTranFlowByKingDeeReceiveBill(dto.getBillNo());
                break;
            case AP_REFUNDBILL:
                tranFlow = transactionVoucherMapper.getTranFlowByKingDeePayRefundBill(dto.getBillNo());
                break;
            case AR_REFUNDBILL:
                tranFlow = transactionVoucherMapper.getTranFlowByKingDeeReceiveRefundBill(dto.getBillNo());
                break;
            default:
                tranFlow = "";
        }
        if (StrUtil.isBlank(tranFlow)) {
            log.info("金蝶回传凭证-款项,银行支付数据,本地回写表未查询到数据,调用金蝶接口查询流水号:{}", JSON.toJSONString(dto));
            KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
            queryParam.setFormId(kingDeeVoucherEnum.getFormId());
            List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
            queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").value(dto.getBillNo()).build());
            queryParam.setFilterString(queryFilterDtos);
            List<KingDeeVoucherQueryResultDto> queryResult = kingDeeBaseApi.query(queryParam, KingDeeVoucherQueryResultDto.class);
            if (CollUtil.isNotEmpty(queryResult)) {
                tranFlow = queryResult.get(0).getF_QZOK_LSH();
            }
        }
        log.info("金蝶回传凭证-款项,银行支付数据,流水号:{}", tranFlow);
        return tranFlow;
    }

    private static void buildTransactionVoucherInsertData(KingDeeOrderPaymentDto dto, KingDeeVoucherEnum kingDeeVoucherEnum, Integer relateId, List<TransactionVoucherEntity> insertList) {
        TransactionVoucherEntity insertEntity = new TransactionVoucherEntity();
        insertEntity.setBillNo(dto.getBillNo());
        insertEntity.setVoucherNo(dto.getVoucherNo());
        insertEntity.setTransactionType(kingDeeVoucherEnum.getTransactionType());
        insertEntity.setIsDelete(ErpConstant.F);
        insertEntity.setVoucherDate(dto.getVoucherDate());
        insertEntity.setRelateId(relateId);
        insertEntity.setSource(kingDeeVoucherEnum.getSource());
        insertEntity.setVoucherUrl("");
        insertList.add(insertEntity);
        log.info("金蝶回传凭证-款项,插入金蝶回传余额支付数据凭证号:{}", JSON.toJSONString(insertEntity));
    }

    private void buildTransactionVoucherUpdateData(KingDeeOrderPaymentDto dto, KingDeeVoucherEnum kingDeeVoucherEnum, TransactionVoucherEntity entity, Integer relateId, List<TransactionVoucherEntity> updateList) {
        entity.setVoucherNo(dto.getVoucherNo());
        entity.setTransactionType(kingDeeVoucherEnum.getTransactionType());
        entity.setIsDelete(ErpConstant.F);
        entity.setVoucherDate(dto.getVoucherDate());
        entity.setRelateId(relateId);
        entity.setSource(kingDeeVoucherEnum.getSource());
        updateList.add(entity);
        log.info("金蝶回传凭证-款项,更新金蝶回传银行支付数据凭证号:{}", JSON.toJSONString(entity));
    }

    private static InvoiceVoucherEntity getInvoiceVoucherEntity(KingDeeInvoiceVoucherDto d) {
        InvoiceVoucherEntity invoiceVoucherEntity = new InvoiceVoucherEntity();
        invoiceVoucherEntity.setInvoiceId(d.getInvoiceId());
        invoiceVoucherEntity.setVoucherNo(d.getInvoiceVoucher());
        invoiceVoucherEntity.setIsDelete(ErpConstant.F);
        invoiceVoucherEntity.setVoucherDate(d.getVoucherDate());
        invoiceVoucherEntity.setVoucherUrl("");
        invoiceVoucherEntity.setAddTime(new Date());
        invoiceVoucherEntity.setModTime(new Date());
        invoiceVoucherEntity.setCreator(KingDeeConstant.ONE);
        invoiceVoucherEntity.setUpdater(KingDeeConstant.ONE);
        invoiceVoucherEntity.setCreatorName(KingDeeConstant.ADMIN);
        invoiceVoucherEntity.setUpdaterName(KingDeeConstant.ADMIN);
        return invoiceVoucherEntity;
    }
}
