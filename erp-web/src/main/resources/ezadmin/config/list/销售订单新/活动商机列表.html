<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科研活动商机</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="kyg_sj" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="1" append_column_url="" append_row_url="" empty_show="" firstcol="checkbox">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">商机编号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="ORDER_NO" placeholder style alias="" jdbctype data datatype oper="LIKE" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">商机状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xmselect" name="ORDER_STATUS" placeholder style="" alias="IF(A.ORDER_STATUS=2 AND B.PAYMENT_STATUS>0,3,IF(A.ORDER_STATUS=3,2, A.ORDER_STATUS))" jdbctype="" data="[{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已建单&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已成单&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;4&quot;}]" datatype="JSON" oper="in" validate_rules="" validate_messages=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">归属销售</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="BELONG_SALES_ID" placeholder style="" alias="" jdbctype="" data="select A.USER_ID K,A.USERNAME V from T_USER A LEFT JOIN T_R_USER_POSIT TRUP on A.USER_ID = TRUP.USER_ID
LEFT JOIN T_POSITION C ON C.POSITION_ID=TRUP.POSITION_ID
LEFT JOIN T_ORGANIZATION D ON D.ORG_ID=C.ORG_ID
WHERE D.ORG_NAME LIKE '科研购事业部%' AND A.IS_DISABLED=0" datatype="KVSQL" oper="in" validate_rules="" validate_messages=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">客户名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="COMPANY_NAME" placeholder style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">联系人姓名</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="CONTACT_PERSON" placeholder style alias="" jdbctype data datatype oper="LIKE" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">联系人电话</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="CONTACT_PHONE" placeholder style alias="" jdbctype data datatype oper="LIKE" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">分配状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xmselect" name="BELONG_SALES_FLAG" placeholder style="" alias="if(A.BELONG_SALES_ID IS NULL OR A.BELONG_SALES_ID ='',        0,        1)" jdbctype="" data="[{&quot;V&quot;:&quot;已分配&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;未分配&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper="in" validate_rules="" validate_messages=""></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">商品订货号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SKU" placeholder style alias="" jdbctype data datatype oper="LIKE" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">商品名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SKU_NAME" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button item_name="全部关闭" name="全部关闭" type="table"> 全部关闭 </button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="ORDER_NO" name="ORDER_NO" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商机编号</th>
            <th item_name="COMPANY_NAME" name="COMPANY_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">公司名称</th>
            <th item_name="SKU" name="SKU" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商品订货号</th>
            <th item_name="SKU_NAME" name="SKU_NAME" body="td-link" order="" url="/goods/vgoods/viewSku.do?skuId=${GOODS_ID}&pageType=1&from=kyg_sj" opentype="PARENT"   head="th" >商品名称</th>
            <th item_name="CONTACT_PERSON" name="CONTACT_PERSON" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">联系人姓名</th>
            <th item_name="CONTACT_PHONE" name="CONTACT_PHONE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">联系人电话</th>
            <th item_name="ORDER_STATUS" name="ORDER_STATUS" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已建单&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已成单&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;4&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商机状态</th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="DATETIME" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商机创建时间</th>
            <th item_name="BELONG_SALES_ID" name="BELONG_SALES_ID" body="td-select" order="" url="" opentype="" windowname="" datatype="KVSQLCACHE" data="select A.USER_ID K,A.USERNAME V from T_USER A LEFT JOIN T_R_USER_POSIT TRUP on A.USER_ID = TRUP.USER_ID
LEFT JOIN T_POSITION C ON C.POSITION_ID=TRUP.POSITION_ID
LEFT JOIN T_ORGANIZATION D ON D.ORG_ID=C.ORG_ID
WHERE D.ORG_NAME LIKE '科研购事业部%' AND A.IS_DISABLED=0" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属销售</th>
            <th type="rowbutton" id="rowbutton">
                <button  style="min-width:175px" item_name="关闭" name="关闭" url="/orderstream/activityPreSaleorder/close.do?ORDER_STATUS=${ORDER_STATUS}&amp;_CHECKD_IDS=${ID}" opentype="MODEL" windowname="关闭"   pre="" ez_session_user_name_key="njadmin" encrypt_list_id="kyg_sj" item_label="关闭" open_type="MODEL"  window_name="关闭" area=""   type="single">关闭</button>
                <button item_name="转订单" name="转订单" url="/orderstream/activityPreSaleorder/toOrder.do?ORDER_STATUS=${ORDER_STATUS}&amp;activityPreOrderId=${ID}" opentype="PARENT" windowname="" style="min-width:175px"  window_name="" area=""  ez_session_user_id_key="2" id="" type="single">转订单</button></th>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"> <pre id="express" class="layui-code" orderby=" order by ACTIVITY_PRE_ORDER_ID desc" groupby="">StringBuilder sql=new StringBuilder();
 sql.append("
   SELECT
        A.ACTIVITY_PRE_ORDER_ID ID,
        A.ORDER_NO,
        A.COMPANY_NAME,
        A.SKU,
        A.GOODS_ID,
        C.SKU_NAME ,
        A.CONTACT_PERSON,
        A.CONTACT_PHONE,B.PAYMENT_STATUS,
        IF(A.ORDER_STATUS=2 AND B.PAYMENT_STATUS>0,3,IF(A.ORDER_STATUS=3,2, A.ORDER_STATUS))
         ORDER_STATUS
        ,
        if(A.BELONG_SALES_ID IS NULL OR A.BELONG_SALES_ID ='',
        0,
        1) BELONG_SALES_FLAG,
        A.BELONG_SALES,
        A.QUOTA,
        A.TAG,
        A.SALEORDER_ID,
        A.BELONG_SALES_ID,
        A.ADD_TIME
    FROM
        T_ACTIVITY_PRE_ORDER  A LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID=B.SALEORDER_ID
    left join V_CORE_SKU C ON A.GOODS_ID=C.SKU_ID
    WHERE
        1 = 1 ");


sql.append ( " and (1=2  " );



if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " or  BELONG_SALES_ID in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}

if ( isBlank("EZ_SESSION_MY_USER_KEY","session") &amp;&amp;  isBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
sql.append ( " or 1=1" );
}
sql.append (  ") " );


 return search(sql);</pre> </td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"> <pre id="count" class="layui-code"></pre> </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>
        $(function(){
            $("input[name='row_data_hidden_ORDER_STATUS']").each(function(){
                //商机状态为进行中的才显示 关闭+分配
                if($(this).val()!=1){
                    $(this).parent().find(".list-body-checkbox").attr("disabled","disabled").addClass("layui-disabled");
                    //进行中 允许转订单
                    var button= $(this).parents("tr").find("button[name='转订单']");
                    button.removeClass("ezopenbutton");
                    button.addClass(" layui-btn-disabled");

                    var button= $(this).parents("tr").find("button[name='关闭']");
                    button.removeClass("ezopenbutton");
                    button.addClass(" layui-btn-disabled");
                }else{
                    var button= $(this).parents("tr").find("button[name='关闭']");
                    button.removeClass("ezopenbutton");
                    button.click(function(){
                        layer.confirm('关闭后，该用户抢购的商品库存占用随即释放给其他用户。确认关闭？', {icon: 3, title:'提示'}, function(index){
                            debugger;
                            $.get(button.attr("item_url"),function(data){
                                if(data.code==0){
                                    location.reload();
                                }else{
                                    layer.alert("关闭失败");
                                    location.reload();
                                }
                            })
                            layer.close(index);
                        });
                    })
                }
                // //商机状态 进行中、已建单状态的才允许关闭
                // if($(this).val()!=1&&$(this).val()!=2){
                //
                // }
            })


            $("button[name='全部关闭']").click(function(){
                if(getCheckIdsUrl()==""){
                    layer.alert("请选择需要关闭的商机");
                    return false;
                }
                $(this).removeClass("ezopenbutton");
                layer.confirm('关闭后，该用户抢购的商品库存占用随即释放给其他用户。确认关闭？', {icon: 3, title:'提示'}, function(index){
                    $.get("/orderstream/activityPreSaleorder/close.do?1=1&"+getCheckIdsUrl(),function(data){
                        if(data.code==0){
                            location.reload();
                        }else{
                            layer.alert("关闭失败");
                            location.reload();
                        }
                    })
                    layer.close(index);
                });
                return false;
            })
            $("button[name='批量分配']").click(function(){
                var cc=getJsonCheckIdAndNames("BELONG_SALES_ID");
                if(cc=="[]"){
                    layer.alert("请选择需要分配的商机");
                    return false;
                }
                var jj=JSON.parse(cc);
                var emp=false;
                for(var i = 0; i < jj.length; i++){
                    if(jj[i]["BELONG_SALES_ID"]!=""){
                        emp=true;
                        break;
                    }
                }
                if(emp){
                    layer.confirm('所选项包含已经分配的商机，请核查！确认无误点继续分配?', {icon: 3, title:'提示'}, function(index){
                        // $.get("/ezadmin/form/form-kyg_sj?"+getCheckIdsUrl(),function(data){
                        //     if(data.code==0){
                        //         location.reload();
                        //     }
                        // })
                        openModel("/ezadmin/form/form-kyg_sj?"+getCheckIdsUrl(),"分配商机");
                    });
                }else{
                    openModel("/ezadmin/form/form-kyg_sj?"+getCheckIdsUrl(),"分配商机");
                }
                return false;
            })
        })

    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>