package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.Data;

import java.util.List;

@Data
public class B2eNbsQryDraftTransStatusResponse  extends BankResponse {

    /**
     * 响应体
     */
    private B2eNbsQryDraftTransStatusResponse.B2eNbsQryDraftTransStatusResBody xDataBody;

    @Data
    public static class B2eNbsQryDraftTransStatusResBody{
        /** 银行渠道交易流水号 */
        private String svrId;

        /** 客户技术请求流水号，同一客户请勿重复 */
        private String trnId;

        /** 总条数 */
        private int total;

        /** 票据信息列表 */
        private List<BillInfo> list;
    }


    @Data
    public static class BillInfo {
        /** 票据（包）号码 */
        private String billNo;

        /** 票据类型 */
        private String billType;

        /** 是否可分包 */
        private String isAllowSplitBill;

        /** 出票日 */
        private String remitDt;

        /** 票面到期日 */
        private String dueDt;

        /** 承兑日期 */
        private String acptDt;

        /** 票据（包）金额 */
        private String billMoney;

        /** 出票人全称 */
        private String drwrName;

        /** 出票人账号 */
        private String drwrAcctNo;

        /** 出票人账号名称 */
        private String drwrAcctName;

        /** 出票人开户行名称 */
        private String drwrBankName;

        /** 出票人开户行行号 */
        private String drwrBankNo;

        /** 收款人名称 */
        private String pyeeName;

        /** 收款人账号 */
        private String pyeeAcctNo;

        /** 收款人账号名称 */
        private String pyeeAcctName;

        /** 收款人开户行名称 */
        private String pyeeBankName;

        /** 收款人开户行行号 */
        private String pyeeBankNo;

        /** 承兑人全称 */
        private String acptName;

        /** 承兑人账号 */
        private String acptAcctNo;

        /** 承兑人账号名称 */
        private String acptAcctName;

        /** 承兑人开户行名称 */
        private String acptBankName;

        /** 承兑人开户行行号 */
        private String acptBankNo;

        /** 子票区间起始 */
        private String billRangeStart;

        /** 子票区间截止 */
        private String billRangeEnd;

        /** 禁止背书标记 */
        private String hldrBanEndrsmtMark;

        /** 票据状态 */
        private String billStatus;

        /** 处理结果 */
        private String statusCode;

        /** 业务交易状态 */
        private String transBusiStatus;

        /** 业务交易状态码 */
        private String transBusiStatusCode;

        /** 申请人名称 */
        private String transFromName;

        /** 申请人账号 */
        private String transFromAcctNo;

        /** 申请人行号 */
        private String transFromBankNo;

        /** 申请日期 */
        private String transApplDt;

        /** 申请人备注 */
        private String transFromRemark;

        /** 接收人名称 */
        private String transtoName;

        /** 接收人账号 */
        private String transtoAcctNo;

        /** 接收人行号 */
        private String transtoBankNo;

        /** 接收人备注 */
        private String transToRemark;

        /** 线上清算标识 */
        private String onlineMark;

        /** 业务类型 */
        private String busiStage;

        /** 处理结果说明 */
        private String errorMsg;

        /** 交易流水号 */
        private String transId;
    }
}
