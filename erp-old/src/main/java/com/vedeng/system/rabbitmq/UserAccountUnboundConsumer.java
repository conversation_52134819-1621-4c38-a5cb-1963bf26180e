package com.vedeng.system.rabbitmq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.aftersales.model.dto.TraderAssociatedLogDto;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.enums.TraderAssociatedLogEnum;
import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.service.TraderAssociatedLogService;
import com.vedeng.trader.service.TraderContactService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 17:26
 * @describe 用户解绑
 */
@Component("userAccountUnboundConsumer")
public class UserAccountUnboundConsumer extends AbstractMessageListener {
    public static final Logger LOGGER = LoggerFactory.getLogger(UserAccountUnboundConsumer.class);

    @Autowired
    @Qualifier("webAccountMapper")
    private WebAccountMapper webAccountMapper;

    @Resource
    private TraderContactService traderContactService;

    @Resource
    private TraderAssociatedLogService traderAssociatedLogService;
    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {

        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("解绑用户同步的消息，消息体：{}",messageBody);
        String mobile = JSONObject.parseObject(messageBody,String.class);
        WebAccount webAccountQuery = webAccountMapper.getWenAccountInfoByMobile(mobile);
        if(webAccountQuery==null){
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }else{
            //若客户信息-联系人中创建过该注册用户的信息，则将该注册用户置为禁用状态
            TraderContactGenerate contactToUpdate = null;
            if (webAccountQuery.getTraderContactId()!=null && webAccountQuery.getTraderContactId()>0) {
                contactToUpdate = traderContactService.selectByPrimaryKey(webAccountQuery.getTraderContactId());
            }
            if (contactToUpdate == null) {
                contactToUpdate = traderContactService.getByTraderIdAndMobileNo(webAccountQuery.getTraderId(), webAccountQuery.getMobile());
            }

            if(contactToUpdate!=null && !Objects.equals(contactToUpdate.getIsEnable(), CommonConstants.DISABLE)){
                //if found one, then to update.
//            contactToUpdate.setTraderId(traderUnboundDto.getTraderId());
                contactToUpdate.setTraderId(webAccountQuery.getTraderId());
                contactToUpdate.setIsEnable(CommonConstants.DISABLE);
                contactToUpdate.setUpdater(2); //njadmin
                contactToUpdate.setModTime(System.currentTimeMillis());
                traderContactService.updateByPrimaryKeySelective(contactToUpdate);
            }


            webAccountQuery.setTraderId(ErpConst.ZERO);
            webAccountQuery.setTraderContactId(ErpConst.ZERO);
            webAccountQuery.setTraderAddressId(ErpConst.ZERO);
            //解除注册用户关联客户信息，并贝登会员置为"非会员"
            webAccountQuery.setIsVedengMember(CommonConstants.STATUS_0);
            webAccountQuery.setModTime(new Date());
            webAccountMapper.updateisVedengJoin(webAccountQuery);

            //清空贝登会员开通时间
            webAccountMapper.updateClearVedengMemberTime(webAccountQuery);

            //记录注册用户解除客户信息日志
            TraderAssociatedLogDto traderAssociatedLogDto = new TraderAssociatedLogDto();
            traderAssociatedLogDto.setErpAccountId(webAccountQuery.getErpAccountId());
            traderAssociatedLogDto.setWebAccountToBelongPlatformNo(webAccountQuery.getBelongPlatform());
            traderAssociatedLogDto.setTraderId(webAccountQuery.getTraderId());
            traderAssociatedLogDto.setOperationType(TraderAssociatedLogEnum.UNBIND.getType());
            traderAssociatedLogDto.setOperatorId(2);//njadmin
            StringBuilder operatorNameBuilder = new StringBuilder("njadmin");

            traderAssociatedLogDto.setOperatorName(operatorNameBuilder.toString());
            traderAssociatedLogDto.setReason("集采账号禁用");//解除原因
            traderAssociatedLogService.recordTraderAssociatedLog(traderAssociatedLogDto);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }


    }

}
