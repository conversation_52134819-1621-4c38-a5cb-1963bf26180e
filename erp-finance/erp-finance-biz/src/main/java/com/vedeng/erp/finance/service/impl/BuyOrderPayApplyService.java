package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.EventBus;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorder.dto.BuyOrderInfoDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.domain.entity.AutoPayConfig;
import com.vedeng.erp.finance.dto.PayApplyCheckDto;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.mapper.AutoPayConfigMapper;
import com.vedeng.erp.finance.service.AbstractPayApply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 采购单付款申请
 */
@Slf4j
@Service
public class BuyOrderPayApplyService extends AbstractPayApply {

    @Autowired
    private BuyorderApiService buyorderApiService;

    @Autowired
    private AutoPayConfigMapper autoPayConfigMapper;

    @Value("${mairui_trade_id}")
    private String maiRuiTradeId;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Override
    public void createBillBaseCheckValid(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        BuyOrderInfoDto dto = buyorderApiService.getBuyOrderInfo(payApplyCreateBillDto.getRelatedId());
        if (Objects.isNull(dto)){
            log.info("未找到对应的采购单,id：{}", payApplyCreateBillDto.getRelatedId());
            throw new ServiceException("未找到对应的采购单");
        }
        //1. 单据进行中：归属业务单据的单据状态 = 进行中
        if (dto.getStatus() != 1 && dto.getStatus() != 2){
            throw new ServiceException("归属业务单据的单据状态不是进行中、已完结，当前状态：" +dto.getStatus());
        }
        //2. 单据已生效：归属业务单据的生效状态 = 已生效
        if (dto.getValidStatus() != 1){
            throw new ServiceException("归属业务单据的生效状态不是已生效，当前状态：" +dto.getValidStatus());
        }
        //3. 单据未完成付款：归属业务单据的付款状态 ！= 全部付款
//        if (dto.getPaymentStatus() == 2){
//            throw new ServiceException("归属业务单据的付款状态是全部付款" );
//        }
        log.info("createBillBaseCheckValid校验完成");
    }

    /**
     * 业务检查
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    @Override
    public void createBillBusinessCheck(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        super.businessCheck(payApplyCreateBillDto);
        BuyOrderInfoDto dto = buyorderApiService.getBuyOrderInfo(payApplyCreateBillDto.getRelatedId());
        payApplyCreateBillDto.setTraderId(dto.getTraderId());
        Integer orderType = dto.getOrderType();
        if (Objects.equals(orderType,1)){
            throw new ServiceException("备货采购订单不支持自动付款");
        }
        if (Objects.equals(orderType,0) && StringUtils.isNotBlank(payApplyCreateBillDto.getBankRemark())){
            // 如果是销售订单，判断银行回单备注
            throw new ServiceException("通过销售单创建的采购单，银行回单备注有值，不支持自动付款");
        }
        log.info("createBillBusinessCheck校验完成");
    }

    @Override
    public void otherProcess(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
    }

    /**
     * 判断时间是否在周一至周五 9:30 - 16:40
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    @Override
    public Integer choseBank(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        log.info("选择银行：{}", JSON.toJSON(payApplyCreateBillDto));
        boolean withinWorkingHours = payRuleCheck();
        List<String> maiRuilist = Arrays.asList(maiRuiTradeId.split(","));
        boolean contains = maiRuilist.contains(String.valueOf(payApplyCreateBillDto.getTraderId()));
        // 民生
        Integer payVedengBankId = ErpConstant.SEVEN;
        if (withinWorkingHours && contains){
            // 周一到周五，采购付迈瑞款：付款银行 = 交通银行
            payVedengBankId = ErpConstant.THREE;
        }
        log.info("选择银行{},withinWorkingHours:{},contains:{}",payVedengBankId,withinWorkingHours,contains);

        return payVedengBankId;
    }

    /**
     * 采购单自动付款规则检查
     * @param payApplyCheckDto
     * @throws ServiceException
     */
    @Override
    public void payApplyRuleCheck(PayApplyCheckDto payApplyCheckDto) throws ServiceException {
        log.info("采购单自动付款规则检查:{}",JSON.toJSON(payApplyCheckDto));
        // 供应商黑名单
        // 613042 浙江贝登鸿瑞供应链管理有限公司，请将该供应商设置为不可发起付款申请
//        if (Objects.equals(613042,payApplyCheckDto.getTraderId())){
//            throw new ServiceException("浙江贝登鸿瑞供应链管理有限公司不可发起付款申请");
//        }
        // 工作日
        boolean withinWorkingHours = payApplyRuleCheck();
        AutoPayConfig autoPayConfig = autoPayConfigMapper.selectByPrimaryKey(1);
        log.info("当前配置：{}",JSON.toJSON(autoPayConfig));
        // 启用采购合同审核状态
        Integer enableContractAudit = autoPayConfig.getEnableContractAudit();
        // 启用供应商白名单
        Integer enableSupplierWhitelist = autoPayConfig.getEnableSupplierWhitelist();
        // 供应商白名单
        String supplierWhitelist = autoPayConfig.getSupplierWhitelist();
        List<String> supplierWhitelistList = Arrays.asList(supplierWhitelist.split(","));
        Boolean isContain = supplierWhitelistList.contains(String.valueOf(payApplyCheckDto.getTraderId()));
        // 限额
        BigDecimal payLimit = new BigDecimal(autoPayConfig.getPayLimit()).multiply(new BigDecimal("10000")) ;
        if (Objects.equals(enableContractAudit,1)){
            // 采购合同审核要求参数：是
            if (Objects.equals(enableSupplierWhitelist,1)){
                // 启用供应商白名单：是
                if (!isContain){
                    // 供应商是否在供应商白名单中：否
                    if (!payApplyCheckDto.getIsAudit()){
                        throw new ServiceException("采购合同未审核，不允许提交申请");
                    }
                }
            }
            if (Objects.equals(enableSupplierWhitelist,0)){
                // 启用供应商白名单：否
                if (!payApplyCheckDto.getIsAudit()){
                    // 采购合同是否已审核 ：否
                    throw new ServiceException("采购合同未审核，不允许提交申请");
                }
            }
        }

        if (!withinWorkingHours && payApplyCheckDto.getAmount().compareTo(payLimit) > 0){
            // 非工作日，申请金额超额
            throw new ServiceException(String.format(
                    "周一至周五%s时间外，申请付款金额不可大于%s万元",
                    autoPayConfig.getPayApplyTime().replace(",","到"),
                    autoPayConfig.getPayLimit()
            ));
        }
    }
}
