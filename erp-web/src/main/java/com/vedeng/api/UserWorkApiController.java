package com.vedeng.api;

import com.alibaba.fastjson.JSON;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.dto.UserWorkDetail;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.service.CommunicateRecordService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.uac.api.dto.OrganizationResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping("/api/userwork")
public class UserWorkApiController extends BaseController {

    @Autowired
    private UserWorkApiService userWorkApiService;


    @NoNeedAccessAuthorization
    @RequestMapping(value = "/handUserWorkList")
    @ResponseBody
    public ResultInfo<?> handUserWorkList(@RequestBody List<UserWorkDetail> userWorkDetailList) {
        return ResultInfo.success(userWorkApiService.handUserWorkDetail(userWorkDetailList));
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/userDetail")
    @ResponseBody
    public ResultInfo<?> userDetail(@RequestParam Integer userId) {
        try{
            UserDto user = userWorkApiService.getUserByUserId(userId);
            if(user != null ){
                user.setPassword(null);
                user.setSalt(null);
                return ResultInfo.success(user);
            }else{
                return ResultInfo.error("用户不存在");
            }
            
        }catch (Exception e){
            log.error("查询用户信息失败",e);
            return ResultInfo.error("查询用户信息失败");
        }

    }

    @Autowired
    private CommunicateRecordService communicateRecordService;



    @NoNeedAccessAuthorization
    @RequestMapping(value = "/voiceCallBack")
    @ResponseBody
    public ResultInfo<?> voiceCallBack(@RequestParam Integer communicateRecordId) {
        try{
            CommunicateRecordDto communicateRecordDto = new CommunicateRecordDto();
            communicateRecordDto.setCommunicateRecordId(communicateRecordId);
            CommunicateRecordDto resultDto = communicateRecordService.getOne(communicateRecordDto);
            if(resultDto == null ){
                return ResultInfo.error("沟通记录不存在");
            }
            Map<String,Object> map = new HashMap<>();
            map.put("toUserId",String.valueOf(resultDto.getCreator()));
            MessageUtil.sendSocketMessage(JSON.toJSONString(map),"voiceAi:complete");
            return ResultInfo.success("成功");
        }catch (Exception e){
            log.error("接收处理消息失败",e);
            return ResultInfo.error("接收处理消息失败");
        }

    }

    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;


    @RequestMapping("/getMyFullDepartmentTree")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<OrganizationResDTO> getFullDepartmentTree(Integer userId) {
        CurrentUser user=CurrentUser.getCurrentUser();
        if (user == null) {
            return R.error("用户未登录或登录信息异常");
        }
        RestfulResult<OrganizationResDTO> result = uacWxUserInfoApiService.getMyFullDepartmentTree(userId==null?user.getId():userId);
        if (result.isSuccess()) {
            return R.success(result.getData());
        }
        return R.error("获取组织结构异常");
    }
}
