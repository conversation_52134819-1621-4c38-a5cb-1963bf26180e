package com.vedeng.todolist.service;


import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.system.service.UserService;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Author: daniel
 * @Date: 2020/12/11 09 45
 * @Description:
 */
public interface ITodoInstance {

    @Resource
    public UserService userService = null;

    /**
     * 新增一条待办事项
     * @param buzId 业务id
     * @param buzExtra 业务冗余字段
     * @param comment 备注
     */
    void add(Integer buzId, String buzExtra, String comment, String buzProperty);

    /**
     * 完成待办事项
     * @param buzId 业务id
     */
    void finish(Integer buzId);

    /**
     * 是否存在待办事项
     *
     * @param bizId 业务id
     * @return
     */
    default boolean hasTodoItem(Integer bizId){
        return true;
    }

    /**
     * 获取当前http request的用户
     * @return 当前登录用户
     */
    default User getCurrentRequestUser(){
    	User user = null;
    	try {
	        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

	        if (attributes != null) {
	            HttpServletRequest request = attributes.getRequest();
	            user =  (User) request.getSession().getAttribute(ErpConst.CURR_USER);
	        }
	        //AROP-3979 前台请求syncYxgAptitudeStatus方法时，request中不带session，此时通过session获取user会报空指针异常
	        if(user == null){
	            return SpringContextHolder.getBean(UserService.class).getUserInfoByName("njadmin");
	        }

    	}catch(Exception e) {
    		user =new User();
    		user.setUsername("njadmin");
    		user.setUserId(2);
    	}
    	 return user;
    }

}
