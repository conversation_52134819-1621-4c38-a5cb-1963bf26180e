package com.vedeng.erp.buyorder.common.enums;

public enum AfterSalesTypeEnum {
     TH(546),HH(547),TP(548);
     Integer code;
     private AfterSalesTypeEnum(Integer code){
        this.code=code;
     }
     public static String getType(Integer code){
         for(AfterSalesTypeEnum e:values()){
            if(e.code.equals(code)){
                return e.name().toLowerCase()    ;
            }
         }
         return "";
     }
}
