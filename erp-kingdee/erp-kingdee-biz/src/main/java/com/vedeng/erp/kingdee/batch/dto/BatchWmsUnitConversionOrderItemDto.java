package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description 库存转换换单明细表
 * <AUTHOR>
 * @date 2023/2/22 9:47
 **/

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchWmsUnitConversionOrderItemDto {
    /**
    * 主键
    */
    private Integer wmsUnitConversionOrderItemId;

    /**
    * 主单id
    */
    private Integer wmsUnitConversionOrderId;

    /**
    * 来源sku id
    */
    private Integer sourceSkuId;

    /**
    * 来源sku
    */
    private String sourceSkuNo;

    /**
    * 来源sku名
    */
    private String sourceGoodsName;

    /**
    * 来源单位id
    */
    private Integer sourceUnitId;

    /**
    * 来源单位名
    */
    private String sourceUnitName;

    /**
    * 来源转换数量
    */
    private BigDecimal sourceNum;

    /**
    * 来源单价
    */
    private BigDecimal sourcePrice;

    /**
    * wms实际出库数量
    */
    private BigDecimal wmsRealOutNum;

    /**
    * 0未出 1部分出 2全部出
    */
    private Integer outStatus;

    /**
    * 目标sku Id
    */
    private Integer targetSkuId;

    /**
    * 目标sku
    */
    private String targetSkuNo;

    /**
    * 目标sku名
    */
    private String targetGoodsName;

    /**
    * 目标单位id
    */
    private Integer targetUnitId;

    /**
    * 目标单位名
    */
    private String targetUnitName;

    /**
    * 目标转换数量
    */
    private BigDecimal targetNum;

    /**
    * 目标单价
    */
    private BigDecimal targetPrice;

    /**
    * wms实际入库数量
    */
    private BigDecimal wmsRealInNum;

    /**
    * 0未入 1部分入 2全部入
    */
    private Integer inStatus;

    /**
    * 税率
    */
    private Integer taxRate;

    /**
    * 是否删除 0未删除 1删除
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 创建者id
    */
    private Integer creator;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者id
    */
    private Integer updater;

    /**
    * 修改者名称
    */
    private String updaterName;

    /**
    * 修改时间
    */
    private Date modTime;
}