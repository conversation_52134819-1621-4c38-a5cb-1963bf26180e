package com.vedeng.temporal.polling.universal.checker;

import java.util.Map;

/**
 * 条件检查器接口
 * 
 * 用于检查轮询查询结果是否满足完成条件的函数式接口。
 * 支持任意类型的数据检查，提供最大的灵活性。
 * 
 * 设计理念：
 * - 函数式接口，支持Lambda表达式
 * - 类型安全，支持泛型
 * - 上下文感知，可以获取完整的请求上下文
 * - 易于组合，支持逻辑组合操作
 * 
 * 使用示例：
 * <pre>
 * // 简单状态检查
 * CompletionChecker&lt;Map&lt;String, Object&gt;&gt; checker = (data, context) -&gt; 
 *     "APPROVED".equals(data.get("auditStatus"));
 * 
 * // 复杂条件组合
 * CompletionChecker&lt;Map&lt;String, Object&gt;&gt; checker = (data, context) -&gt; {
 *     String status = (String) data.get("auditStatus");
 *     Double amount = (Double) data.get("amount");
 *     
 *     return "APPROVED".equals(status) && amount != null && amount &gt; 0;
 * };
 * 
 * // 使用上下文信息
 * CompletionChecker&lt;Map&lt;String, Object&gt;&gt; checker = (data, context) -&gt; {
 *     String expectedStatus = (String) context.get("expectedStatus");
 *     return expectedStatus.equals(data.get("status"));
 * };
 * </pre>
 * 
 * @param <T> 检查数据的类型
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
@FunctionalInterface
public interface CompletionChecker<T> {
    
    /**
     * 检查数据是否满足完成条件
     * 
     * @param data 查询返回的数据，可能为null
     * @param context 请求上下文信息，包含业务ID、公司代码等
     * @return true 如果满足完成条件，false 如果不满足
     * @throws RuntimeException 如果检查过程中发生异常
     */
    boolean isCompleted(T data, Map<String, Object> context);
    
    /**
     * 组合多个检查器 - AND 逻辑
     * 所有检查器都返回 true 时才返回 true
     * 
     * @param other 另一个检查器
     * @return 组合后的检查器
     */
    default CompletionChecker<T> and(CompletionChecker<T> other) {
        return (data, context) -> 
            this.isCompleted(data, context) && other.isCompleted(data, context);
    }
    
    /**
     * 组合多个检查器 - OR 逻辑
     * 任一检查器返回 true 时就返回 true
     * 
     * @param other 另一个检查器
     * @return 组合后的检查器
     */
    default CompletionChecker<T> or(CompletionChecker<T> other) {
        return (data, context) -> 
            this.isCompleted(data, context) || other.isCompleted(data, context);
    }
    
    /**
     * 取反检查器
     * 
     * @return 取反后的检查器
     */
    default CompletionChecker<T> negate() {
        return (data, context) -> !this.isCompleted(data, context);
    }
    
    /**
     * 创建总是返回true的检查器
     * 用于测试或特殊场景
     * 
     * @param <T> 数据类型
     * @return 总是返回true的检查器
     */
    static <T> CompletionChecker<T> alwaysTrue() {
        return (data, context) -> true;
    }
    
    /**
     * 创建总是返回false的检查器
     * 用于测试或特殊场景
     * 
     * @param <T> 数据类型
     * @return 总是返回false的检查器
     */
    static <T> CompletionChecker<T> alwaysFalse() {
        return (data, context) -> false;
    }
    
    /**
     * 创建检查数据不为null的检查器
     * 
     * @param <T> 数据类型
     * @return 检查数据不为null的检查器
     */
    static <T> CompletionChecker<T> notNull() {
        return (data, context) -> data != null;
    }
}