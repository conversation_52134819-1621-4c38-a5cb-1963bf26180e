var template=function(){var c={},x=Object.prototype.hasOwnProperty,r=Array.prototype.indexOf,s=Array.prototype.some,t=Array.prototype.filter,u=Array.prototype.map,n={};c.type=function(a){var b,d=/\{\s*\[native\s*code\]\s*\}/i;null===a?b="null":"undefined"===typeof a?b="undefined":(b=Object.prototype.toString.call(a).match(/\w+/g)[1].toLowerCase(),"object"===b&&d.test(a+"")&&(b="function"));return b};c.trim=function(a){return(a+"").replace(/^[\s\u00A0]+|[\s\u00A0]+$/g,"")};c.extend=function(){var a=
arguments.callee,b,d;"object"!==c.type(arguments[0])?(b=1,d=!!arguments[0]):(b=0,d=!1);var e=arguments[b]||{};b=[].slice.call(arguments,b+1);for(var k,h;b.length;)if(k=b.shift(),"object"===c.type(k)){var f,g;for(g in k)if(f=k[g],"object"===c.type(f))if(f==window||f==document||"childNodes"in f&&"nextSibling"in f&&"nodeType"in f){if(d||!(g in e))e[g]=f}else if(f.jquery&&/^[\d\.]+$/.test(f.jquery))e[g]=f;else{h=c.type(e[g]);if(!(g in e)||"undefined"===h||"null"===h||d&&("string"===h||"number"===h||"bool"===
h))e[g]={};"object"===c.type(e[g])&&a(d,e[g],f)}else if(d||!(g in e))e[g]=f}return e};var p=c.each=function(a,b,d){if(null!=a)if([].forEach&&a.forEach===[].forEach)a.forEach(b,d);else if(a.length===+a.length)for(var e=0,k=a.length;e<k&&b.call(d,a[e],e,a)!==n;e++);else for(e in a)if(c.has(a,e)&&b.call(d,a[e],e,a)===n)break};c.has=function(a,b){return x.call(a,b)};c.identity=function(a){return a};var v=c.some=c.any=function(a,b,d){b||(b=c.identity);var e=!1;if(null==a)return e;if(s&&a.some===s)return a.some(b,
d);p(a,function(a,c,f){if(e||(e=b.call(d,a,c,f)))return n});return!!e};c.find=c.detect=function(a,b,d){var e;v(a,function(a,c,f){if(b.call(d,a,c,f))return e=a,!0});return e};c.contains=c.include=function(a,b){return null==a?!1:r&&a.indexOf===r?-1!=a.indexOf(b):v(a,function(a){return a===b})};c.filter=c.select=function(a,b,d){var c=[];if(null==a)return c;if(t&&a.filter===t)return a.filter(b,d);p(a,function(a,h,f){b.call(d,a,h,f)&&(c[c.length]=a)});return c};c.map=c.collect=function(a,b,c){var e=[];
if(null==a)return e;if(u&&a.map===u)return a.map(b,c);p(a,function(a,h,f){e[e.length]=b.call(c,a,h,f)});return e};c.invert=function(a){var b={},d;for(d in a)c.has(a,d)&&(b[a[d]]=d);return b};c.keys=Object.keys||function(a){if(a!==Object(a))throw new TypeError("Invalid object");var b=[],d;for(d in a)c.has(a,d)&&(b[b.length]=d);return b};c.values=function(a){var b=[],d;for(d in a)c.has(a,d)&&b.push(a[d]);return b};c.random=function(a,b){null==b&&(b=a,a=0);return a+Math.floor(Math.random()*(b-a+1))};
var l={escape:{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;"}};l.unescape=c.invert(l.escape);var y={escape:RegExp("["+c.keys(l.escape).join("")+"]","g"),unescape:RegExp("("+c.keys(l.unescape).join("|")+")","g")};c.each(["escape","unescape"],function(a){c[a]=function(b){return null==b?"":(""+b).replace(y[a],function(b){return l[a][b]})}});var w={evaluate:/{{([\s\S]+?)}}/g,interpolate:/{{=([\s\S]+?)}}/g,escape:/{{-([\s\S]+?)}}/g},q=/(.)^/,z={"'":"'","\\":"\\","\r":"r","\n":"n",
"\t":"t","\u2028":"u2028","\u2029":"u2029"},A=/\\|'|\r|\n|\t|\u2028|\u2029/g,m=function(a,b,d){var e;d=c.extend(!0,{},w,d);var k=RegExp([(d.escape||q).source,(d.interpolate||q).source,(d.evaluate||q).source].join("|")+"|$","g"),h=0,f="__p+='";a.replace(k,function(b,c,d,e,g){f+=a.slice(h,g).replace(A,function(a){return"\\"+z[a]});c&&(f+="'+\n((__t=("+c+"))==null?'':util.escape(__t))+\n'");d&&(f+="'+\n((__t=("+d+"))==null?'':__t)+\n'");e&&(f+="';\n"+e+"\n__p+='");h=g+b.length;return b});f+="';\n";d.variable||
(f="with(obj||{}){\n"+f+"}\n");f="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+f+"return __p;\n";try{e=new Function(d.variable||"obj","util",f)}catch(g){throw g.source=f,g;}if(b)return e(b,c);b=function(a){return e.call(this,a,c)};b.source="function("+(d.variable||"obj")+"){\n"+f+"}";return b};m.util=c;m.entities=l;m.settings=w;return m}.call(this);
