function complementTask(){
	checkLogin();
	var comment = $("input[name='comment']").val()
	var taskId = $("input[name='taskId']").val()
	var pass = $("input[name='pass']").val()
	var type = $("input[name='type']").val()
	var needRemark = $("input[name='needRemark']").val()
	if(pass =="false" && comment == ""){
		warnTips("comment","请填写备注");
		return false;
	}
	if (pass == "true" && needRemark == "true" && comment == ""){
		warnTips("comment","改低价订单，请填写备注");
		return false;
	}
	if(comment.length > 1024){
		warnTips("comment","备注内容不允许超过256个字符");
		return false;
	}
	    if(type==1003){
			var loadingIndex = layer.open({
				type: 3,
				content: '提交中...',
				shade: [0.5, '#000']
			});
			$.ajax({
				type: "POST",
				url: "../saleorder/checkSaleorderFlow.do",
				data: $('#complement').serialize(),
				dataType:'json',
				success: function(data){
					layer.close(loadingIndex);
					refreshPageList(data)
				},
				error:function(data){
					layer.close(loadingIndex);
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}else if(type == 1){
			var loadingIndex = layer.open({
				type: 3,
				content: '提交中...',
				shade: [0.5, '#000']
			});
			//销售订单审核
			$.ajax({
				type: "POST",
				url: "./complementTaskParallel.do",
				data: $('#complement').serialize(),
				dataType:'json',
				success: function(data){
					layer.close(loadingIndex);
					if (data.code === -3) {
						// 账期校验失败
						var period_html = '<div class="checkBillPeriodForOrder"> <br/>' +
							'<p style="color:red">当前客户账期额度不足，请至客户详情/财务与资质信息中申请 ! </p> ' +
							'<br/>' +
							'<table class="table table-bordered table-striped table-condensed table-centered">\n' +
							'    <thead>\n' +
							'    <tr>\n' +
							'        <th>账期类型</th>\n' +
							'        <th>可用额度（元）</th>\n' +
							'        <th>结算标准</th>\n' +
							'        <th>结算周期（天）</th>\n' +
							'    </tr>\n' +
							'    </thead>\n' +
							'    <tbody>\n' +
							' ';
						var responseData = data.data.allBillPeriod;
						for (let i = 0; i < responseData.length; i++) {
							if (responseData[i].billPeriodType === 1) {
								period_html += "<tr> <td>正式账期</td>";
							} else if (responseData[i].billPeriodType === 2) {
								period_html += "<tr> <td>临时账期</td>";
							} else if (responseData[i].billPeriodType === 3) {
								period_html += "<tr> <td>订单账期</td>";
							} else {
								period_html += "<tr> <td></td> "
							}
							period_html += "<td>" + responseData[i].availableAmount + "</td>";
							if (responseData[i].billPeriodSettlementType === 1) {
								period_html += "<td>订单发货</td>";
							} else if (responseData[i].billPeriodSettlementType === 2) {
								period_html += "<td>订单开票</td>";
							} else {
								period_html += "<td></td> "
							}
							period_html += "<td>" + responseData[i].settlementPeriod + "</td> </tr>";
						}

						period_html += '</tbody></table></div>'

						layer.open({
							type: 1,
							id: 'checkBillPeriodForOrder',
							title: '温馨提示:',
							area: ['460px', '250px'],
							resize: false,
							btn: ['前往', '取消'],
							content: period_html,
							yes: function (index, layero) {
								// 请至客户详情/财务与资质信息中申请 跳转

								layer.close(index);
							},

						});
					}else {
						refreshPageList(data);
					}

				},
				error:function(data){
					layer.close(loadingIndex);
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}else if(type == 2){
			var loadingIndex = layer.open({
				type: 3,
				content: '提交中...',
				shade: [0.5, '#000']
			});
			//销售售后审核
			$.ajax({
				type: "POST",
				url: "./complementAfterSaleTask.do",
				data: $('#complement').serialize(),
				dataType:'json',
				success: function(data){
					layer.close(loadingIndex);
					refreshPageList(data)
				},
				error:function(data){
					layer.close(loadingIndex);
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			
		}
	if (pass == "true" && needRemark == "true" && comment != ""){
		$.ajax({
			type: "POST",
			url: "./addCommentToLowerPrice.do",
			data: $('#complement').serialize(),
			dataType:'json',
		})
	}

}