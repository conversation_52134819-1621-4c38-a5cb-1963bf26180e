<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.GoodsSafeStockMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsSafeStock" >
    <id column="GOODS_SAFE_STOCK_ID" property="goodsSafeStockId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    GOODS_SAFE_STOCK_ID, COMPANY_ID, GOODS_ID, NUM, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByGoodsId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_GOODS_SAFE_STOCK
    where GOODS_ID = #{goodsId,jdbcType=INTEGER}
    and
    	COMPANY_ID = #{companyId,jdbcType=INTEGER} 
  </select>
  <insert id="insert" parameterType="com.vedeng.goods.model.GoodsSafeStock" >
    insert into T_GOODS_SAFE_STOCK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="goodsSafeStockId != null" >
        GOODS_SAFE_STOCK_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="goodsSafeStockId != null" >
        #{goodsSafeStockId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByGoodsId" parameterType="com.vedeng.goods.model.GoodsSafeStock" >
    update T_GOODS_SAFE_STOCK
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where GOODS_ID = #{goodsId,jdbcType=INTEGER}
  </update>
  
  <select id="getGoodsSafeStock" resultType="com.vedeng.goods.model.GoodsSafeStock">
    select 
    GOODS_SAFE_STOCK_ID, COMPANY_ID, GOODS_ID, NUM, ADD_TIME, CREATOR, MOD_TIME, UPDATER
    from T_GOODS_SAFE_STOCK
    where 
    	GOODS_ID in
		<foreach collection="goodsIds" item="goodsId" index="index"
          open="(" close=")" separator=",">
          #{goodsId}
    	</foreach>
    and
    	COMPANY_ID = #{companyId,jdbcType=INTEGER} 
  </select>
</mapper>