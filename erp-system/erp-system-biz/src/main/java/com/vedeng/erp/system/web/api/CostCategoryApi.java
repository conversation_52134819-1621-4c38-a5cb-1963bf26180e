package com.vedeng.erp.system.web.api;

import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.system.domain.dto.CostCategoryDto;
import com.vedeng.erp.system.service.CostCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.system.web.api
 * @Date 2022/8/12 15:33
 */
@ExceptionController
@RestController
@RequestMapping("/costCategory")
@Slf4j
public class CostCategoryApi {

    @Autowired
    CostCategoryService costCategoryService;

    /**
     * 新增费用类别
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<?> add(CostCategoryDto costCategoryDto) {
        this.validatorDtoInfo(costCategoryDto);
        costCategoryService.insertSelective(costCategoryDto);
        return R.success();
    }

    /**
     * 根据ID查询费用类别信息
     */
    @RequestMapping(value = "/getCostCategoryInfo")
    public R<?> getCostCategoryInfo(Integer costCategoryId) {
        return R.success(costCategoryService.selectByPrimaryKey(costCategoryId));
    }


    /**
     * 更新费用类别
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<?> update(CostCategoryDto costCategoryDto) {
        this.validatorDtoInfo(costCategoryDto);
        if (costCategoryDto.getCategoryName().equals(costCategoryDto.getOldCategoryName())){
            costCategoryDto.setOldCategoryName(null);
        }
        if (costCategoryDto.getUnitKingDeeNo().equals(costCategoryDto.getOldUnitKingDeeName().split("-")[0])){
            costCategoryDto.setOldUnitKingDeeName(null);
        }
        return R.success(costCategoryService.updateByPrimaryKeySelective(costCategoryDto));
    }

    /**
     * 校验
     *
     * @param costCategoryDto 参数
     */
    private void validatorDtoInfo(CostCategoryDto costCategoryDto) {

        //名称为空
        if (Objects.isNull(costCategoryDto.getCategoryName())) {
            throw new ServiceException("名称为空!");
        }

        //名称长度
        if (costCategoryDto.getCategoryName().length() > ErpConstant.STR_SIZE_LIMIT) {
            throw new ServiceException("名称长度过长!");
        }

        //重复校验
        costCategoryService.checkRepeat(costCategoryDto);
    }


}
