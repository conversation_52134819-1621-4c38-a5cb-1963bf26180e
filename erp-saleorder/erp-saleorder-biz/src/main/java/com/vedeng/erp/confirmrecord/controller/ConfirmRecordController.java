package com.vedeng.erp.confirmrecord.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.confirmrecord.dto.ButtonIsShow;
import com.vedeng.erp.confirmrecord.dto.SignInRecordDto;
import com.vedeng.erp.confirmrecord.model.ConfirmRecordRes;
import com.vedeng.erp.confirmrecord.service.ConfirmRecordService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/11 11:06
 */
@Controller
@RequestMapping("/order/confirm")
public class ConfirmRecordController extends BaseController {

    @Resource
    private ConfirmRecordService confirmRecordService;

    /**
     * 异步加载订单确认模块
     * @param saleorderId 订单Id
     */
    @ResponseBody
    @RequestMapping("/getConfirmRecord")
    @NoNeedAccessAuthorization
    public ResultInfo<ConfirmRecordRes> getConfirmRecord(Integer saleorderId){
        if(saleorderId==null){
           return ResultInfo.error();
        }
        ConfirmRecordRes confirmRecordRes;
        try {
             confirmRecordRes =confirmRecordService.getConfirmRecord(saleorderId);
        }catch (Exception e){
            logger.error("异步加载订单确认模块失败: 入参[{}],异常信息[{}]",saleorderId,e);
           return ResultInfo.error();
        }
        return ResultInfo.success(confirmRecordRes);
    }

    /**
     * 复制链接
     * @param saleorderId 订单Id
     */
    @ResponseBody
    @RequestMapping("/copyUrlLink")
    @NoNeedAccessAuthorization
    public ResultInfo<String> copyUrlLink(Integer saleorderId){
        if(saleorderId==null){
            return ResultInfo.error();
        }
        String url;
        try{
           url =  confirmRecordService.copyUrlLink(saleorderId);
        }catch (Exception e){
            log.error("复制链接失败：[{}],异常信息[{}]",saleorderId,e);
            return ResultInfo.error(e.getMessage());
        }
        return ResultInfo.success("复制成功",url);

    }

    /**
     * 发送短信
     * @param saleorderId 订单Id
     * @param session session
     */
    @ResponseBody
    @RequestMapping("/sendMessage")
    @NoNeedAccessAuthorization
    public ResultInfo sendMessage(Integer saleorderId , HttpSession session){
        if(saleorderId==null){
            return ResultInfo.error();
        }
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        try{
            confirmRecordService.sendMessage(saleorderId,user);
        }catch (Exception e){
            log.error("发送短信失败：[{}],异常信息[{}]",saleorderId,e);
         return ResultInfo.error(e.getMessage());
        }
        return ResultInfo.success("发送成功");

    }

    /**
     * 复制按钮是否展示
     */
    @ResponseBody
    @RequestMapping("/copyAndSendMessageButtonIsShow")
    @NoNeedAccessAuthorization
    public ResultInfo<ButtonIsShow> copyAndSendMessageButtonIsShow(Integer saleorderId , HttpSession session){
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ButtonIsShow buttonIsShow =confirmRecordService.copyAndSendMessageButtonIsShow(saleorderId,user);
        return ResultInfo.success(buttonIsShow);

    }

    /**
     * 客户签收记录模块
     * @return
     */
    @ResponseBody
    @RequestMapping("/customerSignature")
    @NoNeedAccessAuthorization
    public ResultInfo customerSignature(Integer saleorderId ){
        List<SignInRecordDto> confirmRecordList;
        if(saleorderId==null){
            return ResultInfo.error();
        }
        try{
            confirmRecordList=confirmRecordService.queryCustomerSignature(saleorderId);
        }catch (Exception e){
            log.error("户签收记录模块加载失败:[{}],异常信息[{}]",saleorderId,e);
            return ResultInfo.error(e.getMessage());
        }

        return ResultInfo.success(confirmRecordList);
    }
}
