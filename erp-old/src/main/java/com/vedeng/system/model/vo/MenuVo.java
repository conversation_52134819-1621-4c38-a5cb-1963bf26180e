package com.vedeng.system.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Cherny.chen
 * @Create: 2021/4/12 9:24
 */
@Data
public class MenuVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // 菜单ID
    private Integer menuId;

    // 父级ID
    private Integer parentId;

    // 链接地址
    private String link;

    // 名称
    private String name;

    // 排序
    private Integer sort;

    private String iconStyle;

    //
    private List<MenuVo> children;


}