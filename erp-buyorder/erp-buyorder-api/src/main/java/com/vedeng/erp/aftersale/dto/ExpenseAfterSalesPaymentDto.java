package com.vedeng.erp.aftersale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 采购费用单退款信息
 * @date 2022/11/7 9:26
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseAfterSalesPaymentDto {

    /**
     * 已付金额（不含信用支付）
     */
    private BigDecimal paymentAmount;

    /**
     * 退货金额 当前退货售后单的总额
     */
    private BigDecimal returnAmount;

    /**
     * 偿还账期
     */
    private BigDecimal repaymentPeriod;

    /**
     * 应退金额
     */
    private BigDecimal needReturnAmount;

    /**
     * 已退金额
     */
    private BigDecimal haveReturnAmount;

    /**
     * 退款状态 退款状态 0无退款 1未退款 2部分退款 3全部退款
     */
    private Integer refundStatus;

    /**
     * 款项退还 1退至公司账户 2退至供应商余额
     */
    private Integer refundMethod;
}
