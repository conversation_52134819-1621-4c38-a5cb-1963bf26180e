package com.vedeng.workbench.model.dto.generate;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * T_BUSSINESS_CHANCE
 * <AUTHOR>
@Data
public class WorkbenchBussinessChanceDo implements Serializable {

    private Integer bussinessChanceId;

    /**
     * 网站询价ID
     */
    private Integer webBussinessChanceId;

    /**
     * 商机单号
     */
    private String bussinessChanceNo;

    /**
     * 网站用户ID
     */
    private Integer webAccountId;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 部门ID
     */
    private Integer orgId;

    /**
     * 归属ERP用户ID
     */
    private Integer userId;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String checkTraderName;

    /**
     * 客户地区
     */
    private String checkTraderArea;

    /**
     * 客户联系人
     */
    private String checkTraderContactName;

    /**
     * 客户联系人手机
     */
    private String checkTraderContactMobile;

    /**
     * 客户联系人电话
     */
    private String checkTraderContactTelephone;

    /**
     * 商机类型字典库
     */
    private Integer type;

    /**
     * 商机时间
     */
    private Long receiveTime;

    /**
     * 商机来源字典库
     */
    private Integer source;

    /**
     * 询价方式字典库
     */
    private Integer communication;

    /**
     * 询价内容
     */
    private String content;

    /**
     * 商品分类 字典库
     */
    private Integer goodsCategory;

    /**
     * 商品品牌
     */
    private String goodsBrand;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 地区最小级ID
     */
    private Integer areaId;

    /**
     * 多级地址逗号“,”拼接（冗余字段）
     */
    private String areaIds;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 其它联系方式
     */
    private String otherContact;

    /**
     * 备注
     */
    private String comments;

    /**
     * 分配时间
     */
    private Long assignTime;

    /**
     * 初次查看时间(归属人)
     */
    private Long firstViewTime;

    /**
     * 商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
     */
    private Integer status;

    /**
     * 审核备注(关闭原因) 字典库
     */
    private Integer statusComments;

    /**
     * 关闭原因备注
     */
    private String closedComments;

    /**
     * 微信openID
     */
    private String wenxinOpenId;

    /**
     * 商机等级字典库
     */
    private Integer bussinessLevel;

    /**
     * 商机阶段字典库
     */
    private Integer bussinessStage;

    /**
     * 询价类型字典库
     */
    private Integer enquiryType;

    /**
     * 成单机率字典库
     */
    private Integer orderRate;

    /**
     * 预计金额
     */
    private BigDecimal amount;

    /**
     * 预计成单时间
     */
    private Long orderTime;

    /**
     * 商机作废原因
     */
    private Integer cancelReason;

    /**
     * 其它原因
     */
    private String otherReason;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 咨询入口字典库：商品详情页，搜索结果页、产品导航页、品牌中心、页头、右侧悬浮、专题模板
     */
    private Integer entrances;

    /**
     * 功能字典库：立即询价、立即订购、商品咨询、帮您找货、发送采购需求
     */
    private Integer functions;

    /**
     * 0：老商机，1：新商机
     */
    private Boolean isNew;

    private String productComments;

    /**
     * 合并状态，0未合并，1被合并，2合并其他的
     */
    private Integer mergeStatus;

    /**
     * 旧商机单号
     */
    private String oldChanceNo;

    /**
     * 关联的商机id
     */
    private Integer bussinessParentId;

    /**
     * 产品备注（销售）
     */
    private String productCommentsSale;

    /**
     * 关闭申请审核状态，0未申请审核或者，1待审核
     */
    private Boolean closeCheckStatus;

    /**
     * 商机关联,json字符串
     */
    private String bncLink;

    /**
     * 是否关联bd订单,0未关联,1已关联
     */
    private Boolean isLinkBd;

    private static final long serialVersionUID = 1L;
}