<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.visitrecord.mapper.CrmVisitRecordMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.crm.visitrecord.domain.entity.CrmVisitRecordEntity">
        <id column="ID" jdbcType="BIGINT" property="id"/>
        <result column="VISIT_RECORD_NO" jdbcType="VARCHAR" property="visitRecordNo"/>
        <result column="PLAN_VISIT_DATE" jdbcType="TIMESTAMP" property="planVisitDate"/>
        <result column="VISITOR_ID" jdbcType="INTEGER" property="visitorId"/>
        <result column="VISITOR_NAME" jdbcType="VARCHAR" property="visitorName"/>
        <result column="VISIT_TARGET" jdbcType="VARCHAR" property="visitTarget"/>
        <result column="PROVINCE_CODE" jdbcType="INTEGER" property="provinceCode"/>
        <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="provinceName"/>
        <result column="CITY_CODE" jdbcType="INTEGER" property="cityCode"/>
        <result column="CITY_NAME" jdbcType="VARCHAR" property="cityName"/>
        <result column="AREA_CODE" jdbcType="INTEGER" property="areaCode"/>
        <result column="AREA_NAME" jdbcType="VARCHAR" property="areaName"/>
        <result column="VISIT_ADDRESS" jdbcType="VARCHAR" property="visitAddress"/>
        <result column="CUSTOMER_NAME" jdbcType="VARCHAR" property="customerName"/>
        <result column="CUSTOMER_FROM" jdbcType="INTEGER" property="customerFrom"/>
        <result column="CUSTOMER_NATURE" jdbcType="INTEGER" property="customerNature"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId"/>
        <result column="ACTUAL_VISIT_DATE" jdbcType="TIMESTAMP" property="actualVisitDate"/>
        <result column="CARD_OFF" jdbcType="VARCHAR" property="cardOff"/>
        <result column="CARD_TIME" jdbcType="TIMESTAMP" property="cardTime"/>
        <result column="PICTURE_LIST" jdbcType="VARCHAR" property="pictureList"/>
        <result column="CONTACT_NAME" jdbcType="VARCHAR" property="contactName"/>
        <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="CONTACT_TELE" jdbcType="VARCHAR" property="contactTele"/>
        <result column="CONTACT_POSITION" jdbcType="VARCHAR" property="contactPosition"/>
        <result column="OTHER_CONTACT" jdbcType="VARCHAR" property="otherContact"/>
        <result column="SHOW_PPT" jdbcType="VARCHAR" property="showPpt"/>
        <result column="INVITE_REG" jdbcType="VARCHAR" property="inviteReg"/>
        <result column="REG_MOBILE" jdbcType="VARCHAR" property="regMobile"/>
        <result column="TRADER_CONTRACT_ID" jdbcType="INTEGER" property="traderContractId"/>
        <result column="COMMUCATE_CONTENT" jdbcType="VARCHAR" property="commucateContent"/>
        <result column="NEXT_VISIT_DATE" jdbcType="TIMESTAMP" property="nextVisitDate"/>
        <result column="CREATE_BUSINESS_CHANGE" jdbcType="VARCHAR" property="createBusinessChange"/>
        <result column="VISIT_SUCCESS" jdbcType="VARCHAR" property="visitSuccess"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="ADD_USER_ID" jdbcType="INTEGER" property="addUserId"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="MOD_USER_ID" jdbcType="INTEGER" property="modUserId"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="VISIT_RECORD_STATUS" jdbcType="INTEGER" property="visitRecordStatus"/>
        <result column="CLOSE_REASON_TYPE" jdbcType="INTEGER" property="closeReasonType"/>
        <result column="CLOSE_REASON_CONTENT" jdbcType="VARCHAR" property="closeReasonContent"/>
        <result column="NO_CONTRACT" jdbcType="VARCHAR" property="noContract"/>
        <result column="RELATE_TYPE" jdbcType="INTEGER" property="relateType"/>
        <result column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId"/>
        <result column="BUSSINESS_CHANCE_NO" jdbcType="VARCHAR" property="bussinessChanceNo"/>
        <result column="COMPLETE_DATETIME" jdbcType="TIMESTAMP" property="completeDatetime"/>
        <result column="CLOSE_DATETIME" jdbcType="TIMESTAMP" property="closeDatetime"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="CUSTOMER_REQUIRES" jdbcType="VARCHAR" property="customerRequires"/>
        <result column="ORG_ID" jdbcType="INTEGER" property="orgId"/>
        <result column="ORG_GROUP" jdbcType="VARCHAR" property="orgGroup"/>
        <result column="RECORD_CONTACT_NAME"  jdbcType="VARCHAR" property="recordContactName" />
        <result column="RECORD_CONTACT_MOBILE"  jdbcType="VARCHAR" property="recordContactMobile" />
        <result column="RECORD_CONTACT_TELE"  jdbcType="VARCHAR" property="recordContactTele" />
        <result column="RECORD_CONTACT_POSITION"  jdbcType="VARCHAR" property="recordContactPosition" />
        <result column="RECORD_OTHER_CONTACT"  jdbcType="VARCHAR" property="recordOtherContact" />
        <result column="RECORD_NO_CONTRACT"  jdbcType="VARCHAR" property="recordNoContract" />
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        VISIT_RECORD_NO,
        PLAN_VISIT_DATE,
        VISITOR_ID,
        VISITOR_NAME,
        VISIT_TARGET,
        PROVINCE_CODE,
        PROVINCE_NAME,
        CITY_CODE,
        CITY_NAME,
        AREA_CODE,
        AREA_NAME,
        VISIT_ADDRESS,
        CUSTOMER_NAME,
        CUSTOMER_FROM,
        CUSTOMER_NATURE,
        TRADER_ID,
        TRADER_CUSTOMER_ID,
        ACTUAL_VISIT_DATE,
        CARD_OFF,
        CARD_TIME,
        PICTURE_LIST,
        CONTACT_NAME,
        CONTACT_MOBILE,
        CONTACT_TELE,
        CONTACT_POSITION,
        OTHER_CONTACT,
        SHOW_PPT,
        INVITE_REG,
        REG_MOBILE,
        TRADER_CONTRACT_ID,
        COMMUCATE_CONTENT,
        NEXT_VISIT_DATE,
        CREATE_BUSINESS_CHANGE,
        VISIT_SUCCESS,
        ADD_TIME,
        ADD_USER_ID,
        MOD_TIME,
        MOD_USER_ID,
        IS_DELETE,
        VISIT_RECORD_STATUS,
        CLOSE_REASON_TYPE,
        CLOSE_REASON_CONTENT,
        NO_CONTRACT,
        RELATE_TYPE,
        BUSSINESS_CHANCE_ID,
        BUSSINESS_CHANCE_NO,
        COMPLETE_DATETIME,
        CLOSE_DATETIME,
        REMARK,
        CUSTOMER_REQUIRES,
        ORG_ID,
        ORG_GROUP,
        RECORD_CONTACT_NAME,
        RECORD_CONTACT_MOBILE,
        RECORD_CONTACT_TELE,
        RECORD_CONTACT_POSITION,
        RECORD_OTHER_CONTACT,
        RECORD_NO_CONTRACT
    </sql>

    <sql id="selectVisitRecordVo">
        SELECT
            ID,
            PLAN_VISIT_DATE,
            VISITOR_ID,
            VISITOR_NAME,
            VISIT_TARGET,
            PROVINCE_CODE,
            PROVINCE_NAME,
            CITY_CODE,
            CITY_NAME,
            AREA_CODE,
            AREA_NAME,
            VISIT_ADDRESS,
            CUSTOMER_NAME,
            CUSTOMER_FROM,
            CUSTOMER_NATURE,
            TRADER_ID,
            TRADER_CUSTOMER_ID,
            ACTUAL_VISIT_DATE,
            CARD_OFF,
            CARD_TIME,
            PICTURE_LIST,
            CONTACT_NAME,
            CONTACT_MOBILE,
            CONTACT_TELE,
            CONTACT_POSITION,
            OTHER_CONTACT,
            SHOW_PPT,
            INVITE_REG,
            REG_MOBILE,
            TRADER_CONTRACT_ID,
            COMMUCATE_CONTENT,
            NEXT_VISIT_DATE,
            CREATE_BUSINESS_CHANGE,
            VISIT_SUCCESS,
            ADD_TIME,
            ADD_USER_ID,
            MOD_TIME,
            MOD_USER_ID,
            IS_DELETE,
            VISIT_RECORD_STATUS,
            CLOSE_REASON_TYPE,
            CLOSE_REASON_CONTENT,
            NO_CONTRACT,
            RELATE_TYPE,
            BUSSINESS_CHANCE_ID,
            BUSSINESS_CHANCE_NO,
            COMPLETE_DATETIME,
            CLOSE_DATETIME,
            REMARK,
            CUSTOMER_REQUIRES,
            ORG_ID,
            ORG_GROUP,
            RECORD_CONTACT_NAME,
            RECORD_CONTACT_MOBILE,
            RECORD_CONTACT_TELE,
            RECORD_CONTACT_POSITION,
            RECORD_OTHER_CONTACT,
            RECORD_NO_CONTRACT
        FROM T_VISIT_RECORD
        WHERE IS_DELETE = 0
    </sql>

    <sql id="globalWhere">
        <choose>
            <when test="seeAll != null and seeAll == 1">
                <!-- 此时不需要增加任何额外的条件 -->
            </when>
            <otherwise>
                AND (
                    <!--1 创建人条件 -->
                    A.ADD_USER_ID IN
                    <foreach collection="allSubordinateUserIdList" item="userIdItem" index="indexU" open="(" close=")" separator=",">
                        #{userIdItem,jdbcType=INTEGER}
                    </foreach>
                    OR
                    <!--2 拜访人条件 -->
                    A.VISITOR_ID IN
                    <foreach collection="allSubordinateUserIdList" item="userIdItem" index="indexU" open="(" close=")" separator=",">
                        #{userIdItem,jdbcType=INTEGER}
                    </foreach>
                    OR
                    <!--3 同行人条件 -->
                    A.ID IN (
                        SELECT RECORD_ID FROM T_VISIT_RECORD_TONGXING_USER WHERE TONGXING_USER_ID IN
                        <foreach collection="allSubordinateUserIdList" item="userIdItem" index="indexU" open="(" close=")" separator=",">
                            #{userIdItem,jdbcType=INTEGER}
                        </foreach>
                    )

                    <!--4 客户归属条件 -->
                    OR
                    A.TRADER_ID IN
                    (
                        SELECT TRADER_ID FROM T_R_TRADER_J_USER WHERE TRADER_TYPE =1 AND
                            USER_ID IN
                            <foreach collection="allSubordinateUserIdList" item="userIdItem" index="indexU" open="(" close=")" separator=",">
                                #{userIdItem,jdbcType=INTEGER}
                            </foreach>
                    )
                )
            </otherwise>
        </choose>
    </sql>

    <select id="selectVisitRecordCreateUserList" resultType="com.vedeng.crm.visitrecord.domain.vo.UserForVisitDto">
        select
            U.USER_ID,U.USERNAME,
            D.ALIAS_HEAD_PICTURE
        from T_USER U
        LEFT JOIN T_USER_DETAIL D ON U.USER_ID = D.USER_ID
        where
            U.USER_ID in
            (
                SELECT
                    distinct A.ADD_USER_ID
                FROM T_VISIT_RECORD A
                LEFT JOIN T_VISIT_RECORD_TONGXING_USER T
                ON A.ID = T.RECORD_ID
                WHERE A.IS_DELETE = 0
                    <include refid="globalWhere">
                    </include>
                )
            group by U.USER_ID,U.USERNAME ORDER BY U.USERNAME
    </select>

    <select id="selectVisitRecordVisitUserList" resultType="com.vedeng.crm.visitrecord.domain.vo.UserForVisitDto">
        select
        U.USER_ID,U.USERNAME,
        D.ALIAS_HEAD_PICTURE
        from T_USER U
        LEFT JOIN T_USER_DETAIL D ON U.USER_ID = D.USER_ID
        where
        U.USER_ID in
        (
        SELECT
            distinct A.VISITOR_ID
        FROM T_VISIT_RECORD A
        LEFT JOIN T_VISIT_RECORD_TONGXING_USER T
        ON A.ID = T.RECORD_ID
        WHERE A.IS_DELETE = 0
        <include refid="globalWhere">
        </include>
        )
        group by U.USER_ID,U.USERNAME ORDER BY U.USERNAME
    </select>

    <select id="selectVisitRecordTongXingUserList" resultType="com.vedeng.crm.visitrecord.domain.vo.UserForVisitDto">
        select
        U.USER_ID,U.USERNAME,
        D.ALIAS_HEAD_PICTURE
        from T_USER U
        LEFT JOIN T_USER_DETAIL D ON U.USER_ID = D.USER_ID
        where
        U.USER_ID in
        (
        SELECT
            distinct  T.TONGXING_USER_ID
        FROM T_VISIT_RECORD A
        LEFT JOIN T_VISIT_RECORD_TONGXING_USER T
        ON A.ID = T.RECORD_ID
        WHERE A.IS_DELETE = 0
        <include refid="globalWhere">
        </include>
        )
        group by U.USER_ID,U.USERNAME ORDER BY U.USERNAME
    </select>


    <select id="selectVisitRecordList" resultType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo">
        SELECT
            A.ID,
            A.VISIT_RECORD_NO,
            A.PLAN_VISIT_DATE,
            A.VISITOR_ID,
            A.VISITOR_NAME,
            A.VISIT_TARGET,
            A.PROVINCE_CODE,
            A.PROVINCE_NAME,
            A.CITY_CODE,
            A.CITY_NAME,
            A.AREA_CODE,
            A.AREA_NAME,
            A.VISIT_ADDRESS,
            A.CUSTOMER_NAME,
            A.CUSTOMER_FROM,
            A.CUSTOMER_NATURE,
            A.TRADER_ID,
            A.TRADER_CUSTOMER_ID,
            A.ACTUAL_VISIT_DATE,
            A.CARD_OFF,
            A.CARD_TIME,
            A.PICTURE_LIST,
            A.CONTACT_NAME,
            A.CONTACT_MOBILE,
            A.CONTACT_TELE,
            A.CONTACT_POSITION,
            A.OTHER_CONTACT,
            A.SHOW_PPT,
            A.INVITE_REG,
            A.ADD_TIME,
            A.COMPLETE_DATETIME,
            A.VISIT_RECORD_STATUS,
            A.BUSSINESS_CHANCE_ID as bussinessChanceId,
            A.BUSSINESS_CHANCE_NO as bussinessChanceNo,
            A.RECORD_CONTACT_NAME,
            A.RECORD_CONTACT_MOBILE,
            A.RECORD_CONTACT_TELE,
            A.RECORD_CONTACT_POSITION,
            A.RECORD_OTHER_CONTACT,
            A.RECORD_NO_CONTRACT,
            VISIT_USER_DETAIL.ALIAS_HEAD_PICTURE AS visitorPic,
            GROUP_CONCAT(DISTINCT TX.USERNAME) AS tongXingUserNames,
            CREATE_USER.USERNAME AS "addUserName",
            CREATE_USER_DETAIL.ALIAS_HEAD_PICTURE as "addUserPic",
            CASE WHEN
                TYC.TRADER_INFO_TYC_ID IS NOT NULL THEN 'Y'
            ELSE
                'N' END AS "tycFlag",
            tlfe.TRADER_LEVEL as customerGrade
        FROM T_VISIT_RECORD A
        LEFT JOIN T_TRADER_CUSTOMER CUSTOMER ON A.TRADER_ID = CUSTOMER.TRADER_ID
        LEFT JOIN DWH_TRADER_LIST_FILTER_ERP tlfe ON tlfe.TRADER_CUSTOMER_ID=CUSTOMER.TRADER_CUSTOMER_ID
        LEFT JOIN T_USER CREATE_USER ON A.ADD_USER_ID = CREATE_USER.USER_ID
        LEFT JOIN T_USER_DETAIL CREATE_USER_DETAIL ON A.ADD_USER_ID = CREATE_USER_DETAIL.USER_ID

        LEFT JOIN T_USER_DETAIL VISIT_USER_DETAIL ON VISIT_USER_DETAIL.USER_ID = A.VISITOR_ID
        LEFT JOIN T_TRADER_INFO_TYC TYC ON A.CUSTOMER_NAME=TYC.NAME
        LEFT JOIN T_COMMUNICATE_RECORD C
            ON A.ID = C.RELATED_ID AND C.COMMUNICATE_TYPE = 5503
        LEFT JOIN T_VISIT_RECORD_TONGXING_USER T
            ON A.ID = T.RECORD_ID
        LEFT JOIN T_VISIT_RECORD_TONGXING_USER T1
            ON A.ID = T1.RECORD_ID
        LEFT JOIN T_USER TX ON T1.TONGXING_USER_ID = TX.USER_ID
        WHERE A.IS_DELETE = 0
        <include refid="globalWhere">
        </include>


        <!-- 1、客户名称 CUSTOMER_NAME (模糊匹配) -->
        <if test="customerName != null and customerName != ''">
            AND A.CUSTOMER_NAME LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <!-- 2、拜访计划编号 VISIT_RECORD_NO (精确匹配) -->
        <if test="visitRecordNo != null and visitRecordNo != ''">
            AND A.VISIT_RECORD_NO = #{visitRecordNo,jdbcType=VARCHAR}
        </if>
        <!-- 3、拜访计划状态 VISIT_RECORD_STATUS (精确匹配) -->
        <if test="visitRecordStatusList != null and visitRecordStatusList.size() != 0">
            AND A.VISIT_RECORD_STATUS IN
            <foreach collection="visitRecordStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 4、拜访人ID VISITOR_ID (精确匹配，存在多个) -->
        <if test="visitorIdList != null and visitorIdList.size() != 0">
            AND A.VISITOR_ID IN
            <foreach collection="visitorIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 5、同行人集合-->
        <if test="tongxingIdList != null and tongxingIdList.size() != 0">
            AND T.TONGXING_USER_ID IN
            <foreach collection="tongxingIdList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <!-- 5.2 创建人集合 -->
        <if test="creatorIdList != null and creatorIdList.size() != 0">
            AND A.ADD_USER_ID IN
            <foreach collection="creatorIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 6、计划拜访时间范围 PLAN_VISIT_DATE (日期范围) -->
        <if test="planVisitDateStart != null and planVisitDateStart!=''">
            AND A.PLAN_VISIT_DATE <![CDATA[ >= ]]> #{planVisitDateStart,jdbcType=VARCHAR}
        </if>
        <if test="planVisitDateEnd != null  and planVisitDateEnd!=''">
            AND A.PLAN_VISIT_DATE <![CDATA[ <= ]]> #{planVisitDateEnd,jdbcType=VARCHAR}
        </if>

        <!-- 7、实际拜访时间范围 COMPLETE_DATETIME (日期范围) -->
        <if test="completeDateStart != null and completeDateStart!=''">
            AND A.COMPLETE_DATETIME <![CDATA[ >= ]]> concat(#{completeDateStart,jdbcType=VARCHAR},' 00:00:00')
        </if>
        <if test="completeDateEnd != null and completeDateEnd!=''">
            AND A.COMPLETE_DATETIME <![CDATA[ <= ]]> CONCAT(#{completeDateEnd,jdbcType=VARCHAR},' 23:59:59')
        </if>

        <!-- 8、创建时间范围 ADD_TIME (日期范围) -->
        <if test="addTimeStart != null  and addTimeStart!=''">
            AND A.ADD_TIME <![CDATA[ >= ]]> concat(#{addTimeStart,jdbcType=VARCHAR},' 00:00:00')
        </if>
        <if test="addTimeEnd != null   and addTimeEnd!=''">
            AND A.ADD_TIME <![CDATA[ <= ]]> CONCAT(#{addTimeEnd,jdbcType=VARCHAR},' 23:59:59')
        </if>


        <!-- 9、客户类型 CUSTOMER_NATURE (精确匹配) -->
        <if test="customerNature !=null ">
            AND A.CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER}
        </if>
        <!--<if test="customerNatureList != null and customerNatureList.size() != 0">
            AND CUSTOMER_NATURE IN
            <foreach collection="customerNatureList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>-->
        <!-- 10、拜访内容-联系人信息-姓名 （模糊查询） -->
        <if test="contactName != null and contactName != ''">
            AND (A.CONTACT_NAME LIKE CONCAT('%', #{contactName}, '%') OR A.RECORD_CONTACT_NAME LIKE CONCAT('%', #{contactName}, '%'))
        </if>
        <!-- 11、拜访内容-联系人信息-手机（模糊查询） -->
        <if test="contactMobile != null and contactMobile != ''">
            AND (A.CONTACT_MOBILE LIKE CONCAT('%', #{contactMobile}, '%') OR A.RECORD_CONTACT_MOBILE LIKE CONCAT('%', #{contactMobile}, '%'))
        </if>
        <!-- 12、拜访内容-联系人信息-电话（模糊查询） -->
        <if test="contactTele != null and contactTele != ''">
            AND (A.CONTACT_TELE LIKE CONCAT('%', #{contactTele}, '%') OR A.RECORD_CONTACT_TELE LIKE CONCAT('%', #{contactTele}, '%'))
        </if>
        <!-- 13、拜访目标-集合 (A新客开发B商机跟进C老客客情维护D签约会员E产品推广，以逗号隔开) （模糊查询）-->
        <if test="visitTargetList != null and visitTargetList.size() != 0">
            AND
            <foreach collection="visitTargetList" item="item" open="(" separator=" OR " close=")">
                A.VISIT_TARGET LIKE  '%${item}%'
            </foreach>

        </if>
        <!-- 14、线索/商机编号（模糊查询）-->
        <if test="bussinessChanceNo != null and bussinessChanceNo != ''">
            AND A.BUSSINESS_CHANCE_NO LIKE CONCAT('%', #{bussinessChanceNo,jdbcType=VARCHAR}, '%')
        </if>
        <!-- 15、沟通事项-沟通情况 （模糊查询）-->
        <if test="commucateContent != null and commucateContent != ''">
            AND C.CONTENT_SUFFIX LIKE CONCAT('%', #{commucateContent,jdbcType=VARCHAR}, '%')
        </if>
        GROUP by A.ID
        ORDER BY A.${orderBy}
    </select>



    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM T_VISIT_RECORD
        WHERE IS_DELETE=0
        AND ID = #{id}
    </select>

    <select id="selectVisitRecordListForToday"   resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM T_VISIT_RECORD
        WHERE IS_DELETE=0
        AND VISIT_RECORD_STATUS = 1
        AND PLAN_VISIT_DATE = CURDATE()
        ORDER BY ADD_TIME DESC
    </select>

    <!-- 查询今日、拜访人未打卡  -->
    <select id="selectVisitRecordListForTodayVisitorNotCard"   resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM T_VISIT_RECORD
        WHERE IS_DELETE=0
        AND VISIT_RECORD_STATUS IN (1,2)
        AND PLAN_VISIT_DATE = CURDATE()
        AND NOT EXISTS (
            SELECT 1
            FROM T_VISIT_RECORD_CARD
            WHERE RECORD_ID = T_VISIT_RECORD.ID
            AND CARD_TIME <![CDATA[ >= ]]> CURDATE()
            AND CARD_TIME <![CDATA[ < ]]> DATE_ADD(CURDATE(), INTERVAL 1 DAY)
            AND VISIT_USER_ID = T_VISIT_RECORD.VISITOR_ID
        )
        ORDER BY ADD_TIME DESC
    </select>

    <select id="selectVisitRecordListForTodayTongXingNotCard"   resultType="com.vedeng.crm.visitrecord.domain.vo.VisitTongXingNotCardVo">
        select
            T_VISIT_RECORD.ID AS "id",
            T_VISIT_RECORD.VISIT_RECORD_NO AS "visitNo",
            T_VISIT_RECORD_TONGXING_USER.TONGXING_USER_ID as "tongXingUserId"
        FROM T_VISIT_RECORD
        JOIN T_VISIT_RECORD_TONGXING_USER ON T_VISIT_RECORD.ID = T_VISIT_RECORD_TONGXING_USER.RECORD_ID
        WHERE T_VISIT_RECORD.IS_DELETE=0
            AND VISIT_RECORD_STATUS IN (1,2)
            AND PLAN_VISIT_DATE = CURDATE()
            AND NOT EXISTS (
                SELECT 1
                FROM T_VISIT_RECORD_CARD
                WHERE RECORD_ID = T_VISIT_RECORD.ID
                AND CARD_TIME <![CDATA[ >= ]]> CURDATE()
                AND CARD_TIME <![CDATA[ < ]]> DATE_ADD(CURDATE(), INTERVAL 1 DAY)
                AND VISIT_USER_ID = T_VISIT_RECORD_TONGXING_USER.TONGXING_USER_ID
            )
        ORDER BY T_VISIT_RECORD.ADD_TIME DESC
    </select>



    <select id="sendYestodayNotRecordReminder"   resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM T_VISIT_RECORD
        WHERE IS_DELETE=0
        AND VISIT_RECORD_STATUS IN (2)
        AND (
        SELECT DATE(MAX(CARD_TIME))
        FROM T_VISIT_RECORD_CARD
        WHERE VISIT_USER_ID = T_VISIT_RECORD.VISITOR_ID
            AND RECORD_ID = T_VISIT_RECORD.ID
        ) = CURDATE() - INTERVAL 1 DAY
        ORDER BY ADD_TIME DESC
    </select>

    <select id="selectVisitRecordById" resultType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo">
        select
        <include refid="Base_Column_List"/>
        FROM T_VISIT_RECORD
        WHERE IS_DELETE=0
        AND ID = #{id}
    </select>


    <insert id="insert" parameterType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO T_VISIT_RECORD (
        VISIT_RECORD_NO,
        PLAN_VISIT_DATE,
        VISITOR_ID,
        VISITOR_NAME,
        VISIT_TARGET,
        PROVINCE_CODE,
        PROVINCE_NAME,
        CITY_CODE,
        CITY_NAME,
        AREA_CODE,
        AREA_NAME,
        VISIT_ADDRESS,
        CUSTOMER_NAME,
        CUSTOMER_FROM,
        CUSTOMER_NATURE,
        TRADER_ID,
        TRADER_CUSTOMER_ID,
        ACTUAL_VISIT_DATE,
        CARD_OFF,
        CARD_TIME,
        PICTURE_LIST,
        CONTACT_NAME,
        CONTACT_MOBILE,
        CONTACT_TELE,
        CONTACT_POSITION,
        OTHER_CONTACT,
        SHOW_PPT,
        INVITE_REG,
        REG_MOBILE,
        TRADER_CONTRACT_ID,
        COMMUCATE_CONTENT,
        NEXT_VISIT_DATE,
        CREATE_BUSINESS_CHANGE,
        VISIT_SUCCESS,
        ADD_TIME,
        ADD_USER_ID,
        MOD_TIME,
        MOD_USER_ID,
        IS_DELETE,
        VISIT_RECORD_STATUS,
        CLOSE_REASON_TYPE,
        CLOSE_REASON_CONTENT,
        NO_CONTRACT,
        RELATE_TYPE,
        BUSSINESS_CHANCE_ID,
        BUSSINESS_CHANCE_NO,
        COMPLETE_DATETIME,
        CLOSE_DATETIME,
        REMARK,
        CUSTOMER_REQUIRES,
        ORG_ID,
        ORG_GROUP
        ) VALUES (
        #{visitRecordNo},
        #{planVisitDate},
        #{visitorId},
        #{visitorName},
        #{visitTarget},
        #{provinceCode},
        #{provinceName},
        #{cityCode},
        #{cityName},
        #{areaCode},
        #{areaName},
        #{visitAddress},
        #{customerName},
        #{customerFrom},
        #{customerNature},
        #{traderId},
        #{traderCustomerId},
        #{actualVisitDate},
        #{cardOff},
        #{cardTime},
        #{pictureList},
        #{contactName},
        #{contactMobile},
        #{contactTele},
        #{contactPosition},
        #{otherContact},
        #{showPpt},
        #{inviteReg},
        #{regMobile},
        #{traderContractId},
        #{commucateContent},
        #{nextVisitDate},
        #{createBusinessChange},
        #{visitSuccess},
        NOW(),
        #{addUserId},
        NOW(),
        #{addUserId},
        0,
        #{visitRecordStatus},
        #{closeReasonType},
        #{closeReasonContent},
        #{noContract},
        #{relateType},
        #{bussinessChanceId},
        #{bussinessChanceNo},
        #{completeDatetime},
        #{closeDatetime},
        #{remark},
        #{customerRequires},
        #{orgId},
        #{orgGroup}
        )
    </insert>

    <update id="update" parameterType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo">
        UPDATE T_VISIT_RECORD
        <set>
            <if test="planVisitDate != null">
                PLAN_VISIT_DATE = #{planVisitDate},
            </if>
            <if test="visitorId != null">
                VISITOR_ID = #{visitorId},
            </if>
            <if test="visitorName != null">
                VISITOR_NAME = #{visitorName},
            </if>
            <if test="visitTarget != null">
                VISIT_TARGET = #{visitTarget},
            </if>
            <if test="provinceCode != null">
                PROVINCE_CODE = #{provinceCode},
            </if>
            <if test="provinceName != null">
                PROVINCE_NAME = #{provinceName},
            </if>
            <if test="cityCode != null">
                CITY_CODE = #{cityCode},
            </if>
            <if test="cityName != null">
                CITY_NAME = #{cityName},
            </if>
            <if test="areaCode != null">
                AREA_CODE = #{areaCode},
            </if>
            <if test="areaName != null">
                AREA_NAME = #{areaName},
            </if>
            <if test="visitAddress != null">
                VISIT_ADDRESS = #{visitAddress},
            </if>
            <if test="customerName != null">
                CUSTOMER_NAME = #{customerName},
            </if>
            <if test="customerFrom != null">
                CUSTOMER_FROM = #{customerFrom},
            </if>
            <if test="customerNature != null">
                CUSTOMER_NATURE = #{customerNature},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId},
            </if>
            <if test="traderCustomerId != null">
                TRADER_CUSTOMER_ID = #{traderCustomerId},
            </if>
            <if test="actualVisitDate != null">
                ACTUAL_VISIT_DATE = #{actualVisitDate},
            </if>
            <if test="cardOff != null">
                CARD_OFF = #{cardOff},
            </if>
            <if test="cardTime != null">
                CARD_TIME = #{cardTime},
            </if>
            <if test="pictureList != null">
                PICTURE_LIST = #{pictureList},
            </if>
            <if test="contactName != null">
                CONTACT_NAME = #{contactName},
            </if>
            <if test="contactMobile != null">
                CONTACT_MOBILE = #{contactMobile},
            </if>
            <if test="contactTele != null">
                CONTACT_TELE = #{contactTele},
            </if>
            <if test="contactPosition != null">
                CONTACT_POSITION = #{contactPosition},
            </if>
            <if test="otherContact != null">
                OTHER_CONTACT = #{otherContact},
            </if>
            <if test="showPpt != null">
                SHOW_PPT = #{showPpt},
            </if>
            <if test="inviteReg != null">
                INVITE_REG = #{inviteReg},
            </if>
            <if test="regMobile != null">
                REG_MOBILE = #{regMobile},
            </if>
            <if test="traderContractId != null">
                TRADER_CONTRACT_ID = #{traderContractId},
            </if>
            <if test="commucateContent != null">
                COMMUCATE_CONTENT = #{commucateContent},
            </if>
            <if test="nextVisitDate != null">
                NEXT_VISIT_DATE = #{nextVisitDate},
            </if>
            <if test="createBusinessChange != null">
                CREATE_BUSINESS_CHANGE = #{createBusinessChange},
            </if>
            <if test="visitSuccess != null">
                VISIT_SUCCESS = #{visitSuccess},
            </if>
            <if test="visitRecordStatus != null">
                VISIT_RECORD_STATUS = #{visitRecordStatus},
            </if>
            <if test="closeReasonType != null">
                CLOSE_REASON_TYPE = #{closeReasonType},
            </if>
            <if test="closeReasonContent != null">
                CLOSE_REASON_CONTENT = #{closeReasonContent},
            </if>
            <if test="noContract != null">
                NO_CONTRACT = #{noContract},
            </if>
            <if test="relateType != null">
                RELATE_TYPE = #{relateType},
            </if>
            <if test="bussinessChanceId != null">
                BUSSINESS_CHANCE_ID = #{bussinessChanceId},
            </if>
            <if test="bussinessChanceNo != null">
                BUSSINESS_CHANCE_NO = #{bussinessChanceNo},
            </if>
            <if test="completeDatetime != null">
                COMPLETE_DATETIME = #{completeDatetime},
            </if>
            <if test="closeDatetime != null">
                CLOSE_DATETIME = #{closeDatetime},
            </if>
            <if test="remark != null">
                REMARK = #{remark},
            </if>
            <if test="customerRequires != null">
                CUSTOMER_REQUIRES = #{customerRequires},
            </if>
            <if test="orgId != null">
                ORG_ID = #{orgId},
            </if>
            <if test="orgGroup != null">
                ORG_GROUP = #{orgGroup},
            </if>
            MOD_TIME = NOW(),
            MOD_USER_ID = #{modUserId}
        </set>
        WHERE ID = #{id}
    </update>

    <update id="updateSelective" parameterType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo">
        UPDATE T_VISIT_RECORD
        <set>
            RECORD_CONTACT_NAME = #{recordContactName},
            RECORD_CONTACT_MOBILE = #{recordContactMobile},
            RECORD_CONTACT_TELE = #{recordContactTele},
            RECORD_CONTACT_POSITION = #{recordContactPosition},
            RECORD_OTHER_CONTACT = #{recordOtherContact},
            RECORD_NO_CONTRACT = #{recordNoContract},
            <if test="showPpt != null">
                SHOW_PPT = #{showPpt,jdbcType=VARCHAR},
            </if>
            <if test="customerRequires != null">
                COMMUCATE_CONTENT = #{customerRequires,jdbcType=VARCHAR},
            </if>
            <if test="visitRecordStatus != null">
                VISIT_RECORD_STATUS = #{visitRecordStatus,jdbcType=INTEGER},
            </if>
            COMPLETE_DATETIME = NOW(),
            MOD_TIME = NOW(),
            MOD_USER_ID = #{modUserId,jdbcType=INTEGER}
        </set>
        WHERE ID = #{id}
        AND IS_DELETE = 0
    </update>

    <update id="updateClose" parameterType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo">
        UPDATE T_VISIT_RECORD
        <set>
            VISIT_RECORD_STATUS = 4,
            <if test="closeReasonType != null">
                CLOSE_REASON_TYPE = #{closeReasonType},
            </if>
            <if test="closeReasonContent != null">
                CLOSE_REASON_CONTENT = #{closeReasonContent,jdbcType=VARCHAR},
            </if>
            CLOSE_DATETIME = NOW(),
            MOD_TIME = NOW(),
            MOD_USER_ID = #{modUserId}
        </set>
        WHERE ID = #{id}
        AND IS_DELETE = 0
    </update>

    <select id="selectVisitRecordByRelateNo" resultType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo">
        select
        <include refid="Base_Column_List"/>
        FROM T_VISIT_RECORD
        WHERE IS_DELETE=0
        AND BUSSINESS_CHANCE_NO = #{relateNo,jdbcType=VARCHAR}
        AND RELATE_TYPE = #{relateType,jdbcType=INTEGER}
    </select>

    <select id="selectVisitRecordByRelateId" resultType="com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo">
        select
        <include refid="Base_Column_List"/>
        FROM T_VISIT_RECORD
        WHERE IS_DELETE=0
        AND BUSSINESS_CHANCE_ID = #{relateId,jdbcType=INTEGER}
        AND RELATE_TYPE = #{relateType,jdbcType=INTEGER}
    </select>


</mapper> 