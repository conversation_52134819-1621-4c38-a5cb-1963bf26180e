package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶供应商dto  由erp实际业务转换
 * @date 2022/11-11 10:18
 */
@Getter
@Setter
@NoArgsConstructor
public class KingDeeSupplierBankDto{

    //帐号
    private String fBankCode;
    //帐户名称
    private String fBankHolder;
    //开户银行
    private String fOpenBankName;
    //联行号
    private String fCNAPS;
}
