package com.vedeng.erp.broadcast.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.broadcast.domain.dto.BroadcastTargetImportResultDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastTargetQueryDto;
import com.vedeng.erp.broadcast.domain.dto.BroadcastTargetRespDto;
import com.vedeng.erp.broadcast.service.BroadcastTargetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 业绩目标管理API控制器
 */
@ExceptionController
@RestController
@RequestMapping("/broadcast/target")
@Slf4j
public class BroadcastTargetApi {

    @Autowired
    private BroadcastTargetService broadcastTargetService;

    /**
     * 导入Excel文件
     * 支持覆盖式导入（相同年度+目标类型的数据会被覆盖）
     *
     * @param file Excel文件，包含3个Sheet：个人目标、小组目标、部门目标
     * @return 导入结果
     */
    @RequestMapping("/import")
    public R<BroadcastTargetImportResultDto> importExcel(@RequestParam("file") MultipartFile file) {
        try {
            BroadcastTargetImportResultDto result = broadcastTargetService.importExcel(file);
            return R.success(result);
        } catch (IOException e) {
            log.error("导入Excel文件失败", e);
            return R.error("导入失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("导入Excel文件异常", e);
            return R.error("导入异常：" + e.getMessage());
        }
    }

    /**
     * 下载Excel导入模板
     * 模板中自动填充当前年度和部门/小组信息
     *
     * @param response HTTP响应
     */
    @RequestMapping("/template/download")
    @NoNeedAccessAuthorization
    public void downloadTemplate(HttpServletResponse response) {
        try {
            broadcastTargetService.downloadTemplate(response);
        } catch (IOException e) {
            log.error("下载模板失败", e);
            // 异常处理已在Service中完成
        }
    }

    /**
     * 查询业绩目标
     * 查询所有有效的广播目标数据（IS_DELETED=0），根据targetType字段对查询结果进行分组
     *
     * @param queryDto 查询条件
     * @return 按targetType分组的目标数据，格式为 Map<String, List<BroadcastTargetRespDto>>
     *         其中key为目标类型标识（person、group、department），value为对应的目标列表
     */
    @RequestMapping("/query")
    @NoNeedAccessAuthorization
    public R<Map<String, List<BroadcastTargetRespDto>>> queryTargets(@RequestBody BroadcastTargetQueryDto queryDto) {
        try {
            log.info("开始查询业绩目标，查询条件：{}", queryDto);
            Map<String, List<BroadcastTargetRespDto>> result = broadcastTargetService.queryTargets(queryDto);
            log.info("查询业绩目标成功，返回{}个分组", result.size());
            return R.success(result);
        } catch (Exception e) {
            log.error("查询业绩目标失败，查询条件：{}", queryDto, e);
            return R.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取有数据的年度列表
     * 用于筛选条件
     *
     * @return 年度列表
     */
    @RequestMapping("/years")
    @NoNeedAccessAuthorization
    public R<List<Integer>> getAvailableYears() {
        try {
            List<Integer> result = broadcastTargetService.getAvailableYears();
            return R.success(result);
        } catch (Exception e) {
            log.error("获取有数据年度失败", e);
            return R.error("获取失败：" + e.getMessage());
        }
    }
}
