package com.vedeng.dwh.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.dwh.externaldb.dao.DwhErpUserInfoMapper;
import com.vedeng.dwh.externaldb.dao.DwhOrganizationInfoMapper;
import com.vedeng.dwh.model.dto.DwhErpOrganizationDto;
import com.vedeng.dwh.model.dto.DwhErpSubDeptDto;
import com.vedeng.dwh.model.dto.DwhErpUserDto;
import com.vedeng.dwh.model.dto.DwhQueryParamsDto;
import com.vedeng.dwh.service.DwhQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * dwh库通用查询工具实现.
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/26 1:42 下午.
 * @author: Tomcat.Hui.
 */
@Service
@Slf4j
public class DwhQueryServiceImpl implements DwhQueryService {

    @Resource
    private DwhErpUserInfoMapper userInfoMapper;

    @Resource
    private DwhOrganizationInfoMapper organizationInfoMapper;

    @Override
    public List<DwhErpOrganizationDto> getAllDeptInfo(Timestamp startTime, Timestamp endTime) {
        DwhQueryParamsDto query = new DwhQueryParamsDto();
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        return organizationInfoMapper.getAllErpDepartments(query);
    }

    @Override
    public Map<DwhErpSubDeptDto,Map<DwhErpSubDeptDto,List<DwhErpSubDeptDto>>> getFoldDeptsUsers(Timestamp startTime,
                                                                                                Timestamp endTime){
        List<DwhErpOrganizationDto> orgs = getAllDeptInfo(startTime, endTime);
        log.info("getAllDeptInfo返回值：{}", JSONObject.toJSONString(orgs));
//        back
//        Map<DwhErpSubDeptDto,Map<DwhErpSubDeptDto,List<DwhErpSubDeptDto>>> result =
//                orgs.stream().collect(groupingBy(o -> new DwhErpSubDeptDto(o.getL1Id(),o.getL1Name()),
//                groupingBy(p -> new DwhErpSubDeptDto(p.getL2Id(),p.getL2Name()),
//                        collectingAndThen(Collectors.toList(),c -> c.stream()
//                                .map(q -> new DwhErpSubDeptDto(q.getL3Id(),q.getL3Name(),q.getSubUsers()))
//                                .collect(toList())))));


// changed by Randy.Xu   .Desc: begin
        Map<DwhErpSubDeptDto,Map<DwhErpSubDeptDto,List<DwhErpSubDeptDto>>> result =
                orgs.stream().collect(groupingBy(o -> new DwhErpSubDeptDto(o.getL1Id(),o.getL1Name()),
                        groupingBy(p -> new DwhErpSubDeptDto(p.getL2Id(),p.getL2Name()),
                                collectingAndThen(Collectors.toList(),c -> c.stream()
                                        .map(q ->{
                                            if(q.getL3Id()==null){
                                               return new DwhErpSubDeptDto(q.getL2Id(),q.getL2Name(),q.getSubUsers());
                                            }
                                            return  new DwhErpSubDeptDto(q.getL3Id(),q.getL3Name(),q.getSubUsers());
                                            })
                                        .collect(toList())))));
// changed by Randy.Xu   .Desc: end


        //排除ID为空的map、list
        Map<DwhErpSubDeptDto,Map<DwhErpSubDeptDto,List<DwhErpSubDeptDto>>> collect = result.entrySet().stream().collect(toMap(p -> p.getKey(),
                p -> p.getValue().entrySet().stream().filter(q -> q.getKey().getDepartId() != null)
                        .collect(toMap(r -> r.getKey(),
                                r -> r.getValue().stream().filter(s -> s.getDepartId() != null).collect(toList()),
                                (a, b) -> a, HashMap::new))));
        log.info("获取全量部门的快照配置(包含销售信息): {}", JSONObject.toJSONString(collect));
        return collect;
    }

    @Override
    public DwhErpUserDto getUserDeptInfo(Integer userId,Timestamp startTime, Timestamp endTime) {
        DwhQueryParamsDto query = new DwhQueryParamsDto();
        query.setUserId(userId);
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        return userInfoMapper.getErpUserSnapshotInfo(query);
    }

    @Override
    public List<DwhErpOrganizationDto> getBaseOrganizationInfo(Timestamp startTime, Timestamp endTime) {
        DwhQueryParamsDto query = new DwhQueryParamsDto();
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        return organizationInfoMapper.getOrgBaseInfo(query);
    }

    @Override
    public List<DwhErpUserDto> getBaseUserInfo(Timestamp startTime, Timestamp endTime) {
        DwhQueryParamsDto query = new DwhQueryParamsDto();
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        return userInfoMapper.getErpUserSnapshotInfoList(query);
    }

}
