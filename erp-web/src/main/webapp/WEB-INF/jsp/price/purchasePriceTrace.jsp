<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="https://cdn.staticfile.org/echarts/4.3.0/echarts.min.js"></script>
</head>

<body>
    采购成本变动轨迹
    <br>
    订货号:${skuInfo.skuNo} &nbsp;&nbsp;&nbsp; 商品名称：${skuInfo.skuName}
    <br>
    <br>
    <div id="box" style="width: 1000px;height:800px;"></div>
</body>
<script>

    function color16(){//十六进制颜色随机
        var r = Math.floor(Math.random()*256);
        var g = Math.floor(Math.random()*256);
        var b = Math.floor(Math.random()*256);
        var color = '#'+r.toString(16)+g.toString(16)+b.toString(16);
        return color;
    }

    // 获取到这个DOM节点，然后初始化

    var myChart = echarts.init(document.getElementById("box"));

    var changeReasonMap = new Map();
    // option 里面的内容基本涵盖你要画的图表的所有内容

    var option = {

        backgroundColor: '#FBFBFB',

        tooltip : {
            trigger: 'axis',
            formatter: function (params) {

                var html = params[0].name+"<br>";
                for(var i = 0;i< params.length ;i++){
                    html+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:"+params[i].color+""></span>'

                    var changeReason = changeReasonMap.get(params[i].seriesName + params[i].value[0] + params[i].value[1]);

                    html+= params[i].seriesName+":"+params[i].value[1];

                    if(changeReason != ''){
                        html +="   变动原因:"+changeReason+"<br>";
                    }
                }
                return html;
            }
        },

        legend : {
            data:[]
        },

        calculable : true,

        xAxis : [
            {
                axisLabel:{
                    rotate: 30,
                    interval:0
                },
                axisLine:{
                    lineStyle :{
                        color: '#CECECE'
                    }
                },
                type : 'category',
                boundaryGap : false,
                data : []
            }
        ],
        yAxis : [
            {

                type : 'value',
                axisLine:{
                    lineStyle :{
                        color: '#CECECE'
                    }
                }
            }
        ],
        series : [

        ]
        // 定义样式和数据
    }


    var legends = [];
    var series = [];
    var xdata = [];

    <c:forEach var="purchaseHistory" items="${purchaseHistoryListDto}">

        legends.push('${purchaseHistory.supplyName}');

        var item = {
            name:'${purchaseHistory.supplyName}',
            type:'line',
            symbol:'none',
            smooth: 0.2,
            color:[],
            data:[]
        }

        <c:forEach var="history" items="${purchaseHistory.historyList}">

            var dataItem = ['${history.addTime}','${myfn:toString(history.purchasePrice)}'];
            item.data.push(dataItem);
            item.color.push(color16());
            changeReasonMap.set('${purchaseHistory.supplyName}'+'${history.addTime}'+'${myfn:toString(history.purchasePrice)}','${history.changeReason}');

        </c:forEach>

        series.push(item);

    </c:forEach>


    option.legend.data = legends;
    option.series = series;

    <c:forEach var="addTime" items="${addTimeList}">
        xdata.push('${addTime}');
    </c:forEach>

    option.xAxis[0].data = xdata;

    myChart.setOption(option);
</script>

</html>
