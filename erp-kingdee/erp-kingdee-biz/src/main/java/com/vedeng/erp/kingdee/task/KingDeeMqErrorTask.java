package com.vedeng.erp.kingdee.task;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeEventMsgService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶重试异常微信告警
 * @date 2022/8/29 13:12
 */
@JobHandler(value = "KingDeeMqErrorTask")
@Component
@Slf4j
public class KingDeeMqErrorTask extends AbstractJobHandler {


    @Autowired
    private KingDeeEventMsgService kingDeeEventMsgService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception  {
        List<KingDeeEventMsgDto> noConsumeMsg = kingDeeEventMsgService.getNeedSendMsg();
        Map<String, List<KingDeeEventMsgDto>> map = noConsumeMsg.stream().collect(Collectors.groupingBy(KingDeeEventMsgDto::getEventType));
        map.forEach((k, v) -> {
            v.sort(Comparator.comparing(KingDeeEventMsgDto::getMsgOrder));
            v.forEach(msgDto -> {
                kingDeeEventMsgService.sendMsg(msgDto);
            });
        });
        return ReturnT.SUCCESS;
    }


}
