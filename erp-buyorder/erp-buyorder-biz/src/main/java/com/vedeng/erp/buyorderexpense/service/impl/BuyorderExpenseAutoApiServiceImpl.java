package com.vedeng.erp.buyorderexpense.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity;
import com.vedeng.erp.aftersale.dto.AfterSaleGoods2ExpenseDto;
import com.vedeng.erp.aftersale.dto.AfterSaleOrder2ExpenseDto;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.aftersale.mapper.ExpenseAfterSalesStatusMapper;
import com.vedeng.erp.aftersale.service.AfterSaleOrder2ExpenseApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesAutoApiService;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorder.service.BuyorderExpenseAutoApiService;
import com.vedeng.erp.buyorderexpense.common.constant.BuyorderExpenseConstant;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseDetailMapper;
import com.vedeng.erp.buyorderexpense.mapper.RBuyorderExpenseJSaleorderMapper;
import com.vedeng.erp.buyorderexpense.service.BuyorderExpenseService;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 自动转单
 * @date 2023/1/12 13:06
 **/
@Service
@Slf4j
public class BuyorderExpenseAutoApiServiceImpl implements BuyorderExpenseAutoApiService {


    @Autowired
    private ExpenseAfterSalesAutoApiService expenseAfterSalesAutoApiService;

    @Autowired
    private ExpenseAfterSalesStatusMapper expenseAfterSalesStatusMapper;

    @Autowired
    private BuyorderExpenseService buyorderExpenseService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private AfterSaleOrder2ExpenseApiService afterSaleOrder2ExpenseApiService;

    @Autowired
    private RBuyorderExpenseJSaleorderMapper rBuyorderExpenseJSaleorderMapper;

    @Autowired
    private PayApplyApiService payApplyApiService;

    @Autowired
    private CapitalBillApiService capitalBillApiService;

    @Autowired
    private InvoiceApiService invoiceApiService;

    @Autowired
    private BuyorderExpenseDetailMapper buyorderExpenseDetailMapper;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public List<BuyorderExpenseDto> autoBuyorderExpense(Integer afterSalesId,List<Integer> warnIds) {

        log.info("autoBuyorderExpense 自动转单入参：{},{}", JSON.toJSONString(afterSalesId),JSON.toJSONString(warnIds));
        Assert.notNull(afterSalesId, "销售售后单不可为空");

        // 售后
        List<ExpenseAfterSalesDto> expenseAfterSalesDtos = expenseAfterSalesAutoApiService.bindingAutoExpenseAfterSales(afterSalesId,warnIds);
        // 新费用单
        List<BuyorderExpenseDto> buyorderExpenseDtos = this.bindingAutoExpenseAfterSales(afterSalesId,warnIds);


        if (CollUtil.isNotEmpty(expenseAfterSalesDtos)) {

            List<Long> longs = expenseAfterSalesAutoApiService.autoAddExpenseAfterSales(expenseAfterSalesDtos);
            // 流水 票
            // 1 执行退款运算 反向更新新的费用单的预付金额和账期金额 2 售后单流水的添加
            expenseAfterSalesAutoApiService.autoExecuteRefundOperation(longs);
            // 3 蓝票执行虚拟冲销
            expenseAfterSalesAutoApiService.autoReversalInvoices(longs);

        }

        // 保存
        if (CollUtil.isNotEmpty(buyorderExpenseDtos)) {
            // 赋值
            buyorderExpenseDtos.forEach(x->{
                Integer originalBuyorderExpenseId = x.getOriginalBuyorderExpenseId();
                List<Long> collect = expenseAfterSalesDtos.stream().filter(c -> c.getBuyorderExpenseId().equals(originalBuyorderExpenseId)).map(ExpenseAfterSalesDto::getExpenseAfterSalesId).collect(Collectors.toList());
                if (CollUtil.isEmpty(collect)) {
                    return;
                }
                List<ExpenseAfterSalesStatusEntity> byExpenseAfterSalesIdIn = expenseAfterSalesStatusMapper.findByExpenseAfterSalesIdIn(collect);
                BigDecimal repaymentPeriod = byExpenseAfterSalesIdIn.stream().map(ExpenseAfterSalesStatusEntity::getRepaymentPeriod).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal needReturnAmount = byExpenseAfterSalesIdIn.stream().map(ExpenseAfterSalesStatusEntity::getNeedReturnAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                BuyorderExpenseDetailDto buyorderExpenseDetailDto = x.getBuyorderExpenseDetailDto();
                buyorderExpenseDetailDto.setPrepaidAmount(needReturnAmount);
                buyorderExpenseDetailDto.setAccountPeriodAmount(repaymentPeriod);

            });
            BuyorderExpenseAutoApiServiceImpl bean = SpringUtil.getBean(BuyorderExpenseAutoApiServiceImpl.class);
            bean.autoSave(buyorderExpenseDtos);
            // 新的费用单 进行 流水添加 票的转移 付款申请 付款流水
            buyorderExpenseDtos.forEach(b->{
                bean.addPayApplyAndBill(b);
                bean.addPeriodAmountBill(b);
                bean.addBlueInvoice(b);
            });

            // 订单说明
            buyorderExpenseDtos.forEach(x->{
                Optional<List<OrderRemarkDto>> first = expenseAfterSalesDtos.stream().filter(cc -> cc.getBuyorderExpenseId().equals(x.getOriginalBuyorderExpenseId())).map(ExpenseAfterSalesDto::getOrderDesc).findFirst();
                if (first.isPresent()) {
                    List<OrderRemarkDto> orderRemarkDtos = first.get();
                    // 原始单 详细说明
                    OrderRemarkDto orderRemarkDto = new OrderRemarkDto();
                    OriginalOrderDto originalOrderDto = orderRemarkDtos.get(0).getOriginalOrderDto();
                    ResultOrderDto resultOrderDto = new ResultOrderDto();
                    resultOrderDto.setOrderId(x.getBuyorderExpenseId());
                    resultOrderDto.setNo(x.getBuyorderExpenseNo());
                    orderRemarkDto.setOriginalOrderDto(originalOrderDto);
                    orderRemarkDto.setResultOrderDto(resultOrderDto);
                    orderRemarkDto.setType(2);

                    BuyorderExpenseDetailEntity updateOld = new BuyorderExpenseDetailEntity();
                    updateOld.setBuyorderExpenseId(x.getOriginalBuyorderExpenseId());
                    updateOld.setOrderDesc(JSONArray.parseArray(JSON.toJSONString(CollUtil.newArrayList(orderRemarkDto))));
                    buyorderExpenseDetailMapper.updateOrderDescByBuyorderExpenseId(updateOld);

                    // 新费用单详细说明
                    orderRemarkDto.setType(3);
                    resultOrderDto.setOrderId(x.getOriginalBuyorderExpenseId());
                    resultOrderDto.setNo(x.getOriginalBuyorderExpenseNo());

                    BuyorderExpenseDetailEntity updateNew = new BuyorderExpenseDetailEntity();
                    updateNew.setBuyorderExpenseId(x.getBuyorderExpenseId());
                    updateNew.setOrderDesc(JSONArray.parseArray(JSON.toJSONString(CollUtil.newArrayList(orderRemarkDto))));
                    buyorderExpenseDetailMapper.updateOrderDescByBuyorderExpenseId(updateNew);
                }

            });

        }

        return buyorderExpenseDtos;

    }

    /**
     * 挂票 蓝票 有效以及作废
     * @param buyorderExpenseDto
     */
    public void addBlueInvoice(BuyorderExpenseDto buyorderExpenseDto) {

        List<InvoiceDto> blueInvoiceByExpenseId = invoiceApiService.getBlueInvoiceByExpenseId(buyorderExpenseDto.getOriginalBuyorderExpenseId());
        log.info("查询到票的数据：{}",JSON.toJSONString(blueInvoiceByExpenseId));
        if (CollUtil.isEmpty(blueInvoiceByExpenseId)) {
            return;
        }
        List<InvoiceDto> real = new ArrayList<>();
        List<InvoiceDto> enable = blueInvoiceByExpenseId.stream().filter(x -> x.getIsEnable().equals(1)).collect(Collectors.toList());
        List<InvoiceDto> unEnable = blueInvoiceByExpenseId.stream().filter(x -> x.getIsEnable().equals(0)).collect(Collectors.toList());
        if (CollUtil.isEmpty(unEnable)) {
            real = enable;
        } else {
            real = enable.stream().filter(x -> {
                Optional<InvoiceDto> any = unEnable.stream().filter(a -> Objects.nonNull(a.getInvoiceNo())&&Objects.nonNull(a.getInvoiceCode())&&a.getInvoiceNo().equals(x.getInvoiceNo()) && a.getInvoiceCode().equals(x.getInvoiceCode())).findAny();
                return !any.isPresent();
            }).collect(Collectors.toList());
        }

        Map<String, List<BuyorderExpenseItemDto>> collect = buyorderExpenseDto.getBuyorderExpenseItemDtos().stream().collect(Collectors.groupingBy(x -> x.getBuyorderExpenseItemDetailDto().getSku()));
        real.forEach(c->{
            c.setInvoiceId(null);
            c.setValidTime(System.currentTimeMillis());
            c.setRelatedId(buyorderExpenseDto.getBuyorderExpenseId());
            c.setCreator(BuyorderExpenseConstant.NJADMIN_ID);
            c.setValidUserid(BuyorderExpenseConstant.NJADMIN_ID);
            c.setValidComments("自动转单费用单售后审核通过");
            List<InvoiceDetailDto> invoiceDetailDtos = c.getInvoiceDetailDtos();
            if (CollUtil.isNotEmpty(invoiceDetailDtos)) {
                invoiceDetailDtos.forEach(x->{
                    x.setInvoiceId(null);
                    List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = collect.get(x.getSku());
                    if (CollUtil.isNotEmpty(buyorderExpenseItemDtos)) {
                        Integer buyorderExpenseItemId = buyorderExpenseItemDtos.get(0).getBuyorderExpenseItemId();
                        x.setDetailgoodsId(buyorderExpenseItemId);
                    }
                });
            }
        });
        log.info("自动转单新费用单绑定蓝票：{}",JSON.toJSONString(blueInvoiceByExpenseId));
        invoiceApiService.saveBlueInvoice(blueInvoiceByExpenseId);

    }

    /**
     * 账期支付以及 账期流水
     * @param buyorderExpenseDto
     */
    @Transactional(rollbackFor = Throwable.class)
    public void addPeriodAmountBill(BuyorderExpenseDto buyorderExpenseDto) {
        // 排除账期支付为0的
        if (buyorderExpenseDto.getBuyorderExpenseDetailDto().getAccountPeriodAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        long timeMillis = System.currentTimeMillis();

        CapitalBillDto capitalBillDto = CapitalBillDto.builder()
                .creator(BuyorderExpenseConstant.NJADMIN_ID)
                .creatorName(BuyorderExpenseConstant.NJADMIN_NAME)
                .amount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getAccountPeriodAmount())
                .payee(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderName())
                .payer(BuyorderExpenseConstant.VEDENG)
                .traderType(3)
                .traderTime(timeMillis)
                .traderMode(527)
                .comments(BuyorderExpenseConstant.PAY_COMMENT)
                .currencyUnitId(1)
                .build();

        CapitalBillDetailDto capitalBillDetailDto = CapitalBillDetailDto.builder()
                .orderType(4)
                .orderNo(buyorderExpenseDto.getBuyorderNo())
                .relatedId(buyorderExpenseDto.getBuyorderExpenseId())
                .traderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId())
                .traderType(2)
                .bussinessType(525)
                .amount(capitalBillDto.getAmount())
                .orgId(0)
                .userId(BuyorderExpenseConstant.NJADMIN_ID)
                .build();

        log.info("自动转单账期支付添加流水：{},{}",JSON.toJSONString(capitalBillDto),JSON.toJSONString(capitalBillDetailDto));
        capitalBillApiService.insertCapitalBill(capitalBillDto);
        capitalBillDetailDto.setCapitalBillId(capitalBillDto.getCapitalBillId());
        String no = generatorNum(capitalBillDto.getCapitalBillId());
        CapitalBillDto update = new CapitalBillDto();
        update.setCapitalBillId(capitalBillDto.getCapitalBillId());
        update.setCapitalBillNo(no);
        capitalBillApiService.updateCapitalBillNo(update);
        capitalBillApiService.insertCapitalBillDetail(CollUtil.newArrayList(capitalBillDetailDto));
    }

    /**
     * 付款申请 以及余额流水
     * @param buyorderExpenseDto
     */
    @Transactional(rollbackFor = Throwable.class)
    public void addPayApplyAndBill(BuyorderExpenseDto buyorderExpenseDto) {

        log.info("计算自动转单的余额支付流水：{}",JSON.toJSONString(buyorderExpenseDto));
        // 排除没有预付款的
        if (buyorderExpenseDto.getBuyorderExpenseDetailDto().getPrepaidAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        long timeMillis = System.currentTimeMillis();
        PayApplyDto build = PayApplyDto.builder()
                .amount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getPrepaidAmount()).payType(4125).validStatus(1)
                .relatedId(buyorderExpenseDto.getBuyorderExpenseId()).traderSubject(1).traderMode(528)
                .traderName(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderName())
                .companyId(1).currencyUnitId(1).isBill(1).payStatus(1).creator(2)
                .creatorName(BuyorderExpenseConstant.NJADMIN_NAME).updater(2).updaterName(BuyorderExpenseConstant.NJADMIN_NAME)
                .addTime(timeMillis)
                .comments(BuyorderExpenseConstant.PAY_COMMENT)
                .build();

        List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = buyorderExpenseDto.getBuyorderExpenseItemDtos();

        AtomicReference<BigDecimal> bigDecimalAtomicReference = new AtomicReference<>(build.getAmount());
        if (CollUtil.isNotEmpty(buyorderExpenseItemDtos)) {
            List<PayApplyDetailDto> payApplyDetailDtos = new ArrayList<>();
            build.setPayApplyDetailDtos(payApplyDetailDtos);
            for (int i = 0; i < buyorderExpenseItemDtos.size(); i++) {
                BuyorderExpenseItemDto c = buyorderExpenseItemDtos.get(i);
                if (bigDecimalAtomicReference.get().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                BigDecimal result = c.getBuyorderExpenseItemDetailDto().getPrice().multiply(BigDecimal.valueOf(c.getNum()));
                if (result.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                if (result.compareTo(bigDecimalAtomicReference.get()) >= 0) {
                    BigDecimal multiply = c.getBuyorderExpenseItemDetailDto().getPrice().multiply(BigDecimal.valueOf(c.getNum()));
                    PayApplyDetailDto payApplyDetailDto = PayApplyDetailDto.builder()
                            .detailgoodsId(c.getBuyorderExpenseItemId())
                            .num(BigDecimal.valueOf(c.getNum()))
                            .price(c.getBuyorderExpenseItemDetailDto().getPrice())
                            .totalAmount(multiply)
                            .build();
                    payApplyDetailDtos.add(payApplyDetailDto);
                    bigDecimalAtomicReference.accumulateAndGet(multiply, BigDecimal::subtract);
                } else {
                    BigDecimal multiply = bigDecimalAtomicReference.get();
                    PayApplyDetailDto payApplyDetailDto = PayApplyDetailDto.builder()
                            .detailgoodsId(c.getBuyorderExpenseItemId())
                            .num(multiply.divide(c.getBuyorderExpenseItemDetailDto().getPrice(),2,RoundingMode.HALF_UP))
                            .price(c.getBuyorderExpenseItemDetailDto().getPrice())
                            .totalAmount(multiply)
                            .build();
                    payApplyDetailDtos.add(payApplyDetailDto);
                    bigDecimalAtomicReference.accumulateAndGet(multiply, BigDecimal::subtract);
                }
            }
        }

        log.info("自动转单余额支付添加付款申请：{}",JSON.toJSONString(build));
        payApplyApiService.addPayApply(build);

        // 流水
        CapitalBillDto capitalBillDto = CapitalBillDto.builder()
                .companyId(1)
                .traderTime(timeMillis)
                .traderSubject(1)
                .traderType(5)
                .traderMode(528)
                .paymentType(0)
                .amount(build.getAmount())
                .currencyUnitId(1)
                .payer(BuyorderExpenseConstant.VEDENG)
                .addTime(timeMillis)
                .payee(build.getTraderName())
                .comments(BuyorderExpenseConstant.PAY_COMMENT)
                .creator(2)
                .build();

        CapitalBillDetailDto capitalBillDetailDto = CapitalBillDetailDto.builder()
                .bussinessType(525)
                .orderType(4)
                .orderNo(buyorderExpenseDto.getBuyorderExpenseNo())
                .relatedId(buyorderExpenseDto.getBuyorderExpenseId())
                .amount(build.getAmount())
                .traderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId())
                .traderType(2)
                .userId(2)
                .orgId(0)
                .build();

        log.info("自动转单余额支付添加流水：{},{}",JSON.toJSONString(capitalBillDto),JSON.toJSONString(capitalBillDetailDto));
        capitalBillApiService.insertCapitalBill(capitalBillDto);
        capitalBillDetailDto.setCapitalBillId(capitalBillDto.getCapitalBillId());
        String no = generatorNum(capitalBillDto.getCapitalBillId());
        CapitalBillDto update = new CapitalBillDto();
        update.setCapitalBillId(capitalBillDto.getCapitalBillId());
        update.setCapitalBillNo(no);
        capitalBillApiService.updateCapitalBillNo(update);
        capitalBillApiService.insertCapitalBillDetail(CollUtil.newArrayList(capitalBillDetailDto));
    }

    /**
     * 生成资金流水号
     *
     * @param capitalBillId
     * @return
     */
    private String generatorNum(Integer capitalBillId) {
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CAPITAL_NO, NoGeneratorBean.builder().dateFormat(ErpConstant.YYYYMMDDHHMMSS).id(capitalBillId).numberOfDigits(9).build());
        return new BillNumGenerator().distribution(billGeneratorBean);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void autoSave(List<BuyorderExpenseDto> buyorderExpenseDtos) {

        if (CollUtil.isNotEmpty(buyorderExpenseDtos)) {
            buyorderExpenseDtos.forEach(c-> buyorderExpenseApiService.add(c));
        }
    }

    /**
     * 绑定费用售后单
     * @param afterSalesOrderId
     * @return
     */
    public List<BuyorderExpenseDto> bindingAutoExpenseAfterSales(Integer afterSalesOrderId,List<Integer> warnIds) {

        log.info("费用单自动转单 入参：{}", JSON.toJSONString(afterSalesOrderId));

        // 查询销售的售后单
        AfterSaleOrder2ExpenseDto afterSaleOrderExpense = afterSaleOrder2ExpenseApiService.getAfterSaleOrderExpense(afterSalesOrderId);
        List<AfterSaleGoods2ExpenseDto> afterSaleGoods2ExpenseDtos = afterSaleOrderExpense.getAfterSaleGoods2ExpenseDtos();
        if (CollUtil.isEmpty(afterSaleGoods2ExpenseDtos)) {
            log.error("bindingAndAllocationAutoExpenseAfterSales 此销售售后单中未查到虚拟商品信息");
            return Collections.emptyList();
        }

        // 查询原始的被采购的
        List<Integer> orderDetailIds = afterSaleGoods2ExpenseDtos.stream().map(AfterSaleGoods2ExpenseDto::getOrderDetailId).collect(Collectors.toList());
        List<RBuyorderExpenseJSaleorderEntity> rBuyorderExpenseJSaleorderEntities = rBuyorderExpenseJSaleorderMapper.selectBySaleorderGoodsIdIn(orderDetailIds);

        if (CollUtil.isEmpty(rBuyorderExpenseJSaleorderEntities)) {
            return Collections.emptyList();
        }
        List<RBuyorderExpenseJSaleorderEntity> res = rBuyorderExpenseJSaleorderEntities.stream().filter(x -> !warnIds.contains(x.getBuyorderExpenseId())).collect(Collectors.toList());
        Map<Integer, List<RBuyorderExpenseJSaleorderEntity>> rBuyorderExpenseJSaleorderEntities2Map = res.stream().collect(Collectors.groupingBy(RBuyorderExpenseJSaleorderEntity::getBuyorderExpenseId));

        List<BuyorderExpenseDto> result = new ArrayList<>();
        // 查询费用单数据
        rBuyorderExpenseJSaleorderEntities2Map.forEach((k, v) ->{
            boolean checkFlag = buyorderExpenseService.checkExpenseCanAfterSales(k);
            if (!checkFlag) {
                log.error("当前费用单不满足售后条件：{}",JSON.toJSONString(k));
                throw new ServiceException("当前费用单不满足售后条件");
            }
            if (CollUtil.isEmpty(v)) {
                log.error("当前费用单未查到和销售单的关联关系");
            }
            if (!checkExpenseOnlyOneSaleOrder(k)) {
                return;
            }
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseService.viewDetail(k);
            if (!buyorderExpenseDto.getPaymentStatus().equals(BuyorderExpenseConstant.PAYMENT_STATUS_ALL)) {
                log.error("当前费用单：{}付款状态不满足自动转单条件",JSON.toJSONString(k));
                throw new ServiceException("当前费用单付款状态不满足自动转单条件");
            }
            List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = buyorderExpenseDto.getBuyorderExpenseItemDtos();
            if (CollUtil.isEmpty(buyorderExpenseItemDtos)) {
                log.error("原费用单商品数据缺失:{}",JSON.toJSONString(buyorderExpenseDto));
                throw new ServiceException("原费用单商品数据缺失");
            }

            List<BuyorderExpenseItemDto> collect = buyorderExpenseItemDtos.stream().filter(a -> a.getNum() - a.getReturnNum() > 0).collect(Collectors.toList());
            if (CollUtil.isEmpty(collect)) {
                log.info("当前费用单：{}下所有商品已经售后", JSON.toJSONString(k));
                return;
            }

            Date now = new Date();
            buyorderExpenseDto.setBuyorderExpenseItemDtos(collect);
            buyorderExpenseDto.setOriginalBuyorderExpenseId(buyorderExpenseDto.getBuyorderExpenseId());
            buyorderExpenseDto.setOriginalBuyorderExpenseNo(buyorderExpenseDto.getBuyorderExpenseNo());
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setBuyorderExpenseId(null);
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setBuyorderExpenseDetailId(null);
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setOrderDesc(null);
            buyorderExpenseDto.setBuyorderExpenseNo(null);
            buyorderExpenseDto.setBuyorderExpenseId(null);
            buyorderExpenseDto.setIsAuto(1);
            buyorderExpenseDto.setPaymentStatus(BuyorderExpenseConstant.PAYMENT_STATUS_ALL);
            buyorderExpenseDto.setAuditStatus(BuyorderExpenseConstant.AUDIT_STATUS_SUCCESS);
            buyorderExpenseDto.setLockedStatus(BuyorderExpenseConstant.LOCKED_STATUS_UN);
            buyorderExpenseDto.setArrivalStatus(BuyorderExpenseConstant.ARRIVAL_STATUS_ALL);
            buyorderExpenseDto.setDeliveryStatus(BuyorderExpenseConstant.DELIVERY_STATUS_ALL);
            buyorderExpenseDto.setAuditTime(now);
            buyorderExpenseDto.setValidTime(now);
            buyorderExpenseDto.setAddTime(now);
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setPaymentType(424);
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setRetainageAmountMonth(0);
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setRetainageAmount(BigDecimal.ZERO);
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setAccountPeriodAmount(BigDecimal.ZERO);
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setPrepaidAmount(BigDecimal.ZERO);
            buyorderExpenseDto.setOrderType(1);
            collect.forEach(b->{
                b.setNum(b.getNum() - b.getReturnNum());
                b.setBuyorderExpenseId(null);
                b.setBuyorderExpenseItemId(null);
                BuyorderExpenseItemDetailDto buyorderExpenseItemDetailDto = b.getBuyorderExpenseItemDetailDto();
                buyorderExpenseItemDetailDto.setBuyorderExpenseItemId(null);
                buyorderExpenseItemDetailDto.setBuyorderExpenseItemDetailId(null);
                b.setArrivalStatus(BuyorderExpenseConstant.ARRIVAL_STATUS_ALL);
                b.setDeliveryStatus(BuyorderExpenseConstant.DELIVERY_STATUS_ALL);
            });
            BigDecimal totalAmount = collect.stream().map(x -> x.getBuyorderExpenseItemDetailDto().getPrice().multiply(BigDecimal.valueOf(x.getNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            buyorderExpenseDto.getBuyorderExpenseDetailDto().setTotalAmount(totalAmount);
            log.info("自动转单封装的新费用单对象：{}",JSON.toJSONString(buyorderExpenseDto));
            // 订单说明 到 自动转单的逻辑里去处理
            result.add(buyorderExpenseDto);

        });
        return result;
    }

    /**
     * 校验是否唯一 以及是否有红票
     * @param buyorderExpenseId
     * @return
     */
    private boolean checkExpenseOnlyOneSaleOrder(Integer buyorderExpenseId) {

        if (Objects.isNull(buyorderExpenseId)) {
            return Boolean.FALSE;
        }

        List<RBuyorderExpenseJSaleorderDto> byBuyorderExpenseId = rBuyorderExpenseJSaleorderMapper.findByBuyorderExpenseId(buyorderExpenseId);
        List<Integer> collect = byBuyorderExpenseId.stream().map(RBuyorderExpenseJSaleorderDto::getSaleorderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            log.error("checkExpenseOnlyOneSaleOrder 费用单没有正向关联关系");
            return Boolean.FALSE;
        }
        // 有且只有一个
        if (CollUtil.isNotEmpty(collect) && collect.size() > 1) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

}
