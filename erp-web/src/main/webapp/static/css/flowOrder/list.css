html,
body {
  font-size: 13px;
  margin: 0;
  background: #f5f7fa;
  color: #333;
}
html *,
body * {
  box-sizing: border-box;
}
.list-wrap {
  min-width: 960px;
  padding-bottom: 20px;
}
.list-wrap .search-wrap {
  padding: 20px;
  background: #fff;
  margin-bottom: 20px;
}
.list-wrap .search-wrap .search-filter-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -10px;
}
.list-wrap .search-wrap .search-filter-list .search-item {
  width: 25%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.list-wrap .search-wrap .search-filter-list .search-item .search-label {
  width: 100px;
  text-align: right;
  margin-right: 5px;
  color: #666;
}
.list-wrap .search-wrap .search-filter-list .search-item .search-cnt {
  flex: 1;
}
.list-wrap .search-wrap .search-filter-list .search-item .search-cnt .el-date-editor--daterange.el-input__inner {
  width: 100%;
}
.list-wrap .search-wrap .search-filter-list .search-item .search-cnt .el-date-editor--daterange.el-input__inner .el-range-separator {
  width: 18px;
}
.list-wrap .search-wrap .search-filter-list .search-item .search-cnt .el-select {
  width: 100%;
}
.list-wrap .search-filter-btns {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.list-wrap .list-container {
  padding: 0 20px;
}
.list-wrap .list-container .tab-title-txt {
  position: relative;
}
.list-wrap .list-container .tab-title-txt .el-badge {
  line-height: 1;
  vertical-align: top;
  position: absolute;
  top: 10px;
  right: -5px;
  transform: translateX(50%);
}
.list-wrap .list-container .tab-title-txt .el-badge__content {
  border: 0;
}
.list-wrap .list-container .list-table-wrap {
  margin-top: -1px;
}
.list-wrap .list-container .list-table-wrap .table-th th {
  background: #f5f7fa;
}
.list-wrap .list-container .list-table-wrap .el-table__empty-text {
  padding: 100px 0;
}
.list-wrap .list-container .text-link {
  color: #409EFF;
  cursor: pointer;
}
.list-wrap .list-container .text-link:hover {
  color: #f60;
}
.list-wrap .list-container .pagination-wrap {
  padding: 10px 20px;
  display: flex;
  justify-content: flex-end;
  background: #fff;
}
.global-loading {
  font-size: 32px;
}
.global-loading .el-icon-loading {
  color: #fff;
}
