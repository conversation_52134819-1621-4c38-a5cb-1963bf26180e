package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * KingDeeSaleReturnStockDetailDto 销售退货入库明细
 */
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class KingDeeSaleReturnStockDetailDto {

    @WriteBackField(value = "FENTRYID", backWriteByErpField = "FQzokBddjhid", backWriteToKingDeeField = "F_QZOK_BDDJHID")
    private String fEntryId;
    /**
     * 物料
     */
    private String fmaterialid;
    /**
     * 退料数量
     */
    private BigDecimal FRealQty;
    /**
     * fstockid
     */
    private String fstockid;
    /**
     * 含税价
     */
    private BigDecimal ftaxprice;
    /**
     * fentrytaxrate
     */
    private BigDecimal fentrytaxrate;
    /**
     * 原始订单号
     */
    private String FQzokYsddh;
    /**
     * 归属业务单号
     */
    private String FQzokGsywdh;
    /**
     * 业务类型
     */
    private String FQzokYwlx;
    /**
     * 批次号
     */
    private String FQzokPch;
    /**
     * 序列号
     */
    private String FQzokXlh;
    /**
     * 贝登订单行ID
     */
    private String FQzokBddjhid;
    /**
     * fQzokSfzf
     */
    private String FQzokSfzf;
    /**
     * 源单类型 关联退料单必填，默认：SAL_OUTSTOCK
     */
    private String FSrcBillTypeID;
    /**
     * fentityLink
     */
    private List<KingDeeSaleReturnStockDetailLink> fentityLink;


}