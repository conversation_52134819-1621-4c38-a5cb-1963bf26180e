package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 快递签收单Service
 * @date 2023/4/17 15:00
 */
public interface KingDeeExpressReceiptApiService extends KingDeeMqBaseService<KingDeeExpressReceiptDto> {

    /**
     * 查询金蝶快递签收单
     * @param d 参数
     */
    void query(KingDeeExpressReceiptDto d);

}
