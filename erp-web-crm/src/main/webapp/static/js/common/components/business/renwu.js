// 任务列表
Vue.component('renwu-list', {
    template: `
        <div class="records-panel">
            <div class="panel-title">任务记录
                <div class="change-btn" @click="changeFilter">{{ filterMe ? '查看全部待办' : '查看我的待办' }}</div>
            </div>

            <div class="panel-wrap has-fixed-btn" id="scroll-wrap">
                <div class="renwu-list">
                    <div v-if="!pageLoading">
                        <div class="list" v-if="list.length">
                            <div class="item" v-for="(item, index) in list" :key="index">
                                <div class="row margin-b15">
                                    <div class="label">待办人：</div>
                                    <div class="content flex">
                                        <div class="user">
                                            <img :src="item.todoUserAliasHeadPicture || ''" class="icon" onerror="this.src='/static/image/img-error.png'"/>
                                            <div class="name">{{ item.todoUserName }}</div>
                                        </div>
                                        <div class="renwu-status" :class="['status'+item.doneStatus]">{{ STATUS[item.doneStatus] }}</div>
                                    </div>
                                </div>
                                <div class="row margin-b10">
                                    <div class="label">任务类型：</div>
                                    <div class="content">{{ item.mainTaskTypeLabel || '-' }}</div>
                                </div>
                                <div class="row margin-b10">
                                    <div class="label">截止时间：</div>
                                    <div class="content">{{ item.deadline }}</div>
                                </div>
                                <div class="coment">{{ item.taskContent }}</div>
                                <div class="renwu-deal-btn detail margin-t10" >
                                    <a v-if="item.canHandle == 1" class="btn" @click="handleRw(item)">处理</a>
                                    <a v-else class="btn" @click="look(item)">查看</a>
                                </div>
                            </div>
                        </div>
                        <div class="panel-null-data" v-else>
                            <i class="vd-ui_icon icon-info1 icon"></i>
                            <p class="font">暂无任务记录</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-btn-wrap">
                <ui-button class="btn" :disabled="disabled" type="primary" @click="addRenwu">添加任务</ui-button>
            </div>

            <renwu-create ref="createRenwu" :success-fun="initData"></renwu-create>
            <renwu-handle ref="handleRenwu" :success-fun="initData"></renwu-handle>
            <renwu-review ref="lookRenwu"></renwu-review>
        </div>
    `,

    props: {
        // 1:商机 2:线索 3:报价
        bizType: {
            type: [Number, String],
        },
        // // 关联表的主键id（如：商机id，线索id）
        relatedId: {
            type: [Number, String],
        },
        // 报价单id
        quoteorderId: {
            type: [Number, String]
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            userId: '', // 登录用户
            STATUS: {
                "0": "待处理",
                "1": "已处理",
                "2": "已关闭"
            },

            list: [], // 商机阶段列表
            filterMe: false, // true我的待办 false全部待办
            pageNum: 1,
            pageSize: 10,
            isEnd: false,
            isLoading: false,
            pageLoading: true,
        }
    },
    commputed: {
    },
    mounted() {
        this.userId = USERINFO.userId;
        this.initData();
        let scrollEle = document.getElementById('scroll-wrap');
        scrollEle && scrollEle.addEventListener('scroll', this.scrollFun);
    },
    methods: {
        initData () {
            this.pageLoading = true;
            this.list = [];

            let reqData = {
                "pageNum": 1,
                "pageSize": this.pageSize,
                "param": {
                    "listType": 1,
                    "bizList": [{
                        "bizType": this.bizType,
                        "bizId": this.relatedId
                    }]
                },
            }
            if (this.quoteorderId) {
                reqData.param.bizList.push({
                    "bizType": "3", // 1:商机 2:线索 3:报价
                    "bizId": this.relatedId
                })
            }
            if (this.filterMe) {
                reqData.param['todoUserIdList'] = [this.userId];
            }
            this.$axios.post('/crm/task/profile/taskPage', reqData).then(({data}) => {
                if (data.success) {
                    let total = data.data.total || 0;
                    let list = data.data.list || [];
                    this.list = list;
                    this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);
                } else {
                    this.$message.error(data.message);
                }
                this.pageLoading = false;
            });

            this.$emit('refresh')
        },
        // 切换列表类型
        changeFilter () {
            this.filterMe = !this.filterMe;
            this.initData();
        },

        scrollFun () {
            let scrollEle = document.getElementById('scroll-wrap');
            let scrollTop = scrollEle.scrollTop; // 滑出屏幕之外的高度
            let scrollH = scrollEle.scrollHeight; // 文档总高度
            let screenH = scrollEle.clientHeight; // 屏幕可视高度

            if (scrollH - scrollTop - screenH < 100 && !this.isLoading && !this.isEnd) {
                this.isLoading = true;

                let reqData = {
                    "pageNum": ++this.pageNum,
                    "pageSize": this.pageSize,
                    "param": {
                        "listType": 1,
                        "bizList": [{
                            "bizType": this.bizType,
                            "bizId": this.relatedId
                        }]
                    },
                }
                if (this.quoteorderId) {
                    reqData.param.bizList.push({
                        "bizType": "3", // 1:商机 2:线索 3:报价
                        "bizId": this.relatedId
                    })
                }
                if (this.filterMe) {
                    reqData.param['todoUserIdList'] = [this.userId];
                }
                this.$axios.post('/crm/task/profile/taskPage', reqData).then(({data}) => {
                    if (data.success) {
                        let total = data.data.total || 0;
                        let list = data.data.list || [];
                        this.list = this.list.concat(list);
                        this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);
                    }
                    this.isLoading = false;
                });
            }
        },

        // 添加任务
        addRenwu () {
            if(this.disabled) {
                return;
            }
            if (GLOBAL.auth('C0301')) {
                this.$refs.createRenwu.open({
                    bizType: this.bizType,
                    relatedId: this.relatedId,
                });
            } else {
                GLOBAL.showNoAuth();
            }
        },
        // 查看任务
        look (item) {
            let Query = Object.assign({}, item, {
                listType: 1, // 1:我的任务 2:我的发起
            })
            this.$refs.lookRenwu.open(Query);
        },        
        // 处理任务
        handleRw (item) {
            // if (GLOBAL.auth('C0302')) {
                this.$refs.handleRenwu.open(item);
            // } else {
            //     GLOBAL.showNoAuth();
            // }
        },
    }
})

// 任务列表-弹层
Vue.component('renwu-dialog', {
    template: `
        <div>
            <ui-dialog
                :visible.sync="isShow"
                title="任务记录"
                width="1280px"
                align="center"
            >
                <div class="renwu-dialog-top">
                    <ui-button class="btn" type="primary" :disabled="disabled" @click="addRenwu">添加任务</ui-button>
                    <div class="change-btn" @click="changeFilter">{{ filterMe ? '查看全部待办' : '查看我的待办' }}</div>
                </div>

                <div class="records-panel" v-if="isShow">
                    <div class="panel-wrap dialog-inner" id="renwu-wrap">
                        <div class="renwu-list" v-if="list.length">
                            <ui-table border :oneline="true" :width-border="true" :auto-scroll="false" :headers="renwuListHeaders" :list="list">
                                <template v-slot:donestatus="{ row }">
                                    <div class="renwu-status" :class="['status'+row.doneStatus]">{{ STATUS[row.doneStatus] }}</div>
                                </template>
                                <template v-slot:option="{ row }">
                                    <div class="option-wrap">
                                        <div class="renwu-deal-btn">
                                            <a v-if="row.canHandle == 1" class="btn" @click="handleRw(row)">处理</a>
                                            <a v-else class="btn" @click="look(row)">查看</a>
                                        </div>
                                    </div>
                                </template>
                            </ui-table>
                        </div>
                        <div class="panel-null-data" v-else>
                            <i class="vd-ui_icon icon-info1 icon"></i>
                            <p class="font">暂无任务记录</p>
                        </div>
                    </div>
                </div>
            </ui-dialog>

            <renwu-create ref="createRenwu" :success-fun="refreshList"></renwu-create>
            <renwu-handle ref="handleRenwu" :success-fun="refreshList"></renwu-handle>
            <renwu-review ref="lookRenwu"></renwu-review>
        </div>
    `,
    props: {
        refreshPanel: Function // 刷新详情面板列表
    },
    data () {
        return {
            userId: '', // 登录用户
            isShow: false,
            STATUS: {
                "0": "待处理",
                "1": "已处理",
                "2": "已关闭"
            },
            // query
            bizType: '', //  1:商机 2:线索 3:报价
            relatedId: '', // 关联表的主键id（如：商机id，线索id）

            renwuListHeaders: [
                {
                    key: "todoUserName",
                    label: "待办人",
                    width: "180px",
                    avatar: 'todoUserAliasHeadPicture'
                },
                {
                    key: "deadline",
                    label: "截止时间",
                    width: "180px"
                },
                {
                    key: "mainTaskTypeLabel",
                    label: "任务类型",
                    width: "192px"
                },
                {
                    key: "taskContent",
                    label: "任务内容",
                    width: "500px"
                },
                {
                    key: "doneStatus",
                    label: "状态",
                    width: "100px"
                },
                {
                    key: "option",
                    label: "操作",
                    width: "88px"
                },
            ],
            list: [], // 商机阶段列表
            filterMe: false,  // 是否查看自己的待办  true我的待办 false全部待办
            pageNum: 1,
            pageSize: 20,
            isEnd: false,
            isLoading: false,
            disabled: false
        }
    },
    commputed: {
    },
    mounted() {
        this.userId = USERINFO.userId;
    },
    methods: {
        close () {
            this.isShow = false;
        },
        open (query) {
            this.bizType = query.bizType; //  1:商机 2:线索 3:报价
            this.relatedId = query.relatedId; // 业务id
            this.quoteorderId = query.quoteorderId; // 报价单id
            this.disabled = query.disabled;
            this.query = query;
            this.initData();
        },
        refreshList () {
            this.initData();
            if (this.refreshPanel) {
                this.refreshPanel();
            }
        },
        initData () {
            GLOBAL.showGlobalLoading();
            this.list = [];

            let reqData = {
                "pageNum": 1,
                "pageSize": this.pageSize,
                "param": {
                    "listType": 1,
                    "bizList": [{
                        "bizType": this.bizType,
                        "bizId": this.relatedId
                    }]
                },
            }
            if (this.bizType == 1) {
                reqData.param.bizList.push({
                    "bizType": "3", // 1:商机 2:线索 3:报价
                    "bizId": this.relatedId
                })
            }
            if (this.filterMe) {
                reqData.param['todoUserIdList'] = [this.userId];
            }

            // 跟进记录
            this.$axios.post('/crm/task/profile/taskPage', reqData).then(({data}) => {
                console.log('任务列表:', data);
                if (data.success) {
                    let total = data.data.total || 0;
                    let list = data.data.list || [];
                    this.list = list;
                    this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);

                    this.isShow = true;

                    this.pageNum = 1;

                    this.$nextTick(() => {
                        let scrollEle = document.getElementById('renwu-wrap');
                        scrollEle && scrollEle.addEventListener('scroll', this.scrollFun);
                    })
                } else {
                    this.$message.error(data.message);
                }
                GLOBAL.hideGlobalLoading();
            });
        },
        // 切换列表类型
        changeFilter () {
            this.filterMe = !this.filterMe;
            this.initData();
        },

        scrollFun () {
            let scrollEle = document.getElementById('renwu-wrap');
            let scrollTop = scrollEle.scrollTop; // 滑出屏幕之外的高度
            let scrollH = scrollEle.scrollHeight; // 文档总高度
            let screenH = scrollEle.clientHeight; // 屏幕可视高度

            if (scrollH - scrollTop - screenH < 100 && !this.isLoading && !this.isEnd) {
                this.isLoading = true;

                let reqData = {
                    "pageNum": ++this.pageNum,
                    "pageSize": this.pageSize,
                    "param": {
                        "listType": 1,
                        "bizList": [{
                            "bizType": this.bizType,
                            "bizId": this.relatedId
                        }]
                    },
                }
                if (this.quoteorderId) {
                    reqData.param.bizList.push({
                        "bizType": "3", // 1:商机 2:线索 3:报价
                        "bizId": this.relatedId
                    })
                }
                if (this.filterMe) {
                    reqData.param['todoUserIdList'] = [this.userId];
                }
                this.$axios.post('/crm/task/profile/taskPage', reqData).then(({data}) => {
                    if (data.success) {
                        let total = data.data.total || 0;
                        let list = data.data.list || [];
                        this.list = this.list.concat(list);
                        this.isEnd = (this.list.length >= total) || (list.length < this.pageSize);
                    }
                    this.isLoading = false;
                });
            }
        },

        // 添加任务
        addRenwu () {
            if(this.disabled) {
                return;
            }
            if (GLOBAL.auth('C0301')) {
                this.$refs.createRenwu.open({
                    bizType: this.bizType,
                    relatedId: this.relatedId,
                });
            } else {
                GLOBAL.showNoAuth();
            }
        },
        // 处理任务
        handleRw (item) {
            // if (!GLOBAL.auth('C0302')) {
            //     GLOBAL.showNoAuth();
            //     return;
            // }
            this.$refs.handleRenwu.open(item);
        },
        // 查看任务
        look (item) {
            let Query = Object.assign({}, item, {
                listType: 1, // 1:我的任务 2:我的发起
            })
            this.$refs.lookRenwu.open(Query);
        },
    }
})

// 添加任务
Vue.component('renwu-create', {
    template: `
        <div class="follow-up-record-created-dialog">
            <ui-dialog
                :visible.sync="isShow"
                title="添加任务"
                width="960px"
                align="center"
            >
                <div class="form-wrap label-width-3" v-if="isShow">
                    <ui-form-item label="任务类型" :must="true" :text="true">
                        <div class="ui-col-8">
                            <ui-radio-group
                                :list="RadioList1"
                                :value.sync="mainTaskType"
                                @change="handlerTaskType"
                                valid="CreateRenwu_mainTaskType"
                            ></ui-radio-group>
                        </div>
                    </ui-form-item>
                    <!-- <ui-form-item v-if="mainTaskType == 4 && RadioList2.length" label="督导类型" :must="true" :text="true">
                        <div class="ui-col-8">
                            <ui-checkbox-group
                                :list="RadioList2"
                                :values.sync="subTaskType"
                                @change="handlerSubTaskType"
                                :errorMsg="subTaskTypeError"
                                :errorable="!!subTaskTypeError"
                            ></ui-checkbox-group>
                        </div>
                    </ui-form-item> -->
                    <ui-form-item label="任务内容" :must="true">
                        <ui-input
                            width="590px"
                            type="textarea"
                            maxlength="200"
                            showWordLimit
                            placeholder="最多200个字"
                            v-model="taskContent"
                            height="75px"
                            width="600px"
                            valid="CreateRenwu_taskContent"
                        ></ui-input>
                    </ui-form-item>
                    <ui-form-item label="待办人" v-if="mainTaskType == 4 && todoUserList.length" :must="true" :text="true">
                        <div class="user-show">
                            <img class="avatar" :src="chanceDetail.belongPic || ''" onerror="this.src='/static/image/img-error.png'"/>
                            <span class="name">{{chanceDetail.belonger}}</span>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="待办人" v-else :must="true">
                        <ui-tree 
                            :data="treeList" 
                            v-model="todoUserList" 
                            @change="handlerDaiban"
                            valid="CreateRenwu_todoUserList"
                        ></ui-tree>
                    </ui-form-item>
                    <ui-form-item v-if="mainTaskType == 4 || mainTaskType == 6" label="截止时间" :must="true">
                        <ui-date-picker
                            type="datetime"
                            :append-to-body="true"
                            width="350px"
                            v-model="deadline"
                            placeholder="请选择截止时间"
                            valid="CreateRenwu_deadline"
                        ></ui-date-picker>
                    </ui-form-item>
                </div>
                <template slot="footer">
                    <div class="dlg-form-footer">
                        <ui-button @click="submit" type="primary">提交</ui-button>
                        <ui-button @click="close" class="close">取消</ui-button>
                    </div>
                </template>
            </ui-dialog>
        </div>
    `,

    props: {
        successFun: Function, // 成功回调
    },
    data () {
        return {
            isShow: false,
            canAjax: true,

            // query ↓↓↓
            query: {}, // 创建携带参数

            // form ↓↓↓
            // 任务类型
            RadioList1: [],
            mainTaskType: '', // 督导4 展示督导类型 截止时间
            // 督导类型
            // RadioList2: [],
            // subTaskType: [],
            // subTaskTypeError: '',
            // 待办人
            chanceDetail: '', // 商机详情
            treeList: [],
            todoUserList: [],

            taskContent: '', // 任务内容
            deadline: '', // 截止时间
        }
    },
    created () {
    },
    watch: {
        isShow (newV) {
            if (!newV) {
                this.query = {};
                // 表单内容清空
                this.RadioList1 = [];
                this.mainTaskType = '';
                // this.RadioList2 = [];
                // this.subTaskType = [];
                this.taskContent = '';
                this.deadline = '';
            }
        }
    },
    mounted () {
    },
    methods: {
        close() {
            this.isShow = false;
        },
        open(data) {
            console.log('create query：', data);
            this.query = data || {};
            this.initData();

            this.$form && this.$form.rules({
                mainTaskType: {
                    required: '请选择任务类型'
                },
                taskContent: {
                    required: '请输入任务内容'
                },
                todoUserList: {
                    required: '请选择待办人'
                },
                deadline: {
                    required: '请选择截止时间'
                },
            }, 'CreateRenwu', this);
        },
        // 表单默认值
        async initData () {
            console.log(this.query)
            let { data: res1 } = await this.$axios.post(`/crm/task/profile/getTaskType?businessId=${this.query.relatedId}&type=${this.query.bizType}`)
            if (res1.success) {
                this.RadioList1 = res1.data || [];
                if (this.RadioList1.length == 1) {
                    this.mainTaskType = this.RadioList1[0].value
                    this.handlerTaskType();
                }
            }

            let { data: res2 } = await this.$axios.post(`/crm/task/profile/getCandidateUser`)
            if (res2.code == 0) {
                this.treeList = res2.data || [];
            }

            // 默认当天23:59:59
            let currentTime = moment(new Date()).format('YYYY-MM-DD');
            this.deadline = currentTime + ' 23:59:59';

            this.isShow = true;
        },

        // handler任务类型
        async handlerTaskType () {
            if (this.mainTaskType == 4) {
                // this.getSupervision();

                // 商机督导类型自动带入线上销售
                if (!this.chanceDetail) {
                    await this.$axios.get(`/crm/presales/profile/detail?dataId=${this.query.relatedId}&type=${{1: 2, 2: 1}[this.query.bizType]}`).then(({data})=> {
                        if (data.success) {
                            this.chanceDetail = data.data || {};
                        }
                    })
                }
                this.todoUserList = [this.chanceDetail.belongerId];
            } else {
                let belonger = this.chanceDetail && this.chanceDetail.userId || '';
                if (this.todoUserList.length == 1 && this.todoUserList[0] == belonger) {
                    this.todoUserList = [];
                }
            }
        },
        // 督导二级字典
        // getSupervision () {
        //     this.$axios.post(`/crm/task/profile/getSupervisionType?businessId=${this.query.relatedId}`).then(({data})=> {
        //         if (data.success) {
        //             this.RadioList2 = data.data || [];
        //         } else {
        //             this.$message.warn(data.message || '网络异常');
        //         }
        //     })
        // },

        // handlerSubTaskType () {
        //     if (this.mainTaskType == 4 && this.RadioList2.length && this.subTaskType.length) {
        //         this.subTaskTypeError = '';
        //     }
        // },
        // 待办change
        handlerDaiban (data) {
            console.log('todoUserList:', this.todoUserList);
            console.log('data:', data);
        },

        checkForm () {
            let error = 0;
            if (!this.$form.validForm('CreateRenwu')) {
                error++;
            }

            // 督导单独判断
            // if (this.mainTaskType == 4 && this.RadioList2.length && !this.subTaskType.length) {
            //     this.subTaskTypeError = '请选择督导类型';
            //     error++;
            // }

            if (error) return false;
            return true;
        },

        submit () {
            if (!this.checkForm()) return;

            this.canAjax = false;
            GLOBAL.showGlobalLoading();

            let reqData = {
                bizType: this.query.bizType, // 沟通记录类型-字典值- 244商机 4109线索
                bizId: this.query.relatedId,             // 主键id（如：商机id，线索id）
                // ↓↓↓form↓↓↓
                mainTaskType: this.mainTaskType,     // 任务类型
                // subTaskType: this.subTaskType.join(','),       // 督导类型
                taskContent: this.taskContent,       // 内容
                todoUserList: this.todoUserList,     // 待办人
                deadline: this.deadline,             // 截止时间
            }

            if (!(this.mainTaskType == 4 || this.mainTaskType == 6)) {
                delete reqData.deadline;
            }
            console.log('submit', reqData);

            this.$axios.post(`/crm/task/profile/save`, reqData).then(({data})=> {
                GLOBAL.hideGlobalLoading();

                if (data.success) {
                    this.$message.success('添加成功');
                    this.isShow = false;
                    this.close();

                    if (this.successFun) {
                        this.successFun();
                    }
                } else if (data.message == 'SUB_TASK_TYPE_NOT_MATCH') { // 异步报错·更新督导类型字典??
                    this.$message.warn('商机状态异常，请刷新页面后重试。');
                }
                else {
                    this.canAjax = true;
                    this.$message.warn(data.message || '网络异常')
                }
            })
        },
    },
})

// 处理任务
Vue.component('renwu-handle', {
    template: `
        <div class="renwu-review-container">
            <ui-dialog
                :visible.sync="isShow"
                title="处理任务"
                width="960px"
            >

                <div class="handle-tips" v-if="!showForm">
                    <i class="vd-ui_icon icon icon-info2"></i>
                    <span v-if="detail.mainTaskType == 7">当拜访完成后，系统会自动处理拜访任务。</span>
                    <span v-else>请在报价单处点击“完成咨询”按钮，来处理此任务。</span>
                </div>

                <div class="handle-wrap" v-if="isShow" :style="{'paddingTop': !showForm? '44px': '0'}">
                    <div class="form-wrap label-width-3">
                        <ui-form-item label="客户名称" :text="true">
                            <template v-if="detail.traderCustomerName">
                                {{ detail.traderCustomerName }}
                            </template>
                            <template v-else>-</template>
                        </ui-form-item>
                        <ui-form-item label="业务编号" :text="true" v-if="detail.bizId && detail.bizNo">
                            <div @click="toDetail" class="td-link">{{ detail.bizNo || '-' }}</div>
                        </ui-form-item>
                        <ui-form-item label="任务类型" :text="true">{{ detail.taskType || '-' }}</ui-form-item>
                        <ui-form-item label="任务内容" :text="true">
                            <ui-col-10>{{ detail.taskContent || '-' }}</ui-col-10>
                        </ui-form-item>
                        <ui-form-item label="发起人" :text="true">
                            <div class="user-show">
                                <img class="avatar" :src="detail.applyUserAliasHeadPicture || ''" onerror="this.src='/static/image/img-error.png'" />
                                <span class="name">{{ detail.applyUserName || '-' }}</span>
                            </div>
                        </ui-form-item>
                        <ui-form-item label="发起时间" :text="true">{{ detail.commitTime || '-' }}</ui-form-item>
                        <ui-form-item label="待办人" :text="true" v-if="detail.subTodoUserList && detail.subTodoUserList.length > 1">
                            <ui-col-10>{{ todoUser || '-' }}</ui-col-10>
                        </ui-form-item>
                        <ui-form-item label="待办人" :text="true" v-else>
                            <div class="user-show">
                                <img class="avatar" :src="detail.todoUserAliasHeadPicture || ''" onerror="this.src='/static/image/img-error.png'" />
                                <span class="name">{{ detail.todoUserName || '-' }}</span>
                            </div>
                        </ui-form-item>
                        <ui-form-item label="截止时间" :text="true">{{ detail.deadline || '-' }}</ui-form-item>
                        <ui-form-item label="是否超时" :text="true">
                            <div v-if="detail.isOverTime == 1" class="font-danger">超时</div>
                            <div v-else>正常</div>
                        </ui-form-item>
                    </div>

                    <div class="form-wrap handle-bordertop label-width-3" v-if="showForm">
                        <ui-form-item label="任务处理结果" :text="true">
                            <ui-radio-group
                                :list="RadioList"
                                :value.sync="doneStatus"
                                valid="HandleRenwu_doneStatus"
                            ></ui-radio-group>
                        </ui-form-item>
                        <ui-form-item label="任务处理说明" v-if="doneStatus" :text="true" :must="doneStatus==2? true: false">
                            <ui-input
                                width="823px"
                                type="textarea"
                                maxlength="200"
                                showWordLimit
                                v-model="doneRemark"
                                height="75px"
                                width="600px"
                                valid="HandleRenwu_doneRemark"
                            ></ui-input>
                        </ui-form-item>
                    </div>
                </div>

                <template slot="footer" v-if="showForm">
                    <div class="dlg-form-footer">
                        <ui-button @click="submit" type="primary">提交</ui-button>
                        <ui-button @click="close" class="close">取消</ui-button>
                    </div>
                </template>
            </ui-dialog>
        </div>
    `,
    props: {
        successFun: Function,
    },
    data () {
        return {
            userId: '', // 登录用户
            isShow: false,
            detail: {},

            // form 
            showForm: true,
            RadioList: [
                { label: '已处理', value: '1' },
                { label: '已关闭', value: '2' }
            ],
            doneStatus: '',
            doneRemark: '',
            canSubmit: true,
        }
    },
    created () {
    },
    mounted () {
        this.userId = USERINFO.userId;
    },
    watch: {
        isShow (newV) {
            if (!newV) {
                this.doneStatus = '';
                this.doneRemark = '';
                this.showForm = true;
                this.canSubmit = true;
                this.detail = {};
            }
        }
    },
    computed: {
        todoUser () {
            let arr = [];
            this.detail.subTodoUserList && this.detail.subTodoUserList.forEach(item => {
                arr.push(item.userName);
            });
            return arr.join(', ');
        }
    },
    methods: {
        close() {
            this.isShow = false;
        },
        open(item) {
            GLOBAL.showGlobalLoading();
            this.initData(item);
        },
        initData (item) {
            let reqData = {
                taskItemId: item.taskItemId
            }
            this.$axios.post(`/crm/task/profile/detail`, reqData).then(({data})=> {
                if (data.success) {
                    this.detail = data.data || {};
                    GLOBAL.hideGlobalLoading();
                    this.isShow = true;

                    if (this.detail.mainTaskType == 2 || this.detail.mainTaskType == 3 || this.detail.mainTaskType == 7) { // 2单品询价 3综合询价 7拜访计划 不展示表单和处理按钮
                        this.showForm = false;
                    } else {
                        this.showForm = true;
                        const _this = this;
                        this.$nextTick(()=> {
                            this.$form && this.$form.rules({
                                doneStatus: {
                                    required: '请选择任务处理结果'
                                },
                                doneRemark: {
                                    custom: {
                                        valid(value) {
                                            if (_this.doneStatus == 2 && !value.trim()) {
                                                return false;
                                            } else {
                                                return true;
                                            }
                                        },
                                        message: '请输入任务处理说明',
                                    },
                                }
                            }, 'HandleRenwu', this);
                        })
                    }
                } else {
                    this.$message.warn(data.message || '网络异常')
                }
            })
        },

        submit () {
            if (!this.canSubmit) return;

            if (!this.$form.validForm('HandleRenwu')) return;

            this.canSubmit = false;
            this.$axios.post('/crm/task/profile/handle', {
                taskId: this.detail.taskId,
                taskItemId: this.detail.taskItemId,
                doneStatus: this.doneStatus,
                doneRemark: this.doneRemark,
            }).then(({data}) => {
                if (data.success) {
                    this.$message.success('处理成功');
                    this.isShow = false;
                    this.close();

                    if (this.successFun) {
                        this.successFun();
                    }
                } else {
                    this.canSubmit = true;
                    this.$message.warn(data.message || '网络异常')
                }
            })
        },

        toDetail () {
            let traderName = this.detail.traderCustomerName || '';
            // bizType 业务类型 1:商机 2：线索 3：报价
            if (this.detail.bizType == 1) {
                GLOBAL.link({name: traderName+'商机查看', url: `/crm/businessChance/profile/detail?id=${ this.detail.bizId }`});
            } else if (this.detail.bizType == 2) {
                GLOBAL.link({name: traderName+'线索查看', url: `/crm/businessLeads/profile/detail?id=${ this.detail.bizId }`});
            } else if (this.detail.bizType == 3) {
                GLOBAL.link({name: traderName+'线索查看', url: `/crm/quoter/profile/index?id=${ this.detail.bizId }`});
            } else if (this.detail.bizType == 7) {
                GLOBAL.link({name: '拜访计划查看', url: `/crm/visitRecord/profile/detail?id=${ this.detail.bizId }`});
            }
        }
    },
})

// 查看任务
Vue.component('renwu-review', {
    template: `
        <div class="renwu-review-container">
            <ui-dialog
                :visible.sync="isShow"
                title="查看任务"
                width="960px"
            >
                <div class="form-wrap label-width-3" v-if="isShow">
                    <ui-form-item label="客户名称" :text="true">
                        <template v-if="detail.traderCustomerName">
                            {{ detail.traderCustomerName }}
                        </template>
                        <template v-else>-</template>
                    </ui-form-item>
                    <ui-form-item label="业务编号" :text="true" v-if="detail.bizId && detail.bizNo">
                        <div @click="toDetail" class="td-link">{{ detail.bizNo || '-' }}</div>
                    </ui-form-item>
                    <ui-form-item label="任务类型" :text="true">{{ detail.taskType || '-' }}</ui-form-item>
                    <ui-form-item label="任务内容" :text="true">
                        <ui-col-10>{{ detail.taskContent || '-' }}</ui-col-10>
                    </ui-form-item>
                    <ui-form-item label="发起人" :text="true">
                        <div class="user-show">
                            <img class="avatar" :src="detail.applyUserAliasHeadPicture || ''" onerror="this.src='/static/image/img-error.png'" />
                            <span class="name">{{ detail.applyUserName || '-' }}</span>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="发起时间" :text="true">{{ detail.commitTime || '-' }}</ui-form-item>
                    <ui-form-item label="待办人" v-if="listType == 2" :text="true">
                        <ui-col-10>{{ todoUser || '-' }}</ui-col-10>
                    </ui-form-item>
                    <ui-form-item label="待办人" v-else :text="true">
                        <div class="user-show">
                            <img class="avatar" :src="detail.todoUserAliasHeadPicture || ''" onerror="this.src='/static/image/img-error.png'" />
                            <span class="name">{{ detail.todoUserName || '-' }}</span>
                        </div>
                    </ui-form-item>
                    <ui-form-item label="截止时间" :text="true">{{ detail.deadline || '-' }}</ui-form-item>
                    <ui-form-item label="是否超时" :text="true">
                        <div v-if="detail.isOverTime == 1" class="font-danger">超时</div>
                        <div v-else>正常</div>
                    </ui-form-item>
                </div>



                <div class="form-wrap border-top label-width-3">
                    <ui-form-item label="任务处理结果" :text="true">
                        <div v-if="listType == 2" class="renwu-status" :class="['status'+detail.doneStatus]">{{ CREATERSTATUS[detail.doneStatus] || '-' }}</div>
                        <div v-else class="renwu-status" :class="['status'+detail.doneStatus]">{{ STATUS[detail.doneStatus] || '-' }}</div>
                    </ui-form-item>

                    <!-- 我的发起 -->
                    <template v-if="listType == 2">
                        <ui-table container-height="470px" :oneline="true" :width-border="true" :auto-scroll="false" :headers="subTaskListHeaders" :list="subTaskList">
                            <template v-slot:donestatus="{ row }">
                                <div class="renwu-status" :class="['status'+row.doneStatus]">{{ STATUS[row.doneStatus] || '-' }}</div>
                            </template>
                        </ui-table>
                    </template>
                    <template v-else-if="detail.doneStatus != 0">
                        <ui-form-item label="处理人" :text="true">
                            <div class="user-show">
                                <img class="avatar" :src="detail.doneUserAliasHeadPicture || ''" onerror="this.src='/static/image/img-error.png'" />
                                <span class="name">{{ detail.doneUserName || '-' }}</span>
                            </div>
                        </ui-form-item>
                        <ui-form-item label="处理时间" :text="true">{{ detail.doneTime || '-' }}</ui-form-item>
                        <ui-form-item label="处理结果说明" :text="true">
                            <ui-col-10>{{ detail.doneRemark || '-' }}</ui-col-10>
                        </ui-form-item>
                    </template>
                </div>

            </ui-dialog>
        </div>
    `,

    data () {
        return {
            userId: '', // 登录用户
            isShow: false,
            // 我的发起状态
            CREATERSTATUS: {
                "0": "处理中",
                "1": "已处理",
                "2": "已关闭"
            },
            // 我的任务状态
            STATUS: {
                "0": "待处理",
                "1": "已处理",
                "2": "已关闭"
            },
            listType: 1, // 1:我的任务 2:我的发起

            detail: {},
            subTaskList: [], // 子任务
            subTaskListHeaders: [
                {
                    key: "todoUserName",
                    label: "待办人",
                    width: "150px",
                    avatar: "todoUserAliasHeadPicture"
                },
                {
                    key: "doneUserName",
                    label: "处理人",
                    width: "150px",
                    avatar: "doneUserAliasHeadPicture"
                },
                {
                    key: "doneStatus",
                    label: "任务状态",
                    width: "100px"
                },
                {
                    key: "doneTime",
                    label: "处理时间",
                    width: "180px"
                },
                {
                    key: "doneRemark",
                    label: "处理结果说明",
                    width: "250px"
                },
            ],
        }
    },
    created () {
    },
    mounted () {
        this.userId = USERINFO.userId;
    },
    computed: {
        todoUser () {
            let arr = [];
            this.detail.subTodoUserList && this.detail.subTodoUserList.forEach(item => {
                arr.push(item.userName);
            });
            return arr.join(', ');
        }
    },
    methods: {
        open(item) {
            GLOBAL.showGlobalLoading();
            this.listType = item.listType || 1;
            this.initData(item);
        },
        initData (item) {
            let reqData = {
                taskItemId: item.taskItemId
            }
            if (this.listType == 2) {
                reqData['taskId'] = item.taskId;
            }
            this.$axios.post(`/crm/task/profile/detail`, reqData).then(({data})=> {
                console.log('任务详情', data);
                if (data.success) {
                    this.detail = data.data || {};
                    this.subTaskList = this.detail.subTaskList || [];
                    GLOBAL.hideGlobalLoading();
                    this.isShow = true;
                } else {
                    this.$message.warn(data.message || '网络异常')
                }
            })
        },
        close() {
            this.isShow = false;
        },
        toDetail () {
            let traderName = this.detail.traderCustomerName || '';
            // bizType 业务类型 1:商机 2：线索 3：报价
            if (this.detail.bizType == 1) {
                GLOBAL.link({name: traderName+'商机查看', url: `/crm/businessChance/profile/detail?id=${ this.detail.bizId }`});
            } else if (this.detail.bizType == 2) {
                GLOBAL.link({name: traderName+'线索查看', url: `/crm/businessLeads/profile/detail?id=${ this.detail.bizId }`});
            } else if (this.detail.bizType == 3) {
                GLOBAL.link({name: traderName+'报价查看', url: `/crm/quoter/profile/index?id=${ this.detail.bizId }`});
            } else if (this.detail.bizType == 7) {
                GLOBAL.link({name: '拜访计划查看', url: `/crm/visitRecord/profile/detail?id=${ this.detail.bizId }`});
            }
        }
    },
})