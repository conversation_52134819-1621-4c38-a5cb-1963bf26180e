package com.vedeng.crm.task.domain.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 任务记录表
 *
 * <AUTHOR>
 */
@Data
public class TaskQueryDto {
    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 我的任务id
     */
    private Long taskItemId;

    /**
     * 业务类型 1:商机 2：线索 3：报价
     */
    private Integer bizType;

    /**
     * 业务id
     */
    private Integer bizId;

    /**
     * 业务信息
     */
    private List<Biz> bizList = new ArrayList<>();

    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 发起人
     */
    private List<Integer> creatorIdList = new ArrayList<>();

    /**
     * 待办人
     */
    private List<Integer> todoUserIdList = new ArrayList<>();

    /**
     * 任务类型
     * 1.初步产品方案
     * 2.单品询价
     * 3.综合询价
     * 4.商机督导
     * 5.再次跟进
     */
    private Integer mainTaskType;

    /**
     * 商机督导-督导类型
     */
    private String subTaskType;

    /**
     * 任务类型 list
     */
    private List<Integer> mainTaskTypeList;

    /**
     * 商机督导-督导类型 list
     */
    private List<Integer> subTaskTypListe;

    /**
     * 处理状态：0处理中 1已处理 2关闭
     */
    private Integer doneStatus;

    /**
     * 超时状态 0全部 1正常 2超时
     */
    private Integer overdueStatus;

    /**
     * 开始截止时间 yyyy-MM-dd HH:mm:ss
     */
    private Date startDeadline;

    /**
     * 结束截止时间 yyyy-MM-dd HH:mm:ss
     */
    private Date endDeadline;

    /**
     * 开始提交时间 yyyy-MM-dd HH:mm:ss
     */
    private Date startCommitTime;
    /**
     * 结束提交时间 yyyy-MM-dd HH:mm:ss
     */
    private Date endCommitTime;

    /**
     * 列表类型 (1.我的任务列表 2.我的发起任务列表)
     */
    private Integer listType;

    @Data
    public static class Biz {

        /**
         * 业务id
         */
        private Integer bizId;

        /**
         * 业务类型 1:商机 2：线索 3：报价
         */
        private Integer bizType;

    }

}