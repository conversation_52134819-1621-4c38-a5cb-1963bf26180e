<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="处理详情" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<script type="text/javascript"
        src='<%= basePath %>static/js/flash/prepare/safe_stock.js?rnd=${resourceVersionKey}'></script>
<div class="content">
    <div class="sku-info line">
        <ul>
            <li>
                <div class="infor_name">
                    <label class="info">订货号：</label>
                </div>
                <div class="info-name">
                    <label>${dealDetailDto.prepareStockDto.skuNo}</label>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <label class="info">商品名称：</label>
                </div>
                <div class="info-name">
                    <label>${dealDetailDto.prepareStockDto.skuName}</label>
                </div>
            </li>
        </ul>
    </div>
    <div>
        <c:choose>
            <c:when test="${not empty dealDetailDto.doNotPrepareLog
            or not empty dealDetailDto.repalceLog
            or not empty dealDetailDto.conversionLog
            or not empty dealDetailDto.deleteLog
            or not empty dealDetailDto.prepareLog}">
                <ul>
                    <c:choose>
                    <c:when test="${not empty dealDetailDto.prepareLog}">
                    <div class="layui-bg-gray" style="padding: 10px;">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md12">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        生成备货单
                                    </div>
                                    <div class="layui-card-body">
                                        <div>
                                            <table class="table table-bordered table-striped table-condensed table-centered"
                                                   id="cus">
                                                <thead>
                                                <tr>
                                                    <th class="text-left wid15">备货单</th>
                                                    <th class="wid13">创建时间</th>
                                                    <th class="wid6">数量</th>
                                                    <th class="wid8">预计到达时间</th>
                                                </tr>
                                                </thead>
                                                <tbody class="employeestate">
                                                <c:forEach items="${dealDetailDto.prepareLog}" var="log"
                                                           varStatus="num">
                                                    <tr>
                                                        <td>${log.saleorderNo }</td>
                                                        <td>${log.operateTimeString }</td>
                                                        <td>${log.buyNum }</td>
                                                        <td>
                                                            <a class="addtitle1 can-click"
                                                               href="javascript:void(0);"
                                                               tabTitle='{"num":"viewBhSaleorder","link":"/order/saleorder/viewBhSaleorder.do?saleorderId=${log.saleorderId}"}'>
                                                                查看
                                                            </a>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                                </tbody>
                                            </table>
                                            <tags:page page="${page}"/>
                                        </div>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </c:when>
                        </c:choose>
                </ul>
                <ul>
                    <c:choose>
                    <c:when test="${not empty dealDetailDto.doNotPrepareLog}">
                    <div class="layui-bg-gray" style="padding: 10px;">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md12">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        暂不备货
                                    </div>
                                    <c:forEach var="log" items="${dealDetailDto.doNotPrepareLog}" varStatus="num">
                                        <div class="layui-card-body">
                                            <div class="layui-col-xs12">
                                                <div class="infor_name">
                                                    <label class="info">操作时间：</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.operateTimeString}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs12">
                                                <div class="infor_name">
                                                    <label class="info">暂不备货原因：</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.operateReason}</label>
                                                </div>
                                            </div>
                                            <hr>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                        </c:when>
                        </c:choose>
                </ul>
                <ul>
                    <c:choose>
                    <c:when test="${not empty dealDetailDto.repalceLog}">
                    <div class="layui-bg-gray" style="padding: 10px;">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md12">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        替换
                                    </div>
                                    <c:forEach var="log" items="${dealDetailDto.repalceLog}" varStatus="num">
                                        <div class="layui-card-body">
                                            <div class="layui-col-xs12">
                                                <div class="infor_name">
                                                    <label class="title">替换时间：</label>
                                                </div>
                                                <div class="info-name">
                                                    <label class="title">${log.operateTimeString}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs12">
                                                <div class="infor_name">
                                                    <label class="title">替换原因：</label>
                                                </div>
                                                <div class="info-name">
                                                    <label class="title">${log.operateReason}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">原SKU</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.skuNo}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">商品名称</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.skuName}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">预警级别</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.stockWarn}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">安全库存</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.safeStockString}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">库存数量</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.stockString}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">建议补充库存数量</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.proposePrepareStockString}</label>
                                                </div>
                                            </div>
                                            <hr>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                        </c:when>
                        </c:choose>
                </ul>
                <ul>
                    <c:choose>
                    <c:when test="${not empty dealDetailDto.conversionLog}">
                    <div class="layui-bg-gray" style="padding: 10px;">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md12">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        转换
                                    </div>
                                    <c:forEach var="log" items="${dealDetailDto.conversionLog}" varStatus="num">
                                        <div class="layui-card-body">
                                            <div class="layui-col-xs12">
                                                <div class="infor_name">
                                                    <label class="title">转换时间：</label>
                                                </div>
                                                <div class="info-name">
                                                    <label class="title">${log.operateTimeString}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs12">
                                                <div class="infor_name">
                                                    <label class="title">转换原因：</label>
                                                </div>
                                                <div class="info-name">
                                                    <label class="title">${log.operateReason}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">原SKU</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.skuNo}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">商品名称</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.skuName}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">预警级别</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.stockWarn}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">安全库存</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.safeStockString}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">库存数量</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.stockString}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs6">
                                                <div class="infor_name">
                                                    <label class="info">建议补充库存数量</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.proposePrepareStockString}</label>
                                                </div>
                                            </div>
                                            <hr>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                        </c:when>
                        </c:choose>
                </ul>
                <ul>
                    <c:choose>
                    <c:when test="${not empty dealDetailDto.deleteLog}">
                    <div class="layui-bg-gray" style="padding: 10px;">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md12">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        删除
                                    </div>
                                    <c:forEach var="log" items="${dealDetailDto.deleteLog}" varStatus="num">
                                        <div class="layui-card-body">
                                            <div class="layui-col-xs12">
                                                <div class="infor_name">
                                                    <label class="info">操作时间：</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.operateTimeString}</label>
                                                </div>
                                            </div>
                                            <div class="layui-col-xs12">
                                                <div class="infor_name">
                                                    <label class="info">删除原因：</label>
                                                </div>
                                                <div class="info-name">
                                                    <label>${log.operateReason}</label>
                                                </div>
                                            </div>
                                            <hr>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                        </c:when>
                        </c:choose>
                </ul>
            </c:when>
            <c:otherwise>
                <tr>
                    <td colspan="19">暂无处理操作！</td>
                </tr>
            </c:otherwise>
        </c:choose>
    </div>
</div>
<style>
    .sku-info {
        width: 100%;
        padding: 20px;
        font-size: 15px;
    }

    .sku-info ul li {
        float: left;
        height: 30px;
        line-height: 30px;
    }

    .info {
        font-weight: 500;
    }

    .sku-info .info-name {
        float: left;
        margin: 3px 10px 0 0;
    }

</style>
<%@ include file="../../common/footer.jsp" %>
