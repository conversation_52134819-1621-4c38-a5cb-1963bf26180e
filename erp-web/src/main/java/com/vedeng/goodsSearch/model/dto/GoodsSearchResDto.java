package com.vedeng.goodsSearch.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 商品搜索运输类
 *
 * <AUTHOR>
 */
@Data
public class GoodsSearchResDto {

    private List<PriceSectionDto> regionPriceList;

    private List<CategoryDto> categoryList;

    private List<AttributeDto> attributeList;

    private List<InstitutionDto> institutionList;

    private List<BrandDto> brandList;

    private List<DepartmentDto> departmentList;

    private List<SpuTypeDto> spuType;

    private List<GoodsLevelDto> goodsLevelList;

    private List<GoodsPositionDto> goodsPositionList;

    private List<BrandNatureDto> brandNature;
}
