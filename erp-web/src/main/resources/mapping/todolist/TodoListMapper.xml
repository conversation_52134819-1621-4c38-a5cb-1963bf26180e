<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.todolist.dao.TodoListMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.todolist.model.TodoList">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="BUZ_TYPE" jdbcType="INTEGER" property="buzType" />
    <result column="BUZ_ID" jdbcType="INTEGER" property="buzId" />
    <result column="BUZ_EXTRA" jdbcType="VARCHAR" property="buzExtra" />
    <result column="BUZ_REDIRECT" jdbcType="VARCHAR" property="buzRedirect" />
    <result column="BUZ_PROPERTY" jdbcType="VARCHAR" property="buzProperty" />
    <result column="CHECK_STATUS" jdbcType="INTEGER" property="checkStatus" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="DELETE" jdbcType="INTEGER" property="delete" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, BUZ_TYPE, BUZ_ID, BUZ_EXTRA, BUZ_REDIRECT, BUZ_PROPERTY,CHECK_STATUS,COMMENTS, `STATUS`,ADD_TIME,
    CREATOR, UPDATE_TIME,
    UPDATER, `DELETE`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_TODO_LIST
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectUnHandledByBuzTypeAndBuzId" resultType="com.vedeng.todolist.model.TodoList">
    SELECT * FROM T_TODO_LIST WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_ID = #{buzId,jdbcType=INTEGER}
  </select>
  <select id="countUnHandledItem" resultType="int" >
    SELECT count(*) FROM T_TODO_LIST WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_ID = #{buzId,jdbcType=INTEGER} AND STATUS=0
  </select>
  <select id="getUnHandledRiskCheckSkuTodoListByUserList" resultType="com.vedeng.todolist.model.TodoList">
    SELECT
      *,
      (
        SELECT P.ORG_ID
        FROM T_R_USER_POSIT RP
        LEFT JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
        LEFT JOIN T_ORGANIZATION OA ON OA.ORG_ID = P.ORG_ID
        WHERE RP.USER_ID = P.ASSIGNMENT_MANAGER_ID AND P.TYPE = 311 LIMIT 1
      ) AS MANAGER_ORG_ID,
      (
        SELECT P.ORG_ID
        FROM T_R_USER_POSIT RP
        LEFT JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
        LEFT JOIN T_ORGANIZATION OA ON OA.ORG_ID = P.ORG_ID
        WHERE RP.USER_ID = P.ASSIGNMENT_ASSISTANT_ID AND P.TYPE = 311 LIMIT 1
      ) AS ASSISTANT_ORG_ID,
      K.GOODS_LEVEL_NO
    FROM T_TODO_LIST T
    JOIN V_CORE_SKU K ON T.BUZ_ID = K.SKU_ID
    JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    WHERE T.BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND T.STATUS = 0
    <if test="userList.size > 0">
      AND ( P.ASSIGNMENT_MANAGER_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
        OR
        P.ASSIGNMENT_ASSISTANT_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      )
    </if>

      AND T.ADD_TIME >unix_timestamp(NOW())*1000 -90*24*60*60*1000


  </select>

  <select id="getUnHandledSkuPushTodoList" resultType="com.vedeng.todolist.model.TodoList">
    SELECT
    *,
    (
      SELECT P.ORG_ID
      FROM T_R_USER_POSIT RP
      LEFT JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
      LEFT JOIN T_ORGANIZATION OA ON OA.ORG_ID = P.ORG_ID
      WHERE RP.USER_ID = P.ASSIGNMENT_MANAGER_ID AND P.TYPE = 311 LIMIT 1
    ) AS MANAGER_ORG_ID,
    (
      SELECT P.ORG_ID
      FROM T_R_USER_POSIT RP
      LEFT JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
      LEFT JOIN T_ORGANIZATION OA ON OA.ORG_ID = P.ORG_ID
      WHERE RP.USER_ID = P.ASSIGNMENT_ASSISTANT_ID AND P.TYPE = 311 LIMIT 1
    ) AS ASSISTANT_ORG_ID,
    K.GOODS_LEVEL_NO
    FROM T_TODO_LIST T
    <if test="userList.size > 0">
      JOIN V_CORE_SKU K ON T.BUZ_ID = K.SKU_ID
      JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    </if>
    WHERE T.BUZ_TYPE in
    <foreach collection="sceneIdList" item="sceneId" index="index" open="(" close=")" separator=",">
      #{sceneId,jdbcType=INTEGER}
    </foreach>
    AND T.STATUS = 0
    <if test="userList.size > 0">
      AND ( P.ASSIGNMENT_MANAGER_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      OR
      P.ASSIGNMENT_ASSISTANT_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      )
    </if>

      AND T.ADD_TIME >unix_timestamp(NOW())*1000 -90*24*60*60*1000
  </select>


  <select id="getHandledRiskCheckSkuTodoListByUserList" resultType="com.vedeng.todolist.model.TodoList">
    SELECT
      *,
      (
        SELECT P.ORG_ID
        FROM T_R_USER_POSIT RP
        LEFT JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
        LEFT JOIN T_ORGANIZATION OA ON OA.ORG_ID = P.ORG_ID
        WHERE RP.USER_ID = P.ASSIGNMENT_MANAGER_ID AND P.TYPE = 311 LIMIT 1
      ) AS MANAGER_ORG_ID,
      (
        SELECT P.ORG_ID
        FROM T_R_USER_POSIT RP
        LEFT JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
        LEFT JOIN T_ORGANIZATION OA ON OA.ORG_ID = P.ORG_ID
        WHERE RP.USER_ID = P.ASSIGNMENT_ASSISTANT_ID AND P.TYPE = 311 LIMIT 1
      ) AS ASSISTANT_ORG_ID
    FROM T_TODO_LIST T
    <if test="userList.size > 0">
      JOIN V_CORE_SKU K ON T.BUZ_ID = K.SKU_ID
      JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    </if>
    WHERE T.BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND T.STATUS = 1 and T.UPDATE_TIME > #{firstDayTimestamp,jdbcType=BIGINT}
    <if test="userList.size > 0">
      AND ( P.ASSIGNMENT_MANAGER_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      OR
      P.ASSIGNMENT_ASSISTANT_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      )
    </if>

      AND T.ADD_TIME >unix_timestamp(NOW())*1000 -90*24*60*60*1000

  </select>

  <select id="getUnHandledRiskCheckTraderSupplyTodoListByUserList"
          resultType="com.vedeng.todolist.model.TodoList">
    SELECT
      *,
      P.ASSIGNMENT_MANAGER_ID,
      P.ASSIGNMENT_ASSISTANT_ID
    FROM T_TODO_LIST T
    <if test="userList.size > 0">
      JOIN T_BUYORDER B ON T.BUZ_EXTRA = B.BUYORDER_NO
      JOIN T_BUYORDER_GOODS BG ON B.BUYORDER_ID = BG.BUYORDER_ID and BG.IS_DELETE = 0
      JOIN V_CORE_SKU K ON BG.SKU = K.SKU_NO
      JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    </if>
    WHERE T.BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND T.STATUS = 0
    <if test="userList.size > 0">
      AND ( P.ASSIGNMENT_MANAGER_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      OR
      P.ASSIGNMENT_ASSISTANT_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      )
    </if>
  </select>
  <select id="getUnHandledMaintainTodoListCountByUserListGroupByGrade" resultType="com.vedeng.todolist.model.TodoList">
    SELECT K.GOODS_LEVEL_NO AS ID, COUNT(*) AS BUZ_ID
    FROM T_TODO_LIST T
      JOIN V_CORE_SKU K ON T.BUZ_ID = K.SKU_ID AND K.GOODS_LEVEL_NO > 0
    <if test="userList.size > 0">
      JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    </if>
    WHERE T.BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND T.STATUS = 0
    <if test="userList.size > 0">
      AND ( P.ASSIGNMENT_MANAGER_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      OR
      P.ASSIGNMENT_ASSISTANT_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      )
    </if>
    GROUP BY K.GOODS_LEVEL_NO
  </select>
  <select id="getUnHandledTodoListCountByBuzTypeListAndPropertyGroupByBuzExtra" resultType="java.lang.Integer">
    SELECT COUNT(DISTINCT BUZ_EXTRA) FROM T_TODO_LIST WHERE STATUS = 0 AND BUZ_PROPERTY = #{buzProperty,jdbcType=VARCHAR} AND BUZ_TYPE IN
    <foreach collection="buzTypeList" index="index" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="getUnHandledMaintainTodoListSkuByUserList" resultType="com.vedeng.todolist.model.TodoList">
    SELECT K.SKU_NO AS BUZ_EXTRA, K.GOODS_LEVEL_NO AS BUZ_TYPE
    FROM T_TODO_LIST T
    JOIN V_CORE_SKU K ON T.BUZ_ID = K.SKU_ID AND K.GOODS_LEVEL_NO > 0
    <if test="userList.size > 0">
      JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    </if>
    WHERE T.BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND T.STATUS = 0
    <if test="userList.size > 0">
      AND ( P.ASSIGNMENT_MANAGER_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      OR
      P.ASSIGNMENT_ASSISTANT_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      )
    </if>
  </select>
  <select id="getUnHandledTodoListByBuzTypeListAndBuzExtra" resultType="com.vedeng.todolist.model.TodoList">
    SELECT * FROM T_TODO_LIST WHERE BUZ_EXTRA = #{buzExtra,jdbcType=VARCHAR} AND BUZ_TYPE IN
    <foreach collection="buzTypeList" index="index" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="selectUnHandledByBuzTypeAndBuzIdAndBuzExtra" resultType="com.vedeng.todolist.model.TodoList">
    SELECT * FROM T_TODO_LIST WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_ID = #{buzId,jdbcType=INTEGER} AND BUZ_EXTRA = #{buzExtra,jdbcType=VARCHAR}
  </select>
  <select id="getUnHandledCountByBuzType" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM T_TODO_LIST WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND STATUS = 0
  </select>

  <select id="getUnHandledListByBuzType" resultType="com.vedeng.todolist.model.TodoList">
    SELECT * FROM T_TODO_LIST WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND STATUS = 0
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_TODO_LIST
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByBuzTypeAndBuzId">
    DELETE FROM T_TODO_LIST WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_ID = #{buzId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByBuzTypeAndBuzIdAndBuzExtra">
    DELETE FROM T_TODO_LIST WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_ID = #{buzId,jdbcType=INTEGER} AND BUZ_EXTRA =
    #{buzExtra,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteByBuzTypeAndBuzExtra">
    DELETE FROM T_TODO_LIST WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_EXTRA = #{buzExtra,jdbcType=VARCHAR}
  </delete>


  <insert id="insert" parameterType="com.vedeng.todolist.model.TodoList">
    insert into T_TODO_LIST (ID, BUZ_TYPE, BUZ_ID, 
      BUZ_EXTRA, BUZ_REDIRECT, BUZ_PROPERTY,CHECK_STATUS,COMMENTS, `STATUS`,
      ADD_TIME, CREATOR, UPDATE_TIME,
      UPDATER, `DELETE`)
    values (#{id,jdbcType=INTEGER}, #{buzType,jdbcType=INTEGER}, #{buzId,jdbcType=INTEGER}, 
      #{buzExtra,jdbcType=VARCHAR},
      #{buzRedirect,jdbcType=VARCHAR},#{buzProperty,jdbcType=VARCHAR},#{checkStatus,jdbcType=INTEGER},#{comments,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
      #{updateTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER}, #{delete,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.todolist.model.TodoList" useGeneratedKeys="true" keyProperty="id">
    insert into T_TODO_LIST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="buzType != null">
        BUZ_TYPE,
      </if>
      <if test="buzId != null">
        BUZ_ID,
      </if>
      <if test="buzExtra != null">
        BUZ_EXTRA,
      </if>
      <if test="buzRedirect != null">
        BUZ_REDIRECT,
      </if>
      <if test="buzProperty != null">
        BUZ_PROPERTY,
      </if>
      <if test="checkStatus != null">
        `CHECK_STATUS`,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="delete != null">
        `DELETE`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="buzType != null">
        #{buzType,jdbcType=INTEGER},
      </if>
      <if test="buzId != null">
        #{buzId,jdbcType=INTEGER},
      </if>
      <if test="buzExtra != null">
        #{buzExtra,jdbcType=VARCHAR},
      </if>
      <if test="buzRedirect != null">
        #{buzRedirect,jdbcType=VARCHAR},
      </if>
      <if test="buzProperty != null">
        #{buzProperty,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="delete != null">
        #{delete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.todolist.model.TodoList">
    update T_TODO_LIST
    <set>
      <if test="buzType != null">
        BUZ_TYPE = #{buzType,jdbcType=INTEGER},
      </if>
      <if test="buzId != null">
        BUZ_ID = #{buzId,jdbcType=INTEGER},
      </if>
      <if test="buzExtra != null">
        BUZ_EXTRA = #{buzExtra,jdbcType=VARCHAR},
      </if>
      <if test="buzRedirect != null">
        BUZ_REDIRECT = #{buzRedirect,jdbcType=VARCHAR},
      </if>
      <if test="buzProperty != null">
        BUZ_PROPERTY = #{buzProperty,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        `CHECK_STATUS` = #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="delete != null">
        `DELETE` = #{delete,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.todolist.model.TodoList">
    update T_TODO_LIST
    set BUZ_TYPE = #{buzType,jdbcType=INTEGER},
      BUZ_ID = #{buzId,jdbcType=INTEGER},
      BUZ_EXTRA = #{buzExtra,jdbcType=VARCHAR},
      BUZ_REDIRECT = #{buzRedirect,jdbcType=VARCHAR},
      BUZ_PROPERTY = #{buzProperty,jdbcType=VARCHAR},
      CHECK_STATUS = #{checkStatus,jdbcType=INTEGER},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      `DELETE` = #{delete,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateStatusByBuzTypeAndBuzId">
    UPDATE T_TODO_LIST
    SET
      STATUS = #{status,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_ID = #{buzId,jdbcType=INTEGER}
  </update>
  <update id="finishZlbTodoList">
    UPDATE T_TODO_LIST SET STATUS = 1, CHECK_STATUS = #{checkStatus,jdbcType=INTEGER}, UPDATE_TIME =
    #{updateTime,jdbcType=BIGINT} WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_ID = #{buzId,jdbcType=INTEGER} AND STATUS = 0
  </update>

  <update id="updateStatusByBuzTypeAndBuzIdAndOrderNo">
    UPDATE T_TODO_LIST
    SET STATUS = #{status,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
        UPDATER = #{updater,jdbcType=INTEGER}
    WHERE BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND BUZ_ID = #{buzId,jdbcType=INTEGER} AND BUZ_EXTRA = #{orderNo,jdbcType=VARCHAR}
  </update>

  <select id="getTodoListByExtraProperty" resultType="com.vedeng.todolist.model.TodoList">
    SELECT
      L.*
    FROM
      T_TODO_LIST L
    WHERE
      L.BUZ_EXTRA=#{orderNo,jdbcType=VARCHAR} AND BUZ_PROPERTY=#{property,jdbcType=VARCHAR}
  </select>

  <select id="getOrderList" resultType="com.vedeng.todolist.model.TodoList">
    SELECT
      L.*
    FROM
      T_TODO_LIST L
    WHERE
      L.BUZ_ID=#{buzId,jdbcType=INTEGER} AND L.BUZ_PROPERTY=#{property,jdbcType=VARCHAR} AND L.STATUS = #{status,jdbcType=INTEGER}
  </select>

  <select id="getHandledRiskCheckTraderSupplyTodoListByUserList"
          resultType="com.vedeng.todolist.model.TodoList">
    SELECT
      *,
      P.ASSIGNMENT_MANAGER_ID,
      P.ASSIGNMENT_ASSISTANT_ID
    FROM T_TODO_LIST T
    <if test="userList.size > 0">
      JOIN T_BUYORDER B ON T.BUZ_EXTRA = B.BUYORDER_NO
      JOIN T_BUYORDER_GOODS BG ON B.BUYORDER_ID = BG.BUYORDER_ID and BG.IS_DELETE = 0
      JOIN V_CORE_SKU K ON BG.SKU = K.SKU_NO
      JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    </if>
    WHERE T.BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND T.STATUS = 1 and T.UPDATE_TIME > #{firstDayTimestamp,jdbcType=BIGINT}
    <if test="userList.size > 0">
      AND ( P.ASSIGNMENT_MANAGER_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      OR
      P.ASSIGNMENT_ASSISTANT_ID IN
      <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
      )
    </if>
  </select>

  <select id="getAllRiskCheckSkuTodoListGroupByAssitId" resultType="com.vedeng.todolist.dto.PersonalTodoDealingInfo">
    SELECT
        ASSSISTANT_ID userId,
        SUM(dealtime) totalDealTime,
        COUNT(*) num
    FROM
    (
        SELECT
               P.ASSIGNMENT_ASSISTANT_ID AS ASSSISTANT_ID,
              (REPLACE(UNIX_TIMESTAMP(CURRENT_TIMESTAMP(3)),'.','') -
                if(FROM_UNIXTIME(T.ADD_TIME / 1000, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m'),
                    T.ADD_TIME,
                    UNIX_TIMESTAMP(date_add(current_date(), interval - day(current_date()) + 1 day)) * 1000)) AS dealtime
            FROM T_TODO_LIST T
            JOIN V_CORE_SKU K ON T.BUZ_ID = K.SKU_ID
            JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
            WHERE T.BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND T.STATUS = 0
        UNION ALL
        SELECT
             P.ASSIGNMENT_ASSISTANT_ID AS ASSSISTANT_ID,
             (T.UPDATE_TIME -
                if(FROM_UNIXTIME(T.ADD_TIME / 1000, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m'),
                    T.ADD_TIME,
                    UNIX_TIMESTAMP(date_add(current_date(), interval - day(current_date()) + 1 day)) * 1000)) AS dealtime
            FROM T_TODO_LIST T
            JOIN V_CORE_SKU K ON T.BUZ_ID = K.SKU_ID
            JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
            WHERE T.BUZ_TYPE = #{buzType,jdbcType=INTEGER} AND T.STATUS = 1 AND T.ADD_TIME > #{firstDayTimestamp,jdbcType=BIGINT}
    )temp
    WHERE ASSSISTANT_ID IS NOT NULL
    GROUP BY ASSSISTANT_ID
    HAVING IFNULL(SUM(dealtime),0) > 0
  </select>

  <select id="getSupplyLicenseNum" resultType="java.lang.Integer">
    SELECT COUNT(DISTINCT(A.TRADER_SUPPLIER_ID)) FROM T_TRADER_SUPPLIER A,T_TRADER_CERTIFICATE B,T_VERIFIES_INFO C
    WHERE A.IS_ENABLE = 1 AND A.TRADER_ID = B.TRADER_ID
    AND B.SYS_OPTION_DEFINITION_ID = #{searchType,jdbcType=INTEGER} AND B.IS_DELETE = 0 AND B.ENDTIME IS NOT NULL
    AND B.ENDTIME != 0
    AND B.ENDTIME &lt;= #{searchDate,jdbcType=BIGINT}
    AND C.RELATE_TABLE_KEY = A.TRADER_SUPPLIER_ID AND C.RELATE_TABLE = 'T_TRADER_SUPPLIER' and C.VERIFIES_TYPE = 619 AND C.STATUS = 1
  </select>

  <select id="getTraderLicenseNum" resultType="java.lang.Integer">
    SELECT COUNT(DISTINCT(A.TRADER_CUSTOMER_ID)) FROM T_TRADER_CUSTOMER A,T_TRADER_CERTIFICATE B,T_VERIFIES_INFO C
    WHERE A.IS_ENABLE = 1 AND A.TRADER_ID = B.TRADER_ID
    AND B.SYS_OPTION_DEFINITION_ID = #{searchType,jdbcType=INTEGER} AND B.IS_DELETE = 0 AND B.ENDTIME IS NOT NULL
    AND B.ENDTIME != 0
    AND B.ENDTIME &lt;= #{searchDate,jdbcType=BIGINT}
    AND C.RELATE_TABLE_KEY = A.TRADER_CUSTOMER_ID AND C.RELATE_TABLE='T_TRADER_CUSTOMER' AND C.VERIFIES_TYPE = 617 AND C.STATUS = 1
  </select>



  <select id="getReceiptRecordCountGroupByCategoryCount" resultType="java.lang.Integer">
    SELECT count(DISTINCT b.BUYORDER_ID)
    FROM
    T_BUYORDER b
      JOIN T_BUYORDER_GOODS bg ON b.BUYORDER_ID = bg.BUYORDER_ID
      LEFT JOIN (
    SELECT ed.RELATED_ID,
           ed.NUM - sum(ifnull(pd.ARRIVAL_COUNT, 0)) to_maintain_count
    FROM T_EXPRESS_DETAIL ed
           JOIN T_EXPRESS e ON ed.BUSINESS_TYPE = 515
      AND ed.EXPRESS_ID = e.EXPRESS_ID
      AND e.IS_ENABLE = 1
           LEFT JOIN T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL pd ON ed.EXPRESS_DETAIL_ID = pd.EXPRESS_DETAIL_ID
    where ed.RELATED_ID in (select TBG.BUYORDER_GOODS_ID
                            from T_BUYORDER TB
                                   left join T_BUYORDER_GOODS TBG on TB.BUYORDER_ID = TBG.BUYORDER_ID
    where TB.CREATOR IN
    <foreach collection="subUserIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and TB.ADD_TIME > #{peerListFilterTime}
    and TB.DELIVERY_DIRECT = 1
    and TB.LOCKED_STATUS = 0
    AND TB.DELIVERY_STATUS IN (1, 2)
    <if test="isUrgent != null">
      AND TB.URGED_MAINTAIN_BATCH_INFO = #{isUrgent}
    </if>
    <if test="orgId != null">
      and TB.ORG_ID = #{orgId,jdbcType=INTEGER}
    </if>
    )
    GROUP BY ed.EXPRESS_DETAIL_ID
    ) a ON bg.BUYORDER_GOODS_ID = a.RELATED_ID
      where a.to_maintain_count > 0
    and b.ADD_TIME > #{peerListFilterTime}
    and b.DELIVERY_DIRECT = 1
    and b.LOCKED_STATUS = 0
    AND b.DELIVERY_STATUS IN (1, 2)
    AND b.CREATOR IN
    <foreach collection="subUserIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>



  <select id="byReceiptRecordCountGroupByCategoryGetOrgIds" resultType="java.lang.Integer">
    SELECT DISTINCT b.ORG_ID
    FROM
    T_BUYORDER b
    JOIN T_BUYORDER_GOODS bg ON b.BUYORDER_ID = bg.BUYORDER_ID
    LEFT JOIN (
    SELECT ed.RELATED_ID,
    ed.NUM - sum(ifnull(pd.ARRIVAL_COUNT, 0)) to_maintain_count
    FROM T_EXPRESS_DETAIL ed
    JOIN T_EXPRESS e ON ed.BUSINESS_TYPE = 515
    AND ed.EXPRESS_ID = e.EXPRESS_ID
    AND e.IS_ENABLE = 1
    LEFT JOIN T_PURCHASE_DELIVERY_DIRECT_BATCH_DETAIL pd ON ed.EXPRESS_DETAIL_ID = pd.EXPRESS_DETAIL_ID
    where ed.RELATED_ID in (select TBG.BUYORDER_GOODS_ID
    from T_BUYORDER TB
    left join T_BUYORDER_GOODS TBG on TB.BUYORDER_ID = TBG.BUYORDER_ID
    where TB.CREATOR IN
    <foreach collection="subUserIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and TB.ADD_TIME > #{peerListFilterTime}
    and TB.DELIVERY_DIRECT = 1
    and TB.LOCKED_STATUS = 0
    AND TB.DELIVERY_STATUS IN (1, 2)
    )
    GROUP BY ed.EXPRESS_DETAIL_ID
    ) a ON bg.BUYORDER_GOODS_ID = a.RELATED_ID
    where a.to_maintain_count > 0
    and b.ADD_TIME > #{peerListFilterTime}
    and b.DELIVERY_DIRECT = 1
    and b.LOCKED_STATUS = 0
    AND b.DELIVERY_STATUS IN (1, 2)
    AND b.CREATOR IN
    <foreach collection="subUserIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>


  <select id="getCrmTaskCount" resultType="java.lang.Integer">
    select count(*)
    from T_TASK
    where CREATOR  = #{userId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>

  <select id="getCrmTodoTaskCount" resultType="java.lang.Integer">
    select count(*)
    from T_TASK_ITEM
    where TASK_USER  = #{userId,jdbcType=INTEGER}
    and DONE_STATUS = 0
    and IS_DELETE = 0
  </select>
</mapper>