package com.newtask;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.QuoteorderConsultDetailMapper;
import com.vedeng.order.dao.QuoteorderConsultMapper;
import com.vedeng.order.model.QuoteorderConsult;
import com.vedeng.order.model.QuoteorderConsultDetail;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@JobHandler(value = "quoteorderConsultDetailInitTask")
public class QuoteorderConsultDetailInitTask extends AbstractJobHandler {
    
    private Logger logger = LoggerFactory.getLogger(SkuOnSaleSyncTask.class);

    public static final Integer PAGE_SIZE = 1000;

    @Resource
    private QuoteorderConsultMapper quoteorderConsultMapper;

    @Resource
    private QuoteorderConsultDetailMapper quoteorderConsultDetailMapper;
    
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        int currentPage = ErpConst.ONE;
        int maxPage;
        Map<String, Object> pageParam = new HashMap<String, Object>();
        Page page = new Page(currentPage,PAGE_SIZE);
        
        quoteorderConsultDetailMapper.deleteAll();
        
        do{
            page.setPageNo(currentPage);
            pageParam.put("page",page);
            List<QuoteorderConsult> quoteorderConsultPageList = quoteorderConsultMapper.getAllQuotedConsultlistpage(pageParam);
            maxPage=page.getTotalPage();
            currentPage++;
            dealQuoteorderConsultList(quoteorderConsultPageList);
        } while (currentPage <= maxPage);
        
        return ReturnT.SUCCESS;
    }

    private void dealQuoteorderConsultList(List<QuoteorderConsult> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }

        List<QuoteorderConsultDetail> quoteorderConsultDetailList = new ArrayList<>();
        QuoteorderConsultDetail quoteorderConsultDetail = null;
        for(QuoteorderConsult quoteorderConsult : list){
            List<String> contentList = Arrays.asList(quoteorderConsult.getContent().split("<br/>").clone());
            for(String content : contentList){
                quoteorderConsultDetail = new QuoteorderConsultDetail();
                quoteorderConsultDetail.setQuoteorderId(quoteorderConsult.getQuoteorderId());
                quoteorderConsultDetail.setQuoteorderConsultId(quoteorderConsult.getQuoteorderConsultId());
                quoteorderConsultDetail.setQuoteConsultType(quoteorderConsult.getType());
                quoteorderConsultDetail.setAddTime(DateUtil.gainNowDate());
                quoteorderConsultDetail.setCreator(2);
                setSkuNoAndOtherContent(quoteorderConsultDetail,content);
                quoteorderConsultDetailList.add(quoteorderConsultDetail);
            }
        }
        if(CollectionUtils.isNotEmpty(quoteorderConsultDetailList)){
            quoteorderConsultDetailMapper.batchInsert(quoteorderConsultDetailList);
        }
    }

    private void setSkuNoAndOtherContent(QuoteorderConsultDetail quoteorderConsultDetail, String content) {
        if(content.length() < 9 || !content.startsWith("【V")){
            quoteorderConsultDetail.setSkuNo(null);
            quoteorderConsultDetail.setOtherContent(content);
        }else {
            quoteorderConsultDetail.setSkuNo(content.substring(1,8));
            quoteorderConsultDetail.setOtherContent(content.length() == 9 ? null : content.substring(9));
        }
    }

}
