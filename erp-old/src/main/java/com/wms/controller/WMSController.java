package com.wms.controller;

import cn.hutool.core.text.UnicodeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wms.dto.WMSResultInfo;
import com.wms.dto.WmsRequestDto;
import com.wms.service.WmsInterfaceDispatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.UUID;

@Controller
@RequestMapping("/wms")
public class WMSController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WMSController.class);

    @Resource
    private WmsInterfaceDispatcher wmsInterfaceDispatcher;

    @Value("${customerIdConfig:VEDENG}")
    private String customerIdConfig;


    @ResponseBody
    @RequestMapping(value="/wmsCommonApi",method = RequestMethod.POST)
    public JSONObject wmsCommonApi(@RequestParam("method") String method,
                                   @RequestParam("client_customerid") String clientCustomerid,
                                   @RequestParam("messageid") String messageId,
                                   @RequestParam("client_db") String clientDb,
                                   @RequestParam("appkey") String appkey,
                                   @RequestParam("apptoken") String apptoken,
                                   @RequestParam("data") String data){

        JSONObject responseObj = null;

        String requestId = UUID.randomUUID().toString();

        try{

            WmsRequestDto wmsRequestDto  = convertToWmsRequestDto(method,clientCustomerid,messageId,clientDb,appkey,apptoken,data);



            LOGGER.info("WMSController->wmsCommonApi:wms请求requestId:"+requestId+",入参======================:" + JSON.toJSONString(wmsRequestDto));

            if(!validateCustomerId(wmsRequestDto.getData())){
                LOGGER.error("wms回传的报文里没有客户ID。");
            }


            if(!"WMS".equals(wmsRequestDto.getClient_customerid())){
                return convertToResponse(WMSResultInfo.error("client_customerid参数错误"));
            }

            if(!"ERPDB".equals(wmsRequestDto.getClient_db())){
                return convertToResponse(WMSResultInfo.error("client_db参数错误"));
            }

            wmsInterfaceDispatcher.disPatch(wmsRequestDto);

            responseObj = convertToResponse(WMSResultInfo.success());

        }catch (Exception e){

            LOGGER.error("WMSController->wmsCommonApi error:",e);

            responseObj = convertToResponse(WMSResultInfo.error(e.getMessage()));
        }

        LOGGER.info("WMSController->wmsCommonApi:wms请求requestId:"+requestId+",响应======================:" + JSON.toJSON(responseObj));

        return responseObj;
    }

    public WmsRequestDto convertToWmsRequestDto(String method, String clientCustomerid, String messageId,
                                                String clientDb, String appkey,
                                                String apptoken, String data) throws UnsupportedEncodingException {

        WmsRequestDto wmsRequestDto = new WmsRequestDto();
        wmsRequestDto.setMethod(method);
        wmsRequestDto.setClient_customerid(clientCustomerid);
        wmsRequestDto.setMessageid(messageId);
        wmsRequestDto.setClient_db(clientDb);
        wmsRequestDto.setAppkey(appkey);
        wmsRequestDto.setApptoken(apptoken);
        wmsRequestDto.setData(UnicodeUtil.toString(data));

        return wmsRequestDto;
    }

    private JSONObject convertToResponse(WMSResultInfo resultInfo) {

        JSONObject returnJsonObj = new JSONObject();
        returnJsonObj.put("return", resultInfo);

        JSONObject responseJsonObj = new JSONObject();
        responseJsonObj.put("Response",returnJsonObj);

        return responseJsonObj;
    }

    private boolean validateCustomerId(String data){
        String customerId = extractCustomerId(data);
        String[] customerIdConfigArray = customerIdConfig.split(",");

        for(String configValue : customerIdConfigArray){
            if(configValue.equals(customerId)){
                return true;
            }
        }
        return false;
    }


    private String extractCustomerId(String data) {
        try {
            JSONObject dataObj = JSON.parseObject(data);
            JSONObject xmldata = dataObj.getJSONObject("xmldata");
            if (xmldata != null && xmldata.getJSONArray("header") != null
                    && xmldata.getJSONArray("header").size() > 0) {
                return xmldata.getJSONArray("header")
                        .getJSONObject(0)
                        .getString("CustomerID");
            }
        } catch (Exception e) {
            LOGGER.error("Failed to extract CustomerID from data", e);
        }
        return null;
    }

}
