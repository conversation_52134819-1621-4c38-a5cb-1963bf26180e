<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.SettlementBillItemMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.SettlementBillItemEntity">
    <!--@mbg.generated-->
    <!--@Table T_SETTLEMENT_BILL_ITEM-->
    <id column="SETTLE_ITEM_BILL_ID" jdbcType="INTEGER" property="settleItemBillId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="SETTLE_BILL_ID" jdbcType="INTEGER" property="settleBillId" />
    <result column="SETTLE_BILL_NO" jdbcType="VARCHAR" property="settleBillNo" />
    <result column="TRADER_DIRECTION" jdbcType="INTEGER" property="traderDirection" />
    <result column="SETTLEMENT_TYPE" jdbcType="INTEGER" property="settlementType" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="BUSINESS_ID" jdbcType="INTEGER" property="businessId" />
    <result column="BUSINESS_ITEM_ID" jdbcType="INTEGER" property="businessItemId" />
    <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="NUMBER" jdbcType="DECIMAL" property="number" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus" />
    <result column="SETTLEMENT_STATUS" jdbcType="INTEGER" property="settlementStatus" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SETTLE_ITEM_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    SETTLE_BILL_ID, SETTLE_BILL_NO, TRADER_DIRECTION, SETTLEMENT_TYPE, BUSINESS_TYPE, 
    BUSINESS_NO, BUSINESS_ID, BUSINESS_ITEM_ID, PRODUCT_NAME, PRICE, `NUMBER`, AMOUNT, 
    INVOICE_STATUS, SETTLEMENT_STATUS, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SETTLEMENT_BILL_ITEM
    where SETTLE_ITEM_BILL_ID = #{settleItemBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SETTLEMENT_BILL_ITEM
    where SETTLE_ITEM_BILL_ID = #{settleItemBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SETTLE_ITEM_BILL_ID" keyProperty="settleItemBillId" parameterType="com.vedeng.erp.settlement.domain.entity.SettlementBillItemEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL_ITEM (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      SETTLE_BILL_ID, SETTLE_BILL_NO, TRADER_DIRECTION, 
      SETTLEMENT_TYPE, BUSINESS_TYPE, BUSINESS_NO, 
      BUSINESS_ID, BUSINESS_ITEM_ID, PRODUCT_NAME, 
      PRICE, `NUMBER`, AMOUNT, 
      INVOICE_STATUS, SETTLEMENT_STATUS, IS_DELETE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{settleBillId,jdbcType=INTEGER}, #{settleBillNo,jdbcType=VARCHAR}, #{traderDirection,jdbcType=INTEGER}, 
      #{settlementType,jdbcType=INTEGER}, #{businessType,jdbcType=VARCHAR}, #{businessNo,jdbcType=VARCHAR}, 
      #{businessId,jdbcType=INTEGER}, #{businessItemId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR}, 
      #{price,jdbcType=DECIMAL}, #{number,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL}, 
      #{invoiceStatus,jdbcType=INTEGER}, #{settlementStatus,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SETTLE_ITEM_BILL_ID" keyProperty="settleItemBillId" parameterType="com.vedeng.erp.settlement.domain.entity.SettlementBillItemEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="settleBillId != null">
        SETTLE_BILL_ID,
      </if>
      <if test="settleBillNo != null and settleBillNo != ''">
        SETTLE_BILL_NO,
      </if>
      <if test="traderDirection != null">
        TRADER_DIRECTION,
      </if>
      <if test="settlementType != null">
        SETTLEMENT_TYPE,
      </if>
      <if test="businessType != null and businessType != ''">
        BUSINESS_TYPE,
      </if>
      <if test="businessNo != null and businessNo != ''">
        BUSINESS_NO,
      </if>
      <if test="businessId != null">
        BUSINESS_ID,
      </if>
      <if test="businessItemId != null">
        BUSINESS_ITEM_ID,
      </if>
      <if test="productName != null and productName != ''">
        PRODUCT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="number != null">
        `NUMBER`,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS,
      </if>
      <if test="settlementStatus != null">
        SETTLEMENT_STATUS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="settleBillId != null">
        #{settleBillId,jdbcType=INTEGER},
      </if>
      <if test="settleBillNo != null and settleBillNo != ''">
        #{settleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderDirection != null">
        #{traderDirection,jdbcType=INTEGER},
      </if>
      <if test="settlementType != null">
        #{settlementType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null and businessType != ''">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null and businessNo != ''">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessItemId != null">
        #{businessItemId,jdbcType=INTEGER},
      </if>
      <if test="productName != null and productName != ''">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        #{number,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.SettlementBillItemEntity">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL_ITEM
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="settleBillId != null">
        SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER},
      </if>
      <if test="settleBillNo != null and settleBillNo != ''">
        SETTLE_BILL_NO = #{settleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderDirection != null">
        TRADER_DIRECTION = #{traderDirection,jdbcType=INTEGER},
      </if>
      <if test="settlementType != null">
        SETTLEMENT_TYPE = #{settlementType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null and businessType != ''">
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null and businessNo != ''">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessItemId != null">
        BUSINESS_ITEM_ID = #{businessItemId,jdbcType=INTEGER},
      </if>
      <if test="productName != null and productName != ''">
        PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        `NUMBER` = #{number,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="settlementStatus != null">
        SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where SETTLE_ITEM_BILL_ID = #{settleItemBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.SettlementBillItemEntity">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL_ITEM
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER},
      SETTLE_BILL_NO = #{settleBillNo,jdbcType=VARCHAR},
      TRADER_DIRECTION = #{traderDirection,jdbcType=INTEGER},
      SETTLEMENT_TYPE = #{settlementType,jdbcType=INTEGER},
      BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      BUSINESS_ITEM_ID = #{businessItemId,jdbcType=INTEGER},
      PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      `NUMBER` = #{number,jdbcType=DECIMAL},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
      SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where SETTLE_ITEM_BILL_ID = #{settleItemBillId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL_ITEM
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SETTLE_BILL_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settleBillId != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.settleBillId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SETTLE_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settleBillNo != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.settleBillNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_DIRECTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderDirection != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.traderDirection,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SETTLEMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settlementType != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.settlementType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessType != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.businessType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessNo != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.businessNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessId != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.businessId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_ITEM_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessItemId != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.businessItemId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productName != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.productName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.price != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="`NUMBER` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.number != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.number,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amount != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.amount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceStatus != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.invoiceStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SETTLEMENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settlementStatus != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.settlementStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when SETTLE_ITEM_BILL_ID = #{item.settleItemBillId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where SETTLE_ITEM_BILL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.settleItemBillId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SETTLE_ITEM_BILL_ID" keyProperty="settleItemBillId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL_ITEM
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, SETTLE_BILL_ID,
      SETTLE_BILL_NO, TRADER_DIRECTION, SETTLEMENT_TYPE, BUSINESS_TYPE, BUSINESS_NO, 
      BUSINESS_ID, BUSINESS_ITEM_ID, PRODUCT_NAME, PRICE, `NUMBER`, AMOUNT, INVOICE_STATUS, 
      SETTLEMENT_STATUS, IS_DELETE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
        #{item.settleBillId,jdbcType=INTEGER}, #{item.settleBillNo,jdbcType=VARCHAR}, #{item.traderDirection,jdbcType=INTEGER}, 
        #{item.settlementType,jdbcType=INTEGER}, #{item.businessType,jdbcType=VARCHAR}, 
        #{item.businessNo,jdbcType=VARCHAR}, #{item.businessId,jdbcType=INTEGER}, #{item.businessItemId,jdbcType=INTEGER}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL}, #{item.number,jdbcType=DECIMAL}, 
        #{item.amount,jdbcType=DECIMAL}, #{item.invoiceStatus,jdbcType=INTEGER}, #{item.settlementStatus,jdbcType=INTEGER},
        #{item.isDelete,jdbcType=INTEGER})
    </foreach>
  </insert>

    <!--auto generated by MybatisCodeHelper on 2023-11-28-->
    <select id="findBySettleBillId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SETTLEMENT_BILL_ITEM
        where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
    </select>


    <!--auto generated by MybatisCodeHelper on 2023-11-29-->
    <select id="findByBusinessIdAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SETTLEMENT_BILL_ITEM
        where BUSINESS_ID = #{businessId,jdbcType=INTEGER}
          and BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
    </select>


    <!--auto generated by MybatisCodeHelper on 2023-11-30-->
    <delete id="deleteBySettleItemBillIdIn">
        delete
        from T_SETTLEMENT_BILL_ITEM
        where SETTLE_ITEM_BILL_ID in
        <foreach close=")" collection="settleItemBillIdCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>