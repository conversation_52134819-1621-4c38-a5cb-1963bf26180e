package com.vedeng.common.trace.track;

import com.vedeng.common.trace.data.TrackParamsData;
import org.springframework.context.ApplicationEvent;

/**
 * 事件发布的对象
 * @ClassName:  EventTrackingEvent
 * @author: <PERSON><PERSON>yang
 * @date:   2024年6月3日 下午3:20:20
 * @Copyright:
 */
public class EventTrackingEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    public EventTrackingEvent(TrackParamsData trackParamsData) {
        super(trackParamsData);
    }
}
