<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.QuotedAlarmLogMapper">
    <insert id="insertSelective" parameterType="com.vedeng.order.model.QuotedAlarmLogDo">
        insert into T_QUOTED_ALARM_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="quoteorderNo != null">
                QUOTEORDER_NO,
            </if>
            <if test="logType != null">
                LOG_TYPE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="userName != null">
                USER_NAME,
            </if>
            <if test="quotedAlarmMode != null">
                QUOTED_ALARM_MODE,
            </if>
            <if test="quotedAlarmLevel != null">
                QUOTED_ALARM_LEVEL,
            </if>
            <if test="createdTime != null">
                CREATED_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="quoteorderNo != null">
                #{quoteorderNo,jdbcType=VARCHAR},
            </if>
            <if test="logType != null">
                #{logType,jdbcType=BIT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="quotedAlarmMode != null">
                #{quotedAlarmMode,jdbcType=BIT},
            </if>
            <if test="quotedAlarmLevel != null">
                #{quotedAlarmLevel,jdbcType=BIT},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>