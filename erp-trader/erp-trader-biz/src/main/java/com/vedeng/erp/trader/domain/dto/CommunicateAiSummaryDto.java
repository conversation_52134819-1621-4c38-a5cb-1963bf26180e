package com.vedeng.erp.trader.domain.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * T_COMMUNICATE_SUMMARY
 * <AUTHOR>
@Data
public class CommunicateAiSummaryDto implements Serializable {
    /**
     * 主键
     */
    private Long communicateSummaryId;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 客户意图
     */
    private String customerIntentions;

    /**
     * 意向商品
     */
    private String intentionGoods;

    /**
     * 品牌
     */
    private String brands;

    /**
     * 型号
     */
    private String models;

    /**
     * 客户类型
     */
    private String customerTypes;

    /**
     * 是否有意向
     */
    private String isIntention;

    /**
     * 是否加微信
     */
    private String isAddWechat;

    /**
     * 是否有效沟通
     */
    private String isEffectiveCommunication;

    /**
     * T_COMMUNICATE_RECORD表的ID
     */
    private Integer communicateRecordId;

    /**
     * 修改历史
     */
    private JSONObject updateLog;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 归属销售
     */
    private String saleUserName;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 沟通时间
     */
    private String communicateTimeRange;

    /**
     * 人工维护的商品信息
     */
    private List<Map<String, String>> goodsList = new ArrayList<>();

    private static final long serialVersionUID = 1L;
}