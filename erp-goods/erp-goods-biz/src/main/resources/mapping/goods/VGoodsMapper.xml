<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.mapper.VGoodsMapper" >

    <!-- window -->
    <select id="queryGoodsForWindow" parameterType="com.vedeng.goods.dto.QueryGoodsDTO"
            resultType="com.vedeng.goods.dto.GoodsDTO">
        SELECT
        A.SKU_ID AS goodsId,
        A.SKU_NO AS sku,
        <PERSON><PERSON>SHOW_NAME AS goodsName,
        D.BRAND_ID AS brandId,
        D.CATEGORY_ID AS categoryId,
        B.BRAND_NAME AS brandName,
        A.MODEL AS model,
        A.spec AS spec,
        A.BASE_UNIT_ID AS unitId,
        C.UNIT_NAME AS unitName,
        A.MATERI<PERSON>_CODE AS materialCode,
        NUM.MANAGE_CATEGORY_LEVEL AS manageCategory,
        D<PERSON>SPU_LEVEL AS spuLevel,
        D.SPU_ID as spuId,
        IFNULL(LV.LEVEL_NAME, '无')  AS spuLevelName,
        IFNULL(POS.POSITION_NAME, '无档位') AS goodsPositionName,
        A.CHECK_STATUS verifyStatus,
        CONCAT(M.USERNAME,',',U.USERNAME) AS proUserName
        FROM
        V_CORE_SKU A
        LEFT JOIN V_CORE_SPU D ON A.SPU_ID=D.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE F ON D.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
        LEFT JOIN T_BRAND B ON D.BRAND_ID = B.BRAND_ID
        LEFT JOIN T_UNIT C ON A.BASE_UNIT_ID = C.UNIT_ID
        LEFT JOIN T_USER U ON U.USER_ID=D.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=D.ASSIGNMENT_MANAGER_ID

        LEFT JOIN V_GOODS_LEVEL LV ON A.GOODS_LEVEL_NO = LV.ID
        LEFT JOIN V_GOODS_POSITION POS ON A.GOODS_POSITION_NO = POS.ID
        WHERE A.STATUS = 1 and D.CHECK_STATUS != 4 and A.CHECK_STATUS = 3
        <if test="goods.searchContent!=null and goods.searchContent!=''">
            AND (
            A.SKU_NO LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            A.SHOW_NAME LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            B.BRAND_NAME LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            OR
            A.MODEL LIKE CONCAT('%',#{goods.searchContent,jdbcType=VARCHAR},'%' )
            )
        </if>
        <if test="goods.specModel!=null and goods.specModel!=''">
            AND(
            A.SPEC LIKE CONCAT('%',#{goods.specModel,jdbcType=VARCHAR},'%')
            OR
            A.MODEL LIKE CONCAT('%',#{goods.specModel,jdbcType=VARCHAR},'%')
            )
        </if>
        <if test="goods.goodsName!=null and goods.goodsName!=''">
            AND A.SHOW_NAME LIKE CONCAT('%',#{goods.goodsName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="goods.sku!=null and goods.sku!=''">
            AND A.SKU_NO LIKE CONCAT('%',#{goods.sku,jdbcType=VARCHAR},'%' )
        </if>
        <if test="goods.brandName!=null and goods.brandName!=''">
            AND B.BRAND_NAME LIKE CONCAT('%',#{goods.brandName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="goods.unitName!=null and goods.unitName!=''">
            AND C.UNIT_NAME LIKE CONCAT('%',#{goods.unitName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="goods.brandId !=null and goods.brandId > 0">
            AND D.BRAND_ID = #{goods.brandId,jdbcType=INTEGER}
        </if>
        <if test="goods.goodsType != null">
            AND D.SPU_TYPE = #{goods.goodsType, jdbcType=INTEGER}
        </if>
    </select>

</mapper>