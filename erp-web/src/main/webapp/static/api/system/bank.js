function addBank(data) {
    return axios({
        url: '/bank/add.do',
        method: 'post',
        data: data
    })
}

function updateBank(data) {
    return axios({
        url: '/bank/update.do',
        method: 'post',
        data: data
    })
}

function getBank(data) {
    return axios({
        url: '/bank/getBank.do',
        method: 'post',
        params: data
    })
}

function getBankByBankName(data) {
    return axios({
        url: '/bank/getBankByBankName.do',
        method: 'post',
        params: data
    })
}

function getBankByKeywords(data) {
    return axios({
        url: '/bank/getBankByKeywords.do',
        method: 'post',
        params: data
    })
}
