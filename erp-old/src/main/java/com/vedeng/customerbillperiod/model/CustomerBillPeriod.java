package com.vedeng.customerbillperiod.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * T_CUSTOMER_BILL_PERIOD
 * <AUTHOR>
@Data
public class CustomerBillPeriod implements Serializable {
    private Long billPeriodId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户所属公司ID
     */
    private Integer companyId;

    /**
     * 账期类型，1正式账期，2临时账期，3订单账期
     */
    private Integer billPeriodType;

    /**
     * 订单账期关联的订单ID
     */
    private Integer relatedOrderId;

    /**
     * 账期额度
     */
    private BigDecimal applyAmount;

    /**
     * 账期有效期开始时间
     */
    private Long billPeriodStart;

    /**
     * 账期有效期截止时间
     */
    private Long billPeriodEnd;

    /**
     * 账期结算周期
     */
    private Integer settlementPeriod;

    /**
     * 账期创建时间
     */
    private Long addTime;

    /**
     * 账期申请人
     */
    private Integer creator;

    /**
     * 账期修改时间
     */
    private Long modTime;

    /**
     * 账期修改者
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;
}