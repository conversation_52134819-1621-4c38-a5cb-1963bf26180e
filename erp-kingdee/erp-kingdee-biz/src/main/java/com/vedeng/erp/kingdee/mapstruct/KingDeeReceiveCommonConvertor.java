package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveCommonEntity;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应收单  贝登dto 转 数据库entity
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeReceiveCommonConvertor extends BaseMapStruct<KingDeeReceiveCommonEntity, KingDeeReceiveCommonDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "FEntityDetail", source = "FEntityDetail", qualifiedByName = "expensesToJsonArray")
    @Override
    KingDeeReceiveCommonEntity toEntity(KingDeeReceiveCommonDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "FEntityDetail", source = "FEntityDetail", qualifiedByName = "expensesJsonArrayToList")
    @Override
    KingDeeReceiveCommonDto toDto(KingDeeReceiveCommonEntity entity);
	
    
    /**
     * entity 中JSONArray 转 原对象
     *
     * @param payBillEntryDtos JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("expensesJsonArrayToList")
    default List<KingDeeReceiveCommonDetailDto> entryJsonArrayToList(JSONArray payBillEntryDtos) {
        if (CollUtil.isEmpty(payBillEntryDtos)) {
            return Collections.emptyList();
        }
        return payBillEntryDtos.toJavaList(KingDeeReceiveCommonDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("expensesToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeeReceiveCommonDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
    
}
