<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSaleSupplyPolicyAttachmentMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSaleSupplyPolicyAttachment" >
    <id column="SUPPLY_POLICY_ATTACHMENT_ID" property="supplyPolicyAttachmentId" jdbcType="BIGINT" />
    <result column="SUPPLY_POLICY_ID" property="supplyPolicyId" jdbcType="BIGINT" />
    <result column="URI" property="uri" jdbcType="VARCHAR" />
    <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="VARCHAR" />
    <result column="UPDATOR" property="updator" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />
    <result column="MOD_TIME" property="modTime" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SUPPLY_POLICY_ATTACHMENT_ID, SUPPLY_POLICY_ID, URI,DOMAIN, FILE_NAME, CREATOR, UPDATOR,
    ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SUPPLY_POLICY_ATTACHMENT
    where SUPPLY_POLICY_ATTACHMENT_ID = #{supplyPolicyAttachmentId,jdbcType=BIGINT}
  </select>

  <select id="getSupplyAfterSalePolicyAttashMent" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SUPPLY_POLICY_ATTACHMENT
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_AFTER_SALE_SUPPLY_POLICY_ATTACHMENT
    where SUPPLY_POLICY_ATTACHMENT_ID = #{supplyPolicyAttachmentId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteBySupplyPolicyId" parameterType="java.lang.Long" >
    delete from T_AFTER_SALE_SUPPLY_POLICY_ATTACHMENT
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </delete>


  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.AfterSaleSupplyPolicyAttachment" >
    insert into T_AFTER_SALE_SUPPLY_POLICY_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="supplyPolicyAttachmentId != null" >
        SUPPLY_POLICY_ATTACHMENT_ID,
      </if>
      <if test="supplyPolicyId != null" >
        SUPPLY_POLICY_ID,
      </if>
      <if test="uri != null" >
        URI,
      </if>
      <if test="domain != null" >
        DOMAIN,
      </if>
      <if test="fileName != null" >
        FILE_NAME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="supplyPolicyAttachmentId != null" >
        #{supplyPolicyAttachmentId,jdbcType=BIGINT},
      </if>
      <if test="supplyPolicyId != null" >
        #{supplyPolicyId,jdbcType=BIGINT},
      </if>
      <if test="uri != null" >
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.AfterSaleSupplyPolicyAttachment" >
    update T_AFTER_SALE_SUPPLY_POLICY_ATTACHMENT
    <set >
      <if test="supplyPolicyId != null" >
        SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT},
      </if>
      <if test="uri != null" >
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        DOMAIN = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SUPPLY_POLICY_ATTACHMENT_ID = #{supplyPolicyAttachmentId,jdbcType=BIGINT}
  </update>

  <insert id="batchInsertAttashment" parameterType="java.util.List">
    INSERT INTO T_AFTER_SALE_SUPPLY_POLICY_ATTACHMENT
      (
        SUPPLY_POLICY_ID,
        URI,
        DOMAIN,
        FILE_NAME,
        CREATOR,
        UPDATOR,
        ADD_TIME,
        MOD_TIME
      )
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (
        #{item.supplyPolicyId,jdbcType=INTEGER},
        #{item.uri,jdbcType=VARCHAR},
        #{item.domain,jdbcType=VARCHAR},
        #{item.fileName,jdbcType=VARCHAR},
        #{item.creator,jdbcType=VARCHAR},
        #{item.updator,jdbcType=VARCHAR},
        #{item.addTime,jdbcType=VARCHAR},
        #{item.modTime,jdbcType=VARCHAR}
       )
    </foreach>
  </insert>
</mapper>