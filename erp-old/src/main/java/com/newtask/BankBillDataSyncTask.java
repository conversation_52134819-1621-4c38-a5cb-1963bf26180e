package com.newtask;


import com.google.common.base.Splitter;
import com.google.common.collect.Iterables;
import com.vedeng.billsync.task.service.BankBillDataSyncService;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

/**
 *  @Description  定时任务 支付宝流水 和 微信流水同步到银行流水
 *  @autohr randy.xu
 */
@Component
@JobHandler(value = "BankBillDataSyncTask")
public class BankBillDataSyncTask extends AbstractJobHandler {


    @Autowired
    BankBillDataSyncService bankBillDataSyncService;

    private static final Logger LOGGER  = LoggerFactory.getLogger(BankBillDataSyncTask.class);

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        LOGGER.info("开始执行支付宝和微信流水临时表同步数据到银行流水表任务,时间:{}",param);
        XxlJobLogger.log("开始执行支付宝和微信流水临时表同步数据到银行流水表任务,时间:{}",param);
        //默认昨天
        Date queryDate = DateUtil.getPreviousDayByDateTime(new Date());
        String type = null;
        Integer tag = -1;

        if(StringUtils.isNotBlank(param)){

            String[] params = Iterables.toArray(Splitter.on(",").split(param), String.class);
            String queryDateStr = params[0];
            type = params[1];
            if(StringUtils.isNotEmpty(params[2])){
                tag = Integer.valueOf(params[2]);
            }
            queryDate = new Date(DateUtil.convertLong(queryDateStr, DateUtil.DATE_FORMAT));
        }



        ResultInfo syncResult = bankBillDataSyncService.syncBillDate2bankBill(queryDate,type,tag);

        if (!Optional.ofNullable(syncResult).map(ResultInfo::getCode).orElse(-1).equals(0)){
            return FAIL;
        }

        LOGGER.info("结束执行支付宝和微信流水临时表同步数据到银行流水表任务,时间:{}",param);
        XxlJobLogger.log("结束执行支付宝和微信流水临时表同步数据到银行流水表任务,时间:{}",param);

        return SUCCESS;

    }
}
