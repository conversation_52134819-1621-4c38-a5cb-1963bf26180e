package com.vedeng.erp.trader.domain.vo;

import lombok.Data;

/**
 * 封装对象
 *
 * <AUTHOR>
 * @date 2022/2/20 9:05
 **/
@Data
public class PublicCustomerRegionRulesVo {

    private Integer publicCustomerRegionRulesId;

    /**
     * T_REGION表的主键，REGION_ID > 100000且REGION_TYPE = 3的数据
     */
    private Integer regionId;

    /**
     * 销售ID
     */
    private Integer userId;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新者
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;


    // === 关联的数据
    private Integer zoneId;

    private String zoneName;

    private Integer cityId;

    private String cityName;

    private Integer provinceId;

    private String provinceName;

    private String orgName;

    private Integer orgId;

    private String username;

    private Integer userBoss;

    private String userBossName;
}
