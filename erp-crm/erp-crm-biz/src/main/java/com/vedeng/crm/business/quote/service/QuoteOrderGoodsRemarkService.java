package com.vedeng.crm.business.quote.service;

import com.vedeng.crm.business.quote.domain.entity.QuoteorderGoodsRemarkEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QuoteOrderGoodsRemarkService {

    void insert(QuoteorderGoodsRemarkEntity record);

    List<QuoteorderGoodsRemarkEntity> queryList(@Param("quoteorderGoodsId") Integer quoteorderGoodsId);
    List<QuoteorderGoodsRemarkEntity> queryListByQuoteId(@Param("quoteorderId") Integer quoteorderId);
}
