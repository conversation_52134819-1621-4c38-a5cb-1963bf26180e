<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.kpi.replica.dao.RTraderJUserReplicaMapper" >

    <resultMap id="BaseResultMap" type="com.vedeng.trader.model.RTraderJUser" >
        <id column="R_TRADER_J_USER_ID" property="rTraderJUserId" jdbcType="INTEGER" />
        <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
        <result column="USER_ID" property="userId" jdbcType="INTEGER" />
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    </resultMap>

    <select id="getUserIdByTraderId" resultMap="BaseResultMap">
        SELECT * FROM T_R_TRADER_J_USER WHERE TRADER_ID = #{traderId} AND TRADER_TYPE = 1 LIMIT 1
    </select>
</mapper>