package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 销售结算调整单
 * @author: Suqin
 * @date: 2023/3/6 17:43
 **/
@Getter
@Setter
@Table(name = "KING_DEE_SALE_SETTLEMENT_ADJUSTMENT")
public class KingDeeSaleSettlementAdjustmentEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer Id;

    /**
     * 单据内码  0：表示新增 * 非0：云星空系统单据FID值，表示修改
     */
    private String fid;

    /**
     * 单据号
     */
    private String fBillNo;

    /**
     * 调整日期
     */
    private String fQzokDate;

    /**
     * 贝登单据行ID
     */
    @Column(name = "F_QZOK_BDDJTID")
    private String fQzokBddjtid;

    /**
     * fEntity json
     */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fEntity;
}
