function lockPublicTraderCustomer(uniqueId, groupId) {

    checkLogin();
    if (uniqueId > 0) {
        let msg = '确定锁定该客户？';
        layer.confirm(msg, {
            btn: ['是', '否'] //按钮
        }, function () {
            $.ajax({
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                url: page_url + "/trader/public/lockPublicTrader.do",
                data: JSON.stringify({publicCustomerRecordId: uniqueId}),
                dataType: 'json',
                success: function (data) {
                    if (data.code == 0) {
                        // window.location.reload();

                        if (groupId == 0) {
                            $("#aPagePublicTraderCustomer" + uniqueId).show();
                            $("#textPagePublicTraderCustomer" + uniqueId).css("display", "none");
                            $("#lock-public-trader-btn-" + uniqueId).css("background", "gray");
                            $("#lock-public-trader-btn-" + uniqueId).css("border", "0px");
                            $("#lock-public-trader-btn-" + uniqueId).attr("onclick", "javascript:void(0)");
                            $("#cancel-public-trader-btn-" + uniqueId).css("background", "gray");
                            $("#cancel-public-trader-btn-" + uniqueId).css("border", "0px");
                            $("#cancel-public-trader-btn-" + uniqueId).attr("onclick", "javascript:void(0)");
                        } else {
                            $(".aPagePublicTraderCustomer" + groupId).show();
                            $(".textPagePublicTraderCustomer" + groupId).css("display", "none");
                            $(".lock-public-trader-btn-" + groupId).css("background", "gray");
                            $(".lock-public-trader-btn-" + groupId).css("border", "0px");
                            $(".lock-public-trader-btn-" + groupId).attr("onclick", "javascript:void(0)");
                            $(".cancel-public-trader-btn-" + uniqueId).css("background", "gray");
                            $(".cancel-public-trader-btn-" + uniqueId).css("border", "0px");
                            $(".cancel-public-trader-btn-" + uniqueId).attr("onclick", "javascript:void(0)");
                        }
                        layer.closeAll();
                        layer.msg('锁定成功', {
                            icon: 1,
                            time: 1000
                        });
                    } else {
                        layer.alert(data.message);
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }, function () {
        });
    }
}

function revokeLockPublicTraderCustomer(uniqueId, groupId) {

    checkLogin();
    if (uniqueId > 0) {
        let msg = '确定撤销锁定该客户？';
        layer.confirm(msg, {
            btn: ['是', '否'] //按钮
        }, function () {
            $.ajax({
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                url: page_url + "/trader/public/cancelLockPublicTrader.do",
                data: JSON.stringify({publicCustomerRecordId: uniqueId}),
                dataType: 'json',
                success: function (data) {
                    if (data.code == 0) {
                        // window.location.reload();

                        if (groupId == 0) {
                            $("#aPagePublicTraderCustomer" + uniqueId).show();
                            $("#textPagePublicTraderCustomer" + uniqueId).css("display", "none");
                            $("#cancel-lock-public-trader-btn-" + uniqueId).css("background", "gray");
                            $("#cancel-lock-public-trader-btn-" + uniqueId).css("border", "0px");
                            $("#cancel-lock-public-trader-btn-" + uniqueId).attr("onclick", "javascript:void(0)");
                        } else {
                            $(".aPagePublicTraderCustomer" + groupId).show();
                            $(".textPagePublicTraderCustomer" + groupId).css("display", "none");
                            $(".cancel-lock-public-trader-btn-" + groupId).css("background", "gray");
                            $(".cancel-lock-public-trader-btn-" + groupId).css("border", "0px");
                            $(".cancel-lock-public-trader-btn-" + groupId).attr("onclick", "javascript:void(0)");
                        }
                        layer.closeAll();
                        layer.msg('撤销锁定成功', {
                            icon: 1,
                            time: 1000
                        });
                    } else {
                        layer.alert(data.message);
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }, function () {
        });
    }
}

//全选
function selectAll(obj){
    checkLogin();
    if($(obj).prop("checked")){
        $.each($("input[name='checkPublicTraderCustomer']"),function(){
            $(this).prop("checked",true);
        });
    }else{
        $.each($("input[name='checkPublicTraderCustomer']"),function(){
            $(this).prop("checked",false);
        });
    }
}

function revokePublicTraderCustomer(uniqueId, groupId) {

    checkLogin();
    if (uniqueId > 0) {
        layer.prompt({
            formType: 2,
            title: '信息',
            btnAlign: 'c',
            content: `<div><p style="text-align: center;">确定将该客户从公海撤销？</p><br><p style="text-align: center;">保护期 <input type="text" value="10" min="1" max="999" minlength="1" maxlength="3" style="width: 48px;color: red" name="dayInput" id="dayInput" onchange="dayInputChanged(this.value)">天</p><br><p style="text-align: center;">保护期内客户不再掉入公海。</p></div>`,
            yes: function (index, layero) {
                let dayInputNum = $('#dayInput').val();
                $('#dayInput').css("border-color", "#ccc");
                if (dayInputNum == undefined || dayInputNum == null || isNaN(dayInputNum)
                    || dayInputNum == ''
                    || dayInputNum < 1 || dayInputNum > 999 || String(dayInputNum).indexOf(".") + 1 > 0) {
                    $('#dayInput').css("border-color", "red")
                } else {
                    // layer.alert('您刚才输入了:' + dayInputNum);
                    //可执行确定按钮事件并把备注信息（即多行文本框值）存入需要的地方
                    $.ajax({
                        type: "POST",
                        url: page_url + "/trader/public/cancelPublicTraderJudge.do",
                        data: {publicCustomerRecordId: uniqueId,dayInput: dayInputNum},
                        dataType: 'json',
                        success: function (data) {
                            if (data.code == 0) {
                                // window.location.reload();

                                if (groupId == 0) {
                                    $("#lock-public-trader-btn-" + uniqueId).css("background", "gray");
                                    $("#lock-public-trader-btn-" + uniqueId).css("border", "0px");
                                    $("#lock-public-trader-btn-" + uniqueId).attr("onclick", "javascript:void(0)");
                                    $("#cancel-public-trader-btn-" + uniqueId).css("background", "gray");
                                    $("#cancel-public-trader-btn-" + uniqueId).css("border", "0px");
                                    $("#cancel-public-trader-btn-" + uniqueId).attr("onclick", "javascript:void(0)");
                                } else {
                                    $(".cancel-public-trader-btn-" + groupId).css("background", "gray");
                                    $(".cancel-public-trader-btn-" + groupId).css("border", "0px");
                                    $(".cancel-public-trader-btn-" + groupId).attr("onclick", "javascript:void(0)");
                                    $(".lock-public-trader-btn-" + groupId).css("background", "gray");
                                    $(".lock-public-trader-btn-" + groupId).css("border", "0px");
                                    $(".lock-public-trader-btn-" + groupId).attr("onclick", "javascript:void(0)");
                                }
                                layer.closeAll();
                                layer.msg('撤销成功', {
                                    icon: 1,
                                    time: 1000
                                });
                            } else {
                                layer.alert(data.message);
                            }
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                            }
                        }
                    });
                }
            }
        });
    }
}

function batchRevokePublicTraderCustomer(uniqueId, groupId) {

    checkLogin();
    var uniqueIds = [];
    var groupIds = [];
    $("input:checkbox[name=checkPublicTraderCustomer]:checked").each(function (i){
        var id = $(this).val();
        uniqueIds.push(id);
        var associatedCustomerGroup = $(this).attr('associatedCustomerGroup');
        groupIds.push(associatedCustomerGroup);

    })
    console.log(uniqueIds);
    console.log(groupIds);
    if (uniqueIds.length == 0) {
        layer.alert('请选中客户！')
        return;
    }
    if (uniqueIds.length  > 0) {
        layer.prompt({
            formType: 2,
            title: '信息',
            btnAlign: 'c',
            content: `<div><p style="text-align: center;">确定将该客户从公海撤销？</p><br><p style="text-align: center;">保护期 <input type="text" value="10" min="1" max="999" minlength="1" maxlength="3" style="width: 48px;color: red" name="dayInput" id="dayInput" onchange="dayInputChanged(this.value)">天</p><br><p style="text-align: center;">保护期内客户不再掉入公海。</p></div>`,
            yes: function (index, layero) {
                let dayInputNum = $('#dayInput').val();
                $('#dayInput').css("border-color", "#ccc");
                if (dayInputNum == undefined || dayInputNum == null || isNaN(dayInputNum)
                    || dayInputNum == ''
                    || dayInputNum < 1 || dayInputNum > 999 || String(dayInputNum).indexOf(".") + 1 > 0) {
                    $('#dayInput').css("border-color", "red")
                } else {
                    // layer.alert('您刚才输入了:' + dayInputNum);
                    //可执行确定按钮事件并把备注信息（即多行文本框值）存入需要的地方
                    $.ajax({
                        type: "POST",
                        url: page_url + "/trader/public/batchCancelPublicTraderJudge.do",
                        data: {publicCustomerRecordIds: uniqueIds,dayInput: dayInputNum},
                        dataType: 'json',
                        traditional:true,
                        success: function (data) {
                            if (data.code == 0) {
                                // window.location.reload();
                                for (let i = 0; i < uniqueIds.length; i++) {
                                    if (groupIds[i] == 0) {
                                        $("#lock-public-trader-btn-" + uniqueIds[i]).css("background", "gray");
                                        $("#lock-public-trader-btn-" + uniqueIds[i]).css("border", "0px");
                                        $("#lock-public-trader-btn-" + uniqueIds[i]).attr("onclick", "javascript:void(0)");
                                        $("#cancel-public-trader-btn-" + uniqueIds[i]).css("background", "gray");
                                        $("#cancel-public-trader-btn-" + uniqueIds[i]).css("border", "0px");
                                        $("#cancel-public-trader-btn-" + uniqueIds[i]).attr("onclick", "javascript:void(0)");
                                    } else {
                                        $(".cancel-public-trader-btn-" + groupIds[i] ).css("background", "gray");
                                        $(".cancel-public-trader-btn-" + groupIds[i] ).css("border", "0px");
                                        $(".cancel-public-trader-btn-" + groupIds[i] ).attr("onclick", "javascript:void(0)");
                                        $(".lock-public-trader-btn-" + groupIds[i] ).css("background", "gray");
                                        $(".lock-public-trader-btn-" + groupIds[i] ).css("border", "0px");
                                        $(".lock-public-trader-btn-" + groupIds[i] ).attr("onclick", "javascript:void(0)");
                                    }
                                }

                                layer.closeAll();
                                layer.msg('撤销成功', {
                                    icon: 1,
                                    time: 1000
                                });
                            } else {
                                layer.alert("选中含非公海客户，请刷新页面重试！");
                            }
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                            }
                        }
                    });
                }
            }
        });
    }
}


function dayInputChanged(dayInputNum){
    if (dayInputNum == undefined || dayInputNum == null || isNaN(dayInputNum)
        || dayInputNum == ''
        || dayInputNum < 1 || dayInputNum > 999 || String(dayInputNum).indexOf(".") + 1 > 0){
        //确认按钮置灰
        $('.layui-layer-btn0').addClass('layui-btn-disabled');
        $('.layui-btn-disabled').removeClass('layui-layer-btn0');
    } else {
        $('.layui-btn-disabled').addClass('layui-layer-btn0');
    }
}

$(function (){
    //单个checkbox 联动全选
    $("input[name='checkPublicTraderCustomer']").click(function(){
        checkLogin();
        if(!$(this).prop("checked")){
            $("#selectAll").prop("checked",false);
        }else{
            var all = true;
            $.each($("input[name='checkPublicTraderCustomer']"),function(i,n){
                if(!$(n).prop("checked")){
                    all = false;
                }
            });
            if(all){
                $("#selectAll").prop("checked",true);
            }
        }
    });
})
