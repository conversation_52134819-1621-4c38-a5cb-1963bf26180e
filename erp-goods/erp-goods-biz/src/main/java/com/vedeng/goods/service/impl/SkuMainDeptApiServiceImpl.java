package com.vedeng.goods.service.impl;

import com.vedeng.goods.dto.SkuMainDeptDto;
import com.vedeng.goods.mapper.SkuMainDeptMapper;
import com.vedeng.goods.service.SkuMainDeptApiService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * SKU主销售部门(大数据回写)接口实现类
 */
@Service
public class SkuMainDeptApiServiceImpl implements SkuMainDeptApiService {

    @Autowired
    private SkuMainDeptMapper skuMainDeptMapper;

    @Override
    public List<SkuMainDeptDto> getMainDeptBySkuNo(String skuNo) {
        if (StringUtils.isBlank(skuNo)) {
            return Collections.emptyList();
        }
        return skuMainDeptMapper.getMainDeptBySkuNo(skuNo);
    }

}
