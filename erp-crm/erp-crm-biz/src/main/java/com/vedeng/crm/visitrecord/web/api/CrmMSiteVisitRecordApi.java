package com.vedeng.crm.visitrecord.web.api;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.vdurmont.emoji.EmojiParser;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.api.ProductKeywordApiService;
import com.vedeng.crm.category.service.ProductKeywordServiceImpl;
import com.vedeng.crm.common.util.CrmEmojiUtils;
import com.vedeng.crm.dto.CategoryMatchDto;
import com.vedeng.crm.follow.service.FollowUpRecordService;
import com.vedeng.crm.presales.dto.PreSalesInfoDto;
import com.vedeng.crm.presales.dto.PreSalesInfoFromVisitDto;
import com.vedeng.crm.presales.dto.PreSalesInfoType;
import com.vedeng.crm.presales.facade.PreSalesFacade;
import com.vedeng.crm.visitrecord.domain.dto.*;
import com.vedeng.crm.visitrecord.domain.entity.CrmVisitRecordEntity;
import com.vedeng.crm.visitrecord.domain.vo.VisitBizCheckVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordLogVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitUserDetailForPageVo;
import com.vedeng.crm.visitrecord.enums.VisitOperationTypeEnum;
import com.vedeng.crm.visitrecord.facade.CrmVisitRecordFacade;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordLogService;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.vedeng.crm.visitrecord.service.VisitRecordCardService;
import com.vedeng.erp.common.Constants;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.system.web.api.UserApi;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 拜访记录接口
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/28
 */
@ExceptionController
@RestController
@RequestMapping("/crm/visitrecord/m")
@Slf4j
public class CrmMSiteVisitRecordApi {

    @Autowired
    private CrmVisitRecordService visitRecordService;


    @Autowired
    private CrmVisitRecordFacade crmVisitRecordFacade;

    @Autowired
    private VisitRecordCardService visitRecordCardService;

    @Value("${mapConfig:fa25f2e4db0a8197a68ae58fab6c2a06,29d8231c342d7a1ad42ddd36d1b3c969}")//生产是f0331237baa46d985190a937e2cdb4aa,61854e7e3b0705cb5089d9bb47957ee9
    private String mapConfig;
    @Autowired
    private UserApiService userApiService;

    @Autowired
    private CrmVisitRecordService crmVisitRecordService;

    @Autowired
    private ProductKeywordApiService productKeywordApiService;

    /**
     * 查询人员信息，根据userId
     * @param userId
     * @return
     */
    @RequestMapping(value = "/getUserInfo", method = RequestMethod.GET)
    public R<UserDto> getUserInfo(Integer userId){
        UserDto userDto  = userApiService.getUserBaseInfo(userId);
        if(userDto == null){
            return R.error("未查询到有效的人员信息");
        }
        return R.success(userDto);
    }

    /**
     * 拜访计划-地图-key
     */
    @RequestMapping(value = "/config", method = RequestMethod.GET)
    public R<String> config() {
        R<String> result = R.success();
        result.setData(mapConfig);
        return result;
    }

    /**
     * 拜访记录列表
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public R<PageInfo<VisitRecordVo>> page(@RequestBody PageParam<VisitRecordQueryDto> pageParam) {
        return R.success(visitRecordService.visitRecordPage(pageParam));
    }

    /**
     * 拜访计划列表-创建人/拜访人/同行人
     * @return
     */
    @RequestMapping(value = "/pageDetail", method = RequestMethod.POST)
    public R<VisitUserDetailForPageVo> pageDetail() {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        return R.success(visitRecordService.pageDetail(currentUser));
    }

    /**
     * 拜访记录详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public R<VisitRecordVo> detail(@RequestParam Integer id) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        return R.success(visitRecordService.detail(id,currentUser));
    }

    /**
     * 保存或更新拜访计划
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public R<VisitRecordVo> save(@RequestBody VisitRecordInputDto inputDto) {
        if(inputDto.getPreId() != null){
            //上次拜访计划ID不为空，则表示是创建下次拜访计划
            CurrentUser currentUser = CurrentUser.getCurrentUser();
            visitRecordService.dealVisitInputDto(inputDto);//将人员工号，转换为userId，给service中统一处理
            VisitRecordVo visitRecordVo = visitRecordService.saveNextVisitRecord(currentUser,inputDto);
            return R.success(visitRecordVo);
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        visitRecordService.dealVisitInputDto(inputDto);//将人员工号，转换为userId，给service中统一处理
        VisitRecordVo visitRecordVo = visitRecordService.saveOrUpdate(currentUser,inputDto);
        return R.success(visitRecordVo);
    }

    /**
     * 创建下次拜访计划
     */
    @RequestMapping(value = "/saveNext", method = RequestMethod.POST)
    public R<VisitRecordVo> saveNext(@RequestBody VisitNextRecordInputDto nextRecordInputDto ) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        VisitRecordVo visitRecordVo = visitRecordService.saveNextVisitRecord(currentUser,nextRecordInputDto);
        return R.success(visitRecordVo);
    }

    /**
     * 填写拜访记录（仅更新传入的字段）
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<Void> update(@RequestBody VisitEditInputDto editDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        visitRecordService.updateSelective(currentUser, editDto);
        return R.success();
    }

    /**
     * 关闭拜访记录
     */
    @RequestMapping(value = "/close", method = RequestMethod.POST)
    public R<Void> closeVisitRecord(@RequestBody VisitCloseDto closeDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        visitRecordService.closeVisitRecord(currentUser, closeDto);
        return R.success();
    }


    /**
     * 校验业务编号
     */
    @RequestMapping(value = "/checkBizNo", method = RequestMethod.POST)
    public R<VisitBizCheckVo> checkBizNo(@RequestBody VisitBizCheckDto checkDto) {
        return R.success(crmVisitRecordFacade.checkBizNo(checkDto));
    }


    /**
     * 检查手机号是否注册贝登商城
     */
    @RequestMapping(value = "/checkMobileExists")
    public R<Boolean> checkMobileExists(@RequestParam String mobile) {
        Boolean result = visitRecordService.checkMobileExists(mobile);
        return R.success(result);
    }

    /**
     * 拜访打卡
     */
    @RequestMapping(value = "/card", method = RequestMethod.POST)
    public R<Void> card(@RequestBody VisitRecordCardDto cardDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        visitRecordCardService.saveCard(cardDto,currentUser);
        return R.success();
    }

    @Autowired
    private PreSalesFacade preSalesFacade;

    /**
     * 通过拜访计划创建商机
     * clueType 传一个新的枚举值，表示是拜访计划创建的
     * visitId 拜访计划的ID
     * @param preSalesInfoFromVisitDto
     * @return
     */
    @RequestMapping(value = "/addBusinessChance", method = RequestMethod.POST)
    public R<PreSalesInfoType> addBusinessChance(@RequestBody PreSalesInfoFromVisitDto preSalesInfoFromVisitDto) {
        log.info("addBusinessChance:" + JSONObject.toJSONString(preSalesInfoFromVisitDto));
        if(preSalesInfoFromVisitDto.getVisitId() != null){
            preSalesInfoFromVisitDto.setClueType(Constants.ID_6012);//如果由拜访计划创建，来源改为拜访
        }
        String remark = CrmEmojiUtils.removeAllEmojis(preSalesInfoFromVisitDto.getRemark());
        preSalesInfoFromVisitDto.setRemark(remark);//备注

        String otherContactInfo = CrmEmojiUtils.removeAllEmojis(preSalesInfoFromVisitDto.getOtherContactInfo());
        preSalesInfoFromVisitDto.setOtherContactInfo(otherContactInfo);//其他联系方式

        //BeanUtils.copyProperties(preSalesInfoFromVisitDto,preSalesInfoDto);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        preSalesInfoFromVisitDto.setCreator(currentUser.getId());
        preSalesInfoFromVisitDto.setCreatorName(currentUser.getUsername());
        preSalesInfoFromVisitDto.setAddTime(new Date());
        if(preSalesInfoFromVisitDto.getBelongerId() == null || preSalesInfoFromVisitDto.getBelongerId() == 0){
            preSalesInfoFromVisitDto.setBelongerId(currentUser.getId());
            preSalesInfoFromVisitDto.setBelonger(currentUser.getUsername());
        }

        List<String> keywords = productKeywordApiService.extractKeywords(preSalesInfoFromVisitDto.getGoodsInfo());
        if (CollUtil.isNotEmpty(keywords)) {
            preSalesInfoFromVisitDto.setKeywords(keywords.stream().collect(Collectors.joining(",")));
        }

        List<CategoryMatchDto.CategoryMatch> categoryMatches = productKeywordApiService.matchCategories(keywords);
        if (CollUtil.isNotEmpty(categoryMatches)) {
            preSalesInfoFromVisitDto.setCategoryIds(categoryMatches.stream()
                    .map(CategoryMatchDto.CategoryMatch::getSubCategoryId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
        }

        PreSalesInfoType data = preSalesFacade.addPreSalesInfo(preSalesInfoFromVisitDto);//step1:先创建线索或商机
        if(preSalesInfoFromVisitDto.getVisitId() != null) {
            VisitRecordInputDto.RelateBizData relateBizData = new VisitRecordInputDto.RelateBizData();//step2:将线索或商机与拜访计划进行关联
            relateBizData.setBizId(data.getDataId());
            relateBizData.setBizNo(data.getDataNo());
            relateBizData.setRelateType(data.getPreSaleDataEnum().getDataType());
            visitRecordService.saveBusinessChanceForVisit(relateBizData, preSalesInfoFromVisitDto.getVisitId(), currentUser);//step3:保存关联
        }
        return R.success(data);
    }

    @Autowired
    private FollowUpRecordService followUpRecordService;

    /**
     * 添加沟通记录
     * @param visitCommunicateRecordDto
     * @return
     */
    @RequestMapping(value = "/addCommunicateRecord", method = RequestMethod.POST)
    public R<?> addCommunicateRecord( @RequestBody VisitCommunicateRecordDto visitCommunicateRecordDto) {

        Integer recordId = visitCommunicateRecordDto.getRecordId();//取拜访计划的ID
        CrmVisitRecordEntity entity = crmVisitRecordService.selectByPrimaryKey(recordId);
        if(entity == null){
            throw new ServiceException("请确认拜访计划");
        }

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        CommunicateRecordDto communicateRecordDto = new CommunicateRecordDto();
        communicateRecordDto.setCommunicateType(5503);//拜访计划-沟通记录中映射的业务类型
        communicateRecordDto.setRelatedId(visitCommunicateRecordDto.getRecordId());//拜访计划的ID
        communicateRecordDto.setContact(entity.getRecordContactName());
        communicateRecordDto.setContactMob(entity.getRecordContactMobile());
        communicateRecordDto.setTelephone(entity.getRecordContactTele());
        //填充其他字段的值
        communicateRecordDto.setContentSuffix(visitCommunicateRecordDto.getCommunicateContent());
        followUpRecordService.add(communicateRecordDto);
        crmVisitRecordLogService.addOperationLog(visitCommunicateRecordDto.getRecordId(), VisitOperationTypeEnum.SUBMIT_COMMUNICATE, "补充了与客户的沟通记录", currentUser);

        return R.success();
    }

    @Autowired
    private CrmVisitRecordLogService crmVisitRecordLogService;


    /**
     * 查询操作日志
     */
    @RequestMapping(value = "/logs", method = RequestMethod.POST)
    public R<PageInfo<OperationLogDto>> logs(@RequestBody PageParam<Integer> pageParam) {
        return R.success(crmVisitRecordLogService.selectPageByVisitRecordId(pageParam));
    }

}
