package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.domain.entity.InvoiceReversalEntity;
import com.vedeng.erp.finance.dto.InvoiceReversalDto;
import com.vedeng.erp.finance.mapper.InvoiceReversalMapper;
import com.vedeng.erp.finance.mapstruct.InvoiceReversalConvertor;
import com.vedeng.erp.finance.service.InvoiceReversalApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class InvoiceReversalServiceImpl implements InvoiceReversalApiService {

    @Autowired
    private InvoiceReversalConvertor invoiceReversalConvertor;

    @Resource
    private InvoiceReversalMapper invoiceReversalMapper;

    @Override
    public InvoiceReversalDto insertSelective(InvoiceReversalDto invoiceReversalDto) {
        InvoiceReversalEntity invoiceReversalEntity = invoiceReversalConvertor.toEntity(invoiceReversalDto);
        invoiceReversalMapper.insertSelective(invoiceReversalEntity);
        log.info("保存冲销申请成功，id为{},",invoiceReversalEntity.getInvoiceReversalId());
        return invoiceReversalConvertor.toDto(invoiceReversalEntity);
    }

    @Override
    public InvoiceReversalDto updateByPrimaryKeySelective(InvoiceReversalDto invoiceReversalDto) {
        InvoiceReversalEntity invoiceReversalEntity = invoiceReversalConvertor.toEntity(invoiceReversalDto);
        invoiceReversalMapper.updateByPrimaryKeySelective(invoiceReversalEntity);
        log.info("冲销申请，审核更新信息{},", JSON.toJSONString(invoiceReversalEntity));
        return invoiceReversalConvertor.toDto(invoiceReversalEntity);
    }

    @Override
    public InvoiceReversalDto queryByPrimaryKey(Integer invoiceReversalId) {
        InvoiceReversalEntity invoiceReversalEntity = invoiceReversalMapper.selectByPrimaryKey(invoiceReversalId);
        return invoiceReversalConvertor.toDto(invoiceReversalEntity);
    }

    @Override
    public InvoiceReversalDto queryByInvoiceAndRelated(Integer invoiceId, String reversalBillNo, Integer reversalBillType) {
        InvoiceReversalEntity invoiceReversalEntity = invoiceReversalMapper.queryByInvoiceAndRelated(invoiceId, reversalBillNo, reversalBillType);
        return invoiceReversalConvertor.toDto(invoiceReversalEntity);
    }
}
