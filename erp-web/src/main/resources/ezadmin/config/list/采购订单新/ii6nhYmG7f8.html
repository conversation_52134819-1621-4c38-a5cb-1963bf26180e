<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>(订单流)采购订单修改列表</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="ii6nhYmG7f8" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">生效时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="VALIDTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.VALID_TIME= 0, NULL, a.VALID_TIME) / 1000, '%Y-%m-%d %H:%i:%s')" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">创建时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADDTIMESTR" placeholder="" style="" alias="FROM_UNIXTIME(IF(a.ADD_TIME= 0, NULL, a.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s')" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">创建人</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="CREATOR" placeholder="默认全部" style="" alias="a" jdbctype="" data="SELECT DISTINCT a.CREATOR K, lower(u.USERNAME) V
FROM T_BUYORDER_MODIFY_APPLY a
         LEFT JOIN T_USER u ON a.CREATOR = u.USER_ID
WHERE a.COMPANY_ID = 1
order by u.USERNAME" datatype="KVSQLCACHE" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">审核状态</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="STATUS" placeholder="默认全部" style="" alias="COALESCE ( c.STATUS, 3 )" jdbctype="" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">采购单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="BUYORDER_NO" placeholder="请输入" style="" alias="b" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">修改申请单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="BUYORDER_MODIFY_APPLY_NO" placeholder="请输入" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="BUYORDER_MODIFY_APPLY_NO" name="BUYORDER_MODIFY_APPLY_NO" body="td-link" order="1" url="/order/newBuyorder/viewModifyApply.do?buyorderModifyApplyId=${BUYORDER_MODIFY_APPLY_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">修改申请单号</th>
      <th item_name="BUYORDER_NO" name="BUYORDER_NO" body="td-link" order="0" url="/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">采购单号</th>
      <th item_name="ORG_ID" name="ORG_ID" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select
		ORG_ID K,ORG_NAME V
		from T_ORGANIZATION" style="" head="18">部门</th>
      <th item_name="CREATOR" name="CREATOR" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select USER_ID K,USERNAME V FROM T_USER" style="" head="18">创建人</th>
      <th item_name="PAYMENT_STATUS" name="PAYMENT_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未付款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分付款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部付款&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">付款状态</th>
      <th item_name="DELIVERY_STATUS" name="DELIVERY_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">发货状态</th>
      <th item_name="ARRIVAL_STATUS" name="ARRIVAL_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">收货状态</th>
      <th item_name="INVOICE_STATUS" name="INVOICE_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未收票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收票&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">收票状态</th>
      <th item_name="VERIFY_STATUS" name="VERIFY_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" head="18">审核状态</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">创建时间</th>
      <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">生效时间</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by a.ADD_TIME DESC" groupby="">SELECT
	a.BUYORDER_MODIFY_APPLY_ID,
	a.BUYORDER_MODIFY_APPLY_NO,
	a.BUYORDER_ID,
	b.BUYORDER_NO,
	b.ORG_ID,
	a.CREATOR,
	b.INVOICE_STATUS*1 AS INVOICE_STATUS,
	b.PAYMENT_STATUS*1 AS PAYMENT_STATUS,
	b.DELIVERY_STATUS*1 AS DELIVERY_STATUS,
	b.ARRIVAL_STATUS*1 AS ARRIVAL_STATUS,
        COALESCE ( c.STATUS, 3 ) AS VERIFY_STATUS,
        FROM_UNIXTIME(IF(a.ADD_TIME= 0, NULL, a.ADD_TIME) / 1000, '%Y-%m-%d %H:%i:%s') AS  ADD_TIME,
	FROM_UNIXTIME(IF(a.VALID_TIME= 0, NULL, a.VALID_TIME) / 1000, '%Y-%m-%d %H:%i:%s') AS  VALID_TIME
FROM
	T_BUYORDER_MODIFY_APPLY a
	LEFT JOIN T_BUYORDER b ON a.BUYORDER_ID = b.BUYORDER_ID
	LEFT JOIN T_VERIFIES_INFO c ON c.RELATE_TABLE_KEY = a.BUYORDER_MODIFY_APPLY_ID
	AND c.RELATE_TABLE = 'T_BUYORDER_MODIFY_APPLY'
WHERE
 a.COMPANY_ID = 1</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>