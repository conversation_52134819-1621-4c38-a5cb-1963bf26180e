<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Q&A数据维护</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="qadatalist1" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">问题内容</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="QUESTION" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">

            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">问题来源</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="" name="DOC_TYPE" placeholder="" style="" alias="" jdbctype="BODY" data="[
                {&quot;V&quot;:&quot;资料库&quot;,&quot;K&quot;:&quot;0&quot;},
                {&quot;V&quot;:&quot;手动创建&quot;,&quot;K&quot;:&quot;5&quot;},
                {&quot;V&quot;:&quot;智能答复不满意&quot;,&quot;K&quot;:&quot;6&quot;}
                ]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder style=""
                        alias="A" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">生效状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="" name="IS_DELETE" placeholder="" style="" alias="" jdbctype="" data="[
                {&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;0&quot;},
                {&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;1&quot;}
                ]" datatype="JSON" oper=""></select>
            </div>
        </div>
    </form>
    <div class="btn-group   bd-highlight" id="tableButton">
        <!--<button item_name="新增" name="新增" url="/ezadmin/form/form-modifySaleQa0"> 新增 </button>-->
        <button type="table" class="layui-btn" url="/ezadmin/form/form-modifySaleQa0" opentype="SELF" area="800px,420px"  windowname="新增">+
            新增
        </button>


    </div>
    <hr class="layui-border-blue">

    <table id="table" class="layui-table" style=" ">
        <thead>

        <tr id="column">
            <th type="rowbutton" id="rowbutton">
                <button  opentype="CONFIRM_AJAX"  type="single" url="/ezadmin/form/doDelete-modifySaleQa0?ID=${ID}&IS_DELETE=${IS_DELETE}"
                          windowname="${VOICE_TEXT_PARSE}" name="deleteBtn"  >启用
                </button>
                <button   type="single" url="/ezadmin/form/form-modifySaleQa0?ID=${ID}" area="800px,500px"
                          windowname="修正答案" name="playrecordBtn"  >修正答案
                </button>
            </th>
            <th name="ID" style="width:60px;max-width:80px;min-width: 30px;"  >问题ID</th>
            <th name="ADD_TIME" style="width:140px;max-width:120px;min-width: 80px;"  >创建时间</th>
            <th name="QUESTION" body="td-link"  style="width:100px;max-width:200px"  >问题内容</th>
            <th name="USERNAME" style="width:80px;max-width:140px;min-width: 60px;"  >提问人</th>
            <th name="DOC_TYPE" style="width:140px;max-width:140px;min-width: 140px;"  >问题来源</th>
            <th name="ANSWER" body="td-link"  style="width:100px;max-width:200px"  >答案</th>
            <th name="UUSERNAME" style="width:80px;max-width:140px;min-width: 80px;"  >最近更新人</th>
            <th name="MOD_TIME" style="width:100px;max-width:140px;min-width: 100px;"  >最近更新时间</th>
            <th name="IS_DELETE_SHOW" style="width:100px;max-width:140px;min-width: 100px;"  >状态</th>

            <!--
                        <th name="ANSWER" body="td-link" url="/system/call/getrecordpaly.do?url=${COID_URI}" style="width:100px;max-width:100px"  >录音地址</th>
            -->


        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="ORDER BY A.MOD_TIME DESC" groupby="">StringBuilder sql=new StringBuilder("
select
    A.ID,
    A.QUESTION ,
    A.ANSWER,
    B.USERNAME,
    C.USERNAME AS UUSERNAME,

    CASE
      WHEN DOC_TYPE = 1 THEN '资料库'
      WHEN DOC_TYPE = 2 THEN '资料库'
      WHEN DOC_TYPE = 3 THEN '资料库'
      WHEN DOC_TYPE = 4 THEN '资料库'
      WHEN DOC_TYPE = 5 THEN '手动创建'
      WHEN DOC_TYPE = 6 THEN '智能答复不满意'
      ELSE concat(DOC_TYPE,'未知状态')
      END AS DOC_TYPE,
     DATE_FORMAT(A.ADD_TIME, '%Y-%m-%d %H:%i:%s') ADD_TIME  ,
     DATE_FORMAT(A.MOD_TIME, '%Y-%m-%d %H:%i:%s') MOD_TIME,
                     A.IS_DELETE,
                     CASE
      WHEN A.IS_DELETE = 1 THEN '禁用'
      WHEN A.IS_DELETE = 0 THEN '启用'
      ELSE A.IS_DELETE
      END AS IS_DELETE_SHOW
      
                     
  from T_ASK_QUESTION_MANAGEMENT A
         LEFT JOIN T_USER B ON A.CREATOR  = B.USER_ID
        LEFT JOIN T_USER C ON A.UPDATER =C.USER_ID

  where 1=1 and CHAT_TYPE=1 ");

if($("DOC_TYPE").equals("0")){
    sql.append ( " and  DOC_TYPE in (1,2,3,4)" );
}

if($("DOC_TYPE").equals("5")){
    sql.append ( " and  DOC_TYPE =5" );
}
if($("DOC_TYPE").equals("6")){
    sql.append ( " and  DOC_TYPE =6" );
}
list=search(sql);
return list;

 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
</body>
<div id="appendFoot">
    <!--<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/mylayer.js"></script>-->
    <script>

        $(function(){
            $('td[item_name="QUESTION"] a').on('click', function(e) {
                // 在这里添加你的 onClick 事件处理代码
                // 可以使用 event.preventDefault() 阻止按钮的默认行为
                e.preventDefault();e.stopPropagation();
                var modifiedText = "<span style='padding: 9px 15px;width: 80px;font-weight: 400;line-height: 20px;font-size: 14px !important;'>提问内容："+$(this).parents("tr").find("[item_name='QUESTION']").val()+"</span><br/><br/>";
                modifiedText = modifiedText+"<span style='padding: 9px 15px;width: 80px;font-weight: 400;line-height: 20px;font-size: 14px !important;'>问题来源："+$(this).parents("tr").find("[item_name='DOC_TYPE']").val()+"</span><br/><br/>";
                modifiedText = modifiedText+"<span style='padding: 9px 15px;width: 80px;font-weight: 400;line-height: 20px;font-size: 14px !important;'>答案："+$(this).parents("tr").find("[item_name='ANSWER']").val()+"</span><br/><br/>";
                modifiedText = "<div style='font-weight: 400;line-height: 20px;font-size:14px !important;'>"+modifiedText+"</div>";
                layer.open({
                    title: 'Q&A',
                    area: ['600px','400px'],
                    content: modifiedText
                });
            });
            $('td[item_name="ANSWER"] a').on('click', function(e) {
                // 在这里添加你的 onClick 事件处理代码
                // 可以使用 event.preventDefault() 阻止按钮的默认行为
                e.preventDefault();e.stopPropagation();
                var modifiedText = "<span style='padding: 9px 15px;width: 80px;font-weight: 400;line-height: 20px;font-size: 14px !important;'>提问内容："+$(this).parents("tr").find("[item_name='QUESTION']").val()+"</span><br/><br/>";
                modifiedText = modifiedText+"<span style='padding: 9px 15px;width: 80px;font-weight: 400;line-height: 20px;font-size: 14px !important;'>问题来源："+$(this).parents("tr").find("[item_name='DOC_TYPE']").val()+"</span><br/><br/>";
                modifiedText = modifiedText+"<span style='padding: 9px 15px;width: 80px;font-weight: 400;line-height: 20px;font-size: 14px !important;'>答案："+$(this).parents("tr").find("[item_name='ANSWER']").val()+"</span><br/><br/>";
                modifiedText = "<div style='font-weight: 400;line-height: 20px;font-size:14px !important;'>"+modifiedText+"</div>";
                layer.open({
                    title: 'Q&A',
                    area: ['600px','400px'],
                    content: modifiedText
                });
            });
            $('button[name="deleteBtn"]').each(function (index,obj) {
                var isDelete = $(this).parents("tr").find("[item_name='IS_DELETE']").val();
                if(isDelete != undefined && isDelete == 1){
                    $(this).css("background-color","antiquewhite");
                    $(this).text("启用");
                }else if(isDelete != undefined && isDelete == 0){
                    $(this).css("background-color","azure");
                    $(this).text("禁用");
                }else{
                    $(this).text("未知状态");
                    $(this).css("background-color","lightgray");
                    $(this).on('click', function(e) {
                        e.preventDefault();e.stopPropagation();
                    });
                }

            })
            // $('button[name="deleteBtn"]').on('click', function(e) {
            //     // 在这里添加你的 onClick 事件处理代码
            //     // 可以使用 event.preventDefault() 阻止按钮的默认行为
            //     e.preventDefault();e.stopPropagation();
            //     var modifiedText = "提问内容："+$(this).parents("tr").find("[item_name='QUESTION']").val()+"<br/>";
            //     modifiedText = modifiedText+"问题来源："+$(this).parents("tr").find("[item_name='DOC_TYPE']").val()+"<br/>";
            //     modifiedText = modifiedText+"答案："+$(this).parents("tr").find("[item_name='ANSWER']").val()+"<br/>";
            //     layer.open({
            //         title: 'Q&A',
            //         area: ['600px','400px'],
            //         content: modifiedText
            //     });
            // });
            // $('button[name="voiceTextParse"]').on('click', function(e) {
            //     // 在这里添加你的 onClick 事件处理代码
            //     // 可以使用 event.preventDefault() 阻止按钮的默认行为
            //     e.preventDefault();e.stopPropagation();
            //     var content = $(this).attr("item_open_title");
            //     //var modifiedText = content.replace(/(。对话人)/g, "<br/>$1");
            //     var modifiedText = content.replace(/对话人/g, "<br/>对话人");
            //
            //     layer.open({
            //         title: '科大讯飞语音转写结果',
            //         area: ['600px','400px'],
            //         content: modifiedText
            //     });
            //     console.log(modifiedText);
            // });
        })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
</html>