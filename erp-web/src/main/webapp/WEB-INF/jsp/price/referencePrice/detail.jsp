<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="详情" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp" %>

<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/price/referencePrice/editReferencePrice.do">

            <input type="hidden" name="skuNo" id="skuNo" value="${skuInfo.skuNo}"/>
            <input type="hidden" name="referencePriceId" id="referencePriceId" value="${referencePriceResponseDto.id}"/>

            <ul class="payplan">

                <li>
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">基本信息</div>
                    </div>
                    <div class="parts">
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                                <tr>
                                    <th style="width:50px">订货号</th>
                                    <th style="width:80px">商品名称</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>${skuInfo.skuNo}</td>
                                    <td>
                                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${skuInfo.skuNo}","link":"./goods/goods/viewbaseinfo.do?goodsId=${fn:substringAfter(skuInfo.skuNo,'V')}","title":"产品信息"}'>${skuInfo.showName}</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>


                <div>
                    <div class="title-container title-container-blue" id="salePrice">
                        <div class="table-title nobor">参考价</div>
                    </div>

                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th style="width:50px">终端参考价</th>
                            <th style="width:80px">经销参考价</th>
                            <th style="width:80px">集团参考价</th>
                        </tr>
                        </thead>
                            <tr>
                                <td>${referencePriceResponseDto.referenceTerminalPrice}</td>
                                <td>${referencePriceResponseDto.referenceDistributionPrice}</td>
                                <td>${referencePriceResponseDto.referenceGroupPrice}</td>
                            </tr>

                        </tbody>
                    </table>
                </div>

                <div>
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">变更记录</div>
                    </div>

                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <thead>
                            <tr>
                                <th style="width:50px">操作人</th>
                                <th style="width:80px">操作时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="history" items="${referencePriceResponseDto.historyList}"  varStatus="num">
                                <tr>
                                    <td>${history.operator}</td>
                                    <td>${history.addTime}</td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>

            </ul>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>