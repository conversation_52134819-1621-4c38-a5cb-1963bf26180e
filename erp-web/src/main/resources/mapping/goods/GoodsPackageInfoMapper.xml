<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.GoodsPackageInfoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.dto.GoodsPackageInfoDTO">
      <id column="SKU_ID" jdbcType="INTEGER" property="skuId" />
      <result column="SPU_ID" jdbcType="INTEGER" property="skuId" />
      <result column="IS_ENABLE_MULTISTAGE_PACKAGE" jdbcType="INTEGER" property="isEnableMultistagePackage" />
      <result column="MID_PACKAGE_NUM" jdbcType="INTEGER" property="midPackageNum" />
      <result column="BOX_PACKAGE_NUM" jdbcType="INTEGER" property="boxPackageNum" />
  </resultMap>
  <sql id="Base_Column_List">
    SKU_ID,SPU_ID,IS_ENABLE_MULTISTAGE_PACKAGE,MID_PACKAGE_NUM,BOX_PACKAGE_NUM
  </sql>
  <select id="getGoodPackageInfo" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER} AND SPU_ID = #{spuId,jdbcType=INTEGER}
  </select>
</mapper>