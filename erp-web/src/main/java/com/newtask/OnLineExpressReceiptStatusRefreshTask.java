package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.OrderReceiptStatusDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 在线快递确认状态更新任务
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "onLineExpressReceiptStatusRefreshTask")
public class OnLineExpressReceiptStatusRefreshTask extends AbstractJobHandler {

    public static final Logger logger = LoggerFactory.getLogger(OnLineExpressReceiptStatusRefreshTask.class);

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private ExpressMapper expressMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("OnLineExpressReceiptStatusRefreshTask -----------" + s);

        List<Integer> orderIds = StringUtil.isNotBlank(s) ? JSON.parseArray(s, Integer.class) : saleorderMapper.getReceiptSaleOrderIdList();

        logger.info("定时任务处理订单在线签收信息订单ID orderIds:{}", JSON.toJSONString(orderIds));
        if (CollectionUtils.isEmpty(orderIds)) {
            return SUCCESS;
        }
        List<Saleorder> orderInfoList = saleorderMapper.getSaleOrersByIdList(orderIds);
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return SUCCESS;
        }
        orderIds.forEach(item->{
            List<Integer> orderIdsTemp=new ArrayList<>();
            orderIdsTemp.add(item);
            processPage(orderInfoList,orderIdsTemp);
        });
        return SUCCESS;
    }
    ReturnT<String> processPage(List<Saleorder> orderInfoList,List<Integer> orderIds){
        List<Express> expressListByOrderIds = expressMapper.getExpressListByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(expressListByOrderIds)) {
            return SUCCESS;
        }

        HashMap<Integer, List<Express>> orderExpressListMap = new HashMap<>();

        expressListByOrderIds.forEach(item -> {
            if (orderExpressListMap.containsKey(item.getSaleorderId())) {
                orderExpressListMap.get(item.getSaleorderId()).add(item);
            } else {
                ArrayList<Express> expressArrayList = new ArrayList<>();
                expressArrayList.add(item);
                orderExpressListMap.put(item.getSaleorderId(), expressArrayList);
            }
        });

        ArrayList<OrderReceiptStatusDto> orderReceiptStatusList = new ArrayList<>();

        orderInfoList.forEach(saleOrderInfo -> {
            List<Express> expressList = orderExpressListMap.get(saleOrderInfo.getSaleorderId());
            if (CollectionUtils.isEmpty(expressList)) {
                return;
            }

            Integer onlineReceiptStatus;

            if (ErpConst.TWO.equals(saleOrderInfo.getDeliveryStatus()) && CollectionUtils.isEmpty(expressList.stream().filter(item -> item.getOnlineReceiptId().equals(0)).collect(Collectors.toList()))) {
                onlineReceiptStatus = ErpConst.TWO;
            } else if (CollectionUtils.isEmpty(expressList.stream().filter(item -> item.getOnlineReceiptId() > 0).collect(Collectors.toList()))) {
                onlineReceiptStatus = ErpConst.ZERO;
            } else {
                onlineReceiptStatus = ErpConst.ONE;
            }
            orderReceiptStatusList.add(new OrderReceiptStatusDto(saleOrderInfo.getSaleorderId(), onlineReceiptStatus));
        });
        if (CollectionUtils.isEmpty(orderReceiptStatusList)) {
            return SUCCESS;
        }
        saleorderMapper.batchUpdateOrderReceiptStatus(orderReceiptStatusList);
        return SUCCESS;
    }
}
