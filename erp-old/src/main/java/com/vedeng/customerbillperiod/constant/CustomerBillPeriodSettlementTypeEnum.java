package com.vedeng.customerbillperiod.constant;

/**
 * @Author: daniel
 * @Date: 2021/7/30 09 02
 * @Description: 客户账期结算方式枚举类
 */
public enum CustomerBillPeriodSettlementTypeEnum {

    /**
     * 订单发货
     */
    ORDER_DELIVERY(1,"订单发货"),


    ORDER_INVOICE(2,"订单开票"),

    ;


    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    CustomerBillPeriodSettlementTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
