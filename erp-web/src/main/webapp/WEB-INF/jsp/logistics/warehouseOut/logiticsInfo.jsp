<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="物流轨迹" scope="application" />
<%@ include file="../../common/common.jsp"%>

<style>
	/* 美化主容器 */
	.main-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 20px;
		background-color: #fff;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		border-radius: 8px;
	}

	/* 设置表格宽度和间距 */
	.table {
		width: 100%;
		border-collapse: collapse;
		margin-top: 20px;
	}

	/* 设置表格内容行样式 */
	.table tbody tr {
		border-bottom: 1px solid #ddd;
	}

	/* 设置表格单元格样式 */
	.table tbody td {
		padding: 12px;
		text-align: left;
	}

	/* 设置特定列宽度 */
	.wid12 {
		width: 18%;
	}

	.wid10 {
		width: 10%;
	}
</style>

<div class="main-container">
	<div class="parts">
		<table class="table">
			<tbody>
			<c:forEach var="list" items="${ldList}" varStatus="num">
				<tr>
					<td class="wid12">${list.dateTime}</td>
					<td class="wid10">${list.timeMillis}</td>
					<td>${list.detail}</td>
				</tr>
			</c:forEach>
			<c:if test="${empty ldList}">
				<tr>
					<td colspan="3">暂无信息</td>
				</tr>
			</c:if>
			</tbody>
		</table>
	</div>
</div>

<%@ include file="../../common/footer.jsp"%>