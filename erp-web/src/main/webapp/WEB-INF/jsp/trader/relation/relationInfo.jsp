<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="关联客户" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ include file="../customer/customer_tag.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/trader/relationInfo.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">
</script>
<div class="content">
    <div class="parts">
        <div class="title-container">
            <div class="table-title">关联客户</div>
            <button type="button" class="confSearch bt-small bt-bg-style bg-light-blue" onclick="initRelation(${traderCustomer.traderId})"
                    id="initRelation" style="float:
            right ; margin-top: 4px; margin-right: 20px">关联客户</button>
        </div>
    </div>
    <div class="parts">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <td>序号</td>
                <td>客户名称</td>
                <td>操作</td>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty traderList}">
                <c:forEach var="tc" items="${traderList}" varStatus="status">
                    <tr>
                        <td>${status.index+1}</td>
                        <td>
                            <c:if test="${tc.isAuth}">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                    "link":"/trader/customer/baseinfo.do?traderId=${tc.traderId}",
                                    "title":"客户信息"}'>
                                        ${tc.traderName}
                                </a>
                            </c:if>
                            <c:if test="${not tc.isAuth}">
                                <span>${tc.traderName}</span>
                            </c:if>
                        </td>
                        <td>
                            <button type="button" class="confSearch bt-small bt-bg-style bg-light-blue" onclick="cancelRelation(${tc.traderCustomerId})"
                                    style="margin-top: 4px; margin-right: 20px">取消关联</button>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty traderList}">
                <tr>
                    <td colspan="3">暂无关联客户</td>
                </tr>
            </c:if>
            </tbody>
        </table>
        <div class="clear"></div>
    </div>
    <div style="display:none;">
        <!-- 弹框 -->
        <div class="title-click nobor  pop-new-data" id="terminalDiv"></div>
    </div>
</div>

<%@ include file="../../common/footer.jsp"%>
