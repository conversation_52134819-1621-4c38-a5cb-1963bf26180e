<%--<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>--%>
<%--<!DOCTYPE html>--%>
<%--<html lang="en">--%>
<%--<head>--%>
<%--    <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>--%>
<%--    <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>--%>
<%--    <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>--%>
<%--    <%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>--%>
<%--    <%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>--%>
<%--    <%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>--%>
<%--    <%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>--%>
<%--    <%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>--%>

<%--    <%@ page trimDirectiveWhitespaces="true" %>--%>

<%--    <%--%>
<%--        String path = request.getContextPath();--%>
<%--        String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()--%>
<%--                + path + "/";--%>
<%--    %>--%>
<%--    <c:set var="path" value="<%=basePath%>" scope="application" />--%>
<%--    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">--%>
<%--    <meta name="renderer" content="webkit|ie-comp|ie-stand">--%>
<%--    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">--%>
<%--    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />--%>
<%--    <meta http-equiv="Cache-Control" content="no-siteapp" />--%>
<%--    <title>${title}</title>--%>
<%--</head>--%>
<%--&lt;%&ndash;<%@ include file="../../common/common.jsp"%>&ndash;%&gt;--%>

<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">--%>
<%--&lt;%&ndash;<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}" />&ndash;%&gt;--%>

<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css" />--%>
<%--<link rel="stylesheet"--%>
<%--      href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/add.css?rnd=${resourceVersionKey}">--%>
<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">--%>
<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/trader/add_contact.css?rnd=${resourceVersionKey}">--%>
<%--<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>--%>
<%--<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js"></script>--%>
<%--<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>--%>
<%--<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>--%>
<%--<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js'></script>--%>
<%--<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/closable-tab.js'></script>--%>
<%--<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js'></script>--%>
<%--<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>--%>
<%--<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/customer/add_contact.js?rnd=${resourceVersionKey}"></script>--%>
<%--&lt;%&ndash;<script src="${pageContext.request.contextPath}/static/js/customer/upload.js?rnd=${resourceVersionKey}"></script>&ndash;%&gt;--%>
<%--<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>--%>
<%--<script src="${pageContext.request.contextPath}/static/new/js/common/global.js"></script>--%>
<%--<style>--%>
<%--    .vd-upload-wrap.vd-upload-file-wrap .vd-upload-item {--%>
<%--        background: white;--%>
<%--    }--%>
<%--    /*i {*/--%>
<%--    /*    display: inline-block;*/--%>
<%--    /*    height: 12px;*/--%>
<%--    /*    margin-bottom: -2px;*/--%>
<%--    /*    background: white;*/--%>
<%--    /*}*/--%>
<%--</style>--%>
<%--<body>--%>
<%--<div class="baseinforeditform content">--%>
<%--    <div class="infor_name">--%>
<%--        <lable>名片</lable>--%>
<%--    </div>--%>
<%--    <div class="f_left">--%>
<%--        <input type="hidden" class="J-upload-data" value='${traderCertificatesMaps}'>--%>
<%--        <div class="J-upload" type="businessCards" style="margin-top: 12px"></div>--%>
<%--        <div class="feedback-block J-upload-error">--%>
<%--            <label  class="error" style="display: none;"></label>--%>
<%--        </div>--%>
<%--    </div>--%>
<%--</div>--%>

<%--</body>--%>
<%--</html>--%>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="en">
<head>
    <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
    <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
    <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
    <%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
    <%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
    <%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
    <%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
    <%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

    <%@ page trimDirectiveWhitespaces="true" %>

    <%
        String path = request.getContextPath();
        String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                + path + "/";
    %>
    <c:set var="path" value="<%=basePath%>" scope="application" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <title>${title}</title>
</head>
<%--<%@ include file="../../common/common.jsp"%>--%>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}" />--%>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css" />
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/add.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/trader/add_contact.css?rnd=${resourceVersionKey}">
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/customer/viewBusinessCard.js?rnd=${resourceVersionKey}"></script>
<%--<script src="${pageContext.request.contextPath}/static/js/customer/upload.js?rnd=${resourceVersionKey}"></script>--%>
<script src="${pageContext.request.contextPath}/static/js/customer/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<style>
    .vd-upload-wrap.vd-upload-file-wrap .vd-upload-item {
        background: white;
    }
    /*i {*/
    /*    display: inline-block;*/
    /*    height: 12px;*/
    /*    margin-bottom: -2px;*/
    /*    background: white;*/
    /*}*/
</style>
<body>
<div class="baseinforeditform content">
    <form method="post" action="${pageContext.request.contextPath}/trader/customer/saveBusinessCards.do" id="myform">
        <ul>
            <li>
                <div class="infor_name">
                    <lable>名片</lable>
                </div>
                <div class="f_left">
                    <input type="hidden" class="J-upload-data" value='${traderCertificatesMaps}'>
                    <div class="J-upload" type="businessCards" style="margin-top: 12px"></div>
                    <div class="feedback-block J-upload-error">
                        <label  class="error" style="display: none;"></label>
                    </div>
                </div>
            </li>
        </ul>
        <input type="hidden" name="traderContactId" id="traderContactId" value="${traderContactId}">
    </form>
</div>
</body>

</html>

