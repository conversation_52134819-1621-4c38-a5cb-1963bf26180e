<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../../../vue/view/common/common.jsp"%>
<c:set var="title" value="导入" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<div id="app">
    <el-container>
        <el-main>
            <%--            加一段提示并调成灰色背景--%>
            <el-row>
                <el-col>
                    <el-alert
                            title="数据模板说明："
                            type="info"
                            :closable="false"
                            >
                        <template slot='title'>
                            <div>数据模板说明:</div>
                            <div> 1、模板中的表头名称不可更改，表头这一行不能删除；</div>
                            <div> 2、带有*的列均为必填项；</div>
                            <div> 3、结款金额必须为纯数字；</div>
                            <div> 4、订单号必须真实存在，且已生效未关闭；</div>
                        </template>
                    </el-alert>
                </el-col>
            </el-row>


            <el-row style="margin-top: 20px;">
                <el-col>
                    一,下载数据模板<br>
                    <br>
                    <el-button size="small"  :underline="false" type="primary"> <a href="<%= basePath %>static/template/批量结款清单.xls" style="color: white; text-decoration: none;" >点击下载</a></el-button>
                    《批量结款数据模板》
                </el-col>
            </el-row>

                <br>
            <el-row>
                <el-col>
                    <p>二,整理数据并导入</p>
                    <br>
                    <el-upload
                            class="upload-demo"
                            ref="upload"
                            multiple="false"
                            action="./bankBillBatchAndConfirm.do"
                            show-file-list="false"
                            accept=".xlsx,.xls"
                            limit="1"
                            :data="{
                                bankBillId: bankBillId,
                                bankAccName: bankAccName,
                            }"
                            :before-upload="beforeUpload"
                            :on-change="onChangeToolFile"
                            :on-success="onSuccess"
                            :file-list="fileList"
                            :auto-upload="false">
                        <el-button slot="trigger" size="small" type="primary">上传文件</el-button>
                        <span style="color: gray; margin-left: 10px;">{{ tipsText }}</span>
                    </el-upload>
                </el-col>
            </el-row>


        </el-main>
        <el-footer class="text-right">
            <el-row>
                <el-col>
                    <el-button size="small" @click="closeThis">取消</el-button>
                    <el-button size="small" type="primary" @click="submitUpload">开始导入</el-button>
                </el-col>
            </el-row>
        </el-footer>

    </el-container>
</div>

<script type="text/javascript">
    let bankBillId = ${bankBillId};
    let bankAccName = '${bankAccName}';

    new Vue({
        el: '#app',

        data() {
            return {
                tipsText: '未选择任何文件',
                bankBillId: bankBillId,
                bankAccName: bankAccName,
                url: '',
                fileList: [],
                list: [],
            };

        },
        mounted() {

        },

        methods: {

            closeThis() {
                parent.layer.close(index);
            },

            beforeUpload(){
                console.log("导入前");
                this.bankBillId = bankBillId;
                this.bankAccName = bankAccName;
                this.$refs.upload.data = {
                    bankBillId: this.bankBillId,
                    bankAccName: this.bankAccName,
                };
            },

            onChangeToolFile(file, fileList) {
                this.tipsText = "";
                if (fileList.length > 0) {
                    this.fileList = [fileList[fileList.length - 1]]  // 这一步，是 展示最后一次选择的文件
                }
            },

            submitUpload() {
                if (this.fileList.length === 0) {
                    this.$message({
                        message: '请选择需要上传的文件！',
                        duration: 1000,
                        showClose: true,
                        type: 'error'
                    });
                    return;
                }
                this.$refs.upload.submit();
            },

            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handlePreview(file) {
                console.log(file);
            },
            onSuccess(data) {
                console.log('====>', data);
                if (data.code === 0) {
                    this.$message({
                        message: data.message,
                        type: 'success',
                        duration: 1000,
                        showClose: true,
                        onClose: () => {
                            debugger
                            parent.layer.close(index);
                            // window.parent.location.reload();
                            window.parent.addCapitalBillForImport(bankBillId);
                        }
                    })
                } else {
                    this.$message({
                        message: data.message,
                        type: 'error'
                    });
                    this.fileList = [];
                    this.$refs.upload.clearFiles();
                }
            },

        }

    });

</script>

<style>

</style>