package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainAndSpecialInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatPlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatSpecialInvoiceApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 销售单蓝票推送，包含专票普票
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/11 11:01
 */
@Service
@Slf4j
public class BatchSaleOrderBlueInvoiceWriter extends BaseWriter<KingDeeSalesVatPlainAndSpecialInvoiceDto> {


    @Autowired
    private KingDeeSalesVatSpecialInvoiceApiService kingDeeSalesVatSpecialInvoiceApiService;

    @Autowired
    private KingDeeSalesVatPlainInvoiceApiService kingDeeSalesVatPlainInvoiceApiService;

    @Override
    public void doWrite(KingDeeSalesVatPlainAndSpecialInvoiceDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        if (item.getSpecial()) {
            // 销售专票
            KingDeeSalesVatSpecialInvoiceDto vatSpecialInvoiceDto = item.getKingDeeSalesVatSpecialInvoiceDto();
            log.info("销售单普通商品专票：{}", JSON.toJSONString(vatSpecialInvoiceDto));
            vatSpecialInvoiceDto.setKingDeeBizEnums(KingDeeBizEnums.saveCommonSpecialInvoice);
            kingDeeSalesVatSpecialInvoiceApiService.register(vatSpecialInvoiceDto,true);
        } else {
            // 销售普票
            KingDeeSalesVatPlainInvoiceDto vatPlainInvoiceDto = item.getKingDeeSalesVatPlainInvoiceDto();
            log.info("销售单普通商品普票：{}", JSON.toJSONString(vatPlainInvoiceDto));
            vatPlainInvoiceDto.setKingDeeBizEnums(KingDeeBizEnums.saveSalesVatPlainInvoice);
            kingDeeSalesVatPlainInvoiceApiService.register(vatPlainInvoiceDto,true);
        }
    }
}