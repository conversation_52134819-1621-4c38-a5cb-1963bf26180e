package com.vedeng.erp.finance.facade;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.domain.dto.DiscountNumberDto;
import com.vedeng.erp.finance.dto.*;

import java.math.BigDecimal;
import java.util.List;

public interface PayApplyFacade {

    void autoCreateCreateBill(PayApplyCreateBillDto payApplyCreateBillDto);

    void payApplyRuleCheck(PayApplyCheckDto payApplyCreateBillDto);

    void filter(PayApplyCreateBillDto payApplyCreateBillDto);

    void createBillRuleCheck(PayApplyCreateBillDto payApplyCreateBillDto);

    List<PayApplyCreateBillDto> getPayApplyByDto(PayApplyCreateBillDto payApplyCreateBillDto);

    PageInfo<PayApplyRespDto> pageList(PageParam<PayApplyReqDto> payApplyReqDto);

    List<AcceptancePayRespDto> acceptancePay(AcceptancePayDto acceptancePayDto);

    List<DiscountNumberDto> getDiscountNumber(BigDecimal amount);
}
