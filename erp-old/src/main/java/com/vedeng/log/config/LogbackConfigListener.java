package com.vedeng.log.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.http.HttpURLConstant;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoBatchQueryDto;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoDetailResponseDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;

@Configuration
public class LogbackConfigListener {
    @Value(value="${log.config}")
    private String logConfig;

    @Bean
    public ApolloConfig doLogConfig() {

        try{
            TypeReference<RestfulResult<List<SkuPriceInfoDetailResponseDto>>> resultTypeReference = new TypeReference<RestfulResult<List<SkuPriceInfoDetailResponseDto>>>(){};
            RestfulResult<List<SkuPriceInfoDetailResponseDto>> listRestfulResult = JSON.parseObject("{\"code\":\"success\",\"data\":[{\"bdMarketPrice\":5000.000,\"distributionPrice\":2000.00,\"marketPrice\":5000.00,\"purchaseList\":[{\"modTime\":\"2023-07-14 08:44:51\",\"purchasePrice\":1800.00,\"traderId\":154188}],\"skuNo\":\"V534355\",\"terminalPrice\":2400.00}],\"success\":true}", resultTypeReference.getType());
            System.out.println(listRestfulResult);
        }catch (Exception e){
            System.err.println(e);
        }
        ApolloConfig config  = new ApolloConfig();
        config.doLoLogconfig(logConfig);
        return config;
    }

//    public static void main(String[] args) {
//        TypeReference<RestfulResult> resultType1 = new TypeReference<RestfulResult>(){};
//        RestfulResult resultType2 = JSON.parseObject("{\"code\":\"success\",\"data\":null,\"success\":true}", resultType1.getType());
//        System.out.println(resultType2);
//
//
//
//    }
}
