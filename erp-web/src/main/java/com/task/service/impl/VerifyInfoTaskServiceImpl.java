package com.task.service.impl;

import com.task.service.VerifyInfoTaskService;
import com.vedeng.activiti.service.ActivitiAssigneeService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.ActHiTaskInfoDo;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.model.VerifiesRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VerifyInfoTaskServiceImpl implements VerifyInfoTaskService {

    @Resource
    VerifiesInfoMapper verifiesInfoMapper;

    @Resource
    UserMapper userMapper;

    @Override
    public Integer handleHistorySupplierVerifyInfo() {
        List<VerifiesInfo> verifiesInfoList = verifiesInfoMapper.getHistorySupplierVerufyInfo(ErpConst.SUPPLYER_VERIFY_TYPE);
        if (CollectionUtils.isEmpty(verifiesInfoList)) {
            log.info("无历史供应商审核信息需要处理");
        }
        Integer total = verifiesInfoList.size();
        log.info("开始处理供应商审核信息,共" + total + "条");
        Integer successNum = 0;
        Integer failNum = 0;

        for (VerifiesInfo verifiesInfo : verifiesInfoList) {
            String businessKey = "traderSupplierVerify_" + verifiesInfo.getRelateTableKey();
            Integer insId = verifiesInfoMapper.getLastHisActivityInfo(businessKey);
            List<ActHiTaskInfoDo> actHiTaskInfoDoList = verifiesInfoMapper.getHisTaskInfo(insId);
            try {
                if (CollectionUtils.isNotEmpty(actHiTaskInfoDoList)) {
                    for (ActHiTaskInfoDo actHiTaskInfoDo : actHiTaskInfoDoList) {
                        deduceFromName(actHiTaskInfoDo,verifiesInfo);
                    }
                    int i = verifiesInfoMapper.updateByPrimaryKeySelective(verifiesInfo);
                    if (i > 0) {
                        successNum++;
                    } else {
                        failNum++;
                    }
                }
            } catch (Exception e) {
                log.error("处理供应商审核信息出错", e);
                failNum++;
            }
        }
        log.info("共处理{}供应商审核信息，成功：{}，失败：{}",total,successNum,failNum);
        return successNum;
    }

    private void deduceFromName(ActHiTaskInfoDo actHiTaskInfoDo, VerifiesInfo verifiesInfo) {
        if ("申请人".equals(actHiTaskInfoDo.getActName())) {
            if (StringUtils.isNotBlank(actHiTaskInfoDo.getAssignee())) {
                String applyerStr = actHiTaskInfoDo.getAssignee();
                if (actHiTaskInfoDo.getAssignee().indexOf("(") > 0) {
                    applyerStr = actHiTaskInfoDo.getAssignee().substring(0, actHiTaskInfoDo.getAssignee().indexOf("("));
                }
                User userByName = userMapper.getUserByUserName(applyerStr);
                verifiesInfo.setApplyerId(userByName.getUserId());
            }
        }
        if ("供应链质量官审核".equals(actHiTaskInfoDo.getActName())) {
            if (StringUtils.isNotBlank(actHiTaskInfoDo.getAssignee())) {
                String verifyerStr = actHiTaskInfoDo.getAssignee();
                if (actHiTaskInfoDo.getAssignee().indexOf("(") > 0) {
                    verifyerStr = actHiTaskInfoDo.getAssignee().substring(0, actHiTaskInfoDo.getAssignee().indexOf("("));
                }
                User userByName = userMapper.getUserByUserName(verifyerStr);
                verifiesInfo.setVerifyerId(userByName.getUserId());
            }
        }
        if ("资质复审".equals(actHiTaskInfoDo.getActName())) {
            if (StringUtils.isNotBlank(actHiTaskInfoDo.getAssignee())) {
                String verifyerStr = actHiTaskInfoDo.getAssignee();
                if (actHiTaskInfoDo.getAssignee().indexOf("(") > 0) {
                    verifyerStr = actHiTaskInfoDo.getAssignee().substring(0, actHiTaskInfoDo.getAssignee().indexOf("("));
                }
                User userByName = userMapper.getUserByUserName(verifyerStr);
                verifiesInfo.setVerifyerId(userByName.getUserId());
            }
        }
    }

    @Override
    public Integer handleHistoryCustomerVerifyInfo() {
        List<VerifiesInfo> verifiesInfoList = verifiesInfoMapper.getHistoryCustomerVerifyInfoByTableName("T_CUSTOMER_APTITUDE");
        if (CollectionUtils.isEmpty(verifiesInfoList)) {
            log.info("无历史客户资质审核信息需要处理");
        }
        Integer total = verifiesInfoList.size();
        log.info("开始处理客户资质审核信息,共" + total + "条");
        Integer successNum = 0;
        Integer failNum = 0;

        for (VerifiesInfo verifiesInfo : verifiesInfoList) {
            String businessKey = "customerAptitude_" + verifiesInfo.getRelateTableKey();
            Integer insId = verifiesInfoMapper.getLastHisActivityInfo(businessKey);
            List<ActHiTaskInfoDo> actHiTaskInfoDoList = verifiesInfoMapper.getHisTaskInfo(insId);
            try {
                if (CollectionUtils.isNotEmpty(actHiTaskInfoDoList)) {
                    for (ActHiTaskInfoDo actHiTaskInfoDo : actHiTaskInfoDoList) {
                        deduceFromName(actHiTaskInfoDo,verifiesInfo);
                    }
                    int i = verifiesInfoMapper.updateByPrimaryKeySelective(verifiesInfo);
                    if (i > 0) {
                        successNum++;
                    } else {
                        failNum++;
                    }
                }
            } catch (Exception e) {
                log.error("处理客户审核信息出错", e);
                failNum++;
            }
        }
        log.info("共处理{}客户资质审核信息，成功：{}，失败：{}",total,successNum,failNum);
        return successNum;
    }
}
