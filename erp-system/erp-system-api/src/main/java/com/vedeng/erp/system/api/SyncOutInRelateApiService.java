package com.vedeng.erp.system.api;

import com.vedeng.erp.system.dto.SyncOutInRelateDto;
import java.util.List;

public interface SyncOutInRelateApiService {
    
    /**
     * 根据ID删除同步数据
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入同步数据
     */
    int insert(SyncOutInRelateDto record);

    /**
     * 选择性插入同步数据
     */
    int insertSelective(SyncOutInRelateDto record);

    /**
     * 根据ID查询同步数据
     */
    SyncOutInRelateDto selectByPrimaryKey(Integer id);

    /**
     * 选择性更新同步数据
     */
    int updateByPrimaryKeySelective(SyncOutInRelateDto record);

    /**
     * 更新同步数据
     */
    int updateByPrimaryKey(SyncOutInRelateDto record);

    /**
     * 根据处理状态查询同步数据
     */
    List<SyncOutInRelateDto> selectByProcessStatus(Integer processStatus);

    /**
     * 根据外部业务单号查询同步数据
     */
    List<SyncOutInRelateDto> selectByOutBusinessNo(String outBusinessNo);

    /**
     * 根据内部业务单号查询同步数据
     */
    List<SyncOutInRelateDto> selectByInBusinessNo(String inBusinessNo);
} 