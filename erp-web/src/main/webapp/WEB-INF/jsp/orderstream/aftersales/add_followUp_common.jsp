<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <table class="table">
        <thead>
        <tr>
            <th>节点时间</th>
            <th>售后内容</th>
            <th>待操作事项</th>
            <th>当前节点部门</th>
            <th>下次跟进时间</th>
            <th>操作人</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty salesFollowUpRecordList}">
            <c:forEach items="${salesFollowUpRecordList}" var="followUpList">
                <tr>
                    <td><date:date value="${followUpList.addTime}" format="yyyy-MM-dd HH:mm:ss" /></td>
                    <td>${followUpList.content}</td>
                    <td>${followUpList.operationalMatters}</td>
                    <td>
                        <c:forEach items="${orgList}" var="org">
                            <c:if test="${followUpList.orgId eq org.orgId}">
                                ${org.orgName}
                            </c:if>
                        </c:forEach>
                    </td>
                    
                     <td>
                         <fmt:formatDate value="${followUpList.nextTime}" pattern="yyyy-MM-dd" />
                     </td>
                    
                    <td>
                        <c:forEach items="${createList}" var="create">
                            <c:if test="${followUpList.creator eq create.userId}">
                                ${create.username}
                            </c:if>
                        </c:forEach>
                    </td>
                    <td style="text-align: center">
                        <c:choose>
                        <c:when test="${afterSalesVo.status eq 2 && afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                            <div class="pop-new-data" style="color: #3384ef" layerParams='{"width":"600px","height":"380px","title":"编辑售后跟进记录","link":"/order/afterSalesCommon/addFollowUpRecord.do?afterSalesId=${afterSalesVo.afterSalesId}&recordId=${followUpList.recordId}"}'>编辑</div>
                        </c:when>
                        <c:otherwise>
                            <button type="button" class="bt-bg-style bg-light-grey bt-small">编辑</button>
                        </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:forEach>
        </c:if>
        <c:if test="${empty salesFollowUpRecordList}">
            <!-- 查询无结果弹出 -->
            <tr>
                <td colspan='6'>暂无记录！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>
