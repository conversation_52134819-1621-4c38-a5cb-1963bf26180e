package com.vedeng.order.dao;

import com.vedeng.order.model.AuthorizationStorage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthorizationStorageMapper {
    /**
     * @param quoteorderId
     * @return
     */

    AuthorizationStorage getAuthorizationStorageInfoByQuoteOrderId(@Param(value = "quoteorderId") Integer quoteorderId);

    /**
     * 可能存在多条草稿了
     * @param quoteorderId
     * @return
     */
    List<AuthorizationStorage> getAuthorizationStorageInfoListByQuoteOrderId(@Param(value = "quoteorderId") Integer quoteorderId);


    void insertAuthorizationStorageInfo(AuthorizationStorage authorizationStorage);

    void updateAuthorizationStorageInfo(AuthorizationStorage authorizationStorage);

    void delAuthorizationStorageById(@Param(value = "temporaryStorageId") Integer temporaryStorageId,@Param(value = "userId") Integer userId,@Param(value = "time") Long time);

    AuthorizationStorage getTemporaryStorageByNum(@Param(value = "authorizationApplyNum") String authorizationApplyNum);

    void updateAuthorizationStorage(@Param(value = "authorizationApplyNum") String authorizationApplyNum);

    AuthorizationStorage getAuthorizationStorageInfoByQuoteOrderIdAndNum(@Param(value = "quoteorderId") Integer quoteorderId,@Param(value = "authorizationApplyNum") String authorizationApplyNum);
}
