package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶  销项费用专用发票
 * @date 2022/11/09 09:13
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@WriteBackField(needBackField = {"FID","FSALEEXINVENTRY.FEntryID", "FSALEEXINVENTRY.F_QZOK_BDDJHID"})
public class OutPutFeeSpecialInvoiceDto extends KingDeeMqBaseDto {

    private Integer specialInvoicId;

    /**
     * 单据内码
     */
    @WriteBackField
    @KingDeeID
    private String fid;
    /**
     * 贝登erp对应的单据头ID
     */
    @BusinessID("F_QZOK_BDDJTID")
    private String fQzokBddjtid;
    /**
     * 发票号
     */
    private String finvoiceno;
    /**
     * 发票代码
     */
    private String fQzokFpdm;
    /**
     * 发票日期  2022-09-07 00:00:00
     */
    private String finvoicedate;
    /**
     * 业务日期 2022-09-07 00:00:00
     */
    private String fdate;
    /**
     * 往来单位类型 BD_Customer
     */
    private String fcontactunittype;
    /**
     * 客户
     */
    private String fcontactunit;
    /**
     * 销售组织
     */
    private String fsaleorgid;
    /**
     * 结算组织
     */
    private String fsettleorgid;
    /**
     * 单据状态
     */
    private String fdocumentstatus;
    /**
     * 红蓝字标识
     */
    private String fRedBlue;
    /**
     * 归属业务单号
     */
    private String fQzokPzgsywdh;
    /**
     * 发票明细
     */
    private List<OutPutFeeSpecialInvoiceDetailDto> FSALEEXINVENTRY;

    public OutPutFeeSpecialInvoiceDto(){
        this.fcontactunittype = "BD_Customer";
        this.fdocumentstatus = "Z";
        this.fsaleorgid = KingDeeConstant.ORG_ID.toString();
        this.fsettleorgid = KingDeeConstant.ORG_ID.toString();
        this.FSALEEXINVENTRY = new ArrayList<>();
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.OUTPUT_FEE_SPECIAL_INVOICE;
    }
}
