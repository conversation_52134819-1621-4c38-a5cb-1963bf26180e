package com.vedeng.kpi.model.DTO.emptyEntity;

import com.google.common.collect.Lists;
import com.vedeng.kpi.model.DTO.KpiTeamDto;
import com.vedeng.kpi.model.DTO.KpiUserConfigDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class NullKpiTeamDto extends KpiTeamDto {

    public NullKpiTeamDto(){
        this.setTeamId(-99);
        this.setTeamManagerId(-99);
        this.setTeamName("");
        this.setUsers(Lists.newArrayList());
    }

}
