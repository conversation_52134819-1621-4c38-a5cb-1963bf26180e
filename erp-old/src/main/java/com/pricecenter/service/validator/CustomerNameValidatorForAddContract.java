package com.pricecenter.service.validator;

import com.pricecenter.dto.PriceInfoUploadValidatorDto;
import com.pricecenter.dto.ValidatorResult;
import com.pricecenter.service.Validator;
import com.vedeng.common.util.StringUtil;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户名称相关校验器->新增协议价
 */
@Service
public class CustomerNameValidatorForAddContract implements Validator<PriceInfoUploadValidatorDto> {

    @Autowired
    private TraderCustomerMapper traderCustomerMapper;

    @Override
    public ValidatorResult validator(PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {

        ValidatorResult result = ValidatorResult.newBuild();

        //获取所有的行
        List<Row> rowList = priceInfoUploadValidatorDto.getRows();

        Row thisRow = null;
        int rowNum = 1;
        int cellNum = 0;

        for(int i = 0;i < rowList.size();i++){

            rowNum++;

            thisRow = rowList.get(i);

            Cell cell = thisRow.getCell(cellNum);

            if (cell == null || cell.getCellType() == CellType.BLANK) {
                return result.setMessage("第:" + rowNum  + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
            }

            if (cell.getCellType() != CellType.STRING) {
                return result.setMessage("第:" + rowNum + "行，第:" + (cellNum + 1) + "列：非字符类型，请验证！");
            }

            cell.setCellType(CellType.STRING);

            String traderName = cell.getStringCellValue();
            if (StringUtil.isEmpty(traderName)) {
                return result.setMessage("第:" + rowNum  + "行，第:" + (cellNum + 1) + "列：客户名称不能为空，请验证！");
            }

            //客户“XXX”不存在，提交失败
            TraderCustomerVo trader = traderCustomerMapper.getTraderCustomerByTraderName(traderName);
            if(trader == null){
                return result.setMessage("第:" + rowNum  + "行无该客户,请检查后重新提交！");
            }

            //归属平台
            if(trader.getBelongPlatform() != 6 && trader.getBelongPlatform() != 1){
                return result.setMessage("第:" + rowNum  + "行客户不属于非公集采或贝登医疗的客户，请检查后重新提交！");
            }

            priceInfoUploadValidatorDto.getTraderNameAndIdMap().put(traderName,Long.valueOf(trader.getTraderId()));
            priceInfoUploadValidatorDto.getTraderNameAndPlatFormMap().put(traderName,trader.getBelongPlatform());
        }

        return result.setResult(true);

    }
}
