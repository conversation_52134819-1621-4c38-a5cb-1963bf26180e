package com.vedeng.docSync.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Author: Cherny.chen
 * @Create: 2021/6/4 16:00
 */
@Getter
public enum DocSyncEventEnum {

    SUPPLIER_SYNC(1, "同步供应商"),

    SKU_SYNC(2, "同步sku到doc"),

    SPU_SYNC(3, "同步spu到doc"),

    FIRST_ENGAGE_SYNC(4, "同步firstEngage到doc");


    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;


    DocSyncEventEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static DocSyncEventEnum oneByType(Integer type) {
        Objects.requireNonNull(type);

        DocSyncEventEnum[] values = DocSyncEventEnum.values();

        for (DocSyncEventEnum ev : values) {
            if (Objects.equals(ev.type, type)) {
                return ev;
            }
        }
        return null;
    }

}