package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.RInvoiceJInvoiceEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/11/5 14:42
 **/
@Named("newRInvoiceJInvoiceMapper")
public interface RInvoiceJInvoiceMapper {
    /**
     * delete by primary key
     * @param rInvoiceJInvoiceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer rInvoiceJInvoiceId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(RInvoiceJInvoiceEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(RInvoiceJInvoiceEntity record);

    /**
     * select by primary key
     * @param rInvoiceJInvoiceId primary key
     * @return object by primary key
     */
    RInvoiceJInvoiceEntity selectByPrimaryKey(Integer rInvoiceJInvoiceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RInvoiceJInvoiceEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RInvoiceJInvoiceEntity record);

    int updateBatchSelective(List<RInvoiceJInvoiceEntity> list);

    int batchInsert(@Param("list") List<RInvoiceJInvoiceEntity> list);
}