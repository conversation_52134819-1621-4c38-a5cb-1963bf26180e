package com.newtask.finance;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.dto.InvoiceTaxSystemRecordDto;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceService;
import com.vedeng.erp.finance.service.InvoiceTaxSystemRecordService;
import com.vedeng.infrastructure.taxes.common.constant.TaxesConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/22 10:28
 **/
@JobHandler(value = "AllElectricInvoiceDownXmlTask")
@Component
@Slf4j
public class AllElectricInvoiceDownXmlTask extends AbstractJobHandler {

    @Autowired
    private InvoiceTaxSystemRecordService invoiceTaxSystemRecordService;

    @Autowired
    private FullyDigitalInvoiceService fullyDigitalInvoiceService;



    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("==================AllElectricInvoiceDownXmlTask开始====================");

        if (StrUtil.isEmpty(param)) {


            int pageSize = 100;
            int pageNum = 1;
            Page<Object> page = new Page<>(pageNum, pageSize);
            PageInfo<InvoiceTaxSystemRecordDto> data = invoiceTaxSystemRecordService.queryDownXmlNeedDown(page);
            pageNum = data.getPages();
            // 拉取失败的数据
            while (true) {
                log.info("分页执行进度 {},{}", pageSize, pageNum);
                // 调用开票
                Page<Object> pageDes = new Page<>(pageNum, pageSize);
                PageInfo<InvoiceTaxSystemRecordDto> listPageInfo = invoiceTaxSystemRecordService.queryDownXmlNeedDown(pageDes);
                List<InvoiceTaxSystemRecordDto> list = listPageInfo.getList();
                if (list.isEmpty()) {
                    break;
                }
                list.forEach(x-> {
                    try {
                        fullyDigitalInvoiceService.errorRetryDownInvoice(x, TaxesConstant.XML);
                    } catch (Exception e) {
                        log.error("数据：{}，文件：{} fullyDigitalizeInvoiceApiService.errorRetryDownInvoice 执行失败", JSON.toJSONString(x),TaxesConstant.XML,e);
                    }
                });

                if (!listPageInfo.isHasPreviousPage()) {
                    break;
                }
                pageNum = listPageInfo.getPrePage();
                log.info("分页查询进度 {},{}", pageSize, pageNum);
            }
        }



        log.info("==================AllElectricInvoiceDownXmlTask结束====================");

        return SUCCESS;
    }
}
