package com.vedeng.aftersales.model;

import java.math.BigDecimal;

public class AfterSaleServiceStandardInfo {
    private Long serviceStandardInfoId;

    private String skuNo;

    private Integer installPolicyInstallType;

    private BigDecimal installPolicyInstallFee;

    private Integer installPolicyHaveInstallationQualification;

    private Integer installPolicyFreeRemoteInstall;

    /** 安装区域备注 **/
    private String installPolicyInstallAreaComment;

    private String installPolicyResponseTime;

    private String installPolicyVisitTime;

    private String installPolicyInstallTime;

    private Integer technicalDirectSupplyMaintain;

    private String technicalDirectResponseTime;

    private String technicalDirectEffectTime;

    private Integer guaranteePolicyIsGuarantee;

    private String guaranteePolicyGuaranteeType;

    private String guaranteePolicyHostGuaranteePeriod;

    private String guaranteePolicyPartsGuaranteePeriod;

    private Integer guaranteePolicyCycleCaltype;

    private Integer guaranteePolicyArea;

    private String guaranteePolicyAreaComment;

    private String guaranteePolicyResponseTime;

    private String guaranteePolicyVisitTime;

    private String guaranteePolicyRepaireTime;

    private String guaranteePolicyRepaireComment;

    private Integer returnPolicySupportReturn;

    private String returnPolicyCondition;

    private Integer returnPolicyNeedIdentify;

    private String returnPolicyIdentifyType;

    private String returnPolicyReturnPeriod;

    private Integer returnPolicyCycleCaltyp;

    private String returnPolicyPackagingRequirements;

    private String returnPolicyReturnComments;

    private Integer exchangePolicySupportChange;

    private String exchangePolicyExchangeContition;

    private Integer exchangePolicyNeedIdentify;

    private String exchangePolicyIdentifyType;

    private String exchangePolicyExchangePeriod;

    private Integer exchangePolicyCycleCaltyp;

    private String exchangePolicyPackagingRequirements;

    private String exchangePolicyExchangeComments;

    private Integer parolePolicySupportRepair;

    private Integer parolePolicySupportRenovation;

    private Integer parolePolicySupplyBox;

    private Integer parolePolicySupplyAttachment;

    private String overduePolicySupplyBackup;

    private String overduePolicyDetail;

    private String auditor;

    private Integer creator;

    private Integer updator;

    private String addTime;

    private String modTime;

    private String afterSaleServiceLabels;

    private Integer status;

    public Long getServiceStandardInfoId() {
        return serviceStandardInfoId;
    }

    public void setServiceStandardInfoId(Long serviceStandardInfoId) {
        this.serviceStandardInfoId = serviceStandardInfoId;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo == null ? null : skuNo.trim();
    }

    public Integer getInstallPolicyInstallType() {
        return installPolicyInstallType;
    }

    public void setInstallPolicyInstallType(Integer installPolicyInstallType) {
        this.installPolicyInstallType = installPolicyInstallType;
    }

    public BigDecimal getInstallPolicyInstallFee() {
        return installPolicyInstallFee;
    }

    public void setInstallPolicyInstallFee(BigDecimal installPolicyInstallFee) {
        this.installPolicyInstallFee = installPolicyInstallFee;
    }

    public Integer getInstallPolicyHaveInstallationQualification() {
        return installPolicyHaveInstallationQualification;
    }

    public void setInstallPolicyHaveInstallationQualification(Integer installPolicyHaveInstallationQualification) {
        this.installPolicyHaveInstallationQualification = installPolicyHaveInstallationQualification;
    }

    public Integer getInstallPolicyFreeRemoteInstall() {
        return installPolicyFreeRemoteInstall;
    }

    public void setInstallPolicyFreeRemoteInstall(Integer installPolicyFreeRemoteInstall) {
        this.installPolicyFreeRemoteInstall = installPolicyFreeRemoteInstall;
    }

    public String getInstallPolicyResponseTime() {
        return installPolicyResponseTime;
    }

    public void setInstallPolicyResponseTime(String installPolicyResponseTime) {
        this.installPolicyResponseTime = installPolicyResponseTime == null ? null : installPolicyResponseTime.trim();
    }

    public String getInstallPolicyVisitTime() {
        return installPolicyVisitTime;
    }

    public void setInstallPolicyVisitTime(String installPolicyVisitTime) {
        this.installPolicyVisitTime = installPolicyVisitTime == null ? null : installPolicyVisitTime.trim();
    }

    public String getInstallPolicyInstallTime() {
        return installPolicyInstallTime;
    }

    public void setInstallPolicyInstallTime(String installPolicyInstallTime) {
        this.installPolicyInstallTime = installPolicyInstallTime == null ? null : installPolicyInstallTime.trim();
    }

    public Integer getTechnicalDirectSupplyMaintain() {
        return technicalDirectSupplyMaintain;
    }

    public void setTechnicalDirectSupplyMaintain(Integer technicalDirectSupplyMaintain) {
        this.technicalDirectSupplyMaintain = technicalDirectSupplyMaintain;
    }

    public String getTechnicalDirectResponseTime() {
        return technicalDirectResponseTime;
    }

    public void setTechnicalDirectResponseTime(String technicalDirectResponseTime) {
        this.technicalDirectResponseTime = technicalDirectResponseTime == null ? null : technicalDirectResponseTime.trim();
    }

    public String getTechnicalDirectEffectTime() {
        return technicalDirectEffectTime;
    }

    public void setTechnicalDirectEffectTime(String technicalDirectEffectTime) {
        this.technicalDirectEffectTime = technicalDirectEffectTime == null ? null : technicalDirectEffectTime.trim();
    }

    public Integer getGuaranteePolicyIsGuarantee() {
        return guaranteePolicyIsGuarantee;
    }

    public void setGuaranteePolicyIsGuarantee(Integer guaranteePolicyIsGuarantee) {
        this.guaranteePolicyIsGuarantee = guaranteePolicyIsGuarantee;
    }

    public String getGuaranteePolicyGuaranteeType() {
        return guaranteePolicyGuaranteeType;
    }

    public void setGuaranteePolicyGuaranteeType(String guaranteePolicyGuaranteeType) {
        this.guaranteePolicyGuaranteeType = guaranteePolicyGuaranteeType;
    }

    public String getGuaranteePolicyHostGuaranteePeriod() {
        return guaranteePolicyHostGuaranteePeriod;
    }

    public void setGuaranteePolicyHostGuaranteePeriod(String guaranteePolicyHostGuaranteePeriod) {
        this.guaranteePolicyHostGuaranteePeriod = guaranteePolicyHostGuaranteePeriod == null ? null : guaranteePolicyHostGuaranteePeriod.trim();
    }

    public String getGuaranteePolicyPartsGuaranteePeriod() {
        return guaranteePolicyPartsGuaranteePeriod;
    }

    public void setGuaranteePolicyPartsGuaranteePeriod(String guaranteePolicyPartsGuaranteePeriod) {
        this.guaranteePolicyPartsGuaranteePeriod = guaranteePolicyPartsGuaranteePeriod == null ? null : guaranteePolicyPartsGuaranteePeriod.trim();
    }

    public Integer getGuaranteePolicyCycleCaltype() {
        return guaranteePolicyCycleCaltype;
    }

    public void setGuaranteePolicyCycleCaltype(Integer guaranteePolicyCycleCaltype) {
        this.guaranteePolicyCycleCaltype = guaranteePolicyCycleCaltype;
    }

    public Integer getGuaranteePolicyArea() {
        return guaranteePolicyArea;
    }

    public void setGuaranteePolicyArea(Integer guaranteePolicyArea) {
        this.guaranteePolicyArea = guaranteePolicyArea;
    }

    public String getGuaranteePolicyAreaComment() {
        return guaranteePolicyAreaComment;
    }

    public void setGuaranteePolicyAreaComment(String guaranteePolicyAreaComment) {
        this.guaranteePolicyAreaComment = guaranteePolicyAreaComment == null ? null : guaranteePolicyAreaComment.trim();
    }

    public String getGuaranteePolicyResponseTime() {
        return guaranteePolicyResponseTime;
    }

    public void setGuaranteePolicyResponseTime(String guaranteePolicyResponseTime) {
        this.guaranteePolicyResponseTime = guaranteePolicyResponseTime == null ? null : guaranteePolicyResponseTime.trim();
    }

    public String getGuaranteePolicyVisitTime() {
        return guaranteePolicyVisitTime;
    }

    public void setGuaranteePolicyVisitTime(String guaranteePolicyVisitTime) {
        this.guaranteePolicyVisitTime = guaranteePolicyVisitTime == null ? null : guaranteePolicyVisitTime.trim();
    }

    public String getGuaranteePolicyRepaireTime() {
        return guaranteePolicyRepaireTime;
    }

    public void setGuaranteePolicyRepaireTime(String guaranteePolicyRepaireTime) {
        this.guaranteePolicyRepaireTime = guaranteePolicyRepaireTime == null ? null : guaranteePolicyRepaireTime.trim();
    }

    public String getGuaranteePolicyRepaireComment() {
        return guaranteePolicyRepaireComment;
    }

    public void setGuaranteePolicyRepaireComment(String guaranteePolicyRepaireComment) {
        this.guaranteePolicyRepaireComment = guaranteePolicyRepaireComment == null ? null : guaranteePolicyRepaireComment.trim();
    }

    public Integer getReturnPolicySupportReturn() {
        return returnPolicySupportReturn;
    }

    public void setReturnPolicySupportReturn(Integer returnPolicySupportReturn) {
        this.returnPolicySupportReturn = returnPolicySupportReturn;
    }

    public String getReturnPolicyCondition() {
        return returnPolicyCondition;
    }

    public void setReturnPolicyCondition(String returnPolicyCondition) {
        this.returnPolicyCondition = returnPolicyCondition == null ? null : returnPolicyCondition.trim();
    }

    public Integer getReturnPolicyNeedIdentify() {
        return returnPolicyNeedIdentify;
    }

    public void setReturnPolicyNeedIdentify(Integer returnPolicyNeedIdentify) {
        this.returnPolicyNeedIdentify = returnPolicyNeedIdentify;
    }

    public String getReturnPolicyIdentifyType() {
        return returnPolicyIdentifyType;
    }

    public void setReturnPolicyIdentifyType(String returnPolicyIdentifyType) {
        this.returnPolicyIdentifyType = returnPolicyIdentifyType == null ? null : returnPolicyIdentifyType.trim();
    }

    public String getReturnPolicyReturnPeriod() {
        return returnPolicyReturnPeriod;
    }

    public void setReturnPolicyReturnPeriod(String returnPolicyReturnPeriod) {
        this.returnPolicyReturnPeriod = returnPolicyReturnPeriod == null ? null : returnPolicyReturnPeriod.trim();
    }

    public Integer getReturnPolicyCycleCaltyp() {
        return returnPolicyCycleCaltyp;
    }

    public void setReturnPolicyCycleCaltyp(Integer returnPolicyCycleCaltyp) {
        this.returnPolicyCycleCaltyp = returnPolicyCycleCaltyp;
    }

    public String getReturnPolicyPackagingRequirements() {
        return returnPolicyPackagingRequirements;
    }

    public void setReturnPolicyPackagingRequirements(String returnPolicyPackagingRequirements) {
        this.returnPolicyPackagingRequirements = returnPolicyPackagingRequirements == null ? null : returnPolicyPackagingRequirements.trim();
    }

    public String getReturnPolicyReturnComments() {
        return returnPolicyReturnComments;
    }

    public void setReturnPolicyReturnComments(String returnPolicyReturnComments) {
        this.returnPolicyReturnComments = returnPolicyReturnComments == null ? null : returnPolicyReturnComments.trim();
    }

    public Integer getExchangePolicySupportChange() {
        return exchangePolicySupportChange;
    }

    public void setExchangePolicySupportChange(Integer exchangePolicySupportChange) {
        this.exchangePolicySupportChange = exchangePolicySupportChange;
    }

    public String getExchangePolicyExchangeContition() {
        return exchangePolicyExchangeContition;
    }

    public void setExchangePolicyExchangeContition(String exchangePolicyExchangeContition) {
        this.exchangePolicyExchangeContition = exchangePolicyExchangeContition == null ? null : exchangePolicyExchangeContition.trim();
    }

    public Integer getExchangePolicyNeedIdentify() {
        return exchangePolicyNeedIdentify;
    }

    public void setExchangePolicyNeedIdentify(Integer exchangePolicyNeedIdentify) {
        this.exchangePolicyNeedIdentify = exchangePolicyNeedIdentify;
    }

    public String getExchangePolicyIdentifyType() {
        return exchangePolicyIdentifyType;
    }

    public void setExchangePolicyIdentifyType(String exchangePolicyIdentifyType) {
        this.exchangePolicyIdentifyType = exchangePolicyIdentifyType;
    }

    public String getExchangePolicyExchangePeriod() {
        return exchangePolicyExchangePeriod;
    }

    public void setExchangePolicyExchangePeriod(String exchangePolicyExchangePeriod) {
        this.exchangePolicyExchangePeriod = exchangePolicyExchangePeriod == null ? null : exchangePolicyExchangePeriod.trim();
    }

    public Integer getExchangePolicyCycleCaltyp() {
        return exchangePolicyCycleCaltyp;
    }

    public void setExchangePolicyCycleCaltyp(Integer exchangePolicyCycleCaltyp) {
        this.exchangePolicyCycleCaltyp = exchangePolicyCycleCaltyp;
    }

    public String getExchangePolicyPackagingRequirements() {
        return exchangePolicyPackagingRequirements;
    }

    public void setExchangePolicyPackagingRequirements(String exchangePolicyPackagingRequirements) {
        this.exchangePolicyPackagingRequirements = exchangePolicyPackagingRequirements == null ? null : exchangePolicyPackagingRequirements.trim();
    }

    public String getExchangePolicyExchangeComments() {
        return exchangePolicyExchangeComments;
    }

    public void setExchangePolicyExchangeComments(String exchangePolicyExchangeComments) {
        this.exchangePolicyExchangeComments = exchangePolicyExchangeComments == null ? null : exchangePolicyExchangeComments.trim();
    }

    public Integer getParolePolicySupportRepair() {
        return parolePolicySupportRepair;
    }

    public void setParolePolicySupportRepair(Integer parolePolicySupportRepair) {
        this.parolePolicySupportRepair = parolePolicySupportRepair;
    }

    public Integer getParolePolicySupportRenovation() {
        return parolePolicySupportRenovation;
    }

    public void setParolePolicySupportRenovation(Integer parolePolicySupportRenovation) {
        this.parolePolicySupportRenovation = parolePolicySupportRenovation;
    }

    public Integer getParolePolicySupplyBox() {
        return parolePolicySupplyBox;
    }

    public void setParolePolicySupplyBox(Integer parolePolicySupplyBox) {
        this.parolePolicySupplyBox = parolePolicySupplyBox;
    }

    public Integer getParolePolicySupplyAttachment() {
        return parolePolicySupplyAttachment;
    }

    public void setParolePolicySupplyAttachment(Integer parolePolicySupplyAttachment) {
        this.parolePolicySupplyAttachment = parolePolicySupplyAttachment;
    }

    public String getOverduePolicySupplyBackup() {
        return overduePolicySupplyBackup;
    }

    public void setOverduePolicySupplyBackup(String overduePolicySupplyBackup) {
        this.overduePolicySupplyBackup = overduePolicySupplyBackup == null ? null : overduePolicySupplyBackup.trim();
    }

    public String getOverduePolicyDetail() {
        return overduePolicyDetail;
    }

    public void setOverduePolicyDetail(String overduePolicyDetail) {
        this.overduePolicyDetail = overduePolicyDetail == null ? null : overduePolicyDetail.trim();
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor == null ? null : auditor.trim();
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime == null ? null : addTime.trim();
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime == null ? null : modTime.trim();
    }

    public String getInstallPolicyInstallAreaComment() {
        return installPolicyInstallAreaComment;
    }

    public void setInstallPolicyInstallAreaComment(String installPolicyInstallAreaComment) {
        this.installPolicyInstallAreaComment = installPolicyInstallAreaComment;
    }

    public String getAfterSaleServiceLabels() {
        return afterSaleServiceLabels;
    }

    public void setAfterSaleServiceLabels(String afterSaleServiceLabels) {
        this.afterSaleServiceLabels = afterSaleServiceLabels;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}