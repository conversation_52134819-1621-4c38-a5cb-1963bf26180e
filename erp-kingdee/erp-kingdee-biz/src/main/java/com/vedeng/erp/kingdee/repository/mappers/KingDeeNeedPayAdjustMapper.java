package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeNeedPayAdjustEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface KingDeeNeedPayAdjustMapper {
    /**
     * delete by primary key
     * @param kingDeeNeedPayEntityId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer kingDeeNeedPayEntityId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeNeedPayAdjustEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeNeedPayAdjustEntity record);

    /**
     * select by primary key
     * @param kingDeeNeedPayEntityId primary key
     * @return object by primary key
     */
    KingDeeNeedPayAdjustEntity selectByPrimaryKey(Integer kingDeeNeedPayEntityId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeNeedPayAdjustEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeNeedPayAdjustEntity record);

    int batchInsert(@Param("list") List<KingDeeNeedPayAdjustEntity> list);
}