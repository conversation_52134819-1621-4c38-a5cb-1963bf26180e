package com.newtask.util;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.OssInfo;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.impl.OssUtilsServiceImpl;
import com.vedeng.trader.dao.TraderCertificateMapper;
import com.vedeng.trader.model.TraderCertificate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @date created in 2020/2/28 11:49
 */
@Service("certificateImage2OssUtil")
public class CertificateImage2OssUtil {

    public static Logger LOGGER = LoggerFactory.getLogger(CertificateImage2OssUtil.class);

    @Value("${oss_http}")
    private String ossHttp;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OssUtilsServiceImpl ossUtilsService;


    @Autowired
    private TraderCertificateMapper traderCertificateMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    public void downloadFileByStream(TraderCertificate certificate){
        String url = ErpConst.HTTP + certificate.getDomain() + certificate.getUri();

        try {
            RequestCallback requestCallback = request -> request.getHeaders().setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
            ResponseExtractor<Void> responseExtractor = response -> {
                //发送到oss
                OssInfo ossInfo = ossUtilsService.sendFile2Oss(url , response.getBody(),false);
                if(ossInfo != null && ossInfo.getCode().equals(0)){
                    String fileSourceUrl = ossInfo.getFileSourceUrl();
                    String ossFileUrl = ossInfo.getOssFileUrl();
                    String resourceId = ossInfo.getResourceId();
                    updateTraderCertificate(certificate.getTraderCertificateId(),fileSourceUrl,ossFileUrl,resourceId);
                }
                return null;
            };
            restTemplate.execute(url, HttpMethod.GET,requestCallback,responseExtractor);
        } catch (Exception e){
            LOGGER.error("迁移文件:{}到OSS失败：",url,e);
        }
    }


    private void updateTraderCertificate(Integer traderCertificateId,String fileSourceUrl, String ossFileUrl, String ossResourceId){
        if (!ossFileUrl.startsWith(ossHttp)){
            LOGGER.error("迁移文件：{}，OSS响应的结果有误：{}",fileSourceUrl,ossFileUrl);
        }
        String domainAndUri = ossFileUrl.split(ossHttp)[1];
        int domainIndex = domainAndUri.indexOf("/");
        String domain = domainAndUri.substring(0,domainIndex);
        String uri = domainAndUri.substring(domainIndex);

        //更新uri、domain、resourceId
        TraderCertificate toUpdateCertificate = new TraderCertificate();
        toUpdateCertificate.setTraderCertificateId(traderCertificateId);
        toUpdateCertificate.setDomain(domain);
        toUpdateCertificate.setUri(uri);
        toUpdateCertificate.setOssResourceId(ossResourceId);
        traderCertificateMapper.updateTraderCertificate(toUpdateCertificate);

    }

    public void downloadFileByStream(Attachment attachment) {
        String url = ErpConst.HTTP + attachment.getDomain() + attachment.getUri();
        try {
            RequestCallback requestCallback = request -> request.getHeaders().setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
            ResponseExtractor<Void> responseExtractor = response -> {
                //发送到oss
                OssInfo ossInfo = ossUtilsService.sendFile2Oss(url , response.getBody(),true);
                if(ossInfo != null && ossInfo.getCode().equals(0)){
                    String ossFileUrl = ossInfo.getOssFileUrl();
                    String resourceId = ossInfo.getResourceId();
                    updateAttachment(attachment.getAttachmentId(),attachment.getUri(),ossFileUrl,resourceId);
                }
                return null;
            };
            restTemplate.execute(url, HttpMethod.GET,requestCallback,responseExtractor);
        } catch (Exception e){
            LOGGER.error("迁移文件:{}到OSS失败：",attachment.getAttachmentId(),e);
        }
    }

    private void updateAttachment(Integer attachmentId, String fileSourceUrl, String ossFileUrl, String resourceId) {
        String domainAndUri = ossFileUrl.split(ossHttp)[1];
        int domainIndex = domainAndUri.indexOf("/");
        String domain = domainAndUri.substring(0,domainIndex);
        String uri = domainAndUri.substring(domainIndex);

        Attachment update = new Attachment();
        update.setAttachmentId(attachmentId);
        update.setOriginalFilepath(fileSourceUrl);
        update.setDomain(domain);
        update.setUri(uri);
        update.setOssResourceId(resourceId);
        attachmentMapper.updateByPrimaryKeySelective(update);
    }
}
