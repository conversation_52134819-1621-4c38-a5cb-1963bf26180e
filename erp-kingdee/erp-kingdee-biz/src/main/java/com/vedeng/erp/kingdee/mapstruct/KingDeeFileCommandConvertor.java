package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeFileCommand;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @description:
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Mapper(componentModel = "spring", builder = @Builder(disableBuilder = true))
public interface KingDeeFileCommandConvertor extends BaseCommandMapStruct<KingDeeFileCommand, KingDeeFileDataDto> {

    @Override
    @Mapping(target = "formId", source = "formId")
    @Mapping(target = "interId", source = "FId")
    KingDeeFileCommand toCommand(KingDeeFileDataDto dto);
}
