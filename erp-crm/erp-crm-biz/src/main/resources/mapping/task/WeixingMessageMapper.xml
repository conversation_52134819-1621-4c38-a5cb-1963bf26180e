<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.task.mapper.WeixingMessageMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.crm.task.domain.entity.WeixingMessageEntity">
    <!--@mbg.generated-->
    <!--@Table T_WEIXING_MESSAGE-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="MESSAGE_USER_ID" jdbcType="INTEGER" property="messageUserId" />
    <result column="BIZ_TYPE" jdbcType="VARCHAR" property="bizType" />
    <result column="BIZ_TYPE_ENUM" jdbcType="VARCHAR" property="bizTypeEnum" />
    <result column="MAIN_TASK_TYPE" jdbcType="INTEGER" property="mainTaskType" />
    <result column="SUB_TASK_TYPE" jdbcType="INTEGER" property="subTaskType" />
    <result column="MESSAGE_COUNT" jdbcType="INTEGER" property="messageCount" />
    <result column="MESSAGE_PARAMS" jdbcType="VARCHAR" property="messageParams" />
    <result column="MESSAGE_CONTENT" jdbcType="VARCHAR" property="messageContent" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, MESSAGE_USER_ID, BIZ_TYPE, BIZ_TYPE_ENUM, MAIN_TASK_TYPE, SUB_TASK_TYPE, MESSAGE_COUNT, 
    MESSAGE_PARAMS, MESSAGE_CONTENT, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, 
    UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WEIXING_MESSAGE
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByUserIdAndType"   resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_WEIXING_MESSAGE
    where MESSAGE_USER_ID = #{userId,jdbcType=INTEGER}
    AND MAIN_TASK_TYPE=#{mainTaskType,jdbcType=INTEGER}
    AND ADD_TIME BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00')  AND NOW()
    LIMIT 1
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_WEIXING_MESSAGE
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.crm.task.domain.entity.WeixingMessageEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WEIXING_MESSAGE (MESSAGE_USER_ID, BIZ_TYPE, BIZ_TYPE_ENUM, 
      MAIN_TASK_TYPE, SUB_TASK_TYPE, MESSAGE_COUNT, 
      MESSAGE_PARAMS, MESSAGE_CONTENT, IS_DELETE, 
      ADD_TIME, MOD_TIME, CREATOR, 
      CREATOR_NAME, UPDATER, UPDATER_NAME, 
      UPDATE_REMARK)
    values (#{messageUserId,jdbcType=INTEGER}, #{bizType,jdbcType=VARCHAR}, #{bizTypeEnum,jdbcType=VARCHAR}, 
      #{mainTaskType,jdbcType=INTEGER}, #{subTaskType,jdbcType=INTEGER}, #{messageCount,jdbcType=INTEGER}, 
      #{messageParams,jdbcType=VARCHAR}, #{messageContent,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.crm.task.domain.entity.WeixingMessageEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WEIXING_MESSAGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="messageUserId != null">
        MESSAGE_USER_ID,
      </if>
      <if test="bizType != null and bizType != ''">
        BIZ_TYPE,
      </if>
      <if test="bizTypeEnum != null and bizTypeEnum != ''">
        BIZ_TYPE_ENUM,
      </if>
      <if test="mainTaskType != null">
        MAIN_TASK_TYPE,
      </if>
      <if test="subTaskType != null">
        SUB_TASK_TYPE,
      </if>
      <if test="messageCount != null">
        MESSAGE_COUNT,
      </if>
      <if test="messageParams != null and messageParams != ''">
        MESSAGE_PARAMS,
      </if>
      <if test="messageContent != null and messageContent != ''">
        MESSAGE_CONTENT,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="messageUserId != null">
        #{messageUserId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null and bizType != ''">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeEnum != null and bizTypeEnum != ''">
        #{bizTypeEnum,jdbcType=VARCHAR},
      </if>
      <if test="mainTaskType != null">
        #{mainTaskType,jdbcType=INTEGER},
      </if>
      <if test="subTaskType != null">
        #{subTaskType,jdbcType=INTEGER},
      </if>
      <if test="messageCount != null">
        #{messageCount,jdbcType=INTEGER},
      </if>
      <if test="messageParams != null and messageParams != ''">
        #{messageParams,jdbcType=VARCHAR},
      </if>
      <if test="messageContent != null and messageContent != ''">
        #{messageContent,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.crm.task.domain.entity.WeixingMessageEntity">
    <!--@mbg.generated-->
    update T_WEIXING_MESSAGE
    <set>
      <if test="messageUserId != null">
        MESSAGE_USER_ID = #{messageUserId,jdbcType=INTEGER},
      </if>
      <if test="bizType != null and bizType != ''">
        BIZ_TYPE = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeEnum != null and bizTypeEnum != ''">
        BIZ_TYPE_ENUM = #{bizTypeEnum,jdbcType=VARCHAR},
      </if>
      <if test="mainTaskType != null">
        MAIN_TASK_TYPE = #{mainTaskType,jdbcType=INTEGER},
      </if>
      <if test="subTaskType != null">
        SUB_TASK_TYPE = #{subTaskType,jdbcType=INTEGER},
      </if>
      <if test="messageCount != null">
        MESSAGE_COUNT = #{messageCount,jdbcType=INTEGER},
      </if>
      <if test="messageParams != null and messageParams != ''">
        MESSAGE_PARAMS = #{messageParams,jdbcType=VARCHAR},
      </if>
      <if test="messageContent != null and messageContent != ''">
        MESSAGE_CONTENT = #{messageContent,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.crm.task.domain.entity.WeixingMessageEntity">
    <!--@mbg.generated-->
    update T_WEIXING_MESSAGE
    set MESSAGE_USER_ID = #{messageUserId,jdbcType=INTEGER},
      BIZ_TYPE = #{bizType,jdbcType=VARCHAR},
      BIZ_TYPE_ENUM = #{bizTypeEnum,jdbcType=VARCHAR},
      MAIN_TASK_TYPE = #{mainTaskType,jdbcType=INTEGER},
      SUB_TASK_TYPE = #{subTaskType,jdbcType=INTEGER},
      MESSAGE_COUNT = #{messageCount,jdbcType=INTEGER},
      MESSAGE_PARAMS = #{messageParams,jdbcType=VARCHAR},
      MESSAGE_CONTENT = #{messageContent,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>