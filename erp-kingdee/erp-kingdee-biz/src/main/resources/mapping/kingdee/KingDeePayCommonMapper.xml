<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeePayCommonMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_PAY_COMMON-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_ID" jdbcType="VARCHAR" property="fId" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_QZOK_BDDJT_ID" jdbcType="VARCHAR" property="fQzokBddjtId" />
    <result column="F_BUSINESS_TYPE" jdbcType="VARCHAR" property="fBusinessType" />
    <result column="F_IN_STOCK_BUS_TYPE" jdbcType="VARCHAR" property="fInStockBusType" />
    <result column="F_SET_ACCOUNT_TYPE" jdbcType="VARCHAR" property="fSetAccountType" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_SUPPLIER_ID" jdbcType="VARCHAR" property="fSupplierId" />
    <result column="F_SETTLE_ORG_ID" jdbcType="VARCHAR" property="fSettleOrgId" />
    <result column="F_PAY_ORG_ID" jdbcType="VARCHAR" property="fPayOrgId" />
    <result column="F_ENTITY_DETAIL"
            javaType="com.vedeng.erp.kingdee.dto.KingDeePayCommonDetailDto"
            property="fEntityDetail"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_ID, F_DATE, 
    F_QZOK_BDDJT_ID, F_BUSINESS_TYPE, F_IN_STOCK_BUS_TYPE, F_SET_ACCOUNT_TYPE, F_BILL_TYPE_ID, 
    F_SUPPLIER_ID, F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_ENTITY_DETAIL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_PAY_COMMON
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_PAY_COMMON
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_PAY_COMMON (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      F_ID, F_DATE, F_QZOK_BDDJT_ID, 
      F_BUSINESS_TYPE, F_IN_STOCK_BUS_TYPE, F_SET_ACCOUNT_TYPE, 
      F_BILL_TYPE_ID, F_SUPPLIER_ID, F_SETTLE_ORG_ID, 
      F_PAY_ORG_ID, F_ENTITY_DETAIL)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fId,jdbcType=VARCHAR}, #{fDate,jdbcType=VARCHAR}, #{fQzokBddjtId,jdbcType=VARCHAR}, 
      #{fBusinessType,jdbcType=VARCHAR}, #{fInStockBusType,jdbcType=VARCHAR}, #{fSetAccountType,jdbcType=VARCHAR}, 
      #{fBillTypeId,jdbcType=VARCHAR}, #{fSupplierId,jdbcType=VARCHAR}, #{fSettleOrgId,jdbcType=VARCHAR}, 
      #{fPayOrgId,jdbcType=VARCHAR}, #{fEntityDetail,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_PAY_COMMON
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fId != null">
        F_ID,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fQzokBddjtId != null">
        F_QZOK_BDDJT_ID,
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE,
      </if>
      <if test="fInStockBusType != null">
        F_IN_STOCK_BUS_TYPE,
      </if>
      <if test="fSetAccountType != null">
        F_SET_ACCOUNT_TYPE,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fSupplierId != null">
        F_SUPPLIER_ID,
      </if>
      <if test="fSettleOrgId != null">
        F_SETTLE_ORG_ID,
      </if>
      <if test="fPayOrgId != null">
        F_PAY_ORG_ID,
      </if>
      <if test="fEntityDetail != null">
        F_ENTITY_DETAIL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtId != null">
        #{fQzokBddjtId,jdbcType=VARCHAR},
      </if>
      <if test="fBusinessType != null">
        #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fInStockBusType != null">
        #{fInStockBusType,jdbcType=VARCHAR},
      </if>
      <if test="fSetAccountType != null">
        #{fSetAccountType,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fSupplierId != null">
        #{fSupplierId,jdbcType=VARCHAR},
      </if>
      <if test="fSettleOrgId != null">
        #{fSettleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null">
        #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fEntityDetail != null">
        #{fEntityDetail,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity">
    <!--@mbg.generated-->
    update KING_DEE_PAY_COMMON
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        F_ID = #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtId != null">
        F_QZOK_BDDJT_ID = #{fQzokBddjtId,jdbcType=VARCHAR},
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fInStockBusType != null">
        F_IN_STOCK_BUS_TYPE = #{fInStockBusType,jdbcType=VARCHAR},
      </if>
      <if test="fSetAccountType != null">
        F_SET_ACCOUNT_TYPE = #{fSetAccountType,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fSupplierId != null">
        F_SUPPLIER_ID = #{fSupplierId,jdbcType=VARCHAR},
      </if>
      <if test="fSettleOrgId != null">
        F_SETTLE_ORG_ID = #{fSettleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null">
        F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fEntityDetail != null">
        F_ENTITY_DETAIL = #{fEntityDetail,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity">
    <!--@mbg.generated-->
    update KING_DEE_PAY_COMMON
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      F_ID = #{fId,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_QZOK_BDDJT_ID = #{fQzokBddjtId,jdbcType=VARCHAR},
      F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      F_IN_STOCK_BUS_TYPE = #{fInStockBusType,jdbcType=VARCHAR},
      F_SET_ACCOUNT_TYPE = #{fSetAccountType,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_SUPPLIER_ID = #{fSupplierId,jdbcType=VARCHAR},
      F_SETTLE_ORG_ID = #{fSettleOrgId,jdbcType=VARCHAR},
      F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      F_ENTITY_DETAIL = #{fEntityDetail,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_PAY_COMMON
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fDate != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokBddjtId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokBddjtId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBusinessType != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fBusinessType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_IN_STOCK_BUS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fInStockBusType != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fInStockBusType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SET_ACCOUNT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSetAccountType != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fSetAccountType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillTypeId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillTypeId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SUPPLIER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSupplierId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fSupplierId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SETTLE_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSettleOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fSettleOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_PAY_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fPayOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fPayOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_ENTITY_DETAIL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fEntityDetail != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fEntityDetail,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_PAY_COMMON
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_ID, F_DATE, 
      F_QZOK_BDDJT_ID, F_BUSINESS_TYPE, F_IN_STOCK_BUS_TYPE, F_SET_ACCOUNT_TYPE, F_BILL_TYPE_ID, 
      F_SUPPLIER_ID, F_SETTLE_ORG_ID, F_PAY_ORG_ID, F_ENTITY_DETAIL)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fId,jdbcType=VARCHAR}, #{item.fDate,jdbcType=VARCHAR}, #{item.fQzokBddjtId,jdbcType=VARCHAR}, 
        #{item.fBusinessType,jdbcType=VARCHAR}, #{item.fInStockBusType,jdbcType=VARCHAR}, 
        #{item.fSetAccountType,jdbcType=VARCHAR}, #{item.fBillTypeId,jdbcType=VARCHAR}, 
        #{item.fSupplierId,jdbcType=VARCHAR}, #{item.fSettleOrgId,jdbcType=VARCHAR}, #{item.fPayOrgId,jdbcType=VARCHAR}, 
        #{item.fEntityDetail,jdbcType=VARCHAR})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-12-06-->
  <select id="findByFQzokBddjtId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from KING_DEE_PAY_COMMON
    where F_QZOK_BDDJT_ID in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
</mapper>