package com.vedeng.api.standard.approval;

import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.BusinessExecutionBuilder;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.ValidationRule;
import com.vedeng.authorization.model.User;
import com.vedeng.system.service.UserService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.impl.identity.Authentication;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用审核执行器
 * 提供统一的多步审核执行能力，支持各种业务模块
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-26
 */
@Component
public class ApprovalExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(ApprovalExecutor.class);
    
    @Autowired
    private BusinessTemplate businessTemplate;
    
    @Autowired
    private UserService userService;
    
    private final ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
    
    /**
     * 执行循环审核，直到审核流程完成
     *
     * @param request API请求
     * @param approvalRequest 审核请求（必须实现ApprovalRequest接口）
     * @param config 审核配置
     * @param <T> 请求类型
     * @param <R> 响应类型
     * @return 审核结果
     * @throws Exception 执行异常
     */
    public <T extends ApprovalRequest, R> ApprovalResult executeMultiStepApproval(
            ApiRequest request,
            T approvalRequest,
            ApprovalConfig<T, R> config) throws Exception {

        List<ApprovalStepDetail> stepDetails = new ArrayList<>();
        User originalUser = getCurrentUser(request);
        int stepCount = 0;
        Object lastStepResult = null;

        try {
            logger.info("开始执行循环多步审核，初始taskId: {}", approvalRequest.getTaskId());

            // 循环执行审核步骤直到流程完成
            while (true) {
                stepCount++;

                // 获取当前任务
                TaskService taskService = processEngine.getTaskService();
                Task currentTask = taskService.createTaskQuery()
                        .taskId(approvalRequest.getTaskId())
                        .singleResult();

                if (currentTask == null) {
                    logger.info("任务不存在，可能流程已完成: {}", approvalRequest.getTaskId());
                    break;
                }

                // 获取当前任务的审核人
                String approverUsername = getCurrentTaskApprover(currentTask);
                logger.info("执行第{}步审核，任务: {}, 审核人: {}", stepCount, currentTask.getName(), approverUsername);

                // 切换到审核人身份
                switchUserContext(approverUsername, request);

                // 执行审核步骤（单独处理异常）
                Object stepResult;
                try {
                    stepResult = executeApprovalStep(request, approvalRequest, currentTask, config);
                    lastStepResult = stepResult;

                    // 记录成功的审核详情
                    ApprovalStepDetail stepDetail = recordApprovalStep(currentTask, approverUsername, stepCount, approvalRequest.getComment());
                    stepDetail.setSuccess(true);
                    stepDetails.add(stepDetail);

                    logger.info("第{}步审核执行成功", stepCount);

                } catch (Exception stepException) {
                    logger.error("第{}步审核执行失败，终止循环审核", stepCount, stepException);

                    // 记录失败的审核详情
                    ApprovalStepDetail failedStepDetail = recordApprovalStep(currentTask, approverUsername, stepCount, approvalRequest.getComment());
                    failedStepDetail.setSuccess(false);
                    failedStepDetail.setErrorMessage(stepException.getMessage());
                    stepDetails.add(failedStepDetail);

                    // 立即终止循环并返回失败结果
                    ApprovalResult failureResult = ApprovalResult.failure(
                        String.format("第%d步审核执行失败：%s", stepCount, stepException.getMessage()),
                        "STEP_EXECUTION_ERROR"
                    );
                    failureResult.setStepDetails(stepDetails);
                    failureResult.setTotalSteps(stepCount);
                    failureResult.setWorkflowCompleted(false);
                    return failureResult;
                }

                // 检查流程是否完成
                if (isWorkflowCompleted(stepResult)) {
                    logger.info("工作流程已完成，总共执行{}步审核", stepCount);
                    break;
                }

                // 获取下一个任务ID（如果流程继续）
                String nextTaskId = getNextTaskId(currentTask.getProcessInstanceId());
                if (nextTaskId == null) {
                    logger.info("没有找到下一个任务，流程可能已完成");
                    break;
                }

                // 更新审核请求中的taskId为下一个任务ID
                approvalRequest.setTaskId(nextTaskId);
                logger.info("准备执行下一步审核，nextTaskId: {}", nextTaskId);

                // 防止无限循环，最多执行10步
                if (stepCount >= 10) {
                    logger.warn("审核步骤超过10步，可能存在无限循环，强制退出");
                    break;
                }
            }

            // 构建审核结果
            return buildApprovalResult(lastStepResult, stepDetails, stepCount);

        } catch (Exception e) {
            logger.error("循环多步审核执行失败，已执行{}步", stepCount, e);
            return ApprovalResult.failure("多步审核执行失败：" + e.getMessage(), "EXECUTION_ERROR");
        } finally {
            // 恢复原始用户身份
            restoreUserContext(originalUser, request);
        }
    }

    /**
     * 获取当前用户
     */
    private User getCurrentUser(ApiRequest request) {
        return request.getCurrentUser();
    }
    
    /**
     * 获取当前任务的审核人
     */
    private String getCurrentTaskApprover(Task task) {
        String approverUsername = task.getAssignee();
        
        // 如果任务没有指定审核人，获取候选人中的第一个
        if (StringUtils.isEmpty(approverUsername)) {
            approverUsername = getFirstCandidateUser(task.getId());
        }
        
        // 如果还是没有，使用默认审核人
        if (StringUtils.isEmpty(approverUsername)) {
            approverUsername = "admin";
            logger.warn("任务 {} 没有找到审核人，使用默认审核人: {}", task.getId(), approverUsername);
        }
        
        return approverUsername;
    }
    
    /**
     * 获取任务的第一个候选用户
     */
    private String getFirstCandidateUser(String taskId) {
        try {
            List<IdentityLink> identityLinks = processEngine.getTaskService()
                    .getIdentityLinksForTask(taskId);

            for (IdentityLink identityLink : identityLinks) {
                if ("candidate".equals(identityLink.getType()) && identityLink.getUserId() != null) {
                    return identityLink.getUserId();
                }
            }
        } catch (Exception e) {
            logger.error("获取任务候选用户失败: " + taskId, e);
        }
        return null;
    }

    /**
     * 获取流程实例的下一个任务ID
     *
     * @param processInstanceId 流程实例ID
     * @return 下一个任务ID，如果没有则返回null
     */
    private String getNextTaskId(String processInstanceId) {
        try {
            TaskService taskService = processEngine.getTaskService();
            List<Task> tasks = taskService.createTaskQuery()
                    .processInstanceId(processInstanceId)
                    .list();

            if (tasks != null && !tasks.isEmpty()) {
                // 返回第一个任务的ID（通常流程中同时只有一个活动任务）
                Task nextTask = tasks.get(0);
                logger.info("找到下一个任务: taskId={}, taskName={}", nextTask.getId(), nextTask.getName());
                return nextTask.getId();
            }

            logger.info("流程实例 {} 没有找到活动任务，可能已完成", processInstanceId);
            return null;

        } catch (Exception e) {
            logger.error("获取下一个任务失败: processInstanceId=" + processInstanceId, e);
            return null;
        }
    }

    /**
     * 切换用户上下文
     *
     * @param username 审核人用户名
     * @param request API请求对象
     */
    private void switchUserContext(String username, ApiRequest request) {
        try {
            // 使用正确的方法签名，companyId默认为1
            User approver = userService.getByUsername(username, 1);
            if (approver == null) {
                throw new RuntimeException("审核人不存在: " + username);
            }

            // 更新 ApiRequest 的用户信息，这样 BusinessTemplate 会传递正确的用户到 Controller
            request.setCurrentUser(approver);

            // 设置 Activiti 认证用户，用于工作流引擎
            Authentication.setAuthenticatedUserId(approver.getUsername());

            logger.info("切换用户身份到: {} ({}), userId: {}", username,
                    approver.getUserDetail() != null ? approver.getUserDetail().getRealName() : username,
                    approver.getUserId());

        } catch (Exception e) {
            logger.error("切换用户身份失败: " + username, e);
            throw new RuntimeException("切换用户身份失败: " + username);
        }
    }

    /**
     * 恢复用户上下文
     *
     * @param originalUser 原始用户信息
     * @param request API请求对象
     */
    private void restoreUserContext(User originalUser, ApiRequest request) {
        try {
            if (originalUser != null) {
                // 恢复 ApiRequest 的用户信息
                request.setCurrentUser(originalUser);

                // 恢复 Activiti 认证用户
                Authentication.setAuthenticatedUserId(originalUser.getUsername());

                logger.info("恢复原始用户身份: {} ({}), userId: {}",
                        originalUser.getUsername(),
                        originalUser.getUserDetail() != null ? originalUser.getUserDetail().getRealName() : originalUser.getUsername(),
                        originalUser.getUserId());
            } else {
                // 清除 ApiRequest 的用户信息
                request.setCurrentUser(null);

                // 清除 Activiti 认证用户
                Authentication.setAuthenticatedUserId(null);

                logger.warn("原始用户信息为空，清除用户身份");
            }
        } catch (Exception e) {
            logger.error("恢复用户身份失败", e);
            // 这里不抛异常，避免影响主流程，但要确保清理工作
            try {
                Authentication.setAuthenticatedUserId(null);
            } catch (Exception cleanupException) {
                logger.error("清理用户身份失败", cleanupException);
            }
        }
    }

    /**
     * 执行审核步骤
     */
    private <T extends ApprovalRequest, R> Object executeApprovalStep(
            ApiRequest request, T approvalRequest, Task task, ApprovalConfig<T, R> config) throws Exception {

        logger.info("执行审核步骤: taskId={}, taskName={}", task.getId(), task.getName());

        // 更新请求中的taskId为当前任务ID
        approvalRequest.setTaskId(task.getId());

        // 使用配置中的信息调用 BusinessTemplate
        BusinessExecutionBuilder<T, R> builder = businessTemplate.<T, R>executeUpdate(request)
                .requestType(config.getRequestType())
                .responseType(config.getResponseType())
                .controller(config.getControllerBeanName(), config.getControllerMethodName())
                .responseConfig(config.getResponseConfig());

        // 添加验证规则（如果有的话）
        if (config.getValidationRules() != null && config.getValidationRules().length > 0) {
            // 类型转换：将 Class<?>[] 转换为 Class<? extends ValidationRule>[]
            @SuppressWarnings("unchecked")
            Class<? extends ValidationRule>[] validationRules =
                (Class<? extends ValidationRule>[]) config.getValidationRules();
            builder.validationRules(validationRules);
        }

        // 配置Controller方法参数（如果有参数配置函数的话）
        if (config.getParameterConfigFunction() != null) {
            ParameterConfig[] parameterConfigs = config.getParameterConfigFunction().apply(approvalRequest);

            if (config.isUseHttpParameters()) {
                // 使用包含HttpServletRequest的参数配置
                builder.withHttpParameters(parameterConfigs);
            } else {
                // 使用不包含HttpServletRequest的参数配置
                builder.withoutHttpParameters(parameterConfigs);
            }
        }

        return builder.execute();
    }

    /**
     * 记录审核步骤详情
     */
    private ApprovalStepDetail recordApprovalStep(Task task, String approverUsername, int stepCount, String comment) {
        try {
            User approver = userService.getByUsername(approverUsername, 1);
            String approverRealName = approver != null && approver.getUserDetail() != null
                    ? approver.getUserDetail().getRealName()
                    : approverUsername;

            ApprovalStepDetail stepDetail = new ApprovalStepDetail(
                    task.getId(),
                    task.getName(),
                    approverUsername,
                    approverRealName,
                    comment,
                    stepCount
            );

            logger.info("记录审核步骤: {}", stepDetail);
            return stepDetail;

        } catch (Exception e) {
            logger.error("记录审核步骤失败", e);
            ApprovalStepDetail stepDetail = new ApprovalStepDetail();
            stepDetail.setTaskId(task.getId());
            stepDetail.setTaskName(task.getName());
            stepDetail.setApproverUsername(approverUsername);
            stepDetail.setComment(comment);
            stepDetail.setStepNumber(stepCount);
            stepDetail.setSuccess(false);
            stepDetail.setErrorMessage(e.getMessage());
            return stepDetail;
        }
    }

    /**
     * 构建审核结果
     */
    private ApprovalResult buildApprovalResult(Object stepResult, List<ApprovalStepDetail> stepDetails, int totalSteps) {
        ApprovalResult result = ApprovalResult.success("多步审核执行成功", stepResult);
        result.setStepDetails(stepDetails);
        result.setTotalSteps(totalSteps);
        result.setWorkflowCompleted(isWorkflowCompleted(stepResult));

        logger.info("构建审核结果: success={}, totalSteps={}, workflowCompleted={}",
                result.isSuccess(), result.getTotalSteps(), result.isWorkflowCompleted());

        return result;
    }

    /**
     * 检查工作流是否完成
     *
     * @param stepResult 审核步骤的执行结果
     * @return true表示工作流已完成，false表示需要继续
     */
    private boolean isWorkflowCompleted(Object stepResult) {
        try {
            // 方法1：检查stepResult中的特定标识
            if (stepResult != null) {
                String resultStr = stepResult.toString();

                // 检查是否包含流程结束的标识
                if (resultStr.contains("endEvent") ||
                    resultStr.contains("\"data\":\"endEvent\"") ||
                    resultStr.contains("流程已结束") ||
                    resultStr.contains("审核完成")) {
                    logger.info("根据stepResult判断流程已完成: {}", resultStr);
                    return true;
                }
            }

            // 方法2：如果stepResult没有明确标识，返回false继续循环
            // 让循环逻辑通过getNextTaskId来判断是否还有下一步
            return false;

        } catch (Exception e) {
            logger.error("检查工作流状态失败", e);
            // 出现异常时，保守地认为流程未完成
            return false;
        }
    }
}
