package com.vedeng.order.dao;


import com.vedeng.order.model.NotCreateBussinessChanceReason;
import org.springframework.stereotype.Repository;

/**
 * <b>Description:未成商机原因mapper</b><br>
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/7/21
 */

public interface NotCreateBussinessChanceReasonMapper {
    int deleteByPrimaryKey(Integer notCreateBussinessChanceReasonId);

    int insert(NotCreateBussinessChanceReason record);

    int insertSelective(NotCreateBussinessChanceReason record);

    NotCreateBussinessChanceReason selectByPrimaryKey(Integer notCreateBussinessChanceReasonId);

    int updateByPrimaryKeySelective(NotCreateBussinessChanceReason record);

    int updateByPrimaryKey(NotCreateBussinessChanceReason record);
}