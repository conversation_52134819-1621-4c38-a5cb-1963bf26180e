@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-button {
  font-size: 14px;
  padding: 5px 14px;
  text-align: center;
  border-radius: 3px;
  border: 1px solid #BABFC2;
  display: inline-block;
  height: max-content;
  background-color: #F5F7FA;
  color: #333333;
  transition: all 0.1s linear;
  cursor: pointer;
  position: relative;
}
.vd-ui-button .vd-ui_icon {
  position: relative;
  top: 2px;
  font-size: 16px;
  margin-right: 3px;
  display: inline-block;
  line-height: 1;
  margin-left: -5px;
}
.vd-ui-button .loading {
  animation: loading 1.8s linear infinite;
}
.vd-ui-button.vd-ui-button--large {
  font-size: 16px;
  padding: 7.5px 19px;
}
.vd-ui-button.vd-ui-button--large .vd-ui_icon {
  font-size: 18px;
  margin-right: 7px;
}
.vd-ui-button.vd-ui-button--middle {
  font-size: 14px;
  padding: 5px 14px;
}
.vd-ui-button.vd-ui-button--middle .vd-ui_icon {
  font-size: 16px;
}
.vd-ui-button.vd-ui-button--small {
  font-size: 12px;
  padding: 3px 9px;
}
.vd-ui-button.vd-ui-button--small .vd-ui_icon {
  font-size: 14px;
}
.vd-ui-button:hover {
  background-color: #EBEFF2;
}
.vd-ui-button:active {
  background-color: #E1E5E8;
}
.vd-ui-button.is-disabled {
  cursor: not-allowed;
  color: #999999;
  border-color: #D7DADE;
  background-color: #F5F7FA;
}
.vd-ui-button.vd-ui-button--primary {
  border-color: #0099FF;
  background-color: #0099FF;
  color: #ffffff;
}
.vd-ui-button.vd-ui-button--primary:hover {
  border-color: #0087e0;
  background-color: #0087e0;
}
.vd-ui-button.vd-ui-button--primary:active {
  border-color: #006cb3;
  background-color: #006cb3;
  transition: all 0.1s linear;
}
.vd-ui-button.vd-ui-button--primary.is-disabled {
  cursor: not-allowed;
  border-color: #7fccff;
  background-color: #7fccff;
}
.vd-ui-button.vd-ui-button--primary.is-loading {
  cursor: wait;
  border-color: #7fccff;
  background-color: #7fccff;
}
.vd-ui-button.vd-ui-button--success {
  border-color: #13bf13;
  background-color: #13bf13;
  color: #ffffff;
}
.vd-ui-button.vd-ui-button--success:hover {
  border-color: #11a811;
  background-color: #11a811;
}
.vd-ui-button.vd-ui-button--success:active {
  border-color: #0d860d;
  background-color: #0d860d;
  transition: all 0.1s linear;
}
.vd-ui-button.vd-ui-button--success.is-disabled {
  cursor: not-allowed;
  border-color: #88df89;
  background-color: #88df89;
}
.vd-ui-button.vd-ui-button--warning {
  border-color: #FF6600;
  background-color: #FF6600;
  color: #ffffff;
}
.vd-ui-button.vd-ui-button--warning:hover {
  border-color: #e05a00;
  background-color: #e05a00;
}
.vd-ui-button.vd-ui-button--warning:active {
  border-color: #b34800;
  background-color: #b34800;
  transition: all 0.1s linear;
}
.vd-ui-button.vd-ui-button--warning.is-disabled {
  cursor: not-allowed;
  border-color: #feb380;
  background-color: #feb380;
}
.vd-ui-button.vd-ui-button--danger {
  border-color: #E64545;
  background-color: #E64545;
  color: #ffffff;
}
.vd-ui-button.vd-ui-button--danger:hover {
  border-color: #ca3d3d;
  background-color: #ca3d3d;
}
.vd-ui-button.vd-ui-button--danger:active {
  border-color: #a23131;
  background-color: #a23131;
  transition: all 0.1s linear;
}
.vd-ui-button.vd-ui-button--danger.is-disabled {
  cursor: not-allowed;
  border-color: #f1a2a2;
  background-color: #f1a2a2;
}
.vd-ui-button.vd-ui-button--text {
  border: none;
  background-color: transparent;
}
.vd-ui-button.vd-ui-button--text:hover {
  color: #0099FF;
}
.vd-ui-button.vd-ui-button--text.is-disabled {
  cursor: not-allowed;
  color: #999999;
}
.vd-ui-button.vd-ui-button--danger-text {
  border: none;
  color: #E64545;
  background-color: transparent;
}
.vd-ui-button.vd-ui-button--danger-text:hover {
  color: #ca3d3d;
}
.vd-ui-button.vd-ui-button--danger-text.is-disabled {
  cursor: not-allowed;
  color: #f1a2a2;
}
.vd-ui-button.vd-ui-button--success-text {
  border: none;
  background-color: transparent;
  color: #13bf13;
}
.vd-ui-button.vd-ui-button--success-text:hover {
  color: #11a811;
}
.vd-ui-button.vd-ui-button--success-text.is-disabled {
  cursor: not-allowed;
  color: #88df89;
}
.vd-ui-button.vd-ui-button--link-text {
  border: none;
  color: #0099FF;
  background-color: transparent;
}
.vd-ui-button.vd-ui-button--link-text:hover {
  color: #0087e0;
}
.vd-ui-button.vd-ui-button--link-text.is-disabled {
  cursor: not-allowed;
  color: #7fccff;
}
.vd-ui-select-button {
  display: flex;
  align-items: center;
  position: relative;
  margin-right: 10px;
}
.vd-ui-select-button .ui-select-btn-txt {
  background: #0099FF;
  color: #fff;
  border-radius: 3px 0 0 3px;
  border-right: 1px solid #0087E0;
  cursor: pointer;
  font-size: 14px;
}
.vd-ui-select-button .ui-select-btn-txt>div {
  padding: 6px 10px;
}
.vd-ui-select-button .ui-select-btn-txt:hover {
  background: #0087E0;
}
.vd-ui-select-button .ui-select-btn-more {
  width: 33px;
  height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: #0099FF;
  border-radius: 0 3px 3px 0;
  cursor: pointer;
}
.vd-ui-select-button .ui-select-btn-more .icon-down {
  font-size: 16px;
  line-height: 1;
  transition: transform 0.22s ease;
}
.vd-ui-select-button .ui-select-btn-more:hover {
  background: #0087E0;
}
.vd-ui-select-button .ui-select-btn-drop {
  background: #fff;
  border-radius: 3px;
  border: 1px solid #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 33px;
  left: 0;
  z-index: 9;
  width: 100%;
  padding: 5px 0;
  display: none;
}
.vd-ui-select-button .ui-select-btn-drop > div {
  padding: 6px 10px;
  cursor: pointer;
}
.vd-ui-select-button .ui-select-btn-drop > div:hover {
  background: #f5f7fa;
}
.vd-ui-select-button.open .ui-select-btn-drop {
  display: block;
}
.vd-ui-select-button.open .ui-select-btn-more .icon-down {
  transform: rotate(180deg);
}
.vd-ui-select-link {
  position: relative;
}
.vd-ui-select-link .ui-select-link-trigger {
  display: flex;
  align-items: center;
  color: #09f;
  cursor: pointer;
  font-size: 12px;
}
.vd-ui-select-link .ui-select-link-trigger:hover {
  color: #f60;
}
.vd-ui-select-link .ui-select-link-trigger .icon-down {
  font-size: 16px;
  line-height: 1;
  transition: transform 0.22s ease;
  margin-left: 3px;
}
.vd-ui-select-link .ui-select-btn-drop {
  background: #fff;
  border-radius: 3px;
  border: 1px solid #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 18px;
  left: 0;
  z-index: 9;
  width: 100%;
  padding: 5px 0;
  display: none;
}
.vd-ui-select-link .ui-select-btn-drop > div {
  padding: 6px 10px;
  cursor: pointer;
}
.vd-ui-select-link .ui-select-btn-drop > div:hover {
  background: #f5f7fa;
}
.vd-ui-select-link.open .ui-select-btn-drop {
  display: block;
}
.vd-ui-select-link.open .icon-down {
  transform: rotate(180deg);
}

.vd-ui-cascader {
  position: relative;
  width: 300px;
  height: 100%;
}
.vd-ui-cascader .vd-ui-cascader-wrapper {
  position: relative;
  width: 100%;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .icon {
  position: absolute;
  right: 5px;
  top: 1px;
  font-size: 16px;
  color: #666666;
  cursor: pointer;
  pointer-events: none;
  line-height: 33px;
  transition: 0.19s;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .icon.large {
  line-height: 42px;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .icon.small {
  line-height: 26px;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .icon.rotate {
  transform: rotate(180deg);
  transition: 0.22s;
}
.vd-ui-cascader .vd-ui-cascader-wrapper input::-webkit-input-placeholder {
  color: #999999;
}
.vd-ui-cascader .vd-ui-cascader-wrapper /deep/ .vd-ui-input input {
  cursor: pointer;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap {
  position: relative;
  color: #333;
  background: #fff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  height: 33px;
  padding: 0px 10px;
  font-size: 14px;
  padding: 0 40px 0 10px;
  display: flex;
  align-items: center;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  margin-right: 5px;
  max-width: 80%;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags .tag {
  position: relative;
  height: 25px;
  padding: 0 5px;
  background: #F5F7FA;
  border: solid 1px #E1E5E8;
  border-radius: 2px;
  font-size: 12px;
  line-height: 23px;
  color: #333;
  margin-right: 5px;
  padding-right: 22px;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags .tag .tag-del {
  width: 20px;
  height: 25px;
  font-size: 14px;
  color: #999;
  cursor: pointer;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags .tag .tag-del:hover {
  color: #f60;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags .tag-add {
  height: 25px;
  padding: 0 5px;
  background: #F5F7FA;
  border: solid 1px #E1E5E8;
  border-radius: 2px;
  font-size: 12px;
  line-height: 23px;
  color: #333;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .input {
  flex: 1;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .input > input {
  width: 100%;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .vd-ui-input__suffix {
  position: absolute;
  height: 100%;
  right: 5px;
  top: 0!important;
  text-align: center;
  color: #c0c4cc;
  transition: all 0.3s;
  pointer-events: none;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .vd-ui-input__suffix .vd-ui-input__suffix-inner .icon {
  top: 0!important;
}
.vd-ui-cascader .vd-ui-cascader-panel-wrap {
  display: inline-flex;
  position: absolute;
  z-index: 1500;
}
.vd-ui-cascader .vd-ui-cascader-menu {
  display: inline-flex;
  position: absolute;
  z-index: 1500;
}
.vd-ui-cascader .vd-ui-cascader-menu.width {
  width: 100%;
}
.vd-ui-cascader .vd-ui-cascader-menu.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-cascader .vd-ui-cascader-menu.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li {
  box-sizing: border-box;
  width: 300px;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li p {
  padding-left: 10px;
  height: 29px;
  line-height: 29px;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li p i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 2px;
  font-size: 16px;
  margin-right: 5px;
  color: #0099FF;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li {
  box-sizing: border-box;
  width: 300px;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li p {
  padding-left: 10px;
  height: 29px;
  line-height: 29px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li p i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li p .reload {
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-cascader .suggestion-list {
  width: 100%;
  box-sizing: border-box;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
}
.vd-ui-cascader .suggestion-list .filter-list {
  padding: 0 10px;
  max-height: 200px;
  overflow-y: auto;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-cascader .suggestion-list .filter-list .filter-item {
  height: 33px;
  line-height: 33px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.vd-ui-cascader .suggestion-list .filter-list .filter-item.active {
  color: #09f;
}
.vd-ui-cascader .suggestion-list .no-filter {
  padding: 5px 0;
  color: #999;
  text-align: center;
}
.vd-ui-cascader .menu-wrap {
  display: inline-flex;
}
@keyframes appear {
  0% {
    opacity: 0;
    -webkit-transform: scale(1, 0);
  }
  100% {
    -webkit-transform: scale(1, 1);
    opacity: 1;
  }
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-cascader-node {
  box-sizing: border-box;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
}
.vd-ui-cascader-node::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-cascader-node::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-cascader-node::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-cascader-node::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-cascader-node::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-cascader-node.multiple {
  width: 200px!important;
}
.vd-ui-cascader-node.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-cascader-node.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-cascader-node li {
  width: 100%;
  cursor: pointer;
  padding: 0px 10px;
  padding-right: 36px;
  height: 33px;
  box-sizing: border-box;
  line-height: 33px;
  position: relative;
  display: flex;
  align-items: center;
}
.vd-ui-cascader-node li.selected {
  color: #09f;
}
.vd-ui-cascader-node li p {
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.vd-ui-cascader-node li .icon-right {
  position: absolute ;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
}
.ui-cascader-checkbox-wrap {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  border: solid 1px #ddd;
  border-radius: 3px;
  margin-right: 5px;
}
.ui-cascader-checkbox-wrap.active {
  border: solid 1px #09f;
  background: #09f;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ui-cascader-checkbox-wrap.active .icon-selected2 {
  font-size: 12px;
  color: #fff;
}
.ui-cascader-checkbox-wrap.active .icon-deduct {
  font-size: 12px;
  color: #fff;
}

.vd-ui-checkbox-group {
  margin-bottom: -10px;
}
.vd-ui-checkbox-group .vd-ui-checkbox-item {
  margin-right: 20px;
  margin-bottom: 10px;
}
.vd-ui-checkbox-group .vd-ui-checkbox-item:last-child {
  margin-right: 0;
}
.vd-ui-checkbox-group .vd-ui-input-error {
  margin-top: -5px !important;
}
.vd-ui-checkbox-item {
  display: inline-block;
  cursor: pointer;
  color: #333333;
  vertical-align: top;
}
.vd-ui-checkbox-item * {
  box-sizing: border-box;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon {
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid #969B9E;
  margin-right: 5px;
  transition: all 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 3px;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0);
  transition: transform 0.1s;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2 {
  font-size: 12px;
  color: #ffffff;
}
.vd-ui-checkbox-item:hover .vd-ui-checkbox-icon {
  border-color: #0099FF;
}
.vd-ui-checkbox-item .vd-ui-checkbox-inner {
  display: flex;
}
.vd-ui-checkbox-item .vd-ui-checkbox-inner .strong {
  color: #f60;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon {
  background: #0099FF;
  border-color: #0099FF;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  transform: scale(1);
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon {
  background: #0099FF;
  border-color: #0099FF;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  transform: scale(1);
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2 {
  display: inline-block;
  width: 8px;
  height: 2px;
  background: #fff;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2::before {
  display: none;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled {
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled .vd-ui-checkbox-icon {
  border-color: #D7DADE;
  background: #F5F7FA;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon {
  border-color: #D7DADE;
  background: #D7DADE;
}
.vd-ui-radio-group {
  margin-bottom: -10px;
}
.vd-ui-radio-group .vd-ui-radio-item {
  margin-right: 20px;
  margin-bottom: 10px;
}
.vd-ui-radio-group .vd-ui-radio-item:last-child {
  margin-right: 0;
}
.vd-ui-radio-group .vd-ui-input-error {
  margin-top: -5px !important;
}
.vd-ui-radio-item {
  display: inline-block;
  cursor: pointer;
  color: #333333;
  vertical-align: top;
}
.vd-ui-radio-item * {
  box-sizing: border-box;
}
.vd-ui-radio-item .vd-ui-radio-icon {
  flex-shrink: 0;
  position: relative;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 1px solid #969B9E;
  margin-right: 5px;
  transition: all 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
}
.vd-ui-radio-item .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  width: 0;
  height: 0;
  background: #0099FF;
  transition: all 0.1s;
  border-radius: 50%;
}
.vd-ui-radio-item .vd-ui-raiod-label {
  white-space: nowrap;
}
.vd-ui-radio-item .vd-ui-raiod-tip {
  color: #999;
}
.vd-ui-radio-item:hover .vd-ui-radio-icon {
  border-color: #0099FF;
}
.vd-ui-radio-item .vd-ui-radio-inner {
  display: flex;
}
.vd-ui-radio-item.vd-ui-radio-item-checked .vd-ui-radio-icon {
  border-color: #0099FF;
}
.vd-ui-radio-item.vd-ui-radio-item-checked .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  width: 6px;
  height: 6px;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled {
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled .vd-ui-radio-icon {
  border-color: #D7DADE;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  background: #D7DADE;
}

.vd-ui-date * {
  margin: 0;
  padding: 0;
  list-style: none;
  box-sizing: border-box;
}
.vd-ui-date {
  position: relative;
  display: inline-block;
  text-align: left;
}
.vd-ui-date .vd-ui-date-editor--year,
.vd-ui-date .vd-ui-date-editor--month,
.vd-ui-date .vd-ui-date-editor--date,
.vd-ui-date .vd-ui-date-editor--datetime,
.vd-ui-date .vd-ui-date-editor--datetimerange,
.vd-ui-date .vd-ui-date-editor--daterange {
  width: 252px;
}
.vd-ui-date .vd-ui-date-editor--time {
  width: 180px;
}
.vd-ui-date .vd-ui-date-editor /deep/ .vd-ui-input__inner {
  cursor: pointer;
}
.vd-ui-date-wrapper {
  position: absolute;
  box-sizing: border-box;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  z-index: 1500;
  background-color: #ffffff;
  list-style-type: none;
  margin: 0;
}
.vd-ui-date-wrapper--year,
.vd-ui-date-wrapper--month,
.vd-ui-date-wrapper--date {
  width: 294px;
}
.vd-ui-date-wrapper--datetime {
  width: 474px;
}
.vd-ui-date-wrapper--time {
  width: 180px;
}
.vd-ui-date-wrapper.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-date-wrapper.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-panel .vd-ui-panel-header {
  background: #fff;
  color: #333;
  height: 46px;
  margin: 0 auto;
  padding: 0;
  border-bottom: solid 1px #ddd;
}
.vd-ui-panel .vd-ui-panel-header > ul {
  margin: 0 auto;
  height: 46px;
  width: 90%;
  display: flex;
  align-items: center;
}
.vd-ui-panel .vd-ui-panel-header > ul > li {
  cursor: pointer;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.year-month {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow {
  width: 30px;
  text-align: center;
  color: #666;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow i {
  display: block;
  font-size: 16px;
  color: #666;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow.arrow-left1 i {
  transform: rotate(-90deg);
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow.arrow-right1 i {
  transform: rotate(90deg);
}
.vd-ui-panel .vd-ui-panel-body {
  position: relative;
  margin: 5px 15px 5px;
}
.vd-ui-panel .vd-ui-panel-body table {
  table-layout: fixed;
  width: 100%;
}
.vd-ui-panel .date-shortcuts {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  border-top: solid 1px #E1E5E8;
  padding: 9px 0;
}
.vd-ui-panel .date-shortcuts .item-sc {
  position: relative;
  font-size: 12px;
  color: #09F;
  padding: 4px 9px;
  cursor: pointer;
}
.vd-ui-panel .date-shortcuts .item-sc::after {
  content: "";
  display: block;
  width: 0;
  height: 12px;
  border-right: solid 1px #e3e3e3;
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.vd-ui-panel .date-shortcuts .item-sc:last-child::after {
  display: none;
}
.vd-ui-date-table {
  font-size: 12px;
  user-select: none;
  width: 100%;
}
.vd-ui-date-table td {
  width: 36px;
  height: 36px;
  box-sizing: border-box;
  text-align: center;
  cursor: pointer;
  position: relative;
}
.vd-ui-date-table td > div {
  height: 100%;
  padding: 0;
  box-sizing: border-box;
}
.vd-ui-date-table td span {
  width: 36px;
  height: 36px;
  display: block;
  margin: 0 auto;
  line-height: 34px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 3px;
}
.vd-ui-date-table td.next-month,
.vd-ui-date-table td.prev-month {
  color: #C0C4CC;
}
.vd-ui-date-table td.today {
  position: relative;
}
.vd-ui-date-table td.today span {
  color: #0099FF;
  font-weight: bold;
  border: solid 1px #0099FF;
}
.vd-ui-date-table td.today.start-date span,
.vd-ui-date-table td.today.end-date span {
  color: #FFF;
}
.vd-ui-date-table td.available:hover span {
  background-color: #F5F7FA;
}
.vd-ui-date-table td.available:active span {
  background-color: #EBEFF2;
}
.vd-ui-date-table td.in-range div {
  background-color: #e0f3ff;
}
.vd-ui-date-table td.in-range div:hover {
  background-color: #4e7a96;
}
.vd-ui-date-table td.in-range div:hover span {
  background-color: #50bcff;
}
.vd-ui-date-table td.current:not(.disabled) span {
  color: #FFF;
  background-color: #0099FF;
}
.vd-ui-date-table td.start-date div,
.vd-ui-date-table td.end-date div {
  color: #FFF;
}
.vd-ui-date-table td.start-date span,
.vd-ui-date-table td.end-date span {
  background-color: #0099FF;
}
.vd-ui-date-table td.start-date div {
  margin-left: 5px;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}
.vd-ui-date-table td.end-date div {
  margin-right: 5px;
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}
.vd-ui-date-table td.disabled div {
  background-color: #F5F7FA;
  opacity: 1;
  cursor: not-allowed;
  color: #C0C4CC;
}
.vd-ui-date-table td.selected div {
  margin-left: 5px;
  margin-right: 5px;
  background-color: #F2F6FC;
  border-radius: 15px;
}
.vd-ui-date-table td.selected div:hover {
  background-color: #F2F6FC;
}
.vd-ui-date-table td.selected span {
  background-color: #0099FF;
  color: #FFF;
  border-radius: 15px;
}
.vd-ui-date-table th {
  position: relative;
  font-weight: 400;
  color: #606266;
  height: 36px;
  text-align: center;
}
.vd-ui-year-table {
  font-size: 12px;
  border-collapse: collapse;
}
.vd-ui-year-table .el-icon {
  color: #303133;
}
.vd-ui-year-table td {
  text-align: center;
  padding: 5px 3px;
  cursor: pointer;
}
.vd-ui-year-table td.today .cell {
  color: #000;
  font-weight: bold;
}
.vd-ui-year-table td.disabled .cell {
  background-color: #F5F7FA;
  cursor: not-allowed;
  color: #999;
}
.vd-ui-year-table td.disabled .cell:hover {
  color: #999;
}
.vd-ui-year-table td .cell {
  width: 80px;
  height: 36px;
  display: block;
  line-height: 36px;
  color: #333;
  margin: 0 auto;
}
.vd-ui-year-table td .cell:hover {
  background-color: #F5F7FA;
}
.vd-ui-year-table td.current:not(.disabled) .cell {
  color: #fff;
  background: #409eff;
  border-radius: 3px;
}
.vd-ui-month-table {
  font-size: 12px;
  border-collapse: collapse;
}
.vd-ui-month-table td {
  text-align: center;
  padding: 0px;
  cursor: pointer;
}
.vd-ui-month-table td div {
  height: 48px;
  padding: 5px 0;
  box-sizing: border-box;
}
.vd-ui-month-table td.today .cell {
  color: #000;
  font-weight: bold;
}
.vd-ui-month-table td.today.start-date .cell,
.vd-ui-month-table td.today.end-date .cell {
  color: #fff;
}
.vd-ui-month-table td.disabled .cell {
  background-color: #F5F7FA;
  cursor: not-allowed;
  color: #999;
}
.vd-ui-month-table td.disabled .cell:hover {
  color: #999;
}
.vd-ui-month-table td .cell {
  width: 58px;
  height: 36px;
  display: block;
  line-height: 36px;
  color: #333;
  margin: 0 auto;
  border-radius: 3px;
}
.vd-ui-month-table td .cell:hover {
  background: #F5F7FA;
}
.vd-ui-month-table td.in-range div {
  background-color: #F2F6FC;
}
.vd-ui-month-table td.in-range div:hover {
  background-color: #F2F6FC;
}
.vd-ui-month-table td.start-date div,
.vd-ui-month-table td.end-date div {
  color: #fff;
}
.vd-ui-month-table td.start-date .cell,
.vd-ui-month-table td.end-date .cell {
  color: #FFF;
  background-color: #409EFF;
}
.vd-ui-month-table td.start-date div {
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.vd-ui-month-table td.end-date div {
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.vd-ui-month-table td.current:not(.disabled) .cell {
  background: #409EFF;
  border-radius: 3px;
  color: #fff;
}
.vd-ui-time-panel {
  width: 180px;
}
.vd-ui-time-panel .vd-ui-time-panel__header {
  height: 46px;
  font-size: 14px;
  color: #333333;
  line-height: 46px;
  text-align: center;
  border-bottom: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content {
  border-bottom: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list {
  font-size: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item {
  display: inline-block;
  vertical-align: top;
  width: 33.32%;
  height: 264px;
  overflow: hidden;
  border-right: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item:last-child {
  border-right: none;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul {
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-y: auto;
  padding-right: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li {
  position: relative;
  font-size: 14px;
  text-align: center;
  height: 33px;
  line-height: 33px;
  padding-left: 6px;
  cursor: pointer;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li:hover {
  background: #edf0f2;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li.active {
  color: #09f;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar-track {
  background: transparent;
  width: 0;
  height: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover {
  padding-right: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-time-panel .vd-ui-time-panel__footer {
  height: 40px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vd-ui-time-panel .vd-ui-time-panel__footer .time-cancel {
  border: none;
  background: none;
  font-size: 12px;
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-time-panel .vd-ui-time-panel__footer .time-confirm {
  width: 44px;
  height: 26px;
  border: none;
  background: #0099FF;
  border-radius: 3px;
  font-size: 12px;
  color: #ffffff;
  cursor: pointer;
}
.vd-ui-datetime-panel {
  width: 474px;
  box-sizing: border-box;
}
.vd-ui-datetime-panel * {
  box-sizing: border-box;
}
.vd-ui-datetime-panel .datetime-content {
  display: flex;
}
.vd-ui-datetime-panel .datetime-content .datetime-date-panel {
  width: 292px;
  border-right: solid 1px #E1E5E8;
}
.vd-ui-datetime-panel .datetime-content .datetime-time-panel {
  width: 180px;
}
.vd-ui-datetime-panel .datetime-btn {
  width: calc(100% - 2px);
  height: 40px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: solid 1px #edf0f2;
}
.vd-ui-datetime-panel .datetime-btn .time-cancel {
  border: none;
  background: none;
  font-size: 12px;
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-datetime-panel .datetime-btn .time-confirm {
  width: 44px;
  height: 26px;
  border: none;
  background: #0099FF;
  border-radius: 3px;
  font-size: 12px;
  color: #ffffff;
  cursor: pointer;
}
.vd-ui-range {
  width: 252px;
  height: 33px;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  padding: 3px 10px;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
}
.vd-ui-range .icon-date {
  font-size: 16px;
  color: #666;
  margin-right: 4px;
}
.vd-ui-range .range-error2 {
  width: 16px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-range .range-error2 .icon-error2 {
  font-size: 16px;
  color: #666;
  cursor: pointer;
}
.vd-ui-range .range-error2 .icon-error2:hover {
  color: #333;
}
.vd-ui-range .split {
  font-size: 14px;
  color: #333;
  margin: 0 5px;
}
.vd-ui-range .input-box {
  flex: 1;
  height: 100%;
  cursor: pointer;
}
.vd-ui-range .input-box input {
  appearance: none;
  border: none;
  outline: none;
  display: inline-block;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: #333;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.1s linear;
}
.vd-ui-range .input-box input::placeholder {
  color: #999;
}
.vd-ui-range .input-box input:hover {
  border-color: #969B9E;
}
.vd-ui-daterange-panel * {
  box-sizing: border-box;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper {
  position: relative;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__sidebar {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100px;
  border-right: 1px solid #e4e4e4;
  box-sizing: border-box;
  padding-top: 15px;
  background-color: #fff;
  overflow: auto;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__sidebar .vd-ui-panel__shortcut {
  display: block;
  padding-left: 12px;
  width: 100%;
  background-color: transparent;
  border: 0;
  font-size: 12px;
  color: #09F;
  text-align: left;
  margin-bottom: 15px;
  outline: none;
  cursor: pointer;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content {
  float: left;
  width: 50%;
  box-sizing: border-box;
  margin: 0;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content.is-left {
  border-right: solid 1px #edf0f2;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header {
  position: relative;
  background: #fff;
  height: 46px;
  color: #333;
  margin: 0 auto;
  padding: 0;
  border-bottom: solid 1px #ddd;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header .header-label {
  margin: 0 50px;
  text-align: center;
  line-height: 46px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow {
  font-size: 12px;
  color: #333;
  border: 0;
  background: 0 0;
  cursor: pointer;
  outline: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow1 {
  left: 10px;
  transform: translateY(-50%) rotate(-90deg);
  margin-top: -1px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow2 {
  left: 30px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow3 {
  right: 30px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow4 {
  right: 10px;
  transform: translateY(-50%) rotate(90deg);
  margin-top: -1px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.disable {
  color: #ccc;
  cursor: not-allowed;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul {
  margin: 0 auto;
  width: 100%;
  height: 46px;
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li {
  cursor: pointer;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.year-month {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow {
  width: 30px;
  text-align: center;
  color: #666;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow i {
  display: block;
  font-size: 16px;
  color: #666;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow.arrow-left1 i {
  transform: rotate(-90deg);
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow.arrow-right1 i {
  transform: rotate(90deg);
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body::after {
  content: "";
  display: block;
  clear: both;
}

.vd-ui-dialog_wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2000;
}
.vd-ui-dialog_wrapper .vd-ui-dialog {
  width: 480px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  margin: 0px auto 50px;
  background-color: #ffffff;
  border-radius: 5px;
  position: relative;
  margin-top: 15vh;
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head {
  position: relative;
  padding: 10px 20px 13px;
  background-color: #F5F7FA;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-bottom: 1px solid #e1e5e8;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_title {
  font-size: 16px;
  line-height: 21px;
  display: block;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_headerBtn {
  position: absolute;
  padding: 0 10px;
  right: 0;
  top: 0;
  height: 44px;
  line-height: 44px;
  cursor: pointer;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_headerBtn i {
  font-size: 24px;
  color: #999999;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_head .vd-ui-dialog_head_headerBtn:hover i {
  color: #666666;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_content {
  position: relative;
  padding: 20px;
  padding-bottom: 0px;
  flex: 1;
  overflow: auto;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_footer {
  padding: 20px;
  padding-bottom: 0px;
  display: flex;
  justify-content: flex-end;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_footer .vd-ui-button {
  margin-right: 10px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog .vd-ui-dialog_footer .vd-ui-button:last-child {
  margin-right: 0;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in {
  max-height: 650px;
  top: 50%;
  margin-top: 0;
  transform: translateY(-50%);
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content {
  position: relative;
  flex: 1;
  overflow: auto;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-dialog_wrapper .vd-ui-dialog--in .vd-ui-dialog_content::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.fade-wrapper-enter {
  opacity: 0;
}
.fade-wrapper-leave-to {
  opacity: 0;
}
.fade-wrapper-enter-active {
  transition: all 0.22s ease-out;
}
.fade-wrapper-leave-active {
  transition: all 0.19s ease-in;
}
.vd-ui-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  background-color: #000000;
  opacity: 0.6;
}
.vd-ui-modal-modal-enter {
  animation: modal-in 0.22s ease-out;
}
.vd-ui-modal-modal-leave {
  animation: modal-out 0.19s ease-in;
}
@keyframes modal-in {
  0% {
    opacity: 0;
  }
}
@keyframes modal-out {
  100% {
    opacity: 0;
  }
}
.ui-poper-wrap {
  position: fixed;
  z-index: 3000;
}
.ui-poper-wrap.hidden {
  opacity: 0;
  z-index: -1;
}

.vd-ui-input {
  display: inline-block;
  vertical-align: top;
  position: relative;
}
.vd-ui-input .vd-ui-input__icon {
  font-size: 16px;
  color: #666666;
  position: relative;
  top: 2.5px;
  margin: 0 5px;
}
.vd-ui-input .vd-ui-input__icon::after {
  content: "";
  height: 100%;
  width: 0;
  display: inline-block;
  vertical-align: middle;
}
.vd-ui-input .icon-type {
  position: absolute;
  font-size: 16px;
  margin-left: 10px;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
}
.vd-ui-input .vd-ui-input__inner {
  color: #333333;
  background-color: #ffffff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  height: 33px;
  padding: 0px 10px;
  font-size: 14px;
  width: 100%;
  transition: border-color 0.1s linear;
}
.vd-ui-input .vd-ui-input__inner::placeholder {
  color: #999999;
}
.vd-ui-input .vd-ui-input__inner:hover {
  border-color: #969B9E;
}
.vd-ui-input.vd-ui-input--error > .vd-ui-input__inner {
  border-color: #e64545;
}
.vd-ui-input .vd-ui-input-text {
  color: #999;
  margin-top: 5px;
  position: absolute;
}
.vd-ui-input.vd-ui-input--large .vd-ui-input__inner {
  height: 42px;
  padding: 0px 10px;
  font-size: 16px;
}
.vd-ui-input.vd-ui-input--small .vd-ui-input__inner {
  height: 26px;
  padding: 0px 10px;
  font-size: 12px;
}
.vd-ui-input.vd-ui-input--small .vd-ui-input__inner::placeholder {
  font-size: 12px;
}
.vd-ui-input.is-disabled .vd-ui-input__inner {
  background-color: #F5F7FA;
  border-color: #D7DADE;
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-input.is-focus .vd-ui-input__inner {
  border-color: #0099FF;
}
.vd-ui-input.vd-ui-input-error .vd-ui-input__inner {
  border-color: #E64545;
}
.vd-ui-input.vd-ui-input--prefix .vd-ui-input__inner {
  padding-left: 36px;
}
.vd-ui-input.vd-ui-input--prefix .vd-ui-input__prefix {
  position: absolute;
  top: 0;
  left: 5px;
  max-height: 35px;
  height: 100%;
  pointer-events: none;
}
.vd-ui-input.vd-ui-input--suffix .vd-ui-input__inner {
  padding-right: 36px;
}
.vd-ui-input.vd-ui-input--suffix .vd-ui-input__suffix {
  position: absolute;
  top: 0;
  right: 5px;
  max-height: 35px;
  height: 100%;
  pointer-events: none;
}
.vd-ui-input.vd-ui-input--suffix .vd-ui-input__suffix .vd-ui-input__suffix-inner .vd-ui-input__icon {
  pointer-events: all;
  cursor: pointer;
}
.vd-ui-input.vd-ui-input-group {
  display: inline-table;
}
.vd-ui-input.vd-ui-input-group .vd-ui-input__inner {
  flex: 1;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--prepend .vd-ui-input-group__prepend {
  background-color: #F5F7FA;
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid #BABFC2;
  border-radius: 4px;
  padding: 0 10px;
  white-space: nowrap;
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  text-align: center;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--prepend .vd-ui-input__inner {
  display: table-cell;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--append .vd-ui-input-group__append {
  background-color: #F5F7FA;
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid #BABFC2;
  border-radius: 4px;
  padding: 0 10px;
  white-space: nowrap;
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  text-align: center;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--append .vd-ui-input-group__append .icon {
  font-size: 16px;
  margin: 0 5px;
  position: relative;
  top: 1px;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--append .vd-ui-input__inner {
  display: table-cell;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.vd-ui-input .vd-ui-input--error {
  color: #E64545;
}
.vd-ui-input .vd-ui-input--error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  margin-top: 1px;
}
.vd-ui-input .vd-ui-input--error .vd-ui-input-error--errmsg {
  margin: 0px;
}
.vd-ui-textarea {
  position: relative;
  display: inline-block;
}
.vd-ui-textarea .vd-ui-textarea__inner {
  width: 100%;
  min-height: 33px;
  height: 33px;
  display: block;
  resize: vertical;
  color: #333333;
  background-color: #ffffff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 14px;
  transition: border-color 0.1s linear;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-textarea .vd-ui-textarea__inner::placeholder {
  color: #999999;
}
.vd-ui-textarea .vd-ui-textarea__inner:hover {
  border-color: #969B9E;
}
.vd-ui-textarea .vd-ui-textarea__inner:focus {
  border-color: #0099FF;
}
.vd-ui-textarea.is-disabled .vd-ui-textarea__inner {
  background-color: #F5F7FA;
  border-color: #D7DADE;
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-textarea .vd-ui-textarea-place {
  position: relative;
  display: inline-block;
}
.vd-ui-textarea .vd-ui-textarea-place .vd-ui-input__count {
  color: #999999;
  position: absolute;
  bottom: 0;
  left: calc(100% + 5px);
  text-align: right;
  white-space: nowrap;
}
.vd-ui-textarea .vd-ui-textarea-place .vd-ui-input__count.upper-limit {
  color: #FF6600;
}
.vd-ui-number-range {
  display: flex;
  align-items: center;
}
.vd-ui-number-range .range-gap {
  margin: 0 5px 0 4px;
}
.vd-ui-number-input {
  display: flex;
  position: relative;
}
.vd-ui-number-input .vd-ui-button {
  width: 20px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
  color: #999;
  user-select: none;
}
.vd-ui-number-input .vd-ui-button.left {
  border-radius: 3px 0 0 3px;
}
.vd-ui-number-input .vd-ui-button.right {
  border-radius: 0 3px 3px 0;
}
.vd-ui-number-input .vd-ui-input {
  flex: 1;
  margin: 0 -1px;
  position: relative;
  z-index: 1;
}
.vd-ui-number-input .vd-ui-input .vd-ui-input__inner {
  border-radius: 0;
  text-align: center;
  padding: 0 5px;
}
.vd-ui-number-input .bubble-tip-wrap {
  bottom: 32px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  z-index: -1;
  pointer-events: none;
  transition: opacity 0.22s ease;
}
.vd-ui-number-input .bubble-tip-wrap.show {
  opacity: 1;
  z-index: 11;
  pointer-events: all;
}

.more-contact-component {
  position: relative;
  margin-bottom: 20px;
}
.more-contact-component .contact-list .contact-item {
  position: relative;
}
.more-contact-component .contact-list .contact-item .delete {
  position: absolute;
  left: 573px;
  top: 0;
  z-index: 5;
  width: 34px;
  height: 33px;
}
.more-contact-component .contact-list .contact-item .delete > i {
  width: 34px;
  height: 33px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.more-contact-component .contact-list .contact-item .delete > i:hover {
  color: #f60;
}
.more-contact-component .add-contact {
  padding-left: 250px;
  margin-top: 20px;
}
.more-contact-component .add-contact > a {
  font-size: 14px;
  color: #09f;
  cursor: pointer;
}
.more-contact-component .add-contact > a:hover {
  color: #f60;
}
.modal-form-wrap {
  min-height: 32px;
}
.other {
  margin-top: 10px;
}
.other .tips {
  margin-top: 5px;
  color: #999;
}

.vd-ui-page {
  position: relative;
  display: flex;
  justify-content: flex-end;
}
.vd-ui-page .vd-ui-page-list {
  display: flex;
  justify-content: center;
}
.vd-ui-page .vd-ui-page-jump {
  display: flex;
  margin-left: 20px;
}
.vd-ui-page .vd-ui-page-jump .vd-ui-input {
  width: 80px !important;
  margin-right: -1px;
  position: relative;
  z-index: 1;
}
.vd-ui-page .vd-ui-page-jump .vd-ui-input .vd-ui-input__inner {
  border-radius: 3px 0 0 3px;
}
.vd-ui-page .vd-ui-page-jump .vd-ui-button {
  border-radius: 0 3px 3px 0;
}
.vd-ui-page .vd-ui-page-total-txt {
  line-height: 33px;
  position: absolute;
  left: 0;
  top: 0;
}
.vd-ui-page .vd-ui-page-total-txt.vd-ui-total-small {
  line-height: 29px;
}
.vd-ui-page .vd-ui-page-item {
  width: 33px;
  height: 33px;
  text-align: center;
  line-height: 31px;
  background: #F5F7FA;
  border-radius: 3px;
  box-sizing: border-box;
  border: solid 1px #BABFC2;
  margin-right: 5px;
  cursor: pointer;
  text-decoration: none;
  color: #333333;
}
.vd-ui-page .vd-ui-page-item:hover {
  background: #EBEFF2;
}
.vd-ui-page .vd-ui-page-item.vd-ui-page-active {
  background: #0099FF;
  border-color: #0099FF;
  color: #ffffff;
}
.vd-ui-page .vd-ui-page-item.vd-ui-page-item-wider {
  width: auto;
  padding: 0 15px;
}
.vd-ui-page .vd-ui-page-item:last-child {
  margin-right: 0;
}
.vd-ui-page .vd-ui-page-item .icon-app-left,
.vd-ui-page .vd-ui-page-item .icon-app-right {
  vertical-align: -2px;
  font-size: 16px;
}
.vd-ui-page .vd-ui-page-disabled {
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-page .vd-ui-page-disabled:hover {
  background: #F5F7FA;
}
.vd-ui-page .vd-ui-page-omit {
  margin: 0 10px 0 5px;
  line-height: 33px;
}
.vd-ui-page .vd-ui-page-small .vd-ui-page-item {
  width: 29px;
  height: 29px;
  line-height: 27px;
}
.vd-ui-page .vd-ui-page-small .vd-ui-page-item.vd-ui-page-item-wider {
  width: auto;
  padding: 0 10px;
}

.ui-card-switch-wrap {
  display: flex;
  align-items: center;
  margin-left: 10px;
}
.ui-card-switch-wrap .ui-card-swich-item {
  width: 33px;
  height: 33px;
  background-color: #F5F7FA;
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
  border: 1px solid #BABFC2;
  margin-right: -1px;
  cursor: pointer;
}
.ui-card-switch-wrap .ui-card-swich-item.line-1 {
  background-image: url(/static/image/common/switch-line-1.svg);
}
.ui-card-switch-wrap .ui-card-swich-item.line-2 {
  background-image: url(/static/image/common/switch-line-2.svg);
}
.ui-card-switch-wrap .ui-card-swich-item:hover {
  background-color: #EBEFF2;
}
.ui-card-switch-wrap .ui-card-swich-item:active {
  background-color: #E1E5E8;
}
.ui-card-switch-wrap .ui-card-swich-item.active {
  background-color: #fff;
}
.ui-card-switch-wrap .ui-card-swich-item.active.line-1 {
  background-image: url(/static/image/common/switch-line-1-active.svg);
}
.ui-card-switch-wrap .ui-card-swich-item.active.line-2 {
  background-image: url(/static/image/common/switch-line-2-active.svg);
}
.ui-card-switch-wrap .ui-card-swich-item:first-child {
  border-radius: 3px 0 0 3px;
}
.ui-card-switch-wrap .ui-card-swich-item:last-child {
  border-radius: 0 3px 3px 0;
}

.ui-global-toast {
  display: flex;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  top: 110px;
  padding: 12px 20px 12px 15px;
  background-color: #fff;
  border-radius: 5px;
  z-index: 99999;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  align-items: center;
}
.ui-global-toast.ui-message-success {
  background: #E3F7E3;
}
.ui-global-toast.ui-message-error {
  background: #FCE9E9;
}
.ui-global-toast.ui-message-warn {
  background: #FFEDE0;
}
.ui-global-toast.ui-message-info {
  background: #E0F3FF;
}
.ui-global-toast i {
  position: relative;
  top: 1px;
  font-size: 20px;
  line-height: 1;
}
.ui-global-toast .icon-yes2 {
  color: #13BF13;
}
.ui-global-toast .icon-error2 {
  color: #E64545;
}
.ui-global-toast .icon-info2 {
  color: #09f;
}
.ui-global-toast .icon-caution2 {
  color: #f60;
}
.ui-global-toast span {
  max-width: 570px;
  margin-left: 10px;
}
.globalToast-enter-active {
  transition: 220ms ease-out;
}
.globalToast-leave-active {
  transition: 190ms ease-in;
}
.globalToast-enter,
.globalToast-leave-to {
  opacity: 0;
  transform: translate(-50%, -30px);
}
.popup-fade-enter-active {
  transition: all 0.15s ease-out;
}
.popup-fade-leave-active {
  transition: all 0.15s ease-in;
}
.popup-fade-enter,
.popup-fade-leave-to {
  opacity: 0;
}
.popup-move-enter-active {
  transition: all 0.15s ease-out;
}
.popup-move-leave-active {
  transition: all 0.15s ease-in;
}
.popup-move-enter,
.popup-move-leave-to {
  opacity: 0;
  transform: translate3d(0, -30px, 0);
}
.ui-popup-message-box-wrapper {
  position: fixed;
  z-index: 2019;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  text-align: center;
}
.ui-popup-message-box-wrapper::after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  vertical-align: middle;
}
.ui-popup-message-box {
  display: inline-block;
  width: 480px;
  min-height: 128px;
  background-color: #FFFFFF;
  border-radius: 5px;
  vertical-align: middle;
  text-align: left;
}
.ui-popup-message-box .msg-title {
  display: flex;
  justify-content: flex-end;
  height: 30px;
}
.ui-popup-message-box .msg-fork {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
}
.ui-popup-message-box .msg-fork:hover {
  color: #333333;
}
.ui-popup-message-box .msg-fork:hover .icon-delete {
  color: #333333;
}
.ui-popup-message-box .msg-fork .icon-delete {
  font-size: 24px;
  color: #CCCCCC;
}
.ui-popup-message-box .msg-fork .icon-delete:hover {
  color: #333333;
}
.ui-popup-message-box .msg-content {
  display: flex;
  padding: 0 20px;
}
.ui-popup-message-box .msg-content .vd-ui_icon {
  font-size: 32px;
  line-height: 1;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-info2 {
  color: #0099FF;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-yes2 {
  color: #13bf13;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-caution2 {
  color: #ff6600;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-error2 {
  color: #e64545;
}
.ui-popup-message-box .msg-content .msg-tip-title {
  width: 398px;
  padding-top: 2px;
  padding-left: 10px;
  padding-right: 20px;
  font-size: 18px;
  font-weight: 700;
}
.ui-popup-message-box .msg-content .msg-tip-word {
  padding-top: 5px;
  padding-left: 10px;
  word-break: break-all;
}
.ui-popup-message-box .msg-button-choice {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
}
.ui-popup-message-box .msg-button-choice .vd-button {
  border: 1px solid #BABFC2;
  cursor: pointer;
  border-radius: 3px;
  padding: 5px 14px;
  background-color: #F5F7FA;
  margin-left: 10px;
}
.ui-popup-message-box .msg-button-choice .vd-button:hover {
  background-color: #EDF0F2;
}
.ui-popup-message-box .msg-button-choice .vd-button.confirm {
  border-color: #09f;
  background-color: #09f;
  color: #fff;
}
.ui-popup-message-box .msg-button-choice .vd-button.confirm:hover {
  border-color: #008ae5;
  background-color: #008ae5;
}
.ui-popup-message-box .msg-button-choice .vd-button.cannel {
  border: 1px solid #CED2D9;
}
.ui-popup-message-box .msg-button-choice .vd-button.cannel:hover {
  border: solid 1px #B6BABF;
}
.ui-popup-message-box .msg-button-choice .vd-button.delete {
  border-color: #E64545;
  background-color: #E64545;
  color: #fff;
}
.ui-popup-message-box .msg-button-choice .vd-button.delete:hover {
  border-color: #cc2929;
  background-color: #cc2929;
}

.vd-ui-phone-related {
  position: relative;
  display: flex;
}
.vd-ui-phone-related > .vd-ui-input {
  width: 300px;
}
.vd-ui-phone-related > .vd-ui-search-related {
  max-height: 338px;
  overflow-y: auto;
  border-radius: 3px;
  background: #fff;
  border: solid 1px #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 33px;
  z-index: 9;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-phone-related > .vd-ui-search-related .loading {
  color: #666;
  height: 39px;
  line-height: 37px;
  padding: 0px 10px;
  overflow: hidden;
}
.vd-ui-phone-related > .vd-ui-search-related .loading i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 1px;
  font-size: 16px;
  margin-right: 5px;
  color: #09F;
}
.vd-ui-phone-related > .vd-ui-search-related .failed-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
}
.vd-ui-phone-related > .vd-ui-search-related .failed-li i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-phone-related > .vd-ui-search-related .failed-li .reload {
  color: #09f;
  cursor: pointer;
}
.vd-ui-phone-related > .vd-ui-search-related .empty-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
  text-align: center;
  color: #999;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list {
  padding: 5px 10px;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item {
  padding: 6px 0;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item .name {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item .mobile {
  width: 120px;
  flex-shrink: 0;
  text-align: right;
  color: #999;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item:hover .name {
  color: #f60;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item:hover .mobile {
  color: #f60;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-select {
  position: relative;
  width: 300px;
  height: 100%;
}
.vd-ui-select-wrapper {
  position: relative;
  width: 100%;
}
.vd-ui-select-wrapper .icon {
  position: absolute;
  right: 5px;
  font-size: 16px;
  color: #666666;
  cursor: pointer;
  pointer-events: none;
  transition: 0.19s;
  top: 8px;
  line-height: 1;
}
.vd-ui-select-wrapper .icon.large {
  line-height: 42px;
}
.vd-ui-select-wrapper .icon.small {
  line-height: 26px;
  top: 0;
}
.vd-ui-select-wrapper .icon.rotate {
  transform: rotate(180deg);
  transition: 0.22s;
}
.vd-ui-select-wrapper input::-webkit-input-placeholder {
  color: #999999;
}
.vd-ui-select-wrapper .vd-ui-input input {
  cursor: pointer;
}
.vd-ui-select-wrapper__disabled .icon {
  color: #999999;
}
.vd-ui-select-wrapper__error .vd-ui-input input {
  border-color: #E64545 !important;
}
.vd-ui-select-multiple-wrapper {
  position: relative;
  background-color: #ffffff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  min-height: 33px;
  box-sizing: border-box;
  transition: border-color 0.1s linear;
  cursor: pointer;
  padding: 3px 5px;
  padding-right: 36px;
  display: flex;
  align-items: center;
}
.vd-ui-select-multiple-wrapper:hover {
  border-color: #969B9E;
}
.vd-ui-select-multiple-wrapper.is-focus {
  border-color: #0099FF;
}
.vd-ui-select-multiple-wrapper.is-focus:hover {
  border-color: #0099FF;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag {
  max-width: 80%;
  font-size: 0;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .placeholder {
  margin-left: 5px;
  margin-top: 4px;
  color: #999999;
  font-size: 14px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag.has-more .vd-ui-select-tag {
  max-width: calc(100% - 38px);
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag {
  padding-left: 5px;
  padding-right: 24px;
  background-color: #F5F7FA;
  font-size: 12px;
  border-radius: 2px;
  border: 1px solid #E1E5E8;
  position: relative;
  margin-right: 5px;
  display: inline-block;
  height: 25px;
  box-sizing: border-box;
  max-width: calc(100% - 5px);
  transform-origin: center center;
  animation: tagAppear 0.19s ease-in;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag.tag-more {
  padding-right: 5px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag > span {
  line-height: 23px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag .tag-text {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  margin-bottom: -5px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag .tag-icon {
  padding-right: 5px;
  cursor: pointer;
  position: absolute;
  right: 0;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag .tag-icon:hover i {
  color: #0099FF;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag .vd-ui-select-tag .tag-icon i {
  font-size: 14px;
  position: relative;
  top: 1px;
  color: #999999;
}
.vd-ui-select-multiple-wrapper .vd-ui-input-multiple {
  flex: 1;
  border: none;
  min-width: 20%;
  padding-left: 5px;
  height: 21px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag-auto {
  margin-top: -4px;
  max-width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag-auto .vd-ui-select-tag {
  margin-top: 4px;
}
.vd-ui-select-multiple-wrapper .vd-ui-tag-auto .vd-ui-input-multiple {
  margin-top: 2px;
  font-size: 14px;
}
.vd-ui-select-multiple-wrapper .vd-ui-readonly {
  max-width: 100%;
}
.vd-ui-select-multiple-wrapper .mul-icon {
  font-size: 16px;
  position: absolute;
  right: 10px;
  transition: 0.19s;
}
.vd-ui-select-multiple-wrapper .mul-icon.icon-error2 {
  color: #666666;
}
.vd-ui-select-multiple-wrapper .mul-icon.rotate {
  transform: rotate(180deg);
  transition: 0.22s;
}
.vd-ui-select-multiple-wrapper__error {
  border-color: #E64545 !important;
}
.vd-ui-select-multiple-wrapper__disabled {
  background-color: #F5F7FA;
  border-color: #D7DADE;
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-select-multiple-wrapper__disabled .vd-ui-tag .vd-ui-select-tag {
  padding-right: 5px;
}
.vd-ui-select-multiple-wrapper__disabled:hover {
  border-color: #D7DADE;
}
.vd-ui-select-multiple-wrapper__disabled.is-focus {
  border-color: #D7DADE;
}
.vd-ui-select-multiple-wrapper__disabled.is-focus:hover {
  border-color: #D7DADE;
}
.vd-ui-select-multiple-wrapper-large {
  min-height: 42px;
}
.vd-ui-select-list {
  width: 100%;
  box-sizing: border-box;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  z-index: 15;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
  overscroll-behavior: contain;
}
.vd-ui-select-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-select-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-select-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-select-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-select-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-select-list.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-select-list.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-select-list .loading-li {
  height: 29px;
  line-height: 29px;
  padding: 0px 10px;
}
.vd-ui-select-list .loading-li i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 2px;
  font-size: 16px;
  margin-right: 5px;
  color: #0099FF;
}
.vd-ui-select-list .failed-li {
  height: 29px;
  line-height: 29px;
  padding: 0px 10px;
}
.vd-ui-select-list .failed-li i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-select-list .failed-li .reload {
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-select-list .empty-li {
  height: 29px;
  line-height: 29px;
  padding: 0px 10px;
  text-align: center;
  color: #999999;
}
.vd-ui-select .vd-ui-input-error {
  color: #E64545;
  margin-top: 5px;
  display: flex;
  align-items: center;
}
.vd-ui-select .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
}
.vd-ui-select .vd-ui-input-error .vd-ui-input-error--errmsg {
  margin: 0px;
}
@keyframes appear {
  0% {
    opacity: 0;
    -webkit-transform: scale(1, 0);
  }
  100% {
    -webkit-transform: scale(1, 1);
    opacity: 1;
  }
}
@keyframes tagAppear {
  0% {
    opacity: 0;
    -webkit-transform: scale(1, 0);
  }
  100% {
    -webkit-transform: scale(1, 1);
    opacity: 1;
  }
}
.slide-enter {
  opacity: 1;
  transform: scale(1, 1);
}
.slide-leave-to {
  opacity: 0;
  -webkit-transform: scale(1, 0);
}
.vd-ui-group {
  margin-bottom: 5px;
}
.vd-ui-group:last-child {
  margin-bottom: 0;
}
.vd-ui-group .vd-ui-group-title {
  padding: 0px 10px;
  height: 33px;
  color: #999999;
}
.vd-ui-group .vd-ui-group-title p {
  line-height: 33px;
}
.ui-select-option-li {
  height: 33px;
  width: 100%;
  cursor: pointer;
  position: relative;
}
.ui-select-option-li .icon-checkbox1,
.ui-select-option-li .icon-checkbox2 {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #969B9E;
}
.ui-select-option-li .icon-checkbox2 {
  color: #0099FF;
}
.ui-select-option-li .li-p {
  padding: 0px 10px;
  display: flex;
}
.ui-select-option-li .li-p .before {
  width: 50%;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}
.ui-select-option-li .li-p .after {
  padding-left: 10px;
  width: 50%;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}
.ui-select-option-li .li-p-made {
  display: flex;
  align-items: center;
}
.ui-select-option-li .li-p-made .li-avatar {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
  position: relative;
}
.ui-select-option-li .li-p-made .li-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.ui-select-option-li .li-p-made .li-avatar img.error {
  width: 0;
  height: 0;
}
.ui-select-option-li .li-p-made .li-avatar img.error:before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  left: 0;
  top: 0;
  background-image: url(../image/crm-user-avatar.svg);
  background-size: 100% 100%;
}
.ui-select-option-li .li-p-made .li-label {
  flex: 1;
}
.ui-select-option-li .li-p-made .li-label .strong {
  color: #f60;
}
.ui-select-option-li .li-p-made .li-name {
  text-align: right;
  width: 50%;
  margin-left: 10px;
  color: #999;
}
.ui-select-option-li.selected .li-p .li-label {
  color: #0099FF;
}
.ui-select-option-li.multiple .li-p {
  padding-left: 36px;
}
.ui-select-option-li p {
  line-height: 33px;
  margin: 0;
  font-size: 14px;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}
.ui-select-option-li:hover {
  background-color: #F5F7FA;
}
.ui-select-option-li:hover .icon-checkbox1 {
  color: #0099FF;
}
.ui-select-option-li.disabled:hover {
  background-color: #ffffff;
}
.ui-select-option-li.disabled:hover .icon-checkbox1 {
  color: #D7DADE;
}
.ui-select-option-li.disabled:hover .icon-checkbox2 {
  color: #D7DADE;
}
.ui-select-option-li.disabled .icon-checkbox1 {
  color: #D7DADE;
}
.ui-select-option-li.disabled .icon-checkbox2 {
  color: #D7DADE;
}
.ui-select-option-li.disabled .li-p {
  cursor: not-allowed;
}
.ui-select-option-li.disabled .li-p p {
  color: #999999;
}
.ui-select-option-li:active {
  background-color: #EBEFF2;
}
.vd-ui-custom-select-drop {
  background: #fff;
  padding: 10px 0;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  z-index: 11;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-search {
  padding: 0 10px;
  margin-bottom: 10px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-search .vd-ui-input .vd-ui-input__inner {
  border-radius: 0;
  border-top: 0;
  border-left: 0;
  border-right: 0;
}
.vd-ui-custom-select-drop .vd-ui-custom-loading-wrap {
  display: flex;
  color: #666;
  padding: 6px 10px;
  align-items: center;
}
.vd-ui-custom-select-drop .vd-ui-custom-loading-wrap .icon-loading {
  font-size: 16px;
  line-height: 1;
  color: #09f;
  margin-right: 5px;
  animation: loading 2s linear infinite;
}
.vd-ui-custom-select-drop .vd-ui-custom-empty {
  color: #999;
  text-align: center;
  padding: 6px 10px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list {
  max-height: 330px;
  overflow: auto;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 33px;
  padding: 0 10px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox {
  font-size: 16px;
  margin-right: 5px;
  display: flex;
  align-items: center;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox .vd-ui_icon {
  line-height: 1;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox .vd-ui_icon.icon-checkbox1 {
  color: #969B9E;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox .vd-ui_icon.icon-checkbox2 {
  color: #09f;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-checkbox + .item-avatar {
  margin-left: 5px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-avatar {
  width: 18px;
  height: 18px;
  border-radius: 2px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-label {
  flex: 1;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item .item-label .strong {
  color: #f60;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item:hover {
  background: #f5f7fa;
}
.vd-ui-custom-select-drop .vd-ui-custom-select-item:hover .item-checkbox .vd-ui_icon.icon-checkbox1 {
  color: #09f;
}
.vd-ui-custom-select-drop .vd-ui-custom-footer {
  margin-top: 5px;
  padding: 10px 10px 0 10px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #E1E5E8;
}
.vd-ui-custom-select-drop .vd-ui-custom-footer .vd-ui-button {
  margin-left: 10px;
}

.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item {
  position: relative;
  margin-bottom: 10px;
  padding-right: 40px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item:last-child {
  margin-bottom: 0;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .del {
  width: 30px;
  height: 33px;
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .del:hover {
  color: #f60;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel {
  position: absolute;
  top: 33px;
  left: 0;
  z-index: 10;
  width: 100%;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner {
  background: #fff;
  border: solid 1px #ddd;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list {
  padding: 5px 10px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content {
  max-height: 352px;
  overflow-y: auto;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content .label {
  color: #999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content .search-panel-item {
  padding: 5px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content .search-panel-item .num {
  color: #999;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-panel-list .search-list-content .search-panel-item:hover {
  color: #f60;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-loading {
  padding: 10px;
  display: flex;
  align-items: center;
}
@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .search-loading > .icon-loading {
  font-size: 16px;
  color: #09f;
  animation: loading 2s linear infinite;
  z-index: 99;
  margin-right: 5px;
}
.vd-ui-search-category .vd-ui-search-category-form .vd-ui-search-category-item .search-panel .search-panel-inner .null-data {
  text-align: center;
  padding: 10px;
}
.vd-ui-search-category .vd-ui-search-category-btn {
  margin-top: 10px;
}
.vd-ui-search-category .vd-ui-search-category-btn .btn {
  color: #09f;
  transition: color 0.12s ease-in;
  cursor: pointer;
}
.vd-ui-search-category .vd-ui-search-category-btn .btn:hover {
  color: #f60;
}

.vd-ui-select-related {
  position: relative;
  display: flex;
}
.vd-ui-select-related > .vd-ui-search-related {
  border-radius: 3px;
  background: #fff;
  border: solid 1px #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 33px;
  z-index: 9;
  max-height: 370px;
  overflow-y: auto;
}
.vd-ui-select-related > .vd-ui-search-related::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-select-related > .vd-ui-search-related::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-select-related > .vd-ui-search-related::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-select-related > .vd-ui-search-related::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-select-related > .vd-ui-search-related::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-select-related > .vd-ui-search-related .loading {
  color: #666;
  height: 39px;
  line-height: 37px;
  padding: 0px 10px;
  overflow: hidden;
}
.vd-ui-select-related > .vd-ui-search-related .loading i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 1px;
  font-size: 16px;
  margin-right: 5px;
  color: #09F;
}
.vd-ui-select-related > .vd-ui-search-related .failed-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
}
.vd-ui-select-related > .vd-ui-search-related .failed-li i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-select-related > .vd-ui-search-related .failed-li .reload {
  color: #09f;
  cursor: pointer;
}
.vd-ui-select-related > .vd-ui-search-related .empty-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
  text-align: center;
  color: #999;
}
.vd-ui-select-related > .vd-ui-search-related .search-list {
  padding: 5px 10px;
}
.vd-ui-select-related > .vd-ui-search-related .search-list .local-data {
  font-size: 12px;
  color: #999;
  line-height: 30px;
}
.vd-ui-select-related > .vd-ui-search-related .search-list .sr-item {
  padding: 6px 0;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
.vd-ui-select-related > .vd-ui-search-related .search-list .sr-item .sr-item-left {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
}
.vd-ui-select-related > .vd-ui-search-related .search-list .sr-item .sr-item-right {
  width: 120px;
  flex-shrink: 0;
  text-align: right;
  color: #999;
}
.vd-ui-select-related > .vd-ui-search-related .search-list .sr-item:hover .sr-item-left {
  color: #f60;
}
.vd-ui-select-related > .vd-ui-search-related .search-list .sr-item:hover .sr-item-right {
  color: #f60;
}
.vd-ui-select-related > .vd-ui-search-related .search-list .sr-item.disabled {
  color: #999;
  pointer-events: none;
}

.vd-ui-tab {
  position: relative;
}
.vd-ui-tab .vd-ui-tab-list {
  display: flex;
  border-bottom: 1px solid #EBEFF2;
}
.vd-ui-tab .vd-ui-tab-list .vd-ui-tab-list-more {
  flex-wrap: wrap;
  max-height: 51px;
}
.vd-ui-tab .vd-ui-tab-item {
  margin: 0 20px;
  color: #333;
  cursor: pointer;
  white-space: nowrap;
  transition: color 0.1s ease;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner {
  display: flex;
  align-items: center;
  position: relative;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-item-txt {
  padding: 15px 0;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-item-txt:hover {
  color: #0099FF;
  transition: color 0.1s ease;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-wrap {
  position: relative;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-option {
  position: absolute;
  border: solid 1px #C9CED1;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  padding: 5px 0;
  background: #fff;
  top: 21px;
  z-index: 11;
  left: 0;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-option .vd-ui-tab-custom-option-item {
  padding: 6px 10px;
  font-weight: normal;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-option .vd-ui-tab-custom-option-item.red {
  color: #e64545;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-inner .vd-ui-tab-custom-option .vd-ui-tab-custom-option-item:hover {
  color: #f60;
  background: #F5F7FA;
}
.vd-ui-tab .vd-ui-tab-item .icon-app-more {
  display: inline-block;
  transform: rotate(90deg);
  width: 21px;
  height: 21px;
  text-align: center;
}
.vd-ui-tab .vd-ui-tab-item .icon-app-more:hover {
  color: #f60;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-more {
  position: relative;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-more .vd-ui-tab-item-more-active {
  color: #0099FF;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-more .icon-down {
  font-size: 16px;
  vertical-align: -2px;
  transition: transform 0.2s ease;
  display: inline-block;
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-more .icon-down.hover {
  transform: rotate(180deg);
}
.vd-ui-tab .vd-ui-tab-item .vd-ui-tab-item-line {
  border: solid 1px #EBEFF2;
  margin: 0;
  padding: 11px 20px;
  margin-right: -1px;
  margin-bottom: -1px;
  position: relative;
  z-index: 1;
}
.vd-ui-tab .vd-ui-tab-item.vd-ui-tab-item-active {
  font-weight: bold;
  color: #0099FF;
  border-bottom-color: #fff;
}
.vd-ui-tab .vd-ui-tab-more .vd-ui-tab-more-list {
  position: absolute;
  background: #fff;
  padding: 5px 0;
  border: solid 1px #C9CED1;
  border-radius: 3px;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
  color: #333;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 11;
}
.vd-ui-tab .vd-ui-tab-more .vd-ui-tab-more-item {
  padding: 6px 10px;
}
.vd-ui-tab .vd-ui-tab-more .vd-ui-tab-more-item:hover {
  background: #F5F7FA;
  color: #333;
}
.vd-ui-tab .vd-ui-tab-bar {
  background: #0099FF;
  height: 3px;
  position: absolute;
  bottom: 0;
  transition: all 0.22s ease-out;
}

.vd-ui-table-wrap {
  width: 100%;
}
.vd-ui-table-wrap .vd-ui-wrap-is-left .vd-ui-th,
.vd-ui-table-wrap .vd-ui-wrap-is-left .vd-ui-td {
  transition: box-shadow 0.3s;
}
.vd-ui-table-wrap .vd-ui-wrap-is-left .vd-ui-th.sticky-item,
.vd-ui-table-wrap .vd-ui-wrap-is-left .vd-ui-td.sticky-item {
  position: sticky;
  left: 0;
  z-index: 3;
}
.vd-ui-table-wrap .vd-ui-wrap-is-left .vd-ui-th.sticky-item:hover,
.vd-ui-table-wrap .vd-ui-wrap-is-left .vd-ui-td.sticky-item:hover {
  z-index: 4;
}
.vd-ui-table-wrap .vd-ui-wrap-is-right .vd-ui-th,
.vd-ui-table-wrap .vd-ui-wrap-is-right .vd-ui-td {
  transition: box-shadow 0.3s;
}
.vd-ui-table-wrap .vd-ui-wrap-is-right .vd-ui-th:last-child,
.vd-ui-table-wrap .vd-ui-wrap-is-right .vd-ui-td:last-child {
  position: sticky;
  right: 0;
  z-index: 3;
}
.vd-ui-table-wrap .vd-ui-wrap-is-right .vd-ui-td:last-child:hover {
  z-index: 4;
}
.vd-ui-table-wrap .vd-ui-table-setting {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 10px;
  border-left: 1px solid #E1E5E8;
  cursor: pointer;
  font-size: 16px;
  background: #F5F7FA;
}
.vd-ui-table-wrap .vd-ui-table-setting:hover {
  background: #EBEFF2;
}
.vd-ui-table-wrap .vd-ui-table-setting .vd-ui-table-setting-icon {
  transform: rotate(90deg);
}
.vd-ui-table-wrap .vd-ui-table-setting.vd-ui-table-setting-right {
  right: -6px;
  z-index: 11;
}
.vd-ui-table-wrap .vd-ui-table-th-cnt {
  display: flex;
  align-items: center;
  margin: -8px -20px;
}
.vd-ui-table-wrap .vd-ui-table-th-cnt .vd-ui-table-th-inner {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 9px 0 8px 20px;
  cursor: pointer;
}
.vd-ui-table-wrap .vd-ui-table-th-cnt .vd-ui-table-th-inner:hover {
  background: #EBEFF2;
}
.vd-ui-table-wrap .vd-ui-table-th-cnt .vd-ui-table-th-inner .vd-ui-table-th-txt {
  flex: 1;
}
.vd-ui-table-wrap .vd-ui-table-th-cnt .vd-ui-table-filter {
  cursor: pointer;
  color: #BABFC2;
}
.vd-ui-table-wrap .vd-ui-table-th-cnt .vd-ui-table-filter .icon-filter {
  width: 34px;
  font-size: 16px;
  height: 37px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vd-ui-table-wrap .vd-ui-table-th-cnt .vd-ui-table-filter:hover,
.vd-ui-table-wrap .vd-ui-table-th-cnt .vd-ui-table-filter.active {
  background: #EBEFF2;
}
.vd-ui-table-wrap .vd-ui-table-th-cnt .vd-ui-table-filter.selected {
  color: #FF6600;
}
.vd-ui-table-wrap .ui-th-tip-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.vd-ui-table-wrap .vd-ui-table-sort {
  padding: 0 10px 0 10px;
}
.vd-ui-table-wrap .vd-ui-table-sort .vd-ui-sort-icon {
  display: block;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #D7DADE;
}
.vd-ui-table-wrap .vd-ui-table-sort .vd-ui-sort-icon.vd-ui-sort-icon-down {
  transform: rotate(180deg);
  margin-top: 2px;
}
.vd-ui-table-wrap .vd-ui-table-sort .vd-ui-sort-icon.vd-ui-sort-icon-active {
  border-bottom-color: #FF6600;
}
.vd-ui-table-wrap .vd-ui-table-header {
  overflow: hidden;
  position: relative;
}
.vd-ui-table-wrap .vd-ui-table-header.header-fixed {
  position: fixed;
  top: 0;
  z-index: 11;
}
.vd-ui-table-wrap .vd-ui-table-header.vd-ui-wrap-right-scroll-fixed .vd-ui-th:nth-last-child(2) {
  position: sticky;
  border-right-color: #F5F7FA;
}
.vd-ui-table-wrap .vd-ui-table-header.vd-ui-wrap-right-scroll-fixed .vd-ui-th:last-child {
  border-left-color: #F5F7FA;
}
.vd-ui-table-wrap .vd-ui-table-header.vd-ui-wrap-right-scroll-fixed .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:nth-last-child(2) {
  border-left: 0;
}
.vd-ui-table-wrap .vd-ui-table-header.vd-ui-wrap-right-scroll-fixed .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:nth-last-child(2):after {
  position: absolute;
  top: -1px;
  right: -1px;
  width: calc(100% - 1px);
  height: 100%;
  content: "";
  pointer-events: none;
}
.vd-ui-table-wrap .vd-ui-table-header.vd-ui-wrap-right-scroll-fixed .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:nth-last-child(2):before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: -1px;
  width: 100%;
  transform: translate(-100%);
  transition: box-shadow 0.3s;
  content: "";
  pointer-events: none;
  box-shadow: inset -10px 0 8px -8px #00000026;
}
.vd-ui-table-wrap .vd-ui-table-header.vd-ui-wrap-right-scroll-fixed .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:last-child {
  border-left: 1px solid #F5F7FA;
}
.vd-ui-table-wrap .vd-ui-table-header.vd-ui-wrap-right-scroll-fixed .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:last-child:after {
  background: #F5F7FA;
  left: -2px;
  top: 1px;
  height: calc(100% - 2px);
}
.vd-ui-table-wrap .vd-ui-table-header.vd-ui-wrap-right-scroll-fixed .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:last-child:before {
  display: none;
}
.vd-ui-table-wrap .vd-ui-table-body {
  overflow: auto;
  position: relative;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-table-wrap .vd-ui-table-body.vd-ui-wrap-scroll {
  overflow-y: scroll;
}
.vd-ui-table-wrap .vd-ui-table-container {
  width: 100%;
  overflow: auto;
}
.vd-ui-table-wrap .vd-ui-table-container.vd-table-width-border {
  border-left: 1px solid #E1E5E8;
  border-right: 1px solid #E1E5E8;
}
.vd-ui-table-wrap .vd-ui-table-container.vd-table-width-border .ui-table-list-empty {
  border-bottom: 1px solid #E1E5E8;
}
.vd-ui-table-wrap .vd-ui-table-container .ui-table-list-empty {
  padding: 100px 0;
  text-align: center;
}
.vd-ui-table-wrap .vd-ui-table-container .ui-table-list-empty .empty-img {
  width: 110px;
  height: 95px;
  background-image: url(../../images/list-empty.svg);
  background-size: 100% 100%;
  margin: 0 auto 20px auto;
}
.vd-ui-table-wrap .vd-ui-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-align: left;
}
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-left-fixed .vd-ui-th.sticky-item:after,
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-left-fixed .vd-ui-td.sticky-item:after {
  position: absolute;
  top: 0;
  left: -1px;
  width: 1px;
  height: 100%;
  content: "";
  background: #E1E5E8;
  pointer-events: none;
}
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-left-fixed .vd-ui-th.last-fixed:after,
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-left-fixed .vd-ui-td.last-fixed:after {
  position: absolute;
  top: 0;
  left: -1px;
  width: 1px;
  height: 100%;
  content: "";
  background: #E1E5E8;
  pointer-events: none;
}
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-left-fixed .vd-ui-th.last-fixed:before,
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-left-fixed .vd-ui-td.last-fixed:before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: -1px;
  width: 100%;
  transform: translate(100%);
  transition: box-shadow 0.3s;
  content: "";
  pointer-events: none;
  box-shadow: inset 10px 0 8px -8px #00000026;
}
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-left-fixed .vd-ui-th:first-child {
  border-right: 0;
}
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-left-fixed .vd-ui-th:nth-child(2) {
  border-left: 0;
}
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:last-child:after,
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-right-fixed .vd-ui-td:last-child:after {
  position: absolute;
  top: 0;
  right: -1px;
  width: 1px;
  height: 100%;
  content: "";
  pointer-events: none;
  background: #e1e5e8;
}
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:last-child:before,
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-right-fixed .vd-ui-td:last-child:before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: -1px;
  width: 100%;
  transform: translate(-100%);
  transition: box-shadow 0.3s;
  content: "";
  pointer-events: none;
  box-shadow: inset -10px 0 8px -8px #00000026;
}
.vd-ui-table-wrap .vd-ui-table.vd-ui-table-right-fixed .vd-ui-th:last-child {
  border-left: 0;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th {
  padding: 8px 20px;
  background: #F5F7FA;
  font-weight: normal;
  border: solid 1px #E1E5E8;
  color: #999999;
  position: relative;
  z-index: 2;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th.with-info {
  position: static;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th.align-right {
  text-align: right;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th:first-child {
  border-left: 0;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th:last-child {
  border-right: 0;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th.vd-ui-th-bar {
  border-left-color: #F5F7FA;
  z-index: 1;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th.vd-ui-th-bar-prev {
  border-right-color: #F5F7FA;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td {
  padding: 14px 20px;
  border-bottom: solid 1px #EBEFF2;
  background: #ffffff;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td.align-right {
  text-align: right;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td.vertical-center {
  vertical-align: middle;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .option-wrap {
  display: flex;
  align-items: center;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .td-left-tip {
  position: absolute;
  top: 10px;
  left: 10px;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit {
  color: #09f;
  cursor: pointer;
  margin-right: 20px;
  position: relative;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit::before {
  content: '';
  width: 1px;
  height: 14px;
  background: #e1e5e8;
  position: absolute;
  right: -10px;
  top: 4px;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit:last-child {
  margin-right: 0;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more {
  margin-right: 0;
  position: relative;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more:hover .table-edit-more-drop {
  z-index: 1;
  opacity: 1;
  transition: all .3s ease .3s;
  pointer-events: all;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more .table-edit-more-drop {
  position: absolute;
  background: #fff;
  width: 119px;
  border: 1px solid #BABFC2;
  border-radius: 3px;
  padding: 5px 0;
  opacity: 0;
  z-index: -1;
  right: 0;
  pointer-events: none;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-tr:nth-last-child(1) .vd-ui-td .table-edit-more .table-edit-more-drop {
  bottom: 21px;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-tr:nth-last-child(2) .vd-ui-td .table-edit-more .table-edit-more-drop {
  bottom: 21px;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-tr:nth-last-child(3) .vd-ui-td .table-edit-more .table-edit-more-drop {
  bottom: 21px;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more .table-edit-more-btn {
  padding: 5px 10px;
  cursor: pointer;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more .table-edit-more-btn:hover {
  background: #f5f7fa;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more-label {
  display: flex;
  align-items: center;
  color: #09f;
  cursor: pointer;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more:hover .icon-down {
  transform: rotate(180deg);
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more-label .edit-more-txt {
  margin-right: 5px;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more-label .icon-down {
  margin-right: 5px;
  font-size: 16px;
  transition: transform .22s ease;
  line-height: 1;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit-more:hover .table-edit-more-label {
  color: #f60;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit:last-child::before {
  display: none;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit:hover {
  color: #f60;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td .table-edit.disabled {
  color: #999;
  cursor: not-allowed;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td.can-edit {
  padding-right: 45px;
  position: relative;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td.can-edit .ui-table-td-edit-wrap {
  position: absolute;
  right: 0;
  top: 0;
  width: 36px;
  height: 49px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td.can-edit .ui-table-td-edit-wrap .vd-ui_icon {
  pointer-events: none;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td.can-edit .ui-table-td-edit-wrap:hover {
  color: #f60;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td.can-edit .ui-table-td-edit-wrap.disabled {
  cursor: not-allowed;
  color: #BABFC2;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td.can-edit .ui-table-td-edit-wrap.disabled:hover {
  color: #BABFC2;
}
.vd-ui-table-wrap .vd-ui-table tr:hover .vd-ui-td {
  background: #FAFBFC;
}
.vd-ui-table-wrap .vd-ui-table tr.on-select .vd-ui-td {
  background: #F0F9FF;
}
.vd-ui-table-wrap .vd-ui-table tr.on-select:hover .vd-ui-td {
  background: #F0F9FF;
}
.vd-ui-table-wrap .vd-ui-checkbox-wrap {
  height: 21px;
  display: flex;
  align-items: center;
}
.vd-ui-table-wrap .vd-ui-checkbox-wrap .vd-ui-checkbox-item .vd-ui-checkbox-icon {
  margin: 0;
}
.vd-ui-table-wrap .ui-table-footer-scroll {
  overflow: auto;
  position: fixed;
  z-index: 80;
  bottom: 10px;
}
.vd-ui-table-wrap .ui-table-footer-scroll::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
.vd-ui-table-wrap .ui-table-footer-scroll::-webkit-scrollbar-track {
  background: transparent;
  width: 10px;
  height: 10px;
}
.vd-ui-table-wrap .ui-table-footer-scroll::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 10px;
  height: 10px;
  border-radius: 5px;
}
.vd-ui-table-wrap .ui-table-footer-scroll::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-table-wrap .ui-table-footer-scroll::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-table-wrap .ui-table-footer-scroll .ui-table-footer-scroll-inner {
  height: 1px;
}
.vd-ui-table-wrap .ui-table-footer-scroll.hidden {
  opacity: 0;
  z-index: -1;
}
.vd-ui-table-wrap .td-link {
  color: #09f;
  cursor: pointer;
}
.vd-ui-table-wrap .td-link:hover {
  color: #f60;
}
.vd-ui-table-wrap .avatar-item-wrap {
  display: flex;
  align-items: center;
}
.vd-ui-table-wrap .avatar-item-wrap .avatar-img {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 5px;
  position: relative;
}
.vd-ui-table-wrap .avatar-item-wrap .avatar-img img {
  max-width: 100%;
  max-height: 100%;
}
.vd-ui-table-wrap .avatar-item-wrap .avatar-img img.error {
  width: 0;
  height: 0;
}
.vd-ui-table-wrap .avatar-item-wrap .avatar-img img.error:before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  left: 0;
  top: 0;
  background-image: url(../image/crm-user-avatar.svg);
  background-size: 100% 100%;
}
.vd-ui-table-wrap .tel-item-wrap {
  display: flex;
  align-items: center;
  color: #09f;
  cursor: pointer;
}
.vd-ui-table-wrap .tel-item-wrap .icon-call {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
}
.vd-ui-table-wrap .tel-item-wrap:hover {
  color: #f60;
}
.vd-ui-table-wrap .tel-item-wrap.normal {
  cursor: default;
  color: #333;
}
.vd-ui-table-wrap .tel-item-wrap.normal .icon-call {
  color: #999;
}
.vd-ui-table-wrap .tel-item-wrap.normal:hover {
  color: #333;
}
.ui-table-filter-drop {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 111;
  background: #fff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  width: 300px;
}
.ui-table-filter-drop .ui-table-filter-item:hover {
  background: #F5F7FA;
}
.ui-table-filter-drop .ui-table-filter-item .vd-ui-checkbox-item {
  padding: 0 10px;
  display: flex;
  align-items: center;
  height: 33px;
  cursor: pointer;
}
.ui-table-filter-drop .ui-table-filter-item .vd-ui-checkbox-item .vd-ui-checkbox-icon {
  margin-right: 10px;
}
.ui-table-filter-drop .ui-table-filter-drop-list {
  padding-bottom: 10px;
}
.ui-table-filter-drop .ui-table-filter-drop-list .ui-table-filter-drop-inner {
  max-height: 330px;
  overflow: auto;
  overscroll-behavior: contain;
}
.ui-table-filter-drop .ui-table-filter-drop-list .ui-table-filter-drop-inner::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.ui-table-filter-drop .ui-table-filter-drop-list .ui-table-filter-drop-inner::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.ui-table-filter-drop .ui-table-filter-drop-list .ui-table-filter-drop-inner::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.ui-table-filter-drop .ui-table-filter-drop-list .ui-table-filter-drop-inner::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.ui-table-filter-drop .ui-table-filter-drop-list .ui-table-filter-drop-inner::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.ui-table-filter-drop .ui-table-filter-drop-search {
  padding: 10px;
}
.ui-table-filter-drop .ui-table-filter-drop-search .vd-ui-input .vd-ui-input__inner {
  border-top: 0;
  border-left: 0;
  border-right: 0;
  border-radius: 0;
}
.ui-table-filter-drop .ui-table-filter-drop-footer {
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px #EBEFF2;
}
.ui-table-filter-drop .ui-table-filter-drop-footer .vd-ui-button {
  margin-left: 10px;
}
.ui-table-filter-drop .ui-table-filter-drop-empty {
  padding: 10px 0 20px 0;
  text-align: center;
  color: #999;
}

.vd-ui-tip-wrap {
  position: relative;
  display: flex;
  overflow: hidden;
}
.vd-ui-tip-wrap .vd-ui_icon {
  font-size: 16px;
  line-height: 1;
}
.vd-ui-tip-wrap.warn {
  color: #f60;
}
.vd-ui-tip-wrap.info {
  color: #09f;
}
.vd-ui-tip-wrap .icon-info2 {
  color: #09f;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt {
  position: absolute;
  z-index: -1;
  opacity: -1;
  pointer-events: none;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt .vd-ui-tip-inner {
  padding: 10px 15px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  border-radius: 3px;
  color: #333;
  position: relative;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt .vd-ui-tip-inner.small {
  font-size: 12px;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt .vd-ui-tip-inner:before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt.nowrap {
  white-space: nowrap;
}
.vd-ui-tip-wrap.tl .vd-ui-tip-cnt {
  bottom: 16px;
  padding-bottom: 10px;
  right: -12px;
}
.vd-ui-tip-wrap.tl .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  bottom: -12px;
  right: 14px;
  border-top-color: #fff;
}
.vd-ui-tip-wrap.r .vd-ui-tip-cnt {
  top: 50%;
  transform: translateY(-50%);
  padding-left: 10px;
  left: 16px;
}
.vd-ui-tip-wrap.r .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #fff;
}
.vd-ui-tip-wrap.rt .vd-ui-tip-cnt {
  top: -14px;
  padding-left: 10px;
  left: 16px;
}
.vd-ui-tip-wrap.rt .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  left: -12px;
  top: 14px;
  border-right-color: #fff;
}
.vd-ui-tip-wrap:hover .vd-ui-tip-cnt {
  pointer-events: all;
  z-index: 1;
  opacity: 1;
}
.vd-ui-title-tip-wrap {
  position: relative;
}
.vd-ui-title-tip-cnt {
  background: #333;
  color: #fff;
  border-radius: 3px;
  padding: 8px 15px;
  white-space: nowrap;
  transform: translateX(-50%);
  opacity: 0;
  z-index: -1;
  pointer-events: none;
  position: fixed;
}
.vd-ui-title-tip-cnt.show {
  z-index: 9999;
  opacity: 1;
}
.vd-ui-title-tip-cnt::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-top-color: #333;
  bottom: -10px;
  left: calc(50% - 5px);
}

.vd-ui-tianyancha {
  position: relative;
  display: flex;
}
.vd-ui-tianyancha .vd-ui-tyc-input {
  width: 300px;
}
.vd-ui-tianyancha .vd-ui-tyc-right {
  height: 33px;
  display: flex;
  align-items: center;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-search-icon {
  display: flex;
  align-items: center;
  margin-left: 10px;
  cursor: pointer;
  color: #09F;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-search-icon .icon-search {
  font-size: 16px;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-search-icon > span {
  margin-left: 5px;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-search-icon:hover {
  color: #f60;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon {
  position: relative;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .icon-caution1 {
  font-size: 16px;
  color: #F60;
  margin-left: 10px;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .icon-caution1:hover + .vd-ui-tyc-icon-tip {
  display: block;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .vd-ui-tyc-icon-tip {
  display: none;
  position: absolute;
  transition: display 0.3s ease;
  top: -50px;
  left: -3px;
  max-width: 600px;
  width: 100vw;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .vd-ui-tyc-icon-tip .tip-txt {
  position: relative;
  max-width: 600px;
  max-width: max-content;
  max-height: 110px;
  padding: 10px 15px;
  background-color: #fff;
  font-size: 12px;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  box-sizing: border-box;
}
.vd-ui-tianyancha .vd-ui-tyc-right .vd-ui-tyc-warn-icon .vd-ui-tyc-icon-tip .tip-txt::after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  border-top: 5px solid #fff;
  border-bottom: 5px solid transparent;
  position: absolute;
  top: 38px;
  left: 16px;
}
.vd-ui-tianyancha .vd-ui-tyc-right .icon-tyc {
  font-size: 16px;
  width: 16px;
  height: 16px;
  display: inline-block;
  background: url(../image/tyc.png) no-repeat;
  background-size: 16px;
  margin-left: 10px;
  cursor: pointer;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related {
  max-height: 370px;
  overflow-y: auto;
  border-radius: 3px;
  background: #fff;
  border: solid 1px #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 33px;
  z-index: 9;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .loading {
  color: #666;
  height: 39px;
  line-height: 37px;
  padding: 0px 10px;
  overflow: hidden;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .loading i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 1px;
  font-size: 16px;
  margin-right: 5px;
  color: #09F;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .failed-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .failed-li i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .failed-li .reload {
  color: #09f;
  cursor: pointer;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .empty-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
  text-align: center;
  color: #999;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list {
  padding: 5px 10px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .local-data {
  font-size: 12px;
  color: #999;
  line-height: 30px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item {
  padding: 6px 0;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item .sr-item-left {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
  display: flex;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item .sr-item-left .icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  margin-right: 5px;
  margin-top: 3px;
  background: url('../image/tyc.png') no-repeat;
  background-size: 16px;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item .sr-item-left > p {
  flex: 1;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item .sr-item-right {
  width: 150px;
  text-align: right;
  color: #999;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item:hover .sr-item-left {
  color: #f60;
}
.vd-ui-tianyancha .vd-ui-tyc-search-related .search-related-list .sr-item.disabled {
  color: #999;
  pointer-events: none;
}
.vd-ui-table-wrap {
  width: 100%;
}
.vd-ui-table-wrap.with-border {
  border-left: 1px solid #E1E5E8;
  border-right: 1px solid #E1E5E8;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-th,
.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-td {
  transition: box-shadow 0.3s;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-th.sticky-left,
.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-td.sticky-left {
  position: sticky;
  left: 0;
  z-index: 3;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-th.sticky-right,
.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-td.sticky-right {
  position: sticky;
  right: 0;
  z-index: 3;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll .vd-ui-th.sticky-left:before {
  content: "";
  width: 1px;
  height: 100%;
  background: #E1E5E8;
  right: -1px;
  position: absolute;
  top: 0;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-th.sticky-left-last:after,
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-td.sticky-left-last:after {
  position: absolute;
  top: -1px;
  left: -1px;
  width: 100%;
  height: 100%;
  content: "";
  pointer-events: none;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-th.sticky-left-last:before,
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-td.sticky-left-last:before {
  background: transparent;
  position: absolute;
  top: 0;
  right: 0;
  bottom: -1px;
  width: 100%;
  transform: translate(100%);
  transition: box-shadow 0.3s;
  content: "";
  pointer-events: none;
  box-shadow: inset 10px 0 8px -8px #00000026;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-th.sticky-left-last {
  border-right: 0;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-left-fixed .vd-ui-th.sticky-left-last + td {
  border-left: 0;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-th.sticky-right-first:after,
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-td.sticky-right-first:after {
  position: absolute;
  top: -1px;
  right: -1px;
  width: calc(100% - 1px);
  height: 100%;
  content: "";
  pointer-events: none;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-th.sticky-right-first:before,
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-td.sticky-right-first:before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: -1px;
  width: 100%;
  transform: translate(-100%);
  transition: box-shadow 0.3s;
  content: "";
  pointer-events: none;
  box-shadow: inset -10px 0 8px -8px #00000026;
}
.vd-ui-table-wrap .vd-ui-wrap-widthscroll.is-right-fixed .vd-ui-th.sticky-right-first {
  border-left: 0;
}
.vd-ui-table-wrap .vd-ui-table-body {
  overflow: auto;
  position: relative;
}
.vd-ui-table-wrap .vd-ui-table-body.vd-ui-wrap-scroll {
  overflow-y: scroll;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-table-wrap .vd-ui-table-body::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-table-wrap .vd-ui-table-container {
  width: 100%;
  overflow: auto;
}
.vd-ui-table-wrap .vd-ui-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-align: left;
}
.vd-ui-table-wrap .vd-ui-table .td-text {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.vd-ui-table-wrap .vd-ui-table .td-text2 {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.vd-ui-table-wrap .vd-ui-table .td-text .td-link {
  color: #09f;
  cursor: pointer;
}
.vd-ui-table-wrap .vd-ui-table .td-text .td-link:hover {
  color: #f60;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th {
  padding: 8px 20px;
  background: #FAFBFC;
  font-weight: normal;
  border: solid 1px #E1E5E8;
  color: #999;
  position: relative;
  z-index: 2;
  font-size: 13px;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th:first-child {
  border-left: 0;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th:last-child {
  border-right: 0;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th.vd-ui-th-bar {
  border-left-color: #F5F7FA;
  z-index: 1;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-th.vd-ui-th-bar-prev {
  border-right-color: #F5F7FA;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-td {
  padding: 14px 20px;
  border-bottom: solid 1px #EBEFF2;
  background: #fff;
  vertical-align: top;
  word-break: break-all;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-tr:hover .vd-ui-td {
  background: #FAFBFC;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-tr.tr-checked .vd-ui-td {
  background: #E6F7FF;
}
.vd-ui-table-wrap .vd-ui-table .vd-ui-tr.tr-checked:hover .vd-ui-td {
  background: #D1EEFF;
}
.vd-ui-table-wrap .option-item {
  color: #09f;
  cursor: pointer;
  margin-right: 20px;
  position: relative;
}
.vd-ui-table-wrap .option-item::before {
  content: "";
  width: 1px;
  position: absolute;
  right: -10px;
  height: 15px;
  background: #e1e5e8;
  top: 3px;
}
.vd-ui-table-wrap .option-item:last-child {
  margin-right: 0;
}
.vd-ui-table-wrap .option-item:last-child::before {
  display: none;
}
.vd-ui-table-wrap .option-item.item-red {
  color: #E64545;
}
.vd-ui-table-wrap .option-item:hover {
  color: #f60;
}
.tyc-empty {
  padding: 130px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.tyc-empty .empty-img {
  width: 110px;
  height: 95px;
  background: url('../image/common/no-data.svg') no-repeat;
  background-size: 110px 95px;
  background-position: center;
  margin-bottom: 40px;
}
.tyc-info-wrap {
  padding-bottom: 20px;
}
@keyframes zhuan {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.tyc-info-wrap .tyc-info-loading {
  padding: 100px 0;
  text-align: center;
}
.tyc-info-wrap .tyc-info-loading .icon-loading {
  display: inline-block;
  font-size: 32px;
  color: #06c;
  animation: zhuan 2s linear infinite;
}
.tyc-info-wrap .tyc-info-title {
  margin-bottom: 20px;
}
.tyc-info-wrap .tyc-info-title .tyc-info-title-txt {
  font-size: 20px;
  font-weight: 700;
}
.tyc-info-wrap .tyc-info-title .tyc-info-tags {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: -5px;
  flex-wrap: wrap;
}
.tyc-info-wrap .tyc-info-title .tyc-info-tags .tag-item {
  background: #E6F7FF;
  font-size: 12px;
  height: 22px;
  line-height: 22px;
  padding: 0 5px;
  margin-right: 5px;
  margin-bottom: 5px;
}
.tyc-info-wrap .tyc-info-title .tyc-info-tags .tag-item:last-child {
  margin-right: 0;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.tyc-info-wrap .tyc-info-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.tyc-info-wrap .tyc-info-list .tyc-info-item {
  display: flex;
  border-bottom: solid 1px #E1E5E8;
}
.tyc-info-wrap .tyc-info-list .tyc-info-item .tyc-info-label {
  width: 160px;
  padding: 7px 10px;
  border-left: solid 1px #E1E5E8;
  background: #F5F7FA;
}
.tyc-info-wrap .tyc-info-list .tyc-info-item .tyc-info-txt {
  flex: 1;
  padding: 7px 10px;
  border-left: solid 1px #E1E5E8;
  border-right: solid 1px #E1E5E8;
}
.tyc-info-wrap .tyc-info-list .tyc-info-item:first-child {
  border-top: solid 1px #E1E5E8;
}

.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-button {
  margin-bottom: 10px;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap.no-info .vd-ui-button {
  margin-bottom: 0;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item {
  width: 357px;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-info {
  display: flex;
  align-items: center;
  padding: 5px;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-info:hover {
  background: #F5F7FA;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-info .vd-ui-file-icon {
  width: 13px;
  height: 16px;
  margin-right: 5px;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-info .vd-ui-file-icon img {
  width: 100%;
  height: 100%;
  vertical-align: top;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-info .vd-ui-file-name {
  flex: 1;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-info .vd-ui-file-size {
  margin-left: 20px;
  color: #999;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-info .vd-ui-file-option {
  font-size: 16px;
  color: #666;
  cursor: pointer;
  line-height: 1;
  margin-left: 20px;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-info .vd-ui-file-option:hover {
  color: #f60;
}
@keyframes uploadProgress {
  0% {
    width: 0;
  }
  100% {
    width: 95%;
  }
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-progress {
  width: 100%;
  height: 3px;
  background: #EBEFF2;
  position: relative;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-item .vd-ui-file-progress::before {
  content: "";
  width: 95%;
  height: 3px;
  background: #13BF13;
  position: absolute;
  top: 0;
  left: 0;
  animation: uploadProgress 5s linear;
}
.vd-ui-upload-wrap .vd-ui-upload-file-wrap .vd-ui-file-list {
  margin-bottom: 10px;
}
.vd-ui-upload-wrap .vd-ui-upload-tips {
  color: #999;
}
.vd-ui-upload-wrap .vd-ui-input-error {
  margin-top: 0;
  margin-bottom: 10px;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap {
  display: flex;
  margin-bottom: 10px;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-btn {
  width: 100px;
  height: 100px;
  border: 1px dashed #babfc2;
  background: #fafbfc;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-btn:hover {
  border-color: #969b9e;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-btn .icon-add {
  font-size: 24px;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-item {
  width: 100px;
  height: 100px;
  border: 1px solid #babfc2;
  background: #fff;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  position: relative;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-item img {
  max-width: 100%;
  max-height: 100%;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-item .vd-ui-upload-img-options {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
  background: rgba(0, 0, 0, 0.3);
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-item .vd-ui-upload-img-options .vd-ui-upload-img-option-item {
  flex: 1;
  color: #fff;
  cursor: pointer;
  height: 24px;
  font-size: 12px;
  text-align: center;
  line-height: 24px;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-item .vd-ui-upload-img-options .vd-ui-upload-img-option-item:hover {
  color: #f60;
}
@keyframes circle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-loading {
  width: 100px;
  height: 100px;
  border: 1px dashed #babfc2;
  background: #fff;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vd-ui-upload-wrap .vd-ui-upload-img-wrap .vd-ui-upload-img-loading .icon-loading {
  font-size: 24px;
  color: #0099FF;
  animation: circle 2s linear infinite;
}

.vd-ui-search-wrap {
  background: #fff;
}
.vd-ui-search-wrap .vd-ui-search-container {
  padding: 20px 10px;
}
.vd-ui-search-wrap .vd-ui-search-container.hidden {
  opacity: 0;
}
.vd-ui-search-wrap .vd-ui-search-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: -10px;
  position: relative;
}
.vd-ui-search-wrap .vd-ui-search-list.show-line {
  max-height: none !important;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item {
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
  width: calc(20% - 20px);
  display: flex;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item.hidden {
  opacity: 0;
  z-index: -1;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-label {
  width: 120px;
  text-align: right;
  color: #999;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content {
  flex: 1;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-select,
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-date,
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-cascader {
  width: 100%;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-input-multiple {
  width: 100%;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-date-editor,
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-range {
  width: 100%;
}
.vd-ui-search-wrap .vd-ui-search-btns {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  position: relative;
}
.vd-ui-search-wrap .vd-ui-search-btns .search-btn-inner {
  display: flex;
  margin: 0 auto;
  align-items: center;
}
.vd-ui-search-wrap .vd-ui-search-btns .search-btn-inner .vd-ui-button {
  margin-left: 10px;
}
.vd-ui-search-wrap .vd-ui-search-btns .search-btn-inner .vd-ui-button:first-child {
  margin-left: 0;
}
.vd-ui-search-wrap .vd-ui-search-btns .vd-ui-search-toggle {
  color: #09f;
  cursor: pointer;
  margin-left: 20px;
}
.vd-ui-search-wrap .vd-ui-search-btns .vd-ui-search-toggle .vd-ui_icon {
  font-size: 16px;
  margin-left: 5px;
  vertical-align: -2px;
}
.vd-ui-search-wrap .vd-ui-search-btns .vd-ui-search-toggle:hover {
  color: #f60;
}
.vd-ui-search-wrap .vd-ui-search-option {
  position: absolute;
  right: 20px;
  display: flex;
  align-items: center;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item {
  color: #09f;
  cursor: pointer;
  margin-left: 20px;
  position: relative;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item::before {
  content: '';
  position: absolute;
  width: 1px;
  height: 14px;
  background: #e1e5e8;
  left: -10px;
  top: 5px;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item:first-child {
  margin-left: 0;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item:first-child::before {
  display: none;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item .vd-ui_icon {
  font-size: 16px;
  vertical-align: -2px;
  margin-right: 5px;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item.item-setting {
  color: #999;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item:hover {
  color: #f60;
}
.vd-ui-search-wrap .dlg-form-footer {
  padding-left: 150px;
  width: 100%;
  display: flex;
}
.search-setting-dlg {
  overflow: hidden;
}
.search-setting-dlg .search-setting-top {
  display: flex;
  margin-bottom: 20px;
}
.search-setting-dlg .search-setting-top .setting-select-all {
  flex: 1;
}
.search-setting-dlg .search-setting-top .setting-refresh {
  color: #09f;
  cursor: pointer;
}
.search-setting-dlg .search-setting-top .setting-refresh .vd-ui_icon {
  font-size: 16px;
  vertical-align: -2px;
  margin-right: 5px;
}
.search-setting-dlg .search-setting-top .setting-refresh:hover {
  color: #f60;
}
.search-setting-dlg .dlg-setting-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -5px;
}
.search-setting-dlg .dlg-setting-list.sortable .dlg-setting-item .vd-ui-checkbox-item {
  cursor: pointer;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item {
  width: calc(25% - 3.75px);
  margin-right: 5px;
  border: solid 1px #E1E5E8;
  border-radius: 3px;
  margin-bottom: 5px;
  user-select: none;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item:nth-child(4n) {
  margin-right: 0;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item .vd-ui-checkbox-item {
  width: 100%;
  padding: 10px;
  cursor: move;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item .vd-ui-checkbox-item .vd-ui-checkbox-inner {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item.placehodler {
  opacity: 0.1;
}
@media screen and (max-width: 1919px) {
  .vd-ui-search-wrap .vd-ui-search-list .search-item {
    width: calc(25% - 20px);
  }
}
@media screen and (max-width: 1366px) {
  .vd-ui-search-wrap .vd-ui-search-list .search-item {
    width: calc(33.33% - 20px);
  }
  .vd-ui-search-wrap.no-left .vd-ui-search-list .search-item {
    width: calc(33.33% - 20px);
  }
}
@media screen and (max-width: 1680px) and (min-width: 1367px) {
  .vd-ui-search-wrap .vd-ui-search-list .search-item {
    width: calc(33.33% - 20px);
  }
  .vd-ui-search-wrap.no-left .vd-ui-search-list .search-item {
    width: calc(25% - 20px);
  }
}
.vd-ui-list-container {
  padding: 20px;
}
.vd-ui-list-container .vd-ui-list-inner {
  background: #fff;
  border: solid 1px #E1E5E8;
}
.vd-ui-list-container .vd-ui-list-inner.no-border-top {
  border-top: 0;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top {
  padding: 10px 20px;
  display: flex;
  align-items: center;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top .list-container-top-buttons {
  display: flex;
  align-items: center;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top .vd-ui-button {
  margin-right: 10px;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top .vd-ui-button:last-child {
  margin-right: 0;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top .list-select-num {
  margin-left: 20px;
  color: #999;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-content {
  position: relative;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-content .list-container-setting {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 11;
  width: 37px;
  height: 37px;
  text-align: center;
  line-height: 37px;
  font-size: 16px;
  color: #666;
  border-left: solid 1px #E1E5E8;
  cursor: pointer;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-content .list-container-setting:hover {
  color: #f60;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-table {
  min-height: calc(100vh - 500px);
}
.vd-ui-list-container .vd-ui-list-inner .list-container-pager {
  padding: 10px 20px;
  display: flex;
  align-items: center;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-pager .list-container-total {
  flex: 1;
  color: #999;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-pager .vd-ui-select {
  margin-left: 20px;
}

