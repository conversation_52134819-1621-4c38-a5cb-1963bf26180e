<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.mobile.system.mapper.MobileCommunicateRecordMapper">
    <sql id="Base_Column_List">
        COMMUNICATE_RECORD_ID,
        COMMUNICATE_TYPE,
        COMPANY_ID,
        RELATED_ID,
        TRADER_ID,
        TRADER_TYPE,
        BEGINTIME,
        ENDTIME,
        TRADER_CONTACT_ID,
        COMMUNICATE_MODE,
        COMMUNICATE_GOAL,
        NEXT_CONTACT_DATE,
        PHONE,
        COID,
        COID_TYPE,
        COID_LENGTH,
        COID_DOMAIN,
        COID_URI,
        NEXT_CONTACT_CONTENT,
        COMMENTS,
        IS_DONE,
        AFTER_SALES_TRADER_ID,
        RELATE_COMMUNICATE_RECORD_ID,
        CONTACT_CONTENT,
        CONTACT,
        CONTACT_MOB,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        SYNC_STATUS,
        IS_LFASR,
        IS_COMMENT,
        TT_NUMBER,
        CONTENT_SUFFIX,
        NONE_NEXT_DATE
    </sql>
    <resultMap id="tagMap" type="com.vedeng.erp.system.dto.TagDto" >
        <result column="TAG_ID" property="tagId" jdbcType="INTEGER" />
        <result column="TAG_TYPE" property="tagType" />
        <result column="IS_RECOMMEND" property="isRecommend" />
        <result column="TAG_NAME" property="tagName" />
    </resultMap>

    <resultMap id="BaseResultMap1" type="com.vedeng.mobile.system.model.CommunicateRecord">
        <!--@mbg.generated-->
        <!--@Table T_COMMUNICATE_RECORD-->
        <id column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId" />
        <result column="COMMUNICATE_TYPE" jdbcType="INTEGER" property="communicateType" />
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
        <result column="TRADER_TYPE" jdbcType="TINYINT" property="traderType" />
        <result column="BEGINTIME" jdbcType="BIGINT" property="begintime" />
        <result column="ENDTIME" jdbcType="BIGINT" property="endtime" />
        <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
        <result column="COMMUNICATE_MODE" jdbcType="INTEGER" property="communicateMode" />
        <result column="COMMUNICATE_GOAL" jdbcType="INTEGER" property="communicateGoal" />
        <result column="NEXT_CONTACT_DATE" jdbcType="DATE" property="nextContactDate" />
        <result column="PHONE" jdbcType="VARCHAR" property="phone" />
        <result column="COID" jdbcType="VARCHAR" property="coid" />
        <result column="COID_TYPE" jdbcType="BOOLEAN" property="coidType" />
        <result column="COID_LENGTH" jdbcType="INTEGER" property="coidLength" />
        <result column="COID_DOMAIN" jdbcType="VARCHAR" property="coidDomain" />
        <result column="COID_URI" jdbcType="VARCHAR" property="coidUri" />
        <result column="NEXT_CONTACT_CONTENT" jdbcType="VARCHAR" property="nextContactContent" />
        <result column="COMMENTS" jdbcType="LONGVARCHAR" property="comments" />
        <result column="IS_DONE" jdbcType="TINYINT" property="isDone" />
        <result column="AFTER_SALES_TRADER_ID" jdbcType="INTEGER" property="afterSalesTraderId" />
        <result column="RELATE_COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="relateCommunicateRecordId" />
        <result column="CONTACT_CONTENT" jdbcType="VARCHAR" property="contactContent" />
        <result column="CONTACT" jdbcType="VARCHAR" property="contact" />
        <result column="CONTACT_MOB" jdbcType="VARCHAR" property="contactMob" />
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="SYNC_STATUS" jdbcType="TINYINT" property="syncStatus" />
        <result column="IS_LFASR" jdbcType="TINYINT" property="isLfasr" />
        <result column="IS_COMMENT" jdbcType="TINYINT" property="isComment" />
        <result column="TT_NUMBER" jdbcType="VARCHAR" property="ttNumber" />
        <result column="CONTENT_SUFFIX" jdbcType="VARCHAR" property="contentSuffix" />
        <result column="NONE_NEXT_DATE" jdbcType="TINYINT" property="noneNextDate" />
    </resultMap>


  <!--  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap1">
        &lt;!&ndash;@mbg.generated&ndash;&gt;
        select
        <include refid="Base_Column_List" />
        from T_COMMUNICATE_RECORD
        where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
    </select>-->
    <!--查询指定行数据-->
    <select id="findByAll" resultType="com.vedeng.mobile.system.dto.MobileCommunicateRecordDto">
        select COMMUNICATE_RECORD_ID,
        COMMUNICATE_TYPE,
        COMPANY_ID,
        RELATED_ID,
        TRADER_ID,
        TRADER_TYPE,
        BEGINTIME,
        ENDTIME,
        TRADER_CONTACT_ID,
        COMMUNICATE_MODE,
        COMMUNICATE_GOAL,
        NEXT_CONTACT_DATE,
        PHONE,
        COID,
        COID_TYPE,
        COID_LENGTH,
        COID_DOMAIN,
        COID_URI,
        NEXT_CONTACT_CONTENT,
        COMMENTS,
        IS_DONE,
        AFTER_SALES_TRADER_ID,
        RELATE_COMMUNICATE_RECORD_ID,
        CONTACT_CONTENT,
        CONTACT,
        CONTACT_MOB,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        SYNC_STATUS,
        IS_LFASR,
        IS_COMMENT,
        TT_NUMBER,
        CONTENT_SUFFIX,
        NONE_NEXT_DATE
        from T_COMMUNICATE_RECORD
        <where>
            <if test="communicateRecordId != null">
                and COMMUNICATE_RECORD_ID = #{communicateRecordId}
            </if>
            <if test="communicateType != null">
                and COMMUNICATE_TYPE = #{communicateType}
            </if>
            <if test="companyId != null">
                and COMPANY_ID = #{companyId}
            </if>
            <if test="relatedId != null">
                and RELATED_ID = #{relatedId}
            </if>
            <if test="traderId != null">
                and TRADER_ID = #{traderId}
            </if>
            <if test="traderType != null">
                and TRADER_TYPE = #{traderType}
            </if>
            <if test="begintime != null">
                and BEGINTIME = #{begintime}
            </if>
            <if test="endtime != null">
                and ENDTIME = #{endtime}
            </if>
            <if test="traderContactId != null">
                and TRADER_CONTACT_ID = #{traderContactId}
            </if>
            <if test="communicateMode != null">
                and COMMUNICATE_MODE = #{communicateMode}
            </if>
            <if test="communicateGoal != null">
                and COMMUNICATE_GOAL = #{communicateGoal}
            </if>
            <if test="nextContactDate != null">
                and NEXT_CONTACT_DATE = #{nextContactDate}
            </if>
            <if test="phone != null and phone != ''">
                and PHONE = #{phone}
            </if>
            <if test="coid != null and coid != ''">
                and COID = #{coid}
            </if>
            <if test="coidType != null">
                and COID_TYPE = #{coidType}
            </if>
            <if test="coidLength != null">
                and COID_LENGTH = #{coidLength}
            </if>
            <if test="coidDomain != null and coidDomain != ''">
                and COID_DOMAIN = #{coidDomain}
            </if>
            <if test="coidUri != null and coidUri != ''">
                and COID_URI = #{coidUri}
            </if>
            <if test="nextContactContent != null and nextContactContent != ''">
                and NEXT_CONTACT_CONTENT = #{nextContactContent}
            </if>
            <if test="comments != null and comments != ''">
                and COMMENTS = #{comments}
            </if>
            <if test="isDone != null">
                and IS_DONE = #{isDone}
            </if>
            <if test="afterSalesTraderId != null">
                and AFTER_SALES_TRADER_ID = #{afterSalesTraderId}
            </if>
            <if test="relateCommunicateRecordId != null">
                and RELATE_COMMUNICATE_RECORD_ID = #{relateCommunicateRecordId}
            </if>
            <if test="contactContent != null and contactContent != ''">
                and CONTACT_CONTENT = #{contactContent}
            </if>
            <if test="contact != null and contact != ''">
                and CONTACT = #{contact}
            </if>
            <if test="contactMob != null and contactMob != ''">
                and CONTACT_MOB = #{contactMob}
            </if>
            <if test="addTime != null">
                and ADD_TIME = #{addTime}
            </if>
            <if test="creator != null">
                and CREATOR = #{creator}
            </if>
            <if test="modTime != null">
                and MOD_TIME = #{modTime}
            </if>
            <if test="updater != null">
                and UPDATER = #{updater}
            </if>
            <if test="syncStatus != null">
                and SYNC_STATUS = #{syncStatus}
            </if>
            <if test="isLfasr != null">
                and IS_LFASR = #{isLfasr}
            </if>
            <if test="isComment != null">
                and IS_COMMENT = #{isComment}
            </if>
            <if test="ttNumber != null and ttNumber != ''">
                and TT_NUMBER = #{ttNumber}
            </if>
            <if test="contentSuffix != null and contentSuffix != ''">
                and CONTENT_SUFFIX = #{contentSuffix}
            </if>
            <if test="noneNextDate != null">
                and NONE_NEXT_DATE = #{noneNextDate}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="communicateRecordId" useGeneratedKeys="true">
        insert into T_COMMUNICATE_RECORD(COMMUNICATE_TYPE, COMPANY_ID, RELATED_ID, TRADER_ID, TRADER_TYPE, BEGINTIME,
                                         ENDTIME, TRADER_CONTACT_ID, COMMUNICATE_MODE, COMMUNICATE_GOAL,
                                         NEXT_CONTACT_DATE, PHONE, COID, COID_TYPE, COID_LENGTH, COID_DOMAIN, COID_URI,
                                         NEXT_CONTACT_CONTENT, COMMENTS, IS_DONE, AFTER_SALES_TRADER_ID,
                                         RELATE_COMMUNICATE_RECORD_ID, CONTACT_CONTENT, CONTACT, CONTACT_MOB, ADD_TIME,
                                         CREATOR, MOD_TIME, UPDATER, SYNC_STATUS, IS_LFASR, IS_COMMENT, TT_NUMBER,
                                         CONTENT_SUFFIX, NONE_NEXT_DATE)
        values (#{communicateType}, #{companyId}, #{relatedId}, #{traderId}, #{traderType}, #{begintime}, #{endtime},
                #{traderContactId}, #{communicateMode}, #{communicateGoal}, #{nextContactDate}, #{phone}, #{coid},
                #{coidType}, #{coidLength}, #{coidDomain}, #{coidUri}, #{nextContactContent}, #{comments}, #{isDone},
                #{afterSalesTraderId}, #{relateCommunicateRecordId}, #{contactContent}, #{contact}, #{contactMob},
                #{addTime}, #{creator}, #{modTime}, #{updater}, #{syncStatus}, #{isLfasr}, #{isComment}, #{ttNumber},
                #{contentSuffix}, #{noneNextDate})
    </insert>

    <insert id="insertSelective" keyColumn="COMMUNICATE_RECORD_ID" keyProperty="communicateRecordId" parameterType="com.vedeng.mobile.system.model.CommunicateRecord" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_COMMUNICATE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communicateType != null">
                COMMUNICATE_TYPE,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="traderType != null">
                TRADER_TYPE,
            </if>
            <if test="begintime != null">
                BEGINTIME,
            </if>
            <if test="endtime != null">
                ENDTIME,
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID,
            </if>
            <if test="communicateMode != null">
                COMMUNICATE_MODE,
            </if>
            <if test="communicateGoal != null">
                COMMUNICATE_GOAL,
            </if>
            <if test="nextContactDate != null">
                NEXT_CONTACT_DATE,
            </if>
            <if test="phone != null">
                PHONE,
            </if>
            <if test="coid != null">
                COID,
            </if>
            <if test="coidType != null">
                COID_TYPE,
            </if>
            <if test="coidLength != null">
                COID_LENGTH,
            </if>
            <if test="coidDomain != null">
                COID_DOMAIN,
            </if>
            <if test="coidUri != null">
                COID_URI,
            </if>
            <if test="nextContactContent != null">
                NEXT_CONTACT_CONTENT,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="isDone != null">
                IS_DONE,
            </if>
            <if test="afterSalesTraderId != null">
                AFTER_SALES_TRADER_ID,
            </if>
            <if test="relateCommunicateRecordId != null">
                RELATE_COMMUNICATE_RECORD_ID,
            </if>
            <if test="contactContent != null">
                CONTACT_CONTENT,
            </if>
            <if test="contact != null">
                CONTACT,
            </if>
            <if test="contactMob != null">
                CONTACT_MOB,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="syncStatus != null">
                SYNC_STATUS,
            </if>
            <if test="isLfasr != null">
                IS_LFASR,
            </if>
            <if test="isComment != null">
                IS_COMMENT,
            </if>
            <if test="ttNumber != null">
                TT_NUMBER,
            </if>
            <if test="contentSuffix != null">
                CONTENT_SUFFIX,
            </if>
            <if test="noneNextDate != null">
                NONE_NEXT_DATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communicateType != null">
                #{communicateType,jdbcType=INTEGER},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null">
                #{traderType,jdbcType=BOOLEAN},
            </if>
            <if test="begintime != null">
                #{begintime,jdbcType=BIGINT},
            </if>
            <if test="endtime != null">
                #{endtime,jdbcType=BIGINT},
            </if>
            <if test="traderContactId != null">
                #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="communicateMode != null">
                #{communicateMode,jdbcType=INTEGER},
            </if>
            <if test="communicateGoal != null">
                #{communicateGoal,jdbcType=INTEGER},
            </if>
            <if test="nextContactDate != null">
                #{nextContactDate,jdbcType=DATE},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="coid != null">
                #{coid,jdbcType=VARCHAR},
            </if>
            <if test="coidType != null">
                #{coidType,jdbcType=BOOLEAN},
            </if>
            <if test="coidLength != null">
                #{coidLength,jdbcType=INTEGER},
            </if>
            <if test="coidDomain != null">
                #{coidDomain,jdbcType=VARCHAR},
            </if>
            <if test="coidUri != null">
                #{coidUri,jdbcType=VARCHAR},
            </if>
            <if test="nextContactContent != null">
                #{nextContactContent,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=LONGVARCHAR},
            </if>
            <if test="isDone != null">
                #{isDone,jdbcType=BOOLEAN},
            </if>
            <if test="afterSalesTraderId != null">
                #{afterSalesTraderId,jdbcType=INTEGER},
            </if>
            <if test="relateCommunicateRecordId != null">
                #{relateCommunicateRecordId,jdbcType=INTEGER},
            </if>
            <if test="contactContent != null">
                #{contactContent,jdbcType=VARCHAR},
            </if>
            <if test="contact != null">
                #{contact,jdbcType=VARCHAR},
            </if>
            <if test="contactMob != null">
                #{contactMob,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="syncStatus != null">
                #{syncStatus,jdbcType=BOOLEAN},
            </if>
            <if test="isLfasr != null">
                #{isLfasr,jdbcType=BOOLEAN},
            </if>
            <if test="isComment != null">
                #{isComment,jdbcType=BOOLEAN},
            </if>
            <if test="ttNumber != null">
                #{ttNumber,jdbcType=VARCHAR},
            </if>
            <if test="contentSuffix != null">
                #{contentSuffix,jdbcType=VARCHAR},
            </if>
            <if test="noneNextDate != null">
                #{noneNextDate,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>



<!--auto generated by MybatisCodeHelper on 2022-07-22-->
    <update id="updateIsDoneByNextContactDateAndRelatedIdAndCommunicateType">
        update T_COMMUNICATE_RECORD
        set IS_DONE=#{isDone,jdbcType=INTEGER}
        where NEXT_CONTACT_DATE <![CDATA[<=]]> #{nextContactDate,jdbcType=TIMESTAMP} and RELATED_ID=#{relatedId,jdbcType=INTEGER}
        and COMMUNICATE_TYPE = #{communicateType,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.mobile.system.model.CommunicateRecord">
        <!--@mbg.generated-->
        update T_COMMUNICATE_RECORD
        <set>
            <if test="communicateType != null">
                COMMUNICATE_TYPE = #{communicateType,jdbcType=INTEGER},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null">
                TRADER_TYPE = #{traderType,jdbcType=BOOLEAN},
            </if>
            <if test="begintime != null">
                BEGINTIME = #{begintime,jdbcType=BIGINT},
            </if>
            <if test="endtime != null">
                ENDTIME = #{endtime,jdbcType=BIGINT},
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="communicateMode != null">
                COMMUNICATE_MODE = #{communicateMode,jdbcType=INTEGER},
            </if>
            <if test="communicateGoal != null">
                COMMUNICATE_GOAL = #{communicateGoal,jdbcType=INTEGER},
            </if>
            <if test="nextContactDate != null">
                NEXT_CONTACT_DATE = #{nextContactDate,jdbcType=DATE},
            </if>
            <if test="phone != null">
                PHONE = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="coid != null">
                COID = #{coid,jdbcType=VARCHAR},
            </if>
            <if test="coidType != null">
                COID_TYPE = #{coidType,jdbcType=BOOLEAN},
            </if>
            <if test="coidLength != null">
                COID_LENGTH = #{coidLength,jdbcType=INTEGER},
            </if>
            <if test="coidDomain != null">
                COID_DOMAIN = #{coidDomain,jdbcType=VARCHAR},
            </if>
            <if test="coidUri != null">
                COID_URI = #{coidUri,jdbcType=VARCHAR},
            </if>
            <if test="nextContactContent != null">
                NEXT_CONTACT_CONTENT = #{nextContactContent,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=LONGVARCHAR},
            </if>
            <if test="isDone != null">
                IS_DONE = #{isDone,jdbcType=BOOLEAN},
            </if>
            <if test="afterSalesTraderId != null">
                AFTER_SALES_TRADER_ID = #{afterSalesTraderId,jdbcType=INTEGER},
            </if>
            <if test="relateCommunicateRecordId != null">
                RELATE_COMMUNICATE_RECORD_ID = #{relateCommunicateRecordId,jdbcType=INTEGER},
            </if>
            <if test="contactContent != null">
                CONTACT_CONTENT = #{contactContent,jdbcType=VARCHAR},
            </if>
            <if test="contact != null">
                CONTACT = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="contactMob != null">
                CONTACT_MOB = #{contactMob,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="syncStatus != null">
                SYNC_STATUS = #{syncStatus,jdbcType=BOOLEAN},
            </if>
            <if test="isLfasr != null">
                IS_LFASR = #{isLfasr,jdbcType=BOOLEAN},
            </if>
            <if test="isComment != null">
                IS_COMMENT = #{isComment,jdbcType=BOOLEAN},
            </if>
            <if test="ttNumber != null">
                TT_NUMBER = #{ttNumber,jdbcType=VARCHAR},
            </if>
            <if test="contentSuffix != null">
                CONTENT_SUFFIX = #{contentSuffix,jdbcType=VARCHAR},
            </if>
            <if test="noneNextDate != null">
                NONE_NEXT_DATE = #{noneNextDate,jdbcType=BOOLEAN},
            </if>
        </set>
        where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
    </update>

<!--auto generated by MybatisCodeHelper on 2022-07-26-->
    <select id="findByRelatedIdAndCommunicateTypeOrderByCommunicateRecordIdDesc" resultMap="BaseResultMap1">
        select
        <include refid="Base_Column_List"/>
        from T_COMMUNICATE_RECORD
        where RELATED_ID=#{relatedId,jdbcType=INTEGER} and COMMUNICATE_TYPE=#{communicateType,jdbcType=INTEGER} order by
        COMMUNICATE_RECORD_ID desc
        limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2022-07-29-->
    <select id="selectByRelatedIdAndCommunicateType" resultType="java.lang.Integer">
        select
        COMMUNICATE_RECORD_ID
        from T_COMMUNICATE_RECORD
        where RELATED_ID=#{relatedId,jdbcType=INTEGER} and COMMUNICATE_TYPE=#{communicateType,jdbcType=INTEGER} limit 1
    </select>


    <select id="getCommunicateTags" resultMap="tagMap">
        select
            b.TAG_ID, b.TAG_TYPE, b.IS_RECOMMEND, b.TAG_NAME
        from
            T_TRADER_TAG a
                left join
            T_TAG b on a.TAG_ID=b.TAG_ID
        where
            a.TRADER_TYPE = 3
          and
            a.TRADER_ID =#{communicateRecordId}
    </select>
</mapper>

