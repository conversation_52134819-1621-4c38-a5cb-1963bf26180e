package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.trader.domain.entity.TraderCommunicateFeedbackEntity;
import com.vedeng.erp.trader.service.TraderCommunicateFeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: traderCommunicateFeedback
 * @date 2024/1/22 14:33
 */
@ExceptionController
@RestController
@RequestMapping("/traderCommunicateFeedback")
@Slf4j
public class TraderCommunicateFeedbackApi {

    @Autowired
    private TraderCommunicateFeedbackService traderCommunicateFeedbackService;


    /**
     * 新增数据
     *
     * @param traderCommunicateFeedbackEntity 实体
     * @return 新增结果
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> add(@RequestBody TraderCommunicateFeedbackEntity traderCommunicateFeedbackEntity) {
        traderCommunicateFeedbackService.add(traderCommunicateFeedbackEntity);
        return R.success();
    }
}
