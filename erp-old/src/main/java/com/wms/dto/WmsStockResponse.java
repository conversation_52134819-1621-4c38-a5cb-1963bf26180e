package com.wms.dto;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName WmsStockResponse.java
 * @Description TODO WMS库存查询响应
 * @createTime 2020年08月11日 14:54:00
 */
public class WmsStockResponse {

    private String sku;
    //库存数量
    private BigDecimal qty;
    //分配数量
    private BigDecimal QtyAllocated;
    //冻结数量 冻结库存，是指由仓库或质量发起的，库存锁定，库存冻结后不能做任何的业务处理。
    private BigDecimal QtyOnHold;
    //生产日期
    private String lotAtt01;
    //有效期至
    private String lotAtt02;
    //入库日期
    private String lotAtt03;
    //厂家批号
    private String lotAtt04;
    //灭菌批号
    private String lotAtt05;
    //注册证号
    private String lotAtt06;
    //质量状态
    private String lotAtt08;
    //销售订单号
    private String lotAtt09;
    //入库单号
    private String lotAtt10;
    //贝登批次码
    private String lotAtt11;

    public BigDecimal getQtyAllocated() {
        return QtyAllocated;
    }

    public void setQtyAllocated(BigDecimal qtyAllocated) {
        QtyAllocated = qtyAllocated;
    }

    public BigDecimal getQtyOnHold() {
        return QtyOnHold;
    }

    public void setQtyOnHold(BigDecimal qtyOnHold) {
        QtyOnHold = qtyOnHold;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return lotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        this.lotAtt06 = lotAtt06;
    }

    public String getLotAtt08() {
        return lotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        this.lotAtt08 = lotAtt08;
    }

    public String getLotAtt09() {
        return lotAtt09;
    }

    public void setLotAtt09(String lotAtt09) {
        this.lotAtt09 = lotAtt09;
    }

    public String getLotAtt10() {
        return lotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        this.lotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return lotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        this.lotAtt11 = lotAtt11;
    }
}

