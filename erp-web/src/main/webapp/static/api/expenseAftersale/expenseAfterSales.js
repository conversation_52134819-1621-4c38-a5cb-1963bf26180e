function getAddEditInfo(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getAddEditInfo.do',
        method: 'post',
        params: data
    })
}

function saveAddExpenseAfterSale(data) {
    return axios({
        url: '/buyorderExpense/aftersale/saveAddExpenseAfterSale.do',
        method: 'post',
        data: data
    })
}

function saveEditExpenseAfterSale(data) {
    return axios({
        url: '/buyorderExpense/aftersale/saveEditExpenseAfterSale.do',
        method: 'post',
        data: data
    })
}

function getExpenseItemAndInvoiceList(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getExpenseItemAndInvoiceList.do',
        method: 'post',
        params: data
    })
}

function getExpenseItemInfoList(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getReturnItemList.do',
        method: 'post',
        params: data
    })
}

function getDetail(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getDetail.do',
        method: 'post',
        params: data
    })
}

function getStepItemList(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getStepNodeList.do',
        method: 'post',
        params: data
    })
}

function getRefundInvoiceList(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getCanRefundInvoices.do',
        method: 'post',
        params: data
    })
}

function getInvoiceList(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getInvoiceList.do',
        method: 'post',
        params: data
    })
}

function closeExpenseAfterSales(data) {
    return axios({
        url: '/buyorderExpense/aftersale/closeExpenseAfter.do',
        method: 'post',
        params: data
    })
}

function auditRecordData(data) {
    return axios({
        url: '/old/buyorderExpenseAfterSales/getBuyorderExpenseAfterSalesCheckStatus.do',
        method: 'post',
        params: data
    })
}

function getRefundInfo(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getReturnPaymentData.do',
        method: 'post',
        params: data
    })
}

function noNeedRefundInvoice(data) {
    return axios({
        url: '/buyorderExpense/aftersale/noNeedRefundInvoice.do',
        method: 'post',
        data: data
    })
}

function reversalInvoices(data) {
    return axios({
        url: '/buyorderExpense/aftersale/reversalInvoices.do',
        method: 'post',
        params: data
    })
}


function getExpenseAfterSalesCapitalBill(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getExpenseAfterSalesCapitalBill.do',
        method: 'post',
        params: data
    })
}

function saveExpenseAfterSalesCapitalBill(data) {
    return axios({
        url: '/buyorderExpense/aftersale/saveExpenseAfterSalesCapitalBill.do',
        method: 'post',
        data: data
    })
}

function executeRefundOperation(data) {
    return axios({
        url: '/buyorderExpense/aftersale/executeRefundOperation.do',
        method: 'post',
        data: data
    })
}

function completeExpenseAfterSale(data) {
    return axios({
        url: '/buyorderExpense/aftersale/completeExpenseAfterSale.do',
        method: 'post',
        params: data
    })
}

function getBankBill(data) {
    return axios({
        url: '/buyorderExpense/aftersale/getBankBill.do',
        method: 'post',
        params: data
    })
}

function parseExpenseAfterSalesDetailDesc(expenseAfterSalesDetail){
    let orderDescStr = ''
    let orderRemarkArr = expenseAfterSalesDetail.orderDesc
    if(orderRemarkArr != undefined && orderRemarkArr.length != 0){
        for(let i in orderRemarkArr){
            orderRemark = orderRemarkArr[i]
            let orderRemarkType = orderRemark.type
            let saleOrderDetail = orderRemark.originalOrderDto
            let saleOrderNo = saleOrderDetail.no
            let saleOrderId = saleOrderDetail.orderId
            let saleOrderGoodsList = saleOrderDetail.goodsDtos
            let buyorderExpenseOrder = orderRemark.resultOrderDto
            let buyorderExpenseOrderNo = buyorderExpenseOrder.no
            let buyorderExpenseOrderId = buyorderExpenseOrder.orderId
            for (let j in saleOrderGoodsList){
                goodsDto = saleOrderGoodsList[j]
                let sku = goodsDto.sku
                let goodsId = goodsDto.goodsId
                let x = goodsDto.x
                let y = goodsDto.y
                let tempDesc = ""
                if(orderRemarkType == 1){
                    // 本次关联的销售订单 A 中 B 订货号退货 X 个，分摊后本订单需要退货 Y 个
                    tempDesc = `本次关联的销售订单 <a style="text-decoration:none;" href="javascript:void(0);"
                                onclick="openTab('销售订单信息', '/order/saleorder/view.do?saleorderId=${saleOrderId}');">
                                <strong class="order-desc-style">${saleOrderNo}</strong>
                                </a> 中 <a href="javascript:void(0);" style="text-decoration:none;"
                           onclick="openTab('产品信息', '/goods/goods/viewbaseinfo.do?goodsId=${goodsId}');">
                           <strong class="order-desc-style">${sku}</strong>
                           </a> 订货号退货 ${x} 个，分摊后本订单需要退货 ${y} 个</br>`
                }else if (orderRemarkType == 2){
                    //该订单由销售单A中B订货号退货，联动采购费用单C退货，自动转单生成
                    tempDesc = `该订单由销售单 <a style="text-decoration:none;" href="javascript:void(0);"
                                onclick="openTab('销售订单信息', '/order/saleorder/view.do?saleorderId=${saleOrderId}');">
                                <strong class="order-desc-style">${saleOrderNo}</strong>
                                </a> 中 <a href="javascript:void(0);" style="text-decoration:none;"
                           onclick="openTab('产品信息', '/goods/goods/viewbaseinfo.do?goodsId=${goodsId}');">
                           <strong class="order-desc-style">${sku}</strong>
                           </a> 订货号退货，联动采购费用单 <a href="javascript:void(0);" style="text-decoration:none;" 
                           onclick="openTab('采购费用单信息', '/buyorderExpense/details.do?buyorderExpenseId=${buyorderExpenseOrderId}');">
                           <strong class="order-desc-style">${buyorderExpenseOrderNo}</strong>
                           </a> 退货自动生成</br>`
                }
                orderDescStr = orderDescStr + tempDesc
            }
        }
    }
    return orderDescStr
}

