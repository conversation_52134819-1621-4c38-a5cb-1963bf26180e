$(function() {
	$("#submit").click(function(){
		checkLogin();
		$(".warning").remove();
		$("input").removeClass("errorbor");
		$("select").removeClass("errorbor");
		if($("#traderContactId").val() == 0){
			warnTips("traderContactId","联系人不允许为空");
			return false;
		}
		if($("#businessChanceAccuracy").val() == -1){
			warnTips("businessChanceAccuracy","商机精准度不允许为空");
			return false;
		}
		if($("#begin").val() == ''){
			warnTipsDate("timeErrorMsg","开始时间不允许为空");
			$("#begin").addClass("errorbor");
			return false;
		}
		if($("#end").val() == ''){
			warnTipsDate("timeErrorMsg","结束时间不允许为空");
			$("#end").addClass("errorbor");
			return false;
		}

	/*	2020.12http://jira.ivedeng.com/browse/VDERP-5008 需求中要求删除的字段*/
		/*var communicateGoal=$('input:radio[name="communicateGoal"]:checked').val();
		if(communicateGoal==undefined||communicateMode==""){
			$("#communicateGoal").css("display","");
			return false;
		}else{
			$("#communicateGoal").css("display","none");
		}

		var communicateMode=$('input:radio[name="communicateMode"]:checked').val();
		if(communicateMode==undefined||communicateMode==""){
			$("#communicateMode").css("display","");
			return false;
		}else{
			$("#communicateMode").css("display","none");
		}*/

		if($("input[name='tagId']").length == 0 && $("input[name='tagName']").length == 0 && $("textarea[name='contentSuffix']").val() == ''){
			//warnTips("tag_show_ul"," 沟通内容不允许为空");
			layer.alert("沟通内容不允许为空")
			return false;
		}
		if ($("textarea[name='contentSuffix']").val().length > 200){
			warnTips("contentSuffixError"," 沟通内容最多输入200字符，请检查后提交");
			return false;
		}

		var nextDate = $("input[name='nextDate']").val();

		if (! $('#noneNextDate').is(':checked') && nextDate.length == 0){
			warnTips("nextDate"," 下次沟通时间不允许为空");
			return false;
		}

		if(nextDate.length != 0 && $("#begin").val()>=(nextDate+" 23:59:59")){
			warnTipsDate("nextDate","下次沟通时间不能在沟通时间之前");
			return false;
		}

		if($("#nextContactContent").val().length > 256){
			warnTips("nextContactContent"," 下次沟通内容长度不允许超过256个字符");
			return false;
		}
		if($("#comments").val().length > 128){
			warnTips("comments"," 备注长度不允许超过128个字符");
			return false;
		}
		var communicateRecordId = $("input[name='communicateRecordId']").val();
		var posturl ='';
		if(communicateRecordId == undefined || communicateRecordId == ''){
			posturl = '/order/bussinesschance/saveaddcommunicate.do';
		}else{
			//编辑页面相关
			posturl = '/order/bussinesschance/saveeditcommunicate.do';
			$("#returnInvoiceForm").find("#dynamicParameter").html("");//清空参数，防止提交失败后再次提交参数重复
			if ($('#traderId').val() == 0){
				var splits = $("#traderContactId option:selected").html().trim().split('|');
				$("#myform").find("#dynamicParameter").html(
					"<input type='hidden' name='contact' value='" + splits[0] +  "'>\n" +
					"<input type='hidden' name='contactMob' value='" + splits[1] +  "'>"
				);
				//商机无联系人不保存关联联系人信息
				$('#traderContactId').attr('name', '');
			}
		}

		$.ajax({
			url:page_url+posturl,
			data:$('#myform').serialize(),
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data)
			{
				if(data.code==0){
					var datas=data.data.split(",");
					window.parent.location.href=page_url+"/order/bussinesschance/toSalesDetailPage.do?bussinessChanceId="
												+datas[0]+"&traderId="+datas[1];
				}else{
					layer.alert(data.message);
				}
				
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		return false;
	});

	$('#noneNextDate').click(function () {
		var noneNextDate = $('#noneNextDate').is(':checked');
		if (noneNextDate){
			$('#nextDate').val('');
			$('#nextDate').css('background-color', '#CCCCCC');
			$('#nextDate').attr('disabled', true);
			$('#noneNextDateVal').val(1);
		} else {
			$('#nextDate').css('background-color', 'white');
			$('#nextDate').attr('disabled', false);
			$('#noneNextDateVal').val(0);
		}
	})
});

function changeDate(obj){
	checkLogin();
	$("#endHidden").val(obj.value)
}
