package com.vedeng.erp.aftersale.enums;

/**
 * 费用售后单-售后单状态
 * 费用售后单状态 0.待确认 1.进行中 2.已完结 3.已关闭'
 */
public enum ExpenseAfterSalesStatusEnum {
    CONFIRM(0,"待确认"),
    PROCRESSING(1,"进行中"),
    COMPLETED(2,"已完结"),
    CLOSED(3,"已关闭"),
    ;

    private Integer code;

    private String desc;


    ExpenseAfterSalesStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
