package com.vedeng.common.redis;

import com.ctrip.framework.apollo.ConfigService;
import com.google.common.base.Preconditions;
import com.vedeng.common.constant.ErpConst;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class RedisKeyUtils {

    private static final String PROP_NAME = "redis_dbtype";
    private static String prefix;


    /**
     * KEY_PREFIX_DATA_DICTIONARY
     */
    private final static String KEY_PREFIX_USER_PERMISSIONS = ErpConst.KEY_PREFIX_USER_PERMISSIONS;

    /**
     * KEY_PREFIX_DATA_DICTIONARY
     */
    private final static String KEY_PREFIX_USER_ROLES = ErpConst.KEY_PREFIX_USER_ROLES;


    /**
     * 根据指定后缀拼接redis key.
     *
     * @param suffix
     * @return
     */
    public static String createKey(String suffix) {
        if (suffix == null || suffix.length() == 0) {
            throw new IllegalArgumentException("suffix is empty");
        }
        return getPrefix() + suffix;
    }


    public static String createPermissionKey(String username) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(username), "username is empty");
        return getPrefix() + KEY_PREFIX_USER_PERMISSIONS + username.toLowerCase() + ":" + ErpConst.NJ_COMPANY_ID;
    }


    public static String createRoleKey(String username) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(username), "username is empty");
        return getPrefix() + KEY_PREFIX_USER_ROLES + username.toLowerCase() + ":" + ErpConst.NJ_COMPANY_ID;
    }

    private static String getPrefix() {
        String result = prefix;
        if (result == null || result.length() == 0) {
            synchronized (RedisKeyUtils.class) {
                result = prefix;
                if (result == null || result.length() == 0) {
                    result = ConfigService.getAppConfig().getProperty(PROP_NAME, null);

                    if (result == null || result.length() == 0) {
                        throw new IllegalStateException(String.format("Failed to get value:[%s] from Apollo config",
                                PROP_NAME));
                    }

                    prefix = result;
                }
            }
        }

        return result;
    }
}
