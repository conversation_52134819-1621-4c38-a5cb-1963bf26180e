package com.vedeng.erp.system.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 业务流转单明细价格表
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlowNodeOrderDetailPriceDto extends BaseDto {
    /**
     * 主键
     */
    private Long flowNodeOrderPriceId;

    /**
     * 关联业务流转单明细ID
     */
    private Long flowOrderDetailId;

    /**
     * 节点ID
     */
    private Long flowNodeId;

    /**
     * 节点级别
     */
    private Integer nodeLevel;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 加价率（%），与上一节点的价格计算相关
     */
    private BigDecimal markupRate;

    /**
     * 毛利率（%）
     */
    private BigDecimal grossProfitRate;

    /**
     * 是否删除
     */
    private Integer isDelete;


    /**
     * 第一位：0 表示未修改，1 表示已修改。
     * 第二位：0 表示通过价格计算加价率和毛利率，1 表示通过加价率计算价格。
     */
    private String calculatePriceFromMarkupRate;
}