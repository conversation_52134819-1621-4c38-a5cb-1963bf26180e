package com.vedeng.erp.system.dto;

import lombok.Data;

/**
 * 子公司主体详细信息
 */
@Data
public class BaseCompanyInfoDetailDto {

    //@Value("${bd_trader_id_sync:179616}")
    private Integer traderIdSync;

    //销售单客户名称、收票客户名称
    //@Value("${bd_trader_name_sync:南京贝登医疗股份有限公司}")
    private String traderNameSync;

    //客户联系名称、收货联系人名称、收票联系人名称
    //@Value("${bd_trader_contactName_sync:袁雪晴}")
    private String traderContactNameSync;

    //客户联系人电话、收货联系人电话、收票联系人电话
    //@Value("${bd_trader_contactMobile_sync:18651867845}")
    private String invoiceTraderContactMobileSync;

    //@Value("${bd_dilivery_provinceId_sync:320000,江苏省}")
    private String diliveryProvinceIdSync;

    //@Value("${bd_dilivery_city_sync:320100,南京市}")
    private String diliveryCityIdSync;

    //@Value("${bd_dilivery_area_sync:320104,秦淮区}")
    private String diliveryAreaIdSync;

    //@Value("${bd_dilivery_address_sync:南京市秦淮区永丰大道36号南京天安数码城01幢1007室}")
    private String diliveryAddressSync;

    /**贝登采购的供应商：医购优选*/
    //@Value("${bd_supply_traderId_sync:599619}")
//    private Integer bdSupplyTraderId;
    /**
     * 采购订单的前缀
     */
    private String orderCgOrderNoPrefix;

    /**
     * 付款单位联行号
     */
    private String bankCode;

    /**
     * 出库印章的图片-用于自动生成确认单
     */
    private String outConfirmSignPngUrl;

    /**
     * 用于当前主体作为收货客户的印章
     */
    private String shouhuoConfirmSignPngUrl;

}
