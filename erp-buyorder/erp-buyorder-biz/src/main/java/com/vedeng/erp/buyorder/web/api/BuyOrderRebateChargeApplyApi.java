package com.vedeng.erp.buyorder.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto;
import com.vedeng.erp.buyorder.service.BuyOrderRebateChargeApplyService;
import com.vedeng.erp.system.dto.ActivityAuditDto;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 采购返利结算收款申请 Api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22 16:26
 */
@ExceptionController
@Controller
@RequestMapping("/buyOrder/rebateChargeApply/api")
public class BuyOrderRebateChargeApplyApi {

    @Autowired
    private BuyOrderRebateChargeApplyService buyOrderRebateChargeApplyService;

    /**
     * 分页查询采购返利结算收款申请 列表
     *
     * @param rebateChargeApplyDtoPageParam 分页查询参数
     * @return PageInfo<BuyOrderRebateChargeApplyDto>
     */
    @RequestMapping("/page")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<PageInfo<BuyOrderRebateChargeApplyDto>> page(@RequestBody PageParam<BuyOrderRebateChargeApplyDto> rebateChargeApplyDtoPageParam) {
        return R.success(buyOrderRebateChargeApplyService.page(rebateChargeApplyDtoPageParam));
    }

    /**
     * 保存新增 采购返利结算收款申请
     *
     * @param rebateChargeApplyDto BuyOrderRebateChargeApplyDto
     * @return buyOrderRebateChargeId 返回新增后的主键id跳转到详情页
     */
    @RequestMapping("/add")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> add(@RequestBody BuyOrderRebateChargeApplyDto rebateChargeApplyDto) {
        return R.success(buyOrderRebateChargeApplyService.add(rebateChargeApplyDto));
    }

    /**
     * 查询详情信息
     *
     * @param buyOrderRebateChargeId 主键id
     * @return BuyOrderRebateChargeApplyDto
     */
    @RequestMapping("/findById")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<BuyOrderRebateChargeApplyDto> findById(@RequestParam Integer buyOrderRebateChargeId) {
        return R.success(buyOrderRebateChargeApplyService.getDetail(buyOrderRebateChargeId));
    }

    /**
     * 校验明细附件并上传oss（仅针对明细附件需要校验，其他附件依旧使用原有的oss上传方法）
     *
     * @param request HttpServletRequest
     * @param lwfile  MultipartFile
     * @return FileInfo
     */
    @ResponseBody
    @RequestMapping("/checkAndUpload")
    @NoNeedAccessAuthorization
    public FileInfo ajaxFileUpload(HttpServletRequest request, @RequestParam("lwfile") MultipartFile lwfile) {
        return buyOrderRebateChargeApplyService.checkAndUploadDetailFile(request, lwfile);
    }

    /**
     * 审核操作
     *
     * @param activityAuditDto 封装审核参数
     * @return R<?>
     */
    @ResponseBody
    @RequestMapping("/audit")
    @NoNeedAccessAuthorization
    public R<?> doAudit(@RequestBody ActivityAuditDto activityAuditDto) {
        buyOrderRebateChargeApplyService.complementRebateChargeProcess(activityAuditDto.getBusinessId(), activityAuditDto.getPass(), activityAuditDto.getComments(), activityAuditDto.getTaskId());
        return R.success();
    }

}