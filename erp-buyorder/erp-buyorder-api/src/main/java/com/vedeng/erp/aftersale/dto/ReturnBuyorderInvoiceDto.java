package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 发票退票信息
 * <AUTHOR>
 */
@Data
public class ReturnBuyorderInvoiceDto {

    /**
     * 主键id
     */
    private Integer afterSalesInvoiceId;
    /**
     * 售后id
     */
    private Integer afterSalesId;
    /**
     * 是否需退票0否1是
     */
    private Integer isRefundInvoice;
    /**
     * 退票状态0未退票1已退票
     */
    private Integer status;
    /**
     * 发票号
     */
    private String invoiceNo;
    /**
     * 售后仅退票金额
     */
    private BigDecimal afterInvoiceAmount;
    /**
     * 发票类型
     */
    private Integer invoiceType;
}
