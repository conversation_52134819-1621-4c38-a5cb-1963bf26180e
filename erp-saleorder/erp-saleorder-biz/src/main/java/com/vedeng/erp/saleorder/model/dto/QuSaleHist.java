package com.vedeng.erp.saleorder.model.dto;

import java.math.BigDecimal;
import java.util.Date;

public class QuSaleHist {
    /** 主键id  QU_SALE_HIS_ID **/
    private Integer quSaleHisId;

    /** 销售单ID  SALEORDER_ID **/
    private Integer saleorderId;

    /** 报价订单ID  QUOTEORDER_ID **/
    private Integer quoteorderId;

    /** SKU  SKU **/
    private String sku;

    /** 商品id  GOODS_ID **/
    private Integer goodsId;

    /** 报价单商品数量  QU_NUM **/
    private Integer quNum;

    /** 销售单商品数量  SALE_NUM **/
    private Integer saleNum;

    /** 单价  PRICE **/
    private BigDecimal price;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MODE_TIME **/
    private Date modeTime;

    /** 是否删除  0否  1是  IS_DELETE **/
    private Boolean isDelete;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    private String skuName;

    private Integer isChangce;

    public Integer getIsChangce() {
        return isChangce;
    }

    public void setIsChangce(Integer isChangce) {
        this.isChangce = isChangce;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    /**   主键id  QU_SALE_HIS_ID   **/
    public Integer getQuSaleHisId() {
        return quSaleHisId;
    }

    /**   主键id  QU_SALE_HIS_ID   **/
    public void setQuSaleHisId(Integer quSaleHisId) {
        this.quSaleHisId = quSaleHisId;
    }

    /**   销售单ID  SALEORDER_ID   **/
    public Integer getSaleorderId() {
        return saleorderId;
    }

    /**   销售单ID  SALEORDER_ID   **/
    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    /**   报价订单ID  QUOTEORDER_ID   **/
    public Integer getQuoteorderId() {
        return quoteorderId;
    }

    /**   报价订单ID  QUOTEORDER_ID   **/
    public void setQuoteorderId(Integer quoteorderId) {
        this.quoteorderId = quoteorderId;
    }

    /**   SKU  SKU   **/
    public String getSku() {
        return sku;
    }

    /**   SKU  SKU   **/
    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    /**   商品id  GOODS_ID   **/
    public Integer getGoodsId() {
        return goodsId;
    }

    /**   商品id  GOODS_ID   **/
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**   报价单商品数量  QU_NUM   **/
    public Integer getQuNum() {
        return quNum;
    }

    /**   报价单商品数量  QU_NUM   **/
    public void setQuNum(Integer quNum) {
        this.quNum = quNum;
    }

    /**   销售单商品数量  SALE_NUM   **/
    public Integer getSaleNum() {
        return saleNum;
    }

    /**   销售单商品数量  SALE_NUM   **/
    public void setSaleNum(Integer saleNum) {
        this.saleNum = saleNum;
    }

    /**   单价  PRICE   **/
    public BigDecimal getPrice() {
        return price;
    }

    /**   单价  PRICE   **/
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MODE_TIME   **/
    public Date getModeTime() {
        return modeTime;
    }

    /**   更新时间  MODE_TIME   **/
    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    /**   是否删除  0否  1是  IS_DELETE   **/
    public Boolean getIsDelete() {
        return isDelete;
    }

    /**   是否删除  0否  1是  IS_DELETE   **/
    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}
