<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.NotCreateBussinessChanceReasonMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.NotCreateBussinessChanceReason">
    <id column="NOT_CREATE_BUSSINESS_CHANCE_REASON_ID" jdbcType="INTEGER" property="notCreateBussinessChanceReasonId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="REASON_ID" jdbcType="INTEGER" property="reasonId" />
    <result column="REASON_DESC" jdbcType="VARCHAR" property="reasonDesc" />
    <result column="SERVICE_USER_ID" jdbcType="INTEGER" property="serviceUserId" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATOR" jdbcType="INTEGER" property="updator" />
  </resultMap>
  <sql id="Base_Column_List">
    NOT_CREATE_BUSSINESS_CHANCE_REASON_ID, TRADER_ID, MOBILE, REASON_ID, REASON_DESC, 
    SERVICE_USER_ID, `SOURCE`, IS_DELETE, ADD_TIME, UPDATE_TIME, CREATOR, UPDATOR
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_NOT_CREATE_BUSSINESS_CHANCE_REASON
    where NOT_CREATE_BUSSINESS_CHANCE_REASON_ID = #{notCreateBussinessChanceReasonId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_NOT_CREATE_BUSSINESS_CHANCE_REASON
    where NOT_CREATE_BUSSINESS_CHANCE_REASON_ID = #{notCreateBussinessChanceReasonId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="NOT_CREATE_BUSSINESS_CHANCE_REASON_ID" keyProperty="notCreateBussinessChanceReasonId" parameterType="com.vedeng.order.model.NotCreateBussinessChanceReason" useGeneratedKeys="true">
    insert into T_NOT_CREATE_BUSSINESS_CHANCE_REASON (TRADER_ID, MOBILE, REASON_ID, 
      REASON_DESC, SERVICE_USER_ID, `SOURCE`, 
      IS_DELETE, ADD_TIME, UPDATE_TIME, 
      CREATOR, UPDATOR)
    values (#{traderId,jdbcType=INTEGER}, #{mobile,jdbcType=VARCHAR}, #{reasonId,jdbcType=INTEGER}, 
      #{reasonDesc,jdbcType=VARCHAR}, #{serviceUserId,jdbcType=INTEGER}, #{source,jdbcType=TINYINT}, 
      #{isDelete,jdbcType=TINYINT}, #{addTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{updator,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="NOT_CREATE_BUSSINESS_CHANCE_REASON_ID" keyProperty="notCreateBussinessChanceReasonId" parameterType="com.vedeng.order.model.NotCreateBussinessChanceReason" useGeneratedKeys="true">
    insert into T_NOT_CREATE_BUSSINESS_CHANCE_REASON
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="reasonId != null">
        REASON_ID,
      </if>
      <if test="reasonDesc != null">
        REASON_DESC,
      </if>
      <if test="serviceUserId != null">
        SERVICE_USER_ID,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="reasonId != null">
        #{reasonId,jdbcType=INTEGER},
      </if>
      <if test="reasonDesc != null">
        #{reasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="serviceUserId != null">
        #{serviceUserId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.NotCreateBussinessChanceReason">
    update T_NOT_CREATE_BUSSINESS_CHANCE_REASON
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="reasonId != null">
        REASON_ID = #{reasonId,jdbcType=INTEGER},
      </if>
      <if test="reasonDesc != null">
        REASON_DESC = #{reasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="serviceUserId != null">
        SERVICE_USER_ID = #{serviceUserId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
    </set>
    where NOT_CREATE_BUSSINESS_CHANCE_REASON_ID = #{notCreateBussinessChanceReasonId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.NotCreateBussinessChanceReason">
    update T_NOT_CREATE_BUSSINESS_CHANCE_REASON
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      REASON_ID = #{reasonId,jdbcType=INTEGER},
      REASON_DESC = #{reasonDesc,jdbcType=VARCHAR},
      SERVICE_USER_ID = #{serviceUserId,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATOR = #{updator,jdbcType=INTEGER}
    where NOT_CREATE_BUSSINESS_CHANCE_REASON_ID = #{notCreateBussinessChanceReasonId,jdbcType=INTEGER}
  </update>
</mapper>