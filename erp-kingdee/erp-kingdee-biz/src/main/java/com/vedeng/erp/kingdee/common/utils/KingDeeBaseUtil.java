package com.vedeng.erp.kingdee.common.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶基础service工具类，主要用于解析字段及适配规则
 * @date 2023/3/28 13:56
 */
@Slf4j
public class KingDeeBaseUtil {


    /**
     * 获取需要返回字段的级别
     */
    public static List<String> getLevel(List<String> needReturnFields) {
        List<String> list = new ArrayList<>();
        // 根据回写的字段，组装出需要的层级
        needReturnFields.forEach(f -> {
            // 解析字段层级
            List<String> fieldsLayer = StrUtil.split(f, StrUtil.DOT);
            if (fieldsLayer.size() == 2) {
                // 2级
                String firstObjField = CollUtil.getFirst(fieldsLayer);
                list.add(firstObjField);
            }
            if (fieldsLayer.size() == 3) {
                // 3级
                String firstLayerField = CollUtil.getFirst(fieldsLayer);
                String secondObjField = fieldsLayer.get(1);
                list.add(firstLayerField);
                list.add(secondObjField);
            }
        });
        return list.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 下一个对象
     */
    public static void nextData(Field field, Object dto, Map<String, Object> map, List<String> level) {
        Object recordNext = BeanUtil.getProperty(dto, field.getName());
        Field[] fields = null;
        if (field.getType() == List.class || field.getType() == Set.class) {
            //得到集合的实际泛型类型
            Type type = field.getGenericType();
            ParameterizedType pType = (ParameterizedType) type;
            Type[] types = pType.getActualTypeArguments();
            Class<?> clazz = (Class<?>) types[0];
            fields = clazz.getDeclaredFields();
            Collection<Object> recordList = (Collection<Object>) recordNext;
            if (CollUtil.isNotEmpty(recordList)) {
                for (Object record : recordList) {
                    for (Field next : fields) {
                        nextData(next, record, map, level);
                        getWriteBackFields(next, record, map, level);
                    }
                }
            }
        }
    }

    /**
     * 获取回写的字段
     */
    public static void getWriteBackFields(Field field,
                                          Object dto,
                                          Map<String, Object> returnMap,
                                          List<String> level) {
        WriteBackField annotation = field.getAnnotation(WriteBackField.class);
        if (null == annotation) {
            return;
        }
        // 需要回写的字段
        String writeBackField = StrUtil.isBlank(annotation.value()) ? field.getName() : annotation.value();
        if (writeBackField == null) {
            return;
        }

        // 一级字段回写
        Object fieldValue = BeanUtil.getProperty(returnMap, writeBackField);
        if (ObjectUtil.isNotEmpty(fieldValue)) {
            BeanUtil.setProperty(dto, field.getName(), fieldValue);
            return;
        }

        // 二级字段回写 通过回传的字段写入当前的字段
        String backWriteByErpField = annotation.backWriteByErpField();
        String backWriteToKingDeeField = annotation.backWriteToKingDeeField();
        if (CollUtil.isNotEmpty(level)) {
            // 存在2级，先获取2级对象
            if (level.size() == 1) {
                Object secondLevelObj = BeanUtil.getProperty(returnMap, CollUtil.getFirst(level));
                if (ObjectUtil.isNotEmpty(secondLevelObj)) {
                    // 根据回传字段在二级对象中获取
                    List<JSONObject> jsonObjectList = Convert.toList(JSONObject.class, secondLevelObj);
                    if (ObjectUtil.isNotEmpty(backWriteByErpField)) {
                        Object backWriteByErpValue = BeanUtil.getProperty(dto, backWriteByErpField);
                        if (ObjectUtil.isNotEmpty(backWriteByErpValue)) {
                            String fEntryId = jsonObjectList
                                    .stream()
                                    .filter(o -> o.get(backWriteToKingDeeField).equals(backWriteByErpValue))
                                    .map(o -> o.getString(writeBackField))
                                    .findFirst()
                                    .orElseThrow(() -> new KingDeeException("金蝶异常:" + JSON.toJSONString(jsonObjectList)));
                            BeanUtil.setProperty(dto, field.getName(), fEntryId);
                        }
                    }
                }
            }
        }

    }


    /**
     * 根据金蝶返回的json对象转换成对应map对象
     */
    public static List<Map<String, Object>> getMaps(ArrayList<JsonObject> needReturnData) {
        Gson gson = new Gson();
        List<Map<String, Object>> maps = new ArrayList<>();
        for (JsonObject object1 : needReturnData) {
            Map<String, Object> map = new HashMap<>();
            for (Map.Entry<String, JsonElement> entry : object1.entrySet()) {
                String key = entry.getKey();
                JsonElement value = entry.getValue();
                if (value.isJsonPrimitive()) {
                    map.put(key, value.getAsString());
                }
                if (value.isJsonObject()) {
                    map.put(key, gson.fromJson(value, Map.class));
                }
                if (value.isJsonArray()) {
                    String json = new Gson().toJson(value);
                    JSONArray objects = JSONUtil.parseArray(json);
                    map.put(key, JSONUtil.toList(objects, Map.class));
                }
            }
            maps.add(map);
        }
        return maps;
    }


    /**
     * 组装需要查询的字段
     */
    public static List<String> assembleQueryFields(List<String> inputs, List<String> variables) {
        if (CollUtil.isEmpty(variables)) {
            variables = new ArrayList<>();
            variables.add("FENTRYID");
            variables = variables.stream().map(String::toUpperCase).collect(Collectors.toList());
        }
        ArrayList<String> newInputs = new ArrayList<>();
        for (String inputStr : inputs) {
            String[] splitResult = inputStr.split("\\.");
            if (splitResult.length == 2) {
                String prefix = splitResult[0].toUpperCase();
                String suffix = splitResult[1].toUpperCase();
                if (variables.contains(suffix)) {
                    String replace = StrUtil.replace(inputStr, ".", "_");
                    newInputs.add(replace);
                } else {
                    newInputs.add(splitResult[1]);
                }
            } else {
                newInputs.add(inputStr);
            }
        }
        return newInputs;
    }

    /**
     * 根据注解获取对应的字段和值
     */
    public static <A extends Annotation> Map<String, Object> getAnnotationParameter(Object obj, Class<A> annotationClass, String method) {
        Map<String, Object> map = new HashMap<>();
        Field[] fields = ReflectUtil.getFields(obj.getClass());
        if (ArrayUtil.isNotEmpty(fields)) {
            for (Field fie : fields) {
                A annotation = fie.getAnnotation(annotationClass);
                if (annotation == null) {
                    continue;
                }
                if (annotation instanceof Annotation) {
                    // 转换类型
                    String methodValue = null;
                    if (StrUtil.isNotBlank(method)) {
                        try {
                            Method valueMethod = annotation.getClass().getMethod(method);
                            methodValue = (String) valueMethod.invoke(annotation);
                        } catch (Exception e) {
                            log.warn("获取注解{}方法,先定义{}方法", method, method);
                        }
                    }
                    String field = StrUtil.isBlank(methodValue) ? fie.getName() : methodValue;
                    // 获取Value的值
                    Object fieldValue = BeanUtil.getProperty(obj, fie.getName());
                    if (StrUtil.isBlank(field)) {
                        log.error(obj.getClass().getName() + "对象无注解,请检查");
                        throw new KingDeeException(StrUtil.format("{}:对应@{}无注解,请检查", obj.getClass().getName(), annotationClass.getName()));
                    }
                    map.put(field, fieldValue);
                }
            }
        }

        if (CollUtil.isEmpty(map)) {
            log.error(obj.getClass().getName() + "对象无值或者无注解,请检查");
            throw new KingDeeException(StrUtil.format("{}:{}无对应注解,请检查", obj.getClass().getName(), annotationClass.getName()));
        }
        return map;
    }

    /**
     * 回写WriteBackField中方法
     *
     * @param needReturnFields needReturnFields
     * @param maps             maps
     */
    public static void writeBack(Object d, List<String> needReturnFields, List<Map<String, Object>> maps) {
        Map<String, Object> map = CollUtil.getFirst(maps);
        List<String> level = KingDeeBaseUtil.getLevel(needReturnFields);
        Field[] fields = ReflectUtil.getFields(d.getClass());
        if (ArrayUtil.isNotEmpty(fields)) {
            for (Field field : fields) {
                KingDeeBaseUtil.nextData(field, d, map, level);
                KingDeeBaseUtil.getWriteBackFields(field, d, map, level);
            }
        }
    }
}
