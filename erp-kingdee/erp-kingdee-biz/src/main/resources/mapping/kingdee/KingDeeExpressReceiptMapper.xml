<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeExpressReceiptMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_EXPRESS_RECEIPT-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_QZOK_ORG_ID" jdbcType="VARCHAR" property="fQzokOrgId" />
    <result column="F_QZOK_YSDDH" jdbcType="VARCHAR" property="fQzokYsddh" />
    <result column="F_QZOK_GSYWDH" jdbcType="VARCHAR" property="fQzokGsywdh" />
    <result column="F_QZOK_CRKDH" jdbcType="VARCHAR" property="fQzokCrkdh" />
    <result column="F_QZOK_KDH" jdbcType="VARCHAR" property="fQzokKdh" />
    <result column="F_QZOK_YWLX" jdbcType="VARCHAR" property="fQzokYwlx" />
    <result column="F_QZOK_QSSJ" jdbcType="VARCHAR" property="fQzokQssj" />
    <result column="F_QZOK_WLGS" jdbcType="VARCHAR" property="fQzokWlgs" />
    <result column="F_QZOK_WLBM" jdbcType="VARCHAR" property="fQzokWlbm" />
    <result column="F_QZOK_XLH" jdbcType="VARCHAR" property="fQzokXlh" />
    <result column="F_QZOK_PCH" jdbcType="VARCHAR" property="fQzokPch" />
    <result column="F_QZOK_FHSL" jdbcType="DECIMAL" property="fQzokFhsl" />
    <result column="F_QZOK_SJR" jdbcType="VARCHAR" property="fQzokSjr" />
    <result column="F_QZOK_DH" jdbcType="VARCHAR" property="fQzokDh" />
    <result column="F_QZOK_DZ" jdbcType="VARCHAR" property="fQzokDz" />
    <result column="F_QZOK_BDDJBH" jdbcType="VARCHAR" property="fQzokBddjbh" />
    <result column="F_QZOK_SFSC" jdbcType="VARCHAR" property="fQzokSfsc" />
    <result column="F_QZOK_SFJRCB" jdbcType="VARCHAR" property="fQzokSfjrcb" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FID, F_BILL_NO, F_QZOK_ORG_ID, F_QZOK_YSDDH, F_QZOK_GSYWDH, F_QZOK_CRKDH, F_QZOK_KDH, 
    F_QZOK_YWLX, F_QZOK_QSSJ, F_QZOK_WLGS, F_QZOK_WLBM, F_QZOK_XLH, F_QZOK_PCH, F_QZOK_FHSL, 
    F_QZOK_SJR, F_QZOK_DH, F_QZOK_DZ, F_QZOK_BDDJBH, F_QZOK_SFSC, F_QZOK_SFJRCB, ADD_TIME, 
    MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_EXPRESS_RECEIPT
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_EXPRESS_RECEIPT
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_EXPRESS_RECEIPT (FID, F_BILL_NO, F_QZOK_ORG_ID, 
      F_QZOK_YSDDH, F_QZOK_GSYWDH, F_QZOK_CRKDH, 
      F_QZOK_KDH, F_QZOK_YWLX, F_QZOK_QSSJ, 
      F_QZOK_WLGS, F_QZOK_WLBM, F_QZOK_XLH, 
      F_QZOK_PCH, F_QZOK_FHSL, F_QZOK_SJR, 
      F_QZOK_DH, F_QZOK_DZ, F_QZOK_BDDJBH, 
      F_QZOK_SFSC, F_QZOK_SFJRCB, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME)
    values (#{fid,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fQzokOrgId,jdbcType=VARCHAR}, 
      #{fQzokYsddh,jdbcType=VARCHAR}, #{fQzokGsywdh,jdbcType=VARCHAR}, #{fQzokCrkdh,jdbcType=VARCHAR}, 
      #{fQzokKdh,jdbcType=VARCHAR}, #{fQzokYwlx,jdbcType=VARCHAR}, #{fQzokQssj,jdbcType=VARCHAR}, 
      #{fQzokWlgs,jdbcType=VARCHAR}, #{fQzokWlbm,jdbcType=VARCHAR}, #{fQzokXlh,jdbcType=VARCHAR}, 
      #{fQzokPch,jdbcType=VARCHAR}, #{fQzokFhsl,jdbcType=DECIMAL}, #{fQzokSjr,jdbcType=VARCHAR}, 
      #{fQzokDh,jdbcType=VARCHAR}, #{fQzokDz,jdbcType=VARCHAR}, #{fQzokBddjbh,jdbcType=VARCHAR}, 
      #{fQzokSfsc,jdbcType=VARCHAR}, #{fQzokSfjrcb,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_EXPRESS_RECEIPT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fid != null and fid != ''">
        FID,
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO,
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        F_QZOK_ORG_ID,
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        F_QZOK_YSDDH,
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        F_QZOK_GSYWDH,
      </if>
      <if test="fQzokCrkdh != null and fQzokCrkdh != ''">
        F_QZOK_CRKDH,
      </if>
      <if test="fQzokKdh != null and fQzokKdh != ''">
        F_QZOK_KDH,
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        F_QZOK_YWLX,
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        F_QZOK_QSSJ,
      </if>
      <if test="fQzokWlgs != null and fQzokWlgs != ''">
        F_QZOK_WLGS,
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        F_QZOK_WLBM,
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        F_QZOK_XLH,
      </if>
      <if test="fQzokPch != null and fQzokPch != ''">
        F_QZOK_PCH,
      </if>
      <if test="fQzokFhsl != null">
        F_QZOK_FHSL,
      </if>
      <if test="fQzokSjr != null and fQzokSjr != ''">
        F_QZOK_SJR,
      </if>
      <if test="fQzokDh != null and fQzokDh != ''">
        F_QZOK_DH,
      </if>
      <if test="fQzokDz != null and fQzokDz != ''">
        F_QZOK_DZ,
      </if>
      <if test="fQzokBddjbh != null and fQzokBddjbh != ''">
        F_QZOK_BDDJBH,
      </if>
      <if test="fQzokSfsc != null and fQzokSfsc != ''">
        F_QZOK_SFSC,
      </if>
      <if test="fQzokSfjrcb != null and fQzokSfjrcb != ''">
        F_QZOK_SFJRCB,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fid != null and fid != ''">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCrkdh != null and fQzokCrkdh != ''">
        #{fQzokCrkdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokKdh != null and fQzokKdh != ''">
        #{fQzokKdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        #{fQzokQssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlgs != null and fQzokWlgs != ''">
        #{fQzokWlgs,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokPch != null and fQzokPch != ''">
        #{fQzokPch,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFhsl != null">
        #{fQzokFhsl,jdbcType=DECIMAL},
      </if>
      <if test="fQzokSjr != null and fQzokSjr != ''">
        #{fQzokSjr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDh != null and fQzokDh != ''">
        #{fQzokDh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDz != null and fQzokDz != ''">
        #{fQzokDz,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjbh != null and fQzokBddjbh != ''">
        #{fQzokBddjbh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfsc != null and fQzokSfsc != ''">
        #{fQzokSfsc,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfjrcb != null and fQzokSfjrcb != ''">
        #{fQzokSfjrcb,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity">
    <!--@mbg.generated-->
    update KING_DEE_EXPRESS_RECEIPT
    <set>
      <if test="fid != null and fid != ''">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCrkdh != null and fQzokCrkdh != ''">
        F_QZOK_CRKDH = #{fQzokCrkdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokKdh != null and fQzokKdh != ''">
        F_QZOK_KDH = #{fQzokKdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        F_QZOK_QSSJ = #{fQzokQssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlgs != null and fQzokWlgs != ''">
        F_QZOK_WLGS = #{fQzokWlgs,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokPch != null and fQzokPch != ''">
        F_QZOK_PCH = #{fQzokPch,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFhsl != null">
        F_QZOK_FHSL = #{fQzokFhsl,jdbcType=DECIMAL},
      </if>
      <if test="fQzokSjr != null and fQzokSjr != ''">
        F_QZOK_SJR = #{fQzokSjr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDh != null and fQzokDh != ''">
        F_QZOK_DH = #{fQzokDh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDz != null and fQzokDz != ''">
        F_QZOK_DZ = #{fQzokDz,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjbh != null and fQzokBddjbh != ''">
        F_QZOK_BDDJBH = #{fQzokBddjbh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfsc != null and fQzokSfsc != ''">
        F_QZOK_SFSC = #{fQzokSfsc,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfjrcb != null and fQzokSfjrcb != ''">
        F_QZOK_SFJRCB = #{fQzokSfjrcb,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity">
    <!--@mbg.generated-->
    update KING_DEE_EXPRESS_RECEIPT
    set FID = #{fid,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      F_QZOK_CRKDH = #{fQzokCrkdh,jdbcType=VARCHAR},
      F_QZOK_KDH = #{fQzokKdh,jdbcType=VARCHAR},
      F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      F_QZOK_QSSJ = #{fQzokQssj,jdbcType=VARCHAR},
      F_QZOK_WLGS = #{fQzokWlgs,jdbcType=VARCHAR},
      F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      F_QZOK_PCH = #{fQzokPch,jdbcType=VARCHAR},
      F_QZOK_FHSL = #{fQzokFhsl,jdbcType=DECIMAL},
      F_QZOK_SJR = #{fQzokSjr,jdbcType=VARCHAR},
      F_QZOK_DH = #{fQzokDh,jdbcType=VARCHAR},
      F_QZOK_DZ = #{fQzokDz,jdbcType=VARCHAR},
      F_QZOK_BDDJBH = #{fQzokBddjbh,jdbcType=VARCHAR},
      F_QZOK_SFSC = #{fQzokSfsc,jdbcType=VARCHAR},
      F_QZOK_SFJRCB = #{fQzokSfjrcb,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_EXPRESS_RECEIPT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokOrgId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSDDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYsddh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_GSYWDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokGsywdh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_CRKDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokCrkdh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_KDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokKdh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YWLX = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYwlx,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_QSSJ = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokQssj,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_WLGS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokWlgs,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_WLBM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokWlbm,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_XLH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokXlh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_PCH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokPch,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_FHSL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokFhsl,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_SJR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokSjr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_DH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokDh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_DZ = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokDz,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJBH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokBddjbh,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_SFSC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokSfsc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="F_QZOK_SFJRCB = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokSfjrcb,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_EXPRESS_RECEIPT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillNo != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YSDDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokYsddh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYsddh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_GSYWDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokGsywdh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokGsywdh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_CRKDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokCrkdh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokCrkdh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_KDH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokKdh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokKdh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_YWLX = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokYwlx != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokYwlx,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_QSSJ = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokQssj != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokQssj,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_WLGS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokWlgs != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokWlgs,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_WLBM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokWlbm != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokWlbm,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_XLH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokXlh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokXlh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_PCH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokPch != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokPch,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_FHSL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokFhsl != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokFhsl,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_SJR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokSjr != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokSjr,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_DH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokDh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokDh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_DZ = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokDz != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokDz,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJBH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokBddjbh != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokBddjbh,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_SFSC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokSfsc != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokSfsc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_SFJRCB = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokSfjrcb != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokSfjrcb,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_EXPRESS_RECEIPT
    (FID, F_BILL_NO, F_QZOK_ORG_ID, F_QZOK_YSDDH, F_QZOK_GSYWDH, F_QZOK_CRKDH, F_QZOK_KDH, 
      F_QZOK_YWLX, F_QZOK_QSSJ, F_QZOK_WLGS, F_QZOK_WLBM, F_QZOK_XLH, F_QZOK_PCH, F_QZOK_FHSL, 
      F_QZOK_SJR, F_QZOK_DH, F_QZOK_DZ, F_QZOK_BDDJBH, F_QZOK_SFSC, F_QZOK_SFJRCB, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fid,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, #{item.fQzokOrgId,jdbcType=VARCHAR}, 
        #{item.fQzokYsddh,jdbcType=VARCHAR}, #{item.fQzokGsywdh,jdbcType=VARCHAR}, #{item.fQzokCrkdh,jdbcType=VARCHAR}, 
        #{item.fQzokKdh,jdbcType=VARCHAR}, #{item.fQzokYwlx,jdbcType=VARCHAR}, #{item.fQzokQssj,jdbcType=VARCHAR}, 
        #{item.fQzokWlgs,jdbcType=VARCHAR}, #{item.fQzokWlbm,jdbcType=VARCHAR}, #{item.fQzokXlh,jdbcType=VARCHAR}, 
        #{item.fQzokPch,jdbcType=VARCHAR}, #{item.fQzokFhsl,jdbcType=DECIMAL}, #{item.fQzokSjr,jdbcType=VARCHAR}, 
        #{item.fQzokDh,jdbcType=VARCHAR}, #{item.fQzokDz,jdbcType=VARCHAR}, #{item.fQzokBddjbh,jdbcType=VARCHAR}, 
        #{item.fQzokSfsc,jdbcType=VARCHAR}, #{item.fQzokSfjrcb,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_EXPRESS_RECEIPT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      FID,
      F_BILL_NO,
      F_QZOK_ORG_ID,
      F_QZOK_YSDDH,
      F_QZOK_GSYWDH,
      F_QZOK_CRKDH,
      F_QZOK_KDH,
      F_QZOK_YWLX,
      F_QZOK_QSSJ,
      F_QZOK_WLGS,
      F_QZOK_WLBM,
      F_QZOK_XLH,
      F_QZOK_PCH,
      F_QZOK_FHSL,
      F_QZOK_SJR,
      F_QZOK_DH,
      F_QZOK_DZ,
      F_QZOK_BDDJBH,
      F_QZOK_SFSC,
      F_QZOK_SFJRCB,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{fid,jdbcType=VARCHAR},
      #{fBillNo,jdbcType=VARCHAR},
      #{fQzokOrgId,jdbcType=VARCHAR},
      #{fQzokYsddh,jdbcType=VARCHAR},
      #{fQzokGsywdh,jdbcType=VARCHAR},
      #{fQzokCrkdh,jdbcType=VARCHAR},
      #{fQzokKdh,jdbcType=VARCHAR},
      #{fQzokYwlx,jdbcType=VARCHAR},
      #{fQzokQssj,jdbcType=VARCHAR},
      #{fQzokWlgs,jdbcType=VARCHAR},
      #{fQzokWlbm,jdbcType=VARCHAR},
      #{fQzokXlh,jdbcType=VARCHAR},
      #{fQzokPch,jdbcType=VARCHAR},
      #{fQzokFhsl,jdbcType=DECIMAL},
      #{fQzokSjr,jdbcType=VARCHAR},
      #{fQzokDh,jdbcType=VARCHAR},
      #{fQzokDz,jdbcType=VARCHAR},
      #{fQzokBddjbh,jdbcType=VARCHAR},
      #{fQzokSfsc,jdbcType=VARCHAR},
      #{fQzokSfjrcb,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=INTEGER},
      </if>
      FID = #{fid,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      F_QZOK_CRKDH = #{fQzokCrkdh,jdbcType=VARCHAR},
      F_QZOK_KDH = #{fQzokKdh,jdbcType=VARCHAR},
      F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      F_QZOK_QSSJ = #{fQzokQssj,jdbcType=VARCHAR},
      F_QZOK_WLGS = #{fQzokWlgs,jdbcType=VARCHAR},
      F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      F_QZOK_PCH = #{fQzokPch,jdbcType=VARCHAR},
      F_QZOK_FHSL = #{fQzokFhsl,jdbcType=DECIMAL},
      F_QZOK_SJR = #{fQzokSjr,jdbcType=VARCHAR},
      F_QZOK_DH = #{fQzokDh,jdbcType=VARCHAR},
      F_QZOK_DZ = #{fQzokDz,jdbcType=VARCHAR},
      F_QZOK_BDDJBH = #{fQzokBddjbh,jdbcType=VARCHAR},
      F_QZOK_SFSC = #{fQzokSfsc,jdbcType=VARCHAR},
      F_QZOK_SFJRCB = #{fQzokSfjrcb,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_EXPRESS_RECEIPT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="fid != null and fid != ''">
        FID,
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO,
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        F_QZOK_ORG_ID,
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        F_QZOK_YSDDH,
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        F_QZOK_GSYWDH,
      </if>
      <if test="fQzokCrkdh != null and fQzokCrkdh != ''">
        F_QZOK_CRKDH,
      </if>
      <if test="fQzokKdh != null and fQzokKdh != ''">
        F_QZOK_KDH,
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        F_QZOK_YWLX,
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        F_QZOK_QSSJ,
      </if>
      <if test="fQzokWlgs != null and fQzokWlgs != ''">
        F_QZOK_WLGS,
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        F_QZOK_WLBM,
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        F_QZOK_XLH,
      </if>
      <if test="fQzokPch != null and fQzokPch != ''">
        F_QZOK_PCH,
      </if>
      <if test="fQzokFhsl != null">
        F_QZOK_FHSL,
      </if>
      <if test="fQzokSjr != null and fQzokSjr != ''">
        F_QZOK_SJR,
      </if>
      <if test="fQzokDh != null and fQzokDh != ''">
        F_QZOK_DH,
      </if>
      <if test="fQzokDz != null and fQzokDz != ''">
        F_QZOK_DZ,
      </if>
      <if test="fQzokBddjbh != null and fQzokBddjbh != ''">
        F_QZOK_BDDJBH,
      </if>
      <if test="fQzokSfsc != null and fQzokSfsc != ''">
        F_QZOK_SFSC,
      </if>
      <if test="fQzokSfjrcb != null and fQzokSfjrcb != ''">
        F_QZOK_SFJRCB,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="fid != null and fid != ''">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCrkdh != null and fQzokCrkdh != ''">
        #{fQzokCrkdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokKdh != null and fQzokKdh != ''">
        #{fQzokKdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        #{fQzokQssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlgs != null and fQzokWlgs != ''">
        #{fQzokWlgs,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokPch != null and fQzokPch != ''">
        #{fQzokPch,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFhsl != null">
        #{fQzokFhsl,jdbcType=DECIMAL},
      </if>
      <if test="fQzokSjr != null and fQzokSjr != ''">
        #{fQzokSjr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDh != null and fQzokDh != ''">
        #{fQzokDh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDz != null and fQzokDz != ''">
        #{fQzokDz,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjbh != null and fQzokBddjbh != ''">
        #{fQzokBddjbh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfsc != null and fQzokSfsc != ''">
        #{fQzokSfsc,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfjrcb != null and fQzokSfjrcb != ''">
        #{fQzokSfjrcb,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=INTEGER},
      </if>
      <if test="fid != null and fid != ''">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokOrgId != null and fQzokOrgId != ''">
        F_QZOK_ORG_ID = #{fQzokOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYsddh != null and fQzokYsddh != ''">
        F_QZOK_YSDDH = #{fQzokYsddh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsywdh != null and fQzokGsywdh != ''">
        F_QZOK_GSYWDH = #{fQzokGsywdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCrkdh != null and fQzokCrkdh != ''">
        F_QZOK_CRKDH = #{fQzokCrkdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokKdh != null and fQzokKdh != ''">
        F_QZOK_KDH = #{fQzokKdh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYwlx != null and fQzokYwlx != ''">
        F_QZOK_YWLX = #{fQzokYwlx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokQssj != null and fQzokQssj != ''">
        F_QZOK_QSSJ = #{fQzokQssj,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlgs != null and fQzokWlgs != ''">
        F_QZOK_WLGS = #{fQzokWlgs,jdbcType=VARCHAR},
      </if>
      <if test="fQzokWlbm != null and fQzokWlbm != ''">
        F_QZOK_WLBM = #{fQzokWlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokXlh != null and fQzokXlh != ''">
        F_QZOK_XLH = #{fQzokXlh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokPch != null and fQzokPch != ''">
        F_QZOK_PCH = #{fQzokPch,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFhsl != null">
        F_QZOK_FHSL = #{fQzokFhsl,jdbcType=DECIMAL},
      </if>
      <if test="fQzokSjr != null and fQzokSjr != ''">
        F_QZOK_SJR = #{fQzokSjr,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDh != null and fQzokDh != ''">
        F_QZOK_DH = #{fQzokDh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDz != null and fQzokDz != ''">
        F_QZOK_DZ = #{fQzokDz,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjbh != null and fQzokBddjbh != ''">
        F_QZOK_BDDJBH = #{fQzokBddjbh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfsc != null and fQzokSfsc != ''">
        F_QZOK_SFSC = #{fQzokSfsc,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSfjrcb != null and fQzokSfjrcb != ''">
        F_QZOK_SFJRCB = #{fQzokSfjrcb,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>