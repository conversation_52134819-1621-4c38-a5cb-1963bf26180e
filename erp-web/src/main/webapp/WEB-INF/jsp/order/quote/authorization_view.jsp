<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags" %>
<c:set var="title" value="审核授权书记录" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<link rel="stylesheet" href="<%=basePath%>static/css/authorization_print_view.css?rnd=${resourceVersionKey}"/>
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/font/font.css"/>
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/lib/pikaday.css?rnd=${resourceVersionKey}"/>
<script type="text/javascript"
        src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>


<style>
    .form-submit {
        border: 1px solid #000; /* 添加边框样式 */
        padding: 20px; /* 添加内边距以便内容不紧贴边框 */
        margin-top: 40px;
        margin-bottom: 20px; /* 添加底部外边距以便与下方内容区分 */
        margin-left: 30px;
    }

    .wrapper {
        padding: 20px;
        width: 800px;
        margin: 0 auto;
    }

    .wrapper .form-item {
        margin-top: 10px;
        padding: 7px 0;
        display: flex;
    }

    .wrapper .form-item .item-label {
        width: 120px;
        text-align: right;
        margin-right: 10px;
    }

    .button-link {
        display: inline-block;
        padding: 2px 10px;
        font-size: 14px;
        color: #fff;
        text-align: center;
        text-decoration: none;
        border: none;
        border-radius: 2px;
        cursor: pointer;
    }

    .button-link:hover {
        text-decoration: none;
        outline-style: none;
        color: #fff;
        cursor: pointer;
    }

    .button-link:visited {
        text-decoration: none;
        outline-style: none;
        color: #fff;
        cursor: pointer;
    }

    .button-link:focus {
        text-decoration: none;
        outline-style: none;
        color: #fff;
        cursor: pointer;
    }
</style>


<div class="print-tab">
    <c:forEach var="authorizationApplie" items="${authorizationApplies}">

        <c:choose>
            <c:when test="${authorizationApplie.authorizationApplyId eq authorizationApply.authorizationApplyId}">
                <a href="<%= basePath %>/order/quote/authorizationView.do?quoteorderId=${authorizationApplie.quoteorderId}&authorizationApplyId=${authorizationApplie.authorizationApplyId}"
                   class="tab-item" style="height: 80px;background-color: #D7D7D7">

                    <div class="item-num" style="height: 30px">${authorizationApplie.authorizationApplyNum}</div>
                    <div class="item-status status-red" style="height: 30px">
                        <c:if test="${authorizationApplie.applyStatus eq '1'}">审核中</c:if>
                        <c:if test="${authorizationApplie.applyStatus eq '2'}">驳回</c:if>
                        <c:if test="${authorizationApplie.applyStatus eq '3'}">审核通过</c:if>
                        <c:if test="${authorizationApplie.applyStatus eq '4'}">已取消</c:if>
                        <c:if test="${authorizationApplie.applyStatus eq '5'}">已作废</c:if>
                    </div>
                </a>
            </c:when>
            <c:otherwise>


                <a href="<%= basePath %>/order/quote/authorizationView.do?quoteorderId=${authorizationApplie.quoteorderId}&authorizationApplyId=${authorizationApplie.authorizationApplyId}"
                   class="tab-item" style="height: 60px">

                    <div class="item-num">${authorizationApplie.authorizationApplyNum}</div>
                    <div class="item-status status-red">
                        <c:if test="${authorizationApplie.applyStatus eq '1'}">审核中</c:if>
                        <c:if test="${authorizationApplie.applyStatus eq '2'}">驳回</c:if>
                        <c:if test="${authorizationApplie.applyStatus eq '3'}">审核通过</c:if>
                        <c:if test="${authorizationApplie.applyStatus eq '4'}">已取消</c:if>
                        <c:if test="${authorizationApplie.applyStatus eq '5'}">已作废</c:if>
                    </div>
                </a>

            </c:otherwise>
        </c:choose>
    </c:forEach>

</div>

<form method="post" action="<%= basePath %>/order/quote/authorizationApply.do" id="tjform">
    <input type="hidden" value="${quoteorderId}" name="quoteorderId">
    <input type="hidden" value="${skuId}" name="skuId" id="skuId">

    <div>
        <div class="wrapper">
            <tags:authorization_common authorizationApply="${authorizationApply}"/>

            <div class="form-submit">
                <c:if test="${not empty authorizationApply.authType}">
                <div class="form-item">
                    <div class="item-label">授权类型：</div>
                    <div class="item-fields">
                    <span class="strong">
                        <c:if test="${not empty authorizationApply.authType and authorizationApply.authType eq 0}">项目授权</c:if>
                        <c:if test="${not empty authorizationApply.authType and authorizationApply.authType eq 1}">经销授权</c:if>
                    </span>
                    </div>
                </div>
                </c:if>
                <div class="form-item">
                    <div class="item-label">授权书模板：</div>
                    <div class="item-fields">
                <span class="strong">
                    <c:if test="${authorizationApply.standardTemplate eq 0}">标准模板</c:if>
                    <c:if test="${authorizationApply.standardTemplate eq 1}">非标准模板</c:if>
                </span>
                    </div>
                </div>

                <c:if test="${authorizationApply.standardTemplate eq 1}">
                <div class="form-item">
                    <div class="item-label">非标授权书附件：</div>
                    <div class="item-fields">
                            <span style="color: #ff0000">
                             <c:set var="nonStandardNoUrl" value="${fn:replace(authorizationApply.nonStandardAuthorizationUrl, 'display', 'download')}" />
                            <a href="http://${domain}${nonStandardNoUrl}"
                               target="_blank">${authorizationApply.nonStandardAuthorizationName}</a>
                            </span>
                            <c:if test="${authorizationApply.whetherSign eq 0 && !empty authorizationApply.nonStandardAuthorizationUrl}">
                                <span style="color: #FF0000FF;font-size: 12px">文件无法识别自动盖电子章，该申请需在授权专员审批后联系财务盖鲜章</span>
                            </c:if>
                    </div>
                </div>
                </c:if>

                <div class="form-item">
                    <div class="item-label">其他附件：</div>
                    <div class="item-fields">
                        <ul>
                            <c:forEach var="attachment" items="${attachmentList}">
                                <li>
                            <span style="color: #1E9FFF">
                            <a href="http://${attachment.domain}${attachment.uri}"
                               target="_blank">${attachment.name}</a>
                            </span>
                                </li>
                            </c:forEach>
                        </ul>
                    </div>
                </div>

                <div class="form-item">
                    <div class="item-label">事项描述：</div>
                    <div class="item-fields">
                <span class="strong">
                    ${authorizationApply.described}
                </span>
                    </div>
                </div>

                <div class="form-item">
                    <div class="item-label">公章类型：</div>
                    <div class="item-fields"><span class="txt-blue txt-lg">公章</span> <span class="txt-blue">
                        <%--<c:choose>
                            <c:when test="${authorizationApply.sealType eq 2}">南京医购优选供应链管理有限公司</c:when>
                            <c:otherwise>南京贝登医疗股份有限公司</c:otherwise>
                        </c:choose>--%>
                        <c:forEach items="${companyInfoList}" var="company">
                            <c:if test="${company.frontEndSeq eq authorizationApply.sealType}">
                                <c:set var="companyName" value="${company.companyName}" />
                            </c:if>
                        </c:forEach>
                        <c:if test="${empty companyName}">南京贝登医疗股份有限公司</c:if>
                        <c:if test="${not empty companyName}">${companyName}</c:if>
                    </span>
                    </div>
                </div>

                <div class="form-item">
                    <div class="item-label">份数：</div>
                    <div class="item-fields">
                <span class="strong">
                ${authorizationApply.num} &nbsp;&nbsp;份
                </span>
                    </div>
                </div>


                <div class="add-tijiao pb15">
                    <c:if test="${authorizationApply.applyStatus eq 1 || authorizationApply.applyStatus eq 2}">
                        <%-- 发起人 --%>
                        <c:if test="${whetherApplyUser}">
                            <button type="button" class="btn btn-blue btn-large mr10 pop-new-data"
                                    style="background-color: #1E9FFF"
                                    layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./chApply.do?authorizationApplyId=${authorizationApply.authorizationApplyId}"}'>
                                撤回申请
                            </button>
                        </c:if>
                    </c:if>

                    <c:if test="${authorizationApply.nonStandardAuthorizationUrl ne null and authorizationApply.nonStandardAuthorizationUrl ne ''}">
                        <c:if test="${authorizationApply.applyStatus eq 3}">
                            <c:set var="modifiedNoUrl" value="${fn:replace(authorizationApply.nonStandardAuthorizationUrl, 'display', 'download')}" />
                            <a class="button-link" style="background-color: #3384EF" href="http://${domain}${modifiedNoUrl}"
                               target="_blank">打印无章原件</a>
                        </c:if>
                    </c:if>

                    <%--  盖章件下载 --%>
                    <c:if test="${authorizationApply.nonStandardAuthorizationSignUrl ne null and authorizationApply.nonStandardAuthorizationSignUrl ne ''}">
                        <c:set var="modifiedUrl" value="${fn:replace(authorizationApply.nonStandardAuthorizationSignUrl, 'display', 'download')}" />
                        <a class="button-link"  style="background-color: #FF0000FF" href="${modifiedUrl}">盖章件下载</a>
                    </c:if>

                </div>

                <%-- 当前审批人 --%>
                <c:if test="${!empty historicInfo.taskInfo and candidateUserMap['belong']}">
                    <div style="width: 590px;margin: 0 auto">
                        审批意见：
                    </div>
                    <div style="width: 590px;margin: 10px auto">
                        <textarea style="width: 600px;height: 80px" id="suggestion" maxlength="200"></textarea>
                    </div>
                    <div class="add-tijiao pb15">
                        <button type="button" class="btn btn-blue btn-large" style="background-color: #1E9FFF"
                                onclick="tg(${authorizationApply.authorizationApplyId},'${formToken}','${endSt}','${authorizationApply.skuId}')">
                            同意
                        </button>

                        <button type="button" class="btn btn-blue btn-large" style="background-color: #1E9FFF"
                                onclick="bh(${authorizationApply.authorizationApplyId},'${formToken}','${endSt}','${authorizationApply.skuId}');">
                            驳回
                        </button>
                    </div>
                </c:if>

                <div class="parts tcenter" style="width: 700px;text-align: center;margin: 0 auto">
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">审核记录</div>
                    </div>
                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th style="width:50px">操作人</th>
                            <th style="width:80px">操作时间</th>
                            <th style="width:80px">操作事项</th>
                            <th style="width:80px">备注</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                            <c:if test="${not empty  hi.activityName}">
                                <tr>
                                    <td>
                                        <c:choose>
                                            <c:when test="${hi.activityType == 'startEvent'}">
                                                ${startUser}
                                            </c:when>
                                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${historicActivityInstance.size() == status.count}">
                                                    ${verifyUsers}
                                                </c:if>
                                                <c:if test="${historicActivityInstance.size() != status.count}">
                                                    ${hi.assignee}
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${hi.activityType == 'startEvent'}">
                                                开始
                                            </c:when>
                                            <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                结束
                                            </c:when>
                                            <c:otherwise>
                                                ${hi.activityName}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td class="font-red">${commentMap[hi.taskId]}</td>
                                </tr>
                                </tr>
                            </c:if>
                        </c:forEach>
                        <c:if test="${authorizationApply.applyStatus eq '4'}">
                            <tr>
                            <tr>
                                <td>${userName}</td>
                                <td><date:date value="${authorizationApply.modTime}"/></td>
                                <td>已取消</td>
                                <td><span style="color: red">${comment}</span></td>
                            </tr>
                        </c:if>
                        <c:if test="${authorizationApply.applyStatus eq '5'}">
                            <td>${userName}</td>
                            <td><date:date value="${authorizationApply.modTime}"/></td>
                            <td>已废弃</td>
                            <td>
                                <span style="color: red">${comment}</span>
                                <c:if test="${!empty attachList}">
                                    <c:forEach var="attach" items="${attachList}">
                            <span style="color: #1E9FFF">
                            <a href="http://${attach.domain}${attach.uri}" target="_blank">${attach.name}</a>
                            </span>
                                    </c:forEach>
                                </c:if>
                            </td>
                        </c:if>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</form>
<script type="text/javascript">
    $("#btnPrint").click(function () {
        $("#btnPrint").hide();
        if (window.ActiveXObject || "ActiveXObject" in window) {
            $("#print-contract").printArea({
                mode: 'popup'
            });
        } else {
            $("#print-contract").printArea();
        }
        $("#btnPrint").show();
    });
</script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/quote/add_communicate.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/quote/authorization_view.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/quote/examineAuthorization.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp" %>
