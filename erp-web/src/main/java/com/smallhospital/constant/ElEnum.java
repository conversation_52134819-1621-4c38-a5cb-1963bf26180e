package com.smallhospital.constant;


/**
 * <AUTHOR>
 * @description:
 * @date 2020/12/12 15:23
 */
public enum ElEnum {

    CERTIFICATE("营业执照", 8),
    LICENSE("三类资质（经营许可证)",12);

    String license;

    Integer type;

    ElEnum(String license, Integer type) {
        this.license = license;
        this.type = type;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}
