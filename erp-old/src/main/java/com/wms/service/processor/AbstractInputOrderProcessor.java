package com.wms.service.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.logistics.dao.BarcodeMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInItemMapper;
import com.vedeng.logistics.eums.WarehouseOutInSourceEnum;
import com.vedeng.logistics.model.Barcode;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.service.*;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.vedeng.util.HashUtils;
import com.wms.constant.LogicalEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import org.apache.commons.collections.CollectionUtils;
import org.jfree.util.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 抽象的入库单处理器
 */
@Service
public abstract class AbstractInputOrderProcessor extends AbstractInputOrOutputProcessor<InputOrderDto>{

    private Logger LOGGER= LoggerFactory.getLogger(AbstractOutputOrderProcessor.class);

    @Autowired
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;
    @Autowired
    private WarehouseGoodsOutService warehouseGoodsOutService;

    @Resource
    private BarcodeMapper barcodeMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private WarehouseGoodsOperateLogService warehouseGoodsOperateLogService;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private WarehousesService warehousesService;

    @Autowired
    private WarehouseGoodsInService warehouseGoodsInService;

    @Autowired
    private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

    protected String getBusinessKey(InputOrderDto inputOrderDto){
        return inputOrderDto.getASNNO();
    }

    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    /**
     * 新增或者修改逻辑订单数据 (考虑到采购单同一个商品有多次收货的可能 加了update)
     * @param orderDto
     */
    @Override
    protected void insertOrUpdateLogicalOrderGoods(InputOrderDto orderDto) throws Exception{

        orderDto.getDetails().stream().forEach(goodsDto -> {

            int relateId = getRelateId(orderDto,goodsDto);
            if(relateId == 0){
                throw new RuntimeException("入库处理器 关联Relateid为空,单号"+orderDto.getASNReference1()+"goodsInfo:"+goodsDto.toString());
            }

            WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
            wmsLogicalOrdergoods.setSku(goodsDto.getSKU());
            wmsLogicalOrdergoods.setRelatedId(relateId);
            wmsLogicalOrdergoods.setOperateType(getWmsLogicalOperateType(orderDto));
            wmsLogicalOrdergoods.setLogicalWarehouseId(Integer.valueOf(LogicalEnum.getLogicalWarehouseIdByCode(goodsDto.getLotAtt08())));

            WmsLogicalOrdergoods dbLogicalOrdergoods = wmsLogicalOrdergoodsMapper.getOrderByCon(wmsLogicalOrdergoods);

            if(dbLogicalOrdergoods == null){

                wmsLogicalOrdergoods.setSku(goodsDto.getSKU());
                wmsLogicalOrdergoods.setGoodsId(Integer.valueOf(goodsDto.getSKU().substring(1)));
                wmsLogicalOrdergoods.setNum(goodsDto.getReceivedQty().intValue());
                wmsLogicalOrdergoods.setArrivalNum(goodsDto.getReceivedQty().intValue());
                wmsLogicalOrdergoods.setAddTime(new Date());
                wmsLogicalOrdergoods.setModeTime(new Date());

                LOGGER.info("单据入库，新增逻辑订单数据:" + JSON.toJSONString(wmsLogicalOrdergoods));
                wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);

            }else{

                WmsLogicalOrdergoods updateLogicalOrdergoods = new WmsLogicalOrdergoods();
                updateLogicalOrdergoods.setLogicalOrderGoodsId(dbLogicalOrdergoods.getLogicalOrderGoodsId());
                updateLogicalOrdergoods.setNum(dbLogicalOrdergoods.getNum() + goodsDto.getReceivedQty().intValue());
                updateLogicalOrdergoods.setArrivalNum(dbLogicalOrdergoods.getArrivalNum() + goodsDto.getReceivedQty().intValue());
                updateLogicalOrdergoods.setModeTime(new Date());

                LOGGER.info("单据入库，更新逻辑订单数据:" + JSON.toJSONString(updateLogicalOrdergoods));
                wmsLogicalOrdergoodsMapper.updateByPrimaryKeySelective(updateLogicalOrdergoods);
            }
        });
    }

    @Override
    protected void insertOrUpdateWarehouseLog(InputOrderDto orderDto) {
        long currentTimeMillis = System.currentTimeMillis();
        //出入库日志主表新增
        LOGGER.info("出入库日志主表新增：{}",JSON.toJSONString(orderDto));
        int operateType = getOperateType(orderDto);
        WarehouseGoodsOutIn warehouse=new WarehouseGoodsOutIn();
        warehouse.setWmsNo(orderDto.getASNNO());
        warehouse.setRelateNo(orderDto.getASNReference1());
        warehouse.setOutInType(operateType);
        warehouse.setSource(WarehouseOutInSourceEnum.NORMAL_DELIVERY.getSource());
        warehouse.setAddTime(new Date());
        warehouse.setModTime(new Date());
        warehouse.setOutInTime(new Date());
        logger.info("入库主表预处理数据：{}",JSON.toJSONString(warehouse));
        WarehouseGoodsOutIn warehouseGoodsOutIn=warehouseGoodsInService.insertWarehouseGoodsOutIn(warehouse);

        List<InputOrderGoodsDto> details = orderDto.getDetails();
        List<WarehouseGoodsOperateLog> insertList = new ArrayList<>();
        //入库明细表快照list
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = new ArrayList<>();
        details.stream().forEach(goodsDto ->{

            if(StringUtil.isBlank(goodsDto.getLotAtt11())){
                throw new RuntimeException(orderDto.getASNReference1()+"入库类型没有贝登批次码,消费失败");
            }
            //修改对应 入库记录
            WarehouseGoodsOperateLog queryCon = new WarehouseGoodsOperateLog();
            queryCon.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(goodsDto.getLotAtt08()));
            queryCon.setVedengBatchNumer(goodsDto.getLotAtt11());
            queryCon.setGoodsId(Integer.valueOf(goodsDto.getSKU().substring(1)));
            queryCon.setRelatedId(getRelateId(orderDto,goodsDto));
            List<WarehouseGoodsOperateLog> avalibleInputLogs = warehouseGoodsOperateLogMapper.getAvailableLogicalGoods(queryCon);

            //修改对应 入库明细快照记录
            WarehouseGoodsOutInItem queryConItem = new WarehouseGoodsOutInItem();
            queryConItem.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(goodsDto.getLotAtt08()));
            queryConItem.setVedengBatchNumber(goodsDto.getLotAtt11());
            queryConItem.setGoodsId(Integer.valueOf(goodsDto.getSKU().substring(1)));
            queryConItem.setRelatedId(getRelateId(orderDto,goodsDto));
            List<WarehouseGoodsOutInItem> avalibleInputItemList = warehouseGoodsOutInItemMapper.getAvailableLogicalGoods(queryConItem);

            //新增贝登条码
            Barcode barcode = new Barcode();
            WarehouseGoodsOperateLog warehouseGoodsOperateLog = new WarehouseGoodsOperateLog();
            //没有日志就新增
            if(CollectionUtils.isEmpty(avalibleInputLogs)){

                barcode.setBarcode(goodsDto.getLotAtt11());
                //采购
                barcode.setType(getBarcodeType(orderDto,goodsDto));
                barcode.setDetailGoodsId(getRelateId(orderDto,goodsDto));
                barcode.setGoodsId(Integer.valueOf(goodsDto.getSKU().substring(1)));
                //有效
                barcode.setIsEnable(1);
                barcode.setAddTime(currentTimeMillis);
                barcode.setModTime(currentTimeMillis);
                barcodeMapper.insertSelective(barcode);
                getwarehouseGoodsOperateLog(orderDto, goodsDto, barcode, warehouseGoodsOperateLog);
                //新增采购入库日志
                logger.info("新增采购入库日志，入参：{}",JSON.toJSONString(warehouseGoodsOperateLog));
                warehouseGoodsOperateLogService.insertSelective(warehouseGoodsOperateLog);
                insertList.add(warehouseGoodsOperateLog);
            }else{
                getwarehouseGoodsOperateLog(orderDto, goodsDto, barcode, warehouseGoodsOperateLog);
                //WarehouseGoodsOperateLog表更新
                logger.info("开始更新入库日志数据...");
                WarehouseGoodsOperateLog dbLog = avalibleInputLogs.get(0);
                WarehouseGoodsOperateLog updateLog = new WarehouseGoodsOperateLog();
                updateLog.setWarehouseGoodsOperateLogId(dbLog.getWarehouseGoodsOperateLogId());
                updateLog.setNum(dbLog.getNum() + goodsDto.getReceivedQty().intValue());
                updateLog.setLastStockNum(dbLog.getLastStockNum() + goodsDto.getReceivedQty().intValue());
                updateLog.setModTime(System.currentTimeMillis());
                logger.info("单据入库，修改入库日志数据:{}" + JSON.toJSONString(updateLog));
                warehouseGoodsOperateLogMapper.updateAvailableLogicalGood(updateLog);
            }

            //保存WarehouseGoodsOutInItem信息
            WarehouseGoodsOutInItem warehouseGoodsOutInItem = new WarehouseGoodsOutInItem();
            Date currentDate = new Date();
            warehouseGoodsOutInItem.setBarcodeId(barcode.getBarcodeId());
            warehouseGoodsOutInItem.setCompanyId(1);
            warehouseGoodsOutInItem.setOperateType(warehouseGoodsOutIn.getOutInType());

            warehouseGoodsOutInItem.setRelatedId(getRelateId(orderDto,goodsDto));
            warehouseGoodsOutInItem.setGoodsId(Integer.valueOf(goodsDto.getSKU().substring(1)));
            if(!"".equals(goodsDto.getLotAtt02())){
                warehouseGoodsOutInItem.setExpirationDate(goodsDto.getLotAtt02()+ " 00:00:00");
            }
            if(!"".equals(goodsDto.getLotAtt03())){
                warehouseGoodsOutInItem.setCheckStatusTime(goodsDto.getLotAtt03() + " 00:00:00");
            }
            if(!"".equals(goodsDto.getLotAtt01())){
                warehouseGoodsOutInItem.setProductDate(goodsDto.getLotAtt01() + " 00:00:00");
            }
            warehouseGoodsOutInItem.setAddTime(currentDate);
            warehouseGoodsOutInItem.setModTime(currentDate);
            warehouseGoodsOutInItem.setSterilizationBatchNumber(goodsDto.getLotAtt05());
            warehouseGoodsOutInItem.setBatchNumber("NO-LOT".equals(goodsDto.getLotAtt04()) ? "": goodsDto.getLotAtt04());
            warehouseGoodsOutInItem.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(goodsDto.getLotAtt08()));
            warehouseGoodsOutInItem.setVedengBatchNumber(goodsDto.getLotAtt11());
            warehouseGoodsOutInItem.setLastStockNum(Integer.valueOf(goodsDto.getReceivedQty().intValue()));
            warehouseGoodsOutInItem.setCreator(2);
            warehouseGoodsOutInItem.setUpdater(2);
            warehouseGoodsOutInItem.setLogType(0);
            warehouseGoodsOutInItem.setRemark(orderDto.getASNReference1());
            warehouseGoodsOutInItem.setDedicatedBuyorderNo(goodsDto.getLotAtt07());
            warehouseGoodsOutInItem.setOutInNo(warehouseGoodsOutIn.getOutInNo());
            warehouseGoodsOutInItem.setWmsNo(warehouseGoodsOutIn.getWmsNo());
            warehouseGoodsOutInItem.setCheckStatus(1);
            warehouseGoodsOutInItem.setIsExpress(0);
            CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(Integer.valueOf(goodsDto.getSKU().substring(1)));
            boolean getSn = false;
            if(Objects.nonNull(coreSku)) {
                //ERP出入库单号
                if(Boolean.TRUE.equals(coreSku.getIsFactorySnCode())) {
                    getSn = true;
                }
            }
            if (StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_IN.equals(operateType)||
                    StockOperateTypeConst.ORDER_WAREHOUSE_BACK_IN.equals(operateType)||
                    StockOperateTypeConst.WAREHOUSE_IN.equals(operateType)){
                warehouseGoodsOutInItem.setNum(goodsDto.getReceivedQty());
                warehouseGoodsOutInItem.setBarcodeFactory(goodsDto.getLotAtt12());
                logger.info("新增采购入库快照表，入参：{}",JSON.toJSONString(warehouseGoodsOutInItem));
                warehouseGoodsOutInItemList.addAll(saveWarehouseGoodsOutInItem(getRelateId(orderDto,goodsDto), operateType, warehouseGoodsOperateLog, warehouseGoodsOutIn,warehouseGoodsOutInItem));
            }else {
                if(getSn){
                    for (int size=0 ; size < goodsDto.getReceivedQty().intValue(); size++){
                        warehouseGoodsOutInItem.setNum(new BigDecimal(1));
                        warehouseGoodsOutInItem.setBarcodeFactory(goodsDto.getLotAtt12());
                        logger.info("新增采购入库快照表，入参：{}",JSON.toJSONString(warehouseGoodsOutInItem));
                        warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
                        warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);

                    }
                }else{
                    warehouseGoodsOutInItem.setNum(goodsDto.getReceivedQty());
                    logger.info("新增采购入库快照表，入参：{}",JSON.toJSONString(warehouseGoodsOutInItem));
                    //入库明细表快照list
                    warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
                    warehouseGoodsOutInItemList.add(warehouseGoodsOutInItem);
                }
            }

        });
        //入库验收报告
        try {
            if(CollectionUtils.isNotEmpty(warehouseGoodsOutInItemList)) {
                warehouseGoodsInService.createWarehouseGoodsInReport(warehouseGoodsOutIn, warehouseGoodsOutInItemList);
            }
        }catch(Exception e) {
            Log.error("增加入库，入库验收报告失败...",e);
        }

        if(CollectionUtils.isNotEmpty(insertList)){
            //保存采购信息至出入库日志表
            warehousesService.savePriceInfoToWareHouseLog(insertList);
        }
    }

    private void getwarehouseGoodsOperateLog(InputOrderDto orderDto, InputOrderGoodsDto goodsDto, Barcode barcode, WarehouseGoodsOperateLog warehouseGoodsOperateLog) {
        warehouseGoodsOperateLog.setBarcodeId(barcode.getBarcodeId());
        warehouseGoodsOperateLog.setCompanyId(1);
        warehouseGoodsOperateLog.setIsEnable(1);
        warehouseGoodsOperateLog.setOperateType(getOperateType(orderDto));
        warehouseGoodsOperateLog.setRelatedId(getRelateId(orderDto, goodsDto));
        warehouseGoodsOperateLog.setGoodsId(Integer.valueOf(goodsDto.getSKU().substring(1)));
        warehouseGoodsOperateLog.setNum(goodsDto.getReceivedQty().intValue());
        //效期
        warehouseGoodsOperateLog.setExpirationDate(DateUtil.convertLong(goodsDto.getLotAtt02() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
        //入库时间
        warehouseGoodsOperateLog.setCheckStatusTime(DateUtil.convertLong(goodsDto.getLotAtt03() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
        //生产日期
        warehouseGoodsOperateLog.setProductDate(DateUtil.convertLong(goodsDto.getLotAtt01() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
        warehouseGoodsOperateLog.setAddTime(System.currentTimeMillis());
        warehouseGoodsOperateLog.setModTime(System.currentTimeMillis());
        //灭菌批号
        warehouseGoodsOperateLog.setSterilizationBatchNo(goodsDto.getLotAtt05());
        //批次号
        warehouseGoodsOperateLog.setBatchNumber("NO-LOT".equals(goodsDto.getLotAtt04()) ? "": goodsDto.getLotAtt04());
        warehouseGoodsOperateLog.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(goodsDto.getLotAtt08()));
        //贝登批次吗
        warehouseGoodsOperateLog.setVedengBatchNumer(goodsDto.getLotAtt11());
        //剩余库存数量
        warehouseGoodsOperateLog.setLastStockNum(Integer.valueOf(goodsDto.getReceivedQty().intValue()));
        warehouseGoodsOperateLog.setCreator(2);
        warehouseGoodsOperateLog.setUpdater(2);
        //入库类型
        warehouseGoodsOperateLog.setLogType(0);
        warehouseGoodsOperateLog.setComments(orderDto.getASNReference1());
        // VDERP-6942 专项发货SKU回传需关联VP单号，WMS通过LotAtt07字段回传
        logger.info("专项发货SKU回传需关联VP单号：{}", goodsDto.getLotAtt07());
        warehouseGoodsOperateLog.setDedicatedBuyorderNo(goodsDto.getLotAtt07());
    }

    private List<WarehouseGoodsOutInItem> saveWarehouseGoodsOutInItem(int relateId, int operateType, WarehouseGoodsOperateLog inlog, WarehouseGoodsOutIn warehouseGoodsOutIn,WarehouseGoodsOutInItem item) {
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = new ArrayList<>();

        //判断是否满足销售换货出库安调拆行条件
        Boolean haveInstallationExchangeBreakLine = warehouseGoodsOutService.haveInstallationExchangeBreakLine(relateId, operateType, inlog);
        int itemNum = inlog.getNum() < 0 ? -1 : 1;
        if (Boolean.TRUE.equals(haveInstallationExchangeBreakLine)) {

            int num = Math.abs(inlog.getNum());


            logger.info("满足采购入库、销售换货、退货安调拆行条件开始拆行operateType:{},realateId:{},num:{}", operateType, relateId, num);

            for (int i = 0; i < num; i++) {
                item.setNum(BigDecimal.valueOf(itemNum));
                item.setWarehouseGoodsOutInDetailId(null);
                if (i>0) {
                    item.setBarcodeFactory("");
                }
                warehouseGoodsOutInItemMapper.insertSelective(item);
                warehouseGoodsOutInItemList.add(item);
            }
        } else {
            //出入库明细表T_WAREHOUSE_GOODS_OUT_IN_ITEM写入
            warehouseGoodsOutInItemMapper.insertSelective(item);
            warehouseGoodsOutInItemList.add(item);
        }
        return warehouseGoodsOutInItemList;
    }


    @Override
    protected void synchronizeStockData(InputOrderDto inputOrderDto) throws Exception {

        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();

        inputOrderDto.getDetails().stream().forEach(inputOrderGoodsDto -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            stockCalculateDto.setSku(inputOrderGoodsDto.getSKU());
            stockCalculateDto.setLogicalWarehouseId(Integer.valueOf(LogicalEnum.getLogicalWarehouseIdByCode(inputOrderGoodsDto.getLotAtt08())));
            stockCalculateDto.setStockNum(inputOrderGoodsDto.getReceivedQty().intValue());
            stockCalculateList.add(stockCalculateDto);
        });

        StockInfoDto stockInfoDto = new StockInfoDto();
        stockInfoDto.setRelatedNo(inputOrderDto.getASNReference1());
        stockInfoDto.setWarehouseStockList(getWarehouseStockList(stockCalculateList));

        LOGGER.info("单据入库，更新库存服务的数据:" + JSON.toJSONString(stockInfoDto));
        warehouseStockService.updateStockInfo(stockInfoDto);
    }

    protected abstract int getOperateType(InputOrderDto orderDto);

    protected abstract int getWmsLogicalOperateType(InputOrderDto orderDto);

    protected abstract int getRelateId(InputOrderDto orderDto,InputOrderGoodsDto goodsDto);

    protected abstract List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList);

    protected abstract int getBarcodeType(InputOrderDto orderDto, InputOrderGoodsDto goodsDto);

}
