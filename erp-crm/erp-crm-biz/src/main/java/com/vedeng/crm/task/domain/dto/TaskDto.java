package com.vedeng.crm.task.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 任务记录表
 * <AUTHOR>
 */
@Getter
@Setter
public class TaskDto extends BaseDto {
    /**
     * 主键
     */
    private Long taskId;

    /**
     * 任务内容
     */
    @NotEmpty(message = "任务内容不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String taskContent;

    /**
     * 业务类型，1：商机 2：线索 3：报价，4：拜访计划
     */
    private Integer bizType;

    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Integer bizId;

    /**
     * 任务类型
     * 1.初步产品方案
     * 2.单品询价
     * 3.综合询价
     * 4.商机督导
     * 5.再次跟进
     * 6.协同报价
     * 7.拜访任务
     */
    @NotNull(message = "任务类型不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Integer mainTaskType;

    /**
     * 商机督导-督导类型
     */
    private String subTaskType;

    /**
     * 处理状态：0处理中 1已处理 2关闭
     */
    private Integer doneStatus;

    /**
     * 提交时间
     */
    private Date commitTime;

    /**
     * 截止时间
     */
    private Date deadline;

    /**
     * 待办人
     */
    @NotNull(message = "待办人不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private List<Integer> todoUserList;

    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * 通过API接口传过来的用户信息-userId
     */
    private Integer apiUserId;
    /**
     * 通过API接口传过来的用户信息-userName
     */
    private String apiUserName;
}
