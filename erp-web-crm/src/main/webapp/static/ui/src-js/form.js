Vue.prototype.$form = {
    rules(params, key, data) {
        let validList = [];
        for (let item in params) {
            validList.push(item);
        }

        this.validList[key] = validList;

        this.ruleList[key] = params;
        this.pages[key] = data;
    },
    ruleList: {},
    validList: {},
    elList: {},
    pages: {},
    checkValid(key, value, formName) {
        let ruleList = this.ruleList[formName];

        if (!ruleList || !ruleList[key]) {
            return {
                result: true
            }
        }

        let ruleItem = ruleList[key];

        if (ruleItem.required) {
            let type = typeof value;

            if (!value || (type === 'string' && !value.trim()) || (Array.isArray(value) && !value.length)) {
                return {
                    result: false,
                    message: ruleItem.required
                }
            }
        }

        // 小数点位
        if (ruleItem.toFixed) {
            const reg1 = /^\d+(\.\d+)?$/; // 数字类型
            const reg2 = new RegExp(`^\\d+(\\.\\d{1,${ruleItem.toFixed.max}})?$`); // 小数点

            if (!reg1.test(value)) {
                return {
                    result: false,
                    message: ruleItem.toFixed.message
                }
            } else if (!reg2.test(value)) {
                return {
                    result: false,
                    message: ruleItem.toFixed.message
                }
            }
        }

        if (ruleItem.custom) {
            if(Array.isArray(ruleItem.custom)) {
                let flag = true;
                let resultData = {
                    result: true
                }
                ruleItem.custom.forEach(item => {
                    if (flag && !item.valid(value)) {
                        flag = false
                        resultData = {
                            result: false,
                            message: item.message
                        }
                    }
                })
                console.log(resultData)
                return resultData
            } else {
                if (!ruleItem.custom.valid(value)) {
                    return {
                        result: false,
                        message: ruleItem.custom.message
                    }
                }
            }
        }

        return {
            result: true
        }
    },
    validForm(formName) {
        let validList = this.validList[formName];

        if (!validList || !validList.length) {
            return true;
        }

        let flag = true;
        let firstEl = null;
        validList.forEach(item => {
            let itemResult = this.checkValid(item, this.pages[formName][item], formName);

            if (!itemResult.result) {
                flag = false;

                let elList = this.elList[formName];
                if (elList[item]) {
                    if(!firstEl) {
                        firstEl = elList[item];
                        let rect = firstEl.$el.getBoundingClientRect();
                        let parent = this.getScrollParent(firstEl.$el);

                        if(!parent || parent == window) {
                            if(rect.top < 200 || rect.top > window.innerHeight) {
                                window.scrollTo(0, window.screenY + rect.top - 200);
                            }
                        } else {
                            let parentRect = parent.getBoundingClientRect();
                            if(rect.top + rect.height - parentRect.top > parentRect.height) {
                                parent.scrollTo(0, firstEl.$el.offsetTop)
                            }
                        }
                    }
                    elList[item].triggerError(itemResult)
                }
            }
        })

        return flag;
    },
    getScrollParent(element) {
        if (!element) {
            return null;
        }

        const overflowRegex = /(scroll|auto)/;
        const parent = element.parentElement;

        if (parent && overflowRegex.test(window.getComputedStyle(parent).overflow + window.getComputedStyle(parent).overflowY + window.getComputedStyle(parent).overflowX)) {
            return parent;
        }

        return this.getScrollParent(parent);
    },
    setValidEl(el) {
        if (el.$attrs.valid || el.valid) {
            let valid = (el.$attrs.valid || el.valid).split('_');

            el.validKey = valid[0];
            el.validValue = valid[1];

            if (el.validKey && el.validValue) {
                if (!this.elList[el.validKey]) {
                    this.elList[el.validKey] = {};
                }

                this.elList[el.validKey][el.validValue] = el;
            }
        }
    },
    validEl(name) {
        let formName_elName = name.split('_');
        let formName = formName_elName[0];
        let elName = formName_elName[1];

        let validList = this.validList[formName];

        if (!validList || !validList.length) {
            return true;
        }

        let flag = true;

        validList.forEach(item => {
            if(item == elName) {
                let itemResult = this.checkValid(item, this.pages[formName][item], formName);

                flag = itemResult.result;

                let elList = this.elList[formName];
                if (elList[item]) {
                    elList[item].triggerError(itemResult)
                }
            }
        })

        return flag;
    }
};