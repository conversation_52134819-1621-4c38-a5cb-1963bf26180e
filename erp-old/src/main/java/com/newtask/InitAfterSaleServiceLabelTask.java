package com.newtask;

import com.alibaba.fastjson.JSON;
import com.newtask.dto.SkuLabelDto;
import com.vedeng.aftersales.dao.AfterSaleServiceStandardInfoMapper;
import com.vedeng.aftersales.model.dto.AfterSaleServiceStandardInfoDto;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;

@JobHandler("InitAfterSaleServiceLabelTask")
@Component
@Slf4j
public class InitAfterSaleServiceLabelTask extends AbstractJobHandler {

    @Resource
    AfterSaleServiceStandardInfoMapper afterSaleServiceStandardInfoMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("初始化开始");
        ClassPathResource resource = new ClassPathResource("file/SKU.xlsx");

        InputStream inputStream = resource.getInputStream();


        try {
            Workbook workbook = new XSSFWorkbook(inputStream);
            //免费安装 S1
            Sheet sheet = workbook.getSheetAt(0);
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getPhysicalNumberOfRows();


            for (int i = firstRowNum +1; i <lastRowNum ; i++) {
                SkuLabelDto skuLabelDto = new SkuLabelDto();
                Row row = sheet.getRow(i);
                //skuNo
                Cell cellSku = row.getCell(0);
                String skuNo = cellSku.getStringCellValue();
                if(StringUtils.isNotBlank(skuNo)){
                    skuLabelDto.setSkuNo(skuNo.trim());
                }
                StringBuffer sb = new StringBuffer();
                //免费安调S1
                Cell cellS1 = row.getCell(3);
                String stringS1 = cellS1.getStringCellValue();
                if(StringUtils.isNotBlank(stringS1)){
                    sb.append("S1");
                    sb.append(";");
                }

                //7天包退S6
                Cell cellS6 = row.getCell(4);
                String stringS6 = cellS6.getStringCellValue();
                if(StringUtils.isNotBlank(stringS6)){
                    sb.append("S6");
                    sb.append(";");
                }

                //远程协助S3
                Cell cellS3 = row.getCell(5);
                String stringS3 = cellS3.getStringCellValue();
                if(StringUtils.isNotBlank(stringS3)){
                    sb.append("S3");
                    sb.append(";");
                }

                //场地勘察S4
                Cell cellS4 = row.getCell(6);
                String stringS4 = cellS4.getStringCellValue();
                if(StringUtils.isNotBlank(stringS4)){
                    sb.append("S4");
                    sb.append(";");
                }
                if(sb.length()>0){
                    String labelStr = sb.substring(0, sb.length() - 1);
                    skuLabelDto.setAfterSaleServiceLabels(labelStr);
                }

                if(StringUtils.isNotBlank(skuLabelDto.getSkuNo()) && StringUtils.isNotBlank(skuLabelDto.getAfterSaleServiceLabels())){
                    AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = afterSaleServiceStandardInfoMapper.selectBySkuNo(skuLabelDto.getSkuNo());
                    if(afterSaleServiceStandardInfoDto != null){
                        int result = afterSaleServiceStandardInfoMapper.updateBySkuNo(skuLabelDto);
                        if(result == 0 ){
                            log.info("初始化售后服务标签失败，sku:"+ JSON.toJSONString(skuLabelDto));
                            XxlJobLogger.log("初始化售后服务标签失败"+ JSON.toJSONString(skuLabelDto));
                        }
                    } else {
                        int result = afterSaleServiceStandardInfoMapper.insertIntiServiceLabels(skuLabelDto);
                        if(result == 0 ){
                            log.info("初始化售后服务标签失败，sku:"+ JSON.toJSONString(skuLabelDto));
                            XxlJobLogger.log("初始化售后服务标签失败"+ JSON.toJSONString(skuLabelDto));
                        }
                    }
                }
            }
            XxlJobLogger.log("初始化结束");
            return SUCCESS;
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.error("【execute】处理异常",e);
            }
        }
    }
}
