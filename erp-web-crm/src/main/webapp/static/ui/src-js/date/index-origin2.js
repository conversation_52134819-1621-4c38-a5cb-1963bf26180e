
const Mixin_uiDate_transition2 = {
    mounted () {
        window.addEventListener('scroll', this.pageScroll, true);
        window.addEventListener('resize', this.pageScroll);
    },
    methods: {
        enter (el) {
            el.style.transition = '0.19s all ease-in';
            el.style.webkitTransform = 'scale(1, 0)';
            el.style.opacity = 0;
        },
        afterEnter (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },

        beforeLeave (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },
        leave (el) {
            if (el.scrollHeight !== 0) {
                el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1, 0)';
                el.style.opacity = 0;
            }
        },
        afterLeave (el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = ''
        },
        
        pageScroll () {
            if (this.$refs.pickers) {
                this.topScroll();
            }
        },
        topScroll () {           
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.pickers.clientHeight;
            let clientHeight = document.body.clientHeight;
            if ( client.bottom + height + 7 > clientHeight && client.top >= height + 2 ) {
                this.animation = 'appear-up';
                this.$refs.pickers.style.top = `-${height+2}px`;
                this.$refs.pickers.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
            } else {
                this.animation = 'appear-down';
                this.$refs.pickers.style.top = "";
                this.$refs.pickers.style.boxShadow = '';
            }
        }
    }
}

Vue.component('ui-date-picker2', {
    template: `
        <div class="vd-ui-date">
            <div 
                v-if="!ranged"
                ref="input-wrap1"
                class="vd-ui-date-editor"
                :class="'vd-ui-date-editor--' + type"
            >
                <ui-input
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    ref="vdInput"
                    :disabled="disabled"
                    :value="displayValue"
                    @input="value => userInput = value"

                    @focus="handleFocus"
                    @blur="handleBlur"
                    @clear="handleClear"
                    @keydown.native="handleKeydown"
                    @change="handleChange"

                    :size="size"
                    width="100%"
                    :readonly="!editable || readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                >
                    <i v-if="type=='time'" slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-time"></i>
                    <i v-else slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-date"></i>
                </ui-input>
            </div>

            <!-- 日期范围选择 -->
            <div
                v-else 
                ref="input-wrap2"
                class="vd-ui-range"
                @mouseenter="rangeMouseenter"
                @mouseleave="rangeMouseleave"
                @click="handleRangeClick"
            >
                <i class="vd-ui_icon icon-date"></i>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="startPlaceholder"
                        :value="displayValue && displayValue[0]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <span class="split">至</span>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="endPlaceholder"
                        :value="displayValue && displayValue[1]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <div class="range-error2">
                    <i 
                        v-show="haveTrigger"
                        class="vd-ui_icon icon-error2" 
                        @click.stop="handleRangeClear"
                    ></i>
                </div>
            </div>

            <ui-poper :show="pickerVisible" :position="animation === 'appear-up' ? 'top' : ''" ref="dropwrap" :errorable="errorable">
                <div 
                    v-show="pickerVisible"
                    class="vd-ui-date-wrapper"
                    :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                    ref="pickers"
                    @click.stop
                >
                    <!-- 范围 -->
                    <ui-date-range-panel
                        v-if="type === 'daterange'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :value="baseValue"
                        :unlinkPanels="unlinkPanels"
                        :shortcuts="shortcuts"
                        v-bind="pickerOptions"
                        :showTime="type === 'datetime'"
                        @pick="pickDateRange"
                    ></ui-date-range-panel>

                    <!-- 时间 -->
                    <ui-time-panel
                        v-else-if="type === 'time'"
                        :type="type"
                        :value="baseValue"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>

                    <!-- 日期时间 -->
                    <ui-date-time-panel
                        v-else-if="type === 'datetime'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :type="type"
                        :value="baseValue"
                        @pick="pickDateTime"
                        :pickerOptions="pickerOptions"
                        :selectionMode="selectionMode"
                    ></ui-date-time-panel>

                    <ui-date-panel
                        v-else
                        :type="type"
                        :value="baseValue"
                        :firstDayOfWeek="firstDayOfWeek"
                        :shortcuts="shortcuts"
                        @pick="pickDate"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
            </ui-poper>

            <!-- 表单校验报错 -->
            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        size: String,   // large, small, mini
        placeholder: String,
        startPlaceholder: String,
        endPlaceholder: String,
        type: {   // date, year, month, time, datetime, daterange
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        rangeSeparator: {
            type: String,
            default: '-'
        },
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        defaultTime: {},  // 范围选择时选中日期所使用的当日内具体时刻
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
            validator
        },
        unlinkPanels: {   // 在范围选择器里取消两个日期面板之间的联动
            type: Boolean,
            default: false
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
            validator
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mixins: [ Mixin_uiDate_transition2 ],

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
            rangeFocus: false,  // 范围选择器是否获取焦点
        }
    },
    watch: {
        pickerVisible (val) {
            if (this.readonly || this.pickerDisabled) return;
            if (val) {
                this.$nextTick(()=>{
                    this.pageScroll();   // 下拉展开动画
                })
            }
        },
    },
    computed: {
        // 是否范围选择
        ranged () {
            return this.type.indexOf('range') > -1;
        },

        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 显示范围清除按钮
        haveTrigger () {
            // 框子获焦 + 至少有一个值
            let zhi = this.baseValue && this.baseValue[0] && this.baseValue[1];
            return zhi && this.rangeFocus
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            if (Array.isArray(this.baseValue)) {
                return [
                    util_date.formatDate(this.baseValue[0]),
                    util_date.formatDate(this.baseValue[1])
                ];
            }
            else if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.$form.setValidEl(this);

        // 关闭 当前日期窗口之外的其他日期窗口（全局一次只能打开一个）
        let _this = this;
        document.addEventListener('click', (e) => {
            let inputWrap1 = _this.$refs['input-wrap1']; // input
            let inputWrap2 = _this.$refs['input-wrap2']; // 范围input
            let panelWrap = _this.$refs['pickers']; // 日期panel层
            let include1 = false; // input 是否包含点击元素
            let include2 = false; // 范围input 是否包含点击元素
            let include3 = false; // 面板是否包含点击元素

            if (inputWrap1 && inputWrap1.contains(e.target)) {
                include1 = true;
            }
            if (inputWrap2 && inputWrap2.contains(e.target)) {
                include2 = true;
            }
            if (panelWrap && panelWrap.contains(e.target)) {
                include3 = true;
            }

            if (_this.pickerVisible && !include1 && !include2 && !include3) {
                _this.pickerVisible = false;
            }
        }, true); // true: 从外向内 由根节点向点击元素执行

        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (this.type == 'daterange') {
                    let test = util_date.arrayToDate(this.value)
                    this.baseValue = test;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.dealBlur();
        },

        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // input 失焦
        handleBlur () {
            // this.pickerVisible = false;
            // this.dealBlur();
        },
        // 主动 失去焦点
        dealBlur() {
            console.log('blur');
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
            this.emitChange(null);
        },
        // 范围清除
        handleRangeClear (event) {
            this.baseValue = null;
            this.userInput = null;
            this.emitInput('');
            this.emitChange('');
        },
        // change
        handleChange () {

            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                if (!this.ranged) {
                    this.handleChange();
                    this.hidePanel();
                    event.stopPropagation();
                } else {
                    setTimeout(() => {
                        if (this.refInput.indexOf(document.activeElement) === -1) {
                            this.hidePanel();
                            event.stopPropagation();
                        }
                    }, 0);
                }
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                this.checkValid(val);
            }
        },
        // input事件
        emitInput(val) {
            this.$emit('input', util_date.format(val, this.type));
            this.checkValid(val);
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期范围  [关闭panel => emit值]
        pickDateRange (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
                this.emitChange(val);
            }
        },
        handleRangeClick() {
            const type = this.type;

            if (util_date.HAVE_TRIGGER_TYPES.indexOf(type) !== -1 && !this.pickerVisible) {
                this.pickerVisible = true;
            }
            this.$emit('focus', this);
        },
        
        rangeMouseenter () {
            this.rangeFocus = true;
        },
        rangeMouseleave () {
            this.rangeFocus = false;
        },
        checkValid(val) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, val, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
            this.$refs.dropwrap.calcPosition();
        },
    }
})