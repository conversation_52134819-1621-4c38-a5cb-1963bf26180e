package com.vedeng.erp.confirmrecord.service.impl;

import com.newtask.util.TimeUtils;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.confirmrecord.enums.ConfirmRecordSendTypeEnum;
import com.vedeng.erp.confirmrecord.service.ConfirmRecordApiService;
import com.vedeng.erp.confirmrecord.dao.ConfirmRecordDao;
import com.vedeng.erp.confirmrecord.dto.ButtonIsShow;
import com.vedeng.erp.confirmrecord.dto.ConfirmRecordDto;
import com.vedeng.erp.confirmrecord.dto.SignInRecordDto;
import com.vedeng.erp.confirmrecord.model.ConfirmRecord;
import com.vedeng.erp.confirmrecord.model.ConfirmRecordRes;
import com.vedeng.erp.confirmrecord.service.ConfirmRecordService;
import com.vedeng.erp.saleorder.constant.ConfirmRecordBusinessTypeEnum;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.impl.SaleorderServiceImpl;
import com.vedeng.trader.dao.TraderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/11 9:28
 */
@Service
@Slf4j
public class ConfirmRecordServiceImpl implements ConfirmRecordService, ConfirmRecordApiService {

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private ConfirmRecordDao confirmRecordDao;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private SaleorderServiceImpl saleorderService;

    @Resource
    private UserMapper userMapper;
    @Value("${confirm_order_url}")
    private String confirmOrderUrl;

    private final int Num = 2;

    @Override
    public ConfirmRecordRes getConfirmRecord(Integer saleorderId) {
        ConfirmRecordRes confirmRecordRes = new ConfirmRecordRes();
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);
        List<ConfirmRecord> list;
        list = confirmRecordDao.queryByBusinessNo(saleorder.getSaleorderNo());

        //如果不是集采 且线上订单 取确认时间 否则取创建时间
        if(!OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_JCO.equals(saleorder.getOrderType())
                && !OrderConstant.ORDER_TYPE_BD.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_HC.equals(saleorder.getOrderType())
                && !OrderConstant.ORDER_TYPE_DH.equals(saleorder.getOrderType())){
                confirmRecordRes.setConfirmTime(saleorder.getConfirmTime());
        }else{
            confirmRecordRes.setConfirmTime( TimeUtils.timeStampToDate(saleorder.getAddTime()) );
        }

        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(res->{
                User user = userMapper.getUserByUserId(res.getCreator());

                if(res.getSendType()==0){
                    res.setUserName("系统");
                }else{
                    res.setUserName(user.getUsername());
                }
            });
            confirmRecordRes.setConfirmRecordList(list);
        }
        return confirmRecordRes;
    }

    @Override
    public String copyUrlLink(Integer saleorderId) {
        String url;
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);
        url = confirmOrderUrl + "e/o/" + saleorder.getSaleorderNo();
        return url;

    }

    @Override
    public void sendMessage(Integer saleorderId, User user) {
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);
        //除集采线下订单外 自动发短信给采购人员 //发送短信
        if (!OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType())) {
            //当天只能手动 发送一次
            String nowTime = TimeUtils.getTimeStr();
            int todayNum = confirmRecordDao.selectTodayNum(saleorder.getSaleorderNo(), nowTime);
            if (todayNum > 0) {
                throw new ServiceException("发送次数已达上限，请勿频繁发送");
            }

            //手动发送超过三次
            int countNum = confirmRecordDao.selectSendCount(saleorder.getSaleorderNo());
            if (countNum > Num) {
                throw new ServiceException("发送次数已达上限，请勿频繁发送");
            }
            // TODO: 2022/10/14 VDERP-12290【审计】订单确认、签回单功能修改（隐藏和取消）
            //发送短信 除集采线下订单 所有线上订单 (JCF、JCO、BD、HC、DH 剔除)
            //if(!OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_JCO.equals(saleorder.getOrderType())
            //        && !OrderConstant.ORDER_TYPE_BD.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_HC.equals(saleorder.getOrderType())
            //        && !OrderConstant.ORDER_TYPE_DH.equals(saleorder.getOrderType())){
            // boolean sendTplSms = saleorderService.sendMes(saleorder);
            //    if (sendTplSms) {
            //        saleorderService.insertConFirmRecord(saleorder.getSaleorderNo(), ConfirmRecordSendTypeEnum.MANUAL_SEND.getCode(), user);
            //    } else {
            //        throw new ServiceException("发送失败");
            //    }
            //}
        }
    }

    @Override
    public void insertConFirmRecord(ConfirmRecordDto confirmRecord) {
        ConfirmRecord confirmRecord1 = new ConfirmRecord();
        BeanUtils.copyProperties(confirmRecord, confirmRecord1);
        confirmRecordDao.insertSelective(confirmRecord1);
    }

    @Override
    public ButtonIsShow copyAndSendMessageButtonIsShow(Integer saleorderId, User user) {
        ButtonIsShow buttonIsShow = new ButtonIsShow();
        //默认都不展示
        buttonIsShow.setCopyButton(false);
        buttonIsShow.setSendMesButton(false);

        //复制按钮是否展示 订单生效且 仅订单所属销售和超级管理员 且除集采线下订单 所有线上订单 (JCF、JCO、BD、HC、DH 剔除)   有操作权限 订单信息确认后 按钮隐藏
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);
        Boolean isShow =saleorder!=null && saleorder.getValidStatus()!=null && saleorder.getValidStatus()==1 &&  ("njadmin".equals(user.getUsername()) || String.valueOf(user.getUserId()).equals(String.valueOf(saleorder.getUserId()))) && (saleorder.getConfirmStatus() == null || saleorder.getConfirmStatus() == 0)
                && !OrderConstant.ORDER_TYPE_JCF.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_JCO.equals(saleorder.getOrderType())
                && !OrderConstant.ORDER_TYPE_BD.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_TYPE_HC.equals(saleorder.getOrderType())
                && !OrderConstant.ORDER_TYPE_DH.equals(saleorder.getOrderType());
        //（njadmin || 登录人和归属销售 ）&& 确认状态是 未确认时展示
        if (isShow) {
            buttonIsShow.setCopyButton(true);
            buttonIsShow.setSendMesButton(true);
        }
        return buttonIsShow;
    }

    @Override
    public List<SignInRecordDto> queryCustomerSignature(Integer saleorderId) {
        List<SignInRecordDto> confirmRecordList = confirmRecordDao.queryCustomerSignature(saleorderId);
        return confirmRecordList;
    }

    @Override
    public void updateConfirmTimeAndStatus(ConfirmRecord confirmRecord) {
        confirmRecordDao.updateConfirmTimeAndStatus(confirmRecord);
    }

    @Override
    public void updateConfirmInfo(Integer customerId, String traderContactMobile, Date signDate, List<SaleorderVo> saleorderVos) {
        Map<Integer, SaleorderVo> collect = saleorderVos.stream().collect(Collectors.toMap(SaleorderVo::getSaleorderGoodsId, SaleorderVo -> SaleorderVo));

        // 获取制定客户和联系人相关的商品
        Saleorder saleorder = new Saleorder();
        saleorder.setTraderId(customerId);
        saleorder.setTraderContactMobile(traderContactMobile);
        List<SaleorderVo> saleorderGoods = saleorderMapper.getSaleorderGoodsId(saleorder);
        Date nowTime = new Date();
        List<ConfirmRecord> confirmRecordList = new ArrayList<>();
        // 更新记录表,无数据则添加
        saleorderGoods.forEach(res -> {
            // 设置除最新记录外的其他记录签收数量设为0
            ConfirmRecord confirmRecord = new ConfirmRecord();
            confirmRecord.setBusinessNo(res.getSaleorderGoodsId().toString());
            confirmRecord.setBusinessType(ConfirmRecordBusinessTypeEnum.PENDING_CONFIRM.getCode());
            confirmRecord.setConfirmStatus(ErpConst.ONE);
            confirmRecord.setModTime(nowTime);
            confirmRecord.setConfirmTime(signDate);
            confirmRecordList.add(confirmRecord);


            // 设置最新记录签收数量
            ConfirmRecord confirmRecordInfo = new ConfirmRecord();
            ConfirmRecord record = confirmRecordDao.queryConfirmRecordInfoByBusinessNo(res.getSaleorderGoodsId().toString());

            BeanUtils.copyProperties(confirmRecord, confirmRecordInfo);
            if (!collect.isEmpty() && collect.get(res.getSaleorderGoodsId())!=null
                && res.getConfirmNumber() - (collect.get(res.getSaleorderGoodsId()).getConfirmNumber() == null ? 0 : collect.get(res.getSaleorderGoodsId()).getConfirmNumber()) > 0) {
                if (record == null) {
                    ConfirmRecord recordInfo = new ConfirmRecord();
                    BeanUtils.copyProperties(confirmRecord, recordInfo);
                    recordInfo.setBusinessDesc(res.getGoodsName());
                    recordInfo.setCreator(0);
                    recordInfo.setAddTime(nowTime);
                    recordInfo.setUpdater(0);
                    recordInfo.setSendType(1);
                    recordInfo.setSendTime(signDate);
                    recordInfo.setSendMethodType("0");
                    confirmRecordDao.insertSelective(recordInfo);
                    confirmRecordInfo.setId(recordInfo.getId());
                } else {
                    confirmRecordInfo.setId(record.getId());
                }

                confirmRecordInfo.setConfirmNumber(res.getConfirmNumber() - (collect.get(res.getSaleorderGoodsId()).getConfirmNumber() == null ? 0 : collect.get(res.getSaleorderGoodsId()).getConfirmNumber()));
            } else {
                confirmRecordInfo.setConfirmNumber(res.getConfirmNumber());
            }
            confirmRecordList.add(confirmRecordInfo);
        });
        confirmRecordDao.updateConfirmInfo(confirmRecordList);

    }

    @Override
    public void updateConfirmTimeAndStatus(ConfirmRecordDto confirmRecordDto) {
        ConfirmRecord confirmRecord = new ConfirmRecord();
        BeanUtils.copyProperties(confirmRecordDto, confirmRecord);
        confirmRecordDao.updateConfirmTimeAndStatus(confirmRecord);

    }
}
