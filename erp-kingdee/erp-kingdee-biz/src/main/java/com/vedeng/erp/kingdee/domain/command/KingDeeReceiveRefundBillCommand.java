package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class KingDeeReceiveRefundBillCommand {

    /**
     * 云星空系统单据FID值
     */
    private String FID;

    /**
     * 单据编号
     */
    @BusinessID
    private String FBillNo;

    /**
     * 单据类型id
     */
    private KingDeeNumberCommand FBillTypeID = new KingDeeNumberCommand();

    /**
     * 单据日期
     */
    private String FDATE;

    private String f_QZOK_PZGSYWDH;

    /**
     * 往来单位类型
     */
    private String FCONTACTUNITTYPE;

    /**
     * 往来单位
     */
    private KingDeeNumberCommand FCONTACTUNIT = new KingDeeNumberCommand();

    /**
     * 收款单位类型
     */
    private String FRECTUNITTYPE;

    /**
     * 收款单位
     */
    private KingDeeNumberCommand FRECTUNIT = new KingDeeNumberCommand();

    /**
     * 结算组织
     */
    private KingDeeNumberCommand FSETTLEORGID = new KingDeeNumberCommand();

    /**
     * 销售组织
     */
    private KingDeeNumberCommand FSALEORGID = new KingDeeNumberCommand();

    /**
     * 收款组织
     */
    private KingDeeNumberCommand FPAYORGID = new KingDeeNumberCommand();

    /**
     * 业务类型
     */
    private String FBUSINESSTYPE;

    /**
     * 流水号
     */
    private String F_QZOK_LSH;

    /**
     * 单据头id
     */
    private String F_QZOK_BDDJTID;

    /**
     * 自动提交银行
     */
    private String F_QZOK_ZDTJYH;

    /**
     * 明细
     */
    private List<KingDeeReceiveRefundEntryCommand> FREFUNDBILLENTRY;

    @NoArgsConstructor
    @Data
    public static class KingDeeReceiveRefundEntryCommand{
        /**
         * 结算方式
         */
        private KingDeeNumberCommand FSETTLETYPEID = new KingDeeNumberCommand();

        /**
         * 用途
         */
        private KingDeeNumberCommand FPURPOSEID = new KingDeeNumberCommand();

        /**
         * 收款金额
         */
        private BigDecimal FRECTOTALAMOUNTFOR;

        /**
         * 实退金额
         */
        private BigDecimal FREFUNDAMOUNTFOR;

        /**
         * 我方银行账号
         */
        private KingDeeNumberCommand FACCOUNTID = new KingDeeNumberCommand();

        /**
         * 收款类型
         */
        private String FRecType;

//    /**
//     * 对方开户行账号
//     */
//    private String FOPPOSITEBANKACCOUNT;

//    /**
//     * 对方开户行名称
//     */
//    private String FOPPOSITECCOUNTNAME;

//    /**
//     * 对方开户行
//     */
//    private String FOPPOSITEBANKNAME;

        /**
         * 收款账户账号
         */
        private String FOPPOSITEBANKACCOUNT;

        /**
         * 收款账户名称
         */
        private String FOPPOSITECCOUNTNAME;

        /**
         * 收款单位开户行
         */
        private String FOPPOSITEBANKNAME;

        /**
         * 联行号
         */
        private String FCNAPS;

        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;

        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;

        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;

        /**
         * 贝登订单行id
         */
        private String F_QZOK_BDDJHID;

        /**
         * 备注
         */
        private String FNote;
    }



}
