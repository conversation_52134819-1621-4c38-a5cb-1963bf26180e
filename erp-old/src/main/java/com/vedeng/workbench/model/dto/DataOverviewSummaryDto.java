package com.vedeng.workbench.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DataOverviewSummaryDto {

    /**
     * 统计对象姓名
     */
    private String name;

    /**
     * 统计对象userid
     */
    private Integer userId;

    /**
     * 商机总额
     */
    private BigDecimal totalBussinessAmount;

    /**
     * 商机总数
     */
    private Integer totalBussinessNum;

    /**
     * 平均商机金额
     */
    private BigDecimal avrBussinessAmount;

    /**
     * 昨日新增商机金额
     */
    private BigDecimal yesterdayBussinessAmount;

    /**
     * 昨日新增商机数量
     */
    private Integer yesterdayBussinessNum;

    /**
     * 本周新增商机金额
     */
    private BigDecimal thisWeekBussinessAmount;

    /**
     * 本周新增商机总数
     */
    private Integer thisWeekBussinessNum;
}
