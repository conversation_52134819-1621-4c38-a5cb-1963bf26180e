package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.enums.OtherTypeConst;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderGoodsDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWmsOutputOrderDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 外借入库单-21-22历史数据
 * @date 2023/05/26 13:55
 */
@Service
@Slf4j
public class BatchAllocationInHistoryProcessor extends BaseProcessor<BatchWarehouseGoodsOutInDto, KingDeeAllocationDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BatchWmsOutputOrderDtoMapper batchWmsOutputOrderDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Override
    public KingDeeAllocationDto doProcess(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto ,JobParameters params, ExecutionContext stepContext) throws Exception {
        KingDeeAllocationDto dto = new KingDeeAllocationDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        //判断是否是21-22其他类型出入库
        if (!OtherTypeConst.UPDATE_REMARK_ORTHER_TYPE.equals(batchWarehouseGoodsOutInDto.getUpdateRemark())){
            return null;
        }
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("外借入库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        // 补充详细单数据
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));
        log.info("外借入库单,BatchAllocationProcessorService.process:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));

        dto.setFId("0");
        dto.setFDate(DateUtil.formatDateTime(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFTransferDirect("GENERAL");
        dto.setFQzokJdr("admin");

        List<KingDeeAllocationDetailDto> detailList = new ArrayList<>();
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }

        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(d -> {
            KingDeeAllocationDetailDto detailDto = new KingDeeAllocationDetailDto();
            detailDto.setFMaterialId("V"+d.getGoodsId());
            detailDto.setFQty(Integer.toString(Math.abs(d.getNum().intValue())));
            detailDto.setFSrcStockId("CK9998");
            detailDto.setFDestStockId("CK9999");
            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokYwlx("外借归还单");
            detailDto.setFQzokPch(d.getVedengBatchNumber());
            detailDto.setFQzokXlh(d.getBarcodeFactory());

            detailList.add(detailDto);
        });
        dto.setFBillEntry(detailList);
        return dto;
    }





}
