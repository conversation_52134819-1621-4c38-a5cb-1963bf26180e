package com.vedeng.infrastructure.kingdee.domain.command;


import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶扩展save参数对象
 * @date 2022/8/25 18:55
 */
public class SaveExtCommand<T> extends BaseCommand {


    private int Creator;

    private ArrayList<String> NeedReturnFields = new ArrayList<>();

    private ArrayList<String> NeedUpDateFields = new ArrayList<>();

    /**
     * 是否自动提交审核
     */
    private final Boolean isAutoSubmitAndAudit;
    /**
     * 是否自动调整字段顺序
     */
    private final Boolean isAutoAdjustField = true;

    private final T Model;

    public SaveExtCommand(T t, Boolean isAutoSubmitAndAudit, String formId) {
        super(formId);
        this.isAutoSubmitAndAudit = isAutoSubmitAndAudit;
        this.Model = t;
    }

    public SaveExtCommand(T t, String formId) {
        super(formId);
        this.isAutoSubmitAndAudit = true;
        this.Model = t;
    }

    public SaveExtCommand(T t, String formId,ArrayList<String> needReturnFields) {
        super(formId);
        this.isAutoSubmitAndAudit = true;
        this.Model = t;
        this.NeedReturnFields = needReturnFields;
    }

    public SaveExtCommand(T t, String formId,ArrayList<String> needReturnFields,boolean isAutoSubmitAndAudit) {
        super(formId);
        this.isAutoSubmitAndAudit = isAutoSubmitAndAudit;
        this.Model = t;
        this.NeedReturnFields = needReturnFields;
    }

    public SaveExtCommand(T t, String formId, int creator, ArrayList<String> needReturnFields, ArrayList<String> needUpDateFields, Boolean isAutoSubmitAndAudit) {
        super(formId);
        this.isAutoSubmitAndAudit = isAutoSubmitAndAudit;
        this.Model = t;
        Creator = creator;
        NeedReturnFields = needReturnFields;
        NeedUpDateFields = needUpDateFields;
    }

    public Boolean getAutoSubmitAndAudit() {
        return isAutoSubmitAndAudit;
    }

    public Boolean getAutoAdjustField() {
        return isAutoAdjustField;
    }

    public int getCreator() {
        return Creator;
    }

    public ArrayList<String> getNeedReturnFields() {
        return NeedReturnFields;
    }

    public ArrayList<String> getNeedUpDateFields() {
        return NeedUpDateFields;
    }

}
