package com.vedeng.trader.enums;


/**
 * <AUTHOR>
 * 逾期状态
 */
public enum OverdueStateEnum {
    UN_STARTED(1, "未开始"),
    UN_OVERDUE(2, "未逾期"),
    PART_OVERDUE(3, "部分逾期"),
    ALL_OVERDUE(4, "全部逾期");


    private Integer code;

    private String desc;

    OverdueStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}