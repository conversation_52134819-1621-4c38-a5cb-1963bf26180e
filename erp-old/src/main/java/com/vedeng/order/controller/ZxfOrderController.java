package com.vedeng.order.controller;

import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.Unit;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.goods.service.UnitService;
import com.vedeng.order.enums.SaleOrderTypeEnum;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.RemarkComponentDto;
import com.vedeng.order.model.query.ZxfGoodsQuery;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.order.service.ZxfOrderService;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.constant.LogicalEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 直销订单管理
 */
@Controller
@RequestMapping("/order/zxf")
public class ZxfOrderController extends BaseController {

    /**
     * 记录日志
     */
    private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(ZxfOrderController.class);


    @Autowired
    @Qualifier("goodsService")
    protected GoodsService goodsService;

    @Autowired
    @Qualifier("zxfOrderService")
    protected ZxfOrderService zxfOrderService;

    @Autowired
    @Qualifier("traderCustomerService")
    protected TraderCustomerService traderCustomerService;// 客户-交易者


    @Autowired
    @Qualifier("regionService")
    protected RegionService regionService;



    // 保存组件标签
    @Autowired
    @Qualifier("saleorderService")
    protected SaleorderService saleorderService;

    @Autowired
    @Qualifier("unitService")
    protected UnitService unitService;

    @ResponseBody
    @RequestMapping(value = "/add")
    public ModelAndView add(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();
        mv.setViewName("order/zxf/add");
        Unit unit = new Unit();
        unit.setCompanyId(user.getCompanyId());
        List<Unit> unitList = unitService.getAllUnitList(unit);
        mv.addObject("unitList", unitList);
        return mv;
    }


    /**
     * 保存内部备注标签
     * @param labelDataDto 存储对象
     * @return 保存结果
     */
    @ResponseBody
    @RequestMapping(value = "/saveInsideComments")
    public ResultInfo<?> saveInsideComments(@RequestBody RemarkComponentDto labelDataDto) {
        log.info("保存内部备注标签 saveInsideComments -- {}",labelDataDto);
        return saleorderService.saveInsideComments(labelDataDto);
    }


    /**
     * 订单产品添加页面
     *
     * @param request
     * @param session
     * @param zxfGoodsQuery
     * @return
     */
    @RequestMapping(value = "/addSaleorderGoods")
    public ModelAndView addSaleorderGoods(HttpServletRequest request, HttpSession session, ZxfGoodsQuery zxfGoodsQuery,@RequestParam(defaultValue = "4") Integer scene) {
        ModelAndView mv = new ModelAndView("/order/zxf/add_saleorder_goods");

        mv.addObject("scene",scene);
        boolean anyNotEmpty = StringUtils.isNotBlank(zxfGoodsQuery.getBrandName()) || StringUtils.isNotBlank(zxfGoodsQuery.getSkuNo())
                || StringUtils.isNotBlank(zxfGoodsQuery.getGoodsName()) || StringUtils.isNotBlank(zxfGoodsQuery.getUnitName())
                || StringUtils.isNotBlank(zxfGoodsQuery.getTypeSpecification());
        if (anyNotEmpty) {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            Page page = getPageTag(request, zxfGoodsQuery.getPageNo(), zxfGoodsQuery.getPageSize());
            Goods goods = new Goods();
            goods.setGoodsName(zxfGoodsQuery.getGoodsName());
            goods.setSku(zxfGoodsQuery.getSkuNo());
            goods.setBrandName(zxfGoodsQuery.getBrandName());
            goods.setUnitName(zxfGoodsQuery.getUnitName());
            goods.setCompanyId(user.getCompanyId());
            goods.setSearchContent(zxfGoodsQuery.getSearchContent());
            //添加组合搜索，规格型号
            goods.setSpecModel(zxfGoodsQuery.getTypeSpecification());
            Map<String, Object> map = new HashMap<String, Object>();
            if (zxfGoodsQuery.getLogicalId() != null && zxfGoodsQuery.getLendOut() == 1) {//

                map = goodsService.getlistGoodsStockPage(request, goods, page, user);
                mv.addObject("goodsList", map.get("goodsList"));
            } else if (StringUtil.isNotBlank(LogicalEnum.getLogicalWarehouseCode(zxfGoodsQuery.getLogicalId()))) {
                request.setAttribute("logicalId", zxfGoodsQuery.getLogicalId());
                map = goodsService.getLogicallistGoodsStockPage(request, goods, page, user);
                mv.addObject("goodsList", map.get("goodsList"));
            } else {
                map = goodsService.queryGoodsListPage(goods, page, session);
                mv.addObject("goodsList", map.get("list"));
            }
            mv.addObject("page", map.get("page"));
            mv.addObject("searchContent", zxfGoodsQuery.getSearchContent());
            mv.addObject("goodsName", zxfGoodsQuery.getGoodsName());
            mv.addObject("skuNo", zxfGoodsQuery.getSkuNo());
            mv.addObject("brandName", zxfGoodsQuery.getBrandName());
            //mv.addObject("unitName", zxfGoodsQuery.getUnitName());
            mv.addObject("typeSpecification", zxfGoodsQuery.getTypeSpecification());
            mv.addObject("zxfunitNameValue", zxfGoodsQuery.getUnitName());
        }
        mv.addObject("saleorderId", zxfGoodsQuery.getSaleorderId());
        //外借单搜索
        mv.addObject("lendOut", zxfGoodsQuery.getLendOut());
        mv.addObject("callbackFuntion", zxfGoodsQuery.getCallbackFuntion());
        mv.addObject("logicalId", zxfGoodsQuery.getLogicalId());
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        Unit unit = new Unit();
        unit.setCompanyId(user.getCompanyId());
        List<Unit> unitList = unitService.getAllUnitList(unit);
        mv.addObject("unitList", unitList);
        return mv;

    }



    /**
     * <b>Description:</b><br>
     * 保存新增的销售订单
     *
     * @param request
     * @param session
     * @param saleorder
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年12月16日 下午2:24:31
     */
    @ResponseBody
    @RequestMapping(value = "/saveaddsaleorderinfo")
    public ModelAndView saveAddSaleorderInfo(HttpServletRequest request, HttpSession session, Saleorder saleorder) {
        ModelAndView mv = new ModelAndView();
        if (checkSaleOrderStatus(saleorder.getSaleorderId())){
            mv.addObject("message","订单状态已发生变化，请勿编辑订单");
            return fail(mv);
        }
        try {
            //Set 发票类型- 13%增值税专用发票
            saleorder.setInvoiceType(972);
            if (SysOptionConstant.CUSTOMER_NATURE_TERMINAL.equals(saleorder.getCustomerNature())) {
                TraderCustomer traderCustomer = new TraderCustomer();
                traderCustomer.setTraderId(saleorder.getTraderId());
                TraderCustomerVo traderBaseInfo = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
                // 终端类型
                saleorder.setTerminalTraderType(traderBaseInfo.getCustomerType());
                // 终端名称
                saleorder.setTerminalTraderName(traderBaseInfo.getTrader().getTraderName());
                // 销售区域
                String region = (String) regionService.getRegion(traderBaseInfo.getAreaId(), 2);
                Region salesAreaIdRegion = regionService.getRegionByRegionId(traderBaseInfo.getAreaId());

                if(null==salesAreaIdRegion || null==salesAreaIdRegion.getRegionCode()){
                    logger.info("订单创建查询销售区域id查询为空，客户id:{}",saleorder.getTraderId());
                }else {
                    saleorder.setSalesAreaId(Integer.parseInt(salesAreaIdRegion.getRegionCode()));
                }
                saleorder.setSalesArea(region);
            }
            User user = (User) session.getAttribute(ErpConst.CURR_USER);
            Long time = DateUtil.sysTimeMillis();

            saleorder.setAddTime(time);
            saleorder.setCreator(user.getUserId());
            saleorder.setCreatorOrgId(user.getOrgId());
            saleorder.setCreatorOrgName(user.getOrgName());
            saleorder.setCompanyId(user.getCompanyId());
            saleorder.setOrderType(SaleOrderTypeEnum.ZXF.getType());
            saleorder.setStatus(0);
            saleorder = zxfOrderService.saveAddSaleorderInfo(saleorder, request, session);
            if (null != saleorder) {
                mv.addObject("url", "/order/saleorder/edit.do?saleorderId=" + saleorder.getSaleorderId() + "&extraType="
                        + saleorder.getExtraType() +"&scene=0");
                return success(mv);
            } else {
                return fail(mv);
            }
        } catch (Exception e) {
            logger.error("saveaddsaleorderinfo:", e);
            return fail(mv);
        }
    }

}
