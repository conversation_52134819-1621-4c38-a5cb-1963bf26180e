

function sea(thirdCategoryId) {
    checkLogin();
    var keyWords=$('#sousuo').val()
    /*$.ajax({
        type: "POST",
        url: page_url+"/category/base/choiceCategory.do",
        data:{"keyWords":keyWords,"thirdCategoryId":thirdCategoryId},
        dataType: 'json',
        async: false,
        success: function (data) {
            window.location.reload()
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }else{
                layer.alert("操作失败")
            }
        }
    });*/
    window.location.href=page_url+"/category/base/choiceCategory.do?keyWords="+keyWords+"&thirdCategoryId="+thirdCategoryId;

}

function quxiao() {
   window.parent.quxiao();
}

function qd(thirdCategoryId) {
    var v=$('input:radio:checked').val();
    if (v==null || v=='') {
        layer.alert("请选择商品分类");
        return;
    }
    var v1=v.split(",");
    var firstCategoryId=v1[0];
    var secondCategoryId=v1[1];
    var searchUrl = page_url+"/category/base/commitCategory.do?firstCategoryId="+firstCategoryId+"&secondCatrgoryId="+secondCategoryId+"&thirdCatrgoryId="+thirdCategoryId;
    window.parent.choice(searchUrl)
}