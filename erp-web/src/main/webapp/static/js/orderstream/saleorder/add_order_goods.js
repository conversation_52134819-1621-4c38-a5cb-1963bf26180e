$(function(){
    $("#price").change(function(){
        if(Number($(this).val()) == 0 && $(this).val() != ''){
            $("#isGift").show();
        }else{
            $("#isGift").hide();
        }
    })
    $("#price").trigger('change');
});

function search() {
    $('.J-error').hide();
    checkLogin();
    // clearErroeMes();//清除錯誤提示信息
    if (!checkSearchCondition()) {
        // warnTips("errorMes", "查询条件不能为空");//文本框ID和提示用语
        $('.J-error').show();
        // $("#searchContent").addClass("errorbor");
        return false;
    }
    // $("#search").submit();
    return true;
}

function checkSearchCondition() {
    var anyNotEmpty = $("input[name=searchContent]").val() != ""||$("select[name=goodsBrandId]").val() != ""||$("select[name=goodsType]").val() != ""||$("select[name=unitName]").val() != ""||($("#zxfTs").val()!=undefined && $("#zxfTs").val()!="") ;
    return anyNotEmpty;
}


function selectGoods(goodsId, sku, goodsName, brandName, model, unitName, goodsLevelName, goodsUser, verifyStatus, stockNum,scene) {
    // //如果有回调函数就调用
    // if (callbackFuntion != null && callbackFuntion != '') {
    //     if (typeof (stockNum) == "undefined") {
    //         stockNum = 0
    //     }
    //     eval("window.parent." + callbackFuntion + "('" + sku + "','" + goodsName + "','" + brandName + "','" + model + "','" + unitName + "','" + stockNum + "')");
    //     $("#close-layer").click();
    //     return;
    // }

    checkLogin();
    //验证该销售订单中是否存在重复产品
    var saleOrderId = $("input[name=saleorderId]").val();
    $.ajax({
        async: false,
        url: '/orderstream/saleorder/vailSaleorderGoodsRepeat.do',
        data: {"saleorderId":  saleOrderId, "goodsId": goodsId, "skuNo": sku},
        type: "GET",
        dataType: "json",
        success: function (data) {
            if (data.code == 0) {
                var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                parent.layer.close(index);
                editDetailGoods(saleOrderId,goodsId,sku,scene);

            } else {
                layer.alert(data.message,
                    {icon: 2},
                    function (index) {
                        layer.close(index);
                    }
                );
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })


}

function editDetailGoods(saleOrderId,goodsId,sku,scene){
    var open = parent.layer.open({
        type: 2,
        title: '添加产品',
        shadeClose: false,
        area : ['800px', '600px'],
        content: '/orderstream/saleorder/editSelectedSaleorderGoodsDetail.do?saleorderId=' + saleOrderId + "&goodsId=" + goodsId + "&skuNo=" + sku + "&scene=" + scene
    });
}



function againSearch(saleOrderId,scene) {
    checkLogin();
    var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    parent.layer.close(index);
    var open = parent.layer.open({
        type: 2,
        title: '添加产品',
        shadeClose: false,
        area : ['800px', '600px'],
        content: '/orderstream/saleorder/addSaleorderGoods.do?saleorderId=' + saleOrderId +"&scene=" + scene
    });
}

function confirmTotalMoney(str) {
    checkLogin();
    clearErroeMes();
    var $form = $("#confirmGoodsDiv");
    $form.find("#confirmTotalMoney").html("");

    var flag = false;

    var price = $form.find("#price").val().trim();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;

    var num = $form.find("#num").val().trim();
    var re = /^[0-9]+$/;

    if (str == "price") {
        if (price.length > 0 && !reg.test(price)) {
            warnTips("priceError", "单价金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
            return false;
        } else if (Number(price) > 300000000) {
            warnTips("priceError", "产品单价不允许超过三千万");//文本框ID和提示用语
            return false;
        }
        if (num.length != 0 && num != "0" && re.test(num)) {
            flag = true;
        }
    } else {
        if (num.length == 0) {
            warnTips("num", "产品数量不允许为空");//文本框ID和提示用语
            return false;
        } else {
            if (num == "0" || !re.test(num)) {
                warnTips("num", "产品数量必须为大于0的整数");//文本框ID和提示用语
                return false;
            }
        }
        if (Number(num) > 100000000) {
            warnTips("num", "产品数量不允许超过一亿");//文本框ID和提示用语
            return false;
        }
        if (price.length > 0 && reg.test(price)) {
            flag = true;
        }
    }
    if ((Number(num) * Number(price)) > 300000000) {
        warnTips("num", "产品总金额不允许超过三亿");//文本框ID和提示用语
        return false;
    }

    if (flag) {
        if (num != undefined && num != "" && price != undefined && price != "") {
            /*var f = Number(num) * Number(price);
            var s = f.toString();
            var rs = s.indexOf('.');
            if (rs < 0) {
                rs = s.length;
                s += '.';
            }
            while (s.length <= rs + 2) {
                s += '0';
            }*/
            $form.find("#confirmTotalMoney").html((Number(num) * Number(price)).toFixed(2));
        }
    }
    return true;
}

function confirmSubmit() {
    checkLogin();
    var $form = $("#confirmGoodsDiv");

    $form.find("form :input").parents('li').find('.warning').remove();
    $form.find("form :input").removeClass("errorbor");

    $("#haveInstallation").val($form.find("input[name='installation']:checked").val());

    var price = $form.find("#price").val().trim();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if (price.length > 0 && !reg.test(price)) {
        warnTips("price", "单价金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
        return false;
    } else if (Number(price) > 300000000) {
        warnTips("price", "产品单价不允许超过三亿");//文本框ID和提示用语
        return false;
    }
    var num = $form.find("#num").val();
    if (num.length == 0) {
        warnTips("num", "产品数量不能为空");//文本框ID和提示用语
        return false;
    } else {
        var re = /^[1-9]\d*$/;
        if (!re.test(num)) {
            warnTips("num", "数量必须为大于0的整数");//文本框ID和提示用语
            return false;
        }
    }
    if (Number(num) > 100000000) {
        warnTips("num", "产品数量不允许超过一亿");//文本框ID和提示用语
        return false;
    }
    if ((Number(num) * Number(price)) > 300000000) {
        warnTips("num", "产品总金额不允许超过三亿");//文本框ID和提示用语
        return false;
    }
    var deliveryCycle = $form.find("#deliveryCycle").val();
    var belongPlatform = $form.find("#belongPlatform").val();

    //针对科研购
    if (belongPlatform == 3){
        var re = /^[1-9]\d*-[1-9]\d*$/;
        var regExp = /^[1-9]\d*$/ ;
        if(deliveryCycle.length != 0 && (!re.test(deliveryCycle) && !regExp.test(deliveryCycle))){
            warnTips("deliveryCycleDiv","请输入正确的货期");//文本框ID和提示用语
            return false;
        }
        var cycle = deliveryCycle.split("-");
        if (parseInt(cycle[1]) < parseInt(cycle[0])){
            warnTips("deliveryCycleDiv","请输入正确的货期");//文本框ID和提示用语
            return false;
        }
    }else {
        var re = /^[1-9]\d*$/ ;
        if(deliveryCycle.length != 0 && !re.test(deliveryCycle)){
            warnTips("deliveryCycleDiv","货期必须为正整数");//文本框ID和提示用语
            return false;
        }
    }

    $("#deliveryDirect").val($form.find("input[name='deliveryDirectRad']:checked").val());
    if ($form.find("#deliveryDirect").val() == "1") {
        var deliveryDirectComments = $form.find("#deliveryDirectComments").val();
        if (deliveryDirectComments.length == 0) {
            warnTips("deliveryDirectCommentsDiv", "直发原因不允许为空");
            return false;
        }
        if (deliveryDirectComments.length < 2 || deliveryDirectComments.length > 128) {
            warnTips("deliveryDirectCommentsDiv", "直发原因长度应该在2-128个字符之间");
            return false;
        }
    }

    var insideComments = $form.find("#insideComments").val();
    if (insideComments.length > 512) {
        warnTips("insideComments", "超过512个字符");
        return false;
    }

    var goodsComments = $form.find("#goodsComments").val();
    if (goodsComments.length > 512) {
        warnTips("goodsComments", "产品备注长度应该在0-512个字符之间");
        return false;
    }
    if (parent.$("#quotePayMoneForm").find("#saleCustomerNature").val() == "465") {//客户为分销
        //终端信息
        var terminalTraderName = parent.$("#terminalTraderNameDiv").text();
        var dwhTerminalId = parent.$("#quotePayMoneForm").find("#dwhTerminalId").val();
        var unifiedSocialCreditIdentifier = parent.$("#quotePayMoneForm").find("#unifiedSocialCreditIdentifier").val();
        var organizationCode = parent.$("#quotePayMoneForm").find("#organizationCode").val();

        // var provinceId = parent.$("#quotePayMoneForm").find("#provinceId").val();
        // var cityId = parent.$("#quotePayMoneForm").find("#cityId").val();
        // var areaId = parent.$("#quotePayMoneForm").find("#areaId").val();
        // var provinceName = parent.$("#quotePayMoneForm").find("#provinceName").val();
        // var cityName = parent.$("#quotePayMoneForm").find("#cityName").val();
        // var areaName = parent.$("#quotePayMoneForm").find("#areaName").val();

        var provinceId = parent.$('#orderTerminal-province option:selected').val();
        var provinceName = provinceId == 0 ? "" : parent.$('#orderTerminal-province option:selected').text();
        var cityId = parent.$('#orderTerminal-city option:selected').val();
        var cityName = cityId == 0 ? "" : parent.$('#orderTerminal-city option:selected').text();
        var areaId = parent.$('#orderTerminal-area option:selected').val();
        var areaName = areaId == 0 ? "" : parent.$('#orderTerminal-area option:selected').text();


        debugger

        $("#confirmForm").find("#terminalTraderName").val(terminalTraderName);
        $("#confirmForm").find("#dwhTerminalId").val(dwhTerminalId);
        $("#confirmForm").find("#unifiedSocialCreditIdentifier").val(unifiedSocialCreditIdentifier);
        $("#confirmForm").find("#organizationCode").val(organizationCode);

        $("#confirmForm").find("#provinceId").val(provinceId);
        $("#confirmForm").find("#cityId").val(cityId);
        $("#confirmForm").find("#areaId").val(areaId);
        $("#confirmForm").find("#provinceName").val(provinceName);
        $("#confirmForm").find("#cityName").val(cityName);
        $("#confirmForm").find("#areaName").val(areaName);
    }


    $.ajax({
        async: false,
        url: '/orderstream/saleorder/saveSaleorderGoods.do',
        data: $("#confirmForm").serialize(),
        type: "POST",
        dataType: "json",
        success: function (data) {
            if (data.code == 0) {
                saveInsideComments()
            } else {
                layer.alert(data.message, {icon: 2});
            }

        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })

    return false;
}

/**
 * 保存产品备注组件
 */
function saveInsideComments() {
    let labelData = $("#insideComments").attr("label_data");
    // 为空，直接跳转
    if (!labelData) {
        let saleorderId = $("#saleorderId").val();
        let sku = $("#sku").val();
        let skuName = $("#goodsName").val();
        let labelQuery = new Object();
        labelQuery.skuNo = sku;
        labelQuery.skuName = skuName;
        labelQuery.relationId = saleorderId;
        labelQuery.scene = 0;
        let dataSource = new Object();
        dataSource.labelQuery = labelQuery;
        labelData = JSON.stringify(dataSource);
        console.log(labelData);
    }
    $.ajax({
        async:false,
        url:'/order/saleorder/saveInsideComments.do',
        data: labelData,
        type:"POST",
        dataType : "json",
        contentType: "application/json",
        success:function(data){
            if (data.code == 0) {
                layerPFF(window.parent.location.href);
                $("#close-layer").click();
            } else {
                layer.alert(data.message,{ icon: 2 });
            }

        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}



$(function () {

    layui.use('form', function () {
        var form = layui.form;

        $.ajax({
            type : "POST",
            url : page_url+"/goods/brand/getallbrand.do",
            dataType : 'json',
            success : function(data) {
                $option = "<option value=''>请输入品牌</option>";
                $.each(data.listData,function(i,n){
                    $option += "<option value='"+data.listData[i]['brandId']+"'>"+data.listData[i]['brandName']+"</option>";
                });
                $("select[name='goodsBrandId']").html($option);
                var goodsTypeValue=$("#goodsBrandIdValue").val();//假设获取隐藏域中的值
                $("#goodsBrandId option[value='"+goodsTypeValue+"']").attr("selected","selected");
                renderForm();
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

        $('.J-search-btn').click(function () {
            if (!$.trim($(".J-searchContent").val())) {
                $('.J-error').show();//文本框ID和提示用语
                return false;
            }
            var searchURL = '/order/saleorder/addSaleorderGoods.do';

            /*	$("#search").submit();*/

            var link = page_url + searchURL + '?searchContent=' + $('.J-searchContent').val() + '&lendOut=1' + '&saleorderId=0';
            window.location.href = link;
        })

        $('.J-searchContent').on('keyup', function (e) {
            if (e.keyCode == 13 || e.keyCode == 108) {
                e.preventDefault();
                $('.J-search-btn').click();
                return false;
            }
        })

        $('.J-add_saleorder-goods-search').submit(function (e) {
            e.preventDefault();
            return false;
        })
        var goodsTypeValue=$("#goodsTypeValue").val();//假设获取隐藏域中的值
        $("#goodsTypeId option[value='"+goodsTypeValue+"']").attr("selected","selected");
        var zxfunitNameValue=$("#zxfunitNameValue").val();//假设获取隐藏域中的值
        $("#zxfunitName option[value='"+zxfunitNameValue+"']").attr("selected","selected");
        form.render('select');


        function renderForm() {
            form.render('select');
        }
    });

});


// $(function () {
//
//
// })
//文本框验证
/*
function warnTips(obj,id,txt){
	$(obj).find("form :input").parents('li').find('.warning').remove();
	$(obj).find("form :input").removeClass("errorbor");
	$(obj).find("#"+id).siblings('.warning').remove();
	$(obj).find("#"+id).after('<div class="warning">'+txt+'</div>');
	$(obj).find("#"+id).focus();
	$(obj).find("#"+id).addClass("errorbor");
	return false;
}*/

// 内部备注重复点击 比较最后一次点击时间和当前时间
var lastClick;
function lockClick(){
    let nowClick = new Date()
    if (lastClick === undefined) {
        lastClick = nowClick
        return true
    } else {
        if (Math.round((nowClick.getTime() - lastClick.getTime())) > 2000) {
            lastClick = nowClick
            return true
        }
        else {
            lastClick = nowClick
            return false
        }
    }
}

/**
 * 内部备注组件触发事件
 * @param dom 当前dom元素
 */
function insideRemark(dom) {

    if(!lockClick()){
        return;
    }

    let labelData = $(dom).attr('label_data');
    let scene = parseInt($(dom).attr('scene'))
    let remark = $(dom).val();
    let hasRemark = false;
    if (remark) {
        hasRemark = true;
    }
    let skuList = [
        {
            skuId: $('#goodsId').val(),
            skuNo: $('#sku').val(),
            skuName: $('#goodsName').val()
        }
    ]
    if (!checkGoods(skuList, 1)) {
        return;
    }
    let relationId = $("#saleorderId").val();
    new LabelMark({
        el: dom,
        value: labelData,
        url: page_url + '/order/remarkComponent/getInitComponent.do',
        query: {
            scene: 0,
            isAll: 1,
            remark: remark,
            hasRemark: hasRemark,
            relationId: relationId,
            skuList: skuList,
            showComponentIcon: true
        }
    });
}

//新增虚拟商品关联费用编码
function virtureChooseGoods(goodsId, sku, goodsName, proUserName,taxCategoryNo){
    $("#virtureSkuNo",window.parent.document).val(sku);
    $("#virtureSkuId",window.parent.document).val(goodsId);
    $("#virtureSkuName",window.parent.document).val(goodsName);
    $("#virtureProUserName",window.parent.document).val(proUserName);
    $("#virtureTaxCategoryNo",window.parent.document).val(taxCategoryNo);
    var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    parent.layer.close(index);
}

function showDeliveryCycle(obj, index) {
    let directDeliveryTimeStart = $("#directDeliveryTimeStart").val();
    let directDeliveryTimeEnd = $("#directDeliveryTimeEnd").val();
    let commonDeliveryTimeStart = $("#commonDeliveryTimeStart").val();
    let commonDeliveryTimeEnd = $("#commonDeliveryTimeEnd").val();
    if (index == 0) {
        if ($(obj).is(":checked")) {
            if (commonDeliveryTimeStart != '' && commonDeliveryTimeEnd != '') {
                if (commonDeliveryTimeStart == commonDeliveryTimeEnd) {
                    $("#deliveryCycleFlag").text("参考：近90天平均货期" + commonDeliveryTimeStart + "天")
                } else {
                    $("#deliveryCycleFlag").text("参考：近90天平均货期" + commonDeliveryTimeStart + "-" + commonDeliveryTimeEnd + "天")
                }
            } else {
                $("#deliveryCycleFlag").text("近90天无成交，货期请咨询相关人员")
            }
        }
    } else {
        if ($(obj).is(":checked")) {
            if (directDeliveryTimeStart != '' && directDeliveryTimeEnd != '') {
                if (directDeliveryTimeStart == directDeliveryTimeEnd) {
                    $("#deliveryCycleFlag").text("参考：近90天供应链允诺货期" + directDeliveryTimeStart + " 天")
                } else {
                    $("#deliveryCycleFlag").text("参考：近90天供应链允诺货期" + directDeliveryTimeStart + "-" + directDeliveryTimeEnd + " 天")
                }
            } else {
                $("#deliveryCycleFlag").text("近90天无成交，货期请咨询相关人员")
            }
        }
    }
}

