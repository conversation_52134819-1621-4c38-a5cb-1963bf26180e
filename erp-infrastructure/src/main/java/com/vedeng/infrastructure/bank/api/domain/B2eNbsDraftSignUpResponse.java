package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 签收返回的结果对象
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/11
 */
@Data
public class B2eNbsDraftSignUpResponse   extends BankResponse {


    /**
     * 响应体
     */
    private B2eNbsDraftSignUpResponse.B2eNbsDraftSignUpResBody xDataBody;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class B2eNbsDraftSignUpResBody {
        /**
         * 银行渠道交易流水号
         */
        private String svrId;

        /**
         * 客户技术请求流水号，同一客户请勿重复
         */
        private String trnId;

        /**
         * 客户业务请求流水号，同一业务请求请勿重复
         */
        private String insId;

        private List<BillSignResultMap> list;
    }



    @Data
    public static class BillSignResultMap {

        /**
         * 交易流水号
         */
        private String transId;

        /**
         * 错误码
         * 1：成功
         * 0：失败
         */
        private String retCode;

        /**
         * 返回信息
         * 1：交易成功
         * 0：具体失败错误信息
         */
        private String retMsg;

    }
}
