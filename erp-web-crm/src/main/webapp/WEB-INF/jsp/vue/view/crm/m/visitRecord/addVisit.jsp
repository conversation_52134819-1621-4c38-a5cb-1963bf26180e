<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>添加拜访记录</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover">
    <link rel="stylesheet" href="/mstatic/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/mui/mui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/css/pages/visitRecord.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <jsp:include page="./head_import_im.jsp"></jsp:include>
    <input type="hidden" id="visitRecordId" name="visitRecordId" value="${param.visitRecordId}"/>

    <div class="page-wrap" id="page-container" style="padding-bottom: 53px;">
        <div class="page-container">
            <div class="page-main">
                <div class="business-page-wrap">

                    <div class="form-wrap" v-if="!pageLoading" style="padding-top: 10px;">
                        <div class="form-section">
                            <div class="form-section-title">联系人信息</div>
                            <div class="form-card">
                                <ui-form-item label="拜访客户">
                                    <div>
                                        <ui-trader-name :info="{
                                            'traderName': traderName,
                                            'traderId': visitCustomerVo.traderId,
                                            'tycFlag': visitCustomerVo.tycFlag,
                                            'baidu': false
                                        }"></ui-trader-name>
                                    </div>
                                    <div class="trader-detail" v-if="Object.keys(visitCustomerVo).length">
                                        <div class="detail-item">总交易额：{{ visitCustomerVo.historyTransactionAmount || '-' }}</div>
                                        <div class="detail-item">交易次数：{{ visitCustomerVo.historyTransactionNum || '-' }}</div>
                                        <div class="detail-item">最近下单：{{ visitCustomerVo.lastOrderTime || '-' }}</div>
                                    </div>
                                </ui-form-item>
                                <template v-if="!noContract">
                                    <ui-form-item label="手机" no-padding>
                                        <ui-phone-related
                                            v-model="visitFormMobile"
                                            placeholder="建档客户可输入手机号搜索"
                                            :trader-id="traderId"
                                            @change="handlerPhone"
                                        ></ui-phone-related>
                                    </ui-form-item>
                                    <ui-form-item label="固话" no-padding>
                                        <ui-input 
                                            v-model="visitFormTelephone" 
                                            type="tel" 
                                            maxlength="20" 
                                            placeholder="需加上区号，可支持20位数字" 
                                        >
                                            <i v-if="otherContact.length < 5" class="vd-ui_icon icon-add icon" @click="addContact"></i>
                                        </ui-input>
                                    </ui-form-item>
                                    <ui-other-contact-dialog ref="otherContactDialog" @change="handlerAddContact"></ui-other-contact-dialog>
                                    <div class="other-contact-list">
                                        <div class="other-contact-item-wrap" v-for="(item, index) in otherContact"  :key="item.id + '' + index">
                                            <ui-form-item :label="item.label" no-padding>
                                                <div class="other-contact-item">
                                                    <ui-input 
                                                        class="contact-input"
                                                        maxlength="50"
                                                        v-model="item.value"
                                                        @input="handlerAddOnInput"
                                                    ></ui-input>
                                                    <div class="contact-del" @click="handlerAddDelete(index)">
                                                        <i class="vd-ui_icon icon-delete"></i>
                                                    </div>
                                                </div>
                                            </ui-form-item>
                                        </div>
                                    </div>
                                    <ui-form-item label="联系人" must no-padding>
                                        <ui-input 
                                            v-model="visitFormContact" 
                                            placeholder="请输入" 
                                            maxlength="20" 
                                            @change="handlerContact"
                                        ></ui-input>
                                    </ui-form-item>
                                    <ui-form-item label="职位" must no-padding v-show="positionList.length">
                                        <ui-select 
                                            title="职位" 
                                            :list="positionList"
                                            v-model="visitFormDepart" 
                                            placeholder="请选择" 
                                        ></ui-select>
                                    </ui-form-item>
                                </template>
                                <ui-form-item>
                                    <ui-checkbox label="未获得联系人信息" :checked.sync="noContract"></ui-checkbox>
                                </ui-form-item>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="form-section-title">拜访内容</div>
                            <div class="form-card">
                                <ui-form-item label="讲解PPT">
                                    <ui-radio-group
                                        :list="[{label: '是', value: 'Y'}, {label: '否', value: 'N'}]"
                                        v-model="visitFormPPT"
                                        @change="visitFormPPTChange"
                                    ></ui-radio-group>
                                </ui-form-item>
                                <ui-form-item label="沟通记录" must no-padding vertical>
                                    <ui-textarea
                                        maxlength="1000"
                                        show-word-limit
                                        placeholder="可详细总结下拜访的结果或客户的沟通记录，最多不超过1000字"
                                        v-model="visitFormConnect"
                                        height="54px"
                                        height-auto
                                    ></ui-textarea>
                                </ui-form-item>
                            </div>
                        </div>
                    </div>

                    <div class="form-fixed-bottom">
                        <div class="submit-btn padding">
                            <vd-ui-button @click="submit" type="primary">提交</vd-ui-button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <script src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js"></script>
    <script src="/mstatic/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/mui/mui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/pages/visitRecord/addVisit.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
