package com.vedeng.infrastructure.bank.api.util;

import cn.hutool.core.date.DateUtil;
import com.vedeng.infrastructure.bank.api.domain.base.ResponseHeader;
import org.dom4j.Document;
import org.dom4j.Element;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-08-14
 * @Description: TODO
 * @Version: 1.0
 */
public class BankApiXmlUtil {
    /**
     * 构建请求头
     * @param document
     * @param trnCode
     * @param clientId
     * @param userId
     * @param userPswd
     * @return
     */
    public static Element createXmlRootAndHead(Document document, String trnCode, String clientId, String userId, String userPswd) {
        Element root = document.addElement("CMBC")
                .addAttribute("header", "100")
                .addAttribute("version", "100")
                .addAttribute("security", "none")
                .addAttribute("lang", "utf-8")
                .addAttribute("trnCode", trnCode);
        // 获取当前日期时间
        String now = DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss");
        Element requestHeader = root.addElement("requestHeader");
        requestHeader.addElement("dtClient").addText(now);
        requestHeader.addElement("clientId").addText(clientId);
        requestHeader.addElement("userId").addText(userId);
        requestHeader.addElement("userPswd").addText(userPswd);
        requestHeader.addElement("language").addText("utf-8");
        requestHeader.addElement("appId").addText("nsbdes");
        requestHeader.addElement("appVer").addText("201");
        return root;
    }

    /**
     * 响应头header xml转实体类
     *
     * @param responseHeader
     * @return
     */
    public static ResponseHeader getResponseHeader(Element responseHeader) {
        ResponseHeader header = new ResponseHeader();
        Element status = responseHeader.element("status");
        header.setStatus(new ResponseHeader.Status(
                status.elementText("code"),
                status.elementText("severity"),
                status.elementText("message")
        ));
        header.setDtServer(responseHeader.elementText("dtServer"));
        header.setUserKey(responseHeader.elementText("userKey"));
        header.setDtDead(responseHeader.elementText("dtDead"));
        header.setLanguage(responseHeader.elementText("language"));
        return header;
    }
}
