<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.customerbillperiod.dao.CustomerBillPeriodRiskManagementDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.customerbillperiod.model.CustomerBillPeriodRiskManagementDetail">
    <id column="BILL_PERIOD_RISK_MANAGEMENT_DETAIL_ID" jdbcType="BIGINT" property="billPeriodRiskManagementDetailId" />
    <result column="BILL_PERIOD_RISK_MANAGEMENT_CODE" jdbcType="VARCHAR" property="billPeriodRiskManagementCode" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="CUSTOMER_ID" jdbcType="BIGINT" property="customerId" />
    <result column="BILL_PERIOD_USE_DETAIL_ID" jdbcType="BIGINT" property="billPeriodUseDetailId" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="UNRETURNED_AMOUNT" jdbcType="DECIMAL" property="unreturnedAmount" />
    <result column="TYPE" jdbcType="TINYINT" property="type" />
    <result column="RELATED_ID" jdbcType="BIGINT" property="relatedId" />
    <result column="PARENT_MANAGEMENT_DETAIL_ID" jdbcType="BIGINT" property="parentManagementDetailId" />
    <result column="SETTLEMENT_PERIOD" jdbcType="INTEGER" property="settlementPeriod" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    BILL_PERIOD_RISK_MANAGEMENT_DETAIL_ID, BILL_PERIOD_RISK_MANAGEMENT_CODE, COMPANY_ID, 
    CUSTOMER_ID, BILL_PERIOD_USE_DETAIL_ID, AMOUNT, UNRETURNED_AMOUNT, `TYPE`, RELATED_ID, 
    PARENT_MANAGEMENT_DETAIL_ID, SETTLEMENT_PERIOD, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL
    where BILL_PERIOD_RISK_MANAGEMENT_DETAIL_ID = #{billPeriodRiskManagementDetailId,jdbcType=BIGINT}
  </select>
  <select id="geNewestBillPeriodManagementCodeByOrderId" resultType="java.lang.String">
    select md.BILL_PERIOD_RISK_MANAGEMENT_CODE
    from T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL md
           join T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud on md.BILL_PERIOD_USE_DETAIL_ID = ud.BILL_PERIOD_USE_DETAIL_ID
    where ud.COMPANY_ID = #{companyId}
      and ud.CUSTOMER_ID = #{customerId}
      and ud.RELATED_ID = #{orderId} and ud.USE_TYPE = 1
    order by md.BILL_PERIOD_RISK_MANAGEMENT_DETAIL_ID desc limit 1
  </select>
  <select id="getRiskManagementDetailListUnreturnedByOrderId"
          resultType="com.vedeng.customerbillperiod.model.CustomerBillPeriodRiskManagementDetail">
    select md.*
    from T_CUSTOMER_BILL_PERIOD_USE_DETAIL ud
           join T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL md
                on ud.BILL_PERIOD_USE_DETAIL_ID = md.BILL_PERIOD_USE_DETAIL_ID
    where ud.USE_TYPE = 1
      and ud.COMPANY_ID = #{companyId}
      and ud.CUSTOMER_ID = #{customerId}
      and ud.RELATED_ID = #{orderId}
      and md.TYPE = 1
      and md.UNRETURNED_AMOUNT > 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL
    where BILL_PERIOD_RISK_MANAGEMENT_DETAIL_ID = #{billPeriodRiskManagementDetailId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BILL_PERIOD_RISK_MANAGEMENT_DETAIL_ID" keyProperty="billPeriodRiskManagementDetailId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodRiskManagementDetail" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL (BILL_PERIOD_RISK_MANAGEMENT_CODE, COMPANY_ID, 
      CUSTOMER_ID, BILL_PERIOD_USE_DETAIL_ID, AMOUNT, 
      UNRETURNED_AMOUNT, `TYPE`, RELATED_ID, 
      PARENT_MANAGEMENT_DETAIL_ID, SETTLEMENT_PERIOD, 
      ADD_TIME, MOD_TIME)
    values (#{billPeriodRiskManagementCode,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{customerId,jdbcType=BIGINT}, #{billPeriodUseDetailId,jdbcType=BIGINT}, #{amount,jdbcType=DECIMAL}, 
      #{unreturnedAmount,jdbcType=DECIMAL}, #{type,jdbcType=TINYINT}, #{relatedId,jdbcType=BIGINT}, 
      #{parentManagementDetailId,jdbcType=BIGINT}, #{settlementPeriod,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{modTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="BILL_PERIOD_RISK_MANAGEMENT_DETAIL_ID" keyProperty="billPeriodRiskManagementDetailId" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodRiskManagementDetail" useGeneratedKeys="true">
    insert into T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billPeriodRiskManagementCode != null">
        BILL_PERIOD_RISK_MANAGEMENT_CODE,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="billPeriodUseDetailId != null">
        BILL_PERIOD_USE_DETAIL_ID,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="unreturnedAmount != null">
        UNRETURNED_AMOUNT,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="parentManagementDetailId != null">
        PARENT_MANAGEMENT_DETAIL_ID,
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billPeriodRiskManagementCode != null">
        #{billPeriodRiskManagementCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="billPeriodUseDetailId != null">
        #{billPeriodUseDetailId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unreturnedAmount != null">
        #{unreturnedAmount,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="parentManagementDetailId != null">
        #{parentManagementDetailId,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.customerbillperiod.model.CustomerBillPeriodRiskManagementDetail">
    update T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL
    <set>
      <if test="billPeriodRiskManagementCode != null">
        BILL_PERIOD_RISK_MANAGEMENT_CODE = #{billPeriodRiskManagementCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="billPeriodUseDetailId != null">
        BILL_PERIOD_USE_DETAIL_ID = #{billPeriodUseDetailId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="unreturnedAmount != null">
        UNRETURNED_AMOUNT = #{unreturnedAmount,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="parentManagementDetailId != null">
        PARENT_MANAGEMENT_DETAIL_ID = #{parentManagementDetailId,jdbcType=BIGINT},
      </if>
      <if test="settlementPeriod != null">
        SETTLEMENT_PERIOD = #{settlementPeriod,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
    </set>
    where BILL_PERIOD_RISK_MANAGEMENT_DETAIL_ID = #{billPeriodRiskManagementDetailId,jdbcType=BIGINT}
  </update>
</mapper>