package com.vedeng.price.dto;

import java.math.BigDecimal;
import java.util.List;

public class SkuPriceInfoDetailResponseDto {

    private String skuNo;

    private BigDecimal marketPrice;//市场价

    private BigDecimal terminalPrice;//终端价

    private BigDecimal distributionPrice;//经销价

    private BigDecimal electronicCommercePrice;//电商价

    private BigDecimal researchTerminalPrice;//科研终端价

    private List<SkuPriceInfoPurchaseDto> purchaseList;//采购价
    
    /**销售价是否含运费 ,0 未维护 1 含运费 2 不含运费*/
    private Integer saleContainsFee;
    
    /**成本价是否含运费 ,0 未维护 1 含运费 2 不含运费*/
    private Integer purchseContainsFee;

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getTerminalPrice() {
        return terminalPrice;
    }

    public void setTerminalPrice(BigDecimal terminalPrice) {
        this.terminalPrice = terminalPrice;
    }

    public BigDecimal getDistributionPrice() {
        return distributionPrice;
    }

    public void setDistributionPrice(BigDecimal distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    public List<SkuPriceInfoPurchaseDto> getPurchaseList() {
        return purchaseList;
    }

    public void setPurchaseList(List<SkuPriceInfoPurchaseDto> purchaseList) {
        this.purchaseList = purchaseList;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public BigDecimal getResearchTerminalPrice() {
        return researchTerminalPrice;
    }

    public void setResearchTerminalPrice(BigDecimal researchTerminalPrice) {
        this.researchTerminalPrice = researchTerminalPrice;
    }
    public BigDecimal getElectronicCommercePrice() {
        return electronicCommercePrice;
    }

    public void setElectronicCommercePrice(BigDecimal electronicCommercePrice) {
        this.electronicCommercePrice = electronicCommercePrice;
    }

	public Integer getSaleContainsFee() {
		return saleContainsFee;
	}

	public void setSaleContainsFee(Integer saleContainsFee) {
		this.saleContainsFee = saleContainsFee;
	}

	public Integer getPurchseContainsFee() {
		return purchseContainsFee;
	}

	public void setPurchseContainsFee(Integer purchseContainsFee) {
		this.purchseContainsFee = purchseContainsFee;
	}
    
    
}
