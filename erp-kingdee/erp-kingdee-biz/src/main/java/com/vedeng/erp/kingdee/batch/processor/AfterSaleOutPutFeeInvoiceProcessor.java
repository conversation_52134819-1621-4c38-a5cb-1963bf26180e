package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesGoodsDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeService;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023−02-02 下午1:43
 * @description 销项费用发票（专票、普票）组装数据
 */
@Slf4j
@Service
public class AfterSaleOutPutFeeInvoiceProcessor extends BaseProcessor<BatchInvoiceDto, BatchReceiveFeeDto> {

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Autowired
    private KingDeeReceiveFeeService kingDeeReceiveFeeService;

    @Autowired
    private BatchAfterSalesGoodsDtoMapper batchAfterSalesGoodsDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

    /**
     * 金蝶销售售后手续费-费用编码"035"
     */
    private static final String UNIT_KING_DEE_NO = "035";

    @Override
    public BatchReceiveFeeDto doProcess(BatchInvoiceDto batchInvoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        //组合对象
        BatchReceiveFeeDto batchReceiveFeeDto = new BatchReceiveFeeDto();
        //专票对象
        OutPutFeeSpecialInvoiceDto specialInvoiceDto;
        //普票对象
        OutPutFeePlainInvoiceDto plainInvoiceDto;
        //判断是否专票
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchInvoiceDto.getInvoiceType());
        // 获取应收单
        List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable = kingDeeReceiveFeeService.getKingDeeReceiveFee(batchInvoiceDto.getInvoiceId().toString());
        if (CollUtil.isEmpty(kingDeeSaleReceivable)) {
            log.info("销售售后费用应收单,数据不存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        log.info("销售售后费用应收单{}", JSON.toJSONString(batchInvoiceDto));
        if (batchInvoiceDto.getRelatedId() == null) {
            log.info("发票id为{},无法找到对应订单,请检查数据", batchInvoiceDto.getInvoiceId());
            return null;
        }
        if (batchInvoiceDto.getAfterSalesId() == null) {
            log.info("发票id为{},无法找到对应售后单,请检查数据", batchInvoiceDto.getInvoiceId());
            return null;
        }
        //原始订单号
        String saleorderNo = batchInvoiceDtoMapper.findSaleorderNoByRelatedId(batchInvoiceDto.getRelatedId());
        //售后对象
        BatchAfterSalesDto afterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesId(batchInvoiceDto.getAfterSalesId());
        String afterSaleOrderNo = afterSalesDto.getAfterSalesNo();
        Boolean isThirdDeal = false;
        if ("537".equals(Convert.toStr(afterSalesDto.getSubjectType()))){
            isThirdDeal = true;
            batchInvoiceDto.setTraderCustomerId(batchAfterSalesDtoMapper.getTraderCustomerIdByAfterSalesId(batchInvoiceDto.getAfterSalesId()));
        }
        DecimalFormat decimalFormat = new DecimalFormat(("0.00#"));
        //税率
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        //发票详情
        List<BatchInvoiceDetailDto> invoiceDetailList = batchInvoiceDetailDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        if (isSpecialInvoice) {
            specialInvoiceDto = new OutPutFeeSpecialInvoiceDto();
            specialInvoiceDto.setFRedBlue("0");
            this.outPutFeeSpecialInvoice(specialInvoiceDto, batchInvoiceDto, invoiceDetailList
                    , taxRate, saleorderNo, afterSaleOrderNo, kingDeeSaleReceivable,isThirdDeal);
            batchReceiveFeeDto.setOutPutFeeSpecialInvoiceDto(specialInvoiceDto);
        } else {
            plainInvoiceDto = new OutPutFeePlainInvoiceDto();
            plainInvoiceDto.setFRedBlue("0");
            this.outPutFeePlainInvoice(plainInvoiceDto, batchInvoiceDto, invoiceDetailList
                    , taxRate, saleorderNo, afterSaleOrderNo, kingDeeSaleReceivable,isThirdDeal);
            batchReceiveFeeDto.setOutPutFeePlainInvoiceDto(plainInvoiceDto);
        }
        return batchReceiveFeeDto;
    }

    /**
     * 销项费用专票对象
     */
    private void outPutFeeSpecialInvoice(OutPutFeeSpecialInvoiceDto specialInvoiceDto, BatchInvoiceDto batchInvoiceDto
            , List<BatchInvoiceDetailDto> invoiceDetailList
            , String taxRate
            , String saleorderNo
            , String afterSaleOrderNo
            , List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable
            , Boolean isThirdDeal) {
        specialInvoiceDto.setFid("0");
        specialInvoiceDto.setFdate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getAddTime())));
        specialInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getAddTime())));
        specialInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        specialInvoiceDto.setFcontactunit(Convert.toStr(batchInvoiceDto.getTraderCustomerId()));
        specialInvoiceDto.setFQzokBddjtid(Convert.toStr(batchInvoiceDto.getInvoiceId()));
        specialInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        specialInvoiceDto.setFQzokPzgsywdh(afterSaleOrderNo);


        for (BatchInvoiceDetailDto d : invoiceDetailList) {
            //发票明细
            OutPutFeeSpecialInvoiceDetailDto detailDto = new OutPutFeeSpecialInvoiceDetailDto();

            detailDto.setFexpenseid(UNIT_KING_DEE_NO);
            detailDto.setFpriceqty(Convert.toStr(d.getNum()));
            detailDto.setFauxtaxprice(d.getTotalAmount().divide(d.getNum(),6,RoundingMode.HALF_UP));
            detailDto.setFtaxrate(taxRate);
            detailDto.setFQzokBddjhid(Convert.toStr(d.getInvoiceDetailId()));
            detailDto.setFQzokYsddh(isThirdDeal ? afterSaleOrderNo : saleorderNo);
            detailDto.setFQzokYwlx(isThirdDeal ?  "第三方安调" : "销售售后");
            detailDto.setFQzokGsywdh(afterSaleOrderNo);
            //特殊产品（手续费）类型的SKU订货号 VDERP-14885
            BatchAfterSalesGoodsDto afterSalesGoodsDto =  batchAfterSalesGoodsDtoMapper.getSpecialGoods(batchInvoiceDto.getAfterSalesId());
            if (ObjectUtil.isNull(afterSalesGoodsDto) && StringUtils.isBlank(afterSalesGoodsDto.getSku())){
                log.info("无法查询到销售售后手续费关联的物料编码,售后单号:{},售后id:{}",afterSaleOrderNo,batchInvoiceDto.getAfterSalesId());
                return;
            }
            detailDto.setF_QZOK_WLBM(afterSalesGoodsDto.getSku());
            specialInvoiceDto.getFSALEEXINVENTRY().add(detailDto);
            //关联关系
            OutPutFeeSpecialInvoiceDetailLinkDto detailLinkDto = new OutPutFeeSpecialInvoiceDetailLinkDto();
            KingDeeReceiveQueryResultDto resultDto = kingDeeSaleReceivable.stream()
                    .filter(s -> s.getF_QZOK_BDDJHID().equals(Convert.toStr(d.getInvoiceDetailId())))
                    .findFirst().orElseThrow(() -> new KingDeeException("销售售后销项费用专票，未能关联上应收单对象，发票id：" + batchInvoiceDto.getInvoiceId()));
            //金蝶费用应收单表头ID
            detailLinkDto.setFsaleexinventryLinkFsbillid(resultDto.getFID());
            //金蝶费用应收单行ID
            detailLinkDto.setFsaleexinventryLinkFsid(resultDto.getFEntityDetail_FEntryId());
            detailLinkDto.setFsaleexinventryLinkFallamountforold(d.getTotalAmount().abs());
            detailLinkDto.setFsaleexinventryLinkFallamountfor(d.getTotalAmount().abs());
            detailDto.getFSALEEXINVENTRY_LINK().add(detailLinkDto);
        }

    }

    /**
     * 销项费用普票对象
     */
    private void outPutFeePlainInvoice(OutPutFeePlainInvoiceDto plainInvoiceDto
            , BatchInvoiceDto batchInvoiceDto
            , List<BatchInvoiceDetailDto> invoiceDetailList
            , String taxRate
            , String saleorderNo
            , String afterSaleOrderNo
            , List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable
            , Boolean isThirdDeal) {
        plainInvoiceDto.setFid("0");
        plainInvoiceDto.setFdate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getAddTime())));
        plainInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(DateUtil.date(batchInvoiceDto.getAddTime())));
        plainInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        plainInvoiceDto.setFcontactunit(Convert.toStr(batchInvoiceDto.getTraderCustomerId()));
        plainInvoiceDto.setFQzokBddjtid(Convert.toStr(batchInvoiceDto.getInvoiceId()));
        plainInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
        plainInvoiceDto.setFQzokPzgsywdh(afterSaleOrderNo);
        for (BatchInvoiceDetailDto d : invoiceDetailList) {
            //发票明细
            OutPutFeePlainInvoiceDetailDto detailDto = new OutPutFeePlainInvoiceDetailDto();

            detailDto.setFexpenseid(UNIT_KING_DEE_NO);
            detailDto.setFpriceqty(Convert.toStr(d.getNum()));
            detailDto.setFtaxrate(taxRate);
            detailDto.setFauxtaxprice(d.getTotalAmount().divide(d.getNum(),6,RoundingMode.HALF_UP));
            detailDto.setFQzokBddjhid(Convert.toStr(d.getInvoiceDetailId()));
            detailDto.setFQzokYsddh(isThirdDeal ? afterSaleOrderNo : saleorderNo);
            detailDto.setFQzokYwlx(isThirdDeal ?  "第三方安调" : "销售售后");
            detailDto.setFQzokGsywdh(afterSaleOrderNo);
            //特殊产品（手续费）类型的SKU订货号 VDERP-14885
            BatchAfterSalesGoodsDto afterSalesGoodsDto =  batchAfterSalesGoodsDtoMapper.getSpecialGoods(batchInvoiceDto.getAfterSalesId());
            if (ObjectUtil.isNull(afterSalesGoodsDto) && StringUtils.isBlank(afterSalesGoodsDto.getSku())){
                log.info("无法查询到销售售后手续费关联的物料编码,售后单号:{},售后id:{}",afterSaleOrderNo,batchInvoiceDto.getAfterSalesId());
                return;
            }
            detailDto.setF_QZOK_WLBM(afterSalesGoodsDto.getSku());
            plainInvoiceDto.getFSALEEXINVENTRY().add(detailDto);
            //关联关系
            OutPutFeePlainInvoiceDetailLinkDto detailLinkDto = new OutPutFeePlainInvoiceDetailLinkDto();
            KingDeeReceiveQueryResultDto resultDto = kingDeeSaleReceivable.stream()
                    .filter(s -> s.getF_QZOK_BDDJHID().equals(Convert.toStr(d.getInvoiceDetailId())))
                    .findFirst().orElseThrow(() -> new KingDeeException("销售售后销项费用普票，未能关联上应收单对象，发票id：" + batchInvoiceDto.getInvoiceId()));
            //金蝶费用应收单表头ID
            detailLinkDto.setFsaleexinventryLinkFsbillid(resultDto.getFID());
            //金蝶费用应收单行ID
            detailLinkDto.setFsaleexinventryLinkFsid(resultDto.getFEntityDetail_FEntryId());
            detailLinkDto.setFsaleexinventryLinkFallamountforold(d.getTotalAmount().abs());
            detailLinkDto.setFsaleexinventryLinkFallamountfor(d.getTotalAmount().abs());
            detailDto.getFSALEEXINVENTRY_LINK().add(detailLinkDto);
        }
    }
}
