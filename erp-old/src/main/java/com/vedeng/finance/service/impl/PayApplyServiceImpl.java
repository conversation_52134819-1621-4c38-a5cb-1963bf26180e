package com.vedeng.finance.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.common.constants.Contant;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.dao.PayApplyMapper;
import com.vedeng.finance.dto.PayVedengBankDto;
import com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankTransferRecord;
import com.vedeng.infrastructure.bank.api.mapper.MinshengBankTransferRecordMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.TraderSupplier;
import net.sf.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.finance.model.BankBill;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.model.PayApplyDetail;
import com.vedeng.finance.service.PayApplyService;

import net.sf.json.JSONObject;


@Service("payApplyService")
public class PayApplyServiceImpl extends BaseServiceimpl implements PayApplyService{
	public static Logger logger = LoggerFactory.getLogger(PayApplyServiceImpl.class);

	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	// add by Tomcat.Hui 2019/9/16 13:18 .Desc: VDERP-1215 付款申请增加批量操作功能. start
	@Autowired
	@Qualifier("payApplyMapper")
	private PayApplyMapper payApplyMapper;
	// add by Tomcat.Hui 2019/9/16 13:18 .Desc: VDERP-1215 付款申请增加批量操作功能. end

	@Resource
	private TraderSupplierMapper traderSupplierMapper;

	@Resource
	private BuyorderMapper buyorderMapper;

	@Resource
	private MinshengBankTransferRecordMapper minshengBankTransferRecordMapper;
	@Override
	public List<PayApply> getPayApplyList(PayApply payApply) {
		List<PayApply> list = payApplyMapper.getPayApplyList(payApply);
		/**迁移db
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<PayApply>>> TypeRef = new TypeReference<ResultInfo<List<PayApply>>>() {};
		String url=httpUrl + "finance/payapply/getpayapplylist.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, payApply,clientId,clientKey, TypeRef);
			list = (List<PayApply>) result.getData();
			**/
			// 操作人信息补充
			if (list.size() > 0) {
				List<Integer> userIds = new ArrayList<>();
				for (PayApply b : list) {
					if (b.getCreator() > 0) {
						userIds.add(b.getCreator());
					}
					if (b.getUpdater() > 0) {
						userIds.add(b.getUpdater());
					}
				}

				if (userIds.size() > 0) {
					List<User> userList = userMapper.getUserByUserIds(userIds);

					for (PayApply b : list) {
						for (User u : userList) {
							if (u.getUserId().equals(b.getCreator())) {
								b.setCreatorName(u.getUsername());
							}
							if (u.getUserId().equals(b.getUpdater())) {
								b.setUpdaterName(u.getUsername());
							}
						}
					}
				}
			}
			/**
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}**/
		return list;
	}

	@Override
	public ResultInfo<?> payApplyNoPass(PayApply payApply) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<PayApply>> TypeRef = new TypeReference<ResultInfo<PayApply>>() {};
		String url=httpUrl + "finance/payapply/payapplynopass.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, payApply,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public ResultInfo<?> payApplyPass(PayApply payApply) {
		ResultInfo<?> result = new ResultInfo<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<PayApply>> TypeRef = new TypeReference<ResultInfo<PayApply>>() {};
		String url=httpUrl + "finance/payapply/payapplypass.htm";
		try {
			result = (ResultInfo<?>) HttpClientUtils.post(url, payApply,clientId,clientKey, TypeRef);
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return result;
	}

	@Override
	public Map<String, Object> getPayApplyListPage(HttpServletRequest request, PayApply payApply, Page page) {
		Map<String,Object> map = new HashMap<>();
		List<PayApply> list = null;
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<Map<String,Object>>> TypeRef = new TypeReference<ResultInfo<Map<String,Object>>>() {};
			String url = httpUrl + "finance/payapply/getpayapplylistpage.htm";

			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, payApply,clientId,clientKey, TypeRef,page);
			if(result!=null && result.getCode() == 0){
				Map<String,Object> result_map = (Map<String,Object>)result.getData();
				if(result_map!=null && result_map.size()>0){
					map = new HashMap<String,Object>();

					net.sf.json.JSONArray json = null;
					String str = result_map.get("payApplyList").toString();
					json = net.sf.json.JSONArray.fromObject(str);
					List<PayApply> payApplyList = new ArrayList<>();
					for(int i=0;i<json.size();i++){
					    Map<String,Class<?>> classMap = new HashMap<String,Class<?>>();
					    classMap.put("bankBillList", BankBill.class);
					    JSONObject jsonObject = (JSONObject) json.get(i);
					    PayApply payApplys = (PayApply) JSONObject.toBean(jsonObject, PayApply.class,classMap);
					    payApplyList.add(payApplys);
					}
					map.put("payApplyList", payApplyList);

					// 申请人信息补充
					if (payApplyList.size() > 0) {
						List<Integer> userIds = new ArrayList<>();
						for (PayApply b : payApplyList) {
							if (b.getCreator() > 0) {
								userIds.add(b.getCreator());
							}
						}

						if (userIds.size() > 0) {
							List<User> userList = userMapper.getUserByUserIds(userIds);

							for (PayApply b : payApplyList) {
								for (User u : userList) {
									if (u.getUserId().equals(b.getCreator())) {
										b.setCreatorName(u.getUsername());
									}
								}
							}
						}
					}

					payApply = (PayApply) JSONObject.toBean(JSONObject.fromObject(result_map.get("payApply")), PayApply.class);
					map.put("payApply", payApply);

					page = result.getPage();
					map.put("page", page);

				}
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return map;
	}
	public List<PayApply> getPayApplyListPage(PayApply payApply, Page page) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("page", page);
		map.put("payApply", payApply);
		//付款申请总额
//		BigDecimal payApplyTotalAmount = payApplyMapper.getPayApplyTotalAmount(map);
//		payApply.setPayApplyTotalAmount(payApplyTotalAmount);

		//付款申请付款总额
//		BigDecimal payApplyPayTotalAmount = payApplyMapper.getPayApplyPayTotalAmount(map);
//		payApply.setPayApplyPayTotalAmount(payApplyPayTotalAmount);

//		map.put("payApply", payApply);

		List<PayApply> payApplyList =  payApplyMapper.getPayApplyListPage(map);
		if(payApply.getSearch() == null){
			if(null != payApplyList){
				for(PayApply pa:payApplyList){
					//如果已制单，去匹配流水
					if(pa.getIsBill()!=0 && pa.getTraderMode().equals(521) && pa.getValidStatus().equals(0)){
						List<BankBill> selectByBankBillId = matchBankBill(pa.getAmount(),pa.getTraderName(),
								DateUtil.convertString(payApply.getSearchPayBegintime() == null ? 0L : payApply.getSearchPayBegintime(), "yyyyMMdd"),
								DateUtil.convertString(payApply.getSearchPayEndtime() == null ? 0L : payApply.getSearchPayEndtime(), "yyyyMMdd"));
						if(null!=selectByBankBillId && selectByBankBillId.size()>0){
							pa.setBankBillList(selectByBankBillId);
						}
					}
				}
			}
		}
		if(payApply.getValidStatus() !=null && payApply.getValidStatus() ==0){
			payApply.setPayApplyTodoCount(page.getTotalRecord());
		}else{
			Map<String, Object> mapToDo = new HashMap<String, Object>();
			Page toDoPage = Page.newBuilder(page.getPageNo(), page.getPageSize(), page.getSearchUrl());
			mapToDo.put("page", toDoPage);
			payApply.setValidStatus(0);//设置成审核中进行查询
			mapToDo.put("payApply", payApply);
			payApplyMapper.getPayApplyListPage(mapToDo);
			payApply.setPayApplyTodoCount(toDoPage.getTotalRecord());
		}

		//map.put("payApplyList", JSONArray.fromObject(payApplyList).toString());
		return payApplyList;
	}
	public List<BankBill> matchBankBill (BigDecimal amt,String accName1, String searchBeginTime, String searchEndTime){
		List<BankBill> data = new ArrayList<>();
		String accName = null;
		data = payApplyMapper.getMatchInfo(amt,accName1,searchBeginTime,searchEndTime);
		return data;
	}
	@Override
	public Map<String, Object> getPayApplyListPageNew(HttpServletRequest request, PayApply payApply, Page page) {


		Map<String,Object> map = new HashMap<>();
		List<PayApply> list = null;
		try {
			// 定义反序列化 数据格式
			//final TypeReference<ResultInfo<Map<String,Object>>> TypeRef = new TypeReference<ResultInfo<Map<String,Object>>>() {};
			//String url = httpUrl + "finance/payapply/getpayapplylistpage.htm";

//			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, payApply,clientId,clientKey, TypeRef,page);
//			if(result!=null && result.getCode() == 0){
				//Map<String,Object> result_map =;
				//if(result_map!=null && result_map.size()>0){
					map = new HashMap<String,Object>();
//
//					net.sf.json.JSONArray json = null;
//					String str = result_map.get("payApplyList").toString();
//					json = net.sf.json.JSONArray.fromObject(str);
			//payApply.setValidUserName("");
					List<PayApply> payApplyList =getPayApplyListPage(payApply,page);

			setMSApplyInfo(payApplyList);

//					for(int i=0;i<json.size();i++){
//						Map<String,Class<?>> classMap = new HashMap<String,Class<?>>();
//						classMap.put("bankBillList", BankBill.class);
//						JSONObject jsonObject = (JSONObject) json.get(i);
//						PayApply payApplys = (PayApply) JSONObject.toBean(jsonObject, PayApply.class,classMap);
//						payApplyList.add(payApplys);
//					}
					map.put("payApplyList", payApplyList);

					// 申请人信息补充
//					if (payApplyList.size() > 0) {
//						List<Integer> userIds = new ArrayList<>();
//						for (PayApply b : payApplyList) {
//							if (b.getCreator() > 0) {
//								userIds.add(b.getCreator());
//							}
//						}
//
//						if (userIds.size() > 0) {
//							List<User> userList = userMapper.getUserByUserIds(userIds);
//
//							for (PayApply b : payApplyList) {
//								for (User u : userList) {
//									if (u.getUserId().equals(b.getCreator())) {
//										b.setCreatorName(u.getUsername());
//									}
//								}
//							}
//						}
//					}

					//payApply = (PayApply) JSONObject.toBean(JSONObject.fromObject(result_map.get("payApply")), PayApply.class);
					map.put("payApply", payApply);

					//page = result.getPage();
					map.put("page", page);

				//}
			//}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return map;
	}

	@Override
	public Map<String, BigDecimal> getPassedByBuyorderGoodsId(Integer buyorderGoodsId) {
		Map<String,BigDecimal> resultMap = new HashMap<>();
		Map<String, BigDecimal> map = new HashMap<>();
		try {
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<Map<String, BigDecimal>>> TypeRef = new TypeReference<ResultInfo<Map<String, BigDecimal>>>() {};
			String url = httpUrl + "finance/payapply/getpassedbybuyordergoodsid.htm";

			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, buyorderGoodsId,clientId,clientKey, TypeRef);
			resultMap = (Map<String, BigDecimal>) result.getData();
			if (null != resultMap) {
				map.put("passedNum", resultMap.get("passedNum"));
				map.put("passedAmount", resultMap.get("passedAmount"));
			} else {
				map.put("passedNum", new BigDecimal(0.00));
				map.put("passedAmount", new BigDecimal(0.00));
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return map;
	}

	/** @description: getPayApplyInfo.
	 * @notes: VDERP-1215 付款申请增加批量操作功能,直接查询,不再调用dbcenter.
	 * @author: Tomcat.Hui.
	 * @date: 2019/9/16 13:19.
	 * @param payApplyId
	 * @return: com.vedeng.finance.model.PayApply.
	 * @throws: .
	 */
	@Override
	public PayApply getPayApplyInfo(Integer payApplyId) {
		return payApplyMapper.selectByPrimaryKey(payApplyId);
	}

	@Override
	public List<PayApplyDetail> getPayApplyDetailList(Integer payApplyId) {
		List<PayApplyDetail> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<PayApplyDetail>>> TypeRef = new TypeReference<ResultInfo<List<PayApplyDetail>>>() {};
		String url=httpUrl + "finance/payapply/getpayapplydetaillist.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, payApplyId,clientId,clientKey, TypeRef);
			list = (List<PayApplyDetail>) result.getData();
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return list;
	}

	@Override
	public PayApply getPayApplyMaxRecord(PayApply payApply) {
		return payApplyMapper.getPayApplyMaxRecord(payApply);
	}

	@Override
	public int updatePayStutas(PayApply payApply) {
		return payApplyMapper.updatePayStutas(payApply);
	}

	@Override
	public int updateBillMethod(PayApply payApply) {
		return payApplyMapper.updateBillMethod(payApply);
	}

	@Override
	public PayApply getPayApplyRecord(PayApply payApply) {
		return payApplyMapper.getPayApplyRecord(payApply);
	}

	/**
	 * @Description: 根据relatedId获取当前最新申请表得申请付款状态
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2020/2/26
	 */
	@Override
	public int getPayStatusBill(Integer payType, Integer payApplyId) {
		PayApply payApply1 = new PayApply();
		payApply1.setPayType(payType);//采购
		payApply1.setPayApplyId(payApplyId);
		PayApply payApply2 = payApplyMapper.getPayApplyRecord(payApply1);
		if (payApply2 != null) {
			if (payApply2.getPayStatus().equals(1)) {
				return -1;
			}
		}
		return 0;
	}


	/**
	 * @Description: 修改申请付款状态
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2020/2/26
	 */
	@Override
	public int updatePayStatusBill(Integer payType, Integer payStatus, Integer payApplyId){
		PayApply payApply1 = new PayApply();
		payApply1.setPayType(payType);//采购
		payApply1.setPayStatus(payStatus);//已付款
		payApply1.setPayApplyId(payApplyId);
		int payApply2 = payApplyMapper.updatePayStutas(payApply1);
		return payApply2;
	}

	/**
	 * 补充付款申请对应的工程师信息
	 * @param afterSalesId,payApply
	 * @param payApply
	 * @return
	 */
	@Override
	public PayApply getPayApplyMoreInfo(Integer afterSalesId,PayApply payApply) {
		if (null != afterSalesId){
			PayApply pay = payApplyMapper.getPayApplyMoreInfo(afterSalesId);
			if (null != pay){
				payApply.setMobile(pay.getMobile());
				payApply.setCard(pay.getCard());
			}
		}
		return payApply;
	}

	@Override
	public Map<String, BigDecimal> queryPassedByBuyorderExpenseGoodsId(Integer buyorderExpenseGoodsId) {
		Map<String,BigDecimal> map = payApplyMapper.queryPassedByBuyorderExpenseGoodsId(buyorderExpenseGoodsId);
		if(map == null){
			map = new HashMap<String,BigDecimal>();
			map.put("passedPayApplyNum", new BigDecimal(0.00));
			map.put("passedPayApplyAmount", new BigDecimal(0.00));
		}
		return map;
	}

	@Override
	public ResultInfo verifyPayApplyInfo(PayApply payApply,User user,Integer payApplyType) {
		ResultInfo resultInfo = new ResultInfo(0,"校验成功");
		PayApply payApplyPre = new PayApply();
		PayApply payApplyVirture = new PayApply();
		//判断订单是否被关闭
		if (payApply.getRelatedId() != null){
			Integer status = buyorderMapper.getStatusById(payApply.getRelatedId());
			if (status.equals(3)){
				return new ResultInfo(-1,"当前采购单已关闭，请刷新采购单详情");
			}
		}
		if(ErpConst.ZERO.equals(payApplyType)) {
			// 获取采购单待审核付款列表校验是否存在其他付款申请
			payApplyPre.setCompanyId(user.getCompanyId());
			payApplyPre.setPayType(SysOptionConstant.ID_517);// 采购付款申请
			payApplyPre.setRelatedId(payApply.getRelatedId());
			payApplyPre.setValidStatus(ErpConst.ZERO);
			List<PayApply> buyorderPayApplyList = getPayApplyList(payApplyPre);
			if (buyorderPayApplyList.size() > 0) {
				return new ResultInfo(-1, "当前有待审核的采购单付款申请，请不要重复申请");
			}
		}
		if(ErpConst.ONE.equals(payApplyType) || (payApply.getBuyorderExpenseRelatedId() != null
				&& !ErpConst.ZERO.equals(payApply.getBuyorderExpenseRelatedId()))) {
			//获取费用单待审核付款列表校验是否存在其他付款申请
			payApplyVirture.setCompanyId(user.getCompanyId());
			payApplyVirture.setPayType(SysOptionConstant.ID_4125);
			payApplyVirture.setRelatedId(payApply.getBuyorderExpenseRelatedId());
			payApplyVirture.setValidStatus(ErpConst.ZERO);
			List<PayApply> buyrderExpensePayApplyList = getPayApplyList(payApplyVirture);
			if (buyrderExpensePayApplyList.size() > 0) {
				return new ResultInfo(-1, "当前有待审核的采购费用单付款申请，请不要重复申请");
			}
		}
		if (payApply.getTraderSupplierId() != null && payApply.getTraderSupplierId() > 0
				&& payApply.getIsUseBalance() == 1) {
			//使用余额的情况下校验是否够
			TraderSupplier traderSupplier = traderSupplierMapper.selectByPrimaryKey(payApply.getTraderSupplierId());
			if (traderSupplier == null) {
				return new ResultInfo(-1, "供应商信息为空，申请失败");
			}
			// 查询当前供应商所有待审核付款申请的总额
			BigDecimal allApplyAmount = payApplyMapper
					.getApplyPayTotalAmountByTraderSupplierId(traderSupplier.getTraderId(), payApply.getCompanyId());
			if (traderSupplier.getAmount().compareTo(allApplyAmount.add(payApply.getAmount())) < 0) {
				return new ResultInfo(-1, "可申请余额不足");
			}
		}
		return resultInfo;
	}

	@Override
	public BigDecimal queryTraderSupplierOccupyAmount(Integer traderId) {
		BigDecimal allApplyAmount =
				payApplyMapper.getApplyPayTotalAmountByTraderSupplierId(traderId, ErpConst.ONE);
		return allApplyAmount;
	}

    @Override
    public void updatePayApplyIsBillInfo(Integer payApplyId, String validComments, Integer payVedengBankId) {
		PayVedengBankDto payVedengBankDto = payApplyMapper.getPayVedengBankByPayBankId(payVedengBankId);
		payApplyMapper.updatePayApplyIsBillInfo(payApplyId, validComments,  payVedengBankId, payVedengBankDto.getPayBankName());
    }

	@Override
	public void updatePayApplyComment(Integer payApplyId, String validComments) {
		payApplyMapper.updatePayApplyComment(payApplyId, validComments);
	}


	@Override
	public List<PayVedengBankDto> getPayVedengBankList() {
		return payApplyMapper.getPayVedengBankList();
	}

	@Override
	public List<PayApply> getPayApplyListByOrderIdAndPayType(Integer payType, Integer relatedId, Integer validStatus) {
		return payApplyMapper.getPayApplyListByOrderIdAndPayType(payType, relatedId, validStatus);
	}

	private void setMSApplyInfo(List<PayApply> payApplyList){
		List<Integer> payApplyIds = payApplyList.stream().map(PayApply::getPayApplyId).collect(Collectors.toList());
		if (CollUtil.isEmpty(payApplyIds)){
			return;
		}
		List<MinshengBankTransferRecord> transferRecords = minshengBankTransferRecordMapper.batchFindByPayApplyIds(payApplyIds);
		if (CollUtil.isEmpty(transferRecords)){
			transferRecords = new ArrayList<>();
		}
		Map<Integer, List<MinshengBankTransferRecord>> recordMap = transferRecords.stream().collect(Collectors.groupingBy(MinshengBankTransferRecord::getPayApplyId));
		if (MapUtil.isEmpty(recordMap)){
			recordMap = MapUtil.newHashMap();
		}
		for (PayApply apply : payApplyList) {
			apply.setOffline(Objects.equals(apply.getOffline(),"-1") ? "" : (Objects.equals(apply.getOffline(),"1") ? "是" : "否"));
			List<MinshengBankTransferRecord> recordList = recordMap.get(apply.getPayApplyId());
			if (CollUtil.isEmpty(recordList)){
				continue;
			}
			MinshengBankTransferRecord minshengBankTransferRecord = recordList.get(0);
			String creditType = Optional.of(minshengBankTransferRecord).map(MinshengBankTransferRecord::getCreditType).orElse("");
			if (Objects.equals(creditType,"01")){
				creditType = "综合授信";
			}else if (Objects.equals(creditType,"02")){
				creditType = "单笔授信";
			}
			apply.setCreditType(creditType);

			String submitCode = "成功";
			String submitMsg = "";
			if (!Objects.equals(minshengBankTransferRecord.getResponseCode(),"0")) {
				submitMsg = minshengBankTransferRecord.getResponseMessage();
				submitCode = "失败";
			}
			if (Objects.equals(minshengBankTransferRecord.getResponseCode(),"0") && !Objects.equals(minshengBankTransferRecord.getTransferCode(),"1")){
				submitMsg = minshengBankTransferRecord.getTransferMessage();
				submitCode = "失败";
			}
			apply.setSubmitStatus(submitCode);
			apply.setSubmitMsg(submitMsg);
		}
	}

}
