package com.vedeng.customerbillperiod.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * T_CUSTOMER_BILL_PERIOD_APPLY
 * <AUTHOR>
@Data
public class CustomerBillPeriodApply implements Serializable {
    private Long billPeriodApplyId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户所属公司ID
     */
    private Integer companyId;

    /**
     * 账期类型，1正式账期，2临时账期，3订单账期
     */
    private Integer billPeriodType;

    /**
     * 申请操作类型，1新增，2调整
     */
    private Integer operateType;

    /**
     * 订单账期关联的订单ID
     */
    private Integer relatedOrderId;

    /**
     * 账期申请额度，调整的金额，正负之分
     */
    private BigDecimal applyAmount;

    /**
     * 账期有效期开始时间
     */
    private Long billPeriodStart;

    /**
     * 账期有效期截止时间
     */
    private Long billPeriodEnd;

    /**
     * 账期结算周期
     */
    private Integer settlementPeriod;

    /**
     * 预期毛利率
     */
    private BigDecimal expectedMargin;

    /**
     * 账期申请原因
     */
    private String applyReason;

    /**
     * 关联的账期ID，如果是新增账期，则为0
     */
    private Long billPeriodId;

    /**
     * 之前的账期申请额度
     */
    private BigDecimal beforeApplyAmount;

    /**
     * 之前的账期有效期开始时间
     */
    private Long beforeBillPeriodStart;

    /**
     * 之前的账期有效期截止时间
     */
    private Long beforeBillPeriodEnd;

    /**
     * 之前的账期结算周期
     */
    private Integer beforeSettlementPeriod;

    /**
     * 账期申请审核状态，0审核中，1审核通过，2审核不通过
     */
    private Integer checkStatus;

    /**
     * 账期申请者
     */
    private Integer creator;

    /**
     * 账期申请时间
     */
    private Long addTime;

    /**
     * 账期申请修改者
     */
    private Integer updater;

    /**
     * 账期申请修改时间
     */
    private Long modTime;

    private static final long serialVersionUID = 1L;
}