<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="报废出库列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="content">
    <div class="searchfunc">
        <form action="${pageContext.request.contextPath}/wms/scrapOut/index.do" method="post" id="search">
            <ul>
                <li>
                    <label class="infor_name">产品名称</label>
                    <input type="text" class="input-middle" name="skuName" id="skuName" value="${scrappedOutQueryDto.skuName }"/>
                </li>
                <li>
                <label class="infor_name">订货号</label>
                <input type="text" class="input-middle" name="skuNo" id="skuNo" value="${scrappedOutQueryDto.skuNo }"/>
            </li>
                <li>
                    <label class="infor_name">品牌</label>
                    <input type="text" class="input-middle" name="brandName" id="brandName" value="${scrappedOutQueryDto.brandName }"/>
                </li>
                <li>
                    <label class="infor_name">型号</label>
                    <input type="text" class="input-middle" name="model" id="model" value="${scrappedOutQueryDto.model }"/>
                </li>
                <li>
                    <label class="infor_name">申请部门</label>
                    <input type="text" class="input-middle" name="applyerDepartment" id="applyerDepartment" value="${scrappedOutQueryDto.applyerDepartment }"/>
                </li>
                <li>
                    <label class="infor_name">申请人</label>
                    <input type="text" class="input-middle" name="applyer" id="applyer" value="${scrappedOutQueryDto.applyer }"/>
                </li>
                <li>
                    <label class="infor_name">出库单号</label>
                    <input type="text" class="input-middle" name="orderNo" id="orderNo" value="${scrappedOutQueryDto.orderNo }"/>
                </li>
                <li>
                    <label class="infor_name">出库状态</label>
                    <select class="input-middle f_left" name="outStatus">
                        <option value="">全部</option>
                        <option <c:if test="${scrappedOutQueryDto.outStatus eq 0}">selected</c:if> value="0">未出库</option>
                        <option <c:if test="${scrappedOutQueryDto.outStatus eq 1}">selected</c:if> value="1">部分出库</option>
                        <option <c:if test="${scrappedOutQueryDto.outStatus eq 2}">selected</c:if> value="2">已出库</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">报废品分类</label>
                    <select class="input-middle f_left" name="scrapType">
                        <option value=""  <c:if test="${scrappedOutQueryDto.scrapType eq null}"> selected </c:if>>全部</option>
                        <c:forEach var="il" items="${scrapTypeList}">
                            <option value="${il.sysOptionDefinitionId}" <c:if
                                    test="${il.sysOptionDefinitionId eq scrappedOutQueryDto.scrapType}"> selected </c:if>>${il.title}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">报废品级别</label>
                    <select class="input-middle f_left" name="scrapLevel">
                        <option value=""  <c:if test="${scrappedOutQueryDto.scrapLevel eq null}"> selected </c:if>>全部</option>
                        <c:forEach var="il" items="${scrapLevelList}">
                            <option value="${il.sysOptionDefinitionId}" <c:if
                                    test="${il.sysOptionDefinitionId eq scrappedOutQueryDto.scrapLevel}"> selected </c:if>>${il.title}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">报废处理方式</label>
                    <select class="input-middle f_left" name="scrapDealType">
                        <option value=""  <c:if test="${scrappedOutQueryDto.scrapDealType eq null}"> selected </c:if>>全部</option>
                        <c:forEach var="il" items="${scrapDealTypeList}">
                            <option value="${il.sysOptionDefinitionId}" <c:if
                                    test="${il.sysOptionDefinitionId eq scrappedOutQueryDto.scrapDealType}"> selected </c:if>>${il.title}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">申请出库日期</label>
                    <input class="Wdate f_left input-smaller96 m0" type="text" placeholder="请选择日期"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endtime\')}'})" autocomplete="off"
                           name="addStartTime" id="starttime" value="${scrappedOutQueryDto.addStartTime }">

                    <div class="f_left ml1 mr1 mt4">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'})"
                           name="addEndTime" id="endtime" value="${scrappedOutQueryDto.addEndTime }">
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span>
                <span class="bg-light-blue bt-bg-style bt-small addtitle"
                      tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                    "link":"./wms/scrapOut/toAddScrappedOut.do","title":"新增报废出库单"}'>新增报废出库单</span>
            </div>
        </form>
    </div>
    <div  class="normal-list-page">
        <div class="fixdiv">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid5">序号</th>
                <th class="wid15">出库单号</th>
                <th class="wid15">产品名称</th>
                <th class="wid15">订货号</th>
                <th class="wid15">是否多个订货号</th>
                <th class="wid15">品牌</th>
                <th class="wid15">申请部门</th>
                <th class="wid15">申请人</th>
                <th class="wid10">申请出库日期</th>
                <th class="wid15">报废处理方式</th>
                <th class="wid15">报废品级别</th>
                <th class="wid15">报废品分类</th>
                <th class="wid15">型号</th>
                <th class="wid15">单位</th>
                <th class="wid10">出库状态</th>
                <th class="wid15">实际出库时间</th>
                <th class="wid15">操作</th>
            </tr>
            </thead>
            <tbody class="company">
            <c:if test="${not empty scrappedOutList}">
                <c:forEach items="${scrappedOutList}" var="scrappedOut" varStatus="num">
                    <tr>
                        <td>${num.count}</td>
                        <td> ${scrappedOut.orderNo} </td>
                        <td>${scrappedOut.skuName}</td>
                        <td> ${scrappedOut.skuNo} </td>
                        <td>
                            <c:if test="${scrappedOut.countGoods > 1} "> 是 </c:if>
                            <c:if test="${scrappedOut.countGoods  eq 1} "> 否 </c:if>
                        </td>
                        <td> ${scrappedOut.brandName}  </td>
                        <td> ${scrappedOut.applyerDepartment} </td>
                        <td> ${scrappedOut.applyer} </td>
                        <td> ${scrappedOut.appleOutDate} </td>
                            <%--报废处理方式--%>
                        <td>${scrappedOut.scrapDealTypeStr}  </td>
                            <%--报废品级别--%>
                        <td>${scrappedOut.scrapLevelStr} </td>
                        <!--报废品分类-->
                        <td>${scrappedOut.scrapTypeStr} </td>
                        <td> ${scrappedOut.model}  </td>
                        <td> ${scrappedOut.unitName} </td>
                        <!--出库状态-->
                        <td>
                            <c:choose>
                                <c:when test="${scrappedOut.outStatus eq 0}">
                                    未出库
                                </c:when>
                                <c:when test="${scrappedOut.outStatus eq 1}">
                                    部分出库
                                </c:when>
                                <c:when test="${scrappedOut.outStatus eq 2}">
                                    已出库
                                </c:when>
                            </c:choose>
                        </td>
                        <td> ${scrappedOut.realOutputTime}  </td>
                        <td>
                            <span class="edit-user addtitle" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./wms/scrapOut/scrapDetail.do?scrappedOutId=${scrappedOut.id}","title":"详情"}'>详情</span>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        </div>
        <c:if test="${empty scrappedOutList}">
            <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>
<script type="text/javascript" src='<%= basePath %>static/js/logistics/warehouseIn/addBarcode.js?rnd=${resourceVersionKey}'></script>
<%@ include file="../../common/footer.jsp"%>