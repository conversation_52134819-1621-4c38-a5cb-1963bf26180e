package com.vedeng.activiti.taskassign;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderGoodsAptitudeConstants;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import net.sf.json.JSONObject;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.HashMap;
import java.util.Map;

/**
 * HC订单自动审核监听器
 */
public class HCOrderAutoAuditListener implements TaskListener {

    public static Logger logger = LoggerFactory.getLogger(HCOrderAutoAuditListener.class);

    //应用程序上下文
    public static WebApplicationContext applicationContext = ContextLoader.getCurrentWebApplicationContext();

    private TraderCustomerService traderCustomerService;

    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    private SaleorderMapper saleorderMapper;

    private UserMapper userMapper;

    private ActionProcdefService actionProcdefService;

    private SaleorderService saleorderService;

    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;

    private WarehouseStockService warehouseStockService;


    public HCOrderAutoAuditListener(){

        processEngine = ProcessEngines.getDefaultProcessEngine();

        traderCustomerService = (TraderCustomerService) applicationContext.getBean(TraderCustomerService.class);

        saleorderMapper = (SaleorderMapper) applicationContext.getBean(SaleorderMapper.class);

        saleorderService = (SaleorderService) applicationContext.getBean(SaleorderService.class);

        userMapper = (UserMapper) applicationContext.getBean(UserMapper.class);

        actionProcdefService = (ActionProcdefService) applicationContext.getBean("actionProcdefService");

        logicalSaleorderChooseServiceImpl = (LogicalSaleorderChooseServiceImpl) applicationContext.getBean("logicalSaleorderChooseService");

        warehouseStockService = (WarehouseStockService) applicationContext.getBean("warehouseStockService");

    }

    @Override
    public void notify(DelegateTask execution) {
        try {

            String businessKey = execution.getVariable("businessKey").toString();

            //hc_order_auto_verify_123
            Integer saleorderId =  Integer.valueOf(businessKey.split("_")[4]);

            String comment = "订单自动审核";

            User adminUser = userMapper.getUserByName("njadmin");

            TaskService taskService = processEngine.getTaskService();

            // 获取当前活动节点
            Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
            if (taskInfo == null) {
                return;
            }

            String taskId = taskInfo.getId();

            Map<String, Object> variables = new HashMap<String, Object>();
            variables.put("pass", "true");
            variables.put("updater", "njadmin");
            variables.put(OrderGoodsAptitudeConstants.KEY_AUTO_CHECK_APTITUDE,false);

            Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderId);

            if (saleorder != null && saleorder.getTraderId() != null) {
                TraderCustomer traderCustomer = traderCustomerService.getSimpleCustomer(saleorder.getTraderId());
                if (traderCustomer != null && traderCustomer.getTraderCustomerId() != null) {
                    VerifiesInfo verifiesInfo = traderCustomerService.getCustomerAptitudeVerifiesInfo(traderCustomer.getTraderCustomerId());
                    if (verifiesInfo != null && OrderGoodsAptitudeConstants.APTITTUDE_IS_PASSED.equals(verifiesInfo.getStatus())) {
                        variables.remove("pass");
                        variables.put(OrderGoodsAptitudeConstants.KEY_AUTO_CHECK_APTITUDE, true);
                    }
                }
            }

            actionProcdefService.complementTask(null, taskId, comment,"njadmin", variables);

            Task nextTask = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
            if (nextTask == null) {
                return;
            }

            if (OrderGoodsAptitudeConstants.KEY_AUTOCHECK_APTITUDE.equals(nextTask.getName())) {

                ResultInfo resultInfo = saleorderService.checkGoodAptitude(saleorderId);
                if(resultInfo.getCode() == 0 || resultInfo.getCode() == 1){
                    variables.put("pass","true");
                }else {
                    variables.put("pass","false");
                }
                actionProcdefService.complementTask(null, nextTask.getId(), comment,"njadmin", variables);

                // add by Tomcat.Hui 2020/5/13 5:00 下午 .Desc: VD-6176 erp部分付款，前台显示待付款	. start
                if (resultInfo.getCode() == 0) {
                    changeSaleorderDeliverState(saleorder,adminUser);
                }
                // add by Tomcat.Hui 2020/5/13 5:00 下午 .Desc: VD-6176 erp部分付款，前台显示待付款. end
            }

        }catch (Exception e){
            logger.error("HC订单自动审核失败:",e);
        }
    }

    /**
     * <b>Description:</b>改变订单的直发状态，并给bd订单发消息<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/3/18
     */
    private void changeSaleorderDeliverState(Saleorder saleorder, User user){
        try {

            saleorderService.updateOrderdeliveryDirect(saleorder);
            if (saleorder.getOrderType() <= 1) {
                JSONObject result2 = saleorderService.updateVedengJX(saleorder.getSaleorderId());
            }
            logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorder,user);
            //调用库存服务
            warehouseStockService.updateOccupyStockService(saleorder, 0);

        }catch (Exception e){
            logger.error("changeSaleorderDeliverState error:",e);
        }
    }
}
