package com.vedeng.order.dao;

import com.vedeng.authorization.model.User;
import com.vedeng.order.model.QuoteorderConsultReply;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QuoteorderConsultReplyMapper {
    int deleteByPrimaryKey(Integer quoteorderConsultReplyId);

    int insert(QuoteorderConsultReply record);

    int insertSelective(QuoteorderConsultReply record);

    QuoteorderConsultReply selectByPrimaryKey(Integer quoteorderConsultReplyId);

    List<QuoteorderConsultReply> getByQuoteorderIdAndSku(@Param("quoteorderId") Integer quoteorderId, @Param("sku") String sku);

    List<QuoteorderConsultReply> getByQuoteorderIdAndQuoteorderGoods(@Param("quoteorderId") Integer quoteorderId, @Param("quoteorderGoodsId") Integer quoteorderGoodsId);

    List<QuoteorderConsultReply> getByQuoteorderIdAndConsultType(@Param("quoteorderId") Integer quoteorderId, @Param("consultType") Integer consultType);

    int updateByPrimaryKeySelective(QuoteorderConsultReply record);

    int updateByPrimaryKey(QuoteorderConsultReply record);

    Integer getUnHandledCountOfConsultSupply(Integer quoteorderId);

    Integer getUnhandledCountOfConsultManager(Integer quoteorderId);

    /**
     * 供应链回复过的咨询条数，包括部分回复
     * @param quoteorderId
     * @return
     */
    Integer getCountOfConsultSupplyReply(Integer quoteorderId);


    void updateConsultExecutiveStatus(@Param("quoteorderConsultReplyId") Integer quoteorderConsultReplyId, @Param("content") String content, @Param("updater") Integer updater, @Param("timestamp") Long timestamp);


    void updateAllConsultStatusByQuoteorderIdAndSkuList(@Param("quoteorderId") Integer quoteorderId, @Param("skuList") List<String> skuList, @Param("status") Integer status);


    void updateAllConsultStatusByQuoteorderId(@Param("quoteorderId") Integer quoteorderId, @Param("status") Integer status);

    List<User> getConsultReplierList();

}