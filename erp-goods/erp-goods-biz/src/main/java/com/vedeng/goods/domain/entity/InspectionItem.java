package com.vedeng.goods.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
 * 检查项目
 *
 * <AUTHOR>
 * @TableName T_INSPECTION_ITEM
 */
@Data
public class InspectionItem extends BaseEntity {


    /**
     * 主键
     */
    private Integer id;

    /**
     * 检查项目名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否删除，0：未删除，1:已删除
     */
    private Integer isDeleted;

}