package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity;

/**
 * <AUTHOR>
 */
public interface KingDeeSalesVatSpecialInvoiceMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeSalesVatSpecialInvoiceEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeSalesVatSpecialInvoiceEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    KingDeeSalesVatSpecialInvoiceEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeSalesVatSpecialInvoiceEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeSalesVatSpecialInvoiceEntity record);
}