package com.test.order;

import com.test.stock.SpeicalWarehouseProcessor;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.service.BaseService;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.dto.GenerateCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.SaleorderData;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.impl.RiskCheckServiceImpl;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.TraderCustomer;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName RiskTest.java
 * @Description TODO 风控测试
 * @createTime 2020年12月12日 15:12:00
 */
public class RiskTest extends SpeicalWarehouseProcessor {

    @Resource
    private RiskCheckServiceImpl riskCheckService;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private BaseService baseService;

    @Resource
    private SaleorderMapper saleorderMapper;


    @Resource
    private TraderCustomerMapper traderCustomerMapper;


    @Resource
    private InvoiceMapper invoiceMapper;

    @Test
    public void test() throws Exception {
//        Saleorder saleorder = new Saleorder();
//        saleorder.setSaleorderId(141590);
//        SaleorderRiskModelVo saleorderRiskModelVo = riskCheckService.riskCheckSaleOrder(saleorder);
//        System.out.println(saleorderRiskModelVo);
//        SkuRiskModelVo skuRiskModelVo = riskCheckService.riskCheckSku("V274449");
//        for (int i = 0; i < 10; i++) {
//            Thread.sleep(500);
//            boolean asd = redisUtils.access("asd1221", 2, 2);
////            System.out.println(asd);
//        }
//        SysOptionDefinition sysOptionDefinition = baseService.getFirstSysOptionDefinitionList(SysOptionConstant.WECHAT_TEMPLATE_BEDENG_REMARK);
//        System.out.println(sysOptionDefinition);
        List<String> saleorderNoList = new ArrayList<>();
        List<Integer> saleorderIdList = saleorderMapper.getAccountPeriodSaleorderIdList(saleorderNoList);

        //查询订单流水金额
        List<SaleorderData> saleorderDataList =  saleorderMapper.getSaleorderLackAccountPeriodAmountDatas(saleorderIdList);

        List<SaleorderData> dataList = saleorderDataList.stream().
                filter(item -> item.getLackAccountPeriodAmount() != null &&  item.getLackAccountPeriodAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

        for (SaleorderData saleorderData : dataList) {
            //未还账期金额
            BigDecimal lackAccountPeriodAmount = saleorderData.getLackAccountPeriodAmount();
            //订单已收款账期金额
            BigDecimal periodAmount = saleorderData.getPeriodAmount();
            Saleorder saleOrder = saleorderMapper.getSaleOrderById(saleorderData.getSaleorderId());

            //将未还账期设置为账期金额
            saleOrder.setAccountPeriodAmount(lackAccountPeriodAmount);

            TraderCustomer traderCustomer = traderCustomerMapper.getBaseCustomerByTraderId(saleOrder.getTraderId());
            //逾期编码生成  风险编码生成
            generateCustomerBillPeriodManagementDetail(saleOrder,traderCustomer);

        }
    }
    private void generateCustomerBillPeriodManagementDetail(Saleorder saleOrder, TraderCustomer traderCustomer) throws CustomerBillPeriodException {
        GenerateCustomerBillPeriodManagementDetailDto detailDto = new GenerateCustomerBillPeriodManagementDetailDto();

        List<Invoice> invoiceList = invoiceMapper.getInvoiceListBySaleorderId(saleOrder.getSaleorderId());

        List<Invoice> dealInvoiceList = new ArrayList<>();

        BigDecimal returnAmount = BigDecimal.ZERO;

        //处理发票信息 优先退票时间早的
        for (Invoice invoice : invoiceList) {
            if(BigDecimal.ZERO.compareTo(invoice.getAmount()) > 0){
                returnAmount = returnAmount.add(invoice.getAmount().abs());
            }
        }
        for (Invoice invoice : invoiceList) {
            if(BigDecimal.ZERO.compareTo(invoice.getAmount()) < 0 && BigDecimal.ZERO.compareTo(returnAmount) < 0){
                if (returnAmount.compareTo(invoice.getAmount()) > 0){
                    returnAmount = returnAmount.subtract(invoice.getAmount());
                    invoice.setAmount(BigDecimal.ZERO);
                }else{
                    invoice.setAmount(invoice.getAmount().subtract(returnAmount));
                    returnAmount = BigDecimal.ZERO;
                }
            }

            if(BigDecimal.ZERO.compareTo(invoice.getAmount()) < 0){
                dealInvoiceList.add(invoice);
            }
        }


        detailDto.setCompanyId(saleOrder.getCompanyId());
        detailDto.setCustomerId(Long.valueOf(traderCustomer.getTraderCustomerId()));
        detailDto.setOrderNo(saleOrder.getSaleorderNo());
        detailDto.setOrderId(Long.valueOf(saleOrder.getSaleorderId()));
        detailDto.setType(CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_INVOICE.getCode());

//        detailDto.setAddTime();
//        detailDto.setRelatedId();
//        detailDto.setAmount();

//        customerBillPeriodManagementDetailService.generateCustomerBillPeriodManagementDetail(detailDto);
    }
}
