package com.vedeng.api.standard.adapter.peerlist.dto;

import lombok.Data;
import com.vedeng.api.standard.core.exception.ApiStandardException;

import java.util.Date;
import java.util.List;

/**
 * 直发同行单创建请求
 * 
 * <AUTHOR>
 */
@Data
public class PeerListCreateRequest {
    
    /**
     * 采购单ID
     */
    private String buyOrderNo;
    
    private List<String> fileUrls;
    
    /**
     * 同行单明细列表
     */
    private List<PeerListDetailRequest> list;
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (buyOrderNo == null) {
            throw ApiStandardException.serviceExecutionError("采购单号不能为空");
        }
        if (list == null || list.isEmpty()) {
            throw ApiStandardException.serviceExecutionError("同行单明细不能为空");
        }
        
        // 验证每个明细项
        for (PeerListDetailRequest detail : list) {
            detail.validate();
        }
    }
} 
