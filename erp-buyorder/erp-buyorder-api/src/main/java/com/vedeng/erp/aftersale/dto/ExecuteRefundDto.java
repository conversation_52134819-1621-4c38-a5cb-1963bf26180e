package com.vedeng.erp.aftersale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecuteRefundDto {

    /**
     * 采购费用售后主键ID
     */
    @NotNull(message = "采购费用售后主键ID不能为空")
    private Long expenseAfterSalesId;

    /**
     * 采购费用单主键ID
     */
    @NotNull(message = "采购费用单主键ID不能为空")
    private Integer buyorderExpenseId;

    /**
     * 售后总额
     */
    @NotNull(message = "采购费用单售后总额不能为空")
    private BigDecimal totalAmount;
}
