package com.vedeng.infrastructure.bank.api.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 民生银行承兑汇票结果表
 */
@Getter
@Setter
public class MinshengBankTransferRecord extends BaseEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 付款申请ID
     */
    private Integer payApplyId;

    /**
     * 授信类型
     */
    private String creditType;

    /**
     * 客户业务流水号
     */
    private String insId;

    /**
     * 银行渠道流水号
     */
    private String svrId;

    /**
     * 0.成功，CALL_START.开始调用，CALL_ERROR.接口请求异常，其他状态码为银行接口返回状态码
     */
    private String responseCode;

    /**
     * 接口返回信息
     */
    private String responseMessage;

    /**
     * 转账结果状态码
     */
    private String transferCode;

    /**
     * 转账结果信息
     */
    private String transferMessage;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;
}