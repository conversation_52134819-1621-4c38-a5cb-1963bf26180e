package com.vedeng.erp.kingdee.batch.common.enums;

import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/18 13:31
 **/
public enum WarehouseTypeAndFormIdEnum {
    LEND_WAREHOUSE_OUT(10, King<PERSON>eeFormConstant.STK_TRANSFER_DIRECT, "外借出库"),
    SCRAP_WAREHOUSE_OUT(13, KingDeeFormConstant.STK_MIS_DELIVERY, "报废出库"),
    RECEIVE_WAREHOUSE_OUT(14, KingDeeFormConstant.STK_MIS_DELIVERY, "领用出库"),
    INVENTORY_LOSS_WAREHOUSE_OUT(16, KingDeeFormConstant.STK_STOCK_COUNT_LOSS, "盘亏出库"),
    SAMPLE_WAREHOUSE_OUT(18, King<PERSON><PERSON><PERSON><PERSON><PERSON>onstant.STK_MIS_DELIVERY, "样品出库"),
    LENDOUT_WAREHOUSE_IN(9,King<PERSON><PERSON><PERSON><PERSON><PERSON>onstant.STK_TRANSFER_DIRECT, "借货归还入库单"),
    DISK_SURPLUS_IN(12,KingDeeFormConstant.STK_STOCK_COUNT_GAIN, "盘盈入库单"),
    UNIT_CONVERSION_IN(20,KingDeeFormConstant.STK_MISCELLANEOUS,"库存转换入库"),
    UNIT_CONVERSION_OUT(19, KingDeeFormConstant.STK_MISCELLANEOUS, "库存转换出库"),
    ;

    private final Integer outInType;

    private final String formId;

    private final String desc;

    public Integer getOutInType() {
        return outInType;
    }

    public String getFormId() {
        return formId;
    }

    public String getDesc() {
        return desc;
    }

    WarehouseTypeAndFormIdEnum(Integer outInType, String formId, String desc) {

        this.outInType = outInType;
        this.formId = formId;
        this.desc = desc;
    }

    public static String getFormIdByType(Integer outInType){
        for (WarehouseTypeAndFormIdEnum warehouseGoodsOutEnum : WarehouseTypeAndFormIdEnum.values()) {
            if (warehouseGoodsOutEnum.outInType.equals(outInType)) {
                return warehouseGoodsOutEnum.formId;
            }
        }
        return "";
    }
}
