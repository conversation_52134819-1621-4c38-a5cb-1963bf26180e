<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterBuyorderInvoiceMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterBuyorderInvoiceEntity" >
    <!--          -->
    <id column="AFTER_BUYORDER_INVOICE_ID" property="afterBuyorderInvoiceId" jdbcType="INTEGER" />
    <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
    <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
    <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
    <result column="RATIO" property="ratio" jdbcType="DECIMAL" />
    <result column="IS_DELETE" property="isDelete" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    AFTER_BUYORDER_INVOICE_ID, AFTER_SALES_ID, INVOICE_NO, INVOICE_CODE, IS_DELETE, ADD_TIME, 
    CREATOR, MOD_TIME, UPDATER,INVOICE_TYPE,RATIO
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_BUYORDER_INVOICE
    where AFTER_BUYORDER_INVOICE_ID = #{afterBuyorderInvoiceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_AFTER_BUYORDER_INVOICE
    where AFTER_BUYORDER_INVOICE_ID = #{afterBuyorderInvoiceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterBuyorderInvoiceEntity" >
    <!--          -->
    insert into T_AFTER_BUYORDER_INVOICE (AFTER_BUYORDER_INVOICE_ID, AFTER_SALES_ID, 
      INVOICE_NO, INVOICE_CODE, IS_DELETE, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER,INVOICE_TYPE,RATIO)
    values (#{afterBuyorderInvoiceId,jdbcType=INTEGER}, #{afterSalesId,jdbcType=INTEGER}, 
      #{invoiceNo,jdbcType=VARCHAR}, #{invoiceCode,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER},#{invoiceType,jdbcType=INTEGER},#{ratio,jdbcType=DECIMAL}
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterBuyorderInvoiceEntity" >
    <!--          -->
    insert into T_AFTER_BUYORDER_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="afterBuyorderInvoiceId != null" >
        AFTER_BUYORDER_INVOICE_ID,
      </if>
      <if test="afterSalesId != null" >
        AFTER_SALES_ID,
      </if>
      <if test="invoiceNo != null" >
        INVOICE_NO,
      </if>
      <if test="invoiceCode != null" >
        INVOICE_CODE,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="ratio != null" >
        RATIO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="afterBuyorderInvoiceId != null" >
        #{afterBuyorderInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null" >
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null" >
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null" >
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="ratio != null" >
        #{ratio,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterBuyorderInvoiceEntity" >
    <!--          -->
    update T_AFTER_BUYORDER_INVOICE
    <set >
      <if test="afterSalesId != null" >
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null" >
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null" >
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="ratio != null" >
        RATIO = #{ratio,jdbcType=DECIMAL},
      </if>
    </set>
    where AFTER_BUYORDER_INVOICE_ID = #{afterBuyorderInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterBuyorderInvoiceEntity" >
    <!--          -->
    update T_AFTER_BUYORDER_INVOICE
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where AFTER_BUYORDER_INVOICE_ID = #{afterBuyorderInvoiceId,jdbcType=INTEGER}
  </update>

  <select id="queryInfoByAfterSalesId" resultType="com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceDto">
    SELECT
    <include refid="Base_Column_List" />
        FROM T_AFTER_BUYORDER_INVOICE
        WHERE AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>
</mapper>