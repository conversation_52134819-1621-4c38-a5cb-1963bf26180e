
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="编辑售后商机" scope="application" />	
<%@ include file="../../common/common.jsp"%>

<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/bussinesschance/add_afterAalesBussinessChance.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/bussinesschance/edit_afterAalesBussinessChanceNew.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
	<div class="formpublic formpublic1 pt0">
        <form method="post" id="myform" action="${pageContext.request.contextPath}/order/bussinesschance/saveEditServiceBussinessChance.do">
            <div class="formtitle">请填写商机信息</div>
            <input type="hidden" name="bussinessChanceId" value="${bussinessChanceVo.bussinessChanceId}">
            <input type="hidden" id="ptype" value="${bussinessChanceVo.type}">
            <input type="hidden" id="psource" value="${bussinessChanceVo.source}">
            <input type="hidden" id="pinquiry" value="${bussinessChanceVo.communication}">
            <input type="hidden" id="sourceZj" value='${sourceZj}'>
            <input type="hidden" id="sourceBd" value='${sourceBd}'>
            <div class="line">
                <ul>
                    <li>
                        <div class="infor_name">
                            <span>*</span>
                            <label>商机时间</label>
                        </div>
                        <div class="f_left">
                            <input class="Wdate m0 input-middle Wdate2" name ="time" id="receiveTime" type="text" placeholder="请选择日期" autocomplete="off"
                            	onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" 
                            	value='<date:date value ="${bussinessChanceVo.receiveTime} " format="yyyy-MM-dd HH:mm:ss"/>'>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>商机类型</label>
                        </div>
                        <div class="f_left  ">
                            <ul style="width:688px">
                                <li style="float: left;margin: 0 10px 4px 0;">
                                    <input type="radio" name="type" onclick="changeData('type')" <c:if test="${391 eq bussinessChanceVo.type }">checked="checked"</c:if>
                                           value="391"><label>总机询价</label>
                                </li>
                            </ul>
                            <div id="type" class="font-red " style="display: none">商机来源不允许为空</div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>询价行为</label>
                        </div>
                        <div class="f_left  ">
                            <ul id="ul_inquire" style="width:688px">
                                <c:if test="${not empty inquiryData }">
                                    <c:forEach items="${inquiryData}" var ="sl">
                                        <li style="float: left;margin: 0 10px 4px 0;">
                                            <input type="radio" name="inquiry" onclick="changeData('inquiry')" <c:if test="${sl.sysOptionDefinitionId eq bussinessChanceVo.inquiry }">checked="checked"</c:if>
                                                   value="${sl.sysOptionDefinitionId}"><label>${sl.title}</label>
                                        </li>
                                    </c:forEach>
                                </c:if>

                            </ul>
                            <div id="inquire" class="font-red " style="display: none">询价行为不允许为空</div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>渠道类型</label>
                        </div>
                        <div class="f_left  ">
                            <ul id="ul_source" style="width:688px">
                            	<c:if test="${not empty bussSource }">
                            		<c:forEach items="${bussSource}" var ="sl">
                            		<c:if test="${sl.sysOptionDefinitionId!=366 && sl.sysOptionDefinitionId != 477}">
                            			<li style="float: left;margin: 0 10px 4px 0;">
                            				<input type="radio" name="source" onclick="changeData('source')" <c:if test="${sl.sysOptionDefinitionId eq bussinessChanceVo.source }">checked="checked"</c:if>
	                                    			value="${sl.sysOptionDefinitionId}"><label>${sl.title}</label>
	                                	</li>
	                                	</c:if>
                            		</c:forEach>
                            	</c:if>
                            </ul>
                            <div id="source" class="font-red " style="display: none">渠道类型不允许为空</div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>渠道名称</label>
                        </div>
                        <div class="f_left  ">
                            <ul id="ul_inquiry" style="width:688px">
                           		 <c:if test="${not empty communications }">
                            		<c:forEach items="${communications}" var ="il">
                            			<li style="float: left;margin: 0 10px 4px 0;">
	                                    	<input type="radio" name="communication" value="${il.sysOptionDefinitionId}"
	                                    		<c:if test="${il.sysOptionDefinitionId eq bussinessChanceVo.communication}">checked="checked"</c:if>><label>${il.title}</label>
	                                	</li>
                            		</c:forEach>
                            	</c:if>
                            </ul>
                            <div id="communication" class="font-red " style="display: none">渠道名称不允许为空</div>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>产品分类</label>
                        </div>
                        <div class="f_left  ">
                            <ul style="width:688px">
                                <c:if test="${not empty goodsTypeList }">
                                    <c:forEach items="${goodsTypeList}" var ="gyl">
                                        <li style="float: left;margin: 0 10px 4px 0;">
                                            <input type="radio" name="goodsCategory" value="${gyl.sysOptionDefinitionId}"
                                                   <c:if test="${gyl.sysOptionDefinitionId eq bussinessChanceVo.goodsCategory}">checked="checked"</c:if>><label>${gyl.title}</label>
                                        </li>
                                    </c:forEach>
                                </c:if>
                            </ul>
                            <div id="goodsCategory" class="font-red " style="display: none">产品分类不允许为空</div>
                        </div>
                    </li>

                    <li id="productDiv_0">
                        <div class="infor_name" id="productTitleDiv_0" idFlag="0">
                            <span>*</span>
                            <label>询价产品</label>
                        </div>
                        <div class="f_left">
                            <input class="input-larger"  id="content_0"/>
                            <a onclick="deleteCategoryName(0)">删除</a>
                        </div>
                    </li>
                    <input type="hidden" id="productCountFlag" value="0" firstIndex = "0">
                    <input  id="content" name="content" type="hidden">

                    <li>
                        <div class="infor_name">
                        </div>
                        <div class="f_left  ">
                            <a class="pop-new-data" layerparams='{"width":"1400px","height":"788px","title":"新增录票","link":"/order/bussinesschance/addBussinessChanceProduct.do"}'>
                                + 添加
                            </a>
                        </div>
                        <input id="contentVa" value="${bussinessChanceVo.content}" type="hidden">
                    </li>

                    <li>
                        <div class="infor_name">
                            <label>产品备注（总机）</label>
                        </div>
                        <div class="f_left">
                            <textarea class="askprice" id="productComments" name="productComments"
                                      placeholder="请输入商机产品备注" >${bussinessChanceVo.productComments}
                            </textarea>
                        </div>
                    </li>

                    <li>
                        <div class="infor_name">
                            <label>附件</label>
                        </div>
                        <div class="f_left">
	                        	<input type="file" class="upload_file" id='file_1' name="lwfile" style="display: none;" onchange="uploadFile(this,1);">
				                <input type="text" class="input-larger" id="name_1" name="name" readonly="readonly" value="${bussinessChanceVo.attachmentName}"
				                       placeholder="使用pdf、jpg、word或excel等文件，不允许超过2MB"  onclick="file_1.click();">
				                <input type="hidden" name="uri" id="uri_1" value="${bussinessChanceVo.attachmentUri}"/>
				                <input type="hidden" name="" id="domain" value="${bussinessChanceVo.attachmentDomain}"/>
	                            <label class="bt-bg-style bt-small bg-light-blue" onclick="file_1.click();">浏览</label>
	                            <c:choose>
									<c:when test="${!empty bussinessChanceVo.attachmentUri}">
										<i class="iconsuccesss mt5" id="img_icon_1"></i>
				                    	<a href="http://${bussinessChanceVo.attachmentDomain}${bussinessChanceVo.attachmentUri}" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_1">查看</a>
				                    	<span class="font-red cursor-pointer mt4" onclick="del(1)" id="img_del_1">删除</span>
									</c:when>
									<c:otherwise>
										<i class="iconsuccesss mt5 none" id="img_icon_1"></i>
			                    		<a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_1">查看</a>
				                    	<span class="font-red cursor-pointer mt4 none" onclick="del(1)" id="img_del_1">删除</span>
									</c:otherwise>
								</c:choose>
	                        <div style="clear:both"></div>    
                            <div id="upload1" class="font-red " style="display: none">请选择正确文件格式</div>
                            <div id="upload2" class="font-red " style="display: none">上传内容不允许超过2MB</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="formtitle ">请填写客户信息</div>
            <div class='line'>
                <ul>
                    <li>
                        <div class="infor_name">
                            <label>客户名称</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-large" value="${bussinessChanceVo.traderName}" name="traderName" id="traderName">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>客户地区</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select name="province" id="province">
                                <option value="0">请选择</option>
		                    	<c:if test="${not empty provinceList }">
		                    		<c:forEach items="${provinceList }" var="prov">
		                    			<option value="${prov.regionId }" <c:if test="${province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
		                    		</c:forEach>
		                    	</c:if>
                            </select>
                            <select name="city" id ="city">
                                <option value="0">请选择</option>
                                <c:if test="${not empty cityList }">
		                    		<c:forEach items="${cityList }" var="ci">
		                    			<option value="${ci.regionId }" <c:if test="${city eq ci.regionId }">selected="selected"</c:if>>${ci.regionName }</option>
		                    		</c:forEach>
		                    	</c:if>
                            </select>
                            <select name="zone" id="zone">
                                <option value="0">请选择</option>
                                <c:if test="${not empty zoneList }">
		                    		<c:forEach items="${zoneList }" var="zo">
		                    			<option value="${zo.regionId }" <c:if test="${zone eq zo.regionId }">selected="selected"</c:if>>${zo.regionName }</option>
		                    		</c:forEach>
		                    	</c:if>
                            </select>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>联系人</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" value="${bussinessChanceVo.traderContactName}"  name="traderContactName" id="traderContactName">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>手机号</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" value="${bussinessChanceVo.mobile}"  name="mobile" id="mobile">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>电话</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" value="${bussinessChanceVo.telephone}"  name="telephone" id="telephone">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name">
                            <label>其他联系方式</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-middle" value="${bussinessChanceVo.otherContact}"  name="otherContact" id="otherContact">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="formtitle">请分配商机</div>
            <div>
                <ul>
                    <li>
                        <div class="infor_name">
                        <span>*</span>
                            <label>分配销售</label>
                        </div>
                        <div class="f_left">
                            <select name="userId" id="userId">
                                <option value="">请选择</option>
                                <c:if test="${not empty userList }">
                                	<c:forEach items="${userList}" var="user">
                                		<option value="${user.userId}" <c:if test="${bussinessChanceVo.userId eq user.userId }">selected="selected"</c:if>>${user.username}</option>
                                	</c:forEach>
                                </c:if>
                            </select>
                        </div>
                    </li>
                     <li>
                        <div class="infor_name">
                            <label>备注</label>
                        </div>
                        <div class="f_left">
                            <input type="text" class="input-large" value="${bussinessChanceVo.comments}"  name="comments" id="comments">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="add-tijiao">
            	<input type="hidden" name="beforeParams" value='${beforeParams}'>
            	<input type="hidden" name="isRest" value="1">
                <button id="submit" type="submit">提交</button>
            </div>
        </form>
    </div>
</body>

</html>
