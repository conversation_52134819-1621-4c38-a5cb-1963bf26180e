<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchCustomerFinanceDtoMapper">
    <update id="updateKingDeePushStatus">
        UPDATE T_TRADER_CUSTOMER_FINANCE
        SET IS_PUSH = 1, PUSH_TIME = NOW(), MOD_TIME = MOD_TIME
        WHERE TRADER_CUSTOMER_FINANCE_ID = #{traderCustomerFinanceId,jdbcType=INTEGER}
    </update>

    <select id="findByAll" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto">
        SELECT
        ttcf.TRADER_CUSTOMER_FINANCE_ID,
        ttc.TRADER_CUSTOMER_ID ,
        tt.TRADER_NAME,
        CASE
        ttcf.CUSTOMER_NATURE
        WHEN 1 THEN '直接客户'
        WHEN 2 THEN '间接客户'
        ELSE ''
        END customerNatureName,
        CASE
        ttcf.CUSTOMER_CLASS
        WHEN 1 THEN '公立'
        WHEN 2 THEN '民营个体'
        WHEN 3 THEN '民营集团'
        ELSE ''
        END customerClassName,
        ttcf.GROUP_NAME,

        CASE
        ttcf.CUSTOMER_THIRD_TYPE
        WHEN 1 THEN '医院'
        WHEN 2 THEN '基层医疗卫生机构'
        WHEN 3 THEN '专业医疗卫生机构'
        WHEN 4 THEN '其他医疗卫生机构'
        ELSE ''
        END customerThirdTypeName,
        CASE
        ttcf.CUSTOMER_SECOND_TYPE
        WHEN 1 THEN '医疗卫生机构'
        WHEN 2 THEN '非医疗卫生机构'
        WHEN 3 THEN '分销商'
        ELSE ''
        END customerSecondTypeName,
        CASE
        ttcf.HOSPITAL_LEVER
        WHEN 1 THEN '一级'
        WHEN 2 THEN '二级'
        WHEN 3 THEN '三级'
        WHEN 4 THEN '未分级'
        ELSE ''
        END hospitalLevelName,
        ttcf.HOSPITAL_NAME
        FROM
        T_TRADER_CUSTOMER_FINANCE ttcf
        LEFT JOIN T_TRADER_CUSTOMER ttc ON
        ttcf.TRADER_ID = ttc.TRADER_ID
        LEFT JOIN T_TRADER tt ON
        ttc.TRADER_ID = tt.TRADER_ID
        WHERE
        ttcf.IS_DELETE = 0
        and ttcf.IS_PUSH = 0
        and  tt.IS_ENABLE = 1
        and ttc.IS_ENABLE = 1
        <if test="beginTime != null">
            AND ttcf.MOD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=DATE}
        </if>
        <if test="endTime != null">
            AND ttcf.MOD_TIME <![CDATA[<=]]> #{endTime,jdbcType=DATE}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
    <select id="getCustomerIdByTraderId" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto">
        select TRADER_CUSTOMER_ID traderCustomerId
        from T_TRADER_CUSTOMER
        WHERE TRADER_ID = #{traderId}
    </select>

    <select id="queryCustomerCompensate" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto">
        select TTC.TRADER_CUSTOMER_ID
        from T_TRADER_CUSTOMER TTC
                 left join T_VERIFIES_INFO TVI1
                           on TTC.TRADER_CUSTOMER_ID = TVI1.RELATE_TABLE_KEY and TVI1.RELATE_TABLE = 'T_TRADER_CUSTOMER' and
                              TVI1.VERIFIES_TYPE = 617
                 left join T_VERIFIES_INFO TVI2
                           on TTC.TRADER_CUSTOMER_ID = TVI2.RELATE_TABLE_KEY and TVI2.RELATE_TABLE = 'T_CUSTOMER_APTITUDE'
                 left join KING_DEE_CUSTOMER KDC on TTC.TRADER_CUSTOMER_ID = KDC.F_NUMBER
        where TTC.IS_ENABLE = 1
          and KDC.ID is null
          and (TVI1.STATUS = 1 or TVI2.STATUS = 1)
        <if test="beginTimestamp != null">
            AND TTC.ADD_TIME <![CDATA[>=]]> #{beginTimestamp,jdbcType=BIGINT}
        </if>
        <if test="endTimestamp != null">
            AND TTC.ADD_TIME <![CDATA[<=]]> #{endTimestamp,jdbcType=BIGINT}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
</mapper>