$(function () {
    $('input:radio[name="deliveryDirect"]').click(function () {
        checkLogin();
        $(".warning").remove();
        var isUpdateDeliveryDirect = $('#isUpdateDeliveryDirect').val();
        var oldDeliveryDirect = $("input[name='oldDeliveryDirect']").val();
        var deliveryDirect = $('input:radio[name="deliveryDirect"]:checked').val();
        if (oldDeliveryDirect == 0 && deliveryDirect == 1 && isUpdateDeliveryDirect == 1) {
            warnErrorTips("isUpdateDeliveryDirect", "isUpdateDeliveryDirectError", "采购单关联多个销售单不允许改成直发");
            $('input:radio:first').attr('checked', 'true');
            return false;
        } else if (oldDeliveryDirect == 0 && deliveryDirect == 1 && isUpdateDeliveryDirect == 2) {
            warnErrorTips("isUpdateDeliveryDirect", "isUpdateDeliveryDirectError", "采购单关联的销售商品含普发商品不允许改成直发");
            $('input:radio:first').attr('checked', 'true');
            return false;
        } else if (oldDeliveryDirect == 0 && deliveryDirect == 1 && isUpdateDeliveryDirect == 0) {
            $("li.ptz").removeClass("none");
            $("li.pf").addClass("none");

            /*<input type="hidden" id="ptz_takeTraderId" value="${bv.saleTakeTraderId}">
            <input type="hidden" id="ptz_takeTraderName" value="${bv.saleTakeTraderName}">
            <input type="hidden" id="ptz_takeTraderContactId" value="${bv.saleTakeTraderContactId}">
            <input type="hidden" id="ptz_takeTraderContactName" value="${bv.saleTakeTraderContactName}">
            <input type="hidden" id="ptz_takeTraderContactMobile" value="${bv.saleTakeTraderContactMobile}">
            <input type="hidden" id="ptz_takeTraderContactTelephone" value="${bv.saleTakeTraderContactTelephone}">
            <input type="hidden" id="ptz_takeTraderAddressId" value="${bv.saleTakeTraderAddressId}">
            <input type="hidden" id="ptz_takeTraderArea" value="${bv.saleTakeTraderArea}">
            <input type="hidden" id="ptz_takeTraderAddress" value="${bv.saleTakeTraderAddress}">*/

            $("input[name='takeTraderId']").val($("#ptz_takeTraderId").val());
            $("input[name='takeTraderName']").val($("#ptz_takeTraderName").val());
            $("input[name='takeTraderContactId']").val($("#ptz_takeTraderContactId").val());
            $("input[name='takeTraderAddressId']").val($("#ptz_takeTraderAddressId").val());
            $("input[name='takeTraderArea']").val($("#ptz_takeTraderArea").val());
            $("input[name='takeTraderAddress']").val($("#ptz_takeTraderAddress").val());
            $("input[name='takeTraderContactName']").val($("#ptz_takeTraderContactName").val());
            $("input[name='takeTraderContactMobile']").val($("#ptz_takeTraderContactMobile").val());
            $("input[name='takeTraderContactTelephone']").val($("#ptz_takeTraderContactTelephone").val());

        } else if (oldDeliveryDirect == 0 && deliveryDirect == 0) {
            $("li.ptz").addClass("none");
            $("li.pf").removeClass("none");
            var str = $("#address_pf").val();
            if (str != undefined && str != '') {
                $("input[name='takeTraderName']").val($("#companyName").val());
                $("input[name='takeTraderAddressId']").val(str.split('|')[0]);
                $("input[name='takeTraderArea']").val(str.split('|')[1]);
                $("input[name='takeTraderAddress']").val(str.split('|')[2]);
                $("input[name='takeTraderContactName']").val(str.split('|')[3]);
                $("input[name='takeTraderContactMobile']").val(str.split('|')[4]);
                $("input[name='takeTraderContactTelephone']").val(str.split('|')[5]);
            }
        } else if (oldDeliveryDirect == 1 && deliveryDirect == 0) {
            $("li.zf").addClass("none");
            $("li.ztp").removeClass("none");
            var str = $("#address_pf").val();
            if (str != undefined && str != '') {
                $("input[name='takeTraderName']").val($("#companyName").val());
                $("input[name='takeTraderAddressId']").val(str.split('|')[0]);
                $("input[name='takeTraderArea']").val(str.split('|')[1]);
                $("input[name='takeTraderAddress']").val(str.split('|')[2]);
                $("input[name='takeTraderContactName']").val(str.split('|')[3]);
                $("input[name='takeTraderContactMobile']").val(str.split('|')[4]);
                $("input[name='takeTraderContactTelephone']").val(str.split('|')[5]);
            }
        } else if (oldDeliveryDirect == 1 && deliveryDirect == 1) {
            $("li.zf").removeClass("none");
            $("li.ztp").addClass("none");
            $("input[name='takeTraderId']").val($("input[name='oldTakeTraderId']").val());
            $("input[name='takeTraderName']").val($("input[name='oldTakeTraderName']").val());
            $("input[name='takeTraderContactId']").val($("input[name='oldTakeTraderContactId']").val());
            $("input[name='takeTraderAddressId']").val($("input[name='oldTakeTraderAddressId").val());
            $("input[name='takeTraderArea']").val($("input[name='oldTakeTraderArea']").val());
            $("input[name='takeTraderAddress']").val($("input[name='oldTakeTraderAddress']").val());
            $("input[name='takeTraderContactName']").val($("input[name='oldTakeTraderContactName']").val());
            $("input[name='takeTraderContactMobile']").val($("input[name='oldTakeTraderContactMobile']").val());
            $("input[name='takeTraderContactTelephone']").val($("input[name='oldTakeTraderContactTelephone']").val());
        }
    })

})

function changeSendGoodsTime(obj,buyorderGoodsId) {
    checkLogin();
    var sendGoodsTimeStr = $(obj).val();
    if (sendGoodsTimeStr != '' && sendGoodsTimeStr != undefined) {
        $(obj).siblings("input[name='sendGoodsTimeStr']").val(buyorderGoodsId + "|" + sendGoodsTimeStr);
    }
}

function changeReceiveGoodsTime(obj,buyorderGoodsId) {
    checkLogin();
    var receiveGoodsTimeStr = $(obj).val();
    if (receiveGoodsTimeStr != '' && receiveGoodsTimeStr != undefined) {
        $(obj).siblings("input[name='receiveGoodsTimeStr']").val(buyorderGoodsId + "|" + receiveGoodsTimeStr);
    }
}

function changComments(obj, buyorderGoodsId) {
    checkLogin();
    var insideComments = $(obj).val();
    if (insideComments != '' && insideComments != undefined) {
        $(obj).siblings("input[name='insideCommentsArray']").val(buyorderGoodsId + "|" + insideComments);
    }

}

function editSubmit() {

    checkLogin();
    var $form = $("#myform");
    $("form").find('.warning').remove();
    var isUpdateDeliveryDirect = $('#isUpdateDeliveryDirect').val();
    var oldDeliveryDirect = $("input[name='oldDeliveryDirect']").val();
    var deliveryDirect = $('input:radio[name="deliveryDirect"]:checked').val();

    if (oldDeliveryDirect == 0 && deliveryDirect == 1 && isUpdateDeliveryDirect == 1) {
        warnErrorTips("isUpdateDeliveryDirect", "isUpdateDeliveryDirectError", "采购单关联多个销售单不允许改成直发");
        $('input:radio:first').attr('checked', 'true');
        return false;
    } else if (oldDeliveryDirect == 0 && deliveryDirect == 1 && isUpdateDeliveryDirect == 2) {
        warnErrorTips("isUpdateDeliveryDirect", "isUpdateDeliveryDirectError", "采购单关联的销售商品含普发商品不允许改成直发");
        $('input:radio:first').attr('checked', 'true');
        return false;
    } else if (oldDeliveryDirect == 0 && deliveryDirect == 1 && isUpdateDeliveryDirect == 0) {
        $("li.ptz").removeClass("none");
        $("li.pf").addClass("none");


        $("input[name='takeTraderId']").val($("#ptz_takeTraderId").val());
        $("input[name='takeTraderName']").val($("#ptz_takeTraderName").val());
        $("input[name='takeTraderContactId']").val($("#ptz_takeTraderContactId").val());
        $("input[name='takeTraderAddressId']").val($("#ptz_takeTraderAddressId").val());
        $("input[name='takeTraderArea']").val($("#ptz_takeTraderArea").val());
        $("input[name='takeTraderAddress']").val($("#ptz_takeTraderAddress").val());
        $("input[name='takeTraderContactName']").val($("#ptz_takeTraderContactName").val());
        $("input[name='takeTraderContactMobile']").val($("#ptz_takeTraderContactMobile").val());
        $("input[name='takeTraderContactTelephone']").val($("#ptz_takeTraderContactTelephone").val());

    } else if (oldDeliveryDirect == 0 && deliveryDirect == 0) {
        $("li.ptz").addClass("none");
        $("li.pf").removeClass("none");
        var str = $("#address_pf").val();
        if (str != undefined && str != '') {
            $("input[name='takeTraderName']").val($("#companyName").val());
            $("input[name='takeTraderAddressId']").val(str.split('|')[0]);
            $("input[name='takeTraderArea']").val(str.split('|')[1]);
            $("input[name='takeTraderAddress']").val(str.split('|')[2]);
            $("input[name='takeTraderContactName']").val(str.split('|')[3]);
            $("input[name='takeTraderContactMobile']").val(str.split('|')[4]);
            $("input[name='takeTraderContactTelephone']").val(str.split('|')[5]);
        }
    } else if (oldDeliveryDirect == 1 && deliveryDirect == 0) {
        $("li.zf").addClass("none");
        $("li.ztp").removeClass("none");
        var str = $("#address_ztp").val();
        if (str != undefined && str != '') {
            $("input[name='takeTraderName']").val($("#companyName").val());
            $("input[name='takeTraderAddressId']").val(str.split('|')[0]);
            $("input[name='takeTraderArea']").val(str.split('|')[1]);
            $("input[name='takeTraderAddress']").val(str.split('|')[2]);
            $("input[name='takeTraderContactName']").val(str.split('|')[3]);
            $("input[name='takeTraderContactMobile']").val(str.split('|')[4]);
            $("input[name='takeTraderContactTelephone']").val(str.split('|')[5]);
        }
    } else if (oldDeliveryDirect == 1 && deliveryDirect == 1) {
        $("li.zf").removeClass("none");
        $("li.ztp").addClass("none");
        $("input[name='takeTraderId']").val($("input[name='oldTakeTraderId']").val());
        $("input[name='takeTraderName']").val($("input[name='oldTakeTraderName']").val());
        $("input[name='takeTraderContactId']").val($("input[name='oldTakeTraderContactId']").val());
        $("input[name='takeTraderAddressId']").val($("input[name='oldTakeTraderAddressId").val());
        $("input[name='takeTraderArea']").val($("input[name='oldTakeTraderArea']").val());
        $("input[name='takeTraderAddress']").val($("input[name='oldTakeTraderAddress']").val());
        $("input[name='takeTraderContactName']").val($("input[name='oldTakeTraderContactName']").val());
        $("input[name='takeTraderContactMobile']").val($("input[name='oldTakeTraderContactMobile']").val());
        $("input[name='takeTraderContactTelephone']").val($("input[name='oldTakeTraderContactTelephone']").val());
    }

    /*【采购预计发货日】和【采购预计收货日】校验*/
    var sendGoodsTimeFlag = true;
    var receiveGoodsTimeFlag = true;
    var dateCompareFlag = true;
    $.each($("input[name='xSendGoodsTime']"),function(i,n){
        var sendTimeStr = $(this).val();
        if(sendTimeStr ==''||sendTimeStr == undefined){
            $(this).addClass("errorbor");
            sendGoodsTimeFlag = false;
            return false;
        }
        var receviceDateStr = $(this).parent().next().children().val();
        if(receviceDateStr != '' && receviceDateStr < sendTimeStr){
            $(this).parent().next().children().addClass("errorbor");
            dateCompareFlag = false;
            return false;
        }
    });
    /*$.each($("input[name='xReceiveGoodsTime']"),function(i,n){
        let sendTimeStr = $(this).val();
        if(sendTimeStr ==''||sendTimeStr == undefined){
            $(this).addClass("errorbor");
            receiveGoodsTimeFlag = false;
            return false;
        }
    });*/
    if(!sendGoodsTimeFlag){
        layer.alert("请填写供应商预计发货日");
        return false;
    }
    /*if(!receiveGoodsTimeFlag){
        layer.alert("请填写采购预计到货日");
        return false;
    }*/
    if(!dateCompareFlag){
        layer.alert("收货时间应该大于或等于发货时间");
        return false;
    }

    var logisticsComments = $("#logisticsComments").val();
    if (logisticsComments != '' && logisticsComments.length > 256) {
        warnTips("logisticsComments", "物流备注不允许超过256个字符");
        return false;
    }

    var invoiceComments = $("#invoiceComments").val();
    if (invoiceComments != '' && invoiceComments.length > 256) {
        warnTips("invoiceComments", "开票备注不允许超过256个字符");
        return false;
    }

    debugger;

    var logisticsChange = oldDeliveryDirect != deliveryDirect || $("#logisticsComments").val() != $("#oldLogisticsComments").val();

    //没有变
    if(!logisticsChange){
        $form.submit();
        return;
    }

    $("#synWmsCancel").val("1");
    var buyorderNo = $("#buyorderNo").val();

    //请求WMS看是否能取消订单,如果不能取消就不让提交申请了
    $.ajax({
        type: "POST",
        url: "./cancelOrder.do",
        data: {'buyorderNo':buyorderNo},
        dataType:'json',
        success: function(data){
            //可以取消 提交表单
            if(data.code == 0){
                $form.submit();
            }else{
                layer.alert(data.message);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });


}

