package com.vedeng.goods.controller;

import com.google.common.collect.Lists;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.ResultJSON;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.common.validator.FormToken;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.goods.service.ManufacturerService;
import com.vedeng.system.model.Attachment;
import com.vedeng.trader.service.TraderSupplierService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/goods/manufacturer")
public class ManufacturerController extends BaseController {

    @Autowired
    private ManufacturerService manufacturerService;

    @Autowired
    private TraderSupplierService traderSupplierService;


    /**
     * 生产企业列表 生产企业列表查询
     *
     * @param request
     * @param manufacturer
     * @param pageNo
     * @param tag
     * @param pageSize
     * @return
     */
    @RequestMapping("/getProductCompanyInfo")
    @ResponseBody
    public Object getProductCompanyInfoListPage(HttpServletRequest request, Manufacturer manufacturer,
                                                @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                @RequestParam(required = false, defaultValue = "1") Integer tag,
                                                @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        ModelAndView mv = new ModelAndView();
        try {
            // 当前登陆用户
            User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            // 分页信息
            Page page = getPageTag(request, pageNo, pageSize);
            // 参数集
            Map<String, Object> paramMap = new HashMap<>();

            //生产企业
            String manufacturerName = manufacturer.getManufacturerName();
            paramMap.put("manufacturerName", manufacturerName);
            //用于判断页面上的选择条件是否是 -1即全部
            mv.addObject("isWhole", manufacturer.getAssignmentManagerId());
            // 用户信息
            paramMap.put("userId", manufacturer.getAssignmentManagerId() == null || "-1".equals(manufacturer.getAssignmentManagerId().toString()) ? sessUser.getUserId() : manufacturer.getAssignmentManagerId());

            paramMap.put("manufacturer", manufacturer);
            // 分页信息
            paramMap.put("page", page);
            // 获取商品首营列表 getFirstEngageInfoListPage
            //获取生产企业管理列表
            Map<String, Object> mapResult = manufacturerService.getProductCompanyInfoListPage(paramMap, page, manufacturer);

            mv.addObject("manufacturerList", mapResult.get("manufacturerList"));
            List<User> usrlist = (List<User>) mapResult.get("userList");
            String isAddUser = "0";
            //是否需要将session中的对象添加到usrlist  0需要 1不需要
            for (User user : usrlist) {
                if (user.getUserId().equals(sessUser.getUserId())) {
                    isAddUser = "1";
                    break;
                }
            }

            if (manufacturer.getAssignmentManagerId() == null) {
                if ("0".equals(isAddUser)) {
                    usrlist.add(sessUser);
                }
            } else if (manufacturer.getAssignmentManagerId().intValue() == sessUser.getUserId().intValue()) {
                if ("0".equals(isAddUser)) {
                    usrlist.add(sessUser);
                }
            }

            mv.addObject("userList", usrlist);
            mv.addObject("page", mapResult.get("page"));
            mv.addObject("tag", tag);
            mv.addObject("manufacturer", manufacturer);
            mv.addObject("userId", sessUser.getUserId());
        } catch (Exception e) {
            logger.error("生产企业列表:", e);
        }
        mv.setViewName("manufacturer/Product/index");
        return mv;
    }

    /**
     * 跳转 生产企业 新增或编辑页面
     *
     * @param request
     * @param manufacturerId
     * @param manufacturerName
     * @return
     */
    @FormToken(save = true)
    @RequestMapping(value = "/addProduct")
    public ModelAndView addProduct(HttpServletRequest request, Integer manufacturerId, String manufacturerName) {
        ModelAndView mv = new ModelAndView();
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            mv.addObject("user", user);
            if (null != manufacturerId) {
                Manufacturer manufacturer = manufacturerService.getManufacturerDetail(manufacturerId);
                mv.addObject("yzMapList", new JSONArray(manufacturer.getYzAttachments()).toString());
                mv.addObject("scMapList", new JSONArray(manufacturer.getScAttachments()).toString());
                mv.addObject("rcMapList", new JSONArray(manufacturer.getRcAttachments()).toString());
                mv.addObject("djbMapList", new JSONArray(manufacturer.getDjbAttachments()).toString());
                mv.addObject("manufacturer", manufacturer);
                //传当前生产商的id
                mv.addObject("manufacturerId", manufacturer.getManufacturerId());
                //展示照片用
                mv.addObject("api_http", api_http);
            } else {
                if (StringUtils.isNotBlank(manufacturerName)) {
                    // 触发首营【供应商生产厂商】转换【生产厂商】
                    Manufacturer manufacturer = traderSupplierService.traderSupplierTransformToManufacturer(manufacturerName);
                    if (manufacturer != null) {
                        // 带信息供应商列表中以及存在该生产企业，部分资质信息以及同步
                        mv.addObject("yzMapList", new JSONArray(manufacturer.getYzAttachments()).toString());
                        mv.addObject("scMapList", new JSONArray(manufacturer.getScAttachments()).toString());
                        mv.addObject("djbMapList", new JSONArray(manufacturer.getDjbAttachments()).toString());
                        mv.addObject("rcMapList", new JSONArray(manufacturer.getRcAttachments()).toString());
                        mv.addObject("manufacturer", manufacturer);
                        //展示照片用
                        mv.addObject("api_http", api_http);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("添加首营->生产企业异常:", e);
        }
        mv.setViewName("manufacturer/Product/new_add");
        return mv;
    }


    /**
     * 校验供应商是否已经存在
     *
     * @param manufacturer
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/checkManufacturerName", method = RequestMethod.GET)
    public ResultJSON checkManufacturerName(Manufacturer manufacturer) {
        try {
            // 校验供应商是否已经存在
            Integer relateId = traderSupplierService.isExistManufacturer(manufacturer.getManufacturerName(), true);
            if (relateId != null) {
                return ResultJSON.success().message("操作成功").data(relateId);
            }
            return ResultJSON.failed();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultJSON.failed();
        }

    }

    /**
     * 生产厂商新增 点击保存按钮
     *
     * @param request
     * @param manufacturer
     */
    @FormToken(remove = true)
    @NoNeedAccessAuthorization
    @RequestMapping("/addProductInfo")
    public ModelAndView addProductInfo(HttpServletRequest request, Manufacturer manufacturer) {
        Integer resId = null;
        //点击编辑时传 manufacturerId 用来判断是新增还是编辑
        try {
            User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            try {
                // 校验生产列表参数
                manufacturerService.checkProductInfo(manufacturer);
            } catch (ShowErrorMsgException e) {
                manufacturer.setErrors(Lists.newArrayList(e.getErrorMsg()));
                request.setAttribute("manufacturer", manufacturer);
                return new ModelAndView("forward:./newpageProduct.do");
            }
            // 添加生产企业信息
            resId = manufacturerService.addProductInfoTimeChange(manufacturer, sessUser.getUserId(), true);
        } catch (Exception e) {
            logger.error("新增生产厂商信息:", e);
        }

        return new ModelAndView("redirect:/goods/manufacturer/getManufacturerDetail.do?manufacturerId=" + resId);
    }


    @FormToken(save = true)
    @RequestMapping("/newpageProduct")
    @NoNeedAccessAuthorization
    public ModelAndView getForwardPage(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mav = new ModelAndView();
        Manufacturer manufacturer = (Manufacturer) request.getAttribute("manufacturer");
        mav.addObject("user", user);

        getAttachments(manufacturer.getYzAttachments(), mav, "yzMapList");
        getAttachments(manufacturer.getScAttachments(), mav, "scMapList");
        getAttachments(manufacturer.getDjbAttachments(), mav, "djbMapList");
        getAttachments(manufacturer.getRcAttachments(), mav, "rcMapList");
        mav.addObject("manufacturer", manufacturer);

        mav.setViewName("manufacturer/Product/new_add");
        return mav;
    }

    /**
     * 获取 附件信息
     *
     * @param attachments
     * @param mv
     * @param name
     */
    private void getAttachments(List<Attachment> attachments, ModelAndView mv, String name) {
        // 空判断
        if (CollectionUtils.isNotEmpty(attachments)) {
            // 定义返回值
            List<Map<String, Object>> resMapList = new ArrayList<>();
            int size = attachments.size();
            for (int i = 0; i < size; i++) {
                Attachment attachment = attachments.get(i);
                // 编辑注册证附件信息
                Map<String, Object> attachmentMap = new HashMap<>();
                attachmentMap.put("message", "操作成功");
                attachmentMap.put("httpUrl", api_http + domain);
                // uri
                String uri = attachment.getUri();
                if (EmptyUtils.isEmpty(uri)) {
                    continue;
                }
                String[] uriArray = uri.split("/");
                String fileName = uriArray[uriArray.length - 1];
                String fileNameTemp = "/" + fileName;
                // 文件后缀
                String[] prefixArray = fileNameTemp.split("\\.");
                String prefix = prefixArray[prefixArray.length - 1];
                // 去除路径名
                String filePath = uri.replaceAll(fileNameTemp, "");
                attachmentMap.put("fileName", fileName);
                attachmentMap.put("filePath", filePath);
                attachmentMap.put("prefix", prefix);
                resMapList.add(attachmentMap);
            }
            // 放入modelandview
            if (CollectionUtils.isNotEmpty(resMapList)) {
                mv.addObject(name, new JSONArray(resMapList).toString());
            }
        }
    }


    /**
     * 点击上传按钮 进入上传企业相关附件
     *
     * @param request
     * @param manufacturerId
     * @return
     */
    @FormToken(save = true)
    @RequestMapping(value = "/addOfficialB")
    public ModelAndView addOfficialB(HttpServletRequest request, Integer manufacturerId) {

        ModelAndView mv = new ModelAndView();
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

            mv.addObject("user", user);
            if (null != manufacturerId) {
                Manufacturer manufacturer = manufacturerService.getOfficialB(manufacturerId);

                // 营业执照（贝） 营照(贝)
                mv.addObject("yzBMapList", new JSONArray(manufacturer.getYzBAttachments()).toString());
                //生产企业生产许可证生产企业(贝)
                mv.addObject("scBMapList", new JSONArray(manufacturer.getScBAttachments()).toString());
                //生产企业备案凭证(贝)
                mv.addObject("rcBMapList", new JSONArray(manufacturer.getRcBAttachments()).toString());
                //生产企业生产产品登记表(即登记表附近)（贝）(即注册登记表附件) 登记表（贝）
                mv.addObject("djbBMapList", new JSONArray(manufacturer.getDjbBAttachments()).toString());

                mv.addObject("manufacturer", manufacturer);
            }

        } catch (Exception e) {
            logger.error("上传企业相关附件异常:", e);
        }
        mv.setViewName("manufacturer/Product/new_addOfficialB");
        return mv;
    }

    /**
     * 点击保存 上传企业相关附件
     *
     * @param request
     * @param manufacturer
     * @return
     */
    @FormToken(remove = true)
    @RequestMapping("/upLoadOfficialB")
    @ResponseBody
    public ResultInfo upLoadOfficialB(HttpServletRequest request, Manufacturer manufacturer) {
        ResultInfo resultInfo = new ResultInfo();
        try {
            // 当前登陆用户
            User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            // 上传贝登公章（贝）
            manufacturerService.uploadOfficialWithB(manufacturer, sessUser);
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        } catch (Exception e) {
            logger.error("操作异常:", e);
            resultInfo.setMessage("操作失败");
            return resultInfo;
        }
        return resultInfo;
    }

    /**
     * 生产企业点击详情
     *
     * @param request
     * @param manufacturerId
     * @param showCheck
     * @return
     */
    @RequestMapping("/getManufacturerDetail")
    public ModelAndView getManufacturerDetail(HttpServletRequest request, Integer manufacturerId, Integer showCheck) {
        ModelAndView mv = new ModelAndView();
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            mv.addObject("user", user);
            if (null != manufacturerId) {
                // 是否展示审核记录
                if (Objects.nonNull(showCheck) && showCheck.equals(0)) {
                    mv.addObject("notShowCheck", 0);
                }
                Manufacturer manufacturer = manufacturerService.getManufacturerDetail(manufacturerId);
                mv.addObject("yzMapList", new JSONArray(manufacturer.getYzAttachments()).toString());
                mv.addObject("scMapList", new JSONArray(manufacturer.getScAttachments()).toString());
                mv.addObject("djbMapList", new JSONArray(manufacturer.getDjbAttachments()).toString());
                mv.addObject("rcMapList", new JSONArray(manufacturer.getRcAttachments()).toString());

                mv.addObject("yzBMapList", new JSONArray(manufacturer.getYzBAttachments()).toString());
                mv.addObject("scBMapList", new JSONArray(manufacturer.getScBAttachments()).toString());
                mv.addObject("djbBMapList", new JSONArray(manufacturer.getDjbBAttachments()).toString());
                mv.addObject("rcBMapList", new JSONArray(manufacturer.getRcBAttachments()).toString());
                mv.addObject("manufacturer", manufacturer);
                //传当前生产商的id
                mv.addObject("manufacturerId", manufacturer.getManufacturerId());
                //展示照片用
                mv.addObject("api_http", api_http);
                // 添加审核记录
                List<LogCheckGenerate> logCheckGenerates = manufacturerService.listCheckLog(manufacturerId);
                logCheckGenerates.forEach(i -> i.setCreatorName(getRealNameByUserName(i.getCreatorName())));
                mv.addObject("checkList", logCheckGenerates);

            }

        } catch (Exception e) {
            logger.error("获取生产企业信息失败:", e);
        }
        mv.setViewName("manufacturer/Product/view");
        return mv;
    }

    /**
     * 生产企业删除
     *
     * @param request
     * @param manufacturerId
     * @return
     */
    @RequestMapping("/deleteManufacturer")
    @ResponseBody
    public ResultInfo deleteManufacturer(HttpServletRequest request, Integer manufacturerId) {
        try {
            User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            return manufacturerService.deleteManufacturer(manufacturerId, sessUser.getUserId());

        } catch (Exception e) {
            logger.error("删除生产企业信息", e);
        }
        return new ResultInfo<>();
    }

    /**
     * 生产企业审核
     *
     * @param request
     * @param manufacturer
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/submitCheck")
    @NoNeedAccessAuthorization
    public ResultJSON submitCheckManufacturer(HttpServletRequest request, Manufacturer manufacturer) {
        try {
            User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            manufacturerService.auditManufacture(manufacturer, sessUser);
        } catch (ServiceException e) {
            return ResultJSON.failed().message(e.getMessage());
        } catch (Exception e) {
            logger.error("提交生产企业审核错误：msg:", e);
            return ResultJSON.failed().message("提交生产企业审核错误,请重试!");
        }
        return ResultJSON.success().message("操作成功");
    }
}
