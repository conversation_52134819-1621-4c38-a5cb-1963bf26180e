

function queryTerminalData(data) {
    return axios({
        url: '/traderCustomerBase/queryTerminalData.do',
        method: 'post',
        params: data
    })
}

function traderCustomerTerminalAdd(data) {
    return axios({
        url: '/traderCustomerTerminalApi/add.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function traderCustomerTerminalUpdate(data) {
    return axios({
        url: '/traderCustomerTerminalApi/update.do',
        method: 'post',
        params: data
    })
}

function traderCustomerTerminalData(data) {
    return axios({
        url: '/traderCustomerTerminalApi/getData.do',
        method: 'post',
        params: data
    })
}

function getAuditRecordList(data) {
    return axios({
        url: '/traderCustomerTerminalApi/getAuditRecordList.do',
        method: 'post',
        params: data
    })
}

function searchTerminalInfo(data) {
    return axios({
        url: '/order/terminal/searchOneData.do',
        method: 'post',
        params: data
    })
}

function searchTycTerminal(data) {
    return axios({
        url: '/order/terminal/searchTyc.do',
        method: 'post',
        params: data
    })
}

function getSaleOrderTerminalBySaleOrderId(data) {
    return axios({
        url: '/order/terminal/saleOrderId.do',
        method: 'post',
        params: data
    })
}

function getTerminalTraderNature(data) {
    return axios({
        url: '/order/terminal/getNatureBySaleOrderId.do',
        method: 'post',
        params: data
    })
}

function searchTycTerminalAndCheck(data) {
    return axios({
        url: '/traderCustomerBase/searchTyc.do',
        method: 'post',
        params: data
    })
}

function saveSaleOrderTerminal(data) {
    return axios({
        url: '/order/terminal/add.do',
        method: 'post',
        data: data
    })
}

function traderTerminalAuditData(data) {

    return axios({
        url: '/traderCustomerTerminalApi/detail.do',
        method: 'post',
        params: data
    })
}

// 审核通过
function traderTerminalAuditPass(data) {
    return axios({
        url: '/traderCustomerTerminalApi/pass.do',
        method: 'post',
        params: data
    })
}
// 审核不通过
function traderTerminalAuditReject(data) {
    return axios({
        url: '/traderCustomerTerminalApi/reject.do',
        method: 'post',
        params: data
    })
}

function getTraderCustomerPage(data) {
    return axios({
        url: '/traderCustomerBase/page.do',
        method: 'post',
        data: data
    })
}

function saveSaleOrderTerminalCustomer(data) {
    return axios({
        url: '/order/terminal/customer/save.do',
        method: 'post',
        data: data
    })
}

function getSaleOrderTerminalCustomerByOrderId(data) {
    return axios({
        url: '/order/terminal/customer/saleOrderId.do',
        method: 'post',
        params: data
    })
}