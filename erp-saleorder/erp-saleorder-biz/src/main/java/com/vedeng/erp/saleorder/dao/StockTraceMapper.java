package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.dto.StockTraceDto;
import com.vedeng.erp.saleorder.dto.StockTraceResult;
import com.vedeng.erp.saleorder.dto.WarehouseLog;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName WarehouseGoodsOperateLogMapper.java
 * @Description TODO
 * @createTime 2022年09月15日 09:50:00
 */
public interface StockTraceMapper {

    /**
     * @description: 直发采购单查询对应销售单
     * @return:
     * @author: Strange
     * @date: 2022/9/19
     **/
    List<StockTraceResult> getSaleByBuorderSku(StockTraceDto stockTrace);

    /**
     * 获取销售商品发货方式
     * @param stockTrace
     * @return
     */
    Integer getSaleSkuDeliverDirect(StockTraceDto stockTrace);

    /**
     * 获取入库记录信息
     * @param stockTrace
     * @return
     */
    List<WarehouseLog> getInWarehouseList(StockTraceDto stockTrace);

    /**
     * 获取关联出库记录
     * @param barcodeId
     * @return
     */
    List<WarehouseLog> getOutWarehouseListByBarcodeId(Integer barcodeId);
}
