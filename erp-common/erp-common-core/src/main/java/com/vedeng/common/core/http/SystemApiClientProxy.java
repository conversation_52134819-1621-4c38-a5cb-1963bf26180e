package com.vedeng.common.core.http;

import com.vedeng.common.core.enums.SystemSourceEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * SystemApiClient 智能代理类
 *
 * 设计目标：
 * - 避免业务模块直接依赖 erp-temporal
 * - 保留 CompanyApiConfigService 的自动域名获取功能
 * - 避免代码重复
 * - 提供统一的系统API调用入口
 *
 * 工作原理：
 * 1. 通过 Spring ApplicationContext 动态获取 SystemApiClient 实例
 * 2. 将所有调用委托给真实的 SystemApiClient
 * 3. 对外提供统一的 ISystemApiClient 接口
 * 
 * 适用场景：
 * - 任何需要调用系统API的业务模块
 * - 避免直接依赖 erp-temporal 模块的场景
 * - 需要使用 CompanyApiConfigService 自动域名获取的场景
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20
 */
@Component
public class SystemApiClientProxy implements ISystemApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemApiClientProxy.class);

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * SystemApiClient 实例（懒加载）
     */
    private volatile ISystemApiClient systemApiClient;

    /**
     * 懒加载获取 SystemApiClient 实例
     * 优雅地处理依赖获取，支持延迟初始化
     */
    private ISystemApiClient getSystemApiClient() {
        if (systemApiClient == null) {
            synchronized (this) {
                if (systemApiClient == null) {
                    systemApiClient = resolveSystemApiClient();
                }
            }
        }
        return systemApiClient;
    }

    /**
     * 解析 SystemApiClient 实例
     * 使用多种策略尝试获取实例，提供更好的容错性
     */
    private ISystemApiClient resolveSystemApiClient() {
        logger.debug("开始解析 SystemApiClient 实例...");

        // 策略1：按类型获取（推荐）
        try {
            ISystemApiClient client = applicationContext.getBean(ISystemApiClient.class);
            // 确保不是代理自己
            if (client != this) {
                logger.info("成功通过类型获取 SystemApiClient: {}", client.getClass().getSimpleName());
                return client;
            }
        } catch (Exception e) {
            logger.debug("按类型获取 SystemApiClient 失败: {}", e.getMessage());
        }

        // 策略2：按名称获取
        try {
            ISystemApiClient client = applicationContext.getBean("systemApiClient", ISystemApiClient.class);
            logger.info("成功通过名称获取 SystemApiClient: {}", client.getClass().getSimpleName());
            return client;
        } catch (Exception e) {
            logger.debug("按名称获取 SystemApiClient 失败: {}", e.getMessage());
        }

        // 策略3：查找所有实现并过滤
        try {
            Map<String, ISystemApiClient> beans = applicationContext.getBeansOfType(ISystemApiClient.class);
            for (Map.Entry<String, ISystemApiClient> entry : beans.entrySet()) {
                ISystemApiClient client = entry.getValue();
                // 排除代理自己，查找真实实现
                if (client != this && !client.getClass().equals(this.getClass())) {
                    logger.info("成功通过类型扫描获取 SystemApiClient: {} ({})",
                              entry.getKey(), client.getClass().getSimpleName());
                    return client;
                }
            }
        } catch (Exception e) {
            logger.debug("类型扫描获取 SystemApiClient 失败: {}", e.getMessage());
        }

        // 所有策略都失败
        String errorMsg = "无法获取 SystemApiClient 实例，请确保 erp-temporal 模块已正确配置并且 SystemApiClient Bean 已注册";
        logger.error(errorMsg);
        throw new IllegalStateException(errorMsg);
    }
    
    @Override
    public ISystemApiClient withUser(Long userId) {
        return getSystemApiClient().withUser(userId);
    }

    @Override
    public ISystemApiClient withUser(String userName) {
        return getSystemApiClient().withUser(userName);
    }

    @Override
    public ISystemApiClient withCompany(String companyCode) {
        return getSystemApiClient().withCompany(companyCode);
    }

    @Override
    public ISystemApiClient clearUser() {
        return getSystemApiClient().clearUser();
    }

    @Override
    public ISystemApiClient clearCompany() {
        return getSystemApiClient().clearCompany();
    }
    
    @Override
    public String postToSystemApi(String apiPath, Object requestData) {
        return getSystemApiClient().postToSystemApi(apiPath, requestData);
    }

    @Override
    public String postToSystemApi(String apiPath, Object requestData, Map<String, String> additionalHeaders) {
        return getSystemApiClient().postToSystemApi(apiPath, requestData, additionalHeaders);
    }

    @Override
    public String getFromSystemApi(String apiPath) {
        return getSystemApiClient().getFromSystemApi(apiPath);
    }

    @Override
    public String getFromSystemApi(String apiPath, Map<String, String> additionalHeaders) {
        return getSystemApiClient().getFromSystemApi(apiPath, additionalHeaders);
    }

    @Override
    public String postToSystemApi(String apiPath, Object requestData, String systemSource) {
        return getSystemApiClient().postToSystemApi(apiPath, requestData, systemSource);
    }

    @Override
    public String postToSystemApi(String apiPath, Object requestData, Map<String, String> additionalHeaders, String systemSource) {
        return getSystemApiClient().postToSystemApi(apiPath, requestData, additionalHeaders, systemSource);
    }

    @Override
    public String getFromSystemApi(String apiPath, String systemSource) {
        return getSystemApiClient().getFromSystemApi(apiPath, systemSource);
    }

    @Override
    public String getFromSystemApi(String apiPath, Map<String, String> additionalHeaders, String systemSource) {
        return getSystemApiClient().getFromSystemApi(apiPath, additionalHeaders, systemSource);
    }

    @Override
    public String postToSystemApi(String apiPath, Object requestData, SystemSourceEnum systemSource) {
        return getSystemApiClient().postToSystemApi(apiPath, requestData, systemSource);
    }

    @Override
    public String getFromSystemApi(String apiPath, SystemSourceEnum systemSource) {
        return getSystemApiClient().getFromSystemApi(apiPath, systemSource);
    }

    @Override
    public boolean supportsAutoCompanyConfig() {
        return getSystemApiClient().supportsAutoCompanyConfig();
    }

    @Override
    public String getImplementationType() {
        ISystemApiClient client = getSystemApiClient();
        return "SystemApiClient-Proxy -> " + client.getImplementationType();
    }

    /**
     * 获取被代理的 SystemApiClient 实例
     * 注意：此方法会触发懒加载
     */
    public ISystemApiClient getTargetSystemApiClient() {
        return getSystemApiClient();
    }
}
