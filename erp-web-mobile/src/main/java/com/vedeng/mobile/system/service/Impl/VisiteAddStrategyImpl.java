package com.vedeng.mobile.system.service.Impl;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.util.TrackUtils;
import com.vedeng.mobile.system.service.TrackBaseService;

/**
 * 创建拜访计划
 * @ClassName:  VisiteAddStrategyImpl     
 * @author: <PERSON>.yang
 * @date:   2024年6月12日 下午7:25:03    
 * @Copyright:
 */
@Component
public class VisiteAddStrategyImpl extends TrackBaseService implements TrackStrategy {

	private static final Logger LOGGER = LoggerFactory.getLogger(VisiteAddStrategyImpl.class);
	
	@Override
	public EventTrackingEnum getTrackStrategy() {
		return EventTrackingEnum.PRE_SALE_NEW_VISITE;
	}

	@Override
	public boolean checkParamsLegal(TrackParamsData trackParamsData) {
		//获取唯一标识符
		Map<String, Object> trackParams = trackParamsData.getTrackParams();
		String primaryKey = trackParamsData.getEventTrackingEnum().getPrimaryKey();
		String primaryValue = TrackUtils.getJsonValue(new JSONObject(trackParams),primaryKey);
		//唯一标识符获取不到，不能埋点
		if(StringUtils.isEmpty(primaryValue) || "0".equals(primaryValue)) {
			LOGGER.info("=======埋点档案：{}，必填字段：{}缺失...",trackParamsData.getEventTrackingEnum().getArchivedName(),primaryKey);
			return false;
		}
		return true;
	}


}
