package com.vedeng.erp.trader.web.controller;


import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.service.CustomerRegionSaleRuleApiService;
import com.vedeng.erp.trader.service.VisitRecordApiService;
import com.vedeng.erp.trader.dto.CustomerRegionSaleRulesApiDto;
import com.vedeng.erp.trader.dto.VisitBatchInputApiDto;
import com.vedeng.erp.trader.dto.VisitConfigDto;
import com.vedeng.erp.trader.dto.VisitInputApiDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/visitrecord")
public class VisitController extends BaseController {

    @Value("${mobile.visitConfigList:[]}")
    private String visitConfigList;

    @Autowired
    private VisitRecordApiService visitRecordApiService;

    @Autowired
    private CustomerRegionSaleRuleApiService customerRegionSaleRuleApiService;

    @Autowired
    private UserApiService userApiService;


    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/profile/saveBatchVisitRecord")
    public ResultInfo saveBatchVisitRecord(@RequestBody VisitBatchInputApiDto visitBatchInputApiDto) {
        int result = visitRecordApiService.saveBatchVisitRecord(visitBatchInputApiDto);
        if (result > 0) {
            return ResultInfo.success("共创建成功拜访计划" + result + "条");
        }
        return ResultInfo.error("未成功创建拜访计划");
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/profile/modifyShareById")
    public ModelAndView modifyShareById(HttpServletRequest request) {
        String visitId = request.getParameter("_CHECKD_IDS");
        ModelAndView mv = new ModelAndView();
        mv.addObject("visitId", visitId);
        HttpSession session = request.getSession();
        String userList = (String) session.getAttribute("EZ_SESSION_MY_USER_MAP_KEY");
        mv.addObject("userList", userList);
        mv.setViewName("trader/visitrecord/visitrecord_modify");
        return mv;
    }

    /**
     * 修改线下销售所负责的区域
     *
     * @param request
     * @return
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/profile/modifyCustomerRegionSale")
    public ModelAndView modifyCustomerRegionSale(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("trader/sale/customer_region_modify");
        return mv;
    }

    /**
     * 保存修改线下销售所负责的区域
     *
     * @param customerRegionSaleRulesApiDto
     * @return
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/profile/saveCustomerRegionSale")
    public ResultInfo modifyCustomerRegionSale(@RequestBody CustomerRegionSaleRulesApiDto customerRegionSaleRulesApiDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        customerRegionSaleRulesApiDto.setUserId(currentUser.getId());
        customerRegionSaleRuleApiService.saveCustomerRegionSaleRule(customerRegionSaleRulesApiDto);
        return ResultInfo.success("reload");
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/profile/getVisitRecordNum")
    public ResultInfo getVisitRecordDetail() {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser == null) {
            Map<String, Integer> resultMap = new HashMap<>();
            resultMap.put("visitrecordNum", 0);
            return ResultInfo.success(resultMap);
        }
        Map<String, Integer> count = visitRecordApiService.queryVisitRecordByVisitorId(currentUser.getId());
        return ResultInfo.success(count);
    }


    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/profile/getVisitRecordDetail")
    public ModelAndView getVisitRecordDetail(Integer visitId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        VisitInputApiDto visitInputApiDto = visitRecordApiService.queryVisitRecordById(visitId);

        List<Integer> allSubordinateByUserIdForVisit = userApiService.getAllSubordinateByUserIdForVisit(currentUser.getId());
        visitInputApiDto.setTraderViewAble(visitInputApiDto.getCustomerFrom() == 1 && visitRecordApiService.checkIfTraderExists(allSubordinateByUserIdForVisit,visitInputApiDto.getTraderId(),427));
        this.dealVisitTargetNames(visitInputApiDto);
        ModelAndView mv = new ModelAndView();
        mv.addObject("visitDto", visitInputApiDto);
        mv.setViewName("trader/visitrecord/visitrecord_detail");
        return mv;
    }

    public void dealVisitTargetNames(VisitInputApiDto visitInputDto) {
        if (visitInputDto != null && StringUtils.isNotBlank(visitInputDto.getVisitTarget())) {
            List<VisitConfigDto> visitConfigDtoList = JSONObject.parseArray(visitConfigList, VisitConfigDto.class);
            //将visitConfigDtoList转换为map，key为visitType，value为visitName
            Map<String, String> visitConfigMap = visitConfigDtoList.stream().collect(Collectors.toMap(VisitConfigDto::getVisitType, VisitConfigDto::getVisitName));
            //将VisitInputDto.getVisitTarget()按英文逗号分割，并将visitConfigList转换成map，获取对应的中文名称
            String[] visitTargetArr = visitInputDto.getVisitTarget().split(",");
            List<String> visitTargetNameList = new ArrayList<>();
            for (String visitTarget : visitTargetArr) {
                visitTargetNameList.add(visitConfigMap.get(visitTarget));
            }
            visitInputDto.setVisitTargetNames(StringUtils.join(visitTargetNameList, ","));
        }
        if (visitInputDto.getTraderId() != null && visitInputDto.getTraderId() > 0 && visitInputDto.getCustomerTz() == null) {
            //如果没有关联到客户属性，一律认为是普通
            visitInputDto.setCustomerTz(0);
        }
    }


    @RequestMapping(value = "/index")
    @NoNeedAccessAuthorization
    public String index() {
        return view();
    }

}
