<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSaleSettlementAdjustmentDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto">
        <!--@mbg.generated-->
        <!--@Table T_SALE_SETTLEMENT_ADJUSTMENT-->
        <id column="SALE_SETTLEMENT_ADJUSTMENT_ID" jdbcType="INTEGER" property="saleSettlementAdjustmentId"/>
        <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId"/>
        <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo"/>
        <result column="AFTER_SALE_ID" jdbcType="INTEGER" property="afterSaleId"/>
        <result column="AFTER_SALE_NO" jdbcType="VARCHAR" property="afterSaleNo"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="RECEIVED_AMOUNT" jdbcType="DECIMAL" property="receivedAmount"/>
        <result column="DIFFERENCE_AMOUNT" jdbcType="DECIMAL" property="differenceAmount"/>
        <result column="ADJUSTMENT_TYPE" jdbcType="INTEGER" property="adjustmentType"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="IS_PUSHED" jdbcType="BOOLEAN" property="isPushed"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        SALE_SETTLEMENT_ADJUSTMENT_ID,
        SALEORDER_ID,
        SALEORDER_NO,
        AFTER_SALE_ID,
        AFTER_SALE_NO,
        TOTAL_AMOUNT,
        RECEIVED_AMOUNT,
        DIFFERENCE_AMOUNT,
        ADJUSTMENT_TYPE,
        ADD_TIME,
        CREATOR,
        CREATOR_NAME,
        UPDATER,
        UPDATER_NAME,
        MOD_TIME,
        IS_PUSHED
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_SALE_SETTLEMENT_ADJUSTMENT
        where SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_SALE_SETTLEMENT_ADJUSTMENT
        where SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="SALE_SETTLEMENT_ADJUSTMENT_ID" keyProperty="saleSettlementAdjustmentId"
            parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SALE_SETTLEMENT_ADJUSTMENT (SALEORDER_ID, SALEORDER_NO, AFTER_SALE_ID,
                                                  AFTER_SALE_NO, WAREHOUSE_OUT_ID, WAREHOUSE_OUT_NO, TOTAL_AMOUNT,
                                                  RECEIVED_AMOUNT, DIFFERENCE_AMOUNT, ADJUSTMENT_TYPE,
                                                  ADD_TIME, CREATOR, CREATOR_NAME,
                                                  UPDATER, UPDATER_NAME, MOD_TIME, IS_PUSHED)
        values (#{saleorderId,jdbcType=INTEGER}, #{saleorderNo,jdbcType=VARCHAR}, #{afterSaleId,jdbcType=INTEGER},
                #{afterSaleNo,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL},
                #{receivedAmount,jdbcType=DECIMAL}, #{differenceAmount,jdbcType=DECIMAL},
                #{adjustmentType,jdbcType=INTEGER},
                #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
                #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP},
                #{isPushed,jdbcType=BOOLEAN})
    </insert>
    <insert id="insertSelective" keyColumn="SALE_SETTLEMENT_ADJUSTMENT_ID" keyProperty="saleSettlementAdjustmentId"
            parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SALE_SETTLEMENT_ADJUSTMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="saleorderId != null">
                SALEORDER_ID,
            </if>
            <if test="saleorderNo != null and saleorderNo != ''">
                SALEORDER_NO,
            </if>
            <if test="afterSaleId != null">
                AFTER_SALE_ID,
            </if>
            <if test="afterSaleNo != null and afterSaleNo != ''">
                AFTER_SALE_NO,
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT,
            </if>
            <if test="receivedAmount != null">
                RECEIVED_AMOUNT,
            </if>
            <if test="differenceAmount != null">
                DIFFERENCE_AMOUNT,
            </if>
            <if test="adjustmentType != null">
                ADJUSTMENT_TYPE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="isPushed != null">
                IS_PUSHED,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="saleorderId != null">
                #{saleorderId,jdbcType=INTEGER},
            </if>
            <if test="saleorderNo != null and saleorderNo != ''">
                #{saleorderNo,jdbcType=VARCHAR},
            </if>
            <if test="afterSaleId != null">
                #{afterSaleId,jdbcType=INTEGER},
            </if>
            <if test="afterSaleNo != null and afterSaleNo != ''">
                #{afterSaleNo,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="receivedAmount != null">
                #{receivedAmount,jdbcType=DECIMAL},
            </if>
            <if test="differenceAmount != null">
                #{differenceAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustmentType != null">
                #{adjustmentType,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isPushed != null">
                #{isPushed,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto">
        <!--@mbg.generated-->
        update T_SALE_SETTLEMENT_ADJUSTMENT
        <set>
            <if test="saleorderId != null">
                SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
            </if>
            <if test="saleorderNo != null and saleorderNo != ''">
                SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
            </if>
            <if test="afterSaleId != null">
                AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER},
            </if>
            <if test="afterSaleNo != null and afterSaleNo != ''">
                AFTER_SALE_NO = #{afterSaleNo,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="receivedAmount != null">
                RECEIVED_AMOUNT = #{receivedAmount,jdbcType=DECIMAL},
            </if>
            <if test="differenceAmount != null">
                DIFFERENCE_AMOUNT = #{differenceAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustmentType != null">
                ADJUSTMENT_TYPE = #{adjustmentType,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isPushed != null">
                IS_PUSHED = #{isPushed,jdbcType=BOOLEAN},
            </if>
        </set>
        where SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto">
        <!--@mbg.generated-->
        update T_SALE_SETTLEMENT_ADJUSTMENT
        set SALEORDER_ID      = #{saleorderId,jdbcType=INTEGER},
            SALEORDER_NO      = #{saleorderNo,jdbcType=VARCHAR},
            AFTER_SALE_ID     = #{afterSaleId,jdbcType=INTEGER},
            AFTER_SALE_NO     = #{afterSaleNo,jdbcType=VARCHAR},
            TOTAL_AMOUNT      = #{totalAmount,jdbcType=DECIMAL},
            RECEIVED_AMOUNT   = #{receivedAmount,jdbcType=DECIMAL},
            DIFFERENCE_AMOUNT = #{differenceAmount,jdbcType=DECIMAL},
            ADJUSTMENT_TYPE   = #{adjustmentType,jdbcType=INTEGER},
            ADD_TIME          = #{addTime,jdbcType=TIMESTAMP},
            CREATOR           = #{creator,jdbcType=INTEGER},
            CREATOR_NAME      = #{creatorName,jdbcType=VARCHAR},
            UPDATER           = #{updater,jdbcType=INTEGER},
            UPDATER_NAME      = #{updaterName,jdbcType=VARCHAR},
            MOD_TIME          = #{modTime,jdbcType=TIMESTAMP},
            IS_PUSHED         = #{isPushed,jdbcType=BOOLEAN}
        where SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId,jdbcType=INTEGER}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_SALE_SETTLEMENT_ADJUSTMENT
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="SALEORDER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.saleorderId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="SALEORDER_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.saleorderNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="AFTER_SALE_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.afterSaleId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="AFTER_SALE_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.afterSaleNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="WAREHOUSE_OUT_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.warehouseOutId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="WAREHOUSE_OUT_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.warehouseOutNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="TOTAL_AMOUNT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.totalAmount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="RECEIVED_AMOUNT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.receivedAmount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="DIFFERENCE_AMOUNT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.differenceAmount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="ADJUSTMENT_TYPE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.adjustmentType,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.addTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.creator,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="CREATOR_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.creatorName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.updater,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="UPDATER_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.updaterName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.modTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                        then #{item.isPushed,jdbcType=BOOLEAN}
                </foreach>
            </trim>
        </trim>
        where SALE_SETTLEMENT_ADJUSTMENT_ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_SALE_SETTLEMENT_ADJUSTMENT
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="SALEORDER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.saleorderId != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.saleorderId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SALEORDER_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.saleorderNo != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.saleorderNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="AFTER_SALE_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.afterSaleId != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.afterSaleId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="AFTER_SALE_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.afterSaleNo != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.afterSaleNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TOTAL_AMOUNT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.totalAmount != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.totalAmount,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="RECEIVED_AMOUNT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.receivedAmount != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.receivedAmount,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="DIFFERENCE_AMOUNT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.differenceAmount != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.differenceAmount,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADJUSTMENT_TYPE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.adjustmentType != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.adjustmentType,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.addTime != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.addTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creator != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.creator,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creatorName != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.creatorName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updater != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.updater,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterName != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.updaterName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modTime != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.modTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IS_PUSHED = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isPushed != null">
                        when SALE_SETTLEMENT_ADJUSTMENT_ID = #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
                            then #{item.isPushed,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
        </trim>
        where SALE_SETTLEMENT_ADJUSTMENT_ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.saleSettlementAdjustmentId,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="SALE_SETTLEMENT_ADJUSTMENT_ID" keyProperty="saleSettlementAdjustmentId"
            parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SALE_SETTLEMENT_ADJUSTMENT
        (SALEORDER_ID, SALEORDER_NO, AFTER_SALE_ID, AFTER_SALE_NO, WAREHOUSE_OUT_ID, WAREHOUSE_OUT_NO, TOTAL_AMOUNT,
         RECEIVED_AMOUNT, DIFFERENCE_AMOUNT, ADJUSTMENT_TYPE, ADD_TIME, CREATOR, CREATOR_NAME,
         UPDATER, UPDATER_NAME, MOD_TIME, IS_PUSHED)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.saleorderId,jdbcType=INTEGER}, #{item.saleorderNo,jdbcType=VARCHAR},
             #{item.afterSaleId,jdbcType=INTEGER},
             #{item.afterSaleNo,jdbcType=VARCHAR},
             #{item.totalAmount,jdbcType=DECIMAL}, #{item.receivedAmount,jdbcType=DECIMAL},
             #{item.differenceAmount,jdbcType=DECIMAL}, #{item.adjustmentType,jdbcType=INTEGER},
             #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR},
             #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP},
             #{item.isPushed,jdbcType=BOOLEAN})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="SALE_SETTLEMENT_ADJUSTMENT_ID" keyProperty="saleSettlementAdjustmentId"
            parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SALE_SETTLEMENT_ADJUSTMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="saleSettlementAdjustmentId != null">
                SALE_SETTLEMENT_ADJUSTMENT_ID,
            </if>
            SALEORDER_ID,
            SALEORDER_NO,
            AFTER_SALE_ID,
            AFTER_SALE_NO,
            WAREHOUSE_OUT_ID,
            WAREHOUSE_OUT_NO,
            TOTAL_AMOUNT,
            RECEIVED_AMOUNT,
            DIFFERENCE_AMOUNT,
            ADJUSTMENT_TYPE,
            ADD_TIME,
            CREATOR,
            CREATOR_NAME,
            UPDATER,
            UPDATER_NAME,
            MOD_TIME,
            IS_PUSHED,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="saleSettlementAdjustmentId != null">
                #{saleSettlementAdjustmentId,jdbcType=INTEGER},
            </if>
            #{saleorderId,jdbcType=INTEGER},
            #{saleorderNo,jdbcType=VARCHAR},
            #{afterSaleId,jdbcType=INTEGER},
            #{afterSaleNo,jdbcType=VARCHAR},
            #{totalAmount,jdbcType=DECIMAL},
            #{receivedAmount,jdbcType=DECIMAL},
            #{differenceAmount,jdbcType=DECIMAL},
            #{adjustmentType,jdbcType=INTEGER},
            #{addTime,jdbcType=TIMESTAMP},
            #{creator,jdbcType=INTEGER},
            #{creatorName,jdbcType=VARCHAR},
            #{updater,jdbcType=INTEGER},
            #{updaterName,jdbcType=VARCHAR},
            #{modTime,jdbcType=TIMESTAMP},
            #{isPushed,jdbcType=BOOLEAN},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="saleSettlementAdjustmentId != null">
                SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId,jdbcType=INTEGER},
            </if>
            SALEORDER_ID      = #{saleorderId,jdbcType=INTEGER},
            SALEORDER_NO      = #{saleorderNo,jdbcType=VARCHAR},
            AFTER_SALE_ID     = #{afterSaleId,jdbcType=INTEGER},
            AFTER_SALE_NO     = #{afterSaleNo,jdbcType=VARCHAR},
            TOTAL_AMOUNT      = #{totalAmount,jdbcType=DECIMAL},
            RECEIVED_AMOUNT   = #{receivedAmount,jdbcType=DECIMAL},
            DIFFERENCE_AMOUNT = #{differenceAmount,jdbcType=DECIMAL},
            ADJUSTMENT_TYPE   = #{adjustmentType,jdbcType=INTEGER},
            ADD_TIME          = #{addTime,jdbcType=TIMESTAMP},
            CREATOR           = #{creator,jdbcType=INTEGER},
            CREATOR_NAME      = #{creatorName,jdbcType=VARCHAR},
            UPDATER           = #{updater,jdbcType=INTEGER},
            UPDATER_NAME      = #{updaterName,jdbcType=VARCHAR},
            MOD_TIME          = #{modTime,jdbcType=TIMESTAMP},
            IS_PUSHED         = #{isPushed,jdbcType=BOOLEAN},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="SALE_SETTLEMENT_ADJUSTMENT_ID"
            keyProperty="saleSettlementAdjustmentId"
            parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_SALE_SETTLEMENT_ADJUSTMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="saleSettlementAdjustmentId != null">
                SALE_SETTLEMENT_ADJUSTMENT_ID,
            </if>
            <if test="saleorderId != null">
                SALEORDER_ID,
            </if>
            <if test="saleorderNo != null and saleorderNo != ''">
                SALEORDER_NO,
            </if>
            <if test="afterSaleId != null">
                AFTER_SALE_ID,
            </if>
            <if test="afterSaleNo != null and afterSaleNo != ''">
                AFTER_SALE_NO,
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT,
            </if>
            <if test="receivedAmount != null">
                RECEIVED_AMOUNT,
            </if>
            <if test="differenceAmount != null">
                DIFFERENCE_AMOUNT,
            </if>
            <if test="adjustmentType != null">
                ADJUSTMENT_TYPE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="isPushed != null">
                IS_PUSHED,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="saleSettlementAdjustmentId != null">
                #{saleSettlementAdjustmentId,jdbcType=INTEGER},
            </if>
            <if test="saleorderId != null">
                #{saleorderId,jdbcType=INTEGER},
            </if>
            <if test="saleorderNo != null and saleorderNo != ''">
                #{saleorderNo,jdbcType=VARCHAR},
            </if>
            <if test="afterSaleId != null">
                #{afterSaleId,jdbcType=INTEGER},
            </if>
            <if test="afterSaleNo != null and afterSaleNo != ''">
                #{afterSaleNo,jdbcType=VARCHAR},
            </if>

            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="receivedAmount != null">
                #{receivedAmount,jdbcType=DECIMAL},
            </if>
            <if test="differenceAmount != null">
                #{differenceAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustmentType != null">
                #{adjustmentType,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isPushed != null">
                #{isPushed,jdbcType=BOOLEAN},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="saleSettlementAdjustmentId != null">
                SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId,jdbcType=INTEGER},
            </if>
            <if test="saleorderId != null">
                SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
            </if>
            <if test="saleorderNo != null and saleorderNo != ''">
                SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
            </if>
            <if test="afterSaleId != null">
                AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER},
            </if>
            <if test="afterSaleNo != null and afterSaleNo != ''">
                AFTER_SALE_NO = #{afterSaleNo,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="receivedAmount != null">
                RECEIVED_AMOUNT = #{receivedAmount,jdbcType=DECIMAL},
            </if>
            <if test="differenceAmount != null">
                DIFFERENCE_AMOUNT = #{differenceAmount,jdbcType=DECIMAL},
            </if>
            <if test="adjustmentType != null">
                ADJUSTMENT_TYPE = #{adjustmentType,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isPushed != null">
                IS_PUSHED = #{isPushed,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>

    <select id="findByAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_SALE_SETTLEMENT_ADJUSTMENT
        <where>
            <if test="saleSettlementAdjustmentId != null">
                AND SALE_SETTLEMENT_ADJUSTMENT_ID = #{saleSettlementAdjustmentId, jdbcType=INTEGER}
            </if>
            <if test="saleorderId != null">
                AND SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
            </if>
            <if test="saleorderNo != null">
                AND SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
            </if>
            <if test="afterSaleId != null">
                AND AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER}
            </if>
            <if test="afterSaleNo != null">
                AND AFTER_SALE_NO = #{afterSaleNo,jdbcType=VARCHAR}
            </if>
            <if test="totalAmount != null">
                AND TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL}
            </if>
            <if test="receivedAmount != null">
                AND RECEIVED_AMOUNT = #{receivedAmount,jdbcType=DECIMAL}
            </if>
            <if test="differenceAmount != null">
                AND DIFFERENCE_AMOUNT = #{differenceAmount,jdbcType=DECIMAL}
            </if>
            <if test="adjustmentType != null">
                AND ADJUSTMENT_TYPE = #{adjustmentType,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                AND ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                AND CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="creatorName != null">
                AND CREATOR_NAME = #{creatorName,jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="updaterName != null">
                AND UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
            </if>
            <if test="modTime != null">
                AND MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="beginTime != null">
                AND ADD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isPushed != null">
                AND IS_PUSHED = #{isPushed,jdbcType=BOOLEAN}
            </if>
        </where>
        order by SALE_SETTLEMENT_ADJUSTMENT_ID desc
    </select>

    <select id="selectBySaleorderIdLatest" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_SALE_SETTLEMENT_ADJUSTMENT
        WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
          AND ADJUSTMENT_TYPE = 2
        ORDER BY SALE_SETTLEMENT_ADJUSTMENT_ID DESC
        limit 1
    </select>

    <select id="findByItem" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_SALE_SETTLEMENT_ADJUSTMENT
        <where>
            <if test="saleorderId != null">
                AND SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
            </if>
            <if test="saleorderNo != null">
                AND SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
            </if>
            <if test="adjustmentType != null">
                AND ADJUSTMENT_TYPE = #{adjustmentType,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY SALE_SETTLEMENT_ADJUSTMENT_ID DESC
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-03-15-->
    <select id="findByAfterSaleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_SALE_SETTLEMENT_ADJUSTMENT
        where AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER}
        ORDER BY SALE_SETTLEMENT_ADJUSTMENT_ID desc
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-03-16-->
    <select id="findBySaleorderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_SALE_SETTLEMENT_ADJUSTMENT
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
        ORDER BY SALE_SETTLEMENT_ADJUSTMENT_ID desc
    </select>
</mapper>