<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="三级分类迁移确认" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<div class="main-container">
    <form id="tjform">
        <input type="hidden" value="${moveCategory.newThirdCategoryId}" name="newThirdCategoryId">
        <input type="hidden" value="${moveCategory.newThirdCategoryName}" name="newThirdCategoryName">
        <input type="hidden" value="${moveCategory.newFirstCategoryId}" name="newFirstCategoryId">
        <input type="hidden" value="${moveCategory.newFirstCategoryName}" name="newFirstCategoryName">
        <input type="hidden" value="${moveCategory.newSecondCategoryId}" name="newSecondCategoryId">
        <input type="hidden" value="${moveCategory.newSecondCategoryName}" name="newSecondCategoryName">
        <input type="hidden" value="${moveCategory.oldFirstCategoryId}" name="oldFirstCategoryId">
        <input type="hidden" value="${moveCategory.oldFirstCategoryName}" name="oldFirstCategoryName">
        <input type="hidden" value="${moveCategory.oldSecondCategoryId}" name="oldSecondCategoryId">
        <input type="hidden" value="${moveCategory.oldSecondCategoryName}" name="oldSecondCategoryName">
        <input type="hidden" value="${formToken}" name="formToken">
    <table
            class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="wid15">SPU名称</th>
            <th class="wid6">SPU_ID</th>
            <th class="wid6">SKU数</th>
            <th class="wid15">原始归属分类名</th>
            <th class="wid15">原始归属分类ID</th>
            <th class="wid15">目标归属分类名</th>
            <th class="wid15">目标归属分类ID</th>
        </tr>
        </thead>
        <tbody>

        <c:forEach var="moveCategoryDto" items="${moveCategoryDtoList}" varStatus="staut">
            <tr>
                <td><span class="parentAddTitle" tabTitle='{"num":"looked_${moveCategoryDto.spuId}","link":"./goods/vgoods/viewSpu.do?spuId=${moveCategoryDto.spuId}","title":"查看SPU"}'>${moveCategoryDto.spuName}</span></td>
                <td>${moveCategoryDto.spuId}</td>
                <td>${moveCategoryDto.skuCount}</td>
                <td>${moveCategoryDto.oldFirstCategoryName}/${moveCategoryDto.oldSecondCategoryName}/${moveCategoryDto.oldThirdCategoryName}</td>
                <td>${moveCategoryDto.oldFirstCategoryId}/${moveCategoryDto.oldSecondCategoryId}/${moveCategoryDto.oldThirdCategoryId}</td>
                <td>${moveCategoryDto.newFirstCategoryName}/${moveCategoryDto.newSecondCategoryName}/${moveCategoryDto.newThirdCategoryName}</td>
                <td>${moveCategoryDto.newFirstCategoryId}/${moveCategoryDto.newSecondCategoryId}/${moveCategoryDto.newThirdCategoryId}</td>
            </tr>
        </c:forEach>

        <c:if test="${empty moveCategoryDtoList}">
            <tr>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>${moveCategory.oldFirstCategoryName}/${moveCategory.oldSecondCategoryName}/${moveCategory.newThirdCategoryName}</td>
                <td>${moveCategory.oldFirstCategoryId}/${moveCategory.oldSecondCategoryId}/${moveCategory.newThirdCategoryId}</td>
                <td>${moveCategory.newFirstCategoryName}/${moveCategory.newSecondCategoryName}/${moveCategory.newThirdCategoryName}</td>
                <td>${moveCategory.newFirstCategoryId}/${moveCategory.newSecondCategoryId}/${moveCategory.newThirdCategoryId}</td>
            </tr>
        </c:if>
        </tbody>
    </table>

        <div class='form-blanks'>
            <span>迁移原因：</span>
            <input type="text" name="migrationReason" style="width: 400px">
        </div>
    </form>
    <div class="add-tijiao tcenter" style="margin-top: 15px">
        <button style="background-color: #00b7ee" onclick="qd(${moveCategory.newSecondCategoryId},${moveCategory.newThirdCategoryId})">确定</button>
        <button class="dele" id="close-layer" type="button" >取消</button>
        <%--<div class="title-click nobor  pop-new-data" id="popEngineer"></div>--%>
    </div>

</div>


<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/pages/goodsdribute/categoryConfirm.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
