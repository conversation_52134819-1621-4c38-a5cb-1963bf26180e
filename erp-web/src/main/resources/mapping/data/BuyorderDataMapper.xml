<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newtask.data.dao.BuyorderDataMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.order.model.Buyorder">
        <id column="BUYORDER_ID" property="buyorderId" jdbcType="INTEGER"/>
        <result column="BUYORDER_NO" property="buyorderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="BIT"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER"/>
        <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="INTEGER"/>
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT"/>
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT"/>
        <result column="STATUS" property="status" jdbcType="BIT"/>
        <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="BIT"/>
        <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="BIT"/>
        <result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT"/>
        <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT"/>
        <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT"/>
        <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT"/>
        <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT"/>
        <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT"/>
        <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT"/>
        <result column="SERVICE_STATUS" property="serviceStatus" jdbcType="BIT"/>
        <result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="BIT"/>
        <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT"/>
        <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER"/>
        <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER"/>
        <result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR"/>
        <result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR"/>
        <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER"/>
        <result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL"/>
        <result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL"/>
        <result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL"/>
        <result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER"/>
        <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER"/>
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER"/>
        <result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER"/>
        <result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR"/>
        <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR"/>
        <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR"/>
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
        <result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
        <result column="UPDATER" property="updater" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR"/>
        <result column="TRADER_AREA" property="traderArea" jdbcType="VARCHAR"/>
        <result column="SATISFY_DELIVERY_TIME" property="satisfyDeliveryTime" jdbcType="BIGINT"/>
        <result column="ESTIMATE_ARRIVAL_TIME" property="estimateArrivalTime" jdbcType="VARCHAR"/>
        <result column="EXPEDITING_STATUS" property="expeditingStatus" jdbcType="INTEGER"/>
        <result column="EXPEDITING_FOLLOW_STATUS" property="expeditingFollowStatus" jdbcType="INTEGER"/>
        <result column="NEW_FLOW" property="newFlow" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="insertBuyorderData">
        INSERT INTO T_BUYORDER_DATA (BUYORDER_ID,ADD_TIME,MOD_TIME) VALUES (#{orderId}, #{nowTime}, #{nowTime})
    </insert>

    <update id="updateOrderSubStatus">
        UPDATE
        T_BUYORDER
        SET
        SUB_STATUS = CASE
        <foreach collection="buyorderDataDtos" item="item" index="index">
            WHEN BUYORDER_ID = #{item.buyorderId} THEN #{item.subStatus}
        </foreach>
        END
        WHERE BUYORDER_ID IN
        <foreach collection="buyorderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.buyorderId}
        </foreach>
    </update>

    <update id="updateOrderIsContractByTime">
        UPDATE T_BUYORDER_DATA SET IS_CONTRACT_RETURN_STATUS = 1,MOD_TIME = #{nowTime}
        WHERE BUYORDER_ID IN
        <foreach collection="orderIds" item="orderId" index="index"
                 open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </update>
    <update id="updateOrderLackPeriodByTime">
        UPDATE T_BUYORDER_DATA SET LACK_PERIOD_STATUS = 1,MOD_TIME = #{nowTime}
        WHERE BUYORDER_ID IN
        <foreach collection="orderIds" item="orderId" index="index"
                 open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </update>
    <update id="updateOrderIsFinanceAlreadyByTime">
        UPDATE
        T_BUYORDER_DATA
        SET
        MOD_TIME = #{nowTime},
        IS_FINANCE_ALREADY_STATUS = CASE
        <foreach collection="buyorderDataDtos" item="item" index="index">
            WHEN BUYORDER_ID = #{item.buyorderId} THEN #{item.isFinanceAlreadyStatus}
        </foreach>
        END
        WHERE BUYORDER_ID IN
        <foreach collection="buyorderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.buyorderId}
        </foreach>
    </update>
    <update id="updateOrderIsAllLogisticsByTime">
        UPDATE
        T_BUYORDER_DATA
        SET
       MOD_TIME = #{nowTime},
        IS_ALL_LOGISTICS_STATUS = CASE
        <foreach collection="buyorderDataDtos" item="item" index="index">
            WHEN BUYORDER_ID = #{item.buyorderId} THEN #{item.isAllLogisticsStatus}
        </foreach>
        END
        WHERE BUYORDER_ID IN
        <foreach collection="buyorderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.buyorderId}
        </foreach>
    </update>
    <update id="updateOrderRecordInvoiceApplyStatusByTime">
        UPDATE
        T_BUYORDER_DATA
        SET
        MOD_TIME = #{nowTime},
        RECORD_INVOICE_APPLY_STATUS = CASE
        <foreach collection="buyorderDataDtos" item="item" index="index">
            WHEN BUYORDER_ID = #{item.buyorderId} THEN #{item.recordInvoiceApplyStatus}
        </foreach>
        END
        WHERE BUYORDER_ID IN
        <foreach collection="buyorderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.buyorderId}
        </foreach>
    </update>
    <update id="updateOrderLackAccountPeriodAmount">
        UPDATE
        T_BUYORDER_DATA
        SET
        MOD_TIME = #{nowTime},
        LACK_ACCOUNT_PERIOD_AMOUNT = CASE
        <foreach collection="buyorderDataDtos" item="item" index="index">
            WHEN BUYORDER_ID = #{item.buyorderId} THEN #{item.lackAccountPeriodAmount}
        </foreach>
        END
        WHERE BUYORDER_ID IN
        <foreach collection="buyorderDataDtos" index="index" item="item" open="(" separator="," close=")">
            #{item.buyorderId}
        </foreach>
    </update>

    <select id="getBuyorderIdByTime" resultType="java.lang.Integer">
      SELECT * FROM T_BUYORDER WHERE MOD_TIME BETWEEN #{nowTime} and #{nowTime}
    </select>

    <select id="getBuyorderByTime" resultType="com.vedeng.order.model.vo.BuyorderVo">
         SELECT * FROM T_BUYORDER WHERE MOD_TIME BETWEEN #{startTime} and #{endTime}
    </select>

    <select id="getBuyorderDataById" resultType="java.lang.Integer">
        SELECT count(1) FROM T_BUYORDER_DATA WHERE BUYORDER_ID = #{orderId}
    </select>

    <select id="getBuyorderVerifyStatusByTime" resultType="com.newtask.data.dto.BuyorderDataDto">
        SELECT
            BUYORDER_ID buyorderId,
            a.`STATUS` verifyStatus
        FROM
            T_BUYORDER bo
            INNER JOIN T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = bo.BUYORDER_ID
            AND a.RELATE_TABLE = 'T_BUYORDER'
        WHERE
            a.MOD_TIME BETWEEN #{startTime} and #{endTime}
    </select>

    <select id="getOrderIsContractByTime" resultType="java.util.Map">
        SELECT
        bo.BUYORDER_ID buyorderId,1 isContractReturn
        FROM
        T_BUYORDER bo
        WHERE  EXISTS (SELECT 1 FROM T_ATTACHMENT atta WHERE atta.RELATED_ID = bo.BUYORDER_ID and atta.ATTACHMENT_FUNCTION = 514 AND ADD_TIME BETWEEN #{startTime} and #{endTime})
    </select>

    <select id="getOrderLackPeriodByTime" resultType="java.util.Map">
        SELECT
            e.BUYORDER_ID buyorderId,
            COALESCE (
                sum( ABS( a.AMOUNT ) ) - IFNULL( c.hk_amount, 0 ) - IFNULL( d.hk_amount, 0 ),
                0
            ) AS LACK_COUNT
        FROM
            T_CAPITAL_BILL a
            LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
            LEFT JOIN (
            SELECT COALESCE
                ( sum( ABS( b1.AMOUNT ) ), 0 ) AS hk_amount,
                b1.RELATED_ID
            FROM
                T_CAPITAL_BILL a1
                LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
            WHERE
                a1.TRADER_TYPE IN ( 2, 5 )
                AND b1.ORDER_TYPE = 2
                AND b1.BUSSINESS_TYPE = 533
            GROUP BY
                b1.RELATED_ID
            ) AS c ON b.RELATED_ID = c.RELATED_ID
            LEFT JOIN (
            SELECT COALESCE
                ( sum( ABS( b1.AMOUNT ) ), 0 ) AS hk_amount,
                c1.ORDER_ID
            FROM
                T_CAPITAL_BILL a1
                LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
                LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
                AND c1.SUBJECT_TYPE = 536
            WHERE
                a1.TRADER_TYPE = 3
                AND b1.ORDER_TYPE = 3
                AND b1.BUSSINESS_TYPE = 531
                AND a1.TRADER_MODE = 529
            GROUP BY
                c1.ORDER_ID
            ) AS d ON d.ORDER_ID = b.RELATED_ID
            LEFT JOIN T_BUYORDER e ON b.RELATED_ID = e.BUYORDER_ID
        WHERE
            a.TRADER_TYPE = 3
            AND a.TRADER_MODE = 527
            AND b.ORDER_TYPE = 2
            AND e.HAVE_ACCOUNT_PERIOD = 1
            AND e.COMPANY_ID = 1
            AND a.ADD_TIME between #{startTime} and #{endTime}
        GROUP BY
            e.BUYORDER_ID
        HAVING
            LACK_COUNT = 0
    </select>

    <select id="getOrderIsFinanceAlreadyByTime" resultType="java.util.Map">
       SELECT
            bo.BUYORDER_ID buyorderId, SUBSTRING_INDEX( c.VERIFY_USERNAME, ',', 1 ) userName
        FROM
            T_BUYORDER bo
            inner JOIN T_PAY_APPLY b ON b.RELATED_ID = bo.BUYORDER_ID
            AND b.COMPANY_ID = bo.COMPANY_ID
            AND b.PAY_TYPE = 517
            inner JOIN T_VERIFIES_INFO c ON c.RELATE_TABLE_KEY = b.PAY_APPLY_ID
            AND c.RELATE_TABLE = 'T_PAY_APPLY' AND c.VERIFIES_TYPE = 644
        WHERE
            c.MOD_TIME between #{startTime} and #{endTime}
    </select>
    <select id="getFinanceUserNameList" resultType="java.lang.String">
        SELECT
            U.USERNAME
        FROM
            T_USER U
            LEFT JOIN T_R_USER_POSIT UR ON UR.USER_ID = U.USER_ID
            LEFT JOIN T_POSITION R ON R.POSITION_ID = UR.POSITION_ID
        WHERE
            U.COMPANY_ID = 1 AND R.`TYPE`  = 314
    </select>
    <select id="getOrderIsAllLogisticsByTime" resultType="java.util.Map">
        SELECT
        b.BUYORDER_ID buyorderId,
        CASE
        WHEN sum( a.NUM - a.AFTER_RETURN_NUM ) <![CDATA[ <= ]]> sum( c.NUM ) THEN
        1 ELSE 0
        END isAllLogisticsStatus
        FROM
        T_BUYORDER b
        LEFT JOIN T_BUYORDER_GOODS a ON b.BUYORDER_ID = a.BUYORDER_ID
        AND a.IS_DELETE = 0
        LEFT JOIN T_EXPRESS_DETAIL c ON a.BUYORDER_GOODS_ID = c.RELATED_ID
        INNER JOIN T_EXPRESS e ON e.EXPRESS_ID = c.EXPRESS_ID
        AND c.BUSINESS_TYPE = 515
        WHERE
        e.MOD_TIME between #{startTime} and #{endTime}
        GROUP BY
        b.BUYORDER_ID
    </select>
    <select id="getRecordInvoiceApplyOneByTime" resultType="java.util.Map">
        SELECT BUYORDER_ID buyorderId,1 recordInvoiceApplyStatus FROM T_BUYORDER WHERE
        (ARRIVAL_STATUS = 1 OR ARRIVAL_STATUS = 2 ) AND INVOICE_STATUS =0
         AND MOD_TIME between #{startTime} and #{endTime}
    </select>
    <select id="getRecordInvoiceApplyTwoByTime" resultType="java.util.Map">
        SELECT tb.BUYORDER_ID buyorderId,2 recordInvoiceApplyStatus  FROM
        T_BUYORDER tb
        INNER JOIN T_BUYORDER_GOODS tbg ON tb.BUYORDER_ID = tbg.BUYORDER_ID
        WHERE
        tbg.ARRIVAL_NUM - tbg.AFTER_RETURN_NUM > tbg.REAL_INVOICE_NUM
        AND tbg.MOD_TIME between #{startTime} and #{endTime}
        GROUP BY tbg.BUYORDER_ID
    </select>
    <select id="getRecordInvoiceApplyThreeByTime" resultType="java.util.Map">
        SELECT tb.BUYORDER_ID buyorderId,3 recordInvoiceApplyStatus FROM
        T_BUYORDER tb
        INNER JOIN T_INVOICE ti ON ti.RELATED_ID = tb.BUYORDER_ID
        AND ti.tag = 2
        AND ti.VALID_STATUS = 0
        AND ti.TYPE = 503
        AND ti.MOD_TIME between #{startTime} and #{endTime}
    </select>
    <select id="getOrderLackAccountPeriodAmountByTime" resultType="java.util.Map">
      select
	  		COALESCE(sum(ABS(a.AMOUNT))-IFNULL(c.hk_amount,0)-IFNULL(d.hk_amount,0),0)  lackAccountPeriodAmount, b.RELATED_ID orderId
	  	from
	  		T_CAPITAL_BILL a
	  	left join
			T_CAPITAL_BILL_DETAIL b
		on
			a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
	  	left join
	  		(
	  			select
	  				COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,b1.RELATED_ID
	  			from
			  		T_CAPITAL_BILL a1
			  	left join
					T_CAPITAL_BILL_DETAIL b1
				on
					a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
				where
					a1.TRADER_TYPE in(2,5)
				and
	  				b1.ORDER_TYPE = 2
				and
					b1.BUSSINESS_TYPE = 533
				group by b1.RELATED_ID
	  		) as c
	  	on
	  		b.RELATED_ID = c.RELATED_ID
	  	left join
	  		(
	  			select
					COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,c1.ORDER_ID
				from
					T_CAPITAL_BILL a1
				left join
					T_CAPITAL_BILL_DETAIL b1
				on
					a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
				left JOIN
					T_AFTER_SALES c1
				ON
					c1.AFTER_SALES_ID = b1.RELATED_ID and c1.SUBJECT_TYPE = 536
				where
					a1.TRADER_TYPE = 3
				and
					b1.ORDER_TYPE = 3
				and
					b1.BUSSINESS_TYPE = 531 AND a1.TRADER_MODE = 529
				group by c1.ORDER_ID
	  		) as d
	  	on
	  		d.ORDER_ID = b.RELATED_ID
	  	where
	  		a.TRADER_TYPE = 3
	  	and
	  		a.TRADER_MODE = 527
	  	and
	  		b.ORDER_TYPE = 2
		AND a.ADD_TIME between #{startTime} and #{endTime}
	  	GROUP BY b.RELATED_ID
    </select>
    <select id="getOrderIsContractByBuyorderId" resultType="java.lang.Integer">
        select distinct b.BUYORDER_ID
        from T_BUYORDER b join T_ATTACHMENT a on b.BUYORDER_ID = a.RELATED_ID and a.ATTACHMENT_FUNCTION = 514
        where b.BUYORDER_ID in
        <foreach collection="bizIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateContractReturnStatus">
        update T_BUYORDER_DATA set IS_CONTRACT_RETURN_STATUS  = #{status,jdbcType=INTEGER}
        where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
    </update>
</mapper>