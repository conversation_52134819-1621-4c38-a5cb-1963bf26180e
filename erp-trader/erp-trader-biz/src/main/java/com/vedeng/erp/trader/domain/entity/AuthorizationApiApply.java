package com.vedeng.erp.trader.domain.entity;

public class AuthorizationApiApply {
    //授权书申请主键
    private Integer authorizationApplyId;
    //授权书申请号
    private String authorizationApplyNum;
    //报价单id
    private Integer quoteorderId;
    //skuid
    private Integer skuId;
    //采购单位/招标公司
    private String purchaseOrBidding;
    //生产厂家
    private String productCompany;
    //经营性质1为生产2为代理3为销售
    private Integer natureOfOperation;
    //品牌名称
    private String brandName;
    //产品名称
    private String skuName;
    //产品型号
    private String skuModel;
    //经销类型1为独家经销商2为经销商3为代理商
    private Integer distributionsType;
    //授权公司
    private String authorizedCompany;
    //采购项目全程
    private String purchaseProjectName;
    //采购项目编号
    private String purchaseProjectNum;
    //文件类型，1为投标2为响应
    private Integer fileType;
    //售后公司全称
    private String aftersalesCompany;
    //授权有限期开始日期
    private String beginTime;
    //授权有效期结束日期
    private String endTime;
    //添加时间
    private Long addTime;
    //修改时间
    private Long modTime;
    //创建人
    private Integer creator;
    //修改人
    private Integer updator;
    //申请人
    private String applyPerson;
    //当前审核人
    private String reviewer;
    //描述
    private String described;
    //份数
    private Integer num;
    //审核状态
    private Integer applyStatus;
    //授权书提交年月
    private String yearAndMonth;

    private Integer standardTemplate;

    private String comment;


    private String applyYear;
    private String applyMonth;
    private String applyDay;

    public Integer getAuthorizationApplyId() {
        return authorizationApplyId;
    }

    public void setAuthorizationApplyId(Integer authorizationApplyId) {
        this.authorizationApplyId = authorizationApplyId;
    }

    public String getAuthorizationApplyNum() {
        return authorizationApplyNum;
    }

    public void setAuthorizationApplyNum(String authorizationApplyNum) {
        this.authorizationApplyNum = authorizationApplyNum;
    }

    public Integer getQuoteorderId() {
        return quoteorderId;
    }

    public void setQuoteorderId(Integer quoteorderId) {
        this.quoteorderId = quoteorderId;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getPurchaseOrBidding() {
        return purchaseOrBidding;
    }

    public void setPurchaseOrBidding(String purchaseOrBidding) {
        this.purchaseOrBidding = purchaseOrBidding;
    }

    public String getProductCompany() {
        return productCompany;
    }

    public void setProductCompany(String productCompany) {
        this.productCompany = productCompany;
    }

    public Integer getNatureOfOperation() {
        return natureOfOperation;
    }

    public void setNatureOfOperation(Integer natureOfOperation) {
        this.natureOfOperation = natureOfOperation;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getSkuModel() {
        return skuModel;
    }

    public void setSkuModel(String skuModel) {
        this.skuModel = skuModel;
    }

    public Integer getDistributionsType() {
        return distributionsType;
    }

    public void setDistributionsType(Integer distributionsType) {
        this.distributionsType = distributionsType;
    }

    public String getAuthorizedCompany() {
        return authorizedCompany;
    }

    public void setAuthorizedCompany(String authorizedCompany) {
        this.authorizedCompany = authorizedCompany;
    }

    public String getPurchaseProjectName() {
        return purchaseProjectName;
    }

    public void setPurchaseProjectName(String purchaseProjectName) {
        this.purchaseProjectName = purchaseProjectName;
    }

    public String getPurchaseProjectNum() {
        return purchaseProjectNum;
    }

    public void setPurchaseProjectNum(String purchaseProjectNum) {
        this.purchaseProjectNum = purchaseProjectNum;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public String getAftersalesCompany() {
        return aftersalesCompany;
    }

    public void setAftersalesCompany(String aftersalesCompany) {
        this.aftersalesCompany = aftersalesCompany;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getApplyPerson() {
        return applyPerson;
    }

    public void setApplyPerson(String applyPerson) {
        this.applyPerson = applyPerson;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public String getDescribed() {
        return described;
    }

    public void setDescribed(String described) {
        this.described = described;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getYearAndMonth() {
        return yearAndMonth;
    }

    public void setYearAndMonth(String yearAndMonth) {
        this.yearAndMonth = yearAndMonth;
    }

    public Integer getStandardTemplate() {
        return standardTemplate;
    }

    public void setStandardTemplate(Integer standardTemplate) {
        this.standardTemplate = standardTemplate;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getApplyYear() {
        return applyYear;
    }

    public void setApplyYear(String applyYear) {
        this.applyYear = applyYear;
    }

    public String getApplyMonth() {
        return applyMonth;
    }

    public void setApplyMonth(String applyMonth) {
        this.applyMonth = applyMonth;
    }

    public String getApplyDay() {
        return applyDay;
    }

    public void setApplyDay(String applyDay) {
        this.applyDay = applyDay;
    }

    @Override
    public String toString() {
        return "AuthorizationApply{" +
                "authorizationApplyId=" + authorizationApplyId +
                ", authorizationApplyNum='" + authorizationApplyNum + '\'' +
                ", quoteorderId=" + quoteorderId +
                ", skuId=" + skuId +
                ", purchaseOrBidding='" + purchaseOrBidding + '\'' +
                ", productCompany='" + productCompany + '\'' +
                ", natureOfOperation=" + natureOfOperation +
                ", brandName='" + brandName + '\'' +
                ", skuName='" + skuName + '\'' +
                ", skuModel='" + skuModel + '\'' +
                ", distributionsType=" + distributionsType +
                ", authorizedCompany='" + authorizedCompany + '\'' +
                ", purchaseProjectName='" + purchaseProjectName + '\'' +
                ", purchaseProjectNum='" + purchaseProjectNum + '\'' +
                ", fileType=" + fileType +
                ", aftersalesCompany='" + aftersalesCompany + '\'' +
                ", beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", addTime=" + addTime +
                ", modTime=" + modTime +
                ", creator=" + creator +
                ", updator=" + updator +
                ", applyPerson='" + applyPerson + '\'' +
                ", reviewer='" + reviewer + '\'' +
                ", described='" + described + '\'' +
                ", num=" + num +
                ", applyStatus=" + applyStatus +
                ", yearAndMonth='" + yearAndMonth + '\'' +
                ", standardTemplate=" + standardTemplate +
                ", comment='" + comment + '\'' +
                ", applyYear='" + applyYear + '\'' +
                ", applyMonth='" + applyMonth + '\'' +
                ", applyDay='" + applyDay + '\'' +
                '}';
    }
}
