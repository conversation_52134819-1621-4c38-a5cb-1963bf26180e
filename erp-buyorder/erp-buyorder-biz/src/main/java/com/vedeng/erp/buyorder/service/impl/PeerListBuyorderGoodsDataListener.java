package com.vedeng.erp.buyorder.service.impl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorder.domain.entity.PeerListBuyorderGoods;
import com.vedeng.erp.buyorder.common.utils.AbstractDoService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/16 17:26
 **/
@Slf4j
public class PeerListBuyorderGoodsDataListener implements ReadListener<PeerListBuyorderGoods> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    /**
     * 缓存的数据
     */
    private List<PeerListBuyorderGoods> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    /**
     * 继承AbstractDoService的类
     */
    private AbstractDoService<PeerListBuyorderGoods> doService;

    public PeerListBuyorderGoodsDataListener() {
    }

    public PeerListBuyorderGoodsDataListener(AbstractDoService<PeerListBuyorderGoods> doService) {
        this.doService = doService;
    }


    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(PeerListBuyorderGoods data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        if (!StringUtils.isEmpty(data.getSku())) {
            cachedDataList.add(data);
        }
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            handleData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要处理
        handleData();
        log.info("所有数据解析完成！");
    }

    /**
     * 处理
     */
    private void handleData() {
        log.info("{}条数据，开始解析！", cachedDataList.size());
        doService.doService(cachedDataList);
        log.info("解析成功！");
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        log.error("解析失败，但是继续解析下一行:{}", exception);
        // 如果是某一个单元格的转换异常 能获取到具体行号
        // 如果要获取头的信息 配合invokeHeadMap使用
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException)exception;
            log.error("第{}行，第{}列解析异常，数据为:{}", excelDataConvertException.getRowIndex(),
                    excelDataConvertException.getColumnIndex(), JSON.toJSONString(excelDataConvertException.getCellData()));
            throw new ServiceException("模板格式错误");
        }
    }
}
