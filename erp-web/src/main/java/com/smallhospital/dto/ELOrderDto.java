package com.smallhospital.dto;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.List;

public class ELOrderDto {

    private String orderId;
    private String orderNumber;
    private Integer purchaserId;
    private String remark;
    private String deliveryAddressArea;
    private String deliveryAddress;
    private String deliveryMan;
    private String deliveryPhone;
    private String invoiceAddressArea;
    private String invoiceAddress;
    private String invoiceMan;
    private String invoicePhone;

    @JsonProperty(value = "DDMX")
    private List<ELOrderItemDto> DDMX;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getPurchaserId() {
        return purchaserId;
    }

    public void setPurchaserId(Integer purchaserId) {
        this.purchaserId = purchaserId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryMan() {
        return deliveryMan;
    }

    public void setDeliveryMan(String deliveryMan) {
        this.deliveryMan = deliveryMan;
    }

    public String getDeliveryPhone() {
        return deliveryPhone;
    }

    public void setDeliveryPhone(String deliveryPhone) {
        this.deliveryPhone = deliveryPhone;
    }

    public String getDeliveryAddressArea() {
        return deliveryAddressArea;
    }

    public void setDeliveryAddressArea(String deliveryAddressArea) {
        this.deliveryAddressArea = deliveryAddressArea;
    }

    public String getInvoiceAddressArea() {
        return invoiceAddressArea;
    }

    public void setInvoiceAddressArea(String invoiceAddressArea) {
        this.invoiceAddressArea = invoiceAddressArea;
    }

    public String getInvoiceAddress() {
        return invoiceAddress;
    }

    public void setInvoiceAddress(String invoiceAddress) {
        this.invoiceAddress = invoiceAddress;
    }

    public String getInvoiceMan() {
        return invoiceMan;
    }

    public void setInvoiceMan(String invoiceMan) {
        this.invoiceMan = invoiceMan;
    }

    public String getInvoicePhone() {
        return invoicePhone;
    }

    public void setInvoicePhone(String invoicePhone) {
        this.invoicePhone = invoicePhone;
    }

    public List<ELOrderItemDto> getDDMX() {
        return DDMX;
    }

    public void setDDMX(List<ELOrderItemDto> DDMX) {
        this.DDMX = DDMX;
    }
}
