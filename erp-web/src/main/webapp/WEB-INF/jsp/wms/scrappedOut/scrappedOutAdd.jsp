<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增报废出库单" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/wms/scrappedOut/scrappedOutAdd.js?rnd=${resourceVersionKey}"></script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/wms/scrapOut/saveScrappedOutOrder.do">
            <input type="hidden" name="formToken" value="${formToken}" />
            <ul class="payplan">
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>报废品分类:</label>
                    </div>
                    <div class="f_left">
                        <select class="input-xx" id="scrapType" name="scrapType">
                            <option value=""  disabled selected hidden>请选择报废品分类</option>
                            <c:forEach var="il" items="${scrapTypeList}">
                                <option value="${il.sysOptionDefinitionId}">${il.title}</option>
                            </c:forEach>
                        </select>
                   </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>报废品级别:</label>
                    </div>
                    <div class="f_left">
                        <select class="input-xx" id="scrapLevel" name="scrapLevel">
                            <option value="" disabled selected hidden>请选择报废品级别</option>
                            <c:forEach var="il" items="${scrapLevelList}">
                                <option value="${il.sysOptionDefinitionId}">${il.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
<%--                已去除--%>
<%--                <li>--%>
<%--                    <div class="infor_name">--%>
<%--                        <span class="font-red">*</span><label>报废处理方式:</label>--%>
<%--                    </div>--%>
<%--                    <div class="f_left">--%>
<%--                        <select class="input-xx" id="scrapDealType" name="scrapDealType" onchange="changespan()">--%>
<%--                            <option value="" disabled selected hidden>请选择报废品处理方式</option>--%>
<%--                            <c:forEach var="il" items="${sysOptionDefinition}">--%>
<%--                                <option value="${il.sysOptionDefinitionId}">${il.title}</option>--%>
<%--                            </c:forEach>--%>
<%--                        </select>--%>
<%--                    </div>--%>
<%--                </li>--%>
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label id="orgtext">申请部门:</label>
                    </div>
                    <div class="f_left">
                        <select class="input-middle f_left" id="orgId" name="orgId" onchange="initUserName();">
                            <option value="">请选择申请部门</option>
                            <c:forEach items="${orgList}" var="org">
                            <option value="${org.orgId }"
                                    <c:if test="${user.orgId != null and user.orgId == org.orgId}">selected</c:if>>
                                    ${org.orgName }
                            </option>
                            </c:forEach>
                        </select>
                    </div>
                </li>

                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label id="usertext">申请人:</label>
                    </div>
                    <div class="f_left">
                        <select class="input-middle f_left" id="userId" name="userId" >
                            <option value="">请选择申请人</option>
                            <c:forEach items="${getUserListByOrgId}" var="singleUser">
                                <option value="${singleUser.userId }"
                                        <c:if test="${singleUser.userId != null and user.userId == singleUser.userId}">selected</c:if>>
                                        ${singleUser.username}
                                </option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <li>
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>出库产品:</label>
                    </div>
                    <div class="f_left">
                        <span class="bt-small bg-light-blue bt-bg-style pop-new-data"
                              layerParams='{"width":"800px","height":"480px","title":"选择出库产品","link":"<%=path%>/wms/scrapOut/logicalSearchGoods.do?callbackFuntion=setGoodsData&logicalId=1710"}'>
						      请选择产品
					</span>
                    </div>
                <div id="goodsError"></div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">已选出库产品</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered" >
                            <thead>
                            <tr>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">单位</th>
                                <th style="width:80px">库存量</th>
                                <th style="width:120px">出库数量</th>
                                <th style="width:80px">操作</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            </tbody>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>申请出库日期:</label>
                    </div>
                    <div class="f_left">
                        <input type="text"
                               onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'${today}'})" autocomplete="off"
                               name="appleOutDate" id="appleOutDate" value="${today}">
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <label>备注:</label>
                    </div>
                    <div class="f_left">
                        <%--<input type="text" style="width: 470px;height:100px" required
                               minlength="" maxlength="20" size="10" class="input-middle" name="remark" id="remark" value=""/>--%>
                        <textarea style="width: 470px;height:100px" name="remark" id="remark" maxlength="200" value=""></textarea>
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter mt10">
                <button type="submit" class="bt-bg-style bg-deep-green" name="submit">申请出库</button>
                <input type="button" value="取消" class="dele J-close-tab" onclick="javascript:window.location.href='${ezScrappedIndexUrl}'"/>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/user/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>