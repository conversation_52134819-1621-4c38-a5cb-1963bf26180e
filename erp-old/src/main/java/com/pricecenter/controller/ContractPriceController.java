package com.pricecenter.controller;


import com.pricecenter.dto.*;
import com.pricecenter.service.ContractPriceService;
import com.pricecenter.service.validator.*;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.system.service.FtpUtilService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "/price/contract")
public class ContractPriceController extends BaseController{

    //定义日志
    private static final Logger LOGGER = LoggerFactory.getLogger(ContractPriceController.class);

    @Autowired
    @Qualifier("ftpUtilService")
    private FtpUtilService ftpUtilService;

    @Autowired
    private ContractPriceService contractPriceService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private CustomerNameValidatorForAddContract customerNameValidatorForAddContract;

    @Autowired
    private CustomerNameValidatorForEditContract customerNameValidatorForEditContract;

    @Autowired
    private SkuNoValidatorForAddContract skuNoValidatorForAddContract;

    @Autowired
    private PriceValidatorForContractAdd priceValidatorForAddContract;

    @Autowired
    private PriceValidatorForContractEdit priceValidatorForEditContract;


    /**
     * 合约商品详情页
     * @Rock
     * @param
     * @return
     */
    @RequestMapping(value = "index")
    public ModelAndView contractIndex(HttpServletRequest request, ContractPriceListPageQueryDto queryDto,
                                      @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                      @RequestParam(required = false) Integer pageSize){

      //  LOGGER.info("价格合约商品列表页");
        queryDto.setPageSize(pageSize);
        queryDto.setPageNo(pageNo);

        PageResultDto<ContractPrice> pageResultVo = this.contractPriceService.findByPage(queryDto);

        List<ContractPrice> contractPriceList=pageResultVo.getDatas();
        List<ContractPriceDto> contractPriceDtos=new ArrayList<>();

        if (CollectionUtils.isNotEmpty(contractPriceList)) {
            for (ContractPrice contractPrice : contractPriceList) {
                String unitName = goodsService.getUnitNameBySkuNo(contractPrice.getSkuNo());
                ContractPriceDto contractPriceDto = new ContractPriceDto();
                BeanUtils.copyProperties(contractPrice, contractPriceDto);
                contractPriceDto.setUnitName(unitName);

                contractPriceDto.setShowPic(contractPriceDto.getPurchasePrice() != null
                        && contractPriceDto.getContractPrice() != null
                        && contractPriceDto.getPurchasePrice().compareTo(contractPriceDto.getContractPrice()) > 0) ;

                contractPriceDtos.add(contractPriceDto);
            }
        }


        Page page = super.getPageTag(request,pageNo,pageSize);
        page.setTotalRecord(pageResultVo.getTotalRecords());

        ModelAndView mv=new ModelAndView("/price/priceContract/index");

        mv.addObject("list",contractPriceDtos);
        mv.addObject("page",page);
        mv.addObject("priceContractVo",queryDto);

       // LOGGER.info("价格合约商品列表页查询成功");
        return mv;
    }

    /**
     * @Rock
     * 批量维护弹窗
     */
    @ResponseBody
    @RequestMapping(value = "/uplodeBatchPriceContract")
    public ModelAndView batchUplodeBatchPrice(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("price/priceContract/batchPriceContract");
        return mv;
    }

    /**
     * 批量新增协议价
     */
    @ResponseBody
    @RequestMapping(value = "/toBatchAddContractPrice")
    public ModelAndView toBatchAddContractPrice(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("price/priceContract/batchAddContractPrice");
        return mv;
    }

    /**
     * 批量新增协议价
     * @param request
     * @param lwfile
     * @return
     */
    @ResponseBody
    @RequestMapping("/batchAddContractPrice")
    @SystemControllerLog(operationType = "import", desc = "批量新增协议价")
    public ResultInfo<?> batchAddContractPrice(HttpServletRequest request,
                                              @RequestParam("lwfile") MultipartFile lwfile){

        ResultInfo<?> resultInfo = new ResultInfo<>();
        try {

            //处理文件
            List<BatchAddContractPriceDto> batchAddContractPriceDtoList = dealwithAddFile(request,lwfile);

            //批量上传
            contractPriceService.batchAddContractPriceDtoList(batchAddContractPriceDtoList);

            resultInfo.setCode(0);
            resultInfo.setMessage("批量导入成功");

        } catch (Exception e) {
            logger.error("batchAddContractPrice:", e);
            resultInfo.setMessage(e.getMessage());
        }
        return resultInfo;

    }

    /**
     * 处理文件
     * @param request
     * @return
     * @throws Exception
     */
    private List<BatchAddContractPriceDto> dealwithAddFile(HttpServletRequest request,MultipartFile lwfile) throws Exception {


        List<Row> rows = getRows(request,lwfile);

        if(CollectionUtils.isEmpty(rows)){
            throw new Exception("上传文件不能为空");
        }

        PriceInfoUploadValidatorDto priceInfoUploadValidatorDto = new PriceInfoUploadValidatorDto();
        priceInfoUploadValidatorDto.setUser((User) request.getSession().getAttribute(ErpConst.CURR_USER));
        priceInfoUploadValidatorDto.setRows(rows);

        ValidatorChain validatorChain = ValidatorChainBuild.newBuild()
                .setValidator(customerNameValidatorForAddContract)
                .setValidator(skuNoValidatorForAddContract)
                .setValidator(priceValidatorForAddContract)
                .create();

        ValidatorResult validatorResult = validatorChain.validator(priceInfoUploadValidatorDto);

        //检验不通过返回结果
        if(!validatorResult.getResult()){
            throw new Exception(validatorResult.getMessage());
        }

        List<BatchAddContractPriceDto> batchAddContractPriceDtoList = processAddRow(rows,priceInfoUploadValidatorDto);

        List<BatchContractPriceValidatorDto> validatorDtoList = new ArrayList<>();
        BatchContractPriceValidatorDto validatorDto = null;

        //请求价格中心 校验skuNo和customerId是否存在
        int row = 1;
        for (BatchAddContractPriceDto batchAddContractPriceDto : batchAddContractPriceDtoList) {
            row++;
            validatorDto = new BatchContractPriceValidatorDto();
            validatorDto.setCustomerId(batchAddContractPriceDto.getCustomerId());
            validatorDto.setSkuNo(batchAddContractPriceDto.getSkuNo());
            validatorDto.setIndex(row);
            validatorDtoList.add(validatorDto);
        }

        List<BatchContractPriceValidatorDto> existContractList = contractPriceService
                .validatorContractExsit(validatorDtoList);

        if(CollectionUtils.isNotEmpty(existContractList)){
            StringBuffer sb = new StringBuffer();
            existContractList.forEach(contract->{
                sb.append("第").append(contract.getIndex()).append("行协议价内容已存在，不可重复新增<br>");
            });
            throw new Exception(sb.toString());
        }


        //做个去重的操作 有可能skuId和customerId有重复
        Map<String,BatchAddContractPriceDto> filterMap = batchAddContractPriceDtoList.stream().collect(Collectors.toMap(k-> (k.getCustomerId()+ k.getSkuNo()), v -> v,(k,v) -> v));

        return new ArrayList<>(filterMap.values());
    }

    private List<BatchUpdateContractPriceDto> processEditRow(List<Row> rows,PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {


        List<BatchUpdateContractPriceDto> batchUpdateContractPriceDtoList = new ArrayList<>();

        Map<String,Long> traderNameAndIdMap = priceInfoUploadValidatorDto.getTraderNameAndIdMap();

        rows.stream().forEach(row -> {

            BatchUpdateContractPriceDto updateContractPriceDto = new BatchUpdateContractPriceDto();
            batchUpdateContractPriceDtoList.add(updateContractPriceDto);

            updateContractPriceDto.setCustomerId(traderNameAndIdMap.get(row.getCell(0).getStringCellValue()));
            updateContractPriceDto.setCustomerName(row.getCell(0).getStringCellValue());
            updateContractPriceDto.setSkuNo(row.getCell(1).getStringCellValue());

            updateContractPriceDto.setContractPrice(new BigDecimal(row.getCell(2).getStringCellValue()));

        });

        return batchUpdateContractPriceDtoList;
    }

    private List<BatchAddContractPriceDto> processAddRow(List<Row> rows,PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {


        List<BatchAddContractPriceDto> batchAddContractPriceDtoList = new ArrayList<>();

        Map<String,Long> traderNameAndIdMap = priceInfoUploadValidatorDto.getTraderNameAndIdMap();

        Map<String,Integer> traderNameAndPlatFormMap = priceInfoUploadValidatorDto.getTraderNameAndPlatFormMap();

        Map<String,String> skuNoAndSkuNameMap = priceInfoUploadValidatorDto.getSkuNoAndSkuNameMap();

        rows.forEach(row -> {

            BatchAddContractPriceDto batchAddContractPriceDto = new BatchAddContractPriceDto();
            batchAddContractPriceDtoList.add(batchAddContractPriceDto);

            batchAddContractPriceDto.setCustomerId(traderNameAndIdMap.get(row.getCell(0).getStringCellValue()));
            batchAddContractPriceDto.setCustomerName(row.getCell(0).getStringCellValue());
            batchAddContractPriceDto.setBelongPlatform(traderNameAndPlatFormMap.get(row.getCell(0).getStringCellValue()));

            batchAddContractPriceDto.setSkuNo(row.getCell(1).getStringCellValue());
            batchAddContractPriceDto.setSkuName(skuNoAndSkuNameMap.get(row.getCell(1).getStringCellValue()));

            Cell categoryCell = row.getCell(2);

            if(categoryCell != null && categoryCell.getCellType() !=  CellType.BLANK){

                if(row.getCell(2).getCellType() == CellType.NUMERIC ){
                    batchAddContractPriceDto.setContractCategory(categoryCell.getNumericCellValue() + "");
                }

                if(row.getCell(2).getCellType() == CellType.STRING){
                    batchAddContractPriceDto.setContractCategory(categoryCell.getStringCellValue());
                }
            }

            batchAddContractPriceDto.setContractPrice(new BigDecimal(row.getCell(3).getStringCellValue()));
        });

        return batchAddContractPriceDtoList;
    }

    /**
     * 解析行
     * @param request
     * @param lwfile
     * @return
     * @throws Exception
     */
    private List<Row> getRows(HttpServletRequest request,MultipartFile lwfile) throws Exception{
        // 临时文件存放地址
        String path = request.getSession().getServletContext().getRealPath("/upload/saleorder");
        FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        if(fileInfo.getCode() != 0){
            throw new Exception("文件上传失败");
        }
        // 获取excel路径
        Workbook workbook =null;
        List<Row> rows = new ArrayList<>();
        FileInputStream inputStream= new FileInputStream(new File(fileInfo.getFilePath()));
        try {
            workbook = WorkbookFactory.create(inputStream);
            // 获取第一面sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 起始行
            int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
            int endRowNum = sheet.getLastRowNum();// 结束行
            for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                rows.add(sheet.getRow(rowNum));
            }
        }catch (Exception e){
            logger.error("",e);
        }
        finally {
            try{
                inputStream.close();
            }catch (Exception e2){}
            try{
                workbook.close();
            }catch (Exception e2){}

        }
        return rows;
    }

    /**
     * 批量更新合约价
     * @Rock
     * @param request
     * @param lwfile
     * @return
     */
    @ResponseBody
    @RequestMapping("saveUplodeBatchPriceContract")
    @SystemControllerLog(operationType = "import", desc = "导入批量维护数据")
    public ResultInfo<?> saveUplodeBatchPrice(HttpServletRequest request,
                                              @RequestParam("lwfile") MultipartFile lwfile){
        ResultInfo<?> resultInfo = new ResultInfo<>();

        try {

            //处理文件
            List<BatchUpdateContractPriceDto> batchUpdateContractPriceDtoList = dealwithUpdateFile(request,lwfile);

            //批量上传
            contractPriceService.batchUpdateContract(batchUpdateContractPriceDtoList);

            resultInfo.setCode(0);
            resultInfo.setMessage("批量导入成功");

        } catch (Exception e) {
           logger.error("batchAddContractPrice  :", e);
            resultInfo.setMessage(e.getMessage());
        }
        return resultInfo;
    }

    /**
     * 处理文件
     * @param request
     * @return
     * @throws Exception
     */
    private List<BatchUpdateContractPriceDto> dealwithUpdateFile(HttpServletRequest request,MultipartFile lwfile) throws Exception {


        List<Row> rows = getRows(request,lwfile);

        if(CollectionUtils.isEmpty(rows)){
            throw new Exception("上传文件不能为空");
        }

        PriceInfoUploadValidatorDto priceInfoUploadValidatorDto = new PriceInfoUploadValidatorDto();
        priceInfoUploadValidatorDto.setUser((User) request.getSession().getAttribute(ErpConst.CURR_USER));
        priceInfoUploadValidatorDto.setRows(rows);

        ValidatorChain validatorChain = ValidatorChainBuild.newBuild()
                .setValidator(customerNameValidatorForEditContract)
                .setValidator(skuNoValidatorForAddContract)
                .setValidator(priceValidatorForEditContract)
                .create();

        ValidatorResult validatorResult = validatorChain.validator(priceInfoUploadValidatorDto);

        //检验不通过返回结果
        if(!validatorResult.getResult()){
            throw new Exception(validatorResult.getMessage());
        }

        List<BatchUpdateContractPriceDto> batchUpdateContractPriceDtoList = processEditRow(rows,priceInfoUploadValidatorDto);

        List<BatchContractPriceValidatorDto> validatorDtoList = new ArrayList<>();
        BatchContractPriceValidatorDto validatorDto = null;

        //请求价格中心 校验skuNo和customerId是否存在
        int row = 1;
        for (BatchUpdateContractPriceDto batchUpdateContractPriceDto : batchUpdateContractPriceDtoList) {
            row++;
            validatorDto = new BatchContractPriceValidatorDto();
            validatorDto.setCustomerId(batchUpdateContractPriceDto.getCustomerId());
            validatorDto.setSkuNo(batchUpdateContractPriceDto.getSkuNo());
            validatorDto.setIndex(row);
            validatorDtoList.add(validatorDto);
        }

        List<BatchContractPriceValidatorDto> notExistContractList = contractPriceService
                .validatorContractNotExsit(validatorDtoList);

        if(CollectionUtils.isNotEmpty(notExistContractList)){
            StringBuffer sb = new StringBuffer();
            notExistContractList.forEach(contract-> sb.append("第").append(contract.getIndex()).append("行协议价不存在，无法提交<br>"));
            throw new Exception(sb.toString());
        }

        return batchUpdateContractPriceDtoList;
    }

    /**
     * 编辑商品价格跳转页
     */
    @RequestMapping(value = "editPriceContract")
    public ModelAndView editPrice(HttpServletRequest request,BatchUpdateContractPriceDto batchUpdateContractPriceDto){
        ModelAndView mv=new ModelAndView("price/priceContract/editContractPrice");
        mv.addObject("contractPriceId",batchUpdateContractPriceDto.getId());
        mv.addObject("contractPrice",batchUpdateContractPriceDto.getContractPrice());
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "saveEditContractPrice")
    public ResultInfo<?> saveEditPrice(HttpServletRequest request,BatchUpdateContractPriceDto batchUpdateContractPriceDto){

        ResultInfo resultInfo=new ResultInfo();
        if (batchUpdateContractPriceDto.getContractPrice()==null){
            resultInfo.setMessage("合约价不得为空");
            return resultInfo;
        }
        if (batchUpdateContractPriceDto.getContractPrice().compareTo(new BigDecimal(0))==0){
            resultInfo.setMessage("合约价不得为0");
            return resultInfo;
        }
        if (batchUpdateContractPriceDto.getContractPrice().compareTo(new BigDecimal(300000000))>0){
            resultInfo.setMessage("合约价不得大于3亿");
            return resultInfo;
        }
        Boolean isflag=this.contractPriceService.updateContractPrice(batchUpdateContractPriceDto);
        if (!isflag){
            resultInfo.setMessage("调用价格中心服务异常");
            return resultInfo;
        }

        resultInfo.setCode(0);
        return resultInfo;
    }

    @ResponseBody
    @RequestMapping(value = "deleteContractPrice")
    public ResultInfo<?> deleteContractPrice(HttpServletRequest request,DeleteContractPriceDto deleteContractPriceDto){

        ResultInfo resultInfo=new ResultInfo();

        Boolean deteleResult =this.contractPriceService.deleteContractPrice(deleteContractPriceDto);

        if (!deteleResult){
            resultInfo.setMessage("删除合约价失败");
            return resultInfo;
        }

        resultInfo.setCode(0);
        return resultInfo;
    }

}
