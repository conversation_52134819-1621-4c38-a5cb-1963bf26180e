package com.vedeng.erp.kingdee.batch.read;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.kingdee.batch.common.reader.BaseItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.batch.dto.BatchKingDeeAliReceiptDto;
import lombok.extern.slf4j.Slf4j;


import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付宝回单附件
 * @date 2023/2/27 17:20
 */
@Slf4j
public class AlipayReceiptPushKingdeeFormRead  extends BaseItemReader<BatchAttachmentDto> {
    private Integer index = 0;


    @Override
    public BatchAttachmentDto read() throws Exception {
        List<BatchKingDeeAliReceiptDto> batchKingDeeAliReceiptDtos = (List<BatchKingDeeAliReceiptDto>) getStepParameter("aliReceiptData");
        String formId = (String) getStepParameter("formId");

        if (CollUtil.isEmpty(batchKingDeeAliReceiptDtos)) {
            return null;
        }
        if (index >= batchKingDeeAliReceiptDtos.size()) {
            index = 0;
            return null;
        }
        if (StrUtil.isEmpty(formId)) {
            log.error("formId不可为空");
            throw new ServiceException("formId不可为空");
        }
        BatchKingDeeAliReceiptDto batchKingDeeAliReceiptDto = batchKingDeeAliReceiptDtos.get(index++);
        if (Objects.isNull(batchKingDeeAliReceiptDto.getReceiptUrl())) {
            return BatchAttachmentDto.builder().build();
        }
        BatchAttachmentDto batchAttachmentDto = new BatchAttachmentDto();
        batchAttachmentDto.setFId(batchKingDeeAliReceiptDto.getFId().toString());
        batchAttachmentDto.setUri(batchKingDeeAliReceiptDto.getReceiptUrl());
        batchAttachmentDto.setFormId(formId);
        batchAttachmentDto.setName(batchKingDeeAliReceiptDto.getTranFlow());
        return batchAttachmentDto;
    }
}
