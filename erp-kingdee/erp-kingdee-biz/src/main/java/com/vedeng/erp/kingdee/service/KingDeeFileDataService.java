package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.service
 * @Date 2023/6/13 13:58
 */
public interface KingDeeFileDataService extends KingDeeBaseService<KingDeeFileDataDto> {

    /**
     * 根据formId、erpId和附件的uri查询该附件是否已推送
     *
     * @param query 查询入参
     * @return List<KingDeeFileDataDto>
     */
    List<KingDeeFileDataDto> getByBusinessIdAndUri(KingDeeFileDataDto query);
}
