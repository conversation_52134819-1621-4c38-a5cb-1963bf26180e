package com.vedeng.trader.group;

import com.vedeng.common.constant.TraderConstants;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.crm.api.dto.customergroup.TraderBehaviorAttributeDto;
import com.vedeng.trader.group.buy.BuyBehavior;
import com.vedeng.trader.group.buy.BuyGoodsBehavior;
import com.vedeng.trader.group.buy.NoBuyBehavior;
import com.vedeng.trader.group.buy.UnBuyGoodsBehavior;
import com.vedeng.trader.group.cart.AddCartBehavior;
import com.vedeng.trader.group.cart.AddGood2CartBehvior;
import com.vedeng.trader.group.cart.NoAddCartBehavior;
import com.vedeng.trader.group.deal.DealAmountBehavior;
import com.vedeng.trader.group.deal.DealRecentlyBehavior;
import com.vedeng.trader.group.deal.DealTimesBehavior;
import com.vedeng.trader.group.enquiry.EnquiryBehavior;
import com.vedeng.trader.group.enquiry.EnquiryGoodsBehavior;
import com.vedeng.trader.group.enquiry.NoEnquiryBehavior;
import com.vedeng.trader.group.order.NoOrderBehavior;
import com.vedeng.trader.group.order.OrderBehavior;
import com.vedeng.trader.group.order.OrderGoodsBehavior;
import com.vedeng.trader.group.search.SearchKeyBehavior;
import com.vedeng.trader.group.visit.NoVisitBehavior;
import com.vedeng.trader.group.visit.VisitBehavior;
import com.vedeng.trader.group.visit.VisitGoodsBehavior;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class BehaviorFactory {

    private Logger logger= LoggerFactory.getLogger(BehaviorFactory.class);
    public BaseBehavior getBehavior(TraderBehaviorAttributeDto attributeDto){
        if(attributeDto==null
                || StringUtil.isEmpty(attributeDto.getTraderBehaviorAttributeName())
                ||StringUtil.isEmpty(attributeDto.getTraderBehaviorAttributeContent())){
            return null;
        }
        String name=attributeDto.getTraderBehaviorAttributeName();
        String content=attributeDto.getTraderBehaviorAttributeContent();
        try {
            if (TraderConstants.BEHAVIOR_VISIT.equals(name)){
                //最近有访问
                return JsonUtils.readValue(content, VisitBehavior.class);
            }else if(TraderConstants.BEHAVIOR_NO_VISIT.equals(name)){
                //最近无访问
                return JsonUtils.readValue(content, NoVisitBehavior.class);
            }else if(TraderConstants.BEHAVIOR_ENQUIRY.equals(name)){
                //最近有询价
                return JsonUtils.readValue(content, EnquiryBehavior.class);
            }else if(TraderConstants.BEHAVIOR_NO_ENQUIRY.equals(name)){
                //最近无询价
                return JsonUtils.readValue(content, NoEnquiryBehavior.class);
            }else if(TraderConstants.BEHAVIOR_ADDCART.equals(name)){
                //最近有加购
                return JsonUtils.readValue(content, AddCartBehavior.class);
            }else if(TraderConstants.BEHAVIOR_NO_ADDCART.equals(name)){
                //最近无加购
                return JsonUtils.readValue(content, NoAddCartBehavior.class);
            }else if(TraderConstants.BEHAVIOR_ORDER.equals(name)){
                //最近有下单
                return JsonUtils.readValue(content, OrderBehavior.class);
            }else if(TraderConstants.BEHAVIOR_NO_ORDER.equals(name)){
                //最近无下单
                return JsonUtils.readValue(content, NoOrderBehavior.class);
            }else if(TraderConstants.BEHAVIOR_BUY.equals(name)){
                //最近有购买
                return JsonUtils.readValue(content, BuyBehavior.class);
            }else if(TraderConstants.BEHAVIOR_NO_BUY.equals(name)){
                //最近无购买
                return JsonUtils.readValue(content, NoBuyBehavior.class);
            }else if(TraderConstants.BEHAVIOR_VISIT_GOODS.equals(name)){
                //访问商品
                return JsonUtils.readValue(content, VisitGoodsBehavior.class);
            }else if(TraderConstants.BEHAVIOR_ENQUIRY_GOODS.equals(name)){
                //询价商品
                return JsonUtils.readValue(content, EnquiryGoodsBehavior.class);
            }else if(TraderConstants.BEHAVIOR_ADDCART_GOODS.equals(name)){
                //加购商品
                return JsonUtils.readValue(content, AddGood2CartBehvior.class);
            }else if(TraderConstants.BEHAVIOR_ORDER_GOODS.equals(name)){
                //下单商品
                return JsonUtils.readValue(content, OrderGoodsBehavior.class);
            }else if(TraderConstants.BEHAVIOR_SEARCH_KEY.equals(name)){
                //搜索关键词
                return JsonUtils.readValue(content, SearchKeyBehavior.class);
            }else if(TraderConstants.BEHAVIOR_BUY_GOODS.equals(name)){
                //购买商品
                return JsonUtils.readValue(content, BuyGoodsBehavior.class);
            }else if(TraderConstants.BEHAVIOR_NOBUY_GOODS.equals(name)){
                //未购买商品
                return JsonUtils.readValue(content, UnBuyGoodsBehavior.class);
            }else if(TraderConstants.BEHAVIOR_DEAL_AMOUNT.equals(name)){
                //成交金额
                return JsonUtils.readValue(content, DealAmountBehavior.class);
            }else if(TraderConstants.BEHAVIOR_DEAL_TIMES.equals(name)){
                //成交次数
                return JsonUtils.readValue(content, DealTimesBehavior.class);
            }else if(TraderConstants.BEHAVIOR_DEAL_RECENT.equals(name)){
                //最近成交
                return JsonUtils.readValue(content, DealRecentlyBehavior.class);
            }
        }catch (Exception ex){
            logger.error("获取客户行为失败：",ex);
        }
        return null;
    }
}
