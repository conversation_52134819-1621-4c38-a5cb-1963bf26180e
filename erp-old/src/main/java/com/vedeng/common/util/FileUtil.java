package com.vedeng.common.util;

import com.ctrip.framework.apollo.ConfigService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Random;

/**
 * 读存文件工具类
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/1/15 10:23.
 * @author: Randy.Xu.
 */
@Slf4j
public class FileUtil {

    private static  String ossHttp;

    static{
        ossHttp = ConfigService.getAppConfig().getProperty("oss_http", "https://");
    }

//    public static void main(String[] args) throws IOException {
//        CloseableHttpResponse response=null;
//        HttpGet httpPost = new HttpGet("https://file1.vedeng.com/upload/ajax/2019-06/21/image/1561085615000_9498.jpg");
//        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(60000).setSocketTimeout(60000).build();
//        httpPost.setConfig(requestConfig);
//        List<NameValuePair> params = new ArrayList<>();
//       // httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
//
//        response = httpclient.execute(httpPost);
//        // log.info("响应结果：{}",response);
//        String contentType = response.getEntity().getContentType().getValue();
//        String fileType = StringUtils.isNotEmpty(contentType) ? contentType.substring(contentType.lastIndexOf("/") + 1) : "txt";
//    }
    @Autowired
    RestTemplate restTemplate;
    private static CloseableHttpClient httpclient = HttpClients.createDefault();
    private static RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(60000).setSocketTimeout(60000).build();
    public static FileInfo getFile(String localPath, String fileUrl) throws Exception{
        FileInfo fileInfo = new FileInfo();
        File targetFile;
        InputStream inputStream = null;
        CloseableHttpResponse response=null;
        FileOutputStream outputStream =null;
        try {

            // http post 请求
            if(fileUrl.indexOf("file1.vedeng.com")!=-1){
                //file1 应该直接返回的，不应该再重新存一份，由于引用的地方较多，此处暂不做优化
                HttpGet httpPost = new HttpGet(fileUrl);
                httpPost.setConfig(requestConfig);
//                List<NameValuePair> params = new ArrayList<>();
//                httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
                response = httpclient.execute(httpPost);
            }else{
                HttpPost httpPost = new HttpPost(fileUrl);
                httpPost.setConfig(requestConfig);
                List<NameValuePair> params = new ArrayList<>();
                httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));
                response = httpclient.execute(httpPost);
            }

            // log.info("响应结果：{}",response);
            String contentType = response.getEntity().getContentType().getValue();
            String fileType = StringUtils.isNotEmpty(contentType) ? contentType.substring(contentType.lastIndexOf("/") + 1) : "txt";

            inputStream = response.getEntity().getContent();
            byte[] fileByte = getBytesFromInputStream(inputStream);
            String md5File=DigestUtils.md5DigestAsHex(fileByte);

            if (StringUtil.isNotEmpty(fileUrl) && fileUrl.indexOf("resourceId") == -1) {
                fileInfo.setMd5(md5File);
            }
            // 时间戳
            String time = DateUtil.gainNowDate() + "";
            Random random = new Random();
            time = time + "_" + String.format("%04d", random.nextInt(10000));// 随机数4位，不足4位，补位
            String new_fileName = time + "." + fileType;// 新文件名称

            Calendar calendar = Calendar.getInstance();
            // 获取年份
            int year = calendar.get(Calendar.YEAR);
            // 获取月份
            int month = calendar.get(Calendar.MONTH) + 1;
            // 拼接年份和月份
            String date = year + "-" + String.format("%02d", month);

            String day = String.format("%02d", calendar.get(Calendar.DAY_OF_MONTH));

            String save_path = localPath + "/" + date + "/" + day + "/file";
            targetFile = new File(save_path);
            if (!targetFile.isDirectory()) {
                targetFile.mkdirs();
            }
            targetFile = new File(save_path + File.separatorChar + new_fileName);

            fileInfo.setMd5(md5File);
            fileInfo.setFile(targetFile);
            outputStream = new FileOutputStream(targetFile);
            outputStream.write(fileByte, 0, fileByte.length);
            //outputStream.close();
            outputStream.flush();
        } catch (Exception e) {
           // log.error("同步资料库厂商 根据URL获取对应的文件信息->getFile失败:"+fileUrl,e);
            throw e;
        } finally {
            try{
            if(inputStream != null){
                inputStream.close();
            }
            }catch (Exception e){
                log.error("【getFile】处理异常",e);
            }
            try{
                response.close();
            }catch (Exception e){
                log.error("【getFile】处理异常",e);
            }
            try{
            outputStream.close();
            }catch (Exception e){
                log.error("【getFile】处理异常",e);
            }
        }
        return fileInfo;
    }

    @Data
    public static class FileInfo{

        File file;

        private String md5;

        private String fileType;
    }

    /**
     * 根据URL获取对应的文件信息
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/1/15 12:53.
     * @author: Randy.Xu.
     * @param fileUrl
     * @return: byte[].
     * @throws:  .
     */
    @Deprecated
    public static FileInfo getFileFromUrlOld(String fileUrl) throws Exception{

        FileInfo fileInfo = null;
        InputStream inputStream = null;
        String http = getHttp(fileUrl);
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = null;
        try {


            // http post 请求
            httpClient = HttpClients.createDefault();
            if (fileUrl.indexOf("http") != -1) {
                httpPost = new HttpPost(fileUrl);
            } else {
                httpPost = new HttpPost(http + fileUrl);
            }
            
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(60000).setSocketTimeout(60000).build();
            httpPost.setConfig(requestConfig);
            List<NameValuePair> params = new ArrayList<>();
            httpPost.setEntity(new UrlEncodedFormEntity(params, Consts.UTF_8));

            response = httpClient.execute(httpPost);
            // log.info("响应结果：{}",response);
            String contentType = response.getEntity().getContentType().getValue();
            String fileType = StringUtils.isNotEmpty(contentType) ? contentType.substring(contentType.lastIndexOf("/") + 1) : "txt";
            inputStream = response.getEntity().getContent();

            fileInfo = new FileInfo();
            fileInfo.setMd5(DigestUtils.md5DigestAsHex(getBytesFromInputStream(inputStream)));
            log.info("根据URL获取对应的文件信息->getFileFromUrl成功，文件后缀: {}",fileType);
            fileInfo.setFileType(fileType);

        } catch (Exception e) {
            log.error("根据URL获取对应的文件信息->getFileFromUrl失败：{}",http+fileUrl);
            log.error("异常信息:",e);
        }finally {

            if(inputStream != null){
                inputStream.close();
            }

            if(httpClient != null){
                httpClient.close();
            }
            if(response != null){
                response.close();
            }
            
        }

        return fileInfo;

    }

    /**
     * 根据URL获取对应的文件信息
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/1/15 12:53.
     * @author: Randy.Xu.
     * @param fileUrl
     * @return: byte[].
     * @throws:  .
     */
    @Deprecated
    public static FileInfo getFileFromUrl(String fileUrl) throws Exception{

        FileInfo fileInfo = null;
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        URL url;
        String http = getHttp(fileUrl);
        try {
            if (fileUrl.indexOf("http") != -1) {
                url=new URL(fileUrl);
            } else {
                url=new URL(http + fileUrl);
            }

            connection=(HttpURLConnection)url.openConnection();
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            inputStream = connection.getInputStream();

            fileInfo = new FileInfo();
            fileInfo.setMd5(DigestUtils.md5DigestAsHex(getBytesFromInputStream(inputStream)));

            String contentType = connection.getContentType();
            String fileType = StringUtils.isNotEmpty(contentType) ? contentType.substring(contentType.lastIndexOf("/") + 1) : "txt";

            fileInfo.setFileType(fileType);
            // log.info("根据URL获取对应的文件信息->getFileFromUrl成功，文件后缀: {}",fileType);
        } catch (Exception e) {
            log.warn("根据URL获取对应的文件信息->getFileFromUrl失败: {}",http+fileUrl);
            log.error("异常信息:",e);
        }finally {

            if(inputStream != null){
                inputStream.close();
            }

            if(connection != null){
                connection.disconnect();
            }
        }

        return fileInfo;

    }

    private static byte[] getBytesFromInputStream(InputStream inputStream) throws Exception{

        ByteArrayOutputStream bos = null;

        try{
            byte[] buffer = new byte[1024];
            int len = 0 ;
            bos = new ByteArrayOutputStream();
            while((len=inputStream.read(buffer) )!=-1){
                bos.write(buffer,0,len);
            }

            return bos.toByteArray();

        }finally {
            if(bos != null) {
                bos.close();
            }
        }

    }

    public static String getOssResourceIdFromStr(String str) {
        if(StringUtil.isEmpty(str)){
            return null;
        }
        int index=str.lastIndexOf("=");
        if(index<=0){
            return null;
        }
        return str.substring(index+1);
    }

    public static String getHttp(String fileUrl){
       return ossHttp ;
    }
}
