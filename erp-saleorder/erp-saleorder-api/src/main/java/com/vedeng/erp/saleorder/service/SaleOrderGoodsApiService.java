package com.vedeng.erp.saleorder.service;

import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;

import java.util.List;

/**
 * @Author: Putin
 * @description: 订单明细内部调用service
 * @CreateTime: 2023-01-13  11:28
 */
public interface SaleOrderGoodsApiService {
    /**
     * 根据销售详情id集合查询详情信息
     * @param saleorderGoodsIdList
     * @return
     */
    List<SaleOrderGoodsDetailDto> getRelatedDetail(List<Integer> saleorderGoodsIdList);

    /**
     * 查询销售商品id集合查询其中虚拟商品的信息
     * @param saleorderGoodsIdList
     * @return
     */
    List<SaleOrderGoodsDetailDto> getExpenseDetail(List<Integer> saleorderGoodsIdList);

    /**
     * 批量更新虚拟销售商品的收发货状态
     * @param saleOrderGoodsDetailDtos
     * @return
     */
    int updateExpenseGoodsDeliveryAndArrivalStatus (List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos);


    /**
     * 直属费用单销售发货(直发采购确认收货触发、普发下发wms触发）
     * @param buyorderExpenseId
     */
    void dosaleDeliveryStatus(Integer buyorderExpenseId);

    /**
     * 直属费用销售普发收货（包含不进待采购列表部分收货和销售单中所有见虚拟商品收货才收货）
     * @param buyorderExpenseId
     */
    void dosaleArrivalStatus(Integer buyorderExpenseId);

    /**
     * 直属费用销售直发收货（采购确认收货触发）
     * @param buyorderExpenseId
     */
    void doConfirmArrival(Integer buyorderExpenseId);

    /**
     * 非直属支付销售收发货（采购支付触发）
     * @param buyorderExpenseId
     */
    void pay4SaleStatus(Integer buyorderExpenseId);

    /**
     * 不可见虚拟商品收货
     * @param saleorderGoodsIds
     */
    void doNoSeeGoodsArrival(List<Integer> saleorderGoodsIds);

    /**
     * 根据采购单id更新不可见虚拟商品收货
     * @param buyorderGoodIds
     */
    void doNoSeeGoodsArrivalByBuyOrderId(List<Integer> buyorderGoodIds);

    /**
     * 根据销售单号查找订单中所有的虚拟商品
     * @param saleorderId
     * @return
     */
    List<SaleOrderGoodsDetailDto> findAllVirtualGoodsBySaleorderId(Integer saleorderId);

    /**
     * 根据skuId判断是否存在销售商品
     *
     * @param skuId skuId
     * @return 销售商品id
     */
    Integer getSaleOrderGoodsBySkuId(Integer skuId);

    /**
     * 根据销售单id查询销售单详情
     * @param saleorderId
     * @return
     */
    List<SaleOrderGoodsDetailDto> getBySaleorderId(Integer saleorderId);

    SaleOrderGoodsDetailDto getBySaleOrderGoodsId(Integer detailgoodsId);


    List<SaleOrderGoodsDetailDto> getSalesOrderGoodsByOrderId(Integer salesOrderId);

}
