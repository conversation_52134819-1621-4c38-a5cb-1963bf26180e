package com.vedeng.erp.kingdee.batch.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 根据红票生成的虚拟出入库记录
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class BatchVirtualWarehouseLogDto {
    /**
     * 虚拟出库记录
     */
    private BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutDto;
    /**
     * 虚拟出库记录明细
     */
    private List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutItemDtoList;
    /**
     * 虚拟入库记录
     */
    private BatchWarehouseGoodsOutInDto batchWarehouseGoodsInDto;
    /**
     * 虚拟出库记录明细
     */
    private List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsInItemDtoList;

    /**
     * 红字发票IDS
     */
    private List<Integer> invoiceIds;

    public BatchVirtualWarehouseLogDto(List<Integer> invoiceIds) {
        this.invoiceIds = invoiceIds;
    }
}
