package com.vedeng.common.mybatis.jbatis.constant;



/** 
 * <p>Description: log日志输出信息</p>
 * <AUTHOR>
 * @version V1.0
 */
public abstract class  ExceptionConstant {

	public final static String LOGGER_INSERT_NOT_ALL_ERROR="实体类初始化过程信息不完整，操作失败";
	
	public final static String LOGGER_UPDATE_OBJECT_ISNULL="参数实体类为空，操作失败";
	public final static String LOGGER_UPDATE_KEY_NOT_EXIST="主键不存在，操作失败";
	
	public final static String LOGGER_DEL_IS_NULL_ERROR="参数实体类为空，操作失败";
	
	public final static String LOGGER_LIST_IS_NULL_ERROR="参数List为空，操作失败";
	
	public final static String PRIMARY_KEY_NOT_FOUND_MSG ="主键不存在";
	
	public final static String LIST_IS_NULL_MSG ="List 不可为空";
	
	public final static String ID_TYPE_NO_CONF ="主键未配置生成策略";
	
	
}
