package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutInitStockQueryResultDto;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface KingDeeSaleInitOutStockService{


    /**
     * 根据ERP出入库单号（OUT_IN_NO）获取金蝶销售出库单
     *
     * @param outInNo ERP出入库单号
     * @return List<KingDeeSaleOutStockQueryResultDto>
     */
    List<KingDeeSaleOutInitStockQueryResultDto> getKingDeeSaleInitOutStock(String outInNo);

}
