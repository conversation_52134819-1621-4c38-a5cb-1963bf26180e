package com.vedeng.erp.aftersale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 冲销后和录票后
 * @date 2022/11/4 11:18
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReturnInvoiceWriteBackDto {

    /**
     * 售后单id
     */
    private Long expenseAfterSalesId;

    /**
     * 蓝发票代码
     */
    private String invoiceCode;

    /**
     * 蓝发票号码
     */
    private String invoiceNo;

    /**
     * 录入红票（冲销或退票）的id
     */
    private Integer invoiceId;

}
