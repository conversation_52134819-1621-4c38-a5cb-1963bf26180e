package com.vedeng.common.mybatis.jbatis.exception;


import com.vedeng.common.mybatis.jbatis.constant.ExceptionConstant;

/**
 * <p>Description: 实体类主键不存在异常</p> 
 * <AUTHOR>
 * @version V1.0
 */
public class PrimaryKeyNotFoundException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	
    public PrimaryKeyNotFoundException(){
        super(ExceptionConstant.PRIMARY_KEY_NOT_FOUND_MSG);
    }

    public PrimaryKeyNotFoundException(String msg){
        super(msg);
    }

    public PrimaryKeyNotFoundException(Throwable cause){
        super(cause);
    }

    public PrimaryKeyNotFoundException(String msg, Throwable cause){
        super(msg, cause);
    }

}
