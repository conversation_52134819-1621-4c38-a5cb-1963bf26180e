package com.vedeng.trader.model.vo;

import java.util.Date;
import java.util.List;

import com.vedeng.trader.model.Trader;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TraderVo extends Trader {
	
	private Integer traderType;
	
	private Integer verifyStatus;
	
	private String area;
	
	private String owner;

	private String userName;
	
	private List<Integer> traderCustomerIds;
	
	private List<Integer> traderSupplierIds;

	/**
	 * 开始时间，用于页面搜索
	 */
	private Long startTime;
	/**
	 * 结束时间，用于页面搜索
	 */
	private Long endTime;
	/**
	 * 业务类型
	 */
	private Integer businessType;

}
