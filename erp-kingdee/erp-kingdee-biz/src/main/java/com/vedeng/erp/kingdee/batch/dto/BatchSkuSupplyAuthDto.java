package com.vedeng.erp.kingdee.batch.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * sku商品供应商授权书信息主表
 * <AUTHOR>
 * @date 2022/11/30 15:31
 **/

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchSkuSupplyAuthDto {
    /**
    * 主键id
    */
    private Integer skuSupplyAuthId;

    /**
    * 供应商ID
    */
    private Integer traderSupplyId;

    /**
    * 品牌ids
    */
    private String brandIds;

    /**
    * 有效期开始时间
    */
    private Date validStartTime;

    /**
    * 有效期结束时间
    */
    private Date validEndTime;

    /**
    * 授权类型
    */
    private Integer authType;

    /**
    * 是否删除0否1是
    */
    private Byte isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 创建者
    */
    private Integer creator;

    /**
    * 更新时间
    */
    private Date modTime;

    /**
    * 更新者
    */
    private Integer updater;
}