package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 播报日志表
 */
@Getter
@Setter
public class BroadcastLogEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 播报图片配置表ID
     */
    private Integer broadcastPicConfigId;

    /**
     * 播报时间
     */
    private Date broadcastTime;

    /**
     * 播报类型：1=日常，2=周，3=月度，4=AED，5=自有品牌，6=自定义
     */
    private Integer broadcastType;

    /**
     * 播报目标：1=个人，2=小组，3=部门
     */
    private Integer broadcastTarget;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 部门ID
     */
    private Integer deptId;

    /**
     * 小组ID
     */
    private Integer secondDeptId;

    /**
     * 金额档位（元）
     */
    private BigDecimal amountStep;
    
    /**梯度值*/
    private Integer amountStepNum;

    /**
     * 本次累积金额
     */
    private BigDecimal totalAmount;

    /**
     * 完成率数字（例如95），可为负数
     */
    private Integer completRate;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;
}
