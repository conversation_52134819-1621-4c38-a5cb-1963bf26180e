package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.Logistics;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 快递公司信息
 * <p>Title: LogisticsMapper</p>  
 * <p>Description: </p>  
 * <AUTHOR>  
 * @date 2019年3月4日
 */
public interface LogisticsMapper {

	/**
	 * 根据参数查询快递信息
	 * <p>Title: getLogisticsListByParam</p>  
	 * <p>Description: </p>  
	 * @param logisticsParam
	 * @return  
	 * <AUTHOR>
	 * @date 2019年3月4日
	 */
	List<Logistics> getLogisticsListByParam(Map<String, Object> logisticsParam);


	/**
	 * 获取物流公司列表-快递寄送
	 * @param logisticsParam
	 * @return
	 */
	List<Logistics> getLogisticsListByParamForDeliery(Map<String, Object> logisticsParam);

	/**
	 * 根据快递公司id和省份id查询快递费用
	 * <p>Title: getFreeByParam</p>  
	 * <p>Description: </p>  
	 * @param regionParamMap
	 * @return  
	 * <AUTHOR>
	 * @date 2019年3月5日
	 */
	BigDecimal getFreeByParam(Map<String, Object> regionParamMap);

	/**
	 * <b>Description:根据快递公司主键获取快递公司信息</b><br>
	 *
	 *
	 * @param :[logistics]
	 * @return :com.vedeng.logistics.model.Logistics
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2019/5/20 7:13 PM
	 */
	Logistics getLogisticsById(@Param("logisticsId")Integer logisticsId);


    /**
    * @Description: 根据快递名称查询物流编码
    * @Param:
    * @return:
    * @Author: addis
    * @Date: 2019/11/8
    */
	String getLogisticsCode(String name);

	/**
	 * 根据快递名称获取主键ID
	 *
	 * @param name
	 * @return
	 */
	Integer getLogisticsIdByName(String name);
	/**
	 * <b>Description:</b><br>
	 * 根据carrierid获取信息
	 *
	 * @param carrierId
	 * @return com.vedeng.logistics.model.Logistics
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/3/30 11:05
	 */
	Logistics getLogisticsByCarrId(String carrierId);

	/**
	 * 快递公司ID获取物流公司Code
	 *
	 * @param logisticsId
	 * @return
	 */
    String getLogisticsCodeByLogisticsId(Integer logisticsId);

	List<Logistics> getWmsLogisticsList(@Param("companyId") Integer companyId,@Param("type")Integer type);

	/**
	 * 快递公司Code获取物流公司Name
	 * @param logisticsCode
	 * @return
	 */
	String getLogisticsNameByLogisticsCode(@Param("logisticsCode") String logisticsCode);

	/**
	 * 根据快递公司编码和快递编号查询logisticsId
	 * @param code,no
	 * @return
	 */
	List<Integer> getExpressIdByCodeAndNo(@Param("code") String code,@Param("logisticsNo")String no);

	Logistics getExistLogisticsById(@Param("logisticsId") Integer logisticsId);
}
