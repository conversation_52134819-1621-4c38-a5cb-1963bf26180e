package com.vedeng.erp.system.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 集团子公司主体基本信息表
 */
@Getter
@Setter
public class BaseCompanyInfoEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司简称
     */
    private String companyShortName;

    /**
     * 前端标识序号
     */
    private Integer frontEndSeq;

    /**
     * 金蝶账套编码
     */
    private String kingdeeAccountCode;

    /**
     * 统一社会信用代码
     */
    private String businessLicense;

    /**
     * 注册地址
     */
    private String companyAddress;

    /**
     * 注册电话
     */
    private String contactPhone;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 合同签约地址
     */
    private String contractAddress;

    /**
     * 客户TRADER_ID
     */
    private Integer customerTraderId;

    /**
     * 供应商TRADER_ID
     */
    private Integer supplierTraderId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 是否删除(0:否,1:是)
     */
    private Boolean isDeleted;

    /**
     * ERP的地址
     */
    private String erpDomain;

    /**
     * 主体详细信息字段
     */
    private String detailJson;
}