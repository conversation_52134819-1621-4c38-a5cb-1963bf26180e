<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.UnitMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.Unit" >
    <id column="UNIT_ID" property="unitId" jdbcType="INTEGER" />
    <result column="UNIT_GROUP_ID" property="unitGroupId" jdbcType="INTEGER" />
    <result column="UNIT_NAME" property="unitName" jdbcType="VARCHAR" />
    <result column="UNIT_NAME_EN" property="unitNameEn" jdbcType="VARCHAR" />
    <result column="IS_DEL" property="isDel" jdbcType="BIT" />
    <result column="SORT" property="sort" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="UNIT_KING_DEE_NO" jdbcType="VARCHAR" property="unitKingDeeNo" />
  </resultMap>
  <sql id="Base_Column_List" >
    UNIT_ID, UNIT_GROUP_ID, UNIT_NAME, UNIT_NAME_EN, IS_DEL, SORT, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER,UNIT_KING_DEE_NO
  </sql>
  
  <select id="getUnitlistpage" resultMap="BaseResultMap" parameterType="Map" >
    select 
    a.*
    from T_UNIT a
    left join 
    	T_UNIT_GROUP b
    on
    	a.UNIT_GROUP_ID = b.UNIT_GROUP_ID
   	<where>
		a.IS_DEL = 0
		<if test="unit.companyId!=null and unit.companyId != 0">
			and b.COMPANY_ID = #{unit.companyId}
		</if>
		<if test="unit.unitName!=null and unit.unitName!=''">
			and a.UNIT_NAME like CONCAT('%',#{unit.unitName,jdbcType=VARCHAR},'%' )
		</if>
	</where>
	order by a.SORT desc, a.UNIT_ID desc
  </select>
  
  <select id="getAllUnitList" resultMap="BaseResultMap" parameterType="com.vedeng.goods.model.Unit" >
    select 
    a.*
    from T_UNIT a
    left join 
    	T_UNIT_GROUP b
    on
    	a.UNIT_GROUP_ID = b.UNIT_GROUP_ID
   	<where>
		a.IS_DEL = 0
		<if test="companyId!=null and companyId != 0">
			and b.COMPANY_ID = #{companyId}
		</if>
		<if test="unitName!=null and unitName!=''">
			and a.UNIT_NAME like CONCAT('%',#{unitName,jdbcType=VARCHAR},'%' )
		</if>
	</where>
	order by a.SORT desc, a.UNIT_ID desc
  </select>
  
  <!-- 验证单位是否存在重名 -->
	<select id="vailUnitName" parameterType="com.vedeng.goods.model.Unit" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM T_UNIT a
		left join
			T_UNIT_GROUP b
	    on
	    	a.UNIT_GROUP_ID = b.UNIT_GROUP_ID
		WHERE a.UNIT_NAME = #{unitName,jdbcType=VARCHAR}
		<if test="unitId!=null and unitName!=''">
			AND a.UNIT_ID <![CDATA[ <> ]]> #{unitId,jdbcType=INTEGER}
		</if>
		<if test="companyId!=null and companyId != 0">
			and b.COMPANY_ID = #{companyId}
		</if>
		AND a.IS_DEL = 0
	</select>
	
	<!-- 验证单位是否已被使用 -->
	<select id="vailUnitNameIsUsed" parameterType="com.vedeng.goods.model.Unit" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM T_GOODS a
		WHERE a.UNIT_ID = #{unitId,jdbcType=INTEGER}
	</select>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_UNIT
    where UNIT_ID = #{unitId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_UNIT
    where UNIT_ID = #{unitId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.Unit" >
    insert into T_UNIT (UNIT_ID, UNIT_GROUP_ID, UNIT_NAME, 
      UNIT_NAME_EN, IS_DEL, SORT, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER,UNIT_KING_DEE_NO)
    values (#{unitId,jdbcType=INTEGER}, #{unitGroupId,jdbcType=INTEGER}, #{unitName,jdbcType=VARCHAR}, 
      #{unitNameEn,jdbcType=VARCHAR}, #{isDel,jdbcType=BIT}, #{sort,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER},#{unitKingDeeNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.Unit" useGeneratedKeys="true" keyProperty="unitId">
    insert into T_UNIT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="unitId != null" >
        UNIT_ID,
      </if>
      <if test="unitGroupId != null" >
        UNIT_GROUP_ID,
      </if>
      <if test="unitName != null" >
        UNIT_NAME,
      </if>
      <if test="unitNameEn != null" >
        UNIT_NAME_EN,
      </if>
      <if test="isDel != null" >
        IS_DEL,
      </if>
      <if test="sort != null" >
        SORT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="unitKingDeeNo != null">
        UNIT_KING_DEE_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="unitId != null" >
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="unitGroupId != null" >
        #{unitGroupId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null" >
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="unitNameEn != null" >
        #{unitNameEn,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null" >
        #{isDel,jdbcType=BIT},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
     <if test="unitKingDeeNo != null">
        #{unitKingDeeNo,jdbcType=VARCHAR},
     </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.Unit" >
    update T_UNIT
    <set >
      <if test="unitGroupId != null" >
        UNIT_GROUP_ID = #{unitGroupId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null" >
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="unitNameEn != null" >
        UNIT_NAME_EN = #{unitNameEn,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null" >
        IS_DEL = #{isDel,jdbcType=BIT},
      </if>
      <if test="sort != null" >
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="unitKingDeeNo != null">
        UNIT_KING_DEE_NO = #{unitKingDeeNo,jdbcType=VARCHAR},
      </if>
    </set>
    where UNIT_ID = #{unitId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.Unit" >
    update T_UNIT
    set UNIT_GROUP_ID = #{unitGroupId,jdbcType=INTEGER},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      UNIT_NAME_EN = #{unitNameEn,jdbcType=VARCHAR},
      IS_DEL = #{isDel,jdbcType=BIT},
      SORT = #{sort,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      UNIT_KING_DEE_NO = #{unitKingDeeNo,jdbcType=VARCHAR}
    where UNIT_ID = #{unitId,jdbcType=INTEGER}
  </update>
  
  <!-- 根据公司ID查询单位list -->
  <select id="selectUnitListByCompanyId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    SELECT 
		A.UNIT_NAME, A.UNIT_NAME_EN, A.UNIT_ID
	FROM T_UNIT A
	LEFT JOIN T_UNIT_GROUP B ON B.UNIT_GROUP_ID = A.UNIT_GROUP_ID
		AND B.COMPANY_ID = #{companyId, jdbcType=INTEGER}
	WHERE
		A.IS_DEL = 0
	ORDER BY A.SORT DESC, A.UNIT_ID DESC
  </select>
  
</mapper>