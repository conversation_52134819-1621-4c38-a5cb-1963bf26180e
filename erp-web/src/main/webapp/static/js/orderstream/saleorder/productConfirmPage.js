function continueQuote(){
    var orderIdList = $("#orderIdList").val();
    var saleOrderNo = $("#saleOrderNo").val();
    var traderId = $("#traderId").val();
    $.ajax({
        async:false,
        url:page_url+'/orderstream/saleorder/getSaleOrderInfo.do',
        data:{"saleOrderNo":saleOrderNo,"traderId":traderId},
        type:"POST",
        dataType : "json",
        beforeSend:function(){
            if (!$.trim(saleOrderNo)){
                layer.alert("订单号不能为空",{ icon: 2 })
                return false;
            }
        },
        success:function(data){
            if (data.code == 0) {
                var result = data.data
                orderIdList = orderIdList.split(",");
                orderIdList.push(result.saleorderId)
                var src = page_url + "/orderstream/saleorder/productConfirmPage.do?saleOrderIds="+orderIdList + "&traderId=" + result.traderId;
                $(window.frameElement).attr('src', src);
            } else {
                layer.alert(data.message,{ icon: 2 });
            }
            //refreshPageList(data);
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })


}

function delSaleOrderGoods(obj){
    n = $(obj).parents("tr").index() + 1;
    $("#product_table").find("tr:eq(" + n + ")").remove();
    $("#product_table").find("tr").each(function(i){
        $(this).find("td").eq(0).text(i);
        var index = i-1;
        $(this).find("input[name$='.saleorderId']").attr("name","goodsList["+index+"].saleorderId");
        $(this).find("input[name$='.goodsId']").attr("name","goodsList["+index+"].goodsId");
        $(this).find("input[name$='.sku']").attr("name","goodsList["+index+"].sku");
        $(this).find("input[name$='.goodsName']").attr("name","goodsList["+index+"].goodsName");
        $(this).find("input[name$='.brandName']").attr("name","goodsList["+index+"].brandName");
        $(this).find("input[name$='.model']").attr("name","goodsList["+index+"].model");
        $(this).find("input[name$='.unitName']").attr("name","goodsList["+index+"].unitName");
        $(this).find("input[name$='.price']").attr("name","goodsList["+index+"].price");
        $(this).find("input[name$='.num']").attr("name","goodsList["+index+"].num");
    });
}


function addSubmit(){
    //必填项校验
    priceFlag = true;
    numFlag = true;
    var flag = true;
    var sku = $("input[name$='.sku']")
    if(sku == null || sku.length == 0) {
        layer.alert("商品不允许为空");
        flag = false;
        return false;
    }
    $.each($("input[name$='.price']"), function () {
        var price = $(this).val()
        if (price === '' || price === undefined){
            $(this).addClass("errorbor");
            layer.alert("单价不允许为空");
            flag = false;
            return false;
        }
        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if (price.length > 0 && !reg.test(price)) {
            $(this).addClass("errorbor");
            layer.alert("单价金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
            flag = false;
            return false;
        }
        if (Number(price) > 300000000) {
            $(this).addClass("errorbor");
            layer.alert("产品单价不允许超过三千万");//文本框ID和提示用语
            flag = false;
            return false;
        }else {
            $(this).removeClass("errorbor");
        }
        var num = $(this).parent().next().children().val()
        if (num === '' || num === undefined){
            $(this).parent().next().children().addClass("errorbor");
            layer.alert("数量不允许为空");
            flag = false;
            return false;
        }
        var re = /^[1-9]\d*$/;
        if (!re.test(num)){
            $(this).parent().next().children().addClass("errorbor");
            layer.alert("数量必须为大于0的整数");//文本框ID和提示用语
            flag = false;
            return false;
        }
        if (Number(num) > 100000000) {
            $(this).parent().next().children().addClass("errorbor");
            layer.alert("产品数量不允许超过一亿");//文本框ID和提示用语
            flag = false;
            return false;
        } else {
            $(this).parent().next().children().removeClass("errorbor");
        }
        if ((Number(num) * Number(price)) > 300000000) {
            $(this).addClass("errorbor");
            $(this).parent().next().children().addClass("errorbor");
            layer.alert("产品总金额不允许超过三亿");//文本框ID和提示用语
            flag = false;
            return false;
        }else {
            $(this).removeClass("errorbor");
            $(this).parent().next().children().removeClass("errorbor");
        }
    })
    if (flag){
        var $form = $("#editForm");
        $form.submit();
    }

}