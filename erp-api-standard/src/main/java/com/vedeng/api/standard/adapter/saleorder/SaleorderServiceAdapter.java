package com.vedeng.api.standard.adapter.saleorder;

import cn.hutool.core.bean.BeanUtil;
import com.vedeng.api.standard.adapter.buyorder.dto.BuyOrderApprovalRequest;
import com.vedeng.api.standard.adapter.buyorder.dto.BuyOrderResponse;
import com.vedeng.api.standard.adapter.saleorder.converter.SaleorderDataConverter;
import com.vedeng.api.standard.adapter.saleorder.dto.*;
import com.vedeng.api.standard.approval.ApprovalConfig;
import com.vedeng.api.standard.approval.ApprovalExecutor;
import com.vedeng.api.standard.approval.ApprovalResult;
import com.vedeng.api.standard.converter.ResponseConfig;
import com.vedeng.api.standard.converter.ResponseMappingConfig;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.rules.BuyOrderExistsRule;
import com.vedeng.api.standard.validation.rules.BuyOrderStatusRule;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.order.service.SaleorderService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component("saleorderServiceAdapter")
public class SaleorderServiceAdapter  extends AbstractServiceAdapter {


    @Autowired
    private ApprovalExecutor approvalExecutor;

    @Autowired
    private SaleorderDataConverter dataConverter;

    @Autowired
    private BusinessTemplate businessTemplate;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private SaleorderService saleorderService;


    @Override
    public String getModuleName() {
        return "saleorder";
    }

    /**
     * 注册操作处理器
     * 使用简单的映射机制替代 switch 语句
     */
    @Override
    protected void registerOperationHandlers() {
        // 注册支持的操作处理器（使用支持异常的版本）
        registerThrowingHandler("create", this::executeCreateOperation);//第一步：创建销售单
        registerThrowingHandler("query", this::executeQueryOperation);//第二步：查询销售单
        registerThrowingHandler("submit", this::executeSubmitOperation);//第三步：提交审核
        registerThrowingHandler("approve", this::executeApproveOperation);//第四步：审核通过


        logger.info("销售单服务适配器注册完成，支持{}个操作", getHandlerCount());
    }


    /**
     * 执行创建操作
     */
    private Object executeCreateOperation(ApiRequest request) {
        //ResponseMappingConfig responseConfig = ResponseConfig.create("采购单创建成功", "buyorderId");
        try {
            // 从请求中提取数据并转换为BuyorderVo
            SaleOrderCreateRequest createRequest = dataConverter.convert(request.getData(), SaleOrderCreateRequest.class);

            OrderData orderData = convertRequestToOrderData(createRequest);
            logger.info("销售订单创建：{}", orderData);
//            BuyorderVo buyorderVo = convertToBuyorderVo(createRequest);
//            logger.info("BuyorderVo转换成功，准备调用Controller: buyorderId={}, saleorderId={}, saleorderGoodsIds={}",
//                    buyorderVo.getBuyorderId(), buyorderVo.getSaleorderId(), buyorderVo.getSaleorderGoodsIds());

            return businessTemplate.<SaleOrderCreateRequest, SaleOrderResponse>executeCreate(request)
                    .requestType(SaleOrderCreateRequest.class)
                    .responseType(SaleOrderResponse.class)
                    //.validationRules(SaleOrderExistsRule.class)  // 暂时注释，避免验证失败
                    //.duplicateRules(SaleOrderDuplicateRule.class) // 暂时注释，避免防重检查失败
                    .controller("saleorderService", "saveBdSaleOrderForStandard")
                    .withIdempotencyHandling("SALE_ORDER", "saleorderId", "saleorderNo") // 框架自动生成规则、构建上下文、处理异常
                    .withoutHttpParameters(ParameterConfig.of(OrderData.class,orderData))
//                    .responseConfig(responseConfig)
                    .execute();

        } catch (Exception e) {
            logger.error("执行创建操作失败: requestId={}", request.getRequestId(), e);
            throw new RuntimeException("销售单创建失败: " + e.getMessage(), e);
        }
    }

    private OrderData convertRequestToOrderData(SaleOrderCreateRequest createRequest ) {
        try {
            OrderData orderData = new OrderData();
            BeanUtils.copyProperties( createRequest,orderData);
            return orderData;
        }catch (Exception e) {
            logger.error("转换请求对象",e);
        }
        return null;

    }



    /**
     * 执行查询操作
     */
    private Object executeQueryOperation(ApiRequest request) throws Exception {
        // 从请求中提取数据
        Map<String,Object> requestMap = request.getData();
        Integer saleorderId = (Integer) requestMap.get("saleOrderId");
        String saleOrderNo = (String) requestMap.get("saleOrderNo");
        
        // 支持通过销售单号或销售单ID查询
        if (saleOrderNo != null && !saleOrderNo.trim().isEmpty()) {
            // 通过销售单号查询
            return businessTemplate.<SaleOrderQueryRequest, SaleOrderResponse>executeQuery(request)
                    .requestType(SaleOrderQueryRequest.class)
                    .responseType(SaleOrderResponse.class)
                    .controller("saleOrderServiceImpl", "getBySaleOrderNoForStandard")
                    .withoutHttpParameters(ParameterConfig.of(String.class, saleOrderNo))
                    .execute();
        } else if (saleorderId != null) {
            // 通过销售单ID查询
            return businessTemplate.<SaleOrderQueryRequest, SaleOrderResponse>executeQuery(request)
                    .requestType(SaleOrderQueryRequest.class)
                    .responseType(SaleOrderResponse.class)
                    .controller("saleOrderServiceImpl", "getBySaleOrderIdForStandard")
                    .withoutHttpParameters(ParameterConfig.of(Integer.class, saleorderId))
                    .execute();
        } else {
            throw new IllegalArgumentException("查询销售单时，saleOrderId 和 saleOrderNo 不能都为空");
        }
    }

    /**
     * 执行提交审核操作
     */
    private Object executeSubmitOperation(ApiRequest request) throws Exception {
        Map<String,Object> requestMap = request.getData();
        Integer saleOrderId = (Integer) requestMap.get("saleOrderId");

        return businessTemplate.<SaleOrderQueryRequest, SaleOrderResponse>executeUpdate(request)
                .requestType(SaleOrderQueryRequest.class)
                .responseType(SaleOrderResponse.class)
                .controller("saleorderController", "submitApproveSaleOrderForStandard")
                .withIdempotencyHandling("SALE_ORDER_SUBMIT" ) // 框架自动生成规则、构建上下文、处理异常
                .withHttpParameters(ParameterConfig.of(Integer.class, saleOrderId))
                .execute();
    }

    /**
     * 执行审核通过操作
     */
    private ApprovalResult executeApproveOperation(ApiRequest request) throws Exception {
//        SaleOrderRequest approvalRequest = new SaleOrderRequest();
//        approvalRequest.setSaleOrderId();

        Map<String,Object> requestMap = request.getData();
        Integer saleOrderId = (Integer) requestMap.get("saleOrderId");

        String taskId = saleorderService.getTaskIdBySaleOrderId(saleOrderId);
//        BeanUtil.fillBeanWithMap(request.getData(), approvalRequest, true);
//        approvalRequest.validate();
        SaleOrderApprovalRequest approvalRequest = new SaleOrderApprovalRequest();
        approvalRequest.setSaleOrderId(saleOrderId);
        approvalRequest.setTaskId(taskId);
        approvalRequest.setPass(Boolean.TRUE);
        approvalRequest.setComment("业务流转单，自动审核通过");

        // 创建审核配置
        ApprovalConfig<SaleOrderApprovalRequest, SaleOrderResponse> config = ApprovalConfig.<SaleOrderApprovalRequest, SaleOrderResponse>builder()
                .requestType(SaleOrderApprovalRequest.class)
                .responseType(SaleOrderResponse.class)
                //模拟调用/saleorder/checkSaleorderFlow方法

                .controller("saleorderController", "checkSaleorderFlow")
                //.validationRules(BuyOrderExistsRule.class, BuyOrderStatusRule.class)

                .responseConfig(ResponseConfig.create("审核步骤成功", "stepResult"))
                .withHttpParameters(approvalReq -> new ParameterConfig[]{
                        ParameterConfig.of(String.class, approvalReq.getTaskId()),      // taskId
                        ParameterConfig.of(String.class, approvalReq.getComment()),    // comment
                        ParameterConfig.of(Boolean.class, approvalReq.getPass()),      // pass
                        ParameterConfig.of(Integer.class, approvalReq.getSaleOrderId()) // saleOrderId
                })
                .build();

        // 使用通用审核执行器
        return approvalExecutor.executeMultiStepApproval(request, approvalRequest, config);
    }



    /**
     * 获取不需要身份认证的操作列表
     * <p>
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query"};
    }



}
