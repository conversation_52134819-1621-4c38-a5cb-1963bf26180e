package com.vedeng.erp.finance.manager;

import com.vedeng.erp.finance.domain.context.InvoiceRedConfirmationContext;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import com.vedeng.common.statemachine.Action;

/**
 * 确认单执行事件
 *
 * <AUTHOR>
 */
public interface InvoiceRedConfirmationAction extends Action<InvoiceRedConfirmationStateEnum, InvoiceRedConfirmationEvent, InvoiceRedConfirmationContext> {

}
