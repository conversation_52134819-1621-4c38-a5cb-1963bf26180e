/* 表单地址弹层 */
Vue.component('ui-form-address', {
    template: `
        <div class="ui-form-address-wrap">
            <div class="form-address-view" @click="openSlideDialog">
                <ui-placeholder v-if="!displayVal">{{ placeholder }}</ui-placeholder>
                <div v-else class="form-address-show">
                    <div class="value text-line-1">{{ displayVal }}</div>
                    <i @click.stop="handlerClear" class="vd-ui_icon icon-error2"></i>
                </div>
            </div>

            <crm-slide-dialog ref="slideDialog" title="地区选择">
                <div class="form-address-panel">
                    <vd-ui-address
                        ref="address"
                        class="panel-inner"
                        v-model="tempValue"
                        :data="addressData"
                        v-$attrs
                        @change="handlerChange"
                    ></vd-ui-address>
                    <div class="slide-dialog-default-footer">
                        <div class="btn-cancel-flex">
                            <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                        </div>
                        <div class="btn-confirm-flex">
                            <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                        </div>
                    </div>
                </div>
            </crm-slide-dialog>
        </div>
    `,
    props: {
        // 省市区数据
        data: Array,
        value: Array,
        placeholder: {
            type: String
        },
        needTrigger: {
            type: Boolean,
            default: false, //值改变后是否重新触发change
        },

        // Address组件参数
        eachLevel: {
            type: Boolean,
            default: true
        },
        maxHeight: Number,
        maxlength: {
            type: String,
            default: ''
        },
    },
    data () {
        return {
            addressData: [],
            // 已选地区
            areaInfo: [],

            // temp
            tempData: [],
            tempValue: [],
        }
    },
    watch: {
        data: {
            handler (newV) {
                if (newV.length) {
                    this.addressData = newV || [];
                    this.init(this.value);
                }
            },
            deep: true,
            immediate: true,
        },
        value: {
            handler (newV, oldV) {
                this.tempValue = JSON.parse(JSON.stringify(newV));

                if (newV && newV.length) {
                    let valchange = !oldV || newV.join(',') !== oldV.join(',');
                    this.init(newV, valchange);
                }
            },
            deep: true,
            immediate: true
        },
    },
    computed: {
        displayVal () {
            arr = [];
            this.areaInfo.forEach(item => {
                arr.push(item.label);
            })
            return arr.join(' / ');
        }
    },
    mounted() {
    },
    methods: {
        init ( defaultValues, valchange) {
            if (defaultValues.length && this.addressData.length) {
                this.areaInfo = [];
                let level = defaultValues.length;
                this.addressData.forEach(levelPro => {
                    if (levelPro.value == defaultValues[0]) {
                        this.areaInfo.push(levelPro);
                        level--;

                        // 市级
                        if (level) {
                            levelPro.children.forEach(levelCity => {
                                if (levelCity.value == defaultValues[1]) {
                                    this.areaInfo.push(levelCity);
                                    level--;

                                    // 区级
                                    if (level) {
                                        levelCity.children.forEach(levelArea => {
                                            if (levelArea.value == defaultValues[2]) {
                                                this.areaInfo.push(levelArea);
                                            }
                                        })
                                    }
                                }
                            })
                        } 
                    }
                });

                if (this.eachLevel && this.areaInfo.length < 3) {
                    this.handlerClear();
                }
                else if (this.needTrigger && valchange) {
                    this.$emit('change', this.areaInfo);
                }
            }
        },

        openSlideDialog () {
            this.tempValue = JSON.parse(JSON.stringify(this.value));
            this.$refs.slideDialog.show();
        },
        handlerChange (data) {
            this.tempData = data || [];
        },
        handlerClear () {
            this.$refs.address.clearData(); // 清空内层组件值
            this.areaInfo = [];
            this.tempData = [];
            this.tempValue = [];
            this.$emit("input", []); // 修改外层v-model值
            this.$emit('change', []);
        },


        // 确定
        handlerConfirm () {
            if (!this.tempValue.length) {
                this.$message({
                    type: 'error',
                    message: '请选择地址',
                })
                return;
            }

            let emitVal = JSON.parse(JSON.stringify(this.tempValue));
            this.areaInfo = this.tempData;
            let emitData = Object.assign([], this.areaInfo);
            this.$emit("input", emitVal); // 修改外层v-model值
            this.$emit('change', emitData);

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.address.clearFilter();
            this.$refs.slideDialog.hide();
            this.tempData = [];
            this.tempValue = [];
        },
    }
})