package com.vedeng.common.trace.xxl;

import com.vedeng.core.trace.constant.MdcConstant;
import com.vedeng.core.trace.util.MdcHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

/**
 * 定时任务
 */
@Slf4j
public abstract class AbstractJobHandler extends IJobHandler {

    public void initMdc() {
        try {
            MdcHelper.initTrace(null);
            String traceId = MDC.get(MdcConstant.TRANCE_ID);
            XxlJobLogger.log("执行开始,traceId:{}启用", traceId);
            log.info("定时任务{}自动生成traceId", this.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("执行AbstractJobHandler.init()中MdcHelper.initTrace报错，但不影响业务流程", e);
        }
    }

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            initMdc();
            ReturnT<String> result = doExecute(param);
            return result;
        } finally {
            clearMdc();
        }
    }
    
    /**
     * 具体任务执行逻辑，由子类实现
     *
     * @param param 任务参数
     * @return 执行结果
     * @throws Exception 执行异常
     */
    protected abstract ReturnT<String> doExecute(String param) throws Exception;

    public void clearMdc() {
        log.info("任务结束执行MdcHelper.clear");
        try {
            MdcHelper.clear();
        } catch (Exception e) {
            log.error("执行AbstractJobHandler.destroy()中LogHelper.clear报错，但不影响业务流程", e);
        }
    }
}
