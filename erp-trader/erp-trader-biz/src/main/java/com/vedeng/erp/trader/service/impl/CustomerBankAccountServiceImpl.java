package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.dto.CustomerBankAccountDto;
import com.vedeng.erp.trader.domain.dto.TraderSupplierDto;
import com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity;
import com.vedeng.erp.trader.domain.entity.TraderSupplierFinance;
import com.vedeng.erp.trader.mapper.CustomerBankAccountMapper;
import com.vedeng.erp.trader.service.CustomerBankAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerBankAccountServiceImpl implements CustomerBankAccountService {

    @Autowired
    private CustomerBankAccountMapper customerBankAccountMapper;

    /**
     * excel 最大数据量 10W
     */
    private final Integer MAX_DATA_LENGTH = 100000;

    @Override
    public PageInfo<CustomerBankAccountDto> list(PageParam<CustomerBankAccountDto> customerBankAccountDtoPageParam) {
        log.info("查询客户账户参数，入参:{}", JSON.toJSONString(customerBankAccountDtoPageParam));
        PageInfo<CustomerBankAccountDto> customerBankAccountDtoPageInfo = PageHelper.startPage(customerBankAccountDtoPageParam)
                .doSelectPageInfo(() -> customerBankAccountMapper.findByAllToPage(customerBankAccountDtoPageParam.getParam()));
        return customerBankAccountDtoPageInfo;
    }

    @Override
    public void delete(CustomerBankAccountDto customerBankAccountDto) {
        if (Objects.isNull(customerBankAccountDto) || Objects.isNull(customerBankAccountDto.getCustomerBankAccountId())) {
            log.info("删除客户账户参数异常，customerBankAccountDto:{}", JSON.toJSONString(customerBankAccountDto));
            return;
        }
        customerBankAccountMapper.updateIsDeleteByCustomerBankAccountId(ErpConstant.ONE, customerBankAccountDto.getCustomerBankAccountId());
    }

    @Override
    public void update(CustomerBankAccountDto dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getCustomerBankAccountId())) {
            log.info("更新客户账户参数异常，dto:{}", JSON.toJSONString(dto));
            return;
        }
        CustomerBankAccountEntity entity = new CustomerBankAccountEntity();
        entity.setCustomerBankAccountId(dto.getCustomerBankAccountId());
        entity.setIsVerify(dto.getIsVerify());
        entity.setModTime(new Date());
        customerBankAccountMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public List<CustomerBankAccountDto> upload(MultipartFile file) throws Exception {
        List<Long> updateIdList = new ArrayList<>();
        List<CustomerBankAccountDto> errorList = new ArrayList<>();
        AtomicInteger row = new AtomicInteger(1);
        EasyExcel.read(file.getInputStream(), CustomerBankAccountDto.class, new CustomerBankAccountExcelReadListener<CustomerBankAccountDto>(list -> {
            for (CustomerBankAccountDto data : list) {
                row.getAndIncrement();
                if (row.get() > MAX_DATA_LENGTH) {
                    throw new ServiceException(StrUtil.format("客户账户上传，数据量超过10W!"));
                }
                if (StrUtil.isBlank(data.getAccountName()) || StrUtil.isBlank(data.getAccountNo())) {
                    log.info("客户账户上传，第{}行数据不可为空", row.get());
                    errorList.add(data);
                    continue;
                }
                List<CustomerBankAccountEntity> customerBankAccountEntities = customerBankAccountMapper.findByAccountNameAndAccountNo(data.getAccountName(), data.getAccountNo());
                if (CollUtil.isEmpty(customerBankAccountEntities)) {
                    log.info("客户账户上传，第{}行客户账户不存在", row.get());
                    errorList.add(data);
                    continue;
                }
                List<Long> idList = customerBankAccountEntities.stream().map(CustomerBankAccountEntity::getCustomerBankAccountId).collect(Collectors.toList());
                updateIdList.addAll(idList);
            }
        })).sheet().doRead();
        if (CollUtil.isNotEmpty(updateIdList)) {
            log.info("客户账户上传，更新客户账户状态，更新数量:{},更新id:{}", updateIdList.size(), JSON.toJSONString(updateIdList));
            customerBankAccountMapper.updateIsVerifyByCustomerBankAccountIdIn(ErpConstant.ONE, updateIdList);
        }
        return errorList;
    }
}
