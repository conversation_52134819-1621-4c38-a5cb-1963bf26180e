package com.vedeng.order.model;

import java.io.Serializable;
import lombok.Data;

/**
 * T_ORDER_ASSISTANT_RELATION
 * <AUTHOR>
@Data
public class OrderAssistantRelationDo implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 订单助理用户id
     */
    private Integer orderAssistantUserId;

    /**
     * 产品经理用户id
     */
    private Integer productManagerUserId;

    /**
     * 产品助理用户id
     */
    private Integer productAssitantUserId;

    /**
     * 添加事件
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 是否删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;
}