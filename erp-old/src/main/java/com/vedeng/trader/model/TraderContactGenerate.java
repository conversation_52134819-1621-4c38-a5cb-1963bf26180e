package com.vedeng.trader.model;

import java.util.Date;

public class TraderContactGenerate {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.TRADER_CONTACT_ID
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer traderContactId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.TRADER_ID
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer traderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.TRADER_TYPE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer traderType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.IS_ENABLE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer isEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.SEX
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer sex;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.NAME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.DEPARTMENT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String department;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.POSITION
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String position;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.TELEPHONE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String telephone;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.FAX
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String fax;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.MOBILE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String mobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.MOBILE2
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String mobile2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.EMAIL
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String email;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.QQ
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String qq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.WEIXIN
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String weixin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.IS_ON_JOB
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer isOnJob;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.IS_DEFAULT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer isDefault;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.BIRTHDAY
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Date birthday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.IS_MARRIED
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Byte isMarried;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.HAVE_CHILDREN
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Byte haveChildren;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.EDUCATION
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer education;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.CHARACTER
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String character;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.COMMENTS
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private String comments;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.ADD_TIME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Long addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.CREATOR
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.MOD_TIME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Long modTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_TRADER_CONTACT.UPDATER
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    private Integer updater;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.TRADER_CONTACT_ID
     *
     * @return the value of T_TRADER_CONTACT.TRADER_CONTACT_ID
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getTraderContactId() {
        return traderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.TRADER_CONTACT_ID
     *
     * @param traderContactId the value for T_TRADER_CONTACT.TRADER_CONTACT_ID
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setTraderContactId(Integer traderContactId) {
        this.traderContactId = traderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.TRADER_ID
     *
     * @return the value of T_TRADER_CONTACT.TRADER_ID
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getTraderId() {
        return traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.TRADER_ID
     *
     * @param traderId the value for T_TRADER_CONTACT.TRADER_ID
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.TRADER_TYPE
     *
     * @return the value of T_TRADER_CONTACT.TRADER_TYPE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getTraderType() {
        return traderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.TRADER_TYPE
     *
     * @param traderType the value for T_TRADER_CONTACT.TRADER_TYPE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setTraderType(Integer traderType) {
        this.traderType = traderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.IS_ENABLE
     *
     * @return the value of T_TRADER_CONTACT.IS_ENABLE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getIsEnable() {
        return isEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.IS_ENABLE
     *
     * @param isEnable the value for T_TRADER_CONTACT.IS_ENABLE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.SEX
     *
     * @return the value of T_TRADER_CONTACT.SEX
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getSex() {
        return sex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.SEX
     *
     * @param sex the value for T_TRADER_CONTACT.SEX
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setSex(Integer sex) {
        this.sex = sex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.NAME
     *
     * @return the value of T_TRADER_CONTACT.NAME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.NAME
     *
     * @param name the value for T_TRADER_CONTACT.NAME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.DEPARTMENT
     *
     * @return the value of T_TRADER_CONTACT.DEPARTMENT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getDepartment() {
        return department;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.DEPARTMENT
     *
     * @param department the value for T_TRADER_CONTACT.DEPARTMENT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setDepartment(String department) {
        this.department = department;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.POSITION
     *
     * @return the value of T_TRADER_CONTACT.POSITION
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getPosition() {
        return position;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.POSITION
     *
     * @param position the value for T_TRADER_CONTACT.POSITION
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setPosition(String position) {
        this.position = position;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.TELEPHONE
     *
     * @return the value of T_TRADER_CONTACT.TELEPHONE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getTelephone() {
        return telephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.TELEPHONE
     *
     * @param telephone the value for T_TRADER_CONTACT.TELEPHONE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.FAX
     *
     * @return the value of T_TRADER_CONTACT.FAX
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getFax() {
        return fax;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.FAX
     *
     * @param fax the value for T_TRADER_CONTACT.FAX
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setFax(String fax) {
        this.fax = fax;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.MOBILE
     *
     * @return the value of T_TRADER_CONTACT.MOBILE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.MOBILE
     *
     * @param mobile the value for T_TRADER_CONTACT.MOBILE
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.MOBILE2
     *
     * @return the value of T_TRADER_CONTACT.MOBILE2
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getMobile2() {
        return mobile2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.MOBILE2
     *
     * @param mobile2 the value for T_TRADER_CONTACT.MOBILE2
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setMobile2(String mobile2) {
        this.mobile2 = mobile2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.EMAIL
     *
     * @return the value of T_TRADER_CONTACT.EMAIL
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.EMAIL
     *
     * @param email the value for T_TRADER_CONTACT.EMAIL
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.QQ
     *
     * @return the value of T_TRADER_CONTACT.QQ
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getQq() {
        return qq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.QQ
     *
     * @param qq the value for T_TRADER_CONTACT.QQ
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setQq(String qq) {
        this.qq = qq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.WEIXIN
     *
     * @return the value of T_TRADER_CONTACT.WEIXIN
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getWeixin() {
        return weixin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.WEIXIN
     *
     * @param weixin the value for T_TRADER_CONTACT.WEIXIN
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setWeixin(String weixin) {
        this.weixin = weixin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.IS_ON_JOB
     *
     * @return the value of T_TRADER_CONTACT.IS_ON_JOB
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getIsOnJob() {
        return isOnJob;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.IS_ON_JOB
     *
     * @param isOnJob the value for T_TRADER_CONTACT.IS_ON_JOB
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setIsOnJob(Integer isOnJob) {
        this.isOnJob = isOnJob;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.IS_DEFAULT
     *
     * @return the value of T_TRADER_CONTACT.IS_DEFAULT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getIsDefault() {
        return isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.IS_DEFAULT
     *
     * @param isDefault the value for T_TRADER_CONTACT.IS_DEFAULT
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.BIRTHDAY
     *
     * @return the value of T_TRADER_CONTACT.BIRTHDAY
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Date getBirthday() {
        return birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.BIRTHDAY
     *
     * @param birthday the value for T_TRADER_CONTACT.BIRTHDAY
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.IS_MARRIED
     *
     * @return the value of T_TRADER_CONTACT.IS_MARRIED
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Byte getIsMarried() {
        return isMarried;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.IS_MARRIED
     *
     * @param isMarried the value for T_TRADER_CONTACT.IS_MARRIED
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setIsMarried(Byte isMarried) {
        this.isMarried = isMarried;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.HAVE_CHILDREN
     *
     * @return the value of T_TRADER_CONTACT.HAVE_CHILDREN
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Byte getHaveChildren() {
        return haveChildren;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.HAVE_CHILDREN
     *
     * @param haveChildren the value for T_TRADER_CONTACT.HAVE_CHILDREN
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setHaveChildren(Byte haveChildren) {
        this.haveChildren = haveChildren;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.EDUCATION
     *
     * @return the value of T_TRADER_CONTACT.EDUCATION
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getEducation() {
        return education;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.EDUCATION
     *
     * @param education the value for T_TRADER_CONTACT.EDUCATION
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setEducation(Integer education) {
        this.education = education;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.CHARACTER
     *
     * @return the value of T_TRADER_CONTACT.CHARACTER
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getCharacter() {
        return character;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.CHARACTER
     *
     * @param character the value for T_TRADER_CONTACT.CHARACTER
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setCharacter(String character) {
        this.character = character;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.COMMENTS
     *
     * @return the value of T_TRADER_CONTACT.COMMENTS
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public String getComments() {
        return comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.COMMENTS
     *
     * @param comments the value for T_TRADER_CONTACT.COMMENTS
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setComments(String comments) {
        this.comments = comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.ADD_TIME
     *
     * @return the value of T_TRADER_CONTACT.ADD_TIME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.ADD_TIME
     *
     * @param addTime the value for T_TRADER_CONTACT.ADD_TIME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.CREATOR
     *
     * @return the value of T_TRADER_CONTACT.CREATOR
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.CREATOR
     *
     * @param creator the value for T_TRADER_CONTACT.CREATOR
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.MOD_TIME
     *
     * @return the value of T_TRADER_CONTACT.MOD_TIME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.MOD_TIME
     *
     * @param modTime the value for T_TRADER_CONTACT.MOD_TIME
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_TRADER_CONTACT.UPDATER
     *
     * @return the value of T_TRADER_CONTACT.UPDATER
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_TRADER_CONTACT.UPDATER
     *
     * @param updater the value for T_TRADER_CONTACT.UPDATER
     *
     * @mbg.generated Sat Apr 20 17:16:41 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}