package com.vedeng.erp.aftersale.service;

import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDetailDto;

import java.math.BigDecimal;

/**
 * 采购费用单 api方法
 */
public interface BuyorderExpenseItemApiService {
    /**
     * 获取采购费用单详情
     * @param buyorderExpenseDetailId
     * @return
     */
    BuyorderExpenseItemDetailDto getBuyorderExpenseDetails(Integer buyorderExpenseDetailId);

    /**
     * 获取采购费用单
     * @param buyorderExpenseItemId
     * @return
     */
    BuyorderExpenseDto getBuyOrderExpense(Integer buyorderExpenseItemId);

    /**
     * 获取采购费用单
     * @param id
     * @return
     */
    BuyorderExpenseDto getBuyOrderExpenseById(Integer id);

    /**
     * 根据关联业务单号和订单商品id 查询商品价格
     * @param orderNo
     * @param expenseItemId
     * @return
     */
    BigDecimal getBuyOrderExpenseByOrderNoAndBuyorderExpenseItemId(String orderNo, Integer expenseItemId);
}
