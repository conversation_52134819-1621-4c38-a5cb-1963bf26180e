package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.domain.entity.ThirdRequestLog;
import com.vedeng.erp.system.dto.ThirdRequestLogDto;
import com.vedeng.erp.system.mapper.ThirdRequestLogMapper;
import com.vedeng.erp.system.mapstruct.ThirdRequestLogConvertor;
import com.vedeng.erp.system.service.ThirdRequestLogApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/16 9:45
 */
@Service
public class ThirdRequestLogServiceImpl implements ThirdRequestLogApiService {

    @Autowired
    private ThirdRequestLogMapper thirdRequestLogMapper;

    @Autowired
    private ThirdRequestLogConvertor thirdRequestLogConvertor;

    @Override
    public void saveLog(ThirdRequestLogDto insert) {
        ThirdRequestLog thirdRequestLog = thirdRequestLogConvertor.toEntity(insert);
        thirdRequestLogMapper.insertSelective(thirdRequestLog);
    }

    @Override
    public List<ThirdRequestLogDto> getRetryRequest(Integer retryStatus, Integer retryTimes) {
        List<ThirdRequestLog> thirdRequestLogList = thirdRequestLogMapper.getRetryRequest(retryStatus, retryTimes);
        return thirdRequestLogConvertor.toDto(thirdRequestLogList);
    }

    @Override
    public void updateLog(ThirdRequestLogDto update) {
        thirdRequestLogMapper.updateByPrimaryKeySelective(thirdRequestLogConvertor.toEntity(update));
    }
}