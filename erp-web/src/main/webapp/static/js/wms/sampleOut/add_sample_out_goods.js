function search() {
    checkLogin();
    clearErroeMes();//清除錯誤提示信息
    debugger
    if (($("#searchContent").val() == undefined || $("#searchContent").val() == "")
        && ($("#zxfbrandName").val() == undefined || $("#zxfbrandName").val() == "")
        && ($("#zxfTs").val() == undefined || $("#zxfTs").val() == "")
        && ($("select[name=unitName]").val() == "")) {
        warnTips("errorMes", "查询条件不能为空");//文本框ID和提示用语
        //$("#searchContent").addClass("errorbor");
        return false;
    }
    $("#search").submit();
}

search();

function selectSampleGoods(callbackFunction, goodsId, sku, goodsName, brandName, model, unitName, goodsLevelName, verifyStatus, stockNum, availableStockNum, price, purchasePrice) {
    checkLogin();
    //如果有回调函数就调用
    if (typeof (stockNum) == "undefined") {
        stockNum = 0
    }
    debugger
    eval("window.parent." + "setGoodData" + "('" + sku + "','" + goodsName + "','" + brandName + "','" + model + "','" + unitName + "','" + stockNum + "','" + availableStockNum + "','" + price + "','" + purchasePrice + "')");
    $("#close-layer").click();
    return;
}


function againSearch() {
    checkLogin();
    //$(".formpublic").find("input[type=text][type=hidden]").val("");
    $(".formpublic").find("input[type=text]").val("");
    $("#confirmTotalMoney").html("");
    $("#goodsListDiv").show();
    $("#confirmGoodsDiv").hide();
}

$(function () {
    $('#zxfunitName').select2();
    // $('.J-search-btn').click(function(){
    // 	if(!$.trim($(".J-searchContent").val())){
    // 		$('.J-error').show();//文本框ID和提示用语
    // 		return false;
    // 	}
    // 	var searchURL = '/wms/receiveOut/addReceiveOutGoods.do';
    //
    // /*	$("#search").submit();*/
    //
    // 	var link = page_url + searchURL + '?searchContent=' + $('.J-searchContent').val() + '&saleorderId=0';
    // 	window.location.href = link;
    // })

    $('.J-searchContent').on('keyup', function (e) {
        if (e.keyCode == 13 || e.keyCode == 108) {
            e.preventDefault();
            $('.J-search-btn').click();
            return false;
        }
    })

    $('.J-add_saleorder-goods-search').submit(function (e) {
        e.preventDefault();
        return false;
    })
    if ($("#zxfunitNameValue").val()) {
        $("#zxfunitName").select2().val($("#zxfunitNameValue").val()).trigger("change");
        $(".select2-container-multi").css("width", "100%");
    }
})
