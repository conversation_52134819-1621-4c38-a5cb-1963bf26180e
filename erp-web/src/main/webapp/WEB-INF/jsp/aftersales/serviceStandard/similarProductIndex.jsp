<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="参考同类商品" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<div class="main-container">
    <div class="list-pages-search">
        <form method="post" id="search" action="<%=basePath%>aftersale/serviceStandard/referenceSimilarProductList.do">
            <span style="margin-left: 42px">同类商品安装费:${installFeeMap.minInstallFee}-${installFeeMap.maxInstallFee}</span>
            </br>
            </br>
            <ul>
                <input type="hidden" name="skuNo" value="${queryDto.skuNo}">
                <li>
                    <label class="infor_name">商品名称</label>
                    <input type="text" class="input-small" placeholder="请输入订货号/商品名称" name="keyWord" id="keyWord" value="${queryDto.keyWord}"/>
                </li>

                <li>
                    <label class="infor_name">品牌</label>

                    <select  class="input-small" name='brandId' id="brandId">
                        <option value=''>请选择品牌</option>
                        <c:forEach items="${brandList}" var="brand">
                            <option value="${brand.brandId}" <c:if test="${brand.brandId == queryDto.brandId}">selected="selected"</c:if>>${brand.brandName}</option>
                        </c:forEach>
                    </select>
                </li>

                <li>
                    <label class="infor_name">制造商型号</label>
                    <input type="text" class="input-small" placeholder="请输入型号" name="spec" id="spec" value="${queryDto.spec}"/>
                </li>

            </ul>
            <div class="tcenter">

                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">查询</span>

                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>

            </div>
        </form>
    </div>
    <div class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
                <tr>
                    <th class="wid6">订货号</th>
                    <th class="wid6">商品名称</th>
                    <th class="wid6">品牌</th>
                    <th class="wid6">规格/型号</th>
                    <th class="wid6">安装费</th>
                    <th class="wid6">审核通过时间</th>
                </tr>
            </thead>
            <tbody>
            <c:forEach var="similarProduct" items="${similarProductList}"  varStatus="num">
                <tr>
                    <td>${similarProduct.skuNo}</td>
                    <td>
                        ${similarProduct.showName}
                    </td>
                    <td>${similarProduct.brandName}</td>
                    <td>
                        ${similarProduct.spec}
                    </td>

                    <td>${similarProduct.installPolicyInstallFee}</td>
                    <td>${similarProduct.latestVerifyPassTime}</td>
                </tr>
            </c:forEach>
                <c:if test="${empty similarProductList}">
                    <tr>
                        <td colspan='5'>查询无结果！请尝试使用其它搜索条件。</td>
                    </tr>
                </c:if>
            </tbody>
        </table>
    </div>
    <tags:page page="${page}" />
</div>

<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/price/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
