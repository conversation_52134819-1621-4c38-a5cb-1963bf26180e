package com.vedeng.docSync.model.pojo.generate;

import java.io.Serializable;
import lombok.Data;

/**
 * T_DOC_R_BUZ_TAG_J_FILE
 * <AUTHOR>
@Data
public class DocRBuzTagJFileDo implements Serializable {
    private Integer id;

    /**
     * 文件ID
     */
    private Integer fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 业务类型，1：商品资料，2：厂商资质
     */
    private Integer buzType;

    /**
     * 业务主键
     */
    private Integer buzId;

    /**
     * 业务标签ID
     */
    private Integer buzTagId;

    /**
     * 效期开始时间
     */
    private Long validStartTime;

    /**
     * 效期截止时间
     */
    private Long validEndTime;

    /**
     * 是否含章，0不含章，1含章
     */
    private Integer hasStamp;

    /**
     * 外部跳转链接
     */
    private String externalUrl;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;
}