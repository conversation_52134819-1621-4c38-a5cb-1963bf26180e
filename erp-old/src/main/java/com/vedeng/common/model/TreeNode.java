package com.vedeng.common.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:  树形结构基础数据类
 * @Author:       davis
 * @Date:         2021/4/14 下午5:13
 * @Version:      1.0
 */
@Data
public class TreeNode {

    protected Integer id;

    protected Integer parentId;

    List<TreeNode> child = new ArrayList<>();

    public void add(TreeNode node){
        child.add(node);
    }
}
