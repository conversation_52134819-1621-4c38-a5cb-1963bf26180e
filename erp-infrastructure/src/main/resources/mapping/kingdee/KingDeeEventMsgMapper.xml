<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.infrastructure.kingdee.mapper.KingDeeEventMsgMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.infrastructure.kingdee.domain.entity.KingDeeEventMsgEntity">
        <!--@mbg.generated-->
        <!--@Table KING_DEE_EVENT_MSG-->
        <id column="KING_DEE_EVENT_MSG_ID" jdbcType="INTEGER" property="kingDeeEventMsgId" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
        <result column="BODY"
                property="body"
                typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
        <result column="MESSAGE_STATUS" jdbcType="VARCHAR" property="messageStatus" />
        <result column="EVENT_TYPE" jdbcType="VARCHAR" property="eventType" />
        <result column="CLASS_PATH" jdbcType="VARCHAR" property="classPath" />
        <result column="MSG_ORDER" jdbcType="INTEGER" property="msgOrder" />
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
        <result column="BUSINESS_ID" jdbcType="VARCHAR" property="businessId" />
        <result column="RETRY_NUM" jdbcType="INTEGER" property="retryNum" />
        <result column="SEND_MSG" jdbcType="INTEGER" property="sendMsg" />
        <result column="ERROR_MSG" jdbcType="LONGVARCHAR" property="errorMsg" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        KING_DEE_EVENT_MSG_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
        BODY, MESSAGE_STATUS, EVENT_TYPE, CLASS_PATH, MSG_ORDER, IS_DELETE, BUSINESS_ID,
        RETRY_NUM, SEND_MSG, ERROR_MSG
    </sql>


    <insert id="insertSelective" keyColumn="KING_DEE_EVENT_MSG_ID" keyProperty="kingDeeEventMsgId" parameterType="com.vedeng.infrastructure.kingdee.domain.entity.KingDeeEventMsgEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into KING_DEE_EVENT_MSG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
            <if test="body != null">
                BODY,
            </if>
            <if test="messageStatus != null">
                MESSAGE_STATUS,
            </if>
            <if test="eventType != null">
                EVENT_TYPE,
            </if>
            <if test="classPath != null">
                CLASS_PATH,
            </if>
            <if test="msgOrder != null">
                MSG_ORDER,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="businessId != null">
                BUSINESS_ID,
            </if>
            <if test="retryNum != null">
                RETRY_NUM,
            </if>
            <if test="sendMsg != null">
                SEND_MSG,
            </if>
            <if test="errorMsg != null">
                ERROR_MSG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="body != null">
                #{body,jdbcType=ARRAY,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler},
            </if>
            <if test="messageStatus != null and messageStatus != ''">
                #{messageStatus,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null and eventType != ''">
                #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="classPath != null and classPath != ''">
                #{classPath,jdbcType=VARCHAR},
            </if>
            <if test="msgOrder != null">
                #{msgOrder,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="retryNum != null">
                #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="sendMsg != null">
                #{sendMsg,jdbcType=INTEGER},
            </if>
            <if test="errorMsg != null">
                #{errorMsg,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertIntoBackUpTable">
        insert into KING_DEE_EVENT_MSG_BACKUPS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            BACK_UP_TIME,
            <if test="kingDeeEventMsgId != null">
                KING_DEE_EVENT_MSG_ID,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
            <if test="body != null">
                BODY,
            </if>
            <if test="messageStatus != null">
                MESSAGE_STATUS,
            </if>
            <if test="eventType != null">
                EVENT_TYPE,
            </if>
            <if test="classPath != null">
                CLASS_PATH,
            </if>
            <if test="msgOrder != null">
                MSG_ORDER,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="businessId != null">
                BUSINESS_ID,
            </if>
            <if test="retryNum != null">
                RETRY_NUM,
            </if>
            <if test="sendMsg != null">
                SEND_MSG,
            </if>
            <if test="errorMsg != null">
                ERROR_MSG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            NOW(),
            <if test="kingDeeEventMsgId != null">
                #{kingDeeEventMsgId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="body != null">
                #{body,jdbcType=ARRAY,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler},
            </if>
            <if test="messageStatus != null and messageStatus != ''">
                #{messageStatus,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null and eventType != ''">
                #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="classPath != null and classPath != ''">
                #{classPath,jdbcType=VARCHAR},
            </if>
            <if test="msgOrder != null">
                #{msgOrder,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="retryNum != null">
                #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="sendMsg != null">
                #{sendMsg,jdbcType=INTEGER},
            </if>
            <if test="errorMsg != null">
                #{errorMsg,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.infrastructure.kingdee.domain.entity.KingDeeEventMsgEntity">
        <!--@mbg.generated-->
        update KING_DEE_EVENT_MSG
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="body != null">
                BODY = #{body,jdbcType=ARRAY,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler},
            </if>
            <if test="messageStatus != null and messageStatus != ''">
                MESSAGE_STATUS = #{messageStatus,jdbcType=VARCHAR},
            </if>
            <if test="eventType != null and eventType != ''">
                EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="classPath != null and classPath != ''">
                CLASS_PATH = #{classPath,jdbcType=VARCHAR},
            </if>
            <if test="msgOrder != null">
                MSG_ORDER = #{msgOrder,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                BUSINESS_ID = #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="retryNum != null">
                RETRY_NUM = #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="sendMsg != null">
                SEND_MSG = #{sendMsg,jdbcType=INTEGER},
            </if>
            <if test="errorMsg != null">
                ERROR_MSG = #{errorMsg,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where KING_DEE_EVENT_MSG_ID = #{kingDeeEventMsgId,jdbcType=INTEGER}
    </update>

    <update id="updateNotHandleByIds" parameterType="java.util.List">
        <!--@mbg.generated-->
        update KING_DEE_EVENT_MSG
        set MESSAGE_STATUS = 4
        where KING_DEE_EVENT_MSG_ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.kingDeeEventMsgId,jdbcType=INTEGER}
        </foreach>
        and  MESSAGE_STATUS in (1,3)
    </update>
    <delete id="deleteByPrimaryKey">
        delete from KING_DEE_EVENT_MSG
        where KING_DEE_EVENT_MSG_ID = #{kingDeeEventMsgId,jdbcType=INTEGER}
    </delete>

    <!--auto generated by MybatisCodeHelper on 2022-09-09-->
    <select id="findByMessageStatus" resultMap="BaseResultMap">
        select
        KING_DEE_EVENT_MSG_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
        BODY, MESSAGE_STATUS, EVENT_TYPE, CLASS_PATH, MSG_ORDER, IS_DELETE, BUSINESS_ID,
        RETRY_NUM, SEND_MSG
        from KING_DEE_EVENT_MSG
        where MESSAGE_STATUS = #{messageStatus,jdbcType=VARCHAR}
        and IS_DELETE = 0
        <if test="retryNum != null">
            and RETRY_NUM  <![CDATA[<]]>  #{retryNum,jdbcType=INTEGER}
        </if>
        <if test="msgId != null">
            and KING_DEE_EVENT_MSG_ID = #{msgId,jdbcType=INTEGER}
        </if>
        <if test="eventTypeList != null">
            and EVENT_TYPE in
            <foreach close=")" collection="eventTypeList" item="item" open="(" separator=", ">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>

        order by MSG_ORDER asc
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-11-28-->
    <select id="findAllByBusinessIdAndEventType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from KING_DEE_EVENT_MSG
        <where>
            BUSINESS_ID = #{businessId}
              and EVENT_TYPE = #{eventType,jdbcType=VARCHAR}
              and IS_DELETE = 0
            and (MESSAGE_STATUS = 1 or  MESSAGE_STATUS =3)
        </where>
        order by MSG_ORDER asc
    </select>

    <select id="findLtByBusinessIdAndEventType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from KING_DEE_EVENT_MSG
        <where>
            BUSINESS_ID = #{businessId}
            and EVENT_TYPE = #{eventType,jdbcType=VARCHAR}
            and IS_DELETE = 0
            and (MESSAGE_STATUS = 1 or  MESSAGE_STATUS =3)
            and KING_DEE_EVENT_MSG_ID  <![CDATA[ < #{msgId,javaType=INTEGER}]]>
        </where>
        order by MSG_ORDER asc
    </select>

    <update id="deleteEventMsg" >
        <!--@mbg.generated-->
        update KING_DEE_EVENT_MSG
        set IS_DELETE = 1,MOD_TIME = NOW(), UPDATER = #{userId,jdbcType=INTEGER}
        where KING_DEE_EVENT_MSG_ID = #{kingDeeEventMsgId,jdbcType=INTEGER}
    </update>

    <update id="addRetryNum">
        update KING_DEE_EVENT_MSG
        set RETRY_NUM = ifnull(RETRY_NUM,0) + 1
        where KING_DEE_EVENT_MSG_ID = #{kingDeeEventMsgId,jdbcType=INTEGER}
    </update>

    <select id="findNeedSendMsg" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from KING_DEE_EVENT_MSG
        where MESSAGE_STATUS = #{messageStatus,jdbcType=VARCHAR}
        and RETRY_NUM >= #{retryNum,jdbcType=INTEGER}
        <if test="openSend">
            and SEND_MSG = 0
        </if>
        and IS_DELETE = 0
        order by MSG_ORDER asc
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from KING_DEE_EVENT_MSG
        where KING_DEE_EVENT_MSG_ID = #{kingDeeEventMsgId,jdbcType=INTEGER}
    </select>
</mapper>