package com.newtask;

import com.newtask.util.CertificateImage2OssUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.trader.dao.TraderCertificateMapper;
import com.vedeng.trader.model.TraderCertificate;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/2/28 9:50
 */
@JobHandler(value = "traderCertificate2OssHandler")
@Component
public class TraderCertificate2OssTask extends AbstractJobHandler {

    public static Logger logger = LoggerFactory.getLogger(TraderCertificate2OssTask.class);

    @Autowired
    private TraderCertificateMapper traderCertificateMapper;

    @Autowired
    private CertificateImage2OssUtil certificateImage2OssUtil;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {

        //s = offset,size
        int start = 0;
        int size = 10;

        if (StringUtils.isNotBlank(s)){
            String[] array = s.split(",");
            start = Integer.parseInt(array[0]);
            if (array.length > 1){
                size = Integer.parseInt(array[1]);
            }
        }

        if (StringUtils.isNotBlank(s) && s.split(",").length == 1) {
            TraderCertificate certificate = traderCertificateMapper.getTraderCertificateById(start);
            if (certificate != null) {
                certificateImage2OssUtil.downloadFileByStream(certificate);
            }
        } else {
            //分页批量迁移
            int count = 1;
            while (count > 0){
                List<TraderCertificate> certificates = traderCertificateMapper.getTraderCertificates(start,size);
                count = certificates.size();
                for (int i = 0; i < certificates.size(); i++) {
                    certificateImage2OssUtil.downloadFileByStream(certificates.get(i));
                    if (i == certificates.size() - 1){
                        start = certificates.get(i).getTraderCertificateId();
                    }
                }
            }
        }
        return null;
    }

}
