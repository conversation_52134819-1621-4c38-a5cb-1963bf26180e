package com.vedeng.erp.aftersale.domain.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 推送前台用户端售后状态
 */
@Data
@Builder
public class PushAfterSalesClientStatusDto {
    /**
    * 售后单号
    */
    private String afterSaleOrderNo;

    /**
    * 流程类型【4：下派工程师；5：工程师完成预约；6：工程师完工】
    */
    private Integer flowType;

    /**
    * 流程状态说明
    */
    private String flowComment;

}