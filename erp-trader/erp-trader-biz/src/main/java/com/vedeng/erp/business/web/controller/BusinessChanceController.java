package com.vedeng.erp.business.web.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 商机页面跳转层
 * @date 2022/7/14 10:41
 **/
@Controller
@RequestMapping("/businessChance")
@Slf4j
public class BusinessChanceController extends BaseController {

    @Autowired
    UserService userService;
    //科研购事业部
    private static final Integer SCIENCE_ORGID = 36;
    //科研购所属COMPANY_ID
    private static final Integer SCIENCE_COMPANY_ID = 1;
    /**
     * 商机列表
     * @return 页面
     */
    @RequestMapping(value = "/index")
    public String index() {
        return view();
    }

    /**
     * 商机列表（销售工作台/待办事项/未处理商机 跳转）
     * @return 商机首页
     */
    @RequestMapping(value = "/indexFromWorkbenchUnHandle")
    @ExcludeAuthorization
    public ModelAndView indexFromWorkbenchUnHandle() {
        ModelAndView mv = new ModelAndView("vue/view/businesschance/index");
        //4131无沟通记录
        mv.addObject("communicateState", 4131);
        return mv;
    }

    /**
     * 商机列表（销售工作台/待办事项/待再次处理商机 跳转）
     * @return 商机首页
     */
    @RequestMapping(value = "/indexFromWorkbenchWaitCommunicate")
    @ExcludeAuthorization
    public ModelAndView indexFromWorkbenchWaitCommunicate() {
        ModelAndView mv = new ModelAndView("vue/view/businesschance/index");
        //4130需下次沟通
        mv.addObject("communicateState", 4130);
        return mv;
    }

    /**
     * 商机详情
     *
     * 详情跳转接口权限放开注解，需求为站内信跳转到新链接，站内信url改为新商机详情链接，因为在新链接站内信跳转阶段，跳转层外非B2B销售没有权限
     * 所以改为接口可访问 在跳转层controller中做了非B2B销售的权限区分转发
     * 接口可访问，内部做了权限区分 VDERP-11856
     * @param id
     * @return
     */
    @RequestMapping(value = "/details")
    @ExcludeAuthorization
    public ModelAndView detail(Integer id, HttpServletRequest request) {
//        User sessionUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
//        //判断是否是B2B的销售
//        Boolean saleAndB2BFlagByUserId = userService.getSaleAndB2BFlagByUserId(sessionUser);
//        if (saleAndB2BFlagByUserId){
//            //判断是否是科研购事业部的
//            Boolean belongPlatfromByOrgAndUser = userService.getBelongPlatfromByOrgAndUser(sessionUser, SCIENCE_ORGID, SCIENCE_COMPANY_ID);
//            ModelAndView mv = new ModelAndView("vue/view/businesschance/details");
//            mv.addObject("belongPlatfromByOrgAndUser", belongPlatfromByOrgAndUser ? 1 : 0);
//            mv.addObject("bussinessChanceId", id);
//            log.info("B2B销售用户，跳转至B2B商机详情页面:mv={}", JSON.toJSON(mv));
//            return mv;
//        }
//        log.info("非B2B销售用户，跳转至B2B商机详情页面:bussinessChanceId={}", id);
//        return new ModelAndView("redirect:/order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=" + id);

        return new ModelAndView("redirect:"+lxcrmUrl +"/crm/businessChance/profile/detail?id="+id);
    }

    /**
     * 编辑商机跳转路由
     * @param businessChanceId 商机id
     * @return 页面
     */
    @RequestMapping(value = "/updateView")
    public ModelAndView updateView(Integer businessChanceId) {
        ModelAndView mv = new ModelAndView("vue/view/businesschance/edit");
        mv.addObject("creatername", CurrentUser.getCurrentUser().getUsername());
        mv.addObject("creater",CurrentUser.getCurrentUser().getId());
        mv.addObject("businessChanceId", businessChanceId);
        return mv;
    }

    /**
     * 线索转商机
     * @param businessLeadsId 线索id
     * @return 页面
     */
    @RequestMapping(value = "/leadsConvertor")
    public ModelAndView leadsConvertor(Integer businessLeadsId) {
        ModelAndView mv = new ModelAndView("vue/view/businesschance/edit");
        mv.addObject("creatername", CurrentUser.getCurrentUser().getUsername());
        mv.addObject("creater",CurrentUser.getCurrentUser().getId());
        mv.addObject("businessLeadsId", businessLeadsId);
        return mv;
    }


    /**
     * 新增页商机跳转路由
     * @return 页面
     */
    @RequestMapping(value = "/edit")
    public ModelAndView edit(@RequestParam(required = false) Integer traderId) {
        ModelAndView mv = new ModelAndView("vue/view/businesschance/edit");
        mv.addObject("traderId", traderId);
        return mv;
    }

    /**
     * ai通话助手跳转新增商机
     * @return 页面
     */
    @RequestMapping(value = "/aiAssistantToEdit")
    @NoNeedAccessAuthorization
    public ModelAndView aiAssistantToEdit(@RequestParam(value = "isAiAssistant") Integer isAiAssistant,
                                          @RequestParam(value = "aiCommunicateRecordId") Integer aiCommunicateRecordId) {
        ModelAndView mv = new ModelAndView("vue/view/businesschance/edit");
        mv.addObject("isAiAssistant", isAiAssistant);
        mv.addObject("aiCommunicateRecordId", aiCommunicateRecordId);
        return mv;
    }

    /**
     * 咨询待处理跳转页面
     * @return 页面
     */
    @RequestMapping(value = "/consultationPendingSkip")
    @NoNeedAccessAuthorization
    public ModelAndView consultationPendingSkip(@RequestParam(required = false) String leadsNo,
                                                @RequestParam(required = false) String from,
                                                @RequestParam(required = false) Integer status) {
        ModelAndView modelAndView = new ModelAndView("vue/view/businesschance/consultationPending");
        modelAndView.addObject("leadsNo", StringUtils.isNotBlank(leadsNo) ? leadsNo : "");
        modelAndView.addObject("from",StringUtils.isNotBlank(from) ? from : "");
        modelAndView.addObject("status", Objects.nonNull(status) ? status : -1);
        return modelAndView;
    }


    
}
