package com.vedeng.erp.aftersale.service.impl;

import com.vedeng.order.service.ReturnVisitRecordService;
import com.vedeng.orderstream.aftersales.dao.ReturnVisitRecordMapper;
import com.vedeng.orderstream.aftersales.model.ReturnVisitRecord;
import com.vedeng.orderstream.aftersales.model.dto.ReturnVisitRecordDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.service.Impl
 * @Date 2021/11/29 13:07
 */
@Service
public class ReturnVisitRecordServiceImpl implements ReturnVisitRecordService {

    @Resource
    private ReturnVisitRecordMapper returnVisitRecordMapper;

    @Override
    public Integer insertReturnVisitRecord(ReturnVisitRecord returnVisitRecord) {
        return returnVisitRecordMapper.insertSelective(returnVisitRecord);
    }

    @Override
    public List<ReturnVisitRecordDto> getReturnVisitRecordListByAfterSaleId(Integer afterSaleId) {
        return returnVisitRecordMapper.getReturnVisitRecordListByAfterSaleId(afterSaleId);
    }
}
