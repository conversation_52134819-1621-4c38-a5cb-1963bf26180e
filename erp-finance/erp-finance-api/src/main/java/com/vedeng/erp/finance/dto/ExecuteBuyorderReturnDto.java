package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExecuteBuyorderReturnDto {
    /**
     * 账期总金额
     */
    private BigDecimal debtPeriodMoney;

    private String traderName;

    /**
     * 采购订单返利支付总额
     */
    private BigDecimal buyOrderSettleAmount;

    /**
     * 本次售后结算金额
     */
    private BigDecimal currentAfterSalesSettleAmount;

    public ExecuteBuyorderReturnDto(){
        this.buyOrderSettleAmount = BigDecimal.ZERO;
        this.currentAfterSalesSettleAmount  = BigDecimal.ZERO;
    }

}
