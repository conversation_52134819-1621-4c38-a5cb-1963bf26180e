<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.ConfirmationFormRecodeMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.ConfirmationFormRecode">
        <id property="id" column="ID" jdbcType="INTEGER"/>
        <result property="confirmationName" column="CONFIRMATION_NAME" jdbcType="VARCHAR"/>
        <result property="confirmationType" column="CONFIRMATION_TYPE" jdbcType="TINYINT"/>
        <result property="fileId" column="FILE_ID" jdbcType="VARCHAR"/>
        <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
        <result property="comments" column="COMMENTS" jdbcType="VARCHAR"/>
        <result property="saleOrderId" column="SALEORDER_ID" jdbcType="INTEGER"/>
        <result property="isEnable" column="IS_ENABLE" jdbcType="TINYINT"/>
        <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
        <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
        <result property="modTime" column="MOD_TIME" jdbcType="BIGINT"/>
        <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,CONFIRMATION_NAME,CONFIRMATION_TYPE,
        FILE_ID,FILE_NAME,COMMENTS,SALEORDER_ID,
        IS_ENABLE,ADD_TIME,CREATOR,
        MOD_TIME,UPDATER
    </sql>
    <update id="updateByPrimaryKey"  parameterType="com.vedeng.logistics.model.ConfirmationFormRecode">
        update T_CONFIRMATION_FORM_RECODE
        <set >
            <if test="confirmationName != null" >
                CONFIRMATION_NAME = #{confirmationName,jdbcType=VARCHAR},
            </if>
            <if test="confirmationType != null" >
                CONFIRMATION_TYPE = #{confirmationType,jdbcType=BIT},
            </if>
            <if test="fileId != null" >
                FILE_ID = #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null" >
                FILE_NAME = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="comments != null" >
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="isEnable != null" >
                IS_ENABLE = #{isEnable,jdbcType=BIT},
            </if>
            <if test="addTime != null" >
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creator != null" >
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="saleOrderId != null" >
                SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectAllByIds"  resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_CONFIRMATION_FORM_RECODE a
        WHERE 1=1
          AND a.IS_ENABLE = 0
        <if test="ids !=null and ids.size()>0">
            and a.ID in
            <foreach collection="ids" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectByPrimaryKey" resultType="com.vedeng.logistics.model.ConfirmationFormRecode" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List" />
        from T_CONFIRMATION_FORM_RECODE a
        where a.ID = #{confirmationId,jdbcType=BIGINT}
        AND a.IS_ENABLE = 0
    </select>



    <insert id="insert" parameterType="com.vedeng.logistics.model.ConfirmationFormRecode">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_CONFIRMATION_FORM_RECODE (CONFIRMATION_NAME, CONFIRMATION_TYPE, FILE_ID,
        FILE_NAME, COMMENTS, SALEORDER_ID,
        ADD_TIME, CREATOR, MOD_TIME,
        UPDATER, IS_ENABLE)
        values (#{confirmationName,jdbcType=VARCHAR}, #{confirmationType,jdbcType=BIT}, #{fileId,jdbcType=VARCHAR},
        #{fileName,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{saleOrderId,jdbcType=INTEGER},
        #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT},
        #{updater,jdbcType=INTEGER}, #{isEnable,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" parameterType="com.vedeng.logistics.model.ConfirmationFormRecode">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_CONFIRMATION_FORM_RECODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="confirmationName != null">
                CONFIRMATION_NAME,
            </if>
            <if test="confirmationType != null">
                CONFIRMATION_TYPE,
            </if>
            <if test="fileId != null">
                FILE_ID,
            </if>
            <if test="fileName != null">
                FILE_NAME,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="saleOrderId != null">
                SALEORDER_ID,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="isEnable != null">
                IS_ENABLE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="confirmationName != null">
                #{confirmationName,jdbcType=VARCHAR},
            </if>
            <if test="confirmationType != null">
                #{confirmationType,jdbcType=BIT},
            </if>
            <if test="fileId != null">
                #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="saleOrderId != null">
                #{saleOrderId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null">
                #{isEnable,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <select id="getConfirmationNameByBatchNo" resultType="java.lang.String">
        SELECT c.CONFIRMATION_NAME
        FROM T_CONFIRMATION_FORM_RECODE c
                 LEFT JOIN T_CONFIRMATION_BATCHES_RELATION b ON c.ID = b.CONFIRMATION_ID
        WHERE b.BATCH_NO = #{batchNo,jdbcType=VARCHAR}
          AND b.IS_ENABLE = 0
          AND c.IS_ENABLE = 0
    </select>
</mapper>
