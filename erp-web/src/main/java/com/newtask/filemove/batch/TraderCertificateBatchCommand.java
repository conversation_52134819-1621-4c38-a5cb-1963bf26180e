package com.newtask.filemove.batch;

import com.google.common.collect.Lists;
import com.newtask.filemove.AbstractFileMoveInterface;
import com.newtask.filemove.dto.CommonConstant;
import com.newtask.filemove.dto.FileMoveDto;
import com.newtask.filemove.theadpool.FileMoveThreadPool;
import com.newtask.filemove.theadpool.TraderCertificateFileMoveTask;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.trader.model.TraderCertificate;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 客户资质批处理命令
 */
@Service
public class TraderCertificateBatchCommand extends AbstractFileMoveInterface {

    private static final Logger LOGGER = LoggerFactory.getLogger(TraderCertificateBatchCommand.class);

    @Resource
    private FileMoveDao fileMoveDao;

    protected String getCommandType() {
        return CommonConstant.BATCH;
    }

    protected List<FileMoveDto> getFileMoveInfoByPageSize(int start, int pageSize) {

        List<TraderCertificate> traderCertificateList = fileMoveDao.getTraderCertificatesByPage(start,pageSize);

        if(CollectionUtils.isEmpty(traderCertificateList)){
            return null;
        }

        return Lists.transform(traderCertificateList, (entity) -> {
                                                            FileMoveDto fileMoveDto = new FileMoveDto();
                                                            fileMoveDto.setId(entity.getTraderCertificateId());
                                                            fileMoveDto.setDomain(entity.getDomain());
                                                            fileMoveDto.setUri(entity.getUri());
                                                            return fileMoveDto;
                                                        });
    }


    @Override
    protected ExecutorService getExecutorService() {
        return FileMoveThreadPool.getTraderCertificateThreadPool();
    }

    @Override
    protected Runnable getFileMoveTask(List<FileMoveDto> fileMoveDtoList) {
        return new TraderCertificateFileMoveTask(fileMoveDtoList);
    }
}
