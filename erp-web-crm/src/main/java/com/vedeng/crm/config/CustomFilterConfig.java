package com.vedeng.crm.config;

import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.security.CasClientConstants;
import com.vedeng.security.FilterDefinitionRegistry;
import org.apache.shiro.web.filter.mgt.DefaultFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: crm自定义拦截器
 * @date 2024/7/20 13:17
 */
@Configuration
public class CustomFilterConfig {

    @Bean
    public FilterDefinitionRegistry customFilterDefinitionRegistry() {
        CustomFilterDefinitionRegistry customFilterDefinitionRegistry = new CustomFilterDefinitionRegistry();
        customFilterDefinitionRegistry.addFilterDefinition("/crm/wx/*", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/noPermission", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/clientMustBeWeixin", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/404", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/500", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/*/public/**", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/task/profile/save", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/category/match", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/task/profile/autoHandle", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/task/profile/closeTaskForBusinessChance", DefaultFilter.anon.name());
        customFilterDefinitionRegistry.addFilterDefinition("/crm/other/profile/getContentForLoginLxcrm", DefaultFilter.anon.name());

        if (!ConfigService.getConfig(CasClientConstants.CAS_CLIENT_CONFIG_NAMESPACE).getBooleanProperty(CasClientConstants.SSO_ENABLED_PROP_NAME, false)) {
            customFilterDefinitionRegistry.addFilterDefinition("/crm/**", DefaultFilter.anon.name());
            customFilterDefinitionRegistry.addFilterDefinition("/crm/*/profile/**", DefaultFilter.anon.name());
        }
        return customFilterDefinitionRegistry;
    }

    //@Bean
    //public UsingSSOConfiguration usingSSOConfiguration(CasClientProperties casClientProperties, Config config) {
    //    return new UsingSSOConfiguration(casClientProperties, config) {
    //        @Override
    //        protected Map<String, Filter> getCustomFilterMap() {
    //            Map<String, Filter> filterMap = super.getCustomFilterMap();
    //            // 添加自定义的 Filter
    //            filterMap.put("customFilterName", null);
    //            return filterMap;
    //        }
    //    };
    //}
}
