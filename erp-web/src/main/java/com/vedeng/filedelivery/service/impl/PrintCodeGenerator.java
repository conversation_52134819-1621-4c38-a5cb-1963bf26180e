package com.vedeng.filedelivery.service.impl;

import java.util.Random;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.vedeng.filedelivery.model.FileDeliveryApolloCommon;

/**
 * 生成打印码，注意！！！ 该redis设置的过期时间在express应用中设置的过期
 * 切记：该key不可以修改！该key不可以修改！该key不可以修改！
 * 跟产品沟通，打印码回收后够用，无需考虑打印码使用完的情况
 * <AUTHOR>
 *
 */
@Service
public class PrintCodeGenerator {
	
	public static Logger logger = LoggerFactory.getLogger(PrintCodeGenerator.class);
	
	@Resource
    private RedissonClient redissonClient;
	
	@Value("${redis_dbtype}")
	private String redisDbtype;
	
	@Autowired
	private FileDeliveryApolloCommon fileDeliveryApolloCommon;

	//生成一个不重复的4位数字
    public String generateUniqueCode() {
    	// 存储已使用的 4 位数字
    	RMapCache<String, String> usedCodesMap = redissonClient.getMapCache(redisDbtype+"usedCodes");
        Random random = new Random();
        String code;
        Integer index = 1;
        do {
        	index++;
            // 生成一个 0000 到 9999 之间的随机数字
            code = String.format("%04d", random.nextInt(fileDeliveryApolloCommon.maxPrintCode));
        } while (StringUtils.isNotEmpty(usedCodesMap.get(code))  && index<fileDeliveryApolloCommon.maxPrintCode); // 如果已经使用过，则重新生成
        //打印码即将使用耗尽提示
        if(index>(fileDeliveryApolloCommon.tipsPrintCode)) {
        	logger.error("！！！面单打印码即将耗尽,总数：{}个，已使用：{}个！！！,请增大打印码范围,同时express应用需配合可输入对应位数打印码的情况！！！",fileDeliveryApolloCommon.maxPrintCode,fileDeliveryApolloCommon.tipsPrintCode);
        }
        //打印码已使用完
        if(index>=fileDeliveryApolloCommon.maxPrintCode) {
        	logger.error("！！！面单打印码已耗尽,总数：{}个",fileDeliveryApolloCommon.maxPrintCode);
        	return null;
        }
        // 将生成的代码加入已使用集合
        usedCodesMap.put(code, code);
        return code;
    }
}
