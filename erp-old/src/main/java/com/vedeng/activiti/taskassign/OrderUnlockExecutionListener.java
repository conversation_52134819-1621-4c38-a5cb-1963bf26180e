package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.StringUtil;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.service.listenner.GenerateAfterSalesLister;
import com.wms.service.util.GlobalThreadPool;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.listenner.PutAfterReturnAuditFinishLister;
import com.wms.service.listenner.PurchaseExgAuditFinishListener;
import com.wms.service.listenner.PurchaseReturnAuditFinishListenner;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

@Slf4j
public class OrderUnlockExecutionListener implements ExecutionListener {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private static final Logger LOGGER = LoggerFactory.getLogger(OrderUnlockExecutionListener.class);

	// 运行时注入service
	WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

	private ActionProcdefService actionProcdefService = (ActionProcdefService) context.getBean("actionProcdefService");

	//采购退货处理器
	private PurchaseReturnAuditFinishListenner purcharseReturnPassListenner = (PurchaseReturnAuditFinishListenner) context.getBean("purchaseReturnAuditFinishListenner");

    //采购换货处理器
    private PurchaseExgAuditFinishListener purchaseExgAuditFinishListener = (PurchaseExgAuditFinishListener) context.getBean("purchaseExgAuditFinishListener");

    //销售退货单处理器
	private PutAfterReturnAuditFinishLister putAfterReturnAuditFinishLister = (PutAfterReturnAuditFinishLister) context.getBean("putAfterReturnAuditFinishLister");

	//售后订单生成处理器
	private GenerateAfterSalesLister generateAfterSalesLister = (GenerateAfterSalesLister) context.getBean("generateAfterSalesLister");

	private WmsSendOrderMapper wmsSendOrderMapper = (WmsSendOrderMapper) context.getBean("wmsSendOrderMapper");

	private LogicalSaleorderChooseService logicalSaleorderChooseService =  (LogicalSaleorderChooseService) context.getBean("logicalSaleorderChooseService");

	@Override
	public void notify(DelegateExecution execution) throws Exception {
		String processKey = execution.getVariable("processDefinitionKey").toString();
		if (StringUtil.isNotBlank(processKey)) {
			Integer orderId = null;
			switch (processKey) {
			case "editSaleorderVerify":// 销售订单编辑审核
				orderId = Integer.parseInt(execution.getVariable("orderId").toString());
				break;
			case "saleorderModifyAudit":// 销售订单编辑审核
				orderId = Integer.parseInt(execution.getVariable("orderId").toString());
				break;
			case "afterSalesVerify":// 售后订单审核
				AfterSalesVo afterSales = (AfterSalesVo) execution.getVariable("afterSalesInfo");
				if (afterSales.getType().equals(543)) {
					orderId = afterSales.getOrderId();
				}

				//是否审核通过
				boolean auditPass = Boolean.valueOf(execution.getVariable("pass").toString());

				//是否是采购退货单
				boolean buyOrderReturn = afterSales.getSubjectType() == 536 && afterSales.getType() == 546;

                //是否是采购换货单
                boolean buyOrderExg = afterSales.getSubjectType() == 536 && afterSales.getType() == 547;

                //是否是销售退货单
				boolean saleOrderReturn = afterSales.getSubjectType().equals(535) && afterSales.getType().equals(539);

				//是否是销售换货单
				boolean saleOrderChange = afterSales.getSubjectType().equals(535) && afterSales.getType().equals(540);

				//采购单退货
				if(buyOrderReturn){
					GlobalThreadPool.submitMessage(new Runnable() {
						@Override
						public void run() {
							try{
								String uuid = UUID.randomUUID().toString().replace("-", "");
								log.info("开始执行采购单退货，uuid：{}",uuid);
								purcharseReturnPassListenner.onActionHappen(afterSales,auditPass,getUser());
								log.info("开始执行采购单退货，uuid：{}",uuid);
							}catch (Exception e){
								LOGGER.error("采购单退货监听器执行报错：",e);
							}
						}
					});
				}

				//采购换货单
                if(buyOrderExg){
                    GlobalThreadPool.submitMessage(new Runnable() {
                        @Override
                        public void run() {
                            try{
								String uuid = UUID.randomUUID().toString().replace("-", "");
								log.info("开始执行采购换货单，uuid：{}",uuid);
                                purchaseExgAuditFinishListener.onActionHappen(afterSales,auditPass,getUser());
								log.info("结束执行采购换货单，uuid：{}",uuid);
                            }catch (Exception e){
                                LOGGER.error("采购单换货监听器执行报错：",e);
                            }
                        }
                    });
                }
				//销售退货单
				if(saleOrderReturn){
					GlobalThreadPool.submitMessage(new Runnable() {
						@Override
						public void run() {
							try{
								String uuid = UUID.randomUUID().toString().replace("-", "");
								log.info("开始执行销售退货单,uuid:{}",uuid);
								putAfterReturnAuditFinishLister.onActionHappen(afterSales,auditPass,getUser());
								log.info("结束执行销售退货单,uuid:{}",uuid);

							}catch (Exception e){
								LOGGER.error("销售退货单监听器执行报错：",e);
							}
						}
					});
				}

				if (saleOrderReturn || saleOrderChange){
					GlobalThreadPool.submitMessage(new Runnable() {
						@Override
						public void run() {
							try {
								String uuid = UUID.randomUUID().toString().replace("-", "");
								log.info("开始执行退货单生成售后订单,uuid:{}",uuid);
								LOGGER.info("销售退换货生成售后订单 afterSales:{}", JSON.toJSONString(afterSales));
								generateAfterSalesLister.onActionHappen(afterSales, auditPass, getUser());
								log.info("结束执行退货单生成售后订单,uuid:{}",uuid);
							} catch (Exception e) {
								LOGGER.error("退货单生成售后订单错误", e);
							}
						}
					});
				}
				break;
			default:
				break;
			}
			if (orderId != null) {
				actionProcdefService.updateInfo("T_SALEORDER_GOODS", "SALEORDER_ID", orderId, "LOCKED_STATUS", 0, 2);
			}
		}
	}

	private User getUser(){
		User user = null;
		ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (ra != null) {
			HttpServletRequest request = ra.getRequest();
			if (request != null && request.getSession() != null) {
				user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			}
		}
		if(user==null){
			user= new User();
			user.setCompanyId(ErpConst.ONE);
			user.setUserId(ErpConst.TWO);
			user.setCompanyName("南京贝登医疗有限公司");
			user.setUsername("njadmin");
		}
		return user;
	}
}
