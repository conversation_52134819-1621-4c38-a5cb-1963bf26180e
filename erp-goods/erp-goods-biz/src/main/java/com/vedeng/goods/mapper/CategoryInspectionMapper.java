package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.CategoryInspection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【V_CATEGORY_INSPECTION(分类与检查项目的中间表)】的数据库操作Mapper
 * @createDate 2022-02-10 15:14:25
 */
public interface CategoryInspectionMapper {

    /**
     * 根据分类id 查询所有对应的检查项目关系表
     *
     * @param categoryId 分类id
     * @return List<CategoryInspection>
     */
    List<CategoryInspection> findByCategoryId(@Param("categoryId")Integer categoryId);

    int deleteByPrimaryKey(Integer id);

    int insert(CategoryInspection record);

    int insertSelective(CategoryInspection record);

    CategoryInspection selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CategoryInspection record);

    int updateByPrimaryKey(CategoryInspection record);

}




