<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">
<!-- 引入脚本 -->
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>

<div id="app">
    <el-form :rules="rules" ref="communicateRecordForm" :model="handleDto" :label-position="labelPosition">
        <el-form-item label="问题类型：" label-width="160px" prop="" style="padding-top: 25px">
            ${problemType}
        </el-form-item>
        <el-form-item label="反馈人：" label-width="160px" prop="">
            ${creatorLabel}
        </el-form-item>
        <div>
        <el-form-item label="处理答复：" label-width="160px" prop="messageReply">
            <el-input maxlength="50" show-word-limit type = "textarea" :rows="5" v-model="handleDto.messageReply" style = "width: 80%"></el-input>
        </el-form-item>
        </div>
    </el-form>
    <span style="display: block;text-align: center;">
        <el-button type="primary" @click="saveRegistrationFeedback" :disabled="disabledForm">确认处理并答复</el-button>
        <el-button @click="reInitCommun()">取 消</el-button>
    </span>
</div>

<script type="text/javascript">
    const viewInfo = {
        businessChanceId: '${bussinessChanceId}',
    };
    const registrationNumber = '${registrationNumber}'
    let vm0 = null
    const sendThis0 = (_this) => {
        vm0 = _this;
    };
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                handleDto: {
                    messageReply: '',
                    registrationFeedbackRecordId: ${registrationFeedbackRecordId},
                },
                rules: {
                    messageReply: [
                        { required: true, message: '请填写处理答复', trigger: 'change'}
                    ],
                },
                labelPosition: 'right',
                disabledForm: false,
            }},


        methods: {
            saveRegistrationFeedback() {
                this.$refs['communicateRecordForm'].validate((valid) => {
                    if (valid){
                        return axios({
                            url: '/registrationFeedback/messageReply.do',
                            method: 'post',
                            data: this.handleDto
                        }).then(res => {
                            if (res.data.code === 0){
                                this.reInitCommun();
                                parent.location.reload();
                            };
                            if (res.data.code === 999){
                                this.open();
                            }
                        })
                    }
                })
            },

            reInitCommun() {
                layer.closeAll();
                parent.layer.closeAll();
            },


            open() {
                this.$confirm('该问题已处理，请刷新重试', '', {
                    confirmButtonText: '确定',
                    type: 'error',
                    center: true,
                    showClose: false,
                    showCancelButton: false,
                    callback: action => {
                        this.reInitCommun();
                        parent.location.reload();
                    }
                });
            }
        }
    });
</script>

<style>
    .el-row {
        margin-bottom: 10px;
    }

    .el-descriptions {
        background-color: #e5f1ff;
        margin-bottom: 20px;
    }

    .el-descriptions__title {
        margin-left: 10px;
        margin-top: 10px;
    }

    .el-descriptions__header {
        margin-bottom: 10px;
    }

    .title-style {
        background-color: #e5f1ff;
        box-sizing: border-box;
        padding-left: 10px;
        font-size: 16px;
        font-weight: 700;
        color: #303133;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .cell {
        text-align: center;
    }

    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .my-content {
        width: 40%;
    }

    .el-tag{
        white-space: normal;
        height:auto;
    }


</style>