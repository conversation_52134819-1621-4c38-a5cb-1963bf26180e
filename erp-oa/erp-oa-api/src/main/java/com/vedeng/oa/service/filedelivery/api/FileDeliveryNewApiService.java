package com.vedeng.oa.service.filedelivery.api;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.vedeng.oa.service.filedelivery.vo.FileDeliveryAddressVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryUploadVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryVO;

public interface FileDeliveryNewApiService {
     String getConfig();
     FileDeliveryVO getDeliveryById(Integer fileDeliveryId);

     /**
	  * 新增一个文件寄送
	  * @param fileDeliveryNew
	  * @return
	  */
     FileDeliveryVO saveFileDeliveryNew(FileDeliveryVO fileDeliveryNew);

     /**
      * 更新一个文件寄送
      * @param fileDeliveryVO
     * @param userId
     * @param applyOrgName
      * @return
      */
	int updateFileDeliveryNew(FileDeliveryVO fileDeliveryVO, Integer userId, String applyOrgName);

    /**
     * 新增空的文件寄送
     * @return
     */
     FileDeliveryVO addEmptyDelivery();

    /**
     * 获取所有有效地址
     * @param fileDeliveryId
     * @return
     */
     List<FileDeliveryAddressVO> getDeliveryAdressList(Integer fileDeliveryId);

    /**
     * 设置为已寄送
     * @param fileDeliveryId
     * @param userId
     * @param username
     */
    void setDeliveryDelived(Integer fileDeliveryId, Integer userId, Integer logisticsName  );

    /**
     * 填充面单URL
     * 一共三个label
     * @param update
     */
    void fillExpressNoAndLabelUrlById(FileDeliveryAddressVO update);

     /**
      * 跟新状态
      * @param
      * @param userId
     * @param integer
      */
	void updateFileDeliveryVerifyStatus(Integer verifyStatus , Integer userId, Integer fileDeliveryId);

	/**
	 * 根据一个主键获取文件寄送详情
	 * @param fileDeliveryId
	 * @return
	 */
	FileDeliveryVO getFileDeliveryNew(Integer fileDeliveryId);
	
	/**
	 * 寄送单关闭
	 * @param i
	 * @param userId
	 * @param fileDeliveryId
	 */
	void updateFileDeliveryColseStatus(int closeStatus,String closeComment,Integer userId, Integer fileDeliveryId);
	
	/**
	 * 更新文件寄送主表中寄送时间等信息
	 * @param fileDeliveryId
	 * @param deliveryStatus 
	 * @param deliveryTime
	 * @param expressNosToSave
	 * @param userId
	 * @param logisticsType 
	 */
	void updateFileDeliveryDeliveryStatus(Integer fileDeliveryId, Integer deliveryStatus, Date deliveryTime, String expressNosToSave,
			Integer userId, Integer logisticsType);

	/**
	 * 选中一次快递公司后，保存
	 * @param fileDeliveryId
	 * @param printType
	 */
    void setFileDeliveryLoginsticsName(Integer fileDeliveryId, Integer printType);
    
    /**
     * 面单地址文件导入
     * @param file
     * @param fileDeliveryId 
     * @return
     * @throws IOException 
     */
    FileDeliveryUploadVO upload(MultipartFile file, Integer fileDeliveryId,Integer userId) throws IOException;
    
    /**
     * 更新主表和子表快递单号信息
     * @param addressId
     * @param logisticNos
     * @param userId
     */
	void updateLogisticNos(Integer addressId, String logisticNos, Integer userId);
}
