package com.vedeng.mobile.system.mapper;

import com.vedeng.mobile.system.model.WebAccount;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/7
 */
@Repository("webAccountMapper")
public interface WebAccountMapper {
    int deleteByPrimaryKey(Integer erpAccountId);

    int insert(WebAccount record);

    int insertSelective(WebAccount record);

    WebAccount selectByPrimaryKey(Integer erpAccountId);

    int updateByPrimaryKeySelective(WebAccount record);

    int updateByPrimaryKey(WebAccount record);

    List<WebAccount> selectByTraderId(Integer traderId);

}