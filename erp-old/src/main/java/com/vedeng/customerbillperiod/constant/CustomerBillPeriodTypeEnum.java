package com.vedeng.customerbillperiod.constant;

/**
 * @Author: daniel
 * @Date: 2021/7/22 15 34
 * @Description:
 */
public enum CustomerBillPeriodTypeEnum {

    /**
     * 异常账期
     */
    EXCEPTION(0,"异常账期"),

    /**
     * 正式账期
     */
    OFFICIAL(1,"正式账期"),

    TEMPORARY(2,"临时账期"),

    ORDER(3,"订单账期"),


    ;

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    CustomerBillPeriodTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CustomerBillPeriodTypeEnum getTypeEnumByCode(Integer code){
        for (CustomerBillPeriodTypeEnum item : CustomerBillPeriodTypeEnum.values()){
            if (item.getCode().equals(code)){
                return item;
            }
        }
        return EXCEPTION;
    }
}
