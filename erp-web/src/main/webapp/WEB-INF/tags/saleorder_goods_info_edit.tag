<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="saleorderGoodsList" type="java.util.List" required="true" %>
<%@attribute name="newSkuInfosMap" type="java.util.Map" required="true" %>
<%@attribute name="terminalTypes" type="java.util.List" required="true" %>
<%@attribute name="skuNoAndPriceMap" type="java.util.Map" required="true" %>
<%@attribute name="regions" type="java.util.List" required="true" %>
<%@attribute name="saleorderCoupon" type="com.vedeng.order.model.SaleorderCoupon" required="true" %>
<%@attribute name="realAmount" type="java.math.BigDecimal" required="true" %>
<%@attribute name="awardAmount" type="java.math.BigDecimal" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/goods-info-table.css">
<%-- 判断是否有退货数量 --%>
<c:set var="hasReturnNum" value="0"/>
<div class="parts">
    <div class="title-container title-container-blue">
        <div class="table-title nobor">产品信息</div>
        <c:if test="${saleorder.orderType ne 5}">
            <%--耗材订单不允许修改商品信息--%>
            <a class="title-click nobor" href="javascript:void(0);" onclick="validateOrderCouponInfo('addGood');">添加</a>
        </c:if>
        <div id="addGood" class="title-click nobor  pop-new-data" layerParams='{"width":"90%","height":"90%","title":"添加产品","link":"./addSaleorderGoods.do?scene=${scene}&saleorderId=${saleorder.saleorderId}"}' style="display: none;"></div>
        <c:if test="${(saleorder.quoteorderId eq 0 && saleorder.orderType ne 5)
                    || orderType eq 1 || orderType eq 9}">
            <%--耗材订单不允许修改商品信息--%>
            <a class="title-click nobor" href="javascript:void(0);" onclick="validateOrderCouponInfo('batchAddGood');">批量新增</a>
            <div id="batchAddGood" class="title-click nobor  pop-new-data" layerparams='{"width":"650px","height":"650px","title":"批量新增","link":"./batchAddSaleGoodsInit.do?saleorderId=${saleorder.saleorderId}"}' style="display: none;">批量新增</div>
        </c:if>
        <%--                        <a onclick="updateSaleGoodsInit(${saleorder.saleorderId},${scene});" >批量修改</a>--%>
        <a class="title-click nobor" href="javascript:void(0);" onclick="updateSaleGoodsInit(${saleorder.saleorderId},0);">批量修改</a>
        <span style="display: none;"> <!-- 弹框 -->
                             <div class="title-click nobor  pop-new-data" id="saleGoodsDeliveryDirect"></div>
                         </span>
    </div>
    <table class="table  table-bordered table-striped table-condensed table-centered goods-info-table" style="min-width:1224px;">
        <colgroup>
            <col width="44px">
            <col width="45px">
            <col width="280px">
            <col width="160px">
            <col width="160px">
            <col width="200px">
            <col width="120px">
            <col width="120px">
            <col width="95px">
        </colgroup>
        <thead>
            <tr >
                <th class="middle center">
                    <input type="checkbox" name="goodsCheckAllName" id="goodsCheckAllName" onclick="goodsCheckAllClick(this);" autocomplete="off"/>
                </th>
                <th>序号</th>
                <th>产品信息</th>
                <th>报价信息</th>
                <th>总价</th>
                <th>参考信息</th>
                <th>产品备注</th>
                <th>内部备注</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="goodsTbody">
            <c:set var="num" value="0" />
            <c:set var="totleMoney" value="0.00" />
            <c:set var="isNotDelPriceZero" value="0" />
            <c:set var="frTotleMoney" value="0.00"></c:set>
            <c:forEach var="list" items="${saleorderGoodsList}" varStatus="staut">
                <c:if test="${list.isDelete eq 0}">
                    <c:choose>
                        <c:when test="${list.sku eq 'V127063'}">
                            <c:set var="num" value="${num + 0}" />
                        </c:when>
                        <c:otherwise>
                            <c:set var="num" value="${num + list.num - list.afterReturnNum}" />
                        </c:otherwise>
                    </c:choose>
                <!-- 总额计算方式变更 2018-12-27 产品Ada 总额 = 单价*(订单商品数量-订单商品售后数量) -->
                <c:set var="totleMoney" value="${totleMoney + (list.price * (list.num - list.afterReturnNum))}" />
                <c:set var="frTotleMoney" value="${frTotleMoney + (list.realPrice * list.num)}"></c:set>

                </c:if>
                <!-- -->
                    <tr>
                        <td class="middle center">
                            <input type="hidden" value="${list.sku}">
                            <input type="checkbox" name="goodsCheckName" onclick="goodsCheckClick(this);" value="${list.saleorderGoodsId}" skuNo="${list.sku}" skuId="${list.goodsId}" skuName="${list.goodsName}" isDirectPurchase="${list.isDirectPurchase}" autocomplete="off"/>
                        </td>
                        <td class="middle center">
                            ${staut.count}
                        </td>
                        <!-- 第2列，产品信息 -->
                        <td>
                            <div class="goods-info-wrap">
                                <div class="info-pic">
                                    <div class="info-pic-inner addtitle" tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":""}'>
                                        <c:if test="${not empty list.imgUrl}">
                                            <img src="${list.imgUrl}" onerror="this.src='${pageContext.request.contextPath}/static/images/prod-img-error.png'">
                                        </c:if>
                                        <c:if test="${empty list.imgUrl}">
                                            <img src="${pageContext.request.contextPath}/static/images/prod-img-placeholder.png" alt="">
                                        </c:if>
                                    </div>
                                    <div class="prod-tag-wrap">
                                        <c:if test="${list.isRisk > 0}">
                                            <div class="goods-tip-wrap J-fk-icon" data-sku="${list.sku}">
                                                <img src="${pageContext.request.contextPath}/static/images/goods-info-tag/tag-fk.svg" class="tag-item tag-fk" />
                                                <div class="goods-tip-cnt">
                                                    <div class="J-tips-cnt-txt" style="display: none;"></div>
                                                    <div class="tips-cnt-loading J-tips-cnt-loading">
                                                        <span class="vd-icon icon-loading"></span>
                                                        <span class="tips-loading-txt">加载中...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${list.isGift eq '1'}">
                                            <img id="isGift" src="<%= basePath %>static/images/goods-info-tag/tag-gift.svg" class="tag-item" title="赠品"/>
                                        </c:if>
                                        <c:if test="${list.isDirectPurchase == 1}">
                                            <img id="isDirectPurchase" src="<%= basePath %>static/images/goods-info-tag/tag-cu.svg" class="tag-item" title="商城&quot;现货现价&quot;促销"/>
                                        </c:if>
                                    </div>
                                </div>
                                <div class="info-detail" >
                                    <!-- <c:if test="${list.isRisk > 0}">
                                        <img src="${pageContext.request.contextPath}/static/images/risk.png" width="28px" id="riskFlag_${list.saleorderGoodsId}"
                                                onclick="checkSaleorderGoodsRisk('${list.sku}','${list.saleorderGoodsId}')" />
                                    </c:if> -->
                                    <!-- <c:if test="${list.contractedGoodsFlag eq 1}">
                                        <span class="contractedGoodsFlag" style="color:red;">【协议商品】</span>
                                    </c:if> -->
                                    <!-- <c:if test="${list.isGift eq '1'}">
                                        <img id="isGift" src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" />
                                    </c:if> -->
                                    <!-- <c:if test="${list.isDirectPurchase == 1}">
                                        <img id="isDirectPurchase" src="<%= basePath %>static/images/promote_icon.svg" style="width: 15px" title="商城&quot;现货现价&quot;促销"/> -->
                                    <!-- </c:if> -->
                                    <div class="goods-name" <c:if test="${list.isDirectPurchase == 1}">title="商城&quot;现货现价&quot;促销"</c:if>><a class="addtitle" href="javascript:void(0);"
                                        tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":""}'>${list.goodsName}</a>
                                    </div>
                                    <div class="goods-sku">
                                        <div class="sku-txt">${list.sku}</div>
                                        <div class="goods-tip-wrap">
                                            <span class="vd-icon icon-info1"></span>
                                            <tags:saleorder_goods_purchase_tips saleorder="${saleorder}" saleorderGoods="${list}" skuDetailMap="${newSkuInfosMap[list.sku]}" />
                                        </div>
                                    </div>
                                    <div class="detail-info-field">
                                        <div class="detail-info-label">品牌：</div>
                                        <div class="detail-info-txt">${list.brandName}</div>
                                    </div>
                                    <div class="detail-info-field">
                                        <div class="detail-info-label">
                                            <c:choose>
                                                <c:when test="${list.spuType == 316 || list.spuType == 1008}">
                                                    型号
                                                </c:when>
                                                <c:when test="${list.spuType == 317 || list.spuType == 318}">
                                                    规格
                                                </c:when>
                                                <c:otherwise>
                                                    <c:choose>
                                                        <c:when test="${list.model != null && list.model != ''}">
                                                            型号
                                                        </c:when>
                                                        <c:otherwise>规格</c:otherwise>
                                                    </c:choose>
                                                </c:otherwise>
                                            </c:choose>
                                            ：</div>
                                        <div class="detail-info-txt">
                                            <c:choose>
                                                <c:when test="${list.spuType == 316 || list.spuType == 1008}">
                                                    ${list.model}
                                                </c:when>
                                                <c:when test="${list.spuType == 317 || list.spuType == 318}">
                                                    ${list.spec}
                                                </c:when>
                                                <c:otherwise>
                                                    <c:choose>
                                                        <c:when test="${list.model != null && list.model != ''}">
                                                            ${list.model}
                                                        </c:when>
                                                        <c:otherwise>${list.spec}</c:otherwise>
                                                    </c:choose>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <!-- 第3列，报价信息 -->
                        <td>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">单价：</div>
                                <div class="detail-info-txt">
                                    <div class="info-txt red">￥${list.price}</div>
                                    <c:if test= "${1 == list.isLowerGoods}" >
                                        <div class="goods-tip-wrap">
                                            <span class="vd-icon icon-caution1"></span>
                                            <div class="goods-tip-cnt nowrap">核价销售价:￥${list.checkPrice}</div>
                                        </div>
                                    </c:if>
                                </div>
                            </div>
                             <c:if test="${saleorder.orderType eq 5 || saleorder.orderType eq 1}">
                                <div class="detail-info-field width1">
                                    <div class="detail-info-label">原单价：</div>
                                    <div class="detail-info-txt">
                                        ￥<fmt:formatNumber type="number" value="${null == list.realPrice ? 0 : list.realPrice}" pattern="0.00" maxFractionDigits="2" />
                                    </div>
                                </div>
                            </c:if>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">数量：</div>
                                <div class="detail-info-txt">
                                    <c:choose>
                                        <c:when test="${((list.afterReturnNum==''||list.afterReturnNum==null) ? 0 : list.afterReturnNum) eq 0}">
                                            <div class="info-txt">${list.num}${list.unitName}</div>
                                        </c:when>
                                        <c:otherwise>
                                            <%-- 将是否有退货标识的标记打上1--%>
                                            <c:set var="hasReturnNum" value="1"/>
                                            <div class="info-txt">${list.num - list.afterReturnNum}${list.unitName}</div>
                                            <div class="goods-tip-wrap">
                                                <span class="vd-icon icon-info1"></span>
                                                <div class="goods-tip-cnt nowrap">原值：${list.num}</div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">货期：</div>
                                <div class="detail-info-txt">${list.deliveryCycle}天
                                    <input type="hidden" class="skuDeliveryCycle_${list.sku}" value="${list.deliveryCycle}">
                                    <div id="skuDeliveryCycle_${list.sku}" style="clear:both;padding-top: 0px;"></div>
                                </div>

                            </div>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">物流：</div>
                                <div class="detail-info-txt">
                                    <c:choose>
										<c:when test="${list.deliveryDirect eq 0}">普发
                                        </c:when>
                                        <c:otherwise>
                                            <div class="info-txt">直发</div>
                                            <div class="goods-tip-wrap width10">
                                                <span class="vd-icon icon-info1"></span>
                                                <div class="goods-tip-cnt">直发原因：${list.deliveryDirectComments}</div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="detail-info-field width1">
                                <div class="detail-info-label">含安调：</div>
                                <div class="detail-info-txt">
                                    <c:choose>
                                        <c:when test="${list.haveInstallation eq 0}">否</c:when>
                                        <c:otherwise>是</c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                        </td>

                        <!-- 第4列，总价 -->
                        <td>
                            <div class="detail-info-field">
                                <div class="detail-info-label">总价：</div>
                                <div class="detail-info-txt red">￥<fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /></div>
                            </div>
                            <c:if test="${saleorder.orderType eq 1 || saleorder.orderType eq 5}">
                                <div class="detail-info-field">
                                    <div class="detail-info-label">原总价：</div>
                                    <div class="detail-info-txt">￥<fmt:formatNumber type="number" value="${list.realPrice * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /></div>
                                </div>
                                <div class="detail-info-field">
                                    <div class="detail-info-label">优惠额：</div>
                                    <div class="detail-info-txt red">￥<fmt:formatNumber type="number" value="${(list.realPrice-list.price) * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /></div>
                                </div>
                            </c:if>
                           <%-- <c:if test="${list.maoLiBuyPrice != null && list.maoLiBuyPrice > 0}">
                                <div class="detail-info-field">
                                    <div class="detail-info-label">预估毛利：</div>
                                    <div class="detail-info-txt red">
                                        <fmt:formatNumber type="number" value="${((list.price-list.maoLiBuyPrice)/list.price)*100}" pattern="0.0" maxFractionDigits="1" />%
                                    </div>
                                </div>
                            </c:if>--%>
                            <%--<div class="detail-info-field wrap">
                                <div class="detail-info-label">近1个月采购均价：</div>
                                <div class="detail-info-txt">￥99999999.00</div>
                            </div>--%>

                            <%-- <div class="customername pos_rel " style="text-align: left">
                                 <!- 总额计算方式变更 2018-12-27 产品Ada 总额 = 单价*(订单商品数量-订单商品售后数量)->
                                <fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" />
                                <span>总价：</span>
                                        <fmt:formatNumber type="number" value="${list.price * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /><br/>
                                <c:if test="${list.realPrice != list.price}">
                                    <span>原总价：</span><fmt:formatNumber type="number" value="${list.realPrice * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /><br/>
                                    <span>优惠额：</span><fmt:formatNumber type="number" value="${(list.realPrice-list.price) * (list.num - list.afterReturnNum)}" pattern="0.00" maxFractionDigits="2" /><br/>
                                </c:if>

                            </div> --%>

                        </td>

                        <!-- 第5列，参考信息 -->
                        <td>
                            <tags:order_goods_consult_hejia_info_new item="${list}" skuNoAndPriceMap="${skuNoAndPriceMap}" />
                            <tags:sku_authorization_new list="${list}" terminalTypes="${terminalTypes}" regions="${regions}"/>
                        </td>

                        <!-- 第6列，产品备注 -->
                        <td>${list.goodsComments}</td>

                        <!-- 第7列，内部备注 -->
                        <td skuId="${list.goodsId}">
                            ${list.insideComments}
                            <div class="no_remark_error" style="display: none;">
                                <span style="color:red;">请设置订单要求，点击编辑进行内部备注修改</span>
                            </div>
                        </td>
                        <!-- 第8列，操作 -->
                        <td class="middle">
                            <div class="caozuo">
                                <span class="caozuo-blue" href="javascript:void(0);" onclick="validateOrderCouponInfo('editSaleOrderGood${list.saleorderGoodsId}');">编辑</span>
                                <span id="editSaleOrderGood${list.saleorderGoodsId}" style="display: none" class="caozuo-blue pop-new-data" layerParams='{"width":"700px","height":"650px","title":"编辑产品信息","link":"/orderstream/saleorder/editSaleorderGoods.do?saleorderGoodsId=${list.saleorderGoodsId}&orderType=${orderType}&isCoupons=${saleorder.isCoupons}&scene=${scene}"}'>编辑</span>

                                <c:if test="${orderType ne 5 }">
                                    <c:choose>
                                        <c:when test="${list.isDirectPurchase == 1}">
                                            <span class="disabled" title="商城&quot;现货现价&quot;促销,商品无法删除">删除</span>
                                        </c:when>
                                        <c:otherwise>
                                            <span class="caozuo-blue" href="javascript:void(0);" onclick="validateOrderCouponInfo('deleteSaleOrderGood${list.saleorderGoodsId}');">删除</span>
                                        </c:otherwise>
                                    </c:choose>
                                    <span id="deleteSaleOrderGood${list.saleorderGoodsId}" style="display: none" class="caozuo-blue" onclick="delSaleorderGoods(${list.saleorderGoodsId},${list.saleorderId},'${list.sku}',0);">删除</span>
                                </c:if>
                            </div>
                        </td>
                    </tr>
            </c:forEach>
        <tr class="total">
            <td colspan="9">
                <input type="hidden" id="goodsTotleMoney"  value="${totleMoney}">
                <!-- modify by tomcat.Hui 20190918 VDERP-1294 编辑订单金额错误  增加隐藏域，用于jQuery下拉框联动 start -->
                <input type="hidden" id="goodsTotleAmount"  value="${saleorder.totalAmount}">
                <input type="hidden" id="goodsOrderType"  value="${orderType}">
                <!-- modify by tomcat.Hui 20190918 VDERP-1294 编辑订单金额错误  增加隐藏域，用于jQuery下拉框联动 end -->

                <div class="goods-total-wrap">
                    <input type="hidden" value="${isNotDelPriceZero}" id="isNotDelPriceZero">
                    <div class="total-txt-item">
                        <div class="total-item-label">实际金额：</div>
                        <div class="total-item-txt red">￥<fmt:formatNumber type="number" value="${realAmount}" pattern="0.00" maxFractionDigits="2" /></div>
                        <c:if test="${hasReturnNum >0}" >
                            <div class="goods-tip-wrap">
                                <span class="vd-icon icon-info1"></span>
                                <div class="goods-tip-cnt nowrap">已扣除退货产品金额</div>
                            </div>
                        </c:if>
                    </div>
                    <div class="total-txt-item">
                        <div class="total-item-label">实际件数：</div>
                        <div class="total-item-txt">${num}</div>
                        <c:if test="${hasReturnNum >0}" >
                            <div class="goods-tip-wrap">
                                <span class="vd-icon icon-info1"></span>
                                <div class="goods-tip-cnt nowrap">已扣除退货产品数量</div>
                            </div>
                        </c:if>
                    </div>
                    <div class="total-txt-item">
                        <div class="total-item-label">优惠金额：</div>
                        <div class="total-item-txt">￥<fmt:formatNumber type="number" value="${(saleorderCoupon == null ? 0 : (saleorderCoupon.denomination == null ? 0 : saleorderCoupon.denomination)) + (awardAmount == null ? 0 : awardAmount)}" pattern="0.00" maxFractionDigits="2" /></div>
                    </div>
                    <div class="total-txt-item">
                        <div class="total-item-label">优惠前金额：</div>
                        <div class="total-item-txt">￥<fmt:formatNumber type="number" value="${saleorder.totalAmount +awardAmount + (saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination)}" pattern="0.00" maxFractionDigits="2" /></div>
                    </div>


                    <%--<div class="f_left inputfloat customername pos_rel">

                            <i class="iconbluesigh ml4 contorlIcon"></i>
                            <div class="pos_abs customernameshow">
                                总件数：指购买的产品总件数，不包含运费！
                            </div>
                        </div>

                        <c:if test="${saleorder.orderType ne 1 && saleorder.orderType ne 5}">
                            ，
                        </c:if>

                        <c:if test="${saleorder.orderType eq 1 || saleorder.orderType eq 5}">
                            ， 优惠前金额 <span class="font-red"><fmt:formatNumber type="number" value="${saleorder.totalAmount +awardAmount + (saleorderCoupon.denomination == null? 0 : saleorderCoupon.denomination)}" pattern="0.00" maxFractionDigits="2" /></span>
                            ，

                            ， 优惠后金额 <span class="font-red"><fmt:formatNumber type="number" value="${saleorder.totalAmount}" pattern="0.00" maxFractionDigits="2" /></span>
                        </c:if> --%>
            </td>
        </tr>
        </tbody>
    </table>
    <div class="goods-table-error J-goods-table-error">
        <span class="vd-icon icon-error2"></span>
        <span class="goods-table-error-txt">产品信息中存在必填字段，请检查完善后重新提交</span>
    </div>
</div>

<script>
    $(
        function (){
            var saleorderId = ${saleorder.saleorderId};

            //补订单产品详情相关数据
            $.ajax({
                async:true,
                url:page_url+'/order/saleorder/getsaleordergoodsextrainfo.do',
                data:{"saleorderId":saleorderId, "extraType":"order_saleorder"},//销售订单详情（占用，库存，采购状态，到库状态，发货状态，收货状态）
                type:"POST",
                dataType : "json",
                success:function(data){
                    if(data.code==0){
                        for (var i = 0; i < data.data.length; i++) {
                            $("#orderOccupy_stockNum_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.availableStockNum+"/"+data.data[i].goods.stockNum);
                            $("#kc_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.stockNum);
                            $("#kykc_"+data.data[i].saleorderGoodsId).html((data.data[i].goods.stockNum-data.data[i].goods.orderOccupy) < 0 ? 0 : (data.data[i].goods.stockNum-data.data[i].goods.orderOccupy));
                            $("#dzzy_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.orderOccupy);
                            $("#ktj_"+data.data[i].saleorderGoodsId).html(data.data[i].goods.adjustableNum);

                            //到库状态
                            // var dkztStr = '';
                            // if (data.data[i].warehouseNum == undefined || data.data[i].warehouseNum == 0) {
                            //     dkztStr = "未到库";
                            // } else if (data.data[i].warehouseNum < data.data[i].num) {
                            //     dkztStr = "部分到库";
                            // } else if (data.data[i].warehouseNum == data.data[i].num) {
                            //     dkztStr = "已到库";
                            // }
                            // $("#dkztStr_"+data.data[i].saleorderGoodsId).html(dkztStr);
                        }
                    }else{
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }
    )
</script>
