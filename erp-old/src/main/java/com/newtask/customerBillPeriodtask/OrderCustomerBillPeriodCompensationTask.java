package com.newtask.customerBillPeriodtask;

import com.alibaba.fastjson.JSON;
import com.newtask.model.UnfreezingBillPeriodDto;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.finance.model.Invoice;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单账期数据补偿
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "orderCustomerBillPeriodCompensationTask")
public class OrderCustomerBillPeriodCompensationTask extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(OrderCustomerBillPeriodCompensationTask.class);

    @Autowired
    private OrderAccountPeriodService orderAccountPeriodService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("OrderCustomerBillPeriodCompensationTask-JOB, START");

        if (StringUtils.isEmpty(s)) {
            XxlJobLogger.log("补偿必须拥有相应参数");
            return FAIL;
        }

        logger.info("OrderCustomerBillPeriodCompensationTask start s:{}", s);

        String[] params = s.split("%%");
        if (params.length < 2) {
            XxlJobLogger.log("请检查补偿数据相关参数！");
            return FAIL;
        }

        String paramName = params[0];
        String data = params[1];

        switch (paramName) {
            case "unfreezingBillPeriodDto": {
                UnfreezingBillPeriodDto unfreezingBillPeriodDto = JSON.parseObject(data, UnfreezingBillPeriodDto.class);
                logger.info("task添加流水处理订单冻结和占用的账期金额释放 unfreezingBillPeriodDto:{}", JSON.toJSONString(unfreezingBillPeriodDto));
                orderAccountPeriodService.unfreezingBillPeriodByCreditRepayment(unfreezingBillPeriodDto.getOrderId(),
                        unfreezingBillPeriodDto.getCreditRepaymentAmount(), unfreezingBillPeriodDto.getCapitalBillId());
                break;
            }

            case "afterSalesResult": {
                AfterSalesVo afterSalesResult = JSON.parseObject(data, AfterSalesVo.class);
                logger.info("task执行退款运算返回信息 afterSalesResult:{}", JSON.toJSONString(afterSalesResult));
                orderAccountPeriodService.dealOrderCustomerBillPeriodWithRefundOperation(afterSalesResult);
                break;
            }

            case "invoice":{
                Invoice invoice = JSON.parseObject(data, Invoice.class);
                logger.info("task售后退票处理账期逾期编码 invoice:{}", JSON.toJSONString(invoice));
                orderAccountPeriodService.dealAccountPeriodOverdueCodeByRefundInvoice(invoice);
                break;
            }

            case "saleorder":{
                Saleorder saleOrder = JSON.parseObject(data, Saleorder.class);
                logger.info("task关闭订单处理订单的账期业务 saleOrder:{}", JSON.toJSONString(saleOrder));
                orderAccountPeriodService.dealCloseOrderCustomerBillPeriodOfOrder(saleOrder);
                break;
            }
            default: {
                XxlJobLogger.log("请检查补偿数据相关参数！");
                return FAIL;
            }
        }
        return SUCCESS;
    }
}
