package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.*;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 文件token响应
 * @date 2024/10/11 9:44
 */
@Getter
@Setter
@XmlRootElement(name = "CMBC")
@XmlAccessorType(XmlAccessType.FIELD)
public class B2eGetTokenRes extends BankResponse {

    /**
     * 响应体
     */
    @XmlElement(name = "xDataBody")
    private B2eGetTokenResBody xDataBody;

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class B2eGetTokenResBody {

        /**
         * 客户端交易的唯一标志
         */
        private String trnId;
        /**
         * token
         */
        private String tokenId;

    }
}
