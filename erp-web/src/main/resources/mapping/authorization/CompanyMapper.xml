<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.CompanyMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.authorization.model.Company" >
    <id column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
    <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    COMPANY_ID, COMPANY_NAME, DOMAIN, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_COMPANY
    where COMPANY_ID = #{companyId,jdbcType=INTEGER}
  </select>
  <insert id="insertSelective" parameterType="com.vedeng.authorization.model.Company" >
    insert into T_COMPANY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="companyName != null" >
        COMPANY_NAME,
      </if>
      <if test="domain != null" >
        DOMAIN,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null" >
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.authorization.model.Company" >
    update T_COMPANY
    <set >
      <if test="companyName != null" >
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        DOMAIN = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where COMPANY_ID = #{companyId,jdbcType=INTEGER}
  </update>
  
  <!-- querylistPage start -->
  <select id="querylistPage" parameterType="Map" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
  	from T_COMPANY a
  	<where>
  		1=1
  		<if test="company.companyName != null and company.companyName != ''">
  		and a.COMPANY_NAME like CONCAT('%',#{company.companyName},'%' )
  		</if>
  	</where>
  	order by a.ADD_TIME DESC
  </select>
  <!-- querylistPage end -->
  
  <!--  selectByCompany start-->
  <select id="selectByCompany" parameterType="com.vedeng.authorization.model.Company" resultMap="BaseResultMap">
  	select
  	<include refid="Base_Column_List" />
  	from T_COMPANY
  	<where>
  		1=1
 		<if test="companyName != null" >
        and COMPANY_NAME = #{companyName,jdbcType=VARCHAR}
      </if>
      <if test="domain != null" >
        and DOMAIN = #{domain,jdbcType=VARCHAR}
      </if>
  	</where>
  </select>
  <!--  selectByCompany end-->
  
  <select id="getAll" resultMap="BaseResultMap">
  	select
  		<include refid="Base_Column_List" />
  	from T_COMPANY
  	order by COMPANY_ID asc
  </select>
  
  <select id="getCompanyList" parameterType="com.vedeng.authorization.model.Company" resultMap="BaseResultMap">
  	select
  		<include refid="Base_Column_List" />
  	from T_COMPANY
  	where
  		1=1
  		<if test="isEnable != null and isEnable !=''">
  		and IS_ENABLE = #{isEnable,jdbcType=INTEGER}
  		</if>
  	order by COMPANY_ID asc
  </select>
</mapper>