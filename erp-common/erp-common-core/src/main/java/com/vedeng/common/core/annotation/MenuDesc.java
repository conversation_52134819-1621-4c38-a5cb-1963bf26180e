package com.vedeng.common.core.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2022/8/8
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MenuDesc {

    /**
     * 当前链接可被访问的菜单或按钮权限,可以是C01,也可以是C0101,C0103
     * @return
     */
    String menuValue();
    /**
     * 当前链接的含义
     * @return
     */
    String menuDesc();

}
