<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.system.dao.TraderAddressGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.TraderAddressGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    <id column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_TYPE" jdbcType="TINYINT" property="traderType" />
    <result column="IS_ENABLE" jdbcType="TINYINT" property="isEnable" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="IS_DEFAULT" jdbcType="TINYINT" property="isDefault" />
    <result column="ZIP_CODE" jdbcType="VARCHAR" property="zipCode" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    TRADER_ADDRESS_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, AREA_ID, AREA_IDS, ADDRESS, 
    IS_DEFAULT, ZIP_CODE, COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.system.model.TraderAddressGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_TRADER_ADDRESS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_ADDRESS
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    delete from T_TRADER_ADDRESS
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.system.model.TraderAddressGenerateExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    delete from T_TRADER_ADDRESS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.system.model.TraderAddressGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    <selectKey keyProperty="traderAddressId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_TRADER_ADDRESS (TRADER_ID, TRADER_TYPE, IS_ENABLE, 
      AREA_ID, AREA_IDS, ADDRESS, 
      IS_DEFAULT, ZIP_CODE, COMMENTS, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=TINYINT}, #{isEnable,jdbcType=TINYINT}, 
      #{areaId,jdbcType=INTEGER}, #{areaIds,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{isDefault,jdbcType=TINYINT}, #{zipCode,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.system.model.TraderAddressGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    <selectKey keyProperty="traderAddressId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_TRADER_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="areaIds != null">
        AREA_IDS,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="isDefault != null">
        IS_DEFAULT,
      </if>
      <if test="zipCode != null">
        ZIP_CODE,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=TINYINT},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.system.model.TraderAddressGenerateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    select count(*) from T_TRADER_ADDRESS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    update T_TRADER_ADDRESS
    <set>
      <if test="record.traderAddressId != null">
        TRADER_ADDRESS_ID = #{record.traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="record.traderId != null">
        TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      </if>
      <if test="record.traderType != null">
        TRADER_TYPE = #{record.traderType,jdbcType=TINYINT},
      </if>
      <if test="record.isEnable != null">
        IS_ENABLE = #{record.isEnable,jdbcType=TINYINT},
      </if>
      <if test="record.areaId != null">
        AREA_ID = #{record.areaId,jdbcType=INTEGER},
      </if>
      <if test="record.areaIds != null">
        AREA_IDS = #{record.areaIds,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        ADDRESS = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.isDefault != null">
        IS_DEFAULT = #{record.isDefault,jdbcType=TINYINT},
      </if>
      <if test="record.zipCode != null">
        ZIP_CODE = #{record.zipCode,jdbcType=VARCHAR},
      </if>
      <if test="record.comments != null">
        COMMENTS = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    update T_TRADER_ADDRESS
    set TRADER_ADDRESS_ID = #{record.traderAddressId,jdbcType=INTEGER},
      TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{record.traderType,jdbcType=TINYINT},
      IS_ENABLE = #{record.isEnable,jdbcType=TINYINT},
      AREA_ID = #{record.areaId,jdbcType=INTEGER},
      AREA_IDS = #{record.areaIds,jdbcType=VARCHAR},
      ADDRESS = #{record.address,jdbcType=VARCHAR},
      IS_DEFAULT = #{record.isDefault,jdbcType=TINYINT},
      ZIP_CODE = #{record.zipCode,jdbcType=VARCHAR},
      COMMENTS = #{record.comments,jdbcType=VARCHAR},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.system.model.TraderAddressGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    update T_TRADER_ADDRESS
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=TINYINT},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="zipCode != null">
        ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.system.model.TraderAddressGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 22 20:59:19 CST 2019.
    -->
    update T_TRADER_ADDRESS
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=TINYINT},
      IS_ENABLE = #{isEnable,jdbcType=TINYINT},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
  </update>
</mapper>