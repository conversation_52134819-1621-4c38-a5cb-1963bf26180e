package com.vedeng.goods.service.impl;

import com.vedeng.common.constant.CommonConstants;
import com.vedeng.goods.dao.BaseAttributeValueMapper;
import com.vedeng.goods.model.BaseAttributeValue;
import com.vedeng.goods.model.vo.BaseAttributeValueVo;
import com.vedeng.goods.model.vo.BaseAttributeVo;
import com.vedeng.goods.model.vo.BaseCategoryVo;
import com.vedeng.goods.service.BaseAttributeValueService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("baseAttributeValueService")
public class BaseAttributeValueServiceImpl implements BaseAttributeValueService {

    @Autowired
    private BaseAttributeValueMapper baseAttributeValueMapper;

    @Override
    public List<BaseAttributeValueVo> getBaseAttributeValueVoList(BaseAttributeValueVo baseAttributeValueVo,List<BaseAttributeVo> list) {
        Map<String,Object> map = new HashMap<>();
        map.put("baseAttributeValueVo",baseAttributeValueVo);
        map.put("list",list);
        return baseAttributeValueMapper.getBaseAttributeValueVoList(map);
    }

    @Override
    public List<BaseAttributeValueVo> getBaseAttributeValueVoListByAttrId(BaseAttributeValueVo baseAttributeValueVo) {

        return baseAttributeValueMapper.getBaseAttributeValueVoListByAttrId(baseAttributeValueVo);
    }

    @Override
    public List<BaseAttributeValueVo> getAttrValueListByCategoryId(List<BaseCategoryVo> list) {
        Map<String,Object> map = new HashMap<>();
        map.put("list",list);
        map.put("isDeleted", CommonConstants.IS_DELETE_0);//未删除
        return baseAttributeValueMapper.getAttrValueListByCategoryId(map);
    }

    @Override
    public List<BaseAttributeValueVo> getBaseAttributeValueVoListASC(BaseAttributeValueVo baseAttributeValueVo, List<BaseAttributeVo> list) {
        Map<String,Object> map = new HashMap<>();
        map.put("baseAttributeValueVo",baseAttributeValueVo);
        map.put("list",list);
        return baseAttributeValueMapper.getBaseAttributeValueVoListASC(map);
    }


    @Override
    public List<BaseAttributeValue> listAttributeValueByIds(List<Integer> attributeValueIdList) {
        if(CollectionUtils.isEmpty(attributeValueIdList)){
            return Collections.emptyList();
        }
        return baseAttributeValueMapper.listAttributeValueByIds(attributeValueIdList);
    }

    @Override
    public List<BaseAttributeValue> listAttributeValueBySkuIds(List<Integer> skuIdList) {
        if(CollectionUtils.isEmpty(skuIdList)){
            return Collections.emptyList();
        }
        return baseAttributeValueMapper.listAttributeValueBySkuIds(skuIdList);
    }
}
