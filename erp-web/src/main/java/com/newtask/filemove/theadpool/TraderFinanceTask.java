package com.newtask.filemove.theadpool;

import com.newtask.filemove.dto.FileMoveDto;
import com.vedeng.common.model.FileInfo;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.trader.model.TraderFinance;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.List;

public class TraderFinanceTask extends FileMoveTask {

    @Resource
    private FileMoveDao fileMoveDao;

    public TraderFinanceTask(List<FileMoveDto> fileMoveList) {
        super(fileMoveList);
        // 运行时注入service
        WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
        fileMoveDao = (FileMoveDao) context.getBean("fileMoveDao");
    }

    @Override
    protected void updateErrorInfo(FileMoveDto fileMoveDto) {
        TraderFinance toUpdate = new TraderFinance();
        toUpdate.setTraderFinanceId(fileMoveDto.getId());
        toUpdate.setSynSuccess(2);
        fileMoveDao.updateTraderFinance(toUpdate);
    }

    @Override
    protected void updateOriginalFileInfo(FileMoveDto fileMoveDto, FileInfo fileInfo) {
        //更新uri、domain、resourceId
        TraderFinance toUpdate = new TraderFinance();
        toUpdate.setTraderFinanceId(fileMoveDto.getId());
        toUpdate.setAverageTaxpayerDomain(fileInfo.getHttpUrl());
        toUpdate.setAverageTaxpayerUri(fileInfo.getFilePath());
        toUpdate.setOriginalFilepath(fileMoveDto.getDomain() + fileMoveDto.getUri());
        toUpdate.setSynSuccess(1);
        toUpdate.setOssResourceId(fileInfo.getOssResourceId());

        if(fileInfo.getPrefix() != null){
            toUpdate.setCostTime(Long.valueOf(fileInfo.getPrefix()));
        }

        fileMoveDao.updateTraderFinance(toUpdate);
    }
}
