<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.filemove.dao.FileMoveDao" >

    <select id="getTraderCertificatesByPage" resultType="com.vedeng.trader.model.TraderCertificate">
        SELECT *
        FROM T_TRADER_CERTIFICATE
        where
              TRADER_CERTIFICATE_ID &gt; #{start}
              AND (DOMAIN is NOT null and DOMAIN != '')
              AND (URI is NOT null and URI != '' AND LOCATE('resourceId', URI) = 0)
              AND SYN_SUCCESS = 0
        order by TRADER_CERTIFICATE_ID
        limit #{limit}
    </select>

    <select id="getTraderCertificateByParam" resultType="com.vedeng.trader.model.TraderCertificate">
        SELECT * FROM T_TRADER_CERTIFICATE WHERE
        TRADER_CERTIFICATE_ID <![CDATA[>=]]> #{start,jdbcType=INTEGER}
        and TRADER_CERTIFICATE_ID <![CDATA[<=]]> #{end,jdbcType=INTEGER}
        AND (DOMAIN is NOT null and DOMAIN != '')
        AND (URI is NOT null and URI != '' AND LOCATE('resourceId', URI) = 0)
        and SYN_SUCCESS = 0
    </select>

    <update id="updateTraderCertificate" parameterType="com.vedeng.trader.model.TraderCertificate" >
        UPDATE T_TRADER_CERTIFICATE
        <set>
            <if test="domain != null and domain != ''" >
                DOMAIN = #{domain,jdbcType=VARCHAR},
            </if>
            <if test="uri != null and uri != ''" >
                URI = #{uri,jdbcType=VARCHAR},
            </if>
            <if test="ossResourceId != null and ossResourceId != ''" >
                OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null and originalFilepath != ''" >
                ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="synSuccess != null" >
                SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
            </if>
            <if test="costTime != null" >
                COST_TIME = #{costTime,jdbcType=BIGINT},
            </if>
        </set>
        WHERE TRADER_CERTIFICATE_ID = #{traderCertificateId}
    </update>


    <select id="getBrandByPage" resultType="com.vedeng.goods.model.Brand">
        SELECT *
        FROM T_BRAND
        where
              BRAND_ID &gt; #{start}
              AND (LOGO_DOMAIN is NOT null and LOGO_DOMAIN != '')
              AND (LOGO_URI is NOT null and LOGO_URI != '' AND LOCATE('resourceId', LOGO_URI) = 0)
              AND SYN_SUCCESS = 0
        order by BRAND_ID
        limit #{limit}
    </select>

    <select id="getBrandByParam" resultType="com.vedeng.goods.model.Brand">
        SELECT * FROM T_BRAND WHERE
        BRAND_ID <![CDATA[>=]]> #{start,jdbcType=INTEGER}
        and BRAND_ID <![CDATA[<=]]> #{end,jdbcType=INTEGER}
        AND (LOGO_DOMAIN is NOT null and LOGO_DOMAIN != '')
        AND (LOGO_URI is NOT null and LOGO_URI != '' AND LOCATE('resourceId', LOGO_URI) = 0)
        and SYN_SUCCESS = 0
    </select>

    <update id="updateBrand" parameterType="com.vedeng.goods.model.Brand">
        update T_BRAND
        <set>
            <if test="logoDomain != null">
                LOGO_DOMAIN = #{logoDomain,jdbcType=VARCHAR},
            </if>
            <if test="logoUri != null">
                LOGO_URI = #{logoUri,jdbcType=VARCHAR},
            </if>
            <if test="ossResourceId != null and ossResourceId != ''" >
                OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null and originalFilepath != ''" >
                ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="synSuccess != null" >
                SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
            </if>
            <if test="costTime != null" >
                COST_TIME = #{costTime,jdbcType=BIGINT},
            </if>
        </set>
        where BRAND_ID = #{brandId,jdbcType=INTEGER}
    </update>


    <select id="getGoodsAttachmentByPage" resultType="com.vedeng.goods.model.GoodsAttachment">
        SELECT *
        FROM T_GOODS_ATTACHMENT
        where
              GOODS_ATTACHMENT_ID &gt; #{start}
              AND (DOMAIN is NOT null and DOMAIN != '' )
              AND (URI is NOT null and URI != '' AND LOCATE('resourceId', URI) = 0)
              AND SYN_SUCCESS = 0
        order by GOODS_ATTACHMENT_ID
        limit #{limit}
    </select>

    <select id="getGoodsAttachmentByParam" resultType="com.vedeng.goods.model.GoodsAttachment">
        SELECT * FROM T_GOODS_ATTACHMENT WHERE
        GOODS_ATTACHMENT_ID <![CDATA[>=]]> #{start,jdbcType=INTEGER}
        and GOODS_ATTACHMENT_ID <![CDATA[<=]]> #{end,jdbcType=INTEGER}
        AND (DOMAIN is NOT null and DOMAIN != '' )
        AND (URI is NOT null and URI != '' AND LOCATE('resourceId', URI) = 0)
        and SYN_SUCCESS = 0
    </select>

    <update id="updateGoodsAttachment" parameterType="com.vedeng.goods.model.GoodsAttachment">
        update T_GOODS_ATTACHMENT
        <set>
            <if test="domain != null and domain != ''">
                DOMAIN = #{domain,jdbcType=VARCHAR},
            </if>
            <if test="uri != null and uri != ''">
                URI = #{uri,jdbcType=VARCHAR},
            </if>
            <if test="ossResourceId != null and ossResourceId != ''" >
                OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null and originalFilepath != ''" >
                ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="synSuccess != null" >
                SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
            </if>
            <if test="costTime != null" >
                COST_TIME = #{costTime,jdbcType=BIGINT},
            </if>
        </set>
        where GOODS_ATTACHMENT_ID = #{goodsAttachmentId,jdbcType=INTEGER}
    </update>



    <select id="getAttachmentByPage" resultType="com.vedeng.system.model.Attachment">
        SELECT *
        FROM T_ATTACHMENT
        where
              ATTACHMENT_ID &gt; #{start}
              AND (DOMAIN is NOT null and DOMAIN != '' )
              AND (URI is NOT null and URI != '' AND LOCATE('resourceId', URI) = 0)
              AND SYN_SUCCESS = 0
        order by ATTACHMENT_ID
        limit #{limit}
    </select>

    <select id="getAttachmentByParam" resultType="com.vedeng.system.model.Attachment">
        SELECT * FROM T_ATTACHMENT WHERE
        ATTACHMENT_ID <![CDATA[>=]]> #{start,jdbcType=INTEGER}
        and ATTACHMENT_ID <![CDATA[<=]]> #{end,jdbcType=INTEGER}
        AND (DOMAIN is NOT null and DOMAIN != '' )
        AND (URI is NOT null and URI != '' AND LOCATE('resourceId', URI) = 0)
        and SYN_SUCCESS = 0
    </select>

    <update id="updateAttachment" parameterType="com.vedeng.system.model.Attachment">
        update T_ATTACHMENT
        <set>
            <if test="domain != null and domain != ''">
                DOMAIN = #{domain,jdbcType=VARCHAR},
            </if>
            <if test="uri != null and uri != ''">
                URI = #{uri,jdbcType=VARCHAR},
            </if>
            <if test="ossResourceId != null and ossResourceId != ''" >
                OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null and originalFilepath != ''" >
                ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="synSuccess != null" >
                SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
            </if>
            <if test="costTime != null" >
                COST_TIME = #{costTime,jdbcType=BIGINT},
            </if>
        </set>
        where ATTACHMENT_ID = #{attachmentId,jdbcType=INTEGER}
    </update>




    <select id="getTraderFinanceByPage" resultType="com.vedeng.trader.model.TraderFinance">
        SELECT *
        FROM T_TRADER_FINANCE
        where
              TRADER_FINANCE_ID &gt; #{start}
              AND (AVERAGE_TAXPAYER_DOMAIN is NOT null and AVERAGE_TAXPAYER_DOMAIN != '' )
              AND (AVERAGE_TAXPAYER_URI is NOT null and AVERAGE_TAXPAYER_URI != '' AND LOCATE('resourceId', AVERAGE_TAXPAYER_URI) = 0)
              AND SYN_SUCCESS = 0
        order by TRADER_FINANCE_ID
        limit #{limit}
    </select>

    <select id="getTraderFinanceByParam" resultType="com.vedeng.trader.model.TraderFinance">
        SELECT * FROM T_TRADER_FINANCE WHERE
        TRADER_FINANCE_ID <![CDATA[>=]]> #{start,jdbcType=INTEGER}
        and TRADER_FINANCE_ID <![CDATA[<=]]> #{end,jdbcType=INTEGER}
        AND (AVERAGE_TAXPAYER_DOMAIN is NOT null and AVERAGE_TAXPAYER_DOMAIN != '' )
        AND (AVERAGE_TAXPAYER_URI is NOT null and AVERAGE_TAXPAYER_URI != '' AND LOCATE('resourceId', AVERAGE_TAXPAYER_URI) = 0)
        and SYN_SUCCESS = 0
    </select>

    <update id="updateTraderFinance" parameterType="com.vedeng.trader.model.TraderFinance">
        update T_TRADER_FINANCE
        <set>
            <if test="averageTaxpayerDomain != null and averageTaxpayerDomain != ''">
                AVERAGE_TAXPAYER_DOMAIN = #{averageTaxpayerDomain,jdbcType=VARCHAR},
            </if>
            <if test="averageTaxpayerUri != null and averageTaxpayerUri != ''">
                AVERAGE_TAXPAYER_URI = #{averageTaxpayerUri,jdbcType=VARCHAR},
            </if>
            <if test="ossResourceId != null and ossResourceId != ''" >
                OSS_RESOURCE_ID = #{ossResourceId,jdbcType=VARCHAR},
            </if>
            <if test="originalFilepath != null and originalFilepath != ''" >
                ORIGINAL_FILEPATH = #{originalFilepath,jdbcType=VARCHAR},
            </if>
            <if test="synSuccess != null" >
                SYN_SUCCESS = #{synSuccess,jdbcType=INTEGER},
            </if>
            <if test="costTime != null" >
                COST_TIME = #{costTime,jdbcType=BIGINT},
            </if>
        </set>
        where TRADER_FINANCE_ID = #{traderFinanceId,jdbcType=INTEGER}
    </update>

</mapper>