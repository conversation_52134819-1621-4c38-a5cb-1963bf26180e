package com.vedeng.erp.trader.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 开户行匹配配置分组表
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BankMatchConfigGroupDto extends BaseDto {
    /**
    * 主键
    */
    private Long groupId;

    /**
    * 分组名称
    */
    private String groupName;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
     * 开户行匹配配置
     * 回单银行
     */
    private String bankConfig;


    public String getGroupName() {
        return groupName.trim();
    }
}
