<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.billsync.dao.TmpAlipayBillDataExtMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.billsync.task.model.entity.TmpAlipayBillDataExtDo">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="FINANCIAL_TURNOVER" jdbcType="VARCHAR" property="financialTurnover" />
    <result column="BUSINESS_TURNOVER" jdbcType="VARCHAR" property="businessTurnover" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="OCCURRENCE_TIME" jdbcType="TIMESTAMP" property="occurrenceTime" />
    <result column="OTHER_ACCOUNT" jdbcType="VARCHAR" property="otherAccount" />
    <result column="INCOME" jdbcType="DECIMAL" property="income" />
    <result column="OUTLAY" jdbcType="DECIMAL" property="outlay" />
    <result column="BALANCE" jdbcType="DECIMAL" property="balance" />
    <result column="TRADING_CHANNEL" jdbcType="VARCHAR" property="tradingChannel" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="SYNC_TIME" jdbcType="DATE" property="syncTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, FINANCIAL_TURNOVER, BUSINESS_TURNOVER, ORDER_NO, SKU_NAME, OCCURRENCE_TIME, OTHER_ACCOUNT, 
    INCOME, OUTLAY, BALANCE, TRADING_CHANNEL, BUSINESS_TYPE, REMARK, SYNC_TIME, UPDATE_TIME, 
    ADD_TIME
  </sql>
    <select id="getAlipayBillListByTime"
            resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    FROM TMP_ALIPAY_BILL_DATA
    WHERE
      SYNC_TIME <![CDATA[=]]> #{queryDate,jdbcType = DATE}
    </select>

  <delete id="deleteBySycTime" parameterType="java.lang.String">
    delete from TMP_ALIPAY_BILL_DATA
    where SYNC_TIME = #{billDate,jdbcType=DATE}
  </delete>

</mapper>