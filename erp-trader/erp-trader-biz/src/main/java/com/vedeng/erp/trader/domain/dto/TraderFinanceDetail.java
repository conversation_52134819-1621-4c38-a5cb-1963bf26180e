package com.vedeng.erp.trader.domain.dto;

import com.vedeng.erp.trader.domain.entity.TraderCustomerFinance;

public class TraderFinanceDetail extends TraderCustomerFinance {
	private String traderName;
	private Integer customerType;
	private String addTimeStr;
	private String pushTimeStr;
	private String modTimeStr;

	public String getTraderName() {
		return traderName;
	}

	public void setTraderName(String traderName) {
		this.traderName = traderName;
	}

	public Integer getCustomerType() {
		return customerType;
	}

	public void setCustomerType(Integer customerType) {
		this.customerType = customerType;
	}

	public String getAddTimeStr() {
		return addTimeStr;
	}

	public void setAddTimeStr(String addTimeStr) {
		this.addTimeStr = addTimeStr;
	}

	public String getPushTimeStr() {
		return pushTimeStr;
	}

	public void setPushTimeStr(String pushTimeStr) {
		this.pushTimeStr = pushTimeStr;
	}

	public String getModTimeStr() {
		return modTimeStr;
	}

	public void setModTimeStr(String modTimeStr) {
		this.modTimeStr = modTimeStr;
	}
}