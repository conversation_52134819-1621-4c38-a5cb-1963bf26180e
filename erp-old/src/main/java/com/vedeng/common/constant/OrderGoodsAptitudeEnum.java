package com.vedeng.common.constant;

public enum OrderGoodsAptitudeEnum {

    ORDER_ID_IS_NULL("订单标识为空","ORDER_ID_IS_NULL"),
    BUSINESS_CERTIFICATE_IS_NULL("该客户缺少营业执照","BUSINESS_CERTIFICATE_IS_NULL"),
    BUSINESS_CERTIFICATE_DATE_OUT("该客户的营业执照过期","BUSINESS_CERTIFICATE_DATE_OUT"),
    BUSINESS_CERTIFICATE_WITHOUT_MED("该客户的营业执照不包含“医疗器械”经营范围","BUSINESS_CERTIFICATE_WITHOUT_MED"),
    SECOND_CERTIFICATE_IS_NULL("该客户缺少医疗器械二类备案凭证","SECOND_CERTIFICATE_IS_NULL"),
    SECOND_CERTIFICATE_DATE_OUT("该客户的医疗器械二类备案凭证过期","SECOND_CERTIFICATE_DATE_OUT"),
    THIRD_CERTIFICATE_IS_NULL("该客户缺少医疗器械经营许可证（三类）","THIRD_CERTIFICATE_IS_NULL"),
    THIRD_CERTIFICATE_DATE_OUT("该客户的医疗器械经营许可证（三类）过期","THIRD_CERTIFICATE_DATE_OUT"),
    OPERATE_CERTIFICATE_IS_NULL("该客户缺少医疗机构执业许可证","OPERATE_CERTIFICATE_IS_NULL"),
    OPERATE_CERTIFICATE_DATE_OUT("该客户的医疗机构执业许可证过期","OPERATE_CERTIFICATE_DATE_OUT"),
    ANIMAL_CERTIFICATE_IS_NULL("缺少动物诊疗许可证","ANIMAL_CERTIFICATE_IS_NULL"),
    ANIMAL_CERTIFICATE_DATE_OUT("动物诊疗许可证过期","ANIMAL_CERTIFICATE_DATE_OUT"),
    CHNMEDICAL_CERTIFICATE_IS_NULL("缺少中医诊所备案证","CHNMEDICAL_CERTIFICATE_IS_NULL"),
    CHNMEDICAL_CERTIFICATE_DATE_OUT("中医诊所备案证过期","CHNMEDICAL_CERTIFICATE_DATE_OUT"),
    SECOND_MEDICAL_IS_INVALIED("该客户的医疗器械（二类）备案凭证经营范围不满足订单中产品的国标分类","SECOND_MEDICAL_IS_INVALIED"),
    THIRD_MEDICAL_IS_INVALIED("该客户的医疗器械经营许可证（三类）经营范围不满足订单中产品的国标分类","THIRD_MEDICAL_IS_INVALIED"),
    ORDER_IS_NULL("订单不存在或客户字段为空","ORDER_IS_NULL"),
    CUSTOMER_IS_NULL("客户不存在或客户性质为空","CUSTOMER_IS_NULL"),
    CATEGORY_IS_NULL("商品资质类别信息缺少","CATEGORY_IS_NULL"),;

    OrderGoodsAptitudeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    OrderGoodsAptitudeEnum(String message, String messageType) {

        this.message = message;
        this.messageType = messageType;
    }



    OrderGoodsAptitudeEnum(String message) {
        this.message = message;
    }

    private Integer code;
    private String message;
    private String messageType;

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
