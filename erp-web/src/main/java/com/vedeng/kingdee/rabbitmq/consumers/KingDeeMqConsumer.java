package com.vedeng.kingdee.rabbitmq.consumers;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeMsgStatusEnums;
import com.vedeng.infrastructure.kingdee.service.KingDeeEventMsgService;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 金蝶mq消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class KingDeeMqConsumer extends AbstractMessageListener {


    @Qualifier("kingDeeMqBaseService")
    @Autowired
    private KingDeeMqBaseService<KingDeeMqBaseDto> kingDeeMqBaseService;
    @Autowired
    private KingDeeEventMsgService kingDeeEventMsgService;

    @Override
    public void doBusiness(Message message, Channel channel) {
        KingDeeEventMsgDto kingDeeEventMsgDto = null;
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("金蝶异步事件，回传消息:[{}]", body);
            kingDeeEventMsgDto = JSON.parseObject(body, KingDeeEventMsgDto.class);
            if (this.msgDontBizHandle(kingDeeEventMsgDto.getMessageStatus())) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            kingDeeMqBaseService.execute(kingDeeEventMsgDto);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            kingDeeEventMsgDto.setErrorMsg(e.getMessage());
            kingDeeEventMsgService.msgRetry(kingDeeEventMsgDto);
            log.error("金蝶异步事件：错误信息：[{}]", JSON.toJSONString(message), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (Exception ex) {
                log.error("金蝶异步事件：错误信息：[{}]", JSON.toJSONString(message), e);
            }
        }
    }

    /**
     * 消息是否进行业务处理
     *
     * @param messageStatus messageStatus
     * @return true : 无需处理  false：需要处理
     */
    private boolean msgDontBizHandle(Integer messageStatus) {
        KingDeeMsgStatusEnums statusEnum = KingDeeMsgStatusEnums.getEnum(messageStatus);
        return statusEnum.equals(KingDeeMsgStatusEnums.CONSUME) || statusEnum.equals(KingDeeMsgStatusEnums.NO_HANDLE);
    }


}

