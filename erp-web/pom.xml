	<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
      	<groupId>com.vedeng.erp</groupId>
		<artifactId>erp</artifactId>
		<version>1.0.0-SNAPSHOT</version>
    </parent>
	<artifactId>erp-web</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<packaging>war</packaging>
	<name>erp-web</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<mysql.driver.version>5.1.47</mysql.driver.version>
		<spring.version>4.1.9.RELEASE</spring.version>
		<mybatis.version>3.3.1</mybatis.version>
		<activiti.version>5.22.0</activiti.version>
		<shiro.version>1.3.2</shiro.version>
		<junit.version>4.12</junit.version>
		<druid.version>1.1.16</druid.version>
		<log4j.version>1.2.17</log4j.version>
		<fastjson.version>1.2.72</fastjson.version>
		<commons.fileupload.version>1.3.2</commons.fileupload.version>
		<commons.codec.version>1.10</commons.codec.version>
		<jedis.version>2.6.2</jedis.version>
		<axis2.version>1.7.8</axis2.version>
		<ftpclient-pool.version>1.0-SNAPSHOT</ftpclient-pool.version>
		<skipTests>true</skipTests>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-wms-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<!-- API标准化模块 -->
		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-api-standard</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-buyorder-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-system-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-finance-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-saleorder-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-goods-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-trader-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-kingdee-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-oa-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-websocket</artifactId>
			<version>8.5.39</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
			<version>8.5.39</version>
			<scope>provided</scope>
		</dependency>
		<!-- <dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
			<scope>provided</scope>
		</dependency> -->
		<!--避免启动出错-->
		<dependency>
			<groupId>tomcat</groupId>
			<artifactId>jasper-runtime</artifactId>
			<version>5.5.23</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.webjars</groupId>
			<artifactId>webjars-locator</artifactId>
			<version>0.45</version>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.vedeng.framework</groupId>
			<artifactId>vedeng-core-offline</artifactId>
			<version>1.2.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.github.kuaidi100-api</groupId>
			<artifactId>sdk</artifactId>
			<version>1.0.13</version>
			<exclusions>
				<exclusion>
					<groupId>org.projectlombok</groupId>
					<artifactId>lombok</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.code.gson</groupId>
					<artifactId>gson</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.httpcomponents</groupId>
					<artifactId>httpclient</artifactId>
				</exclusion>

				<exclusion>
					<groupId>org.apache.httpcomponents</groupId>
					<artifactId>httpcore</artifactId>
				</exclusion>

				<exclusion>
					<groupId>org.apache.httpcomponents</groupId>
					<artifactId>httpmime</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
				<exclusion>
					<groupId>junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.65</version>
		</dependency>

		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-common-cat</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc6</artifactId>
			<version>11.2.0.3</version>
		</dependency>


		<!-- ERP Temporal工作流引擎模块 -->
		<dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-temporal</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>



	</dependencies>

	 
	<build>
		<resources>
			<!-- 扫描src/main/java下所有xx.xml文件 -->
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
			</resource>
		</resources>
		<finalName>erp</finalName>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.8.1</version>
					<configuration>
						<source>1.8</source>
						<target>1.8</target>
					</configuration>
				</plugin>

				<plugin>
					<groupId>org.mybatis.generator</groupId>
					<artifactId>mybatis-generator-maven-plugin</artifactId>
					<version>1.3.2</version>
					<configuration>
						<verbose>true</verbose>
						<overwrite>true</overwrite>
					</configuration>
					<dependencies>
						<!--mysql驱动包-->
						<dependency>
							<groupId>mysql</groupId>
							<artifactId>mysql-connector-java</artifactId>
							<version>${mysql.driver.version}</version>
						</dependency>
					</dependencies>
				</plugin>
			</plugins>
	</build>


	<repositories>
		<repository>
			<id>maven</id>
			<name>maven-public</name>
				<url>http://nexus.ivedeng.com/repository/maven-public/</url>
			<layout>default</layout>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>
		<repository>
			<id>third</id>
			<name>thirdc</name>
			<url>http://nexus.ivedeng.com/repository/third/</url>
			<layout>default</layout>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>
		<repository>
			<id>maven-snapshots</id>
			<name>maven-snapshots</name>
			<url>http://nexus.ivedeng.com/repository/maven-snapshots</url>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>
	</repositories>


</project>
