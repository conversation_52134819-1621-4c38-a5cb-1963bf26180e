package com.vedeng.erp.trader.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: ai分析通话汇总
 * @date 2024/5/16 16:58
 */
@Data
public class AiCommunicateRecordSummary {


    /**
     * 统计日期
     */
    private Date summaryDate;

    /**
     * 总通话数
     */
    private Integer sumCommunicatNum = 0;

    /**
     * 通话总时长
     */
    private BigDecimal sumCommunicateTime = BigDecimal.ONE;

    /**
     * 意向客户预测
     */
    private Integer traderForecastNum = 0;

    /**
     * 生成商机数
     */
    private Integer businessNum = 0;

    /**
     * 添加微信数预测
     */
    private Integer wechatForecastNum = 0;

    /**
     * 汇总图片
     * 1.总通话数
     * 2.总通话时长
     * 3.意向客户预测
     * 4.生成商机数预测
     * 5.添加微信数预测
     */
    private Map<String, List<Pie>> pieMap;

    /**
     * 汇总
     */
    private List<Summary> summaryList;

    /**
     * 待办明细统计
     */
    private TodoDetail todoDetail;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Summary {
        private String type;
        private Integer total;
        private Integer completed;
        private BigDecimal duration;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TodoDetail {

        private List<Integer> needBusinessOpportunityPhoneList = new ArrayList<>();
        private Integer needBusinessOpportunityNum = 0;

        private List<Integer> needToSendProductInfoRecordIdList = new ArrayList<>();
        private Integer needToSendProductInfoNum = 0;

        private List<Integer> needToSendQuoteRecordIdList = new ArrayList<>();
        private Integer needToSendQuoteNum = 0;

        private List<Integer> recommendProductList = new ArrayList<>();
        private Integer recommendProductNum = 0;

        private List<Integer> needToSendProductPlanRecordIdList = new ArrayList<>();
        private Integer needToSendProductPlanNum;

        private List<Integer> sendAfterSalesPolicyRecordIdList = new ArrayList<>();
        private Integer sendAfterSalesPolicyNum = 0;

        private List<Integer> completeCustomerTagList = new ArrayList<>();
        private Integer completeCustomerTagNum = 0;

        private List<Integer> completeContactPositionInfoRecordIdList = new ArrayList<>();
        private Integer completeContactPositionInfoNum = 0;

        private List<String> isWeChatAddedRecordIdList = new ArrayList<>();
        private Integer isWeChatAddedNum = 0;

        private List<Integer> needToSendContractInformationAddedRecordIdList = new ArrayList<>();
        private Integer needToSendContractInformationNum = 0;

        private List<Integer> otherMattersRecordIdList = new ArrayList<>();
        private Integer otherMattersNum = 0;

        private Integer total = 0;

    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Pie {
        /**
         * 数量
         */
        private Object value;
        private String name;
    }
}
