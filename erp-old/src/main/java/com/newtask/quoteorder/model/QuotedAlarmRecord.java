package com.newtask.quoteorder.model;

import com.vedeng.order.enums.QuotedAlarmLevelEnum;
import com.vedeng.order.enums.QuotedAlarmModeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
@Setter
public class QuotedAlarmRecord {

    public final static long DEFAULT_TIMESTAMP = 0;

    private Integer quoteOrderKey;

    private List<Integer> quotedGoodsIdList;

    /**
     * 报价模式
     *
     * @see QuotedAlarmModeEnum
     */
    private Integer mode;

    private Integer currentLevel;

    /**
     * 报价预警消息接受人id列表
     */
    @Deprecated
    private List<Integer> userIdList;

    /**
     * 首次标记时间
     */
    private Long firstMarkTime;


    /**
     * 下一次触发时间
     */
    private Long triggerTime;

    public QuotedAlarmRecord() {
        this.triggerTime = DEFAULT_TIMESTAMP;
        this.currentLevel = QuotedAlarmLevelEnum.NONE.getLevel();
    }

    /**
     * 是否设置触发时间
     *
     * @return
     */
    public boolean hasTriggerTime() {
        return triggerTime > 0L;
    }


    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append("quoteOrderKey=").append(quoteOrderKey).append(", ");
        stringBuilder.append("firstMarkTime=").append(firstMarkTime).append(", ");
        stringBuilder.append("triggerTime=").append(triggerTime).append(", ");
        stringBuilder.append("mode=").append(mode).append(", ");
        stringBuilder.append("level=").append(currentLevel);
        stringBuilder.append("}");
        return stringBuilder.toString();
    }

}