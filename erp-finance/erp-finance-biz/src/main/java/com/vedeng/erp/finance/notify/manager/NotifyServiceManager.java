package com.vedeng.erp.finance.notify.manager;

import cn.hutool.core.map.MapUtil;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.enums.NotifyTypeEnum;
import com.vedeng.erp.finance.notify.NotifyService;
import com.vedeng.erp.finance.notify.impl.EmailNotifyService;
import com.vedeng.erp.finance.notify.impl.SmsNotifyService;
import com.vedeng.erp.finance.notify.impl.WxAppNotifyService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 开票后 通知 适配器
 * @date 2024/1/22 15:49
 **/
@Service
public class NotifyServiceManager implements InitializingBean {

    @Autowired
    private EmailNotifyService emailNotifyService;

    @Autowired
    private SmsNotifyService smsNotifyService;

    @Autowired
    private WxAppNotifyService wxAppNotifyService;

    private final Map<NotifyTypeEnum, NotifyService> notifyServiceHashMap = MapUtil.newHashMap();


    @Override
    public void afterPropertiesSet() throws Exception {
        notifyServiceHashMap.put(NotifyTypeEnum.SMS, smsNotifyService);
        notifyServiceHashMap.put(NotifyTypeEnum.MAIL, emailNotifyService);
        notifyServiceHashMap.put(NotifyTypeEnum.WXAPP, wxAppNotifyService);
    }

    public void notify(NotifyTypeEnum notifyTypeEnum, InvoiceDto data) {
        NotifyService notifyService = notifyServiceHashMap.get(notifyTypeEnum);
        if (notifyService == null) {
            throw new ServiceException("此通知类型不存在");
        }
        notifyService.notifyMessage(data);
    }
}
