.vd-ui-date * {
  margin: 0;
  padding: 0;
  list-style: none;
  box-sizing: border-box;
}
.vd-ui-date {
  position: relative;
  display: inline-block;
  text-align: left;
}
.vd-ui-date .vd-ui-date-editor--year,
.vd-ui-date .vd-ui-date-editor--month,
.vd-ui-date .vd-ui-date-editor--date,
.vd-ui-date .vd-ui-date-editor--datetime,
.vd-ui-date .vd-ui-date-editor--datetimerange,
.vd-ui-date .vd-ui-date-editor--daterange {
  width: 252px;
}
.vd-ui-date .vd-ui-date-editor--time {
  width: 180px;
}
.vd-ui-date .vd-ui-date-editor /deep/ .vd-ui-input__inner {
  cursor: pointer;
}
.vd-ui-date-wrapper {
  position: absolute;
  box-sizing: border-box;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  z-index: 1500;
  background-color: #ffffff;
  list-style-type: none;
  margin: 0;
}
.vd-ui-date-wrapper--year,
.vd-ui-date-wrapper--month,
.vd-ui-date-wrapper--date {
  width: 294px;
}
.vd-ui-date-wrapper--datetime {
  width: 474px;
}
.vd-ui-date-wrapper--time {
  width: 180px;
}
.vd-ui-date-wrapper.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-date-wrapper.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-panel .vd-ui-panel-header {
  background: #fff;
  color: #333;
  height: 46px;
  margin: 0 auto;
  padding: 0;
  border-bottom: solid 1px #ddd;
}
.vd-ui-panel .vd-ui-panel-header > ul {
  margin: 0 auto;
  height: 46px;
  width: 90%;
  display: flex;
  align-items: center;
}
.vd-ui-panel .vd-ui-panel-header > ul > li {
  cursor: pointer;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.year-month {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow {
  width: 30px;
  text-align: center;
  color: #666;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow i {
  display: block;
  font-size: 16px;
  color: #666;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow.arrow-left1 i {
  transform: rotate(-90deg);
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow.arrow-right1 i {
  transform: rotate(90deg);
}
.vd-ui-panel .vd-ui-panel-body {
  position: relative;
  margin: 5px 15px 5px;
}
.vd-ui-panel .vd-ui-panel-body table {
  table-layout: fixed;
  width: 100%;
}
.vd-ui-panel .date-shortcuts {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  border-top: solid 1px #E1E5E8;
  padding: 9px 0;
}
.vd-ui-panel .date-shortcuts .item-sc {
  position: relative;
  font-size: 12px;
  color: #09F;
  padding: 4px 9px;
  cursor: pointer;
}
.vd-ui-panel .date-shortcuts .item-sc::after {
  content: "";
  display: block;
  width: 0;
  height: 12px;
  border-right: solid 1px #e3e3e3;
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.vd-ui-panel .date-shortcuts .item-sc:last-child::after {
  display: none;
}
.vd-ui-date-table {
  font-size: 12px;
  user-select: none;
  width: 100%;
}
.vd-ui-date-table td {
  width: 36px;
  height: 36px;
  box-sizing: border-box;
  text-align: center;
  cursor: pointer;
  position: relative;
}
.vd-ui-date-table td > div {
  height: 100%;
  padding: 0;
  box-sizing: border-box;
}
.vd-ui-date-table td span {
  width: 36px;
  height: 36px;
  display: block;
  margin: 0 auto;
  line-height: 34px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 3px;
}
.vd-ui-date-table td.next-month,
.vd-ui-date-table td.prev-month {
  color: #C0C4CC;
}
.vd-ui-date-table td.today {
  position: relative;
}
.vd-ui-date-table td.today span {
  color: #0099FF;
  font-weight: bold;
  border: solid 1px #0099FF;
}
.vd-ui-date-table td.today.start-date span,
.vd-ui-date-table td.today.end-date span {
  color: #FFF;
}
.vd-ui-date-table td.available:hover span {
  background-color: #F5F7FA;
}
.vd-ui-date-table td.available:active span {
  background-color: #EBEFF2;
}
.vd-ui-date-table td.in-range div {
  background-color: #e0f3ff;
}
.vd-ui-date-table td.in-range div:hover {
  background-color: #4e7a96;
}
.vd-ui-date-table td.in-range div:hover span {
  background-color: #50bcff;
}
.vd-ui-date-table td.current:not(.disabled) span {
  color: #FFF;
  background-color: #0099FF;
}
.vd-ui-date-table td.start-date div,
.vd-ui-date-table td.end-date div {
  color: #FFF;
}
.vd-ui-date-table td.start-date span,
.vd-ui-date-table td.end-date span {
  background-color: #0099FF;
}
.vd-ui-date-table td.start-date div {
  margin-left: 5px;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}
.vd-ui-date-table td.end-date div {
  margin-right: 5px;
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}
.vd-ui-date-table td.disabled div {
  background-color: #F5F7FA;
  opacity: 1;
  cursor: not-allowed;
  color: #C0C4CC;
}
.vd-ui-date-table td.selected div {
  margin-left: 5px;
  margin-right: 5px;
  background-color: #F2F6FC;
  border-radius: 15px;
}
.vd-ui-date-table td.selected div:hover {
  background-color: #F2F6FC;
}
.vd-ui-date-table td.selected span {
  background-color: #0099FF;
  color: #FFF;
  border-radius: 15px;
}
.vd-ui-date-table th {
  position: relative;
  font-weight: 400;
  color: #606266;
  height: 36px;
  text-align: center;
}
.vd-ui-year-table {
  font-size: 12px;
  border-collapse: collapse;
}
.vd-ui-year-table .el-icon {
  color: #303133;
}
.vd-ui-year-table td {
  text-align: center;
  padding: 5px 3px;
  cursor: pointer;
}
.vd-ui-year-table td.today .cell {
  color: #000;
  font-weight: bold;
}
.vd-ui-year-table td.disabled .cell {
  background-color: #F5F7FA;
  cursor: not-allowed;
  color: #999;
}
.vd-ui-year-table td.disabled .cell:hover {
  color: #999;
}
.vd-ui-year-table td .cell {
  width: 80px;
  height: 36px;
  display: block;
  line-height: 36px;
  color: #333;
  margin: 0 auto;
}
.vd-ui-year-table td .cell:hover {
  background-color: #F5F7FA;
}
.vd-ui-year-table td.current:not(.disabled) .cell {
  color: #fff;
  background: #409eff;
  border-radius: 3px;
}
.vd-ui-month-table {
  font-size: 12px;
  border-collapse: collapse;
}
.vd-ui-month-table td {
  text-align: center;
  padding: 0px;
  cursor: pointer;
}
.vd-ui-month-table td div {
  height: 48px;
  padding: 5px 0;
  box-sizing: border-box;
}
.vd-ui-month-table td.today .cell {
  color: #000;
  font-weight: bold;
}
.vd-ui-month-table td.today.start-date .cell,
.vd-ui-month-table td.today.end-date .cell {
  color: #fff;
}
.vd-ui-month-table td.disabled .cell {
  background-color: #F5F7FA;
  cursor: not-allowed;
  color: #999;
}
.vd-ui-month-table td.disabled .cell:hover {
  color: #999;
}
.vd-ui-month-table td .cell {
  width: 58px;
  height: 36px;
  display: block;
  line-height: 36px;
  color: #333;
  margin: 0 auto;
  border-radius: 3px;
}
.vd-ui-month-table td .cell:hover {
  background: #F5F7FA;
}
.vd-ui-month-table td.in-range div {
  background-color: #F2F6FC;
}
.vd-ui-month-table td.in-range div:hover {
  background-color: #F2F6FC;
}
.vd-ui-month-table td.start-date div,
.vd-ui-month-table td.end-date div {
  color: #fff;
}
.vd-ui-month-table td.start-date .cell,
.vd-ui-month-table td.end-date .cell {
  color: #FFF;
  background-color: #409EFF;
}
.vd-ui-month-table td.start-date div {
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.vd-ui-month-table td.end-date div {
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.vd-ui-month-table td.current:not(.disabled) .cell {
  background: #409EFF;
  border-radius: 3px;
  color: #fff;
}
.vd-ui-time-panel {
  width: 180px;
}
.vd-ui-time-panel .vd-ui-time-panel__header {
  height: 46px;
  font-size: 14px;
  color: #333333;
  line-height: 46px;
  text-align: center;
  border-bottom: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content {
  border-bottom: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list {
  font-size: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item {
  display: inline-block;
  vertical-align: top;
  width: 33.32%;
  height: 264px;
  overflow: hidden;
  border-right: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item:last-child {
  border-right: none;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul {
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-y: auto;
  padding-right: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li {
  position: relative;
  font-size: 14px;
  text-align: center;
  height: 33px;
  line-height: 33px;
  padding-left: 6px;
  cursor: pointer;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li:hover {
  background: #edf0f2;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li.active {
  color: #09f;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar-track {
  background: transparent;
  width: 0;
  height: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover {
  padding-right: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-time-panel .vd-ui-time-panel__footer {
  height: 40px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vd-ui-time-panel .vd-ui-time-panel__footer .time-cancel {
  border: none;
  background: none;
  font-size: 12px;
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-time-panel .vd-ui-time-panel__footer .time-confirm {
  width: 44px;
  height: 26px;
  border: none;
  background: #0099FF;
  border-radius: 3px;
  font-size: 12px;
  color: #ffffff;
  cursor: pointer;
}
.vd-ui-datetime-panel {
  width: 474px;
  box-sizing: border-box;
}
.vd-ui-datetime-panel * {
  box-sizing: border-box;
}
.vd-ui-datetime-panel .datetime-content {
  display: flex;
}
.vd-ui-datetime-panel .datetime-content .datetime-date-panel {
  width: 292px;
  border-right: solid 1px #E1E5E8;
}
.vd-ui-datetime-panel .datetime-content .datetime-time-panel {
  width: 180px;
}
.vd-ui-datetime-panel .datetime-btn {
  width: calc(100% - 2px);
  height: 40px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: solid 1px #edf0f2;
}
.vd-ui-datetime-panel .datetime-btn .time-cancel {
  border: none;
  background: none;
  font-size: 12px;
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-datetime-panel .datetime-btn .time-confirm {
  width: 44px;
  height: 26px;
  border: none;
  background: #0099FF;
  border-radius: 3px;
  font-size: 12px;
  color: #ffffff;
  cursor: pointer;
}
.vd-ui-range {
  width: 252px;
  height: 30px;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  padding: 3px 10px;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
}
.vd-ui-range .icon-date {
  font-size: 16px;
  color: #666;
  margin-right: 4px;
}
.vd-ui-range .range-error2 {
  width: 16px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-range .range-error2 .icon-error2 {
  font-size: 16px;
  color: #666;
  cursor: pointer;
}
.vd-ui-range .range-error2 .icon-error2:hover {
  color: #333;
}
.vd-ui-range .split {
  font-size: 12px;
  color: #333;
  margin: 0 5px;
}
.vd-ui-range .input-box {
  flex: 1;
  height: 100%;
  cursor: pointer;
}
.vd-ui-range .input-box input {
  appearance: none;
  border: none;
  outline: none;
  display: inline-block;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 12px;
  color: #333;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.1s linear;
}
.vd-ui-range .input-box input::placeholder {
  color: #999;
  font-size: 12px;
}
.vd-ui-range .input-box input:hover {
  border-color: #969B9E;
}
.vd-ui-daterange-panel * {
  box-sizing: border-box;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper {
  position: relative;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__sidebar {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100px;
  border-right: 1px solid #e4e4e4;
  box-sizing: border-box;
  padding-top: 15px;
  background-color: #fff;
  overflow: auto;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__sidebar .vd-ui-panel__shortcut {
  display: block;
  padding-left: 12px;
  width: 100%;
  background-color: transparent;
  border: 0;
  font-size: 12px;
  color: #09F;
  text-align: left;
  margin-bottom: 15px;
  outline: none;
  cursor: pointer;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content {
  float: left;
  width: 50%;
  box-sizing: border-box;
  margin: 0;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content.is-left {
  border-right: solid 1px #edf0f2;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header {
  position: relative;
  background: #fff;
  height: 46px;
  color: #333;
  margin: 0 auto;
  padding: 0;
  border-bottom: solid 1px #ddd;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header .header-label {
  margin: 0 50px;
  text-align: center;
  line-height: 46px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow {
  font-size: 12px;
  color: #333;
  border: 0;
  background: 0 0;
  cursor: pointer;
  outline: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow1 {
  left: 10px;
  transform: translateY(-50%) rotate(-90deg);
  margin-top: -1px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow2 {
  left: 30px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow3 {
  right: 30px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow4 {
  right: 10px;
  transform: translateY(-50%) rotate(90deg);
  margin-top: -1px;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.disable {
  color: #ccc;
  cursor: not-allowed;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul {
  margin: 0 auto;
  width: 100%;
  height: 46px;
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li {
  cursor: pointer;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.year-month {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow {
  width: 30px;
  text-align: center;
  color: #666;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow i {
  display: block;
  font-size: 16px;
  color: #666;
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow.arrow-left1 i {
  transform: rotate(-90deg);
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow.arrow-right1 i {
  transform: rotate(90deg);
}
.vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body::after {
  content: "";
  display: block;
  clear: both;
}
.vd-ui-date-range-wrap {
  display: flex;
  align-items: center;
}
.vd-ui-date-range-wrap .vd-ui-date-range-gap {
  padding: 0 5px;
  display: flex;
  align-items: center;
}
