package com.vedeng.erp.system.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义操作类型枚举类
 * @date 2022/7/15 16:58
 */
public enum CustomDataOperTypeEnums {

    //置顶
    TOP(1),
    //取消置顶
    UN_TOP(-1),

    //关注
    ATTENTION(2),
    //取消关注
    CANCEL_ATTENTION(-2);


    private final Integer type;


    public Integer getType() {
        return type;
    }

    CustomDataOperTypeEnums(Integer type) {
        this.type = type;
    }
}
