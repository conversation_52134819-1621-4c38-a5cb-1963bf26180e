<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增沟通记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="formpublic">
        <form method="post" action="" id="addCommunicate">
            <ul>
<c:if test="${not empty traders}">
                <li>
                    <div class="infor_name">
                        <lable>客户名称</lable>
                    </div>
                    <div class="f_left">
                        <div>
                            <select id="traderInfoSelect" onchange="changeTraderInfo()">
                                <c:if test="${fn:length(traders) > 1}">
                                    <option selected="selected" value="0">请选择客户名称</option>
                                </c:if>
                                <c:if test="${not empty traders}">
                                    <c:forEach	items="${traders}" var="trader">
                                        <option value="${trader.traderId}" salesNameStr="${trader.salesNameStr}" data-nature="${trader.customerNature}"
                                                <c:if test="${fn:length(traders) == 1}">selected</c:if>
                                                <c:if test="${callOut.traderId == trader.traderId}">selected</c:if>
                                        >${trader.traderName}</option>
                                    </c:forEach>
                                </c:if>
                            </select>

                        </div>
                        <div id="traderNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="infor_name">
                        <lable>归属销售</lable>
                    </div>
                    <div class="f_left">
                        <div id="salesNameStrDiv">
                            <c:choose>
                                <c:when test="${fn:length(traders) == 1}">
                                    ${traders[0].salesNameStr}
                                </c:when>
                                <c:otherwise>
                                    <c:forEach	items="${traders}" var="trader">
                                        <c:if test="${callOut.traderId == trader.traderId}">${trader.salesNameStr}</c:if>
                                    </c:forEach>
                                </c:otherwise>
                            </c:choose>
                        </div>
                        <div id="salesNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>联系人</lable>
                    </div>
                    <div class="f_left  ">
                        <select class="mr5" name="traderContactId" id="traderContactId">
                            <c:choose>
                                <c:when test="${contactList.size() == 0}">
                                    <option selected="selected" value="0">请添加联系人</option>
                                </c:when>
                                <c:otherwise>
                                    <option selected="selected" value="0">请选择</option>
                                    <c:if test="${not empty contactList }">
                                        <c:forEach	items="${contactList }" var="contact">
                                            <option value="${contact.traderContactId }"
                                                    <c:if test="${contact.telephone == callOut.phone || contact.mobile == callOut.phone|| contact.mobile2 == callOut.phone}">
                                                        selected="selected"
                                                    </c:if>
                                                    data-position="${contact.position}">
                                                    ${contact.name }
                                                <c:if test="${contact.telephone !='' and contact.telephone != null }">|${contact.telephone }</c:if>
                                                <c:if test="${contact.mobile !='' and contact.mobile != null }">|${contact.mobile }</c:if>
                                                <c:if test="${contact.mobile2 !=''and contact.mobile2 != null}">|${contact.mobile2 }</c:if>
                                            </option>
                                        </c:forEach>
                                    </c:if>
                                </c:otherwise>
                            </c:choose>
                        </select>
                    </div>
                    <div class="title-click   pop-new-data" id="addTraderContactDiv" style='float:left;margin:-4px 0 0 10px;'
                         layerParams='{"width":"700px","height":"500px","title":"新增联系人","link":"${pageContext.request.contextPath}/order/bussinesschance/addTraderContact.do?traderId=${callOut.traderId}"}'>
                        添加联系人
                    </div>
                </li>
</c:if>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>沟通时间</lable>
                    </div>
                    <div class="f_left ">
                        <input class="Wdate f_left input-small mr4" type="text" placeholder="请选择时间" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'#F{$dp.$D(\'end\')}'})" name="begin" id="begin" value="${startTime}"/>
                        <div class="gang mr5">-</div>
                        <input class="Wdate f_left input-small" type="text" placeholder="请选择时间" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'begin\')}'})" name="end" id="end" value="${endTime}"/>
	                    <div id="timeErrorMsg"></div>
                    </div>
                </li>
                <c:if test="${ bussinessType eq 391 || bussinessType eq 394}">
                    <li>
                        <div class="infor_name">
                            <span>*</span>
                            <lable>商机精准度</lable>
                        </div>
                        <div class="f_left">
                            <div>
                                <select class="mr5" name="businessChanceAccuracy" id="businessChanceAccuracy">
                                    <option value="-1">请选择</option>
                                    <option value="0" <c:if test="${businessChanceAccuracy== 0}">selected="selected"</c:if>>无法判断</option>
                                    <option value="1" <c:if test="${businessChanceAccuracy == 1}">selected="selected"</c:if>>不精准</option>
                                    <option value="2" <c:if test="${businessChanceAccuracy== 2}">selected="selected"</c:if>>一般精准</option>
                                    <option value="3" <c:if test="${businessChanceAccuracy == 3}">selected="selected"</c:if>>高精准</option>
                                </select>
                                <c:if test="${ belongPlatfromByOrgAndUser eq 0}">
                                    <div class="tip-wrap cus-tip"  style="display:inline;">
                                        <i class="vd-icon icon-info2" style="background: none">
                                            <div class="tip arrow-left">
                                                <div class="tip-con">
                                                    高精准：客户属于本部门经营对象，购买意向为公司签约商品或者一年内有成交过的商品；
                                                    <br/>一般精准：客户属于本部门经营对象，但当前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                                                    <br/>不精准：客户不属于本部门经营对象，且目前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                                                    <br/>无法判断：未接通（连续三天联系不上）
                                                </div>
                                            </div>
                                            <span class="arrow arrow-out">
               <span class="arrow arrow-in"></span>
              </span>
                                        </i>
                                    </div>
                                </c:if>
                                <c:if test="${ belongPlatfromByOrgAndUser eq 1}">
                                    <div class="tip-wrap cus-tip"  style="display:inline;">
                                        <i class="vd-icon icon-info2" style="background: none">
                                            <div class="tip arrow-left">
                                                <div class="tip-con">
                                                    高精准：客户有明确购买意向，且需求与我司匹配
                                                    <br/>一般精准：客户有明确购买意向，但需求与我司不匹配
                                                    <br/>不精准：客户目前没有明确购买意向
                                                    <br/>无法判断：未接通（连续三天联系不上）
                                                </div>
                                            </div>
                                            <span class="arrow arrow-out">
               <span class="arrow arrow-in"></span>
              </span>
                                        </i>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                    </li>
                </c:if>
                <li style="margin-top:-5px;">
                    <div class="infor_name mt0">
                        <span>*</span>
                        <lable>沟通内容</lable>
                    </div>
                    <div class="f_left table-largest">
						<div class="inputfloat manageaddtag">
							<label class=" mr8">您可以从这些标签中选择</label>
							<c:if test="${not empty tagList }">
								<c:forEach items="${tagList }" var="tag">
									<span onclick="addTag(${tag.tagId},'${tag.tagName }',this);">${tag.tagName }</span>
								</c:forEach>
							</c:if>
							<c:if test="${page.totalPage > 1}">
							<div class="change" onclick="changeTag(${page.totalPage},10,this,32);"><span class="m0">换一批(</span><span class="m0" id="leftNum">${page.totalPage}</span><span class="m0">)</span>
							<input type="hidden" id="pageNo" value="${page.pageNo}">
							</div>
							</c:if>
						</div>
						<div class="inputfloat <c:if test="${empty tagList }">mt8</c:if>">
							<input type="text" id="defineTag" placeholder="如果标签中没有您所需要的，请自行填写"
								class="input-large">
							<div class="f_left bt-bg-style bg-light-blue bt-small  addbrand" onclick="addDefineTag(this);">添加</div>
						</div>
						<div class="addtags">
							<ul id="tag_show_ul">
							</ul>
						</div>
                        <div>
                                <textarea  name="contentSuffix" placeholder="沟通内容最多输入200字符，请检查后提交"
                                           style="width: 450px; height: 100px"></textarea>
                            <div id="contentSuffixError" ></div>
                        </div>
					</div>
                </li>

            <c:if test="${traderCustomerDto.customerNature == 465 || traderCustomerDto.customerNature == 466}">
                <li>
                    <input hidden="hidden" id="allPosition" name="allPosition" value=""/>
                    <div class="infor_name">
                        <label><span style="color: red;margin-right: 5px">*</span>联系人职位</label>
                    </div>
                    <div class="f_left commonuse table-largest">
                        <div class="position">
                            <!-- 复选框选项 -->
                            <c:if test="${traderCustomerDto.customerNature == 465}">
                                <label>
                                    <input type="checkbox" name="position" value="老板" /> 老板
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="销售负责人" /> 销售负责人
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="销售经理" /> 销售经理
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="采购负责人" /> 采购负责人
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="采购经理" /> 采购经理
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="商务人员" /> 商务人员
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="财务人员" /> 财务人员
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="物流人员" /> 物流人员
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="售后人员" /> 售后人员
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="临床工程师" /> 临床工程师
                                </label>
                            </c:if>
                            <c:if test="${traderCustomerDto.customerNature == 466}">
                                <label>
                                    <input type="checkbox" name="position" value="院长" /> 院长
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="设备科人员" /> 设备科人员
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="临床医生" /> 临床医生
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="采购负责人" /> 采购负责人
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="采购经理" /> 采购经理
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="运营人员" /> 运营人员
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="财务人员" /> 财务人员
                                </label>
                                <label>
                                    <input type="checkbox" name="position" value="医院库管" /> 医院库管
                                </label>
                            </c:if>
                            <label>
                                <input type="checkbox" name="position" value="其他" id="otherCheckbox" /> 其他
                            </label>
                            <!-- 其他职位的输入框 -->
                            <input type="text" name="otherPosition" id="otherPosition" class="input-largest" placeholder="请填写具体职位，最多50字" maxlength="50" style="display: none;width: 400px;margin-left: -30px" />
                        </div>
                        <div id="positionError" style="color: red;"></div> <!-- 错误提示 -->
                    </div>
                </li>
            </c:if>
                
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>下次沟通时间</lable>
                    </div>
                    <div class="f_left inputfloat ">
                        <input class="Wdate input-small" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'end\')}'})" name="nextDate" id="nextDate"/>
                    </div>
                    <input type="checkbox" style="cursor: pointer;" id="noneNextDate">
                    <span id="noneNextDateSpan" style="cursor: pointer;">暂无下次沟通时间</span>
                    <input type="hidden" id="noneNextDateVal" name="noneNextDate">
                </li>
                <li>
                    <div class="infor_name">
                        <lable>下次沟通内容</lable>
                    </div>
                    <div class="f_left  ">
                        <input type="text" class="input-xxx" name="nextContactContent" id="nextContactContent">
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <lable>备注</lable>
                    </div>
                    <div class="f_left  ">
                        <input type="text" class="input-xxx" name="comments" id="comments">
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter">
            	<input type="hidden" id="traderId" name="traderId" value="${callOut.traderId}" >
                <input type="hidden" id="phone" name="phone" value="${callOut.phone}">
                <input type="hidden" id="coid" name="coid" value="${callOut.coid}">
                <input type="hidden" id="communicateType" name="communicateType" value="${communicateType}">
                <input type="hidden" id="callType" name="callType" value="${callOut.callType}">
                <input type="hidden" id="orderId" name="orderId" value="${callOut.orderId}">
                <input type="hidden" id="relatedId" name="relatedId" value="${relatedId}">
            	<input type="hidden" name="traderType" value="1" >
            	<input type="hidden" name="callFrom" value="1" >
                <%-- 去电--%>
                <input type="hidden" name="communicateMode" value="250">
            	<input type="hidden" name="communicateType" value="0" >
            	<input type="hidden" name="coidType" value="2" >
            	<input type="hidden" id="communicateTime" name="communicateTime" value="${communicateTime}">
                <button type="submit" id="submit">提交</button>
            </div>
        </form>
    </div>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/add_communicate.js?rnd=${resourceVersionKey}"></script>
	<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<script type="text/javascript">
    $(document).ready(function() {
        setTimeout(function(){
            $.ajax({
                url: "${pageContext.request.contextPath}/system/call/pushVoiceMp3.do",
                type: "post",
                timeout: 60000,
                data: {
                    "coid":'${param.coid}'
                },
                dataType : "json",
                success: function(data) {
                    console.log("通话完成，发起AI解析。");
                }
            });
        }, 2000);
    });


</script>
    <style>
    .cus-tip .icon-info2 {
        color:  #09f;
        vertical-align: -3px;
    }

    .cus-tip .tip {
        z-index: 9999; /* 一个较大的数值 */
        max-width: 500px;
        display: none;
    }

    .cus-tip:hover .tip {
        display: block;
    }
    .position{
        width: 600px;
    }
    .position label{
        width: 100px;
        padding: 1px 10px;
    }
    </style>
<%@ include file="../../common/footer.jsp"%>
