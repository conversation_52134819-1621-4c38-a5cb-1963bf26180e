<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.PlatformMapper">
	<resultMap id="BaseResultMap" type="com.vedeng.authorization.model.Platform">
		<id column="PLATFORM_ID" property="platformId" jdbcType="INTEGER" />
		<result column="PLATFORM_NAME" property="platformName" jdbcType="VARCHAR" />
	</resultMap>
	<!-- 获取应用平台 -->
	<select id="queryPlatformList" resultMap="BaseResultMap">
		select PLATFORM_ID,PLATFORM_NAME from T_PLATFORM order by PLATFORM_ID ASC
	</select>

	<select id="queryPlatformById" resultMap="BaseResultMap">
		select PLATFORM_ID,PLATFORM_NAME from T_PLATFORM where PLATFORM_ID = #{id}
	</select>

</mapper>