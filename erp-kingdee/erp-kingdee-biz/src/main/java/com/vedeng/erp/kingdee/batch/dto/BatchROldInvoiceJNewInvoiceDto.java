package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/12/7 13:33
 **/
/**
    * 新旧发票的关系表
    */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchROldInvoiceJNewInvoiceDto {
    private Integer rOldInvoiceJNewInvoiceId;

    /**
    * 原蓝字发票ID
    */
    private Integer oldInvoiceId;

    /**
    * 新蓝字发票ID
    */
    private Integer newInvoiceId;

    /**
    * 仅退票的金额
    */
    private BigDecimal invoiceAmount;

    /**
    * 是否删除0否1是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;
}