package com.vedeng.erp.wms.dto;

import java.math.BigDecimal;
import java.util.Date;

import lombok.*;

/**
    * 出入库订单额外信息表
    */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsOutputOrderExtra {
    /**
    * 主键
    */
    private Long outputOrderExtraId;

    /**
    * 出库单的id
    */
    private Long wmsOutputOrderId;

    /**
    * 申请类型
    */
    private Integer applyType;

    /**
    * 申请总金额
    */
    private BigDecimal applyAmount;

    /**
    * 活动编码
    */
    private String activityCode;

    /**
    * 活动名称
    */
    private String activityName;

    /**
    * 收货人ID
    */
    private Integer receiverId;

    /**
    * 收货地区ID
    */
    private Integer receiverAddressId;

    /**
    * 是否删除 0否 1是
    */
    private Boolean isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;
}