package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchExpressCostDto;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

@Service
@Slf4j
public class ExpressCostPushAddProcessor extends BaseProcessor<BatchExpressCostDto,KingDeeExpressCostDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public KingDeeExpressCostDto doProcess(BatchExpressCostDto batchExpressCostDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("[add]ExpressCostPushAddProcessor.doProcess，batchExpressCostDto：{}", JSONObject.toJSONString(batchExpressCostDto));

        KingDeeExpressCostDto queryDto = KingDeeExpressCostDto.builder().fQzokBddjbh(batchExpressCostDto.getUniqueKey()).build();
        if (kingDeeBaseApi.isExist(queryDto)) {
            log.info("新增快递成本数据已推送到金蝶:{}", JSON.toJSONString(queryDto));
            return null;
        }

        return KingDeeExpressCostDto.builder()
                .fid("0")
                .fBillNo("")
                .fQzokYsddh(batchExpressCostDto.getSaleorderNo())
                .fQzokGsywdh(batchExpressCostDto.getSaleorderNo())
                .fQzokCrkdh(batchExpressCostDto.getOutInNo())
                .fQzokKddh(batchExpressCostDto.getLogisticsNo())
                .fQzokWlbm(batchExpressCostDto.getSku())
                .fQzokXlh(batchExpressCostDto.getBarcodeFactory())
                .fQzokPch(batchExpressCostDto.getBatchNumber())
                .fQzokFhsl(String.valueOf(batchExpressCostDto.getShareNum()))
                .fQzokCb(batchExpressCostDto.getShareExpressAmountNoTax() != null ? batchExpressCostDto.getShareExpressAmountNoTax().toPlainString() : "0.00000000")
                .fQzokSjr(batchExpressCostDto.getTakeTraderContactName())
                .fQzokDh(batchExpressCostDto.getTakeTraderContactTelephone())
                .fQzokDz(batchExpressCostDto.getTakeTraderAddress())
                .fQzokBddjbh(batchExpressCostDto.getUniqueKey())
                .fQzokSfsc(Boolean.FALSE.toString())
                .fQzokSfjrcb("Y")
                .FQzokWlgs(batchExpressCostDto.getLogistics())
                .FQzokSfzp(Objects.nonNull(batchExpressCostDto.getIsGift()) && KingDeeConstant.ONE.equals(batchExpressCostDto.getIsGift()) ? 1 : 0)
                .build();
    }
}
