package com.vedeng.workbench.model.dto.emptyDto;

import com.vedeng.workbench.model.dto.BussinessCommunicateRecordDto;

import java.util.Date;

/**
 * 初始化空值
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/7 21:08.
 * @author: <PERSON><PERSON>.
 */
public class NullBussinessCommunicateRecordDto  extends BussinessCommunicateRecordDto {

    public NullBussinessCommunicateRecordDto() {
        this.setEndDate(new Date());
        this.setCommunicateRecordId(-1);
        this.setEndTime(-1);
        this.setNextContactContent("默认");
        this.setNextContactDate(new Date());
    }
}
