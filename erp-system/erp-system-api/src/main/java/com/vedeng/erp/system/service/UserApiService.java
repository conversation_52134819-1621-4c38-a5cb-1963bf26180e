package com.vedeng.erp.system.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.system.common.enums.UserOrgEnums;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.vo.UserSmartQuoteVO;


import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.user.api
 * @Date 2022/7/13 10:13
 */
public interface UserApiService {

    UserDto searchByUserIdFromUac(Integer userId);

    /**
     * 所有主管的人员，请注意：去掉了David.ding。不要发消息给老板
     * @return
     */
    List<UserDto> searchIsParentFromUac();


    /**
     * 从UAC获取某个userId的所有下级。
     * @param userId
     * @return
     */
    List<UserDto>  queryUserSubFromUac(Integer userId);

    List<Integer>  queryUserIdListSubFromUac(Integer userId);


    /**
     * 获取所有用户信息，不包括禁用用户
     *
     * @return
     */
    List<UserDto> getAllUserNotDisabled(String name);


    /**
     * 获取当前用户的下级和下级的下级用户信息集合，包括本人
     *
     * @param userDto          当前用户信息
     * @param positionType     需要检索的职位类型集合 null为全部 ； 如果当前用户在职位类型集合内，则查子集
     * @param haveDisabledUser 是否包含禁用用户 true包含
     * @return 用户信息集合
     */
    List<UserDto> getAllSubUserList(UserDto userDto, List<Integer> positionType, boolean haveDisabledUser);

    /**
     *  根据当前用户自定义的ORG_IDS_LIST获取以下权限的用户列表，getMyUserList，可配置这个方法一起使用
     * @param userDto
     * @param positionType
     * @param haveDisabeldUser
     * @return
     */
    List<UserDto> getMyUserListByUserOrgsList(UserDto userDto, List<Integer> positionType, boolean haveDisabeldUser);

    /**
     * 根据部门id 职位类型 查用户集合信息
     * @param orgIds 部门
     * @param positionType 职位类型
     * @param haveDisabledUser 是否包含禁用用户 true 包含
     * @return
     */
    List<UserDto> getUserByPositionTypeAndOrg(List<Integer> orgIds, List<Integer> positionType, boolean haveDisabledUser);

    /**
     * 根据用户id查询用户信息
     * @param userId 用户id
     * @return 用户信息
     */
    UserDto getUserById(Integer userId);

    /**
     * 根据用户id查询用户主部门信息
     * @param userId 用户id
     * @return 用户信息
     */
    UserDto getUserByIdGetMainOrg(Integer userId);


    UserDto getUserBaseInfo(Integer userId);

    /**
     * 根据工号来查询个人信息
     * @param jobNumber
     * @return
     */
    UserDto getUserBaseInfoByJobNumber(String jobNumber);


    /**
     * 根据用户id批量查询用户信息
     *
     * @param userIds 用户id集合
     * @return List<UserDto>
     */
    List<UserDto> getUserInfoByUserIds(List<Integer> userIds);

    /**
     * 获取当前登录人顶级部门下所有销售
     * @return
     */
    List<UserDto> getUserList();

    List<UserDto> getUserList(List<Integer> orgIdList);

    /**
     * 根据客户获取销售信息
     * @param traderId 客户id
     * @return UserDto
     */
    UserDto getUserByTraderId(Integer traderId);

    List<String> getUserListByPositionId(Integer positionId);

    /**
     * 获取用户的所有非禁用下属(从old中的userService迁移)
     *
     * @param userId 用户id
     * @return 下属集合
     */
    List<Integer> getAllSubordinateByUserId(Integer userId);

    /**
     * 获取用户的所有下属(包含禁用用户)
     *
     * @param userId 用户id
     * @return 下属集合
     */
    List<Integer> getAllSubordinateHaveDisabledUser(Integer userId);

    /**
     * 查询自己及下属
     * @param userId
     * @return
     */
    List<Integer> getAllSubordinateByUserIdForVisit(Integer userId);

    /**
     * 批量根据客户id查询归属销售信息
     *
     * @param traderIdList 客户id集合
     * @return 归属销售信息
     */
    List<UserDto> getUserByTraderIdList(List<Integer> traderIdList);

    /**
     * 根据工号获取user信息（提供给贝壳助理）
     */
    UserSmartQuoteVO getUserPermissionForAIQuote(Integer jobNumber);

    List<UserDto> getUserByNumberlist(List<Integer> numberList);

    /**
     * 根据职位类型查询职位下的所有用户，例如 310、311
     *
     * @param positionType T_POSITION TYPE
     * @return List<UserDto>
     */
    List<UserDto> getUserByPositionType(Integer positionType);


    /**
     * 优先判断当前人员是否在6个大区，需要进行ID的apollo配置。为拜访计划服务
     * @param userId
     * @return
     */
    UserOrgEnums getUserOrgEnum(Integer userId);

    boolean checkUserIsB2b(Integer userId,Integer positionType);
    
    List<String> getOrgNameList(Integer userId);
    
    List<Integer> getAllSubUserList(CurrentUser currentUser);

    /**
     * 获取某个角色下的所有用户
     */
    List<UserDto> getUserByRoleId(String roleName);

}
