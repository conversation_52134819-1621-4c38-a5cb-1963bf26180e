$(function(){
	var	url = page_url + '/order/quote/getQuoteDetail.do?quoteorderId='+$("#quoteorderId").val()+'&viewType=3';
	if($(window.frameElement).attr('src').indexOf("viewType=3")<0){
		$(window.frameElement).attr('data-url',url);
	}
});
window.linkBd=function (param) {
	if(param==1){
		window.location.reload();
	}
}
function revokeValId(){
	checkLogin();
	layer.confirm("您是否确认撤销该报价生效状态？", {
		btn : [ '确定', '取消' ]// 按钮
	}, function() {
		$(window.frameElement).attr('data-url','');
		$("#viewQuoteOperationForm").submit();
	});
}
function printBj(obj){
	
	var url =page_url+"/order/quote/printOrder.do?quoteorderId="+obj;
	var name="打印报价单";
	var w=800;
	var h=600;
	window.open(url,name,"top=100,left=400,width=" + w + ",height=" + h + ",toolbar=no,menubar=no,scrollbars=yes,resizable=no,location=no,status=no");
}
function quoteorderToSaleorder(quoteorderId) {
	checkLogin();
	index = layer.confirm("是否确认转为订单?", {
		btn : [ '是', '否' ]// 按钮
	}, function() {
		var formToken = $("input[name='formToken']").val();
		$.ajax({
			async : false,
			url : page_url + '/order/saleorder/preQuoteorderToSaleorder.do',
			data : {"quoteorderId":quoteorderId,"formToken":formToken},
			type : "POST",
			dataType : "json",
			success : function(data) {
				if (data.code == 0) {
					layer.close(index);
					window.location.href = page_url + "/order/saleorder/quoteordertosaleorder.do?quoteorderId="+quoteorderId;
				}else if(data.code == -1){
					layer.alert(data.message);
					location.reload();
				}
				else {
					if (data.status == 2) {
						layer.close(index);
						index1 = layer.confirm(data.message, {
							btn : [ '是', '否' ]// 按钮
						}, function() {
							layer.close(index1);
							window.location.href = page_url + "/order/saleorder/quoteordertosaleorder.do?quoteorderId="+quoteorderId;
						});
					} else if (data.status == 1) {
						layer.alert(data.message);
					}
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	});
}

function orderStreamQuoteToSaleOrder(quoteorderId) {

	checkLogin();
	index = layer.confirm("是否确认转为订单?", {
		btn : [ '是', '否' ]
	}, function() {
		var formToken = $("input[name='formToken']").val();
		$.ajax({
			async : false,
			url : page_url + '/orderstream/saleorder/preQuoteToSaleOrder.do',
			data : {"quoteOrderId":quoteorderId,"formToken":formToken},
			type : "POST",
			dataType : "json",
			success : function(data) {
				if (data.code == 0) {
					layer.close(index);
					window.location.href = page_url + "/orderstream/saleorder/quoteToSaleOrder.do?quoteOrderId="+quoteorderId;
				}else if(data.code == -1){
					layer.alert(data.message);
					location.reload();
				}
				else {
					if (data.status == 2) {
						layer.close(index);
						index1 = layer.confirm(data.message, {
							btn : [ '是', '否' ]
						}, function() {
							layer.close(index1);
							window.location.href = page_url + "/orderstream/saleorder/quoteToSaleOrder.do?quoteOrderId="+quoteorderId;
						});
					} else if (data.status == 1) {
						layer.alert(data.message);
					}
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	});
}






function authorizationApply(quoteorderId,num,optUserName) {
	checkLogin();
	if (num==0){
		layer.alert("此报价单中不含公司产品库中产品，需要添加后才可申请授权书")
	} else {
		$.ajax({
			async : false,
			url : page_url + '/order/quote/operationAuthority.do',
            data : {"quoteorderId":quoteorderId,"optUserName":optUserName},
			type : "POST",
			dataType : "json",
			success : function(data) {
				if (data.code == 0) {
				    if (data.data==0) {
					/*window.location.href = page_url + "/order/quote/apply.do?quoteorderId="+quoteorderId+"&num="+num;*/
						url=page_url + "/order/quote/apply.do?quoteorderId="+quoteorderId;
						$("#addAuthorization").attr('tabTitle','{"num":"authorizationview'+quoteorderId+'","title":"授权书申请","link":"'+url+'"}');
						$("#addAuthorization").click();
                    }else {
                        index = layer.confirm(data.message, {
                            btn: ['查看授权','继续申请'] //按钮
                        }, function(){
							url = page_url + "/order/quote/authorizationView.do?quoteorderId="+quoteorderId;
							$("#addAuthorization").attr('tabTitle','{"num":"authorizationlook'+quoteorderId+'","title":"授权书申请中","link":"'+url+'"}');
							$("#addAuthorization").click();
							layer.closeAll()
                        }, function(){
                            /*window.location.href = page_url + "/order/quote/apply.do?quoteorderId="+quoteorderId;*/
							url=page_url + "/order/quote/apply.do?quoteorderId="+quoteorderId;
							$("#addAuthorization").attr('tabTitle','{"num":"authorizationview'+quoteorderId+'","title":"授权书申请","link":"'+url+'"}');
							$("#addAuthorization").click();
                        });
                    }
				}else if(data.code == -1){
					layer.alert(data.message);
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}

