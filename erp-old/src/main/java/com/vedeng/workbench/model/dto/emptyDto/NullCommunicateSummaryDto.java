package com.vedeng.workbench.model.dto.emptyDto;


import com.vedeng.workbench.model.dto.CommunicateSummaryDto;

import java.math.BigDecimal;

/**
 * 防止空指针
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/7 22:04.
 * @author: <PERSON><PERSON>.
 */
public class NullCommunicateSummaryDto extends CommunicateSummaryDto {

    public NullCommunicateSummaryDto() {
        this.setCompletionRate(BigDecimal.ZERO);
        this.setInPlaningNum(0);
        this.setName("默认");
        this.setOutPlaningNum(0);
        this.setPlaningCommunicateNum(0);
        this.setTodayToCommunicateNum(0);
    }
}
