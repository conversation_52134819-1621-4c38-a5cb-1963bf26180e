package com.vedeng.erp.aftersale.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.aftersale.domain.entity.AfterBuyorderInvoiceEntity;
import com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.stereotype.Repository;

@Repository
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface AfterBuyorderInvoiceConvert extends BaseMapStruct<AfterBuyorderInvoiceEntity, AfterBuyorderInvoiceDto>{
}