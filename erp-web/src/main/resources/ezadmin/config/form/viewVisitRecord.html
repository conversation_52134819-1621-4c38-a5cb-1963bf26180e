<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>拜访记录</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="viewVisitRecord" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append">
      <style>
          .layui-quote-nm{display: none;}
      </style>
  </div>
  <div class="layui-container"> 
   <form id="inputForm" method="post" class="layui-form">

      <div class="layui-form-item ">
          <label class="layui-form-label">计划拜访时间</label>
            <div class="layui-input-inline form-group">
            <input    lay_verify="required"
                   type="text"    class="layui-input form-item"
                   value=""  name="PLAN_VISIT_DATE" item_desc="计划拜访时间" maxlength="200">
            </div>
      </div>
      <div class="layui-form-item ">
           <label class="layui-form-label">拜访目标</label>
           <div class="layui-input-inline form-group">
               <input  readonly="readonly"
                         type="text"   class="layui-input form-item"
                         value=""  name="VISIT_TARGET" item_desc="拜访目标">
           </div>
       </div>




   </form> 
   <form id="configForm" class="layui-form"> 
    <div class="layui-card" >
     <div class="layui-card-header">
       表单数据配置 
     </div> 
     <div class="layui-card-body"> 
     </div> 
    </div> 
    <div class="layui-form-item "><label class="layui-form-label">初始表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("SELECT
        *
FROM
	T_VISIT_RECORD a

WHERE
	ID= ${recordId}");
return list.get(0);</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">if(null==ID||"".equals(ID)){
          sessionUserId=$$("EZ_SESSION_USER_ID_KEY");
          insert("INSERT INTO T_ASK_QUESTION_MANAGEMENT(QUESTION,ANSWER,DOC_TYPE,CREATOR,UPDATER,MOD_TIME) VALUES(#{QUESTION},#{ANSWER},5,#{EZ_SESSION_USER_ID_KEY},#{EZ_SESSION_USER_ID_KEY},NOW())");
     return 0;
}else{
update("
UPDATE T_ASK_QUESTION_MANAGEMENT
SET ANSWER = #{ANSWER},
QUESTION = #{QUESTION},
UPDATER = #{EZ_SESSION_USER_ID_KEY},
VECTOR_STATUS =2 ,
MOD_TIME = NOW()
WHERE
        ID = #{ID}");
return ID;
}</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type="">
          <!--if(null!=ID||IS_DELETE ==0){-->
            <!--update("UPDATE T_ASK_QUESTION_MANAGEMENT  SET IS_DELETE = 1, UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          <!--}else{-->
            <!--update(" UPDATE T_ASK_QUESTION_MANAGEMENT SET IS_DELETE = 0, UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          <!--}-->
          update(" UPDATE T_ASK_QUESTION_MANAGEMENT SET IS_DELETE = abs(IS_DELETE-1), VECTOR_STATUS =2 ,UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");
          return ID;
      </pre>
     </div> 
    </div>

   </form> 
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
  <div id="APPEND_FOOT" class="append"> 
   <script>
        var id=  $("#ID").val();
        if(id == undefined || id == ""){
            $("div[item_name='DOC_TYPE']").hide();
        }


        $(function(){
            // updateCharCount($('textarea[name="ANSWER"]')[0]);
            // $('textarea[name="ANSWER"]').attr("maxlength",600);
            // $('input[name="QUESTION"]').attr("maxlength",200);
            // $('textarea[name="ANSWER"]').on('input', function(e) {
            //     updateCharCount(this);
            // });
            
            // $("#submitbtn").click(function () {
            //     var question = $("[name='QUESTION']").val();
            //     var answer = $("[name='ANSWER']").val();
            //     if($.trim(question) == ''){
            //         var layer = layui.layer; // 弹层模块
            //         // 当需要弹出提示信息时
            //         layer.msg('请输入提问内容');
            //         return false;
            //     }
            //     if($.trim(answer)  == ''){
            //         var layer = layui.layer; // 弹层模块
            //         // 当需要弹出提示信息时
            //         layer.msg('请输入答案');
            //         return false;
            //     }
            // })
            
        });
    </script> 
  </div> 
 </body>
</html>