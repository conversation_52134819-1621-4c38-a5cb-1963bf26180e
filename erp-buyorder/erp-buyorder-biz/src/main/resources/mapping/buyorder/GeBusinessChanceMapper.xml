<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.buyorder.mapper.GeBusinessChanceMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.GeBusinessChance" >
    <!--          -->
    <id column="GE_BUSSINESS_CHANCE_ID" property="geBussinessChanceId" jdbcType="INTEGER" />
    <result column="GE_BUSSINESS_CHANCE_NO" property="geBussinessChanceNo" jdbcType="VARCHAR" />
    <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER" />
    <result column="QUOTEORDER_NO" property="quoteorderNo" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="BIT" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    <result column="TERMINAL_TRADER_ID" property="terminalTraderId" jdbcType="INTEGER" />
    <result column="TERMINAL_TRADER_NAME" property="terminalTraderName" jdbcType="VARCHAR" />
    <result column="SALES_AREA_ID" property="salesAreaId" jdbcType="INTEGER" />
    <result column="SALES_AREA" property="salesArea" jdbcType="VARCHAR" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
    <result column="HOSPITAL_TYPE" property="hospitalType" jdbcType="BIT" />
    <result column="GE_BUSINESS_CHANCE_SOURCE" property="geBusinessChanceSource" jdbcType="INTEGER" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="IS_DEFEND" property="isDefend" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="CREATOR_NAME" property="creatorName" jdbcType="VARCHAR" />
    <result column="UPDATER_NAME" property="updaterName" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    GE_BUSSINESS_CHANCE_ID, GE_BUSSINESS_CHANCE_NO, QUOTEORDER_ID, QUOTEORDER_NO, STATUS, 
    TRADER_ID, TRADER_NAME, TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, SALES_AREA_ID, 
    SALES_AREA, GOODS_ID, SKU, GOODS_NAME, HOSPITAL_TYPE, GE_BUSINESS_CHANCE_SOURCE,
    ADDRESS, IS_DEFEND, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_GE_BUSINESS_CHANCE
    where GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER}
  </select>
    <select id="queryBasicInfo" resultType="com.vedeng.erp.buyorder.dto.GeExamineBasicDto">
      select
      a.GE_BUSSINESS_CHANCE_ID,
      a.QUOTEORDER_NO,
      a.ADD_TIME,
      a.STATUS,
      c.BUSINESS_CHANCE_STATUS,
      a.TERMINAL_TRADER_NAME,
      a.HOSPITAL_TYPE,
      a.GOODS_NAME,
      a.SALES_AREA,
      a.ADDRESS,
      b.TITLE,
      a.TRADER_NAME
       from T_GE_BUSINESS_CHANCE a
       left join T_SYS_OPTION_DEFINITION b on a.GE_BUSINESS_CHANCE_SOURCE=b.SYS_OPTION_DEFINITION_ID
       left join T_GE_BUSINESS_CHANCE_DETAIL c on a.GE_BUSSINESS_CHANCE_ID =c.GE_BUSSINESS_CHANCE_ID
       and c.IS_DELETE=0
        where a.IS_DELETE=0
        and a.GE_BUSSINESS_CHANCE_ID=#{geBussinessChanceId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_GE_BUSINESS_CHANCE
    where GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChance" >
    <!--          -->
    insert into T_GE_BUSINESS_CHANCE (GE_BUSSINESS_CHANCE_ID, GE_BUSSINESS_CHANCE_NO, 
      QUOTEORDER_ID, QUOTEORDER_NO, STATUS, 
      TRADER_ID, TRADER_NAME, TERMINAL_TRADER_ID, 
      TERMINAL_TRADER_NAME, SALES_AREA_ID, SALES_AREA, 
      GOODS_ID, SKU, GOODS_NAME, 
      HOSPITAL_TYPE, GE_BUSINESS_CHANCE_SOURCE, ADDRESS,
      IS_DEFEND, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, IS_DELETE)
    values (#{geBussinessChanceId,jdbcType=INTEGER}, #{geBussinessChanceNo,jdbcType=VARCHAR}, 
      #{quoteorderId,jdbcType=INTEGER}, #{quoteorderNo,jdbcType=VARCHAR}, #{status,jdbcType=BIT}, 
      #{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{terminalTraderId,jdbcType=INTEGER}, 
      #{terminalTraderName,jdbcType=VARCHAR}, #{salesAreaId,jdbcType=INTEGER}, #{salesArea,jdbcType=VARCHAR}, 
      #{goodsId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{hospitalType,jdbcType=BIT}, #{geBusinessChanceSource,jdbcType=INTEGER}, #{address,jdbcType=VARCHAR},
      #{isDefend,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{isDelete,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChance" keyProperty="GE_BUSSINESS_CHANCE_ID" useGeneratedKeys="true">
    <!--          -->
    insert into T_GE_BUSINESS_CHANCE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="geBussinessChanceId != null" >
        GE_BUSSINESS_CHANCE_ID,
      </if>
      <if test="geBussinessChanceNo != null" >
        GE_BUSSINESS_CHANCE_NO,
      </if>
      <if test="quoteorderId != null" >
        QUOTEORDER_ID,
      </if>
      <if test="quoteorderNo != null" >
        QUOTEORDER_NO,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="traderName != null" >
        TRADER_NAME,
      </if>
      <if test="terminalTraderId != null" >
        TERMINAL_TRADER_ID,
      </if>
      <if test="terminalTraderName != null" >
        TERMINAL_TRADER_NAME,
      </if>
      <if test="salesAreaId != null" >
        SALES_AREA_ID,
      </if>
      <if test="salesArea != null" >
        SALES_AREA,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="sku != null" >
        SKU,
      </if>
      <if test="goodsName != null" >
        GOODS_NAME,
      </if>
      <if test="hospitalType != null" >
        HOSPITAL_TYPE,
      </if>
      <if test="geBusinessChanceSource != null" >
        GE_BUSINESS_CHANCE_SOURCE,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="isDefend != null" >
        IS_DEFEND,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME,
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="geBussinessChanceId != null" >
        #{geBussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="geBussinessChanceNo != null" >
        #{geBussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="quoteorderId != null" >
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNo != null" >
        #{quoteorderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=BIT},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null" >
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null" >
        #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null" >
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="salesAreaId != null" >
        #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null" >
        #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null" >
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalType != null" >
        #{hospitalType,jdbcType=BIT},
      </if>
      <if test="geBusinessChanceSource != null" >
        #{geBusinessChanceSource,jdbcType=INTEGER},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="isDefend != null" >
        #{isDefend,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null" >
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChance" >
    <!--          -->
    update T_GE_BUSINESS_CHANCE
    <set >
      <if test="geBussinessChanceNo != null" >
        GE_BUSSINESS_CHANCE_NO = #{geBussinessChanceNo,jdbcType=VARCHAR},
      </if>
      <if test="quoteorderId != null" >
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNo != null" >
        QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=BIT},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null" >
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null" >
        TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null" >
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="salesAreaId != null" >
        SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null" >
        SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null" >
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null" >
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalType != null" >
        HOSPITAL_TYPE = #{hospitalType,jdbcType=BIT},
      </if>
      <if test="geBusinessChanceSource != null" >
        GE_BUSINESS_CHANCE_SOURCE = #{geBusinessChanceSource,jdbcType=INTEGER},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="isDefend != null" >
        IS_DEFEND = #{isDefend,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null" >
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.GeBusinessChance" >
    <!--          -->
    update T_GE_BUSINESS_CHANCE
    set GE_BUSSINESS_CHANCE_NO = #{geBussinessChanceNo,jdbcType=VARCHAR},
      QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=BIT},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      HOSPITAL_TYPE = #{hospitalType,jdbcType=BIT},
      GE_BUSINESS_CHANCE_SOURCE = #{geBusinessChanceSource,jdbcType=INTEGER},
      ADDRESS = #{address,jdbcType=VARCHAR},
      IS_DEFEND = #{isDefend,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BIT}
    where GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER}
  </update>
    <update id="updateStatusByGeBussinessChanceId">
      update T_GE_BUSINESS_CHANCE
      set STATUS =#{status},
      ADD_TIME=#{addTime},
      MOD_TIME=#{modTime},
      CREATOR=#{creator},
      UPDATER=#{updater},
      CREATOR_NAME = #{creatorName},
      UPDATER_NAME = #{updaterName}
      where IS_DELETE=0
      and GE_BUSSINESS_CHANCE_ID=#{geBussinessChanceId}
    </update>

    <select id="queryForUniqueVerify"  resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_GE_BUSINESS_CHANCE
    WHERE QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR}
    AND TERMINAL_TRADER_NAME =#{terminalTraderName,jdbcType=VARCHAR}
    AND GOODS_NAME = #{goodsName,jdbcType=VARCHAR}
    AND (STATUS = 0 OR STATUS = 1)
  </select>

  <select id="queryByNo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_GE_BUSINESS_CHANCE
    WHERE GE_BUSSINESS_CHANCE_NO = #{geBussinessChanceNo,jdbcType=VARCHAR}
  </select>

<!--  更新商机是否维护字段-->
  <update id="updateIsDefend">
    UPDATE T_GE_BUSINESS_CHANCE
    SET IS_DEFEND = #{isDefend,jdbcType=BIT}
    WHERE GE_BUSSINESS_CHANCE_ID = #{geBussinessChanceId,jdbcType=INTEGER}
  </update>
</mapper>