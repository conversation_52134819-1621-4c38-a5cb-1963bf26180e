package com.vedeng.todolist.constant;

/**
 * 风控待办事项涉及的业务属性枚举类
 * @Author: daniel
 * @Date: 2020/12/17 16 04
 * @Description:
 */
public enum RiskCheckTodoListBuzPropertyEnum {

    /**
     * 风控涉及到销售订单
     */
    RISK_CHECK_SALEORDER("saleorder"),

    /**
     * 风控涉及到采购订单
     */
    RISK_CHECK_BUYORDER("buyorder"),
    /**
     * 风控涉及到报价单
     */
    RISK_CHECK_QUOTEORDER("quoteorder"),
    ;

    private String value;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    RiskCheckTodoListBuzPropertyEnum(String value) {
        this.value = value;
    }
}
