package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchTransactionVoucherDto;

public interface BatchTransactionVoucherMapper {
    int deleteByPrimaryKey(Long transactionVoucherId);

    int insert(BatchTransactionVoucherDto record);

    int insertSelective(BatchTransactionVoucherDto record);

    BatchTransactionVoucherDto selectByPrimaryKey(Long transactionVoucherId);

    int updateByPrimaryKeySelective(BatchTransactionVoucherDto record);

    int updateByPrimaryKey(BatchTransactionVoucherDto record);
}