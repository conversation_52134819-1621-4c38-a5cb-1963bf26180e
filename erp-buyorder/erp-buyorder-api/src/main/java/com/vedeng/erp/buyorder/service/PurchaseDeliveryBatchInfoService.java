package com.vedeng.erp.buyorder.service;

import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchInfoDto;

import java.util.List;

public interface PurchaseDeliveryBatchInfoService {

    /**
     * 根据采购单id查询同行单信息
     *
     * @param buyorderId
     * @return
     */
    List<PurchaseDeliveryBatchInfoDto> queryPurchaseDeliveryInfoByBuyorderId(Integer buyorderId);

    /**
     * @param buyorderGoodsVoIds
     * @return
     * <AUTHOR> @desc 验证采购单详情页面的
     */
    Integer verifyIsShowDeliveryDirectBtn(List<Integer> buyorderGoodsVoIds);

    /**
     * <AUTHOR>
     * @desc 根据主键id查询记录
     * @param purchaseDeliveryDirectBatchInfoId
     * @return
     */
    PurchaseDeliveryBatchInfoDto queryByPrimarykey(Integer purchaseDeliveryDirectBatchInfoId);
}
