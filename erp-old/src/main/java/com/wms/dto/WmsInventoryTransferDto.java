package com.wms.dto;

        import java.util.List;

/**
 * WMS库存转移单运输类
 *
 * <AUTHOR>
 * @date 2020/7/25 14:11:12
 */
public class WmsInventoryTransferDto extends AbstractCommonDto {

    /**
     * 库存转移单单号
     */
    private String TDOCNo;

    /**
     * 库存转移单类型
     */
    private String TDOCType;

    /**
     * 转移原因代码
     */
    private String ReasonCode;

    /**
     * 转移原因描述
     */
    private String Reason;

    /**
     * ERP库存转移单号
     */
    private String UserDefine1;

    /**
     * 预留字段
     */
    private String UserDefine2;

    /**
     * 预留字段
     */
    private String UserDefine3;

    /**
     * 预留字段
     */
    private String UserDefine4;

    /**
     * 预留字段
     */
    private String UserDefine5;

    /**
     * 备注
     */
    private String Notes;

    /**
     * WMS库存转移单详情
     */
    private List<WmsInventoryTransferDetailDto> details;

    public String getTDOCNo() {
        return TDOCNo;
    }

    public void setTDOCNo(String TDOCNo) {
        this.TDOCNo = TDOCNo;
    }

    public String getTDOCType() {
        return TDOCType;
    }

    public void setTDOCType(String TDOCType) {
        this.TDOCType = TDOCType;
    }

    public String getReasonCode() {
        return ReasonCode;
    }

    public void setReasonCode(String reasonCode) {
        ReasonCode = reasonCode;
    }

    public String getReason() {
        return Reason;
    }

    public void setReason(String reason) {
        Reason = reason;
    }

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return UserDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        UserDefine2 = userDefine2;
    }

    public String getUserDefine3() {
        return UserDefine3;
    }

    public void setUserDefine3(String userDefine3) {
        UserDefine3 = userDefine3;
    }

    public String getUserDefine4() {
        return UserDefine4;
    }

    public void setUserDefine4(String userDefine4) {
        UserDefine4 = userDefine4;
    }

    public String getUserDefine5() {
        return UserDefine5;
    }

    public void setUserDefine5(String userDefine5) {
        UserDefine5 = userDefine5;
    }

    public String getNotes() {
        return Notes;
    }

    public void setNotes(String notes) {
        Notes = notes;
    }

    public List<WmsInventoryTransferDetailDto> getDetails() {
        return details;
    }

    public void setDetails(List<WmsInventoryTransferDetailDto> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "WmsInventoryTransferDto{" +
                "TDOCNo='" + TDOCNo + '\'' +
                ", TDOCType='" + TDOCType + '\'' +
                ", ReasonCode='" + ReasonCode + '\'' +
                ", Reason='" + Reason + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine2='" + UserDefine2 + '\'' +
                ", UserDefine3='" + UserDefine3 + '\'' +
                ", UserDefine4='" + UserDefine4 + '\'' +
                ", UserDefine5='" + UserDefine5 + '\'' +
                ", Notes='" + Notes + '\'' +
                ", details=" + details +
                '}';
    }
}
