package com.vedeng.erp.saleorder.service;

import com.vedeng.erp.saleorder.dto.SaleOrderGoodsImageDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsMaoLiBuyPriceDto;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */
public interface SaleOrderDataApiService {


    public void initSaleOrderGoodsMaoli(List<SaleOrderGoodsMaoLiBuyPriceDto> initList);


    public void updateSaleOrderGoodsMaoliForJob();

    /**
     * 查询onedata已返回成功的采购均价信息，
     * @param saleOrderId
     * @return
     */
    public List<SaleOrderGoodsMaoLiBuyPriceDto> queryListForSaleOrderGoodsMaoli(Integer saleOrderId);


    public List<SaleOrderGoodsImageDto> queryListForSaleOrderGoodsImage(List<String> skuNoList);

}
