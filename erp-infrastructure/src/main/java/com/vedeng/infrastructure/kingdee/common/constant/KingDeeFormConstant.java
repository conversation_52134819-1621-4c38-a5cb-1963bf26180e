package com.vedeng.infrastructure.kingdee.common.constant;

/**
 * 金蝶表单常量类
 *
 * <AUTHOR>
 */
public class KingDeeFormConstant {

    /**
     * 供应商
     */
    public final static String BD_SUPPLIER = "BD_Supplier";

    /**
     * 付款单
     */
    public final static String AP_PAYBILL = "AP_PAYBILL";

    /**
     * 银行
     */
    public final static String BD_BANK = "BD_BANK";

    /**
     * 客户
     */
    public final static String BD_CUSTOMER = "BD_Customer";

    /**
     * 付款单回单流水查询接口
     */
    public final static String RECEIPT_BILL = "QZOK_ReceiptBillFile";

    /**
     * 付款单回单流水查询接口
     */
    public final static String BANK_BILL = "QZOK_BankBill";


    /**
     * 物料
     */
    public final static String BD_MATERIAL = "BD_MATERIAL";

    /**
     * 税码
     */
    public final static String IV_GTTAXCODE = "IV_GTTAXCODE";

    /**
     * 应付余额调整单
     */
    public final static String BD_NEEDPAY = "QZOK_BOS_YFTZD";

    /**
     * 应收余额调整单
     */
    public final static String BD_NEEDREVEIVE = "QZOK_BOS_YSTZD";


    /**
     * 销项费用普票
     */
    public final static String OUTPUT_FEE_PLAIN_INVOICE = "IV_SALEEXINV";

    /**
     * 销项费用专票
     */
    public final static String OUTPUT_FEE_SPECIAL_INVOICE = "IV_SALEEXVATIN";


    /**
     * 进项费用普票
     */
    public final static String INPUT_FEE_PLAIN_INVOICE = "IV_PUREXPINV";

    /**
     * 进项费用专票
     */
    public final static String INPUT_FEE_SPECIAL_INVOICE = "IV_PUREXVATIN";

    /**
     * 采购费用普票
     */
    public final static String PURCHASE_VAT_PLAIN_INVOICE = "IV_PURCHASEOC";

    /**
     * 采购费用专票
     */
    public final static String PURCHASE_VAT_SPECIAL_INVOICE = "IV_PURCHASEIC";

    /**
     * 销售普票
     */
    public final static String SALE_VAT_PLAIN_INVOICE = "IV_SALESOC";

    /**
     * 销售专票
     */
    public final static String SALE_VAT_SPECIAL_INVOICE = "IV_SALESIC";

    /**
     * 应付单
     */
    public final static String PAY_EXPENSES = "AP_Payable";

    /**
     * 应收单
     */
    public static final String RECEIVE_EXPENSES = "AR_receivable";

    /**
     * 采购退料单
     */
    public static final String PURCHASE_BACK = "PUR_MRB";

    /**
     * 其他入库
     */
    public static final String STK_MISCELLANEOUS = "STK_MISCELLANEOUS";

    /**
     * 采购入库单
     */
    public static final String STK_INSTOCK = "STK_InStock";

    /**
     * 期初销售出库单
     */
    public static final String SAL_INIT_OUT_STOCK = "SAL_INITOUTSTOCK";

    /**
     * 期初采购入库单
     */
    public static final String STK_INIT_IN_STOCK = "STK_InitInStock";

    /**
     * 其他出库
     */
    public static final String STK_MIS_DELIVERY = "STK_MisDelivery";

    /**
     * 调拨单
     */
    public static final String STK_TRANSFER_DIRECT = "STK_TransferDirect";

    /**
     * 盘盈单
     */
    public static final String STK_STOCK_COUNT_GAIN = "STK_StockCountGain";

    /**
     * 盘亏单
     */
    public static final String STK_STOCK_COUNT_LOSS = "STK_StockCountLoss";

    /**
     * 销售退货单
     */
    public static final String SAL_RETURN_STOCK = "SAL_RETURNSTOCK";

    /**
     * 销售出库单
     */
    public static final String SAL_OUT_STOCK = "SAL_OUTSTOCK";

    /**
     * 安调单
     */
    public static final String QZOK_ATHD = "QZOK_ATHD";
    /**
     * 收款单
     */
    public static final String RECEIVE_BILL = "AR_RECEIVEBILL";

    /**
     * 销售结算调整单
     */
    public static final String QZOK_XSJSTZD = "QZOK_XSJSTZD";

    /**
     * 快递成本
     */
    public final static String QZOK_KDCB = "QZOK_KDCB";

    /**
     * 快递签收
     */
    public static final String QZOK_KDQSD = "QZOK_KDQSD";

    /**
     * 银行流水
     */
    public static final String WB_RecBankTradeDetail = "WB_RecBankTradeDetail";

    /**
     * 冲正场景
     */
    public static final String CORRECT_THE_SERIAL_NUMBER = "00000000000000000000000000000000";

    /**
     * 其他业务付款单
     */
    public static final String FIN_OTHERS = "FIN_OTHERS";

    /**
     * 收款退款单
     */
    public static final String RECEIVE_REFUND = "AR_REFUNDBILL";

    /**
     * 付款退款单
     */
    public static final String PAY_REFUND = "AP_REFUNDBILL";

    /**
     * 销售合同
     */
    public static final String SALEORDER_CONTRACT = "QZOK_BDXSDD";
    /**
     * 采购合同
     */
    public static final String BUYORDER_CONTRACT = "QZOK_BDCGHT";

    /**
     * 内部采销
     */
    public static final String INTERNAL_PROCUREMENT = "QZOK_NBCX";


    /**
     * 流转单销售订单
     */
    public static final String SAL_SaleOrder = "SAL_SaleOrder";

    /**
     * 流转单采购订单
     */
    public static final String PUR_PurchaseOrder = "PUR_PurchaseOrder";
}
