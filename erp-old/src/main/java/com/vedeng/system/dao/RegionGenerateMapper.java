package com.vedeng.system.dao;

import com.vedeng.system.model.RegionGenerate;
import com.vedeng.system.model.RegionGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RegionGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    long countByExample(RegionGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int deleteByExample(RegionGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int deleteByPrimaryKey(Integer regionId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int insert(RegionGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int insertSelective(RegionGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    List<RegionGenerate> selectByExample(RegionGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    RegionGenerate selectByPrimaryKey(Integer regionId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int updateByExampleSelective(@Param("record") RegionGenerate record, @Param("example") RegionGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int updateByExample(@Param("record") RegionGenerate record, @Param("example") RegionGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int updateByPrimaryKeySelective(RegionGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGION
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int updateByPrimaryKey(RegionGenerate record);
}