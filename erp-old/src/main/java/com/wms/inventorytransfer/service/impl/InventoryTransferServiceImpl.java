package com.wms.inventorytransfer.service.impl;

import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.goods.dao.GoodsMapper;
import com.wms.constant.*;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.PutSaleOrderDto;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.inventorytransfer.dao.InventoryTransferMapper;
import com.wms.inventorytransfer.model.dto.InventoryTransferDto;
import com.wms.inventorytransfer.model.dto.InventoryTransferRequest;
import com.wms.inventorytransfer.model.dto.InventoryTransferSearchDto;
import com.wms.inventorytransfer.model.po.InventoryTransferDetailPO;
import com.wms.inventorytransfer.model.po.InventoryTransferPO;
import com.wms.inventorytransfer.model.vo.InventoryTransferDetailVO;
import com.wms.inventorytransfer.model.vo.InventoryTransferVO;
import com.wms.inventorytransfer.service.InventoryTransferService;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 库存转移单
 *
 * <AUTHOR>
 * @date 2020/7/10 14:22:10
 */
@Service("inventoryTransferService")
public class InventoryTransferServiceImpl extends BaseServiceimpl implements InventoryTransferService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private InventoryTransferMapper inventoryTransferMapper;

    @Autowired
    private GoodsMapper goodsMapper;

    @Autowired
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Value("${stock_url}")
    protected String stockUrl;

    @Resource
    private OrderNoDict orderNoDict;

    /**
     * @return
     * @describe 获取库存转移单信息
     * <AUTHOR>
     * @date 2020/7/10 14:10:15
     */
    @Override
    public Map<String, Object> getInventoryTransferVos(InventoryTransferSearchDto inventoryTransferDto, Page page) {
        //搜索条件处理
        HashMap<String, Object> queryParams = new HashMap<>(4);
        if (inventoryTransferDto != null && EmptyUtils.isNotBlank(inventoryTransferDto.getSearchBeginTime())) {
            inventoryTransferDto.setStartTime(DateUtil.convertLong(inventoryTransferDto.getSearchBeginTime() + " 00:00:00", DateUtil.TIME_FORMAT));
        }
        if (inventoryTransferDto != null && EmptyUtils.isNotBlank(inventoryTransferDto.getSearchEndTime())) {
            inventoryTransferDto.setEndTime(DateUtil.convertLong(inventoryTransferDto.getSearchEndTime() + " 23:59:59", DateUtil.TIME_FORMAT));
        }
        queryParams.put("inventoryTransferDto", inventoryTransferDto);
        queryParams.put("page", page);

        //展示结果处理
        HashMap<String, Object> resultMap = new HashMap<>(4);
        List<InventoryTransferVO> inventoryTransferVos = inventoryTransferMapper.getInventoryTransferVosListPage(queryParams);
        if (CollectionUtils.isNotEmpty(inventoryTransferVos)) {
            inventoryTransferVos.stream().forEach(inventoryTransferVO -> {
                inventoryTransferVO.setToWarehouseStr(LogicalEnum.getLogicalWarehouseName(inventoryTransferVO.getToWarehouseId()));
                inventoryTransferVO.setStatusStr(InventoryTransferStatusEnum.getStatusStr(inventoryTransferVO.getStatus()));
                inventoryTransferVO.setTypeStr(InventoryTransferTypeEnum.getTypeStr(inventoryTransferVO.getType()));
            });
            resultMap.put("inventoryTransferVos", inventoryTransferVos);
        }
        resultMap.put("page", page);
        return resultMap;
    }

    /**
     * @return
     * @describe 获取库存转移单详情信息
     * <AUTHOR>
     * @date 2020/7/10 15:27:15
     */
    @Override
    public List<InventoryTransferDetailVO> getInventoryTransferDetailById(Integer inventoryTransferId) {
        List<InventoryTransferDetailVO> inventoryTransferDetailVos = inventoryTransferMapper.getInventoryTransferDetailById(inventoryTransferId);
        if (CollectionUtils.isNotEmpty(inventoryTransferDetailVos)) {
            inventoryTransferDetailVos.stream().forEach(inventoryTransferDetailVO -> {
                inventoryTransferDetailVO.setFromWarehoseStr(LogicalEnum.getLogicalWarehouseName(inventoryTransferDetailVO.getFromWarehouseId()));
                inventoryTransferDetailVO.setToWarehouseStr(LogicalEnum.getLogicalWarehouseName(inventoryTransferDetailVO.getToWarehouseId()));
            });
        }
        return inventoryTransferDetailVos;
    }

    /**
     * @param inventoryTransferId
     * @return
     * @describe 通过库存转移单ID查询信息
     * <AUTHOR>
     * @date 2020/7/13 9:45:22
     */
    @Override
    public InventoryTransferVO getInventoryTransferById(Integer inventoryTransferId) {
        InventoryTransferVO inventoryTransferVO = inventoryTransferMapper.getInventoryTransferById(inventoryTransferId);
        if (inventoryTransferVO != null && inventoryTransferVO.getType() != null) {
            inventoryTransferVO.setTypeStr(InventoryTransferTypeEnum.getTypeStr(inventoryTransferVO.getType()));
        }
        if (inventoryTransferVO != null && inventoryTransferVO.getStatus() != null) {
            inventoryTransferVO.setStatusStr(InventoryTransferStatusEnum.getStatusStr(inventoryTransferVO.getStatus()));
        }
        return inventoryTransferVO;
    }

    /**
     * @param actionId 活动ID
     * @return
     * @describe 根据活动ID获取库存转移单信息
     * <AUTHOR>
     * @date 2020/7/20 9:12:24
     */
    @Override
    public List<InventoryTransferDto> getInventoryTransferInfoByActionId(Integer actionId) {
        return inventoryTransferMapper.getInventoryTransferInfoByActionId(actionId);
    }

    /**
     * 保存库存转移单相关信息
     *
     * @param inventoryTransferRequest
     * @return
     * <AUTHOR>
     * @date 2020/7/24 16:45:23
     */
    @Override
    public void saveInventoryTransferRequest(InventoryTransferRequest inventoryTransferRequest) {
        if (inventoryTransferRequest != null && inventoryTransferRequest.getActionId() != null &&
                CollectionUtils.isNotEmpty(inventoryTransferRequest.getInventoryTransferDetailDtos())) {
            //1.保存库存转移单；
            InventoryTransferPO inventoryTransferPO = saveInventoryTransfer(inventoryTransferRequest);

            //2.更新库存转移单单号
            InventoryTransferPO inventoryTransfer = new InventoryTransferPO();
            inventoryTransfer.setInventoryTransferId(inventoryTransferPO.getInventoryTransferId());
            inventoryTransfer.setInventoryTransferNo(orderNoDict.getOrderNum(inventoryTransferPO.getInventoryTransferId(), 19));
            inventoryTransferMapper.updateInventoryTransfer(inventoryTransfer);

            //3.保存库存转移单详情,添加转移占用信息
            saveInventoryDetail(inventoryTransferRequest, inventoryTransferPO);

            //4.封装转移单下传信息
            List<PutSaleOrderGoodsDto> putSaleOrderGoodsDtos = setWmsInventoryTransferDetails(inventoryTransferPO);

            //5.下传WMS发起库存转移单
            putInventoryTransfer(inventoryTransferPO, inventoryTransfer, putSaleOrderGoodsDtos);

        } else {
            logger.error("库存转移单信息不全" + inventoryTransferRequest.toString());
        }
    }

    /**
     * 封装下传WMS的库存转移详情信息
     *
     * @param inventoryTransferPO
     * @return
     */
    private List<PutSaleOrderGoodsDto> setWmsInventoryTransferDetails(InventoryTransferPO inventoryTransferPO) {
        ArrayList<PutSaleOrderGoodsDto> putSaleOrderGoodsDtos = new ArrayList<>();
        List<InventoryTransferDetailVO> inventoryTransferDetails = inventoryTransferMapper.getInventoryTransferDetailById(inventoryTransferPO.getInventoryTransferId());
        if (CollectionUtils.isEmpty(inventoryTransferDetails)) {
            logger.error("库存转移单中详情信息异常 inventoryTransferDetails" + inventoryTransferDetails.toString());
            return putSaleOrderGoodsDtos;
        }
        inventoryTransferDetails.stream().forEach(inventoryTransferDetail -> {
            PutSaleOrderGoodsDto putSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
            putSaleOrderGoodsDto.setSku(inventoryTransferDetail.getSkuNo());
            putSaleOrderGoodsDto.setQtyOrdered(inventoryTransferDetail.getNum());
            putSaleOrderGoodsDto.setDedi04("N");
            putSaleOrderGoodsDto.setDedi05(LogicalEnum.getLogicalWarehouseCode(inventoryTransferDetail.getFromWarehouseId()));
            putSaleOrderGoodsDto.setDedi06(LogicalEnum.getLogicalWarehouseCode(inventoryTransferDetail.getToWarehouseId()));
            putSaleOrderGoodsDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(inventoryTransferDetail.getFromWarehouseId()));
            putSaleOrderGoodsDto.setDedi07(inventoryTransferDetail.getInventoryTransferDetailId().toString());
            putSaleOrderGoodsDtos.add(putSaleOrderGoodsDto);
        });
        return putSaleOrderGoodsDtos;
    }

    /**
     * 向WMS下传库存转移信息
     *
     * @param inventoryTransferPO
     * @param inventoryTransfer
     * @param putSaleOrderGoodsDtos
     */
    private void putInventoryTransfer(InventoryTransferPO inventoryTransferPO, InventoryTransferPO inventoryTransfer, List<PutSaleOrderGoodsDto> putSaleOrderGoodsDtos) {
        PutSaleOrderDto putSaleOrderDto = new PutSaleOrderDto();
        putSaleOrderDto.setDocNo(inventoryTransfer.getInventoryTransferNo() + "_" +
                DateUtil.convertString(System.currentTimeMillis(), DateUtil.TIME_FORMAT_LONG));
        putSaleOrderDto.setOrderType(WmsInterfaceOrderType.OUT_TRANSFER);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        putSaleOrderDto.setOrderTime(simpleDateFormat.format(new Date()));
        putSaleOrderDto.setExpectedShipmentTime1(simpleDateFormat.format(new Date()));
        //部门名称
        putSaleOrderDto.setSoReferenceA("System");
        //制单员
        putSaleOrderDto.setSoReferenceB("System");
        //客户ID
        putSaleOrderDto.setConsigneeId("VEDENG");
        putSaleOrderDto.setSoReferenceB("njadmin");
        putSaleOrderDto.setHedi03("N");
        putSaleOrderDto.setHedi04("N");
        putSaleOrderDto.setHedi05("N");
        //目标逻辑仓
        putSaleOrderDto.setHedi02(LogicalEnum.getLogicalWarehouseCode(inventoryTransferPO.getToWarehouseId()));
        putSaleOrderDto.setDetails(putSaleOrderGoodsDtos);

        try {
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);
            wmsInterface.request(putSaleOrderDto);
        } catch (Exception e) {
            logger.error("下传库存转移单error putSaleOrderDto:{}" + putSaleOrderDto,e);
        }
    }

    /**
     * 活动保存库存转移单详情以及ERP逻辑库存占用信息
     *
     * @param inventoryTransferRequest
     * @param inventoryTransferPO
     */
    private void saveInventoryDetail(InventoryTransferRequest inventoryTransferRequest, InventoryTransferPO inventoryTransferPO) {
        inventoryTransferRequest.getInventoryTransferDetailDtos().stream().forEach(inventoryTransferDetailDto -> {
            //1.维护库存详情信息
            InventoryTransferDetailPO inventoryTransferDetailPO = new InventoryTransferDetailPO();
            inventoryTransferDetailPO.setInventoryTransferId(inventoryTransferPO.getInventoryTransferId());
            inventoryTransferDetailPO.setSkuNo(inventoryTransferDetailDto.getSkuNo());
            inventoryTransferDetailPO.setGoodsName(goodsMapper.getGoodNameBySkuNo(inventoryTransferDetailDto.getSkuNo()));
            inventoryTransferDetailPO.setNum(inventoryTransferDetailDto.getNum());
            inventoryTransferDetailPO.setFromWarehouseId(inventoryTransferDetailDto.getFromWarehouseId());
            inventoryTransferDetailPO.setToWarehouseId(inventoryTransferDetailDto.getToWarehouseId());
            inventoryTransferDetailPO.setAddTime(System.currentTimeMillis());
            inventoryTransferMapper.insertInventoryTransferDetail(inventoryTransferDetailPO);

            //2.添加ERP库存占用信息
            WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
            wmsLogicalOrdergoods.setRelatedId(inventoryTransferDetailPO.getInventoryTransferDetailId());
            wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.INVENTORY_TRANSFER_TYPE.getOperateTypeCode());
            wmsLogicalOrdergoods.setSku(inventoryTransferDetailDto.getSkuNo());
            wmsLogicalOrdergoods.setGoodsId(goodsMapper.getGoodsIdBySku(inventoryTransferDetailDto.getSkuNo()));
            wmsLogicalOrdergoods.setNum(inventoryTransferDetailDto.getNum());
            wmsLogicalOrdergoods.setLogicalWarehouseId(inventoryTransferDetailDto.getFromWarehouseId());
            wmsLogicalOrdergoods.setOccupyNum(inventoryTransferDetailDto.getNum());
            wmsLogicalOrdergoods.setIsDelete(0);
            wmsLogicalOrdergoods.setArrivalNum(0);
            wmsLogicalOrdergoods.setDeliveryNum(0);
            wmsLogicalOrdergoods.setAddTime(new Date());
            wmsLogicalOrdergoods.setModeTime(new Date());
            wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);
        });
    }

    /**
     * 活动保存库存转移单信息
     *
     * @param inventoryTransferRequest
     * @return
     */
    private InventoryTransferPO saveInventoryTransfer(InventoryTransferRequest inventoryTransferRequest) {
        InventoryTransferPO inventoryTransferPO = new InventoryTransferPO();
        inventoryTransferPO.setActionId(inventoryTransferRequest.getActionId());
        inventoryTransferPO.setType(InventoryTransferTypeEnum.SECKILL_INVENTORY_TRANSFER.getType());
        inventoryTransferPO.setStatus(InventoryTransferStatusEnum.ON_GOING.getStatus());
        inventoryTransferPO.setToWarehouseId(inventoryTransferRequest.getInventoryTransferDetailDtos().get(0).getToWarehouseId());
        inventoryTransferPO.setCreator(1);
        inventoryTransferPO.setAddTime(System.currentTimeMillis());
        inventoryTransferMapper.insertInventoryTransfer(inventoryTransferPO);
        return inventoryTransferPO;
    }

}
