package com.wms.service.listenner;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.chain.Build.StepBuildFactory;
import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.HandlerStepContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ERP采购换货单审核通过
 */
@Service
public class PurchaseExgAuditFinishListener extends AbstractErpListenner{

    private static final Logger logger = LoggerFactory.getLogger(PurchaseExgAuditFinishListener.class);


    @Autowired
    private StepBuildFactory stepBuildFactory;



    @Override
    @Transactional
    protected void doOnActionHappen(Object[] params) throws Exception {

        AfterSalesVo afterSalesInfo = (AfterSalesVo)params[0];

        boolean auditPass = (boolean)params[1];

        User user = (User)params[2];

        //换货单审核不通过直接返回
        if(!auditPass){
            logger.info("采购换货单:"+afterSalesInfo.getAfterSalesNo()+"审核不通过，处理结束=================");
            return;
        }

        HandlerStepContext handlerStepContext = new HandlerStepContext();
        handlerStepContext.put("afterSalesInfo",afterSalesInfo);
        handlerStepContext.put("user",user);

        //构建处理步骤
        HandlerStep handlerStep = stepBuildFactory.buildPurchaseExgStep();

        //处理链条
        handlerStep.dealWith(handlerStepContext);

    }
}
