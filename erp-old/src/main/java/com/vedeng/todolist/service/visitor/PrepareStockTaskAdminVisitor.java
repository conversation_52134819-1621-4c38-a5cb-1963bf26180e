package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.todolist.dto.PrepareStockTaskToDoDto;
import com.vedeng.todolist.dto.SupplyChainAdminTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class PrepareStockTaskAdminVisitor implements TodoVisitor<SupplyChainAdminTodoDtoBuild> {

    @Autowired
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Resource
    private UserMapper userMapper;

    private Map<Integer,List<Integer>> userIdByOrgaIdMap = new HashedMap();

    @Override
    public void visitor(SupplyChainAdminTodoDtoBuild todoDtoBuild) {

        List<Organization> orgList =  ThreadLocalContext.get("orgaList");

        Set<Integer> userIdSet = new HashSet<>();

        orgList.stream().forEach(organization -> {
            List<Integer> userIdList = userMapper.getUserIdListByOrgId(organization.getOrgId());
            userIdSet.addAll(userIdList);
            userIdByOrgaIdMap.put(organization.getOrgId(),userIdList);
        });

        List<Integer> userIdAllList = new ArrayList<>(userIdSet);

        PrepareStockTaskToDoDto prepareStockTaskToDoDto = earlyWarningTaskMapper.getPrepareStockTaskByUserId(userIdAllList);

        todoDtoBuild.get().setPrepareStockTaskToDoDto(prepareStockTaskToDoDto);
    }
}
