package com.newtask;

import com.vedeng.common.constant.Constants;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.OutboundBatchesRecodeMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.OutboundBatchesRecode;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.SaleorderGoods;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
     * @Description 确认单历史数据修复
 * <AUTHOR>
 */
@Component
@JobHandler(value = "ConfirmOrderHistoryDataProcessTask")
@Slf4j
public class ConfirmOrderHistoryDataProcessTask extends AbstractJobHandler {

    @Autowired
    private SaleorderMapper saleorderMapper;
    @Autowired
    private ExpressMapper expressMapper;
    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;
    @Autowired
    private OutboundBatchesRecodeMapper outboundBatchesRecodeMapper;



    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("===================确认单历史数据修复开始===================");
        List<Integer> resultList = new ArrayList<>();
        List<Integer> reviewResultList = new ArrayList<>();
        List<Integer> saleOrderIds = saleorderMapper.selectConfirmOrderHistoryData();
        if (CollectionUtils.isNotEmpty(saleOrderIds)) {
            // 筛选出需要修复的确认单历史数据
            extracted(saleOrderIds, resultList);
        }
        List<Integer> reviewSaleOrderIds = saleorderMapper.selectReviewConfirmOrderHistoryData();
        if (CollectionUtils.isNotEmpty(reviewSaleOrderIds)) {
            // 筛选出需要修复的复核确认单历史数据
            extracted(reviewSaleOrderIds, reviewResultList);
        }

        log.info("需要修复的确认单历史数据有:{}条,具体订单主键ID集合为:{}", resultList.size(), resultList);
        XxlJobLogger.log("需要修复的确认单历史数据有:{}条,具体订单主键ID集合为:{}", resultList.size(), resultList);

        log.info("订单下批次都审核通过，但订单在确认单审核中数据有:{}条,具体订单主键ID集合为:{}", reviewResultList.size(), reviewResultList);
        XxlJobLogger.log("订单下批次都审核通过，但订单在确认单审核中数据有:{}条,具体订单主键ID集合为:{}", reviewResultList.size(), reviewResultList);

        XxlJobLogger.log("===================确认单历史数据修复结束===================");
        return SUCCESS;
    }

    private void extracted(List<Integer> saleOrderIds, List<Integer> resultList) {
        for (Integer saleOrderId : saleOrderIds) {
            Express selectExpress = new Express();
            selectExpress.setSaleorderId(saleOrderId);
            List<Express> expressInfoConfirmation = expressMapper.getExpressInfoConfirmation(selectExpress);
            List<SaleorderGoods> saleOrderGoodsList = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleOrderId);
            if (CollectionUtils.isNotEmpty(saleOrderGoodsList)) {
                List<Integer> saleOrderGoodsIds = saleOrderGoodsList.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
                selectExpress.setRelatedIds(saleOrderGoodsIds);
                expressInfoConfirmation.addAll(expressMapper.getBuyExpressList(selectExpress));
            }
            List<String> batchNoList = expressInfoConfirmation.stream().map(Express::getBatchNo).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(batchNoList)) {
                List<OutboundBatchesRecode> allBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNoList);
                if (CollectionUtils.isNotEmpty(allBatchNoList) && allBatchNoList.stream().allMatch(batchNo -> Constants.ONE.equals(batchNo.getAuditStatus()))) {
                    resultList.add(saleOrderId);
                }
            }
        }
    }

}
