package com.vedeng.business.controller;

import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.common.validator.FormToken;
import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailDTO;
import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailQueryDTO;
import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailResponse;
import com.vedeng.erp.business.domain.entity.BusinessClues;
import com.vedeng.erp.business.domain.vo.BusinessCluesVo;
import com.vedeng.erp.trader.api.CommunicateVoiceTaskApi;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.domain.vo.TraderCustomerTuokeLabelVo;
import com.vedeng.erp.business.feign.RemoteCrmAdminApiService;
import com.vedeng.erp.business.service.BusinessCluesService;
import com.vedeng.erp.trader.dto.CommunicateVoiceTaskDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;
import com.vedeng.erp.trader.service.TraderCustomerLabelService;
import com.vedeng.system.model.Tag;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.TagService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.dto.TraderBaseInfoDto;
import com.vedeng.trader.model.vo.TraderContactVo;
import com.vedeng.trader.service.TraderCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 线索控制层类
 *
 * <AUTHOR>
 * @date 2022/3/10 17:08
 **/
@Slf4j
@Controller
@RequestMapping("/business/clues")
public class BusinessCluesController extends BaseController {

    @Autowired
    private BusinessCluesService businessCluesService;

    @Autowired
    private UserService userService;

    @Autowired
    private TraderCustomerLabelService traderCustomerLabelService;

    @Autowired
    private RegionService regionService;

    @Resource
    private RemoteCrmAdminApiService remoteCrmAdminApiService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private TagService tagService;

    /**
     * 线索列表
     *
     * @param request
     * @param businessCluesVo
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/index")
    public ModelAndView index(HttpServletRequest request, BusinessCluesVo businessCluesVo,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        ModelAndView mv = new ModelAndView("businessclues/index");
        Page page = getPageTag(request, pageNo, pageSize);
        User user = getSessionUser(request);
        //判断是否为销售
        User sessionUser = getSessionUser(request);
        Boolean saleAndB2BFlagByUserId = userService.getSaleAndB2BFlagByUserId(sessionUser);
        int isSaleFlag = 0;
        if(saleAndB2BFlagByUserId){
            isSaleFlag = 1;
        }
        mv.addObject("isSaleFlag", isSaleFlag);
        // 归属销售下拉框
        List<Integer> positionType = new ArrayList<>();
        positionType.add(SysOptionConstant.ID_310);
        List<User> userList = userService.getMyUserList(user, positionType, true);
        mv.addObject("userList", userList);

        // 处理省市区搜索项
        Integer areaId = regionSelectOptionHandle(mv, businessCluesVo.getProvince(), businessCluesVo.getCity(), businessCluesVo.getZone());
        businessCluesVo.setAreaId(areaId);

        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("page", page);
        // 处理查询条件的时间字符串
        businessCluesVo.setStartTime(DateUtil.convertLong(businessCluesVo.getStartTimeStr() + " 00:00:00", DateUtil.TIME_FORMAT));
        businessCluesVo.setEndTime(DateUtil.convertLong(businessCluesVo.getEndTimeStr() + " 23:59:59", DateUtil.TIME_FORMAT));
        // 数据权限 查看自己和下属和下下属的数据
        businessCluesVo.setUserIds(userList.stream().map(User::getUserId).collect(Collectors.toList()));
        queryParam.put("businessCluesVo", businessCluesVo);
        Map<String, Object> businessCluesListPage = businessCluesService.getBusinessCluesListPage(queryParam);

        mv.addObject("businessCluesVoList", businessCluesListPage.get("businessCluesVoList"));
        mv.addObject("page", businessCluesListPage.get("page"));
        mv.addObject("businessCluesVo", businessCluesVo);
        return mv;
    }

    // 客户地区 搜索项处理
    private Integer regionSelectOptionHandle(ModelAndView mv, Integer province, Integer city, Integer zone) {
        Integer areaId = null;

        // 省
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mv.addObject("provinceList", provinceList);

        // 市区
        if (ObjectUtils.notEmpty(zone)) {
            areaId = zone;
            List<Region> list = regionService.getRegionByParentId(city);
            mv.addObject("zoneList", list);
            List<Region> cys = regionService.getRegionByParentId(province);
            mv.addObject("cityList", cys);
        } else if (ObjectUtils.notEmpty(province) && ObjectUtils.notEmpty(city) && ObjectUtils.isEmpty(zone)) {
            areaId = city;
            List<Region> list = regionService.getRegionByParentId(city);
            mv.addObject("zoneList", list);
            List<Region> cys = regionService.getRegionByParentId(province);
            mv.addObject("cityList", cys);
        } else if (ObjectUtils.notEmpty(province) && ObjectUtils.isEmpty(city) && ObjectUtils.isEmpty(zone)) {
            areaId = province;
            List<Region> cys = regionService.getRegionByParentId(province);
            mv.addObject("cityList", cys);
        }
        return areaId;
    }

    /**
     * 线索详情页面
     *
     * @param businessCluesDetailQueryDTO
     * @return
     */
    @RequestMapping(value = "/viewBusinessClues")
    public ModelAndView viewBusinessClues(HttpServletRequest request, BusinessCluesDetailQueryDTO businessCluesDetailQueryDTO,
                                          Integer businessCluesId,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false) Integer pageSize) {
        ModelAndView mv = new ModelAndView("businessclues/view_business_clues");

        // 从客户详情-通话记录页面点进来时，直传了一个 businessCluesId，因此需要先去库里查询
        if (businessCluesDetailQueryDTO.getGroupId() == null || businessCluesDetailQueryDTO.getTraderId() == null) {
            BusinessClues cluesInfo = businessCluesService.getCluesInfo(businessCluesId);
            businessCluesDetailQueryDTO.setGroupId(cluesInfo.getGroupId());
            businessCluesDetailQueryDTO.setTraderId(cluesInfo.getTraderId());
        }

        mv.addObject("groupId", businessCluesDetailQueryDTO.getGroupId());

        // 客户分群标签
        TraderCustomerTuokeLabelVo tuokeLabelInfo = traderCustomerLabelService.getTuokeLabelInfo(businessCluesDetailQueryDTO.getTraderId(), businessCluesId);
        mv.addObject("tuokeLabelInfo", tuokeLabelInfo);

        // 处理省市区搜索项
        regionSelectOptionHandle(mv, businessCluesDetailQueryDTO.getZhaobiaoProvince(), businessCluesDetailQueryDTO.getZhaobiaoCity(), businessCluesDetailQueryDTO.getZhaobiaoCountry());

        // 中标信息分页查询
        mv.addObject("businessCluesDetailQueryDTO", businessCluesDetailQueryDTO);

        // 沟通记录
        CommunicateRecord cr = new CommunicateRecord();
        cr.setBusinessCluesId(businessCluesId);
        Page pageParam = Page.newBuilder(null, null, null);
        List<CommunicateRecord> communicateList = traderCustomerService.getCommunicateRecordListPage(cr, pageParam);
        if (!communicateList.isEmpty()) {
            mv.addObject("communicateList", communicateList);
        }

        try {
            businessCluesDetailQueryDTO.setPageNum(pageNo);
            businessCluesDetailQueryDTO.setPageSize(pageSize);
            RestfulResult<BusinessCluesDetailResponse> businessCluesDetails = remoteCrmAdminApiService.getBusinessCluesDetails(businessCluesDetailQueryDTO);
            List<BusinessCluesDetailDTO> cluesDetailDTOS = businessCluesDetails.getData().getBusinessCluesDetailDTOList();
            mv.addObject("businessCluesDetailList", cluesDetailDTOS);
            Page page = getPageTag(request, businessCluesDetails.getData().getPageNum(), businessCluesDetails.getData().getPageSize(), businessCluesDetails.getData().getSize());
            mv.addObject("page", page);
            List<TraderContactVo> traderContactList = businessCluesService.getTraderContactList(businessCluesDetailQueryDTO.getTraderId(), cluesDetailDTOS);
            mv.addObject("traderContactList", traderContactList);
        } catch (Exception e) {
            logger.error("线索详情页调用CRM接口异常", e);
        }

        return mv;
    }

    /**
     * 修改线索的置顶状态
     *
     * @param businessCluesVo
     * @return
     */
    @RequestMapping(value = "/changeTopStatus")
    @ResponseBody
    public ResultInfo<?> changeTopStatus(BusinessCluesVo businessCluesVo) {
        return ResultInfo.success(businessCluesService.changeTopStatus(businessCluesVo));
    }

    /**
     * 编辑线索价值
     *
     * @param businessCluesVo
     * @return
     */

    @RequestMapping("/editCluesWorth")
    public ModelAndView editCluesWorth(BusinessCluesVo businessCluesVo) {
        ModelAndView mv = new ModelAndView("businessclues/edit_clue_worth");
        BusinessClues cluesInfo = businessCluesService.getCluesInfo(businessCluesVo.getBusinessCluesId());
        mv.addObject("businessCluesVo", cluesInfo);
        return mv;
    }

    /**
     * 保存编辑线索价值
     *
     * @param businessCluesVo
     * @return
     */
    @RequestMapping(value = "/saveCluesWorth")
    @ResponseBody
    public ResultInfo<?> saveCluesWorth(BusinessCluesVo businessCluesVo) {
        int i = businessCluesService.saveCluesWorth(businessCluesVo);
        if (i > 0) {
            return ResultInfo.success();
        } else {
            return ResultInfo.error("线索价值更新失败!");
        }
    }

    /**
     * 线索新增沟通记录
     *
     * @param traderCustomerTuoke
     * @param request
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/addCommunicatePage")
    public ModelAndView addCommunicatePage(TraderCustomerTuokeLabelVo traderCustomerTuoke, HttpServletRequest request) {
        User user = getSessionUser(request);
        ModelAndView mav = new ModelAndView("businessclues/add_communicate");
        TraderContact traderContact = new TraderContact();
        // 联系人
        traderContact.setTraderId(traderCustomerTuoke.getTraderId());
        traderContact.setIsEnable(1);
        traderContact.setTraderType(1);
        List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);

        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(1);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        TraderBaseInfoDto traderBaseInfo = traderCustomerService.getTraderBaseInfo(traderCustomerTuoke.getTraderId());
        mav.addObject("traderBaseInfo", traderBaseInfo);

        mav.addObject("businessClues", traderCustomerTuoke);
        mav.addObject("contactList", contactList);

        CommunicateRecord communicate = new CommunicateRecord();
        communicate.setBegintime(DateUtil.sysTimeMillis());
        communicate.setEndtime(DateUtil.sysTimeMillis() + 2 * 60 * 1000);
        mav.addObject("communicateRecord", communicate);

        //当前时间
        mav.addObject("nowDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 1).getTime());
        //15天后的时间
        mav.addObject("hideDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 30).getTime());
        mav.addObject("tagList", tagMap.get("list"));
        mav.addObject("page", tagMap.get("page"));
        mav.addObject("formToken", request.getAttribute("formToken"));
        return mav;
    }


    /**
     * 保存沟通记录
     *
     * @param communicateRecord
     * @param request
     * @param session
     * @return
     * @throws Exception
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveaddcommunicate")
    public ResultInfo saveAddCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request, HttpSession session) throws Exception {
        User user = getSessionUser(request);
        Boolean record;
        communicateRecord.setCompanyId(user.getCompanyId());
        communicateRecord.setCommunicateType(SysOptionConstant.ID_4083);// 线索
        communicateRecord.setRelatedId(communicateRecord.getBusinessCluesId());
        if (null != communicateRecord.getCommunicateRecordId() && communicateRecord.getCommunicateRecordId() > 0) {
            record = traderCustomerService.saveEditCommunicate(communicateRecord, request, session);
        } else {
            record = traderCustomerService.saveAddCommunicate(communicateRecord, request, session);
        }
        if (record) {
            return new ResultInfo(0, "操作成功！", communicateRecord.getBusinessCluesId() + "," + communicateRecord.getTraderId());
        } else {
            return new ResultInfo(1, "操作失败！");
        }
    }

    @Autowired
    private CommunicateVoiceTaskApi communicateVoiceTaskApi;

    /**
     * 编辑沟通记录
     * @param communicateRecord
     * @param traderCustomerTuoke
     * @param request
     * @return
     * @throws IOException
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/editcommunicate")
    public ModelAndView editCommunicate(CommunicateRecord communicateRecord, TraderCustomerTuokeLabelVo traderCustomerTuoke, HttpServletRequest request) throws IOException {
        User user = getSessionUser(request);
        ModelAndView mv = new ModelAndView("businessclues/edit_communicate");
        CommunicateRecord communicate = traderCustomerService.getCommunicate(communicateRecord);
        if(StringUtils.isNotBlank(communicate.getCoidUri())){
            String voiceStatusGptSuccess = "9";//Gpt解析成功
            CommunicateVoiceTaskDto taskDto = communicateVoiceTaskApi.selectByCommunicateRecordIdAndSence(communicateRecord.getCommunicateRecordId(),
                    AiConstant.getEnumByCommunicateType(communicateRecord.getCommunicateType()).getCode());

            mv.addObject("communicateTypeName",AiConstant.getEnumByCommunicateType(communicateRecord.getCommunicateType()).getName());

            if(taskDto != null && voiceStatusGptSuccess.equals(taskDto.getVoiceStatus()) ){
                List<VoiceFieldResultDto> voiceFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                        communicateRecord.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicateRecord.getCommunicateType()).getCode(),
                        AiConstant.CODE_GROUP_SUMMARY);
                mv.addObject("voiceFieldList", voiceFieldList);

                List<VoiceFieldResultDto> voiceToDoFieldList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                        communicateRecord.getCommunicateRecordId(), AiConstant.getEnumByCommunicateType(communicateRecord.getCommunicateType()).getCode(),
                        AiConstant.CODE_GROUP_TODOTASK);
                mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
            }else{
                String voiceStatusIgnore = "-1";//忽略的状态
                if(taskDto == null){
                    //如果解析任务不存在，即历史数据或忽略
                }else if(voiceStatusIgnore.equals(taskDto.getVoiceStatus())){
                    //忽略，则不做任务数据展示
                } else{  //非以上情况，即如果解析任务存在，但是在解析中，则展示AI解析中...
                    List<VoiceFieldResultDto> voiceFieldList = new ArrayList<>();
                    VoiceFieldResultDto tipVoiceField = new VoiceFieldResultDto();
                    tipVoiceField.setFieldName("提示");
                    tipVoiceField.setFieldResult("AI解析中...");
                    voiceFieldList.add(tipVoiceField);
                    mv.addObject("voiceFieldList", voiceFieldList);

                    List<VoiceFieldResultDto> voiceToDoFieldList = new ArrayList<>();
                    VoiceFieldResultDto tipVoiceToDoField = new VoiceFieldResultDto();
                    tipVoiceToDoField.setFieldName("提示");
                    tipVoiceToDoField.setFieldResult("AI解析中...");
                    voiceToDoFieldList.add(tipVoiceToDoField);
                    mv.addObject("voiceToDoFieldList", voiceToDoFieldList);
                }
            }
        }


        communicate.setTraderId(communicateRecord.getTraderId());

        TraderContact traderContact = new TraderContact();
        List<TraderContact> contactList = new ArrayList<>();
        // 联系人
        if (communicateRecord.getTraderId() != 0) {
            traderContact.setTraderId(communicateRecord.getTraderId());
            traderContact.setIsEnable(1);
            traderContact.setTraderType(1);
            contactList = traderCustomerService.getTraderContact(traderContact);
        } else {
            traderContact.setTraderContactId(0);
            traderContact.setIsEnable(1);
            traderContact.setName(communicate.getContact());
            traderContact.setMobile(communicate.getContactMob());
            contactList.add(traderContact);
        }
        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(1);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        mv.addObject("communicateRecord", communicate);

        mv.addObject("contactList", contactList);
        //当前时间
        mv.addObject("nowDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 1).getTime());
        //15天后的时间
        mv.addObject("hideDate", DateUtil.subDateByDays(new Date(System.currentTimeMillis()), 30).getTime());
        mv.addObject("tagList", tagMap.get("list"));
        mv.addObject("page", (Page) tagMap.get("page"));
        mv.addObject("method", "communicaterecord");
        mv.addObject("businessClues", traderCustomerTuoke);
        mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(communicate)));
        TraderBaseInfoDto traderBaseInfo = traderCustomerService.getTraderBaseInfo(traderCustomerTuoke.getTraderId());
        mv.addObject("traderBaseInfo", traderBaseInfo);
        mv.addObject("formToken", request.getAttribute("formToken"));
        return mv;
    }


}
