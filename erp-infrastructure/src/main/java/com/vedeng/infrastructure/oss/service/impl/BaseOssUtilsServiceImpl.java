package com.vedeng.infrastructure.oss.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.constants.Contant;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.file.api.constants.TerminationType;
import com.vedeng.file.api.request.UploadConstantsMapKey;
import com.vedeng.file.api.util.SignUtil;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.oss.service.domain.FileInfo;
import com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2021/12/15 16:36
 **/
@Component
@Slf4j
public class BaseOssUtilsServiceImpl implements OssUtilsService {


    @Autowired(required = false)
    private RestTemplate restTemplate;

    @Value("${oss_http}")
    private String ossHttp;

    /**
     * OSS地址
     */
    @Value("${oss_url}")
    private String ossUrl;
    /**
     * oss秘钥
     */
    @Value("${oss_key}")
    private String ossKey;
    /**
     * oss应用码
     */
    @Value("${oss_app_code}")
    private String ossAppCode;
    /**
     * oss文档路径
     */
    @Value("${oss_file_path}")
    private String ossFilePath;

    public static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public String uploadFileStream2Oss(InputStream inputStream, String suffixOfFile) {

        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        String authorization = SignUtil.sign(ossKey, (ossAppCode + timestamp).getBytes());
        String reqUrl = ossHttp + ossUrl + ossFilePath;
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        //封装请求body
        String originFileName = RandomStringUtils.random(50, "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");
        builder.addBinaryBody("file", inputStream, ContentType.MULTIPART_FORM_DATA, originFileName);
        // 类似浏览器表单提交，对应input的name和value
        builder.addTextBody(UploadConstantsMapKey.appCode, ossAppCode);
        builder.addTextBody(UploadConstantsMapKey.Authorization, authorization);
        builder.addTextBody(UploadConstantsMapKey.timestamp, String.valueOf(timestamp));
        builder.addTextBody(UploadConstantsMapKey.deviceInfo, "pc");
        builder.addTextBody(UploadConstantsMapKey.suffix, suffixOfFile);
        builder.addTextBody(UploadConstantsMapKey.previlege, "0");
        builder.addTextBody(UploadConstantsMapKey.termination, TerminationType.PC.getCode());
        //发送请求
        org.apache.http.HttpEntity entity = builder.build();
        HttpPost httpPost = new HttpPost(reqUrl);
        httpPost.setEntity(entity);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpResponse response = httpClient.execute(httpPost);
            //解析返回的结果，获取ossUrl和resourceId
            JSONObject jsonObject = JSON.parseObject(EntityUtils.toString(response.getEntity()));

            boolean result = Boolean.parseBoolean(jsonObject.getString("success"));
            if (result) {
                JSONObject data = JSON.parseObject(jsonObject.getString("data"));
                return data.getString("url");
            }
        } catch (IOException e) {
            log.error("file upload: e", e);
        }
        return null;
    }


    @Override
    public String upload2OssForInputStream(String suffix, String newFileName, InputStream inputStream) {
        String reqUrl = ossHttp + ossUrl + ossFilePath;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        String authorization = SignUtil.sign(ossKey,(ossAppCode+timestamp).getBytes());
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.setCharset(StandardCharsets.UTF_8);
        builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
        //封装请求body
        builder.addBinaryBody("file",inputStream, ContentType.create("multipart/form-data","utf-8"),newFileName);
        // 类似浏览器表单提交，对应input的name和value
        builder.addTextBody(UploadConstantsMapKey.appCode,ossAppCode);
        builder.addTextBody(UploadConstantsMapKey.Authorization, authorization);
        builder.addTextBody(UploadConstantsMapKey.timestamp, String.valueOf(timestamp));
        builder.addTextBody(UploadConstantsMapKey.deviceInfo,"pc");
        builder.addTextBody(UploadConstantsMapKey.suffix,suffix);
        builder.addTextBody(UploadConstantsMapKey.previlege, "0");
        builder.addTextBody(UploadConstantsMapKey.termination, TerminationType.PC.getCode());
        //是否需要压缩，如果是，file会压缩默认的尺寸
//        builder.addTextBody(UploadConstantsMapKey.isNeedCompress, compress+"");

        try{
            //发送请求
            org.apache.http.HttpEntity entity = builder.build();
            HttpPost httpPost = new HttpPost(reqUrl);
            httpPost.setEntity(entity);
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpResponse response = httpClient.execute(httpPost);

            //解析返回的结果，获取ossUrl和resourceId
            JSONObject jsonObject = JSONObject.parseObject(EntityUtils.toString(response.getEntity()));
            boolean result = Boolean.parseBoolean(jsonObject.getString("success"));

            if (result) {
                JSONObject data = (JSONObject)jsonObject.get("data");
                String ossFileUrl = data.getString("url");
                return ossFileUrl;
            }
            log.info("上传文件时返回失败:{}",jsonObject.toJSONString());
        }catch ( Exception e){
            log.error("上传文件到oss失败，失败原因",e);
        }
        return null;
    }

    @Override
    public String migrateFile2Oss(String originSourceUrl, String suffix, String fileName, UrlToPdfParam urlToPdfParam) {
        RequestCallback requestCallback = request -> {
            request.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.writeValue(request.getBody(), urlToPdfParam);
        };

        ResponseExtractor<String> responseExtractor = response -> {
            //发送到oss
            return upload2OssForInputStream("pdf", fileName, ossHttp + ossUrl + ossFilePath, response.getBody());
        };
        try {
            return restTemplate.execute(originSourceUrl, HttpMethod.POST, requestCallback, responseExtractor);
        } catch (RestClientException ex) {
            log.warn("请求访问URL：{}，文件：{}，异常：", originSourceUrl, fileName, ex);
            return null;
        } catch (Exception e) {
            log.error("请求访问URL：{}，文件：{}，异常：", originSourceUrl, fileName, e);
            return null;
        }
    }

    @Override
    public String upload2OssForInputStream(String suffix, String newFileName, String ossTargetUrl, InputStream inputStream) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        queryParams.add("appCode", ossAppCode);
        String authorization = SignUtil.sign(ossKey, (ossAppCode + timestamp).getBytes());
        queryParams.add("Authorization", authorization);
        queryParams.add("timestamp", String.valueOf(timestamp));
        queryParams.add("deviceInfo", "pc");
        queryParams.add("sourceId", "erp");
        queryParams.add("suffix", suffix);
        queryParams.add("previlege", "0");
        queryParams.add("termination", TerminationType.PC.getCode());
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder
                .fromHttpUrl(ossTargetUrl)
                .queryParams(queryParams);

        MultiValueMap<String, Object> bodyMap = new LinkedMultiValueMap<>();
        Resource resource = new InputStreamResource(inputStream) {

            @Override
            public String getFilename() {
                return newFileName;
            }

            @Override
            public long contentLength() throws IOException {
                return inputStream.available();
            }
        };
        bodyMap.add("file", resource);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(bodyMap, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(uriComponentsBuilder.build().encode().toUri(), httpEntity, String.class);

        //解析返回的结果，获取ossUrl和resourceId
        JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
        boolean result = Boolean.parseBoolean(jsonObject.getString("success"));

        if (!result) {
            return null;
        }
        JSONObject data = JSONObject.parseObject(jsonObject.getString("data"));
        return data.getString("url");
    }

    @Override
    public FileInfo upload2Oss(HttpServletRequest request, MultipartFile upfile) {
        String fileName = upfile.getOriginalFilename();
        // 文件后缀名称
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        try {
            return this.uploadFile2Oss(request, upfile, suffix, false);
        } catch (Exception ex) {
            log.error("图片上传OSS失败，请重新上传", ex);
            return new FileInfo(-1, "上传失败，请重新上传");
        }
    }

    @Override
    public FileInfo upload2OssByOriginalName(HttpServletRequest request, MultipartFile upfile) {
        String fileName = upfile.getOriginalFilename();
        // 文件后缀名称
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        try {
            return this.uploadFile2Oss(request, upfile,fileName, suffix, false);
        } catch (Exception ex) {
            log.error("图片上传OSS失败，请重新上传", ex);
            return new FileInfo(-1, "上传失败，请重新上传");
        }
    }

    private FileInfo uploadFile2Oss(HttpServletRequest request, MultipartFile oldfile,String fileName, String suffix, boolean compress) throws IOException {
        String newFileName = fileName;
        if(StringUtils.isEmpty(fileName)){
            String time = DateUtil.current() + "";
            Random random = new Random();
            time = time + "_" + String.format("%04d", random.nextInt(10000));// 随机数4位，不足4位，补位
            newFileName = time + "." + suffix;
        }

        String[] splitFileName = oldfile.getOriginalFilename().split("." + suffix);
        MultipartFile file;
        if (splitFileName.length > 0) {
            String orgfileName = splitFileName[0];
            file = getNewFile(orgfileName, oldfile);
        } else {
            file = oldfile;
        }
        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        String authorization = SignUtil.sign(ossKey, (ossAppCode + timestamp).getBytes());
        String reqUrl = ossHttp + ossUrl + ossFilePath;
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.setCharset(StandardCharsets.UTF_8);
        builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
        //封装请求body
        builder.addBinaryBody("file", file.getInputStream(), ContentType.create("multipart/form-data", "utf-8"), file.getOriginalFilename());
        // 类似浏览器表单提交，对应input的name和value
        builder.addTextBody(UploadConstantsMapKey.appCode, ossAppCode);
        builder.addTextBody(UploadConstantsMapKey.Authorization, authorization);
        builder.addTextBody(UploadConstantsMapKey.timestamp, String.valueOf(timestamp));
        builder.addTextBody(UploadConstantsMapKey.deviceInfo, request.getHeader("User-Agent"));
        builder.addTextBody(UploadConstantsMapKey.suffix, suffix);
        builder.addTextBody(UploadConstantsMapKey.previlege, "0");
        builder.addTextBody(UploadConstantsMapKey.termination, TerminationType.PC.getCode());
        //是否需要压缩，如果是，file会压缩默认的尺寸
        builder.addTextBody(UploadConstantsMapKey.isNeedCompress, compress + "");

        //发送请求
        org.apache.http.HttpEntity entity = builder.build();
        HttpPost httpPost = new HttpPost(reqUrl);
        httpPost.setEntity(entity);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpResponse response = httpClient.execute(httpPost);

        //解析返回的结果，获取ossUrl和resourceId
        JSONObject jsonObject = JSONObject.parseObject(EntityUtils.toString(response.getEntity()));
        boolean result = Boolean.parseBoolean(jsonObject.getString("success"));
        if (result) {
            JSONObject data = JSONObject.parseObject(jsonObject.get("data").toString());
            String ossFileUrl = data.getString("url");
            String resourceId = data.getString("resourceId");
            String[] domainAndUri = this.getDomainAndUriFromUrl(ossFileUrl);
            if (domainAndUri != null && StringUtil.isNotBlank(domainAndUri[0]) && StringUtil.isNotBlank(domainAndUri[1])) {
                FileInfo fileInfo = new FileInfo(0, "上传成功", newFileName, domainAndUri[1],
                        domainAndUri[0], resourceId, null);
                fileInfo.setScheme(ossHttp);
                fileInfo.setPrefix(suffix);
                if (domainAndUri[0].startsWith("http://") || domainAndUri[0].startsWith("https://")) {
                    domainAndUri[0] = domainAndUri[0].replaceFirst("http://|https://", "");
                }
                if (domainAndUri[0].endsWith("/")) {
                    domainAndUri[0] = domainAndUri[0].substring(0, domainAndUri[0].length() - 1);
                }
                fileInfo.setDomain(domainAndUri[0]);
                return fileInfo;
            }
        }
        return new FileInfo(-1, "上传失败，文件服务异常：" + jsonObject.getString("message"));
    }

    private FileInfo uploadFile2Oss(HttpServletRequest request, MultipartFile oldfile, String suffix, boolean compress) throws IOException {
         return uploadFile2Oss(request,oldfile,null,suffix,compress);
    }

    private MultipartFile getNewFile(String fileName, MultipartFile currentFile) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return currentFile.getName();
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return currentFile.getContentType();
            }

            @Override
            public boolean isEmpty() {
                return currentFile.isEmpty();
            }

            @Override
            public long getSize() {
                return currentFile.getSize();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return currentFile.getBytes();
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return currentFile.getInputStream();
            }

            @Override
            public void transferTo(File file) throws IOException, IllegalStateException {

            }
        };
    }

    /**
     * 从UrlUtils迁移过来
     *
     * @param pic
     * @return
     */
    private String[] getDomainAndUriFromUrl(String pic) {
        if (StringUtil.isBlank(pic)) {
            return null;
        }
        String[] urlArray = new String[2];
        try {
            URL url = new URL(pic);
            String host = url.getHost();
            String path = url.getPath();
            String param = url.getQuery();
            urlArray[0] = host;
            urlArray[1] = path + "?" + param;
            return urlArray;
        } catch (Exception ex) {
        }
        return null;
    }

}
