package com.test.kingdee;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeMaterialCommand;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialSubHeadEntityDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeMaterialMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeMaterialCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeMaterialConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.Data;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;


/**
 * 金蝶  物料测试类
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeMaterial {

    @Autowired
    private KingDeeMaterialCommandConvertor materialCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeMaterialMapper kingDeeMaterialMapper;

    @Autowired
    private KingDeeMaterialConvertor dtoConvertor;

    @Test
    public void kingDeeSaveMaterial() {

        //构建金蝶请求业务参数
        KingDeeMaterialDto dto = new KingDeeMaterialDto(KingDeeBizEnums.saveMaterial);
        //必填参数如下---------------------------------------
        //【必填】 物料内码 0代表新增物料
        dto.setFmaterialid("0");
        //【必填】多组织填写对应组织编码，贝登医疗101
        dto.setFCreateOrgId("101");
        //【必填】多组织填写对应组织编码，贝登医疗101
        dto.setFUseOrgId("101");
        //【必填】物料编码  不允许重复
        dto.setFNumber("V500005");
        //【必填】物料名称
        dto.setFName("这是测试商品修改顶顶顶");
        //物料基本信息
        KingDeeMaterialSubHeadEntityDto sonDto = new KingDeeMaterialSubHeadEntityDto();
        sonDto.setFErpClsID("1");
        //存货类别  该字段参见  存货类别表 贝登商品的分类
        sonDto.setFCategoryID("CHLB03_SYS");
        //基本单位  该字段参加 金蝶计量单位的数据字典 贝登商品的计量单位
        sonDto.setFBaseUnitId("08");
        dto.setSubHeadEntity(sonDto);

        //非必填参数如下(没有值，但是key需要传递)------------------------------------
        //【非必填】规格型号
        dto.setFSpecification("SP-001");
        //【非必填】 填写编码（贝海专用）
        dto.setFQzokDhh("A1");
        //【非必填】供应商物料编码
        dto.setFQzokGyswlbm("A2");
        //【非必填】品牌
        dto.setFQzokPpptext("A3");
        //【非必填】注册证号
        dto.setFQzokZczhtext("这是测试注册证修改请求");
        //【非必填】是否为医疗器械产品
        dto.setFQzokYlqxtext("A4");
        //【非必填】医疗器械产线
        dto.setFQzokYlqxcxtext("A5");
        //【非必填】医疗器械用途
        dto.setFQzokYlqxyttext("A6");
        //【非必填】医疗器械细分类
        dto.setFQzokYlqxxfltext("A7");
        //【非必填】医疗器械分类
        dto.setFQzokYlqxfltext("A8");
        //【非必填】商品运营一级分类
        dto.setFQzokSpyyyjfltext("A9");
        //【非必填】商品运营二级分类
        dto.setFQzokSpyyejfltext("A10");
        //【非必填】商品运营三级分类
        dto.setFQzokSpyysjfltext("A11");
        //【非必填】商品运营四级分类
        dto.setFQzokSpyysjfltext("A12");
        //【非必填】商品运营五级分类
        dto.setFQzokSpyywjfltext("A13");
        //【非必填】非医疗器械一级分类
        dto.setFQzokFylqxyjfltext("A14");
        //【非必填】非医疗器械二级分类
        dto.setFQzokFylqxejfltext("A15");
        //【非必填】产线划分类型
        dto.setFQzokCxfl("A16");
        //【非必填】主要产品线
        dto.setFQzokZycpx("A17");
        System.out.println("dto-->"+JSON.toJSON(dto));
        //转换为金蝶入参
        KingDeeMaterialCommand command = materialCommandConvertor.toCommand(dto);
        System.out.println("入参commad-->"+JSON.toJSON(command));
        //调用金蝶方法
         kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));


        return;
//        System.out.println("金蝶返回-->"+JSON.toJSON(successEntities));
//
//        if (CollUtil.isNotEmpty(successEntities)) {
//            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
//            dto.setFmaterialid(successEntity.getId());
//            //金蝶业务数据入表
//            kingDeeMaterialMapper.insertSelective(dtoConvertor.toEntity(dto));
//        System.out.println("finish-->");
//        }
    }

    //1、字段类型定义不清楚，只能靠猜
    //2、接口是否自己调试通？
    //示例json和下面注释存在字段不一致情况，已调整两个，反馈是 "这个字段优化掉了，普票的漏修改了，现在修改完成。"
    // QZOK_BOS_YSTZD接口不存在？


    //调用记录-1 失败
    // 入参 {"fUseOrgId":{"fNumber":"101"},"fmaterialid":0,"subHeadEntity":{"fBaseUnitId":{"fNumber":"8"},"fErpClsID":"1","fCategoryID":{"fNumber":"CHLB03_SYS"}},"fName":"这是测试商品","fSpecification":"SP-001","fCreateOrgId":{"fNumber":"101"},"fNumber":"V123456"}
//    "Errors":[{"FieldName":"FPurchaseUnitId","Message":"字段“采购单位”是必填项","DIndex":0},
//    {"FieldName":"FPurchasePriceUnitId","Message":"字段“采购计价单位”是必填项","DIndex":0},
//    {"FieldName":"FMinIssueUnitId","Message":"字段“最小发料批量单位”是必填项","DIndex":0},
//    {"FieldName":"FSaleUnitId","Message":"字段“销售单位”是必填项","DIndex":0},
//    {"FieldName":"FStoreUnitID","Message":"字段“库存单位”是必填项","DIndex":0},
//    {"FieldName":"FSalePriceUnitId","Message":"字段“销售计价单位”是必填项","DIndex":0},
//    {"FieldName":"FBaseUnitId","Message":"字段“基本单位”是必填项","DIndex":0},
//    {"FieldName":"FStoreUnitID","Message":"编码为“V123456”的物料，库存单位不能与辅助单位相同!","DIndex":0}],
//    "SuccessEntitys":[],"SuccessMessages":[],"MsgCode":11},"Id":"","Number":"","NeedReturnData":[{}]}}]



    //调用记录 -2 失败
    //{"fUseOrgId":{"fNumber":"101"},"fQzokZczhtext":"","fmaterialid":0,"subHeadEntity":{"fBaseUnitId":{"fNumber":"8"},"fErpClsID":"1","fCategoryID":{"fNumber":"CHLB03_SYS"}},"fQzokYlqxxfltext":"","fQzokFylqxejfltext":"","fQzokYlqxfltext":"","fQzokSpyysjfltext":"","fQzokCxfl":"","fQzokZycpx":"","fName":"这是测试商品555666","fQzokDhh":"","fQzokGyswlbm":"","fSpecification":"SP-001","fCreateOrgId":{"fNumber":"101"},"fQzokYlqxyttext":"","fQzokYlqxcxtext":"","fQzokSpyyejfltext":"","fQzokFylqxyjfltext":"","fQzokSpyywjfltext":"","fQzokSpyyyjfltext":"","fQzokPpptext":"","fNumber":"V555666","fQzokYlqxtext":""}
    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":"FPurchaseUnitId","Message":"字段“采购单位”是必填项","DIndex":0},{"FieldName":"FPurchasePriceUnitId","Message":"字段“采购计价单位”是必填项","DIndex":0},{"FieldName":"FMinIssueUnitId","Message":"字段“最小发料批量单位”是必填项","DIndex":0},{"FieldName":"FSaleUnitId","Message":"字段“销售单位”是必填项","DIndex":0},{"FieldName":"FStoreUnitID","Message":"字段“库存单位”是必填项","DIndex":0},{"FieldName":"FSalePriceUnitId","Message":"字段“销售计价单位”是必填项","DIndex":0},{"FieldName":"FBaseUnitId","Message":"字段“基本单位”是必填项","DIndex":0},{"FieldName":"FStoreUnitID","Message":"编码为“V555666”的物料，库存单位不能与辅助单位相同!","DIndex":0}],"SuccessEntitys":[],"SuccessMessages":[],"MsgCode":11},"Id":"","Number":"","NeedReturnData":[{}]}}]

    //调用记录 -3  FBaseUnitId 修改为 L  因为测试环境 没有 8 正确的值 也应该是 08，本地EXcel内 写的是8
    //{"fUseOrgId":"101","fQzokZczhtext":"这是测试注册证","fmaterialid":0,"subHeadEntity":{"fBaseUnitId":"L","fErpClsID":"1","fCategoryID":"CHLB03_SYS"},"fQzokYlqxxfltext":"A7","fQzokFylqxejfltext":"A15","fQzokYlqxfltext":"A8","fQzokSpyysjfltext":"A12","fQzokCxfl":"A16","fQzokZycpx":"A17","fName":"这是测试商品V555669","fQzokDhh":"A1","fQzokGyswlbm":"A2","fSpecification":"SP-001","kingDeeBizEnums":"saveMaterial","fCreateOrgId":"101","fQzokYlqxyttext":"A6","formId":"BD_MATERIAL","fQzokYlqxcxtext":"A5","fQzokSpyyejfltext":"A10","fQzokFylqxyjfltext":"A14","fQzokSpyywjfltext":"A13","fQzokSpyyyjfltext":"A9","fQzokPpptext":"A3","fNumber":"V555669","fQzokYlqxtext":"A4"}
    //入参commad-->{"fUseOrgId":{"fNumber":"101"},"fmaterialid":0,"subHeadEntity":{"fBaseUnitId":{"fNumber":"L"},"fErpClsID":"1","fCategoryID":{"fNumber":"CHLB03_SYS"}},"f_QZOK_FYLQXYJFLTEXT":"A14","f_QZOK_CXFL":"A17","f_QZOK_YLQXXFLTEXT":"A7","f_QZOK_YLQXYTTEXT":"A6","fName":"这是测试商品V555669","f_QZOK_GYSWLBM":"A2","f_QZOK_PPPTEXT":"A3","f_QZOK_ZCZHTEXT":"这是测试注册证","fSpecification":"SP-001","f_QZOK_YLQXFLTEXT":"A8","fCreateOrgId":{"fNumber":"101"},"f_QZOK_FYLQXEJFLTEXT":"A15","f_QZOK_YLQXCXTEXT":"A5","f_QZOK_SPYYEJFLTEXT":"A10","f_QZOK_YLQXTEXT":"A4","fNumber":"V555669","f_QZOK_DHH":"A1","f_QZOK_ZYCPX":"A16","f_QZOK_SPYYWJFLTEXT":"A13","f_QZOK_SPYYYJFLTEXT":"A9","f_QZOK_SPYYSJFLTEXT":"A12"}
    // 返回成功


    //调用记录 - 4 失败 修改   107990  拿接口返回的  id进行该记录的修改 没法修改
    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":"","Message":"编码为“V555669”的物料，已审核的单据不允许提交!","DIndex":0}],"SuccessEntitys":[],"SuccessMessages":[],"MsgCode":11},"Id":"","Number":"","NeedReturnData":[{}]}}]


    //调用记录  - 5 成功 修改 107990   IsAutoSubmitAndAudit 不传该字段
    //接口  [{"parameters":["BD_MATERIAL","{\"Creator\":0,\"NeedReturnFields\":[],\"NeedUpDateFields\":[],\"isAutoAdjustField\":true,\"Model\":{\"fmaterialid\":\"107990\",\"fCreateOrgId\":{\"FNumber\":\"101\"},\"fUseOrgId\":{\"FNumber\":\"101\"},\"fNumber\":\"V555669\",\"fName\":\"这是测试商品修改顶顶顶\",\"fSpecification\":\"SP-001\",\"F_QZOK_DHH\":\"A1\",\"F_QZOK_GYSWLBM\":\"A2\",\"F_QZOK_PPPTEXT\":\"A3\",\"F_QZOK_ZCZHTEXT\":\"这是测试注册证修改请求\",\"F_QZOK_YLQXTEXT\":\"A4\",\"F_QZOK_YLQXCXTEXT\":\"A5\",\"F_QZOK_YLQXYTTEXT\":\"A6\",\"F_QZOK_YLQXXFLTEXT\":\"A7\",\"F_QZOK_YLQXFLTEXT\":\"A8\",\"F_QZOK_SPYYYJFLTEXT\":\"A9\",\"F_QZOK_SPYYEJFLTEXT\":\"A10\",\"F_QZOK_SPYYSJFLTEXT\":\"A12\",\"F_QZOK_SPYYWJFLTEXT\":\"A13\",\"F_QZOK_FYLQXYJFLTEXT\":\"A14\",\"F_QZOK_FYLQXEJFLTEXT\":\"A15\",\"F_QZOK_CXFL\":\"A17\",\"F_QZOK_ZYCPX\":\"A16\",\"subHeadEntity\":{\"fErpClsID\":\"1\",\"fCategoryID\":{\"FNumber\":\"CHLB03_SYS\"},\"fBaseUnitId\":{\"FNumber\":\"L\"}}},\"formId\":\"BD_MATERIAL\"}"]}]
    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":107990,"Number":"V555669","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":107990,"Number":"V555669","NeedReturnData":[{}]}}]
    //金蝶返回-->[{"number":"V555669","dIndex":0,"id":"107990"}]


    //调用记录  -6 失败 再次新增  fBaseUnitId 传 L，接口失败 ，被告知需要传 01，02，03 类似的值
    //有截图


    @Test
    public void getKingDeeMaterial() {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.BD_MATERIAL);
        java.util.List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("fNumber")
                .value("V111409")
                .build());
        queryParam.setFilterString(queryFilterDtos);
        List<KingdeeMaterialPayCommonQueryResultDto> query = kingDeeBaseApi.query(queryParam, KingdeeMaterialPayCommonQueryResultDto.class);
        System.out.println(JSON.toJSONString(query));
    }

    @Data
    static class  KingdeeMaterialPayCommonQueryResultDto{

        private String FMATERIALID;
        private String fNumber;
        private KingDeeNumberCommand FBaseUnitId;

    }
}
