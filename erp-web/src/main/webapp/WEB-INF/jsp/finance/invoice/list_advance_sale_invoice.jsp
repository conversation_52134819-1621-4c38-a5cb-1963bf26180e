<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="提前开票记录列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<style>
	/* 固定"操作"列 */
	td:last-child {
		position: sticky;
		right: 0;
		background-color: #fff; /* 可选，背景颜色，使列在右侧固定时更清晰 */
		z-index: 2; /* 确保列位于上方 */
	}

	/* 固定"操作"列 */
	th:last-child {
		position: sticky;
		right: 0;
		background-color: #E5E5E5; /* 可选，背景颜色，使列在右侧固定时更清晰 */
		z-index: 2; /* 确保列位于上方 */
	}

    .loading-icon {
        border: 5px solid #f3f3f3;
        border-radius: 50%;
        border-top: 5px solid #3498db;
        width: 45px;
        height: 45px;
        animation: spin 2s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
<script type="text/javascript" src='<%=basePath%>static/js/finance/invoice/list_advance_sale_invoice.js?rnd=${resourceVersionKey}'></script>
<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/xm-select.js"></script>
<style>
	.xm-option-icon {
		height: auto;
		background: none;
	}
	xm-select {
		min-height: 27px;
		line-height: 36px;
	}
</style>

<input type="hidden" id="ticketReasonEqualList" value="${invoiceApply.ticketReasonEqualList}">
	<div class="main-container">
		<div class="list-pages-search">
			<form method="post" id="search" action="<%=basePath%>finance/invoice/getAdvanceInvoiceApplyListPage.do">
				<ul>
					<li>
						<label class="infor_name">关联单号</label>
						<input type="text" class="input-middle" name="saleorderNo" id="saleorderNo" value="${invoiceApply.saleorderNo}" />
					</li>
					<li>
						<label class="infor_name">客户名称</label>
						<input type="text" class="input-middle" name="traderName" id="traderName" value="${invoiceApply.traderName}" />
					</li>
					<li>
						<label class="infor_name">收款状态</label>
						<select class="input-middle" name="paymentStatus" id="paymentStatus">
							<option value="">全部</option>
							<option <c:if test="${invoiceApply.paymentStatus eq 0}">selected</c:if> value="0">未收款</option>
							<option <c:if test="${invoiceApply.paymentStatus eq 1}">selected</c:if> value="1">部分收款</option>
							<option <c:if test="${invoiceApply.paymentStatus eq 2}">selected</c:if> value="2">已收款</option>
						</select>
					</li>
					<li>
						<label class="infor_name">发货状态</label>
						<select class="input-middle" name="deliveryStatus" id="deliveryStatus">
							<option value="">全部</option>
							<option <c:if test="${invoiceApply.deliveryStatus eq 0}">selected</c:if> value="0">未发货</option>
							<option <c:if test="${invoiceApply.deliveryStatus eq 1}">selected</c:if> value="1">部分发货</option>
							<option <c:if test="${invoiceApply.deliveryStatus eq 2}">selected</c:if> value="2">全部发货</option>
						</select>
					</li>
					<li>
						<label class="infor_name">订单来源</label>
						<select class="input-middle" name="orderSourceType" id="orderSourceType">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.orderSourceType eq 0}">selected</c:if> value="0">线上</option>
							<option <c:if test="${invoiceApply.orderSourceType eq 1}">selected</c:if> value="1">线下</option>
						</select>
					</li>
					<li>
						<label class="infor_name">确认单状态</label>
						<select class="input-middle" name="confirmationFormAudit" id="confirmationFormAudit">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.confirmationFormAudit eq 0}">selected</c:if> value="0">待提交审核</option>
							<option <c:if test="${invoiceApply.confirmationFormAudit eq 1}">selected</c:if> value="1">审核中</option>
							<option <c:if test="${invoiceApply.confirmationFormAudit eq 2}">selected</c:if> value="2">审核通过</option>
							<option <c:if test="${invoiceApply.confirmationFormAudit eq 3}">selected</c:if> value="3">审核不通过</option>
						</select>
					</li>
					<li>
						<label class="infor_name">合同状态</label>
						<select class="input-middle" name="contractVerifyStatus" id="contractVerifyStatus">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.contractVerifyStatus eq 5}">selected</c:if> value="5">未上传</option>
							<option <c:if test="${invoiceApply.contractVerifyStatus eq 4}">selected</c:if> value="4">待审核</option>
							<option <c:if test="${invoiceApply.contractVerifyStatus eq 0}">selected</c:if> value="0">审核中</option>
							<option <c:if test="${invoiceApply.contractVerifyStatus eq 1}">selected</c:if> value="1">审核通过</option>
							<option <c:if test="${invoiceApply.contractVerifyStatus eq 2}">selected</c:if> value="2">审核不通过</option>
						</select>
					</li>
					<li>
						<label class="infor_name">审核状态</label>
						<select class="input-middle" name="advanceValidStatus" id="advanceValidStatus">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.advanceValidStatus eq 0}">selected</c:if> value="0">审核中</option>
							<option <c:if test="${invoiceApply.advanceValidStatus eq 1}">selected</c:if> value="1">审核通过</option>
							<option <c:if test="${invoiceApply.advanceValidStatus eq 2}">selected</c:if> value="2">审核不通过</option>
						</select>
					</li>
					<li>
						<label class="infor_name">申请方式</label>
						<select class="input-middle" name="applyMethod" id="applyMethod">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.applyMethod eq 0}">selected</c:if> value="0">手动申请触发</option>
							<option <c:if test="${invoiceApply.applyMethod eq 1}">selected</c:if> value="1">定时任务触发</option>
							<option <c:if test="${invoiceApply.applyMethod eq 2}">selected</c:if> value="2">票货同行物流部申请</option>
							<option <c:if test="${invoiceApply.applyMethod eq 3}">selected</c:if> value="3">客户在线申请开票</option>
							<option <c:if test="${invoiceApply.applyMethod eq 4}">selected</c:if> value="4">售后手动申请</option>
						</select>
					</li>
					<li>
						<div class="infor_name specialinfor" >
							<input type="hidden" name="searchDateType" value="second"/><!-- 标记是否新打开查询页 -->
							<select name="dateType" id="dateType" style="width:75px;">
								<option value="1" <c:if test="${invoiceApply.dateType eq 1}">selected</c:if>>申请日期</option>
								<option value="2" <c:if test="${invoiceApply.dateType eq 2}">selected</c:if>>审核日期</option>
							</select>
						</div>
						<input type="hidden" id="de_startTime" value="${(empty searchDateType)?startTime:de_startTime}"/>
						<input class="Wdate f_left input-smaller96 m0" style="width:90px;" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()" name="startTime"	id="startTime" value="${startTime}">
						<div class="f_left ml1 mr1 mt4">-</div> 
						<input type="hidden" id="de_endTime" value="${(empty searchDateType)?endTime:de_endTime}"/>
						<input class="Wdate f_left input-smaller96" style="width:90px;" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()" name="endTime" id="endTime" value="${endTime}">
					</li>
					<li>
						<label class="infor_name">账期支付</label>
						<select class="input-middle" name="haveAccountPeriod" id="haveAccountPeriod">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.haveAccountPeriod eq 0}">selected</c:if> value="0">否</option>
							<option <c:if test="${invoiceApply.haveAccountPeriod eq 1}">selected</c:if> value="1">是</option>
						</select>
					</li>
					<li style="margin-left: 42px">
						<label>不通过原因</label>
						<select class="input-middle" name="ticketReason" id="ticketReason" style="	margin-left: 5px;width: 59px;">
							<option <c:if test="${invoiceApply.ticketReason eq 'equal'}">selected</c:if> value="equal">等于</option>
							<option <c:if test="${invoiceApply.ticketReason eq 'contain'}">selected</c:if> value="contain">包含</option>
							<option <c:if test="${invoiceApply.ticketReason eq 'notContain'}">selected</c:if> value="notContain">不包含</option>
						</select>
					</li>
					<li style="width: 104px;height: 26px">
						<div id="ticketReasonEqual" name = "ticketReasonEqual"></div>
					</li>
					<li>
						<label class="infor_name">开票信息</label>
						<select class="input-middle" name="invoiceInfoType" id="invoiceInfoType">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.invoiceInfoType eq 0}">selected</c:if> value="0">标准开票信息</option>
							<option <c:if test="${invoiceApply.invoiceInfoType eq 1}">selected</c:if> value="1">自定开票信息</option>
						</select>
					</li>
					<li>
						<label class="infor_name">票面备注</label>
						<select class="input-middle" name="invoiceComments" id="invoiceComments">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.invoiceComments eq 0}">selected</c:if> value="0">有</option>
							<option <c:if test="${invoiceApply.invoiceComments eq 1}">selected</c:if> value="1">无</option>
						</select>
					</li>
					<li>
						<label class="infor_name">开票留言</label>
						<select class="input-middle" name="orderComments" id="orderComments">
							<option value="-1">全部</option>
							<option <c:if test="${invoiceApply.orderComments eq 0}">selected</c:if> value="0">有</option>
							<option <c:if test="${invoiceApply.orderComments eq 1}">selected</c:if> value="1">无</option>
						</select>
					</li>
				</ul>
				<div class="tcenter">
					<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
					<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="resetPage();">重置</span>
					<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="doAdvanceAudit();">自动审核</span>
					<%--<span class="bt-small bg-light-blue bt-bg-style" onclick="exportInvoiceAdvanceApplyList()">导出列表</span>--%>
				</div>
			</form>
		</div>
		<div class="normal-list-page" id="list_table">
			<div class="fixdiv">
				<div class='superdiv'>
					<table class="table table-bordered table-striped table-condensed table-centered">
						<thead>
							<tr>
								<th class="wid6">
									<span style="vertical-align:middle;">全选&nbsp;</span>
									<input type="checkbox" name="checkAllOpt" style="vertical-align:middle;" onchange="checkAllOpt(this);">
								</th>
								<th class="wid4">序号</th>
								<th class="wid12">关联单号</th>
								<th class="wid20">客户名称</th>
								<th class="wid9">订单实际金额</th>
								<th class="wid12">申请开票金额</th>
								<th class="wid7">发票类型</th>
								<th class="wid7">开票信息</th>
								<th class="wid7">票面备注</th>
								<th class="wid7">开票留言</th>
								<th class="wid12">提前申请原因</th>
								<th class="wid12">不通过原因</th>
								<th class="wid7">账期支付</th>
								<th class="wid7">售后状态</th>
								<th class="wid7">收款状态</th>
								<th class="wid7">发货状态</th>
								<th class="wid7">收货状态</th>
								<th class="wid7">合同状态</th>
								<th class="wid8">确认单状态</th>
								<th class="wid8">申请来源</th>
								<th class="wid12">申请方式</th>
								<th class="wid14">申请时间</th>
								<th class="wid8">申请人</th>
								<th class="wid8">审核状态</th>
								<th class="wid10">销售部门</th>
								<th class="wid8">归属销售</th>
								<th class="wid8">订单来源</th>
								<th class="wid27">操作</th>
							</tr>
						</thead>
						<tbody>
							<c:set var="pageAmount" value="0"></c:set><!-- 当前页总额 -->
							<c:set var="pageNum" value="0"></c:set><!-- 当前页记录数 -->
							<c:forEach var="list" items="${openInvoiceApplyList}" varStatus="num">
								<c:set var="pageNum" value="${pageNum + 1}"></c:set>
								<tr>
									<!-- 选择框 -->
									<td><input type="checkbox" name="checkName" value="${list.invoiceApplyId}" onclick="checkedOnly(this)"/></td>
									<!-- 序号 -->
									<td>${num.count}</td>
									<!-- 关联单号 -->
									<td>
										<c:if test="${list.type == 505}">
											<a class="addtitle" style="color:#3384ef" href="javascript:void(0);" tabtitle='{"num":"viewfinancesaleorder${list.relatedId}","link":"./finance/invoice/viewSaleorder.do?saleorderId=${list.relatedId}","title":"订单信息"}'>${list.saleorderNo}</a>
										</c:if>
										<c:if test="${list.type == 504}">
											<a class="addtitle" style="color:#3384ef" href="javascript:void(0);" tabtitle='{"num":"viewfinancesaleorder${list.relatedId}","link":"/finance/after/getFinanceAfterSaleDetail.do?afterSalesId=${list.relatedId}&subjectType=${list.afterSaleSubjectType}&type=${list.afterSaleType}","title":"订单信息"}'>${list.saleorderNo}</a>
										</c:if>
									</td>
									<!-- 客户名称 -->
									<td>
										<a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"viewfinancecustomer${list.traderId}", "link":"./trader/customer/baseinfo.do?traderId=${list.traderId}", "title":"客户信息"}'>${list.traderName}</a>
									</td>
									<!-- 订单实际金额 -->
									<td>
										<c:set var="pageAmount" value="${list.totalAmount + pageAmount}"></c:set>
										<fmt:formatNumber type="number" value="${list.totalAmount}" pattern="0.00" maxFractionDigits="2" />
									</td>
									<!-- 申请开票金额	-->
									<td><fmt:formatNumber type="number" value="${(empty list.applyAmount or list.applyAmount eq 0) ? list.totalAmount : list.applyAmount}" pattern="0.00" maxFractionDigits="2" /></td>
									<!-- 发票类型 -->
									<td>${list.invoiceTypeStr}</td>
									<!-- 开票信息 -->
									<td>
										<c:choose>
											<c:when test="${list.invoiceInfoType eq 0}">标准开票信息</c:when>
											<c:when test="${list.invoiceInfoType eq 1}">自定开票信息</c:when>
										</c:choose>
									</td>
									<!-- 票面备注 -->
									<td>
											${list.comments}
									</td>
									<!-- 开票留言 -->
									<td>
											${list.invoiceMessage}
									</td>
									<!-- 提前申请原因 -->
									<td>
											${list.advanceValidReason}
									</td>
									<!-- 不通过原因 -->
									<td>
											${list.applyNoPassRuleCode}
									</td>
									<!-- 账期支付 -->
									<td>
										<c:if test="${list.type == 505}">
											<c:choose>
												<c:when test="${list.haveAccountPeriod eq 1}">是</c:when>
												<c:otherwise>否</c:otherwise>
											</c:choose>
										</c:if>
										<c:if test="${list.type == 504}">
										</c:if>
									</td>
									<!-- 售后状态 -->
									<td>
										<c:choose>
											<c:when test="${list.serviceStatus eq 0}">无</c:when>
											<c:when test="${list.serviceStatus eq 1}">售后中</c:when>
											<c:when test="${list.serviceStatus eq 2}">售后完成</c:when>
											<c:otherwise>售后关闭</c:otherwise>
										</c:choose>
									</td>
									<!-- 收款状态 -->
									<td>
										<c:choose>
											<c:when test="${list.paymentStatus eq 0}">未收款</c:when>
											<c:when test="${list.paymentStatus eq 1}">部分收款</c:when>
											<c:otherwise>已收款</c:otherwise>
										</c:choose>
									</td>
									<!-- 发货状态 -->
									<td>
										<c:if test="${list.type == 505}">
										<c:choose>
											<c:when test="${list.deliveryStatus eq 0}">未发货</c:when>
											<c:when test="${list.deliveryStatus eq 1}">部分发货</c:when>
											<c:otherwise>全部发货</c:otherwise>
										</c:choose>
										</c:if>
										<c:if test="${list.type == 504}">
										</c:if>
									</td>
									<!-- 收货状态 -->
									<td>
										<c:if test="${list.type == 505}">
											<c:choose>
												<c:when test="${list.arrivalStatus eq 0}">未收货</c:when>
												<c:when test="${list.arrivalStatus eq 1}">部分收货</c:when>
												<c:otherwise>全部收货</c:otherwise>
											</c:choose>
										</c:if>
										<c:if test="${list.type == 504}">
										</c:if>
									</td>
									<!-- 合同状态 -->
									<td>
										<c:if test="${list.type == 505}">
										<c:choose>
											<c:when test="${list.contractVerifyStatus eq 4}">待审核</c:when>
											<c:when test="${list.contractVerifyStatus eq 0}">审核中</c:when>
											<c:when test="${list.contractVerifyStatus eq 1}">审核通过</c:when>
											<c:when test="${list.contractVerifyStatus eq 2}">审核不通过</c:when>
											<c:when test="${list.contractVerifyStatus eq 5}">未上传</c:when>
											<c:otherwise>--</c:otherwise>
										</c:choose>
										</c:if>
										<c:if test="${list.type == 504}">
										</c:if>
									</td>
									<!-- 确认单状态 -->
									<td>
										<c:if test="${list.type == 505}">
										<c:choose>
											<c:when test="${list.confirmationFormAudit eq 0}">待提交审核</c:when>
											<c:when test="${list.confirmationFormAudit eq 1}">审核中</c:when>
											<c:when test="${list.confirmationFormAudit eq 2}">审核通过</c:when>
											<c:when test="${list.confirmationFormAudit eq 3}">审核不通过</c:when>
											<c:otherwise>--</c:otherwise>
										</c:choose>
										</c:if>
										<c:if test="${list.type == 504}">
										</c:if>
									</td>
									<!-- 申请来源 -->
									<td>
										<c:choose>
											<c:when test="${list.type eq 505}">销售开票</c:when>
											<c:when test="${list.type eq 504}">售后开票</c:when>
											<c:otherwise>--</c:otherwise>
										</c:choose>
									</td>
									<!-- 申请方式-->
									<td>
										<c:if test="${list.applyMethod eq 0}">销售手动申请</c:if>
										<c:if test="${list.applyMethod eq 1}">系统自动推送</c:if>
										<c:if test="${list.applyMethod eq 2}">票货同行物流部申请</c:if>
										<c:if test="${list.applyMethod eq 3}">客户在线申请开票</c:if>
										<c:if test="${list.applyMethod eq 4}">售后手动申请</c:if>
									</td>
									<!-- 申请时间 -->
									<td><date:date value="${list.addTime}" /></td>
									<!-- 申请人 -->
									<td>
										<c:choose>
											<c:when test="${list.applyMethod eq 3}">
												${list.signerName}
											</c:when>
											<c:otherwise>
												<c:forEach varStatus="userNum" var="user" items="${userList}">
													<c:if test="${list.creator eq user.userId}">
														${user.username}
													</c:if>
												</c:forEach>
											</c:otherwise>
										</c:choose>
									</td>
									<!-- 审核状态 -->
									<td>
										<c:choose>
											<c:when test="${list.advanceValidStatus eq 1}"><span style="color: green">审核通过</span></c:when>
											<c:when test="${list.advanceValidStatus eq 2}"><span style="color: red">审核不通过</span></c:when>
											<c:otherwise>
												审核中
											</c:otherwise>
										</c:choose>
									</td>
									<!-- 销售部门 -->
									<td>
										<c:forEach var="org" items="${traderUserList}" varStatus="num">
											<c:if test="${list.traderId eq org.traderId}">${org.orgName}</c:if>
										</c:forEach>
									</td>
									<!-- 归属销售 -->
									<td>
										<c:forEach items="${traderUserList}" var="user" varStatus="status">
											<c:if test="${user.traderId eq list.traderId}">${user.username}</c:if>
										</c:forEach>
									</td>
									<td>${list.orderSourceTypeStr}</td>
									<!-- 操作 -->
									<td>
										<c:choose>
											<c:when test="${list.advanceValidStatus eq 0}">
												<span class=" edit-user"  onclick="saveOpenInvoiceAudit(${list.invoiceApplyId},1);">通过</span>

												<span class="pop-new-data delete" layerparams='{"width":"600px","height":"220px","title":"确认审核","link":"./auditOpenInvoice.do?invoiceApplyId=${list.invoiceApplyId}&isAdvance=1"}'>驳回</span>
											</c:when>
											<c:otherwise>
												--
											</c:otherwise>
										</c:choose>
<%--										<span class="edit-user pop-new-data" layerParams='{"width":"80%","height":"600px","title":"查看开票申请","link":"../../finance/invoice/selectInvoiceItems.do?relatedId=${list.relatedId}&invoiceApplyId=${list.invoiceApplyId}&editFlag=false"}' >查看开票申请</span>--%>
										<span class="edit-user pop-new-data" layerParams='{"width":"80%","height":"600px","title":"查看开票申请","link":"/invoice/invoiceApply/invoiceApplyDetail.do?invoiceApplyId=${list.invoiceApplyId}"}' >查看开票申请</span>
									</td>
								</tr>
							</c:forEach>
							<c:if test="${empty openInvoiceApplyList}">
								<tr>
									<td colspan="18">
										<!-- 查询无结果弹出 --> 查询无结果！请尝试使用其他搜索条件。
									</td>
								</tr>
							</c:if>
						</tbody>
					</table>
				</div>
			</div>
			<div>
				<c:if test="${!empty openInvoiceApplyList}">
					<div class="table-style4 f_left" style="margin:0px;">
						<div class="allchose">
<%--							<input type="checkbox" name="checkAllOpt" onclick="checkAllOpt(this);"><span>全选</span>--%>
						</div>
						<div class="print-record">
							<span class="bt-border-style border-blue" onclick="auditAdvanceInvoiceApply(1);">通过</span>
		                </div>
					</div>
				</c:if>
				<tags:page page="${page}" />
				<div class="clear"></div>
				<div class="fixtablelastline">
					【全部结果 条目：${page.totalRecord} 申请开票总金额：<fmt:formatNumber type="number" value="${invoiceApply.totalMoney}" pattern="0.00" maxFractionDigits="2" />】
					【本页统计 条目：${pageNum} 申请开票总金额：<fmt:formatNumber type="number" value="${pageAmount}" pattern="0.00" maxFractionDigits="2" />】
				</div>
				申请检查项目:1全部收款，2专票对公收款，3无进行中货款票售后，4不存在已完成的仅退票售后，5申请数量合规，6 标准开票信息，7合同状态，8确认单状态
			</div>
		</div>
	</div>
<script type="text/javascript">

	initTicketReasonEqualList();
	function initTicketReasonEqualList() {
		var initValueStr = $('#ticketReasonEqualList').val();
		var initValue = JSON.parse(initValueStr);
		$.ajax({
			url: '/invoiceApply/api/getCheckRuleList.do?ruleType=1',
			method: 'GET',
			contentType: 'application/x-www-form-urlencoded',
			success: function (response) {
				var responseData = JSON.parse(response); // 将 JSON 字符串解析为 JavaScript 对象或数组
				var processedData = responseData.data.map(function (item) {
					return {
						name: item.ruleCode,
						value: item.ruleCode,
					};
				});

				window.ticketReasonEqual = xmSelect.render({
					el: '#ticketReasonEqual',
					name: 'ticketReasonEqual',
					data: processedData,
					initValue: initValue
				});
			},
		});
	}

</script>

<%@ include file="../../common/footer.jsp"%>
<script>

    function doAdvanceAudit() {

        $.ajax({
            type: "POST",
            url: page_url + "/finance/invoiceApplyAutoAudit/advance.do",
            dataType: 'json',
            success: function (data) {

                if (data.code == 0) {
                    var index = layer.open({
                        type: 1,
                        // skin: 'my-style',
                        title: false,
                        closeBtn: false,
                        area: '300px;',
                        shade: 0.8,
                        id: 'LAY_layuipro',
                        resize: false,
                        btnAlign: 'c',
                        moveType: 1,
                        content: '<div class="loading-icon" style="margin: 20px auto;"></div><span id="percentText" style="display: block; text-align: center;">0%</span><br><span style="display: block; text-align: center;">正在处理中，请不要关闭当前页面</span><br><br>'
                    });
                    // 模拟加载过程
                    var percent = 0;
                    var interval = setInterval(function() {
                        $.ajax({
                            type: "POST",
                            url: page_url + "/finance/invoiceApplyAutoAudit/advanceLoading.do",
                            dataType: 'json',
                            success: async function (data) {

                                if (data.code == 0) {
                                    percent = data.data;
                                    // 更新百分比数字
                                    $('#percentText').text(percent + '%');
                                    if (percent >= 100) {
                                        await sleep(2000);
                                        clearInterval(interval);
                                        search();
                                        // 隐藏弹出层
                                        layer.close(index);
                                    }

                                } else {
                                    layer.alert(data.message);
                                }

                            },
                        });
                    }, 2000);

                } else {
                    layer.alert(data.message);
                }

            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("当前操作无权限")
                }
            }
        });

    }

	async function sleep(ms) {
		return new Promise(resolve => setTimeout(resolve, ms));
	}

</script>
