package com.vedeng.erp.saleorder.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.onedataapi.api.skuinfo.SkuQueryApi;
import com.vedeng.onedataapi.api.skuinfo.req.SkuReqDto;
import com.vedeng.onedataapi.api.skuinfo.resp.SkuAvgPriceResDto;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */
@FeignApi(serverName = "onedataapi")
public interface OneDataSkuPriceFeignApi extends SkuQueryApi {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /sku/querySkuAvgPrice")
    @Override
    RestfulResult<List<SkuAvgPriceResDto>> getCategorySkuRankList(@RequestBody SkuReqDto var1);
}


