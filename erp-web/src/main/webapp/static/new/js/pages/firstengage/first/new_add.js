function getCategory(s) {
    var a =s.indexOf("备");
    var b =s.indexOf("国");
    var c =s.indexOf("准");
    var d =s.indexOf("进");
    var f =s.indexOf("许");
    if(a>-1){
        if(b>-1){
            return 3;
        }
        return 1;
    }
    if(d>-1||f>-1){
        return 2;
    }
    if(c>-1){
        return 0;
    }
    return -1;
}
function showCreateFactory(obj,t){ //t为对应的div id,也是生产企业内容的classname
    if(obj.checked){
        $("[id='"+t+"Div']").show();//将
        //$("."+t+"Class").show();
    }else {
        $("[id='"+t+"Div']").hide();//将
        if($("."+t+"Class")){
            $("."+t+"Class").not('#'+t).remove();
        }
    }
}

$(function () {
    //查看大图
    GLOBAL.showLargePic('.J-show-big');
    $(".printAtta").each(function(){
        $(this).click(function(){
            $(this).hide();
            $(this).prev().printArea();
            $(this).show()
        })
    })
    // $("#province").select2();
    $("#manufacturerName").select2();


    // $("input[name='registration.registrationNumber']").bind("input propertychange",function () {
    //     var s=$("input[name='registration.registrationNumber']").val();
    //     if(!s||s.length<1){
    //         return
    //     }
    //     $("input[name='registration.category'][value="+getCategory(s)+"]").prop("checked","checked")
    //     var type = getCategory(s);
    //     if (type === 1 || type === 3){
    //         $("input[name='registration.manageCategoryLevel'][value="+968+"]").prop("checked","checked")
    //         $("input[name='registration.manageCategoryLevel'][value="+969+"]").prop("disabled","true")
    //         $("input[name='registration.manageCategoryLevel'][value="+970+"]").prop("disabled","true")
    //         changeItemShow()
    //     }
    // });

    function changeItemShow(){
        //先进行一次移除操作，再进行判断
        $("#exd").removeClass("disappear")
        $("#exd").removeClass("ignore")
        // $(".J-inter-type").removeClass("disappear")
        // $(".J-inter-type").removeClass("ignore")

        var level=$('input[name="registration.manageCategoryLevel"]:checked').val();
        if(level=='968'){
            $("#exd").addClass("disappear");
            $("#exd").addClass("ignore");

            $("#exd2").removeClass("disappear");
            $("#exd2").removeClass("ignore");

            $(".cl").addClass("disappear")
            $(".cl").addClass("ignore")
            // $(".J-inter-type").addClass("disappear")
            // $(".J-inter-type").addClass("ignore")
        }else if(level=="969"||level=="970"){
            $("#exd").removeClass("disappear");
            $("#exd").removeClass("ignore");

            $("#exd2").addClass("disappear");
            $("#exd2").addClass("ignore");

            $(".cl").removeClass("disappear")
            $(".cl").removeClass("ignore")
            // $(".J-inter-type").removeClass("disappear")
            // $(".J-inter-type").removeClass("ignore")
        }else{
            $("#exd").removeClass("disappear")
            $("#exd").removeClass("ignore")

            $("#exd2").addClass("disappear");
            $("#exd2").addClass("ignore");

            // $(".J-inter-type").removeClass("disappear")
            // $(".J-inter-type").removeClass("ignore")
        }



        // var isSubProduct=$('input[name="registration.isSubcontractProduction"]:checked').val();
        // if(isSubProduct=='0'){
        //     $(".c-title").each(function () {
        //         var title=$(this).data("a");
        //         $(this).html(title)
        //     })
        // }else if(isSubProduct=='1'){
        //    $(".c-title").each(function () {
        //        var title=$(this).data("b");
        //        $(this).html(title)
        //    })
        // }

        var s=$("input[name='registration.registrationNumber']").val();
        $("input[name='registration.category'][value="+getCategory(s)+"]").prop("checked","checked")
        var type = getCategory(s);
        if (type === 1 || type === 3){
            $("input[name='registration.manageCategoryLevel'][value="+968+"]").prop("checked","checked")
            $("input[name='registration.manageCategoryLevel'][value="+969+"]").prop("disabled","true")
            $("input[name='registration.manageCategoryLevel'][value="+970+"]").prop("disabled","true")
        }
    }

    changeItemShow();
    //校验
    $.validator.addMethod("requiredCheck", function (value, element, params) {
        var hasVal = false;
        $(params).each(function () {
            if ($(this)[0].checked) {
                hasVal = true;
            }
        })
        return hasVal;
    });

    $.validator.addMethod("larger", function (value, element, params) {
        return parseFloat(value) > params;
    });

    $.validator.addMethod("registrationNumber", function (value, element, params) {
        return validRegistrationNumber;
    });

    $.validator.addMethod("rnSpace", function (value, element, params) {
        var index=value.indexOf(" ");
        return index>0?false:true;
    });

    $.validator.addMethod("isSubcontractProductionCheck", function (value, element, params) {
        if($("#isSubcontractProduction").is(':checked')){//如果自行生产选中了才校验
            return $(".detail-table.manufacturerClass").length>0?true:false;
        }
        return true;
    });

    $.validator.addMethod("notSubcontractProduction", function (value, element, params) {
        if($("#notSubcontractProduction").is(':checked')){
            return $(".detail-table.manufacturerWeituoClass").length>0?true:false;
        }
        return true;

    });

    var getGroupName = function (el) {
        var name = [];
        $(el).each(function () {
            name.push($(this).attr('name'));
        })

        return name.join(',');
    }

    // 生产企业生产许可证号或备案凭证编号
    jQuery.validator.addMethod("productCompanyLicence", function(value, element) {
        $(element).parent()
        return this.optional(element) || /^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(value);
    }, "只可包含中文、英文、数字");
    // 生产企业生产许可证号或备案凭证编号
    jQuery.validator.addMethod("temperature", function(value, element) {
        return this.optional(element) || /^\-?[0-9]+(.[0-9]+)?~-?[0-9]+(.[0-9]+)?$/.test(value);
    }, "请正确填写温度范围；列：-3.5~10.0");

    //校验框架
    var canSubmit = true;
    $('.J-form').validate({
        errorWrap: true,
        ignore: '.ignore input',
        rules: {
            'registration.registrationNumber': {
                required: true,
                maxlength: 64,
                registrationNumber: true,
                rnSpace:true
            },
            'registration.manageCategoryLevel': {
                required: true
            },
            'registration.productCompany.productCompanyChineseName': {
                required: true,
                maxlength: 64
            },
            'registration.productChineseName': {
                required: true,
            },
            // 'registration.productCompany.productCompanyLicence': {
            //     required: true,
            //     maxlength: 25,
            //     productCompanyLicence:true
            // },
            'registration.category': {
                required: true
            },
            'registration.issuingDateStr': {
                required: true
            },
            'registration.effectiveDateStr': {
                required: true
            },
            'registration.model': {
                required: true
            },
            'registration.isSubcontractProduction1': {
                required: true
            },
            'registration.manufacturerId': {
                // required: true,
                isSubcontractProductionCheck:true
            },
            'registration.weituomanufacturerId': {
                // required: true,
                notSubcontractProduction:true
            },

            standardCategoryType: {
                required: true
            },
            newStandardCategoryId: {
                required: true
            },
            oldStandardCategoryId: {
                required: true
            },
            upload0: {
                required: true
            }
        },
        messages: {
            'registration.registrationNumber': {
                required: '请填写注册证号/备案凭证号',
                registrationNumber: function () {
                    var id=$("#preId").val();
                    var href_str='<a href="javascript:void()" style="color: #2E8AE6" onclick="newPage('+id+')">'+$("input[name='registration.registrationNumber']").val()+'</a>'
                    return '该注册证号已经存在，前往查看' + href_str;
                },
                rnSpace: "注册证号/备案凭证号不能有空格"
            },
            'registration.manageCategoryLevel': {
                required: '请选择管理类别'
            },
            'registration.productCompany.productCompanyChineseName': {
                required: '请填写生产企业'
            },
            'registration.productChineseName': {
                required: '请输入产品名称（注册证/备案凭证）'
            },
            // 'registration.productCompany.productCompanyLicence': {
            //     required: '请填写生产企业生产许可证号或备案凭证编号'
            // },
            'registration.category': {
                required: '请选择注册证/备案凭证类别'
            },
            'registration.issuingDateStr': {
                required: '请选择批准日期'
            },
            'registration.manufacturerId': {
                // required: '请选择生产企业名称！',
                isSubcontractProductionCheck:'请选择生产企业名称！'
            },
            'registration.weituomanufacturerId': {
                // required: '请选择受委托生产企业名称！',
                notSubcontractProduction:'请选择受委托生产企业名称！'
            },
            'registration.effectiveDateStr': {
                required: '请选择有效期'
            },
            'registration.model': {
                required: '请输入规格、型号（注册证/备案凭证）'
            },
            'registration.isSubcontractProduction1': {
                required: "请选择生产模式"
            },
            standardCategoryType: {
                required: '请选择国标类型'
            },
            newStandardCategoryId: {
                required: '请选择新国际分类'
            },
            oldStandardCategoryId: {
                required: '请选择旧国际分类'
            },
            upload0: {
                required: '请上传注册证附件/备案凭证附件'
            }
        },
        submitHandler: function (form) {
            if (canSubmit) {
                canSubmit = false;
                let userIndex = -1; // 初始化用户索引 -此为为-1开始,数组以0为开始
                var manufacturer = ""
                $(".detail-table.manufacturerClass").each(function (i,obj) {
                    userIndex++;
                    var registrationNumberId = $('input[name="registration.registrationNumberId"]').val();
                    var productionMode = 0;
                    var enterpriseName = $(obj).attr('enterpriseName');
                    var manufacturerId = $(obj).attr('manufacturerId');
                    manufacturer = manufacturer+
                        `    <input type="hidden" name="manufacturerList[${userIndex}].registrationNumberId" value="${registrationNumberId}">
                            <input type="hidden" name="manufacturerList[${userIndex}].productionMode" value="${productionMode}">
                            <input type="hidden" name="manufacturerList[${userIndex}].enterpriseName" value="${enterpriseName}">
                            <input type="hidden" name="manufacturerList[${userIndex}].manufacturerId" value="${manufacturerId}">
                            
                        `;
                })
                $(".detail-table.manufacturerWeituoClass").each(function (i,obj) {
                    userIndex++;
                    var registrationNumberId = $('input[name="registration.registrationNumberId"]').val();
                    var productionMode = 1;
                    var enterpriseName = $(obj).attr('enterpriseName');
                    var manufacturerId = $(obj).attr('manufacturerId');
                    manufacturer = manufacturer+
                        `    <input type="hidden" name="manufacturerList[${userIndex}].registrationNumberId" value="${registrationNumberId}">
                            <input type="hidden" name="manufacturerList[${userIndex}].productionMode" value="${productionMode}">
                            <input type="hidden" name="manufacturerList[${userIndex}].enterpriseName" value="${enterpriseName}">
                            <input type="hidden" name="manufacturerList[${userIndex}].manufacturerId" value="${manufacturerId}">
                            
                        `;
                })
                $(".J-form").append(manufacturer);
                window.localStorage.setItem('addsuccess', true);
                form.submit();
            }
        }
    })

    // $('.J-condition').rules("add", {
    //     requiredCheck: '.J-condition',
    //     messages: {
    //         requiredCheck: '请选择存储条件'
    //     }
    // })

    // $('.J-condition').change(function () {
    //     $(this).valid();
    // })

    $("input[name='registration.category']").click(function () {
        changeItemShow();
    })

    $("input[name='registration.manageCategoryLevel']").click(function () {
        changeItemShow();
    })

    // $("input[name='registration.isSubcontractProduction']").click(function () {
    //     changeItemShow();
    // })


    $('[valid-max]').each(function () {
        $(this).rules('add', {
            maxlength: $(this).attr('valid-max')
        })
    })

    //展开收起
    $('.J-toggle-show').click(function () {
        var $optionalWrap = $(this).siblings('.J-optional');
        var isShow = $optionalWrap.hasClass('show');
        var $less = $(this).find('.J-less');
        var $more = $(this).find('.J-more');

        if (isShow) {
            $optionalWrap.removeClass('show').slideUp(200);
            $less.hide();
            $more.show();
        } else {
            $optionalWrap.addClass('show').slideDown(200);
            $less.show();
            $more.hide();
        }
    })

    //导入
    var parseLoadData = function (data) {
        var parseData = {};
        for (var item in data) {
            parseData['registration.' + item] = data[item];
            if (item === 'productCompany') {
                for (var item1 in data[item]) {
                    parseData['registration.productCompany.' + item1] = data.productCompany[item1];
                }
            }
        }

        return parseData;
    };

    //日期空间初始化
    var Pikadays = [];
    $('.J-date').each(function (i) {
        var $this = $(this);
        Pikadays[i] = new Pikaday({
            format: 'yyyy-mm-dd',
            field: $(this)[0],
            firstDay: 1,
            yearRange: [1900, 2099],
            onSelect: function (date) {
                var date1 = new Date(date).valueOf();
                if (i == 0) {
                    if (!$('.J-date-2').val()) {
                        Pikadays[1].setDate(new Date(date1 + 5 * 365 * 24 * 60 * 60 * 1000));
                    }

                    Pikadays[1].setMinDate(date1);
                }

                if (i == 1) {
                    Pikadays[0].setMaxDate(date1);
                }

                if(i>2){
                    var j=i%2;
                    if(j==1){
                        Pikadays[i+1].setMinDate(date1);
                    }else if(j==0){
                        Pikadays[i-1].setMaxDate(date1)
                    }
                }
            }
        });
    })

    var checkDateRange = function () {
        var val1 = $('.J-date').eq(0).val();

        if (val1) {
            Pikadays[1].setMinDate(new Date($.trim(val1)));
        }
    };

    checkDateRange();



    var register = null, prevRegister = null;
    var validRegistrationNumber = true;
    var prevNum = $.trim($('.J-suggest-input').val());
    var loadInfo = function () {
        register = $.trim($('.J-suggest-input').val());
        if (register && ((prevRegister && register !== prevRegister) || !prevRegister)) {
            prevRegister = register;
            $.ajax({
                url: page_url + '/firstengage/baseinfo/getFirstSearchInfoById.do',
                data: {
                    registrationNumber: register
                },
                type: 'post',
                dataType: 'json',
                success: function (data) {
                    if (data && data.code === 0) {
                        var resData = parseLoadData(data.data)
                        if (resData['registration.firstEngageId'] && prevNum !== register) {
                            validRegistrationNumber = false;
                            $("#preId").val(resData['registration.firstEngageId'])
                        } else {
                            validRegistrationNumber = true;
                            $('.J-block-1').find('input').each(function () {
                                var name = $(this).attr('name');
                                if (name !== 'registration.registrationNumber' && !$.trim($(this).val())) {
                                    $(this).val(resData[name]);
                                }

                                if (name === 'registration.manageCategoryLevel' && !$('[name="registration.manageCategoryLevel"]:checked').length) {
                                    $('[name=\'' + name + '\']').each(function () {
                                        $(this)[0].checked = false;
                                        if ($(this).val() == resData[name]) {
                                            $(this)[0].checked = true;
                                        }
                                    })
                                }
                            })
                            // changeDate();

                            if (data.data) {
                                var $wrap = $('.J-suggest-input').parents('.form-block:first');
                                if (!$('.J-optional', $wrap).hasClass('show')) {
                                    $('.J-toggle-show', $wrap).trigger('click');
                                }
                            }

                            Pikadays[0].setDate(new Date($('.J-date').eq(0).val()));
                        }

                        checkDateRange();
                    }
                    $('.J-suggest-input').valid();
                }
            })
        }
    };

    //建议词搜索初始化
    new Suggest({
        el: '.J-suggest-input',
        url: page_url + '/firstengage/baseinfo/getRegistrationInfo.do',
        params: 'registrationStr',
        parseData: function (data) {
            var list = data.listData || [];
            var reslist = [];
            $.each(list, function (i, item) {
                reslist.push({
                    word: item.registrationNumber,
                    disabled: !!item.firstEngageId
                })
            })
            return reslist;
        }
    });

    $('.J-suggest-input').blur(function () {
        var s=$("input[name='registration.registrationNumber']").val();
        if(!s||s.length<1){
            return
        }
        $("input[name='registration.category'][value="+getCategory(s)+"]").prop("checked","checked")
        var type = getCategory(s);
        if (type === 1 || type === 3){
            $("input[name='registration.manageCategoryLevel'][value="+968+"]").prop("checked","checked")
            $("input[name='registration.manageCategoryLevel'][value="+969+"]").prop("disabled","true")
            $("input[name='registration.manageCategoryLevel'][value="+970+"]").prop("disabled","true")
            changeItemShow()
        }
        setTimeout(function () {
            loadInfo();
        }, 100)
    })

    //商品有效期显示隐藏
    $('.J-prod-type input').change(function () {
        var val = $('.J-prod-type input:checked').val();

        if (val == 316 || !val) {
            $('.J-prod-date').addClass('ignore').hide();
        } else {
            $('.J-prod-date').removeClass('ignore').show();
        }
    })

    $('.J-prod-type input').trigger('change');


    $('[name=conditionOne]').trigger('change');

    //国际分类选择显示隐藏
    $('.J-inter-type input').change(function () {
        var val = $('.J-inter-type input:checked').val();
        if (val == 2) {
            $('.J-inter-old').removeClass('ignore').show();
            $('.J-inter-new').addClass('ignore').hide();
        } else if (val == 1) {
            $('.J-inter-new').removeClass('ignore').show();
            $('.J-inter-old').addClass('ignore').hide();
        }
    })

    $('.J-inter-type input').trigger('change');

    //旧国标国搜索下拉框
    new SuggestSelect({
        placeholder: '请选择',
        wrap: '.J-old-wrap',
        data: JSON.parse($('.J-old-data').html()),
        input: '.J-old-value'
    })

    new SuggestSelect({
        placeholder: '请选择',
        wrap: '.J-new-wrap',
        data: JSON.parse($('.J-new-data').html()),
        input: '.J-new-value'
    })

    //上传组件初始化
    timeout = null;


    var zczUploader = new Upload({
        limit: 50,
        url: GLOBAL.IMGUPLOADURL+"?scene=registration_certificate&side=pc",
        wrapper: $('.J-upload'),
        uploadName: 'upload0',
        list: JSON.parse($('.J-upload').siblings('.J-upload-data').val() || '[]'),
        onchange: function () {
            $('.J-upload').find('.J-upload-item').each(function (ii) {
                var data = $(this).data('item');
                $(this).find('.J-item-name').remove();
                $(this).append('<input type="hidden" class="J-item-name" name="registration.zczAttachments[' + ii + '].attachmentFunction" value="975">');
                $(this).append('<input type="hidden" class="J-item-name" name="registration.zczAttachments[' + ii + '].uri" value="' + data.filePath +'">');
                $(this).append('<input type="hidden" class="J-item-name" name="registration.zczAttachments[' + ii + '].domain" value="' + data.domain + '">');
            })
            $('.J-upload').find('[name^=upload]').valid();
        },
        filters: {
            mime_types: [
                { title: "Image files", extensions: "jpg,jpeg,png" }
            ],
            max_file_size: '800KB'
        },
        onError: function (error) {
            var errorMsg = {
                TYPE: '上传图片格式为：JPG、JPEG、PNG格式',
                SIZE: '图片大小不超过800KB'
            }
            if (error) {
                var $error = $('.J-upload').siblings('.J-upload-error');
                $error.show().find('label').html(errorMsg[error]).show();
                timeout && clearTimeout(timeout);
                timeout = setTimeout(function () {
                    $error.hide();
                }, 3000)
            }
        },
        handlerError(res){
            var $error = $('.J-upload').siblings('.J-upload-error');
            $error.show().find('label').html(res.message).show();
            timeout && clearTimeout(timeout);
            timeout = setTimeout(function () {
                $error.hide();
            }, 3000)
        }
    })



    // 标签样稿上传组件初始化
    var labelUploader = new Upload({
        limit: 50,
        url: GLOBAL.IMGUPLOADURL + "?scene=label_sample&side=pc",
        wrapper: $('.J-upload-label'),
        uploadName: 'uploadLabel',
        list: JSON.parse($('.J-upload-label').siblings('.J-upload-data').val() || '[]'),
        onchange: function () {
            $('.J-upload-label').find('.J-upload-item').each(function (ii) {
                var data = $(this).data('item');
                $(this).find('.J-item-name').remove();
                $(this).append('<input type="hidden" class="J-item-name" name="registration.labelAttachments[' + ii + '].attachmentFunction" value="5608">');
                $(this).append('<input type="hidden" class="J-item-name" name="registration.labelAttachments[' + ii + '].uri" value="' + data.filePath + '">');
                $(this).append('<input type="hidden" class="J-item-name" name="registration.labelAttachments[' + ii + '].domain" value="' + data.domain + '">');
            })
            $('.J-upload-label').find('[name^=upload]').valid();
        },
        filters: {
            mime_types: [
                { title: "Image files", extensions: "jpg,jpeg,png" }
            ],
        },
        onError: function (error) {
            var errorMsg = {
                TYPE: '上传图片格式为：JPG、JPEG、PNG格式'
            }
            if (error) {
                var $error = $('.J-upload-label').siblings('.J-upload-error');
                $error.show().find('label').html(errorMsg[error]).show();
                timeout && clearTimeout(timeout);
                timeout = setTimeout(function () {
                    $error.hide();
                }, 3000)
            }
        },
        handlerError(res) {
            var $error = $('.J-upload-label').siblings('.J-upload-error');
            $error.show().find('label').html(res.message).show();
            timeout && clearTimeout(timeout);
            timeout = setTimeout(function () {
                $error.hide();
            }, 3000)
        }
    });



    var showFileTip = function(type, txt){
        if(type==='error'){
            $(".J-upload-file-tip").find(".J-error").show().find(".J-error-txt").html(txt);
            $(".J-upload-file-tip").find(".J-loading").hide();
        }else{
            $(".J-upload-file-tip").find(".J-loading").show();
            $(".J-upload-file-tip").find(".J-error").hide();
        }

        $('.J-upload-file-tip').css('display', 'flex');
    };

    var hideFileTip = function(){
        $('.J-upload-file-tip').hide();
    }

    //回显源文件
    var sourceList = JSON.parse($('.J-source-list').val() || "[]");

    if(sourceList && sourceList.length){
        $.each(sourceList, function (i, item){
            $('.J-file-source-wrap').append("<div class='file-item J-file-item' data-item='" + JSON.stringify(item) + "'>" +
                "<div class='item-name J-file-name' data-name='" + item.name +"'></div>" +
                "<div class='options'>" +
                "<a target='_blank' href='" + item.httpUrl + item.filePath + "' class='option-item'>查看</a>" +
                "<a class='option-item red J-file-del'>删除</a>" +
                "</div></div>");
        })
    }

    var initFileName = function(){
        $('.J-file-source-wrap .J-file-name').each(function(i, item){
            var name = $(this).data('name');
            var type = name.split('.');
            type = type[type.length - 1];
            var reg = new RegExp('.'+type+'$');
            name = name.replace(reg, "");

            $(this).html('<span class="name-txt">'+ name +'</span><span class="name-type">.'+ type +'</span>')
        })

        $('.J-file-source-wrap .J-file-item').each(function (i){
            var data = $(this).data('item');

            $(this).find('input').remove();
            $(this).append('<input type="hidden" name="registration.zczyAttachments['+ i +'].domain" value="'+ data.domain +'">')
            $(this).append('<input type="hidden" name="registration.zczyAttachments['+ i +'].uri" value="'+ data.filePath +'">')
            $(this).append('<input type="hidden" name="registration.zczyAttachments['+ i +'].name" value="'+ data.fileName +'">')
            $(this).append('<input type="hidden" name="registration.zczyAttachments['+ i +'].attachmentFunction" value="1010">')
        })
    }

    initFileName();

    var fileUploader = new Upload({
        limit: 50,
        url: "/firstengage/baseinfo/fileUploadPDFtoImgList.do",
        wrapper: $('.J-upload-file'),
        uploadName: 'uploadFile',
        type: 'file',
        multi_selection: false,
        filters: {
            mime_types: [
                { title: "Image files", extensions: "jpg,jpeg,png,pdf" }
            ],
            max_file_size: '10MB'
        },
        parseResData: function (resData) {
            hideFileTip();

            if(resData.code === 0){
                //新增源文件
                $('.J-file-source-wrap').append("<div class='file-item J-file-item' data-item='" + JSON.stringify(resData.data) + "'>" +
                    "<div class='item-name J-file-name' data-name='" + resData.data.fileName +"'></div>" +
                    "<div class='options'>" +
                    "<a target='_blank' href='" + resData.data.httpUrl + resData.data.filePath + "' class='option-item'>查看</a>" +
                    "<a class='option-item red J-file-del'>删除</a>" +
                    "</div></div>");

                //新增图片
                zczUploader.addItems(resData.listData);

                var needWarn = false;

                $.each(resData.listData, function (i, item) {
                    if (item.fileWidth) {
                        needWarn = true;
                    }
                })

                if(needWarn){
                    $('.J-upload-warn').show();
                }

                initFileName();
            }else{
                showFileTip('error', resData.message || '系统处理异常');
            }
        },
        handlerProgress(){
            showFileTip();
        },
        onError: function (error) {
            var errorMsg = {
                TYPE: '仅支持上传pdf、jpg、jpeg、png格式文件',
                SIZE: '文件大小不超过10M'
            }
            if (error) {
                var $error = $('.J-upload-file-error');
                $error.show().find('label').html(errorMsg[error]).show();
                timeout && clearTimeout(timeout);
                timeout = setTimeout(function () {
                    $error.hide();
                }, 3000)
            }
        }
    })

    $('.J-upload-file-btn').click(function(){
        $("#J-upload-2").click();
    })

    $('.J-upload-error-close').click(function(){
        hideFileTip();
    })

    //删除源文件
    $('.J-file-source-wrap').on('click', '.J-file-del', function(){
        $(this).parents('.J-file-item:first').remove();
        initFileName();
    })

    window.localStorage.removeItem('addsuccess');

    //增加关闭提示
    GLOBAL.addtip();
})

function syncTyInfo() {
    var no=$("input[name='registration.registrationNumber']").val();
    if(!no){
        $("#tip").removeClass("disappear");
        $("#tip").html("请先输入注册证号/备案凭证号");
    }
    var typeStr=["国产注册证","国产备案","进口注册证","进口备案"]
    $.ajax({
        url: page_url + '/firstengage/baseinfo/getTyRegisterNumber.do?registerNumberNo='+no,
        type: 'get',
        dataType : "json",
        success: function (data) {
            if(data){
                if(data.code==0){
                    $("#tip").addClass("disappear");
                    var p=data.data;
                    $("#re").addClass("col-6");
                    $(".sa").addClass("col-10")
                    $(".sa").removeClass("col-6")
                    $("#ty").removeClass("disappear");
                    $("#t_no").html(p.productNum);
                    $("#t_name").html(p.peopleName);
                    $("input[name='registration.productCompany.productCompanyChineseName']").val(p.peopleName)
                    $("#t_house").html(p.productAddress);
                    $("input[name='registration.productCompany.productCompanyAddress']").val(p.productAddress)
                    $("#t_proaddress").html(p.factoryAddress);
                    $("input[name='registration.productionAddress']").val(p.factoryAddress)
                    $("#t_proxyName").html(p.agentName);
                    $("input[name='registration.registeredAgent']").val(p.agentName)
                    $("#t_proxyHouse").html(p.agentAddress);
                    $("input[name='registration.registeredAgentAddress']").val(p.agentAddress)
                    $("#t_proName").html(p.productName);
                    $("input[name='registration.productChineseName']").val(p.productName);
                    $("#t_ml").html(p.managerCategory);
                    $("#t_model").html(p.productModel);
                    $("textarea[name='registration.model']").val(p.productModel);
                    $("#t_struct").html(p.productStructure);
                    $("textarea[name='registration.proPerfStruAndComp']").val(p.productStructure);
                    $("#t_use").html(p.productUseRange);
                    $("textarea[name='registration.productUseRange']").val(p.productUseRange);
                    $("#t_attachment").html(p.attachFile);
                    $("input[name='registration.attachment']").val(p.attachFile);
                    $("#t_storage").html(p.storageCondition);
                    $("input[name='registration.storageAndExpiryDate']").val(p.storageCondition);
                    $("#t_oc").html(p.otherContent);
                    $("textarea[name='registration.otherContents']").val(p.otherContent);
                    $("#t_remark").html(p.remark);
                    $("textarea[name='registration.remarks']").val(p.remark);
                    $("#t_pd").html(p.approvalDepartment?p.approvalDepartment:p.recordDepartment);
                    $("input[name='registration.approvalDepartment']").val(p.approvalDepartment?p.approvalDepartment:p.recordDepartment);

                    $("#t_change").html(p.changes);
                    $("textarea" +
                        "[name='registration.changeContents']").val(p.changes);
                    var c =getCategory(p.productNum)
                    if(c>-1&&c<4){
                        $("#t_type").val(typeStr[c]);
                    }
                    var l=getManageLevel(p.managerCategory);
                    $("input[name='registration.manageCategoryLevel'][value="+l+"]").prop("checked","checked")
                    var rd=p.approvalDate?p.approvalDate:p.recordDate;
                    $("#t_pt").html(rd);
                    $("#t_efd").html(p.validDate);
                    if(rd){$("input[name='registration.issuingDateStr']").val(rd);}
                    if(p.validDate){ $("input[name='registration.effectiveDateStr']").val(p.validDate);}

                }else{
                    $("#tip").removeClass("disappear");
                    $("#tip").html(data.message);
                    $("#ty").addClass("disappear");
                    $("#re").removeClass("col-6");
                    $(".sa").removeClass("col-10")
                    $(".sa").addClass("col-6")

                }
            }
        }
    })

}

function cancel() {
    var _self=self;
    var dialog = artDialog.confirm('取消后，所填写的信息，系统将不做保存', '', {
        fn: function () {
            // $(window.parent.document).find('[role=presentation].active .glyphicon.small').click();
            window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
        }, text: '确定'
    }, {
        fn: function () {
            dialog.close();
        }, text: '取消'
    });
}

function closeTy() {
    $("#ty").addClass("disappear");
    $("#re").removeClass("col-6");
    $(".sa").removeClass("col-10")
    $(".sa").addClass("col-6")
}

function getManageLevel(s) {
    if(s=="第一类"){
        return 968
    }else if(s=="第二类"){
        return 969
    }else if(s=="第三类"){
        return 970
    }
    return 0;
}

var newPage = function(id){
    var url="./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId="+id;
    var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
    var item = { 'id': 'viewProduct' + new Date().valueOf(), 'name': "查看商品", 'url': url, 'closable': true };
    if (typeof(self.parent.closableTab) != 'undefined') {
        self.parent.closableTab.addTab(item);
        self.parent.closableTab.resizeMove();
        $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }else{
        try{
            var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
            window.parent.postMessage({
                from:'ez',
                name: title,
                url:url,
                id:"tab-"+uniqueName
            }, '*');
        }catch (e){}
    }
    // self.parent.closableTab.addTab(item);
    // self.parent.closableTab.resizeMove();
    // $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
};

// 点击名称事件
function onclickManufacturerName() {
    var manufacturerId = $("#manufacturerName").val();
    $.ajax({
        url: page_url + '/goods/manufacturer/getManufacturerDetail.do?manufacturerId=' + manufacturerId +'&showCheck=0',
        type: 'GET',
        dataType: 'html',
        success: function (html) {
            if (html != null) {
                document.getElementById("manufacturer").innerHTML = html;
            }
            // 删除重复元素
            $('#manufacturer_inner').remove()
            // $('#manufacturer_inner_name').remove()
        }, error: function (data) {

        }
    })
}