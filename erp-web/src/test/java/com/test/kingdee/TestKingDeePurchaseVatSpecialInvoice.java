package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatSpecialInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatSpecialInvoiceConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 金蝶  采购费用专用发票
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeePurchaseVatSpecialInvoice {

    @Autowired
    private KingDeePurchaseVatSpecialInvoiceCommandConvertor vatSpecialInvoiceCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePurchaseVatSpecialInvoiceMapper vatPlainInvoiceMapper;

    @Autowired
    private KingDeePurchaseVatSpecialInvoiceConvertor vatSpecialInvoiceConvertor;

    @Test
    public void KingDeePurchaseVatSpecialInvoice() {

        PurchaseVatSpecialInvoiceDto dto = new PurchaseVatSpecialInvoiceDto();

        dto.setFid("0");
        dto.setFdate("2022-11-10 00:00:00");
        dto.setFQzokBddjtid("BD43444");
        dto.setFinvoiceno("FP123");
        dto.setFsupplierid("VEN0006");
        dto.setFdocumentstatus("Z");
        dto.setFBillTypeID("CGZZSZYFP01_SYS");
        dto.setFsettleorgid("101");
        dto.setFCancelStatus("A");
        dto.setFRedBlue("0");
        dto.setFQzokFpdm("FPDM21");

        List<PurchaseVatSpecialInvoiceDetailDto> FPURCHASEICENTRY = new ArrayList<>();
        PurchaseVatSpecialInvoiceDetailDto sonDto = new PurchaseVatSpecialInvoiceDetailDto();
        sonDto.setFmaterialid("sku0001");
        sonDto.setFpriceqty("80.0");
        sonDto.setFauxtaxprice(new BigDecimal(113));
        sonDto.setFtaxrate("13.00");
        sonDto.setFamountfor(new BigDecimal(8000));
        sonDto.setFdetailtaxamountfor(new BigDecimal(1040));
        sonDto.setFallamountfor(new BigDecimal(9040));
        sonDto.setFQzokBddjhid("333");
        sonDto.setFQzokYsddh("BD1");
        sonDto.setFQzokGsywdh("BD9");
        sonDto.setFQzokYwlx("ADD_Q");
        sonDto.setFsourcetype("AP_Payable");

        List<PurchaseVatSpecialInvoiceDetailLinkDto> fpurchaseicentryLink = new ArrayList<>();
        PurchaseVatSpecialInvoiceDetailLinkDto inSon1 = new PurchaseVatSpecialInvoiceDetailLinkDto();
        inSon1.setFLinkId("0");
        inSon1.setFpurchaseicentryLinkFflowid("");
        inSon1.setFpurchaseicentryLinkFflowlineid("0");
        inSon1.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
        inSon1.setFpurchaseicentryLinkFstableid("0");
        inSon1.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
        inSon1.setFpurchaseicentryLinkFsbillid("100083");
        inSon1.setFpurchaseicentryLinkFsid("100088");
        inSon1.setFpurchaseicentryLinkFbasicunitqtyold("40");
        inSon1.setFpurchaseicentryLinkFbasicunitqty("40");
        inSon1.setFpurchaseicentryLinkFallamountforold(new BigDecimal(4520));
        inSon1.setFpurchaseicentryLinkFallamountfor(new BigDecimal(4000));
        fpurchaseicentryLink.add(inSon1);

        PurchaseVatSpecialInvoiceDetailLinkDto inSon2 = new PurchaseVatSpecialInvoiceDetailLinkDto();
        inSon2.setFLinkId("0");
        inSon2.setFpurchaseicentryLinkFflowid("");
        inSon2.setFpurchaseicentryLinkFflowlineid("0");
        inSon2.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
        inSon2.setFpurchaseicentryLinkFstableid("0");
        inSon2.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
        inSon2.setFpurchaseicentryLinkFsbillid("100084");
        inSon2.setFpurchaseicentryLinkFsid("100089");
        inSon2.setFpurchaseicentryLinkFbasicunitqtyold("40");
        inSon2.setFpurchaseicentryLinkFbasicunitqty("40");
        inSon2.setFpurchaseicentryLinkFallamountforold(new BigDecimal(4520));
        inSon2.setFpurchaseicentryLinkFallamountfor(new BigDecimal(4000));
        fpurchaseicentryLink.add(inSon2);

        sonDto.setFPURCHASEICENTRY_Link(fpurchaseicentryLink);
        FPURCHASEICENTRY.add(sonDto);
        dto.setFPURCHASEICENTRY(FPURCHASEICENTRY);

        System.out.println("dto-->"+ JSON.toJSON(dto));
        //转换为金蝶入参
        KingDeePurchaseVatSpecialInvoiceCommand command = vatSpecialInvoiceCommandConvertor.toCommand(dto);
        System.out.println("入参commad-->"+JSON.toJSON(command));
        //调用金蝶方法
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
        System.out.println("金蝶返回-->"+JSON.toJSON(successEntities));

        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            dto.setFid(successEntity.getId());
            //金蝶业务数据入表
            vatPlainInvoiceMapper.insertSelective(vatSpecialInvoiceConvertor.toEntity(dto));
            System.out.println("finish-->");
        }
    }

    //调用记录 - 1 11-10 16:46 成功
    //{"fDOCUMENTSTATUS":"Z","fID":"0","fSUPPLIERID":{"fNumber":"VEN0006"},"f_QZOK_FPDM":"FPDM21","f_QZOK_BDDJTID":"BD43444","fRedBlue":"0","fINVOICENO":"FP123","fBillTypeID":{"fNumber":"CGZZSZYFP01_SYS"},"fSETTLEORGID":{"fNumber":"101"},"fCancelStatus":"A","fDATE":"2022-11-10 00:00:00","fPURCHASEICENTRY":[{"fAMOUNTFOR":8000,"fSOURCETYPE":"AP_Payable","f_QZOK_YSDDH":"BD1","fTAXRATE":"13.00","fPURCHASEICENTRY_Link":[{"fPURCHASEICENTRY_Link_FALLAMOUNTFOROld":4520,"fPURCHASEICENTRY_Link_FSId":"100088","fPURCHASEICENTRY_Link_FSTableName":"T_AP_PAYABLEENTRY","fPURCHASEICENTRY_Link_FSBillId":"100083","fPURCHASEICENTRY_Link_FALLAMOUNTFOR":4000,"fPURCHASEICENTRY_Link_FBASICUNITQTYOld":"40","fPURCHASEICENTRY_Link_FLinkId":"0","fPURCHASEICENTRY_Link_FFlowId":"","fPURCHASEICENTRY_Link_FFlowLineId":"0","fPURCHASEICENTRY_Link_FBASICUNITQTY":"40","fPURCHASEICENTRY_Link_FRuleId":"IV_PayableToPurchaseIC","fPURCHASEICENTRY_Link_FSTableId":"0"},{"fPURCHASEICENTRY_Link_FALLAMOUNTFOROld":4520,"fPURCHASEICENTRY_Link_FSId":"100089","fPURCHASEICENTRY_Link_FSTableName":"T_AP_PAYABLEENTRY","fPURCHASEICENTRY_Link_FSBillId":"100084","fPURCHASEICENTRY_Link_FALLAMOUNTFOR":4000,"fPURCHASEICENTRY_Link_FBASICUNITQTYOld":"40","fPURCHASEICENTRY_Link_FLinkId":"0","fPURCHASEICENTRY_Link_FFlowId":"","fPURCHASEICENTRY_Link_FFlowLineId":"0","fPURCHASEICENTRY_Link_FBASICUNITQTY":"40","fPURCHASEICENTRY_Link_FRuleId":"IV_PayableToPurchaseIC","fPURCHASEICENTRY_Link_FSTableId":"0"}],"fAUXTAXPRICE":113,"fPRICEQTY":"80.0","fALLAMOUNTFOR":9040,"f_QZOK_GSYWDH":"BD9","f_QZOK_YWLX":"ADD_Q","fDETAILTAXAMOUNTFOR":1040,"fMATERIALID":{"fNumber":"sku0001"},"f_QZOK_BDDJHID":"333"}]}

    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":101232,"Number":"PVINV00001150","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":101232,"Number":"PVINV00001150","NeedReturnData":[{}]}}]
    //金蝶返回-->[{"number":"PVINV00001150","dIndex":0,"id":"101232"}]
}
