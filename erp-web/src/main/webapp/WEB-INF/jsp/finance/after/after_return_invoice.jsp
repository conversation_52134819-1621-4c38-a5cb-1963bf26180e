<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="售后退票" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/finance/after/after_return_invoice.js?rnd=${resourceVersionKey}'></script>
<div class="form-list form-tips4" >
	<div id="addAfterCapitalBillForm">
		<ul>
			<li>
				<div class="form-tips">
					<span>*</span>
					<lable>发票代码</lable>
				</div>
				<div class="f_left">
					<div class="form-blanks">
						<%--<c:if test="${afterInvoice.currentMonthInvoice eq 1 && afterInvoice.invoiceProperty ne 3}">${afterInvoice.invoiceCode}</c:if>--%>
						<%--<c:if test="${afterInvoice.currentMonthInvoice eq 0 || afterInvoice.invoiceProperty eq 3}">--%>
							<%--<input type="text" class="input-middle" id="in_invoiceCode" onkeyup="vailInvoiceCode(this);"/>--%>
						<%--</c:if>--%>

						<input type="text" class="input-middle" id="in_invoiceCode" onkeyup="vailInvoiceCode(this);" <c:if test="${afterInvoice.currentMonthInvoice eq 1}">value="${afterInvoice.invoiceCode}" readonly="readonly" style="background-color:#c5c2c2"</c:if>/>
						<input type="checkbox" name="fullElectronicInvoice" class="mt5" onclick="chooseBuyorderAfterReturnFullElectronicInvoice();">
						<span>数电发票</span>
					</div>
				</div>
			</li>
			<li>
				<div class="form-tips">
					<span>*</span>
					<lable>发票号码</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks">
						<%--<c:if test="${afterInvoice.currentMonthInvoice eq 1}">${afterInvoice.invoiceNo}</c:if>--%>
						<%--<c:if test="${afterInvoice.currentMonthInvoice eq 0}">--%>
							<%--<input type="text" id="in_invoiceNo" class="input-middle" onkeyup="vailInvoiceNo(this);"/>--%>
						<%--</c:if>--%>

                        <input type="text" id="in_invoiceNo" class="input-middle" onkeyup="vailInvoiceNo(this);" <c:if test="${afterInvoice.currentMonthInvoice eq 1}">value="${afterInvoice.invoiceNo}" readonly="readonly" style="background-color:#c5c2c2"</c:if>/>
                    </div>
				</div>
			</li>
			<li>
				<div class="form-tips">
					<lable>发票金额</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks" id="afterReturnInvoiceAmount">
						<fmt:formatNumber type="number" value="${afterInvoice.amount}" pattern="0.00" maxFractionDigits="2" />
					</div>
					<input type="hidden" value="${afterInvoice.amount}" id="afterReturnInvoiceAmountHidden">
				</div>
			</li>
			<c:if test="${afterInvoice.afterType eq 546}">
				<li>
					<div class="form-tips">
						<lable>退票金额</lable>
					</div>
					<div class="f_left ">
						<div class="form-blanks" id="returnInvoiceAmount">
							0.00
						</div>
					</div>
				</li>
			</c:if>
			<li>
				<div class="form-tips">
					<span>*</span>
					<lable>票种</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks">
						<c:forEach var="invoiceList" items="${invoiceTypeList}">
							<c:if test="${invoiceList.sysOptionDefinitionId eq afterInvoice.invoiceType}">${invoiceList.title}</c:if>
						</c:forEach>
					</div>
				</div>
			</li>
			<li>
				<div class="form-tips">
					<lable>红蓝字</lable>
				</div>
				<div class="f_left ">
					<div class="form-blanks">
						 <span id="invoiceTypeText" <c:if test="${afterInvoice.currentMonthInvoice eq 1}">style="color: blue" </c:if> <c:if test="${afterInvoice.currentMonthInvoice eq 0}">style="color: red"</c:if>>
                            <c:if test="${afterInvoice.currentMonthInvoice eq 1}">蓝字作废</c:if>
                            <c:if test="${afterInvoice.currentMonthInvoice eq 0}">红字有效</c:if>
                        </span>
					</div>
				</div>
			</li>
			<li>
				<%--  	<c:choose>
                          <c:when test="${afterInvoice.currentMonthInvoice eq 0}"><!-- 非当月发票 -->--%>
				<div class="form-tips">
					<lable>退票范围</lable>
				</div>
				<div class="f_left f_left_wid90">
					<div class="form-blanks ">
						<%-- <c:choose>
                            <c:when test="${afterInvoice.afterType eq 542}"><!-- 销售退票 -->
                                <input type="radio" name="returnInvoiceCheck" checked onclick="checkReturnInvoice(this,1);"><label>全部商品退票</label>
                            </c:when>
                            <c:otherwise>
                                <input type="radio" name="returnInvoiceCheck" checked onclick="checkReturnInvoice(this,0);"><label>仅退货商品部分退票</label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="radio" name="returnInvoiceCheck" onclick="checkReturnInvoice(this,1);"><label>全部商品退票</label>
                            </c:otherwise>
                        </c:choose> --%>
						<c:if test="${afterInvoice.afterType ne 546 && afterInvoice.afterType ne 548}">
							<input type="radio" name="returnInvoiceCheck" value="1" onclick="checkReturnInvoice(this,1);"><label>全部商品退票</label>
						</c:if>
						<c:choose>
							<c:when test="${afterInvoice.afterType eq 548}">
								<input type="radio" name="returnInvoiceCheck" value="0" checked onclick="checkReturnInvoice(this,0);"><label>仅退票商品</label>
							</c:when>
							<c:otherwise>
								<input type="radio" name="returnInvoiceCheck" value="0" checked onclick="checkReturnInvoice(this,0);"><label>仅退货商品部分退票</label>
							</c:otherwise>
						</c:choose>
					</div>
					<%--	</c:when>
                        <c:otherwise>
                            <div class="f_left" style='margin-left:70px;'>
                        </c:otherwise>
                    </c:choose>--%>
					<div class="mt10">
						<table class="table" id="afterGoodsListId">
							<thead>
							<tr>
								<c:choose>
									<c:when test="${afterInvoice.afterType eq 548}">
										<th hidden="hidden" class="wid6">选择</th>
									</c:when>
									<c:otherwise>
										<th class="wid6">选择</th>
									</c:otherwise>
								</c:choose>
								<th class="wid25">产品名称</th>
								<th class="wid15">品牌</th>
								<th class="wid12">型号</th>
								<c:if test="${afterInvoice.afterType eq 548}">
									<th class="wid10">单价</th>
								</c:if>
								<c:choose>
									<c:when test="${afterInvoice.afterType eq 548}">
										<th class="wid8">录票单价</th>
									</c:when>
									<c:otherwise>
										<th class="wid8">单价</th>
									</c:otherwise>
								</c:choose>
								<th class="wid8">订单数量</th>
								<th class="wid6">单位</th>
								<c:if test="${afterInvoice.afterType ne 542 && afterInvoice.afterType ne 548}"><!-- 不是销售退票 -->
								<th class="wid10">退货数量</th>
								</c:if>
								<c:if test="${afterInvoice.afterType eq 548}">
									<th class="wid10">仅退票售后数量</th>
								</c:if>
								<th class="wid10">退票数量</th>
								<th class="wid10">退票金额</th>
								<c:if test="${afterInvoice.afterType eq 546}">
									<th class="wid10">商品已出库数</th>
									<th class="wid10">蓝票已录入该订单数量</th>
								</c:if>
							</tr>
							</thead>
							<tbody>
							<c:forEach var="goodslist" items="${afterInvoice.afterGoodsList}" varStatus="listNum">
								<input type="hidden" name="detailGoodsId" id="detailGoodsId_${listNum.index}" value="${goodslist.orderDetailId}"/>
								<tr>
									<c:choose>
										<c:when test="${afterInvoice.afterType eq 548}">
											<td hidden="hidden">
												<input type="checkbox" checked name="chooseGoods" id="chooseGoods_${listNum.index}" value="${listNum.index}">
											</td>
										</c:when>
										<c:otherwise>
											<td>
												<input type="checkbox" name="chooseGoods"  id="chooseGoods_${listNum.index}" onchange="checkboxChange()" value="${listNum.index}" checked>
											</td>
										</c:otherwise>
									</c:choose>
									<td class='text-left'>
											<span class="font-blue cursor-pointer addtitle" tabtitle='{"num":"viewgoods${goodslist.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${goodslist.goodsId}","title":"产品信息"}'>
													${goodslist.goodsName}
											</span><br/>
											${goodslist.sku}<br/>
											${goodslist.materialCode}
									</td>
									<td>${goodslist.brandName}</td>
									<td>${goodslist.model}</td>
									<c:if test="${afterInvoice.afterType eq 548}">
										<td>${goodslist.orderPrice}</td>
									</c:if>
									<td id="showPrice_${listNum.index}"><fmt:formatNumber type="number" value="${goodslist.orderPrice}" pattern="0.00" maxFractionDigits="2" /></td>
									<input type="hidden" id="price_${listNum.index}"value="${goodslist.orderPrice}" name="orderPrice">
									<td><fmt:formatNumber type="number" value="${goodslist.orderNum}" pattern="0.00" maxFractionDigits="2" /></td>
									<td>${goodslist.unitName}</td>
									<c:if test="${afterInvoice.afterType ne 542 && afterInvoice.afterType ne 548}"><!-- 不是销售退票 -->
									<td>
                                                <span id="spanReturnNum${listNum.index}">
													<fmt:formatNumber type="number" value="${goodslist.num==null?0:goodslist.num}" pattern="0.00" maxFractionDigits="2" />
												</span>
									</td>
									</c:if>
									<c:if test="${afterInvoice.afterType eq 548}">
										<td><fmt:formatNumber type="number" value="${goodslist.afterInvoiceNum}" pattern="0.00" maxFractionDigits="2" /></td>
									</c:if>
									<c:choose>
										<c:when test="${afterInvoice.currentMonthInvoice eq 1}">
											<input type="hidden" name="hideReturnNum" id="${listNum.index}" value="${goodslist.invoiceNum}"/>
											<%--                                                <input type="hidden" id="realReturnNum_${listNum.index}" value="${goodslist.invoiceNum}">--%>
										</c:when>
										<c:otherwise>
											<input type="hidden" name="hideReturnNum" id="${listNum.index}" value="${(goodslist.num le goodslist.invoiceNum)?goodslist.num:goodslist.invoiceNum}"/>
											<%--                                                <input type="hidden" id="realReturnNum_${listNum.index}" value="${(goodslist.num le goodslist.invoiceNum)?goodslist.num:goodslist.invoiceNum}">--%>
										</c:otherwise>
									</c:choose>
									<input type="hidden" id="allReturnNum_${listNum.index}" value="${goodslist.invoiceNum}">


									<td align="center">
										<input type="hidden" name="hideInvoiceNum" id="${listNum.index}" value="${goodslist.invoiceNum==null?0:goodslist.invoiceNum}"/>
										<!-- 如果是蓝字作废退票数量取退货数量 -->
											<%--											<div id="spanInvoiceNum${listNum.index}">--%>
										<c:choose >
											<c:when test="${afterInvoice.afterType eq 546}">
												<c:set var="needInvoiceNum" value="${goodslist.canReturnInvoiceNum}"/>
												<input name="hideReturnInvoiceNum" id="hideReturnInvoiceNum_${listNum.index}"
													   indexFlag="${listNum.index}" size="10"
													   valueFlag="<fmt:formatNumber type="number" value="${goodslist.canReturnInvoiceNum}" pattern="0.00" maxFractionDigits="2" />"
													   onblur="invoiceChange(${goodslist.orderPrice},${listNum.index});" onkeyup="clearNoNum(this)"
													   value="<fmt:formatNumber type="number" value="${goodslist.canReturnInvoiceNum}" pattern="0.00" maxFractionDigits="2" />"/>
											</c:when>
											<c:when test="${afterInvoice.currentMonthInvoice eq 1}">
												<c:set var="needInvoiceNum" value="${goodslist.invoiceNum}"/>
												<input name="hideReturnInvoiceNum" id="hideReturnInvoiceNum_${listNum.index}"
													   indexFlag="${listNum.index}" size="10"
													   valueFlag="<fmt:formatNumber type="number" value="${goodslist.invoiceNum}" pattern="0.00" maxFractionDigits="2" />"
													   onblur="invoiceChange(${goodslist.orderPrice},${listNum.index});" onkeyup="clearNoNum(this)"
													   value="<fmt:formatNumber type="number" value="${goodslist.invoiceNum}" pattern="0.00" maxFractionDigits="2" />"/>
											</c:when>
											<c:otherwise>
												<input name="hideReturnInvoiceNum" id="hideReturnInvoiceNum_${listNum.index}"
													   indexFlag="${listNum.index}" size="10"
													   valueFlag="<fmt:formatNumber type="number" value="${(goodslist.num le goodslist.invoiceNum)?goodslist.num:goodslist.invoiceNum}" pattern="0.00" maxFractionDigits="2" />"
													   onblur="invoiceChange(${goodslist.orderPrice},${listNum.index});" onkeyup="clearNoNum(this)"
													   value="<fmt:formatNumber type="number" value="${(goodslist.num le goodslist.invoiceNum)?goodslist.num:goodslist.invoiceNum}" pattern="0.00" maxFractionDigits="2" />"/>
												<c:set var="needInvoiceNum" value="${(goodslist.num le goodslist.invoiceNum)?goodslist.num:goodslist.invoiceNum}"/>
											</c:otherwise>
										</c:choose>
											<%--											</div>--%>
									</td>
									<td>
										<!-- 计算发票总额 -->
										<input  name="hideReturnInvoiceAmount" id="hideReturnInvoiceAmount_${listNum.index}"
												indexFlag="${listNum.index}" size="10" canRecordAmount="${goodslist.canRecordAmount}" canRecordNum="${goodslist.canRecordNum}"
												valueFlag="<fmt:formatNumber type="number" pattern="0.00" maxFractionDigits="2" value="${needInvoiceNum * goodslist.orderPrice}" />"
												placeholder="请保留两位小数" onkeyup="clearNoNum(this)"
												onblur="invoiceChange(${goodslist.orderPrice},${listNum.index});"
												value="<fmt:formatNumber type="number" pattern="0.00" maxFractionDigits="2"
																			 value="${needInvoiceNum * (goodslist.orderPrice - goodslist.rebatePrice)}"/>"/>
									</td>

									<c:if test="${afterInvoice.afterType eq 546}">
										<td>
												${goodslist.outcnt}
										</td>

										<td>
												${goodslist.invoiceNum}
										</td>
									</c:if>
								</tr>
							</c:forEach>
							</tbody>
						</table>

						<c:if test="${afterInvoice.afterType eq 546}">
							<div style="color: red">
								1、为确保金蝶数据一致，商品退票数量必须小于等于该商品已出库数量
								<br/>
								2、若需要退掉该发票下该商品的所有数量，请务必确保该商品采购售后出库数量大于退票数量
							</div>
						</c:if>
						<c:if test="${afterInvoice.afterType eq 548}">
							<div style="color: #d58c0f;">
								<i class="vd-icon icon-info2" style="color: #d58c0f; background: none"></i>
								红票录入后，系统在自动生成红票记录的同事，将自动使用录票单价、数量及售后提交人员所提供的蓝字发票，重新录入至原订单，请知悉
							</div>
						</c:if>
					</div>
				</div>
			</li>
		</ul>
		<form method="post" action="<%= basePath %>/finance/after/saveAfterReturnInvoice.do" id="returnInvoiceForm">
			<input type="hidden" name="formToken" value="${formToken}"/>
			<input type="hidden" name="currentMonthInvoice" id="currentMonthInvoice" value="${afterInvoice.currentMonthInvoice}"/><!-- 是否为当月发票1是：0否 -->
			<input type="hidden" name="invoiceId" value="${afterInvoice.invoiceId}"><!-- 发票主表ID -->
			<input type="hidden" name="afterSalesId" value="${afterInvoice.afterSalesId}"><!-- 售后主表ID -->
			<input type="hidden" name="relatedId" id="relatedId" value="${afterInvoice.orderId}"/><!-- 销售、采购ID -->
			<input type="hidden" name="invoiceProperty" id="invoiceProperty" value="${afterInvoice.invoiceProperty}"/>

			<input type="hidden" name="originInvoiceCode" id="originInvoiceCode" value="${afterInvoice.invoiceCode}"/>
			<input type="hidden" name="originInvoiceNo" id="originInvoiceNo" value="${afterInvoice.invoiceNo}"/>
			<c:choose>
				<c:when test="${afterInvoice.currentMonthInvoice eq 1}">
					<input type="hidden" name="invoiceNo" id="invoiceNo" value="${afterInvoice.invoiceNo}" />
				</c:when>
				<c:otherwise>
					<input type="hidden" name="invoiceNo" id="invoiceNo" /><!-- 发票号码 -->
				</c:otherwise>
			</c:choose>
			<input type="hidden" name="invoiceCode" id="invoiceCode" /><!-- 发票代码 -->
			<input type="hidden" name="invoiceType" id="invoiceType" value="${afterInvoice.invoiceType}"/><!-- 发票类型 -->
			<input type="hidden" name="ratio" id="ratio" value="${afterInvoice.ratio}"/><!-- 发票税率 -->
			<c:if test="${afterInvoice.currentMonthInvoice eq 1}"><!-- 蓝字作废 -->
			<input type="hidden" name="colorType" id="colorType" value="2"/><!-- 红蓝字 1红2蓝 -->
			<input type="hidden" name="isEnable" id="isEnable" value="0"/><!-- 是否有效 0否 1是 -->
			</c:if>
			<c:if test="${afterInvoice.currentMonthInvoice eq 0}"><!-- 红字有效 -->
			<input type="hidden" name="colorType" id="colorType" value="1"/><!-- 红蓝字 1红2蓝 -->
			<input type="hidden" name="isEnable" id="isEnable" value="1"/><!-- 是否有效 0否 1是 -->
			</c:if>
			<input type="hidden" name="validStatus" id="validStatus" value="1"/>
			<c:choose>
				<c:when test="${afterInvoice.subjectType eq 536}"><!-- 采购 -->
					<input type="hidden" name="type" id="type" value="503"/><!-- 采购 -->
					<input type="hidden" name="tag" id="tag" value="2"/><!-- 采购 -->
				</c:when>
				<c:otherwise>
					<input type="hidden" name="type" id="type" value="505"/><!-- 销售 -->
					<input type="hidden" name="tag" id="tag" value="1"/><!-- 销售 -->
				</c:otherwise>
			</c:choose>
			<div class="add-tijiao text-left f_left_wid90 " style='margin:-10px 0 0 110px;'>
				<button type="button" id="addAfterReturnInvoiceButton" class="bt-bg-style bg-light-green bt-small" onclick="addAfterReturnInvoice();">提交</button>
				<!-- <button class="dele" type="button" id="close-layer">取消</button> -->
			</div>
			<span id="dynamicParameter"></span><!-- jQuery动态参数 -->
			<input type="hidden" name="afterId" id="afterId" value="${afterInvoice.afterSalesInvoiceId}"/><!-- 售后发票ID -->
			<input type="hidden" id="afterType" value="${afterInvoice.afterType}">
			<input type="hidden" id="invoiceProperty" name="invoiceProperty" value="${afterInvoice.invoiceProperty}"/>
			<input type="hidden" id="oldInvoiceNo" name="oldInvoiceNo" value="${afterInvoice.oldInvoiceNo}"/>
		</form>
	</div>
</div>
<%@ include file="../../common/footer.jsp"%>