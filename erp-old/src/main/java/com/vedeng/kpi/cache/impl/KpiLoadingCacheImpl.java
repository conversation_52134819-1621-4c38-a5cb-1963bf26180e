package com.vedeng.kpi.cache.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.dao.KpiConfigMapper;
import com.vedeng.kpi.model.DTO.*;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiGroupConfigDto;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiTeamDto;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiUserConfigDto;
import com.vedeng.kpi.share.KpiUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;


@Component("kpiLoadingCache")
public class KpiLoadingCacheImpl implements KpiLoadingCache {

    private static final Logger log = LoggerFactory.getLogger("kpilog");

    private static final ThreadLocal<DateFormat> sdf =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    @Resource
    private KpiConfigMapper kpiConfigMapper;

    private LoadingCache<Integer,KpiGroupConfigDto> groupConfigCache = null;

    private LoadingCache<Integer,KpiUserConfigDto> userConfigCache = null;

    private LoadingCache<Integer, List<KpiUserConfigDto>> hisTargetCache = null;

    private CacheLoader<Integer, KpiGroupConfigDto> groupConfigCacheLoader = new CacheLoader<Integer, KpiGroupConfigDto>() {
        /**
         * 单条数据查询,缓存穿透.
         * @jira: VDERP-2376【五行剑法】规则修改.
         * @notes: 这里未重载loadAll方法,是因为团队配置量很少,无需进行批量查询优化.
         * @version: 1.0.
         * @date: 2020/7/6 9:32 上午.
         * @author: Tomcat.Hui.
         * @param key: .
         * @return: com.vedeng.kpi.model.DTO.KpiGroupConfigDto.
         * @throws: .
         */
        @Override
        public KpiGroupConfigDto load(Integer key){
//            log.info("正在加载团队 " + key + " 配置");
            KpiConfigQueryDto query = new KpiConfigQueryDto();
            query.setCompanyId(1);
            query.setIsDelete(0);
            query.setGroupId(key);
            return Optional.ofNullable(kpiConfigMapper.getGroupConfig(query)).orElse(new NullKpiGroupConfigDto());
        }
    };

    private CacheLoader<Integer, KpiUserConfigDto> userConfigCacheLoader = new CacheLoader<Integer, KpiUserConfigDto>() {
        @Override
        public KpiUserConfigDto load(Integer key){
//            log.info("正在加载用户 " + key + " 配置");
            KpiConfigQueryDto query = new KpiConfigQueryDto();
            query.setUserId(key);
            return Optional.ofNullable(kpiConfigMapper.getUserConfig(query)).orElse(new NullKpiUserConfigDto());
        }

        /**
         * 重载loadAll()方法.
         * @jira: VDERP-2376【五行剑法】规则修改.
         * @notes: 如果不重载该方法,默认会使用未被缓存的ID作为传参,挨个调用get()方法,即每个ID都会执行一次数据库查询
         *          这里使用了where id in (1,2,3,eg.) 的方式,避免在ID数量过多时,查询次数过多.
         * @version: 1.0.
         * @date: 2020/7/6 9:28 上午.
         * @author: Tomcat.Hui.
         * @param keys: .
         * @return: java.util.Map<java.lang.Integer,com.vedeng.kpi.model.DTO.KpiUserConfigDto>.
         * @throws: .
         */
        @Override
        public Map<Integer, KpiUserConfigDto> loadAll(Iterable<? extends Integer> keys){
            log.info("正在加载全量用户配置");
            KpiConfigQueryDto query = new KpiConfigQueryDto();
            query.setUserIds(Lists.newArrayList(keys));
            return kpiConfigMapper.getBatchUserConfig(query).stream()
                    .collect(Collectors.toMap(KpiUserConfigDto::getUserId,c -> c));
        }
    };

    private CacheLoader<Integer, List<KpiUserConfigDto>> hisTargetCacheLoader = new CacheLoader<Integer, List<KpiUserConfigDto>> () {
        @Override
        public List<KpiUserConfigDto> load(Integer key){
            log.info("正在加载全量历史业绩目标");
            //默认取全量,然后过滤用户(为了兼容getAll场景)
            return kpiConfigMapper.getHisMonthTarget();
        }
    };

    /**
     * 初始化加载器.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 9:42 上午.
     * @author: Tomcat.Hui.
     * @return: void.
     * @throws: .
     */
    @PostConstruct
    public void init(){
        userConfigCache = CacheBuilder.newBuilder().build(userConfigCacheLoader);
        groupConfigCache = CacheBuilder.newBuilder().build(groupConfigCacheLoader);
        hisTargetCache = CacheBuilder.newBuilder().build(hisTargetCacheLoader);
    }

    @Override
    public KpiGroupConfigDto getGroupConfig(Integer groupId) {
        try {
            return groupConfigCache.get(groupId);
        } catch (Exception e) {
            log.error("查询团队 " + groupId + " " + " 配置出现异常",e);
            return new NullKpiGroupConfigDto();
        }
    }


    @Override
    public List<KpiGroupConfigDto> getAllGroupConfig() {
        try {
            List<Integer> groupIds = kpiConfigMapper.getGroupIds().stream().filter(id -> id != null).collect(Collectors.toList());
            log.info("查询全量有效团队id: " + groupIds);
            return groupConfigCache.getAll(groupIds).values().stream()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询全量团队配置出现异常",e);
            return Lists.newArrayList();
        }
    }

    @Override
    public KpiTeamDto getTeamConfig(Integer groupId, Integer teamId) {
        try {
            return groupConfigCache.get(groupId).getTeams().stream()
                    .filter(t -> t.getTeamId().equals(teamId)).findFirst().orElse(new NullKpiTeamDto());
        } catch (Exception e) {
            log.error("查询小组 " + teamId + "  配置出现异常",e);
            return new NullKpiTeamDto();
        }
    }

    @Override
    public KpiUserConfigDto getUserConfig(Integer userId) {
        try {
            return userConfigCache.get(userId);
        } catch (ExecutionException e) {
            log.info("查询个人 " + userId + "  配置出现异常: " + e.getMessage());
            return new NullKpiUserConfigDto();
        }catch (Exception e) {
            log.error("查询个人 " + userId + "  配置出现异常",e);
            return new NullKpiUserConfigDto();
        }
    }

    @Override
    public List<KpiUserConfigDto> getAllUserConfig() {
        try {
            List<Integer> userIds = kpiConfigMapper.getUserIds().stream().filter(id -> id != null).collect(Collectors.toList());
//            log.info("查询全量有效用户id: " + userIds);
            return userConfigCache.getAll(userIds).values().stream().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询全量个人配置出现异常",e);
            return Lists.newArrayList();
        }
    }

    @Override
    public BigDecimal getGroupMonthGoal(Integer groupId, Date date) {
        try {
            String nowMonthStr = sdf.get().format(KpiUtils.getMonthStart(new Date()));
            String targetMonthStr = sdf.get().format(KpiUtils.getMonthStart(date));

            if (sdf.get().format(date).equals(nowMonthStr)) {
                //本月
                return userConfigCache.getAll(kpiConfigMapper.getUserIds()).values().stream()
                        .filter(c -> c.getGroupId().equals(groupId))
                        .map(c -> c.getMonthAmountGoal())
                        .reduce(BigDecimal.ZERO,BigDecimal::add);
            } else {
                //历史月
                return this.getAllUserConfig().stream().filter(u -> u.getGroupId().equals(groupId))
                        .map(KpiUserConfigDto::getUserId)
                        .map(id ->getHisTargetByUser(id,targetMonthStr))
                        .reduce(BigDecimal.ZERO,BigDecimal::add);
            }

        } catch (Exception e) {
            log.error("查询团队 " + groupId + " 当月业绩目标金额出现异常",e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getTeamMonthGoal(Integer groupId,Integer teamId, Date date) {
        try {
            String nowMonthStr = sdf.get().format(KpiUtils.getMonthStart(new Date()));
            String targetMonthStr = sdf.get().format(KpiUtils.getMonthStart(date));

            if (sdf.get().format(date).equals(nowMonthStr)) {
                //本月
                return userConfigCache.getAll(kpiConfigMapper.getUserIds()).values().stream()
                        .filter(c -> c.getGroupId().equals(groupId) && c.getTeamId().equals(teamId))
                        .map(c -> c.getMonthAmountGoal())
                        .reduce(BigDecimal.ZERO,BigDecimal::add);
            } else {
                //历史月
                return this.getAllUserConfig().stream().filter(u -> u.getGroupId().equals(groupId)
                        && u.getTeamId().equals(teamId))
                        .map(KpiUserConfigDto::getUserId)
                        .map(id ->getHisTargetByUser(id,targetMonthStr))
                        .reduce(BigDecimal.ZERO,BigDecimal::add);
            }

        } catch (Exception e) {
            log.error("查询小组 " + teamId + " 当月业绩目标金额出现异常",e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public List<Integer> getGroupUserIdsByUserId(Integer userId) {
        try {
            KpiUserConfigDto userConfig = userConfigCache.get(userId);
            if (null == userConfig) {
                log.info("查询用户 " + userId + " 所在团队所有用户ID无结果");
                return Lists.newArrayList();
            } else {
                KpiGroupConfigDto group = groupConfigCache.get(userConfig.getGroupId());
//                log.info("用户 " + userId + " 所在团队: " + group.getGroupName());
                return group.getTeams().stream().flatMap(t -> t.getUsers().stream()
                        .map(KpiUserConfigDto::getUserId))
                        .filter(id -> id != null)
                        .collect(Collectors.toList());
            }
        } catch (ExecutionException e) {
            log.info("查询用户 " + userId + " 所在团队所有用户ID出现异常: " + e.getMessage());
            return Lists.newArrayList();
        } catch (Exception e){
            log.info("查询用户 " + userId + " 所在团队所有用户ID出现异常"+ e.getMessage());
            return Lists.newArrayList();
        }
    }

    @Override
    public List<KpiUserConfigDto> getGroupUsersByGroupId(Integer groupId){
        try {
        return groupConfigCache.get(groupId).getTeams().stream()
                .flatMap(t -> t.getUsers().stream()).filter(u -> u.getUserId() != null)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询团队 " + groupId + " 所有用户出现异常",e);
            return Lists.newArrayList();
        }
    }

    @Override
    public BigDecimal getHisTargetByUser(Integer userId,String month) {
        try {
            String monthStr = sdf.get().format(KpiUtils.getMonthStart(month));
            String nowMonthStr = sdf.get().format(KpiUtils.getMonthStart(new Date()));
            return nowMonthStr.equals(monthStr) ?
                    userConfigCache.get(userId).getMonthAmountGoal() :
                    hisTargetCache.get(1).stream()
                            .filter(c -> c.getUserId().equals(userId) && c.getMonthStr().equals(monthStr))
                    .findFirst().orElse(new NullKpiUserConfigDto()).getMonthAmountGoal().divide( new BigDecimal(10000));
        } catch (Exception e) {
            log.warn("查询用户 {} 历史业绩目标出现异常", userId, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public String getGroupName(Integer groupId) {
        try {
            return this.getGroupConfig(groupId).getGroupName();
        } catch (Exception e) {
            log.error("查询团队 " + groupId + " 名称出现异常",e);
            return "";
        }
    }

    @Override
    public void clearAll(){
        try {
            log.info("开始清空五行所有配置");
            groupConfigCache.invalidateAll();
            userConfigCache.invalidateAll();
            hisTargetCache.invalidateAll();
        }catch (Exception e){
            log.error("刷新五行所有配置出现异常:",e);
        }
    }

    @Override
    public void clearUserAndGroup() {
        try {
            groupConfigCache.invalidateAll();
            userConfigCache.invalidateAll();
        } catch (Exception e){
            log.error("刷新五行用户&团队配置出现异常:",e);
        }
    }

    @Override
    public void KpiAmountTarget() {
        try {
            hisTargetCache.invalidateAll();
        }catch (Exception e){
            log.error("刷新五行月度目标配置出现异常:",e);
        }
    }

    @Override
    public List<KpiUserConfigDto> getManagerUsers(KpiUserInfoDto userInfo) {
        List<KpiGroupConfigDto> result = this.getAllGroupConfig();

        if (userInfo.getUserName().equals("njadmin")) {
            //njadmin查看所有人
            return result.stream()
                    .flatMap(g -> g.getTeams().stream()).filter(t -> t.getTeamId() != null)
                    .flatMap(t -> t.getUsers().stream()).filter(u -> u.getUserId() != null)
                    .collect(Collectors.toList());

        } else {
            //查询用户管理哪些团队
            List<KpiGroupConfigDto> managerGroups = result
                    .stream().filter(g -> g.getGroupManagerId().contains(userInfo.getUserId())).collect(Collectors.toList());

            if (null != managerGroups && managerGroups.size() != 0){
                return managerGroups.stream()
                        .map(KpiGroupConfigDto::getGroupId)
                        .flatMap(id -> this.getGroupUsersByGroupId(id).stream())
                        .collect(Collectors.toList());
            } else {
                //查询管理哪些小组
                return this.getAllGroupConfig()
                        .stream().flatMap(g -> g.getTeams().stream())
                        .filter(t -> t.getTeamId() != null)
                        .filter(t -> t.getTeamManagerId().equals(userInfo.getUserId()))
                        .flatMap(t -> t.getUsers().stream())
                        .filter(u -> u.getUserId()!= null)
                        .collect(Collectors.toList());
            }
        }
    }
}
