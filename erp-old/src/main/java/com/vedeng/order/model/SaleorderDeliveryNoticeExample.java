package com.vedeng.order.model;

import java.util.ArrayList;
import java.util.List;

public class SaleorderDeliveryNoticeExample {

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SaleorderDeliveryNoticeExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDeliveryNoticeIdIsNull() {
            addCriterion("DELIVERY_NOTICE_ID is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdIsNotNull() {
            addCriterion("DELIVERY_NOTICE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID =", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdNotEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID <>", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdGreaterThan(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID >", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID >=", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdLessThan(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID <", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID <=", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdIn(List<Integer> values) {
            addCriterion("DELIVERY_NOTICE_ID in", values, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdNotIn(List<Integer> values) {
            addCriterion("DELIVERY_NOTICE_ID not in", values, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_NOTICE_ID between", value1, value2, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_NOTICE_ID not between", value1, value2, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoIsNull() {
            addCriterion("DELIVERY_NOTICE_NO is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoIsNotNull() {
            addCriterion("DELIVERY_NOTICE_NO is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoEqualTo(String value) {
            addCriterion("DELIVERY_NOTICE_NO =", value, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoNotEqualTo(String value) {
            addCriterion("DELIVERY_NOTICE_NO <>", value, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoGreaterThan(String value) {
            addCriterion("DELIVERY_NOTICE_NO >", value, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoGreaterThanOrEqualTo(String value) {
            addCriterion("DELIVERY_NOTICE_NO >=", value, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoLessThan(String value) {
            addCriterion("DELIVERY_NOTICE_NO <", value, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoLessThanOrEqualTo(String value) {
            addCriterion("DELIVERY_NOTICE_NO <=", value, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoLike(String value) {
            addCriterion("DELIVERY_NOTICE_NO like", value, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoNotLike(String value) {
            addCriterion("DELIVERY_NOTICE_NO not like", value, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoIn(List<String> values) {
            addCriterion("DELIVERY_NOTICE_NO in", values, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoNotIn(List<String> values) {
            addCriterion("DELIVERY_NOTICE_NO not in", values, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoBetween(String value1, String value2) {
            addCriterion("DELIVERY_NOTICE_NO between", value1, value2, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeNoNotBetween(String value1, String value2) {
            addCriterion("DELIVERY_NOTICE_NO not between", value1, value2, "deliveryNoticeNo");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("ORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("ORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Integer value) {
            addCriterion("ORDER_ID =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Integer value) {
            addCriterion("ORDER_ID <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Integer value) {
            addCriterion("ORDER_ID >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ORDER_ID >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Integer value) {
            addCriterion("ORDER_ID <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("ORDER_ID <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Integer> values) {
            addCriterion("ORDER_ID in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Integer> values) {
            addCriterion("ORDER_ID not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("ORDER_ID between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ORDER_ID not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("ORDER_NO is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("ORDER_NO is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("ORDER_NO =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("ORDER_NO <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("ORDER_NO >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("ORDER_NO >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("ORDER_NO <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("ORDER_NO <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("ORDER_NO like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("ORDER_NO not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("ORDER_NO in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("ORDER_NO not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("ORDER_NO between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("ORDER_NO not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNull() {
            addCriterion("DELIVERY_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNotNull() {
            addCriterion("DELIVERY_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusEqualTo(Byte value) {
            addCriterion("DELIVERY_STATUS =", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotEqualTo(Byte value) {
            addCriterion("DELIVERY_STATUS <>", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThan(Byte value) {
            addCriterion("DELIVERY_STATUS >", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("DELIVERY_STATUS >=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThan(Byte value) {
            addCriterion("DELIVERY_STATUS <", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThanOrEqualTo(Byte value) {
            addCriterion("DELIVERY_STATUS <=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIn(List<Byte> values) {
            addCriterion("DELIVERY_STATUS in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotIn(List<Byte> values) {
            addCriterion("DELIVERY_STATUS not in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusBetween(Byte value1, Byte value2) {
            addCriterion("DELIVERY_STATUS between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("DELIVERY_STATUS not between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`STATUS` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`STATUS` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Boolean value) {
            addCriterion("`STATUS` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Boolean value) {
            addCriterion("`STATUS` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Boolean value) {
            addCriterion("`STATUS` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("`STATUS` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Boolean value) {
            addCriterion("`STATUS` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("`STATUS` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Boolean> values) {
            addCriterion("`STATUS` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Boolean> values) {
            addCriterion("`STATUS` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("`STATUS` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("`STATUS` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andValidTimeIsNull() {
            addCriterion("VALID_TIME is null");
            return (Criteria) this;
        }

        public Criteria andValidTimeIsNotNull() {
            addCriterion("VALID_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andValidTimeEqualTo(Long value) {
            addCriterion("VALID_TIME =", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeNotEqualTo(Long value) {
            addCriterion("VALID_TIME <>", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeGreaterThan(Long value) {
            addCriterion("VALID_TIME >", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("VALID_TIME >=", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeLessThan(Long value) {
            addCriterion("VALID_TIME <", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeLessThanOrEqualTo(Long value) {
            addCriterion("VALID_TIME <=", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeIn(List<Long> values) {
            addCriterion("VALID_TIME in", values, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeNotIn(List<Long> values) {
            addCriterion("VALID_TIME not in", values, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeBetween(Long value1, Long value2) {
            addCriterion("VALID_TIME between", value1, value2, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeNotBetween(Long value1, Long value2) {
            addCriterion("VALID_TIME not between", value1, value2, "validTime");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNull() {
            addCriterion("AUDIT_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNotNull() {
            addCriterion("AUDIT_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusEqualTo(Boolean value) {
            addCriterion("AUDIT_STATUS =", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotEqualTo(Boolean value) {
            addCriterion("AUDIT_STATUS <>", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThan(Boolean value) {
            addCriterion("AUDIT_STATUS >", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("AUDIT_STATUS >=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThan(Boolean value) {
            addCriterion("AUDIT_STATUS <", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("AUDIT_STATUS <=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIn(List<Boolean> values) {
            addCriterion("AUDIT_STATUS in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotIn(List<Boolean> values) {
            addCriterion("AUDIT_STATUS not in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("AUDIT_STATUS between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("AUDIT_STATUS not between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Long value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Long value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Long value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Long value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Long> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Long> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNull() {
            addCriterion("TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNotNull() {
            addCriterion("TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderNameEqualTo(String value) {
            addCriterion("TRADER_NAME =", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotEqualTo(String value) {
            addCriterion("TRADER_NAME <>", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThan(String value) {
            addCriterion("TRADER_NAME >", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME >=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThan(String value) {
            addCriterion("TRADER_NAME <", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME <=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLike(String value) {
            addCriterion("TRADER_NAME like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotLike(String value) {
            addCriterion("TRADER_NAME not like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameIn(List<String> values) {
            addCriterion("TRADER_NAME in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotIn(List<String> values) {
            addCriterion("TRADER_NAME not in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameBetween(String value1, String value2) {
            addCriterion("TRADER_NAME between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_NAME not between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIsNull() {
            addCriterion("TRADER_CONTACT_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIsNotNull() {
            addCriterion("TRADER_CONTACT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID =", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID <>", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdGreaterThan(Integer value) {
            addCriterion("TRADER_CONTACT_ID >", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID >=", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdLessThan(Integer value) {
            addCriterion("TRADER_CONTACT_ID <", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID <=", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_ID in", values, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_ID not in", values, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_ID between", value1, value2, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_ID not between", value1, value2, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIsNull() {
            addCriterion("TRADER_CONTACT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIsNotNull() {
            addCriterion("TRADER_CONTACT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME =", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME <>", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_NAME >", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME >=", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLessThan(String value) {
            addCriterion("TRADER_CONTACT_NAME <", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME <=", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLike(String value) {
            addCriterion("TRADER_CONTACT_NAME like", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotLike(String value) {
            addCriterion("TRADER_CONTACT_NAME not like", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIn(List<String> values) {
            addCriterion("TRADER_CONTACT_NAME in", values, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_NAME not in", values, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_NAME between", value1, value2, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_NAME not between", value1, value2, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIsNull() {
            addCriterion("TRADER_CONTACT_MOBILE is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIsNotNull() {
            addCriterion("TRADER_CONTACT_MOBILE is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE =", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <>", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_MOBILE >", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE >=", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLessThan(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <=", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLike(String value) {
            addCriterion("TRADER_CONTACT_MOBILE like", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotLike(String value) {
            addCriterion("TRADER_CONTACT_MOBILE not like", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIn(List<String> values) {
            addCriterion("TRADER_CONTACT_MOBILE in", values, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_MOBILE not in", values, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_MOBILE between", value1, value2, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_MOBILE not between", value1, value2, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneIsNull() {
            addCriterion("TRADER_CONTACT_TELEPHONE is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneIsNotNull() {
            addCriterion("TRADER_CONTACT_TELEPHONE is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneEqualTo(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE =", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE <>", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE >", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE >=", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneLessThan(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE <", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE <=", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneLike(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE like", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneNotLike(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE not like", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneIn(List<String> values) {
            addCriterion("TRADER_CONTACT_TELEPHONE in", values, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_TELEPHONE not in", values, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_TELEPHONE between", value1, value2, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_TELEPHONE not between", value1, value2, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdIsNull() {
            addCriterion("TRADER_ADDRESS_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdIsNotNull() {
            addCriterion("TRADER_ADDRESS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdEqualTo(Integer value) {
            addCriterion("TRADER_ADDRESS_ID =", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdNotEqualTo(Integer value) {
            addCriterion("TRADER_ADDRESS_ID <>", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdGreaterThan(Integer value) {
            addCriterion("TRADER_ADDRESS_ID >", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ADDRESS_ID >=", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdLessThan(Integer value) {
            addCriterion("TRADER_ADDRESS_ID <", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ADDRESS_ID <=", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdIn(List<Integer> values) {
            addCriterion("TRADER_ADDRESS_ID in", values, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdNotIn(List<Integer> values) {
            addCriterion("TRADER_ADDRESS_ID not in", values, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ADDRESS_ID between", value1, value2, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ADDRESS_ID not between", value1, value2, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdIsNull() {
            addCriterion("TRADER_AREA_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdIsNotNull() {
            addCriterion("TRADER_AREA_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdEqualTo(Integer value) {
            addCriterion("TRADER_AREA_ID =", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdNotEqualTo(Integer value) {
            addCriterion("TRADER_AREA_ID <>", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdGreaterThan(Integer value) {
            addCriterion("TRADER_AREA_ID >", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_AREA_ID >=", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdLessThan(Integer value) {
            addCriterion("TRADER_AREA_ID <", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_AREA_ID <=", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdIn(List<Integer> values) {
            addCriterion("TRADER_AREA_ID in", values, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdNotIn(List<Integer> values) {
            addCriterion("TRADER_AREA_ID not in", values, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_AREA_ID between", value1, value2, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_AREA_ID not between", value1, value2, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIsNull() {
            addCriterion("TRADER_AREA is null");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIsNotNull() {
            addCriterion("TRADER_AREA is not null");
            return (Criteria) this;
        }

        public Criteria andTraderAreaEqualTo(String value) {
            addCriterion("TRADER_AREA =", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaNotEqualTo(String value) {
            addCriterion("TRADER_AREA <>", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaGreaterThan(String value) {
            addCriterion("TRADER_AREA >", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_AREA >=", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaLessThan(String value) {
            addCriterion("TRADER_AREA <", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaLessThanOrEqualTo(String value) {
            addCriterion("TRADER_AREA <=", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaLike(String value) {
            addCriterion("TRADER_AREA like", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaNotLike(String value) {
            addCriterion("TRADER_AREA not like", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIn(List<String> values) {
            addCriterion("TRADER_AREA in", values, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaNotIn(List<String> values) {
            addCriterion("TRADER_AREA not in", values, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaBetween(String value1, String value2) {
            addCriterion("TRADER_AREA between", value1, value2, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaNotBetween(String value1, String value2) {
            addCriterion("TRADER_AREA not between", value1, value2, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIsNull() {
            addCriterion("TRADER_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIsNotNull() {
            addCriterion("TRADER_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andTraderAddressEqualTo(String value) {
            addCriterion("TRADER_ADDRESS =", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressNotEqualTo(String value) {
            addCriterion("TRADER_ADDRESS <>", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressGreaterThan(String value) {
            addCriterion("TRADER_ADDRESS >", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_ADDRESS >=", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressLessThan(String value) {
            addCriterion("TRADER_ADDRESS <", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressLessThanOrEqualTo(String value) {
            addCriterion("TRADER_ADDRESS <=", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressLike(String value) {
            addCriterion("TRADER_ADDRESS like", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressNotLike(String value) {
            addCriterion("TRADER_ADDRESS not like", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIn(List<String> values) {
            addCriterion("TRADER_ADDRESS in", values, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressNotIn(List<String> values) {
            addCriterion("TRADER_ADDRESS not in", values, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressBetween(String value1, String value2) {
            addCriterion("TRADER_ADDRESS between", value1, value2, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressNotBetween(String value1, String value2) {
            addCriterion("TRADER_ADDRESS not between", value1, value2, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsIsNull() {
            addCriterion("TRADER_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsIsNotNull() {
            addCriterion("TRADER_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsEqualTo(String value) {
            addCriterion("TRADER_COMMENTS =", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsNotEqualTo(String value) {
            addCriterion("TRADER_COMMENTS <>", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsGreaterThan(String value) {
            addCriterion("TRADER_COMMENTS >", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_COMMENTS >=", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsLessThan(String value) {
            addCriterion("TRADER_COMMENTS <", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsLessThanOrEqualTo(String value) {
            addCriterion("TRADER_COMMENTS <=", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsLike(String value) {
            addCriterion("TRADER_COMMENTS like", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsNotLike(String value) {
            addCriterion("TRADER_COMMENTS not like", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsIn(List<String> values) {
            addCriterion("TRADER_COMMENTS in", values, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsNotIn(List<String> values) {
            addCriterion("TRADER_COMMENTS not in", values, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsBetween(String value1, String value2) {
            addCriterion("TRADER_COMMENTS between", value1, value2, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsNotBetween(String value1, String value2) {
            addCriterion("TRADER_COMMENTS not between", value1, value2, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdIsNull() {
            addCriterion("TAKE_TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdIsNotNull() {
            addCriterion("TAKE_TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ID =", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdNotEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ID <>", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdGreaterThan(Integer value) {
            addCriterion("TAKE_TRADER_ID >", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ID >=", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdLessThan(Integer value) {
            addCriterion("TAKE_TRADER_ID <", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ID <=", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_ID in", values, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdNotIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_ID not in", values, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_ID between", value1, value2, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_ID not between", value1, value2, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameIsNull() {
            addCriterion("TAKE_TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameIsNotNull() {
            addCriterion("TAKE_TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameEqualTo(String value) {
            addCriterion("TAKE_TRADER_NAME =", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_NAME <>", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameGreaterThan(String value) {
            addCriterion("TAKE_TRADER_NAME >", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_NAME >=", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameLessThan(String value) {
            addCriterion("TAKE_TRADER_NAME <", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_NAME <=", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameLike(String value) {
            addCriterion("TAKE_TRADER_NAME like", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameNotLike(String value) {
            addCriterion("TAKE_TRADER_NAME not like", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameIn(List<String> values) {
            addCriterion("TAKE_TRADER_NAME in", values, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_NAME not in", values, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_NAME between", value1, value2, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_NAME not between", value1, value2, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdIsNull() {
            addCriterion("TAKE_TRADER_CONTACT_ID is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdIsNotNull() {
            addCriterion("TAKE_TRADER_CONTACT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID =", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdNotEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID <>", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdGreaterThan(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID >", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID >=", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdLessThan(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID <", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdLessThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID <=", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_CONTACT_ID in", values, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdNotIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_CONTACT_ID not in", values, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_CONTACT_ID between", value1, value2, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_CONTACT_ID not between", value1, value2, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameIsNull() {
            addCriterion("TAKE_TRADER_CONTACT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameIsNotNull() {
            addCriterion("TAKE_TRADER_CONTACT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME =", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME <>", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameGreaterThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME >", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME >=", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameLessThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME <", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME <=", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME like", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameNotLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME not like", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_NAME in", values, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_NAME not in", values, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_NAME between", value1, value2, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_NAME not between", value1, value2, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileIsNull() {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileIsNotNull() {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE =", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE <>", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileGreaterThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE >", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE >=", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileLessThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE <", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE <=", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE like", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileNotLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE not like", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE in", values, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE not in", values, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE between", value1, value2, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE not between", value1, value2, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneIsNull() {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneIsNotNull() {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE =", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE <>", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneGreaterThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE >", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE >=", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneLessThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE <", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE <=", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE like", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneNotLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE not like", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE in", values, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE not in", values, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE between", value1, value2, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE not between", value1, value2, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdIsNull() {
            addCriterion("TAKE_TRADER_ADDRESS_ID is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdIsNotNull() {
            addCriterion("TAKE_TRADER_ADDRESS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID =", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdNotEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID <>", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdGreaterThan(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID >", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID >=", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdLessThan(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID <", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdLessThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID <=", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_ADDRESS_ID in", values, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdNotIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_ADDRESS_ID not in", values, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_ADDRESS_ID between", value1, value2, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_ADDRESS_ID not between", value1, value2, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdIsNull() {
            addCriterion("TAKE_TRADER_AREA_ID is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdIsNotNull() {
            addCriterion("TAKE_TRADER_AREA_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID =", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdNotEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID <>", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdGreaterThan(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID >", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID >=", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdLessThan(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID <", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdLessThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID <=", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_AREA_ID in", values, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdNotIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_AREA_ID not in", values, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_AREA_ID between", value1, value2, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_AREA_ID not between", value1, value2, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIsNull() {
            addCriterion("TAKE_TRADER_AREA is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIsNotNull() {
            addCriterion("TAKE_TRADER_AREA is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaEqualTo(String value) {
            addCriterion("TAKE_TRADER_AREA =", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_AREA <>", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaGreaterThan(String value) {
            addCriterion("TAKE_TRADER_AREA >", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_AREA >=", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaLessThan(String value) {
            addCriterion("TAKE_TRADER_AREA <", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_AREA <=", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaLike(String value) {
            addCriterion("TAKE_TRADER_AREA like", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaNotLike(String value) {
            addCriterion("TAKE_TRADER_AREA not like", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIn(List<String> values) {
            addCriterion("TAKE_TRADER_AREA in", values, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_AREA not in", values, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_AREA between", value1, value2, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_AREA not between", value1, value2, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIsNull() {
            addCriterion("TAKE_TRADER_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIsNotNull() {
            addCriterion("TAKE_TRADER_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressEqualTo(String value) {
            addCriterion("TAKE_TRADER_ADDRESS =", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_ADDRESS <>", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressGreaterThan(String value) {
            addCriterion("TAKE_TRADER_ADDRESS >", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_ADDRESS >=", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressLessThan(String value) {
            addCriterion("TAKE_TRADER_ADDRESS <", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_ADDRESS <=", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressLike(String value) {
            addCriterion("TAKE_TRADER_ADDRESS like", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressNotLike(String value) {
            addCriterion("TAKE_TRADER_ADDRESS not like", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIn(List<String> values) {
            addCriterion("TAKE_TRADER_ADDRESS in", values, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_ADDRESS not in", values, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_ADDRESS between", value1, value2, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_ADDRESS not between", value1, value2, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIsNull() {
            addCriterion("FREIGHT_DESCRIPTION is null");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIsNotNull() {
            addCriterion("FREIGHT_DESCRIPTION is not null");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionEqualTo(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION =", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotEqualTo(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION <>", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionGreaterThan(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION >", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionGreaterThanOrEqualTo(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION >=", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionLessThan(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION <", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionLessThanOrEqualTo(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION <=", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIn(List<Integer> values) {
            addCriterion("FREIGHT_DESCRIPTION in", values, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotIn(List<Integer> values) {
            addCriterion("FREIGHT_DESCRIPTION not in", values, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionBetween(Integer value1, Integer value2) {
            addCriterion("FREIGHT_DESCRIPTION between", value1, value2, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotBetween(Integer value1, Integer value2) {
            addCriterion("FREIGHT_DESCRIPTION not between", value1, value2, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNull() {
            addCriterion("DELIVERY_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNotNull() {
            addCriterion("DELIVERY_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE =", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE <>", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThan(Integer value) {
            addCriterion("DELIVERY_TYPE >", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE >=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThan(Integer value) {
            addCriterion("DELIVERY_TYPE <", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE <=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIn(List<Integer> values) {
            addCriterion("DELIVERY_TYPE in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotIn(List<Integer> values) {
            addCriterion("DELIVERY_TYPE not in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_TYPE between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_TYPE not between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdIsNull() {
            addCriterion("LOGISTICS_ID is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdIsNotNull() {
            addCriterion("LOGISTICS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdEqualTo(Integer value) {
            addCriterion("LOGISTICS_ID =", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdNotEqualTo(Integer value) {
            addCriterion("LOGISTICS_ID <>", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdGreaterThan(Integer value) {
            addCriterion("LOGISTICS_ID >", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("LOGISTICS_ID >=", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdLessThan(Integer value) {
            addCriterion("LOGISTICS_ID <", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdLessThanOrEqualTo(Integer value) {
            addCriterion("LOGISTICS_ID <=", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdIn(List<Integer> values) {
            addCriterion("LOGISTICS_ID in", values, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdNotIn(List<Integer> values) {
            addCriterion("LOGISTICS_ID not in", values, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdBetween(Integer value1, Integer value2) {
            addCriterion("LOGISTICS_ID between", value1, value2, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("LOGISTICS_ID not between", value1, value2, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsIsNull() {
            addCriterion("LOGISTICS_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsIsNotNull() {
            addCriterion("LOGISTICS_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsEqualTo(String value) {
            addCriterion("LOGISTICS_COMMENTS =", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsNotEqualTo(String value) {
            addCriterion("LOGISTICS_COMMENTS <>", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsGreaterThan(String value) {
            addCriterion("LOGISTICS_COMMENTS >", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("LOGISTICS_COMMENTS >=", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsLessThan(String value) {
            addCriterion("LOGISTICS_COMMENTS <", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsLessThanOrEqualTo(String value) {
            addCriterion("LOGISTICS_COMMENTS <=", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsLike(String value) {
            addCriterion("LOGISTICS_COMMENTS like", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsNotLike(String value) {
            addCriterion("LOGISTICS_COMMENTS not like", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsIn(List<String> values) {
            addCriterion("LOGISTICS_COMMENTS in", values, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsNotIn(List<String> values) {
            addCriterion("LOGISTICS_COMMENTS not in", values, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsBetween(String value1, String value2) {
            addCriterion("LOGISTICS_COMMENTS between", value1, value2, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsNotBetween(String value1, String value2) {
            addCriterion("LOGISTICS_COMMENTS not between", value1, value2, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutIsNull() {
            addCriterion("IS_PRINTOUT is null");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutIsNotNull() {
            addCriterion("IS_PRINTOUT is not null");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutEqualTo(Boolean value) {
            addCriterion("IS_PRINTOUT =", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutNotEqualTo(Boolean value) {
            addCriterion("IS_PRINTOUT <>", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutGreaterThan(Boolean value) {
            addCriterion("IS_PRINTOUT >", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutGreaterThanOrEqualTo(Boolean value) {
            addCriterion("IS_PRINTOUT >=", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutLessThan(Boolean value) {
            addCriterion("IS_PRINTOUT <", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutLessThanOrEqualTo(Boolean value) {
            addCriterion("IS_PRINTOUT <=", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutIn(List<Boolean> values) {
            addCriterion("IS_PRINTOUT in", values, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutNotIn(List<Boolean> values) {
            addCriterion("IS_PRINTOUT not in", values, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutBetween(Boolean value1, Boolean value2) {
            addCriterion("IS_PRINTOUT between", value1, value2, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutNotBetween(Boolean value1, Boolean value2) {
            addCriterion("IS_PRINTOUT not between", value1, value2, "isPrintout");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}