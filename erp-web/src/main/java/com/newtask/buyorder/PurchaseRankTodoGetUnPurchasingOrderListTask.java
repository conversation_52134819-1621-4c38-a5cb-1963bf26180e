package com.newtask.buyorder;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.page.Page;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.model.vo.ProductManagerAndAssistantIdVo;
import com.vedeng.order.dao.OrderAssistantRelationMapper;
import com.vedeng.order.model.OrderAssistantRelationDo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.service.OrgService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 供应链工作台 采购任务处理月度排名 获取待采购订单数据，由于待采购数据计算量较大，从实时查询变成定时任务查询
 * @date 2023/8/30 16:00
 */
@Slf4j
@Component
@JobHandler(value = "purchaseRankTodoGetUnPurchasingOrderListTask")
public class PurchaseRankTodoGetUnPurchasingOrderListTask extends AbstractJobHandler {

    @Autowired
    private BuyorderService buyorderService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private OrderAssistantRelationMapper orderAssistantRelationMapper;
    @Autowired
    private UserMapper userMapper;

    private final String REDIS_KEY = "ERP:BUYORDER:TODO:UN_PURCHASING_ORDER";

    ///**
    // * 采购任务处理月度排名 以及部门id
    // */
    //@Value("purchase_rank_todo_root_department_id")
    //private Integer purchaseRankingTodoRootDepartmentId;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        try {
            GoodsVo goodsVo = this.assembleGetPreGetUnPurchasingOrderParameter();
            int pageNo = 1;
            int pageSize = 10;
            Page page = Page.newBuilder(pageNo, pageSize, null);

            Map<String, Object> map = new HashMap<>(4);
            int buySum = 0;
            List<SaleorderVo> list = new ArrayList<>();

            // 预处理查看是否有数据，有则加入已有数据，无则返回并存入redis
            while (true) {
                // 判断是否有数据
                boolean isHasData = this.getPreGetUnPurchasingOrder(goodsVo, page);
                if (!isHasData) {
                    break;
                }
                Map<String, Object> nextMap = buyorderService.getUnPurchasingOrderList(goodsVo, page);
                List<SaleorderVo> newSaleOrderVoList = (List<SaleorderVo>) nextMap.get("list");
                int nextBuySum = (Integer) nextMap.get("buySum");
                buySum += nextBuySum;
                list.addAll(newSaleOrderVoList);
                page.setPageNo(pageNo++);
                log.info("供应链工作台采购任务处理月度排名:获取待采购订单数据进度:当前页数{},当前数据量{}", pageNo, list.size());
            }

            map.put("buySum", buySum);
            map.put("list", list);
            map.put("page", page);

            RedisUtil.HashOps.hPutAll(REDIS_KEY, map);
        } catch (Exception e) {
            log.error("供应链工作台:采购任务处理月度排名 获取待采购订单数据异常", e);
            throw e;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 组装获取待采购数据参数
     *
     * @return GoodsVo GoodsVo
     */
    private GoodsVo assembleGetPreGetUnPurchasingOrderParameter() {
        //获取供应链管理部所有数据
        List<Organization> orgList = orgService.getChildrenOrgByParentId(6, 1);
        List<Integer> orgaIdList = orgList.stream().map(Organization::getOrgId).collect(Collectors.toList());
        List<Integer> orderAssistantIdList = userMapper.getOrderAsisitIdByOrgId(orgaIdList);

        GoodsVo goodsVo = new GoodsVo();
        goodsVo.setComponentId(2);
        goodsVo.setOrderAsistIdList(orderAssistantIdList);
        goodsVo.setOrderType(1);
        List<OrderAssistantRelationDo> orderAssistantRelationDos = orderAssistantRelationMapper.getBingdedInfoByOrderAssIds(orderAssistantIdList);
        List<ProductManagerAndAssistantIdVo> productManagerAndAssistantIdVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderAssistantRelationDos)) {
            orderAssistantRelationDos.forEach(item -> {
                ProductManagerAndAssistantIdVo productManagerAndAssistantIdVo = new ProductManagerAndAssistantIdVo();
                productManagerAndAssistantIdVo.setProductAssistantUserId(item.getProductAssitantUserId());
                productManagerAndAssistantIdVo.setProductManagerUserId(item.getProductManagerUserId());
                productManagerAndAssistantIdVoList.add(productManagerAndAssistantIdVo);
            });
        } else {
            ProductManagerAndAssistantIdVo productManagerAndAssistantIdVo = new ProductManagerAndAssistantIdVo();
            productManagerAndAssistantIdVoList.add(productManagerAndAssistantIdVo);
        }
        goodsVo.setProductManagerAndAssistantIdVoList(productManagerAndAssistantIdVoList);
        return goodsVo;
    }

    /**
     * 是否还存在数据
     * @param goodsVo goodsVo
     * @param page page
     * @return 是否存在数据  true:存在 false:不存在
     * @throws Exception
     */
    private boolean getPreGetUnPurchasingOrder(GoodsVo goodsVo, Page page) throws Exception {
        Map<String, Object> nextMap = buyorderService.getUnPurchasingOrderList(goodsVo, page);
        return CollUtil.isNotEmpty((List<SaleorderVo>) nextMap.get("list"));
    }


}
