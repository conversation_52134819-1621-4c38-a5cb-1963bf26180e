<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsLogicalOrdergoodsMapper" >
  <resultMap id="BaseResultMap" type="com.wms.model.po.WmsLogicalOrdergoods" >
    <!--          -->
    <id column="LOGICAL_ORDER_GOODS_ID" property="logicalOrderGoodsId" jdbcType="INTEGER" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="LOGICAL_WAREHOUSE_ID" property="logicalWarehouseId" jdbcType="INTEGER" />
    <result column="OCCUPY_NUM" property="occupyNum" jdbcType="INTEGER" />
    <result column="DELIVERY_NUM" property="deliveryNum" jdbcType="INTEGER" />
    <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="MODE_TIME" property="modeTime" jdbcType="TIMESTAMP" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    LOGICAL_ORDER_GOODS_ID, RELATED_ID, OPERATE_TYPE, SKU,GOODS_ID, NUM, LOGICAL_WAREHOUSE_ID,
    OCCUPY_NUM, DELIVERY_NUM, ARRIVAL_NUM, ADD_TIME, MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select
    <include refid="Base_Column_List" />
    from V_WMS_LOGICAL_ORDER_GOODS
    where LOGICAL_ORDER_GOODS_ID = #{logicalOrderGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from V_WMS_LOGICAL_ORDER_GOODS
    where LOGICAL_ORDER_GOODS_ID = #{logicalOrderGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.wms.model.po.WmsLogicalOrdergoods" >
    <!--          -->
    insert into V_WMS_LOGICAL_ORDER_GOODS (LOGICAL_ORDER_GOODS_ID, RELATED_ID, OPERATE_TYPE,
      SKU,GOODS_ID, NUM, LOGICAL_WAREHOUSE_ID,
      OCCUPY_NUM, DELIVERY_NUM, ARRIVAL_NUM,
      ADD_TIME, MODE_TIME, IS_DELETE,
      CREATOR, UPDATER)
    values (#{logicalOrderGoodsId,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER}, #{operateType,jdbcType=TINYINT},
      #{sku,jdbcType=VARCHAR},#{goodsId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER}, #{logicalWarehouseId,jdbcType=INTEGER},
      #{occupyNum,jdbcType=INTEGER}, #{deliveryNum,jdbcType=INTEGER}, #{arrivalNum,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BIT},
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.wms.model.po.WmsLogicalOrdergoods" useGeneratedKeys="true" keyProperty="logicalOrderGoodsId">
    <!--          -->
    insert into V_WMS_LOGICAL_ORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logicalOrderGoodsId != null" >
        LOGICAL_ORDER_GOODS_ID,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="logicalWarehouseId != null" >
        LOGICAL_WAREHOUSE_ID,
      </if>
      <if test="occupyNum != null" >
        OCCUPY_NUM,
      </if>
      <if test="deliveryNum != null" >
        DELIVERY_NUM,
      </if>
      <if test="arrivalNum != null" >
        ARRIVAL_NUM,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modeTime != null" >
        MODE_TIME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="sku != null" >
        SKU,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logicalOrderGoodsId != null" >
        #{logicalOrderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
      <if test="logicalWarehouseId != null" >
        #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null" >
        #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null" >
        #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null" >
        #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
       #{sku,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsLogicalOrdergoods" >
    <!--          -->
    update V_WMS_LOGICAL_ORDER_GOODS
    <set >
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="goodsId != null" >
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="logicalWarehouseId != null" >
        LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null" >
        OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null" >
        DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="arrivalNum != null" >
        ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null" >
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where LOGICAL_ORDER_GOODS_ID = #{logicalOrderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.model.po.WmsLogicalOrdergoods" >
    <!--          -->
    update V_WMS_LOGICAL_ORDER_GOODS
    set RELATED_ID = #{relatedId,jdbcType=INTEGER},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where LOGICAL_ORDER_GOODS_ID = #{logicalOrderGoodsId,jdbcType=INTEGER}
  </update>

  <update id="updateIsdelte">
     update V_WMS_LOGICAL_ORDER_GOODS
     SET IS_DELETE = 1
     WHERE OPERATE_TYPE =  #{operateType,jdbcType=TINYINT}
     AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
  </update>
  <select id="getLogicalOrderInfoGroupByRelateId" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
	*
    FROM
	V_WMS_LOGICAL_ORDER_GOODS A
    WHERE
	A.OPERATE_TYPE = #{operateType,jdbcType=TINYINT}
	AND A.IS_DELETE = 0
	AND A.RELATED_ID IN
	<foreach collection="list" index="index" item="item" separator=","  open="(" close=")">
    #{item,jdbcType=INTEGER}
  </foreach>
    GROUP BY A.RELATED_ID
  </select>
  <update id="updateOccupyNum">
      UPDATE V_WMS_LOGICAL_ORDER_GOODS
      <set >
        <if test="operateType != null" >
          OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
        </if>
        <if test="goodsId != null" >
          GOODS_ID = #{goodsId,jdbcType=INTEGER},
        </if>
        <if test="num != null" >
          NUM = NUM + #{num,jdbcType=INTEGER},
        </if>
        <if test="logicalWarehouseId != null" >
          LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
        </if>
        <if test="occupyNum != null" >
          OCCUPY_NUM = OCCUPY_NUM + #{occupyNum,jdbcType=INTEGER},
        </if>
        <if test="deliveryNum != null" >
          DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
        </if>
        <if test="arrivalNum != null" >
          ARRIVAL_NUM = #{arrivalNum,jdbcType=INTEGER},
        </if>
        <if test="addTime != null" >
          ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        </if>
        <if test="modeTime != null" >
          MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
        </if>
        <if test="isDelete != null" >
          IS_DELETE = #{isDelete,jdbcType=BIT},
        </if>
        <if test="creator != null" >
          CREATOR = #{creator,jdbcType=INTEGER},
        </if>
        <if test="updater != null" >
          UPDATER = #{updater,jdbcType=INTEGER},
        </if>
      </set>
        WHERE
            RELATED_ID = #{relatedId,jdbcType=INTEGER}
            AND OPERATE_TYPE = #{operateType,jdbcType=TINYINT}
    </update>

  <select id="getorderChooseInfoByOptType" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
	A.LOGICAL_ORDER_GOODS_ID,A.RELATED_ID, B.DELIVERY_NUM
    <if test="optType == 1">
      ,B.ARRIVAL_NUM
    </if>
    FROM
	V_WMS_LOGICAL_ORDER_GOODS A
	<if test="optType == 0">
	LEFT JOIN T_SALEORDER_GOODS B ON A.RELATED_ID = B.SALEORDER_GOODS_ID
    </if>
    <if test="optType == 1">
      LEFT JOIN T_AFTER_SALES_GOODS B ON A.RELATED_ID=B.AFTER_SALES_GOODS_ID
    </if>
    WHERE
	A.IS_DELETE=0 AND A.OPERATE_TYPE=#{optType}

  </select>
    <select id="getSaleorderLogicalChooseInfoByNo" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
	A.*,B.SPECIAL_DELIVERY
    FROM
	V_WMS_LOGICAL_ORDER_GOODS A
	LEFT JOIN T_SALEORDER_GOODS B ON A.RELATED_ID = B.SALEORDER_GOODS_ID
	LEFT JOIN T_SALEORDER C ON B.SALEORDER_ID = C.SALEORDER_ID
    WHERE
	A.OPERATE_TYPE = 0
    AND C.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
    AND A.IS_DELETE = 0
    AND B.DELIVERY_STATUS !=2
    AND A.RELATED_ID NOT IN (
      SELECT AFG.ORDER_DETAIL_ID
      FROM
        T_AFTER_SALES AF
          LEFT JOIN T_AFTER_SALES_GOODS AFG ON AF.AFTER_SALES_ID = AFG.AFTER_SALES_ID AND AFG.ORDER_DETAIL_ID > 0
      WHERE
        AF.TYPE = 539
        AND AF.ORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
        AND AF.ATFER_SALES_STATUS IN (0,1)
    )
    </select>

  <select id="getOrderByCon" parameterType="com.wms.model.po.WmsLogicalOrdergoods" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
      <include refid="Base_Column_List"/>
    FROM
      V_WMS_LOGICAL_ORDER_GOODS
    WHERE IS_DELETE=0
        and SKU = #{sku,jdbcType=VARCHAR}
        and RELATED_ID = #{relatedId,jdbcType=INTEGER}
        and OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
        and LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER}
  </select>

  <select id="getOutOrderByCon" parameterType="com.wms.model.po.WmsLogicalOrdergoods" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    V_WMS_LOGICAL_ORDER_GOODS
    WHERE IS_DELETE=0
    and SKU = #{sku,jdbcType=VARCHAR}
    and RELATED_ID = #{relatedId,jdbcType=INTEGER}
    and OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
    and LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER}
    and OCCUPY_NUM > 0
  </select>

    <select id="getLogicalInfoByTypeAndRelate" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
	*
    FROM
	V_WMS_LOGICAL_ORDER_GOODS A
    WHERE
	A.OPERATE_TYPE = #{operateType,jdbcType=TINYINT}
	AND A.IS_DELETE = 0
	AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>

  <select id="getAfterSaleRelateLogicOrderGood" parameterType="java.util.Map" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT *
      FROM V_WMS_LOGICAL_ORDER_GOODS a
    INNER JOIN T_AFTER_SALES_GOODS b ON a.RELATED_ID=b.AFTER_SALES_GOODS_ID
    INNER JOIN T_AFTER_SALES c ON c.AFTER_SALES_ID = b.AFTER_SALES_ID
    where a.IS_DELETE=0
        and c.AFTER_SALES_ID = #{afterSaleId,jdbcType=INTEGER}
        and a.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
  </select>
  <update id="updateLogicalNum">
     UPDATE V_WMS_LOGICAL_ORDER_GOODS SET
     OCCUPY_NUM = OCCUPY_NUM + #{occupyNum,jdbcType=INTEGER},
        DELIVERY_NUM = DELIVERY_NUM + #{deliveryNum,jdbcType=INTEGER}
        WHERE  LOGICAL_ORDER_GOODS_ID = #{logicalOrderGoodsId,jdbcType=INTEGER}
  </update>

  <select id="getAfterorderLogicalChooseInfoByNo" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
	A.*
    FROM
	V_WMS_LOGICAL_ORDER_GOODS A
	LEFT JOIN T_AFTER_SALES_GOODS B ON A.RELATED_ID = B.AFTER_SALES_GOODS_ID
	LEFT JOIN T_AFTER_SALES C ON B.AFTER_SALES_ID = C.AFTER_SALES_ID
    WHERE
	A.OPERATE_TYPE = 1
    AND C.AFTER_SALES_NO = #{afterNo,jdbcType=VARCHAR}
    AND A.IS_DELETE=0
  </select>
  <update id="updateSaleIsdelte">
    update V_WMS_LOGICAL_ORDER_GOODS
     SET IS_DELETE = 1
     WHERE OPERATE_TYPE = 0 AND OCCUPY_NUM=0
  </update>
    <update id="updateAfterIsdelte">
      update V_WMS_LOGICAL_ORDER_GOODS
     SET IS_DELETE = 1
     WHERE OPERATE_TYPE = 1 AND OCCUPY_NUM=0
    </update>
  <select id="getCloseOrderErrorOccupy" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
      C.SALEORDER_ID,
      C.SALEORDER_NO,
      C.STATUS,
      C.COMPANY_ID,
      C.ACTION_ID,
      B.SALEORDER_GOODS_ID,
      B.SKU,
      A.OCCUPY_NUM,
      A.LOGICAL_WAREHOUSE_ID ,
      A.LOGICAL_ORDER_GOODS_ID
    FROM
      V_WMS_LOGICAL_ORDER_GOODS A
        LEFT JOIN T_SALEORDER_GOODS B ON A.RELATED_ID = B.SALEORDER_GOODS_ID
        LEFT JOIN T_SALEORDER C ON B.SALEORDER_ID = C.SALEORDER_ID
    WHERE
      A.OPERATE_TYPE = 0
      AND C.`STATUS` IN ( 2, 3 )
      AND A.IS_DELETE = 0
      AND A.OCCUPY_NUM > 0
  </select>
  <select id="getorderErrorOccupy" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
    C.SALEORDER_ID,
    C.SALEORDER_NO,
    B.SALEORDER_GOODS_ID,
    A.LOGICAL_ORDER_GOODS_ID,
    A.SKU,C.`STATUS`,C.ACTION_ID,
    FROM_UNIXTIME( C.ADD_TIME / 1000 ) ADDTIME,
    B.NUM -IFNULL( B.AFTER_RETURN_NUM, 0 ) NUM,
    (B.NUM - IFNULL( B.AFTER_RETURN_NUM, 0 )- B.DELIVERY_NUM) LAST_NUM,
    SUM( A.OCCUPY_NUM ) ALLOCCUPYNUM,
    A.LOGICAL_WAREHOUSE_ID
    FROM
    V_WMS_LOGICAL_ORDER_GOODS A
    LEFT JOIN T_SALEORDER_GOODS B ON A.RELATED_ID = B.SALEORDER_GOODS_ID
    LEFT JOIN T_SALEORDER C ON B.SALEORDER_ID = C.SALEORDER_ID
    WHERE
    A.OPERATE_TYPE = 0
    AND A.IS_DELETE = 0
    AND A.IS_DELETE = 0
    AND A.OCCUPY_NUM > 0
    GROUP BY
    A.LOGICAL_ORDER_GOODS_ID
    HAVING LAST_NUM <![CDATA[<]]> ALLOCCUPYNUM AND LAST_NUM = 0
  </select>

  <select id="getAfterorderLogicalChooseInfo" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    SELECT
      A.*
    FROM
      V_WMS_LOGICAL_ORDER_GOODS A
        LEFT JOIN T_AFTER_SALES_GOODS B ON A.RELATED_ID = B.AFTER_SALES_GOODS_ID
        LEFT JOIN T_AFTER_SALES C ON B.AFTER_SALES_ID = C.AFTER_SALES_ID
    WHERE
      A.OPERATE_TYPE = #{operaType,jdbcType=INTEGER}
      AND C.AFTER_SALES_ID = #{afterSalesId,jdbcType=VARCHAR}
      AND A.IS_DELETE=0
      AND A.OCCUPY_NUM > 0
  </select>

  <select id="selectUnitConversionData" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    select VWLOG.*
    from V_WMS_LOGICAL_ORDER_GOODS VWLOG left join T_WMS_UNIT_CONVERSION_ORDER_ITEM TWUCOI on VWLOG.RELATED_ID =
    TWUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID
    where TWUCOI.WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionId} and VWLOG.OPERATE_TYPE = #{operateType}
    and VWLOG.IS_DELETE=0
  </select>

  <select id="selectByRelateIdAndOperateType" resultType="com.wms.model.po.WmsLogicalOrdergoods">
    select * from V_WMS_LOGICAL_ORDER_GOODS where OPERATE_TYPE = #{operateType,jdbcType=INTEGER} and RELATED_ID = #{relateId,jdbcType=INTEGER} and IS_DELETE = 0
  </select>
</mapper>
