package com.vedeng.dwh.service;

import com.vedeng.dwh.model.dto.DwhErpOrganizationDto;
import com.vedeng.dwh.model.dto.DwhErpSubDeptDto;
import com.vedeng.dwh.model.dto.DwhErpUserDto;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * dwh库通用查询工具.
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/26 1:41 下午.
 * @author: Tomcat.Hui.
 */
public interface DwhQueryService {

    /**
     * 查询指定时间点的快照数据.
     * @jira: .
     * @notes: if start = end 则查询出来的数据唯一.
     * @version: 1.0.
     * @date: 2020/11/2 11:00 上午.
     * @author: Tomcat.Hui.
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhErpOrganizationDto>.
     * @throw: .
     */
    List<DwhErpOrganizationDto> getAllDeptInfo(Timestamp startTime,Timestamp endTime);

    /**
     * 获取全量部门的快照配置(包含销售信息).
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/3 7:55 下午.
     * @author: Tomcat.Hui.
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.Map<com.vedeng.dwh.model.dto.DwhErpSubDept,java.util.Map<com.vedeng.dwh.model.dto.DwhErpSubDept,java.util.List<com.vedeng.dwh.model.dto.DwhErpSubDept>>>.
     * @throw: .
     */
    Map<DwhErpSubDeptDto, Map<DwhErpSubDeptDto,List<DwhErpSubDeptDto>>> getFoldDeptsUsers(Timestamp startTime, Timestamp endTime);

    /**
     * 获取用户指定时间快照部门信息.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/2 6:00 下午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @param startTime: .
     * @param endTime: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhErpOrganizationDto>.
     * @throw: .
     */
    DwhErpUserDto getUserDeptInfo(Integer userId, Timestamp startTime, Timestamp endTime);

    List<DwhErpOrganizationDto> getBaseOrganizationInfo(Timestamp startTime, Timestamp endTime);

    List<DwhErpUserDto> getBaseUserInfo(Timestamp startTime, Timestamp endTime);

}
