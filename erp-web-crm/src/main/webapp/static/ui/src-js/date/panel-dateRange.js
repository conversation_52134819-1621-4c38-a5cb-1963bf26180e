// 日期面板
Vue.component('ui-date-range-panel', {
    template: `
        <div class="vd-ui-daterange-panel" :style="{ width: shortcuts && shortcuts.length ? '685px': '588px' }">

            <div class="vd-ui-panel__body-wrapper">

                <!-- 快捷时间 -->
                <slot name="sidebar" class="el-picker-panel__sidebar"></slot>
                <div class="vd-ui-panel__sidebar" v-if="shortcuts && shortcuts.length">
                    <button
                        class="vd-ui-panel__shortcut"
                        v-for="(shortcut, key) in shortcuts"
                        :key="key"
                        @click="handleShortcutClick(shortcut)">{{ shortcut.text }}</button>
                </div>

                <!-- 日期面板 -->
                <div class="vd-ui-panel__body" :style="{ marginLeft: shortcuts && shortcuts.length ? '100px' : '0' }">

                    <div class="el-picker-panel__content is-left">

                        <div class="el-date-range-picker__header">
                            <button
                                @click="leftPrevYear"
                                class="arrow arrow1"
                                label="前一年">
                                <i class="vd-ui_icon icon-slide-up"></i>
                            </button>
                            <button
                                @click="leftPrevMonth"
                                class="arrow arrow2"
                                label="上个月">
                                <i class="vd-ui_icon icon-app-left"></i>
                            </button>
                            <div class="header-label" label="开始日期">{{ leftLabel }}</div>
                            <button 
                                v-if="unlinkPanels"
                                :disabled="!enableMonthArrow"
                                @click="leftNextMonth"
                                :class="{ 'disable': !enableMonthArrow }"
                                class="arrow arrow3"
                                label="下个月">
                                <i class="vd-ui_icon icon-app-right"></i>
                            </button>
                            <button 
                                v-if="unlinkPanels"
                                :disabled="!enableYearArrow"
                                @click="leftNextYear"
                                :class="{ 'disable': !enableYearArrow }"
                                class="arrow arrow4"
                                label="后一年">
                                <i class="vd-ui_icon icon-slide-up"></i>
                            </button>
                        </div>

                        <ui-date-table
                            selection-mode="range"
                            :date="leftDate"
                            :default-value="defaultValue"

                            :min-date="minDate"
                            :max-date="maxDate"
                            :range-state="rangeState"

                            :disabled-date="disabledDate"
                            :cell-class-name="cellClassName"
                            @changerange="handleChangeRange"
                            :firstDayOfWeek="firstDayOfWeek"
                            @pick="handleRangePick"
                        ></ui-date-table>

                    </div>

                    <div class="el-picker-panel__content is-right">
                        <div class="el-date-range-picker__header">

                            <button
                                v-if="unlinkPanels"
                                :disabled="!enableYearArrow" 
                                @click="rightPrevYear"
                                :class="{ 'disable': !enableYearArrow }"
                                class="arrow arrow1"
                                label="前一年">
                                <i class="vd-ui_icon icon-slide-up"></i>
                            </button>
                            <button
                                v-if="unlinkPanels" 
                                :disabled="!enableMonthArrow"
                                @click="rightPrevMonth"
                                :class="{ 'disable': !enableMonthArrow }"
                                class="arrow arrow2"
                                label="上个月">
                                <i class="vd-ui_icon icon-app-left"></i>
                            </button>
                            <div class="header-label" label="结束日期">{{ rightLabel }}</div>
                            <button 
                                @click="rightNextMonth"
                                class="arrow arrow3"
                                label="下个月">
                                <i class="vd-ui_icon icon-app-right"></i>
                            </button>
                            <button 
                                @click="rightNextYear"
                                class="arrow arrow4"
                                label="后一年">
                                <i class="vd-ui_icon icon-slide-up"></i>
                            </button>
                        </div>

                        <ui-date-table
                            selection-mode="range"
                            :date="rightDate"
                            :default-Value="defaultValue"

                            :min-date="minDate"
                            :max-date="maxDate"
                            :range-state="rangeState"

                            :disabled-date="disabledDate"
                            :cell-class-name="cellClassName"
                            @changerange="handleChangeRange"
                            :firstDayOfWeek="firstDayOfWeek"
                            @pick="handleRangePick"
                        ></ui-date-table>

                    </div>

                </div>

            </div>

        </div>
    `,

    props: {
        value: Array,
        firstDayOfWeek: {
            default: 1,
            type: Number,
            validator: val => val >= 1 && val <= 7
        },
        disabledDate: {},
        cellClassName: {},

        unlinkPanels: Boolean,  // 取消两个面板之间的联动
        // 快捷时间
        shortcuts: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    computed: {
        leftLabel () {
            return `${ this.leftDate.getFullYear() } 年 ${ this.leftDate.getMonth() + 1 } 月`;
        },
        rightLabel () {
            return `${ this.rightDate.getFullYear() } 年 ${ this.rightDate.getMonth() + 1 } 月`;
        },
        leftYear () {
            return this.leftDate.getFullYear();
        },
        leftMonth () {
            return this.leftDate.getMonth();
        },
        rightYear () {
            return this.rightDate.getFullYear();
        },
        rightMonth () {
            return this.rightDate.getMonth();
        },

        enableMonthArrow () {
            const nextMonth = (this.leftMonth + 1) % 12;
            const yearOffset = this.leftMonth + 1 >= 12 ? 1 : 0;
            return this.unlinkPanels && new Date(this.leftYear + yearOffset, nextMonth) < new Date(this.rightYear, this.rightMonth);
        },
        enableYearArrow () {
            return this.unlinkPanels && this.rightYear * 12 + this.rightMonth - (this.leftYear * 12 + this.leftMonth + 1) >= 12;
        }

    },
    data () {
        return {
            defaultValue: null,

            minDate: '',
            maxDate: '',
            leftDate: new Date(),
            rightDate: util_date.nextMonth(new Date()),
            rangeState: {
                endDate: null,
                selecting: false,
                row: null,
                column: null
            },

        }
    },
    watch: {
        value: {
            handler (newVal) {
                if (!newVal) {
                    this.minDate = null;
                    this.maxDate = null;
                } else if (Array.isArray(newVal)) {
                    this.minDate = util_date.isDate(newVal[0]) ? new Date(newVal[0]) : null;
                    this.maxDate = util_date.isDate(newVal[1]) ? new Date(newVal[1]) : null;
                    if (this.minDate) {
                        this.leftDate = this.minDate;
                        if (this.unlinkPanels && this.maxDate) {
                            const minDateYear = this.minDate.getFullYear();
                            const minDateMonth = this.minDate.getMonth();
                            const maxDateYear = this.maxDate.getFullYear();
                            const maxDateMonth = this.maxDate.getMonth();

                            this.rightDate = minDateYear === maxDateYear && minDateMonth === maxDateMonth
                                ? util_date.nextMonth(this.maxDate)
                                : this.maxDate;
                        }
                        else {
                            this.rightDate = util_date.nextMonth(this.leftDate);
                        }
                    }
                    else {
                        this.leftDate = this.calcDefaultValue(this.defaultValue)[0];
                        this.rightDate = util_date.nextMonth(this.leftDate);
                    }
                }
            },
            immediate: true
        },
        defaultValue (val) {
            if (!Array.isArray(this.value)) {
                const [left, right] = this.calcDefaultValue(val);
                this.leftDate = left;
                this.rightDate = val && val[1] && this.unlinkPanels
                    ? right
                    : util_date.nextMonth(this.leftDate);
            }
        },
    },
    mounted () {
    },
    methods: {
        // 计算默认值
        calcDefaultValue (defaultValue) {
            if (Array.isArray(defaultValue)) {
                return [new Date(defaultValue[0]), new Date(defaultValue[1])];
            } else if (defaultValue) {
                return [new Date(defaultValue), util_date.nextDate(new Date(defaultValue), 1)];
            } else {
                return [new Date(), util_date.nextDate(new Date(), 1)];
            }
        },

        handleChangeRange (val) {
            this.minDate = val.minDate;
            this.maxDate = val.maxDate;
            this.rangeState = val.rangeState;
        },

        handleShortcutClick (shortcut) {
            if (shortcut.onClick) {
                shortcut.onClick(this);
            }
        },

        // 范围值change
        handleRangePick (val, close = true) {
            const minDate = val.minDate;
            const maxDate = val.maxDate;

            if (this.maxDate === maxDate && this.minDate === minDate) {
                return;
            }

            this.maxDate = maxDate;
            this.minDate = minDate;

            setTimeout(() => {
                this.maxDate = maxDate;
                this.minDate = minDate;
            }, 10);
            if (!close ) return;
            this.handleConfirm();
        },
        handleConfirm () {
            if (this.isValidValue([this.minDate, this.maxDate])) {
                this.$emit('pick', [this.minDate, this.maxDate], true);  // true:关闭选择面板
            }
        },
        isValidValue (value) {
            return Array.isArray(value) &&
                value && value[0] && value[1] &&
                util_date.isDate(value[0]) && util_date.isDate(value[1]) &&
                value[0].getTime() <= value[1].getTime() && (
                    typeof this.disabledDate === 'function'
                        ? !this.disabledDate(value[0]) && !this.disabledDate(value[1])
                        : true
                );
        },

        leftPrevYear () {
            this.leftDate = util_date.prevYear(this.leftDate);
            if (!this.unlinkPanels) {
                this.rightDate = util_date.nextMonth(this.leftDate);
            }
        },
        leftPrevMonth () {
            this.leftDate = util_date.prevMonth(this.leftDate);
            if (!this.unlinkPanels) {
                this.rightDate = util_date.nextMonth(this.leftDate);
            }
        },
        rightNextMonth () {
            if (!this.unlinkPanels) {
                this.leftDate = util_date.nextMonth(this.leftDate);
                this.rightDate = util_date.nextMonth(this.leftDate);
            } else {
                this.rightDate = util_date.nextMonth(this.rightDate);
            }
        },
        rightNextYear () {
            if (!this.unlinkPanels) {
                this.leftDate = util_date.nextYear(this.leftDate);
                this.rightDate = util_date.nextMonth(this.leftDate);
            } else {
                this.rightDate = util_date.nextYear(this.rightDate);
            }
        },

        leftNextYear () {
            this.leftDate = util_date.nextYear(this.leftDate);
        },
        leftNextMonth () {
            this.leftDate = util_date.nextMonth(this.leftDate);
        },
        rightPrevYear () {
            this.rightDate = util_date.prevYear(this.rightDate);
        },
        rightPrevMonth () {
            this.rightDate = util_date.prevMonth(this.rightDate);
        },
        

    }
})