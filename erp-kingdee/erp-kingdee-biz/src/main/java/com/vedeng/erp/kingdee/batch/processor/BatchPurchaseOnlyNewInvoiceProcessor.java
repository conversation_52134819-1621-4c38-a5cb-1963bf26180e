package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderGoodsDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchROldInvoiceJNewInvoiceDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatPlainInvoiceMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatPlainInvoiceConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatSpecialInvoiceConvertor;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购仅退票 新蓝票挂到 原始应付单上
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseOnlyNewInvoiceProcessor implements ItemProcessor<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto> {

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;


    @Autowired
    private KingDeePurchaseVatSpecialInvoiceMapper kingDeePurchaseVatSpecialInvoiceMapper;

    @Autowired
    private KingDeePurchaseVatPlainInvoiceMapper kingDeePurchaseVatPlainInvoiceMapper;

    @Autowired
    private KingDeePurchaseVatSpecialInvoiceConvertor kingDeePurchaseVatSpecialInvoiceConvertor;

    @Autowired
    private KingDeePurchaseVatPlainInvoiceConvertor kingDeePurchaseVatPlainInvoiceConvertor;

    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;


    @Autowired
    private BatchROldInvoiceJNewInvoiceDtoMapper batchROldInvoiceJNewInvoiceDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;


    @Override
    public KingDeePayCommonAndInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("BatchPurchaseOnlyNewInvoiceProcessorService 处理采购单仅退票新蓝票有效票{}" , JSON.toJSONString(batchInvoiceDto));


        List<BatchInvoiceDetailDto> batchInvoiceDetailDtoList = batchInvoiceDetailDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        if (CollUtil.isEmpty(batchInvoiceDetailDtoList)) {

            log.error("当前采购单仅退票新蓝票id:{},未查到发票明细",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单仅退票新蓝票id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未查到发票明细");
        }
        List<BatchROldInvoiceJNewInvoiceDto> byNewInvoiceId = batchROldInvoiceJNewInvoiceDtoMapper.findByNewInvoiceId(batchInvoiceDto.getInvoiceId());

        if (CollUtil.isEmpty(byNewInvoiceId)) {

            log.error("当前采购单仅退新蓝字有效票id:{},未查到原始蓝票信息",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单仅退新蓝字有效票id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未查到原始蓝票信息");
        }

        List<BatchBuyorderGoodsDto> batchBuyorderGoodsDtos = batchBuyorderGoodsDtoMapper.selectByBuyorderIdNotDelete(batchInvoiceDto.getRelatedId());
        if (CollUtil.isEmpty(batchBuyorderGoodsDtos)) {
            log.error("当前采购单仅退新蓝字有效票id:{},未查到采购商品信息",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单仅退新蓝字有效票id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未查到采购商品信息");
        }
        Map<Integer, BatchBuyorderGoodsDto> batchBuyorderGoods2Map = batchBuyorderGoodsDtos.stream().collect(Collectors.toMap(BatchBuyorderGoodsDto::getBuyorderGoodsId, c -> c, (k1, k2) -> k1));
        List<String> blueInvoiceId = byNewInvoiceId.stream().map(c -> c.getOldInvoiceId().toString()).distinct().collect(Collectors.toList());
        List<Integer> blueInvoiceIds = byNewInvoiceId.stream().map(BatchROldInvoiceJNewInvoiceDto::getOldInvoiceId).distinct().collect(Collectors.toList());

        boolean isSpecial = StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains("专用发票");
        // 发票类型 1电票 2 纸票
        String QZOK_FPLX = Objects.isNull(batchInvoiceDto.getInvoiceProperty()) ? "2" : batchInvoiceDto.getInvoiceProperty().equals(1) ? "2" : "1";
        DecimalFormat decimalFormat = new DecimalFormat(("0.00#"));
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : isSpecial ? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)) : "0.00";
        BigDecimal taxRateNum = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO : isSpecial ? batchInvoiceDto.getRatio() : BigDecimal.ZERO;
        KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto = new KingDeePayCommonAndInvoiceDto();
        BatchAfterSalesDto byAfterSalesId = batchAfterSalesDtoMapper.findByAfterSalesId(batchInvoiceDto.getAfterSalesId());
        if (Objects.nonNull(byAfterSalesId)) {
            batchInvoiceDto.setAfterSalesNo(byAfterSalesId.getAfterSalesNo());
        }
        if (isSpecial) {
            // 过滤未认证的专票
            if (!batchInvoiceDto.getIsAuth().equals(1)) {
                log.info("新蓝票专票id:{}需要认证",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
                return null;
            }
            kingDeePayCommonAndInvoiceDto.setSpecial(true);

            List<KingDeePurchaseVatSpecialInvoiceEntity> byFQzokBddjtid = kingDeePurchaseVatSpecialInvoiceMapper.findByFQzokBddjtid(blueInvoiceId);
            List<KingDeePurchaseVatPlainInvoiceEntity> pupiao = kingDeePurchaseVatPlainInvoiceMapper.findByFQzokBddjtid(blueInvoiceId);

            if (CollUtil.isEmpty(byFQzokBddjtid)&&CollUtil.isEmpty(pupiao)) {
                log.error("当前采购单仅退新蓝字有效票id:{},未能查到原始蓝票的推送金蝶的信息",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
                throw new KingDeeException("当前采购单仅退新蓝字有效票id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未能查到原始蓝票的推送金蝶的信息");
            }

            List<DateTime> dateTimes = byFQzokBddjtid.stream().map(x -> DateUtil.parseDateTime(x.getFdate())).collect(Collectors.toList());
            Optional<DateTime> max = dateTimes.stream().max(Comparator.comparing(x -> x));
            if (max.isPresent() && max.get().getTime() > batchInvoiceDto.getValidTime()) {
                batchInvoiceDto.setValidTime(max.get().getTime());
            }

            PurchaseVatSpecialInvoiceDto dto = new PurchaseVatSpecialInvoiceDto();
            dto.setFid("0");
            dto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
            dto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
            dto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            dto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            dto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
            dto.setFdocumentstatus("Z");
            dto.setFBillTypeID("CGZZSZYFP01_SYS");
            dto.setFsettleorgid(KingDeeConstant.ORG_ID.toString());
            dto.setFCancelStatus("A");
            dto.setFRedBlue("0");
            dto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());


            List<PurchaseVatSpecialInvoiceDto> purchaseVatSpecialInvoiceDtos = kingDeePurchaseVatSpecialInvoiceConvertor.toDto(byFQzokBddjtid);
            Map<String, List<PurchaseVatSpecialInvoiceDto>> collect = purchaseVatSpecialInvoiceDtos.stream().collect(Collectors.groupingBy(PurchaseVatSpecialInvoiceDto::getFQzokBddjtid));


            List<PurchaseVatPlainInvoiceDto> purchaseVatPlainInvoiceDtos = kingDeePurchaseVatPlainInvoiceConvertor.toDto(pupiao);
            Map<String, List<PurchaseVatPlainInvoiceDto>> collect2 = purchaseVatPlainInvoiceDtos.stream().collect(Collectors.groupingBy(PurchaseVatPlainInvoiceDto::getFQzokBddjtid));
            // 二级
            List<PurchaseVatSpecialInvoiceDetailDto> FPURCHASEICENTRY = new ArrayList<>();
            dto.setFPURCHASEICENTRY(FPURCHASEICENTRY);
            batchInvoiceDetailDtoList.forEach(c -> {
                BatchBuyorderGoodsDto batchBuyorderGoodsDto = batchBuyorderGoods2Map.get(c.getDetailgoodsId());
                if (Objects.isNull(batchBuyorderGoodsDto)) {
                    return;
                }
                // 票明细总额
                BigDecimal totalAmount = Objects.isNull(c.getTotalAmount()) ? BigDecimal.ZERO : c.getTotalAmount();
                BigDecimal price = c.getTotalAmount().divide(c.getNum(),6, RoundingMode.HALF_UP);
                // 税额  [价税合计/(1+ 税率)]*税率
                BigDecimal taxAmouontFor = totalAmount.multiply(taxRateNum).divide(BigDecimal.ONE.add(taxRateNum),2,RoundingMode.HALF_UP);
                PurchaseVatSpecialInvoiceDetailDto sonDto = new PurchaseVatSpecialInvoiceDetailDto();
                sonDto.setFmaterialid(batchBuyorderGoodsDto.getSku());
                sonDto.setFpriceqty(c.getNum().abs().toString());
                sonDto.setFauxtaxprice(price.abs());
                sonDto.setFtaxrate(taxRate);
                // 发票不含税的金额 价税合计-税额
                sonDto.setFamountfor(totalAmount.subtract(taxAmouontFor));
                sonDto.setFdetailtaxamountfor(taxAmouontFor);
                sonDto.setFallamountfor(totalAmount);
                sonDto.setFQzokBddjhid(c.getInvoiceDetailId().toString());
                sonDto.setFsourcetype(KingDeeFormConstant.PAY_EXPENSES);
                sonDto.setFQzokYsddh(batchInvoiceDto.getOrderNo());
                sonDto.setFQzokGsywdh(batchInvoiceDto.getAfterSalesNo());
                sonDto.setFQzokYwlx("采购退票");

                // 三级
                List<PurchaseVatSpecialInvoiceDetailLinkDto> fpurchaseicentryLink = new ArrayList<>();
                sonDto.setFPURCHASEICENTRY_Link(fpurchaseicentryLink);
                FPURCHASEICENTRY.add(sonDto);
                // 找所有的老蓝票的三级关系
                List<PurchaseVatSpecialInvoiceDetailLinkDto> all = new ArrayList<>();
                // 找所有的老蓝票的三级关系
                List<PurchaseVatPlainInvoiceDetailLinkDto> all2 = new ArrayList<>();
                blueInvoiceIds.forEach(a -> {

                    List<PurchaseVatSpecialInvoiceDto> data = collect.get(a.toString());
                    if (CollUtil.isNotEmpty(data)) {
                        data.forEach(x -> {
                            List<PurchaseVatSpecialInvoiceDetailDto> fpurchaseicentry = x.getFPURCHASEICENTRY();
                            if (CollUtil.isEmpty(fpurchaseicentry)) {
                                log.error("采购费用仅退票新蓝票，原始蓝票金蝶的二级级关系为空,{}", JSON.toJSONString(x));
                                return;
                            }
                            List<PurchaseVatSpecialInvoiceDetailDto> originalBlueDetails = fpurchaseicentry.stream().filter(xx -> xx.getFmaterialid().equals(batchBuyorderGoodsDto.getSku())).collect(Collectors.toList());

                            if (CollUtil.isEmpty(originalBlueDetails)) {
                                log.error("采购费用仅退票，原始蓝票当前sku：{}的金蝶的二级关系为空,{}", batchBuyorderGoodsDto.getSku(), JSON.toJSONString(x));
                                return;
                            }
                            originalBlueDetails.forEach(xx -> {
                                if (CollUtil.isNotEmpty(xx.getFPURCHASEICENTRY_Link())) {
                                    all.addAll(xx.getFPURCHASEICENTRY_Link());
                                }
                            });
                        });
                    }

                    List<PurchaseVatPlainInvoiceDto> data1 = collect2.get(a.toString());
                    if (CollUtil.isNotEmpty(data1)) {
                        data1.forEach(x -> {
                            List<PurchaseVatPlainInvoiceDetailDto> fpurchaseicentry = x.getFPURCHASEICENTRY();
                            if (CollUtil.isEmpty(fpurchaseicentry)) {
                                log.error("采购费用仅退票新蓝票，原始蓝票金蝶的二级级关系为空,{}", JSON.toJSONString(x));
                                return;
                            }
                            List<PurchaseVatPlainInvoiceDetailDto> originalBlueDetails = fpurchaseicentry.stream().filter(xx -> xx.getFmaterialid().equals(batchBuyorderGoodsDto.getSku())).collect(Collectors.toList());
                            if (CollUtil.isEmpty(originalBlueDetails)) {
                                log.error("采购费用仅退票新蓝票，原始蓝票当前sku：{}的金蝶的二级关系为空,{}", batchBuyorderGoodsDto.getSku(), JSON.toJSONString(x));
                                return;
                            }
                            originalBlueDetails.forEach(k -> {
                                if (CollUtil.isNotEmpty(k.getFpurchaseicentryLink())) {
                                    all2.addAll(k.getFpurchaseicentryLink());
                                }
                            });
                        });
                    }

                });


                if (CollUtil.isEmpty(all)&&CollUtil.isEmpty(all2)) {
                    log.error("采购费用仅退票新蓝票id:{}，原始蓝票金蝶的三级关系为空",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
                    return;
                }
                if (CollUtil.isNotEmpty(all)) {
                    for (int i = 0; i < all.size(); i++) {
                        PurchaseVatSpecialInvoiceDetailLinkDto item = all.get(i);
                        log.info("新蓝票找到的原始蓝票三级：{}",JSON.toJSONString(item));
                        PurchaseVatSpecialInvoiceDetailLinkDto inSon1 = new PurchaseVatSpecialInvoiceDetailLinkDto();

                        inSon1.setFLinkId("0");
                        inSon1.setFpurchaseicentryLinkFflowlineid("0");
                        inSon1.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
                        inSon1.setFpurchaseicentryLinkFstableid("0");
                        inSon1.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
                        inSon1.setFpurchaseicentryLinkFsbillid(item.getFpurchaseicentryLinkFsbillid());
                        inSon1.setFpurchaseicentryLinkFsid(item.getFpurchaseicentryLinkFsid());
                        if (i == all.size() - 1&&CollUtil.isEmpty(all2)) {
                            // 减去最后一行 获取前面的已录入
                            BigDecimal previousAmount = price.multiply(c.getNum().subtract(new BigDecimal(item.getFpurchaseicentryLinkFbasicunitqty()))).setScale(10, RoundingMode.HALF_UP);
                            // 避免尾差
                            BigDecimal thisLine = c.getTotalAmount().subtract(previousAmount);
                            log.info("采购仅退票新蓝票invoiceId:{},sku:{},已录入:{},最后一行:{}", batchInvoiceDto.getInvoiceId(), sonDto.getFmaterialid(), previousAmount, thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountforold(thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountfor(thisLine);
                            inSon1.setFpurchaseicentryLinkFbasicunitqtyold(item.getFpurchaseicentryLinkFbasicunitqtyold());
                            inSon1.setFpurchaseicentryLinkFbasicunitqty(item.getFpurchaseicentryLinkFbasicunitqty());
                        } else {
                            // 均价*录入关系的数量
                            BigDecimal thisLine = price.multiply(new BigDecimal(item.getFpurchaseicentryLinkFbasicunitqty())).setScale(10, RoundingMode.HALF_UP);
                            log.info("采购仅退票新蓝票invoiceId:{},sku:{},前几行金额:{}", batchInvoiceDto.getInvoiceId(), sonDto.getFmaterialid(), thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountforold(thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountfor(thisLine);
                            inSon1.setFpurchaseicentryLinkFbasicunitqtyold(item.getFpurchaseicentryLinkFbasicunitqtyold());
                            inSon1.setFpurchaseicentryLinkFbasicunitqty(item.getFpurchaseicentryLinkFbasicunitqty());
                        }
                        fpurchaseicentryLink.add(inSon1);
                    }
                }

                if (CollUtil.isNotEmpty(all2)) {
                    for (int i = 0; i < all2.size(); i++) {
                        PurchaseVatPlainInvoiceDetailLinkDto item = all2.get(i);
                        log.info("新蓝票找到的原始蓝票三级：{}",JSON.toJSONString(item));
                        PurchaseVatSpecialInvoiceDetailLinkDto inSon1 = new PurchaseVatSpecialInvoiceDetailLinkDto();

                        inSon1.setFLinkId("0");
                        inSon1.setFpurchaseicentryLinkFflowlineid("0");
                        inSon1.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
                        inSon1.setFpurchaseicentryLinkFstableid("0");
                        inSon1.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
                        inSon1.setFpurchaseicentryLinkFsbillid(item.getFpurchaseicentryLinkFsbillid());
                        inSon1.setFpurchaseicentryLinkFsid(item.getFpurchaseicentryLinkFsid());
                        if (i == all2.size() - 1) {
                            // 减去最后一行 获取前面的已录入
                            BigDecimal previousAmount = price.multiply(c.getNum().subtract(new BigDecimal(item.getFpurchaseicentryLinkFbasicunitqty()))).setScale(10, RoundingMode.HALF_UP);
                            // 避免尾差
                            BigDecimal thisLine = c.getTotalAmount().subtract(previousAmount);
                            log.info("采购仅退票新蓝票invoiceId:{},sku:{},已录入:{},最后一行:{}", batchInvoiceDto.getInvoiceId(), sonDto.getFmaterialid(), previousAmount, thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountforold(thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountfor(thisLine);
                            inSon1.setFpurchaseicentryLinkFbasicunitqtyold(item.getFpurchaseicentryLinkFbasicunitqtyold());
                            inSon1.setFpurchaseicentryLinkFbasicunitqty(item.getFpurchaseicentryLinkFbasicunitqty());
                        } else {
                            // 均价*录入关系的数量
                            BigDecimal thisLine = price.multiply(new BigDecimal(item.getFpurchaseicentryLinkFbasicunitqty())).setScale(10, RoundingMode.HALF_UP);
                            log.info("采购仅退票新蓝票invoiceId:{},sku:{},前几行金额:{}", batchInvoiceDto.getInvoiceId(), sonDto.getFmaterialid(), thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountforold(thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountfor(thisLine);
                            inSon1.setFpurchaseicentryLinkFbasicunitqtyold(item.getFpurchaseicentryLinkFbasicunitqtyold());
                            inSon1.setFpurchaseicentryLinkFbasicunitqty(item.getFpurchaseicentryLinkFbasicunitqty());
                        }
                        fpurchaseicentryLink.add(inSon1);
                    }
                }


            });

            kingDeePayCommonAndInvoiceDto.setPurchaseVatSpecialInvoiceDto(dto);

        } else {
            kingDeePayCommonAndInvoiceDto.setSpecial(false);

            List<KingDeePurchaseVatPlainInvoiceEntity> byFQzokBddjtid = kingDeePurchaseVatPlainInvoiceMapper.findByFQzokBddjtid(blueInvoiceId);
            List<KingDeePurchaseVatSpecialInvoiceEntity> zhuanpiao = kingDeePurchaseVatSpecialInvoiceMapper.findByFQzokBddjtid(blueInvoiceId);
            if (CollUtil.isEmpty(byFQzokBddjtid)&&CollUtil.isEmpty(zhuanpiao)) {
                log.warn("采购费用仅退票新蓝票，未能查到原始蓝票的推送金蝶的信息");
                return null;
            }


            PurchaseVatPlainInvoiceDto dto = new PurchaseVatPlainInvoiceDto();
            List<DateTime> dateTimes = byFQzokBddjtid.stream().map(x -> DateUtil.parseDateTime(x.getFdate())).collect(Collectors.toList());
            Optional<DateTime> max = dateTimes.stream().max(Comparator.comparing(x -> x));
            if (max.isPresent() && max.get().getTime() > batchInvoiceDto.getValidTime()) {
                batchInvoiceDto.setValidTime(max.get().getTime());
            }
            // 1电票 2 纸票
            dto.setFQzokFplx(QZOK_FPLX);
            dto.setFid("0");
            dto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
            dto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
            dto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            dto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            dto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
            dto.setFdocumentstatus("Z");
            dto.setFBillTypeID("CGPTFP01_SYS");
            dto.setFsettleorgid(KingDeeConstant.ORG_ID.toString());
            dto.setFpurchaseorgid(KingDeeConstant.ORG_ID.toString());
            dto.setFCancelStatus("A");
            dto.setFRedBlue("0");
            dto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());


            List<PurchaseVatPlainInvoiceDto> purchaseVatPlainInvoiceDtos = kingDeePurchaseVatPlainInvoiceConvertor.toDto(byFQzokBddjtid);
            Map<String, List<PurchaseVatPlainInvoiceDto>> collect = purchaseVatPlainInvoiceDtos.stream().collect(Collectors.groupingBy(PurchaseVatPlainInvoiceDto::getFQzokBddjtid));

            List<PurchaseVatSpecialInvoiceDto> purchaseVatSpecialInvoiceDtos = kingDeePurchaseVatSpecialInvoiceConvertor.toDto(zhuanpiao);
            Map<String, List<PurchaseVatSpecialInvoiceDto>> collect2 = purchaseVatSpecialInvoiceDtos.stream().collect(Collectors.groupingBy(PurchaseVatSpecialInvoiceDto::getFQzokBddjtid));
            // 二级
            List<PurchaseVatPlainInvoiceDetailDto> FPURCHASEICENTRY = new ArrayList<>();
            dto.setFPURCHASEICENTRY(FPURCHASEICENTRY);
            batchInvoiceDetailDtoList.forEach(c -> {
                BatchBuyorderGoodsDto batchBuyorderGoodsDto = batchBuyorderGoods2Map.get(c.getDetailgoodsId());
                if (Objects.isNull(batchBuyorderGoodsDto)) {
                    return;
                }

                BigDecimal price = c.getTotalAmount().divide(c.getNum(),6, RoundingMode.HALF_UP);
                // 票明细总额

                PurchaseVatPlainInvoiceDetailDto sonDto = new PurchaseVatPlainInvoiceDetailDto();
                sonDto.setFmaterialid(batchBuyorderGoodsDto.getSku());
                sonDto.setFpriceqty(c.getNum().abs().toString());
                sonDto.setFauxtaxprice(price.abs().toString());
                sonDto.setFQzokBddjhid(c.getInvoiceDetailId().toString());
                sonDto.setFsourcetype(KingDeeFormConstant.PAY_EXPENSES);
                sonDto.setFQzokYsddh(batchInvoiceDto.getOrderNo());
                sonDto.setFQzokGsywdh(batchInvoiceDto.getAfterSalesNo());
                sonDto.setFQzokYwlx("采购退票");

                // 三级
                List<PurchaseVatPlainInvoiceDetailLinkDto> fpurchaseicentryLink = new ArrayList<>();
                sonDto.setFpurchaseicentryLink(fpurchaseicentryLink);
                FPURCHASEICENTRY.add(sonDto);

                // 找所有的老蓝票的三级关系
                List<PurchaseVatPlainInvoiceDetailLinkDto> all = new ArrayList<>();
                List<PurchaseVatSpecialInvoiceDetailLinkDto> all2 = new ArrayList<>();
                blueInvoiceIds.forEach(a -> {
                    List<PurchaseVatPlainInvoiceDto> data = collect.get(a.toString());
                    if (CollUtil.isNotEmpty(data)) {
                        data.forEach(x -> {
                            List<PurchaseVatPlainInvoiceDetailDto> fpurchaseicentry = x.getFPURCHASEICENTRY();
                            if (CollUtil.isEmpty(fpurchaseicentry)) {
                                log.warn("采购费用仅退票新蓝票，原始蓝票金蝶的二级级关系为空,{}", JSON.toJSONString(x));
                                return;
                            }
                            List<PurchaseVatPlainInvoiceDetailDto> originalBlueDetails = fpurchaseicentry.stream().filter(xx -> xx.getFmaterialid().equals(batchBuyorderGoodsDto.getSku())).collect(Collectors.toList());
                            if (CollUtil.isEmpty(originalBlueDetails)) {
                                log.info("采购费用仅退票新蓝票，原始蓝票当前sku：{}的金蝶的二级关系为空,{}", batchBuyorderGoodsDto.getSku(), JSON.toJSONString(x));
                                return;
                            }
                            originalBlueDetails.forEach(k -> {
                                if (CollUtil.isNotEmpty(k.getFpurchaseicentryLink())) {
                                    all.addAll(k.getFpurchaseicentryLink());
                                }
                            });
                        });
                    }

                    List<PurchaseVatSpecialInvoiceDto> data1 = collect2.get(a.toString());
                    if (CollUtil.isNotEmpty(data1)) {
                        data1.forEach(x -> {
                            List<PurchaseVatSpecialInvoiceDetailDto> fpurchaseicentry = x.getFPURCHASEICENTRY();
                            if (CollUtil.isEmpty(fpurchaseicentry)) {
                                log.warn("采购费用仅退票新蓝票，原始蓝票金蝶的二级级关系为空,{}", JSON.toJSONString(x));
                                return;
                            }
                            List<PurchaseVatSpecialInvoiceDetailDto> originalBlueDetails = fpurchaseicentry.stream().filter(xx -> xx.getFmaterialid().equals(batchBuyorderGoodsDto.getSku())).collect(Collectors.toList());

                            if (CollUtil.isEmpty(originalBlueDetails)) {
                                log.info("采购费用仅退票，原始蓝票当前sku：{}的金蝶的二级关系为空,{}", batchBuyorderGoodsDto.getSku(), JSON.toJSONString(x));
                                return;
                            }
                            originalBlueDetails.forEach(xx -> {
                                if (CollUtil.isNotEmpty(xx.getFPURCHASEICENTRY_Link())) {
                                    all2.addAll(xx.getFPURCHASEICENTRY_Link());
                                }
                            });
                        });
                    }
                });

                if (CollUtil.isEmpty(all)&&CollUtil.isEmpty(all2)) {
                    log.warn("采购费用仅退票新蓝票，原始蓝票金蝶的三级关系为空");
                    return;
                }
                if (CollUtil.isNotEmpty(all)) {
                    for (int i = 0; i < all.size(); i++) {
                        PurchaseVatPlainInvoiceDetailLinkDto item = all.get(i);
                        log.info("新蓝票找到的原始蓝票三级：{}",JSON.toJSONString(item));
                        PurchaseVatPlainInvoiceDetailLinkDto inSon1 = new PurchaseVatPlainInvoiceDetailLinkDto();

                        inSon1.setFLinkId("0");
                        inSon1.setFpurchaseicentryLinkFflowlineid("0");
                        inSon1.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
                        inSon1.setFpurchaseicentryLinkFstableid("0");
                        inSon1.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
                        inSon1.setFpurchaseicentryLinkFsbillid(item.getFpurchaseicentryLinkFsbillid());
                        inSon1.setFpurchaseicentryLinkFsid(item.getFpurchaseicentryLinkFsid());
                        if (i == all.size() - 1&&CollUtil.isEmpty(all2)) {
                            // 减去最后一行 获取前面的已录入
                            BigDecimal previousAmount = price.multiply(c.getNum().subtract(new BigDecimal(item.getFpurchaseicentryLinkFbasicunitqty()))).setScale(10, RoundingMode.HALF_UP);
                            // 避免尾差
                            BigDecimal thisLine = c.getTotalAmount().subtract(previousAmount);
                            log.info("采购仅退票新蓝票invoiceId:{},sku:{},已录入:{},最后一行:{}", batchInvoiceDto.getInvoiceId(), sonDto.getFmaterialid(), previousAmount, thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountforold(thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountfor(thisLine);
                            inSon1.setFpurchaseicentryLinkFbasicunitqtyold(item.getFpurchaseicentryLinkFbasicunitqtyold());
                            inSon1.setFpurchaseicentryLinkFbasicunitqty(item.getFpurchaseicentryLinkFbasicunitqty());
                        } else {
                            // 均价*录入关系的数量
                            BigDecimal thisLine = price.multiply(new BigDecimal(item.getFpurchaseicentryLinkFbasicunitqty())).setScale(10, RoundingMode.HALF_UP);
                            log.info("采购仅退票新蓝票invoiceId:{},sku:{},前几行金额:{}", batchInvoiceDto.getInvoiceId(), sonDto.getFmaterialid(), thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountforold(thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountfor(thisLine);
                            inSon1.setFpurchaseicentryLinkFbasicunitqtyold(item.getFpurchaseicentryLinkFbasicunitqtyold());
                            inSon1.setFpurchaseicentryLinkFbasicunitqty(item.getFpurchaseicentryLinkFbasicunitqty());
                        }
                        fpurchaseicentryLink.add(inSon1);
                    }
                }
                if (CollUtil.isNotEmpty(all2)) {
                    for (int i = 0; i < all2.size(); i++) {
                        PurchaseVatSpecialInvoiceDetailLinkDto item = all2.get(i);
                        log.info("新蓝票找到的原始蓝票三级：{}",JSON.toJSONString(item));
                        PurchaseVatPlainInvoiceDetailLinkDto inSon1 = new PurchaseVatPlainInvoiceDetailLinkDto();

                        inSon1.setFLinkId("0");
                        inSon1.setFpurchaseicentryLinkFflowlineid("0");
                        inSon1.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
                        inSon1.setFpurchaseicentryLinkFstableid("0");
                        inSon1.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
                        inSon1.setFpurchaseicentryLinkFsbillid(item.getFpurchaseicentryLinkFsbillid());
                        inSon1.setFpurchaseicentryLinkFsid(item.getFpurchaseicentryLinkFsid());
                        if (i == all2.size() - 1) {
                            // 减去最后一行 获取前面的已录入
                            BigDecimal previousAmount = price.multiply(c.getNum().subtract(new BigDecimal(item.getFpurchaseicentryLinkFbasicunitqty()))).setScale(10, RoundingMode.HALF_UP);
                            // 避免尾差
                            BigDecimal thisLine = c.getTotalAmount().subtract(previousAmount);
                            log.info("采购仅退票新蓝票invoiceId:{},sku:{},已录入:{},最后一行:{}", batchInvoiceDto.getInvoiceId(), sonDto.getFmaterialid(), previousAmount, thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountforold(thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountfor(thisLine);
                            inSon1.setFpurchaseicentryLinkFbasicunitqtyold(item.getFpurchaseicentryLinkFbasicunitqtyold());
                            inSon1.setFpurchaseicentryLinkFbasicunitqty(item.getFpurchaseicentryLinkFbasicunitqty());
                        } else {
                            // 均价*录入关系的数量
                            BigDecimal thisLine = price.multiply(new BigDecimal(item.getFpurchaseicentryLinkFbasicunitqty())).setScale(10, RoundingMode.HALF_UP);
                            log.info("采购仅退票新蓝票invoiceId:{},sku:{},前几行金额:{}", batchInvoiceDto.getInvoiceId(), sonDto.getFmaterialid(), thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountforold(thisLine);
                            inSon1.setFpurchaseicentryLinkFallamountfor(thisLine);
                            inSon1.setFpurchaseicentryLinkFbasicunitqtyold(item.getFpurchaseicentryLinkFbasicunitqtyold());
                            inSon1.setFpurchaseicentryLinkFbasicunitqty(item.getFpurchaseicentryLinkFbasicunitqty());
                        }
                        fpurchaseicentryLink.add(inSon1);
                    }
                }

            });

            kingDeePayCommonAndInvoiceDto.setPurchaseVatPlainInvoiceDto(dto);
        }

        return kingDeePayCommonAndInvoiceDto;
    }

}
