package com.vedeng.erp.system.dto;

import lombok.Data;

import java.util.List;

/**
 * 人员与产线区域配置DTO
 */
@Data
public class RoleUserRegionConfigDto {
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 线上销售用户ID
     */
    private Integer onlineSalesId;

    /**
     * 线下销售用户ID
     */
    private Integer offlineSalesId;

    /**
    * 产线人员用户ID
    */
    private Integer productionUserId;

    /**
     * 省市列表
     */
    private List<Region> regions;
    
    /**
     * 省市对象
     */
    @Data
    public static class Region {
        /**
         * 省ID
         */
        private Integer provinceId;
        
        /**
         * 市ID
         */
        private Integer cityId;
        
    }
} 