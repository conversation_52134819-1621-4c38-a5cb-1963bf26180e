package com.vedeng.erp.system.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义数据日志类型
 * @date 2022/7/11 22:33
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomDataLogDto extends BaseDto {


    /**
     * 主键
     */
    private Integer id;

    /**
     * 对应的业务主键ids，成功时存在。
     */
    private String relatedIds;

    /**
     * 业务类型（1=线索 2=商机）
     */
    private Integer type;

    /**
     * 新增方式（1.代码 2.excel导入）
     */
    private Integer saveType;

    /**
     * 结果信息
     */
    private String resultInfo;

    /**
     * 是否成功
     */
    private Boolean successFlag;

    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 归属人id
     */
    private String belonger;

    /**
     * 归属集合
     */
    private List<Integer> belongers;

}
