<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="盘盈入库列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="content">
    <div class="searchfunc">
        <form action="${pageContext.request.contextPath}/wms/surplusIn/index.do" method="post" id="search">
            <ul>
                <li>
                    <label class="infor_name">产品名称</label>
                    <input type="text" class="input-middle" name="goodsName" id="goodsName" value="${wmsSurplusInQueryDto.goodsName }"/>
                </li>
                <li>
                <label class="infor_name">订货号</label>
                <input type="text" class="input-middle" name="skuNo" id="skuNo" value="${wmsSurplusInQueryDto.skuNo }"/>
            </li>
                <li>
                    <label class="infor_name">品牌</label>
                    <input type="text" class="input-middle" name="brandName" id="brandName" value="${wmsSurplusInQueryDto.brandName }"/>
                </li>
                <li>
                    <label class="infor_name">型号</label>
                    <input type="text" class="input-middle" name="model" id="model" value="${wmsSurplusInQueryDto.model }"/>
                </li>
                <li>
                    <label class="infor_name">入库单号</label>
                    <input type="text" class="input-middle" name="orderNo" id="orderNo" value="${wmsSurplusInQueryDto.orderNo }"/>
                </li>
                <li>
                    <label class="infor_name">审核状态</label>
                    <select class="input-middle f_left" name="verifyStatus">
                        <option value="">全部</option>
                        <option <c:if test="${wmsSurplusInQueryDto.verifyStatus eq 0}">selected</c:if> value="0">待审核</option>
                        <option <c:if test="${wmsSurplusInQueryDto.verifyStatus eq 1}">selected</c:if> value="1">审核中</option>
                        <option <c:if test="${wmsSurplusInQueryDto.verifyStatus eq 2}">selected</c:if> value="2">审核通过</option>
                        <option <c:if test="${wmsSurplusInQueryDto.verifyStatus eq 3}">selected</c:if> value="3">审核不通过</option>
                        <option <c:if test="${wmsSurplusInQueryDto.verifyStatus eq 4}">selected</c:if> value="4">已关闭</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">入库状态</label>
                    <select class="input-middle f_left" name="arrivalStatus">
                        <option value="">全部</option>
                        <option <c:if test="${wmsSurplusInQueryDto.arrivalStatus eq 0}">selected</c:if> value="0">未入库</option>
                        <option <c:if test="${wmsSurplusInQueryDto.arrivalStatus eq 1}">selected</c:if> value="1">部分入库</option>
                        <option <c:if test="${wmsSurplusInQueryDto.arrivalStatus eq 2}">selected</c:if> value="2">全部入库</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">申请入库日期</label>
                    <input class="Wdate f_left input-smaller96 m0" type="text" placeholder="请选择日期" autocomplete="off"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endtime\')}'})"
                           name="applyIntimeStrat" id="starttime" value="${applyIntimeStrat }">

                    <div class="f_left ml1 mr1 mt4">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'})"
                           name="applyIntimeEnd" id="endtime" value="${applyIntimeEnd }">
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span>
                <span class="bg-light-blue bt-bg-style bt-small addtitle"
                      tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                    "link":"./wms/surplusIn/surplusInAdd.do","title":"新增盘盈入库单"}'>新增盘盈入库单</span>
            </div>
        </form>
    </div>
    <div  class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid5">序号</th>
                <th class="wid10">入库单号</th>
                <th class="wid10">入库状态</th>
                <th class="wid7">是否多个商品</th>
                <th class="wid20">产品名称</th>
                <th class="wid10">订货号</th>
                <th class="wid15">品牌</th>
                <th class="wid15">型号</th>
                <th class="wid7">单位</th>
                <th class="wid7">入库数量</th>
                <th class="wid15">审核状态</th>
                <th class="wid10">操作</th>
            </tr>
            </thead>
            <tbody class="company">
            <c:if test="${not empty wmsSurplusInOrderlist}">
                <c:forEach items="${wmsSurplusInOrderlist}" var="item" varStatus="num">
                    <tr>
                        <td>${num.count}</td>
                        <td> <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
													"link":"./wms/surplusIn/applyIndex.do?wmsInputOrderId=${item.wmsInputOrderId}","title":"订单信息"}'>${item.orderNo}</a>
                        </td>
                        <td> ${item.arrivalStatusStr} </td>
                        <td> <c:choose>
                            <c:when test="${item.countSku eq 1}">
                                否
                            </c:when>
                            <c:when test="${item.countSku ne 1}">
                                是
                            </c:when>
                        </c:choose>
                        </td>
                        <td> ${item.goodsName} </td>
                        <td> ${item.skuNo} </td>
                        <td> ${item.brandName}  </td>
                        <td> ${item.model}  </td>
                        <td> ${item.unitName} </td>
                        <td> ${item.inputNum} </td>
                        <td> ${item.verifyStatusStr} </td>
                        <td>
                            <span class="edit-user addtitle" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./wms/surplusIn/applyIndex.do?wmsInputOrderId=${item.wmsInputOrderId}","title":"详情"}'>详情</span>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
      <%--  <div class="allchose">
            <input type="checkbox" name="" onclick="selectall(this);" autocomplete="off">
            <span>全选</span>
        </div>--%>
        <c:if test="${empty wmsSurplusInOrderlist}">
            <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>
<script type="text/javascript" src='<%= basePath %>static/js/logistics/warehouseIn/addBarcode.js?rnd=${resourceVersionKey}'></script>
<%@ include file="../../common/footer.jsp"%>