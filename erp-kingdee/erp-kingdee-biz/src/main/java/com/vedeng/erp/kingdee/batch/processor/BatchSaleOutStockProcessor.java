package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchSaleorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/7 13:42
 */
@Service
@Slf4j
public class BatchSaleOutStockProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeSaleOutStockDto> {

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private BatchSaleorderDtoMapper batchSaleorderDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Value("${kingDeeSaleorderOutNotPush}")
    private String kingDeeSaleorderOutNotPush;


    @Override
    public KingDeeSaleOutStockDto process(BatchWarehouseGoodsOutInDto batchSaleOutStockDto) throws Exception {
        // 判断是否数据已存在
        KingDeeSaleOutStockDto query = new KingDeeSaleOutStockDto();
        query.setF_qzok_bddjtid(batchSaleOutStockDto.getWarehouseGoodsOutInId().toString());
        boolean old = kingDeeBaseApi.isExist(query);
        if (old) {
            log.info("销售实物出库单,金蝶数据已存在:{}", JSON.toJSONString(query));
            return null;
        }

        BatchSaleorderDto batchSaleorderDto = batchSaleorderDtoMapper.selectBySaleorderNo(batchSaleOutStockDto.getRelateNo());
        if (StrUtil.isNotEmpty(kingDeeSaleorderOutNotPush)) {
            List<Integer> traderIds = JSON.parseArray(kingDeeSaleorderOutNotPush, Integer.class);
            if (CollUtil.isNotEmpty(traderIds) && Objects.nonNull(batchSaleorderDto)) {
                boolean contains = traderIds.contains(batchSaleorderDto.getTraderId());
                if (contains) {
                    log.info("销售出库单：{}，符合不推送金蝶条件，{}", batchSaleOutStockDto.getOutInNo(), kingDeeSaleorderOutNotPush);
                    return null;
                }

            }
        }
        log.info("开始构造销售实物出库单：{}", JSON.toJSONString(batchSaleOutStockDto));
        KingDeeSaleOutStockDto kingDeeSaleOutStockDto = new KingDeeSaleOutStockDto();
        kingDeeSaleOutStockDto.setFid("0");
        kingDeeSaleOutStockDto.setFBillNo(batchSaleOutStockDto.getOutInNo());
        kingDeeSaleOutStockDto.setF_qzok_bddjtid(batchSaleOutStockDto.getWarehouseGoodsOutInId().toString());
        kingDeeSaleOutStockDto.setF_qzok_gsbm(batchSaleOutStockDto.getOrgName());
        kingDeeSaleOutStockDto.setFDate(DateUtil.formatDate(batchSaleOutStockDto.getOutInTime()));
        kingDeeSaleOutStockDto.setFCustomerID(batchSaleOutStockDto.getTraderCustomerId().toString());
        kingDeeSaleOutStockDto.setFSaleOrgId(KingDeeConstant.ORG_ID.toString());
        kingDeeSaleOutStockDto.setFStockOrgId(KingDeeConstant.ORG_ID.toString());

        // 查询出入库日志 明细
        List<KingDeeSaleOutStockDetailDto> detailDtoList = new ArrayList<>();
        List<BatchWarehouseGoodsOutInItemDto> itemList = batchWarehouseGoodsOutInDtoMapper.getWarehouseGoodsOutInItemByWarehouseId(batchSaleOutStockDto.getOutInNo());
        itemList.forEach(item -> {
            KingDeeSaleOutStockDetailDto temp = new KingDeeSaleOutStockDetailDto();
            temp.setFMaterialID(item.getSkuNo());
            temp.setFRealQty(item.getNum());
            temp.setFIsFree(item.getIsGift() == 1);
            temp.setFTaxPrice(item.getPrice());
            temp.setFEntryTaxRate(item.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
            temp.setF_QZOK_SFZF(item.getDeliveryDirect() == 1 ? "是" : "否");
            temp.setF_QZOK_YSDDH(batchSaleOutStockDto.getRelateNo());
            temp.setF_QZOK_GSYWDH(batchSaleOutStockDto.getRelateNo());
            temp.setF_QZOK_YWLX("销售订单");
            temp.setF_QZOK_PCH(item.getBatchNumber());
            temp.setF_QZOK_XLH(item.getBarcodeFactory());
            temp.setF_QZOK_SFAT2(item.getHaveInstallation() == 1 ? "是" : "否");
            temp.setF_QZOK_BDDJHID(item.getWarehouseGoodsOutInDetailId().toString());
            detailDtoList.add(temp);
        });
        kingDeeSaleOutStockDto.setFEntity(detailDtoList);
        log.info("销售实物出库单构造完成：{}", JSON.toJSONString(kingDeeSaleOutStockDto));
        return kingDeeSaleOutStockDto;
    }
}