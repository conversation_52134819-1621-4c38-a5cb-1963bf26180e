<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>入库记录</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="SXb58Hg_u80" datasource="erp-reportdatasource" fixednumber="0" fixednumberright="0" append_column_url="" append_row_url="" empty_show="/" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">

    <div class=" layui-inline "><label class="layui-form-label">订货号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="SKU_NO" placeholder="" style="" alias="" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></input>
     </div>
    </div>

    <div class=" layui-inline "><label class="layui-form-label">采购单号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="BUYORDER_NO" placeholder="" style="" alias="" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></input>
     </div>
    </div>

   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>

     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
     <th item_name="SKU_NO" name="SKU_NO" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订货号</th>
      <th item_name="SKU_NAME" name="SKU_NAME" body="td-link" order="0" url="/goods/goods/viewbaseinfo.do?goodsId=${SKU_ID}" opentype="PARENT" windowname="${SKU_NAME}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">产品名称</th>
      <th item_name="MATERIAL_CODE" name="MATERIAL_CODE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">物料编码</th>
      <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">品牌</th>
      <th item_name="MODEL" name="MODEL" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">型号</th>
      <th item_name="NUM" name="NUM" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">实际收货数量</th>
      <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">单位</th>
      <th item_name="VEDENG_BATCH_NUMER" name="VEDENG_BATCH_NUMER" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">贝登批次码</th>
      <th item_name="PRODUCT_DATE" name="PRODUCT_DATE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">生产日期</th>
      <th item_name="EXPIRATION_DATE" name="EXPIRATION_DATE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">有效期至</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">入库时间</th>
      <th item_name="BATCH_NUMBER" name="BATCH_NUMBER" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">厂家批次号</th>
      <th item_name="STERILZATION_BATCH_NUMBER" name="STERILZATION_BATCH_NUMBER" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">灭菌批号</th>
      <th item_name="REGISTRATION_NUMBER" name="REGISTRATION_NUMBER" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">注册证号</th>
      </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY ADD_TIME DESC" groupby="">StringBuilder sql1=new StringBuilder("SELECT
	b.SKU,
	b.GOODS_NAME,
	b.MODEL,
	b.BRAND_NAME,
	w.NUM,
	b.UNIT_NAME,
	w.VEDENG_BATCH_NUMER,
	IF(w.PRODUCT_DATE = 0 ,'',FROM_UNIXTIME( w.PRODUCT_DATE / 1000, '%Y-%m-%d' ) )as PRODUCT_DATE,
	IF(w.EXPIRATION_DATE = 0 ,'',FROM_UNIXTIME( w.EXPIRATION_DATE / 1000, '%Y-%m-%d' ) )as EXPIRATION_DATE,
	FROM_UNIXTIME( w.ADD_TIME / 1000, '%Y-%m-%d' )  AS ADD_TIME,
	w.BATCH_NUMBER,
	w.STERILZATION_BATCH_NUMBER,
	num.REGISTRATION_NUMBER,
bu.BUYORDER_NO
FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG w
	LEFT JOIN  T_BUYORDER_GOODS b  ON w.RELATED_ID = b.BUYORDER_GOODS_ID
LEFT JOIN T_BUYORDER bu ON b.BUYORDER_ID =bu.BUYORDER_ID
	LEFT JOIN V_CORE_SKU sku ON b.GOODS_ID = sku.SKU_ID
	LEFT JOIN V_CORE_SPU spu ON sku.SPU_ID = spu.SPU_ID
	LEFT JOIN T_FIRST_ENGAGE z ON spu.FIRST_ENGAGE_ID = z.FIRST_ENGAGE_ID
	AND z.IS_DELETED = 0
	LEFT JOIN T_REGISTRATION_NUMBER num ON z.REGISTRATION_NUMBER_ID = num.REGISTRATION_NUMBER_ID
WHERE
	w.OPERATE_TYPE = 1
	AND w.IS_ENABLE = 1
	AND w.COMPANY_ID = 1
                                       ");


StringBuilder sql2=new StringBuilder("SELECT
	b.SKU,
	b.GOODS_NAME,
	b.MODEL,
	b.BRAND_NAME,
	w.NUM,
	b.UNIT_NAME,
	w.VEDENG_BATCH_NUMER,
	IF(w.PRODUCT_DATE = 0 ,'',FROM_UNIXTIME( w.PRODUCT_DATE / 1000, '%Y-%m-%d' ) )as PRODUCT_DATE,
	IF(w.EXPIRATION_DATE = 0 ,'',FROM_UNIXTIME( w.EXPIRATION_DATE / 1000, '%Y-%m-%d' ) )as EXPIRATION_DATE,
	FROM_UNIXTIME( w.ADD_TIME / 1000, '%Y-%m-%d' )  AS ADD_TIME,
	w.BATCH_NUMBER,
	w.STERILZATION_BATCH_NUMBER,
	num.REGISTRATION_NUMBER,
bu.BUYORDER_NO
FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL w
	LEFT JOIN  T_BUYORDER_GOODS b  ON w.RELATED_ID = b.BUYORDER_GOODS_ID
LEFT JOIN T_BUYORDER bu ON b.BUYORDER_ID =bu.BUYORDER_ID
	LEFT JOIN V_CORE_SKU sku ON b.GOODS_ID = sku.SKU_ID
	LEFT JOIN V_CORE_SPU spu ON sku.SPU_ID = spu.SPU_ID
	LEFT JOIN T_FIRST_ENGAGE z ON spu.FIRST_ENGAGE_ID = z.FIRST_ENGAGE_ID
	AND z.IS_DELETED = 0
	LEFT JOIN T_REGISTRATION_NUMBER num ON z.REGISTRATION_NUMBER_ID = num.REGISTRATION_NUMBER_ID
WHERE
	w.OPERATE_TYPE = 1
	AND w.IS_ENABLE = 1
	AND w.COMPANY_ID = 1
                                                                        ");



result=unionall(sql1,"","",sql2,"","");
return result;</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot">
   <script>
  function submitHandle(){
   if($("#ADD_TIME_START_ID").val()==""){
    layer.alert("入库开始时间不能为空")
    return false;
   }
   return true;
  }

 </script>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
 layui.use(function () {

 })
</script>
 </body>
</html>