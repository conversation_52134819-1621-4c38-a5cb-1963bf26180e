package com.vedeng.finance.vo;

import com.vedeng.finance.model.HxInvoiceDetail;

import java.math.BigDecimal;
import java.util.List;

/**
 * @describe 航信进项票商品视图对象
 * <AUTHOR>
 * @date created in 2020/5/26 16:19
 */
public class HxInvoiceDetailVo extends HxInvoiceDetail {
    /**
     * 已录票金额
     */
    private BigDecimal recordedAmount;

    /**
     * 可录票金额
     */
    private BigDecimal canRecordAmount;

    /**
     * 已录票的相关商品信息
     */
    private List<InvoiceEntryStashVo> invoiceEntryStashVos;

    public BigDecimal getRecordedAmount() {
        return recordedAmount;
    }

    public void setRecordedAmount(BigDecimal recordedAmount) {
        this.recordedAmount = recordedAmount;
    }

    public BigDecimal getCanRecordAmount() {
        return canRecordAmount;
    }

    public void setCanRecordAmount(BigDecimal canRecordAmount) {
        this.canRecordAmount = canRecordAmount;
    }

    public List<InvoiceEntryStashVo> getInvoiceEntryStashVos() {
        return invoiceEntryStashVos;
    }

    public void setInvoiceEntryStashVos(List<InvoiceEntryStashVo> invoiceEntryStashVos) {
        this.invoiceEntryStashVos = invoiceEntryStashVos;
    }
}
