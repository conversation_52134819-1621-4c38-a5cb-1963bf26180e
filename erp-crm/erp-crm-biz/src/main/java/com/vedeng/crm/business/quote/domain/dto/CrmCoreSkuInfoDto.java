package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.erp.system.dto.UserDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CrmCoreSkuInfoDto {

    private Integer skuId;
    // 报价产品信息
    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * crm未被erp嵌入时，CRM打开ERP的地址并在erp中打开该链接的tab切页
     * 例：http://erp.ivedeng.com/index.do?target=/goods/vgoods/viewSku.do?skuId=111126&pageType=1
     * 标题：商品信息整合页
     */
    private String skuNameLink;

    /**
     * 在嵌入ERP时，CRM打开erp的tab页时的超链接
     * 例：/goods/vgoods/viewSku.do?skuId=111126&pageType=1
     * 标题：商品信息整合页
     */
    private String skuNameInnerLink;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 型号
     */
    private String modelOrSpec;

    // 主要参数
    /**
     * 主要参数
     */
    private List<String> mainParam;


    // 销售属性信息

    /**
     * 销售单价（元）
     */
    private BigDecimal salePrice;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 预计货期（天）
     */
    private String expectDeliveryTime;

    // 售后政策
    /**
     * 质保信息 保修政策-主机保修期
     */
    private String warrantyInfo;

    /**
     * 使用年限/效期
     */
    private String useLife;

    /**
     * 是否可安装
     */
    private Integer isInstall;

    // 参考信息

    /**
     * 经销核价
     */
    private BigDecimal dealerPrice;

    /**
     * 可用库存
      */
    private String availableStockNum;

    // 报备信息

    /**
     * 产品负责
     */
    private List<UserDto> productManager;


    /**
     * 是否需要报备
     */
    private Integer isNeedReport;

    /**
     * 报备状态
     * 0无需报备 1报备中 2报备成功 3报备失败
     */
    private Integer reportStatus;

    /**
     * 报备状态描述
     */
    private String reportStatusDesc;

    /**
     * 图片
     */
    private String imageUrl;

    /**
     * 报备原因
     */
    private String reportComments;

    /**
     * 是否咨询了货期
     */
    private Integer isConsulDeliveryCycle;

    /**
     * 是否咨询了价格
     */
    private Integer isConsulPrice;

    /**
     * 是否咨询了报备
     */
    private Integer isConsulReport;

    /**
     * 商品档位
     */
    private Integer goodsPositionNo;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * SKU的审核状态，取自V_CORE_SKU里的checkStatus字段
     * 审核状态 0待完善 1审核中 2审核不通过 3审核通过 4删除 5 待提交审核
     */
    private Integer checkStatus;

}
