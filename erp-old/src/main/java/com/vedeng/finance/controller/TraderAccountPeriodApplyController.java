package com.vedeng.finance.controller;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.authorization.model.Organization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodDetailManageDto;
import com.vedeng.customerbillperiod.dto.CustomerBillPeriodDetailsDto;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodApply;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail;
import com.vedeng.customerbillperiod.model.vo.CustomerBillPeriodDetailManageVo;
import com.vedeng.customerbillperiod.model.vo.CustomerBillPeriodUseDetailVo;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodApplyService;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodService;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.Invoice;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.service.OrgService;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.dto.*;
import com.vedeng.trader.model.model.vo.CustomerBillPeriodDetailVo;
import com.vedeng.trader.model.vo.*;
import com.vedeng.trader.service.*;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.jasper.IreportExport;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.finance.model.AccountPeriod;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.finance.service.TraderAccountPeriodApplyService;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Controller
@RequestMapping("/finance/accountperiod")
public class TraderAccountPeriodApplyController extends BaseController{
    	
    @Autowired // 自动装载
	private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
	@Autowired
	@Qualifier("accountPeriodService")
	private TraderAccountPeriodApplyService accountPeriodService;
	
	@Autowired
	@Qualifier("userService")
	private UserService userService;//自动注入userService
	
	@Autowired
	@Qualifier("traderCustomerService")
	private TraderCustomerService traderCustomerService;//客户-交易者
	
	@Autowired
	@Qualifier("traderSupplierService")
	private TraderSupplierService traderSupplierService;
	
	@Autowired
	@Qualifier("actionProcdefService")
	private ActionProcdefService actionProcdefService;
	
	@Autowired
	@Qualifier("verifiesRecordService")
	private VerifiesRecordService verifiesRecordService;

	@Autowired
	private OrgService orgService;

	@Autowired
	private TraderDataService traderDataService;

 	@Autowired
	private CustomerBillPeriodService customerBillPeriodService;

 	@Autowired
	private CustomerBillPeriodApplyService customerBillPeriodApplyService;

 	@Autowired
	private SaleorderService saleorderService;

	@Autowired
	private TraderCreditService traderCreditService;

	@Resource
	private CustomerAccountPeriodProcessService customerAccountPeriodProcessService;

	@Value("${ez.domain}")
	private String ezDomain;

    @Value("${customer_export}")
    private String customerExport;

	@Autowired
	@Qualifier("verifiesInfoMapper")
	private VerifiesInfoMapper verifiesInfoMapper;

	/**
	 * <b>Description:</b><br> 查询账期申请记录列表信息NEW
	 * @param request
	 * @param tapa
	 * @param session
	 * @param startTime
	 * @param endTime
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="accountPeriodApplyListNew")
	public ModelAndView customerPeriodApplyList(HttpServletRequest request,TraderAccountPeriodApply tapa,HttpSession session,
											   @RequestParam(required = false, value="startTime") String startTime,
											   @RequestParam(required = false, value="endTime") String endTime,
											   @RequestParam(required = false, defaultValue = "1") Integer pageNo,@RequestParam(required = false) Integer pageSize){
		ModelAndView mv = new ModelAndView();
		mv.setViewName("finance/accountPeriod/list_accountPeriod_new");

		try {
            mv.addObject("ezDomain", ezDomain);
            mv.addObject("customerExport", customerExport.split(","));

			if(StringUtils.isNoneBlank(startTime)){
				tapa.setStartDate(DateUtil.convertLong(startTime + " 00:00:00",""));
			}
			if(StringUtils.isNoneBlank(endTime)){
				tapa.setEndDate(DateUtil.convertLong(endTime + " 23:59:59",""));
			}
			//发票类型
			List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_464);
			mv.addObject("invoiceTypeList", invoiceTypeList);
			mv.addObject("startTime", startTime);mv.addObject("endTime", endTime);
			User user = (User)session.getAttribute(Consts.SESSION_USER);
			mv.addObject("loginUser",user);//登陆用户
			//申请人员列表
			List<Integer> list = new ArrayList<>();
			list.add(SysOptionConstant.ID_310);//销售
			list.add(SysOptionConstant.ID_311);//采购
			List<User> userList = userService.getMyUserList(user,list,false);
			mv.addObject("userList",userList);
			//默认展示审核中的
			if(null == request.getParameter("status") || request.getParameter("status") == ""){
				tapa.setStatus(0);
			}

			Page page = getPageTag(request,pageNo,pageSize);

			if (CollectionUtils.isNotEmpty(tapa.getDepartmentInfo()) && tapa.getDepartmentInfo().size() >1){
				String url = page.getSearchUrl().substring(0,page.getSearchUrl().indexOf("departmentInfo")) +"departmentInfo=";
				for (Integer i:tapa.getDepartmentInfo()){
					if (tapa.getDepartmentInfo().indexOf(i) != 0){
						url= url+",";
					}
					url= url+i;
				}
				Integer len = page.getSearchUrl().indexOf("&", page.getSearchUrl().indexOf("departmentInfo="));
				if (len > 0 ){
					url = url + page.getSearchUrl().substring(len);
				}
				page.setSearchUrl( url);
			}

			if (null==user.getCompanyId()){
				user.setCompanyId(ErpConst.ONE);
			}
			tapa.setCompanyId(user.getCompanyId());
			//判断当前用户是否在审核人名单中
			Integer position = 0;
			if(user.getPositions() != null){
				position = user.getPositions().get(0).getType();
			}

			if(position == 314){
				tapa.setValidUserName(user.getUsername());
			}

			//VDERP-7302 供应商账期类型
			tapa.setTraderType(ErpConst.ONE);
			tapa.setCompanyId(ErpConst.ONE);

			//可选销售
            if (CollectionUtils.isNotEmpty(userList)){
                List<Integer> saleList = userList.stream().filter(Objects::nonNull).map(User::getUserId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(saleList)){
                    tapa.setCreatorList(saleList);
                }
            }

			//部门
            if (CollectionUtils.isNotEmpty(tapa.getDepartmentInfo())){
                List<Integer> departmentOrderInfo = tapa.getDepartmentInfo().stream().filter(Objects::nonNull).map(s->orgService.getChildrenByParentId(s,tapa.getCompanyId())).filter(Objects::nonNull).flatMap(Collection::stream).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(departmentOrderInfo)){
                    tapa.setDepartmentCustomerInfo(departmentOrderInfo);
                }
            }

            //归属部门
            List<Organization> orgList = orgService.getSalesOrgListOrderByStruct(SysOptionConstant.ID_310, user.getCompanyId());
            //mv.addObject("orgList", orgList);
            List<Integer> orgIdDepartmentList = orgService.getAccountPeriodApplyOrgList(user.getOrgId(),tapa);
            if(CollectionUtils.isNotEmpty(orgIdDepartmentList) && !ErpConst.ZERO.equals(tapa.getIsShow())){
                List<Organization> organizationList = new ArrayList<>();
                if(ErpConst.TWO.equals(tapa.getIsShow())){
                    orgIdDepartmentList.stream().filter((Objects::nonNull)).forEach(orgId->{
                        for (Organization org :orgList)
                        {
                            if (org.getOrgId().equals(orgId)){
                                Organization organization = new Organization();
                                organization.setOrgId(orgId);
                                organization.setOrgName(org.getOrgName());
                                organizationList.add(organization);
                            }
                        }
                    });
                    //orgIdDepartmentList = orgIdDepartmentList.stream().filter(Objects::nonNull).filter(s->orgList.contains(s)).collect(Collectors.toList());
                    mv.addObject("orgList", organizationList);
                    if(CollectionUtils.isNotEmpty(organizationList)){
                        List<Integer> departList = organizationList.stream().filter(Objects::nonNull).map(Organization::getOrgId).distinct().collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(departList)){
                            tapa.setDepartmentIdList(departList);
                        }
                    }

                }
            }
            if (ErpConst.ONE.equals(tapa.getIsShow())||ErpConst.ONE.equals(user.getIsAdmin())){
                mv.addObject("orgList", orgList);
                if(CollectionUtils.isNotEmpty(orgList)){
                    List<Integer> departList = orgList.stream().filter(Objects::nonNull).map(Organization::getOrgId).distinct().collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(departList)){
                        tapa.setDepartmentIdList(departList);
                    }
                }
            }
            //回显部门
            mv.addObject("departmentInfoList", tapa.getDepartmentInfo());

            //销售不查
            List<TraderCustomer> customerApplyList = new ArrayList<>();
            if (ObjectUtils.notEmpty(tapa.getIsShow())||ErpConst.ONE.equals(user.getIsAdmin())){
                //获取客户List
                customerApplyList = traderCustomerService.getCustomerByInfo(tapa);
            }

			//traderId->customerId
			List<Long> traderCustomerId = new ArrayList<>();

			List<Long>  traderCustomerCreatorId =  traderCreditService.getCustomerBIllPeriodApplyByCreator(tapa.getStatus(),user.getUserId());
			if (CollectionUtils.isNotEmpty(traderCustomerCreatorId)){
				traderCustomerId.addAll(traderCustomerCreatorId);
			}

            if(CollectionUtils.isNotEmpty(customerApplyList)){
				for(TraderCustomer traderCustomer : customerApplyList){
					if (null != traderCustomer.getTraderCustomerId()){
						traderCustomerId.add(Long.valueOf(traderCustomer.getTraderCustomerId()));
					}
				}
			}
			mv.addObject("tapa",tapa);

			CustomerBillPeriodApplyListQueryDto queryDto = new CustomerBillPeriodApplyListQueryDto();
			//待当前登录用户审核的账期申请的客户
			if (tapa.getNeedUserCheck() != null && tapa.getNeedUserCheck() == 1){
				List<VerifiesInfo> verifiesInfos = verifiesInfoMapper.getVerifiesInfoWaitUserCheck("T_CUSTOMER_BILL_PERIOD_APPLY",
						user.getUsername(),tapa.getNeedUserCheck() == 1);
				if (verifiesInfos.size() == 0) {
					return mv;
				}
				List<Long> billPeriodApplyIdList = verifiesInfos.stream()
						.filter(item -> Arrays.asList(item.getVerifyUsername().split(",")).contains(user.getUsername()))
						.map(VerifiesInfo::getRelateTableKey)
						.map(Long::valueOf)
						.collect(Collectors.toList());
				if (billPeriodApplyIdList.size() == 0) {
					return mv;
				}
				queryDto.setCustomerBillPeriodApplyIdList(billPeriodApplyIdList);
			}

            if(CollectionUtils.isNotEmpty(traderCustomerId)) {
				queryDto.setBillPeriodType(tapa.getBillPeriodType());
				queryDto.setBillPeriodStart(tapa.getStartDate());
				queryDto.setBillPeriodEnd(tapa.getEndDate());
				if (null != tapa.getStatus() && tapa.getStatus() != -1) {
					queryDto.setCheckStatus(tapa.getStatus());
				}
				queryDto.setCustomerIdList(traderCustomerId);
				if (null != tapa.getStatus() && tapa.getStatus() == 0) {
					queryDto.setCreatorId(user.getUserId());
				}

				//获取客户账期信息
				CustomerBillPeriodApplyListDto customerBillPeriodApplyListDto = traderCreditService.getCustomerBillPeriodApplyList(queryDto, page);
				if (null != customerBillPeriodApplyListDto && null != customerBillPeriodApplyListDto.getCustomerBillPeriodApplyItemDtoList()) {

					List<CustomerBillPeriodApplyItemVo> customerBillListVo = traderCustomerService.getCustomerInfoByCustomerId(customerBillPeriodApplyListDto.getCustomerBillPeriodApplyItemDtoList());
					mv.addObject("customerApplyList", customerBillListVo);
				}
				mv.addObject("page", customerBillPeriodApplyListDto.getPage());

			}
		} catch (Exception e) {
			logger.error("accountPeriodApplyList:", e);
			return null;
		}

		return mv;
	}

	/**
	 * <b>Description:</b><br> 查询账期申请记录列表信息
	 * @param request
	 * @param tapa
	 * @param session
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月1日 下午2:01:48
	 */
	@ResponseBody
	@RequestMapping(value="accountPeriodApplyList")
	public ModelAndView accountPeriodApplyList(HttpServletRequest request,TraderAccountPeriodApply tapa,HttpSession session,
			@RequestParam(required = false, value="startTime") String startTime,
			@RequestParam(required = false, value="endTime") String endTime,
			@RequestParam(required = false, defaultValue = "1") Integer pageNo,@RequestParam(required = false) Integer pageSize){
		ModelAndView mv = new ModelAndView();
		
		try {
			
			if(StringUtils.isNoneBlank(startTime)){
				tapa.setStartDate(DateUtil.convertLong(startTime + " 00:00:00",""));
			}
			if(StringUtils.isNoneBlank(endTime)){
				tapa.setEndDate(DateUtil.convertLong(endTime + " 23:59:59",""));
			}
			mv.addObject("startTime", startTime);mv.addObject("endTime", endTime);
			User user = (User)session.getAttribute(Consts.SESSION_USER);
			mv.addObject("loginUser",user);//登陆用户
			//申请人员列表
			List<Integer> list = new ArrayList<>();
			list.add(SysOptionConstant.ID_310);//销售
			list.add(SysOptionConstant.ID_311);//采购
			List<User> userList = userService.getMyUserList(user,list,false);
			mv.addObject("userList",userList);
			//默认展示审核中的
			if(null == request.getParameter("status") || request.getParameter("status") == ""){
			    tapa.setStatus(0);
			}
			Page page = getPageTag(request,pageNo,pageSize);
			
			tapa.setCompanyId(user.getCompanyId());
			//判断当前用户是否在审核人名单中
			Integer position = 0;
			if(user.getPositions() != null){
			     position = user.getPositions().get(0).getType();
			}
			
			if(position == 314){
			    tapa.setValidUserName(user.getUsername());
			}

			//VDERP-7302 供应商账期类型
			tapa.setTraderType(ErpConst.TWO);
			Map<String,Object> map = accountPeriodService.getAccountPeriodApplyListPage(tapa,page);
			
			List<TraderAccountPeriodApply> accountPeriodApplyList = (List<TraderAccountPeriodApply>)map.get("list");
			if(accountPeriodApplyList!=null && !accountPeriodApplyList.isEmpty()){
				List<Integer> userIds = new ArrayList<>();
				for(int i=0;i<accountPeriodApplyList.size();i++){
					userIds.add(accountPeriodApplyList.get(i).getCreator());
					if(null != accountPeriodApplyList.get(i).getVerifyUsername()){
					    List<String> verifyUsernameList = Arrays.asList(accountPeriodApplyList.get(i).getVerifyUsername().split(","));  
					    accountPeriodApplyList.get(i).setVerifyUsernameList(verifyUsernameList);
					}
				}
				List<User> creatorUserList = userService.getUserByUserIds(userIds);
				mv.addObject("creatorUserList",creatorUserList);
			}

			mv.addObject("accountPeriodApplyList",accountPeriodApplyList);
			mv.addObject("tapa",tapa);

			mv.addObject("page", (Page)map.get("page"));
			
			//发票类型
			List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_464);
			mv.addObject("invoiceTypeList", invoiceTypeList);
		} catch (Exception e) {
			logger.error("accountPeriodApplyList:", e);
			return null;
		}
		
		mv.setViewName("finance/accountPeriod/list_accountPeriod");
		return mv;
	}
	
	/**
	 * <b>Description:</b><br> 导出账期申请记录1
	 * @param model
	 * @param request
	 * @param tapa
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年11月20日 下午6:09:37
	 */
	@RequestMapping(value = "/export_{1}", method = RequestMethod.GET)
	public String report(Model model,HttpServletRequest request,HttpServletResponse response,TraderAccountPeriodApply tapa) {
		try {
			List<TraderAccountPeriodApply> accountPeriodApplyList = accountPeriodService.exportAccountPeriodApplyList(tapa);
			if(accountPeriodApplyList == null || accountPeriodApplyList.size() == 0){
				return null;
			}
			// 报表数据源  
			JRDataSource jrDataSource = new JRBeanCollectionDataSource(accountPeriodApplyList);
			
			model = IreportExport.exportOut(model, "/WEB-INF/ireport/jasper/accountPeriod.jasper", jrDataSource, "xls");
			
			// 动态指定报表模板url  
			/*model.addAttribute("url", "/WEB-INF/ireport/jasper/accountPeriod.jasper");
			model.addAttribute("format", "xls"); // 报表格式  PDF，XLS，RTF，HTML，CSV
			model.addAttribute("jrDataSource", jrDataSource);*/
			
		} catch (Exception e) {
			logger.error("export_", e);
		}
	    return "iReportView"; // 对应jasper-defs.xml中的bean id  
	}
	
	/**
	 * <b>Description:</b><br> 导出账期申请记录2
	 * @param model
	 * @param request
	 * @param response
	 * @param tapa
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年11月21日 下午7:02:08
	 */
	@RequestMapping(value = "/exportAccountperiod", method = RequestMethod.GET)
	public void exportAccountperiod(Model model,HttpServletRequest request,HttpServletResponse response,TraderAccountPeriodApply tapa) {
		
		try {
			List<TraderAccountPeriodApply> accountPeriodApplyList = accountPeriodService.exportAccountPeriodApplyList(tapa);
			if(accountPeriodApplyList != null && accountPeriodApplyList.size() > 0){
				JRDataSource dataSource = new JRBeanCollectionDataSource(accountPeriodApplyList);
				IreportExport.exportWrite(request, response, "/WEB-INF/ireport/jrxml/账期申请.jrxml", dataSource, "账期列表.xls");
			}
		} catch (Exception e) {
			logger.error("exportAccountperiod:", e);
		}

	}
	
	/**
	 * <b>Description:</b><br> 根据主键获取账期申请信息
	 * @param request
	 * @param tapa
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月2日 上午10:22:36
	 */
	@ResponseBody
	@RequestMapping(value="getAccountPeriodApply")
	public ModelAndView getAccountPeriodApply(HttpServletRequest request,TraderAccountPeriodApply tapa){
		ModelAndView mv = new ModelAndView();
		User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		mv.addObject("curr_user", curr_user);
		Integer billPeriodApplyId = tapa.getBillPeriodApplyId();
		Integer billPeriodType = tapa.getBillPeriodType();
		Integer traderCustomerId = tapa.getTraderCustomerId();
		String verifyComment = tapa.getVerifyComment();
		Integer traderType = tapa.getTraderType();

		Integer supplierBillPeroidApplyId=null;
		try {
			if(ErpConst.ONE.equals(tapa.getTraderType())){
				TraderCustomer traderCustomerQuery = traderCustomerService.selectByPrimaryKey(traderCustomerId);
				tapa.setTraderId(traderCustomerQuery.getTraderId());
				mv.setViewName("finance/accountPeriod/audit_accountPeriod_cust");
				tapa.setTraderAccountPeriodApplyId(billPeriodApplyId);
			}else {
				supplierBillPeroidApplyId=tapa.getTraderAccountPeriodApplyId();
				//供应商保持原有逻辑
				tapa = accountPeriodService.getAccountPeriodApply(tapa);
				mv.setViewName("finance/accountPeriod/audit_accountPeriod");
			}
			mv.addObject("tapa", tapa);
			Integer customerNature = null;
			TraderCertificateVo tc = new TraderCertificateVo();
			tc.setTraderId(tapa.getTraderId());
			if(tapa.getTraderId()==null){
				logger.error("getAccountPeriodApply::traderId{}",tapa.getTraderId());
			}
			if(ErpConst.ONE.equals(tapa.getTraderType())){//客户
				//根据客户ID查询客户信息
				TraderCustomerVo customer = traderCustomerService.getTraderCustomerInfo(tapa.getTraderId());
				mv.addObject("customer", customer);
				tc.setTraderType(ErpConst.ONE);
				if(customer.getCustomerNature() == 466){
					tc.setCustomerType(1);
				}else{
					tc.setCustomerType(2);
				}
				customerNature = customer.getCustomerNature();
			}else{//供应商
				TraderSupplier ts = new TraderSupplier();
				ts.setTraderId(tapa.getTraderId());
				TraderSupplierVo traderSupplier = traderSupplierService.getTraderSupplierInfo(ts);
				mv.addObject("traderSupplier", traderSupplier);
				tc.setTraderType(ErpConst.TWO);
			}
			
			Map<String, Object> map = traderCustomerService.getFinanceAndAptitudeByTraderId(tc, "all");
			//资质信息
			if(map!=null){
//				Integer customerProperty = Integer.valueOf(map.get("customerProperty").toString());
				List<TraderCertificateVo> bus = null;
				// 营业执照信息
				if(map.containsKey("business")){
//			JSONObject json=JSONObject.fromObject(map.get("business"));
//			bus=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
//			mav.addObject("business", bus);
					bus=(List<TraderCertificateVo>)map.get("business");
					if(!CollectionUtils.isEmpty(bus)) {
						mv.addObject("business", bus.get(0));
					}
				}
				// 税务登记信息
				TraderCertificateVo tax = null;
				if(map.containsKey("tax")){
					JSONObject json=JSONObject.fromObject(map.get("tax"));
					tax=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
					mv.addObject("tax", tax);
				}
				// 组织机构信息
				TraderCertificateVo orga = null;
				if(map.containsKey("orga")){
					JSONObject json=JSONObject.fromObject(map.get("orga"));
					orga=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
					mv.addObject("orga", orga);
				}
				mv.addObject("traderType", tapa.getTraderType());
				if(ErpConst.TWO.equals(tapa.getTraderType())){//供应商
					// 二类医疗资质
					List<TraderCertificateVo> twoMedicalList = null;
					if(map.containsKey("twoMedical")){
//						JSONObject json=JSONObject.fromObject(map.get("twoMedical"));
					    twoMedicalList=(List<TraderCertificateVo>) map.get("twoMedical");
						mv.addObject("twoMedicalList", twoMedicalList);
					}
					// 三类医疗资质
					TraderCertificateVo threeMedical1 = null;
//					if(map.containsKey("threeMedical")){
//						JSONObject json=JSONObject.fromObject(map.get("threeMedical"));
//						threeMedical=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
//						mv.addObject("threeMedical", threeMedical);
//					}
					// 三类医疗资质
					List<TraderCertificateVo> threeMedicalList = null;
					if(map.containsKey("threeMedical")){
						try{
							// JSONObject json=JSONObject.fromObject(map.get("threeMedical"));
							threeMedicalList=(List<TraderCertificateVo>)map.get("threeMedical");
							if (CollectionUtils.isNotEmpty(threeMedicalList)) {
								mv.addObject("threeMedical", threeMedicalList.get(0));
							}
						}catch(Exception e){
							logger.error("threeMedical",e);
						}
					}


					List<TraderMedicalCategoryVo> list=null;
					if(map.containsKey("medicalCertificate")){
						JSONArray jsonArray=JSONArray.fromObject(map.get("medicalCertificate"));
						list=(List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
						mv.addObject("medicalCertificates", list);
					}
					
					List<TraderMedicalCategoryVo> two=null;
					if(map.containsKey("two")){
						JSONArray jsonArray=JSONArray.fromObject(map.get("two"));
						two=(List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
						mv.addObject("two", two);
					}
					List<TraderMedicalCategoryVo> three=null;
					if(map.containsKey("three")){
						JSONArray jsonArray=JSONArray.fromObject(map.get("three"));
						three=(List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
						mv.addObject("three", three);
					}
					
					// 医疗器械生产许可证
					TraderCertificateVo product = null;
					if(map.containsKey("product")){
						JSONObject json=JSONObject.fromObject(map.get("product"));
						product=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
						mv.addObject("product", product);
					}
					// 医疗器械经营许可证
					TraderCertificateVo operate = null;
					if(map.containsKey("operate")){
						JSONObject json=JSONObject.fromObject(map.get("operate"));
						operate=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
						mv.addObject("operate", operate);
					}
					// 销售授权书与授权销售人
					TraderCertificateVo saleAuth = null;
					if (map.containsKey("saleAuth"))
					{
						JSONObject json = JSONObject.fromObject(map.get("saleAuth"));
						saleAuth = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
						mv.addObject("saleAuth", saleAuth);
					}
					// 品牌授权书
                    List<TraderCertificateVo> brandList = null;
                    if(map.containsKey("brandBookList")){
                        brandList=(List<TraderCertificateVo>) map.get("brandBookList");
                        mv.addObject("brandBookList", brandList);
                    }
                 // 其他医疗资质
                    List<TraderCertificateVo> otherList = null;
                    if(map.containsKey("otherList")){
                        otherList=(List<TraderCertificateVo>) map.get("otherList");
                        mv.addObject("otherList", otherList);
                    }
				}else{//客户
					/*Integer customerProperty = null;
					customerProperty = traderCustomerService.getCustomerCategory(tapa.getTraderId());
					mv.addObject("customerProperty", customerProperty);*/
					
					if(customerNature == 465){
						// 二类医疗资质
						List<TraderCertificateVo> twoMedicalList = null;
						if(map.containsKey("twoMedical")){
//							JSONObject json=JSONObject.fromObject(map.get("twoMedical"));
						    twoMedicalList=(List<TraderCertificateVo>) map.get("twoMedical");
							mv.addObject("twoMedicalList", twoMedicalList);
						}
						// 三类医疗资质
//						TraderCertificateVo threeMedical = null;
//						if(map.containsKey("threeMedical")){
//							JSONObject json=JSONObject.fromObject(map.get("threeMedical"));
//							threeMedical=(TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
//							mv.addObject("threeMedical", threeMedical);
//						}

						// 三类医疗资质
						List<TraderCertificateVo> threeMedicalList = null;
						if(map.containsKey("threeMedical")){
							try{
								// JSONObject json=JSONObject.fromObject(map.get("threeMedical"));
								threeMedicalList=(List<TraderCertificateVo>)map.get("threeMedical");
								if (!CollectionUtils.isEmpty(threeMedicalList)) {
									mv.addObject("threeMedical", threeMedicalList.get(0));
								}
							}catch(Exception e){
								logger.error("threeMedical",e);
							}
						}



						List<TraderMedicalCategoryVo> list=null;
						if(map.containsKey("medicalCertificate")){
							JSONArray jsonArray=JSONArray.fromObject(map.get("medicalCertificate"));
							list=(List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
							mv.addObject("medicalCertificates", list);
						}
						
						List<TraderMedicalCategoryVo> two=null;
						if(map.containsKey("two")){
							JSONArray jsonArray=JSONArray.fromObject(map.get("two"));
							two=(List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
							mv.addObject("two", two);
						}
						
						List<TraderMedicalCategoryVo> three=null;
						if(map.containsKey("three")){
							JSONArray jsonArray=JSONArray.fromObject(map.get("three"));
							three=(List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray, TraderMedicalCategoryVo.class);
							mv.addObject("three", three);
						}
						// 品牌授权书
                        List<TraderCertificateVo> brandList = null;
                        if(map.containsKey("brandBookList")){
                            brandList=(List<TraderCertificateVo>) map.get("brandBookList");
                            mv.addObject("brandBookList", brandList);
                        }
                     // 其他医疗资质
                        List<TraderCertificateVo> otherList = null;
                        if(map.containsKey("otherList")){
                            otherList=(List<TraderCertificateVo>) map.get("otherList");
                            mv.addObject("otherList", otherList);
                        }
					} 
					if(customerNature == 466){
						// 医疗机构执业许可证
						List<TraderCertificateVo> practiceList = null;
						if(map.containsKey("practice")){
//							JSONObject json=JSONObject.fromObject(map.get("practice"));
						    practiceList=(List<TraderCertificateVo>) map.get("practice");
							mv.addObject("practiceList", practiceList);
						}
					}
				}
			}
			//查询审核记录
			TraderAmountBill tab = new TraderAmountBill();
			tab.setTraderType(tapa.getTraderType());
			tab.setTraderId(tapa.getTraderId());
			tab.setAmountType(ErpConst.ONE);
			tab.setEvent(SysOptionConstant.ID_433);
			List<TraderAmountBill> amountBillList = accountPeriodService.getTraderAmountBillList(tab);
			if(amountBillList!=null && !amountBillList.isEmpty()){
				List<Integer> userIdList = new ArrayList<>();
				for(TraderAmountBill bill:amountBillList){
					userIdList.add(bill.getCreator());
				}
				List<User> userList = userService.getUserByUserIds(userIdList);
				mv.addObject("userList", userList);
				mv.addObject("amountBillList", amountBillList);
			}

			Map<String, Object> historicInfo;
			if (ErpConst.ONE.equals(tapa.getTraderType())) {
				historicInfo=actionProcdefService.getHistoric(processEngine, ProcessConstants.APPLY_ACCOUNT_PERIOD_PROC_KEY+"_"+tapa.getTraderAccountPeriodApplyId());
			} else {
				historicInfo=actionProcdefService.getHistoric(processEngine, "customerPeriodVerify_"+ supplierBillPeroidApplyId);
			}

			Task taskInfo = (Task) historicInfo.get("taskInfo");
			mv.addObject("taskInfo", historicInfo.get("taskInfo"));
			mv.addObject("startUser", historicInfo.get("startUser"));
			mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
			// 最后审核状态
			mv.addObject("endStatus",historicInfo.get("endStatus"));
			mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
			mv.addObject("commentMap", historicInfo.get("commentMap"));
		    	
		    	String verifyUsers = null;
		    	if(null!=taskInfo){
		    	    Map<String, Object> taskInfoVariables= actionProcdefService.getVariablesMap(taskInfo);
		    	    verifyUsers = (String) taskInfoVariables.get("verifyUsers");

					if (ErpConst.ONE.equals(tapa.getTraderType())) {
						mv.addObject("customerId", MapUtils.getLongValue(taskInfoVariables, "customerId"));
						mv.addObject("billPeriodType", MapUtils.getInteger(taskInfoVariables,"accountPeriodType"));
					}
		    	}
		    	mv.addObject("verifyUsers", verifyUsers);
			//-----------------VDERP-7564账期申请审核及查看(新)--------------------//
			if( ErpConst.ONE.equals(tapa.getTraderType())){
				//供应商不走新客户账期逻辑
				CustomerBillPeriodApply customerBillPeriodApply = customerBillPeriodApplyService.selectByPrimaryKey(Long.valueOf(billPeriodApplyId));
				mv.addObject("customerBillPeriodApply",customerBillPeriodApply);
				List<CustomerBillPeriodDetailsDto> customerBillPeriodDetailsDtos = new ArrayList<>();
				if(customerBillPeriodApply.getBillPeriodId() != null){
					customerBillPeriodDetailsDtos = customerBillPeriodService.getCustomerBillPeriodDetailsByType(1,Long.valueOf(traderCustomerId),billPeriodType)
							.stream().filter(item -> customerBillPeriodApply.getBillPeriodId().equals(item.getBillPeriodId())).collect(Collectors.toList());
				}
				if(!CollectionUtils.isEmpty(customerBillPeriodDetailsDtos)){
					mv.addObject("customerBillPeriodDetailsDto",customerBillPeriodDetailsDtos.get(0));
				}else {
					mv.addObject("customerBillPeriodDetailsDto",null);
				}
				User create = userService.getUserById(customerBillPeriodApply.getCreator());
				if(null != create){
				    mv.addObject("createId",create.getUserId());
				    mv.addObject("createOrgId",create.getOrgId());
                    mv.addObject("create",create.getUsername());
                }

				Saleorder saleorder = new Saleorder();
				if(customerBillPeriodApply != null && customerBillPeriodApply.getRelatedOrderId() != null){
					saleorder = saleorderService.getSaleOrderById(customerBillPeriodApply.getRelatedOrderId());
				}
				mv.addObject("saleorderNo",saleorder.getSaleorderNo());
				mv.addObject("saleorderId",saleorder.getSaleorderId());
				mv.addObject("verifyComment",verifyComment);
			}
			//-----------------VDERP-7564账期申请审核及查看(新)--------------------//
		} catch (Exception e) {
			logger.error("getAccountPeriodApply:", e);
			return null;
		}
		return mv;
	}
	
	/**
	 * <b>Description:</b><br> 账期审核原因填写初始化
	 * @param request
	 * @param tapa
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月2日 下午6:08:59
	 */
	@ResponseBody
	@RequestMapping(value="accountPeriodAuditReasonInit")
	public ModelAndView accountPeriodAuditReasonInit(HttpServletRequest request,TraderAccountPeriodApply tapa){
		ModelAndView mv = new ModelAndView();
		mv.addObject("tapa", tapa);
		mv.setViewName("finance/accountPeriod/audit_accountPeriod_reason");
		return mv;
	}
	
	/**
	 * <b>Description:</b><br> 修改账期审核状态
	 * @param request
	 * @param tapa
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年8月3日 下午4:23:57
	 */
	@ResponseBody
	@RequestMapping(value="editAccountPeriodAudit")
	@SystemControllerLog(operationType = "edit",desc = "修改账期审核状态")
	public ResultInfo<?> editAccountPeriodAudit(HttpServletRequest request,TraderAccountPeriodApply tapa){
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			tapa.setUpdater(user.getUserId());
			tapa.setModTime(DateUtil.sysTimeMillis());
		}
		try {
			return accountPeriodService.editAccountPeriodAudit(tapa);
		} catch (Exception e) {
			logger.error("trader account period apply edit:", e);
			return new ResultInfo<>();
		}
	}
	/**
	 * <b>Description:</b><br> 客户账期记录
	 * @param request
	 * @param ap
	 * @param startTime
	 * @param endTime
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年9月5日 下午5:25:05
	 */
	@ResponseBody
	@RequestMapping(value="getCustomerAccountListPage")
	public ModelAndView getCustomerAccountListPage(HttpServletRequest request,AccountPeriod ap,
			@RequestParam(required = false, value="startTime") String startTime,
			@RequestParam(required = false, value="endTime") String endTime,
			@RequestParam(required = false, defaultValue = "1") Integer pageNo,@RequestParam(required = false) Integer pageSize){
		ModelAndView mv = new ModelAndView();
		mv.addObject("ap",ap);
		try {
            mv.addObject("ezDomain", ezDomain);
            mv.addObject("customerExport", customerExport.split(","));

			if(StringUtils.isNoneBlank(startTime)){
				ap.setStartDate(DateUtil.convertLong(startTime + " 00:00:00",""));
			}
			if(StringUtils.isNoneBlank(endTime)){
				ap.setEndDate(DateUtil.convertLong(endTime + " 23:59:59",""));
			}
			mv.addObject("startTime", startTime);mv.addObject("endTime", endTime);

			if( null != ap.getTraderUserId()){
				List<Integer> traderIdList = userService.getTraderIdListByUserId(ap.getTraderUserId(), ErpConst.ONE);// 1客户，2供应商
				if(traderIdList!=null && !traderIdList.isEmpty()){// 销售人员无客户，默认不出数据
					ap.setTraderIdList(traderIdList);
				}
			}

			User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
			if (user.getPositType()!=null && user.getPositType().intValue()==SysOptionConstant.ID_310){//销售
				mv.addObject("roleFinance",1);
			}
			if (null==user.getCompanyId()){
				user.setCompanyId(ErpConst.ONE);
			}
			ap.setCompanyId(user.getCompanyId());

			//获取当前销售用户下级职位用户
			List<Integer> positionType = new ArrayList<>();
			positionType.add(SysOptionConstant.ID_310);
			//增加质管部和售后部
			positionType.add(SysOptionConstant.ID_589);
			positionType.add(SysOptionConstant.ID_312);
			List<User> ascriptionUserList = userService.getMyUserList(user, positionType, false);//归属销售
			mv.addObject("ascriptionUserList", ascriptionUserList);

			List<Integer> userIdList = ascriptionUserList.stream().map(User::getUserId).collect(Collectors.toList());
			ap.setUserIdList(userIdList);

			//发票类型
			List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);
			mv.addObject("invoiceTypeList", invoiceTypeList);

			//归属部门
			List<Organization> orgList = orgService.getSalesOrgListOrderByStruct(SysOptionConstant.ID_310, user.getCompanyId());

			//增加质管部和售后部
			List<Organization> afterorgList = orgService.getSalesOrgListOrderByStruct(SysOptionConstant.ID_312, user.getCompanyId());
			List<Organization> riskorgList = orgService.getSalesOrgListOrderByStruct(SysOptionConstant.ID_589, user.getCompanyId());

			orgList.addAll(afterorgList);
			orgList.addAll(riskorgList);

			List<Integer> orgIdDepartmentList = orgService.getCustomerAccountOrgInfo(user.getOrgId(),ap);
			if(CollectionUtils.isNotEmpty(orgIdDepartmentList) && !ErpConst.ZERO.equals(ap.getIsShow())){
				List<Organization> list = new ArrayList<>();
				if(ErpConst.TWO.equals(ap.getIsShow())){
					orgIdDepartmentList.stream().filter((Objects::nonNull)).forEach(orgId->{
						for (Organization org :orgList)
						{
							if (org.getOrgId().equals(orgId)){
								Organization organization = new Organization();
								organization.setOrgId(orgId);
								organization.setOrgName(org.getOrgName());
								list.add(organization);
							}
						}
					});
					//orgIdDepartmentList = orgIdDepartmentList.stream().filter(Objects::nonNull).filter(s->orgList.contains(s)).collect(Collectors.toList());
					mv.addObject("orgList", list);
				}
			}
            if (ErpConst.ONE.equals(ap.getIsShow())||ErpConst.ONE.equals(user.getIsAdmin())){
				mv.addObject("orgList", orgList);
			}

			//回显部门
			mv.addObject("departmentInfoList", ap.getDepartmentInfo());

			Page page = getPageTag(request,pageNo,pageSize);

			if (CollectionUtils.isNotEmpty(ap.getDepartmentInfo()) && ap.getDepartmentInfo().size() >1){
				String url = page.getSearchUrl().substring(0,page.getSearchUrl().indexOf("departmentInfo")) +"departmentInfo=";
				for (Integer i:ap.getDepartmentInfo()){
					if (ap.getDepartmentInfo().indexOf(i) != 0){
						url= url+",";
					}
					url= url+i;
				}
				Integer len = page.getSearchUrl().indexOf("&", page.getSearchUrl().indexOf("departmentInfo="));
				if (len > 0 ){
					url = url + page.getSearchUrl().substring(len);
				}
				page.setSearchUrl( url);
			}

			//获取订单List
            if (CollectionUtils.isNotEmpty(ap.getDepartmentInfo())){
               List<Integer> departmentOrderInfo = ap.getDepartmentInfo().stream().filter(Objects::nonNull).map(s->orgService.getChildrenByParentId(s,ap.getCompanyId())).filter(Objects::nonNull).flatMap(Collection::stream).distinct().collect(Collectors.toList());
               if (CollectionUtils.isNotEmpty(departmentOrderInfo)){
                   ap.setDepartmentOrderInfo(departmentOrderInfo);
               }
            }
			List<Saleorder> orderList = traderCustomerService.getSaleOrderInfoBySaleOrderInfo(ap);
			if (CollectionUtils.isEmpty(orderList)){
				mv.setViewName("finance/accountPeriod/list_customer_account_new");
				return mv;
			}

			List<Integer> traderIdList = new ArrayList<>();
			List<Long> orderIds = new ArrayList<>();
			orderList.stream().forEach(list->{
				traderIdList.add(list.getTraderId());
				orderIds.add(Long.valueOf(list.getSaleorderId()));
			});

			if(CollectionUtils.isNotEmpty(traderIdList)){
				List<User> userList = userService.getUserByTraderIdList(traderIdList,ErpConst.ONE);//1客户，2供应商
				mv.addObject("userList", userList);
			}

			CustomerBillPeriodListQueryDto customerBillPeriodListQueryDto = new CustomerBillPeriodListQueryDto();
			customerBillPeriodListQueryDto.setOrderIds(orderIds);
			customerBillPeriodListQueryDto.setOverdueState(ap.getOverdueState());
			customerBillPeriodListQueryDto.setBillPeriodType(ap.getBillType());
			customerBillPeriodListQueryDto.setIsReturn(ap.getBillNotPaid());

			//获取客户账期信息
			CustomerBillPeriodListViewDto customerAccountList = traderCreditService.getCustomerBillPeriodListView(customerBillPeriodListQueryDto, page);

			if (customerAccountList != null && customerAccountList.getPeriodRecordStatementDto() != null){
				mv.addObject("periodUsedAmount", customerAccountList.getPeriodRecordStatementDto().getPeriodUsedAmount());
				mv.addObject("unReturnedAmount", customerAccountList.getPeriodRecordStatementDto().getUnReturnedAmount());
				//逾期未还金额
				mv.addObject("unReturnedOverDueAmount",customerAccountList.getPeriodRecordStatementDto().getUnReturnedOverDueAmount());
			}
			if (customerAccountList != null && customerAccountList.getPeriodOrderStatementDto() != null){
				mv.addObject("orderNum", customerAccountList.getPeriodOrderStatementDto().getOrderNum());
				mv.addObject("totalAmount", customerAccountList.getPeriodOrderStatementDto().getTotalAmount());
			}
			if (null != customerAccountList.getCustomerBillPeriodItemDtoList() && customerAccountList.getCustomerBillPeriodItemDtoList().size()>0){
				//返回客户账期+订单信息
				List<CustomerBillPeriodDetailVo> voList = traderCustomerService.getTraderCustomerInfoNew(customerAccountList.getCustomerBillPeriodItemDtoList(),ap);

				if (CollectionUtils.isNotEmpty(voList)){
					List<Integer> orgIdList = voList.stream().filter(s->null != s.getOrgId()).map(CustomerBillPeriodDetailVo::getOrgId).distinct().collect(Collectors.toList());
					List<Organization> orgNameList = new ArrayList<>();
					if (CollectionUtils.isNotEmpty(orgIdList)){
						orgIdList.stream().forEach(orgId->{
							String orgName = orgService.getOrgName(orgId);
							Organization organization = new Organization();
							organization.setOrgId(orgId);
							organization.setOrgName(orgName);
							orgNameList.add(organization);

						});
						mv.addObject("orgNameList",orgNameList);
					}
				}

				mv.addObject("customerAccountList",voList);

				//订单统计信息
				if (CollectionUtils.isEmpty(voList)){
					mv.addObject("orderNumPage",0);
					mv.addObject("orderTotalAmount", BigDecimal.ZERO);
				} else {
					HashMap<String, CustomerBillPeriodDetailVo> resultSet = new HashMap<>();
					for (CustomerBillPeriodDetailVo customerBillPeriodDetailVo : voList) {
						if (!resultSet.containsKey(customerBillPeriodDetailVo.getSaleorderNo())){
							resultSet.put(customerBillPeriodDetailVo.getSaleorderNo(), customerBillPeriodDetailVo);
						}
					}
					mv.addObject("orderNumPage", resultSet.size());

					BigDecimal orderTotalAmount= BigDecimal.ZERO;
					for (CustomerBillPeriodDetailVo value : resultSet.values()) {
						orderTotalAmount = orderTotalAmount.add(value.getTotalAmount() != null ? value.getTotalAmount() : BigDecimal.ZERO);
					}
					mv.addObject("orderTotalAmount", orderTotalAmount);
				}
				mv.addObject("page", customerAccountList.getPage());
				mv.addObject("sysdate", DateUtil.gainNowDate());
			}
		} catch (Exception e) {
			logger.error("getCustomerAccountListPage:", e);
			return null;
		}
		
		mv.setViewName("finance/accountPeriod/list_customer_account_new");
		return mv;
	}
	
	/**
	 * <b>Description:</b><br> 供应商账期记录
	 * @param request
	 * @param ap
	 * @param startTime
	 * @param endTime
	 * @param pageNo
	 * @param pageSize
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年9月7日 上午10:13:50
	 */
	@ResponseBody
	@RequestMapping(value="getSupplierAccountListPage")
	public ModelAndView getSupplierAccountListPage(HttpServletRequest request, AccountPeriod ap,
			@RequestParam(required = false, value = "startTime") String startTime,
			@RequestParam(required = false, value = "endTime") String endTime,
			@RequestParam(required = false, defaultValue = "1") Integer pageNo,
			@RequestParam(required = false) Integer pageSize) {
		ModelAndView mv = new ModelAndView();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		try {
			if (StringUtils.isNoneBlank(startTime)) {
				ap.setStartDate(DateUtil.convertLong(startTime + " 00:00:00", ""));
			}
			if (StringUtils.isNoneBlank(endTime)) {
				ap.setEndDate(DateUtil.convertLong(endTime + " 23:59:59", ""));
			}
			mv.addObject("startTime", startTime);
			mv.addObject("endTime", endTime);

			if (ap.getTraderUserId() != null) {
				List<Integer> traderIdList = userService.getTraderIdListByUserId(ap.getTraderUserId(), ErpConst.TWO);// 1客户，2供应商
				if (traderIdList != null && !traderIdList.isEmpty()) {// 销售人员无客户，默认不出数据
					ap.setTraderIdList(traderIdList);
				}else{
					traderIdList = new ArrayList<Integer>(){{add(-1);}};
					ap.setTraderIdList(traderIdList);
				}
			}

			// 获取当前销售用户下级职位用户
			List<Integer> positionType = new ArrayList<>();
			positionType.add(SysOptionConstant.ID_311);
			List<User> ascriptionUserList = userService.getMyUserList(user, positionType, false);// 归属销售
			mv.addObject("ascriptionUserList", ascriptionUserList);

			// 发票类型
			List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);
			mv.addObject("invoiceTypeList", invoiceTypeList);

			Page page = getPageTag(request, pageNo, pageSize);
			ap.setCompanyId(user.getCompanyId());
			Map<String, Object> map = accountPeriodService.getSupplierAccountListPage(ap, page);
			if (map != null) {
				mv.addObject("supplierAccountList", (List<AccountPeriod>) map.get("supplierAccountList"));
				// mv.addObject("customerRepayList",(List<TraderAccountPeriodApply>)map.get("customerRepayList"));
				mv.addObject("ap", (AccountPeriod) map.get("ap"));
				mv.addObject("userList", (List<User>) map.get("userList"));
				mv.addObject("page", (Page) map.get("page"));
				mv.addObject("sysdate", DateUtil.gainNowDate());
			}
		} catch (Exception e) {
			logger.error("getSupplierAccountListPage:", e);
			return null;
		}
		mv.setViewName("finance/accountPeriod/list_supplier_account");
		return mv;
	}
	
	/**
	 * 
	 * <b>Description:</b><br>
	 * 账期申请审核页面
	 * 
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2017年11月10日 下午1:39:42
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/complement")
	@NoNeedAccessAuthorization
	public ModelAndView complement(HttpSession session,Integer traderAccountPeriodApplyId, String taskId, Boolean pass,
								   @RequestParam(required = false)String verifyComment) {
		ModelAndView mv = new ModelAndView();
		mv.addObject("taskId", taskId);
		mv.addObject("pass", pass);
		mv.addObject("traderAccountPeriodApplyId", traderAccountPeriodApplyId);
		mv.addObject("verifyComment",verifyComment);
		mv.setViewName("finance/accountPeriod/complement");
		return mv;
	}

	/**
	 * 
	 * <b>Description:</b><br>
	 * 账期申请审核操作
	 * 
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2017年11月10日 下午1:39:42
	 */
	@FormToken(remove=true)
	@ResponseBody
	@RequestMapping(value = "/complementTask")
	public ResultInfo<?> complementTask(HttpServletRequest request, String taskId, String comment, Boolean pass,
			String verifyComment) {
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		try {
			customerAccountPeriodProcessService.completeTask(taskId,user.getUserId(), comment,  pass);
		} catch (Exception e) {
			logger.error("trader account period apply complementTask:", e);
			return ResultInfo.error("任务完成操作失败：" + e.getMessage());
		}

		return ResultInfo.success();
	}


	/**
	 *
	 * <b>Description:</b><br>
	 * 归还明细页面
	 *
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/returnDetailsView")
	public ModelAndView returnDetailsView(CustomerBillPeriodUseDetail detail) {
		ModelAndView mv = new ModelAndView();
		List<CustomerBillPeriodUseDetailVo> customerBillPeriodUseDetailVos = new ArrayList<>();
		if (null != detail && null != detail.getBillPeriodId() && null !=  detail.getRelatedId() && null != detail.getParentUseDetailId()){

			List<CustomerBillPeriodUseDetail> customerBillPeriodUseDetail = customerBillPeriodService.revertCustomerBillPeriodInfo(detail.getBillPeriodId(),detail.getRelatedId(),detail.getParentUseDetailId());
			if (CollectionUtils.isNotEmpty(customerBillPeriodUseDetail)){
				customerBillPeriodUseDetail.stream().filter(Objects::nonNull).forEach(useDetail->{
					CustomerBillPeriodUseDetailVo vo = new CustomerBillPeriodUseDetailVo();
					if (ErpConst.FOUR.equals(useDetail.getUseType())){
						if (null != useDetail.getRelatedId()){
							Integer relatedId = useDetail.getRelatedId().intValue();
							CapitalBill capitalBill =  traderCustomerService.selectBillInfo(relatedId);
							if(null != capitalBill){
								vo.setTranFlow(capitalBill.getCapitalBillNo());
							}
						}
					}
					if (ErpConst.THREE.equals(useDetail.getUseType())){
						if (null != useDetail.getRelatedId()){
							Integer relatedId = useDetail.getRelatedId().intValue();
							AfterSales afterSales = traderCustomerService.selectAfterInfo(relatedId);
							if(null != afterSales){
								vo.setAfterSalesNo(afterSales.getAfterSalesNo());
							}
						}
					}
					vo.setAmount(useDetail.getAmount());
					vo.setUseType(useDetail.getUseType());
					vo.setAddTime(useDetail.getAddTime());
					customerBillPeriodUseDetailVos.add(vo);
				});
			}
		}
		mv.addObject("customerBillPeriodUseDetail",customerBillPeriodUseDetailVos);
		mv.setViewName("finance/accountPeriod/returnDetailsView");
		return mv;
	}
	/**
	 *
	 * <b>Description:</b><br>
		 * 监管明细-账期详情页面
	 *
	 */
	@FormToken(save=true)
	@ResponseBody
	@RequestMapping(value = "/billPeriodDetailsView")
	public ModelAndView billPeriodDetailsView(CustomerBillPeriodDetailManageVo detail) {
		ModelAndView mv = new ModelAndView();
		mv.setViewName("finance/accountPeriod/billPeriodDetailsView");

		if (null != detail && null != detail.getParentUseDetailId()) {
            Long parentUseDetailId = detail.getParentUseDetailId();
            List<CustomerBillPeriodDetailManageVo> customerBillVos = new ArrayList<>();
            List<CustomerBillPeriodDetailManageDto> customerBillDto = customerBillPeriodService.getBillPeriodDetailManageInfo(parentUseDetailId);
            if (CollectionUtils.isNotEmpty(customerBillDto)) {
				customerBillDto.stream().filter(Objects::nonNull).filter(s->null!=s.getBillPeriodOverdueManagementCode())
						.filter(s->null!=s.getCalAmount()&&s.getCalAmount().compareTo(BigDecimal.ZERO)!=ErpConst.ZERO).forEach(dto->{
                    CustomerBillPeriodDetailManageVo vo = new CustomerBillPeriodDetailManageVo();
                    vo.setIsShow(ErpConst.ONE);
				    if (null!=dto.getAmount()&&dto.getAmount().compareTo(BigDecimal.ZERO)<ErpConst.ZERO){
				        vo.setIsShow(ErpConst.ZERO);
                    }else {
                        vo.setBillPeriodOverdueManagementCode(dto.getBillPeriodOverdueManagementCode());
                        vo.setType(dto.getType());

                        if (ErpConst.ONE.equals(dto.getType())||ErpConst.TWO.equals(dto.getType())){
                            if (null != dto.getOrderRelatedId()){
                                Integer relatedId = dto.getOrderRelatedId().intValue();
                                Saleorder saleorder =  traderCustomerService.selectBySaleOrderId(relatedId);
                                if (null != saleorder){
                                    vo.setOrderRelatedNo(saleorder.getSaleorderNo());
                                }
                            }
                        }
                        if (ErpConst.FIVE.equals(dto.getType())){
                            if (null != dto.getRelatedId()){
                                Integer relatedId = dto.getRelatedId().intValue();
                                CapitalBill capitalBill =  traderCustomerService.selectBillInfo(relatedId);
                                if (null != capitalBill){
                                    vo.setOrderRelatedNo(capitalBill.getCapitalBillNo());
                                }
                            }
                        }
                        if (ErpConst.THREE.equals(dto.getType())||ErpConst.FOUR.equals(dto.getType())){
                            if (null != dto.getRelatedId()){
                                Integer relatedId = dto.getRelatedId().intValue();
                                AfterSales afterSales = traderCustomerService.selectAfterInfo(relatedId);
                                if (null != afterSales){
                                    vo.setOrderRelatedNo(afterSales.getAfterSalesNo());
                                }
                            }
                        }
                        vo.setAddTime(dto.getAddTime());
                        vo.setAmount(dto.getAmount());
                        vo.setOverdueDays(dto.getOverdueDays());
                        vo.setSettlementPeriod(dto.getSettlementPeriod());
                        //逾期编码
                        vo.setBillPeriodOverdueManagementCode(dto.getBillPeriodOverdueManagementCode());
                        //结算标准
                        Integer settlementType = dto.getSettlementType();
                        vo.setSettlementType(settlementType);
                        //物流日志/发票票号
                        if (ObjectUtils.notEmpty(settlementType)) {
                            if (ErpConst.ONE.equals(settlementType)) {
                                Express express = new Express();
                                if (null != dto.getRelatedId()){
                                    express.setExpressId(dto.getRelatedId().intValue());
                                    Express expressList = traderCustomerService.selectAmountByExpressId(express);
                                    if (null != expressList) {
                                        vo.setRelatedInfo(expressList.getLogisticsNo());
                                        vo.setProductAmount(expressList.getAmount());
                                        //是否直发
                                        vo.setDeliveryDirect(detail.getDeliveryDirect());
                                    }
                                }
                            }
                            if (ErpConst.TWO.equals(settlementType)) {
                                if (null != dto.getRelatedId()) {
                                    Integer relatedId = dto.getRelatedId().intValue();
                                    Invoice invoice = traderCustomerService.selectInvoice(relatedId);
                                    if (null != invoice){
                                        vo.setRelatedInfo(invoice.getInvoiceNo());
                                        vo.setProductAmount(invoice.getAmount());
                                    }

                                }
                            }
                        }
                        //生成方式
                        vo.setType(dto.getType());
                        customerBillVos.add(vo);
                    }
				});
            }
            mv.addObject("customerBillPeriodDetail", customerBillVos);
        }
		return mv;
	}
}
