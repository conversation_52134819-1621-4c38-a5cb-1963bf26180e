.page-wrap {
  padding-top: 0;
}
.rank-container {
  position: relative;
  min-height: 100vh;
  background: url('../../../image/broadcast/banner1.jpg') no-repeat #AD0D27;
  background-position: top center;
  padding: 320px 20px 30px;
}
.rank-container .fixed-nav {
  display: none;
}
.rank-container .rank-wrap {
  margin: auto;
  padding: 15px;
  border-radius: 10px;
  background: #970012;
  width: 1727px;
}
.rank-container .rank-wrap .rank-card-wrap {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -15px;
}
.rank-container .rank-wrap .rank-card-wrap .rank-card {
  width: 413px;
  flex-shrink: 0;
  border-radius: 5px;
  background: #fff;
  margin-right: 15px;
  margin-bottom: 15px;
}
.rank-container .rank-wrap .rank-card-wrap .rank-card:nth-child(4n) {
  margin-right: 0;
}
@media screen and (max-width: 1800px) {
  .rank-container .rank-wrap {
    width: 871px;
  }
  .rank-container .rank-wrap .rank-card-wrap .rank-card:nth-child(2n) {
    margin-right: 0;
  }
}
@media screen and (max-width: 768px) {
  .page-wrap {
    min-width: auto;
  }
  .rank-container {
    min-width: auto;
    min-height: 100vh;
    background: url('../../../image/broadcast/banner2.jpg') no-repeat #AD0D27;
    background-size: contain;
    background-position: top center;
    padding: 22.47vh 0 0;
  }
  .rank-container .rank-wrap {
    width: 100%;
    padding: 10px;
    background: none;
  }
  .rank-container .rank-wrap .rank-card-wrap {
    display: block;
    margin-bottom: 0;
  }
  .rank-container .rank-wrap .rank-card-wrap .rank-card {
    margin-right: 0;
    margin-bottom: 10px;
    width: 100%;
  }
  .rank-container .rank-wrap .rank-card-wrap .rank-card:last-child {
    margin-bottom: 0;
  }
  .rank-container .fixed-nav {
    height: 41px;
    display: block;
  }
  .rank-container .fixed-nav .m-nav {
    position: relative;
  }
  .rank-container .fixed-nav .m-nav.fixed {
    position: fixed;
    top: 0;
    z-index: 9;
  }
  .rank-container .fixed-nav .m-nav .scroll-bar {
    position: relative;
    width: 100vw;
    display: flex;
    white-space: nowrap;
    overflow: auto;
    background: #911100;
  }
  .rank-container .fixed-nav .m-nav .scroll-bar::-webkit-scrollbar {
    display: none;
  }
  .rank-container .fixed-nav .m-nav .scroll-bar .nav-list {
    width: 710px;
    display: flex;
    padding: 0 15px;
  }
  .rank-container .fixed-nav .m-nav .scroll-bar .nav-list .nav-item {
    position: relative;
    padding: 10px 20px 10px 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    white-space: nowrap;
  }
  .rank-container .fixed-nav .m-nav .scroll-bar .nav-list .nav-item:last-child {
    padding-right: 0;
  }
  .rank-container .fixed-nav .m-nav .scroll-bar .nav-list .nav-item.active {
    color: #fff;
    font-weight: 700;
  }
  .rank-container .fixed-nav .m-nav .scroll-bar .nav-list .nav-item.active::after {
    content: "";
    width: 25px;
    height: 4px;
    border-radius: 2px;
    background: #fff;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
  }
}
