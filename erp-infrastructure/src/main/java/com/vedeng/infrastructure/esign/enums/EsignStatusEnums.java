package com.vedeng.infrastructure.esign.enums;

import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 签章消息枚举类
 * @date 2022/8/29 11:32
 */
public enum EsignStatusEnums {

    /**
     * 消息类型
     */
    SEND_SUCCESS(1, "发送签章成功"),
    SEND_FAIL(2, "发送签章失败"),
    SIGN_SUCCESS(3, "签章成功"),
    SIGN_FAIL(4, "签章失败");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 描述
     */
    private final String desc;

    EsignStatusEnums(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 获取签章业务枚举
     *
     * @param status 状态
     * @return EsignStatusEnums
     */
    public static EsignStatusEnums getEnum(Integer status) {
        return Arrays.stream(EsignStatusEnums.values())
                .filter(enums -> enums.getStatus().equals(status))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的签章消息状态"));
    }



}
