package com.vedeng.customerbillperiod.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL
 * <AUTHOR>
@Data
public class CustomerBillPeriodRiskManagementDetail implements Serializable {
    private Long billPeriodRiskManagementDetailId;

    /**
     * 账期风险管理编码
     */
    private String billPeriodRiskManagementCode;

    /**
     * 客户所属公司ID
     */
    private Integer companyId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户账期使用明细ID
     */
    private Long billPeriodUseDetailId;

    /**
     * 客户账期风险管理金额
     */
    private BigDecimal amount;

    /**
     * 账期风险管理明细待还金额
     */
    private BigDecimal unreturnedAmount;

    /**
     * 账期风险管理明细类型，1订单发货，3售后退货，5还款
     */
    private Integer type;

    /**
     * 账期风险管理关联表的ID，与TYPE字段对应：物流ID，发票ID，售后退货单ID，支付流水ID
     */
    private Long relatedId;

    /**
     * 当售后退货、退票、还款时产生的明细，对应的订单发货明细ID
     */
    private Long parentManagementDetailId;

    /**
     * 账期结算周期的，单位：天
     */
    private Integer settlementPeriod;

    /**
     * 客户账期风险管理明细添加时间
     */
    private Long addTime;

    /**
     * 客户账期风险管理明细更新时间
     */
    private Long modTime;

    private static final long serialVersionUID = 1L;
}