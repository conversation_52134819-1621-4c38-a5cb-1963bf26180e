package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;

import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 销项费用专用发票
 */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_OUT_PUT_FEE_SPECIAL_INVOICE")
public class KingDeeOutPutFeeSpecialInvoiceEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer specialInvoicId;

    /**
     * 单据内码
     */
    private String fid;

    /**
     * 贝登erp对应的单据头ID
     */
    private String fQzokBddjtid;

    /**
     * 发票号
     */
    private String finvoiceno;

    /**
     * 发票代码
     */
    private String fQzokFpdm;

    /**
     * 发票日期  2022-09-07 00:00:00
     */
    private String finvoicedate;

    /**
     * 业务日期 2022-09-07 00:00:00
     */
    private String fdate;

    /**
     * 往来单位类型 BD_Customer
     */
    private String fcontactunittype;

    /**
     * 客户
     */
    private String fcontactunit;

    /**
     * 结算组织
     */
    private String fsaleorgid;

    /**
     * 单据状态
     */
    private String fdocumentstatus;

    /**
     * 红蓝字标识
     */
    private String fRedBlue;

    /**
     * 发票明细
     */
    @Column(jdbcType = "VARCHAR")
    private String fsaleexinventry;
}