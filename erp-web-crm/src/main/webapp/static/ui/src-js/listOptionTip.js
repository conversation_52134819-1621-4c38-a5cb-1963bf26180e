
Vue.component('ui-list-option-tip', {
    template: `<div class="vd-ui-list-option-tip" v-show="isShow">
        <div class="vd-ui-list-option-cnt">
            <div class="vd-ui-list-option-close" @click="hide">
                <i class="vd-ui_icon icon-delete"></i>
            </div>
            <div class="vd-ui-list-option-img"></div>
            <div class="vd-ui-list-option-txt">通过 Shift 加鼠标滚轮上下滚动，实现列表左右快速查看</div>
            <div class="vd-ui-list-option-btn">
                <ui-button type="primary" @click="hide">我知道了</ui-button>
            </div>
        </div>
    </div>`,
    props: {
      
    },
    data() {
        return {
            isShow: false
        }
    },
    computed: {
        
    },
    mounted(){
        this.checkShow();
    },
    methods: {
        checkShow() {
            let historyShow = localStorage.getItem('crm_list_option_show');

            if(!historyShow) {
                this.isShow = true;
            }
        },
        hide() {
            this.isShow = false;
            window.localStorage.setItem('crm_list_option_show', 1);
        }
    }
})