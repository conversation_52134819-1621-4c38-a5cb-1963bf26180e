<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>供应商列表（财务专用）</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="financia_supplier_list" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>供应商名称：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder=""  oper="like"
                       name="TRADER_NAME" empty_show="-" >
            </div>
        </div>

        <div class="layui-inline ">
            <label>供应商ID：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder=""  oper="EQ"
                       name="TRADER_ID" alias = "A" empty_show="-" >
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">归属采购:</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="USER_ID" alias = "C" placeholder="全部" style="" jdbctype=""
                        data="
select DISTINCT B.USER_ID k,
                lower( C.USERNAME) V
FROM
    T_TRADER_SUPPLIER_FINANCE A
        LEFT JOIN T_R_TRADER_J_USER B ON A.TRADER_ID = B.TRADER_ID
        LEFT JOIN T_USER C ON B.USER_ID = C.USER_ID
WHERE
        B.TRADER_TYPE = 2" datatype="KVSQLCACHE" oper="EQ"></object>
            </div>
        </div>


        <div class=" layui-inline ">
            <label class="layui-form-label">供应商类别：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="TRADER_TYPE" alias = "A"  jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"生产厂家"},{"K":"2","V":"经销商"}]'
                        datatype="json"></select>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">是否推送金蝶：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="IS_PUSH" alias = "A" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"是"},{"K":"0","V":"否"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class="layui-inline ">
            <label>创建时间：</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" jdbctype="DATETIME" type="search"
                        placeholder="" name="ADD_TIME" alias = "FROM_UNIXTIME(E.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%s')" oper="BETWEEN"></object>
            </div>
        </div>
        <div class="layui-inline ">
            <label>推送金蝶时间：</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" jdbctype="DATETIME" type="search"
                        placeholder="" name="PUSH_TIME" oper="BETWEEN"></object>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">

    <div class="btn-group bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/traderFinancia/toUploadExcelPageSupplier.do" opentype="MODEL" area="600px,400px" windowname="批量修改供应商信息">批量处理</button>
        <button item_name="导出" name="导出" url="/ezadmin/list/export-financia_supplier_list" opentype="_BLANK_PARAM_COLUMN" windowname="" style type="table">导出</button>
    </div>

    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th name="TRADER_ID" >供应商ID</th>
            <th name="USERNAME">归属采购</th>
            <th name="TRADER_NAME">供应商名称</th>
            <th name="TRADER_TYPE">供应商类别</th>
            <th name="ADD_TIME">创建时间</th>
            <th name="PUSH_TIME">推送金蝶时间</th>
            <th name="IS_PUSH">是否推送金蝶</th>

            <th type="rowbutton" id="rowbutton">
                <button class="layui-btn list-row-button" type="single" opentype="MODEL"
                        url="/traderFinancia/editSupplierTrader.do?id=${TRADER_SUPPLIER_FINANCE_ID}" windowname="供应商信息（财务专用）修改" area="600px,600px"
                        name="update">修改
                </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by A.MOD_TIME desc" groupby="">
select A.TRADER_SUPPLIER_FINANCE_ID,
       A.TRADER_ID,
       D.USERNAME,
       B.TRADER_NAME,
       case A.TRADER_TYPE
           when '1' then '生产厂家'
           when '2' then '经销商'
           end                          as                TRADER_TYPE,
       FROM_UNIXTIME(E.ADD_TIME/1000, '%Y-%m-%d %H:%i:%s')  ADD_TIME,
      case  A.IS_PUSH
           when '1' then DATE_FORMAT(A.PUSH_TIME, '%Y-%m-%d %H:%i:%s')
           when '0' then '-' end     as                PUSH_TIME,

       case  A.IS_PUSH
           when '1' then '是'
           when '0' then '否' end     as                IS_PUSH,
       C.USER_ID,
       DATE_FORMAT(A.MOD_TIME, '%Y-%m-%d %H:%i:%s')          MOD_TIME

from T_TRADER_SUPPLIER_FINANCE A
         left join T_TRADER B on A.TRADER_ID = B.TRADER_ID
         left join T_R_TRADER_J_USER C on C.TRADER_ID = A.TRADER_ID and C.TRADER_TYPE = 2
         left join T_USER D ON C.USER_ID = D.USER_ID
         left join T_TRADER_SUPPLIER E on E.TRADER_ID = A.TRADER_ID
         left join  T_VERIFIES_INFO F ON F.RELATE_TABLE_KEY = E.TRADER_SUPPLIER_ID AND F.RELATE_TABLE = 'T_TRADER_SUPPLIER' and F.VERIFIES_TYPE = 619
where 1 = 1
                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>