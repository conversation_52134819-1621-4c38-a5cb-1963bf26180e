package com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext;

import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.OverBuyorderAfterSaleReturnOrderAfterProcessor;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
public class DealSaleorderForBuyReturnAfterProcessor extends OverBuyorderAfterSaleReturnOrderAfterProcessor {

    @Autowired
    AfterSalesMapper afterSalesMapper;

    @Autowired
    AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    BuyorderMapper buyorderMapper;

    @Autowired
    BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    SaleorderMapper saleorderMapper;

    @Autowired
    SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    OrderInfoSyncService orderInfoSyncService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doProcess(BizDto bizDto) {
        AfterSalesVo afterSalesVo = (AfterSalesVo) bizDto.getBizData();
        AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(afterSalesVo.getAfterSalesId());
        orderInfoSyncService.updateSaleorderForbuyorderReturnOver(afterSalesById.getAfterSalesId(),afterSalesById.getOrderId());
    }
}
