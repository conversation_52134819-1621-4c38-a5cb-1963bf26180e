Vue.component('ui-dialog', {
    template: `
        <div class="vd-ui-dialog_wrapper" ref="dialogWrap" v-show="visible" @click.prevent="modalClose" :style="{'z-index': zIndex}">
            <div class="vd-ui-dialog" :class="{'vd-ui-dialog--in': scroll=='in', 'noPadding': noPadding}" :style="style" @click="handlerStop">
                <div class="vd-ui-dialog_head">
                    <span class="vd-ui-dialog_head_title"><img :src="titleIcon" v-if="titleIcon" class="vd-ui-dialog_head_title_icon"/>{{title}}<span v-if="titleTip" class="vd-ui-dialog_head_title_tip">{{ titleTip }}</span></span>
                    <div class="vd-ui-dialog_head_headerBtn" @click="handleClose('close')" v-show="showClose">
                        <i class="vd-ui_icon icon-close1"></i>
                    </div>
                </div>
                <div class="vd-ui-dialog_content" :class="{'noPadding': noPadding, 'noScroll': noScroll}">
                    <slot></slot>
                </div>
                <div class="vd-ui-dialog_footer" :style="{'text-align': align}" v-if="$slots.footer" >
                    <slot name="footer"></slot>
                </div>
            </div> 
        </div>
    `,
    props: {
        title: {
            type: String,
            default: '标题'
        },
        titleTip: {
            type: String,
            default: ''
        },
        titleIcon: {
            type: String,
            default: ''
        },
        visible: {
            type: Boolean,
            default: false
        },
        size: String,
        width: String,
        maxHeight: String,
        noPadding: Boolean, // 没有内边距
        noScroll: Boolean, // dialog禁止滚动
        clickModal: {
            type: Boolean,
            default: false
        },
        showClose: {
            type: Boolean,
            default: true
        },
        align: {
            type: String,
            default: ''
        },
        scroll: {
            type: String,
            default: 'in'
        },
        nostop: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            zIndex: '',
        }
    },
    computed: {
        style() {
            let style = {};
            switch (this.size) {
                case 'small':
                   style.width = '480px';
                   break;
                case 'middle':
                   style.width = '720px';
                   break;
                case 'large':
                   style.width = '960px';
                   break;
            }
            if (this.width) style.width = this.width;
            style.maxHeight = this.maxHeight;
            return style;
        }
    },
    watch: {
        visible (val) {
            if (val) {
                document.body.append(this.$refs.dialogWrap);

                // if (document.querySelector(".vd-ui-modal")) {
                //     let index = Number(document.querySelector(".vd-ui-modal").style.zIndex);
                //     document.querySelector(".vd-ui-modal").style.zIndex = index + 2;
                //     this.zIndex = index + 3;
                // } else {
                //     const div = document.createElement("div");
                //     div.className="vd-ui-modal vd-ui-modal-enter";
                //     document.body.appendChild(div);
                //     document.body.style.overflow = 'hidden';
                //     document.querySelector(".vd-ui-modal").style.zIndex = 2000;
                //     this.zIndex = 2001
                // }
                this.$emit('open');
            } else {
                this.$refs.dialogWrap.remove();
                // this.closeModal();
            }
        }
    },
    mounted() {
        if (this.visible) {
            this.$emit('open');
            if (document.querySelector(".vd-ui-modal")) {
                let index = Number(document.querySelector(".vd-ui-modal").style.zIndex);
                document.querySelector(".vd-ui-modal").style.zIndex = index + 2;
                this.zIndex = index + 3;
            } else {
                const div = document.createElement("div");
                div.className="vd-ui-modal vd-ui-modal-enter";
                document.body.appendChild(div);
                document.body.style.overflow = 'hidden';
                document.querySelector(".vd-ui-modal").style.zIndex = 2000;
                this.zIndex = 2001;
            }
        }
    },
    methods: {
        modalClose() {
            if (!this.clickModal) return;
            this.handleClose();
        },
        handleClose() {
            this.$emit('update:visible', false);
            this.$emit('close')
        },
        closeModal() {
            // if (document.querySelector(".vd-ui-modal")) {
            //     let index = Number(document.querySelector(".vd-ui-modal").style.zIndex);
            //     if (index > 2000) {
            //         document.querySelector(".vd-ui-modal").style.zIndex = index -2;
            //     } else {
            //         let dom = document.querySelector(".vd-ui-modal");
            //         dom.classList.remove('vd-ui-modal-enter'); //删除类名
            //         dom.classList.add('vd-ui-modal-leave'); //添加类名
            //         document.body.style.overflow = 'auto';
            //         setTimeout(()=> {
            //             dom.parentNode.removeChild(dom);
            //         }, 180)
            //     }
            // }
            // this.$emit('close');
        },
        handlerStop(e) {
            if(!this.nostop) {
                e.stopPropagation();
            }
        }
    }
})