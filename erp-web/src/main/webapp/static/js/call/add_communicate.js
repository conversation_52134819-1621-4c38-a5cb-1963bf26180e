$(function() {
    console.log("init")
	var otherCheckbox = document.getElementById('otherCheckbox');
	if (otherCheckbox) {
		var otherInput = document.getElementById('otherPosition');
		otherCheckbox.addEventListener('change', function() {
			if (this.checked) {
				otherInput.style.display = 'inline-block'; // 显示输入框
			} else {
				otherInput.style.display = 'none'; // 隐藏输入框
			}
		});
	}

	$("#submit").click(function(){
		console.log("submit");
		checkLogin();
		$(".warning").remove();
		$("input").removeClass("errorbor");
		if($("#traderContactId").val() == 0){
			warnTips("traderContactId","联系人不允许为空");
			return false;
		}
		if($("#begin").val() == ''){
			warnTipsDate("timeErrorMsg","开始时间不允许为空");
			$("#begin").addClass("errorbor");
			return false;
		}
		if($("#end").val() == ''){
			warnTipsDate("timeErrorMsg","结束时间不允许为空");
			$("#end").addClass("errorbor");
			return false;
		}
		if($("#businessChanceAccuracy").val() == -1){
			warnTips("businessChanceAccuracy","商机精准度不允许为空");
			return false;
		}
		
		if($("input[name='tagId']").length == 0 && $("input[name='tagName']").length == 0 && $("textarea[name='contentSuffix']").val() == ''){
			//warnTips("tag_show_ul"," 沟通内容不允许为空");
            layer.alert("沟通内容不允许为空")
			return false;
		}

		if ($("textarea[name='contentSuffix']").val().length > 200){
			warnTips("contentSuffixError"," 沟通内容最多输入200字符，请检查后提交");
			return false;
		}


		var nextDate = $("input[name='nextDate']").val();

		if (! $('#noneNextDate').is(':checked') && nextDate.length == 0){
			warnTips("nextDate"," 下次沟通时间不允许为空");
			return false;
		}

		if(nextDate.length != 0 && $("#begin").val()>=(nextDate+" 23:59:59")){
			warnTipsDate("nextDate","下次沟通时间不能在沟通时间之前");
			return false;
		}
		
		if($("#nextContactContent").val().length > 256){
			warnTips("nextContactContent"," 下次沟通内容长度不允许超过256个字符");
			return false;
		}
		if($("#comments").val().length > 128){
			warnTips("comments"," 备注长度不允许超过128个字符");
			return false;
		}

		var selectedPositions = document.getElementById('allPosition');

		if (selectedPositions){
			var checkboxes = document.querySelectorAll('input[name="position"]:checked');
			var otherCheckbox = document.getElementById('otherCheckbox');
			var otherInput = document.getElementById('otherPosition');
			var errorMessage = document.getElementById('positionError');
			if (checkboxes.length === 0) {
				errorMessage.textContent = '请选择联系人职位';
				return false; // 阻止表单提交
			}

			// 校验“其他”选项是否填写
			if (otherCheckbox.checked && otherInput.value.trim() === '') {
				errorMessage.textContent = '请填写具体职位';
				return false; // 阻止表单提交
			}
			if (otherCheckbox.checked && otherInput.value.length > 50) {
				errorMessage.textContent = '其他职位不能超过50字！';
				return false; // 阻止表单提交
			}
			errorMessage.textContent = '';
			if (!otherCheckbox.checked){
				// 其他职位
				otherInput.value = "";
			}	
		}
		
		$.ajax({
			url:page_url+'/system/call/completeCommunicateInfo.do',
			data:$('#addCommunicate').serialize(),
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data){
				if(data.code == 0){
					$('#cancle').click();
					parent.layer.closeAll();
				}else{
					layer.alert(data.message, { icon : 2});
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		return false;
	});

	var selectElement = document.getElementById('traderContactId');
	
	if (selectElement){
		// 添加 change 事件监听器
		selectElement.addEventListener('change', function(event) {
			// 获取选中的值
			var selectedValue = event.target.value;

			var selectedOption = event.target.options[event.target.selectedIndex];

			var position = selectedOption.getAttribute('data-position');

			// 打印选中的值到控制台
			console.log('选中的值是是:', selectedValue + position);


			// 解析position为数组，赋值给allPosition
			var positionsArray = position.split(',');
			$('#allPosition').val(positionsArray);

			resetAllPosition()
			initPosition();

			// 你可以在这里执行其他操作，比如根据选中的值更新页面内容或发送请求
		});
	}
	
	$('.J-company-1').change(function () {
		$('.J-company-2').val($(this).val());
	});

	$('.J-company-2').change(function () {
		$('.J-company-1').val($(this).val());
	});
	$("#noneNextDateSpan").click(function(){
		$('#noneNextDate').click();
	})
	$('#noneNextDate').click(function () {
		var noneNextDate = $('#noneNextDate').is(':checked');
		if (noneNextDate){
			$('#nextDate').val('');
			$('#nextDate').css('background-color', '#CCCCCC');
			$('#nextDate').attr('disabled', true);
			$('#noneNextDateVal').val(1);
		} else {
			$('#nextDate').css('background-color', 'white');
			$('#nextDate').attr('disabled', false);
			$('#noneNextDateVal').val(0);
		}
	})
    if ($('#traderId').val() == undefined || $('#traderId').val() == '' || $('#traderId').val() == 0){
        $('#addTraderContactDiv').hide();
    }
    setAllPosition();
	initPosition();
});

function resetAllPosition() {
	// 清空所有复选框的选中状态
	var checkboxes = document.querySelectorAll('input[name="position"]');
	checkboxes.forEach(checkbox => {
		checkbox.checked = false;
	});

	// 隐藏并清空“其他”输入框
	var otherInput = document.getElementById('otherPosition');
	otherInput.style.display = 'none'; // 隐藏输入框
	otherInput.value = ''; // 清空输入框的值

}

function setAllPosition(){

	var selectedOption = $('#traderContactId option:selected');

// 获取 data-position 属性
	var dataPosition = selectedOption.data('position');
	
	$('#allPosition').val(dataPosition);
}

function initPosition(){
	// 假设这是你从后端获取的值
	var selectedPositions = $('#allPosition').val();

	if ( selectedPositions == '' || selectedPositions === undefined){
		var checkboxes = document.querySelectorAll('input[name="position"]');
		if (checkboxes){
			checkboxes.forEach(checkbox => {
				checkbox.checked = false;
			});
		}
		var otherInput = document.getElementById('otherPosition');
		if (otherInput){
			otherInput.style.display = 'none'; // 隐藏输入框
			otherInput.value = '';
		}
		return
	}
	// 将字符串按逗号分割成数组
	var positionsArray = selectedPositions.split(',');

	// 用于存储未匹配的值
	var unmatchedPositions = [];

	// 遍历数组，设置对应的复选框为选中状态
	positionsArray.forEach(position => {
		// 去除首尾空格
		position = position.trim();

		// 查找对应的复选框
		var checkbox = document.querySelector(`input[name="position"][value="${position}"]`);

		if (checkbox) {
			// 如果找到对应的复选框，则选中
			checkbox.checked = true;
		} else if (position) {
			// 如果没有找到对应的复选框，则收集到未匹配的数组中
			unmatchedPositions.push(position);
		}
	});

	// 如果有未匹配的值，则显示“其他”输入框并填入值
	if (unmatchedPositions.length > 0) {
		document.getElementById('otherCheckbox').checked = true;
		document.getElementById('otherPosition').style.display = 'inline-block';
		document.getElementById('otherPosition').value = unmatchedPositions.join(', ');

	}
	debugger
	if (positionsArray.includes("其他")){
		var otherInput = document.getElementById('otherPosition');
		otherInput.style.display = 'inline-block'; // 显示输入框
	}
}
/**
 * 切换用户信息
 */
function changeTraderInfo() {
	$('#salesNameStrDiv').html($("#traderInfoSelect option:selected").attr("salesNameStr"));
	var traderId = $("#traderInfoSelect option:selected").val();
	if (traderId == 0){
		$('#traderContactId').html('');
		$('#salesNameStrDiv').html('');
		$('#addTraderContactDiv').hide();
		return;
	}else {
        $('#addTraderContactDiv').show();
    }
	var layerParams = JSON.parse($('#addTraderContactDiv').attr('layerParams'));
	layerParams.link = '/order/bussinesschance/addTraderContact.do?traderId='+traderId;
	$('#addTraderContactDiv').attr('layerParams', JSON.stringify(layerParams));
	window.location = '/system/call/addCommunicate.do?phone='+ $('#phone').val() +'&coid=' +
		$('#coid').val() +'&traderId=' + traderId + '&callType=' + $('#callType').val() +
		'&orderId=' + $('#orderId').val() + '&communicateTime=' + $('#communicateTime').val();

	/*$('#traderId').val(traderId);
	$.ajax({
		url:page_url+'/system/call/getTraderContact.do',
		data:{'traderId':traderId},
		type:"GET",
		dataType: 'json',
		success:function(data){
			console.log(data);
			if(data.code == 0){
				var contactList = data.listData;
				var optionStr = '<option selected="selected" value="0">请选择</option>';
				for(var i = 0; i < contactList.length; i++) {
					optionStr += ' <option value="' + contactList[i].traderContactId + '">' + contactList[i].name + '|' +
						getPhoneStr(contactList[i].telephone) + getPhoneStr(contactList[i].telephone) + getPhoneStr(contactList[i].mobile) + '</option>';
					console.log('optionStr' + optionStr);

				};
				$('#traderContactId').html(optionStr);
			}else{
				layer.alert(data.message, { icon : 2});
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});*/
}

/**
 * 获取option的展示值
 *
 * @param phoneNo
 * @returns {string}
 */
function getPhoneStr(phoneNo) {
	if (phoneNo == undefined || phoneNo == ''){
		return '';
	}
	return '|' + phoneNo;
}
