package com.vedeng.infrastructure.esign.domain;

import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.base.api.dto.reqParam.EAccount;
import com.vedeng.base.api.dto.reqParam.Org;
import com.vedeng.base.api.dto.resParam.SignCompanyInfo;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.enums.VendorCertificateEnums;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 电子签章公共参数
 * @date 2021/12/7 9:05
 */
@Data
@Builder
public class ElectronicSignParam {


    /**
     * 盖章流程类型(1.全程(双方盖章） 2.半程(仅盖贝登章)  3.子公司多主体章
     */
    private Integer flowType;

    /**
     * 签署方(客户必传)
     */
    private EAccount account;

    /**
     * 签署org(客户必传)
     */
    private Org org;

    /**
     * 电子签章业务枚举
     */
    private ElectronicSignBusinessEnums electronicSignBusinessEnums;

    /**
     * 订单id
     */
    private Integer saleOrderId;

    /**
     * 采购单id
     */
    private Integer buyOrderId;

    /**
     * 采购费用单id
     */
    private Integer buyOrderExpenseId;

    /**
     * 厂商id
     */
    private Integer vendorId;

    /**
     * 授权申请书id
     */
    private Integer authorizationApplyId;


    /**
     * 厂商证书枚举
     */
    private VendorCertificateEnums vendorCertificateEnums;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 业务数据
     */
    private BusinessInfo businessInfo;

    /**
     * 重试次数
     */
    @Builder.Default
    private AtomicInteger retryCount = new AtomicInteger(0);

    /**
     * 业务id
     */
    private String businessId;

    /**
     * 签章主题信息
     */
    List<SignCompanyInfo> signCompanyInfoList;

    /**
     * 是否只盖最后一个关键字位置
     */
    private String onlySignLast;



    public BusinessInfo getBusinessInfo() {
        this.businessInfo.setBusinessName(electronicSignBusinessEnums.getType().toString());
        this.businessInfo.setBusinessType(flowType);
        return businessInfo;
    }

    public String getBusinessId() {
        return Stream.of(
                        Optional.ofNullable(saleOrderId),
                        Optional.ofNullable(buyOrderId),
                        Optional.ofNullable(buyOrderExpenseId),
                        Optional.ofNullable(vendorId),
                        Optional.ofNullable(authorizationApplyId)
                )
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(String::valueOf)
                .findFirst()
                .orElse(this.businessId);
    }
}
