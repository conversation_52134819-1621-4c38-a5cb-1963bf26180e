package com.vedeng.doc.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DocOfGoodsQueryDetailDto {

    /**
     * 业务类型(1：商品资料，2：厂商资质)
     */
    private Integer buzType;

    /**
     * 标签文件集合
     */
    private List<TagFileDto> tagFileDtoList;

    /**
     * 标签文件
     */
    @Data
    public static class TagFileDto {

        /**
         * 标签名称
         */
        private String tagName;

        /**
         * 附件集合
         */
        private List<DocFileDto> fileList;

        /**
         * 有效起期
         */
        private String validStartTime;

        /**
         * 有效止期
         */
        private String validEndTime;
    }

}
