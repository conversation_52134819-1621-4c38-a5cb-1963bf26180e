package com.vedeng.goods.model;

import java.util.Date;

public class CategoryAttrValueMappingGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.CATEGORY_ATTR_VALUE_MAPPING_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer categoryAttrValueMappingId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer baseCategoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer baseAttributeId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer baseAttributeValueId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer isDeleted;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CATEGORY_ATTR_VALUE_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date addTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.CATEGORY_ATTR_VALUE_MAPPING_ID
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.CATEGORY_ATTR_VALUE_MAPPING_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCategoryAttrValueMappingId() {
        return categoryAttrValueMappingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.CATEGORY_ATTR_VALUE_MAPPING_ID
     *
     * @param categoryAttrValueMappingId the value for V_CATEGORY_ATTR_VALUE_MAPPING.CATEGORY_ATTR_VALUE_MAPPING_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCategoryAttrValueMappingId(Integer categoryAttrValueMappingId) {
        this.categoryAttrValueMappingId = categoryAttrValueMappingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_CATEGORY_ID
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.BASE_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBaseCategoryId() {
        return baseCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_CATEGORY_ID
     *
     * @param baseCategoryId the value for V_CATEGORY_ATTR_VALUE_MAPPING.BASE_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseCategoryId(Integer baseCategoryId) {
        this.baseCategoryId = baseCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBaseAttributeId() {
        return baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @param baseAttributeId the value for V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseAttributeId(Integer baseAttributeId) {
        this.baseAttributeId = baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBaseAttributeValueId() {
        return baseAttributeValueId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @param baseAttributeValueId the value for V_CATEGORY_ATTR_VALUE_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseAttributeValueId(Integer baseAttributeValueId) {
        this.baseAttributeValueId = baseAttributeValueId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.IS_DELETED
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.IS_DELETED
     *
     * @param isDeleted the value for V_CATEGORY_ATTR_VALUE_MAPPING.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.CREATOR
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.CREATOR
     *
     * @param creator the value for V_CATEGORY_ATTR_VALUE_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.UPDATER
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.UPDATER
     *
     * @param updater the value for V_CATEGORY_ATTR_VALUE_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.MOD_TIME
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.MOD_TIME
     *
     * @param modTime the value for V_CATEGORY_ATTR_VALUE_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.ADD_TIME
     *
     * @return the value of V_CATEGORY_ATTR_VALUE_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CATEGORY_ATTR_VALUE_MAPPING.ADD_TIME
     *
     * @param addTime the value for V_CATEGORY_ATTR_VALUE_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }
}