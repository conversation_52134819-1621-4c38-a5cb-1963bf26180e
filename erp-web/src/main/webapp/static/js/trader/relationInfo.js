function initRelation(traderId) {
    $.ajax({
        async: false,
        url: page_url + '/trader/publicCustomer/isPrivatized.do?traderId=' + traderId,
        type: 'GET',
        dataType: 'JSON',
        success: function (data){
            if (data.code === 0){
                debugger
                $("#terminalDiv")
                    .attr(
                        'layerParams',
                        '{"width":"800px","height":"400px","title":"搜索客户","link":"'+ page_url+'/trader/relation/searchCustomerList.do?searchTraderName=' + '&traderId=' + traderId +'"}'
                    );
                $("#terminalDiv").click();
            } else {
                layer.alert(data.message);
            }
        }
    })

}



function associateCustomer(traderCustomerId, associatedTraderCustomerId){
    $(".warning").remove();
    $.ajax({
        async: false,
        url: page_url + '/trader/relation/associate.do?traderCustomerId=' + traderCustomerId + "&associatedTraderCustomerId=" + associatedTraderCustomerId,
        type: "GET",
        dataType: "JSON",
        success: function (data){
            if (data.code == 0){
                layer.close()
                window.parent.location.reload()
            } else {
                $("#searchNameError").after('<div class="warning">' + data.message +'</div>');
            }
        },
        error: function (error){
            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi！")
        }
    })
}


function cancelRelation(traderCustomerId){
    layer.confirm(
        "确定取消关联该客户？",
        {
            btn: ['确定','取消']
        },
        function (){
            $.ajax({
                async: false,
                url: page_url + '/trader/relation/cancel_associate.do?traderCustomerId=' + traderCustomerId,
                type: "GET",
                dataType: "JSON",
                success: function (data){
                    if (data.code == 0){
                        window.location.reload();
                    }
                },
                error: function (error){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi！")
                }
            })
        }
    );
}