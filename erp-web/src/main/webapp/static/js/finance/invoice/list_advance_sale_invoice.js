

var startTime = "",endTime = "";
$(function(){
	$("#sInvoiceNo").focus();
	startTime = $("#de_startTime").val();
	endTime = $("#de_endTime").val();
})
function resetPage(){
	reset();
	$("#startTime").val(startTime);
	$("#endTime").val(endTime);
	$("#advanceValidStatus").val(0);
	$("#ticketReasonEqualList").val('[]');
	initTicketReasonEqualList();
}
function checkedOnly(obj){
	if($(obj).is(":checked")){
		var n = 0;
		$("#list_table").find("input[type='checkbox'][name='checkName']").each(function(){
			if($(this).is(":checked")){
				n++;
			}else{
				return false;
			}
		});
		if($("#list_table").find("input[type='checkbox'][name='checkName']").length == n){
			$("#list_table").find("input[type='checkbox'][name='checkAllOpt']").prop("checked",true);
		}
	}else{
		$("#list_table").find("input[type='checkbox'][name='checkAllOpt']").prop("checked",false);
	}
}

function saveOpenInvoiceAudit(invoiceApplyId,status){
	layer.confirm("您是否确认审核通过？", {
		btn : [ '确定', '取消' ]
	}, function() {
		checkLogin();
		$.ajax({
			async : false,
			url : './saveOpenInvoiceAudit.do',
			data : {"invoiceApplyId":invoiceApplyId,"advanceValidStatus":status,"isAdvance":1},
			type : "POST",
			dataType : "json",
			success : function(data) {

				if (data.code == 0) {
					refreshNowPageList(data);
				} else {

					layui.use(['layer', 'table'], function () {
						var layer = layui.layer;
						var table = layui.table;
						layer.open({
							title: '异常信息',
							area: ['800px', '400px'],
							content: '<table id="errorTable"></table>', // 弹出层中的内容是一个表格
							success: function(layero, index){
								// 在弹出层创建成功后，使用 layui 的 table 模块渲染表格
								table.render({
									elem: '#errorTable',
									data: data.data,
									cols: [[
										{field: 'invoiceApplyId',width:80, title: 'ID'},
										{field: 'orderNo', title: '编码'},
										{field: 'msg', title: '失败原因'}
									]]
								});
							}
						});
					});

				}


			},error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	});
}

function checkAllOpt(obj){
	if($(obj).is(":checked")){
		$("#list_table").find("input[type='checkbox'][name='checkName']").each(function(){
			$(this).prop("checked",true);
		});
	}else{
		$("#list_table").find("input[type='checkbox'][name='checkName']").each(function(){
			$(this).prop("checked",false);
		});
	}
}
/*$(function(){
	$("input[name='startTime']").val(getPreMonth(new Date()));
	$("input[name='endTime']").val(formatDate(new Date()));
});*/

function auditAdvanceInvoiceApply(validStatus){
	var invoiceApplyIdArr = [];
	$("#list_table").find("input[type='checkbox'][name='checkName']").each(function(){
		if($(this).is(":checked")){
			invoiceApplyIdArr.push($(this).val());
		}
	});
	if(invoiceApplyIdArr.length == 0){
		layer.alert("请选择需要审核的记录！", {icon: 2},
			function (index) {
				layer.close(index);
			}
		);
		return false;
	}
	layer.confirm("您是否确认审核通过？", {
		btn : [ '确定', '取消' ]
	}, function() {
		layer.closeAll();
		var index = layer.load(1);
		$.ajax({
			async : false,
			url : './saveOpenInvoiceAuditBatch.do',
			data : {"invoiceApplyIdArr":JSON.stringify(invoiceApplyIdArr),"advanceValidStatus":validStatus,"isAdvance":1},
			type : "POST",
			dataType : "json",
			success : function(data) {
				if (data.code == 0) {
					refreshNowPageList(data);
				} else {

					layui.use(['layer', 'table'], function () {
						var layer = layui.layer;
						var table = layui.table;
						layer.open({
							title: '异常信息',
							area: ['800px', '400px'],
							content: '<table id="errorTable"></table>', // 弹出层中的内容是一个表格
							success: function(layero, index){
								// 在弹出层创建成功后，使用 layui 的 table 模块渲染表格
								table.render({
									elem: '#errorTable',
									data: data.data,
									cols: [[
										{field: 'invoiceApplyId',width:80, title: 'ID'},
										{field: 'orderNo', title: '编码'},
										{field: 'msg', title: '失败原因'}
									]]
								});
							},
							btn:[]
						});
					});

				}

			},
			complete: function () {
				layer.close(index);
			}
		});
	});
}

function exportInvoiceAdvanceApplyList(){
	location.href = page_url + '/report/finance/exportInvoiceAdvanceApplyList.do?' + $("#search").serialize();
}