package com.newtask.goods.builder;

import com.vedeng.common.constant.ErpConst;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Description com.newtask.goods.builder
 * @Date 2021/12/11 17:09
 */
public class SaleorderGoodsBuyStatusSQLStatementBuilder extends AbstractSqlBuilder {

    private String table;

    private String primaryKeyName;

    private Integer keyInInteger;

    private Integer purchaseStatusData;

    private Integer buyNumData;


    public static SaleorderGoodsBuyStatusSQLStatementBuilder builder() {
        return new SaleorderGoodsBuyStatusSQLStatementBuilder();
    }

    @SuppressWarnings("all")
    @Override
    public String build() {
        if (table == null || table.length() == 0) {
            throw new IllegalArgumentException("Table name is empty");
        }
        if (primaryKeyName == null || primaryKeyName.length() == 0) {
            throw new IllegalArgumentException("primary key name is empty");
        }
        if (keyInInteger == null) {
            throw new IllegalArgumentException("primary key is null");
        }


        StringJoiner columnsToUpdate = new StringJoiner(ErpConst.Symbol.COMMA);

        if (purchaseStatusData != null) {
            columnsToUpdate.add("PURCHASE_STATUS_DATA=" + purchaseStatusData);
        }

        if (buyNumData != null) {
            columnsToUpdate.add("BUY_NUM_DATA=" + buyNumData);
        }

        if (columnsToUpdate.length() == 0) {
            throw new IllegalArgumentException("Has not any columns need to update");
        }

        return "update " + table + "\nset " + columnsToUpdate.toString() + " \nwhere " + primaryKeyName + "=" + keyInInteger;
    }

    public SaleorderGoodsBuyStatusSQLStatementBuilder setTable(String table) {
        this.table = table;
        return this;
    }

    public SaleorderGoodsBuyStatusSQLStatementBuilder setPrimaryKeyName(String primaryKeyName) {
        this.primaryKeyName = primaryKeyName;
        return this;
    }

    public SaleorderGoodsBuyStatusSQLStatementBuilder setKeyInInteger(Integer keyInInteger) {
        this.keyInInteger = keyInInteger;
        return this;
    }

    public SaleorderGoodsBuyStatusSQLStatementBuilder setPurchaseStatusData(Integer purchaseStatusData) {
        this.purchaseStatusData = purchaseStatusData;
        return this;
    }

    public SaleorderGoodsBuyStatusSQLStatementBuilder setBuyNumData(Integer buyNumData) {
        this.buyNumData = buyNumData;
        return this;
    }
}
