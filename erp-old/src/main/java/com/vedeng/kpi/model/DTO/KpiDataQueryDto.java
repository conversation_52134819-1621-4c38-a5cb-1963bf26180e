package com.vedeng.kpi.model.DTO;

import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: kpilog查询对象扩展.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 10:36 上午.
 * @author: Tomcat.Hui.
 */
public class KpiDataQueryDto extends KpiOrderLogDo {

    private Date kpiDateStart;

    private Date kpiDateEnd;

    private Integer coCustomers;

    private Integer lostCustomers;

    private Integer coOrderNum;

    private Integer isAllReturn;

    private List<Integer> userIds;

    private long startMillisecond;

    private long endMillisecond;

    private Integer retFlag;

    private Date retDate;

    private Integer days;

    private List<Integer> traderIds;

    private Integer custNum;

    private Long lastOrderTime;

    private Long lastChatTime;

    private Long chatApartDays;

    private Long validTime;

    private BigDecimal orderPaidAmount;

    private Long mobile;

    private List<Integer> chanceIds;

    private Long shValidTime;

    private Long chanceAddTime;

    private Integer startId;

    private Integer endId;

    private Long assignTime;

    public Long getAssignTime() {
        return assignTime;
    }

    public void setAssignTime(Long assignTime) {
        this.assignTime = assignTime;
    }

    public Date getKpiDateStart() {
        return kpiDateStart;
    }

    public void setKpiDateStart(Date kpiDateStart) {
        this.kpiDateStart = kpiDateStart;
    }

    public Date getKpiDateEnd() {
        return kpiDateEnd;
    }

    public void setKpiDateEnd(Date kpiDateEnd) {
        this.kpiDateEnd = kpiDateEnd;
    }

    public Integer getCoCustomers() {
        return coCustomers;
    }

    public void setCoCustomers(Integer coCustomers) {
        this.coCustomers = coCustomers;
    }

    public Integer getLostCustomers() {
        return lostCustomers;
    }

    public void setLostCustomers(Integer lostCustomers) {
        this.lostCustomers = lostCustomers;
    }

    public Integer getCoOrderNum() {
        return coOrderNum;
    }

    public void setCoOrderNum(Integer coOrderNum) {
        this.coOrderNum = coOrderNum;
    }

    public Integer getIsAllReturn() {
        return isAllReturn;
    }

    public void setIsAllReturn(Integer isAllReturn) {
        this.isAllReturn = isAllReturn;
    }

    public List<Integer> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Integer> userIds) {
        this.userIds = userIds;
    }

    public long getStartMillisecond() {
        return startMillisecond;
    }

    public void setStartMillisecond(long startMillisecond) {
        this.startMillisecond = startMillisecond;
    }

    public long getEndMillisecond() {
        return endMillisecond;
    }

    public void setEndMillisecond(long endMillisecond) {
        this.endMillisecond = endMillisecond;
    }

    public Integer getRetFlag() {
        return retFlag;
    }

    public void setRetFlag(Integer retFlag) {
        this.retFlag = retFlag;
    }

    public Date getRetDate() {
        return retDate;
    }

    public void setRetDate(Date retDate) {
        this.retDate = retDate;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public List<Integer> getTraderIds() {
        return traderIds;
    }

    public void setTraderIds(List<Integer> traderIds) {
        this.traderIds = traderIds;
    }

    public Integer getCustNum() {
        return custNum;
    }

    public void setCustNum(Integer custNum) {
        this.custNum = custNum;
    }

    public Long getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Long lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    public Long getLastChatTime() {
        return lastChatTime;
    }

    public void setLastChatTime(Long lastChatTime) {
        this.lastChatTime = lastChatTime;
    }

    public Long getChatApartDays() {
        return chatApartDays;
    }

    public void setChatApartDays(Long chatApartDays) {
        this.chatApartDays = chatApartDays;
    }

    public Long getValidTime() {
        return validTime;
    }

    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }

    public BigDecimal getOrderPaidAmount() {
        return orderPaidAmount;
    }

    public void setOrderPaidAmount(BigDecimal orderPaidAmount) {
        this.orderPaidAmount = orderPaidAmount;
    }

    public Long getMobile() {
        return mobile;
    }

    public void setMobile(Long mobile) {
        this.mobile = mobile;
    }

    public List<Integer> getChanceIds() {
        return chanceIds;
    }

    public void setChanceIds(List<Integer> chanceIds) {
        this.chanceIds = chanceIds;
    }

    public Long getShValidTime() {
        return shValidTime;
    }

    public void setShValidTime(Long shValidTime) {
        this.shValidTime = shValidTime;
    }

    public Long getChanceAddTime() {
        return chanceAddTime;
    }

    public void setChanceAddTime(Long chanceAddTime) {
        this.chanceAddTime = chanceAddTime;
    }

    public Integer getStartId() {
        return startId;
    }

    public void setStartId(Integer startId) {
        this.startId = startId;
    }

    public Integer getEndId() {
        return endId;
    }

    public void setEndId(Integer endId) {
        this.endId = endId;
    }
}
