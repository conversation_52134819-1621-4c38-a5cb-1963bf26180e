package com.vedeng.api.standard.converter;

/**
 * 响应配置工具类
 * 提供简洁的DSL语法来创建ResponseMappingConfig
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-30
 */
public class ResponseConfig {
    
    /**
     * 创建用于创建操作的配置
     * 
     * @param message 成功消息
     * @param idFields ID字段名称
     * @return 配置对象
     */
    public static ResponseMappingConfig create(String message, String... idFields) {
        return ResponseMappingConfig.forCreateOperation(message, idFields);
    }
    
    /**
     * 创建用于更新操作的配置
     * 
     * @param message 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig update(String message) {
        return ResponseMappingConfig.forUpdateOperation(message);
    }
    
    /**
     * 创建用于删除操作的配置
     * 
     * @param message 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig delete(String message) {
        return ResponseMappingConfig.forDeleteOperation(message);
    }
    
    /**
     * 创建用于查询操作的配置
     * 
     * @param message 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig query(String message) {
        return ResponseMappingConfig.forQueryOperation(message);
    }
    
    /**
     * 创建用于提交操作的配置
     * 
     * @param message 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig submit(String message) {
        return ResponseMappingConfig.forSubmitOperation(message);
    }
    
    /**
     * 创建用于审核操作的配置
     * 
     * @param message 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig approval(String message) {
        return ResponseMappingConfig.forApprovalOperation(message);
    }
    
    /**
     * 创建用于通用操作的配置
     * 
     * @param message 成功消息
     * @return 配置对象
     */
    public static ResponseMappingConfig success(String message) {
        return ResponseMappingConfig.forOperation(message);
    }
    
    /**
     * 创建配置构建器，支持链式调用
     * 
     * @param message 成功消息
     * @return 配置构建器
     */
    public static ConfigBuilder operation(String message) {
        return new ConfigBuilder(message);
    }
    
    /**
     * 配置构建器，提供链式API
     */
    public static class ConfigBuilder {
        private final String message;
        
        public ConfigBuilder(String message) {
            this.message = message;
        }
        
        /**
         * 创建带ID字段的创建操作配置
         * 
         * @param idFields ID字段名称
         * @return 配置对象
         */
        public ResponseMappingConfig withIds(String... idFields) {
            return ResponseMappingConfig.forCreateOperation(message, idFields);
        }
        
        /**
         * 创建简单的成功配置
         * 
         * @return 配置对象
         */
        public ResponseMappingConfig simple() {
            return ResponseMappingConfig.forOperation(message);
        }
        
        /**
         * 创建查询操作配置
         * 
         * @return 配置对象
         */
        public ResponseMappingConfig asQuery() {
            return ResponseMappingConfig.forQueryOperation(message);
        }
        
        /**
         * 创建更新操作配置
         * 
         * @return 配置对象
         */
        public ResponseMappingConfig asUpdate() {
            return ResponseMappingConfig.forUpdateOperation(message);
        }
        
        /**
         * 创建删除操作配置
         * 
         * @return 配置对象
         */
        public ResponseMappingConfig asDelete() {
            return ResponseMappingConfig.forDeleteOperation(message);
        }
        
        /**
         * 创建提交操作配置
         * 
         * @return 配置对象
         */
        public ResponseMappingConfig asSubmit() {
            return ResponseMappingConfig.forSubmitOperation(message);
        }
        
        /**
         * 创建审核操作配置
         * 
         * @return 配置对象
         */
        public ResponseMappingConfig asApproval() {
            return ResponseMappingConfig.forApprovalOperation(message);
        }
        
        /**
         * 使用自定义构建器进行高级配置
         * 
         * @return ResponseMappingConfig构建器
         */
        public ResponseMappingConfig.Builder custom() {
            return ResponseMappingConfig.builder().successMessage(message);
        }
    }
}
