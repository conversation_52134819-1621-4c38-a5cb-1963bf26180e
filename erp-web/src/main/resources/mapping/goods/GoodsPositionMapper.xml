<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.GoodsPositionMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.model.entity.GoodsPositionDo">
        <id column="ID" jdbcType="INTEGER" property="id" />
        <result column="PRIORITY" jdbcType="INTEGER" property="priority" />
        <result column="POSITION_NAME" jdbcType="VARCHAR" property="positionName" />
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
        <result column="ORDINAL" jdbcType="INTEGER" property="ordinal" />
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted" />
        <result column="SIGN_CONTRACT_MODE" jdbcType="TINYINT" property="signContractMode" />
        <result column="CREATOR_ID" jdbcType="INTEGER" property="creatorId" />
        <result column="UPDATER_ID" jdbcType="INTEGER" property="updaterId" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from V_GOODS_POSITION
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.vedeng.goods.model.entity.GoodsPositionDo">
        insert into V_GOODS_POSITION (ID, PRIORITY, POSITION_NAME,
                                      DESCRIPTION, ORDINAL, IS_DELETED,
                                      SIGN_CONTRACT_MODE,
                                      CREATOR_ID, UPDATER_ID, ADD_TIME,
                                      MOD_TIME)
        values (#{id,jdbcType=INTEGER}, #{priority,jdbcType=INTEGER}, #{positionName,jdbcType=VARCHAR},
                #{description,jdbcType=VARCHAR}, #{ordinal,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT},
                #{signContractMode,jdbcType=TINYINT},
                #{creatorId,jdbcType=INTEGER}, #{updaterId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
                #{modTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.entity.GoodsPositionDo">
        update V_GOODS_POSITION
        set PRIORITY = #{priority,jdbcType=INTEGER},
            POSITION_NAME = #{positionName,jdbcType=VARCHAR},
            DESCRIPTION = #{description,jdbcType=VARCHAR},
            ORDINAL = #{ordinal,jdbcType=INTEGER},
            IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            SIGN_CONTRACT_MODE = #{signContractMode,jdbcType=TINYINT},
            CREATOR_ID = #{creatorId,jdbcType=INTEGER},
            UPDATER_ID = #{updaterId,jdbcType=INTEGER},
            ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select ID, PRIORITY, POSITION_NAME, DESCRIPTION, ORDINAL, IS_DELETED,SIGN_CONTRACT_MODE, CREATOR_ID,
               UPDATER_ID, ADD_TIME, MOD_TIME
        from V_GOODS_POSITION
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select ID, PRIORITY, POSITION_NAME, DESCRIPTION, ORDINAL, IS_DELETED, SIGN_CONTRACT_MODE, CREATOR_ID,
               UPDATER_ID, ADD_TIME, MOD_TIME
        from V_GOODS_POSITION
    </select>
</mapper>