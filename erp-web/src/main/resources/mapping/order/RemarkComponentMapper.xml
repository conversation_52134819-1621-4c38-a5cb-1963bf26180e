<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.RemarkComponentMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.vedeng.order.model.RemarkComponent">
        <id column="COMPONENT_ID" property="componentId" />
        <result column="PARENT_ID" property="parentId" />
        <result column="NAME" property="name" />
        <result column="TYPE" property="type" />
        <result column="CODE" property="code" />
        <result column="STATUS" property="status" />
        <result column="IS_DEFAULT" property="isDefault" />
        <result column="IS_DATE" property="isDate" />
        <result column="IS_REASON" property="isReason" />
        <result column="SORT" property="sort" />
        <result column="ADD_TIME" property="addTime" />
        <result column="CREATOR" property="creator" />
        <result column="MOD_TIME" property="modTime" />
        <result column="UPDATER" property="updater" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        COMPONENT_ID, PARENT_ID, NAME, TYPE, CODE, STATUS, IS_DEFAULT, IS_DATE, IS_REASON, SORT, ADD_TIME, CREATOR, MOD_TIME, MOD_TIME
    </sql>

    <select id="getInitComponent" parameterType="com.vedeng.order.model.query.LabelQuery" resultType="com.vedeng.order.model.RemarkComponent">
        SELECT
            T.COMPONENT_ID,
            T.PARENT_ID,
            T.NAME,
            T.TYPE,
            T.CODE,
            T.IS_DEFAULT,
            T.IS_DATE,
            T.IS_REASON,
            T.SORT,
            T.ADD_TIME
        FROM
            T_REMARK_COMPONENT T
        WHERE
            T.STATUS = 0
        ORDER BY
            T.SORT
    </select>

    <select id="getComponentRelation"  parameterType="com.vedeng.order.model.query.LabelQuery" resultType="com.vedeng.order.model.ComponentRelation" >
        SELECT
            T.ID,
            T.COMPONENT_ID,
            T.SCENE,
            T.RELATION_ID,
            T.DETAIL_ID,
            T.SKU_NO,
            T.SKU_NAME,
            T.TIME,
            T.REASON,
            T.IS_DELETE
        FROM
            T_COMPONENT_RELATION T
        WHERE
            T.COMPONENT_ID = #{componentId,jdbcType=INTEGER}
        AND T.SCENE = #{scene,jdbcType=INTEGER}
        AND T.RELATION_ID = #{relationId,jdbcType=INTEGER}
        AND T.SKU_NO = #{skuNo,jdbcType=VARCHAR}
        AND T.IS_DELETE = 0
    </select>

    <insert id="saveComponentRelation" parameterType="com.vedeng.order.model.ComponentRelation">
        INSERT INTO T_COMPONENT_RELATION
            ( SCENE, RELATION_ID, DETAIL_ID, SKU_NO, SKU_NAME, COMPONENT_ID, TIME, REASON, IS_DELETE )
        VALUES
            ( #{scene,jdbcType=INTEGER}, #{relationId,jdbcType=INTEGER},#{detailId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR},
             #{skuName,jdbcType=VARCHAR}, #{componentId,jdbcType=INTEGER}, #{time,jdbcType=BIGINT},
             #{reason,jdbcType=VARCHAR}, 0);
    </insert>

    <delete id="deleteComponentRelation" parameterType="com.vedeng.order.model.query.LabelQuery">
        UPDATE T_COMPONENT_RELATION SET IS_DELETE = 1
        WHERE
         RELATION_ID = #{relationId,jdbcType=INTEGER}
        AND SKU_NO = #{skuNo,jdbcType=VARCHAR}
        AND SCENE = #{scene,jdbcType=INTEGER}
        <if test="componentId != null">
            AND COMPONENT_ID = #{componentId,jdbcType=INTEGER}
        </if>
    </delete>

    <update id="updateComponentRelation" parameterType="com.vedeng.order.model.ComponentRelation">
        UPDATE T_COMPONENT_RELATION SET TIME = #{time,jdbcType=BIGINT}, REASON = #{reason,jdbcType=VARCHAR}
        WHERE
         RELATION_ID = #{relationId,jdbcType=INTEGER}
        AND SKU_NO = #{skuNo,jdbcType=VARCHAR}
        AND SCENE = #{scene,jdbcType=INTEGER}
        AND COMPONENT_ID = #{componentId,jdbcType=INTEGER}
    </update>

    <select id="getComponentRelationList" parameterType="com.vedeng.order.model.query.LabelQuery" resultType="com.vedeng.order.model.ComponentRelation">
        SELECT
            T.ID,
            T.COMPONENT_ID,
            T.SCENE,
            T.RELATION_ID,
            T.DETAIL_ID,
            T.SKU_NO,
            T.SKU_NAME,
            T.TIME,
            T.REASON,
            T.IS_DELETE,
            T1.PARENT_ID,
            T1.NAME,
            T1.TYPE,
            T1.IS_DATE,
            T1.IS_REASON
        FROM
            T_COMPONENT_RELATION T
        LEFT JOIN T_REMARK_COMPONENT T1 ON T1.COMPONENT_ID = T.COMPONENT_ID
        WHERE
           T.RELATION_ID = #{relationId,jdbcType=INTEGER}
          AND T.SCENE = #{scene,jdbcType=INTEGER}
            <if test="skuNoList != null and skuNoList.size() > 0">
                AND SKU_NO IN
                <foreach collection="skuNoList" item="skuNo" index="index" open="(" separator="," close=")">
                    #{skuNo}
                </foreach>
            </if>
          AND T.SKU_NO = #{skuNo,jdbcType=VARCHAR}
          AND T.IS_DELETE = 0
    </select>

    <select id="findComponentList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        TCR.RELATION_ID AS relationId,
        TRC.PARENT_ID as parentId,
        TCR.SKU_NO as skuNo,
        TRC.NAME as name,
        TCR.TIME as time,
        TCR.REASON as reason
        FROM T_REMARK_COMPONENT TRC LEFT JOIN  T_COMPONENT_RELATION TCR
        ON TRC.COMPONENT_ID = TCR.COMPONENT_ID
        WHERE  RELATION_ID=#{saleorderId,jdbcType=INTEGER}
        and SCENE = #{scene,jdbcType=INTEGER}
        <if test="skuNos != null and skuNos.size() > 0">
            AND SKU_NO IN
            <foreach collection="skuNos" item="skuId" index="index" open="(" separator="," close=")">
            #{skuId}
            </foreach>
        </if>
        and TRC.STATUS=0
        and TCR.IS_DELETE=0
    </select>

    <select id="getComponentRelationListByRelationId" parameterType="java.lang.Integer" resultType="com.vedeng.order.model.ComponentRelation">
        SELECT
            T.ID,
            T.COMPONENT_ID,
            T.SCENE,
            T.RELATION_ID,
            T.DETAIL_ID,
            T.SKU_NO,
            T.SKU_NAME,
            T.TIME,
            T.REASON,
            tr.type,
            T.IS_DELETE
            FROM T_COMPONENT_RELATION T
            left join T_REMARK_COMPONENT tr
			on tr.COMPONENT_ID =T.COMPONENT_ID
            where 1=1
         and RELATION_ID=#{labelQuery.relationId}
         and SCENE = #{labelQuery.scene,jdbcType=INTEGER}
         and is_delete=0
    </select>
    <select id="selectComponentRelationList" resultType="com.vedeng.order.model.RemarkComponent">
        select
            T1.COMPONENT_ID,T2.TYPE,T2.PARENT_ID
        from T_COMPONENT_RELATION T1
         left join T_REMARK_COMPONENT T2 on T1.COMPONENT_ID = T2.COMPONENT_ID
        where
            T1.IS_DELETE = 0
        and T1.RELATION_ID = #{labelQuery.relationId,jdbcType=INTEGER}
        and T1.SKU_NO =  #{labelQuery.skuNo,jdbcType=VARCHAR}
        and T1.SCENE = #{labelQuery.scene,jdbcType=INTEGER}
    </select>
    <select id="selectType" resultType="java.lang.Integer">
        select distinct TYPE
        from T_REMARK_COMPONENT
        where PARENT_ID = 0
          and STATUS = 0
    </select>

</mapper>
