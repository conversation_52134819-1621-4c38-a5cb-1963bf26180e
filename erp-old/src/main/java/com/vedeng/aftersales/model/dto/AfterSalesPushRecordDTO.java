package com.vedeng.aftersales.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class AfterSalesPushRecordDTO {

    private Long id;
    /**
     * 售后单号
     */
    private String afterSalesNo;

    /**
     * 关联的销售订单号
     */
    private String saleOrderNo;

    /**
     * 销售单生效时间
     */
    private Date saleOrderValidTime;

    /**
     * 工单编号
     */
    private String orderNum;

    /**
     * 售后类型（字典库）
     */
    private String type;

    /**
     * 酬金
     */
    private BigDecimal payment;

    /**
     * 下派时间
     */
    private Date pushTime;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityName;

    /**
     * 区
     */
    private String areaName;

    /**
     * 售后信息-详情说明
     */
    private String businessDescription;

    /**
     * 售后信息-售后联系人
     */
    private String customerCharge;
    /**
     * <pre>
     * 报单经销商联系人手机
     * 表字段 : V_AFTER_SALES_PUSH_RECORD.DEALER_CONTACTS_PHONE
     * </pre>
     *
     */
    private String dealerContactsPhone;

    /**
     * <pre>
     * 报单经销商联系人职位
     * 表字段 : V_AFTER_SALES_PUSH_RECORD.DEALER_CONTACTS_POSITION
     * </pre>
     *
     */
    private String dealerContactsPosition;

    /**
     * 售后信息-售后联系人电话
     */
    private String customerPhone;

    /**
     * 售后地区
     */
    private String customerRegion;

    /**
     * 售后地址
     */
    private String customerAddress;

    /**
     * 客户ID
     */
    private Integer dealerCustomerId;

    /**
     * 所属订单信息-客户名称
     */
    private String dealerCustomerName;

    /**
     * 售后信息-售后单报单人
     */
    private String dealerContacts;

    /**
     * 售后信息-售后单报单人电话
     */
    private String dealerContactsMobile;

    /**
     * 客户等级
     */
    private String dealerLevel;

    /**
     * 客户类型
     */
    private String dealerType;

    /**
     * 报单经销商id
     */
    private Integer dealerContactsId;

    /**
     * 客户-省
     */
    private String dealerProvince;

    /**
     * 客户-市
     */
    private String dealerCity;

    /**
     * 客户-区
     */
    private String dealerArea;

    /**
     * 归属销售
     */
    private String salesPerson;

    /**
     * 归属部门
     */
    private String departmentName;
    /**
     * 报单联系人备注
     */
    private String dealerContactsRemark;

    /**
     * 售后处理人
     */
    private String afterSalesPerson;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 0.下派；1.完成；2.关闭；3.取消
     */
    private Integer erpOrderStatus;

    /**
     * 取消原因
     */
    private String cancelReason;

    private List<DeviceInfo> deviceInfoList;

    private String token;

    @Data
    public static class DeviceInfo {
        /**
         * 设备名称
         *
         */
        private String mechineName;

        /**
         * 型号
         *
         */
        private String mechineModel;

        /**
         * 品牌
         *
         */
        private String mechineBrand;

        /**
         * 贝登sku码
         *
         */
        private String mechineSku;

        /**
         * 售后数量
         *
         */
        private Integer afterSaleNum;
    }

    @Data
    public static class File {
        private String fileName;
        private String fileUrl;
    }
}
