package com.vedeng.erp.buyorder.web.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import com.vedeng.erp.buyorder.service.BuyorderInfoQueryService;
import com.vedeng.erp.buyorder.service.NewAfterBuyorderService;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.system.dto.RoleDto;
import com.vedeng.erp.system.service.RoleApiService;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.service.InvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 采购售后控制类
 */
@Slf4j
@Controller
@ExceptionController
@RequestMapping("/after/newBuyorder")
public class NewAfterBuyorderController extends BaseController {

    @Autowired
    private NewAfterBuyorderService newAfterBuyorderService;

    @Autowired
    private BuyorderInfoQueryService buyorderInfoQueryService;

    @Autowired
    private RoleApiService roleApiService;

    /**
     * <AUTHOR>
     * 采购售后无需退票
     * @param afterSalesInvoiceId 售后发票ID
     * @param afterSalesId 售后单ID
     * @return 是否成功
     */
    @RequestMapping("/withOutRetureTicket")
    @ResponseBody
    public R<?> withOutRetureTicket(Integer afterSalesInvoiceId, Integer afterSalesId){
        log.info("售后单ID{},售后单发票ID{},",afterSalesInvoiceId,afterSalesId);
        newAfterBuyorderService.withOutRetureTicket(afterSalesInvoiceId,afterSalesId);
        return R.success();
    }

    /**
     * 采购售后退票申请虚拟冲销
     * @param invoiceId 申请冲销的发票id
     */
    @RequestMapping("/applyCoverTicket")
    @ResponseBody
    public R<?> applyCoverTicket(Integer invoiceId,Integer afterSalesId){
        log.info("售后单ID{},售后单发票ID{},",invoiceId,afterSalesId);
        newAfterBuyorderService.applyCoverTicket(invoiceId,afterSalesId);
        return R.success();
    }

    /**
     * 打开冲销确认弹窗
     * @param invoiceReversalId 冲销申请ID
     * @param isPass 是否通过
     * @return
     */
    @RequestMapping("/verifyCoverApply")
    @FormToken(save = true)
    public ModelAndView verifyCoverApply(Integer invoiceReversalId,boolean isPass){
        ModelAndView modelAndView = new ModelAndView("vue/view/afterBuyorder/invoiceCoverVerify");
        modelAndView.addObject("invoiceReversalId",invoiceReversalId);
        modelAndView.addObject("isPass",isPass);
        return modelAndView;
    }

    /**
     * 保存冲销审核信息
     * @param invoiceReversalId 冲销审核ID
     * @param isPass 是否通过
     * @param auditComment 审核备注
     * @return
     */
    @RequestMapping("/verifyCoverSave")
    @FormToken(remove = true)
    @ResponseBody
    public R<?> verifyCoverSave(Integer invoiceReversalId, boolean isPass, String auditComment){
        newAfterBuyorderService.verifyCoverSave(invoiceReversalId,isPass,auditComment);
        return R.success();
    }

    /**
     * 校验航信发票可录金额是否满足条件
     * <AUTHOR>
     * @param invoiceNo
     * @param invoiceCode
     * @param amount
     * @return
     */
    @RequestMapping("/verifyHxInvoice")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> verifyHxInvoice(String invoiceNo, String invoiceCode, BigDecimal amount){
        if(newAfterBuyorderService.verifyHxInvoiceLeftAmount(invoiceNo,invoiceCode,amount)){
            return R.success();
        }else{
            return R.error("所提供的蓝字发票不满足录票条件！");
        }
    }

    /**
     * 采购仅退票售后详情页
     * @param afterSalesId
     * @return
     */
    @RequestMapping(value = "/afterBuyorderTpDetail")
    @NoNeedAccessAuthorization
    public ModelAndView afterBuyorderTpDetail(Integer afterSalesId) {
        ModelAndView mv = new ModelAndView("vue/view/afterBuyorder/tp_details");
        mv.addObject("afterSalesId", afterSalesId);
        List<RoleDto> roleList = roleApiService.getRoleListByUserId(CurrentUser.getCurrentUser().getId());
        int topOrgId = 0;
        for (RoleDto item : roleList) {
            if ("产品专员".equals(item.getRoleName()) || "产品主管".equals(item.getRoleName()) || "产品总监".equals(item.getRoleName())) {
                topOrgId = 6;
                break;
            }
            if ("财务专员".equals(item.getRoleName()) || "财务总监".equals(item.getRoleName())) {
                topOrgId = 8;
                break;
            }
        }
        mv.addObject("topOrgId", topOrgId);
        return mv;
    }

    /**
     * 采购售后仅退票详情页
     * <AUTHOR>
     * @param afterSalesId
     * @return
     */
    @RequestMapping(value = "/getTpDetail", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<AfterSalesDto> getDetail(@RequestParam Integer afterSalesId) {
        return R.success(newAfterBuyorderService.viewTpDetail(afterSalesId));
    }

    /**
     * 查询采购单信息
     * <AUTHOR>
     * @param buyorderNo
     * @return
     */
    @RequestMapping(value = "/getBuyorderInfo", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<BuyOrderDto> getBuyorderInfo(@RequestParam String buyorderNo) {
        return R.success(buyorderInfoQueryService.queryInfoByNo(buyorderNo));
    }

    /**
     * 关闭仅退票售后单
     *
     * @param afterSalesId 退票售后单id
     * @param buyorderId   费用单id
     * @return R<?>
     */
    @RequestMapping(value = "/closeTpAfter", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> closeTpAfter(@RequestParam Integer afterSalesId, @RequestParam Integer buyorderId) {
        newAfterBuyorderService.closeTpAfterSales(afterSalesId, buyorderId);
        return R.success();
    }

    @RequestMapping(value = "/saveTpReturnInvoice",method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> saveTpReturnInvoice(Invoice invoice
            , @RequestParam(required = false, value="detailGoodsIdArr") String detailGoodsIdArr
            , @RequestParam(required = false, value="invoiceAmountArr") String invoiceAmountArr
            , @RequestParam(required = false, value="invoicePriceArr") String invoicePriceArr
            , @RequestParam(required = false, value="invoiceNumArr") String invoiceNumArr){
        // 校验发票是否已退票
        List<Integer> detailGoodsIdList = JSON.parseArray("["+detailGoodsIdArr+"]",Integer.class);
        List<BigDecimal> invoiceAmountList = JSON.parseArray("["+invoiceAmountArr+"]",BigDecimal.class);
        List<BigDecimal> invoicePriceList = JSON.parseArray("["+invoicePriceArr+"]",BigDecimal.class);
        List<BigDecimal> invoiceNumList = JSON.parseArray("["+invoiceNumArr+"]",BigDecimal.class);
        newAfterBuyorderService.saveTpReturnInvoice(invoice,detailGoodsIdList,invoiceAmountList,invoicePriceList,invoiceNumList);
        return R.success();
    }
}

