$(function () {

    //校验
    var canSubmit = true;

    $('.J-form').validate({
        errorWrap: true,
        rules: {
            baseCategoryName: {
                required: true
            },
            baseCategoryType: {
                required: true
            },
            baseCategoryExampleProduct:{
                required: true
            },
            // taxClassificationCode: {
            //     required: true
            // }
        },
        messages: {
            baseCategoryName: {
                required: '请输入分类名称'
            },
            baseCategoryType: {
                required: '分类类型'
            },
            baseCategoryExampleProduct: {
                required: '请输入品名举例'
            },
            // taxClassificationCode: {
            //     required: '请输入税收编码'
            // }
        },
        submitHandler: function (form) {
            var baseAttributeId = [];
            var baseAttributeValueIds = [];

            $('.J-attr-item').each(function(){
                var name = $(this).find('.J-attr-name').val();
                var value = $(this).find('.J-attr-value').val();
                if(name && value){
                    baseAttributeId.push(name);
                    baseAttributeValueIds.push(value);
                }
            })

            if ($('[name=baseCategoryLevel]').val() == '3' && $('[name=baseCategoryId]').val() != null && baseAttributeId.length) {
                var categoryId = $('[name=baseCategoryId]').val();

                $.ajax({
                    url: './checkCategoryAttr.do',
                    data: {
                        baseCategoryId: categoryId,
                        attrName: baseAttributeId.join(','),
                        attrValue: baseAttributeValueIds.join(',')
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code == -1) {
                            var dialog = artDialog.confirm('修改类目，会导致所有子商品参数信息丢失且不可恢复，并且会下架所有子商品，请确认是否继续修改？', '', {
                                fn: function () {
                                    if (canSubmit) {
                                        $('.J-continue-add').val('1');
                                        canSubmit = false;
                                        window.localStorage.setItem('addCategorysuccess', true);
                                        form.submit();
                                    }
                                }, text: '确认'
                            }, {
                                    fn: function () {
                                        dialog.close();
                                    }, text: '取消'
                                });
                        } else {
                            if (canSubmit) {
                                canSubmit = false;
                                window.localStorage.setItem('addCategorysuccess', true);
                                form.submit();
                            }
                        }
                    }
                })
            } else {
                if (canSubmit) {
                    canSubmit = false;
                    window.localStorage.setItem('addCategorysuccess', true);
                    form.submit();
                }
            }
        }
    })

    $('[valid-max]').each(function () {
        $(this).rules('add', {
            maxlength: $(this).attr('valid-max')
        })
    })

    //选择属性名和属性值
    var attrData = JSON.parse($('.J-attr-json').html());
    var selectOptions = [];

    $.each(attrData, function (i, obj) {
        selectOptions.push({
            label: obj.attrName,
            value: obj.attrId
        });
    });

    var parseData = function (data) {
        var resData = [];

        $.each(data, function (i, obj) {
            resData.push({
                label: obj.attrValueName,
                value: obj.attrValueId
            })
        })

        return resData;
    };

    var initAttrItem = function ($item) {
        var suggest = new SuggestSelect({
            placeholder: '请选择',
            wrap: $item.find('.J-attr-name-wrap'),
            data: selectOptions,
            input: $item.find('.J-attr-name'),
            onchange: function () {
                var attrValue = [];
                var $attrvalue = $item.find('.J-attr-value-wrap');

                $.each(attrData, function (i, obj) {
                    if (obj.attrId == $item.find('.J-attr-name').val()) {
                        attrValue = parseData(obj.attrValue);
                    }
                })

                $attrvalue.after('<div class="J-attr-value-wrap"></div>');
                $attrvalue.remove();
                new SuggestSelect({
                    placeholder: '请选择',
                    multi: true,
                    multiAll: true,
                    wrap: $item.find('.J-attr-value-wrap'),
                    data: attrValue,
                    input: $item.find('.J-attr-value')
                })
            }
        });

        suggest.onchange();
    };

    $('.J-attr-item').each(function () {
        initAttrItem($(this));
    });

    //check Add and Delete
    var checkLen = function () {
        var $items = $('.J-attr-item');
        var len = $items.length;

        if (len > 1) {
            $('.J-attr-del').show();
        } else {
            $('.J-attr-del').hide();
        }

        if (len >= 50) {
            $('.J-attr-add').hide();
        } else {
            $('.J-attr-add').show();
        }

        $('.J-attr-num').html(len);
    };

    checkLen();

    $('.J-attr-wrap').on('click', '.J-attr-del', function () {
        $(this).parents('.J-attr-item:first').remove();
        checkLen();
    });

    $('.J-attr-add').click(function () {
        $('.J-attr-wrap').append($('.J-attr-tmpl').html());
        checkLen();
        initAttrItem($('.J-attr-item:last'));
    });

    Select.use('select');

    //增加关闭提示
    GLOBAL.addtip();


    //品名校验
    $('.J-tech-params').on('blur', '.J-sort-value', function () {
        var $label = $('label[for=paramsValid]');

        if ($label.length && $label.css('display') !== 'none') {
            $('[name=paramsValid]').valid();
        }
    });

    //检查添加或者删除是否显示
    var checkItemNum = function ($wrap) {
        var len = $('.J-sort-item', $wrap).length;
        var min = 1;

        if (len <= min) {
            $('.J-sort-del', $wrap).hide();
        } else {
            $('.J-sort-del', $wrap).show();
        }
    };

    var addParamsItem = function ($wrap, params) {

        var defaults = {
            value: '',
            itemName: '',
            itemValue: ''
        };

        var data = $.extend({}, defaults, params);
        var $list = $('#ex1', $wrap);

        $list.append(paramsTmpl(data));

        checkItemNum($wrap);
    };

    //删除参数
    $(document).on('click', '.J-sort-del', function () {
        debugger
        var $wrap = $(this).parents('.J-sort-wrap:first');
        $(this).parents('.J-sort-item:first').remove();
        checkItemNum($wrap);
    });


    //参数添加
    var paramsTmpl = template($('.J-sort-tmpl').html());

    //品名导入
    $('.J-sort-import').click(function () {
        var $wrap = $(this).parents('.J-sort-wrap:first');
        new artDialog({
            title: '复制文本导入品名',
            content: $('.J-import-tmpl').html(),
            init: function () { },
            width: 680,
            button: [{
                name: '导入',
                highlight: true,
                callback: function () {
                    var cnt = $.trim($('.J-import-cnt').val());
                    if (cnt) {
                        var params = cnt.split(/[;；]/);
                        $.each(params, function (i, item) {
                            if ($.trim(item)) {
                                var needInput = true;

                                $wrap.find('.J-sort-item').each(function () {
                                    if (needInput) {
                                        var $value = $(this).find('.J-sort-value');

                                        if (!$.trim($value.val())) {
                                            $value.val(item || '');
                                            needInput = false;
                                        }
                                    }
                                });

                                if ($wrap.find('.J-sort-item').length < 30 && needInput) {
                                    addParamsItem($wrap, {
                                        value: item || ''
                                    })
                                }

                                var $label = $('label[for=paramsValid]');

                                if ($label.length && $label.css('display') !== 'none') {
                                    $('[name=paramsValid]').valid();
                                }
                            }
                        })
                    }
                }
            }, {
                name: '取消'
            }],
        })
    });

    //鼠标悬停提示特效
    $("#editableDepartmentOrInspection").hover(
        function () {
            openMsg();
        },
        function () {
            layer.close(subtips);
        }
    );
    function openMsg() {
        subtips = layer.tips('管理人员：Kelly、Levi、Serena', '#editableDepartmentOrInspection',{tips:[2,'#2e8ae6'],time: 30000});
    }
    //END 鼠标悬停提示特效
})