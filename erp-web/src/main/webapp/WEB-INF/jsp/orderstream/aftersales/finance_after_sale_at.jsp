<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="C" uri="http://java.sun.com/jsp/jstl/core" %>
<c:choose>
    <c:when test="${afterSalesVo.type eq 584}">
        <c:set var="title" value="销售维修-售后" scope="application"/>
        <c:set var="flag" value="wx"></c:set>
    </c:when>
    <c:otherwise>
        <c:set var="title" value="销售安调-售后" scope="application"/>
        <c:set var="flag" value="at"></c:set>
    </c:otherwise>
</c:choose>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%= basePath %>static/js/orderstream/aftersales/finance_after_sales.js?rnd=${resourceVersionKey}'></script>
<script src="<%= basePath %>static/js/orderstream/jquery.js"></script>
<script src="<%= basePath %>static/js/orderstream/afterSales_index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/style.css">
<div class="main-container">

    <%--用户已点击过关闭-》完结字段改为关闭--%>
    <c:choose>
        <c:when test="${afterSalesVo.atferSalesStatus eq 2}">
            <div class="t-line-wrap J-line-wrap"
                 data-json='[
                 {"label":"确认","status":${topStatusList.get(0)}},
                 {"label":"处理","status":${topStatusList.get(1)}},
                 {"label":"关闭","status":${topStatusList.get(2)}}]'>
            </div>
        </c:when>
        <c:otherwise>
            <div class="t-line-wrap J-line-wrap"
                 data-json='[
             {"label":"确认","status":${topStatusList.get(0)}},
             {"label":"处理","status":${topStatusList.get(1)}},
             {"label":"完结","status":${topStatusList.get(2)}}]'>
            </div>
        </c:otherwise>
    </c:choose>

    <%-- 财务制单-付款审核通过-付款审核不通过 按钮--%>
    <div class="table-buttons">
        <c:choose>
            <c:when test="${(null!=taskInfoPay and null!=taskInfoPay.getProcessInstanceId() and null!=taskInfoPay.assignee) or !empty candidateUserMapPay[taskInfoPay.id]}">
                <c:choose>
                    <c:when test="${endStatusPay eq '财务制单' and (taskInfoPay.assignee == curr_user.username or candidateUserMapPay['belong'])}">
<%--                        <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./initFinanceTaskComplement.do?taskId=${taskInfoPay.id}&pass=true&type=1"}'>财务制单</button>--%>
<%--                        <button type="button" class="bt-bg-style bg-light-grey bt-small">--%>
<%--                            付款审核通过--%>
<%--                        </button>--%>
<%--                        <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./initFinanceTaskComplement.do?taskId=${taskInfoPay.id}&pass=false&type=1"}'>审核不通过</button>--%>
                        <%--<button type="button" class="bt-bg-style bg-light-grey bt-small">
                            付款审核不通过
                        </button>--%>
                        <%--<button type="button" class="bt-bg-style bg-light-grey bt-small">
                            申请开票
                        </button>--%>
                    </c:when>
                    <c:when test="${endStatusPay eq '财务审核' and (taskInfoPay.assignee == curr_user.username or candidateUserMapPay['belong'])}">
<%--                        <button type="button" class="bt-bg-style bg-light-grey bt-small">--%>
<%--                            财务制单--%>
<%--                        </button>--%>
<%--                        <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"600px","height":"350px","title":"新增交易记录","link":"/order/afterSalesCommon/addFinanceAfterCapital.do?afterSalesId=${afterSalesVo.afterSalesId}&billType=2&payApplyId=${payApplyId}&taskId=${taskInfoPay.id}&pageType=1"}'>审核通过</button>--%>
<%--                        <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"/order/aftersalesUpgrade/initFinanceTaskComplement.do?taskId=${taskInfoPay.id}&pass=false&type=1"}'>审核不通过</button>--%>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            财务制单
                        </button>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            付款审核通过
                        </button>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            付款审核不通过
                        </button>
                    </c:otherwise>
                </c:choose>
            </c:when>
            <c:when test="${afterSalesVo.isCanApplyInvoice eq 1 }">
                <%--无退款或全部退款，申请开票--%>
                <button type="button" class="bt-bg-style bg-light-grey bt-small">
                    财务制单
                </button>
                <button type="button" class="bt-bg-style bg-light-grey bt-small">
                    付款审核通过
                </button>
                <button type="button" class="bt-bg-style bg-light-grey bt-small">
                    付款审核不通过
                </button>
            </c:when>
            <c:otherwise>
                <button type="button" class="bt-bg-style bg-light-grey bt-small">
                    财务制单
                </button>
                <button type="button" class="bt-bg-style bg-light-grey bt-small">
                    付款审核通过
                </button>
                <button type="button" class="bt-bg-style bg-light-grey bt-small">
                    付款审核不通过
                </button>
            </c:otherwise>
        </c:choose>
    </div>

    <%--基本信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                基本信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">订单号</td>
                <td>${afterSalesVo.afterSalesNo}</td>
                <td class="wid20">售后类型</td>
                <td>${afterSalesVo.typeName}</td>
            </tr>

            <tr>
                <td class="wid20">订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
                </td>

                <td>审核状态</td>
                <td>
                    <c:if test="${afterSalesVo.status eq 0}">待审核</c:if>
                    <c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
                    <c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
                    <c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
                </td>
            </tr>


            <tr>
                <td>生效状态</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
                </td>

                <td>售后处理人</td>
                <td>${afterSalesVo.serviceUserName}</td>
            </tr>


            <tr>
                <td>申请人</td>
                <td>${afterSalesVo.creatorName}</td>

                <td>申请时间</td>
                <td><date:date value="${afterSalesVo.addTime}"/></td>
            </tr>

            <tr>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.validTime}"/></td>

                <td>完结时间</td>
                <td><c:if test="${afterSalesVo.atferSalesStatus == 2}"><date:date
                        value="${afterSalesVo.modTime}"/></c:if></td>
            </tr>

            <tr>
                <td>完结/关闭原因</td>
                <td>${afterSalesVo.afterSalesStatusResonName}</td>
                <td>完结/关闭人员</td>
                <td>${afterSalesVo.afterSalesStatusUserName}</td>
            </tr>

            <tr>
                <td>完结/关闭备注</td>
                <td colspan="3" class="text-left">${afterSalesVo.afterSalesStatusComments}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <%--售后信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后原因</td>
                <td>${afterSalesVo.reasonName}</td>
                <td class="wid20">第一责任部门</td>
                <td>${afterSalesVo.firstResponsibleDepartmentStr}</td>
            </tr>

            <tr>
                <td class="wid20">售后联系人</td>
                <td>${afterSalesVo.afterConnectUserName}</td>

                <td>售后联系人电话</td>
                <td>
                    ${afterSalesVo.afterConnectPhone}
                    <c:if test="${not empty afterSalesVo.afterConnectPhone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.afterConnectPhone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td class="wid20">售后报单人</td>
                <td>${afterSalesVo.traderContactName}</td>

                <td>售后报单人电话</td>
                <td>
                    ${afterSalesVo.traderContactTelephone}
                    <c:if test="${not empty afterSalesVo.traderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td>售后报单人手机</td>
                <td>
                    ${afterSalesVo.traderContactMobile}
                    <c:if test="${not empty afterSalesVo.traderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>售后地区</td>
                <td>${afterSalesVo.area}</td>
                <td>售后地址</td>
                <td>${afterSalesVo.takeTraderAddress}</td>
            </tr>
            <tr>
                <td>详情说明</td>
                <td colspan="3" class="text-left">${afterSalesVo.comments}</td>

            </tr>
            <tr>
                <td>附件</td>
                <td colspan="3" class="text-left">
                    <%@ include file="view_afterSales_files.jsp" %>
                </td>

            </tr>
            </tbody>
        </table>
    </div>

    <%--所属订单信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                所属订单信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">所属销售单号</td>
                <td>
                    <div class="customername pos_rel">
							<span style="float:none" class="brand-color1 addtitle"
                                  tabTitle='{"num":"viewfinancesaleorder${afterSalesVo.orderId}","title":"订单信息",
                               	"link":"./finance/invoice/viewSaleorder.do?saleorderId=${afterSalesVo.orderId}"}'>
                                ${afterSalesVo.orderNo}
                            </span>
                        <i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            付款状态：<c:if test="${empty afterSalesVo.amountPayStatus || afterSalesVo.amountPayStatus eq 0}">未付款</c:if>
                            <c:if test="${afterSalesVo.amountPayStatus eq 1}">部分付款</c:if>
                            <c:if test="${afterSalesVo.amountPayStatus eq 2}">全部付款</c:if><br>
                            <br> 发货状态：
                            <c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if>
                            <br> 开票状态：
                            <%--<c:if test="${afterSalesVo.invoiceStatus eq 0}">未开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 1}">部分开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 2}">全部开票</c:if>--%>
                            <c:if test="${empty afterSalesVo.invoiceMakeoutStatus ||afterSalesVo.invoiceMakeoutStatus eq 0}">
                                无开票
                            </c:if>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 1}">未开票</c:if>
                            <%--<c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">部分开票</c:if>--%>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">全部开票</c:if>
                            <br> 收货状态：
                            <c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if>
                        </div>
                    </div>
                </td>
                <td class="wid20">销售单金额</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00"
                                      maxFractionDigits="2"/></td>
            </tr>
            <tr>
                <td>归属销售</td>
                <td>${afterSalesVo.userName}</td>
                <td>归属部门</td>
                <td>${afterSalesVo.orgName}</td>
            </tr>

            <tr>
                <td>客户名称</td>
                <td>
                    <div class="customername pos_rel">
							<span style="float:none" class="brand-color1 addtitle"
                                  tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"客户信息",
								"link":"./trader/customer/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}
                            </span>
                        <i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            客户性质：
                            <c:if test="${afterSalesVo.customerNature eq 465}">分销</c:if>
                            <c:if test="${afterSalesVo.customerNature eq 466}">终端</c:if>
                            <br> 交易次数：${afterSalesVo.orderCount}<br>
                            交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00"
                                                   maxFractionDigits="2"/><br>
                            上次交易日期：<date:date value="${afterSalesVo.lastOrderTime}" format="yyyy.MM"/>
                        </div>
                    </div>
                </td>
                <td>客户等级</td>
                <td>
                    ${afterSalesVo.customerLevelStr}
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <%--商品与工程师模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                商品与售后安调公司
            </div>
        </div>
        <%@ include file="add_product_engineer_common.jsp"%>
    </div>

    <%--跟进记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                跟进记录
            </div>

        </div>
        <%@ include file="add_followUp_common.jsp"%>
    </div>

    <%--合同回传--%>
    <%--需求变更3--%>
    <%--<c:if test="${afterSalesVo.status eq 2}">
        <div class="parts content1">
            <div class="title-container">
                <div class="table-title nobor">
                    合同回传
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th>合同</th>
                    <th class="table-small">操作人</th>
                    <th class="table-small">时间</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${afterSalesVo.afterContractAttachmentList}" var="list" varStatus="status">
                    <tr>
                        <td class="font-blue"><a href="http://${list.domain}${list.uri}"
                                                 target="_blank">${list.name}</a></td>
                        <td>${list.username}</td>
                        <td><date:date value="${list.addTime}"/></td>
                    </tr>
                </c:forEach>
                <c:if test="${empty afterSalesVo.afterContractAttachmentList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='3'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>--%>

    <%--售后服务费信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">售后服务费信息</div>

        </div>
        <%@ include file="add_afterSales_service_fee_common.jsp"%>
    </div>

    <%--收款交易记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">收款交易记录</div>
            <c:if test="${afterSalesVo.atferSalesStatus eq 1}"><!-- 進行中 -->
                <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增收款记录","link":"/order/afterSalesCommon/addFinanceAfterCapital.do?afterSalesId=${afterSalesVo.afterSalesId}&billType=1"}'>新增收款记录</div>
            </c:if>

        </div>
        <%@ include file="add_collection_transaction_common.jsp"%>
    </div>

    <%--付款申请记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">付款申请记录</div>

        </div>
        <%@ include file="add_payment_request_common.jsp"%>
    </div>

    <%--付款交易记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">付款交易记录</div>
        </div>
        <%@ include file="add_payment_transaction_common.jsp"%>
    </div>

    <%--发票记录模块--%>
    <%--删除于2021-10-20,变更记录二，所有售后类型，发票记录，财务页都不展示--%>
    <c:if test="${afterSalesVo.status eq 2}">
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">发票记录</div>
                <c:if test="${afterSalesVo.atferSalesStatus eq 1}"><!-- 進行中 -->
                    <c:set var="invoice_totle_amount" value="0"></c:set>
                    <c:if test="${not empty afterSalesVo.afterOpenInvoiceList}">
                        <c:forEach items="${afterSalesVo.afterOpenInvoiceList}" var="aoil">
                            <c:set var="invoice_totle_amount" value="${invoice_totle_amount + aoil.amount}"></c:set>
                        </c:forEach>
                    </c:if>
                    <!-- 售后详情表服务费和售后产品表特殊产品费用相同 -->
                    <c:if test="${(invoice_totle_amount eq 0) or (invoice_totle_amount < afterSalesVo.serviceAmount)}">
                        <c:if test="${((afterSalesVo.isCanApplyInvoice eq 1 and afterSalesVo.companyId eq 1) or (invoiceApply.validStatus eq 1 and afterSalesVo.companyId ne 1)) and (afterSalesVo.eFlag eq 'cw' or afterSalesVo.eFlag eq '0')}">
                            <div class="title-click nobor addtitle" tabTitle='{"num":"at_add_invoice${afterSalesVo.afterSalesId}","title":"安调确认开票",
                                "link":"./order/afterSalesCommon/addAfterInvoiceAt.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                                确认开票
                            </div>
                        </c:if>
                    </c:if>
                </c:if>
            </div>
            <table class="table">
                <thead>
                <tr>
                    <td colspan="11">
                        <div style="float: left;margin-left: 30px;background: orange;clip-path: circle(50%); height: 1em; width: 1em;margin-top: 2px;">
                            <div style="background: white; width: 0.1em;height: 0.5em; margin: 25% 50%;"></div>
                        </div>
                        <div style="float: left; margin-left: 20px;">开票状态：
                            <c:if test="${empty afterSalesVo.invoiceMakeoutStatus ||afterSalesVo.invoiceMakeoutStatus eq 0}">
                                无开票
                            </c:if>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 1}">未开票</c:if>
                                <%--<c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">部分开票</c:if>--%>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">全部开票</c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>发票号</th>
                    <th>发票代码</th>
                    <th>票种</th>
                    <th>红蓝字</th>
                    <th>发票金额</th>
                    <th>开票日期</th>
                    <th>操作时间</th>
                    <th>操作人</th>
                    <th>快递公司</th>
                    <th>快递单号</th>
                    <th>快递状态</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty afterSalesVo.afterOpenInvoiceList}">
                    <c:forEach items="${afterSalesVo.afterOpenInvoiceList}" var="aoi">
                        <tr>
                            <td>${aoi.invoiceNo}</td>
                            <td>${aoi.invoiceCode}</td>
                            <td>
                                <c:if test="${aoi.invoiceType eq 429}">17%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 430}">17%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 682}">16%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 681}">16%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 972}">13%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 971}">13%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 683}">6%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 684}">6%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 685}">3%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 686}">3%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 687}">0%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 688}">0%增值税专用发票</c:if>
                            </td>

                            <td>
                                <c:choose>
                                    <c:when test="${aoi.colorType eq 1}">
                                        <c:choose>
                                            <c:when test="${aoi.isEnable eq 0}">
                                                <span style="color: red">红字作废</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span style="color: red">红字有效</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:choose>
                                            <c:when test="${aoi.isEnable eq 0}">
                                                <span style="color: red">蓝字作废</span>
                                            </c:when>
                                            <c:otherwise>
                                                蓝字有效
                                            </c:otherwise>
                                        </c:choose>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td><fmt:formatNumber type="number" value="${aoi.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td><date:date value="${aoi.validTime}" /></td>
                            <td><date:date value="${aoi.addTime}" /></td>
                            <td>
                                <c:if test="${empty aoi.creatorName}">
                                    <c:forEach var="user" items="${afterSalesVo.userList}">
                                        <c:if test="${user.userId eq aoi.creator}">${user.username}</c:if>
                                    </c:forEach>
                                </c:if>

                            </td>
                            <td>${aoi.logisticsName}</td>
                            <td>${aoi.logisticsNo}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${aoi.arrivalStatus eq 0}">
                                        未收货
                                    </c:when>
                                    <c:when test="${aoi.arrivalStatus eq 1}">
                                        部分收货
                                    </c:when>
                                    <c:when test="${aoi.arrivalStatus eq 2}">
                                        全部收货
                                    </c:when>
                                </c:choose>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty afterSalesVo.afterOpenInvoiceList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='11'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>


    <%--录票记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                录票记录
            </div>
        </div>
        <%@ include file="add_record_ticket_common.jsp"%>
    </div>

    <%--支出记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                支出记录
            </div>
        </div>
        <%@ include file="add_expenditure_common.jsp"%>
    </div>

    <%--收入记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                收入记录
            </div>
        </div>
        <%@ include file="add_revenue_common.jsp"%>
    </div>

    <%--各项审核记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                各项审核记录
            </div>
        </div>
        <%@ include file="add_auditRecords_common.jsp"%>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>
