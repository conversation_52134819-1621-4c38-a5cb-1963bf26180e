package com.vedeng.erp.kingdee.batch.handle;

import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/12/2 10:33
 * @version 1.0
 */
@Component
public class InvoiceFileHandle {

    public static final String SPECIAL_INVOICE = "专用发票";

    public static final String PLAIN_INVOICE = "普通发票";

    public static final String ZERO = "0";

    /**
     * 构造附件参数（Pdf和Xml公用，后缀名通过工具类动态读取）
     *
     * @param invoiceDto BatchInvoiceDto
     * @param formId     formId
     * @param fId        fId 金蝶内码
     * @return KingDeeFileDataDto
     */
    public KingDeeFileDataDto createKingDeeFileDataDto(BatchInvoiceDto invoiceDto, String formId, String fId) {
        KingDeeFileDataDto kingDeeFileDataDto = new KingDeeFileDataDto();
        kingDeeFileDataDto.setFileName(invoiceDto.getInvoiceNo() + FileInfoUtils.getSuffix(invoiceDto.getOssFileUrl()));
        kingDeeFileDataDto.setAliasFileName(invoiceDto.getInvoiceNo() + FileInfoUtils.getSuffix(invoiceDto.getOssFileUrl()));
        kingDeeFileDataDto.setFormId(formId);
        kingDeeFileDataDto.setFId(fId);
        kingDeeFileDataDto.setBillNo(invoiceDto.getInvoiceNo());
        kingDeeFileDataDto.setUrl(invoiceDto.getOssFileUrl());
        kingDeeFileDataDto.setErpId(invoiceDto.getInvoiceId().toString());
        kingDeeFileDataDto.setBusinessId(formId + invoiceDto.getInvoiceId().toString());
        return kingDeeFileDataDto;
    }
}