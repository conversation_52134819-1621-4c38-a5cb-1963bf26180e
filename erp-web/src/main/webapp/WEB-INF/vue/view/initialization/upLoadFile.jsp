<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<style scoped>
    .infinite-list {
        height: 200px;
        padding: 0;
        margin: 0;
        list-style: none;
    }
</style>
<div id="app">
    <el-row>
        <el-col :span="2">
            <span style="margin-left: 1px"></span>
        </el-col>
        <el-col :span="8">
            <el-link :href="fileUrl" :underline="false" type="primary"
            >下载数据模板
            </el-link>
            <br>
            <span style="color: grey;font-size: 14px;">注：导入文件请勿超过1M</span>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="2">
            <span style="margin-left: 1px"></span>
        </el-col>
        <el-col :span="20">
            <p style="text-align: left">请选择要导入的文件：</p>
            <el-upload
                    class="upload-demo"
                    ref="upload"
                    multiple="false"
                    :action="url"
                    show-file-list="false"
                    accept=".xlsx,.xls"
                    <%--limit="1"--%>
                    :before-upload="beforeUpload"
                    :on-preview="handlePreview"
                    :on-change="onChangeToolFile"
                    :on-remove="handleRemove"
                    :on-success="onSuccess"
                    :file-list="fileList"
                    :auto-upload="false">
                <p style="text-align: left">
                    <el-button slot="trigger" size="small" type="primary">添加文件</el-button>
                </p>
                <div slot="tip" class="el-upload__tip">只能上传excel文件，且不超过1M</div>


            </el-upload>
            <p style="text-align: left">
                <el-button size="small" @click="closeThis">取消</el-button>
                <el-button size="small" type="primary" @click="submitUpload">
                    导入
                </el-button>
            </p>
        </el-col>
    </el-row>


    <el-row v-if="logShow">
        <el-col :span="2">
            <span style="margin-left: 1px"></span>
        </el-col>
        <el-col :span="20">
            【{{parseTime(customDataLogDto.addTime, '{y}年{m}月{d}日')}}】 {{customDataLogDto.belonger}}
            {{customDataLogDto.resultInfo}}
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="2">
            <span style="margin-left: 1px"></span>
        </el-col>
        <el-col :span="20">
            <el-button type="text" @click="clickCenterDialog()">查看历史导入日志</el-button>

            <el-dialog
                    :title="title"
                    :visible.sync="centerDialogVisible"
                    <%--:destroy-on-close="true"--%>
                    :before-close="handleClose"
                    <%--:before-close="handleClose"--%>
                    width="80%"
            >
                <template>
                    <div v-if="showUser">
                        <el-select
                                v-model="user"
                                filterable
                                clearable
                                <%--reserve-keyword--%>
                                placeholder="所有"
                                style="width: 300px;"
                                @clear="userClear"
                                <%--@keyup.enter.native="updateData"--%>
                        >
                            <el-option
                                    v-for="item in users"
                                    :key="item.userId"
                                    :label="item.username"
                                    :value="item.userId"
                                    @click.native="usersSelect(item)"

                            >
                                <span style="float: left">{{ item.username}}</span>
                            </el-option>

                        </el-select>
                    </div>


                    <ul class="infinite-list" v-infinite-scroll="load" :infinite-scroll-immediate="false" :infinite-scroll-disabled="disInf" style="overflow:auto">
                        <li v-for="item in list" class="infinite-list-item">【{{parseTime(item.addTime, '{y}年{m}月{d}日')}}】
                            {{item.belonger}} {{item.resultInfo}}
                        </li>
                    </ul>
                </template>
                <div slot="footer" class="dialog-footer" style="text-align: center">
                    <el-button type="primary" @click="handleClose">返回</el-button>
                </div>
            </el-dialog>
        </el-col>
    </el-row>

</div>

<script type="text/javascript">

    const viewInfo = {
        type: '${type}',
        currentUser: '${currentUser}',
        currentUserName: '${currentUserName}'
    }
    var flag = true;
    new Vue({
        el: '#app',
        created() {
            this.type = viewInfo.type;
            this.belong = viewInfo.currentUser;
            if (this.type == 1) {
                this.title = '初始化线索日志';
                this.url = '/businessLeads/import.do';
                this.fileUrl = '/static/template/初始化线索模板.xlsx'
            } else {
                this.title = '初始化商机日志';
                this.url = '/businessChance/import.do';
                this.fileUrl = '/static/template/初始化商机模板.xlsx'
            }

            getUserData().then(res => {
                // debugger
                console.log(res.data.data);
                if (res.data.code == 0) {
                    this.users = res.data.data
                    if (this.users != null && this.users.length > 1) {
                        this.belong = null;
                        this.showUser = true;
                    } else {
                        this.showUser = false;
                    }
                }
            })
        },
        data() {
            return {
                fileUrl: '',
                url: '',
                showUser: false,
                users: [],
                belong: null,
                user: '',
                count: 0,
                pageSize: 15,
                pageNum: 1,
                title: '初始化日志',
                centerDialogVisible: false,
                disInf: true,
                logShow: false,
                type: null,
                fileList: [],
                customDataLogDto: {},
                list: [],
                pageData: {pageNum: 1, pageSize: 1}
            };

        },
        mounted() {
        },
        watch: {},
        methods: {
            closeThis() {
                var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
                parent.layer.close(index);
            },
            beforeUpload(file) {
                debugger
                let size = file.size / 1024 / 1024;
                if (size > 1) {
                    this.$message.warning('文件大小不得超过1M');
                    return false;
                }

            },

            async clickCenterDialog() {
                // this.disInf = false;
                if (this.list != null && this.list.length == 0) {
                    this.userClear();
                }
                this.centerDialogVisible = true;
            },
            onChangeToolFile(file, fileList){
                if (fileList.length > 0) {
                    this.fileList = [fileList[fileList.length - 1]]  // 这一步，是 展示最后一次选择的文件
                }
            },
            handleClose() {

                // debugger
                this.user = null;
                this.belong = null;
                this.list = [];
                this.pageNum = 1;

                this.centerDialogVisible = false;
            },

            submitUpload() {
                this.$refs.upload.submit();
            },
            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handlePreview(file) {
                console.log(file);
            },
            onSuccess(data) {
                console.log(data);
                if (data.code == 0) {
                    this.$message({
                        message: '导入成功',
                        type: 'success'
                    });
                } else {
                    // this.$message({
                    //     message: data.message,
                    //     type: 'warn'
                    // });
                }

                getUserLog(1, 1, viewInfo.currentUser).then(res => {
                    console.log(res.data)
                    this.logShow = true
                    this.customDataLogDto = res.data.data.list[0]
                })


            },
            load() {
                this.disInf = true;
                getUserLog(this.pageNum, this.pageSize, this.belong).then(res => {
                    console.log(res.data)
                    debugger
                    // this.logShow = true
                    this.list.push(...res.data.data.list)
                    // this.count=res.data.data.pages
                    this.pageNum = this.pageNum + 1;
                    this.disInf = false;

                });
            },
            usersSelect(item) {
                this.belong = item.userId;
                this.user = item.username;
                this.updateData();

            },
            updateData() {
                this.list = [];
                this.pageNum=1;
                this.load();
            },
            userClear() {
                this.user = null;
                this.belong = null;
                this.updateData();

            }

        }

    });

    function getUserLog(pageNum, pageSize, belongerId) {

        var data = {
            "param": {"belongerId": belongerId, "type": viewInfo.type, "saveType": 2},
            "orderBy": 'ADD_TIME desc',
            "pageSize": pageSize,
            "pageNum": pageNum
        }
        return axios({
            url: '/customDataLog/page.do',
            method: 'post',
            data: data,
            headers: {
                'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
            }
        })
    }

    function getUserData() {
        return axios({
            url: '/user/getAllSubUserList.do',
            method: 'get'
        })
    }
</script>
