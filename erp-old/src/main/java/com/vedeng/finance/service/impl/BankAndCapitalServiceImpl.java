package com.vedeng.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.finance.model.BatchBillInfo;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.service.BankAndCapitalService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/3 10:32
 **/
@Service
@Slf4j
public class BankAndCapitalServiceImpl implements BankAndCapitalService {

    @Autowired
    private CapitalBillApiService capitalBillApiService;

    @Autowired
    private BankBillApiService bankBillApiService;

    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;

    @Autowired
    @Qualifier("userService")
    private UserService userService;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveAliPayBillToCapitalBill(BatchBillInfo batchBillInfo) {

        log.info("saveAliPayBillToCapitalBill param:{}", JSON.toJSONString(batchBillInfo));

        CapitalBill excelData = batchBillInfo.getCapitalBill();
        String bankAccName = batchBillInfo.getBankAccName();
        // 获取订单信息
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(batchBillInfo.getSaleOrderId());
        Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorder);
        User belongUser = getBelongUser(saleorderInfo);
        // 获取银行流水信息
        BankBillDto bankBillDto = bankBillApiService.getBankBillByBankBillId(batchBillInfo.getCapitalBill().getBankBillId());
        // 获取订单已付款金额
        // 资金流水赋值
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        CapitalBillDto capitalBillDto = getCapitalBillDto(excelData, bankBillDto, currentUser);
        CapitalBillDetailDto capitalBillDetail = getCapitalBillDetailDto(excelData, saleorderInfo, belongUser);

        saveCapitalBill(capitalBillDto, capitalBillDetail);

        updateBankBillMatchAmount(bankBillDto, capitalBillDto);


        // 对私提现的
        if(bankAccName.equals(ErpConst.TAOBAO)||bankAccName.equals(ErpConst.WEIXIN)) {
            //对私方式交易为支付宝或微信
            capitalBillDto.setPayer(bankAccName);
            privateWithdrawal( capitalBillDto,  capitalBillDetail);
        }
    }



    private void updateBankBillMatchAmount(BankBillDto bankBillDto, CapitalBillDto capitalBillDto) {
        BigDecimal matchAmount = Objects.isNull(bankBillDto.getMatchedAmount()) ? BigDecimal.ZERO : bankBillDto.getMatchedAmount();
        BigDecimal add = matchAmount.add(capitalBillDto.getAmount().abs());
        BankBillDto update = new BankBillDto();
        update.setMatchedAmount(add);
        update.setBankBillId(bankBillDto.getBankBillId());
        log.info("更新匹配银行流水，{}",JSON.toJSONString(update));
        bankBillApiService.update(update);
    }

    private CapitalBillDetailDto getCapitalBillDetailDto(CapitalBill excelData, Saleorder saleorderInfo, User belongUser) {
        CapitalBillDetailDto capitalBillDetail = new CapitalBillDetailDto();
        capitalBillDetail.setOrderType(1);
        capitalBillDetail.setOrderNo(saleorderInfo.getSaleorderNo());
        capitalBillDetail.setRelatedId(saleorderInfo.getSaleorderId());
        capitalBillDetail.setTraderType(1);
        capitalBillDetail.setTraderId(saleorderInfo.getTraderId());
        capitalBillDetail.setUserId(saleorderInfo.getUserId());

        //交易类型订单收款
        capitalBillDetail.setBussinessType(526);

        capitalBillDetail.setAmount(excelData.getAmount());

        if(belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null){
            capitalBillDetail.setOrgName(belongUser.getOrgName());
            capitalBillDetail.setOrgId(belongUser.getOrgId());
        }
        return capitalBillDetail;
    }

    private CapitalBillDto getCapitalBillDto(CapitalBill excelData, BankBillDto bankBillDto, CurrentUser currentUser) {

        CapitalBillDto capitalBillDto = new CapitalBillDto();
        capitalBillDto.setCreator(currentUser.getId());
        capitalBillDto.setAddTime(DateUtil.sysTimeMillis());
        capitalBillDto.setCompanyId(currentUser.getCompanyId());
        capitalBillDto.setBankBillId(excelData.getBankBillId());
        capitalBillDto.setAmount(excelData.getAmount());
        capitalBillDto.setComments(excelData.getComments());
        capitalBillDto.setPayer(excelData.getPayer());
        capitalBillDto.setTraderSubject(excelData.getTraderSubject());
        // 交易方式银行
        capitalBillDto.setTraderMode(521);
        capitalBillDto.setTranFlow(bankBillDto.getTranFlow());
        capitalBillDto.setCurrencyUnitId(1);
        capitalBillDto.setTraderTime(DateUtil.sysTimeMillis());
        capitalBillDto.setTraderType(1);
        capitalBillDto.setPayerBankAccount(bankBillDto.getAccno2());
        capitalBillDto.setPayerBankName(bankBillDto.getCadbankNm());
        capitalBillDto.setPayee(ErpConst.BD);
        return capitalBillDto;
    }

    private void privateWithdrawal( CapitalBillDto capitalBillDto,  CapitalBillDetailDto capitalBillDetail) {

        capitalBillDto.setCapitalBillId(null);
        String payer = capitalBillDto.getPayer();
        if(payer.equals(ErpConst.TAOBAO)) {
            //520  支付宝
            capitalBillDto.setTraderMode(520);
        }
        if(payer.equals(ErpConst.WEIXIN)) {
            //522 微信
            capitalBillDto.setTraderMode(522);
        }
        capitalBillDto.setPayer(" ");
        capitalBillDto.setBankBillId(0);
        capitalBillDto.setTranFlow(" ");
        // 支付宝提现金额作负数
        capitalBillDto.setAmount(capitalBillDto.getAmount().multiply(new BigDecimal(-1)));
        capitalBillDto.setCurrencyUnitId(1);
        capitalBillDto.setTraderTime(DateUtil.sysTimeMillis());
        //交易主体  对私
        capitalBillDto.setTraderSubject(2);
        //交易类型支出
        capitalBillDto.setTraderType(2);
        capitalBillDetail.setCapitalBillId(null);
        capitalBillDetail.setCapitalBillDetailId(null);
        capitalBillDetail.setAmount(capitalBillDto.getAmount());
        //对私提现
        capitalBillDetail.setBussinessType(679);
        //交易类型支出
        capitalBillDetail.setTraderType(2);
        saveCapitalBill(capitalBillDto, capitalBillDetail);
    }


    private void saveCapitalBill(CapitalBillDto capitalBillDto, CapitalBillDetailDto capitalBillDetail) {
        ArrayList<CapitalBillDetailDto> capitalBillDetailDtos = CollUtil.newArrayList(capitalBillDetail);
        log.info("保存流水,{}",JSON.toJSONString(capitalBillDto));
        capitalBillApiService.insertCapitalBill(capitalBillDto);
        CapitalBillDto updateCap = new CapitalBillDto();
        updateCap.setCapitalBillId(capitalBillDto.getCapitalBillId());
        updateCap.setCapitalBillNo(generatorNum(capitalBillDto.getCapitalBillId()));
        capitalBillApiService.updateCapitalBillNo(updateCap);
        capitalBillDetail.setCapitalBillId(capitalBillDto.getCapitalBillId());
        // 订单收款
        log.info("保存流水明细,{}",JSON.toJSONString(capitalBillDetailDtos));
        capitalBillApiService.insertCapitalBillDetail(capitalBillDetailDtos);
    }

    private User getBelongUser(Saleorder saleorderInfo) {
        // 归属销售
        User belongUser = new User();
        if(saleorderInfo.getTraderId() != null ){
            // 1客户，2供应商
            belongUser = userService.getUserByTraderId(saleorderInfo.getTraderId(), 1);
            if(belongUser != null && belongUser.getUserId() != null){
                belongUser = userService.getUserById(belongUser.getUserId());
            }
        }
        return belongUser;
    }

    private String generatorNum(Integer capitalBillId) {
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CAPITAL_NO, NoGeneratorBean.builder().dateFormat(ErpConstant.YYYYMMDDHHMMSS).id(capitalBillId).numberOfDigits(9).build());
        return new BillNumGenerator().distribution(billGeneratorBean);
    }
}
