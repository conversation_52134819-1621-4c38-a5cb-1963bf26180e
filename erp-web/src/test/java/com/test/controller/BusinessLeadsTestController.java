package com.test.controller;

import com.alibaba.fastjson.JSON;
import com.test.BaseTestController;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import org.junit.Test;
import org.springframework.http.MediaType;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商机线索测试类 例子
 * @date 2023/10/8 16:03
 */
public class BusinessLeadsTestController extends BaseTestController {

    /**
     * 例子
     * @throws Exception 异常
     */
    @Test
    public void testBusinessLeadsPage() throws Exception {
        PageParam<BusinessLeadsDto> businessLeadsDto = new PageParam<>();
        mockMvc.perform(post("/businessLeads/page.do")
                        .session(session)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(businessLeadsDto))
                )
                .andExpect(status().isOk())
                .andDo(print());
    }

}
