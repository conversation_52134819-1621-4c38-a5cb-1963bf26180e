package com.vedeng.erp.aftersale.service;

import com.vedeng.erp.aftersale.dto.AfterSaleOrder2ExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyOrderSaleOrderGoodsDetailDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 费用单采销联动 服务类
 * @date 2023/1/5 15:54
 **/
public interface AfterSaleOrder2ExpenseApiService  {

    AfterSaleOrder2ExpenseDto getAfterSaleOrderExpense(Integer afterSaleId);

    /**
     * 查询销售单中虚拟商品退货数量
     * @param saleOrderGoodsDetailDtos
     * @return
     */
    List<RBuyorderExpenseJSaleorderDto> saleorderAfterSaleNum(List<BuyOrderSaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos);
}
