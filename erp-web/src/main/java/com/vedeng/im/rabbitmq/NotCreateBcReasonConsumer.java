package com.vedeng.im.rabbitmq;

import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.order.dao.NotCreateBussinessChanceReasonMapper;
import com.vedeng.order.model.NotCreateBussinessChanceReason;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
/**
 * <b>Description:</b>未成商机消息消费者<br>
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/8/24
 */
@Component
public class NotCreateBcReasonConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(NotCreateBcReasonConsumer.class);

    @Autowired
    private NotCreateBussinessChanceReasonMapper notCreateBussinessChanceReasonMapper;
    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("未成商机原因 {}",messageBody);
        try{
            NotCreateBussinessChanceReason reason= JsonUtils.readValue(messageBody,NotCreateBussinessChanceReason.class);
            if(reason==null||reason.getReasonId()==null){
                channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
                return;
            }
            notCreateBussinessChanceReasonMapper.insertSelective(reason);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }catch (Exception ex){
            LOGGER.error("bnc创建未成商机报错",ex);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
            } catch (IOException e) {
                LOGGER.error("bnc创建未成商机报错，将消息返回给rabbitmq错误：",e);
            }
        }
    }
}
