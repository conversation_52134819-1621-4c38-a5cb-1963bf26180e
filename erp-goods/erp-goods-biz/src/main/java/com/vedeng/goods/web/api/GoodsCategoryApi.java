package com.vedeng.goods.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.goods.dto.BaseCategoryDto;
import com.vedeng.goods.dto.CategoryFrontDto;
import com.vedeng.goods.service.BaseCategoryApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/goodsCategory")
@Slf4j
@ExceptionController
public class GoodsCategoryApi {
    @Autowired
    private BaseCategoryApiService baseCategoryApiService;

    /**
     * 查询分类详情
     * @return
     */
    @RequestMapping("/getAllCategory")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<CategoryFrontDto>> findLevelCategory() {

        return R.success(baseCategoryApiService.getAllCategory());
    }

    /**
     * 根据主键id查询分类详情
     * @param baseCategoryId
     * @return
     */
    @RequestMapping("/getByBaseCategoryId")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<BaseCategoryDto> getByBaseCategoryId(Integer baseCategoryId) {

        return R.success(baseCategoryApiService.selectByPrimaryKey(baseCategoryId));
    }

    /**
     * 根据完整的三级分类名称获取分类ID
     *
     * @param fullCategoryPath
     * @return
     */
    @RequestMapping("/getCategoryIdByFullPath")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<Integer> getCategoryIdByFullPath(String fullCategoryPath) {
        // 调用服务获取分类ID
        log.info("根据完整的三级分类名称获取分类ID,fullCategoryPath:{}", fullCategoryPath);
        Integer categoryId = baseCategoryApiService.getCategoryIdByFullPath(fullCategoryPath);
        if (categoryId == null) {
            return R.error("分类不存在");
        }
        return R.success("分类获取成功", categoryId);
    }

    /**
     * 根据分类ID获取完整路径名称
     *
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    @RequestMapping("/getFullPathNameById")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<String> getFullPathNameById(Integer categoryId) {
        log.info("根据分类ID获取完整路径名称,categoryId:{}", categoryId);
        String fullPathName = baseCategoryApiService.getFullPathNameById(categoryId);
        if (fullPathName == null) {
            return R.error("分类不存在");
        }
        return R.success("分类路径获取成功", fullPathName);
    }

}
