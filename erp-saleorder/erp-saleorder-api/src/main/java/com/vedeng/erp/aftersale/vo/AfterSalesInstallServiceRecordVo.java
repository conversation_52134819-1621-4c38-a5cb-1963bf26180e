package com.vedeng.erp.aftersale.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.vo
 * @Date 2022/5/11 14:25
 */
@Getter
@Setter
public class AfterSalesInstallServiceRecordVo {

    private Integer afterSalesServiceId;

    /**
     * 售后单ID  AFTER_SALES_ID
     **/
    private Integer afterSalesId;

    /**
     * 本次验收时间  CHECK_DATE
     **/
    private Date checkDate;

    /**
     * 验收方式，1电话回访2纸单验收3短信通知  CHECK_TYPE
     **/
    private Integer checkType;

    /**
     * 录音ID  RECORD_ID
     **/
    private String recordId;

    /**
     * 验收结论(字典)  CHECK_CONCLUSION
     **/
    private Integer checkConclusion;

    /**
     * 创建时间  ADD_TIME
     **/
    private Date addTime;

    /**
     * 创建者  CREATOR
     **/
    private Integer creator;

    /**
     * 更新时间  MOD_TIME
     **/
    private Date modTime;

    /**
     * 更新者  UPDATER
     **/
    private Integer updater;

    /**
     * 是否删除 0否 1是  IS_DELETE
     **/
    private Integer isDelete;

    /**
     * 验收结论名称
     */
    private String checkConclusionName;

    /**
     * 安调服务详情集合
     */
    private List<AfterSalesInstallServiceRecordDetailVo> recordDetailList;

}
