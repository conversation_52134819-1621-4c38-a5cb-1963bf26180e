package com.vedeng.trader.model.model.vo;

import com.vedeng.customerbillperiod.dto.CustomerBillPeriodApplyDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <b>Description:</b><br>
 * 类解释
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.trader.model.model.vo <br>
 * <b>ClassName:</b> CustomerBillPeriodApplyVo <br>
 * <b>Date:</b> 2021/8/2 16:38 <br>
 */
@Data
public class CustomerBillPeriodApplyVo extends CustomerBillPeriodApplyDto {

    private Long billPeriodApplyId;
    //订单号
    private String saleorderNo;
    //有效期开始时间
    private String startTime;
    //有效期截止时间
    private String endTime;
    //客户id
    private Integer traderId;
    //是否在审批过程中的修改 1表示在审批过程中的修改
    private Integer isVerifyChange;

    private BigDecimal totalAmount;//总额度

    private BigDecimal unReturnAmount;//未归还账期额度
    public boolean isNew() {
        return billPeriodApplyId == null;
    }
}
