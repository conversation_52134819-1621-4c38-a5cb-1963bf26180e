.vd-ui-input-wrap {
  position: relative;
}
.vd-ui-input-wrap .vd-ui-input {
  width: 100%;
  padding: 10px;
  border-radius: 3px;
  background: transparent;
}
.vd-ui-input-wrap .vd-ui-input::placeholder {
  color: #ccc;
  font-size: 12px;
}
.vd-ui-input-wrap .vd-ui-input.border {
  border: 1px solid #BABFC2;
  padding: 5px 9px;
  font-size: 14px;
  background: #fff;
}
.vd-ui-input-wrap .vd-ui-input.border::placeholder {
  font-size: 14px;
}
.vd-ui-input-wrap .vd-ui-input.border:focus {
  border-color: #09f;
}
.vd-ui-input-wrap .icon {
  position: absolute;
  width: 36px;
  height: calc(100% - 2px);
  background: #fff;
  right: 1px;
  top: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #666;
  border-radius: 3px;
}
.vd-ui-textarea-wrap {
  position: relative;
  padding: 10px;
}
.vd-ui-textarea-wrap .vd-ui-textarea {
  width: 100%;
  border-radius: 3px;
  background: transparent;
}
.vd-ui-textarea-wrap .vd-ui-textarea::placeholder {
  color: #ccc;
  font-size: 12px;
}
.vd-ui-textarea-wrap .vd-ui-textarea.border {
  border: 1px solid #BABFC2;
  padding: 5px 9px;
  font-size: 14px;
  background: #fff;
}
.vd-ui-textarea-wrap .vd-ui-textarea.border::placeholder {
  font-size: 14px;
}
.vd-ui-textarea-wrap .vd-ui-textarea.border:focus {
  border-color: #09f;
}
.vd-ui-textarea-wrap .vd-ui-textarea-count {
  color: #999;
  position: absolute;
  top: -23px;
  right: 10px;
  text-align: right;
  white-space: nowrap;
}
.vd-ui-textarea-wrap .vd-ui-textarea-count.upper-limit {
  color: #f60;
}
