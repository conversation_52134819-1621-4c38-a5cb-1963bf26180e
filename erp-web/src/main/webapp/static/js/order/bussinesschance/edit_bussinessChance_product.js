/**
 * @description 手动填写商品分类信息
 * <AUTHOR>
 * @date 2020/6/15 16:17:12
 */
function addProductByEdit() {
    var productName = $('#productName').val().replace(/(^\s*)|(\s*$)/g, '');
    if (productName == '' || productName == undefined || productName == null) {
        $('#productNameMsg').html('产品名称不能为空');
    } else {
        var result = $('#productName').val().replace(/(^\s*)|(\s*$)/g, '');
        result = addResultStr(result,$('#brand').val());
        result = addResultStr(result,$('#model').val());
        window.parent.addCategoryName(result);
    }
}

/**
 * @description 拼接手动添加产品结果
 * @param result
 * @param str
 * @returns {*}
 * <AUTHOR>
 * @date 2020/6/16 10:53:12
 */
function addResultStr(result, str) {
    var str = str.replace(/(^\s*)|(\s*$)/g, '');
    if (!(str == '' || str == undefined || str == null)) {
        result = result + '/' + str;
    }
    return result;
}

/**
 * @description 切换自动检索入口
 * @authoe hugo
 * @date 2020/6/16 10:27:15
 */
function addBussinessChanceProduct() {
    window.location = '/order/bussinesschance/addBussinessChanceProduct.do';
}