package com.vedeng.common.constant.goods;

import java.util.Arrays;
import java.util.List;

public class GoodsConstants {
	public static final String GOODS_EDIT_ROLE = "商品流编辑商品角色;产品专员;产品主管";
	public static final String MANUFACTURER_EDIT_ROLE = "新商品流首营权限;产品专员;产品主管";
	public static final String GOODS_EDIT_TEMP_ROLE = "商品流编辑临时商品角色;产品专员;产品主管";
	public static final String GOODS_CHECK_ROLE = "供应链质量官";
	public static final String SPU_NO_PRE = "V";
	public static final String SKU_NO_PRE = "V";

	public static final int SKU_PAGE_SIZE = 5;
	/**
	 * 器械
	 */
	public static final int SKU_TYPE_INSTRUMENT = 1;//器械
	/**
	 * 耗材
	 */
	public static final int SKU_TYPE_CONSUMABLES = 2;//耗材


	public static final int SPU_OPERATE = 1;//spu

	public static final int SKU_OPERATE  = 2;//sku

	//商品未推送到平台
	public static final int PUSH_STATUS_UN_PUSH=0;
	//商品推送到贝登
	public static final int PUSH_STATUS_VEDENG=1;
	//商品推送到医械购
	public static final int PUSH_STATUS_YXG=2;
	//商品推送到贝登和医械购
	public static final int PUSH_STATUS_ALL=3;

	// 值为0
	public static final int ZERO=0;
	// 值为1
	public static final int ONE=1;
	//值为2
	public static final int TWO=2;
	//值为6
	public static final int SIX=6;
	//运费
	public static final Integer FREIGHT=127063;

	// 虚拟商品范围：V127063，V251526，V256675，V253620，V251462，V140633
	public static final List<Integer> VIRTUAL_GOODS = Arrays.asList(127063, 251526, 256675, 253620, 251462, 140633);

	public static final int THREE=3;

	public static final int FOUR=4;


	public static final int FIVE=5;

	/**
	 *
	 * 平台类型和上下架状态（二进制）约定的最大位数
	 * com.vedeng.common.constant.PushPlatformEnum保持一致
	 */
	public static final int PLAT_MAX_LENGTH = 6;


	/**
	 * 上下架值最大范围（全平台推送时未最大）
	 */
	public static final int ON_SALE_MAX= (1 << PLAT_MAX_LENGTH) - 1;
	public static final String ON_SALE_SEPERATOR="/";
	public static final Integer ON_SALE_YES=1;
	public static final Integer ON_SALE_NO=0;
    public static final Integer MAX_PAGE_SIZE=500;



	/**
	 * 医疗器械类型
	 */
	public static final Integer MEDICAL_INSTRUMENT = 1;

	/**
	 * 医疗器械类型
	 */
	public static final Integer NOT_MEDICAL_INSTRUMENT = 2;

	// 是否区域商城校验
	public static final String IS_REGIONAL_MALL = "regionalMall";

	public static final String NOT_REGIONAL_MALL = "notRegionalMall";

	public static final String CHECK_REGIONAL_MALL = ",regionalMall";
}
