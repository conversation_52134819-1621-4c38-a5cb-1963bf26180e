package com.vedeng.erp.buyorder.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * T_EXPRESS_DETAIL
 * <AUTHOR>
public class ExpressDetail implements Serializable {
    private Integer expressDetailId;

    private Integer expressId;

    /**
     * 业务类型 字典表（销售、销售发票、文件寄送等）
     */
    private Integer businessType;

    /**
     * 关联主表字段ID
     */
    private Integer relatedId;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 历史数量
     */
    private Integer historicalNum;

    /**
     * 未全部到货原因备注
     */
    private String nonAllArrivalReason;

    /**
     * 采购单id
     */
    private Integer buyOrderId;

    public Integer getBuyOrderId() {
        return buyOrderId;
    }

    public void setBuyOrderId(Integer buyOrderId) {
        this.buyOrderId = buyOrderId;
    }

    private static final long serialVersionUID = 1L;

    public Integer getExpressDetailId() {
        return expressDetailId;
    }

    public void setExpressDetailId(Integer expressDetailId) {
        this.expressDetailId = expressDetailId;
    }

    public Integer getExpressId() {
        return expressId;
    }

    public void setExpressId(Integer expressId) {
        this.expressId = expressId;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getHistoricalNum() {
        return historicalNum;
    }

    public void setHistoricalNum(Integer historicalNum) {
        this.historicalNum = historicalNum;
    }

    public String getNonAllArrivalReason() {
        return nonAllArrivalReason;
    }

    public void setNonAllArrivalReason(String nonAllArrivalReason) {
        this.nonAllArrivalReason = nonAllArrivalReason;
    }
}