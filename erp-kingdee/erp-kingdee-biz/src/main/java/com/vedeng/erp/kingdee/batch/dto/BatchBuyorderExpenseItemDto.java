package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.Getter;
import lombok.Setter;

/**
 * 采购费用明细
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BatchBuyorderExpenseItemDto extends BatchBaseDto {
    /**
     * 主键
     */
    private Integer buyorderExpenseItemId;

    /**
     * 采购费用单ID
     */
    private Integer buyorderExpenseId;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 收货时间
     */
    private Date arrivalTime;

    /**
     * 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    private Date deliveryTime;

    /**
     * 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer invoiceStatus;

    /**
     * 收票时间
     */
    private Date invoiceTime;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 采购费用单商品明细详情
     */
    private BatchBuyorderExpenseItemDetailDto buyorderExpenseItemDetailDto;


    /**
     * 发票单价
     */
    private BigDecimal invoicePrice;

    /**
     * 发票明细ids
     */
    private List<Integer> invoiceDetailIds;

    /**
     * @组合对象@
     * 关联对象
     */
    private List<BatchPayExpenseSkuAndSaleOrderDto> batchPayExpenseSkuAndSaleOrderDtoList;


    /**
     * @组合对象@
     * 数量
     */
    private BigDecimal preciseNum;
}