<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>AI配置解析结果分析</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">

</head>
<body id="airesult" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show=""   firstcol="numbers" >
<div id="appendHead">
    <style type="text/css">
        .ezadmin-td-FIELD_ABOUT div{
            width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .layui-elip {
            white-space: normal;
        }
    </style>
    <script type="text/javascript">
        // $(document).ready(function() {
        //     var objVisitorId1 = $('#itemName-VISITOR_ID');
        //     // var xmel = $(objVisitorId)[0];
        //     // //var initdata=objVisitorId.attr("itemsJson");
        //     var initvalue1=objVisitorId1.attr("value");
        //     // 创建隐藏域
        //     var input = $('<input>').attr({
        //         type: 'hidden',
        //         id:'VISITOR_ID_HIDDEN',
        //         name: 'VISITOR_ID',
        //         value: initvalue1
        //     });
        //
        //     // 将隐藏域添加到form中
        //     $('#searchForm').append(input);
        // });
    </script>

</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <!--        <li class="layui-this"><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
            <!--        <li><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->

        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">录音ID</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="COMMUNICATE_RECORD_ID" placeholder="完全匹配" style="" alias="F.COMMUNICATE_RECORD_ID" jdbctype="" data="" datatype="" oper="EQ">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">场景名称</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="SENCE_CODE" alias="F.SENCE_CODE" placeholder="全部" jdbctype=""
                        data='[ {"K":"SENCE_BUSINESS_FOLLOW","V":"商机跟进"},  {"K":"SENCE_PRODUCT_PROMOT","V":"产品推广"},  {"K":"SENCE_CUSTOMER_DEVELOP","V":"客户开发"},  {"K":"SENCE_QUOTAT_RESP","V":"报价响应"},  {"K":"SENCE_BUSINESS_PROCESS","V":"商务处理"},  {"K":"SENCE_AFTER_SALE","V":"售后"},  {"K":"SENCE_OTHER","V":"其他"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">分组名称</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="GROUP_CODE"  alias="F.GROUP_CODE"  placeholder="全部" jdbctype=""
                        data='[  {"K":"GROUP_SUMMARY","V":"摘要"},  {"K":"GROUP_KEYWORD","V":"话术关键词"},  {"K":"GROUP_TODOTASK","V":"待办"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">字段名</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="FIELD_NAME" placeholder="模糊搜索" style="" alias="F.FIELD_NAME" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">英文字段名</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="FIELD_CODE" placeholder="模糊搜索" style="" alias="F.FIELD_CODE" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">GPT_VERSION</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="GPT_VERSION" placeholder="模糊搜索" style="" alias="T.GPT_VERSION" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <!--<button item_name="新增" name="newCreate" type="table" >新增</button>-->

    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton">
                <!--<button  type="single" url="/ezadmin/form/form-modifyAiSetting?ID=${FIELD_ID}" area="800px,500px"
                          windowname="编辑" name="playrecordBtn"  >编辑
                </button>
                <button type="table" url="/ezadmin/form/doDelete-modifyAiSetting?ID=${FIELD_ID}"
                        opentype="CONFIRM_AJAX" windowname="删除"  >删除
                </button>-->
            </th>
            <th item_name="COMMUNICATE_RECORD_ID" name="COMMUNICATE_RECORD_ID" body="td-spanlink" url="/system/call/getrecordplayForAi.do?communicateRecordId=${COMMUNICATE_RECORD_ID}" opentype="MODEL" windowname="语音播放" data="" style="text-align:LEFT;min-width:80px;width: 80px;max-width: 80px;word-break: break-all;" head="18">录音ID</th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" url=""  data="" style="text-align:LEFT;width: 40px;min-width: 40px;max-width: 80px;" head="18">添加时间</th>
            <th item_name="SENCE_NAME" name="SENCE_NAME" body="td-text"  data="" style="text-align:LEFT;min-width:80px;width: 80px;max-width: 80px;word-break: break-all;" head="18">场景名称</th>
            <th item_name="GROUP_NAME" name="GROUP_NAME" body="td-text" url=""  data="" style="text-align:LEFT;width: 100px;min-width: 60px;max-width: 80px;" head="18" >分组名称</th>
            <th item_name="FIELD_NAME" name="FIELD_NAME" body="td-text" url=""  data="" style="text-align:LEFT;width: 100px;min-width: 60px;max-width: 80px;" head="18" >字段名</th>
            <th item_name="FIELD_NAME" name="FIELD_ABOUT" body="td-text" url=""  data="" style="text-align:LEFT;width: 100px;min-width: 60px;max-width: 80px;" head="18" >字段PROMPT</th>
            <!--<th item_name="AREA_ID" name="AREA_ID" body="td-text" url=""  data="" style="text-align:LEFT;width: 100px;min-width: 60px;max-width: 180px;" head="18" >区ID</th>-->
            <th item_name="FIELD_CODE" name="FIELD_CODE" body="td-text" url=""  data="" style="text-align:LEFT;width: 40px;min-width: 40px;max-width: 80px;" head="18">字段英文名</th>
            <th item_name="FIELD_ORDER" name="FIELD_ORDER" body="td-text" url=""  data="" style="text-align:LEFT;width: 40px;min-width: 40px;max-width: 80px;" head="18">序号</th>
            <th item_name="GPT_VERSION" name="GPT_VERSION" body="td-text" url=""  data="" style="text-align:LEFT;width: 40px;min-width: 40px;max-width: 80px;" head="18">模型名称</th>
            <th item_name="FIELD_RESULT" name="FIELD_RESULT" body="td-text" url=""  data="" style="text-align:LEFT;width: 80px;min-width: 60px;max-width: 400px;" head="18">字段含义</th>

            <!--<th item_name="ADD_USER" name="ADD_USER" body="td-text" url=""  data="" style="" head="18">添加人</th>
           <th item_name="UPDATE_TIME" name="UPDATE_TIME" body="td-text" url=""  data="" style="" head="18">修改时间</th>
           <th item_name="UPDATE_USER" name="UPDATE_USER" body="td-text" url=""  data="" style="" head="18">修改时间</th>-->
            <!--<th type="rowbutton" id="rowbutton" style="min-width: 60px">
                <button type="single" opentype="CONFIRM_AJAX" url="/ezadmin/form/doDelete-modifyCustomerRegionSale?ID=${ID}"
                        windowname="删除" name="MODEL" >删除
                </button>

            </th>-->

        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by F.COMMUNICATE_RECORD_ID DESC,S.SETTING_ID ASC, G.GROUP_ORDER ASC,F.FIELD_ORDER ASC" groupby="">
            SELECT
                F.COMMUNICATE_RECORD_ID,
                F.SENCE_CODE,
                F.GROUP_CODE,
                F.FIELD_CODE,
                F.FIELD_NAME,
                AF.FIELD_ABOUT,
                T.GPT_VERSION,
                CONCAT(G.GROUP_ORDER,'.',F.FIELD_ORDER) FIELD_ORDER,
                F.FIELD_RESULT,
                DATE_FORMAT(F.ADD_TIME, '%Y-%m-%d %H:%i:%s') AS ADD_TIME,
                F.ADD_USER_ID,
                DATE_FORMAT(F.UPDATE_TIME, '%Y-%m-%d %H:%i:%s') AS UPDATE_TIME,
                F.UPDATE_USER_ID,
                S.SENCE_NAME,
                G.GROUP_NAME
            FROM
                T_VOICE_FIELD_RESULT F
            LEFT JOIN T_COMMUNICATE_VOICE_TASK T ON T.COMMUNICATE_RECORD_ID = F.COMMUNICATE_RECORD_ID AND T.SENCE_CODE = F.SENCE_CODE
            LEFT JOIN T_AI_FIELD AF   ON F.FIELD_CODE = AF.FIELD_CODE AND F.SENCE_CODE = AF.SENCE_CODE AND F.GROUP_CODE = AF.GROUP_CODE
            JOIN
                T_AI_SETTING S ON F.SENCE_CODE = S.SENCE_CODE
            JOIN
                T_AI_GROUP G ON F.GROUP_CODE = G.GROUP_CODE
            LEFT JOIN T_USER U1 ON F.ADD_USER_ID = U1.USER_ID
            LEFT JOIN T_USER U2 ON F.UPDATE_USER_ID = U2.USER_ID
            where 1=1

            </pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>
        $(function () {
            table.on( 'fixedHeader-show fixedHeader-hide', function ( e, type, data ) {
                table.draw();
            });
        })
        // $("button[item_open_title='新增']").click(function(){
        //     openModel("/ezadmin/form/form-modifyAiSetting?ID=0","新增AI识别字段配置");
        //     return false;
        // })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>

    layui.use(function () {

    })
</script>
</body>
</html>