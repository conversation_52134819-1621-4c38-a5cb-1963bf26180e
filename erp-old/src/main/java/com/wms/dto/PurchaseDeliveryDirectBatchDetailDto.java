package com.wms.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Author: daniel
 * @Date: 2022/4/14 09 06
 * @Description: 采购直发批次详情
 */
@Data
public class PurchaseDeliveryDirectBatchDetailDto {

    private Integer buyorderId;

    private Integer purchaseDeliveryDirectBatchDetailId;

    private String sku;

    private Integer buyorderGoodsId;

    /**
     * 注册证号
     */
    private String registerNumber;

    /**
     * 生产批次号
     */
    private String batchNumber;

    /**
     * 收货数量
     */
    private Integer arrivalCount;

    /**
     * 生产日期
     */
    private Date manufactureDateTime;

    private Integer unionSequence;

    /**
     * 失效日期
     */
    private Date invalidDateTime;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流寄送时间
     */
    private String expressDeliveryTime;
}
