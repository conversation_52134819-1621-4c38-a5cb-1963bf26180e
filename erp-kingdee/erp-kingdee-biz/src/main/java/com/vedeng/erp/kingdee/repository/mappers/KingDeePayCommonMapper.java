package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeePayCommonMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeePayCommonEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeePayCommonEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeePayCommonEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeePayCommonEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeePayCommonEntity record);

    int updateBatchSelective(List<KingDeePayCommonEntity> list);

    int batchInsert(@Param("list") List<KingDeePayCommonEntity> list);

    List<KingDeePayCommonEntity> findByFQzokBddjtId(@Param("list")List<String> invoiceIds);


}