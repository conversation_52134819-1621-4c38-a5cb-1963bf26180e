<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.finance.mapper.ROldInvoiceJNewInvoiceMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.ROldInvoiceJNewInvoiceEntity" >
    <!--          -->
    <id column="R_OLD_INVOICE_J_NEW_INVOICE_ID" property="rOldInvoiceJNewInvoiceId" jdbcType="INTEGER" />
    <result column="OLD_INVOICE_ID" property="oldInvoiceId" jdbcType="INTEGER" />
    <result column="NEW_INVOICE_ID" property="newInvoiceId" jdbcType="INTEGER" />
    <result column="INVOICE_AMOUNT" property="invoiceAmount" jdbcType="DECIMAL" />
    <result column="IS_DELETE" property="isDelete" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    R_OLD_INVOICE_J_NEW_INVOICE_ID, OLD_INVOICE_ID, NEW_INVOICE_ID, INVOICE_AMOUNT, IS_DELETE, 
    ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_R_OLD_INVOICE_J_NEW_INVOICE
    where R_OLD_INVOICE_J_NEW_INVOICE_ID = #{rOldInvoiceJNewInvoiceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_R_OLD_INVOICE_J_NEW_INVOICE
    where R_OLD_INVOICE_J_NEW_INVOICE_ID = #{rOldInvoiceJNewInvoiceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.finance.domain.entity.ROldInvoiceJNewInvoiceEntity" >
    <!--          -->
    insert into T_R_OLD_INVOICE_J_NEW_INVOICE (R_OLD_INVOICE_J_NEW_INVOICE_ID, OLD_INVOICE_ID, 
      NEW_INVOICE_ID, INVOICE_AMOUNT, IS_DELETE, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{rOldInvoiceJNewInvoiceId,jdbcType=INTEGER}, #{oldInvoiceId,jdbcType=INTEGER}, 
      #{newInvoiceId,jdbcType=INTEGER}, #{invoiceAmount,jdbcType=DECIMAL}, #{isDelete,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.finance.domain.entity.ROldInvoiceJNewInvoiceEntity" >
    <!--          -->
    insert into T_R_OLD_INVOICE_J_NEW_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rOldInvoiceJNewInvoiceId != null" >
        R_OLD_INVOICE_J_NEW_INVOICE_ID,
      </if>
      <if test="oldInvoiceId != null" >
        OLD_INVOICE_ID,
      </if>
      <if test="newInvoiceId != null" >
        NEW_INVOICE_ID,
      </if>
      <if test="invoiceAmount != null" >
        INVOICE_AMOUNT,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rOldInvoiceJNewInvoiceId != null" >
        #{rOldInvoiceJNewInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="oldInvoiceId != null" >
        #{oldInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="newInvoiceId != null" >
        #{newInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="invoiceAmount != null" >
        #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.ROldInvoiceJNewInvoiceEntity" >
    <!--          -->
    update T_R_OLD_INVOICE_J_NEW_INVOICE
    <set >
      <if test="oldInvoiceId != null" >
        OLD_INVOICE_ID = #{oldInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="newInvoiceId != null" >
        NEW_INVOICE_ID = #{newInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="invoiceAmount != null" >
        INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where R_OLD_INVOICE_J_NEW_INVOICE_ID = #{rOldInvoiceJNewInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.ROldInvoiceJNewInvoiceEntity" >
    <!--          -->
    update T_R_OLD_INVOICE_J_NEW_INVOICE
    set OLD_INVOICE_ID = #{oldInvoiceId,jdbcType=INTEGER},
      NEW_INVOICE_ID = #{newInvoiceId,jdbcType=INTEGER},
      INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where R_OLD_INVOICE_J_NEW_INVOICE_ID = #{rOldInvoiceJNewInvoiceId,jdbcType=INTEGER}
  </update>
</mapper>